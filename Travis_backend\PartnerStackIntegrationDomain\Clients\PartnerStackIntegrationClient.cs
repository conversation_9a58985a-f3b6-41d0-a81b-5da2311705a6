using System;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Threading;
using System.Threading.Tasks;
using System.Web;
using Newtonsoft.Json.Linq;
using Travis_backend.Exceptions;

namespace Travis_backend.PartnerStackIntegrationDomain.Clients;

public class PartnerStackIntegrationClient
{
    private const string BasePath = "https://api.partnerstack.com/api/v2";

    private readonly HttpClient _httpClient;

    private readonly string _publicKey;
    private readonly string _secretKey;

    public PartnerStackIntegrationClient(
        HttpClient httpClient,
        string publicKey,
        string secretKey)
    {
        _httpClient = httpClient;
        _publicKey = publicKey;
        _secretKey = secretKey;
    }

    public async Task<RetrievePartnerStackCustomerByCustomerKeyResponse> RetrievePartnerStackCustomerByCustomerKey(
        string customerKey,
        CancellationToken cancellationToken = default)
    {
        return await MakeHttpRequestAsync(
            new RetrievePartnerStackCustomerByCustomerKeyRequest(customerKey),
            cancellationToken);
    }

    public async Task<ListPartnerStackTransactionsResponse> ListPartnerStackTransactions(
        string customerKey,
        string customerExternalKey,
        string customerEmail,
        string productKey,
        string categoryKey,
        int limit,
        string startingAfter,
        string endingBefore,
        CancellationToken cancellationToken = default)
    {
        return await MakeHttpRequestAsync(
            new ListPartnerStackTransactionsRequest(
                customerKey,
                customerExternalKey,
                customerEmail,
                productKey,
                categoryKey,
                limit,
                startingAfter,
                endingBefore),
            cancellationToken);
    }

    public async Task<CreatePartnerStackTransactionResponse> CreatePartnerStackTransaction(
        string customerExternalKey,
        int amount,
        string currency,
        string categoryKey = null,
        string productKey = null,
        JObject metadata = null,
        CancellationToken cancellationToken = default)
    {
        if (metadata == null)
        {
            return await MakeHttpRequestAsync(
                new CreatePartnerStackTransactionRequest(
                    customerExternalKey,
                    amount,
                    currency,
                    categoryKey,
                    productKey),
                cancellationToken);
        }

        return await MakeHttpRequestAsync(
            new CreatePartnerStackTransactionWithMetadataRequest(
                customerExternalKey,
                amount,
                currency,
                metadata,
                categoryKey,
                productKey),
            cancellationToken);
    }

    public async Task<RetrievePartnerStackPartnershipByUniqueIdentifierResponse> RetrievePartnerStackPartnershipByUniqueIdentifier(
        string uniqueIdentifier,
        CancellationToken cancellationToken = default)
    {
        return await MakeHttpRequestAsync(
            new RetrievePartnerStackPartnershipByUniqueIdentifierRequest(uniqueIdentifier),
            cancellationToken);
    }

    private async Task<TResponse> MakeHttpRequestAsync<TResponse>(
        PartnerStackIntegrationClientApiRequestBase<TResponse> request,
        CancellationToken cancellationToken = default)
        where TResponse : new()
    {
        var requestPath = BasePath + request.MethodName;
        var urlBuilder = new UriBuilder(requestPath);

        if (request.QueryParams != null)
        {
            var query = HttpUtility.ParseQueryString(string.Empty);

            foreach (var queryParam in request.QueryParams.Where(queryParam => queryParam.Value != null))
            {
                query[queryParam.Key] = queryParam.Value;
            }

            urlBuilder.Query = query.ToString() ?? string.Empty;
        }

        var httpRequestMessage = new HttpRequestMessage(request.Method, urlBuilder.Uri);

        if (request.Method != HttpMethod.Get)
        {
            httpRequestMessage.Content = request.ToHttpContent();
        }

        httpRequestMessage.Headers.Authorization = new AuthenticationHeaderValue(
            "Basic",
            Convert.ToBase64String(System.Text.Encoding.UTF8.GetBytes($"{_publicKey}:{_secretKey}")));

        var httpResponse = await _httpClient.SendAsync(httpRequestMessage, cancellationToken);

        var responseAsString = await httpResponse.Content.ReadAsStringAsync(cancellationToken);
        PartnerStackIntegrationClientJsonHelper.TryDeserializeJson<TResponse>(responseAsString, out var response);

        if (!httpResponse.IsSuccessStatusCode || response == null)
        {
            throw new PartnerStackIntegrationClientException(
                urlBuilder.ToString(),
                (int) httpResponse.StatusCode,
                await request.ToHttpContent().ReadAsStringAsync(cancellationToken),
                responseAsString);
        }

        return response;
    }
}