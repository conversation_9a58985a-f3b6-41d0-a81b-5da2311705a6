using Pulumi;
using Pulumi.AzureNative.Resources;
using Sleekflow.Core.Infra.Components.Configs;
using Sleekflow.Core.Infra.Components.Models;
using Sleekflow.Core.Infra.Constants;
using Random = Pulumi.Random;
using Resource = Pulumi.Resource;
using Cdn = Pulumi.AzureNative.Cdn;

namespace Sleekflow.Core.Infra.Components;

public class FrontDoor
{
    private readonly MyConfig _myConfig;
    private readonly List<EnvGroup> _envGroups;
    private readonly ServerConfig _serverConfig;
    private readonly ResourceGroup _resourceGroup;

    private const string DefaultOriginGroupIdentity = "default";

    public FrontDoor(
        MyConfig myConfig,
        List<EnvGroup> envGroups,
        ServerConfig serverConfig,
        ResourceGroup resourceGroup)
    {
        _myConfig = myConfig;
        _envGroups = envGroups;
        _serverConfig = serverConfig;
        _resourceGroup = resourceGroup;
    }

    public Cdn.AFDEndpoint InitFrontDoor()
    {
        var profile = new Cdn.Profile(
            "sleekflow-core-front-door-profile",
            new Cdn.ProfileArgs
            {
                ResourceGroupName = _resourceGroup.Name,
                ProfileName = "sleekflow",
                Location = "Global",
                Sku = new Cdn.Inputs.SkuArgs
                {
                    Name = Cdn.SkuName.Standard_AzureFrontDoor,
                },
                OriginResponseTimeoutSeconds = 60,
            },
            new CustomResourceOptions
            {
                Parent = _resourceGroup
            });

        var afdEndpoint = new Cdn.AFDEndpoint(
            "sleekflow-core-front-door-endpoint",
            new Cdn.AFDEndpointArgs
            {
                ResourceGroupName = _resourceGroup.Name,
                ProfileName = profile.Name,
                EndpointName = $"sleekflow-core-{_myConfig.Name}",
                EnabledState = Cdn.EnabledState.Enabled,
                Location = "Global",
            },
            new CustomResourceOptions
            {
                Parent = profile
            });

        var distinctLocationNameToEnvGroups = _envGroups
            .GroupBy(m => m.LocationName)
            .ToDictionary(m => m.Key, m => m.ToList());

        // If there is east asia set it as default otherwise add an default in
        if (distinctLocationNameToEnvGroups.TryGetValue(LocationNames.EastAsia, out var eastAsiaEnvGroup))
        {
            // Replace the key with default
            distinctLocationNameToEnvGroups.Remove(LocationNames.EastAsia);
            distinctLocationNameToEnvGroups.Add(DefaultOriginGroupIdentity, eastAsiaEnvGroup);
        }
        else
        {
            // Concat the default Travis Backend Origin Group Location
            distinctLocationNameToEnvGroups.Add(DefaultOriginGroupIdentity, new List<EnvGroup>());
        }

        var afdOriginGroups =
            new Dictionary<string, (Cdn.AFDOriginGroup AfdOriginGroup, List<Cdn.AFDOrigin> AfdOrigins)>();

        foreach (var (originGroupIdentity, envGroups) in distinctLocationNameToEnvGroups)
        {
            afdOriginGroups.Add(
                originGroupIdentity,
                InitAfdOriginGroup(profile, originGroupIdentity, envGroups));
        }

        var (afdOriginGroup, afdOrigins) = afdOriginGroups.First(a => a.Key == DefaultOriginGroupIdentity)!.Value;

        var ruleSet = InitRuleSet(
            profile,
            _serverConfig.CorsConfig,
            afdOriginGroups.Where(a => a.Key != DefaultOriginGroupIdentity).ToDictionary(i => i.Key, i => i.Value),
            out var corsRule,
            out var signalRRule,
            out var regionalRules,
            out var corsDomainsRules);

        var _ = new Cdn.Route(
            "sleekflow-core-front-door-default-route",
            new Cdn.RouteArgs
            {
                ResourceGroupName = _resourceGroup.Name,
                ProfileName = profile.Name,
                EndpointName = afdEndpoint.Name,
                RouteName = "default-route",
                CustomDomains = new InputList<Cdn.Inputs.ActivatedResourceReferenceArgs>(),
                OriginGroup = new Cdn.Inputs.ResourceReferenceArgs
                {
                    Id = Output
                        .Tuple(afdOriginGroup.Id, afdOrigins.First().ProvisioningState)
                        .Apply(t => t.Item1),
                },
                RuleSets = new List<Cdn.Inputs.ResourceReferenceArgs>
                {
                    new Cdn.Inputs.ResourceReferenceArgs
                    {
                        Id = ruleSet.Id
                    }
                },
                SupportedProtocols =
                {
                    "Https",
                },
                PatternsToMatch =
                {
                    "/*",
                },
                ForwardingProtocol = Cdn.ForwardingProtocol.MatchRequest,
                LinkToDefaultDomain = Cdn.LinkToDefaultDomain.Enabled,
                HttpsRedirect = Cdn.HttpsRedirect.Disabled,
                EnabledState = Cdn.EnabledState.Enabled,
            },
            new CustomResourceOptions
            {
                DependsOn =
                    afdOrigins
                        .Concat(
                            new List<Resource>
                            {
                                ruleSet, corsRule, signalRRule
                            }.Concat(regionalRules).Concat(corsDomainsRules))
                        .ToList(),
                Parent = profile
            });

        return afdEndpoint;
    }

    private Cdn.RuleSet InitRuleSet(
        Cdn.Profile profile,
        CorsConfig corsConfig,
        // This excludes the default origin group
        Dictionary<string, (Cdn.AFDOriginGroup AfdOriginGroup, List<Cdn.AFDOrigin> AfdOrigins)> afdOriginGroups,
        out Cdn.Rule corsRule,
        out Cdn.Rule signalRRule,
        out List<Cdn.Rule> regionalRules,
        out List<Cdn.Rule> corsDomainsRules)
    {
        var ruleSet = new Cdn.RuleSet(
            "sleekflow-core-front-door-default-ruleset",
            new Cdn.RuleSetArgs
            {
                ResourceGroupName = _resourceGroup.Name, ProfileName = profile.Name, RuleSetName = "default"
            },
            new CustomResourceOptions
            {
                Parent = profile
            });

        corsRule = new Cdn.Rule(
            "sleekflow-core-front-door-default-ruleset-default-cors-rule",
            new Cdn.RuleArgs
            {
                ResourceGroupName = _resourceGroup.Name,
                ProfileName = profile.Name,
                RuleSetName = ruleSet.Name,
                RuleName = "CORS",
                Actions = new List<object>
                {
                    new Cdn.Inputs.DeliveryRuleResponseHeaderActionArgs
                    {
                        Name = "ModifyResponseHeader",
                        Parameters = new Cdn.Inputs.HeaderActionParametersArgs
                        {
                            TypeName = "DeliveryRuleHeaderActionParameters",
                            HeaderAction = "Overwrite",
                            HeaderName = "access-control-allow-credentials",
                            Value = "true"
                        },
                    },
                    new Cdn.Inputs.DeliveryRuleResponseHeaderActionArgs
                    {
                        Name = "ModifyResponseHeader",
                        Parameters = new Cdn.Inputs.HeaderActionParametersArgs
                        {
                            TypeName = "DeliveryRuleHeaderActionParameters",
                            HeaderAction = "Overwrite",
                            HeaderName = "access-control-allow-headers",
                            Value = "authorization,cache-control,content-type,x-sleekflow-location,x-sleekflow-distributed-invocation-context,x-sleekflow-api-key"
                        },
                    },
                    new Cdn.Inputs.DeliveryRuleResponseHeaderActionArgs
                    {
                        Name = "ModifyResponseHeader",
                        Parameters = new Cdn.Inputs.HeaderActionParametersArgs
                        {
                            TypeName = "DeliveryRuleHeaderActionParameters",
                            HeaderAction = "Overwrite",
                            HeaderName = "access-control-allow-origin",
                            Value = "*"
                        },
                    },
                    new Cdn.Inputs.DeliveryRuleResponseHeaderActionArgs
                    {
                        Name = "ModifyResponseHeader",
                        Parameters = new Cdn.Inputs.HeaderActionParametersArgs
                        {
                            TypeName = "DeliveryRuleHeaderActionParameters",
                            HeaderAction = "Overwrite",
                            HeaderName = "access-control-expose-headers",
                            Value = "content-disposition"
                        },
                    },
                },
                Order = 1,
                MatchProcessingBehavior = Cdn.MatchProcessingBehavior.Continue
            },
            new CustomResourceOptions
            {
                Parent = ruleSet
            });

        signalRRule = new Cdn.Rule(
            "sleekflow-core-front-door-default-ruleset-default-signalR-rule",
            new Cdn.RuleArgs
            {
                ResourceGroupName = _resourceGroup.Name,
                ProfileName = profile.Name,
                RuleSetName = ruleSet.Name,
                RuleName = $"SignalR",
                Conditions = new List<object>
                {
                    new Cdn.Inputs.DeliveryRuleRequestHeaderConditionArgs
                    {
                        Name = "RequestHeader",
                        Parameters = new Cdn.Inputs.RequestHeaderMatchConditionParametersArgs
                        {
                            TypeName = "DeliveryRuleRequestHeaderConditionParameters",
                            Operator = "Contains",
                            Selector = "Access-Control-Request-Headers",
                            NegateCondition = false,
                            MatchValues = new InputList<string>
                            {
                                "x-signalr-user-agent", "x-requested-with"
                            },
                            Transforms = new InputList<Union<string, Cdn.Transform>>()
                        },
                    },
                },
                Actions = new List<object>
                {
                    new Cdn.Inputs.DeliveryRuleResponseHeaderActionArgs
                    {
                        Name = "ModifyResponseHeader",
                        Parameters = new Cdn.Inputs.HeaderActionParametersArgs
                        {
                            TypeName = "DeliveryRuleHeaderActionParameters",
                            HeaderAction = "Append",
                            HeaderName = "access-control-allow-headers",
                            Value = ",x-signalr-user-agent,x-requested-with"
                        },
                    },
                },
                Order = 2
            },
            new CustomResourceOptions
            {
                Parent = ruleSet
            });

        var counter = 3;
        regionalRules = new List<Cdn.Rule>();

        foreach (var (originGroupIdentity, (afdOriginGroup, afdOrigins)) in afdOriginGroups)
        {
            regionalRules.Add(
                new Cdn.Rule(
                    $"sleekflow-core-front-door-default-ruleset-{LocationNames.GetShortName(originGroupIdentity)}-regional-rule",
                    new Cdn.RuleArgs
                    {
                        ResourceGroupName = _resourceGroup.Name,
                        ProfileName = profile.Name,
                        RuleSetName = ruleSet.Name,
                        RuleName = $"RegionalRule{LocationNames.GetCamelCaseShortName(originGroupIdentity)}",
                        Conditions = new List<object>
                        {
                            new Cdn.Inputs.DeliveryRuleRequestHeaderConditionArgs
                            {
                                Name = "RequestHeader",
                                Parameters = new Cdn.Inputs.RequestHeaderMatchConditionParametersArgs
                                {
                                    TypeName = "DeliveryRuleRequestHeaderConditionParameters",
                                    Operator = "Contains",
                                    Selector = "X-Sleekflow-Location",
                                    NegateCondition = false,
                                    MatchValues = new InputList<string>
                                    {
                                        originGroupIdentity
                                    },
                                    Transforms = new InputList<Union<string, Cdn.Transform>>()
                                },
                            },
                        },
                        Actions = new List<object>
                        {
                            new Cdn.Inputs.DeliveryRuleRouteConfigurationOverrideActionArgs()
                            {
                                Name = "RouteConfigurationOverride",
                                Parameters = new Cdn.Inputs.RouteConfigurationOverrideActionParametersArgs()
                                {
                                    TypeName = "DeliveryRuleRouteConfigurationOverrideActionParameters",
                                    OriginGroupOverride = new Cdn.Inputs.OriginGroupOverrideArgs()
                                    {
                                        OriginGroup = new Cdn.Inputs.ResourceReferenceArgs()
                                        {
                                            Id = Output
                                                .Tuple(afdOriginGroup.Id, afdOrigins.First().ProvisioningState)
                                                .Apply(t => t.Item1),
                                        },
                                        ForwardingProtocol = Cdn.ForwardingProtocol.MatchRequest
                                    },
                                },
                            },
                            new Cdn.Inputs.DeliveryRuleResponseHeaderActionArgs
                            {
                                Name = "ModifyResponseHeader",
                                Parameters = new Cdn.Inputs.HeaderActionParametersArgs
                                {
                                    TypeName = "DeliveryRuleHeaderActionParameters",
                                    HeaderAction = "Overwrite",
                                    HeaderName = "X-Sleekflow-Location",
                                    Value = originGroupIdentity
                                },
                            },
                        },
                        Order = counter
                    },
                    new CustomResourceOptions
                    {
                        Parent = ruleSet
                    }));
            counter++;
        }

        var corsDomainRulesCounter = 100;
        corsDomainsRules = new List<Cdn.Rule>();
        foreach (var domain in corsConfig.Domains)
        {
            var randomId = new Random.RandomId(
                $"sleekflow-core-front-door-default-ruleset-{domain}-core-domain-rule-random-id",
                new Random.RandomIdArgs
                {
                    ByteLength = 4,
                    Keepers =
                    {
                        {
                            "Domain", domain
                        }
                    },
                },
                new CustomResourceOptions
                {
                    Parent = ruleSet
                });
            var domainRandomId = randomId.Hex.Apply(h => "s" + h);
            corsDomainsRules.Add(
                new Cdn.Rule(
                    $"sleekflow-core-front-door-default-ruleset-{domain}-cors-domain-rule",
                    new Cdn.RuleArgs
                    {
                        ResourceGroupName = _resourceGroup.Name,
                        ProfileName = profile.Name,
                        RuleSetName = ruleSet.Name,
                        RuleName = domainRandomId.Apply(d => $"CorsDomainRule{d}"),
                        Conditions = new List<object>
                        {
                            new Cdn.Inputs.DeliveryRuleRequestHeaderConditionArgs
                            {
                                Name = "RequestHeader",
                                Parameters = new Cdn.Inputs.RequestHeaderMatchConditionParametersArgs
                                {
                                    TypeName = "DeliveryRuleRequestHeaderConditionParameters",
                                    Operator = "Contains",
                                    Selector = "Origin",
                                    NegateCondition = false,
                                    MatchValues = new InputList<string>
                                    {
                                        domain
                                    },
                                    Transforms = new InputList<Union<string, Cdn.Transform>>()
                                },
                            },
                        },
                        Actions = new List<object>
                        {
                            new Cdn.Inputs.DeliveryRuleResponseHeaderActionArgs
                            {
                                Name = "ModifyResponseHeader",
                                Parameters = new Cdn.Inputs.HeaderActionParametersArgs
                                {
                                    TypeName = "DeliveryRuleHeaderActionParameters",
                                    HeaderAction = "Overwrite",
                                    HeaderName = "access-control-allow-origin",
                                    Value = domain
                                },
                            },
                        },
                        Order = corsDomainRulesCounter
                    },
                    new CustomResourceOptions
                    {
                        Parent = ruleSet
                    }));
            corsDomainRulesCounter++;
        }

        return ruleSet;
    }

    private (Cdn.AFDOriginGroup AfdOriginGroup, List<Cdn.AFDOrigin> AfdOrigins) InitAfdOriginGroup(
        Cdn.Profile profile,
        string originGroupIdentity,
        List<EnvGroup> envGroups)
    {
        var identity = originGroupIdentity == DefaultOriginGroupIdentity
            ? DefaultOriginGroupIdentity
            : LocationNames.GetShortName(originGroupIdentity);
        var afdOriginGroup = new Cdn.AFDOriginGroup(
            $"sleekflow-core-front-door-{identity}-origin-group",
            new Cdn.AFDOriginGroupArgs
            {
                ResourceGroupName = _resourceGroup.Name,
                ProfileName = profile.Name,
                OriginGroupName = $"sleekflow-{identity}-origin-group",
                SessionAffinityState = Cdn.EnabledState.Disabled,

                // As the front door is a global service,
                // the health probes are also from global instances.
                // This would causes serious traffics globally.
                // So this is disabled currently.
                HealthProbeSettings = originGroupIdentity != DefaultOriginGroupIdentity
                    ? new Cdn.Inputs.HealthProbeParametersArgs
                    {
                        ProbePath = "/__health",
                        ProbeProtocol = Cdn.ProbeProtocol.Https,
                        ProbeRequestType = Cdn.HealthProbeRequestType.GET,
                        ProbeIntervalInSeconds = 120,
                    }
                    : new Cdn.Inputs.HealthProbeParametersArgs(),
                LoadBalancingSettings = new Cdn.Inputs.LoadBalancingSettingsParametersArgs
                {
                    SampleSize = 4, SuccessfulSamplesRequired = 2, AdditionalLatencyInMilliseconds = 50,
                },
            },
            new CustomResourceOptions
            {
                Parent = profile
            });

        var afdOrigins = new List<Cdn.AFDOrigin>();
        if (originGroupIdentity == DefaultOriginGroupIdentity)
        {
            var hostName = Output.Create(_serverConfig.DefaultSleekflowCoreDomain);

            if (envGroups.Any(e => e.LocationName == LocationNames.EastAsia))
            {
                var envGroup = envGroups.First(e => e.LocationName == LocationNames.EastAsia);
                hostName = envGroup.WebApps[ServiceNames.SleekflowCore].WebApp.DefaultHostName;
            }

            var afdOrigin = new Cdn.AFDOrigin(
                $"sleekflow-core-front-door-{originGroupIdentity}-origin",
                new Cdn.AFDOriginArgs
                {
                    ProfileName = profile.Name,
                    ResourceGroupName = _resourceGroup.Name,
                    OriginGroupName = afdOriginGroup.Name,
                    OriginName = $"sleekflow-{originGroupIdentity}-origin",
                    HostName = hostName,
                    OriginHostHeader = hostName,
                    HttpPort = 80,
                    HttpsPort = 443,
                    Priority = 1,
                    Weight = 1000,
                    EnabledState = Cdn.EnabledState.Enabled,
                },
                new CustomResourceOptions
                {
                    Parent = afdOriginGroup, Version = string.Empty
                });

            afdOrigins.Add(afdOrigin);
            return (afdOriginGroup, afdOrigins);
        }

        foreach (var envGroup in envGroups)
        {
            var name = LocationNames.GetShortName(envGroup.LocationName);
            var webApps = envGroup.WebApps;

            var afdOrigin = new Cdn.AFDOrigin(
                $"sleekflow-core-front-door-{name}-origin",
                new Cdn.AFDOriginArgs
                {
                    ProfileName = profile.Name,
                    ResourceGroupName = _resourceGroup.Name,
                    OriginGroupName = afdOriginGroup.Name,
                    OriginName = $"sleekflow-{name}-origin",
                    HostName = webApps[ServiceNames.SleekflowCore].WebApp.DefaultHostName,
                    OriginHostHeader = webApps[ServiceNames.SleekflowCore].WebApp.DefaultHostName,
                    HttpPort = 80,
                    HttpsPort = 443,
                    Priority = 1,
                    Weight = 1000,
                    EnabledState = Cdn.EnabledState.Enabled,
                },
                new CustomResourceOptions
                {
                    Parent = afdOriginGroup, Version = string.Empty
                });

            afdOrigins.Add(afdOrigin);
        }

        return (afdOriginGroup, afdOrigins);
    }
}