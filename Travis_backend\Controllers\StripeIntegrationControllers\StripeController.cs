using System;
using System.Collections.Generic;
using System.Collections.Immutable;
using System.IO;
using System.Linq;
using System.Linq.Expressions;
using System.Threading.Tasks;
using System.Web;
using AutoMapper;
using Hangfire;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Sleekflow.Apis.FlowHub.Api;
using Sleekflow.Apis.FlowHub.Model;
using Stripe;
using Stripe.Checkout;
using Travis_backend.AccountAuthenticationDomain.Models;
using Travis_backend.Cache;
using Travis_backend.CommonDomain.ViewModels;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.CompanyDomain.Services;
using Travis_backend.CompanyDomain.ViewModels;
using Travis_backend.Configuration;
using Travis_backend.Constants;
using Travis_backend.ContactDomain.Models;
using Travis_backend.ContactDomain.Services;
using Travis_backend.ConversationServices;
using Travis_backend.CoreDomain.Models;
using Travis_backend.Database;
using Travis_backend.Enums;
using Travis_backend.Extensions;
using Travis_backend.FlowHubs;
using Travis_backend.Infrastructures.Options;
using Travis_backend.IntelligentHubDomain.Services;
using Travis_backend.InternalDomain.Services;
using Travis_backend.ResellerDomain.Models;
using Travis_backend.ResellerDomain.Services;
using Travis_backend.ResellerDomain.ViewModels;
using Travis_backend.SleekflowCrmHubDomain.Services;
using Travis_backend.StripeIntegrationDomain.Configs;
using Travis_backend.StripeIntegrationDomain.Services;
using Travis_backend.StripeIntegrationDomain.ViewModels;
using Travis_backend.SubscriptionPlanDomain.Constants;
using Travis_backend.SubscriptionPlanDomain.Enums;
using Travis_backend.SubscriptionPlanDomain.Models;
using Travis_backend.SubscriptionPlanDomain.Services;
using Travis_backend.SubscriptionPlanDomain.ViewModels;
using Twilio;
using Twilio.Rest.Api.V2010.Account.Usage;
using DateTime = System.DateTime;
using Invoice = Stripe.Invoice;
using Session = Stripe.Checkout.Session;
using Subscription = Stripe.Subscription;

namespace Travis_backend.Controllers.StripeIntegrationControllers
{
    public class StripeController : Controller
    {
        // GET: /<controller>/
        private readonly ApplicationDbContext _appDbContext;
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly IMapper _mapper;
        private readonly ILogger _logger;
        private readonly IConfiguration _configuration;
        private readonly IUserProfileService _userProfileService;
        private readonly ICompanyService _companyService;
        private readonly ICompanySubscriptionService _companySubscriptionService;
        private readonly ISubscriptionPlanService _subscriptionPlanService;
        private readonly ICompanyInfoCacheService _companyInfoCacheService;
        private readonly ICoreService _coreService;
        private readonly ITwilioService _twilioService;
        private readonly IResellerPortalRepository _resellerPortalRepository;
        private readonly IStripeAuthenticationConfig _stripeAuthenticationConfig;
        private readonly IFeatureQuantityService _featureQuantityService;
        private readonly IFlowHubConfigsApi _flowHubConfigsApi;

        private readonly ICompanyUsageService _companyUsageService;

        private readonly IUsageCycleCalculator _usageCycleCalculator;

        private readonly IDefaultSubscriptionPlanIdGetter _defaultSubscriptionPlanIdGetter;

        private readonly IStripeSubscriptionService _stripeSubscriptionService;

        private readonly GlobalPricingOptions _globalPricingOptions;

        private readonly bool _isEnableTenantHubLogic;

        /// <summary>
        /// Stripe Services Factory
        /// </summary>
        private readonly IStripeServicesFactory _stripeServicesFactory;

        public StripeController(
            ApplicationDbContext appDbContext,
            UserManager<ApplicationUser> userManager,
            IMapper mapper,
            IConfiguration configuration,
            ILogger<StripeController> logger,
            IUserProfileService userProfileService,
            ICompanyService companyService,
            ICompanySubscriptionService companySubscriptionService,
            ISubscriptionPlanService subscriptionPlanService,
            ICompanyInfoCacheService companyInfoCacheService,
            ICoreService coreService,
            ITwilioService twilioService,
            IResellerPortalRepository resellerPortalRepository,
            IStripeAuthenticationConfig stripeAuthenticationConfig,
            IFeatureQuantityService featureQuantityService,
            IFlowHubConfigsApi flowHubConfigsApi,
            IStripeServicesFactory stripeServicesFactory,
            ICompanyUsageService companyUsageService,
            IUsageCycleCalculator usageCycleCalculator,
            IDefaultSubscriptionPlanIdGetter defaultSubscriptionPlanIdGetter,
            IStripeSubscriptionService stripeSubscriptionService,
            IOptions<GlobalPricingOptions> globalPricingOptions)
        {
            _userManager = userManager;
            _appDbContext = appDbContext;
            _mapper = mapper;
            _configuration = configuration;
            _logger = logger;
            _userProfileService = userProfileService;
            _companyService = companyService;
            _companySubscriptionService = companySubscriptionService;
            _subscriptionPlanService = subscriptionPlanService;
            _companyInfoCacheService = companyInfoCacheService;
            _coreService = coreService;
            _twilioService = twilioService;
            _resellerPortalRepository = resellerPortalRepository;
            _stripeAuthenticationConfig = stripeAuthenticationConfig;
            _featureQuantityService = featureQuantityService;
            _flowHubConfigsApi = flowHubConfigsApi;
            _stripeServicesFactory = stripeServicesFactory;
            _companyUsageService = companyUsageService;
            _usageCycleCalculator = usageCycleCalculator;
            _defaultSubscriptionPlanIdGetter = defaultSubscriptionPlanIdGetter;
            _stripeSubscriptionService = stripeSubscriptionService;
            _globalPricingOptions = globalPricingOptions.Value;

            StripeConfiguration.ApiKey = _stripeAuthenticationConfig.StripeSecretKey;

            _isEnableTenantHubLogic = _configuration.GetValue<bool>("TenantHub:IsEnableTenantLogic");
        }

        [HttpGet]
        [Route("stripe/twilio/topup")]
        public async Task<IActionResult> TwilioTopupPlan()
        {
            var twilio = await _appDbContext.CoreTwilioTopupPlans.OrderBy(x => x.Value).ToListAsync(HttpContext.RequestAborted);
            var response = _mapper.Map<List<TwilioTopUpPlanResponse>>(twilio);
            return Ok(response);
        }

        [HttpPost]
        [Authorize]
        [Route("stripe/twilio/topup")]
        public async Task<IActionResult> TwilioTopup([FromBody] TwilioTopup twilioTopup)
        {
            if (User.Identity.IsAuthenticated)
            {
                // var companyUser = await _appDbContext.UserRoleStaffs.Where(x => x.IdentityId == userId).Include(x => x.Company).Include(x => x.Identity).FirstOrDefaultAsync();
                var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

                // var twilioSetting = await _appDbContext.ConfigWhatsAppConfigs.Where(x => x.TwilioAccountId == twilioTopup.AccountSID).FirstOrDefaultAsync();
                // if (twilioSetting == null)
                //    return BadRequest(new ResponseViewModel { message = "not found" });

                try
                {
                    var lastValidPayment = await _appDbContext.CompanyBillRecords
                        .OrderByDescending(x => x.Id)
                        .FirstOrDefaultAsync(
                            x =>
                                x.CompanyId == companyUser.CompanyId
                                && !string.IsNullOrEmpty(x.stripe_subscriptionId)
                                && !string.IsNullOrEmpty(x.customerId));

                    var topupPlan = await _appDbContext.CoreTwilioTopupPlans.Where(x => x.Id == twilioTopup.TopupPlanId)
                        .FirstOrDefaultAsync();

                    if (string.IsNullOrEmpty(twilioTopup.AccountSID))
                    {
                        var companyTwilioRecord = await _appDbContext.CompanyTwilioUsageRecords
                            .Where(x => x.CompanyId == companyUser.CompanyId).FirstOrDefaultAsync();
                        twilioTopup.AccountSID = companyTwilioRecord.TwilioAccountId;
                    }

                    var topupRecord = new TwilioTopupRecord
                    {
                        AccountSID = twilioTopup.AccountSID, TopupPlanId = twilioTopup.TopupPlanId
                    };
                    _appDbContext.CoreTwilioTopupRecords.Add(topupRecord);
                    await _appDbContext.SaveChangesAsync();

                    var name = topupPlan.Name;
                    var amount = Convert.ToInt64(topupPlan.Price);
                    var currency = topupPlan.Currency;
                    var quantity = 1;

                    var metaData = new Dictionary<string, string>()
                    {
                        {
                            "companyId", companyUser.CompanyId
                        },
                        {
                            "twilioAccountId", twilioTopup.AccountSID
                        },
                        {
                            "twilioTopupPlan", topupPlan.Id
                        },
                        {
                            "rewardful", "false"
                        }
                    };

                    var subscriptionData = new List<SessionLineItemOptions>
                    {
                        new SessionLineItemOptions
                        {
                            Price = topupPlan.Id, Quantity = quantity,
                        }
                    };

                    var appDomainName = _configuration.GetValue<String>("Values:AppDomainName");
                    var successUrl = string.IsNullOrEmpty(twilioTopup.SuccessUrl)
                        ? $"{appDomainName}/stripe/twilio/topup/success?topupId={topupRecord.Id}&data={twilioTopup.Data}"
                        : twilioTopup.SuccessUrl;
                    var cancelUrl = string.IsNullOrEmpty(twilioTopup.CancelUrl)
                        ? $"{appDomainName}/stripe/twilio/topup/cancel?topupId={topupRecord.Id}&data={twilioTopup.Data}"
                        : twilioTopup.CancelUrl;

                    var options = new SessionCreateOptions
                    {
                        SuccessUrl = successUrl,
                        CancelUrl = cancelUrl,
                        PaymentMethodTypes = new List<string>
                        {
                            "card"
                        },
                        LineItems = subscriptionData,
                        ClientReferenceId = companyUser.CompanyId,
                        Metadata = metaData,
                        Mode = "payment"
                    };

                    if (!string.IsNullOrEmpty(companyUser.Company.AffiliateCustomerId))
                    {
                        options.Customer = companyUser.Company.AffiliateCustomerId;
                    }
                    else if (string.IsNullOrEmpty(lastValidPayment?.customerId))
                    {
                        options.CustomerEmail = companyUser.Identity.Email;
                    }
                    else if (lastValidPayment.Status == BillStatus.Active ||
                             lastValidPayment.Status == BillStatus.Canceled)
                    {
                        options.Customer = lastValidPayment.customerId;
                    }
                    else
                    {
                        options.CustomerEmail = companyUser.Identity.Email;
                    }

                    var service = new SessionService();
                    Session session = service.Create(options);

                    return Ok(session);
                }
                catch (Exception ex)
                {
                    return BadRequest(
                        new ResponseViewModel
                        {
                            message = ex.Message
                        });
                }
            }

            return BadRequest();
        }

        [HttpPost]
        [Authorize]
        [Route("stripe/twilio/topup/{result}")]
        public async Task<IActionResult> CompleteTopup(string result, [FromQuery(Name = "topupId")] string topupId)
        {
            if (User.Identity.IsAuthenticated)
            {
                var topupRecord = await _appDbContext.CoreTwilioTopupRecords.Where(x => x.Id == topupId)
                    .FirstOrDefaultAsync();
                switch (result)
                {
                    case "success":
                        if (topupRecord.Status == TopupStatus.Redeemed)
                        {
                            return BadRequest(
                                new ResponseViewModel
                                {
                                    message = "redeemed"
                                });
                        }

                        var twilioConfig = await _appDbContext.ConfigWhatsAppConfigs
                            .Where(x => x.TwilioAccountId == topupRecord.AccountSID).FirstOrDefaultAsync();
                        var topupPlan = await _appDbContext.CoreTwilioTopupPlans
                            .Where(X => X.Id == topupRecord.TopupPlanId).FirstOrDefaultAsync();

                        TwilioClient.Init(twilioConfig.TwilioAccountId, twilioConfig.TwilioSecret);

                        try
                        {
                            var domainName = _configuration.GetValue<String>("Values:DomainName");

                            var callbackUrl = new Uri($"{domainName}/twilio/usage");
                            twilioConfig.TriggerValue = twilioConfig.TriggerValue + Convert.ToInt32(topupPlan.Value);
                            var triggerValue = $"+{twilioConfig.TriggerValue}";
                            TriggerResource.Delete(twilioConfig.SID);

                            var trigger = TriggerResource.Create(
                                callbackUrl,
                                triggerValue,
                                TriggerResource.UsageCategoryEnum.Totalprice,
                                twilioConfig.TwilioAccountId,
                                Twilio.Http.HttpMethod.Post,
                                "Trigger if $20 credit left",
                                TriggerResource.RecurringEnum.Alltime,
                                TriggerResource.TriggerFieldEnum.Price);

                            twilioConfig.SID = trigger.Sid;
                            twilioConfig.IsRequireTopup = false;

                            topupRecord.RedeemedAt = DateTime.UtcNow;
                            topupRecord.Status = TopupStatus.Redeemed;

                            // var twilioRecord = await _appDbContext.CompanyTwilioUsageRecords.Where(x => x.TwilioAccountId == twilioConfig.TwilioAccountId).FirstOrDefaultAsync();
                            // twilioRecord.TotalCreditValue += (decimal) topupPlan.Value;

                            await _appDbContext.SaveChangesAsync();
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(
                                ex,
                                "[Stripe {MethodName}] error creating callback trigger when Twilio credit low. {ExceptionMessage}",
                                nameof(CompleteTopup),
                                ex.Message);
                        }

                        break;
                    case "cancel":
                        topupRecord.Status = TopupStatus.Cancelled;
                        break;
                }
            }

            return BadRequest();
        }

        [HttpGet]
        [Authorize(Roles = ApplicationUserRole.ResellerPortalUser)]
        [Route("stripe/reseller/topup")]
        public async Task<IActionResult> ResellerTopUpOption()
        {
            var resellerTopUpOptions =
                await _appDbContext.CoreResellerStripeTopUpOptions.OrderBy(x => x.Value).ToListAsync(HttpContext.RequestAborted);
            var response = _mapper.Map<List<ResellerStripeTopUpOptionResponse>>(resellerTopUpOptions);
            return Ok(response);
        }

        [HttpPost]
        [Authorize(Roles = ApplicationUserRole.ResellerPortalUser)]
        [Route("stripe/reseller/topup")]
        public async Task<IActionResult> ResellerTopUp([FromBody] ResellerTopUp resellerTopUp)
        {
            if (User.Identity.IsAuthenticated)
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(
                        new ResponseViewModel()
                        {
                            message = "ResellerTopUpOptionId and ResellerCompanyProfileId are required"
                        });
                }

                var user = await _userManager.GetUserAsync(User);
                var resellerProfileResponse = await _resellerPortalRepository.GetUserIdentityReseller(user);

                if (!resellerProfileResponse.IsSuccess)
                {
                    return BadRequest(
                        new ResponseViewModel()
                        {
                            message = resellerProfileResponse.ErrorMsg
                        });
                }

                var resellerProfileId = (string) resellerProfileResponse.Data;

                try
                {
                    var topUpOption = await _appDbContext.CoreResellerStripeTopUpOptions
                        .Where(x => x.Id == resellerTopUp.ResellerTopUpOptionId).FirstOrDefaultAsync();

                    if (topUpOption == null)
                    {
                        return BadRequest(
                            new ResponseViewModel()
                            {
                                message = "Top up option id not found"
                            });
                    }

                    var resellerTopUpLog = new ResellerTransactionLog()
                    {
                        ResellerCompanyProfileId = resellerTopUp.ResellerCompanyProfileId,
                        UserIdentityId = user.Id,
                        TransactionMode = TransactionMode.TopUp,
                        TopUpMethod = ResellerTopUpMethod.Stripe,
                        TopupStatus = TopupStatus.Pending,
                        Currency = "USD",
                        TransactionCategory = ResellerTransactionCategory.Balance,
                        TransactionAction = "Reseller Account Top Up"
                    };
                    await _appDbContext.ResellerTransactionLogs.AddAsync(resellerTopUpLog);
                    await _appDbContext.SaveChangesAsync();

                    var name = topUpOption.Name;
                    var amount = Convert.ToInt64(topUpOption.Price);
                    var currency = topUpOption.Currency;
                    var quantity = 1;

                    var domainName = _configuration.GetValue<String>("Reseller:DomainName");

                    var metaData = new Dictionary<string, string>()
                    {
                        {
                            "resellerCompanyProfileId", resellerProfileId
                        },
                        {
                            "resellerCompanyId", user.CompanyId
                        },
                        {
                            "resellerUserId", user.Id
                        },
                        {
                            "resellerTopUpLogId", resellerTopUpLog.Id.ToString()
                        },
                        {
                            "resellerTopUpOption", topUpOption.Id
                        },
                        {
                            "rewardful", "false"
                        }
                    };

                    var subscriptionData = new List<SessionLineItemOptions>
                    {
                        new SessionLineItemOptions
                        {
                            Price = topUpOption.Id, Quantity = quantity,
                        }
                    };

                    var options = new SessionCreateOptions
                    {
                        SuccessUrl = $"{domainName}/settings/billing",
                        CancelUrl = $"{domainName}/settings/billing",
                        PaymentMethodTypes = new List<string>
                        {
                            "card"
                        },
                        LineItems = subscriptionData,
                        ClientReferenceId = resellerProfileId,
                        Metadata = metaData,
                        Mode = "payment"
                    };

                    options.CustomerEmail = user.Email;

                    var service = new SessionService();
                    Session session = service.Create(options);

                    return Ok(session);
                }
                catch (Exception ex)
                {
                    return BadRequest(
                        new ResponseViewModel
                        {
                            message = ex.Message
                        });
                }
            }

            return BadRequest();
        }

        [HttpGet]
        [Route("stripe/setup")]
        public async Task<IActionResult> SetupStripe(
            [FromQuery(Name = "Currency")]
            string currency = "usd",
            [FromQuery(Name = "Version")]
            int version = 8)
        {
            var responseData = new StripeSetupResponse();
            responseData.PublicKey = _stripeAuthenticationConfig.StripePublicKey;

            var subscriptionPlan = await _appDbContext.CoreSubscriptionPlans
                .Where(
                    x => ValidSubscriptionPlan.AllSubscriptionPlan.Contains(x.Id) && x.Currency == currency &&
                         x.Version == version).OrderBy(x => x.Amount).ToListAsync(HttpContext.RequestAborted);
            responseData.Plans = _mapper.Map<List<SubscriptionPlanResponse>>(subscriptionPlan);

            return Ok(responseData);
        }

        [HttpGet]
        [Authorize]
        [Route("stripe/update-card")]
        public async Task<IActionResult> UpdateCard()
        {
            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            if (companyUser == null)
            {
                return Unauthorized();
            }

            var appDomainName = _configuration.GetValue<String>("Values:AppDomainName");
            var lastValidPayment = await _appDbContext.CompanyBillRecords
                .OrderByDescending(x => x.Id)
                .FirstOrDefaultAsync(
                    x =>
                        x.CompanyId == companyUser.CompanyId
                        && !string.IsNullOrEmpty(x.stripe_subscriptionId)
                        && !string.IsNullOrEmpty(x.customerId));

            var options = new SessionCreateOptions
            {
                PaymentMethodTypes = new List<string>
                {
                    "card",
                },
                Customer = lastValidPayment.customerId,
                SetupIntentData = new SessionSetupIntentDataOptions
                {
                    Metadata = new Dictionary<string, string>
                    {
                        {
                            "customer_id", lastValidPayment.customerId
                        },
                        {
                            "subscription_id", lastValidPayment.stripe_subscriptionId
                        },
                    }
                },
                Mode = "setup",
                SuccessUrl = $"{appDomainName}/stripe/update/success",
                CancelUrl = $"{appDomainName}/stripe/update/cancel"
            };

            var service = new SessionService();
            var session = service.Create(options);

            return Ok(session);
        }

        [HttpPost]
        [Authorize]
        [Route("stripe/update-card")]
        public async Task<IActionResult> UpdateCard([FromBody] StripeSessionRedirectionInput input)
        {
            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            if (companyUser == null)
            {
                return Unauthorized();
            }

            var appDomainName = _configuration.GetValue<String>("Values:AppDomainName");

            // Redirect link handling
            var successUrl = string.IsNullOrEmpty(input?.SuccessUrl)
                ? $"{appDomainName}/stripe/update/success"
                : input.SuccessUrl;
            var cancelUrl = string.IsNullOrEmpty(input?.CancelUrl)
                ? $"{appDomainName}/stripe/update/cancel"
                : input.CancelUrl;

            var lastValidPayment = await _appDbContext.CompanyBillRecords
                .OrderByDescending(x => x.Id)
                .FirstOrDefaultAsync(
                    x =>
                        x.CompanyId == companyUser.CompanyId
                        && !string.IsNullOrEmpty(x.stripe_subscriptionId)
                        && !string.IsNullOrEmpty(x.customerId));

            var options = new SessionCreateOptions
            {
                PaymentMethodTypes = new List<string>
                {
                    "card",
                },
                Customer = lastValidPayment.customerId,
                SetupIntentData = new SessionSetupIntentDataOptions
                {
                    Metadata = new Dictionary<string, string>
                    {
                        {
                            "customer_id", lastValidPayment.customerId
                        },
                        {
                            "subscription_id", lastValidPayment.stripe_subscriptionId
                        },
                    }
                },
                Mode = "setup",
                SuccessUrl = successUrl,
                CancelUrl = cancelUrl
            };

            var service = new SessionService();
            var session = await service.CreateAsync(options);

            return Ok(session);
        }

        [HttpPost("stripe/create-customer-portal-session")]
        [Authorize]
        public async Task<IActionResult> CustomerPortal([FromBody] StripeCustomerPortalSessionRedirectionInput input)
        {
            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            if (companyUser == null)
            {
                return Unauthorized();
            }

            var lastValidPayment = await _appDbContext.CompanyBillRecords
                .OrderByDescending(x => x.Id)
                .FirstOrDefaultAsync(
                    x =>
                        x.CompanyId == companyUser.CompanyId
                        && !string.IsNullOrEmpty(x.stripe_subscriptionId)
                        && !string.IsNullOrEmpty(x.customerId));

            // Redirect link handling
            var appDomainName = _configuration.GetValue<String>("Values:AppDomainName");
            var returnUrl = string.IsNullOrEmpty(input.ReturnUrl)
                ? $"{appDomainName}/settings/plansubscription"
                : input.ReturnUrl;

            // Authenticate your user.
            var options = new Stripe.BillingPortal.SessionCreateOptions
            {
                Customer = lastValidPayment.customerId,
                ReturnUrl = $"{appDomainName}/settings/plansubscription",
            };
            var service = new Stripe.BillingPortal.SessionService();
            var session = await service.CreateAsync(options);

            dynamic response = new JObject();
            response.url = session.Url;

            return Ok(response);
        }

        [HttpPost]
        [Authorize]
        [Route("v2/stripe/create-checkout-session")]
        public async Task<IActionResult> CreateCheckoutSession([FromBody] StripeViewModel stripeViewModel)
        {
            if (User.Identity.IsAuthenticated)
            {
                try
                {
                    var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

                    if (companyUser == null)
                    {
                        return Unauthorized();
                    }

                    var lastValidPayment = await _appDbContext.CompanyBillRecords
                        .OrderByDescending(x => x.Id)
                        .FirstOrDefaultAsync(
                            x =>
                                x.CompanyId == companyUser.CompanyId
                                && !string.IsNullOrEmpty(x.stripe_subscriptionId)
                                && !string.IsNullOrEmpty(x.customerId));

                    var subscriptionPlanId = string.Empty;

                    var metaData = new Dictionary<string, string>()
                    {
                        {
                            "companyId", companyUser.CompanyId
                        }
                    };

                    var lmref = (string.IsNullOrEmpty(companyUser.Company.lmref))
                        ? stripeViewModel.lmref
                        : companyUser.Company.lmref;
                    if (!string.IsNullOrEmpty(lmref))
                    {
                        metaData.Add("lmref", lmref);
                    }

                    var subscriptionData = new SessionSubscriptionDataOptions
                    {
                        Items = new List<SessionSubscriptionDataItemOptions>(), Metadata = metaData
                    };

                    var oneoffPaidAddons = new List<SessionLineItemOptions>();

                    var companyId = companyUser.CompanyId;

                    var baseSubscriptionBillRecord = await _companySubscriptionService.GetCurrentBaseSubscriptionBillRecord(companyId);

                    var companySubscriptionInfo = await _companyService.GetCompanySubscriptionInfo(companyId);
                    var companyBasePlanId = companySubscriptionInfo.BasePlanId;

                    foreach (var stripePlan in stripeViewModel.SubscriptionItems)
                    {
                        var isCompanySubscriptionRecord =
                            await _companySubscriptionService.IsCompanySubscriptionRecord(
                                companyUser.CompanyId,
                                stripePlan.PlanId);

                        if (stripePlan.IsFreeTrial)
                        {
                            var companySubscriptionTier =
                                await _companySubscriptionService.GetCompanySubscriptionTierAsync(companyUser.CompanyId);

                            if (ValidSubscriptionPlan.SalesforceCrmIntegrationAddOns.Contains(stripePlan.PlanId))
                            {
                                if (isCompanySubscriptionRecord ||
                                    stripePlan.Quantity != 1 ||
                                    companySubscriptionTier == SubscriptionTier.Free)
                                {
                                    return BadRequest(
                                        new ResponseViewModel
                                        {
                                            message = "Failed SalesForce CRM integration free trial!"
                                        });
                                }

                                subscriptionData.TrialPeriodDays = 31;
                            }

                            if (ValidSubscriptionPlan.HubspotIntegrationAddOns.Contains(stripePlan.PlanId))
                            {
                                if (isCompanySubscriptionRecord ||
                                    stripePlan.Quantity != 1 ||
                                    companySubscriptionTier == SubscriptionTier.Free)
                                {
                                    return BadRequest(
                                        new ResponseViewModel
                                        {
                                            message = "Failed HubSpot CRM integration free trial!"
                                        });
                                }

                                subscriptionData.TrialPeriodDays = 31;
                            }
                        }
                        else
                        {
                            if (!isCompanySubscriptionRecord)
                            {
                                if (ValidSubscriptionPlan.SalesforceCrmIntegrationAddOns.Contains(stripePlan.PlanId) ||
                                    ValidSubscriptionPlan.HubspotIntegrationAddOns.Contains(stripePlan.PlanId))
                                {
                                    return BadRequest(
                                        new ResponseViewModel
                                        {
                                            message = "Direct Salesforce/Hubspot subscription purchase is not allowed!"
                                        });
                                }
                            }
                        }

                        var planId = stripePlan.PlanId;

                        var quantity = 1;

                        var subscriptionPlan = await _appDbContext.CoreSubscriptionPlans.Where(x => x.Id == planId)
                            .FirstOrDefaultAsync();

                        if (subscriptionPlan.SubscriptionTier == SubscriptionTier.AddOn ||
                            subscriptionPlan.SubscriptionTier == SubscriptionTier.Agent)
                        {
                            try
                            {
                                if (baseSubscriptionBillRecord.IsOnCancelling)
                                {
                                    throw new ApplicationException("Unable to purchase add-on as current subscription plan are on cancelling.");
                                }

                                if (subscriptionPlan.Version == SubscriptionPlanVersions.Version10)
                                {
                                    await _subscriptionPlanService.CheckIsAddOnPurchasable(companyId, subscriptionPlan);

                                    var featureId = _subscriptionPlanService.GetFeatureId(subscriptionPlan);
                                    var purchasePlanFeatureQuantity = await _featureQuantityService.GetPlanFeatureQuantity(subscriptionPlan.Id, featureId);
                                    var purchaseQuantity = stripePlan.Quantity * purchasePlanFeatureQuantity.Quantity;

                                    var isFeatureExceedPurchaseLimit = await _featureQuantityService.IsPurchaseFeatureExceedPurchaseLimit(companyId, companyBasePlanId, featureId, purchaseQuantity);

                                    if (isFeatureExceedPurchaseLimit)
                                    {
                                        throw new ApplicationException($"Exceed purchase limit for {featureId}");
                                    }
                                }
                            }
                            catch (ApplicationException exception)
                            {
                                return BadRequest(new ResponseViewModel(exception.Message));
                            }
                        }

                        if (subscriptionPlan.Id.Contains("oneoff"))
                        {
                            oneoffPaidAddons.Add(
                                new SessionLineItemOptions
                                {
                                    Name = subscriptionPlan.SubscriptionName,
                                    Amount = Convert.ToInt64(subscriptionPlan.Amount) * 100,
                                    Currency = subscriptionPlan.Currency,
                                    Description = subscriptionPlan.SubscriptionName,
                                    Quantity = stripePlan.Quantity
                                });
                        }

                        switch (subscriptionPlan.SubscriptionTier)
                        {
                            case SubscriptionTier.AddOn:
                            case SubscriptionTier.Agent:
                                quantity = stripePlan.Quantity;
                                metaData.Add("rewardful", "false");
                                if (string.IsNullOrEmpty(subscriptionPlanId))
                                {
                                    subscriptionPlanId = subscriptionPlan.Id;
                                }

                                break;
                            case SubscriptionTier.Premium:
                            case SubscriptionTier.Pro:
                            case SubscriptionTier.Enterprise:
                                subscriptionPlanId = subscriptionPlan.Id;
                                break;
                        }

                        if (subscriptionData.Items.All(x => x.Plan != planId))
                        {
                            subscriptionData.Items.Add(
                                new SessionSubscriptionDataItemOptions
                                {
                                    Plan = subscriptionPlan.StripePlanId, Quantity = quantity
                                });
                        }

                        if (stripePlan.ShopifyConfigId != null)
                        {
                            metaData.Add("isShopifyRenewal", "true");
                            metaData.Add("shopifyConfigId", stripePlan.ShopifyConfigId);
                        }
                        else
                        {
                            metaData.Add("isShopifyRenewal", "false");
                        }
                    }

                    var json = stripeViewModel.Data;

                    var appDomainName = _configuration.GetValue<String>("Values:AppDomainName");
                    var successUrl = string.IsNullOrEmpty(stripeViewModel.SuccessUrl)
                        ? $"{appDomainName}/stripe/success?companyId={companyUser.CompanyId}&planId={subscriptionPlanId}&data={json}"
                        : stripeViewModel.SuccessUrl;
                    var cancelUrl = string.IsNullOrEmpty(stripeViewModel.CancelUrl)
                        ? $"{appDomainName}/stripe/cancel?companyId={companyUser.CompanyId}&planId={subscriptionPlanId}&data={json}"
                        : stripeViewModel.CancelUrl;

                    var options = new SessionCreateOptions
                    {
                        SuccessUrl = successUrl,
                        CancelUrl = cancelUrl,
                        PaymentMethodTypes = new List<string>
                        {
                            "card"
                        },
                        ClientReferenceId = companyUser.CompanyId,
                        AllowPromotionCodes = true,
                        Metadata = metaData
                    };

                    if (oneoffPaidAddons == null || oneoffPaidAddons.Count == 0)
                    {
                        options.SubscriptionData = subscriptionData;
                    }
                    else
                    {
                        options.Mode = "payment";
                        options.LineItems = oneoffPaidAddons;
                    }

                    var plan = await _appDbContext.CoreSubscriptionPlans.FirstOrDefaultAsync(
                        x =>
                            x.Id == stripeViewModel.SubscriptionItems[0].PlanId);

                    if (!string.IsNullOrEmpty(companyUser.Company.AffiliateCustomerId))
                    {
                        options.Customer = companyUser.Company.AffiliateCustomerId;
                    }
                    else if (lastValidPayment != null)
                    {
                        options.Customer = lastValidPayment.customerId;
                    }
                    else
                    {
                        var invoiceFooter = await GetRegionalInvoiceFooterBySubscriptionCurrency(plan.Currency);

                        if (string.IsNullOrEmpty(invoiceFooter))
                        {
                            options.CustomerEmail = companyUser.Identity.Email;
                        }
                        else
                        {
                            var customerService = new CustomerService();

                            var customer = await customerService.CreateAsync(
                                new CustomerCreateOptions
                                {
                                    Email = companyUser.Identity.Email,
                                    InvoiceSettings = new CustomerInvoiceSettingsOptions
                                    {
                                        Footer = invoiceFooter
                                    }
                                });

                            options.Customer = customer.Id;
                        }
                    }

                    // Create Checkout Session on Stripe
                    Session session;
                    try
                    {
                        var service = new SessionService();
                        session = await service.CreateAsync(options);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(
                            ex,
                            "[{MethodName}] error when create stripe checkout session {CompanyId}, request {Request}, options {options}",
                            nameof(CreateCheckoutSession),
                            companyUser.CompanyId,
                            JsonConvert.SerializeObject(stripeViewModel),
                            JsonConvert.SerializeObject(options));

                        throw;
                    }

                    return Ok(session);
                }
                catch (Exception ex)
                {
                    _logger.LogError(
                        ex,
                        "[{MethodName}] error when process create stripe checkout session, request {Request}",
                        nameof(CreateCheckoutSession),
                        JsonConvert.SerializeObject(stripeViewModel));

                    return BadRequest(
                        new ResponseViewModel
                        {
                            message = "error when process create stripe checkout session"
                        });
                }
            }

            return BadRequest();
        }

        [HttpPost]
        [Route("stripe/other/create-checkout-session")]
        public IActionResult CreateOtherCheckoutSession([FromBody] JObject data)
        {
            try
            {
                var lmref = data["lmref"]?.ToString();
                var planId = data["planId"].ToString();
                var trial = data["trial"]?.ToString();
                var email = data["email"]?.ToString();
                var quantity = 1;
                if (!string.IsNullOrEmpty(data["quantity"]?.ToString()))
                {
                    quantity = int.Parse(data["quantity"]?.ToString());
                }

                var appDomainName = _configuration.GetValue<String>("Values:AppDomainName");
                var successUrl = string.IsNullOrEmpty(data["successUrl"]?.ToString())
                    ? $"{appDomainName}/stripe/success"
                    : data["successUrl"]?.ToString();
                var cancelUrl = string.IsNullOrEmpty(data["cancelUrl"]?.ToString())
                    ? $"{appDomainName}/stripe/cancel"
                    : data["cancelUrl"]?.ToString();

                var subscriptionData = new SessionSubscriptionDataOptions
                {
                    Items = new List<SessionSubscriptionDataItemOptions>()
                    {
                        new SessionSubscriptionDataItemOptions
                        {
                            Plan = planId, Quantity = quantity,
                            // PlanId = data["planId"].ToString()
                        },
                    },
                    Metadata = new Dictionary<string, string>()
                    {
                        {
                            "lm_data", lmref
                        }
                    },
                };

                var trialPeriod = 0;
                if (!string.IsNullOrEmpty(trial))
                {
                    trialPeriod = int.Parse(trial);
                }

                if (trialPeriod > 0)
                {
                    subscriptionData.TrialPeriodDays = trialPeriod;
                }

                var options = new SessionCreateOptions
                {
                    SuccessUrl = successUrl,
                    CancelUrl = cancelUrl,
                    PaymentMethodTypes = new List<string>
                    {
                        "card"
                    },
                    SubscriptionData = subscriptionData,
                    AllowPromotionCodes = true,
                };

                if (!string.IsNullOrEmpty(email))
                {
                    options.CustomerEmail = email;
                }

                var service = new SessionService();
                Session session = service.Create(options);

                return Ok(session);
            }
            catch (Exception ex)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        message = ex.Message
                    });
            }
        }

        [HttpPost]
        [Route("stripe/one-time/create-checkout-session")]
        public IActionResult CreateOneTimeCheckoutSession([FromBody] JObject data)
        {
            try
            {
                var lmref = data["lmref"]?.ToString();
                var _data = data["data"]?.ToString();
                var name = data["name"]?.ToString();
                var amount = data["amount"]?.ToString();
                var email = data["email"]?.ToString();
                var currency = data["currency"]?.ToString();
                var quantity = 1;
                if (!string.IsNullOrEmpty(data["quantity"]?.ToString()))
                {
                    quantity = int.Parse(data["quantity"]?.ToString());
                }

                if (string.IsNullOrEmpty(_data))
                {
                    _data = string.Empty;
                }

                // Redirect link handling
                var appDomainName = _configuration.GetValue<String>("Values:AppDomainName");
                var successUrl = string.IsNullOrEmpty(data["successUrl"]?.ToString())
                    ? $"{appDomainName}/stripe/success?data={System.Web.HttpUtility.UrlEncode(_data)}"
                    : data["successUrl"]?.ToString();
                var cancelUrl = string.IsNullOrEmpty(data["cancelUrl"]?.ToString())
                    ? $"{appDomainName}/stripe/cancel?data={System.Web.HttpUtility.UrlEncode(_data)}"
                    : data["cancelUrl"]?.ToString();

                var subscriptionData = new List<SessionLineItemOptions>
                {
                    new SessionLineItemOptions
                    {
                        Name = name, Amount = long.Parse(amount) * 100, Currency = currency, Quantity = quantity
                    },
                };

                var options = new SessionCreateOptions
                {
                    SuccessUrl = successUrl,
                    CancelUrl = cancelUrl,
                    PaymentMethodTypes = new List<string>
                    {
                        "card"
                    },
                    LineItems = subscriptionData,
                    AllowPromotionCodes = true
                };

                if (!string.IsNullOrEmpty(email))
                {
                    options.CustomerEmail = email;
                }

                var service = new SessionService();
                Session session = service.Create(options);

                return Ok(session);
            }
            catch (Exception ex)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        message = ex.Message
                    });
            }
        }

        [HttpPost]
        [Route("stripe/one-time/alipay/create-checkout-session")]
        public IActionResult CreateAlipayOneTimeCheckoutSession([FromBody] JObject data)
        {
            try
            {
                var lmref = data["lmref"]?.ToString();
                var name = data["name"]?.ToString();
                var amount = data["amount"]?.ToString();
                var email = data["email"]?.ToString();
                var currency = data["currency"]?.ToString();
                var quantity = 1;
                if (!string.IsNullOrEmpty(data["quantity"]?.ToString()))
                {
                    quantity = int.Parse(data["quantity"]?.ToString());
                }

                var appDomainName = _configuration.GetValue<String>("Values:AppDomainName");
                var successUrl = string.IsNullOrEmpty(data["successUrl"]?.ToString())
                    ? $"{appDomainName}/stripe/success"
                    : data["successUrl"]?.ToString();
                var cancelUrl = string.IsNullOrEmpty(data["cancelUrl"]?.ToString())
                    ? $"{appDomainName}/stripe/cancel"
                    : data["cancelUrl"]?.ToString();

                var subscriptionData = new List<SessionLineItemOptions>
                {
                    new SessionLineItemOptions
                    {
                        Name = name, Amount = long.Parse(amount) * 100, Currency = currency, Quantity = quantity
                    },
                };

                var options = new SessionCreateOptions
                {
                    SuccessUrl = successUrl,
                    CancelUrl = cancelUrl,
                    PaymentMethodTypes = new List<string>
                    {
                        "alipay"
                    },
                    LineItems = subscriptionData
                };

                if (!string.IsNullOrEmpty(email))
                {
                    options.CustomerEmail = email;
                }

                var service = new SessionService();
                Session session = service.Create(options);

                return Ok(session);
            }
            catch (Exception ex)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        message = ex.Message
                    });
            }
        }

        [HttpPost]
        [Authorize]
        [Route("stripe/create-checkout-session")]
        public async Task<IActionResult> CreateCheckoutSession([FromBody] JObject data)
        {
            if (User.Identity.IsAuthenticated)
            {
                try
                {
                    var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

                    if (companyUser != null)
                    {
                        var planId = data["planId"].ToString();
                        var extraAgent = data["extra_agent"]?.ToString();
                        int trialPeriod = 0;

                        var quantity = 1;

                        var lastValidPayment = await _appDbContext.CompanyBillRecords
                            .OrderByDescending(x => x.Id)
                            .FirstOrDefaultAsync(
                                x =>
                                    x.CompanyId == companyUser.CompanyId
                                    && !string.IsNullOrEmpty(x.stripe_subscriptionId)
                                    && !string.IsNullOrEmpty(x.customerId));

                        if (planId == "sleekflow_pro")
                        {
                            planId = "sleekflow_v2_pro";
                        }

                        var metaData = new Dictionary<string, string>()
                        {
                            {
                                "companyId", companyUser.CompanyId
                            }
                        };

                        var subscriptionPlan = await _appDbContext.CoreSubscriptionPlans.Where(x => x.Id == planId)
                            .FirstOrDefaultAsync();
                        switch (subscriptionPlan.SubscriptionTier)
                        {
                            case SubscriptionTier.AddOn:
                            case SubscriptionTier.Agent:
                                if (!(string.IsNullOrEmpty(data["quantity"]?.ToString())))
                                {
                                    quantity = int.Parse(data["quantity"]?.ToString());
                                }

                                metaData.Add("rewardful", "false");
                                break;
                            case SubscriptionTier.Enterprise:
                            case SubscriptionTier.Premium:
                            case SubscriptionTier.Pro:
                                try
                                {
                                    var quota = subscriptionPlan.IncludedAgents;
                                    // if (companyUser.Company.MaximumAgents > subscriptionPlan.IncludedAgents)
                                    //    quota = companyUser.Company.MaximumAgents;

                                    var currentAgents = await _appDbContext.UserRoleStaffs
                                        .CountAsync(
                                            x =>
                                                x.CompanyId == companyUser.CompanyId
                                                && x.Id != 1);


                                    var additional = quota - currentAgents;
                                    if (additional < 0)
                                    {
                                        extraAgent = (additional * -1).ToString();
                                    }
                                    else
                                    {
                                        extraAgent = null;
                                    }
                                }
                                catch (Exception ex)
                                {
                                    _logger.LogError(ex, ex.Message);
                                }

                                break;
                        }

                        var lmref = (string.IsNullOrEmpty(companyUser.Company.lmref))
                            ? data["lmref"]?.ToString()
                            : companyUser.Company.lmref;
                        if (!string.IsNullOrEmpty(lmref))
                        {
                            metaData.Add("lmref", lmref);
                        }

                        var subscriptionData = new SessionSubscriptionDataOptions
                        {
                            Items = new List<SessionSubscriptionDataItemOptions>()
                            {
                                new SessionSubscriptionDataItemOptions
                                {
                                    Plan = subscriptionPlan.StripePlanId, Quantity = quantity,
                                    // PlanId = data["planId"].ToString()
                                },
                            },
                            Metadata = metaData
                        };

                        if (!string.IsNullOrEmpty(extraAgent))
                        {
                            try
                            {
                                if (!ValidSubscriptionPlan.AgentPlan.Contains(planId))
                                {
                                    var targetPlanSubscription = await _appDbContext.CoreSubscriptionPlans
                                        .Where(x => x.Id == subscriptionPlan.ExtraChatAgentPlan).FirstOrDefaultAsync();
                                    subscriptionData.Items.Add(
                                        new SessionSubscriptionDataItemOptions
                                        {
                                            Plan = targetPlanSubscription.StripePlanId, Quantity = int.Parse(extraAgent)
                                        });
                                }
                            }
                            catch (Exception ex)
                            {
                                _logger.LogError(
                                    ex,
                                    "Add extra Agents Error {sleekflow_company_id}",
                                    companyUser.CompanyId);
                            }
                        }

                        if (trialPeriod > 0)
                        {
                            subscriptionData.TrialPeriodDays = trialPeriod;
                        }

                        var json = data["data"]?.ToString();
                        var appDomainName = _configuration.GetValue<String>("Values:AppDomainName");
                        var successUrl = string.IsNullOrEmpty(data["successUrl"]?.ToString())
                            ? $"{appDomainName}/stripe/success?companyId={companyUser.CompanyId}&planId={planId}&data={json}"
                            : data["successUrl"]?.ToString();
                        var cancelUrl = string.IsNullOrEmpty(data["cancelUrl"]?.ToString())
                            ? $"{appDomainName}/stripe/cancel?companyId={companyUser.CompanyId}&planId={planId}&data={json}"
                            : data["cancelUrl"]?.ToString();


                        var options = new SessionCreateOptions
                        {
                            SuccessUrl = successUrl,
                            CancelUrl = cancelUrl,
                            PaymentMethodTypes = new List<string>
                            {
                                "card"
                            },
                            ClientReferenceId = companyUser.CompanyId,
                            // CustomerEmail = companyUser.Identity.Email,
                            SubscriptionData = subscriptionData,
                            AllowPromotionCodes = true,
                            Metadata = metaData
                        };

                        if (!string.IsNullOrEmpty(companyUser.Company.AffiliateCustomerId))
                        {
                            options.Customer = companyUser.Company.AffiliateCustomerId;
                        }
                        else if (string.IsNullOrEmpty(lastValidPayment?.customerId))
                        {
                            options.CustomerEmail = companyUser.Identity.Email;
                        }
                        else if (lastValidPayment.Status == BillStatus.Active ||
                                 lastValidPayment.Status == BillStatus.Canceled)
                        {
                            options.Customer = lastValidPayment.customerId;
                        }
                        else
                        {
                            options.CustomerEmail = companyUser.Identity.Email;
                        }

                        var service = new SessionService();
                        Session session = service.Create(options);

                        return Ok(session);
                    }
                }
                catch (Exception ex)
                {
                    return BadRequest(
                        new ResponseViewModel
                        {
                            message = "error"
                        });
                }
            }

            return BadRequest();
        }

        [HttpGet]
        [Route("subscription/cancel/reason")]
        [Route("subscription/cannel/reason")]
        public IActionResult CancelSubscriptionReason()
        {
            if (User.Identity.IsAuthenticated)
            {
                var reasons = new List<string>
                {
                    "I no longer need it",
                    "I found a better tool",
                    "The product doesn’t work/ do what I need",
                    "It’s too expensive",
                    "It’s too hard to use",
                    "Other"
                };

                return Ok(reasons);
            }

            return BadRequest();
        }

        [HttpPost]
        [Route("stripe/webhook")]
        public async Task<IActionResult> Webhook()
        {
            // var json = new StreamReader(HttpContext.Request.Body).ReadToEnd();
            var json = await new StreamReader(HttpContext.Request.Body).ReadToEndAsync();

            _logger.LogInformation("stripe/webhook {json}", json);

            try
            {
                var stripeEvent = EventUtility.ParseEvent(json, false);

                // Handle the event
                switch (stripeEvent.Type)
                {
                    case Events.InvoicePaymentFailed:
                        var invoicePaymentFailed = stripeEvent.Data.Object as Invoice;

                        // Is the event from other hubs instead of travis_backend
                        if (invoicePaymentFailed != null && invoicePaymentFailed.Metadata.ContainsKey("source"))
                        {
                            _logger.LogInformation(
                                "The source of the event:{StripeEventId} is not from Travis_backend, will be ignored",
                                stripeEvent.Id);

                            return Ok();
                        }

                        var failedSubscriptionInfos = invoicePaymentFailed.Lines.Data.ToList();

                        try
                        {
                            var companyId = failedSubscriptionInfos.FirstOrDefault().Metadata["companyId"];
                            var company = await _appDbContext.CompanyCompanies.Where(x => x.Id == companyId)
                                .FirstOrDefaultAsync();
                            company.IsPaymentFailed = true;

                            _appDbContext.CompanyPaymentFailedLogs.Add(
                                new CompanyPaymentFailedLog()
                                {
                                    CompanyId = companyId,
                                });

                            await _appDbContext.SaveChangesAsync();
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(
                                ex,
                                "POST stripe/webhook error when processing event type {EventType}. {ExceptionMessage}",
                                stripeEvent.Type,
                                ex.Message);
                        }

                        break;

                    case Events.CustomerSubscriptionCreated:
                        var createdSubscription = stripeEvent.Data.Object as Subscription;
                        // Is the event from other hubs instead of travis_backend
                        if (createdSubscription != null && createdSubscription.Metadata.ContainsKey("source"))
                        {
                            _logger.LogInformation(
                                "The source of the event:{StripeEventId} is not from Travis_backend, will be ignored",
                                stripeEvent.Id);

                            return Ok();
                        }

                        var createdSubscriptionCompanyId = createdSubscription.Metadata["companyId"];

                        try
                        {
                            if (createdSubscription.Status == "trialing")
                            {
                                var subscriptionBillRecord = await _appDbContext.CompanyBillRecords.FirstOrDefaultAsync(
                                    b =>
                                        b.CompanyId == createdSubscriptionCompanyId &&
                                        b.stripe_subscriptionId == createdSubscription.Id);

                                if (subscriptionBillRecord != null)
                                {
                                    subscriptionBillRecord.IsFreeTrial = true;

                                    await _appDbContext.SaveChangesAsync();
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(
                                ex,
                                "POST stripe/webhook error when processing event type {EventType}. {ExceptionMessage}",
                                stripeEvent.Type,
                                ex.Message);
                        }

                        break;
                    case Events.CustomerSubscriptionUpdated:
                        return await HandleStripeCustomerSubscriptionUpdatedEvent(stripeEvent);
                    case Events.CustomerSubscriptionDeleted:
                        return await HandleStripeCustomerSubscriptionDeletedEvent(stripeEvent);
                    case Events.CheckoutSessionCompleted:
                        return await HandleStripeCheckoutSessionCompletedEvent(stripeEvent);
                    case Events.InvoicePaid:
                        var invoicePayment = stripeEvent.Data.Object as Invoice;

                        if (invoicePayment != null && invoicePayment.Metadata.ContainsKey("source"))
                        {
                            _logger.LogInformation(
                                "The source of the event:{StripeEventId} is not from Travis_backend, will be ignored",
                                stripeEvent.Id);

                            return Ok();
                        }

                        var subscriptionInfos = invoicePayment.Lines.Data.ToList();

                        var subscriptionUser = await _appDbContext.UserRoleStaffs
                            .Include(x => x.Company.BillRecords)
                            .Include(x => x.Identity)
                            .FirstOrDefaultAsync(x => x.Identity.Email.ToLower() == invoicePayment.CustomerEmail.ToLower());

                        try
                        {
                            var companyId = subscriptionInfos.Where(x => x.Metadata.Any(x => x.Key == "companyId"))
                                .FirstOrDefault()
                                ?.Metadata["companyId"];

                            if (!string.IsNullOrWhiteSpace(companyId))
                            {
                                //// Trim CompanyId to avoid extra spaces or linebreaks when creating custom plans in Stripe.
                                companyId = companyId.Trim();
                            }

                            if (string.IsNullOrEmpty(companyId))
                            {
                                BackgroundJob.Enqueue<IEmailNotificationService>(
                                    x => x.SendSystemAlertToSlackChannel(
                                        $"[Stripe] Missing companyId Metadata on invoice payment event",
                                        $"[[Invoice]]: <a clicktracking=off href='https://dashboard.stripe.com/invoices/{invoicePayment.Id}'>{invoicePayment.Id}</a>\n" +
                                        $"[[Customer]]: {invoicePayment.CustomerEmail} (<a clicktracking=off href='https://dashboard.stripe.com/customers/{invoicePayment.Id}'>{invoicePayment.Id}</a>)\n" +
                                        $"[[Payload]]:\n" +
                                        $"<pre>{json}</pre>",
                                        "stripe",
                                        "notification",
                                        null,
                                        false));
                            }
                            else if (!string.IsNullOrEmpty(companyId) && companyId != subscriptionUser?.CompanyId)
                            {
                                subscriptionUser = await _appDbContext.UserRoleStaffs
                                    .Where(x => x.CompanyId == companyId).Include(x => x.Company.BillRecords)
                                    .Include(x => x.Identity).FirstOrDefaultAsync();
                            }
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(
                                ex,
                                "POST stripe/webhook error when processing event type {EventType}. {ExceptionMessage}",
                                stripeEvent.Type,
                                ex.Message);
                        }

                        if (subscriptionUser == null)
                        {
                            var env = _configuration["SF_ENVIRONMENT"] ?? string.Empty;
                            _logger.LogInformation(
                                $"Invoice events related to invoice {invoicePayment.Id}, " +
                                $"customer {invoicePayment.CustomerId}, " +
                                $"subscription {invoicePayment.SubscriptionId} +" +
                                $"have no matching company in the current {env} environment");

                            return new OkObjectResult(
                                $"Invoice events related to invoice {invoicePayment.Id}, " +
                                $"customer {invoicePayment.CustomerId}, " +
                                $"subscription {invoicePayment.SubscriptionId} is not to be handled in this environment");
                        }

                        var lastPayment = await _appDbContext.CompanyBillRecords
                            .OrderByDescending(x => x.Id)
                            .Include(x => x.SubscriptionPlan)
                            .FirstOrDefaultAsync(
                                x =>
                                    x.CompanyId == subscriptionUser.CompanyId
                                    && x.Status != BillStatus.Inactive
                                    && x.Status != BillStatus.Terminated
                                    && ValidSubscriptionPlan.PaidPlan.Contains(x.SubscriptionPlanId));

                        var isBillUpgraded = false;
                        var isBillDowngraded = false;
                        var isBaseSubscriptionPlanIncluded = false;
                        var isFlowHubLimitChanged = false;

                        if (subscriptionUser.Company.IsPaymentFailed)
                        {
                            subscriptionUser.Company.IsPaymentFailed = false;
                        }

                        if (subscriptionUser.Company.SubscriptionTrialDays.HasValue)
                        {
                            subscriptionUser.Company.SubscriptionTrialDays = null;
                        }

                        bool hasSubscriptionCountryTierAndCurrency = await _companyService.HasSubscriptionCountryTierAndCurrencyAsync(subscriptionUser.CompanyId);

                        foreach (var subscriptionInfo in subscriptionInfos)
                        {
                            var isProratedCancelledItem = subscriptionInfo.Proration &&
                                                          (subscriptionInfo.Amount < 0 ||
                                                           subscriptionInfo.Description.Contains("Unused time") ||
                                                           subscriptionInfo.Description.Contains("未使用"));

                            var subscriptionPlan = await _subscriptionPlanService.GetSubscriptionPlanByStripePlanIdAsync(subscriptionInfo.Plan.Id);

                            #region Create Subscription Plan if Not Found

                            if (subscriptionPlan == null)
                            {
                                if (ValidSubscriptionPlan.EnterpriseTier.Any(x => subscriptionInfo.Plan.Id.Contains(x)))
                                {
                                    string enterprisePlanId = ValidSubscriptionPlan.EnterpriseTier.First(x => subscriptionInfo.Plan.Id.Contains(x));
                                    subscriptionPlan = await _subscriptionPlanService.GetSubscriptionPlanByStripePlanIdAsync(enterprisePlanId);
                                }
                                else
                                {
                                    // Add subscription plan if not exist in db
                                    subscriptionPlan = new SubscriptionPlan
                                    {
                                        Id = subscriptionInfo.Plan.Id,
                                        StripePlanId = subscriptionInfo.Plan.Id,
                                        SubscriptionName = subscriptionInfo.Plan.Nickname,
                                        Description = subscriptionInfo.Plan.Nickname,
                                        Amount = subscriptionInfo.Plan.Amount.Value / 100
                                    };

                                    if (subscriptionPlan.Id.Contains("premium") &&
                                        subscriptionPlan.Id.Contains("yearly"))
                                    {
                                        subscriptionPlan.IncludedAgents = 5;
                                        subscriptionPlan.MaximumAPICall = 1200000;
                                        subscriptionPlan.MaximumAutomation = 999;
                                        subscriptionPlan.MaximumCampaignSent = 50000000;
                                        subscriptionPlan.MaximumNumberOfChannel = 999;
                                        subscriptionPlan.MaximumContact = 10000;
                                        subscriptionPlan.MaximumMessageSent = 50000000;
                                    }
                                    else if (subscriptionPlan.Id.Contains("premium"))
                                    {
                                        subscriptionPlan.IncludedAgents = 5;
                                        subscriptionPlan.MaximumAPICall = 100000;
                                        subscriptionPlan.MaximumAutomation = 999;
                                        subscriptionPlan.MaximumCampaignSent = 50000000;
                                        subscriptionPlan.MaximumNumberOfChannel = 999;
                                        subscriptionPlan.MaximumContact = 10000;
                                        subscriptionPlan.MaximumMessageSent = 50000000;
                                    }
                                    else if (subscriptionPlan.Id.Contains("pro") &&
                                             subscriptionPlan.Id.Contains("yearly"))
                                    {
                                        subscriptionPlan.IncludedAgents = 3;
                                        subscriptionPlan.MaximumAPICall = 60000;
                                        subscriptionPlan.MaximumAutomation = 999;
                                        subscriptionPlan.MaximumCampaignSent = 60000;
                                        subscriptionPlan.MaximumNumberOfChannel = 999;
                                        subscriptionPlan.MaximumContact = 2000;
                                        subscriptionPlan.MaximumMessageSent = 60000;
                                    }
                                    else if (subscriptionPlan.Id.Contains("pro"))
                                    {
                                        subscriptionPlan.IncludedAgents = 3;
                                        subscriptionPlan.MaximumAPICall = 5000;
                                        subscriptionPlan.MaximumAutomation = 999;
                                        subscriptionPlan.MaximumCampaignSent = 5000;
                                        subscriptionPlan.MaximumNumberOfChannel = 999;
                                        subscriptionPlan.MaximumContact = 2000;
                                        subscriptionPlan.MaximumMessageSent = 5000;
                                    }

                                    _appDbContext.CoreSubscriptionPlans.Add(subscriptionPlan);
                                    await _appDbContext.SaveChangesAsync();
                                }
                            }

                            #endregion

                            if (hasSubscriptionCountryTierAndCurrency == false && subscriptionPlan.Version >= 10)
                            {
                                int numberOfUpdatedCompanies = await _companyService.UpdateCompanySubscriptionTierAndCurrencyAsync(
                                    subscriptionUser.CompanyId,
                                    subscriptionPlan.CountryTier,
                                    subscriptionPlan.Currency
                                );

                                hasSubscriptionCountryTierAndCurrency = numberOfUpdatedCompanies > 0;
                            }

                            var start = invoicePayment.Lines.Data.OrderByDescending(x => x.Period.End).FirstOrDefault()
                                .Period.Start;
                            var end = invoicePayment.Lines.Data.OrderByDescending(x => x.Period.End).FirstOrDefault()
                                .Period.End;

                            DateTime timespan = new DateTime(1970, 1, 1, 0, 0, 0, 0, System.DateTimeKind.Utc);
                            var startDateTime = timespan.AddSeconds(start).ToUniversalTime();

                            // DEVS-3136 Added 3 Hours Buffer in PeriodEnd to avoid draft invoice
                            var endDateTime = timespan.AddSeconds(end).ToUniversalTime().AddHours(3);
                            var isBaseSubscriptionPlan = IsBaseSubscriptionPlan(subscriptionPlan.SubscriptionTier);

                            isBaseSubscriptionPlanIncluded = isBaseSubscriptionPlanIncluded || isBaseSubscriptionPlan;

                            switch (subscriptionPlan.SubscriptionTier)
                            {
                                case SubscriptionTier.Pro:
                                case SubscriptionTier.Premium:
                                case SubscriptionTier.Enterprise:
                                    if (isProratedCancelledItem)
                                    {
                                        //// Prorated item, no usage update required.
                                        break;
                                    }

                                    subscriptionUser.Company.IsFreeTrial = false;

                                    if (subscriptionPlan.IncludedAgents > subscriptionUser.Company.MaximumAgents)
                                    {
                                        subscriptionUser.Company.MaximumAgents = subscriptionPlan.IncludedAgents;
                                    }

                                    if (subscriptionPlan.MaximumAutomation >
                                        subscriptionUser.Company.MaximumAutomations)
                                    {
                                        subscriptionUser.Company.MaximumAutomations =
                                            subscriptionPlan.MaximumAutomation;
                                    }

                                    try
                                    {
                                        var companyId = _configuration.GetValue<String>("Values:SleekFlowCompanyId");

                                        await UpdateSleekFlowUserProfile(
                                            subscriptionInfo,
                                            lastPayment,
                                            subscriptionPlan,
                                            subscriptionUser.Identity.PhoneNumber,
                                            startDateTime);

                                        if (subscriptionPlan.SubscriptionTier == SubscriptionTier.Premium)
                                        {
                                            if (lastPayment?.SubscriptionPlan?.SubscriptionTier ==
                                                SubscriptionTier.Pro &&
                                                !string.IsNullOrEmpty(lastPayment?.stripe_subscriptionId))
                                            {
                                                isBillUpgraded = true;

                                                if (subscriptionPlan.Version >= SubscriptionPlanVersions.Version10)
                                                {
                                                    //// Exclude following Stripe Subscription Id:
                                                    //// - Last Payment's SubscriptionId: Base plan will be cancel at below part of the code, no need to cancel here
                                                    //// - Current InvoicePaid SubscriptionId: Prevent cancel subscription that come with base plan and add-ons together
                                                    var excludedStripeSubscriptionIds = ImmutableHashSet.Create(lastPayment.stripe_subscriptionId, subscriptionInfo.Subscription);
                                                    await CancelAgentPlansOnPlanTierChanged(subscriptionUser.CompanyId, excludedStripeSubscriptionIds);
                                                    await CancelFlowBuilderFlowEnrolmentAddOnsAsync(subscriptionUser.CompanyId, excludedStripeSubscriptionIds);

                                                    isFlowHubLimitChanged = true;
                                                }
                                            }

                                            // HubSpot Plan Upgrade Flow: free / pro -> premium
                                            if (lastPayment == null ||
                                                lastPayment?.SubscriptionPlan?.SubscriptionTier == SubscriptionTier.Pro)
                                            {
                                                BackgroundJob.Enqueue<IInternalHubSpotService>(
                                                    x => x.SetCompanyOwnerUpgradeToPremiumPlanFlag(
                                                        subscriptionUser.CompanyId));
                                                BackgroundJob.Enqueue<ICoreService>(
                                                    x => x.SyncCompanyStaffsToSleekFlowContactsBackground(
                                                        subscriptionUser.CompanyId));
                                            }
                                        }
                                        else if (subscriptionPlan.SubscriptionTier == SubscriptionTier.Pro)
                                        {
                                            if (lastPayment?.SubscriptionPlan?.SubscriptionTier ==
                                                SubscriptionTier.Premium &&
                                                !string.IsNullOrEmpty(lastPayment?.stripe_subscriptionId))
                                            {
                                                subscriptionUser.Company.MaximumAgents =
                                                    subscriptionPlan.IncludedAgents;

                                                isBillDowngraded = true;
                                                if (subscriptionPlan.Version >= SubscriptionPlanVersions.Version10)
                                                {
                                                    //// Exclude following Stripe Subscription Id:
                                                    //// - Last Payment's SubscriptionId: Base plan will be cancel at below part of the code, no need to cancel here
                                                    //// - Current InvoicePaid SubscriptionId: Prevent cancel subscription that come with base plan and add-ons together
                                                    var excludedStripeSubscriptionIds = ImmutableHashSet.Create(lastPayment.stripe_subscriptionId, subscriptionInfo.Subscription);
                                                    await CancelAgentPlansOnPlanTierChanged(subscriptionUser.CompanyId, excludedStripeSubscriptionIds);
                                                    await CancelFlowBuilderFlowEnrolmentAddOnsAsync(subscriptionUser.CompanyId, excludedStripeSubscriptionIds);

                                                    isFlowHubLimitChanged = true;
                                                }
                                            }

                                            // HubSpot Plan Upgrade Flow: free -> pro
                                            if (lastPayment == null)
                                            {
                                                BackgroundJob.Enqueue<IInternalHubSpotService>(
                                                    x => x.SetCompanyOwnerUpgradeToProPlanFlag(
                                                        subscriptionUser.CompanyId));
                                                BackgroundJob.Enqueue<ICoreService>(
                                                    x => x.SyncCompanyStaffsToSleekFlowContactsBackground(companyId));
                                            }
                                        }

                                        if (lastPayment?.SubscriptionPlan?.Version < SubscriptionPlanVersions.Version10 &&
                                            subscriptionPlan.Version == SubscriptionPlanVersions.Version10)
                                        {
                                            //// When company upgrade base plan to v10 from older version.
                                            await CancelLegacyPlans(subscriptionUser.CompanyId);
                                        }

                                        try
                                        {
                                            await HandleSubscriptionPlanUpgradeIncentivesAsync(subscriptionUser.CompanyId, lastPayment, subscriptionInfo, subscriptionPlan, stripeEvent.Created);
                                        }
                                        catch (Exception ex)
                                        {
                                            _logger.LogError(ex, "Error during set CompanyUsageLimitOffsetProfile. Message: {Message}", ex.Message);
                                        }

                                        BackgroundJob.Enqueue<IInternalAnalyticService>(
                                            x => x.UpdateCompanyAllTimeRevenueAnalyticInfo(
                                                new List<string>()
                                                {
                                                    subscriptionUser.CompanyId
                                                }));
                                    }
                                    catch (Exception ex)
                                    {
                                        _logger.LogError(
                                            ex,
                                            "Save plan error {ExceptionMessage}",
                                            ex.Message);
                                    }

                                    if (await _appDbContext.CompanySandboxes
                                            .AnyAsync(x => x.CompanyId == subscriptionUser.CompanyId))
                                    {
                                        BackgroundJob.Enqueue<ICompanyService>(
                                            x => x.DeleteSandbox(subscriptionUser.CompanyId));
                                    }

                                    break;

                                case SubscriptionTier.Agent:
                                    lastPayment = await _appDbContext.CompanyBillRecords
                                        .Where(
                                            x => x.CompanyId == subscriptionUser.CompanyId &&
                                                 ValidSubscriptionPlan.PaidPlan.Contains(x.SubscriptionPlanId))
                                        .OrderByDescending(x => x.Id).Include(x => x.SubscriptionPlan)
                                        .FirstOrDefaultAsync();

                                    var count = 0;
                                    count = lastPayment?.SubscriptionPlan?.IncludedAgents ?? 0;

                                    var billRecords = await _companySubscriptionService.GetSubscribedAddOnBillRecords(
                                        subscriptionUser.CompanyId,
                                        SubscriptionPlanType.Agent);

                                    var groupedBillRecords = billRecords.GroupBy(x => x.stripe_subscriptionId).ToList();
                                    foreach (var groupedBillRecord in groupedBillRecords)
                                    {
                                        var purchased = groupedBillRecord.FirstOrDefault();
                                        count += (int) purchased.quantity;
                                    }

                                    var agentFeatureQuantity = _isEnableTenantHubLogic
                                        ? await _featureQuantityService.GetFeatureQuantityAsync(
                                            subscriptionUser.Company.Id,
                                            "agents")
                                        : count;

                                    if (agentFeatureQuantity > subscriptionUser.Company.MaximumAgents)
                                    {
                                        subscriptionUser.Company.MaximumAgents = agentFeatureQuantity;
                                        await _appDbContext.SaveChangesAsync();
                                    }

                                    break;
                            }

                            #region Creat or Update BillRecord

                            var isUnusedTimeBillRecord = subscriptionInfo.Description.Contains("Unused time") ||
                                                         subscriptionInfo.Description.Contains("未使用") ||
                                                         subscriptionInfo.Amount < 0;

                            var subscriptionDiscount = subscriptionInfo.DiscountAmounts?.Sum(x => x.Amount) ?? 0;
                            var billRecord = await _appDbContext.CompanyBillRecords
                                .Where(x => x.stripeId == subscriptionInfo.Id).FirstOrDefaultAsync();

                            if (billRecord == null)
                            {
                                billRecord = new BillRecord
                                {
                                    stripeId = subscriptionInfo.Id,
                                    invoice_Id = invoicePayment.Id,
                                    SubscriptionPlan = subscriptionPlan,
                                    stripe_subscriptionId = subscriptionInfo.Subscription,
                                    customer_email = invoicePayment.CustomerEmail,
                                    currency = invoicePayment.Currency,
                                    created = DateTime.UtcNow,
                                    CompanyId = subscriptionUser.CompanyId,
                                    amount_due = subscriptionInfo.Amount - subscriptionDiscount,
                                    amount_paid = subscriptionInfo.Amount - subscriptionDiscount,
                                    amount_remaining = invoicePayment.AmountRemaining,
                                    PayAmount = (subscriptionInfo.Amount - subscriptionDiscount) / 100d,
                                    Status = isUnusedTimeBillRecord ? BillStatus.Inactive : BillStatus.Active,
                                    PaymentStatus = PaymentStatus.Paid,
                                    hosted_invoice_url = invoicePayment.HostedInvoiceUrl,
                                    invoice_pdf = invoicePayment.InvoicePdf,
                                    PeriodStart = startDateTime,
                                    PeriodEnd = endDateTime,
                                    customerId = invoicePayment.CustomerId,
                                    chargeId = invoicePayment.ChargeId,
                                    PurchaseStaff = subscriptionUser,
                                    metadata = subscriptionInfo.Metadata,
                                    quantity = subscriptionInfo.Quantity.Value,
                                    UpgradeFromBillRecordId = isBillUpgraded ? lastPayment?.Id : null,
                                    DowngradeFromBillRecordId = isBillDowngraded ? lastPayment?.Id : null,
                                    SubscriptionTier = subscriptionPlan.SubscriptionTier,
                                    IsFreeTrial = subscriptionInfo.Description.Contains("Trial"),
                                    StripeSubscriptionItemId = subscriptionInfo.SubscriptionItem
                                };

                                if (isBaseSubscriptionPlan)
                                {
                                    var usageCycle = _usageCycleCalculator.GetUsageCycle(billRecord.PeriodStart, billRecord.PeriodEnd);
                                    billRecord.UsageCycleStart = usageCycle.From;
                                    billRecord.UsageCycleEnd = usageCycle.To;
                                }

                                try
                                {
                                    await _appDbContext.CompanyBillRecords.AddAsync(billRecord);
                                    await _appDbContext.SaveChangesAsync();

                                    await TerminateBillRecordsOnProratedCancelledInvoiceItemReceive(
                                        subscriptionUser.CompanyId,
                                        isProratedCancelledItem,
                                        subscriptionInfo);

                                    if (ValidSubscriptionPlan.ShopifyIntegrationAddOns.Contains(
                                            billRecord
                                                .SubscriptionPlanId))
                                    {
                                        var shopifyConfig =
                                            await _appDbContext.ConfigShopifyConfigs.FirstOrDefaultAsync(
                                                sc =>
                                                    sc.BillRecord.stripe_subscriptionId ==
                                                    billRecord.stripe_subscriptionId);

                                        if (shopifyConfig != null)
                                        {
                                            shopifyConfig.BillRecord = billRecord;

                                            await _appDbContext.SaveChangesAsync();
                                        }
                                    }
                                }
                                catch (Exception ex)
                                {
                                    _logger.LogError(
                                        ex,
                                        "unable to add bill {CompanyId}",
                                        subscriptionUser.CompanyId);

                                    billRecord = await _appDbContext.CompanyBillRecords
                                        .Where(x => x.invoice_Id == billRecord.invoice_Id).FirstOrDefaultAsync();
                                }

                                BackgroundJob.Enqueue<IInternalHubSpotService>(
                                    x => x.SyncCompany(subscriptionUser.CompanyId, null));
                                BackgroundJob.Enqueue<IInternalAnalyticService>(
                                    x => x.UpdateCompanyAllTimeRevenueAnalyticInfo(
                                        new List<string>()
                                        {
                                            subscriptionUser.CompanyId
                                        }));
                            }
                            else
                            {
                                billRecord.SubscriptionPlan =
                                    await _appDbContext.CoreSubscriptionPlans.FirstOrDefaultAsync(
                                        x => x.Id == subscriptionPlan.Id);
                                billRecord.PeriodStart = startDateTime;
                                billRecord.PeriodEnd = endDateTime;
                                billRecord.stripeId = subscriptionInfo.Id;
                                billRecord.StripeSubscriptionItemId = subscriptionInfo.SubscriptionItem;

                                if (isBaseSubscriptionPlan)
                                {
                                    var usageCycle = _usageCycleCalculator.GetUsageCycle(billRecord.PeriodStart, billRecord.PeriodEnd);
                                    billRecord.UsageCycleStart = usageCycle.From;
                                    billRecord.UsageCycleEnd = usageCycle.To;
                                }

                                await _appDbContext.SaveChangesAsync();

                                await TerminateBillRecordsOnProratedCancelledInvoiceItemReceive(
                                    subscriptionUser.CompanyId,
                                    isProratedCancelledItem,
                                    subscriptionInfo);
                            }

                            #endregion

                            #region Update Feature Usage

                            //// Only update feature usage when the subscription item is not prorated BillRecord.
                            if (!isProratedCancelledItem)
                            {
                                if (subscriptionPlan.PlanTypeCode == PlanTypeCodes.BasePlan)
                                {
                                    await _companyUsageService.ResetApiKeyUsageAsync(subscriptionUser.CompanyId);
                                }
                            }

                            switch (subscriptionPlan.SubscriptionTier)
                            {
                                case SubscriptionTier.Agent:
                                    lastPayment = await _appDbContext.CompanyBillRecords
                                        .OrderByDescending(x => x.Id)
                                        .Include(x => x.SubscriptionPlan)
                                        .FirstOrDefaultAsync(
                                            x =>
                                                x.CompanyId == subscriptionUser.CompanyId
                                                && x.Status != BillStatus.Inactive
                                                && x.Status != BillStatus.Terminated
                                                && ValidSubscriptionPlan.PaidPlan.Contains(x.SubscriptionPlanId));

                                    var count = 0;
                                    count = lastPayment?.SubscriptionPlan?.IncludedAgents ?? 0;

                                    var billRecords = await _companySubscriptionService.GetSubscribedAddOnBillRecords(
                                        subscriptionUser.CompanyId,
                                        SubscriptionPlanType.Agent);

                                    var groupedBillRecords = billRecords.GroupBy(x => x.stripe_subscriptionId).ToList();
                                    foreach (var groupedBillRecord in groupedBillRecords)
                                    {
                                        var purchased = groupedBillRecord.FirstOrDefault();
                                        count += (int) purchased.quantity;
                                    }

                                    var agentFeatureQuantity = _isEnableTenantHubLogic
                                        ? await _featureQuantityService.GetFeatureQuantityAsync(
                                            subscriptionUser.Company.Id,
                                            "agents")
                                        : count;

                                    if (agentFeatureQuantity > subscriptionUser.Company.MaximumAgents)
                                    {
                                        subscriptionUser.Company.MaximumAgents = agentFeatureQuantity;
                                        await _appDbContext.SaveChangesAsync();
                                    }

                                    break;
                            }

                            switch (subscriptionPlan.Id)
                            {
                                case "sleekflow_twilio_100":
                                case "sleekflow_twilio_1000":
                                case "sleekflow_twilio_20":
                                case "sleekflow_twilio_50":
                                case "sleekflow_twilio_500":
                                case "sleekflow_twilio_2000":
                                    var twilioAccountId =
                                        subscriptionInfos.FirstOrDefault().Metadata["twilioAccountId"];

                                    var twilioRecord = await _appDbContext.CompanyTwilioUsageRecords
                                        .Where(x => x.TwilioAccountId == twilioAccountId).FirstOrDefaultAsync();
                                    twilioRecord.TotalCreditValue += (decimal) billRecord.PayAmount;

                                    await _appDbContext.SaveChangesAsync();

                                    break;
                            }

                            switch (subscriptionPlan.SubscriptionTier)
                            {
                                case SubscriptionTier.AddOn:
                                    if (ValidSubscriptionPlan.AdditionalContactAddOns.Contains(subscriptionPlan.Id))
                                    {
                                        lastPayment = await _appDbContext.CompanyBillRecords
                                            .OrderByDescending(x => x.Id)
                                            .Include(x => x.SubscriptionPlan)
                                            .FirstOrDefaultAsync(
                                                x =>
                                                    x.CompanyId == subscriptionUser.CompanyId
                                                    && x.Status != BillStatus.Inactive
                                                    && x.Status != BillStatus.Terminated
                                                    && ValidSubscriptionPlan.PaidPlan.Contains(x.SubscriptionPlanId));

                                        var contact = 0;
                                        contact = lastPayment?.SubscriptionPlan?.MaximumContact ?? 0;

                                        var _puchasedcontact = await _appDbContext.CompanyBillRecords.Where(
                                                x => x.CompanyId == subscriptionUser.CompanyId &&
                                                     x.Status == BillStatus.Active &&
                                                     ValidSubscriptionPlan.AdditionalContactAddOns.Contains(
                                                         x.SubscriptionPlanId) && DateTime.UtcNow < x.PeriodEnd)
                                            .Include(x => x.SubscriptionPlan).ToListAsync();

                                        var purchasedContacts = _puchasedcontact
                                            .GroupBy(x => x.stripe_subscriptionId)
                                            .ToList();

                                        foreach (var purchasedContact in purchasedContacts)
                                        {
                                            var purchased = purchasedContact.FirstOrDefault();
                                            contact += (int) purchased.quantity *
                                                       purchased.SubscriptionPlan.MaximumContact;
                                        }

                                        var contactFeatureQuantity = _isEnableTenantHubLogic
                                            ? await _featureQuantityService.GetFeatureQuantityAsync(
                                                subscriptionUser.CompanyId,
                                                "contacts")
                                            : contact;

                                        if (subscriptionUser.Company.MaximumContacts == null ||
                                            contactFeatureQuantity > subscriptionUser.Company.MaximumContacts)
                                        {
                                            subscriptionUser.Company.MaximumContacts = contactFeatureQuantity;
                                            await _appDbContext.SaveChangesAsync();
                                        }
                                    }
                                    else if (ValidSubscriptionPlan.AdditionalChannelAddOns.Contains(subscriptionPlan.Id))
                                    {
                                        lastPayment = await _appDbContext.CompanyBillRecords
                                            .OrderByDescending(x => x.Id)
                                            .Include(x => x.SubscriptionPlan)
                                            .FirstOrDefaultAsync(
                                                x =>
                                                    x.CompanyId == subscriptionUser.CompanyId
                                                    && x.Status != BillStatus.Inactive
                                                    && x.Status != BillStatus.Terminated
                                                    && ValidSubscriptionPlan.PaidPlan.Contains(x.SubscriptionPlanId));

                                        var channel = 0;
                                        channel = lastPayment?.SubscriptionPlan?.MaximumNumberOfChannel ?? 0;

                                        var purchasedChannelRecords = await _appDbContext.CompanyBillRecords.Where(
                                                x => x.CompanyId == subscriptionUser.CompanyId &&
                                                     x.Status == BillStatus.Active &&
                                                     ValidSubscriptionPlan.AdditionalChannelAddOns.Contains(
                                                         x.SubscriptionPlanId) && DateTime.UtcNow < x.PeriodEnd)
                                            .Include(x => x.SubscriptionPlan).ToListAsync();

                                        var purchasedChannelGroupedByStripePaymentId = purchasedChannelRecords
                                            .GroupBy(x => x.stripe_subscriptionId).ToList();
                                        foreach (var item in purchasedChannelGroupedByStripePaymentId)
                                        {
                                            var purchased = item.FirstOrDefault();
                                            channel += (int) purchased.quantity *
                                                       purchased.SubscriptionPlan.MaximumNumberOfChannel;
                                        }

                                        var channelFeatureQuantity = _isEnableTenantHubLogic
                                            ? await _featureQuantityService.GetFeatureQuantityAsync(
                                                subscriptionUser.CompanyId,
                                                "channels")
                                            : channel;

                                        if (subscriptionUser.Company.MaximumNumberOfChannel == null ||
                                            channelFeatureQuantity > subscriptionUser.Company.MaximumNumberOfChannel)
                                        {
                                            subscriptionUser.Company.MaximumNumberOfChannel = channelFeatureQuantity;
                                            await _appDbContext.SaveChangesAsync();
                                        }
                                    }
                                    // WhatsApp Cloud API Number
                                    else if (ValidSubscriptionPlan.WhatsAppPhoneNumberAddOns.Contains(subscriptionPlan.Id))
                                    {
                                        try
                                        {
                                            lastPayment = await _appDbContext.CompanyBillRecords
                                                .OrderByDescending(x => x.Id)
                                                .Include(x => x.SubscriptionPlan)
                                                .FirstOrDefaultAsync(
                                                    x =>
                                                        x.CompanyId == subscriptionUser.CompanyId
                                                        && x.Status != BillStatus.Inactive
                                                        && x.Status != BillStatus.Terminated
                                                        && ValidSubscriptionPlan.PaidPlan.Contains(x.SubscriptionPlanId));

                                            var count = 0;
                                            var purchasedWhatsApps = (await _appDbContext.CompanyBillRecords
                                                    .Where(
                                                        x =>
                                                            x.CompanyId == subscriptionUser.CompanyId
                                                            && x.Status != BillStatus.Inactive
                                                            && x.Status != BillStatus.Terminated
                                                            && ValidSubscriptionPlan.WhatsAppPhoneNumberAddOns
                                                                .Contains(x.SubscriptionPlanId)
                                                            && DateTime.UtcNow >= x.PeriodStart
                                                            && DateTime.UtcNow < x.PeriodEnd)
                                                    .ToListAsync())
                                                .GroupBy(x => x.stripe_subscriptionId);

                                            foreach (var purchasedWhatsApp in purchasedWhatsApps)
                                            {
                                                var purchased = purchasedWhatsApp.FirstOrDefault();
                                                count += (int) purchased.quantity;
                                            }

                                            var whatsappPhoneNumberFeatureQuantity = _isEnableTenantHubLogic
                                                ? await _featureQuantityService.GetAddOnsFeatureQuantityAsync(
                                                    subscriptionUser.CompanyId,
                                                    "whatsapp_phone_number")
                                                : count;

                                            if (whatsappPhoneNumberFeatureQuantity > subscriptionUser.Company.MaximumWhatsappInstance)
                                            {
                                                subscriptionUser.Company.MaximumWhatsappInstance = whatsappPhoneNumberFeatureQuantity;
                                                await _appDbContext.SaveChangesAsync();
                                            }
                                        }
                                        catch (Exception ex)
                                        {
                                            _logger.LogError(
                                                ex,
                                                "Update MaximumWhatsappInstance error: {ExceptionMessage}",
                                                ex.Message);
                                        }
                                    }
                                    else if (subscriptionPlan.PlanTypeCode == PlanTypeCodes.FlowBuilderFlowEnrolments)
                                    {
                                        var excludedSubscriptionIds = ImmutableList.Create(subscriptionInfo.Subscription);
                                        await CancelFlowBuilderFlowEnrolmentAddOnsAsync(subscriptionUser.CompanyId, excludedSubscriptionIds);

                                        isFlowHubLimitChanged = true;
                                    }

                                    break;
                            }

                            #endregion

                            #region Cancel Subscription Plans

                            bool isUpdated = false;
                            try
                            {
                                if (!isUnusedTimeBillRecord && ValidSubscriptionPlan.PaidPlan.Contains(subscriptionPlan.Id))
                                {
                                    var lastPayments = await _appDbContext.CompanyBillRecords.Where(
                                            x =>
                                                x.CompanyId == subscriptionUser.CompanyId &&
                                                x.PeriodStart <= DateTime.UtcNow && DateTime.UtcNow <= x.PeriodEnd &&
                                                x.Status == BillStatus.Active &&
                                                !string.IsNullOrEmpty(x.stripe_subscriptionId) &&
                                                x.stripe_subscriptionId != subscriptionInfo.Subscription &&
                                                (ValidSubscriptionPlan.PaidPlan.Contains(x.SubscriptionPlanId) ||
                                                 x.SubscriptionTier == SubscriptionTier.Pro &&
                                                 x.SubscriptionTier == SubscriptionTier.Premium))
                                        .OrderByDescending(x => x.Id)
                                        .ToListAsync();
                                    foreach (var _lastPayment in lastPayments)
                                    {
                                        if (!ValidSubscriptionPlan.AgentPlan.Contains(_lastPayment.SubscriptionPlanId)
                                            && _lastPayment.customerId == invoicePayment.CustomerId)
                                        {
                                            if (!string.IsNullOrEmpty(_lastPayment.stripe_subscriptionId))
                                            {
                                                List<string> cancellingPlanTypeCodes = [PlanTypeCodes.BasePlan];

                                                if (isBillUpgraded || isBillDowngraded)
                                                {
                                                    cancellingPlanTypeCodes.Add(PlanTypeCodes.Agents);
                                                    cancellingPlanTypeCodes.Add(PlanTypeCodes.FlowBuilderFlowEnrolments);
                                                }

                                                var stripeSubscriptionBillRecords = await _companySubscriptionService.GetActiveBillRecordsAsync(
                                                    subscriptionUser.CompanyId,
                                                    x => x.stripe_subscriptionId == _lastPayment.stripe_subscriptionId);

                                                var stripeSubscriptionBillRecordPlanIds = stripeSubscriptionBillRecords.Select(x => x.SubscriptionPlanId).ToImmutableHashSet();

                                                var subscriptionPlans = await _subscriptionPlanService.GetSubscriptionPlansAsync(stripeSubscriptionBillRecordPlanIds);

                                                _logger.LogInformation(
                                                    "Handle Subscription Cancellation for StripeSubscriptionId: {StripeSubscriptionId}, CancelPlanTypes: {CancelPlanTypes}",
                                                    _lastPayment.stripe_subscriptionId,
                                                    string.Join(",", cancellingPlanTypeCodes));

                                                if (subscriptionPlans.All(x => cancellingPlanTypeCodes.Contains(x.PlanTypeCode))
                                                    || stripeSubscriptionBillRecords.Any(x => string.IsNullOrWhiteSpace(x.StripeSubscriptionItemId)))
                                                {
                                                    _logger.LogInformation(
                                                        "All SubscriptionItems need to be cancelled, cancelling entire subscription. StripeSubscriptionId: {StripeSubscriptionId}",
                                                        _lastPayment.stripe_subscriptionId);

                                                    await CancelStripeSubscriptionAsync(
                                                        subscriptionUser.CompanyId,
                                                        invoicePayment,
                                                        _lastPayment,
                                                        subscriptionPlan,
                                                        json);
                                                }
                                                else
                                                {
                                                    //// Group BillRecord by StripeSubscriptionItemId to prevent cancel same item twice as we added extra 3 hours to PeriodEnd.
                                                    var subscriptionItemGroupedBillRecords = stripeSubscriptionBillRecords.GroupBy(x => x.StripeSubscriptionItemId).Select(x => x.First());

                                                    foreach (var subscriptionItemBillRecord in subscriptionItemGroupedBillRecords)
                                                    {
                                                        var billRecordPlan = subscriptionPlans.FirstOrDefault(x => x.Id == subscriptionItemBillRecord.SubscriptionPlanId);

                                                        if (billRecordPlan == null || !cancellingPlanTypeCodes.Contains(billRecordPlan.PlanTypeCode))
                                                        {
                                                            _logger.LogInformation(
                                                                "SubscriptionPlan not found or not in the Cancelling Plan Type Codes. " +
                                                                "StripeSubscriptionId: {StripeSubscriptionId}, " +
                                                                "SubscriptionPlanId: {SubscriptionPlanId}, " +
                                                                "BillRecordId: {BillRecordId}, " +
                                                                "PlanTypeCode: {PlanTypeCode}",
                                                                subscriptionItemBillRecord.stripe_subscriptionId,
                                                                subscriptionItemBillRecord.SubscriptionPlanId,
                                                                subscriptionItemBillRecord.Id,
                                                                billRecordPlan?.PlanTypeCode);

                                                            continue;
                                                        }

                                                        try
                                                        {
                                                            _logger.LogInformation(
                                                                "Deleting SubscriptionItem. " +
                                                                "StripeSubscriptionId: {StripeSubscriptionId}, " +
                                                                "StripeSubscriptionItemId: {StripeSubscriptionItemId}," +
                                                                "BillRecordId: {BillRecordId}," +
                                                                "SubscriptionPlanId: {SubscriptionPlanId}",
                                                                subscriptionItemBillRecord.stripe_subscriptionId,
                                                                subscriptionItemBillRecord.StripeSubscriptionItemId,
                                                                subscriptionItemBillRecord.Id,
                                                                subscriptionItemBillRecord.SubscriptionPlanId);

                                                            await _stripeSubscriptionService.DeleteSubscriptionItemAsync(
                                                                subscriptionItemBillRecord.stripe_subscriptionId,
                                                                subscriptionItemBillRecord.StripeSubscriptionItemId);

                                                            SendSubscriptionCancelledAlert(
                                                                subscriptionUser.CompanyId,
                                                                $"[Stripe] StripeSubscriptionItem: {subscriptionItemBillRecord.StripeSubscriptionItemId} Cancelled from StripeSubscription: {subscriptionItemBillRecord.stripe_subscriptionId}",
                                                                invoicePayment,
                                                                subscriptionPlan,
                                                                _lastPayment,
                                                                json);
                                                        }
                                                        catch (Exception ex)
                                                        {
                                                            _logger.LogError(
                                                                ex,
                                                                "Failed to Delete SubscriptionItem. " +
                                                                "StripeSubscriptionId: {StripeSubscriptionId}, " +
                                                                "StripeSubscriptionItemId: {StripeSubscriptionItemId}, " +
                                                                "BillRecordId: {BillRecordId}," +
                                                                "SubscriptionPlanId: {SubscriptionPlanId}, " +
                                                                "ExceptionMessage: {ExceptionMessage}",
                                                                subscriptionItemBillRecord.stripe_subscriptionId,
                                                                subscriptionItemBillRecord.StripeSubscriptionItemId,
                                                                subscriptionItemBillRecord.Id,
                                                                subscriptionItemBillRecord.SubscriptionPlanId,
                                                                ex.Message);
                                                        }
                                                    }
                                                }
                                            }

                                            isUpdated = true;
                                        }
                                    }
                                }
                            }
                            catch (Exception ex)
                            {
                                _logger.LogError(
                                    ex,
                                    "Send Invoice error: {Subscription}. {ExceptionMessage}",
                                    subscriptionInfo.Subscription,
                                    ex.Message);
                            }

                            #endregion

                            try
                            {
                                if (ValidSubscriptionPlan.PaidPlan.Contains(subscriptionPlan.Id))
                                {
                                    if (isUpdated)
                                    {
                                    }
                                    else
                                    {
                                        lastPayment = await _appDbContext.CompanyBillRecords
                                            .OrderByDescending(x => x.Id)
                                            .FirstOrDefaultAsync(
                                                x =>
                                                    x.CompanyId == subscriptionUser.CompanyId
                                                    && x.Status != BillStatus.Inactive
                                                    && x.Status != BillStatus.Terminated
                                                    && ValidSubscriptionPlan.SubscriptionPlan.Contains(x.SubscriptionPlanId));

                                        if (ValidSubscriptionPlan.FreeTier.Contains(lastPayment?.SubscriptionPlanId))
                                        {
                                            BackgroundJob.Enqueue<IEmailNotificationService>(
                                                x => x.SendInvoiceToCustomer(
                                                    subscriptionUser.IdentityId,
                                                    billRecord.Id,
                                                    NotificationType.SendInvoiceToCustomer));
                                        }
                                    }
                                    // break;
                                    // }
                                }
                            }
                            catch (Exception ex)
                            {
                                _logger.LogError(
                                    ex,
                                    "Send Invoice error for Stripe subscription id {StripeSubscriptionId}. {ExceptionMessage}",
                                    billRecord.stripe_subscriptionId,
                                    ex.Message);
                            }

                            // check promotion code
                            try
                            {
                                var pendingPromotionCode = await _appDbContext.CoreRedeemPromotionRecords
                                    .Where(
                                        x => x.CompanyId == subscriptionUser.CompanyId &&
                                             x.Status == TopupStatus.Pending).Include(x => x.Promotion)
                                    .FirstOrDefaultAsync();
                                if (pendingPromotionCode != null)
                                {
                                    lastPayment = await _appDbContext.CompanyBillRecords
                                        .Where(
                                            x => x.CompanyId == subscriptionUser.CompanyId &&
                                                 x.Status == BillStatus.Active &&
                                                 ValidSubscriptionPlan.PaidPlan.Contains(x.SubscriptionPlanId))
                                        .Include(x => x.SubscriptionPlan).OrderByDescending(x => x.Id)
                                        .FirstOrDefaultAsync();

                                    if (lastPayment.SubscriptionPlan.IncludedAgents >
                                        subscriptionUser.Company.MaximumAgents)
                                    {
                                        subscriptionUser.Company.MaximumAgents =
                                            lastPayment.SubscriptionPlan.IncludedAgents;
                                    }

                                    if (!subscriptionUser.Company.MaximumWhAutomatedMessages.HasValue)
                                    {
                                        subscriptionUser.Company.MaximumWhAutomatedMessages =
                                            lastPayment.SubscriptionPlan.MaximumCampaignSent;
                                    }

                                    if (!subscriptionUser.Company.MaximumContacts.HasValue)
                                    {
                                        subscriptionUser.Company.MaximumContacts =
                                            lastPayment.SubscriptionPlan.MaximumContact;
                                    }

                                    subscriptionUser.Company.MaximumWhAutomatedMessages +=
                                        (lastPayment.SubscriptionPlanId.Contains("yearly"))
                                            ? pendingPromotionCode.Promotion.ExtraAutomatedMessages * 12
                                            : pendingPromotionCode.Promotion.ExtraAutomatedMessages;
                                    subscriptionUser.Company.MaximumContacts +=
                                        pendingPromotionCode.Promotion.ExtraContacts;
                                    subscriptionUser.Company.MaximumAgents +=
                                        pendingPromotionCode.Promotion.ExtraAgents;

                                    pendingPromotionCode.Status = TopupStatus.Redeemed;
                                    pendingPromotionCode.RedeemedAt = DateTime.UtcNow;
                                    await _appDbContext.SaveChangesAsync();

                                    var referralCode = await _appDbContext.CorePromotions.Where(
                                        x => x.Id == pendingPromotionCode.PromotionId &&
                                             !string.IsNullOrEmpty(x.CompanyId)).FirstOrDefaultAsync();
                                    if (referralCode != null)
                                    {
                                        if (await _appDbContext.CoreRedeemPromotionRecords
                                                .CountAsync(
                                                    x =>
                                                        x.PromotionId == referralCode.Id
                                                        && x.ReferrerStatus == TopupStatus.Redeemed) >= 5)
                                        {
                                            return Ok();
                                        }

                                        var referralPromotionCode = new RedeemPromotionRecord
                                        {
                                            CompanyId = referralCode.CompanyId, PromotionId = referralCode.Id,
                                        };

                                        _appDbContext.CoreRedeemPromotionRecords.Add(pendingPromotionCode);
                                        await _appDbContext.SaveChangesAsync();

                                        if (referralCode != null)
                                        {
                                            var referrer = await _appDbContext.CompanyCompanies
                                                .Where(x => x.Id == referralCode.CompanyId).FirstOrDefaultAsync();

                                            var referlastPayment = await _appDbContext.CompanyBillRecords
                                                .Where(
                                                    x => x.CompanyId == referrer.Id && x.Status == BillStatus.Active &&
                                                         ValidSubscriptionPlan.PaidPlan.Contains(x.SubscriptionPlanId))
                                                .Include(x => x.SubscriptionPlan).OrderByDescending(x => x.Id)
                                                .FirstOrDefaultAsync();

                                            if (referlastPayment.SubscriptionPlan.IncludedAgents >
                                                referrer.MaximumAgents)
                                            {
                                                referrer.MaximumAgents =
                                                    referlastPayment.SubscriptionPlan.IncludedAgents;
                                            }

                                            if (!referrer.MaximumWhAutomatedMessages.HasValue)
                                            {
                                                referrer.MaximumWhAutomatedMessages = referlastPayment.SubscriptionPlan
                                                    .MaximumCampaignSent;
                                            }

                                            if (!referrer.MaximumContacts.HasValue)
                                            {
                                                referrer.MaximumContacts =
                                                    referlastPayment.SubscriptionPlan.MaximumContact;
                                            }

                                            referrer.MaximumWhAutomatedMessages +=
                                                (referlastPayment.SubscriptionPlanId.Contains("yearly"))
                                                    ? referralCode.ExtraAutomatedMessages * 12
                                                    : referralCode.ExtraAutomatedMessages;
                                            referrer.MaximumContacts += referralCode.ExtraContacts;
                                            referrer.MaximumAgents += referralCode.ExtraAgents;

                                            referralPromotionCode.ReferrerStatus = TopupStatus.Redeemed;
                                            referralPromotionCode.ReferrerRedeemedAt = DateTime.UtcNow;
                                            await _appDbContext.SaveChangesAsync();
                                        }
                                    }
                                }
                            }
                            catch (Exception ex)
                            {
                                _logger.LogError(
                                    ex,
                                    "Redeem Error for Stripe subscription id {StripeSubscriptionId}. {ExceptionMessage}",
                                    billRecord.stripe_subscriptionId,
                                    ex.Message);
                            }

                            try
                            {
                                if (subscriptionInfo.Metadata["isShopifyRenewal"] != null &&
                                    subscriptionInfo.Metadata["isShopifyRenewal"] == "true" &&
                                    subscriptionInfo.Metadata["companyId"] != null &&
                                    subscriptionInfo.Metadata["shopifyConfigId"] != null)
                                {
                                    var shopifyConfig = await _appDbContext.ConfigShopifyConfigs
                                        .Include(sc => sc.BillRecord).FirstOrDefaultAsync(
                                            sc =>
                                                sc.CompanyId == subscriptionInfo.Metadata["companyId"] &&
                                                sc.Id == long.Parse(subscriptionInfo.Metadata["shopifyConfigId"]) &&
                                                sc.BillRecord.stripe_subscriptionId !=
                                                billRecord.stripe_subscriptionId);

                                    if (shopifyConfig != null &&
                                        (shopifyConfig.BillRecord == null ||
                                         shopifyConfig.BillRecord.PeriodEnd < billRecord.PeriodEnd))
                                    {
                                        shopifyConfig.BillRecord = billRecord;

                                        await _appDbContext.SaveChangesAsync();
                                    }
                                }
                            }
                            catch (Exception ex)
                            {
                                _logger.LogError(
                                    ex,
                                    "Update Shopify config error for Stripe subscription id {StripeSubscriptionId}. {ExceptionMessage}",
                                    billRecord.stripe_subscriptionId,
                                    ex.Message);
                            }
                        }

                        if (isBaseSubscriptionPlanIncluded || isFlowHubLimitChanged)
                        {
                            BackgroundJob.Enqueue<IFlowHubService>(
                                x => x.UpdateFlowHubConfigAsync(subscriptionUser.CompanyId, subscriptionUser));
                        }

                        // only fresh outer hub configs when the payment include base subscription plans
                        if (isBaseSubscriptionPlanIncluded)
                        {
                            BackgroundJob.Enqueue<IIntelligentHubService>(
                                x => x.RefreshIntelligentHubConfigAsync(subscriptionUser.CompanyId));
                            BackgroundJob.Enqueue<ICrmHubService>(
                                x => x.RefreshCrmHubConfigAsync(subscriptionUser.CompanyId, subscriptionUser));
                        }

                        await _companyInfoCacheService.RemoveCompanyInfoCache(subscriptionUser.CompanyId);

                        break;

                    case Events.CustomerCreated:
                        var customer = stripeEvent.Data.Object as Customer;

                        if (!string.IsNullOrEmpty(customer?.Address?.Country))
                        {
                            var invoiceFooter =
                                await GetRegionalInvoiceFooterByCountryIsoCode(customer.Address.Country);

                            if (!string.IsNullOrEmpty(invoiceFooter))
                            {
                                var customerService = new CustomerService();

                                await customerService.UpdateAsync(
                                    customer.Id,
                                    new CustomerUpdateOptions
                                    {
                                        InvoiceSettings = new CustomerInvoiceSettingsOptions
                                        {
                                            Footer = invoiceFooter
                                        }
                                    });
                            }
                        }

                        break;
                }
            }
            catch (StripeException ex)
            {
                _logger.LogError(
                    ex,
                    "POST stripe/webhook Stripe exception. {ExceptionString}",
                    ex.ToString());

                return BadRequest();
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "POST stripe/webhook exception. Payload: {Payload}. Exception: {ExceptionString}",
                    json,
                    ex.ToString());

                return BadRequest();
            }

            return Ok();
        }

        private async Task<string> GetRegionalInvoiceFooterByCountryIsoCode(
            string countryIsoCode)
        {
            var regionalInfo = await _appDbContext.CompanyRegionalInfos.FirstOrDefaultAsync(
                i =>
                    i.IsSleekFlow &&
                    i.CountryIsoCode == countryIsoCode);
            if (regionalInfo == null)
            {
                return null;
            }

            return "Bill from \n" +
                   $"Company: {regionalInfo.RegisteredCompanyName} \n" +
                   $"Company Registered Number: {regionalInfo.RegisteredBusinessNumber} \n" +
                   $"Company Address: {regionalInfo.CompanyAddress}";
        }

        private async Task<string> GetRegionalInvoiceFooterBySubscriptionCurrency(
            string subscriptionCurrency)
        {
            var countryIsoCode = GetCountryIsoCodeByCurrencyIsoCode(subscriptionCurrency);
            if (string.IsNullOrEmpty(countryIsoCode))
            {
                return null;
            }

            return await GetRegionalInvoiceFooterByCountryIsoCode(countryIsoCode);
        }

        private static string GetCountryIsoCodeByCurrencyIsoCode(
            string currencyIsoCode) => currencyIsoCode.ToUpper() switch
        {
            "BRL" => "BR",
            "GBP" => "GB",
            "HKD" => "HK",
            "IDR" => "ID",
            "MYR" => "MY",
            "SGD" => "SG",
            _ => null
        };

        private bool IsBaseSubscriptionPlan(SubscriptionTier tier)
        {
            var whiteList = ImmutableList.Create(SubscriptionTier.Pro, SubscriptionTier.Premium, SubscriptionTier.Enterprise);
            return whiteList.Contains(tier);
        }

        private async Task CancelAgentPlansOnPlanTierChanged(string companyId, IEnumerable<string> excludeStripeSubscriptionId)
        {
            //// Exclude agent plans that came from previous base subscription plan as base subscription will be cancel later along with it's add-ons
            var agentPlanBillRecords = (await _companySubscriptionService.GetSubscribedAddOnBillRecords(companyId, SubscriptionPlanType.Agent))
                .Where(x => !excludeStripeSubscriptionId.Contains(x.stripe_subscriptionId))
                .ToList();

            _logger.LogInformation("Cancelling Agent Plans. CompanyId: {CompanyId}, BillRecordIds: {BillRecordIds}", companyId, string.Join(",", agentPlanBillRecords.Select(x => x.Id)));

            await CancelBillRecordSubscription(agentPlanBillRecords);
        }

        private async Task CancelFlowBuilderFlowEnrolmentAddOnsAsync(string companyId, IEnumerable<string> excludedStripeSubscriptionId)
        {
            Expression<Func<BillRecord, bool>> predicate = (x) =>
                SubscriptionPlansId.FlowBuilderFlowEnrolmentsAddOns.Contains(x.SubscriptionPlanId) &&
                !excludedStripeSubscriptionId.Contains(x.stripe_subscriptionId);

            var billRecords = await _companySubscriptionService.GetActiveBillRecordsAsync(companyId, predicate);

            _logger.LogInformation("Cancelling Flow Enrolments Plans. CompanyId: {CompanyId}, BillRecordIds: {BillRecordIds}", companyId, string.Join(",", billRecords.Select(x => x.Id)));

            await CancelBillRecordSubscription(billRecords);
        }

        private async Task CancelLegacyPlans(string companyId)
        {
            Expression<Func<BillRecord, bool>> predicate = (x) => SubscriptionPlansId.LegacyAddOnsForV10BasePlan.Contains(x.SubscriptionPlanId);

            var legacyAddOnBillRecords = await _companySubscriptionService.GetActiveBillRecordsAsync(companyId, predicate);

            _logger.LogInformation("Cancelling Add-Ons. CompanyId: {CompanyId}, BillRecordIds: {BillRecordIds}", companyId, string.Join(",", legacyAddOnBillRecords.Select(x => x.Id)));

            await CancelBillRecordSubscription(legacyAddOnBillRecords);
        }

        private async Task CancelBillRecordSubscription(IEnumerable<BillRecord> billRecords)
        {
            var subscriptionIds = billRecords.Select(x => x.stripe_subscriptionId).ToHashSet();

            foreach (var subscriptionId in subscriptionIds)
            {
                if (string.IsNullOrWhiteSpace(subscriptionId))
                {
                    continue;
                }

                var cancellingBillRecords = billRecords.Where(x => x.stripe_subscriptionId == subscriptionId);

                _logger.LogInformation(
                    "Cancelling Stripe Subscription. StripeSubscriptionId: {StripeSubscriptionId}, BillRecordId: {BillRecordId}, SubscriptionPlanId: {SubscriptionPlanId}",
                    subscriptionId,
                    string.Join(",", cancellingBillRecords.Select(x => x.Id)),
                    string.Join(",", cancellingBillRecords.Select(x => x.SubscriptionPlanId)));

                var service = new SubscriptionService();
                var cancelOptions = new SubscriptionCancelOptions
                {
                    InvoiceNow = false, Prorate = true
                };

                await service.CancelAsync(subscriptionId, cancelOptions);
            }
        }

        #region Stripe Event Handlers

        private async Task<IActionResult> HandleStripeCustomerSubscriptionUpdatedEvent(Event stripeEvent)
        {
            var updatedSubscription = stripeEvent.Data.Object as Subscription;
            var jobjPreviousSubscriptionAttrs = stripeEvent.Data.PreviousAttributes as JObject;
            var previousSubscriptionAttrs = jobjPreviousSubscriptionAttrs?.ToObject<Subscription>();

            if (updatedSubscription != null && updatedSubscription.Metadata.ContainsKey("source"))
            {
                _logger.LogInformation(
                    "The source of the event:{StripeEventId} is not from Travis_backend, will be ignored",
                    stripeEvent.Id);

                return Ok();
            }

            //// Ideally should determine if a subscription plan is requested to cancel by 'reason' in 'cancellation_details' in Subscription object.
            //// But due to the current installed Stripe SDK version, we're not able to access the property at the moment.
            //// Recommend to update this once Stripe SDK updated.
            if (previousSubscriptionAttrs?.CancelAt.HasValue == false && updatedSubscription?.CancelAt.HasValue == true)
            {
                var subscriptionPlanIds = updatedSubscription.Items.Select(x => x.Price.LookupKey);

                foreach (var subscriptionPlanId in subscriptionPlanIds)
                {
                    await _companySubscriptionService.SetBillRecordCancellationDateTime(updatedSubscription.Id, subscriptionPlanId, updatedSubscription.CancelAt.Value);
                }
            }

            if (updatedSubscription.Status == "trialing")
            {
                var updatedSubscriptionCompanyId = updatedSubscription.Metadata["companyId"];

                var subscriptionBillRecord = await _appDbContext.CompanyBillRecords
                    .Include(b => b.SubscriptionPlan).FirstOrDefaultAsync(
                        b =>
                            b.CompanyId == updatedSubscriptionCompanyId &&
                            b.stripe_subscriptionId == updatedSubscription.Id &&
                            b.IsFreeTrial &&
                            b.Status == BillStatus.Active);

                if (subscriptionBillRecord != null &&
                    subscriptionBillRecord.SubscriptionPlan.SubscriptionTier == SubscriptionTier.Agent)
                {
                    var company =
                        await _appDbContext.CompanyCompanies.FirstOrDefaultAsync(
                            c =>
                                c.Id == updatedSubscriptionCompanyId);

                    if (company.MaximumAgents - Convert.ToInt32(subscriptionBillRecord.quantity) > 0)
                    {
                        company.MaximumAgents -= Convert.ToInt32(subscriptionBillRecord.quantity);
                    }

                    await _appDbContext.CompanyBillRecords
                        .Where(x => x.stripe_subscriptionId == updatedSubscription.Id)
                        .ForEachAsync(x => x.Status = BillStatus.Canceled);
                    await _companyInfoCacheService.RemoveCompanyInfoCache(updatedSubscriptionCompanyId);
                    await _appDbContext.SaveChangesAsync();
                }
            }

            return Ok();
        }

        private async Task<IActionResult> HandleStripeCustomerSubscriptionDeletedEvent(Event stripeEvent)
        {
            var deletedStripeSubscription = stripeEvent.Data.Object as Subscription;
            // Is the event from other hubs instead of travis_backend
            if (deletedStripeSubscription != null && deletedStripeSubscription.Metadata.ContainsKey("source"))
            {
                _logger.LogInformation($"The source of the event:{stripeEvent.Id} is not from Travis_backend, will be ignored");
                return Ok();
            }

            var bill = await _appDbContext.CompanyBillRecords.FirstOrDefaultAsync(x => x.stripe_subscriptionId == deletedStripeSubscription.Id && x.Status != BillStatus.Canceled && x.Status != BillStatus.Terminated);

            if (bill == null)
            {
                return Ok();
            }

            var delete_subscriptionUser = await _appDbContext.UserRoleStaffs
                .Include(x => x.Company.BillRecords)
                .Include(x => x.Identity)
                .FirstOrDefaultAsync(x => x.CompanyId == bill.CompanyId);

            if (delete_subscriptionUser == null)
            {
                try
                {
                    var companyId = deletedStripeSubscription.Items.FirstOrDefault().Metadata["companyId"];
                    await _companyInfoCacheService.RemoveCompanyInfoCache(companyId);
                    delete_subscriptionUser = await _appDbContext.UserRoleStaffs
                        .Where(x => x.CompanyId == companyId).Include(x => x.Company.BillRecords)
                        .Include(x => x.Identity).FirstOrDefaultAsync();
                }
                catch (Exception ex)
                {
                    _logger.LogError(
                        ex,
                        "POST stripe/webhook error when processing event type {EventType}. {ExceptionMessage}",
                        stripeEvent.Type,
                        ex.Message);
                }
            }

            foreach (var subscriptionInfo in deletedStripeSubscription.Items)
            {
                var subscriptionPlan = await _appDbContext.CoreSubscriptionPlans.FirstOrDefaultAsync(x => x.StripePlanId == subscriptionInfo.Plan.Id);

                switch (subscriptionPlan.SubscriptionTier)
                {
                    case SubscriptionTier.Agent:
                        if (delete_subscriptionUser.Company.MaximumAgents -
                            Convert.ToInt32(subscriptionInfo.Quantity) > 0)
                        {
                            delete_subscriptionUser.Company.MaximumAgents -=
                                Convert.ToInt32(subscriptionInfo.Quantity);
                        }

                        break;
                    case SubscriptionTier.Premium:
                    case SubscriptionTier.Pro:
                    case SubscriptionTier.Enterprise:
                        try
                        {
                            var currentSubscriptionPlan = await _appDbContext.CompanyBillRecords
                                .Where(
                                    x => x.CompanyId == delete_subscriptionUser.CompanyId &&
                                         ValidSubscriptionPlan.SubscriptionPlan.Contains(
                                             x.SubscriptionPlanId)).OrderByDescending(x => x.Id)
                                .Include(x => x.SubscriptionPlan).FirstOrDefaultAsync();

                            delete_subscriptionUser.Company.MaximumContacts = null;

                            if (currentSubscriptionPlan.SubscriptionPlan.Version == 9)
                            {
                                delete_subscriptionUser.Company.MaximumNumberOfChannel =
                                    currentSubscriptionPlan.SubscriptionPlan.MaximumNumberOfChannel;

                                if (subscriptionPlan.Version == 9)
                                {
                                    var purchasedChannelPlans = await _appDbContext.CompanyBillRecords
                                        .Where(
                                            x => x.CompanyId == delete_subscriptionUser.CompanyId &&
                                                 x.Status == BillStatus.Active &&
                                                 ValidSubscriptionPlan.AdditionalChannelAddOns.Contains(
                                                     x.SubscriptionPlanId) && DateTime.UtcNow < x.PeriodEnd)
                                        .Include(x => x.SubscriptionPlan).ToListAsync();
                                    var purchasedChannelPlansGrouped = purchasedChannelPlans
                                        .GroupBy(x => x.stripe_subscriptionId).ToList();
                                    var channelsFromAddons = 0;
                                    foreach (var purchasedChannelPlanGrouping in
                                             purchasedChannelPlansGrouped)
                                    {
                                        var purchasedChannelPlan =
                                            purchasedChannelPlanGrouping.FirstOrDefault();
                                        channelsFromAddons += (int) purchasedChannelPlan.quantity *
                                                              purchasedChannelPlan.SubscriptionPlan
                                                                  .MaximumNumberOfChannel;
                                    }

                                    var channelAddOnFeatureQuantity = _isEnableTenantHubLogic
                                        ? await _featureQuantityService.GetAddOnsFeatureQuantityAsync(
                                            delete_subscriptionUser.CompanyId,
                                            "channels")
                                        : channelsFromAddons;

                                    delete_subscriptionUser.Company.MaximumNumberOfChannel += channelAddOnFeatureQuantity;
                                }
                            }
                            else
                            {
                                delete_subscriptionUser.Company.MaximumNumberOfChannel = null;
                            }

                            delete_subscriptionUser.Company.MaximumAutomations = null;
                            delete_subscriptionUser.Company.MaximumWhAutomatedMessages = null;

                            var userProfile = await _userProfileService.GetUserProfilesByFields(
                                bill.chargeId,
                                new List<Condition>
                                {
                                    new Condition
                                    {
                                        FieldName = "PhoneNumber",
                                        ConditionOperator = SupportedOperator.Equals,
                                        Values = new List<string>
                                        {
                                            delete_subscriptionUser.Identity.PhoneNumber
                                        }
                                    }
                                },
                                0,
                                1);
                            BackgroundJob.Enqueue<IUserProfileService>(
                                x => x.SetFieldValueByFieldNameSafe(
                                    userProfile.UserProfiles.FirstOrDefault().Id,
                                    "LeadStage",
                                    "Cancelled",
                                    true));
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(
                                ex,
                                "POST stripe/webhook error when processing deletetion of subscription plan tier {SubscriptionPlanTier}. {ExceptionMessage}",
                                subscriptionPlan.SubscriptionTier,
                                ex.Message);
                        }

                        break;
                    case SubscriptionTier.AddOn:
                        if (ValidSubscriptionPlan.WhatsAppPhoneNumberAddOns.Contains(subscriptionPlan.Id))
                        {
                            if (delete_subscriptionUser.Company.MaximumWhatsappInstance > 0)
                            {
                                delete_subscriptionUser.Company.MaximumWhatsappInstance -=
                                    (int) subscriptionInfo.Quantity;
                            }
                        }
                        else if (ValidSubscriptionPlan.AdditionalContactAddOns.Contains(subscriptionPlan.Id))
                        {
                            var companyMaximumContact = delete_subscriptionUser.Company.MaximumContacts;
                            var totalDeduction = subscriptionInfo.Quantity * subscriptionPlan.MaximumContact;
                            var deductedMaximumContact = companyMaximumContact - totalDeduction;

                            _logger.LogInformation(
                                "Deducting Contact Limit. CompanyMaximumContact: {CompanyMaximumContact}, TotalDeduction: {TotalDeduction}, DeductedContactLimit: {DeductedContactLimit}",
                                companyMaximumContact,
                                totalDeduction,
                                deductedMaximumContact);

                            if (deductedMaximumContact > 0)
                            {
                                delete_subscriptionUser.Company.MaximumContacts = Convert.ToInt32(deductedMaximumContact);
                            }
                        }

                        break;
                }
            }

            #region Cancel / Terminate BillRecords

            var subscriptionPlanIds = deletedStripeSubscription.Items.Data.Select(x => x.Plan.Id);

            bool isTerminateBillRecordEnabled = _configuration.GetValue<bool>("FeatureFlags:CancelledSubscriptionTermination:IsEnabled");

            _logger.LogInformation("Is Terminate BillRecords Enabled: {IsTerminateBillRecordEnabled}", isTerminateBillRecordEnabled);

            bool isCancelWithProrate = false;

            if (isTerminateBillRecordEnabled)
            {
                isCancelWithProrate = await _stripeSubscriptionService.IsSubscriptionCancelledWithProrate(deletedStripeSubscription.CustomerId, deletedStripeSubscription.Id, subscriptionPlanIds);
                _logger.LogInformation("Stripe Subscription {SubscriptionId} is Cancelled with Prorate: {IsCancelWithProrate}", deletedStripeSubscription.Id, isCancelWithProrate);
            }

            if (isCancelWithProrate == true)
            {
                await _companySubscriptionService.TerminateBillRecords(delete_subscriptionUser.CompanyId, deletedStripeSubscription.Id);
            }
            else
            {
                await _appDbContext.CompanyBillRecords.Where(x => x.stripe_subscriptionId == deletedStripeSubscription.Id)
                    .ForEachAsync(x => x.Status = BillStatus.Canceled);
            }

            #endregion

            var requiredFlowHubUsageUpdate = false;

            foreach (var subscriptionItem in deletedStripeSubscription.Items)
            {
                var subscriptionPlan = await _subscriptionPlanService.GetSubscriptionPlanByStripePlanIdAsync(subscriptionItem.Plan.Id);

                requiredFlowHubUsageUpdate = requiredFlowHubUsageUpdate || subscriptionPlan.PlanTypeCode is PlanTypeCodes.BasePlan or PlanTypeCodes.FlowBuilderFlowEnrolments;
            }

            if (requiredFlowHubUsageUpdate)
            {
                BackgroundJob.Enqueue<IFlowHubService>(x => x.UpdateFlowHubConfigAsync(delete_subscriptionUser.CompanyId, delete_subscriptionUser));
            }

            await _companyInfoCacheService.RemoveCompanyInfoCache(bill.CompanyId);
            await _appDbContext.SaveChangesAsync();

            return Ok();
        }

        private async Task<IActionResult> HandleStripeCheckoutSessionCompletedEvent(Event stripeEvent)
        {
            SessionService sessionService = _stripeServicesFactory.CreateSessionService();

            SessionGetOptions sessionGetOptions = new SessionGetOptions();
            sessionGetOptions.AddExpand("line_items");

            try
            {
                //// TODO: Survey if able to loop through session's line items instead of parsing query string
                var session = stripeEvent.Data.Object as Session;
                session = await sessionService.GetAsync(session!.Id, sessionGetOptions);

                if (session != null && session.Metadata.ContainsKey("source"))
                {
                    _logger.LogInformation(
                        "The source of the event:{StripeEventId} is not from Travis_backend, will be ignored",
                        stripeEvent.Id);

                    return Ok();
                }

                string successUrl = session.SuccessUrl;

                if (successUrl.Contains("planId") && successUrl.Contains("oneoff"))
                {
                    var company = await _appDbContext.CompanyCompanies.FirstOrDefaultAsync(x => x.Id == session.ClientReferenceId);

                    if (company.IsPaymentFailed)
                    {
                        company.IsPaymentFailed = false;
                    }

                    if (company.SubscriptionTrialDays.HasValue)
                    {
                        company.SubscriptionTrialDays = null;
                    }

                    var successUri = new Uri(session.SuccessUrl);
                    var planId = HttpUtility.ParseQueryString(successUri.Query).Get("planId");
                    var subscriptionPlan = await _appDbContext.CoreSubscriptionPlans.FirstOrDefaultAsync(x => x.Id == planId);

                    //// Use Description to Query at this moment, as PriceId in LineItems are not able to match with PriceId in database.
                    var purchasedItem = session.LineItems?.FirstOrDefault(x => x.Description == subscriptionPlan.SubscriptionName);

                    var billRecord = new BillRecord
                    {
                        SubscriptionPlan = subscriptionPlan,
                        customer_email = session.CustomerEmail,
                        currency = session.Currency,
                        created = DateTime.UtcNow,
                        CompanyId = company.Id,
                        amount_due = (session.AmountTotal - session.TotalDetails.AmountDiscount).Value,
                        amount_paid = (session.AmountTotal - session.TotalDetails.AmountDiscount).Value,
                        amount_remaining = 0,
                        PayAmount = (session.AmountTotal - session.TotalDetails.AmountDiscount).Value / 100d,
                        Status = BillStatus.Active,
                        PaymentStatus = PaymentStatus.Paid,
                        customerId = session.CustomerId,
                        metadata = session.Metadata,
                        SubscriptionTier = subscriptionPlan.SubscriptionTier,
                        PeriodStart = DateTime.UtcNow,
                        PeriodEnd = DateTime.UtcNow.AddYears(99),
                        quantity = purchasedItem?.Quantity ?? 0
                    };

                    await _appDbContext.CompanyBillRecords.AddAsync(billRecord);
                    await _appDbContext.SaveChangesAsync();

                    BackgroundJob.Enqueue<IInternalHubSpotService>(x => x.SyncCompany(company.Id, null));
                    BackgroundJob.Enqueue<ICoreService>(
                        x => x.SyncCompanyStaffsToSleekFlowContactsBackground(company.Id));
                    BackgroundJob.Enqueue<IInternalAnalyticService>(
                        x => x.UpdateCompanyAllTimeRevenueAnalyticInfo(
                            new List<string>()
                            {
                                company.Id
                            }));
                }
                else
                {
                    string _twilioAccountId = null;
                    string resellerCompanyProfileId = null;
                    string resellerTopUpLogId = null;

                    try
                    {
                        _twilioAccountId = session.Metadata["twilioAccountId"];
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(
                            ex,
                            "POST stripe/webhook error getting twilioAccountId from session metadata. {ExceptionMessage}",
                            ex.Message);
                    }

                    try
                    {
                        resellerCompanyProfileId = session.Metadata["resellerCompanyProfileId"];
                        resellerTopUpLogId = session.Metadata["resellerTopUpLogId"];
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(
                            ex,
                            "POST stripe/webhook error getting resellerCompanyProfileId/resellerTopUpLogId from session metadata. {ExceptionMessage}",
                            ex.Message);
                    }

                    if (_twilioAccountId == null && resellerCompanyProfileId == null &&
                        resellerTopUpLogId == null)
                    {
                        // update card
                        var service = new SetupIntentService();
                        var intnet = service.Get(session.SetupIntentId);

                        var options = new CustomerUpdateOptions
                        {
                            InvoiceSettings = new CustomerInvoiceSettingsOptions
                            {
                                DefaultPaymentMethod = intnet.PaymentMethodId,
                            },
                        };
                        var customerService = new CustomerService();
                        customerService.Update(session.CustomerId, options);

                        var subscriptions = await _appDbContext.CompanyBillRecords.Where(
                                x => x.Status == BillStatus.Active && x.customerId == session.CustomerId)
                            .ToListAsync();
                        var subscriptionService = new SubscriptionService();
                        var _options = new SubscriptionUpdateOptions
                        {
                            DefaultPaymentMethod = intnet.PaymentMethodId,
                        };
                        foreach (var subscription in subscriptions)
                        {
                            try
                            {
                                subscriptionService.Update(subscription.stripe_subscriptionId, _options);
                                await _companyInfoCacheService.RemoveCompanyInfoCache(subscription.CompanyId);
                            }
                            catch (Exception ex)
                            {
                                _logger.LogError(
                                    ex,
                                    "POST stripe/webhook error updating subscription payment for Stripe subscription id {StripeSubscriptionId}. {ExceptionMessage}",
                                    subscription.stripe_subscriptionId,
                                    ex.Message);
                            }
                        }
                    }
                    else if (_twilioAccountId != null)
                    {
                        await _twilioService.Topup(
                            _twilioAccountId,
                            (decimal) session.AmountTotal / 100,
                            TwilioTopUpMethod.Stripe,
                            stripeCheckoutSession: session);
                    }
                    else if (resellerCompanyProfileId != null && resellerTopUpLogId != null)
                    {
                        await _resellerPortalRepository.TopUp(
                            resellerCompanyProfileId,
                            resellerTopUpLogId,
                            (decimal) session.AmountTotal / 100,
                            ResellerTopUpMethod.Stripe,
                            stripeCheckoutSession: session);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "POST stripe/webhook error when processing event type {EventType}. {ExceptionMessage}",
                    stripeEvent.Type,
                    ex.Message);
            }

            return Ok();
        }

        #endregion

        #region Private Functions

        private async Task UpdateSleekFlowUserProfile(
            InvoiceLineItem subscriptionInfo,
            BillRecord lastPayment,
            SubscriptionPlan newSubscriptionPlan,
            string phoneNumber,
            DateTime planStartDateTime)
        {
            try
            {
                var companyId = _configuration.GetValue<String>("Values:SleekFlowCompanyId");
                var userProfile = (await _userProfileService.GetUserProfilesByFields(
                    companyId,
                    new List<Condition>
                    {
                        new Condition
                        {
                            FieldName = "PhoneNumber",
                            ConditionOperator = SupportedOperator.Equals,
                            Values = new List<string>
                            {
                                phoneNumber
                            }
                        }
                    },
                    0,
                    1)).UserProfiles.FirstOrDefault();

                if (subscriptionInfo.Amount > 0)
                {
                    BackgroundJob.Enqueue<IUserProfileService>(
                        x => x.SetFieldValueByFieldNameSafe(
                            userProfile.Id,
                            "LeadStage",
                            "Customers",
                            true));
                }

                if (lastPayment == null)
                {
                    BackgroundJob.Enqueue<IUserProfileService>(
                        x => x.SetFieldValueByFieldNameSafe(
                            userProfile.Id,
                            "Plan Start",
                            planStartDateTime.ToString("o"),
                            true));
                }

                BackgroundJob.Enqueue<IUserProfileService>(
                    x => x.SetFieldValueByFieldNameSafe(
                        userProfile.Id,
                        "Plan",
                        newSubscriptionPlan.SubscriptionTier.ToString(),
                        true));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Unabled to update UserProfile. Message: {Message}", ex.Message);
            }
        }

        private async Task HandleSubscriptionPlanUpgradeIncentivesAsync(string companyId, BillRecord lastSubscriptionBillRecord, InvoiceLineItem invoiceLineItem, SubscriptionPlan subscriptionPlan, DateTime eventDateTime)
        {
            _logger.LogInformation(
                "Handle Subscription Plan Upgrade Incentives. " +
                "PreviousSubscriptionPlanId: {PreviousSubscriptionPlanId}, " +
                "NewSubscriptionPlanId: {NewSubscriptionPlanId}," +
                "NewSubscriptionPlanStripePriceId: {NewSubscriptionPlanStripePriceId}, " +
                "EventDateTime: {EventDateTime}, " +
                "IncentivesStartDateTime: {IncentivesStartDateTime}, " +
                "IncentivesEndDateTime: {IncentivesEndDateTime}",
                lastSubscriptionBillRecord?.SubscriptionPlanId,
                subscriptionPlan.Id,
                subscriptionPlan.StripePlanId,
                eventDateTime,
                _globalPricingOptions.GetPlanMigrationIncentivesStartDate(),
                _globalPricingOptions.GetPlanMigrationIncentivesEndDate());

            var isPreviousPlanIsLagacyProPlan = lastSubscriptionBillRecord?.SubscriptionPlan.Version < SubscriptionPlanVersions.Version10 && lastSubscriptionBillRecord?.SubscriptionPlan.SubscriptionTier == SubscriptionTier.Pro;
            var isSubscribedToStandardV10ProPlan = subscriptionPlan.Version == SubscriptionPlanVersions.Version10 && subscriptionPlan.SubscriptionTier == SubscriptionTier.Pro && subscriptionPlan.StripePlanId == invoiceLineItem.Plan.Id;
            var isInValidCountryTier = subscriptionPlan.CountryTier != nameof(SubscriptionCountryTier.Tier3);
            var isValidUpgradeTime = _globalPricingOptions.GetPlanMigrationIncentivesStartDate() <= eventDateTime && eventDateTime < _globalPricingOptions.GetPlanMigrationIncentivesEndDate().AddDays(1);

            if (isPreviousPlanIsLagacyProPlan && isSubscribedToStandardV10ProPlan && isInValidCountryTier && isValidUpgradeTime)
            {
                _logger.LogInformation("Start Applying Plan Upgrade Incetives to Company: {CompanyId}.", companyId);

                var companyUsageOffsetProfile = await _companyUsageService.GetCompanyUsageLimitOffsetProfile(companyId);
                var incentiveOffsetProfile = GetSubscriptionPlanUpgradeIncentiveOffsetProfile(subscriptionPlan);

                if (companyUsageOffsetProfile == null)
                {
                    await _companyUsageService.UpdateCompanyUsageLimitOffsetProfileAsync(companyId, incentiveOffsetProfile);
                }
                else
                {
                    companyUsageOffsetProfile.AgentsLimitOffset += incentiveOffsetProfile.AgentsLimitOffset;
                    companyUsageOffsetProfile.ContactsLimitOffset += incentiveOffsetProfile.ContactsLimitOffset;
                    companyUsageOffsetProfile.AutomatedMessagesLimitOffset += incentiveOffsetProfile.AutomatedMessagesLimitOffset;
                    companyUsageOffsetProfile.ChannelLimitOffset += incentiveOffsetProfile.ChannelLimitOffset;

                    await _companyUsageService.UpdateCompanyUsageLimitOffsetProfileAsync(companyId, companyUsageOffsetProfile);
                }
            }
        }

        private CompanyUsageLimitOffsetProfile GetSubscriptionPlanUpgradeIncentiveOffsetProfile(SubscriptionPlan subscriptionPlan)
        {
            var countryTier = subscriptionPlan.CountryTier.ToEnum<SubscriptionCountryTier>();

            var contactLimit = countryTier switch
            {
                SubscriptionCountryTier.Tier1 or SubscriptionCountryTier.Tier2 => 8000,
                SubscriptionCountryTier.Tier4 => 15000,
                SubscriptionCountryTier.Tier5 => 35000,
                _ => throw new ApplicationException("Invalid Tier for Subscription Plan Upgrade Incentives.")
            };

            return new CompanyUsageLimitOffsetProfile
            {
                AgentsLimitOffset = 2,
                ContactsLimitOffset = contactLimit,
                AutomatedMessagesLimitOffset = 295_000,
                ChannelLimitOffset = 5
            };
        }

        private async Task TerminateBillRecordsOnProratedCancelledInvoiceItemReceive(string companyId, bool isProratedCancelledItem, InvoiceLineItem invoiceLineItem)
        {
            if (isProratedCancelledItem
                && !string.IsNullOrWhiteSpace(invoiceLineItem.Subscription)
                && !string.IsNullOrWhiteSpace(invoiceLineItem.SubscriptionItem))
            {
                var stripeSubscription = await _stripeSubscriptionService.GetSubscriptionAsync(invoiceLineItem.Subscription);

                if (stripeSubscription is { Status: SubscriptionStatuses.Active })
                {
                    var stripeSubscriptionItem = stripeSubscription.Items.FirstOrDefault(x => x.Id == invoiceLineItem.SubscriptionItem);

                    if (stripeSubscriptionItem == null)
                    {
                        //// Cannot find SubscriptionItem from it active Subscription
                        //// Terminate all BillRecords of this item under this subscription
                        await _companySubscriptionService.TerminateBillRecords(
                            companyId,
                            invoiceLineItem.Subscription,
                            invoiceLineItem.SubscriptionItem);
                    }
                }
            }
        }

        private async Task CancelStripeSubscriptionAsync(
            string companyId,
            Invoice invoicePayment,
            BillRecord lastPayment,
            SubscriptionPlan subscriptionPlan,
            string json)
        {
            await _stripeSubscriptionService.CancelSubscriptionAsync(lastPayment.stripe_subscriptionId);

            _logger.LogInformation(
                "[Stripe] Subscription refund flow triggered" +
                "Invoice events related to invoice {InvoicePaymentId}, " +
                "Customer {InvoicePaymentCustomerEmail} ({InvoicePaymentCustomerId}), " +
                "new subscription {InvoicePaymentSubscriptionId}, " +
                "from subscription {LastPayment.stripe_subscriptionId} ({LastPayment.SubscriptionPlanId}))",
                invoicePayment.Id,
                invoicePayment.CustomerEmail,
                invoicePayment.CustomerId,
                invoicePayment.SubscriptionId,
                lastPayment.stripe_subscriptionId,
                lastPayment.SubscriptionPlanId);

            SendSubscriptionCancelledAlert(
                companyId,
                "[Stripe] Subscription refund flow triggered",
                invoicePayment,
                subscriptionPlan,
                lastPayment,
                json);
        }

        private void SendSubscriptionCancelledAlert(
            string companyId,
            string subject,
            Invoice invoicePayment,
            SubscriptionPlan subscriptionPlan,
            BillRecord lastPayment,
            string json)
        {
            BackgroundJob.Enqueue<IEmailNotificationService>(
                x => x.SendSystemAlertToSlackChannel(
                    subject,
                    $"[[Invoice]]: <a clicktracking=off href='https://dashboard.stripe.com/invoices/{invoicePayment.Id}'>{invoicePayment.Id}</a>\n" +
                    $"[[Customer]]: {invoicePayment.CustomerEmail} (<a clicktracking=off href='https://dashboard.stripe.com/customers/{invoicePayment.Id}'>{invoicePayment.Id}</a>)\n" +
                    $"[[New subscription]]: {subscriptionPlan.Id} (<a clicktracking=off href='https://dashboard.stripe.com/subscriptions/{invoicePayment.SubscriptionId}'>{invoicePayment.SubscriptionId}</a>)\n" +
                    $"[[Last subscription]]: {lastPayment.SubscriptionPlanId} ({lastPayment.stripe_subscriptionId})\n" +
                    $"[[Payload]]:\n" +
                    $"<pre>{json}</pre>",
                    "stripe",
                    "notification",
                    companyId,
                    true));
        }

        #endregion
    }
}