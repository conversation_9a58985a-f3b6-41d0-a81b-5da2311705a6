using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.CompanyDomain.ViewModels;
using Travis_backend.Database;

namespace Travis_backend.CompanyDomain.Repositories;

/// <inheritdoc />
public class CompanyRepository : ICompanyRepository
{
    /// <summary>
    /// Application DbContext
    /// </summary>
    private readonly ApplicationDbContext _applicationDbContext;

    /// <summary>
    /// Initializes a new instance of the <see cref="CompanyRepository"/> class.
    /// </summary>
    /// <param name="appDbContext">ApplicationDbContext</param>
    public CompanyRepository(ApplicationDbContext appDbContext)
    {
        _applicationDbContext = appDbContext;
    }

    /// <inheritdoc />
    public async Task<Company> FindById(string companyId)
    {
        return await _applicationDbContext.CompanyCompanies.FindAsync(companyId);
    }

    /// <inheritdoc />
    public async Task<int> UpdateCompanySubscriptionTierAndCurrencyAsync(string companyId, string subscriptionCountryTier, string currency)
    {
        return await _applicationDbContext.CompanyCompanies.Where(x => x.Id == companyId)
            .ExecuteUpdateAsync(entity => entity
                .SetProperty(p => p.SubscriptionCountryTier, subscriptionCountryTier)
                .SetProperty(p => p.SubscriptionCurrency, currency));
    }

    /// <inheritdoc />
    public async Task<bool> IsSubscriptionCountryTierHasValue(string companyId)
    {
        return await _applicationDbContext.CompanyCompanies
            .AnyAsync(x => x.Id == companyId && string.IsNullOrWhiteSpace(x.SubscriptionCountryTier) == false);
    }

    /// <inheritdoc />
    public async Task<bool> IsSubscriptionCurrencyHasValue(string companyId)
    {
        return await _applicationDbContext.CompanyCompanies
            .AnyAsync(x => x.Id == companyId && string.IsNullOrWhiteSpace(x.SubscriptionCurrency) == false);
    }

    /// <inheritdoc />
    public Task<int> UpdateCompanyUsageLimitOffsetProfileAsync(string companyId, CompanyUsageLimitOffsetProfile profile)
    {
        return _applicationDbContext.CompanyCompanies.Where(x => x.Id == companyId)
            .ExecuteUpdateAsync(entity => entity
                .SetProperty(p => p.UsageLimitOffsetProfile, profile));
    }

}