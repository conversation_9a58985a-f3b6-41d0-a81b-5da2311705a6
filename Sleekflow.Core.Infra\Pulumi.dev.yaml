config:
  pulumi:template: azure-csharp
  sleekflow:name: dev
  sleekflow:cors:
    domains:
      - http://localhost:3000
      - http://localhost:5173
      - https://uat.sleekflow.io
      - https://uat2.sleekflow.io
      - https://uat-revamp.sleekflow.io
      - https://sleekflow-chat-react-app.hensontsai.com
      - https://dev-revamp.sleekflow.io
      - http://livechat-dev-sleekflow.s3-website-ap-southeast-1.amazonaws.com
      - http://tripbo-frontend-demo.s3-website-ap-southeast-1.amazonaws.com
      - http://localhost:5174
      - https://v1-dev.sleekflow.io
      - https://dev.sleekflow.io
  sleekflow:default_sleekflow_core_domain:
  sleekflow:default_sleekflow_core_front_door_domain: https://sleekflow-dev-gug7frbbe9grfvhh.z01.azurefd.net
  sleekflow:regional_configs:
    - location_name: eastasia
      sku_config:
        sleekflow_core:
          name: P3V3
          tier: PremiumV3
        sleekflow_core_db:
          name: HS_S_Gen5
          tier: Hyperscale
          family: Gen5
          capacity: 2
        sleekflow_powerflow:
          name: P1V3
          tier: PremiumV3
        sleekflow_sleek_pay:
          name: P0V3
          tier: PremiumV3
        redis:
          default:
            name: Standard
            family: C
            capacity: 1
          caching:
            name: Standard
            family: C
            capacity: 1
      sql_db_config:
        administrator_login_random_secret: admin
        administrator_login_password_random_secret: admin123456
        whitelist_ip_ranges:
          - start_ip_address: **************
            end_ip_address: **************
          - start_ip_address: **************
            end_ip_address: **************
          - start_ip_address: **************
            end_ip_address: **************
          - start_ip_address: **************
            end_ip_address: **************
          - start_ip_address: **************
            end_ip_address: **************
        is_read_scale_enable: "true"
        high_availability_replica_count: 1
      vnet:
        default_address_space: **********/16
        default_subnet_address_prefix: **********/24
        sleekflow_core_db_address_prefix: **********/24
        sleekflow_core_address_prefix: **********/24
        sleekflow_powerflow_address_prefix: **********/24
        sleekflow_sleek_pay_address_prefix: **********/24
        sleekflow_core_worker_address_prefix: **********/24
      auto_scale_config:
        sleekflow_core:
          capacity:
            default: "3"
            maximum: "10"
            minimum: "2"
          scale_out_instances: "1"
          scale_down_instances: "1"
      sleekflow_core_config:
        aspnetcore_environment: development
        logger:
          gcp_is_enabled: "TRUE"
          gcp_project_id: cool-phalanx-404402
          gcp_credential_json: ************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
        audit_hub:
          endpoint: https://sleekflow-dev-gug7frbbe9grfvhh.z01.azurefd.net/v1/audit-hub
          key: 3Ri7RqtKBR1sXHCGXLoGiasTBVqE23pzPXUQVnKPw3v5BX4zr2SM9HcXFGlUkWMq
        auth0:
          action_audience: https://api-dev.sleekflow.io/
          action_issuer: https://sso-dev.sleekflow.io/
          audience: https://api-dev.sleekflow.io
          client_id: A8CAuyJshC04w6FYYZZdxAtfoXSvKFnU
          client_secret: gprpIRuClarYc4yrpsYHFCDrxHhbVSets7C7v3BmsDOmoinRC84BjABvE76LCVpl
          database_connection_name: Sleekflow-Username-Password-Authentication
          domain: sleekflow-dev.eu.auth0.com
          http_retries: 10
          issuers:
            - https://sso-dev.sf.chat/
            - https://sleekflow-dev.eu.auth0.com
          namespace: https://app.sleekflow.io/
          role_claim_type: roles
          user_email_claim_type: email
          user_id_claim_type: user_id
          username_claim_type: user_name
          tenant_hub_secret_key: 6a9_nBt?)R#@_he=v2Eo!K3B3Ae0ao`x*I`m}ZSX*S~hsQ7bQD]k#gh8r38ad,9o
          health_check:
            is_enabled: "true"
            client_id: cMUb5OuJmGnWPhejGeqMzIexkBVX5XKE
            client_secret: 92kTj3EOB1nuvN8My84dS8GD8W4159oa_3jyUiuxurOff1ViN-SvEwGBMOLIE8aE
            username: <EMAIL>
            password: uhn7W+=LdXLY9^m
        azure:
          media_service:
            account_name: sfmediadev
            client_id: 048e63db-bf3d-442e-bf1e-d30b2fd4b555
            client_secret: ****************************************
            resource_group: sleekflow-resource-group-devf9af1d41
            subscription_id: c19c9b56-93e9-4d4c-bc81-838bd3f72ad6
            tenant_id: d66fa1cc-347d-42e9-9444-19c5fd0bbcce
          text_analytics_credentials: 4a76771e5629485ba34bcdfca9ed3487
          text_analytics_url: https://sleekflowtextanalytics.cognitiveservices.azure.com/
        application_insights:
          is_telemetry_tracer_enabled: "true"
          is_sampling_disabled: "false"
        chat_api:
          api_key: qVH3fqZufwPZaPlAmsIJMwcxuHI2
          api_url: https://us-central1-app-chat-api-com.cloudfunctions.net
        commerce_hub:
          endpoint: https://sleekflow-dev-gug7frbbe9grfvhh.z01.azurefd.net/v1/commerce-hub
          key: w7zJUyirdVnNom4M6YTyxgtbTQDQL2gxHPRbKy9sSXaraX6GC4Ru8I1ejjpFS0LnSnJrRxAtO9YxxrRMGqj2H5EycUywMgQc0RhKWmQvmAe93j5jMJqunIG8aYilMp8Q
        crm_hub:
          endpoint: https://sleekflow-dev-gug7frbbe9grfvhh.z01.azurefd.net/v1/crm-hub
          key: a12da7c775d00cada5b1ee611d3f6dca
        data_snapshot:
          is_enable: "false"
        development:
          redirect_fb_webhook: https://9633-42-200-140-118.ngrok.io/facebook/Webhook
        environment_features:
          is_recurring_job_enabled: "false"
        epplus:
          excel_package:
            license_context: NonCommercial
        facebook:
          client_id: 812364635796464
          client_secret: ********************************
        ffmpeg:
          ffmpeg_exe_name: /usr/bin/ffmpeg
        flow_hub:
          endpoint: https://sleekflow-dev-gug7frbbe9grfvhh.z01.azurefd.net/v1/flow-hub
          key: BWijifKMPGviFQpYEkZuLjHOTpySNVawLYhiFKHEcmJyUDQPYlKuYUmJqgXVGGHP
        geo_sql_db:
          name: travis-crm-prod-db
        hub_spot:
          internal_hub_spot_api_key:
          is_enable: "false"
        instrumentation_engine_extension_version: disabled
        internal_google_cloud:
          credential:
        general_google_cloud:
**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
          project_id: cool-phalanx-404402
          server_location: asia-east1
          google_storage_bucket_name: sleekflow-transcoder-dev
        ip_look_up:
          key: 8fc198c42a094d588657c6f05c707518
        intelligent_hub:
          endpoint: https://sleekflow-dev-gug7frbbe9grfvhh.z01.azurefd.net/v1/intelligent-hub
          key: BWijifKMPGviFQpYEkZuLjHOTpySNVawLYhiFKHEcmJyUDQPYlKuYUmJqgXVGGHP
        webhook_hub:
          endpoint: https://sleekflow-dev-gug7frbbe9grfvhh.z01.azurefd.net/v1/webhook-hub
          key: BWijifKMPGviFQpYEkZuLjHOTpySNVawLYhiFKHEcmJyUDQPYlKuYUmJqgXVGGHP
          auth_secret_key: O19dnDlZhSZkZ5TigIXiolCTqSl461kyBCotXGOJGUMA6eiHfyxRQVwQTFN5qMr1
        message_hub:
          endpoint: https://sleekflow-dev-gug7frbbe9grfvhh.z01.azurefd.net/v1/messaging-hub
          key: a12da7c775d00cada5b1ee611d3f6dca
        mixpanel:
          token: f1fdcc653c49d57f8417633000b36626
        mobile_app_management_extension_version: latest
        notification_hub:
          connection_string: Endpoint=sb://sleekflowtesting.servicebus.windows.net/;SharedAccessKeyName=DefaultFullSharedAccessSignature;SharedAccessKey=l5XR33aDeefA79lau7IjY252oJRpdB/h4pP6v3F6hVY=
          hub_name: sleekflowTesting
        public_api_gateway:
          endpoint: https://sleekflow-dev-gug7frbbe9grfvhh.z01.azurefd.net/v1/public-api-gateway
          key: a12da7c775d00cada5b1ee611d3f6dca
        redis:
          connection_string: sleekflow-sp-uat.redis.cache.windows.net:6380,password=Ld6bAceqgYpA64Qu5B7QzFqx3GhRZolAKAzCaB5f6Fw=,ssl=True,abortConnect=False
        reseller:
          domain_name: https://partner-uat.sleekflow.io/
        rewardful:
          api_secret: ac7a0f566451bc405148d186bf0301c1
        salesforce:
          custom_active_web_app: https://sleekflow-sfmc-jb-ca-app.graydesert-391172fb.eastasia.azurecontainerapps.io
        share_hub:
          endpoint: https://sleekflow-dev-gug7frbbe9grfvhh.z01.azurefd.net/v1/share-hub
          key: a12da7c775d00cada5b1ee611d3f6dca
        shopify:
          shopify_api_key: b1781c9e361cfc851ee8a857fd046cca
          shopify_secret_key: 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
        sleek_pay:
          domain_name: https://pay-eas-dev.sleekflow.io
          shopify_graphql_admin_api_version: 2022-10
          stripe:
            public_keys:
              default: pk_test_51Klpk8I2ilBBY57ZOYxowsWUSUD7Wb71pSzgBzcRwF7CQPpa1XdP7vpwubZkbgu5w9HAsggIbs8W6n1bVuesSBnh00hSrtEwNW
              gb: pk_test_51LBDmrD9JBREupA8qeg1Xl7DIhOMkP4z4lk0PLRyt4CIE7p25Wfgg8D6MGHqc8hESWQb269sxIk7YfgeXnZDQBNr00uIwJ3AEF
              hk: pk_test_51Klpk8I2ilBBY57ZOYxowsWUSUD7Wb71pSzgBzcRwF7CQPpa1XdP7vpwubZkbgu5w9HAsggIbs8W6n1bVuesSBnh00hSrtEwNW
              my: pk_test_51LBDj8GBiw7eM4IGPO4LyWhI0ayqKzuDWEHFyQhTl0lCizTCKPFgDqyJ3mGrFSOHfb9gmCYeYFP8JXNQUP3x7Uhb00d6wqvCuw
              sg: pk_test_51L8iI0HRmviISgVc4FP0hp9HWJRMT6RuNKOD72qRZt3LeyyUXaS1XnuJVhtq7bPFk3jSMUyMvdA0rNcmEA1r69CX00ZmE8hrzr
            secret_keys:
              default: sk_test_51Klpk8I2ilBBY57ZDMKI33PKdi1uuyCunMQEqRmPdoyRgeBAG1zGH4Lw1DFNecaAgQZJy8yWENVMSo2IEx2bsBi20085GLAlzT
              gb: sk_test_51LBDmrD9JBREupA8NA9jMA4igzfP1cobkZaFDwLSzM4A6NGqT5EoPRBZwxOc09ELrysTrjJSr2S2JbnAwbUzhTUd00OyV4vbbY
              hk: sk_test_51Klpk8I2ilBBY57ZDMKI33PKdi1uuyCunMQEqRmPdoyRgeBAG1zGH4Lw1DFNecaAgQZJy8yWENVMSo2IEx2bsBi20085GLAlzT
              my: sk_test_51LBDj8GBiw7eM4IGl9EetqAZyX28K63bVNe7WGgxQKxgyBGWDzVvB6Pl5ZgJo9lwHkScCALGcTMbAZYuzuyfJyKp00jzoqWzw4
              sg: sk_test_51L8iI0HRmviISgVc5jfusO5bURwVRNqHWffm14fELQ1TPDJFr83PHejoQdDipVjRIphP70P6T0SuRsKgjKceStyR0089XUcIQL
            connect_webhook_secrets:
              gb: whsec_q0kKqmX40FPGTRx3spPqWGpkaPcphnZW
              hk: whsec_DdelEMMBKJTSn2AxEl1EHbezSctBBZnU
              my: whsec_1CGmZLf0OTT8pWOFtb8SWkEOhntvAo7b
              sg: whsec_R8hnPAIyxnAjRCx6SuJUb8zhQZQ0FUS1
            report_webhook_secrets:
              gb:
              hk:
              my:
              sg:
            webhook_secrets:
              default: whsec_NpeP3lCjZNosFjWyENB602BlKpzFGgHA
              gb: whsec_40gh0M75e9oBPqA6eGWXgmresw2mI6JN
              hk: whsec_NpeP3lCjZNosFjWyENB602BlKpzFGgHA
              my: whsec_5kwjzRTwabM92Tc8l0rBSKqqjzpyW7YZ
              sg: whsec_eftFUXDoZ5bjzf3SzakHRgVdmDNayguK
        snapshot_debugger_extension_version: disabled
        sql_performance:
          from_raw_sql: "true"
          is_and_condition_enabled: "true"
          is_or_condition_enabled: "true"
          is_conversation_analytics_condition_enabled: "true"
          is_shopify_order_statistics_enabled: "true"
          is_sales_performance_enabled: "true"
        stripe:
          stripe_public_key: pk_test_YRHpj6Ye2OBU9veVI4CdFMBe004bf0cq6Q
          stripe_report_key: ***********************************************************************************************************
          stripe_secret_key: sk_test_W6snxTtlAExO9vFjvX5fbLdq00lAxGJMuk
          stripe_webhook_secret: whsec_damtLB6SsZURaPvJ3FrGxKT6wLsGTPnz
        stripe_payment:
          stripe_payment_secret_key_gb: sk_test_51LBDmrD9JBREupA8NA9jMA4igzfP1cobkZaFDwLSzM4A6NGqT5EoPRBZwxOc09ELrysTrjJSr2S2JbnAwbUzhTUd00OyV4vbbY
          stripe_payment_secret_key_hk: sk_test_51Klpk8I2ilBBY57ZDMKI33PKdi1uuyCunMQEqRmPdoyRgeBAG1zGH4Lw1DFNecaAgQZJy8yWENVMSo2IEx2bsBi20085GLAlzT
          stripe_payment_secret_key_my: sk_test_51LBDj8GBiw7eM4IGl9EetqAZyX28K63bVNe7WGgxQKxgyBGWDzVvB6Pl5ZgJo9lwHkScCALGcTMbAZYuzuyfJyKp00jzoqWzw4
          stripe_payment_secret_key_sg: sk_test_51L8iI0HRmviISgVc5jfusO5bURwVRNqHWffm14fELQ1TPDJFr83PHejoQdDipVjRIphP70P6T0SuRsKgjKceStyR0089XUcIQL
        stripe_report:
          stripe_report_webhook_secret_gb:
          stripe_report_webhook_secret_hk:
          stripe_report_webhook_secret_my:
          stripe_report_webhook_secret_sg:
        tenant_hub:
          endpoint: https://sleekflow-dev-gug7frbbe9grfvhh.z01.azurefd.net/v1/tenant-hub
          key: BWijifKMPGviFQpYEkZuLjHOTpySNVawLYhiFKHEcmJyUDQPYlKuYUmJqgXVGGHP
          is_enable_tenant_logic: "true"
        ticketing_hub:
          endpoint: https://sleekflow-dev-gug7frbbe9grfvhh.z01.azurefd.net/v1/ticketing-hub
          key: wLYhiFKHEcmJyUDQPYlKuYUmJqgXVGGHPBWijifKMPGviFQpYEkZuLjHOTpySNVa
          is_enable_ticketing_logic: "false"
        test_swaping: 1
        token:
          audience: https://sleekflow-prod-api.azurewebsites.net
        tokens:
          audience: https://uat.sleekflow.io
          issuer: https://uat.sleekflow.io
          key: 83A74F5963336617701F83BBB8A4201212C42A51E721DD51CAB28201ADEDF8BB
          lifetime: 365
        user_event_hub:
          endpoint: https://sleekflow-dev-gug7frbbe9grfvhh.z01.azurefd.net/v1/user-event-hub
          key: wLYhiFKHEcmJyUDQPYlKuYUmJqgXVGGHPBWijifKMPGviFQpYEkZuLjHOTpySNVa
        values:
          app_domain_name: https://uat2.sleekflow.io
          app_domain_name_v1: https://v1-dev.sleekflow.io
          domain_name: https://sleekflow-core-app-eas-dev.azurewebsites.net
          share_link_function: https://sleekflow-share-tracking-uat.azurewebsites.net
          sleekflow_api_gateway: https://sleekflow-dev-gug7frbbe9grfvhh.z01.azurefd.net
          sleekflow_company_id: b6d7e442-38ae-4b9a-b100-2951729768bc
        website_http_logging_retention_days: 2
        website_node_default_version: 6.9.1
        whatsapp_cloud_api_template:
          default_image_blob_id: sleekflow.png
        xdt_microsoft_application_insights_base_extensions: disabled
        xdt_microsoft_application_insight_mode: recommended
        global_pricing:
          is_feature_enabled: "true"
          plan_migration_incentives_start_date: "2024-10-20"
          plan_migration_incentives_end_date: "2025-02-20"
        feature_flags:
          - feature_name: FlowBuilderMonetisation
            is_enabled: "true"
          - feature_name: CancelledSubscriptionTermination
            is_enabled: "true"
        contact_safe_deletion:
          is_feature_enabled: "true"
        partner_stack:
          public_key: pk_bvEwuCDvoz9Qh11y48006zWXdCgsvInk
          secret_key: sk_KnZllhAJOasP09CPsMOOoiUqKAO81nMy
        facebook_lead_ads_disconnected_notification:
          from_phone_number: "18454069890"
          template_name: "integration_disconnect_noti"
          reconnect_url: "https://dev.sleekflow.io/en/integrations/facebook-lead-ad"
          endpoint: "https://sleekflow-core-dev-e6d7dyf5drg4eag5.z01.azurefd.net/api/notifications/integration-disconnected"
          api_key: "BmRu1DrI4hEh01Up864pLdoTHKKxOJJCvuxIqPAsw"
          host_company_id: "b6d7e442-38ae-4b9a-b100-2951729768bc"
        hangfire_worker:
          worker_count: 20
        integration_alert:
          endpoint: "https://sleekflow-core-dev-e6d7dyf5drg4eag5.z01.azurefd.net/api/notifications/integration-disconnected"
          api_key: "BmRu1DrI4hEh01Up864pLdoTHKKxOJJCvuxIqPAsw"
          host_company_id: "b6d7e442-38ae-4b9a-b100-2951729768bc"
          from_phone_number: "18454069890"
          template_name: "integration_disconnect_noti"
          facebook_lead_ads_help_center_url: "https://help.sleekflow.io/integrations/facebook-lead-ads-integration"
        internal_integration_hub:
          endpoint: "https://sleekflow-dev-gug7frbbe9grfvhh.z01.azurefd.net/v1/internal-integration-hub"
          key: LxiRidS8SN9XY2rqu2GpQPih9kuxfNbcn5nxewNwmHmxdMDundJVbf4BfwZVMcjX
        hangfire_queues:
          disable_instances: ""
