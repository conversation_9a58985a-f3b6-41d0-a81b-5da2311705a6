﻿using System.Net.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Sleekflow.Apis.MessagingHub.Api;
using Travis_backend.Constants;
using Travis_backend.MessagingHubDomain.HttpClientHandlers;

namespace Travis_backend.MessagingHubDomain;

public static class MessagingHubExtensions
{
    public static IServiceCollection RegisterMessagingHubServices(
        this IServiceCollection services,
        IConfiguration configuration)
    {
        var messagingHubConfig = new Sleekflow.Apis.MessagingHub.Client.Configuration
        {
            BasePath = configuration.GetValue<string>("MessagingHub:Endpoint")
        };

        services.AddSingleton(messagingHubConfig);

        services.AddTransient<MessagingHubHttpClientHandler>();
        services.AddHttpClient(
            HttpClientHandlerName.MessagingHub,
            (sp, client) =>
            {
                var config = sp.GetRequiredService<IConfiguration>();
                client.DefaultRequestHeaders.TryAddWithoutValidation(
                    "X-Sleekflow-Key",
                    config.GetValue<string>("MessagingHub:Key"));
            });

        services.AddScoped<IWabasApi, WabasApi>(
            sp =>
                new (
                    sp.GetRequiredService<IHttpClientFactory>()
                        .CreateClient(HttpClientHandlerName.MessagingHub),
                    sp.GetRequiredService<Sleekflow.Apis.MessagingHub.Client.Configuration>(),
                    sp.GetRequiredService<MessagingHubHttpClientHandler>()));

        services.AddScoped<ITemplatesApi, TemplatesApi>(
            sp =>
                new (
                    sp.GetRequiredService<IHttpClientFactory>()
                        .CreateClient(HttpClientHandlerName.MessagingHub),
                    sp.GetRequiredService<Sleekflow.Apis.MessagingHub.Client.Configuration>(),
                    sp.GetRequiredService<MessagingHubHttpClientHandler>()));

        services.AddScoped<IChannelsApi, ChannelsApi>(
            sp =>
                new (
                    sp.GetRequiredService<IHttpClientFactory>()
                        .CreateClient(HttpClientHandlerName.MessagingHub),
                    sp.GetRequiredService<Sleekflow.Apis.MessagingHub.Client.Configuration>(),
                    sp.GetRequiredService<MessagingHubHttpClientHandler>()));

        services.AddScoped<IBalancesApi, BalancesApi>(
            sp =>
                new (
                    sp.GetRequiredService<IHttpClientFactory>()
                        .CreateClient(HttpClientHandlerName.MessagingHub),
                    sp.GetRequiredService<Sleekflow.Apis.MessagingHub.Client.Configuration>(),
                    sp.GetRequiredService<MessagingHubHttpClientHandler>()));

        services.AddScoped<IMigrationsApi, MigrationsApi>(
            sp =>
                new (
                    sp.GetRequiredService<IHttpClientFactory>()
                        .CreateClient(HttpClientHandlerName.MessagingHub),
                    sp.GetRequiredService<Sleekflow.Apis.MessagingHub.Client.Configuration>(),
                    sp.GetRequiredService<MessagingHubHttpClientHandler>()));

        services.AddScoped<IManagementsApi, ManagementsApi>(
            sp =>
                new (
                    sp.GetRequiredService<IHttpClientFactory>()
                        .CreateClient(HttpClientHandlerName.MessagingHub),
                    sp.GetRequiredService<Sleekflow.Apis.MessagingHub.Client.Configuration>(),
                    sp.GetRequiredService<MessagingHubHttpClientHandler>()));

        services.AddScoped<IMediasApi, MediasApi>(
            sp =>
                new (
                    sp.GetRequiredService<IHttpClientFactory>()
                        .CreateClient(HttpClientHandlerName.MessagingHub),
                    sp.GetRequiredService<Sleekflow.Apis.MessagingHub.Client.Configuration>(),
                    sp.GetRequiredService<MessagingHubHttpClientHandler>()));

        services.AddScoped<ITransactionLogsApi, TransactionLogsApi>(
            sp =>
                new (
                    sp.GetRequiredService<IHttpClientFactory>()
                        .CreateClient(HttpClientHandlerName.MessagingHub),
                    sp.GetRequiredService<Sleekflow.Apis.MessagingHub.Client.Configuration>(),
                    sp.GetRequiredService<MessagingHubHttpClientHandler>()));

        services.AddScoped<IProductCatalogsApi, ProductCatalogsApi>(
            sp =>
                new (
                    sp.GetRequiredService<IHttpClientFactory>()
                        .CreateClient(HttpClientHandlerName.MessagingHub),
                    sp.GetRequiredService<Sleekflow.Apis.MessagingHub.Client.Configuration>(),
                    sp.GetRequiredService<MessagingHubHttpClientHandler>()));

        services.AddScoped<IConversationalAutomationsApi, ConversationalAutomationsApi>(
            sp =>
                new ConversationalAutomationsApi(
                    sp.GetRequiredService<IHttpClientFactory>()
                        .CreateClient(HttpClientHandlerName.MessagingHub),
                    sp.GetRequiredService<Sleekflow.Apis.MessagingHub.Client.Configuration>(),
                    sp.GetRequiredService<MessagingHubHttpClientHandler>()));

        services.AddScoped<IWhatsappFlowsApi, WhatsappFlowsApi>(
            sp => new WhatsappFlowsApi(
                sp.GetRequiredService<IHttpClientFactory>().CreateClient(HttpClientHandlerName.MessagingHub),
                sp.GetRequiredService<Sleekflow.Apis.MessagingHub.Client.Configuration>(),
                sp.GetRequiredService<MessagingHubHttpClientHandler>()));

        services.AddScoped<IMetaConversionApisApi, MetaConversionApisApi>(
            sp => new MetaConversionApisApi(
                sp.GetRequiredService<IHttpClientFactory>().CreateClient(HttpClientHandlerName.MessagingHub),
                sp.GetRequiredService<Sleekflow.Apis.MessagingHub.Client.Configuration>(),
                sp.GetRequiredService<MessagingHubHttpClientHandler>()));
        return services;
    }
}