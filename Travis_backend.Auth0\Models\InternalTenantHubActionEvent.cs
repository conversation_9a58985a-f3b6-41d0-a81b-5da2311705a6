using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Travis_backend.Enums;

namespace Travis_backend.Auth0.Models;

public class RegisterCompanyInput
{
    [Required]
    public string CompanyId { get; set; }

    public string? UserId { get; set; }

    [Required]
    public string FirstName { get; set; }

    [Required]
    public string LastName { get; set; }

    [Required]
    public string PhoneNumber { get; set; }

    public string Industry { get; set; }

    public string OnlineShopSystem { get; set; }

    public string CompanyWebsite { get; set; }

    [Required]
    public string CompanyName { get; set; }

    public string TimeZoneInfoId { get; set; }

    public string CompanySize { get; set; }

    public string SubscriptionPlanId { get; set; } = "sleekflow_free";

    public string Lmref { get; set; }

    public string HeardFrom { get; set; }

    public string PromotionCode { get; set; }

    public string WebClientUUID { get; set; }

    public string Referral { get; set; }

    public CompanyType CompanyType { get; set; } = CompanyType.DirectClient;

    public List<string> CommunicationTools { get; set; }

    public bool? IsAgreeMarketingConsent { get; set; }

    public string Location { get; set; }

    [Required]
    public string ConnectionStrategy { get; set; }

    [JsonConstructor]
    public RegisterCompanyInput(
        string companyId,
        string? id,
        string firstName,
        string lastName,
        string phoneNumber,
        string industry,
        string onlineShopSystem,
        string companyWebsite,
        string companyName,
        string timeZoneInfoId,
        string companySize,
        string subscriptionPlanId,
        string lmref,
        string heardFrom,
        string promotionCode,
        string webClientUuid,
        string referral,
        CompanyType companyType,
        List<string> communicationTools,
        bool? isAgreeMarketingConsent,
        string location,
        string connectionStrategy)
    {
        CompanyId = companyId;
        UserId = id;
        FirstName = firstName;
        LastName = lastName;
        PhoneNumber = phoneNumber;
        Industry = industry;
        OnlineShopSystem = onlineShopSystem;
        CompanyWebsite = companyWebsite;
        CompanyName = companyName;
        TimeZoneInfoId = timeZoneInfoId;
        CompanySize = companySize;
        SubscriptionPlanId = subscriptionPlanId;
        Lmref = lmref;
        HeardFrom = heardFrom;
        PromotionCode = promotionCode;
        WebClientUUID = webClientUuid;
        Referral = referral;
        CompanyType = companyType;
        CommunicationTools = communicationTools;
        IsAgreeMarketingConsent = isAgreeMarketingConsent;
        Location = location;
        ConnectionStrategy = connectionStrategy;
    }
}

public class InviteUserInputObject
{
    [JsonProperty("tenanthub_user_id")]
    public string? TenantHubUserId { get; set; }

    [JsonProperty("sleekflow_user_id")]
    public string SleekflowUserId { get; set; }

    [JsonProperty("user_name")]
    public string? UserName { get; set; }

    [Required]
    [JsonProperty("email")]
    public string Email { get; set; }

    [JsonProperty("last_name")]
    public string Lastname { get; set; }

    [JsonProperty("first_name")]
    public string Firstname { get; set; }

    [Required]
    [JsonProperty("user_role")]
    public StaffUserRole UserRole { get; set; }

    [JsonProperty("position")]
    public string Position { get; set; }

    [JsonProperty("time_zone_info_id")]
    public string TimeZoneInfoId { get; set; }

    [Obsolete("This will cause invited user cannot to assign team")]
    public List<long> TeamIds { get; set; }

    [JsonConstructor]
    public InviteUserInputObject(
        string? tenantHubUserId,
        string sleekflowUserId,
        string? name,
        string email,
        string lastname,
        string firstname,
        StaffUserRole role,
        string position,
        string timeZoneInfoId,
        List<long> teamIds)
    {
        TenantHubUserId = tenantHubUserId;
        SleekflowUserId = sleekflowUserId;
        UserName = name;
        Email = email;
        Lastname = lastname;
        Firstname = firstname;
        UserRole = role;
        Position = position;
        TimeZoneInfoId = timeZoneInfoId;
        TeamIds = teamIds;
    }

}



