﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Sleekflow.Apis.CrmHub.Api;
using Sleekflow.Apis.CrmHub.Model;
using Travis_backend.Cache;
using Travis_backend.Cache.Models.CacheKeyPatterns;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.CompanyDomain.Services;
using Travis_backend.Constants;
using Travis_backend.ContactDomain.Models;
using Travis_backend.ContactDomain.Services;
using Travis_backend.ContactDomain.ViewModels;
using Travis_backend.Database;
using Travis_backend.Enums;
using Travis_backend.Exceptions;
using Travis_backend.Helpers;
using Travis_backend.SleekflowCrmHubDomain.Models;

namespace Travis_backend.SleekflowCrmHubDomain.Services;

public interface ICrmHubService
{
    Task<List<UserProfile>> GetRelatedUserProfilesAsync(string companyId, string crmHubEntityId);

    Task<bool> HasRelatedUserProfilesAsync(string companyId, string crmHubEntityId);

    Task<List<UserProfile>> AddUserProfilesByContactObjectDictAsync(
        string companyId,
        IReadOnlyDictionary<string, object> contactObjectDict,
        string crmHubEntityId);

    Task<List<UserProfile>> UpdateUserProfilesByContactObjectDictAsync(
        string companyId,
        IReadOnlyCollection<EntityEventPropertyChangeEntry> changeEntries,
        string crmHubEntityId);

    Task<List<UserProfile>> AddOnlyNewUserProfilesByContactObjectDictAsync(
        string companyId,
        IReadOnlyDictionary<string, object> contactObjectDict,
        string crmHubEntityId);

    Task RefreshCrmHubConfigAsync(string companyId, Staff staff);

    Task<(UsageLimit UsageLimit, FeatureAccessibilitySettings FeatureAccessibilitySettings)>
        GetCrmHubCompanySettingsAsync(string companyId);
}

public class CrmHubService : ICrmHubService
{
    private readonly IUserProfileService _userProfileService;
    private readonly ApplicationDbContext _appDbContext;
    private readonly ILogger<CrmHubService> _logger;
    private readonly ICacheManagerService _cacheManagerService;
    private readonly ILockService _lockService;
    private readonly IObjectsApi _objectsApi;
    private readonly ICrmHubConfigsApi _crmHubConfigsApi;
    private readonly ICompanySubscriptionService _companySubscriptionService;

    public CrmHubService(
        IUserProfileService userProfileService,
        ApplicationDbContext appDbContext,
        ILogger<CrmHubService> logger,
        ICacheManagerService cacheManagerService,
        ILockService lockService,
        IObjectsApi objectApi,
        ICrmHubConfigsApi crmHubConfigsApi,
        ICompanySubscriptionService companySubscriptionService)
    {
        _userProfileService = userProfileService;
        _appDbContext = appDbContext;
        _logger = logger;
        _cacheManagerService = cacheManagerService;
        _lockService = lockService;
        _objectsApi = objectApi;
        _crmHubConfigsApi = crmHubConfigsApi;
        _companySubscriptionService = companySubscriptionService;
    }

    public async Task<List<UserProfile>> GetRelatedUserProfilesAsync(string companyId, string crmHubEntityId)
    {
        var userProfiles = await _userProfileService.GetUserProfilesByCrmHubEntityIdAsync(companyId, crmHubEntityId);
        if (userProfiles == null || userProfiles.Count == 0)
        {
            return new List<UserProfile>();
        }

        return userProfiles;
    }

    public Task<bool> HasRelatedUserProfilesAsync(string companyId, string crmHubEntityId)
    {
        return _userProfileService.HasUserProfilesByCrmHubEntityIdAsync(companyId, crmHubEntityId);
    }

    public async Task<List<UserProfile>> AddUserProfilesByContactObjectDictAsync(
        string companyId,
        IReadOnlyDictionary<string, object> contactObjectDict,
        string crmHubEntityId)
    {
        var phoneNumberStr = ((string) contactObjectDict.GetValueOrDefault("sys_resolved_phone_number"))?
            .Replace("+", string.Empty);
        var emailStr = (string) contactObjectDict.GetValueOrDefault("sys_resolved_email");

        try
        {
            var phoneNumberUtil = PhoneNumbers.PhoneNumberUtil.GetInstance();
            var phoneNumber = phoneNumberUtil.Parse($"+{phoneNumberStr}", null);
            var countryCode = PhoneNumberHelper.GetCountryCode(phoneNumber);
        }
        catch (Exception ex)
        {
            throw new InvalidPhoneNumberException(phoneNumberStr);
        }

        if (phoneNumberStr == null && emailStr == null)
        {
            throw new UnableToResolveIdentityException(phoneNumberStr, emailStr, crmHubEntityId);
        }

        var addCustomFieldsViewModels = await GetAddCustomFieldsViewModels(companyId, contactObjectDict);
        addCustomFieldsViewModels.Add(
            new AddCustomFieldsViewModel()
            {
                CustomFieldName = "PhoneNumber", CustomValue = phoneNumberStr
            });

        var newProfileViewModel = new NewProfileViewModel
        {
            FirstName = (string) contactObjectDict.GetValueOrDefault("unified:FirstName"),
            LastName = (string) contactObjectDict.GetValueOrDefault("unified:LastName"),
            PhoneNumber = phoneNumberStr,
            WhatsAppPhoneNumber = phoneNumberStr,
            Email = emailStr,
            UserProfileFields = addCustomFieldsViewModels,
            Labels = null,
            AddLabels = new List<string>(),
            RemoveLabels = null,
            ListIds = null
        };

        // Assign contact owner to user profile based on owner from third-party CRM.
        // Only supported single CRM integration.
        Staff owner = null;
        GetObjectsOutputOutput getObjectsOutputOutput = null;

        if (contactObjectDict.TryGetValue("tmp:HubSpot Owner ID", out var ownerId))
        {
            getObjectsOutputOutput = await _objectsApi.ObjectsGetObjectsPostAsync(
                getObjectsInput: new GetObjectsInput(
                    null!,
                    companyId,
                    "User",
                    1,
                    new List<GetObjectsInputFilterGroup>
                    {
                        new GetObjectsInputFilterGroup(
                            new List<Filter>()
                            {
                                new Filter("unified:HubspotIntegratorId", _operator: "=", value: ownerId)
                            })
                    },
                    new List<Sort>()));
        }
        else if (contactObjectDict.TryGetValue("tmp:Salesforce Owner ID", out ownerId))
        {
            getObjectsOutputOutput = await _objectsApi.ObjectsGetObjectsPostAsync(
                getObjectsInput: new GetObjectsInput(
                    null!,
                    companyId,
                    "User",
                    1,
                    new List<GetObjectsInputFilterGroup>
                    {
                        new GetObjectsInputFilterGroup(
                            new List<Filter>()
                            {
                                new Filter("unified:SalesforceIntegratorId", _operator: "=", value: ownerId)
                            })
                    },
                    new List<Sort>()));
        }

        var record = getObjectsOutputOutput?.Data?.Records.FirstOrDefault();
        if (record is not null && record.TryGetValue("sleekflow:Id", out var sleekflowUserId))
        {
            owner = await _appDbContext.UserRoleStaffs
                .Where(
                    x => x.CompanyId == companyId &&
                    x.Id != 1 &&
                    x.IdentityId == (string) sleekflowUserId)
                .FirstOrDefaultAsync();
        }

        List<UserProfile> userProfiles;
        try
        {
            userProfiles = await _userProfileService.AddOrUpdateUserProfile(
                companyId,
                newProfileViewModel,
                staffId: owner?.IdentityId);
        }
        catch (FormatException formatException)
        {
            _logger.LogError(
                formatException,
                "Invalid phone number format. objectDict=[{ObjectDict}].",
                JsonConvert.SerializeObject(contactObjectDict));

            if (Guid.TryParse(formatException.Message, out var userProfileId))
            {
                var userProfileIdStr = userProfileId.ToString();

                userProfiles = await _appDbContext.UserProfiles
                    .Include(x => x.CustomFields)
                    .Include(x => x.InstagramUser)
                    .Include(x => x.FacebookAccount)
                    .Include(x => x.WhatsAppAccount)
                    .Include(x => x.EmailAddress)
                    .Include(x => x.RegisteredUser.Identity)
                    .Include(x => x.UserDevice)
                    .Include(x => x.WebClient)
                    .Include(x => x.WeChatUser)
                    .Include(x => x.LineUser)
                    .Include(x => x.SMSUser)
                    .Include(x => x.WhatsApp360DialogUser)
                    .Include(x => x.WhatsappCloudApiUser)
                    .Include(x => x.TelegramUser)
                    .Include(x => x.ViberUser)
                    .Where(x => x.CompanyId == companyId && x.Id == userProfileIdStr)
                    .ToListAsync();
            }
            else
            {
                userProfiles = new List<UserProfile>();
            }
        }

        foreach (var userProfile in userProfiles)
        {
            var exists = await _appDbContext
                .CrmHubEntities
                .AnyAsync(e => e.EntityId == crmHubEntityId && e.UserProfileId == userProfile.Id);
            if (!exists)
            {
                _appDbContext.CrmHubEntities.Add(
                    new CrmHubEntity
                    {
                        EntityId = crmHubEntityId, UserProfileId = userProfile.Id
                    });
            }
        }

        await _appDbContext.SaveChangesAsync();

        return userProfiles;
    }

    public async Task<List<UserProfile>> UpdateUserProfilesByContactObjectDictAsync(
        string companyId,
        IReadOnlyCollection<EntityEventPropertyChangeEntry> changeEntries,
        string crmHubEntityId)
    {
        var userProfiles = await GetRelatedUserProfilesAsync(companyId, crmHubEntityId);
        if (userProfiles == null || userProfiles.Count == 0)
        {
            return new List<UserProfile>();
        }

        var addCustomFieldsViewModels = await GetAddCustomFieldsViewModels(
            companyId,
            changeEntries.Select(ce => KeyValuePair.Create(ce.Name, ce.ToValue)).ToList());
        if (addCustomFieldsViewModels.Any() == false)
        {
            return userProfiles;
        }

        foreach (var userProfile in userProfiles)
        {
            await _userProfileService.UpdateUserProfileCustomFields(
                companyId,
                userProfile.Id,
                staffId: null,
                addCustomFieldsViewModels: addCustomFieldsViewModels);
        }

        return userProfiles;
    }

    private async Task<List<AddCustomFieldsViewModel>> GetAddCustomFieldsViewModels(
        string companyId,
        IReadOnlyCollection<KeyValuePair<string, object>> changeEntries)
    {
        if (changeEntries == null || changeEntries.Any() == false)
        {
            return new List<AddCustomFieldsViewModel>();
        }

        if (changeEntries.Any(ce => ce.Key == "tmp:Dynamics365 Last Order Number") ||
            changeEntries.Any(ce => ce.Key == "tmp:Dynamics365 Last Order Amount") ||
            changeEntries.Any(ce => ce.Key == "tmp:Dynamics365 Last Order Currency") ||
            changeEntries.Any(ce => ce.Key == "tmp:Dynamics365 Total Number Of Orders") ||
            changeEntries.Any(ce => ce.Key == "tmp:Dynamics365 Total Number Of Products") ||
            changeEntries.Any(ce => ce.Key == "tmp:Dynamics365 Preferred Language") ||
            changeEntries.Any(ce => ce.Key == "tmp:Dynamics365 Salesperson") ||
            changeEntries.Any(ce => ce.Key == "tmp:Dynamics365 Awareness Source") ||
            changeEntries.Any(ce => ce.Key == "tmp:Dynamics365 Boutique Visited") ||
            changeEntries.Any(ce => ce.Key == "tmp:Dynamics365 12M Total Orders"))
        {
            await InitDynamics365CommerceFieldsAsync(companyId);
        }

        if (changeEntries.Any(ce => ce.Key == "tmp:Salesforce Owner"))
        {
            await InitSalesforceFieldsAsync(companyId);
        }

        if (changeEntries.Any(ce => ce.Key == "tmp:HubSpot Owner"))
        {
            await InitHubSpotFieldsAsync(companyId);
        }

        var companyCustomFields = await _appDbContext.CompanyCustomUserProfileFields
            .Where(x => x.CompanyId == companyId)
            .ToListAsync();
        var customFieldNameToCustomFieldDict = companyCustomFields
            .GroupBy(cf => cf.FieldName, StringComparer.InvariantCulture)
            .ToDictionary(cf => cf.Key, cf => cf.First());

        var addCustomFieldsViewModels = new List<AddCustomFieldsViewModel>();

        // Sync unified fields to the matched custom fields
        foreach (var changeEntry in changeEntries.Where(p => p.Key.StartsWith("unified:")))
        {
            var fieldName = changeEntry.Key.Replace("unified:", string.Empty);
            var value = changeEntry.Value?.ToString();

            if (fieldName == "LastName")
            {
                addCustomFieldsViewModels.Add(
                    new AddCustomFieldsViewModel
                    {
                        CustomFieldName = "LastName", CustomValue = value,
                    });

                continue;
            }

            if (fieldName == "FirstName")
            {
                addCustomFieldsViewModels.Add(
                    new AddCustomFieldsViewModel
                    {
                        CustomFieldName = "FirstName", CustomValue = value,
                    });

                continue;
            }

            if (fieldName == "PhoneNumber")
            {
                // Skip it because we only relied on the sys_resolved_phone_number
                continue;
            }

            if (fieldName == "Email")
            {
                if (value != null)
                {
                    addCustomFieldsViewModels.Add(
                        new AddCustomFieldsViewModel
                        {
                            CustomFieldName = "Email", CustomValue = value,
                        });
                }

                continue;
            }

            // Only matched unified field will be synced
            if (!customFieldNameToCustomFieldDict.ContainsKey(fieldName))
            {
                continue;
            }

            var companyCustomField = customFieldNameToCustomFieldDict[fieldName];

            addCustomFieldsViewModels.Add(
                new AddCustomFieldsViewModel
                {
                    CustomFieldId = companyCustomField.Id.ToString(),
                    CustomFieldName = companyCustomField.FieldName,
                    CustomValue = value,
                });
        }

        // Sync tmp fields to the matched custom fields
        foreach (var changeEntry in changeEntries.Where(p => p.Key.StartsWith("tmp:")))
        {
            var fieldName = changeEntry.Key.Replace("tmp:", string.Empty);
            var value = changeEntry.Value?.ToString();

            // Only matched tmp field will be synced
            if (!customFieldNameToCustomFieldDict.ContainsKey(fieldName))
            {
                continue;
            }

            var companyCustomField = customFieldNameToCustomFieldDict[fieldName];

            addCustomFieldsViewModels.Add(
                new AddCustomFieldsViewModel
                {
                    CustomFieldId = companyCustomField.Id.ToString(),
                    CustomFieldName = companyCustomField.FieldName,
                    CustomValue = value,
                });
        }

        return addCustomFieldsViewModels;
    }

    private async Task InitDynamics365CommerceFieldsAsync(string companyId)
    {
        var cacheKey = $"{nameof(CrmHubService)}-{nameof(InitDynamics365CommerceFieldsAsync)}-{companyId}";

        var crmHubServiceCacheKeyPattern = new CrmHubServiceCacheKeyPattern(
            nameof(CrmHubService),
            nameof(InitDynamics365CommerceFieldsAsync),
            companyId);

        var isInit = await _cacheManagerService.GetCacheAsync(crmHubServiceCacheKeyPattern);
        if (isInit == "true")
        {
            return;
        }

        var @lock = await _lockService.WaitUntilLockAcquiredAsync($"{cacheKey}-lock", TimeSpan.FromMinutes(5));
        try
        {
            await InitField(
                companyId,
                "Dynamics365 Last Order Number",
                displayNameEn: "D365 Last Order Number",
                displayNameHk: "D365 最近的訂購號碼",
                order: 100,
                FieldDataType.SingleLineText);
            await InitField(
                companyId,
                "Dynamics365 Last Order Amount",
                displayNameEn: "D365 Last Order Amount",
                displayNameHk: "D365 最近的訂貨金額",
                order: 101,
                FieldDataType.Number);
            await InitField(
                companyId,
                "Dynamics365 Last Order Currency",
                displayNameEn: "D365 Last Order Currency",
                displayNameHk: "D365 最近的訂貨貨幣",
                order: 102,
                FieldDataType.SingleLineText);
            await InitField(
                companyId,
                "Dynamics365 Total Number Of Orders",
                displayNameEn: "D365 Total Number Of Orders",
                displayNameHk: "D365 過往訂單總數",
                order: 103,
                FieldDataType.Number);
            await InitField(
                companyId,
                "Dynamics365 Total Number Of Products",
                displayNameEn: "D365 Total Number Of Products",
                displayNameHk: "D365 過往產品總數",
                order: 104,
                FieldDataType.Number);
            await InitField(
                companyId,
                "Dynamics365 Preferred Language",
                displayNameEn: "D365 Preferred Language",
                displayNameHk: "D365 偏好語言",
                order: 105,
                FieldDataType.SingleLineText);
            await InitField(
                companyId,
                "Dynamics365 Salesperson",
                displayNameEn: "D365 Salesperson",
                displayNameHk: "D365 銷售負責人",
                order: 106,
                FieldDataType.SingleLineText);
            await InitField(
                companyId,
                "Dynamics365 Awareness Source",
                displayNameEn: "D365 How Do You Know Us",
                displayNameHk: "D365 認識品牌途徑",
                order: 107,
                FieldDataType.SingleLineText);
            await InitField(
                companyId,
                "Dynamics365 Boutique Visited",
                displayNameEn: "D365 Boutique Visited",
                displayNameHk: "D365 Boutique Visited",
                order: 108,
                FieldDataType.Number);
            await InitField(
                companyId,
                "Dynamics365 12M Total Orders",
                displayNameEn: "D365 12M Total Orders",
                displayNameHk: "D365 12M Total Orders",
                order: 109,
                FieldDataType.Number);
        }
        finally
        {
            await _lockService.ReleaseLockAsync(@lock);
        }

        await _cacheManagerService.SaveCacheAsync(
            crmHubServiceCacheKeyPattern,
            "true");
    }

    private async Task InitSalesforceFieldsAsync(string companyId)
    {
        var cacheKey = $"{nameof(CrmHubService)}-{nameof(InitSalesforceFieldsAsync)}-{companyId}";

        var crmHubServiceCacheKeyPattern = new CrmHubServiceCacheKeyPattern(
            nameof(CrmHubService),
            nameof(InitSalesforceFieldsAsync),
            companyId);

        var isInit = await _cacheManagerService.GetCacheAsync(crmHubServiceCacheKeyPattern);
        if (isInit == "true")
        {
            return;
        }

        var @lock = await _lockService.WaitUntilLockAcquiredAsync($"{cacheKey}-lock", TimeSpan.FromMinutes(5));
        try
        {
            await InitField(
                companyId,
                "Salesforce Owner",
                displayNameEn: "Salesforce Owner",
                displayNameHk: "Salesforce Owner",
                order: 200,
                FieldDataType.SingleLineText);
            await InitField(
                companyId,
                "Salesforce Owner ID",
                displayNameEn: "Salesforce Owner ID",
                displayNameHk: "Salesforce Owner ID",
                order: 201,
                FieldDataType.SingleLineText);
        }
        finally
        {
            await _lockService.ReleaseLockAsync(@lock);
        }

        await _cacheManagerService.SaveCacheAsync(
            crmHubServiceCacheKeyPattern,
            "true");
    }

    private async Task InitHubSpotFieldsAsync(string companyId)
    {
        var cacheKey = $"{nameof(CrmHubService)}-{nameof(InitHubSpotFieldsAsync)}-{companyId}";

        var crmHubServiceCacheKeyPattern = new CrmHubServiceCacheKeyPattern(
            nameof(CrmHubService),
            nameof(InitHubSpotFieldsAsync),
            companyId);

        var isInit = await _cacheManagerService.GetCacheAsync(crmHubServiceCacheKeyPattern);
        if (isInit == "true")
        {
            return;
        }

        var @lock = await _lockService.WaitUntilLockAcquiredAsync($"{cacheKey}-lock", TimeSpan.FromMinutes(5));
        try
        {
            await InitField(
                companyId,
                "HubSpot Owner",
                displayNameEn: "HubSpot Owner",
                displayNameHk: "HubSpot Owner",
                order: 300,
                FieldDataType.SingleLineText);
            await InitField(
                companyId,
                "HubSpot Owner ID",
                displayNameEn: "HubSpot Owner ID",
                displayNameHk: "HubSpot Owner ID",
                order: 301,
                FieldDataType.SingleLineText);
        }
        finally
        {
            await _lockService.ReleaseLockAsync(@lock);
        }

        await _cacheManagerService.SaveCacheAsync(
            crmHubServiceCacheKeyPattern,
            "true");
    }

    private async Task InitField(
        string companyId,
        string fieldName,
        string displayNameEn,
        string displayNameHk,
        int order,
        FieldDataType fieldDataType)
    {
        var existingField = await _appDbContext.CompanyCustomUserProfileFields
            .Where(x => x.CompanyId == companyId && x.FieldName == fieldName)
            .Include(x => x.CustomUserProfileFieldLinguals).FirstOrDefaultAsync();
        if (existingField == null)
        {
            var newTeamField = new CompanyCustomUserProfileField
            {
                CompanyId = companyId,
                FieldName = fieldName,
                Type = fieldDataType,
                Order = order,
                CustomUserProfileFieldLinguals = new List<CustomUserProfileFieldLingual>()
                {
                    new CustomUserProfileFieldLingual
                    {
                        DisplayName = displayNameEn, Language = "en"
                    },
                    new CustomUserProfileFieldLingual
                    {
                        DisplayName = displayNameHk, Language = "zh-hk"
                    }
                },
                IsDeletable = false,
                IsEditable = false,
                IsVisible = true,
                IsDefault = true,
                FieldsCategory = FieldsCategory.Dynamics365
            };
            _appDbContext.CompanyCustomUserProfileFields.Add(newTeamField);
            await _appDbContext.SaveChangesAsync();
        }
        else
        {
            existingField.Order = order;
            existingField.FieldName = fieldName;
            existingField.FieldsCategory = FieldsCategory.Dynamics365;
            try
            {
                var enFieldLingual =
                    existingField.CustomUserProfileFieldLinguals.FirstOrDefault(x => x.Language == "en");
                if (enFieldLingual != null)
                {
                    enFieldLingual.DisplayName = displayNameEn;
                }

                var hkFieldLingual =
                    existingField.CustomUserProfileFieldLinguals.FirstOrDefault(x => x.Language == "zh-hk");
                if (hkFieldLingual != null)
                {
                    hkFieldLingual.DisplayName = displayNameHk;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[{MethodName}] Company {CompanyId} error for field data type {FieldDataType} of name: {FieldName}, " +
                    "EN display name: {EnDisplayName}, ZH display name: {ZhDisplayName}. {ExceptionMessage}",
                    nameof(InitField),
                    companyId,
                    fieldDataType.ToString(),
                    fieldName,
                    displayNameEn,
                    displayNameHk,
                    ex.Message);
            }

            await _appDbContext.SaveChangesAsync();
        }
    }

    public async Task<List<UserProfile>> AddOnlyNewUserProfilesByContactObjectDictAsync(
        string companyId,
        IReadOnlyDictionary<string, object> contactObjectDict,
        string crmHubEntityId)
    {
        var phoneNumberStr = ((string) contactObjectDict.GetValueOrDefault("sys_resolved_phone_number"))?
            .Replace("+", string.Empty);
        var emailStr = (string) contactObjectDict.GetValueOrDefault("sys_resolved_email");

        try
        {
            var phoneNumberUtil = PhoneNumbers.PhoneNumberUtil.GetInstance();
            var phoneNumber = phoneNumberUtil.Parse($"+{phoneNumberStr}", null);
            var countryCode = PhoneNumberHelper.GetCountryCode(phoneNumber);
        }
        catch (Exception ex)
        {
            throw new InvalidPhoneNumberException(phoneNumberStr);
        }

        if (phoneNumberStr == null && emailStr == null)
        {
            throw new UnableToResolveIdentityException(phoneNumberStr, emailStr, crmHubEntityId);
        }

        var addCustomFieldsViewModels = await GetAddCustomFieldsViewModels(companyId, contactObjectDict);
        addCustomFieldsViewModels.Add(
            new AddCustomFieldsViewModel()
            {
                CustomFieldName = "PhoneNumber", CustomValue = phoneNumberStr
            });

        var newProfileViewModel = new NewProfileViewModel
        {
            FirstName = (string) contactObjectDict.GetValueOrDefault("unified:FirstName"),
            LastName = (string) contactObjectDict.GetValueOrDefault("unified:LastName"),
            PhoneNumber = phoneNumberStr,
            WhatsAppPhoneNumber = phoneNumberStr,
            Email = emailStr,
            UserProfileFields = addCustomFieldsViewModels,
            Labels = null,
            AddLabels = new List<string>(),
            RemoveLabels = null,
            ListIds = null
        };

        List<UserProfile> userProfiles;
        try
        {
            userProfiles = await _userProfileService.AddNewUserProfileOnly(
                companyId,
                newProfileViewModel);
        }
        catch (FormatException formatException)
        {
            if (Guid.TryParse(formatException.Message, out var userProfileId))
            {
                var userProfileIdStr = userProfileId.ToString();

                userProfiles = await _appDbContext.UserProfiles
                    .Include(x => x.CustomFields)
                    .Include(x => x.InstagramUser)
                    .Include(x => x.FacebookAccount)
                    .Include(x => x.WhatsAppAccount)
                    .Include(x => x.EmailAddress)
                    .Include(x => x.RegisteredUser.Identity)
                    .Include(x => x.UserDevice)
                    .Include(x => x.WebClient)
                    .Include(x => x.WeChatUser)
                    .Include(x => x.LineUser)
                    .Include(x => x.SMSUser)
                    .Include(x => x.WhatsApp360DialogUser)
                    .Include(x => x.WhatsappCloudApiUser)
                    .Include(x => x.TelegramUser)
                    .Include(x => x.ViberUser)
                    .Where(x => x.CompanyId == companyId && x.Id == userProfileIdStr)
                    .ToListAsync();
            }
            else
            {
                userProfiles = new List<UserProfile>();
            }
        }

        foreach (var userProfile in userProfiles)
        {
            var exists = await _appDbContext
                .CrmHubEntities
                .AnyAsync(e => e.EntityId == crmHubEntityId && e.UserProfileId == userProfile.Id);
            if (!exists)
            {
                _appDbContext.CrmHubEntities.Add(
                    new CrmHubEntity
                    {
                        EntityId = crmHubEntityId, UserProfileId = userProfile.Id
                    });
            }
        }

        await _appDbContext.SaveChangesAsync();

        return userProfiles;
    }

    public async Task RefreshCrmHubConfigAsync(string companyId, Staff staff)
    {
        try
        {
            var (usageLimit, featureAccessibilitySettings) = await GetCrmHubCompanySettingsAsync(companyId);

            var getCrmHubConfigOutputOutput =
                await _crmHubConfigsApi.CrmHubConfigsGetCrmHubConfigPostAsync(
                    getCrmHubConfigInput: new GetCrmHubConfigInput(companyId));

            var crmHubConfig = getCrmHubConfigOutputOutput.Data.CrmHubConfig;

            // if not initialized
            if (crmHubConfig.Id == "NOT_INITIALIZED")
            {
                await _crmHubConfigsApi.CrmHubConfigsInitializeCrmHubConfigPostAsync(
                    initializeCrmHubConfigInput: new InitializeCrmHubConfigInput(
                        companyId,
                        usageLimit,
                        featureAccessibilitySettings,
                        staff.Id.ToString()));
            }

            // else, update
            await _crmHubConfigsApi.CrmHubConfigsUpdateCrmHubConfigPostAsync(
                updateCrmHubConfigInput: new UpdateCrmHubConfigInput(
                    crmHubConfig.Id,
                    companyId,
                    usageLimit,
                    featureAccessibilitySettings,
                    staff.Id.ToString()));
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Errors when refreshing Crm Hub Config: {CompanyId}", companyId);
        }
    }

    public async Task<(UsageLimit UsageLimit, FeatureAccessibilitySettings FeatureAccessibilitySettings)>
        GetCrmHubCompanySettingsAsync(string companyId)
    {
        var subscriptionTier =
            await _companySubscriptionService.GetCompanySubscriptionTierAsync(
                companyId);

        var usageLimit = subscriptionTier != null
            ? CrmHubLimitConstants.UsageLimits[subscriptionTier.Value]
            : new UsageLimit(0, 0, 0, 0, 0);

        var featureAccessibilitySettings = subscriptionTier != null
            ? CrmHubLimitConstants.FeatureAccessibilitySettings[subscriptionTier.Value]
            : new FeatureAccessibilitySettings();

        return (usageLimit, featureAccessibilitySettings);
    }
}