using System;
using System.Collections.Generic;
using System.Linq;
using GraphApi.Client.Const.WhatsappCloudApi;
using Newtonsoft.Json;
using Sleekflow.Apis.MessagingHub.Model;
using Travis_backend.Extensions;
using Travis_backend.MessageDomain.Models;
using ComponentType = WABA360Dialog.ApiClient.Payloads.Enums.ComponentType;
using TemplateMessageComponentObject =
    WABA360Dialog.ApiClient.Payloads.Models.MessageObjects.InteractiveObjects.TemplateMessageComponentObject;

namespace Travis_backend.Helpers;

public static class ExtendedMessageHelper
{
    private const string Salt =
        "sTXu0a0db3ZYlKTssCNrOvHyMgJcvfsX+zIQPOFMpRE+rbWx6cWXT67TaeWPETKs27aLNcT7J+4F83L6Iqh15Q==";

    public static string GenerateHashCodeForFile(string fileId)
    {
        return SHA256Helper.sha256_hash($"{fileId}{Salt}");
    }

    public static bool CheckHashCodeIsValid(string fileId, string hashCode)
    {
        if (string.IsNullOrWhiteSpace(fileId) || string.IsNullOrWhiteSpace(hashCode))
        {
            return false;
        }

        var validCode = GenerateHashCodeForFile(fileId);

        return hashCode == validCode;
    }

    public const long FbIgAllowedImageByteSize = 8388608;

    public static List<string> FbIgAllowedImageContentTypes => new ()
    {
        "image/jpeg",
        "image/png",
        "image/gif",
        "image/apng",
        "image/avif",
        "image/svg+xml",
        "image/webp"
    };

    public const long FbAllowedFileByteSize = 26214400;

    public static List<string> FbAllowedAudioContentTypes => new ()
    {
        "audio/aac",
        "audio/mp3",
        "audio/amr",
        "audio/mpeg",
        "audio/ogg; codecs=opus"
    };

    public static List<string> FbAllowedVideoContentTypes => new ()
    {
        "video/mp4", "video/3gpp", "video/MPV", "video/AV1"
    };

    public static List<string> FbAllowedDocumentContentTypes => new ()
    {
        "application/pdf",
        "application/msword",
        "application/vnd.ms-excel",
        "application/vnd.ms-powerpoint",
        "text/csv",
        "text/plain"
    };

    public static ExtendedMessagePayloadDetail Format(
        this ExtendedMessagePayloadDetail extendedMessagePayloadDetail,
        List<string> parameters)
    {
        if (extendedMessagePayloadDetail == null)
        {
            return null;
        }

        var extendedMessagePayloadWithUserProfileParams = JsonConvert.SerializeObject(
            extendedMessagePayloadDetail,
            new JsonSerializerSettings
            {
                NullValueHandling = NullValueHandling.Ignore
            });
        extendedMessagePayloadWithUserProfileParams =
            extendedMessagePayloadWithUserProfileParams.SafeFormat(parameters.Select(x => x.ToString()).ToArray());

        return JsonConvert.DeserializeObject<ExtendedMessagePayloadDetail>(extendedMessagePayloadWithUserProfileParams);
    }

    public static string FormatParamToBodyText(
        this string messageBody,
        List<TemplateMessageComponentObject> components = null,
        List<string> parameters = null)
    {
        if (messageBody == null)
        {
            return messageBody;
        }

        if (components == null && parameters == null)
        {
            return messageBody;
        }

        if (components != null && components.Any())
        {
            var bodyComponentObject = components.FirstOrDefault(x => x.Type == ComponentType.body);

            if (bodyComponentObject == null)
            {
                return messageBody;
            }

            if (bodyComponentObject.Parameters == null)
            {
                return messageBody;
            }

            var bodyParams = bodyComponentObject.Parameters.ToArray();

            for (var i = 0; i < bodyParams.Length; i++)
            {
                messageBody = messageBody.Replace("{{" + (i + 1) + "}}", bodyParams[i].Text);
            }
        }
        else if (parameters != null && parameters.Any())
        {
            for (var i = 0; i < parameters.Count; i++)
            {
                messageBody = messageBody.Replace($"{{{i}}}", parameters[i]);
            }
        }

        return messageBody;
    }

    public static string FormatWhatsappCloudApiTemplateParamToBodyText(
        this string messageBody,
        List<WhatsappCloudApiTemplateMessageComponentObject> components = null)
    {
        if (messageBody == null || components == null)
        {
            return messageBody;
        }

        if (components.Any())
        {
            var bodyComponentObject = components.FirstOrDefault(x => string.Equals(x.Type, WhatsappCloudApiComponentTypeConst.body, StringComparison.OrdinalIgnoreCase));

            if (bodyComponentObject == null)
            {
                return messageBody;
            }

            if (bodyComponentObject.Parameters == null || bodyComponentObject.Parameters.Count == 0)
            {
                return messageBody;
            }

            var bodyParams = bodyComponentObject.Parameters.ToArray();

            for (var i = 0; i < bodyParams.Length; i++)
            {
                messageBody = messageBody.Replace("{{" + (i + 1) + "}}", bodyParams[i].Text);
            }
        }

        return messageBody;
    }
}