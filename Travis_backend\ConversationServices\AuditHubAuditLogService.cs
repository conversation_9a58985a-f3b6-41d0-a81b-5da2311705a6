using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using AutoMapper;
using AutoMapper.QueryableExtensions;
using Hangfire;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Sleekflow.Apis.AuditHub.Api;
using Sleekflow.Apis.AuditHub.Model;
using Travis_backend.BackgroundTaskServices.Attributes;
using Travis_backend.ContactDomain.Constants;
using Travis_backend.ConversationDomain.ViewModels;
using Travis_backend.Database;
using Travis_backend.**********************;
using Travis_backend.FlowHubs.Models;
using Travis_backend.SignalR;

namespace Travis_backend.ConversationServices;

[HangfireJobExpirationTimeout(timeoutInMinutes: 1)]
public interface IAuditHubAuditLogService
{
    Task CreateAutomationTriggeredLogAsync(string companyId, string userProfileId, AutomationTriggeredLogData data);

    Task CreateConversationAssignedTeamChangedLogAsync(
        string companyId,
        string userProfileId,
        string staffId,
        AssignedTeamChangedLogData data);

    Task CreateConversationAssigneeChangedLogAsync(
        string companyId,
        string userProfileId,
        string staffId,
        AssigneeChangedLogData data);

    Task CreateConversationChannelSwitchedLogAsync(
        string companyId,
        string userProfileId,
        string staffId,
        ConversationChannelSwitchedLogData data);

    Task CreateConversationCollaboratorAddedLogAsync(
        string companyId,
        string userProfileId,
        string staffId,
        CollaboratorAddedLogData data);

    Task CreateConversationCollaboratorRemovedLogAsync(
        string companyId,
        string userProfileId,
        string staffId,
        CollaboratorRemovedLogData data);

    Task CreateConversationLabelAddedLogAsync(
        string companyId,
        string userProfileId,
        string staffId,
        LabelAddedLogData data);

    Task CreateConversationLabelRemovedLogAsync(
        string companyId,
        string userProfileId,
        string staffId,
        LabelRemovedLogData data);

    Task CreateConversationReadLogAsync(
        string companyId,
        string userProfileId,
        string staffId,
        ConversationReadLogData data);

    Task CreateConversationStatusChangedLogAsync(
        string companyId,
        string userProfileId,
        string staffId,
        ConversationStatusChangedLogData data);

    Task<RemarkResponse> CreateStaffManualAddedLogAsync(
        string companyId,
        string userProfileId,
        string staffId,
        string logMessage,
        Dictionary<string, object> data);

    Task<UpdateStaffManualAddedLogOutputOutput> UpdateStaffManualAddedLogAsync(
        string auditLogId,
        string companyId,
        string userProfileId,
        string staffId,
        string logMessage,
        Dictionary<string, object> data);

    Task<DeleteStaffManualAddedLogOutputOutput> DeleteStaffManualAddedLogAsync(
        string auditLogId,
        string companyId,
        string userProfileId);

    Task CreateUserProfileAddedToListLogAsync(
        string companyId,
        string userProfileId,
        string staffId,
        UserProfileAddedToListLogData data);

    Task CreateUserProfileChatHistoryBackedUpAsync(
        string companyId,
        string userProfileId,
        string staffId,
        UserProfileAddedToListLogData data);

    Task CreateUserProfileFieldChangedLogAsync(
        string companyId,
        string userProfileId,
        string staffId,
        UserProfileFieldChangedLogData data);

    Task CreateUserProfileRemovedFromListLogAsync(
        string companyId,
        List<string> userProfileIds,
        string staffId,
        UserProfileRemovedFromListLogData data);

    Task<List<RemarkResponse>> GetUserProfileAuditLogsAsync(
        string companyId,
        string userProfileId,
        int offset,
        int limit);

    Task CreateUserProfileDeletedLogAsync(
        string companyId,
        string userProfileId,
        string staffId,
        UserProfileDeletedLogData data);

    Task CreateUserProfileDeletedLogsAsync(
        string companyId,
        List<string> userProfileIds,
        string staffId,
        UserProfileDeletedLogData data);

    Task CreateUserProfileSoftDeletedLogAsync(
        string companyId,
        string userProfileId,
        string staffId,
        UserProfileSoftDeletedLogData data);

    Task CreateUserProfileSoftDeletedLogsAsync(
        string companyId,
        List<string> userProfileIds,
        string staffId,
        UserProfileSoftDeletedLogData data);

    Task CreateUserProfileRecoveredLogAsync(
        string companyId,
        string userProfileId,
        string staffId,
        UserProfileRecoveredLogData data);

    Task CreateUserProfileRecoveredLogsAsync(
        string companyId,
        List<string> userProfileIds,
        string staffId,
        UserProfileRecoveredLogData data);

    Task CreateUserProfileEnrolledIntoFlowHubWorkflowLogAsync(
        string companyId,
        string userProfileId,
        OnUserProfileEnrolledIntoFlowHubWorkflowData data);
}

public class AuditHubAuditLogService : IAuditHubAuditLogService
{
    private readonly ApplicationDbContext _appDbContext;
    private readonly IMapper _mapper;
    private readonly ILogger _logger;
    private readonly IAuditLogsApi _auditLogsApi;
    private readonly ISignalRService _signalRService;
    private readonly IDistributedInvocationContextService _distributedInvocationContextService;

    public AuditHubAuditLogService(
        ApplicationDbContext appDbContext,
        IMapper mapper,
        ILogger<AuditHubAuditLogService> logger,
        IAuditLogsApi auditLogsApi,
        ISignalRService signalRService,
        IDistributedInvocationContextService distributedInvocationContextService)
    {
        _appDbContext = appDbContext;
        _mapper = mapper;
        _logger = logger;
        _auditLogsApi = auditLogsApi;
        _signalRService = signalRService;
        _distributedInvocationContextService = distributedInvocationContextService;
    }

    public Task CreateAutomationTriggeredLogAsync(
        string companyId,
        string userProfileId,
        AutomationTriggeredLogData data)
    {
        var logMessage = $"Triggered Automation: {data.AutomationName}, Action: {data.AutomationActionType}";

        BackgroundJob.Enqueue<IAuditLogsApi>(
            x => x.AuditLogsCreateAutomationTriggeredLogPostAsync(
                null,
                _distributedInvocationContextService.GetSerializedContextHeader(),
                new CreateAutomationTriggeredLogInput(
                    companyId,
                    userProfileId,
                    null,
                    logMessage,
                    data),
                CancellationToken.None));

        return Task.CompletedTask;
    }

    public Task CreateConversationAssignedTeamChangedLogAsync(
        string companyId,
        string userProfileId,
        string staffId,
        AssignedTeamChangedLogData data)
    {
        var logMessage = (data.NewTeam == null)
            ? $"Assigned Team removed"
            : $"Assigned Team changed to {data.NewTeam.Name}";

        BackgroundJob.Enqueue<IAuditLogsApi>(
            x => x.AuditLogsCreateConversationAssignedTeamChangedLogPostAsync(
                null,
                _distributedInvocationContextService.GetSerializedContextHeader(),
                new CreateConversationAssignedTeamChangedLogInput(
                    companyId,
                    userProfileId,
                    staffId,
                    logMessage,
                    data),
                CancellationToken.None));

        return Task.CompletedTask;
    }

    public Task CreateConversationAssigneeChangedLogAsync(
        string companyId,
        string userProfileId,
        string staffId,
        AssigneeChangedLogData data)
    {
        var originalAssignee = (data.OriginalAssignee == null) ? "Unassigned" : data.OriginalAssignee.Name;
        var newAssignee = (data.NewAssignee == null) ? "Unassigned" : data.NewAssignee.Name;

        var logMessage = $"Assignee changed from {originalAssignee} to {newAssignee}";

        BackgroundJob.Enqueue<IAuditLogsApi>(
            x => x.AuditLogsCreateConversationAssigneeChangedLogPostAsync(
                null,
                _distributedInvocationContextService.GetSerializedContextHeader(),
                new CreateConversationAssigneeChangedLogInput(
                    companyId,
                    userProfileId,
                    staffId,
                    logMessage,
                    data),
                CancellationToken.None));

        return Task.CompletedTask;
    }

    public Task CreateConversationChannelSwitchedLogAsync(
        string companyId,
        string userProfileId,
        string staffId,
        ConversationChannelSwitchedLogData data)
    {
        var logMessage = $"Switched channel to {data.ChannelType} - {data.ChannelName} ({data.PhoneNumber})";

        BackgroundJob.Enqueue<IAuditLogsApi>(
            x => x.AuditLogsCreateConversationChannelSwitchedLogPostAsync(
                null,
                _distributedInvocationContextService.GetSerializedContextHeader(),
                new CreateConversationChannelSwitchedLogInput(
                    companyId,
                    userProfileId,
                    staffId,
                    logMessage,
                    data),
                CancellationToken.None));

        return Task.CompletedTask;
    }

    public Task CreateConversationCollaboratorAddedLogAsync(
        string companyId,
        string userProfileId,
        string staffId,
        CollaboratorAddedLogData data)
    {
        var addCollaborators = string.Join(",", data.CollaboratorsAdded.Select(x => x.Name));
        var logMessage = $"Added collaborators: {addCollaborators}";

        BackgroundJob.Enqueue<IAuditLogsApi>(
            x => x.AuditLogsCreateConversationCollaboratorAddedLogPostAsync(
                null,
                _distributedInvocationContextService.GetSerializedContextHeader(),
                new CreateConversationCollaboratorAddedLogInput(
                    companyId,
                    userProfileId,
                    staffId,
                    logMessage,
                    data),
                CancellationToken.None));

        return Task.CompletedTask;
    }

    public Task CreateConversationCollaboratorRemovedLogAsync(
        string companyId,
        string userProfileId,
        string staffId,
        CollaboratorRemovedLogData data)
    {
        var removeCollaborators = string.Join(",", data.CollaboratorsRemoved.Select(x => x.Name));
        var logMessage = $"Removed collaborators: {removeCollaborators}";

        BackgroundJob.Enqueue<IAuditLogsApi>(
            x => x.AuditLogsCreateConversationCollaboratorRemovedLogPostAsync(
                null,
                _distributedInvocationContextService.GetSerializedContextHeader(),
                new CreateConversationCollaboratorRemovedLogInput(
                    companyId,
                    userProfileId,
                    staffId,
                    logMessage,
                    data),
                CancellationToken.None));

        return Task.CompletedTask;
    }

    public Task CreateConversationLabelAddedLogAsync(
        string companyId,
        string userProfileId,
        string staffId,
        LabelAddedLogData data)
    {
        var labelString = string.Join(",\n", data.LabelsAdded.Select(x => $"[{x.Label}]"));
        var logMessage = $"Added labels:\n{labelString}";

        BackgroundJob.Enqueue<IAuditLogsApi>(
            x => x.AuditLogsCreateConversationLabelAddedLogPostAsync(
                null,
                _distributedInvocationContextService.GetSerializedContextHeader(),
                new CreateConversationLabelAddedLogInput(
                    companyId,
                    userProfileId,
                    staffId,
                    logMessage,
                    data),
                CancellationToken.None));

        return Task.CompletedTask;
    }

    public Task CreateConversationLabelRemovedLogAsync(
        string companyId,
        string userProfileId,
        string staffId,
        LabelRemovedLogData data)
    {
        var labelString = string.Join(",\n", data.LabelsRemoved.Select(x => $"[{x.Label}]"));
        var logMessage = $"Removed labels:\n{labelString}";

        BackgroundJob.Enqueue<IAuditLogsApi>(
            x => x.AuditLogsCreateConversationLabelRemovedLogPostAsync(
                null,
                _distributedInvocationContextService.GetSerializedContextHeader(),
                new CreateConversationLabelRemovedLogInput(
                    companyId,
                    userProfileId,
                    staffId,
                    logMessage,
                    data),
                CancellationToken.None));

        return Task.CompletedTask;
    }

    public Task CreateConversationReadLogAsync(
        string companyId,
        string userProfileId,
        string staffId,
        ConversationReadLogData data)
    {
        var logMessage = $"{data.ReadStaff.Name} viewed unread messages";

        BackgroundJob.Enqueue<IAuditLogsApi>(
            x => x.AuditLogsCreateConversationReadLogPostAsync(
                null,
                _distributedInvocationContextService.GetSerializedContextHeader(),
                new CreateConversationReadLogInput(
                    companyId,
                    userProfileId,
                    staffId,
                    logMessage,
                    data),
                CancellationToken.None));

        return Task.CompletedTask;
    }

    public Task CreateConversationStatusChangedLogAsync(
        string companyId,
        string userProfileId,
        string staffId,
        ConversationStatusChangedLogData data)
    {
        var logMessage = $"Conversation status: [{data.OriginalStatus}] to [{data.NewStatus}]";

        // schedule
        if (data.ScheduledTime.HasValue)
        {
            logMessage += $"; Scheduled at {data.ScheduledTime.Value:f} UTC";
        }

        BackgroundJob.Enqueue<IAuditLogsApi>(
            x => x.AuditLogsCreateConversationStatusChangedLogPostAsync(
                null,
                _distributedInvocationContextService.GetSerializedContextHeader(),
                new CreateConversationStatusChangedLogInput(
                    companyId,
                    userProfileId,
                    staffId,
                    logMessage,
                    data),
                CancellationToken.None));

        return Task.CompletedTask;
    }

    public async Task<RemarkResponse> CreateStaffManualAddedLogAsync(
        string companyId,
        string userProfileId,
        string staffId,
        string logMessage,
        Dictionary<string, object> data)
    {
        CreateStaffManualAddedLogOutputOutput createStaffManualAddedLogOutputOutput;
        try
        {
            createStaffManualAddedLogOutputOutput = await _auditLogsApi.AuditLogsCreateStaffManualAddedLogPostAsync(
                null,
                _distributedInvocationContextService.GetSerializedContextHeader(),
                new CreateStaffManualAddedLogInput(
                    companyId,
                    userProfileId,
                    staffId,
                    logMessage,
                    data));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "CreateStaffManualAddedLogAsync");
            throw;
        }

        var staffInfo = await _appDbContext.UserRoleStaffs
            .Where(x => x.CompanyId == companyId && x.IdentityId == staffId)
            .Include(x => x.Identity)
            .ProjectTo<StaffWithoutCompanyResponse>(_mapper.ConfigurationProvider)
            .ToListAsync();

        var remarkResponse = new RemarkResponse()
        {
            RemarkId = createStaffManualAddedLogOutputOutput.Data.Id,
            UserProfileId = userProfileId,
            Remarks = logMessage,
            RemarksStaff = staffInfo.FirstOrDefault(x => x.UserInfo.Id == staffId),
            CreatedAt = DateTime.UtcNow
        };

        await _signalRService.SignalROnRemarksReceived(remarkResponse);

        return remarkResponse;
    }

    public async Task<UpdateStaffManualAddedLogOutputOutput> UpdateStaffManualAddedLogAsync(
        string auditLogId,
        string companyId,
        string userProfileId,
        string staffId,
        string logMessage,
        Dictionary<string, object> data)
    {
        var updateStaffManualAddedLogOutputOutput = await _auditLogsApi.AuditLogsUpdateStaffManualAddedLogPostAsync(
            null,
            _distributedInvocationContextService.GetSerializedContextHeader(),
            new UpdateStaffManualAddedLogInput(
                auditLogId,
                userProfileId,
                logMessage,
                data));

        var dataUserProfileAuditLog = updateStaffManualAddedLogOutputOutput.Data.UserProfileAuditLog;

        var createdByStaffInfo = await GetStaffManualAddedLogStaffInfoAsync(companyId, dataUserProfileAuditLog.SleekflowStaffId);
        var updatedByStaffInfo = await GetStaffManualAddedLogStaffInfoAsync(companyId, staffId);

        var updateRemarkResponse = new UpdateRemarkResponse
        {
            RemarkId = auditLogId,
            UserProfileId = userProfileId,
            Remarks = logMessage,
            RemarksStaff = createdByStaffInfo,
            CreatedAt = dataUserProfileAuditLog.CreatedTime.DateTime,
            UpdatedBy = updatedByStaffInfo,
            UpdatedAt = dataUserProfileAuditLog.UpdatedTime?.DateTime ?? DateTime.UtcNow
        };

        await _signalRService.SignalROnRemarkUpdated(updateRemarkResponse, companyId);

        return updateStaffManualAddedLogOutputOutput;
    }

    public async Task<DeleteStaffManualAddedLogOutputOutput> DeleteStaffManualAddedLogAsync(
        string auditLogId,
        string companyId,
        string userProfileId)
    {
        var deleteStaffManualAddedLogOutputOutput = await _auditLogsApi.AuditLogsDeleteStaffManualAddedLogPostAsync(
            null,
            _distributedInvocationContextService.GetSerializedContextHeader(),
            new DeleteStaffManualAddedLogInput(
                auditLogId,
                userProfileId));

        var deleteRemarkResponse = new DeleteRemarkResponse
        {
            RemarkId = auditLogId,
            UserProfileId = userProfileId
        };

        await _signalRService.SignalROnRemarkDeleted(deleteRemarkResponse, companyId);

        return deleteStaffManualAddedLogOutputOutput;
    }

    public Task CreateUserProfileAddedToListLogAsync(
        string companyId,
        string userProfileId,
        string staffId,
        UserProfileAddedToListLogData data)
    {
        var logMessage = $"Added to list: {data.UserProfileAddedToList.Name}";

        BackgroundJob.Enqueue<IAuditLogsApi>(
            x => x.AuditLogsCreateUserProfileAddedToListLogPostAsync(
                null,
                _distributedInvocationContextService.GetSerializedContextHeader(),
                new CreateUserProfileAddedToListLogInput(
                    companyId,
                    new List<string>
                    {
                        userProfileId
                    },
                    staffId,
                    logMessage,
                    data),
                CancellationToken.None));

        return Task.CompletedTask;
    }

    public Task CreateUserProfileChatHistoryBackedUpAsync(
        string companyId,
        string userProfileId,
        string staffId,
        UserProfileAddedToListLogData data)
    {
        var logMessage = $"The chat history has been backed up successfully and sent to your email.";

        BackgroundJob.Enqueue<IAuditLogsApi>(
            x => x.AuditLogsCreateUserProfileChatHistoryBackedUpLogPostAsync(
                null,
                _distributedInvocationContextService.GetSerializedContextHeader(),
                new CreateUserProfileChatHistoryBackedUpLogInput(
                    companyId,
                    userProfileId,
                    staffId,
                    logMessage,
                    null),
                CancellationToken.None));

        return Task.CompletedTask;
    }

    public Task CreateUserProfileFieldChangedLogAsync(
        string companyId,
        string userProfileId,
        string staffId,
        UserProfileFieldChangedLogData data)
    {
        var fieldChangedLogMessage = data.ChangedFields.Aggregate(
            string.Empty,
            (current, changedField) => current + $"{changedField.FieldName}: new value: [{changedField.Value}]\n");
        var logMessage = $"Profile Information updated:\n{fieldChangedLogMessage}";

        BackgroundJob.Enqueue<IAuditLogsApi>(
            x => x.AuditLogsCreateUserProfileFieldChangedLogPostAsync(
                null,
                _distributedInvocationContextService.GetSerializedContextHeader(),
                new CreateUserProfileFieldChangedLogInput(
                    companyId,
                    userProfileId,
                    staffId,
                    logMessage,
                    data),
                CancellationToken.None));

        return Task.CompletedTask;
    }

    public Task CreateUserProfileRemovedFromListLogAsync(
        string companyId,
        List<string> userProfileIds,
        string staffId,
        UserProfileRemovedFromListLogData data)
    {
        var logMessage = $"Removed from list: {data.UserProfileRemovedFromList.Name}";

        BackgroundJob.Enqueue<IAuditLogsApi>(
            x => x.AuditLogsCreateUserProfileRemovedFromListLogPostAsync(
                null,
                _distributedInvocationContextService.GetSerializedContextHeader(),
                new CreateUserProfileRemovedFromListLogInput(
                    companyId,
                    userProfileIds,
                    staffId,
                    logMessage,
                    data),
                CancellationToken.None));

        return Task.CompletedTask;
    }

    public async Task<List<RemarkResponse>> GetUserProfileAuditLogsAsync(
        string companyId,
        string userProfileId,
        int offset,
        int limit)
    {
        var auditLogs = await _auditLogsApi.AuditLogsGetUserProfileAuditLogsPostAsync(
            null,
            _distributedInvocationContextService.GetSerializedContextHeader(),
            new GetUserProfileAuditLogsInput(
                companyId,
                userProfileId,
                offset,
                limit));

        var staffIds = auditLogs.Data.UserProfileAuditLogs.Select(x => x.SleekflowStaffId).ToList();
        var staffInfo = await _appDbContext.UserRoleStaffs
            .Where(x => x.CompanyId == companyId && staffIds.Contains(x.IdentityId))
            .Include(x => x.Identity)
            .ProjectTo<StaffWithoutCompanyResponse>(_mapper.ConfigurationProvider)
            .ToListAsync();

        return auditLogs.Data.UserProfileAuditLogs.Select(
                auditLog => new RemarkResponse()
                {
                    RemarkId = auditLog.Id,
                    UserProfileId = auditLog.SleekflowUserProfileId,
                    Remarks = auditLog.AuditLogText,
                    RemarksStaff = staffInfo.FirstOrDefault(x => x.UserInfo.Id == auditLog.SleekflowStaffId),
                    CreatedAt = auditLog.CreatedTime.UtcDateTime,
                    Data = auditLog.Data,
                    Type = auditLog.Type
                })
            .ToList();
    }

    public Task CreateUserProfileDeletedLogAsync(
        string companyId,
        string userProfileId,
        string staffId,
        UserProfileDeletedLogData data)
    {
        var logMessage = data.TriggerDetails.TriggerSource switch
        {
            UpdateUserProfileTriggerSource.StaffManual => $"Deleted by {data.TriggerDetails.StaffName}",
            _ => $"Deleted via {data.TriggerDetails.TriggerSource}"
        };

        BackgroundJob.Enqueue<IAuditLogsApi>(
            x => x.AuditLogsCreateUserProfileDeletedLogPostAsync(
                null,
                _distributedInvocationContextService.GetSerializedContextHeader(),
                new CreateUserProfileDeletedLogInput(
                    companyId,
                    userProfileId,
                    staffId,
                    logMessage,
                    data),
                CancellationToken.None));

        return Task.CompletedTask;
    }

    public Task CreateUserProfileDeletedLogsAsync(
        string companyId,
        List<string> userProfileIds,
        string staffId,
        UserProfileDeletedLogData data)
    {
        var logMessage = data.TriggerDetails.TriggerSource switch
        {
            UpdateUserProfileTriggerSource.StaffManual => $"Recovered by {data.TriggerDetails.StaffName}",
            _ => $"Recovered via {data.TriggerDetails.TriggerSource}"
        };

        return _auditLogsApi.AuditLogsCreateUserProfileDeletedLogsPostAsync(
            null,
            _distributedInvocationContextService.GetSerializedContextHeader(),
            new CreateUserProfileDeletedLogsInput(
                companyId,
                userProfileIds,
                staffId,
                logMessage,
                data));
    }

    public Task CreateUserProfileSoftDeletedLogAsync(
        string companyId,
        string userProfileId,
        string staffId,
        UserProfileSoftDeletedLogData data)
    {
        var logMessage = data.TriggerDetails.TriggerSource switch
        {
            UpdateUserProfileTriggerSource.StaffManual => $"Soft deleted by {data.TriggerDetails.StaffName}",
            _ => $"Soft deleted via {data.TriggerDetails.TriggerSource}"
        };

        BackgroundJob.Enqueue<IAuditLogsApi>(
            x => x.AuditLogsCreateUserProfileSoftDeletedLogPostAsync(
                null,
                _distributedInvocationContextService.GetSerializedContextHeader(),
                new CreateUserProfileSoftDeletedLogInput(
                    companyId,
                    userProfileId,
                    staffId,
                    logMessage,
                    data),
                CancellationToken.None));

        return Task.CompletedTask;
    }

    public Task CreateUserProfileSoftDeletedLogsAsync(
        string companyId,
        List<string> userProfileIds,
        string staffId,
        UserProfileSoftDeletedLogData data)
    {
        var logMessage = data.TriggerDetails.TriggerSource switch
        {
            UpdateUserProfileTriggerSource.StaffManual => $"Recovered by {data.TriggerDetails.StaffName}",
            _ => $"Recovered via {data.TriggerDetails.TriggerSource}"
        };

        return _auditLogsApi.AuditLogsCreateUserProfileSoftDeletedLogsPostAsync(
            null,
            _distributedInvocationContextService.GetSerializedContextHeader(),
            new CreateUserProfileSoftDeletedLogsInput(
                companyId,
                userProfileIds,
                staffId,
                logMessage,
                data));
    }

    public Task CreateUserProfileRecoveredLogAsync(
        string companyId,
        string userProfileId,
        string staffId,
        UserProfileRecoveredLogData data)
    {
        var logMessage = data.TriggerDetails.TriggerSource switch
        {
            UpdateUserProfileTriggerSource.StaffManual => $"Recovered by {data.TriggerDetails.StaffName}",
            _ => $"Recovered via {data.TriggerDetails.TriggerSource}"
        };

        BackgroundJob.Enqueue<IAuditLogsApi>(
            x => x.AuditLogsCreateUserProfileRecoveredLogPostAsync(
                null,
                _distributedInvocationContextService.GetSerializedContextHeader(),
                new CreateUserProfileRecoveredLogInput(
                    companyId,
                    userProfileId,
                    staffId,
                    logMessage,
                    data),
                CancellationToken.None));

        return Task.CompletedTask;
    }

    public Task CreateUserProfileRecoveredLogsAsync(
        string companyId,
        List<string> userProfileIds,
        string staffId,
        UserProfileRecoveredLogData data)
    {
        var logMessage = data.TriggerDetails.TriggerSource switch
        {
            UpdateUserProfileTriggerSource.StaffManual => $"Recovered by {data.TriggerDetails.StaffName}",
            _ => $"Recovered via {data.TriggerDetails.TriggerSource}"
        };

        return _auditLogsApi.AuditLogsCreateUserProfileRecoveredLogsPostAsync(
            null,
            _distributedInvocationContextService.GetSerializedContextHeader(),
            new CreateUserProfileRecoveredLogsInput(
                companyId,
                userProfileIds,
                staffId,
                logMessage,
                data));
    }

    public Task CreateUserProfileEnrolledIntoFlowHubWorkflowLogAsync(
        string companyId,
        string userProfileId,
        OnUserProfileEnrolledIntoFlowHubWorkflowData data)
    {
        BackgroundJob.Enqueue<IAuditLogsApi>(
            x => x.AuditLogsCreateUserProfileEnrolledIntoFlowHubWorkflowLogPostAsync(
                null,
                _distributedInvocationContextService.GetSerializedContextHeader(),
                new CreateUserProfileEnrolledIntoFlowHubWorkflowLogInput(
                    companyId,
                    userProfileId,
                    null,
                    data.WorkflowId,
                    data.WorkflowName,
                    data.WorkflowVersionedId,
                    data.StateId),
                CancellationToken.None));

        return Task.CompletedTask;
    }

    /// <summary>
    /// Due to design changes, the [sleekflow_staff_id] stored in container [user_profile_audit_log] could be
    /// 1. Staff.Id -- long
    /// 2. Staff.IdentityId -- string
    /// </summary>
    /// <param name="companyId">Company Id.</param>
    /// <param name="sleekflowStaffId">Sleekflow Staff Id.</param>
    /// <returns>StaffWithoutCompanyResponse.</returns>
    private async Task<StaffWithoutCompanyResponse> GetStaffManualAddedLogStaffInfoAsync(
        string companyId,
        string sleekflowStaffId)
    {
        return long.TryParse(sleekflowStaffId, out var staffId)
            ? await _appDbContext.UserRoleStaffs.Where(x => x.CompanyId == companyId && x.Id == staffId)
                .Include(x => x.Identity)
                .ProjectTo<StaffWithoutCompanyResponse>(_mapper.ConfigurationProvider)
                .FirstOrDefaultAsync()
            : await _appDbContext.UserRoleStaffs
                .Where(x => x.CompanyId == companyId && x.IdentityId == sleekflowStaffId)
                .Include(x => x.Identity)
                .ProjectTo<StaffWithoutCompanyResponse>(_mapper.ConfigurationProvider)
                .FirstOrDefaultAsync();
    }
}