﻿#nullable enable

using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using StackExchange.Redis;
using Travis_backend.Cache.Models;
using Travis_backend.Cache.Models.CacheKeyPatterns;
using Travis_backend.Database;

namespace Travis_backend.Cache;

public interface ICacheManagerService
{
    Task<T> GetAndSaveCacheAsync<T>(
        ICacheKeyPattern cacheKeyPattern,
        Func<Task<T>> fun,
        TimeSpan? customDuration = null,
        JsonSerializerSettings? jsonSerializerSettings = null);

    Task SaveCacheAsync<T>(
        ICacheKeyPattern cacheKeyPattern,
        T obj,
        TimeSpan? customDuration = null,
        JsonSerializerSettings? jsonSerializerSettings = null);

    Task<string?> GetCacheAsync(
        ICacheKeyPattern cacheKeyPattern);

    Task<T> GetAndSaveCacheWithConstantKeyAsync<T>(
        string keyName,
        Func<Task<T>> fun,
        TimeSpan customDuration,
        JsonSerializerSettings? jsonSerializerSettings = null);

    Task SaveCacheWithConstantKeyAsync<T>(
        string keyName,
        T obj,
        TimeSpan customDuration,
        JsonSerializerSettings? jsonSerializerSettings = null);

    Task<string?> GetCacheWithConstantKeyAsync(
        string keyName);

    Task DeleteCacheAsync(ICacheKeyPattern cacheKeyPattern);

    Task DeleteCacheWithConstantKeyAsync(string keyName);
}

public class CacheManagerService : ICacheManagerService
{
    public static readonly JsonSerializerSettings DefaultJsonSerializerSettings = new JsonSerializerSettings()
    {
        Converters = new List<JsonConverter>()
        {
            new IsoDateTimeConverter
            {
                DateTimeFormat = "yyyy'-'MM'-'dd'T'HH':'mm':'ss.fff'Z'"
            }
        },
        ReferenceLoopHandling = ReferenceLoopHandling.Error,
        DateParseHandling = DateParseHandling.DateTimeOffset,
        DateTimeZoneHandling = DateTimeZoneHandling.Utc,
        MaxDepth = 32,
    };


    private readonly IConnectionMultiplexer _connectionMultiplexer;
    private readonly ILogger<CacheManagerService> _logger;

    public CacheManagerService(
        IConnectionMultiplexer connectionMultiplexer,
        ILogger<CacheManagerService> logger)
    {
        _connectionMultiplexer = connectionMultiplexer;
        _logger = logger;
    }

    public async Task<T> GetAndSaveCacheAsync<T>(
        ICacheKeyPattern cacheKeyPattern,
        Func<Task<T>> fun,
        TimeSpan? customDuration = null,
        JsonSerializerSettings? jsonSerializerSettings = null)
    {
        var keyName = GetKeyNameFromEntity(cacheKeyPattern);
        var cacheConfig = KeyPatternConfigMap.GetCacheConfig(cacheKeyPattern);

        var cache = await GetAndSaveCacheWithConstantKeyAsync(
            keyName,
            fun,
            customDuration ?? cacheConfig.Duration,
            jsonSerializerSettings);


        return cache;
    }

    public async Task SaveCacheAsync<T>(
        ICacheKeyPattern cacheKeyPattern,
        T obj,
        TimeSpan? customDuration = null,
        JsonSerializerSettings? jsonSerializerSettings = null)
    {
        var keyName = GetKeyNameFromEntity(cacheKeyPattern);
        var cacheConfig = KeyPatternConfigMap.GetCacheConfig(cacheKeyPattern);

        await SaveCacheWithConstantKeyAsync(
            keyName,
            obj,
            customDuration ?? cacheConfig.Duration,
            jsonSerializerSettings);
    }

    public async Task<string?> GetCacheAsync(
        ICacheKeyPattern cacheKeyPattern)
    {
        var keyName = GetKeyNameFromEntity(cacheKeyPattern);

        var keyValue = await GetCacheWithConstantKeyAsync(keyName);
        return keyValue;
    }

    public async Task<T> GetAndSaveCacheWithConstantKeyAsync<T>(
        string keyName,
        Func<Task<T>> fun,
        TimeSpan customDuration,
        JsonSerializerSettings? jsonSerializerSettings = null)
    {
        var database = _connectionMultiplexer.GetDatabase();

        try
        {
            var redisValue = await database.StringGetAsync(keyName);
            if (redisValue.HasValue)
            {
                var deserializeObject =
                    JsonConvert.DeserializeObject<T>(
                        redisValue!,
                        jsonSerializerSettings ?? DefaultJsonSerializerSettings);
                if (deserializeObject != null)
                {
                    return deserializeObject;
                }
            }
        }
        catch (Exception e)
        {
            _logger.LogWarning(e, "Caught an exception when getting the cache, key {Key}", keyName);
        }

        var cache = await fun.Invoke();

        try
        {
            if (cache != null)
            {
                var value = cache is string
                    ? cache.ToString()
                    : JsonConvert.SerializeObject(cache, jsonSerializerSettings ?? DefaultJsonSerializerSettings);

                await database.StringSetAsync(
                    keyName,
                    value,
                    customDuration,
                    When.NotExists);
            }
        }
        catch (Exception e)
        {
            _logger.LogWarning(e, "Caught an exception when saving the cache, key {Key}", keyName);
        }

        return cache;
    }


    public async Task SaveCacheWithConstantKeyAsync<T>(
        string keyName,
        T obj,
        TimeSpan customDuration,
        JsonSerializerSettings? jsonSerializerSettings = null)
    {
        try
        {
            var value = obj is string
                ? obj.ToString()
                : JsonConvert.SerializeObject(obj, jsonSerializerSettings ?? DefaultJsonSerializerSettings);

            await _connectionMultiplexer.GetDatabase().StringSetAsync(
                keyName,
                value,
                customDuration);
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Failed to save cache with key name {KeyName}", keyName);
        }
    }

    public async Task<string?> GetCacheWithConstantKeyAsync(
        string keyName)
    {
        try
        {
            var keyValue = await _connectionMultiplexer.GetDatabase().StringGetAsync(keyName);

            return keyValue;
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Failed to retrieve cache value with key name {KeyName}", keyName);
        }
        return null;
    }

    public async Task DeleteCacheAsync(ICacheKeyPattern cacheKeyPattern)
    {
        var keyName = GetKeyNameFromEntity(cacheKeyPattern);
        try
        {
            await _connectionMultiplexer.GetDatabase().KeyDeleteAsync(keyName);
        }
        catch (Exception e)
        {
            _logger.LogWarning(e, "Caught an exception when removing the cache, key {KeyName}", keyName);
        }
    }

    public async Task DeleteCacheWithConstantKeyAsync(string keyName)
    {
        try
        {
            await _connectionMultiplexer.GetDatabase().KeyDeleteAsync(keyName);
        }
        catch (Exception e)
        {
            _logger.LogWarning(e, "Caught an exception when removing the cache, key {KeyName}", keyName);
        }
    }

    private static string GetKeyNameFromEntity(ICacheKeyPattern cacheKeyPattern)
    {
        var cacheConfig = KeyPatternConfigMap.GetCacheConfig(cacheKeyPattern);
        var paramValues = cacheKeyPattern.GenerateKeyPattern();
        return cacheConfig.Prefix + paramValues;
    }
}