using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Threading.Tasks;
using AutoMapper;
using Hangfire;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Polly;
using Travis_backend.AccountAuthenticationDomain.Models;
using Travis_backend.AutomationDomain.Models;
using Travis_backend.Cache;
using Travis_backend.CommonDomain.Services;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.Configuration;
using Travis_backend.Constants;
using Travis_backend.ConversationDomain.ViewModels;
using Travis_backend.ConversationServices;
using Travis_backend.Database;
using Travis_backend.Enums;
using Travis_backend.Helpers;
using Travis_backend.InternalDomain.Services;
using Travis_backend.ResellerDomain.Constants;
using Travis_backend.ResellerDomain.Models;
using Travis_backend.ResellerDomain.ViewModels;
using Travis_backend.SubscriptionPlanDomain.Models;
using Travis_backend.SubscriptionPlanDomain.Services;
using Country = Travis_backend.CoreDomain.Models.Country;

namespace Travis_backend.ResellerDomain.Services;

public interface IPowerflowManageResellerRepository
{
    Task<ResellerBasicInformation> GetResellerInformation(string companyId);

    Task<List<ResellerBasicInformation>> GetAllResellerInformation();

    Task<ResponseWrapper> CreateResellerCompany(ResellerCompanyViewModel createResellerCompanyViewModel);

    Task<ResponseWrapper> CreateResellerStaff(ResellerStaffViewModel createdResellerStaffViewModel);

    Task<ResponseWrapper> UpdateResellerCompany(UpdateResellerCompanyViewModel resellerCompanyViewModel);

    Task<ResponseWrapper> TopUpBalance(ApplicationUser internalUser, ResellerTopUpViewModel topUpViewModel);

    string SetTransactionAction(SubscriptionPlan subscriptionPlan);

    Task SetSubscriptionPlanMaximumUsage(Company company);

    Task<ResponseWrapper> UpdateResellerSubscriptionPlanConfig(
        UpdateResellerSubscriptionPlanConfigViewModel resellerSubscriptionPlanConfigViewModel);

    Task AddResellerActivityLog(ResellerActivityLog resellerActivityLog);

    Task<ResponseWrapper> MigrateDirectClientToResellerClient(string resellerCompanyId, string clientCompanyId);
}

public class PowerflowManageResellerRepository : IPowerflowManageResellerRepository
{
    private readonly ApplicationDbContext _appDbContext;
    private readonly IMapper _mapper;
    private readonly ILogger<PowerflowManageResellerRepository> _logger;
    private readonly UserManager<ApplicationUser> _userManager;
    private readonly IAzureBlobStorageService _azureBlobStorageService;
    private readonly RoleManager<IdentityRole> _roleManager;
    private readonly ICompanyInfoCacheService _companyInfoCacheService;
    private readonly ICountryService _countryService;
    private readonly IDefaultSubscriptionPlanIdGetter _defaultSubscriptionPlanIdGetter;
    private readonly IResellerBaseService _resellerBaseService;

    public PowerflowManageResellerRepository(
        ApplicationDbContext appDbContext,
        IMapper mapper,
        ILogger<PowerflowManageResellerRepository> logger,
        UserManager<ApplicationUser> userManager,
        IAzureBlobStorageService azureBlobStorageService,
        RoleManager<IdentityRole> roleManager,
        ICompanyInfoCacheService companyInfoCacheService,
        ICountryService countryService,
        IDefaultSubscriptionPlanIdGetter defaultSubscriptionPlanIdGetter,
        IResellerBaseService resellerBaseService)
    {
        _appDbContext = appDbContext;
        _mapper = mapper;
        _logger = logger;
        _userManager = userManager;
        _azureBlobStorageService = azureBlobStorageService;
        _roleManager = roleManager;
        _companyInfoCacheService = companyInfoCacheService;
        _countryService = countryService;
        _defaultSubscriptionPlanIdGetter = defaultSubscriptionPlanIdGetter;
        _resellerBaseService = resellerBaseService;
    }

    public async Task<ResellerBasicInformation> GetResellerInformation(string companyId)
    {
        var resellerProfile = await _appDbContext.ResellerCompanyProfiles
            .Include(x => x.Company)
            .Include(x => x.ResellerProfileLogo)
            .Where(x => x.CompanyId == companyId)
            .FirstOrDefaultAsync();

        resellerProfile = await _resellerBaseService.CheckAndUpdateResellerSubscriptionPlanConfig(resellerProfile);

        var resellerBasicInformation = new ResellerBasicInformation
        {
            ResellerProfileId = resellerProfile.Id,
            CompanyId = resellerProfile.CompanyId,
            CompanyName = resellerProfile.Company.CompanyName,
            Balance = resellerProfile.Balance,
            Currency = resellerProfile.Currency,
            TopUp = resellerProfile.TopUp,
            Debited = resellerProfile.Debited,
            ResellerDiscount = resellerProfile.ResellerDiscount,
            PhoneNumber = resellerProfile.PhoneNumber,
            ResellerCompanyLogoLink = resellerProfile.ResellerProfileLogo != null
                ? _azureBlobStorageService.GetAzureBlobSasUri(
                    resellerProfile.ResellerProfileLogo.Filename,
                    resellerProfile.Company.StorageConfig.ContainerName,
                    30 * 24)
                : string.Empty,
            BalanceMinimumLimit = resellerProfile.BalanceMinimumLimit,
            CountryTier = resellerProfile.CountryTier,
            ResellerSubscriptionPlanConfig = resellerProfile.ResellerSubscriptionPlanConfig,
            PartnerStackPartnerKey = resellerProfile.PartnerStackPartnerKey
        };

        return resellerBasicInformation;
    }

    public async Task<List<ResellerBasicInformation>> GetAllResellerInformation()
    {
        var resellerBasicInformationList = await _appDbContext.ResellerCompanyProfiles.Include(x => x.Company)
            .Include(x => x.ResellerProfileLogo).Select(
                x => new ResellerBasicInformation()
                {
                    ResellerProfileId = x.Id,
                    CompanyId = x.CompanyId,
                    CompanyName = x.Company.CompanyName,
                    Balance = x.Balance,
                    Currency = x.Currency,
                    TopUp = x.TopUp,
                    Debited = x.Debited,
                    ResellerDiscount = x.ResellerDiscount,
                    PhoneNumber = x.PhoneNumber,
                    ResellerCompanyLogoLink = x.ResellerProfileLogo != null
                        ? _azureBlobStorageService.GetAzureBlobSasUri(
                            x.ResellerProfileLogo.Filename,
                            x.Company.StorageConfig.ContainerName,
                            30 * 24)
                        : string.Empty
                }).ToListAsync();

        return resellerBasicInformationList;
    }

    public async Task<ResponseWrapper> CreateResellerCompany(ResellerCompanyViewModel createResellerCompanyViewModel)
    {
        var response = new ResponseWrapper()
        {
            IsSuccess = false
        };

        try
        {
            await CreateResellerUserRoleIfNotExistAsync();

            if (string.IsNullOrEmpty(createResellerCompanyViewModel.TimeZoneInfoId))
            {
                createResellerCompanyViewModel.TimeZoneInfoId = "GMT Standard Time";
            }

            var company = new Company
            {
                CompanyName = createResellerCompanyViewModel.CompanyName,
                BillRecords = new List<BillRecord>(),
                TimeZoneInfoId = createResellerCompanyViewModel.TimeZoneInfoId,
                IsShopifyAccount = false,
                CompanyType = createResellerCompanyViewModel.CompanyType
            };
            company.StorageConfig = new StorageConfig()
            {
                ContainerName = company.Id
            };

            try
            {
                var phoneNumberUtil = PhoneNumbers.PhoneNumberUtil.GetInstance();
                var phonenumber = phoneNumberUtil.Parse($"+{createResellerCompanyViewModel.PhoneNumber}", null);
                var countCode = PhoneNumberHelper.GetCountryCode(phonenumber);
                string countryName = _countryService.GetCountryEnglishNameByCountryCode(countCode);

                company.CompanyCountry = countryName;
            }
            catch (Exception ex)
            {
                _logger.LogInformation(ex, "Ignore country: {Message}", ex.Message);
            }

            var customFields = new List<CompanyCustomField>()
            {
                new CompanyCustomField
                {
                    Category = "CompanyInfo",
                    FieldName = "CompanySize",
                    Type = FieldDataType.SingleLineText,
                    CompanyCustomFieldFieldLinguals = new List<CompanyCustomFieldFieldLingual>
                    {
                        new CompanyCustomFieldFieldLingual
                        {
                            DisplayName = "公司人數", Language = "zh-HK"
                        },
                        new CompanyCustomFieldFieldLingual
                        {
                            DisplayName = "Company Size", Language = "en"
                        }
                    },
                    Value = createResellerCompanyViewModel.CompanySize
                },
                new CompanyCustomField
                {
                    Category = "CompanyInfo",
                    FieldName = "PhoneNumber",
                    Type = FieldDataType.SingleLineText,
                    CompanyCustomFieldFieldLinguals = new List<CompanyCustomFieldFieldLingual>
                    {
                        new CompanyCustomFieldFieldLingual
                        {
                            DisplayName = "公司電話", Language = "zh-HK"
                        },
                        new CompanyCustomFieldFieldLingual
                        {
                            DisplayName = "Company Phone", Language = "en"
                        }
                    },
                    Value = createResellerCompanyViewModel.PhoneNumber
                }
            };

            company.CompanyCustomFields = customFields;

            await _appDbContext.CompanyCompanies.AddAsync(company);
            await _appDbContext.SaveChangesAsync();

            company.IsFreeTrial = false;
            company.SignalRGroupName = company.Id;

            var newBill = new BillRecord()
            {
                SubscriptionPlanId =
                    _defaultSubscriptionPlanIdGetter
                        .GetDefaultEnterprisePlanId(), // enterprise plan by default for reseller company temporarily now
                PayAmount = 0,
                CompanyId = company.Id,
                Status = BillStatus.Active,
                PaymentStatus = PaymentStatus.FreeOfCharge,
                PeriodStart = DateTime.UtcNow,
                PeriodEnd = DateTime.UtcNow.AddMonths(1),
                quantity = 1
            };

            company.BillRecords ??= new List<BillRecord>();

            company.BillRecords.Add(newBill);

            company.MaximumContacts = null;
            company.MaximumWhAutomatedMessages = null;
            company.MaximumAutomations = null;

            company = AddDefaultUserFiels(company);
            company = AddDefaultQuickReply(company);
            company = AddDefaultLabel(company);
            company = AddDefaultCompanyList(company);

            var countryTier =
                ResellerCountryTier.GetAllCountryTiers.Contains(createResellerCompanyViewModel.CountryTier)
                    ? createResellerCompanyViewModel.CountryTier
                    : ResellerCountryTier.Tier1;

            var resellerProfile = new ResellerCompanyProfile()
            {
                CompanyId = company.Id,
                PhoneNumber = createResellerCompanyViewModel.PhoneNumber,
                Currency = createResellerCompanyViewModel.Currency,
                Email = createResellerCompanyViewModel.Email,
                ResellerDiscount = createResellerCompanyViewModel.ResellerDiscount,
                CountryTier = countryTier,
                ResellerSubscriptionPlanConfig = _resellerBaseService.InitialiseResellerSubscriptionPlanConfig(
                    countryTier)
            };

            await _appDbContext.ResellerCompanyProfiles.AddAsync(resellerProfile);
            await _appDbContext.SaveChangesAsync();

            if (createResellerCompanyViewModel.ResellerStaffViewModel != null)
            {
                createResellerCompanyViewModel.ResellerStaffViewModel.ResellerCompanyId = company.Id;
                var resellerStaffResponse =
                    await CreateResellerStaff(createResellerCompanyViewModel.ResellerStaffViewModel);

                if (!resellerStaffResponse.IsSuccess)
                {
                    _logger.LogWarning(
                        "Reseller Company {ResellerCompanyId} Staff {ResellerUsername} creation error: {ErrorMessage}",
                        resellerProfile.CompanyId,
                        createResellerCompanyViewModel.ResellerStaffViewModel.Username,
                        resellerStaffResponse.ErrorMsg);
                }
            }

            response.IsSuccess = true;
            response.Data = new ResellerCompanyResponse()
            {
                ResellerCompanyId = company.Id,
                ResellerCompanyName = company.CompanyName,
                ResellerProfileId = resellerProfile.Id,
                PhoneNumber = resellerProfile.PhoneNumber,
                Balance = resellerProfile.Balance,
                ResellerDiscount = resellerProfile.ResellerDiscount,
                Currency = resellerProfile.Currency,
                BalanceMinimumLimit = resellerProfile.BalanceMinimumLimit
            };

            BackgroundJob.Enqueue<IInternalHubSpotService>(x => x.CreateNewResellerSignupCompanyAndStaff(company.Id));

            // Sync Partner Key from HubSpot
            BackgroundJob.Enqueue<IInternalPartnerStackService>(
                x => x.SyncPartnerStackPartnerKeyFromHubSpotContact(
                    company.Id,
                    createResellerCompanyViewModel.ResellerStaffViewModel.Email));

            return response;
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "Reseller Company {CompanyName} creation error: {ExceptionMessage}",
                createResellerCompanyViewModel.CompanyName,
                ex.Message);

            response.ErrorMsg = ex.Message;

            return response;
        }
    }

    public async Task<ResponseWrapper> UpdateResellerCompany(UpdateResellerCompanyViewModel resellerCompanyViewModel)
    {
        var response = new ResponseWrapper()
        {
            IsSuccess = false
        };

        var resellerProfile = await _appDbContext.ResellerCompanyProfiles.Include(x => x.Company)
            .FirstOrDefaultAsync(x => x.CompanyId == resellerCompanyViewModel.ResellerCompanyId);

        if (resellerProfile == null)
        {
            response.ErrorMsg = "Reseller Company Not Found";

            return response;
        }

        if (!string.IsNullOrEmpty(resellerCompanyViewModel.TimeZoneInfoId))
        {
            resellerProfile.Company.TimeZoneInfoId = resellerCompanyViewModel.TimeZoneInfoId;
        }

        if (resellerCompanyViewModel.ResellerDiscount.HasValue &&
            resellerCompanyViewModel.ResellerDiscount != resellerProfile.ResellerDiscount)
        {
            resellerProfile.ResellerDiscount = resellerCompanyViewModel.ResellerDiscount.Value;
        }

        if (!string.IsNullOrEmpty(resellerCompanyViewModel.PhoneNumber))
        {
            resellerProfile.PhoneNumber = resellerCompanyViewModel.PhoneNumber;
        }

        if (!string.IsNullOrEmpty(resellerCompanyViewModel.CompanyName))
        {
            resellerProfile.Company.CompanyName = resellerCompanyViewModel.CompanyName;
        }

        if (!string.IsNullOrEmpty(resellerCompanyViewModel.Email))
        {
            resellerProfile.Email = resellerCompanyViewModel.Email;
        }

        if (resellerCompanyViewModel.MinimumBalanceLimit.HasValue &&
            resellerCompanyViewModel.MinimumBalanceLimit != resellerProfile.BalanceMinimumLimit)
        {
            resellerProfile.BalanceMinimumLimit = resellerCompanyViewModel.MinimumBalanceLimit.Value;
        }

        resellerProfile.UpdatedAt = DateTime.UtcNow;
        resellerProfile.Company.UpdatedAt = DateTime.UtcNow;
        await _appDbContext.SaveChangesAsync();

        response.IsSuccess = true;
        response.Data = new ResellerCompanyResponse()
        {
            ResellerCompanyId = resellerProfile.Company.Id,
            ResellerCompanyName = resellerProfile.Company.CompanyName,
            ResellerProfileId = resellerProfile.Id,
            PhoneNumber = resellerProfile.PhoneNumber,
            Balance = resellerProfile.Balance,
            ResellerDiscount = resellerProfile.ResellerDiscount,
            Currency = resellerProfile.Currency,
            BalanceMinimumLimit = resellerProfile.BalanceMinimumLimit
        };

        await _companyInfoCacheService.RemoveCompanyInfoCache(resellerProfile.CompanyId);
        return response;
    }

    public async Task<ResponseWrapper> TopUpBalance(ApplicationUser internalUser, ResellerTopUpViewModel topUpViewModel)
    {
        var response = new ResponseWrapper()
        {
            IsSuccess = false
        };
        var resellerCompanyProfile = await _appDbContext.ResellerCompanyProfiles.Include(x => x.ResellerTransactionLogs)
            .Include(x => x.Company).Include(x => x.ClientCompanyProfiles).ThenInclude(x => x.ClientCompany)
            .FirstOrDefaultAsync(
                x => x.Id == topUpViewModel.ResellerProfileId &&
                     x.Company.CompanyName == topUpViewModel.ResellerCompanyName);

        if (resellerCompanyProfile == null)
        {
            response.ErrorMsg = "Reseller Profile Not Found";

            return response;
        }

        if (topUpViewModel.Amount <= 0)
        {
            response.ErrorMsg = "Top Up Amount should be more than 0";

            return response;
        }

        var currencyExchangeRates = await _appDbContext.CmsCurrencyExchangeRates.ToListAsync();

        var exchangeRate = currencyExchangeRates.First(
            x => x.CurrencyFrom == topUpViewModel.Currency.ToUpper() &&
                 x.CurrencyTo == resellerCompanyProfile.Currency);

        var transactionLog = new ResellerTransactionLog()
        {
            ResellerCompanyProfileId = resellerCompanyProfile.Id,
            Amount = topUpViewModel.Amount,
            Currency = topUpViewModel.Currency,
            TransactionMode = TransactionMode.TopUp,
            UserIdentityId = internalUser.Id,
            TransactionCategory = ResellerTransactionCategory.Balance,
            TransactionAction = "Reseller Account Top Up",
            Detail = topUpViewModel.Detail,
            TopUpMethod = ResellerTopUpMethod.Powerflow
        };

        resellerCompanyProfile.ResellerTransactionLogs ??=[];

        resellerCompanyProfile.ResellerTransactionLogs.Add(transactionLog);
        var topUpAmountAfterExchange = topUpViewModel.Amount * exchangeRate.ExchangeRate;
        resellerCompanyProfile.TopUp += topUpAmountAfterExchange;
        await _appDbContext.SaveChangesAsync();

        var transactionLogDtos = new List<TransactionLogDto>();

        var billRecordIds = resellerCompanyProfile.ResellerTransactionLogs.Where(x => x.BillRecordId != null)
            .Select(x => x.BillRecordId).ToList();
        var billRecords = await _appDbContext.CompanyBillRecords.Where(x => billRecordIds.Contains(x.Id)).ToListAsync();

        var clientCompanies = resellerCompanyProfile.ClientCompanyProfiles.Select(x => x.ClientCompany).ToList();

        foreach (var resellerTransactionLog in resellerCompanyProfile.ResellerTransactionLogs)
        {
            var transactionLogDto = _mapper.Map<TransactionLogDto>(resellerTransactionLog);

            if (resellerTransactionLog.BillRecordId != null)
            {
                transactionLogDto.BillRecordDto = _mapper.Map<BillRecordDto>(
                    billRecords.Find(x => x.Id == resellerTransactionLog.BillRecordId));
            }

            if (!string.IsNullOrEmpty(resellerTransactionLog.ClientCompanyId))
            {
                transactionLogDto.ClientCompany = _mapper.Map<CompanyResponse>(
                    clientCompanies.Find(x => x.Id == resellerTransactionLog.ClientCompanyId));
            }

            if (!string.IsNullOrEmpty(resellerTransactionLog.UserIdentityId))
            {
                transactionLogDto.UserIdentity = _mapper.Map<UserInfoResponse>(
                    await _userManager.FindByIdAsync(resellerTransactionLog.UserIdentityId));
            }

            transactionLogDtos.Add(transactionLogDto);
        }

        response.Data = new ResellerTopUpResponse()
        {
            ResellerCompanyName = resellerCompanyProfile.Company.CompanyName,
            ResellerBalance = resellerCompanyProfile.Balance,
            TopUp = resellerCompanyProfile.TopUp,
            Debited = resellerCompanyProfile.Debited,
            Currency = resellerCompanyProfile.Currency,
            ResellerTransactionLogs = transactionLogDtos
        };

        response.IsSuccess = true;
        return response;
    }

    public async Task<ResponseWrapper> CreateResellerStaff(ResellerStaffViewModel createdResellerStaffViewModel)
    {
        var response = new ResponseWrapper()
        {
            IsSuccess = false
        };

        try
        {
            if (createdResellerStaffViewModel.Username == null)
            {
                createdResellerStaffViewModel.Username = createdResellerStaffViewModel.Email;
            }

            var isExist = await _userManager.FindByNameAsync(createdResellerStaffViewModel.Username) != null;
            if (isExist)
            {
                response.ErrorMsg = "Username already exists!";
                return response;
            }

            isExist = await _userManager.FindByEmailAsync(createdResellerStaffViewModel.Email) != null;
            if (isExist)
            {
                response.ErrorMsg = "Email already exists!";
                return response;
            }

            if (!_resellerBaseService.ValidatePassword(createdResellerStaffViewModel.Password))
            {
                response.ErrorMsg =
                    "Password must be at least 8 characters long, contain at least one uppercase letter, one lowercase letter, one number and one special character.";
                return response;
            }

            var userIdentity = new ApplicationUser()
            {
                UserName = createdResellerStaffViewModel.Username,
                Email = createdResellerStaffViewModel.Email,
                DisplayName = createdResellerStaffViewModel.DisplayName,
                FirstName = createdResellerStaffViewModel.FirstName,
                LastName = createdResellerStaffViewModel.LastName,
                PhoneNumber = createdResellerStaffViewModel.PhoneNumber,
                CompanyId = createdResellerStaffViewModel.ResellerCompanyId
            };

            if (string.IsNullOrEmpty(userIdentity.UserName))
            {
                userIdentity.UserName = createdResellerStaffViewModel.Email;
            }

            userIdentity.UserRole = "user";
            userIdentity.EmailConfirmed = true;

            var identityResult = await _userManager.CreateAsync(userIdentity, createdResellerStaffViewModel.Password);
            if (!identityResult.Succeeded)
            {
                response.ErrorMsg = identityResult.Errors.FirstOrDefault()?.Description;
                return response;
            }

            var identity = await _userManager.FindByNameAsync(createdResellerStaffViewModel.Username);

            if (identity == null)
            {
                response.ErrorMsg = "User Not Found";
                return response;
            }

            await Policy
                .Handle<Exception>()
                .WaitAndRetryAsync(5, sleepDurationProvider: _ => TimeSpan.FromSeconds(1))
                .ExecuteAsync(
                    async () => await _userManager.AddToRoleAsync(
                        userIdentity,
                        ApplicationUserRole.ResellerPortalUser));

            // To sync the role to Auth0
            await Policy
                .Handle<Exception>()
                .WaitAndRetryAsync(5, sleepDurationProvider: _ => TimeSpan.FromSeconds(1))
                .ExecuteAsync(async () => await _userManager.UpdateAsync(identity));

            var resellerCompanyProfile = await _appDbContext.ResellerCompanyProfiles.Include(x => x.ResellerStaffs)
                .Include(x => x.Company).FirstOrDefaultAsync(x => x.CompanyId == createdResellerStaffViewModel.ResellerCompanyId);
            if (resellerCompanyProfile == null)
            {
                response.ErrorMsg = "Reseller Company Not Found";
                return response;
            }

            var resellerStaff = new ResellerStaff()
            {
                IdentityId = identity.Id,
                ResellerCompanyProfileId = resellerCompanyProfile.Id,
                Position = createdResellerStaffViewModel.Position,
                TimeZoneInfoId = resellerCompanyProfile.Company.TimeZoneInfoId,
                Locale = "zh-hk"
            };

            resellerCompanyProfile.ResellerStaffs ??=[];

            resellerCompanyProfile.ResellerStaffs.Add(resellerStaff);

            await _appDbContext.UserRoleStaffs.AddAsync(
                new Staff
                {
                    IdentityId = identity.Id,
                    Company = resellerCompanyProfile.Company,
                    Locale = "zh-hk",
                    Position = createdResellerStaffViewModel.Position,
                    TimeZoneInfoId = resellerCompanyProfile.Company.TimeZoneInfoId,
                    RoleType = StaffUserRole.Admin,
                    Order = await _appDbContext.UserRoleStaffs.AnyAsync(x => x.Order == 0) ? 1 : 0
                });

            await _appDbContext.SaveChangesAsync();

            await _resellerBaseService.AddResellerStaffsToClientCompanies(
                resellerCompanyProfile,
                resellerStaff);

            var resellerStaffInfo = new ResellerStaffInformation
            {
                Id = resellerStaff.Id,
                IdentityId = identity.Id,
                UserInfo = _mapper.Map<UserInfoResponse>(userIdentity),
                ResellerProfileId = resellerStaff.ResellerCompanyProfileId,
                ResellerProfileInformation = _mapper.Map<ResellerProfileInformation>(resellerCompanyProfile),
                ResellerCompanyId = resellerCompanyProfile.CompanyId,
                CompanyResponse = _mapper.Map<CompanyResponse>(resellerCompanyProfile.Company),
                Locale = resellerStaff.Locale,
                Position = resellerStaff.Position,
                ProfilePictureURL = resellerStaff.ProfilePicture != null
                    ? _azureBlobStorageService.GetAzureBlobSasUri(
                        resellerStaff.ProfilePicture.Filename,
                        resellerStaff.ProfilePicture.BlobContainer)
                    : null
            };

            response.IsSuccess = true;
            response.Data = resellerStaffInfo;

            BackgroundJob.Enqueue<IInternalHubSpotService>(
                x => x.SyncCompanyStaffs(resellerCompanyProfile.CompanyId));

            return response;
        }
        catch (Exception ex)
        {
            response.ErrorMsg = ex.Message;

            _logger.LogError(
                ex,
                "[{MethodName}] Reseller company {ResellerCompanyId} create user {ResellerUsername} error",
                nameof(CreateResellerStaff),
                createdResellerStaffViewModel.ResellerCompanyId,
                createdResellerStaffViewModel.Username);

            return response;
        }
    }

    private Company AddDefaultCompanyList(Company company)
    {
        company.ImportContactHistories = new List<ImportContactHistory>();

        company.ImportContactHistories.Add(
            new ImportContactHistory
            {
                ImportName = "[Template] Leads"
            });
        company.ImportContactHistories.Add(
            new ImportContactHistory
            {
                ImportName = "[Template] Customers"
            });
        company.ImportContactHistories.Add(
            new ImportContactHistory
            {
                ImportName = "[Template] Pending"
            });
        company.ImportContactHistories.Add(
            new ImportContactHistory
            {
                ImportName = "[Template] VIP"
            });

        return company;
    }

    private Company AddDefaultLabel(Company company)
    {
        company.CompanyHashtags = new List<CompanyHashtag>();

        company.CompanyHashtags.Add(
            new CompanyHashtag
            {
                Hashtag = "New customer", HashTagColor = HashTagColor.Cyan
            });
        company.CompanyHashtags.Add(
            new CompanyHashtag
            {
                Hashtag = "New order", HashTagColor = HashTagColor.Yellow
            });
        company.CompanyHashtags.Add(
            new CompanyHashtag
            {
                Hashtag = "Pending payment", HashTagColor = HashTagColor.Pink
            });
        company.CompanyHashtags.Add(
            new CompanyHashtag
            {
                Hashtag = "Paid", HashTagColor = HashTagColor.Purple
            });
        company.CompanyHashtags.Add(
            new CompanyHashtag
            {
                Hashtag = "Order complete", HashTagColor = HashTagColor.Green
            });
        company.CompanyHashtags.Add(
            new CompanyHashtag
            {
                Hashtag = "VIP", HashTagColor = HashTagColor.Blue
            });
        company.CompanyHashtags.Add(
            new CompanyHashtag
            {
                Hashtag = "Issue", HashTagColor = HashTagColor.Red
            });

        return company;
    }

    private Company AddDefaultUserFiels(Company company)
    {
        List<CompanyCustomUserProfileField> companyCustomUserProfiles = new List<CompanyCustomUserProfileField>()
        {
            new CompanyCustomUserProfileField
            {
                FieldName = "Labels",
                Type = FieldDataType.Labels,
                Order = 0,
                CustomUserProfileFieldLinguals = new List<CustomUserProfileFieldLingual>()
                {
                    new CustomUserProfileFieldLingual
                    {
                        DisplayName = "Labels", Language = "en"
                    },
                    new CustomUserProfileFieldLingual
                    {
                        DisplayName = "標籤", Language = "zh-hk"
                    }
                },
                IsDeletable = false,
                IsEditable = false,
                IsDefault = true,
                FieldsCategory = FieldsCategory.Segmentation
            },
            new CompanyCustomUserProfileField
            {
                FieldName = "Lists",
                Type = FieldDataType.Lists,
                Order = 1,
                CustomUserProfileFieldLinguals = new List<CustomUserProfileFieldLingual>()
                {
                    new CustomUserProfileFieldLingual
                    {
                        DisplayName = "Lists", Language = "en"
                    },
                    new CustomUserProfileFieldLingual
                    {
                        DisplayName = "名單", Language = "zh-hk"
                    }
                },
                IsDeletable = false,
                IsEditable = false,
                IsDefault = true,
                FieldsCategory = FieldsCategory.Segmentation
            },
            new CompanyCustomUserProfileField
            {
                FieldName = "Email",
                Type = FieldDataType.Email,
                Order = 2,
                CustomUserProfileFieldLinguals = new List<CustomUserProfileFieldLingual>()
                {
                    new CustomUserProfileFieldLingual
                    {
                        DisplayName = "Email", Language = "en"
                    },
                    new CustomUserProfileFieldLingual
                    {
                        DisplayName = "電郵地址", Language = "zh-hk"
                    }
                },
                IsDeletable = false
            },
            new CompanyCustomUserProfileField
            {
                FieldName = "PhoneNumber",
                Type = FieldDataType.PhoneNumber,
                Order = 3,
                CustomUserProfileFieldLinguals = new List<CustomUserProfileFieldLingual>()
                {
                    new CustomUserProfileFieldLingual
                    {
                        DisplayName = "Phone Number", Language = "en"
                    },
                    new CustomUserProfileFieldLingual
                    {
                        DisplayName = "電話號碼", Language = "zh-hk"
                    }
                },
                IsDeletable = false
            },
            new CompanyCustomUserProfileField
            {
                FieldName = "CompanyName",
                Type = FieldDataType.SingleLineText,
                Order = 4,
                CustomUserProfileFieldLinguals = new List<CustomUserProfileFieldLingual>()
                {
                    new CustomUserProfileFieldLingual
                    {
                        DisplayName = "Company Name", Language = "en"
                    },
                    new CustomUserProfileFieldLingual
                    {
                        DisplayName = "公司名稱", Language = "zh-hk"
                    }
                }
            },
            new CompanyCustomUserProfileField
            {
                FieldName = "JobTitle",
                Type = FieldDataType.SingleLineText,
                Order = 5,
                CustomUserProfileFieldLinguals = new List<CustomUserProfileFieldLingual>()
                {
                    new CustomUserProfileFieldLingual
                    {
                        DisplayName = "Job Title", Language = "en"
                    },
                    new CustomUserProfileFieldLingual
                    {
                        DisplayName = "職稱", Language = "zh-hk"
                    }
                }
            },
            new CompanyCustomUserProfileField
            {
                FieldName = "ContactOwner",
                Type = FieldDataType.TravisUser,
                Order = 6,
                CustomUserProfileFieldLinguals = new List<CustomUserProfileFieldLingual>()
                {
                    new CustomUserProfileFieldLingual
                    {
                        DisplayName = "Contact Owner", Language = "en"
                    },
                    new CustomUserProfileFieldLingual
                    {
                        DisplayName = "聯絡負責人", Language = "zh-hk"
                    }
                },
                IsDeletable = false,
                IsEditable = true,
                IsDefault = true,
                FieldsCategory = FieldsCategory.SleekFlowUser
            },
            new CompanyCustomUserProfileField
            {
                FieldName = "LeadStage",
                Type = FieldDataType.Options,
                Order = 7,
                CustomUserProfileFieldLinguals = new List<CustomUserProfileFieldLingual>()
                {
                    new CustomUserProfileFieldLingual
                    {
                        DisplayName = "Lead Stage", Language = "en"
                    },
                    new CustomUserProfileFieldLingual
                    {
                        DisplayName = "階段", Language = "zh-hk"
                    }
                },
                CustomUserProfileFieldOptions = new List<CustomUserProfileFieldOption>()
                {
                    new CustomUserProfileFieldOption
                    {
                        Value = "Prospect",
                        CustomUserProfileFieldOptionLinguals = new List<CustomUserProfileFieldOptionLingual>()
                        {
                            new CustomUserProfileFieldOptionLingual
                            {
                                DisplayName = "Prospect", Language = "en"
                            },
                            new CustomUserProfileFieldOptionLingual
                            {
                                DisplayName = "Prospect", Language = "zh-HK"
                            }
                        },
                        Order = 0
                    },
                    new CustomUserProfileFieldOption
                    {
                        Value = "Contacted",
                        CustomUserProfileFieldOptionLinguals = new List<CustomUserProfileFieldOptionLingual>()
                        {
                            new CustomUserProfileFieldOptionLingual
                            {
                                DisplayName = "Contacted", Language = "en"
                            },
                            new CustomUserProfileFieldOptionLingual
                            {
                                DisplayName = "Contacted", Language = "zh-HK"
                            }
                        },
                        Order = 1
                    },
                    new CustomUserProfileFieldOption
                    {
                        Value = "Lead",
                        CustomUserProfileFieldOptionLinguals = new List<CustomUserProfileFieldOptionLingual>()
                        {
                            new CustomUserProfileFieldOptionLingual
                            {
                                DisplayName = "Lead", Language = "en"
                            },
                            new CustomUserProfileFieldOptionLingual
                            {
                                DisplayName = "Lead", Language = "zh-HK"
                            }
                        },
                        Order = 2
                    },
                    new CustomUserProfileFieldOption
                    {
                        Value = "Opportunity",
                        CustomUserProfileFieldOptionLinguals = new List<CustomUserProfileFieldOptionLingual>()
                        {
                            new CustomUserProfileFieldOptionLingual
                            {
                                DisplayName = "Opportunity", Language = "en"
                            },
                            new CustomUserProfileFieldOptionLingual
                            {
                                DisplayName = "Opportunity", Language = "zh-HK"
                            }
                        },
                        Order = 3
                    },
                    new CustomUserProfileFieldOption
                    {
                        Value = "Customer",
                        CustomUserProfileFieldOptionLinguals = new List<CustomUserProfileFieldOptionLingual>()
                        {
                            new CustomUserProfileFieldOptionLingual
                            {
                                DisplayName = "Customer", Language = "en"
                            },
                            new CustomUserProfileFieldOptionLingual
                            {
                                DisplayName = "Customer", Language = "zh-HK"
                            }
                        },
                        Order = 4
                    },
                    new CustomUserProfileFieldOption
                    {
                        Value = "Lost",
                        CustomUserProfileFieldOptionLinguals = new List<CustomUserProfileFieldOptionLingual>()
                        {
                            new CustomUserProfileFieldOptionLingual
                            {
                                DisplayName = "Lost", Language = "en"
                            },
                            new CustomUserProfileFieldOptionLingual
                            {
                                DisplayName = "Lost", Language = "zh-HK"
                            }
                        },
                        Order = 5
                    },
                    new CustomUserProfileFieldOption
                    {
                        Value = "Inactive",
                        CustomUserProfileFieldOptionLinguals = new List<CustomUserProfileFieldOptionLingual>()
                        {
                            new CustomUserProfileFieldOptionLingual
                            {
                                DisplayName = "Inactive / to be reviewed", Language = "en"
                            },
                            new CustomUserProfileFieldOptionLingual
                            {
                                DisplayName = "Inactive / to be reviewed", Language = "zh-HK"
                            }
                        },
                        Order = 6
                    }
                },
                IsDeletable = false,
                IsEditable = true,
                IsDefault = true
            },
            new CompanyCustomUserProfileField
            {
                FieldName = "LeadSource",
                Type = FieldDataType.Options,
                Order = 8,
                CustomUserProfileFieldLinguals = new List<CustomUserProfileFieldLingual>()
                {
                    new CustomUserProfileFieldLingual
                    {
                        DisplayName = "Lead Source", Language = "en"
                    },
                    new CustomUserProfileFieldLingual
                    {
                        DisplayName = "資料來源", Language = "zh-hk"
                    }
                },
                CustomUserProfileFieldOptions = new List<CustomUserProfileFieldOption>()
                {
                    new CustomUserProfileFieldOption
                    {
                        Value = "Organic search",
                        CustomUserProfileFieldOptionLinguals = new List<CustomUserProfileFieldOptionLingual>()
                        {
                            new CustomUserProfileFieldOptionLingual
                            {
                                DisplayName = "Organic search", Language = "en"
                            },
                            new CustomUserProfileFieldOptionLingual
                            {
                                DisplayName = "網站", Language = "zh-HK"
                            }
                        },
                        Order = 0
                    },
                    new CustomUserProfileFieldOption
                    {
                        Value = "Referrals",
                        CustomUserProfileFieldOptionLinguals = new List<CustomUserProfileFieldOptionLingual>()
                        {
                            new CustomUserProfileFieldOptionLingual
                            {
                                DisplayName = "Referrals", Language = "en"
                            },
                            new CustomUserProfileFieldOptionLingual
                            {
                                DisplayName = "推薦", Language = "zh-HK"
                            }
                        },
                        Order = 1
                    },
                    new CustomUserProfileFieldOption
                    {
                        Value = "Social media",
                        CustomUserProfileFieldOptionLinguals = new List<CustomUserProfileFieldOptionLingual>()
                        {
                            new CustomUserProfileFieldOptionLingual
                            {
                                DisplayName = "Social media", Language = "en"
                            },
                            new CustomUserProfileFieldOptionLingual
                            {
                                DisplayName = "社交媒體", Language = "zh-HK"
                            }
                        },
                        Order = 2
                    },
                    new CustomUserProfileFieldOption
                    {
                        Value = "Email marketing",
                        CustomUserProfileFieldOptionLinguals = new List<CustomUserProfileFieldOptionLingual>()
                        {
                            new CustomUserProfileFieldOptionLingual
                            {
                                DisplayName = "Email marketing", Language = "en"
                            },
                            new CustomUserProfileFieldOptionLingual
                            {
                                DisplayName = "社交媒體", Language = "zh-HK"
                            }
                        },
                        Order = 3
                    },
                    new CustomUserProfileFieldOption
                    {
                        Value = "Paid search",
                        CustomUserProfileFieldOptionLinguals = new List<CustomUserProfileFieldOptionLingual>()
                        {
                            new CustomUserProfileFieldOptionLingual
                            {
                                DisplayName = "Paid search", Language = "en"
                            },
                            new CustomUserProfileFieldOptionLingual
                            {
                                DisplayName = "付費搜索", Language = "zh-HK"
                            }
                        },
                        Order = 4
                    },
                    new CustomUserProfileFieldOption
                    {
                        Value = "Paid social",
                        CustomUserProfileFieldOptionLinguals = new List<CustomUserProfileFieldOptionLingual>()
                        {
                            new CustomUserProfileFieldOptionLingual
                            {
                                DisplayName = "Paid social", Language = "en"
                            },
                            new CustomUserProfileFieldOptionLingual
                            {
                                DisplayName = "付費社交媒體", Language = "zh-HK"
                            }
                        },
                        Order = 5
                    },
                    new CustomUserProfileFieldOption
                    {
                        Value = "Direct traffic",
                        CustomUserProfileFieldOptionLinguals = new List<CustomUserProfileFieldOptionLingual>()
                        {
                            new CustomUserProfileFieldOptionLingual
                            {
                                DisplayName = "Direct traffic", Language = "en"
                            },
                            new CustomUserProfileFieldOptionLingual
                            {
                                DisplayName = "直接流量", Language = "zh-HK"
                            }
                        },
                        Order = 6
                    },
                    new CustomUserProfileFieldOption
                    {
                        Value = "Events and seminar",
                        CustomUserProfileFieldOptionLinguals = new List<CustomUserProfileFieldOptionLingual>()
                        {
                            new CustomUserProfileFieldOptionLingual
                            {
                                DisplayName = "Events and seminar", Language = "en"
                            },
                            new CustomUserProfileFieldOptionLingual
                            {
                                DisplayName = "研討會", Language = "zh-HK"
                            }
                        },
                        Order = 7
                    },
                    new CustomUserProfileFieldOption
                    {
                        Value = "Other offline sources",
                        CustomUserProfileFieldOptionLinguals = new List<CustomUserProfileFieldOptionLingual>()
                        {
                            new CustomUserProfileFieldOptionLingual
                            {
                                DisplayName = "Other offline sources", Language = "en"
                            },
                            new CustomUserProfileFieldOptionLingual
                            {
                                DisplayName = "其他活動", Language = "zh-HK"
                            }
                        },
                        Order = 8
                    },
                    new CustomUserProfileFieldOption
                    {
                        Value = "Others",
                        CustomUserProfileFieldOptionLinguals = new List<CustomUserProfileFieldOptionLingual>()
                        {
                            new CustomUserProfileFieldOptionLingual
                            {
                                DisplayName = "Others", Language = "en"
                            },
                            new CustomUserProfileFieldOptionLingual
                            {
                                DisplayName = "其他", Language = "zh-HK"
                            }
                        },
                        Order = 9
                    }
                },
                IsDeletable = false,
                IsEditable = true,
                IsDefault = true
            },
            new CompanyCustomUserProfileField
            {
                FieldName = "Priority",
                Type = FieldDataType.Options,
                Order = 9,
                CustomUserProfileFieldLinguals = new List<CustomUserProfileFieldLingual>()
                {
                    new CustomUserProfileFieldLingual
                    {
                        DisplayName = "Priority", Language = "en"
                    },
                    new CustomUserProfileFieldLingual
                    {
                        DisplayName = "優先度", Language = "zh-hk"
                    }
                },
                CustomUserProfileFieldOptions = new List<CustomUserProfileFieldOption>()
                {
                    new CustomUserProfileFieldOption
                    {
                        Value = "High",
                        CustomUserProfileFieldOptionLinguals = new List<CustomUserProfileFieldOptionLingual>()
                        {
                            new CustomUserProfileFieldOptionLingual
                            {
                                DisplayName = "High", Language = "en"
                            },
                            new CustomUserProfileFieldOptionLingual
                            {
                                DisplayName = "高", Language = "zh-HK"
                            }
                        },
                        Order = 0
                    },
                    new CustomUserProfileFieldOption
                    {
                        Value = "Medium",
                        CustomUserProfileFieldOptionLinguals = new List<CustomUserProfileFieldOptionLingual>()
                        {
                            new CustomUserProfileFieldOptionLingual
                            {
                                DisplayName = "Medium", Language = "en"
                            },
                            new CustomUserProfileFieldOptionLingual
                            {
                                DisplayName = "中", Language = "zh-HK"
                            }
                        },
                        Order = 1
                    },
                    new CustomUserProfileFieldOption
                    {
                        Value = "Low",
                        CustomUserProfileFieldOptionLinguals = new List<CustomUserProfileFieldOptionLingual>()
                        {
                            new CustomUserProfileFieldOptionLingual
                            {
                                DisplayName = "Low", Language = "en"
                            },
                            new CustomUserProfileFieldOptionLingual
                            {
                                DisplayName = "低", Language = "zh-HK"
                            }
                        },
                        Order = 2
                    }
                }
            },
            new CompanyCustomUserProfileField
            {
                FieldName = "Country",
                Type = FieldDataType.Options,
                Order = 10,
                CustomUserProfileFieldLinguals = new List<CustomUserProfileFieldLingual>()
                {
                    new CustomUserProfileFieldLingual
                    {
                        DisplayName = "Country", Language = "en"
                    },
                    new CustomUserProfileFieldLingual
                    {
                        DisplayName = "國家", Language = "zh-hk"
                    },
                },
                CustomUserProfileFieldOptions = GetCountryList().Result,
                IsDefault = true,
                IsDeletable = false,
                IsEditable = false
            },
            new CompanyCustomUserProfileField
            {
                FieldName = "Subscriber",
                Type = FieldDataType.Boolean,
                Order = 11,
                CustomUserProfileFieldLinguals = new List<CustomUserProfileFieldLingual>()
                {
                    new CustomUserProfileFieldLingual
                    {
                        DisplayName = "Subscriber", Language = "en"
                    },
                    new CustomUserProfileFieldLingual
                    {
                        DisplayName = "訂閱者", Language = "zh-hk"
                    }
                },
                IsDeletable = false,
                IsEditable = true,
                IsDefault = true
            },
            new CompanyCustomUserProfileField
            {
                FieldName = "LastChannel",
                Type = FieldDataType.Channel,
                Order = 12,
                CustomUserProfileFieldLinguals = new List<CustomUserProfileFieldLingual>()
                {
                    new CustomUserProfileFieldLingual
                    {
                        DisplayName = "Last Channel", Language = "en"
                    },
                    new CustomUserProfileFieldLingual
                    {
                        DisplayName = "最後聯繫頻道", Language = "zh-hk"
                    }
                },
                IsEditable = false,
                IsDeletable = false,
                FieldsCategory = FieldsCategory.Message
            },
            new CompanyCustomUserProfileField
            {
                FieldName = "LastContact",
                Type = FieldDataType.DateTime,
                Order = 13,
                CustomUserProfileFieldLinguals = new List<CustomUserProfileFieldLingual>()
                {
                    new CustomUserProfileFieldLingual
                    {
                        DisplayName = "Last Contact From You", Language = "en"
                    },
                    new CustomUserProfileFieldLingual
                    {
                        DisplayName = "最後聯絡時間", Language = "zh-hk"
                    }
                },
                IsEditable = false,
                IsDeletable = false,
                FieldsCategory = FieldsCategory.Message
            },
            new CompanyCustomUserProfileField
            {
                FieldName = "LastContactFromCustomers",
                Type = FieldDataType.DateTime,
                Order = 14,
                CustomUserProfileFieldLinguals = new List<CustomUserProfileFieldLingual>()
                {
                    new CustomUserProfileFieldLingual
                    {
                        DisplayName = "Last Contact From Customers", Language = "en"
                    },
                    new CustomUserProfileFieldLingual
                    {
                        DisplayName = "客戶的最後聯絡時間", Language = "zh-hk"
                    }
                },
                IsEditable = false,
                IsDeletable = false,
                FieldsCategory = FieldsCategory.Message
            }
        };

        company.CustomUserProfileFields = new List<CompanyCustomUserProfileField>();
        foreach (var customFeild in companyCustomUserProfiles)
        {
            company.CustomUserProfileFields.Add(customFeild);
        }

        return company;
    }

    private Company AddDefaultQuickReply(Company company)
    {
        company.QuickReplies = new List<CompanyQuickReply>()
        {
            new CompanyQuickReply
            {
                Value = "Welcome",
                CompanyQuickReplyLinguals = new List<CompanyQuickReplyLingual>
                {
                    new CompanyQuickReplyLingual
                    {
                        Language = "en", Value = "Hello. Thank you for contacting. How may we help you today?"
                    }
                }
            },
            new CompanyQuickReply
            {
                Value = "Thanks",
                CompanyQuickReplyLinguals = new List<CompanyQuickReplyLingual>
                {
                    new CompanyQuickReplyLingual
                    {
                        Language = "en",
                        Value = "Thank you for your business! We look forward to working with you again."
                    }
                }
            },
            new CompanyQuickReply
            {
                Value = "Away",
                CompanyQuickReplyLinguals = new List<CompanyQuickReplyLingual>
                {
                    new CompanyQuickReplyLingual
                    {
                        Language = "en",
                        Value =
                            "Hello. Thank you for your message. We’re not here right now, but will respond as soon as we return."
                    }
                }
            },
            new CompanyQuickReply
            {
                Value = "Close",
                CompanyQuickReplyLinguals = new List<CompanyQuickReplyLingual>
                {
                    new CompanyQuickReplyLingual
                    {
                        Language = "en",
                        Value = "Let me know if you have any further questions. Our team is always here for you."
                    }
                }
            }
        };

        return company;
    }

    public async Task<List<CustomUserProfileFieldOption>> GetCountryList()
    {
        var result = await _appDbContext.CoreCountries.ToListAsync();

        if (result.Count > 0)
        {
            var response = _mapper.Map<List<CustomUserProfileFieldOption>>(result);
            return response;
        }
        else
        {
            List<Country> cultureList = new List<Country>();

            CultureInfo[] getCultureInfo = CultureInfo.GetCultures(CultureTypes.SpecificCultures);
            foreach (CultureInfo getCulture in getCultureInfo)
            {
                try
                {
                    RegionInfo getRegionInfo = new RegionInfo(getCulture.LCID);
                    if (!cultureList.Select(x => x.EnglishName).Contains(getRegionInfo.EnglishName))
                    {
                        cultureList.Add(
                            new Country
                            {
                                Id = getRegionInfo.TwoLetterISORegionName,
                                EnglishName = getRegionInfo.EnglishName,
                                DisplayName = getRegionInfo.DisplayName
                            });
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogInformation(ex, ex.Message);
                }
            }

            await _appDbContext.CoreCountries.AddRangeAsync(cultureList);
            await _appDbContext.SaveChangesAsync();

            var response = _mapper.Map<List<CustomUserProfileFieldOption>>(cultureList);
            return response;
        }
    }

    private async Task CreateResellerUserRoleIfNotExistAsync()
    {
        var roles = await _roleManager.Roles.Where(r => r.Name.StartsWith("ResellerPortal")).ToListAsync();

        if (!roles.Select(r => r.Name).Contains(ApplicationUserRole.ResellerPortalUser))
        {
            await _roleManager.CreateAsync(new IdentityRole(ApplicationUserRole.ResellerPortalUser));
        }
    }

    public string SetTransactionAction(SubscriptionPlan subscriptionPlan)
    {
        return _resellerBaseService.SetTransactionAction(subscriptionPlan);
    }

    public async Task SetSubscriptionPlanMaximumUsage(Company company)
    {
        await _resellerBaseService.SetSubscriptionPlanMaximumUsage(company);
    }

    public async Task<ResponseWrapper> UpdateResellerSubscriptionPlanConfig(
        UpdateResellerSubscriptionPlanConfigViewModel resellerSubscriptionPlanConfigViewModel)
    {
        var response = new ResponseWrapper()
        {
            IsSuccess = false
        };

        var resellerProfile = await _appDbContext.ResellerCompanyProfiles
            .FirstOrDefaultAsync(x => x.CompanyId == resellerSubscriptionPlanConfigViewModel.ResellerCompanyId);

        if (resellerProfile == null)
        {
            response.ErrorMsg = "Reseller Company Not Found";
            return response;
        }

        foreach (var subscriptionPlanId in ResellerSubscriptionPlanIds.GetAllSubscriptionPlanIds)
        {
            if (!resellerSubscriptionPlanConfigViewModel.ResellerSubscriptionPlanConfig.TryGetValue(
                    subscriptionPlanId,
                    out var planId))
            {
                continue;
            }

            if (string.IsNullOrWhiteSpace(planId))
            {
                continue;
            }

            if (subscriptionPlanId.Contains("Subscription"))
            {
                if (!ValidSubscriptionPlan.SubscriptionPlan.Contains(planId) || !await IsSubscriptionPlanExist(planId))
                {
                    response.ErrorMsg = "Subscription Plan ID is not valid.";
                    return response;
                }
            }
            else
            {
                if (!ValidSubscriptionPlan.CmsAllAddOn.Contains(planId) || !await IsSubscriptionPlanExist(planId))
                {
                    response.ErrorMsg = "Subscription Plan ID is not valid.";
                    return response;
                }
            }

            resellerProfile.ResellerSubscriptionPlanConfig[subscriptionPlanId] = planId;
        }

        resellerProfile.ResellerSubscriptionPlanConfig =
            new Dictionary<string, string>(resellerProfile.ResellerSubscriptionPlanConfig);
        resellerProfile.UpdatedAt = DateTime.UtcNow;
        await _appDbContext.SaveChangesAsync();

        response.IsSuccess = true;
        response.Data = resellerProfile.ResellerSubscriptionPlanConfig;

        await _companyInfoCacheService.RemoveCompanyInfoCache(resellerProfile.CompanyId);
        return response;
    }

    public async Task AddResellerActivityLog(ResellerActivityLog resellerActivityLog)
    {
        await _resellerBaseService.AddResellerActivityLog(resellerActivityLog);
    }

    private async Task<bool> IsSubscriptionPlanExist(string subscriptionPlanId)
    {
        var subscriptionPlan = await _appDbContext.CoreSubscriptionPlans
            .AsNoTracking()
            .Where(x => x.Id == subscriptionPlanId)
            .FirstOrDefaultAsync();

        return subscriptionPlan != null;
    }

    public async Task<ResponseWrapper> MigrateDirectClientToResellerClient(
        string resellerCompanyId,
        string clientCompanyId)
    {
        var response = new ResponseWrapper
        {
            IsSuccess = false
        };

        var resellerCompany = await _appDbContext.CompanyCompanies
            .Include(x => x.ResellerCompanyProfile)
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.Id == resellerCompanyId && x.CompanyType == CompanyType.Reseller);

        if (resellerCompany == null)
        {
            response.ErrorMsg = "Reseller Company Not Found";
            return response;
        }

        var clientCompany = await _appDbContext.CompanyCompanies
            .FirstOrDefaultAsync(x => x.Id == clientCompanyId && x.CompanyType == CompanyType.DirectClient);

        if (clientCompany == null)
        {
            response.ErrorMsg = "Client Company Not Found";
            return response;
        }

        var contactOwner = await _appDbContext.UserRoleStaffs
            .AsNoTracking()
            .OrderBy(s => s.Order)
            .ThenBy(s => s.Id)
            .FirstOrDefaultAsync(x => x.CompanyId == resellerCompanyId);

        if (contactOwner == null)
        {
            response.ErrorMsg = "Contact Owner Not Found";
            return response;
        }

        clientCompany.CompanyType = CompanyType.ResellerClient;
        await _appDbContext.SaveChangesAsync();

        var resellerClientCompanyProfile = new ResellerClientCompanyProfile()
        {
            ResellerCompanyProfileId = resellerCompany.ResellerCompanyProfile.Id,
            ClientCompanyId = clientCompany.Id,
            ContactOwnerIdentityId = contactOwner.IdentityId
        };

        await _appDbContext.ResellerClientCompanyProfiles.AddAsync(resellerClientCompanyProfile);
        await _appDbContext.SaveChangesAsync();

        var responseWrapper = await _resellerBaseService.AddResellerStaffsToClientCompanies(
            resellerCompany.ResellerCompanyProfile,
            clientCompany: clientCompany);

        if (responseWrapper.IsSuccess)
        {
            var resellerClientStaves = (List<Staff>) responseWrapper.Data;
            var resellerClientStaff = resellerClientStaves.First(x => x.IdentityId == contactOwner.IdentityId);

            var assignmentRule = new AssignmentRule
            {
                AssignmentRuleName = "Default",
                AssignmentType = AssignmentType.SpecificPerson,
                AssignedStaff = resellerClientStaff,
                CompanyId = clientCompany.Id
            };

            await _appDbContext.CompanyAssignmentRules.AddAsync(assignmentRule);
        }

        // HubSpot
        BackgroundJob.Schedule<IInternalHubSpotService>(
            x => x.SyncCompany(
                clientCompany.Id,
                null),
            TimeSpan.FromMinutes(2));

        await _companyInfoCacheService.RemoveCompanyInfoCache(clientCompanyId);

        response.IsSuccess = true;
        response.Data = "Client Migrated Successfully";
        return response;
    }
}