﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AutoMapper;
using Hangfire;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Travis_backend.AccountAuthenticationDomain.Models;
using Travis_backend.AccountAuthenticationDomain.ViewModels;
using Travis_backend.CommonDomain.ViewModels;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.CompanyDomain.Services;
using Travis_backend.ConversationServices;
using Travis_backend.Database;
using Travis_backend.Enums;
using Travis_backend.InternalDomain.Services;
using Travis_backend.ShareInvitationDomain.Models;
using Travis_backend.ShareInvitationDomain.ViewModels;

// For more information on enabling Web API for empty projects, visit https://go.microsoft.com/fwlink/?LinkID=397860

namespace Travis_backend.Controllers.CompanyManagementControllers
{
    [Authorize]
    [Route("company/invite/shared")]
    public class ShareableInvitationController : Controller
    {
        private readonly ApplicationDbContext _appDbContext;
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly IMapper _mapper;
        private readonly ILogger _logger;
        private readonly ICompanyUsageService _companyUsageService;
        private readonly ICompanyTeamService _companyTeamService;
        private readonly ICoreService _coreService;
        private readonly IConfiguration _configuration;

        public ShareableInvitationController(
            ApplicationDbContext appDbContext,
            UserManager<ApplicationUser> userManager,
            IMapper mapper,
            ILogger<ShareableInvitationController> logger,
            ICompanyUsageService companyUsageService,
            ICompanyTeamService companyTeamService,
            ICoreService coreService,
            IConfiguration configuration)
        {
            _userManager = userManager;
            _appDbContext = appDbContext;
            _mapper = mapper;
            _logger = logger;
            _companyUsageService = companyUsageService;
            _companyTeamService = companyTeamService;
            _coreService = coreService;
            _configuration = configuration;
        }

        [HttpPost]
        [Obsolete("This method is deprecated, use the new method instead.")]
        [Route("generate")]
        public async Task<IActionResult> GeneratesShareableURL(
            [FromBody]
            ShareableInvitationViewModel shareableInvitationViewModel)
        {
            var companyUser = await _coreService.GetCompanyStaff(
                await _userManager.GetUserAsync(User));

            if (companyUser == null)
            {
                return Unauthorized();
            }

            if (companyUser.RoleType == StaffUserRole.Staff)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        message = "staff role cannot invite by shareable link"
                    });
            }

            var invitation = await _appDbContext.CompanyShareableInvitations
                .Include(x => x.ShareableInvitationRecords)
                .ThenInclude(x => x.InvitedStaff.Identity)
                .FirstOrDefaultAsync(
                    x =>
                        x.TeamIds == shareableInvitationViewModel.TeamIds
                        && x.Role == shareableInvitationViewModel.Role
                        && x.GeneratedById == companyUser.Id
                        && x.Status == ShareableLinkStatus.Enabled);

            if (invitation == null)
            {
                invitation = new ShareableInvitation
                {
                    CompanyId = companyUser.CompanyId,
                    Role = shareableInvitationViewModel.Role,
                    TeamIds = shareableInvitationViewModel.TeamIds,
                    Quota = shareableInvitationViewModel.Quota,
                    ExpirationDate = shareableInvitationViewModel.ExpirationDate.ToUniversalTime(),
                    GeneratedById = companyUser.Id,
                    Status = ShareableLinkStatus.Enabled,
                };

                _appDbContext.CompanyShareableInvitations.Add(invitation);
            }

            invitation.Role = shareableInvitationViewModel.Role;
            invitation.TeamIds = shareableInvitationViewModel.TeamIds;
            invitation.Quota = shareableInvitationViewModel.Quota;
            invitation.ExpirationDate = shareableInvitationViewModel.ExpirationDate;
            invitation.UpdatedAt = DateTime.UtcNow;

            await _appDbContext.SaveChangesAsync();

            var response = _mapper.Map<ShareableInvitationResponse>(invitation);
            response.Location = _configuration["SF_ENVIRONMENT"] ?? string.Empty;

            return Ok(response);
        }

        [HttpPost]
        [Route("disable/{shareableId}")]
        public async Task<IActionResult> GeneratesShareableURL(string shareableId)
        {
            // var companyUser = await _appDbContext.UserRoleStaffs.Where(x => x.IdentityId == userId).FirstOrDefaultAsync();
            var companyUser = await _coreService.GetCompanyStaff(
                await _userManager.GetUserAsync(User));

            if (companyUser == null)
            {
                return Unauthorized();
            }

            var invitation = await _appDbContext.CompanyShareableInvitations
                .Include(x => x.ShareableInvitationRecords)
                .FirstOrDefaultAsync(
                    x =>
                        x.InvitationId == shareableId
                        && x.Status == ShareableLinkStatus.Enabled);

            if (invitation == null)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        message = "Not found"
                    });
            }

            invitation.Status = ShareableLinkStatus.Disabled;

            await _appDbContext.SaveChangesAsync();

            var response = _mapper.Map<ShareableInvitationResponse>(invitation);

            return Ok(response);
        }

        [HttpGet]
        [AllowAnonymous]
        [Obsolete("This method is deprecated, use the new method instead.")]
        [Route("{shareableId}")]
        public async Task<IActionResult> GetInviationDetails(string shareableId)
        {
            if (ModelState.IsValid)
            {
                var invitation = await _appDbContext.CompanyShareableInvitations
                    .Include(x => x.GeneratedBy.Identity)
                    .FirstOrDefaultAsync(x => x.InvitationId == shareableId);

                if (invitation == null)
                {
                    return BadRequest();
                }

                var response = _mapper.Map<ShareableInvitationResponse>(invitation);
                response.TeamNames = new List<string>();

                foreach (var teamId in response.TeamIds)
                {
                    var team = await _appDbContext.CompanyStaffTeams
                        .FirstOrDefaultAsync(x => x.Id == teamId);

                    response.TeamNames.Add(team.TeamName);
                }

                response.CompanyName = await _appDbContext.CompanyCompanies
                    .Where(x => x.Id == invitation.CompanyId)
                    .Select(x => x.CompanyName)
                    .FirstOrDefaultAsync();

                return Ok(response);
            }

            return BadRequest();
        }

        [HttpPost]
        [AllowAnonymous]
        [Obsolete("This method is deprecated, use the new method instead.")]
        [Route("{shareableId}")]
        public async Task<IActionResult> InviteByShareableURL(
            string shareableId,
            [FromBody]
            InviteSharedUserObject inviteSharedUserObject)
        {
            if (ModelState.IsValid)
            {
                var invitation = await _appDbContext.CompanyShareableInvitations
                    .Include(x => x.ShareableInvitationRecords)
                    .FirstOrDefaultAsync(x => x.InvitationId == shareableId);

                if (invitation == null)
                {
                    return BadRequest();
                }

                var usage = await _companyUsageService.GetCompanyUsage(invitation.CompanyId);

                if (usage.MaximumAgents <= usage.totalAgents)
                {
                    return BadRequest(
                        new ResponseViewModel
                        {
                            message = "Not enough agent quota, please purchase",
                            errorId = "SHARED_INVITE_EXCEEDED_COMPANY_QUOTA"
                        });
                }

                if (invitation.Status == ShareableLinkStatus.Disabled)
                {
                    return BadRequest(
                        new ResponseViewModel
                        {
                            message = "Invitation disabled", errorId = "SHARED_INVITE_DISABLED"
                        });
                }

                if (invitation.Quota <= invitation.ShareableInvitationRecords.Count)
                {
                    invitation.Status = ShareableLinkStatus.Disabled;

                    await _appDbContext.SaveChangesAsync();

                    return BadRequest(
                        new ResponseViewModel
                        {
                            message = "Exceeded quota", errorId = "SHARED_INVITE_EXCEEDED_INVITATION_QUOTA"
                        });
                }

                if (DateTime.UtcNow > invitation.ExpirationDate)
                {
                    invitation.Status = ShareableLinkStatus.Disabled;

                    await _appDbContext.SaveChangesAsync();

                    return BadRequest(
                        new ResponseViewModel
                        {
                            message = "Expired", errorId = "SHARED_INVITE_EXPIRED"
                        });
                }

                var staffId = await _appDbContext.UserRoleStaffs
                    .Where(x => x.CompanyId == invitation.CompanyId)
                    .OrderBy(x => x.Order)
                    .ThenBy(x => x.Id)
                    .Select(x => x.IdentityId)
                    .FirstOrDefaultAsync();

                var user = await _userManager.FindByEmailAsync(inviteSharedUserObject.Email);

                if (user is null)
                {
                    var identityResult = await _userManager.CreateAsync(
                        new ApplicationUser
                        {
                            UserName = inviteSharedUserObject.UserName ?? inviteSharedUserObject.Email,
                            Email = inviteSharedUserObject.Email,
                            FirstName = inviteSharedUserObject.Firstname,
                            LastName = inviteSharedUserObject.Lastname,
                            DisplayName = $"{inviteSharedUserObject.Firstname} {inviteSharedUserObject.Lastname}",
                            PhoneNumber = inviteSharedUserObject.PhoneNumber
                        },
                        inviteSharedUserObject.Password);

                    if (!identityResult.Succeeded)
                    {
                        _logger.LogError(
                            "[CompanyInvite] Company {CompanyId} create user {UserEmail} error: {Errors}",
                            invitation.CompanyId,
                            inviteSharedUserObject.Email,
                            JsonConvert.SerializeObject(identityResult.Errors));

                        return BadRequest(
                            new ResponseViewModel
                            {
                                message = identityResult.Errors.First().Description, errorId = "CREATE_USER_FAILED"
                            });
                    }

                    user = await _userManager.FindByEmailAsync(inviteSharedUserObject.Email);
                }

                var userHaveCompanyRegistered = await _appDbContext.UserRoleStaffs
                        .AnyAsync(u => u.IdentityId == user.Id);

                if (userHaveCompanyRegistered)
                {
                    return BadRequest(
                        new ResponseViewModel
                        {
                            message = $"User {user.Email} have registered a company",
                            errorId = "USER_HAVE_COMPANY_REGISTERED"
                        });
                }

                var staff = new Staff
                {
                    CompanyId = invitation.CompanyId,
                    IdentityId = user.Id,
                    Identity = user,
                    Locale = "en",
                    RoleType = invitation.Role,
                    Position = inviteSharedUserObject.Position,
                    TimeZoneInfoId = inviteSharedUserObject.TimeZoneInfoId,
                    NotificationSettingId = 1
                };

                await _appDbContext.UserRoleStaffs.AddAsync(staff);

                invitation.ShareableInvitationRecords.Add(
                    new ShareableInvitationRecord
                    {
                        InvitedStaff = staff
                    });

                invitation.Redeemed = invitation.ShareableInvitationRecords.Count;

                if (invitation.Redeemed == invitation.Quota)
                {
                    invitation.Status = ShareableLinkStatus.Disabled;
                }

                await _appDbContext.SaveChangesAsync();

                await _companyTeamService.AddOrRemoveTeam(
                    invitation.CompanyId,
                    staff.Id,
                    invitation.TeamIds);

                var company = await _appDbContext.CompanyCompanies
                    .FirstOrDefaultAsync(x => x.Id == invitation.CompanyId);

                await _coreService.UpdatePlanFreeToFreemium(staff);

                var response = _mapper.Map<AuthenticationResponse>(user);

                response.SignalRGroupName = staff.Company.SignalRGroupName;
                response.IsShopifyAccount = staff.Company.IsShopifyAccount;
                response.AssociatedCompanyIds = await _appDbContext.UserRoleStaffs.Where(x => x.IdentityId == user.Id)
                    .Select(x => x.CompanyId).ToListAsync();
                _logger.LogInformation(
                    $"[InviteByShareableURL] {response.Id}, {response.Email}, {response.SignalRGroupName}");

                BackgroundJob.Enqueue<ICoreService>(
                    x => x.AddToSleekFlowCRM(
                        staff.CompanyId,
                        user,
                        "User",
                        null,
                        null,
                        null,
                        null));

                BackgroundJob.Enqueue<IInternalHubSpotService>(
                    x => x.SyncCompanyStaffs(staff.CompanyId));

                return Ok(response);
            }

            return BadRequest(
                new ResponseViewModel
                {
                    message = ModelState?.First().Value?.Errors.First().ErrorMessage,
                    errorId = "INVALID_FORM_VALUE"
                });
        }
    }
}
