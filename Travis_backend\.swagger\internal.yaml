openapi: 3.0.1
info:
  title: Sleekflow internal Api
  description: Sleekflow internal Api
  version: internal
paths:
  /FlowHubIntegrator/Internals/Functions/AuthenticateZapierApiKey:
    post:
      tags:
        - FlowHubIntegratorInternalsFunctions
      requestBody:
        content:
          application/json-patch+json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Integrator.Model.AuthenticateZapierApiKeyInput'
          application/json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Integrator.Model.AuthenticateZapierApiKeyInput'
          text/json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Integrator.Model.AuthenticateZapierApiKeyInput'
          application/*+json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Integrator.Model.AuthenticateZapierApiKeyInput'
      responses:
        '200':
          description: OK
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Integrator.Model.AuthenticateZapierApiKeyOutput'
            application/json:
              schema:
                $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Integrator.Model.AuthenticateZapierApiKeyOutput'
            text/json:
              schema:
                $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Integrator.Model.AuthenticateZapierApiKeyOutput'
  /FlowHubIntegrator/Internals/Functions/GetSchemafulObjectZapierSample:
    post:
      tags:
        - FlowHubIntegratorInternalsFunctions
      requestBody:
        content:
          application/json-patch+json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Integrator.Model.GetSchemafulObjectZapierSampleInput'
          application/json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Integrator.Model.GetSchemafulObjectZapierSampleInput'
          text/json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Integrator.Model.GetSchemafulObjectZapierSampleInput'
          application/*+json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Integrator.Model.GetSchemafulObjectZapierSampleInput'
      responses:
        '200':
          description: OK
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Integrator.Model.GetSchemafulObjectZapierSampleOutput'
            application/json:
              schema:
                $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Integrator.Model.GetSchemafulObjectZapierSampleOutput'
            text/json:
              schema:
                $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Integrator.Model.GetSchemafulObjectZapierSampleOutput'
  /FlowHubIntegrator/Internals/Functions/GetSchemaIdByUniqueName:
    post:
      tags:
        - FlowHubIntegratorInternalsFunctions
      requestBody:
        content:
          application/json-patch+json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Integrator.Model.GetSchemaIdByUniqueNameInput'
          application/json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Integrator.Model.GetSchemaIdByUniqueNameInput'
          text/json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Integrator.Model.GetSchemaIdByUniqueNameInput'
          application/*+json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Integrator.Model.GetSchemaIdByUniqueNameInput'
      responses:
        '200':
          description: OK
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Integrator.Model.GetSchemaIdByUniqueNameOutput'
            application/json:
              schema:
                $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Integrator.Model.GetSchemaIdByUniqueNameOutput'
            text/json:
              schema:
                $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Integrator.Model.GetSchemaIdByUniqueNameOutput'
  /FlowHub/Internals/Commands/AddInternalNoteToContact:
    post:
      tags:
        - FlowHubInternalsCommands
      requestBody:
        content:
          application/json-patch+json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.AddInternalNoteToContactInput'
          application/json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.AddInternalNoteToContactInput'
          text/json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.AddInternalNoteToContactInput'
          application/*+json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.AddInternalNoteToContactInput'
      responses:
        '200':
          description: OK
  /FlowHub/Internals/Commands/SendMessage:
    post:
      tags:
        - FlowHubInternalsCommands
      requestBody:
        content:
          application/json-patch+json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.SendMessageInput'
          application/json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.SendMessageInput'
          text/json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.SendMessageInput'
          application/*+json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.SendMessageInput'
      responses:
        '200':
          description: OK
  /FlowHub/Internals/Commands/UpdateContactCollaboratorRelationships:
    post:
      tags:
        - FlowHubInternalsCommands
      requestBody:
        content:
          application/json-patch+json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.UpdateContactCollaboratorRelationshipsInput'
          application/json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.UpdateContactCollaboratorRelationshipsInput'
          text/json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.UpdateContactCollaboratorRelationshipsInput'
          application/*+json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.UpdateContactCollaboratorRelationshipsInput'
      responses:
        '200':
          description: OK
  /FlowHub/Internals/Commands/UpdateContactConversationStatus:
    post:
      tags:
        - FlowHubInternalsCommands
      requestBody:
        content:
          application/json-patch+json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.UpdateContactConversationStatusInput'
          application/json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.UpdateContactConversationStatusInput'
          text/json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.UpdateContactConversationStatusInput'
          application/*+json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.UpdateContactConversationStatusInput'
      responses:
        '200':
          description: OK
  /FlowHub/Internals/Commands/UpdateContactLabelRelationships:
    post:
      tags:
        - FlowHubInternalsCommands
      requestBody:
        content:
          application/json-patch+json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.UpdateContactLabelRelationshipsInput'
          application/json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.UpdateContactLabelRelationshipsInput'
          text/json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.UpdateContactLabelRelationshipsInput'
          application/*+json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.UpdateContactLabelRelationshipsInput'
      responses:
        '200':
          description: OK
  /FlowHub/Internals/Commands/UpdateContactListRelationships:
    post:
      tags:
        - FlowHubInternalsCommands
      requestBody:
        content:
          application/json-patch+json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.UpdateContactListRelationshipsInput'
          application/json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.UpdateContactListRelationshipsInput'
          text/json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.UpdateContactListRelationshipsInput'
          application/*+json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.UpdateContactListRelationshipsInput'
      responses:
        '200':
          description: OK
  /FlowHub/Internals/Commands/UpdateContactOwnerRelationships:
    post:
      tags:
        - FlowHubInternalsCommands
      requestBody:
        content:
          application/json-patch+json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.UpdateContactOwnerRelationshipsInput'
          application/json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.UpdateContactOwnerRelationshipsInput'
          text/json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.UpdateContactOwnerRelationshipsInput'
          application/*+json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.UpdateContactOwnerRelationshipsInput'
      responses:
        '200':
          description: OK
  /FlowHub/Internals/Commands/UpdateContactProperties:
    post:
      tags:
        - FlowHubInternalsCommands
      requestBody:
        content:
          application/json-patch+json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.UpdateContactPropertiesInput'
          application/json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.UpdateContactPropertiesInput'
          text/json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.UpdateContactPropertiesInput'
          application/*+json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.UpdateContactPropertiesInput'
      responses:
        '200':
          description: OK
  /FlowHub/Internals/Commands/CreateSalesforceObject:
    post:
      tags:
        - FlowHubInternalsCommands
      requestBody:
        content:
          application/json-patch+json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.CreateSalesforceObjectInput'
          application/json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.CreateSalesforceObjectInput'
          text/json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.CreateSalesforceObjectInput'
          application/*+json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.CreateSalesforceObjectInput'
      responses:
        '200':
          description: OK
  /FlowHub/Internals/Commands/UpdateSalesforceObject:
    post:
      tags:
        - FlowHubInternalsCommands
      requestBody:
        content:
          application/json-patch+json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.UpdateSalesforceObjectInput'
          application/json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.UpdateSalesforceObjectInput'
          text/json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.UpdateSalesforceObjectInput'
          application/*+json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.UpdateSalesforceObjectInput'
      responses:
        '200':
          description: OK
  /FlowHub/Internals/Commands/SearchSalesforceObject:
    post:
      tags:
        - FlowHubInternalsCommands
      requestBody:
        content:
          application/json-patch+json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.SearchSalesforceObjectInput'
          application/json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.SearchSalesforceObjectInput'
          text/json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.SearchSalesforceObjectInput'
          application/*+json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.SearchSalesforceObjectInput'
      responses:
        '200':
          description: OK
  /FlowHub/Internals/Commands/CreateContact:
    post:
      tags:
        - FlowHubInternalsCommands
      requestBody:
        content:
          application/json-patch+json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.CreateContactInput'
          application/json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.CreateContactInput'
          text/json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.CreateContactInput'
          application/*+json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.CreateContactInput'
      responses:
        '200':
          description: OK
  /FlowHub/Internals/Commands/CreateContactWithSalesforceUserMapping:
    post:
      tags:
        - FlowHubInternalsCommands
      requestBody:
        content:
          application/json-patch+json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.CreateContactWithSalesforceUserMappingInput'
          application/json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.CreateContactWithSalesforceUserMappingInput'
          text/json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.CreateContactWithSalesforceUserMappingInput'
          application/*+json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.CreateContactWithSalesforceUserMappingInput'
      responses:
        '200':
          description: OK
  /FlowHub/Internals/Commands/GetConversionLastMessages:
    post:
      tags:
        - FlowHubInternalsCommands
      requestBody:
        content:
          application/json-patch+json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetConversationLastMessagesInput'
          application/json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetConversationLastMessagesInput'
          text/json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetConversationLastMessagesInput'
          application/*+json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetConversationLastMessagesInput'
      responses:
        '200':
          description: OK
  /FlowHub/Internals/Commands/UpdateContactPropertiesByPropertyKey:
    post:
      tags:
        - FlowHubInternalsCommands
      requestBody:
        content:
          application/json-patch+json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.UpdateContactPropertiesByPropertyKeyInput'
          application/json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.UpdateContactPropertiesByPropertyKeyInput'
          text/json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.UpdateContactPropertiesByPropertyKeyInput'
          application/*+json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.UpdateContactPropertiesByPropertyKeyInput'
      responses:
        '200':
          description: OK
  /FlowHub/Internals/Functions/GetContact:
    post:
      tags:
        - FlowHubInternalsFunctions
      requestBody:
        content:
          application/json-patch+json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetContactInput'
          application/json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetContactInput'
          text/json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetContactInput'
          application/*+json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetContactInput'
      responses:
        '200':
          description: OK
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetContactOutput'
            application/json:
              schema:
                $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetContactOutput'
            text/json:
              schema:
                $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetContactOutput'
  /FlowHub/Internals/Functions/GetStaff:
    post:
      tags:
        - FlowHubInternalsFunctions
      requestBody:
        content:
          application/json-patch+json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetStaffInput'
          application/json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetStaffInput'
          text/json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetStaffInput'
          application/*+json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetStaffInput'
      responses:
        '200':
          description: OK
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetStaffOutput'
            application/json:
              schema:
                $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetStaffOutput'
            text/json:
              schema:
                $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetStaffOutput'
  /FlowHub/Internals/Functions/GetContactLists:
    post:
      tags:
        - FlowHubInternalsFunctions
      requestBody:
        content:
          application/json-patch+json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetContactListsInput'
          application/json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetContactListsInput'
          text/json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetContactListsInput'
          application/*+json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetContactListsInput'
      responses:
        '200':
          description: OK
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetContactListsOutput'
            application/json:
              schema:
                $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetContactListsOutput'
            text/json:
              schema:
                $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetContactListsOutput'
  /FlowHub/Internals/Functions/GetContactConversation:
    post:
      tags:
        - FlowHubInternalsFunctions
      requestBody:
        content:
          application/json-patch+json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetContactConversationInput'
          application/json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetContactConversationInput'
          text/json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetContactConversationInput'
          application/*+json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetContactConversationInput'
      responses:
        '200':
          description: OK
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetContactConversationOutput'
            application/json:
              schema:
                $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetContactConversationOutput'
            text/json:
              schema:
                $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetContactConversationOutput'
  /FlowHub/Internals/Functions/GetContactIdByPhoneNumber:
    post:
      tags:
        - FlowHubInternalsFunctions
      requestBody:
        content:
          application/json-patch+json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetContactIdByPhoneNumberInput'
          application/json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetContactIdByPhoneNumberInput'
          text/json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetContactIdByPhoneNumberInput'
          application/*+json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetContactIdByPhoneNumberInput'
      responses:
        '200':
          description: OK
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetContactIdOutput'
            application/json:
              schema:
                $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetContactIdOutput'
            text/json:
              schema:
                $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetContactIdOutput'
  /FlowHub/Internals/Functions/GetOrCreateContactIdByPhoneNumber:
    post:
      tags:
        - FlowHubInternalsFunctions
      requestBody:
        content:
          application/json-patch+json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetOrCreateContactIdByPhoneNumberInput'
          application/json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetOrCreateContactIdByPhoneNumberInput'
          text/json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetOrCreateContactIdByPhoneNumberInput'
          application/*+json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetOrCreateContactIdByPhoneNumberInput'
      responses:
        '200':
          description: OK
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetOrCreateContactIdOutput'
            application/json:
              schema:
                $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetOrCreateContactIdOutput'
            text/json:
              schema:
                $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetOrCreateContactIdOutput'
  /FlowHub/Internals/Functions/GetContactIdByEmail:
    post:
      tags:
        - FlowHubInternalsFunctions
      requestBody:
        content:
          application/json-patch+json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetContactIdByEmailInput'
          application/json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetContactIdByEmailInput'
          text/json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetContactIdByEmailInput'
          application/*+json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetContactIdByEmailInput'
      responses:
        '200':
          description: OK
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetContactIdOutput'
            application/json:
              schema:
                $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetContactIdOutput'
            text/json:
              schema:
                $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetContactIdOutput'
  /FlowHub/Internals/Functions/GetOrCreateContactIdByEmail:
    post:
      tags:
        - FlowHubInternalsFunctions
      requestBody:
        content:
          application/json-patch+json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetOrCreateContactIdByEmailInput'
          application/json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetOrCreateContactIdByEmailInput'
          text/json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetOrCreateContactIdByEmailInput'
          application/*+json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetOrCreateContactIdByEmailInput'
      responses:
        '200':
          description: OK
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetOrCreateContactIdOutput'
            application/json:
              schema:
                $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetOrCreateContactIdOutput'
            text/json:
              schema:
                $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetOrCreateContactIdOutput'
  /FlowHub/Internals/Functions/GetConversationChannelLastMessage:
    post:
      tags:
        - FlowHubInternalsFunctions
      requestBody:
        content:
          application/json-patch+json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetConversationChannelLastMessageInput'
          application/json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetConversationChannelLastMessageInput'
          text/json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetConversationChannelLastMessageInput'
          application/*+json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetConversationChannelLastMessageInput'
      responses:
        '200':
          description: OK
          content:
            text/plain:
              schema:
                $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetConversationChannelLastMessageOutput'
            application/json:
              schema:
                $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetConversationChannelLastMessageOutput'
            text/json:
              schema:
                $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.GetConversationChannelLastMessageOutput'
  /FlowHub/Internals/ServicesCall/EmailNotificationService/SendWorkflowInfiniteLoopEmail:
    post:
      tags:
        - FlowHubInternalsServicesCall
      requestBody:
        content:
          application/json-patch+json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.SendWorkflowInfiniteLoopEmailInput'
          application/json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.SendWorkflowInfiniteLoopEmailInput'
          text/json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.SendWorkflowInfiniteLoopEmailInput'
          application/*+json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.SendWorkflowInfiniteLoopEmailInput'
      responses:
        '200':
          description: OK
  /FlowHub/Internals/ServicesCall/EmailNotificationService/SendExecutionUsageReachedThresholdEmail:
    post:
      tags:
        - FlowHubInternalsServicesCall
      requestBody:
        content:
          application/json-patch+json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.SendExecutionUsageReachedThresholdEmailInput'
          application/json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.SendExecutionUsageReachedThresholdEmailInput'
          text/json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.SendExecutionUsageReachedThresholdEmailInput'
          application/*+json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.SendExecutionUsageReachedThresholdEmailInput'
      responses:
        '200':
          description: OK
  /IntelligentHub/Internals/Commands/GetUserProfile:
    post:
      tags:
        - IntelligentHubInternalCommands
      requestBody:
        content:
          application/json-patch+json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.IntelligentHub.Model.GetUserProfileInput'
          application/json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.IntelligentHub.Model.GetUserProfileInput'
          text/json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.IntelligentHub.Model.GetUserProfileInput'
          application/*+json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.IntelligentHub.Model.GetUserProfileInput'
      responses:
        '200':
          description: OK
  /IntelligentHub/Internals/Commands/CreateUserProfile:
    post:
      tags:
        - IntelligentHubInternalCommands
      requestBody:
        content:
          application/json-patch+json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.IntelligentHub.Model.CreateUserProfileInput'
          application/json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.IntelligentHub.Model.CreateUserProfileInput'
          text/json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.IntelligentHub.Model.CreateUserProfileInput'
          application/*+json:
            schema:
              $ref: '#/components/schemas/Sleekflow.Apis.IntelligentHub.Model.CreateUserProfileInput'
      responses:
        '200':
          description: OK
  /TicketingHub/Internals/CreateTicketConversationIndicator:
    post:
      tags:
        - TicketingHubInternal
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Travis_backend.TicketingHubDomain.Controllers.TicketingHubInternalController.CreateTicketConversationIndicatorInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Travis_backend.TicketingHubDomain.Controllers.TicketingHubInternalController.CreateTicketConversationIndicatorOutput'
components:
  schemas:
    Sleekflow.Apis.FlowHub.Integrator.Model.AuthenticateZapierApiKeyInput:
      type: object
      properties:
        api_key:
          type: string
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Integrator.Model.AuthenticateZapierApiKeyOutput:
      type: object
      properties:
        sleekflow_company_id:
          type: string
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Integrator.Model.GetSchemaIdByUniqueNameInput:
      type: object
      properties:
        schema_unique_name:
          type: string
          nullable: true
        sleekflow_company_id:
          type: string
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Integrator.Model.GetSchemaIdByUniqueNameOutput:
      type: object
      properties:
        schemaId:
          type: string
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Integrator.Model.GetSchemafulObjectZapierSampleInput:
      type: object
      properties:
        schema_unique_name:
          type: string
          nullable: true
        sleekflow_company_id:
          type: string
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Integrator.Model.GetSchemafulObjectZapierSampleOutput:
      type: object
      properties:
        schemafulObjectZapierViewModel:
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.ActionMessageObject:
      type: object
      properties:
        flow_token:
          type: string
          nullable: true
        flow_action_data:
          type: object
          additionalProperties: { }
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.AddInternalNoteToContactInput:
      type: object
      properties:
        state_id:
          type: string
          nullable: true
        state_identity:
          $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.StateIdentity'
        contact_id:
          type: string
          nullable: true
        content:
          type: string
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.AudioMessageObject:
      type: object
      properties:
        id:
          type: string
          nullable: true
        link:
          type: string
          nullable: true
        caption:
          type: string
          nullable: true
        filename:
          type: string
          nullable: true
        provider:
          $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.MediaMessageObjectProvider'
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.ButtonReplyMessageObject:
      type: object
      properties:
        id:
          type: string
          nullable: true
        title:
          type: string
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.ContactConversation:
      type: object
      properties:
        conversation_status:
          type: string
          nullable: true
        conversation_id:
          type: string
          nullable: true
        last_message_channel:
          type: string
          nullable: true
        last_message_channel_id:
          type: string
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.ContactList:
      type: object
      properties:
        id:
          type: string
          nullable: true
        name:
          type: string
          nullable: true
        added_at:
          type: string
          format: date-time
        is_imported:
          type: boolean
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.ContactMessageObject:
      type: object
      properties:
        addresses:
          type: array
          items:
            $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.ContactMessageObjectAddress'
          nullable: true
        birthday:
          type: string
          nullable: true
        emails:
          type: array
          items:
            $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.ContactMessageObjectEmail'
          nullable: true
        name:
          $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.ContactMessageObjectName'
        org:
          $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.ContactMessageObjectOrg'
        ims:
          type: array
          items:
            $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.ContactMessageObjectIm'
          nullable: true
        phones:
          type: array
          items:
            $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.ContactMessageObjectPhone'
          nullable: true
        urls:
          type: array
          items:
            $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.ContactMessageObjectUrl'
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.ContactMessageObjectAddress:
      type: object
      properties:
        street:
          type: string
          nullable: true
        city:
          type: string
          nullable: true
        state:
          type: string
          nullable: true
        zip:
          type: string
          nullable: true
        country:
          type: string
          nullable: true
        country_code:
          type: string
          nullable: true
        type:
          type: string
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.ContactMessageObjectEmail:
      type: object
      properties:
        email:
          type: string
          nullable: true
        type:
          type: string
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.ContactMessageObjectIm:
      type: object
      properties:
        service:
          type: string
          nullable: true
        user_id:
          type: string
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.ContactMessageObjectName:
      type: object
      properties:
        formatted_name:
          type: string
          nullable: true
        first_name:
          type: string
          nullable: true
        last_name:
          type: string
          nullable: true
        middle_name:
          type: string
          nullable: true
        suffix:
          type: string
          nullable: true
        prefix:
          type: string
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.ContactMessageObjectOrg:
      type: object
      properties:
        company:
          type: string
          nullable: true
        title:
          type: string
          nullable: true
        department:
          type: string
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.ContactMessageObjectPhone:
      type: object
      properties:
        phone:
          type: string
          nullable: true
        type:
          type: string
          nullable: true
        wa_id:
          type: string
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.ContactMessageObjectUrl:
      type: object
      properties:
        url:
          type: string
          nullable: true
        type:
          type: string
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.ConversationChannelLastMessage:
      type: object
      properties:
        conversation_id:
          type: string
          nullable: true
        message_id:
          type: string
          nullable: true
        message_unique_id:
          type: string
          nullable: true
        message_status:
          type: string
          nullable: true
        message_type:
          type: string
          nullable: true
        message_delivery_type:
          type: string
          nullable: true
        channel:
          type: string
          nullable: true
        channel_id:
          type: string
          nullable: true
        message_content:
          type: string
          nullable: true
        created_at:
          type: string
          format: date-time
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.CreateContactInput:
      type: object
      properties:
        state_id:
          type: string
          nullable: true
        state_identity:
          $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.StateIdentity'
        phone_number:
          type: string
          nullable: true
        contact_properties:
          type: object
          additionalProperties: { }
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.CreateContactWithSalesforceUserMappingInput:
      type: object
      properties:
        state_id:
          type: string
          nullable: true
        state_identity:
          $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.StateIdentity'
        phone_number:
          type: string
          nullable: true
        salesforce_user_id_for_mapping:
          type: string
          nullable: true
        salesforce_connection_id:
          type: string
          nullable: true
        contact_properties:
          type: object
          additionalProperties: { }
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.CreateSalesforceObjectInput:
      type: object
      properties:
        state_id:
          type: string
          nullable: true
        state_identity:
          $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.StateIdentity'
        salesforce_connection_id:
          type: string
          nullable: true
        object_type:
          type: string
          nullable: true
        is_custom_object:
          type: boolean
        is_set_owner_by_user_mapping_config:
          type: boolean
        sleekflow_user_id_for_mapping:
          type: string
          nullable: true
        object_properties:
          type: object
          additionalProperties: { }
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.CurrencyMessageObject:
      type: object
      properties:
        fallback_value:
          type: string
          nullable: true
        code:
          type: string
          nullable: true
        amount_1000:
          type: integer
          format: int32
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.DateTimeMessageObject:
      type: object
      properties:
        fallback_value:
          type: string
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.DocumentMessageObject:
      type: object
      properties:
        id:
          type: string
          nullable: true
        link:
          type: string
          nullable: true
        caption:
          type: string
          nullable: true
        filename:
          type: string
          nullable: true
        provider:
          $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.MediaMessageObjectProvider'
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.FacebookMessengerMessageObject:
      type: object
      properties:
        message:
          $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.FacebookPageMessengerMessageObject'
        messaging_type:
          type: string
          nullable: true
        tag:
          type: string
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.FacebookPageMessengerAttachmentObject:
      type: object
      properties:
        type:
          type: string
          nullable: true
        payload:
          $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.FacebookPageMessengerPayloadObject'
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.FacebookPageMessengerMessageObject:
      type: object
      properties:
        attachment:
          $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.FacebookPageMessengerAttachmentObject'
        text:
          type: string
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.FacebookPageMessengerPayloadObject:
      type: object
      properties:
        url:
          type: string
          nullable: true
        is_reusable:
          type: boolean
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.GetContactConversationInput:
      type: object
      properties:
        sleekflow_company_id:
          type: string
          nullable: true
        sleekflow_contact_id:
          type: string
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.GetContactConversationOutput:
      type: object
      properties:
        conversation:
          $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.ContactConversation'
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.GetContactIdByEmailInput:
      type: object
      properties:
        sleekflow_company_id:
          type: string
          nullable: true
        email:
          type: string
          nullable: true
        workflow_id:
          type: string
          nullable: true
        workflow_versioned_id:
          type: string
          nullable: true
        workflow_name:
          type: string
          nullable: true
        state_id:
          type: string
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.GetContactIdByPhoneNumberInput:
      type: object
      properties:
        sleekflow_company_id:
          type: string
          nullable: true
        phone_number:
          type: string
          nullable: true
        workflow_id:
          type: string
          nullable: true
        workflow_versioned_id:
          type: string
          nullable: true
        workflow_name:
          type: string
          nullable: true
        state_id:
          type: string
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.GetContactIdOutput:
      type: object
      properties:
        contact_id:
          type: string
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.GetContactInput:
      type: object
      properties:
        sleekflow_company_id:
          type: string
          nullable: true
        sleekflow_contact_id:
          type: string
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.GetContactListsInput:
      type: object
      properties:
        sleekflow_company_id:
          type: string
          nullable: true
        sleekflow_contact_id:
          type: string
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.GetContactListsOutput:
      type: object
      properties:
        lists:
          type: array
          items:
            $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.ContactList'
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.GetContactOutput:
      type: object
      properties:
        contact:
          type: object
          additionalProperties: { }
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.GetConversationChannelLastMessageInput:
      type: object
      properties:
        sleekflow_company_id:
          type: string
          nullable: true
        channel:
          type: string
          nullable: true
        channel_id:
          type: string
          nullable: true
        conversation_id:
          type: string
          nullable: true
        is_sent_from_sleekflow:
          type: boolean
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.GetConversationChannelLastMessageOutput:
      type: object
      properties:
        last_message:
          $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.ConversationChannelLastMessage'
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.GetConversationLastMessagesInput:
      type: object
      properties:
        state_identity:
          $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.StateIdentity'
        contact_id:
          type: string
          nullable: true
        offset:
          type: integer
          format: int32
        limit:
          type: integer
          format: int32
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.GetOrCreateContactIdByEmailInput:
      type: object
      properties:
        sleekflow_company_id:
          type: string
          nullable: true
        email:
          type: string
          nullable: true
        workflow_id:
          type: string
          nullable: true
        workflow_versioned_id:
          type: string
          nullable: true
        workflow_name:
          type: string
          nullable: true
        state_id:
          type: string
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.GetOrCreateContactIdByPhoneNumberInput:
      type: object
      properties:
        sleekflow_company_id:
          type: string
          nullable: true
        phone_number:
          type: string
          nullable: true
        workflow_id:
          type: string
          nullable: true
        workflow_versioned_id:
          type: string
          nullable: true
        workflow_name:
          type: string
          nullable: true
        state_id:
          type: string
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.GetOrCreateContactIdOutput:
      type: object
      properties:
        contact_id:
          type: string
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.GetStaffInput:
      type: object
      properties:
        sleekflow_company_id:
          type: string
          nullable: true
        sleekflow_staff_id:
          type: string
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.GetStaffOutput:
      type: object
      properties:
        staff:
          type: object
          additionalProperties: { }
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.ImageMessageObject:
      type: object
      properties:
        id:
          type: string
          nullable: true
        link:
          type: string
          nullable: true
        caption:
          type: string
          nullable: true
        filename:
          type: string
          nullable: true
        provider:
          $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.MediaMessageObjectProvider'
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.InstagramMessengerMessageObject:
      type: object
      properties:
        message:
          $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.InstagramPageMessengerMessageObject'
        messaging_type:
          type: string
          nullable: true
        tag:
          type: string
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.InstagramPageMessengerAttachmentDataObject:
      type: object
      properties:
        type:
          type: string
          nullable: true
        payload:
          $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.InstagramPageMessengerPayloadObject'
        is_reusable:
          type: boolean
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.InstagramPageMessengerMessageObject:
      type: object
      properties:
        attachment:
          $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.InstagramPageMessengerAttachmentDataObject'
        text:
          type: string
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.InstagramPageMessengerPayloadObject:
      type: object
      properties:
        id:
          type: string
          nullable: true
        url:
          type: string
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.InteractiveMessageObject:
      type: object
      properties:
        type:
          type: string
          nullable: true
        header:
          $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.InteractiveMessageObjectHeader'
        body:
          $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.InteractiveMessageObjectBody'
        footer:
          $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.InteractiveMessageObjectFooter'
        action:
          $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.InteractiveMessageObjectAction'
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.InteractiveMessageObjectAction:
      type: object
      properties:
        button:
          type: string
          nullable: true
        buttons:
          type: array
          items:
            $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.InteractiveMessageObjectActionButton'
          nullable: true
        sections:
          type: array
          items:
            $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.InteractiveMessageObjectActionSection'
          nullable: true
        catalog_id:
          type: string
          nullable: true
        product_retailer_id:
          type: string
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.InteractiveMessageObjectActionButton:
      type: object
      properties:
        type:
          type: string
          nullable: true
        reply:
          $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.InteractiveMessageObjectActionButtonReply'
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.InteractiveMessageObjectActionButtonReply:
      type: object
      properties:
        id:
          type: string
          nullable: true
        title:
          type: string
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.InteractiveMessageObjectActionSection:
      type: object
      properties:
        title:
          type: string
          nullable: true
        product_items:
          type: array
          items:
            $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.InteractiveMessageObjectActionSectionProductItem'
          nullable: true
        rows:
          type: array
          items:
            $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.InteractiveMessageObjectActionSectionRow'
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.InteractiveMessageObjectActionSectionProductItem:
      type: object
      properties:
        product_retailer_id:
          type: string
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.InteractiveMessageObjectActionSectionRow:
      type: object
      properties:
        id:
          type: string
          nullable: true
        title:
          type: string
          nullable: true
        description:
          type: string
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.InteractiveMessageObjectBody:
      type: object
      properties:
        text:
          type: string
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.InteractiveMessageObjectFooter:
      type: object
      properties:
        text:
          type: string
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.InteractiveMessageObjectHeader:
      type: object
      properties:
        type:
          type: string
          nullable: true
        text:
          type: string
          nullable: true
        video:
          $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.VideoMessageObject'
        image:
          $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.ImageMessageObject'
        document:
          $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.DocumentMessageObject'
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.InteractiveReplyMessageObject:
      type: object
      properties:
        type:
          type: string
          nullable: true
        button_reply:
          $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.ButtonReplyMessageObject'
        list_reply:
          $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.ListReplyMessageObject'
        nfm_reply:
          $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.NfmReplyMessageObject'
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.ListReplyMessageObject:
      type: object
      properties:
        id:
          type: string
          nullable: true
        title:
          type: string
          nullable: true
        description:
          type: string
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.LocationMessageObject:
      type: object
      properties:
        latitude:
          type: number
          format: double
          nullable: true
        longitude:
          type: number
          format: double
          nullable: true
        name:
          type: string
          nullable: true
        address:
          type: string
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.MediaMessageObjectProvider:
      type: object
      properties:
        name:
          type: string
          nullable: true
        type:
          type: string
          nullable: true
        config:
          $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.MediaMessageObjectProviderConfig'
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.MediaMessageObjectProviderConfig:
      type: object
      properties:
        basic:
          $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.MediaMessageObjectProviderConfigBasic'
        bearer:
          $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.MediaMessageObjectProviderConfigBearer'
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.MediaMessageObjectProviderConfigBasic:
      type: object
      properties:
        username:
          type: string
          nullable: true
        password:
          type: string
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.MediaMessageObjectProviderConfigBearer:
      type: object
      properties:
        bearer:
          type: string
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.MessageBody:
      type: object
      properties:
        audio_message:
          $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.AudioMessageObject'
        contacts_message:
          type: array
          items:
            $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.ContactMessageObject'
          nullable: true
        currency_message:
          $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.CurrencyMessageObject'
        document_message:
          $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.DocumentMessageObject'
        image_message:
          $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.ImageMessageObject'
        location_message:
          $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.LocationMessageObject'
        reaction_message:
          $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.ReactionMessageObject'
        text_message:
          $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.TextMessageObject'
        video_message:
          $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.VideoMessageObject'
        date_time_message:
          $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.DateTimeMessageObject'
        interactive_message:
          $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.InteractiveMessageObject'
        template_message:
          $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.TemplateMessageObject'
        interactive_reply_message:
          $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.InteractiveReplyMessageObject'
        facebook_messenger_message:
          $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.FacebookMessengerMessageObject'
        instagram_messenger_message:
          $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.InstagramMessengerMessageObject'
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.NfmReplyMessageObject:
      type: object
      properties:
        name:
          type: string
          nullable: true
        body:
          type: string
          nullable: true
        response_json:
          type: string
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.ReactionMessageObject:
      type: object
      properties:
        message_id:
          type: string
          nullable: true
        emoji:
          type: string
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.SearchObjectCondition:
      type: object
      properties:
        field_name:
          type: string
          nullable: true
        operator:
          type: string
          nullable: true
        value:
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.SearchSalesforceObjectInput:
      type: object
      properties:
        state_id:
          type: string
          nullable: true
        state_identity:
          $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.StateIdentity'
        salesforce_connection_id:
          type: string
          nullable: true
        object_type:
          type: string
          nullable: true
        is_custom_object:
          type: boolean
        conditions:
          type: array
          items:
            $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.SearchObjectCondition'
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.SendExecutionUsageReachedThresholdEmailInput:
      type: object
      properties:
        sleekflow_company_id:
          type: string
          nullable: true
        threshold:
          type: number
          format: double
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.SendMessageInput:
      type: object
      properties:
        state_id:
          type: string
          nullable: true
        state_identity:
          $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.StateIdentity'
        channel:
          type: string
          nullable: true
        from_to:
          $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.SendMessageInputFromTo'
        message_type:
          type: string
          nullable: true
        message_body:
          $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.MessageBody'
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.SendMessageInputFromTo:
      type: object
      properties:
        from_to_type:
          type: string
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.SendWorkflowInfiniteLoopEmailInput:
      type: object
      properties:
        sleekflow_company_id:
          type: string
          nullable: true
        workflow_id:
          type: string
          nullable: true
        workflow_name:
          type: string
          nullable: true
        created_at:
          type: string
          format: date-time
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.StateIdentity:
      type: object
      properties:
        sleekflow_company_id:
          type: string
          nullable: true
        object_id:
          type: string
          nullable: true
        workflow_id:
          type: string
          nullable: true
        workflow_versioned_id:
          type: string
          nullable: true
        object_type:
          type: string
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.TemplateMessageObject:
      type: object
      properties:
        template_name:
          type: string
          nullable: true
        language:
          type: string
          nullable: true
        components:
          type: array
          items:
            $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.TemplateMessageObjectComponent'
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.TemplateMessageObjectComponent:
      type: object
      properties:
        type:
          type: string
          nullable: true
        sub_type:
          type: string
          nullable: true
        index:
          type: integer
          format: int32
        parameters:
          type: array
          items:
            $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.TemplateMessageObjectComponentParameter'
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.TemplateMessageObjectComponentParameter:
      type: object
      properties:
        type:
          type: string
          nullable: true
        text:
          type: string
          nullable: true
        payload:
          type: string
          nullable: true
        image:
          $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.ImageMessageObject'
        audio:
          $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.AudioMessageObject'
        document:
          $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.DocumentMessageObject'
        video:
          $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.VideoMessageObject'
        location:
          $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.LocationMessageObject'
        currency:
          $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.CurrencyMessageObject'
        date_time:
          $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.DateTimeMessageObject'
        action:
          $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.ActionMessageObject'
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.TextMessageObject:
      type: object
      properties:
        text:
          type: string
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.UpdateContactCollaboratorRelationshipsInput:
      type: object
      properties:
        state_id:
          type: string
          nullable: true
        state_identity:
          $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.StateIdentity'
        contact_id:
          type: string
          nullable: true
        add_staff_ids:
          type: array
          items:
            type: string
          nullable: true
        remove_staff_ids:
          type: array
          items:
            type: string
          nullable: true
        set_staff_ids:
          type: array
          items:
            type: string
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.UpdateContactConversationStatusInput:
      type: object
      properties:
        state_id:
          type: string
          nullable: true
        state_identity:
          $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.StateIdentity'
        contact_id:
          type: string
          nullable: true
        status:
          type: string
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.UpdateContactLabelRelationshipsInput:
      type: object
      properties:
        state_id:
          type: string
          nullable: true
        state_identity:
          $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.StateIdentity'
        contact_id:
          type: string
          nullable: true
        add_labels:
          type: array
          items:
            type: string
          nullable: true
        remove_labels:
          type: array
          items:
            type: string
          nullable: true
        set_labels:
          type: array
          items:
            type: string
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.UpdateContactListRelationshipsInput:
      type: object
      properties:
        state_id:
          type: string
          nullable: true
        state_identity:
          $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.StateIdentity'
        contact_id:
          type: string
          nullable: true
        add_list_ids:
          type: array
          items:
            type: string
          nullable: true
        remove_list_ids:
          type: array
          items:
            type: string
          nullable: true
        set_list_ids:
          type: array
          items:
            type: string
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.UpdateContactOwnerRelationshipsInput:
      type: object
      properties:
        state_id:
          type: string
          nullable: true
        state_identity:
          $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.StateIdentity'
        contact_id:
          type: string
          nullable: true
        is_unassigned:
          type: boolean
        assignment_strategy:
          type: string
          nullable: true
        team_id:
          type: string
          nullable: true
        staff_id:
          type: string
          nullable: true
        assignment_counter:
          type: integer
          format: int64
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.UpdateContactPropertiesByPropertyKeyInput:
      type: object
      properties:
        state_id:
          type: string
          nullable: true
        state_identity:
          $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.StateIdentity'
        contact_property_key_id:
          type: string
          nullable: true
        contact_property_key_value:
          type: string
          nullable: true
        properties_dict:
          type: object
          additionalProperties: { }
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.UpdateContactPropertiesInput:
      type: object
      properties:
        state_id:
          type: string
          nullable: true
        state_identity:
          $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.StateIdentity'
        contact_id:
          type: string
          nullable: true
        properties_dict:
          type: object
          additionalProperties: { }
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.UpdateSalesforceObjectInput:
      type: object
      properties:
        state_id:
          type: string
          nullable: true
        state_identity:
          $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.StateIdentity'
        salesforce_connection_id:
          type: string
          nullable: true
        object_id:
          type: string
          nullable: true
        object_type:
          type: string
          nullable: true
        is_custom_object:
          type: boolean
        object_properties:
          type: object
          additionalProperties: { }
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.FlowHub.Model.VideoMessageObject:
      type: object
      properties:
        id:
          type: string
          nullable: true
        link:
          type: string
          nullable: true
        caption:
          type: string
          nullable: true
        filename:
          type: string
          nullable: true
        provider:
          $ref: '#/components/schemas/Sleekflow.Apis.FlowHub.Model.MediaMessageObjectProvider'
      additionalProperties: false
    Sleekflow.Apis.IntelligentHub.Model.CreateUserProfileInput:
      type: object
      properties:
        sleekflow_company_id:
          type: string
          nullable: true
        name:
          type: string
          nullable: true
        phone_number:
          type: string
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.IntelligentHub.Model.GetUserProfileInput:
      type: object
      properties:
        sleekflow_company_id:
          type: string
          nullable: true
        phone_number:
          type: string
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.TicketingHub.Model.Channel:
      type: object
      properties:
        channel_type:
          type: string
          nullable: true
        channel_identity_id:
          type: string
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.TicketingHub.Model.MediaDto:
      type: object
      properties:
        filename:
          type: string
          nullable: true
        blob_name:
          type: string
          nullable: true
        blob_id:
          type: string
          nullable: true
        blob_type:
          type: string
          nullable: true
        download_url:
          type: string
          nullable: true
        expires_on:
          type: string
          format: date-time
          nullable: true
        content_type:
          type: string
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.TicketingHub.Model.TicketDto:
      type: object
      properties:
        sleekflow_company_id:
          type: string
          nullable: true
        external_id:
          type: integer
          format: int64
        title:
          type: string
          nullable: true
        description:
          type: string
          nullable: true
        status_id:
          type: string
          nullable: true
        priority_id:
          type: string
          nullable: true
        type_id:
          type: string
          nullable: true
        channel:
          $ref: '#/components/schemas/Sleekflow.Apis.TicketingHub.Model.Channel'
        medias:
          type: array
          items:
            $ref: '#/components/schemas/Sleekflow.Apis.TicketingHub.Model.MediaDto'
          nullable: true
        due_date:
          type: string
          format: date-time
          nullable: true
        sleekflow_user_profile_id:
          type: string
          nullable: true
        assignee_id:
          type: string
          nullable: true
        assigned_team_ids:
          type: array
          items:
            type: string
          nullable: true
        associated_message_ids:
          type: array
          items:
            type: integer
            format: int64
          nullable: true
        url:
          type: string
          nullable: true
        resolution_time:
          type: string
          nullable: true
        resolved_at:
          type: string
          format: date-time
          nullable: true
        created_at:
          type: string
          format: date-time
        id:
          type: string
          nullable: true
      additionalProperties: false
    Sleekflow.Apis.TicketingHub.Model.TicketUser:
      type: object
      properties:
        sleekflow_user_id:
          type: string
          nullable: true
        sleekflow_user_identity_id:
          type: string
          nullable: true
        sleekflow_role:
          type: string
          nullable: true
        sleekflow_team_ids:
          type: array
          items:
            type: string
          nullable: true
      additionalProperties: false
    Travis_backend.TicketingHubDomain.Controllers.TicketingHubInternalController.CreateTicketConversationIndicatorInput:
      type: object
      properties:
        ticket:
          $ref: '#/components/schemas/Sleekflow.Apis.TicketingHub.Model.TicketDto'
        operation:
          type: string
          nullable: true
        ticket_user:
          $ref: '#/components/schemas/Sleekflow.Apis.TicketingHub.Model.TicketUser'
      additionalProperties: false
    Travis_backend.TicketingHubDomain.Controllers.TicketingHubInternalController.CreateTicketConversationIndicatorOutput:
      type: object
      additionalProperties: false
  securitySchemes:
    Bearer:
      type: http
      description: Please insert JWT with Bearer into field
      scheme: bearer
      bearerFormat: Bearer _YourToken_
security:
  - Bearer: [ ]