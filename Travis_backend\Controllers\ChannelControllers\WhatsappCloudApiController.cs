using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using AutoMapper;
using AutoMapper.QueryableExtensions;
using CsvHelper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.ModelBinding.Validation;
using Microsoft.EntityFrameworkCore;
using Sleekflow.Apis.MessagingHub.Model;
using Travis_backend.AccountAuthenticationDomain.Models;
using Travis_backend.CommonDomain.ViewModels;
using Travis_backend.CompanyDomain.Services;
using Travis_backend.ConversationServices;
using Travis_backend.Data.WhatsappCloudApi;
using Travis_backend.Database;
using Travis_backend.Exceptions;
using Travis_backend.Extensions;
using Travis_backend.Filters;
using Travis_backend.Helpers;
using Travis_backend.Models.ChatChannelConfig;
using DateTime = System.DateTime;
using Task = System.Threading.Tasks.Task;

namespace Travis_backend.Controllers.ChannelControllers;

[Authorize]
[Route("company/whatsapp/cloudapi")]
[TypeFilter(typeof(MessagingHubExceptionFilter))]
[TypeFilter(typeof(ModelStateValidationFilter))]
public class WhatsappCloudApiController : Controller
{
    private readonly ApplicationDbContext _appDbContext;
    private readonly ICoreService _coreService;
    private readonly IWhatsappCloudApiService _whatsappCloudApiService;
    private readonly UserManager<ApplicationUser> _userManager;
    private readonly IMapper _mapper;
    private readonly ICompanyUsageService _companyUsageService;

    public WhatsappCloudApiController(
        ApplicationDbContext appDbContext,
        UserManager<ApplicationUser> userManager,
        IMapper mapper,
        ICoreService coreService,
        IWhatsappCloudApiService whatsappCloudApiService,
        ICompanyUsageService companyUsageService)
    {
        _appDbContext = appDbContext;
        _userManager = userManager;
        _mapper = mapper;
        _coreService = coreService;
        _whatsappCloudApiService = whatsappCloudApiService;
        _companyUsageService = companyUsageService;
    }

    [HttpGet("by-waba")]
    public async Task<ActionResult<WhatsappCloudApiByWabaConfigResponse>> GetWhatsApp360DialogChannelsGroupByWaba()
    {
        var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (companyUser == null)
        {
            return Unauthorized();
        }

        var channelConfig = await _appDbContext.ConfigWhatsappCloudApiConfigs
            .AsNoTracking()
            .Where(x => x.CompanyId == companyUser.CompanyId)
            .ProjectTo<WhatsappCloudApiConfigViewModel>(_mapper.ConfigurationProvider)
            .ToListAsync(HttpContext.RequestAborted);

        var channelConfigByGroup = channelConfig.GroupBy(x => x.MessagingHubWabaId)
            .Select(
                x => new WhatsappCloudApiByWabaConfigViewModel
                {
                    WabaAccountId = x.Key,
                    MessagingHubWabaId = x.Key,
                    FacebookWabaBusinessId = x.FirstOrDefault()?.FacebookWabaBusinessId,
                    FacebookWabaId = x.FirstOrDefault()?.FacebookWabaId,
                    FacebookPhoneNumberId = x.FirstOrDefault()?.FacebookPhoneNumberId,
                    WabaName = x.FirstOrDefault()?.FacebookWabaName,
                    TemplateNamespace = x.FirstOrDefault()?.TemplateNamespace,
                    WhatsappCloudApiConfigs = x.ToList()
                }).ToList();

        return Ok(new WhatsappCloudApiByWabaConfigResponse(channelConfigByGroup));
    }

    [HttpGet("waba")]
    public async Task<ActionResult<ConnectWabaResponse>> GetConnectWaba([FromQuery] bool shouldRefresh = false)
    {
        var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (companyUser == null)
        {
            return Unauthorized();
        }

        var result = await _whatsappCloudApiService.GetConnectWaba(companyUser.CompanyId, shouldRefresh);

        var newWabaDto = _mapper.Map<List<WhatsappCloudApiMessagingHubWabaDto>>(result);

        return Ok(new ConnectWabaResponse(newWabaDto, new List<string>()));
    }

    [HttpPost("waba")]
    public async Task<ActionResult<ConnectWabaResponse>> ConnectWaba([FromBody] ConnectWabaRequest request)
    {
        var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (companyUser == null)
        {
            return Unauthorized();
        }

        ConnectWabaResponse result;

        try
        {
            var output = await _whatsappCloudApiService.ConnectWaba(
                companyUser.CompanyId,
                request.FacebookAppUserToken);

            var newWabaDto = _mapper.Map<List<WhatsappCloudApiMessagingHubWabaDto>>(output.ConnectedWabas);

            result = new ConnectWabaResponse(newWabaDto, output.ForbiddenWabaNames);
        }
        catch (SleekflowUiException ex)
        {
            return BadRequest(
                new ResponseViewModel()
                {
                    message = ex.Message
                });
        }

        return Ok(result);
    }

    [HttpPost("waba/exchange-facebook-authorization-code")]
    public async Task<ActionResult<ConnectWabaResponse>> ExchangeFacebookAuthorizationCode([FromBody] ConnectWabaWithFacebookAuthorizationCodeRequest request)
    {
        var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (companyUser == null)
        {
            return Unauthorized();
        }

        ConnectWabaResponse result;

        try
        {
            var output = await _whatsappCloudApiService.ConnectWabaWithFacebookAuthorizationCode(
                companyUser.CompanyId,
                request.FacebookAuthorizationCode);

            var newWabaDto = _mapper.Map<List<WhatsappCloudApiMessagingHubWabaDto>>(output.ConnectedWabas);

            result = new ConnectWabaResponse(newWabaDto, output.ForbiddenWabaNames);
        }
        catch (SleekflowUiException ex)
        {
            return BadRequest(
                new ResponseViewModel()
                {
                    message = ex.Message
                });
        }

        return Ok(result);
    }

    [HttpGet("channel")]
    public async Task<ActionResult<GetConnectedWabaPhoneNumberResponse>> GetConnectedWabaPhoneNumber(
        [FromQuery]
        bool shouldRefresh = false)
    {
        var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (companyUser == null)
        {
            return Unauthorized();
        }

        if (shouldRefresh)
        {
            await _whatsappCloudApiService.RefreshWhatsappCloudApiConfigsDetail(
                new List<string>()
                {
                    companyUser.CompanyId
                });
        }

        var messageHubOutputTask = _whatsappCloudApiService.GetConnectWaba(companyUser.CompanyId);
        var configsTask = _appDbContext.ConfigWhatsappCloudApiConfigs
            .AsNoTracking()
            .Where(x => x.CompanyId == companyUser.CompanyId)
            .ToListAsync(HttpContext.RequestAborted);

        await Task.WhenAll(messageHubOutputTask, configsTask);

        var wabaAndPhoneNumberDtos = await messageHubOutputTask;
        var configs = await configsTask;

        wabaAndPhoneNumberDtos.ForEach(
            x =>
            {
                x.WabaDtoPhoneNumbers = x.WabaDtoPhoneNumbers.Where(
                        w => !configs.Select(c => c.FacebookPhoneNumberId)
                            .Contains(w.FacebookPhoneNumberId))
                    .ToList();
            });

        return Ok(
            new GetConnectedWabaPhoneNumberResponse(
                _mapper.Map<List<WhatsappCloudApiConfigViewModel>>(configs),
                _mapper.Map<List<WhatsappCloudApiMessagingHubWabaDto>>(wabaAndPhoneNumberDtos)));
    }

    [HttpPost("channel")]
    public async Task<ActionResult<ConnectWabaPhoneNumberResponse>> ConnectWabaPhoneNumber(
        [FromBody]
        ConnectWabaPhoneNumberRequest request)
    {
        var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        var companyUsage = await _companyUsageService.GetCompanyUsage(companyUser.CompanyId);

        if (companyUsage.totalChannelAdded > companyUsage.MaximumNumberOfChannel)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = $"Your subscription plan only could add {companyUsage.MaximumNumberOfChannel} channels"
                });
        }

        if (companyUsage.MaximumNumberOfWhatsappCloudApiChannels <=
            companyUsage.CurrentNumberOfWhatsappCloudApiChannels)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = $"Your cannot connect more WhatsApp Cloud API channels."
                });
        }

        if (companyUser == null)
        {
            return Unauthorized();
        }

        WhatsappCloudApiConfig whatsappCloudApiConfig;
        try
        {
            whatsappCloudApiConfig = await _whatsappCloudApiService.ConnectWabaPhoneNumber(
                companyUser.CompanyId,
                request.ChannelName,
                request.WabaId,
                request.WabaPhoneNumberId,
                request.Pin);
        }
        catch (SleekflowUiException ex)
        {
            return BadRequest(
                new ResponseViewModel()
                {
                    message = ex.Message
                });
        }

        return Ok(
            new ConnectWabaPhoneNumberResponse(_mapper.Map<WhatsappCloudApiConfigViewModel>(whatsappCloudApiConfig)));
    }

    [HttpPut("channel")]
    public async Task<ActionResult<ResponseViewModel>> UpdateWabaPhoneNumberChannel(
        [FromBody]
        UpdateWabaPhoneNumberRequest request)
    {
        var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (companyUser == null)
        {
            return Unauthorized();
        }

        try
        {
            await _whatsappCloudApiService.UpdateWabaPhoneNumber(
                companyUser.CompanyId,
                request.ChannelName,
                request.WabaId,
                request.WabaPhoneNumberId);
        }
        catch (SleekflowUiException ex)
        {
            return BadRequest(
                new ResponseViewModel()
                {
                    message = ex.Message
                });
        }

        return Ok(
            new ResponseViewModel()
            {
                message = "Updated"
            });
    }

    [HttpPost("channel/reconnect")]
    public async Task<ActionResult<ConnectWabaPhoneNumberResponse>> ReconnectWabaPhoneNumber(
        [FromBody]
        ReconnectWabaPhoneNumberRequest request)
    {
        var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (companyUser == null)
        {
            return Unauthorized();
        }

        try
        {
            await _whatsappCloudApiService.ReconnectWabaPhoneNumber(
                companyUser.CompanyId,
                request.WabaId,
                request.WabaPhoneNumberId);
        }
        catch (SleekflowUiException ex)
        {
            return BadRequest(
                new ResponseViewModel()
                {
                    message = ex.Message
                });
        }

        return Ok(
            new ResponseViewModel()
            {
                message = "Reconnected."
            });
    }

    [HttpDelete("channel")]
    public async Task<ActionResult<ResponseViewModel>> DisconnectWabaPhoneNumber(
        [FromBody]
        DisconnectWabaPhoneNumberRequest request)
    {
        var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (companyUser == null)
        {
            return Unauthorized();
        }

        try
        {
            await _whatsappCloudApiService.DisconnectWabaPhoneNumber(
                companyUser.CompanyId,
                request.WabaId,
                request.WabaPhoneNumberId);
        }
        catch (SleekflowUiException ex)
        {
            return BadRequest(
                new ResponseViewModel()
                {
                    message = ex.Message
                });
        }

        return Ok(
            new ResponseViewModel()
            {
                message = "Deleted"
            });
    }

    # region auto top-up

    [HttpGet]
    [Authorize]
    [Route("auto-top-up/{facebookBusinessId}")]
    public async Task<ActionResult<GetWhatsappCloudApiBusinessBalanceAutoTopUpProfileOutput>>
        GetBusinessBalanceAutoTopUpProfile([FromRoute] string facebookBusinessId)
    {
        var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (companyUser == null)
        {
            return Unauthorized();
        }

        var result = await _whatsappCloudApiService.GetBusinessBalanceAutoTopUpProfile(
            companyUser.CompanyId,
            facebookBusinessId);

        return Ok(result);
    }

    [HttpGet]
    [Authorize]
    [Route("auto-top-up/settings")]
    public async Task<ActionResult<GetWhatsappCloudApiBusinessBalanceAutoTopUpProfileSettingsOutput>>
        GetBusinessBalanceAutoTopUpProfileSettings()
    {
        var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (companyUser == null)
        {
            return Unauthorized();
        }

        var result = await _whatsappCloudApiService.GetBusinessBalanceAutoTopUpProfileSettings(companyUser.CompanyId);

        return Ok(result);
    }

    [HttpPost]
    [Authorize]
    [Route("auto-top-up")]
    public async Task<ActionResult<UpsertWhatsappCloudApiBusinessBalanceAutoTopUpProfileOutput>>
        CreateBusinessBalanceAutoTopUpProfile([FromBody] CreateBusinessBalanceAutoTopUpProfileRequest request)
    {
        var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (companyUser == null)
        {
            return Unauthorized();
        }

        var result = await _whatsappCloudApiService.UpsertBusinessBalanceAutoTopUpProfile(
            request.AutoTopUpProfile,
            companyUser.CompanyId,
            companyUser.Id,
            companyUser.Identity.Email,
            companyUser.IdentityId,
            companyUser.Identity.DisplayName,
            request.RedirectTotUrl,
            request.PhoneNumber);

        return Ok(result);
    }

    [HttpPut]
    [Authorize]
    [Route("auto-top-up")]
    public async Task<ActionResult<GenerateWhatsappCloudApiBusinessBalanceStripeTopUpLinkOutput>>
        UpdateBusinessBalanceAutoTopUpProfile([FromBody] UpdateBusinessBalanceAutoTopUpProfileRequest request)
    {
        var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (companyUser == null)
        {
            return Unauthorized();
        }

        var result = await _whatsappCloudApiService.UpsertBusinessBalanceAutoTopUpProfile(
            request.AutoTopUpProfile,
            companyUser.CompanyId,
            companyUser.Id,
            companyUser.Identity.Email,
            companyUser.IdentityId,
            companyUser.Identity.DisplayName,
            request.RedirectToUrl,
            request.PhoneNumber);

        return Ok(result);
    }

    # endregion

    # region manual top-up

    [HttpGet]
    [Authorize]
    [Route("top-up")]
    public async Task<ActionResult<GetWhatsappCloudApiBusinessBalancesOutput>> GetBusinessBalanceStripeTopUpPlans()
    {
        var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (companyUser == null)
        {
            return Unauthorized();
        }

        var result = await _whatsappCloudApiService.GetBusinessBalanceStripeTopUpPlans(companyUser.CompanyId);

        return Ok(result);
    }

    [HttpGet]
    [Authorize]
    [Route("top-up/invoices")]
    public async Task<ActionResult<GetBusinessBalanceStripeTopUpInvoiceResponse>> GetBusinessBalanceStripeTopUpInvoices(
        [FromQuery]
        DateTime? start = null,
        [FromQuery]
        DateTime? end = null,
        [FromQuery]
        string continuationToken = null,
        [FromQuery]
        int limit = 10000)
    {
        var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (companyUser == null)
        {
            return Unauthorized();
        }

        var result = await _whatsappCloudApiService.GetBusinessBalancesStripeTopUpInvoices(
            companyUser.CompanyId,
            start,
            end,
            continuationToken,
            limit);

        return Ok(result);
    }

    [HttpPost]
    [Authorize]
    [Route("top-up")]
    public async Task<ActionResult<GenerateWhatsappCloudApiBusinessBalanceStripeTopUpLinkOutput>>
        GenerateBusinessBalanceStripeTopUpLink([FromBody] GenerateBusinessBalanceStripeTopUpLinkRequest request)
    {
        var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (companyUser == null)
        {
            return Unauthorized();
        }

        var result = await _whatsappCloudApiService
            .GenerateBusinessBalanceStripeTopUpLink(
                companyUser.CompanyId,
                companyUser.IdentityId,
                companyUser.Identity.DisplayName,
                companyUser.Identity.Email,
                request.TopUpPlanId,
                request.FacebookBusinessId,
                request.RedirectToUrl,
                request.WabaId);

        return Ok(result);
    }

    # endregion

    [HttpGet]
    [Authorize]
    [Route("balances")]
    public async Task<ActionResult<GetWhatsappCloudApiBusinessBalancesOutput>> GetBusinessBalance()
    {
        var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (companyUser == null)
        {
            return Unauthorized();
        }

        var result = (await _whatsappCloudApiService.GetBusinessBalances(companyUser.CompanyId)).BusinessBalances;

        return Ok(result);
    }

    [HttpPost]
    [Route("conversation-usage/analytic")]
    public async Task<ActionResult<GetWhatsappCloudApiConversationUsageAnalyticOutput>>
        GetConversationUsageByFacebookWabaId([FromBody] GetConversationUsageByFacebookWabaIdRequest request)
    {
        var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (companyUser == null)
        {
            return Unauthorized();
        }

        var result = await _whatsappCloudApiService.GetConversationUsageByFacebookWabaIdAsync(
            companyUser.CompanyId,
            request.FacebookBusinessId,
            request.FacebookWabaId,
            request.Start,
            request.End);

        return Ok(result);
    }

    [HttpPost]
    [Route("conversation-usage/analytic/export")]
    public async Task<ActionResult> ExportConversationUsageByFacebookWabaId(
        [FromBody]
        GetConversationUsageByFacebookWabaIdRequest request)
    {
        var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (companyUser == null)
        {
            return Unauthorized();
        }

        var result = await _whatsappCloudApiService.GetConversationUsageByFacebookWabaIdAsync(
            companyUser.CompanyId,
            request.FacebookBusinessId,
            request.FacebookWabaId,
            request.Start,
            request.End);

        var results = WhatsappCloudApiConversationUsageHelper.MapConversationUsageCsvExportDtos(result);

        var ms = new MemoryStream();

        await using (var writer = new StreamWriter(ms, Encoding.UTF8, leaveOpen: true))
        {
            await using (var csv = new CsvWriter(writer, CultureInfo.InvariantCulture))
            {
                await csv.WriteRecordsAsync(results);
            }
        }

        var filename =
            $"{result.FacebookBusinessWaba.FacebookWabaName}-ConversationUsage-{result.ConversationUsageAnalytic.GranularConversationUsageAnalytics.MinBy(x => x.Start).Start.Replace("-", string.Empty)}-{result.ConversationUsageAnalytic.GranularConversationUsageAnalytics.MaxBy(x => x.Start).Start.Replace("-", string.Empty)}.csv";

        return File(ms.ToArray(), "text/csv", filename.RemoveInvalidFileNameChars());
    }

    #region Waba Level Credit Management

    [HttpPost("balances/waba/get-auto-top-up-profile")]
    public async Task<ActionResult<GetWabaBalanceAutoTopUpProfileOutputOutput>> GetWabaBalanceAutoTopUpProfileAsync(
        [FromBody]
        GetWabaBalanceAutoTopUpProfileRequest request,
        CancellationToken cancellationToken)
    {
        var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (companyUser == null)
        {
            return Unauthorized();
        }

        var result = await _whatsappCloudApiService
            .GetWabaBalanceAutoTopUpProfileAsync(request.Input, cancellationToken);

        return Ok(result);
    }

    [HttpPost("balances/waba/upsert-waba-balance-auto-top-up-profile")]
    public async Task<ActionResult<UpsertWabaBalanceAutoTopUpProfileOutputOutput>> UpsertWabaBalanceAutoTopUpProfileAsync(
        [FromBody]
        UpsertWabaBalanceAutoTopUpProfileRequest request,
        CancellationToken cancellationToken)
    {
        var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (companyUser == null)
        {
            return Unauthorized();
        }

        var result = await _whatsappCloudApiService
            .UpsertWabaBalanceAutoTopUpProfileAsync(request, companyUser, cancellationToken);

        return Ok(result);
    }

    [HttpPost("balances/get-business-waba-balance-transfer-transactions")]
    public async Task<ActionResult<GetBusinessBalanceCreditTransferTransactionLogsOutputOutput>> GetBusinessWabaBalanceTransferTransactionsAsync(
        [FromBody]
        GetBusinessBalanceCreditTransferTransactionLogsInput request,
        CancellationToken cancellationToken)
    {
        var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (companyUser == null)
        {
            return Unauthorized();
        }

        var result = await _whatsappCloudApiService
            .GetBusinessWabaBalanceTransferTransactionsAsync(request, cancellationToken);

        return Ok(result);
    }

    [HttpPost("balances/allocate-credit-between-business-and-waba")]
    public async Task<ActionResult<AllocateBusinessWabaLevelCreditOutputOutput>> AllocateCreditBetweenBusinessAndWabaAsync(
        [FromBody]
        AllocateBusinessWabaLevelCreditDto content,
        CancellationToken cancellationToken)
    {
        var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (companyUser == null)
        {
            return Unauthorized();
        }

        var result = await _whatsappCloudApiService.AllocateCreditBetweenBusinessAndWabaAsync(
            new AllocateBusinessWabaLevelCreditInput(
                companyUser.CompanyId,
                content.FacebookBusinessId,
                content.ETag,
                content.CreditAllocation,
                companyUser.IdentityId),
            cancellationToken);

        return Ok(result);
    }

    [HttpPost("balances/switch-to-waba-level-credit-management")]
    public async Task<ActionResult<SwitchFromBusinessLevelToWabaLevelCreditManagementOutputOutput>> SwitchToWabaLevelCreditManagementAsync(
        [FromBody]
        AllocateBusinessWabaLevelCreditDto content,
        CancellationToken cancellationToken)
    {
        var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (companyUser == null)
        {
            return Unauthorized();
        }

        var result = await _whatsappCloudApiService.SwitchToWabaLevelCreditManagementAsync(
            new SwitchFromBusinessLevelToWabaLevelCreditManagementInput(
                companyUser.CompanyId,
                content.FacebookBusinessId,
                content.ETag,
                content.CreditAllocation,
                companyUser.IdentityId),
            cancellationToken);

        return Ok(result);
    }

    [HttpPost("balances/switch-to-business-level-credit-management")]
    public async Task<ActionResult<SwitchFromWabaLevelToBusinessLevelCreditManagementOutputOutput>> SwitchToBusinessLevelCreditManagementAsync(
        [FromBody]
        AllocateBusinessWabaLevelCreditDto content,
        CancellationToken cancellationToken)
    {
        var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (companyUser == null)
        {
            return Unauthorized();
        }

        var result = await _whatsappCloudApiService.SwitchToBusinessLevelCreditManagementAsync(
            new SwitchFromWabaLevelToBusinessLevelCreditManagementInput(
                companyUser.CompanyId,
                content.FacebookBusinessId,
                content.ETag,
                companyUser.IdentityId),
            cancellationToken);

        return Ok(result);
    }

    #endregion

    [HttpPost("balances/waba/get-all-time-usage")]
    public async Task<ActionResult<Money>> GetWabaAllTimeUsageAsync(
        [FromBody]
        [ValidateNever]
        GetBusinessBalanceTransactionLog content,
        CancellationToken cancellationToken)
    {
        var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (companyUser == null)
        {
            return Unauthorized();
        }

        var result = await _whatsappCloudApiService
            .GetWabaAllTimeUsageAsync(
                new BusinessBalanceTransactionLogFilter(
                    "CONVERSATION_USAGE",
                    content.FacebookBusinessId,
                    content.FacebookWabaId,
                    null,
                    null,
                    null,
                    null,
                    null,
                    true),
                limit: content.Limit,
                cancellationToken);

        return Ok(result);
    }
}