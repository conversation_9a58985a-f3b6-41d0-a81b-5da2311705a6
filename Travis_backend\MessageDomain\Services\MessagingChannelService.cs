﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Travis_backend.ChannelDomain.Models;
using Travis_backend.ChannelDomain.Models.Interfaces;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.Constants;
using Travis_backend.ConversationServices.Models;
using Travis_backend.Database;
using Travis_backend.Database.Services;
using Travis_backend.Enums;
using Travis_backend.Extensions;
using Travis_backend.Models.ChatChannelConfig;

namespace Travis_backend.MessageDomain.Services;

public interface IMessagingChannelService
{
    Task<List<IMessagingChannel>> GetAllChannelsAsync(string companyId);

    Task<IMessagingChannel> GetChannelAsync(string companyId, string channelType, string channelIdentityId);

    Task<bool> IsChannelExistedAsync(string companyId, string channelType, string channelIdentityId);

    Task<T> GetChannelAsync<T>(string companyId, string channelType, string channelIdentityId)
        where T : class, IMessagingChannel;

    Task<(
        List<WhatsAppConfig> WhatsAppConfigs,
        List<WhatsApp360DialogConfig> WhatsApp360DialogConfigs,
        List<WhatsappCloudApiConfig> WhatsappCloudApiConfigs,
        List<FacebookConfig> FacebookConfigs,
        List<InstagramConfig> InstagramConfigs,
        EmailConfig EmailConfig,
        WeChatConfig WeChatConfig,
        List<LineConfig> LineConfigs,
        List<ViberConfig> ViberConfigs,
        List<TelegramConfig> TelegramConfigs,
        List<SMSConfig> SmsConfigs)> GetAvailableChannels(Staff staff);
}

public class MessagingChannelService : IMessagingChannelService
{
    private readonly ApplicationDbContext _appDbContext;
    private readonly IDbContextService _dbContextService;
    private readonly ILogger<MessagingChannelService> _logger;


    public MessagingChannelService(
        ApplicationDbContext appDbContext,
        ILogger<MessagingChannelService> logger,
        IDbContextService dbContextService)
    {
        _appDbContext = appDbContext;
        _logger = logger;
        _dbContextService = dbContextService;
    }

    public async Task<List<IMessagingChannel>> GetAllChannelsAsync(string companyId)
    {
        var allMessagingChannels = new List<IMessagingChannel>();

        var facebookConfigs = await _appDbContext.ConfigFacebookConfigs
            .Where(x => x.CompanyId == companyId)
            .ToListAsync();

        var whatsAppConfigs = await _appDbContext.ConfigWhatsAppConfigs
            .Where(x => x.CompanyId == companyId)
            .ToListAsync();

        var emailConfig = await _appDbContext.ConfigEmailConfigs
            .Where(x => x.Id == _appDbContext.CompanyCompanies.First(c => c.Id == companyId).EmailConfigId)
            .FirstOrDefaultAsync();

        var weChatConfig = await _appDbContext.ConfigWeChatConfigs
            .Where(x => x.Id == _appDbContext.CompanyCompanies.First(c => c.Id == companyId).WeChatConfigId)
            .FirstOrDefaultAsync();

        var lineConfigs = await _appDbContext.ConfigLineConfigs
            .Where(x => x.CompanyId == companyId)
            .ToListAsync();

        var viberConfigs = await _appDbContext.ConfigViberConfigs
            .Where(x => x.CompanyId == companyId)
            .ToListAsync();

        var telegramConfigs = await _appDbContext.ConfigTelegramConfigs
            .Where(x => x.CompanyId == companyId)
            .ToListAsync();

        var smsConfigs = await _appDbContext.ConfigSMSConfigs
            .Where(x => x.CompanyId == companyId)
            .ToListAsync();

        var instagramConfigs = await _appDbContext.ConfigInstagramConfigs
            .Where(x => x.CompanyId == companyId)
            .ToListAsync();

        var whatsApp360DialogConfigs = await _appDbContext.ConfigWhatsApp360DialogConfigs
            .Where(x => x.CompanyId == companyId)
            .ToListAsync();

        var whatsappCloudApiConfigs = await _appDbContext.ConfigWhatsappCloudApiConfigs
            .Where(x => x.CompanyId == companyId)
            .ToListAsync();

        allMessagingChannels.AddRange(facebookConfigs);
        allMessagingChannels.AddRange(whatsAppConfigs);

        if (emailConfig != null)
        {
            allMessagingChannels.Add(emailConfig);
        }

        if (weChatConfig != null)
        {
            allMessagingChannels.Add(weChatConfig);
        }

        allMessagingChannels.AddRange(lineConfigs);
        allMessagingChannels.AddRange(viberConfigs);
        allMessagingChannels.AddRange(telegramConfigs);
        allMessagingChannels.AddRange(smsConfigs);
        allMessagingChannels.AddRange(instagramConfigs);
        allMessagingChannels.AddRange(whatsApp360DialogConfigs);
        allMessagingChannels.AddRange(whatsappCloudApiConfigs);

        return allMessagingChannels;
    }

    public async Task<IMessagingChannel> GetChannelAsync(
        string companyId,
        string channelType,
        string channelIdentityId)
    {
        switch (channelType)
        {
            case ChannelTypes.Facebook:
                return await _appDbContext.ConfigFacebookConfigs
                    .FirstOrDefaultAsync(x => x.CompanyId == companyId && x.ChannelIdentityId == channelIdentityId);

            case ChannelTypes.Instagram:
                return await _appDbContext.ConfigInstagramConfigs
                    .FirstOrDefaultAsync(x => x.CompanyId == companyId && x.ChannelIdentityId == channelIdentityId);

            case ChannelTypes.Email:
                return await _appDbContext.ConfigEmailConfigs
                    .Where(
                        x => x.Id == _appDbContext.CompanyCompanies.First(c => c.Id == companyId).EmailConfigId
                             && x.ChannelIdentityId == channelIdentityId)
                    .FirstOrDefaultAsync();

            case ChannelTypes.WhatsappTwilio:
                return await _appDbContext.ConfigWhatsAppConfigs
                    .FirstOrDefaultAsync(x => x.CompanyId == companyId && x.ChannelIdentityId == channelIdentityId);

            case ChannelTypes.Whatsapp360Dialog:
                return await _appDbContext.ConfigWhatsApp360DialogConfigs
                    .FirstOrDefaultAsync(x => x.CompanyId == companyId && x.ChannelIdentityId == channelIdentityId);

            case ChannelTypes.WhatsappCloudApi:
                return await _appDbContext.ConfigWhatsappCloudApiConfigs
                    .FirstOrDefaultAsync(x => x.CompanyId == companyId && x.ChannelIdentityId == channelIdentityId);
            case ChannelTypes.Wechat:
                return await _appDbContext.ConfigWeChatConfigs
                    .FirstOrDefaultAsync(
                        x => x.Id == _appDbContext.CompanyCompanies.First(c => c.Id == companyId).WeChatConfigId
                             && x.ChannelIdentityId == channelIdentityId);
            case ChannelTypes.Line:
                return await _appDbContext.ConfigLineConfigs
                    .FirstOrDefaultAsync(x => x.CompanyId == companyId && x.ChannelIdentityId == channelIdentityId);

            case ChannelTypes.Viber:
                return await _appDbContext.ConfigViberConfigs
                    .FirstOrDefaultAsync(x => x.CompanyId == companyId && x.ChannelIdentityId == channelIdentityId);
            case ChannelTypes.Telegram:
                return await _appDbContext.ConfigTelegramConfigs
                    .FirstOrDefaultAsync(x => x.CompanyId == companyId && x.ChannelIdentityId == channelIdentityId);

            case ChannelTypes.Sms:
                return await _appDbContext.ConfigSMSConfigs
                    .FirstOrDefaultAsync(x => x.CompanyId == companyId && x.ChannelIdentityId == channelIdentityId);
            default:
                return null;
        }
    }

    public async Task<bool> IsChannelExistedAsync(string companyId, string channelType, string channelIdentityId)
    {
        switch (channelType)
        {
            case ChannelTypes.Facebook:
                return await _appDbContext.ConfigFacebookConfigs
                    .AnyAsync(x => x.CompanyId == companyId && x.ChannelIdentityId == channelIdentityId);

            case ChannelTypes.Instagram:
                return await _appDbContext.ConfigInstagramConfigs
                    .AnyAsync(x => x.CompanyId == companyId && x.ChannelIdentityId == channelIdentityId);

            case ChannelTypes.Email:
                return await _appDbContext.ConfigEmailConfigs
                    .Where(
                        x => x.Id == _appDbContext.CompanyCompanies.First(c => c.Id == companyId).EmailConfigId
                             && x.ChannelIdentityId == channelIdentityId)
                    .AnyAsync();

            case ChannelTypes.WhatsappTwilio:
                return await _appDbContext.ConfigWhatsAppConfigs
                    .AnyAsync(x => x.CompanyId == companyId && x.ChannelIdentityId == channelIdentityId);

            case ChannelTypes.Whatsapp360Dialog:
                return await _appDbContext.ConfigWhatsApp360DialogConfigs
                    .AnyAsync(x => x.CompanyId == companyId && x.ChannelIdentityId == channelIdentityId);

            case ChannelTypes.WhatsappCloudApi:
                return await _appDbContext.ConfigWhatsappCloudApiConfigs
                    .AnyAsync(x => x.CompanyId == companyId && x.ChannelIdentityId == channelIdentityId);
            case ChannelTypes.Wechat:
                return await _appDbContext.ConfigWeChatConfigs
                    .AnyAsync(
                        x => x.Id == _appDbContext.CompanyCompanies.First(c => c.Id == companyId).WeChatConfigId
                             && x.ChannelIdentityId == channelIdentityId);
            case ChannelTypes.Line:
                return await _appDbContext.ConfigLineConfigs
                    .AnyAsync(x => x.CompanyId == companyId && x.ChannelIdentityId == channelIdentityId);

            case ChannelTypes.Viber:
                return await _appDbContext.ConfigViberConfigs
                    .AnyAsync(x => x.CompanyId == companyId && x.ChannelIdentityId == channelIdentityId);
            case ChannelTypes.Telegram:
                return await _appDbContext.ConfigTelegramConfigs
                    .AnyAsync(x => x.CompanyId == companyId && x.ChannelIdentityId == channelIdentityId);

            case ChannelTypes.Sms:
                return await _appDbContext.ConfigSMSConfigs
                    .AnyAsync(x => x.CompanyId == companyId && x.ChannelIdentityId == channelIdentityId);
            case ChannelTypes.Note:
            case ChannelTypes.LiveChat:
                return true;
            default:
                return false;
        }
    }

    public async Task<T> GetChannelAsync<T>(
        string companyId,
        string channelType,
        string channelIdentityId)
        where T : class, IMessagingChannel
    {
        return await GetChannelAsync(
            companyId,
            channelType,
            channelIdentityId) as T;
    }

    /// <summary>
    ///     Obtain all the staff's available channels based on their role and permissions
    /// </summary>
    /// <param name="staff">Staff - Contains all the staff information's.</param>
    /// <returns>A <see cref="Task" /> representing the asynchronous operation.</returns>
    public async Task<(
        List<WhatsAppConfig> WhatsAppConfigs,
        List<WhatsApp360DialogConfig> WhatsApp360DialogConfigs,
        List<WhatsappCloudApiConfig> WhatsappCloudApiConfigs,
        List<FacebookConfig> FacebookConfigs,
        List<InstagramConfig> InstagramConfigs,
        EmailConfig EmailConfig,
        WeChatConfig WeChatConfig,
        List<LineConfig> LineConfigs,
        List<ViberConfig> ViberConfigs,
        List<TelegramConfig> TelegramConfigs,
        List<SMSConfig> SmsConfigs)> GetAvailableChannels(
        Staff staff)
    {
        var dbContext = _dbContextService.GetDbContext();
        var companyId = staff.CompanyId;
        var isAdmin = staff.RoleType == StaffUserRole.Admin;

        // If the availableChannelsIdentityIds is empty then all the channels would be available for the staff to view or send the message
        var availableChannelsIdentityIds = new List<string>();

        // This only applies to staff and team admin roles would excluded role admin, Note this logic is only design for admin, staff and team admin roles
        if (!isAdmin)
        {
            var companyRolePermission = await dbContext.CompanyRolePermissions
                .Where(c => c.CompanyId == staff.CompanyId && c.StaffUserRole == staff.RoleType)
                .SingleOrDefaultAsync();

            // If and Only If the IsShowDefaultChannelMessagesOnly config in CompanyRolePermissions is enabled would apply the default channels filter or else all user role would able to view all message channels
            var isShowDefaultChannelMessagesOnly =
                companyRolePermission?.StoredPermission?.IsShowDefaultChannelMessagesOnly ?? false;
            if (isShowDefaultChannelMessagesOnly)
            {
                var associatedTeams = await dbContext.CompanyStaffTeams
                    .Where(x => x.Members.Any(y => y.StaffId == staff.Id))
                    .ToListAsync();
                availableChannelsIdentityIds =
                    associatedTeams.SelectMany(
                            a =>
                                a.DefaultChannels == null
                                    ? new List<string>()
                                    : a.DefaultChannels?.SelectMany(d => d.ids))
                        .ToList();
            }
        }

        var whatsappConfigs =
            await GetChannelConfigs(
                dbContext.ConfigWhatsAppConfigs.Where(w => w.CompanyId == staff.CompanyId),
                availableChannelsIdentityIds,
                r => availableChannelsIdentityIds.Contains(r.TwilioAccountId + ";" + r.WhatsAppSender));

        var whatsapp360DialogConfigs =
            await GetChannelConfigs(
                dbContext.ConfigWhatsApp360DialogConfigs.Where(w => w.CompanyId == companyId),
                availableChannelsIdentityIds,
                r => availableChannelsIdentityIds.Contains(r.Id.ToString()));

        var whatsappCloudApiConfigs =
            await GetChannelConfigs(
                dbContext.ConfigWhatsappCloudApiConfigs.Where(w => w.CompanyId == companyId),
                availableChannelsIdentityIds);

        var facebookConfigs =
            await GetChannelConfigs(
                dbContext.ConfigFacebookConfigs.Where(f => f.CompanyId == companyId),
                availableChannelsIdentityIds);

        var instagramConfigs =
            await GetChannelConfigs(
                dbContext.ConfigInstagramConfigs.Where(i => i.CompanyId == companyId),
                availableChannelsIdentityIds);

        return (
            whatsappConfigs,
            whatsapp360DialogConfigs,
            whatsappCloudApiConfigs,
            facebookConfigs,
            instagramConfigs,
            await dbContext.ConfigEmailConfigs
                .Where(x => x.Id == dbContext.CompanyCompanies.First(c => c.Id == companyId).EmailConfigId)
                .FirstOrDefaultAsync(),
            await dbContext.ConfigWeChatConfigs
                .Where(x => x.Id == dbContext.CompanyCompanies.First(c => c.Id == companyId).WeChatConfigId)
                .FirstOrDefaultAsync(),
            await dbContext.ConfigLineConfigs
                .Where(x => x.CompanyId == companyId)
                .ToListAsync(),
            await dbContext.ConfigViberConfigs
                .Where(x => x.CompanyId == companyId)
                .ToListAsync(),
            await dbContext.ConfigTelegramConfigs
                .Where(x => x.CompanyId == companyId)
                .ToListAsync(),
            await dbContext.ConfigSMSConfigs
                .Where(x => x.CompanyId == companyId)
                .ToListAsync());
    }

    private static async Task<List<T>> GetChannelConfigs<T>(
        IQueryable<T> data,
        List<string> availableChannelsIdentityIds,
        Expression<Func<T, bool>> configFilterExpression = null)
        where T : IMessagingChannel
    {
        configFilterExpression ??= r => availableChannelsIdentityIds.Contains(r.ChannelIdentityId);
        return await data.WhereIf(availableChannelsIdentityIds.Count > 0, configFilterExpression).ToListAsync();
    }
}