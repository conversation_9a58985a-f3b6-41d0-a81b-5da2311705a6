using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AutoMapper;
using Hangfire;
using isRock.LineBot.Extensions;
using LinqKit;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using ShopifySharp;
using ShopifySharp.Filters;
using Travis_backend.AccountAuthenticationDomain.Models;
using Travis_backend.Cache;
using Travis_backend.Cache.Models.CacheKeyPatterns;
using Travis_backend.CommonDomain.ViewModels;
using Travis_backend.ConversationServices;
using Travis_backend.Database;
using Travis_backend.Enums;
using Travis_backend.Extensions;
using Travis_backend.IntegrationServices.Models;
using Travis_backend.IntegrationServices.ViewModels;
using Travis_backend.ShareInvitationDomain.Models;
using Travis_backend.ShareInvitationDomain.Services;
using IShopifyIntegrationService = Travis_backend.IntegrationServices.IShopifyService;

namespace Travis_backend.Controllers.ShopifyIntegrationControllers;

[Route("Shopify/Product")]
[Authorize]
public class ShopifyProductController : Controller
{
    private readonly ApplicationDbContext _appDbContext;
    private readonly UserManager<ApplicationUser> _userManager;
    private readonly IMapper _mapper;
    private readonly ILogger _logger;
    private readonly IConfiguration _configuration;
    private readonly IShopifyIntegrationService _shopifyService;
    private readonly ICoreService _coreService;
    private readonly IShareLinkService _shareLinkService;
    private readonly ICacheManagerService _cacheManagerService;

    public ShopifyProductController(
        ApplicationDbContext appDbContext,
        UserManager<ApplicationUser> userManager,
        IMapper mapper,
        IConfiguration configuration,
        ILogger<ShopifyProductController> logger,
        IShopifyIntegrationService shopifyService,
        ICoreService coreService,
        IShareLinkService shareLinkService,
        ICacheManagerService cacheManagerService)
    {
        _userManager = userManager;
        _appDbContext = appDbContext;
        _mapper = mapper;
        _configuration = configuration;
        _logger = logger;
        _shopifyService = shopifyService;
        _coreService = coreService;
        _shareLinkService = shareLinkService;
        _cacheManagerService = cacheManagerService;
    }

    [HttpPost("sync")]
    public async Task<IActionResult> SyncProduct([FromQuery(Name = "ShopifyId")] long? shopifyId)
    {
        var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (companyUser == null)
        {
            return Unauthorized();
        }

        var config = await _appDbContext.ConfigShopifyConfigs
            .Where(x => x.CompanyId == companyUser.CompanyId)
            .WhereIf(shopifyId.HasValue, config => config.Id == shopifyId)
            .FirstOrDefaultAsync();

        BackgroundJob.Enqueue<IShopifyIntegrationService>(x => x.SyncProduct(companyUser.CompanyId, shopifyId.Value));
        if (string.IsNullOrEmpty(config.SyncShopifyOrderJobId))
        {
            config.SyncShopifyOrderJobId = Guid.NewGuid().ToString();
            await _appDbContext.SaveChangesAsync();
        }

        RecurringJob.AddOrUpdate<IShopifyIntegrationService>(
            config.SyncShopifyOrderJobId,
            x => x.SyncProduct(companyUser.CompanyId, shopifyId.Value),
            $"{0} {19} * * *");

        return Ok();
    }

    [HttpGet("Collection/Count")]
    public async Task<IActionResult> GetProductCollectionCount(
        [FromQuery(Name = "ShopifyId")]
        long? shopifyId,
        [FromQuery(Name = "Title")]
        string title)
    {
        var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (companyUser == null)
        {
            return Unauthorized();
        }

        try
        {
            var response = new ShopifyCountResponse
            {
                Count = await _appDbContext.ShopifyCollectionRecords.Where(
                        x => x.CompanyId == companyUser.CompanyId && x.ShopifyId == shopifyId)
                    .WhereIf(!string.IsNullOrEmpty(title), x => x.Title.Contains(title)).CountAsync()
            };

            return Ok(response);
        }
        catch (Exception ex)
        {
            return BadRequest(
                new ResponseViewModel()
                {
                    message = ex.Message
                });
        }
    }

    [HttpGet("Collection")]
    public async Task<IActionResult> GetProductCollection(
        [FromQuery(Name = "ShopifyId")]
        long? shopifyId,
        [FromQuery(Name = "Title")]
        string title,
        [FromQuery(Name = "Offset")]
        int offset = 0,
        [FromQuery(Name = "limit")]
        int limit = 50)
    {
        var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (companyUser == null)
        {
            return Unauthorized();
        }

        try
        {
            var result = new CollectionResponse();

            var collections = await _appDbContext.ShopifyCollectionRecords
                .Where(x => x.CompanyId == companyUser.CompanyId && x.ShopifyId == shopifyId)
                .WhereIf(!string.IsNullOrEmpty(title), x => x.Title.Contains(title))
                .Select(x => x.CollectionPayload)
                .Skip(offset).Take(limit)
                .ToListAsync(HttpContext.RequestAborted);

            result.Items = collections;

            return Ok(result);
        }
        catch (Exception ex)
        {
            return BadRequest(
                new ResponseViewModel()
                {
                    message = ex.Message
                });
        }
    }

    // [HttpGet("SmartCollection/Count")]
    // public async Task<IActionResult> GetSmartCollectionCount([FromQuery(Name = "ShopifyId")] long? shopifyId)
    // {
    //     var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
    //
    //     if (companyUser == null)
    //         return Unauthorized();
    //
    //     var config = await _appDbContext.ConfigShopifyConfigs
    //         .Where(x => x.CompanyId == companyUser.CompanyId)
    //         .WhereIf(shopifyId.HasValue, config => config.Id == shopifyId)
    //         .FirstOrDefaultAsync();
    //
    //     try
    //     {
    //         var collectionServices = new SmartCollectionService(config.UsersMyShopifyUrl, config.AccessToken);
    //         var collectionCount = await collectionServices.CountAsync();
    //
    //         var response = new ShopifyCountResponse {Count = collectionCount};
    //
    //         return Ok(response);
    //     }
    //     catch (Exception ex)
    //     {
    //         return BadRequest(new ResponseViewModel() {message = ex.Message});
    //     }
    // }
    //
    //
    // [HttpGet("SmartCollection")]
    // public async Task<IActionResult> GetSmartCollection([FromQuery(Name = "ShopifyId")] long? shopifyId, [FromQuery(Name = "Title")] string title, [FromQuery(Name = "Offset")] int offset = 0, [FromQuery(Name = "limit")] int limit = 50)
    // {
    //     var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
    //
    //     if (companyUser == null)
    //         return Unauthorized();
    //
    //     var config = await _appDbContext.ConfigShopifyConfigs
    //         .Where(x => x.CompanyId == companyUser.CompanyId)
    //         .WhereIf(shopifyId.HasValue, config => config.Id == shopifyId)
    //         .FirstOrDefaultAsync();
    //
    //     try
    //     {
    //         var collectionServices = new SmartCollectionService(config.UsersMyShopifyUrl, config.AccessToken);
    //
    //         ListResult<SmartCollection> result = null;
    //
    //         if (offset > 0)
    //         {
    //             var previousFilter = new SmartCollectionListFilter()
    //             {
    //                 Limit = offset,
    //                 Title = title
    //             };
    //
    //             result = await collectionServices.ListAsync(previousFilter);
    //
    //             if (result.HasNextPage)
    //             {
    //                 var nextFilter = result.GetNextPageFilter();
    //                 nextFilter.Limit = limit;
    //                 result = await collectionServices.ListAsync(nextFilter);
    //             }
    //             else
    //             {
    //                 result = null;
    //             }
    //         }
    //         else
    //         {
    //             var filter = new SmartCollectionListFilter()
    //             {
    //                 Limit = limit,
    //                 Title = title
    //             };
    //
    //             result = await collectionServices.ListAsync(filter);
    //         }
    //
    //         return Ok(result);
    //     }
    //     catch (Exception ex)
    //     {
    //         return BadRequest(new ResponseViewModel() {message = ex.Message});
    //     }
    // }
    //
    // [HttpGet("CustomCollection")]
    // public async Task<IActionResult> GetCustomCollection([FromQuery(Name = "ShopifyId")] long? shopifyId, [FromQuery(Name = "Title")] string title, [FromQuery(Name = "Offset")] int offset = 0, [FromQuery(Name = "limit")] int limit = 50)
    // {
    //     var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
    //
    //     if (companyUser == null)
    //         return Unauthorized();
    //
    //     var config = await _appDbContext.ConfigShopifyConfigs
    //         .Where(x => x.CompanyId == companyUser.CompanyId)
    //         .WhereIf(shopifyId.HasValue, config => config.Id == shopifyId)
    //         .FirstOrDefaultAsync();
    //
    //     try
    //     {
    //         var collectionServices = new CustomCollectionService(config.UsersMyShopifyUrl, config.AccessToken);
    //
    //         ListResult<CustomCollection> result = null;
    //
    //         if (offset > 0)
    //         {
    //             var previousFilter = new CustomCollectionListFilter()
    //             {
    //                 Limit = offset,
    //                 Title = title
    //             };
    //
    //             result = await collectionServices.ListAsync(previousFilter);
    //
    //             if (result.HasNextPage)
    //             {
    //                 var nextFilter = result.GetNextPageFilter();
    //                 nextFilter.Limit = limit;
    //                 result = await collectionServices.ListAsync(nextFilter);
    //             }
    //             else
    //             {
    //                 result = null;
    //             }
    //         }
    //         else
    //         {
    //             var filter = new CustomCollectionListFilter()
    //             {
    //                 Limit = limit,
    //                 Title = title
    //             };
    //
    //             result = await collectionServices.ListAsync(filter);
    //         }
    //
    //         return Ok(result);
    //     }
    //     catch (Exception ex)
    //     {
    //         return BadRequest(new ResponseViewModel() {message = ex.Message});
    //     }
    // }
    [HttpGet("Variant")]
    public async Task<ActionResult<List<ShopifyProductVariant>>> GetVariant(
        [FromQuery(Name = "ShopifyId")]
        long? shopifyId,
        [FromQuery(Name = "ProductId")]
        long? productId)
    {
        var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (companyUser == null)
        {
            return Unauthorized();
        }

        var config = await _appDbContext.ConfigShopifyConfigs
            .Where(x => x.CompanyId == companyUser.CompanyId)
            .WhereIf(shopifyId.HasValue, config => config.Id == shopifyId)
            .FirstOrDefaultAsync();

        try
        {
            var result = new List<ShopifyProductVariant>();

            var productVariantService = new ProductVariantService(config.UsersMyShopifyUrl, config.AccessToken);
            var variantResults = await productVariantService.ListAsync(productId.Value);

            result = _mapper.Map<List<ShopifyProductVariant>>(variantResults.Items);

            if (companyUser.CompanyId is "e53b13a4-b371-47a6-a4d7-ba10caa296fe"
                or "ef710422-690b-430a-9d3a-369589c46567")
            {
                result.ForEach(x => x.InventoryQuantity = 100);
            }

            // Get multiple Currency
            if (config.SupportedCountries == null || !config.SupportedCountries.Any())
            {
                return Ok(result);
            }

            foreach (var productVariantItem in result)
            {
                productVariantItem.MultipleCurrencies = new List<Currency>();
                var multipleCurrencyResult =
                    await _shopifyService.GetMultipleCurrencyAsync(productVariantItem.Id.ToString(), config);
                var productVariantMultipleVariant = JObject.Parse(multipleCurrencyResult["productVariant"].ToJson());

                foreach (var (key, value) in productVariantMultipleVariant)
                {
                    var amount = value["price"]?.Value<decimal>("amount");
                    var currencyCode = value["price"]?.Value<string>("currencyCode");

                    if (amount != null)
                    {
                        productVariantItem.MultipleCurrencies.Add(
                            new Currency()
                            {
                                Name = key, Amount = amount.Value, CurrencyCode = currencyCode
                            });
                    }
                }
            }

            return Ok(result);
        }
        catch (Exception ex)
        {
            try
            {
                var variant = await _appDbContext.ShopifyProductRecords
                    .Where(
                        x => x.CompanyId == companyUser.CompanyId && x.ShopifyId == shopifyId &&
                             x.ProductId == productId)
                    .Select(x => x.ProductPayload.Variants.FirstOrDefault()).ToListAsync(HttpContext.RequestAborted);

                return Ok(variant);
            }
            catch (Exception eex)
            {
                return BadRequest(
                    new ResponseViewModel()
                    {
                        message = eex.Message
                    });
            }
        }
    }

    [HttpGet("Count")]
    public async Task<ActionResult<ShopifyCountResponse>> GetProductCount(
        [FromQuery(Name = "ShopifyId")]
        long? shopifyId,
        [FromQuery(Name = "CollectionId")]
        long? collectionId)
    {
        var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (companyUser == null)
        {
            return Unauthorized();
        }

        var config = await _appDbContext.ConfigShopifyConfigs
            .Where(x => x.CompanyId == companyUser.CompanyId)
            .WhereIf(shopifyId.HasValue, config => config.Id == shopifyId)
            .FirstOrDefaultAsync();

        try
        {
            var productCount = await _appDbContext.ShopifyProductRecords
                .Where(x => x.CompanyId == companyUser.CompanyId && x.ShopifyId == config.Id)
                .WhereIf(
                    collectionId.HasValue,
                    x =>
                        _appDbContext.ShopifyCollectionProductRecords.Where(
                                y =>
                                    y.ShopifyCollectionRecord.CompanyId == companyUser.CompanyId &&
                                    y.ShopifyCollectionRecord.ShopifyId == config.Id &&
                                    y.ShopifyCollectionRecord.CollectionId == collectionId.Value)
                            .Select(y => y.ProductRecordId).Contains(x.Id))
                .CountAsync();

            var response = new ShopifyCountResponse
            {
                Count = productCount
            };
            if (productCount == 0)
            {
                var service = new ProductService(config.UsersMyShopifyUrl, config.AccessToken);

                var productCountFilter = new ProductCountFilter()
                {
                    CollectionId = collectionId, PublishedStatus = "published"
                };

                response.Count = await service.CountAsync(productCountFilter);
            }

            return Ok(response);
        }
        catch (Exception ex)
        {
            return BadRequest(
                new ResponseViewModel()
                {
                    message = ex.Message
                });
        }
    }

    [HttpGet]
    public async Task<ActionResult<ProductResponse>> GetProduct(
        [FromQuery(Name = "ShopifyId")]
        long? shopifyId,
        [FromQuery(Name = "CollectionId")]
        long? collectionId,
        [FromQuery(Name = "Title")]
        string title,
        [FromQuery(Name = "Offset")]
        int offset = 0,
        [FromQuery(Name = "limit")]
        int limit = 50)
    {
        var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (companyUser == null)
        {
            return Unauthorized();
        }

        var config = await _appDbContext.ConfigShopifyConfigs
            .Where(x => x.CompanyId == companyUser.CompanyId)
            .WhereIf(shopifyId.HasValue, config => config.Id == shopifyId)
            .FirstOrDefaultAsync();

        if (config == null)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = $"Shopify not found"
                });
        }

        var shopifyProductV1CacheKeyPattern = new ShopifyProductV1CacheKeyPattern(config?.Id, collectionId, title, offset, limit);
        var data = await _cacheManagerService.GetCacheAsync(shopifyProductV1CacheKeyPattern);

        if (!string.IsNullOrEmpty(data))
        {
            return Ok(JsonConvert.DeserializeObject<ProductResponse>(data));
        }

        try
        {
            var result = new ProductResponse();

            var products = await _appDbContext.ShopifyProductRecords
                .Where(x => x.CompanyId == companyUser.CompanyId && x.ShopifyId == config.Id)
                .WhereIf(!string.IsNullOrEmpty(title), x => x.Title.Contains(title) || x.Sku.Contains(title))
                .WhereIf(
                    collectionId.HasValue,
                    x =>
                        _appDbContext.ShopifyCollectionProductRecords.Where(
                                y =>
                                    y.ShopifyCollectionRecord.CompanyId == companyUser.CompanyId &&
                                    y.ShopifyCollectionRecord.ShopifyId == config.Id &&
                                    y.ShopifyCollectionRecord.CollectionId == collectionId.Value)
                            .Select(y => y.ProductRecordId).Contains(x.Id))
                .Select(x => x.ProductPayload)
                .Skip(offset).Take(limit)
                .ToListAsync(HttpContext.RequestAborted);

            result.Items = products;

            if (!await _appDbContext.ShopifyProductRecords
                    .AnyAsync(
                        x =>
                            x.CompanyId == companyUser.CompanyId
                            && x.ShopifyId == config.Id)
                || string.IsNullOrEmpty(config.SyncShopifyOrderJobId))
            {
                if (string.IsNullOrEmpty(config.SyncShopifyOrderJobId))
                {
                    config.SyncShopifyOrderJobId = Guid.NewGuid().ToString();
                    await _appDbContext.SaveChangesAsync();
                }

                BackgroundJob.Enqueue<IShopifyIntegrationService>(x => x.SyncProduct(companyUser.CompanyId, shopifyId.Value));
                RecurringJob.AddOrUpdate<IShopifyIntegrationService>(
                    config.SyncShopifyOrderJobId,
                    x => x.SyncProduct(companyUser.CompanyId, shopifyId.Value),
                    $"{0} {19} * * *");

                var service = new ProductService(config.UsersMyShopifyUrl, config.AccessToken);
                var filter = new ProductListFilter()
                {
                    Limit = 250, Status = "active"
                };

                if (collectionId.HasValue)
                {
                    filter.CollectionId = collectionId;
                }

                var listResult = await service.ListAsync(filter);

                result.Items.AddRange(_mapper.Map<List<ShopifyProduct>>(listResult.Items));
            }

            if (companyUser.CompanyId is "e53b13a4-b371-47a6-a4d7-ba10caa296fe"
                or "ef710422-690b-430a-9d3a-369589c46567")
            {
                result.Items.ForEach(x => x.Variants.ForEach(y => y.InventoryQuantity = 100));
            }

            await _cacheManagerService.SaveCacheAsync(shopifyProductV1CacheKeyPattern, result);

            return Ok(result);
        }
        catch (Exception ex)
        {
            return BadRequest(
                new ResponseViewModel()
                {
                    message = ex.Message
                });
        }
    }

    [HttpGet("Share")]
    public async Task<IActionResult> GetProductShareLink(
        [FromQuery(Name = "ShopifyId")]
        long? shopifyId,
        [FromQuery(Name = "CollectionId")]
        long? collectionId,
        [FromQuery(Name = "ProductId")]
        string productId,
        [FromQuery(Name = "UserProfileId")]
        string userProfileId)
    {
        var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (companyUser == null)
        {
            return Unauthorized();
        }

        var config = await _appDbContext.ConfigShopifyConfigs
            .Where(x => x.CompanyId == companyUser.CompanyId)
            .WhereIf(shopifyId.HasValue, config => config.Id == shopifyId)
            .FirstOrDefaultAsync();

        try
        {
            var service = new ProductService(config.UsersMyShopifyUrl, config.AccessToken);

            var filter = new ProductListFilter
            {
                Limit = 250, CollectionId = collectionId, Status = "active"
            };

            if (collectionId.HasValue)
            {
                filter.CollectionId = collectionId;
            }

            try
            {
                var ids = productId
                    .Split(",")
                    .Select(Int64.Parse)
                    .ToList();

                filter.Ids = ids;
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Shopify config {ShopifyConfigId} unable to parse product ids: {ProductIds}. {ExceptionMessage}",
                    shopifyId,
                    JsonConvert.SerializeObject(productId),
                    ex.Message);
            }

            var result = await service.ListAsync(filter);
            var productUrlResult = new List<ProductShareLink>();

            foreach (var product in result.Items)
            {
                var productUrl =
                    $"https://{config.UsersMyShopifyUrl.Replace("https://", string.Empty).Replace("/", string.Empty)}/products/{product.Handle}";
                var shareLinkResult = await _shareLinkService.GenerateShareLink(
                    new CreateShareLinkTracking()
                    {
                        CompanyId = companyUser.CompanyId,
                        Title = product.Title,
                        Url = productUrl,
                        SharedStaffId = companyUser.Id,
                        UserProfileId = userProfileId,
                        ShareLinkType = ShareLinkType.ShopifyProduct
                    });

                productUrlResult.Add(
                    new ProductShareLink()
                    {
                        Title = product.Title, Url = shareLinkResult.TrackingUrl
                    });
            }

            return Ok(productUrlResult);
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "Unable to generate Shopify product link for ShopifyConfig {ShopifyId}, " +
                "product collection {CollectionId}," +
                "product {ProductId}," +
                "and UserProfile {UserProfileId}: {ExceptionMessage}",
                shopifyId, collectionId, productId, userProfileId,
                ex.Message);

            return BadRequest(
                new ResponseViewModel()
                {
                    message = ex.Message
                });
        }
    }
}