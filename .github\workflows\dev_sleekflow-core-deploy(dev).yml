name: dev_sleekflow-core-deploy(dev)

on:
  push:
    branches:
      - dev

concurrency: sleekflow-core-deploy-dev

env:
  NUGET_PACKAGES: ${{ github.workspace }}/.nuget/packages

jobs:
  update:
    name: Update
    runs-on:
      group: "Default Middle Runners"
    steps:
      - uses: actions/checkout@v3

      - name: Set up .NET Core
        uses: actions/setup-dotnet@v3
        with:
          dotnet-version: '8.0.303'
          include-prerelease: false

      - name: Install Azure Cli
        run: |
          curl -sL https://aka.ms/InstallAzureCLIDeb | sudo bash

      - uses: actions/cache@v3
        with:
          path: ${{ github.workspace }}/.nuget/packages
          key: ${{ runner.os }}-nuget-${{ hashFiles('**/*.csproj') }}
          restore-keys: |
            ${{ runner.os }}-nuget-

      - name: Restore with dotnet
        run: dotnet restore

      - name: Build with dotnet
        run: |
          dotnet publish Travis_backend.Auth0 -c Release --self-contained /p:ExcludeBuildDbMigration=TRUE
          dotnet publish Sleekflow.SleekPay -c Release

      - name: Build images locally
        run: |
          docker compose -f docker-compose.common.yml build
          docker compose -f docker-compose.yml build

      - name: Deploy Everything
        uses: pulumi/actions@v4
        if: github.ref == 'refs/heads/dev' || github.event.pull_request.base.ref == 'dev'
        with:
          command: up
          stack-name: dev
          work-dir: ./Sleekflow.Core.Infra/
        env:
          PULUMI_ACCESS_TOKEN: ${{ secrets.PULUMI_ACCESS_TOKEN }}
          ARM_CLIENT_ID: ${{ secrets.AZURE_SP_ARM_CLIENT_ID }}
          ARM_CLIENT_SECRET: ${{ secrets.AZURE_SP_ARM_CLIENT_SECRET }}
          ARM_SUBSCRIPTION_ID: ${{ secrets.AZURE_SP_ARM_SUBSCRIPTION_ID }}
          ARM_TENANT_ID: ${{ secrets.AZURE_SP_ARM_TENANT_ID }}

      - name: Azure Login
        uses: azure/login@v1
        with:
          creds: ${{ secrets.AZURE_SP_LOGIN_CREDS }}

      - name: Swap Standby to Production
        run: |
          az webapp deployment slot swap -g sleekflow-core-rg-eas-deve3bd117d -n sleekflow-core-app-eas-dev --slot standby --target-slot production

      - name: Deploy Everything Again
        uses: pulumi/actions@v4
        if: github.ref == 'refs/heads/dev' || github.event.pull_request.base.ref == 'dev'
        with:
          command: up
          stack-name: dev
          work-dir: ./Sleekflow.Core.Infra/
        env:
          PULUMI_ACCESS_TOKEN: ${{ secrets.PULUMI_ACCESS_TOKEN }}
          ARM_CLIENT_ID: ${{ secrets.AZURE_SP_ARM_CLIENT_ID }}
          ARM_CLIENT_SECRET: ${{ secrets.AZURE_SP_ARM_CLIENT_SECRET }}
          ARM_SUBSCRIPTION_ID: ${{ secrets.AZURE_SP_ARM_SUBSCRIPTION_ID }}
          ARM_TENANT_ID: ${{ secrets.AZURE_SP_ARM_TENANT_ID }}

      - name: Run tests with coverage
        run: dotnet test Sleekflow.Core.Tests/Sleekflow.Core.Tests.csproj --collect:"XPlat Code Coverage"
        continue-on-error: true

      - name: Generate coverage report
        run: |
         dotnet tool install --global dotnet-reportgenerator-globaltool
         reportgenerator -reports:Sleekflow.Core.Tests/TestResults/**/coverage.cobertura.xml -targetdir:coverage-report -reporttypes:Html
        continue-on-error: true

      - name: Upload coverage report
        uses: actions/upload-artifact@v4
        with:
          name: coverage-report
          path: coverage-report