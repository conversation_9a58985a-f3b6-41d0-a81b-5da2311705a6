﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using AutoMapper;
using Hangfire;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using Newtonsoft.Json.Serialization;
using Sleekflow.Apis.CrmHub.Api;
using Sleekflow.Apis.CrmHub.Model;
using Sleekflow.Apis.FlowHub.Api;
using Sleekflow.Apis.FlowHub.Model;
using Travis_backend.AnalyticsDomain.Services;
using Travis_backend.AutomationDomain.Models;
using Travis_backend.BackgroundTaskServices.Helpers;
using Travis_backend.BackgroundTaskServices.Models;
using Travis_backend.BroadcastDomain.Services;
using Travis_backend.Cache;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.CompanyDomain.Services;
using Travis_backend.Constants;
using Travis_backend.ContactDomain.Services;
using Travis_backend.ContactDomain.ViewModels;
using Travis_backend.ConversationDomain.Models;
using Travis_backend.ConversationDomain.ViewModels;
using Travis_backend.ConversationServices;
using Travis_backend.Data.BackgroundTask;
using Travis_backend.Database;
using Travis_backend.**********************;
using Travis_backend.Enums;
using Travis_backend.FileDomain.Services;
using Travis_backend.FlowHubs;
using Travis_backend.Helpers;
using Travis_backend.MessageDomain.Models;
using Travis_backend.MessageDomain.Repositories;
using Travis_backend.Models.BackgroundTask;
using Travis_backend.SignalR;
using Travis_backend.SleekflowCrmHubDomain.Services;

namespace Travis_backend.BackgroundTaskServices
{
    public interface IBackgroundTaskService
    {
        Task Handle(long backgroundTaskId);

        Task<BackgroundTask> EnqueueAddContactsToListTask(
            string userId,
            long staffId,
            string companyId,
            long listId,
            UserProfileIdsViewModel userprofileViewModel);

        Task<BackgroundTask> EnqueueImportContactsTask(
            string userId,
            long staffId,
            string companyId,
            long listId,
            bool isImportIntoList,
            bool isTriggerAutomation,
            ImportSpreadsheet importSpreadsheet,
            long? importContactToListRecordId = null);

        Task<BackgroundTask> EnqueueImportContactsTaskV2(
            string userId,
            long staffId,
            string companyId,
            long listId,
            bool isImportIntoList,
            bool isTriggerAutomation,
            ImportSpreadsheet importSpreadsheet,
            long? importContactToListRecordId = null);

        /// <summary>
        /// Create a background task for bulk importing contacts.
        /// </summary>
        /// <remarks>
        /// Does not enqueue the task. Enqueue with <see cref="EnqueueBulkImportContactsTask"/>.
        /// </remarks>
        /// <param name="userId">User ID.</param>
        /// <param name="staffId">Staff ID.</param>
        /// <param name="companyId">Company ID.</param>
        /// <param name="listId">List ID.</param>
        /// <param name="isImportIntoList">Whether the contacts are being imported into a list.</param>
        /// <param name="isTriggerAutomation">Whether to trigger the automation afterwards.</param>
        /// <param name="importSpreadsheet"><see cref="ImportSpreadsheet"/>.</param>
        /// <param name="importContactToListRecordId">Record ID.</param>
        /// <returns><see cref="BackgroundTask"/>.</returns>
        Task<BackgroundTask> CreateBulkImportContactsTask(
            string userId,
            long staffId,
            string companyId,
            long listId,
            bool isImportIntoList,
            bool isTriggerAutomation,
            ImportSpreadsheet importSpreadsheet,
            long? importContactToListRecordId = null);

        /// <summary>
        /// Enqueue the background task for bulk importing contacts.
        /// </summary>
        /// <param name="companyId">Company ID.</param>
        /// <param name="taskId">Background task ID.</param>
        /// <returns><see cref="Task"/>.</returns>
        Task EnqueueBulkImportContactsTask(
            string companyId,
            long taskId);

        Task<BackgroundTask> EnqueueBulkUpdateCustomFieldsTask(
            string userId,
            string companyId,
            long staffId,
            BulkUpdateCustomFieldsViewModel bulkUpdateCustomFieldsViewModel);

        Task<BackgroundTask> EnqueueExportContactsListToCsvTask(
            string userId,
            string companyId,
            long staffId,
            UserProfileIdsViewModel userprofileViewModel);

        Task<BackgroundTask> EnqueueExportBroadcastStatusToCsvTask(
            string userId,
            string companyId,
            long staffId,
            string broadcastTemplateId,
            int count);

        Task<BackgroundTask> EnqueueExportStatisticsDataToCsvTask(
            string userId,
            string companyId,
            long staffId,
            string staffUserId,
            DateTime messageFrom,
            DateTime messageTo,
            string status = null,
            string channels = null,
            string channelIds = null,
            string tags = null,
            long? teamId = null,
            bool? isTeamUnassigned = null,
            List<Condition> conditions = null);

        Task<BackgroundTask> EnqueueImportWhatsAppHistoryTask(
            string userId,
            string companyId,
            long staffId,
            ImportWhatsAppHistoryViewModel importWhatsAppHistoryViewModel);

        Task<BackgroundTask> EnqueueConvertCampaignLeadsToContactListTask(
            string userId,
            string companyId,
            long staffId,
            string campaignId,
            long listId,
            ConvertCampaignLeadsToContactListTaskPayLoad convertCampaignLeadsToContactListTaskPayLoad);

        Task<BackgroundTask> EnqueueImportGenericHistoryTask(
            string userId,
            string companyId,
            long staffId,
            ImportGenericHistoryViewModel importGenericHistoryViewModel);

        Task<BackgroundTask> EnqueueLoopThroughAndEnrollContactsToFlowHubTask(
            string userId,
            string companyId,
            long staffId,
            string flowHubWorkflowId,
            string flowHubWorkflowVersionedId);

        Task<BackgroundTask> EnqueueExportFlowHubWorkflowExecutionUsagesToCsvTask(
            string userId,
            string companyId,
            long staffId,
            WorkflowExecutionUsageFilters filters);
    }

    public class BackgroundTaskService : IBackgroundTaskService
    {
        private readonly ApplicationDbContext _appDbContext;
        private readonly IMapper _mapper;
        private readonly ILogger _logger;
        private readonly IConfiguration _configuration;
        private readonly ICompanyService _companyService;
        private readonly IUserProfileService _userProfileService;
        private readonly IUserProfileImportService _userProfileImportService;
        private readonly ISignalRService _signalRService;
        private readonly IUploadService _uploadService;
        private readonly IAzureBlobStorageService _azureBlobStorageService;
        private readonly IBroadcastService _broadcastService;
        private readonly IAnalyticsService _analyticsService;
        private readonly IConversationMessageService _conversationMessageService;
        private readonly IChannelIdentityIdRepository _channelIdentityIdRepository;
        private readonly ICrmHubService _crmHubService;
        private readonly IObjectsApi _objectsApi;
        private readonly IFlowHubService _flowHubService;
        private readonly IInflowActionsApi _inflowActionsApi;
        private readonly IExecutionsApi _workflowExecutionsApi;
        private readonly IUserProfileListHooks _userProfileListHooks;
        private readonly IDistributedInvocationContextService _distributedInvocationContextService;

        private readonly JsonSerializerSettings _jsonSerializerSettings = new ()
        {
            NullValueHandling = NullValueHandling.Ignore,
            DateTimeZoneHandling = DateTimeZoneHandling.Utc,
            ContractResolver = new CamelCasePropertyNamesContractResolver(),
        };

        public BackgroundTaskService(
            ApplicationDbContext appDbContext,
            IMapper mapper,
            ILogger<BackgroundTaskService> logger,
            IConfiguration configuration,
            ICompanyService companyService,
            IUserProfileService userProfileService,
            IUserProfileImportService userProfileImportService,
            ISignalRService signalRService,
            IAzureBlobStorageService azureBlobStorageService,
            IUploadService uploadService,
            IBroadcastService broadcastService,
            IAnalyticsService analyticsService,
            IConversationMessageService conversationMessageService,
            IChannelIdentityIdRepository channelIdentityIdRepository,
            ICrmHubService crmHubService,
            IObjectsApi objectsApi,
            IFlowHubService flowHubService,
            IInflowActionsApi inflowActionsApi,
            IExecutionsApi workflowExecutionsApi,
            IUserProfileListHooks userProfileListHooks,
            IDistributedInvocationContextService distributedInvocationContextService)
        {
            _appDbContext = appDbContext;
            _mapper = mapper;
            _logger = logger;
            _configuration = configuration;
            _companyService = companyService;
            _userProfileService = userProfileService;
            _userProfileImportService = userProfileImportService;
            _signalRService = signalRService;
            _azureBlobStorageService = azureBlobStorageService;
            _uploadService = uploadService;
            _broadcastService = broadcastService;
            _analyticsService = analyticsService;
            _conversationMessageService = conversationMessageService;
            _channelIdentityIdRepository = channelIdentityIdRepository;
            _crmHubService = crmHubService;
            _objectsApi = objectsApi;
            _flowHubService = flowHubService;
            _inflowActionsApi = inflowActionsApi;
            _workflowExecutionsApi = workflowExecutionsApi;
            _userProfileListHooks = userProfileListHooks;
            _distributedInvocationContextService = distributedInvocationContextService;
            _jsonSerializerSettings.Converters.Add(new StringEnumConverter());
        }

        public async Task Handle(long backgroundTaskId)
        {
            var backgroundTask = await _appDbContext.BackgroundTasks
                .FirstOrDefaultAsync(
                    x =>
                        x.Id == backgroundTaskId
                        && !x.IsCompleted);

            if (backgroundTask == null)
            {
                return;
            }

            // Read from blob storage
            var payloadTaskBlob = await _azureBlobStorageService.DownloadFromAzureBlob(
                $"{backgroundTask.Id}.json",
                "background-tasks");

            var payloadTask = Encoding.UTF8.GetString(payloadTaskBlob.ToArray());

            try
            {
                switch (backgroundTask.TaskType)
                {
                    case BackgroundTaskType.AddContactsToList:
                        await HandleAddContactsToList(
                            backgroundTask,
                            JsonConvert.DeserializeObject<AddContactsToListBackgroundTaskPayload>(payloadTask));

                        break;
                    case BackgroundTaskType.ImportContacts:
                        await HandleImportContacts(
                            backgroundTask,
                            JsonConvert.DeserializeObject<ImportContactsBackgroundTaskPayload>(payloadTask));

                        break;
                    case BackgroundTaskType.ImportContactsV2:
                        await HandleImportContactsV2(
                            backgroundTask,
                            JsonConvert.DeserializeObject<ImportContactsBackgroundTaskPayload>(payloadTask));

                        break;
                    case BackgroundTaskType.BulkImportContacts:
                        await HandleBulkImportContacts(
                            backgroundTask,
                            JsonConvert.DeserializeObject<ImportContactsBackgroundTaskPayload>(payloadTask));

                        break;
                    case BackgroundTaskType.BulkUpdateContactsCustomFields:
                        await HandleBulkUpdateContactsCustomFields(
                            backgroundTask,
                            JsonConvert.DeserializeObject<BulkUpdateCustomFieldsBackgroundTaskPayload>(payloadTask));

                        break;
                    case BackgroundTaskType.ExportContactsListToCsv:
                        await HandleExportContactsListToCsv(
                            backgroundTask,
                            JsonConvert.DeserializeObject<ExportContactsListToCsvBackgroundTaskPayload>(payloadTask));

                        break;
                    case BackgroundTaskType.ExportBroadcastStatusListToCsv:
                        await HandleExportBroadcastStatusListToCsv(
                            backgroundTask,
                            JsonConvert.DeserializeObject<ExportBroadcastStatusToCsvBackgroundTaskPayload>(
                                payloadTask));

                        break;
                    case BackgroundTaskType.ExportAnalyticToCsv:
                        await HandleExportAnalyticToCsv(
                            backgroundTask,
                            JsonConvert.DeserializeObject<ExportAnalyticListToCsvBackgroundTaskPayload>(payloadTask));

                        break;
                    case BackgroundTaskType.ImportWhatsAppHistory:
                        await HandleImportWhatsAppHistory(
                            backgroundTask,
                            JsonConvert.DeserializeObject<ImportWhatsAppHistoryTaskPayLoad>(payloadTask));

                        break;

                    case BackgroundTaskType.ImportGenericHistory:
                        await HandleImportGenericHistory(
                            backgroundTask,
                            JsonConvert.DeserializeObject<ImportGenericHistoryTaskPayLoad>(payloadTask));

                        break;
                    case BackgroundTaskType.ConvertCampaignLeadsToContactList:
                        await HandleConvertCampaignLeadsToContactList(
                            backgroundTask,
                            JsonConvert.DeserializeObject<ConvertCampaignLeadsToContactListTaskPayLoad>(payloadTask));

                        break;
                    case BackgroundTaskType.LoopThroughAndEnrollContactsToFlowHub:
                        await HandleLoopThroughAndEnrollContactsToFlowHub(
                            backgroundTask);

                        break;
                    case BackgroundTaskType.ExportFlowHubWorkflowExecutionUsagesToCsv:
                        await HandleExportFlowHubWorkflowExecutionUsagesToCsv(
                            backgroundTask,
                            JsonConvert.DeserializeObject<ExportFlowHubWorkflowExecutionUsagesToCsvTaskTargetPayload>(
                                payloadTask));

                        break;
                }
            }
            catch (Exception e)
            {
                _logger.LogError(
                    e,
                    "BackgroundTaskService Handle Error occurred, Id: {BackgroundTaskId}, CompanyId: {CompanyId}",
                    backgroundTask.Id,
                    backgroundTask.CompanyId);

                backgroundTask.ErrorMessage = e.Message;
                backgroundTask.UpdatedAt = DateTime.UtcNow;

                await _appDbContext.SaveChangesAsync();

                await _signalRService.SignalROnBackgroundTaskStatusChange(
                    backgroundTask.UserId,
                    backgroundTask,
                    BackgroundTaskStatus.Error);

                throw;
            }
        }

        #region Task Handlers

        private async Task HandleAddContactsToList(
            BackgroundTask backgroundTask,
            AddContactsToListBackgroundTaskPayload taskPayload)
        {
            backgroundTask.StartedAt = DateTime.UtcNow;

            await _signalRService.SignalROnBackgroundTaskStatusChange(
                backgroundTask.UserId,
                backgroundTask,
                BackgroundTaskStatus.Started);

            // Update Name first if needed
            if (taskPayload.UserprofileViewModel.GroupListName != null)
            {
                await _userProfileService.AddToUserList(
                    taskPayload.CompanyId,
                    taskPayload.ListId,
                    new UserProfileIdsViewModel
                    {
                        GroupListName = taskPayload.UserprofileViewModel.GroupListName
                    });
            }

            var total = backgroundTask.Total;

            var amountPreExecution = BackgroundTaskHelper.CalculateAmountPreExecution(total, minPreExecution: 100);
            var completed = 0;

            var result = new List<ImportedUserProfileBackgroundTaskResultPayload>();

            foreach (var userProfileIds in taskPayload.UserprofileViewModel.UserProfileIds.Chunk(amountPreExecution))
            {
                var batchResult = await _userProfileService.AddToUserList(
                    taskPayload.CompanyId,
                    taskPayload.ListId,
                    new UserProfileIdsViewModel
                    {
                        UserProfileIds = userProfileIds.ToList()
                    },
                    backgroundTask.StaffId);

                // No need to store the result as it is not needed
                // result.AddRange(
                //     batchResult.Select(
                //         x => new ImportedUserProfileBackgroundTaskResultPayload
                //         {
                //             Id = x.Id,
                //             ImportContactHistoryId = x.ImportContactHistoryId,
                //             UserProfileId = x.UserProfileId,
                //         }));

                completed += amountPreExecution;

                backgroundTask.Progress = BackgroundTaskHelper.CalculateProgress(completed, total);
                backgroundTask.UpdatedAt = DateTime.UtcNow;

                await _appDbContext.SaveChangesAsync();

                await _signalRService.SignalROnBackgroundTaskStatusChange(
                    backgroundTask.UserId,
                    backgroundTask,
                    BackgroundTaskStatus.Processing);
            }

            backgroundTask.ResultPayload = JsonConvert.SerializeObject(result, _jsonSerializerSettings);
            backgroundTask.UpdatedAt = DateTime.UtcNow;
            backgroundTask.CompletedAt = DateTime.UtcNow;
            backgroundTask.IsCompleted = true;
            backgroundTask.ErrorMessage = null;
            backgroundTask.Progress = 100;

            await _appDbContext.SaveChangesAsync();

            await _signalRService.SignalROnBackgroundTaskStatusChange(
                backgroundTask.UserId,
                backgroundTask,
                BackgroundTaskStatus.Completed);

            // remove the json
            await _azureBlobStorageService.DeleteFromAzureBlob($"{backgroundTask.Id}.json", "background-tasks");
        }

        private async Task HandleImportContacts(
            BackgroundTask backgroundTask,
            ImportContactsBackgroundTaskPayload taskPayload)
        {
            backgroundTask.StartedAt = DateTime.UtcNow;

            await _signalRService.SignalROnBackgroundTaskStatusChange(
                backgroundTask.UserId,
                backgroundTask,
                BackgroundTaskStatus.Started);

            var progressMilestoneStep = BackgroundTaskHelper.CalculateMilestoneStep(backgroundTask.Total);

            var progressMilestoneStepPercent =
                BackgroundTaskHelper.CalculateProgress(progressMilestoneStep, backgroundTask.Total);

            if (progressMilestoneStepPercent == 0)
            {
                progressMilestoneStepPercent = 1;
            }

            var progressMilestone = progressMilestoneStepPercent;

            await _userProfileImportService.ImportUserProfileBackground(
                taskPayload.ListId,
                taskPayload.CompanyId,
                backgroundTask.StaffId,
                taskPayload.ImportSpreadsheet,
                taskPayload.IsImportIntoList,
                taskPayload.IsTriggerAutomation,
                async completed =>
                {
                    var progress = BackgroundTaskHelper.CalculateProgress(completed, backgroundTask.Total);

                    if (progress >= progressMilestone)
                    {
                        backgroundTask.Progress = progress;
                        backgroundTask.UpdatedAt = DateTime.UtcNow;
                        await _appDbContext.SaveChangesAsync();

                        await _signalRService.SignalROnBackgroundTaskStatusChange(
                            backgroundTask.UserId,
                            backgroundTask,
                            BackgroundTaskStatus.Processing);
                        progressMilestone += progressMilestoneStepPercent;
                    }
                },
                taskPayload.ImportContactToListRecordId);

            backgroundTask.ResultPayload = null;
            backgroundTask.UpdatedAt = DateTime.UtcNow;
            backgroundTask.CompletedAt = DateTime.UtcNow;
            backgroundTask.IsCompleted = true;
            backgroundTask.ErrorMessage = null;
            backgroundTask.Progress = 100;

            await _appDbContext.SaveChangesAsync();

            await _signalRService.SignalROnBackgroundTaskStatusChange(
                backgroundTask.UserId,
                backgroundTask,
                BackgroundTaskStatus.Completed);

            // remove the json
            await _azureBlobStorageService.DeleteFromAzureBlob($"{backgroundTask.Id}.json", "background-tasks");
        }

        private async Task HandleImportContactsV2(
            BackgroundTask backgroundTask,
            ImportContactsBackgroundTaskPayload taskPayload)
        {
            backgroundTask.StartedAt = DateTime.UtcNow;

            await _signalRService.SignalROnBackgroundTaskStatusChange(
                backgroundTask.UserId,
                backgroundTask,
                BackgroundTaskStatus.Started);

            var progressMilestoneStep = BackgroundTaskHelper.CalculateMilestoneStep(backgroundTask.Total);

            var progressMilestoneStepPercent =
                BackgroundTaskHelper.CalculateProgress(progressMilestoneStep, backgroundTask.Total);

            if (progressMilestoneStepPercent == 0)
            {
                progressMilestoneStepPercent = 1;
            }

            var progressMilestone = progressMilestoneStepPercent;

            await _userProfileImportService.ImportUserProfileBackgroundV2(
                taskPayload.ListId,
                taskPayload.CompanyId,
                backgroundTask.StaffId,
                taskPayload.ImportSpreadsheet,
                taskPayload.IsImportIntoList,
                taskPayload.IsTriggerAutomation,
                async completed =>
                {
                    var progress = BackgroundTaskHelper.CalculateProgress(completed, backgroundTask.Total);

                    if (progress >= progressMilestone)
                    {
                        backgroundTask.Progress = progress;
                        backgroundTask.UpdatedAt = DateTime.UtcNow;
                        await _appDbContext.SaveChangesAsync();

                        await _signalRService.SignalROnBackgroundTaskStatusChange(
                            backgroundTask.UserId,
                            backgroundTask,
                            BackgroundTaskStatus.Processing);
                        progressMilestone += progressMilestoneStepPercent;
                    }
                },
                taskPayload.ImportContactToListRecordId);

            backgroundTask.ResultPayload = null;
            backgroundTask.UpdatedAt = DateTime.UtcNow;
            backgroundTask.CompletedAt = DateTime.UtcNow;
            backgroundTask.IsCompleted = true;
            backgroundTask.ErrorMessage = null;
            backgroundTask.Progress = 100;

            await _appDbContext.SaveChangesAsync();

            await _signalRService.SignalROnBackgroundTaskStatusChange(
                backgroundTask.UserId,
                backgroundTask,
                BackgroundTaskStatus.Completed);

            // remove the json
            await _azureBlobStorageService.DeleteFromAzureBlob($"{backgroundTask.Id}.json", "background-tasks");
        }

        private async Task HandleBulkImportContacts(
            BackgroundTask backgroundTask,
            ImportContactsBackgroundTaskPayload taskPayload)
        {
            backgroundTask.StartedAt = DateTime.UtcNow;

            await _signalRService.SignalROnBackgroundTaskStatusChange(
                backgroundTask.UserId,
                backgroundTask,
                BackgroundTaskStatus.Started);

            var progressMilestoneStep = BackgroundTaskHelper.CalculateMilestoneStep(backgroundTask.Total);

            var progressMilestoneStepPercent =
                BackgroundTaskHelper.CalculateProgress(progressMilestoneStep, backgroundTask.Total);

            if (progressMilestoneStepPercent == 0)
            {
                progressMilestoneStepPercent = 1;
            }

            var progressMilestone = progressMilestoneStepPercent;

            await _userProfileImportService.BulkImportUserProfileBackground(
                taskPayload.ListId,
                taskPayload.CompanyId,
                backgroundTask.StaffId,
                taskPayload.ImportSpreadsheet,
                taskPayload.IsImportIntoList,
                async completed =>
                {
                    var progress = BackgroundTaskHelper.CalculateProgress(completed, backgroundTask.Total);

                    if (progress >= progressMilestone)
                    {
                        backgroundTask.Progress = progress;
                        backgroundTask.UpdatedAt = DateTime.UtcNow;
                        await _appDbContext.SaveChangesAsync();

                        await _signalRService.SignalROnBackgroundTaskStatusChange(
                            backgroundTask.UserId,
                            backgroundTask,
                            BackgroundTaskStatus.Processing);
                        progressMilestone += progressMilestoneStepPercent;
                    }
                },
                taskPayload.ImportContactToListRecordId);

            backgroundTask.ResultPayload = null;
            backgroundTask.UpdatedAt = DateTime.UtcNow;
            backgroundTask.CompletedAt = DateTime.UtcNow;
            backgroundTask.IsCompleted = true;
            backgroundTask.ErrorMessage = null;
            backgroundTask.Progress = 100;

            await _appDbContext.SaveChangesAsync();

            await _signalRService.SignalROnBackgroundTaskStatusChange(
                backgroundTask.UserId,
                backgroundTask,
                BackgroundTaskStatus.Completed);

            // remove the json
            await _azureBlobStorageService.DeleteFromAzureBlob($"{backgroundTask.Id}.json", "background-tasks");
        }

        private async Task HandleBulkUpdateContactsCustomFields(
            BackgroundTask backgroundTask,
            BulkUpdateCustomFieldsBackgroundTaskPayload taskPayload)
        {
            backgroundTask.StartedAt = DateTime.UtcNow;

            await _signalRService.SignalROnBackgroundTaskStatusChange(
                backgroundTask.UserId,
                backgroundTask,
                BackgroundTaskStatus.Started);

            var progressMilestoneStep = BackgroundTaskHelper.CalculateMilestoneStep(backgroundTask.Total);

            var progressMilestoneStepPercent =
                BackgroundTaskHelper.CalculateProgress(progressMilestoneStep, backgroundTask.Total);

            if (progressMilestoneStepPercent == 0)
            {
                progressMilestoneStepPercent = 1;
            }

            var progressMilestone = progressMilestoneStepPercent;

            await _userProfileService.BulkUpdateUserProfiles(
                taskPayload.CompanyId,
                taskPayload.StaffId,
                taskPayload.BulkUpdateCustomFieldsViewModel,
                false,
                async completed =>
                {
                    var progress = BackgroundTaskHelper.CalculateProgress(completed, backgroundTask.Total);

                    if (progress >= progressMilestone)
                    {
                        backgroundTask.Progress = progress;
                        backgroundTask.UpdatedAt = DateTime.UtcNow;
                        await _appDbContext.SaveChangesAsync();

                        await _signalRService.SignalROnBackgroundTaskStatusChange(
                            backgroundTask.UserId,
                            backgroundTask,
                            BackgroundTaskStatus.Processing);
                        progressMilestone += progressMilestoneStepPercent;
                    }
                });

            backgroundTask.ResultPayload = null;
            backgroundTask.UpdatedAt = DateTime.UtcNow;
            backgroundTask.CompletedAt = DateTime.UtcNow;
            backgroundTask.IsCompleted = true;
            backgroundTask.ErrorMessage = null;
            backgroundTask.Progress = 100;

            await _appDbContext.SaveChangesAsync();

            await _signalRService.SignalROnBackgroundTaskStatusChange(
                backgroundTask.UserId,
                backgroundTask,
                BackgroundTaskStatus.Completed);

            // remove the json
            await _azureBlobStorageService.DeleteFromAzureBlob($"{backgroundTask.Id}.json", "background-tasks");
        }

        private async Task HandleExportContactsListToCsv(
            BackgroundTask backgroundTask,
            ExportContactsListToCsvBackgroundTaskPayload taskPayload)
        {
            backgroundTask.StartedAt = DateTime.UtcNow;

            await _signalRService.SignalROnBackgroundTaskStatusChange(
                backgroundTask.UserId,
                backgroundTask,
                BackgroundTaskStatus.Started);

            var progressMilestoneStep = BackgroundTaskHelper.CalculateMilestoneStep(backgroundTask.Total);

            var progressMilestoneStepPercent =
                BackgroundTaskHelper.CalculateProgress(progressMilestoneStep, backgroundTask.Total);

            if (progressMilestoneStepPercent == 0)
            {
                progressMilestoneStepPercent = 1;
            }

            var progressMilestone = progressMilestoneStepPercent;

            var csv = await _userProfileService.ExportUserProfileInCsv(
                taskPayload.CompanyId,
                taskPayload.StaffId,
                taskPayload.UserprofileViewModel,
                async completed =>
                {
                    var progress = BackgroundTaskHelper.CalculateProgress(completed, backgroundTask.Total);

                    if (progress >= progressMilestone)
                    {
                        backgroundTask.Progress = progress;
                        backgroundTask.UpdatedAt = DateTime.UtcNow;
                        await _appDbContext.SaveChangesAsync();

                        await _signalRService.SignalROnBackgroundTaskStatusChange(
                            backgroundTask.UserId,
                            backgroundTask,
                            BackgroundTaskStatus.Processing);
                        progressMilestone += progressMilestoneStepPercent;
                    }
                });

            var ms = new MemoryStream();
            var sw = new StreamWriter(ms, Encoding.UTF8);
            await sw.WriteAsync(csv);
            await sw.FlushAsync();
            ms.Seek(0, SeekOrigin.Begin);

            var storageConfig = await _appDbContext.ConfigStorageConfigs
                .AsNoTracking()
                .FirstOrDefaultAsync(x => x.CompanyId == backgroundTask.CompanyId);

            var fileName = $"SleekFlow Exported Contacts {backgroundTask.CreatedAt:yyyyMMdd_HHmmss}.csv";
            var filePath = $"ExportContacts/{backgroundTask.StaffId}/{fileName}";

            var uploadFileResult = await _uploadService.UploadFileBySteam(
                storageConfig?.ContainerName ?? backgroundTask.CompanyId,
                filePath,
                ms,
                "text/csv");

            var sasUri = _azureBlobStorageService.GetAzureBlobSasUri(
                filePath,
                storageConfig?.ContainerName ?? backgroundTask.CompanyId,
                24);

            var result = new ExportContactsListToCsvBackgroundTaskResultPayload
            {
                FileName = fileName,
                FilePath = filePath,
                MIMEType = "text/csv",
                Url = sasUri,
                FileSize = uploadFileResult.FileSizeInByte,
            };

            try
            {
                var contactListBackgroundTaskTargetPayload =
                    JsonConvert.DeserializeObject<ContactListBackgroundTaskTargetPayload>(backgroundTask.TargetPayload);

                _distributedInvocationContextService.SetContext(
                    backgroundTask.CompanyId,
                    backgroundTask.StaffId.ToString());

                await _userProfileListHooks.OnUserProfileListExportedAsync(
                    contactListBackgroundTaskTargetPayload.ListId.ToString(),
                    contactListBackgroundTaskTargetPayload.ImportName);
            }
            catch (Exception e)
            {
                _logger.LogError(e, "Error deserializing background task target payload");
            }

            backgroundTask.ResultPayload = JsonConvert.SerializeObject(result, _jsonSerializerSettings);
            backgroundTask.UpdatedAt = DateTime.UtcNow;
            backgroundTask.CompletedAt = DateTime.UtcNow;
            backgroundTask.Progress = 100;
            backgroundTask.ErrorMessage = null;
            backgroundTask.IsCompleted = true;

            await _appDbContext.SaveChangesAsync();

            await _signalRService.SignalROnBackgroundTaskStatusChange(
                backgroundTask.UserId,
                backgroundTask,
                BackgroundTaskStatus.Completed);

            // remove the json
            await _azureBlobStorageService.DeleteFromAzureBlob($"{backgroundTask.Id}.json", "background-tasks");
        }

        private async Task HandleExportBroadcastStatusListToCsv(
            BackgroundTask backgroundTask,
            ExportBroadcastStatusToCsvBackgroundTaskPayload taskPayload)
        {
            backgroundTask.StartedAt = DateTime.UtcNow;

            await _signalRService.SignalROnBackgroundTaskStatusChange(
                backgroundTask.UserId,
                backgroundTask,
                BackgroundTaskStatus.Started);

            var progressMilestoneStep = BackgroundTaskHelper.CalculateMilestoneStep(backgroundTask.Total);

            var progressMilestoneStepPercent =
                BackgroundTaskHelper.CalculateProgress(progressMilestoneStep, backgroundTask.Total);

            if (progressMilestoneStepPercent == 0)
            {
                progressMilestoneStepPercent = 1;
            }

            var progressMilestone = progressMilestoneStepPercent;

            var messageTemplates = await _appDbContext.CompanyMessageTemplates
                .AsNoTracking()
                .FirstAsync(
                    x =>
                        x.CompanyId == taskPayload.CompanyId
                        && x.Id == taskPayload.BroadcastTemplateId);

            var csv = await _broadcastService.ExportBroadcastStatus(
                taskPayload.CompanyId,
                taskPayload.BroadcastTemplateId,
                taskPayload.StaffId,
                async completed =>
                {
                    var progress = BackgroundTaskHelper.CalculateProgress(completed, backgroundTask.Total);

                    if (progress >= progressMilestone)
                    {
                        backgroundTask.Progress = progress;
                        backgroundTask.UpdatedAt = DateTime.UtcNow;
                        await _appDbContext.SaveChangesAsync();

                        await _signalRService.SignalROnBackgroundTaskStatusChange(
                            backgroundTask.UserId,
                            backgroundTask,
                            BackgroundTaskStatus.Processing);
                        progressMilestone += progressMilestoneStepPercent;
                    }
                });

            var ms = new MemoryStream();
            var sw = new StreamWriter(ms, Encoding.UTF8);
            await sw.WriteAsync(csv);
            await sw.FlushAsync();
            ms.Seek(0, SeekOrigin.Begin);

            var storageConfig = await _appDbContext.ConfigStorageConfigs
                .AsNoTracking()
                .FirstOrDefaultAsync(x => x.CompanyId == backgroundTask.CompanyId);

            var fileName = $"{messageTemplates.TemplateName.Trim()}-{DateTime.UtcNow:yyyyMMdd}.csv";
            var filePath = $"ExportBroadcastStatus/{backgroundTask.StaffId}/{fileName}";

            var uploadFileResult = await _uploadService.UploadFileBySteam(
                storageConfig?.ContainerName ?? backgroundTask.CompanyId,
                filePath,
                ms,
                "text/csv");

            var sasUri = _azureBlobStorageService.GetAzureBlobSasUri(
                filePath,
                storageConfig?.ContainerName ?? backgroundTask.CompanyId,
                24);

            var result = new ExportBroadcastStatusListToCsvBackgroundTaskResultPayload
            {
                FileName = fileName,
                FilePath = filePath,
                MIMEType = "text/csv",
                Url = sasUri,
                FileSize = uploadFileResult.FileSizeInByte,
            };

            backgroundTask.ResultPayload = JsonConvert.SerializeObject(result, _jsonSerializerSettings);
            backgroundTask.UpdatedAt = DateTime.UtcNow;
            backgroundTask.CompletedAt = DateTime.UtcNow;
            backgroundTask.Progress = 100;
            backgroundTask.ErrorMessage = null;
            backgroundTask.IsCompleted = true;

            await _appDbContext.SaveChangesAsync();

            await _signalRService.SignalROnBackgroundTaskStatusChange(
                backgroundTask.UserId,
                backgroundTask,
                BackgroundTaskStatus.Completed);

            // remove the json
            await _azureBlobStorageService.DeleteFromAzureBlob($"{backgroundTask.Id}.json", "background-tasks");
        }

        private async Task HandleExportAnalyticToCsv(
            BackgroundTask backgroundTask,
            ExportAnalyticListToCsvBackgroundTaskPayload taskPayload)
        {
            backgroundTask.StartedAt = DateTime.UtcNow;

            await _signalRService.SignalROnBackgroundTaskStatusChange(
                backgroundTask.UserId,
                backgroundTask,
                BackgroundTaskStatus.Started);

            var csv = await _analyticsService.ExportDataForCompany(
                taskPayload.CompanyId,
                backgroundTask.StaffId,
                taskPayload.StaffId,
                taskPayload.MessageFrom,
                taskPayload.MessageTo,
                taskPayload.Status,
                taskPayload.Channels,
                taskPayload.ChannelIds,
                taskPayload.Tags,
                taskPayload.TeamId,
                taskPayload.IsTeamUnassigned,
                taskPayload.Conditions,
                async completed =>
                {
                    var progress = BackgroundTaskHelper.CalculateProgress(completed, backgroundTask.Total);
                    backgroundTask.Progress = progress;
                    backgroundTask.UpdatedAt = DateTime.UtcNow;
                    await _appDbContext.SaveChangesAsync();

                    await _signalRService.SignalROnBackgroundTaskStatusChange(
                        backgroundTask.UserId,
                        backgroundTask,
                        BackgroundTaskStatus.Processing);
                });

            var ms = new MemoryStream();
            var sw = new StreamWriter(ms, Encoding.UTF8);
            await sw.WriteAsync(csv);
            await sw.FlushAsync();
            ms.Seek(0, SeekOrigin.Begin);

            var storageConfig = await _appDbContext.ConfigStorageConfigs
                .AsNoTracking()
                .FirstOrDefaultAsync(x => x.CompanyId == backgroundTask.CompanyId);

            var companyName = await _appDbContext.CompanyCompanies
                .Where(x => x.Id == backgroundTask.CompanyId)
                .Select(x => x.CompanyName)
                .FirstAsync();

            var fileName =
                $"{companyName} Report ({taskPayload.MessageFrom.ToString("yyyy-MM-dd")} to {taskPayload.MessageTo.ToString("yyyy-MM-dd")}).csv";
            var filePath = $"ExportAnalytic/{backgroundTask.StaffId}/{fileName}";

            var uploadFileResult = await _uploadService.UploadFileBySteam(
                storageConfig?.ContainerName ?? backgroundTask.CompanyId,
                filePath,
                ms,
                "text/csv");

            var sasUri = _azureBlobStorageService.GetAzureBlobSasUri(
                filePath,
                storageConfig?.ContainerName ?? backgroundTask.CompanyId,
                24);

            var result = new ExportAnalyticListToCsvBackgroundTaskResultPayload()
            {
                FileName = fileName,
                FilePath = filePath,
                MIMEType = "text/csv",
                Url = sasUri,
                FileSize = uploadFileResult.FileSizeInByte,
            };

            backgroundTask.ResultPayload = JsonConvert.SerializeObject(result, _jsonSerializerSettings);
            backgroundTask.UpdatedAt = DateTime.UtcNow;
            backgroundTask.CompletedAt = DateTime.UtcNow;
            backgroundTask.Progress = 100;
            backgroundTask.ErrorMessage = null;
            backgroundTask.IsCompleted = true;

            await _appDbContext.SaveChangesAsync();

            await _signalRService.SignalROnBackgroundTaskStatusChange(
                backgroundTask.UserId,
                backgroundTask,
                BackgroundTaskStatus.Completed);

            // remove the json
            await _azureBlobStorageService.DeleteFromAzureBlob($"{backgroundTask.Id}.json", "background-tasks");
        }

        private async Task HandleImportWhatsAppHistory(
            BackgroundTask backgroundTask,
            ImportWhatsAppHistoryTaskPayLoad taskPayload)
        {
            backgroundTask.StartedAt = DateTime.UtcNow;

            await _signalRService.SignalROnBackgroundTaskStatusChange(
                backgroundTask.UserId,
                backgroundTask,
                BackgroundTaskStatus.Started);

            var total = backgroundTask.Total;
            var completed = 0;

            var phoneNumber = taskPayload.ImportWhatsAppHistoryViewModel.PhoneNumber?.Replace(" ", string.Empty);
            var importedToChannel = taskPayload.ImportWhatsAppHistoryViewModel.ImportedToChannel;
            var chatHistories = taskPayload.ImportWhatsAppHistoryViewModel.ChatHistories;

            foreach (var chatHistory in chatHistories)
            {
                await ImportConversation(backgroundTask, chatHistory, phoneNumber, importedToChannel);

                completed++;
                backgroundTask.Progress = BackgroundTaskHelper.CalculateProgress(completed, total);
                backgroundTask.UpdatedAt = DateTime.UtcNow;
                await _appDbContext.SaveChangesAsync();

                await _signalRService.SignalROnBackgroundTaskStatusChange(
                    backgroundTask.UserId,
                    backgroundTask,
                    BackgroundTaskStatus.Processing);
            }

            backgroundTask.UpdatedAt = DateTime.UtcNow;
            backgroundTask.CompletedAt = DateTime.UtcNow;
            backgroundTask.IsCompleted = true;
            backgroundTask.ErrorMessage = null;
            backgroundTask.Progress = 100;

            await _appDbContext.SaveChangesAsync();

            await _signalRService.SignalROnBackgroundTaskStatusChange(
                backgroundTask.UserId,
                backgroundTask,
                BackgroundTaskStatus.Completed);

            // remove the json
            await _azureBlobStorageService.DeleteFromAzureBlob($"{backgroundTask.Id}.json", "background-tasks");
        }

        public async Task HandleImportGenericHistory(
            BackgroundTask backgroundTask,
            ImportGenericHistoryTaskPayLoad taskPayload)
        {
            backgroundTask.StartedAt = DateTime.UtcNow;

            await _signalRService.SignalROnBackgroundTaskStatusChange(
                backgroundTask.UserId,
                backgroundTask,
                BackgroundTaskStatus.Started);

            var total = backgroundTask.Total;
            var completed = 0;

            var phoneNumber = taskPayload.ImportGenericHistoryViewModel.PhoneNumber?.Replace(" ", string.Empty);
            var importedToChannel = taskPayload.ImportGenericHistoryViewModel.ImportedToChannel;

            foreach (var chatHistory in taskPayload.ImportGenericHistoryViewModel.ChatHistories)
            {
                await ImportConversation(backgroundTask, chatHistory, phoneNumber, importedToChannel);

                completed++;
                backgroundTask.Progress = BackgroundTaskHelper.CalculateProgress(completed, total);
                backgroundTask.UpdatedAt = DateTime.UtcNow;
                await _appDbContext.SaveChangesAsync();

                await _signalRService.SignalROnBackgroundTaskStatusChange(
                    backgroundTask.UserId,
                    backgroundTask,
                    BackgroundTaskStatus.Processing);
            }

            backgroundTask.UpdatedAt = DateTime.UtcNow;
            backgroundTask.CompletedAt = DateTime.UtcNow;
            backgroundTask.IsCompleted = true;
            backgroundTask.ErrorMessage = null;
            backgroundTask.Progress = 100;

            await _appDbContext.SaveChangesAsync();

            await _signalRService.SignalROnBackgroundTaskStatusChange(
                backgroundTask.UserId,
                backgroundTask,
                BackgroundTaskStatus.Completed);

            // remove the json
            await _azureBlobStorageService.DeleteFromAzureBlob($"{backgroundTask.Id}.json", "background-tasks");
        }

        private async Task ImportConversation(
            BackgroundTask backgroundTask,
            ChatHistory chatHistory,
            string phoneNumber,
            TargetedChannelModel importedToChannel)
        {
            const int MaximumPhoneNoLength = 15;

            var chatRoomId = chatHistory.ChatRoomId;
            var chatRoomName = chatHistory.ChatRoomName;

            var channelIdentityId =
                (await _channelIdentityIdRepository.GetChannelIdentityIdsByChannelIds(
                    importedToChannel.channel,
                    importedToChannel.ids)).FirstOrDefault();

            var isGroupChat = chatRoomId.Contains("-") || chatRoomId.Length > MaximumPhoneNoLength;

            if (isGroupChat)
            {
                return;
            }

            foreach (var messageHistory in chatHistory.MessageHistories)
            {
                var messageUniqueId = SHA256Helper.sha256_hash(
                    $"{backgroundTask.CompanyId}{messageHistory.FromUser}{messageHistory.ToUser}{messageHistory.Time}{messageHistory.Text}");

                var messageDatetime = DateTimeOffset.FromUnixTimeMilliseconds(messageHistory.Time).UtcDateTime;
                var isFromMe = messageHistory.FromUser == phoneNumber;
                var senderId = messageHistory.FromUser;
                var receiverId = messageHistory.ToUser;

                var conversationMessage = new ConversationMessage()
                {
                    MessageContent = messageHistory.Text,
                    Channel = importedToChannel.channel,
                    MessageType = "text",
                    MessageUniqueID = messageUniqueId,
                    CreatedAt = messageDatetime,
                    UpdatedAt = messageDatetime,
                    IsSentFromSleekflow = isFromMe,
                    Status = MessageStatus.Sent,
                    IsFromImport = true,
                    ChannelIdentityId = channelIdentityId
                };

                var company = await _appDbContext.CompanyCompanies
                    .FirstOrDefaultAsync(x => x.Id == backgroundTask.CompanyId);

                var conversation = new Conversation
                {
                    MessageGroupName = company.SignalRGroupName,
                    ActiveStatus = ActiveStatus.Active,
                    UpdatedTime = conversationMessage.UpdatedAt,
                    Status = "closed"
                };

                switch (importedToChannel.channel)
                {
                    case "twilio_whatsapp":
                    case ChannelTypes.WhatsappTwilio:
                        var twilioInstance = importedToChannel.ids
                            .FirstOrDefault()?
                            .Split(";", StringSplitOptions.RemoveEmptyEntries);

                        if (twilioInstance?.Count() != 2)
                        {
                            return;
                        }

                        var instanceId = twilioInstance[0];
                        var instanceSender = twilioInstance[1].Replace(": ", ":+");

                        // Format twilio whatsapp Id
                        senderId = $"whatsapp:+{senderId}";
                        receiverId = $"whatsapp:+{receiverId}";

                        var whatsappSender = await _appDbContext.SenderWhatsappSenders
                            .FirstOrDefaultAsync(
                                x =>
                                    x.CompanyId == backgroundTask.CompanyId
                                    && x.whatsAppId == senderId
                                    && x.InstanceId == instanceId
                                    && x.InstaneSender == instanceSender);

                        if (whatsappSender == null)
                        {
                            whatsappSender = new WhatsAppSender
                            {
                                whatsAppId = senderId,
                                CompanyId = backgroundTask.CompanyId,
                                isVerified = true,
                                InstanceId = instanceId,
                                InstaneSender = instanceSender
                            };

                            whatsappSender.phone_number = messageHistory.FromUser;
                            whatsappSender.name = whatsappSender.phone_number;

                            if (!isFromMe)
                            {
                                whatsappSender.name = chatRoomName;
                            }

                            await _appDbContext.SenderWhatsappSenders.AddAsync(whatsappSender);
                            await _appDbContext.SaveChangesAsync();
                        }

                        var whatsappReceiver = await _appDbContext.SenderWhatsappSenders
                            .FirstOrDefaultAsync(
                                x =>
                                    x.CompanyId == backgroundTask.CompanyId
                                    && x.whatsAppId == receiverId
                                    && x.InstanceId == instanceId
                                    && x.InstaneSender == instanceSender);

                        if (whatsappReceiver == null)
                        {
                            whatsappReceiver = new WhatsAppSender
                            {
                                whatsAppId = receiverId,
                                CompanyId = backgroundTask.CompanyId,
                                isVerified = true,
                                InstanceId = instanceId,
                                InstaneSender = instanceSender
                            };

                            whatsappReceiver.phone_number = messageHistory.ToUser;
                            whatsappReceiver.name = whatsappReceiver.phone_number;

                            if (isFromMe)
                            {
                                whatsappReceiver.name = chatRoomName;
                            }

                            await _appDbContext.SenderWhatsappSenders.AddAsync(whatsappReceiver);
                            await _appDbContext.SaveChangesAsync();
                        }

                        conversationMessage.whatsappSender = whatsappSender;
                        conversationMessage.whatsappReceiver = whatsappReceiver;

                        conversation.WhatsappUser = isFromMe ? whatsappReceiver : whatsappSender;
                        conversationMessage.IsSentFromSleekflow = isFromMe;

                        break;

                    case ChannelTypes.Whatsapp360Dialog:
                        var whatsapp360DialogChannelId = Int64.Parse(importedToChannel.ids.FirstOrDefault());

                        var whatsapp360DialogSender = await _appDbContext.SenderWhatsApp360DialogSenders
                            .FirstOrDefaultAsync(
                                x =>
                                    x.CompanyId == backgroundTask.CompanyId
                                    && x.WhatsAppId == senderId
                                    && x.ChannelId == whatsapp360DialogChannelId);

                        if (whatsapp360DialogSender == null)
                        {
                            whatsapp360DialogSender = new WhatsApp360DialogSender()
                            {
                                WhatsAppId = senderId,
                                CompanyId = backgroundTask.CompanyId,
                                ChannelId = whatsapp360DialogChannelId
                            };

                            whatsapp360DialogSender.PhoneNumber = $"+{messageHistory.FromUser}";

                            if (!isFromMe
                                && !chatRoomName.Replace(" ", string.Empty).Contains(senderId))
                            {
                                whatsapp360DialogSender.Name = chatRoomName;
                            }

                            await _appDbContext.SenderWhatsApp360DialogSenders.AddAsync(whatsapp360DialogSender);
                            await _appDbContext.SaveChangesAsync();
                        }

                        var whatsapp360DialogReceiver = await _appDbContext.SenderWhatsApp360DialogSenders
                            .FirstOrDefaultAsync(
                                x =>
                                    x.CompanyId == backgroundTask.CompanyId
                                    && x.WhatsAppId == receiverId
                                    && x.ChannelId == whatsapp360DialogChannelId);

                        if (whatsapp360DialogReceiver == null)
                        {
                            whatsapp360DialogReceiver = new WhatsApp360DialogSender
                            {
                                WhatsAppId = receiverId,
                                CompanyId = backgroundTask.CompanyId,
                                ChannelId = whatsapp360DialogChannelId
                            };

                            whatsapp360DialogReceiver.PhoneNumber = $"+{messageHistory.ToUser}";

                            if (isFromMe
                                && !chatRoomName.Replace(" ", string.Empty).Contains(receiverId))
                            {
                                whatsapp360DialogReceiver.Name = chatRoomName;
                            }

                            await _appDbContext.SenderWhatsApp360DialogSenders.AddAsync(whatsapp360DialogReceiver);
                            await _appDbContext.SaveChangesAsync();
                        }

                        conversationMessage.Whatsapp360DialogSender = whatsapp360DialogSender;
                        conversationMessage.Whatsapp360DialogReceiver = whatsapp360DialogReceiver;

                        conversation.WhatsApp360DialogUser =
                            isFromMe ? whatsapp360DialogReceiver : whatsapp360DialogSender;
                        conversationMessage.IsSentFromSleekflow = isFromMe;

                        break;

                    case ChannelTypes.WhatsappCloudApi:
                        var whatsappCloudApiChannelId = importedToChannel.ids.FirstOrDefault();

                        receiverId = GetValidPhoneNumber(receiverId);
                        senderId = GetValidPhoneNumber(senderId);

                        var whatsappCloudApiSender = await _appDbContext.WhatsappCloudApiSenders
                            .FirstOrDefaultAsync(
                                x =>
                                    x.CompanyId == backgroundTask.CompanyId
                                    && x.WhatsappId == senderId);

                        if (whatsappCloudApiSender == null)
                        {
                            whatsappCloudApiSender = new WhatsappCloudApiSender
                            {
                                WhatsappId = senderId,
                                CompanyId = backgroundTask.CompanyId,
                                WhatsappChannelPhoneNumber = whatsappCloudApiChannelId
                            };

                            if (!isFromMe
                                && !chatRoomName.Replace(" ", string.Empty).Contains(senderId))
                            {
                                whatsappCloudApiSender.WhatsappUserDisplayName = chatRoomName;
                            }
                        }

                        var whatsappCloudApiReceiver = await _appDbContext.WhatsappCloudApiSenders
                            .FirstOrDefaultAsync(
                                x =>
                                    x.CompanyId == backgroundTask.CompanyId
                                    && x.WhatsappId == receiverId);

                        if (whatsappCloudApiReceiver == null)
                        {
                            whatsappCloudApiReceiver = new WhatsappCloudApiSender
                            {
                                WhatsappId = receiverId,
                                CompanyId = backgroundTask.CompanyId,
                                WhatsappChannelPhoneNumber = whatsappCloudApiChannelId
                            };

                            if (isFromMe
                                && !chatRoomName.Replace(" ", string.Empty).Contains(receiverId))
                            {
                                whatsappCloudApiReceiver.WhatsappUserDisplayName = chatRoomName;
                            }
                        }

                        conversationMessage.WhatsappCloudApiSender = whatsappCloudApiSender;
                        conversationMessage.WhatsappCloudApiReceiver = whatsappCloudApiReceiver;

                        conversation.WhatsappCloudApiUser =
                            isFromMe ? whatsappCloudApiReceiver : whatsappCloudApiSender;
                        conversationMessage.IsSentFromSleekflow = isFromMe;

                        break;
                }

                await _conversationMessageService.SendMessage(
                    conversation,
                    conversationMessage,
                    false);
            }
        }

        private static string GetValidPhoneNumber(string phoneNumber)
        {
            return phoneNumber.StartsWith("55") && phoneNumber.Length == 12
                ? PhoneNumberHelper.GetValidPhoneNumberWithCountryCodeForImportContact(
                    phoneNumber,
                    "Brazil")
                : phoneNumber;
        }

        private async Task HandleConvertCampaignLeadsToContactList(
            BackgroundTask backgroundTask,
            ConvertCampaignLeadsToContactListTaskPayLoad convertCampaignLeadsToContactListTaskPayLoad)
        {
            backgroundTask.StartedAt = DateTime.UtcNow;

            await _signalRService.SignalROnBackgroundTaskStatusChange(
                backgroundTask.UserId,
                backgroundTask,
                BackgroundTaskStatus.Started);

            var progressMilestoneStep = 20;

            var newImport = await _appDbContext.CompanyImportContactHistories
                .Include(x => x.ImportedUserProfiles)
                .Where(
                    x =>
                        x.CompanyId == convertCampaignLeadsToContactListTaskPayLoad.CompanyId
                        && x.Id == convertCampaignLeadsToContactListTaskPayLoad.ListId)
                .FirstOrDefaultAsync();

            string continuationToken = null;
            var count = 0;

            while (true)
            {
                var getObjectsV3InputFilterGroups = new List<GetObjectsV3InputFilterGroup>
                {
                    new GetObjectsV3InputFilterGroup(
                        new List<GetObjectsV3InputFilter>()
                        {
                            new GetObjectsV3InputFilter(
                                "unified:SalesforceIntegratorId",
                                "query",
                                new
                                {
                                    EntityTypeName = "CampaignMember",
                                    Select = new
                                    {
                                        FieldName = "unified:SalesforceIntegratorLeadId"
                                    },
                                    FilterGroups = new List<GetObjectsV3InputFilterGroup>
                                    {
                                        new GetObjectsV3InputFilterGroup(
                                            new List<GetObjectsV3InputFilter>()
                                            {
                                                new GetObjectsV3InputFilter(
                                                    "unified:SalesforceIntegratorCampaignId",
                                                    "=",
                                                    convertCampaignLeadsToContactListTaskPayLoad.CampaignId)
                                            })
                                    }
                                })
                        })
                };

                var getObjectsOutputOutput = await _objectsApi.ObjectsGetObjectsV3PostAsync(
                    getObjectsV3Input: new GetObjectsV3Input(
                        continuationToken,
                        convertCampaignLeadsToContactListTaskPayLoad.CompanyId,
                        "Lead",
                        100,
                        getObjectsV3InputFilterGroups,
                        new List<Sort>(),
                        new List<GetObjectsV3InputExpand>()));

                continuationToken = getObjectsOutputOutput.Data.ContinuationToken;

                foreach (var crmHubObject in getObjectsOutputOutput.Data.Records)
                {
                    var crmHubObjectId = (string) crmHubObject["id"];

                    try
                    {
                        var userProfiles = await _crmHubService.AddOnlyNewUserProfilesByContactObjectDictAsync(
                            convertCampaignLeadsToContactListTaskPayLoad.CompanyId,
                            crmHubObject,
                            crmHubObjectId);

                        foreach (var userprofile in userProfiles)
                        {
                            newImport.ImportedUserProfiles.Add(
                                new ImportedUserProfile
                                {
                                    UserProfileId = userprofile.Id
                                });
                        }

                        await _appDbContext.SaveChangesAsync();
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(
                            ex,
                            "Company {CompanyId} unable to convert CrmHub object to contact in background task {BackgroundTaskId}: {ExceptionMessage}",
                            backgroundTask.CompanyId,
                            backgroundTask.Id,
                            ex.Message);
                    }
                }

                backgroundTask.Progress += 1;
                backgroundTask.Progress = Math.Min(backgroundTask.Progress, 99);
                backgroundTask.Total += getObjectsOutputOutput.Data.Records.Count;

                if (backgroundTask.Total % progressMilestoneStep == 0)
                {
                    backgroundTask.UpdatedAt = DateTime.UtcNow;

                    await _signalRService.SignalROnBackgroundTaskStatusChange(
                        backgroundTask.UserId,
                        backgroundTask,
                        BackgroundTaskStatus.Processing);
                }

                await _appDbContext.SaveChangesAsync();

                if (string.IsNullOrEmpty(continuationToken))
                {
                    break;
                }
            }

            backgroundTask.Progress = 100;
            backgroundTask.UpdatedAt = DateTime.UtcNow;
            backgroundTask.CompletedAt = DateTime.UtcNow;
            backgroundTask.IsCompleted = true;

            await _appDbContext.SaveChangesAsync();

            await _signalRService.SignalROnBackgroundTaskStatusChange(
                backgroundTask.UserId,
                backgroundTask,
                BackgroundTaskStatus.Completed);
            await _azureBlobStorageService.DeleteFromAzureBlob($"{backgroundTask.Id}.json", "background-tasks");
        }

        private async Task HandleLoopThroughAndEnrollContactsToFlowHub(
            BackgroundTask backgroundTask)
        {
            backgroundTask.StartedAt = DateTime.UtcNow;

            await _signalRService.SignalROnBackgroundTaskStatusChange(
                backgroundTask.UserId,
                backgroundTask,
                BackgroundTaskStatus.Started);

            var userProfileIds = await _appDbContext.UserProfiles
                .Where(u => u.CompanyId == backgroundTask.CompanyId && u.ActiveStatus == ActiveStatus.Active)
                .AsNoTracking()
                .Select(u => u.Id)
                .ToListAsync();

            var numberOfEnrolledContacts = 0;
            var numberOfContacts = userProfileIds.Count;

            backgroundTask.Total = userProfileIds.Count;

            var targetPayload = JsonConvert.DeserializeObject<LoopThroughAndEnrollContactsToFlowHubTaskTargetPayload>(
                backgroundTask.TargetPayload);

            var userProfileIdsChunks = userProfileIds.Chunk(100).ToList();
            var totalChunks = userProfileIdsChunks.Count;

            for (var i = 0; i < totalChunks; i++)
            {
                var userProfileIdsChunk = userProfileIdsChunks[i].ToList();

                var contacts = new List<Dictionary<string, object?>>();
                foreach (var userProfileId in userProfileIdsChunk)
                {
                    var userProfileDict = (await _flowHubService.GetUserProfileDictAsync(
                        backgroundTask.CompanyId,
                        userProfileId))
                        .ToDictionary(pair => pair.Key, pair => (object) pair.Value);
                    userProfileDict.Add("Id", userProfileId);

                    contacts.Add(userProfileDict);
                }

                await _inflowActionsApi.InflowActionsEnrollContactsToFlowHubPostAsync(
                    enrollContactsToFlowHubInput: new EnrollContactsToFlowHubInput(
                        backgroundTask.CompanyId,
                        contacts,
                        targetPayload.FlowHubWorkflowId,
                        targetPayload.FlowHubWorkflowVersionedId));

                if (i < totalChunks - 1)
                {
                    await Task.Delay(TimeSpan.FromSeconds(1));
                }

                numberOfEnrolledContacts += userProfileIdsChunk.Count;

                backgroundTask.Progress = BackgroundTaskHelper.CalculateProgress(
                    numberOfEnrolledContacts,
                    numberOfContacts);
                backgroundTask.UpdatedAt = DateTime.UtcNow;

                await _appDbContext.SaveChangesAsync();

                await _signalRService.SignalROnBackgroundTaskStatusChange(
                    backgroundTask.UserId,
                    backgroundTask,
                    BackgroundTaskStatus.Processing);
            }

            backgroundTask.UpdatedAt = DateTime.UtcNow;
            backgroundTask.CompletedAt = DateTime.UtcNow;
            backgroundTask.IsCompleted = true;
            backgroundTask.Progress = 100;

            await _appDbContext.SaveChangesAsync();

            await _signalRService.SignalROnBackgroundTaskStatusChange(
                backgroundTask.UserId,
                backgroundTask,
                BackgroundTaskStatus.Completed);

            await _azureBlobStorageService.DeleteFromAzureBlob($"{backgroundTask.Id}.json", "background-tasks");
        }

        private async Task HandleExportFlowHubWorkflowExecutionUsagesToCsv(
            BackgroundTask backgroundTask,
            ExportFlowHubWorkflowExecutionUsagesToCsvTaskTargetPayload taskPayload)
        {
            backgroundTask.StartedAt = DateTime.UtcNow;
            await _appDbContext.SaveChangesAsync();

            await _signalRService.SignalROnBackgroundTaskStatusChange(
                backgroundTask.UserId,
                backgroundTask,
                BackgroundTaskStatus.Started);

            var workflowExecutionUsages = new List<WorkflowExecutionUsageListDto>();
            string continuationToken = null;

            while (true)
            {
                var workflowExecutionUsagesOutput =
                    await _workflowExecutionsApi.ExecutionsGetWorkflowExecutionUsagesPostAsync(
                        getWorkflowExecutionUsagesInput: new GetWorkflowExecutionUsagesInput(
                            backgroundTask.CompanyId,
                            taskPayload.Filters,
                            15,
                            continuationToken));

                workflowExecutionUsages.AddRange(workflowExecutionUsagesOutput.Data.WorkflowExecutionUsages);
                continuationToken = workflowExecutionUsagesOutput.Data.ContinuationToken;

                if (backgroundTask.Progress == 0)
                {
                    backgroundTask.Progress = 25;

                    await _signalRService.SignalROnBackgroundTaskStatusChange(
                        backgroundTask.UserId,
                        backgroundTask,
                        BackgroundTaskStatus.Processing);
                }

                backgroundTask.UpdatedAt = DateTime.UtcNow;
                await _appDbContext.SaveChangesAsync();

                if (string.IsNullOrEmpty(continuationToken))
                {
                    break;
                }
            }

            backgroundTask.Progress = 50;
            await _appDbContext.SaveChangesAsync();

            await _signalRService.SignalROnBackgroundTaskStatusChange(
                backgroundTask.UserId,
                backgroundTask,
                BackgroundTaskStatus.Processing);

            var sb = new StringBuilder();
            sb.AppendLine("Date range");
            sb.AppendLine(
                $"\"{taskPayload.Filters.ExecutionFromDateTime:dd MMMM yyyy} - {taskPayload.Filters.ExecutionToDateTime:dd MMMM yyyy}\"");
            sb.AppendLine("Flow ID,Flow name,Status,Total enrollments,Last enrollment date");

            foreach (var usage in workflowExecutionUsages)
            {
                sb.AppendLine(
                    $"\"{usage.Workflow.WorkflowId}\",\"{usage.Workflow.Name}\",\"{usage.Workflow.ActivationStatus}\",\"{usage.TotalExecutionCount - usage.FailedExecutionCount}\",\"{usage.LastEnrolledAt:o}\"");
            }

            await using var ms = new MemoryStream();
            await using var sw = new StreamWriter(ms, Encoding.UTF8);
            await sw.WriteAsync(sb.ToString());
            await sw.FlushAsync();
            ms.Seek(0, SeekOrigin.Begin);

            var storageConfig = await _appDbContext.ConfigStorageConfigs
                .AsNoTracking()
                .FirstOrDefaultAsync(x => x.CompanyId == backgroundTask.CompanyId);

            var fileName =
                $"flow_usage_{taskPayload.Filters.ExecutionFromDateTime:yyyyMMdd}-{taskPayload.Filters.ExecutionToDateTime:yyyyMMdd}.csv";
            var filePath = $"WorkflowUsages/{backgroundTask.StaffId}/{fileName}";

            var uploadFileResult = await _uploadService.UploadFileBySteam(
                storageConfig?.ContainerName ?? backgroundTask.CompanyId,
                filePath,
                ms,
                "text/csv");

            var sasUri = _azureBlobStorageService.GetAzureBlobSasUri(
                filePath,
                storageConfig?.ContainerName ?? backgroundTask.CompanyId,
                24);

            var result = new ExportFlowHubWorkflowExecutionUsagesTaskResultPayload
            {
                FileName = fileName,
                FilePath = filePath,
                MIMEType = "text/csv",
                Url = sasUri,
                FileSize = uploadFileResult.FileSizeInByte,
            };

            backgroundTask.ResultPayload = JsonConvert.SerializeObject(result, _jsonSerializerSettings);
            backgroundTask.UpdatedAt = DateTime.UtcNow;
            backgroundTask.CompletedAt = DateTime.UtcNow;
            backgroundTask.Progress = 100;
            backgroundTask.ErrorMessage = null;
            backgroundTask.IsCompleted = true;

            await _appDbContext.SaveChangesAsync();

            await _signalRService.SignalROnBackgroundTaskStatusChange(
                backgroundTask.UserId,
                backgroundTask,
                BackgroundTaskStatus.Completed);

            await _azureBlobStorageService.DeleteFromAzureBlob($"{backgroundTask.Id}.json", "background-tasks");
        }

        #endregion

        public async Task<BackgroundTask> EnqueueAddContactsToListTask(
            string userId,
            long staffId,
            string companyId,
            long listId,
            UserProfileIdsViewModel userprofileViewModel)
        {
            var targetPayload = await _appDbContext.CompanyImportContactHistories
                .Where(
                    x =>
                        x.Id == listId
                        && x.CompanyId == companyId)
                .Select(
                    x => new ContactListBackgroundTaskTargetPayload
                    {
                        ListId = x.Id, ImportName = x.ImportName,
                    })
                .FirstAsync();

            if (userprofileViewModel.ListIds is { Count: > 0 })
            {
                var userProfileIdFromList = await _appDbContext.CompanyImportedUserProfiles
                    .AsNoTracking()
                    .Where(x => userprofileViewModel.ListIds.Contains(x.ImportContactHistoryId))
                    .Select(x => x.UserProfileId)
                    .Distinct()
                    .ToListAsync();

                if (userprofileViewModel.UserProfileIds != null)
                {
                    userprofileViewModel.UserProfileIds.AddRange(userProfileIdFromList);
                    userprofileViewModel.UserProfileIds = userprofileViewModel.UserProfileIds.Distinct().ToList();
                }
                else
                {
                    userprofileViewModel.UserProfileIds = userProfileIdFromList;
                }

                userprofileViewModel.ListIds = null;
            }

            var backgroundTask = new BackgroundTask
            {
                CompanyId = companyId,
                StaffId = staffId,
                UserId = userId,
                Total = userprofileViewModel.UserProfileIds.Count,
                TargetPayload = JsonConvert.SerializeObject(targetPayload, _jsonSerializerSettings),
                TaskType = BackgroundTaskType.AddContactsToList
            };

            _appDbContext.BackgroundTasks.Add(backgroundTask);
            await _appDbContext.SaveChangesAsync();

            var taskPayload = JsonConvert.SerializeObject(
                new AddContactsToListBackgroundTaskPayload()
                {
                    CompanyId = companyId, ListId = listId, UserprofileViewModel = userprofileViewModel,
                },
                _jsonSerializerSettings);

            var byteArray = Encoding.UTF8.GetBytes(taskPayload);
            var stream = new MemoryStream(byteArray);

            await _azureBlobStorageService.UploadFileAsBlob(stream, $"{backgroundTask.Id}.json", "background-tasks");

            BackgroundJob.Enqueue<IBackgroundTaskService>(x => x.Handle(backgroundTask.Id));

            return backgroundTask;
        }

        public async Task<BackgroundTask> EnqueueImportContactsTask(
            string userId,
            long staffId,
            string companyId,
            long listId,
            bool isImportIntoList,
            bool isTriggerAutomation,
            ImportSpreadsheet importSpreadsheet,
            long? importContactToListRecordId = null)
        {
            var targetPayload = await _appDbContext.CompanyImportContactHistories
                .Where(
                    x =>
                        x.Id == listId
                        && x.CompanyId == companyId)
                .Select(
                    x => new ContactListBackgroundTaskTargetPayload
                    {
                        ListId = x.Id, ImportName = x.ImportName, TargetType = BackgroundTaskTargetType.List
                    })
                .FirstAsync();

            var backgroundTask = new BackgroundTask
            {
                CompanyId = companyId,
                StaffId = staffId,
                UserId = userId,
                Total = importSpreadsheet.records.Count,
                TargetPayload = JsonConvert.SerializeObject(targetPayload, _jsonSerializerSettings),
                TaskType = BackgroundTaskType.ImportContacts
            };

            // Reset the index to 0 once
            if (isImportIntoList)
            {
                await _appDbContext.CompanyImportContactHistories.Where(x => x.Id == listId)
                    .ExecuteUpdateAsync(
                        calls => calls.SetProperty(
                            p => p.ImportIndex,
                            0));
            }

            _appDbContext.BackgroundTasks.Add(backgroundTask);
            await _appDbContext.SaveChangesAsync();

            var taskPayload = JsonConvert.SerializeObject(
                new ImportContactsBackgroundTaskPayload()
                {
                    ListId = listId,
                    CompanyId = companyId,
                    ImportSpreadsheet = importSpreadsheet,
                    IsImportIntoList = isImportIntoList,
                    IsTriggerAutomation = isTriggerAutomation,
                    ImportContactToListRecordId = importContactToListRecordId
                },
                _jsonSerializerSettings);

            var byteArray = Encoding.UTF8.GetBytes(taskPayload);
            var stream = new MemoryStream(byteArray);

            await _azureBlobStorageService.UploadFileAsBlob(stream, $"{backgroundTask.Id}.json", "background-tasks");

            BackgroundJob.Enqueue<IBackgroundTaskService>(x => x.Handle(backgroundTask.Id));

            return backgroundTask;
        }

        public async Task<BackgroundTask> EnqueueImportContactsTaskV2(
            string userId,
            long staffId,
            string companyId,
            long listId,
            bool isImportIntoList,
            bool isTriggerAutomation,
            ImportSpreadsheet importSpreadsheet,
            long? importContactToListRecordId = null)
        {
            var targetPayload = await _appDbContext.CompanyImportContactHistories
                .Where(
                    x =>
                        x.Id == listId
                        && x.CompanyId == companyId)
                .Select(
                    x => new ContactListBackgroundTaskTargetPayload
                    {
                        ListId = x.Id, ImportName = x.ImportName, TargetType = BackgroundTaskTargetType.List
                    })
                .FirstAsync();

            var backgroundTask = new BackgroundTask
            {
                CompanyId = companyId,
                StaffId = staffId,
                UserId = userId,
                Total = importSpreadsheet.records.Count,
                TargetPayload = JsonConvert.SerializeObject(targetPayload, _jsonSerializerSettings),
                TaskType = BackgroundTaskType.ImportContacts
            };

            // Reset the index to 0 once
            if (isImportIntoList)
            {
                await _appDbContext.CompanyImportContactHistories.Where(x => x.Id == listId)
                    .ExecuteUpdateAsync(
                        calls => calls.SetProperty(
                            p => p.ImportIndex,
                            0));
            }

            _appDbContext.BackgroundTasks.Add(backgroundTask);
            await _appDbContext.SaveChangesAsync();

            var taskPayload = JsonConvert.SerializeObject(
                new ImportContactsBackgroundTaskPayload()
                {
                    ListId = listId,
                    CompanyId = companyId,
                    ImportSpreadsheet = importSpreadsheet,
                    IsImportIntoList = isImportIntoList,
                    IsTriggerAutomation = isTriggerAutomation,
                    ImportContactToListRecordId = importContactToListRecordId
                },
                _jsonSerializerSettings);

            var byteArray = Encoding.UTF8.GetBytes(taskPayload);
            var stream = new MemoryStream(byteArray);

            await _azureBlobStorageService.UploadFileAsBlob(stream, $"{backgroundTask.Id}.json", "background-tasks");

            BackgroundJob.Enqueue<IBackgroundTaskService>(x => x.Handle(backgroundTask.Id));

            return backgroundTask;
        }

        /// <summary>
        /// Create a background task for bulk importing contacts.
        /// </summary>
        /// <remarks>
        /// Does not enqueue the task. Enqueue with <see cref="EnqueueBulkImportContactsTask"/>.
        /// </remarks>
        /// <param name="userId">User ID.</param>
        /// <param name="staffId">Staff ID.</param>
        /// <param name="companyId">Company ID.</param>
        /// <param name="listId">List ID.</param>
        /// <param name="isImportIntoList">Whether the contacts are being imported into a list.</param>
        /// <param name="isTriggerAutomation">Whether to trigger the automation afterwards.</param>
        /// <param name="importSpreadsheet"><see cref="ImportSpreadsheet"/>.</param>
        /// <param name="importContactToListRecordId">Record ID.</param>
        /// <returns><see cref="BackgroundTask"/>.</returns>
        public async Task<BackgroundTask> CreateBulkImportContactsTask(
            string userId,
            long staffId,
            string companyId,
            long listId,
            bool isImportIntoList,
            bool isTriggerAutomation,
            ImportSpreadsheet importSpreadsheet,
            long? importContactToListRecordId = null)
        {
            var targetPayload = await _appDbContext.CompanyImportContactHistories
                .Where(
                    x =>
                        x.Id == listId
                        && x.CompanyId == companyId)
                .Select(
                    x => new ContactListBackgroundTaskTargetPayload
                    {
                        ListId = x.Id, ImportName = x.ImportName, TargetType = BackgroundTaskTargetType.List
                    })
                .FirstAsync();

            // If the import name is null or empty, contacts will not be imported into a list.
            if (string.IsNullOrEmpty(targetPayload.ImportName))
            {
                targetPayload.TargetType = BackgroundTaskTargetType.IndividualContacts;
            }

            var backgroundTask = new BackgroundTask
            {
                CompanyId = companyId,
                StaffId = staffId,
                UserId = userId,
                Total = importSpreadsheet.records.Count,
                TargetPayload = JsonConvert.SerializeObject(targetPayload, _jsonSerializerSettings),
                TaskType = BackgroundTaskType.BulkImportContacts
            };

            // Reset the index to 0 once
            if (isImportIntoList)
            {
                await _appDbContext.CompanyImportContactHistories.Where(x => x.Id == listId)
                    .ExecuteUpdateAsync(
                        calls => calls.SetProperty(
                            p => p.ImportIndex,
                            0));
            }

            _appDbContext.BackgroundTasks.Add(backgroundTask);
            await _appDbContext.SaveChangesAsync();

            var taskPayload = JsonConvert.SerializeObject(
                new ImportContactsBackgroundTaskPayload()
                {
                    ListId = listId,
                    CompanyId = companyId,
                    ImportSpreadsheet = importSpreadsheet,
                    IsImportIntoList = isImportIntoList,
                    IsTriggerAutomation = isTriggerAutomation,
                    ImportContactToListRecordId = importContactToListRecordId
                },
                _jsonSerializerSettings);

            var byteArray = Encoding.UTF8.GetBytes(taskPayload);
            var stream = new MemoryStream(byteArray);

            await _azureBlobStorageService.UploadFileAsBlob(stream, $"{backgroundTask.Id}.json", "background-tasks");

            // There is delay in starting the background task for express import;
            // therefore, we will publish a “Queued” status for the UI to handle.
            await _signalRService.SignalROnBackgroundTaskStatusChange(
                backgroundTask.UserId,
                backgroundTask,
                BackgroundTaskStatus.Queued);

            return backgroundTask;
        }

        /// <summary>
        /// Enqueue the background task for bulk importing contacts.
        /// </summary>
        /// <param name="companyId">Company ID.</param>
        /// <param name="taskId">Background task ID.</param>
        /// <returns><see cref="Task"/>.</returns>
        public async Task EnqueueBulkImportContactsTask(string companyId, long taskId)
        {
            var backgroundTask = await _appDbContext.BackgroundTasks
                .AsNoTracking()
                .Where(
                    x =>
                        x.CompanyId == companyId
                        && x.Id == taskId)
                .FirstOrDefaultAsync();

            if (backgroundTask is null)
            {
                return;
            }

            BackgroundJob.Enqueue<IBackgroundTaskService>(x => x.Handle(backgroundTask.Id));
        }

        public async Task<BackgroundTask> EnqueueExportContactsListToCsvTask(
            string userId,
            string companyId,
            long staffId,
            UserProfileIdsViewModel userprofileViewModel)
        {
            var targetPayload = await _appDbContext.CompanyImportContactHistories
                .Where(
                    x =>
                        x.Id == userprofileViewModel.ListIds.First()
                        && x.CompanyId == companyId)
                .Select(
                    x => new ContactListBackgroundTaskTargetPayload
                    {
                        ListId = x.Id, ImportName = x.ImportName,
                    })
                .FirstAsync();

            if (userprofileViewModel.ListIds is { Count: > 0 })
            {
                var userProfileIdFromList = await _appDbContext.CompanyImportedUserProfiles
                    .AsNoTracking()
                    .Where(x => userprofileViewModel.ListIds.Contains(x.ImportContactHistoryId))
                    .Select(x => x.UserProfileId)
                    .Distinct()
                    .ToListAsync();

                if (userprofileViewModel.UserProfileIds != null)
                {
                    userprofileViewModel.UserProfileIds.AddRange(userProfileIdFromList);
                    userprofileViewModel.UserProfileIds = userprofileViewModel.UserProfileIds.Distinct().ToList();
                }
                else
                {
                    userprofileViewModel.UserProfileIds = userProfileIdFromList;
                }

                userprofileViewModel.ListIds = null;
            }

            var backgroundTask = new BackgroundTask
            {
                CompanyId = companyId,
                StaffId = staffId,
                UserId = userId,
                Total = userprofileViewModel.UserProfileIds.Count,
                TargetPayload = JsonConvert.SerializeObject(targetPayload, _jsonSerializerSettings),
                TaskType = BackgroundTaskType.ExportContactsListToCsv
            };

            _appDbContext.BackgroundTasks.Add(backgroundTask);
            await _appDbContext.SaveChangesAsync();

            var taskPayload = JsonConvert.SerializeObject(
                new ExportContactsListToCsvBackgroundTaskPayload()
                {
                    CompanyId = companyId, StaffId = staffId, UserprofileViewModel = userprofileViewModel,
                },
                _jsonSerializerSettings);

            var byteArray = Encoding.UTF8.GetBytes(taskPayload);
            var stream = new MemoryStream(byteArray);

            await _azureBlobStorageService.UploadFileAsBlob(stream, $"{backgroundTask.Id}.json", "background-tasks");

            BackgroundJob.Enqueue<IBackgroundTaskService>(
                x => x.Handle(backgroundTask.Id));

            return backgroundTask;
        }

        public async Task<BackgroundTask> EnqueueBulkUpdateCustomFieldsTask(
            string userId,
            string companyId,
            long staffId,
            BulkUpdateCustomFieldsViewModel bulkUpdateCustomFieldsViewModel)
        {
            var backgroundTask = new BackgroundTask
            {
                CompanyId = companyId,
                StaffId = staffId,
                UserId = userId,
                Total = bulkUpdateCustomFieldsViewModel.UserProfileIds.Count,
                TargetPayload = JsonConvert.SerializeObject(
                    new ContactBackgroundTaskTargetPayload(),
                    _jsonSerializerSettings),
                TaskType = BackgroundTaskType.BulkUpdateContactsCustomFields
            };

            _appDbContext.BackgroundTasks.Add(backgroundTask);
            await _appDbContext.SaveChangesAsync();

            var taskPayload = JsonConvert.SerializeObject(
                new BulkUpdateCustomFieldsBackgroundTaskPayload()
                {
                    CompanyId = companyId,
                    StaffId = staffId,
                    BulkUpdateCustomFieldsViewModel = bulkUpdateCustomFieldsViewModel,
                },
                _jsonSerializerSettings);

            var byteArray = Encoding.UTF8.GetBytes(taskPayload);
            var stream = new MemoryStream(byteArray);

            await _azureBlobStorageService.UploadFileAsBlob(stream, $"{backgroundTask.Id}.json", "background-tasks");

            BackgroundJob.Enqueue<IBackgroundTaskService>(
                x => x.Handle(backgroundTask.Id));

            return backgroundTask;
        }

        public async Task<BackgroundTask> EnqueueExportBroadcastStatusToCsvTask(
            string userId,
            string companyId,
            long staffId,
            string broadcastTemplateId,
            int count)
        {
            var targetPayload = await _appDbContext.CompanyMessageTemplates
                .Where(
                    x =>
                        x.Id == broadcastTemplateId
                        && x.CompanyId == companyId)
                .Select(
                    x => new CampaignBackgroundTaskTargetPayload
                    {
                        BroadcastTemplateId = x.Id, TemplateName = x.TemplateName,
                    })
                .FirstAsync();

            var backgroundTask = new BackgroundTask
            {
                CompanyId = companyId,
                StaffId = staffId,
                UserId = userId,
                Total = count,
                TargetPayload = JsonConvert.SerializeObject(
                    targetPayload,
                    _jsonSerializerSettings),
                TaskType = BackgroundTaskType.ExportBroadcastStatusListToCsv
            };

            _appDbContext.BackgroundTasks.Add(backgroundTask);
            await _appDbContext.SaveChangesAsync();

            var taskPayload = JsonConvert.SerializeObject(
                new ExportBroadcastStatusToCsvBackgroundTaskPayload()
                {
                    CompanyId = companyId, BroadcastTemplateId = broadcastTemplateId,
                },
                _jsonSerializerSettings);

            var byteArray = Encoding.UTF8.GetBytes(taskPayload);
            var stream = new MemoryStream(byteArray);

            await _azureBlobStorageService.UploadFileAsBlob(stream, $"{backgroundTask.Id}.json", "background-tasks");

            BackgroundJob.Enqueue<IBackgroundTaskService>(
                x => x.Handle(backgroundTask.Id));

            return backgroundTask;
        }

        public async Task<BackgroundTask> EnqueueExportStatisticsDataToCsvTask(
            string userId,
            string companyId,
            long staffId,
            string staffUserId,
            DateTime messageFrom,
            DateTime messageTo,
            string status = null,
            string channels = null,
            string channelIds = null,
            string tags = null,
            long? teamId = null,
            bool? isTeamUnassigned = null,
            List<Condition> conditions = null)
        {
            int total = 0;

            if (string.IsNullOrEmpty(staffUserId) && !teamId.HasValue)
            {
                total = 1 + await _appDbContext.UserRoleStaffs
                    .CountAsync(
                        x =>
                            x.CompanyId == companyId
                            && x.Id != 1);
            }
            else if (teamId.HasValue)
            {
                total = 1 + await _appDbContext.CompanyStaffTeams
                    .CountAsync(
                        x =>
                            x.CompanyId == companyId
                            && x.Id == teamId);
            }
            else
            {
                total = (int) (messageTo - messageFrom).TotalDays;
            }

            var backgroundTask = new BackgroundTask
            {
                CompanyId = companyId,
                StaffId = staffId,
                UserId = userId,
                Total = total,
                TargetPayload = JsonConvert.SerializeObject(
                    new AnalyticBackgroundTaskTargetPayload(),
                    _jsonSerializerSettings),
                TaskType = BackgroundTaskType.ExportAnalyticToCsv
            };

            _appDbContext.BackgroundTasks.Add(backgroundTask);
            await _appDbContext.SaveChangesAsync();

            var taskPayload = JsonConvert.SerializeObject(
                new ExportAnalyticListToCsvBackgroundTaskPayload
                {
                    CompanyId = companyId,
                    StaffId = staffUserId,
                    MessageFrom = messageFrom,
                    MessageTo = messageTo,
                    Status = status,
                    Channels = channels,
                    ChannelIds = channelIds,
                    Tags = tags,
                    TeamId = teamId,
                    IsTeamUnassigned = isTeamUnassigned,
                    Conditions = conditions,
                },
                _jsonSerializerSettings);

            var byteArray = Encoding.UTF8.GetBytes(taskPayload);
            var stream = new MemoryStream(byteArray);

            await _azureBlobStorageService.UploadFileAsBlob(stream, $"{backgroundTask.Id}.json", "background-tasks");

            BackgroundJob.Enqueue<IBackgroundTaskService>(
                x => x.Handle(backgroundTask.Id));

            return backgroundTask;
        }

        public async Task<BackgroundTask> EnqueueImportWhatsAppHistoryTask(
            string userId,
            string companyId,
            long staffId,
            ImportWhatsAppHistoryViewModel importWhatsAppHistoryViewModel)
        {
            var backgroundTask = new BackgroundTask
            {
                CompanyId = companyId,
                StaffId = staffId,
                UserId = userId,
                Total = importWhatsAppHistoryViewModel.ChatHistories.Count,
                TargetPayload = JsonConvert.SerializeObject(
                    new WhatsAppHistoryBackgroundTaskTargetPayload(),
                    _jsonSerializerSettings),
                TaskType = BackgroundTaskType.ImportWhatsAppHistory,
            };

            _appDbContext.BackgroundTasks.Add(backgroundTask);
            await _appDbContext.SaveChangesAsync();

            var taskPayload = JsonConvert.SerializeObject(
                new ImportWhatsAppHistoryTaskPayLoad
                {
                    ImportWhatsAppHistoryViewModel = importWhatsAppHistoryViewModel
                },
                _jsonSerializerSettings);

            var byteArray = Encoding.UTF8.GetBytes(taskPayload);
            var stream = new MemoryStream(byteArray);

            await _azureBlobStorageService.UploadFileAsBlob(stream, $"{backgroundTask.Id}.json", "background-tasks");

            BackgroundJob.Enqueue<IBackgroundTaskService>(
                x => x.Handle(backgroundTask.Id));

            return backgroundTask;
        }

        public async Task<BackgroundTask> EnqueueImportGenericHistoryTask(
            string userId,
            string companyId,
            long staffId,
            ImportGenericHistoryViewModel importGenericHistoryViewModel)
        {
            var backgroundTask = new BackgroundTask
            {
                CompanyId = companyId,
                StaffId = staffId,
                UserId = userId,
                Total = importGenericHistoryViewModel.ChatHistories.Count,
                TargetPayload = JsonConvert.SerializeObject(
                    new ImportGenericHistoryTaskPayLoad(),
                    _jsonSerializerSettings),
                TaskType = BackgroundTaskType.ImportGenericHistory,
                IsDismissed = true, // Not supported on FE yet, will cause crashing
            };

            _appDbContext.BackgroundTasks.Add(backgroundTask);
            await _appDbContext.SaveChangesAsync();

            var taskPayload = JsonConvert.SerializeObject(
                new ImportGenericHistoryTaskPayLoad
                {
                    ImportGenericHistoryViewModel = importGenericHistoryViewModel
                },
                _jsonSerializerSettings);

            var byteArray = Encoding.UTF8.GetBytes(taskPayload);
            var stream = new MemoryStream(byteArray);

            await _azureBlobStorageService.UploadFileAsBlob(stream, $"{backgroundTask.Id}.json", "background-tasks");

            BackgroundJob.Enqueue<IBackgroundTaskService>(
                x => x.Handle(backgroundTask.Id));

            return backgroundTask;
        }

        public async Task<BackgroundTask> EnqueueConvertCampaignLeadsToContactListTask(
            string userId,
            string companyId,
            long staffId,
            string campaignId,
            long listId,
            ConvertCampaignLeadsToContactListTaskPayLoad convertCampaignLeadsToContactListTaskPayLoad)
        {
            var backgroundTask = new BackgroundTask
            {
                CompanyId = companyId,
                StaffId = staffId,
                UserId = userId,
                TargetPayload = JsonConvert.SerializeObject(
                    new ConvertCampaignLeadsToContactListTaskTargetPayLoad
                    {
                        NewContactListName = convertCampaignLeadsToContactListTaskPayLoad.NewContactListName,
                        ListId = listId
                    },
                    _jsonSerializerSettings),
                TaskType = BackgroundTaskType.ConvertCampaignLeadsToContactList,
                Total = 0,
                Progress = 0
            };

            _appDbContext.BackgroundTasks.Add(backgroundTask);
            await _appDbContext.SaveChangesAsync();

            var taskPayload = JsonConvert.SerializeObject(
                convertCampaignLeadsToContactListTaskPayLoad,
                _jsonSerializerSettings);
            var byteArray = Encoding.UTF8.GetBytes(taskPayload);
            var stream = new MemoryStream(byteArray);

            await _azureBlobStorageService.UploadFileAsBlob(stream, $"{backgroundTask.Id}.json", "background-tasks");

            BackgroundJob.Enqueue<IBackgroundTaskService>(
                x => x.Handle(backgroundTask.Id));

            return backgroundTask;
        }

        public async Task<BackgroundTask> EnqueueLoopThroughAndEnrollContactsToFlowHubTask(
            string userId,
            string companyId,
            long staffId,
            string flowHubWorkflowId,
            string flowHubWorkflowVersionedId)
        {
            var backgroundTask = new BackgroundTask
            {
                CompanyId = companyId,
                StaffId = staffId,
                UserId = userId,
                TargetPayload = JsonConvert.SerializeObject(
                    new LoopThroughAndEnrollContactsToFlowHubTaskTargetPayload
                    {
                        FlowHubWorkflowId = flowHubWorkflowId, FlowHubWorkflowVersionedId = flowHubWorkflowVersionedId
                    },
                    _jsonSerializerSettings),
                TaskType = BackgroundTaskType.LoopThroughAndEnrollContactsToFlowHub
            };

            _appDbContext.BackgroundTasks.Add(backgroundTask);
            await _appDbContext.SaveChangesAsync();

            var taskPayload = JsonConvert.SerializeObject(
                new LoopThroughAndEnrollContactsToFlowHubTaskPayLoad
                {
                    FlowHubWorkflowId = flowHubWorkflowId, FlowHubWorkflowVersionedId = flowHubWorkflowVersionedId,
                },
                _jsonSerializerSettings);

            var byteArray = Encoding.UTF8.GetBytes(taskPayload);
            var stream = new MemoryStream(byteArray);

            await _azureBlobStorageService.UploadFileAsBlob(stream, $"{backgroundTask.Id}.json", "background-tasks");

            BackgroundJob.Enqueue<IBackgroundTaskService>(
                x => x.Handle(backgroundTask.Id));

            return backgroundTask;
        }

        public async Task<BackgroundTask> EnqueueExportFlowHubWorkflowExecutionUsagesToCsvTask(
            string userId,
            string companyId,
            long staffId,
            WorkflowExecutionUsageFilters filters)
        {
            var backgroundTask = new BackgroundTask
            {
                CompanyId = companyId,
                StaffId = staffId,
                UserId = userId,
                TargetPayload = JsonConvert.SerializeObject(
                    new ExportFlowHubWorkflowExecutionUsagesToCsvTaskTargetPayload()
                    {
                        Filters = filters,
                    },
                    _jsonSerializerSettings),
                TaskType = BackgroundTaskType.ExportFlowHubWorkflowExecutionUsagesToCsv
            };

            _appDbContext.Add(backgroundTask);
            await _appDbContext.SaveChangesAsync();

            var byteArray = Encoding.UTF8.GetBytes(backgroundTask.TargetPayload);
            using var stream = new MemoryStream(byteArray);

            await _azureBlobStorageService.UploadFileAsBlob(stream, $"{backgroundTask.Id}.json", "background-tasks");

            BackgroundJob.Enqueue<IBackgroundTaskService>(
                x => x.Handle(backgroundTask.Id));

            return backgroundTask;
        }
    }
}