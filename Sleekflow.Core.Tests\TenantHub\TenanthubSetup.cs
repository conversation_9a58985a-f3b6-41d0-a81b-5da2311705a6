using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;
using Auth0.AuthenticationApi;
using Auth0.AuthenticationApi.Models;
using Auth0.ManagementApi;
using AutoMapper;
using Hangfire;
using Hangfire.MemoryStorage;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Logging.Abstractions;
using Microsoft.Extensions.Options;
using Microsoft.IdentityModel.Tokens;
using Moq;
using Newtonsoft.Json;
using Sleekflow.Apis.TenantHub.Api;
using Sleekflow.Core.Tests.Tools;
using StackExchange.Redis;
using Stripe;
using Travis_backend.AccountAuthenticationDomain.Models;
using Travis_backend.Auth0.Configuration;
using Travis_backend.Auth0.Controllers.Webhook;
using Travis_backend.Auth0.Services;
using Travis_backend.Auth0.Services.Auth0;
using Travis_backend.Cache;
using Travis_backend.CommonDomain.Models;
using Travis_backend.CommonDomain.Repositories;
using Travis_backend.CommonDomain.Services;
using Travis_backend.CompanyDomain.Services;
using Travis_backend.ContactDomain.Repositories;
using Travis_backend.ContactDomain.Services;
using Travis_backend.ConversationServices;
using Travis_backend.Database;
using Travis_backend.Database.Services;
using Travis_backend.**********************;
using Travis_backend.Mapping;
using Travis_backend.Services.Internal;
using Travis_backend.SignalR;
using Travis_backend.SubscriptionPlanDomain.Services;
using Auth0Config = Travis_backend.Auth0.Configuration.Auth0Config;

namespace Sleekflow.Core.Tests.TenantHub;

[SetUpFixture]
public abstract class TenanthubSetup
{
    protected IConfiguration _conf;
    protected ICoreService _coreService;
    protected IdentityOptions _options;
    protected ApplicationDbContext _dbContext;
    protected JwtOptions _jwtOptions;
    protected Auth0Config _auth0Config;
    protected IMapper _mapper;
    protected ITokenService _tokenService;
    protected HttpClientManagementConnection _managementConnection;
    protected HttpClient _httpClient;
    protected AuthenticationApiClient _authenticaionApiClient;
    protected ManagementApiClient _managementApiClient;
    protected SleekflowUserManager _userManager;
    protected UserStore<ApplicationUser> _userStore;
    protected IManagementFeaturesApi _managementFeaturesApi;
    protected IManagementEnabledFeaturesApi _managementEnabledFeaturesApi;
    protected IManagementIpWhitelistsApi _managementIpWhitelistsApi;
    protected IAuthorizedIpWhitelistsApi _authorizedIpWhitelistsApi;
    protected Auth0ActionEventController _auth0Event;
    protected IDistributedCache _cache;
    protected InternalTenantHubActionEventController _internalTenantHubActionEventController;
    protected ICountryService _countryService;
    protected IAuth0CompanyService _auth0CompanyService;
    protected ICacheManagerService _cacheManagerService;

    [OneTimeSetUp]
    public async Task Setup()
    {
        _conf = AppConfigure.InitConfiguration();
        _auth0Config = _conf.GetSection("Auth0").Get<Auth0Config>();

        // var mockCoreAuth0Config = new Mock<CoreAuth0Config>(
        var mockCoreAuth0Config = new Auth0Config()
        {
            Audience = _auth0Config.Audience,
            Issuers = _auth0Config.Issuers,
            ClientId = _auth0Config.ClientId,
            ClientSecret = _auth0Config.ClientSecret,
            DatabaseConnectionName = _auth0Config.DatabaseConnectionName,
            Domain = _auth0Config.Domain,
            HttpRetries = _auth0Config.HttpRetries,
            Namespace = _auth0Config.Namespace,
            RoleClaimType = _auth0Config.RoleClaimType,
            UserEmailClaimType = _auth0Config.UserEmailClaimType,
            UserIdClaimType = _auth0Config.UserIdClaimType,
        };

        var optionsBuilder = new DbContextOptionsBuilder<ApplicationDbContext>()
            .EnableSensitiveDataLogging()
            .UseQueryTrackingBehavior(QueryTrackingBehavior.NoTracking)
            // .UseInMemoryDatabase(databaseName:"UsersTest");                     // If you want to use inmemory database, enable it
            .UseSqlServer(
                _conf.GetConnectionString("DefaultConnection")); // If you want to use UAT database, enable it
        var jobStorage = new MemoryStorage();
        GlobalConfiguration.Configuration.UseStorage(jobStorage);

        _jwtOptions = (new JwtOptions()
        {
            Key = "83A74F5963336617701F83BBB8A4201212C42A51E721DD51CAB28201ADEDF8BB",
            Issuer = "https://travis-crm-api-hk.azurewebsites.net",
            Audience = "https://travis-crm-api-hk.azurewebsites.net",
            Lifetime = 365
        });
        _httpClient = new HttpClient();
        _managementConnection = new HttpClientManagementConnection(
            _httpClient,
            new HttpClientManagementConnectionOptions()
            {
                NumberOfHttpRetries = 10
            });
        _authenticaionApiClient = new AuthenticationApiClient(new Uri($"https://{_auth0Config.Domain}"));
        var token = await _authenticaionApiClient.GetTokenAsync(
            new ClientCredentialsTokenRequest()
            {
                ClientId = _auth0Config.ClientId,
                ClientSecret = _auth0Config.ClientSecret,
                // Audience = auth0Config.Audience
                Audience = "https://sleekflow-dev.eu.auth0.com/api/v2/"
            });

        _managementApiClient =
            new ManagementApiClient(
                token.AccessToken,
                _auth0Config.Domain,
                // new Uri(auth0Config.Audience).Host,
                // "https://sleekflow-dev.eu.auth0.com/api/v2/",
                _managementConnection);
        _options = new IdentityOptions();

        // Default Password settings.
        _options.Password.RequireDigit = false;
        _options.Password.RequireLowercase = false;
        _options.Password.RequireNonAlphanumeric = false;
        _options.Password.RequireUppercase = false;
        _options.Password.RequiredLength = 6;
        _options.ClaimsIdentity.RoleClaimType = _conf["Auth0:Namespace"] + _conf["Auth0:RoleClaimType"];
        _options.ClaimsIdentity.UserIdClaimType = _conf["Auth0:Namespace"] + _conf["Auth0:UserIdClaimType"];
        _options.ClaimsIdentity.UserNameClaimType = _conf["Auth0:Namespace"] + _conf["Auth0:UserNameClaimType"];
        _options.ClaimsIdentity.EmailClaimType = _conf["Auth0:Namespace"] + _conf["Auth0:UserEmailClaimType"];

        _dbContext = new ApplicationDbContext(optionsBuilder.Options);
        _userStore = new UserStore<ApplicationUser>(_dbContext);

        var distributedCacheOptions =
            Options.Create<MemoryDistributedCacheOptions>(new MemoryDistributedCacheOptions());
        IDistributedCache distributedCache = new MemoryDistributedCache(distributedCacheOptions);

        var cacheManagerService = new CacheManagerService(
            ConnectionMultiplexer.Connect(
                "sleekflow-redis739dbd6c.redis.cache.windows.net:6380,password=LSpaOPbm5b308TOUYaMDQwfDVUQZV7OODAzCaBAySj0=,ssl=True,abortConnect=False"),
            NullLogger<CacheManagerService>.Instance
        );

        var mapperConf = new MapperConfiguration(
            c =>
            {
                c.AddProfile<ViewModelToEntityMappingProfile>();
            });
        _dbContext = new ApplicationDbContext(optionsBuilder.Options);

        IOptions<IdentityOptions> idtOptionsAccessor = Options.Create(_options);
        Mock<IPasswordHasher<ApplicationUser>> passwordHasher = new ();
        Mock<IUserTwoFactorTokenProvider<ApplicationUser>> mockTokenProvider =
            new Mock<IUserTwoFactorTokenProvider<ApplicationUser>>();
        mockTokenProvider
            .Setup(
                o => o.GenerateAsync(
                    It.IsAny<string>(),
                    It.IsAny<UserManager<ApplicationUser>>(),
                    It.IsAny<ApplicationUser>()))
            .ReturnsAsync("diuLaMa");
        mockTokenProvider
            .Setup(
                o => o.ValidateAsync(
                    It.IsAny<string>(),
                    It.IsAny<string>(),
                    It.IsAny<UserManager<ApplicationUser>>(),
                    It.IsAny<ApplicationUser>()))
            .ReturnsAsync(true);
        var httpContext = new HttpContextAccessor();
        var mockUserManagerLogger = new Mock<ILogger<SleekflowUserManager>>();

        _mapper = new Mapper(mapperConf);
        _userManager = new SleekflowUserManager(
            _userStore,
            idtOptionsAccessor,
            null,
            null!,
            null!,
            null!,
            null!,
            null!,
            mockUserManagerLogger.Object,
            _auth0Config,
            _managementApiClient,
            _authenticaionApiClient,
            httpContext,
            cacheManagerService);

        _userManager.PasswordHasher = passwordHasher.Object;
        _userManager.RegisterTokenProvider("Default", mockTokenProvider.Object);
        var mockCountryRepository = new Mock<ICountryRepository>();
        var mockCoreLogger = new Mock<ILogger<CoreService>>();
        var mockTokenServiceLogger = new Mock<ILogger<Auth0TokenService>>();
        var mockHttpClientFactory = new Mock<IHttpClientFactory>();

        mockHttpClientFactory.Setup(x => x.CreateClient(It.IsAny<string>()))
            .Returns(new HttpClient());
        _tokenService = new Auth0TokenService(mockTokenServiceLogger.Object, _auth0Config, _userManager);
        _countryService = new CountryService(mockCountryRepository.Object);
        _coreService = new CoreService(
            _dbContext,
            _mapper,
            _conf,
            mockCoreLogger.Object,
            null,
            null,
            null,
            null,
            _userManager,
            Options.Create(_jwtOptions),
            null,
            null,
            null,
            null,
            _tokenService,
            mockHttpClientFactory.Object,
            _countryService,
            new Mock<IDbContextService>().Object,
            new UsageCycleCalculator(TimeProvider.System),
            new Mock<IDefaultSubscriptionPlanIdGetter>().Object,
            new Mock<ContactDeletionConfig>().Object,
            new Mock<IDistributedInvocationContextService>().Object,
            new UserProfileDuplicatedLogRepository(_dbContext),
            new IntegrationAlertConfigRepository(_dbContext));

        var mockSignalRService = new Mock<ISignalRService>();
        var mockActionEventLogger = new Mock<ILogger<Auth0ActionEventController>>();
        var mockTenantHubEventLogger = new Mock<ILogger<InternalTenantHubActionEventController>>();
        var tenantHubHttpClientHandler = new HttpClientHandler();
        var tenantHubHttpClient = new HttpClient(tenantHubHttpClientHandler)
        {
            DefaultRequestHeaders =
            {
                // {
                { "X-Sleekflow-Key", _conf.GetValue<string>("TenantHub:Key") },
                { "X-Sleekflow-Staff-Id", "staff-id" },
                { "X-Sleekflow-Company-Id", "company-id"},
                { "X-Sleekflow-Roles", "Admin"}
                // }
            }
        };

        var tenantHubConfig = new Sleekflow.Apis.TenantHub.Client.Configuration
        {
            BasePath = _conf.GetValue<string>("TenantHub:Endpoint")
        };
        _managementFeaturesApi = new ManagementFeaturesApi(tenantHubHttpClient, tenantHubConfig, tenantHubHttpClientHandler);
        _managementEnabledFeaturesApi = new ManagementEnabledFeaturesApi(
            tenantHubHttpClient,
            tenantHubConfig,
            tenantHubHttpClientHandler);
        _managementIpWhitelistsApi = new ManagementIpWhitelistsApi(
            tenantHubHttpClient,
            tenantHubConfig,
            tenantHubHttpClientHandler);
        _authorizedIpWhitelistsApi = new AuthorizedIpWhitelistsApi(
            tenantHubHttpClient,
            tenantHubConfig,
            tenantHubHttpClientHandler);

        _auth0Event = new Auth0ActionEventController(
            _dbContext,
            mockSignalRService.Object,
            _mapper,
            mockActionEventLogger.Object,
            _userManager,
            _conf,
            _auth0Config!,
            _coreService,
            _managementEnabledFeaturesApi,
            _managementFeaturesApi,
            _managementIpWhitelistsApi);

        var cacheOpts =
            Options.Create<MemoryDistributedCacheOptions>(new MemoryDistributedCacheOptions());
        _cache = new MemoryDistributedCache(cacheOpts);

        var mockCompanyServiceLogger = new Mock<ILogger<Auth0CompanyService>>();
        _auth0CompanyService = new Auth0CompanyService(
            _userManager,
            mockCompanyServiceLogger.Object,
            _coreService,
            _dbContext,
            new Mock<ICompanyInfoCacheService>().Object,
            _mapper,
            null,
            null,
            _conf,
            null);

        var connectionMultiplexer = ConnectionMultiplexer.Connect(
            "sleekflow-redis739dbd6c.redis.cache.windows.net:6380,password=LSpaOPbm5b308TOUYaMDQwfDVUQZV7OODAzCaBAySj0=,ssl=True,abortConnect=False");

        _cacheManagerService = new CacheManagerService(
            connectionMultiplexer,
            new Mock<ILogger<CacheManagerService>>().Object);

        _internalTenantHubActionEventController = new InternalTenantHubActionEventController(
            _coreService,
            _conf,
            null,
            _userManager,
            mockTenantHubEventLogger.Object,
            _dbContext,
            _cacheManagerService,
            _managementIpWhitelistsApi,
            _managementFeaturesApi,
            _managementEnabledFeaturesApi,
            mockSignalRService.Object,
            _mapper,
            _auth0CompanyService,
            null,
            null,
            null);
    }

    public static string CreateAuth0EventToken()
    {
        var tokenHandler = new JwtSecurityTokenHandler();
        var tokenDescriptor = new SecurityTokenDescriptor
        {
            Subject = new ClaimsIdentity(),
            Expires = DateTime.UtcNow.AddMinutes(5),
            Issuer = string.Empty,
            Audience = string.Empty,
            SigningCredentials = new SigningCredentials(
                // new SymmetricSecurityKey(Encoding.ASCII.GetBytes(_travisBackendConfig.Secret)),
                new SymmetricSecurityKey(Encoding.ASCII.GetBytes("6a9_nBt?)R#@_he=v2Eo!K3B3Ae0ao`x*I`m}ZSX*S~hsQ7bQD]k#gh8r38ad,9o")),
                SecurityAlgorithms.HmacSha256Signature)
        };
        return tokenHandler.WriteToken(tokenHandler.CreateToken(tokenDescriptor));
    }

    internal async Task<AccessTokenResponse> LoginUser(string userName, string password)
    {
        var response = await _authenticaionApiClient.GetTokenAsync(
            new ResourceOwnerTokenRequest
            {
                Audience = _auth0Config.Audience,
                ClientId = _auth0Config.ClientId,
                ClientSecret = _auth0Config.ClientSecret,
                Realm = "Sleekflow-Username-Password-Authentication",
                Password = password,
                Scope = "openid",
                Username = userName,
            });
        return response;
    }

    internal ClaimsPrincipal TokenToUserLoginPrincipal(string token)
    {
        var jwtHandler = new JwtSecurityTokenHandler();
        var jwtToken = jwtHandler.ReadJwtToken(token);
        var identity = new Mock<ClaimsIdentity>();
        identity.Setup(o => o.Claims).Returns(jwtToken.Claims);
        identity.Setup(o => o.IsAuthenticated).Returns(true);

        var principal = new Mock<ClaimsPrincipal>();
        principal.Setup(o => o.Claims).Returns(identity.Object.Claims);
        principal.Setup(o => o.Identity).Returns(identity.Object);
        principal.Setup(o => o.FindFirst("sub"))
            .Returns(identity.Object.Claims.FirstOrDefault(o => o.Type == "sub"));
        principal.Setup(o => o.FindFirst("iss"))
            .Returns(identity.Object.Claims.FirstOrDefault(o => o.Type == "iss"));

        return principal.Object;
    }
}

