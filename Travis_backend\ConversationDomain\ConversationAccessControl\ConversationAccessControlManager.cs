using Travis_backend.ConversationDomain.Models;
using Travis_backend.ConversationDomain.ViewModels;

namespace Travis_backend.ConversationDomain.ConversationAccessControl;

public interface IConversationAccessControlManager
{
    public bool HasAccess(StaffAccessControlAggregate staff, Conversation conversation);
}

public class ConversationAccessControlManager : IConversationAccessControlManager
{
    private readonly IConversationAccessRuleSet _conversationAccessRuleSet = new ConversationAccessRuleSet();

    public bool HasAccess(StaffAccessControlAggregate staff, Conversation conversation)
    {
        if (staff is null || conversation is null || staff.CompanyId != conversation.CompanyId )
        {
            return false;
        }

        return _conversationAccessRuleSet.Evaluate(staff, conversation);
    }
}