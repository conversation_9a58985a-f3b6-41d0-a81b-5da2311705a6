﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Travis_backend.AccountAuthenticationDomain.Models;
using Travis_backend.AccountAuthenticationDomain.Services;
using Travis_backend.CommonDomain.ViewModels;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.CompanyDomain.ViewModels;
using Travis_backend.ConversationServices;
using Travis_backend.Database;
using Travis_backend.Database.Filters;
using Travis_backend.Enums;
using Travis_backend.FileDomain.Services;
using Travis_backend.MessageDomain.Services;


namespace Travis_backend.Controllers.CompanyManagementControllers
{
    [Authorize]
    [Route("Company/[controller]")]
    public class QuickReplyController : Controller
    {
        private readonly ApplicationDbContext _appDbContext;
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly ILogger _logger;
        private readonly IConfiguration _configuration;
        private readonly IUploadService _uploadService;
        private readonly IQuickReplyService _quickReplyService;
        private readonly IAzureBlobStorageService _azureBlobStorageService;
        private readonly ICoreService _coreService;
        private readonly ISleekflowUserService _sleekflowUserService;

        public QuickReplyController(
            ApplicationDbContext appDbContext,
            UserManager<ApplicationUser> userManager,
            IConfiguration configuration,
            ILogger<QuickReplyController> logger,
            IUploadService uploadService,
            IQuickReplyService quickReplyService,
            IAzureBlobStorageService azureBlobStorageService,
            ICoreService coreService,
            ISleekflowUserService sleekflowUserService)
        {
            _userManager = userManager;
            _appDbContext = appDbContext;
            _configuration = configuration;
            _logger = logger;
            _uploadService = uploadService;
            _quickReplyService = quickReplyService;
            _azureBlobStorageService = azureBlobStorageService;
            _coreService = coreService;
            _sleekflowUserService = sleekflowUserService;
        }

        [HttpGet]
        [ServiceFilter(typeof(ReadOnlyEndpointAttribute))]
        public async Task<ActionResult<AddQuickReplyResult>> GetCompanyQuickReplies(
            [FromQuery(Name = "keyword")]
            string keyword = null,
            [FromQuery(Name = "offset")]
            int offset = 0,
            [FromQuery(Name = "limit")]
            int limit = 10,
            [FromQuery(Name = "conversationId")]
            string conversationId = null,
            [FromQuery]
            QuickReplyType type = QuickReplyType.Text)
        {
            try
            {
                if (User.Identity.IsAuthenticated)
                {
                    var applicationUser = await _sleekflowUserService.GetUserAsync(User);
                    // var companyUser = await _appDbContext.UserRoleStaffs.Where(x => x.IdentityId == userId).FirstOrDefaultAsync();
                    var companyUser = await _coreService.GetCompanyStaff(applicationUser);

                    if (companyUser == null)
                    {
                        return Unauthorized();
                    }

                    var response = await _quickReplyService.GetQuickReplyResult(companyUser, conversationId, type, keyword, offset, limit, false, cancellationToken: HttpContext.RequestAborted);
                    return Ok(response);
                }

                return Ok(
                    new AddQuickReplyResult
                    {
                        List = new List<CompanyQuickReplyResponse>()
                    });
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[{MethodName} endpoint] Error: {ExceptionMessage}",
                    nameof(GetCompanyQuickReplies),
                    ex.Message);

                return Ok(
                    new AddQuickReplyResult
                    {
                        List = new List<CompanyQuickReplyResponse>()
                    });
            }
        }

        [HttpGet]
        [Route("Text")]
        [ServiceFilter(typeof(ReadOnlyEndpointAttribute))]
        public async Task<ActionResult<AddQuickReplyResult>> GetCompanyQuickRepliesText(
            [FromQuery(Name = "keyword")]
            string keyword = null,
            [FromQuery(Name = "offset")]
            int offset = 0,
            [FromQuery(Name = "limit")]
            int limit = 10,
            [FromQuery(Name = "conversationId")]
            string conversationId = null,
            [FromQuery]
            QuickReplyType type = QuickReplyType.Text)
        {
            if (User.Identity.IsAuthenticated)
            {
                var applicationUser = await _sleekflowUserService.GetUserAsync(User);
                var companyUser = await _coreService.GetCompanyStaff(applicationUser);

                if (companyUser == null)
                {
                    return Unauthorized();
                }

                var response = await _quickReplyService.GetQuickReplyResult(companyUser, conversationId, type, keyword, offset, limit, cancellationToken: HttpContext.RequestAborted);

                return Ok(response);
            }

            return BadRequest();
        }



        [HttpPost]
        public async Task<ActionResult<AddQuickReplyResult>> AddCompanyQuickReplies(
            [FromBody]
            List<CompanyQuickReplyViewModel> companyQuickReplyViewModels)
        {
            if (User.Identity.IsAuthenticated)
            {
                // var companyUser = await _appDbContext.UserRoleStaffs.Where(x => x.IdentityId == userId).FirstOrDefaultAsync();
                var companyUser = await _coreService.GetCompanyStaff(
                    await _userManager.GetUserAsync(User));

                if (companyUser == null
                    || companyUser.RoleType == StaffUserRole.Staff)
                {
                    return Unauthorized();
                }

                var result = await _quickReplyService.AddQuickReplyResult(companyUser, companyQuickReplyViewModels);

                return Ok(result);
            }

            return BadRequest();
        }

        [HttpPost]
        [Route("attachment/{quickReplyId}")]
        public async Task<ActionResult<CompanyQuickReplyFile>> UploadFile(
            long quickReplyId,
            [FromForm]
            AssignmentAttachmentViewModel attachmentViewModel)
        {
            if (ModelState.IsValid
                && User.Identity.IsAuthenticated)
            {
                // var senderId = _userManager.GetUserId(User);
                // var companyUser = await _appDbContext.UserRoleStaffs.Where(x => x.IdentityId == senderId).Include(x => x.Company.StorageConfig).FirstOrDefaultAsync();
                var companyUser = await _coreService.GetCompanyStaff(
                    await _userManager.GetUserAsync(User));

                if (companyUser == null
                    || companyUser.RoleType == StaffUserRole.Staff)
                {
                    return Unauthorized();
                }

                var quickReply = await _appDbContext.CompanyQuickReplies
                    .Include(x => x.CompanyQuickReplyLinguals)
                    .Include(x => x.QuickReplyFile)
                    .FirstOrDefaultAsync(
                        x =>
                            x.Id == quickReplyId
                            && x.CompanyId == companyUser.CompanyId);

                foreach (IFormFile file in attachmentViewModel.files)
                {
                    var fileName = $"QuickReply/{quickReplyId}/{DateTime.UtcNow:o}/{file.FileName}";

                    var uploadFileResult = await _uploadService.UploadFile(
                        companyUser.CompanyId,
                        fileName,
                        file);

                    if (uploadFileResult?.Url == null)
                    {
                        return BadRequest(
                            new ResponseViewModel
                            {
                                message = "upload failure"
                            });
                    }

                    var newUploadedFile = new CompanyQuickReplyFile();
                    var appDomainName = _configuration.GetValue<string>("Values:DomainName");

                    uploadFileResult.Url = $"{appDomainName}/Company/quickreply/attachment/{newUploadedFile.QuickReplyFileId}";

                    newUploadedFile.Filename = fileName;
                    newUploadedFile.BlobContainer = companyUser.CompanyId;
                    newUploadedFile.Url = uploadFileResult.Url;
                    newUploadedFile.MIMEType = file.ContentType;

                    if (quickReply.QuickReplyFile != null)
                    {
                        _appDbContext.CompanyQuickReplyFiles.Remove(quickReply.QuickReplyFile);
                    }

                    quickReply.QuickReplyFile = newUploadedFile;

                    await _appDbContext.SaveChangesAsync();

                    return Ok(newUploadedFile);
                }
            }

            return BadRequest();
        }

        [HttpDelete]
        [Route("attachment/{quickReplyIdStr}")]
        public async Task<IActionResult> DeleteUploadedFile(
            string quickReplyIdStr,
            [FromForm]
            AssignmentAttachmentViewModel attachmentViewModel)
        {
            if (ModelState.IsValid
                && User.Identity.IsAuthenticated)
            {
                // var companyUser = await _appDbContext.UserRoleStaffs.Where(x => x.IdentityId == senderId).Include(x => x.Company.StorageConfig).FirstOrDefaultAsync();
                var companyUser = await _coreService.GetCompanyStaff(
                    await _userManager.GetUserAsync(User));

                if (companyUser == null
                    || companyUser.RoleType == StaffUserRole.Staff)
                {
                    return Unauthorized();
                }

                long quickReplyId = 0;

                if (long.TryParse(quickReplyIdStr, out quickReplyId))
                {
                    var quickReply = await _appDbContext.CompanyQuickReplies
                        .Include(x => x.CompanyQuickReplyLinguals)
                        .Include(x => x.QuickReplyFile)
                        .FirstOrDefaultAsync(
                            x =>
                                x.Id == quickReplyId
                                && x.CompanyId == companyUser.CompanyId);

                    if (quickReply == null)
                    {
                        return BadRequest(
                            new ResponseViewModel
                            {
                                message = $"No quickreply is found, Id: {quickReplyIdStr}"
                            });
                    }

                    if (quickReply.QuickReplyFile != null)
                    {
                        try
                        {
                            await _azureBlobStorageService.DeleteFromAzureBlob(
                                quickReply.QuickReplyFile.Filename,
                                companyUser.Company.StorageConfig.ContainerName);
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(
                                ex,
                                "[{MethodName} endpoint] Error deleting file for Quick Reply {QuickReplyId}: {ExceptionMessage}",
                                nameof(DeleteUploadedFile),
                                quickReplyId,
                                ex.Message);
                        }

                        _appDbContext.CompanyQuickReplyFiles.Remove(quickReply.QuickReplyFile);

                        await _appDbContext.SaveChangesAsync();

                        return Ok(
                            new ResponseViewModel
                            {
                                message = $"success"
                            });
                    }
                    else
                    {
                        return BadRequest();
                    }
                }
                // else
                // {
                //    var quickReply = await _appDbContext.CompanyQuickReplyFiles.Where(x => x.BlobContainer == companyUser.Company.StorageConfig.ContainerName && x.QuickReplyFileId == quickReplyIdStr).FirstOrDefaultAsync();

                // if (quickReply != null)
                //    {
                //        try
                //        {
                //            await _azureBlobStorageService.DeleteFromAzureBlob(quickReply.Filename, companyUser.Company.StorageConfig.ContainerName);
                //        }
                //        catch (Exception ex)
                //        {
                //            _logger.LogError(ex, ex.Message);
                //        }

                // _appDbContext.CompanyQuickReplyFiles.Remove(quickReply);
                //        await _appDbContext.SaveChangesAsync();
                //        return Ok(new ResponseViewModel { message = $"success" });
                //    }
                // }
            }

            return BadRequest();
        }

        // [HttpDelete]
        // [Route("attachment/{fileId}")]
        // public async Task<IActionResult> DeleteUploadedFile(string fileId, [FromForm] AssignmentAttachmentViewModel attachmentViewModel)
        // {
        //    if (ModelState.IsValid && User.Identity.IsAuthenticated)
        //    {
        //        var senderId = _userManager.GetUserId(User);
        //        var companyUser = await _appDbContext.UserRoleStaffs.Where(x => x.IdentityId == senderId).Include(x => x.Company.StorageConfig).FirstOrDefaultAsync();

        // if (string.IsNullOrEmpty(companyUser.CompanyId))
        //            return Unauthorized();

        // var quickReply = await _appDbContext.CompanyQuickReplyFiles.Where(x => x.QuickReplyFileId == fileId).FirstOrDefaultAsync();

        // if (quickReply != null)
        //        {
        //            await _azureBlobStorageService.DeleteFromAzureBlob(quickReply.Filename, companyUser.Company.StorageConfig.ContainerName);

        // _appDbContext.CompanyQuickReplyFiles.Remove(quickReply);
        //            await _appDbContext.SaveChangesAsync();
        //            return Ok(new ResponseViewModel { message = $"success" });
        //        }
        //        else
        //        {
        //            return BadRequest();
        //        }
        //    }
        //    return BadRequest();
        // }

        [HttpGet]
        [AllowAnonymous]
        [Route("attachment/private/{fileId}")]
        public async Task<IActionResult> GetPrivateFile(string fileId)
        {
            var file = await _appDbContext.CompanyQuickReplyFiles
                .FirstOrDefaultAsync(x => x.QuickReplyFileId == fileId);

            var stream = await _azureBlobStorageService.DownloadFromAzureBlob(
                file.Filename,
                file.BlobContainer);

            var extension = Path.GetExtension(file.Filename);

            if (string.IsNullOrEmpty(extension))
            {
                switch (file.MIMEType)
                {
                    case "video/mp4":
                        extension = "mp4";
                        break;
                    case "image/jpeg":
                        extension = "jpg";
                        break;
                    case "image/png":
                        extension = "png";
                        break;
                }
            }

            var filename = Path.GetFileNameWithoutExtension(file.Filename);

            var res = File(
                stream.ToArray(),
                string.IsNullOrEmpty(file.MIMEType) ? "application/octet-stream" : file.MIMEType,
                $"{filename}{(string.IsNullOrEmpty(extension) && file.MIMEType == "video/mp4" ? ".mp4" : $".{extension}")}");

            res.EnableRangeProcessing = true;

            return res;
        }

        [HttpGet]
        [AllowAnonymous]
        [Route("attachment/{fileId}")]
        public async Task<IActionResult> GetFile(string fileId)
        {
            var file = await _appDbContext.CompanyQuickReplyFiles
                .FirstOrDefaultAsync(x => x.QuickReplyFileId == fileId);

            var stream = await _azureBlobStorageService.DownloadFromAzureBlob(
                file.Filename,
                file.BlobContainer);

            var extension = Path.GetExtension(file.Filename);

            if (string.IsNullOrEmpty(extension))
            {
                switch (file.MIMEType)
                {
                    case "video/mp4":
                        extension = "mp4";
                        break;
                    case "image/jpeg":
                        extension = "jpg";
                        break;
                    case "image/png":
                        extension = "png";
                        break;
                }
            }

            var filename = Path.GetFileNameWithoutExtension(file.Filename);

            var res = File(
                stream.ToArray(),
                string.IsNullOrEmpty(file.MIMEType) ? "application/octet-stream" : file.MIMEType,
                $"{filename}{(string.IsNullOrEmpty(extension) && file.MIMEType == "video/mp4" ? ".mp4" : $".{extension}")}");

            res.EnableRangeProcessing = true;

            return res;
        }

        [HttpDelete]
        public async Task<IActionResult> DeleteCompanyQuickReplies(
            [FromBody]
            List<CompanyQuickReplyViewModel> companyQuickReplyViewModels)
        {
            if (User.Identity.IsAuthenticated)
            {
                // var companyUser = await _appDbContext.UserRoleStaffs.Where(x => x.IdentityId == userId).FirstOrDefaultAsync();
                var companyUser = await _coreService.GetCompanyStaff(
                    await _userManager.GetUserAsync(User));

                if (companyUser == null
                    || companyUser.RoleType != StaffUserRole.Admin)
                {
                    return Unauthorized();
                }

                var result = await _quickReplyService.DeleteQuickReplyResult(companyUser, companyQuickReplyViewModels);

                return Ok(result);
            }

            return BadRequest();
        }

    }
}