namespace Travis_backend.CompanyDomain.Services;

using System;

/// <inheritdoc/>
public class UsageCycleCalculator : IUsageCycleCalculator
{
    /// <summary>
    /// TimeProvider.
    /// </summary>
    private readonly TimeProvider _timeProvider;

    /// <summary>
    /// Initializes a new instance of the <see cref="UsageCycleCalculator"/> class.
    /// </summary>
    /// <param name="timeProvider">TimeProvider.</param>
    public UsageCycleCalculator(TimeProvider timeProvider)
    {
        _timeProvider = timeProvider;
    }

    /// <inheritdoc/>
    public (DateTime From, DateTime To) GetUsageCycle(DateTime periodStart, DateTime periodEnd)
    {
        var (from, to) = GetUsageCycle(periodStart, periodEnd, TimeSpan.Zero);
        return (from.DateTime, to.DateTime);
    }

    /// <inheritdoc/>
    public (DateTimeOffset From, DateTimeOffset To) GetUsageCycle(DateTimeOffset periodStart, DateTimeOffset periodEnd, TimeSpan offset = default)
    {
        DateTimeOffset now = _timeProvider.GetUtcNow();

        var daysInCurrentMonth = DateTime.DaysInMonth(now.Year, now.Month);
        var passedMonthsOffset = periodStart.Day > daysInCurrentMonth || now.Day >= periodStart.Day ? 0 : -1;
        var passedMonths = now.Month - periodStart.Month + (12 * (now.Year - periodStart.Year)) + passedMonthsOffset;

        var usageCycleFrom = periodStart.AddMonths(passedMonths);
        var usageCycleTo = periodStart.AddMonths(passedMonths + 1) < periodEnd ? periodStart.AddMonths(passedMonths + 1) : periodEnd;

        return (new DateTimeOffset(usageCycleFrom.DateTime, offset), new DateTimeOffset(usageCycleTo.DateTime, offset));
    }
}