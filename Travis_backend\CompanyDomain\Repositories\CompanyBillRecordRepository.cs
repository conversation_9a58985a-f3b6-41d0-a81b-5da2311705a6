using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.Constants;
using Travis_backend.Database;
using Travis_backend.Enums;
using Travis_backend.Extensions;

namespace Travis_backend.CompanyDomain.Repositories;

/// <inheritdoc />
public class CompanyBillRecordRepository : ICompanyBillRecordRepository
{
    /// <summary>
    /// ApplicationDbContext.
    /// </summary>
    private readonly ApplicationDbContext _appDbContext;

    /// <summary>
    /// Initializes a new instance of the <see cref="CompanyBillRecordRepository"/> class.
    /// </summary>
    /// <param name="appDbContext">ApplicationDbContext.</param>
    public CompanyBillRecordRepository(ApplicationDbContext appDbContext)
    {
        _appDbContext = appDbContext;
    }

    /// <inheritdoc />
    public async Task<BillRecord> GetCompanyCurrentBaseSubscriptionBillRecord(string companyId)
    {
        DateTime now = DateTime.UtcNow;

        return await _appDbContext.CompanyBillRecords.AsNoTracking()
            .OrderByDescending(x => x.created)
            .ThenByDescending(x => x.PayAmount)
            .FirstOrDefaultAsync(x =>
                x.CompanyId == companyId
                && x.Status != BillStatus.Inactive
                && x.Status != BillStatus.Terminated
                && ValidSubscriptionPlan.SubscriptionPlan.Contains(x.SubscriptionPlanId)
                && x.PeriodStart <= now
                && x.PeriodEnd > now);
    }

    /// <inheritdoc />
    public async Task<IEnumerable<BillRecord>> GetActiveBillRecordAsync(string companyId, Expression<Func<BillRecord, bool>> extraPredication = null)
    {
        DateTime now = DateTime.UtcNow;

        return await _appDbContext.CompanyBillRecords.AsNoTracking()
            .Where(x =>
                x.CompanyId == companyId &&
                x.Status == BillStatus.Active &&
                x.PeriodStart <= now && now < x.PeriodEnd)
            .WhereIf(extraPredication != null, extraPredication)
            .ToListAsync();
    }

    /// <inheritdoc />
    public async Task<IEnumerable<BillRecord>> FindActiveByCompanyIdAndPlanTier(string companyId, SubscriptionTier tier)
    {
        return await _appDbContext.CompanyBillRecords.AsNoTracking()
            .Where(x =>
                x.CompanyId == companyId &&
                x.Status == BillStatus.Active &&
                x.SubscriptionTier == tier &&
                DateTime.UtcNow < x.PeriodEnd)
            .ToListAsync();
    }

    /// <inheritdoc />
    public async Task<IEnumerable<BillRecord>> FindActiveByCompanyIdAndPlanIds(string companyId, IEnumerable<string> planIds)
    {
        return await _appDbContext.CompanyBillRecords.AsNoTracking()
            .Where(x =>
                x.CompanyId == companyId &&
                x.Status == BillStatus.Active &&
                planIds.Contains(x.SubscriptionPlanId) &&
                DateTime.UtcNow < x.PeriodEnd)
            .ToListAsync();
    }

    /// <inheritdoc />
    public async Task<BillRecord> GetLastValidPaymentBillRecord(string companyId)
    {
        return await _appDbContext.CompanyBillRecords.AsNoTracking()
            .FirstOrDefaultAsync(x =>
                x.CompanyId == companyId &&
                !string.IsNullOrWhiteSpace(x.stripe_subscriptionId) &&
                !string.IsNullOrWhiteSpace(x.customerId));
    }

    /// <inheritdoc />
    public async Task<BillRecord> GetLatestStripeSubscriptionBillRecord(string stripeSubscriptionId, string subscriptionPlanId)
    {
        DateTime now = DateTime.UtcNow;

        return await _appDbContext.CompanyBillRecords.AsNoTracking()
            .OrderByDescending(x => x.created)
            .ThenByDescending(x => x.PayAmount)
            .FirstOrDefaultAsync(x =>
                x.stripe_subscriptionId == stripeSubscriptionId
                && x.SubscriptionPlanId == subscriptionPlanId
                && x.Status == BillStatus.Active
                && x.PeriodStart <= now
                && x.PeriodEnd > now);
    }

    /// <inheritdoc />
    public async Task<int> UpdateStipeSubscriptionCancelDateTime(long id, DateTime? cancelDateTime)
    {
        return await _appDbContext.CompanyBillRecords.Where(x => x.Id == id)
            .ExecuteUpdateAsync(entity => entity.SetProperty(x => x.StripeSubscriptionCancelDateTime, cancelDateTime));
    }

    /// <inheritdoc />
    public async Task<int> TerminateBillRecords(string companyId, string stripeSubscriptionId, string stripeSubscriptionItemId = null)
    {
        ArgumentException.ThrowIfNullOrEmpty(stripeSubscriptionId);

        DateTime now = DateTime.UtcNow;

        return await _appDbContext.CompanyBillRecords
            .Where(x => x.CompanyId == companyId && x.stripe_subscriptionId == stripeSubscriptionId && x.Status != BillStatus.Inactive)
            .WhereIf(!string.IsNullOrWhiteSpace(stripeSubscriptionItemId), x => x.StripeSubscriptionItemId == stripeSubscriptionItemId)
            .ExecuteUpdateAsync(entity => entity
                .SetProperty(x => x.Status, BillStatus.Terminated)
                .SetProperty(x => x.TerminatedDateTime, now));
    }
}