﻿using Travis_backend.InternalDomain.ViewModels;

namespace Sleekflow.Powerflow.Apis.ViewModels;

public class GetCmsAnalyticResponse
{
    public List<Travis_backend.InternalDomain.ViewModels.CmsDailyRevenueAnalyticDto> DailyAnalytics { get; set; } = new ();
}

public class GetCmsAccruedAnalyticResponse
{
    public List<CmsDailyAccruedRevenueAnalyticDto> DailyAccruedAnalytics { get; set; } = new ();
}

public class GetCmsCohortAnalysisDataResponse
{
    public List<Travis_backend.InternalDomain.ViewModels.CmsCohortAnalysisDataDto> MonthlyCohortAnalysisData { get; set; } = new ();
}

public class CompanyRevenueStatusBreakdownsResponse
{
    public List<Travis_backend.InternalDomain.ViewModels.CompanyRevenueStatusBreakdown> CompanyRevenueStatusBreakdowns { get; set; } = new ();
}

public class GetCmsAnalyticBySalesResponse
{
    public List<CmsDailyAnalyticByContactOwnerDto> ByContactOwnerDailyAnalytics { get; set; } = new ();
}

public class GetCmsAnalyticByOwnerResponse
{
    public CmsDailyAnalyticByContactOwnerDto ByOwnerDailyAnalytic { get; set; } = new ();
}

public class GetCmsAnalyticByOwnersResponse
{
    public CmsDailyAnalyticByContactOwnersDto ByOwnersDailyAnalytic { get; set; } = new ();
}

public class GetCmsAnalyticByPartnerStackGroupNamesResponse
{
    public CmsDailyAnalyticByPartnerStackGroupNamesDto ByPartnerStackGroupNamesDailyAnalytic { get; set; } = new ();
}

public class GetCmsDistributionAnalyticResponse
{
    public List<CmsDailyDistributionAnalyticDto> DailyDistributionAnalytics { get; set; } = new ();
}

public enum CompanyRevenueStatus
{
    New,
    Return,
    Increase,
    Sustain,
    Decrease,
    Churn,
    ChurnedInMiddle
}