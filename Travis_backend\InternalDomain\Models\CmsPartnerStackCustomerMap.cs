using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Travis_backend.CommonDomain.Models;
using Travis_backend.CompanyDomain.Models;

namespace Travis_backend.InternalDomain.Models;

public class CmsPartnerStackCustomerMap : DateAuditedEntity<long>
{
    [ForeignKey(nameof(CompanyId))]
    public Company Company { get; set; }

    [Required]
    public string CompanyId { get; set; }

    [MaxLength(255)]
    [Required]
    public string PartnerStackCustomerKey { get; set; }

    public IndividualCommissionConfig IndividualCommissionConfig { get; set; }

    public PartnerStackPartnerInformation PartnerStackPartnerInformation { get; set; }
}