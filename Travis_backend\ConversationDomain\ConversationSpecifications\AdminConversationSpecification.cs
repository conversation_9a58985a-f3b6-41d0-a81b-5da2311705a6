using System;
using System.Linq.Expressions;
using Travis_backend.ConversationDomain.Models;
using Travis_backend.ConversationDomain.ViewModels;
using static LinqKit.PredicateBuilder;

namespace Travis_backend.ConversationDomain;

public class AdminConversationSpecification : ISpecification<Conversation>
{
    private readonly StaffAccessControlAggregate _staff;

    public AdminConversationSpecification(StaffAccessControlAggregate staff)
    {
        _staff = staff;
    }

    public Expression<Func<Conversation, bool>> ToExpression()
    {
        // Admin currently can see any conversations with no restriction
        var conversation = New<Conversation>(true);
        return conversation;
    }

    public bool IsSatisfiedBy(Conversation conversation)
    {
        return conversation is not null && ToExpression().Compile().Invoke(conversation);
    }
}