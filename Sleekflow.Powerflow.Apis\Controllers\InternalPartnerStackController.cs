using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Travis_backend.AccountAuthenticationDomain.Models;
using Travis_backend.CommonDomain.ViewModels;
using Travis_backend.Configuration;
using Travis_backend.InternalDomain.Services;
using Travis_backend.InternalDomain.ViewModels;
using Travis_backend.ResellerDomain.ViewModels;

namespace Sleekflow.Powerflow.Apis.Controllers;

[Authorize(Roles = ApplicationUserRole.InternalCmsUser)]
[Route("/internal/partnerstack/[action]")]
public class InternalPartnerStackController : InternalControllerBase
{
    private readonly IInternalPartnerStackService _internalPartnerStackService;

    public InternalPartnerStackController(
        UserManager<ApplicationUser> userManager,
        IInternalPartnerStackService internalPartnerStackService)
        : base(userManager)
    {
        _internalPartnerStackService = internalPartnerStackService;
    }

    [HttpPost]
    public async Task<ActionResult<ResponseViewModel>> UpdatePartnerStackCustomerKey(
        [FromBody]
        UpdatePartnerStackCustomerKeyRequest request)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Invalid Form"
                });
        }

        var user = await GetCurrentValidInternalUser(
            new List<string>()
            {
                ApplicationUserRole.InternalCmsSuperUser,
                ApplicationUserRole.InternalCmsAdmin,
                ApplicationUserRole.InternalCmsCustomerSuccessUser,
                ApplicationUserRole.InternalCmsSalesUser,
                ApplicationUserRole.InternalCmsUser
            });

        if (user == null)
        {
            return Unauthorized();
        }

        var responseWrapper =
            await _internalPartnerStackService.CreateOrUpdatePartnerStackCustomerMapping(
                request.CompanyId,
                request.CustomerKey);

        if (!responseWrapper.IsSuccess)
        {
            return BadRequest(
                new ResponseViewModel()
                {
                    message = responseWrapper.ErrorMsg
                });
        }

        var response = (CmsPartnerStackCustomerMapDto) responseWrapper.Data;

        return Ok(response);
    }

    [HttpPost]
    public async Task<ActionResult<ResponseViewModel>> SyncMrrToPartnerStack(
        [FromBody]
        SyncMrrToPartnerStackRequest request)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Invalid Form"
                });
        }

        var user = await GetCurrentValidInternalUser(
            new List<string>()
            {
                ApplicationUserRole.InternalCmsSuperUser,
                ApplicationUserRole.InternalCmsAdmin,
                ApplicationUserRole.InternalCmsCustomerSuccessUser,
                ApplicationUserRole.InternalCmsSalesUser,
                ApplicationUserRole.InternalCmsUser
            });

        if (user == null)
        {
            return Unauthorized();
        }

        var responseWrapper = await _internalPartnerStackService.PowerflowSyncMrrToPartnerStack(request.CompanyId);

        if (!responseWrapper.IsSuccess)
        {
            return BadRequest(
                new ResponseViewModel()
                {
                    message = responseWrapper.ErrorMsg
                });
        }

        var response = responseWrapper.Data.ToString();

        return Ok(response);
    }

    [HttpPost]
    public async Task<ActionResult<ResponseViewModel>> UpdatePartnerStackPartnerKey(
        [FromBody]
        UpdatePartnerStackPartnerKeyRequest request)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Invalid Form"
                });
        }

        var user = await GetCurrentValidInternalUser(
            new List<string>()
            {
                ApplicationUserRole.InternalCmsSuperUser,
                ApplicationUserRole.InternalCmsAdmin,
                ApplicationUserRole.InternalCmsCustomerSuccessUser,
                ApplicationUserRole.InternalCmsSalesUser,
                ApplicationUserRole.InternalCmsUser
            });

        if (user == null)
        {
            return Unauthorized();
        }

        var responseWrapper =
            await _internalPartnerStackService.UpdatePartnerStackPartnerKey(
                request.CompanyId,
                request.PartnerKey);

        if (!responseWrapper.IsSuccess)
        {
            return BadRequest(
                new ResponseViewModel()
                {
                    message = responseWrapper.ErrorMsg
                });
        }

        var response = (ResellerProfileInformation) responseWrapper.Data;

        return Ok(response);
    }

    [HttpPost]
    public async Task<ActionResult<ResponseViewModel>> UpdatePartnerStackIndividualCommissionConfig(
        [FromBody]
        UpdatePartnerStackCustomerCommissionConfigRequest request)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Invalid Form"
                });
        }

        var user = await GetCurrentValidInternalUser(
            new List<string>()
            {
                ApplicationUserRole.InternalCmsSuperUser,
                ApplicationUserRole.InternalCmsAdmin,
                ApplicationUserRole.InternalCmsCustomerSuccessUser,
                ApplicationUserRole.InternalCmsSalesUser,
                ApplicationUserRole.InternalCmsUser
            });

        if (user == null)
        {
            return Unauthorized();
        }

        var responseWrapper = await _internalPartnerStackService.PowerflowUpdatePartnerStackIndividualCommissionConfig(
            request.CompanyId,
            request.SyncType,
            request.IndividualCommissionRate,
            request.CommissionEndDate);

        if (!responseWrapper.IsSuccess)
        {
            return BadRequest(
                new ResponseViewModel()
                {
                    message = responseWrapper.ErrorMsg
                });
        }

        var response = (CmsPartnerStackCustomerMapDto) responseWrapper.Data;

        return Ok(response);
    }

    [HttpPost]
    public async Task<ActionResult<ResponseViewModel>> SyncPartnerStackPartnerInformation(
        [FromBody]
        SyncPartnerStackPartnerInformationRequest request)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Invalid Form"
                });
        }

        var user = await GetCurrentValidInternalUser(
            new List<string>()
            {
                ApplicationUserRole.InternalCmsSuperUser,
                ApplicationUserRole.InternalCmsAdmin,
                ApplicationUserRole.InternalCmsCustomerSuccessUser,
                ApplicationUserRole.InternalCmsSalesUser,
                ApplicationUserRole.InternalCmsUser
            });

        if (user == null)
        {
            return Unauthorized();
        }

        var responseWrapper = await _internalPartnerStackService.SyncPartnerStackPartnerInformation(request.CompanyId);

        if (!responseWrapper.IsSuccess)
        {
            return BadRequest(
                new ResponseViewModel()
                {
                    message = responseWrapper.ErrorMsg
                });
        }

        var response = (CmsPartnerStackCustomerMapDto) responseWrapper.Data;

        return Ok(response);
    }
}