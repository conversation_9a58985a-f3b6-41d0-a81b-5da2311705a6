using Pulumi;
using Sleekflow.Core.Infra.Perf.Components.Configs;
using Sleekflow.Core.Infra.Perf.Components.Models;
using Sleekflow.Core.Infra.Perf.Constants;
using Sleekflow.Core.Infra.Perf.Utils;
using Web = Pulumi.AzureNative.Web;
using Insights = Pulumi.AzureNative.Insights;

namespace Sleekflow.Core.Infra.Perf.Components.SleekflowCore;

public class SleekflowCore
{
    private readonly MyConfig _myConfig;
    private readonly List<EnvGroup> _envGroups;
    private readonly ServerConfig _serverConfig;
    private readonly ContainerRegistryOutput _containerRegistryOutput;

    public SleekflowCore(
        MyConfig myConfig,
        List<EnvGroup> envGroups,
        ServerConfig serverConfig,
        ContainerRegistryOutput containerRegistryOutput)
    {
        _myConfig = myConfig;
        _envGroups = envGroups;
        _serverConfig = serverConfig;
        _containerRegistryOutput = containerRegistryOutput;
    }

    public void InitSleekflowCore()
    {
        var imageName = Output.Format(
            $"{_containerRegistryOutput.Registry.LoginServer}/{ServiceNames.GetSleekflowPrefixedShortName(ServiceNames.SleekflowCore)}:{_myConfig.BuildTime}");
        var image = new DockerImages(
            ServiceNames.GetSleekflowPrefixedShortName(ServiceNames.SleekflowCore),
            imageName,
            _containerRegistryOutput
        ).InitDockerImages();

        foreach (var envGroup in _envGroups)
        {
            var resourceGroup = envGroup.ResourceGroup;
            var webApps = envGroup.WebApps;

            var regionalConfig = _serverConfig
                .RegionalConfigs
                .First(s => s.LocationName == envGroup.LocationName);

            var skuConfig = regionalConfig.SkuConfig.SleekflowCore;
            var plan = new Web.AppServicePlan(
                ResourceUtils.GetName(
                    $"sleekflow-core-plan-{LocationNames.GetShortName(envGroup.LocationName)}",
                    _myConfig),
                new Web.AppServicePlanArgs
                {
                    Kind = "Linux",
                    ResourceGroupName = resourceGroup.Name,
                    Sku = new Web.Inputs.SkuDescriptionArgs
                    {
                        Name = skuConfig.Name, Tier = skuConfig.Tier
                    },
                    Reserved = true,
                },
                new CustomResourceOptions
                {
                    Parent = resourceGroup
                });

            var webAppName =
                ResourceUtils.GetName(
                    envGroup.FormatAppName(ServiceNames.GetShortName(ServiceNames.SleekflowCore)),
                    _myConfig);

            var appSettings = AppSettingUtils.GetWebAppSettings(
                webAppName,
                envGroup.Redis,
                image,
                envGroup.LocationName,
                resourceGroup,
                regionalConfig.SleekflowCoreConfig,
                _containerRegistryOutput);

            var app = new Web.WebApp(
                webAppName,
                new Web.WebAppArgs
                {
                    ServerFarmId = plan.Id,
                    Name = webAppName,
                    ResourceGroupName = resourceGroup.Name,
                    SiteConfig = new Web.Inputs.SiteConfigArgs
                    {
                        AlwaysOn = true,
                        NumberOfWorkers = 1,
                        AppSettings = appSettings,
                        HealthCheckPath = "/__health",
                        ConnectionStrings = new[]
                        {
                            new Web.Inputs.ConnStringInfoArgs()
                            {
                                Name = "DefaultConnection",
                                ConnectionString = regionalConfig.SqlDbConfig.ConnectionString,
                                Type = Web.ConnectionStringType.SQLAzure
                            },
                            new Web.Inputs.ConnStringInfoArgs()
                            {
                                Name = "StorageConnectionString",
                                ConnectionString = regionalConfig.StorageConfig.ConnectionString,
                                Type = Web.ConnectionStringType.Custom
                            },
                        },
                        LinuxFxVersion =
                            imageName.Apply(n => $"DOCKER|{n}")
                    },
                    Kind = "app,linux,container",
                    Reserved = true,
                    PublicNetworkAccess = "Enabled",
                    HttpsOnly = true,
                },
                new CustomResourceOptions
                {
                    Parent = resourceGroup
                });

            webApps.Add(ServiceNames.SleekflowCore, app);
        }
    }
}