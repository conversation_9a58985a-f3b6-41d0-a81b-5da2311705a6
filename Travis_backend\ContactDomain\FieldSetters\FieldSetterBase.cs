﻿using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.ContactDomain.Exceptions;
using Travis_backend.ContactDomain.Interfaces;
using Travis_backend.ContactDomain.Models;
using Travis_backend.Database;
using Travis_backend.Enums;
using Travis_backend.Helpers;

namespace Travis_backend.ContactDomain.FieldSetters;

public abstract class FieldSetterBase : IFieldSetter
{
    protected readonly ApplicationDbContext _appDbContext;
    private readonly IFieldSetterFacade _fieldSetterFacade;

    protected abstract string FieldName { get; }

    protected UserProfile TargetUserProfile { get; private set; }

    protected CompanyCustomUserProfileField TargetCompanyCustomField { get; private set; }

    protected SetFieldRequest Request { get; private set; }

    protected FieldSetterBase(
        ApplicationDbContext appDbContext,
        IServiceProvider serviceProvider)
    {
        _appDbContext = appDbContext;
        _fieldSetterFacade = serviceProvider.GetRequiredService<IFieldSetterFacade>();
    }

    public async Task<bool> SetAsync(SetFieldRequest request)
    {
        // Initialization
        Request = InitializeRequest(request);
        Request = await FormatRequestAsync(Request);
        TargetUserProfile = await PopulateUserProfileAsync();
        TargetCompanyCustomField = await PopulateCompanyCustomFieldAsync();

        // Validation
        var hasNoChanges = ValidateHasNoChangesToCustomField();

        if (hasNoChanges)
        {
            return false;
        }

        await ValidateAsync();
        await ValidateFieldDataTypeValueFormatAsync();
        await RemoveUserDuplicatedTargetFieldsAsync();

        // Update fields logic
        var isUpdated = await HandleChangesAsync();

        if (isUpdated)
        {
            UpdateTargetCustomUserProfileField();
            await _appDbContext.SaveChangesAsync();

            await ExecutePostUpdateActionsAsync();
        }

        return isUpdated;
    }

    protected Task<bool> SetAsync(string fieldName, string fieldValue)
        => _fieldSetterFacade.SetAsync(
            new(
                Request.CompanyId,
                Request.UserProfileId,
                fieldName,
                fieldValue));

    public bool IsDefault
        => FieldName.Equals(DefaultFieldSetter.DefaultSetter);

    protected virtual Task<SetFieldRequest> FormatRequestAsync(SetFieldRequest request)
        => Task.FromResult(request);

    protected virtual Task ValidateAsync()
        => Task.CompletedTask;

    protected virtual Task ExecutePostUpdateActionsAsync()
        => Task.CompletedTask;

    public virtual bool IsSetterApplicable(string fieldName)
        => FieldName.Equals(fieldName, StringComparison.OrdinalIgnoreCase)
           || FieldName.Replace(" ", string.Empty).Equals(fieldName?.Replace(" ", string.Empty), StringComparison.OrdinalIgnoreCase);

    protected virtual bool ValidateHasNoChangesToCustomField()
    {
        var existingUserProfileField = TargetUserProfile.CustomFields?
            .FirstOrDefault(x => x.CompanyDefinedFieldId == TargetCompanyCustomField?.Id);

        return TargetCompanyCustomField is not null
               && (existingUserProfileField?.Value == Request.FieldValue
                   || (existingUserProfileField is null && string.IsNullOrEmpty(Request.FieldValue))
                   || IsMaskedValue(Request.FieldValue));
    }

    protected abstract Task<bool> HandleChangesAsync();

    #region Helper methods

    private static SetFieldRequest InitializeRequest(SetFieldRequest request)
    {
        var fieldValue = request.FieldValue?.Trim() ?? string.Empty;

        if (fieldValue.Length > 450)
        {
            fieldValue = fieldValue[..449];
        }

        request = request with
        {
            FieldValue = fieldValue
        };

        return request;
    }

    private async Task<UserProfile> PopulateUserProfileAsync()
    {
        var targetUserProfile = await _appDbContext.UserProfiles
            .Include(profile => profile.CustomFields)
            .FirstOrDefaultAsync(
                profile =>
                    profile.CompanyId == Request.CompanyId
                    && profile.Id == Request.UserProfileId);

        if (targetUserProfile is null)
        {
            throw new UserProfileNotFoundException(Request.CompanyId, Request.UserProfileId);
        }

        return targetUserProfile;
    }

    private async Task<CompanyCustomUserProfileField> PopulateCompanyCustomFieldAsync()
    {
        var targetCompanyCustomField = await _appDbContext.CompanyCustomUserProfileFields
            .Include(x => x.CustomUserProfileFieldOptions)
            .FirstOrDefaultAsync(
                x =>
                    x.FieldName.ToLower() == Request.FieldName.ToLower()
                    && x.CompanyId == Request.CompanyId);

        if (targetCompanyCustomField is null)
        {
            targetCompanyCustomField = await _appDbContext.CompanyCustomUserProfileFields
                .Include(x => x.CustomUserProfileFieldOptions)
                .FirstOrDefaultAsync(
                    x =>
                        x.FieldName.Replace(" ", string.Empty).ToLower() == Request.FieldName.Replace(" ", string.Empty).ToLower()
                        && x.CompanyId == Request.CompanyId);
        }

        return targetCompanyCustomField;
    }

    private async Task ValidateFieldDataTypeValueFormatAsync()
    {
        if (string.IsNullOrEmpty(Request.FieldValue))
        {
            return;
        }

        var fieldValue = Request.FieldValue;

        try
        {
            switch (TargetCompanyCustomField?.Type)
            {
                case FieldDataType.Email:
                    if (!string.IsNullOrEmpty(fieldValue))
                    {
                        if (await _appDbContext.UserProfileCustomFields
                                .AnyAsync(
                                    x =>
                                        x.CompanyDefinedFieldId == TargetCompanyCustomField.Id
                                        && x.Value == fieldValue
                                        && x.UserProfileId != Request.UserProfileId))
                        {
                            throw new FormatException($"Duplicated {TargetCompanyCustomField.Type.ToString()}");
                        }
                    }

                    break;

                case FieldDataType.PhoneNumber:
                    if (!string.IsNullOrEmpty(fieldValue))
                    {
                        if (await _appDbContext.UserProfileCustomFields
                                .AnyAsync(
                                    x =>
                                        x.CompanyDefinedFieldId == TargetCompanyCustomField.Id
                                        && x.Value == fieldValue
                                        && x.UserProfileId != Request.UserProfileId))
                        {

                            throw new FormatException($"Duplicated {TargetCompanyCustomField.Type.ToString()}");
                        }

                        //Brazil phone number not return invalid phone number error
                        if (!fieldValue.StartsWith("55") && !PhoneNumberHelper.IsValidPhoneNumberContainsCountryCode(
                                fieldValue))
                        {
                            throw new FormatException("Invalid phone number");
                        }
                    }

                    break;
                case FieldDataType.Boolean:
                    if (!bool.TryParse(fieldValue, out _))
                    {
                        throw new FormatException($"Cannot parse to {TargetCompanyCustomField.Type.ToString()}");
                    }

                    break;

                case FieldDataType.Date or FieldDataType.DateTime:
                    if (!DateTime.TryParse(fieldValue, out _))
                    {
                        var isParsable = DateTime.TryParseExact(
                            fieldValue,
                            "dd/MM/yyyy HH:mm:ss",
                            new CultureInfo("en-US"),
                            DateTimeStyles.AllowWhiteSpaces,
                            out var targetDate);

                        if (!isParsable)
                        {
                            throw new FormatException($"Cannot parse to {TargetCompanyCustomField.Type.ToString()}");
                        }

                        var timeZoneId = await _appDbContext.CompanyCompanies
                            .Where(x => x.Id == Request.CompanyId)
                            .Select(x => x.TimeZoneInfoId)
                            .FirstOrDefaultAsync();

                        var daytime = targetDate
                            .Tolocaltime(timeZoneId)
                            .ToUniversalTime();

                        fieldValue = daytime.ToString("o");
                    }
                    else
                    {
                        fieldValue = DateTime.Parse(fieldValue).ToUniversalTime().ToString("o");
                    }

                    break;

                case FieldDataType.Number:
                    if (!double.TryParse(fieldValue, out _))
                    {
                        throw new FormatException($"Cannot parse to {TargetCompanyCustomField.Type.ToString()}");
                    }

                    break;

                case FieldDataType.TravisUser:
                    if (!await _appDbContext.UserRoleStaffs
                            .AnyAsync(
                                x =>
                                    x.CompanyId == Request.CompanyId
                                    && x.IdentityId == fieldValue))
                    {
                        throw new FormatException($"Cannot parse to {TargetCompanyCustomField.Type.ToString()}");
                    }

                    break;

                case FieldDataType.Team:
                    if (!long.TryParse(fieldValue, out var teamId)
                        || !await _appDbContext.CompanyStaffTeams
                            .AnyAsync(
                                x =>
                                    x.CompanyId == Request.CompanyId
                                    && x.Id == teamId))
                    {
                        throw new FormatException($"Cannot parse to {TargetCompanyCustomField.Type.ToString()}");
                    }

                    break;

                case FieldDataType.Options:
                    if (Request.FieldName.Equals("AssignedTeam", StringComparison.OrdinalIgnoreCase))
                    {
                        if (!long.TryParse(fieldValue, out var optionTeamId)
                            || !await _appDbContext.CompanyStaffTeams
                                .AnyAsync(
                                    x =>
                                        x.CompanyId == Request.CompanyId
                                        && x.Id == optionTeamId))
                        {
                            throw new FormatException($"Cannot parse to {TargetCompanyCustomField.Type.ToString()}");
                        }
                    }
                    else
                    {
                        if (TargetCompanyCustomField.CustomUserProfileFieldOptions
                            .All(
                                x => !string.Equals(
                                    x.Value.Trim(),
                                    fieldValue.Trim(),
                                    StringComparison.OrdinalIgnoreCase)))
                        {
                            // Add new Options
                            var newOptions = new CustomUserProfileFieldOption()
                            {
                                CompanyCustomUserProfileFieldId = TargetCompanyCustomField.Id,
                                Value = fieldValue,
                                CustomUserProfileFieldOptionLinguals =
                                    new List<CustomUserProfileFieldOptionLingual>()
                                    {
                                        new CustomUserProfileFieldOptionLingual()
                                        {
                                            DisplayName = fieldValue, Language = "en"
                                        }
                                    }
                            };

                            TargetCompanyCustomField.CustomUserProfileFieldOptions.Add(newOptions);
                            await _appDbContext.SaveChangesAsync();
                        }
                    }

                    break;
            }
        }
        catch (FormatException ex)
        {
            throw new FormatException(
                $"{{userProfileId: \"{TargetUserProfile.Id}\", fieldName: \"{TargetCompanyCustomField?.FieldName}\", value: \"{fieldValue}\", message: \"{ex.Message}\"}}");
        }

        Request = Request with
        {
            FieldValue = fieldValue
        };
    }

    private static bool IsMaskedValue(string fieldValue)
        => !string.IsNullOrEmpty(fieldValue) &&
           (fieldValue == "<-Masked->" || fieldValue.Contains("****"));

    private void UpdateTargetCustomUserProfileField()
    {
        if (TargetCompanyCustomField is null)
        {
            return;
        }

        var existingUserProfileField = TargetUserProfile.CustomFields
            .FirstOrDefault(x => x.CompanyDefinedFieldId == TargetCompanyCustomField.Id);

        if (existingUserProfileField is not null)
        {
            existingUserProfileField.Value = Request.FieldValue;
        }
        else
        {
            TargetUserProfile.CustomFields!.Add(new UserProfileCustomField()
            {
                CompanyDefinedField = TargetCompanyCustomField,
                Value = Request.FieldValue,
                CompanyId = TargetUserProfile.CompanyId
            });
        }
    }

    private async Task RemoveUserDuplicatedTargetFieldsAsync()
    {
        if (TargetCompanyCustomField is null)
        {
            return;
        }

        var duplicatedUserProfileFields = TargetUserProfile.CustomFields
            .Where(field => field.CompanyDefinedFieldId == TargetCompanyCustomField.Id)
            .Skip(1)
            .ToList();

        foreach (var duplicatedField in duplicatedUserProfileFields)
        {
            TargetUserProfile.CustomFields.Remove(duplicatedField);
        }

        if (duplicatedUserProfileFields.Count > 0)
        {
            await _appDbContext.SaveChangesAsync();
        }
    }

    #endregion
}