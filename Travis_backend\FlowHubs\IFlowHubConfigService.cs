using System.Threading.Tasks;
using Sleekflow.Apis.FlowHub.Model;

namespace Travis_backend.FlowHubs;

/// <summary>
/// FlowHub Config Service.
/// </summary>
public interface IFlowHubConfigService
{
    /// <summary>
    /// Get Company's FlowHub Config.
    /// </summary>
    /// <param name="companyId">Company Id.</param>
    /// <returns>Instance of FlowHubConfig.</returns>
    Task<FlowHubConfig> GetFlowHubConfigAsync(string companyId);

    /// <summary>
    /// Set whether to enable usage limit for company.
    /// </summary>
    /// <param name="companyId">Company Id.</param>
    /// <param name="isEnable">Boolean to indicate whether the usage limit is enabled.</param>
    /// <returns>Instance of FlowHubConfig.</returns>
    Task<FlowHubConfig> ToggleFlowHubUsageLimitAsync(string companyId, bool isEnable);
}