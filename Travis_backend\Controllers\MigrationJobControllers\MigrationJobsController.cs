﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Hangfire;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Sleekflow.Apis.FlowHub.Api;
using Sleekflow.Apis.FlowHub.Model;
using Travis_backend.BroadcastDomain.Services;
using Travis_backend.Constants;
using Travis_backend.Database;
using Travis_backend.Enums;
using Travis_backend.Extensions;
using Travis_backend.FlowHubs;
using Travis_backend.MigrationJobs.Attributes;

namespace Travis_backend.Controllers.MigrationJobControllers;

[ApiController]
[MigrationJobAuthorization]
[ApiExplorerSettings(IgnoreApi = true)]
[Route("api/migration-jobs")]
public class MigrationJobsController : ControllerBase
{
    private readonly IRecurringJobManager _recurringJobManager;
    private readonly IBroadcastMigrationService _broadcastMigrationService;
    private readonly ApplicationDbContext _appDbContext;
    private readonly ILogger<MigrationJobsController> _logger;
    private readonly IConfiguration _configuration;
    private readonly IFlowHubConfigService _flowHubConfigService;
    private readonly IFlowHubService _flowHubService;
    private readonly IFlowHubConfigsApi _flowHubConfigsApi;

    public MigrationJobsController(
        IRecurringJobManager recurringJobManager,
        IBroadcastMigrationService broadcastMigrationService,
        ApplicationDbContext appDbContext,
        ILogger<MigrationJobsController> logger,
        IFlowHubConfigService flowHubConfigService,
        IFlowHubService flowHubService,
        IFlowHubConfigsApi flowHubConfigsApi,
        IConfiguration configuration)
    {
        _recurringJobManager = recurringJobManager;
        _broadcastMigrationService = broadcastMigrationService;
        _appDbContext = appDbContext;
        _flowHubConfigService = flowHubConfigService;
        _flowHubService = flowHubService;
        _configuration = configuration;
        _flowHubConfigsApi = flowHubConfigsApi;
        _logger = logger;
    }

    [HttpPost]
    [Route("broadcast/migrate-targeted-channel")]
    public IActionResult MigrateBroadcastTargetedChannelAsync()
    {
        _recurringJobManager.AddOrUpdate(
            "MigrateBroadcastTargetedChannel",
            () => _broadcastMigrationService.MigrateTargetedChannelAsync(),
            "*/3 * * * *");

        return Ok();
    }

    [HttpPost]
    [Route("flowbuilder/patch-usage-limit")]
    public IActionResult PatchFlowHubConfigData([FromQuery] string companyId = null)
    {
        var jobId = BackgroundJob.Enqueue(() => PatchFlowBuilderUsageLimitAsync(companyId));
        return Ok(
            new
            {
                JobId = jobId
            });
    }

    public async Task PatchFlowBuilderUsageLimitAsync(string paramCompanyId)
    {
        var allActiveCompaniesIds = await _appDbContext.CompanyCompanies.AsNoTracking()
            .Where(c => !c.IsDeleted)
            .WhereIf(!string.IsNullOrWhiteSpace(paramCompanyId), company => company.Id == paramCompanyId)
            .Select(x => x.Id)
            .ToListAsync();

        _logger.LogInformation(JsonConvert.SerializeObject(allActiveCompaniesIds));

        foreach (var companyId in allActiveCompaniesIds)
        {
            try
            {
                var usageLimit = await _flowHubService.GetUsageLimitForUpdateAsync(companyId);

                await _flowHubConfigsApi.FlowHubConfigsUpdateFlowHubConfigPostAsync(
                    updateFlowHubConfigInput: new UpdateFlowHubConfigInput(
                        sleekflowCompanyId: companyId,
                        sleekflowStaffId: null!,
                        sleekflowStaffTeamIds: null!,
                        usageLimit: usageLimit,
                        origin: _configuration.GetValue<string>("Values:DomainName")));
            }
            catch (Exception e)
            {
                _logger.LogError(e, "Error while patching FlowHub config data for company {CompanyId}", companyId);
            }
        }
    }

    #region PatchEnableFlowHubUsageLimit

    [HttpGet]
    [Route("FlowHub/PatchEnableFlowHubUsageLimit")]
    public IActionResult PatchEnableFlowHubUsageLimit()
    {
        var jobId = BackgroundJob.Enqueue(() => PatchEnableFlowHubUsageLimitAsync());
        return Ok(
            new
            {
                JobId = jobId
            });
    }

    public async Task PatchEnableFlowHubUsageLimitAsync()
    {
        var date = new DateTime(2024, 8, 29, 0, 0, 0, DateTimeKind.Utc);

        var companyIds = await _appDbContext.CompanyCompanies.AsNoTracking()
            .Where(x => !x.IsDeleted && x.CreatedAt >= date)
            .Select(x => x.Id)
            .ToListAsync();

        foreach (var companyId in companyIds)
        {
            try
            {
                await _flowHubConfigService.ToggleFlowHubUsageLimitAsync(companyId, true);
            }
            catch (Exception e)
            {
                _logger.LogError(e, "Error while enabling FlowHub UsageLimit for company {CompanyId}", companyId);
            }
        }
    }

    #endregion
}