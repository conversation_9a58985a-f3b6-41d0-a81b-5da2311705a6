using Moq;
using Sleekflow.Apis.FlowHub.Api;
using Sleekflow.Apis.FlowHub.Model;
using Travis_backend.FlowHubs;

namespace Sleekflow.Core.Tests.FlowHubs;

public class FlowHubConfigServiceTest
{
    private Mock<IFlowHubConfigsApi> _mockFlowHubConfigsApi;

    [SetUp]
    public void Setup()
    {
        _mockFlowHubConfigsApi = new Mock<IFlowHubConfigsApi>();
    }

    [Test]
    public async Task Test_ToggleFlowHubUsageLimitAsync_Success()
    {
        //// Arrange
        var apiResponse = new ToggleFlowHubUsageLimitOutputOutput
        {
            Data = new ToggleFlowHubUsageLimitOutput
            {
                FlowHubConfig = new FlowHubConfig
                {
                    IsEnrolled = true,
                    IsUsageLimitEnabled = true
                }
            }
        };
        _mockFlowHubConfigsApi.Setup(x => x.FlowHubConfigsToggleFlowHubUsageLimitPostAsync(null, null, It.IsAny<ToggleFlowHubUsageLimitInput>(), default))
            .Returns(Task.FromResult(apiResponse));

        //// Act
        var service = GetFlowHubConfigService();
        var result = await service.ToggleFlowHubUsageLimitAsync("any-company-id", true);

        //// Assert
        Assert.Multiple(() =>
        {
            Assert.That(result.IsEnrolled, Is.True);
            Assert.That(result.IsUsageLimitEnabled, Is.True);
        });
    }

    private IFlowHubConfigService GetFlowHubConfigService() => new FlowHubConfigService(_mockFlowHubConfigsApi.Object);
}