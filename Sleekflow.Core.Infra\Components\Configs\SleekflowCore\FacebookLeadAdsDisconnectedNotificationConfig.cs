using Newtonsoft.Json;

namespace Sleekflow.Core.Infra.Components.Configs.SleekflowCore;

public class FacebookLeadAdsDisconnectedNotificationConfig
{
    [JsonProperty("from_phone_number")]
    public string FromPhoneNumber { get; set; }

    [Json<PERSON>roperty("template_name")]
    public string TemplateName { get; set; }

    [JsonProperty("reconnect_url")]
    public string ReconnectUrl { get; set; }

    [Json<PERSON>roperty("endpoint")]
    public string Endpoint { get; set; }

    [JsonProperty("api_key")]
    public string ApiKey { get; set; }

    [JsonProperty("host_company_id")]
    public string HostCompanyId { get; set; }

    public FacebookLeadAdsDisconnectedNotificationConfig(
        string fromPhoneNumber, 
        string templateName,
        string reconnectUrl,
        string endpoint,
        string apiKey,
        string hostCompanyId)
    {
        FromPhoneNumber = fromPhoneNumber;
        TemplateName = templateName;
        ReconnectUrl = reconnectUrl;
        Endpoint = endpoint;
        ApiKey = apiKey;
        HostCompanyId = hostCompanyId;
    }
}