using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AutoMapper;
using Hangfire;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.CompanyDomain.ViewModels;
using Travis_backend.ContactDomain.Services;
using Travis_backend.ConversationDomain.Models;
using Travis_backend.ConversationDomain.ViewModels;
using Travis_backend.ConversationServices;
using Travis_backend.Database;
using Travis_backend.Extensions;
using Travis_backend.FlowHubs;
using Travis_backend.FlowHubs.Models;
using Travis_backend.SignalR;

namespace Travis_backend.ConversationDomain.Services
{
    public interface IConversationAssigneeService
    {
        Task<Conversation> AddAdditionalAssignees(
            Conversation conversation,
            List<long> assigneeIds,
            Staff companyUser = null,
            bool isTriggerUpdate = true);

        Task<Conversation> ReplaceAdditionalAssignees(
            Conversation conversation,
            List<string> assigneeIds,
            Staff companyUser = null,
            bool isTriggerUpdate = true);

        Task<Conversation> RemoveAdditionalAssignees(
            Conversation conversation,
            List<long> assigneeIds,
            Staff companyUser = null,
            bool isTriggerUpdate = true);

        Task RemoveAssigneeAssignedTeamId(string companyId, long teamId, CreateNewTeamViewModel createNewTeamViewModel);

        Task SetAssigneeNewAssignedTeamId(string companyId, long teamId, CreateNewTeamViewModel createNewTeamViewModel);
    }

    public class ConversationAssigneeService : IConversationAssigneeService
    {
        private const string AssignedTeamFieldName = "assignedteam";

        private readonly ApplicationDbContext _appDbContext;
        private readonly ILogger _logger;
        private readonly ISignalRService _signalRService;
        private readonly IUserProfileHooks _userProfileHooks;

        public ConversationAssigneeService(
            ApplicationDbContext appDbContext,
            ILogger<ConversationAssigneeService> logger,
            ISignalRService signalRService,
            IUserProfileHooks userProfileHooks)
        {
            _appDbContext = appDbContext;
            _logger = logger;
            _signalRService = signalRService;
            _userProfileHooks = userProfileHooks;
        }

        public async Task<Conversation> AddAdditionalAssignees(
            Conversation conversation,
            List<long> assigneeIds,
            Staff companyUser = null,
            bool isTriggerUpdate = true)
        {
            try
            {
                if (assigneeIds is not { Count: > 0 })
                {
                    return conversation;
                }

                var collaboratorsCreated = new HashSet<AdditionalAssignee>();

                if (conversation.AdditionalAssignees is not { Count: > 0 })
                {
                    conversation.AdditionalAssignees = await _appDbContext.ConversationAdditionalAssignees
                        .Where(x => x.ConversationId == conversation.Id)
                        .ToListAsync();
                }

                foreach (var assigneeId in assigneeIds)
                {
                    if (conversation.AdditionalAssignees.Exists(x => x.AssigneeId == assigneeId))
                    {
                        continue;
                    }

                    var staff = await _appDbContext.UserRoleStaffs
                        .Include(x => x.Identity)
                        .FirstOrDefaultAsync(x => x.CompanyId == conversation.CompanyId
                                                  && x.Id == assigneeId);

                    if (staff is null)
                    {
                        continue;
                    }

                    var collaboratorToCreate = new AdditionalAssignee
                    {
                        ConversationId = conversation.Id,
                        AssigneeId = assigneeId,
                        CompanyId = conversation.CompanyId
                    };

                    conversation.AdditionalAssignees.Add(collaboratorToCreate);
                    conversation.ModifiedAt = DateTime.UtcNow;

                    await _appDbContext.SaveChangesAsync();

                    collaboratorsCreated.Add(collaboratorToCreate);

                    if (!isTriggerUpdate)
                    {
                        continue;
                    }

                    await _signalRService.SignalROnConversationAdditionalAssigneeChanged(
                        conversation.Id,
                        staff,
                        AdditionalAssigneeEvent.Added);
                }

                // add to activity log
                if (collaboratorsCreated.Any())
                {
                    await _userProfileHooks.OnConversationCollaboratorAddedAsync(
                    conversation.CompanyId,
                    conversation.UserProfileId,
                    companyUser?.IdentityId,
                    async () =>
                    {
                        var collaboratorsAdded = collaboratorsCreated
                            .Select(
                                x => new StaffData(
                                    x.Assignee.Id.ToString(),
                                    x.Assignee.IdentityId,
                                    x.Assignee.Identity.DisplayName))
                            .ToList();
                        var conversationAllCollaborators = await _appDbContext.ConversationAdditionalAssignees
                            .Where(
                                x =>
                                    x.Assignee.CompanyId == conversation.CompanyId
                                    && x.ConversationId == conversation.Id)
                            .Select(
                                x => new StaffData(
                                    x.Assignee.Id.ToString(),
                                    x.Assignee.IdentityId,
                                    x.Assignee.Identity.DisplayName))
                            .ToListAsync();
                        return new OnConversationCollaboratorAddedData(
                            collaboratorsAdded,
                            conversationAllCollaborators,
                            conversation.Id,
                            conversation.UserProfile.FirstName,
                            conversation.UserProfile.LastName);
                    });
                }

                return conversation;
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "AddAdditionalAssignee Error {ExceptionMessage}",
                    ex.Message);

                return conversation;
            }
        }

        public async Task<Conversation> ReplaceAdditionalAssignees(
            Conversation conversation,
            List<string> assigneeIds,
            Staff companyUser = null,
            bool isTriggerUpdate = true)
        {
            try
            {
                assigneeIds = assigneeIds
                    .Distinct()
                    .ToList();

                if (conversation.AdditionalAssignees is not { Count: > 0 })
                {
                    conversation.AdditionalAssignees = await _appDbContext.ConversationAdditionalAssignees
                        .Include(x => x.Assignee)
                        .ThenInclude(y => y.Identity)
                        .Where(x => x.ConversationId == conversation.Id)
                        .ToListAsync();
                }

                if (conversation.AdditionalAssignees.Any())
                {
                    var existingAssigneeIds = new List<long>();

                    foreach (AdditionalAssignee assignee in conversation.AdditionalAssignees)
                    {
                        if (assignee.AssigneeId.HasValue)
                        {
                            existingAssigneeIds.Add((long) assignee.AssigneeId);
                        }
                    }

                    conversation = await RemoveAdditionalAssignees(
                        conversation,
                        existingAssigneeIds,
                        companyUser,
                        isTriggerUpdate);
                }

                var assignedIdsInLong = await _appDbContext.UserRoleStaffs
                    .WhereIf(
                        companyUser != null,
                        x => x.CompanyId == companyUser.CompanyId)
                    .Where(x => assigneeIds.Contains(x.IdentityId))
                    .Select(x => x.Id)
                    .ToListAsync();

                conversation = await AddAdditionalAssignees(
                    conversation,
                    assignedIdsInLong,
                    companyUser,
                    isTriggerUpdate);

                return conversation;
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "ReplaceAdditionalAssignees Error: {ExceptionMessage}",
                    ex.Message);

                return conversation;
            }
        }

        public async Task<Conversation> RemoveAdditionalAssignees(
            Conversation conversation,
            List<long> assigneeIds,
            Staff companyUser = null,
            bool isTriggerUpdate = true)
        {
            try
            {
                if (assigneeIds is not { Count: > 0 })
                {
                    return conversation;
                }

                var collaboratorsDeleted = new HashSet<AdditionalAssignee>();

                if (conversation.AdditionalAssignees is not { Count: > 0 })
                {
                    conversation.AdditionalAssignees = await _appDbContext.ConversationAdditionalAssignees
                        .Where(x => x.ConversationId == conversation.Id)
                        .ToListAsync();
                }

                foreach (var assigneeId in assigneeIds)
                {
                    var collaboratorToDelete = await _appDbContext.ConversationAdditionalAssignees.FirstOrDefaultAsync(
                        x => x.ConversationId == conversation.Id
                             && x.AssigneeId == assigneeId);

                    if (collaboratorToDelete != null)
                    {
                        _appDbContext.ConversationAdditionalAssignees
                            .RemoveRange(collaboratorToDelete);
                        conversation.ModifiedAt = DateTime.UtcNow;

                        await _appDbContext.SaveChangesAsync();

                        conversation.AdditionalAssignees = await _appDbContext.ConversationAdditionalAssignees
                            .Where(x => x.ConversationId == conversation.Id)
                            .ToListAsync();

                        collaboratorsDeleted.Add(collaboratorToDelete);
                    }

                    if (!isTriggerUpdate)
                    {
                        continue;
                    }

                    var staff = await _appDbContext.UserRoleStaffs
                        .Include(x => x.Identity)
                        .FirstOrDefaultAsync(
                            x => x.CompanyId == conversation.CompanyId
                                 && x.Id == assigneeId);

                    await _signalRService.SignalROnConversationAdditionalAssigneeChanged(
                        conversation.Id,
                        staff,
                        AdditionalAssigneeEvent.Removed);
                }

                // add to activity log
                if (collaboratorsDeleted.Any())
                {
                    await _userProfileHooks.OnConversationCollaboratorRemovedAsync(
                    conversation.CompanyId,
                    conversation.UserProfileId,
                    companyUser?.IdentityId,
                    async () =>
                    {
                        var collaboratorsRemoved = collaboratorsDeleted
                            .Select(
                                x => new StaffData(
                                    x.Assignee.Id.ToString(),
                                    x.Assignee.IdentityId,
                                    x.Assignee.Identity.DisplayName))
                            .ToList();
                        var conversationAllCollaborators = await _appDbContext.ConversationAdditionalAssignees
                            .Where(
                                x =>
                                    x.Assignee.CompanyId == conversation.CompanyId
                                    && x.ConversationId == conversation.Id)
                            .Select(
                                x => new StaffData(
                                    x.Assignee.Id.ToString(),
                                    x.Assignee.IdentityId,
                                    x.Assignee.Identity.DisplayName))
                            .ToListAsync();
                        return new OnConversationCollaboratorRemovedData(
                            collaboratorsRemoved,
                            conversationAllCollaborators,
                            conversation.Id,
                            conversation.UserProfile.FirstName,
                            conversation.UserProfile.LastName);
                    });
                }

                return conversation;
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "RemoveAdditionalAssignees Error: {ExceptionMessage}",
                    ex.Message);

                return conversation;
            }
        }

        public async Task RemoveAssigneeAssignedTeamId(
            string companyId,
            long teamId,
            CreateNewTeamViewModel createNewTeamViewModel)
        {
            _logger.LogInformation(
                "{CompanyId} removed {@StaffIds} from team id {TeamId}",
                companyId,
                createNewTeamViewModel.StaffIds,
                teamId);

            var conversationAssigneeIds = await _appDbContext.UserRoleStaffs
                .Where(
                    s => s.CompanyId == companyId &&
                         createNewTeamViewModel.StaffIds.Contains(s.IdentityId)).Select(s => s.Id)
                .ToListAsync();

            var companyAssignedTeamCustomField =
                await GetCompanyCustomFieldByNameAsync(companyId, AssignedTeamFieldName);

            var affectedUserProfiles = await _appDbContext.Conversations
                .AsNoTracking()
                .Where(
                    conversation =>
                        conversation.CompanyId == companyId &&
                        (conversation.AssigneeId.HasValue && conversationAssigneeIds.Contains(conversation.AssigneeId.Value)) &&
                        (conversation.AssignedTeamId.HasValue && conversation.AssignedTeamId.Value == teamId))
                .Select(c => c.UserProfile)
                .ToListAsync();

            if (!string.IsNullOrWhiteSpace(companyAssignedTeamCustomField?.Id))
            {
                affectedUserProfiles.ForEach(
                    profile =>
                    {
                        BackgroundJob.Enqueue<IUserProfileService>(
                            svc =>
                                svc.SetFieldValueByFieldNameSafe(
                                    profile,
                                    companyAssignedTeamCustomField.FieldName,
                                    null,
                                    true));
                    });
            }
        }

        public async Task SetAssigneeNewAssignedTeamId(
            string companyId,
            long teamId,
            CreateNewTeamViewModel createNewTeamViewModel)
        {
            _logger.LogInformation(
                "{CompanyId} added {@StaffIds} to team id {TeamId}",
                companyId,
                createNewTeamViewModel.StaffIds,
                teamId);

            var conversationAssigneeIds = await _appDbContext.UserRoleStaffs
                .Where(
                    s => s.CompanyId == companyId &&
                         createNewTeamViewModel.StaffIds.Contains(s.IdentityId)).Select(s => s.Id)
                .ToListAsync();

            var companyAssignedTeamCustomField =
                await GetCompanyCustomFieldByNameAsync(companyId, AssignedTeamFieldName);

            var affectedUserProfiles = await _appDbContext.Conversations
                .AsNoTracking()
                .Where(
                    conversation =>
                        conversation.CompanyId == companyId &&
                        (conversation.AssigneeId.HasValue && conversationAssigneeIds.Contains(conversation.AssigneeId.Value)) &&
                        !conversation.AssignedTeamId.HasValue)
                .Select(c => c.UserProfile)
                .ToListAsync();

            if (!string.IsNullOrWhiteSpace(companyAssignedTeamCustomField?.Id))
            {
                affectedUserProfiles.ForEach(
                    profile =>
                    {
                        BackgroundJob.Enqueue<IUserProfileService>(
                            svc =>
                                svc.SetFieldValueByFieldNameSafe(
                                    profile,
                                    companyAssignedTeamCustomField.FieldName,
                                    teamId.ToString(),
                                    true));
                    });
            }
        }

        private Task<CompanyCustomUserProfileField> GetCompanyCustomFieldByNameAsync(
            string companyId,
            string customFieldName)
            => _appDbContext.CompanyCustomUserProfileFields
                .AsNoTracking()
                .FirstOrDefaultAsync(
                    field =>
                        field.CompanyId == companyId &&
                        field.FieldName.ToLower() == customFieldName);
    }
}