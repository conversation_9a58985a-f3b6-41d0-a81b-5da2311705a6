﻿using FluentAssertions;
using Moq;
using Travis_backend.CommonDomain.Repositories;
using Travis_backend.CommonDomain.Services;
using Travis_backend.Helpers;

namespace Sleekflow.Core.Tests.UserProfiles;

public class PhoneNumberTest
{
    [SetUp]
    public void Setup()
    {
    }

    [Test]
    public void Test_NormalizeWhatsappPhoneNumber()
    {
        //HK
        PhoneNumberHelper.NormalizeWhatsappPhoneNumber("85266668888").Should().Be("85266668888");

        //Brazil
        PhoneNumberHelper.NormalizeWhatsappPhoneNumber("556293500000").Should().Be("556293500000");
        PhoneNumberHelper.NormalizeWhatsappPhoneNumber("5562993500000").Should().Be("5562993500000");

        // Mexico Number
        PhoneNumberHelper.NormalizeWhatsappPhoneNumber("+529842793533").Should().Be("529842793533");
        PhoneNumberHelper.NormalizeWhatsappPhoneNumber("+5219842793533").Should().Be("529842793533");
    }

    [Test]
    public void Test_NormalizeWhatsappPhoneNumber2()
    {
        //HK
        PhoneNumberHelper.GetValidPhoneNumberWithCountryCodeForImportContact("+852 67385722").Should().Be("85267385722");
        PhoneNumberHelper.GetValidPhoneNumberWithCountryCodeForImportContact("85267385722","Hong Kong SAR").Should().Be("85267385722");
        PhoneNumberHelper.GetValidPhoneNumberWithCountryCodeForImportContact("85267385722","Hong Kong SAR", "HK").Should().Be("85267385722");
        PhoneNumberHelper.GetValidPhoneNumberWithCountryCodeForImportContact("67385722", "Hong Kong SAR").Should().Be("85267385722");
        PhoneNumberHelper.GetValidPhoneNumberWithCountryCodeForImportContact("67385722", null, "HK").Should().Be("85267385722");
        PhoneNumberHelper.GetValidPhoneNumberWithCountryCodeForImportContact("67385722").Should().Be(null);

        // SG
        PhoneNumberHelper.GetValidPhoneNumberWithCountryCodeForImportContact("65 9027 1880").Should().Be("6590271880");
        PhoneNumberHelper.GetValidPhoneNumberWithCountryCodeForImportContact("6590271880", "Singapore").Should().Be("6590271880");
        PhoneNumberHelper.GetValidPhoneNumberWithCountryCodeForImportContact("6590271880", null, "SG").Should().Be("6590271880");
        PhoneNumberHelper.GetValidPhoneNumberWithCountryCodeForImportContact("6590271880").Should().Be("6590271880");
        PhoneNumberHelper.GetValidPhoneNumberWithCountryCodeForImportContact("90271880").Should().Be(null);

        // MY
        PhoneNumberHelper.GetValidPhoneNumberWithCountryCodeForImportContact("+60 12 687 8486").Should().Be("60126878486");
        PhoneNumberHelper.GetValidPhoneNumberWithCountryCodeForImportContact("60126878486", "Malaysia").Should().Be("60126878486");
        PhoneNumberHelper.GetValidPhoneNumberWithCountryCodeForImportContact("60126878486", null, "MY").Should().Be("60126878486");
        PhoneNumberHelper.GetValidPhoneNumberWithCountryCodeForImportContact("60126878486").Should().Be("60126878486");
        PhoneNumberHelper.GetValidPhoneNumberWithCountryCodeForImportContact("126878486").Should().Be(null);

        // ID
        PhoneNumberHelper.GetValidPhoneNumberWithCountryCodeForImportContact("+62 812 8864 6715").Should().Be("6281288646715");
        PhoneNumberHelper.GetValidPhoneNumberWithCountryCodeForImportContact("6281288646715", "Indonesia").Should().Be("6281288646715");
        PhoneNumberHelper.GetValidPhoneNumberWithCountryCodeForImportContact("6281288646715", null, "ID").Should().Be("6281288646715");
        PhoneNumberHelper.GetValidPhoneNumberWithCountryCodeForImportContact("6281288646715").Should().Be("6281288646715");

        //Brazil
        PhoneNumberHelper.GetValidPhoneNumberWithCountryCodeForImportContact("+55 62 993500000", "Brazil").Should().Be("5562993500000");

        PhoneNumberHelper.GetValidPhoneNumberWithCountryCodeForImportContact("5562993500000", "Brazil").Should().Be("5562993500000");
        PhoneNumberHelper.GetValidPhoneNumberWithCountryCodeForImportContact("554896441186", "Brazil").Should().Be("554896441186");
        PhoneNumberHelper.GetValidPhoneNumberWithCountryCodeForImportContact("554891757696", "Brazil").Should().Be("554891757696");
        PhoneNumberHelper.GetValidPhoneNumberWithCountryCodeForImportContact("551132109876", "Brazil").Should().Be("551132109876");
        PhoneNumberHelper.GetValidPhoneNumberWithCountryCodeForImportContact("5562993500000").Should().Be("5562993500000");
        PhoneNumberHelper.GetValidPhoneNumberWithCountryCodeForImportContact("554896441186").Should().Be("554896441186");
        PhoneNumberHelper.GetValidPhoneNumberWithCountryCodeForImportContact("554891757696").Should().Be("554891757696");
        PhoneNumberHelper.GetValidPhoneNumberWithCountryCodeForImportContact("551132109876").Should().Be("551132109876");
        PhoneNumberHelper.GetValidPhoneNumberWithCountryCodeForImportContact("5562993500000", null, "BR").Should().Be("5562993500000");
        PhoneNumberHelper.GetValidPhoneNumberWithCountryCodeForImportContact("554896441186", null, "BR").Should().Be("554896441186");
        PhoneNumberHelper.GetValidPhoneNumberWithCountryCodeForImportContact("554891757696", null, "BR").Should().Be("554891757696");
        PhoneNumberHelper.GetValidPhoneNumberWithCountryCodeForImportContact("551132109876", null, "BR").Should().Be("551132109876");
    }

    [Test]
    public void Test_IsValidPhoneNumber()
    {
        PhoneNumberHelper.IsValidPhoneNumberContainsCountryCode("23779506772").Should().BeTrue();
        PhoneNumberHelper.IsValidPhoneNumberContainsCountryCode("256726710960").Should().BeTrue();
        PhoneNumberHelper.IsValidPhoneNumberContainsCountryCode("242066735480").Should().BeTrue();
        PhoneNumberHelper.IsValidPhoneNumberContainsCountryCode("22597753461").Should().BeTrue();
    }


    [Test]
    public void Test_PhoneNumberCounty()
    {
        GetCountryCode("79162685576").Should().Be("Russia");
        GetCountryCode("78162685576").Should().Be("Russia");
        GetCountryCode("77012345678").Should().Be("Kazakhstan");
        GetCountryCode("76012345678").Should().Be("Kazakhstan");
        GetCountryCode("74162685576").Should().Be("Russia");
        GetCountryCode("73162685576").Should().Be("Russia");
        GetCountryCode("72162685576").Should().Be("Russia");
    }

    private static string GetCountryCode(string phoneNumberText)
    {
        var phoneNumberUtil = PhoneNumbers.PhoneNumberUtil.GetInstance();
        var phoneNumber = phoneNumberUtil.Parse($"+{phoneNumberText}", null);
        var countryCode = PhoneNumberHelper.GetCountryCode(phoneNumber);

        var _countryService = new CountryService(
            new Mock<ICountryRepository>().Object);

        var country = _countryService.GetCountryEnglishNameByCountryCode(countryCode);

        return country;
    }

    [Test]
    public void Test_NormalizePhoneNumber()
    {
        PhoneNumberHelper.NormalizePhoneNumber("9710526499077").Should().Be("971526499077");
        PhoneNumberHelper.NormalizePhoneNumber("971526499077").Should().Be("971526499077");
        PhoneNumberHelper.NormalizePhoneNumber("6309364442859").Should().Be("639364442859");
        PhoneNumberHelper.NormalizePhoneNumber("4407931234567").Should().Be("447931234567");

        PhoneNumberHelper.NormalizeWhatsappPhoneNumber("9710526499077").Should().Be("971526499077");
        PhoneNumberHelper.NormalizeWhatsappPhoneNumber("971526499077").Should().Be("971526499077");
        PhoneNumberHelper.NormalizeWhatsappPhoneNumber("6309364442859").Should().Be("639364442859");
        PhoneNumberHelper.NormalizeWhatsappPhoneNumber("4407931234567").Should().Be("447931234567");

        PhoneNumberHelper.NormalizeWhatsappPhoneNumber("559174009371").Should().Be("559174009371");
        PhoneNumberHelper.NormalizeWhatsappPhoneNumber("5591343040").Should().Be("5591343040");

        PhoneNumberHelper.NormalizeWhatsappPhoneNumber("5548998298050").Should().Be("5548998298050");
        PhoneNumberHelper.NormalizeWhatsappPhoneNumber("554898298050").Should().Be("554898298050");
    }

    [Test]
    public void Test_DEVS_9907()
    {
        // Test with phone numbers in the ticket
        PhoneNumberHelper.GetValidPhoneNumberWithCountryCodeForImportContact("61028965", "Hong Kong SAR", "HK").Should().Be("85261028965");
        PhoneNumberHelper.GetValidPhoneNumberWithCountryCodeForImportContact("54037651", "Hong Kong SAR", "HK").Should().Be("85254037651");
        PhoneNumberHelper.GetValidPhoneNumberWithCountryCodeForImportContact("64392585", "Hong Kong SAR", "HK").Should().Be("85264392585");
        PhoneNumberHelper.GetValidPhoneNumberWithCountryCodeForImportContact("93044770", "Hong Kong SAR", "HK").Should().Be("85293044770");

        // Test with different phone numbers
        PhoneNumberHelper.GetValidPhoneNumberWithCountryCodeForImportContact("61028966", "Hong Kong SAR", "HK").Should().Be("85261028966");
        PhoneNumberHelper.GetValidPhoneNumberWithCountryCodeForImportContact("54037652", "Hong Kong SAR", "HK").Should().Be("85254037652");
        PhoneNumberHelper.GetValidPhoneNumberWithCountryCodeForImportContact("64392586", "Hong Kong SAR", "HK").Should().Be("85264392586");
        PhoneNumberHelper.GetValidPhoneNumberWithCountryCodeForImportContact("93044771", "Hong Kong SAR", "HK").Should().Be("85293044771");

        // Test with phone numbers and no country name
        PhoneNumberHelper.GetValidPhoneNumberWithCountryCodeForImportContact("61028967", null, "HK").Should().Be("85261028967");
        PhoneNumberHelper.GetValidPhoneNumberWithCountryCodeForImportContact("54037653", null, "HK").Should().Be("85254037653");

        // Test with phone numbers and no country code
        PhoneNumberHelper.GetValidPhoneNumberWithCountryCodeForImportContact("61028968", "Hong Kong SAR", null).Should().Be("85261028968");
        PhoneNumberHelper.GetValidPhoneNumberWithCountryCodeForImportContact("54037654", "Hong Kong SAR", null).Should().Be("85254037654");

        // Test with phone numbers, no country name, and no country code
        PhoneNumberHelper.GetValidPhoneNumberWithCountryCodeForImportContact("61028969").Should().Be(null);
        PhoneNumberHelper.GetValidPhoneNumberWithCountryCodeForImportContact("54037655").Should().Be(null);
    }


    // Number that that is not in E164 format
    [TestCase("+1855) 737-0036", "+18557370036")]
    [TestCase("+****************", "+18557370036")]
    [TestCase("+1973) 791-9109", "+19737919109")]

    // Number that that is not in E164 format
    [TestCase("+1855) 737-0036", "+18557370036")]
    [TestCase("+****************", "+18557370036")]
    [TestCase("+1973) 791-9109", "+19737919109")]
    [TestCase("******-555-0147", "+12025550147")]
    [TestCase("+81 3-1234-5678", "+81312345678")]

    // UAE Number
    [TestCase("9710526499077", "+971526499077")]
    [TestCase("971526499077", "+971526499077")]

    // UK Number
    [TestCase("+44 20 7946 0976", "+442079460976")]
    [TestCase("4407931234567", "+447931234567")]

    // Philippines Number
    [TestCase("6309364442859", "+639364442859")]

    // Brazil Number - without 9 or with 9
    [TestCase("559174009371", "+559174009371")]
    [TestCase("5591343040", "+5591343040")]
    [TestCase("554898298050", "+554898298050")]
    [TestCase("5548998298050", "+5548998298050")]
    [TestCase("+55 62 993500000", "+5562993500000")]

    // MY Number
    // Invalid MY number
    [TestCase("060169019983", "+060169019983")]

    // Mexico Number
    [TestCase("5219842793533", "+529842793533")]
    [TestCase("529842793533", "+529842793533")]

    // HK Number
    [TestCase("85266668888", "+85266668888")]

    // Other Channel Addresses - remain the same
    [TestCase("wwhatsApp", "wwhatsApp")]
    [TestCase("YiPlay", "YiPlay")]
    [TestCase("T2408050728", "T2408050728")]
    [TestCase("undefined", "undefined")]
    [TestCase("+undefined", "+undefined")]
    [TestCase("+NA998934511198", "+NA998934511198")]
    // e.g. wwhatsApp, YiPlay, T2408050728, +undefined, +NA998934511198
    // [TestCase("InvalidNumber", null)]
    // [TestCase("", "US", null, ExpectedException = typeof(ArgumentException))]
    // [TestCase(null, "US", null, ExpectedException = typeof(ArgumentException))]

    public void ToE164Format_Test(string phoneNumber, string expected)
    {
        var result = PhoneNumberHelper.ToE164Format(phoneNumber);
        Assert.That(result, Is.EqualTo(expected));
    }


    [TestCase("******-555-0147", false)]
    [TestCase("+44 20 7946 0976", false)]
    [TestCase("+81 3-1234-5678", false)]
    [TestCase("9710526499077", false)]
    [TestCase("971526499077", false)]
    [TestCase("6309364442859", false)]
    [TestCase("4407931234567", false)]
    [TestCase("559174009371", false)]
    [TestCase("5591343040", false)]
    [TestCase("5548998298050", false)]
    [TestCase("554898298050", false)]
    [TestCase("85266668888", false)]
    [TestCase("+1855) 737-0036", false)]
    [TestCase("+****************", false)]
    [TestCase("+1973) 791-9109", false)]
    [TestCase("wwhatsApp", false)]
    [TestCase("YiPlay", false)]
    [TestCase("T2408050728", false)]
    [TestCase("undefined", false)]
    [TestCase("+undefined", false)]
    [TestCase("+NA998934511198", false)]
    [TestCase("+9710526499077", false)]
    [TestCase("+971526499077", true)]

    public void IsValidE164Format_Test(string phoneNumber, bool expected)
    {
        var result = PhoneNumberHelper.IsValidE164FormatPhoneNumber(phoneNumber);
        Assert.That(result, Is.EqualTo(expected));
    }

    [TestCase("******-555-0147", false)]
    [TestCase("+44 20 7946 0976", false)]
    [TestCase("+81 3-1234-5678", false)]
    [TestCase("9710526499077", false)]
    [TestCase("971526499077", false)]
    [TestCase("6309364442859", false)]
    [TestCase("4407931234567", false)]
    [TestCase("559174009371", false)]
    [TestCase("5591343040", false)]
    [TestCase("5548998298050", false)]
    [TestCase("554898298050", false)]
    [TestCase("85266668888", false)]
    [TestCase("+1855) 737-0036", false)]
    [TestCase("+****************", false)]
    [TestCase("+1973) 791-9109", false)]
    [TestCase("wwhatsApp", false)]
    [TestCase("YiPlay", false)]
    [TestCase("T2408050728", false)]
    [TestCase("undefined", false)]
    [TestCase("+undefined", false)]
    [TestCase("+NA998934511198", false)]
    [TestCase("+9710526499077", true)]
    [TestCase("+971526499077", true)]
    public void IsE164Format_Test(string phoneNumber, bool expected)
    {
        var result = PhoneNumberHelper.IsE164FormatPhoneNumber(phoneNumber);
        Assert.That(result, Is.EqualTo(expected));
    }
}
