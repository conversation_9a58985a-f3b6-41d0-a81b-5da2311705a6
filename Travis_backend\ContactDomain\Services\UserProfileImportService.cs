﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using Hangfire;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Travis_backend.BackgroundTaskServices.Attributes;
using Travis_backend.CommonDomain.Services;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.CompanyDomain.Services;
using Travis_backend.ContactDomain.Constants;
using Travis_backend.ContactDomain.Models;
using Travis_backend.ContactDomain.ViewModels;
using Travis_backend.ConversationDomain.Models;
using Travis_backend.ConversationDomain.Services;
using Travis_backend.ConversationServices;
using Travis_backend.Database;
using Travis_backend.Enums;
using Travis_backend.FlowHubs;
using Travis_backend.FlowHubs.Models;
using Travis_backend.Helpers;
using Travis_backend.MessageDomain.Models;
using Travis_backend.MessageDomain.ViewModels;
using Travis_backend.SignalR;

namespace Travis_backend.ContactDomain.Services;

public interface IUserProfileImportService
{
    Task ImportUserProfile(
        long importToListId,
        string companyId,
        long staffId,
        ImportSpreadsheet importSpreadsheet,
        bool isImportIntoList,
        bool isTriggerAutomation = true);

    Task ImportUserProfileBackground(
        long importToListId,
        string companyId,
        long staffId,
        ImportSpreadsheet importSpreadsheet,
        bool isImportIntoList,
        bool isTriggerAutomation = true,
        Func<int, ValueTask> onComplete = null,
        long? importContactToListRecordId = null);

    Task ImportUserProfileBackgroundV2(
        long importToListId,
        string companyId,
        long staffId,
        ImportSpreadsheet importSpreadsheet,
        bool isImportIntoList,
        bool isTriggerAutomation = true,
        Func<int, ValueTask> onComplete = null,
        long? importContactToListRecordId = null);

    Task BulkImportUserProfileBackground(
        long importToListId,
        string companyId,
        long staffId,
        ImportSpreadsheet importSpreadsheet,
        bool isImportIntoList,
        Func<int, ValueTask> onComplete = null,
        long? importContactToListRecordId = null);
}

public class UserProfileImportService : IUserProfileImportService
{
    private readonly ApplicationDbContext _appDbContext;
    private readonly ILogger<UserProfileImportService> _logger;
    private readonly ISignalRService _signalRService;
    private readonly ICompanyUsageService _companyUsageService;
    private readonly IConversationHashtagService _conversationHashtagService;
    private readonly IConversationAssigneeService _conversationAssigneeService;
    private readonly IUserProfileService _userProfileService;
    private readonly IConversationMessageService _conversationMessageService;
    private readonly IUserProfileHooks _userProfileHooks;
    private readonly ICountryService _countryService;

    public UserProfileImportService(
        ApplicationDbContext appDbContext,
        ILogger<UserProfileImportService> logger,
        ISignalRService signalRService,
        ICompanyUsageService companyUsageService,
        IConversationHashtagService conversationHashtagService,
        IConversationAssigneeService conversationAssigneeService,
        IUserProfileService userProfileService,
        IConversationMessageService conversationMessageService,
        IUserProfileHooks userProfileHooks,
        ICountryService countryService)
    {
        _appDbContext = appDbContext;
        _logger = logger;
        _signalRService = signalRService;
        _companyUsageService = companyUsageService;
        _conversationHashtagService = conversationHashtagService;
        _conversationAssigneeService = conversationAssigneeService;
        _userProfileService = userProfileService;
        _conversationMessageService = conversationMessageService;
        _userProfileHooks = userProfileHooks;
        _countryService = countryService;
    }

    public async Task ImportUserProfile(
        long importToListId,
        string companyId,
        long staffId,
        ImportSpreadsheet importSpreadsheet,
        bool isImportIntoList,
        bool isTriggerAutomation = true)
    {
        await ImportUserProfileBackground(
            importToListId,
            companyId,
            staffId,
            importSpreadsheet,
            isImportIntoList,
            isTriggerAutomation);
    }

    public async Task ImportUserProfileBackground(
        long importToListId,
        string companyId,
        long staffId,
        ImportSpreadsheet importSpreadsheet,
        bool isImportIntoList,
        bool isTriggerAutomation = true,
        Func<int, ValueTask> onComplete = null,
        long? importContactToListRecordId = null)
    {
        var newImport = await _appDbContext.CompanyImportContactHistories
            .AsSplitQuery()
            .Include(x => x.ImportedUserProfiles)
            .FirstOrDefaultAsync(
                x =>
                    x.Id == importToListId
                    && x.CompanyId == companyId);

        var importContactToListRecord =
            isImportIntoList
            && importContactToListRecordId != null ?
                await _appDbContext.CompanyImportContactToListRecords
                    .FirstOrDefaultAsync(r => r.Id == importContactToListRecordId) :
                null;

        var companyUser = await _appDbContext.UserRoleStaffs
            .Include(x => x.Company)
            .Include(x => x.Identity)
            .FirstOrDefaultAsync(x => x.Id == staffId);

        try
        {
            var savedHeaders = new List<ImportHeader>(importSpreadsheet.headers);
            var failedImportList = new List<ImportFailedRecord>();
            var executedUserProfileIds = new HashSet<string>();

            var startIndex = isImportIntoList ?
                importContactToListRecord?.ImportIndex ?? 0 :
                newImport.ImportIndex;

            for (int i = startIndex; i < importSpreadsheet.records.Count; i++)
            {
                var record = importSpreadsheet.records[i];

                importSpreadsheet.headers = new List<ImportHeader>(savedHeaders);

                var importObject = new UserProfileService.ImportUserProfileObject(
                    importSpreadsheet.headers,
                    record.fields);

                var contactId = importObject.GetValueFromList("contactid");
                var firstName = importObject.GetValueFromList("firstname");
                var lastName = importObject.GetValueFromList("lastname");
                var phoneNumber = importObject.GetValueFromList("phonenumber");
                var email = importObject.GetValueFromList("email");

                try
                {
                    if (!string.IsNullOrEmpty(phoneNumber))
                    {
                        var contactCountry = importObject.GetValueFromList("country");

                        if (string.IsNullOrEmpty(contactCountry))
                        {
                            contactCountry = companyUser.Company.CompanyCountry;
                        }

                        phoneNumber = PhoneNumberHelper.GetValidPhoneNumberWithCountryCodeForImportContact(
                            phoneNumber,
                            contactCountry);
                    }

                    phoneNumber = PhoneNumberHelper.FormatPhoneNumber(phoneNumber);
                    importObject.TrySetValue("phonenumber", phoneNumber);

                    UserProfile importedUserProfile = null;

                    if (!string.IsNullOrEmpty(contactId))
                    {
                        importedUserProfile = await _appDbContext.UserProfiles
                            .FirstOrDefaultAsync(
                                x =>
                                    x.CompanyId == companyUser.CompanyId
                                    && x.Id == contactId);
                    }

                    if (!string.IsNullOrEmpty(phoneNumber)
                        && importedUserProfile == null)
                    {
                        importedUserProfile = (await _userProfileService.GetUserProfilesByFields(
                            companyUser.CompanyId,
                            new List<Condition>
                            {
                                new Condition
                                {
                                    FieldName = "phonenumber",
                                    ConditionOperator = SupportedOperator.Equals,
                                    Values = new List<string>
                                    {
                                        phoneNumber
                                    },
                                    NextOperator = SupportedNextOperator.And
                                }
                            },
                            0,
                            1)).UserProfiles.FirstOrDefault();
                    }

                    // validate email: if valid, can set field value; if invalid, do not set field value
                    var isValidEmail =
                        !string.IsNullOrWhiteSpace(email)
                        && Regex.IsMatch(
                            email,
                            @"^[^@\s]+@[^@\s]+\.[^@\s]+$",
                            RegexOptions.IgnoreCase);

                    if (!isValidEmail)
                    {
                        email = null;
                        importObject.TrySetValue("email", null);
                    }

                    if (!string.IsNullOrEmpty(email)
                        && importedUserProfile == null)
                    {
                        importedUserProfile = (await _userProfileService.GetUserProfilesByFields(
                            companyUser.CompanyId,
                            new List<Condition>
                            {
                                new Condition
                                {
                                    FieldName = "email",
                                    ConditionOperator = SupportedOperator.Equals,
                                    Values = new List<string>
                                    {
                                        email
                                    },
                                    NextOperator = SupportedNextOperator.And
                                }
                            },
                            0,
                            1)).UserProfiles.FirstOrDefault();
                    }

                    var isNewImport = false;

                    if (importedUserProfile == null)
                    {
                        //var newProfile = new NewProfileViewModel
                        //{
                        //    UserProfileFields = new List<AddCustomFieldsViewModel>()
                        //};

                        //if (!string.IsNullOrEmpty(firstName))
                        //    newProfile.FirstName = firstName;
                        //if (!string.IsNullOrEmpty(email))
                        //    newProfile.Email = email;
                        //if (!string.IsNullOrEmpty(phoneNumber))
                        //    newProfile.WhatsAppPhoneNumber = phoneNumber;
                        //if (!string.IsNullOrEmpty(lastName))
                        //    newProfile.LastName = lastName;

                        //try
                        //{
                        //    if (!string.IsNullOrEmpty(newProfile.WhatsAppPhoneNumber) && !string.IsNullOrEmpty(newProfile.Email))
                        //    {
                        //        newImport.ImportIndex = i + 1;
                        //        await _appDbContext.SaveChangesAsync();
                        //        continue;
                        //    }
                        //    var addResult = await AddUserProfiles(companyId, null, new List<NewProfileViewModel> { newProfile }, false);
                        //    targetUserProfiles = addResult.FirstOrDefault();
                        //} catch (Exception ex)
                        //{
                        //    _logger.LogError(ex, ex.Message);

                        //    newImport.ImportIndex = i + 1;
                        //    await _appDbContext.SaveChangesAsync();
                        //    continue;
                        //}

                        var response = await _companyUsageService.GetCompanyContactUsageAsync(companyId);

                        if (response.TotalContactCount > response.MaximumContacts)
                        {
                            _logger.LogWarning(
                                "Company {CompanyId} exceeded contact limitation: maximum {MaximumContactCount} contacts",
                                companyId,
                                response.MaximumContacts);

                            newImport.Status = ImportStatus.Imported;
                            await _appDbContext.SaveChangesAsync();

                            return;
                            //throw new Exception($"Exceeded contact limitation: maximum {response.billingPeriodUsages.FirstOrDefault().BillRecord.SubscriptionPlan.MaximumContact} contacts");
                        }

                        try
                        {
                            var newAddedContact = await _userProfileService.AddUserProfiles(
                                companyId,
                                null,
                                new List<NewProfileViewModel>
                                {
                                    new NewProfileViewModel
                                    {
                                        FirstName = firstName,
                                        LastName = lastName,
                                        Email = email,
                                        PhoneNumber = phoneNumber
                                    }
                                },
                                recoveryTriggerContext: new UserProfileRecoveryTriggerContext(
                                    UpdateUserProfileTriggerSource.ContactImport,
                                    null));
                            importedUserProfile = newAddedContact.FirstOrDefault();

                            if (newAddedContact.Count == 0)
                            {
                                continue;
                            }

                            isNewImport = true;
                        }
                        catch (FormatException ex)
                        {
                            // DEVS-6382 Johnson is enhanced duplicate contact error message, but we didn't update the handling here
                            if (ex.Message.Contains(":"))
                            {
                                var messageParts = ex.Message.Split(':');
                                var userProfileId = messageParts[0];

                                importedUserProfile = await _appDbContext.UserProfiles
                                    .Where(x => x.CompanyId == companyUser.CompanyId && x.Id == userProfileId)
                                    .FirstOrDefaultAsync();
                            }
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(
                                ex,
                                "Import Id {ImportListId} Error. Company Id: {CompanyId}. {Message}",
                                importToListId,
                                companyId,
                                ex.Message);

                            if (ex.Message.Contains("no contact created", StringComparison.OrdinalIgnoreCase))
                            {
                                phoneNumber = importObject.GetValueFromList("phonenumber");
                                email = importObject.GetValueFromList("email");

                                throw new("Contact creation failed due to not having either valid phone number or valid email.");
                            }

                            throw new ("Something went wrong when importing this contact.");
                        }
                    }

                    BackgroundJob.Enqueue(
                        () => HandleUserProfilePostImportActionsV2(
                            companyId,
                            staffId,
                            importedUserProfile.Id,
                            importToListId,
                            isNewImport,
                            importObject._headers
                                .Select(header => new UserProfileImportFieldDto(
                                    header.HeaderName,
                                    importObject.GetValueFromList(header.HeaderName),
                                    header.importAction))
                                .GroupBy(x => x.FieldName)
                                .ToDictionary(
                                    g => g.Key.ToLower(),
                                    g => g.First()),
                            isTriggerAutomation));

                    if (!executedUserProfileIds.Contains(importedUserProfile.Id))
                    {
                        newImport.ImportedUserProfiles.Add(
                            new ImportedUserProfile
                            {
                                UserProfile = importedUserProfile
                            });
                    }

                    try
                    {
                        if (isNewImport)
                        {
                            newImport.ImportedCount += 1;
                            executedUserProfileIds.Add(importedUserProfile.Id);
                        }
                        else if (!executedUserProfileIds.Contains(importedUserProfile.Id))
                        {
                            newImport.UpdatedCount += 1;
                            executedUserProfileIds.Add(importedUserProfile.Id);
                        }

                        if (isImportIntoList
                            && importContactToListRecord != null)
                        {
                            importContactToListRecord.ImportIndex = i + 1;
                        }
                        else
                        {
                            newImport.ImportIndex = i + 1;
                        }

                        await _appDbContext.SaveChangesAsync();
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(
                            ex,
                            "Company Id: {CompanyId}. Import Error: {Message}",
                            companyId,
                            ex.Message);

                        newImport.FailedCount += 1;
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, ex.Message);

                    failedImportList.Add(
                        new ImportFailedRecord()
                        {
                            Row = i,
                            Email = email,
                            PhoneNumber = phoneNumber,
                            Name = $"{firstName} {lastName}",
                            ErrorMessage = ex.Message
                        });

                    newImport.FailedCount += 1;
                }

                if (onComplete != null)
                {
                    await onComplete(i + 1);
                }
            } // For loop end

            try
            {
                newImport.Status = ImportStatus.Imported;
                await _appDbContext.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Company Id: {CompanyId}. Import Error: {Message}", companyId, ex.Message);
            }

            await _signalRService.SignalROnImportCompleted(companyUser.Company, newImport);

            BackgroundJob.Enqueue<IEmailNotificationService>(
                x => x.ImportCompleted(newImport.Id, failedImportList));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Company Id: {CompanyId}. Import Error: {Message}", companyId, ex.Message);
            throw;
        }
    }

    public async Task ImportUserProfileBackgroundV2(
        long importToListId,
        string companyId,
        long staffId,
        ImportSpreadsheet importSpreadsheet,
        bool isImportIntoList,
        bool isTriggerAutomation = true,
        Func<int, ValueTask> onComplete = null,
        long? importContactToListRecordId = null)
    {
        var newImport = await _appDbContext.CompanyImportContactHistories
            .AsSplitQuery()
            .Include(x => x.ImportedUserProfiles)
            .FirstOrDefaultAsync(
                x =>
                    x.Id == importToListId
                    && x.CompanyId == companyId);

        var importContactToListRecord =
            isImportIntoList
            && importContactToListRecordId != null ?
                await _appDbContext.CompanyImportContactToListRecords
                    .FirstOrDefaultAsync(r => r.Id == importContactToListRecordId) :
                null;

        var companyUser = await _appDbContext.UserRoleStaffs
            .Include(x => x.Company)
            .Include(x => x.Identity)
            .FirstOrDefaultAsync(x => x.Id == staffId);

        // Preload validation data into memory
        var defaultWhatsappConfigs = await _appDbContext.ConfigWhatsAppConfigs
            .AsNoTracking()
            .Where(x => x.CompanyId == companyId)
            .ToListAsync();

        var defaultWhatsApp360DialogConfig = await _appDbContext.ConfigWhatsApp360DialogConfigs
            .AsNoTracking()
            .OrderBy(x => x.CreatedAt)
            .FirstOrDefaultAsync(x => x.CompanyId == companyId);

        var defaultWhatsAppCloudApiConfig = await _appDbContext.ConfigWhatsappCloudApiConfigs
            .AsNoTracking()
            .OrderBy(x => x.CreatedAt)
            .FirstOrDefaultAsync(x => x.CompanyId == companyId);

        var defaultSmsConfig = await _appDbContext.ConfigSMSConfigs
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.CompanyId == companyId);

        var defaultEmailConfig = await _appDbContext.CompanyCompanies
            .AsNoTracking()
            .Where(x => x.Id == companyId)
            .Select(x => x.EmailConfig)
            .FirstOrDefaultAsync();

        try
        {
            var savedHeaders = new List<ImportHeader>(importSpreadsheet.headers);
            var failedImportList = new List<ImportFailedRecord>();
            var executedUserProfileIds = new HashSet<string>();

            var startIndex = isImportIntoList ?
                importContactToListRecord?.ImportIndex ?? 0 :
                newImport.ImportIndex;

            for (int i = startIndex; i < importSpreadsheet.records.Count; i++)
            {
                var record = importSpreadsheet.records[i];

                importSpreadsheet.headers = new List<ImportHeader>(savedHeaders);

                var importObject = new UserProfileService.ImportUserProfileObject(
                    importSpreadsheet.headers,
                    record.fields);

                var contactId = importObject.GetValueFromList("contactid");
                var firstName = importObject.GetValueFromList("firstname");
                var lastName = importObject.GetValueFromList("lastname");
                var phoneNumber = importObject.GetValueFromList("phonenumber");
                var email = importObject.GetValueFromList("email");

                try
                {
                    if (!string.IsNullOrEmpty(phoneNumber))
                    {
                        var contactCountry = importObject.GetValueFromList("country");

                        if (string.IsNullOrEmpty(contactCountry))
                        {
                            contactCountry = companyUser.Company.CompanyCountry;
                        }

                        phoneNumber = PhoneNumberHelper.GetValidPhoneNumberWithCountryCodeForImportContact(
                            phoneNumber,
                            contactCountry);
                    }

                    phoneNumber = PhoneNumberHelper.FormatPhoneNumber(phoneNumber);
                    importObject.TrySetValue("phonenumber", phoneNumber);

                    UserProfile importedUserProfile = null;

                    if (!string.IsNullOrEmpty(contactId))
                    {
                        importedUserProfile = await _appDbContext.UserProfiles
                            .FirstOrDefaultAsync(
                                x =>
                                    x.CompanyId == companyUser.CompanyId
                                    && x.Id == contactId);
                    }

                    if (!string.IsNullOrEmpty(phoneNumber)
                        && importedUserProfile == null)
                    {
                        importedUserProfile = (await _userProfileService.GetUserProfilesByFields(
                            companyUser.CompanyId,
                            new List<Condition>
                            {
                                new Condition
                                {
                                    FieldName = "phonenumber",
                                    ConditionOperator = SupportedOperator.Equals,
                                    Values = new List<string>
                                    {
                                        phoneNumber
                                    },
                                    NextOperator = SupportedNextOperator.And
                                }
                            },
                            0,
                            1)).UserProfiles.FirstOrDefault();
                    }

                    // validate email: if valid, can set field value; if invalid, do not set field value
                    var isValidEmail =
                        !string.IsNullOrWhiteSpace(email)
                        && Regex.IsMatch(
                            email,
                            @"^[^@\s]+@[^@\s]+\.[^@\s]+$",
                            RegexOptions.IgnoreCase);

                    if (!isValidEmail)
                    {
                        email = null;
                        importObject.TrySetValue("email", null);
                    }

                    if (!string.IsNullOrEmpty(email)
                        && importedUserProfile == null)
                    {
                        importedUserProfile = (await _userProfileService.GetUserProfilesByFields(
                            companyUser.CompanyId,
                            new List<Condition>
                            {
                                new Condition
                                {
                                    FieldName = "email",
                                    ConditionOperator = SupportedOperator.Equals,
                                    Values = new List<string>
                                    {
                                        email
                                    },
                                    NextOperator = SupportedNextOperator.And
                                }
                            },
                            0,
                            1)).UserProfiles.FirstOrDefault();
                    }

                    var isNewImport = false;

                    if (importedUserProfile == null)
                    {
                        var response = await _companyUsageService.GetCompanyContactUsageAsync(companyId);

                        if (response.TotalContactCount > response.MaximumContacts)
                        {
                            _logger.LogWarning(
                                "Company {CompanyId} exceeded contact limitation: maximum {MaximumContactCount} contacts",
                                companyId,
                                response.MaximumContacts);

                            newImport.Status = ImportStatus.Imported;
                            await _appDbContext.SaveChangesAsync();

                            return;
                        }

                        try
                        {
                            var newAddedContact = await _userProfileService.AddUserProfilesV2(
                                companyId,
                                null,
                                new List<NewProfileViewModel>
                                {
                                    new NewProfileViewModel
                                    {
                                        FirstName = firstName,
                                        LastName = lastName,
                                        Email = email,
                                        PhoneNumber = phoneNumber
                                    }
                                },
                                defaultWhatsappConfigs,
                                defaultWhatsApp360DialogConfig,
                                defaultWhatsAppCloudApiConfig,
                                defaultSmsConfig,
                                defaultEmailConfig,
                                recoveryTriggerContext: new UserProfileRecoveryTriggerContext(
                                    UpdateUserProfileTriggerSource.ContactImport,
                                    null));
                            importedUserProfile = newAddedContact.FirstOrDefault();

                            if (newAddedContact.Count == 0)
                            {
                                continue;
                            }

                            isNewImport = true;
                        }
                        catch (FormatException ex)
                        {
                            // DEVS-6382 Johnson is enhanced duplicate contact error message, but we didn't update the handling here
                            if (ex.Message.Contains(":"))
                            {
                                var messageParts = ex.Message.Split(':');
                                var userProfileId = messageParts[0];

                                importedUserProfile = await _appDbContext.UserProfiles
                                    .Where(x => x.CompanyId == companyUser.CompanyId && x.Id == userProfileId)
                                    .FirstOrDefaultAsync();
                            }
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(
                                ex,
                                "Import Id {ImportListId} Error. Company Id: {CompanyId}. {Message}",
                                importToListId,
                                companyId,
                                ex.Message);

                            if (ex.Message.Contains("no contact created", StringComparison.OrdinalIgnoreCase))
                            {
                                phoneNumber = importObject.GetValueFromList("phonenumber");
                                email = importObject.GetValueFromList("email");

                                throw new("Contact creation failed due to not having either valid phone number or valid email.");
                            }

                            throw new ("Something went wrong when importing this contact.");
                        }
                    }

                    BackgroundJob.Enqueue(
                        () => HandleUserProfilePostImportActionsV2(
                            companyId,
                            staffId,
                            importedUserProfile.Id,
                            importToListId,
                            isNewImport,
                            importObject._headers
                                .Select(header => new UserProfileImportFieldDto(
                                    header.HeaderName,
                                    importObject.GetValueFromList(header.HeaderName),
                                    header.importAction))
                                .GroupBy(x => x.FieldName)
                                .ToDictionary(
                                    g => g.Key.ToLower(),
                                    g => g.First()),
                            isTriggerAutomation));

                    if (!executedUserProfileIds.Contains(importedUserProfile.Id))
                    {
                        newImport.ImportedUserProfiles.Add(
                            new ImportedUserProfile
                            {
                                UserProfile = importedUserProfile
                            });
                    }

                    try
                    {
                        if (isNewImport)
                        {
                            newImport.ImportedCount += 1;
                            executedUserProfileIds.Add(importedUserProfile.Id);
                        }
                        else if (!executedUserProfileIds.Contains(importedUserProfile.Id))
                        {
                            newImport.UpdatedCount += 1;
                            executedUserProfileIds.Add(importedUserProfile.Id);
                        }

                        if (isImportIntoList
                            && importContactToListRecord != null)
                        {
                            importContactToListRecord.ImportIndex = i + 1;
                        }
                        else
                        {
                            newImport.ImportIndex = i + 1;
                        }

                        await _appDbContext.SaveChangesAsync();
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(
                            ex,
                            "Company Id: {CompanyId}. Import Error: {Message}",
                            companyId,
                            ex.Message);

                        newImport.FailedCount += 1;
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, ex.Message);

                    failedImportList.Add(
                        new ImportFailedRecord()
                        {
                            Row = i,
                            Email = email,
                            PhoneNumber = phoneNumber,
                            Name = $"{firstName} {lastName}",
                            ErrorMessage = ex.Message
                        });

                    newImport.FailedCount += 1;
                }

                if (onComplete != null)
                {
                    await onComplete(i + 1);
                }
            } // For loop end

            try
            {
                newImport.Status = ImportStatus.Imported;
                await _appDbContext.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Company Id: {CompanyId}. Import Error: {Message}", companyId, ex.Message);
            }

            await _signalRService.SignalROnImportCompleted(companyUser.Company, newImport);

            BackgroundJob.Enqueue<IEmailNotificationService>(
                x => x.ImportCompleted(newImport.Id, failedImportList));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Company Id: {CompanyId}. Import Error: {Message}", companyId, ex.Message);
            throw;
        }
    }

    public async Task BulkImportUserProfileBackground(
        long importToListId,
        string companyId,
        long staffId,
        ImportSpreadsheet importSpreadsheet,
        bool isImportIntoList,
        Func<int, ValueTask> onComplete = null,
        long? importContactToListRecordId = null)
    {
        var importContactHistory = await _appDbContext.CompanyImportContactHistories
            .AsSplitQuery()
            .Include(x => x.ImportedUserProfiles)
            .Include(x => x.ImportedFrom.Identity)
            .FirstOrDefaultAsync(
                x =>
                    x.Id == importToListId
                    && x.CompanyId == companyId);

        // Make sure the company has enough contact capacity to import.
        // Peter DEVS-9055: This is a temporary solution to prevent the company from exceeding the contact limit.
        var response = await _companyUsageService.GetCompanyContactUsageAsync(companyId);
        if (response.TotalContactCount + importSpreadsheet.records.Count > response.MaximumContacts)
        {
            _logger.LogWarning(
                "Company {CompanyId} exceeded contact limitation: maximum {MaximumContactCount} contacts",
                companyId,
                response.MaximumContacts);
            importContactHistory.Status = ImportStatus.Imported;
            await _appDbContext.SaveChangesAsync();
            return;
        }

        var importContactToListRecord =
            isImportIntoList && importContactToListRecordId != null
                ? await _appDbContext.CompanyImportContactToListRecords
                    .FirstOrDefaultAsync(r => r.Id == importContactToListRecordId)
                : null;

        var companyUser = await _appDbContext.UserRoleStaffs
            .Include(x => x.Company)
            .Include(x => x.Identity)
            .FirstOrDefaultAsync(x => x.Id == staffId);

        var result = new List<NewProfileViewModel>();

        var savedHeaders = new List<ImportHeader>(importSpreadsheet.headers);
        var executedUserProfileIds = new HashSet<string>();
        var startIndex = isImportIntoList ?
            importContactToListRecord?.ImportIndex ?? 0 :
            importContactHistory.ImportIndex;

        try
        {
            for (int i = startIndex; i < importSpreadsheet.records.Count; i++)
            {
                var importObject = new UserProfileService.ImportUserProfileObject(
                    importSpreadsheet.headers,
                    importSpreadsheet.records[i].fields);

                var defaultHeaderList = new List<string>
                {
                    "firstname", "lastname", "phonenumber", "email"
                };

                var firstName = importObject.GetValueFromList("firstname");
                var lastName = importObject.GetValueFromList("lastname");
                var phoneNumber = importObject.GetValueFromList("phonenumber");
                var email = importObject.GetValueFromList("email");

                var newProfileViewModel = new NewProfileViewModel()
                {
                    FirstName = firstName,
                    LastName = lastName,
                    WhatsAppPhoneNumber = PhoneNumberHelper.FormatPhoneNumber(phoneNumber),
                    Email = email,
                };

                foreach (var header in importObject._headers.Where(
                             x => !defaultHeaderList.Contains(x.HeaderName.ToLower())))
                {
                    var value = importObject.GetValueFromList(header.HeaderName);

                    if (!string.IsNullOrWhiteSpace(value))
                    {
                        newProfileViewModel.UserProfileFields.Add(
                            new AddCustomFieldsViewModel()
                            {
                                CustomFieldName = header.HeaderName, CustomValue = value
                            });
                    }
                }

                if (importObject._headers.All(x => x.HeaderName.ToLower() != "subscriber"))
                {
                    newProfileViewModel.UserProfileFields.Add(
                        new AddCustomFieldsViewModel()
                        {
                            CustomFieldName = "subscriber", CustomValue = "true"
                        });
                }

                result.Add(newProfileViewModel);
            } // For loop end

            var (existingUserProfileIds, newUserProfiles) = await PrepareNewUserProfilesAsync(
                companyId,
                result);

            await BulkInsertUserProfilesAsync(
                companyId,
                newUserProfiles,
                importContactHistory,
                isImportIntoList,
                importContactToListRecord,
                onComplete);

            // The implicit behaviour is that contacts are imported as individual contacts
            // when the import name is not provided. Only supported for Express/Bulk Import.
            if (!string.IsNullOrEmpty(importContactHistory.ImportName))
            {
                await BulkInsertUserProfilesToListAsync(
                    companyId,
                    existingUserProfileIds,
                    importContactHistory,
                    importContactToListRecord,
                    onComplete);
            }

            try
            {
                importContactHistory.Status = ImportStatus.Imported;
                await _appDbContext.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Company Id: {CompanyId}. Import Error: {Message}", companyId, ex.Message);
            }

            await _signalRService.SignalROnImportCompleted(companyUser.Company, importContactHistory);

            // Bulk import has no detailed informaton of failed imported contacts.
            BackgroundJob.Enqueue<IEmailNotificationService>(
                x => x.BulkImportCompleted(
                    importContactHistory.ImportedFrom.Identity.Email,
                    importContactHistory.ImportedFrom.Identity.DisplayName,
                    importContactHistory.ImportName,
                    importContactHistory.ImportedCount,
                    importContactHistory.FailedCount));

            // Remove list once import is completed if import name is null or empty.
            // The implicit behaviour is that contacts are imported as individual contacts
            // when the import name is not provided. Only supported for Express/Bulk Import.
            if (string.IsNullOrEmpty(importContactHistory.ImportName))
            {
                try
                {
                    await _userProfileService.DeleteContactList(
                        companyId,
                        staffId,
                        new GroupListViewModel
                        {
                            ListIds = new List<long>
                            {
                                importContactHistory.Id
                            }
                        });
                }
                catch (Exception ex)
                {
                    _logger.LogError(
                        ex,
                        "Company Id: {CompanyId}. Failed to remove import list for individual contacts: {Message}",
                        companyId,
                        ex.Message);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Company Id: {CompanyId}. Import Error: {Message}", companyId, ex.Message);
            throw;
        }
    }

    private async Task<(List<string> ExistingUserProfileIds, List<UserProfile> NewUserProfiles)> PrepareNewUserProfilesAsync(
        string companyId,
        List<NewProfileViewModel> newProfileViewModels)
    {
        var userProfileCustomFields = await _appDbContext.CompanyCustomUserProfileFields
            .AsNoTracking()
            .Where(x => x.CompanyId == companyId)
            .ToListAsync();

        var staffs = await _appDbContext.UserRoleStaffs
            .AsNoTracking()
            .Where(x => x.CompanyId == companyId)
            .Include(x => x.Identity)
            .Select(
                x => new
                {
                    Id = x.IdentityId,
                    x.Identity.DisplayName
                })
            .ToListAsync();

        var defaultSmsInstance = await _appDbContext.ConfigSMSConfigs
            .AsNoTracking()
            .Where(x => x.CompanyId == companyId)
            .FirstOrDefaultAsync();

        var default360DialogChannel = await _appDbContext.ConfigWhatsApp360DialogConfigs
            .AsNoTracking()
            .OrderBy(x => x.CreatedAt)
            .FirstOrDefaultAsync(x => x.CompanyId == companyId);

        var defaultTwilioWhatsApp = await _appDbContext.ConfigWhatsAppConfigs
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.CompanyId == companyId);

        var defaultCloudApiConfig = await _appDbContext.ConfigWhatsappCloudApiConfigs
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.CompanyId == companyId);

        var emailCustomField = userProfileCustomFields.First(x => x.FieldName.ToLower() == "email");
        var numberCustomField = userProfileCustomFields.First(x => x.FieldName.ToLower() == "phonenumber");
        var countryCustomField = userProfileCustomFields.First(x => x.FieldName.ToLower() == "country");

        var otherCustomFields = userProfileCustomFields
            .Where(u => u.Id != numberCustomField.Id || u.Id != emailCustomField.Id)
            .ToList();

        var allExistingUsers = await _appDbContext.UserProfiles
            .AsNoTracking()
            .Where(x => x.CompanyId == companyId)
            .Select(
                x => new UserProfile
                {
                    Id = x.Id,
                    CompanyId = x.CompanyId,
                    ContactOwnerId = x.ContactOwnerId,
                    Email = x.Email,
                    PhoneNumber = x.PhoneNumber
                })
            .ToListAsync();

        var insertedEmails = new HashSet<string>();
        var insertedPhoneNumber = new HashSet<string>();

        var existingEmailToUserProfileIdDict = new Dictionary<string, string>();
        var existingPhoneNumberToUserProfileIdDict = new Dictionary<string, string>();

        var existingUserProfileIds = new List<string>();

        allExistingUsers.ForEach(
            user =>
            {
                if (!string.IsNullOrWhiteSpace(user.Email))
                {
                    insertedEmails.Add(user.Email);
                    existingEmailToUserProfileIdDict.TryAdd(user.Email, user.Id);
                }

                if (!string.IsNullOrWhiteSpace(user.PhoneNumber))
                {
                    insertedPhoneNumber.Add(user.PhoneNumber);
                    existingPhoneNumberToUserProfileIdDict.TryAdd(user.PhoneNumber, user.Id);
                }
            });

        var emailSenders = await _appDbContext.SenderEmailSenders.AsNoTracking()
            .Where(x => x.CompanyId == companyId).Select(
                x => new EmailSender()
                {
                    Id = x.Id, Email = x.Email
                }).ToListAsync();

        var phoneNumberUtil = PhoneNumbers.PhoneNumberUtil.GetInstance();

        var newUserProfilesToInsert = new List<UserProfile>();
        foreach (var profile in newProfileViewModels)
        {
            var newUserProfile = new UserProfile
            {
                CompanyId = companyId,
                CustomFields = new List<UserProfileCustomField>()
            };

            if (!string.IsNullOrWhiteSpace(profile.Email))
            {
                var isValidEmail =
                    !string.IsNullOrWhiteSpace(profile.Email)
                    && Regex.IsMatch(profile.Email, @"^[^@\s]+@[^@\s]+\.[^@\s]+$", RegexOptions.IgnoreCase);

                if (insertedEmails.Contains(profile.Email) || !isValidEmail)
                {
                    if (existingEmailToUserProfileIdDict.TryGetValue(profile.Email, out var existingUserProfileId))
                    {
                        existingUserProfileIds.Add(existingUserProfileId);
                    }

                    continue;
                }

                newUserProfile.Email = profile.Email;
                newUserProfile.CustomFields.Add(
                    new UserProfileCustomField()
                    {
                        CompanyDefinedFieldId = emailCustomField.Id,
                        Value = profile.Email,
                        CompanyId = newUserProfile.CompanyId
                    });

                var existingEmailSender = emailSenders.FirstOrDefault(x => x.Email == profile.Email);
                if (existingEmailSender != null)
                {
                    newUserProfile.EmailAddressId = existingEmailSender.Id;
                }
                else
                {
                    newUserProfile.EmailAddress = new EmailSender
                    {
                        Email = profile.Email, Name = profile.FirstName, CompanyId = companyId
                    };
                }

                insertedEmails.Add(profile.Email);
            }

            if (!string.IsNullOrWhiteSpace(profile.WhatsAppPhoneNumber))
            {
                try
                {
                    profile.WhatsAppPhoneNumber =
                        PhoneNumberHelper.NormalizeWhatsappPhoneNumber(profile.WhatsAppPhoneNumber);
                    var phonenumber = phoneNumberUtil.Parse($"+{profile.WhatsAppPhoneNumber}", null);
                    var countCode = PhoneNumberHelper.GetCountryCode(phonenumber);
                    string countryName = _countryService.GetCountryEnglishNameByCountryCode(countCode);

                    UserProfileCustomField userProfileCountryField = new UserProfileCustomField
                    {
                        CompanyId = newUserProfile.CompanyId,
                        CompanyDefinedFieldId = countryCustomField.Id,
                        Value = countryName
                    };
                    newUserProfile.CustomFields.Add(userProfileCountryField);

                    if (insertedPhoneNumber.Contains(profile.WhatsAppPhoneNumber))
                    {
                        if (existingPhoneNumberToUserProfileIdDict.TryGetValue(profile.WhatsAppPhoneNumber, out var existingUserProfileId))
                        {
                            existingUserProfileIds.Add(existingUserProfileId);
                        }

                        continue;
                    }

                    newUserProfile.PhoneNumber = profile.WhatsAppPhoneNumber;

                    newUserProfile.CustomFields.Add(
                        new UserProfileCustomField()
                        {
                            CompanyDefinedFieldId = numberCustomField.Id, Value = profile.WhatsAppPhoneNumber,
                            CompanyId = newUserProfile.CompanyId
                        });

                    newUserProfile.WhatsAppAccount = new WhatsAppSender
                    {
                        whatsAppId = $"whatsapp:+{profile.WhatsAppPhoneNumber}",
                        name = profile.FirstName,
                        phone_number = profile.WhatsAppPhoneNumber,
                        CompanyId = companyId,
                        InstanceId = defaultTwilioWhatsApp?.TwilioAccountId,
                        InstaneSender = defaultTwilioWhatsApp?.WhatsAppSender
                    };

                    newUserProfile.SMSUser = new SMSSender
                    {
                        SMSId = $"+{profile.WhatsAppPhoneNumber}",
                        name = profile.FirstName,
                        phone_number = profile.WhatsAppPhoneNumber,
                        CompanyId = companyId,
                        InstanceId = defaultSmsInstance?.TwilioAccountId
                    };

                    newUserProfile.WhatsApp360DialogUser = new WhatsApp360DialogSender()
                    {
                        PhoneNumber = $"+{profile.WhatsAppPhoneNumber}",
                        WhatsAppId = profile.WhatsAppPhoneNumber,
                        CompanyId = companyId,
                        ChannelId = default360DialogChannel?.Id,
                        ChannelWhatsAppPhoneNumber = default360DialogChannel?.WhatsAppPhoneNumber
                    };

                    newUserProfile.WhatsappCloudApiUser = new WhatsappCloudApiSender()
                    {
                        WhatsappId = profile.WhatsAppPhoneNumber,
                        CompanyId = companyId,
                        WhatsappChannelPhoneNumber = defaultCloudApiConfig?.WhatsappPhoneNumber
                    };

                    insertedPhoneNumber.Add(profile.WhatsAppPhoneNumber);
                }
                catch (Exception ex)
                {
                    _logger.LogError(
                        ex,
                        "[{MethodName}] Error processing phone number {PhoneNumber}. {ExceptionMessage}",
                        nameof(PrepareNewUserProfilesAsync),
                        profile.WhatsAppPhoneNumber,
                        ex.Message);
                    profile.WhatsAppPhoneNumber = null;
                }
            }

            foreach (var customFieldsViewModel in profile.UserProfileFields.Where(
                         x => !string.IsNullOrWhiteSpace(x.CustomFieldName) && x.CustomFieldId == null))
            {
                if (customFieldsViewModel.CustomFieldName.ToLower() == "contactowner")
                {
                    newUserProfile.ContactOwnerId =
                        staffs.FirstOrDefault(x => x.DisplayName == customFieldsViewModel.CustomValue)?.Id;
                }
                else
                {
                    customFieldsViewModel.CustomFieldId = userProfileCustomFields.FirstOrDefault(
                        x => string.Equals(
                            x.FieldName,
                            customFieldsViewModel.CustomFieldName,
                            StringComparison.CurrentCultureIgnoreCase))?.Id;
                }
            }

            foreach (var customFieldsViewModel in profile.UserProfileFields.Where(
                         x => otherCustomFields.Select(c => c.Id).Contains(x.CustomFieldId)))
            {
                newUserProfile.CustomFields.Add(
                    new UserProfileCustomField()
                    {
                        CompanyDefinedFieldId = customFieldsViewModel.CustomFieldId,
                        Value = customFieldsViewModel.CustomValue,
                        CompanyId = newUserProfile.CompanyId
                    });
            }

            if (!string.IsNullOrWhiteSpace(profile.FirstName))
            {
                newUserProfile.FirstName = profile.FirstName;
            }

            if (!string.IsNullOrWhiteSpace(profile.LastName))
            {
                newUserProfile.LastName = profile.LastName;
            }

            if (newUserProfile.FirstName == "Anonymous")
            {
                if (newUserProfile.PhoneNumber != null)
                {
                    newUserProfile.FirstName = newUserProfile.PhoneNumber;
                }
                else if (newUserProfile.Email != null)
                {
                    newUserProfile.FirstName = newUserProfile.Email;
                }
            }

            newUserProfilesToInsert.Add(newUserProfile);
        }

        return (existingUserProfileIds, newUserProfilesToInsert);
    }

    private async Task BulkInsertUserProfilesAsync(
        string companyId,
        List<UserProfile> newUserProfiles,
        ImportContactHistory importContactHistory,
        bool isImportIntoList,
        ImportContactToListRecord importContactToListRecord,
        Func<int, ValueTask> onComplete = null)
    {
        var completeCount = 0;
        foreach (var userProfiles in newUserProfiles.Chunk(1000))
        {
            var importedUserProfilesCount = userProfiles.Length;

            var conversations = userProfiles.Select(
                x => new Conversation
                {
                    SMSUser = x.SMSUser,
                    WhatsappUser = x.WhatsAppAccount,
                    facebookUser = x.FacebookAccount,
                    WeChatUser = x.WeChatUser,
                    WebClient = x.WebClient,
                    UserDevice = x.UserDevice,
                    NaiveUser = x.RegisteredUser,
                    LineUser = x.LineUser,
                    InstagramUser = x.InstagramUser,
                    WhatsApp360DialogUser = x.WhatsApp360DialogUser,
                    WhatsappCloudApiUser = x.WhatsappCloudApiUser,
                    CompanyId = x.CompanyId,
                    UserProfile = x,
                    MessageGroupName = companyId,
                    UpdatedTime = DateTime.UtcNow,
                    Status = "closed",
                    ActiveStatus = ActiveStatus.Active
                })
                .ToList();

            _appDbContext.UserProfiles.AddRange(userProfiles);
            _appDbContext.Conversations.AddRange(conversations);

            try
            {
                await _appDbContext.SaveChangesAsync();

                importContactHistory.ImportedCount += importedUserProfilesCount;

                if (isImportIntoList && importContactToListRecord != null)
                {
                    importContactToListRecord.ImportIndex += importedUserProfilesCount;
                }
                else
                {
                    importContactHistory.ImportIndex += importedUserProfilesCount;
                }

                // The implicit behaviour is that contacts are imported as individual contacts
                // when the import name is not provided. Only supported for Express/Bulk Import.
                if (!string.IsNullOrEmpty(importContactHistory.ImportName))
                {
                    importContactHistory.ImportedUserProfiles.AddRange(
                        userProfiles.Select(
                            x => new ImportedUserProfile
                            {
                                ImportContactHistoryId = importContactHistory.Id,
                                UserProfileId = x.Id
                            }));
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Company Id: {CompanyId}. Import Error: {Message}",
                    companyId,
                    ex.Message);

                _appDbContext.UserProfiles.RemoveRange(userProfiles);
                importContactHistory.FailedCount += importedUserProfilesCount;
            }

            completeCount += importedUserProfilesCount;
            if (onComplete != null)
            {
                await onComplete(completeCount);
            }
        }
    }

    /// <summary>
    /// Insert user profiles to the list.
    /// Will not count inserted records as ImportContactHistory.UpdatedCount
    /// Will not count failed records as ImportContactHistory.FailedCount
    /// </summary>
    private async Task BulkInsertUserProfilesToListAsync(
        string companyId,
        List<string> userProfileIds,
        ImportContactHistory importContactHistory,
        ImportContactToListRecord importContactToListRecord,
        Func<int, ValueTask> onComplete = null)
    {
        var importContactHistoryId = importContactHistory.Id;

        var insertedUserProfileIds = await _appDbContext.CompanyImportedUserProfiles
            .Where(x => x.ImportContactHistoryId == importContactHistoryId)
            .Select(x => x.UserProfileId)
            .ToAsyncEnumerable()
            .ToHashSetAsync();

        var userProfileIdsToInsert = userProfileIds
            .Except(insertedUserProfileIds)
            .ToHashSet();

        var completedCount = 0;
        foreach (var chunkedUserProfileIdsToInsert in userProfileIdsToInsert.Chunk(1000))
        {
            var insertedCount = chunkedUserProfileIdsToInsert.Length;

            try
            {
                if (importContactToListRecord != null)
                {
                    importContactToListRecord.ImportIndex += insertedCount;
                }
                else
                {
                    importContactHistory.ImportIndex += insertedCount;
                }

                if (!string.IsNullOrEmpty(importContactHistory.ImportName))
                {
                    importContactHistory.ImportedUserProfiles.AddRange(
                        chunkedUserProfileIdsToInsert.Select(
                            x => new ImportedUserProfile
                            {
                                ImportContactHistoryId = importContactHistory.Id,
                                UserProfileId = x
                            }));
                }

                await _appDbContext.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Company Id: {CompanyId}. Insert UserProfileId to list Error: {Message}",
                    companyId,
                    ex.Message);
            }

            completedCount += insertedCount;
            if (onComplete != null)
            {
                await onComplete(completedCount);
            }
        }
    }

    [HangfireJobExpirationTimeout(timeoutInMinutes: 1)]
    public async Task HandleUserProfilePostImportActionsV2(
        string companyId,
        long staffId,
        string userProfileId,
        long contactListId,
        bool isNewImport,
        Dictionary<string, UserProfileImportFieldDto> userProfileImportFields,
        bool isTriggerAutomation)
    {
        var labels = userProfileImportFields.GetValueOrDefault("label")?.FieldValue;
        var collaborator = userProfileImportFields.GetValueOrDefault("collaborators")?.FieldValue;
        var assignedTeam = userProfileImportFields.GetValueOrDefault("assignedteam")?.FieldValue;

        try
        {
            var importedUserProfile = await _appDbContext.UserProfiles
                .FirstOrDefaultAsync(
                    profile =>
                        profile.CompanyId == companyId
                        && profile.Id == userProfileId);

            var companyUser = await _appDbContext.UserRoleStaffs
                .Include(x => x.Company)
                .Include(x => x.Identity)
                .FirstOrDefaultAsync(x => x.Id == staffId);

            if (importedUserProfile.ActiveStatus == ActiveStatus.Inactive)
            {
                importedUserProfile.ActiveStatus = ActiveStatus.Active;
            }

            if (string.IsNullOrEmpty(importedUserProfile.FirstName))
            {
                importedUserProfile.FirstName = importedUserProfile.PhoneNumber;
            }

            await LocalizeDateTimeCustomFieldsAsyncV2(companyId, userProfileImportFields);
            Conversation conversation = null;

            if (!string.IsNullOrEmpty(labels)
                || !string.IsNullOrEmpty(collaborator)
                || !string.IsNullOrEmpty(assignedTeam))
            {
                conversation = await _userProfileService.GetConversationByUserProfileId(
                    companyId,
                    importedUserProfile.Id);

                if (!string.IsNullOrEmpty(labels))
                {
                    var conversationHashtags = labels
                        .Split(";", StringSplitOptions.RemoveEmptyEntries)
                        .Distinct()
                        .Select(
                            labelValue => new ConversationHashtagViewModel
                            {
                                Hashtag = labelValue
                            })
                        .ToList();

                    var labelImportAction = userProfileImportFields["label"].ImportAction;

                    switch (labelImportAction)
                    {
                        case ImportAction.Overwrite:
                            await _conversationHashtagService.SetConversationHashtag(
                                companyId,
                                conversation.Id,
                                staffId,
                                conversationHashtags);

                            break;
                        case ImportAction.Append:
                        case ImportAction.UpdateBlankOnly:
                            await _conversationHashtagService.AddConversationHashtag(
                                companyId,
                                conversation.Id,
                                staffId,
                                conversationHashtags);

                            break;
                    }
                }

                if (!string.IsNullOrEmpty(collaborator))
                {
                    var collaborators = collaborator.Split(";", StringSplitOptions.RemoveEmptyEntries);

                    var collaboratorIds = new List<string>();

                    foreach (var field in collaborators)
                    {
                        var staffInfo = await _appDbContext.UserRoleStaffs
                            .Where(
                                x => x.CompanyId == companyId
                                     && (x.IdentityId == field
                                         || x.Identity.DisplayName == field
                                         || x.Identity.UserName == field))
                            .Select(x => x.IdentityId)
                            .FirstOrDefaultAsync();

                        if (!string.IsNullOrEmpty(staffInfo))
                        {
                            collaboratorIds.Add(staffInfo);
                        }
                    }

                    await _conversationAssigneeService.ReplaceAdditionalAssignees(
                        conversation,
                        collaboratorIds,
                        companyUser,
                        false);
                }

                if (!string.IsNullOrEmpty(assignedTeam))
                {
                    var companyTeam = await _appDbContext.CompanyStaffTeams
                        .FirstOrDefaultAsync(
                            x =>
                                x.CompanyId == companyUser.CompanyId
                                && x.TeamName.Trim().ToLower() == assignedTeam.ToLower());

                    if (companyTeam != null)
                    {
                        var field = userProfileImportFields["assignedteam"];

                        // 1. Conversation has assigned team or assigned staff before
                        if (conversation.AssignedTeamId != null
                            || conversation.AssigneeId != null)
                        {
                            // ImportAction is "overwrite"
                            if (field.ImportAction == ImportAction.Overwrite)
                            {
                                // 1.1 remove assigned staff and assigned team
                                await _conversationMessageService.ChangeConversationAssignee(
                                    conversation,
                                    null,
                                    isTriggerAutomation,
                                    companyUser.IdentityId);

                                // 1.2 assigned new team to that conversation
                                await _conversationMessageService.ChangeConversationAssignedTeam(
                                    conversation,
                                    companyTeam,
                                    isTriggerAutomation,
                                    companyUser.IdentityId);

                            }
                        }
                        else // 2. conversation hsa no assigned team and staff before
                        {
                            // 2.1 assigned new team to that conversation
                            await _conversationMessageService.ChangeConversationAssignedTeam(
                                conversation,
                                companyTeam,
                                isTriggerAutomation,
                                companyUser.IdentityId);
                        }
                    }
                }
            }

            List<ImportHeader> importHeaders = new();
            List<string> importFields = new();

            foreach (var key in userProfileImportFields.Keys)
            {
                importHeaders.Add(new()
                {
                    HeaderName = userProfileImportFields[key].FieldName,
                    importAction = userProfileImportFields[key].ImportAction
                });

                importFields.Add(userProfileImportFields[key].FieldValue);
            }

            await _userProfileService.BulkSetFields(
                importedUserProfile,
                companyUser.Id,
                new UserProfileService.ImportUserProfileObject(
                    importHeaders,
                    importFields),
                isTriggerAutomation,
                true);

            if (isNewImport)
            {
                await _userProfileService.SetFieldValueByFieldNameNotSaved(
                    importedUserProfile,
                    "Subscriber",
                    "true");

                if (conversation is null)
                {
                    await _userProfileService.GetConversationByUserProfileId(companyId, importedUserProfile.Id);
                }
            }

            if (isNewImport
                && isTriggerAutomation)
            {
                var hasNewContactTrigger = await _appDbContext.CompanyAssignmentRules
                    .AnyAsync(
                        x =>
                            x.CompanyId == companyId
                            && x.AutomationType == AutomationType.ContactAdded);

                if (hasNewContactTrigger)
                {
                    BackgroundJob.Schedule<IAutomationService>(
                        x => x.NewContactTrigger(importedUserProfile.Id),
                        TimeSpan.FromSeconds(10));

                    await _userProfileHooks.OnUserProfileCreatedAsync(
                        companyId,
                        importedUserProfile.Id,
                        null,
                        () => Task.FromResult(new OnUserProfileCreatedData(importedUserProfile)));
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "[{MethodName}] Company {CompanyId}. Import list {ListId} user profile {UserProfileId} post-actions error. " +
                "Is new contact: {IsNewContact}, trigger automation: {IsTriggerAutomation}.  {ExceptionMessage}",
                nameof(HandleUserProfilePostImportActionsV2),
                companyId,
                contactListId,
                userProfileId,
                isNewImport,
                isTriggerAutomation,
                ex.Message);
        }
    }

    [Obsolete("Use HandleUserProfilePostImportActionsV2 instead")]
    public async Task HandleUserProfilePostImportActions(
        string companyId,
        long staffId,
        string userProfileId,
        long contactListId,
        bool isNewImport,
        UserProfileService.ImportUserProfileObject importUserProfileObject,
        bool isTriggerAutomation)
    {
        var labels = importUserProfileObject.GetValueFromList("label");
        var collaborator = importUserProfileObject.GetValueFromList("collaborator");
        var assignedTeam = importUserProfileObject.GetValueFromList("assignedteam");

        var headers = importUserProfileObject._headers;
        var fields = importUserProfileObject._fields;

        try
        {
            var importedUserProfile = await _appDbContext.UserProfiles
                .FirstOrDefaultAsync(
                    profile =>
                        profile.CompanyId == companyId
                        && profile.Id == userProfileId);

            var companyUser = await _appDbContext.UserRoleStaffs
                .Include(x => x.Company)
                .Include(x => x.Identity)
                .FirstOrDefaultAsync(x => x.Id == staffId);

            if (importedUserProfile.ActiveStatus == ActiveStatus.Inactive)
            {
                importedUserProfile.ActiveStatus = ActiveStatus.Active;
            }

            if (string.IsNullOrEmpty(importedUserProfile.FirstName))
            {
                importedUserProfile.FirstName = importedUserProfile.PhoneNumber;
            }

            await LocalizeDateTimeCustomFieldsAsync(companyId, importUserProfileObject, headers, fields);
            Conversation conversation = null;

            if (!string.IsNullOrEmpty(labels)
                || !string.IsNullOrEmpty(collaborator)
                || !string.IsNullOrEmpty(assignedTeam))
            {
                conversation = await _userProfileService.GetConversationByUserProfileId(
                    companyId,
                    importedUserProfile.Id);

                if (!string.IsNullOrEmpty(labels))
                {
                    var conversationHashtags = labels
                        .Split(";", StringSplitOptions.RemoveEmptyEntries)
                        .Distinct()
                        .Select(
                            labelValue => new ConversationHashtagViewModel
                            {
                                Hashtag = labelValue
                            })
                        .ToList();

                    var labelImportAction = importUserProfileObject.GetHeaderFromList("label").importAction;

                    switch (labelImportAction)
                    {
                        case ImportAction.Overwrite:
                            await _conversationHashtagService.SetConversationHashtag(
                                companyId,
                                conversation.Id,
                                staffId,
                                conversationHashtags);

                            break;
                        case ImportAction.Append:
                        case ImportAction.UpdateBlankOnly:
                            await _conversationHashtagService.AddConversationHashtag(
                                companyId,
                                conversation.Id,
                                staffId,
                                conversationHashtags);

                            break;
                    }
                }

                if (!string.IsNullOrEmpty(collaborator))
                {
                    var collaborators = collaborator.Split(";", StringSplitOptions.RemoveEmptyEntries);

                    var collaboratorIds = new List<string>();

                    foreach (var field in collaborators)
                    {
                        var staffInfo = await _appDbContext.UserRoleStaffs
                            .Where(
                                x => x.CompanyId == companyId
                                     && (x.IdentityId == field
                                         || x.Identity.DisplayName == field
                                         || x.Identity.UserName == field))
                            .Select(x => x.IdentityId)
                            .FirstOrDefaultAsync();

                        if (!string.IsNullOrEmpty(staffInfo))
                        {
                            collaboratorIds.Add(staffInfo);
                        }
                    }

                    await _conversationAssigneeService.ReplaceAdditionalAssignees(
                        conversation,
                        collaboratorIds,
                        companyUser,
                        false);
                }

                if (!string.IsNullOrEmpty(assignedTeam))
                {
                    var companyTeam = await _appDbContext.CompanyStaffTeams
                        .FirstOrDefaultAsync(
                            x =>
                                x.CompanyId == companyUser.CompanyId
                                && x.TeamName.Trim().ToLower() == assignedTeam.ToLower());

                    if (companyTeam != null)
                    {
                        var header = importUserProfileObject.GetHeaderFromList("assignedTeam");

                        // 1. Conversation has assigned team or assigned staff before
                        if (conversation.AssignedTeamId != null
                            || conversation.AssigneeId != null)
                        {
                            // ImportAction is "overwrite"
                            if (header.importAction == ImportAction.Overwrite)
                            {
                                // 1.1 remove assigned staff and assigned team
                                await _conversationMessageService.ChangeConversationAssignee(
                                    conversation,
                                    null,
                                    isTriggerAutomation,
                                    companyUser.IdentityId);

                                // 1.2 assigned new team to that conversation
                                await _conversationMessageService.ChangeConversationAssignedTeam(
                                    conversation,
                                    companyTeam,
                                    isTriggerAutomation,
                                    companyUser.IdentityId);

                            }
                        }
                        else // 2. conversation hsa no assigned team and staff before
                        {
                            // 2.1 assigned new team to that conversation
                            await _conversationMessageService.ChangeConversationAssignedTeam(
                                conversation,
                                companyTeam,
                                isTriggerAutomation,
                                companyUser.IdentityId);
                        }
                    }
                }
            }

            await _userProfileService.BulkSetFields(
                importedUserProfile,
                companyUser.Id,
                importUserProfileObject,
                isTriggerAutomation,
                true);

            if (isNewImport)
            {
                await _userProfileService.SetFieldValueByFieldNameNotSaved(
                    importedUserProfile,
                    "Subscriber",
                    "true");

                if (conversation is null)
                {
                    await _userProfileService.GetConversationByUserProfileId(companyId, importedUserProfile.Id);
                }
            }

            if (isNewImport
                && isTriggerAutomation)
            {
                var hasNewContactTrigger = await _appDbContext.CompanyAssignmentRules
                    .AnyAsync(
                        x =>
                            x.CompanyId == companyId
                            && x.AutomationType == AutomationType.ContactAdded);

                if (hasNewContactTrigger)
                {
                    BackgroundJob.Schedule<IAutomationService>(
                        x => x.NewContactTrigger(importedUserProfile.Id),
                        TimeSpan.FromSeconds(10));

                    await _userProfileHooks.OnUserProfileCreatedAsync(
                        companyId,
                        importedUserProfile.Id,
                        null,
                        () => Task.FromResult(new OnUserProfileCreatedData(importedUserProfile)));
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "[{MethodName}] Company {CompanyId}. Import list {ListId} user profile {UserProfileId} post-actions error. " +
                "Is new contact: {IsNewContact}, trigger automation: {IsTriggerAutomation}.  {ExceptionMessage}",
                nameof(HandleUserProfilePostImportActions),
                companyId,
                contactListId,
                userProfileId,
                isNewImport,
                isTriggerAutomation,
                ex.Message);
        }
    }

    private async Task LocalizeDateTimeCustomFieldsAsync(
        string companyId,
        UserProfileService.ImportUserProfileObject importUserProfileObject,
        List<ImportHeader> headers,
        List<string> fields)
    {
        try
        {
            for (var headerIndex = 0; headerIndex < headers.Count; headerIndex++)
            {
                var header = importUserProfileObject._headers[headerIndex];

                var customField = await _appDbContext.CompanyCustomUserProfileFields
                    .FirstOrDefaultAsync(
                        field =>
                            field.CompanyId == companyId
                            && field.FieldName == header.HeaderName);

                if (customField is not
                    { Type: FieldDataType.Date or FieldDataType.DateTime })
                {
                    continue;
                }

                var value = fields[headerIndex];

                if (!DateTime.TryParse(value, out var dateTime))
                {
                    continue;
                }

                var timeZoneId = await _appDbContext.CompanyCompanies
                    .Where(x => x.Id == companyId)
                    .Select(x => x.TimeZoneInfoId)
                    .FirstOrDefaultAsync();

                fields[headerIndex] = dateTime
                    .Tolocaltime(timeZoneId)
                    .ToString("o");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "Company Id: {CompanyId}. Unable to convert to company's timezone {Message}",
                companyId,
                ex.Message);
        }
    }

    private async Task LocalizeDateTimeCustomFieldsAsyncV2(
        string companyId,
        Dictionary<string, UserProfileImportFieldDto> userProfileImportFields)
    {
        try
        {
            foreach (var entry in userProfileImportFields)
            {
                var fieldName = entry.Value.FieldName;

                var customField = await _appDbContext.CompanyCustomUserProfileFields
                    .FirstOrDefaultAsync(
                        field =>
                            field.CompanyId == companyId
                            && field.FieldName == fieldName);

                if (customField is not
                    { Type: FieldDataType.Date or FieldDataType.DateTime })
                {
                    continue;
                }

                if (!DateTime.TryParse(entry.Value.FieldValue, out var dateTime))
                {
                    continue;
                }

                var timeZoneId = await _appDbContext.CompanyCompanies
                    .Where(x => x.Id == companyId)
                    .Select(x => x.TimeZoneInfoId)
                    .FirstOrDefaultAsync();

                userProfileImportFields[entry.Key] = entry.Value with
                {
                    FieldValue = dateTime
                        .Tolocaltime(timeZoneId)
                        .ToString("o")
                };
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "Company Id: {CompanyId}. Unable to convert to company's timezone {Message}",
                companyId,
                ex.Message);
        }
    }
}