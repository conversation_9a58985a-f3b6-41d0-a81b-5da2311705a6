using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;
using AutoMapper;
using Hangfire;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using Newtonsoft.Json.Linq;
using Sleekflow.Apis.CrmHub.Api;
using Sleekflow.Apis.CrmHub.Model;
using Travis_backend.AccountAuthenticationDomain.Models;
using Travis_backend.AutomationDomain.Models;
using Travis_backend.BackgroundTaskServices;
using Travis_backend.BackgroundTaskServices.Helpers;
using Travis_backend.BackgroundTaskServices.Models;
using Travis_backend.Cache;
using Travis_backend.CommonDomain.ViewModels;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.CompanyDomain.Services;
using Travis_backend.Constants;
using Travis_backend.ContactDomain.Constants;
using Travis_backend.ContactDomain.Services;
using Travis_backend.ConversationDomain.ViewModels;
using Travis_backend.ConversationServices;
using Travis_backend.Database;
using Travis_backend.Enums;
using Travis_backend.Infrastructures.Attributes;
using Travis_backend.MessageDomain.ViewModels;
using Travis_backend.SleekflowCrmHubDomain.Filters;
using Travis_backend.SleekflowCrmHubDomain.Models;
using Travis_backend.SleekflowCrmHubDomain.Services;
using Filter = Sleekflow.Apis.CrmHub.Model.Filter;

namespace Travis_backend.Controllers.SleekflowControllers;

[Authorize]
[Route("CrmHub")]
public class CrmHubController : ControllerBase
{
    private readonly ApplicationDbContext _appDbContext;
    private readonly IMapper _mapper;
    private readonly ILogger<CrmHubController> _logger;
    private readonly UserManager<ApplicationUser> _userManager;
    private readonly ICoreService _coreService;
    private readonly ICompanyInfoCacheService _companyInfoCacheService;
    private readonly IUserProfileService _userProfileService;
    private readonly IObjectsApi _objectsApi;
    private readonly IProvidersApi _providersApi;
    private readonly IUnifyRulesApi _unifyRulesApi;
    private readonly IWebhooksApi _webhooksApi;
    private readonly IConfiguration _configuration;
    private readonly ICrmHubService _crmHubService;
    private readonly IBackgroundTaskService _backgroundTaskService;
    private readonly ICrmHubConfigsApi _crmHubConfigsApi;
    private readonly IBlobsApi _blobsApi;

    public CrmHubController(
        ApplicationDbContext appDbContext,
        IMapper mapper,
        ILogger<CrmHubController> logger,
        UserManager<ApplicationUser> userManager,
        ICoreService coreService,
        ICompanyInfoCacheService companyInfoCacheService,
        IUserProfileService userProfileService,
        IObjectsApi objectsApi,
        IProvidersApi providersApi,
        IUnifyRulesApi unifyRulesApi,
        IWebhooksApi webhooksApi,
        IConfiguration configuration,
        ICrmHubService crmHubService,
        IBackgroundTaskService backgroundTaskService,
        ICrmHubConfigsApi crmHubConfigsApi,
        ICompanySubscriptionService companySubscriptionService,
        IBlobsApi blobsApi)
    {
        _appDbContext = appDbContext;
        _mapper = mapper;
        _logger = logger;
        _userManager = userManager;
        _coreService = coreService;
        _companyInfoCacheService = companyInfoCacheService;
        _userProfileService = userProfileService;
        _objectsApi = objectsApi;
        _providersApi = providersApi;
        _unifyRulesApi = unifyRulesApi;
        _webhooksApi = webhooksApi;
        _configuration = configuration;
        _crmHubService = crmHubService;
        _backgroundTaskService = backgroundTaskService;
        _crmHubConfigsApi = crmHubConfigsApi;
        _blobsApi = blobsApi;
    }

    public class SyncObjectsToCrmHubOutput
    {
        public CompanyDto Company { get; set; }

        public List<Dictionary<string, object>> UserProfiles { get; set; }
    }

    public class CompanyDto
    {
        public string Id { get; set; }

        public string CompanyName { get; set; }

        public List<CompanyCustomUserProfileFieldViewModel> CustomUserProfileFields { get; set; }
    }

    public class UserProfileDto
    {
        public string Id { get; set; }

        public string FirstName { get; set; }

        public string LastName { get; set; }

        public string PictureUrl { get; set; }

        public string DisplayProfilePicture
        {
            get
            {
                if (InstagramProfilePic != null)
                {
                    return InstagramProfilePic;
                }

                if (FacebookProfilePic != null)
                {
                    return FacebookProfilePic;
                }

                if (WeChatHeadimgurl != null)
                {
                    return WeChatHeadimgurl;
                }

                if (WhatsappAccountProfilePic != null)
                {
                    return WhatsappAccountProfilePic;
                }

                if (LinePictureUrl != null)
                {
                    return LinePictureUrl;
                }

                if (ViberPictureUrl != null)
                {
                    return ViberPictureUrl;
                }

                if (TelegramPicturePhotoUrl != null)
                {
                    return TelegramPicturePhotoUrl;
                }

                return PictureUrl;
            }
        }

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        public DateTime? LastContact { get; set; }

        public DateTime? LastContactFromCustomers { get; set; }

        public bool IsSandbox { get; set; }

        public bool IsShopifyProfile { get; set; }

        public string Description { get; set; }

        public string Status { get; set; }

        public string FacebookId { get; set; }

        public string FacebookPageId { get; set; }

        public string FacebookName { get; set; }

        public string FacebookEmail { get; set; }

        public string FacebookLocale { get; set; } = "en";

        public string FacebookProfilePic { get; set; }

        public string SmsUserId { get; set; }

        public string SmsUserPhoneNumber { get; set; }

        public string SmsUsername { get; set; }

        public string SmsUserLocale { get; set; } = "en";

        public string SmsUserProfilePic { get; set; }

        public string WhatsappAccountId { get; set; }

        public string WhatsappAccountPhoneNumber { get; set; }

        public string WhatsappAccountName { get; set; }

        public string WhatsappAccountLocale { get; set; } = "en";

        public string WhatsappAccountProfilePic { get; set; }

        public string WhatsappAccountInstanceId { get; set; }

        public bool WhatsappAccountIsGroup
        {
            get
            {
                if (string.IsNullOrEmpty(Id))
                {
                    return false;
                }

                return Id.Contains("@g.us");
            }
        }

        public bool WhatsappAccountIsTwilio
        {
            get
            {
                if (string.IsNullOrEmpty(Id))
                {
                    return false;
                }

                return Id.Contains("whatsapp:+");
            }
        }

        // public UserDeviceResponse UserDevice { get; set; }
        // public GuestResponse RegisteredUser { get; set; }
        public string EmailEmail { get; set; }

        public string EmailName { get; set; }

        public string EmailLocale { get; set; } = "en";

        public string WebClientWebClientUUID { get; set; }

        public string WebClientIPAddress { get; set; }

        public string WebClientName { get; set; }

        public string WebClientLocale { get; set; } = "en";

        public DateTime? WebClientCreatedAt { get; set; }

        public DateTime? WebClientUpdatedAt { get; set; }

        public string WebClientOnlineStatus { get; set; }

        public string WebClientDevice { get; set; }

        public string WeChatOpenid { get; set; }

        public string WeChatNickname { get; set; }

        public string WeChatLanguage { get; set; }

        public string WeChatHeadimgurl { get; set; }

        public string LineUserId { get; set; }

        public string LineDisplayName { get; set; }

        public string LinePictureUrl { get; set; }

        public string LineStatusMessage { get; set; }

        public string InstagramInstagramId { get; set; }

        public string InstagramInstagramPageId { get; set; }

        public string InstagramPageId { get; set; }

        public string InstagramCompanyId { get; set; }

        public string InstagramUserId { get; set; }

        public string InstagramUsername { get; set; }

        public string InstagramName { get; set; }

        public string InstagramProfilePic { get; set; }

        public long? ViberId { get; set; }

        public string ViberCompanyId { get; set; }

        public string ViberViberUserId { get; set; }

        public string ViberDisplayName { get; set; }

        public string ViberViberBotId { get; set; }

        public bool? ViberIsSubscribed { get; set; }

        public string ViberPictureUrl { get; set; }

        public long? TelegramId { get; set; }

        public string TelegramCompanyId { get; set; }

        public long? TelegramTelegramChatId { get; set; }

        public string TelegramFirstName { get; set; }

        public string TelegramLastName { get; set; }

        public string TelegramType { get; set; }

        public string TelegramPicturePhotoUrl { get; set; }

        public long? TelegramTelegramBotId { get; set; }

        public long? WhatsApp360DialogId { get; set; }

        public string WhatsApp360DialogWhatsAppId { get; set; }

        public string WhatsApp360DialogPhoneNumber { get; set; }

        public string WhatsApp360DialogCompanyId { get; set; }

        public string WhatsApp360DialogName { get; set; }

        public string WhatsApp360DialogContactStatus { get; set; }

        public long? WhatsApp360DialogChannelId { get; set; }

        public string WhatsApp360DialogChannelWhatsAppPhoneNumber { get; set; }

        public DateTime? WhatsApp360DialogCreatedAt { get; set; }

        public DateTime? WhatsApp360DialogUpdatedAt { get; set; }

        public IList<UserProfileCustomFieldNoOptionsViewModel> CustomFields { get; set; }

        public IList<ConversationHashtagResponse> ConversationHashtags { get; set; }
    }

    [HttpPost("WebhookOnEntityCreated")]
    [Consumes("application/json")]
    [Produces("application/json")]
    [AllowAnonymous]
    public IActionResult WebhookOnEntityCreated(
        [FromBody]
        OnEntityCreatedWebhookPayload onEntityCreatedWebhookPayload)
    {
        try
        {
            _logger.LogInformation(
                "Started WebhookOnEntityCreated onEntityCreatedWebhookPayload=[{OnEntityCreatedWebhookPayload}].",
                JsonConvert.SerializeObject(onEntityCreatedWebhookPayload));

            BackgroundJob.Enqueue<CrmHubJobs>(x => x.OnEntityCreated(onEntityCreatedWebhookPayload));

            return Ok();
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "Failed WebhookOnEntityCreated onEntityCreatedWebhookPayload=[{OnEntityCreatedWebhookPayload}].",
                JsonConvert.SerializeObject(onEntityCreatedWebhookPayload));

            return Ok(ex.Message);
        }
    }

    [HttpPost("WebhookOnEntityFieldsChanged")]
    [Consumes("application/json")]
    [Produces("application/json")]
    [AllowAnonymous]
    public IActionResult WebhookOnEntityFieldsChanged(
        [FromBody]
        OnEntityFieldsChangedWebhookPayload onEntityFieldsChangedWebhookPayload)
    {
        try
        {
            _logger.LogInformation(
                "Started WebhookOnEntityFieldsChanged onEntityFieldsChangedWebhookPayload=[{OnEntityFieldsChangedWebhookPayload}].",
                JsonConvert.SerializeObject(onEntityFieldsChangedWebhookPayload));

            BackgroundJob.Enqueue<CrmHubJobs>(x => x.OnEntityFieldsChanged(onEntityFieldsChangedWebhookPayload));

            return Ok();
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "Failed WebhookOnEntityFieldsChanged onEntityFieldsChangedWebhookPayload=[{OnEntityFieldsChangedWebhookPayload}].",
                JsonConvert.SerializeObject(onEntityFieldsChangedWebhookPayload));

            return Ok(ex.Message);
        }
    }

    public class InitProviderInput
    {
        [JsonProperty("provider_name")]
        [Required]
        public string ProviderName { get; set; }

        [JsonProperty("return_to_url")]
        [Required]
        public string ReturnToUrl { get; set; }

        [JsonProperty("additional_details")]
        public Dictionary<string, object> AdditionalDetails { get; set; }
    }

    [HttpPost("InitProvider")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<InitProviderOutput>> InitProvider([FromBody] InitProviderInput initProviderInput)
    {
        var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (companyUser == null)
        {
            return Unauthorized();
        }

        try
        {
            var initProviderOutputOutput =
                await _providersApi.ProvidersInitProviderPostAsync(
                    initProviderInput: new Sleekflow.Apis.CrmHub.Model.InitProviderInput(
                        companyUser.CompanyId,
                        initProviderInput.ProviderName,
                        initProviderInput.ReturnToUrl,
                        defaultRegionCode: "ZZ",
                        additionalDetails: initProviderInput.AdditionalDetails));

            return new OkObjectResult(initProviderOutputOutput.Data);
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "Failed InitProvider initProviderInput=[{InitProviderInput}].",
                JsonConvert.SerializeObject(initProviderInput));
        }

        return new OkResult();
    }

    public class GetProviderSupportedTypesInput
    {
        [JsonProperty("provider_name")]
        [Required]
        public string ProviderName { get; set; }
    }

    [HttpPost("GetProviderSupportedTypes")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GetProviderSupportedTypesOutput>> GetProviderSupportedTypes(
        [FromBody]
        GetProviderSupportedTypesInput getProviderSupportedTypesInput)
    {
        var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (companyUser == null)
        {
            return Unauthorized();
        }

        var getProviderSupportedTypesOutput =
            await _providersApi.ProvidersGetProviderSupportedTypesPostAsync(
                getProviderSupportedTypesInput: new Sleekflow.Apis.CrmHub.Model.GetProviderSupportedTypesInput(
                    companyUser.CompanyId,
                    getProviderSupportedTypesInput.ProviderName));

        return new OkObjectResult(getProviderSupportedTypesOutput.Data);
    }

    [HttpPost("GetProviders")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GetProvidersOutput>> GetProviders()
    {
        var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (companyUser == null)
        {
            return Unauthorized();
        }

        var getProvidersOutput =
            await _objectsApi.ObjectsGetProvidersPostAsync();

        return new OkObjectResult(getProvidersOutput.Data);
    }

    public class GetProviderTypeFieldsInput
    {
        [JsonProperty("provider_name")]
        [Required]
        public string ProviderName { get; set; }

        [JsonProperty("entity_type_name")]
        [Required]
        public string EntityTypeName { get; set; }
    }

    public class GetProviderTypeFieldsOutput
    {
        [JsonProperty("fields")]
        public List<GetTypeFieldsOutputFieldDto> Fields { get; set; }

        [JsonProperty("unmapped_field_names")]
        public List<string> UnmappedFieldNames { get; set; }

        [JsonProperty("mapped_field_names")]
        public List<string> MappedFieldNames { get; set; }
    }

    [HttpPost("GetProviderTypeFields")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GetProviderTypeFieldsOutput>> GetProviderTypeFields(
        [FromBody]
        GetProviderTypeFieldsInput getProviderTypeFieldsInput)
    {
        var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (companyUser == null)
        {
            return Unauthorized();
        }

        var getProviderTypeFieldsOutputOutput = await _providersApi.ProvidersGetProviderTypeFieldsPostAsync(
            getProviderTypeFieldsInput: new Sleekflow.Apis.CrmHub.Model.GetProviderTypeFieldsInput(
                companyUser.CompanyId,
                getProviderTypeFieldsInput.EntityTypeName,
                getProviderTypeFieldsInput.ProviderName));
        var fields = new List<GetTypeFieldsOutputFieldDto>();
        fields.AddRange(getProviderTypeFieldsOutputOutput.Data.CreatableFields);
        fields.AddRange(getProviderTypeFieldsOutputOutput.Data.UpdatableFields);
        fields.AddRange(getProviderTypeFieldsOutputOutput.Data.ViewableFields);

        var getUnifyRulesOutputOutput = await _unifyRulesApi.UnifyRulesGetUnifyRulesPostAsync(
            getUnifyRulesInput: new Sleekflow.Apis.CrmHub.Model.GetUnifyRulesInput(
                companyUser.CompanyId,
                getProviderTypeFieldsInput.EntityTypeName));

        var mappedFieldNames = new HashSet<string>(
            getUnifyRulesOutputOutput
                .Data
                .UnifyRules
                .SelectMany(unifyRule => unifyRule.ProviderPrecedences));
        var unmappedFieldNames =
            new HashSet<string>(fields.Select(f => getProviderTypeFieldsInput.ProviderName + ":" + f.Name))
                .Except(mappedFieldNames)
                .ToHashSet();

        var getProviderTypeFieldsOutput = new GetProviderTypeFieldsOutput
        {
            Fields = fields,
            UnmappedFieldNames = unmappedFieldNames.ToList(),
            MappedFieldNames = mappedFieldNames.ToList()
        };

        return new OkObjectResult(getProviderTypeFieldsOutput);
    }

    public class UpsertUnifyRulesInput
    {
        [JsonProperty("entity_type_name")]
        [Required]
        public string EntityTypeName { get; set; }

        [JsonProperty("unify_rules")]
        [Required]
        public List<UnifyRuleDto> UnifyRules { get; set; }
    }

    [HttpPost("UpsertUnifyRules")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult> UpsertUnifyRules(
        [FromBody]
        UpsertUnifyRulesInput upsertUnifyRulesInput)
    {
        var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (companyUser == null)
        {
            return Unauthorized();
        }

        await _unifyRulesApi.UnifyRulesUpsertUnifyRulesPostAsync(
            upsertUnifyRulesInput: new Sleekflow.Apis.CrmHub.Model.UpsertUnifyRulesInput(
                companyUser.CompanyId,
                upsertUnifyRulesInput.EntityTypeName,
                upsertUnifyRulesInput.UnifyRules));

        return new OkResult();
    }

    public class GetUnifyRulesInput
    {
        [JsonProperty("entity_type_name")]
        [Required]
        public string EntityTypeName { get; set; }
    }

    [HttpPost("GetUnifyRules")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GetUnifyRulesOutput>> GetUnifyRules(
        [FromBody]
        GetUnifyRulesInput getUnifyRulesInput)
    {
        var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (companyUser == null)
        {
            return Unauthorized();
        }

        var getUnifyRulesOutputOutput = await _unifyRulesApi.UnifyRulesGetUnifyRulesPostAsync(
            getUnifyRulesInput: new Sleekflow.Apis.CrmHub.Model.GetUnifyRulesInput(
                companyUser.CompanyId,
                getUnifyRulesInput.EntityTypeName));

        return new OkObjectResult(getUnifyRulesOutputOutput);
    }

    public class PreviewObjectsInput
    {
        [JsonProperty("entity_type_name")]
        [Required]
        public string EntityTypeName { get; set; }

        [JsonProperty("provider_name")]
        [Required]
        public string ProviderName { get; set; }

        [JsonProperty("filters")]
        [Required]
        public List<SyncConfigFilter> Filters { get; set; }
    }

    [HttpPost("PreviewObjects")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<PreviewObjectsOutput>> PreviewObjects(
        [FromBody]
        PreviewObjectsInput previewObjectsInput)
    {
        var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (companyUser == null)
        {
            return Unauthorized();
        }

        var previewObjectsOutputOutput = await _providersApi.ProvidersPreviewObjectsPostAsync(
            previewObjectsInput: new Sleekflow.Apis.CrmHub.Model.PreviewObjectsInput(
                companyUser.CompanyId,
                previewObjectsInput.ProviderName,
                previewObjectsInput.EntityTypeName,
                previewObjectsInput.Filters,
                fieldFilters: null));

        return new OkObjectResult(previewObjectsOutputOutput.Data);
    }

    public class PreviewObjectsV2Request
    {
        [JsonProperty("provider_name")]
        [Required]
        public string ProviderName { get; set; }

        [JsonProperty("provider_connection_id")]
        [Required]
        public string ProviderConnectionId { get; set; }

        [JsonProperty("entity_type_name")]
        [Required]
        public string EntityTypeName { get; set; }

        [JsonProperty("filters")]
        [Required]
        public List<SyncConfigFilter> Filters { get; set; }

        [JsonProperty("field_filters")]
        public List<SyncConfigFieldFilter>? FieldFilters { get; set; }

        [JsonProperty("next_records_url")]
        public string? NextRecordsUrl { get; set; }
    }

    [HttpPost("PreviewObjectsV2")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<PreviewObjectsV2Output>> PreviewObjectsV2(
        [FromBody]
        PreviewObjectsV2Request previewObjectsV2Request)
    {
        var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (companyUser == null)
        {
            return Unauthorized();
        }

        var previewObjectsV2OutputOutput = await _providersApi.ProvidersPreviewObjectsV2PostAsync(
            previewObjectsV2Input: new PreviewObjectsV2Input(
                companyUser.CompanyId,
                previewObjectsV2Request.ProviderName,
                previewObjectsV2Request.ProviderConnectionId,
                previewObjectsV2Request.EntityTypeName,
                previewObjectsV2Request.Filters,
                previewObjectsV2Request.FieldFilters,
                previewObjectsV2Request.NextRecordsUrl));

        return new OkObjectResult(previewObjectsV2OutputOutput.Data);
    }

    public class InitProviderTypesSyncInput
    {
        [JsonProperty("provider_name")]
        [Required]
        public string ProviderName { get; set; }

        [JsonProperty("entity_type_name_to_sync_config_dict")]
        [Required]
        public Dictionary<string, SyncConfig> EntityTypeNameToSyncConfigDict { get; set; }
    }

    [HttpPost("InitProviderTypesSync")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult> InitProviderTypesSync(
        [FromBody]
        InitProviderTypesSyncInput initProviderTypesSyncInput)
    {
        var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (companyUser == null)
        {
            return Unauthorized();
        }

        var companyId = companyUser.CompanyId;

        await _providersApi.ProvidersInitProviderTypesSyncPostAsync(
            initProviderTypesSyncInput: new Sleekflow.Apis.CrmHub.Model.InitProviderTypesSyncInput(
                companyId,
                initProviderTypesSyncInput.EntityTypeNameToSyncConfigDict,
                initProviderTypesSyncInput.ProviderName));

        var domainName = _configuration.GetValue<string>("Values:DomainName");

        foreach (var entityTypeNameToSyncConfig in initProviderTypesSyncInput.EntityTypeNameToSyncConfigDict)
        {
            var getOnEntityCreatedWebhooksOutputOutput = await _webhooksApi.WebhooksGetWebhooksPostAsync(
                getWebhooksInput: new GetWebhooksInput(
                    companyId,
                    entityTypeNameToSyncConfig.Key,
                    "OnEntityCreated"));
            var hasNoOnEntityCreatedWebhook = getOnEntityCreatedWebhooksOutputOutput.Data
                .Webhooks
                .All(webhook => webhook.Url != (domainName + "/CrmHub/WebhookOnEntityCreated"));
            if (hasNoOnEntityCreatedWebhook)
            {
                await _webhooksApi.WebhooksRemoveWebhooksPostAsync(
                    removeWebhooksInput: new RemoveWebhooksInput(
                        companyId,
                        entityTypeNameToSyncConfig.Key,
                        "OnEntityCreated"));
                await _webhooksApi.WebhooksRegisterWebhookPostAsync(
                    registerWebhookInput: new RegisterWebhookInput(
                        companyId,
                        entityTypeNameToSyncConfig.Key,
                        "OnEntityCreated",
                        domainName + "/CrmHub/WebhookOnEntityCreated",
                        new Dictionary<string, object>()));
            }

            var getOnEntityFieldsChangedWebhooksOutputOutput = await _webhooksApi.WebhooksGetWebhooksPostAsync(
                getWebhooksInput: new GetWebhooksInput(
                    companyId,
                    entityTypeNameToSyncConfig.Key,
                    "OnEntityFieldsChanged"));
            var hasNoOnEntityFieldsChangedWebhook = getOnEntityFieldsChangedWebhooksOutputOutput.Data
                .Webhooks
                .All(webhook => webhook.Url != (domainName + "/CrmHub/WebhookOnEntityFieldsChanged"));
            if (hasNoOnEntityFieldsChangedWebhook)
            {
                await _webhooksApi.WebhooksRemoveWebhooksPostAsync(
                    removeWebhooksInput: new RemoveWebhooksInput(
                        companyId,
                        entityTypeNameToSyncConfig.Key,
                        "OnEntityFieldsChanged"));
                await _webhooksApi.WebhooksRegisterWebhookPostAsync(
                    registerWebhookInput: new RegisterWebhookInput(
                        companyId,
                        entityTypeNameToSyncConfig.Key,
                        "OnEntityFieldsChanged",
                        domainName + "/CrmHub/WebhookOnEntityFieldsChanged",
                        new Dictionary<string, object>()));
            }
        }

        foreach (var key in initProviderTypesSyncInput.EntityTypeNameToSyncConfigDict.Keys)
        {
            var getUnifyRulesOutputOutput = await _unifyRulesApi.UnifyRulesGetUnifyRulesPostAsync(
                getUnifyRulesInput: new Sleekflow.Apis.CrmHub.Model.GetUnifyRulesInput(
                    sleekflowCompanyId: companyId,
                    entityTypeName: key));
            var getUnifyRulesOutput = getUnifyRulesOutputOutput.Data;
            if (getUnifyRulesOutput.UnifyRules.Count == 0)
            {
                await _unifyRulesApi.UnifyRulesUpsertUnifyRulesPostAsync(
                    upsertUnifyRulesInput: new Sleekflow.Apis.CrmHub.Model.UpsertUnifyRulesInput(
                        companyId,
                        key,
                        new List<UnifyRuleDto>()));
            }
        }

        if (initProviderTypesSyncInput.ProviderName == "salesforce-integrator")
        {
            var hasNoContactToLeadRule = await _appDbContext.CompanyAssignmentRules
                .Where(r => r.CompanyId == companyId)
                .AllAsync(r => r.AssignmentRuleName != "Sleekflow Contact To Salesforce Lead");
            if (hasNoContactToLeadRule)
            {
                var contactToLeadRule = new AssignmentRule
                {
                    CompanyId = companyId,
                    AssignmentRuleName = $"Sleekflow Contact To Salesforce Lead",
                    AutomationType = AutomationType.ContactAdded,
                    Status = AutomationStatus.Live,
                    Conditions = new List<Condition>()
                    {
                        new Condition
                        {
                            FieldName = "PhoneNumber",
                            ConditionOperator = SupportedOperator.IsNotNull,
                            Values = new List<string>(),
                            NextOperator = SupportedNextOperator.And
                        },
                        new Condition
                        {
                            FieldName = "LastName",
                            ConditionOperator = SupportedOperator.IsNotNull,
                            Values = new List<string>(),
                            NextOperator = SupportedNextOperator.And
                        }
                    },
                    AutomationActions = new List<AutomationAction>
                    {
                        new AutomationAction
                        {
                            AutomatedTriggerType = AutomatedTriggerType.CrmHubAddContactToLead, CompanyId = companyId,
                        }
                    },
                    Order = 0,
                    IsContinue = true
                };
                _appDbContext.CompanyAssignmentRules.Add(contactToLeadRule);
            }
        }

        var hasNoSleekflowContactToCrmHubRule = await _appDbContext.CompanyAssignmentRules
            .Where(r => r.CompanyId == companyId)
            .AllAsync(r => r.AssignmentRuleName != "Sleekflow Contact To Crm Hub");
        if (hasNoSleekflowContactToCrmHubRule)
        {
            var sleekflowContactToCrmHubRule = new AssignmentRule
            {
                CompanyId = companyId,
                AssignmentRuleName = $"Sleekflow Contact To Crm Hub",
                AutomationType = AutomationType.CrmHubContactUpdated,
                Status = AutomationStatus.Live,
                Conditions = new List<Condition>(),
                AutomationActions = new List<AutomationAction>
                {
                    new AutomationAction
                    {
                        AutomatedTriggerType = AutomatedTriggerType.CrmHubAddOrUpdateContact, CompanyId = companyId,
                    }
                },
                Order = 0,
                IsContinue = true
            };
            _appDbContext.CompanyAssignmentRules.Add(sleekflowContactToCrmHubRule);
        }

        await _appDbContext.SaveChangesAsync();

        await _companyInfoCacheService.RemoveCompanyInfoCache(companyId);

        return new OkResult();
    }

    public class TriggerProviderSyncObjectsInput
    {
        [JsonProperty("provider_name")]
        [Required]
        public string ProviderName { get; set; }

        [JsonProperty("entity_type_name")]
        [Required]
        public string EntityTypeName { get; set; }
    }

    [HttpPost("TriggerProviderSyncObjects")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<TriggerProviderSyncObjectsOutput>> TriggerProviderSyncObjects(
        [FromBody]
        TriggerProviderSyncObjectsInput triggerProviderSyncObjectsInput)
    {
        var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (companyUser == null)
        {
            return Unauthorized();
        }

        var triggerProviderSyncObjectsOutputOutput = await _providersApi.ProvidersTriggerProviderSyncObjectsPostAsync(
            triggerProviderSyncObjectsInput: new Sleekflow.Apis.CrmHub.Model.TriggerProviderSyncObjectsInput(
                companyUser.CompanyId,
                triggerProviderSyncObjectsInput.ProviderName,
                triggerProviderSyncObjectsInput.EntityTypeName));

        return new OkObjectResult(triggerProviderSyncObjectsOutputOutput.Data);
    }

    public class GetProviderSyncObjectsProgressInput
    {
        [JsonProperty("provider_name")]
        [Required]
        public string ProviderName { get; set; }

        [JsonProperty("provider_state_id")]
        [Required]
        public string ProviderStateId { get; set; }
    }

    [HttpPost("GetProviderSyncObjectsProgress")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GetProviderSyncObjectsProgressOutput>> GetProviderSyncObjectsProgress(
        [FromBody]
        GetProviderSyncObjectsProgressInput getProviderSyncObjectsProgressInput)
    {
        var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (companyUser == null)
        {
            return Unauthorized();
        }

        var getProviderSyncObjectsProgressOutputOutput =
            await _providersApi.ProvidersGetProviderSyncObjectsProgressPostAsync(
                getProviderSyncObjectsProgressInput: new
                    Sleekflow.Apis.CrmHub.Model.GetProviderSyncObjectsProgressInput(
                        companyUser.CompanyId,
                        getProviderSyncObjectsProgressInput.ProviderName,
                        getProviderSyncObjectsProgressInput.ProviderStateId));

        return new OkObjectResult(getProviderSyncObjectsProgressOutputOutput.Data);
    }

    public class GetProviderConfigInput
    {
        [JsonProperty("provider_name")]
        [Required]
        public string ProviderName { get; set; }
    }

    [HttpPost("GetProviderConfig")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GetProviderConfigOutput>> GetProviderConfig(
        [FromBody]
        GetProviderConfigInput getProviderConfigInput)
    {
        var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (companyUser == null)
        {
            return Unauthorized();
        }

        var getProviderConfigOutputOutput = await _providersApi.ProvidersGetProviderConfigPostAsync(
            getProviderConfigInput: new Sleekflow.Apis.CrmHub.Model.GetProviderConfigInput(
                companyUser.CompanyId,
                getProviderConfigInput.ProviderName));

        return new OkObjectResult(getProviderConfigOutputOutput.Data);
    }

    [HttpPost("SyncObjectsToCrmHub")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<SyncObjectsToCrmHubOutput>> SyncObjectsToCrmHub()
    {
        var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (companyUser == null)
        {
            return Unauthorized();
        }

        companyUser.Company = await _appDbContext.CompanyCompanies
            .Where(x => x.Id == companyUser.CompanyId)
            .Include(x => x.CompanyIconFile)
            .Include(x => x.RolePermission)
            .Include(x => x.TwilioUsageRecords)
            .FirstOrDefaultAsync();

        companyUser.Company.FacebookConfigs = await _appDbContext.ConfigFacebookConfigs
            .Where(x => x.CompanyId == companyUser.CompanyId)
            .ToListAsync();
        companyUser.Company.WhatsAppConfigs = await _appDbContext.ConfigWhatsAppConfigs
            .Where(x => x.CompanyId == companyUser.CompanyId)
            .ToListAsync();
        companyUser.Company.EmailConfig = await _appDbContext.ConfigEmailConfigs
            .Where(x => x.Id == companyUser.Company.EmailConfigId)
            .FirstOrDefaultAsync();
        companyUser.Company.WeChatConfig = await _appDbContext.ConfigWeChatConfigs
            .Where(x => x.Id == companyUser.Company.WeChatConfigId)
            .FirstOrDefaultAsync();
        companyUser.Company.LineConfigs = await _appDbContext.ConfigLineConfigs
            .Where(x => x.CompanyId == companyUser.CompanyId)
            .ToListAsync();
        companyUser.Company.ViberConfigs = await _appDbContext.ConfigViberConfigs
            .Where(x => x.CompanyId == companyUser.CompanyId)
            .ToListAsync();
        companyUser.Company.TelegramConfigs = await _appDbContext.ConfigTelegramConfigs
            .Where(x => x.CompanyId == companyUser.CompanyId)
            .ToListAsync();
        companyUser.Company.CompanyHashtags = await _appDbContext.CompanyDefinedHashtags
            .Where(x => x.CompanyId == companyUser.CompanyId)
            .ToListAsync();
        companyUser.Company.SMSConfigs = await _appDbContext.ConfigSMSConfigs
            .Where(x => x.CompanyId == companyUser.CompanyId)
            .ToListAsync();
        companyUser.Company.ShopifyConfigs = await _appDbContext.ConfigShopifyConfigs
            .Where(x => x.CompanyId == companyUser.CompanyId)
            .ToListAsync();
        companyUser.Company.ShoplineConfigs = await _appDbContext.ConfigShoplineConfigs
            .Where(x => x.CompanyId == companyUser.CompanyId)
            .ToListAsync();
        companyUser.Company.InstagramConfigs = await _appDbContext.ConfigInstagramConfigs
            .Where(x => x.CompanyId == companyUser.CompanyId)
            .ToListAsync();
        companyUser.Company.WhatsApp360DialogConfigs = await _appDbContext.ConfigWhatsApp360DialogConfigs
            .Where(x => x.CompanyId == companyUser.CompanyId)
            .ToListAsync();
        companyUser.Company.WhatsApp360DialogUsageRecords = await _appDbContext.CompanyWhatsApp360DialogUsageRecords
            .Where(x => x.CompanyId == companyUser.CompanyId)
            .ToListAsync();
        companyUser.Company.CompanyCustomFields = await _appDbContext.CompanyCompanyCustomFields
            .Where(x => x.CompanyId == companyUser.CompanyId).Include(x => x.CompanyCustomFieldFieldLinguals)
            .ToListAsync();

        companyUser.Company.CustomUserProfileFields = await _appDbContext.CompanyCustomUserProfileFields
            .Where(x => x.CompanyId == companyUser.CompanyId).Include(x => x.CustomUserProfileFieldOptions)
            .ThenInclude(ling => ling.CustomUserProfileFieldOptionLinguals)
            .Include(x => x.CustomUserProfileFieldLinguals)
            .ToListAsync();
        companyUser.Company.CustomUserProfileFields = companyUser.Company.CustomUserProfileFields
            .OrderBy(x => x.Order)
            .ToList();
        companyUser.Company.CustomUserProfileFields
            .ForEach(
                x =>
                    x.CustomUserProfileFieldOptions =
                        x.CustomUserProfileFieldOptions.OrderBy(z => z.Order).ThenBy(z => z.Value).ToList());

        var dict = new Dictionary<string, string>();
        foreach (var companyCustomUserProfileField in companyUser.Company.CustomUserProfileFields)
        {
            dict[companyCustomUserProfileField.Id] = companyCustomUserProfileField.FieldName;
        }

        var userProfiles = await _userProfileService.GetManageableUserProfilesQueryable(companyUser)
            .Include(x => x.CustomFields).ThenInclude(x => x.CompanyDefinedField)
            .Include(x => x.FacebookAccount)
            .Include(x => x.SMSUser)
            .Include(x => x.WhatsAppAccount)
            .Include(x => x.UserDevice)
            .Include(x => x.RegisteredUser.Identity)
            .Include(x => x.WebClient)
            .Include(x => x.EmailAddress)
            .Include(x => x.WeChatUser)
            .Include(x => x.LineUser)
            .Include(x => x.InstagramUser)
            .Include(x => x.ViberUser)
            .Include(x => x.TelegramUser)
            .Include(x => x.WhatsApp360DialogUser)
            .Include(x => x.WhatsappCloudApiUser)
            .Where(x => !x.IsShopifyProfile)
            .Skip(0)
            .Take(2000)
            .ToListAsync();

        var userProfileDicts = new List<Dictionary<string, object>>();
        foreach (var userProfile in userProfiles)
        {
            var userProfileDict =
                _mapper.Map<Dictionary<string, object>>(_mapper.Map<UserProfileDto>(userProfile));

            foreach (var userProfileCustomField in userProfile.CustomFields)
            {
                userProfileDict[
                        dict.GetValueOrDefault(
                            userProfileCustomField.CompanyDefinedFieldId,
                            userProfileCustomField.CompanyDefinedFieldId)]
                    = userProfileCustomField.Value;
            }

            userProfileDict.Remove("CustomFields");

            userProfileDicts.Add(userProfileDict);
        }

        foreach (var userProfileDict in userProfileDicts)
        {
            await _objectsApi.ObjectsUpsertObjectPostAsync(
                upsertObjectInput: new UpsertObjectInput(
                    companyUser.CompanyId,
                    "sleekflow",
                    "Contact",
                    userProfileDict));
        }

        // TODO
        return new SyncObjectsToCrmHubOutput
        {
            Company = _mapper.Map<CompanyDto>(companyUser.Company),
            UserProfiles = new List<Dictionary<string, object>>()
        };
    }

    public class PropagateObjectUpdateToProviderInput
    {
        [JsonProperty("provider_name")]
        [Required]
        public string ProviderName { get; set; }

        [JsonProperty("entity_type_name")]
        [Required]
        public string EntityTypeName { get; set; }

        [JsonProperty("object_id")]
        [Required]
        public string ObjectId { get; set; }
    }

    [HttpPost("PropagateObjectUpdateToProvider")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult> PropagateObjectUpdateToProvider(
        [FromBody]
        PropagateObjectUpdateToProviderInput propagateObjectUpdateToProviderInput)
    {
        var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (companyUser == null)
        {
            return Unauthorized();
        }
        var propagateObjectUpdateToProviderOutputOutput =
            await _providersApi.ProvidersPropagateObjectUpdateToProviderPostAsync(
                propagateObjectUpdateToProviderInput:
                new Sleekflow.Apis.CrmHub.Model.PropagateObjectUpdateToProviderInput(
                    companyUser.CompanyId,
                    propagateObjectUpdateToProviderInput.ProviderName,
                    propagateObjectUpdateToProviderInput.EntityTypeName,
                    propagateObjectUpdateToProviderInput.ObjectId));

        return new OkResult();
    }

    public class GetConversationByObjectInput
    {
        [JsonProperty("entity_type_name")]
        [Required]
        public string EntityTypeName { get; set; }

        [JsonProperty("object_id")]
        [Required]
        public string ObjectId { get; set; }
    }

    public class GetConversationByObjectOutput
    {
        [JsonProperty("conversation_id")]
        public string ConversationId { get; set; }
    }

    [UpdateUserProfileTriggerSource(UpdateUserProfileTriggerSource.CrmHubAction)]
    [HttpPost("GetConversationByObject")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult> GetConversationByObject(
        [FromBody]
        GetConversationByObjectInput getConversationByObjectInput)
    {
        var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (companyUser == null)
        {
            return Unauthorized();
        }

        try
        {
            var companyId = companyUser.CompanyId;
            var crmHubObjectId = getConversationByObjectInput.ObjectId;

            var getObjectsByIdentitiesOutputOutput = await _objectsApi.ObjectsGetObjectsByIdentitiesPostAsync(
                getObjectsByIdentitiesInput: new Sleekflow.Apis.CrmHub.Model.GetObjectsByIdentitiesInput(
                    companyId,
                    new List<Identity>
                    {
                        new Identity(objectId: crmHubObjectId)
                    },
                    getConversationByObjectInput.EntityTypeName,
                    new List<Sort>()));

            var objects = getObjectsByIdentitiesOutputOutput.Data.Records;
            if (objects.Count == 0)
            {
                throw new Exception("No object found.");
            }

            var crmHubEntity = await _appDbContext
                .CrmHubEntities
                .FirstOrDefaultAsync(che => che.EntityId == crmHubObjectId);
            if (crmHubEntity != null)
            {
                var conversation = await _userProfileService.GetConversationByUserProfileId(
                    companyId,
                    crmHubEntity.UserProfileId,
                    "closed",
                    false);

                return new OkObjectResult(
                    new GetConversationByObjectOutput
                    {
                        ConversationId = conversation.Id
                    });
            }

            // Take only the first matched object from CrmHub
            // Normally, there should be only one matched
            var userProfiles =
                await _crmHubService.AddUserProfilesByContactObjectDictAsync(companyId, objects[0], crmHubObjectId);

            // Pick the first one as the conversation
            var newConversation = await _userProfileService.GetConversationByUserProfileId(
                companyId,
                userProfiles[0].Id,
                "closed",
                false);

            return new OkObjectResult(
                new GetConversationByObjectOutput
                {
                    ConversationId = newConversation.Id
                });
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "Failed GetConversationByObject getConversationByObjectInput=[{GetConversationByObjectInput}].",
                JsonConvert.SerializeObject(getConversationByObjectInput));

            return BadRequest(
                new ResponseViewModel
                {
                    message = ex.Message
                });
        }
    }

    public class GetObjectsInput
    {
        [JsonProperty("continuation_token")]
        public string ContinuationToken { get; set; }

        [JsonProperty("entity_type_name")]
        [Required]
        public string EntityTypeName { get; set; }

        [JsonProperty("limit")]
        [Required]
        [Range(1, 200)]
        public int Limit { get; set; }

        [JsonProperty("filter_groups")]
        [Required]
        public List<GetObjectsInputFilterGroup> FilterGroups { get; set; }

        [JsonProperty("sorts")]
        [Required]
        public List<Sort> Sorts { get; set; }

        [JsonProperty("expands")]
        public List<GetObjectsV2InputExpand> Expands { get; set; }
    }

    [HttpPost("GetObjects")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GetObjectsOutput>> GetObjects(
        [FromBody]
        GetObjectsInput getObjectsInput)
    {
        var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (companyUser == null)
        {
            return Unauthorized();
        }

        if (getObjectsInput.Expands != null)
        {
            var getObjectsOutputOutput = await _objectsApi.ObjectsGetObjectsV2PostAsync(
                getObjectsV2Input: new Sleekflow.Apis.CrmHub.Model.GetObjectsV2Input(
                    getObjectsInput.ContinuationToken,
                    companyUser.CompanyId,
                    getObjectsInput.EntityTypeName,
                    getObjectsInput.Limit,
                    getObjectsInput.FilterGroups
                        .Select(
                            fg =>
                                new GetObjectsV2InputFilterGroup(
                                    fg.Filters
                                        .Select(
                                            f => new Filter(
                                                f.FieldName,
                                                f.Operator,
                                                f.Value))
                                        .ToList()))
                        .ToList(),
                    getObjectsInput.Sorts,
                    getObjectsInput.Expands));

            return new OkObjectResult(getObjectsOutputOutput.Data);
        }
        else
        {
            var getObjectsOutputOutput = await _objectsApi.ObjectsGetObjectsPostAsync(
                getObjectsInput: new Sleekflow.Apis.CrmHub.Model.GetObjectsInput(
                    getObjectsInput.ContinuationToken,
                    companyUser.CompanyId,
                    getObjectsInput.EntityTypeName,
                    getObjectsInput.Limit,
                    getObjectsInput.FilterGroups,
                    getObjectsInput.Sorts));

            return new OkObjectResult(getObjectsOutputOutput.Data);
        }
    }

    public class GetObjectsV2Input
    {
        [JsonProperty("continuation_token")]
        public string ContinuationToken { get; set; }

        [JsonProperty("entity_type_name")]
        [Required]
        public string EntityTypeName { get; set; }

        [JsonProperty("limit")]
        [Required]
        [Range(1, 200)]
        public int Limit { get; set; }

        [JsonProperty("filter_groups")]
        [Required]
        public List<GetObjectsV3InputFilterGroup> FilterGroups { get; set; }

        [JsonProperty("sorts")]
        [Required]
        public List<Sort> Sorts { get; set; }

        [JsonProperty("expands")]
        [Required]
        public List<GetObjectsV3InputExpand> Expands { get; set; }
    }

    [HttpPost("GetObjectsV2")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GetObjectsV2Output>> GetObjectsV2(
        [FromBody]
        GetObjectsV2Input getObjectsInput)
    {
        var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (companyUser == null)
        {
            return Unauthorized();
        }

        var getObjectsOutputOutput = await _objectsApi.ObjectsGetObjectsV3PostAsync(
            getObjectsV3Input: new GetObjectsV3Input(
                getObjectsInput.ContinuationToken,
                companyUser.CompanyId,
                getObjectsInput.EntityTypeName,
                getObjectsInput.Limit,
                getObjectsInput.FilterGroups,
                getObjectsInput.Sorts,
                getObjectsInput.Expands));

        return new OkObjectResult(getObjectsOutputOutput.Data);
    }

    public class GetObjectsCountInput
    {
        [JsonProperty("entity_type_name")]
        [Required]
        public string EntityTypeName { get; set; }

        [JsonProperty("filter_groups")]
        [Required]
        public List<GetObjectsCountInputFilterGroup> FilterGroups { get; set; }
    }

    public class GetObjectsCountOutput
    {
        [JsonProperty("count", NullValueHandling = NullValueHandling.Include)]
        public long Count { get; set; }
    }

    [HttpPost("GetObjectsCount")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GetObjectsCountOutput>> GetObjectsCount(
        [FromBody]
        GetObjectsCountInput getObjectsCountInput)
    {
        var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (companyUser == null)
        {
            return Unauthorized();
        }

        var getObjectsCountOutputOutput = await _objectsApi.ObjectsGetObjectsCountPostAsync(
            getObjectsCountInput: new Sleekflow.Apis.CrmHub.Model.GetObjectsCountInput(
                companyUser.CompanyId,
                getObjectsCountInput.EntityTypeName,
                getObjectsCountInput.FilterGroups));

        return new OkObjectResult(
            new GetObjectsCountOutput
            {
                Count = getObjectsCountOutputOutput.Data.Count
            });
    }

    public class GetObjectsCountV2Input
    {
        [JsonProperty("entity_type_name")]
        [Required]
        public string EntityTypeName { get; set; }

        [JsonProperty("filter_groups")]
        [Required]
        public List<GetObjectsCountV2InputFilterGroup> FilterGroups { get; set; }

        [JsonProperty("group_bys")]
        [Required]
        public List<GroupBy> GroupBys { get; set; }
    }

    public class GetObjectsCountV2Output
    {
        [JsonProperty("count", NullValueHandling = NullValueHandling.Include)]
        public long Count { get; set; }

        [JsonProperty("aggregated_counts", NullValueHandling = NullValueHandling.Include)]
        public List<Dictionary<string, object>> AggregatedCounts { get; set; }
    }

    [HttpPost("GetObjectsCountV2")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GetObjectsCountV2Output>> GetObjectsCountV2(
        [FromBody]
        GetObjectsCountV2Input getObjectsCountV2Input)
    {
        var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (companyUser == null)
        {
            return Unauthorized();
        }

        var getObjectsCountV2OutputOutput = await _objectsApi.ObjectsGetObjectsCountV2PostAsync(
            getObjectsCountV2Input: new Sleekflow.Apis.CrmHub.Model.GetObjectsCountV2Input(
                companyUser.CompanyId,
                getObjectsCountV2Input.EntityTypeName,
                getObjectsCountV2Input.GroupBys,
                getObjectsCountV2Input.FilterGroups));
        var getObjectsCountV2Output = getObjectsCountV2OutputOutput.Data;

        return new OkObjectResult(
            new GetObjectsCountV2Output
            {
                Count = getObjectsCountV2Output.Count,
                AggregatedCounts = getObjectsCountV2Output.AggregatedCounts
                    .Select(ac => ac.ToDictionary(kvp => kvp.Key, kvp => kvp.Value))
                    .ToList()
            });
    }

    public class GetObjectsByIdentitiesInputIdentity
    {
        [JsonProperty("phone_number")]
        public string PhoneNumber { get; set; }

        [JsonProperty("email")]
        public string Email { get; set; }

        [JsonProperty("provider_object_id")]
        public string ProviderObjectId { get; set; }

        [JsonProperty("object_id")]
        public string ObjectId { get; set; }
    }

    public class GetObjectsByIdentitiesInput
    {
        [JsonProperty("identities")]
        [Required]
        public List<GetObjectsByIdentitiesInputIdentity> Identities { get; set; }

        [JsonProperty("entity_type_name")]
        [Required]
        public string EntityTypeName { get; set; }

        [JsonProperty("sorts")]
        [Required]
        public List<Sort> Sorts { get; set; }
    }

    [HttpPost("GetObjectsByIdentities")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GetObjectsByIdentitiesOutput>> GetObjectsByIdentities(
        [FromBody]
        GetObjectsByIdentitiesInput getObjectsByIdentitiesInput)
    {
        var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (companyUser == null)
        {
            return Unauthorized();
        }

        var getObjectsByIdentitiesOutputOutput = await _objectsApi.ObjectsGetObjectsByIdentitiesPostAsync(
            getObjectsByIdentitiesInput: new Sleekflow.Apis.CrmHub.Model.GetObjectsByIdentitiesInput(
                companyUser.CompanyId,
                getObjectsByIdentitiesInput.Identities
                    .Select(i => new Identity(i.PhoneNumber, i.Email, i.ProviderObjectId, i.ObjectId))
                    .ToList(),
                getObjectsByIdentitiesInput.EntityTypeName,
                getObjectsByIdentitiesInput.Sorts));

        return new OkObjectResult(getObjectsByIdentitiesOutputOutput.Data);
    }

    public class GetProviderObjectsCountInput
    {
        [JsonProperty("entity_type_name")]
        [Required]
        public string EntityTypeName { get; set; }

        [JsonProperty("provider_name")]
        [Required]
        public string ProviderName { get; set; }

        [JsonProperty("filters")]
        [Required]
        public List<SyncConfigFilter> Filters { get; set; }
    }

    [HttpPost("GetProviderObjectsCount")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GetProviderObjectsCountOutput>> GetProviderObjectsCount(
        [FromBody]
        GetProviderObjectsCountInput getProviderObjectsCountInput)
    {
        var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (companyUser == null)
        {
            return Unauthorized();
        }

        var getProviderObjectsCountOutputOutput = await _providersApi.ProvidersGetProviderObjectsCountPostAsync(
            getProviderObjectsCountInput: new Sleekflow.Apis.CrmHub.Model.GetProviderObjectsCountInput(
                companyUser.CompanyId,
                getProviderObjectsCountInput.ProviderName,
                getProviderObjectsCountInput.EntityTypeName,
                getProviderObjectsCountInput.Filters,
                fieldFilters: null));

        return new OkObjectResult(getProviderObjectsCountOutputOutput.Data);
    }

    public class GetTypeFieldsInput
    {
        [JsonProperty("entity_type_name")]
        [Required]
        public string EntityTypeName { get; set; }
    }

    [HttpPost("GetTypeFields")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GetTypeFieldsOutput>> GetTypeFields(
        [FromBody]
        GetTypeFieldsInput getTypeFieldsInput)
    {
        var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (companyUser == null)
        {
            return Unauthorized();
        }

        var getTypeFieldsOutputOutput = await _objectsApi.ObjectsGetTypeFieldsPostAsync(
            getTypeFieldsInput: new Sleekflow.Apis.CrmHub.Model.GetTypeFieldsInput(
                companyUser.CompanyId,
                getTypeFieldsInput.EntityTypeName));

        return new OkObjectResult(getTypeFieldsOutputOutput.Data);
    }

    public class GetTypeFieldValuesInput
    {
        [JsonProperty("entity_type_name")]
        [Required]
        public string EntityTypeName { get; set; }

        [JsonProperty("field_name")]
        [Required]
        public string FieldName { get; set; }
    }

    [HttpPost("GetTypeFieldValues")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GetTypeFieldValuesOutput>> GetTypeFieldValues(
        [FromBody]
        GetTypeFieldValuesInput getTypeFieldValuesInput)
    {
        var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (companyUser == null)
        {
            return Unauthorized();
        }

        var getTypeFieldValuesOutputOutput = await _objectsApi.ObjectsGetTypeFieldValuesPostAsync(
            getTypeFieldValuesInput: new Sleekflow.Apis.CrmHub.Model.GetTypeFieldValuesInput(
                companyUser.CompanyId,
                getTypeFieldValuesInput.EntityTypeName,
                getTypeFieldValuesInput.FieldName));

        return new OkObjectResult(getTypeFieldValuesOutputOutput.Data);
    }

    public class GetProviderObjectDirectRefUrlInput
    {
        [JsonProperty("provider_name")]
        [Required]
        public string ProviderName { get; set; }

        [JsonProperty("entity_type_name")]
        [Required]
        public string EntityTypeName { get; set; }

        [JsonProperty("object_id")]
        [Required]
        public string ObjectId { get; set; }
    }

    [HttpPost("GetProviderObjectDirectRefUrl")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GetProviderObjectDirectRefUrlOutput>> GetProviderObjectDirectRefUrl(
        [FromBody]
        GetProviderObjectDirectRefUrlInput getProviderObjectDirectRefUrlInput)
    {
        var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (companyUser == null)
        {
            return Unauthorized();
        }

        var getProviderObjectDirectRefUrlOutputOutput =
            await _providersApi.ProvidersGetProviderObjectDirectRefUrlPostAsync(
                getProviderObjectDirectRefUrlInput: new Sleekflow.Apis.CrmHub.Model.GetProviderObjectDirectRefUrlInput(
                    companyUser.CompanyId,
                    getProviderObjectDirectRefUrlInput.ProviderName,
                    getProviderObjectDirectRefUrlInput.EntityTypeName,
                    getProviderObjectDirectRefUrlInput.ObjectId));

        return new OkObjectResult(getProviderObjectDirectRefUrlOutputOutput.Data);
    }

    public class UpdateUserMappingsInput
    {
        [JsonProperty("crm_hub_user_object_id_to_user_id_dict")]
        [Required]
        public Dictionary<string, string> CrmHubUserObjectIdToUserIdDict { get; set; }
    }

    [HttpPost("UpdateUserMappings")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult> UpdateUserMappings(
        [FromBody]
        UpdateUserMappingsInput updateUserMappingsInput)
    {
        var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (companyUser == null)
        {
            return Unauthorized();
        }

        var idToStaffDict = await _appDbContext.UserRoleStaffs
            .Where(x => x.CompanyId == companyUser.CompanyId && x.Id != 1)
            .ToDictionaryAsync(s => s.IdentityId, s => s);

        var hasInvalidUserIds = updateUserMappingsInput.CrmHubUserObjectIdToUserIdDict
            .Any(pair => idToStaffDict.ContainsKey(pair.Value) == false);
        if (hasInvalidUserIds)
        {
            throw new Exception("There are invalid users.");
        }

        var hasDuplicateUserIds = updateUserMappingsInput.CrmHubUserObjectIdToUserIdDict.Values.Distinct().Count()
                                  != updateUserMappingsInput.CrmHubUserObjectIdToUserIdDict.Values.Count;
        if (hasDuplicateUserIds)
        {
            throw new Exception("The users have to be unique.");
        }

        var getObjectsOutputOutput = await _objectsApi.ObjectsGetObjectsPostAsync(
            getObjectsInput: new Sleekflow.Apis.CrmHub.Model.GetObjectsInput(
                null!,
                companyUser.CompanyId,
                "User",
                200,
                new List<GetObjectsInputFilterGroup>
                {
                    new GetObjectsInputFilterGroup(
                        new List<Filter>()
                        {
                            new Filter("unified:SleekflowId", _operator: "!=", value: null!)
                        })
                },
                new List<Sort>()));

        var crmHubUserIdToSleekflowUserIdDict = new Dictionary<string, string>();
        foreach (var record in getObjectsOutputOutput.Data.Records)
        {
            var sleekflowUserId = record.GetValueOrDefault("sleekflow:Id");
            if (sleekflowUserId != null)
            {
                crmHubUserIdToSleekflowUserIdDict[(string) record["id"]] = (string) sleekflowUserId;
            }
        }

        // Remove All
        foreach (var pair in crmHubUserIdToSleekflowUserIdDict)
        {
            await _objectsApi.ObjectsUnassociateObjectPostAsync(
                unassociateObjectInput: new UnassociateObjectInput(
                    companyUser.CompanyId,
                    "User",
                    pair.Key,
                    pair.Value,
                    "sleekflow"));
        }

        // Add All
        foreach (var pair in updateUserMappingsInput.CrmHubUserObjectIdToUserIdDict.Where(p => p.Value != null))
        {
            await _objectsApi.ObjectsAssociateObjectPostAsync(
                associateObjectInput: new AssociateObjectInput(
                    companyUser.CompanyId,
                    "User",
                    pair.Key,
                    pair.Value,
                    "sleekflow"));
        }

        return new OkResult();
    }

    public class GetUserMappingsOutput
    {
        [JsonProperty("crm_hub_user_id_to_staff_dict")]
        public Dictionary<string, StaffDto> CrmHubUserIdToStaffDict { get; set; }

        [JsonProperty("crm_hub_user_id_to_provider_ids_dict")]
        public Dictionary<string, List<string>> CrmHubUserIdToProviderIdsDict { get; set; }

        [JsonProperty("provider_id_to_crm_hub_user_mappings_dict")]
        public Dictionary<string, GetUserMappingsProviderIdMapping> ProviderIdToCrmHubUserMappingsDict { get; set; }
    }

    public class GetUserMappingsProviderIdMapping
    {
        [JsonProperty("main_crm_hub_user_id")]
        public string MainCrmHubUserId { get; set; }

        [JsonProperty("all_crm_hub_user_ids")]
        public List<string> AllCrmHubUserIds { get; set; }
    }

    public class StaffDto
    {
        [JsonProperty("staff_id")]
        public long StaffId { get; set; }

        [JsonProperty("user_id")]
        public string UserId { get; set; }

        [JsonProperty("first_name")]
        public string FirstName { get; set; }

        [JsonProperty("last_name")]
        public string LastName { get; set; }

        [JsonProperty("display_id")]
        public string DisplayName { get; set; }

        [JsonProperty("user_name")]
        public string UserName { get; set; }

        [JsonProperty("email")]
        public string Email { get; set; }

        [JsonProperty("phone_number")]
        public string PhoneNumber { get; set; }

        [JsonProperty("position")]
        public string Position { get; set; }

        [JsonProperty("created_at")]
        public DateTime CreatedAt { get; set; }

        [JsonProperty("last_login_at")]
        public DateTime LastLoginAt { get; set; }

        [JsonProperty("email_confirmed")]
        public bool EmailConfirmed { get; set; }

        [JsonProperty("status")]
        [JsonConverter(typeof(StringEnumConverter))]
        public StaffStatus Status { get; set; }

        [JsonProperty("role_type")]
        [JsonConverter(typeof(StringEnumConverter))]
        public StaffUserRole RoleType { get; set; }

        [JsonProperty("time_zone_info_id")]
        public string TimeZoneInfoId { get; set; }
    }

    [HttpPost("GetUserMappings")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult> GetUserMappings()
    {
        var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (companyUser == null)
        {
            return Unauthorized();
        }

        var getObjectsOutputOutput = await _objectsApi.ObjectsGetObjectsPostAsync(
            getObjectsInput: new Sleekflow.Apis.CrmHub.Model.GetObjectsInput(
                null!,
                companyUser.CompanyId,
                "User",
                200,
                new List<GetObjectsInputFilterGroup>
                {
                    new GetObjectsInputFilterGroup(
                        new List<Filter>()
                        {
                            new Filter("unified:SleekflowId", _operator: "!=", value: null!)
                        })
                },
                new List<Sort>()));

        var (
            crmHubUserIdToProviderIds,
            providerIdToCrmHubUserIds,
            crmHubUserIdToSleekflowUserIdDict,
            sleekflowUserIdToCrmHubUserIdDict) = GetUserDicts(getObjectsOutputOutput);

        var crmHubUserIdToStaffDto = await _appDbContext.UserRoleStaffs
            .Where(
                x => x.CompanyId == companyUser.CompanyId && x.Id != 1 &&
                     crmHubUserIdToSleekflowUserIdDict.Values.Contains(x.IdentityId))
            .Include(x => x.Identity)
            .OrderBy(x => x.Order)
            .ThenBy(x => x.Id)
            .Select(
                x => new StaffDto
                {
                    StaffId = x.Id,
                    UserId = x.Identity.Id,
                    FirstName = x.Identity.FirstName,
                    LastName = x.Identity.LastName,
                    DisplayName = x.Identity.DisplayName,
                    UserName = x.Identity.UserName,
                    Email = x.Identity.Email,
                    EmailConfirmed = x.Identity.EmailConfirmed,
                    PhoneNumber = x.Identity.PhoneNumber,
                    Status = x.Status,
                    CreatedAt = x.Identity.CreatedAt,
                    LastLoginAt = x.Identity.LastLoginAt,
                    RoleType = x.RoleType,
                    TimeZoneInfoId = x.TimeZoneInfoId,
                    Position = x.Position
                })
            .ToDictionaryAsync(e => sleekflowUserIdToCrmHubUserIdDict[e.UserId], e => e);

        return new OkObjectResult(
            new GetUserMappingsOutput
            {
                CrmHubUserIdToStaffDict = crmHubUserIdToStaffDto,
                CrmHubUserIdToProviderIdsDict = crmHubUserIdToProviderIds,
                ProviderIdToCrmHubUserMappingsDict = providerIdToCrmHubUserIds.ToDictionary(
                    pair => pair.Key,
                    pair => new GetUserMappingsProviderIdMapping
                    {
                        MainCrmHubUserId = pair.Value.FirstOrDefault(), AllCrmHubUserIds = pair.Value
                    }),
            });
    }

    public class GetProviderConnectionsInput
    {
        [JsonProperty("provider_name")]
        [Required]
        public string ProviderName { get; set; }
    }

    [HttpPost("GetProviderConnections")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GetProviderConnectionsOutput>> GetProviderConnections(
        [FromBody]
        GetProviderConnectionsInput getProviderConnectionsInput)
    {
        var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (companyUser == null)
        {
            return Unauthorized();
        }

        var getProviderConnectionsOutput =
            await _providersApi.ProvidersGetProviderConnectionsPostAsync(
                getProviderConnectionsInput: new Sleekflow.Apis.CrmHub.Model.GetProviderConnectionsInput(
                    companyUser.CompanyId,
                    getProviderConnectionsInput.ProviderName));

        return new OkObjectResult(getProviderConnectionsOutput.Data);
    }

    public class InitProviderV2Input
    {
        [JsonProperty("provider_name")]
        [Required]
        public string ProviderName { get; set; }

        [JsonProperty("success_url")]
        [Required]
        public string SuccessUrl { get; set; }

        [JsonProperty("failure_url")]
        [Required]
        public string FailureUrl { get; set; }

        [JsonProperty("additional_details")]
        public Dictionary<string, object> AdditionalDetails { get; set; }
    }

    [HttpPost("InitProviderV2")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<InitProviderV2Output>> InitProviderV2(
        [FromBody]
        InitProviderV2Input initProviderV2Input)
    {
        var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (companyUser == null)
        {
            return Unauthorized();
        }

        var initProviderV2Output =
            await _providersApi.ProvidersInitProviderV2PostAsync(
                initProviderV2Input: new Sleekflow.Apis.CrmHub.Model.InitProviderV2Input(
                    companyUser.CompanyId,
                    initProviderV2Input.ProviderName,
                    initProviderV2Input.SuccessUrl,
                    initProviderV2Input.FailureUrl,
                    "ZZ",
                    initProviderV2Input.AdditionalDetails));

        var crmSourceProviderNameField = await _appDbContext.CompanyCustomUserProfileFields
            .FirstOrDefaultAsync(
                x => x.CompanyId == companyUser.CompanyId
                     && x.Type == FieldDataType.CrmSourceProviderName
                     && x.FieldsCategory == FieldsCategory.CRM
                     && x.IsDefault == true);

        if (crmSourceProviderNameField is null)
        {
            crmSourceProviderNameField = new CompanyCustomUserProfileField
            {
                CompanyId = companyUser.CompanyId,
                FieldName = "CRM Source Provider Name",
                Type = FieldDataType.CrmSourceProviderName,
                Order = 2001,
                IsDeletable = false,
                IsEditable = false,
                IsVisible = false,
                IsDefault = true,
                FieldsCategory = FieldsCategory.CRM
            };

            await _appDbContext.CompanyCustomUserProfileFields.AddAsync(crmSourceProviderNameField);
        }

        var crmSourceObjectIdField = await _appDbContext.CompanyCustomUserProfileFields
            .FirstOrDefaultAsync(
                x => x.CompanyId == companyUser.CompanyId
                     && x.Type == FieldDataType.CrmSourceObjectId
                     && x.FieldsCategory == FieldsCategory.CRM
                     && x.IsDefault == true);

        if (crmSourceObjectIdField is null)
        {
            crmSourceObjectIdField = new CompanyCustomUserProfileField
            {
                CompanyId = companyUser.CompanyId,
                FieldName = "CRM Source Object Id",
                Type = FieldDataType.CrmSourceObjectId,
                Order = 2000,
                IsDeletable = false,
                IsEditable = false,
                IsVisible = false,
                IsDefault = true,
                FieldsCategory = FieldsCategory.CRM
            };

            await _appDbContext.CompanyCustomUserProfileFields.AddAsync(crmSourceObjectIdField);
        }

        await _appDbContext.SaveChangesAsync();

        return new OkObjectResult(initProviderV2Output.Data);
    }

    public class ReInitProviderConnectionInput
    {
        [JsonProperty("provider_name")]
        [Required]
        public string ProviderName { get; set; }

        [JsonProperty("provider_connection_id")]
        [Required]
        public string ProviderConnectionId { get; set; }

        [JsonProperty("success_url")]
        [Required]
        public string SuccessUrl { get; set; }

        [JsonProperty("failure_url")]
        [Required]
        public string FailureUrl { get; set; }

        [JsonProperty("additional_details")]
        public Dictionary<string, object> AdditionalDetails { get; set; }
    }

    [HttpPost("ReInitProviderConnection")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<ReInitProviderConnectionOutput>> ReInitProviderConnection(
        [FromBody]
        ReInitProviderConnectionInput reInitProviderConnectionInput)
    {
        var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (companyUser == null)
        {
            return Unauthorized();
        }

        var reInitProviderConnectionOutput =
            await _providersApi.ProvidersReInitProviderConnectionPostAsync(
                reInitProviderConnectionInput: new Sleekflow.Apis.CrmHub.Model.ReInitProviderConnectionInput(
                    companyUser.CompanyId,
                    reInitProviderConnectionInput.ProviderName,
                    reInitProviderConnectionInput.ProviderConnectionId,
                    reInitProviderConnectionInput.SuccessUrl,
                    reInitProviderConnectionInput.FailureUrl));

        return new OkObjectResult(reInitProviderConnectionOutput.Data);
    }

    public class RenameProviderConnectionInput
    {
        [JsonProperty("provider_name")]
        [Required]
        public string ProviderName { get; set; }

        [JsonProperty("provider_connection_id")]
        [Required]
        public string ProviderConnectionId { get; set; }

        [JsonProperty("name")]
        [Required]
        public string Name { get; set; }
    }

    [HttpPost("RenameProviderConnection")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<RenameProviderConnectionOutput>> RenameProviderConnection(
        [FromBody]
        RenameProviderConnectionInput renameProviderConnectionInput)
    {
        var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (companyUser == null)
        {
            return Unauthorized();
        }

        var renameProviderConnectionOutput =
            await _providersApi.ProvidersRenameProviderConnectionPostAsync(
                renameProviderConnectionInput: new Sleekflow.Apis.CrmHub.Model.RenameProviderConnectionInput(
                    companyUser.CompanyId,
                    renameProviderConnectionInput.ProviderName,
                    renameProviderConnectionInput.ProviderConnectionId,
                    renameProviderConnectionInput.Name));

        return new OkObjectResult(renameProviderConnectionOutput.Data);
    }

    public class DeleteProviderConnectionInput
    {
        [JsonProperty("provider_name")]
        [Required]
        public string ProviderName { get; set; }

        [JsonProperty("provider_connection_id")]
        [Required]
        public string ProviderConnectionId { get; set; }
    }

    [HttpPost("DeleteProviderConnection")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<DeleteProviderConnectionOutputOutput>> DeleteProviderConnection(
        [FromBody]
        DeleteProviderConnectionInput deleteProviderConnectionInput)
    {
        var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (companyUser == null)
        {
            return Unauthorized();
        }

        var deleteProviderConnectionOutput =
            await _providersApi.ProvidersDeleteProviderConnectionPostAsync(
                deleteProviderConnectionInput: new Sleekflow.Apis.CrmHub.Model.DeleteProviderConnectionInput(
                    companyUser.CompanyId,
                    deleteProviderConnectionInput.ProviderName,
                    deleteProviderConnectionInput.ProviderConnectionId));

        return new OkObjectResult(deleteProviderConnectionOutput);
    }

    public class GetProviderSubscriptionsInput
    {
        [JsonProperty("provider_name")]
        [Required]
        public string ProviderName { get; set; }

        [JsonProperty("provider_connection_id")]
        [Required]
        public string ProviderConnectionId { get; set; }

        [JsonProperty("entity_type_name")]
        [Required]
        public string EntityTypeName { get; set; }
    }

    [HttpPost("GetProviderSubscriptions")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GetProviderSubscriptionsOutput>> GetProviderSubscriptions(
        [FromBody]
        GetProviderSubscriptionsInput getProviderSubscriptionsInput)
    {
        var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (companyUser == null)
        {
            return Unauthorized();
        }

        var getProviderSubscriptionsOutput =
            await _providersApi.ProvidersGetProviderSubscriptionsPostAsync(
                getProviderSubscriptionsInput: new Sleekflow.Apis.CrmHub.Model.GetProviderSubscriptionsInput(
                    companyUser.CompanyId,
                    getProviderSubscriptionsInput.ProviderName,
                    getProviderSubscriptionsInput.ProviderConnectionId,
                    getProviderSubscriptionsInput.EntityTypeName));

        return new OkObjectResult(getProviderSubscriptionsOutput.Data);
    }

    public class InitProviderTypeSyncInput
    {
        [JsonProperty("provider_name")]
        [Required]
        public string ProviderName { get; set; }

        [JsonProperty("provider_connection_id")]
        [Required]
        public string ProviderConnectionId { get; set; }

        [JsonProperty("entity_type_name")]
        [Required]
        public string EntityTypeName { get; set; }

        [JsonProperty("sync_interval")]
        [Required]
        public int SyncInterval { get; set; }

        [JsonProperty("is_flows_based")]
        [Required]
        public bool IsFlowsBased { get; set; }
    }

    [HttpPost("InitProviderTypeSync")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<InitProviderTypeSyncOutputOutput>> InitProviderTypeSync(
        [FromBody]
        InitProviderTypeSyncInput initProviderTypeSyncInput)
    {
        var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (companyUser == null)
        {
            return Unauthorized();
        }

        var initProviderTypeSyncOutput =
            await _providersApi.ProvidersInitProviderTypeSyncPostAsync(
                initProviderTypeSyncInput: new Sleekflow.Apis.CrmHub.Model.InitProviderTypeSyncInput(
                    companyUser.CompanyId,
                    initProviderTypeSyncInput.ProviderName,
                    initProviderTypeSyncInput.ProviderConnectionId,
                    initProviderTypeSyncInput.EntityTypeName,
                    initProviderTypeSyncInput.SyncInterval,
                    initProviderTypeSyncInput.IsFlowsBased));

        return new OkObjectResult(initProviderTypeSyncOutput.Data);
    }

    public class GetProviderTypeFieldsV2Input
    {
        [JsonProperty("provider_name")]
        [Required]
        public string ProviderName { get; set; }

        [JsonProperty("provider_connection_id")]
        [Required]
        public string ProviderConnectionId { get; set; }

        [JsonProperty("entity_type_name")]
        [Required]
        public string EntityTypeName { get; set; }
    }

    [HttpPost("GetProviderTypeFieldsV2")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GetProviderTypeFieldsV2Output>> GetProviderTypeFieldsV2(
        [FromBody]
        GetProviderTypeFieldsV2Input getProviderTypeFieldsV2Input)
    {
        var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (companyUser == null)
        {
            return Unauthorized();
        }

        var getProviderTypeFieldsOutputOutput =
            await _providersApi.ProvidersGetProviderTypeFieldsV2PostAsync(
                getProviderTypeFieldsV2Input: new Sleekflow.Apis.CrmHub.Model.GetProviderTypeFieldsV2Input(
                    companyUser.CompanyId,
                    getProviderTypeFieldsV2Input.ProviderConnectionId,
                    getProviderTypeFieldsV2Input.EntityTypeName,
                    getProviderTypeFieldsV2Input.ProviderName));

        if (getProviderTypeFieldsOutputOutput.Success)
        {
            return new OkObjectResult(getProviderTypeFieldsOutputOutput.Data);
        }

        return StatusCode(
            getProviderTypeFieldsOutputOutput.HttpStatusCode,
            getProviderTypeFieldsOutputOutput.Message);
    }

    private record GetUserDictsOutput(
        Dictionary<string, List<string>> CrmHubUserIdToProviderIds,
        Dictionary<string, List<string>> ProviderIdToCrmHubUserIds,
        Dictionary<string, string> CrmHubUserIdToSleekflowUserIdDict,
        Dictionary<string, string> SleekflowUserIdToCrmHubUserIdDict);

    private static GetUserDictsOutput GetUserDicts(
        GetObjectsOutputOutput getObjectsOutputOutput)
    {
        var crmHubUserIdToProviderIds = new Dictionary<string, List<string>>();
        var providerIdToCrmHubUserIds = new Dictionary<string, List<string>>();
        var crmHubUserIdToSleekflowUserIdDict = new Dictionary<string, string>();

        foreach (var record in getObjectsOutputOutput.Data.Records)
        {
            var sleekflowUserId = record.GetValueOrDefault("sleekflow:Id");
            if (sleekflowUserId == null)
            {
                continue;
            }

            var crmHubUserId = (string) record["id"];
            var externalId = (string) record.GetValueOrDefault("ctx_external_id");
            var externalIds = ((JArray) record.GetValueOrDefault("ctx_external_ids"))?.Values<string>();

            crmHubUserIdToSleekflowUserIdDict[crmHubUserId] = (string) sleekflowUserId;

            if (crmHubUserIdToProviderIds.ContainsKey(crmHubUserId) == false)
            {
                crmHubUserIdToProviderIds[crmHubUserId] = new List<string>();
            }

            if (externalId != null)
            {
                crmHubUserIdToProviderIds[crmHubUserId].Add(externalId);
            }

            if (externalIds != null)
            {
                crmHubUserIdToProviderIds[crmHubUserId].AddRange(externalIds);
            }
        }

        foreach (var (crmHubUserId, providerIds) in crmHubUserIdToProviderIds)
        {
            foreach (var providerId in providerIds)
            {
                if (providerIdToCrmHubUserIds.ContainsKey(providerId) == false)
                {
                    providerIdToCrmHubUserIds[providerId] = new List<string>();
                }

                providerIdToCrmHubUserIds[providerId].Add(crmHubUserId);
            }
        }

        var sleekflowUserIdToCrmHubUserIdDict =
            crmHubUserIdToSleekflowUserIdDict.GroupBy(
                x => x.Value,
                x => x.Key,
                (entity2, entity1) => (entity1, entity2)).ToDictionary(x=>x.entity2, x=>x.entity1.First());

        return new GetUserDictsOutput(
            crmHubUserIdToProviderIds,
            providerIdToCrmHubUserIds,
            crmHubUserIdToSleekflowUserIdDict,
            sleekflowUserIdToCrmHubUserIdDict);
    }

    [HttpPost("GetProviderConfigs")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult> GetProviderConfigs()
    {
        var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (companyUser == null)
        {
            return Unauthorized();
        }

        var getProviderConfigsOutputOutput = await _providersApi.ProvidersGetProviderConfigsPostAsync(
            getProviderConfigsInput: new Sleekflow.Apis.CrmHub.Model.GetProviderConfigsInput(companyUser.CompanyId));
        var getProviderConfigsOutput = getProviderConfigsOutputOutput.Data;

        return new OkObjectResult(getProviderConfigsOutput);
    }

    [HttpPost("GetContactListByCampaignId")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<IActionResult> GetContactListByCampaignId(string campaignId, string newContactListName)
    {
        var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (companyUser == null)
        {
            return Unauthorized();
        }

        var companyId = companyUser.CompanyId;
        var staffId = companyUser.Id;
        var userId = companyUser.IdentityId;

        var newImport = new ImportContactHistory
        {
            CompanyId = companyId,
            ImportName = newContactListName,
            ImportedUserProfiles = new List<ImportedUserProfile>(),
            IsImported = false,
            Status = ImportStatus.Imported,
            ImportedFrom = await _appDbContext.UserRoleStaffs
                .Include(x => x.Identity)
                .FirstOrDefaultAsync(x => x.Id == staffId)
        };
        await _appDbContext.CompanyImportContactHistories.AddAsync(newImport);
        await _appDbContext.SaveChangesAsync();

        var listId = newImport.Id;

        var taskPayload = new ConvertCampaignLeadsToContactListTaskPayLoad
        {
            CampaignId = campaignId,
            NewContactListName = newContactListName,
            ListId = listId,
            CompanyId = companyId,
            StaffId = staffId
        };

        var task = await _backgroundTaskService.EnqueueConvertCampaignLeadsToContactListTask(
            userId,
            companyId,
            staffId,
            campaignId,
            listId,
            taskPayload);

        return Ok(task.MapToResultViewModel());
    }

    #region CrmHub Config

    [HttpPost("CrmHubConfigs/GetCrmHubConfig")]
    [Consumes("application/json")]
    [Produces("application/json")]
    [TypeFilter(typeof(CrmHubExceptionFilter))]
    public async Task<ActionResult<CrmHubConfig>> GetCrmHubConfig()
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var companyId = staff.CompanyId;

        var getCrmHubConfigOutputOutput =
            await _crmHubConfigsApi.CrmHubConfigsGetCrmHubConfigPostAsync(
                getCrmHubConfigInput: new GetCrmHubConfigInput(companyId));

        var crmHubConfig = getCrmHubConfigOutputOutput.Data.CrmHubConfig;

        if (!string.IsNullOrEmpty(crmHubConfig.Id) &&
            crmHubConfig.Id != "NOT_INITIALIZED")
        {
            return Ok(crmHubConfig);
        }

        // else, initialize
        var (usageLimit, featureAccessibilitySettings) = await _crmHubService.GetCrmHubCompanySettingsAsync(companyId);

        var initializeCrmHubConfigOutputOutput =
            await _crmHubConfigsApi.CrmHubConfigsInitializeCrmHubConfigPostAsync(
                initializeCrmHubConfigInput: new InitializeCrmHubConfigInput(
                    companyId,
                    usageLimit,
                    featureAccessibilitySettings,
                    staff.Id.ToString()));

        return Ok(initializeCrmHubConfigOutputOutput.Data.CrmHubConfig);
    }

    [HttpPost("CrmHubConfigs/RefreshCrmHubConfig")]
    [Consumes("application/json")]
    [Produces("application/json")]
    [TypeFilter(typeof(CrmHubExceptionFilter))]
    public async Task<ActionResult<CrmHubConfig>> RefreshCrmHubConfig()
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        await _crmHubService.RefreshCrmHubConfigAsync(staff.CompanyId, staff);

        var getCrmHubConfigOutputOutput =
            await _crmHubConfigsApi.CrmHubConfigsGetCrmHubConfigPostAsync(
                getCrmHubConfigInput: new GetCrmHubConfigInput(staff.CompanyId));

        return Ok(getCrmHubConfigOutputOutput.Data.CrmHubConfig);
    }

    #endregion

    #region UserMappingConfig

    public class CreateProviderUserMappingConfigRequest
    {
        [Required]
        [JsonProperty("provider_name")]
        public string ProviderName { get; set; }

        [Required]
        [JsonProperty("provider_connection_id")]
        public string ProviderConnectionId { get; set; }

        [JsonProperty("user_mappings")]
        public List<UserMapping>? UserMappings { get; set; }
    }

    [HttpPost("ProviderUserMappingConfigs/CreateProviderUserMappingConfig")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<CreateProviderUserMappingConfigOutput>> CreateProviderUserMappingConfig(
        [FromBody]
        CreateProviderUserMappingConfigRequest createProviderUserMappingConfigRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var createProviderUserMappingConfigOutput =
            (await _providersApi.ProvidersCreateProviderUserMappingConfigPostAsync(
                createProviderUserMappingConfigInput: new CreateProviderUserMappingConfigInput(
                    staff.CompanyId,
                    createProviderUserMappingConfigRequest.ProviderName,
                    createProviderUserMappingConfigRequest.ProviderConnectionId,
                    createProviderUserMappingConfigRequest.UserMappings))).Data;

        return Ok(createProviderUserMappingConfigOutput);
    }

    public class GetProviderUserMappingConfigRequest
    {
        [Required]
        [JsonProperty("provider_name")]
        public string ProviderName { get; set; }

        [Required]
        [JsonProperty("provider_connection_id")]
        public string ProviderConnectionId { get; set; }
    }

    public class GetProviderUserMappingConfigOutput
    {
        [JsonProperty("user_mapping_config")]
        public ProviderUserMappingConfigDto UserMappingConfig { get; set; }
    }

    public class ProviderUserMappingConfigDto
    {
        [JsonProperty("id")]
        public string Id { get; set; }

        [JsonProperty("sleekflow_company_id")]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("connection_id")]
        public string ConnectionId { get; set; }

        [JsonProperty("user_mappings")]
        public List<UserMappingDto>? UserMappings { get; set; }
    }

    public class UserMappingDto
    {
        [JsonProperty("provider_user_id")]
        public string ProviderUserId { get; set; }

        [JsonProperty("sleekflow_user_id")]
        public string SleekflowUserId { get; set; }

        [JsonProperty("provider_user")]
        public ProviderUserDto ProviderUser { get; set; }

        [JsonProperty("sleekflow_user")]
        public SleekflowUserDto SleekflowUser { get; set; }
    }

    public class ProviderUserDto
    {
        [JsonProperty("id")]
        public string Id { get; set; }

        [JsonProperty("first_name")]
        public string? FirstName { get; set; }

        [JsonProperty("last_name")]
        public string? LastName { get; set; }

        [JsonProperty("email")]
        public string? Email { get; set; }
    }

    public class SleekflowUserDto
    {
        [JsonProperty("id")]
        public string Id { get; set; }

        [JsonProperty("display_name")]
        public string DisplayName { get; set; }

        [JsonProperty("user_name")]
        public string UserName { get; set; }

        [JsonProperty("email")]
        public string Email { get; set; }
    }

    [HttpPost("ProviderUserMappingConfigs/GetProviderUserMappingConfig")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GetProviderUserMappingConfigOutput>> GetProviderUserMappingConfig(
        [FromBody]
        GetProviderUserMappingConfigRequest getProviderUserMappingConfigRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var userMappingConfig =
            (await _providersApi.ProvidersGetProviderUserMappingConfigPostAsync(
                getProviderUserMappingConfigInput: new GetProviderUserMappingConfigInput(
                    staff.CompanyId,
                    getProviderUserMappingConfigRequest.ProviderName,
                    getProviderUserMappingConfigRequest.ProviderConnectionId))).Data.UserMappingConfig;
        var userMappings = userMappingConfig.UserMappings;
        var providerUserIds = userMappings.Select(m => m.ProviderUserId).ToList();
        var sleekflowUserIds = userMappings.Select(m => m.SleekflowUserId).ToList();

        var providerUserDtos = (await _providersApi.ProvidersPreviewObjectsV2PostAsync(
            previewObjectsV2Input: new PreviewObjectsV2Input(
                sleekflowCompanyId: staff.CompanyId,
                providerName: getProviderUserMappingConfigRequest.ProviderName,
                providerConnectionId: getProviderUserMappingConfigRequest.ProviderConnectionId,
                entityTypeName: "User",
                filters: providerUserIds.Select(i => new SyncConfigFilter("Id", "'" + i + "'", "=")).ToList(),
                fieldFilters: new List<SyncConfigFieldFilter>
                {
                    new ("Id"),
                    new ("FirstName"),
                    new ("LastName"),
                    new ("Email")
                }))).Data.Objects.Select(u => new ProviderUserDto
        {
            Id = (string) u["Id"],
            FirstName = (string) u["FirstName"],
            LastName = (string) u["LastName"],
            Email = (string) u["Email"]
        }).ToList();

        var sleekflowUserDtos = await _appDbContext.UserRoleStaffs.AsNoTracking()
                                                .Where(x => x.CompanyId == staff.CompanyId && sleekflowUserIds.Contains(x.IdentityId))
                                                .Select(x => new SleekflowUserDto
                                                {
                                                    Id = x.IdentityId,
                                                    DisplayName = x.Identity.DisplayName,
                                                    UserName = x.Identity.UserName,
                                                    Email = x.Identity.Email
                                                })
                                                .ToListAsync();

        var userMappingDtos = userMappings.Select(
            m => new UserMappingDto
            {
                ProviderUserId = m.ProviderUserId,
                SleekflowUserId = m.SleekflowUserId,
                ProviderUser = providerUserDtos.Find(u => u.Id == m.ProviderUserId),
                SleekflowUser = sleekflowUserDtos.Find(u => u.Id == m.SleekflowUserId)
            }).ToList();

        return Ok(new GetProviderUserMappingConfigOutput
        {
            UserMappingConfig = new ProviderUserMappingConfigDto
            {
                Id = userMappingConfig.Id,
                SleekflowCompanyId = userMappingConfig.SleekflowCompanyId,
                ConnectionId = userMappingConfig.ConnectionId,
                UserMappings = userMappingDtos
            }
        });
    }

    public class UpdateProviderUserMappingConfigRequest
    {
        [Required]
        [JsonProperty("provider_name")]
        public string ProviderName { get; set; }

        [Required]
        [JsonProperty("provider_user_mapping_config_id")]
        public string ProviderUserMappingConfigId { get; set; }

        [JsonProperty("user_mappings")]
        public List<UserMapping>? UserMappings { get; set; }
    }

    [HttpPost("ProviderUserMappingConfigs/UpdateProviderUserMappingConfig")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<UpdateProviderUserMappingConfigOutput>> UpdateProviderUserMappingConfig(
        [FromBody]
        UpdateProviderUserMappingConfigRequest updateProviderUserMappingConfigRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var updateProviderUserMappingConfigOutput =
            (await _providersApi.ProvidersUpdateProviderUserMappingConfigPostAsync(
                updateProviderUserMappingConfigInput: new UpdateProviderUserMappingConfigInput(
                    staff.CompanyId,
                    updateProviderUserMappingConfigRequest.ProviderName,
                    updateProviderUserMappingConfigRequest.ProviderUserMappingConfigId,
                    updateProviderUserMappingConfigRequest.UserMappings))).Data;

        return Ok(updateProviderUserMappingConfigOutput);
    }

    #endregion

    #region Blob

    public class CreateBlobDownloadSasUrlsRequest
    {
        [JsonProperty("blob_names")]
        public List<string> BlobNames { get; set; }

        [JsonProperty("blob_type")]
        public string BlobType { get; set; }
    }

    [HttpPost("Blobs/CreateBlobDownloadSasUrls")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<CreateBlobDownloadSasUrlsOutput>> CreateBlobDownloadSasUrls(
        [FromBody]
        CreateBlobDownloadSasUrlsRequest createBlobDownloadSasUrlsRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var createBlobDownloadSasUrlsOutputOutput = await _blobsApi.BlobsCreateBlobDownloadSasUrlsPostAsync(
            createBlobDownloadSasUrlsInput: new CreateBlobDownloadSasUrlsInput(
                staff.CompanyId,
                createBlobDownloadSasUrlsRequest.BlobNames,
                createBlobDownloadSasUrlsRequest.BlobType));

        return Ok(createBlobDownloadSasUrlsOutputOutput.Data);
    }

    public class CreateBlobUploadSasUrlsRequest
    {
        [Range(1, 16)]
        [JsonProperty("number_of_blobs")]
        public int NumberOfBlobs { get; set; }

        [JsonProperty("blob_type")]
        public string BlobType { get; set; }
    }

    [HttpPost("Blobs/CreateBlobUploadSasUrls")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<CreateBlobUploadSasUrlsOutput>> CreateBlobUploadSasUrls(
        [FromBody]
        CreateBlobUploadSasUrlsRequest createBlobUploadSasUrlsRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var createBlobUploadSasUrlsOutputOutput = await _blobsApi.BlobsCreateBlobUploadSasUrlsPostAsync(
            createBlobUploadSasUrlsInput: new CreateBlobUploadSasUrlsInput(
                staff.CompanyId,
                createBlobUploadSasUrlsRequest.NumberOfBlobs,
                createBlobUploadSasUrlsRequest.BlobType));

        return Ok(createBlobUploadSasUrlsOutputOutput.Data);
    }

    public class DeleteBlobsRequest
    {
        [JsonProperty("blob_names")]
        public List<string> BlobNames { get; set; }

        [JsonProperty("blob_type")]
        public string BlobType { get; set; }
    }

    [HttpPost("Blobs/DeleteBlobs")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<DeleteBlobsOutput>> DeleteBlobs(
        [FromBody]
        DeleteBlobsRequest deleteBlobsRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var deleteBlobsOutputOutput = await _blobsApi.BlobsDeleteBlobsPostAsync(
            deleteBlobsInput: new DeleteBlobsInput(
                staff.CompanyId,
                deleteBlobsRequest.BlobNames,
                deleteBlobsRequest.BlobType));

        return Ok(deleteBlobsOutputOutput.Data);
    }

    #endregion
}