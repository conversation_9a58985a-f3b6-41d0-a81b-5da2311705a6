﻿using System.Security.Claims;
using System.Text;
using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Travis_backend.AccountAuthenticationDomain.Models;
using Travis_backend.AccountAuthenticationDomain.ViewModels;
using Travis_backend.Cache;
using Travis_backend.CommonDomain.Models;
using Travis_backend.CommonDomain.ViewModels;
using Travis_backend.Configuration;
using Travis_backend.ConversationDomain.ViewModels;
using Travis_backend.Database;
using Travis_backend.InternalDomain.Services;
using CmsCacheKeyHelper = Sleekflow.Powerflow.Apis.Helpers.CmsCacheKeyHelper;
using CmsUserDto = Sleekflow.Powerflow.Apis.ViewModels.CmsUserDto;
using GrantCmsUserPermissionRequest = Sleekflow.Powerflow.Apis.ViewModels.GrantCmsUserPermissionRequest;
using InternalCmsAuthenticationResponse = Sleekflow.Powerflow.Apis.ViewModels.InternalCmsAuthenticationResponse;
using JwtRegisteredClaimNames = Microsoft.IdentityModel.JsonWebTokens.JwtRegisteredClaimNames;
using ManageCmsRoleRequest = Sleekflow.Powerflow.Apis.ViewModels.ManageCmsRoleRequest;
using UserAndRoleDto = Sleekflow.Powerflow.Apis.ViewModels.UserAndRoleDto;

namespace Sleekflow.Powerflow.Apis.Controllers;

/// <summary>
/// Internal CMS Login and Role Managements.
/// </summary>
[Route("/internal/auth/[action]")]
public class InternalCmsAuthenticationController : InternalControllerBase
{
    // private readonly string CMS_PASSWORD = "r5u8x/A?D(G+KbPeSgVkYp3s6v9y$B&E";
    private readonly string CMS_PASSWORD = "PeShVmYq3t6w9z$C&F)H@McQfTjWnZr4u7x!A%D*G-KaNdRgUkXp2s5v8y/B?E(H";

    private readonly ApplicationDbContext _appDbContext;
    private readonly RoleManager<IdentityRole> _roleManager;
    private readonly SignInManager<ApplicationUser> _signInManager;
    private readonly JwtOptions _options;
    private readonly IMapper _mapper;
    private readonly ICacheManagerService _cacheManagerService;
    private readonly IInternalHubSpotService _internalHubSpotService;

    public InternalCmsAuthenticationController(
        UserManager<ApplicationUser> userManager,
        RoleManager<IdentityRole> roleManager,
        SignInManager<ApplicationUser> signInManager,
        ApplicationDbContext appDbContext,
        IOptions<JwtOptions> options,
        IMapper mapper,
        ICacheManagerService cacheManagerService,
        IInternalHubSpotService internalHubSpotService)
        : base(userManager)
    {
        _roleManager = roleManager;
        _signInManager = signInManager;
        _appDbContext = appDbContext;
        _mapper = mapper;
        _cacheManagerService = cacheManagerService;
        _internalHubSpotService = internalHubSpotService;
        _options = options.Value;
    }

    /// <summary>
    /// Get User My User Info.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    [Authorize(Roles = ApplicationUserRole.InternalCmsUser)]
    public async Task<ActionResult<UserAndRoleDto>> GetMyCmsUser()
    {
        var user = await _userManager.GetUserAsync(User);

        var roles = await _userManager.GetRolesAsync(user);

        return Ok(
            new UserAndRoleDto
            {
                User = _mapper.Map<UserInfoResponse>(user), Roles = roles.ToList()
            });
    }

    /// <summary>
    /// Add, Update, Remove Cms User Roles for Current User By Email.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    [Authorize(Roles = ApplicationUserRole.InternalCmsSuperUser)]
    public async Task<ActionResult<InternalCmsAuthenticationResponse>> GetAllCmsUser()
    {
        var result = new InternalCmsAuthenticationResponse();

        var users = await _userManager.GetUsersInRoleAsync(ApplicationUserRole.InternalCmsUser);

        var cmsUsers = new List<CmsUserDto>();

        foreach (var user in users)
        {
            var cmsUser = new CmsUserDto
            {
                Id = user.Id,
                DisplayName = user.DisplayName,
                Email = user.Email,
                PhoneNumber = user.PhoneNumber,
                CreatedAt = user.CreatedAt,
                Roles = (await _userManager.GetRolesAsync(user)).ToList(),
            };

            foreach (var role in cmsUser.Roles)
            {
                if (role == ApplicationUserRole.InternalCmsAdmin)
                {
                    cmsUser.IsInternalCmsAdmin = true;
                }
                else if (role == ApplicationUserRole.InternalCmsSuperUser)
                {
                    cmsUser.IsInternalCmsSuperUser = true;
                }
                else if (role == ApplicationUserRole.InternalCmsUser)
                {
                    cmsUser.IsInternalCmsUser = true;
                }
                else if (role == ApplicationUserRole.InternalCmsSalesUser)
                {
                    cmsUser.IsInternalCmsSalesUser = true;
                }
                else if (role == ApplicationUserRole.InternalCmsCustomerSuccessUser)
                {
                    cmsUser.IsInternalCmsCustomerSuccessUser = true;
                }
                else if (role == ApplicationUserRole.InternalCmsTeamLead)
                {
                    cmsUser.IsInternalCmsTeamLead = true;
                }
            }

            cmsUsers.Add(cmsUser);
        }

        result.CmsUsers = cmsUsers.OrderBy(x => x.CreatedAt)
            .ThenByDescending(x => x.IsInternalCmsAdmin)
            .ToList();

        return Ok(result);
    }

    /// <summary>
    /// Add, Update, Remove Cms User Roles for Current User By Email.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    [Authorize(Roles = ApplicationUserRole.InternalCmsAdmin)]
    public async Task<ActionResult<UserAndRoleDto>> GrantCmsUserPermission(
        [FromBody]
        GrantCmsUserPermissionRequest request)
    {
        if (request.CmsPassword != null && request.CmsPassword != CMS_PASSWORD)
        {
            return Unauthorized();
        }

        var currentUser = await _userManager.GetUserAsync(User);
        var currentUserRoles = await _userManager.GetRolesAsync(currentUser);

        if (!currentUserRoles.Contains(ApplicationUserRole.InternalCmsAdmin))
        {
            return Unauthorized();
        }

        if (!request.Email.ToLower().EndsWith("@sleekflow.io"))
        {
            return BadRequest(
                new ResponseViewModel()
                {
                    message = "Only allow Email end with @sleekflow.io"
                });
        }

        var user = await _userManager.FindByEmailAsync(request.Email);

        if (user == null)
        {
            return BadRequest(
                new ResponseViewModel()
                {
                    message = "User Not Found."
                });
        }

        await CreateCmsUserRoleIfNotExistAsync();

        var userRoles = await _userManager.GetRolesAsync(user);

        // Not Allow downgrade user himself
        if (user.Id == currentUser.Id && userRoles.Contains(ApplicationUserRole.InternalCmsAdmin))
        {
            request.IsCmsAdmin = true;
        }

        // Grant CMS User Role together with Super User
        if (request.IsCmsSuperUser)
        {
            request.IsCmsUser = true;
        }

        if (request.IsCmsSalesUser ||
            request.IsCmsCustomerSuccessUser ||
            request.IsCmsTeamLead ||
            request.IsCmsAdmin)
        {
            request.IsCmsUser = true;
            request.IsCmsSuperUser = true;
        }

        // Add CMS Super User Role
        if (request.IsCmsUser && !userRoles.Contains(ApplicationUserRole.InternalCmsUser))
        {
            await _userManager.AddToRoleAsync(user, ApplicationUserRole.InternalCmsUser);
        }
        else if (!request.IsCmsUser && userRoles.Contains(ApplicationUserRole.InternalCmsUser))
        {
            await _userManager.RemoveFromRoleAsync(user, ApplicationUserRole.InternalCmsUser);
        }

        // Add CMS Super User Role
        if (request.IsCmsSuperUser && !userRoles.Contains(ApplicationUserRole.InternalCmsSuperUser))
        {
            await _userManager.AddToRoleAsync(user, ApplicationUserRole.InternalCmsSuperUser);
        }
        else if (!request.IsCmsSuperUser && userRoles.Contains(ApplicationUserRole.InternalCmsSuperUser))
        {
            await _userManager.RemoveFromRoleAsync(user, ApplicationUserRole.InternalCmsSuperUser);
        }

        // Add CMS Sales User Role
        if (request.IsCmsSalesUser && !userRoles.Contains(ApplicationUserRole.InternalCmsSalesUser))
        {
            await _userManager.AddToRoleAsync(user, ApplicationUserRole.InternalCmsSalesUser);
        }
        else if (!request.IsCmsSalesUser && userRoles.Contains(ApplicationUserRole.InternalCmsSalesUser))
        {
            await _userManager.RemoveFromRoleAsync(user, ApplicationUserRole.InternalCmsSalesUser);
        }

        // Add CMS Customer Success User Role
        if (request.IsCmsCustomerSuccessUser &&
            !userRoles.Contains(ApplicationUserRole.InternalCmsCustomerSuccessUser))
        {
            await _userManager.AddToRoleAsync(user, ApplicationUserRole.InternalCmsCustomerSuccessUser);
        }
        else if (!request.IsCmsCustomerSuccessUser &&
                 userRoles.Contains(ApplicationUserRole.InternalCmsCustomerSuccessUser))
        {
            await _userManager.RemoveFromRoleAsync(user, ApplicationUserRole.InternalCmsCustomerSuccessUser);
        }

        // Add CMS Team Lead User Role
        if (request.IsCmsTeamLead && !userRoles.Contains(ApplicationUserRole.InternalCmsTeamLead))
        {
            await _userManager.AddToRoleAsync(user, ApplicationUserRole.InternalCmsTeamLead);
        }
        else if (!request.IsCmsTeamLead && userRoles.Contains(ApplicationUserRole.InternalCmsTeamLead))
        {
            await _userManager.RemoveFromRoleAsync(user, ApplicationUserRole.InternalCmsTeamLead);
        }

        // Add Admin Role
        if (request.IsCmsAdmin && !userRoles.Contains(ApplicationUserRole.InternalCmsAdmin))
        {
            await _userManager.AddToRoleAsync(user, ApplicationUserRole.InternalCmsAdmin);
        }
        else if (!request.IsCmsAdmin && userRoles.Contains(ApplicationUserRole.InternalCmsAdmin))
        {
            await _userManager.RemoveFromRoleAsync(user, ApplicationUserRole.InternalCmsAdmin);
        }

        userRoles = await _userManager.GetRolesAsync(user);

        var updateResult = await _userManager.UpdateAsync(user);

        await _cacheManagerService.DeleteCacheWithConstantKeyAsync(CmsCacheKeyHelper.GetSelectionsKey);

        await _internalHubSpotService.SyncInternalContactOwnerMapping();

        if (!updateResult.Succeeded)
        {
            return BadRequest(
                new ResponseViewModel()
                {
                    message = updateResult.Errors.FirstOrDefault().Description
                });
        }

        return Ok(
            new UserAndRoleDto
            {
                User = _mapper.Map<UserInfoResponse>(user), Roles = userRoles.ToList()
            });
    }

    /// <summary>
    /// One Time Api for Creating Cms User Roles in Db.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    [AllowAnonymous]
    public async Task<ActionResult<List<IdentityRole>>> CreateCmsUserRole([FromBody] ManageCmsRoleRequest request)
    {
        if (request.CmsPassword != null && request.CmsPassword != CMS_PASSWORD)
        {
            return Unauthorized();
        }

        await CreateCmsUserRoleIfNotExistAsync();

        var roles = await _roleManager.Roles.Where(r => r.Name.StartsWith("InternalCms")).ToListAsync();

        return Ok(roles);
    }

    private async Task<bool> CreateCmsUserRoleIfNotExistAsync()
    {
        var roles = await _roleManager.Roles.Where(r => r.Name.StartsWith("InternalCms")).ToListAsync();

        if (!roles.Select(r => r.Name).Contains(ApplicationUserRole.InternalCmsUser))
        {
            await _roleManager.CreateAsync(new IdentityRole(ApplicationUserRole.InternalCmsUser));
        }

        if (!roles.Select(r => r.Name).Contains(ApplicationUserRole.InternalCmsSuperUser))
        {
            await _roleManager.CreateAsync(new IdentityRole(ApplicationUserRole.InternalCmsSuperUser));
        }

        if (!roles.Select(r => r.Name).Contains(ApplicationUserRole.InternalCmsSalesUser))
        {
            await _roleManager.CreateAsync(new IdentityRole(ApplicationUserRole.InternalCmsSalesUser));
        }

        if (!roles.Select(r => r.Name).Contains(ApplicationUserRole.InternalCmsCustomerSuccessUser))
        {
            await _roleManager.CreateAsync(new IdentityRole(ApplicationUserRole.InternalCmsCustomerSuccessUser));
        }

        if (!roles.Select(r => r.Name).Contains(ApplicationUserRole.InternalCmsAdmin))
        {
            await _roleManager.CreateAsync(new IdentityRole(ApplicationUserRole.InternalCmsAdmin));
        }

        if (!roles.Select(r => r.Name).Contains(ApplicationUserRole.InternalCmsTeamLead))
        {
            await _roleManager.CreateAsync(new IdentityRole(ApplicationUserRole.InternalCmsTeamLead));
        }

        return true;
    }

#if DEBUG
    /// <summary>
    /// Listing Creating Cms User Roles in Db.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    public async Task<ActionResult<List<IdentityRole>>> ListCmsUserRoles([FromBody] ManageCmsRoleRequest request)
    {
        if (request.CmsPassword != null && request.CmsPassword != CMS_PASSWORD)
        {
            return Unauthorized();
        }

        var roles = await _roleManager.Roles.Where(r => r.Name.StartsWith("InternalCms")).ToListAsync();

        return Ok(roles);
    }

    /// <summary>
    /// Testing Role token setting for User with InternalCmsUser.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    [Authorize(Roles = ApplicationUserRole.InternalCmsUser)]
    public async Task<ActionResult<UserAndRoleDto>> TryAuthorizeOnlyCmsUser()
    {
        var user = await _userManager.GetUserAsync(User);

        var roles = await _userManager.GetRolesAsync(user);

        return Ok(
            new UserAndRoleDto
            {
                User = _mapper.Map<UserInfoResponse>(user), Roles = roles.ToList()
            });
    }

    /// <summary>
    /// Testing Role token setting for User with InternalCmsUser and InternalCmsSuperUser.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    [Authorize(Roles = ApplicationUserRole.InternalCmsUser)]
    [Authorize(Roles = ApplicationUserRole.InternalCmsSuperUser)]
    public async Task<ActionResult<UserAndRoleDto>> TryAuthorizeOnlySuperUser()
    {
        var user = await _userManager.GetUserAsync(User);

        var roles = await _userManager.GetRolesAsync(user);

        return Ok(
            new UserAndRoleDto
            {
                User = _mapper.Map<UserInfoResponse>(user), Roles = roles.ToList()
            });
    }
#endif
}