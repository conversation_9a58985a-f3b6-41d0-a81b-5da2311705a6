﻿using System.Threading.Tasks;
using Sleekflow.Apis.FlowHub.Model;
using Travis_backend.ContactDomain.Services;
using Travis_backend.ConversationServices;
using Travis_backend.FlowHubs.Constants;
using Travis_backend.FlowHubs.Interfaces;

namespace Travis_backend.FlowHubs.Handlers.UpdateContactOwnerRelationshipsAssignments;

public sealed class UnassignStrategyHandler : IUpdateContactOwnerRelationshipsAssignmentStrategyHandler
{
    private readonly IUserProfileService _userProfileService;
    private readonly IConversationMessageService _conversationMessageService;

    public string StrategyName => UpdateContactOwnerRelationshipsAssignmentStrategy.Unassigned;

    public UnassignStrategyHandler(
        IUserProfileService userProfileService,
        IConversationMessageService conversationMessageService)
    {
        _userProfileService = userProfileService;
        _conversationMessageService = conversationMessageService;
    }

    public async Task HandleAsync(UpdateContactOwnerRelationshipsInput input)
    {
        var companyId = input.StateIdentity.SleekflowCompanyId;
        var contactId = input.ContactId;

        var conversation = await _userProfileService.GetConversationByUserProfileId(
            companyId,
            userProfileId: contactId,
            status: "closed");

        await _conversationMessageService.ChangeConversationAssignee(
            conversation,
            assignee: null);

        await _conversationMessageService.ChangeConversationAssignedTeam(
            conversation,
            companyTeam: null);
    }
}