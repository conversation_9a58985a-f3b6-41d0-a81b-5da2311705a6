using System;
using System.Linq;
using System.Linq.Expressions;
using LinqKit;
using Travis_backend.ConversationDomain.Extensions;
using Travis_backend.ConversationDomain.Models;
using Travis_backend.ConversationDomain.ViewModels;
using static LinqKit.PredicateBuilder;

namespace Travis_backend.ConversationDomain;

public class StaffConversationSpecification : ISpecification<Conversation>
{
    private readonly StaffAccessControlAggregate _staff;

    public StaffConversationSpecification(StaffAccessControlAggregate staff)
    {
        _staff = staff;
    }

    public Expression<Func<Conversation, bool>> ToExpression()
    {
        var associatedTeamIds = _staff.AssociatedTeams.Select(team => team.Id).ToList();
        var conversation = New<Conversation>(false);

        conversation = conversation.IsUnassigned()
            .Or(conversation.IsAssignedToStaff(_staff.StaffId))
            .Or(conversation.IsAssignedToTeamInbox().And(conversation.IsAssignedToAnyAssociatedTeam(associatedTeamIds)))
            .Or(conversation.HasSpecificStaffAsCollaborator(_staff.StaffId))
            .Or(conversation.HasMentionedStaff(_staff.StaffId));

        return conversation;
    }

    public bool IsSatisfiedBy(Conversation conversation)
    {
        return conversation is not null && ToExpression().Compile().Invoke(conversation);
    }
}