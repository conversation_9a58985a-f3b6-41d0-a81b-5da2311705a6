using System.Threading.Tasks;

namespace Travis_backend.StripeIntegrationDomain.Services;

/// <summary>
/// Stripe's Customer Service
/// </summary>
public interface IStripeCustomerService
{
    /// <summary>
    /// Create Stripe Customer.
    /// </summary>
    /// <param name="email">Email.</param>
    /// <param name="invoiceFooter">Invoice Footer.</param>
    /// <returns>Customer ID.</returns>
    Task<string> CreateAsync(string email, string invoiceFooter);
}