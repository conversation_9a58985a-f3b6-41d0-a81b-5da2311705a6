using System;
using System.Collections.Generic;
using System.Collections.Immutable;

namespace Travis_backend.Constants;

public static class ChannelTypes
{
    public const string WhatsappTwilio = "whatsapp";
    public const string Whatsapp360Dialog = "whatsapp360dialog";
    public const string WhatsappCloudApi = "whatsappcloudapi";
    public const string Sms = "sms";
    public const string Facebook = "facebook";
    public const string Instagram = "instagram";
    public const string LiveChat = "web";
    public const string Viber = "viber";
    public const string Telegram = "telegram";
    public const string Email = "email";
    public const string Line = "line";
    public const string Wechat = "wechat";
    public const string Note = "note";
    public const string System = "system";

    public static readonly ImmutableList<string> SingleIntegratedChannelTypes = ImmutableList<string>.Empty
        .Add(Wechat)
        .Add(Line)
        .Add(LiveChat)
        .Add(Viber)
        .Add(Telegram)
        .Add(Sms)
        .Add(Email);

    public static readonly ImmutableList<string> NonDefaultChannelChannelTypes = ImmutableList<string>.Empty
        .Add(Sms)
        .Add(LiveChat)
        .Add(Viber)
        .Add(Telegram)
        .Add(Email)
        .Add(Line)
        .Add(Wechat)
        .Add(Note);

    public static readonly Lazy<IList<string>> AllChannelTypes = new (
        () => new List<string>()
        {
            WhatsappTwilio,
            Whatsapp360Dialog,
            WhatsappCloudApi,
            Sms,
            Facebook,
            Instagram,
            LiveChat,
            Viber,
            Telegram,
            Email,
            Line,
            Wechat,
            Note
        });
}