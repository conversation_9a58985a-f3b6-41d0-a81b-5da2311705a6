<Project Sdk="Microsoft.NET.Sdk.Web">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <Nullable>warnings</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
        <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="AutoMapper" Version="13.0.1" />
        <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="8.0.7" />
        <PackageReference Include="Swashbuckle.AspNetCore" Version="6.7.0" />
    </ItemGroup>

    <!-- https://github.com/dotnet/runtime/issues/62329 -->
    <ItemGroup Condition="'$(OS)' == 'Windows_NT'">
        <RuntimeHostConfigurationOption Include="System.Globalization.AppLocalIcu" Value="********" />
        <PackageReference Include="Microsoft.ICU.ICU4C.Runtime" Version="********" />
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\Travis_backend.Auth0\Travis_backend.Auth0.csproj" />
        <ProjectReference Include="..\Travis_backend\Travis_backend.csproj" />
    </ItemGroup>

    <ItemGroup>
        <Reference Include="GraphApi.Client.Const">
            <HintPath>..\Travis_backend\Binaries\GraphApi.Client\netstandard2.0\GraphApi.Client.Const.dll</HintPath>
        </Reference>
        <Reference Include="Hangfire.Pro">
            <HintPath>..\Travis_backend\Hangfire.Pro.Binaries\Hangfire.Pro.dll</HintPath>
        </Reference>
        <Reference Include="Hangfire.Pro.Redis">
            <HintPath>..\Travis_backend\Hangfire.Pro.Binaries\Hangfire.Pro.Redis.dll</HintPath>
        </Reference>
        <Reference Include="Sleekflow.Apis.CommerceHub">
            <HintPath>..\Travis_backend\Binaries\Sleekflow.Apis.CommerceHub\netstandard2.0\Sleekflow.Apis.CommerceHub.dll</HintPath>
        </Reference>
        <Reference Include="Sleekflow.Apis.FlowHub">
            <HintPath>..\Travis_backend\Binaries\Sleekflow.Apis.FlowHub\netstandard2.0\Sleekflow.Apis.FlowHub.dll</HintPath>
        </Reference>
        <Reference Include="Sleekflow.Apis.**********************">
          <HintPath>..\Travis_backend\Binaries\Sleekflow.Apis.**********************\netstandard2.0\Sleekflow.Apis.**********************.dll</HintPath>
        </Reference>
        <Reference Include="Sleekflow.Apis.MessagingHub">
            <HintPath>..\Travis_backend\Binaries\Sleekflow.Apis.MessagingHub\netstandard2.0\Sleekflow.Apis.MessagingHub.dll</HintPath>
        </Reference>
        <Reference Include="Sleekflow.Apis.PublicApiGateway">
            <HintPath>..\Travis_backend\Binaries\Sleekflow.Apis.PublicApiGateway\netstandard2.0\Sleekflow.Apis.PublicApiGateway.dll</HintPath>
        </Reference>
        <Reference Include="Sleekflow.Apis.CrmHub">
            <HintPath>..\Travis_backend\Binaries\Sleekflow.Apis.CrmHub\netstandard2.0\Sleekflow.Apis.CrmHub.dll</HintPath>
        </Reference>
        <Reference Include="Sleekflow.Apis.TicketingHub">
            <HintPath>..\Travis_backend\Binaries\Sleekflow.Apis.TicketingHub\netstandard2.0\Sleekflow.Apis.TicketingHub.dll</HintPath>
        </Reference>
        <Reference Include="Sleekflow.Apis.Webhookhub">
            <HintPath>..\Travis_backend\Binaries\Sleekflow.Apis.WebhookHub\netstandard2.0\Sleekflow.Apis.WebhookHub.dll</HintPath>
        </Reference>
    </ItemGroup>

    <ItemGroup>
        <Content Update="appsettings.json">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
            <CopyToPublishDirectory>Never</CopyToPublishDirectory>
        </Content>
        <Content Include="..\.dockerignore">
            <Link>.dockerignore</Link>
        </Content>
    </ItemGroup>

    <ItemGroup>
        <Content Update="web.config">
            <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
            <CopyToPublishDirectory>Never</CopyToPublishDirectory>
        </Content>
    </ItemGroup>

    <PropertyGroup Condition="'$(SWAGGERGEN)' == 'TRUE'">
        <DefineConstants>SWAGGERGEN</DefineConstants>
    </PropertyGroup>

    <Target Name="SwaggerGen" AfterTargets="Build" Condition="'$(SWAGGERGEN)' == 'TRUE'">
        <MakeDir Directories=".swagger" />
        <Exec Command="dotnet tool restore" />
        <Exec Command="dotnet swagger tofile --output ./.swagger/internal.yaml --yaml $(OutputPath)$(AssemblyName).dll internal" WorkingDirectory="$(ProjectDir)" />
    </Target>

</Project>
