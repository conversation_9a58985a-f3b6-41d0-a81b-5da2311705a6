using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AutoMapper;
using AutoMapper.QueryableExtensions;
using Hangfire;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Sleekflow.Apis.MessagingHub.Model;
using Travis_backend.AccountAuthenticationDomain.Models;
using Travis_backend.Cache;
using Travis_backend.CommonDomain.ViewModels;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.Configuration;
using Travis_backend.Constants;
using Travis_backend.ConversationDomain.ViewModels;
using Travis_backend.ConversationServices;
using Travis_backend.Data.WhatsappCloudApi;
using Travis_backend.Database;
using Travis_backend.Enums;
using Travis_backend.FlowHubs;
using Travis_backend.IntelligentHubDomain.Services;
using Travis_backend.InternalDomain.Services;
using Travis_backend.ResellerDomain.Constants;
using Travis_backend.ResellerDomain.Models;
using Travis_backend.ResellerDomain.Services;
using Travis_backend.ResellerDomain.ViewModels;
using Travis_backend.SleekflowCrmHubDomain.Services;

namespace Travis_backend.Controllers.ResellerControllers
{
    /// <summary>
    /// Reseller Portal Manage Client Companies.
    /// </summary>
    [Route("reseller/account/[action]")]
    public class ResellerAccountController : ResellerBaseController
    {
        private readonly ApplicationDbContext _appDbContext;
        private readonly IMapper _mapper;
        private readonly ILogger _logger;
        private readonly IResellerPortalRepository _resellerPortalRepository;
        private readonly ICompanyInfoCacheService _companyInfoCacheService;
        private readonly IAzureBlobStorageService _azureBlobStorageService;
        private readonly IWhatsappCloudApiService _whatsappCloudApiService;
        private readonly IInternalWhatsappCloudApiService _internalWhatsappCloudApiService;

        public ResellerAccountController(
            ApplicationDbContext appDbContext,
            UserManager<ApplicationUser> userManager,
            IMapper mapper,
            ILogger<ResellerAccountController> logger,
            IResellerPortalRepository resellerPortalRepository,
            ICompanyInfoCacheService companyInfoCacheService,
            IAzureBlobStorageService azureBlobStorageService,
            IWhatsappCloudApiService whatsappCloudApiService,
            IInternalWhatsappCloudApiService internalWhatsappCloudApiService)
            : base(userManager, appDbContext)
        {
            _appDbContext = appDbContext;
            _mapper = mapper;
            _logger = logger;
            _resellerPortalRepository = resellerPortalRepository;
            _companyInfoCacheService = companyInfoCacheService;
            _azureBlobStorageService = azureBlobStorageService;
            _whatsappCloudApiService = whatsappCloudApiService;
            _internalWhatsappCloudApiService = internalWhatsappCloudApiService;
        }

        /// <summary>
        /// Get Account Summary with Remaining Balance, Total Clients, Newly Added Clients in Current Month.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        [HttpPost]
        [Authorize(Roles = ApplicationUserRole.ResellerPortalUser)]
        public async Task<ActionResult<ResellerProfileDto>> GetAccountSummary()
        {
            if (User.Identity.IsAuthenticated)
            {
                var user = await GetCurrentValidResellerUser(
                    new List<string>()
                    {
                        ApplicationUserRole.ResellerPortalUser
                    });
                var resellerProfileResponse = await _resellerPortalRepository.GetUserIdentityReseller(user);

                if (!resellerProfileResponse.IsSuccess)
                {
                    return BadRequest(
                        new ResponseViewModel()
                        {
                            message = resellerProfileResponse.ErrorMsg
                        });
                }

                var resellerProfileId = (string) resellerProfileResponse.Data;
                var responseWrapper = await _resellerPortalRepository.GetResellerOwnProfile(resellerProfileId);

                if (!responseWrapper.IsSuccess)
                {
                    return BadRequest(
                        new ResponseViewModel()
                        {
                            message = responseWrapper.ErrorMsg
                        });
                }

                var resellerProfileDto = (ResellerProfileDto) responseWrapper.Data;

                return Ok(resellerProfileDto);
            }

            return Unauthorized();
        }

        /// <summary>
        /// Get Reseller Subscription Plan Config.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        [HttpPost]
        [Authorize(Roles = ApplicationUserRole.ResellerPortalUser)]
        public async Task<ActionResult<Dictionary<string, string>>> GetResellerSubscriptionPlanConfig()
        {
            if (User.Identity.IsAuthenticated)
            {
                var user = await GetCurrentValidResellerUser(
                    new List<string>()
                    {
                        ApplicationUserRole.ResellerPortalUser
                    });

                var resellerProfileResponse = await _resellerPortalRepository.GetUserIdentityReseller(user);

                if (!resellerProfileResponse.IsSuccess)
                {
                    return BadRequest(
                        new ResponseViewModel()
                        {
                            message = resellerProfileResponse.ErrorMsg
                        });
                }

                var resellerProfileId = (string) resellerProfileResponse.Data;

                var resellerProfile = await _appDbContext.ResellerCompanyProfiles
                    .FirstOrDefaultAsync(x => x.Id == resellerProfileId);

                // Check if the reseller has a country tier and subscription plan config, if not then update it.
                resellerProfile =
                    await _resellerPortalRepository.CheckAndUpdateResellerSubscriptionPlanConfig(resellerProfile);

                var resellerProfileDto = resellerProfile.ResellerSubscriptionPlanConfig;

                return Ok(resellerProfileDto);
            }

            return Unauthorized();
        }

        /// <summary>
        /// Get Top Up and Billing History.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        [HttpPost]
        [Authorize(Roles = ApplicationUserRole.ResellerPortalUser)]
        public async Task<ActionResult<TransactionLogsResponse>> GetTransactionLogs(
            [FromBody]
            GetTransactionLogsRequest request)
        {
            if (User.Identity.IsAuthenticated)
            {
                var user = await GetCurrentValidResellerUser(
                    new List<string>()
                    {
                        ApplicationUserRole.ResellerPortalUser
                    });

                if (user == null)
                {
                    return Unauthorized();
                }

                var resellerProfileResponse = await _resellerPortalRepository.GetUserIdentityReseller(user);

                if (!resellerProfileResponse.IsSuccess)
                {
                    return BadRequest(
                        new ResponseViewModel()
                        {
                            message = resellerProfileResponse.ErrorMsg
                        });
                }

                var resellerProfileId = (string) resellerProfileResponse.Data;
                var transactionLogsResponse =
                    await _resellerPortalRepository.GetAllTransactionLogs(resellerProfileId, request);

                if (!transactionLogsResponse.IsSuccess)
                {
                    return BadRequest(
                        new ResponseViewModel()
                        {
                            message = transactionLogsResponse.ErrorMsg
                        });
                }

                var response = (TransactionLogsResponse) transactionLogsResponse.Data;

                return Ok(response);
            }

            return Unauthorized();
        }

        /// <summary>
        /// Get Reseller Activity Logs.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        [HttpPost]
        [Authorize(Roles = ApplicationUserRole.ResellerPortalUser)]
        public async Task<ActionResult<ActivityLogsResponse>> GetActivityLogs(
            [FromBody]
            GetActivityLogsRequest request)
        {
            if (User.Identity.IsAuthenticated)
            {
                var user = await GetCurrentValidResellerUser(
                    new List<string>()
                    {
                        ApplicationUserRole.ResellerPortalUser
                    });

                if (user == null)
                {
                    return Unauthorized();
                }

                var resellerProfileResponse = await _resellerPortalRepository.GetUserIdentityReseller(user);

                if (!resellerProfileResponse.IsSuccess)
                {
                    return BadRequest(
                        new ResponseViewModel()
                        {
                            message = resellerProfileResponse.ErrorMsg
                        });
                }

                var resellerProfileId = (string) resellerProfileResponse.Data;
                var activityLogsResponse =
                    await _resellerPortalRepository.GetAllActivityLogs(resellerProfileId, request);

                if (!activityLogsResponse.IsSuccess)
                {
                    return BadRequest(
                        new ResponseViewModel()
                        {
                            message = activityLogsResponse.ErrorMsg
                        });
                }

                var response = (ActivityLogsResponse) activityLogsResponse.Data;

                return Ok(response);
            }

            return Unauthorized();
        }

        /// <summary>
        /// Get Client Company Names for search bar.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        [HttpPost]
        [Authorize(Roles = ApplicationUserRole.ResellerPortalUser)]
        public async Task<ActionResult<ClientCompanyNamesResponse>> SearchCompanyNameForAutoComplete(
            [FromBody]
            ClientCompanyNamesRequest request)
        {
            if (User.Identity.IsAuthenticated)
            {
                var user = await GetCurrentValidResellerUser(
                    new List<string>()
                    {
                        ApplicationUserRole.ResellerPortalUser
                    });

                if (user == null)
                {
                    return Unauthorized();
                }

                var resellerProfileResponse = await _resellerPortalRepository.GetUserIdentityReseller(user);

                if (!resellerProfileResponse.IsSuccess)
                {
                    return BadRequest(
                        new ResponseViewModel()
                        {
                            message = resellerProfileResponse.ErrorMsg
                        });
                }

                var resellerProfileId = (string) resellerProfileResponse.Data;

                var response = new ClientCompanyNamesResponse()
                {
                    ClientCompanyNames = new List<string>()
                };

                if (string.IsNullOrEmpty(request.CompanyName))
                {
                    response.ClientCompanyNames = await _appDbContext.ResellerClientCompanyProfiles.AsNoTracking()
                        .Include(x => x.ClientCompany)
                        .Where(x => x.ResellerCompanyProfileId == resellerProfileId && x.ClientCompany != null)
                        .Select(x => x.ClientCompany.CompanyName).Distinct().ToListAsync();
                }
                else
                {
                    response.ClientCompanyNames = await _appDbContext.ResellerClientCompanyProfiles.AsNoTracking()
                        .Include(x => x.ClientCompany)
                        .Where(
                            x => x.ResellerCompanyProfileId == resellerProfileId && x.ClientCompany != null &&
                                 x.ClientCompany.CompanyName.Contains(request.CompanyName))
                        .Select(x => x.ClientCompany.CompanyName).Distinct().ToListAsync();
                }

                return Ok(response);
            }

            return Unauthorized();
        }

        /// <summary>
        /// Get Client Company Information with search keyword as query parameter for company name.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        [HttpPost]
        [Authorize(Roles = ApplicationUserRole.ResellerPortalUser)]
        public async Task<ActionResult<GetResellerClientCompanyDto>> GetClientCompaniesInfo(
            [FromBody]
            GetCompaniesViewModel request)
        {
            if (ModelState.IsValid && User.Identity.IsAuthenticated)
            {
                var user = await GetCurrentValidResellerUser(
                    new List<string>()
                    {
                        ApplicationUserRole.ResellerPortalUser
                    });

                if (user == null)
                {
                    return Unauthorized();
                }

                var resellerProfileResponse = await _resellerPortalRepository.GetUserIdentityReseller(user);

                if (!resellerProfileResponse.IsSuccess)
                {
                    return BadRequest(
                        new ResponseViewModel()
                        {
                            message = resellerProfileResponse.ErrorMsg
                        });
                }

                var resellerProfileId = (string) resellerProfileResponse.Data;

                var responseWrapper =
                    await _resellerPortalRepository.GetResellerClientCompanies(resellerProfileId, request);

                if (!responseWrapper.IsSuccess)
                {
                    return BadRequest(
                        new ResponseViewModel()
                        {
                            message = responseWrapper.ErrorMsg
                        });
                }

                var getResellerClientCompanyDto = (GetResellerClientCompanyDto) responseWrapper.Data;

                return Ok(getResellerClientCompanyDto);
            }

            return Unauthorized();
        }

        /// <summary>
        /// Get Client Company Information By Id.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        [HttpPost]
        [Authorize(Roles = ApplicationUserRole.ResellerPortalUser)]
        public async Task<ActionResult<ResellerClientCompanyUsageDetails>> GetCompanyById(
            [FromBody]
            GetCompanyByIdViewModel request)
        {
            if (ModelState.IsValid && User.Identity.IsAuthenticated)
            {
                var user = await GetCurrentValidResellerUser(
                    new List<string>()
                    {
                        ApplicationUserRole.ResellerPortalUser
                    });

                if (user == null)
                {
                    return Unauthorized();
                }

                var resellerProfileResponse = await _resellerPortalRepository.GetUserIdentityReseller(user);

                if (!resellerProfileResponse.IsSuccess)
                {
                    return BadRequest(
                        new ResponseViewModel()
                        {
                            message = resellerProfileResponse.ErrorMsg
                        });
                }

                var resellerProfileId = (string) resellerProfileResponse.Data;

                var responseWrapper =
                    await _resellerPortalRepository.GetResellerCompanyById(resellerProfileId, request.CompanyId);

                if (!responseWrapper.IsSuccess)
                {
                    return BadRequest(
                        new ResponseViewModel()
                        {
                            message = responseWrapper.ErrorMsg
                        });
                }

                var resellerClientDto = (ResellerClientCompanyUsageDetails) responseWrapper.Data;

                return Ok(resellerClientDto);
            }

            return Unauthorized();
        }

        /// <summary>
        /// Get Client Company Information By Id.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        [HttpPost]
        [Authorize(Roles = ApplicationUserRole.ResellerPortalUser)]
        public async Task<ActionResult<List<ResellerClientStaffInformation>>> GetResellerClientUsers(
            [FromBody]
            GetResellerClientUsersViewModel request)
        {
            if (ModelState.IsValid && User.Identity.IsAuthenticated)
            {
                var user = await GetCurrentValidResellerUser(
                    new List<string>()
                    {
                        ApplicationUserRole.ResellerPortalUser
                    });

                if (user == null)
                {
                    return Unauthorized();
                }

                var resellerProfileResponse = await _resellerPortalRepository.GetUserIdentityReseller(user);

                if (!resellerProfileResponse.IsSuccess)
                {
                    return BadRequest(
                        new ResponseViewModel()
                        {
                            message = resellerProfileResponse.ErrorMsg
                        });
                }

                var resellerProfileId = (string) resellerProfileResponse.Data;

                var responseWrapper =
                    await _resellerPortalRepository.GetResellerClientStaffs(resellerProfileId, request.ClientCompanyId);

                if (!responseWrapper.IsSuccess)
                {
                    return BadRequest(
                        new ResponseViewModel()
                        {
                            message = responseWrapper.ErrorMsg
                        });
                }

                var resellerClientStaffList = (List<ResellerClientStaffInformation>) responseWrapper.Data;

                return Ok(resellerClientStaffList);
            }

            return BadRequest();
        }

        [HttpPost]
        [Authorize(Roles = ApplicationUserRole.ResellerPortalUser)]
        public async Task<ActionResult> DeleteResellerClientStaff([FromBody] RemoveResellerClientStaffRequest request)
        {
            if (ModelState.IsValid && User.Identity.IsAuthenticated)
            {
                var user = await GetCurrentValidResellerUser(
                    new List<string>()
                    {
                        ApplicationUserRole.ResellerPortalUser
                    });

                if (user == null)
                {
                    return Unauthorized();
                }

                var resellerProfileResponse = await _resellerPortalRepository.GetUserIdentityReseller(user);

                if (!resellerProfileResponse.IsSuccess)
                {
                    return BadRequest(
                        new ResponseViewModel()
                        {
                            message = resellerProfileResponse.ErrorMsg
                        });
                }

                var resellerProfileId = (string) resellerProfileResponse.Data;

                var responseWrapper =
                    await _resellerPortalRepository.RemoveResellerClientStaff(
                        resellerProfileId,
                        request.Email,
                        request.ClientCompanyId,
                        user.Id);

                if (!responseWrapper.IsSuccess)
                {
                    return BadRequest(
                        new ResponseViewModel()
                        {
                            message = responseWrapper.ErrorMsg
                        });
                }

                var result = (string) responseWrapper.Data;

                return Ok(result);
            }

            return BadRequest();
        }

        /// <summary>
        /// Login as client admin by switching the CompanyId and going to web app.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        [HttpPost]
        [Authorize(Roles = ApplicationUserRole.ResellerPortalUser)]
        public async Task<ActionResult<LoginAsClientAdminResponse>> LoginAsClientAdmin(
            [FromBody]
            LoginClientCompanyAsAdminViewModel request)
        {
            if (User.Identity.IsAuthenticated && ModelState.IsValid)
            {
                var user = await GetCurrentValidResellerUser(
                    new List<string>()
                    {
                        ApplicationUserRole.ResellerPortalUser
                    });

                var response = new LoginAsClientAdminResponse();

                var resellerProfileResponse = await _resellerPortalRepository.GetUserIdentityReseller(user);

                if (!resellerProfileResponse.IsSuccess)
                {
                    return BadRequest(
                        new ResponseViewModel()
                        {
                            message = "Reseller Profile Not Found"
                        });
                }

                var resellerProfileId = (string) resellerProfileResponse.Data;

                var resellerClientCompany = await _appDbContext.ResellerClientCompanyProfiles.FirstOrDefaultAsync(
                    x => x.ResellerCompanyProfileId == resellerProfileId && x.ClientCompanyId == request.CompanyId);

                if (resellerClientCompany == null)
                {
                    return BadRequest(
                        new ResponseViewModel()
                        {
                            message = "Client Company Not Found"
                        });
                }

                var responseWrapper =
                    await _resellerPortalRepository.SetResellerStaffLoginClient(request.CompanyId, user);

                if (!responseWrapper.IsSuccess)
                {
                    return BadRequest(
                        new ResponseViewModel()
                        {
                            message = responseWrapper.ErrorMsg
                        });
                }

                response.Url = (string) responseWrapper.Data;

                return Ok(response);
            }

            return BadRequest();
        }

        /// <summary>
        /// Register Client Company with selected subscription plan.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        [HttpPost]
        [Authorize(Roles = ApplicationUserRole.ResellerPortalUser)]
        public async Task<ActionResult<ResellerClientInformation>> RegisterClientCompany(
            [FromBody]
            RegisterClientCompanyViewModel registerCompanyViewModel)
        {
            if (ModelState.IsValid && User.Identity.IsAuthenticated)
            {
                var user = await GetCurrentValidResellerUser(
                    new List<string>()
                    {
                        ApplicationUserRole.ResellerPortalUser
                    });

                if (user == null)
                {
                    return Unauthorized();
                }

                try
                {
                    var responseWrapper =
                        await _resellerPortalRepository.CreateClientCompany(user, registerCompanyViewModel);

                    if (!responseWrapper.IsSuccess)
                    {
                        return BadRequest(
                            new ResponseViewModel()
                            {
                                message = responseWrapper.ErrorMsg
                            });
                    }

                    var resellerClientInformation = (ResellerClientInformation) responseWrapper.Data;

                    return resellerClientInformation;
                }
                catch (Exception ex)
                {
                    _logger.LogError(
                        ex,
                        "Create company {CompanyName} error: {ExceptionMessage}",
                        registerCompanyViewModel.CompanyName,
                        ex.Message);

                    return BadRequest(
                        new ResponseViewModel
                        {
                            message = ex.Message
                        });
                }
            }

            return BadRequest();
        }

        /// <summary>
        /// Check Duplicate Username and Email one by one.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        [HttpPost]
        [Authorize(Roles = ApplicationUserRole.ResellerPortalUser)]
        public async Task<IActionResult> CheckUserNameUserEmailTaken(
            [FromBody]
            CheckExistingUsernameUserEmailViewModel checkModel)
        {
            if (User.Identity.IsAuthenticated)
            {
                var user = await GetCurrentValidResellerUser(
                    new List<string>()
                    {
                        ApplicationUserRole.ResellerPortalUser
                    });

                if (user == null)
                {
                    return Unauthorized();
                }

                ApplicationUser existingUser;

                if (!string.IsNullOrEmpty(checkModel.Email))
                {
                    existingUser = await _userManager.FindByEmailAsync(checkModel.Email);
                    if (existingUser != null)
                    {
                        return BadRequest(
                            new ResponseViewModel
                            {
                                message = "user email already taken"
                            });
                    }

                    return Ok();
                }

                if (!string.IsNullOrEmpty(checkModel.Username))
                {
                    existingUser = await _userManager.FindByNameAsync(checkModel.Username);
                    if (existingUser != null)
                    {
                        return BadRequest(
                            new ResponseViewModel
                            {
                                message = "username already taken"
                            });
                    }

                    return Ok();
                }
            }

            return BadRequest();
        }

        /// <summary>
        /// Register Client Company Staff Login Account for the web appp.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        [HttpPost]
        [Authorize(Roles = ApplicationUserRole.ResellerPortalUser)]
        public async Task<ActionResult> RegisterClientAccount([FromBody] RegisterClientAccountViewModel registerModel)
        {
            if (ModelState.IsValid && User.Identity.IsAuthenticated)
            {
                var user = await GetCurrentValidResellerUser(
                    new List<string>()
                    {
                        ApplicationUserRole.ResellerPortalUser
                    });

                if (user == null)
                {
                    return Unauthorized();
                }

                try
                {
                    var responseWrapper =
                        await _resellerPortalRepository.CreateClientAccount(user, registerModel);

                    if (!responseWrapper.IsSuccess)
                    {
                        return BadRequest(
                            new ResponseViewModel()
                            {
                                message = responseWrapper.ErrorMsg
                            });
                    }

                    var response = (string) responseWrapper.Data;

                    return Ok(
                        new ResponseViewModel
                        {
                            message = response
                        });
                }
                catch (Exception ex)
                {
                    _logger.LogError(
                        ex,
                        "Create Client Account username {Username}, email {Email} error : {ExceptionMessage}",
                        registerModel.Username,
                        registerModel.Email,
                        ex.Message);

                    return BadRequest(
                        new ResponseViewModel
                        {
                            message = ex.Message
                        });
                }
            }

            return BadRequest();
        }

        /// <summary>
        /// Get available staff account in client companies.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        [HttpPost]
        [Authorize(Roles = ApplicationUserRole.ResellerPortalUser)]
        public async Task<ActionResult<AvailableAccountDto>> GetAvailableAccountsToSwitch()
        {
            if (ModelState.IsValid && User.Identity.IsAuthenticated)
            {
                var user = await GetCurrentValidResellerUser(
                    new List<string>()
                    {
                        ApplicationUserRole.ResellerPortalUser
                    });

                if (user == null)
                {
                    return Unauthorized();
                }

                var resellerCompanyStaff = await _appDbContext.ResellerStaffs.AsNoTracking()
                    .Include(x => x.ProfilePicture).Include(x => x.ResellerCompanyProfile).ThenInclude(x => x.Company)
                    .FirstOrDefaultAsync(x => x.IdentityId == user.Id);

                if (resellerCompanyStaff == null)
                {
                    return BadRequest(
                        new ResponseViewModel()
                        {
                            message = "Cannot find the reseller staff in the reseller company"
                        });
                }

                var resellerStaffInfo = new ResellerStaffInformation
                {
                    Id = resellerCompanyStaff.Id,
                    IdentityId = resellerCompanyStaff.IdentityId,
                    UserInfo = _mapper.Map<UserInfoResponse>(user),
                    ResellerProfileId = resellerCompanyStaff.ResellerCompanyProfileId,
                    ResellerProfileInformation =
                        _mapper.Map<ResellerProfileInformation>(resellerCompanyStaff.ResellerCompanyProfile),
                    ResellerCompanyId = resellerCompanyStaff.ResellerCompanyProfile.CompanyId,
                    CompanyResponse = _mapper.Map<CompanyResponse>(resellerCompanyStaff.ResellerCompanyProfile.Company),
                    Locale = resellerCompanyStaff.Locale,
                    Position = resellerCompanyStaff.Position,
                    TimeZoneInfoId = resellerCompanyStaff.TimeZoneInfoId,
                    ProfilePictureURL = resellerCompanyStaff.ProfilePicture != null
                        ? _azureBlobStorageService.GetAzureBlobSasUri(
                            resellerCompanyStaff.ProfilePicture.Filename,
                            resellerCompanyStaff.ProfilePicture.BlobContainer)
                        : null
                };

                var availableAccDto = new AvailableAccountDto()
                {
                    ResellerStaffInformation = resellerStaffInfo
                };

                var resellerStaves = await _appDbContext.UserRoleStaffs.Include(x => x.Company).Include(x => x.Identity)
                    .Where(x => x.IdentityId == user.Id && x.Company.CompanyType == CompanyType.ResellerClient)
                    .ToListAsync();

                if (!resellerStaves.Any())
                {
                    return BadRequest(
                        new ResponseViewModel()
                        {
                            message = "Cannot find the reseller staff in client companies"
                        });
                }

                var stavesResponse = new List<StaffResponse>();

                foreach (var resellerStaff in resellerStaves)
                {
                    stavesResponse.Add(_mapper.Map<StaffResponse>(resellerStaff));
                }

                availableAccDto.ResellerStaffInClientCompanies = stavesResponse;

                return availableAccDto;
            }

            return BadRequest();
        }

        /// <summary>
        /// Set Assignee for a client company.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        [HttpPost]
        [Authorize(Roles = ApplicationUserRole.ResellerPortalUser)]
        public async Task<ActionResult> SetAssignee([FromBody] SetCompanyAssigneeRequest request)
        {
            if (ModelState.IsValid && User.Identity.IsAuthenticated)
            {
                var user = await GetCurrentValidResellerUser(
                    new List<string>()
                    {
                        ApplicationUserRole.ResellerPortalUser
                    });

                if (user == null)
                {
                    return Unauthorized();
                }

                var resellerProfileResponse = await _resellerPortalRepository.GetUserIdentityReseller(user);

                if (!resellerProfileResponse.IsSuccess)
                {
                    return BadRequest(
                        new ResponseViewModel()
                        {
                            message = "Reseller Profile Not Found"
                        });
                }

                var resellerProfileId = (string) resellerProfileResponse.Data;

                var clientProfile = await _appDbContext.ResellerClientCompanyProfiles.FirstOrDefaultAsync(
                    x => x.Id == request.ClientProfileId && x.ResellerCompanyProfileId == resellerProfileId);

                if (clientProfile == null)
                {
                    return BadRequest(
                        new ResponseViewModel()
                        {
                            message = "Client Profile Not Found"
                        });
                }

                if (!await _appDbContext.ResellerStaffs.AnyAsync(
                        x => x.ResellerCompanyProfileId == resellerProfileId &&
                             x.IdentityId == request.AssigneeIdentityId))
                {
                    return BadRequest(
                        new ResponseViewModel()
                        {
                            message = "Reseller Staff Identity Id Not Found"
                        });
                }

                clientProfile.AssigneeIdentityId = request.AssigneeIdentityId;

                await _appDbContext.SaveChangesAsync();

                return Ok(
                    new ResponseViewModel()
                    {
                        message = "success"
                    });
            }

            return BadRequest();
        }

        /// <summary>
        /// Upload Reseller Client Company Logo.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        [HttpPost]
        [Authorize(Roles = ApplicationUserRole.ResellerPortalUser)]
        public async Task<ActionResult<ProfileLogoLinkDto>> UploadResellerClientCompanyLogo(
            [FromForm]
            UpdateCompanyLogoViewModel updateCompanyLogoViewModel)
        {
            if (ModelState.IsValid && User.Identity.IsAuthenticated)
            {
                var user = await GetCurrentValidResellerUser(
                    new List<string>()
                    {
                        ApplicationUserRole.ResellerPortalUser
                    });

                if (user == null)
                {
                    return Unauthorized();
                }

                var resellerClientCompanyProfile = await _appDbContext.ResellerClientCompanyProfiles
                    .Include(x => x.ClientCompany).ThenInclude(x => x.CompanyIconFile)
                    .FirstOrDefaultAsync(x => x.Id == updateCompanyLogoViewModel.CompanyProfileId);

                if (resellerClientCompanyProfile == null)
                {
                    return BadRequest(
                        new ResponseViewModel()
                        {
                            message = "Reseller Profile Not Found"
                        });
                }

                var updateResponse = await _resellerPortalRepository.UpdateResellerClientProfileLogo(
                    resellerClientCompanyProfile,
                    updateCompanyLogoViewModel.CompanyLogo);

                if (!updateResponse.IsSuccess)
                {
                    return BadRequest(
                        new ResponseViewModel()
                        {
                            message = updateResponse.ErrorMsg
                        });
                }

                var profileLogoLinkDto = new ProfileLogoLinkDto()
                {
                    ClientCompanyProfileId = resellerClientCompanyProfile.Id,
                    ClientCompanyName = resellerClientCompanyProfile.ClientCompany.CompanyName,
                    AccessUrl = (string) updateResponse.Data
                };

                return Ok(profileLogoLinkDto);
            }

            return BadRequest();
        }

        /// <summary>
        /// Get available subscription plans.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        [HttpPost]
        [Authorize(Roles = ApplicationUserRole.ResellerPortalUser)]
        public async Task<ActionResult<SubscriptionPlanDto>> GetSubscriptionPlans()
        {
            if (User.Identity.IsAuthenticated)
            {
                var user = await GetCurrentValidResellerUser(
                    new List<string>()
                    {
                        ApplicationUserRole.ResellerPortalUser
                    });

                if (user == null)
                {
                    return Unauthorized();
                }

                var resellerProfileResponse = await _resellerPortalRepository.GetUserIdentityReseller(user);

                if (!resellerProfileResponse.IsSuccess)
                {
                    return BadRequest(
                        new ResponseViewModel()
                        {
                            message = resellerProfileResponse.ErrorMsg
                        });
                }

                var resellerProfileId = (string) resellerProfileResponse.Data;

                var resellerCompanyProfile =
                    await _appDbContext.ResellerCompanyProfiles.FirstOrDefaultAsync(x => x.Id == resellerProfileId);

                if (resellerCompanyProfile == null)
                {
                    return BadRequest(
                        new ResponseViewModel()
                        {
                            message = "Reseller Profile Not Found"
                        });
                }

                var subscriptionPlanDtos = await _appDbContext.CoreSubscriptionPlans.Where(
                        x =>
                            ValidSubscriptionPlan.SubscriptionPlan.Contains(x.Id) ||
                            ValidSubscriptionPlan.CmsAllAddOn.Contains(x.Id))
                    .OrderBy(x => x.Amount).ProjectTo<SubscriptionPlanDto>(_mapper.ConfigurationProvider).ToListAsync();

                if (resellerCompanyProfile.ResellerDiscount > 0)
                {
                    var discountRate = 1 - resellerCompanyProfile.ResellerDiscount;
                    subscriptionPlanDtos.ForEach(
                        x =>
                        {
                            x.AmountWithDiscount = Math.Round(Convert.ToDecimal(x.Amount) * discountRate, 0);
                        });
                }

                return Ok(subscriptionPlanDtos);
            }

            return BadRequest();
        }

        /// <summary>
        /// Switch Plan before clicking confirm.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        [HttpPost]
        [Authorize(Roles = ApplicationUserRole.ResellerPortalUser)]
        public async Task<ActionResult<SwitchPlanResponse>> SwitchSubscriptionPlan([FromBody] SwitchPlanRequest request)
        {
            if (ModelState.IsValid && User.Identity.IsAuthenticated)
            {
                var user = await GetCurrentValidResellerUser(
                    new List<string>()
                    {
                        ApplicationUserRole.ResellerPortalUser
                    });

                if (user == null)
                {
                    return Unauthorized();
                }

                var resellerProfile = await _appDbContext.ResellerStaffs
                    .AsNoTracking()
                    .Include(x => x.ResellerCompanyProfile)
                    .ThenInclude(x => x.ClientCompanyProfiles)
                    .ThenInclude(x => x.ClientCompany)
                    .ThenInclude(x => x.BillRecords)
                    .ThenInclude(x => x.SubscriptionPlan)
                    .Where(x => x.IdentityId == user.Id)
                    .Select(x => x.ResellerCompanyProfile)
                    .FirstOrDefaultAsync();

                if (resellerProfile == null)
                {
                    return BadRequest(
                        new ResponseViewModel()
                        {
                            message = "Reseller Profile Not Found"
                        });
                }

                // Check if the reseller has a country tier and subscription plan config, if not then update it.
                resellerProfile =
                    await _resellerPortalRepository.CheckAndUpdateResellerSubscriptionPlanConfig(resellerProfile);

                var clientCompany = resellerProfile.ClientCompanyProfiles
                    .Where(x => x.ClientCompanyId == request.CompanyId)
                    .Select(x => x.ClientCompany)
                    .FirstOrDefault();

                if (clientCompany == null)
                {
                    return BadRequest(
                        new ResponseViewModel()
                        {
                            message = "Client Company Not Found"
                        });
                }

                var toSubscriptionPlanId = _resellerPortalRepository.GetResellerSubscriptionPlanId(
                    request.ToSubscriptionTier,
                    resellerProfile);

                if (toSubscriptionPlanId != request.ToSubscriptionPlanId)
                {
                    return BadRequest(
                        new ResponseViewModel()
                        {
                            message = "To Subscription Plan Id Not Same with Reseller Subscription Plan Config."
                        });
                }

                var fromSubscriptionPlan = await _appDbContext.CoreSubscriptionPlans
                    .AsNoTracking()
                    .FirstOrDefaultAsync(
                        x => x.Id == request.FromSubscriptionPlanId);

                if (fromSubscriptionPlan == null)
                {
                    return BadRequest(
                        new ResponseViewModel()
                        {
                            message = "From Subscription Plan Not Found"
                        });
                }

                var toSubscriptionPlan = await _appDbContext.CoreSubscriptionPlans
                    .AsNoTracking()
                    .FirstOrDefaultAsync(
                        x => ValidSubscriptionPlan.SubscriptionPlan.Contains(x.Id)
                             && x.Id == toSubscriptionPlanId);

                if (toSubscriptionPlan == null)
                {
                    return BadRequest(
                        new ResponseViewModel()
                        {
                            message = "To Subscription Plan Not Found"
                        });
                }

                switch (request.ChangePlanType)
                {
                    case ChangePlanType.Switch:
                    {
                        if (string.Equals(
                                request.FromSubscriptionPlanId,
                                request.ToSubscriptionPlanId,
                                StringComparison.CurrentCultureIgnoreCase))
                        {
                            return BadRequest(
                                new ResponseViewModel()
                                {
                                    message = "Not a switch plan request"
                                });
                        }

                        if (clientCompany.BillRecords != null)
                        {
                            var currentBillRecord = clientCompany.BillRecords
                                .OrderByDescending(x => x.created)
                                .ThenByDescending(x => x.PayAmount)
                                .FirstOrDefault(
                                    x => ValidSubscriptionPlan.SubscriptionPlan.Contains(x.SubscriptionPlanId)
                                         && x.Status != BillStatus.Inactive
                                         && x.Status != BillStatus.Terminated
                                         && x.PeriodStart <= DateTime.UtcNow
                                         && x.PeriodEnd > DateTime.UtcNow);

                            if (currentBillRecord == null)
                            {
                                return BadRequest(
                                    new ResponseViewModel()
                                    {
                                        message = "Plan Not Found in Existing Subscription"
                                    });
                            }

                            var response = new SwitchPlanResponse()
                            {
                                CompanyId = request.CompanyId,
                                CompanyName = clientCompany.CompanyName,
                                FromSubscriptionPlanId = request.FromSubscriptionPlanId,
                                FromSubscriptionPlanPricing =
                                    Math.Round(
                                        Convert.ToDecimal(fromSubscriptionPlan.Amount) *
                                        (1 - resellerProfile.ResellerDiscount),
                                        0),
                                ToSubscriptionPlanId = request.ToSubscriptionPlanId,
                                ToSubscriptionPlanPricing =
                                    Math.Round(
                                        Convert.ToDecimal(toSubscriptionPlan.Amount) *
                                        (1 - resellerProfile.ResellerDiscount),
                                        0),
                                CurrentBalance = resellerProfile.Balance,
                                Currency = resellerProfile.Currency,
                                EffectiveDate = DateTime.UtcNow,
                                CurrentSubscriptionExpiryDate = DateTime.UtcNow,
                                ChargeDate = DateTime.UtcNow,
                                ChangePlanType = request.ChangePlanType
                            };

                            response.TotalAmount = Math.Round(
                                Convert.ToDecimal(toSubscriptionPlan.Amount) * (1 - resellerProfile.ResellerDiscount),
                                0);

                            // Calculate refund amount
                            var currentAndExtendBillRecords = clientCompany.BillRecords
                                .OrderByDescending(x => x.created)
                                .ThenByDescending(x => x.PayAmount)
                                .Where(
                                    x => ValidSubscriptionPlan.SubscriptionPlan.Contains(x.SubscriptionPlanId)
                                         && x.Status is BillStatus.Active or BillStatus.Renewed
                                         && x.PeriodEnd > DateTime.UtcNow)
                                .ToList();

                            if (currentAndExtendBillRecords.Any())
                            {
                                currentAndExtendBillRecords.ForEach(
                                    billRecord =>
                                    {
                                        if (billRecord.PeriodEnd >= DateTime.UtcNow.AddDays(1)
                                            && billRecord.PaymentStatus == PaymentStatus.Paid
                                            && billRecord.PayAmount > 0)
                                        {
                                            response.RefundAmount ??= 0;
                                            decimal refundAmount;

                                            if (DateTime.UtcNow <= billRecord.PeriodStart)
                                            {
                                                refundAmount = Math.Round(Convert.ToDecimal(billRecord.PayAmount), 0);
                                            }
                                            else
                                            {
                                                refundAmount = Math.Round(
                                                    Convert.ToDecimal(
                                                        ((billRecord.PeriodEnd - billRecord.PeriodStart).TotalDays -
                                                         (DateTime.UtcNow - billRecord.PeriodStart).TotalDays) /
                                                        (billRecord.PeriodEnd - billRecord.PeriodStart).TotalDays) *
                                                    Convert.ToDecimal(billRecord.PayAmount),
                                                    0);
                                            }

                                            response.RefundAmount += refundAmount;
                                            response.TotalAmount -= refundAmount;
                                        }
                                    });
                            }

                            return Ok(response);
                        }
                        else
                        {
                            var response = new SwitchPlanResponse()
                            {
                                CompanyId = request.CompanyId,
                                FromSubscriptionPlanId = "No Existing Plan Bill Record",
                                ToSubscriptionPlanId = toSubscriptionPlan.Id,
                                ToSubscriptionPlanPricing =
                                    Math.Round(
                                        Convert.ToDecimal(toSubscriptionPlan.Amount) *
                                        (1 - resellerProfile.ResellerDiscount),
                                        0),
                                CurrentBalance = resellerProfile.Balance,
                                Currency = resellerProfile.Currency,
                                EffectiveDate = DateTime.UtcNow,
                                TotalAmount = Math.Round(
                                    Convert.ToDecimal(toSubscriptionPlan.Amount) *
                                    (1 - resellerProfile.ResellerDiscount),
                                    0),
                                ChangePlanType = request.ChangePlanType
                            };

                            return Ok(response);
                        }
                    }

                    case ChangePlanType.Extend:
                    {
                        if (!string.Equals(
                                request.FromSubscriptionPlanId,
                                request.ToSubscriptionPlanId,
                                StringComparison.CurrentCultureIgnoreCase))
                        {
                            return BadRequest(
                                new ResponseViewModel()
                                {
                                    message = "Not a extend plan request"
                                });
                        }

                        // Check the fromSubscriptionPlanId with the reseller subscription plan config when extend the plan
                        var fromSubscriptionPlanId = _resellerPortalRepository.GetResellerSubscriptionPlanId(
                            request.FromSubscriptionTier,
                            resellerProfile);

                        if (fromSubscriptionPlanId != request.FromSubscriptionPlanId)
                        {
                            return BadRequest(
                                new ResponseViewModel()
                                {
                                    message =
                                        "From Subscription Plan Id Not Same with Reseller Subscription Plan Config."
                                });
                        }

                        if (clientCompany.BillRecords != null)
                        {
                            var currentBillRecord = clientCompany.BillRecords
                                .OrderByDescending(x => x.created)
                                .ThenByDescending(x => x.PayAmount)
                                .FirstOrDefault(
                                    x => x.SubscriptionPlanId == request.FromSubscriptionPlanId
                                         && x.Status != BillStatus.Inactive
                                         && x.Status != BillStatus.Terminated
                                         && x.PeriodStart <= DateTime.UtcNow
                                         && x.PeriodEnd > DateTime.UtcNow);

                            var lastBillRecord = clientCompany.BillRecords
                                .OrderByDescending(x => x.created)
                                .ThenByDescending(x => x.PayAmount)
                                .FirstOrDefault(
                                    x => x.SubscriptionPlanId == request.FromSubscriptionPlanId
                                         && x.Status == BillStatus.Active
                                         && x.PeriodEnd > DateTime.UtcNow);

                            if (currentBillRecord == null || lastBillRecord == null)
                            {
                                return BadRequest(
                                    new ResponseViewModel()
                                    {
                                        message = "Plan Not Found in Existing Subscription"
                                    });
                            }

                            var response = new SwitchPlanResponse()
                            {
                                CompanyId = request.CompanyId,
                                FromSubscriptionPlanId = fromSubscriptionPlan.Id,
                                FromSubscriptionPlanPricing =
                                    Math.Round(
                                        Convert.ToDecimal(fromSubscriptionPlan.Amount) *
                                        (1 - resellerProfile.ResellerDiscount),
                                        0),
                                ToSubscriptionPlanId = toSubscriptionPlan.Id,
                                ToSubscriptionPlanPricing =
                                    Math.Round(
                                        Convert.ToDecimal(toSubscriptionPlan.Amount) *
                                        (1 - resellerProfile.ResellerDiscount),
                                        0),
                                CurrentBalance = resellerProfile.Balance,
                                ChargeDate = DateTime.UtcNow,
                                Currency = resellerProfile.Currency,
                                EffectiveDate = lastBillRecord.PeriodEnd,
                                CurrentSubscriptionExpiryDate = lastBillRecord.PeriodEnd,
                                ChangePlanType = request.ChangePlanType
                            };

                            // Extend add-on plan
                            var addonBillRecords = clientCompany.BillRecords
                                .OrderByDescending(x => x.created)
                                .ThenByDescending(x => x.PayAmount)
                                .Where(
                                    x => ValidSubscriptionPlan.CmsAllAddOn.Contains(x.SubscriptionPlanId)
                                         && x.PeriodEnd > DateTime.UtcNow
                                         && x.Status == BillStatus.Active)
                                .ToList();

                            if (addonBillRecords.Any())
                            {
                                addonBillRecords.ForEach(
                                    billRecord =>
                                    {
                                        response.AddOnInitialAmount ??= 0;

                                        if (billRecord.PeriodEnd.Date < lastBillRecord.PeriodEnd.Date)
                                        {
                                            var subscriptionPeriodEnd = lastBillRecord.PeriodEnd;

                                            // Calculate the full months left from the end of the current subscription plan to now
                                            var fullMonth = 0;
                                            while (billRecord.PeriodEnd < subscriptionPeriodEnd.AddMonths(-1))
                                            {
                                                fullMonth++;
                                                subscriptionPeriodEnd = subscriptionPeriodEnd.AddMonths(-1);
                                            }

                                            // Calculate the proportion of the remaining days in the current month of subscription
                                            decimal proportion = 0;
                                            if ((subscriptionPeriodEnd - billRecord.PeriodEnd).TotalDays > 1)
                                            {
                                                proportion =
                                                    Convert.ToDecimal(
                                                        Math.Max(
                                                            (subscriptionPeriodEnd - billRecord.PeriodEnd).Days,
                                                            1)) /
                                                    Convert.ToDecimal(
                                                        (subscriptionPeriodEnd - subscriptionPeriodEnd.AddMonths(-1))
                                                        .Days);
                                            }

                                            var periodOfAddOnInMonths = proportion + fullMonth;

                                            // Calculate add-on plan amount which period end before extended subscription plan
                                            var addOnInitialAmount = Math.Round(
                                                    Math.Round(
                                                        Convert.ToDecimal(billRecord.SubscriptionPlan.Amount) *
                                                        (1 - resellerProfile.ResellerDiscount),
                                                        0) * periodOfAddOnInMonths,
                                                    0) * billRecord.quantity;

                                            response.AddOnInitialAmount ??= 0;
                                            response.AddOnInitialAmount += addOnInitialAmount;
                                        }

                                        var addOnMonthlyAmount = Math.Round(
                                                Convert.ToDecimal(billRecord.SubscriptionPlan.Amount) *
                                                (1 - resellerProfile.ResellerDiscount),
                                                0) * billRecord.quantity;

                                        response.AddOnMonthlyAmount ??= 0;
                                        response.AddOnMonthlyAmount += addOnMonthlyAmount;
                                    });
                            }

                            response.TotalAmount = Math.Round(
                                Convert.ToDecimal(toSubscriptionPlan.Amount) * (1 - resellerProfile.ResellerDiscount),
                                0) + (response.AddOnInitialAmount ?? 0);

                            return Ok(response);
                        }
                        else
                        {
                            var response = new SwitchPlanResponse()
                            {
                                CompanyId = request.CompanyId,
                                CompanyName = clientCompany.CompanyName,
                                FromSubscriptionPlanId = "No Existing Plan Bill Record",
                                ToSubscriptionPlanId = toSubscriptionPlan.Id,
                                ToSubscriptionPlanPricing =
                                    Math.Round(
                                        Convert.ToDecimal(toSubscriptionPlan.Amount) *
                                        (1 - resellerProfile.ResellerDiscount),
                                        0),
                                CurrentBalance = resellerProfile.Balance,
                                TotalAmount =
                                    Math.Round(
                                        Convert.ToDecimal(toSubscriptionPlan.Amount) *
                                        (1 - resellerProfile.ResellerDiscount),
                                        0),
                                Currency = resellerProfile.Currency,
                                EffectiveDate = DateTime.UtcNow,
                                ChargeDate = DateTime.UtcNow,
                                ChangePlanType = request.ChangePlanType
                            };

                            return Ok(response);
                        }
                    }

                    case ChangePlanType.Upgrade:
                    case ChangePlanType.Downgrade:
                    case ChangePlanType.Unsubscribe:
                    default:
                    {
                        return BadRequest(
                            new ResponseViewModel()
                            {
                                message = "Invalid Change Plan Type"
                            });
                    }
                }
            }

            return BadRequest();
        }

        /// <summary>
        /// Confirm the plan change.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        [HttpPost]
        [Authorize(Roles = ApplicationUserRole.ResellerPortalUser)]
        public async Task<ActionResult<SwitchPlanResponse>> ConfirmSwitchSubscriptionPlan(
            [FromBody]
            ConfirmSwitchPlanRequest request)
        {
            if (ModelState.IsValid && User.Identity.IsAuthenticated)
            {
                var user = await GetCurrentValidResellerUser(
                    new List<string>()
                    {
                        ApplicationUserRole.ResellerPortalUser
                    });

                if (user == null)
                {
                    return Unauthorized();
                }

                var resellerProfile = await _appDbContext.ResellerStaffs
                    .AsNoTracking()
                    .Include(x => x.ResellerCompanyProfile)
                    .ThenInclude(x => x.ClientCompanyProfiles)
                    .ThenInclude(x => x.ClientCompany)
                    .ThenInclude(x => x.BillRecords)
                    .ThenInclude(x => x.SubscriptionPlan)
                    .Where(x => x.IdentityId == user.Id)
                    .Select(x => x.ResellerCompanyProfile)
                    .FirstOrDefaultAsync();

                if (resellerProfile == null)
                {
                    return BadRequest(
                        new ResponseViewModel()
                        {
                            message = "Reseller Profile Not Found"
                        });
                }

                // Check if the reseller has a country tier and subscription plan config, if not then update it.
                resellerProfile =
                    await _resellerPortalRepository.CheckAndUpdateResellerSubscriptionPlanConfig(resellerProfile);

                var clientCompany = resellerProfile.ClientCompanyProfiles
                    .Where(x => x.ClientCompanyId == request.CompanyId)
                    .Select(x => x.ClientCompany)
                    .FirstOrDefault();

                if (clientCompany == null)
                {
                    return BadRequest(
                        new ResponseViewModel()
                        {
                            message = "Client Company Not Found"
                        });
                }

                var toSubscriptionPlanId = _resellerPortalRepository.GetResellerSubscriptionPlanId(
                    request.ToSubscriptionTier,
                    resellerProfile);

                if (toSubscriptionPlanId != request.ToSubscriptionPlanId)
                {
                    return BadRequest(
                        new ResponseViewModel()
                        {
                            message = "To Subscription Plan Id Not Same with Reseller Subscription Plan Config."
                        });
                }

                var fromSubscriptionPlan = await _appDbContext.CoreSubscriptionPlans
                    .AsNoTracking()
                    .FirstOrDefaultAsync(
                        x => x.Id == request.FromSubscriptionPlanId);

                if (fromSubscriptionPlan == null)
                {
                    return BadRequest(
                        new ResponseViewModel()
                        {
                            message = "From Subscription Plan Not Found"
                        });
                }

                var toSubscriptionPlan = await _appDbContext.CoreSubscriptionPlans
                    .AsNoTracking()
                    .FirstOrDefaultAsync(
                        x => ValidSubscriptionPlan.SubscriptionPlan.Contains(x.Id)
                             && x.Id == toSubscriptionPlanId);

                if (toSubscriptionPlan == null)
                {
                    return BadRequest(
                        new ResponseViewModel()
                        {
                            message = "To Subscription Plan Not Found"
                        });
                }

                switch (request.ChangePlanType)
                {
                    case ChangePlanType.Switch:
                    {
                        if (string.Equals(
                                request.FromSubscriptionPlanId,
                                request.ToSubscriptionPlanId,
                                StringComparison.CurrentCultureIgnoreCase))
                        {
                            return BadRequest(
                                new ResponseViewModel()
                                {
                                    message = "Not a switch plan request"
                                });
                        }

                        var totalAmount = Math.Round(
                            Convert.ToDecimal(toSubscriptionPlan.Amount) * (1 - resellerProfile.ResellerDiscount),
                            0) * request.Month;

                        var response = new SwitchPlanResponse()
                        {
                            CompanyId = request.CompanyId,
                            CompanyName = clientCompany.CompanyName,
                            FromSubscriptionPlanId = fromSubscriptionPlan.Id,
                            FromSubscriptionPlanPricing =
                                Math.Round(
                                    Convert.ToDecimal(fromSubscriptionPlan.Amount) *
                                    (1 - resellerProfile.ResellerDiscount),
                                    0),
                            ToSubscriptionPlanId = toSubscriptionPlan.Id,
                            ToSubscriptionPlanPricing =
                                Math.Round(
                                    Convert.ToDecimal(toSubscriptionPlan.Amount) *
                                    (1 - resellerProfile.ResellerDiscount),
                                    0),
                            Currency = resellerProfile.Currency,
                            TotalAmount = totalAmount,
                            ChangePlanType = request.ChangePlanType,
                            Month = request.Month
                        };

                        var currentClientCompany = await _appDbContext.CompanyCompanies
                            .Include(x => x.BillRecords)
                            .ThenInclude(x => x.SubscriptionPlan)
                            .FirstOrDefaultAsync(x => x.Id == request.CompanyId);

                        // Do the refund if there is more than 1 day from the expiry date
                        if (currentClientCompany.BillRecords != null)
                        {
                            var currentBillRecord = currentClientCompany.BillRecords
                                .OrderByDescending(x => x.created)
                                .ThenByDescending(x => x.PayAmount)
                                .FirstOrDefault(
                                    x => ValidSubscriptionPlan.SubscriptionPlan.Contains(x.SubscriptionPlanId)
                                         && x.Status != BillStatus.Inactive
                                         && x.Status != BillStatus.Terminated
                                         && x.PeriodStart <= DateTime.UtcNow
                                         && x.PeriodEnd > DateTime.UtcNow);

                            if (currentBillRecord == null)
                            {
                                return BadRequest(
                                    new ResponseViewModel()
                                    {
                                        message = "Plan Not Found in Existing Subscription"
                                    });
                            }

                            // Cancel and refund the current and extended plan
                            var currentAndExtendedBillRecords = currentClientCompany.BillRecords
                                .OrderByDescending(x => x.created)
                                .ThenByDescending(x => x.PayAmount)
                                .Where(
                                    x => ValidSubscriptionPlan.SubscriptionPlan.Contains(x.SubscriptionPlanId)
                                         && x.Status is BillStatus.Active or BillStatus.Renewed
                                         && x.PeriodEnd > DateTime.UtcNow)
                                .ToList();

                            if (currentAndExtendedBillRecords.Any())
                            {
                                var refundResellerTransactionLogs = new List<ResellerTransactionLog>();

                                currentAndExtendedBillRecords.ForEach(
                                    billRecord =>
                                    {
                                        if (billRecord.PeriodEnd >= DateTime.UtcNow.AddDays(1) &&
                                            billRecord.PaymentStatus == PaymentStatus.Paid && billRecord.PayAmount > 0)
                                        {
                                            response.RefundAmount ??= 0;
                                            decimal refundAmount;

                                            if (DateTime.UtcNow <= billRecord.PeriodStart)
                                            {
                                                refundAmount = Math.Round(Convert.ToDecimal(billRecord.PayAmount), 0);
                                            }
                                            else
                                            {
                                                refundAmount = Math.Round(
                                                    Convert.ToDecimal(
                                                        ((billRecord.PeriodEnd - billRecord.PeriodStart).TotalDays -
                                                         (DateTime.UtcNow - billRecord.PeriodStart).TotalDays) /
                                                        (billRecord.PeriodEnd - billRecord.PeriodStart).TotalDays) *
                                                    Convert.ToDecimal(billRecord.PayAmount),
                                                    0);
                                            }

                                            var refundTransactionLog = new ResellerTransactionLog()
                                            {
                                                ResellerCompanyProfileId = resellerProfile.Id,
                                                Amount = refundAmount,
                                                Currency = resellerProfile.Currency,
                                                BillRecordId = billRecord.Id,
                                                ClientCompanyId = request.CompanyId,
                                                TransactionMode = TransactionMode.Refund,
                                                UserIdentityId = user?.Id,
                                                TransactionCategory = ResellerTransactionCategory.Subscription,
                                                TransactionAction = "Refund " +
                                                                    _resellerPortalRepository.SetTransactionAction(
                                                                        billRecord.SubscriptionPlan)
                                            };

                                            refundResellerTransactionLogs.Add(refundTransactionLog);

                                            response.RefundAmount += refundAmount;
                                            response.TotalAmount -= refundAmount;

                                            billRecord.Status = BillStatus.Inactive;
                                            billRecord.PeriodEnd = DateTime.UtcNow;
                                        }
                                        else
                                        {
                                            billRecord.Status = BillStatus.Canceled;
                                        }

                                        billRecord.UpdatedAt = DateTime.UtcNow;
                                    });

                                if (refundResellerTransactionLogs.Any())
                                {
                                    await _appDbContext.ResellerTransactionLogs.AddRangeAsync(
                                        refundResellerTransactionLogs);
                                }

                                if (resellerProfile.Balance + (response.RefundAmount ?? 0) - totalAmount <
                                    resellerProfile.BalanceMinimumLimit)
                                {
                                    return BadRequest(
                                        new ResponseViewModel()
                                        {
                                            message =
                                                $"Insufficient Balance, You have reached the Minimum Balance Limit. " +
                                                $"Balance: {resellerProfile.Balance:0.00}, " +
                                                $"Minimum Balance Limit: {resellerProfile.BalanceMinimumLimit:0.00}."
                                        });
                                }

                                await _appDbContext.SaveChangesAsync();
                            }

                            // Cancel all add-on plan
                            var addonBillRecords = currentClientCompany.BillRecords
                                .Where(
                                    x => ValidSubscriptionPlan.CmsAllAddOn.Contains(x.SubscriptionPlanId)
                                         && x.Status is BillStatus.Active or BillStatus.Renewed
                                         && x.PeriodEnd > DateTime.UtcNow)
                                .OrderByDescending(x => x.Id)
                                .ToList();

                            if (addonBillRecords.Any())
                            {
                                addonBillRecords.ForEach(
                                    billRecord =>
                                    {
                                        billRecord.UpdatedAt = DateTime.UtcNow;
                                        billRecord.Status = BillStatus.Canceled;
                                    });
                            }

                            var toBillRecord = new BillRecord()
                            {
                                CompanyId = request.CompanyId,
                                SubscriptionPlanId = toSubscriptionPlan.Id,
                                PaidByReseller = true,
                                Status = BillStatus.Active,
                                PaymentStatus = totalAmount > 0 ? PaymentStatus.Paid : PaymentStatus.FreeOfCharge,
                                SubscriptionTier = toSubscriptionPlan.SubscriptionTier,
                                PayAmount = Convert.ToDouble(totalAmount),
                                currency = resellerProfile.Currency,
                                PeriodStart = DateTime.UtcNow,
                                PeriodEnd = DateTime.UtcNow.AddMonths(request.Month),
                                quantity = 1
                            };
                            await _appDbContext.CompanyBillRecords.AddAsync(toBillRecord);
                            await _appDbContext.SaveChangesAsync();

                            var toTransactionLog = new ResellerTransactionLog()
                            {
                                ResellerCompanyProfileId = resellerProfile.Id,
                                Amount = totalAmount,
                                Currency = resellerProfile.Currency,
                                BillRecordId = toBillRecord.Id,
                                ClientCompanyId = request.CompanyId,
                                TransactionMode = TransactionMode.Debit,
                                UserIdentityId = user?.Id,
                                TransactionCategory = ResellerTransactionCategory.Subscription,
                                TransactionAction = "Switch " +
                                                    _resellerPortalRepository.SetTransactionAction(toSubscriptionPlan)
                            };
                            await _appDbContext.ResellerTransactionLogs.AddAsync(toTransactionLog);
                            await _appDbContext.SaveChangesAsync();

                            var resellerProfileToUpdate = await _appDbContext.ResellerCompanyProfiles
                                .FirstOrDefaultAsync(x => x.Id == resellerProfile.Id);

                            resellerProfileToUpdate.TopUp += response.RefundAmount ?? 0;
                            resellerProfileToUpdate.Debited += totalAmount;

                            await _appDbContext.SaveChangesAsync();

                            await _resellerPortalRepository.SetSubscriptionPlanMaximumUsage(clientCompany);

                            await _resellerPortalRepository.AddResellerActivityLog(
                                new ResellerActivityLog
                                {
                                    CompanyId = request.CompanyId,
                                    CreatedByUserId = user.Id,
                                    Category = ResellerActivityLogCategory.Subscription,
                                    Action = $"Switch Subscription Plan: {toSubscriptionPlan.SubscriptionName}"
                                });

                            response.EffectiveDate = toBillRecord.PeriodStart;
                            response.CurrentBalance = resellerProfileToUpdate.Balance;

                            // Update Company API Keys limit
                            await _appDbContext.CompanyAPIKeys
                                .Where(x => x.CompanyId == request.CompanyId)
                                .ExecuteUpdateAsync(
                                    key =>
                                        key
                                            .SetProperty(k => k.CallLimit, toSubscriptionPlan.MaximumAPICall)
                                            .SetProperty(k => k.Calls, 0));

                            // HubSpot
                            if (ValidSubscriptionPlan.PremiumTier.Contains(request.ToSubscriptionPlanId) &&
                                (ValidSubscriptionPlan.ProTier.Contains(request.FromSubscriptionPlanId) ||
                                 ValidSubscriptionPlan.FreePlans.Contains(request.FromSubscriptionPlanId)))
                            {
                                BackgroundJob.Enqueue<IInternalHubSpotService>(
                                    x => x.SetCompanyOwnerUpgradeToPremiumPlanFlag(request.CompanyId));
                                BackgroundJob.Enqueue<ICoreService>(
                                    x => x.SyncCompanyStaffsToSleekFlowContactsBackground(request.CompanyId));
                            }

                            if (ValidSubscriptionPlan.ProTier.Contains(request.ToSubscriptionPlanId) &&
                                ValidSubscriptionPlan.FreePlans.Contains(request.FromSubscriptionPlanId))
                            {
                                BackgroundJob.Enqueue<IInternalHubSpotService>(
                                    x => x.SetCompanyOwnerUpgradeToProPlanFlag(request.CompanyId));
                                BackgroundJob.Enqueue<ICoreService>(
                                    x => x.SyncCompanyStaffsToSleekFlowContactsBackground(request.CompanyId));
                            }

                            BackgroundJob.Enqueue<IInternalHubSpotService>(x => x.SyncCompany(request.CompanyId, null));

                            var staff = await _appDbContext.UserRoleStaffs
                                .AsNoTracking()
                                .FirstOrDefaultAsync(
                                    x => x.CompanyId == request.CompanyId &&
                                         x.IdentityId == user.Id);

                            BackgroundJob.Enqueue<IFlowHubService>(
                                x => x.UpdateFlowHubConfigAsync(request.CompanyId, null));
                            BackgroundJob.Enqueue<IIntelligentHubService>(
                                x => x.RefreshIntelligentHubConfigAsync(request.CompanyId));
                            BackgroundJob.Enqueue<ICrmHubService>(
                                x => x.RefreshCrmHubConfigAsync(request.CompanyId, staff));

                            BackgroundJob.Enqueue<IInternalAnalyticService>(
                                x => x.UpdateCompanyAllTimeRevenueAnalyticInfo(
                                    new List<string>()
                                    {
                                        request.CompanyId
                                    }));

                            await _companyInfoCacheService.RemoveCompanyInfoCache(request.CompanyId);

                            return Ok(response);
                        }

                        return BadRequest(
                            new ResponseViewModel()
                            {
                                message = "From Subscription Plan Id Not Found in Bill Records"
                            });
                    }

                    case ChangePlanType.Extend:
                    {
                        if (!string.Equals(
                                request.FromSubscriptionPlanId,
                                request.ToSubscriptionPlanId,
                                StringComparison.CurrentCultureIgnoreCase))
                        {
                            return BadRequest(
                                new ResponseViewModel()
                                {
                                    message = "Not a extend plan request"
                                });
                        }

                        // Check the fromSubscriptionPlanId with the reseller subscription plan config when extend the plan
                        var fromSubscriptionPlanId = _resellerPortalRepository.GetResellerSubscriptionPlanId(
                            request.FromSubscriptionTier,
                            resellerProfile);

                        if (fromSubscriptionPlanId != request.FromSubscriptionPlanId)
                        {
                            return BadRequest(
                                new ResponseViewModel()
                                {
                                    message =
                                        "From Subscription Plan Id Not Same with Reseller Subscription Plan Config."
                                });
                        }

                        var response = new SwitchPlanResponse()
                        {
                            CompanyId = request.CompanyId,
                            CompanyName = clientCompany.CompanyName,
                            FromSubscriptionPlanId = fromSubscriptionPlan.Id,
                            ToSubscriptionPlanId = toSubscriptionPlan.Id,
                            CurrentBalance = resellerProfile.Balance,
                            Currency = resellerProfile.Currency,
                            ChargeDate = DateTime.UtcNow,
                            ChangePlanType = request.ChangePlanType,
                            Month = request.Month
                        };

                        var currentClientCompany = await _appDbContext.CompanyCompanies
                            .Include(x => x.BillRecords)
                            .ThenInclude(x => x.SubscriptionPlan)
                            .FirstOrDefaultAsync(x => x.Id == request.CompanyId);

                        if (currentClientCompany.BillRecords != null)
                        {
                            var currentBillRecord = currentClientCompany.BillRecords
                                .OrderByDescending(x => x.created)
                                .ThenByDescending(x => x.PayAmount)
                                .FirstOrDefault(
                                    x => x.SubscriptionPlanId == request.FromSubscriptionPlanId
                                         && x.Status != BillStatus.Inactive
                                         && x.Status != BillStatus.Terminated
                                         && x.PeriodStart <= DateTime.UtcNow
                                         && x.PeriodEnd > DateTime.UtcNow);

                            var lastBillRecord = currentClientCompany.BillRecords
                                .OrderByDescending(x => x.created)
                                .ThenByDescending(x => x.PayAmount)
                                .FirstOrDefault(
                                    x => x.SubscriptionPlanId == request.FromSubscriptionPlanId
                                         && x.Status == BillStatus.Active
                                         && x.PeriodEnd > DateTime.UtcNow);

                            if (currentBillRecord == null || lastBillRecord == null)
                            {
                                return BadRequest(
                                    new ResponseViewModel()
                                    {
                                        message = "Plan Not Found in Existing Subscription"
                                    });
                            }

                            lastBillRecord.Status = BillStatus.Renewed;
                            await _appDbContext.SaveChangesAsync();

                            var subscriptionTotalAmount = Math.Round(
                                Convert.ToDecimal(toSubscriptionPlan.Amount) * (1 - resellerProfile.ResellerDiscount),
                                0) * request.Month;

                            var subscriptionExtendBillRecord = new BillRecord()
                            {
                                CompanyId = request.CompanyId,
                                SubscriptionPlanId = request.ToSubscriptionPlanId,
                                PaidByReseller = true,
                                Status = BillStatus.Active,
                                SubscriptionTier = toSubscriptionPlan.SubscriptionTier,
                                currency = resellerProfile.Currency,
                                quantity = 1,
                                PayAmount = Convert.ToDouble(subscriptionTotalAmount),
                                PeriodStart = lastBillRecord.PeriodEnd,
                                PeriodEnd = lastBillRecord.PeriodEnd.AddMonths(request.Month),
                                ExtendFromBillRecordId = lastBillRecord.Id,
                                PaymentStatus =
                                    request.ToSubscriptionPlanId.Contains("free")
                                        ? PaymentStatus.FreeOfCharge
                                        : PaymentStatus.Paid
                            };

                            await _appDbContext.CompanyBillRecords.AddAsync(subscriptionExtendBillRecord);
                            await _appDbContext.SaveChangesAsync();

                            var subscriptionExtendTransactionLog = new ResellerTransactionLog()
                            {
                                ResellerCompanyProfileId = resellerProfile.Id,
                                Amount = subscriptionTotalAmount,
                                Currency = resellerProfile.Currency,
                                BillRecordId = subscriptionExtendBillRecord.Id,
                                ClientCompanyId = request.CompanyId,
                                TransactionMode = TransactionMode.Debit,
                                UserIdentityId = user?.Id,
                                TransactionCategory = ResellerTransactionCategory.Subscription,
                                TransactionAction = "Extend " +
                                                    _resellerPortalRepository.SetTransactionAction(toSubscriptionPlan)
                            };

                            await _appDbContext.ResellerTransactionLogs.AddAsync(subscriptionExtendTransactionLog);
                            await _appDbContext.SaveChangesAsync();

                            // Get all add-on plan
                            var addOnBillRecords = currentClientCompany.BillRecords
                                .OrderByDescending(x => x.created)
                                .ThenByDescending(x => x.PayAmount)
                                .Where(
                                    x => ValidSubscriptionPlan.CmsAllAddOn.Contains(x.SubscriptionPlanId)
                                         && x.Status == BillStatus.Active
                                         && x.PeriodEnd > DateTime.UtcNow)
                                .ToList();

                            decimal addOnTotalAmount = 0;

                            if (addOnBillRecords.Any())
                            {
                                foreach (var billRecord in addOnBillRecords)
                                {
                                    var addOnInitialAmount = 0M;

                                    if (billRecord.PeriodEnd.Date != lastBillRecord.PeriodEnd.Date)
                                    {
                                        var subscriptionPeriodEnd = lastBillRecord.PeriodEnd;

                                        // Calculate the full months left from the end of the current subscription plan to now
                                        var fullMonth = 0;
                                        while (billRecord.PeriodEnd < subscriptionPeriodEnd.AddMonths(-1))
                                        {
                                            fullMonth++;
                                            subscriptionPeriodEnd = subscriptionPeriodEnd.AddMonths(-1);
                                        }

                                        // Calculate the proportion of the remaining days in the current month of subscription
                                        decimal proportion = 0;
                                        if ((subscriptionPeriodEnd - billRecord.PeriodEnd).TotalDays > 1)
                                        {
                                            proportion =
                                                Convert.ToDecimal(
                                                    Math.Max((subscriptionPeriodEnd - billRecord.PeriodEnd).Days, 1)) /
                                                Convert.ToDecimal(
                                                    (subscriptionPeriodEnd - subscriptionPeriodEnd.AddMonths(-1)).Days);
                                        }

                                        var periodOfAddOnInMonths = proportion + fullMonth;

                                        // Calculate add-on plan amount which period end before extended subscription plan
                                        addOnInitialAmount = Math.Round(
                                            Math.Round(
                                                Convert.ToDecimal(billRecord.SubscriptionPlan.Amount),
                                                0) * periodOfAddOnInMonths,
                                            0) * billRecord.quantity;
                                    }

                                    var addOnMonthlyAmount = Math.Round(
                                        Convert.ToDecimal(billRecord.SubscriptionPlan.Amount) *
                                        (1 - resellerProfile.ResellerDiscount),
                                        0) * billRecord.quantity;

                                    billRecord.Status = BillStatus.Renewed;

                                    var currentAmount = addOnInitialAmount + (addOnMonthlyAmount * request.Month);

                                    var addOnExtendBillRecord = new BillRecord()
                                    {
                                        CompanyId = request.CompanyId,
                                        SubscriptionPlanId = billRecord.SubscriptionPlanId,
                                        PaidByReseller = true,
                                        Status = BillStatus.Active,
                                        SubscriptionTier = billRecord.SubscriptionTier,
                                        currency = resellerProfile.Currency,
                                        PayAmount = Convert.ToDouble(currentAmount),
                                        PeriodStart = billRecord.PeriodEnd,
                                        PeriodEnd =
                                            lastBillRecord.PeriodEnd.AddMonths(request.Month),
                                        PaymentStatus = PaymentStatus.Paid,
                                        ExtendFromBillRecordId = billRecord.Id,
                                        quantity = billRecord.quantity
                                    };

                                    await _appDbContext.CompanyBillRecords.AddAsync(addOnExtendBillRecord);
                                    await _appDbContext.SaveChangesAsync();

                                    addOnTotalAmount += currentAmount;

                                    var addOnExtendTransactionLog = new ResellerTransactionLog()
                                    {
                                        ResellerCompanyProfileId = resellerProfile.Id,
                                        Amount = Convert.ToDecimal(currentAmount),
                                        Currency = resellerProfile.Currency,
                                        BillRecordId = addOnExtendBillRecord.Id,
                                        ClientCompanyId = request.CompanyId,
                                        TransactionMode = TransactionMode.Debit,
                                        UserIdentityId = user?.Id,
                                        TransactionCategory = ResellerTransactionCategory.AddOn,
                                        TransactionAction = "Extend " +
                                                            _resellerPortalRepository.SetTransactionAction(
                                                                billRecord.SubscriptionPlan)
                                    };

                                    await _appDbContext.ResellerTransactionLogs.AddAsync(addOnExtendTransactionLog);
                                    await _appDbContext.SaveChangesAsync();
                                }
                            }

                            var totalAmount = subscriptionTotalAmount + addOnTotalAmount;

                            if (resellerProfile.Balance - totalAmount < resellerProfile.BalanceMinimumLimit)
                            {
                                return BadRequest(
                                    new ResponseViewModel
                                    {
                                        message =
                                            $"Insufficient Balance, You have reached the Minimum Balance Limit. " +
                                            $"Balance: {resellerProfile.Balance:0.00}, " +
                                            $"Minimum Balance Limit: {resellerProfile.BalanceMinimumLimit:0.00}."
                                    });
                            }

                            await _appDbContext.SaveChangesAsync();

                            var resellerProfileToUpdate = await _appDbContext.ResellerCompanyProfiles
                                .FirstOrDefaultAsync(x => x.Id == resellerProfile.Id);

                            resellerProfileToUpdate.Debited += totalAmount;
                            await _appDbContext.SaveChangesAsync();

                            await _resellerPortalRepository.AddResellerActivityLog(
                                new ResellerActivityLog
                                {
                                    CompanyId = request.CompanyId,
                                    CreatedByUserId = user.Id,
                                    Category = ResellerActivityLogCategory.Subscription,
                                    Action = $"Extend Subscription Plan: {toSubscriptionPlan.SubscriptionName}"
                                });

                            response.TotalAmount = totalAmount;
                            response.EffectiveDate = subscriptionExtendBillRecord.PeriodStart;
                            response.CurrentBalance = resellerProfileToUpdate.Balance;

                            await _companyInfoCacheService.RemoveCompanyInfoCache(request.CompanyId);

                            return Ok(response);
                        }

                        return BadRequest(
                            new ResponseViewModel()
                            {
                                message = "From Subscription Plan Id Not Found in Bill Records"
                            });
                    }

                    case ChangePlanType.Upgrade:
                    case ChangePlanType.Downgrade:
                    case ChangePlanType.Unsubscribe:
                    default:
                    {
                        return BadRequest(
                            new ResponseViewModel()
                            {
                                message = "Invalid Change Plan Type"
                            });
                    }
                }
            }

            return BadRequest();
        }

        /// <summary>
        /// Get Company's Existing AddOn Plan Number.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        [HttpPost]
        [Authorize(Roles = ApplicationUserRole.ResellerPortalUser)]
        public async Task<ActionResult<int>> GetCompanyExistingAddOnNumber([FromBody] AddOnPlanQuery request)
        {
            if (ModelState.IsValid && User.Identity.IsAuthenticated)
            {
                var user = await GetCurrentValidResellerUser(
                    new List<string>()
                    {
                        ApplicationUserRole.ResellerPortalUser
                    });

                if (user == null)
                {
                    return Unauthorized();
                }

                var resellerProfile = await _appDbContext.ResellerStaffs
                    .AsNoTracking()
                    .Include(x => x.ResellerCompanyProfile)
                    .ThenInclude(x => x.ClientCompanyProfiles)
                    .ThenInclude(x => x.ClientCompany)
                    .ThenInclude(x => x.BillRecords)
                    .ThenInclude(x => x.SubscriptionPlan)
                    .Where(x => x.IdentityId == user.Id)
                    .Select(x => x.ResellerCompanyProfile)
                    .FirstOrDefaultAsync();

                if (resellerProfile == null)
                {
                    return BadRequest(
                        new ResponseViewModel()
                        {
                            message = "Reseller Profile Not Found"
                        });
                }

                var clientCompany = resellerProfile.ClientCompanyProfiles
                    .Where(x => x.ClientCompanyId == request.CompanyId).Select(x => x.ClientCompany).FirstOrDefault();

                if (clientCompany == null)
                {
                    return BadRequest(
                        new ResponseViewModel()
                        {
                            message = "Client Company Not Found"
                        });
                }

                long count;

                switch (request.ChangeAddOnType)
                {
                    case ChangeAddOnType.Agent:
                        count = clientCompany.BillRecords
                            .Where(
                                x => ValidSubscriptionPlan.AgentPlan.Contains(x.SubscriptionPlanId)
                                     && x.CompanyId == request.CompanyId
                                     && x.PeriodStart < DateTime.UtcNow
                                     && x.PeriodEnd > DateTime.UtcNow)
                            .Sum(x => x.quantity);
                        break;
                    case ChangeAddOnType.AdditionalContact:
                        count = clientCompany.BillRecords
                            .Where(
                                x => ValidSubscriptionPlan.AdditionalContactAddOns.Contains(x.SubscriptionPlanId)
                                     && x.CompanyId == request.CompanyId
                                     && x.PeriodStart < DateTime.UtcNow
                                     && x.PeriodEnd > DateTime.UtcNow)
                            .Sum(x => x.SubscriptionPlan.MaximumContact * x.quantity);
                        break;
                    case ChangeAddOnType.WhatsAppPhoneNumber:
                        count = clientCompany.BillRecords
                            .Where(
                                x => ValidSubscriptionPlan.WhatsAppPhoneNumberAddOns.Contains(x.SubscriptionPlanId)
                                     && x.CompanyId == request.CompanyId
                                     && x.PeriodStart < DateTime.UtcNow
                                     && x.PeriodEnd > DateTime.UtcNow)
                            .Sum(x => x.quantity);
                        break;
                    default:
                        return BadRequest(
                            new ResponseViewModel()
                            {
                                message = "Invalid Add On Plan Type"
                            });
                }

                return Ok(count);
            }

            return BadRequest();
        }

        /// <summary>
        /// Request to change the addon plan before confirmation.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        [HttpPost]
        [Authorize(Roles = ApplicationUserRole.ResellerPortalUser)]
        public async Task<ActionResult<ChangeAddOnPlanResponse>> ChangeCompanyAddOn(
            [FromBody]
            ChangeCompanyAddOnRequest request)
        {
            if (ModelState.IsValid && User.Identity.IsAuthenticated)
            {
                var user = await GetCurrentValidResellerUser(
                    new List<string>()
                    {
                        ApplicationUserRole.ResellerPortalUser
                    });

                if (user == null)
                {
                    return Unauthorized();
                }

                var resellerProfile = await _appDbContext.ResellerStaffs
                    .AsNoTracking()
                    .Include(x => x.ResellerCompanyProfile)
                    .ThenInclude(x => x.ClientCompanyProfiles)
                    .ThenInclude(x => x.ClientCompany)
                    .ThenInclude(x => x.BillRecords)
                    .Where(x => x.IdentityId == user.Id).Select(x => x.ResellerCompanyProfile).FirstOrDefaultAsync();

                if (resellerProfile == null)
                {
                    return BadRequest(
                        new ResponseViewModel()
                        {
                            message = "Reseller Profile Not Found"
                        });
                }

                // Check if the reseller has a country tier and subscription plan config, if not then update it.
                resellerProfile =
                    await _resellerPortalRepository.CheckAndUpdateResellerSubscriptionPlanConfig(resellerProfile);

                var clientCompany = resellerProfile.ClientCompanyProfiles
                    .Where(x => x.ClientCompanyId == request.CompanyId)
                    .Select(x => x.ClientCompany)
                    .FirstOrDefault();

                if (clientCompany == null)
                {
                    return BadRequest(
                        new ResponseViewModel()
                        {
                            message = "Client Company Not Found"
                        });
                }

                if (!ValidSubscriptionPlan.CmsAllAddOn.Contains(request.AddOnPlanId))
                {
                    return BadRequest(
                        new ResponseViewModel()
                        {
                            message = "Invalid Add On Plan"
                        });
                }

                // Check current subscription plan
                var subscriptionBillRecord = clientCompany.BillRecords
                    .OrderByDescending(x => x.created)
                    .ThenByDescending(x => x.PayAmount)
                    .FirstOrDefault(
                        x => ValidSubscriptionPlan.SubscriptionPlan.Contains(x.SubscriptionPlanId)
                             && x.PeriodStart <= DateTime.UtcNow
                             && x.PeriodEnd > DateTime.UtcNow
                             && x.Status != BillStatus.Inactive
                             && x.Status != BillStatus.Terminated);

                if (subscriptionBillRecord == null)
                {
                    return BadRequest(
                        new ResponseViewModel()
                        {
                            message = "Subscription Plan Not Found"
                        });
                }

                var resellerProfileAddOnPlanId = _resellerPortalRepository.GetResellerSubscriptionPlanId(
                    subscriptionBillRecord.SubscriptionTier,
                    resellerProfile,
                    request.ChangeAddOnType);

                if (request.AddOnPlanId != resellerProfileAddOnPlanId)
                {
                    return BadRequest(
                        new ResponseViewModel()
                        {
                            message = "Add On Plan Id Not Same with Reseller Subscription Plan Config."
                        });
                }

                var addOnPlan =
                    await _appDbContext.CoreSubscriptionPlans
                        .FirstOrDefaultAsync(x => x.Id == request.AddOnPlanId);

                if (addOnPlan == null)
                {
                    return BadRequest(
                        new ResponseViewModel()
                        {
                            message = "Add-on Plan Not Found"
                        });
                }

                var response = new ChangeAddOnPlanResponse()
                {
                    CompanyId = request.CompanyId,
                    CompanyName = clientCompany.CompanyName,
                    AddOnPlanId = request.AddOnPlanId,
                    AddOnPlanPricing =
                        Math.Round(Convert.ToDecimal(addOnPlan.Amount) * (1 - resellerProfile.ResellerDiscount), 0),
                    ChangeAddOnPlan = request.ChangeAddOnPlan,
                    Currency = resellerProfile.Currency,
                    ChangeAddOnType = request.ChangeAddOnType,
                    CurrentBalance = resellerProfile.Balance
                };

                if (request.ChangeAddOnPlan == ChangeAddOnPlan.Add)
                {
                    var lastSubscriptionBillRecord = clientCompany.BillRecords
                        .OrderByDescending(x => x.PeriodEnd)
                        .ThenByDescending(x => x.Id)
                        .FirstOrDefault(
                            x => x.SubscriptionPlanId == subscriptionBillRecord.SubscriptionPlanId
                                 && x.PeriodEnd > DateTime.UtcNow
                                 && x.Status != BillStatus.Inactive
                                 && x.Status != BillStatus.Terminated);

                    if (lastSubscriptionBillRecord == null)
                    {
                        return BadRequest(
                            new ResponseViewModel()
                            {
                                message = "Subscription Plan Not Found"
                            });
                    }

                    var subscriptionPeriodEnd = lastSubscriptionBillRecord.PeriodEnd;

                    // Calculate the full months left from the end of the current subscription plan to now
                    var fullMonth = 0;
                    while (DateTime.UtcNow < subscriptionPeriodEnd.AddMonths(-1))
                    {
                        fullMonth++;
                        subscriptionPeriodEnd = subscriptionPeriodEnd.AddMonths(-1);
                    }

                    // Calculate the proportion of the remaining days in the current month of subscription
                    decimal proportion = 0;
                    if ((DateTime.UtcNow - subscriptionBillRecord.PeriodStart).TotalDays <= 1)
                    {
                        proportion = 1;
                    }
                    else if ((subscriptionPeriodEnd - DateTime.UtcNow).TotalDays > 1)
                    {
                        proportion =
                            Convert.ToDecimal(Math.Max((subscriptionPeriodEnd - DateTime.UtcNow).Days, 1)) /
                            Convert.ToDecimal((subscriptionPeriodEnd - subscriptionPeriodEnd.AddMonths(-1)).Days);
                    }

                    var periodOfAddOnInMonths = proportion + fullMonth;

                    var paymentAmount = Math.Round(
                        Math.Round(
                            Convert.ToDecimal(addOnPlan.Amount) *
                            (1 - resellerProfile.ResellerDiscount),
                            0) * periodOfAddOnInMonths,
                        0) * request.Quantity;

                    response.TotalAmount = paymentAmount;
                    response.Quantity = request.Quantity;
                    response.EffectiveDate = DateTime.UtcNow;
                    response.Period = periodOfAddOnInMonths;

                    return Ok(response);
                }
                else
                {
                    var existingAddOnBillRecords = await _appDbContext.CompanyBillRecords
                        .Where(
                            x => x.CompanyId == request.CompanyId
                                 && x.SubscriptionPlanId == request.AddOnPlanId
                                 && (x.Status == BillStatus.Active || x.Status == BillStatus.Renewed)
                                 && x.PeriodStart < DateTime.UtcNow
                                 && x.PeriodEnd > DateTime.UtcNow)
                        .OrderBy(x => x.PeriodEnd)
                        .ToListAsync();

                    if (!existingAddOnBillRecords.Any())
                    {
                        return BadRequest(
                            new ResponseViewModel()
                            {
                                message = "No anymore add on to cancel"
                            });
                    }

                    if (existingAddOnBillRecords.Count < request.Quantity)
                    {
                        return BadRequest(
                            new ResponseViewModel()
                            {
                                message = "Quantity to cancel exceeds the existing add on plan quantity"
                            });
                    }

                    response.Quantity = request.Quantity;
                    response.EffectiveDate = DateTime.UtcNow;

                    return Ok(response);
                }
            }

            return BadRequest();
        }

        /// <summary>
        /// Confirm change the addon plan.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        [HttpPost]
        [Authorize(Roles = ApplicationUserRole.ResellerPortalUser)]
        public async Task<ActionResult<ChangeAddOnPlanResponse>> ConfirmChangeCompanyAddOn(
            [FromBody]
            ChangeCompanyAddOnRequest request)
        {
            if (User.Identity.IsAuthenticated)
            {
                var user = await GetCurrentValidResellerUser(
                    new List<string>()
                    {
                        ApplicationUserRole.ResellerPortalUser
                    });

                if (user == null)
                {
                    return Unauthorized();
                }

                var resellerProfile = await _appDbContext.ResellerStaffs
                    .AsNoTracking()
                    .Include(x => x.ResellerCompanyProfile)
                    .ThenInclude(x => x.ClientCompanyProfiles)
                    .ThenInclude(x => x.ClientCompany)
                    .ThenInclude(x => x.BillRecords)
                    .Where(x => x.IdentityId == user.Id)
                    .Select(x => x.ResellerCompanyProfile)
                    .FirstOrDefaultAsync();

                if (resellerProfile == null)
                {
                    return BadRequest(
                        new ResponseViewModel()
                        {
                            message = "Reseller Profile Not Found"
                        });
                }

                // Check if the reseller has a country tier and subscription plan config, if not then update it.
                resellerProfile =
                    await _resellerPortalRepository.CheckAndUpdateResellerSubscriptionPlanConfig(resellerProfile);

                var clientCompany = resellerProfile.ClientCompanyProfiles
                    .Where(x => x.ClientCompanyId == request.CompanyId).Select(x => x.ClientCompany).FirstOrDefault();

                if (clientCompany == null)
                {
                    return BadRequest(
                        new ResponseViewModel()
                        {
                            message = "Client Company Not Found"
                        });
                }

                if (!ValidSubscriptionPlan.CmsAllAddOn.Contains(request.AddOnPlanId))
                {
                    return BadRequest(
                        new ResponseViewModel()
                        {
                            message = "Invalid Add On Plan"
                        });
                }

                // Check current subscription plan
                var subscriptionBillRecord = clientCompany.BillRecords
                    .OrderByDescending(x => x.created)
                    .ThenByDescending(x => x.PayAmount)
                    .FirstOrDefault(
                        x => ValidSubscriptionPlan.SubscriptionPlan.Contains(x.SubscriptionPlanId)
                             && x.PeriodStart <= DateTime.UtcNow
                             && x.PeriodEnd > DateTime.UtcNow
                             && x.Status != BillStatus.Inactive
                             && x.Status != BillStatus.Terminated);

                if (subscriptionBillRecord == null)
                {
                    return BadRequest(
                        new ResponseViewModel()
                        {
                            message = "Subscription Plan Not Found"
                        });
                }

                var resellerProfileAddOnPlanId = _resellerPortalRepository.GetResellerSubscriptionPlanId(
                    subscriptionBillRecord.SubscriptionTier,
                    resellerProfile,
                    request.ChangeAddOnType);

                if (request.AddOnPlanId != resellerProfileAddOnPlanId)
                {
                    return BadRequest(
                        new ResponseViewModel()
                        {
                            message = "Add On Plan Id Not Same with Reseller Subscription Plan Config."
                        });
                }

                var addOnPlan =
                    await _appDbContext.CoreSubscriptionPlans
                        .FirstOrDefaultAsync(x => x.Id == request.AddOnPlanId);

                if (addOnPlan == null)
                {
                    return BadRequest(
                        new ResponseViewModel()
                        {
                            message = "Add-on Plan Not Found"
                        });
                }

                var response = new ChangeAddOnPlanResponse()
                {
                    CompanyId = request.CompanyId,
                    CompanyName = clientCompany.CompanyName,
                    AddOnPlanId = request.AddOnPlanId,
                    AddOnPlanPricing = Convert.ToDecimal(
                        Math.Round(Convert.ToDecimal(addOnPlan.Amount) * (1 - resellerProfile.ResellerDiscount), 0)),
                    ChangeAddOnPlan = request.ChangeAddOnPlan,
                    Currency = resellerProfile.Currency,
                    ChangeAddOnType = request.ChangeAddOnType
                };

                if (request.ChangeAddOnPlan == ChangeAddOnPlan.Add)
                {
                    var lastSubscriptionBillRecord = clientCompany.BillRecords
                        .OrderByDescending(x => x.PeriodEnd)
                        .ThenByDescending(x => x.Id)
                        .FirstOrDefault(
                            x => x.SubscriptionPlanId == subscriptionBillRecord.SubscriptionPlanId
                                 && x.PeriodEnd > DateTime.UtcNow
                                 && x.Status != BillStatus.Inactive
                                 && x.Status != BillStatus.Terminated);

                    if (lastSubscriptionBillRecord == null)
                    {
                        return BadRequest(
                            new ResponseViewModel()
                            {
                                message = "Subscription Plan Not Found"
                            });
                    }

                    var subscriptionPeriodEnd = lastSubscriptionBillRecord.PeriodEnd;

                    // Calculate the full months left from the end of the current subscription plan to now
                    var fullMonth = 0;
                    while (DateTime.UtcNow < subscriptionPeriodEnd.AddMonths(-1))
                    {
                        fullMonth++;
                        subscriptionPeriodEnd = subscriptionPeriodEnd.AddMonths(-1);
                    }

                    // Calculate the proportion of the remaining days in the current month of subscription
                    decimal proportion = 0;
                    if ((DateTime.UtcNow - subscriptionBillRecord.PeriodStart).TotalDays <= 1)
                    {
                        proportion = 1;
                    }
                    else if ((subscriptionPeriodEnd - DateTime.UtcNow).TotalDays > 1)
                    {
                        proportion =
                            Convert.ToDecimal(Math.Max((subscriptionPeriodEnd - DateTime.UtcNow).Days, 1)) /
                            Convert.ToDecimal((subscriptionPeriodEnd - subscriptionPeriodEnd.AddMonths(-1)).Days);
                    }

                    var periodOfAddOnInMonths = proportion + fullMonth;

                    var totalAmount = Math.Round(
                        Math.Round(
                            Convert.ToDecimal(addOnPlan.Amount) *
                            (1 - resellerProfile.ResellerDiscount),
                            0) * periodOfAddOnInMonths,
                        0) * request.Quantity;

                    if (resellerProfile.Balance - totalAmount < resellerProfile.BalanceMinimumLimit)
                    {
                        return BadRequest(
                            new ResponseViewModel()
                            {
                                message =
                                    $"Insufficient Balance, You have reached the Minimum Balance Limit. " +
                                    $"Balance: {resellerProfile.Balance:0.00}, " +
                                    $"Minimum Balance Limit: {resellerProfile.BalanceMinimumLimit:0.00}."
                            });
                    }

                    var billRecord = new BillRecord()
                    {
                        CompanyId = request.CompanyId,
                        SubscriptionPlanId = request.AddOnPlanId,
                        PaidByReseller = true,
                        Status = BillStatus.Active,
                        SubscriptionTier = addOnPlan.SubscriptionTier,
                        PayAmount = Convert.ToDouble(totalAmount),
                        currency = resellerProfile.Currency,
                        PeriodStart = DateTime.UtcNow,
                        PeriodEnd = lastSubscriptionBillRecord.PeriodEnd,
                        PaymentStatus = PaymentStatus.Paid,
                        quantity = request.Quantity
                    };
                    await _appDbContext.CompanyBillRecords.AddAsync(billRecord);
                    await _appDbContext.SaveChangesAsync();

                    var addOnTransactionLog = new ResellerTransactionLog()
                    {
                        ResellerCompanyProfileId = resellerProfile.Id,
                        Amount = totalAmount,
                        Currency = resellerProfile.Currency,
                        BillRecordId = billRecord.Id,
                        ClientCompanyId = request.CompanyId,
                        TransactionMode = TransactionMode.Debit,
                        UserIdentityId = user?.Id,
                        TransactionCategory = ResellerTransactionCategory.AddOn,
                        TransactionAction = "Add " + _resellerPortalRepository.SetTransactionAction(addOnPlan),
                    };

                    await _appDbContext.ResellerTransactionLogs.AddAsync(addOnTransactionLog);

                    switch (request.ChangeAddOnType)
                    {
                        case ChangeAddOnType.AdditionalContact:
                            clientCompany.MaximumContacts += addOnPlan.MaximumContact * request.Quantity;
                            break;
                        case ChangeAddOnType.Agent:
                            clientCompany.MaximumAgents += request.Quantity;
                            break;
                        case ChangeAddOnType.WhatsAppPhoneNumber:
                            clientCompany.MaximumWhatsappInstance += request.Quantity;
                            break;
                    }

                    await _appDbContext.SaveChangesAsync();

                    var resellerProfileToUpdate = await _appDbContext.ResellerCompanyProfiles
                        .FirstOrDefaultAsync(x => x.Id == resellerProfile.Id);

                    resellerProfileToUpdate.Debited += totalAmount;

                    var clientCompanyToUpdate = await _appDbContext.CompanyCompanies
                        .FirstOrDefaultAsync(x => x.Id == clientCompany.Id);

                    clientCompanyToUpdate.MaximumContacts = clientCompany.MaximumContacts;
                    clientCompanyToUpdate.MaximumAgents = clientCompany.MaximumAgents;
                    clientCompanyToUpdate.MaximumWhatsappInstance = clientCompany.MaximumWhatsappInstance;

                    await _appDbContext.SaveChangesAsync();

                    await _resellerPortalRepository.AddResellerActivityLog(
                        new ResellerActivityLog
                        {
                            CompanyId = request.CompanyId,
                            CreatedByUserId = user.Id,
                            Category = ResellerActivityLogCategory.Subscription,
                            Action = $"Add On: {addOnPlan.SubscriptionName}"
                        });

                    response.CurrentBalance = resellerProfileToUpdate.Balance;
                    response.TotalAmount = totalAmount;
                    response.Quantity = request.Quantity;
                    response.EffectiveDate = DateTime.UtcNow;

                    response.CurrentAddOnAmount = request.ChangeAddOnType switch
                    {
                        ChangeAddOnType.AdditionalContact => clientCompanyToUpdate.MaximumContacts ?? 0,
                        ChangeAddOnType.Agent => clientCompanyToUpdate.MaximumAgents,
                        ChangeAddOnType.WhatsAppPhoneNumber => clientCompanyToUpdate.MaximumWhatsappInstance,
                        _ => response.CurrentAddOnAmount
                    };

                    BackgroundJob.Enqueue<IInternalHubSpotService>(x => x.SyncCompany(request.CompanyId, null));

                    await _companyInfoCacheService.RemoveCompanyInfoCache(request.CompanyId);

                    return Ok(response);
                }
                else
                {
                    var existingAddOnBillRecords = await _appDbContext.CompanyBillRecords
                        .Where(
                            x => x.CompanyId == request.CompanyId
                                 && x.SubscriptionPlanId == request.AddOnPlanId
                                 && (x.Status == BillStatus.Active || x.Status == BillStatus.Renewed)
                                 && x.PeriodEnd > DateTime.UtcNow)
                        .OrderBy(x => x.PeriodEnd)
                        .ToListAsync();

                    if (!existingAddOnBillRecords.Any())
                    {
                        return BadRequest(
                            new ResponseViewModel()
                            {
                                message = "No anymore add on to cancel"
                            });
                    }

                    var currentAddOnBillRecords = existingAddOnBillRecords
                        .Where(x => x.PeriodStart <= DateTime.UtcNow)
                        .ToList();

                    if (currentAddOnBillRecords.Count < request.Quantity)
                    {
                        return BadRequest(
                            new ResponseViewModel()
                            {
                                message = "Quantity to cancel exceeds the existing add on plan quantity"
                            });
                    }

                    var count = request.Quantity;
                    foreach (var record in currentAddOnBillRecords)
                    {
                        if (record.quantity <= count)
                        {
                            record.UpdatedAt = DateTime.UtcNow;
                            record.Status = BillStatus.Canceled;

                            var extendBillRecord = existingAddOnBillRecords
                                .FirstOrDefault(x => x.ExtendFromBillRecordId == record.Id);

                            while (extendBillRecord != null)
                            {
                                extendBillRecord.Status = BillStatus.Canceled;

                                var followingBillRecord = existingAddOnBillRecords
                                    .FirstOrDefault(x => x.ExtendFromBillRecordId == extendBillRecord.Id);

                                if (followingBillRecord != null)
                                {
                                    extendBillRecord = followingBillRecord;
                                }
                                else
                                {
                                    extendBillRecord = null;
                                }
                            }

                            switch (request.ChangeAddOnType)
                            {
                                case ChangeAddOnType.AdditionalContact:
                                    clientCompany.MaximumContacts -=
                                        addOnPlan.MaximumContact * Convert.ToInt32(record.quantity);
                                    break;
                                case ChangeAddOnType.Agent:
                                    clientCompany.MaximumAgents -= Convert.ToInt32(record.quantity);
                                    break;
                                case ChangeAddOnType.WhatsAppPhoneNumber:
                                    clientCompany.MaximumWhatsappInstance -= Convert.ToInt32(record.quantity);
                                    break;
                            }

                            await _appDbContext.SaveChangesAsync();
                            count -= Convert.ToInt32(record.quantity);

                            if (count == 0)
                            {
                                break;
                            }
                        }
                        else
                        {
                            record.UpdatedAt = DateTime.UtcNow;
                            record.quantity -= count;

                            var extendBillRecord = existingAddOnBillRecords
                                .FirstOrDefault(x => x.ExtendFromBillRecordId == record.Id);

                            while (extendBillRecord != null)
                            {
                                extendBillRecord.quantity -= count;

                                var followingBillRecord = existingAddOnBillRecords
                                    .FirstOrDefault(x => x.ExtendFromBillRecordId == extendBillRecord.Id);

                                if (followingBillRecord != null)
                                {
                                    extendBillRecord = followingBillRecord;
                                }
                                else
                                {
                                    extendBillRecord = null;
                                }
                            }

                            switch (request.ChangeAddOnType)
                            {
                                case ChangeAddOnType.AdditionalContact:
                                    clientCompany.MaximumContacts -= addOnPlan.MaximumContact * count;
                                    break;
                                case ChangeAddOnType.Agent:
                                    clientCompany.MaximumAgents -= count;
                                    break;
                                case ChangeAddOnType.WhatsAppPhoneNumber:
                                    clientCompany.MaximumWhatsappInstance -= count;
                                    break;
                            }

                            await _appDbContext.SaveChangesAsync();
                            break;
                        }
                    }

                    var clientCompanyToUpdate = await _appDbContext.CompanyCompanies
                        .FirstOrDefaultAsync(x => x.Id == clientCompany.Id);

                    clientCompanyToUpdate.MaximumContacts = clientCompany.MaximumContacts;
                    clientCompanyToUpdate.MaximumAgents = clientCompany.MaximumAgents;
                    clientCompanyToUpdate.MaximumWhatsappInstance = clientCompany.MaximumWhatsappInstance;

                    await _appDbContext.SaveChangesAsync();

                    await _resellerPortalRepository.AddResellerActivityLog(
                        new ResellerActivityLog
                        {
                            CompanyId = request.CompanyId,
                            CreatedByUserId = user.Id,
                            Category = ResellerActivityLogCategory.Subscription,
                            Action = $"Add On: {addOnPlan.SubscriptionName}"
                        });

                    response.CurrentBalance = resellerProfile.Balance;
                    response.Quantity = request.Quantity;
                    response.EffectiveDate = DateTime.UtcNow;

                    response.CurrentAddOnAmount = request.ChangeAddOnType switch
                    {
                        ChangeAddOnType.AdditionalContact => clientCompanyToUpdate.MaximumContacts ?? 0,
                        ChangeAddOnType.Agent => clientCompanyToUpdate.MaximumAgents,
                        ChangeAddOnType.WhatsAppPhoneNumber => clientCompanyToUpdate.MaximumWhatsappInstance,
                        _ => response.CurrentAddOnAmount
                    };

                    BackgroundJob.Enqueue<IInternalHubSpotService>(x => x.SyncCompany(request.CompanyId, null));

                    await _companyInfoCacheService.RemoveCompanyInfoCache(request.CompanyId);

                    return Ok(response);
                }
            }

            return BadRequest();
        }

        /// <summary>
        /// Get Business Balances of the reseller client company.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        [HttpPost]
        public async Task<ActionResult<GetWhatsappCloudApiUsageRecordsResponse>> GetWhatsAppCloudApiUsageRecords(
            [FromBody]
            GetWhatsAppCloudApiUsageRecordsRequest request)
        {
            var user = await GetCurrentValidResellerUser(
                new List<string>()
                {
                    ApplicationUserRole.ResellerPortalUser
                });

            if (user == null)
            {
                return Unauthorized();
            }

            var response = await _whatsappCloudApiService.GetBusinessBalances(request.ClientCompanyId);

            var result = new GetWhatsappCloudApiUsageRecordsResponse
            {
                WhatsappCloudApiUsageRecords = response.BusinessBalances
            };

            return Ok(result);
        }

        [HttpPost]
        public async Task<ActionResult<GetFilteredManagementWhatsappCloudApiBusinessBalanceTransactionLogsOutput>>
            GetFilteredManagementWhatsappCloudApiBusinessBalanceTransactionLogs(
                [FromBody]
                GetFilteredManagementWhatsappCloudApiBusinessBalanceTransactionLogsRequest request)
        {
            var user = await GetCurrentValidResellerUser(
                new List<string>()
                {
                    ApplicationUserRole.ResellerPortalUser
                });

            if (user == null)
            {
                return Unauthorized();
            }

            return Ok(
                await _internalWhatsappCloudApiService
                    .GetFilteredManagementWhatsappCloudApiBusinessBalanceTransactionLogs(
                        request.BusinessBalanceTransactionLogFilter,
                        request.Limit,
                        request.ContinuationToken));
        }

        [HttpPost]
        public async Task<ActionResult<GetWhatsappCloudApiConversationUsageAnalyticOutput>>
            GetWhatsappCloudApiConversationUsageAnalytic(
                [FromBody]
                GetWhatsappCloudApiConversationUsageRequest request)
        {
            var user = await GetCurrentValidResellerUser(
                new List<string>()
                {
                    ApplicationUserRole.ResellerPortalUser
                });

            if (user == null)
            {
                return Unauthorized();
            }

            var resellerProfile = await _appDbContext.ResellerStaffs
                .Include(x => x.ResellerCompanyProfile)
                .ThenInclude(x => x.ClientCompanyProfiles)
                .ThenInclude(x => x.ClientCompany)
                .Where(x => x.IdentityId == user.Id)
                .Select(x => x.ResellerCompanyProfile)
                .FirstOrDefaultAsync();

            if (resellerProfile == null)
            {
                return BadRequest(
                    new ResponseViewModel()
                    {
                        message = "Reseller Profile Not Found"
                    });
            }

            var clientCompany = resellerProfile.ClientCompanyProfiles
                .Where(x => x.ClientCompanyId == request.ClientCompanyId)
                .Select(x => x.ClientCompany)
                .FirstOrDefault();

            if (clientCompany == null)
            {
                return BadRequest(
                    new ResponseViewModel()
                    {
                        message = "Client Company Not Found"
                    });
            }

            var result = await _whatsappCloudApiService.GetConversationUsageByFacebookWabaIdAsync(
                clientCompany.Id,
                request.FacebookBusinessId,
                request.FacebookWabaId,
                request.Start,
                request.End);

            return Ok(result);
        }

        [HttpPost]
        public async Task<ActionResult<CreateWhatsappCloudApiTransactionLogResponse>>
            CreateWhatsappCloudApiTransactionLog(
                [FromBody]
                CreateWhatsappCloudApiTransactionLogRequest request)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        message = "Invalid Form"
                    });
            }

            var user = await GetCurrentValidResellerUser(
                new List<string>()
                {
                    ApplicationUserRole.ResellerPortalUser
                });

            if (user == null)
            {
                return Unauthorized();
            }

            if (request.AddCreditValue <= 0)
            {
                return BadRequest(
                    new ResponseViewModel()
                    {
                        message = "Invalid Add Credit Value"
                    });
            }

            Money credit = new Money("USD", request.AddCreditValue);

            var uniqueId = string.Join(
                "/",
                request.FacebookBusinessId,
                user.Id,
                DateTimeOffset.Now.ToUnixTimeSeconds());

            var response = await _whatsappCloudApiService.TopUp(
                request.CompanyId,
                uniqueId,
                request.FacebookBusinessId,
                WhatsappCloudApiTopUpPaymentMethods.Internal,
                credit,
                user.Id,
                user.DisplayName,
                resellerTransactionLogDetail: request.BillingHistoryDetail,
                metadata: request.Metadata);

            if (!response.Success)
            {
                return BadRequest(
                    new ResponseViewModel()
                    {
                        message = response.Message
                    });
            }

            var result = new CreateWhatsappCloudApiTransactionLogResponse
            {
                WhatsappCloudApiBusinessBalanceTransactionLog = response.Data.WabaBalanceTransactionLog
            };

            return Ok(result);
        }
    }
}