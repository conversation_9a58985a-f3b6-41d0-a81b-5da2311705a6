using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Travis_backend.Constants;
using Travis_backend.ContactDomain.Services;
using Travis_backend.ConversationDomain.Models;
using Travis_backend.ConversationServices.ViewModels;
using Travis_backend.Database;
using Travis_backend.FileDomain.Services;
using Travis_backend.MessageDomain.Models;
using Travis_backend.OpenTelemetry.Meters;
using Travis_backend.OpenTelemetry.Meters.Constants;

namespace Travis_backend.MessageDomain.ChannelMessageProvider.ChannelMessageHandlers;

public class FacebookChannelMessageHandler : IChannelMessageHandler
{
    private readonly IUserProfileService _userProfileService;
    private readonly ApplicationDbContext _appDbContext;
    private readonly IConfiguration _configuration;
    private readonly ILogger<FacebookChannelMessageHandler> _logger;
    private readonly IMediaProcessService _mediaProcessService;
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly IConversationMeters _conversationMeters;

    public string ChannelType => ChannelTypes.Facebook;

    public FacebookChannelMessageHandler(
        IUserProfileService userProfileService,
        ApplicationDbContext appDbContext,
        IConfiguration configuration,
        ILogger<FacebookChannelMessageHandler> logger,
        IMediaProcessService mediaProcessService,
        IHttpClientFactory httpClientFactory,
        IConversationMeters conversationMeters)
    {
        _userProfileService = userProfileService;
        _appDbContext = appDbContext;
        _configuration = configuration;
        _logger = logger;
        _mediaProcessService = mediaProcessService;
        _httpClientFactory = httpClientFactory;
        _conversationMeters = conversationMeters;
    }

    public async ValueTask<ConversationMessage> SendChannelMessageAsync(
        Conversation conversation,
        ConversationMessage conversationMessage)
    {
        if (conversationMessage.facebookReceiver != null &&
            conversationMessage.facebookReceiver?.pageId != conversationMessage.facebookReceiver?.FacebookId &&
            string.IsNullOrEmpty(conversationMessage.MessageUniqueID))
        {
            var facebookConfig = await _appDbContext.ConfigFacebookConfigs.FirstOrDefaultAsync(
                x => x.CompanyId == conversation.CompanyId &&
                     x.PageId == conversationMessage.facebookReceiver.pageId);

            if (facebookConfig == null)
            {
                throw new Exception(
                    $"No FacebookConfig found CompanyId: {conversation.CompanyId} pageId: {conversationMessage.facebookReceiver.pageId}");
            }

            var messengerAccessToken = facebookConfig.PageAccessToken;
            var httpClient = _httpClientFactory.CreateClient(HttpClientHandlerName.Default);

            try
            {
                if (conversationMessage.ExtendedMessagePayload != null)
                {
                    var sendFBMessage = new SendFacebookMessage()
                    {
                        recipient = new Recipient()
                    };

                    switch (conversationMessage.ExtendedMessagePayload.ExtendedMessageType)
                    {
                        case ExtendedMessageType.FacebookOTNRequest:
                            sendFBMessage.recipient.id = conversationMessage.facebookReceiver.FacebookId;

                            sendFBMessage.message = conversationMessage.ExtendedMessagePayload
                                .ExtendedMessagePayloadDetail.FacebookMessagePayload;
                            sendFBMessage.messaging_type = "MESSAGE_TAG";

                            sendFBMessage.tag = !string.IsNullOrEmpty(conversationMessage.MessageTag)
                                ? conversationMessage.MessageTag
                                : "HUMAN_AGENT";

                            var sendMessageResponse = await httpClient.PostAsJsonAsync(
                                $"https://graph.facebook.com/me/messages?access_token={messengerAccessToken}",
                                sendFBMessage);
                            var sendMessageString = await sendMessageResponse.Content.ReadAsStringAsync();
                            var sendMessage = JsonConvert.DeserializeObject<SendMessageResponse>(sendMessageString);

                            conversationMessage.MessageUniqueID = sendMessage?.message_id;

                            if (sendMessage == null)
                            {
                                conversationMessage.Status = MessageStatus.Failed;
                            }
                            else if (sendMessage.message_id != null)
                            {
                                conversationMessage.Status = MessageStatus.Sent;
                            }
                            else
                            {
                                conversationMessage.Status = MessageStatus.Failed;
                                if (sendMessage.error is { error_subcode: not null })
                                {
                                    if (sendMessage.error.error_subcode is 2018310)
                                    {
                                        conversationMessage.ChannelStatusMessage =
                                            "2018310-You need one time notification permission for this message.";
                                    }
                                    else if (sendMessage.error.error_subcode is 2018278)
                                    {
                                        conversationMessage.ChannelStatusMessage =
                                            "2018278-This message is sent outside of allowed window.";
                                    }
                                else if (sendMessage.error.error_subcode.HasValue)
                                {
                                    conversationMessage.ChannelStatusMessage =
                                        $"{sendMessage.error.error_subcode}-{sendMessage.error.message}";
                                }
                                else
                                {
                                    conversationMessage.ChannelStatusMessage = sendMessage.error.message;
                                }

                                conversationMessage.Metadata ??= new Dictionary<string, object>();
                                conversationMessage.Metadata.Add("error", new List<ConversationMessageError>()
                                {
                                    new ()
                                    {
                                        Code = sendMessage.error.error_subcode.ToString(),
                                        Message = sendMessage.error.message,
                                        InnerError = sendMessage.error
                                    }
                                });

                                    _logger.LogError(
                                        "[{MethodName}] PageId {FacebookPageId} Sending OTN Request to FacebookUserId {FacebookUserId} Error: {ResponseBody}",
                                        nameof(SendChannelMessageAsync),
                                        conversationMessage.facebookReceiver?.pageId,
                                        conversationMessage.facebookReceiver?.FacebookId,
                                        sendMessageString);
                                }
                            }

                            await _appDbContext.SaveChangesAsync();

                            break;

                        case ExtendedMessageType.FacebookOTNText:
                        case ExtendedMessageType.FacebookOTNFile:
                        case ExtendedMessageType.FacebookOTNInteractive:
                            var fbUserOneTimeToken = await _appDbContext.FacebookUserOneTimeTokens
                                .OrderBy(x => x.ExpiryDate)
                                .FirstOrDefaultAsync(
                                    x =>
                                        x.FacebookOTNTopicId == conversationMessage.ExtendedMessagePayload
                                            .FacebookOTNTopicId
                                        && x.FacebookUserId == conversationMessage.facebookReceiverId
                                        && x.IsTokenRedeemed
                                        && x.ExpiryDate > DateTime.UtcNow);

                            if (fbUserOneTimeToken == null)
                            {
                                conversationMessage.Status = MessageStatus.Failed;
                                conversationMessage.ChannelStatusMessage = "404-Unable to find the token.";

                                _logger.LogError(
                                    "[{MethodName}] Conversation {ConversationId} cannot find OTN token for FacebookUserId {FacebookUserId} on topicId {FacebookOtnTopicId}",
                                    nameof(SendChannelMessageAsync),
                                    conversation.Id,
                                    conversationMessage.facebookReceiverId,
                                    conversationMessage.ExtendedMessagePayload.FacebookOTNTopicId);

                                break;
                            }

                            sendFBMessage.message = conversationMessage.ExtendedMessagePayload
                                .ExtendedMessagePayloadDetail.FacebookMessagePayload;
                            sendFBMessage.recipient.one_time_notif_token = fbUserOneTimeToken.OneTimeToken;

                            var sendOtnMessageResponse = await httpClient.PostAsJsonAsync(
                                $"https://graph.facebook.com/me/messages?access_token={messengerAccessToken}",
                                sendFBMessage);
                            var sendOtnMessageString = await sendOtnMessageResponse.Content.ReadAsStringAsync();

                            var sendOtnMessage =
                                JsonConvert.DeserializeObject<SendMessageResponse>(sendOtnMessageString);

                            conversationMessage.MessageUniqueID = sendOtnMessage?.message_id;

                            if (sendOtnMessage == null)
                            {
                                conversationMessage.Status = MessageStatus.Failed;
                            }
                            else if (sendOtnMessage.message_id != null)
                            {
                                conversationMessage.Status = MessageStatus.Sent;
                            }
                            else
                            {
                                conversationMessage.Status = MessageStatus.Failed;
                                if (sendOtnMessage.error is { error_subcode: not null })
                                {
                                    if (sendOtnMessage.error.error_subcode is 2018310)
                                    {
                                        conversationMessage.ChannelStatusMessage =
                                            "2018310-You need one time notification permission for this message.";
                                    }
                                else if (sendOtnMessage.error.error_subcode.HasValue)
                                {
                                    conversationMessage.ChannelStatusMessage =
                                        $"{sendOtnMessage.error.error_subcode}-{sendOtnMessage.error.message}";
                                }
                                else
                                {
                                    conversationMessage.ChannelStatusMessage = sendOtnMessage.error.message;
                                }

                                conversationMessage.Metadata ??= new Dictionary<string, object>();
                                conversationMessage.Metadata.Add("error", new List<ConversationMessageError>()
                                {
                                    new ()
                                    {
                                        Code = sendOtnMessage.error.error_subcode.ToString(),
                                        Message = sendOtnMessage.error.message,
                                        InnerError = sendOtnMessage.error
                                    }
                                });

                                    _logger.LogError(
                                        "PageId {FacebookPageId} Sending OTN Request to FacebookUserId {FacebookUserId} Error: {ResponseBody}",
                                        conversationMessage.facebookReceiver.pageId,
                                        conversationMessage.facebookReceiver.FacebookId,
                                        sendOtnMessageString);
                                }
                            }

                            _appDbContext.FacebookUserOneTimeTokens.Remove(fbUserOneTimeToken);
                            await _appDbContext.SaveChangesAsync();

                            break;
                        case ExtendedMessageType.FacebookInteractiveMessage:
                            sendFBMessage.recipient.id = conversationMessage.facebookReceiver.FacebookId;

                            sendFBMessage.message = conversationMessage.ExtendedMessagePayload
                                .ExtendedMessagePayloadDetail.FacebookMessagePayload;
                            sendFBMessage.messaging_type = "MESSAGE_TAG";

                            sendFBMessage.tag = !string.IsNullOrEmpty(conversationMessage.MessageTag)
                                ? conversationMessage.MessageTag
                                : "HUMAN_AGENT";

                            var sendInteractiveMessageResponse = await httpClient.PostAsJsonAsync(
                                $"https://graph.facebook.com/me/messages?access_token={messengerAccessToken}",
                                sendFBMessage);

                            var sendInteractiveMessageString =
                                await sendInteractiveMessageResponse.Content.ReadAsStringAsync();

                            var sendInteractiveMessage =
                                JsonConvert.DeserializeObject<SendMessageResponse>(sendInteractiveMessageString);

                            conversationMessage.MessageUniqueID = sendInteractiveMessage?.message_id;

                            if (sendInteractiveMessage == null)
                            {
                                conversationMessage.Status = MessageStatus.Failed;
                            }
                            else if (sendInteractiveMessage.message_id != null)
                            {
                                conversationMessage.Status = MessageStatus.Sent;
                            }
                            else
                            {
                                conversationMessage.Status = MessageStatus.Failed;
                                if (sendInteractiveMessage.error is { error_subcode: not null })
                                {
                                    if (sendInteractiveMessage.error.error_subcode is 2018310)
                                    {
                                        conversationMessage.ChannelStatusMessage =
                                            "2018310-You need one time notification permission for this message.";
                                    }
                                    else if (sendInteractiveMessage.error.error_subcode is 2018278)
                                    {
                                        conversationMessage.ChannelStatusMessage =
                                            "2018278-This message is sent outside of allowed window.";
                                    }
                                else if (sendInteractiveMessage.error.error_subcode.HasValue)
                                {
                                    conversationMessage.ChannelStatusMessage =
                                        $"{sendInteractiveMessage.error.error_subcode}-{sendInteractiveMessage.error.message}";
                                }
                                else
                                {
                                    conversationMessage.ChannelStatusMessage = sendInteractiveMessage.error.message;
                                }

                                conversationMessage.Metadata ??= new Dictionary<string, object>();
                                conversationMessage.Metadata.Add("error", new List<ConversationMessageError>()
                                {
                                    new ()
                                    {
                                        Code = sendInteractiveMessage.error.error_subcode.ToString(),
                                        Message = sendInteractiveMessage.error.message,
                                        InnerError = sendInteractiveMessage.error
                                    }
                                });

                                    _logger.LogError(
                                        "PageId {FacebookPageId} Sending Interactive Message to FacebookUserId {FacebookUserId} Error: {ResponseBody}",
                                        conversationMessage.facebookReceiver.pageId,
                                        conversationMessage.facebookReceiver.FacebookId,
                                        sendInteractiveMessageString);
                                }
                            }

                            await _appDbContext.SaveChangesAsync();

                            break;
                    }
                }
                else
                {
                    var sendFBMessage = new SendFacebookMessage()
                    {
                        recipient = new Recipient()
                        {
                            id = conversationMessage.facebookReceiver.FacebookId
                        },
                        messaging_type = "MESSAGE_TAG",
                        tag = !string.IsNullOrEmpty(conversationMessage.MessageTag)
                            ? conversationMessage.MessageTag
                            : "HUMAN_AGENT"
                    };

                    switch (conversationMessage.MessageType)
                    {
                        case "text":
                            string messageContent = string.Empty;

                            if (conversationMessage.TranslationResults != null)
                            {
                                messageContent = conversationMessage.TranslationResults
                                    .FirstOrDefault().translations
                                    .FirstOrDefault().text;
                            }
                            else
                            {
                                messageContent = conversationMessage.MessageContent;
                            }

                            sendFBMessage.message = new MessageData()
                            {
                                text = messageContent
                            };

                            var sendMessageResponse = await httpClient.PostAsJsonAsync(
                                $"https://graph.facebook.com/me/messages?access_token={messengerAccessToken}",
                                sendFBMessage);
                            var sendMessageString = await sendMessageResponse.Content.ReadAsStringAsync();
                            var sendMessage = JsonConvert.DeserializeObject<SendMessageResponse>(sendMessageString);

                            conversationMessage.MessageUniqueID = sendMessage?.message_id;

                            if (sendMessage == null)
                            {
                                conversationMessage.Status = MessageStatus.Failed;
                            }
                            else if (sendMessage.message_id != null)
                            {
                                conversationMessage.Status = MessageStatus.Sent;
                            }
                            else
                            {
                                conversationMessage.Status = MessageStatus.Failed;
                                if (sendMessage.error is { error_subcode: not null })
                                {
                                    if (sendMessage.error.error_subcode is 2018278)
                                    {
                                        conversationMessage.ChannelStatusMessage =
                                            "2018278-This message is sent outside of allowed window.";
                                    }
                                else if (sendMessage.error.error_subcode.HasValue)
                                {
                                    conversationMessage.ChannelStatusMessage =
                                        $"{sendMessage.error.error_subcode}-{sendMessage.error.message}";
                                }
                                else
                                {
                                    conversationMessage.ChannelStatusMessage = sendMessage.error.message;
                                }

                                conversationMessage.Metadata ??= new Dictionary<string, object>();
                                conversationMessage.Metadata.Add("error", new List<ConversationMessageError>()
                                {
                                    new ()
                                    {
                                        Code = sendMessage.error.error_subcode.ToString(),
                                        Message = sendMessage.error.message,
                                        InnerError = sendMessage.error
                                    }
                                });

                                    _logger.LogError(
                                        "PageId {FacebookPageId} Sending text message to FacebookUserId {FacebookUserId} Error: {ResponseBody}",
                                        conversationMessage.facebookReceiver.pageId,
                                        conversationMessage.facebookReceiver.FacebookId,
                                        sendMessageString);
                                }
                            }

                            await _appDbContext.SaveChangesAsync();

                            break;
                        case "file":
                            foreach (var uploadedFile in conversationMessage.UploadedFiles)
                            {
                                if ((uploadedFile.MIMEType.Contains("audio") &&
                                     !uploadedFile.MIMEType.Contains("mp3")) ||
                                    Path.GetExtension(uploadedFile.Filename) == ".webm" ||
                                    Path.GetExtension(uploadedFile.Filename) == ".bin")
                                {
                                    await _mediaProcessService.ProcessMedia(conversation.Id, uploadedFile, "mp3");
                                }

                                string messageType = "file";

                                switch (uploadedFile.MIMEType)
                                {
                                    case "image/png":
                                    case "image/jpeg":
                                    case "image/gif":
                                        messageType = "image";

                                        break;
                                    case "audio/wav":
                                    case "audio/mpeg":
                                        messageType = "audio";

                                        break;
                                    default:
                                        messageType = "file";

                                        break;
                                }

                                var filename = Path.GetFileName(uploadedFile.Filename);

                                filename = filename.Replace(" ", string.Empty).Replace("(", string.Empty)
                                    .Replace(")", string.Empty)
                                    .Replace("[", string.Empty).Replace("]", string.Empty);

                                var domainName = _configuration.GetValue<String>("Values:DomainName");
                                var sendFBAttachementMessage = sendFBMessage;

                                sendFBAttachementMessage.message = new MessageData()
                                {
                                    attachment = new FBAttachment
                                    {
                                        payload = new AttachmentPayload
                                        {
                                            url =
                                                $"{domainName}/Message/File/Private/{uploadedFile.FileId}/{filename}",
                                            is_reusable = true
                                        },
                                        type = messageType
                                    }
                                };

                                try
                                {
                                    var sendAttResponse = await httpClient.PostAsJsonAsync(
                                        $"https://graph.facebook.com/me/messages?access_token={messengerAccessToken}",
                                        sendFBAttachementMessage);
                                    var sendAttString = await sendAttResponse.Content.ReadAsStringAsync();
                                    var sendAtt = JsonConvert.DeserializeObject<SendMessageResponse>(sendAttString);

                                    if (sendAtt == null)
                                    {
                                        conversationMessage.Status = MessageStatus.Failed;
                                    }
                                    else if (sendAtt.message_id != null)
                                    {
                                        conversationMessage.Status = MessageStatus.Sent;
                                    }
                                    else
                                    {
                                        conversationMessage.Status = MessageStatus.Failed;

                                        if (sendAtt?.error != null)
                                        {
                                            if (sendAtt.error.error_subcode is 2018278)
                                            {
                                                conversationMessage.ChannelStatusMessage =
                                                    "2018278-This message is sent outside of allowed window.";
                                            }
                                        else if (sendAtt.error.error_subcode.HasValue)
                                        {
                                            conversationMessage.ChannelStatusMessage =
                                                $"{sendAtt.error.error_subcode}-{sendAtt.error.message}";
                                        }
                                        else
                                        {
                                            conversationMessage.ChannelStatusMessage = sendAtt.error.message;
                                        }

                                        conversationMessage.Metadata ??= new Dictionary<string, object>();
                                        conversationMessage.Metadata.Add("error", new List<ConversationMessageError>()
                                        {
                                            new ()
                                            {
                                                Code = sendAtt.error.error_subcode.ToString(),
                                                Message = sendAtt.error.message,
                                                InnerError = sendAtt.error
                                            }
                                        });


                                            _logger.LogError(
                                                "PageId {FacebookPageId} Sending file message to FacebookUserId {FacebookUserId} Error: {ResponseBody}",
                                                conversationMessage.facebookReceiver.pageId,
                                                conversationMessage.facebookReceiver.FacebookId,
                                                sendAttString);
                                        }
                                    }

                                    if (conversationMessage.MessageContent == null)
                                    {
                                        conversationMessage.MessageUniqueID = sendAtt?.message_id;
                                    }

                                    await _appDbContext.SaveChangesAsync();
                                }
                                catch (Exception ex)
                                {
                                    _logger.LogError(
                                        ex,
                                        "Send Facebook file message error for conversation {ConversationId}: {ExceptionString}",
                                        conversation?.Id,
                                        ex.ToString());

                                    conversationMessage.Status = MessageStatus.Failed;
                                    conversationMessage.ChannelStatusMessage += $";Error: {ex.ToString()}";
                                }
                            }

                            break;
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Send Facebook message error for conversation {ConversationId}: {ExceptionString}",
                    conversation?.Id,
                    ex.ToString());
                conversationMessage.Status = MessageStatus.Failed;
                conversationMessage.ChannelStatusMessage += $";Error: {ex.ToString()}";
            }

            await FetchFacebookUserProfile(
                facebookConfig.CompanyId,
                conversationMessage.facebookReceiver,
                facebookConfig.PageId,
                facebookConfig.PageAccessToken);

            if (!string.IsNullOrEmpty(conversationMessage.MessageUniqueID))
            {
                conversationMessage.Status = MessageStatus.Sent;

                _conversationMeters.IncrementCounter(ChannelTypes.Facebook, ConversationMeterOptions.SendSuccess);
            }
            else
            {
                conversationMessage.Status = MessageStatus.Failed;

                _conversationMeters.IncrementCounter(ChannelTypes.Facebook, ConversationMeterOptions.SendFailed);
            }

            await _appDbContext.SaveChangesAsync();

            return conversationMessage;
        }

        return conversationMessage;
    }

    private async Task FetchFacebookUserProfile(
        string companyId,
        FacebookSender fbSender,
        string pageId,
        string token)
    {
        try
        {
            if (string.IsNullOrEmpty(fbSender.profile_pic) ||
                fbSender.profile_pic.Contains("https://platform-lookaside.fbsbx.com"))
            {
                var httpClient = _httpClientFactory.CreateClient(HttpClientHandlerName.Default);

                var profileDataResponse = await httpClient.GetStringAsync(
                    $"https://graph.facebook.com/{fbSender.FacebookId}?access_token={token}&fields=profile_pic,gender,locale,first_name,last_name");
                var profileData = JsonConvert.DeserializeObject<FacebookUserProfile>(profileDataResponse);

                var newuUploadedFile = await _userProfileService.UploadSocialProfilePicture(
                    companyId,
                    ChannelTypes.Facebook,
                    fbSender.FacebookId,
                    profileData.profile_pic);

                if (newuUploadedFile == null)
                {
                    _logger.LogError("line pic upload fail");
                }
                else
                {
                    _appDbContext.UserProfilePictureFiles.Add(newuUploadedFile);

                    var domainName = _configuration.GetValue<String>("Values:DomainName");
                    fbSender.profile_pic = $"{domainName}/userprofile/picture/{newuUploadedFile.ProfilePictureId}";
                }

                fbSender.first_name = profileData.first_name;
                fbSender.last_name = profileData.last_name;
                fbSender.locale = profileData.locale;
                fbSender.gender = profileData.gender;
                fbSender.pageId = pageId;
                await _appDbContext.SaveChangesAsync();
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "Fetch Facebook Profile Picture error for {SenderFacebookId}: {ExceptionMessage}",
                fbSender.FacebookId,
                ex.Message);
        }
    }
}