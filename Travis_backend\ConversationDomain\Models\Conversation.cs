﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Travis_backend.AccountAuthenticationDomain.Models;
using Travis_backend.CommonDomain.Models;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.ContactDomain.Models;
using Travis_backend.Enums;
using Travis_backend.MessageDomain.Models;

namespace Travis_backend.ConversationDomain.Models
{
    public class Conversation
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();

        public string CompanyId { get; set; }

        // public Company Company { get; set; }
        public string MessageGroupName { get; set; }

        public string UserProfileId { get; set; }

        public UserProfile UserProfile { get; set; }

        public Guest NaiveUser { get; set; }

        public long? NaiveUserId { get; set; }

        public FacebookSender facebookUser { get; set; }

        public long? facebookUserId { get; set; }

        public WhatsAppSender WhatsappUser { get; set; }

        public long? WhatsappUserId { get; set; }

        [ForeignKey(nameof(WhatsApp360DialogUserId))]
        public WhatsApp360DialogSender WhatsApp360DialogUser { get; set; }

        public long? WhatsApp360DialogUserId { get; set; }

        // public ChatAPIWhatsAppSender ChatAPIWhatsAppUser { get; set; }
        public WhatsappCloudApiSender WhatsappCloudApiUser { get; set; }

        public UserDevice UserDevice { get; set; }

        public long? UserDeviceId { get; set; }

        public EmailSender EmailAddress { get; set; }

        public long? EmailAddressId { get; set; }

        public WebClientSender WebClient { get; set; }

        public long? WebClientId { get; set; }

        public WeChatSender WeChatUser { get; set; }

        public long? WeChatUserId { get; set; }

        public LineSender LineUser { get; set; }

        public long? LineUserId { get; set; }

        public ViberSender ViberUser { get; set; }

        public long? ViberUserId { get; set; }

        public TelegramSender TelegramUser { get; set; }

        public long? TelegramUserId { get; set; }

        public SMSSender SMSUser { get; set; }

        public long? SMSUserId { get; set; }

        public long? InstagramUserId { get; set; }

        public InstagramSender InstagramUser { get; set; }

        public string Status { get; set; } = "closed";

        public ActiveStatus ActiveStatus { get; set; }

        public IList<ConversationMessage> ChatHistory { get; set; }

        // public IList<ConversationTicket> Tickets { get; set; }
        //Last message
        public DateTime UpdatedTime { get; set; }

        //Conversation status changes
        public DateTime ModifiedAt { get; set; }

        public DateTime? CreatedAt { get; set; } = DateTime.UtcNow;

        public DateTime? StatusChangedFromClosedToOpenAt { get; set; }

        public DateTime? StatusChangedToClosedAt { get; set; }

        public List<ConversationHashtag> conversationHashtags { get; set; }

        // public List<ConversationActivityLog> ConversationActivityLogs { get; set; }
        // public List<ConversationRemark> ConversationRemarks { get; set; }
        public int UnreadMessageCount { get; set; }

        public DateTime? SnoozeUntil { get; set; }

        public bool IsSandbox { get; set; }

        public bool IsBookmarked { get; set; }

        public bool IsNewCreatedConversation { get; set; } = true;

        public long? AssigneeId { get; set; }

        public Staff Assignee { get; set; }

        public List<AdditionalAssignee> AdditionalAssignees { get; set; } = new ();

        public long? AssignedTeamId { get; set; }

        public CompanyTeam AssignedTeam { get; set; }

        public long? LastMessageId { get; set; }

        public string LastMessageChannel { get; set; }

        public long? LastMessageChannelId { get; set; }

        [MaxLength(400)]
        public string LastChannelIdentityId { get; set; }

        public List<ConversationBookmark> ConversationBookmarks { get; set; }

        public List<ConversationWhatsappSenderHistory> WhatsAppSenderHistories { get; set; } =
            new List<ConversationWhatsappSenderHistory>();

        // public SandboxSender SandboxUser { get; set; }
        // public long? SandboxUserId { get; set; }
        public bool IsUnreplied { get; set; }

        public Dictionary<string, object> Metadata { get; set; }
    }

    // public class ConversationRemark
    // {
    //    public long Id { get; set; }
    //    public string RemarkId { get; set; } = Guid.NewGuid().ToString();
    //    public string ConversationId { get; set; }
    //    public string Remarks { get; set; }
    //    public Staff RemarksStaff { get; set; }
    //    public long? RemarksStaffId { get; set; }
    //    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    // }

    // public class ConversationActivityLog
    // {
    //    public long Id { get; set; }
    //    public string ConversationId { get; set; }
    //    public string LogMessage { get; set; }
    //    public string Status { get; set; }
    //    public string Type { get; set; }
    //    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
    //    public Staff ModifiedBy { get; set; }
    // }
}