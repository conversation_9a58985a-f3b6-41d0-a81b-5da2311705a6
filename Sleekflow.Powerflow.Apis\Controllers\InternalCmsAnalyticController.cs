﻿using System.Globalization;
using System.Net.Mime;
using System.Text;
using AutoMapper;
using AutoMapper.QueryableExtensions;
using CsvHelper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Build.Framework;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using Travis_backend.AccountAuthenticationDomain.Models;
using Travis_backend.Cache;
using Travis_backend.CommonDomain.ViewModels;
using Travis_backend.Configuration;
using Travis_backend.Database;
using Travis_backend.Extensions;
using Travis_backend.InternalDomain.Models;
using Travis_backend.InternalDomain.Services;
using Travis_backend.MessageDomain.Models;
using CmsCacheKeyHelper = Sleekflow.Powerflow.Apis.Helpers.CmsCacheKeyHelper;
using CompanyRevenueStatusBreakdownsResponse =
    Sleekflow.Powerflow.Apis.ViewModels.CompanyRevenueStatusBreakdownsResponse;
using GetCmsAnalyticByOwnerResponse = Sleekflow.Powerflow.Apis.ViewModels.GetCmsAnalyticByOwnerResponse;
using GetCmsAnalyticByOwnersResponse = Sleekflow.Powerflow.Apis.ViewModels.GetCmsAnalyticByOwnersResponse;
using GetCmsAnalyticBySalesResponse = Sleekflow.Powerflow.Apis.ViewModels.GetCmsAnalyticBySalesResponse;
using GetCmsAnalyticByPartnerStackGroupNamesResponse =
    Sleekflow.Powerflow.Apis.ViewModels.GetCmsAnalyticByPartnerStackGroupNamesResponse;
using GetCmsAnalyticResponse = Sleekflow.Powerflow.Apis.ViewModels.GetCmsAnalyticResponse;
using GetCmsAccruedAnalyticResponse = Sleekflow.Powerflow.Apis.ViewModels.GetCmsAccruedAnalyticResponse;
using GetCmsCohortAnalysisDataRequest = Sleekflow.Powerflow.Apis.ViewModels.GetCmsCohortAnalysisDataRequest;
using GetCmsCohortAnalysisDataResponse = Sleekflow.Powerflow.Apis.ViewModels.GetCmsCohortAnalysisDataResponse;
using GetCmsCompanyAdditionalInfoResponse = Sleekflow.Powerflow.Apis.ViewModels.GetCmsCompanyAdditionalInfoResponse;
using GetCmsCompanyAdditionalInfosRequest = Sleekflow.Powerflow.Apis.ViewModels.GetCmsCompanyAdditionalInfosRequest;
using GetCmsDistributionAnalyticResponse = Sleekflow.Powerflow.Apis.ViewModels.GetCmsDistributionAnalyticResponse;
using InternalCmsAnalyticByOwnerIdRequest = Sleekflow.Powerflow.Apis.ViewModels.InternalCmsAnalyticByOwnerIdRequest;
using InternalCmsAnalyticByOwnerIdsRequest = Sleekflow.Powerflow.Apis.ViewModels.InternalCmsAnalyticByOwnerIdsRequest;
using InternalCmsAnalyticByPartnerStackGroupNamesRequest =
    Sleekflow.Powerflow.Apis.ViewModels.InternalCmsAnalyticByPartnerStackGroupNamesRequest;
using InternalCmsAnalyticRequest = Sleekflow.Powerflow.Apis.ViewModels.InternalCmsAnalyticRequest;
using UpdateCompaniesAllTimeRevenueAnalyticDataRequest =
    Sleekflow.Powerflow.Apis.ViewModels.UpdateCompaniesAllTimeRevenueAnalyticDataRequest;

namespace Sleekflow.Powerflow.Apis.Controllers;

/// <summary>
/// Internal Cms Revenue for display MRR.
/// </summary>
[Authorize(
    Roles = ApplicationUserRole.InternalCmsTeamLead + "," + ApplicationUserRole.InternalCmsCustomerSuccessUser +
            "," + ApplicationUserRole.InternalCmsSalesUser)]
[Route("/internal/analytic/[action]")]
public class InternalCmsAnalyticController : InternalControllerBase
{
    private readonly ApplicationDbContext _appDbContext;
    private readonly IMapper _mapper;
    private readonly IConfiguration _configuration;
    private readonly ILogger<InternalCmsAnalyticController> _logger;
    private readonly ICacheManagerService _cacheManagerService;
    private readonly IInternalAnalyticService _internalAnalyticService;

    public InternalCmsAnalyticController(
        ApplicationDbContext appDbContext,
        UserManager<ApplicationUser> userManager,
        IMapper mapper,
        IConfiguration configuration,
        ILogger<InternalCmsAnalyticController> logger,
        ICacheManagerService cacheManagerService,
        IInternalAnalyticService internalAnalyticService)
        : base(userManager)
    {
        _mapper = mapper;
        _configuration = configuration;
        _logger = logger;
        _cacheManagerService = cacheManagerService;
        _internalAnalyticService = internalAnalyticService;
        _appDbContext = appDbContext;
    }

    /// <summary>
    /// Get All Company Revenue Analytic.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    public async Task<ActionResult<GetCmsAnalyticResponse>> GetCmsAnalytic(
        [FromBody]
        InternalCmsAnalyticRequest request)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Invalid Form"
                });
        }

        if (await GetCurrentValidInternalUser(
                new List<string>()
                {
                    ApplicationUserRole.InternalCmsTeamLead
                }) == null)
        {
            return Unauthorized();
        }

        var start = request.Start.Date.AddHours(request.TimezoneHourOffset);
        var end = request.End.Date.AddHours(request.TimezoneHourOffset);

        var cacheKey = CmsCacheKeyHelper.GetCmsAnalyticKey(
            request.Start,
            request.End,
            request.TimezoneHourOffset,
            request.IsExcludeMarkupPlan,
            request.CompanyType);
        if (request.AllowCache)
        {
            var resultCacheData = await _cacheManagerService.GetCacheWithConstantKeyAsync(cacheKey);
            if (!string.IsNullOrWhiteSpace(resultCacheData))
            {
                return Content(resultCacheData, MediaTypeNames.Application.Json, Encoding.UTF8);
            }
        }

        var result = new GetCmsAnalyticResponse
        {
            DailyAnalytics = await _internalAnalyticService.GetDailyRevenueAnalytic(
                start,
                end,
                excludeMarkupPlan: request.IsExcludeMarkupPlan,
                companyType: request.CompanyType),
        };

        await _cacheManagerService.SaveCacheWithConstantKeyAsync(
            cacheKey,
            result,
            TimeSpan.FromHours(1),
            _jsonSerializerSettings
        );

        return Ok(result);
    }

    /// <summary>
    /// Get All Company Accrued Revenue Analytic.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    public async Task<ActionResult<GetCmsAccruedAnalyticResponse>> GetCmsAccruedAnalytic(
        [FromBody]
        InternalCmsAnalyticRequest request)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Invalid Form"
                });
        }

        if (await GetCurrentValidInternalUser(
                new List<string>()
                {
                    ApplicationUserRole.InternalCmsTeamLead
                }) == null)
        {
            return Unauthorized();
        }

        var start = request.Start.Date.AddHours(request.TimezoneHourOffset);
        var end = request.End.Date.AddHours(request.TimezoneHourOffset);

        var cacheKey = CmsCacheKeyHelper.GetCmsAccruedAnalyticKey(
            request.Start,
            request.End,
            request.TimezoneHourOffset,
            request.IsExcludeMarkupPlan,
            request.CompanyType);
        if (request.AllowCache)
        {
            var resultCacheData = await _cacheManagerService.GetCacheWithConstantKeyAsync(cacheKey);
            if (!string.IsNullOrWhiteSpace(resultCacheData))
            {
                return Content(resultCacheData, MediaTypeNames.Application.Json, Encoding.UTF8);
            }
        }

        var result = new GetCmsAccruedAnalyticResponse
        {
            DailyAccruedAnalytics = await _internalAnalyticService.GetDailyAccruedRevenueAnalytic(
                start,
                end,
                excludeMarkupPlan: request.IsExcludeMarkupPlan,
                companyType: request.CompanyType),
        };

        await _cacheManagerService.SaveCacheWithConstantKeyAsync(
            cacheKey,
            result,
            TimeSpan.FromHours(1),
            _jsonSerializerSettings
        );

        return Ok(result);
    }

    /// <summary>
    /// Get Cohort Analysis Data.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [Obsolete("Not using currently")]
    [HttpPost]
    public async Task<ActionResult<GetCmsCohortAnalysisDataResponse>> GetCmsCohortAnalysisData(
        [FromBody]
        GetCmsCohortAnalysisDataRequest request)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Invalid Form"
                });
        }

        if (await GetCurrentValidInternalUser(
                new List<string>()
                {
                    ApplicationUserRole.InternalCmsTeamLead
                }) == null)
        {
            return Unauthorized();
        }

        if (request.Year < 2020 || request.Year >= DateTime.UtcNow.Year)
        {
            return BadRequest(
                new ResponseViewModel()
                {
                    message = "Invalid Date Range."
                });
        }

        var start = new DateTime(request.Year - 1, 12, 1);
        var end = new DateTime(request.Year + 1, 1, 1);

        var cacheKey = CmsCacheKeyHelper.GetCmsCohortAnalysisDataKey(request.Year);
        if (request.AllowCache)
        {
            var resultCacheData = await _cacheManagerService.GetCacheWithConstantKeyAsync(cacheKey);
            if (!string.IsNullOrWhiteSpace(resultCacheData))
            {
                return Content(resultCacheData, MediaTypeNames.Application.Json, Encoding.UTF8);
            }
        }

        var result = new GetCmsCohortAnalysisDataResponse()
        {
            MonthlyCohortAnalysisData =
                await _internalAnalyticService.GetCmsCohortAnalysisData(request.Year, start, end),
        };

        await _cacheManagerService.SaveCacheWithConstantKeyAsync(
            cacheKey,
            result,
            TimeSpan.FromHours(12),
            _jsonSerializerSettings
        );

        return Ok(result);
    }

    /// <summary>
    /// Get All Company Revenue Analytic.
    /// </summary>
    /// <returns></returns>
    [HttpPost]
    public async Task<ActionResult<GetCmsDistributionAnalyticResponse>> GetCmsDistributionAnalytic(
        [FromBody]
        InternalCmsAnalyticRequest request)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Invalid Form"
                });
        }

        if (await GetCurrentValidInternalUser(
                new List<string>()
                {
                    ApplicationUserRole.InternalCmsTeamLead
                }) == null)
        {
            return Unauthorized();
        }

        var start = request.Start.Date.AddHours(request.TimezoneHourOffset);
        var end = request.End.Date.AddHours(request.TimezoneHourOffset);

        var result = new GetCmsDistributionAnalyticResponse
        {
            DailyDistributionAnalytics = await _internalAnalyticService.GetCmsDistributionAnalytic(start, end)
        };

        return Ok(result);
    }

    /// <summary>
    /// Get All Company Revenue Analytic by Sales / Activation Owner.
    /// </summary>
    /// <returns></returns>
    [Obsolete("Not using currently")]
    [HttpPost]
    public async Task<ActionResult<GetCmsAnalyticBySalesResponse>> GetCmsAnalyticBySales(
        [FromBody]
        InternalCmsAnalyticRequest request)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Invalid Form"
                });
        }

        if (await GetCurrentValidInternalUser(
                new List<string>()
                {
                    ApplicationUserRole.InternalCmsTeamLead,
                    ApplicationUserRole.InternalCmsCustomerSuccessUser,
                    ApplicationUserRole.InternalCmsSalesUser
                }) == null)
        {
            return Unauthorized();
        }

        var start = request.Start.Date.AddHours(request.TimezoneHourOffset);
        var end = request.End.Date.AddHours(request.TimezoneHourOffset);

        var cacheKey = CmsCacheKeyHelper.GetCmsAnalyticBySalesKey(
            request.Start,
            request.End,
            request.TimezoneHourOffset,
            request.IsExcludeMarkupPlan);
        if (request.AllowCache)
        {
            var resultCacheData = await _cacheManagerService.GetCacheWithConstantKeyAsync(cacheKey);
            if (!string.IsNullOrWhiteSpace(resultCacheData))
            {
                return Content(resultCacheData, MediaTypeNames.Application.Json, Encoding.UTF8);
            }
        }

        var result = new GetCmsAnalyticBySalesResponse()
        {
            ByContactOwnerDailyAnalytics = await _internalAnalyticService.GetCmsAnalyticByActivationOwner(
                start,
                end,
                excludeMarkupPlan: request.IsExcludeMarkupPlan,
                companyType: request.CompanyType)
        };

        await _cacheManagerService.SaveCacheWithConstantKeyAsync(
            cacheKey,
            result,
            TimeSpan.FromHours(1),
            _jsonSerializerSettings
        );

        return Ok(result);
    }

    /// <summary>
    /// Get All Company Revenue Analytic by Company Owner / CS.
    /// </summary>
    /// <returns></returns>
    [Obsolete("Not using currently")]
    [HttpPost]
    public async Task<ActionResult<GetCmsAnalyticBySalesResponse>> GetCmsAnalyticByCompanyOwner(
        [FromBody]
        InternalCmsAnalyticRequest request)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Invalid Form"
                });
        }

        if (await GetCurrentValidInternalUser(
                new List<string>()
                {
                    ApplicationUserRole.InternalCmsTeamLead,
                    ApplicationUserRole.InternalCmsCustomerSuccessUser,
                    ApplicationUserRole.InternalCmsSalesUser
                }) == null)
        {
            return Unauthorized();
        }

        var start = request.Start.Date.AddHours(request.TimezoneHourOffset);
        var end = request.End.Date.AddHours(request.TimezoneHourOffset);

        var cacheKey = CmsCacheKeyHelper.GetCmsAnalyticByCompanyOwnerKey(
            request.Start,
            request.End,
            request.TimezoneHourOffset,
            request.IsExcludeMarkupPlan);
        if (request.AllowCache)
        {
            var resultCacheData = await _cacheManagerService.GetCacheWithConstantKeyAsync(cacheKey);
            if (!string.IsNullOrWhiteSpace(resultCacheData))
            {
                return Content(resultCacheData, MediaTypeNames.Application.Json, Encoding.UTF8);
            }
        }

        var result = new GetCmsAnalyticBySalesResponse()
        {
            ByContactOwnerDailyAnalytics = await _internalAnalyticService.GetCmsAnalyticByCompanyOwner(
                start,
                end,
                excludeMarkupPlan: request.IsExcludeMarkupPlan,
                companyType: request.CompanyType)
        };

        await _cacheManagerService.SaveCacheWithConstantKeyAsync(
            cacheKey,
            result,
            TimeSpan.FromHours(1),
            _jsonSerializerSettings
        );

        return Ok(result);
    }

    /// <summary>
    /// Get Company Revenue Analytic by Sales / Activation Owner.
    /// </summary>
    /// <returns></returns>
    [HttpPost]
    public async Task<ActionResult<GetCmsAnalyticByOwnerResponse>> GetCmsAnalyticByActivationOwnerId(
        [FromBody]
        InternalCmsAnalyticByOwnerIdRequest request)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Invalid Form"
                });
        }

        if (await GetCurrentValidInternalUser(
                new List<string>()
                {
                    ApplicationUserRole.InternalCmsTeamLead,
                    ApplicationUserRole.InternalCmsCustomerSuccessUser,
                    ApplicationUserRole.InternalCmsSalesUser
                }) == null)
        {
            return Unauthorized();
        }

        var start = request.Start.Date.AddHours(request.TimezoneHourOffset);
        var end = request.End.Date.AddHours(request.TimezoneHourOffset);

        var cacheKey = CmsCacheKeyHelper.GetCmsAnalyticByActivationOwnerIdKey(
            request.Start,
            request.End,
            request.TimezoneHourOffset,
            request.IsExcludeMarkupPlan,
            request.OwnerId);
        if (request.AllowCache)
        {
            var resultCacheData = await _cacheManagerService.GetCacheWithConstantKeyAsync(cacheKey);
            if (!string.IsNullOrWhiteSpace(resultCacheData))
            {
                return Content(resultCacheData, MediaTypeNames.Application.Json, Encoding.UTF8);
            }
        }

        var result = new GetCmsAnalyticByOwnerResponse()
        {
            ByOwnerDailyAnalytic = await _internalAnalyticService.GetCmsAnalyticByActivationOwnerId(
                start,
                end,
                request.OwnerId,
                excludeMarkupPlan: request.IsExcludeMarkupPlan,
                companyType: request.CompanyType)
        };

        await _cacheManagerService.SaveCacheWithConstantKeyAsync(
            cacheKey,
            result,
            TimeSpan.FromHours(12),
            _jsonSerializerSettings
        );

        return Ok(result);
    }

    /// <summary>
    /// Get Company Revenue Analytic by Company Owner.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    public async Task<ActionResult<GetCmsAnalyticByOwnerResponse>> GetCmsAnalyticByCompanyOwnerId(
        [FromBody]
        InternalCmsAnalyticByOwnerIdRequest request)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Invalid Form"
                });
        }

        if (await GetCurrentValidInternalUser(
                new List<string>()
                {
                    ApplicationUserRole.InternalCmsTeamLead,
                    ApplicationUserRole.InternalCmsCustomerSuccessUser,
                    ApplicationUserRole.InternalCmsSalesUser
                }) == null)
        {
            return Unauthorized();
        }

        var start = request.Start.Date.AddHours(request.TimezoneHourOffset);
        var end = request.End.Date.AddHours(request.TimezoneHourOffset);

        var cacheKey = CmsCacheKeyHelper.GetCmsAnalyticByCompanyOwnerIdKey(
            request.Start,
            request.End,
            request.TimezoneHourOffset,
            request.IsExcludeMarkupPlan,
            request.OwnerId);
        if (request.AllowCache)
        {
            var resultCacheData = await _cacheManagerService.GetCacheWithConstantKeyAsync(cacheKey);
            if (!string.IsNullOrWhiteSpace(resultCacheData))
            {
                return Content(resultCacheData, MediaTypeNames.Application.Json, Encoding.UTF8);
            }
        }

        var result = new GetCmsAnalyticByOwnerResponse()
        {
            ByOwnerDailyAnalytic = await _internalAnalyticService.GetCmsAnalyticByCompanyOwnerId(
                start,
                end,
                request.OwnerId,
                excludeMarkupPlan: request.IsExcludeMarkupPlan,
                companyType: request.CompanyType)
        };

        await _cacheManagerService.SaveCacheWithConstantKeyAsync(
            cacheKey,
            result,
            TimeSpan.FromHours(1),
            _jsonSerializerSettings
        );

        return Ok(result);
    }

    /// <summary>
    /// Get Company Revenue Analytic by Sales / Activation Owner.
    /// </summary>
    /// <returns></returns>
    [HttpPost]
    public async Task<ActionResult<GetCmsAnalyticByOwnersResponse>> GetCmsAnalyticByActivationOwnerIds(
        [FromBody]
        InternalCmsAnalyticByOwnerIdsRequest request)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Invalid Form"
                });
        }

        if (await GetCurrentValidInternalUser(
                new List<string>()
                {
                    ApplicationUserRole.InternalCmsTeamLead,
                    ApplicationUserRole.InternalCmsCustomerSuccessUser,
                    ApplicationUserRole.InternalCmsSalesUser
                }) == null)
        {
            return Unauthorized();
        }

        var start = request.Start.Date.AddHours(request.TimezoneHourOffset);
        var end = request.End.Date.AddHours(request.TimezoneHourOffset);

        var cacheKey = CmsCacheKeyHelper.GetCmsAnalyticByActivationOwnerIdsKey(
            request.Start,
            request.End,
            request.TimezoneHourOffset,
            request.IsExcludeMarkupPlan,
            request.OwnerIds);
        if (request.AllowCache)
        {
            var resultCacheData = await _cacheManagerService.GetCacheWithConstantKeyAsync(cacheKey);
            if (!string.IsNullOrWhiteSpace(resultCacheData))
            {
                return Content(resultCacheData, MediaTypeNames.Application.Json, Encoding.UTF8);
            }
        }

        var result = new GetCmsAnalyticByOwnersResponse()
        {
            ByOwnersDailyAnalytic = await _internalAnalyticService.GetCmsAnalyticByActivationOwnerIds(
                start,
                end,
                request.OwnerIds,
                excludeMarkupPlan: request.IsExcludeMarkupPlan,
                companyType: request.CompanyType)
        };

        await _cacheManagerService.SaveCacheWithConstantKeyAsync(
            cacheKey,
            result,
            TimeSpan.FromHours(12),
            _jsonSerializerSettings
        );

        return Ok(result);
    }

    /// <summary>
    /// Get Company Revenue Analytic by Company Owner.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    public async Task<ActionResult<GetCmsAnalyticByOwnersResponse>> GetCmsAnalyticByCompanyOwnerIds(
        [FromBody]
        InternalCmsAnalyticByOwnerIdsRequest request)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Invalid Form"
                });
        }

        if (await GetCurrentValidInternalUser(
                new List<string>()
                {
                    ApplicationUserRole.InternalCmsTeamLead,
                    ApplicationUserRole.InternalCmsCustomerSuccessUser,
                    ApplicationUserRole.InternalCmsSalesUser
                }) == null)
        {
            return Unauthorized();
        }

        var start = request.Start.Date.AddHours(request.TimezoneHourOffset);
        var end = request.End.Date.AddHours(request.TimezoneHourOffset);

        var cacheKey = CmsCacheKeyHelper.GetCmsAnalyticByCompanyOwnerIdsKey(
            request.Start,
            request.End,
            request.TimezoneHourOffset,
            request.IsExcludeMarkupPlan,
            request.OwnerIds);
        if (request.AllowCache)
        {
            var resultCacheData = await _cacheManagerService.GetCacheWithConstantKeyAsync(cacheKey);
            if (!string.IsNullOrWhiteSpace(resultCacheData))
            {
                return Content(resultCacheData, MediaTypeNames.Application.Json, Encoding.UTF8);
            }
        }

        var result = new GetCmsAnalyticByOwnersResponse()
        {
            ByOwnersDailyAnalytic = await _internalAnalyticService.GetCmsAnalyticByCompanyOwnerIds(
                start,
                end,
                request.OwnerIds,
                excludeMarkupPlan: request.IsExcludeMarkupPlan,
                companyType: request.CompanyType)
        };

        await _cacheManagerService.SaveCacheWithConstantKeyAsync(
            cacheKey,
            result,
            TimeSpan.FromHours(1),
            _jsonSerializerSettings
        );

        return Ok(result);
    }

    /// <summary>
    /// Get Company Revenue Analytic by Sales / Activation Owner.
    /// </summary>
    /// <returns></returns>
    [HttpPost]
    public async Task<ActionResult<GetCmsAnalyticByOwnersResponse>> GetCmsAnalyticByPartnerStackGroupNames(
        [FromBody]
        InternalCmsAnalyticByPartnerStackGroupNamesRequest request)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Invalid Form"
                });
        }

        if (await GetCurrentValidInternalUser(
                new List<string>()
                {
                    ApplicationUserRole.InternalCmsTeamLead,
                    ApplicationUserRole.InternalCmsCustomerSuccessUser,
                    ApplicationUserRole.InternalCmsSalesUser
                }) == null)
        {
            return Unauthorized();
        }

        var start = request.Start.Date.AddHours(request.TimezoneHourOffset);
        var end = request.End.Date.AddHours(request.TimezoneHourOffset);

        var cacheKey = CmsCacheKeyHelper.GetCmsAnalyticByPartnerStackGroupNamesKey(
            request.Start,
            request.End,
            request.TimezoneHourOffset,
            request.IsExcludeMarkupPlan,
            request.GroupNames);
        if (request.AllowCache)
        {
            var resultCacheData = await _cacheManagerService.GetCacheWithConstantKeyAsync(cacheKey);
            if (!string.IsNullOrWhiteSpace(resultCacheData))
            {
                return Content(resultCacheData, MediaTypeNames.Application.Json, Encoding.UTF8);
            }
        }

        var result = new GetCmsAnalyticByPartnerStackGroupNamesResponse()
        {
            ByPartnerStackGroupNamesDailyAnalytic = await _internalAnalyticService.GetCmsAnalyticByPartnerStackGroupNames(
                start,
                end,
                request.GroupNames,
                excludeMarkupPlan: request.IsExcludeMarkupPlan,
                companyType: request.CompanyType)
        };

        await _cacheManagerService.SaveCacheWithConstantKeyAsync(
            cacheKey,
            result,
            TimeSpan.FromHours(12),
            _jsonSerializerSettings
        );

        return Ok(result);
    }

    /// <summary>
    /// Get Company Revenue Status Breakdowns.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [Obsolete("Not using currently")]
    [HttpPost]
    public async Task<ActionResult<GetCmsAnalyticBySalesResponse>> GetCmsCompanyRevenueStatusBreakdowns(
        [FromBody]
        InternalCmsAnalyticRequest request)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Invalid Form"
                });
        }

        if (await GetCurrentValidInternalUser(
                new List<string>()
                {
                    ApplicationUserRole.InternalCmsTeamLead
                }) == null)
        {
            return Unauthorized();
        }

        var start = request.Start.Date.AddHours(request.TimezoneHourOffset);
        var end = request.End.Date.AddHours(request.TimezoneHourOffset);

        if (start >= end)
        {
            return BadRequest();
        }

        var cacheKey = CmsCacheKeyHelper.GetCmsCompanyRevenueStatusBreakdownsKey(
            request.Start,
            request.End,
            request.TimezoneHourOffset);
        if (request.AllowCache)
        {
            var resultCacheData = await _cacheManagerService.GetCacheWithConstantKeyAsync(cacheKey);
            if (!string.IsNullOrWhiteSpace(resultCacheData))
            {
                return Content(resultCacheData, MediaTypeNames.Application.Json, Encoding.UTF8);
            }
        }

        var result = new CompanyRevenueStatusBreakdownsResponse()
        {
            CompanyRevenueStatusBreakdowns =
                await _internalAnalyticService.GetCompanyRevenueStatusBreakdowns(end, start)
        };

        await _cacheManagerService.SaveCacheWithConstantKeyAsync(
            cacheKey,
            result,
            TimeSpan.FromHours(1),
            _jsonSerializerSettings
        );

        return Ok(result);
    }

    /// <summary>
    /// Get Company Additional Infos.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    public async Task<ActionResult<GetCmsCompanyAdditionalInfoResponse>> GetCmsCompanyAdditionalInfos(
        [FromBody]
        GetCmsCompanyAdditionalInfosRequest request)
    {
        if (await GetCurrentValidInternalUser(
                new List<string>()
                {
                    ApplicationUserRole.InternalCmsTeamLead,
                    ApplicationUserRole.InternalCmsCustomerSuccessUser,
                    ApplicationUserRole.InternalCmsSalesUser
                }) == null)
        {
            return Unauthorized();
        }

        var cacheKey = CmsCacheKeyHelper.GetCmsCompaniesChurnReasonKey(
            request.CompanyIds,
            request.HasChurnReason,
            request.HasTier,
            request.HasAllTimeRevenueAnalyticData);
        if (request.AllowCache)
        {
            var resultCacheData = await _cacheManagerService.GetCacheWithConstantKeyAsync(cacheKey);
            if (!string.IsNullOrWhiteSpace(resultCacheData))
            {
                return Content(resultCacheData, MediaTypeNames.Application.Json, Encoding.UTF8);
            }
        }

        var companyAdditionalInfos = await _appDbContext
            .CmsCompanyAdditionalInfos
            .WhereIf(request.HasChurnReason, info => info.ChurnReason != null)
            .WhereIf(request.HasTier, info => info.CompanyTier != null)
            .ProjectTo<CmsCompanyAdditionalInfoViewModel>(_mapper.ConfigurationProvider)
            .ToListAsync();

        if (request.HasAllTimeRevenueAnalyticData)
        {
            companyAdditionalInfos = companyAdditionalInfos
                .Where(x => x.AllTimeRevenueAnalyticData?.TotalRevenue > 0).ToList();
        }

        var result = new GetCmsCompanyAdditionalInfoResponse()
        {
            CmsCompanyAdditionalInfos = companyAdditionalInfos
        };

        await _cacheManagerService.SaveCacheWithConstantKeyAsync(
            cacheKey,
            result,
            TimeSpan.FromHours(1),
            _jsonSerializerSettings
        );

        return Ok(result);
    }

    /// <summary>
    /// Update Companies All Time Revenue Analytic Data.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    public async Task<ActionResult<ResponseViewModel>> UpdateCompaniesAllTimeRevenueAnalyticData(
        [FromBody]
        UpdateCompaniesAllTimeRevenueAnalyticDataRequest request)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Invalid Form"
                });
        }

        if (await GetCurrentValidInternalUser(
                new List<string>()
                {
                    ApplicationUserRole.InternalCmsTeamLead
                }) == null)
        {
            return Unauthorized();
        }

        await _internalAnalyticService.UpdateCompanyAllTimeRevenueAnalyticInfo(request.CompanyIds);

        return Ok(
            new ResponseViewModel()
            {
                message = "Companies All Time Revenue Analytic updated"
            });
    }
}