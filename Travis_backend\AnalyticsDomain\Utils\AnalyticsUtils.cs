﻿using System;
using System.Collections.Generic;
using System.Linq;
using Travis_backend.AnalyticsDomain.Models;
using Travis_backend.Constants;
using Travis_backend.ContactDomain.Models;
using Travis_backend.ConversationDomain.Models;
using Travis_backend.Helpers;
using Travis_backend.MessageDomain.Models;

namespace Travis_backend.AnalyticsDomain.Utils;

public static class AnalyticsUtils
{
    /**
     * Constructing Analytics Data from the conversation messages
     * @param conversationMessagesEnum
     * @param newCustomersConversationsEnum
     * @param newUserProfileIdsEnum
     * @param conversationMessagesRespTimeEnum
     * @param fromWithTimeZone
     * @param timeZoneId
     * @return AnalyticsData
     */
    public static AnalyticsData ConstructingAnalyticsData(
        List<ConversationMessage> conversationMessagesEnum,
        List<Conversation> newCustomersConversationsEnum,
        List<UserProfile> newUserProfileIdsEnum,
        List<ConversationMessage> conversationMessagesRespTimeEnum,
        DateTime fromWithTimeZone,
        string timeZoneId)
    {
        // Conversation Message Analytic
        var conversationMessagesAnalytic = conversationMessagesEnum
            .GroupBy(x => x.CompanyId)
            .Select(
                g =>
                    new AnalyticsData()
                    {
                        NumberOfAllConversations = g.Select(x => x.ConversationId).Distinct().Count(),
                        NumberOfActiveConversations =
                            g.Where(x => !x.IsSentFromSleekflow).Select(x => x.ConversationId).Distinct().Count(),
                        NumberOfMessagesSent =
                            g.Count(x => x.IsSentFromSleekflow && x.Channel != ChannelTypes.Note),
                        NumberOfMessageReceived = g.Count(x => !x.IsSentFromSleekflow),
                        NumberOfBroadcastSent = g.Count(x => x.DeliveryType == DeliveryType.Broadcast),
                        NumberOfAutomatedMessages =
                            g.Count(
                                x =>
                                    x.MessageType != MessageTypes.System
                                    && (x.DeliveryType == DeliveryType.AutomatedMessage
                                        || x.DeliveryType == DeliveryType.FlowHubAction)),
                        NumberOfBroadcastDelivered =
                            g.Count(
                                x => x.MessageType != MessageTypes.System
                                      && x.DeliveryType == DeliveryType.Broadcast
                                      && (x.Status == MessageStatus.Received ||
                                          x.Status == MessageStatus.Read)),
                        NumberOfBroadcastRead =
                            g.Count(
                                x => x.DeliveryType == DeliveryType.Broadcast && x.Status == MessageStatus.Read),
                        ActiveAgents =
                            g.Select(x => x.SenderId ?? "0").Distinct().Count(x => x != "0"), // Count include null
                    })
            .FirstOrDefault();

        // Enquiries From New Customers
        var newEnquiryConversation = newCustomersConversationsEnum
            .Select(
                g =>
                    new
                    {
                        ConversationId = g.Id,
                    })
            .ToList();

        // User Profile Analytic
        var newUserProfileCount = newUserProfileIdsEnum.Count;

        // Response Time By Day
        var conversationAvgResponseTimeSum = 0L;
        var conversationResponseTimeCount = 0;

        var firstConversationAvgResponseTimeSum = 0L;
        var firstConversationResponseTimeCount = 0;

        var conversationMessages = conversationMessagesRespTimeEnum
            .Where(
                x =>
                    x.DeliveryType != DeliveryType.Broadcast
                    && x.DeliveryType != DeliveryType.AutomatedMessage
                    && x.DeliveryType != DeliveryType.FlowHubAction
                    && x.MessageType != MessageTypes.System)
            .Select(
                c =>
                    new
                    {
                        ConversationId = c.ConversationId,
                        IsSentFromSleekflow = c.IsSentFromSleekflow,
                        TimeStamp = c.Timestamp,
                        DeliveryType = c.DeliveryType
                    })
            .ToList();

        var conversationsWithMessages = conversationMessages
            .OrderBy(c => c.TimeStamp)
            .GroupBy(x => x.ConversationId)
            .Select(
                g =>
                    new
                    {
                        conversationId = g.Key,
                        messages = g.Select(
                                c =>
                                    new
                                    {
                                        IsSentFromSleekflow = c.IsSentFromSleekflow,
                                        TimeStamp = c.TimeStamp,
                                        DeliveryType = c.DeliveryType
                                    })
                            .OrderBy(x => x.TimeStamp)
                            .ToList(),
                    })
            .ToList();

        int broadcastReplyCount = 0;

        // Looping Conversations
        foreach (var conversation in conversationsWithMessages)
        {
            var receivedTime = 0L;
            var replyTime = 0L;

            var responseTimeSum = 0L;

            bool hasConversationReceivedBroadcastFromSleekflow = false;
            bool hasBroadcastReplied = false;
            int messageReplyCount = 0;

            var firstConversationResponseTimeNeeded =
                newEnquiryConversation != null
                && newEnquiryConversation.Any(x => x.ConversationId == conversation.conversationId);

            // Looping Messages In Conversation
            foreach (var messages in conversation.messages)
            {
                // Set Receive Time
                if (receivedTime == 0L && !messages.IsSentFromSleekflow)
                {
                    receivedTime = messages.TimeStamp;
                }

                // Set Reply Time
                if (receivedTime != 0L && replyTime == 0L && messages.IsSentFromSleekflow)
                {
                    replyTime = messages.TimeStamp;
                }

                if (receivedTime != 0L && replyTime != 0L)
                {
                    // Calculate response time
                    responseTimeSum += replyTime - receivedTime;
                    messageReplyCount++;

                    // Reset Receive, Reply Time
                    receivedTime = 0;
                    replyTime = 0;

                    // Calculate first conversation response time
                    if (firstConversationResponseTimeNeeded)
                    {
                        firstConversationAvgResponseTimeSum += responseTimeSum;
                        firstConversationResponseTimeCount++;
                        firstConversationResponseTimeNeeded = false;
                    }
                }

                // Broadcast reply count
                if (!hasConversationReceivedBroadcastFromSleekflow
                    && messages.DeliveryType == DeliveryType.Broadcast
                    && messages.IsSentFromSleekflow)
                {
                    hasConversationReceivedBroadcastFromSleekflow = true;
                }

                // Check is use replied broadcast
                if (hasConversationReceivedBroadcastFromSleekflow
                    && !messages.IsSentFromSleekflow)
                {
                    hasBroadcastReplied = true;
                }
            }

            if (responseTimeSum > 0L)
            {
                conversationAvgResponseTimeSum += responseTimeSum / messageReplyCount;
                conversationResponseTimeCount++;
            }

            if (hasConversationReceivedBroadcastFromSleekflow && hasBroadcastReplied)
            {
                broadcastReplyCount++;
            }
        }

        // Calculate response times
        var responseTime = new AnalyticsData()
        {
            DateTime = fromWithTimeZone.ConvertUtcDateTimeToSpecificTimeZoneDateTime(timeZoneId).Date,
            ResponseTimeForAllMessages =
                conversationResponseTimeCount > 0
                    ? TimeSpan.FromSeconds(conversationAvgResponseTimeSum / conversationResponseTimeCount)
                    : TimeSpan.Zero,
            ResponseTimeForFirstMessages =
                firstConversationResponseTimeCount > 0
                    ? TimeSpan.FromSeconds(firstConversationAvgResponseTimeSum / firstConversationResponseTimeCount)
                    : TimeSpan.Zero,
            NumberOfBroadcastReplied = broadcastReplyCount
        };

        // Append All Analytic Data
        var finalAnalyticData = new AnalyticsData()
        {
            DateTime = fromWithTimeZone.ConvertUtcDateTimeToSpecificTimeZoneDateTime(timeZoneId).Date,
        };

        // Append Conversation Messages Analytic Data
        if (conversationMessagesAnalytic != null)
        {
            finalAnalyticData.NumberOfAllConversations = conversationMessagesAnalytic.NumberOfAllConversations;
            finalAnalyticData.NumberOfActiveConversations =
                conversationMessagesAnalytic.NumberOfActiveConversations;
            finalAnalyticData.NumberOfMessagesSent = conversationMessagesAnalytic.NumberOfMessagesSent;
            finalAnalyticData.NumberOfMessageReceived = conversationMessagesAnalytic.NumberOfMessageReceived;
            finalAnalyticData.NumberOfBroadcastSent = conversationMessagesAnalytic.NumberOfBroadcastSent;
            finalAnalyticData.NumberOfAutomatedMessages = conversationMessagesAnalytic.NumberOfAutomatedMessages;
            finalAnalyticData.NumberOfBroadcastDelivered = conversationMessagesAnalytic.NumberOfBroadcastDelivered;
            finalAnalyticData.NumberOfBroadcastRead = conversationMessagesAnalytic.NumberOfBroadcastRead;
            finalAnalyticData.NumberOfBroadcastBounced =
                conversationMessagesAnalytic.NumberOfBroadcastSent -
                conversationMessagesAnalytic.NumberOfBroadcastDelivered;
            finalAnalyticData.ActiveAgents = conversationMessagesAnalytic.ActiveAgents;

            finalAnalyticData.NumberOfBroadcastReplied = broadcastReplyCount;
        }
        else
        {
            finalAnalyticData.ActiveAgents = 0;
        }

        // Append New Enquiry Analytic Data
        finalAnalyticData.NumberOfNewEnquires = newEnquiryConversation.Count;

        // Append New User Profile Count
        finalAnalyticData.NumberOfContacts = newUserProfileCount;

        // Append Conversation Response Time
        finalAnalyticData.ResponseTimeForAllMessages = responseTime.ResponseTimeForAllMessages;
        finalAnalyticData.ResponseTimeForFirstMessages = responseTime.ResponseTimeForFirstMessages;
        return finalAnalyticData;
    }
}