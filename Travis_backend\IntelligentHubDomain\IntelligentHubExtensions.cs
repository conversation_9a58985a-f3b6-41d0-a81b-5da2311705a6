﻿using System.Net.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Sleekflow.Apis.IntelligentHub.Api;
using Travis_backend.Constants;
using Travis_backend.IntelligentHubDomain.HttpClientHandlers;
using Travis_backend.IntelligentHubDomain.Services;

namespace Travis_backend.IntelligentHubDomain;

public static class IntelligentHubExtensions
{
    public static IServiceCollection RegisterIntelligentHubServices(
        this IServiceCollection services,
        IConfiguration configuration)
    {
        var intelligentHubConfig = new Sleekflow.Apis.IntelligentHub.Client.Configuration
        {
            BasePath = configuration.GetValue<string>("IntelligentHub:Endpoint")
        };

        services.AddSingleton(intelligentHubConfig);

        services.AddTransient<IntelligentHubHttpClientHandler>();
        services.AddHttpClient(
            HttpClientHandlerName.IntelligentHub,
            (sp, client) =>
            {
                var config = sp.GetRequiredService<IConfiguration>();
                client.DefaultRequestHeaders.TryAddWithoutValidation(
                    "X-Sleekflow-Key",
                    config.GetValue<string>("IntelligentHub:Key"));
            });

        services.AddScoped<IBlobsApi, BlobsApi>(
            sp =>
                new (
                    sp.GetRequiredService<IHttpClientFactory>()
                        .CreateClient(HttpClientHandlerName.IntelligentHub),
                    sp.GetRequiredService<Sleekflow.Apis.IntelligentHub.Client.Configuration>(),
                    sp.GetRequiredService<IntelligentHubHttpClientHandler>()));

        services.AddScoped<IDocumentsApi, DocumentsApi>(
            sp =>
                new (
                    sp.GetRequiredService<IHttpClientFactory>()
                        .CreateClient(HttpClientHandlerName.IntelligentHub),
                    sp.GetRequiredService<Sleekflow.Apis.IntelligentHub.Client.Configuration>(),
                    sp.GetRequiredService<IntelligentHubHttpClientHandler>()));

        services.AddScoped<IKnowledgeBasesApi, KnowledgeBasesApi>(
            sp =>
                new (
                    sp.GetRequiredService<IHttpClientFactory>()
                        .CreateClient(HttpClientHandlerName.IntelligentHub),
                    sp.GetRequiredService<Sleekflow.Apis.IntelligentHub.Client.Configuration>(),
                    sp.GetRequiredService<IntelligentHubHttpClientHandler>()));

        services.AddScoped<ITextEnrichmentsApi, TextEnrichmentsApi>(
            sp =>
                new (
                    sp.GetRequiredService<IHttpClientFactory>()
                        .CreateClient(HttpClientHandlerName.IntelligentHub),
                    sp.GetRequiredService<Sleekflow.Apis.IntelligentHub.Client.Configuration>(),
                    sp.GetRequiredService<IntelligentHubHttpClientHandler>()));

        services.AddScoped<IWebScrapersApi, WebScrapersApi>(
            sp =>
                new (
                    sp.GetRequiredService<IHttpClientFactory>()
                        .CreateClient(HttpClientHandlerName.IntelligentHub),
                    sp.GetRequiredService<Sleekflow.Apis.IntelligentHub.Client.Configuration>(),
                    sp.GetRequiredService<IntelligentHubHttpClientHandler>()));

        services.AddTransient<IIntelligentHubConfigsApi, IntelligentHubConfigsApi>(
            sp => new (
                sp.GetRequiredService<IHttpClientFactory>()
                    .CreateClient(HttpClientHandlerName.IntelligentHub),
                sp.GetRequiredService<Sleekflow.Apis.IntelligentHub.Client.Configuration>(),
                sp.GetRequiredService<IntelligentHubHttpClientHandler>()));

        services.AddTransient<IRecommendedRepliesApi, RecommendedRepliesApi>(
            sp => new (
                sp.GetRequiredService<IHttpClientFactory>()
                    .CreateClient(HttpClientHandlerName.IntelligentHub),
                sp.GetRequiredService<Sleekflow.Apis.IntelligentHub.Client.Configuration>(),
                sp.GetRequiredService<IntelligentHubHttpClientHandler>()));

        services.AddScoped<IIntelligentHubService, IntelligentHubService>();

        return services;
    }
}