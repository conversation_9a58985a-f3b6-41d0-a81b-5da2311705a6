﻿using System.Collections.Generic;
using Travis_backend.ContactDomain.Services;
using Travis_backend.ContactDomain.ViewModels;

namespace Travis_backend.Extensions;

public static class DictionaryExtensions
{
    public static UserProfileService.ImportUserProfileObject ToImportUserProfileObject(
        this IDictionary<string, string> source)
    {
        if (source is null)
        {
            return new UserProfileService.ImportUserProfileObject(
                new List<ImportHeader>(),
                new List<string>());
        }

        List<ImportHeader> headers = new();
        List<string> fields = new();

        foreach (var key in source.Keys)
        {
            headers.Add(new() { HeaderName = key });
            fields.Add(source[key]);
        }

        return new(headers, fields);
    }
}