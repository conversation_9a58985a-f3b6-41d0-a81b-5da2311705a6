using System;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Travis_backend.ChannelDomain.Models;
using Travis_backend.ChannelDomain.ViewModels;
using Travis_backend.Constants;
using Travis_backend.ConversationDomain.Models;
using Travis_backend.ConversationServices;
using Travis_backend.Database;
using Travis_backend.FileDomain.Models;
using Travis_backend.MessageDomain.Models;
using Travis_backend.OpenTelemetry.Meters;
using Travis_backend.OpenTelemetry.Meters.Constants;

namespace Travis_backend.MessageDomain.ChannelMessageProvider.ChannelMessageHandlers;

public class WechatChannelMessageHandler : IChannelMessageHandler
{
    private readonly ApplicationDbContext _appDbContext;
    private readonly ILogger<WechatChannelMessageHandler> _logger;
    private readonly IAzureBlobStorageService _azureBlobStorageService;
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly IConversationMeters _conversationMeters;

    public string ChannelType => ChannelTypes.Wechat;

    public WechatChannelMessageHandler(
        ApplicationDbContext appDbContext,
        ILogger<WechatChannelMessageHandler> logger,
        IAzureBlobStorageService azureBlobStorageService,
        IHttpClientFactory httpClientFactory,
        IConversationMeters conversationMeters)
    {
        _appDbContext = appDbContext;
        _logger = logger;
        _azureBlobStorageService = azureBlobStorageService;
        _httpClientFactory = httpClientFactory;
        _conversationMeters = conversationMeters;
    }

    public async ValueTask<ConversationMessage> SendChannelMessageAsync(
        Conversation conversation,
        ConversationMessage conversationMessage)
    {
        if (conversationMessage.WeChatReceiver != null && string.IsNullOrEmpty(conversationMessage.MessageUniqueID))
        {
            string messageContent = string.Empty;

            if (conversationMessage.TranslationResults != null)
            {
                messageContent = conversationMessage.TranslationResults.FirstOrDefault().translations
                    .FirstOrDefault().text;
            }
            else
            {
                messageContent = conversationMessage.MessageContent;
            }

            var weChatConfig = await GetWeChatConfigNUpdateAccessToken(conversation);

            switch (conversationMessage.MessageType)
            {
                case "file":
                    foreach (var file in conversationMessage.UploadedFiles)
                    {
                        var memoryStreamByteArray = (await DownloadUploadedFile(file)).ToArray();

                        var uploadResponse = await UploadTemporaryMaterialToWeChatServer(
                            weChatConfig,
                            memoryStreamByteArray,
                            file.MIMEType);

                        await SendWeChatMediaFileMessage(
                            weChatConfig,
                            conversationMessage,
                            uploadResponse.MediaId,
                            file.MIMEType);
                    }

                    break;
                case "text":
                {
                    var request = new WeChatSendTextMessageRequest
                    {
                        ToUser = conversationMessage.WeChatReceiver.openid,
                        MsgType = "text",
                        Text = new WeChatTextMessageContent
                        {
                            Content = messageContent
                        }
                    };

                    var httpClient = _httpClientFactory.CreateClient(HttpClientHandlerName.Default);

                    var sendMessageResponse = await PostJsonAsync(
                        httpClient,
                        $"https://api.weixin.qq.com/cgi-bin/message/custom/send?access_token={weChatConfig.AccessToken}",
                        request);
                    var sendMessageString = await sendMessageResponse.Content.ReadAsStringAsync();
                    var sendMessage = JsonConvert.DeserializeObject<WeChatAPIResponse>(sendMessageString);

                    conversationMessage.Status =
                        (sendMessage.errcode == 0) ? MessageStatus.Sent : MessageStatus.Failed;
                    conversationMessage.ChannelStatusMessage = sendMessage.errmsg;

                    if (conversationMessage.Status == MessageStatus.Failed)
                    {
                        _logger.LogWarning(
                            "Error response from WeChat's server: appId = {WeChatConfigAppId}, conversationId = {ConversationId}, error message = {ConversationMessageChannelStatusMessage}",
                            weChatConfig.AppId,
                            conversation?.Id,
                            conversationMessage.ChannelStatusMessage);

                        _conversationMeters.IncrementCounter(ChannelTypes.Wechat, ConversationMeterOptions.SendFailed);
                    }
                }

                    break;
            }
        }
        else
        {
            conversationMessage.Status = MessageStatus.Sent;
        }

        if (conversationMessage.Status == MessageStatus.Sent)
        {
            conversationMessage.Status = MessageStatus.Read;

            _conversationMeters.IncrementCounter(ChannelTypes.Wechat, ConversationMeterOptions.SendSuccess);
        }

        await _appDbContext.SaveChangesAsync();

        return conversationMessage;
    }

    private async Task<ConversationMessage> SendWechatMessageAsync(
        Conversation conversation,
        ConversationMessage conversationMessage)
    {
        string messageContent = string.Empty;

        if (conversationMessage.TranslationResults != null)
        {
            messageContent = conversationMessage.TranslationResults.FirstOrDefault().translations
                .FirstOrDefault().text;
        }
        else
        {
            messageContent = conversationMessage.MessageContent;
        }

        var weChatConfig = await GetWeChatConfigNUpdateAccessToken(conversation);

        switch (conversationMessage.MessageType)
        {
            case "file":
                foreach (var file in conversationMessage.UploadedFiles)
                {
                    var memoryStreamByteArray = (await DownloadUploadedFile(file)).ToArray();

                    var uploadResponse = await UploadTemporaryMaterialToWeChatServer(
                        weChatConfig,
                        memoryStreamByteArray,
                        file.MIMEType);

                    await SendWeChatMediaFileMessage(
                        weChatConfig,
                        conversationMessage,
                        uploadResponse.MediaId,
                        file.MIMEType);
                }

                break;
            case "text":
            {
                var request = new WeChatSendTextMessageRequest
                {
                    ToUser = conversationMessage.WeChatReceiver.openid,
                    MsgType = "text",
                    Text = new WeChatTextMessageContent
                    {
                        Content = messageContent
                    }
                };

                var httpClient = _httpClientFactory.CreateClient(HttpClientHandlerName.Default);

                var sendMessageResponse = await PostJsonAsync(
                    httpClient,
                    $"https://api.weixin.qq.com/cgi-bin/message/custom/send?access_token={weChatConfig.AccessToken}",
                    request);
                var sendMessageString = await sendMessageResponse.Content.ReadAsStringAsync();
                var sendMessage = JsonConvert.DeserializeObject<WeChatAPIResponse>(sendMessageString);

                conversationMessage.Status =
                    (sendMessage.errcode == 0) ? MessageStatus.Sent : MessageStatus.Failed;
                conversationMessage.ChannelStatusMessage = sendMessage.errmsg;

                if (conversationMessage.Status == MessageStatus.Failed)
                {
                    _logger.LogWarning(
                        "Error response from WeChat's server: appId = {WeChatConfigAppId}, conversationId = {ConversationId}, error message = {ConversationMessageChannelStatusMessage}",
                        weChatConfig.AppId,
                        conversation?.Id,
                        conversationMessage.ChannelStatusMessage);
                }
            }

                break;
        }

        return conversationMessage;
    }

    private async Task SendWeChatMediaFileMessage(
        WeChatConfig weChatConfig,
        ConversationMessage conversationMessage,
        string media_id,
        string mimeType)
    {
        string type = mimeType.Split('/')[0];

        string sendMediaMessageUrl = string.Format(
            "https://api.weixin.qq.com/cgi-bin/message/custom/send?access_token=" + weChatConfig.AccessToken);
        HttpResponseMessage sendMessageResponse = default;
        var httpClient = _httpClientFactory.CreateClient(HttpClientHandlerName.Default);

        if (type == "image")
        {
            var sendImageMessageRequest = new WeChatSendImageMessageRequest()
            {
                ToUser = conversationMessage.WeChatReceiver.openid,
                MsgType = type,
                Image = new WeChatImageMessageContent
                {
                    MediaId = media_id
                }
            };

            sendMessageResponse = await PostJsonAsync(httpClient, sendMediaMessageUrl, sendImageMessageRequest);
        }
        else if (type == "video")
        {
            string getUploadedVideoFromWeChatServerUrl = string.Format(
                "https://api.weixin.qq.com/cgi-bin/media/get?access_token={0}&media_id={1}",
                weChatConfig.AccessToken,
                media_id);

            var getUploadedVideFromWeChatServerResponse =
                await httpClient.GetAsync(getUploadedVideoFromWeChatServerUrl);

            var getUploadedVideoResponse = JsonConvert.DeserializeObject<WeChatGetUploadedVideoResponse>(
                await getUploadedVideFromWeChatServerResponse.Content.ReadAsStringAsync());
            string videoUrl = getUploadedVideoResponse.VideoUrl;

            var sendTextMessageRequest = new WeChatSendTextMessageRequest
            {
                ToUser = conversationMessage.WeChatReceiver.openid,
                MsgType = "text",
                Text = new WeChatTextMessageContent
                {
                    Content = "Dear user, please click this link to access the video \n" + videoUrl
                }
            };
            sendMessageResponse = await PostJsonAsync(httpClient, sendMediaMessageUrl, sendTextMessageRequest);
        }

        var sendMessageString = await sendMessageResponse.Content.ReadAsStringAsync();
        var sendMessage = JsonConvert.DeserializeObject<WeChatAPIResponse>(sendMessageString);
        conversationMessage.Status = (sendMessage.errcode == 0) ? MessageStatus.Sent : MessageStatus.Failed;
        conversationMessage.ChannelStatusMessage = sendMessage.errmsg;
    }

    private async Task<WeChatConfig> GetWeChatConfigNUpdateAccessToken(Conversation conversation)
    {
        var httpClient = _httpClientFactory.CreateClient(HttpClientHandlerName.Default);

        var weChatConfig = await _appDbContext.CompanyCompanies.Where(x => x.Id == conversation.CompanyId)
            .Select(x => x.WeChatConfig).FirstOrDefaultAsync();

        if (weChatConfig == null)
        {
            throw new Exception($"No WeChat Config found. CompanyId: {conversation.CompanyId}");
        }

        if (string.IsNullOrEmpty(weChatConfig.AccessToken) || DateTime.UtcNow > weChatConfig.TokenExpireAt)
        {
            var getAccessTokenResponse = await httpClient.GetStringAsync(
                $"https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid={weChatConfig.AppId}&secret={weChatConfig.AppSecret}");
            var accessTokenResponse = JsonConvert.DeserializeObject<AccessTokenResponse>(getAccessTokenResponse);
            weChatConfig.AccessToken = accessTokenResponse.access_token;
            weChatConfig.TokenExpireAt = DateTime.UtcNow.AddSeconds(accessTokenResponse.expires_in - 30);
            await _appDbContext.SaveChangesAsync();
        }
        else
        {
            var tokenValidationResponse = await httpClient.GetStringAsync(
                $"https://api.weixin.qq.com/cgi-bin/get_api_domain_ip?access_token={weChatConfig.AccessToken}");

            if (tokenValidationResponse.Contains("40001"))
            {
                var getAccessTokenResponse = await httpClient.GetStringAsync(
                    $"https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid={weChatConfig.AppId}&secret={weChatConfig.AppSecret}");

                var accessTokenResponse =
                    JsonConvert.DeserializeObject<AccessTokenResponse>(getAccessTokenResponse);

                if (getAccessTokenResponse.Contains("expires_in"))
                {
                    // 200
                    weChatConfig.AccessToken = accessTokenResponse.access_token;
                    weChatConfig.TokenExpireAt = DateTime.UtcNow.AddSeconds(accessTokenResponse.expires_in - 30);
                    await _appDbContext.SaveChangesAsync();
                }
            }
        }

        return weChatConfig;
    }

    private async Task<MemoryStream> DownloadUploadedFile(UploadedFile file)
    {
        var fileMemoryStream =
            await _azureBlobStorageService.DownloadFromAzureBlob(file.Filename, file.BlobContainer);

        return fileMemoryStream;
    }

    private async Task<WeChatUploadTemporaryMaterialResponse> UploadTemporaryMaterialToWeChatServer(
        WeChatConfig weChatConfig,
        byte[] memoryStreamByteArray,
        string mimeType)
    {
        string type = mimeType.Split('/')[0];

        string apiToWeChatServer = string.Format(
            "https://api.weixin.qq.com/cgi-bin/media/upload?access_token={0}&type={1}",
            weChatConfig.AccessToken,
            type);

        // Set params
        HttpWebRequest request = WebRequest.Create(apiToWeChatServer) as HttpWebRequest;
        CookieContainer cookieContainer = new CookieContainer();
        request.CookieContainer = cookieContainer;
        request.AllowAutoRedirect = true;
        request.Method = "POST";
        string boundary = DateTime.Now.Ticks.ToString("X");
        request.ContentType = "multipart/form-data;charset=utf-8;boundary=" + boundary;
        byte[] itemBoundaryBytes = Encoding.UTF8.GetBytes("\r\n--" + boundary + "\r\n");
        byte[] endBoundaryBytes = Encoding.UTF8.GetBytes("\r\n--" + boundary + "--\r\n");

        // int pos = path.LastIndexOf("\\");
        // string fileName = path.Substring(pos + 1);

        // form data
        StringBuilder header = new StringBuilder();
        header.Append("--" + boundary + "\r\n");
        string fileType = mimeType.Split('/')[1];

        header.Append(
            "Content-Disposition: form-data; name=\"media\"; filename=\"" + "sth." + fileType +
            "\"; filelength=\"" + memoryStreamByteArray.Length + "\"");
        header.Append("\r\n");
        header.Append("Content-Type: " + mimeType);
        header.Append("\r\n\r\n");

        byte[] headerInBytes = Encoding.UTF8.GetBytes(header.ToString());

        Stream postStream = request.GetRequestStream();
        postStream.Write(itemBoundaryBytes, 0, itemBoundaryBytes.Length);
        postStream.Write(headerInBytes, 0, headerInBytes.Length);
        postStream.Write(memoryStreamByteArray, 0, memoryStreamByteArray.Length);
        postStream.Write(endBoundaryBytes, 0, endBoundaryBytes.Length);
        postStream.Close();

        // send request & get response
        HttpWebResponse response = await request.GetResponseAsync() as HttpWebResponse;
        Stream instream = response.GetResponseStream();
        StreamReader streamReader = new StreamReader(instream, Encoding.UTF8);

        // convert the response to an object to return
        string content = streamReader.ReadToEnd();

        if (content.Contains("errcode"))
        {
            _logger.LogWarning(
                "[{MethodName}] WeChat {WeChatConfigId} upload to WeChat server fails : {Payload}",
                nameof(UploadTemporaryMaterialToWeChatServer),
                weChatConfig.Id,
                content);
        }

        var result = JsonConvert.DeserializeObject<WeChatUploadTemporaryMaterialResponse>(content);

        return result;
    }

    private async Task<HttpResponseMessage> PostJsonAsync<T>(
        HttpClient client,
        string requestUri,
        T value)
    {
        var data = JsonConvert.SerializeObject(value);

        var content = new StringContent(
            data,
            System.Text.Encoding.UTF8,
            "application/json");

        return await client.PostAsync(requestUri, content).ConfigureAwait(false);
    }
}