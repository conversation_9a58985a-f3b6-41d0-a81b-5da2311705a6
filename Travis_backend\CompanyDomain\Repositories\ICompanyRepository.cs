using System.Threading.Tasks;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.CompanyDomain.ViewModels;

namespace Travis_backend.CompanyDomain.Repositories;

/// <summary>
/// Repository for access CompanyCompanies table.
/// </summary>
public interface ICompanyRepository
{
    /// <summary>
    /// Find Company by Id
    /// </summary>
    /// <param name="companyId">Company Id.</param>
    /// <returns>Entity of Company</returns>
    Task<Company> FindById(string companyId);

    /// <summary>
    /// Update Company's Subscription Tier.
    /// </summary>
    /// <param name="companyId">Company Id.</param>
    /// <param name="subscriptionCountryTier">Subscription Country Tier.</param>
    /// <param name="currency">Currency.</param>
    /// <returns>Number of Affected Rows.</returns>
    Task<int> UpdateCompanySubscriptionTierAndCurrencyAsync(string companyId, string subscriptionCountryTier, string currency);

    /// <summary>
    /// Get whether if company has set subscription country tier.
    /// </summary>
    /// <param name="companyId">Company Id.</param>
    /// <returns>Boolean.</returns>
    Task<bool> IsSubscriptionCountryTierHasValue(string companyId);

    /// <summary>
    /// Get whether if company has set subscription currency.
    /// </summary>
    /// <param name="companyId">Company Id.</param>
    /// <returns>Boolean.</returns>
    Task<bool> IsSubscriptionCurrencyHasValue(string companyId);

    /// <summary>
    /// Update Company's CompanyUsageLimitOffsetProfile.
    /// </summary>
    /// <param name="companyId">CompanyId.</param>
    /// <param name="profile">CompanyUsageLimitOffsetProfile to update.</param>
    /// <returns>Affected Rows.</returns>
    Task<int> UpdateCompanyUsageLimitOffsetProfileAsync(string companyId, CompanyUsageLimitOffsetProfile profile);
}