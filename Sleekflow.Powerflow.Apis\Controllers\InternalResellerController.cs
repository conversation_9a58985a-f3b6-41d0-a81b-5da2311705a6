using AutoMapper;
using Hangfire;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Travis_backend.AccountAuthenticationDomain.Models;
using Travis_backend.Cache;
using Travis_backend.CommonDomain.ViewModels;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.CompanyDomain.Repositories;
using Travis_backend.Configuration;
using Travis_backend.Constants;
using Travis_backend.ConversationServices;
using Travis_backend.Database;
using Travis_backend.Enums;
using Travis_backend.FlowHubs;
using Travis_backend.IntelligentHubDomain.Services;
using Travis_backend.InternalDomain.Services;
using Travis_backend.ResellerDomain.Constants;
using Travis_backend.ResellerDomain.Models;
using Travis_backend.ResellerDomain.Services;
using Travis_backend.ResellerDomain.ViewModels;
using Travis_backend.SleekflowCrmHubDomain.Services;
using Travis_backend.SubscriptionPlanDomain.Services;

namespace Sleekflow.Powerflow.Apis.Controllers;

[Authorize(Roles = ApplicationUserRole.InternalCmsUser)]
[Route("/internal/reseller/[action]")]
public class InternalResellerController : InternalControllerBase
{
    private readonly ApplicationDbContext _appDbContext;
    private readonly IMapper _mapper;
    private readonly ILogger<InternalResellerController> _logger;
    private readonly ICoreService _coreService;
    private readonly IPowerflowManageResellerRepository _powerflowManageResellerRepository;
    private readonly RoleManager<IdentityRole> _roleManager;
    private readonly ICompanyInfoCacheService _companyInfoCacheService;
    private readonly IDefaultSubscriptionPlanIdGetter _defaultSubscriptionPlanIdGetter;
    private readonly IUserRoleStaffRepository _userRoleStaffRepository;

    public InternalResellerController(
        ApplicationDbContext appDbContext,
        UserManager<ApplicationUser> userManager,
        IMapper mapper,
        ILogger<InternalResellerController> logger,
        ICoreService coreService,
        IPowerflowManageResellerRepository powerflowManageResellerRepository,
        RoleManager<IdentityRole> roleManager,
        ICompanyInfoCacheService companyInfoCacheService,
        IDefaultSubscriptionPlanIdGetter defaultSubscriptionPlanIdGetter,
        IUserRoleStaffRepository userRoleStaffRepository)
        : base(userManager)
    {
        _appDbContext = appDbContext;
        _mapper = mapper;
        _logger = logger;
        _coreService = coreService;
        _powerflowManageResellerRepository = powerflowManageResellerRepository;
        _roleManager = roleManager;
        _companyInfoCacheService = companyInfoCacheService;
        _defaultSubscriptionPlanIdGetter = defaultSubscriptionPlanIdGetter;
        _userRoleStaffRepository = userRoleStaffRepository;
    }

    [HttpPost]
    public async Task<ActionResult<List<ResellerBasicInformation>>> GetResellersInfo()
    {
        var user = await GetCurrentValidInternalUser(
            new List<string>()
            {
                ApplicationUserRole.InternalCmsSuperUser,
                ApplicationUserRole.InternalCmsAdmin,
                ApplicationUserRole.InternalCmsCustomerSuccessUser,
                ApplicationUserRole.InternalCmsSalesUser,
                ApplicationUserRole.InternalCmsUser
            });
        if (user == null)
        {
            return Unauthorized();
        }

        var resellers = await _powerflowManageResellerRepository.GetAllResellerInformation();

        if (resellers == null)
        {
            return BadRequest(
                new ResponseViewModel()
                {
                    message = "No Reseller Found"
                });
        }

        return resellers;
    }

    [HttpPost]
    public async Task<ActionResult<ResellerCompanyResponse>> CreateResellerCompany(
        [FromBody]
        ResellerCompanyViewModel createResellerCompanyViewModel)
    {
        var user = await GetCurrentValidInternalUser(
            new List<string>()
            {
                ApplicationUserRole.InternalCmsSuperUser,
                ApplicationUserRole.InternalCmsAdmin,
                ApplicationUserRole.InternalCmsCustomerSuccessUser,
                ApplicationUserRole.InternalCmsSalesUser,
                ApplicationUserRole.InternalCmsUser
            });
        if (user == null)
        {
            return Unauthorized();
        }

        if (!ModelState.IsValid)
        {
            return BadRequest(
                new ResponseViewModel()
                {
                    message = "Company Name and Company Email cannot be empty"
                });
        }

        if (createResellerCompanyViewModel.ResellerStaffViewModel.Username != null)
        {
            var userName =
                await _userManager.FindByNameAsync(createResellerCompanyViewModel.ResellerStaffViewModel.Username);

            if (userName != null)
            {
                return BadRequest(
                    new ResponseViewModel()
                    {
                        message = "Staff Username already exists"
                    });
            }
        }

        if (createResellerCompanyViewModel.ResellerStaffViewModel.Email != null)
        {
            var userEmail =
                await _userManager.FindByEmailAsync(createResellerCompanyViewModel.ResellerStaffViewModel.Email);

            if (userEmail != null)
            {
                return BadRequest(
                    new ResponseViewModel()
                    {
                        message = "Staff Email already exists"
                    });
            }
        }

        await CreateResellerUserRoleIfNotExistAsync();

        var responseWrapper =
            await _powerflowManageResellerRepository.CreateResellerCompany(createResellerCompanyViewModel);

        if (!responseWrapper.IsSuccess)
        {
            return BadRequest(
                new ResponseViewModel()
                {
                    message = responseWrapper.ErrorMsg
                });
        }

        var resellerCompany = (ResellerCompanyResponse) responseWrapper.Data;
        return resellerCompany;
    }

    [HttpPost]
    public async Task<ActionResult<ResellerStaffInformation>> CreateResellerStaff(
        [FromBody]
        ResellerStaffViewModel resellerStaffViewModel)
    {
        var user = await GetCurrentValidInternalUser(
            new List<string>()
            {
                ApplicationUserRole.InternalCmsSuperUser,
                ApplicationUserRole.InternalCmsAdmin,
                ApplicationUserRole.InternalCmsCustomerSuccessUser,
                ApplicationUserRole.InternalCmsSalesUser,
                ApplicationUserRole.InternalCmsUser
            });
        if (user == null)
        {
            return Unauthorized();
        }

        if (!ModelState.IsValid)
        {
            return BadRequest(
                new ResponseViewModel()
                {
                    message = "Invalid Data"
                });
        }

        await CreateResellerUserRoleIfNotExistAsync();

        var userName = await _userManager.FindByNameAsync(resellerStaffViewModel.Username);
        if (userName != null)
        {
            return BadRequest(
                new ResponseViewModel()
                {
                    message = "Staff Username already exists"
                });
        }

        var userEmail = await _userManager.FindByEmailAsync(resellerStaffViewModel.Email);
        if (userEmail != null)
        {
            return BadRequest(
                new ResponseViewModel()
                {
                    message = "Staff Email already exists"
                });
        }

        var responseWrapper = await _powerflowManageResellerRepository.CreateResellerStaff(resellerStaffViewModel);

        if (!responseWrapper.IsSuccess)
        {
            return BadRequest(
                new ResponseViewModel()
                {
                    message = responseWrapper.ErrorMsg
                });
        }

        await _powerflowManageResellerRepository.AddResellerActivityLog(
            new ResellerActivityLog
            {
                CompanyId = resellerStaffViewModel.ResellerCompanyId,
                CreatedByUserId = user.Id,
                Category = ResellerActivityLogCategory.User,
                Action = $"Create New User - {resellerStaffViewModel.Username}"
            });

        var resellerStaff = (ResellerStaffInformation) responseWrapper.Data;

        return resellerStaff;
    }

    [HttpPost]
    public async Task<ActionResult<ResellerCompanyResponse>> UpdateResellerCompany(
        [FromBody]
        UpdateResellerCompanyViewModel resellerCompanyViewModel)
    {
        var user = await GetCurrentValidInternalUser(
            new List<string>()
            {
                ApplicationUserRole.InternalCmsSuperUser,
                ApplicationUserRole.InternalCmsAdmin,
                ApplicationUserRole.InternalCmsCustomerSuccessUser,
                ApplicationUserRole.InternalCmsSalesUser,
                ApplicationUserRole.InternalCmsUser
            });

        if (user == null)
        {
            return Unauthorized();
        }

        if (resellerCompanyViewModel.ResellerCompanyId == null)
        {
            return BadRequest(
                new ResponseViewModel()
                {
                    message = "Reseller Company Id cannot be empty"
                });
        }

        var responseWrapper = await _powerflowManageResellerRepository.UpdateResellerCompany(resellerCompanyViewModel);

        if (!responseWrapper.IsSuccess)
        {
            return BadRequest(
                new ResponseViewModel()
                {
                    message = responseWrapper.ErrorMsg
                });
        }

        var resellerCompany = (ResellerCompanyResponse) responseWrapper.Data;
        return resellerCompany;
    }

    [HttpPost]
    public async Task<ActionResult<Dictionary<string, string>>> UpdateResellerSubscriptionPlanConfig(
        [FromBody]
        UpdateResellerSubscriptionPlanConfigViewModel resellerSubscriptionPlanConfigViewModel)
    {
        var user = await GetCurrentValidInternalUser(
            new List<string>()
            {
                ApplicationUserRole.InternalCmsSuperUser,
                ApplicationUserRole.InternalCmsAdmin,
                ApplicationUserRole.InternalCmsCustomerSuccessUser,
                ApplicationUserRole.InternalCmsSalesUser,
                ApplicationUserRole.InternalCmsUser
            });

        if (user == null)
        {
            return Unauthorized();
        }

        if (resellerSubscriptionPlanConfigViewModel.ResellerCompanyId == null)
        {
            return BadRequest(
                new ResponseViewModel()
                {
                    message = "Reseller Company Id cannot be empty"
                });
        }

        var responseWrapper =
            await _powerflowManageResellerRepository.UpdateResellerSubscriptionPlanConfig(
                resellerSubscriptionPlanConfigViewModel);

        if (!responseWrapper.IsSuccess)
        {
            return BadRequest(
                new ResponseViewModel()
                {
                    message = responseWrapper.ErrorMsg
                });
        }

        var resellerSubscriptionPlanConfig = (Dictionary<string, string>) responseWrapper.Data;
        return resellerSubscriptionPlanConfig;
    }

    [HttpPost]
    public async Task<ActionResult<ResellerCompanyResponse>> SetResellerDiscount(
        [FromBody]
        SetDiscountViewModel setDiscountViewModel)
    {
        if (ModelState.IsValid && User.Identity.IsAuthenticated)
        {
            var user = await GetCurrentValidInternalUser(
                new List<string>()
                {
                    ApplicationUserRole.InternalCmsSuperUser,
                    ApplicationUserRole.InternalCmsAdmin,
                    ApplicationUserRole.InternalCmsCustomerSuccessUser
                });
            if (user == null)
            {
                return Unauthorized();
            }

            var resellerProfile = await _appDbContext.ResellerCompanyProfiles.Include(x => x.Company)
                .FirstOrDefaultAsync(
                    x => x.Id == setDiscountViewModel.ResellerProfileId && x.Company != null &&
                         x.CompanyId == setDiscountViewModel.ResellerCompanyId &&
                         x.Company.CompanyName == setDiscountViewModel.ResellerCompanyName);

            if (resellerProfile == null)
            {
                return BadRequest(
                    new ResponseViewModel()
                    {
                        message = "Reseller Profile Not Found"
                    });
            }

            resellerProfile.ResellerDiscount = setDiscountViewModel.ResellerDiscount;
            await _appDbContext.SaveChangesAsync();

            var resellerCompanyResponse = new ResellerCompanyResponse()
            {
                ResellerCompanyId = resellerProfile.Company.Id,
                ResellerCompanyName = resellerProfile.Company.CompanyName,
                ResellerProfileId = resellerProfile.Id,
                PhoneNumber = resellerProfile.PhoneNumber,
                Balance = resellerProfile.Balance,
                ResellerDiscount = resellerProfile.ResellerDiscount,
                Currency = resellerProfile.Currency,
                BalanceMinimumLimit = resellerProfile.BalanceMinimumLimit
            };

            return resellerCompanyResponse;
        }

        return BadRequest();
    }

    [HttpPost]
    [Authorize(
        Roles = ApplicationUserRole.InternalCmsSuperUser + "," + ApplicationUserRole.InternalCmsAdmin + "," +
                ApplicationUserRole.InternalCmsCustomerSuccessUser)]
    public async Task<ActionResult<ResellerTopUpResponse>> TopUpResellerBalance(
        [FromBody]
        ResellerTopUpViewModel resellerTopUpViewModel)
    {
        if (ModelState.IsValid && User.Identity.IsAuthenticated)
        {
            var user = await _userManager.GetUserAsync(User);
            if (user == null)
            {
                return Unauthorized();
            }

            var responseWrapper = await _powerflowManageResellerRepository.TopUpBalance(user, resellerTopUpViewModel);

            if (!responseWrapper.IsSuccess)
            {
                return BadRequest(
                    new ResponseViewModel()
                    {
                        message = responseWrapper.ErrorMsg
                    });
            }

            var resellerTopUp = (ResellerTopUpResponse) responseWrapper.Data;

            return resellerTopUp;
        }

        return BadRequest();
    }

    [HttpPost]
    public async Task<ActionResult<BillRecordDto>> AddOrExtendResellerCompanyPlan(
        [FromBody]
        ResellerCompanyIdRequest request)
    {
        var user = await GetCurrentValidInternalUser(
            new List<string>()
            {
                ApplicationUserRole.InternalCmsSuperUser,
                ApplicationUserRole.InternalCmsAdmin,
                ApplicationUserRole.InternalCmsCustomerSuccessUser
            });
        if (user == null)
        {
            return Unauthorized();
        }

        var resellerCompany = await _appDbContext.CompanyCompanies.Include(x => x.BillRecords)
            .FirstOrDefaultAsync(x => x.Id == request.CompanyId && x.CompanyType == CompanyType.Reseller);

        if (resellerCompany == null)
        {
            return BadRequest(
                new ResponseViewModel()
                {
                    message = "Reseller Company Not Found"
                });
        }

        if (resellerCompany.BillRecords == null)
        {
            var newBill = new BillRecord()
            {
                SubscriptionPlanId = _defaultSubscriptionPlanIdGetter.GetDefaultEnterprisePlanId(),
                PayAmount = 0,
                CompanyId = resellerCompany.Id,
                Status = BillStatus.Active,
                PaymentStatus = PaymentStatus.FreeOfCharge,
                PeriodStart = DateTime.UtcNow,
                PeriodEnd = DateTime.UtcNow.AddMonths(1),
                PaidByReseller = true,
                quantity = 1
            };
            resellerCompany.BillRecords = new List<BillRecord>();
            resellerCompany.BillRecords.Add(newBill);

            resellerCompany.MaximumContacts = null;
            resellerCompany.MaximumWhAutomatedMessages = null;
            resellerCompany.MaximumAutomations = null;

            await _appDbContext.SaveChangesAsync();
            var billRecordDto = _mapper.Map<BillRecordDto>(newBill);

            return billRecordDto;
        }
        else
        {
            var lastBillRecord = resellerCompany.BillRecords.OrderByDescending(x => x.UpdatedAt)
                .FirstOrDefault(x => ValidSubscriptionPlan.EnterpriseTier.Contains(x.SubscriptionPlanId));
            lastBillRecord.Status = BillStatus.Inactive;
            lastBillRecord.PeriodEnd = DateTime.UtcNow;

            var newBill = new BillRecord()
            {
                SubscriptionPlanId = _defaultSubscriptionPlanIdGetter.GetDefaultEnterprisePlanId(),
                PayAmount = 0,
                CompanyId = resellerCompany.Id,
                Status = BillStatus.Active,
                PaymentStatus = PaymentStatus.FreeOfCharge,
                PeriodStart = DateTime.UtcNow,
                PeriodEnd = DateTime.UtcNow.AddMonths(1),
                PaidByReseller = true,
                quantity = 1
            };

            resellerCompany.BillRecords.Add(newBill);
            await _appDbContext.SaveChangesAsync();
            var billRecordDto = _mapper.Map<BillRecordDto>(newBill);

            return billRecordDto;
        }
    }

    /// <summary>
    /// if refund, then set new bill record start now and refund amount on pro rata basis. Also, the existing plan bill record period end date is set by the request period start date
    /// if not, the existing plan bill record period end date remain unchanged.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    [Authorize(
        Roles = ApplicationUserRole.InternalCmsSuperUser + "," + ApplicationUserRole.InternalCmsAdmin + "," +
                ApplicationUserRole.InternalCmsCustomerSuccessUser)]
    public async Task<ActionResult> SetSubscriptionPlan([FromBody] SetResellerClientSubscriptionPlanRequest request)
    {
        if (ModelState.IsValid && User.Identity.IsAuthenticated)
        {
            var user = await GetCurrentValidInternalUser(
                new List<string>()
                {
                    ApplicationUserRole.InternalCmsSuperUser,
                    ApplicationUserRole.InternalCmsAdmin,
                    ApplicationUserRole.InternalCmsCustomerSuccessUser
                });
            if (user == null)
            {
                return Unauthorized();
            }

            var subscriptionPlan = await _appDbContext.CoreSubscriptionPlans
                .Where(x => x.Id == request.SubscriptionPlanId)
                .FirstOrDefaultAsync();

            if (subscriptionPlan == null)
            {
                return BadRequest(
                    new ResponseViewModel()
                    {
                        message = "Subscription Plan does not exist."
                    });
            }

            if (request.PeriodStart >= request.PeriodEnd)
            {
                return BadRequest(
                    new ResponseViewModel()
                    {
                        message = "Invalid Subscription date range!"
                    });
            }

            if (request.PeriodEnd < DateTime.UtcNow)
            {
                return BadRequest(
                    new ResponseViewModel()
                    {
                        message = "You cannot create a Bill Record that is already expired."
                    });
            }

            var company = await _appDbContext.CompanyCompanies
                .Where(x => x.Id == request.CompanyId && x.CompanyType == CompanyType.ResellerClient)
                .Include(x => x.BillRecords)
                .ThenInclude(x => x.SubscriptionPlan)
                .Include(x => x.ResellerClientCompanyProfile)
                .ThenInclude(x => x.ResellerCompanyProfile)
                .FirstOrDefaultAsync();

            if (company == null)
            {
                return BadRequest(
                    new ResponseViewModel()
                    {
                        message = "Company Not Found"
                    });
            }

            company.IsFreeTrial = false;

            var lastBillRecord = company.BillRecords
                .OrderByDescending(x => x.PeriodEnd)
                .FirstOrDefault(
                    x => ValidSubscriptionPlan.SubscriptionPlan.Contains(x.SubscriptionPlanId) &&
                         x.Status == BillStatus.Active &&
                         ((x.PeriodStart < DateTime.UtcNow && x.PeriodEnd > DateTime.UtcNow) ||
                          (x.PeriodStart > DateTime.UtcNow && x.PeriodEnd > DateTime.UtcNow)));

            var newBillRecord = new BillRecord()
            {
                CompanyId = request.CompanyId,
                SubscriptionPlanId = request.SubscriptionPlanId,
                PaidByReseller = true,
                Status = BillStatus.Active,
                PaymentStatus = PaymentStatus.Paid,
                SubscriptionTier = subscriptionPlan.SubscriptionTier,
                PayAmount = Math.Round(Convert.ToDouble(request.PayAmount), 0),
                currency = company.ResellerClientCompanyProfile.ResellerCompanyProfile.Currency,
                IsCustomized = true,
                quantity = 1
            };

            if (lastBillRecord == null)
            {
                if (company.ResellerClientCompanyProfile.ResellerCompanyProfile.Balance - request.PayAmount <
                    company.ResellerClientCompanyProfile.ResellerCompanyProfile.BalanceMinimumLimit)
                {
                    return BadRequest(
                        new ResponseViewModel()
                        {
                            message =
                                $"Insufficient Balance, Reseller has reached the Minimum Balance Limit. " +
                                $"Balance: {company.ResellerClientCompanyProfile.ResellerCompanyProfile.Balance:0.00}, " +
                                $"Minimum Balance Limit: {company.ResellerClientCompanyProfile.ResellerCompanyProfile.BalanceMinimumLimit:0.00}."
                        });
                }

                newBillRecord.PeriodStart = request.PeriodStart;
                newBillRecord.PeriodEnd = request.PeriodEnd;
                await _appDbContext.CompanyBillRecords.AddAsync(newBillRecord);
                await _appDbContext.SaveChangesAsync();

                var debitLog = new ResellerTransactionLog()
                {
                    ResellerCompanyProfileId = company.ResellerClientCompanyProfile.ResellerCompanyProfile.Id,
                    Amount = Math.Round(Convert.ToDecimal(newBillRecord.PayAmount), 0),
                    Currency = company.ResellerClientCompanyProfile.ResellerCompanyProfile.Currency,
                    BillRecordId = newBillRecord.Id,
                    ClientCompanyId = request.CompanyId,
                    TransactionMode = TransactionMode.Debit,
                    UserIdentityId = user?.Id,
                    TransactionCategory = ResellerTransactionCategory.Subscription,
                    TransactionAction =
                        "Switch " + _powerflowManageResellerRepository.SetTransactionAction(subscriptionPlan),
                    Detail = request.Detail
                };

                company.ResellerClientCompanyProfile.ResellerCompanyProfile.Debited += debitLog.Amount;
                await _appDbContext.ResellerTransactionLogs.AddAsync(debitLog);
                await _appDbContext.SaveChangesAsync();

                await _powerflowManageResellerRepository.AddResellerActivityLog(
                    new ResellerActivityLog
                    {
                        ResellerCompanyProfileId = company.ResellerClientCompanyProfile.ResellerCompanyProfile.Id,
                        CompanyId = request.CompanyId,
                        CreatedByUserId = user?.Id,
                        Category = ResellerActivityLogCategory.Subscription,
                        Action = $"Switch Subscription Plan: {subscriptionPlan.SubscriptionName}"
                    });
            }
            else
            {
                var currentAndExtendedBillRecords = company.BillRecords
                    .OrderByDescending(x => x.PeriodStart)
                    .ThenByDescending(x => x.Id)
                    .Where(
                        x => ValidSubscriptionPlan.SubscriptionPlan.Contains(x.SubscriptionPlanId)
                             && (x.Status == BillStatus.Active || x.Status == BillStatus.Renewed)
                             && x.PeriodEnd > DateTime.UtcNow)
                    .ToList();

                decimal totalRefundAmount = 0;

                if (request.IsRefund)
                {
                    // Cancel and refund the current and extended plan
                    if (currentAndExtendedBillRecords.Any())
                    {
                        var refundResellerTransactionLogs = new List<ResellerTransactionLog>();
                        var refundResellerActivityLogs = new List<ResellerActivityLog>();

                        currentAndExtendedBillRecords.ForEach(
                            billRecord =>
                            {
                                if (billRecord.PeriodEnd >= DateTime.UtcNow.AddDays(1) &&
                                    billRecord.PaymentStatus == PaymentStatus.Paid && billRecord.PayAmount > 0)
                                {
                                    decimal refundAmount = 0;

                                    if (DateTime.UtcNow < billRecord.PeriodStart)
                                    {
                                        refundAmount = Math.Round(Convert.ToDecimal(billRecord.PayAmount), 0);
                                    }
                                    else
                                    {
                                        refundAmount = Math.Round(
                                            Convert.ToDecimal(
                                                ((billRecord.PeriodEnd - billRecord.PeriodStart).TotalDays -
                                                 (DateTime.UtcNow - billRecord.PeriodStart).TotalDays) /
                                                (billRecord.PeriodEnd - billRecord.PeriodStart).TotalDays) *
                                            Convert.ToDecimal(billRecord.PayAmount),
                                            0);
                                    }

                                    var refundTransactionLog = new ResellerTransactionLog()
                                    {
                                        ResellerCompanyProfileId =
                                            company.ResellerClientCompanyProfile.ResellerCompanyProfile.Id,
                                        Amount = refundAmount,
                                        Currency = company.ResellerClientCompanyProfile.ResellerCompanyProfile.Currency,
                                        BillRecordId = billRecord.Id,
                                        ClientCompanyId = request.CompanyId,
                                        TransactionMode = TransactionMode.Refund,
                                        UserIdentityId = user?.Id,
                                        TransactionCategory = ResellerTransactionCategory.Subscription,
                                        TransactionAction = "Refund " +
                                                            _powerflowManageResellerRepository.SetTransactionAction(
                                                                billRecord.SubscriptionPlan),
                                    };

                                    refundResellerTransactionLogs.Add(refundTransactionLog);

                                    var refundActivityLog = new ResellerActivityLog
                                    {
                                        ResellerCompanyProfileId =
                                            company.ResellerClientCompanyProfile.ResellerCompanyProfile.Id,
                                        CompanyId = request.CompanyId,
                                        CreatedByUserId = user?.Id,
                                        Category = ResellerActivityLogCategory.Subscription,
                                        Action =
                                            $"Cancel and Refund Subscription Plan: {subscriptionPlan.SubscriptionName}"
                                    };
                                    refundResellerActivityLogs.Add(refundActivityLog);

                                    totalRefundAmount += refundAmount;

                                    billRecord.Status = BillStatus.Inactive;
                                    billRecord.PeriodEnd = DateTime.UtcNow;
                                }
                                else
                                {
                                    billRecord.Status = BillStatus.Canceled;
                                }

                                billRecord.UpdatedAt = DateTime.UtcNow;
                            });

                        if (refundResellerTransactionLogs.Any())
                        {
                            await _appDbContext.ResellerTransactionLogs.AddRangeAsync(
                                refundResellerTransactionLogs);
                        }

                        if (refundResellerActivityLogs.Any())
                        {
                            await _appDbContext.ResellerActivityLogs.AddRangeAsync(
                                refundResellerActivityLogs);
                        }

                        if (company.ResellerClientCompanyProfile.ResellerCompanyProfile.Balance + totalRefundAmount -
                            Math.Round(Convert.ToDecimal(newBillRecord.PayAmount), 0) <
                            company.ResellerClientCompanyProfile.ResellerCompanyProfile.BalanceMinimumLimit)
                        {
                            return BadRequest(
                                new ResponseViewModel()
                                {
                                    message =
                                        $"Insufficient Balance, Reseller has reached the Minimum Balance Limit. " +
                                        $"Balance: {company.ResellerClientCompanyProfile.ResellerCompanyProfile.Balance:0.00}, " +
                                        $"Minimum Balance Limit: {company.ResellerClientCompanyProfile.ResellerCompanyProfile.BalanceMinimumLimit:0.00}."
                                });
                        }

                        await _appDbContext.SaveChangesAsync();
                    }

                    newBillRecord.PeriodStart = DateTime.UtcNow;
                    newBillRecord.PeriodEnd = request.PeriodEnd;
                }
                else
                {
                    if (currentAndExtendedBillRecords.Any())
                    {
                        currentAndExtendedBillRecords.ForEach(
                            billRecord =>
                            {
                                billRecord.PeriodEnd = DateTime.UtcNow;
                                billRecord.Status = BillStatus.Canceled;
                                billRecord.UpdatedAt = DateTime.UtcNow;
                            });

                        await _appDbContext.SaveChangesAsync();
                    }

                    newBillRecord.PeriodStart = request.PeriodStart;
                    newBillRecord.PeriodEnd = request.PeriodEnd;
                }

                await _appDbContext.CompanyBillRecords.AddAsync(newBillRecord);
                await _appDbContext.SaveChangesAsync();

                var debitLog = new ResellerTransactionLog()
                {
                    ResellerCompanyProfileId = company.ResellerClientCompanyProfile.ResellerCompanyProfile.Id,
                    Amount = Math.Round(Convert.ToDecimal(newBillRecord.PayAmount), 0),
                    Currency = company.ResellerClientCompanyProfile.ResellerCompanyProfile.Currency,
                    BillRecordId = newBillRecord.Id,
                    ClientCompanyId = request.CompanyId,
                    TransactionMode = TransactionMode.Debit,
                    UserIdentityId = user?.Id,
                    TransactionCategory = ResellerTransactionCategory.Subscription,
                    TransactionAction =
                        "Switch " + _powerflowManageResellerRepository.SetTransactionAction(subscriptionPlan),
                    Detail = request.Detail
                };

                company.ResellerClientCompanyProfile.ResellerCompanyProfile.TopUp += totalRefundAmount;
                company.ResellerClientCompanyProfile.ResellerCompanyProfile.Debited += debitLog.Amount;
                await _appDbContext.ResellerTransactionLogs.AddAsync(debitLog);
                await _appDbContext.SaveChangesAsync();

                await _powerflowManageResellerRepository.AddResellerActivityLog(
                    new ResellerActivityLog
                    {
                        ResellerCompanyProfileId = company.ResellerClientCompanyProfile.ResellerCompanyProfile.Id,
                        CompanyId = request.CompanyId,
                        CreatedByUserId = user?.Id,
                        Category = ResellerActivityLogCategory.Subscription,
                        Action = $"Switch Subscription Plan: {subscriptionPlan.SubscriptionName}"
                    });
            }

            await _powerflowManageResellerRepository.SetSubscriptionPlanMaximumUsage(company);

            // Cancel all add-on plan
            var addonBillRecords = company.BillRecords
                .OrderByDescending(x => x.Id)
                .Where(
                    x => ValidSubscriptionPlan.CmsAllAddOn.Contains(x.SubscriptionPlanId)
                         && (x.Status == BillStatus.Active || x.Status == BillStatus.Renewed)
                         && x.PeriodEnd > DateTime.UtcNow)
                .ToList();

            if (addonBillRecords.Any())
            {
                addonBillRecords.ForEach(
                    billRecord =>
                    {
                        billRecord.Status = BillStatus.Canceled;
                    });

                await _appDbContext.SaveChangesAsync();
            }

            // Update Company API Keys limit
            await _appDbContext.CompanyAPIKeys
                .Where(x => x.CompanyId == company.Id)
                .ExecuteUpdateAsync(
                    key =>
                        key
                            .SetProperty(k => k.CallLimit, subscriptionPlan.MaximumAPICall)
                            .SetProperty(k => k.Calls, 0));

            await _appDbContext.SaveChangesAsync();

            // HubSpot
            var lastPayment = await _appDbContext.CompanyBillRecords
                .Where(
                    x => x.CompanyId == request.CompanyId &&
                         ValidSubscriptionPlan.SubscriptionPlan.Contains(x.SubscriptionPlanId))
                .OrderByDescending(x => x.Id)
                .Include(x => x.SubscriptionPlan)
                .FirstOrDefaultAsync();

            if (ValidSubscriptionPlan.PremiumTier.Contains(request.SubscriptionPlanId) &&
                (ValidSubscriptionPlan.ProTier.Contains(lastPayment?.SubscriptionPlanId) ||
                 ValidSubscriptionPlan.FreePlans.Contains(lastPayment?.SubscriptionPlanId)))
            {
                BackgroundJob.Enqueue<IInternalHubSpotService>(
                    x => x.SetCompanyOwnerUpgradeToPremiumPlanFlag(request.CompanyId));
                BackgroundJob.Enqueue<ICoreService>(
                    x => x.SyncCompanyStaffsToSleekFlowContactsBackground(request.CompanyId));
            }

            if (ValidSubscriptionPlan.ProTier.Contains(request.SubscriptionPlanId) &&
                ValidSubscriptionPlan.FreePlans.Contains(lastPayment?.SubscriptionPlanId))
            {
                BackgroundJob.Enqueue<IInternalHubSpotService>(
                    x => x.SetCompanyOwnerUpgradeToProPlanFlag(request.CompanyId));
                BackgroundJob.Enqueue<ICoreService>(
                    x => x.SyncCompanyStaffsToSleekFlowContactsBackground(request.CompanyId));
            }

            BackgroundJob.Enqueue<IInternalHubSpotService>(x => x.SyncCompany(request.CompanyId, null));

            var staff = await _userRoleStaffRepository.GetFirstAdmin(request.CompanyId);

            BackgroundJob.Enqueue<IFlowHubService>(x => x.UpdateFlowHubConfigAsync(request.CompanyId, null));
            BackgroundJob.Enqueue<IIntelligentHubService>(x => x.RefreshIntelligentHubConfigAsync(request.CompanyId));
            BackgroundJob.Enqueue<ICrmHubService>(x => x.RefreshCrmHubConfigAsync(request.CompanyId, staff));

            BackgroundJob.Enqueue<IInternalAnalyticService>(
                x => x.UpdateCompanyAllTimeRevenueAnalyticInfo(
                    new List<string>()
                    {
                        request.CompanyId
                    }));

            await _companyInfoCacheService.RemoveCompanyInfoCache(request.CompanyId);

            return Ok(
                new ResponseViewModel
                {
                    message = "Subscription Plan have updated."
                });
        }

        return BadRequest();
    }

    /// <summary>
    /// refund the bill record at least two days before the period ends. The plan bill record will be set to end now.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    [Authorize(
        Roles = ApplicationUserRole.InternalCmsSuperUser + "," + ApplicationUserRole.InternalCmsAdmin + "," +
                ApplicationUserRole.InternalCmsCustomerSuccessUser)]
    public async Task<ActionResult> RefundBillRecord([FromBody] RefundResellerClientBillRecordRequest request)
    {
        var user = await GetCurrentValidInternalUser(
            new List<string>()
            {
                ApplicationUserRole.InternalCmsSuperUser,
                ApplicationUserRole.InternalCmsAdmin,
                ApplicationUserRole.InternalCmsCustomerSuccessUser
            });
        if (user == null)
        {
            return Unauthorized();
        }

        var resellerProfile = await _appDbContext.ResellerClientCompanyProfiles
            .Where(x => x.ClientCompanyId == request.CompanyId).Include(x => x.ResellerCompanyProfile)
            .Select(x => x.ResellerCompanyProfile).FirstOrDefaultAsync();
        if (resellerProfile == null)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Reseller Profile Not Found for the client company id"
                });
        }

        var billRecordToRefund = await _appDbContext.CompanyBillRecords
            .Include(billRecord => billRecord.SubscriptionPlan)
            .Where(
                x => x.Id == request.BillRecordId
                     && x.CompanyId == request.CompanyId
                     && x.SubscriptionPlanId == request.SubscriptionPlanId)
            .FirstOrDefaultAsync();

        if (billRecordToRefund == null)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Subscription Plan have updated."
                });
        }

        if (billRecordToRefund.Status == BillStatus.Inactive)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Bill record already inactive"
                });
        }

        if (billRecordToRefund.PeriodEnd.AddDays(-1).Date == DateTime.UtcNow.Date ||
            DateTime.UtcNow.Date >= billRecordToRefund.PeriodEnd.Date)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Refund should take place at least two day before the period end"
                });
        }

        var billRecordLog = await _appDbContext.ResellerTransactionLogs.AsNoTracking().FirstOrDefaultAsync(
            x => x.BillRecordId.HasValue && x.BillRecordId.Value == billRecordToRefund.Id &&
                 x.ClientCompanyId == request.CompanyId);
        if (billRecordLog == null)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Bill record not found"
                });
        }

        if (billRecordLog.TransactionMode == TransactionMode.Refund)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Bill Record already refunded"
                });
        }

        decimal refundAmount = 0;

        if (DateTime.UtcNow < billRecordToRefund.PeriodStart) // refund the full amount for the plan not yet start
        {
            refundAmount = Math.Round(Convert.ToDecimal(billRecordToRefund.PayAmount), 0);
            billRecordToRefund.PeriodStart = DateTime.UtcNow;
            billRecordToRefund.PaymentStatus = PaymentStatus.UnPaid;
        }
        else // pro rata refund
        {
            refundAmount = Math.Round(
                Convert.ToDecimal(
                    ((billRecordToRefund.PeriodEnd - billRecordToRefund.PeriodStart).TotalDays -
                     (DateTime.UtcNow - billRecordToRefund.PeriodStart).TotalDays) /
                    (billRecordToRefund.PeriodEnd - billRecordToRefund.PeriodStart).TotalDays) * billRecordLog.Amount,
                0);
        }

        billRecordToRefund.PeriodEnd = DateTime.UtcNow;
        billRecordToRefund.UpdatedAt = DateTime.UtcNow;
        billRecordToRefund.Status = BillStatus.Inactive;

        var refundTransactionLog = new ResellerTransactionLog()
        {
            ResellerCompanyProfileId = resellerProfile.Id,
            Amount = refundAmount,
            Currency = resellerProfile.Currency,
            BillRecordId = billRecordToRefund.Id,
            ClientCompanyId = request.CompanyId,
            TransactionMode = TransactionMode.Refund,
            UserIdentityId = user?.Id,
            TransactionCategory = ResellerTransactionCategory.Subscription,
            TransactionAction = "Refund " +
                                _powerflowManageResellerRepository.SetTransactionAction(
                                    billRecordToRefund.SubscriptionPlan),
        };

        resellerProfile.TopUp += refundAmount;
        await _appDbContext.ResellerTransactionLogs.AddAsync(refundTransactionLog);
        await _appDbContext.SaveChangesAsync();

        await _powerflowManageResellerRepository.AddResellerActivityLog(
            new ResellerActivityLog
            {
                ResellerCompanyProfileId = resellerProfile.CompanyId,
                CompanyId = request.CompanyId,
                CreatedByUserId = user?.Id,
                Category = ResellerActivityLogCategory.Subscription,
                Action = $"Cancel and Refund Subscription Plan: {billRecordToRefund.SubscriptionPlan.SubscriptionName}"
            });

        return Ok(
            new ResponseViewModel()
            {
                message = $"{refundAmount} {resellerProfile.Currency} is refunded to reseller balance"
            });
    }

    [HttpPost]
    [Authorize(
        Roles = ApplicationUserRole.InternalCmsSuperUser + "," + ApplicationUserRole.InternalCmsAdmin + "," +
                ApplicationUserRole.InternalCmsCustomerSuccessUser)]
    public async Task<ActionResult> SetAddOnPlan([FromBody] SetResellerClientAddOnRequest request)
    {
        if (ModelState.IsValid && User.Identity.IsAuthenticated)
        {
            var user = await GetCurrentValidInternalUser(
                new List<string>()
                {
                    ApplicationUserRole.InternalCmsSuperUser,
                    ApplicationUserRole.InternalCmsAdmin,
                    ApplicationUserRole.InternalCmsCustomerSuccessUser
                });
            if (user == null)
            {
                return Unauthorized();
            }

            if (ValidSubscriptionPlan.CmsAllAddOn.All(x => x != request.AddOnPlanId))
            {
                return BadRequest(
                    new ResponseViewModel()
                    {
                        message = "Add On Plan ID is not valid."
                    });
            }

            var addOnPlan = await _appDbContext.CoreSubscriptionPlans
                .FirstOrDefaultAsync(x => x.Id == request.AddOnPlanId);

            if (addOnPlan == null)
            {
                return BadRequest(
                    new ResponseViewModel()
                    {
                        message = "Add On Plan does not exist."
                    });
            }

            if (request.PeriodStart >= request.PeriodEnd)
            {
                return BadRequest(
                    new ResponseViewModel()
                    {
                        message = "Invalid Subscription date range!"
                    });
            }

            if (request.PeriodEnd < DateTime.UtcNow)
            {
                return BadRequest(
                    new ResponseViewModel()
                    {
                        message = "You cannot create a Bill Record that is already expired."
                    });
            }

            var company = await _appDbContext.CompanyCompanies
                .Where(x => x.Id == request.CompanyId && x.CompanyType == CompanyType.ResellerClient)
                .Include(x => x.BillRecords)
                .ThenInclude(x => x.SubscriptionPlan)
                .Include(x => x.ResellerClientCompanyProfile)
                .ThenInclude(x => x.ResellerCompanyProfile)
                .FirstOrDefaultAsync();

            if (company == null)
            {
                return BadRequest(
                    new ResponseViewModel()
                    {
                        message = "Company Not Found"
                    });
            }

            if (company.ResellerClientCompanyProfile.ResellerCompanyProfile.Balance - request.PayAmount <
                company.ResellerClientCompanyProfile.ResellerCompanyProfile.BalanceMinimumLimit)
            {
                return BadRequest(
                    new ResponseViewModel()
                    {
                        message =
                            $"Insufficient Balance, Reseller has reached the Minimum Balance Limit. " +
                            $"Balance: {company.ResellerClientCompanyProfile.ResellerCompanyProfile.Balance:0.00}, " +
                            $"Minimum Balance Limit: {company.ResellerClientCompanyProfile.ResellerCompanyProfile.BalanceMinimumLimit:0.00}."
                    });
            }

            var newBillRecord = new BillRecord()
            {
                SubscriptionPlanId = request.AddOnPlanId,
                PayAmount = Math.Round(Convert.ToDouble(request.PayAmount), 0),
                CompanyId = company.Id,
                Status = BillStatus.Active,
                PaymentStatus = PaymentStatus.Paid,
                PeriodStart = request.PeriodStart,
                PeriodEnd = request.PeriodEnd,
                IsCustomized = true,
                PaidByReseller = true,
                SubscriptionTier = addOnPlan.SubscriptionTier,
                quantity = 1
            };

            await _appDbContext.CompanyBillRecords.AddAsync(newBillRecord);
            await _appDbContext.SaveChangesAsync();

            var debitLog = new ResellerTransactionLog()
            {
                ResellerCompanyProfileId = company.ResellerClientCompanyProfile.ResellerCompanyProfile.Id,
                Amount = Math.Round(Convert.ToDecimal(newBillRecord.PayAmount), 0),
                Currency = company.ResellerClientCompanyProfile.ResellerCompanyProfile.Currency,
                BillRecordId = newBillRecord.Id,
                ClientCompanyId = request.CompanyId,
                TransactionMode = TransactionMode.Debit,
                UserIdentityId = user?.Id,
                TransactionCategory = ResellerTransactionCategory.AddOn,
                TransactionAction = "Add " + _powerflowManageResellerRepository.SetTransactionAction(addOnPlan),
                Detail = request.Detail
            };
            await _appDbContext.ResellerTransactionLogs.AddAsync(debitLog);

            company.ResellerClientCompanyProfile.ResellerCompanyProfile.Debited += debitLog.Amount;

            // Reset maximum usage limit
            if (ValidSubscriptionPlan.AgentPlan
                .Contains(request.AddOnPlanId))
            {
                company.MaximumAgents = 0;
            }
            else if (ValidSubscriptionPlan.AdditionalContactAddOns
                     .Contains(request.AddOnPlanId))
            {
                company.MaximumContacts = null;
            }
            else if (ValidSubscriptionPlan.WhatsAppPhoneNumberAddOns
                     .Contains(request.AddOnPlanId))
            {
                company.MaximumWhatsappInstance = 0;
            }

            await _appDbContext.SaveChangesAsync();

            await _powerflowManageResellerRepository.AddResellerActivityLog(
                new ResellerActivityLog
                {
                    ResellerCompanyProfileId = company.ResellerClientCompanyProfile.ResellerCompanyProfile.Id,
                    CompanyId = request.CompanyId,
                    CreatedByUserId = user?.Id,
                    Category = ResellerActivityLogCategory.Subscription,
                    Action = $"Add On: {addOnPlan.SubscriptionName}"
                });

            // HubSpot
            BackgroundJob.Enqueue<IInternalHubSpotService>(x => x.SyncCompany(request.CompanyId, null));

            await _companyInfoCacheService.RemoveCompanyInfoCache(request.CompanyId);

            return Ok(
                new ResponseViewModel
                {
                    message = "Subscription Plan have updated."
                });
        }

        return BadRequest();
    }

    /// <summary>
    /// Change the add on plan to cancelled bill status, will be inactive when the period ends.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    [Authorize(
        Roles = ApplicationUserRole.InternalCmsSuperUser + "," + ApplicationUserRole.InternalCmsAdmin + "," +
                ApplicationUserRole.InternalCmsCustomerSuccessUser)]
    public async Task<ActionResult> CancelAddOnPlan([FromBody] CancelResellerClientAddOnRequest request)
    {
        var user = await GetCurrentValidInternalUser(
            new List<string>()
            {
                ApplicationUserRole.InternalCmsSuperUser,
                ApplicationUserRole.InternalCmsAdmin,
                ApplicationUserRole.InternalCmsCustomerSuccessUser
            });
        if (user == null)
        {
            return Unauthorized();
        }

        if (!ValidSubscriptionPlan.CmsAllAddOn.Any(x => x == request.AddOnPlanId))
        {
            return BadRequest(
                new ResponseViewModel()
                {
                    message = "Add On Plan does not exist."
                });
        }

        var billRecord = await _appDbContext.CompanyBillRecords.FirstOrDefaultAsync(
            x => x.Id == request.AddOnPlanBillRecordId && x.CompanyId == request.CompanyId &&
                 x.SubscriptionPlanId == request.AddOnPlanId);

        if (billRecord == null)
        {
            return BadRequest(
                new ResponseViewModel()
                {
                    message = "Bill Record does not exist."
                });
        }

        if (billRecord.Status == BillStatus.Canceled || billRecord.Status == BillStatus.Inactive ||
            billRecord.Status == BillStatus.Renewed)
        {
            return BadRequest(
                new ResponseViewModel()
                {
                    message = "Bill Record already cancelled or inactive or renewed."
                });
        }

        billRecord.Status = BillStatus.Canceled;
        billRecord.UpdatedAt = DateTime.UtcNow;

        await _appDbContext.SaveChangesAsync();

        return Ok(
            new ResponseViewModel()
            {
                message = "Add On Plan Bill Record is cancelled successfully"
            });
    }

    [HttpPost]
    [Authorize(Roles = ApplicationUserRole.InternalCmsAdmin)]
    public async Task<ActionResult> MigrationPlanToVersion9()
    {
        var user = await GetCurrentValidInternalUser(
            new List<string>()
            {
                ApplicationUserRole.InternalCmsAdmin
            });
        if (user == null)
        {
            return Unauthorized();
        }

        var resellerClientBillRecords = await _appDbContext.CompanyCompanies.Include(x => x.BillRecords)
            .Where(x => x.CompanyType == CompanyType.ResellerClient).SelectMany(
                x => x.BillRecords.Where(y => y.Status == BillStatus.Active && y.PeriodEnd > DateTime.UtcNow))
            .ToListAsync();

        foreach (var billRecord in resellerClientBillRecords)
        {
            var v9Plan = await _appDbContext.CoreSubscriptionPlans.Where(
                    x => x.Id.Split("v9", StringSplitOptions.None)[1] ==
                        billRecord.SubscriptionPlanId.Split("v8", StringSplitOptions.None)[1] && x.Version == 9)
                .FirstOrDefaultAsync();

            if (v9Plan != null)
            {
                billRecord.SubscriptionPlanId = v9Plan.Id;
            }
        }

        await _appDbContext.SaveChangesAsync();

        return Ok();
    }

    [HttpPost]
    public async Task<ActionResult> DeleteResellerCompany([FromBody] ResellerCompanyIdRequest request)
    {
        // for testing only now
        var user = await GetCurrentValidInternalUser(
            new List<string>()
            {
                ApplicationUserRole.InternalCmsSuperUser, ApplicationUserRole.InternalCmsAdmin
            });
        if (user == null)
        {
            return Unauthorized();
        }

        _logger.LogWarning($"Reseller - removing company data {request.CompanyId} by [{user.Id} {user.DisplayName}]");

        var resellerProfile = await _appDbContext.ResellerCompanyProfiles.Include(x => x.ResellerStaffs)
            .Include(x => x.ResellerTransactionLogs).Include(x => x.ClientCompanyProfiles)
            .Include(x => x.ResellerProfileLogo)
            .FirstOrDefaultAsync(x => x.CompanyId == request.CompanyId);

        if (resellerProfile == null)
        {
            return BadRequest(
                new ResponseViewModel()
                {
                    message = "Reseller Profile Not Found"
                });
        }

        await _coreService.RemoveCompanyData(request.CompanyId);

        _appDbContext.ResellerCompanyProfiles.Remove(resellerProfile);
        _appDbContext.ResellerStaffs.RemoveRange(resellerProfile.ResellerStaffs);
        _appDbContext.ResellerProfileLogos.Remove(resellerProfile.ResellerProfileLogo);

        await _appDbContext.SaveChangesAsync();

        return Ok();
    }

    [HttpPost]
    public async Task<ActionResult> MigrateDirectClientToResellerClient([FromBody] MigrateDirectClientToResellerClientRequest request)
    {
        // for testing only now
        var user = await GetCurrentValidInternalUser([ApplicationUserRole.InternalCmsAdmin]);

        if (user == null)
        {
            return Unauthorized();
        }

        _logger.LogInformation(
            "Reseller - Migrating company {ClientCompanyId} to reseller client of {ResellerCompanyId} by [{UserId} {UserDisplayName}]",
            request.ClientCompanyId,
            request.ResellerCompanyId,
            user.Id,
            user.DisplayName);

        var response = await _powerflowManageResellerRepository.MigrateDirectClientToResellerClient(request.ResellerCompanyId, request.ClientCompanyId);

        if (!response.IsSuccess)
        {
            _logger.LogWarning(
                "Reseller - Migrated company {ClientCompanyId} to reseller client of {ResellerCompanyId} failed by [{UserId} {UserDisplayName}]",
                request.ClientCompanyId,
                request.ResellerCompanyId,
                user.Id,
                user.DisplayName);

            return BadRequest(
                new ResponseViewModel()
                {
                    message = response.ErrorMsg
                });
        }

        _logger.LogInformation(
            "Reseller - Migrated company {ClientCompanyId} to reseller client of {ResellerCompanyId} succeed by [{UserId} {UserDisplayName}]",
            request.ClientCompanyId,
            request.ResellerCompanyId,
            user.Id,
            user.DisplayName);

        return Ok(response.Data.ToString());
    }

    private async Task CreateResellerUserRoleIfNotExistAsync()
    {
        var roles = await _roleManager.Roles.Where(r => r.Name.StartsWith("ResellerPortal")).ToListAsync();

        if (!roles.Select(r => r.Name).Contains(ApplicationUserRole.ResellerPortalUser))
        {
            await _roleManager.CreateAsync(new IdentityRole(ApplicationUserRole.ResellerPortalUser));
        }
    }
}