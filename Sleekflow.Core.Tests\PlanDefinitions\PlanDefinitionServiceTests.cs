using Microsoft.EntityFrameworkCore;
using Travis_backend.Database;
using Travis_backend.SubscriptionPlanDomain.Models;
using Travis_backend.SubscriptionPlanDomain.Models.Common;
using Travis_backend.SubscriptionPlanDomain.Repositories;
using Travis_backend.SubscriptionPlanDomain.Services;

namespace Sleekflow.Core.Tests.PlanDefinitions;

[TestFixture]
public class PlanDefinitionServiceTests
{
    private IPlanDefinitionService _planDefinitionService;
    private IPlanDefinitionRepository _planDefinitionRepository;
    private ApplicationDbContext _applicationDbContext;

    [SetUp]
    public void Setup()
    {
        var connectionString =
            "Server=tcp:sleekflow-core-sql-server-eas-productiond2a4d949.database.windows.net,1433;Initial Catalog=sleekflow-crm-prod;Persist Security Info=False;User ID=sleekflow_read_only;Password=7*4#5Ctg3Nwrestadesw7M54$NfYtt;MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;Pooling=true;Max Pool Size=500;Min Pool Size=100;";
        var options = new DbContextOptionsBuilder<ApplicationDbContext>()
            .UseSqlServer(connectionString)
            .Options;
        _applicationDbContext = new ApplicationDbContext(options);

        _planDefinitionRepository = new PlanDefinitionRepository();
        _planDefinitionService = new PlanDefinitionService(_planDefinitionRepository, _applicationDbContext);
    }

    [Test]
    public async Task GetPlanDefinition_ValidId_ReturnsPlanDefinition_1()
    {
        var usdPlanId = "sleekflow_v8_whatsapp";
        var audPlanId = "sleekflow_v8_whatsapp_aud";
        var cadPlanId = "sleekflow_v8_whatsapp_cad";
        var cnyPlanId = "sleekflow_v8_whatsapp_cny";
        var eurPlanId = "sleekflow_v8_whatsapp_eur";
        var gbpPlanId = "sleekflow_v8_whatsapp_gbp";
        var hkdPlanId = "sleekflow_v8_whatsapp_hkd";
        var idrPlanId = "sleekflow_v8_whatsapp_idr";
        var myrPlanId = "sleekflow_v8_whatsapp_myr";
        var sgdPlanId = "sleekflow_v8_whatsapp_sgd";

        var expectedPlanDefinition = new PlanDefinition(
            new List<Multilingual>
            {
                new ("en", "whatsapp", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new()
                {
                    FeatureId = "whatsapp",
                    Quantity = 1
                }
            },
            new List<Price>
            {
                new ("USD", 29),
                new ("AUD", 39),
                new ("CAD", 29),
                new ("CNY", 189),
                new ("EUR", 29),
                new ("GBP", 19),
                new ("HKD", 229),
                new ("IDR", 409000),
                new ("MYR", 119),
                new ("SGD", 39),
            },
            PlanTypes.AddOns,
            null,
            "v8",
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow);

        var usdResult = await _planDefinitionService.GetPlanDefinitionAsync(usdPlanId);
        var audResult = await _planDefinitionService.GetPlanDefinitionAsync(audPlanId);
        var cadResult = await _planDefinitionService.GetPlanDefinitionAsync(cadPlanId);
        var cnyResult = await _planDefinitionService.GetPlanDefinitionAsync(cnyPlanId);
        var eurResult = await _planDefinitionService.GetPlanDefinitionAsync(eurPlanId);
        var gbpResult = await _planDefinitionService.GetPlanDefinitionAsync(gbpPlanId);
        var hkdResult = await _planDefinitionService.GetPlanDefinitionAsync(hkdPlanId);
        var idrResult = await _planDefinitionService.GetPlanDefinitionAsync(idrPlanId);
        var myrResult = await _planDefinitionService.GetPlanDefinitionAsync(myrPlanId);
        var sgdResult = await _planDefinitionService.GetPlanDefinitionAsync(sgdPlanId);

        Assert.Multiple(() =>
        {
            Assert.That(usdResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(audResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(cadResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(cnyResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(eurResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(gbpResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(hkdResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(idrResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(myrResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(sgdResult, Is.EqualTo(expectedPlanDefinition));
        });
    }

    [Test]
    public async Task GetPlanDefinition_ValidId_ReturnsPlanDefinition_2()
    {
        var usdPlanId = "sleekflow_freemium";

        var expectedPlanDefinition = new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "freemium", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "contacts", Quantity = 100
                },
                new FeatureQuantity
                {
                    FeatureId = "messages", Quantity = 100
                },
                new FeatureQuantity
                {
                    FeatureId = "agents", Quantity = 3
                },
                new FeatureQuantity
                {
                    FeatureId = "campaigns", Quantity = 100
                },
                new FeatureQuantity
                {
                    FeatureId = "automations", Quantity = 5
                },
                new FeatureQuantity
                {
                    FeatureId = "channels", Quantity = 999
                },
                new FeatureQuantity
                {
                    FeatureId = "flow_builder_flow_enrolment", Quantity = 50, MaximumQuantity = 50
                },
                new FeatureQuantity
                {
                    FeatureId = "ai_features_total_usage", Quantity = 2000
                },
            },
            new List<Price>
            {
                new Price("USD", 0),
            },
            PlanTypes.Subscription,
            "free",
            "v0",
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow);

        var usdResult = await _planDefinitionService.GetPlanDefinitionAsync(usdPlanId);

        Assert.Multiple(() =>
        {
            Assert.That(usdResult, Is.EqualTo(expectedPlanDefinition));
        });
    }

    [Test]
    public async Task GetPlanDefinition_ValidId_ReturnsPlanDefinition_3()
    {
        var usdPlanId = "sleekflow_whatsapp";

        var expectedPlanDefinition = new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "whatsapp", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "whatsapp",
                    Quantity = 1
                }
            },
            new List<Price>
            {
                new Price("USD", 39),
            },
            PlanTypes.AddOns,
            null,
            "v0",
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow);

        var usdResult = await _planDefinitionService.GetPlanDefinitionAsync(usdPlanId);

        Assert.Multiple(() =>
        {
            Assert.That(usdResult, Is.EqualTo(expectedPlanDefinition));
        });
    }

    [Test]
    public async Task GetPlanDefinition_ValidId_ReturnsPlanDefinition_4()
    {
        var usdPlanId = "sleekflow_v9_whatsapp_phone_number_usd";
        var aedPlanId = "sleekflow_v9_whatsapp_phone_number_aed";
        var audPlanId = "sleekflow_v9_whatsapp_phone_number_aud";
        var brlPlanId = "sleekflow_v9_whatsapp_phone_number_brl";
        var cadPlanId = "sleekflow_v9_whatsapp_phone_number_cad";
        var cnyPlanId = "sleekflow_v9_whatsapp_phone_number_cny";
        var eurPlanId = "sleekflow_v9_whatsapp_phone_number_eur";
        var gbpPlanId = "sleekflow_v9_whatsapp_phone_number_gbp";
        var hkdPlanId = "sleekflow_v9_whatsapp_phone_number_hkd";
        var idrPlanId = "sleekflow_v9_whatsapp_phone_number_idr";
        var inrPlanId = "sleekflow_v9_whatsapp_phone_number_inr";
        var myrPlanId = "sleekflow_v9_whatsapp_phone_number_myr";
        var sgdPlanId = "sleekflow_v9_whatsapp_phone_number_sgd";

        var expectedPlanDefinition = new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "whatsapp_phone_number", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "whatsapp_phone_number",
                    Quantity = 1
                }
            },
            new List<Price>
            {
                new Price("USD", 15),
                new Price("AED", 55),
                new Price("AUD", 23),
                new Price("BRL", 79),
                new Price("CAD", 19),
                new Price("CNY", 109),
                new Price("EUR", 15),
                new Price("GBP", 13),
                new Price("HKD", 119),
                new Price("IDR", 235000),
                new Price("INR", 698),
                new Price("MYR", 69),
                new Price("SGD", 19),
            },
            PlanTypes.AddOns,
            null,
            "v9",
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow);

        var usdResult = await _planDefinitionService.GetPlanDefinitionAsync(usdPlanId);
        var aedResult = await _planDefinitionService.GetPlanDefinitionAsync(aedPlanId);
        var audResult = await _planDefinitionService.GetPlanDefinitionAsync(audPlanId);
        var brlResult = await _planDefinitionService.GetPlanDefinitionAsync(brlPlanId);
        var cadResult = await _planDefinitionService.GetPlanDefinitionAsync(cadPlanId);
        var cnyResult = await _planDefinitionService.GetPlanDefinitionAsync(cnyPlanId);
        var eurResult = await _planDefinitionService.GetPlanDefinitionAsync(eurPlanId);
        var gbpResult = await _planDefinitionService.GetPlanDefinitionAsync(gbpPlanId);
        var hkdResult = await _planDefinitionService.GetPlanDefinitionAsync(hkdPlanId);
        var idrResult = await _planDefinitionService.GetPlanDefinitionAsync(idrPlanId);
        var inrResult = await _planDefinitionService.GetPlanDefinitionAsync(inrPlanId);
        var myrResult = await _planDefinitionService.GetPlanDefinitionAsync(myrPlanId);
        var sgdResult = await _planDefinitionService.GetPlanDefinitionAsync(sgdPlanId);

        Assert.Multiple(() =>
        {
            Assert.That(usdResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(aedResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(audResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(brlResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(cadResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(cnyResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(eurResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(gbpResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(hkdResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(idrResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(inrResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(myrResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(sgdResult, Is.EqualTo(expectedPlanDefinition));
        });
    }

    [Test]
    public async Task GetPlanDefinition_ValidId_ReturnsPlanDefinition_5()
    {
        var sgdPlanId = "sleekflow_v9_whatsapp_phone_number_yearly_sgd";
        var idrPlanId = "sleekflow_v9_whatsapp_phone_number_yearly_idr";
        var brlPlanId = "sleekflow_v9_whatsapp_phone_number_yearly_brl";
        var usdPlanId = "sleekflow_v9_whatsapp_phone_number_yearly_usd";

        var expectedPlanDefinition = new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "whatsapp_phone_number_yearly", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "whatsapp_phone_number",
                    Quantity = 1
                }
            },
            new List<Price>
            {
                new Price("SGD", 228),
                new Price("IDR", 2820000),
                new Price("BRL", 948),
                new Price("USD", 180),
                new Price("AED", 660),
                new Price("AUD", 276),
                new Price("CAD", 228),
                new Price("CNY", 1308),
                new Price("EUR", 180),
                new Price("GBP", 156),
                new Price("HKD", 1428),
                new Price("INR", 7698),
                new Price("MYR", 828),
            },
            PlanTypes.AddOns,
            null,
            "v9",
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow);

        var usdResult = await _planDefinitionService.GetPlanDefinitionAsync(usdPlanId);
        var sgdResult = await _planDefinitionService.GetPlanDefinitionAsync(sgdPlanId);
        var idrResult = await _planDefinitionService.GetPlanDefinitionAsync(idrPlanId);
        var brlResult = await _planDefinitionService.GetPlanDefinitionAsync(brlPlanId);

        Assert.Multiple(() =>
        {
            Assert.That(usdResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(sgdResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(idrResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(brlResult, Is.EqualTo(expectedPlanDefinition));
        });
    }

    [Test]
    public async Task GetPlanDefinition_ValidId_ReturnsPlanDefinition_6()
    {
        var usdPlanId = "sleekflow_v9_shopify_integration";
        var aedPlanId = "sleekflow_v9_shopify_integration_aed";
        var audPlanId = "sleekflow_v9_shopify_integration_aud";
        var brlPlanId = "sleekflow_v9_shopify_integration_brl";
        var cadPlanId = "sleekflow_v9_shopify_integration_cad";
        var cnyPlanId = "sleekflow_v9_shopify_integration_cny";
        var eurPlanId = "sleekflow_v9_shopify_integration_eur";
        var gbpPlanId = "sleekflow_v9_shopify_integration_gbp";
        var hkdPlanId = "sleekflow_v9_shopify_integration_hkd";
        var idrPlanId = "sleekflow_v9_shopify_integration_idr";
        var inrPlanId = "sleekflow_v9_shopify_integration_inr";
        var myrPlanId = "sleekflow_v9_shopify_integration_myr";
        var sgdPlanId = "sleekflow_v9_shopify_integration_sgd";

        var expectedPlanDefinition = new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "shopify_integration", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "shopify",
                    Quantity = 1
                }
            },
            new List<Price>
            {
                new Price("USD", 49),
                new Price("AED", 179),
                new Price("AUD", 89),
                new Price("BRL", 269),
                new Price("CAD", 79),
                new Price("CNY", 369),
                new Price("EUR", 49),
                new Price("GBP", 49),
                new Price("HKD", 399),
                new Price("IDR", 699000),
                new Price("INR", 2098),
                new Price("MYR", 209),
                new Price("SGD", 69),
            },
            PlanTypes.AddOns,
            null,
            "v9",
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow);

        var usdResult = await _planDefinitionService.GetPlanDefinitionAsync(usdPlanId);
        var aedResult = await _planDefinitionService.GetPlanDefinitionAsync(aedPlanId);
        var audResult = await _planDefinitionService.GetPlanDefinitionAsync(audPlanId);
        var brlResult = await _planDefinitionService.GetPlanDefinitionAsync(brlPlanId);
        var cadResult = await _planDefinitionService.GetPlanDefinitionAsync(cadPlanId);
        var cnyResult = await _planDefinitionService.GetPlanDefinitionAsync(cnyPlanId);
        var eurResult = await _planDefinitionService.GetPlanDefinitionAsync(eurPlanId);
        var gbpResult = await _planDefinitionService.GetPlanDefinitionAsync(gbpPlanId);
        var hkdResult = await _planDefinitionService.GetPlanDefinitionAsync(hkdPlanId);
        var idrResult = await _planDefinitionService.GetPlanDefinitionAsync(idrPlanId);
        var inrResult = await _planDefinitionService.GetPlanDefinitionAsync(inrPlanId);
        var myrResult = await _planDefinitionService.GetPlanDefinitionAsync(myrPlanId);
        var sgdResult = await _planDefinitionService.GetPlanDefinitionAsync(sgdPlanId);

        Assert.Multiple(() =>
        {
            Assert.That(usdResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(aedResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(audResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(brlResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(cadResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(cnyResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(eurResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(gbpResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(hkdResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(idrResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(inrResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(myrResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(sgdResult, Is.EqualTo(expectedPlanDefinition));
        });
    }

    [Test]
    public async Task GetPlanDefinition_ValidId_ReturnsPlanDefinition_7()
    {
        var usdPlanId = "sleekflow_v8_addon_shopify_monthly";

        var expectedPlanDefinition = new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "addon_shopify_monthly", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "shopify",
                    Quantity = 1
                }
            },
            new List<Price>
            {
                new Price("USD", 49),
            },
            PlanTypes.AddOns,
            null,
            "v8",
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow);

        var usdResult = await _planDefinitionService.GetPlanDefinitionAsync(usdPlanId);

        Assert.Multiple(() =>
        {
            Assert.That(usdResult, Is.EqualTo(expectedPlanDefinition));
        });
    }

    [Test]
    public async Task GetPlanDefinition_ValidId_ReturnsPlanDefinition_8()
    {
        var usdPlanId = "sleekflow_v8_addon_shopify_yearly";

        var expectedPlanDefinition = new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "addon_shopify_yearly", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "shopify",
                    Quantity = 1
                }
            },
            new List<Price>
            {
                new Price("USD", 588),
            },
            PlanTypes.AddOns,
            null,
            "v8",
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow);

        var usdResult = await _planDefinitionService.GetPlanDefinitionAsync(usdPlanId);

        Assert.Multiple(() =>
        {
            Assert.That(usdResult, Is.EqualTo(expectedPlanDefinition));
        });
    }

    [Test]
    public async Task GetPlanDefinition_ValidId_ReturnsPlanDefinition_9()
    {
        var usdPlanId = "sleekflow_additional_2000_contact";

        var expectedPlanDefinition = new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "additional_2000_contact", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "contacts",
                    Quantity = 2000
                }
            },
            new List<Price>
            {
                new Price("USD", 49),
            },
            PlanTypes.AddOns,
            null,
            "v0",
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow);

        var usdResult = await _planDefinitionService.GetPlanDefinitionAsync(usdPlanId);

        Assert.Multiple(() =>
        {
            Assert.That(usdResult, Is.EqualTo(expectedPlanDefinition));
        });
    }

    [Test]
    public async Task GetPlanDefinition_ValidId_ReturnsPlanDefinition_10()
    {
        var usdPlanId = "sleekflow_v8_additional_2000_contact";
        var audPlanId = "sleekflow_v8_additional_2000_contact_aud";
        var cadPlanId = "sleekflow_v8_additional_2000_contact_cad";
        var cnyPlanId = "sleekflow_v8_additional_2000_contact_cny";
        var eurPlanId = "sleekflow_v8_additional_2000_contact_eur";
        var gbpPlanId = "sleekflow_v8_additional_2000_contact_gbp";
        var hkdPlanId = "sleekflow_v8_additional_2000_contact_hkd";
        var idrPlanId = "sleekflow_v8_additional_2000_contact_idr";
        var myrPlanId = "sleekflow_v8_additional_2000_contact_myr";
        var sgdPlanId = "sleekflow_v8_additional_2000_contact_sgd";

        var expectedPlanDefinition = new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "additional_2000_contact", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "contacts",
                    Quantity = 2000
                }
            },
            new List<Price>
            {
                new Price("USD", 49),
                new Price("HKD", 399),
                new Price("SGD", 69),
                new Price("CNY", 319),
                new Price("MYR", 209),
                new Price("IDR", 699000),
                new Price("EUR", 49),
                new Price("GBP", 39),
                new Price("CAD", 79),
                new Price("AUD", 89),
            },
            PlanTypes.AddOns,
            null,
            "v8",
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow);

        var usdResult = await _planDefinitionService.GetPlanDefinitionAsync(usdPlanId);
        var hkdResult = await _planDefinitionService.GetPlanDefinitionAsync(hkdPlanId);
        var sgdResult = await _planDefinitionService.GetPlanDefinitionAsync(sgdPlanId);
        var cnyResult = await _planDefinitionService.GetPlanDefinitionAsync(cnyPlanId);
        var myrResult = await _planDefinitionService.GetPlanDefinitionAsync(myrPlanId);
        var idrResult = await _planDefinitionService.GetPlanDefinitionAsync(idrPlanId);
        var eurResult = await _planDefinitionService.GetPlanDefinitionAsync(eurPlanId);
        var gbpResult = await _planDefinitionService.GetPlanDefinitionAsync(gbpPlanId);
        var cadResult = await _planDefinitionService.GetPlanDefinitionAsync(audPlanId);
        var audResult = await _planDefinitionService.GetPlanDefinitionAsync(cadPlanId);

        Assert.Multiple(() =>
        {
            Assert.That(usdResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(hkdResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(sgdResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(cnyResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(myrResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(idrResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(eurResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(gbpResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(cadResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(audResult, Is.EqualTo(expectedPlanDefinition));
        });
    }

    [Test]
    public async Task GetPlanDefinition_ValidId_ReturnsPlanDefinition_11()
    {
        var usdPlanId = "sleekflow_v9_additional_2000_contact";
        var aedPlanId = "sleekflow_v9_additional_2000_contact_aed";
        var audPlanId = "sleekflow_v9_additional_2000_contact_aud";
        var cadPlanId = "sleekflow_v9_additional_2000_contact_cad";
        var cnyPlanId = "sleekflow_v9_additional_2000_contact_cny";
        var eurPlanId = "sleekflow_v9_additional_2000_contact_eur";
        var gbpPlanId = "sleekflow_v9_additional_2000_contact_gbp";
        var hkdPlanId = "sleekflow_v9_additional_2000_contact_hkd";
        var sgdPlanId = "sleekflow_v9_additional_2000_contact_sgd";

        var expectedPlanDefinition = new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "additional_2000_contact", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "contacts",
                    Quantity = 2000
                }
            },
            new List<Price>
            {
                new Price("USD", 49),
                new Price("AED", 179),
                new Price("AUD", 89),
                new Price("CAD", 79),
                new Price("CNY", 369),
                new Price("EUR", 49),
                new Price("GBP", 49),
                new Price("HKD", 399),
                new Price("SGD", 69),
            },
            PlanTypes.AddOns,
            null,
            "v9",
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow);

        var usdResult = await _planDefinitionService.GetPlanDefinitionAsync(usdPlanId);
        var hkdResult = await _planDefinitionService.GetPlanDefinitionAsync(hkdPlanId);
        var sgdResult = await _planDefinitionService.GetPlanDefinitionAsync(sgdPlanId);
        var cnyResult = await _planDefinitionService.GetPlanDefinitionAsync(cnyPlanId);
        var eurResult = await _planDefinitionService.GetPlanDefinitionAsync(eurPlanId);
        var gbpResult = await _planDefinitionService.GetPlanDefinitionAsync(gbpPlanId);
        var cadResult = await _planDefinitionService.GetPlanDefinitionAsync(audPlanId);
        var audResult = await _planDefinitionService.GetPlanDefinitionAsync(cadPlanId);
        var aedResult = await _planDefinitionService.GetPlanDefinitionAsync(aedPlanId);

        Assert.Multiple(() =>
        {
            Assert.That(usdResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(hkdResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(sgdResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(cnyResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(aedResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(eurResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(gbpResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(cadResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(audResult, Is.EqualTo(expectedPlanDefinition));
        });
    }

    [Test]
    public async Task GetPlanDefinition_ValidId_ReturnsPlanDefinition_12()
    {
        var brlPlanId = "sleekflow_v9_additional_5000_contact_brl";
        var idrPlanId = "sleekflow_v9_additional_5000_contact_idr";
        var inrPlanId = "sleekflow_v9_additional_5000_contact_inr";
        var myrPlanId = "sleekflow_v9_additional_5000_contact_myr";

        var expectedPlanDefinition = new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "additional_5000_contact", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "contacts",
                    Quantity = 5000
                }
            },
            new List<Price>
            {
                new Price("BRL", 269),
                new Price("IDR", 699000),
                new Price("INR", 698),
                new Price("MYR", 209),
            },
            PlanTypes.AddOns,
            null,
            "v9",
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow);

        var brlPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(brlPlanId);
        var idrPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(idrPlanId);
        var inrPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(inrPlanId);
        var myrPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(myrPlanId);

        Assert.Multiple(() =>
        {
            Assert.That(brlPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(idrPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(inrPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(myrPlanResult, Is.EqualTo(expectedPlanDefinition));
        });
    }

    [Test]
    public async Task GetPlanDefinition_ValidId_ReturnsPlanDefinition_13()
    {
        var usdPlanId = "sleekflow_v9_unlimited_contact";
        var aedPlanId = "sleekflow_v9_unlimited_contact_aed";
        var audPlanId = "sleekflow_v9_unlimited_contact_aud";
        var brlPlanId = "sleekflow_v9_unlimited_contact_brl";
        var cadPlanId = "sleekflow_v9_unlimited_contact_cad";
        var cnyPlanId = "sleekflow_v9_unlimited_contact_cny";
        var eurPlanId = "sleekflow_v9_unlimited_contact_eur";
        var gbpPlanId = "sleekflow_v9_unlimited_contact_gbp";
        var hkdPlanId = "sleekflow_v9_unlimited_contact_hkd";
        var idrPlanId = "sleekflow_v9_unlimited_contact_idr";
        var myrPlanId = "sleekflow_v9_unlimited_contact_myr";
        var sgdPlanId = "sleekflow_v9_unlimited_contact_sgd";

        var expectedPlanDefinition = new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "unlimited_contact", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "contacts",
                    Quantity = 50000000
                }
            },
            new List<Price>
            {
                new Price("USD", 499),
                new Price("AED", 1799),
                new Price("AUD", 739),
                new Price("BRL", 2699),
                new Price("CAD", 649),
                new Price("CNY", 3399),
                new Price("EUR", 499),
                new Price("GBP", 419),
                new Price("HKD", 3999),
                new Price("IDR", 7400000),
                new Price("MYR", 2199),
                new Price("SGD", 699),
            },
            PlanTypes.AddOns,
            null,
            "v9",
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow);

        var usdPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(usdPlanId);
        var aedPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(aedPlanId);
        var audPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(audPlanId);
        var brlPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(brlPlanId);
        var cadPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(cadPlanId);
        var cnyPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(cnyPlanId);
        var eurPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(eurPlanId);
        var gbpPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(gbpPlanId);
        var hkdPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(hkdPlanId);
        var idrPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(idrPlanId);
        var myrPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(myrPlanId);
        var sgdPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(sgdPlanId);

        Assert.Multiple(() =>
        {
            Assert.That(usdPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(aedPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(audPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(brlPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(cadPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(cnyPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(eurPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(gbpPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(hkdPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(idrPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(myrPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(sgdPlanResult, Is.EqualTo(expectedPlanDefinition));
        });
    }

    [Test]
    public async Task GetPlanDefinition_ValidId_ReturnsPlanDefinition_14()
    {
        var usdPlanId = "sleekflow_sensitive_data_masking";

        var expectedPlanDefinition = new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "sensitive_data_masking", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "sensitive_data_masking",
                    Quantity = 1
                }
            },
            new List<Price>
            {
                new Price("USD", 99),
            },
            PlanTypes.AddOns,
            null,
            "v0",
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow);

        var usdPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(usdPlanId);

        Assert.Multiple(() =>
        {
            Assert.That(usdPlanResult, Is.EqualTo(expectedPlanDefinition));
        });
    }

    [Test]
    public async Task GetPlanDefinition_ValidId_ReturnsPlanDefinition_15()
    {
        var usdPlanId = "sleekflow_v8_sensitive_data_masking";
        var audPlanId = "sleekflow_v8_sensitive_data_masking_aud";
        var cadPlanId = "sleekflow_v8_sensitive_data_masking_cad";
        var cnyPlanId = "sleekflow_v8_sensitive_data_masking_cny";
        var eurPlanId = "sleekflow_v8_sensitive_data_masking_eur";
        var gbpPlanId = "sleekflow_v8_sensitive_data_masking_gbp";
        var hkdPlanId = "sleekflow_v8_sensitive_data_masking_hkd";
        var idrPlanId = "sleekflow_v8_sensitive_data_masking_idr";
        var myrPlanId = "sleekflow_v8_sensitive_data_masking_myr";
        var sgdPlanId = "sleekflow_v8_sensitive_data_masking_sgd";

        var expectedPlanDefinition = new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "sensitive_data_masking", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "sensitive_data_masking",
                    Quantity = 1
                }
            },
            new List<Price>
            {
                new Price("USD", 99),
                new Price("AUD", 139),
                new Price("CAD", 129),
                new Price("CNY", 629),
                new Price("EUR", 89),
                new Price("GBP", 79),
                new Price("HKD", 799),
                new Price("IDR", 1390000),
                new Price("MYR", 419),
                new Price("SGD", 139),
            },
            PlanTypes.AddOns,
            null,
            "v8",
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow);

        var usdPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(usdPlanId);
        var audPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(audPlanId);
        var cadPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(cadPlanId);
        var cnyPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(cnyPlanId);
        var eurPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(eurPlanId);
        var gbpPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(gbpPlanId);
        var hkdPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(hkdPlanId);
        var idrPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(idrPlanId);
        var myrPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(myrPlanId);
        var sgdPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(sgdPlanId);

        Assert.Multiple(() =>
        {
            Assert.That(usdPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(audPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(cadPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(cnyPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(eurPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(gbpPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(hkdPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(idrPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(myrPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(sgdPlanResult, Is.EqualTo(expectedPlanDefinition));
        });
    }

    [Test]
    public async Task GetPlanDefinition_ValidId_ReturnsPlanDefinition_16()
    {
        var usdPlanId = "sleekflow_v9_sensitive_data_masking";
        var aedPlanId = "sleekflow_v9_sensitive_data_masking_aed";
        var audPlanId = "sleekflow_v9_sensitive_data_masking_aud";
        var brlPlanId = "sleekflow_v9_sensitive_data_masking_brl";
        var cadPlanId = "sleekflow_v9_sensitive_data_masking_cad";
        var cnyPlanId = "sleekflow_v9_sensitive_data_masking_cny";
        var eurPlanId = "sleekflow_v9_sensitive_data_masking_eur";
        var gbpPlanId = "sleekflow_v9_sensitive_data_masking_gbp";
        var hkdPlanId = "sleekflow_v9_sensitive_data_masking_hkd";
        var idrPlanId = "sleekflow_v9_sensitive_data_masking_idr";
        var inrPlanId = "sleekflow_v9_sensitive_data_masking_inr";
        var myrPlanId = "sleekflow_v9_sensitive_data_masking_myr";
        var sgdPlanId = "sleekflow_v9_sensitive_data_masking_sgd";

        var expectedPlanDefinition = new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "sensitive_data_masking", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "sensitive_data_masking",
                    Quantity = 1
                }
            },
            new List<Price>
            {
                new Price("USD", 99),
                new Price("AED", 369),
                new Price("AUD", 149),
                new Price("BRL", 539),
                new Price("CAD", 129),
                new Price("CNY", 709),
                new Price("EUR", 99),
                new Price("GBP", 89),
                new Price("HKD", 779),
                new Price("IDR", 1390000),
                new Price("INR", 4198),
                new Price("MYR", 439),
                new Price("SGD", 139),
            },
            PlanTypes.AddOns,
            null,
            "v9",
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow);

        var usdPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(usdPlanId);
        var aedPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(aedPlanId);
        var audPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(audPlanId);
        var brlPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(brlPlanId);
        var cadPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(cadPlanId);
        var cnyPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(cnyPlanId);
        var eurPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(eurPlanId);
        var gbpPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(gbpPlanId);
        var hkdPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(hkdPlanId);
        var idrPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(idrPlanId);
        var inrPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(inrPlanId);
        var myrPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(myrPlanId);
        var sgdPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(sgdPlanId);

        Assert.Multiple(() =>
        {
            Assert.That(usdPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(audPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(cadPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(cnyPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(eurPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(gbpPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(hkdPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(idrPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(myrPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(sgdPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(aedPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(brlPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(inrPlanResult, Is.EqualTo(expectedPlanDefinition));
        });
    }

    [Test]
    public async Task GetPlanDefinition_ValidId_ReturnsPlanDefinition_17()
    {
        var usdPlanId = "sleekflow_v8_consultation";
        var audPlanId = "sleekflow_v8_consultation_aud";
        var cadPlanId = "sleekflow_v8_consultation_cad";
        var cnyPlanId = "sleekflow_v8_consultation_cny";
        var eurPlanId = "sleekflow_v8_consultation_eur";
        var gbpPlanId = "sleekflow_v8_consultation_gbp";
        var hkdPlanId = "sleekflow_v8_consultation_hkd";
        var idrPlanId = "sleekflow_v8_consultation_idr";
        var myrPlanId = "sleekflow_v8_consultation_myr";
        var sgdPlanId = "sleekflow_v8_consultation_sgd";

        var expectedPlanDefinition = new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "consultation", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "consultation",
                    Quantity = 1
                }
            },
            new List<Price>
            {
                new Price("USD", 299),
                new Price("AUD", 399),
                new Price("CAD", 379),
                new Price("CNY", 1899),
                new Price("EUR", 269),
                new Price("GBP", 229),
                new Price("HKD", 2299),
                new Price("IDR", 4200000),
                new Price("MYR", 1249),
                new Price("SGD", 399),
            },
            PlanTypes.AddOns,
            null,
            "v8",
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow);

        var usdPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(usdPlanId);
        var audPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(audPlanId);
        var cadPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(cadPlanId);
        var cnyPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(cnyPlanId);
        var eurPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(eurPlanId);
        var gbpPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(gbpPlanId);
        var hkdPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(hkdPlanId);
        var idrPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(idrPlanId);
        var myrPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(myrPlanId);
        var sgdPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(sgdPlanId);

        Assert.Multiple(() =>
        {
            Assert.That(usdPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(audPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(cadPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(cnyPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(eurPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(gbpPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(hkdPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(idrPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(myrPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(sgdPlanResult, Is.EqualTo(expectedPlanDefinition));
        });
    }

    [Test]
    public async Task GetPlanDefinition_ValidId_ReturnsPlanDefinition_18()
    {
        var usdPlanId = "sleekflow_v8_support_and_consultation";
        var audPlanId = "sleekflow_v8_support_and_consultation_aud";
        var cadPlanId = "sleekflow_v8_support_and_consultation_cad";
        var cnyPlanId = "sleekflow_v8_support_and_consultation_cny";
        var eurPlanId = "sleekflow_v8_support_and_consultation_eur";
        var gbpPlanId = "sleekflow_v8_support_and_consultation_gbp";
        var hkdPlanId = "sleekflow_v8_support_and_consultation_hkd";
        var idrPlanId = "sleekflow_v8_support_and_consultation_idr";
        var myrPlanId = "sleekflow_v8_support_and_consultation_myr";
        var sgdPlanId = "sleekflow_v8_support_and_consultation_sgd";

        var expectedPlanDefinition = new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "support_and_consultation", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "support_and_consultation",
                    Quantity = 1
                }
            },
            new List<Price>
            {
                new Price("USD", 499),
                new Price("AUD", 699),
                new Price("CAD", 639),
                new Price("CNY", 3199),
                new Price("EUR", 449),
                new Price("GBP", 379),
                new Price("HKD", 3899),
                new Price("IDR", 6900000),
                new Price("MYR", 1999),
                new Price("SGD", 649),
            },
            PlanTypes.AddOns,
            null,
            "v8",
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow);

        var usdPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(usdPlanId);
        var audPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(audPlanId);
        var cadPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(cadPlanId);
        var cnyPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(cnyPlanId);
        var eurPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(eurPlanId);
        var gbpPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(gbpPlanId);
        var hkdPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(hkdPlanId);
        var idrPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(idrPlanId);
        var myrPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(myrPlanId);
        var sgdPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(sgdPlanId);

        Assert.Multiple(() =>
        {
            Assert.That(usdPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(audPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(cadPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(cnyPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(eurPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(gbpPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(hkdPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(idrPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(myrPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(sgdPlanResult, Is.EqualTo(expectedPlanDefinition));
        });
    }

    [Test]
    public async Task GetPlanDefinition_ValidId_ReturnsPlanDefinition_19()
    {
        var usdPlanId = "sleekflow_v8_priority_support";
        var audPlanId = "sleekflow_v8_priority_support_aud";
        var cadPlanId = "sleekflow_v8_priority_support_cad";
        var cnyPlanId = "sleekflow_v8_priority_support_cny";
        var eurPlanId = "sleekflow_v8_priority_support_eur";
        var gbpPlanId = "sleekflow_v8_priority_support_gbp";
        var hkdPlanId = "sleekflow_v8_priority_support_hkd";
        var idrPlanId = "sleekflow_v8_priority_support_idr";
        var myrPlanId = "sleekflow_v8_priority_support_myr";
        var sgdPlanId = "sleekflow_v8_priority_support_sgd";

        var expectedPlanDefinition = new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "priority_support", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "priority_support",
                    Quantity = 1
                }
            },
            new List<Price>
            {
                new Price("USD", 299),
                new Price("AUD", 399),
                new Price("CAD", 379),
                new Price("CNY", 1899),
                new Price("EUR", 269),
                new Price("GBP", 229),
                new Price("HKD", 2299),
                new Price("IDR", 4200000),
                new Price("MYR", 1249),
                new Price("SGD", 399),
            },
            PlanTypes.AddOns,
            null,
            "v8",
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow);

        var usdPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(usdPlanId);
        var audPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(audPlanId);
        var cadPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(cadPlanId);
        var cnyPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(cnyPlanId);
        var eurPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(eurPlanId);
        var gbpPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(gbpPlanId);
        var hkdPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(hkdPlanId);
        var idrPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(idrPlanId);
        var myrPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(myrPlanId);
        var sgdPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(sgdPlanId);

        Assert.Multiple(() =>
        {
            Assert.That(usdPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(audPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(cadPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(cnyPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(eurPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(gbpPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(hkdPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(idrPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(myrPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(sgdPlanResult, Is.EqualTo(expectedPlanDefinition));
        });
    }

    [Test]
    public async Task GetPlanDefinition_ValidId_ReturnsPlanDefinition_20()
    {
        var usdPlanId = "sleekflow_v9_priority_support_yearly";
        var aedPlanId = "sleekflow_v9_priority_support_yearly_aed";
        var audPlanId = "sleekflow_v9_priority_support_yearly_aud";
        var brlPlanId = "sleekflow_v9_priority_support_yearly_brl";
        var cadPlanId = "sleekflow_v9_priority_support_yearly_cad";
        var cnyPlanId = "sleekflow_v9_priority_support_yearly_cny";
        var eurPlanId = "sleekflow_v9_priority_support_yearly_eur";
        var gbpPlanId = "sleekflow_v9_priority_support_yearly_gbp";
        var hkdPlanId = "sleekflow_v9_priority_support_yearly_hkd";
        var inrPlanId = "sleekflow_v9_priority_support_yearly_inr";
        var idrPlanId = "sleekflow_v9_priority_support_yearly_idr";
        var myrPlanId = "sleekflow_v9_priority_support_yearly_myr";
        var sgdPlanId = "sleekflow_v9_priority_support_yearly_sgd";

        var expectedPlanDefinition = new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "priority_support_yearly", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "priority_support",
                    Quantity = 1
                }
            },
            new List<Price>
            {
                new Price("USD", 1188),
                new Price("AED", 4428),
                new Price("AUD", 1788),
                new Price("BRL", 6468),
                new Price("CAD", 1548),
                new Price("CNY", 8508),
                new Price("EUR", 1188),
                new Price("GBP", 1068),
                new Price("HKD", 9348),
                new Price("IDR", 16680000),
                new Price("INR", 4199),
                new Price("MYR", 5268),
                new Price("SGD", 1668),
            },
            PlanTypes.AddOns,
            null,
            "v9",
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow);

        var usdPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(usdPlanId);
        var audPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(audPlanId);
        var cadPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(cadPlanId);
        var cnyPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(cnyPlanId);
        var eurPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(eurPlanId);
        var gbpPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(gbpPlanId);
        var hkdPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(hkdPlanId);
        var idrPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(idrPlanId);
        var inrPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(inrPlanId);
        var myrPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(myrPlanId);
        var sgdPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(sgdPlanId);
        var aedPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(aedPlanId);
        var brlPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(brlPlanId);

        Assert.Multiple(() =>
        {
            Assert.That(usdPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(audPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(cadPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(cnyPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(eurPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(gbpPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(hkdPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(idrPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(inrPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(myrPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(sgdPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(aedPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(brlPlanResult, Is.EqualTo(expectedPlanDefinition));
        });
    }

    [Test]
    public async Task GetPlanDefinition_ValidId_ReturnsPlanDefinition_21()
    {
        var usdPlanId = "sleekflow_v8_agent_premium_monthy";
        var audPlanId = "sleekflow_v8_agent_premium_monthy_aud";
        var cadPlanId = "sleekflow_v8_agent_premium_monthy_cad";
        var cnyPlanId = "sleekflow_v8_agent_premium_monthy_cny";
        var eurPlanId = "sleekflow_v8_agent_premium_monthy_aud";
        var gbpPlanId = "sleekflow_v8_agent_premium_monthy_gbp";
        var hkdPlanId = "sleekflow_v8_agent_premium_monthy_hkd";
        var idrPlanId = "sleekflow_v8_agent_premium_monthy_idr";
        var myrPlanId = "sleekflow_v8_agent_premium_monthy_myr";
        var sgdPlanId = "sleekflow_v8_agent_premium_monthy_sgd";

        var expectedPlanDefinition = new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "agent_premium_monthy", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "agents",
                    Quantity = 1
                }
            },
            new List<Price>
            {
                new Price("USD", 39),
                new Price("AUD", 59),
                new Price("CAD", 49),
                new Price("CNY", 249),
                new Price("EUR", 39),
                new Price("GBP", 29),
                new Price("HKD", 299),
                new Price("IDR", 549000),
                new Price("MYR", 159),
                new Price("SGD", 59),
            },
            PlanTypes.AddOns,
            null,
            "v8",
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow);

        var usdPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(usdPlanId);
        var audPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(audPlanId);
        var cadPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(cadPlanId);
        var cnyPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(cnyPlanId);
        var eurPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(eurPlanId);
        var gbpPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(gbpPlanId);
        var hkdPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(hkdPlanId);
        var idrPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(idrPlanId);
        var myrPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(myrPlanId);
        var sgdPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(sgdPlanId);

        Assert.Multiple(() =>
        {
            Assert.That(usdPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(audPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(cadPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(cnyPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(eurPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(gbpPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(hkdPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(idrPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(myrPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(sgdPlanResult, Is.EqualTo(expectedPlanDefinition));
        });
    }

    [Test]
    public async Task GetPlanDefinition_ValidId_ReturnsPlanDefinition_22()
    {
        var usdPlanId = "sleekflow_v8_agent_premium_yearly";
        var audPlanId = "sleekflow_v8_agent_premium_yearly_aud";
        var cadPlanId = "sleekflow_v8_agent_premium_yearly_cad";
        var cnyPlanId = "sleekflow_v8_agent_premium_yearly_cny";
        var eurPlanId = "sleekflow_v8_agent_premium_yearly_aud";
        var gbpPlanId = "sleekflow_v8_agent_premium_yearly_gbp";
        var hkdPlanId = "sleekflow_v8_agent_premium_yearly_hkd";
        var idrPlanId = "sleekflow_v8_agent_premium_yearly_idr";
        var myrPlanId = "sleekflow_v8_agent_premium_yearly_myr";
        var sgdPlanId = "sleekflow_v8_agent_premium_yearly_sgd";

        var expectedPlanDefinition = new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "agent_premium_yearly", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "agents",
                    Quantity = 1
                }
            },
            new List<Price>
            {
                new Price("USD", 468),
                new Price("AUD", 708),
                new Price("CAD", 588),
                new Price("CNY", 2988),
                new Price("EUR", 468),
                new Price("GBP", 348),
                new Price("HKD", 3588),
                new Price("IDR", 6588000),
                new Price("MYR", 1908),
                new Price("SGD", 708),
            },
            PlanTypes.AddOns,
            null,
            "v8",
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow);

        var usdPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(usdPlanId);
        var audPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(audPlanId);
        var cadPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(cadPlanId);
        var cnyPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(cnyPlanId);
        var eurPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(eurPlanId);
        var gbpPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(gbpPlanId);
        var hkdPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(hkdPlanId);
        var idrPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(idrPlanId);
        var myrPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(myrPlanId);
        var sgdPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(sgdPlanId);

        Assert.Multiple(() =>
        {
            Assert.That(usdPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(audPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(cadPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(cnyPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(eurPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(gbpPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(hkdPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(idrPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(myrPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(sgdPlanResult, Is.EqualTo(expectedPlanDefinition));
        });
    }

    [Test]
    public async Task GetPlanDefinition_ValidId_ReturnsPlanDefinition_23()
    {
        var usdPlanId = "sleekflow_v8_agent_pro_monthly";
        var audPlanId = "sleekflow_v8_agent_pro_monthly_aud";
        var cadPlanId = "sleekflow_v8_agent_pro_monthly_cad";
        var cnyPlanId = "sleekflow_v8_agent_pro_monthly_cny";
        var eurPlanId = "sleekflow_v8_agent_pro_monthly_aud";
        var gbpPlanId = "sleekflow_v8_agent_pro_monthly_gbp";
        var hkdPlanId = "sleekflow_v8_agent_pro_monthly_hkd";
        var idrPlanId = "sleekflow_v8_agent_pro_monthly_idr";
        var myrPlanId = "sleekflow_v8_agent_pro_monthly_myr";
        var sgdPlanId = "sleekflow_v8_agent_pro_monthly_sgd";

        var expectedPlanDefinition = new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "agent_pro_monthly", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "agents",
                    Quantity = 1
                }
            },
            new List<Price>
            {
                new Price("USD", 19),
                new Price("AUD", 29),
                new Price("CAD", 29),
                new Price("CNY", 119),
                new Price("EUR", 19),
                new Price("GBP", 19),
                new Price("HKD", 149),
                new Price("IDR", 269000),
                new Price("MYR", 79),
                new Price("SGD", 29),
            },
            PlanTypes.AddOns,
            null,
            "v8",
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow);

        var usdPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(usdPlanId);
        var audPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(audPlanId);
        var cadPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(cadPlanId);
        var cnyPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(cnyPlanId);
        var eurPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(eurPlanId);
        var gbpPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(gbpPlanId);
        var hkdPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(hkdPlanId);
        var idrPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(idrPlanId);
        var myrPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(myrPlanId);
        var sgdPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(sgdPlanId);

        Assert.Multiple(() =>
        {
            Assert.That(usdPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(audPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(cadPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(cnyPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(eurPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(gbpPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(hkdPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(idrPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(myrPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(sgdPlanResult, Is.EqualTo(expectedPlanDefinition));
        });
    }

    [Test]
    public async Task GetPlanDefinition_ValidId_ReturnsPlanDefinition_24()
    {
        var usdPlanId = "sleekflow_v8_agent_pro_yearly";
        var audPlanId = "sleekflow_v8_agent_pro_yearly_aud";
        var cadPlanId = "sleekflow_v8_agent_pro_yearly_cad";
        var cnyPlanId = "sleekflow_v8_agent_pro_yearly_cny";
        var eurPlanId = "sleekflow_v8_agent_pro_yearly_aud";
        var gbpPlanId = "sleekflow_v8_agent_pro_yearly_gbp";
        var hkdPlanId = "sleekflow_v8_agent_pro_yearly_hkd";
        var idrPlanId = "sleekflow_v8_agent_pro_yearly_idr";
        var myrPlanId = "sleekflow_v8_agent_pro_yearly_myr";
        var sgdPlanId = "sleekflow_v8_agent_pro_yearly_sgd";

        var expectedPlanDefinition = new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "agent_pro_yearly", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "agents",
                    Quantity = 1
                }
            },
            new List<Price>
            {
                new Price("USD", 228),
                new Price("AUD", 348),
                new Price("CAD", 348),
                new Price("CNY", 1428),
                new Price("EUR", 228),
                new Price("GBP", 228),
                new Price("HKD", 1788),
                new Price("IDR", 3228000),
                new Price("MYR", 948),
                new Price("SGD", 348),
            },
            PlanTypes.AddOns,
            null,
            "v8",
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow);

        var usdPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(usdPlanId);
        var audPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(audPlanId);
        var cadPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(cadPlanId);
        var cnyPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(cnyPlanId);
        var eurPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(eurPlanId);
        var gbpPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(gbpPlanId);
        var hkdPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(hkdPlanId);
        var idrPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(idrPlanId);
        var myrPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(myrPlanId);
        var sgdPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(sgdPlanId);

        Assert.Multiple(() =>
        {
            Assert.That(usdPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(audPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(cadPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(cnyPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(eurPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(gbpPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(hkdPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(idrPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(myrPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(sgdPlanResult, Is.EqualTo(expectedPlanDefinition));
        });
    }

    [Test]
    public async Task GetPlanDefinition_ValidId_ReturnsPlanDefinition_25()
    {
        var usdPlanId = "sleekflow_v9_agent_premium_monthly";
        var audPlanId = "sleekflow_v9_agent_premium_monthly_aud";
        var cadPlanId = "sleekflow_v9_agent_premium_monthly_cad";
        var cnyPlanId = "sleekflow_v9_agent_premium_monthly_cny";
        var eurPlanId = "sleekflow_v9_agent_premium_monthly_aud";
        var gbpPlanId = "sleekflow_v9_agent_premium_monthly_gbp";
        var hkdPlanId = "sleekflow_v9_agent_premium_monthly_hkd";
        var idrPlanId = "sleekflow_v9_agent_premium_monthly_idr";
        var myrPlanId = "sleekflow_v9_agent_premium_monthly_myr";
        var sgdPlanId = "sleekflow_v9_agent_premium_monthly_sgd";
        var aedPlanId = "sleekflow_v9_agent_premium_monthly_aed";
        var brlPlanId = "sleekflow_v9_agent_premium_monthly_brl";

        var expectedPlanDefinition = new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "agent_premium_monthly", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "agents",
                    Quantity = 1
                }
            },
            new List<Price>
            {
                new Price("USD", 39),
                new Price("AED", 139),
                new Price("AUD", 59),
                new Price("BRL", 209),
                new Price("CAD", 59),
                new Price("CNY", 279),
                new Price("EUR", 39),
                new Price("GBP", 39),
                new Price("HKD", 299),
                new Price("IDR", 549000),
                new Price("MYR", 159),
                new Price("SGD", 59),
            },
            PlanTypes.AddOns,
            null,
            "v9",
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow);

        var usdPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(usdPlanId);
        var audPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(audPlanId);
        var cadPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(cadPlanId);
        var cnyPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(cnyPlanId);
        var eurPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(eurPlanId);
        var gbpPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(gbpPlanId);
        var hkdPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(hkdPlanId);
        var idrPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(idrPlanId);
        var myrPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(myrPlanId);
        var sgdPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(sgdPlanId);
        var aedPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(aedPlanId);
        var brlPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(brlPlanId);

        Assert.Multiple(() =>
        {
            Assert.That(usdPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(audPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(cadPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(cnyPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(eurPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(gbpPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(hkdPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(idrPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(myrPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(sgdPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(aedPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(brlPlanResult, Is.EqualTo(expectedPlanDefinition));
        });
    }

    [Test]
    public async Task GetPlanDefinition_ValidId_ReturnsPlanDefinition_26()
    {
        var inrPlanId = "sleekflow_v9_agent_premium_monthy_inr";

        var expectedPlanDefinition = new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "agent_premium_monthy", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "agents",
                    Quantity = 1
                }
            },
            new List<Price>
            {
                new Price("INR", 1698),
            },
            PlanTypes.AddOns,
            null,
            "v9",
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow);

        var inrPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(inrPlanId);

        Assert.Multiple(() =>
        {
            Assert.That(inrPlanResult, Is.EqualTo(expectedPlanDefinition));
        });
    }

    [Test]
    public async Task GetPlanDefinition_ValidId_ReturnsPlanDefinition_27()
    {
        var usdPlanId = "sleekflow_v9_agent_pro_monthly";
        var aedPlanId = "sleekflow_v9_agent_pro_monthly_aed";
        var audPlanId = "sleekflow_v9_agent_pro_monthly_aud";
        var brlPlanId = "sleekflow_v9_agent_pro_monthly_brl";
        var cadPlanId = "sleekflow_v9_agent_pro_monthly_cad";
        var cnyPlanId = "sleekflow_v9_agent_pro_monthly_cny";
        var eurPlanId = "sleekflow_v9_agent_pro_monthly_eur";
        var gbpPlanId = "sleekflow_v9_agent_pro_monthly_gbp";
        var hkdPlanId = "sleekflow_v9_agent_pro_monthly_hkd";
        var idrPlanId = "sleekflow_v9_agent_pro_monthly_idr";
        var inrPlanId = "sleekflow_v9_agent_pro_monthly_inr";
        var myrPlanId = "sleekflow_v9_agent_pro_monthly_myr";
        var sgdPlanId = "sleekflow_v9_agent_pro_monthly_sgd";

        var expectedPlanDefinition = new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "agent_pro_monthly", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "agents",
                    Quantity = 1
                }
            },
            new List<Price>
            {
                new Price("USD", 19),
                new Price("AED", 69),
                new Price("AUD", 29),
                new Price("BRL", 109),
                new Price("CAD", 29),
                new Price("CNY", 139),
                new Price("EUR", 19),
                new Price("GBP", 19),
                new Price("HKD", 149),
                new Price("IDR", 269000),
                new Price("INR", 898),
                new Price("MYR", 79),
                new Price("SGD", 29),
            },
            PlanTypes.AddOns,
            null,
            "v9",
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow);

        var usdPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(usdPlanId);
        var aedPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(aedPlanId);
        var audPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(audPlanId);
        var brlPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(brlPlanId);
        var cadPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(cadPlanId);
        var cnyPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(cnyPlanId);
        var eurPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(eurPlanId);
        var gbpPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(gbpPlanId);
        var hkdPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(hkdPlanId);
        var idrPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(idrPlanId);
        var inrPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(inrPlanId);
        var myrPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(myrPlanId);
        var sgdPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(sgdPlanId);

        Assert.Multiple(() =>
        {
            Assert.That(usdPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(aedPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(audPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(brlPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(cadPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(cnyPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(eurPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(gbpPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(hkdPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(idrPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(inrPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(myrPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(sgdPlanResult, Is.EqualTo(expectedPlanDefinition));
        });
    }

    [Test]
    public async Task GetPlanDefinition_ValidId_ReturnsPlanDefinition_28()
    {
        var usdPlanId = "sleekflow_v9_chatbot_automation_support_basic";
        var aedPlanId = "sleekflow_v9_chatbot_automation_support_basic_aed";
        var audPlanId = "sleekflow_v9_chatbot_automation_support_basic_aud";
        var brlPlanId = "sleekflow_v9_chatbot_automation_support_basic_brl";
        var cadPlanId = "sleekflow_v9_chatbot_automation_support_basic_cad";
        var cnyPlanId = "sleekflow_v9_chatbot_automation_support_basic_cny";
        var eurPlanId = "sleekflow_v9_chatbot_automation_support_basic_eur";
        var gbpPlanId = "sleekflow_v9_chatbot_automation_support_basic_gbp";
        var hkdPlanId = "sleekflow_v9_chatbot_automation_support_basic_hkd";
        var idrPlanId = "sleekflow_v9_chatbot_automation_support_basic_idr";
        var inrPlanId = "sleekflow_v9_chatbot_automation_support_basic_inr";
        var myrPlanId = "sleekflow_v9_chatbot_automation_support_basic_myr";
        var sgdPlanId = "sleekflow_v9_chatbot_automation_support_basic_sgd";

        var expectedPlanDefinition = new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "chatbot_automation_support_basic", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "chatbot_automation_support_basic",
                    Quantity = 1
                }
            },
            new List<Price>
            {
                new Price("USD", 3999),
                new Price("AED", 14689),
                new Price("AUD", 5899),
                new Price("BRL", 21499),
                new Price("CAD", 5179),
                new Price("CNY", 28469),
                new Price("EUR", 3969),
                new Price("GBP", 3359),
                new Price("HKD", 31399),
                new Price("IDR", 60052000),
                new Price("INR", 169399),
                new Price("MYR", 17749),
                new Price("SGD", 5619),
            },
            PlanTypes.AddOns,
            null,
            "v9",
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow);

        var usdPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(usdPlanId);
        var aedPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(aedPlanId);
        var audPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(audPlanId);
        var brlPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(brlPlanId);
        var cadPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(cadPlanId);
        var cnyPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(cnyPlanId);
        var eurPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(eurPlanId);
        var gbpPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(gbpPlanId);
        var hkdPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(hkdPlanId);
        var idrPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(idrPlanId);
        var inrPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(inrPlanId);
        var myrPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(myrPlanId);
        var sgdPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(sgdPlanId);

        Assert.Multiple(() =>
        {
            Assert.That(usdPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(aedPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(audPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(brlPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(cadPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(cnyPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(eurPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(gbpPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(hkdPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(idrPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(inrPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(myrPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(sgdPlanResult, Is.EqualTo(expectedPlanDefinition));
        });
    }

    [Test]
    public async Task GetPlanDefinition_ValidId_ReturnsPlanDefinition_29()
    {
        var usdPlanId = "sleekflow_v9_chatbot_automation_support_intermediate";
        var aedPlanId = "sleekflow_v9_chatbot_automation_support_intermediate_aed";
        var audPlanId = "sleekflow_v9_chatbot_automation_support_intermediate_aud";
        var brlPlanId = "sleekflow_v9_chatbot_automation_support_intermediate_brl";
        var cadPlanId = "sleekflow_v9_chatbot_automation_support_intermediate_cad";
        var cnyPlanId = "sleekflow_v9_chatbot_automation_support_intermediate_cny";
        var eurPlanId = "sleekflow_v9_chatbot_automation_support_intermediate_eur";
        var gbpPlanId = "sleekflow_v9_chatbot_automation_support_intermediate_gbp";
        var hkdPlanId = "sleekflow_v9_chatbot_automation_support_intermediate_hkd";
        var idrPlanId = "sleekflow_v9_chatbot_automation_support_intermediate_idr";
        var inrPlanId = "sleekflow_v9_chatbot_automation_support_intermediate_inr";
        var myrPlanId = "sleekflow_v9_chatbot_automation_support_intermediate_myr";
        var sgdPlanId = "sleekflow_v9_chatbot_automation_support_intermediate_sgd";

        var expectedPlanDefinition = new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "chatbot_automation_support_intermediate", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "chatbot_automation_support_intermediate",
                    Quantity = 1
                }
            },
            new List<Price>
            {
                new Price("USD", 9999),
                new Price("AED", 36729),
                new Price("AUD", 14749),
                new Price("BRL", 53759),
                new Price("CAD", 12959),
                new Price("CNY", 71159),
                new Price("EUR", 9919),
                new Price("GBP", 8389),
                new Price("HKD", 78499),
                new Price("IDR", 150153000),
                new Price("INR", 423599),
                new Price("MYR", 44369),
                new Price("SGD", 14049),
            },
            PlanTypes.AddOns,
            null,
            "v9",
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow);

        var usdPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(usdPlanId);
        var aedPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(aedPlanId);
        var audPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(audPlanId);
        var brlPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(brlPlanId);
        var cadPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(cadPlanId);
        var cnyPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(cnyPlanId);
        var eurPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(eurPlanId);
        var gbpPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(gbpPlanId);
        var hkdPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(hkdPlanId);
        var idrPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(idrPlanId);
        var inrPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(inrPlanId);
        var myrPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(myrPlanId);
        var sgdPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(sgdPlanId);

        Assert.Multiple(() =>
        {
            Assert.That(usdPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(aedPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(audPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(brlPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(cadPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(cnyPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(eurPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(gbpPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(hkdPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(idrPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(inrPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(myrPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(sgdPlanResult, Is.EqualTo(expectedPlanDefinition));
        });
    }

    [Test]
    public async Task GetPlanDefinition_ValidId_ReturnsPlanDefinition_30()
    {
        var usdPlanId = "sleekflow_v9_chatbot_automation_support_advanced";
        var aedPlanId = "sleekflow_v9_chatbot_automation_support_advanced_aed";
        var audPlanId = "sleekflow_v9_chatbot_automation_support_advanced_aud";
        var brlPlanId = "sleekflow_v9_chatbot_automation_support_advanced_brl";
        var cadPlanId = "sleekflow_v9_chatbot_automation_support_advanced_cad";
        var cnyPlanId = "sleekflow_v9_chatbot_automation_support_advanced_cny";
        var eurPlanId = "sleekflow_v9_chatbot_automation_support_advanced_eur";
        var gbpPlanId = "sleekflow_v9_chatbot_automation_support_advanced_gbp";
        var hkdPlanId = "sleekflow_v9_chatbot_automation_support_advanced_hkd";
        var idrPlanId = "sleekflow_v9_chatbot_automation_support_advanced_idr";
        var inrPlanId = "sleekflow_v9_chatbot_automation_support_advanced_inr";
        var myrPlanId = "sleekflow_v9_chatbot_automation_support_advanced_myr";
        var sgdPlanId = "sleekflow_v9_chatbot_automation_support_advanced_sgd";

        var expectedPlanDefinition = new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "chatbot_automation_support_advanced", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "chatbot_automation_support_advanced",
                    Quantity = 1
                }
            },
            new List<Price>
            {
                new Price("USD", 19999),
                new Price("AED", 73459),
                new Price("AUD", 29489),
                new Price("BRL", 107519),
                new Price("CAD", 25909),
                new Price("CNY", 142309),
                new Price("EUR", 19839),
                new Price("GBP", 16779),
                new Price("HKD", 156999),
                new Price("IDR", 300320000),
                new Price("INR", 847199),
                new Price("MYR", 88739),
                new Price("SGD", 28089),
            },
            PlanTypes.AddOns,
            null,
            "v9",
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow);

        var usdPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(usdPlanId);
        var aedPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(aedPlanId);
        var audPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(audPlanId);
        var brlPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(brlPlanId);
        var cadPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(cadPlanId);
        var cnyPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(cnyPlanId);
        var eurPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(eurPlanId);
        var gbpPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(gbpPlanId);
        var hkdPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(hkdPlanId);
        var idrPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(idrPlanId);
        var inrPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(inrPlanId);
        var myrPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(myrPlanId);
        var sgdPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(sgdPlanId);

        Assert.Multiple(() =>
        {
            Assert.That(usdPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(aedPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(audPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(brlPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(cadPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(cnyPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(eurPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(gbpPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(hkdPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(idrPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(inrPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(myrPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(sgdPlanResult, Is.EqualTo(expectedPlanDefinition));
        });
    }

    [Test]
    public async Task GetPlanDefinition_ValidId_ReturnsPlanDefinition_31()
    {
        var usdPlanId = "sleekflow_v9_chatbot_automation_support_enterprise";
        var aedPlanId = "sleekflow_v9_chatbot_automation_support_enterprise_aed";
        var audPlanId = "sleekflow_v9_chatbot_automation_support_enterprise_aud";
        var brlPlanId = "sleekflow_v9_chatbot_automation_support_enterprise_brl";
        var cadPlanId = "sleekflow_v9_chatbot_automation_support_enterprise_cad";
        var cnyPlanId = "sleekflow_v9_chatbot_automation_support_enterprise_cny";
        var eurPlanId = "sleekflow_v9_chatbot_automation_support_enterprise_eur";
        var gbpPlanId = "sleekflow_v9_chatbot_automation_support_enterprise_gbp";
        var hkdPlanId = "sleekflow_v9_chatbot_automation_support_enterprise_hkd";
        var idrPlanId = "sleekflow_v9_chatbot_automation_support_enterprise_idr";
        var inrPlanId = "sleekflow_v9_chatbot_automation_support_enterprise_inr";
        var myrPlanId = "sleekflow_v9_chatbot_automation_support_enterprise_myr";
        var sgdPlanId = "sleekflow_v9_chatbot_automation_support_enterprise_sgd";

        var expectedPlanDefinition = new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "chatbot_automation_support_enterprise", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "chatbot_automation_support_enterprise",
                    Quantity = 1
                }
            },
            new List<Price>
            {
                new Price("USD", 29999),
                new Price("AED", 110189),
                new Price("AUD", 44229),
                new Price("BRL", 161269),
                new Price("CAD", 38859),
                new Price("CNY", 213459),
                new Price("EUR", 29759),
                new Price("GBP", 25169),
                new Price("HKD", 235499),
                new Price("IDR", 450488000),
                new Price("INR", 1270799),
                new Price("MYR", 133109),
                new Price("SGD", 42129),
            },
            PlanTypes.AddOns,
            null,
            "v9",
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow);

        var usdPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(usdPlanId);
        var aedPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(aedPlanId);
        var audPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(audPlanId);
        var brlPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(brlPlanId);
        var cadPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(cadPlanId);
        var cnyPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(cnyPlanId);
        var eurPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(eurPlanId);
        var gbpPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(gbpPlanId);
        var hkdPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(hkdPlanId);
        var idrPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(idrPlanId);
        var inrPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(inrPlanId);
        var myrPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(myrPlanId);
        var sgdPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(sgdPlanId);

        Assert.Multiple(() =>
        {
            Assert.That(usdPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(aedPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(audPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(brlPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(cadPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(cnyPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(eurPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(gbpPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(hkdPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(idrPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(inrPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(myrPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(sgdPlanResult, Is.EqualTo(expectedPlanDefinition));
        });
    }

    [Test]
    public async Task GetPlanDefinition_ValidId_ReturnsPlanDefinition_32()
    {
        var usdPlanId = "sleekflow_v9_hubspot_integration";
        var aedPlanId = "sleekflow_v9_hubspot_integration_aed";
        var audPlanId = "sleekflow_v9_hubspot_integration_aud";
        var brlPlanId = "sleekflow_v9_hubspot_integration_brl";
        var cadPlanId = "sleekflow_v9_hubspot_integration_cad";
        var cnyPlanId = "sleekflow_v9_hubspot_integration_cny";
        var eurPlanId = "sleekflow_v9_hubspot_integration_eur";
        var gbpPlanId = "sleekflow_v9_hubspot_integration_gbp";
        var hkdPlanId = "sleekflow_v9_hubspot_integration_hkd";
        var idrPlanId = "sleekflow_v9_hubspot_integration_idr";
        var inrPlanId = "sleekflow_v9_hubspot_integration_inr";
        var myrPlanId = "sleekflow_v9_hubspot_integration_myr";
        var sgdPlanId = "sleekflow_v9_hubspot_integration_sgd";

        var expectedPlanDefinition = new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "hubspot_integration", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "hubspot",
                    Quantity = 1
                }
            },
            new List<Price>
            {
                new Price("USD", 199),
                new Price("AED", 739),
                new Price("AUD", 299),
                new Price("BRL", 1099),
                new Price("CAD", 259),
                new Price("CNY", 1449),
                new Price("EUR", 199),
                new Price("GBP", 169),
                new Price("HKD", 1599),
                new Price("IDR", 3000000),
                new Price("INR", 8499),
                new Price("MYR", 889),
                new Price("SGD", 279),
            },
            PlanTypes.AddOns,
            null,
            "v9",
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow);

        var usdPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(usdPlanId);
        var aedPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(aedPlanId);
        var audPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(audPlanId);
        var brlPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(brlPlanId);
        var cadPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(cadPlanId);
        var cnyPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(cnyPlanId);
        var eurPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(eurPlanId);
        var gbpPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(gbpPlanId);
        var hkdPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(hkdPlanId);
        var idrPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(idrPlanId);
        var inrPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(inrPlanId);
        var myrPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(myrPlanId);
        var sgdPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(sgdPlanId);

        Assert.Multiple(() =>
        {
            Assert.That(usdPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(aedPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(audPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(brlPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(cadPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(cnyPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(eurPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(gbpPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(hkdPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(idrPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(inrPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(myrPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(sgdPlanResult, Is.EqualTo(expectedPlanDefinition));
        });
    }

    [Test]
    public async Task GetPlanDefinition_ValidId_ReturnsPlanDefinition_33()
    {
        var usdPlanId = "sleekflow_v9_internal_custom_subscription_payment";
        var aedPlanId = "sleekflow_v9_internal_custom_subscription_payment_aed";
        var audPlanId = "sleekflow_v9_internal_custom_subscription_payment_aud";
        var brlPlanId = "sleekflow_v9_internal_custom_subscription_payment_brl";
        var cadPlanId = "sleekflow_v9_internal_custom_subscription_payment_cad";
        var cnyPlanId = "sleekflow_v9_internal_custom_subscription_payment_cny";
        var eurPlanId = "sleekflow_v9_internal_custom_subscription_payment_eur";
        var gbpPlanId = "sleekflow_v9_internal_custom_subscription_payment_gbp";
        var hkdPlanId = "sleekflow_v9_internal_custom_subscription_payment_hkd";
        var idrPlanId = "sleekflow_v9_internal_custom_subscription_payment_idr";
        var inrPlanId = "sleekflow_v9_internal_custom_subscription_payment_inr";
        var myrPlanId = "sleekflow_v9_internal_custom_subscription_payment_myr";
        var sgdPlanId = "sleekflow_v9_internal_custom_subscription_payment_sgd";

        var expectedPlanDefinition = new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "internal_custom_subscription_payment", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "internal_custom_subscription_payment",
                    Quantity = 1
                }
            },
            new List<Price>
            {
                new Price("USD", 0),
                new Price("AED", 0),
                new Price("AUD", 0),
                new Price("BRL", 0),
                new Price("CAD", 0),
                new Price("CNY", 0),
                new Price("EUR", 0),
                new Price("GBP", 0),
                new Price("HKD", 0),
                new Price("IDR", 0),
                new Price("INR", 0),
                new Price("MYR", 0),
                new Price("SGD", 0),
            },
            PlanTypes.AddOns,
            null,
            "v9",
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow);

        var usdPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(usdPlanId);
        var aedPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(aedPlanId);
        var audPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(audPlanId);
        var brlPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(brlPlanId);
        var cadPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(cadPlanId);
        var cnyPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(cnyPlanId);
        var eurPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(eurPlanId);
        var gbpPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(gbpPlanId);
        var hkdPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(hkdPlanId);
        var idrPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(idrPlanId);
        var inrPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(inrPlanId);
        var myrPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(myrPlanId);
        var sgdPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(sgdPlanId);

        Assert.Multiple(() =>
        {
            Assert.That(usdPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(aedPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(audPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(brlPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(cadPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(cnyPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(eurPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(gbpPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(hkdPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(idrPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(inrPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(myrPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(sgdPlanResult, Is.EqualTo(expectedPlanDefinition));
        });
    }

	[Test]
    public async Task GetPlanDefinition_ValidId_ReturnsPlanDefinition_34()
    {
        var usdPlanId = "sleekflow_v9_onboarding_support_oneoff";
        var aedPlanId = "sleekflow_v9_onboarding_support_oneoff_aed";
        var audPlanId = "sleekflow_v9_onboarding_support_oneoff_aud";
        var brlPlanId = "sleekflow_v9_onboarding_support_oneoff_brl";
        var cadPlanId = "sleekflow_v9_onboarding_support_oneoff_cad";
        var cnyPlanId = "sleekflow_v9_onboarding_support_oneoff_cny";
        var eurPlanId = "sleekflow_v9_onboarding_support_oneoff_eur";
        var gbpPlanId = "sleekflow_v9_onboarding_support_oneoff_gbp";
        var hkdPlanId = "sleekflow_v9_onboarding_support_oneoff_hkd";
        var idrPlanId = "sleekflow_v9_onboarding_support_oneoff_idr";
        var inrPlanId = "sleekflow_v9_onboarding_support_oneoff_inr";
        var myrPlanId = "sleekflow_v9_onboarding_support_oneoff_myr";
        var sgdPlanId = "sleekflow_v9_onboarding_support_oneoff_sgd";

        var expectedPlanDefinition = new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "onboarding_support_oneoff", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "onboarding_support",
                    Quantity = 1
                }
            },
            new List<Price>
            {
                new Price("USD", 499),
                new Price("AED", 1799),
                new Price("AUD", 739),
                new Price("BRL", 2699),
                new Price("CAD", 649),
                new Price("CNY", 3629),
                new Price("EUR", 499),
                new Price("GBP", 419),
                new Price("HKD", 3999),
                new Price("IDR", 7400000),
                new Price("INR", 21199),
                new Price("MYR", 2199),
                new Price("SGD", 699),
            },
            PlanTypes.AddOns,
            null,
            "v9",
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow);

        var usdPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(usdPlanId);
        var aedPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(aedPlanId);
        var audPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(audPlanId);
        var brlPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(brlPlanId);
        var cadPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(cadPlanId);
        var cnyPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(cnyPlanId);
        var eurPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(eurPlanId);
        var gbpPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(gbpPlanId);
        var hkdPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(hkdPlanId);
        var idrPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(idrPlanId);
        var inrPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(inrPlanId);
        var myrPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(myrPlanId);
        var sgdPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(sgdPlanId);

        Assert.Multiple(() =>
        {
            Assert.That(usdPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(aedPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(audPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(brlPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(cadPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(cnyPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(eurPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(gbpPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(hkdPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(idrPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(inrPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(myrPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(sgdPlanResult, Is.EqualTo(expectedPlanDefinition));
        });
    }

	[Test]
    public async Task GetPlanDefinition_ValidId_ReturnsPlanDefinition_35()
    {
        var usdPlanId = "sleekflow_v9_payment_integration";
        var aedPlanId = "sleekflow_v9_payment_integration_aed";
        var audPlanId = "sleekflow_v9_payment_integration_aud";
        var brlPlanId = "sleekflow_v9_payment_integration_brl";
        var cadPlanId = "sleekflow_v9_payment_integration_cad";
        var cnyPlanId = "sleekflow_v9_payment_integration_cny";
        var eurPlanId = "sleekflow_v9_payment_integration_eur";
        var gbpPlanId = "sleekflow_v9_payment_integration_gbp";
        var hkdPlanId = "sleekflow_v9_payment_integration_hkd";
        var idrPlanId = "sleekflow_v9_payment_integration_idr";
        var inrPlanId = "sleekflow_v9_payment_integration_inr";
        var myrPlanId = "sleekflow_v9_payment_integration_myr";
        var sgdPlanId = "sleekflow_v9_payment_integration_sgd";

        var expectedPlanDefinition = new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "payment_integration", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "payment_integration",
                    Quantity = 1
                }
            },
            new List<Price>
            {
                new Price("USD", 99),
                new Price("AED", 369),
                new Price("AUD", 149),
                new Price("BRL", 539),
                new Price("CAD", 129),
                new Price("CNY", 709),
                new Price("EUR", 99),
                new Price("GBP", 89),
                new Price("HKD", 779),
                new Price("IDR", 1390000),
                new Price("INR", 4198),
                new Price("MYR", 439),
                new Price("SGD", 139),
            },
            PlanTypes.AddOns,
            null,
            "v9",
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow);

        var usdPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(usdPlanId);
        var aedPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(aedPlanId);
        var audPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(audPlanId);
        var brlPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(brlPlanId);
        var cadPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(cadPlanId);
        var cnyPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(cnyPlanId);
        var eurPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(eurPlanId);
        var gbpPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(gbpPlanId);
        var hkdPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(hkdPlanId);
        var idrPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(idrPlanId);
        var inrPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(inrPlanId);
        var myrPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(myrPlanId);
        var sgdPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(sgdPlanId);

        Assert.Multiple(() =>
        {
            Assert.That(usdPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(aedPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(audPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(brlPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(cadPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(cnyPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(eurPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(gbpPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(hkdPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(idrPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(inrPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(myrPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(sgdPlanResult, Is.EqualTo(expectedPlanDefinition));
        });
    }

    [Test]
    public async Task GetPlanDefinition_ValidId_ReturnsPlanDefinition_36()
    {
        var usdPlanId = "sleekflow_v9_salesforce_commerce_cloud";
        var aedPlanId = "sleekflow_v9_salesforce_commerce_cloud_aed";
        var audPlanId = "sleekflow_v9_salesforce_commerce_cloud_aud";
        var brlPlanId = "sleekflow_v9_salesforce_commerce_cloud_brl";
        var cadPlanId = "sleekflow_v9_salesforce_commerce_cloud_cad";
        var cnyPlanId = "sleekflow_v9_salesforce_commerce_cloud_cny";
        var eurPlanId = "sleekflow_v9_salesforce_commerce_cloud_eur";
        var gbpPlanId = "sleekflow_v9_salesforce_commerce_cloud_gbp";
        var hkdPlanId = "sleekflow_v9_salesforce_commerce_cloud_hkd";
        var idrPlanId = "sleekflow_v9_salesforce_commerce_cloud_idr";
        var inrPlanId = "sleekflow_v9_salesforce_commerce_cloud_inr";
        var myrPlanId = "sleekflow_v9_salesforce_commerce_cloud_myr";
        var sgdPlanId = "sleekflow_v9_salesforce_commerce_cloud_sgd";

        var expectedPlanDefinition = new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "salesforce_commerce_cloud", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "salesforce_commerce_cloud",
                    Quantity = 1
                }
            },
            new List<Price>
            {
                new Price("USD", 999),
                new Price("AED", 3669),
                new Price("AUD", 1479),
                new Price("BRL", 5379),
                new Price("CAD", 1299),
                new Price("CNY", 7119),
                new Price("EUR", 999),
                new Price("GBP", 839),
                new Price("HKD", 7849),
                new Price("IDR", 15002000),
                new Price("INR", 42399),
                new Price("MYR", 4439),
                new Price("SGD", 1409),
            },
            PlanTypes.AddOns,
            null,
            "v9",
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow);

        var usdPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(usdPlanId);
        var aedPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(aedPlanId);
        var audPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(audPlanId);
        var brlPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(brlPlanId);
        var cadPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(cadPlanId);
        var cnyPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(cnyPlanId);
        var eurPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(eurPlanId);
        var gbpPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(gbpPlanId);
        var hkdPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(hkdPlanId);
        var idrPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(idrPlanId);
        var inrPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(inrPlanId);
        var myrPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(myrPlanId);
        var sgdPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(sgdPlanId);

        Assert.Multiple(() =>
        {
            Assert.That(usdPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(aedPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(audPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(brlPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(cadPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(cnyPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(eurPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(gbpPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(hkdPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(idrPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(inrPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(myrPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(sgdPlanResult, Is.EqualTo(expectedPlanDefinition));
        });
    }

    [Test]
    public async Task GetPlanDefinition_ValidId_ReturnsPlanDefinition_37()
    {
        var usdPlanId = "sleekflow_v9_salesforce_integration";
        var aedPlanId = "sleekflow_v9_salesforce_integration_aed";
        var audPlanId = "sleekflow_v9_salesforce_integration_aud";
        var brlPlanId = "sleekflow_v9_salesforce_integration_brl";
        var cadPlanId = "sleekflow_v9_salesforce_integration_cad";
        var cnyPlanId = "sleekflow_v9_salesforce_integration_cny";
        var eurPlanId = "sleekflow_v9_salesforce_integration_eur";
        var gbpPlanId = "sleekflow_v9_salesforce_integration_gbp";
        var hkdPlanId = "sleekflow_v9_salesforce_integration_hkd";
        var idrPlanId = "sleekflow_v9_salesforce_integration_idr";
        var inrPlanId = "sleekflow_v9_salesforce_integration_inr";
        var myrPlanId = "sleekflow_v9_salesforce_integration_myr";
        var sgdPlanId = "sleekflow_v9_salesforce_integration_sgd";

        var expectedPlanDefinition = new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "salesforce_integration", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "salesforce_integration",
                    Quantity = 1
                }
            },
            new List<Price>
            {
                new Price("USD", 499),
                new Price("AED", 1839),
                new Price("AUD", 739),
                new Price("BRL", 2689),
                new Price("CAD", 649),
                new Price("CNY", 3559),
                new Price("EUR", 499),
                new Price("GBP", 419),
                new Price("HKD", 3919),
                new Price("IDR", 7494000),
                new Price("INR", 21199),
                new Price("MYR", 2219),
                new Price("SGD", 709),
            },
            PlanTypes.AddOns,
            null,
            "v9",
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow);

        var usdPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(usdPlanId);
        var aedPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(aedPlanId);
        var audPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(audPlanId);
        var brlPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(brlPlanId);
        var cadPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(cadPlanId);
        var cnyPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(cnyPlanId);
        var eurPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(eurPlanId);
        var gbpPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(gbpPlanId);
        var hkdPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(hkdPlanId);
        var idrPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(idrPlanId);
        var inrPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(inrPlanId);
        var myrPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(myrPlanId);
        var sgdPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(sgdPlanId);

        Assert.Multiple(() =>
        {
            Assert.That(usdPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(aedPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(audPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(brlPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(cadPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(cnyPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(eurPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(gbpPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(hkdPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(idrPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(inrPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(myrPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(sgdPlanResult, Is.EqualTo(expectedPlanDefinition));
        });
    }

    [Test]
    public async Task GetPlanDefinition_ValidId_ReturnsPlanDefinition_38()
    {
        var usdPlanId = "sleekflow_v9_salesforce_marketing_cloud";
        var aedPlanId = "sleekflow_v9_salesforce_marketing_cloud_aed";
        var audPlanId = "sleekflow_v9_salesforce_marketing_cloud_aud";
        var brlPlanId = "sleekflow_v9_salesforce_marketing_cloud_brl";
        var cadPlanId = "sleekflow_v9_salesforce_marketing_cloud_cad";
        var cnyPlanId = "sleekflow_v9_salesforce_marketing_cloud_cny";
        var eurPlanId = "sleekflow_v9_salesforce_marketing_cloud_eur";
        var gbpPlanId = "sleekflow_v9_salesforce_marketing_cloud_gbp";
        var hkdPlanId = "sleekflow_v9_salesforce_marketing_cloud_hkd";
        var idrPlanId = "sleekflow_v9_salesforce_marketing_cloud_idr";
        var inrPlanId = "sleekflow_v9_salesforce_marketing_cloud_inr";
        var myrPlanId = "sleekflow_v9_salesforce_marketing_cloud_myr";
        var sgdPlanId = "sleekflow_v9_salesforce_marketing_cloud_sgd";

        var expectedPlanDefinition = new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "salesforce_marketing_cloud", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "salesforce_marketing_cloud",
                    Quantity = 1
                }
            },
            new List<Price>
            {
                new Price("USD", 999),
                new Price("AED", 3669),
                new Price("AUD", 1479),
                new Price("BRL", 5379),
                new Price("CAD", 1299),
                new Price("CNY", 7119),
                new Price("EUR", 999),
                new Price("GBP", 839),
                new Price("HKD", 7849),
                new Price("IDR", 15002000),
                new Price("INR", 42399),
                new Price("MYR", 4439),
                new Price("SGD", 1409),
            },
            PlanTypes.AddOns,
            null,
            "v9",
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow);

        var usdPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(usdPlanId);
        var aedPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(aedPlanId);
        var audPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(audPlanId);
        var brlPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(brlPlanId);
        var cadPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(cadPlanId);
        var cnyPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(cnyPlanId);
        var eurPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(eurPlanId);
        var gbpPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(gbpPlanId);
        var hkdPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(hkdPlanId);
        var idrPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(idrPlanId);
        var inrPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(inrPlanId);
        var myrPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(myrPlanId);
        var sgdPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(sgdPlanId);

        Assert.Multiple(() =>
        {
            Assert.That(usdPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(aedPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(audPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(brlPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(cadPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(cnyPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(eurPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(gbpPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(hkdPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(idrPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(inrPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(myrPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(sgdPlanResult, Is.EqualTo(expectedPlanDefinition));
        });
    }

    [Test]
    public async Task GetPlanDefinition_ValidId_ReturnsPlanDefinition_40()
    {
        var usdPlanId = "sleekflow_v9_unlimited_contact";
        var aedPlanId = "sleekflow_v9_unlimited_contact_aed";
        var audPlanId = "sleekflow_v9_unlimited_contact_aud";
        var brlPlanId = "sleekflow_v9_unlimited_contact_brl";
        var cadPlanId = "sleekflow_v9_unlimited_contact_cad";
        var cnyPlanId = "sleekflow_v9_unlimited_contact_cny";
        var eurPlanId = "sleekflow_v9_unlimited_contact_eur";
        var gbpPlanId = "sleekflow_v9_unlimited_contact_gbp";
        var hkdPlanId = "sleekflow_v9_unlimited_contact_hkd";
        var idrPlanId = "sleekflow_v9_unlimited_contact_idr";
        var myrPlanId = "sleekflow_v9_unlimited_contact_myr";
        var sgdPlanId = "sleekflow_v9_unlimited_contact_sgd";

        var expectedPlanDefinition = new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "unlimited_contact", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "contacts",
                    Quantity = 50000000
                }
            },
            new List<Price>
            {
                new Price("USD", 499),
                new Price("AED", 1799),
                new Price("AUD", 739),
                new Price("BRL", 2699),
                new Price("CAD", 649),
                new Price("CNY", 3399),
                new Price("EUR", 499),
                new Price("GBP", 419),
                new Price("HKD", 3999),
                new Price("IDR", 7400000),
                new Price("MYR", 2199),
                new Price("SGD", 699),
            },
            PlanTypes.AddOns,
            null,
            "v9",
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow);

        var usdPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(usdPlanId);
        var aedPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(aedPlanId);
        var audPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(audPlanId);
        var brlPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(brlPlanId);
        var cadPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(cadPlanId);
        var cnyPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(cnyPlanId);
        var eurPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(eurPlanId);
        var gbpPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(gbpPlanId);
        var hkdPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(hkdPlanId);
        var idrPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(idrPlanId);
        var myrPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(myrPlanId);
        var sgdPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(sgdPlanId);

        Assert.Multiple(() =>
        {
            Assert.That(usdPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(aedPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(audPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(brlPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(cadPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(cnyPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(eurPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(gbpPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(hkdPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(idrPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(myrPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(sgdPlanResult, Is.EqualTo(expectedPlanDefinition));
        });
    }

    [Test]
    public async Task GetPlanDefinition_ValidId_ReturnsPlanDefinition_41()
    {
        var usdPlanId = "sleekflow_v9_whatsapp_qr_code";
        var aedPlanId = "sleekflow_v9_whatsapp_qr_code_aed";
        var audPlanId = "sleekflow_v9_whatsapp_qr_code_aud";
        var brlPlanId = "sleekflow_v9_whatsapp_qr_code_brl";
        var cadPlanId = "sleekflow_v9_whatsapp_qr_code_cad";
        var cnyPlanId = "sleekflow_v9_whatsapp_qr_code_cny";
        var eurPlanId = "sleekflow_v9_whatsapp_qr_code_eur";
        var gbpPlanId = "sleekflow_v9_whatsapp_qr_code_gbp";
        var hkdPlanId = "sleekflow_v9_whatsapp_qr_code_hkd";
        var idrPlanId = "sleekflow_v9_whatsapp_qr_code_idr";
        var inrPlanId = "sleekflow_v9_whatsapp_qr_code_inr";
        var myrPlanId = "sleekflow_v9_whatsapp_qr_code_myr";
        var sgdPlanId = "sleekflow_v9_whatsapp_qr_code_sgd";

        var expectedPlanDefinition = new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "whatsapp_qr_code", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "whatsapp_qr_code",
                    Quantity = 1
                }
            },
            new List<Price>
            {
                new Price("USD", 99),
                new Price("AED", 369),
                new Price("AUD", 149),
                new Price("BRL", 539),
                new Price("CAD", 129),
                new Price("CNY", 709),
                new Price("EUR", 99),
                new Price("GBP", 89),
                new Price("HKD", 779),
                new Price("IDR", 1390000),
                new Price("INR", 4198),
                new Price("MYR", 439),
                new Price("SGD", 139),
            },
            PlanTypes.AddOns,
            null,
            "v9",
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow);

        var usdPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(usdPlanId);
        var aedPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(aedPlanId);
        var audPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(audPlanId);
        var brlPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(brlPlanId);
        var cadPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(cadPlanId);
        var cnyPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(cnyPlanId);
        var eurPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(eurPlanId);
        var gbpPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(gbpPlanId);
        var hkdPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(hkdPlanId);
        var idrPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(idrPlanId);
        var inrPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(inrPlanId);
        var myrPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(myrPlanId);
        var sgdPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(sgdPlanId);

        Assert.Multiple(() =>
        {
            Assert.That(usdPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(aedPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(audPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(brlPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(cadPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(cnyPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(eurPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(gbpPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(hkdPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(idrPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(inrPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(myrPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(sgdPlanResult, Is.EqualTo(expectedPlanDefinition));
        });
    }

    // Test case is disabled due to feature plan is updated
    // Team: Team GenR
    // [Test]
    // public async Task GetPlanDefinition_ValidId_ReturnsPlanDefinition_42()
    // {
    //     var idrPlanId = "sleekflow_v9_premium_monthly_idr";
    //     var inrPlanId = "sleekflow_v9_premium_monthly_inr";
    //
    //     var expectedPlanDefinition = new PlanDefinition(
    //         new List<Multilingual>
    //         {
    //             new Multilingual("en", "premium_monthly", true)
    //         },
    //         new List<Multilingual>(),
    //         new List<FeatureQuantity>
    //         {
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "contacts",
    //                 Quantity = 40000
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "messages",
    //                 Quantity = 50000000
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "agents",
    //                 Quantity = 5
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "campaigns",
    //                 Quantity = 50000000
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "automations",
    //                 Quantity = 999
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "channels",
    //                 Quantity = 10
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "api_calls",
    //                 Quantity = 100000
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "priority_support",
    //                 Quantity = 1
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "chatbot_automation_support_basic",
    //                 Quantity = 1
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "ai_features_total_usage",
    //                 Quantity = 2000
    //             },
    //         },
    //         new List<Price>
    //         {
    //             new Price("IDR", 4890000),
    //             new Price("INR", 14999),
    //         },
    //         PlanTypes.Subscription,
    //         "premium",
    //         "v9",
    //         new List<string>
    //         {
    //             "Active"
    //         },
    //         new Dictionary<string, object>(),
    //         string.Empty,
    //         string.Empty,
    //         null,
    //         DateTimeOffset.UtcNow,
    //         DateTimeOffset.UtcNow);
    //
    //     var idrPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(idrPlanId);
    //     var inrPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(inrPlanId);
    //
    //     Assert.Multiple(() =>
    //     {
    //         Assert.That(idrPlanResult, Is.EqualTo(expectedPlanDefinition));
    //         Assert.That(inrPlanResult, Is.EqualTo(expectedPlanDefinition));
    //     });
    // }

    // [Test]
    // public async Task GetPlanDefinition_ValidId_ReturnsPlanDefinition_43()
    // {
    //     var usdPlanId = "sleekflow_v9_premium_monthly";
    //     var aedPlanId = "sleekflow_v9_premium_monthly_aed";
    //     var audPlanId = "sleekflow_v9_premium_monthly_aud";
    //     var cadPlanId = "sleekflow_v9_premium_monthly_cad";
    //     var cnyPlanId = "sleekflow_v9_premium_monthly_cny";
    //     var eurPlanId = "sleekflow_v9_premium_monthly_eur";
    //     var gbpPlanId = "sleekflow_v9_premium_monthly_gbp";
    //     var hkdPlanId = "sleekflow_v9_premium_monthly_hkd";
    //     var sgdPlanId = "sleekflow_v9_premium_monthly_sgd";
    //
    //     var expectedPlanDefinition = new PlanDefinition(
    //         new List<Multilingual>
    //         {
    //             new Multilingual("en", "premium_monthly", true)
    //         },
    //         new List<Multilingual>(),
    //         new List<FeatureQuantity>
    //         {
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "contacts",
    //                 Quantity = 10000
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "messages",
    //                 Quantity = 50000000
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "agents",
    //                 Quantity = 5
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "campaigns",
    //                 Quantity = 50000000
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "automations",
    //                 Quantity = 999
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "channels",
    //                 Quantity = 10
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "api_calls",
    //                 Quantity = 100000
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "priority_support",
    //                 Quantity = 1
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "chatbot_automation_support_basic",
    //                 Quantity = 1
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "ai_features_total_usage",
    //                 Quantity = 2000
    //             },
    //         },
    //         new List<Price>
    //         {
    //             new Price("USD", 349),
    //             new Price("AED", 1399),
    //             new Price("AUD", 519),
    //             new Price("CAD", 459),
    //             new Price("CNY", 2539),
    //             new Price("EUR", 349),
    //             new Price("GBP", 299),
    //             new Price("HKD", 2799),
    //             new Price("SGD", 479),
    //         },
    //         PlanTypes.Subscription,
    //         "premium",
    //         "v9",
    //         new List<string>
    //         {
    //             "Active"
    //         },
    //         new Dictionary<string, object>(),
    //         string.Empty,
    //         string.Empty,
    //         null,
    //         DateTimeOffset.UtcNow,
    //         DateTimeOffset.UtcNow);
    //
    //     var usdPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(usdPlanId);
    //     var aedPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(aedPlanId);
    //     var audPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(audPlanId);
    //     var cadPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(cadPlanId);
    //     var cnyPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(cnyPlanId);
    //     var eurPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(eurPlanId);
    //     var gbpPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(gbpPlanId);
    //     var hkdPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(hkdPlanId);
    //     var sgdPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(sgdPlanId);
    //
    //     Assert.Multiple(() =>
    //     {
    //         Assert.That(usdPlanResult, Is.EqualTo(expectedPlanDefinition));
    //         Assert.That(aedPlanResult, Is.EqualTo(expectedPlanDefinition));
    //         Assert.That(audPlanResult, Is.EqualTo(expectedPlanDefinition));
    //         Assert.That(cadPlanResult, Is.EqualTo(expectedPlanDefinition));
    //         Assert.That(cnyPlanResult, Is.EqualTo(expectedPlanDefinition));
    //         Assert.That(eurPlanResult, Is.EqualTo(expectedPlanDefinition));
    //         Assert.That(gbpPlanResult, Is.EqualTo(expectedPlanDefinition));
    //         Assert.That(hkdPlanResult, Is.EqualTo(expectedPlanDefinition));
    //         Assert.That(sgdPlanResult, Is.EqualTo(expectedPlanDefinition));
    //     });
    // }

    // [Test]
    // public async Task GetPlanDefinition_ValidId_ReturnsPlanDefinition_44()
    // {
    //     var myrPlanId = "sleekflow_v9_premium_monthly_myr";
    //     var brlPlanId = "sleekflow_v9_premium_monthly_brl";
    //
    //     var expectedPlanDefinition = new PlanDefinition(
    //         new List<Multilingual>
    //         {
    //             new Multilingual("en", "premium_monthly", true)
    //         },
    //         new List<Multilingual>(),
    //         new List<FeatureQuantity>
    //         {
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "contacts",
    //                 Quantity = 20000
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "messages",
    //                 Quantity = 50000000
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "agents",
    //                 Quantity = 5
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "campaigns",
    //                 Quantity = 50000000
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "automations",
    //                 Quantity = 999
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "channels",
    //                 Quantity = 10
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "api_calls",
    //                 Quantity = 100000
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "priority_support",
    //                 Quantity = 1
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "chatbot_automation_support_basic",
    //                 Quantity = 1
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "ai_features_total_usage",
    //                 Quantity = 2000
    //             },
    //         },
    //         new List<Price>
    //         {
    //             new Price("MYR", 1459),
    //             new Price("BRL", 1899),
    //         },
    //         PlanTypes.Subscription,
    //         "premium",
    //         "v9",
    //         new List<string>
    //         {
    //             "Active"
    //         },
    //         new Dictionary<string, object>(),
    //         string.Empty,
    //         string.Empty,
    //         null,
    //         DateTimeOffset.UtcNow,
    //         DateTimeOffset.UtcNow);
    //
    //     var myrPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(myrPlanId);
    //     var brlPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(brlPlanId);
    //
    //     Assert.Multiple(() =>
    //     {
    //         Assert.That(myrPlanResult, Is.EqualTo(expectedPlanDefinition));
    //         Assert.That(brlPlanResult, Is.EqualTo(expectedPlanDefinition));
    //     });
    // }

    // [Test]
    // public async Task GetPlanDefinition_ValidId_ReturnsPlanDefinition_45()
    // {
    //     var usdPlanId = "sleekflow_v9_premium_yearly";
    //     var aedPlanId = "sleekflow_v9_premium_yearly_aed";
    //     var audPlanId = "sleekflow_v9_premium_yearly_aud";
    //     var cadPlanId = "sleekflow_v9_premium_yearly_cad";
    //     var cnyPlanId = "sleekflow_v9_premium_yearly_cny";
    //     var eurPlanId = "sleekflow_v9_premium_yearly_eur";
    //     var gbpPlanId = "sleekflow_v9_premium_yearly_gbp";
    //     var hkdPlanId = "sleekflow_v9_premium_yearly_hkd";
    //     var sgdPlanId = "sleekflow_v9_premium_yearly_sgd";
    //
    //     var expectedPlanDefinition = new PlanDefinition(
    //         new List<Multilingual>
    //         {
    //             new Multilingual("en", "premium_yearly", true)
    //         },
    //         new List<Multilingual>(),
    //         new List<FeatureQuantity>
    //         {
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "contacts",
    //                 Quantity = 10000
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "messages",
    //                 Quantity = 50000000
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "agents",
    //                 Quantity = 5
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "campaigns",
    //                 Quantity = 50000000
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "automations",
    //                 Quantity = 999
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "channels",
    //                 Quantity = 10
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "api_calls",
    //                 Quantity = 1200000
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "onboarding_support",
    //                 Quantity = 1
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "chatbot_automation_support_basic",
    //                 Quantity = 1
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "priority_support",
    //                 Quantity = 1
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "ai_features_total_usage",
    //                 Quantity = 2000
    //             },
    //         },
    //         new List<Price>
    //         {
    //             new Price("USD", 3588),
    //             new Price("AED", 11988),
    //             new Price("AUD", 5388),
    //             new Price("CAD", 4668),
    //             new Price("CNY", 23988),
    //             new Price("EUR", 3588),
    //             new Price("GBP", 3108),
    //             new Price("HKD", 28788),
    //             new Price("SGD", 4908),
    //         },
    //         PlanTypes.Subscription,
    //         "premium",
    //         "v9",
    //         new List<string>
    //         {
    //             "Active"
    //         },
    //         new Dictionary<string, object>(),
    //         string.Empty,
    //         string.Empty,
    //         null,
    //         DateTimeOffset.UtcNow,
    //         DateTimeOffset.UtcNow);
    //
    //     var usdPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(usdPlanId);
    //     var aedPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(aedPlanId);
    //     var audPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(audPlanId);
    //     var cadPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(cadPlanId);
    //     var cnyPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(cnyPlanId);
    //     var eurPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(eurPlanId);
    //     var gbpPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(gbpPlanId);
    //     var hkdPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(hkdPlanId);
    //     var sgdPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(sgdPlanId);
    //
    //     Assert.Multiple(() =>
    //     {
    //         Assert.That(usdPlanResult, Is.EqualTo(expectedPlanDefinition));
    //         Assert.That(aedPlanResult, Is.EqualTo(expectedPlanDefinition));
    //         Assert.That(audPlanResult, Is.EqualTo(expectedPlanDefinition));
    //         Assert.That(cadPlanResult, Is.EqualTo(expectedPlanDefinition));
    //         Assert.That(cnyPlanResult, Is.EqualTo(expectedPlanDefinition));
    //         Assert.That(eurPlanResult, Is.EqualTo(expectedPlanDefinition));
    //         Assert.That(gbpPlanResult, Is.EqualTo(expectedPlanDefinition));
    //         Assert.That(hkdPlanResult, Is.EqualTo(expectedPlanDefinition));
    //         Assert.That(sgdPlanResult, Is.EqualTo(expectedPlanDefinition));
    //     });
    // }

    // [Test]
    // public async Task GetPlanDefinition_ValidId_ReturnsPlanDefinition_46()
    // {
    //     var idrPlanId = "sleekflow_v9_premium_yearly_idr";
    //     var inrPlanId = "sleekflow_v9_premium_yearly_inr";
    //
    //     var expectedPlanDefinition = new PlanDefinition(
    //         new List<Multilingual>
    //         {
    //             new Multilingual("en", "premium_yearly", true)
    //         },
    //         new List<Multilingual>(),
    //         new List<FeatureQuantity>
    //         {
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "contacts",
    //                 Quantity = 40000
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "messages",
    //                 Quantity = 50000000
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "agents",
    //                 Quantity = 5
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "campaigns",
    //                 Quantity = 50000000
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "automations",
    //                 Quantity = 999
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "channels",
    //                 Quantity = 10
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "api_calls",
    //                 Quantity = 1200000
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "ai_features_total_usage",
    //                 Quantity = 2000
    //             },
    //         },
    //         new List<Price>
    //         {
    //             new Price("IDR", 50280000),
    //             new Price("INR", 155988),
    //         },
    //         PlanTypes.Subscription,
    //         "premium",
    //         "v9",
    //         new List<string>
    //         {
    //             "Active"
    //         },
    //         new Dictionary<string, object>(),
    //         string.Empty,
    //         string.Empty,
    //         null,
    //         DateTimeOffset.UtcNow,
    //         DateTimeOffset.UtcNow);
    //
    //     var idrPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(idrPlanId);
    //     var inrPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(inrPlanId);
    //
    //     Assert.Multiple(() =>
    //     {
    //         Assert.That(idrPlanResult, Is.EqualTo(expectedPlanDefinition));
    //         Assert.That(inrPlanResult, Is.EqualTo(expectedPlanDefinition));
    //     });
    // }

    // [Test]
    // public async Task GetPlanDefinition_ValidId_ReturnsPlanDefinition_47()
    // {
    //     var usdPlanId = "sleekflow_v9_pro_monthly";
    //     var audPlanId = "sleekflow_v9_pro_monthly_aud";
    //     var cadPlanId = "sleekflow_v9_pro_monthly_cad";
    //     var cnyPlanId = "sleekflow_v9_pro_monthly_cny";
    //     var eurPlanId = "sleekflow_v9_pro_monthly_eur";
    //     var gbpPlanId = "sleekflow_v9_pro_monthly_gbp";
    //     var hkdPlanId = "sleekflow_v9_pro_monthly_hkd";
    //     var sgdPlanId = "sleekflow_v9_pro_monthly_sgd";
    //
    //     var expectedPlanDefinition = new PlanDefinition(
    //         new List<Multilingual>
    //         {
    //             new Multilingual("en", "pro_monthly", true)
    //         },
    //         new List<Multilingual>(),
    //         new List<FeatureQuantity>
    //         {
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "contacts",
    //                 Quantity = 2000
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "messages",
    //                 Quantity = 5000
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "agents",
    //                 Quantity = 3
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "campaigns",
    //                 Quantity = 5000
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "automations",
    //                 Quantity = 10
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "channels",
    //                 Quantity = 5
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "api_calls",
    //                 Quantity = 5000
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "ai_features_total_usage",
    //                 Quantity = 2000
    //             },
    //         },
    //         new List<Price>
    //         {
    //             new Price("USD", 99),
    //             new Price("AUD", 149),
    //             new Price("CAD", 129),
    //             new Price("CNY", 729),
    //             new Price("EUR", 99),
    //             new Price("GBP", 89),
    //             new Price("HKD", 799),
    //             new Price("SGD", 139),
    //         },
    //         PlanTypes.Subscription,
    //         "pro",
    //         "v9",
    //         new List<string>
    //         {
    //             "Active"
    //         },
    //         new Dictionary<string, object>(),
    //         string.Empty,
    //         string.Empty,
    //         null,
    //         DateTimeOffset.UtcNow,
    //         DateTimeOffset.UtcNow);
    //
    //     var usdPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(usdPlanId);
    //     var audPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(audPlanId);
    //     var cadPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(cadPlanId);
    //     var cnyPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(cnyPlanId);
    //     var eurPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(eurPlanId);
    //     var gbpPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(gbpPlanId);
    //     var hkdPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(hkdPlanId);
    //     var sgdPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(sgdPlanId);
    //
    //     Assert.Multiple(() =>
    //     {
    //         Assert.That(usdPlanResult, Is.EqualTo(expectedPlanDefinition));
    //         Assert.That(audPlanResult, Is.EqualTo(expectedPlanDefinition));
    //         Assert.That(cadPlanResult, Is.EqualTo(expectedPlanDefinition));
    //         Assert.That(cnyPlanResult, Is.EqualTo(expectedPlanDefinition));
    //         Assert.That(eurPlanResult, Is.EqualTo(expectedPlanDefinition));
    //         Assert.That(gbpPlanResult, Is.EqualTo(expectedPlanDefinition));
    //         Assert.That(hkdPlanResult, Is.EqualTo(expectedPlanDefinition));
    //         Assert.That(sgdPlanResult, Is.EqualTo(expectedPlanDefinition));
    //     });
    // }

    // [Test]
    // public async Task GetPlanDefinition_ValidId_ReturnsPlanDefinition_48()
    // {
    //     var brlPlanId = "sleekflow_v9_pro_monthly_brl";
    //     var idrPlanId = "sleekflow_v9_pro_monthly_idr";
    //     var myrPlanId = "sleekflow_v9_pro_monthly_myr";
    //
    //     var expectedPlanDefinition = new PlanDefinition(
    //         new List<Multilingual>
    //         {
    //             new Multilingual("en", "pro_monthly", true)
    //         },
    //         new List<Multilingual>(),
    //         new List<FeatureQuantity>
    //         {
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "contacts",
    //                 Quantity = 5000
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "messages",
    //                 Quantity = 5000
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "agents",
    //                 Quantity = 3
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "campaigns",
    //                 Quantity = 5000
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "automations",
    //                 Quantity = 10
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "channels",
    //                 Quantity = 5
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "api_calls",
    //                 Quantity = 5000
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "ai_features_total_usage",
    //                 Quantity = 2000
    //             },
    //         },
    //         new List<Price>
    //         {
    //             new Price("BRL", 539),
    //             new Price("IDR", 1390000),
    //             new Price("MYR", 409),
    //         },
    //         PlanTypes.Subscription,
    //         "pro",
    //         "v9",
    //         new List<string>
    //         {
    //             "Active"
    //         },
    //         new Dictionary<string, object>(),
    //         string.Empty,
    //         string.Empty,
    //         null,
    //         DateTimeOffset.UtcNow,
    //         DateTimeOffset.UtcNow);
    //
    //     var brlPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(brlPlanId);
    //     var idrPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(idrPlanId);
    //     var myrPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(myrPlanId);
    //
    //     Assert.Multiple(() =>
    //     {
    //         Assert.That(brlPlanResult, Is.EqualTo(expectedPlanDefinition));
    //         Assert.That(idrPlanResult, Is.EqualTo(expectedPlanDefinition));
    //         Assert.That(myrPlanResult, Is.EqualTo(expectedPlanDefinition));
    //     });
    // }

    // [Test]
    // public async Task GetPlanDefinition_ValidId_ReturnsPlanDefinition_49()
    // {
    //     var inrPlanId = "sleekflow_v9_pro_monthly_inr";
    //
    //     var expectedPlanDefinition = new PlanDefinition(
    //         new List<Multilingual>
    //         {
    //             new Multilingual("en", "pro_monthly", true)
    //         },
    //         new List<Multilingual>(),
    //         new List<FeatureQuantity>
    //         {
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "contacts",
    //                 Quantity = 10000
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "messages",
    //                 Quantity = 5000
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "agents",
    //                 Quantity = 3
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "campaigns",
    //                 Quantity = 5000
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "automations",
    //                 Quantity = 10
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "channels",
    //                 Quantity = 5
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "ai_features_total_usage",
    //                 Quantity = 2000
    //             },
    //         },
    //         new List<Price>
    //         {
    //             new Price("INR", 4199),
    //         },
    //         PlanTypes.Subscription,
    //         "pro",
    //         "v9",
    //         new List<string>
    //         {
    //             "Active"
    //         },
    //         new Dictionary<string, object>(),
    //         string.Empty,
    //         string.Empty,
    //         null,
    //         DateTimeOffset.UtcNow,
    //         DateTimeOffset.UtcNow);
    //
    //     var inrPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(inrPlanId);
    //
    //     Assert.Multiple(() =>
    //     {
    //         Assert.That(inrPlanResult, Is.EqualTo(expectedPlanDefinition));
    //     });
    // }

    // [Test]
    // public async Task GetPlanDefinition_ValidId_ReturnsPlanDefinition_50()
    // {
    //     var usdPlanId = "sleekflow_v9_pro_yearly";
    //     var audPlanId = "sleekflow_v9_pro_yearly_aud";
    //     var cadPlanId = "sleekflow_v9_pro_yearly_cad";
    //     var cnyPlanId = "sleekflow_v9_pro_yearly_cny";
    //     var eurPlanId = "sleekflow_v9_pro_yearly_eur";
    //     var gbpPlanId = "sleekflow_v9_pro_yearly_gbp";
    //     var hkdPlanId = "sleekflow_v9_pro_yearly_hkd";
    //     var sgdPlanId = "sleekflow_v9_pro_yearly_sgd";
    //
    //     var expectedPlanDefinition = new PlanDefinition(
    //         new List<Multilingual>
    //         {
    //             new Multilingual("en", "pro_yearly", true)
    //         },
    //         new List<Multilingual>(),
    //         new List<FeatureQuantity>
    //         {
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "contacts",
    //                 Quantity = 2000
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "messages",
    //                 Quantity = 60000
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "agents",
    //                 Quantity = 3
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "campaigns",
    //                 Quantity = 60000
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "automations",
    //                 Quantity = 10
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "channels",
    //                 Quantity = 5
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "api_calls",
    //                 Quantity = 60000
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "onboarding_support",
    //                 Quantity = 1
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "chatbot_automation_support_basic",
    //                 Quantity = 1
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "ai_features_total_usage",
    //                 Quantity = 2000
    //             },
    //         },
    //         new List<Price>
    //         {
    //             new Price("USD", 948),
    //             new Price("AUD", 1428),
    //             new Price("CAD", 1308),
    //             new Price("CNY", 6588),
    //             new Price("EUR", 948),
    //             new Price("GBP", 828),
    //             new Price("HKD", 7188),
    //             new Price("SGD", 1308),
    //         },
    //         PlanTypes.Subscription,
    //         "pro",
    //         "v9",
    //         new List<string>
    //         {
    //             "Active"
    //         },
    //         new Dictionary<string, object>(),
    //         string.Empty,
    //         string.Empty,
    //         null,
    //         DateTimeOffset.UtcNow,
    //         DateTimeOffset.UtcNow);
    //
    //     var usdPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(usdPlanId);
    //     var audPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(audPlanId);
    //     var cadPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(cadPlanId);
    //     var cnyPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(cnyPlanId);
    //     var eurPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(eurPlanId);
    //     var gbpPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(gbpPlanId);
    //     var hkdPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(hkdPlanId);
    //     var sgdPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(sgdPlanId);
    //
    //     Assert.Multiple(() =>
    //     {
    //         Assert.That(usdPlanResult, Is.EqualTo(expectedPlanDefinition));
    //         Assert.That(audPlanResult, Is.EqualTo(expectedPlanDefinition));
    //         Assert.That(cadPlanResult, Is.EqualTo(expectedPlanDefinition));
    //         Assert.That(cnyPlanResult, Is.EqualTo(expectedPlanDefinition));
    //         Assert.That(eurPlanResult, Is.EqualTo(expectedPlanDefinition));
    //         Assert.That(gbpPlanResult, Is.EqualTo(expectedPlanDefinition));
    //         Assert.That(hkdPlanResult, Is.EqualTo(expectedPlanDefinition));
    //         Assert.That(sgdPlanResult, Is.EqualTo(expectedPlanDefinition));
    //     });
    // }

    // [Test]
    // public async Task GetPlanDefinition_ValidId_ReturnsPlanDefinition_51()
    // {
    //     var brlPlanId = "sleekflow_v9_pro_yearly_brl";
    //     var idrPlanId = "sleekflow_v9_pro_yearly_idr";
    //     var myrPlanId = "sleekflow_v9_pro_yearly_myr";
    //
    //     var expectedPlanDefinition = new PlanDefinition(
    //         new List<Multilingual>
    //         {
    //             new Multilingual("en", "pro_yearly", true)
    //         },
    //         new List<Multilingual>(),
    //         new List<FeatureQuantity>
    //         {
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "contacts",
    //                 Quantity = 5000
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "messages",
    //                 Quantity = 60000
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "agents",
    //                 Quantity = 3
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "campaigns",
    //                 Quantity = 60000
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "automations",
    //                 Quantity = 10
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "channels",
    //                 Quantity = 5
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "api_calls",
    //                 Quantity = 60000
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "onboarding_support",
    //                 Quantity = 1
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "chatbot_automation_support_basic",
    //                 Quantity = 1
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "ai_features_total_usage",
    //                 Quantity = 2000
    //             },
    //         },
    //         new List<Price>
    //         {
    //             new Price("BRL", 5148),
    //             new Price("IDR", 13080000),
    //             new Price("MYR", 3948),
    //         },
    //         PlanTypes.Subscription,
    //         "pro",
    //         "v9",
    //         new List<string>
    //         {
    //             "Active"
    //         },
    //         new Dictionary<string, object>(),
    //         string.Empty,
    //         string.Empty,
    //         null,
    //         DateTimeOffset.UtcNow,
    //         DateTimeOffset.UtcNow);
    //
    //     var brlPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(brlPlanId);
    //     var idrPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(idrPlanId);
    //     var myrPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(myrPlanId);
    //
    //     Assert.Multiple(() =>
    //     {
    //         Assert.That(brlPlanResult, Is.EqualTo(expectedPlanDefinition));
    //         Assert.That(idrPlanResult, Is.EqualTo(expectedPlanDefinition));
    //         Assert.That(myrPlanResult, Is.EqualTo(expectedPlanDefinition));
    //     });
    // }

    // [Test]
    // public async Task GetPlanDefinition_ValidId_ReturnsPlanDefinition_52()
    // {
    //     var inrPlanId = "sleekflow_v9_pro_yearly_inr";
    //
    //     var expectedPlanDefinition = new PlanDefinition(
    //         new List<Multilingual>
    //         {
    //             new Multilingual("en", "pro_yearly", true)
    //         },
    //         new List<Multilingual>(),
    //         new List<FeatureQuantity>
    //         {
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "contacts",
    //                 Quantity = 10000
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "messages",
    //                 Quantity = 60000
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "agents",
    //                 Quantity = 3
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "campaigns",
    //                 Quantity = 60000
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "automations",
    //                 Quantity = 10
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "channels",
    //                 Quantity = 5
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "onboarding_support",
    //                 Quantity = 1
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "chatbot_automation_support_basic",
    //                 Quantity = 1
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "ai_features_total_usage",
    //                 Quantity = 2000
    //             },
    //         },
    //         new List<Price>
    //         {
    //             new Price("INR", 40788),
    //         },
    //         PlanTypes.Subscription,
    //         "pro",
    //         "v9",
    //         new List<string>
    //         {
    //             "Active"
    //         },
    //         new Dictionary<string, object>(),
    //         string.Empty,
    //         string.Empty,
    //         null,
    //         DateTimeOffset.UtcNow,
    //         DateTimeOffset.UtcNow);
    //
    //     var inrPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(inrPlanId);
    //
    //     Assert.Multiple(() =>
    //     {
    //         Assert.That(inrPlanResult, Is.EqualTo(expectedPlanDefinition));
    //     });
    // }

    [Test]
    public async Task GetPlanDefinition_ValidId_ReturnsPlanDefinition_53()
    {
        var gbpPlanId = "sleekflow_v8_additional_15k_broadcast";

        var expectedPlanDefinition = new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "additional_15k_broadcast", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "campaigns",
                    Quantity = 15000
                },
            },
            new List<Price>
            {
                new Price("GBP", 110),
            },
            PlanTypes.AddOns,
            null,
            "v8",
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow);

        var gbpPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(gbpPlanId);

        Assert.Multiple(() =>
        {
            Assert.That(gbpPlanResult, Is.EqualTo(expectedPlanDefinition));
        });
    }

  //   [Test]
  //   public async Task GetPlanDefinition_ValidId_ReturnsPlanDefinition_54()
  //   {
  //       var usdPlanId = "sleekflow_v8_premium_monthly";
  //       var audPlanId = "sleekflow_v8_premium_monthly_aud";
  //       var cadPlanId = "sleekflow_v8_premium_monthly_cad";
  //       var cnyPlanId = "sleekflow_v8_premium_monthly_cny";
  //       var eurPlanId = "sleekflow_v8_premium_monthly_eur";
  //       var gbpPlanId = "sleekflow_v8_premium_monthly_gbp";
  //       var hkdPlanId = "sleekflow_v8_premium_monthly_hkd";
  //       var sgdPlanId = "sleekflow_v8_premium_monthly_sgd";
		// var myrPlanId = "sleekflow_v8_premium_monthly_myr";
		// var idrPlanId = "sleekflow_v8_premium_monthly_idr";
  //
  //       var expectedPlanDefinition = new PlanDefinition(
  //           new List<Multilingual>
  //           {
  //               new Multilingual("en", "premium_monthly", true)
  //           },
  //           new List<Multilingual>(),
  //           new List<FeatureQuantity>
  //           {
  //               new FeatureQuantity
  //               {
  //                   FeatureId = "contacts",
  //                   Quantity = 10000
  //               },
  //               new FeatureQuantity
  //               {
  //                   FeatureId = "messages",
  //                   Quantity = 50000000
  //               },
  //               new FeatureQuantity
  //               {
  //                   FeatureId = "agents",
  //                   Quantity = 5
  //               },
  //               new FeatureQuantity
  //               {
  //                   FeatureId = "campaigns",
  //                   Quantity = 50000000
  //               },
  //               new FeatureQuantity
  //               {
  //                   FeatureId = "automations",
  //                   Quantity = 999
  //               },
  //               new FeatureQuantity
  //               {
  //                   FeatureId = "channels",
  //                   Quantity = 999
  //               },
  //               new FeatureQuantity
  //               {
  //                   FeatureId = "api_calls",
  //                   Quantity = 100000
  //               },
  //               new FeatureQuantity
  //               {
  //                   FeatureId = "ai_features_total_usage",
  //                   Quantity = 2000
  //               },
  //           },
  //           new List<Price>
  //           {
  //               new Price("USD", 349),
  //               new Price("AUD", 479),
  //               new Price("CAD", 439),
  //               new Price("CNY", 2239),
  //               new Price("EUR", 309),
  //               new Price("GBP", 269),
  //               new Price("HKD", 2799),
  //               new Price("IDR", 4890000),
  //               new Price("MYR", 1459),
  //               new Price("SGD", 479),
  //           },
  //           PlanTypes.Subscription,
  //           "premium",
  //           "v8",
  //           new List<string>
  //           {
  //               "Active"
  //           },
  //           new Dictionary<string, object>(),
  //           string.Empty,
  //           string.Empty,
  //           null,
  //           DateTimeOffset.UtcNow,
  //           DateTimeOffset.UtcNow);
  //
  //       var usdPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(usdPlanId);
  //       var audPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(audPlanId);
  //       var cadPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(cadPlanId);
  //       var cnyPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(cnyPlanId);
  //       var eurPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(eurPlanId);
  //       var gbpPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(gbpPlanId);
  //       var hkdPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(hkdPlanId);
  //       var sgdPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(sgdPlanId);
		// var idrPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(idrPlanId);
		// var myrPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(myrPlanId);
  //
  //       Assert.Multiple(() =>
  //       {
  //           Assert.That(usdPlanResult, Is.EqualTo(expectedPlanDefinition));
  //           Assert.That(audPlanResult, Is.EqualTo(expectedPlanDefinition));
  //           Assert.That(cadPlanResult, Is.EqualTo(expectedPlanDefinition));
  //           Assert.That(cnyPlanResult, Is.EqualTo(expectedPlanDefinition));
  //           Assert.That(eurPlanResult, Is.EqualTo(expectedPlanDefinition));
  //           Assert.That(gbpPlanResult, Is.EqualTo(expectedPlanDefinition));
  //           Assert.That(hkdPlanResult, Is.EqualTo(expectedPlanDefinition));
  //           Assert.That(sgdPlanResult, Is.EqualTo(expectedPlanDefinition));
		// 	Assert.That(idrPlanResult, Is.EqualTo(expectedPlanDefinition));
		// 	Assert.That(myrPlanResult, Is.EqualTo(expectedPlanDefinition));
  //       });
  //   }
  //
  //   [Test]
  //   public async Task GetPlanDefinition_ValidId_ReturnsPlanDefinition_55()
  //   {
  //       var usdPlanId = "sleekflow_v8_premium_yearly";
  //       var audPlanId = "sleekflow_v8_premium_yearly_aud";
  //       var cadPlanId = "sleekflow_v8_premium_yearly_cad";
  //       var cnyPlanId = "sleekflow_v8_premium_yearly_cny";
  //       var eurPlanId = "sleekflow_v8_premium_yearly_eur";
  //       var gbpPlanId = "sleekflow_v8_premium_yearly_gbp";
  //       var hkdPlanId = "sleekflow_v8_premium_yearly_hkd";
  //       var sgdPlanId = "sleekflow_v8_premium_yearly_sgd";
		// var myrPlanId = "sleekflow_v8_premium_yearly_myr";
		// var idrPlanId = "sleekflow_v8_premium_yearly_idr";
  //
  //       var expectedPlanDefinition = new PlanDefinition(
  //           new List<Multilingual>
  //           {
  //               new Multilingual("en", "premium_yearly", true)
  //           },
  //           new List<Multilingual>(),
  //           new List<FeatureQuantity>
  //           {
  //               new FeatureQuantity
  //               {
  //                   FeatureId = "contacts",
  //                   Quantity = 10000
  //               },
  //               new FeatureQuantity
  //               {
  //                   FeatureId = "messages",
  //                   Quantity = 50000000
  //               },
  //               new FeatureQuantity
  //               {
  //                   FeatureId = "agents",
  //                   Quantity = 5
  //               },
  //               new FeatureQuantity
  //               {
  //                   FeatureId = "campaigns",
  //                   Quantity = 50000000
  //               },
  //               new FeatureQuantity
  //               {
  //                   FeatureId = "automations",
  //                   Quantity = 999
  //               },
  //               new FeatureQuantity
  //               {
  //                   FeatureId = "channels",
  //                   Quantity = 999
  //               },
  //               new FeatureQuantity
  //               {
  //                   FeatureId = "api_calls",
  //                   Quantity = 1200000
  //               },
  //               new FeatureQuantity
  //               {
  //                   FeatureId = "ai_features_total_usage",
  //                   Quantity = 2000
  //               },
  //           },
  //           new List<Price>
  //           {
  //               new Price("USD", 3588),
  //               new Price("AUD", 5028),
  //               new Price("CAD", 4548),
  //               new Price("CNY", 23148),
  //               new Price("EUR", 3228),
  //               new Price("GBP", 2748),
  //               new Price("HKD", 28788),
  //               new Price("IDR", 50280000),
  //               new Price("MYR", 14988),
  //               new Price("SGD", 4908),
  //           },
  //           PlanTypes.Subscription,
  //           "premium",
  //           "v8",
  //           new List<string>
  //           {
  //               "Active"
  //           },
  //           new Dictionary<string, object>(),
  //           string.Empty,
  //           string.Empty,
  //           null,
  //           DateTimeOffset.UtcNow,
  //           DateTimeOffset.UtcNow);
  //
  //       var usdPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(usdPlanId);
  //       var audPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(audPlanId);
  //       var cadPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(cadPlanId);
  //       var cnyPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(cnyPlanId);
  //       var eurPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(eurPlanId);
  //       var gbpPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(gbpPlanId);
  //       var hkdPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(hkdPlanId);
  //       var sgdPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(sgdPlanId);
		// var idrPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(idrPlanId);
		// var myrPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(myrPlanId);
  //
  //       Assert.Multiple(() =>
  //       {
  //           Assert.That(usdPlanResult, Is.EqualTo(expectedPlanDefinition));
  //           Assert.That(audPlanResult, Is.EqualTo(expectedPlanDefinition));
  //           Assert.That(cadPlanResult, Is.EqualTo(expectedPlanDefinition));
  //           Assert.That(cnyPlanResult, Is.EqualTo(expectedPlanDefinition));
  //           Assert.That(eurPlanResult, Is.EqualTo(expectedPlanDefinition));
  //           Assert.That(gbpPlanResult, Is.EqualTo(expectedPlanDefinition));
  //           Assert.That(hkdPlanResult, Is.EqualTo(expectedPlanDefinition));
  //           Assert.That(sgdPlanResult, Is.EqualTo(expectedPlanDefinition));
		// 	Assert.That(idrPlanResult, Is.EqualTo(expectedPlanDefinition));
		// 	Assert.That(myrPlanResult, Is.EqualTo(expectedPlanDefinition));
  //       });
  //   }
  //
  //   [Test]
  //   public async Task GetPlanDefinition_ValidId_ReturnsPlanDefinition_56()
  //   {
  //       var usdPlanId = "sleekflow_v8_pro_monthly";
  //       var audPlanId = "sleekflow_v8_pro_monthly_aud";
  //       var cadPlanId = "sleekflow_v8_pro_monthly_cad";
  //       var cnyPlanId = "sleekflow_v8_pro_monthly_cny";
  //       var eurPlanId = "sleekflow_v8_pro_monthly_eur";
  //       var gbpPlanId = "sleekflow_v8_pro_monthly_gbp";
  //       var hkdPlanId = "sleekflow_v8_pro_monthly_hkd";
  //       var sgdPlanId = "sleekflow_v8_pro_monthly_sgd";
		// var myrPlanId = "sleekflow_v8_pro_monthly_myr";
		// var idrPlanId = "sleekflow_v8_pro_monthly_idr";
  //
  //       var expectedPlanDefinition = new PlanDefinition(
  //           new List<Multilingual>
  //           {
  //               new Multilingual("en", "pro_monthly", true)
  //           },
  //           new List<Multilingual>(),
  //           new List<FeatureQuantity>
  //           {
  //               new FeatureQuantity
  //               {
  //                   FeatureId = "contacts",
  //                   Quantity = 2000
  //               },
  //               new FeatureQuantity
  //               {
  //                   FeatureId = "messages",
  //                   Quantity = 5000
  //               },
  //               new FeatureQuantity
  //               {
  //                   FeatureId = "agents",
  //                   Quantity = 3
  //               },
  //               new FeatureQuantity
  //               {
  //                   FeatureId = "campaigns",
  //                   Quantity = 5000
  //               },
  //               new FeatureQuantity
  //               {
  //                   FeatureId = "automations",
  //                   Quantity = 5
  //               },
  //               new FeatureQuantity
  //               {
  //                   FeatureId = "channels",
  //                   Quantity = 999
  //               },
  //               new FeatureQuantity
  //               {
  //                   FeatureId = "api_calls",
  //                   Quantity = 5000
  //               },
  //               new FeatureQuantity
  //               {
  //                   FeatureId = "ai_features_total_usage",
  //                   Quantity = 2000
  //               },
  //           },
  //           new List<Price>
  //           {
  //               new Price("USD", 99),
  //               new Price("AUD", 139),
  //               new Price("CAD", 129),
  //               new Price("CNY", 639),
  //               new Price("EUR", 89),
  //               new Price("GBP", 79),
  //               new Price("HKD", 799),
  //               new Price("IDR", 1390000),
  //               new Price("MYR", 409),
  //               new Price("SGD", 139),
  //           },
  //           PlanTypes.Subscription,
  //           "pro",
  //           "v8",
  //           new List<string>
  //           {
  //               "Active"
  //           },
  //           new Dictionary<string, object>(),
  //           string.Empty,
  //           string.Empty,
  //           null,
  //           DateTimeOffset.UtcNow,
  //           DateTimeOffset.UtcNow);
  //
  //       var usdPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(usdPlanId);
  //       var audPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(audPlanId);
  //       var cadPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(cadPlanId);
  //       var cnyPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(cnyPlanId);
  //       var eurPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(eurPlanId);
  //       var gbpPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(gbpPlanId);
  //       var hkdPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(hkdPlanId);
  //       var sgdPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(sgdPlanId);
		// var idrPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(idrPlanId);
		// var myrPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(myrPlanId);
  //
  //       Assert.Multiple(() =>
  //       {
  //           Assert.That(usdPlanResult, Is.EqualTo(expectedPlanDefinition));
  //           Assert.That(audPlanResult, Is.EqualTo(expectedPlanDefinition));
  //           Assert.That(cadPlanResult, Is.EqualTo(expectedPlanDefinition));
  //           Assert.That(cnyPlanResult, Is.EqualTo(expectedPlanDefinition));
  //           Assert.That(eurPlanResult, Is.EqualTo(expectedPlanDefinition));
  //           Assert.That(gbpPlanResult, Is.EqualTo(expectedPlanDefinition));
  //           Assert.That(hkdPlanResult, Is.EqualTo(expectedPlanDefinition));
  //           Assert.That(sgdPlanResult, Is.EqualTo(expectedPlanDefinition));
		// 	Assert.That(idrPlanResult, Is.EqualTo(expectedPlanDefinition));
		// 	Assert.That(myrPlanResult, Is.EqualTo(expectedPlanDefinition));
  //       });
  //   }
  //
  //   [Test]
  //   public async Task GetPlanDefinition_ValidId_ReturnsPlanDefinition_57()
  //   {
  //       var usdPlanId = "sleekflow_v8_pro_yearly";
  //       var audPlanId = "sleekflow_v8_pro_yearly_aud";
  //       var cadPlanId = "sleekflow_v8_pro_yearly_cad";
  //       var cnyPlanId = "sleekflow_v8_pro_yearly_cny";
  //       var eurPlanId = "sleekflow_v8_pro_yearly_eur";
  //       var gbpPlanId = "sleekflow_v8_pro_yearly_gbp";
  //       var hkdPlanId = "sleekflow_v8_pro_yearly_hkd";
  //       var sgdPlanId = "sleekflow_v8_pro_yearly_sgd";
		// var myrPlanId = "sleekflow_v8_pro_yearly_myr";
		// var idrPlanId = "sleekflow_v8_pro_yearly_idr";
  //
  //       var expectedPlanDefinition = new PlanDefinition(
  //           new List<Multilingual>
  //           {
  //               new Multilingual("en", "pro_yearly", true)
  //           },
  //           new List<Multilingual>(),
  //           new List<FeatureQuantity>
  //           {
  //               new FeatureQuantity
  //               {
  //                   FeatureId = "contacts",
  //                   Quantity = 2000
  //               },
  //               new FeatureQuantity
  //               {
  //                   FeatureId = "messages",
  //                   Quantity = 60000
  //               },
  //               new FeatureQuantity
  //               {
  //                   FeatureId = "agents",
  //                   Quantity = 3
  //               },
  //               new FeatureQuantity
  //               {
  //                   FeatureId = "campaigns",
  //                   Quantity = 60000
  //               },
  //               new FeatureQuantity
  //               {
  //                   FeatureId = "automations",
  //                   Quantity = 5
  //               },
  //               new FeatureQuantity
  //               {
  //                   FeatureId = "channels",
  //                   Quantity = 999
  //               },
  //               new FeatureQuantity
  //               {
  //                   FeatureId = "api_calls",
  //                   Quantity = 60000
  //               },
  //               new FeatureQuantity
  //               {
  //                   FeatureId = "ai_features_total_usage",
  //                   Quantity = 2000
  //               },
  //           },
  //           new List<Price>
  //           {
  //               new Price("USD", 948),
  //               new Price("AUD", 1308),
  //               new Price("CAD", 1188),
  //               new Price("CNY", 6108),
  //               new Price("EUR", 828),
  //               new Price("GBP", 708),
  //               new Price("HKD", 7188),
  //               new Price("IDR", 13080000),
  //               new Price("MYR", 3948),
  //               new Price("SGD", 1308),
  //           },
  //           PlanTypes.Subscription,
  //           "pro",
  //           "v8",
  //           new List<string>
  //           {
  //               "Active"
  //           },
  //           new Dictionary<string, object>(),
  //           string.Empty,
  //           string.Empty,
  //           null,
  //           DateTimeOffset.UtcNow,
  //           DateTimeOffset.UtcNow);
  //
  //       var usdPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(usdPlanId);
  //       var audPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(audPlanId);
  //       var cadPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(cadPlanId);
  //       var cnyPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(cnyPlanId);
  //       var eurPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(eurPlanId);
  //       var gbpPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(gbpPlanId);
  //       var hkdPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(hkdPlanId);
  //       var sgdPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(sgdPlanId);
		// var idrPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(idrPlanId);
		// var myrPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(myrPlanId);
  //
  //       Assert.Multiple(() =>
  //       {
  //           Assert.That(usdPlanResult, Is.EqualTo(expectedPlanDefinition));
  //           Assert.That(audPlanResult, Is.EqualTo(expectedPlanDefinition));
  //           Assert.That(cadPlanResult, Is.EqualTo(expectedPlanDefinition));
  //           Assert.That(cnyPlanResult, Is.EqualTo(expectedPlanDefinition));
  //           Assert.That(eurPlanResult, Is.EqualTo(expectedPlanDefinition));
  //           Assert.That(gbpPlanResult, Is.EqualTo(expectedPlanDefinition));
  //           Assert.That(hkdPlanResult, Is.EqualTo(expectedPlanDefinition));
  //           Assert.That(sgdPlanResult, Is.EqualTo(expectedPlanDefinition));
		// 	Assert.That(idrPlanResult, Is.EqualTo(expectedPlanDefinition));
		// 	Assert.That(myrPlanResult, Is.EqualTo(expectedPlanDefinition));
  //       });
  //   }
  //
  //   [Test]
  //   public async Task GetPlanDefinition_ValidId_ReturnsPlanDefinition_58()
  //   {
  //       var usdPlanId = "sleekflow_v7_premium_monthly";
  //
  //       var expectedPlanDefinition = new PlanDefinition(
  //           new List<Multilingual>
  //           {
  //               new Multilingual("en", "premium_monthly", true)
  //           },
  //           new List<Multilingual>(),
  //           new List<FeatureQuantity>
  //           {
  //               new FeatureQuantity
  //               {
  //                   FeatureId = "contacts",
  //                   Quantity = 10000
  //               },
  //               new FeatureQuantity
  //               {
  //                   FeatureId = "messages",
  //                   Quantity = 5000000
  //               },
  //               new FeatureQuantity
  //               {
  //                   FeatureId = "agents",
  //                   Quantity = 5
  //               },
  //               new FeatureQuantity
  //               {
  //                   FeatureId = "campaigns",
  //                   Quantity = 5000000
  //               },
  //               new FeatureQuantity
  //               {
  //                   FeatureId = "automations",
  //                   Quantity = 999
  //               },
  //               new FeatureQuantity
  //               {
  //                   FeatureId = "channels",
  //                   Quantity = 999
  //               },
  //               new FeatureQuantity
  //               {
  //                   FeatureId = "api_calls",
  //                   Quantity = 100000
  //               },
  //               new FeatureQuantity
  //               {
  //                   FeatureId = "ai_features_total_usage",
  //                   Quantity = 2000
  //               },
  //           },
  //           new List<Price>
  //           {
  //               new Price("USD", 349),
  //           },
  //           PlanTypes.Subscription,
  //           "premium",
  //           "v7",
  //           new List<string>
  //           {
  //               "Active"
  //           },
  //           new Dictionary<string, object>(),
  //           string.Empty,
  //           string.Empty,
  //           null,
  //           DateTimeOffset.UtcNow,
  //           DateTimeOffset.UtcNow);
  //
  //       var usdPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(usdPlanId);
  //
  //       Assert.Multiple(() =>
  //       {
  //           Assert.That(usdPlanResult, Is.EqualTo(expectedPlanDefinition));
  //       });
  //   }
  //
  //   [Test]
  //   public async Task GetPlanDefinition_ValidId_ReturnsPlanDefinition_59()
  //   {
  //       var sgdPlanId = "sleekflow_v7_premium_monthly_sg";
  //
  //       var expectedPlanDefinition = new PlanDefinition(
  //           new List<Multilingual>
  //           {
  //               new Multilingual("en", "premium_monthly_sg", true)
  //           },
  //           new List<Multilingual>(),
  //           new List<FeatureQuantity>
  //           {
  //               new FeatureQuantity
  //               {
  //                   FeatureId = "contacts",
  //                   Quantity = 10000
  //               },
  //               new FeatureQuantity
  //               {
  //                   FeatureId = "messages",
  //                   Quantity = 50000000
  //               },
  //               new FeatureQuantity
  //               {
  //                   FeatureId = "agents",
  //                   Quantity = 5
  //               },
  //               new FeatureQuantity
  //               {
  //                   FeatureId = "campaigns",
  //                   Quantity = 50000000
  //               },
  //               new FeatureQuantity
  //               {
  //                   FeatureId = "automations",
  //                   Quantity = 999
  //               },
  //               new FeatureQuantity
  //               {
  //                   FeatureId = "channels",
  //                   Quantity = 999
  //               },
  //               new FeatureQuantity
  //               {
  //                   FeatureId = "api_calls",
  //                   Quantity = 100000
  //               },
  //               new FeatureQuantity
  //               {
  //                   FeatureId = "ai_features_total_usage",
  //                   Quantity = 2000
  //               },
  //           },
  //           new List<Price>
  //           {
  //               new Price("SGD", 479),
  //           },
  //           PlanTypes.Subscription,
  //           "premium",
  //           "v7",
  //           new List<string>
  //           {
  //               "Active"
  //           },
  //           new Dictionary<string, object>(),
  //           string.Empty,
  //           string.Empty,
  //           null,
  //           DateTimeOffset.UtcNow,
  //           DateTimeOffset.UtcNow);
  //
  //       var sgdPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(sgdPlanId);
  //
  //       Assert.Multiple(() =>
  //       {
  //           Assert.That(sgdPlanResult, Is.EqualTo(expectedPlanDefinition));
  //       });
  //   }

    // [Test]
    // public async Task GetPlanDefinition_ValidId_ReturnsPlanDefinition_60()
    // {
    //     var usdPlanId = "sleekflow_v7_premium_yearly";
    //
    //     var expectedPlanDefinition = new PlanDefinition(
    //         new List<Multilingual>
    //         {
    //             new Multilingual("en", "premium_yearly", true)
    //         },
    //         new List<Multilingual>(),
    //         new List<FeatureQuantity>
    //         {
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "contacts",
    //                 Quantity = 10000
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "messages",
    //                 Quantity = 5000000
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "agents",
    //                 Quantity = 5
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "campaigns",
    //                 Quantity = 5000000
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "automations",
    //                 Quantity = 999
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "channels",
    //                 Quantity = 999
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "api_calls",
    //                 Quantity = 1200000
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "ai_features_total_usage",
    //                 Quantity = 2000
    //             },
    //         },
    //         new List<Price>
    //         {
    //             new Price("USD", 3588),
    //         },
    //         PlanTypes.Subscription,
    //         "premium",
    //         "v7",
    //         new List<string>
    //         {
    //             "Active"
    //         },
    //         new Dictionary<string, object>(),
    //         string.Empty,
    //         string.Empty,
    //         null,
    //         DateTimeOffset.UtcNow,
    //         DateTimeOffset.UtcNow);
    //
    //     var usdPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(usdPlanId);
    //
    //     Assert.Multiple(() =>
    //     {
    //         Assert.That(usdPlanResult, Is.EqualTo(expectedPlanDefinition));
    //     });
    // }
    //
    // [Test]
    // public async Task GetPlanDefinition_ValidId_ReturnsPlanDefinition_61()
    // {
    //     var usdPlanId = "sleekflow_v7_pro_monthly";
    //
    //     var expectedPlanDefinition = new PlanDefinition(
    //         new List<Multilingual>
    //         {
    //             new Multilingual("en", "pro_monthly", true)
    //         },
    //         new List<Multilingual>(),
    //         new List<FeatureQuantity>
    //         {
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "contacts",
    //                 Quantity = 2000
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "messages",
    //                 Quantity = 5000
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "agents",
    //                 Quantity = 3
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "campaigns",
    //                 Quantity = 5000
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "automations",
    //                 Quantity = 5
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "channels",
    //                 Quantity = 999
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "api_calls",
    //                 Quantity = 5000
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "ai_features_total_usage",
    //                 Quantity = 2000
    //             },
    //         },
    //         new List<Price>
    //         {
    //             new Price("USD", 99),
    //         },
    //         PlanTypes.Subscription,
    //         "pro",
    //         "v7",
    //         new List<string>
    //         {
    //             "Active"
    //         },
    //         new Dictionary<string, object>(),
    //         string.Empty,
    //         string.Empty,
    //         null,
    //         DateTimeOffset.UtcNow,
    //         DateTimeOffset.UtcNow);
    //
    //     var usdPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(usdPlanId);
    //
    //     Assert.Multiple(() =>
    //     {
    //         Assert.That(usdPlanResult, Is.EqualTo(expectedPlanDefinition));
    //     });
    // }
    //
    // [Test]
    // public async Task GetPlanDefinition_ValidId_ReturnsPlanDefinition_62()
    // {
    //     var hkdPlanId = "sleekflow_v7_pro_monthly_hkd";
    //     var sgdPlanId = "sleekflow_v7_pro_monthly_sgd";
    //
    //     var expectedPlanDefinition = new PlanDefinition(
    //         new List<Multilingual>
    //         {
    //             new Multilingual("en", "pro_monthly", true)
    //         },
    //         new List<Multilingual>(),
    //         new List<FeatureQuantity>
    //         {
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "contacts",
    //                 Quantity = 2000
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "messages",
    //                 Quantity = 5000
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "agents",
    //                 Quantity = 3
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "campaigns",
    //                 Quantity = 5000
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "automations",
    //                 Quantity = 999
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "channels",
    //                 Quantity = 999
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "api_calls",
    //                 Quantity = 5000
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "ai_features_total_usage",
    //                 Quantity = 2000
    //             },
    //         },
    //         new List<Price>
    //         {
    //             new Price("HKD", 780),
    //             new Price("SGD", 139),
    //         },
    //         PlanTypes.Subscription,
    //         "pro",
    //         "v7",
    //         new List<string>
    //         {
    //             "Active"
    //         },
    //         new Dictionary<string, object>(),
    //         string.Empty,
    //         string.Empty,
    //         null,
    //         DateTimeOffset.UtcNow,
    //         DateTimeOffset.UtcNow);
    //
    //     var hkdPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(hkdPlanId);
    //     var sgdPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(sgdPlanId);
    //
    //     Assert.Multiple(() =>
    //     {
    //         Assert.That(hkdPlanResult, Is.EqualTo(expectedPlanDefinition));
    //         Assert.That(sgdPlanResult, Is.EqualTo(expectedPlanDefinition));
    //     });
    // }
    //
    // [Test]
    // public async Task GetPlanDefinition_ValidId_ReturnsPlanDefinition_63()
    // {
    //     var usdPlanId = "sleekflow_v7_pro_yearly";
    //
    //     var expectedPlanDefinition = new PlanDefinition(
    //         new List<Multilingual>
    //         {
    //             new Multilingual("en", "pro_yearly", true)
    //         },
    //         new List<Multilingual>(),
    //         new List<FeatureQuantity>
    //         {
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "contacts",
    //                 Quantity = 2000
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "messages",
    //                 Quantity = 60000
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "agents",
    //                 Quantity = 3
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "campaigns",
    //                 Quantity = 60000
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "automations",
    //                 Quantity = 999
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "channels",
    //                 Quantity = 999
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "api_calls",
    //                 Quantity = 60000
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "ai_features_total_usage",
    //                 Quantity = 2000
    //             },
    //         },
    //         new List<Price>
    //         {
    //             new Price("USD", 948),
    //         },
    //         PlanTypes.Subscription,
    //         "pro",
    //         "v7",
    //         new List<string>
    //         {
    //             "Active"
    //         },
    //         new Dictionary<string, object>(),
    //         string.Empty,
    //         string.Empty,
    //         null,
    //         DateTimeOffset.UtcNow,
    //         DateTimeOffset.UtcNow);
    //
    //     var usdPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(usdPlanId);
    //
    //     Assert.Multiple(() =>
    //     {
    //         Assert.That(usdPlanResult, Is.EqualTo(expectedPlanDefinition));
    //     });
    // }
    //
    // [Test]
    // public async Task GetPlanDefinition_ValidId_ReturnsPlanDefinition_64()
    // {
    //     var usdPlanId = "sleekflow_v6_premium_monthly";
    //
    //     var expectedPlanDefinition = new PlanDefinition(
    //         new List<Multilingual>
    //         {
    //             new Multilingual("en", "premium_monthly", true)
    //         },
    //         new List<Multilingual>(),
    //         new List<FeatureQuantity>
    //         {
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "contacts",
    //                 Quantity = 20000
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "messages",
    //                 Quantity = 5000000
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "agents",
    //                 Quantity = 5
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "campaigns",
    //                 Quantity = 5000000
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "automations",
    //                 Quantity = 999
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "channels",
    //                 Quantity = 999
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "api_calls",
    //                 Quantity = 100000
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "ai_features_total_usage",
    //                 Quantity = 2000
    //             },
    //         },
    //         new List<Price>
    //         {
    //             new Price("USD", 349),
    //         },
    //         PlanTypes.Subscription,
    //         "premium",
    //         "v6",
    //         new List<string>
    //         {
    //             "Active"
    //         },
    //         new Dictionary<string, object>(),
    //         string.Empty,
    //         string.Empty,
    //         null,
    //         DateTimeOffset.UtcNow,
    //         DateTimeOffset.UtcNow);
    //
    //     var usdPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(usdPlanId);
    //
    //     Assert.Multiple(() =>
    //     {
    //         Assert.That(usdPlanResult, Is.EqualTo(expectedPlanDefinition));
    //     });
    // }
    //
    // [Test]
    // public async Task GetPlanDefinition_ValidId_ReturnsPlanDefinition_65()
    // {
    //     var usdPlanId = "sleekflow_v6_premium_yearly";
    //
    //     var expectedPlanDefinition = new PlanDefinition(
    //         new List<Multilingual>
    //         {
    //             new Multilingual("en", "premium_yearly", true)
    //         },
    //         new List<Multilingual>(),
    //         new List<FeatureQuantity>
    //         {
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "contacts",
    //                 Quantity = 20000
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "messages",
    //                 Quantity = 5000000
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "agents",
    //                 Quantity = 5
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "campaigns",
    //                 Quantity = 5000000
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "automations",
    //                 Quantity = 999
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "channels",
    //                 Quantity = 999
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "api_calls",
    //                 Quantity = 1200000
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "ai_features_total_usage",
    //                 Quantity = 2000
    //             },
    //         },
    //         new List<Price>
    //         {
    //             new Price("USD", 3588),
    //         },
    //         PlanTypes.Subscription,
    //         "premium",
    //         "v6",
    //         new List<string>
    //         {
    //             "Active"
    //         },
    //         new Dictionary<string, object>(),
    //         string.Empty,
    //         string.Empty,
    //         null,
    //         DateTimeOffset.UtcNow,
    //         DateTimeOffset.UtcNow);
    //
    //     var usdPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(usdPlanId);
    //
    //     Assert.Multiple(() =>
    //     {
    //         Assert.That(usdPlanResult, Is.EqualTo(expectedPlanDefinition));
    //     });
    // }
    //
    // [Test]
    // public async Task GetPlanDefinition_ValidId_ReturnsPlanDefinition_66()
    // {
    //     var usdPlanId = "sleekflow_v6_pro_monthly";
    //
    //     var expectedPlanDefinition = new PlanDefinition(
    //         new List<Multilingual>
    //         {
    //             new Multilingual("en", "pro_monthly", true)
    //         },
    //         new List<Multilingual>(),
    //         new List<FeatureQuantity>
    //         {
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "contacts",
    //                 Quantity = 5000
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "messages",
    //                 Quantity = 5000
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "agents",
    //                 Quantity = 3
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "campaigns",
    //                 Quantity = 5000
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "automations",
    //                 Quantity = 5
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "channels",
    //                 Quantity = 999
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "api_calls",
    //                 Quantity = 5000
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "ai_features_total_usage",
    //                 Quantity = 2000
    //             },
    //         },
    //         new List<Price>
    //         {
    //             new Price("USD", 99),
    //         },
    //         PlanTypes.Subscription,
    //         "pro",
    //         "v6",
    //         new List<string>
    //         {
    //             "Active"
    //         },
    //         new Dictionary<string, object>(),
    //         string.Empty,
    //         string.Empty,
    //         null,
    //         DateTimeOffset.UtcNow,
    //         DateTimeOffset.UtcNow);
    //
    //     var usdPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(usdPlanId);
    //
    //     Assert.Multiple(() =>
    //     {
    //         Assert.That(usdPlanResult, Is.EqualTo(expectedPlanDefinition));
    //     });
    // }
    //
    // [Test]
    // public async Task GetPlanDefinition_ValidId_ReturnsPlanDefinition_67()
    // {
    //     var usdPlanId = "sleekflow_v6_pro_yearly";
    //
    //     var expectedPlanDefinition = new PlanDefinition(
    //         new List<Multilingual>
    //         {
    //             new Multilingual("en", "pro_yearly", true)
    //         },
    //         new List<Multilingual>(),
    //         new List<FeatureQuantity>
    //         {
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "contacts",
    //                 Quantity = 5000
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "messages",
    //                 Quantity = 5000
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "agents",
    //                 Quantity = 3
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "campaigns",
    //                 Quantity = 60000
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "automations",
    //                 Quantity = 5
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "channels",
    //                 Quantity = 999
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "api_calls",
    //                 Quantity = 60000
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "ai_features_total_usage",
    //                 Quantity = 2000
    //             },
    //         },
    //         new List<Price>
    //         {
    //             new Price("USD", 948),
    //         },
    //         PlanTypes.Subscription,
    //         "pro",
    //         "v6",
    //         new List<string>
    //         {
    //             "Active"
    //         },
    //         new Dictionary<string, object>(),
    //         string.Empty,
    //         string.Empty,
    //         null,
    //         DateTimeOffset.UtcNow,
    //         DateTimeOffset.UtcNow);
    //
    //     var usdPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(usdPlanId);
    //
    //     Assert.Multiple(() =>
    //     {
    //         Assert.That(usdPlanResult, Is.EqualTo(expectedPlanDefinition));
    //     });
    // }
    //
    // [Test]
    // public async Task GetPlanDefinition_ValidId_ReturnsPlanDefinition_68()
    // {
    //     var usdPlanId = "sleekflow_v4_pro_yearly";
    //     var hkdPlanId = "sleekflow_v4_pro_yearly_hkd";
    //
    //     var expectedPlanDefinition = new PlanDefinition(
    //         new List<Multilingual>
    //         {
    //             new Multilingual("en", "pro_yearly", true)
    //         },
    //         new List<Multilingual>(),
    //         new List<FeatureQuantity>
    //         {
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "contacts",
    //                 Quantity = 5000
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "messages",
    //                 Quantity = 60000
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "agents",
    //                 Quantity = 3
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "campaigns",
    //                 Quantity = 60000
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "automations",
    //                 Quantity = 5
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "channels",
    //                 Quantity = 999
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "api_calls",
    //                 Quantity = 60000
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "ai_features_total_usage",
    //                 Quantity = 2000
    //             },
    //         },
    //         new List<Price>
    //         {
    //             new Price("USD", 948),
    //             new Price("HKD", 7350),
    //         },
    //         PlanTypes.Subscription,
    //         "pro",
    //         "v4",
    //         new List<string>
    //         {
    //             "Active"
    //         },
    //         new Dictionary<string, object>(),
    //         string.Empty,
    //         string.Empty,
    //         null,
    //         DateTimeOffset.UtcNow,
    //         DateTimeOffset.UtcNow);
    //
    //     var usdPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(usdPlanId);
    //     var hkdPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(hkdPlanId);
    //
    //     Assert.Multiple(() =>
    //     {
    //         Assert.That(usdPlanResult, Is.EqualTo(expectedPlanDefinition));
    //         Assert.That(hkdPlanResult, Is.EqualTo(expectedPlanDefinition));
    //     });
    // }
    //
    // [Test]
    // public async Task GetPlanDefinition_ValidId_ReturnsPlanDefinition_69()
    // {
    //     var usdPlanId = "sleekflow_v5_pro_yearly";
    //
    //     var expectedPlanDefinition = new PlanDefinition(
    //         new List<Multilingual>
    //         {
    //             new Multilingual("en", "pro_yearly", true)
    //         },
    //         new List<Multilingual>(),
    //         new List<FeatureQuantity>
    //         {
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "contacts",
    //                 Quantity = 5000
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "messages",
    //                 Quantity = 60000
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "agents",
    //                 Quantity = 3
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "campaigns",
    //                 Quantity = 60000
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "automations",
    //                 Quantity = 5
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "channels",
    //                 Quantity = 999
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "api_calls",
    //                 Quantity = 60000
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "ai_features_total_usage",
    //                 Quantity = 2000
    //             },
    //         },
    //         new List<Price>
    //         {
    //             new Price("USD", 948),
    //         },
    //         PlanTypes.Subscription,
    //         "pro",
    //         "v5",
    //         new List<string>
    //         {
    //             "Active"
    //         },
    //         new Dictionary<string, object>(),
    //         string.Empty,
    //         string.Empty,
    //         null,
    //         DateTimeOffset.UtcNow,
    //         DateTimeOffset.UtcNow);
    //
    //     var usdPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(usdPlanId);
    //
    //     Assert.Multiple(() =>
    //     {
    //         Assert.That(usdPlanResult, Is.EqualTo(expectedPlanDefinition));
    //     });
    // }

    // [Test]
    // public async Task GetPlanDefinition_ValidId_ReturnsPlanDefinition_70()
    // {
    //     var usdPlanId = "sleekflow_v4_premium_monthly";
    //
    //     var expectedPlanDefinition = new PlanDefinition(
    //         new List<Multilingual>
    //         {
    //             new Multilingual("en", "premium_monthly", true)
    //         },
    //         new List<Multilingual>(),
    //         new List<FeatureQuantity>
    //         {
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "contacts",
    //                 Quantity = 500000000
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "messages",
    //                 Quantity = 1000000
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "agents",
    //                 Quantity = 5
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "campaigns",
    //                 Quantity = 1000000
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "automations",
    //                 Quantity = 999
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "channels",
    //                 Quantity = 999
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "api_calls",
    //                 Quantity = 100000
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "ai_features_total_usage",
    //                 Quantity = 2000
    //             },
    //         },
    //         new List<Price>
    //         {
    //             new Price("USD", 349),
    //         },
    //         PlanTypes.Subscription,
    //         "premium",
    //         "v4",
    //         new List<string>
    //         {
    //             "Active"
    //         },
    //         new Dictionary<string, object>(),
    //         string.Empty,
    //         string.Empty,
    //         null,
    //         DateTimeOffset.UtcNow,
    //         DateTimeOffset.UtcNow);
    //
    //     var usdPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(usdPlanId);
    //
    //     Assert.Multiple(() =>
    //     {
    //         Assert.That(usdPlanResult, Is.EqualTo(expectedPlanDefinition));
    //     });
    // }
    //
    // [Test]
    // public async Task GetPlanDefinition_ValidId_ReturnsPlanDefinition_71()
    // {
    //     var usdPlanId = "sleekflow_v4_premium_yearly";
    //
    //     var expectedPlanDefinition = new PlanDefinition(
    //         new List<Multilingual>
    //         {
    //             new Multilingual("en", "premium_yearly", true)
    //         },
    //         new List<Multilingual>(),
    //         new List<FeatureQuantity>
    //         {
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "contacts",
    //                 Quantity = 500000000
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "messages",
    //                 Quantity = 1000000
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "agents",
    //                 Quantity = 5
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "campaigns",
    //                 Quantity = 1000000
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "automations",
    //                 Quantity = 999
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "channels",
    //                 Quantity = 999
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "api_calls",
    //                 Quantity = 1200000
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "ai_features_total_usage",
    //                 Quantity = 2000
    //             },
    //         },
    //         new List<Price>
    //         {
    //             new Price("USD", 3588),
    //         },
    //         PlanTypes.Subscription,
    //         "premium",
    //         "v4",
    //         new List<string>
    //         {
    //             "Active"
    //         },
    //         new Dictionary<string, object>(),
    //         string.Empty,
    //         string.Empty,
    //         null,
    //         DateTimeOffset.UtcNow,
    //         DateTimeOffset.UtcNow);
    //
    //     var usdPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(usdPlanId);
    //
    //     Assert.Multiple(() =>
    //     {
    //         Assert.That(usdPlanResult, Is.EqualTo(expectedPlanDefinition));
    //     });
    // }
    //
    // [Test]
    // public async Task GetPlanDefinition_ValidId_ReturnsPlanDefinition_72()
    // {
    //     var usdPlanId = "sleekflow_v3_premium";
    //
    //     var expectedPlanDefinition = new PlanDefinition(
    //         new List<Multilingual>
    //         {
    //             new Multilingual("en", "premium", true)
    //         },
    //         new List<Multilingual>(),
    //         new List<FeatureQuantity>
    //         {
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "contacts",
    //                 Quantity = 500000000
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "messages",
    //                 Quantity = 20000
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "agents",
    //                 Quantity = 5
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "campaigns",
    //                 Quantity = 20000
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "automations",
    //                 Quantity = 999
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "channels",
    //                 Quantity = 999
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "api_calls",
    //                 Quantity = 100000
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "ai_features_total_usage",
    //                 Quantity = 2000
    //             },
    //         },
    //         new List<Price>
    //         {
    //             new Price("USD", 299),
    //         },
    //         PlanTypes.Subscription,
    //         "premium",
    //         "v3",
    //         new List<string>
    //         {
    //             "Active"
    //         },
    //         new Dictionary<string, object>(),
    //         string.Empty,
    //         string.Empty,
    //         null,
    //         DateTimeOffset.UtcNow,
    //         DateTimeOffset.UtcNow);
    //
    //     var usdPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(usdPlanId);
    //
    //     Assert.Multiple(() =>
    //     {
    //         Assert.That(usdPlanResult, Is.EqualTo(expectedPlanDefinition));
    //     });
    // }
    //
    // [Test]
    // public async Task GetPlanDefinition_ValidId_ReturnsPlanDefinition_73()
    // {
    //     var usdPlanId = "sleekflow_v3_pro";
    //
    //     var expectedPlanDefinition = new PlanDefinition(
    //         new List<Multilingual>
    //         {
    //             new Multilingual("en", "pro", true)
    //         },
    //         new List<Multilingual>(),
    //         new List<FeatureQuantity>
    //         {
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "contacts",
    //                 Quantity = 5000
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "messages",
    //                 Quantity = 5000
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "agents",
    //                 Quantity = 3
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "campaigns",
    //                 Quantity = 5000
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "automations",
    //                 Quantity = 5
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "channels",
    //                 Quantity = 999
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "api_calls",
    //                 Quantity = 5000
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "ai_features_total_usage",
    //                 Quantity = 2000
    //             },
    //         },
    //         new List<Price>
    //         {
    //             new Price("USD", 99),
    //         },
    //         PlanTypes.Subscription,
    //         "pro",
    //         "v3",
    //         new List<string>
    //         {
    //             "Active"
    //         },
    //         new Dictionary<string, object>(),
    //         string.Empty,
    //         string.Empty,
    //         null,
    //         DateTimeOffset.UtcNow,
    //         DateTimeOffset.UtcNow);
    //
    //     var usdPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(usdPlanId);
    //
    //     Assert.Multiple(() =>
    //     {
    //         Assert.That(usdPlanResult, Is.EqualTo(expectedPlanDefinition));
    //     });
    // }
    //
    // [Test]
    // public async Task GetPlanDefinition_ValidId_ReturnsPlanDefinition_74()
    // {
    //     var usdPlanId = "sleekflow_v3_standard";
    //
    //     var expectedPlanDefinition = new PlanDefinition(
    //         new List<Multilingual>
    //         {
    //             new Multilingual("en", "standard", true)
    //         },
    //         new List<Multilingual>(),
    //         new List<FeatureQuantity>
    //         {
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "contacts",
    //                 Quantity = 500
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "messages",
    //                 Quantity = 100
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "agents",
    //                 Quantity = 1
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "campaigns",
    //                 Quantity = 100
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "automations",
    //                 Quantity = 1
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "channels",
    //                 Quantity = 999
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "ai_features_total_usage",
    //                 Quantity = 2000
    //             },
    //         },
    //         new List<Price>
    //         {
    //             new Price("USD", 49),
    //         },
    //         PlanTypes.Subscription,
    //         "pro",
    //         "v3",
    //         new List<string>
    //         {
    //             "Active"
    //         },
    //         new Dictionary<string, object>(),
    //         string.Empty,
    //         string.Empty,
    //         null,
    //         DateTimeOffset.UtcNow,
    //         DateTimeOffset.UtcNow);
    //
    //     var usdPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(usdPlanId);
    //
    //     Assert.Multiple(() =>
    //     {
    //         Assert.That(usdPlanResult, Is.EqualTo(expectedPlanDefinition));
    //     });
    // }

    [Test]
    public async Task GetPlanDefinition_ValidId_ReturnsPlanDefinition_75()
    {
        var usdPlanId = "sleekflow_v2_agent_premium";

        var expectedPlanDefinition = new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "agent_premium", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "agents",
                    Quantity = 1
                },
            },
            new List<Price>
            {
                new Price("USD", 39),
            },
            PlanTypes.AddOns,
            null,
            "v2",
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow);

        var usdPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(usdPlanId);

        Assert.Multiple(() =>
        {
            Assert.That(usdPlanResult, Is.EqualTo(expectedPlanDefinition));
        });
    }

    [Test]
    public async Task GetPlanDefinition_ValidId_ReturnsPlanDefinition_76()
    {
        var hkdPlanId = "sleekflow_v2_agent_premium_hkd";

        var expectedPlanDefinition = new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "agent_premium", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "agents",
                    Quantity = 5
                },
            },
            new List<Price>
            {
                new Price("HKD", 300),
            },
            PlanTypes.AddOns,
            null,
            "v2",
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow);

        var hkdPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(hkdPlanId);

        Assert.Multiple(() =>
        {
            Assert.That(hkdPlanResult, Is.EqualTo(expectedPlanDefinition));
        });
    }

    [Test]
    public async Task GetPlanDefinition_ValidId_ReturnsPlanDefinition_77()
    {
        var usdPlanId = "sleekflow_v2_agent_premium_39";

        var expectedPlanDefinition = new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "agent_premium_39", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "agents",
                    Quantity = 1
                },
            },
            new List<Price>
            {
                new Price("USD", 39),
            },
            PlanTypes.AddOns,
            null,
            "v2",
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow);

        var usdPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(usdPlanId);

        Assert.Multiple(() =>
        {
            Assert.That(usdPlanResult, Is.EqualTo(expectedPlanDefinition));
        });
    }

    [Test]
    public async Task GetPlanDefinition_ValidId_ReturnsPlanDefinition_78()
    {
        var usdPlanId = "sleekflow_v2_agent_premium_39_yearly";

        var expectedPlanDefinition = new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "agent_premium_39_yearly", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "agents",
                    Quantity = 1
                },
            },
            new List<Price>
            {
                new Price("USD", 468),
            },
            PlanTypes.AddOns,
            null,
            "v2",
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow);

        var usdPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(usdPlanId);

        Assert.Multiple(() =>
        {
            Assert.That(usdPlanResult, Is.EqualTo(expectedPlanDefinition));
        });
    }

    [Test]
    public async Task GetPlanDefinition_ValidId_ReturnsPlanDefinition_79()
    {
        var usdPlanId = "sleekflow_v2_agent_pro";

        var expectedPlanDefinition = new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "agent_pro", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "agents",
                    Quantity = 1
                },
            },
            new List<Price>
            {
                new Price("USD", 19),
            },
            PlanTypes.AddOns,
            null,
            "v2",
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow);

        var usdPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(usdPlanId);

        Assert.Multiple(() =>
        {
            Assert.That(usdPlanResult, Is.EqualTo(expectedPlanDefinition));
        });
    }

    [Test]
    public async Task GetPlanDefinition_ValidId_ReturnsPlanDefinition_80()
    {
        var usdPlanId = "sleekflow_v2_agent_pro_yearly";

        var expectedPlanDefinition = new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "agent_pro_yearly", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "agents",
                    Quantity = 1
                },
            },
            new List<Price>
            {
                new Price("USD", 228),
            },
            PlanTypes.AddOns,
            null,
            "v2",
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow);

        var usdPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(usdPlanId);

        Assert.Multiple(() =>
        {
            Assert.That(usdPlanResult, Is.EqualTo(expectedPlanDefinition));
        });
    }

    [Test]
    public async Task GetPlanDefinition_ValidId_ReturnsPlanDefinition_81()
    {
        var usdPlanId = "sleekflow_v2_agent_standard";

        var expectedPlanDefinition = new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "agent_standard", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "agents",
                    Quantity = 1
                },
            },
            new List<Price>
            {
                new Price("USD", 9),
            },
            PlanTypes.AddOns,
            null,
            "v2",
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow);

        var usdPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(usdPlanId);

        Assert.Multiple(() =>
        {
            Assert.That(usdPlanResult, Is.EqualTo(expectedPlanDefinition));
        });
    }

    [Test]
    public async Task GetPlanDefinition_ValidId_ReturnsPlanDefinition_82()
    {
        var usdPlanId = "sleekflow_v2_agent_standard_yearly";

        var expectedPlanDefinition = new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "agent_standard_yearly", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "agents",
                    Quantity = 1
                },
            },
            new List<Price>
            {
                new Price("USD", 228),
            },
            PlanTypes.AddOns,
            null,
            "v2",
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow);

        var usdPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(usdPlanId);

        Assert.Multiple(() =>
        {
            Assert.That(usdPlanResult, Is.EqualTo(expectedPlanDefinition));
        });
    }

    // Test case is disabled due to feature plan is updated
    // Team: Team GenR
    // [Test]
    // public async Task GetPlanDefinition_ValidId_ReturnsPlanDefinition_83()
    // {
    //     var usdPlanId = "sleekflow_v2_premium";
    //
    //     var expectedPlanDefinition = new PlanDefinition(
    //         new List<Multilingual>
    //         {
    //             new Multilingual("en", "premium", true)
    //         },
    //         new List<Multilingual>(),
    //         new List<FeatureQuantity>
    //         {
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "contacts",
    //                 Quantity = 500000000
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "messages",
    //                 Quantity = 1000000
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "agents",
    //                 Quantity = 5
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "campaigns",
    //                 Quantity = 1000000
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "automations",
    //                 Quantity = 999
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "channels",
    //                 Quantity = 999
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "api_calls",
    //                 Quantity = 100000
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "ai_features_total_usage",
    //                 Quantity = 2000
    //             },
    //         },
    //         new List<Price>
    //         {
    //             new Price("USD", 299),
    //         },
    //         PlanTypes.Subscription,
    //         "premium",
    //         "v2",
    //         new List<string>
    //         {
    //             "Active"
    //         },
    //         new Dictionary<string, object>(),
    //         string.Empty,
    //         string.Empty,
    //         null,
    //         DateTimeOffset.UtcNow,
    //         DateTimeOffset.UtcNow);
    //
    //     var usdPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(usdPlanId);
    //
    //     Assert.Multiple(() =>
    //     {
    //         Assert.That(usdPlanResult, Is.EqualTo(expectedPlanDefinition));
    //     });
    // }
    //
    // [Test]
    // public async Task GetPlanDefinition_ValidId_ReturnsPlanDefinition_84()
    // {
    //     var usdPlanId = "sleekflow_v2_pro";
    //
    //     var expectedPlanDefinition = new PlanDefinition(
    //         new List<Multilingual>
    //         {
    //             new Multilingual("en", "pro", true)
    //         },
    //         new List<Multilingual>(),
    //         new List<FeatureQuantity>
    //         {
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "contacts",
    //                 Quantity = 5000
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "messages",
    //                 Quantity = 5000
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "agents",
    //                 Quantity = 3
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "campaigns",
    //                 Quantity = 5000
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "automations",
    //                 Quantity = 999
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "channels",
    //                 Quantity = 999
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "api_calls",
    //                 Quantity = 5000
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "ai_features_total_usage",
    //                 Quantity = 2000
    //             },
    //         },
    //         new List<Price>
    //         {
    //             new Price("USD", 99),
    //         },
    //         PlanTypes.Subscription,
    //         "pro",
    //         "v2",
    //         new List<string>
    //         {
    //             "Active"
    //         },
    //         new Dictionary<string, object>(),
    //         string.Empty,
    //         string.Empty,
    //         null,
    //         DateTimeOffset.UtcNow,
    //         DateTimeOffset.UtcNow);
    //
    //     var usdPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(usdPlanId);
    //
    //     Assert.Multiple(() =>
    //     {
    //         Assert.That(usdPlanResult, Is.EqualTo(expectedPlanDefinition));
    //     });
    // }
    //
    // [Test]
    // public async Task GetPlanDefinition_ValidId_ReturnsPlanDefinition_85()
    // {
    //     var usdPlanId = "sleekflow_v2_standard";
    //
    //     var expectedPlanDefinition = new PlanDefinition(
    //         new List<Multilingual>
    //         {
    //             new Multilingual("en", "standard", true)
    //         },
    //         new List<Multilingual>(),
    //         new List<FeatureQuantity>
    //         {
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "contacts",
    //                 Quantity = 500
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "messages",
    //                 Quantity = 100
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "agents",
    //                 Quantity = 1
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "campaigns",
    //                 Quantity = 100
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "automations",
    //                 Quantity = 1
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "channels",
    //                 Quantity = 999
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "ai_features_total_usage",
    //                 Quantity = 2000
    //             },
    //         },
    //         new List<Price>
    //         {
    //             new Price("USD", 39),
    //         },
    //         PlanTypes.Subscription,
    //         "pro",
    //         "v2",
    //         new List<string>
    //         {
    //             "Active"
    //         },
    //         new Dictionary<string, object>(),
    //         string.Empty,
    //         string.Empty,
    //         null,
    //         DateTimeOffset.UtcNow,
    //         DateTimeOffset.UtcNow);
    //
    //     var usdPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(usdPlanId);
    //
    //     Assert.Multiple(() =>
    //     {
    //         Assert.That(usdPlanResult, Is.EqualTo(expectedPlanDefinition));
    //     });
    // }
    //
    // [Test]
    // public async Task GetPlanDefinition_ValidId_ReturnsPlanDefinition_86()
    // {
    //     var usdPlanId = "sleekflow_pro";
    //
    //     var expectedPlanDefinition = new PlanDefinition(
    //         new List<Multilingual>
    //         {
    //             new Multilingual("en", "pro", true)
    //         },
    //         new List<Multilingual>(),
    //         new List<FeatureQuantity>
    //         {
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "messages",
    //                 Quantity = 5000
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "agents",
    //                 Quantity = 1
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "automations",
    //                 Quantity = 1
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "channels",
    //                 Quantity = 3
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "api_calls",
    //                 Quantity = 5000
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "ai_features_total_usage",
    //                 Quantity = 2000
    //             },
    //         },
    //         new List<Price>
    //         {
    //             new Price("USD", 39),
    //         },
    //         PlanTypes.Subscription,
    //         "pro",
    //         "v0",
    //         new List<string>
    //         {
    //             "Active"
    //         },
    //         new Dictionary<string, object>(),
    //         string.Empty,
    //         string.Empty,
    //         null,
    //         DateTimeOffset.UtcNow,
    //         DateTimeOffset.UtcNow);
    //
    //     var usdPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(usdPlanId);
    //
    //     Assert.Multiple(() =>
    //     {
    //         Assert.That(usdPlanResult, Is.EqualTo(expectedPlanDefinition));
    //     });
    // }
    //
    // [Test]
    // public async Task GetPlanDefinition_ValidId_ReturnsPlanDefinition_87()
    // {
    //     var usdPlanId = "sleekflow_demo";
    //
    //     var expectedPlanDefinition = new PlanDefinition(
    //         new List<Multilingual>
    //         {
    //             new Multilingual("en", "demo", true)
    //         },
    //         new List<Multilingual>(),
    //         new List<FeatureQuantity>
    //         {
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "contacts",
    //                 Quantity = 50000000
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "messages",
    //                 Quantity = 100
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "agents",
    //                 Quantity = 12
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "campaigns",
    //                 Quantity = 100
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "automations",
    //                 Quantity = 999
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "channels",
    //                 Quantity = 999
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "api_calls",
    //                 Quantity = 999
    //             },
    //         },
    //         new List<Price>
    //         {
    //             new Price("USD", 0),
    //         },
    //         PlanTypes.Subscription,
    //         "free",
    //         "v0",
    //         new List<string>
    //         {
    //             "Active"
    //         },
    //         new Dictionary<string, object>(),
    //         string.Empty,
    //         string.Empty,
    //         null,
    //         DateTimeOffset.UtcNow,
    //         DateTimeOffset.UtcNow);
    //
    //     var usdPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(usdPlanId);
    //
    //     Assert.Multiple(() =>
    //     {
    //         Assert.That(usdPlanResult, Is.EqualTo(expectedPlanDefinition));
    //     });
    // }
    //
    // [Test]
    // public async Task GetPlanDefinition_ValidId_ReturnsPlanDefinition_88()
    // {
    //     var usdPlanId = "sleekflow_enterprise";
    //
    //     var expectedPlanDefinition = new PlanDefinition(
    //         new List<Multilingual>
    //         {
    //             new Multilingual("en", "enterprise", true)
    //         },
    //         new List<Multilingual>(),
    //         new List<FeatureQuantity>
    //         {
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "contacts",
    //                 Quantity = 100000
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "messages",
    //                 Quantity = 50000000
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "agents",
    //                 Quantity = 5
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "campaigns",
    //                 Quantity = 50000000
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "automations",
    //                 Quantity = 999
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "channels",
    //                 Quantity = 999
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "api_calls",
    //                 Quantity = 5000000
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "onboarding_support",
    //                 Quantity = 1
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "chatbot_automation_support_basic",
    //                 Quantity = 1
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "priority_support",
    //                 Quantity = 1
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "ai_features_total_usage",
    //                 Quantity = 2000
    //             },
    //         },
    //         new List<Price>
    //         {
    //             new Price("USD", 999),
    //         },
    //         PlanTypes.Subscription,
    //         "enterprise",
    //         "v0",
    //         new List<string>
    //         {
    //             "Active"
    //         },
    //         new Dictionary<string, object>(),
    //         string.Empty,
    //         string.Empty,
    //         null,
    //         DateTimeOffset.UtcNow,
    //         DateTimeOffset.UtcNow);
    //
    //     var usdPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(usdPlanId);
    //
    //     Assert.Multiple(() =>
    //     {
    //         Assert.That(usdPlanResult, Is.EqualTo(expectedPlanDefinition));
    //     });
    // }

    [Test]
    public async Task GetPlanDefinition_ValidId_ReturnsPlanDefinition_89()
    {
        var usdPlanId = "sleekflow_free";

        var expectedPlanDefinition = new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "free", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "contacts",
                    Quantity = 100
                },
                new FeatureQuantity
                {
                    FeatureId = "messages",
                    Quantity = 200
                },
                new FeatureQuantity
                {
                    FeatureId = "agents",
                    Quantity = 1
                },
                new FeatureQuantity
                {
                    FeatureId = "channels",
                    Quantity = 1
                },
            },
            new List<Price>
            {
                new Price("USD", 0),
            },
            PlanTypes.Subscription,
            "free",
            "v0",
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow);

        var usdPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(usdPlanId);

        Assert.Multiple(() =>
        {
            Assert.That(usdPlanResult, Is.EqualTo(expectedPlanDefinition));
        });
    }

    // Test case is disabled due to feature plan is updated
    // Team: Team GenR
    // [Test]
    // public async Task GetPlanDefinition_ValidId_ReturnsPlanDefinition_90()
    // {
    //     var usdPlanId = "sleekflow_freemium";
    //
    //     var expectedPlanDefinition = new PlanDefinition(
    //         new List<Multilingual>
    //         {
    //             new Multilingual("en", "freemium", true)
    //         },
    //         new List<Multilingual>(),
    //         new List<FeatureQuantity>
    //         {
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "contacts",
    //                 Quantity = 100
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "messages",
    //                 Quantity = 100
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "agents",
    //                 Quantity = 3
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "campaigns",
    //                 Quantity = 100
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "automations",
    //                 Quantity = 5
    //             },
    //             new FeatureQuantity
    //             {
    //                 FeatureId = "channels",
    //                 Quantity = 999
    //             },
    //         },
    //         new List<Price>
    //         {
    //             new Price("USD", 0),
    //         },
    //         PlanTypes.Subscription,
    //         "free",
    //         "v0",
    //         new List<string>
    //         {
    //             "Active"
    //         },
    //         new Dictionary<string, object>(),
    //         string.Empty,
    //         string.Empty,
    //         null,
    //         DateTimeOffset.UtcNow,
    //         DateTimeOffset.UtcNow);
    //
    //     var usdPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(usdPlanId);
    //
    //     Assert.Multiple(() =>
    //     {
    //         Assert.That(usdPlanResult, Is.EqualTo(expectedPlanDefinition));
    //     });
    // }

    [Test]
    public async Task GetPlanDefinition_ValidId_ReturnsPlanDefinition_91()
    {
        var usdPlanId = "sleekflow_v9_chatbot_automation_support_basic_oneoff";
        var aedPlanId = "sleekflow_v9_chatbot_automation_support_basic_oneoff_aed";
        var audPlanId = "sleekflow_v9_chatbot_automation_support_basic_oneoff_aud";
        var brlPlanId = "sleekflow_v9_chatbot_automation_support_basic_oneoff_brl";
        var cadPlanId = "sleekflow_v9_chatbot_automation_support_basic_oneoff_cad";
        var cnyPlanId = "sleekflow_v9_chatbot_automation_support_basic_oneoff_cny";
        var eurPlanId = "sleekflow_v9_chatbot_automation_support_basic_oneoff_eur";
        var gbpPlanId = "sleekflow_v9_chatbot_automation_support_basic_oneoff_gbp";
        var hkdPlanId = "sleekflow_v9_chatbot_automation_support_basic_oneoff_hkd";
        var idrPlanId = "sleekflow_v9_chatbot_automation_support_basic_oneoff_idr";
        var inrPlanId = "sleekflow_v9_chatbot_automation_support_basic_oneoff_inr";
        var myrPlanId = "sleekflow_v9_chatbot_automation_support_basic_oneoff_myr";
        var sgdPlanId = "sleekflow_v9_chatbot_automation_support_basic_oneoff_sgd";

        var expectedPlanDefinition = new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "chatbot_automation_support_basic_oneoff", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "chatbot_automation_support_basic",
                    Quantity = 1
                }
            },
            new List<Price>
            {
                new Price("USD", 3999),
                new Price("AED", 14689),
                new Price("AUD", 5899),
                new Price("BRL", 21499),
                new Price("CAD", 5179),
                new Price("CNY", 28469),
                new Price("EUR", 3969),
                new Price("GBP", 3359),
                new Price("HKD", 31399),
                new Price("IDR", 60052000),
                new Price("INR", 169399),
                new Price("MYR", 17749),
                new Price("SGD", 5619),
            },
            PlanTypes.AddOns,
            null,
            "v9",
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow);

        var usdPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(usdPlanId);
        var aedPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(aedPlanId);
        var audPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(audPlanId);
        var brlPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(brlPlanId);
        var cadPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(cadPlanId);
        var cnyPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(cnyPlanId);
        var eurPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(eurPlanId);
        var gbpPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(gbpPlanId);
        var hkdPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(hkdPlanId);
        var idrPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(idrPlanId);
        var inrPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(inrPlanId);
        var myrPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(myrPlanId);
        var sgdPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(sgdPlanId);

        Assert.Multiple(() =>
        {
            Assert.That(usdPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(aedPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(audPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(brlPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(cadPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(cnyPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(eurPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(gbpPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(hkdPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(idrPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(inrPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(myrPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(sgdPlanResult, Is.EqualTo(expectedPlanDefinition));
        });
    }

    [Test]
    public async Task GetPlanDefinition_ValidId_ReturnsPlanDefinition_92()
    {
        var usdPlanId = "sleekflow_v9_chatbot_automation_support_intermediate_oneoff";
        var aedPlanId = "sleekflow_v9_chatbot_automation_support_intermediate_oneoff_aed";
        var audPlanId = "sleekflow_v9_chatbot_automation_support_intermediate_oneoff_aud";
        var brlPlanId = "sleekflow_v9_chatbot_automation_support_intermediate_oneoff_brl";
        var cadPlanId = "sleekflow_v9_chatbot_automation_support_intermediate_oneoff_cad";
        var cnyPlanId = "sleekflow_v9_chatbot_automation_support_intermediate_oneoff_cny";
        var eurPlanId = "sleekflow_v9_chatbot_automation_support_intermediate_oneoff_eur";
        var gbpPlanId = "sleekflow_v9_chatbot_automation_support_intermediate_oneoff_gbp";
        var hkdPlanId = "sleekflow_v9_chatbot_automation_support_intermediate_oneoff_hkd";
        var idrPlanId = "sleekflow_v9_chatbot_automation_support_intermediate_oneoff_idr";
        var inrPlanId = "sleekflow_v9_chatbot_automation_support_intermediate_oneoff_inr";
        var myrPlanId = "sleekflow_v9_chatbot_automation_support_intermediate_oneoff_myr";
        var sgdPlanId = "sleekflow_v9_chatbot_automation_support_intermediate_oneoff_sgd";

        var expectedPlanDefinition = new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "chatbot_automation_support_intermediate_oneoff", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "chatbot_automation_support_intermediate",
                    Quantity = 1
                }
            },
            new List<Price>
            {
                new Price("USD", 9999),
                new Price("AED", 36729),
                new Price("AUD", 14749),
                new Price("BRL", 53759),
                new Price("CAD", 12959),
                new Price("CNY", 71159),
                new Price("EUR", 9919),
                new Price("GBP", 8389),
                new Price("HKD", 78499),
                new Price("IDR", 150153000),
                new Price("INR", 423599),
                new Price("MYR", 44369),
                new Price("SGD", 14049),
            },
            PlanTypes.AddOns,
            null,
            "v9",
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow);

        var usdPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(usdPlanId);
        var aedPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(aedPlanId);
        var audPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(audPlanId);
        var brlPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(brlPlanId);
        var cadPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(cadPlanId);
        var cnyPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(cnyPlanId);
        var eurPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(eurPlanId);
        var gbpPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(gbpPlanId);
        var hkdPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(hkdPlanId);
        var idrPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(idrPlanId);
        var inrPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(inrPlanId);
        var myrPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(myrPlanId);
        var sgdPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(sgdPlanId);

        Assert.Multiple(() =>
        {
            Assert.That(usdPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(aedPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(audPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(brlPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(cadPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(cnyPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(eurPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(gbpPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(hkdPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(idrPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(inrPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(myrPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(sgdPlanResult, Is.EqualTo(expectedPlanDefinition));
        });
    }

    [Test]
    public async Task GetPlanDefinition_ValidId_ReturnsPlanDefinition_93()
    {
        var usdPlanId = "sleekflow_v9_chatbot_automation_support_advanced_oneoff";
        var aedPlanId = "sleekflow_v9_chatbot_automation_support_advanced_oneoff_aed";
        var audPlanId = "sleekflow_v9_chatbot_automation_support_advanced_oneoff_aud";
        var brlPlanId = "sleekflow_v9_chatbot_automation_support_advanced_oneoff_brl";
        var cadPlanId = "sleekflow_v9_chatbot_automation_support_advanced_oneoff_cad";
        var cnyPlanId = "sleekflow_v9_chatbot_automation_support_advanced_oneoff_cny";
        var eurPlanId = "sleekflow_v9_chatbot_automation_support_advanced_oneoff_eur";
        var gbpPlanId = "sleekflow_v9_chatbot_automation_support_advanced_oneoff_gbp";
        var hkdPlanId = "sleekflow_v9_chatbot_automation_support_advanced_oneoff_hkd";
        var idrPlanId = "sleekflow_v9_chatbot_automation_support_advanced_oneoff_idr";
        var inrPlanId = "sleekflow_v9_chatbot_automation_support_advanced_oneoff_inr";
        var myrPlanId = "sleekflow_v9_chatbot_automation_support_advanced_oneoff_myr";
        var sgdPlanId = "sleekflow_v9_chatbot_automation_support_advanced_oneoff_sgd";

        var expectedPlanDefinition = new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "chatbot_automation_support_advanced_oneoff", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "chatbot_automation_support_advanced",
                    Quantity = 1
                }
            },
            new List<Price>
            {
                new Price("USD", 19999),
                new Price("AED", 73459),
                new Price("AUD", 29489),
                new Price("BRL", 107519),
                new Price("CAD", 25909),
                new Price("CNY", 142309),
                new Price("EUR", 19839),
                new Price("GBP", 16779),
                new Price("HKD", 156999),
                new Price("IDR", 300320000),
                new Price("INR", 847199),
                new Price("MYR", 88739),
                new Price("SGD", 28089),
            },
            PlanTypes.AddOns,
            null,
            "v9",
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow);

        var usdPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(usdPlanId);
        var aedPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(aedPlanId);
        var audPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(audPlanId);
        var brlPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(brlPlanId);
        var cadPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(cadPlanId);
        var cnyPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(cnyPlanId);
        var eurPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(eurPlanId);
        var gbpPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(gbpPlanId);
        var hkdPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(hkdPlanId);
        var idrPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(idrPlanId);
        var inrPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(inrPlanId);
        var myrPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(myrPlanId);
        var sgdPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(sgdPlanId);

        Assert.Multiple(() =>
        {
            Assert.That(usdPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(aedPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(audPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(brlPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(cadPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(cnyPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(eurPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(gbpPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(hkdPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(idrPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(inrPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(myrPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(sgdPlanResult, Is.EqualTo(expectedPlanDefinition));
        });
    }

    [Test]
    public async Task GetPlanDefinition_ValidId_ReturnsPlanDefinition_94()
    {
        var usdPlanId = "sleekflow_v9_chatbot_automation_support_enterprise_oneoff";
        var aedPlanId = "sleekflow_v9_chatbot_automation_support_enterprise_oneoff_aed";
        var audPlanId = "sleekflow_v9_chatbot_automation_support_enterprise_oneoff_aud";
        var brlPlanId = "sleekflow_v9_chatbot_automation_support_enterprise_oneoff_brl";
        var cadPlanId = "sleekflow_v9_chatbot_automation_support_enterprise_oneoff_cad";
        var cnyPlanId = "sleekflow_v9_chatbot_automation_support_enterprise_oneoff_cny";
        var eurPlanId = "sleekflow_v9_chatbot_automation_support_enterprise_oneoff_eur";
        var gbpPlanId = "sleekflow_v9_chatbot_automation_support_enterprise_oneoff_gbp";
        var hkdPlanId = "sleekflow_v9_chatbot_automation_support_enterprise_oneoff_hkd";
        var idrPlanId = "sleekflow_v9_chatbot_automation_support_enterprise_oneoff_idr";
        var inrPlanId = "sleekflow_v9_chatbot_automation_support_enterprise_oneoff_inr";
        var myrPlanId = "sleekflow_v9_chatbot_automation_support_enterprise_oneoff_myr";
        var sgdPlanId = "sleekflow_v9_chatbot_automation_support_enterprise_oneoff_sgd";

        var expectedPlanDefinition = new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "chatbot_automation_support_enterprise_oneoff", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "chatbot_automation_support_enterprise",
                    Quantity = 1
                }
            },
            new List<Price>
            {
                new Price("USD", 29999),
                new Price("AED", 110189),
                new Price("AUD", 44229),
                new Price("BRL", 161269),
                new Price("CAD", 38859),
                new Price("CNY", 213459),
                new Price("EUR", 29759),
                new Price("GBP", 25169),
                new Price("HKD", 235499),
                new Price("IDR", 450488000),
                new Price("INR", 1270799),
                new Price("MYR", 133109),
                new Price("SGD", 42129),
            },
            PlanTypes.AddOns,
            null,
            "v9",
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow);

        var usdPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(usdPlanId);
        var aedPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(aedPlanId);
        var audPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(audPlanId);
        var brlPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(brlPlanId);
        var cadPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(cadPlanId);
        var cnyPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(cnyPlanId);
        var eurPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(eurPlanId);
        var gbpPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(gbpPlanId);
        var hkdPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(hkdPlanId);
        var idrPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(idrPlanId);
        var inrPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(inrPlanId);
        var myrPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(myrPlanId);
        var sgdPlanResult = await _planDefinitionService.GetPlanDefinitionAsync(sgdPlanId);

        Assert.Multiple(() =>
        {
            Assert.That(usdPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(aedPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(audPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(brlPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(cadPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(cnyPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(eurPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(gbpPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(hkdPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(idrPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(inrPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(myrPlanResult, Is.EqualTo(expectedPlanDefinition));
            Assert.That(sgdPlanResult, Is.EqualTo(expectedPlanDefinition));
        });
    }

    [Test]
    public async Task GetPlanDefinition_InvalidId_ReturnsNull()
    {
        var planId = "sleekflow_hoball";

        var result = await _planDefinitionService.GetPlanDefinitionAsync(planId);

        Assert.IsNull(result);
    }
}