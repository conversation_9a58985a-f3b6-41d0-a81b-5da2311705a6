using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using MimeDetective;
using MimeDetective.Definitions;
using Sleekflow.Apis.CrmHub.Api;
using Sleekflow.Apis.CrmHub.Model;
using Sleekflow.Apis.FlowHub.Model;
using Travis_backend.AutomationDomain.Models;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.CompanyDomain.Services;
using Travis_backend.CompanyDomain.ViewModels;
using Travis_backend.Constants;
using Travis_backend.ContactDomain.Constants;
using Travis_backend.ContactDomain.Models;
using Travis_backend.ContactDomain.Services;
using Travis_backend.ContactDomain.ViewModels;
using Travis_backend.Controllers.MessageControllers;
using Travis_backend.ConversationDomain.Models;
using Travis_backend.ConversationDomain.Services;
using Travis_backend.ConversationServices;
using Travis_backend.Database;
using Travis_backend.Enums;
using Travis_backend.FlowHubs.Attributes;
using Travis_backend.FlowHubs.Interfaces;
using Travis_backend.FlowHubs.Mappers;
using Travis_backend.Helpers;
using Travis_backend.Infrastructures.Attributes;
using Travis_backend.MessageDomain.Models;
using Travis_backend.MessageDomain.ViewModels;
using SendMessageInput = Sleekflow.Apis.FlowHub.Model.SendMessageInput;

namespace Travis_backend.FlowHubs.Controllers;

[Route("FlowHub/Internals")]
[FlowHubAuthorization]
[SuppressMessage("ReSharper", "ConditionIsAlwaysTrueOrFalse")]
public class FlowHubInternalsCommandsController : ControllerBase
{
    private readonly ILogger<FlowHubInternalsCommandsController> _logger;
    private readonly ICompanyService _companyService;
    private readonly ICompanyTeamService _companyTeamService;
    private readonly IConversationMessageService _conversationMessageService;
    private readonly ApplicationDbContext _appDbContext;
    private readonly IUserProfileService _userProfileService;
    private readonly IConversationAssigneeService _conversationAssigneeService;
    private readonly IConversationHashtagService _conversationHashtagService;
    private readonly IEnumerable<IUpdateContactOwnerRelationshipsAssignmentStrategyHandler> _updateContactOwnerRelationshipsAssignmentStrategyHandlers;
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly IInflowActionsApi _inflowActionsApi;
    private readonly IUserProfileSafeDeleteService _userProfileSafeDeleteService;
    private readonly IProvidersApi _providersApi;

    public FlowHubInternalsCommandsController(
        ILogger<FlowHubInternalsCommandsController> logger,
        ICompanyService companyService,
        ICompanyTeamService companyTeamService,
        IConversationMessageService conversationMessageService,
        ApplicationDbContext appDbContext,
        IUserProfileService userProfileService,
        IConversationAssigneeService conversationAssigneeService,
        IConversationHashtagService conversationHashtagService,
        IEnumerable<IUpdateContactOwnerRelationshipsAssignmentStrategyHandler> updateContactOwnerRelationshipsAssignmentStrategyHandlers,
        IHttpClientFactory httpClientFactory,
        IInflowActionsApi inflowActionsApi,
        IUserProfileSafeDeleteService userProfileSafeDeleteService,
        IProvidersApi providersApi)
    {
        _logger = logger;
        _companyService = companyService;
        _companyTeamService = companyTeamService;
        _conversationMessageService = conversationMessageService;
        _appDbContext = appDbContext;
        _userProfileService = userProfileService;
        _conversationAssigneeService = conversationAssigneeService;
        _conversationHashtagService = conversationHashtagService;
        _updateContactOwnerRelationshipsAssignmentStrategyHandlers = updateContactOwnerRelationshipsAssignmentStrategyHandlers;
        _httpClientFactory = httpClientFactory;
        _inflowActionsApi = inflowActionsApi;
        _userProfileSafeDeleteService = userProfileSafeDeleteService;
        _providersApi = providersApi;
    }

    private async Task<List<string>> GetTeamIdsBySleekflowStaffAsync(Staff sleekflowStaff)
    {
        var companyId = sleekflowStaff.CompanyId;
        var identityId = sleekflowStaff.IdentityId;

        var companyTeams = await _companyTeamService.GetCompanyteamFromStaffId(companyId, identityId);

        return companyTeams.Select(t => t.Id.ToString()).ToList();
    }

    private class StaffDto
    {
        public long StaffId { get; set; }

        public string StaffIdentityId { get; set; }

        public string Name { get; set; }
    }

    private async Task<List<StaffDto>> GetStaffsAsync(string companyId)
    {
        var staffs = await _appDbContext.UserRoleStaffs
            .Where(s => s.CompanyId == companyId)
            .Include(s => s.Identity)
            .Select(
                s => new StaffDto
                {
                    Name = s.Identity.DisplayName, StaffIdentityId = s.Identity.Id, StaffId = s.Id
                })
            .ToListAsync();

        return staffs;
    }

    [HttpPost("Commands/AddInternalNoteToContact")]
    public async Task<IActionResult> AddInternalNoteToContact(
        [FromBody]
        AddInternalNoteToContactInput addInternalNoteToContactInput)
    {
        var companyId = addInternalNoteToContactInput.StateIdentity.SleekflowCompanyId;
        var contactId = addInternalNoteToContactInput.ContactId;
        var content = addInternalNoteToContactInput.Content;

        await AssertActiveContactExistByIdAsync(companyId, contactId);

        StaffDto atStaff = null;
        if (content.Contains('@'))
        {
            var allStaffs = await GetStaffsAsync(companyId);

            atStaff = allStaffs.FirstOrDefault(
                staff =>
                    content.Contains("@" + staff.Name)
                    || content.Contains("@" + staff.StaffId)
                    || content.Contains("@" + staff.StaffIdentityId));
        }

        var conversation = await _userProfileService.GetConversationByUserProfileId(
            companyId,
            contactId);
        var conversationId = conversation.Id;

        var conversationMessageNote = new ConversationMessage
        {
            Channel = ChannelTypes.Note,
            ConversationId = conversationId,
            MessageContent = atStaff == null
                ? content
                : content
                    .Replace("@" + atStaff.Name, atStaff.Name)
                    .Replace("@" + atStaff.StaffId, atStaff.Name)
                    .Replace("@" + atStaff.StaffIdentityId, atStaff.Name),
            MessageType = "text",
        };

        await _conversationMessageService.SendConversationNote(
            companyId,
            conversationId,
            null,
            conversationMessageNote,
            new ConversationNoteViewModel
            {
                AssigneeId = atStaff?.StaffIdentityId,
            });

        return Ok();
    }

    [HttpPost("Commands/SendMessage")]
    public async Task<IActionResult> SendMessage(
        [FromBody]
        SendMessageInput sendMessageInput)
    {
        var companyId = sendMessageInput.StateIdentity.SleekflowCompanyId;
        var fromTo = sendMessageInput.FromTo;
        var channel = sendMessageInput.Channel;
        var messageType = sendMessageInput.MessageType;
        var messageBody = sendMessageInput.MessageBody;

        var company = await _appDbContext.CompanyCompanies.FirstOrDefaultAsync(
            x => x.Id == companyId && x.IsDeleted == false);

        var conversation = await GetTargetConversation(fromTo, company.Id);

        var systemChannel = new List<string>
        {
            ChannelTypes.Note, "system"
        };
        var lastConversationMessage = await _appDbContext.ConversationMessages.AsNoTracking()
            .Where(x => x.ConversationId == conversation.Id && !systemChannel.Contains(x.Channel))
            .OrderByDescending(x => x.Id)
            .Take(1)
            .FirstOrDefaultAsync();

        if (lastConversationMessage == null)
        {
            var firstWhatsappTwilio = await _appDbContext.ConfigWhatsAppConfigs.AsNoTracking()
                .Where(x => x.CompanyId == conversation.CompanyId)
                .OrderBy(x => x.ConnectedDateTime)
                .Select(
                    x => new
                    {
                        x.Id, x.ConnectedDateTime
                    })
                .FirstOrDefaultAsync();

            var firstWhatsapp360dialog = await _appDbContext.ConfigWhatsApp360DialogConfigs.AsNoTracking()
                .Where(x => x.CompanyId == conversation.CompanyId)
                .OrderBy(x => x.CreatedAt)
                .Select(
                    x => new
                    {
                        x.Id, x.CreatedAt
                    })
                .FirstOrDefaultAsync();

            var firstWhatsappCloudApi = await _appDbContext.ConfigWhatsappCloudApiConfigs.AsNoTracking()
                .Where(x => x.CompanyId == conversation.CompanyId)
                .OrderBy(x => x.CreatedAt)
                .Select(
                    x => new
                    {
                        x.Id, x.CreatedAt
                    })
                .FirstOrDefaultAsync();

            var isUseTwilio = false;
            var isUse360Dialog = false;
            var isUseCloudApi = false;

            if (firstWhatsappTwilio != null && firstWhatsapp360dialog != null && firstWhatsappCloudApi != null)
            {
                isUseTwilio = firstWhatsappTwilio.ConnectedDateTime <= firstWhatsapp360dialog.CreatedAt &&
                              firstWhatsappTwilio.ConnectedDateTime < firstWhatsappCloudApi.CreatedAt;
                isUse360Dialog = firstWhatsappTwilio.ConnectedDateTime >= firstWhatsapp360dialog.CreatedAt &&
                                 firstWhatsapp360dialog.CreatedAt < firstWhatsappCloudApi.CreatedAt;

                isUseCloudApi = firstWhatsappCloudApi.CreatedAt <= firstWhatsappTwilio.ConnectedDateTime &&
                                firstWhatsappCloudApi.CreatedAt <= firstWhatsapp360dialog.CreatedAt;
            }
            else if (firstWhatsappTwilio != null)
            {
                isUseTwilio = true;
            }
            else if (firstWhatsapp360dialog != null)
            {
                isUse360Dialog = true;
            }
            else if (firstWhatsappCloudApi != null)
            {
                isUseCloudApi = true;
            }

            lastConversationMessage = new ConversationMessage();

            if (conversation.WhatsappUser != null && isUseTwilio)
                lastConversationMessage.Channel = ChannelTypes.WhatsappTwilio;
            else if (conversation.WhatsApp360DialogUser != null &&
                     conversation.WhatsApp360DialogUser.ChannelId.HasValue && isUse360Dialog)
                lastConversationMessage.Channel = ChannelTypes.Whatsapp360Dialog;
            else if (conversation.WhatsappCloudApiUser != null && isUseCloudApi)
                lastConversationMessage.Channel = ChannelTypes.WhatsappCloudApi;
            else if (conversation.SMSUser != null)
                lastConversationMessage.Channel = ChannelTypes.Sms;
        }
        else
        {
            lastConversationMessage.Id = 0;
        }

        var targetChannels = new List<TargetedChannelModel>();
        if (fromTo.ActualInstance is WhatsappCloudApiSendMessageInputFromTo)
        {
            var whatsappCloudApiSendMessageInputFromTo = fromTo.GetWhatsappCloudApiSendMessageInputFromTo();

            targetChannels.Add(
                new TargetedChannelModel
                {
                    channel = channel,
                    ids = new List<string>
                    {
                        whatsappCloudApiSendMessageInputFromTo.FromPhoneNumber
                    }
                });
        }
        else if (fromTo.ActualInstance is FacebookPageMessengerSendMessageInputFromTo)
        {
            throw new NotImplementedException();
        }
        else if (fromTo.ActualInstance is InstagramPageMessengerSendMessageInputFromTo)
        {
            throw new NotImplementedException();
        }

        if (targetChannels.Count > 0)
        {
            foreach (var targetChannel in targetChannels)
            {
                switch (targetChannel.channel)
                {
                    case ChannelTypes.WhatsappTwilio:
                    case "twilio_whatsapp":
                        if (conversation.UserProfile.WhatsAppAccount == null)
                            return null;

                        if (targetChannel.ids?.Count > 0)
                        {
                            if (!targetChannel.ids.Contains(
                                    ConversationHelper.GetInstanceId(conversation.UserProfile.WhatsAppAccount)))
                            {
                                await _userProfileService.SwitchWhatsappChannel(
                                    conversation.UserProfileId,
                                    new ChangeChatAPIInstance
                                    {
                                        InstanceId = targetChannel.ids.FirstOrDefault()
                                    });

                                conversation = await _appDbContext.Conversations
                                    .Where(x => x.Id == conversation.Id && x.CompanyId == company.Id)
                                    .Include(x => x.NaiveUser)
                                    .Include(x => x.facebookUser)
                                    .Include(X => X.EmailAddress)
                                    .Include(x => x.WhatsappUser)
                                    .Include(x => x.InstagramUser)
                                    .Include(x => x.WebClient)
                                    .Include(x => x.WeChatUser)
                                    .Include(x => x.LineUser)
                                    .Include(x => x.WhatsApp360DialogUser)
                                    .Include(x => x.WhatsappCloudApiUser)
                                    .Include(x => x.TelegramUser)
                                    .Include(x => x.ViberUser)
                                    .Include(x => x.SMSUser)
                                    .FirstOrDefaultAsync();
                            }
                        }

                        if (lastConversationMessage.Channel != ChannelTypes.WhatsappTwilio)
                            lastConversationMessage.Channel = ChannelTypes.WhatsappTwilio;

                        break;
                    case ChannelTypes.Whatsapp360Dialog:
                        if (conversation.UserProfile.WhatsApp360DialogUser == null)
                            return null;

                        if (targetChannel.ids?.Count > 0)
                        {
                            if (conversation.UserProfile.WhatsApp360DialogUser.ChannelId == null || !targetChannel.ids
                                    .Select(long.Parse).Contains(
                                        conversation.UserProfile.WhatsApp360DialogUser.ChannelId.Value))
                            {
                                await _userProfileService.SwitchWhatsapp360DialogChannel(
                                    conversation.CompanyId,
                                    conversation.UserProfileId,
                                    long.Parse(targetChannel.ids.First()));

                                conversation = await _appDbContext.Conversations
                                    .Where(x => x.Id == conversation.Id && x.CompanyId == company.Id)
                                    .Include(x => x.NaiveUser)
                                    .Include(x => x.facebookUser)
                                    .Include(x => x.InstagramUser)
                                    .Include(x => x.EmailAddress)
                                    .Include(x => x.WhatsappUser)
                                    .Include(x => x.WebClient)
                                    .Include(x => x.WeChatUser)
                                    .Include(x => x.LineUser)
                                    .Include(x => x.SMSUser)
                                    .Include(x => x.ViberUser)
                                    .Include(x => x.TelegramUser)
                                    .Include(x => x.WhatsApp360DialogUser)
                                    .Include(x => x.WhatsappCloudApiUser)
                                    .FirstOrDefaultAsync();
                            }
                        }

                        if (lastConversationMessage.Channel != ChannelTypes.Whatsapp360Dialog)
                            lastConversationMessage.Channel = ChannelTypes.Whatsapp360Dialog;

                        break;
                    case ChannelTypes.WhatsappCloudApi:
                        if (conversation.UserProfile.WhatsappCloudApiUser == null)
                            return null;

                        if (targetChannel.ids?.Count > 0)
                        {
                            if (conversation.UserProfile.WhatsappCloudApiUser.WhatsappChannelPhoneNumber == null ||
                                !targetChannel.ids.Contains(
                                    await _appDbContext.ConfigWhatsappCloudApiConfigs.AsNoTracking()
                                        .Where(
                                            x => x.CompanyId == conversation.CompanyId &&
                                                 x.WhatsappPhoneNumber == conversation.UserProfile
                                                     .WhatsappCloudApiUser.WhatsappChannelPhoneNumber)
                                        .Select(x => x.WhatsappPhoneNumber)
                                        .FirstOrDefaultAsync()))
                            {
                                await _userProfileService.SwitchWhatsappCloudApiChannel(
                                    conversation.CompanyId,
                                    conversation.UserProfileId,
                                    await _appDbContext.ConfigWhatsappCloudApiConfigs.AsNoTracking()
                                        .Where(
                                            x => x.CompanyId == conversation.CompanyId &&
                                                 x.WhatsappPhoneNumber == targetChannel.ids.FirstOrDefault())
                                        .Select(x => x.WhatsappPhoneNumber).FirstOrDefaultAsync());
                                conversation = await _appDbContext.Conversations.Where(x => x.Id == conversation.Id)
                                    .Include(x => x.NaiveUser)
                                    .Include(x => x.facebookUser)
                                    .Include(x => x.InstagramUser)
                                    .Include(x => x.EmailAddress)
                                    .Include(x => x.WhatsappUser)
                                    .Include(x => x.WebClient)
                                    .Include(x => x.WeChatUser)
                                    .Include(x => x.LineUser)
                                    .Include(x => x.SMSUser)
                                    .Include(x => x.ViberUser)
                                    .Include(x => x.TelegramUser)
                                    .Include(x => x.WhatsApp360DialogUser)
                                    .Include(x => x.WhatsappCloudApiUser)
                                    .FirstOrDefaultAsync();
                            }
                        }

                        if (lastConversationMessage.Channel != ChannelTypes.WhatsappCloudApi)
                            lastConversationMessage.Channel = ChannelTypes.WhatsappCloudApi;

                        break;
                    case ChannelTypes.Sms:
                        if (lastConversationMessage.Channel != ChannelTypes.Sms)
                            lastConversationMessage.Channel = ChannelTypes.Sms;

                        break;
                }
            }
        }

        conversation.conversationHashtags = await _appDbContext.ConversationHashtags
            .Where(x => x.ConversationId == conversation.Id)
            .ToListAsync();

        var preparedReplyMessage = PrepareReplyMessageAndChannel(conversation, lastConversationMessage);

        if (preparedReplyMessage.Channel != ChannelTypes.WhatsappCloudApi)
        {
            throw new NotImplementedException();
        }

        IList<ConversationMessage> sentConversationMessages;
        if (messageType is "audio" or "document" or "image" or "video")
        {
            var link = messageType switch
            {
                "audio" => messageBody.AudioMessage.Link,
                "document" => messageBody.DocumentMessage.Link,
                "image" => messageBody.ImageMessage.Link,
                "video" => messageBody.VideoMessage.Link,
                _ => throw new ArgumentOutOfRangeException()
            };
            var filename = messageType switch
            {
                "audio" => messageBody.AudioMessage.Filename,
                "document" => messageBody.DocumentMessage.Filename,
                "image" => messageBody.ImageMessage.Filename,
                "video" => messageBody.VideoMessage.Filename,
                _ => throw new ArgumentOutOfRangeException()
            };
            var caption = messageType switch
            {
                "audio" => messageBody.AudioMessage.Caption,
                "document" => messageBody.DocumentMessage.Caption,
                "image" => messageBody.ImageMessage.Caption,
                "video" => messageBody.VideoMessage.Caption,
                _ => throw new ArgumentOutOfRangeException()
            };

            preparedReplyMessage.MessageType = "file";
            preparedReplyMessage.MessageContent = caption;

            var httpClient = _httpClientFactory.CreateClient(HttpClientHandlerName.Default);
            var response = await httpClient.GetAsync(link, HttpCompletionOption.ResponseHeadersRead);
            response.EnsureSuccessStatusCode();

            using var memoryStream = new MemoryStream();
            await using var stream = await response.Content.ReadAsStreamAsync();
            await stream.CopyToAsync(memoryStream, 16384);
            memoryStream.Position = 0;

            // ContentType
            memoryStream.Position = 0;
            var blobContentType = response.Content.Headers.ContentType?.MediaType;
            var contentType = DetermineContentType(blobContentType, filename, memoryStream);

            // Prepare SendFileMessage
            memoryStream.Position = 0;
            var formFile = new FormFile(memoryStream, 0, memoryStream.Length, filename, filename)
            {
                Headers = new HeaderDictionary(),
                ContentType = contentType ?? "application/octet-stream",
                ContentDisposition = "form-data",
            };

            sentConversationMessages = await _conversationMessageService.SendFileMessage(
                conversation,
                preparedReplyMessage,
                new ConversationMessageViewModel
                {
                    files = new List<IFormFile>()
                    {
                        formFile
                    }
                });
        }
        else if (messageType == "text")
        {
            preparedReplyMessage.MessageContent = sendMessageInput.MessageBody.TextMessage.Text;
            preparedReplyMessage.MessageType = "text";

            sentConversationMessages =
                await _conversationMessageService.SendMessage(conversation, preparedReplyMessage);
        }
        else if (channel == ChannelTypes.WhatsappCloudApi
                 && messageType is "contacts" or "location" or "reaction" or "interactive" or "template")
        {
            preparedReplyMessage.MessageType = messageType;
            preparedReplyMessage.ExtendedMessagePayload = new ExtendedMessagePayload()
            {
                Channel = ChannelTypes.WhatsappCloudApi
            };

            preparedReplyMessage.ExtendedMessagePayload.SetExtendedMessagePayloadDetailWithType(
                MessageBodyMapper.ToCloudApiExtendedMessagePayloadDetail(messageType, messageBody));

            sentConversationMessages =
                await _conversationMessageService.SendMessage(conversation, preparedReplyMessage);
        }
        else
        {
            throw new NotImplementedException();
        }

        _logger.LogInformation(
            "Sent message to {ConversationId} with message id {MessageId} with SentMessageInput {SendMessageInput}",
            conversation.Id,
            sentConversationMessages.FirstOrDefault()?.Id,
            sendMessageInput);

        return Ok();
    }

    private static string DetermineContentType(
        string? blobContentType,
        string? filename,
        MemoryStream memoryStream)
    {
        string contentType;
        if (blobContentType != null)
        {
            if (blobContentType == "text/plain")
            {
                if (filename != null && filename.Trim().EndsWith(".csv"))
                {
                    contentType = "text/csv";
                }
                else if (filename != null && filename.Trim().EndsWith(".json"))
                {
                    contentType = "application/json";
                }
                else
                {
                    contentType = blobContentType;
                }
            }
            else
            {
                contentType = blobContentType;
            }
        }
        else
        {
            memoryStream.Position = 0;

            var inspector = new ContentInspectorBuilder
                {
                    Definitions = Default.All()
                }
                .Build();
            var inspectedDefinitionMatches = inspector.Inspect(memoryStream);
            var inspectedFileTypes = inspectedDefinitionMatches.Select(dm => dm.Definition.File).ToList();
            var inspectedFileType = inspectedFileTypes.FirstOrDefault();

            contentType = inspectedFileType?.MimeType ?? "application/octet-stream";
        }

        return contentType;
    }

    private ConversationMessage PrepareReplyMessageAndChannel(
        Conversation conversation,
        ConversationMessage conversationMessage)
    {
        var message = new ConversationMessage
        {
            ConversationId = conversation.Id,
            Channel = conversationMessage.Channel,
            facebookReceiver = conversation.facebookUser,
            whatsappReceiver = conversation.WhatsappUser,
            WebClientReceiver = conversation.WebClient,
            WeChatReceiver = conversation.WeChatUser,
            LineReceiver = conversation.LineUser,
            SMSReceiver = conversation.SMSUser,
            ViberReceiver = conversation.ViberUser,
            TelegramReceiver = conversation.TelegramUser,
            InstagramReceiver = conversation.InstagramUser,
            Whatsapp360DialogReceiver = conversation.WhatsApp360DialogUser,
            WhatsappCloudApiReceiver = conversation.WhatsappCloudApiUser,
            AnalyticTags = conversationMessage.AnalyticTags,
            DeliveryType = DeliveryType.FlowHubAction,
            IsSentFromSleekflow = true
        };

        return message;
    }

    private async Task<Conversation> GetTargetConversation(SendMessageInputFromTo fromTo, string companyId)
    {
        var toConversations = new List<Conversation>();

        if (fromTo.ActualInstance is WhatsappCloudApiSendMessageInputFromTo whatsappCloudApiSendMessageInputFromTo)
        {
            if (!string.IsNullOrEmpty(whatsappCloudApiSendMessageInputFromTo.ToPhoneNumber))
            {
                await AssertActiveContactExistByPhoneNumberAsync(companyId, whatsappCloudApiSendMessageInputFromTo.ToPhoneNumber);

                var toUserProfiles = (await _userProfileService.GetUserProfilesByFields(
                        companyId,
                        new List<Condition>
                        {
                            new Condition
                            {
                                FieldName = "phonenumber",
                                ConditionOperator = SupportedOperator.Equals,
                                Values = new List<string>
                                {
                                    PhoneNumberHelper.NormalizePhoneNumber(
                                        PhoneNumberHelper.FormatPhoneNumber(
                                            whatsappCloudApiSendMessageInputFromTo.ToPhoneNumber))
                                },
                                NextOperator = SupportedNextOperator.And
                            }
                        }))
                    .UserProfiles;

                foreach (var toUserProfile in toUserProfiles)
                {
                    var toConversation = await _userProfileService.GetConversationByUserProfileId(
                        companyId,
                        toUserProfile.Id);
                    toConversations.Add(toConversation);
                }
            }

            if (!string.IsNullOrEmpty(whatsappCloudApiSendMessageInputFromTo.ToContactId))
            {
                await AssertActiveContactExistByIdAsync(companyId, whatsappCloudApiSendMessageInputFromTo.ToContactId);

                var toUserProfile = await _userProfileService.GetUserProfileByUserProfileId(
                    companyId,
                    whatsappCloudApiSendMessageInputFromTo.ToContactId);
                var toConversation = await _userProfileService.GetConversationByUserProfileId(
                    companyId,
                    toUserProfile.Id);
                toConversations.Add(toConversation);
            }
        }
        else if (fromTo.ActualInstance is FacebookPageMessengerSendMessageInputFromTo)
        {
            throw new NotImplementedException();
        }
        else if (fromTo.ActualInstance is InstagramPageMessengerSendMessageInputFromTo)
        {
            throw new NotImplementedException();
        }

        var conversation = toConversations.FirstOrDefault();

        if (conversation == null)
        {
            throw new NotImplementedException();
        }

        return conversation;
    }

    [HttpPost("Commands/UpdateContactCollaboratorRelationships")]
    public async Task<IActionResult> UpdateContactCollaboratorRelationships(
        [FromBody]
        UpdateContactCollaboratorRelationshipsInput updateContactCollaboratorRelationshipsInput)
    {
        var companyId = updateContactCollaboratorRelationshipsInput.StateIdentity.SleekflowCompanyId;
        var contactId = updateContactCollaboratorRelationshipsInput.ContactId;

        await AssertActiveContactExistByIdAsync(companyId, contactId);

        var conversation = await _userProfileService.GetConversationByUserProfileId(
            companyId,
            contactId);

        var addStaffIdStrs = updateContactCollaboratorRelationshipsInput.AddStaffIds;
        if (addStaffIdStrs != null)
        {
            var staffs = new List<Staff>();
            foreach (var addStaffIdStr in addStaffIdStrs)
            {
                var staff = await _companyService.GetStaffAsync(companyId, addStaffIdStr);

                if (staff is not null)
                {
                    staffs.Add(staff);
                }
            }

            await _conversationAssigneeService.AddAdditionalAssignees(
                conversation,
                staffs.Select(s => s.Id).ToList(),
                null,
                false);
        }

        var removeStaffIdStrs = updateContactCollaboratorRelationshipsInput.RemoveStaffIds;
        if (removeStaffIdStrs != null)
        {
            var staffs = new List<Staff>();
            foreach (var removeStaffIdStr in removeStaffIdStrs)
            {
                var staff = await _companyService.GetStaffAsync(companyId, removeStaffIdStr);

                if (staff is not null)
                {
                    staffs.Add(staff);
                }
            }

            await _conversationAssigneeService.RemoveAdditionalAssignees(
                conversation,
                staffs.Select(s => s.Id).ToList(),
                null,
                false);
        }

        var setStaffIdStrs = updateContactCollaboratorRelationshipsInput.SetStaffIds;
        if (setStaffIdStrs != null)
        {
            var staffs = new List<Staff>();
            foreach (var setStaffIdStr in setStaffIdStrs)
            {
                var staff = await _companyService.GetStaffAsync(companyId, setStaffIdStr);

                if (staff is not null)
                {
                    staffs.Add(staff);
                }
            }

            await _conversationAssigneeService.ReplaceAdditionalAssignees(
                conversation,
                staffs.Select(s => s.IdentityId).ToList(),
                null,
                false);
        }


        return Ok();
    }

    [HttpPost("Commands/UpdateContactConversationStatus")]
    public async Task<IActionResult> UpdateContactConversationStatus(
        [FromBody]
        UpdateContactConversationStatusInput updateContactConversationStatusInput)
    {
        var companyId = updateContactConversationStatusInput.StateIdentity.SleekflowCompanyId;
        var contactId = updateContactConversationStatusInput.ContactId;
        var status = updateContactConversationStatusInput.Status;

        await AssertActiveContactExistByIdAsync(companyId, contactId);

        var conversation =
            await _userProfileService.GetConversationByUserProfileId(
                companyId,
                contactId);

        await _conversationMessageService.ChangeConversationStatus(
            conversation.Id,
            null,
            new StatusViewModel()
            {
                Status = status
            });

        return Ok();
    }

    [HttpPost("Commands/UpdateContactLabelRelationships")]
    public async Task<IActionResult> UpdateContactLabelRelationships(
        [FromBody]
        UpdateContactLabelRelationshipsInput updateContactLabelRelationshipsInput)
    {
        var companyId = updateContactLabelRelationshipsInput.StateIdentity.SleekflowCompanyId;
        var contactId = updateContactLabelRelationshipsInput.ContactId;

        await AssertActiveContactExistByIdAsync(companyId, contactId);

        var conversation =
            await _userProfileService.GetConversationByUserProfileId(
                companyId,
                contactId,
                "closed");

        var addLabels = updateContactLabelRelationshipsInput.AddLabels;
        if (addLabels != null)
        {
            var labelsAddedConversation = await _conversationHashtagService.AddConversationHashtag(
                companyId,
                conversation.Id,
                null,
                addLabels
                    .Select(
                        l => new ConversationHashtagViewModel()
                        {
                            Hashtag = l
                        })
                    .ToList(),
                isTriggerAutomation: false);
        }

        var removeLabels = updateContactLabelRelationshipsInput.RemoveLabels;
        if (removeLabels != null)
        {
            var labelsRemovedConversation = await _conversationHashtagService.RemoveConversationHashtag(
                companyId,
                conversation.Id,
                null,
                removeLabels
                    .Select(
                        l => new ConversationHashtagViewModel()
                        {
                            Hashtag = l
                        })
                    .ToList(),
                isTriggerAutomation: false);
        }

        var setLabels = updateContactLabelRelationshipsInput.SetLabels;
        if (setLabels != null)
        {
            var labelsSetConversation = await _conversationHashtagService.SetConversationHashtag(
                companyId,
                conversation.Id,
                null,
                setLabels
                    .Select(
                        l => new ConversationHashtagViewModel()
                        {
                            Hashtag = l
                        })
                    .ToList(),
                isTriggerAutomation: false);
        }

        return Ok();
    }

    [HttpPost("Commands/UpdateContactListRelationships")]
    public async Task<IActionResult> UpdateContactListRelationships(
        [FromBody]
        UpdateContactListRelationshipsInput updateContactListRelationshipsInput)
    {
        var companyId = updateContactListRelationshipsInput.StateIdentity.SleekflowCompanyId;
        var contactId = updateContactListRelationshipsInput.ContactId;

        await AssertActiveContactExistByIdAsync(companyId, contactId);

        var addListIds = updateContactListRelationshipsInput.AddListIds;
        if (addListIds != null)
        {
            foreach (var listId in addListIds)
            {
                try
                {
                    await _userProfileService.AddToUserList(
                        companyId,
                        long.Parse(listId),
                        new UserProfileIdsViewModel
                        {
                            UserProfileIds = new List<string>
                            {
                                contactId
                            }
                        });
                }
                catch (Exception ex)
                {
                    _logger.LogError(
                        ex,
                        "[FlowHub {MethodName} Command Executor] Company {CompanyId} error adding user profile {UserProfileId}" +
                        " to contact list {ListId}. {ExceptionMessage}",
                        nameof(UpdateContactListRelationships),
                        companyId,
                        contactId,
                        listId,
                        ex.Message);
                }
            }
        }

        var removeListIds = updateContactListRelationshipsInput.RemoveListIds;
        if (removeListIds != null)
        {
            foreach (var listId in removeListIds)
            {
                try
                {
                    await _userProfileService.RemoveFromUserList(
                        companyId,
                        long.Parse(listId),
                        new UserProfileIdsViewModel
                        {
                            UserProfileIds = new List<string>
                            {
                                contactId
                            }
                        });
                }
                catch (Exception ex)
                {
                    _logger.LogError(
                        ex,
                        "[FlowHub {MethodName} Command Executor] Company {CompanyId} error removing user profile {UserProfileId}" +
                        " from contact list {ListId}. {ExceptionMessage}",
                        nameof(UpdateContactListRelationships),
                        companyId,
                        contactId,
                        listId,
                        ex.Message);
                }
            }
        }

        var setListIdStrs = updateContactListRelationshipsInput.SetListIds;
        if (setListIdStrs != null)
        {
            var setListIds = setListIdStrs.Select(s => long.Parse(s)).ToList();

            var existingLists = await _appDbContext.CompanyImportedUserProfiles
                .Where(x => x.UserProfileId == contactId)
                .ToListAsync();
            var existingListIds = existingLists.Select(x => x.ImportContactHistoryId).ToList();
            var listsToRemove = existingListIds.Except(setListIds).ToList();
            var listsToAdd = setListIds.Except(existingListIds).ToList();

            // Remove lists
            foreach (var listId in listsToRemove)
            {
                try
                {
                    await _userProfileService.RemoveFromUserList(
                        companyId,
                        listId,
                        new UserProfileIdsViewModel
                        {
                            UserProfileIds = new List<string>
                            {
                                contactId
                            }
                        });
                }
                catch (Exception ex)
                {
                    _logger.LogError(
                        ex,
                        "[FlowHub {MethodName} Command Executor] [Set contact list for user profile] Company {CompanyId} " +
                        "error removing user profile {UserProfileId} from contact list {ListId}. {ExceptionMessage}",
                        nameof(UpdateContactListRelationships),
                        companyId,
                        contactId,
                        listId,
                        ex.Message);
                }
            }

            // Add lists
            foreach (var listId in listsToAdd)
            {
                try
                {
                    await _userProfileService.AddToUserList(
                        companyId,
                        listId,
                        new UserProfileIdsViewModel
                        {
                            UserProfileIds = new List<string>
                            {
                                contactId
                            }
                        });
                }
                catch (Exception ex)
                {
                    _logger.LogError(
                        ex,
                        "[FlowHub {MethodName} Command Executor] [Set contact list for user profile] Company {CompanyId} " +
                        "error adding user profile {UserProfileId} to contact list {ListId}. {ExceptionMessage}",
                        nameof(UpdateContactListRelationships),
                        companyId,
                        contactId,
                        listId,
                        ex.Message);
                }
            }
        }

        return Ok();
    }

    [HttpPost("Commands/UpdateContactOwnerRelationships")]
    public async Task<IActionResult> UpdateContactOwnerRelationships(
        [FromBody]
        UpdateContactOwnerRelationshipsInput updateContactOwnerRelationshipsInput)
    {
        var companyId = updateContactOwnerRelationshipsInput.StateIdentity.SleekflowCompanyId;
        var contactId = updateContactOwnerRelationshipsInput.ContactId;

        await AssertActiveContactExistByIdAsync(companyId, contactId);

        var strategyHandler = _updateContactOwnerRelationshipsAssignmentStrategyHandlers
            .FirstOrDefault(
                x => x.StrategyName.Equals(
                    updateContactOwnerRelationshipsInput.AssignmentStrategy,
                    StringComparison.OrdinalIgnoreCase));

        if (strategyHandler is null)
        {
            throw new NotImplementedException(
                $"No handler implemented for strategy {updateContactOwnerRelationshipsInput.AssignmentStrategy}");
        }

        await strategyHandler.HandleAsync(updateContactOwnerRelationshipsInput);

        return Ok();
    }

    [HttpPost("Commands/UpdateContactProperties")]
    public async Task<IActionResult> UpdateContactProperties(
        [FromBody]
        UpdateContactPropertiesInput updateContactPropertiesInput)
    {
        var companyId = updateContactPropertiesInput.StateIdentity.SleekflowCompanyId;
        var contactId = updateContactPropertiesInput.ContactId;

        await AssertActiveContactExistByIdAsync(companyId, contactId);

        var addCustomFieldsViewModels = await GetAddCustomFieldsViewModels(
            companyId,
            updateContactPropertiesInput.PropertiesDict.Select(ce => KeyValuePair.Create(ce.Key, ce.Value)).ToList());

        if (addCustomFieldsViewModels.Any() == false)
        {
            return Ok();
        }

        await _userProfileService.UpdateUserProfileCustomFields(
            companyId,
            contactId,
            staffId: null,
            addCustomFieldsViewModels: addCustomFieldsViewModels);

        return Ok();
    }

    [HttpPost("Commands/CreateSalesforceObject")]
    public async Task<IActionResult> CreateSalesforceObject(
        [FromBody]
        Sleekflow.Apis.FlowHub.Model.CreateSalesforceObjectInput createSalesforceObjectInput)
    {
        var companyId = createSalesforceObjectInput.StateIdentity.SleekflowCompanyId;
        var salesforceConnectionId = createSalesforceObjectInput.SalesforceConnectionId;
        var objectType = createSalesforceObjectInput.ObjectType;
        var objectProperties = createSalesforceObjectInput.ObjectProperties;

        var isSetOwnerByUserMappingConfig = createSalesforceObjectInput.IsSetOwnerByUserMappingConfig;
        var sleekflowUserIdForMapping = createSalesforceObjectInput.SleekflowUserIdForMapping;
        if (isSetOwnerByUserMappingConfig && !string.IsNullOrWhiteSpace(sleekflowUserIdForMapping))
        {
            var userMappingConfig = (await _providersApi.ProvidersGetProviderUserMappingConfigPostAsync(
                getProviderUserMappingConfigInput: new GetProviderUserMappingConfigInput(
                    sleekflowCompanyId: companyId,
                    providerName: "salesforce-integrator",
                    providerConnectionId: salesforceConnectionId))).Data.UserMappingConfig;

            var mappedSalesforceUserId = userMappingConfig.UserMappings.Find(
                m => m.SleekflowUserId == sleekflowUserIdForMapping)?.ProviderUserId;
            if (!string.IsNullOrEmpty(mappedSalesforceUserId))
            {
                objectProperties.Add("OwnerId", mappedSalesforceUserId);
            }
        }

        await _inflowActionsApi.InflowActionsCreateSalesforceObjectPostAsync(
            createSalesforceObjectInput: new Sleekflow.Apis.CrmHub.Model.CreateSalesforceObjectInput(
                sleekflowCompanyId: companyId,
                salesforceConnectionId: salesforceConnectionId,
                entityTypeName: objectType,
                dict: objectProperties));

        return Ok();
    }

    [HttpPost("Commands/UpdateSalesforceObject")]
    public async Task<IActionResult> UpdateSalesforceObject(
        [FromBody]
        Sleekflow.Apis.FlowHub.Model.UpdateSalesforceObjectInput updateSalesforceObjectInput)
    {
        var companyId = updateSalesforceObjectInput.StateIdentity.SleekflowCompanyId;
        var salesforceConnectionId = updateSalesforceObjectInput.SalesforceConnectionId;
        var objectProperties = updateSalesforceObjectInput.ObjectProperties;
        var objectId = updateSalesforceObjectInput.ObjectId;
        var objectType = updateSalesforceObjectInput.ObjectType;

        await _inflowActionsApi.InflowActionsUpdateSalesforceObjectPostAsync(
            updateSalesforceObjectInput: new Sleekflow.Apis.CrmHub.Model.UpdateSalesforceObjectInput(
                sleekflowCompanyId: companyId,
                salesforceConnectionId: salesforceConnectionId,
                entityTypeName: objectType,
                objectId: objectId,
                dict: objectProperties));

        return Ok();
    }

    [HttpPost("Commands/SearchSalesforceObject")]
    public async Task<IActionResult> SearchSalesforceObject(
        [FromBody]
        Sleekflow.Apis.FlowHub.Model.SearchSalesforceObjectInput searchSalesforceObjectInput)
    {
        var companyId = searchSalesforceObjectInput.StateIdentity.SleekflowCompanyId;
        var salesforceConnectionId = searchSalesforceObjectInput.SalesforceConnectionId;
        var objectType = searchSalesforceObjectInput.ObjectType;
        var conditions = searchSalesforceObjectInput.Conditions.Select(
            c => new Sleekflow.Apis.CrmHub.Model.SearchObjectCondition(c.FieldName, c.Operator, c.Value)).ToList();

        var record = (await _inflowActionsApi.InflowActionsSearchSalesforceObjectPostAsync(
            searchSalesforceObjectInput: new Sleekflow.Apis.CrmHub.Model.SearchSalesforceObjectInput(
                sleekflowCompanyId: companyId,
                salesforceConnectionId: salesforceConnectionId,
                entityTypeName: objectType,
                conditions: conditions))).Data.Record;

        return Ok(record);
    }

    [UpdateUserProfileTriggerSource(UpdateUserProfileTriggerSource.FlowHubAction)]
    [HttpPost("Commands/CreateContact")]
    public async Task<IActionResult> CreateContact(
        [FromBody]
        CreateContactInput createContactInput)
    {
        var companyId = createContactInput.StateIdentity.SleekflowCompanyId;
        var phoneNumber = createContactInput.PhoneNumber;
        var contactProperties = createContactInput.ContactProperties;

        var userProfileIdStatus = await GetUserProfileIdStatusByPhoneNumberAsync(
            companyId,
            phoneNumber);

        if (!string.IsNullOrWhiteSpace(userProfileIdStatus.UserProfileId))
        {
            if (userProfileIdStatus.ActiveStatus is ActiveStatus.Inactive)
            {
                _logger.LogInformation(
                    "[FlowHub Commands/CreateContact] Recovering soft deleted user profile {UserProfileId} for company {CompanyId}",
                    userProfileIdStatus.UserProfileId,
                    companyId);

                await _userProfileSafeDeleteService.RecoverSoftDeletedUserProfilesAsync(
                    companyId,
                    new HashSet<string>() { userProfileIdStatus.UserProfileId },
                    new UserProfileRecoveryTriggerContext(
                        UpdateUserProfileTriggerSource.FlowHubAction,
                        null));
            }

            return Ok();
        }

        var fieldEntries = contactProperties;
        fieldEntries.Add("PhoneNumber", phoneNumber);
        var newProfileViewModel = await GetNewProfileViewModel(companyId, fieldEntries);

        await _userProfileService.AddNewUserProfileOnly(
            companyId,
            newProfileViewModel,
            false);

        return Ok();
    }

    [UpdateUserProfileTriggerSource(UpdateUserProfileTriggerSource.FlowHubAction)]
    [HttpPost("Commands/CreateContactWithSalesforceUserMapping")]
    public async Task<IActionResult> CreateContactWithSalesforceUserMapping(
        [FromBody]
        CreateContactWithSalesforceUserMappingInput createContactWithSalesforceUserMappingInput)
    {
        var companyId = createContactWithSalesforceUserMappingInput.StateIdentity.SleekflowCompanyId;
        var phoneNumber = createContactWithSalesforceUserMappingInput.PhoneNumber;
        var salesforceUserIdForMapping = createContactWithSalesforceUserMappingInput.SalesforceUserIdForMapping;
        var salesforceConnectionId = createContactWithSalesforceUserMappingInput.SalesforceConnectionId;
        var contactProperties = createContactWithSalesforceUserMappingInput.ContactProperties;

        var userProfileIdStatus = await GetUserProfileIdStatusByPhoneNumberAsync(
            companyId,
            phoneNumber);

        UserProfile userProfile = null;

        if (!string.IsNullOrWhiteSpace(userProfileIdStatus.UserProfileId)
            && userProfileIdStatus.ActiveStatus is ActiveStatus.Inactive)
        {
            _logger.LogInformation(
                "[FlowHub Commands/CreateContact] Recovering soft deleted user profile {UserProfileId} for company {CompanyId}",
                userProfileIdStatus.UserProfileId,
                companyId);

            await _userProfileSafeDeleteService.RecoverSoftDeletedUserProfilesAsync(
                companyId,
                new HashSet<string>() { userProfileIdStatus.UserProfileId },
                new UserProfileRecoveryTriggerContext(
                    UpdateUserProfileTriggerSource.FlowHubAction,
                    null));

            userProfile = await _appDbContext.UserProfiles.FirstOrDefaultAsync(u => u.Id == userProfileIdStatus.UserProfileId);
        }
        else
        {
            var fieldEntries = contactProperties;
            fieldEntries.Add("PhoneNumber", phoneNumber);
            var newProfileViewModel = await GetNewProfileViewModel(companyId, fieldEntries);

            userProfile = (await _userProfileService.AddNewUserProfileOnly(
                companyId,
                newProfileViewModel,
                false)).FirstOrDefault();
        }

        if (!string.IsNullOrWhiteSpace(salesforceUserIdForMapping))
        {
            var userMappingConfig = (await _providersApi.ProvidersGetProviderUserMappingConfigPostAsync(
                getProviderUserMappingConfigInput: new GetProviderUserMappingConfigInput(
                    sleekflowCompanyId: companyId,
                    providerName: "salesforce-integrator",
                    providerConnectionId: salesforceConnectionId))).Data.UserMappingConfig;

            var mappedSleekflowUserId = userMappingConfig.UserMappings.Find(
                m => m.ProviderUserId == salesforceUserIdForMapping)?.SleekflowUserId;
            if (!string.IsNullOrEmpty(mappedSleekflowUserId))
            {
                await _userProfileService.SetFieldValueByFieldNameSafe(
                    userProfile,
                    "ContactOwner",
                    mappedSleekflowUserId);
            }
        }

        return Ok();
    }

    [HttpPost("Commands/GetConversionLastMessages")]
    public async Task<IActionResult> GetConversationLastMessages(
        [FromBody]
        GetConversationLastMessagesInput getConversationLastMessagesInput)
    {
        var conversationMessages =
            await _conversationMessageService.GetConversationLastMessagesAsync(
                getConversationLastMessagesInput.StateIdentity.SleekflowCompanyId,
                getConversationLastMessagesInput.ContactId,
                getConversationLastMessagesInput.Offset,
                getConversationLastMessagesInput.Limit);

        var conversationMessagesOutputs = conversationMessages.Select(
            c => new ConversationMessageOutput(
                c.Id.ToString(),
                c.MessageUniqueID,
                c.MessageContent,
                c.IsSentFromSleekflow)).ToList();
        return Ok(new GetConversationLastMessagesOutput(conversationMessagesOutputs));
    }

    [HttpPost("Commands/UpdateContactPropertiesByPropertyKey")]
    public async Task<IActionResult> UpdateContactPropertiesByPropertyKey(
        [FromBody]
        UpdateContactPropertiesByPropertyKeyInput updateContactPropertiesByPropertyKeyInput)
    {
        var companyId = updateContactPropertiesByPropertyKeyInput.StateIdentity.SleekflowCompanyId;
        var contactPropertyKeyId = updateContactPropertiesByPropertyKeyInput.ContactPropertyKeyId;
        var contactPropertyKeyValue = updateContactPropertiesByPropertyKeyInput.ContactPropertyKeyValue;

        await AssertActiveContactExistByPropertyKeyAsync(
            companyId,
            contactPropertyKeyId,
            contactPropertyKeyValue);

        var addCustomFieldsViewModels = await GetAddCustomFieldsViewModels(
            companyId,
            updateContactPropertiesByPropertyKeyInput.PropertiesDict.
                Select(ce => KeyValuePair.Create(ce.Key, ce.Value)).ToList());

        if (addCustomFieldsViewModels.Any() == false)
        {
            return Ok();
        }

        var userProfileId = await GetUserProfileIdByPropertyKeyAsync(
            companyId,
            contactPropertyKeyId,
            contactPropertyKeyValue);

        await _userProfileService.UpdateUserProfileCustomFields(
            companyId,
            userProfileId,
            staffId: null,
            addCustomFieldsViewModels: addCustomFieldsViewModels);

        return Ok();
    }

    private async Task<List<AddCustomFieldsViewModel>> GetAddCustomFieldsViewModels(
        string companyId,
        IReadOnlyCollection<KeyValuePair<string, object>> changeEntries)
    {
        if (changeEntries == null || changeEntries.Any() == false)
        {
            return new List<AddCustomFieldsViewModel>();
        }

        var companyCustomFields = await _appDbContext.CompanyCustomUserProfileFields
            .Where(x => x.CompanyId == companyId)
            .ToListAsync();
        var customFieldNameToCustomFieldDict = companyCustomFields
            .GroupBy(cf => cf.FieldName, StringComparer.InvariantCulture)
            .ToDictionary(cf => cf.Key, cf => cf.First());
        var customFieldIdToCustomFieldDict = companyCustomFields
            .GroupBy(cf => cf.Id, StringComparer.InvariantCulture)
            .ToDictionary(cf => cf.Key, cf => cf.First());

        var addCustomFieldsViewModels = new List<AddCustomFieldsViewModel>();

        // Sync unified fields to the matched custom fields
        foreach (var changeEntry in changeEntries)
        {
            var fieldNameOrId = changeEntry.Key;
            var value = changeEntry.Value?.ToString();

            if (fieldNameOrId == "LastName")
            {
                addCustomFieldsViewModels.Add(
                    new AddCustomFieldsViewModel
                    {
                        CustomFieldName = "LastName", CustomValue = value,
                    });

                continue;
            }

            if (fieldNameOrId == "FirstName")
            {
                addCustomFieldsViewModels.Add(
                    new AddCustomFieldsViewModel
                    {
                        CustomFieldName = "FirstName", CustomValue = value,
                    });

                continue;
            }

            if (fieldNameOrId == "PhoneNumber")
            {
                if (value != null)
                {
                    addCustomFieldsViewModels.Add(
                        new AddCustomFieldsViewModel
                        {
                            CustomFieldName = "PhoneNumber", CustomValue = value,
                        });
                }

                continue;
            }

            if (fieldNameOrId == "Email")
            {
                if (value != null)
                {
                    addCustomFieldsViewModels.Add(
                        new AddCustomFieldsViewModel
                        {
                            CustomFieldName = "Email", CustomValue = value,
                        });
                }

                continue;
            }

            CompanyCustomUserProfileField companyCustomField;
            if (customFieldIdToCustomFieldDict.TryGetValue(fieldNameOrId, out var value1))
            {
                companyCustomField = value1;
            }
            else if (customFieldNameToCustomFieldDict.TryGetValue(fieldNameOrId, out var value2))
            {
                companyCustomField = value2;
            }
            else
            {
                continue;
            }

            addCustomFieldsViewModels.Add(
                new AddCustomFieldsViewModel
                {
                    CustomFieldId = companyCustomField.Id.ToString(),
                    CustomFieldName = companyCustomField.FieldName,
                    CustomValue = value,
                });
        }

        return addCustomFieldsViewModels;
    }

    private async Task<NewProfileViewModel> GetNewProfileViewModel(
        string companyId,
        IReadOnlyCollection<KeyValuePair<string, object>> fieldEntries)
    {
        if (fieldEntries == null || fieldEntries.Any() == false)
        {
            return new NewProfileViewModel();
        }

        var companyCustomFields = await _appDbContext.CompanyCustomUserProfileFields
            .Where(x => x.CompanyId == companyId)
            .ToListAsync();
        var customFieldNameToCustomFieldDict = companyCustomFields
            .GroupBy(cf => cf.FieldName, StringComparer.InvariantCulture)
            .ToDictionary(cf => cf.Key, cf => cf.First());
        var customFieldIdToCustomFieldDict = companyCustomFields
            .GroupBy(cf => cf.Id, StringComparer.InvariantCulture)
            .ToDictionary(cf => cf.Key, cf => cf.First());

        var newProfileViewModel = new NewProfileViewModel();
        var addCustomFieldsViewModels = new List<AddCustomFieldsViewModel>();

        // Sync unified fields to the matched custom fields
        foreach (var fieldEntry in fieldEntries)
        {
            var fieldNameOrId = fieldEntry.Key;
            var value = fieldEntry.Value?.ToString();

            if (fieldNameOrId == "LastName")
            {
                addCustomFieldsViewModels.Add(
                    new AddCustomFieldsViewModel
                    {
                        CustomFieldName = "LastName", CustomValue = value,
                    });

                newProfileViewModel.LastName = value;

                continue;
            }

            if (fieldNameOrId == "FirstName")
            {
                addCustomFieldsViewModels.Add(
                    new AddCustomFieldsViewModel
                    {
                        CustomFieldName = "FirstName", CustomValue = value,
                    });

                newProfileViewModel.FirstName = value;

                continue;
            }

            if (fieldNameOrId == "PhoneNumber")
            {
                if (value != null)
                {
                    addCustomFieldsViewModels.Add(
                        new AddCustomFieldsViewModel
                        {
                            CustomFieldName = "PhoneNumber", CustomValue = value,
                        });
                }

                newProfileViewModel.PhoneNumber = value;

                continue;
            }

            if (fieldNameOrId == "Email")
            {
                if (value != null)
                {
                    addCustomFieldsViewModels.Add(
                        new AddCustomFieldsViewModel
                        {
                            CustomFieldName = "Email", CustomValue = value,
                        });
                }

                newProfileViewModel.Email = value;

                continue;
            }

            CompanyCustomUserProfileField companyCustomField;
            if (customFieldIdToCustomFieldDict.TryGetValue(fieldNameOrId, out var value1))
            {
                companyCustomField = value1;
            }
            else if (customFieldNameToCustomFieldDict.TryGetValue(fieldNameOrId, out var value2))
            {
                companyCustomField = value2;
            }
            else
            {
                continue;
            }

            addCustomFieldsViewModels.Add(
                new AddCustomFieldsViewModel
                {
                    CustomFieldId = companyCustomField.Id,
                    CustomFieldName = companyCustomField.FieldName,
                    CustomValue = value,
                });
        }

        newProfileViewModel.UserProfileFields = addCustomFieldsViewModels;

        return newProfileViewModel;
    }

    private async Task AssertActiveContactExistByIdAsync(string companyId, string userProfileId)
    {
        var contactStatus = await GetUserProfileIdStatusByIdAsync(companyId, userProfileId);

        if (contactStatus.ActiveStatus != ActiveStatus.Active)
        {
            _logger.LogWarning(
                "Contact {ContactId} in company {CompanyId} is with active status {ActiveStatus} ",
                userProfileId,
                companyId,
                contactStatus.ActiveStatus);

            throw new InvalidOperationException($"Active contact with id {userProfileId} not found in company {companyId}");
        }
    }

    private async Task AssertActiveContactExistByPhoneNumberAsync(string companyId, string phoneNumber)
    {
        var contactStatus = await GetUserProfileIdStatusByPhoneNumberAsync(companyId, phoneNumber);

        if (contactStatus.ActiveStatus != ActiveStatus.Active)
        {
            _logger.LogWarning(
                "Contact {ContactId} with phone number {PhoneNumber} in company {CompanyId} is with active status {ActiveStatus} ",
                contactStatus.UserProfileId,
                phoneNumber,
                companyId,
                contactStatus.ActiveStatus);

            throw new InvalidOperationException($"Active contact with phone number {phoneNumber} not found in company {companyId}");
        }
    }

    private async Task<(string UserProfileId, ActiveStatus? ActiveStatus)> GetUserProfileIdStatusByIdAsync(string companyId, string userProfileId)
    {
        var userProfile = await _appDbContext.UserProfiles
            .Where(x => x.CompanyId == companyId && x.Id == userProfileId)
            .Select(x
                => new
                {
                    x.Id,
                    x.ActiveStatus
                })
            .FirstOrDefaultAsync();

        return (userProfile?.Id, userProfile?.ActiveStatus);
    }

    private async Task<(string UserProfileId, ActiveStatus? ActiveStatus)> GetUserProfileIdStatusByPhoneNumberAsync(string companyId, string phoneNumber)
    {
        var userProfile = await _appDbContext.UserProfiles
            .Where(x => x.CompanyId == companyId && x.PhoneNumber == PhoneNumberHelper.NormalizePhoneNumber(phoneNumber))
            .Select(x
                => new
                {
                    x.Id,
                    x.ActiveStatus
                })
            .FirstOrDefaultAsync();

        return (userProfile?.Id, userProfile?.ActiveStatus);
    }

    private async Task<(string UserProfileId, ActiveStatus? ActiveStatus)>
        GetUserProfileIdStatusByPropertyKeyAsync(
            string companyId,
            string userProfileCustomFieldId,
            string userProfileCustomFieldValue)
    {
        var userProfile = await _appDbContext.UserProfiles
            .Include(x => x.CustomFields)
            .Where(x => x.CompanyId == companyId
                        && x.CustomFields.Any(f =>
                            f.CompanyDefinedFieldId == userProfileCustomFieldId && f.Value == userProfileCustomFieldValue))
            .Select(x
                => new
                {
                    x.Id,
                    x.ActiveStatus
                })
            .FirstOrDefaultAsync();

        return (userProfile?.Id, userProfile?.ActiveStatus);
    }

    private async Task AssertActiveContactExistByPropertyKeyAsync(
        string companyId,
        string userProfileCustomFieldId,
        string userProfileCustomFieldValue)
    {
        var contactStatus = await GetUserProfileIdStatusByPropertyKeyAsync(
            companyId,
            userProfileCustomFieldId,
            userProfileCustomFieldValue);

        if (contactStatus.ActiveStatus != ActiveStatus.Active)
        {
            _logger.LogWarning(
                "No active contact in company {CompanyId} " +
                "with custom field Id {UserProfileCustomFieldId} and value {UserProfileCustomFieldValue}",
                companyId,
                userProfileCustomFieldId,
                userProfileCustomFieldValue);

            throw new InvalidOperationException(
                $"Active contact with custom field Id {userProfileCustomFieldId} and value {userProfileCustomFieldValue} " +
                $"not found in company {companyId}");
        }
    }

    private async Task<string> GetUserProfileIdByPropertyKeyAsync(
            string companyId,
            string userProfileCustomFieldId,
            string userProfileCustomFieldValue)
    {
        var userProfile = await _appDbContext.UserProfiles
            .Include(x => x.CustomFields)
            .Where(x => x.CompanyId == companyId
                        && x.CustomFields.Any(f =>
                            f.CompanyDefinedFieldId == userProfileCustomFieldId && f.Value == userProfileCustomFieldValue))
            .Select(x
                => new
                {
                    x.Id
                })
            .FirstOrDefaultAsync();

        return userProfile.Id;
    }
}