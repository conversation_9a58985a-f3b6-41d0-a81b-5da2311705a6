﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using GraphApi.Client.ApiClients.Models;
using Microsoft.AspNetCore.Mvc.ModelBinding.Validation;
using Newtonsoft.Json;
using Sleekflow.Apis.MessagingHub.Model;
using Travis_backend.Models.ChatChannelConfig;

namespace Travis_backend.Data.WhatsappCloudApi;

public record ConnectWabaRequest(
    [Required]
    string FacebookAppUserToken);

public record ConnectWabaWithFacebookAuthorizationCodeRequest(
    [Required]
    [JsonProperty("facebook_authorization_code")]
    string FacebookAuthorizationCode);

public record ConnectWabaPhoneNumberRequest(
    [Required]
    string ChannelName,
    [Required]
    string WabaId,
    [Required]
    string WabaPhoneNumberId,
    string Pin = null);

public record UpdateWabaPhoneNumberRequest(
    [Required]
    string WabaId,
    [Required]
    string WabaPhoneNumberId,
    [Required]
    string ChannelName);

public record CreateOptinRequest(
    [Required]
    string WabaId,
    [Required]
    string TemplateName,
    [Required]
    string TemplateLanguage);

public record UpdateOptinRequest(
    [Required]
    string WabaId,
    bool? IsOptInEnable,
    string TemplateName,
    string TemplateLanguage);

public record DeleteOptinRequest(
    [Required]
    string WabaId);

public record ReconnectWabaPhoneNumberRequest(
    [Required]
    string WabaId,
    [Required]
    string WabaPhoneNumberId);

public record DisconnectWabaPhoneNumberRequest(
    [Required]
    string WabaId,
    [Required]
    string WabaPhoneNumberId);

public record MessagingHubErrorResponse(
    bool Success,
    int ErrorCode,
    string ErrorMessage,
    Dictionary<string, object> ErrorContext);

public record ConnectWabaResponse(
    List<WhatsappCloudApiMessagingHubWabaDto> ConnectedWaba,
    List<string> ForbiddenWabaNames);

public record ConnectWabaPhoneNumberResponse(WhatsappCloudApiConfigViewModel ConnectedWhatsappCloudApiConfig);

public record GetConnectedWabaPhoneNumberResponse(
    List<WhatsappCloudApiConfigViewModel> WhatsappCloudApiConfigs,
    List<WhatsappCloudApiMessagingHubWabaDto> UnconnectedWabaPhoneNumberChannels);

public record GetTemplatesResponse(List<WhatsappCloudApiTemplateResponse> WhatsappTemplates);

public class WhatsappCloudApiTemplateResponse : WhatsappCloudApiTemplate
{
    [JsonProperty("is_template_bookmarked")]
    public bool IsTemplateBookmarked { get; set; }

    [JsonConstructor]
    public WhatsappCloudApiTemplateResponse(
        string id,
        string name,
        List<WhatsappCloudApiTemplateComponentObject> components,
        string category,
        string status,
        string language,
        string rejectedReason,
        bool isTemplateBookmarked)
    {
        Id = id;
        Name = name;
        Components = components;
        Category = category;
        Status = status;
        Language = language;
        RejectedReason = rejectedReason;
        IsTemplateBookmarked = isTemplateBookmarked;
    }
}

public record GenerateTemplateMessagePayloadRequest([ValidateNever] List<WhatsappCloudApiTemplateComponentObject> Components);

public record GenerateTemplateMessagePayloadResponse(
    TemplateMessagePayload TemplateMessagePayload);

public class TemplateMessagePayload
{
    [JsonProperty("components")]
    public List<WhatsappCloudApiTemplateMessageComponentObject> Components { get; set; }

    [JsonConstructor]
    public TemplateMessagePayload(List<WhatsappCloudApiTemplateMessageComponentObject> components)
    {
        Components = components;
    }
}

public record CreateTemplateRequest(
    [Required]
    string WabaId,
    [ValidateNever]
    WhatsappCloudApiCreateTemplateObject CreateTemplateObject);

public record UpdateTemplateRequest(
    [Required]
    string WabaId,
    [Required]
    string TemplateId,
    [ValidateNever]
    List<WhatsappCloudApiTemplateComponentObject> TemplateComponents);

public record DeleteTemplateRequest(
    [Required]
    string WabaId,
    [Required]
    string TemplateName);

public record RequestUploadTemplateFileUrlRequest(
    [Required]
    string WabaId);

public record AddTemplateBookmarkRequest(
    [Required]
    string WabaId,
    [Required]
    string TemplateId,
    [Required]
    string TemplateName,
    [Required]
    string TemplateLanguage);

public record DeleteTemplateBookmarkRequest(
    [Required]
    string WabaId,
    [Required]
    string TemplateId);

public record CreateTemplateResponse(string TemplateId);

public record RequestUploadTemplateFileUrlResponse(string BlobId, string UploadUrl);

public record RetrieveTemplateFileHeaderHandleResponse(string HeaderHandle, string ReadUrl);

# region manual top-up

public record GenerateBusinessBalanceStripeTopUpLinkRequest(
    [Required]
    string TopUpPlanId,
    [Required]
    string FacebookBusinessId,
    string? RedirectToUrl,
    string? WabaId);

public record GetConversationUsageByFacebookWabaIdRequest(
    [Required, JsonProperty("facebook_business_id")]
    string FacebookBusinessId,
    [Required, JsonProperty("facebook_waba_id")]
    string FacebookWabaId,
    [Required, JsonProperty("start")]
    DateTime Start,
    [Required, JsonProperty("end")]
    DateTime End);

# endregion

# region auto top-up

public record CreateBusinessBalanceAutoTopUpProfileRequest(
    [Required]
    [JsonProperty("auto_top_up_profile")]
    BusinessBalanceAutoTopUpProfileDto AutoTopUpProfile,
    [JsonProperty("redirect_to_url")]
    string RedirectTotUrl,
    [JsonProperty("phone_number")]
    string PhoneNumber);

public record UpdateBusinessBalanceAutoTopUpProfileRequest(
    [Required]
    [JsonProperty("auto_top_up_profile")]
    BusinessBalanceAutoTopUpProfileDto AutoTopUpProfile,
    [JsonProperty("redirect_to_url")]
    string RedirectToUrl,
    [JsonProperty("phone_number")]
    string PhoneNumber);

# endregion

public record GetBusinessBalanceStripeTopUpInvoiceRequest(
    DateTime? Start = null,
    DateTime? End = null,
    string ContinuationToken = null,
    int Limit = 10000);

public record GetBusinessBalanceStripeTopUpInvoiceResponse(
    List<BusinessBalanceInvoiceDto> BusinessBalanceInvoices,
    string NextContinuationToken = null,
    int Count = 0);

public record WhatsappCloudApiByWabaConfigResponse(
    List<WhatsappCloudApiByWabaConfigViewModel> WhatsappCloudApiByWabaIdConfigs);

public class WhatsappCloudApiByWabaConfigViewModel
{
    public string WabaAccountId { get; set; }

    public string MessagingHubWabaId { get; set; }

    public string WabaName { get; set; }

    public string TemplateNamespace { get; set; }

    public List<WhatsappCloudApiConfigViewModel> WhatsappCloudApiConfigs { get; set; }

    public string FacebookWabaBusinessId { get; set; }

    public string FacebookWabaId { get; set; }

    public string FacebookPhoneNumberId { get; set; }
}
public record GetUserBusinessesRequest(
    [Required]
    string FacebookAppUserToken);

public record GetBusinessWabasRequest(
    [Required]
    string FacebookAppUserToken,
    [Required]
    string FacebookBusinessId);

public record GetBusinessWabaPhoneNumbersRequest(
    [Required]
    string FacebookAppUserToken,
    [Required]
    string FacebookWabaId);

public record InitiatePhoneNumberWabaMigrationRequest(
    [Required]
    string FacebookWabaId,
    [Required]
    string FacebookPhoneNumber);

public record RequestPhoneNumberVerificationCodeRequest(
    [Required]
    string FacebookWabaId,
    [Required]
    string FacebookPhoneNumber,
    [Required]
    string DestinationPhoneNumberId,
    [Required]
    string CodeMethod,
    [Required]
    string Language = "en_US");

public record PhoneNumberMigrationVerifyingCodeRequest(
    [Required]
    string FacebookWabaId,
    [Required]
    string FacebookPhoneNumber,
    [Required]
    string DestinationPhoneNumberId,
    [Required]
    string Code);
public record ConnectFacebookWabasProductCatalogsRequest([Required] List<ConnectWabaProductCatalog> ConnectWabaProductCatalogs);
public class ConnectWabaProductCatalog
{
    [JsonProperty("waba_id")]
    public string WabaId { get; set; }

    [JsonProperty("facebook_product_catalog_id")]
    public string FacebookProductCatalogId { get; set; }
}
public class ConnectWabaProductCatalogResponse
{
    public List<WhatsappCloudApiConfigViewModel> ConnectedWabasProductCatalogs { get; set; } = new List<WhatsappCloudApiConfigViewModel>();

    public List<WhatsappCloudApiConfigViewModel> ForbiddenWabasProductCatalogs { get; set; } = new List<WhatsappCloudApiConfigViewModel>();
}
public record UpdateWabaConnectedFacebookProductCatalogRequest(
    [Required]
    string WabaId,
    [Required]
    string FacebookProductCatalogId);
public record DisconnectWabaConnectedFacebookProductCatalogRequest(
    [Required] string WabaId,
    [Required] string FacebookProductCatalogId);
public record GetWabaProductCatalogPhoneNumbersCommerceSettingsResponse(
    WabaDto Waba);
public record UpdateWabaProductCatalogPhoneNumbersCommerceSettingsRequest(
    [Required]
    string WabaId,
    [Required]
    string WabaPhoneNumberId,
    [Required]
    bool IsCatalogVisible = true,
    [Required]
    bool IsCartEnabled = true);
public record UpdatePhoneNumberAutoSendStripePaymentLinkSettingRequest(
    [Required]
    string WabaId,
    [Required]
    string WabaPhoneNumberId,
    [Required] bool IsActive = true);
public record UpdatePhoneNumberAutoSendStripePaymentLinkSettingResponse(
    bool HasEnabledAutoSendStripePaymentLink);
public record GetProductCatalogProductItemsResponse(
    [Required]
    List<ProductItemDto> ProductItems);
public record GetProductCatalogProductItemResponse(
    [Required]
    ProductItemDto ProductItem);

public record GetConversationalAutomationListDto(
    [Required]
    string FacebookWabaId);

public class GetConversationalAutomationListResponse : ConversationalAutomationListResponse
{
    [Required]
    [JsonProperty("channel_name")]
    public string ChannelName { get; set; }

    [Required]
    [JsonProperty("facebook_phone_number_status")]
    public string FacebookPhoneNumberStatus { get; set; }

    public GetConversationalAutomationListResponse(
        string channelName,
        string facebookPhoneNumberStatus,
        ConversationalAutomationListResponse data)
        : base(
            data.Id,
            data.VerifiedName,
            data.DisplayPhoneNumber,
            data.ConversationalAutomation)
    {
        ChannelName = channelName;
        FacebookPhoneNumberStatus = facebookPhoneNumberStatus;
    }
}

public record UpdateConversationalAutomationDto(
    [Required]
    string FacebookWabaId,
    [Required]
    string FacebookPhoneNumberId,
    [Required]
    ConversationalAutomation ConversationalAutomation);

public record DateRange(
    [JsonProperty("start")]
    DateTime Start,
    [JsonProperty("end")]
    DateTime End);

#nullable enable
public record GetBusinessWabaBalanceTransferTransactionsInput(
    [Required]
    [JsonProperty("facebook_business_id")]
    string FacebookBusinessId,
    [JsonProperty("facebook_waba_id")]
    string? FacebookWabaId,
    [JsonProperty("created_at_range")]
    DateRange? CreatedAtRange,
    [JsonProperty("updated_at_range")]
    DateRange? UpdatedAtRange,
    [JsonProperty("limit")]
    int? Limit,
    [JsonProperty("order_by")]
    int? OrderBy,
    [JsonProperty("order")]
    int? Order,
    [JsonProperty("continuation_token")]
    string? ContinuationToken);

public record AllocateBusinessWabaLevelCreditDto(
    [Required]
    [JsonProperty("facebook_business_id")]
    string FacebookBusinessId,
    [Required]
    [JsonProperty("e_tag")]
    string ETag,
    [Required]
    [ValidateNever]
    [JsonProperty("credit_allocation")]
    CreditAllocationObject CreditAllocation);

public record UpsertWabaBalanceAutoTopUpProfileRequest(
    [Required]
    [JsonProperty("auto_top_up_profile")]
    WabaBalanceAutoTopUpProfileDto AutoTopUpProfile,
    [JsonProperty("redirect_to_url")]
    string RedirectToUrl,
    [JsonProperty("phone_number")]
    string? PhoneNumber);

public record GetWabaBalanceAutoTopUpProfileRequest(
    [ValidateNever]
    GetWabaBalanceAutoTopUpProfileInput Input);

public record GetBusinessBalanceTransactionLog(
    [Required]
    string FacebookBusinessId,
    [Required]
    string FacebookWabaId,
    int Limit = 10000);

public class GetWhatsappFlowsByWabaResponse
{
    [Required]
    [JsonProperty("waba_id")]
    public string WabaId { get; set; }

    [Required]
    [JsonProperty("facebook_waba_name")]
    public string FacebookWabaName { get; set; }

    [Required]
    [JsonProperty("whatsapp_flows")]
    public GetFlowsResponse WhatsappFlows { get; set; }

    [JsonConstructor]
    public GetWhatsappFlowsByWabaResponse(
        string wabaId,
        string facebookWabaName,
        GetFlowsResponse whatsappFlows)
    {
        WabaId = wabaId;
        FacebookWabaName = facebookWabaName;
        WhatsappFlows = whatsappFlows;
    }
}
