name: sleekflow-core-preview

on:
  pull_request:
    branches:
      - dev
      - staging
      - master
env:
  NUGET_PACKAGES: ${{ github.workspace }}/.nuget/packages

jobs:
  update:
    name: Update
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3

      - name: Free Disk Space (Ubuntu)
        uses: jlumbroso/free-disk-space@main
        with:
          # this might remove tools that are actually needed,
          # if set to "true" but frees about 6 GB
          tool-cache: false

          # all of these default to true, but feel free to set to
          # "false" if necessary for your workflow
          android: true
          dotnet: true
          haskell: true
          large-packages: true
          docker-images: false
          swap-storage: false

      - name: Set up .NET Core
        uses: actions/setup-dotnet@v3
        with:
          dotnet-version: '8.0.303'
          include-prerelease: false

      - name: Install Azure Cli
        run: |
          curl -sL https://aka.ms/InstallAzureCLIDeb | sudo bash

      - uses: actions/cache@v3
        with:
          path: ${{ github.workspace }}/.nuget/packages
          key: ${{ runner.os }}-nuget-${{ hashFiles('**/*.csproj') }}
          restore-keys: |
            ${{ runner.os }}-nuget-

      - name: Restore with dotnet
        run: dotnet restore

      - name: Build with dotnet
        run: |
          dotnet publish Travis_backend.Auth0 -c Release --self-contained /p:ExcludeBuildDbMigration=TRUE
          dotnet publish Sleekflow.SleekPay -c Release

      - name: Build images locally
        run: |
          docker compose -f docker-compose.common.yml build
          docker compose -f docker-compose.yml build

      - name: Preview Everything
        uses: pulumi/actions@v4
        if: github.ref == 'refs/heads/dev' || github.event.pull_request.base.ref == 'dev'
        with:
          command: preview
          stack-name: dev
          work-dir: ./Sleekflow.Core.Infra/
        env:
          PULUMI_ACCESS_TOKEN: ${{ secrets.PULUMI_ACCESS_TOKEN }}
          ARM_CLIENT_ID: ${{ secrets.AZURE_SP_ARM_CLIENT_ID }}
          ARM_CLIENT_SECRET: ${{ secrets.AZURE_SP_ARM_CLIENT_SECRET }}
          ARM_SUBSCRIPTION_ID: ${{ secrets.AZURE_SP_ARM_SUBSCRIPTION_ID }}
          ARM_TENANT_ID: ${{ secrets.AZURE_SP_ARM_TENANT_ID }}

      - name: Preview Everything
        uses: pulumi/actions@v4
        if: github.ref == 'refs/heads/staging' || github.event.pull_request.base.ref == 'staging'
        with:
          command: preview
          stack-name: staging
          work-dir: ./Sleekflow.Core.Infra/
        env:
          PULUMI_ACCESS_TOKEN: ${{ secrets.PULUMI_ACCESS_TOKEN }}
          ARM_CLIENT_ID: ${{ secrets.AZURE_SP_ARM_CLIENT_ID }}
          ARM_CLIENT_SECRET: ${{ secrets.AZURE_SP_ARM_CLIENT_SECRET }}
          ARM_SUBSCRIPTION_ID: ${{ secrets.AZURE_SP_ARM_SUBSCRIPTION_ID }}
          ARM_TENANT_ID: ${{ secrets.AZURE_SP_ARM_TENANT_ID }}

      - name: Preview Everything
        uses: pulumi/actions@v4
        if: github.ref == 'refs/heads/master' || github.event.pull_request.base.ref == 'master'
        with:
          command: preview
          stack-name: production
          work-dir: ./Sleekflow.Core.Infra/
        env:
          PULUMI_ACCESS_TOKEN: ${{ secrets.PULUMI_ACCESS_TOKEN }}
          ARM_CLIENT_ID: ${{ secrets.AZURE_SP_ARM_CLIENT_ID }}
          ARM_CLIENT_SECRET: ${{ secrets.AZURE_SP_ARM_CLIENT_SECRET }}
          ARM_SUBSCRIPTION_ID: ${{ secrets.AZURE_SP_ARM_SUBSCRIPTION_ID }}
          ARM_TENANT_ID: ${{ secrets.AZURE_SP_ARM_TENANT_ID }}

      - name: Running Test
        run: dotnet test Sleekflow.Core.Tests/Sleekflow.Core.Tests.csproj --collect:"XPlat Code Coverage" --results-directory:coverage
        continue-on-error: true

      - name: Generate Report
        run: |
          dotnet tool install -g dotnet-reportgenerator-globaltool
          reportgenerator -reports:coverage/**/coverage.cobertura.xml -targetdir:coverage -reporttypes:Cobertura

      - name: Generate Coverage Report
        uses: clearlyip/code-coverage-report-action@v5
        id: code_coverage_report_action
        with:
          filename: "./coverage/Cobertura.xml"
          artifact_download_workflow_names: 'master-coverage-snapshot'
          only_list_changed_files: true

      - name: Add Coverage PR Comment
        uses: marocchino/sticky-pull-request-comment@v2
        if: github.event_name == 'pull_request'
        with:
          recreate: true
          path: code-coverage-results.md
