using BenchmarkDotNet.Attributes;
using BenchmarkDotNet.Engines;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Travis_backend.ConversationDomain.Models;
using Travis_backend.Database;

namespace Sleekflow.Core.Benchmarks;

[SimpleJob(RunStrategy.Monitoring, launchCount: 1, warmupCount: 1, iterationCount: 5, invocationCount: 1)]
public class ConversationHashtagsBenchmark
{
    private bool _firstCall = true;
    private readonly ApplicationDbContext _applicationDbContext;
    private readonly ILogger<ConversationHashtagsBenchmark> _logger;

    public ConversationHashtagsBenchmark()
    {
        var loggerFactory = LoggerFactory.Create(builder => { builder.AddConsole(); });
        _logger = loggerFactory.CreateLogger<ConversationHashtagsBenchmark>();

        _applicationDbContext = new ApplicationDbContext(
            new DbContextOptionsBuilder<ApplicationDbContext>()
                .UseSqlServer(new SqlConnection(DbConfig.ConnStr))
                .Options);
    }

    [Benchmark(Baseline = true)]
    public async Task<List<ConversationHashtag>> HashtagId()
    {
        var queryable =
            _applicationDbContext.ConversationHashtags
                .Where(ch => ch.HashtagId == "");

        if (_firstCall)
        {
            var queryString = queryable.ToQueryString();

            _logger.LogInformation(nameof(HashtagId) + " " + queryString);

            _firstCall = false;
        }

        return await queryable.ToListAsync();
    }

    [Benchmark]
    public async Task<List<ConversationHashtag>> CompanyId_HashtagId()
    {
        var queryable =
            _applicationDbContext.ConversationHashtags
                .Where(ch => ch.CompanyId == "" && ch.HashtagId == "");

        if (_firstCall)
        {
            var queryString = queryable.ToQueryString();

            _logger.LogInformation(nameof(CompanyId_HashtagId) + " " + queryString);

            _firstCall = false;
        }

        return await queryable.ToListAsync();
    }
}