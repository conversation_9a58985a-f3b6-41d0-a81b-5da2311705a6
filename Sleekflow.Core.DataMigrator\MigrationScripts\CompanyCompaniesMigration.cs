using System.Text;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using Sleekflow.Core.DataMigrator.Migrations;
using Sleekflow.Core.DataMigrator.Serializations;
using Travis_backend.AccountAuthenticationDomain.Models;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.Extensions;

namespace Sleekflow.Core.DataMigrator.MigrationScripts;

public class CompanyCompaniesMigration : BaseMigration
{
    public CompanyCompaniesMigration(Configurations configurations)
        : base(configurations)
    {
    }

    public override async Task<int> ExecuteAsync(string companyId)
    {
        var existedCompanies = await MigrationContext.CompanyCompanies.Where(c => c.Id == companyId).Select(x => x.Id)
            .ToListAsync();
        if (existedCompanies.Contains(companyId))
        {
            Console.WriteLine("Company Already Exist");
            return 0;
        }

        var originItems = await OriginalContext.CompanyCompanies.Where(c => c.Id == companyId).ToListAsync();
        var results = new List<Company>();
        if (FilterDuplicate)
        {
            var migratedItems = MigrationContext.CompanyCompanies.Select(x => x.Id)
                .ToHashSet();
            foreach (var originItem in originItems)
            {
                if (!migratedItems.Contains(originItem.Id))
                {
                    results.Add(originItem);
                }
            }
        }

        var companies = new List<string?>();

        var settings = new JsonSerializerSettings();
        settings.Converters.Add(new DateTimeOffsetConverter());

        var aspNetUserMigratedItems = MigrationContext.Users.Select(x => x.Id)
            .ToHashSet();


        foreach (var company in results)
        {
            var cmsActivationOwnerId = company.CmsActivationOwnerId;
            var cmsCompanyOwnerId = company.CmsCompanyOwnerId;

            ApplicationUser? aspNetUserActivationOwner = null;
            if (!cmsActivationOwnerId.IsNullOrEmpty())
            {
                aspNetUserActivationOwner =
                    await OriginalContext.Users.Where(u => u.Id == cmsActivationOwnerId).FirstAsync();
                if (!aspNetUserMigratedItems.Contains(aspNetUserActivationOwner.Id))
                {
                    await MigrationContext.Users.AddAsync(aspNetUserActivationOwner);
                }
            }

            if (!cmsCompanyOwnerId.IsNullOrEmpty())
            {
                var aspNetUserCompanyUser =
                    await OriginalContext.Users.Where(u => u.Id == cmsCompanyOwnerId).FirstAsync();
                if (aspNetUserCompanyUser != aspNetUserActivationOwner &&
                    !aspNetUserMigratedItems.Contains(aspNetUserCompanyUser.Id))
                {
                    await MigrationContext.Users.AddAsync(aspNetUserCompanyUser);
                }
            }

            var serializedCompanySb = new StringBuilder();
            try
            {
                serializedCompanySb.Append(JsonConvert.SerializeObject(company, settings));
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
            }

            companies.Add(serializedCompanySb.ToString());

            await MigrationContext.CompanyCompanies.AddAsync(company);
        }

        var filePath = SerializeEntityToJson.GetPath("CompanyCompanies", "SerializedDocuments");
        await SerializeEntityToJson.SerializeToJson(companies, filePath);

        var entityEntries = MigrationContext.ChangeTracker.Entries().ToList();
        var type = typeof(Company);
        foreach (var entityEntry in entityEntries.Where(
                     entityEntry => entityEntry.Entity.GetType() != type))
        {
            entityEntry.State = EntityState.Unchanged;
        }

        return await MigrationContext.SaveChangesAsync();
    }
}