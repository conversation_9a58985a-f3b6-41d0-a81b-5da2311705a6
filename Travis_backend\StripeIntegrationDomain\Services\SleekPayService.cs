using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Threading.Tasks;
using AutoMapper;
using AutoMapper.QueryableExtensions;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.OpenApi.Extensions;
using Newtonsoft.Json;
using Stripe.Reporting;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.Constants;
using Travis_backend.ConversationServices;
using Travis_backend.Database;
using Travis_backend.Database.Services;
using Travis_backend.Enums;
using Travis_backend.Extensions;
using Travis_backend.FileDomain.Services;
using Travis_backend.MessageDomain.ViewModels;
using Travis_backend.StripeIntegrationDomain.Clients;
using Travis_backend.StripeIntegrationDomain.Models;
using Travis_backend.StripeIntegrationDomain.ViewModels;
using DateTime = System.DateTime;

namespace Travis_backend.StripeIntegrationDomain.Services;

public interface ISleekPayService
{
    Task Wakeup();

    Task<ConnectStripeResponse> ConnectSleekPay(string companyId, long staffId, string platformCountry);

    Task<ConnectStripeResponse> DashboardLogin(string companyId, long staffId, string platformCountry);

    Task<ConnectStripeResponse> GenerateSleekPayLink(
        string companyId,
        long? sharedStaffId,
        GenerateStripPaymentRequest generateStripPaymentRequest);

    Task<RefundPaymentResponse> RefundPayment(
        string companyId,
        long? refundStaff,
        RefundPaymentRequest refundPaymentRequest);

    Task<Stripe.Balance> GetStripeBalance(string companyId, long staffId, string platformCountry);

    ValueTask AddSleekPayRecord(List<ConversationMessageResponseViewModel> responseVm);

    Task AddSleekPayRecord(ConversationMessageResponseViewModel responseVm);

    Task<StripePaymentOrderPageViewModels> GetStripePaymentOrderPageRecords(
        Staff currentUser,
        string platformCountry,
        int offset,
        int limit,
        string sortBy,
        string order,
        List<StripePaymentStatus> statuses,
        long? staffId,
        DateTime? startDate,
        DateTime? endDate,
        bool isSystem = false);

    Task<StripePaymentCustomerPaymentHistoryViewModel> GetStripePaymentCustomerPaymentHistoryRecords(
        Staff currentUser,
        string userprofileId,
        int offset,
        int limit,
        StripePaymentStatus? status);

    Task<StripePaymentConfig> UpdateStripePaymentLinkSettings(
        string companyId,
        string accountId,
        PaymentLinkExpirationOption paymentLinkExpirationOption,
        List<string> shippingAllowedCountries,
        List<ShippingOption> shippingOptions,
        bool isShippingEnabled,
        string supportEmail,
        string supportPhoneNumber,
        string statementDescriptor,
        string brandColor,
        string buttonsColor,
        IFormFile companyLogo);

    Task<decimal> GetStripeVolume(string companyId, string platformCountry, DateTime volumeFrom, DateTime volumeTo);

    Task<string> GetStripeCompanyLogoUrl(string companyId, string platformCountry);

    Task<StripePaymentResultResponse> GetPaymentResult(string resultId);

    Task<List<StripeSupportedCurrencyMapping>> GetStripeSupportedCurrencies(string companyId);

    Task<List<string>> GetStripeConnectedPlatformCountries(string companyId);

    Task<ReportRun> CreateStripePaymentActivityReport(
        string companyId,
        string receiverEmailAddress,
        DateTime startDateTime,
        DateTime endDateTime,
        string platformCountry);
}

public class SleekPayService : ISleekPayService
{
    private readonly IConfiguration _configuration;
    private readonly ApplicationDbContext _appDbContext;
    private readonly IMapper _mapper;
    private readonly IUploadService _uploadService;
    private readonly IAzureBlobStorageService _azureBlobStorageService;
    private readonly IStripeClients _stripeClients;
    private readonly ILogger<SleekPayService> _logger;
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly IDbContextService _dbContextService;

    public SleekPayService(
        IConfiguration configuration,
        ApplicationDbContext appDbContext,
        IMapper mapper,
        IUploadService uploadService,
        IAzureBlobStorageService azureBlobStorageService,
        IStripeClients stripeClients,
        ILogger<SleekPayService> logger,
        IHttpClientFactory httpClientFactory,
        IDbContextService dbContextService)
    {
        _configuration = configuration;
        _appDbContext = appDbContext;
        _mapper = mapper;
        _uploadService = uploadService;
        _azureBlobStorageService = azureBlobStorageService;
        _stripeClients = stripeClients;
        _logger = logger;
        _httpClientFactory = httpClientFactory;
        _dbContextService = dbContextService;
    }

    public async Task<RefundPaymentResponse> RefundPayment(
        string companyId,
        long? refundStaff,
        RefundPaymentRequest refundPaymentRequest)
    {
        refundPaymentRequest.CompanyId = companyId;

        var shareLinkAzureFunction = _configuration.GetValue<string>("Values:SleekPayFunction");

        var httpClient = _httpClientFactory.CreateClient(HttpClientHandlerName.Default);

        var apiResponse = await httpClient.PostAsJsonAsync(
            $"{shareLinkAzureFunction}/refund?companyId={companyId}&staffId={refundStaff}",
            refundPaymentRequest);

        if (!apiResponse.IsSuccessStatusCode)
        {
            throw new Exception(await apiResponse.Content.ReadAsStringAsync());
        }

        var createShareLinkTracking =
            JsonConvert.DeserializeObject<RefundPaymentResponse>(await apiResponse.Content.ReadAsStringAsync());

        return createShareLinkTracking;
    }

    public async Task<Stripe.Balance> GetStripeBalance(string companyId, long staffId, string platformCountry)
    {
        var shareLinkAzureFunction = _configuration.GetValue<string>("Values:SleekPayFunction");

        var httpClient = _httpClientFactory.CreateClient(HttpClientHandlerName.Default);

        var apiResponse = await httpClient.GetAsync(
            $"{shareLinkAzureFunction}/balance?companyId={companyId}&staffId={staffId}&platformCountry={platformCountry}");

        if (!apiResponse.IsSuccessStatusCode)
        {
            throw new Exception(await apiResponse.Content.ReadAsStringAsync());
        }

        var createShareLinkTracking =
            JsonConvert.DeserializeObject<Stripe.Balance>(await apiResponse.Content.ReadAsStringAsync());

        return createShareLinkTracking;
    }

    public async Task<ConnectStripeResponse> DashboardLogin(string companyId, long staffId, string platformCountry)
    {
        var shareLinkAzureFunction = _configuration.GetValue<string>("Values:SleekPayFunction");

        var httpClient = _httpClientFactory.CreateClient(HttpClientHandlerName.Default);

        var apiResponse = await httpClient.GetAsync(
            $"{shareLinkAzureFunction}/dashboard?companyId={companyId}&staffId={staffId}&platformCountry={platformCountry}");

        if (!apiResponse.IsSuccessStatusCode)
        {
            throw new Exception(await apiResponse.Content.ReadAsStringAsync());
        }

        var createShareLinkTracking =
            JsonConvert.DeserializeObject<ConnectStripeResponse>(await apiResponse.Content.ReadAsStringAsync());

        return createShareLinkTracking;
    }

    public async Task Wakeup()
    {
        var shareLinkAzureFunction = _configuration.GetValue<string>("Values:SleekPayFunction");

        var httpClient = _httpClientFactory.CreateClient(HttpClientHandlerName.Default);

        var apiResponse = await httpClient.GetAsync($"{shareLinkAzureFunction}");

        if (!apiResponse.IsSuccessStatusCode)
        {
            throw new Exception(await apiResponse.Content.ReadAsStringAsync());
        }
    }

    public async Task<ConnectStripeResponse> ConnectSleekPay(string companyId, long staffId, string platformCountry)
    {
        var shareLinkAzureFunction = _configuration.GetValue<string>("Values:SleekPayFunction");

        var httpClient = _httpClientFactory.CreateClient(HttpClientHandlerName.Default);

        var apiResponse = await httpClient.GetAsync(
            $"{shareLinkAzureFunction}/stripeConnect?companyId={companyId}&staffId={staffId}&platformCountry={platformCountry}");

        if (!apiResponse.IsSuccessStatusCode)
        {
            throw new Exception(await apiResponse.Content.ReadAsStringAsync());
        }

        var createShareLinkTracking =
            JsonConvert.DeserializeObject<ConnectStripeResponse>(await apiResponse.Content.ReadAsStringAsync());

        return createShareLinkTracking;
    }

    public async Task<ConnectStripeResponse> GenerateSleekPayLink(
        string companyId,
        long? sharedStaffId,
        GenerateStripPaymentRequest generateStripPaymentRequest)
    {
        generateStripPaymentRequest.CompanyId = companyId;
        generateStripPaymentRequest.SharedStaffId = sharedStaffId;

        if (!string.IsNullOrEmpty(generateStripPaymentRequest.UserProfileId))
        {
            var userProfile = await _appDbContext.UserProfiles.FirstOrDefaultAsync(
                x => x.CompanyId == companyId && x.Id == generateStripPaymentRequest.UserProfileId);

            if (userProfile == null)
            {
                throw new Exception($"Unable to find this user profile.");
            }

            generateStripPaymentRequest.ShopifyCustomerId = userProfile.ShopifyCustomerId;
            generateStripPaymentRequest.ShopifyCustomerEmail = userProfile.Email;
            generateStripPaymentRequest.ShopifyCustomerFirstname = userProfile.FirstName;
            generateStripPaymentRequest.ShopifyCustomerLastname = userProfile.LastName;
            generateStripPaymentRequest.ShopifyCustomerPhone = userProfile.PhoneNumber;

            if (string.IsNullOrEmpty(generateStripPaymentRequest.CustomerEmail) &&
                !string.IsNullOrEmpty(userProfile.Email))
            {
                generateStripPaymentRequest.CustomerEmail = userProfile.Email;
            }

            var teamId = await _appDbContext.Conversations
                .Where(x => x.CompanyId == companyId && x.UserProfileId == generateStripPaymentRequest.UserProfileId)
                .Select(x => x.AssignedTeamId).FirstOrDefaultAsync();
            generateStripPaymentRequest.SharedTeamId = teamId;

            if (string.IsNullOrEmpty(generateStripPaymentRequest.CustomerId))
            {
                switch (generateStripPaymentRequest.PlatformCountry)
                {
                    case "HK":
                        if (!string.IsNullOrEmpty(userProfile.StripeCustomerId))
                        {
                            generateStripPaymentRequest.CustomerId = userProfile.StripeCustomerId;
                        }

                        break;
                    case "SG":
                        if (!string.IsNullOrEmpty(userProfile.StripeCustomerSGId))
                        {
                            generateStripPaymentRequest.CustomerId = userProfile.StripeCustomerSGId;
                        }

                        break;
                    case "MY":
                        if (!string.IsNullOrEmpty(userProfile.StripeCustomerMYId))
                        {
                            generateStripPaymentRequest.CustomerId = userProfile.StripeCustomerMYId;
                        }

                        break;
                    case "GB":
                        if (!string.IsNullOrEmpty(userProfile.StripeCustomerGBId))
                        {
                            generateStripPaymentRequest.CustomerId = userProfile.StripeCustomerGBId;
                        }

                        break;
                }
            }
        }

        if (sharedStaffId.HasValue)
        {
            var staff = await _appDbContext.UserRoleStaffs
                .Where(x => x.Id == sharedStaffId.Value)
                .Select(
                    x => new
                    {
                        Name = $"{x.Identity.FirstName} {x.Identity.LastName}",
                        Username = $"{x.Identity.UserName}",
                        Email = $"{x.Identity.Email}",
                        StaffId = x.Identity.UserName
                    })
                .FirstOrDefaultAsync();
            generateStripPaymentRequest.SharedStaffEmail = staff?.Email;
            generateStripPaymentRequest.SharedStaffName = staff?.Name;
            generateStripPaymentRequest.SharedStaffUsername = staff?.Username;
            generateStripPaymentRequest.SharedStaffCompanyId = staff?.StaffId;
        }

        // Hardcode for e53b13a4-b371-47a6-a4d7-ba10caa296fe
        if (companyId is "e53b13a4-b371-47a6-a4d7-ba10caa296fe" or "ef710422-690b-430a-9d3a-369589c46567")
        {
            generateStripPaymentRequest.IsReserveInventory = false;
        }

        if (generateStripPaymentRequest.LineItems != null && generateStripPaymentRequest.LineItems.Any())
        {
            foreach (var lineItem in generateStripPaymentRequest.LineItems)
            {
                if (lineItem.ImageFiles != null && lineItem.ImageFiles.Any())
                {
                    var imageUrls = await UploadLineItemImageFilesAsync(companyId, lineItem.ImageFiles);

                    if (imageUrls.Any())
                    {
                        lineItem.ImageUrls = imageUrls;
                    }
                }
            }
        }

        var response = await GenerateSleekPayLink(generateStripPaymentRequest);

        return response;
    }

    private async Task<List<string>> UploadLineItemImageFilesAsync(
        string companyId,
        List<StripePaymentLineItemImageFile> imageFiles)
    {
        var imageUrls = new List<string>();

        foreach (var imageFile in imageFiles)
        {
            try
            {
                using var memoryStream = new MemoryStream(imageFile.File);

                var imageFormFile = new FormFile(
                    memoryStream,
                    0,
                    imageFile.File.Length,
                    imageFile.Filename,
                    imageFile.Filename)
                {
                    Headers = new HeaderDictionary(),
                    ContentType = imageFile.Filename.Contains("jpg") || imageFile.Filename.Contains("jpeg")
                        ? "image/jpeg"
                        : "image/png"
                };

                await _uploadService.UploadImage(companyId, imageFormFile.FileName, imageFormFile);

                var azureBlobSasUri =
                    _azureBlobStorageService.GetAzureBlobSasUriForever(imageFormFile.FileName, companyId);

                if (!string.IsNullOrEmpty(azureBlobSasUri))
                {
                    imageUrls.Add(azureBlobSasUri);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[SleekPay {MethodName}] Upload {CompanyId} Stripe payment request item image file {ImageFileName} error. {Exception}",
                    nameof(UploadLineItemImageFilesAsync),
                    companyId,
                    imageFile.Filename,
                    JsonConvert.SerializeObject(ex));
            }
        }

        return imageUrls;
    }

    private async Task<ConnectStripeResponse> GenerateSleekPayLink(
        GenerateStripPaymentRequest generateStripPaymentRequest)
    {
        var shareLinkAzureFunction = _configuration.GetValue<string>("Values:SleekPayFunction");

        var httpClient = _httpClientFactory.CreateClient(HttpClientHandlerName.Default);

        var apiResponse = await httpClient.PostAsJsonAsync(
            $"{shareLinkAzureFunction}/generateStripePaymentLink",
            generateStripPaymentRequest);

        if (!apiResponse.IsSuccessStatusCode)
        {
            throw new Exception(await apiResponse.Content.ReadAsStringAsync());
        }

        var createShareLinkTracking =
            JsonConvert.DeserializeObject<ConnectStripeResponse>(await apiResponse.Content.ReadAsStringAsync());

        return createShareLinkTracking;
    }

    public async Task<StripePaymentResultResponse> GetPaymentResult(string resultId)
    {
        var shareLinkAzureFunction = _configuration.GetValue<string>("Values:SleekPayFunction");

        var httpClient = _httpClientFactory.CreateClient(HttpClientHandlerName.Default);

        var apiResponse = await httpClient.GetAsync($"{shareLinkAzureFunction}/result/{resultId}");

        if (!apiResponse.IsSuccessStatusCode)
        {
            throw new Exception(await apiResponse.Content.ReadAsStringAsync());
        }

        var createShareLinkTracking =
            JsonConvert.DeserializeObject<StripePaymentResultResponse>(await apiResponse.Content.ReadAsStringAsync());

        return createShareLinkTracking;
    }

    public async ValueTask AddSleekPayRecord(List<ConversationMessageResponseViewModel> responseVm)
    {
        if (responseVm.Any(x => x.SleekPayRecordId.HasValue))
        {
            var dbContext = _dbContextService.GetDbContext();
            foreach (var message in responseVm.Where(x => x.SleekPayRecordId.HasValue))
            {
                message.SleekPayRecord = await dbContext.StripePaymentRecords
                    .ProjectTo<StripePaymentRecordResponse>(_mapper.ConfigurationProvider)
                    .FirstOrDefaultAsync(x => x.Id == message.SleekPayRecordId);
            }
        }
    }

    public async Task AddSleekPayRecord(ConversationMessageResponseViewModel responseVm)
    {
        if (responseVm.SleekPayRecordId.HasValue)
        {
            responseVm.SleekPayRecord = await _appDbContext.StripePaymentRecords
                .ProjectTo<StripePaymentRecordResponse>(_mapper.ConfigurationProvider)
                .FirstOrDefaultAsync(x => x.Id == responseVm.SleekPayRecordId);
        }
    }

    public async Task<StripePaymentOrderPageViewModels> GetStripePaymentOrderPageRecords(
        Staff currentUser,
        string platformCountry,
        int offset,
        int limit,
        string sortBy,
        string order,
        List<StripePaymentStatus> statuses,
        long? staffId = null,
        DateTime? startDate = null,
        DateTime? endDate = null,
        bool isSystem = false)
    {
        List<long> teamIdList;

        var stripePaymentOrderPageQuery = _appDbContext.StripePaymentRecords
            .AsNoTracking()
            .Where(
                x =>
                    x.CompanyId == currentUser.CompanyId
                    && x.PlatformCountry == platformCountry);

        if (currentUser.RoleType == StaffUserRole.TeamAdmin)
        {
            teamIdList = await _appDbContext.CompanyTeamMembers
                .Where(y => y.StaffId == currentUser.Id)
                .Select(y => y.CompanyTeamId)
                .ToListAsync();

            if (isSystem)
            {
                stripePaymentOrderPageQuery = stripePaymentOrderPageQuery.Where(x => x.StaffId == null);
            }
            else
            {
                if (teamIdList.IsNullOrEmpty())
                {
                    // If the team admin does not belong to any team. Get all the records belong to the team admin
                    stripePaymentOrderPageQuery = stripePaymentOrderPageQuery
                        .Where(x => x.StaffId == currentUser.Id);
                }
                else
                {
                    // If the stripe payment record has teamId and the team admin belong to that team
                    // Or
                    // If the stripe payment record belong to the team admin (e.g. teamId is null but belong to the team admin)
                    stripePaymentOrderPageQuery = stripePaymentOrderPageQuery
                        .Where(
                            x =>
                                (x.TeamId != null
                                 && teamIdList.Contains((long) x.TeamId))
                                || x.StaffId == currentUser.Id);
                }
            }
        }

        stripePaymentOrderPageQuery = stripePaymentOrderPageQuery
            .WhereIf(isSystem, x => x.StaffId == null)
            .WhereIf(staffId.HasValue && !isSystem, x => x.StaffId == staffId)
            .WhereIf(startDate.HasValue, x => x.CreatedAt >= startDate.Value)
            .WhereIf(endDate.HasValue, x => x.CreatedAt < endDate.Value.AddDays(1))
            .WhereIf(statuses.Count > 0, x => statuses.Contains(x.Status));

        var stripePaymentOrderPageRecordsTotalCount = await stripePaymentOrderPageQuery.CountAsync();

        if (stripePaymentOrderPageRecordsTotalCount == 0)
        {
            return new StripePaymentOrderPageViewModels
            {
                totalNumberOfRecords = stripePaymentOrderPageRecordsTotalCount
            };
        }

        if (order == "desc")
        {
            if (sortBy == "createdAt")
            {
                stripePaymentOrderPageQuery = stripePaymentOrderPageQuery
                    .OrderByDescending(x => x.CreatedAt);
            }
        }
        else
        {
            if (sortBy == "createdAt")
            {
                stripePaymentOrderPageQuery = stripePaymentOrderPageQuery
                    .OrderBy(x => x.CreatedAt);
            }
        }

        var listOfStripePaymentOrderPageRecords = await stripePaymentOrderPageQuery
            .Skip(offset)
            .Take(limit)
            .Select(
                x => new StripePaymentOrderPageRecord()
                {
                    SleekPayId = x.Id,
                    ShopifyId = x.ShopifyId,
                    PlatformCountry = x.PlatformCountry,
                    ShopifyOrderId = x.ShopifyOrderId != null ? x.ShopifyOrderId : x.ShopifyDraftOrderId,
                    PaymentId = x.StripePaymentIntentId,
                    UserProfileId = x.UserProfileId,

                    // The null-coalescing operator ?? returns the value of its left-hand operand if it isn't null
                    EshopPlatformOrderTrackingUrl = x.ShopifyOrderStatusUrl ?? x.ShopifyInvoiceUrl,
                    CustomerName = _appDbContext.UserProfiles
                        .Where(u => u.Id == x.UserProfileId && u.CompanyId == currentUser.CompanyId)
                        .Select(u => string.Join(" ", u.FirstName, u.LastName).Trim())
                        .FirstOrDefault(),
                    Status = x.Status.GetDisplayName(),
                    PaymentTrackingUrl = x.PaymentTrackingUrl,
                    CreatedAt = x.CreatedAt,
                    LineItems = x.LineItems,
                    PaidAt = x.PaidAt,
                    ExpiredAt = x.ExpiredAt,
                    RefundedAmount = x.RefundedAmount,
                    RefundId = x.RefundId,
                    RefundReason = x.RefundReason,
                    PayAmount = x.PayAmount,
                    Shipping = x.Shipping,
                    Billing = x.Billing,
                })
            .ToListAsync();

        foreach (var listOfStripePaymentOrderPageRecord in listOfStripePaymentOrderPageRecords)
        {
            listOfStripePaymentOrderPageRecord.PaymentAmount = listOfStripePaymentOrderPageRecord.LineItems
                .Sum(x => (x.Amount - x.TotalDiscount) * x.Quantity);

            listOfStripePaymentOrderPageRecord.ShippingFeeAmount =
                listOfStripePaymentOrderPageRecord.PayAmount == 0 ||
                listOfStripePaymentOrderPageRecord.LineItems == null
                    ? 0
                    : listOfStripePaymentOrderPageRecord.PayAmount -
                      listOfStripePaymentOrderPageRecord.LineItems.Sum(l => (l.Amount - l.TotalDiscount) * l.Quantity);

            listOfStripePaymentOrderPageRecord.PhoneNumber =
                (await _appDbContext.UserProfiles.FirstOrDefaultAsync(
                    u => u.Id == listOfStripePaymentOrderPageRecord.UserProfileId))?.PhoneNumber;

            var contactOwnerId =
                (await _appDbContext.UserProfiles.FirstOrDefaultAsync(
                    u =>
                        u.Id == listOfStripePaymentOrderPageRecord.UserProfileId &&
                        u.CompanyId == currentUser.CompanyId))?.ContactOwnerId;

            if (!string.IsNullOrEmpty(contactOwnerId))
            {
                var contactOwner = await _appDbContext.UserRoleStaffs
                    .Include(s => s.Identity)
                    .FirstOrDefaultAsync(
                        s =>
                            s.IdentityId == contactOwnerId
                            && s.CompanyId == currentUser.CompanyId);

                listOfStripePaymentOrderPageRecord.ContactOwner = contactOwner?.Identity?.DisplayName;
            }

            var stripePaymentOrderPageRecordQuery = _appDbContext.ConversationMessages
                .Where(
                    cm =>
                        cm.SleekPayRecordId == listOfStripePaymentOrderPageRecord.SleekPayId
                        && cm.CompanyId == currentUser.CompanyId);

            var isPaymentLinkAlreadySent = await stripePaymentOrderPageRecordQuery.AnyAsync();

            if (isPaymentLinkAlreadySent)
            {
                listOfStripePaymentOrderPageRecord.ConversationMessage = await stripePaymentOrderPageRecordQuery
                    .Select(
                        cm => new StripePaymentOrderPageConversationMessage
                        {
                            conversationId = cm.ConversationId, lastChannel = cm.Channel
                        })
                    .FirstOrDefaultAsync();
            }
            else
            {
                var userProfileId = listOfStripePaymentOrderPageRecord.UserProfileId;

                var stripePaymentOrderPageConversationMessageQuery = _appDbContext.Conversations
                    .Where(
                        c =>
                            c.UserProfileId == userProfileId
                            && c.CompanyId == currentUser.CompanyId);

                var isConversationExists = await stripePaymentOrderPageConversationMessageQuery.AnyAsync();

                if (isConversationExists)
                {
                    // If the payment link is not sent then only conversationId will be returned
                    listOfStripePaymentOrderPageRecord.ConversationMessage =
                        await stripePaymentOrderPageConversationMessageQuery
                            .Select(
                                c => new StripePaymentOrderPageConversationMessage
                                {
                                    conversationId = c.Id,
                                })
                            .FirstOrDefaultAsync();
                }
            }

            listOfStripePaymentOrderPageRecord.IsPaymentLinkSent = isPaymentLinkAlreadySent;
        }

        var stripePaymentOrderPageViewModels = new StripePaymentOrderPageViewModels
        {
            totalNumberOfRecords = stripePaymentOrderPageRecordsTotalCount,
            stripePaymentOrderPageRecords = listOfStripePaymentOrderPageRecords
        };

        return stripePaymentOrderPageViewModels;
    }

    public async Task<StripePaymentCustomerPaymentHistoryViewModel> GetStripePaymentCustomerPaymentHistoryRecords(
        Staff currentUser,
        string userprofileId,
        int offset,
        int limit,
        StripePaymentStatus? status)
    {
        var dbContext = _dbContextService.GetDbContext();
        var stripePaymentCustomerPaymentHistoryQuery = dbContext.StripePaymentRecords
            .AsNoTracking()
            .Where(x => x.CompanyId == currentUser.CompanyId)
            .Where(x => x.UserProfileId == userprofileId)
            .WhereIf(status.HasValue, x => x.Status == status);

        var stripePaymentCustomerPaymentHistoryRecordsTotalCount = await stripePaymentCustomerPaymentHistoryQuery
            .CountAsync();

        stripePaymentCustomerPaymentHistoryQuery = stripePaymentCustomerPaymentHistoryQuery
            .OrderByDescending(x => x.UpdatedAt)
            .Skip(offset)
            .Take(limit);

        var listOfStripePaymentCustomerPaymentHistoryRecords = await stripePaymentCustomerPaymentHistoryQuery
            .ProjectTo<StripePaymentCustomerPaymentHistoryRecord>(_mapper.ConfigurationProvider)
            .ToListAsync();

        listOfStripePaymentCustomerPaymentHistoryRecords.ForEach(
            r =>
                r.PaymentAmount = r.LineItems.Sum(x => (x.Amount - x.TotalDiscount) * x.Quantity));

        foreach (var stripePaymentCustomerPaymentHistoryRecord in listOfStripePaymentCustomerPaymentHistoryRecords)
        {
            stripePaymentCustomerPaymentHistoryRecord.ShippingFeeAmount =
                stripePaymentCustomerPaymentHistoryRecord.PayAmount == 0 ||
                stripePaymentCustomerPaymentHistoryRecord.LineItems == null
                    ? 0
                    : stripePaymentCustomerPaymentHistoryRecord.PayAmount -
                      stripePaymentCustomerPaymentHistoryRecord.LineItems.Sum(
                          l => (l.Amount - l.TotalDiscount) * l.Quantity);

            stripePaymentCustomerPaymentHistoryRecord.IsPaymentLinkSent =
                await _appDbContext.ConversationMessages.AnyAsync(
                    m => m.CompanyId == currentUser.CompanyId &&
                         m.SleekPayRecordId == stripePaymentCustomerPaymentHistoryRecord.SleekPayId);
        }

        var userProfileId = listOfStripePaymentCustomerPaymentHistoryRecords.FirstOrDefault()?.UserProfileId;

        if (userProfileId != null)
        {
            var userProfile = await dbContext.UserProfiles
                .Include(u => u.Conversation)
                .FirstOrDefaultAsync(u => u.Id == userProfileId);

            var contactOwner =
                await dbContext.UserRoleStaffs
                    .Include(s => s.Identity)
                    .FirstOrDefaultAsync(s => s.IdentityId == userProfile.ContactOwnerId);

            listOfStripePaymentCustomerPaymentHistoryRecords.ForEach(
                r => r.ContactOwner = contactOwner?.Identity?.DisplayName);

            listOfStripePaymentCustomerPaymentHistoryRecords.ForEach(
                r => r.ConversationId = userProfile.Conversation?.Id);
        }

        var stripePaymentCustomerPaymentHistoryViewModel = new StripePaymentCustomerPaymentHistoryViewModel()
        {
            totalNumberOfRecords = stripePaymentCustomerPaymentHistoryRecordsTotalCount,
            stripePaymentCustomerPaymentHistoryRecords = listOfStripePaymentCustomerPaymentHistoryRecords
        };

        return stripePaymentCustomerPaymentHistoryViewModel;
    }

    public async Task<StripePaymentConfig> UpdateStripePaymentLinkSettings(
        string companyId,
        string accountId,
        PaymentLinkExpirationOption paymentLinkExpirationOption,
        List<string> shippingAllowedCountries,
        List<ShippingOption> shippingOptions,
        bool isShippingEnabled,
        string supportEmail,
        string supportPhoneNumber,
        string statementDescriptor,
        string brandColor,
        string buttonsColor,
        IFormFile companyLogo)
    {
        var stripePaymentConfig = await _appDbContext.ConfigStripePaymentConfigs
            .FirstOrDefaultAsync(
                x =>
                    x.CompanyId == companyId
                    && x.AccountId == accountId);

        if (stripePaymentConfig == null)
        {
            throw new Exception("no such stripe payment config found");
        }

        stripePaymentConfig.PaymentLinkExpirationOption = paymentLinkExpirationOption;

        await UpdateStripeAccountInfo(
            companyId,
            accountId,
            supportEmail,
            supportPhoneNumber,
            statementDescriptor,
            brandColor,
            buttonsColor,
            companyLogo);

        stripePaymentConfig.ShippingAllowedCountries = shippingAllowedCountries;
        stripePaymentConfig.ShippingOptions = shippingOptions;
        stripePaymentConfig.IsShippingEnabled = isShippingEnabled;

        await _appDbContext.SaveChangesAsync();

        return stripePaymentConfig;
    }

    private async Task UpdateStripeAccountInfo(
        string companyId,
        string accountId,
        string supportEmail,
        string supportPhoneNumber,
        string statementDescriptor,
        string brandColor,
        string buttonsColor,
        IFormFile companyLogo)
    {
        var shareLinkAzureFunction = _configuration.GetValue<string>("Values:SleekPayFunction");

        var httpClient = _httpClientFactory.CreateClient(HttpClientHandlerName.Default);

        if (companyLogo != null)
        {
            await UploadStripeCompanyLogo(companyId, accountId, companyLogo);

            using (var formData = new MultipartFormDataContent())
            {
                using (var memoryStream = new MemoryStream())
                {
                    await companyLogo.CopyToAsync(memoryStream);
                    var fileBytes = memoryStream.ToArray();

                    formData.Add(new ByteArrayContent(fileBytes), "companyLogo", "companyLogo");

                    var response = await httpClient.PostAsync(
                        $"{shareLinkAzureFunction}/UpdateStripePaymentLinkBranding?companyId={companyId}&accountId={accountId}&supportEmail={supportEmail}&supportPhoneNumber={supportPhoneNumber}&statementDescriptor={statementDescriptor}&primaryColor={brandColor}&secondaryColor={buttonsColor}",
                        formData);

                    if (!response.IsSuccessStatusCode)
                    {
                        throw new Exception(await response.Content.ReadAsStringAsync());
                    }
                }
            }
        }
        else
        {
            var response = await httpClient.PostAsync(
                $"{shareLinkAzureFunction}/UpdateStripePaymentLinkBranding?companyId={companyId}&accountId={accountId}&supportEmail={supportEmail}&supportPhoneNumber={supportPhoneNumber}&statementDescriptor={statementDescriptor}&primaryColor={brandColor}&secondaryColor={buttonsColor}",
                null);

            if (!response.IsSuccessStatusCode)
            {
                throw new Exception(await response.Content.ReadAsStringAsync());
            }
        }
    }

    public async Task<decimal> GetStripeVolume(
        string companyId,
        string platformCountry,
        DateTime volumeFrom,
        DateTime volumeTo)
    {
        return await _appDbContext.StripePaymentRecords
            .Where(
                x =>
                    x.CompanyId == companyId
                    && x.PlatformCountry == platformCountry
                    && x.PaidAt >= volumeFrom
                    && x.PaidAt <= volumeTo)
            .SumAsync(x => x.PayAmount);
    }

    private async Task UploadStripeCompanyLogo(string companyId, string accountId, IFormFile companyLogo)
    {
        var fileName = $"StripeCompanyLogo/{companyId}/{DateTime.UtcNow.ToString("o")}/{companyLogo.FileName}";

        var uploadFileResult = await _uploadService.UploadImage(companyId, fileName, companyLogo);

        if (uploadFileResult?.Url == null)
        {
            throw new Exception("Failed to upload company logo");
        }

        var stripeCompanyLogo = new StripeCompanyLogo
        {
            BlobContainer = companyId,
            Filename = fileName,
            Url = uploadFileResult.Url,
            MIMEType = companyLogo.ContentType
        };

        await _appDbContext.StripeCompanyLogos.AddAsync(stripeCompanyLogo);

        var stripePaymentConfig =
            await _appDbContext.ConfigStripePaymentConfigs
                .FirstOrDefaultAsync(
                    x =>
                        x.CompanyId == companyId
                        && x.AccountId == accountId);

        stripePaymentConfig.StripeCompanyLogo = stripeCompanyLogo;

        await _appDbContext.SaveChangesAsync();
    }

    public async Task<string> GetStripeCompanyLogoUrl(string companyId, string platformCountry)
    {
        var stripePaymentConfig = await _appDbContext.ConfigStripePaymentConfigs
            .Include(x => x.StripeCompanyLogo)
            .FirstOrDefaultAsync(
                x =>
                    x.CompanyId == companyId &&
                    x.Country == platformCountry);

        if (stripePaymentConfig == null)
        {
            throw new Exception("no stripe payment config found for this company");
        }

        if (stripePaymentConfig.StripeCompanyLogo == null)
        {
            return null;
        }

        return _azureBlobStorageService.GetAzureBlobSasUri(
            stripePaymentConfig.StripeCompanyLogo.Filename,
            stripePaymentConfig.StripeCompanyLogo.BlobContainer);
    }

    public async Task<List<StripeSupportedCurrencyMapping>> GetStripeSupportedCurrencies(string companyId)
    {
        var stripePaymentConfigs = await _appDbContext.ConfigStripePaymentConfigs
            .Where(
                x =>
                    x.CompanyId == companyId
                    && x.Status == StripePaymentRegistrationStatus.Registered)
            .ToListAsync();

        var mappings = new List<StripeSupportedCurrencyMapping>();

        foreach (var stripePaymentConfig in stripePaymentConfigs)
        {
            var supportedCurrencies = stripePaymentConfig.SupportedCurrencies
                .Where(x => mappings.All(y => y.Currency != x));

            mappings.AddRange(
                supportedCurrencies.Select(
                    x => new StripeSupportedCurrencyMapping
                    {
                        Currency = x, PlatformCountry = stripePaymentConfig.Country
                    }));
        }

        return mappings;
    }

    public async Task<List<string>> GetStripeConnectedPlatformCountries(string companyId)
    {
        var stripePaymentConfigs = await _appDbContext.ConfigStripePaymentConfigs
            .Where(
                x =>
                    x.CompanyId == companyId
                    && x.Status == StripePaymentRegistrationStatus.Registered)
            .ToListAsync();

        return stripePaymentConfigs
            .Select(x => x.Country)
            .ToList();
    }

    public async Task<ReportRun> CreateStripePaymentActivityReport(
        string companyId,
        string receiverEmailAddress,
        DateTime startDateTime,
        DateTime endDateTime,
        string platformCountry)
    {
        var stripeClient = _stripeClients.GetStripePaymentClient(platformCountry);

        var reportTyeService = new ReportTypeService(stripeClient);

        var reportType = await reportTyeService.GetAsync("activity.itemized.2");

        if (reportType.DataAvailableEnd < startDateTime
            || reportType.DataAvailableStart > endDateTime
            || startDateTime > endDateTime)
        {
            return null;
        }

        if (reportType.DataAvailableEnd < endDateTime)
        {
            endDateTime = reportType.DataAvailableEnd;
        }

        var options = new ReportRunCreateOptions
        {
            ReportType = "activity.itemized.2",
            Parameters = new ReportRunParametersOptions
            {
                IntervalStart = startDateTime,
                IntervalEnd = endDateTime,
                Columns = new List<string>
                {
                    "balance_transaction_id",
                    "balance_transaction_created_at",
                    "balance_transaction_reporting_category",
                    "activity_at",
                    "currency",
                    "customer_id",
                    "customer_email",
                    "customer_name",
                    "shipping_address_line1",
                    "shipping_address_line2",
                    "shipping_address_city",
                    "shipping_address_state",
                    "shipping_address_country",
                    "charge_id",
                    "payment_intent_id",
                    "invoice_id",
                    "payment_method_type",
                    "card_brand",
                    "card_funding",
                    "card_country",
                    "statement_descriptor",
                    "refund_id",
                    "dispute_id",
                    "transfer_id",
                    "connected_account_id",
                    "connected_account_name",
                    "connected_account_country",
                    "connected_account_direct_charge_id",
                    "amount"
                },
            },
        };

        var reportRunService = new ReportRunService(stripeClient);
        var reportRun = await reportRunService.CreateAsync(options);

        var stripePaymentReportExportRecord = new StripePaymentReportExportRecord
        {
            CompanyId = companyId,
            StripeReportRunId = reportRun.Id,
            StripeReportRunRequestId = reportRun.StripeResponse?.RequestId,
            PlatformCountry = platformCountry,
            ReportReceiverEmailAddress = receiverEmailAddress,
            ReportDataStartAt = startDateTime,
            ReportDataEndAt = endDateTime
        };

        await _appDbContext.StripePaymentReportExportRecords.AddAsync(stripePaymentReportExportRecord);

        await _appDbContext.SaveChangesAsync();

        if (reportRun.Status == "failed"
            || reportRun.Status == "succeeded")
        {
            if (reportRun.Status == "failed")
            {
                _logger.LogWarning(
                    "[SleekPay {MethodName}] Company {CompanyId} Stripe ReportRun {ReportRunId} failed with error: {ReportRunError}",
                    nameof(CreateStripePaymentActivityReport),
                    companyId,
                    reportRun.Id,
                    reportRun.Error);
            }

            await ProcessReportRun(
                new ProcessReportRunRequest
                {
                    StripePaymentReportExportRecordId = stripePaymentReportExportRecord.Id,
                    CompanyId = companyId,
                    PlatformCountry = platformCountry,
                    ReportDataDateRangeStartAt = startDateTime,
                    ReportDataDateRangeEndAt = endDateTime,
                    ReportReceiverEmailAddress = receiverEmailAddress,
                    ReportRun = reportRun
                });
        }

        return reportRun;
    }

    private async Task ProcessReportRun(ProcessReportRunRequest processReportRunRequest)
    {
        var sleekPayFunctionDomain = _configuration.GetValue<string>("Values:SleekPayFunction");

        var httpClient = _httpClientFactory.CreateClient(HttpClientHandlerName.Default);

        var response = await httpClient.PostAsJsonAsync(
            $"{sleekPayFunctionDomain}/ProcessReportRun",
            processReportRunRequest);

        if (!response.IsSuccessStatusCode)
        {
            throw new Exception(await response.Content.ReadAsStringAsync());
        }
    }
}