﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Sleekflow.Apis.CrmHub.Api;
using Sleekflow.Apis.CrmHub.Model;
using Travis_backend.Cache;
using Travis_backend.Cache.Models;
using Travis_backend.Cache.Models.CacheKeyPatterns;
using Travis_backend.CompanyDomain.Services;
using Travis_backend.CustomObjectDomain.Helpers;
using Travis_backend.CustomObjectDomain.ViewModels;
using Travis_backend.Database;
using Travis_backend.Enums;

namespace Travis_backend.CustomObjectDomain.Services;

public interface ICustomObjectService
{
    Task<List<CustomObjectViewModel>> GetUserprofileReferencedCustomObjectsAsync(
        string companyId,
        string userProfileId,
        int schemafulObjectLimit,
        bool isFetchAllSchemas);

    Task<List<SchemaDto>> GetCompanyAllSchemasAsync(string companyId);

    Task<SchemafulObjectViewModel> AssembleSchemafulObjectViewModelAsync(SchemafulObjectDto schemafulObjectDto);

    Task<SchemaDto> GetSchemaByUniqueName(string companyId, string uniqueName);

    Task<SchemafulObjectDto> GetSchemafulObjectByPrimaryPropertyValue(
        string companyId,
        string schemaId,
        string primaryPropertyValue);
}

public class CustomObjectService : ICustomObjectService
{
    private readonly ICacheManagerService _cacheManagerService;
    private readonly ISchemasApi _schemasApi;
    private readonly ISchemafulObjectsApi _schemafulObjectsApi;
    private readonly ILogger<CustomObjectService> _logger;
    private readonly ApplicationDbContext _appDbContext;
    private readonly ICompanyTeamService _companyTeamService;

    public CustomObjectService(
        ICacheManagerService cacheManagerService,
        ISchemasApi schemasApi,
        ISchemafulObjectsApi schemafulObjectsApi,
        ILogger<CustomObjectService> logger,
        ApplicationDbContext appDbContext,
        ICompanyTeamService companyTeamService)
    {
        _cacheManagerService = cacheManagerService;
        _schemasApi = schemasApi;
        _schemafulObjectsApi = schemafulObjectsApi;
        _logger = logger;
        _appDbContext = appDbContext;
        _companyTeamService = companyTeamService;
    }

    public async Task<List<CustomObjectViewModel>> GetUserprofileReferencedCustomObjectsAsync(
        string companyId,
        string userProfileId,
        int schemafulObjectLimit,
        bool isFetchAllSchemas)
    {
        var schemas = await GetCompanyAllSchemasAsync(companyId);

        var customObjects = new List<CustomObjectViewModel>();

        var filterGroups = new List<GetSchemafulObjectsFilterGroup>
        {
            new GetSchemafulObjectsFilterGroup(
                new List<SchemafulObjectFilter>
                {
                    new SchemafulObjectFilter(
                        "sleekflow_user_profile_id",
                        "=",
                        userProfileId)
                })
        };
        var sorts = new List<SchemafulObjectSort>
        {
            new SchemafulObjectSort(
                "created_at",
                "desc")
        };

        foreach (var schema in schemas)
        {
            var getSchemafulObjectsOutputOutput =
                await _schemafulObjectsApi.SchemafulObjectsGetSchemafulObjectsPostAsync(
                    getSchemafulObjectsInput: new GetSchemafulObjectsInput(
                        companyId,
                        schema.Id,
                        null,
                        schemafulObjectLimit,
                        true,
                        filterGroups,
                        sorts));

            if (isFetchAllSchemas || getSchemafulObjectsOutputOutput.Data.SchemafulObjects.Count > 0)
            {
                var schemafulObjects = new List<SchemafulObjectViewModel>();
                foreach (var schemafulObject in getSchemafulObjectsOutputOutput.Data.SchemafulObjects)
                {
                    var schemafulObjectViewModel = await AssembleSchemafulObjectViewModelAsync(schemafulObject);
                    schemafulObjects.Add(schemafulObjectViewModel);
                }

                customObjects.Add(
                    new CustomObjectViewModel
                    {
                        Schema = schema,
                        SchemafulObjects = schemafulObjects,
                        ContinuationToken = getSchemafulObjectsOutputOutput.Data.ContinuationToken
                    });
            }
        }

        return customObjects;
    }

    public async Task<List<SchemaDto>> GetCompanyAllSchemasAsync(string companyId)
    {
        var key = $"{nameof(CustomObjectService)}-{nameof(GetCompanyAllSchemasAsync)}-{companyId}";

        var getCompanyAllSchemasCacheKeyPattern = new GetCompanyAllSchemasCacheKeyPattern(
            nameof(CustomObjectService),
            nameof(GetCompanyAllSchemasAsync),
            companyId);

        var data = await _cacheManagerService.GetCacheAsync(getCompanyAllSchemasCacheKeyPattern);
        if (!string.IsNullOrEmpty(data))
        {
            return JsonConvert.DeserializeObject<List<SchemaDto>>(data);
        }

        var schemas = new List<SchemaDto>();

        var filterGroup = new List<GetSchemasFilterGroup>
        {
            new GetSchemasFilterGroup(
                new List<SchemaFilter>
                {
                    new SchemaFilter("is_deleted", "=", false)
                })
        };
        var sort = new List<SchemaSort>
        {
            new SchemaSort("sorting_weight", "asc")
        };

        string continuationToken = null;
        int count;
        do
        {
            var getSchemasOutputOutput =
                await _schemasApi.SchemasGetSchemasPostAsync(
                    getSchemasInput: new GetSchemasInput(
                        companyId,
                        100,
                        continuationToken,
                        filterGroup,
                        sort));

            count = getSchemasOutputOutput.Data.Count;
            continuationToken = getSchemasOutputOutput.Data.ContinuationToken;

            if (count > 0)
            {
                schemas.AddRange(getSchemasOutputOutput.Data.Schemas);
            }
        }
        while (count > 0 && !string.IsNullOrEmpty(continuationToken));

        await _cacheManagerService.SaveCacheAsync(
            getCompanyAllSchemasCacheKeyPattern,
            schemas);

        return schemas;
    }

    public async Task<SchemafulObjectViewModel> AssembleSchemafulObjectViewModelAsync(
        SchemafulObjectDto schemafulObjectDto)
    {
        var referencedUserProfileViewModel = await GetReferencedUserProfileViewModelAsync(
            schemafulObjectDto.SleekflowCompanyId,
            schemafulObjectDto.SleekflowUserProfileId);

        // ReSharper disable once ConditionIsAlwaysTrueOrFalse - schemafulObjectDto.CreatedBy could be null
        if (schemafulObjectDto.CreatedBy != null)
        {
            schemafulObjectDto.CreatedBy.SleekflowStaffTeamIds = await GetTeamIdsByStaffIdAsync(
                schemafulObjectDto.SleekflowCompanyId,
                schemafulObjectDto.CreatedBy.SleekflowStaffId);
        }

        // ReSharper disable once ConditionIsAlwaysTrueOrFalse - schemafulObjectDto.UpdatedBy could be null
        if (schemafulObjectDto.UpdatedBy != null)
        {
            schemafulObjectDto.UpdatedBy.SleekflowStaffTeamIds = await GetTeamIdsByStaffIdAsync(
                schemafulObjectDto.SleekflowCompanyId,
                schemafulObjectDto.UpdatedBy.SleekflowStaffId);
        }

        return new SchemafulObjectViewModel(schemafulObjectDto, referencedUserProfileViewModel);
    }

    public async Task<SchemaDto> GetSchemaByUniqueName(string companyId, string uniqueName)
    {
        var filterGroup = new List<GetSchemasFilterGroup>
        {
            new GetSchemasFilterGroup(
                new List<SchemaFilter>
                {
                    new SchemaFilter("is_deleted", "=", false)
                }),
            new GetSchemasFilterGroup(
                new List<SchemaFilter>
                {
                    new SchemaFilter("unique_name", "=", uniqueName)
                }),
        };

        return (await _schemasApi.SchemasGetSchemasPostAsync(
                getSchemasInput: new GetSchemasInput(
                    companyId,
                    1,
                    null,
                    filterGroup,
                    new List<SchemaSort>())))
            .Data
            .Schemas
            .FirstOrDefault();
    }

    public async Task<SchemafulObjectDto> GetSchemafulObjectByPrimaryPropertyValue(
        string companyId,
        string schemaId,
        string primaryPropertyValue)
    {
        var filterGroup = new List<GetSchemafulObjectsFilterGroup>
        {
            new GetSchemafulObjectsFilterGroup(
                new List<SchemafulObjectFilter>
                {
                    new SchemafulObjectFilter(
                        "primary_property_value",
                        "=",
                        primaryPropertyValue,
                        false)
                })
        };

        return (await _schemafulObjectsApi.SchemafulObjectsGetSchemafulObjectsPostAsync(
                getSchemafulObjectsInput: new GetSchemafulObjectsInput(
                    companyId,
                    schemaId,
                    null,
                    1,
                    true,
                    filterGroup,
                    new List<SchemafulObjectSort>())))
            .Data
            .SchemafulObjects
            .FirstOrDefault();
    }

    private async Task<ReferencedUserProfileViewModel> GetReferencedUserProfileViewModelAsync(
        string companyId,
        string userProfileId)
    {
        if (string.IsNullOrEmpty(userProfileId))
        {
            return null;
        }

        try
        {
            var getCompanyAllSchemasCacheKeyPattern = new GetCompanyAllSchemasCacheKeyPattern(
                nameof(CustomObjectService),
                nameof(GetReferencedUserProfileViewModelAsync),
                userProfileId);

            var data = await _cacheManagerService.GetAndSaveCacheAsync(
                getCompanyAllSchemasCacheKeyPattern,
                async () =>
                {
                    var userProfile = await _appDbContext.UserProfiles.FirstOrDefaultAsync(
                        up =>
                            up.CompanyId == companyId &&
                            up.Id == userProfileId &&
                            up.ActiveStatus == ActiveStatus.Active);

                    var referencedUserProfileViewModel = new ReferencedUserProfileViewModel(
                        userProfile.Id,
                        userProfile.FirstName,
                        userProfile.LastName);

                    return referencedUserProfileViewModel;
                });

            return data;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(
                ex,
                "Unable to assemble user profile view model. {CompanyId} {UserProfileId}",
                companyId,
                userProfileId);

            return new ReferencedUserProfileViewModel(userProfileId, "Unknown");
        }
    }

    private async Task<List<string>> GetTeamIdsByStaffIdAsync(string companyId, string staffId)
    {
        // if created/updated by system
        if (!int.TryParse(staffId, out var staffIdInt))
        {
            return new List<string>();
        }

        try
        {
            var key = $"{nameof(CustomObjectService)}-{nameof(GetTeamIdsByStaffIdAsync)}-{companyId}-{staffId}";
            var data = await _cacheManagerService.GetAndSaveCacheWithConstantKeyAsync(
                key,
                async () =>
                {
                    var staff = await _appDbContext.UserRoleStaffs
                        .Where(x => x.CompanyId == companyId && x.Id != 1)
                        .OrderBy(x => x.Order)
                        .ThenBy(x => x.Id)
                        .Where(x => x.Id == staffIdInt)
                        .FirstOrDefaultAsync();

                    var companyTeams = await _companyTeamService.GetCompanyteamFromStaffId(companyId, staff.IdentityId);

                    var teamIds = companyTeams.Select(t => t.Id.ToString()).ToList();
                    return teamIds;
                },
                TimeSpan.FromMinutes(5));

            return data;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Get team IDs failed. {CompanyId} {StaffId}", companyId, staffId);

            return new List<string>();
        }
    }
}