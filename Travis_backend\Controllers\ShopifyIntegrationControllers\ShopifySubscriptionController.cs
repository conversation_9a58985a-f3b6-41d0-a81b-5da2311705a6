using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using AutoMapper;
using Hangfire;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using ShopifySharp;
using Travis_backend.AccountAuthenticationDomain.Models;
using Travis_backend.CommonDomain.ViewModels;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.CompanyDomain.Services;
using Travis_backend.Constants;
using Travis_backend.ConversationServices;
using Travis_backend.Database;
using Travis_backend.Enums;
using Travis_backend.Extensions;
using Travis_backend.IntegrationServices.Models;
using Travis_backend.IntegrationServices.ViewModels;
using Travis_backend.InternalDomain.Services;
using Travis_backend.StripeIntegrationDomain.ViewModels;
using Travis_backend.SubscriptionPlanDomain.Constants;
using Travis_backend.SubscriptionPlanDomain.Services;

namespace Travis_backend.Controllers.ShopifyIntegrationControllers;

[Route("shopify/subscription")]
[Authorize]
public class ShopifySubscriptionController : Controller
{
    private const string Annual = "ANNUAL";
    private const string Every30Days = "EVERY_30_DAYS";

    private readonly ApplicationDbContext _appDbContext;
    private readonly UserManager<ApplicationUser> _userManager;
    private readonly IMapper _mapper;
    private readonly ILogger _logger;
    private readonly IConfiguration _configuration;
    private readonly IHostEnvironment _environment;
    private readonly ICoreService _coreService;
    private readonly IDefaultSubscriptionPlanIdGetter _defaultSubscriptionPlanIdGetter;

    public ShopifySubscriptionController(
        ApplicationDbContext appDbContext,
        UserManager<ApplicationUser> userManager,
        IMapper mapper,
        IConfiguration configuration,
        ILogger<ShopifySubscriptionController> logger,
        IHostEnvironment environment,
        ICoreService coreService,
        IDefaultSubscriptionPlanIdGetter defaultSubscriptionPlanIdGetter)
    {
        _userManager = userManager;
        _appDbContext = appDbContext;
        _mapper = mapper;
        _configuration = configuration;
        _logger = logger;
        _environment = environment;
        _coreService = coreService;
        _defaultSubscriptionPlanIdGetter = defaultSubscriptionPlanIdGetter;
    }

    [HttpGet("status")]
    public async Task<IActionResult> GetSubscriptionStatus()
    {
        var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (companyUser == null)
        {
            return Unauthorized();
        }

        var status = false;
        var plan = string.Empty;

        var shopifyConfig = await _appDbContext.ConfigShopifyConfigs
            .Where(x => x.CompanyId == companyUser.CompanyId && x.IsShopifyBillingOwner && x.IsShopifySubscriptionPaid)
            .FirstOrDefaultAsync();

        if (shopifyConfig is not null && shopifyConfig.ChargeId is not null)
        {
            status = true;

            var billRecord = await _appDbContext.CompanyBillRecords
                .Where(x => x.CompanyId == companyUser.CompanyId && x.ShopifyChargeId == shopifyConfig.ChargeId)
                .FirstOrDefaultAsync();

            plan = billRecord?.SubscriptionPlanId ?? string.Empty;
        }

        dynamic response = new JObject();
        response.subscriptionStatus = status;
        response.subscriptionPlan = plan;

        return Ok(response);
    }

    [HttpGet]
    public async Task<IActionResult> SetupStripe()
    {
        var responseData = new StripeSetupResponse();

        var subscriptionPlan = await _appDbContext.CoreSubscriptionPlans
            .Where(x => ValidSubscriptionPlan.ShopifySubscriptionPlan.Contains(x.Id)).OrderBy(x => x.Amount)
            .ToListAsync(HttpContext.RequestAborted);
        responseData.Plans = _mapper.Map<List<SubscriptionPlanResponse>>(subscriptionPlan);

        return Ok(responseData);
    }

    [HttpPost]
    public async Task<IActionResult> StartSubscription(
        [FromBody] ShopifySubscriptionViewModels shopifySubscriptionViewModels)
    {
        var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        var subscriptionPlan =
            await _appDbContext.CoreSubscriptionPlans.FirstOrDefaultAsync(
                x => x.Id == shopifySubscriptionViewModels.PlanId);
        var amount = subscriptionPlan.Amount;
        var priceName = subscriptionPlan.SubscriptionName;

        var interval = subscriptionPlan.Id.Contains("yearly") ? Annual : Every30Days;

        var shopifyQuota = await _appDbContext.CompanyBillRecords.FirstOrDefaultAsync(x =>
            x.CompanyId == companyUser.CompanyId
            && ValidSubscriptionPlan.ShopifySubscriptionPlan.Contains(x.SubscriptionPlanId)
            && x.PeriodEnd > DateTime.Now
            && x.Status == BillStatus.Active);

        if (shopifyQuota != null)
        {
            return BadRequest(new ResponseViewModel()
            {
                message = $"You have an existing active subscription"
            });
        }

        var shopifyIntegrationPlan = await _appDbContext.CoreSubscriptionPlans.FirstOrDefaultAsync(x => x.Id == "sleekflow_v9_shopify_integration");

        priceName += $" + {shopifyIntegrationPlan.SubscriptionName}";
        amount += interval == Annual
            ? shopifyIntegrationPlan.Amount * 12
            : shopifyIntegrationPlan.Amount;

        var shopifyConfig = await _appDbContext.ConfigShopifyConfigs.FirstOrDefaultAsync(x => x.CompanyId == companyUser.CompanyId && x.IsShopifyBillingOwner);
        if (shopifyConfig == null)
        {
            _logger.LogError($"Shopify billing owner not found for company {companyUser.CompanyId}");
            return BadRequest(new ResponseViewModel() { message = $"You can only start a new subscription after designating a store as the Shopify billing owner" });
        }

        var appDomainName = _configuration.GetValue<String>("Values:AppDomainName");
        string redirectUrl = $"{appDomainName}/shopify/subscription/chargeResult";

        try
        {
            var confirmationUrl = await AppSubscriptionCreateAsync(
                shopifyConfig.UsersMyShopifyUrl,
                shopifyConfig.AccessToken,
                priceName,
                redirectUrl,
                amount,
                interval);

            dynamic apiResponse = new JObject();
            apiResponse.url = confirmationUrl;

            if (!await RegisterAppSubscriptionsUpdateWebhookAsync(shopifyConfig))
                return BadRequest(new ResponseViewModel() { message = $"Failed to register Shopify app subscriptions update webhook" }); ;

            return Ok(apiResponse);
        }
        catch (Exception ex)
        {
            return BadRequest(new ResponseViewModel() { message = ex.Message });
        }
    }

    [HttpPut]
    public async Task<IActionResult> UpdateSubscription([FromBody] ShopifySubscriptionViewModels shopifySubscriptionViewModels)
    {
        var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (companyUser == null) return Unauthorized();

        var newSubscriptionPlan = await _appDbContext.CoreSubscriptionPlans.FirstOrDefaultAsync(x => x.Id == shopifySubscriptionViewModels.PlanId);
        if (newSubscriptionPlan == null)
            return BadRequest(new ResponseViewModel() { message = $"Invalid subscription plan ID: {shopifySubscriptionViewModels.PlanId}" });

        // Retrieve Shopify account with Shopify billing owner
        var shopifyConfig = await _appDbContext.ConfigShopifyConfigs
            .Where(x => x.CompanyId == companyUser.CompanyId && x.IsShopifyBillingOwner && x.IsShopifySubscriptionPaid)
            .FirstOrDefaultAsync();

        if (shopifyConfig == null)
            return BadRequest(new ResponseViewModel() { message = "Shopify account is missing or unpaid" });

        var billRecord = await _appDbContext.CompanyBillRecords.FirstOrDefaultAsync(x =>
            x.CompanyId == companyUser.CompanyId
            && x.ShopifyChargeId == shopifyConfig.ChargeId
            && x.PeriodEnd > DateTime.Now
            && x.Status == BillStatus.Active);

        if (billRecord == null)
            return BadRequest(new ResponseViewModel() { message = "Cannot find subscription or subscription is inactive" });

        if (billRecord.SubscriptionPlanId == newSubscriptionPlan.Id)
            return BadRequest(new ResponseViewModel() { message = "You've already subscribed to this plan" });

        var addon = await _appDbContext.CoreSubscriptionPlans.FirstOrDefaultAsync(x => x.Id == "sleekflow_v9_shopify_integration");
        var interval = (newSubscriptionPlan.Id.Contains("yearly")) ? Annual : Every30Days;
        var addonAmount = interval == Annual ? addon.Amount * 12 : addon.Amount;

        // Prepare mutation variables
        var subscriptionName = $"{newSubscriptionPlan.SubscriptionName} + {addon.SubscriptionName}";
        var appDomainName = _configuration.GetValue<String>("Values:AppDomainName");
        string returnUrl = $"{appDomainName}/shopify/subscription/chargeResult";
        var amount = newSubscriptionPlan.Amount + addonAmount;

        try
        {
            var confirmationUrl = await AppSubscriptionCreateAsync(
                shopifyConfig.UsersMyShopifyUrl,
                shopifyConfig.AccessToken,
                subscriptionName,
                returnUrl,
                amount,
                interval);

            dynamic apiResponse = new JObject();
            apiResponse.url = confirmationUrl;

            return Ok(apiResponse);
        }
        catch (Exception ex)
        {
            return BadRequest(new ResponseViewModel() { message = ex.Message });
        }
    }

    /// <summary>
    /// Shopify appSubscriptionCreate mutation
    /// </summary>
    private async Task<string> AppSubscriptionCreateAsync(
        string myShopifyUrl,
        string shopAccessToken,
        string subscriptionName,
        string returnUrl,
        double amount,
        string interval)
    {
        // Note: need to use double curly brackets or else String.Format will get confused and throw an exception
        var mutation = @"
                mutation {{
                    appSubscriptionCreate(
                        name: ""{0}""
                        returnUrl: ""{1}""
                        replacementBehavior: APPLY_IMMEDIATELY
                        test: {2}
                        lineItems: [
                        {{
                            plan: {{
                                appRecurringPricingDetails: {{
                                    price: {{
                                        amount: {3},
                                        currencyCode: USD
                                    }}
                                    interval: {4}
                                }}
                            }}
                        }}
                        ]
                        ) {{
                            appSubscription {{
                                id
                            }}
                            confirmationUrl
                            userErrors {{
                                field
                                message
                            }}
                        }}
                    }}";

        mutation = String.Format(
            mutation,
            subscriptionName,
            returnUrl,
            _environment.IsDevelopment().ToString().ToLower(),
            Convert.ToDecimal(amount),
            interval);

        var service = new GraphService(myShopifyUrl, shopAccessToken);
        var response = await service.PostAsync(mutation);

        var subscriptionData = response.ToObject<ShopifySubscriptionResponse>();
        if (subscriptionData?.AppSubscriptionCreate.UserErrors == null)
            throw new ShopifyException(subscriptionData?.AppSubscriptionCreate?.UserErrors?.FirstOrDefault()?.Message);

        return subscriptionData?.AppSubscriptionCreate.ConfirmationUrl;
    }

    [HttpDelete]
    public async Task<IActionResult> CancelSubscription()
    {
        var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (companyUser == null) return Unauthorized();

        var shopifyConfig = await _appDbContext.ConfigShopifyConfigs
            .Where(x => x.CompanyId == companyUser.CompanyId && x.IsShopifyBillingOwner && x.IsShopifySubscriptionPaid)
            .FirstOrDefaultAsync();

        if (shopifyConfig is null)
            return BadRequest(new ResponseViewModel() { message = "The account does not have a billing owner or the subscription fee has not been paid" });

        if (shopifyConfig.ChargeId.HasValue)
        {
            try
            {
                var service = new RecurringChargeService(shopifyConfig.UsersMyShopifyUrl, shopifyConfig.AccessToken);
                await service.DeleteAsync(shopifyConfig.ChargeId.Value);
            }
            catch (Exception ex)
            {
                _logger.LogError($"Failed to delete subscription in Shopify: {ex.Message}");
                return BadRequest(new ResponseViewModel() { message = "Cannot delete subscription in Shopify" });
            }
        }

        dynamic response = new JObject();
        response.subscriptionStatus = false;

        return Ok(response);
    }

    private async Task<bool> RegisterAppSubscriptionsUpdateWebhookAsync(ShopifyConfig shopifyConfig)
    {
        var mutation = string.Empty;
        var appDomainName = _configuration.GetValue<string>("Values:DomainName");
        string redirectUrl = $"{appDomainName}/shopify/subscription/webhook/{shopifyConfig.Id}";

        try
        {
            var webhookId = await GetExistingAppSubscriptionsUpdateWebhookIdAsync(shopifyConfig);

            if (string.IsNullOrWhiteSpace(webhookId))
            {
                mutation = @"
                        mutation {{
                            webhookSubscriptionCreate(
                                topic: APP_SUBSCRIPTIONS_UPDATE,
                                webhookSubscription: {{
                                    callbackUrl: ""{0}"",
                                    format: JSON
                                }}
                            ) {{
                                userErrors {{
                                    field,
                                    message
                                }},
                                webhookSubscription {{
                                    callbackUrl,
                                    createdAt,
                                    id,
                                    updatedAt,
                                    topic
                                }},
                            }}
                        }}";

                mutation = string.Format(mutation, redirectUrl);
            }
            else
            {
                mutation = @"
                        mutation {{
                            webhookSubscriptionUpdate(
                                id: ""{0}"",
                                webhookSubscription: {{
                                    callbackUrl: ""{1}""
                                }}
                            ) {{
                                userErrors {{
                                    field,
                                    message
                                }},
                                webhookSubscription {{
                                    id,
                                    topic,
                                    endpoint {{
                                        ... on WebhookHttpEndpoint {{
                                            callbackUrl
                                        }}
                                    }}
                                }},
                            }}
                        }}";

                mutation = string.Format(mutation, webhookId, redirectUrl);
            }

            var service = new GraphService(shopifyConfig.UsersMyShopifyUrl, shopifyConfig.AccessToken);
            var response = await service.PostAsync(mutation);
        }
        catch (Exception ex)
        {
            _logger.LogError($"Shopify Config ID: {shopifyConfig.Id}. Error: {ex.Message}");
            return false;
        }

        return true;
    }

    private async Task<string> GetExistingAppSubscriptionsUpdateWebhookIdAsync(ShopifyConfig shopifyConfig)
    {
        var query = @"
                query {
                    webhookSubscriptions(
                        first: 1,
                        topics: APP_SUBSCRIPTIONS_UPDATE
                    ) {
                        edges {
                            node {
                                id
                            }
                        }
                    }
                }";

        var service = new GraphService(shopifyConfig.UsersMyShopifyUrl, shopifyConfig.AccessToken);
        var response = await service.PostAsync(query);
        var webhookSubscriptionsResponse = response.ToObject<ShopifyWebhookSubscriptionsResponse>();
        var edges = webhookSubscriptionsResponse.WebhookSubscriptions.Edges;
        return edges.Length > 0 ? edges[0].Node.Id : null;
    }

    [HttpPost("webhook/{shopifyId}")]
    [AllowAnonymous]
    public async Task<IActionResult> SubscriptionWebhook(
        long shopifyId,
        [FromBody] ShopifySubscriptionResponse webhookdata)
    {
        _logger.LogInformation(
            "shopify subscription webhook: {sleekflow_company_id} {payload}",
            shopifyId,
            JsonConvert.SerializeObject(webhookdata));

        var chargeId = Int64.Parse(Path.GetFileName(webhookdata.AppSubscription.AdminGraphqlApiId) ?? string.Empty);
        var shopifyConfig = await _appDbContext.ConfigShopifyConfigs.FirstOrDefaultAsync(x => x.Id == shopifyId);
        if (shopifyConfig == null)
            return Ok();

        bool status;
        switch (webhookdata.AppSubscription.Status)
        {
            case "ACTIVE":
                status = await HandleActiveSubscriptionWebhookAsync(shopifyConfig, chargeId);
                break;
            case "CANCELLED":
                status = await HandleCancelledSubscriptionWebhookAsync(shopifyConfig, chargeId);
                break;
            default:
                return Ok();
        }

        dynamic response = new JObject();
        response.subscriptionStatus = status;

        return Ok(response);
    }

    private async Task<bool> HandleActiveSubscriptionWebhookAsync(ShopifyConfig shopifyConfig, long chargeId)
    {
        shopifyConfig.IsShopifySubscriptionPaid = true;
        shopifyConfig.ChargeId = chargeId;
        shopifyConfig.ChargeUpdatedAt = DateTime.UtcNow;
        await _appDbContext.SaveChangesAsync();

        return await UpdateBillingAsync(shopifyConfig, chargeId);
    }

    private async Task<bool> UpdateBillingAsync(ShopifyConfig shopifyConfig, long chargeId)
    {
        var company = await _appDbContext.CompanyCompanies.FirstOrDefaultAsync(x => x.Id == shopifyConfig.CompanyId);

        // Get the charge via Shopify API and check its status
        var service = new RecurringChargeService(
            shopifyConfig?.UsersMyShopifyUrl,
            shopifyConfig?.AccessToken);
        var charge = await service.GetAsync(chargeId);

        if (charge.Name.Contains("+"))
            charge.Name = charge.Name.Substring(0, charge.Name.IndexOf("+") - 1);
        var subscriptionPlan =
            await _appDbContext.CoreSubscriptionPlans.FirstOrDefaultAsync(
                x => charge.Name == x.SubscriptionName && x.Currency == "usd");

        var startDateTime = charge.CreatedAt.Value.UtcDateTime;
        var endDateTime = subscriptionPlan.Id.Contains("yearly") ? startDateTime.AddYears(1) : startDateTime.AddDays(30);

        switch (charge.Status)
        {
            case "active":
                //add billing
                company.IsFreeTrial = false;

                if (subscriptionPlan.IncludedAgents > company.MaximumAgents)
                    company.MaximumAgents = subscriptionPlan.IncludedAgents;

                if (subscriptionPlan.MaximumAutomation > company.MaximumAutomations)
                    company.MaximumAutomations = subscriptionPlan.MaximumAutomation;

                if (_appDbContext.CompanySandboxes.Any(x => x.CompanyId == company.Id))
                    BackgroundJob.Enqueue<ICompanyService>(x => x.DeleteSandbox(company.Id));

                var apiKey = _appDbContext.CompanyAPIKeys.Where(x => x.CompanyId == company.Id);

                await apiKey.ForEachAsync(
                    x =>
                    {
                        x.CallLimit = subscriptionPlan.MaximumAPICall;
                        x.Calls = 0;
                    });

                var billRecords = await _appDbContext.CompanyBillRecords
                    .Where(x => x.CompanyId == shopifyConfig.CompanyId)
                    .ToListAsync();

                var chargedBillRecord = billRecords.FirstOrDefault(x => x.ShopifyChargeId == chargeId && x.PeriodStart == startDateTime);

                if (chargedBillRecord is null)
                {
                    // Cancel the freemium bill record before creating a new bill record for the new subscription
                    var activeBillRecord = billRecords
                        .Where(x => SubscriptionPlansId.FreemiumStartupPlans.ContainsIgnoreCase(x.SubscriptionPlanId) && x.Status == BillStatus.Active)
                        .OrderByDescending(x => x.created)
                        .FirstOrDefault();

                    if (activeBillRecord is not null)
                        activeBillRecord.Status = BillStatus.Canceled;

                    chargedBillRecord = new BillRecord
                    {
                        SubscriptionPlan = subscriptionPlan,
                        currency = "usd",
                        created = DateTime.UtcNow,
                        CompanyId = company.Id,
                        amount_due = Convert.ToInt64(charge.Price * 100),
                        amount_paid = Convert.ToInt64(charge.Price * 100),
                        amount_remaining = 0,
                        PayAmount = Convert.ToDouble(charge.Price),
                        Status = BillStatus.Active,
                        PaymentStatus = PaymentStatus.Paid,
                        PeriodStart = startDateTime,
                        PeriodEnd = endDateTime,
                        SubscriptionTier = subscriptionPlan.SubscriptionTier,
                        ShopifyChargeId = charge.Id
                    };

                    try
                    {
                        await _appDbContext.CompanyBillRecords.AddAsync(chargedBillRecord);
                        await _appDbContext.SaveChangesAsync();
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "unable to add bill");
                        chargedBillRecord = await _appDbContext.CompanyBillRecords.Where(x => x.invoice_Id == chargedBillRecord.invoice_Id).FirstOrDefaultAsync();
                    }

                    BackgroundJob.Enqueue<IInternalHubSpotService>(x => x.SyncCompany(shopifyConfig.CompanyId, null));
                }
                else
                {
                    chargedBillRecord.SubscriptionPlan = await _appDbContext.CoreSubscriptionPlans.FirstOrDefaultAsync(x => x.Id == subscriptionPlan.Id);
                    chargedBillRecord.PeriodStart = startDateTime;
                    chargedBillRecord.PeriodEnd = endDateTime;
                    chargedBillRecord.ShopifyChargeId = charge.Id;
                    await _appDbContext.SaveChangesAsync();
                }

                return true;
        }

        return false;
    }

    private async Task<bool> HandleCancelledSubscriptionWebhookAsync(ShopifyConfig shopifyConfig, long chargeId)
    {
        var activeBillRecords = await _appDbContext.CompanyBillRecords
            .Where(x => x.CompanyId == shopifyConfig.CompanyId && x.Status == BillStatus.Active)
            .ToListAsync();

        var chargedBillRecord = activeBillRecords.FirstOrDefault(x => x.ShopifyChargeId == chargeId);

        if (chargedBillRecord is null)
            return false;

        chargedBillRecord.Status = BillStatus.Canceled;
        chargedBillRecord.PayAmount = 0;
        chargedBillRecord.amount_due = 0;
        chargedBillRecord.amount_paid = 0;

        if (!activeBillRecords.Any(x => SubscriptionPlansId.FreemiumStartupPlans.ContainsIgnoreCase(x.SubscriptionPlanId)))
        {
            // Create a new bill record with freemium plan if there is no existing active freemium plan
            // This indicates that the user will revert to the freemium plan once their subscription is canceled
            var freemiumBillRecord = new BillRecord()
            {
                CompanyId = shopifyConfig.CompanyId,
                SubscriptionPlanId = _defaultSubscriptionPlanIdGetter.GetDefaultStartupPlanId(),
                Status = BillStatus.Active,
                PaymentStatus = PaymentStatus.FreeOfCharge,
                PayAmount = 0,
                currency = chargedBillRecord.currency,
                PeriodStart = DateTime.UtcNow,
                PeriodEnd = DateTime.UtcNow.AddMonths(1)
            };

            await _appDbContext.CompanyBillRecords.AddAsync(freemiumBillRecord);
        }

        // Sometimes, Shopify doesn't trigger events in sequence
        // Make sure the latest shopifyConfig is not overriden by previous charges
        if (shopifyConfig.ChargeId == chargeId)
        {
            shopifyConfig.IsShopifySubscriptionPaid = false;
            shopifyConfig.ChargeId = null;
            shopifyConfig.ChargeUpdatedAt = DateTime.UtcNow;
        }

        await _appDbContext.SaveChangesAsync();
        return false;
    }

    [HttpGet("chargeResult")]
    public async Task<IActionResult> ReceiveChargeResult([FromQuery] long charge_id)
    {
        var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (companyUser == null)
        {
            return Unauthorized();
        }

        var shopifyConfig = await _appDbContext.ConfigShopifyConfigs
            .Where(x => x.CompanyId == companyUser.CompanyId && x.ChargeId == charge_id)
            .FirstOrDefaultAsync();

        if (shopifyConfig is null)
        {
            return NotFound(new ResponseViewModel() { message = $"Cannot find Shopify account with charge ID: {charge_id}" });
        }
        else
        {
            dynamic response = new JObject();
            response.charge_id = charge_id;
            return Ok(response);
        }
    }
}