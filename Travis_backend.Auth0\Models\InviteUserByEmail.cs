using Auth0.ManagementApi.Models;
using Newtonsoft.Json;
using Travis_backend.Enums;

namespace Travis_backend.Auth0.Models;


public class InvitedUserByEmailObject
{
    [JsonProperty("email")]
    public string Email { get; set; }

    [JsonProperty("username")]
    public string UserName { get; set; }

    [JsonProperty("firstname")]
    public string Firstname { get; set; }

    [Json<PERSON>roperty("lastname")]
    public string Lastname { get; set; }

    [<PERSON>son<PERSON>roperty("user_role")]
    public string UserRole { get; set; }

    [JsonProperty("position")]
    public string Position { get; set; }

    [JsonProperty("time_zone_info_id")]
    public string TimeZoneInfoId { get; set; }

    [JsonProperty("staff_id")]
    public long StaffId { get; set; }

    [JsonProperty("sleekflow_user_id")]
    public string SleekflowUserId { get; set; }

    [JsonProperty("tenant_hub_user_id")]
    public string TenantHubUserId { get; set; }

    [JsonProperty("auth0_user")]
    public User? Auth0User { get; set; }

    [JsonConstructor]
    public InvitedUserByEmailObject(
        string email,
        string userName,
        string firstname,
        string lastname,
        string userRole,
        string position,
        string timeZoneInfoId,
        string sleekflowUserId,
        string tenantHubUserId,
        long staffId,
        User? auth0User)
    {
        Email = email;
        UserName = userName;
        Firstname = firstname;
        Lastname = lastname;
        UserRole = userRole;
        Position = position;
        TimeZoneInfoId = timeZoneInfoId;
        SleekflowUserId = sleekflowUserId;
        TenantHubUserId = tenantHubUserId;
        StaffId = staffId;
        Auth0User = auth0User;
    }
}