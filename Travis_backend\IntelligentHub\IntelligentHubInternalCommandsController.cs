using System;
using System.Diagnostics.CodeAnalysis;
using System.Linq;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Sleekflow.Apis.IntelligentHub.Model;
using Travis_backend.Cache;
using Travis_backend.ChannelDomain.Services;
using Travis_backend.CommonDomain.ViewModels;
using Travis_backend.CompanyDomain.Services;
using Travis_backend.ContactDomain.Services;
using Travis_backend.ContactDomain.ViewModels;
using Travis_backend.Database.Services;
using Travis_backend.Enums;
using Travis_backend.Exceptions;
using Travis_backend.IntelligentHub.Attributes;

namespace Travis_backend.IntelligentHub;

[IntelligentHubAuthorization]
[Route("IntelligentHub/Internals")]
[SuppressMessage("ReSharper", "ConditionIsAlwaysTrueOrFalse")]
public class IntelligentHubInternalCommandsController : ControllerBase
{
    private readonly ILogger _logger;
    private readonly IMapper _mapper;
    private readonly ILockService _lockService;
    private readonly IDbContextService _dbContextService;
    private readonly IUserProfileService _userProfileService;
    private readonly ICompanyUsageService _companyUsageService;
    private readonly IContactWhatsappSenderLockService _contactWhatsappSenderLockService;

    public IntelligentHubInternalCommandsController(
        ILogger<IntelligentHubInternalCommandsController> logger,
        IMapper mapper,
        ILockService lockService,
        IDbContextService dbContextService,
        IUserProfileService userProfileService,
        ICompanyUsageService companyUsageService,
        IContactWhatsappSenderLockService contactWhatsappSenderLockService)
    {
        _logger = logger;
        _mapper = mapper;
        _lockService = lockService;
        _dbContextService = dbContextService;
        _userProfileService = userProfileService;
        _companyUsageService = companyUsageService;
        _contactWhatsappSenderLockService = contactWhatsappSenderLockService;
    }


    [HttpPost("Commands/GetUserProfile")]
    public async Task<IActionResult> GetUserProfileAsync([FromBody] GetUserProfileInput getUserProfileInput)
    {
        var userProfile = await _userProfileService.GetUserProfileWithPhoneNumberAsync(
            getUserProfileInput.SleekflowCompanyId,
            getUserProfileInput.PhoneNumber);
        var response = userProfile == null
            ? null
            : new GetUserProfileOutput(userProfile.Id, userProfile.CompanyId, userProfile.PhoneNumber);
        return Ok(response);
    }

    [HttpPost("Commands/CreateUserProfile")]
    public async Task<IActionResult> CreateUserProfileAsync([FromBody] CreateUserProfileInput createUserProfileInput)
    {
        var sleekflowCompanyId = createUserProfileInput.SleekflowCompanyId;
        var dbContext = _dbContextService.GetDbContext();

        var phoneNumber = createUserProfileInput.PhoneNumber;

        var company = await dbContext.CompanyCompanies.FirstOrDefaultAsync(x => x.Id == sleekflowCompanyId);
        if (company == null)
        {
            return Unauthorized();
        }

        try
        {
            var companyUsage = await _companyUsageService.GetCompanyContactUsageAsync(company.Id);
            var availableContactCount = companyUsage.MaximumContacts - companyUsage.TotalContactCount;

            if (availableContactCount < 1)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        code = 400,
                        ErrorCode = "ContactLimitExceeded",
                        message =
                            "Your company has reached the maximum number of contacts allowed. Please upgrade your plan or purchase additional add on to add more contacts"
                    });
            }

            ILockService.Lock myLock = null;
            ILockService.Lock whatsappPhoneNumberLock = null;
            if (!string.IsNullOrEmpty(phoneNumber))
            {
                if (!await dbContext.UserProfiles
                        .AnyAsync(
                            x =>
                                x.CompanyId == company.Id
                                && x.PhoneNumber == phoneNumber
                                && x.ActiveStatus == ActiveStatus.Active))
                {
                    myLock = await _lockService.AcquireLockAsync(
                        AddNewContactLockKey(company.Id, phoneNumber),
                        TimeSpan.FromSeconds(5));
                }

                try
                {
                    whatsappPhoneNumberLock =
                        await _contactWhatsappSenderLockService.GetContactWhatsappPhoneNumberLockAsync(
                            company.Id,
                            phoneNumber);
                }
                catch (ContactWhatsappSenderLockException contactWhatsappSenderLockException)
                {
                    return BadRequest(
                        new ResponseViewModel
                        {
                            code = 400, message = "Server busy"
                        });
                }
            }

            var createdUserProfiles = await _userProfileService.AddOrUpdateUserProfile(
                company.Id,
                new NewProfileViewModel
                {
                    FirstName = createUserProfileInput.Name,
                    PhoneNumber = createUserProfileInput.PhoneNumber,
                    WhatsAppPhoneNumber = phoneNumber,
                });

            if (myLock != null)
            {
                await _lockService.ReleaseLockAsync(myLock);
            }

            if (whatsappPhoneNumberLock != null)
            {
                await _lockService.ReleaseLockAsync(whatsappPhoneNumberLock);
            }

            var userProfile = createdUserProfiles.FirstOrDefault();

            return Ok(new CreateUserProfileOutput(userProfile!.Id, userProfile!.CompanyId, userProfile!.PhoneNumber));
        }
        catch (FormatException fex)
        {
            try
            {
                var errorDetail = JsonConvert.DeserializeObject<ErrorDetail>(fex.Message);

                if (await dbContext.UserProfiles.AnyAsync(
                        x => x.Id == errorDetail.UserProfileId && x.CompanyId == company.Id))
                {
                    return BadRequest(
                        new ResponseViewModel()
                        {
                            ErrorCode = "DuplicateContact",
                            message =
                                $"Duplicate contact found. ID: {errorDetail.UserProfileId}. Reason: {errorDetail.FieldName}"
                        });
                }
            }
            catch (Exception)
            {
                var splitMessage = fex.Message.Split(':');

                if (splitMessage.Length != 2)
                {
                    return BadRequest(
                        new ResponseViewModel()
                        {
                            message = fex.Message
                        });
                }

                var userProfileId = splitMessage[0];
                var duplicateReason = splitMessage[1];

                if (await dbContext.UserProfiles.AnyAsync(x => x.Id == userProfileId && x.CompanyId == company.Id))
                {
                    return BadRequest(
                        new ResponseViewModel()
                        {
                            ErrorCode = "DuplicateContact",
                            message = $"Duplicate contact found. ID: {userProfileId}. Reason: {duplicateReason}"
                        });
                }
            }

            return BadRequest(
                new ResponseViewModel()
                {
                    message = fex.Message
                });
        }
        catch (Exception ex)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    code = 400, message = ex.Message
                });
        }
    }

    private string AddNewContactLockKey(string companyId, string phoneNumber)
    {
        return $"intelligent_hub_add_contact_{companyId}_{phoneNumber}";
    }
}