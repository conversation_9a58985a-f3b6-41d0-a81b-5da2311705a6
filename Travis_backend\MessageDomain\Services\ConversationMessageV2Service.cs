using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Threading;
using System.Threading.Tasks;
using LinqKit;
using Microsoft.EntityFrameworkCore;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.Constants;
using Travis_backend.Database;
using Travis_backend.Enums;
using Travis_backend.Extensions;
using Travis_backend.MessageDomain.Models;
using Travis_backend.MessageDomain.Repositories;
using Travis_backend.SignalR;

namespace Travis_backend.MessageDomain.Services;

public interface IConversationMessageV2Service
{
    ValueTask<IQueryable<ConversationMessage>> FilterConversationMessageWithDefaultChannelQueryableAsync(
        string companyId,
        long staffId,
        StaffUserRole roleType,
        string conversationId,
        long? beforeMessageId,
        long? afterMessageId,
        long? beforeTimestamp,
        long? afterTimestamp,
        List<ChannelMessageFilter> channelMessageFilters,
        string messageType,
        string messageContent,
        string? messageStatus,
        bool? isFromUser,
        bool? isFromImport,
        string order);

    IQueryable<ConversationMessage> FilterConversationMessageQueryable(
        string companyId,
        string conversationId,
        long? beforeMessageId,
        long? afterMessageId,
        long? beforeTimestamp,
        long? afterTimestamp,
        List<ChannelMessageFilter> channelMessageFilters,
        string messageType,
        string messageContent,
        string messageStatus,
        bool? isFromUser,
        bool? isFromImport,
        string order);

    IQueryable<ConversationMessage> GetConversationMessageByMessageIdQueryable(
        string companyId,
        string conversationId,
        long messageId);

    IQueryable<ConversationMessage> GetConversationMessageByMessageUniqueIdQueryable(
        string companyId,
        string conversationId,
        string messageUniqueId);

    ValueTask<(bool IsValid, string ErrorMessage)> ValidateConversationStaffPermission(
        Staff companyUser,
        string conversationId,
        CancellationToken cancellationToken = default);
}

public class ConversationMessageV2Service : IConversationMessageV2Service
{
    private readonly ApplicationDbContext _appDbContext;
    private readonly IChannelIdentityIdRepository _channelIdentityIdRepository;

    public ConversationMessageV2Service(
        ApplicationDbContext appDbContext,
        IChannelIdentityIdRepository channelIdentityIdRepository)
    {
        _appDbContext = appDbContext;
        _channelIdentityIdRepository = channelIdentityIdRepository;
    }

    public async ValueTask<IQueryable<ConversationMessage>> FilterConversationMessageWithDefaultChannelQueryableAsync(
        string companyId,
        long staffId,
        StaffUserRole roleType,
        string conversationId,
        long? beforeMessageId,
        long? afterMessageId,
        long? beforeTimestamp,
        long? afterTimestamp,
        List<ChannelMessageFilter> channelMessageFilters,
        string messageType,
        string messageContent,
        string? messageStatus,
        bool? isFromUser,
        bool? isFromImport,
        string order)
    {
        ExpressionStarter<ConversationMessage> defaultChannelPredicate = null;

        if (roleType != StaffUserRole.Admin)
        {
            var rolePermission = await _appDbContext.CompanyRolePermissions
                .Where(x => x.CompanyId == companyId && x.StaffUserRole == roleType)
                .FirstOrDefaultAsync();

            if (rolePermission != null && rolePermission.Permission.IsShowDefaultChannelMessagesOnly)
            {
                var staffInTeamIds = await _appDbContext.CompanyTeamMembers
                    .Where(x => x.StaffId == staffId)
                    .Select(x => x.CompanyTeamId)
                    .ToListAsync();

                var teamDefaultChannelList = await _appDbContext.CompanyStaffTeams
                    .AsNoTracking()
                    .Where(x => staffInTeamIds.Contains(x.Id))
                    .Select(x => x.DefaultChannels)
                    .ToListAsync();

                var teamPredicate = PredicateBuilder.New<ConversationMessage>(false);

                foreach (var teamDefaultChannels in teamDefaultChannelList
                             .Where(x => x != null && x.Any()))
                {
                    var teamDefaultChannelsPredicate = PredicateBuilder.New<ConversationMessage>(false);

                    foreach (var defaultChannel in teamDefaultChannels)
                    {
                        var teamDefaultChannelIdentityIds =
                            await _channelIdentityIdRepository.GetChannelIdentityIdsByChannelIds(
                                defaultChannel.channel,
                                defaultChannel.ids);

                        foreach (var teamDefaultChannelIdentityId in teamDefaultChannelIdentityIds)
                        {
                            teamDefaultChannelsPredicate =
                                teamDefaultChannelsPredicate.Or(
                                    x => x.ChannelIdentityId == teamDefaultChannelIdentityId &&
                                         x.Channel == defaultChannel.channel);
                        }
                    }

                    teamPredicate = teamPredicate.Or(teamDefaultChannelsPredicate);
                }

                if (teamDefaultChannelList.Any(x => x != null && x.Any()))
                {
                    defaultChannelPredicate = teamPredicate.Or(
                        x => ChannelTypes.NonDefaultChannelChannelTypes.Contains(x.Channel));
                }
            }
        }

        var result = FilterConversationMessageQueryable(
                companyId,
                conversationId,
                beforeMessageId,
                afterMessageId,
                beforeTimestamp,
                afterTimestamp,
                channelMessageFilters,
                messageType,
                messageContent,
                messageStatus,
                isFromUser,
                isFromImport,
                order)
            .WhereIf(defaultChannelPredicate != null, defaultChannelPredicate);

        return result;
    }

    public IQueryable<ConversationMessage> FilterConversationMessageQueryable(
        string companyId,
        string conversationId,
        long? beforeMessageId,
        long? afterMessageId,
        long? beforeTimestamp,
        long? afterTimestamp,
        List<ChannelMessageFilter> channelMessageFilters,
        string messageType,
        string messageContent,
        string? messageStatus,
        bool? isFromUser,
        bool? isFromImport,
        string order)
    {
        var predicate = PredicateBuilder.New<ConversationMessage>(x => x.CompanyId == companyId);

        if (conversationId != null)
        {
            predicate = predicate.And(x => x.ConversationId == conversationId);
        }

        if (beforeMessageId != null)
        {
            predicate = predicate.And(x => x.Id <= beforeMessageId);
        }

        if (afterMessageId != null)
        {
            predicate = predicate.And(x => x.Id >= afterMessageId);
        }

        if (beforeTimestamp != null)
        {
            predicate = predicate.And(x => x.Timestamp <= beforeTimestamp);
        }

        if (afterTimestamp != null)
        {
            predicate = predicate.And(x => x.Timestamp >= afterTimestamp);
        }

        if (afterTimestamp != null)
        {
            predicate = predicate.And(x => x.Timestamp >= afterTimestamp);
        }

        if (isFromUser.HasValue)
        {
            predicate = predicate.And(x => x.IsSentFromSleekflow == !isFromUser.Value);
        }

        if (isFromImport.HasValue)
        {
            predicate = predicate.And(x => x.IsFromImport == isFromImport.Value);
        }

        if (!string.IsNullOrEmpty(messageType))
        {
            predicate = predicate.And(x => x.MessageType == messageType);
        }

        if (!string.IsNullOrEmpty(messageContent))
        {
            predicate = predicate.And(x => x.MessageContent.Contains(messageContent));
        }

        if (messageStatus is not null
            &&
            Enum.TryParse(messageStatus, true, out MessageStatus convertedMessageStatus))
        {
            predicate = predicate.And(x => x.Status == convertedMessageStatus);
        }

        if (channelMessageFilters != null && channelMessageFilters.Any())
        {
            var channelFilterPredicate = PredicateBuilder.New<ConversationMessage>(true);

            foreach (var channelMessageFilter in channelMessageFilters)
            {
                channelFilterPredicate = channelFilterPredicate.Or(IsSameChannel(channelMessageFilter));
            }

            channelFilterPredicate = channelFilterPredicate.Or(x => x.Channel == ChannelTypes.Note);
            channelFilterPredicate = channelFilterPredicate.Or(x => x.Channel == ChannelTypes.LiveChat);

            predicate = predicate.And(channelFilterPredicate);
        }

        var resultQueryable = _appDbContext.ConversationMessages
            .AsExpandable()
            .Where(predicate);

        resultQueryable = order switch
        {
            "asc" => resultQueryable.OrderBy(x => x.Timestamp),
            _ => resultQueryable.OrderByDescending(x => x.Timestamp)
        };

        return resultQueryable;
    }

    public IQueryable<ConversationMessage> GetConversationMessageByMessageIdQueryable(
        string companyId,
        string conversationId,
        long messageId)
    {
        var resultQueryable = _appDbContext.ConversationMessages
            .Where(x => x.CompanyId == companyId && x.ConversationId == conversationId && x.Id == messageId);

        return resultQueryable;
    }

    public IQueryable<ConversationMessage> GetConversationMessageByMessageUniqueIdQueryable(
        string companyId,
        string conversationId,
        string messageUniqueId)
    {
        var resultQueryable = _appDbContext.ConversationMessages
            .Where(
                x => x.CompanyId == companyId && x.ConversationId == conversationId &&
                     x.MessageUniqueID == messageUniqueId);

        return resultQueryable;
    }

    public ValueTask<(bool IsValid, string ErrorMessage)> ValidateConversationStaffPermission(
        Staff companyUser,
        string conversationId,
        CancellationToken cancellationToken = default)
    {
        return ValueTask.FromResult<(bool IsValid, string ErrorMessage)>((true, null));
    }

    private Expression<Func<ConversationMessage, bool>> IsSameChannel(
        ChannelMessageFilter filter)
    {
        if (string.IsNullOrEmpty(filter.ChannelType) && string.IsNullOrEmpty(filter.ChannelIdentityId))
        {
            return x => false;
        }

        if (!string.IsNullOrEmpty(filter.ChannelType) && !string.IsNullOrEmpty(filter.ChannelIdentityId))
        {
            return x => x.Channel == filter.ChannelType && x.ChannelIdentityId == filter.ChannelIdentityId;
        }

        return x => x.Channel == filter.ChannelType;
    }
}