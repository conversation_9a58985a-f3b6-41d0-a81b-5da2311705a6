using System;
using System.Collections.Generic;
using Travis_backend.SubscriptionPlanDomain.Constants;
using Travis_backend.SubscriptionPlanDomain.Models;
using Travis_backend.SubscriptionPlanDomain.Models.Common;

namespace Travis_backend.SubscriptionPlanDomain.Repositories;

public static class FlowBuilderFlowEnrolmentsPlanDefinitions
{
    private const string V10 = "v10";

    public static readonly IEnumerable<PlanDefinition> FlowEnrolmentsDefinitions = new List<PlanDefinition>
    {
        #region V10 FlowEnrolments Tier1 Pro Monthly Plans
        new(
            new List<Multilingual>
            {
                new("en", "flow_builder_flow_enrolments_1500_pro_monthly", true),
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new(FeatureId.FlowBuilderFlowEnrolment, 1500),
            },
            new List<Price>
            {
                new("USD", 59),
                new("HKD", 469),
                new("CNY", 429),
                new("EUR", 59),
                new("GBP", 49),
                new("CAD", 89),
                new("AUD", 99),
                new("AED", 219),
            },
            PlanTypes.AddOns,
            null,
            V10,
            new List<string> { "Active" },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            "Tier1",
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new(
            new List<Multilingual>
            {
                new("en", "flow_builder_flow_enrolments_2500_pro_monthly", true),
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new(FeatureId.FlowBuilderFlowEnrolment, 2500),
            },
            new List<Price>
            {
                new("USD", 119),
                new("HKD", 929),
                new("CNY", 869),
                new("EUR", 109),
                new("GBP", 99),
                new("CAD", 169),
                new("AUD", 189),
                new("AED", 439),
            },
            PlanTypes.AddOns,
            null,
            V10,
            new List<string> { "Active" },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            "Tier1",
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new(
            new List<Multilingual>
            {
                new("en", "flow_builder_flow_enrolments_3000_pro_monthly", true),
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new(FeatureId.FlowBuilderFlowEnrolment, 3000),
            },
            new List<Price>
            {
                new("USD", 149),
                new("HKD", 1169),
                new("CNY", 1079),
                new("EUR", 139),
                new("GBP", 119),
                new("CAD", 209),
                new("AUD", 229),
                new("AED", 549),
            },
            PlanTypes.AddOns,
            null,
            V10,
            new List<string> { "Active" },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            "Tier1",
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        #endregion
        #region V10 FlowEnrolments Tier1 Premium Monthly Plans
        new(
            new List<Multilingual>
            {
                new("en", "flow_builder_flow_enrolments_5000_premium_monthly", true),
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new(FeatureId.FlowBuilderFlowEnrolment, 5000),
            },
            new List<Price>
            {
                new("USD", 99),
                new("HKD", 779),
                new("CNY", 719),
                new("EUR", 99),
                new("GBP", 79),
                new("CAD", 139),
                new("AUD", 159),
                new("AED", 369),
            },
            PlanTypes.AddOns,
            null,
            V10,
            new List<string> { "Active" },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            "Tier1",
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new(
            new List<Multilingual>
            {
                new("en", "flow_builder_flow_enrolments_7000_premium_monthly", true),
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new(FeatureId.FlowBuilderFlowEnrolment, 7000),
            },
            new List<Price>
            {
                new("USD", 199),
                new("HKD", 1559),
                new("CNY", 1449),
                new("EUR", 189),
                new("GBP", 159),
                new("CAD", 279),
                new("AUD", 309),
                new("AED", 739),
            },
            PlanTypes.AddOns,
            null,
            V10,
            new List<string> { "Active" },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            "Tier1",
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new(
            new List<Multilingual>
            {
                new("en", "flow_builder_flow_enrolments_10000_premium_monthly", true),
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new(FeatureId.FlowBuilderFlowEnrolment, 10000),
            },
            new List<Price>
            {
                new("USD", 349),
                new("HKD", 2729),
                new("CNY", 2529),
                new("EUR", 329),
                new("GBP", 269),
                new("CAD", 489),
                new("AUD", 539),
                new("AED", 1289),
            },
            PlanTypes.AddOns,
            null,
            V10,
            new List<string> { "Active" },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            "Tier1",
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        #endregion
        #region V10 FlowEnrolments Tier2 Pro Monthly Plans
        new(
            new List<Multilingual>
            {
                new("en", "flow_builder_flow_enrolments_1500_pro_monthly", true),
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new(FeatureId.FlowBuilderFlowEnrolment, 1500),
            },
            new List<Price>
            {
                new("USD", 59),
                new("SGD", 79),
                new("EUR", 59),
            },
            PlanTypes.AddOns,
            null,
            V10,
            new List<string> { "Active" },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            "Tier2",
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new(
            new List<Multilingual>
            {
                new("en", "flow_builder_flow_enrolments_2500_pro_monthly", true),
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new(FeatureId.FlowBuilderFlowEnrolment, 2500),
            },
            new List<Price>
            {
                new("USD", 119),
                new("SGD", 159),
                new("EUR", 109),
            },
            PlanTypes.AddOns,
            null,
            V10,
            new List<string> { "Active" },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            "Tier2",
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new(
            new List<Multilingual>
            {
                new("en", "flow_builder_flow_enrolments_3000_pro_monthly", true),
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new(FeatureId.FlowBuilderFlowEnrolment, 3000),
            },
            new List<Price>
            {
                new("USD", 149),
                new("SGD", 209),
                new("EUR", 139),
            },
            PlanTypes.AddOns,
            null,
            V10,
            new List<string> { "Active" },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            "Tier2",
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        #endregion
        #region V10 FlowEnrolments Tier2 Premium Monthly Plans
        new(
            new List<Multilingual>
            {
                new("en", "flow_builder_flow_enrolments_5000_premium_monthly", true),
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new(FeatureId.FlowBuilderFlowEnrolment, 5000),
            },
            new List<Price>
            {
                new("USD", 99),
                new("SGD", 139),
                new("EUR", 99),
            },
            PlanTypes.AddOns,
            null,
            V10,
            new List<string> { "Active" },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            "Tier2",
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new(
            new List<Multilingual>
            {
                new("en", "flow_builder_flow_enrolments_7000_premium_monthly", true),
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new(FeatureId.FlowBuilderFlowEnrolment, 7000),
            },
            new List<Price>
            {
                new("USD", 199),
                new("SGD", 269),
                new("EUR", 189),
            },
            PlanTypes.AddOns,
            null,
            V10,
            new List<string> { "Active" },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            "Tier2",
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new(
            new List<Multilingual>
            {
                new("en", "flow_builder_flow_enrolments_10000_premium_monthly", true),
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new(FeatureId.FlowBuilderFlowEnrolment, 10000),
            },
            new List<Price>
            {
                new("USD", 349),
                new("SGD", 469),
                new("EUR", 329),
            },
            PlanTypes.AddOns,
            null,
            V10,
            new List<string> { "Active" },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            "Tier2",
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        #endregion
        #region V10 FlowEnrolments Tier3 Pro Monthly Plans
        new(
            new List<Multilingual>
            {
                new("en", "flow_builder_flow_enrolments_1500_pro_monthly", true),
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new(FeatureId.FlowBuilderFlowEnrolment, 1500),
            },
            new List<Price>
            {
                new("USD", 59),
                new("BRL", 339),
            },
            PlanTypes.AddOns,
            null,
            V10,
            new List<string> { "Active" },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            "Tier3",
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new(
            new List<Multilingual>
            {
                new("en", "flow_builder_flow_enrolments_2500_pro_monthly", true),
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new(FeatureId.FlowBuilderFlowEnrolment, 2500),
            },
            new List<Price>
            {
                new("USD", 119),
                new("BRL", 669),
            },
            PlanTypes.AddOns,
            null,
            V10,
            new List<string> { "Active" },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            "Tier3",
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new(
            new List<Multilingual>
            {
                new("en", "flow_builder_flow_enrolments_3000_pro_monthly", true),
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new(FeatureId.FlowBuilderFlowEnrolment, 3000),
            },
            new List<Price>
            {
                new("USD", 149),
                new("BRL", 839),
            },
            PlanTypes.AddOns,
            null,
            V10,
            new List<string> { "Active" },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            "Tier3",
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        #endregion
        #region V10 FlowEnrolments Tier3 Premium Monthly Plans
        new(
            new List<Multilingual>
            {
                new("en", "flow_builder_flow_enrolments_5000_premium_monthly", true),
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new(FeatureId.FlowBuilderFlowEnrolment, 5000),
            },
            new List<Price>
            {
                new("USD", 99),
                new("BRL", 559),
            },
            PlanTypes.AddOns,
            null,
            V10,
            new List<string> { "Active" },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            "Tier3",
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new(
            new List<Multilingual>
            {
                new("en", "flow_builder_flow_enrolments_7000_premium_monthly", true),
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new(FeatureId.FlowBuilderFlowEnrolment, 7000),
            },
            new List<Price>
            {
                new("USD", 199),
                new("BRL", 1119),
            },
            PlanTypes.AddOns,
            null,
            V10,
            new List<string> { "Active" },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            "Tier3",
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new(
            new List<Multilingual>
            {
                new("en", "flow_builder_flow_enrolments_10000_premium_monthly", true),
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new(FeatureId.FlowBuilderFlowEnrolment, 10000),
            },
            new List<Price>
            {
                new("USD", 349),
                new("BRL", 1959),
            },
            PlanTypes.AddOns,
            null,
            V10,
            new List<string> { "Active" },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            "Tier3",
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        #endregion
        #region V10 FlowEnrolments Tier4 Pro Monthly Plans
        new(
            new List<Multilingual>
            {
                new("en", "flow_builder_flow_enrolments_1500_pro_monthly", true),
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new(FeatureId.FlowBuilderFlowEnrolment, 1500),
            },
            new List<Price>
            {
                new("USD", 59),
                new("MYR", 279),
            },
            PlanTypes.AddOns,
            null,
            V10,
            new List<string> { "Active" },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            "Tier4",
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new(
            new List<Multilingual>
            {
                new("en", "flow_builder_flow_enrolments_2500_pro_monthly", true),
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new(FeatureId.FlowBuilderFlowEnrolment, 2500),
            },
            new List<Price>
            {
                new("USD", 119),
                new("MYR", 549),
            },
            PlanTypes.AddOns,
            null,
            V10,
            new List<string> { "Active" },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            "Tier4",
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new(
            new List<Multilingual>
            {
                new("en", "flow_builder_flow_enrolments_3000_pro_monthly", true),
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new(FeatureId.FlowBuilderFlowEnrolment, 3000),
            },
            new List<Price>
            {
                new("USD", 149),
                new("MYR", 689),
            },
            PlanTypes.AddOns,
            null,
            V10,
            new List<string> { "Active" },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            "Tier4",
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        #endregion
        #region V10 FlowEnrolments Tier4 Premium Monthly Plans
        new(
            new List<Multilingual>
            {
                new("en", "flow_builder_flow_enrolments_5000_premium_monthly", true),
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new(FeatureId.FlowBuilderFlowEnrolment, 5000),
            },
            new List<Price>
            {
                new("USD", 99),
                new("MYR", 459),
            },
            PlanTypes.AddOns,
            null,
            V10,
            new List<string> { "Active" },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            "Tier4",
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new(
            new List<Multilingual>
            {
                new("en", "flow_builder_flow_enrolments_7000_premium_monthly", true),
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new(FeatureId.FlowBuilderFlowEnrolment, 7000),
            },
            new List<Price>
            {
                new("USD", 199),
                new("MYR", 919),
            },
            PlanTypes.AddOns,
            null,
            V10,
            new List<string> { "Active" },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            "Tier4",
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new(
            new List<Multilingual>
            {
                new("en", "flow_builder_flow_enrolments_10000_premium_monthly", true),
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new(FeatureId.FlowBuilderFlowEnrolment, 10000),
            },
            new List<Price>
            {
                new("USD", 349),
                new("MYR", 1609),
            },
            PlanTypes.AddOns,
            null,
            V10,
            new List<string> { "Active" },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            "Tier4",
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        #endregion
        #region V10 FlowEnrolments Tier5 Pro Monthly Plans
        new(
            new List<Multilingual>
            {
                new("en", "flow_builder_flow_enrolments_1500_pro_monthly", true),
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new(FeatureId.FlowBuilderFlowEnrolment, 1500),
            },
            new List<Price>
            {
                new("USD", 59),
                new("IDR", 963000),
                new("INR", 4939),
            },
            PlanTypes.AddOns,
            null,
            V10,
            new List<string> { "Active" },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            "Tier5",
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new(
            new List<Multilingual>
            {
                new("en", "flow_builder_flow_enrolments_2500_pro_monthly", true),
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new(FeatureId.FlowBuilderFlowEnrolment, 2500),
            },
            new List<Price>
            {
                new("USD", 119),
                new("IDR", 1942000),
                new("INR", 9969),
            },
            PlanTypes.AddOns,
            null,
            V10,
            new List<string> { "Active" },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            "Tier5",
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new(
            new List<Multilingual>
            {
                new("en", "flow_builder_flow_enrolments_3000_pro_monthly", true),
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new(FeatureId.FlowBuilderFlowEnrolment, 3000),
            },
            new List<Price>
            {
                new("USD", 149),
                new("IDR", 2431000),
                new("INR", 12479),
            },
            PlanTypes.AddOns,
            null,
            V10,
            new List<string> { "Active" },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            "Tier5",
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        #endregion
        #region V10 FlowEnrolments Tier5 Premium Monthly Plans
        new(
            new List<Multilingual>
            {
                new("en", "flow_builder_flow_enrolments_5000_premium_monthly", true),
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new(FeatureId.FlowBuilderFlowEnrolment, 5000),
            },
            new List<Price>
            {
                new("USD", 99),
                new("IDR", 1615000),
                new("INR", 8289),
            },
            PlanTypes.AddOns,
            null,
            V10,
            new List<string> { "Active" },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            "Tier5",
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new(
            new List<Multilingual>
            {
                new("en", "flow_builder_flow_enrolments_7000_premium_monthly", true),
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new(FeatureId.FlowBuilderFlowEnrolment, 7000),
            },
            new List<Price>
            {
                new("USD", 199),
                new("IDR", 3247000),
                new("INR", 16669),
            },
            PlanTypes.AddOns,
            null,
            V10,
            new List<string> { "Active" },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            "Tier5",
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new(
            new List<Multilingual>
            {
                new("en", "flow_builder_flow_enrolments_10000_premium_monthly", true),
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new(FeatureId.FlowBuilderFlowEnrolment, 10000),
            },
            new List<Price>
            {
                new("USD", 349),
                new("IDR", 5694000),
                new("INR", 29219),
            },
            PlanTypes.AddOns,
            null,
            V10,
            new List<string> { "Active" },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            "Tier5",
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        #endregion
        #region V10 FlowEnrolments Tier1 Pro Yearly Plans
        new(
            new List<Multilingual>
            {
                new("en", "flow_builder_flow_enrolments_1500_pro_yearly", true),
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new(FeatureId.FlowBuilderFlowEnrolment, 1500),
            },
            new List<Price>
            {
                new("USD", 708),
                new("HKD", 5628),
                new("CNY", 5148),
                new("EUR", 708),
                new("GBP", 588),
                new("CAD", 1068),
                new("AUD", 1188),
                new("AED", 2628),
            },
            PlanTypes.AddOns,
            null,
            V10,
            new List<string> { "Active" },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            "Tier1",
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new(
            new List<Multilingual>
            {
                new("en", "flow_builder_flow_enrolments_2500_pro_yearly", true),
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new(FeatureId.FlowBuilderFlowEnrolment, 2500),
            },
            new List<Price>
            {
                new("USD", 1428),
                new("HKD", 11148),
                new("CNY", 10428),
                new("EUR", 1308),
                new("GBP", 1188),
                new("CAD", 2028),
                new("AUD", 2268),
                new("AED", 5268),
            },
            PlanTypes.AddOns,
            null,
            V10,
            new List<string> { "Active" },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            "Tier1",
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new(
            new List<Multilingual>
            {
                new("en", "flow_builder_flow_enrolments_3000_pro_yearly", true),
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new(FeatureId.FlowBuilderFlowEnrolment, 3000),
            },
            new List<Price>
            {
                new("USD", 1788),
                new("HKD", 14028),
                new("CNY", 12948),
                new("EUR", 1668),
                new("GBP", 1428),
                new("CAD", 2508),
                new("AUD", 2748),
                new("AED", 6588),
            },
            PlanTypes.AddOns,
            null,
            V10,
            new List<string> { "Active" },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            "Tier1",
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        #endregion
        #region V10 FlowEnrolments Tier1 Premium Yearly Plans
        new(
            new List<Multilingual>
            {
                new("en", "flow_builder_flow_enrolments_5000_premium_yearly", true),
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new(FeatureId.FlowBuilderFlowEnrolment, 5000),
            },
            new List<Price>
            {
                new("USD", 1188),
                new("HKD", 9348),
                new("CNY", 8628),
                new("EUR", 1188),
                new("GBP", 948),
                new("CAD", 1668),
                new("AUD", 1908),
                new("AED", 4428),
            },
            PlanTypes.AddOns,
            null,
            V10,
            new List<string> { "Active" },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            "Tier1",
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new(
            new List<Multilingual>
            {
                new("en", "flow_builder_flow_enrolments_7000_premium_yearly", true),
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new(FeatureId.FlowBuilderFlowEnrolment, 7000),
            },
            new List<Price>
            {
                new("USD", 2388),
                new("HKD", 18708),
                new("CNY", 17388),
                new("EUR", 2268),
                new("GBP", 1908),
                new("CAD", 3348),
                new("AUD", 3708),
                new("AED", 8868),
            },
            PlanTypes.AddOns,
            null,
            V10,
            new List<string> { "Active" },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            "Tier1",
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new(
            new List<Multilingual>
            {
                new("en", "flow_builder_flow_enrolments_10000_premium_yearly", true),
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new(FeatureId.FlowBuilderFlowEnrolment, 10000),
            },
            new List<Price>
            {
                new("USD", 4188),
                new("HKD", 32748),
                new("CNY", 30348),
                new("EUR", 3948),
                new("GBP", 3228),
                new("CAD", 5868),
                new("AUD", 6468),
                new("AED", 15468),
            },
            PlanTypes.AddOns,
            null,
            V10,
            new List<string> { "Active" },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            "Tier1",
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        #endregion
        #region V10 FlowEnrolments Tier1 Enterprise Yearly Plans
        new(
            new List<Multilingual>
            {
                new("en", "flow_builder_flow_enrolments_15000_enterprise_yearly", true),
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new(FeatureId.FlowBuilderFlowEnrolment, 15000),
            },
            new List<Price>
            {
                new("USD", 2388),
                new("HKD", 18708),
                new("CNY", 17388),
                new("EUR", 2268),
                new("GBP", 1908),
                new("CAD", 3348),
                new("AUD", 3708),
                new("AED", 8868),
            },
            PlanTypes.AddOns,
            null,
            V10,
            new List<string> { "Active" },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            "Tier1",
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new(
            new List<Multilingual>
            {
                new("en", "flow_builder_flow_enrolments_20000_enterprise_yearly", true),
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new(FeatureId.FlowBuilderFlowEnrolment, 20000),
            },
            new List<Price>
            {
                new("USD", 4788),
                new("HKD", 37428),
                new("CNY", 34668),
                new("EUR", 4428),
                new("GBP", 3708),
                new("CAD", 6708),
                new("AUD", 7308),
                new("AED", 17628),
            },
            PlanTypes.AddOns,
            null,
            V10,
            new List<string> { "Active" },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            "Tier1",
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new(
            new List<Multilingual>
            {
                new("en", "flow_builder_flow_enrolments_25000_enterprise_yearly", true),
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new(FeatureId.FlowBuilderFlowEnrolment, 25000),
            },
            new List<Price>
            {
                new("USD", 7188),
                new("HKD", 56148),
                new("CNY", 52068),
                new("EUR", 6708),
                new("GBP", 5628),
                new("CAD", 9948),
                new("AUD", 11028),
                new("AED", 26388),
            },
            PlanTypes.AddOns,
            null,
            V10,
            new List<string> { "Active" },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            "Tier1",
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new(
            new List<Multilingual>
            {
                new("en", "flow_builder_flow_enrolments_30000_enterprise_yearly", true),
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new(FeatureId.FlowBuilderFlowEnrolment, 30000),
            },
            new List<Price>
            {
                new("USD", 8388),
                new("HKD", 65508),
                new("CNY", 60828),
                new("EUR", 7788),
                new("GBP", 6468),
                new("CAD", 11628),
                new("AUD", 12828),
                new("AED", 30828),
            },
            PlanTypes.AddOns,
            null,
            V10,
            new List<string> { "Active" },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            "Tier1",
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new(
            new List<Multilingual>
            {
                new("en", "flow_builder_flow_enrolments_35000_enterprise_yearly", true),
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new(FeatureId.FlowBuilderFlowEnrolment, 35000),
            },
            new List<Price>
            {
                new("USD", 9588),
                new("HKD", 74988),
                new("CNY", 69468),
                new("EUR", 8868),
                new("GBP", 7428),
                new("CAD", 13308),
                new("AUD", 14628),
                new("AED", 35268),
            },
            PlanTypes.AddOns,
            null,
            V10,
            new List<string> { "Active" },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            "Tier1",
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new(
            new List<Multilingual>
            {
                new("en", "flow_builder_flow_enrolments_40000_enterprise_yearly", true),
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new(FeatureId.FlowBuilderFlowEnrolment, 40000),
            },
            new List<Price>
            {
                new("USD", 10788),
                new("HKD", 84348),
                new("CNY", 78108),
                new("EUR", 9948),
                new("GBP", 8388),
                new("CAD", 14988),
                new("AUD", 16428),
                new("AED", 39588),
            },
            PlanTypes.AddOns,
            null,
            V10,
            new List<string> { "Active" },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            "Tier1",
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        #endregion
        #region V10 FlowEnrolments Tier2 Pro Yearly Plans
        new(
            new List<Multilingual>
            {
                new("en", "flow_builder_flow_enrolments_1500_pro_yearly", true),
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new(FeatureId.FlowBuilderFlowEnrolment, 1500),
            },
            new List<Price>
            {
                new("USD", 708),
                new("SGD", 948),
                new("EUR", 708),
            },
            PlanTypes.AddOns,
            null,
            V10,
            new List<string> { "Active" },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            "Tier2",
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new(
            new List<Multilingual>
            {
                new("en", "flow_builder_flow_enrolments_2500_pro_yearly", true),
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new(FeatureId.FlowBuilderFlowEnrolment, 2500),
            },
            new List<Price>
            {
                new("USD", 1428),
                new("SGD", 1908),
                new("EUR", 1308),
            },
            PlanTypes.AddOns,
            null,
            V10,
            new List<string> { "Active" },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            "Tier2",
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new(
            new List<Multilingual>
            {
                new("en", "flow_builder_flow_enrolments_3000_pro_yearly", true),
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new(FeatureId.FlowBuilderFlowEnrolment, 3000),
            },
            new List<Price>
            {
                new("USD", 1788),
                new("SGD", 2508),
                new("EUR", 1668),
            },
            PlanTypes.AddOns,
            null,
            V10,
            new List<string> { "Active" },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            "Tier2",
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        #endregion
        #region V10 FlowEnrolments Tier2 Premium Yearly Plans
        new(
            new List<Multilingual>
            {
                new("en", "flow_builder_flow_enrolments_5000_premium_yearly", true),
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new(FeatureId.FlowBuilderFlowEnrolment, 5000),
            },
            new List<Price>
            {
                new("USD", 1188),
                new("SGD", 1668),
                new("EUR", 1188),
            },
            PlanTypes.AddOns,
            null,
            V10,
            new List<string> { "Active" },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            "Tier2",
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new(
            new List<Multilingual>
            {
                new("en", "flow_builder_flow_enrolments_7000_premium_yearly", true),
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new(FeatureId.FlowBuilderFlowEnrolment, 7000),
            },
            new List<Price>
            {
                new("USD", 2388),
                new("SGD", 3228),
                new("EUR", 2268),
            },
            PlanTypes.AddOns,
            null,
            V10,
            new List<string> { "Active" },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            "Tier2",
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new(
            new List<Multilingual>
            {
                new("en", "flow_builder_flow_enrolments_10000_premium_yearly", true),
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new(FeatureId.FlowBuilderFlowEnrolment, 10000),
            },
            new List<Price>
            {
                new("USD", 4188),
                new("SGD", 5628),
                new("EUR", 3948),
            },
            PlanTypes.AddOns,
            null,
            V10,
            new List<string> { "Active" },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            "Tier2",
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        #endregion
        #region V10 FlowEnrolments Tier2 Enterprise Yearly Plans
        new(
            new List<Multilingual>
            {
                new("en", "flow_builder_flow_enrolments_15000_enterprise_yearly", true),
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new(FeatureId.FlowBuilderFlowEnrolment, 15000),
            },
            new List<Price>
            {
                new("USD", 2388),
                new("SGD", 3228),
                new("EUR", 2268),
            },
            PlanTypes.AddOns,
            null,
            V10,
            new List<string> { "Active" },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            "Tier2",
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new(
            new List<Multilingual>
            {
                new("en", "flow_builder_flow_enrolments_20000_enterprise_yearly", true),
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new(FeatureId.FlowBuilderFlowEnrolment, 20000),
            },
            new List<Price>
            {
                new("USD", 4788),
                new("SGD", 6468),
                new("EUR", 4428),
            },
            PlanTypes.AddOns,
            null,
            V10,
            new List<string> { "Active" },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            "Tier2",
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new(
            new List<Multilingual>
            {
                new("en", "flow_builder_flow_enrolments_25000_enterprise_yearly", true),
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new(FeatureId.FlowBuilderFlowEnrolment, 25000),
            },
            new List<Price>
            {
                new("USD", 7188),
                new("SGD", 9708),
                new("EUR", 6708),
            },
            PlanTypes.AddOns,
            null,
            V10,
            new List<string> { "Active" },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            "Tier2",
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new(
            new List<Multilingual>
            {
                new("en", "flow_builder_flow_enrolments_30000_enterprise_yearly", true),
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new(FeatureId.FlowBuilderFlowEnrolment, 30000),
            },
            new List<Price>
            {
                new("USD", 8388),
                new("SGD", 11268),
                new("EUR", 7788),
            },
            PlanTypes.AddOns,
            null,
            V10,
            new List<string> { "Active" },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            "Tier2",
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new(
            new List<Multilingual>
            {
                new("en", "flow_builder_flow_enrolments_35000_enterprise_yearly", true),
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new(FeatureId.FlowBuilderFlowEnrolment, 35000),
            },
            new List<Price>
            {
                new("USD", 9588),
                new("SGD", 12948),
                new("EUR", 8868),
            },
            PlanTypes.AddOns,
            null,
            V10,
            new List<string> { "Active" },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            "Tier2",
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new(
            new List<Multilingual>
            {
                new("en", "flow_builder_flow_enrolments_40000_enterprise_yearly", true),
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new(FeatureId.FlowBuilderFlowEnrolment, 40000),
            },
            new List<Price>
            {
                new("USD", 10788),
                new("SGD", 14508),
                new("EUR", 9948),
            },
            PlanTypes.AddOns,
            null,
            V10,
            new List<string> { "Active" },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            "Tier2",
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        #endregion
        #region V10 FlowEnrolments Tier3 Pro Yearly Plans
        new(
            new List<Multilingual>
            {
                new("en", "flow_builder_flow_enrolments_1500_pro_yearly", true),
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new(FeatureId.FlowBuilderFlowEnrolment, 1500),
            },
            new List<Price>
            {
                new("USD", 708),
                new("BRL", 4068),
            },
            PlanTypes.AddOns,
            null,
            V10,
            new List<string> { "Active" },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            "Tier3",
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new(
            new List<Multilingual>
            {
                new("en", "flow_builder_flow_enrolments_2500_pro_yearly", true),
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new(FeatureId.FlowBuilderFlowEnrolment, 2500),
            },
            new List<Price>
            {
                new("USD", 1428),
                new("BRL", 8028),
            },
            PlanTypes.AddOns,
            null,
            V10,
            new List<string> { "Active" },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            "Tier3",
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new(
            new List<Multilingual>
            {
                new("en", "flow_builder_flow_enrolments_3000_pro_yearly", true),
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new(FeatureId.FlowBuilderFlowEnrolment, 3000),
            },
            new List<Price>
            {
                new("USD", 1788),
                new("BRL", 10068),
            },
            PlanTypes.AddOns,
            null,
            V10,
            new List<string> { "Active" },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            "Tier3",
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        #endregion
        #region V10 FlowEnrolments Tier3 Premium Yearly Plans
        new(
            new List<Multilingual>
            {
                new("en", "flow_builder_flow_enrolments_5000_premium_yearly", true),
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new(FeatureId.FlowBuilderFlowEnrolment, 5000),
            },
            new List<Price>
            {
                new("USD", 1188),
                new("BRL", 6708),
            },
            PlanTypes.AddOns,
            null,
            V10,
            new List<string> { "Active" },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            "Tier3",
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new(
            new List<Multilingual>
            {
                new("en", "flow_builder_flow_enrolments_7000_premium_yearly", true),
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new(FeatureId.FlowBuilderFlowEnrolment, 7000),
            },
            new List<Price>
            {
                new("USD", 2388),
                new("BRL", 13428),
            },
            PlanTypes.AddOns,
            null,
            V10,
            new List<string> { "Active" },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            "Tier3",
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new(
            new List<Multilingual>
            {
                new("en", "flow_builder_flow_enrolments_10000_premium_yearly", true),
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new(FeatureId.FlowBuilderFlowEnrolment, 10000),
            },
            new List<Price>
            {
                new("USD", 4188),
                new("BRL", 23508),
            },
            PlanTypes.AddOns,
            null,
            V10,
            new List<string> { "Active" },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            "Tier3",
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        #endregion
        #region V10 FlowEnrolments Tier3 Enterprise Yearly Plans
        new(
            new List<Multilingual>
            {
                new("en", "flow_builder_flow_enrolments_15000_enterprise_yearly", true),
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new(FeatureId.FlowBuilderFlowEnrolment, 15000),
            },
            new List<Price>
            {
                new("USD", 2388),
                new("BRL", 13428),
            },
            PlanTypes.AddOns,
            null,
            V10,
            new List<string> { "Active" },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            "Tier3",
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new(
            new List<Multilingual>
            {
                new("en", "flow_builder_flow_enrolments_20000_enterprise_yearly", true),
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new(FeatureId.FlowBuilderFlowEnrolment, 20000),
            },
            new List<Price>
            {
                new("USD", 4788),
                new("BRL", 26868),
            },
            PlanTypes.AddOns,
            null,
            V10,
            new List<string> { "Active" },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            "Tier3",
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new(
            new List<Multilingual>
            {
                new("en", "flow_builder_flow_enrolments_25000_enterprise_yearly", true),
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new(FeatureId.FlowBuilderFlowEnrolment, 25000),
            },
            new List<Price>
            {
                new("USD", 7188),
                new("BRL", 40428),
            },
            PlanTypes.AddOns,
            null,
            V10,
            new List<string> { "Active" },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            "Tier3",
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new(
            new List<Multilingual>
            {
                new("en", "flow_builder_flow_enrolments_30000_enterprise_yearly", true),
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new(FeatureId.FlowBuilderFlowEnrolment, 30000),
            },
            new List<Price>
            {
                new("USD", 8388),
                new("BRL", 47148),
            },
            PlanTypes.AddOns,
            null,
            V10,
            new List<string> { "Active" },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            "Tier3",
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new(
            new List<Multilingual>
            {
                new("en", "flow_builder_flow_enrolments_35000_enterprise_yearly", true),
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new(FeatureId.FlowBuilderFlowEnrolment, 35000),
            },
            new List<Price>
            {
                new("USD", 9588),
                new("BRL", 53868),
            },
            PlanTypes.AddOns,
            null,
            V10,
            new List<string> { "Active" },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            "Tier3",
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new(
            new List<Multilingual>
            {
                new("en", "flow_builder_flow_enrolments_40000_enterprise_yearly", true),
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new(FeatureId.FlowBuilderFlowEnrolment, 40000),
            },
            new List<Price>
            {
                new("USD", 10788),
                new("BRL", 60588),
            },
            PlanTypes.AddOns,
            null,
            V10,
            new List<string> { "Active" },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            "Tier3",
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        #endregion
        #region V10 FlowEnrolments Tier4 Pro Yearly Plans
        new(
            new List<Multilingual>
            {
                new("en", "flow_builder_flow_enrolments_1500_pro_yearly", true),
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new(FeatureId.FlowBuilderFlowEnrolment, 1500),
            },
            new List<Price>
            {
                new("USD", 708),
                new("MYR", 3348),
            },
            PlanTypes.AddOns,
            null,
            V10,
            new List<string> { "Active" },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            "Tier4",
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new(
            new List<Multilingual>
            {
                new("en", "flow_builder_flow_enrolments_2500_pro_yearly", true),
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new(FeatureId.FlowBuilderFlowEnrolment, 2500),
            },
            new List<Price>
            {
                new("USD", 1428),
                new("MYR", 6588),
            },
            PlanTypes.AddOns,
            null,
            V10,
            new List<string> { "Active" },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            "Tier4",
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new(
            new List<Multilingual>
            {
                new("en", "flow_builder_flow_enrolments_3000_pro_yearly", true),
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new(FeatureId.FlowBuilderFlowEnrolment, 3000),
            },
            new List<Price>
            {
                new("USD", 1788),
                new("MYR", 8268),
            },
            PlanTypes.AddOns,
            null,
            V10,
            new List<string> { "Active" },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            "Tier4",
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        #endregion
        #region V10 FlowEnrolments Tier4 Premium Yearly Plans
        new(
            new List<Multilingual>
            {
                new("en", "flow_builder_flow_enrolments_5000_premium_yearly", true),
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new(FeatureId.FlowBuilderFlowEnrolment, 5000),
            },
            new List<Price>
            {
                new("USD", 1188),
                new("MYR", 5508),
            },
            PlanTypes.AddOns,
            null,
            V10,
            new List<string> { "Active" },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            "Tier4",
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new(
            new List<Multilingual>
            {
                new("en", "flow_builder_flow_enrolments_7000_premium_yearly", true),
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new(FeatureId.FlowBuilderFlowEnrolment, 7000),
            },
            new List<Price>
            {
                new("USD", 2388),
                new("MYR", 11028),
            },
            PlanTypes.AddOns,
            null,
            V10,
            new List<string> { "Active" },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            "Tier4",
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new(
            new List<Multilingual>
            {
                new("en", "flow_builder_flow_enrolments_10000_premium_yearly", true),
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new(FeatureId.FlowBuilderFlowEnrolment, 10000),
            },
            new List<Price>
            {
                new("USD", 4188),
                new("MYR", 19308),
            },
            PlanTypes.AddOns,
            null,
            V10,
            new List<string> { "Active" },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            "Tier4",
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        #endregion
        #region V10 FlowEnrolments Tier4 Enterprise Yearly Plans
        new(
            new List<Multilingual>
            {
                new("en", "flow_builder_flow_enrolments_15000_enterprise_yearly", true),
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new(FeatureId.FlowBuilderFlowEnrolment, 15000),
            },
            new List<Price>
            {
                new("USD", 2388),
                new("MYR", 11028),
            },
            PlanTypes.AddOns,
            null,
            V10,
            new List<string> { "Active" },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            "Tier4",
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new(
            new List<Multilingual>
            {
                new("en", "flow_builder_flow_enrolments_20000_enterprise_yearly", true),
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new(FeatureId.FlowBuilderFlowEnrolment, 20000),
            },
            new List<Price>
            {
                new("USD", 4788),
                new("MYR", 22068),
            },
            PlanTypes.AddOns,
            null,
            V10,
            new List<string> { "Active" },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            "Tier4",
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new(
            new List<Multilingual>
            {
                new("en", "flow_builder_flow_enrolments_25000_enterprise_yearly", true),
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new(FeatureId.FlowBuilderFlowEnrolment, 25000),
            },
            new List<Price>
            {
                new("USD", 7188),
                new("MYR", 33228),
            },
            PlanTypes.AddOns,
            null,
            V10,
            new List<string> { "Active" },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            "Tier4",
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new(
            new List<Multilingual>
            {
                new("en", "flow_builder_flow_enrolments_30000_enterprise_yearly", true),
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new(FeatureId.FlowBuilderFlowEnrolment, 30000),
            },
            new List<Price>
            {
                new("USD", 8388),
                new("MYR", 38748),
            },
            PlanTypes.AddOns,
            null,
            V10,
            new List<string> { "Active" },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            "Tier4",
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new(
            new List<Multilingual>
            {
                new("en", "flow_builder_flow_enrolments_35000_enterprise_yearly", true),
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new(FeatureId.FlowBuilderFlowEnrolment, 35000),
            },
            new List<Price>
            {
                new("USD", 9588),
                new("MYR", 44268),
            },
            PlanTypes.AddOns,
            null,
            V10,
            new List<string> { "Active" },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            "Tier4",
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new(
            new List<Multilingual>
            {
                new("en", "flow_builder_flow_enrolments_40000_enterprise_yearly", true),
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new(FeatureId.FlowBuilderFlowEnrolment, 40000),
            },
            new List<Price>
            {
                new("USD", 10788),
                new("MYR", 49788),
            },
            PlanTypes.AddOns,
            null,
            V10,
            new List<string> { "Active" },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            "Tier4",
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        #endregion
        #region V10 FlowEnrolments Tier5 Pro Yearly Plans
        new(
            new List<Multilingual>
            {
                new("en", "flow_builder_flow_enrolments_1500_pro_yearly", true),
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new(FeatureId.FlowBuilderFlowEnrolment, 1500),
            },
            new List<Price>
            {
                new("USD", 708),
                new("IDR", 11556000),
                new("INR", 59268),
            },
            PlanTypes.AddOns,
            null,
            V10,
            new List<string> { "Active" },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            "Tier5",
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new(
            new List<Multilingual>
            {
                new("en", "flow_builder_flow_enrolments_2500_pro_yearly", true),
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new(FeatureId.FlowBuilderFlowEnrolment, 2500),
            },
            new List<Price>
            {
                new("USD", 1428),
                new("IDR", 23304000),
                new("INR", 119628),
            },
            PlanTypes.AddOns,
            null,
            V10,
            new List<string> { "Active" },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            "Tier5",
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new(
            new List<Multilingual>
            {
                new("en", "flow_builder_flow_enrolments_3000_pro_yearly", true),
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new(FeatureId.FlowBuilderFlowEnrolment, 3000),
            },
            new List<Price>
            {
                new("USD", 1788),
                new("IDR", 29172000),
                new("INR", 149748),
            },
            PlanTypes.AddOns,
            null,
            V10,
            new List<string> { "Active" },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            "Tier5",
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        #endregion
        #region V10 FlowEnrolments Tier5 Premium Yearly Plans
        new(
            new List<Multilingual>
            {
                new("en", "flow_builder_flow_enrolments_5000_premium_yearly", true),
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new(FeatureId.FlowBuilderFlowEnrolment, 5000),
            },
            new List<Price>
            {
                new("USD", 1188),
                new("IDR", 19380000),
                new("INR", 99468),
            },
            PlanTypes.AddOns,
            null,
            V10,
            new List<string> { "Active" },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            "Tier5",
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new(
            new List<Multilingual>
            {
                new("en", "flow_builder_flow_enrolments_7000_premium_yearly", true),
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new(FeatureId.FlowBuilderFlowEnrolment, 7000),
            },
            new List<Price>
            {
                new("USD", 2388),
                new("IDR", 38964000),
                new("INR", 200028),
            },
            PlanTypes.AddOns,
            null,
            V10,
            new List<string> { "Active" },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            "Tier5",
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new(
            new List<Multilingual>
            {
                new("en", "flow_builder_flow_enrolments_10000_premium_yearly", true),
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new(FeatureId.FlowBuilderFlowEnrolment, 10000),
            },
            new List<Price>
            {
                new("USD", 4188),
                new("IDR", 68328000),
                new("INR", 350628),
            },
            PlanTypes.AddOns,
            null,
            V10,
            new List<string> { "Active" },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            "Tier5",
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        #endregion
        #region V10 FlowEnrolments Tier5 Enterprise Yearly Plans
        new(
            new List<Multilingual>
            {
                new("en", "flow_builder_flow_enrolments_15000_enterprise_yearly", true),
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new(FeatureId.FlowBuilderFlowEnrolment, 15000),
            },
            new List<Price>
            {
                new("USD", 2388),
                new("IDR", 38964000),
                new("INR", 200028),
            },
            PlanTypes.AddOns,
            null,
            V10,
            new List<string> { "Active" },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            "Tier5",
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new(
            new List<Multilingual>
            {
                new("en", "flow_builder_flow_enrolments_20000_enterprise_yearly", true),
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new(FeatureId.FlowBuilderFlowEnrolment, 20000),
            },
            new List<Price>
            {
                new("USD", 4788),
                new("IDR", 78120000),
                new("INR", 400908),
            },
            PlanTypes.AddOns,
            null,
            V10,
            new List<string> { "Active" },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            "Tier5",
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new(
            new List<Multilingual>
            {
                new("en", "flow_builder_flow_enrolments_25000_enterprise_yearly", true),
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new(FeatureId.FlowBuilderFlowEnrolment, 25000),
            },
            new List<Price>
            {
                new("USD", 7188),
                new("IDR", 117276000),
                new("INR", 601788),
            },
            PlanTypes.AddOns,
            null,
            V10,
            new List<string> { "Active" },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            "Tier5",
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new(
            new List<Multilingual>
            {
                new("en", "flow_builder_flow_enrolments_30000_enterprise_yearly", true),
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new(FeatureId.FlowBuilderFlowEnrolment, 30000),
            },
            new List<Price>
            {
                new("USD", 8388),
                new("IDR", 136860000),
                new("INR", 702348),
            },
            PlanTypes.AddOns,
            null,
            V10,
            new List<string> { "Active" },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            "Tier5",
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new(
            new List<Multilingual>
            {
                new("en", "flow_builder_flow_enrolments_35000_enterprise_yearly", true),
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new(FeatureId.FlowBuilderFlowEnrolment, 35000),
            },
            new List<Price>
            {
                new("USD", 9588),
                new("IDR", 156432000),
                new("INR", 802788),
            },
            PlanTypes.AddOns,
            null,
            V10,
            new List<string> { "Active" },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            "Tier5",
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new(
            new List<Multilingual>
            {
                new("en", "flow_builder_flow_enrolments_40000_enterprise_yearly", true),
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new(FeatureId.FlowBuilderFlowEnrolment, 40000),
            },
            new List<Price>
            {
                new("USD", 10788),
                new("IDR", 176016000),
                new("INR", 903228),
            },
            PlanTypes.AddOns,
            null,
            V10,
            new List<string> { "Active" },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            "Tier5",
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        #endregion
    };
}