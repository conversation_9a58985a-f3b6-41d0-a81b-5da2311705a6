﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Net.Http.Formatting;
using System.Net.Mime;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using AutoMapper;
using AutoMapper.QueryableExtensions;
using Hangfire;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;
using Polly;
using Serilog.Context;
using Sleekflow.Apis.AuditHub.Model;
using Sleekflow.Apis.CrmHub.Api;
using Sleekflow.Apis.CrmHub.Model;
using Travis_backend.AutomationDomain.Models;
using Travis_backend.Cache;
using Travis_backend.ClientCustomDomain.Constant;
using Travis_backend.ClientCustomDomain.Services;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.Constants;
using Travis_backend.ContactDomain.Constants;
using Travis_backend.ContactDomain.Models;
using Travis_backend.ContactDomain.Services;
using Travis_backend.ContactDomain.ViewModels;
using Travis_backend.Controllers.SleekflowControllers;
using Travis_backend.ConversationDomain.Models;
using Travis_backend.ConversationDomain.Services;
using Travis_backend.ConversationDomain.ViewModels;
using Travis_backend.Database;
using Travis_backend.Enums;
using Travis_backend.ConversationServices.ViewModels;
using Travis_backend.Extensions;
using Travis_backend.FileDomain.Models;
using Travis_backend.MessageDomain.Models;
using Travis_backend.MessageDomain.ViewModels;
using Travis_backend.OpenTelemetry.Meters;
using Travis_backend.SignalR;
using static Travis_backend.ContactDomain.Services.UserProfileService;
using PlatformType = Travis_backend.AutomationDomain.Models.PlatformType;

namespace Travis_backend.ConversationServices
{
    public interface IAutomationService
    {
        // Scheduled automation
        Task ScheduledAutomation(string automationRuleId);

        // Triggers
        Task AddToListTrigger(List<ImportedUserProfileForAutomationTrigger> importedUserProfile);

        Task CustomFieldsChangedTrigger(string userProfileId, string fieldName, string value);

        Task CustomFieldsBulkChangedTrigger(
            string userProfileId,
            ImportUserProfileObject importUserProfileObject,
            List<string> fields);

        Task CustomFieldsBulkChangedTrigger(
            string userProfileId,
            ImportUserProfileObject importUserProfileObject,
            List<string> fields,
            AutomationType? automationType = AutomationType.FieldValueChanged);

        Task NewContactMessageTrigger(string conversationId, long conversationMessageId, bool isSendMessage = true);

        Task NewMessageTrigger(string conversationId, long conversationMessageId, bool isSendMessage = true);

        Task NewContactTrigger(string userProfileId);

        Task NewContactTrigger(string userProfileId, AutomationType automationType);

        Task OutgoingMessageTrigger(string conversationId, long conversationMessageId, bool isSendMessage = true);

        Task ConversationAssignmentTrigger(string conversationId, long ConversationMessageId);

        Task FbCommentTrigger(Entry entry, Change change);

        Task IgCommentTrigger(Entry entry, Change change);

        Task FbIgPreviewCodeTrigger(FBMessaging messaging, Entry entry);

        Task FbIcebreakerTrigger(FBMessaging messaging);

        Task IgIcebreakerTrigger(FBMessaging messaging);

        Task<ResponseWrapper> ReplayFbIgDmAction(FbIgAutoReplyHistoryRecord trigger);

        Task TriggerAction(
            string userProfileId,
            string assignmentRuleId,
            bool isSendMessage = true,
            string regexValue = null,
            long? conversationMessageId = null,
            long? replayId = null);

        Task TriggerAutomationAction(
            string userProfileId,
            long assignmentId,
            long automationId,
            bool isSchedule = true,
            bool isSendMessage = true,
            string regexValue = null,
            long? conversationMessageId = null);

        Task TriggerAutomationAction(
            string userProfileId,
            long assignmentId,
            long automationId,
            bool isSchedule = true,
            bool isSendMessage = true,
            string regexValue = null,
            long? conversationMessageId = null,
            long? automationHistoryId = null);

        Task FbIgCommentTriggerAction(
            Entry entry,
            Change change,
            UserProfile userProfile,
            AssignmentRule automationRule,
            bool isNewContact = false,
            bool isSendMessage = false,
            long? replayId = null);

        Task IcebreakerTriggerAction(
            FBMessaging messaging,
            UserProfile userProfile,
            AssignmentRule automationRule,
            long[] automationActionIdList,
            bool isNewContact,
            bool isSendMessage = false,
            long? replayId = null);

        Task TriggerFbIgCommentAutomationAction(
            Entry entry,
            Change change,
            string userProfileId,
            long assignmentId,
            long automationId,
            bool isNewContact,
            bool isSchedule = true,
            bool isSendMessage = false,
            long? automationHistoryId = null);

        Task TriggerFbIgIcebreakerAutomationAction(
            FBMessaging messaging,
            string userProfileId,
            long assignmentId,
            long automationId,
            bool isNewContact,
            bool isSchedule = true,
            bool isSendMessage = false,
            long? automationHistoryId = null);

        Task HashtagChangedTrigger(string userProfileId, List<ConversationHashtagViewModel> conversationTagViewModels);

        Task AutomationAssignment(
            UserProfile userProfile,
            AutomationAction automationTriggerAction,
            bool isTriggerUpdate = true,
            string regexValue = null,
            int attempts = 3,
            string changedBy = null);

        Task ExecuteAutomationActions(
            string userProfileId,
            string triggeredSource,
            List<AutomationAction> automationActions);

        Task ExecuteAutomationActions(
            string userProfileId,
            string triggeredSource,
            List<AutomationAction> automationActions,
            long? conversationMessageId);

        Task ExecuteAutomationAction(
            string userProfileId,
            string triggeredSoruce,
            bool isSendMessage,
            string regexValue,
            long? conversationMessageId,
            AutomationAction? automationAction,
            UserProfile? userProfile,
            AutomationActionRecord automationRecord);
    }

    public class AutomationService : IAutomationService
    {
        private readonly ApplicationDbContext _appDbContext;
        private readonly IMapper _mapper;
        private readonly IConfiguration _configuration;
        private readonly ILogger _logger;
        private readonly IConversationMessageService _conversationMessageService;
        private readonly IUserProfileService _userProfileService;
        private readonly ISignalRService _signalRService;
        private readonly IEmailNotificationService _emailNotificationService;
        private readonly IAzureBlobStorageService _azureBlobStorageService;
        private readonly ILockService _lockService;
        private readonly IConversationHashtagService _conversationHashtagService;
        private readonly IConversationAssigneeService _conversationAssigneeService;
        private readonly IFacebookService _facebookService;
        private readonly IInstagramService _instagramService;
        private readonly IObjectsApi _objectsApi;
        private readonly IAuditHubAuditLogService _auditHubAuditLogService;
        private readonly ISuzukiService _suzukiService;
        private readonly ISuzukiUatService _suzukiUatService;
        private readonly IHttpClientFactory _httpClientFactory;
        private readonly IAutomationMeters _automationMeters;
        private readonly IServiceProvider _serviceProvider;
        private readonly IUserProfileSafeDeleteService _userProfileSafeDeleteService;

        public AutomationService(
            ApplicationDbContext appDbContext,
            IMapper mapper,
            IConfiguration configuration,
            ILogger<AutomationService> logger,
            IConversationMessageService conversationMessageService,
            IUserProfileService userProfileService,
            ISignalRService signalRService,
            IEmailNotificationService emailNotificationService,
            IAzureBlobStorageService azureBlobStorageService,
            ILockService lockService,
            IConversationHashtagService conversationHashtagService,
            IConversationAssigneeService conversationAssigneeService,
            IFacebookService facebookService,
            IInstagramService instagramService,
            IObjectsApi objectsApi,
            IAuditHubAuditLogService auditHubAuditLogService,
            ISuzukiService suzukiService,
            ISuzukiUatService suzukiUatService,
            IHttpClientFactory httpClientFactory,
            IAutomationMeters automationMeters,
            IServiceProvider serviceProvider,
            IUserProfileSafeDeleteService userProfileSafeDeleteService)
        {
            _appDbContext = appDbContext;
            _mapper = mapper;
            _configuration = configuration;
            _logger = logger;
            _conversationMessageService = conversationMessageService;
            _userProfileService = userProfileService;
            _signalRService = signalRService;
            _emailNotificationService = emailNotificationService;
            _azureBlobStorageService = azureBlobStorageService;
            _lockService = lockService;
            _conversationHashtagService = conversationHashtagService;
            _conversationAssigneeService = conversationAssigneeService;
            _facebookService = facebookService;
            _instagramService = instagramService;
            _objectsApi = objectsApi;
            _auditHubAuditLogService = auditHubAuditLogService;
            _suzukiService = suzukiService;
            _suzukiUatService = suzukiUatService;
            _httpClientFactory = httpClientFactory;
            _automationMeters = automationMeters;
            _serviceProvider = serviceProvider;
            _userProfileSafeDeleteService = userProfileSafeDeleteService;
        }

        public async Task ScheduledAutomation(string automationRuleId)
        {
            var automationRule = await _appDbContext.CompanyAssignmentRules
                .FirstOrDefaultAsync(
                    x =>
                        x.AssignmentId == automationRuleId
                        && x.Status == AutomationStatus.Live);

            if (automationRule == null || automationRule.AutomationActions?.Count == 0)
            {
                return;
            }

            // var company = await _appDbContext.CompanyCompanies.Where(x => x.Id == automationRule.CompanyId).FirstOrDefaultAsync();
            // var conversations = _appDbContext.Conversations.Where(x => x.CompanyId == automationRule.CompanyId &&
            // (
            //    (automationRule.TargetedChannels != null && automationRule.TargetedChannels.Count > 0) ? (x.ChatHistory.Where(y => automationRule.TargetedChannels.Contains(y.Channel)).Count() > 0) :
            //    (automationRule.TargetedChannelWithIds != null && automationRule.TargetedChannelWithIds.Count > 0) ? (x.ChatHistory.Where(y => automationRule.TargetedChannelWithIds.Select(y => y.channel).Contains(y.Channel)).Count() > 0) :
            //    x.CompanyId == automationRule.CompanyId)
            // )
            // .Include(x => x.UserProfile)
            // .Include(x => x.conversationHashtags).Include(x => x.NaiveUser).Include(x => x.facebookUser).Include(X => X.EmailAddress).Include(x => x.WhatsappUser).Include(x => x.WebClient).Include(x => x.WeChatUser).Include(x => x.LineUser).Include(x => x.SMSUser);

            // if (automationRule.TargetedChannelWithIds?.Count > 0)
            // {
            //     foreach (var channel in automationRule.TargetedChannelWithIds)
            //     {
            //         switch (channel.channel)
            //         {
            //             case "whatsapp":
            //                 if (channel.ids?.Count > 0)
            //                     conversations = conversations.Where(x => x.WhatsappUser != null && channel.ids.Contains(x.WhatsappUser.InstanceId)).Include(x => x.UserProfile).Include(x => x.NaiveUser).Include(x => x.facebookUser).Include(X => X.EmailAddress).Include(x => x.WhatsappUser).Include(x => x.WebClient).Include(x => x.WeChatUser).Include(x => x.LineUser).Include(x => x.SMSUser);
            //                 break;
            //         }
            //     }
            // }

            // List<Conversation> conversationList = null;
            // conversationList = conversations.ToList();
            if (automationRule.TargetedChannelWithIds?.Count > 0)
            {
                var lastChannel = automationRule.Conditions
                    .FirstOrDefault(x => x.FieldName == "LastChannel");

                foreach (var channel in automationRule.TargetedChannelWithIds)
                {
                    foreach (var id in channel.ids)
                    {
                        lastChannel?.Values.Add($"{channel.channel}:{id}");
                    }
                }
            }

            if (automationRule.Conditions != null)
            {
                SearchUserProfileResult selectedUserProfiles;
                try
                {
                    selectedUserProfiles =
                        await _userProfileService.GetUserProfilesByFields(
                            automationRule.CompanyId,
                            automationRule.Conditions);

                    _logger.LogInformation(
                        "[{MethodName}] Get user profiles: conditions=[{Conditions}], companyId=[{CompanyId}], automationRuleId=[{AutomationRuleId}], userProfiles=[{UserProfiles}]",
                        nameof(ScheduledAutomation),
                        JsonConvert.SerializeObject(automationRule.Conditions),
                        automationRule.CompanyId,
                        automationRule.Id,
                        JsonConvert.SerializeObject(selectedUserProfiles.UserProfiles.Select(u => u.Id)));

                }
                catch (Exception e)
                {
                    _logger.LogError(
                        e,
                        "[{MethodName}] Failed to get user profiles: conditions=[{Conditions}], companyId=[{CompanyId}], automationRuleId=[{AutomationRuleId}]",
                        nameof(ScheduledAutomation),
                        JsonConvert.SerializeObject(automationRule.Conditions),
                        automationRule.CompanyId,
                        automationRule.Id);
                    throw;
                }


                // if (automationRule.Conditions?.Count > 0)
                // {
                //    if (selectedUserProile.UserProfiles.Count > 0)
                //    {
                //        conversations = conversations.Where(x => selectedUserProile.UserProfiles.Contains(x.UserProfile)).Include(x => x.UserProfile).Include(x => x.NaiveUser).Include(x => x.facebookUser).Include(X => X.EmailAddress).Include(x => x.WhatsappUser).Include(x => x.WebClient).Include(x => x.WeChatUser).Include(x => x.LineUser).Include(x => x.SMSUser);
                //    }
                //    else
                //        return;
                // }

                // var userProfileId = conversationList.Select(x => x.UserProfileId).ToList();
                // var missingUserProfiles = selectedUserProile.UserProfiles.Where(x => !userProfileId.Contains(x.Id)).ToList();
                if (selectedUserProfiles != null)
                {
                    foreach (var userProfile in selectedUserProfiles.UserProfiles)
                    {
                        try
                        {
                            await TriggerAction(userProfile.Id, automationRule.AssignmentId, true);
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(
                                ex,
                                "[{MethodName}] Failed to trigger automation action: targetUserProfileId=[{UserProfileId}], companyId=[{CompanyId}], automationRuleId=[{AutomationRuleId}]",
                                nameof(ScheduledAutomation),
                                userProfile.Id,
                                automationRule.CompanyId,
                                automationRule.Id);
                        }
                    }
                }
            }

            // conversationList = conversations.ToList();

            // foreach (var conversation in conversationList)
            // {
            //    await TriggerAction(conversation.UserProfileId, automationRule.AssignmentId, true);
            // }
        }

        public async Task NewContactMessageTrigger(
            string conversationId,
            long conversationMessageId,
            bool isSendMessage = true)
        {
            var newContactAutomationLockKey = $"automation:new-contact:{conversationId}";
            var newContactAutomationLock = await _lockService.AcquireLockAsync(
                newContactAutomationLockKey,
                TimeSpan.FromMinutes(1));

            // New contact message trigger should run only once for a conversation
            if (newContactAutomationLock is null)
            {
                return;
            }

            try
            {
                var conversation = await _appDbContext.Conversations
                    .Include(x => x.Assignee.Identity)
                    .Include(x => x.UserProfile)
                    .Include(x => x.facebookUser)
                    .Include(x => x.WhatsappUser)
                    .Include(x => x.NaiveUser.Identity)
                    .Include(x => x.EmailAddress)
                    .Include(x => x.WebClient)
                    .Include(x => x.WeChatUser)
                    .Include(x => x.InstagramUser)
                    .Include(x => x.LineUser)
                    .Include(x => x.SMSUser)
                    .Include(x => x.ViberUser)
                    .Include(x => x.TelegramUser)
                    .Include(x => x.WhatsApp360DialogUser)
                    .Include(x => x.WhatsappCloudApiUser)
                    .FirstOrDefaultAsync(x => x.Id == conversationId);

                if (conversation == null)
                {
                    return;
                }

                var conversationMessage = await _appDbContext.ConversationMessages
                    .FirstOrDefaultAsync(
                        x =>
                            x.ConversationId == conversation.Id
                            && x.Id == conversationMessageId);

                if (conversationMessage == null)
                {
                    return;
                }

                var userProfileIdStatus = await GetUserProfileIdStatusByIdAsync(
                    conversation.CompanyId,
                    conversation.UserProfileId);

                if (userProfileIdStatus.ActiveStatus != ActiveStatus.Active)
                {
                    return;
                }

                // only do QRCode mapping checking if message content is not null
                if (!string.IsNullOrEmpty(conversationMessage.MessageContent))
                {
                    // FUSO SUZUKI would to continue the automation
                    var continueAutomationCompanyIds = new List<string>()
                    {
                        "7f5ae26e-e131-4212-bd8f-faa245546f37", "98527647-4306-480f-bdf0-7fde93db7676",
                        "72bd4e4c-abb4-44cb-9ebc-d4a3b94839be", "66c26d18-c08a-4dae-82bb-0f99c7dc91f4"
                    };

                    // do QRCode mapping checking
                    if (await _appDbContext.CompanyAssignmentRules
                            .AnyAsync(
                                x =>
                                    x.CompanyId == conversation.CompanyId
                                    && (x.AutomationType == AutomationType.QRCodeAssigneeMapping
                                        || x.AutomationType == AutomationType.QRCodeAssignTeamMapping)
                                    && x.Status == AutomationStatus.Live))
                    {
                        if (await QRCodeAssigneeMapping(conversation, conversationMessage, isSendMessage))
                        {
                            if (!continueAutomationCompanyIds.Contains(conversation.CompanyId))
                            {
                                return;
                            }
                        }
                    }
                }

                if (await _appDbContext.CompanyAssignmentRules.AnyAsync(
                        x =>
                            x.CompanyId == conversation.CompanyId
                            && x.AutomationType == AutomationType.ZapierNewIncomingMessage
                            && x.Status == AutomationStatus.Live))
                {
                    await ZapierIncomingMessage(conversation, conversationMessage, isSendMessage);
                }

                var automatedMessageTriggers = await _appDbContext.CompanyAssignmentRules
                    .Where(
                        x =>
                            x.CompanyId == conversation.CompanyId
                            && (x.AutomationType == AutomationType.NewContactMessage
                                || x.AutomationType == AutomationType.MessageReceived)
                            && x.Status == AutomationStatus.Live)
                    .Include(x => x.AutomationActions)
                    .OrderBy(x => x.Order)
                    .ToListAsync();

                if (automatedMessageTriggers.Count > 0)
                {
                    foreach (var automatedMessageTrigger in automatedMessageTriggers)
                    {
                        if (automatedMessageTrigger.TargetedChannels?.Count > 0 ||
                            automatedMessageTrigger.TargetedChannelWithIds?.Count > 0 ||
                            automatedMessageTrigger.Conditions?.Count > 0)
                        {
                            var assignmentRuleResult = await _userProfileService.CompareConditionConversation(
                                conversation,
                                conversationMessage,
                                automatedMessageTrigger);

                            if (assignmentRuleResult)
                            {
                                var regexCondition = automatedMessageTrigger.Conditions?
                                    .Where(
                                        x =>
                                            x.ConditionOperator == SupportedOperator.RegexMatched)
                                    .ToList();

                                if (regexCondition is { Count: > 0 })
                                {
                                    var match = Regex.Match(
                                        conversationMessage.MessageContent,
                                        regexCondition.FirstOrDefault().Values.FirstOrDefault());

                                    if (match.Success)
                                    {
                                        var identifyId = match.Groups[1].Value;

                                        await TriggerAction(
                                            conversation.UserProfileId,
                                            automatedMessageTrigger.AssignmentId,
                                            isSendMessage,
                                            identifyId,
                                            conversationMessageId);

                                        if (!automatedMessageTrigger.IsContinue)
                                        {
                                            return;
                                        }
                                    }
                                }

                                await TriggerAction(
                                    conversation.UserProfileId,
                                    automatedMessageTrigger.AssignmentId,
                                    isSendMessage,
                                    conversationMessageId: conversationMessageId);

                                if (!automatedMessageTrigger.IsContinue)
                                {
                                    return;
                                }
                            }
                        }
                        else
                        {
                            await TriggerAction(
                                conversation.UserProfileId,
                                automatedMessageTrigger.AssignmentId,
                                isSendMessage,
                                conversationMessageId: conversationMessageId);

                            if (!automatedMessageTrigger.IsContinue)
                            {
                                return;
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                await _lockService.ReleaseLockAsync(newContactAutomationLock);

                throw;
            }
        }

        public async Task NewMessageTrigger(
            string conversationId,
            long conversationMessageId,
            bool isSendMessage = true)
        {
            if (!await _appDbContext.CompanyAssignmentRules
                    .AnyAsync(
                        x =>
                            (x.AutomationType == AutomationType.MessageReceived
                             || x.AutomationType == AutomationType.QRCodeAssigneeMapping
                             || x.AutomationType == AutomationType.ZapierNewIncomingMessage
                             || x.AutomationType == AutomationType.QRCodeAssignTeamMapping)
                            && x.Status == AutomationStatus.Live))
            {
                return;
            }

            var conversation = await _appDbContext.Conversations
                .Include(x => x.Assignee.Identity)
                .Include(x => x.UserProfile)
                .Include(x => x.facebookUser)
                .Include(x => x.WhatsappUser)
                .Include(x => x.NaiveUser.Identity)
                .Include(x => x.EmailAddress)
                .Include(x => x.WebClient)
                .Include(x => x.WeChatUser)
                .Include(x => x.LineUser)
                .Include(x => x.InstagramUser)
                .Include(x => x.SMSUser)
                .Include(x => x.ViberUser)
                .Include(x => x.TelegramUser)
                .Include(x => x.WhatsApp360DialogUser)
                .Include(x => x.WhatsappCloudApiUser)
                .FirstOrDefaultAsync(x => x.Id == conversationId);

            if (conversation == null)
            {
                return;
            }

            var userProfileIdStatus = await GetUserProfileIdStatusByIdAsync(
                conversation.CompanyId,
                conversation.UserProfileId);

            if (userProfileIdStatus.ActiveStatus != ActiveStatus.Active)
            {
                return;
            }

            var conversationMessage = await _appDbContext.ConversationMessages
                .FirstOrDefaultAsync(
                    x =>
                        x.ConversationId == conversation.Id
                        && x.Id == conversationMessageId);

            if (conversationMessage == null)
            {
                return;
            }

            // only do QRCode mapping checking if message content is not null
            if (!string.IsNullOrEmpty(conversationMessage.MessageContent))
            {
                // FUSO SUZUKI would to continue the automation
                var continueAutomationCompanyIds = new List<string>()
                {
                    "7f5ae26e-e131-4212-bd8f-faa245546f37", "98527647-4306-480f-bdf0-7fde93db7676",
                    "72bd4e4c-abb4-44cb-9ebc-d4a3b94839be", "66c26d18-c08a-4dae-82bb-0f99c7dc91f4"
                };

                // do QRCode mapping checking
                if (await _appDbContext.CompanyAssignmentRules.AnyAsync(
                        x =>
                            x.CompanyId == conversation.CompanyId
                            && (x.AutomationType == AutomationType.QRCodeAssigneeMapping
                                || x.AutomationType == AutomationType.QRCodeAssignTeamMapping)
                            && x.Status == AutomationStatus.Live))
                {
                    if (await QRCodeAssigneeMapping(conversation, conversationMessage, isSendMessage))
                    {
                        if (!continueAutomationCompanyIds.Contains(conversation.CompanyId))
                        {
                            return;
                        }
                    }
                }
            }

            if (await _appDbContext.CompanyAssignmentRules.AnyAsync(
                    x =>
                        x.CompanyId == conversation.CompanyId
                        && x.AutomationType == AutomationType.ZapierNewIncomingMessage
                        && x.Status == AutomationStatus.Live))
            {
                await ZapierIncomingMessage(conversation, conversationMessage, isSendMessage);
            }

            var automatedMessageTriggers = await _appDbContext.CompanyAssignmentRules
                .Where(
                    x =>
                        x.CompanyId == conversation.CompanyId
                        && x.AutomationType == AutomationType.MessageReceived
                        && x.Status == AutomationStatus.Live)
                .Include(x => x.AutomationActions)
                .OrderBy(x => x.Order)
                .ToListAsync();

            if (automatedMessageTriggers.Count > 0)
            {
                foreach (var automatedMessageTrigger in automatedMessageTriggers)
                {
                    if (automatedMessageTrigger.TargetedChannels?.Count > 0 ||
                        automatedMessageTrigger.TargetedChannelWithIds?.Count > 0 ||
                        automatedMessageTrigger.Conditions?.Count > 0)
                    {
                        var assignmentRuleResult = await _userProfileService.CompareConditionConversation(
                            conversation,
                            conversationMessage,
                            automatedMessageTrigger,
                            automatedMessageTrigger.AutomationType);

                        if (assignmentRuleResult)
                        {
                            var regexCondition = automatedMessageTrigger.Conditions?
                                .Where(x => x.ConditionOperator == SupportedOperator.RegexMatched)
                                .ToList();

                            if (regexCondition is { Count: > 0 })
                            {
                                var match = Regex.Match(
                                    conversationMessage.MessageContent,
                                    regexCondition.FirstOrDefault().Values.FirstOrDefault());

                                if (match.Success)
                                {
                                    var identifyId = match.Groups[1].Value;

                                    await TriggerAction(
                                        conversation.UserProfileId,
                                        automatedMessageTrigger.AssignmentId,
                                        isSendMessage,
                                        identifyId,
                                        conversationMessageId);

                                    if (!automatedMessageTrigger.IsContinue)
                                    {
                                        return;
                                    }
                                }
                            }

                            await TriggerAction(
                                conversation.UserProfileId,
                                automatedMessageTrigger.AssignmentId,
                                isSendMessage,
                                conversationMessageId: conversationMessageId);

                            if (!automatedMessageTrigger.IsContinue)
                            {
                                return;
                            }
                        }
                    }
                    else
                    {
                        await TriggerAction(
                            conversation.UserProfileId,
                            automatedMessageTrigger.AssignmentId,
                            isSendMessage,
                            conversationMessageId: conversationMessageId);

                        if (!automatedMessageTrigger.IsContinue)
                        {
                            return;
                        }
                    }
                }
            }
        }

        private async Task ZapierIncomingMessage(
            Conversation conversation,
            ConversationMessage conversationMessage,
            bool isSendMessage = true)
        {
            try
            {
                var automatedMessageTriggers = await _appDbContext.CompanyAssignmentRules
                    .Where(
                        x =>
                            x.CompanyId == conversation.CompanyId
                            && x.AutomationType == AutomationType.ZapierNewIncomingMessage
                            && x.Status == AutomationStatus.Live)
                    .Include(x => x.AutomationActions)
                    .ToListAsync();

                if (automatedMessageTriggers.Count > 0)
                {
                    foreach (var automatedMessageTrigger in automatedMessageTriggers)
                    {
                        await TriggerAction(
                            conversation.UserProfileId,
                            automatedMessageTrigger.AssignmentId,
                            conversationMessageId: conversationMessage.Id);

                        if (!automatedMessageTrigger.IsContinue)
                        {
                            break;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "ZapierIncomingMessage error for conversation {ConversationId} message {MessageId}: {ExceptionMessage}",
                    conversation?.Id,
                    conversationMessage?.Id,
                    ex.Message);
            }
        }

        public async Task<bool> QRCodeAssigneeMapping(
            Conversation conversation,
            ConversationMessage conversationMessage,
            bool isSendMessage = true)
        {
            // OTO Bodycare (HK) Ltd. request do not assign with qrcode if already mapped to a contact owner
            if (conversation.CompanyId is "60cf7ef2-b249-47af-af18-476962ae5c89"
                && conversation.AssigneeId.HasValue)
            {
                return false;
            }

            var teamAssignmentResult = await QRCodeMappingForTeam(conversation, conversationMessage, isSendMessage);

            // if team assignment success return
            if (teamAssignmentResult == true)
            {
                return teamAssignmentResult;
            }

            return await QRCodeMappingForStaff(conversation, conversationMessage, isSendMessage);
        }

        public async Task<bool> QRCodeMappingForTeam(
            Conversation conversation,
            ConversationMessage conversationMessage,
            bool isSendMessage = true)
        {
            var qrCodeAssignmentForTeam = await _appDbContext.CompanyAssignmentRules
                .Include(x => x.AutomationActions)
                .OrderBy(x => x.Order)
                .FirstOrDefaultAsync(
                    x =>
                        x.CompanyId == conversation.CompanyId
                        && x.AutomationType == AutomationType.QRCodeAssignTeamMapping
                        && x.Status == AutomationStatus.Live);

            if (qrCodeAssignmentForTeam == null)
            {
                return false;
            }

            var regexCondition = qrCodeAssignmentForTeam.Conditions?
                .Where(x => x.ConditionOperator == SupportedOperator.RegexMatched)
                .ToList();

            if (regexCondition is { Count: > 0 })
            {
                var match = Regex.Match(
                    conversationMessage.MessageContent,
                    regexCondition.FirstOrDefault().Values.FirstOrDefault());

                if (match.Success)
                {
                    var identifyId = match.Groups[1].Value;

                    var team = await _appDbContext.CompanyStaffTeams
                        .Where(
                            x =>
                                x.CompanyId == conversation.CompanyId
                                && x.QRCodeIdentity == identifyId)
                        .FirstOrDefaultAsync();

                    if (team != null)
                    {
                        Staff staff = null;

                        if (!string.IsNullOrEmpty(team.QRCodeAssignmentStaffId))
                        {
                            staff = await _appDbContext.UserRoleStaffs
                                .FirstOrDefaultAsync(x => x.IdentityId == team.QRCodeAssignmentStaffId);
                        }

                        await AutomationAssignment(
                            conversation.UserProfile,
                            new AutomationAction
                            {
                                AutomatedTriggerType = AutomatedTriggerType.Assignment,
                                AssignmentType = AssignmentType.SpecificGroup,
                                AssignedTeamId = team.Id,
                                AssignedTeam = team,
                                TeamAssignmentType = team.QRCodeAssignmentType,
                                AssignedStaffId = staff?.Id,
                                AssignedStaff = staff
                            },
                            isSendMessage);

                        await TriggerAction(
                            conversation.UserProfileId,
                            qrCodeAssignmentForTeam.AssignmentId,
                            isSendMessage,
                            null,
                            conversationMessage.Id);

                        return true;
                    }
                }
            }

            return false;
        }

        public async Task<bool> QRCodeMappingForStaff(
            Conversation conversation,
            ConversationMessage conversationMessage,
            bool isSendMessage = true)
        {
            var automatedMessageTrigger = await _appDbContext.CompanyAssignmentRules
                .Include(x => x.AutomationActions)
                .OrderBy(x => x.Order)
                .FirstOrDefaultAsync(
                    x =>
                        x.CompanyId == conversation.CompanyId
                        && x.AutomationType == AutomationType.QRCodeAssigneeMapping
                        && x.Status == AutomationStatus.Live);

            if (automatedMessageTrigger == null)
            {
                return false;
            }

            var regexCondition = automatedMessageTrigger.Conditions?
                .Where(x => x.ConditionOperator == SupportedOperator.RegexMatched)
                .ToList();

            if (regexCondition is { Count: > 0 })
            {
                var match = Regex.Match(
                    conversationMessage.MessageContent,
                    regexCondition.FirstOrDefault().Values.FirstOrDefault());

                if (match.Success)
                {
                    var identifyId = match.Groups[1].Value;

                    await TriggerAction(
                        conversation.UserProfileId,
                        automatedMessageTrigger.AssignmentId,
                        isSendMessage,
                        identifyId,
                        conversationMessage.Id);

                    return true;
                }
            }

            return false;
        }

        public async Task ZaiperUserProfileUpdated(UserProfile userProfile)
        {
            var automatedMessageTriggers = await _appDbContext.CompanyAssignmentRules
                .Where(
                    x =>
                        x.CompanyId == userProfile.CompanyId
                        && x.AutomationType == AutomationType.ZapierContactUpdated
                        && x.Status == AutomationStatus.Live)
                .Include(x => x.AutomationActions)
                .ToListAsync();

            if (automatedMessageTriggers.Count > 0)
            {
                foreach (var automatedMessageTrigger in automatedMessageTriggers)
                {
                    await TriggerAction(
                        userProfile.Id,
                        automatedMessageTrigger.AssignmentId);

                    if (!automatedMessageTrigger.IsContinue)
                    {
                        break;
                    }
                }
            }
        }

        public async Task CrmHubUserProfileUpdated(UserProfile userProfile)
        {
            var automatedMessageTriggers = await _appDbContext.CompanyAssignmentRules
                .Where(
                    x =>
                        x.CompanyId == userProfile.CompanyId
                        && x.AutomationType == AutomationType.CrmHubContactUpdated
                        && x.Status == AutomationStatus.Live)
                .Include(x => x.AutomationActions)
                .ToListAsync();

            if (automatedMessageTriggers.Count > 0)
            {
                foreach (var automatedMessageTrigger in automatedMessageTriggers)
                {
                    await TriggerAction(
                        userProfile.Id,
                        automatedMessageTrigger.AssignmentId);

                    if (!automatedMessageTrigger.IsContinue)
                    {
                        break;
                    }
                }
            }
        }

        public async Task CustomFieldsChangedTrigger(string userProfileId, string fieldName, string value)
        {
            try
            {
                if (fieldName == null)
                {
                    return;
                }

                var userProfile = await _appDbContext.UserProfiles
                    .FirstOrDefaultAsync(x => x.Id == userProfileId);

                if (userProfile == null)
                {
                    return;
                }

                var userProfileIdStatus = await GetUserProfileIdStatusByIdAsync(userProfile.CompanyId, userProfile.Id);

                if (userProfileIdStatus.ActiveStatus != ActiveStatus.Active)
                {
                    return;
                }

                var conversation = await _userProfileService.GetConversationByUserProfileId(
                    userProfile.CompanyId,
                    userProfile.Id,
                    "closed");

                if (conversation == null)
                {
                    return;
                }

                if (await _appDbContext.CompanyAssignmentRules.AnyAsync(
                        x =>
                            x.CompanyId == userProfile.CompanyId
                            && x.AutomationType == AutomationType.ZapierContactUpdated
                            && x.Status == AutomationStatus.Live))
                {
                    await ZaiperUserProfileUpdated(userProfile);
                }

                if (await _appDbContext.CompanyAssignmentRules.AnyAsync(
                        x =>
                            x.CompanyId == userProfile.CompanyId
                            && x.AutomationType == AutomationType.CrmHubContactUpdated
                            && x.Status == AutomationStatus.Live))
                {
                    await CrmHubUserProfileUpdated(userProfile);
                }

                var automatedMessageTriggers = await _appDbContext.CompanyAssignmentRules
                    .Where(
                        x =>
                            x.CompanyId == conversation.CompanyId
                            && x.AutomationType == AutomationType.FieldValueChanged
                            && x.Status == AutomationStatus.Live)
                    .Include(x => x.AutomationActions)
                    .ToListAsync();

                automatedMessageTriggers = automatedMessageTriggers
                    .Where(
                        x =>
                            x.Conditions
                                .Select(y => y.FieldName.ToLower().Replace(" ", string.Empty))
                                .Contains(fieldName.ToLower().Replace(" ", string.Empty)))
                    .OrderBy(x => x.Order)
                    .ToList();

                if (automatedMessageTriggers.Count > 0)
                {
                    foreach (var automatedMessageTrigger in automatedMessageTriggers)
                    {
                        var conversationMessages = await _appDbContext.ConversationMessages
                            .Where(x => x.ConversationId == conversation.Id && x.Channel != ChannelTypes.Note)
                            .OrderByDescending(x => x.Id)
                            .FirstOrDefaultAsync();

                        var changedFieldsDict = new Dictionary<string, string>
                        {
                            [fieldName] = value
                        };

                        var assignmentRuleResult = await _userProfileService.CompareConditionConversation(
                            conversation,
                            conversationMessages,
                            automatedMessageTrigger,
                            importUserProfileObject: changedFieldsDict.ToImportUserProfileObject());

                        if (assignmentRuleResult)
                        {
                            await TriggerAction(
                                userProfileId,
                                automatedMessageTrigger.AssignmentId);

                            if (!automatedMessageTrigger.IsContinue)
                            {
                                break;
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[{MethodName}] error processing field {FieldName} value change {FieldValue} for user profile {UserProfileId}: {ExceptionMessage}",
                    nameof(CustomFieldsChangedTrigger),
                    fieldName,
                    value,
                    userProfileId,
                    ex.Message);
            }
        }

        public async Task CustomFieldsBulkChangedTrigger(
            string userProfileId,
            ImportUserProfileObject importUserProfileObject,
            List<string> fields)
        {
            await CustomFieldsBulkChangedTrigger(
                userProfileId,
                importUserProfileObject,
                fields,
                AutomationType.FieldValueChanged);
        }

        public async Task CustomFieldsBulkChangedTrigger(
            string userProfileId,
            ImportUserProfileObject importUserProfileObject,
            List<string> fields,
            AutomationType? automationType = AutomationType.FieldValueChanged)
        {
            // Emergency fix, can remove this anytime 14397acf-4f77-4365-96e7-6c4351a2f30c
            if (userProfileId == "14397acf-4f77-4365-96e7-6c4351a2f30c")
            {
                return;
            }

            try
            {
                automationType ??= AutomationType.FieldValueChanged;

                if (fields == null ||
                    fields.Count == 0)
                {
                    return;
                }

                var userProfile = await _appDbContext.UserProfiles
                    .FirstOrDefaultAsync(x => x.Id == userProfileId);

                if (userProfile == null)
                {
                    return;
                }

                var userProfileIdStatus = await GetUserProfileIdStatusByIdAsync(userProfile.CompanyId, userProfile.Id);

                if (userProfileIdStatus.ActiveStatus != ActiveStatus.Active)
                {
                    return;
                }

                var conversation = await _userProfileService.GetConversationByUserProfileId(
                    userProfile.CompanyId,
                    userProfile.Id,
                    "closed");

                if (conversation == null)
                {
                    throw new Exception($"conversation is null");
                }

                ILockService.Lock myLock = null;

                while (true)
                {
                    myLock = await _lockService.AcquireLockAsync(
                        $"CustomFieldsBulkChangedTrigger_{userProfileId}",
                        TimeSpan.FromSeconds(10));

                    if (myLock == null)
                    {
                        await Task.Delay(TimeSpan.FromSeconds(2));
                    }
                    else
                    {
                        break;
                    }
                }

                if (await _appDbContext.CompanyAssignmentRules.AnyAsync(
                        x =>
                            x.CompanyId == userProfile.CompanyId
                            && x.AutomationType == AutomationType.ZapierContactUpdated
                            && x.Status == AutomationStatus.Live))
                {
                    await ZaiperUserProfileUpdated(userProfile);
                }

                if (await _appDbContext.CompanyAssignmentRules.AnyAsync(
                        x =>
                            x.CompanyId == userProfile.CompanyId
                            && x.AutomationType == AutomationType.CrmHubContactUpdated
                            && x.Status == AutomationStatus.Live))
                {
                    await CrmHubUserProfileUpdated(userProfile);
                }

                for (int i = 0; i < fields.Count; i++)
                {
                    var field = fields[i];
                    var header = importUserProfileObject._headers[i];

                    var automatedMessageTriggers = await _appDbContext.CompanyAssignmentRules
                        .Where(
                            x =>
                                x.CompanyId == conversation.CompanyId
                                && x.AutomationType == automationType
                                && x.Status == AutomationStatus.Live)
                        .Include(x => x.AutomationActions)
                        .ToListAsync();

                    automatedMessageTriggers = automatedMessageTriggers
                        .Where(
                            x =>
                                x.Conditions is { Count: > 0 }
                                && x.Conditions
                                    .Select(y => y.FieldName?.Replace(" ", string.Empty).ToLower())
                                    .Contains(header.HeaderName.Replace(" ", string.Empty).ToLower()))
                        .OrderBy(x => x.Order)
                        .ToList();

                    if (automatedMessageTriggers.Count > 0)
                    {
                        foreach (var automatedMessageTrigger in automatedMessageTriggers)
                        {
                            var conversationMessages = await _appDbContext.ConversationMessages
                                .Where(
                                    x =>
                                        x.ConversationId == conversation.Id
                                        && x.Channel != ChannelTypes.Note)
                                .OrderByDescending(x => x.Id)
                                .FirstOrDefaultAsync();

                            var isTriggerConditionMatched = await _userProfileService.CompareConditionConversation(
                                conversation,
                                conversationMessages,
                                automatedMessageTrigger,
                                automationType,
                                importUserProfileObject);

                            if (isTriggerConditionMatched)
                            {
                                // var key = $"triggered:{conversation.Id}_{automatedMessageTrigger.AssignmentId}";
                                // ICacheService.Lock triggerLock = await _lockService.AcquireLockAsync(key, TimeSpan.FromSeconds(15));
                                // if (triggerLock != null)
                                // {
                                //     await TriggerAction(userProfileId, automatedMessageTrigger.AssignmentId);
                                // }
                                await TriggerAction(
                                    userProfileId,
                                    automatedMessageTrigger.AssignmentId);

                                if (!automatedMessageTrigger.IsContinue)
                                {
                                    await _lockService.ReleaseLockAsync(myLock);
                                    return;
                                }
                            }
                        }
                    }

                    // else
                    // {
                    //    automatedMessageTriggers = _appDbContext.CompanyAssignmentRules.Where(x => x.CompanyId == conversation.CompanyId && x.AutomationType == automationType && x.Status == AutomationStatus.Live).Include(x => x.AutomationActions).AsEnumerable().Where(x => x.Conditions == null || x.Conditions.Count() == 0).OrderBy(x => x.Order).ToList();
                    //    foreach (var automatedMessageTrigger in automatedMessageTriggers)
                    //    {
                    //        //var conversationMessages = await _appDbContext.ConversationMessages.Where(x => x.ConversationId == conversation.Id && x.Channel != ChannelTypes.Note).OrderByDescending(x => x.Id).Take(1).FirstOrDefaultAsync();
                    //        await TriggerAction(userProfileId, automatedMessageTrigger.AssignmentId);
                    //        if (!automatedMessageTrigger.IsContinue)
                    //        {
                    //            await _lockService.ReleaseLockAsync(mylock);
                    //            return;
                    //        }
                    //    }
                    // }
                }

                await _lockService.ReleaseLockAsync(myLock);
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[{MethodName}] error processing bulk field changes for user profile {UserProfileId}: {ExceptionMessage}. Fields: {ChangedFields}",
                    nameof(CustomFieldsBulkChangedTrigger),
                    userProfileId,
                    ex.Message,
                    JsonConvert.SerializeObject(importUserProfileObject));
            }
        }

        public async Task AddToListTrigger(List<ImportedUserProfileForAutomationTrigger> importedUserProfiles)
        {
            var userProfiles = await _appDbContext.UserProfiles
                .Where(
                    profile =>
                        importedUserProfiles
                            .Select(x => x.UserProfileId)
                            .Contains(profile.Id))
                .ToListAsync();

            foreach (var userProfile in userProfiles)
            {
                var listId = importedUserProfiles
                    .Select(y => y.ImportContactHistoryId)
                    .FirstOrDefault()
                    .ToString();

                var conversation = await _userProfileService.GetConversationByUserProfileId(
                    userProfile.CompanyId,
                    userProfile.Id,
                    "closed");

                if (conversation != null)
                {
                    var userProfileIdStatus = await GetUserProfileIdStatusByIdAsync(
                        conversation.CompanyId,
                        conversation.UserProfileId);

                    if (userProfileIdStatus.ActiveStatus != ActiveStatus.Active)
                    {
                        return;
                    }

                    var automatedMessageTriggers = await _appDbContext.CompanyAssignmentRules
                        .Where(
                            x =>
                                x.CompanyId == conversation.CompanyId
                                && x.AutomationType == AutomationType.FieldValueChanged
                                && x.Status == AutomationStatus.Live)
                        .Include(x => x.AutomationActions)
                        .ToListAsync();

                    automatedMessageTriggers = automatedMessageTriggers
                        .Where(
                            trigger =>
                                trigger.Conditions
                                    .Any(
                                        x =>
                                            x.FieldName == "importfrom"
                                            && x.Values.Contains(listId)))
                        .OrderBy(x => x.Order)
                        .ToList();

                    if (automatedMessageTriggers.Count > 0)
                    {
                        foreach (var automatedMessageTrigger in automatedMessageTriggers)
                        {
                            var conversationMessages = await _appDbContext.ConversationMessages
                                .OrderByDescending(x => x.Id)
                                .FirstOrDefaultAsync(
                                    x =>
                                        x.ConversationId == conversation.Id
                                        && x.Channel != ChannelTypes.Note);

                            var assignmentRuleResult = await _userProfileService.CompareConditionConversation(
                                conversation,
                                conversationMessages,
                                automatedMessageTrigger);

                            if (assignmentRuleResult)
                            {
                                await TriggerAction(
                                    userProfile.Id,
                                    automatedMessageTrigger.AssignmentId);

                                if (!automatedMessageTrigger.IsContinue)
                                {
                                    return;
                                }
                            }
                        }
                    }
                }
            }
        }

        public async Task HashtagChangedTrigger(
            string userProfileId,
            List<ConversationHashtagViewModel> conversationTagViewModels)
        {
            var userProfile = await _appDbContext.UserProfiles
                .FirstOrDefaultAsync(x => x.Id == userProfileId);

            var conversation = await _userProfileService.GetConversationByUserProfileId(
                userProfile.CompanyId,
                userProfile.Id,
                "closed");

            var userProfileIdStatus = await GetUserProfileIdStatusByIdAsync(userProfile.CompanyId, userProfile.Id);

            if (userProfileIdStatus.ActiveStatus != ActiveStatus.Active)
            {
                return;
            }

            if (conversation != null)
            {
                var hashtags = conversationTagViewModels
                    .Select(x => x.Hashtag)
                    .ToList();

                var automatedMessageTriggers = await _appDbContext.CompanyAssignmentRules
                    .Where(
                        x =>
                            x.CompanyId == conversation.CompanyId
                            && x.AutomationType == AutomationType.FieldValueChanged
                            && x.Status == AutomationStatus.Live)
                    .Include(x => x.AutomationActions)
                    .ToListAsync();

                automatedMessageTriggers = automatedMessageTriggers
                    .Where(
                        x =>
                            x.Conditions
                                .Where(x => !string.IsNullOrEmpty(x.ContainHashTag))
                                .Select(x => x.Values)
                                .Any(value => hashtags.Any(hashtag => value.Contains(hashtag))))
                    .OrderBy(x => x.Order)
                    .ToList();

                if (automatedMessageTriggers.Count > 0)
                {
                    foreach (var automatedMessageTrigger in automatedMessageTriggers)
                    {
                        var conversationMessages = await _appDbContext.ConversationMessages
                            .Where(
                                x =>
                                    x.ConversationId == conversation.Id
                                    && x.Channel != ChannelTypes.Note)
                            .OrderByDescending(x => x.Id)
                            .FirstOrDefaultAsync();

                        var assignmentRuleResult = await _userProfileService.CompareConditionConversation(
                            conversation,
                            conversationMessages,
                            automatedMessageTrigger);

                        if (assignmentRuleResult)
                        {
                            await TriggerAction(
                                userProfileId,
                                automatedMessageTrigger.AssignmentId);

                            if (!automatedMessageTrigger.IsContinue)
                            {
                                return;
                            }
                        }
                    }
                }
            }
        }

        public async Task OutgoingMessageTrigger(
            string conversationId,
            long conversationMessageId,
            bool isSendMessage = true)
        {
            if (!await _appDbContext.CompanyAssignmentRules
                    .AnyAsync(
                        x =>
                            x.AutomationType == AutomationType.OutgoingMessageTrigger
                            && x.Status == AutomationStatus.Live))
            {
                return;
            }

            var conversation = await _appDbContext.Conversations
                .Include(x => x.Assignee.Identity)
                .Include(x => x.UserProfile)
                .Include(x => x.facebookUser)
                .Include(x => x.WhatsappUser)
                .Include(x => x.NaiveUser.Identity)
                .Include(x => x.EmailAddress)
                .Include(x => x.WebClient)
                .Include(x => x.WeChatUser)
                .Include(x => x.LineUser)
                .Include(x => x.InstagramUser)
                .Include(x => x.SMSUser)
                .Include(x => x.ViberUser)
                .Include(x => x.TelegramUser)
                .Include(x => x.WhatsApp360DialogUser)
                .Include(x => x.WhatsappCloudApiUser)
                .FirstOrDefaultAsync(x => x.Id == conversationId);

            if (conversation == null)
            {
                return;
            }

            var userProfileIdStatus = await GetUserProfileIdStatusByIdAsync(
                conversation.CompanyId,
                conversation.UserProfileId);

            if (userProfileIdStatus.ActiveStatus != ActiveStatus.Active)
            {
                return;
            }

            var conversationMessage = await _appDbContext.ConversationMessages
                .FirstOrDefaultAsync(
                    x =>
                        x.ConversationId == conversation.Id
                        && x.Id == conversationMessageId);

            if (conversationMessage == null)
            {
                return;
            }

            var automatedMessageTriggers = await _appDbContext.CompanyAssignmentRules
                .Where(
                    x =>
                        x.CompanyId == conversation.CompanyId
                        && x.AutomationType == AutomationType.OutgoingMessageTrigger
                        && x.Status == AutomationStatus.Live)
                .Include(x => x.AutomationActions)
                .OrderBy(x => x.Order)
                .ToListAsync();

            if (automatedMessageTriggers.Count > 0)
            {
                foreach (var automatedMessageTrigger in automatedMessageTriggers)
                {
                    if (automatedMessageTrigger.TargetedChannels?.Count > 0 ||
                        automatedMessageTrigger.TargetedChannelWithIds?.Count > 0 ||
                        automatedMessageTrigger.Conditions?.Count > 0)
                    {
                        var assignmentRuleResult = await _userProfileService.CompareConditionConversation(
                            conversation,
                            conversationMessage,
                            automatedMessageTrigger);

                        if (assignmentRuleResult)
                        {
                            var regexCondition = automatedMessageTrigger.Conditions?
                                .Where(x => x.ConditionOperator == SupportedOperator.RegexMatched)
                                .ToList();

                            if (regexCondition is { Count: > 0 })
                            {
                                var match = Regex.Match(
                                    conversationMessage.MessageContent,
                                    regexCondition.FirstOrDefault().Values.FirstOrDefault());

                                if (match.Success)
                                {
                                    var identifyId = match.Groups[1].Value;

                                    await TriggerAction(
                                        conversation.UserProfileId,
                                        automatedMessageTrigger.AssignmentId,
                                        isSendMessage,
                                        identifyId,
                                        conversationMessageId);

                                    if (!automatedMessageTrigger.IsContinue)
                                    {
                                        return;
                                    }
                                }
                            }

                            await TriggerAction(
                                conversation.UserProfileId,
                                automatedMessageTrigger.AssignmentId,
                                isSendMessage,
                                conversationMessageId: conversationMessageId);

                            if (!automatedMessageTrigger.IsContinue)
                            {
                                return;
                            }
                        }
                    }
                    else
                    {
                        await TriggerAction(
                            conversation.UserProfileId,
                            automatedMessageTrigger.AssignmentId,
                            isSendMessage,
                            conversationMessageId: conversationMessageId);

                        if (!automatedMessageTrigger.IsContinue)
                        {
                            return;
                        }
                    }
                }
            }
        }

        public async Task ConversationAssignmentTrigger(string conversationId, long ConversationMessageId)
        {
            try
            {
                var conversation = await _appDbContext.Conversations
                    .Include(x => x.Assignee.Identity)
                    .FirstOrDefaultAsync(x => x.Id == conversationId);

                var userProfileIdStatus = await GetUserProfileIdStatusByIdAsync(
                    conversation.CompanyId,
                    conversation.UserProfileId);

                if (userProfileIdStatus.ActiveStatus != ActiveStatus.Active)
                {
                    return;
                }

                if (conversation is { AssigneeId: null })
                {
                    var conversationMessage = await _appDbContext.ConversationMessages
                        .FirstOrDefaultAsync(x => x.Id == ConversationMessageId);

                    await ConversationAssignment(conversation, conversationMessage, true);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[{MethodName}] error for conversation {ConversationId} message {MessageId}: {ExceptionMessage}",
                    nameof(ConversationAssignmentTrigger),
                    conversationId,
                    ConversationMessageId,
                    ex.Message);
            }
        }

        public async Task FbCommentTrigger(Entry entry, Change change)
        {
            AssignmentRule assignmentRule = null;

            var companyId = await _appDbContext.ConfigFacebookConfigs
                .Where(x => x.PageId == entry.Id)
                .Select(x => x.CompanyId)
                .FirstOrDefaultAsync();

            try
            {
                var assignmentRules = await _appDbContext.CompanyAssignmentRules
                    .Include(x => x.AutomationActions)
                    .ThenInclude(x => x.FbIgAutoReply)
                    .ThenInclude(x => x.FbIgAutoReplyHistoryRecords.Where(y => y.PageId == entry.Id && y.ParentCommentId == change.Value.CommentId && y.ErrorMessage == null))
                    .OrderBy(x => x.Order)
                    .Where(
                        x =>
                            x.Status == AutomationStatus.Live
                            && x.CompanyId == companyId
                            && x.AutomationType == AutomationType.FacebookPostComment
                            && x.Conditions != null)
                    .ToListAsync();

                foreach (var rule in assignmentRules)
                {
                    if (rule.Conditions
                            .Any(
                                x =>
                                    x.FieldName.ToLower() == "pageid"
                                    && x.Values.Contains(entry.Id))
                        && rule.Conditions
                            .Any(
                                x =>
                                    x.FieldName.ToLower() == "postid"
                                    && (x.Values.Contains(change.Value.PostId)
                                        || x.Values.Contains("all"))))
                    {
                        if (rule.Conditions.All(x => x.FieldName.ToLower() != "keywords"))
                        {
                            assignmentRule = rule;
                            break;
                        }

                        var keywords = rule.Conditions
                            .Where(x => x.FieldName.ToLower() == "keywords")
                            .SelectMany(x => x.Values)
                            .ToList();

                        if (keywords.Any(y => change.Value.Message.ToLower().Contains(y.ToLower())))
                        {
                            assignmentRule = rule;
                            break;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Trigger Facebook comment automation error for Page ID {EntryId} comment id {ChangeValueCommentId}: {ExceptionMessage}",
                    entry.Id,
                    change.Value.CommentId,
                    ex.Message);
            }

            if (assignmentRule == null)
            {
                return;
            }

            var isCommentAlreadyReplied = assignmentRule.AutomationActions.Any(
                x => x.FbIgAutoReply != null && x.FbIgAutoReply.FbIgAutoReplyHistoryRecords != null &&
                     x.FbIgAutoReply.FbIgAutoReplyHistoryRecords.Any());

            if (isCommentAlreadyReplied)
            {
                return;
            }

            ILockService.Lock myLock = null;

            try
            {
                while (!await _appDbContext.UserProfiles.AnyAsync(
                           x => x.CompanyId == companyId &&
                                x.FacebookAccount != null
                                && x.FacebookAccount.FacebookId == change.Value.From.Id
                                && x.FacebookAccount.pageId == entry.Id))
                {
                    var fbCommenterId = change.Value.From.Id;
                    var lockId = $"{entry.Id}_{fbCommenterId}";

                    myLock = await _lockService.AcquireLockAsync(lockId, TimeSpan.FromSeconds(15));

                    if (myLock == null)
                    {
                        await Task.Delay(TimeSpan.FromSeconds(5));
                    }
                    else
                    {
                        break;
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Unable to lock {MethodName} for Page ID {EntryId} comment id {ChangeValueCommentId}",
                    nameof(FbCommentTrigger),
                    entry.Id,
                    change.Value.CommentId);
            }

            _logger.LogInformation(
                "{MethodName} {LockId} acquired lock for Page ID {EntryId} comment id {ChangeValueCommentId}",
                nameof(FbCommentTrigger),
                myLock?.Id,
                entry.Id,
                change.Value.CommentId);

            var userProfile = await _appDbContext.UserProfiles
                .Include(x => x.FacebookAccount)
                .Where(
                    x =>
                        x.FacebookAccount != null
                        && x.FacebookAccount.FacebookId == change.Value.From.Id
                        && x.FacebookAccount.pageId == entry.Id)
                .OrderByDescending(x => x.UpdatedAt)
                .FirstOrDefaultAsync();

            bool isNewContact = userProfile == null;

            if (userProfile is { ActiveStatus: ActiveStatus.Inactive })
            {
                _logger.LogInformation(
                   "[{MethodName}] Recovering soft deleted user profile {UserProfileId} (Facebook ID - {FacebookId}) for company {CompanyId}",
                   nameof(FbCommentTrigger),
                   userProfile.Id,
                   change.Value.From.Id,
                   companyId);

                await _userProfileSafeDeleteService.RecoverSoftDeletedUserProfilesAsync(
                    companyId,
                    new HashSet<string>()
                    {
                        userProfile.Id
                    },
                    new UserProfileRecoveryTriggerContext(
                        UpdateUserProfileTriggerSource.AutomationAction,
                        null));
            }

            if (assignmentRule.Conditions
                    .Any(
                        x =>
                            x.FieldName.ToLower() == "onlynewcontacts"
                            && x.Values.Contains("true"))
                && !isNewContact)
            {
                return;
            }

            if (isNewContact
                && !string.IsNullOrEmpty(change.Value.From.Id)
                && !string.IsNullOrEmpty(entry.Id))
            {
                userProfile = await _facebookService.CreateUserProfile(
                    change.Value.From.Id,
                    entry.Id,
                    change.Value.From.Name);
            }

            var existingConversation = await _appDbContext.Conversations
                .FirstOrDefaultAsync(x => x.UserProfileId == userProfile.Id);

            if (existingConversation == null)
            {
                var conversation = new Conversation
                {
                    UserProfileId = userProfile!.Id,
                    facebookUser = userProfile.FacebookAccount,
                    MessageGroupName = userProfile.CompanyId,
                    CompanyId = userProfile.CompanyId,
                    ActiveStatus = ActiveStatus.Active
                };

                _appDbContext.Conversations.Add(conversation);
                await _appDbContext.SaveChangesAsync();

                existingConversation = conversation;
            }

            if (myLock is not null)
            {
                await _lockService.ReleaseLockAsync(myLock);
            }

            _logger.LogInformation(
                "{MethodName} {LockId} released lock for Page ID {EntryId} comment id {ChangeValueCommentId}, " +
                "is new contact: {IsNewContact}, userProfileId: {UserProfileId}",
                nameof(FbCommentTrigger),
                myLock?.Id,
                entry.Id,
                change.Value.CommentId,
                isNewContact,
                userProfile?.Id);

            if (userProfile != null)
            {
                await FbIgCommentTriggerAction(entry, change, userProfile, assignmentRule, isNewContact, true, null);
            }

            var fbIgAutoReplies = await _appDbContext.CompanyAutomationActions
                .Where(x => x.AssignmentRuleId == assignmentRule.Id)
                .Include(x => x.FbIgAutoReply)
                .ThenInclude(x => x.FbIgAutoReplyHistoryRecords.OrderByDescending(y => y.CreatedAt).Where(y => y.PageId == entry.Id && y.ParentCommentId == change.Value.CommentId))
                .Select(x => x.FbIgAutoReply)
                .ToListAsync();

            foreach (var autoReply in fbIgAutoReplies)
            {
                // autoReply could be null if the action is not comment / dm reply
                var latestHistory = autoReply?.FbIgAutoReplyHistoryRecords?.FirstOrDefault();

                if (latestHistory != null)
                {
                    latestHistory.IsNewContact = isNewContact;

                    if (latestHistory.MessageId != null)
                    {
                        await _conversationMessageService.FetchByMessageId(
                            entry.Id,
                            latestHistory.MessageId,
                            true,
                            conversation: existingConversation);
                    }
                }

                await _appDbContext.SaveChangesAsync();
            }
        }

        public async Task IgCommentTrigger(Entry entry, Change change)
        {
            AssignmentRule assignmentRule = null;

            var companyId = await _appDbContext.ConfigInstagramConfigs
                .Where(x => x.InstagramPageId == entry.Id)
                .Select(x => x.CompanyId)
                .FirstOrDefaultAsync();

            try
            {
                var assignmentRules = await _appDbContext.CompanyAssignmentRules
                    .Include(x => x.AutomationActions)
                    .ThenInclude(x => x.FbIgAutoReply)
                    .ThenInclude(
                        x => x.FbIgAutoReplyHistoryRecords.Where(
                            y => y.PageId == entry.Id && y.ParentCommentId == change.Value.Id &&
                                 y.ErrorMessage == null))
                    .Include(x => x.AutomationActions)
                    .ThenInclude(x => x.FbIgAutoReply)
                    .ThenInclude(x => x.FbIgAutoReplyFiles)
                    .Where(
                        x =>
                            x.Status == AutomationStatus.Live
                            && x.CompanyId == companyId
                            && x.AutomationType == AutomationType.InstagramMediaComment
                            && x.Conditions != null)
                    .OrderBy(x => x.Order)
                    .ToListAsync();

                foreach (var rule in assignmentRules)
                {
                    if (rule.Conditions
                            .Any(
                                x =>
                                    x.FieldName.ToLower() == "pageid"
                                    && x.Values.Contains(entry.Id))
                        && rule.Conditions
                            .Any(
                                x =>
                                    x.FieldName.ToLower() == "postid"
                                    && (x.Values.Contains(change.Value.Media.Id)
                                        || x.Values.Contains("all"))))
                    {
                        if (rule.Conditions.All(x => x.FieldName.ToLower() != "keywords"))
                        {
                            assignmentRule = rule;
                            break;
                        }

                        var keywords = rule.Conditions
                            .Where(x => x.FieldName.ToLower() == "keywords")
                            .SelectMany(x => x.Values)
                            .ToList();

                        if (keywords.Any(y => change.Value.Text.ToLower().Contains(y.ToLower())))
                        {
                            assignmentRule = rule;
                            break;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Trigger Instagram comment automation error for Page ID {EntryId} comment id {ChangeValueCommentId}: {ExceptionMessage}",
                    entry.Id,
                    change.Value.CommentId,
                    ex.Message);
            }

            if (assignmentRule == null)
            {
                return;
            }

            var isCommentAlreadyReplied = assignmentRule.AutomationActions
                .Any(
                    x => x.FbIgAutoReply != null && x.FbIgAutoReply.FbIgAutoReplyHistoryRecords != null &&
                         x.FbIgAutoReply.FbIgAutoReplyHistoryRecords.Any());

            if (isCommentAlreadyReplied)
            {
                return;
            }

            ILockService.Lock myLock = null;

            try
            {
                // We only AcquireLockAsync for unable to get the existing User Profile
                while (!await _appDbContext.UserProfiles.AnyAsync(
                           x => x.CompanyId == companyId &&
                                x.InstagramUser != null
                                && x.InstagramUser.InstagramId == change.Value.From.Id
                                && x.InstagramUser.InstagramPageId == entry.Id))
                {
                    var igCommenterId = change.Value.From.Id;
                    var lockId = $"{entry.Id}_{igCommenterId}";

                    myLock = await _lockService.AcquireLockAsync(lockId, TimeSpan.FromSeconds(10));

                    if (myLock == null)
                    {
                        await Task.Delay(TimeSpan.FromSeconds(10));
                    }
                    else
                    {
                        break;
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Unable to lock {MethodName} for Page ID {EntryId} comment id {ChangeValueCommentId}",
                    nameof(IgCommentTrigger),
                    entry.Id,
                    change.Value.CommentId);
            }

            var userProfile = await _appDbContext.UserProfiles
                .Include(x => x.InstagramUser)
                .Where(
                    x =>
                        x.CompanyId == companyId &&
                        x.InstagramUser != null
                        && x.InstagramUser.InstagramId == change.Value.From.Id
                        && x.InstagramUser.InstagramPageId == entry.Id)
                .OrderByDescending(x => x.UpdatedAt)
                .FirstOrDefaultAsync();

            bool isNewContact = userProfile == null;

            if (userProfile is { ActiveStatus: ActiveStatus.Inactive })
            {
                _logger.LogInformation(
                   "[{MethodName}] Recovering soft deleted user profile {UserProfileId} (Instagram ID - {InstagramId}) for company {CompanyId}",
                   nameof(IgCommentTrigger),
                   userProfile.Id,
                   change.Value.From.Id,
                   companyId);

                await _userProfileSafeDeleteService.RecoverSoftDeletedUserProfilesAsync(
                    companyId,
                    new HashSet<string>()
                    {
                        userProfile.Id
                    },
                    new UserProfileRecoveryTriggerContext(
                        UpdateUserProfileTriggerSource.AutomationAction,
                        null));
            }

            if (assignmentRule.Conditions
                    .Any(
                        x =>
                            x.FieldName.ToLower() == "onlynewcontacts"
                            && x.Values.Contains("true"))
                && !isNewContact)
            {
                return;
            }

            if (isNewContact
                && !string.IsNullOrEmpty(change.Value.From.Id)
                && !string.IsNullOrEmpty(entry.Id))
            {
                userProfile = await _instagramService.CreateUserProfile(
                    change.Value.From.Id,
                    entry.Id,
                    change.Value.From.UserName);
            }

            var existingConversation = await _appDbContext.Conversations
                .FirstOrDefaultAsync(x => x.UserProfileId == userProfile.Id);

            if (existingConversation == null)
            {
                var conversation = new Conversation
                {
                    UserProfileId = userProfile!.Id,
                    facebookUser = userProfile.FacebookAccount,
                    MessageGroupName = userProfile.CompanyId,
                    CompanyId = userProfile.CompanyId,
                    ActiveStatus = ActiveStatus.Active
                };

                _appDbContext.Conversations.Add(conversation);
                await _appDbContext.SaveChangesAsync();

                existingConversation = conversation;
            }

            if (myLock is not null)
            {
                await _lockService.ReleaseLockAsync(myLock);
            }

            if (userProfile != null)
            {
                await FbIgCommentTriggerAction(
                    entry,
                    change,
                    userProfile,
                    assignmentRule,
                    isNewContact,
                    true,
                    null);
            }

            var fbIgAutoReplies = await _appDbContext.CompanyAutomationActions
                .Include(x => x.FbIgAutoReply)
                    .ThenInclude(x => x.FbIgAutoReplyHistoryRecords.OrderByDescending(y => y.CreatedAt).Where(y => y.PageId == entry.Id && y.ParentCommentId == change.Value.Id))
                .Where(x => x.AssignmentRuleId == assignmentRule.Id)
                .Select(x => x.FbIgAutoReply)
                .ToListAsync();

            foreach (var autoReply in fbIgAutoReplies)
            {
                var latestHistory = autoReply?.FbIgAutoReplyHistoryRecords?
                    .FirstOrDefault();

                if (latestHistory != null)
                {
                    latestHistory.IsNewContact = isNewContact;

                    if (latestHistory.MessageId != null)
                    {
                        await _conversationMessageService.FetchByMessageId(
                            entry.Id,
                            latestHistory.MessageId,
                            true,
                            conversation: existingConversation);
                    }
                }

                await _appDbContext.SaveChangesAsync();
            }
        }

        public async Task FbIgPreviewCodeTrigger(FBMessaging messaging, Entry entry)
        {
            var pageId = entry.Id;
            string companyId = null;
            AssignmentRule assignmentRule = null;

            try
            {
                companyId = await _appDbContext.ConfigFacebookConfigs
                    .Where(x => x.PageId == pageId)
                    .Select(x => x.CompanyId)
                    .FirstOrDefaultAsync();

                if (companyId == null)
                {
                    companyId = await _appDbContext.ConfigInstagramConfigs
                        .Where(x => x.InstagramPageId == pageId)
                        .Select(x => x.CompanyId)
                        .FirstOrDefaultAsync();
                }

                if (companyId == null)
                {
                    var assignmentRules = await _appDbContext.CompanyAssignmentRules
                        .Include(x => x.AutomationActions)
                            .ThenInclude(x => x.FbIgAutoReply)
                            .ThenInclude(x => x.FbIgAutoReplyFiles)
                        .Include(x => x.AutomationActions)
                            .ThenInclude(x => x.FbIgAutoReply)
                            .ThenInclude(x => x.FbIgAutoReplyHistoryRecords)
                        .Where(
                            x =>
                                x.IsPreview
                                && x.Status == AutomationStatus.Draft
                                && x.PreviewCode != null
                                && x.PreviewCode == messaging.message.text
                                && (x.AutomationType == AutomationType.InstagramMediaComment
                                    || x.AutomationType == AutomationType.FacebookPostComment)
                                && x.Conditions != null)
                        .OrderBy(x => x.Order)
                        .ToListAsync();

                    foreach (var rule in assignmentRules)
                    {
                        if (rule.Conditions
                                .Any(
                                    x =>
                                        x.FieldName.ToLower() == "pageid"
                                        && x.Values.Contains(pageId)))
                        {
                            assignmentRule = rule;
                            break;
                        }
                    }
                }
                else
                {
                    var assignmentRules = await _appDbContext.CompanyAssignmentRules
                        .Include(x => x.AutomationActions)
                        .ThenInclude(x => x.FbIgAutoReply)
                        .ThenInclude(x => x.FbIgAutoReplyFiles)
                        .Include(x => x.AutomationActions)
                        .ThenInclude(x => x.FbIgAutoReply)
                        .ThenInclude(x => x.FbIgAutoReplyHistoryRecords)
                        .Where(
                            x =>
                                x.IsPreview
                                && x.Status == AutomationStatus.Draft
                                && x.PreviewCode != null
                                && x.PreviewCode == messaging.message.text
                                && x.CompanyId == companyId
                                && (x.AutomationType == AutomationType.InstagramMediaComment
                                    || x.AutomationType == AutomationType.FacebookPostComment)
                                && x.Conditions != null)
                        .OrderByDescending(x => x.UpdatedAt)
                        .ToListAsync();

                    foreach (var rule in assignmentRules)
                    {
                        if (rule.Conditions.Any(x => x.FieldName.ToLower() == "pageid" && x.Values.Contains(pageId)))
                        {
                            assignmentRule = rule;
                            break;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[{MethodName}] automation error: page id {FacebookPageId} message " +
                    "id {MessageMid} preview text {MessageText}: {ExceptionMessage}",
                    nameof(FbIgPreviewCodeTrigger),
                    pageId,
                    messaging.message.mid,
                    messaging.message.text,
                    ex.Message);
            }

            if (assignmentRule == null)
            {
                return;
            }

            var historyRecords = assignmentRule.AutomationActions
                .Where(x => x.FbIgAutoReply != null)
                .Select(x => x.FbIgAutoReply)
                .SelectMany(x => x.FbIgAutoReplyHistoryRecords)
                .ToList();

            var isPreviewMessageReplied = historyRecords.Any(
                x =>
                    x.PreviewCode == messaging.message.text
                    && x.PreviewMessageId == messaging.message.mid
                    && x.ErrorMessage == null);

            if (isPreviewMessageReplied)
            {
                return;
            }

            var automationAction = assignmentRule.AutomationActions
                .FirstOrDefault(
                x =>
                    x.AutomatedTriggerType == AutomatedTriggerType.InstagramInitiateDm
                    || x.AutomatedTriggerType == AutomatedTriggerType.FacebookInitiateDm
                    && x.FbIgAutoReply != null);
            if (automationAction == null)
            {
                return;
            }

            if (automationAction.AutomatedTriggerType == AutomatedTriggerType.FacebookInitiateDm)
            {
                var userProfile = await _appDbContext.UserProfiles
                    .Include(x => x.FacebookAccount)
                    .OrderByDescending(x => x.UpdatedAt)
                    .FirstOrDefaultAsync(
                        x =>
                            x.FacebookAccount != null
                            && x.FacebookAccount.FacebookId == messaging.sender.id
                            && x.FacebookAccount.pageId == messaging.recipient.id);

                bool isNewContact = userProfile == null;

                if (userProfile is { ActiveStatus: ActiveStatus.Inactive })
                {
                    await _userProfileSafeDeleteService.RecoverSoftDeletedUserProfilesAsync(
                        userProfile.CompanyId,
                        new HashSet<string>()
                        {
                            userProfile.Id
                        },
                        new UserProfileRecoveryTriggerContext(
                            UpdateUserProfileTriggerSource.AutomationAction,
                            null));
                }

                if (isNewContact
                    && !string.IsNullOrEmpty(messaging.sender.id)
                    && !string.IsNullOrEmpty(messaging.recipient.id))
                {
                    userProfile = await _facebookService.CreateUserProfile(messaging.sender.id, messaging.recipient.id);
                }

                var history = new AutomationHistory
                {
                    AssignmentRuleId = assignmentRule.Id,
                    CompanyId = userProfile.CompanyId,
                    Name = $"{userProfile.FirstName} {userProfile.LastName}".Trim(),
                    TargetUserProfileId = userProfile.Id,
                    TargetAssignmentRuleId = assignmentRule.AssignmentId,
                    IsSendMessage = false
                };

                await _appDbContext.CompanyAutomationHistories.AddAsync(history);
                await _appDbContext.SaveChangesAsync();

                var automationRecord = _mapper.Map<AutomationActionRecord>(automationAction);

                try
                {
                    await _facebookService.PreviewDmAutomation(automationAction.Id, messaging, isNewContact);
                    history.Status = AutomationHistoryStatus.Success;
                    automationRecord.Status = AutomationHistoryStatus.Success;
                }
                catch (Exception ex)
                {
                    history.UpdatedAt = DateTime.UtcNow;
                    history.Status = AutomationHistoryStatus.Failed;
                    history.ErrorMessage = ex.Message;

                    automationRecord.Status = AutomationHistoryStatus.Failed;
                    automationRecord.ErrorMessage = ex.Message;
                }

                history.UpdatedAt = DateTime.UtcNow;
                await _appDbContext.SaveChangesAsync();
            }
            else if (automationAction.AutomatedTriggerType == AutomatedTriggerType.InstagramInitiateDm)
            {
                var userProfile = await _appDbContext.UserProfiles
                    .Include(x => x.InstagramUser)
                    .OrderByDescending(x => x.UpdatedAt)
                    .FirstOrDefaultAsync(
                        x =>
                            x.InstagramUser != null
                            && x.InstagramUser.InstagramId == messaging.sender.id
                            && x.InstagramUser.InstagramPageId == messaging.recipient.id);

                bool isNewContact = userProfile == null;

                if (userProfile is { ActiveStatus: ActiveStatus.Inactive })
                {
                    await _userProfileSafeDeleteService.RecoverSoftDeletedUserProfilesAsync(
                        userProfile.CompanyId,
                        new HashSet<string>()
                        {
                            userProfile.Id
                        },
                        new UserProfileRecoveryTriggerContext(
                            UpdateUserProfileTriggerSource.AutomationAction,
                            null));
                }

                if (isNewContact
                    && !string.IsNullOrEmpty(messaging.sender.id)
                    && !string.IsNullOrEmpty(messaging.recipient.id))
                {
                    userProfile = await _instagramService.CreateUserProfile(
                        messaging.sender.id,
                        messaging.recipient.id);
                }

                var history = new AutomationHistory
                {
                    AssignmentRuleId = assignmentRule.Id,
                    CompanyId = userProfile.CompanyId,
                    Name = $"{userProfile.FirstName} {userProfile.LastName}".Trim(),
                    TargetUserProfileId = userProfile.Id,
                    TargetAssignmentRuleId = assignmentRule.AssignmentId,
                    IsSendMessage = false
                };

                await _appDbContext.CompanyAutomationHistories.AddAsync(history);
                await _appDbContext.SaveChangesAsync();

                var automationRecord = _mapper.Map<AutomationActionRecord>(automationAction);

                try
                {
                    await _instagramService.PreviewDmAutomation(automationAction.Id, messaging, isNewContact);
                    history.Status = AutomationHistoryStatus.Success;
                    automationRecord.Status = AutomationHistoryStatus.Success;
                }
                catch (Exception ex)
                {
                    history.UpdatedAt = DateTime.UtcNow;
                    history.Status = AutomationHistoryStatus.Failed;
                    history.ErrorMessage = ex.Message;

                    automationRecord.Status = AutomationHistoryStatus.Failed;
                    automationRecord.ErrorMessage = ex.Message;
                }

                history.UpdatedAt = DateTime.UtcNow;

                await _appDbContext.SaveChangesAsync();
            }
        }

        public async Task<ResponseWrapper> ReplayFbIgDmAction(FbIgAutoReplyHistoryRecord trigger)
        {
            var response = new ResponseWrapper();

            if (trigger.PlatformType == PlatformType.Facebook)
            {
                var config = await _appDbContext.ConfigFacebookConfigs
                    .FirstOrDefaultAsync(x => x.PageId == trigger.PageId);

                var accessToken = config.PageAccessToken;
                var companyId = config.CompanyId;

                if (trigger.FbIgAutoReply.MessageFormat == MessageFormat.Text)
                {
                    var bodyData = new PmBodyData()
                    {
                        Recipient = new FacebookMessageRecipient()
                        {
                            CommentId = trigger.ParentCommentId
                        },
                        Message = new Message()
                        {
                            Text = trigger.FbIgAutoReply.MessageContent
                        }
                    };

                    var privateReplyResponse = await _facebookService.PrivateReplyForComment(bodyData, accessToken);

                    if (privateReplyResponse.isSuccess)
                    {
                        trigger.ErrorMessage = null;
                        trigger.MessageId = privateReplyResponse.messageId;
                        trigger.UpdatedAt = DateTime.UtcNow;
                        await _appDbContext.SaveChangesAsync();
                        response.IsSuccess = true;
                        return response;
                    }
                    else
                    {
                        trigger.ErrorMessage = privateReplyResponse.errorMessage;
                        trigger.UpdatedAt = DateTime.UtcNow;
                        await _appDbContext.SaveChangesAsync();
                        response.ErrorMsg = trigger.ErrorMessage;
                        return response;
                    }
                }
                else if (trigger.FbIgAutoReply.MessageFormat == MessageFormat.Attachment)
                {
                    var fbIgAutoReplyAttachment = trigger.FbIgAutoReply.FbIgAutoReplyFiles[0];

                    var storageConfig = await _appDbContext.CompanyCompanies
                        .Where(x => x.Id == companyId)
                        .Select(x => x.StorageConfig)
                        .FirstOrDefaultAsync();

                    if (storageConfig == null)
                    {
                        _logger.LogWarning(
                            "[{MethodName}] Storage config not found for companyId {CompanyId}",
                            nameof(ReplayFbIgDmAction),
                            companyId);

                        response.ErrorMsg = $"storage config not found for companyId {companyId}";

                        return response;
                    }

                    var accessUrl = _azureBlobStorageService.GetAzureBlobSasUriForever(
                        trigger.FbIgAutoReply.FbIgAutoReplyFiles[0].Filename,
                        storageConfig.ContainerName,
                        true);

                    var fileType = fbIgAutoReplyAttachment.MIMEType;

                    if (fileType.Contains("image"))
                    {
                        fileType = "image";
                    }
                    else if (fileType.Contains("video"))
                    {
                        fileType = "video";
                    }
                    else if (fileType.Contains("audio"))
                    {
                        fileType = "audio";
                    }
                    else
                    {
                        fileType = "file";
                    }

                    var bodyData = new PmBodyData()
                    {
                        Recipient = new FacebookMessageRecipient()
                        {
                            CommentId = trigger.ParentCommentId
                        },
                        Message = new Message()
                        {
                            MessageAttachment = new MessageAttachment()
                            {
                                Type = fileType,
                                MessagePayload = new MessagePayload()
                                {
                                    Url = accessUrl
                                }
                            }
                        }
                    };

                    var privateReplyResponse =
                        await _facebookService.PrivateReplyForComment(bodyData, accessToken);

                    if (privateReplyResponse.isSuccess)
                    {
                        trigger.ErrorMessage = null;
                        trigger.MessageId = privateReplyResponse.messageId;
                        trigger.UpdatedAt = DateTime.UtcNow;
                        await _appDbContext.SaveChangesAsync();
                        response.IsSuccess = true;
                        return response;
                    }
                    else
                    {
                        trigger.ErrorMessage = privateReplyResponse.errorMessage;
                        trigger.UpdatedAt = DateTime.UtcNow;
                        await _appDbContext.SaveChangesAsync();

                        response.ErrorMsg = trigger.ErrorMessage;
                        return response;
                    }
                }
                else if (trigger.FbIgAutoReply.MessageFormat == MessageFormat.Carousel)
                {
                    MessageAttachment messageAttachment = trigger.FbIgAutoReply.MessageAttachment;

                    if (messageAttachment == null)
                    {
                        response.ErrorMsg = "Message Payload Not Found";
                        return response;
                    }

                    var carouselPayload = new CarouselPayload()
                    {
                        TemplateType = messageAttachment.MessagePayload.TemplateType,
                        Elements = messageAttachment.MessagePayload.Elements
                    };

                    var carouselAttachment = new CarouselAttachment()
                    {
                        Type = messageAttachment.Type, CarouselPayload = carouselPayload
                    };

                    var carouselBodyData = new CarouselBodyData()
                    {
                        Recipient = new FacebookMessageRecipient()
                        {
                            CommentId = trigger.ParentCommentId
                        },
                        CarouselMessage = new CarouselMessage()
                        {
                            CarouselAttachment = carouselAttachment
                        }
                    };

                    var privateReplyResponse =
                        await _facebookService.CarouselReplyForComment(carouselBodyData, accessToken);

                    if (privateReplyResponse.isSuccess)
                    {
                        trigger.ErrorMessage = null;
                        trigger.MessageId = privateReplyResponse.messageId;
                        trigger.UpdatedAt = DateTime.UtcNow;
                        await _appDbContext.SaveChangesAsync();

                        response.IsSuccess = true;
                        return response;
                    }
                    else
                    {
                        trigger.ErrorMessage = privateReplyResponse.errorMessage;
                        trigger.UpdatedAt = DateTime.UtcNow;
                        await _appDbContext.SaveChangesAsync();

                        response.ErrorMsg = trigger.ErrorMessage;
                        return response;
                    }
                }
                else if (trigger.FbIgAutoReply.MessageFormat == MessageFormat.QuickReplyButton)
                {
                    List<QuickReplyButton> quickReplyButtons = trigger.FbIgAutoReply.QuickReplyButtons;
                    if (quickReplyButtons == null)
                    {
                        response.ErrorMsg = "Message Payload Not Found";
                        return response;
                    }

                    var quickReplyButtonBodyData = new QuickReplyButtonBodyData()
                    {
                        Recipient = new FacebookMessageRecipient()
                        {
                            CommentId = trigger.ParentCommentId
                        },
                        QuickReplyButtonMessage = new QuickReplyButtonMessage()
                        {
                            Text = trigger.FbIgAutoReply.MessageContent,
                            QuickReplyButtons = trigger.FbIgAutoReply.QuickReplyButtons
                        }
                    };

                    var privateReplyResponse =
                        await _facebookService.QuickReplyButtonForComment(quickReplyButtonBodyData, accessToken);

                    if (privateReplyResponse.isSuccess)
                    {
                        trigger.ErrorMessage = null;
                        trigger.MessageId = privateReplyResponse.messageId;
                        trigger.UpdatedAt = DateTime.UtcNow;
                        await _appDbContext.SaveChangesAsync();

                        response.IsSuccess = true;
                        return response;
                    }
                    else
                    {
                        trigger.ErrorMessage = privateReplyResponse.errorMessage;
                        trigger.UpdatedAt = DateTime.UtcNow;
                        await _appDbContext.SaveChangesAsync();

                        response.ErrorMsg = trigger.ErrorMessage;
                        return response;
                    }
                }
                else
                {
                    MessageAttachment messageAttachment = trigger.FbIgAutoReply.MessageAttachment;
                    if (messageAttachment == null)
                    {
                        response.ErrorMsg = "Message Payload Not Found";
                        return response;
                    }

                    var buttonTemplateBodyData = new ButtonTemplateBodyData()
                    {
                        Recipient = new FacebookMessageRecipient()
                        {
                            CommentId = trigger.ParentCommentId
                        },
                        ButtonMessage = new ButtonMessage()
                        {
                            ButtonAttachment = new ButtonAttachment()
                            {
                                Type = messageAttachment.Type,
                                ButtonPayload = new ButtonPayload()
                                {
                                    TemplateType = messageAttachment.MessagePayload
                                        .TemplateType,
                                    Text = messageAttachment.MessagePayload.Text,
                                    Buttons = messageAttachment.MessagePayload.Buttons
                                }
                            }
                        }
                    };

                    var privateReplyResponse =
                        await _facebookService.ButtonTemplateForComment(buttonTemplateBodyData, accessToken);

                    if (privateReplyResponse.isSuccess)
                    {
                        trigger.ErrorMessage = null;
                        trigger.MessageId = privateReplyResponse.messageId;
                        trigger.UpdatedAt = DateTime.UtcNow;
                        await _appDbContext.SaveChangesAsync();

                        response.IsSuccess = true;
                        return response;
                    }
                    else
                    {
                        trigger.ErrorMessage = privateReplyResponse.errorMessage;
                        trigger.UpdatedAt = DateTime.UtcNow;
                        await _appDbContext.SaveChangesAsync();

                        response.ErrorMsg = trigger.ErrorMessage;
                        return response;
                    }
                }
            }
            else
            {
                var config = await _appDbContext.ConfigInstagramConfigs
                    .FirstOrDefaultAsync(x => x.InstagramPageId == trigger.PageId);

                var accessToken = config.PageAccessToken;
                var companyId = config.CompanyId;

                if (trigger.FbIgAutoReply.MessageFormat == MessageFormat.Text)
                {
                    var bodyData = new PmBodyData()
                    {
                        Recipient = new FacebookMessageRecipient()
                        {
                            CommentId = trigger.ParentCommentId
                        },
                        Message = new Message()
                        {
                            Text = trigger.FbIgAutoReply.MessageContent
                        }
                    };

                    var privateReplyResponse =
                        await _instagramService.PrivateReplyForComment(bodyData, accessToken);

                    if (privateReplyResponse.isSuccess)
                    {
                        trigger.ErrorMessage = null;
                        trigger.MessageId = privateReplyResponse.messageId;
                        trigger.UpdatedAt = DateTime.UtcNow;
                        await _appDbContext.SaveChangesAsync();

                        response.IsSuccess = true;
                        return response;
                    }
                    else
                    {
                        trigger.ErrorMessage = privateReplyResponse.errorMessage;
                        trigger.UpdatedAt = DateTime.UtcNow;
                        await _appDbContext.SaveChangesAsync();

                        response.ErrorMsg = trigger.ErrorMessage;
                        return response;
                    }
                }
                else if (trigger.FbIgAutoReply.MessageFormat == MessageFormat.Attachment)
                {
                    var fbIgAutoReplyAttachment = trigger.FbIgAutoReply.FbIgAutoReplyFiles[0];

                    var storageConfig = await _appDbContext.CompanyCompanies
                        .Where(x => x.Id == companyId).Select(x => x.StorageConfig)
                        .FirstOrDefaultAsync();

                    if (storageConfig == null)
                    {
                        _logger.LogError(
                            "Storage config not found for companyId {CompanyId}",
                            companyId);

                        response.ErrorMsg = $"storage config not found for companyId {companyId}";

                        return response;
                    }

                    var accessUrl = _azureBlobStorageService.GetAzureBlobSasUriForever(
                        trigger.FbIgAutoReply.FbIgAutoReplyFiles[0].Filename,
                        storageConfig.ContainerName,
                        true);

                    var fileType = fbIgAutoReplyAttachment.MIMEType;

                    if (fileType.Contains("image")) // ig only support file url for image type only
                    {
                        fileType = "image";
                    }

                    var bodyData = new PmBodyData()
                    {
                        Recipient = new FacebookMessageRecipient()
                        {
                            CommentId = trigger.ParentCommentId
                        },
                        Message = new Message()
                        {
                            MessageAttachment = new MessageAttachment()
                            {
                                Type = fileType,
                                MessagePayload = new MessagePayload()
                                {
                                    Url = accessUrl
                                }
                            }
                        }
                    };

                    var privateReplyResponse =
                        await _instagramService.PrivateReplyForComment(bodyData, accessToken);

                    if (privateReplyResponse.isSuccess)
                    {
                        trigger.ErrorMessage = null;
                        trigger.MessageId = privateReplyResponse.messageId;
                        trigger.UpdatedAt = DateTime.UtcNow;
                        await _appDbContext.SaveChangesAsync();

                        response.IsSuccess = true;
                        return response;
                    }
                    else
                    {
                        trigger.ErrorMessage = privateReplyResponse.errorMessage;
                        trigger.UpdatedAt = DateTime.UtcNow;
                        await _appDbContext.SaveChangesAsync();

                        response.ErrorMsg = trigger.ErrorMessage;
                        return response;
                    }
                }
                else if (trigger.FbIgAutoReply.MessageFormat == MessageFormat.Carousel)
                {
                    MessageAttachment messageAttachment = trigger.FbIgAutoReply.MessageAttachment;

                    if (messageAttachment == null)
                    {
                        response.ErrorMsg = "Message Payload Not Found";
                        return response;
                    }

                    var carouselPayload = new CarouselPayload()
                    {
                        TemplateType = messageAttachment.MessagePayload.TemplateType,
                        Elements = messageAttachment.MessagePayload.Elements
                    };

                    var carouselAttachment = new CarouselAttachment()
                    {
                        Type = messageAttachment.Type, CarouselPayload = carouselPayload
                    };

                    var carouselBodyData = new CarouselBodyData()
                    {
                        MessagingType = "RESPONSE",
                        Recipient = new FacebookMessageRecipient()
                        {
                            CommentId = trigger.ParentCommentId
                        },
                        CarouselMessage = new CarouselMessage()
                        {
                            CarouselAttachment = carouselAttachment
                        }
                    };

                    var privateReplyResponse =
                        await _instagramService.CarouselReplyForComment(carouselBodyData, accessToken);

                    if (privateReplyResponse.isSuccess)
                    {
                        trigger.ErrorMessage = null;
                        trigger.MessageId = privateReplyResponse.messageId;
                        trigger.UpdatedAt = DateTime.UtcNow;
                        await _appDbContext.SaveChangesAsync();

                        response.IsSuccess = true;
                        return response;
                    }
                    else
                    {
                        trigger.ErrorMessage = privateReplyResponse.errorMessage;
                        trigger.UpdatedAt = DateTime.UtcNow;
                        await _appDbContext.SaveChangesAsync();

                        response.ErrorMsg = trigger.ErrorMessage;
                        return response;
                    }
                }
                else
                {
                    List<QuickReplyButton> quickReplyButtons = trigger.FbIgAutoReply.QuickReplyButtons;

                    if (quickReplyButtons == null)
                    {
                        response.ErrorMsg = "Message Payload Not Found";
                        return response;
                    }

                    var quickReplyButtonBodyData = new QuickReplyButtonBodyData()
                    {
                        Recipient = new FacebookMessageRecipient()
                        {
                            CommentId = trigger.ParentCommentId
                        },
                        QuickReplyButtonMessage = new QuickReplyButtonMessage()
                        {
                            Text = trigger.FbIgAutoReply.MessageContent,
                            QuickReplyButtons = trigger.FbIgAutoReply.QuickReplyButtons
                        }
                    };

                    var privateReplyResponse =
                        await _instagramService.QuickReplyButtonForComment(quickReplyButtonBodyData, accessToken);

                    if (privateReplyResponse.isSuccess)
                    {
                        trigger.ErrorMessage = null;
                        trigger.MessageId = privateReplyResponse.messageId;
                        trigger.UpdatedAt = DateTime.UtcNow;
                        await _appDbContext.SaveChangesAsync();

                        response.IsSuccess = true;
                        return response;
                    }
                    else
                    {
                        trigger.ErrorMessage = privateReplyResponse.errorMessage;
                        trigger.UpdatedAt = DateTime.UtcNow;
                        await _appDbContext.SaveChangesAsync();

                        response.ErrorMsg = trigger.ErrorMessage;
                        return response;
                    }
                }
            }
        }

        public async Task FbIcebreakerTrigger(FBMessaging messaging)
        {
            if (messaging.postback.Title == null
                || messaging.postback.Payload == null
                || messaging.recipient.id == null)
            {
                return;
            }

            var icebreaker = await _appDbContext.FbIgIcebreakers
                .Include(x => x.IcebreakerReplyRules)
                .ThenInclude(x => x.IcebreakerHistoryRecords)
                .Include(x => x.AssignmentRule)
                .ThenInclude(x => x.AutomationActions)
                .ThenInclude(x => x.FbIgAutoReply)
                .OrderByDescending(x => x.UpdatedAt)
                .FirstOrDefaultAsync(
                    x =>
                        x.pageId == messaging.recipient.id
                        && x.AssignmentRule.Status == AutomationStatus.Live);

            if (icebreaker == null ||
                icebreaker.IcebreakerReplyRules == null)
            {
                return;
            }

            var icebreakerReplyRule = icebreaker.IcebreakerReplyRules?
                .OrderByDescending(x => x.UpdatedAt)
                .FirstOrDefault(
                    x =>
                        x.FbIgIcebreakerId == icebreaker.Id
                        && x.PostbackTitle == messaging.postback.Title
                        && x.FbIgIcebreaker.pageId == messaging.recipient.id
                        && x.FbIgIcebreaker.PlatformType == PlatformType.Facebook
                        && x.IsActive);

            if (icebreakerReplyRule == null)
            {
                return;
            }

            if (icebreakerReplyRule.IcebreakerHistoryRecords.Any(x => x.MessageId == messaging.postback.Mid))
            {
                return;
            }

            var companyId = icebreaker.AssignmentRule.CompanyId;

            var icebreakerHistoryRecord = new IcebreakerHistoryRecord()
            {
                IcebreakerReplyRuleId = icebreakerReplyRule.Id,
                IcebreakerReplyRule = icebreakerReplyRule,
                MessageId = messaging.postback.Mid,
                Title = messaging.postback.Title,
                Payload = messaging.postback.Payload,
                PageId = messaging.recipient.id,
                CompanyId = companyId,
                SenderId = messaging.sender.id
            };

            try
            {
                _appDbContext.IcebreakerHistoryRecords.Add(icebreakerHistoryRecord);
                await _appDbContext.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Error creating Facebook icebreaker click historical records {IceBreakId}",
                    icebreaker.Id);
            }

            if (icebreakerReplyRule.AutomationActionIdList != null
                && icebreakerReplyRule.AutomationActionIdList.Any())
            {
                var automationActions = icebreaker.AssignmentRule.AutomationActions
                    .Where(x => icebreakerReplyRule.AutomationActionIdList.Contains(x.Id))
                    .ToList();

                if (automationActions.Any())
                {
                    var userProfile = await _appDbContext.UserProfiles
                        .Include(x => x.FacebookAccount)
                        .OrderByDescending(x => x.UpdatedAt)
                        .FirstOrDefaultAsync(
                            x =>
                                x.FacebookAccount != null
                                && x.FacebookAccount.FacebookId == messaging.sender.id
                                && x.FacebookAccount.pageId == messaging.recipient.id);

                    bool isNewContact = userProfile == null;

                    if (userProfile is { ActiveStatus: ActiveStatus.Inactive })
                    {
                        await _userProfileSafeDeleteService.RecoverSoftDeletedUserProfilesAsync(
                            companyId,
                            new HashSet<string>()
                            {
                                userProfile.Id
                            },
                            new UserProfileRecoveryTriggerContext(
                                UpdateUserProfileTriggerSource.AutomationAction,
                                null));
                    }

                    if (isNewContact
                        && !string.IsNullOrEmpty(messaging.sender.id)
                        && !string.IsNullOrEmpty(messaging.recipient.id))
                    {
                        userProfile = await _facebookService.CreateUserProfile(
                            messaging.sender.id,
                            messaging.recipient.id);
                    }

                    if (userProfile != null)
                    {
                        await IcebreakerTriggerAction(
                            messaging,
                            userProfile,
                            icebreakerReplyRule.FbIgIcebreaker.AssignmentRule,
                            icebreakerReplyRule.AutomationActionIdList,
                            isNewContact);
                    }
                }
            }
        }

        public async Task IgIcebreakerTrigger(FBMessaging messaging)
        {
            var icebreaker = await _appDbContext.FbIgIcebreakers
                .Include(x => x.IcebreakerReplyRules)
                .Include(x => x.AssignmentRule)
                    .ThenInclude(x => x.AutomationActions)
                    .ThenInclude(x => x.FbIgAutoReply)
                .OrderByDescending(x => x.UpdatedAt)
                .FirstOrDefaultAsync(
                    x =>
                        x.pageId == messaging.recipient.id
                        && x.AssignmentRule.Status == AutomationStatus.Live);

            if (icebreaker == null ||
                icebreaker.IcebreakerReplyRules == null)
            {
                return;
            }

            var icebreakerReplyRule = icebreaker.IcebreakerReplyRules?
                .OrderByDescending(x => x.UpdatedAt)
                .FirstOrDefault(
                    x =>
                        x.FbIgIcebreakerId == icebreaker.Id
                        && x.PostbackTitle == messaging.postback.Title
                        && x.FbIgIcebreaker.pageId == messaging.recipient.id
                        && x.FbIgIcebreaker.PlatformType == PlatformType.Instagram
                        && x.IsActive);

            if (icebreakerReplyRule == null)
            {
                return;
            }

            if (icebreakerReplyRule.IcebreakerHistoryRecords.Any(x => x.MessageId == messaging.postback.Mid))
            {
                return;
            }

            var companyId = icebreaker.AssignmentRule.CompanyId;

            var icebreakerHistoryRecord = new IcebreakerHistoryRecord()
            {
                IcebreakerReplyRuleId = icebreakerReplyRule.Id,
                IcebreakerReplyRule = icebreakerReplyRule,
                MessageId = messaging.postback.Mid,
                Title = messaging.postback.Title,
                Payload = messaging.postback.Payload,
                PageId = messaging.recipient.id,
                CompanyId = companyId,
                SenderId = messaging.sender.id
            };

            try
            {
                _appDbContext.IcebreakerHistoryRecords.Add(icebreakerHistoryRecord);
                await _appDbContext.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Error creating Instagram icebreaker click historical records {IceBreakId}",
                    icebreaker.Id);
            }

            if (icebreakerReplyRule.AutomationActionIdList != null
                && icebreakerReplyRule.AutomationActionIdList.Any())
            {
                var automationActions = icebreaker.AssignmentRule.AutomationActions
                    .Where(x => icebreakerReplyRule.AutomationActionIdList.Contains(x.Id))
                    .ToList();

                if (automationActions.Any())
                {
                    var userProfile = await _appDbContext.UserProfiles
                        .Include(x => x.InstagramUser)
                        .OrderByDescending(x => x.UpdatedAt)
                        .FirstOrDefaultAsync(
                            x =>
                                x.InstagramUser != null
                                && x.InstagramUser.InstagramId == messaging.sender.id
                                && x.InstagramUser.InstagramPageId == messaging.recipient.id);

                    bool isNewContact = userProfile == null;

                    if (userProfile is { ActiveStatus: ActiveStatus.Inactive })
                    {
                        await _userProfileSafeDeleteService.RecoverSoftDeletedUserProfilesAsync(
                            companyId,
                            new HashSet<string>()
                            {
                                userProfile.Id
                            },
                            new UserProfileRecoveryTriggerContext(
                                UpdateUserProfileTriggerSource.AutomationAction,
                                null));
                    }

                    if (isNewContact
                        && !string.IsNullOrEmpty(messaging.sender.id)
                        && !string.IsNullOrEmpty(messaging.recipient.id))
                    {
                        userProfile = await _instagramService.CreateUserProfile(
                            messaging.sender.id,
                            messaging.recipient.id);
                    }

                    if (userProfile != null)
                    {
                        await IcebreakerTriggerAction(
                            messaging,
                            userProfile,
                            icebreakerReplyRule.FbIgIcebreaker.AssignmentRule,
                            icebreakerReplyRule.AutomationActionIdList,
                            isNewContact);
                    }
                }
            }
        }

        public async Task ConversationAssignment(
            Conversation conversation,
            ConversationMessage conversationMesasge,
            bool isTriggerUpdate)
        {
            if (conversation.Assignee == null)
            {
                var assignment = await _appDbContext.CompanyAssignmentRules
                    .Where(
                        x =>
                            x.CompanyId == conversation.CompanyId
                            && x.AutomationType == AutomationType.Assignment
                            && x.Status == AutomationStatus.Live)
                    .Include(x => x.AssignedStaff)
                    .Include(x => x.AssignedTeam)
                    .Include(x => x.AutomationActions)
                    .OrderBy(x => x.Order)
                    .ToListAsync();

                var userProfileIdStatus = await GetUserProfileIdStatusByIdAsync(
                    conversation.CompanyId,
                    conversation.UserProfileId);

                if (userProfileIdStatus.ActiveStatus != ActiveStatus.Active)
                {
                    return;
                }

                try
                {
                    var contactOwner = await _userProfileService.GetCustomFieldByFieldName(
                        conversation.UserProfileId,
                        "ContactOwner");

                    if (contactOwner != null)
                    {
                        var assignee = await _appDbContext.UserRoleStaffs
                            .FirstOrDefaultAsync(
                                x =>
                                    x.CompanyId == conversation.CompanyId
                                    && x.IdentityId == contactOwner.Value);

                        if (assignee != null)
                        {
                            await _conversationMessageService.ChangeConversationAssignee(
                                conversation,
                                assignee,
                                isTriggerUpdate);

                            return;
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(
                        ex,
                        "[Automation {MethodName}] error when changing conversation {ConversationId} assignee: {ExceptionMessage}",
                        nameof(ConversationAssignment),
                        conversation.Id,
                        ex.Message);
                }

                var defaultAssignment = assignment
                    .FirstOrDefault(
                        x =>
                            (x.TargetedChannels == null || x.TargetedChannels?.Count == 0)
                            && (x.TargetedChannelWithIds == null || x.TargetedChannelWithIds?.Count == 0)
                            && (x.Conditions == null || x.Conditions?.Count == 0));

                if (defaultAssignment != null
                    && !conversation.AssigneeId.HasValue
                    && !conversation.AssignedTeamId.HasValue)
                {
                    var defaultAssignmentLockKey = $"automation:default-assignment:{conversation.Id}";
                    var defaultAssignmentLock = await _lockService.AcquireLockAsync(
                        defaultAssignmentLockKey,
                        TimeSpan.FromSeconds(5));
                    if (defaultAssignmentLock is null)
                    {
                        return;
                    }

                    await AssignAssignee(conversation, defaultAssignment, conversationMesasge, isTriggerUpdate);
                }
            }
        }

        public Task NewContactTrigger(string userProfileId)
        {
            return RunNewContactTrigger(userProfileId, null);
        }

        public Task NewContactTrigger(string userProfileId, AutomationType automationType)
        {
            return RunNewContactTrigger(userProfileId, automationType);
        }

        private async Task RunNewContactTrigger(string userProfileId, AutomationType? automationType)
        {
            try
            {
                var userProfile = await _appDbContext.UserProfiles
                    .FirstOrDefaultAsync(x => x.Id == userProfileId);

                var userProfileIdStatus = await GetUserProfileIdStatusByIdAsync(userProfile.CompanyId, userProfile.Id);

                if (userProfileIdStatus.ActiveStatus != ActiveStatus.Active)
                {
                    return;
                }

                var automatedMessageTriggers = await _appDbContext.CompanyAssignmentRules
                    .Where(
                        x =>
                            x.CompanyId == userProfile.CompanyId
                            && x.AutomationType == AutomationType.ContactAdded
                            && x.Status == AutomationStatus.Live)
                    .Include(x => x.AutomationActions)
                    .OrderBy(x => x.Order)
                    .ToListAsync();

                if (automatedMessageTriggers.Count > 0)
                {
                    foreach (var automatedMessageTrigger in automatedMessageTriggers)
                    {
                        if (automatedMessageTrigger.TargetedChannels?.Count > 0 ||
                            automatedMessageTrigger.TargetedChannelWithIds?.Count > 0 ||
                            automatedMessageTrigger.Conditions?.Count > 0)
                        {
                            if (automationType == AutomationType.CrmHubOnEntityCreated)
                            {
                                var assignmentRuleResult = await _userProfileService.EvaluateConditionsInCrmHub(
                                    userProfile,
                                    automatedMessageTrigger.Conditions);

                                if (assignmentRuleResult)
                                {
                                    await TriggerAction(userProfile.Id, automatedMessageTrigger.AssignmentId);

                                    if (!automatedMessageTrigger.IsContinue)
                                    {
                                        return;
                                    }
                                }
                            }
                            else
                            {
                                var (assignmentRuleResult, _) = await _userProfileService.CompareConditionUserProfile(
                                    userProfile,
                                    automatedMessageTrigger.Conditions,
                                    automationType);

                                if (assignmentRuleResult)
                                {
                                    await TriggerAction(
                                        userProfile.Id,
                                        automatedMessageTrigger.AssignmentId);

                                    if (!automatedMessageTrigger.IsContinue)
                                    {
                                        return;
                                    }
                                }
                            }
                        }
                        else
                        {
                            await TriggerAction(
                                userProfile.Id,
                                automatedMessageTrigger.AssignmentId);

                            if (!automatedMessageTrigger.IsContinue)
                            {
                                return;
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[{MethodName}] error for user profile {UserProfileId}: {ExceptionMessage}",
                    nameof(RunNewContactTrigger),
                    userProfileId,
                    ex.Message);
            }
        }

        public async Task TriggerAction(
            string userProfileId,
            string assignmentRuleId,
            bool isSendMessage = true,
            string regexValue = null,
            long? conversationMessageId = null,
            long? replayId = null)
        {
            try
            {
                var key = $"triggered:{userProfileId}_{assignmentRuleId}_{conversationMessageId}";
                ILockService.Lock triggerLock = await _lockService.AcquireLockAsync(key, TimeSpan.FromSeconds(15));

                if (triggerLock != null)
                {
                    var userProfile = await _appDbContext.UserProfiles
                        .FirstOrDefaultAsync(x => x.Id == userProfileId);

                    var automationRule = await _appDbContext.CompanyAssignmentRules
                        .Include(x => x.AutomationActions)
                        .FirstOrDefaultAsync(
                            x =>
                                x.CompanyId == userProfile.CompanyId
                                && x.AssignmentId == assignmentRuleId
                                && x.Status == AutomationStatus.Live);

                    await TriggerAction(
                        userProfile,
                        automationRule,
                        isSendMessage,
                        regexValue,
                        conversationMessageId,
                        replayId);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[{MethodName}] error when executing assignment rule {AssignmentRuleId} for user profile {UserProfileId}: {ExceptionMessage}",
                    nameof(TriggerAction),
                    assignmentRuleId,
                    userProfileId,
                    ex.Message);

                throw;
            }
        }

        public async Task TriggerAction(
            UserProfile userProfile,
            AssignmentRule automationRule,
            bool isSendMessage = true,
            string regexValue = null,
            long? conversationMessageId = null,
            long? replayId = null)
        {
            using (LogContext.PushProperty("SleekflowAutomationRuleId", automationRule.Id))
            {AutomationHistory history = null;
            ILockService.Lock automationHistoryLock = null;

            var userProfileIdStatus = await GetUserProfileIdStatusByIdAsync(userProfile.CompanyId, userProfile.Id);

            if (userProfileIdStatus.ActiveStatus != ActiveStatus.Active)
            {
                return;
            }

            try
            {
                try
                {
                    if (!replayId.HasValue)
                    {
                        history = new AutomationHistory
                        {
                            CompanyId = userProfile.CompanyId,
                            Name = $"{userProfile.FirstName} {userProfile.LastName}".Trim(),
                            TargetUserProfileId = userProfile.Id,
                            TargetAssignmentRuleId = automationRule.AssignmentId,
                            IsSendMessage = isSendMessage,
                            RegexValue = regexValue,
                            ConversationMessageId = conversationMessageId,
                            Status = AutomationHistoryStatus.Success,
                        };

                            automationRule.AutomationHistories.Add(history);
                            await _appDbContext.SaveChangesAsync();
                        }
                        else
                        {
                            automationHistoryLock = await _lockService.WaitUntilLockAcquiredAsync(
                                $"automation:history:{replayId}",
                                TimeSpan.FromSeconds(15));
                            history = await _appDbContext.CompanyAutomationHistories
                                .FirstOrDefaultAsync(
                                    x =>
                                        x.Id == replayId
                                        && x.CompanyId == userProfile.CompanyId);

                            history.Status = AutomationHistoryStatus.Success;
                            history.UpdatedAt = DateTime.UtcNow;

                            await _appDbContext.SaveChangesAsync();
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(
                            ex,
                            "[{MethodName}] Error updating automation rule {AssignmentRuleId} automation " +
                            "history {AutomationHistoryId} for user profile {UserProfileId} to success: {ExceptionMessage}",
                            nameof(TriggerAction),
                            automationRule?.Id,
                            history?.Id,
                            userProfile?.Id,
                            ex.Message);

                        if (automationRule.AutomationType == AutomationType.RecurringJob)
                        {
                            throw;
                        }
                    }

                    try
                    {
                        if (history is null || history.Id == 0)
                        {
                            _logger.LogWarning(
                                "[{MethodName}] Automation rule {AssignmentRuleId} history is not found or invalid for user profile {UserProfileId}",
                                nameof(TriggerAction),
                                automationRule?.Id,
                                userProfile?.Id);
                            return;
                        }

                        foreach (var automationTrigger in automationRule.AutomationActions
                                     .OrderBy(x => x.Order)
                                     .ThenBy(x => x.Id))
                        {
                            using (var scope = _serviceProvider.CreateScope())
                            {
                                var automationService =
                                    scope.ServiceProvider.GetRequiredService<IAutomationService>();
                                await automationService.TriggerAutomationAction(
                                    userProfile.Id,
                                    automationRule.Id,
                                    automationTrigger.Id,
                                    true,
                                    isSendMessage,
                                    regexValue,
                                    conversationMessageId,
                                    history.Id);
                            }
                        }

                        if (automationRule.AssignmentType != AssignmentType.Unassigned)
                        {
                            var conversation = await _userProfileService.GetConversationByUserProfileId(
                                userProfile.CompanyId,
                                userProfile.Id,
                                "closed");

                            var message = await _appDbContext.ConversationMessages
                                .OrderByDescending(x => x.Id)
                                .FirstOrDefaultAsync(x => x.ConversationId == conversation.Id);

                            await AssignAssignee(conversation, automationRule, message, false);
                        }

                        if (!replayId.HasValue)
                        {
                            automationRule.TriggeredCounter++;

                            if (await _appDbContext.CompanyAutomationActionRecords
                                    .AnyAsync(
                                        x =>
                                            x.AutomationHistoryId == history.Id
                                            && x.Status == AutomationHistoryStatus.Failed))
                            {
                                automationRule.TriggeredFailedCounter++;
                            }
                            else
                            {
                                automationRule.TriggeredSuccessCounter++;
                            }

                            // await _lockService.RemoveCompanyInfoCache(automationRule.CompanyId, "AssignmentRuleInfo");
                            await _appDbContext.SaveChangesAsync();
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(
                            ex,
                            "[{MethodName}] Automation rule {AssignmentRuleId} failed for {UserProfileId}: {ExceptionMessage}",
                            nameof(TriggerAction),
                            automationRule?.Id,
                            userProfile?.Id,
                            ex.Message);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(
                        ex,
                        "[{MethodName}] Automation rule {AssignmentRuleId} general exception for {UserProfileId}: {ExceptionMessage}",
                        nameof(TriggerAction),
                        automationRule?.Id,
                        userProfile?.Id,
                        ex.Message);

                    if (automationRule.AutomationType == AutomationType.RecurringJob)
                    {
                        throw;
                    }
                }
                finally
                {
                    await _lockService.ReleaseLockAsync(automationHistoryLock);
                }
            }
        }

        public async Task FbIgCommentTriggerAction(
            Entry entry,
            Change change,
            UserProfile userProfile,
            AssignmentRule automationRule,
            bool isNewContact,
            bool isSendMessage = false,
            long? replayId = null)
        {
            AutomationHistory history = null;
            ILockService.Lock automationHistoryLock = null;
            try
            {
                if (!replayId.HasValue)
                {
                    history = new AutomationHistory
                    {
                        CompanyId = userProfile.CompanyId,
                        Name = $"{userProfile.FirstName} {userProfile.LastName}".Trim(),
                        TargetUserProfileId = userProfile.Id,
                        TargetAssignmentRuleId = automationRule.AssignmentId,
                        IsSendMessage = isSendMessage,
                        Status = AutomationHistoryStatus.Success,
                    };

                    automationRule.AutomationHistories.Add(history);
                }
                else
                {
                    automationHistoryLock = await _lockService.WaitUntilLockAcquiredAsync(
                        $"automation:history:{replayId}",
                        TimeSpan.FromSeconds(15));
                    history = await _appDbContext.CompanyAutomationHistories
                        .FirstOrDefaultAsync(
                            x =>
                                x.Id == replayId
                                && x.CompanyId == userProfile.CompanyId);
                }

                history.Status = AutomationHistoryStatus.Success;
                history.UpdatedAt = DateTime.UtcNow;

                await _appDbContext.SaveChangesAsync();

                try
                {
                    foreach (var automationTrigger in automationRule.AutomationActions
                                 .OrderBy(x => x.Order)
                                 .ThenBy(x => x.Id))
                    {
                        var userProfileIdStatus = await GetUserProfileIdStatusByIdAsync(userProfile.CompanyId, userProfile.Id);

                        if (userProfileIdStatus.ActiveStatus != ActiveStatus.Active)
                        {
                            break;
                        }

                        await TriggerFbIgCommentAutomationAction(
                            entry,
                            change,
                            userProfile.Id,
                            automationRule.Id,
                            automationTrigger.Id,
                            isNewContact,
                            true,
                            isSendMessage,
                            history.Id);
                    }

                    if (automationRule.AssignmentType != AssignmentType.Unassigned)
                    {
                        var conversation = await _userProfileService.GetConversationByUserProfileId(
                            userProfile.CompanyId,
                            userProfile.Id,
                            "closed");

                        var message = await _appDbContext.ConversationMessages
                            .OrderByDescending(x => x.Id)
                            .FirstOrDefaultAsync(x => x.ConversationId == conversation.Id);

                        await AssignAssignee(conversation, automationRule, message, false);
                    }

                    if (!replayId.HasValue)
                    {
                        automationRule.TriggeredCounter++;

                        if (await _appDbContext.CompanyAutomationActionRecords
                                .AnyAsync(
                                    x =>
                                        x.AutomationHistoryId == history.Id
                                        && x.Status == AutomationHistoryStatus.Failed))
                        {
                            automationRule.TriggeredFailedCounter++;
                        }
                        else
                        {
                            automationRule.TriggeredSuccessCounter++;
                        }

                        await _appDbContext.SaveChangesAsync();
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(
                        ex,
                        "[{MethodName}] Automation failed for user profile {UserProfileId}, automation rule {AutomationRuleId}, entry {EntryId}, page {PageId}: {ExceptionMessage}",
                        nameof(FbIgCommentTriggerAction),
                        userProfile?.Id,
                        automationRule?.Id,
                        entry.Id,
                        change.Value.PageId,
                        ex.Message);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[{MethodName}] Automation general exceptionn for user profile {UserProfileId}, automation rule {AutomationRuleId}, entry {EntryId}, page {PageId}: {ExceptionMessage}",
                    nameof(FbIgCommentTriggerAction),
                    userProfile?.Id,
                    automationRule?.Id,
                    entry.Id,
                    change.Value.PageId,
                    ex.Message);
            }
            finally
            {
                await _lockService.ReleaseLockAsync(automationHistoryLock);
            }
        }

        public async Task IcebreakerTriggerAction(
            FBMessaging messaging,
            UserProfile userProfile,
            AssignmentRule automationRule,
            long[] automationActionIdList,
            bool isNewContact,
            bool isSendMessage = false,
            long? replayId = null)
        {
            AutomationHistory history = null;
            ILockService.Lock automationHistoryLock = null;
            try
            {
                if (!replayId.HasValue)
                {
                    history = new AutomationHistory
                    {
                        CompanyId = userProfile.CompanyId,
                        Name = $"{userProfile.FirstName} {userProfile.LastName}".Trim(),
                        TargetUserProfileId = userProfile.Id,
                        TargetAssignmentRuleId = automationRule.AssignmentId,
                        IsSendMessage = isSendMessage,
                        Status = AutomationHistoryStatus.Success,
                    };

                    automationRule.AutomationHistories.Add(history);
                }
                else
                {
                    automationHistoryLock = await _lockService.WaitUntilLockAcquiredAsync(
                        $"automation:history:{replayId}",
                        TimeSpan.FromSeconds(15));
                    history = await _appDbContext.CompanyAutomationHistories
                        .FirstOrDefaultAsync(
                            x =>
                                x.Id == replayId
                                && x.CompanyId == userProfile.CompanyId);
                }

                history.Status = AutomationHistoryStatus.Success;
                history.UpdatedAt = DateTime.UtcNow;

                await _appDbContext.SaveChangesAsync();

                var autoActionList = await _appDbContext.CompanyAutomationActions
                    .Where(x => automationActionIdList.Contains(x.Id))
                    .OrderBy(x => x.Order)
                    .ThenBy(x => x.Id)
                    .ToListAsync();

                try
                {
                    foreach (var automationTrigger in autoActionList)
                    {
                        var userProfileIdStatus = await GetUserProfileIdStatusByIdAsync(
                            userProfile.CompanyId,
                            userProfile.Id);

                        if (userProfileIdStatus.ActiveStatus != ActiveStatus.Active)
                        {
                            break;
                        }

                        await TriggerFbIgIcebreakerAutomationAction(
                            messaging,
                            userProfile.Id,
                            automationRule.Id,
                            automationTrigger.Id,
                            isNewContact,
                            true,
                            false,
                            history.Id);
                    }

                    if (automationRule.AssignmentType != AssignmentType.Unassigned)
                    {
                        var conversation = await _userProfileService.GetConversationByUserProfileId(
                            userProfile.CompanyId,
                            userProfile.Id,
                            "closed");

                        var message = await _appDbContext.ConversationMessages
                            .OrderByDescending(x => x.Id)
                            .FirstOrDefaultAsync(x => x.ConversationId == conversation.Id);

                        await AssignAssignee(conversation, automationRule, message, false);
                    }

                    if (!replayId.HasValue)
                    {
                        automationRule.TriggeredCounter++;

                        if (await _appDbContext.CompanyAutomationActionRecords
                                .AnyAsync(
                                    x =>
                                        x.AutomationHistoryId == history.Id
                                        && x.Status == AutomationHistoryStatus.Failed))
                        {
                            automationRule.TriggeredFailedCounter++;
                            history.Status = AutomationHistoryStatus.Failed;
                        }
                        else
                        {
                            automationRule.TriggeredSuccessCounter++;
                            history.Status = AutomationHistoryStatus.Success;
                        }

                        await _appDbContext.SaveChangesAsync();
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(
                        ex,
                        "[{MethodName}] Automation failed for user profile {UserProfileId}, automation rule {AssignmentRuleId}, " +
                        "sender: {SenderId}, page {PageId}: {ExceptionMessage}",
                        nameof(IcebreakerTriggerAction),
                        userProfile?.Id,
                        automationRule?.Id,
                        messaging.sender.id,
                        messaging.recipient.id,
                        ex.Message);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[{MethodName}] Automation general exception for user profile {UserProfileId}, automation rule {AssignmentRuleId}, " +
                    "sender: {SenderId}, page {PageId}: {ExceptionMessage}",
                    nameof(IcebreakerTriggerAction),
                    userProfile?.Id,
                    automationRule?.Id,
                    messaging.sender.id,
                    messaging.recipient.id,
                    ex.Message);
            }
            finally
            {
                await _lockService.ReleaseLockAsync(automationHistoryLock);
            }
        }

        public async Task TriggerFbIgCommentAutomationAction(
            Entry entry,
            Change change,
            string userProfileId,
            long assignmentId,
            long automationId,
            bool isNewContact,
            bool isSchedule = true,
            bool isSendMessage = false,
            long? automationHistoryId = null)
        {
            ILockService.Lock automationHistoryLock = null;
            try
            {
                var userProfile = await _appDbContext.UserProfiles
                    .Include(x => x.CustomFields)
                    .FirstOrDefaultAsync(x => x.Id == userProfileId);

                var automation = await _appDbContext.CompanyAssignmentRules
                    .FirstOrDefaultAsync(x => x.Id == assignmentId);

                if (automation == null ||
                    automation.Status != AutomationStatus.Live)
                {
                    return;
                }

                var automationAction = await _appDbContext.CompanyAutomationActions
                    .Where(
                        x =>
                            x.AssignmentRuleId == assignmentId
                            && x.Id == automationId)
                    .Include(x => x.UploadedFiles)
                    .Include(x => x.AssignedStaff.Identity)
                    .Include(x => x.AssignedTeam)
                    .Include(x => x.FbIgAutoReply)
                    .ThenInclude(x => x.FbIgAutoReplyFiles)
                    .FirstOrDefaultAsync();

                if (userProfile == null)
                {
                    return;
                }

                var userProfileIdStatus = await GetUserProfileIdStatusByIdAsync(userProfile.CompanyId, userProfile.Id);

                if (userProfileIdStatus.ActiveStatus != ActiveStatus.Active)
                {
                    return;
                }

                if (automationAction == null)
                {
                    return;
                }

                if (isSchedule
                    && (automationAction.ActionWait.HasValue
                        || automationAction.ActionWaitDays.HasValue))
                {
                    DateTime dateTime = DateTime.UtcNow;

                    if (automationAction.ActionWaitDays.HasValue)
                    {
                        dateTime = dateTime.AddDays(automationAction.ActionWaitDays.Value);
                    }

                    if (automationAction.ActionWait.HasValue)
                    {
                        dateTime = dateTime + automationAction.ActionWait.Value;
                    }

                    TimeSpan ts = dateTime - DateTime.UtcNow;

                    BackgroundJob.Schedule<IAutomationService>(
                        x => x.TriggerFbIgCommentAutomationAction(
                            entry,
                            change,
                            userProfileId,
                            assignmentId,
                            automationId,
                            isNewContact,
                            false,
                            isSendMessage,
                            automationHistoryId),
                        ts);
                    return;
                }

                if (!isSendMessage
                    && (automationAction.AutomatedTriggerType == AutomatedTriggerType.SendMessage
                        || automationAction.AutomatedTriggerType == AutomatedTriggerType.SendMedia))
                {
                    return;
                }

                automationHistoryLock = await _lockService.WaitUntilLockAcquiredAsync(
                    $"automation:history:{automationHistoryId}",
                    TimeSpan.FromSeconds(15));
                var automationHistory = await _appDbContext.CompanyAutomationHistories
                    .Include(x => x.AutomationActionRecords)
                    .FirstOrDefaultAsync(x => x.Id == automationHistoryId);

                var automationRecord = _mapper.Map<AutomationActionRecord>(automationAction);

                try
                {
                    if (automationAction.AutomatedTriggerType == AutomatedTriggerType.FacebookInitiateDm ||
                        automationAction.AutomatedTriggerType == AutomatedTriggerType.FacebookReplyComment ||
                        automationAction.AutomatedTriggerType == AutomatedTriggerType.InstagramInitiateDm ||
                        automationAction.AutomatedTriggerType == AutomatedTriggerType.InstagramReplyComment)
                    {
                        await ExecuteFbIgCommentAutomationAction(
                            entry,
                            change,
                            isNewContact,
                            automation.AssignmentRuleName,
                            automationAction,
                            userProfile,
                            automationRecord);
                    }
                    else
                    {
                        await ExecuteAutomationAction(
                            userProfileId,
                            automation.AssignmentRuleName,
                            isSendMessage,
                            null,
                            null,
                            automationAction,
                            userProfile,
                            automationRecord);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(
                        ex,
                        "[{MethodName}] Trigger automation action failed for user profile {UserProfileId}, automation rule {AutomationRuleId}, entry {EntryId}, page {PageId}: {ExceptionMessage}",
                        nameof(TriggerFbIgCommentAutomationAction),
                        userProfileId,
                        assignmentId,
                        entry.Id,
                        change.Value.PageId,
                        ex.Message);

                    automationRecord.Status = AutomationHistoryStatus.Failed;
                    automationRecord.ErrorMessage = ex.Message;

                    if (automationHistory.Status != AutomationHistoryStatus.Failed)
                    {
                        automationHistory.Status = AutomationHistoryStatus.Failed;
                    }
                }

                automationHistory.AutomationActionRecords.Add(automationRecord);

                await _appDbContext.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[{MethodName}] Trigger automation action general exception for user profile {UserProfileId}, automation rule {AutomationRuleId}, entry {EntryId}, page {PageId}: {ExceptionMessage}",
                    nameof(TriggerFbIgCommentAutomationAction),
                    userProfileId,
                    assignmentId,
                    entry.Id,
                    change.Value.PageId,
                    ex.Message);

                throw;
            }
            finally
            {
                await _lockService.ReleaseLockAsync(automationHistoryLock);
            }
        }

        public async Task TriggerFbIgIcebreakerAutomationAction(
            FBMessaging messaging,
            string userProfileId,
            long assignmentId,
            long automationId,
            bool isNewContact,
            bool isSchedule = true,
            bool isSendMessage = false,
            long? automationHistoryId = null)
        {
            ILockService.Lock automationHistoryLock = null;
            try
            {
                var userProfile = await _appDbContext.UserProfiles
                    .Include(x => x.CustomFields)
                    .FirstOrDefaultAsync(x => x.Id == userProfileId);

                var automation = await _appDbContext.CompanyAssignmentRules
                    .FirstOrDefaultAsync(x => x.Id == assignmentId);

                if (automation == null ||
                    automation.Status != AutomationStatus.Live)
                {
                    return;
                }

                var automationAction = await _appDbContext.CompanyAutomationActions
                    .Where(x => x.AssignmentRuleId == assignmentId && x.Id == automationId)
                    .Include(x => x.UploadedFiles)
                    .Include(x => x.AssignedStaff.Identity)
                    .Include(x => x.AssignedTeam)
                    .FirstOrDefaultAsync();

                if (userProfile == null)
                {
                    return;
                }

                var userProfileIdStatus = await GetUserProfileIdStatusByIdAsync(userProfile.CompanyId, userProfile.Id);

                if (userProfileIdStatus.ActiveStatus != ActiveStatus.Active)
                {
                    return;
                }

                if (automationAction == null)
                {
                    return;
                }

                if (isSchedule
                    && (automationAction.ActionWait.HasValue
                        || automationAction.ActionWaitDays.HasValue))
                {
                    DateTime dateTime = DateTime.UtcNow;

                    if (automationAction.ActionWaitDays.HasValue)
                    {
                        dateTime = dateTime.AddDays(automationAction.ActionWaitDays.Value);
                    }

                    if (automationAction.ActionWait.HasValue)
                    {
                        dateTime = dateTime + automationAction.ActionWait.Value;
                    }

                    TimeSpan ts = dateTime - DateTime.UtcNow;

                    BackgroundJob.Schedule<IAutomationService>(
                        x => x.TriggerFbIgIcebreakerAutomationAction(
                            messaging,
                            userProfileId,
                            assignmentId,
                            automationId,
                            isNewContact,
                            false,
                            isSendMessage,
                            automationHistoryId),
                        ts);
                    return;
                }

                if (!isSendMessage
                    && (automationAction.AutomatedTriggerType == AutomatedTriggerType.SendMessage
                        || automationAction.AutomatedTriggerType == AutomatedTriggerType.SendMedia))
                {
                    return;
                }

                automationHistoryLock = await _lockService.WaitUntilLockAcquiredAsync(
                    $"automation:history:{automationHistoryId}",
                    TimeSpan.FromSeconds(15));
                var automationHistory = await _appDbContext.CompanyAutomationHistories
                    .Include(x => x.AutomationActionRecords)
                    .FirstOrDefaultAsync(x => x.Id == automationHistoryId);

                var automationRecord = _mapper.Map<AutomationActionRecord>(automationAction);

                try
                {
                    if (automationAction.AutomatedTriggerType == AutomatedTriggerType.FacebookInitiateDm ||
                        automationAction.AutomatedTriggerType == AutomatedTriggerType.InstagramInitiateDm)
                    {
                        await ExecuteFbIgIcebreakerAutomationAction(
                            messaging,
                            isNewContact,
                            automation.AssignmentRuleName,
                            automationAction,
                            userProfile,
                            automationRecord);
                    }
                    else
                    {
                        await ExecuteAutomationAction(
                            userProfileId,
                            automation.AssignmentRuleName,
                            isSendMessage,
                            null,
                            null,
                            automationAction,
                            userProfile,
                            automationRecord);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(
                        ex,
                        "[{MethodName}] Automation failed for user profile {UserProfileId}, automation rule {AssignmentRuleId}, " +
                        "sender: {SenderId}, page {PageId}: {ExceptionMessage}",
                        nameof(TriggerFbIgIcebreakerAutomationAction),
                        userProfileId,
                        assignmentId,
                        messaging.sender.id,
                        messaging.recipient.id,
                        ex.Message);

                    automationRecord.Status = AutomationHistoryStatus.Failed;
                    automationRecord.ErrorMessage = ex.Message;

                    if (automationHistory.Status != AutomationHistoryStatus.Failed)
                    {
                        automationHistory.Status = AutomationHistoryStatus.Failed;
                    }
                }

                automationHistory.AutomationActionRecords.Add(automationRecord);

                await _appDbContext.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[{MethodName}] Automation general exception for user profile {UserProfileId}, automation rule {AssignmentRuleId}, " +
                    "sender: {SenderId}, page {PageId}: {ExceptionMessage}",
                    nameof(TriggerFbIgIcebreakerAutomationAction),
                    userProfileId,
                    assignmentId,
                    messaging.sender.id,
                    messaging.recipient.id,
                    ex.Message);

                throw;
            }
            finally
            {
                await _lockService.ReleaseLockAsync(automationHistoryLock);
            }
        }

        public async Task TriggerAutomationAction(
            string userProfileId,
            long assignmentId,
            long automationId,
            bool isSchedule = true,
            bool isSendMessage = true,
            string regexValue = null,
            long? conversationMessageId = null,
            long? automationHistoryId = null)
        {
            ILockService.Lock automationHistoryLock = null;
            try
            {
                var userProfile = await _appDbContext.UserProfiles
                    .Include(x => x.CustomFields)
                    .FirstOrDefaultAsync(x => x.Id == userProfileId);

                var automation = await _appDbContext.CompanyAssignmentRules
                    .FirstOrDefaultAsync(x => x.Id == assignmentId);

                if (automation.Status != AutomationStatus.Live)
                {
                    return;
                }

                var automationAction = await _appDbContext.CompanyAutomationActions
                    .Include(x => x.UploadedFiles)
                    .Include(x => x.AssignedStaff.Identity)
                    .Include(x => x.AssignedTeam)
                    .FirstOrDefaultAsync(
                        x =>
                            x.AssignmentRuleId == assignmentId
                            && x.Id == automationId);

                if (userProfile == null)
                {
                    return;
                }

                var userProfileIdStatus = await GetUserProfileIdStatusByIdAsync(userProfile.CompanyId, userProfile.Id);

                if (userProfileIdStatus.ActiveStatus != ActiveStatus.Active)
                {
                    return;
                }

                if (automationAction == null)
                {
                    return;
                }

                if (isSchedule
                    && (automationAction.ActionWait.HasValue
                        || automationAction.ActionWaitDays.HasValue))
                {
                    DateTime dateTime = DateTime.UtcNow;

                    if (automationAction.ActionWaitDays.HasValue)
                    {
                        dateTime = dateTime.AddDays(automationAction.ActionWaitDays.Value);
                    }

                    if (automationAction.ActionWait.HasValue)
                    {
                        dateTime = dateTime + automationAction.ActionWait.Value;
                    }

                    TimeSpan ts = dateTime - DateTime.UtcNow;

                    BackgroundJob.Schedule<IAutomationService>(
                        x => x.TriggerAutomationAction(
                            userProfileId,
                            assignmentId,
                            automationId,
                            false,
                            isSendMessage,
                            regexValue,
                            conversationMessageId,
                            automationHistoryId),
                        ts);

                    return;
                }

                if (!isSendMessage
                    && (automationAction.AutomatedTriggerType == AutomatedTriggerType.SendMessage
                        || automationAction.AutomatedTriggerType == AutomatedTriggerType.SendMedia))
                {
                    return;
                }

                automationHistoryLock = await _lockService.WaitUntilLockAcquiredAsync(
                    $"automation:history:{automationHistoryId}",
                    TimeSpan.FromSeconds(15));
                var automationHistory = await _appDbContext.CompanyAutomationHistories
                    .Include(x => x.AutomationActionRecords)
                    .FirstOrDefaultAsync(x => x.Id == automationHistoryId);

                if (automationHistory == null)
                {
                    _logger.LogWarning(
                        "[{MethodName}] Automation history {AutomationHistoryId} not found for user profile {UserProfileId}, automation rule {AutomationRuleId}, assignment rule {AssignmentRuleId}",
                        nameof(TriggerAutomationAction),
                        automationHistoryId,
                        userProfileId,
                        automationId,
                        assignmentId);

                    return;
                }

                var automationRecord = _mapper.Map<AutomationActionRecord>(automationAction);

                try
                {
                    await ExecuteAutomationAction(
                        userProfileId,
                        automation.AssignmentRuleName,
                        isSendMessage,
                        regexValue,
                        conversationMessageId,
                        automationAction,
                        userProfile,
                        automationRecord);
                }
                catch (Exception ex)
                {
                    _logger.LogError(
                        ex,
                        "[{MethodName}] Automation {AutomationRuleId} action {AutomationActionId} failed for user profile {UserProfileId},: {ExceptionMessage}",
                        nameof(TriggerAutomationAction),
                        assignmentId,
                        automationId,
                        userProfileId,
                        ex.Message);

                    automationRecord.Status = AutomationHistoryStatus.Failed;
                    automationRecord.ErrorMessage = ex.Message;

                    if (automationHistory.Status != AutomationHistoryStatus.Failed)
                    {
                        automationHistory.Status = AutomationHistoryStatus.Failed;
                    }
                }

                automationHistory.AutomationActionRecords.Add(automationRecord);

                await _appDbContext.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[{MethodName}] Automation {AutomationRuleId} action {AutomationActionId} general exception for user profile {UserProfileId},: {ExceptionMessage}",
                    nameof(TriggerAutomationAction),
                    assignmentId,
                    automationId,
                    userProfileId,
                    ex.Message);

                throw;
            }
            finally
            {
                await _lockService.ReleaseLockAsync(automationHistoryLock);
            }
        }

        public async Task ExecuteAutomationActions(
            string userProfileId,
            string triggeredSource,
            List<AutomationAction> automationActions,
            long? conversationMessageId)
        {
            var userProfile = await _appDbContext.UserProfiles
                .FirstOrDefaultAsync(x => x.Id == userProfileId);

            var automationRecord = new AutomationActionRecord();

            foreach (var automationAction in automationActions)
            {
                try
                {
                    var userProfileIdStatus = await GetUserProfileIdStatusByIdAsync(userProfile.CompanyId, userProfile.Id);

                    if (userProfileIdStatus.ActiveStatus != ActiveStatus.Active)
                    {
                        return;
                    }

                    await ExecuteAutomationAction(
                        userProfile.Id,
                        triggeredSource,
                        true,
                        null,
                        conversationMessageId,
                        automationAction,
                        userProfile,
                        automationRecord);
                }
                catch (Exception ex)
                {
                    await LogAutomationError(
                        userProfile.CompanyId,
                        userProfile.Id,
                        triggeredSource,
                        automationAction,
                        ex.Message);
                }
            }
        }

        public async Task ExecuteAutomationActions(
            string userProfileId,
            string triggeredSource,
            List<AutomationAction> automationActions)
        {
            await ExecuteAutomationActions(userProfileId, triggeredSource, automationActions, null);
        }

        public async Task ExecuteAutomationAction(
            string userProfileId,
            string triggeredSource,
            bool isSendMessage,
            string regexValue,
            long? conversationMessageId,
            AutomationAction? automationAction,
            UserProfile? userProfile,
            AutomationActionRecord automationRecord)
        {
            if (automationAction is not null)
            {
               _automationMeters.IncrementCounter(automationAction.AutomatedTriggerType);
            }

            switch (automationAction.AutomatedTriggerType)
            {
                case AutomatedTriggerType.GetReplyFromDialogflow:
                case AutomatedTriggerType.SendMessage:
                case AutomatedTriggerType.SendMedia:
                    var conversation =
                        await _userProfileService.GetConversationByUserProfileId(
                            userProfile.CompanyId,
                            userProfile.Id,
                            "closed");
                    if (conversation != null)
                    {
                        automationRecord.FormattedMessageContent =
                            await _conversationMessageService.SendAutomatedMessage(conversation, automationAction);
                    }

                    break;
                case AutomatedTriggerType.AddToList:
                    foreach (var groupId in automationAction.ActionAddedToGroupIds)
                    {
                        try
                        {
                            await _userProfileService.AddToUserList(
                                userProfile.CompanyId,
                                groupId,
                                new UserProfileIdsViewModel
                                {
                                    UserProfileIds = new List<string>
                                    {
                                        userProfile.Id
                                    }
                                });
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(
                                ex,
                                "[{MethodName}] Error adding user profile {UserProfileId} to list {ListId}: {ExceptionMessage}",
                                nameof(ExecuteAutomationAction),
                                userProfileId,
                                groupId,
                                ex.Message);
                        }
                    }

                    break;
                case AutomatedTriggerType.RemoveFromList:
                    if (!isSendMessage)
                    {
                        break;
                    }

                    foreach (var groupId in automationAction.ActionRemoveFromGroupIds)
                    {
                        try
                        {
                            await _userProfileService.RemoveFromUserList(
                                userProfile.CompanyId,
                                groupId,
                                new UserProfileIdsViewModel
                                {
                                    UserProfileIds = new List<string>
                                    {
                                        userProfile.Id
                                    }
                                });
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(
                                ex,
                                "[{MethodName}] Error removing user profile {UserProfileId} from list {ListId}: {ExceptionMessage}",
                                nameof(ExecuteAutomationAction),
                                userProfileId,
                                groupId,
                                ex.Message);
                        }
                    }

                    break;
                case AutomatedTriggerType.UpdateCustomFields:
                    if (!isSendMessage)
                    {
                        break;
                    }

                    // TECH-1142 Incoming message as variable
                    if (conversationMessageId.HasValue
                        && automationAction.ActionUpdateCustomFields
                            .Any(
                                x =>
                                    !string.IsNullOrEmpty(x.CustomValue)
                                    && x.CustomValue.Contains("{message}")))
                    {
                        var message = await _appDbContext.ConversationMessages
                            .Where(x => x.Id == conversationMessageId)
                            .Select(x => x.MessageContent)
                            .FirstOrDefaultAsync();

                        automationAction.ActionUpdateCustomFields.ForEach(
                            x =>
                            {
                                x.CustomValue = x.CustomValue.Replace("{message}", message);
                            });
                    }

                    // PDD-4989 Add current time stamp (v1)
                    if (automationAction.ActionUpdateCustomFields
                        .Any(
                            x =>
                                !string.IsNullOrEmpty(x.CustomValue)
                                && x.CustomValue.Contains("{datetime.now}")))
                    {
                        automationAction.ActionUpdateCustomFields.ForEach(
                            x =>
                            {
                                x.CustomValue = x.CustomValue.Replace("{datetime.now}", DateTime.UtcNow.ToString("o"));
                            });
                    }

                    await _userProfileService.UpdateUserProfileCustomFields(
                        userProfile.CompanyId,
                        userProfileId,
                        null,
                        automationAction.ActionUpdateCustomFields,
                        false);
                    break;
                case AutomatedTriggerType.AddTags:
                case AutomatedTriggerType.RemoveTags:
                    var conversationTag =
                        await _userProfileService.GetConversationByUserProfileId(
                            userProfile.CompanyId,
                            userProfile.Id,
                            "closed");

                    if (conversationTag != null)
                    {
                        var tagTargetedConversation = new Conversation();

                        if (automationAction.AutomatedTriggerType == AutomatedTriggerType.AddTags)
                        {
                            tagTargetedConversation = await _conversationHashtagService.AddConversationHashtag(
                                userProfile.CompanyId,
                                conversationTag.Id,
                                null,
                                automationAction.ActionAddConversationHashtags);
                        }
                        else if (automationAction.AutomatedTriggerType == AutomatedTriggerType.RemoveTags)
                        {
                            tagTargetedConversation = await _conversationHashtagService.RemoveConversationHashtag(
                                userProfile.CompanyId,
                                conversationTag.Id,
                                null,
                                automationAction.ActionAddConversationHashtags);
                        }
                    }

                    break;
                case AutomatedTriggerType.AddActivityLogs:
                    foreach (var remark in automationAction.ActionAddConversationRemarks)
                    {
                        await _auditHubAuditLogService.CreateStaffManualAddedLogAsync(
                            userProfile.CompanyId,
                            userProfileId,
                            null,
                            remark.Remarks,
                            null);
                    }

                    break;
                case AutomatedTriggerType.SuzukiAssignSalesmanIdsToCustomFieldsUat:
                    try
                    {
                        _logger.LogInformation(
                            $"UAT - Executing {nameof(AutomatedTriggerType.SuzukiAssignSalesmanIdsToCustomFieldsUat)} {automationAction.Id}");

                        await _suzukiUatService.AssignSalesmanIdsToCustomFieldsInSuzuki(userProfile);
                    }
                    catch (Exception e)
                    {
                        _logger.LogInformation(e, "UAT - SuzukiAssignSalesmanIdsToCustomFields throws an exception");
                    }

                    break;
                case AutomatedTriggerType.SuzukiAssignSalesmanIdsToCustomFields:
                    try
                    {
                        _logger.LogInformation(
                            $"Executing {nameof(AutomatedTriggerType.SuzukiAssignSalesmanIdsToCustomFields)} {automationAction.Id}");

                        await _suzukiService.AssignSalesmanIdsToCustomFieldsInSuzuki(userProfile);
                    }
                    catch (Exception e)
                    {
                        _logger.LogInformation(e, "SuzukiAssignSalesmanIdsToCustomFields throws an exception");
                    }

                    break;
                case AutomatedTriggerType.SuzukiAssignServiceAdvisorIdsToCustomFieldsUat:
                    try
                    {
                        _logger.LogInformation(
                            $"UAT - Executing {nameof(AutomatedTriggerType.SuzukiAssignServiceAdvisorIdsToCustomFieldsUat)} {automationAction.Id}");

                        await _suzukiUatService.AssignServiceAdvisorIdsToCustomFieldsInSuzuki(userProfile);
                    }
                    catch (Exception e)
                    {
                        _logger.LogInformation(
                            e,
                            "UAT - SuzukiAssignServiceAdvisorIdsToCustomFields throws an exception");
                    }

                    break;
                case AutomatedTriggerType.SuzukiAssignServiceAdvisorIdsToCustomFields:
                    try
                    {
                        _logger.LogInformation(
                            $"Executing {nameof(AutomatedTriggerType.SuzukiAssignServiceAdvisorIdsToCustomFields)} {automationAction.Id}");

                        await _suzukiService.AssignServiceAdvisorIdsToCustomFieldsInSuzuki(userProfile);
                    }
                    catch (Exception e)
                    {
                        _logger.LogInformation(e, "SuzukiAssignServiceAdvisorIdsToCustomFields throws an exception");
                    }

                    break;
                case AutomatedTriggerType.SuzukiAssignContactOwnerAndCreateLeadWithSalesmanUat:
                    try
                    {
                        _logger.LogInformation(
                            $"UAT - Executing {nameof(AutomatedTriggerType.SuzukiAssignContactOwnerAndCreateLeadWithSalesmanUat)} {automationAction.Id}");

                        await _suzukiUatService.AssignContactOwnerByCustomField(userProfileId, userProfile, $"{SuzukiRoleTypes.Salesman}Email");
                        await _suzukiUatService.CreateLeadInSuzuki(userProfileId, userProfile, $"{SuzukiRoleTypes.Salesman}Id", "UAT");
                    }
                    catch (Exception e)
                    {
                        _logger.LogInformation(
                            e,
                            "UAT - SuzukiAssignContactOwnerAndCreateLeadWithSalesman throws an exception");
                    }

                    break;
                case AutomatedTriggerType.SuzukiAssignContactOwnerAndCreateLeadWithSalesman:
                    try
                    {
                        _logger.LogInformation(
                            $"Executing {nameof(AutomatedTriggerType.SuzukiAssignContactOwnerAndCreateLeadWithSalesman)} {automationAction.Id}");

                        await _suzukiService.AssignContactOwnerByCustomField(userProfileId, userProfile, $"{SuzukiRoleTypes.Salesman}Email");
                        await _suzukiService.CreateLeadInSuzuki(userProfileId, userProfile, $"{SuzukiRoleTypes.Salesman}Id");
                    }
                    catch (Exception e)
                    {
                        _logger.LogInformation(
                            e,
                            "SuzukiAssignContactOwnerAndCreateLeadWithSalesman throws an exception");
                    }

                    break;
                case AutomatedTriggerType.SuzukiAssignContactOwnerAndCreateLeadWithSupervisorUat:
                    try
                    {
                        _logger.LogInformation(
                            $"UAT - Executing {nameof(AutomatedTriggerType.SuzukiAssignContactOwnerAndCreateLeadWithSupervisorUat)} {automationAction.Id}");

                        await _suzukiUatService.AssignContactOwnerByCustomField(userProfileId, userProfile, $"{SuzukiRoleTypes.Supervisor}Email");
                        await _suzukiUatService.CreateLeadInSuzuki(userProfileId, userProfile, $"{SuzukiRoleTypes.Supervisor}Id");
                    }
                    catch (Exception e)
                    {
                        _logger.LogInformation(
                            e,
                            "UAT - SuzukiAssignContactOwnerAndCreateLeadWithSupervisor throws an exception");
                    }

                    break;
                case AutomatedTriggerType.SuzukiAssignContactOwnerAndCreateLeadWithSupervisor:
                    try
                    {
                        _logger.LogInformation(
                            $"Executing {nameof(AutomatedTriggerType.SuzukiAssignContactOwnerAndCreateLeadWithSupervisor)} {automationAction.Id}");

                        await _suzukiService.AssignContactOwnerByCustomField(userProfileId, userProfile, $"{SuzukiRoleTypes.Supervisor}Email");
                        await _suzukiService.CreateLeadInSuzuki(userProfileId, userProfile, $"{SuzukiRoleTypes.Supervisor}Id");
                    }
                    catch (Exception e)
                    {
                        _logger.LogInformation(
                            e,
                            "SuzukiAssignContactOwnerAndCreateLeadWithSupervisor throws an exception");
                    }

                    break;
                case AutomatedTriggerType.SuzukiAssignContactOwnerAndCreateLeadWithSalesmanOnDutyUat:
                    try
                    {
                        _logger.LogInformation(
                            $"UAT - Executing {nameof(AutomatedTriggerType.SuzukiAssignContactOwnerAndCreateLeadWithSalesmanOnDutyUat)} {automationAction.Id}");

                        await _suzukiUatService.AssignContactOwnerByCustomField(
                            userProfileId,
                            userProfile,
                            $"{SuzukiRoleTypes.SalesmanOnDuty}Email");
                        await _suzukiUatService.CreateLeadInSuzuki(userProfileId, userProfile, $"{SuzukiRoleTypes.SalesmanOnDuty}Id");
                    }
                    catch (Exception e)
                    {
                        _logger.LogInformation(
                            e,
                            "UAT - SuzukiAssignContactOwnerAndCreateLeadWithSalesmanOnDuty throws an exception");
                    }

                    break;
                case AutomatedTriggerType.SuzukiAssignContactOwnerAndCreateLeadWithSalesmanOnDuty:
                    try
                    {
                        _logger.LogInformation(
                            $"Executing {nameof(AutomatedTriggerType.SuzukiAssignContactOwnerAndCreateLeadWithSalesmanOnDuty)} {automationAction.Id}");

                        await _suzukiService.AssignContactOwnerByCustomField(
                            userProfileId,
                            userProfile,
                            $"{SuzukiRoleTypes.SalesmanOnDuty}Email");
                        await _suzukiService.CreateLeadInSuzuki(userProfileId, userProfile, $"{SuzukiRoleTypes.SalesmanOnDuty}Id");
                    }
                    catch (Exception e)
                    {
                        _logger.LogInformation(
                            e,
                            "SuzukiAssignContactOwnerAndCreateLeadWithSalesmanOnDuty throws an exception");
                    }

                    break;
                case AutomatedTriggerType.SuzukiAssignContactOwnerAndCreateLeadWithCsaOrCsaOnDutyUat:
                    try
                    {
                        _logger.LogInformation(
                            $"UAT - Executing {nameof(AutomatedTriggerType.SuzukiAssignContactOwnerAndCreateLeadWithCsaOrCsaOnDutyUat)} {automationAction.Id}");

                        await _suzukiUatService.AssignContactOwnerByCustomField(
                            userProfileId,
                            userProfile,
                            $"{SuzukiRoleTypes.Csa}Email",
                            $"{SuzukiRoleTypes.CsaOnDuty}Email");
                        await _suzukiUatService.CreateLeadInSuzuki(
                            userProfileId,
                            userProfile,
                            $"{SuzukiRoleTypes.Csa}Id",
                            $"{SuzukiRoleTypes.CsaOnDuty}Id");
                    }
                    catch (Exception e)
                    {
                        _logger.LogInformation(
                            e,
                            "UAT - SuzukiAssignContactOwnerAndCreateLeadWithCsaOrCsaOnDuty throws an exception");
                    }

                    break;
                case AutomatedTriggerType.SuzukiAssignContactOwnerAndCreateLeadWithCsaOrCsaOnDuty:
                    try
                    {
                        _logger.LogInformation(
                            $"Executing {nameof(AutomatedTriggerType.SuzukiAssignContactOwnerAndCreateLeadWithCsaOrCsaOnDuty)} {automationAction.Id}");

                        await _suzukiService.AssignContactOwnerByCustomField(
                            userProfileId,
                            userProfile,
                            $"{SuzukiRoleTypes.Csa}Email",
                            $"{SuzukiRoleTypes.CsaOnDuty}Email");
                        await _suzukiService.CreateLeadInSuzuki(
                            userProfileId,
                            userProfile,
                            $"{SuzukiRoleTypes.Csa}Id",
                            $"{SuzukiRoleTypes.CsaOnDuty}Id");
                    }
                    catch (Exception e)
                    {
                        _logger.LogInformation(
                            e,
                            "SuzukiAssignContactOwnerAndCreateLeadWithCsaOrCsaOnDuty throws an exception");
                    }

                    break;
                case AutomatedTriggerType.SuzukiAssignContactOwnerAndCreateLeadWithCsaOnDutyUat:
                    try
                    {
                        _logger.LogInformation(
                            $"UAT - Executing {nameof(AutomatedTriggerType.SuzukiAssignContactOwnerAndCreateLeadWithCsaOnDutyUat)} {automationAction.Id}");

                        await _suzukiUatService.AssignContactOwnerByCustomField(userProfileId, userProfile, $"{SuzukiRoleTypes.CsaOnDuty}Email");
                        await _suzukiUatService.CreateLeadInSuzuki(userProfileId, userProfile, $"{SuzukiRoleTypes.CsaOnDuty}Id");
                    }
                    catch (Exception e)
                    {
                        _logger.LogInformation(
                            e,
                            "UAT - SuzukiAssignContactOwnerAndCreateLeadWithCsaOnDuty throws an exception");
                    }

                    break;
                case AutomatedTriggerType.SuzukiAssignContactOwnerAndCreateLeadWithCsaOnDuty:
                    try
                    {
                        _logger.LogInformation(
                            $"Executing {nameof(AutomatedTriggerType.SuzukiAssignContactOwnerAndCreateLeadWithCsaOnDuty)} {automationAction.Id}");

                        await _suzukiService.AssignContactOwnerByCustomField(userProfileId, userProfile, $"{SuzukiRoleTypes.CsaOnDuty}Email");
                        await _suzukiService.CreateLeadInSuzuki(userProfileId, userProfile, $"{SuzukiRoleTypes.CsaOnDuty}Id");
                    }
                    catch (Exception e)
                    {
                        _logger.LogInformation(
                            e,
                            "SuzukiAssignContactOwnerAndCreateLeadWithCsaOnDuty throws an exception");
                    }

                    break;
                case AutomatedTriggerType.SuzukiCreateLeadAndAssignSalesmanIdsToCustomFieldsUat:
                    try
                    {
                        _logger.LogInformation(
                            $"UAT - Executing {nameof(AutomatedTriggerType.SuzukiCreateLeadAndAssignSalesmanIdsToCustomFieldsUat)} {automationAction.Id}");

                        await _suzukiUatService.CreateLeadInSuzuki(userProfileId, userProfile, null);
                        await _suzukiUatService.AssignSalesmanIdsToCustomFieldsInSuzuki(userProfile);
                    }
                    catch (Exception e)
                    {
                        _logger.LogInformation(
                            e,
                            "UAT - SuzukiCreateLeadAndAssignSalesmanIdsToCustomFields throws an exception");
                    }

                    break;
                case AutomatedTriggerType.SuzukiCreateLeadAndAssignSalesmanIdsToCustomFields:
                    try
                    {
                        _logger.LogInformation(
                            $"Executing {nameof(AutomatedTriggerType.SuzukiCreateLeadAndAssignSalesmanIdsToCustomFields)} {automationAction.Id}");

                        await _suzukiService.CreateLeadInSuzuki(userProfileId, userProfile, null);
                        await _suzukiService.AssignSalesmanIdsToCustomFieldsInSuzuki(userProfile);
                    }
                    catch (Exception e)
                    {
                        _logger.LogInformation(
                            e,
                            "SuzukiCreateLeadAndAssignSalesmanIdsToCustomFields throws an exception");
                    }

                    break;
                case AutomatedTriggerType.SuzukiCreateLeadAndAssignServiceAdvisorIdsIdsToCustomFieldsUat:
                    try
                    {
                        _logger.LogInformation(
                            $"UAT - Executing {nameof(AutomatedTriggerType.SuzukiCreateLeadAndAssignServiceAdvisorIdsIdsToCustomFieldsUat)} {automationAction.Id}");

                        await _suzukiUatService.CreateLeadInSuzuki(userProfileId, userProfile, null);
                        await _suzukiUatService.AssignServiceAdvisorIdsToCustomFieldsInSuzuki(userProfile);
                    }
                    catch (Exception e)
                    {
                        _logger.LogInformation(
                            e,
                            "UAT - SuzukiCreateLeadAndAssignServiceAdvisorIdsIdsToCustomFields throws an exception");
                    }

                    break;
                case AutomatedTriggerType.SuzukiCreateLeadAndAssignServiceAdvisorIdsIdsToCustomFields:
                    try
                    {
                        _logger.LogInformation(
                            $"Executing {nameof(AutomatedTriggerType.SuzukiCreateLeadAndAssignServiceAdvisorIdsIdsToCustomFields)} {automationAction.Id}");

                        await _suzukiService.CreateLeadInSuzuki(userProfileId, userProfile, null);
                        await _suzukiService.AssignServiceAdvisorIdsToCustomFieldsInSuzuki(userProfile);
                    }
                    catch (Exception e)
                    {
                        _logger.LogInformation(
                            e,
                            "SuzukiCreateLeadAndAssignServiceAdvisorIdsIdsToCustomFields throws an exception");
                    }

                    break;
                case AutomatedTriggerType.SuzukiAssignCollaboratorWithSupervisorUat:
                    try
                    {
                        _logger.LogInformation(
                            $"UAT - Executing {nameof(AutomatedTriggerType.SuzukiAssignCollaboratorWithSupervisorUat)} {automationAction.Id}");

                        await _suzukiUatService.AssignCollaboratorByCustomField(userProfileId, userProfile, $"{SuzukiRoleTypes.Supervisor}Email");
                    }
                    catch (Exception e)
                    {
                        _logger.LogInformation(e, "UAT - SuzukiAssignCollaboratorWithSupervisor throws an exception");
                    }

                    break;
                case AutomatedTriggerType.SuzukiAssignCollaboratorWithSupervisor:
                    try
                    {
                        _logger.LogInformation(
                            $"Executing {nameof(AutomatedTriggerType.SuzukiAssignCollaboratorWithSupervisor)} {automationAction.Id}");

                        await _suzukiService.AssignCollaboratorByCustomField(userProfileId, userProfile, $"{SuzukiRoleTypes.Supervisor}Email");
                    }
                    catch (Exception e)
                    {
                        _logger.LogInformation(e, "SuzukiAssignCollaboratorWithSupervisor throws an exception");
                    }

                    break;
                case AutomatedTriggerType.SuzukiCreateLeadUat:
                    try
                    {
                        _logger.LogInformation(
                            $"UAT - Executing {nameof(AutomatedTriggerType.SuzukiCreateLeadUat)} {automationAction.Id}");

                        await _suzukiUatService.CreateLeadInSuzuki(userProfileId, userProfile, null);
                    }
                    catch (Exception e)
                    {
                        _logger.LogInformation(e, "UAT - SuzukiCreateLead throws an exception");
                    }

                    break;
                case AutomatedTriggerType.SuzukiCreateLead:
                    try
                    {
                        _logger.LogInformation(
                            $"Executing {nameof(AutomatedTriggerType.SuzukiCreateLead)} {automationAction.Id}");

                        await _suzukiService.CreateLeadInSuzuki(userProfileId, userProfile, null);
                    }
                    catch (Exception e)
                    {
                        _logger.LogInformation(e, "SuzukiCreateLead throws an exception");
                    }

                    break;
                case AutomatedTriggerType.SuzukiAssignCollaboratorAndCreateLeadWithSalesmanOnDutyUat:
                    try
                    {
                        _logger.LogInformation(
                            $"UAT - Executing {nameof(AutomatedTriggerType.SuzukiAssignCollaboratorAndCreateLeadWithSalesmanOnDutyUat)} {automationAction.Id}");

                        await _suzukiUatService.AssignCollaboratorByCustomField(
                            userProfileId,
                            userProfile,
                            $"{SuzukiRoleTypes.SalesmanOnDuty}Email");
                        await _suzukiUatService.CreateLeadInSuzuki(userProfileId, userProfile, $"{SuzukiRoleTypes.SalesmanOnDuty}Id");
                    }
                    catch (Exception e)
                    {
                        _logger.LogInformation(
                            e,
                            "UAT - SuzukiAssignCollaboratorAndCreateLeadWithSalesmanOnDuty throws an exception");
                    }

                    break;
                case AutomatedTriggerType.SuzukiAssignCollaboratorAndCreateLeadWithSalesmanOnDuty:
                    try
                    {
                        _logger.LogInformation(
                            $"Executing {nameof(AutomatedTriggerType.SuzukiAssignCollaboratorAndCreateLeadWithSalesmanOnDuty)} {automationAction.Id}");

                        await _suzukiService.AssignCollaboratorByCustomField(
                            userProfileId,
                            userProfile,
                            $"{SuzukiRoleTypes.SalesmanOnDuty}Email");
                        await _suzukiService.CreateLeadInSuzuki(userProfileId, userProfile, $"{SuzukiRoleTypes.SalesmanOnDuty}Id");
                    }
                    catch (Exception e)
                    {
                        _logger.LogInformation(
                            e,
                            "SuzukiAssignCollaboratorAndCreateLeadWithSalesmanOnDuty throws an exception");
                    }

                    break;
                case AutomatedTriggerType.SuzukiAssignCollaboratorAndCreateLeadWithCsaOrCsaOnDutyUat:
                    try
                    {
                        _logger.LogInformation(
                            $"UAT - Executing {nameof(AutomatedTriggerType.SuzukiAssignCollaboratorAndCreateLeadWithCsaOrCsaOnDutyUat)} {automationAction.Id}");

                        await _suzukiUatService.AssignCollaboratorByCustomField(
                            userProfileId,
                            userProfile,
                            $"{SuzukiRoleTypes.Csa}Email",
                            $"{SuzukiRoleTypes.CsaOnDuty}Email");
                        await _suzukiUatService.CreateLeadInSuzuki(
                            userProfileId,
                            userProfile,
                            $"{SuzukiRoleTypes.Csa}Id",
                            $"{SuzukiRoleTypes.CsaOnDuty}Id");
                    }
                    catch (Exception e)
                    {
                        _logger.LogInformation(
                            e,
                            "UAT - SuzukiAssignCollaboratorAndCreateLeadWithCsaOrCsaOnDuty throws an exception");
                    }

                    break;
                case AutomatedTriggerType.SuzukiAssignCollaboratorAndCreateLeadWithCsaOrCsaOnDuty:
                    try
                    {
                        _logger.LogInformation(
                            $"Executing {nameof(AutomatedTriggerType.SuzukiAssignCollaboratorAndCreateLeadWithCsaOrCsaOnDuty)} {automationAction.Id}");

                        await _suzukiService.AssignCollaboratorByCustomField(
                            userProfileId,
                            userProfile,
                            $"{SuzukiRoleTypes.Csa}Email",
                            $"{SuzukiRoleTypes.CsaOnDuty}Email");
                        await _suzukiService.CreateLeadInSuzuki(
                            userProfileId,
                            userProfile,
                            $"{SuzukiRoleTypes.Csa}Id",
                            $"{SuzukiRoleTypes.CsaOnDuty}Id");
                    }
                    catch (Exception e)
                    {
                        _logger.LogInformation(
                            e,
                            "SuzukiAssignCollaboratorAndCreateLeadWithCsaOrCsaOnDuty throws an exception");
                    }

                    break;
                case AutomatedTriggerType.Assignment:
                    await AutomationAssignment(userProfile, automationAction, isSendMessage, regexValue);

                    break;
                case AutomatedTriggerType.AddConversationNote:
                    var conversationNote = await _userProfileService.GetConversationByUserProfileId(
                        userProfile.CompanyId,
                        userProfile.Id,
                        "closed");

                    if (conversationNote != null)
                    {
                        var messageContent = automationAction.MessageContent;

                        if (automationAction.MessageParams?.Count > 0)
                        {
                            var template_params = await _conversationMessageService.FormatParams(
                                userProfile,
                                                                                      automationAction.MessageParams);
                            messageContent = string.Format(
                                automationAction.MessageContent,
                                template_params.Select(x => x.ToString()).ToArray());
                        }

                        var conversationMessageNote = new ConversationMessage
                        {
                            Channel = ChannelTypes.Note,
                            ConversationId = conversationNote.Id,
                            MessageContent = messageContent,
                            MessageType = "text"
                        };

                        if (automationAction.UploadedFiles.Count > 0)
                        {
                            conversationMessageNote.MessageType = "file";
                            conversationMessageNote.UploadedFiles =
                                _mapper.Map<List<UploadedFile>>(automationAction.UploadedFiles);
                        }

                        await _conversationMessageService.SendConversationNote(
                            userProfile.CompanyId,
                            conversationNote.Id,
                            null,
                            conversationMessageNote,
                            new ConversationNoteViewModel());
                    }

                    break;

                case AutomatedTriggerType.ChangeConversationStatus:
                    var conversationStatus =
                        await _userProfileService.GetConversationByUserProfileId(
                            userProfile.CompanyId,
                            userProfile.Id,
                            "closed");

                    if (conversationStatus != null)
                    {
                        await _conversationMessageService.ChangeConversationStatus(
                            conversationStatus.Id,
                            null,
                            automationAction.ChangeConversationStatus);
                    }

                    break;
                case AutomatedTriggerType.AddAdditionalAssignee:
                case AutomatedTriggerType.RemoveAdditionalAssignee:
                    var conversationAdditionalAssignee =
                        await _userProfileService.GetConversationByUserProfileId(
                            userProfile.CompanyId,
                            userProfile.Id,
                            "closed");

                    if (conversationAdditionalAssignee == null)
                    {
                        break;
                    }

                    var ids = await _appDbContext.UserRoleStaffs
                        .Where(
                            x =>
                                x.CompanyId == userProfile.CompanyId
                                && automationAction.AddAdditionalAssigneeIds.Contains(x.IdentityId))
                        .Select(x => x.Id)
                        .ToListAsync();

                    if (automationAction.AutomatedTriggerType == AutomatedTriggerType.AddAdditionalAssignee)
                    {
                        await _conversationAssigneeService.AddAdditionalAssignees(
                            conversationAdditionalAssignee,
                            ids,
                            null,
                            true);
                    }
                    else
                    {
                        await _conversationAssigneeService.RemoveAdditionalAssignees(
                            conversationAdditionalAssignee,
                            ids,
                            null,
                            true);
                    }

                    break;
                case AutomatedTriggerType.SendWebhook:
                {
                    if (string.IsNullOrEmpty(automationAction.WebhookURL))
                    {
                        return;
                    }

                    var isSuccess = false;

                    var client = _httpClientFactory.CreateClient(HttpClientHandlerName.Default);

                    var jsonFormatter = new JsonSerializerSettings
                    {
                        ContractResolver = new DefaultContractResolver()
                        {
                            NamingStrategy = new CamelCaseNamingStrategy()
                        },
                        NullValueHandling = NullValueHandling.Ignore,
                        DateTimeZoneHandling = DateTimeZoneHandling.Utc
                    };

                    var formatter = new JsonMediaTypeFormatter
                    {
                        SerializerSettings = jsonFormatter
                    };

                    if (conversationMessageId.HasValue)
                    {
                        var responseVM = await _appDbContext.ConversationMessages
                            .Where(x => x.Id == conversationMessageId)
                            .AsNoTracking()
                            .ProjectTo<ConversationMessageWebhookResponse>(_mapper.ConfigurationProvider)
                            .FirstOrDefaultAsync();

                        if (responseVM.UploadedFiles?.Count > 0)
                        {
                            responseVM.UploadedFiles.ForEach(
                                y =>
                                    y.Url = _azureBlobStorageService.GetAzureBlobSasUri(
                                        y.Filename,
                                        y.BlobContainer,
                                        24 * 7));
                        }

                        try
                        {
                            responseVM.ContactEmail = await _appDbContext.Conversations
                                .Where(x => x.Id == responseVM.ConversationId)
                                .Select(x => x.UserProfile.Email)
                                .FirstOrDefaultAsync() ?? string.Empty;
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(
                                ex,
                                "[{MethodName}] Send webhook unable to set contact email for conversation {ConversationId}: {ExceptionMessage}",
                                nameof(ExecuteAutomationAction),
                                responseVM.ConversationId,
                                ex.Message);
                        }

                        automationRecord.WebhookPayload = JsonConvert.SerializeObject(responseVM, jsonFormatter);

                        await Policy
                            .Handle<Exception>()
                            .WaitAndRetryAsync(
                                5,
                                sleepDurationProvider: i => TimeSpan.FromSeconds(5 * i),
                                onRetry: (exception, _, retryCount, _) =>
                                {
                                    _logger.LogError(
                                        exception,
                                        "[Retry count - {RetryCount}] Message Id: {MessageId} | Send webhook failed | Error: {ExceptionMessage} | AutomationId: {AutomationRuleId} | URL: {WebhookUrl} | Payload: {Payload}",
                                        retryCount,
                                        responseVM.Id,
                                        exception.Message,
                                        automationAction.AssignmentRuleId,
                                        automationAction.WebhookURL,
                                        JsonConvert.SerializeObject(responseVM));

                                    automationRecord.ErrorMessage = $"Send Webhook Failed: Error {exception.Message}";
                                })
                            .ExecuteAndCaptureAsync(
                                async () =>
                                {
                                    var apiResponse = await client.PostAsync(
                                        new Uri(automationAction.WebhookURL),
                                        new StringContent(
                                            automationRecord.WebhookPayload,
                                            Encoding.UTF8,
                                            MediaTypeNames.Application.Json));

                                    if (!apiResponse.IsSuccessStatusCode)
                                    {
                                        // If the endpoint is gone, delete the automation rule
                                        if (apiResponse.StatusCode == HttpStatusCode.Gone)
                                        {
                                            _logger.LogInformation(
                                                "The Zap automation is gone, unsubscribe {StatusCode} Message: {Message}",
                                                apiResponse.StatusCode,
                                                await apiResponse.Content.ReadAsStringAsync());

                                            await _appDbContext.CompanyAssignmentRules
                                                .Where(x => x.CompanyId == automationAction.CompanyId && x.Id == automationAction.AssignmentRuleId).ExecuteDeleteAsync();

                                            return;
                                        }

                                        throw new Exception(
                                            $"Cannot post webhook to the endpoint: {apiResponse.StatusCode} | {await apiResponse.Content.ReadAsStringAsync()}");
                                    }

                                    _logger.LogInformation(
                                        "Send Webhook Success: AutomationId: {AutomationRuleId} URL: {WebhookUrl} Payload: {Payload}",
                                        automationAction.AssignmentRuleId,
                                        automationAction.WebhookURL,
                                        JsonConvert.SerializeObject(responseVM));

                                    isSuccess = true;
                                });
                    }
                    else
                    {
                        var response = await _userProfileService.NormalizeUserProfileResponse(
                            await _appDbContext.CompanyCustomUserProfileFields
                                .Where(x => x.CompanyId == userProfile.CompanyId)
                                .ToListAsync(),
                            userProfile);

                        automationRecord.WebhookPayload = JsonConvert.SerializeObject(response, jsonFormatter);

                        await Policy
                            .Handle<Exception>()
                            .WaitAndRetryAsync(
                                5,
                                sleepDurationProvider: i => TimeSpan.FromSeconds(5 * i),
                                onRetry: (exception, _, retryCount, _) =>
                                {
                                    _logger.LogError(
                                        exception,
                                        "[Retry count - {RetryCount}] User Profile Id: {UserProfileId} | Send webhook failed | Error: {ExceptionMessage} | AutomationId: {AutomationRuleId} | URL: {WebhookUrl} | Payload: {Payload}",
                                        retryCount,
                                        userProfile?.Id,
                                        exception.Message,
                                        automationAction.AssignmentRuleId,
                                        automationAction.WebhookURL,
                                        JsonConvert.SerializeObject(response));

                                    automationRecord.ErrorMessage = $"Send Webhook Failed: Error {exception.Message}";
                                })
                            .ExecuteAndCaptureAsync(
                                async () =>
                                {
                                    var apiResponse = await client.PostAsync(
                                        new Uri(automationAction.WebhookURL),
                                        new StringContent(
                                            automationRecord.WebhookPayload,
                                            Encoding.UTF8,
                                            MediaTypeNames.Application.Json));

                                    if (!apiResponse.IsSuccessStatusCode)
                                    {
                                        // If the endpoint is gone, delete the automation rule
                                        if (apiResponse.StatusCode == HttpStatusCode.Gone)
                                        {
                                            _logger.LogInformation(
                                                "The Zap automation is gone, unsubscribe {StatusCode} Message: {Message}",
                                                apiResponse.StatusCode,
                                                await apiResponse.Content.ReadAsStringAsync());

                                            await _appDbContext.CompanyAssignmentRules
                                                .Where(x => x.CompanyId == automationAction.CompanyId && x.Id == automationAction.AssignmentRuleId).ExecuteDeleteAsync();

                                            return;
                                        }

                                        throw new Exception(
                                            $"Cannot post webhook to the endpoint: {apiResponse.StatusCode} | {await apiResponse.Content.ReadAsStringAsync()}");
                                    }

                                    _logger.LogInformation(
                                        "Send Webhook Success: AutomationId: {AutomationRuleId} URL: {WebhookUrl} Payload: {Payload}",
                                        automationAction.AssignmentRuleId,
                                        automationAction.WebhookURL,
                                        JsonConvert.SerializeObject(response));

                                    isSuccess = true;
                                });
                    }

                    if (!isSuccess)
                    {
                        throw new Exception(automationRecord.ErrorMessage);
                    }

                    break;
                }
                case AutomatedTriggerType.CrmHubAddContactToLead:
                    try
                    {
                        var companyCustomUserProfileFields = await _appDbContext.CompanyCustomUserProfileFields
                            .Where(x => x.CompanyId == userProfile.CompanyId)
                            .ToListAsync();

                        var fieldIdToFieldNameDict = new Dictionary<string, string>();

                        foreach (var companyCustomUserProfileField in companyCustomUserProfileFields)
                        {
                            fieldIdToFieldNameDict[companyCustomUserProfileField.Id] = companyCustomUserProfileField.FieldName;
                        }

                        var userProfileDict =
                            _mapper.Map<Dictionary<string, object>>(
                                _mapper.Map<CrmHubController.UserProfileDto>(userProfile));

                        foreach (var userProfileCustomField in userProfile.CustomFields)
                        {
                            userProfileDict[
                                    fieldIdToFieldNameDict.GetValueOrDefault(
                                        userProfileCustomField.CompanyDefinedFieldId,
                                        userProfileCustomField.CompanyDefinedFieldId)]
                                = userProfileCustomField.Value;
                        }

                        userProfileDict.Remove("CustomFields");

                        var isFromCrmHub = await _appDbContext
                            .CrmHubEntities
                            .AnyAsync(e => e.UserProfileId == userProfile.Id);

                        if (isFromCrmHub)
                        {
                            // ignored
                        }
                        else
                        {
                            await _objectsApi.ObjectsUpsertObjectPostAsync(
                                upsertObjectInput: new UpsertObjectInput(
                                    userProfile.CompanyId,
                                    "sleekflow",
                                    "Lead",
                                    userProfileDict));
                        }
                    }
                    catch (Exception e)
                    {
                        _logger.LogError(
                            e,
                            "Unable to execute CrmHubAddContactToLead for user profile {UserProfileId}",
                            userProfileId);
                    }

                    break;
                case AutomatedTriggerType.CrmHubAddOrUpdateContact:
                    try
                    {
                        var companyCustomUserProfileFields = await _appDbContext.CompanyCustomUserProfileFields
                            .Where(x => x.CompanyId == userProfile.CompanyId)
                            .ToListAsync();

                        var fieldIdToFieldNameDict = new Dictionary<string, string>();

                        foreach (var companyCustomUserProfileField in companyCustomUserProfileFields)
                        {
                            fieldIdToFieldNameDict[companyCustomUserProfileField.Id] = companyCustomUserProfileField.FieldName;
                        }

                        var userProfileDict =
                            _mapper.Map<Dictionary<string, object>>(
                                _mapper.Map<CrmHubController.UserProfileDto>(userProfile));

                        foreach (var userProfileCustomField in userProfile.CustomFields)
                        {
                            userProfileDict[
                                    fieldIdToFieldNameDict.GetValueOrDefault(
                                        userProfileCustomField.CompanyDefinedFieldId,
                                        userProfileCustomField.CompanyDefinedFieldId)]
                                = userProfileCustomField.Value;
                        }

                        userProfileDict.Remove("CustomFields");

                        // We only upsert contacts to CrmHub if they have a phone number or email.
                        // The phone number field is populated late during contact creation via the messaging API.
                        // Without a phone number or email, we cannot associate a CrmHub object with a provider object, which has caused duplicate CrmHub objects.
                        // Note that historical contacts without a phone number or email will remain in CrmHub after this change.
                        if ((userProfile.PhoneNumber is not null && userProfileDict.ContainsKey("PhoneNumber"))
                            || (userProfile.Email is not null && userProfileDict.ContainsKey("Email")))
                        {
                            await _objectsApi.ObjectsUpsertObjectPostAsync(
                                upsertObjectInput: new UpsertObjectInput(
                                    userProfile.CompanyId,
                                    "sleekflow",
                                    "Contact",
                                    userProfileDict));
                        }
                        else
                        {
                            _logger.LogInformation(
                                "CrmHubAddOrUpdateContact was executed, but it has no effect for user profile {UserProfileId} as it does not have phone number or email",
                                userProfileId);
                        }
                    }
                    catch (Exception e)
                    {
                        _logger.LogError(
                            e,
                            "Unable to execute CrmHubAddOrUpdateContact for user profile {UserProfileId}",
                            userProfileId);
                    }

                    break;
            }

            automationRecord.Status = AutomationHistoryStatus.Success;

            await LogAutomationTriggered(
                userProfile.CompanyId,
                userProfile.Id,
                triggeredSource,
                automationAction,
                automationRecord);
        }

        public async Task ExecuteFbIgCommentAutomationAction(
            Entry entry,
            Change change,
            bool isNewContact,
            string triggeredSource,
            AutomationAction automationAction,
            UserProfile userProfile,
            AutomationActionRecord automationRecord)
        {
            _logger.LogInformation(
                "ExecuteFbIgCommentAutomationAction: {Entry} {Change}",
                JsonConvert.SerializeObject(entry),
                JsonConvert.SerializeObject(change));

            switch (automationAction.AutomatedTriggerType)
            {
                case AutomatedTriggerType.FacebookInitiateDm:
                case AutomatedTriggerType.FacebookReplyComment:
                    await _facebookService.HandleFbAutoAction(automationAction, entry, change, isNewContact);
                    automationRecord.Status = AutomationHistoryStatus.Success;

                    var fbAutoReplyHistory = await _appDbContext.FbIgAutoReplyHistoryRecords
                        .AsNoTracking()
                        .OrderBy(x => x.CreatedAt)
                        .LastOrDefaultAsync(x => x.FbIgAutoReplyId == automationAction.FbIgAutoReply.Id);

                    if (fbAutoReplyHistory != null
                        && !string.IsNullOrEmpty(fbAutoReplyHistory.ErrorMessage))
                    {
                        automationRecord.Status = AutomationHistoryStatus.Failed;
                    }

                    break;

                case AutomatedTriggerType.InstagramInitiateDm:
                case AutomatedTriggerType.InstagramReplyComment:
                    await _instagramService.HandleIgAutoAction(automationAction, entry, change, isNewContact);
                    automationRecord.Status = AutomationHistoryStatus.Success;

                    var igAutoReplyHistory = await _appDbContext.FbIgAutoReplyHistoryRecords
                        .AsNoTracking()
                        .OrderBy(x => x.CreatedAt)
                        .LastOrDefaultAsync(x => x.FbIgAutoReplyId == automationAction.FbIgAutoReply.Id);

                    if (igAutoReplyHistory != null
                        && !string.IsNullOrEmpty(igAutoReplyHistory.ErrorMessage))
                    {
                        automationRecord.Status = AutomationHistoryStatus.Failed;
                    }

                    break;
            }

            await LogAutomationTriggered(
                userProfile.CompanyId,
                userProfile.Id,
                triggeredSource,
                automationAction,
                automationRecord);
        }

        public async Task ExecuteFbIgIcebreakerAutomationAction(
            FBMessaging messaging,
            bool isNewContact,
            string triggeredSource,
            AutomationAction automationAction,
            UserProfile userProfile,
            AutomationActionRecord automationRecord)
        {
            switch (automationAction.AutomatedTriggerType)
            {
                case AutomatedTriggerType.FacebookInitiateDm:

                    await _facebookService.HandleFbIcebreakerDm(automationAction.Id, messaging, isNewContact);
                    automationRecord.Status = AutomationHistoryStatus.Success;

                    var fbAutoReplyHistory = await _appDbContext.FbIgAutoReplyHistoryRecords
                        .AsNoTracking()
                        .AsSplitQuery()
                        .OrderBy(x => x.CreatedAt)
                        .LastOrDefaultAsync(
                            x =>
                                x.FbIgAutoReplyId == _appDbContext.FbIgAutoReplies
                                    .Where(y => y.AutomationActionId == automationAction.Id)
                                    .Select(y => y.Id)
                                    .FirstOrDefault());

                    if (fbAutoReplyHistory != null
                        && !string.IsNullOrEmpty(fbAutoReplyHistory.ErrorMessage))
                    {
                        automationRecord.Status = AutomationHistoryStatus.Failed;
                    }

                    break;

                case AutomatedTriggerType.InstagramInitiateDm:
                    await _instagramService.HandleIgIcebreakerDm(automationAction.Id, messaging, isNewContact);
                    automationRecord.Status = AutomationHistoryStatus.Success;

                    var igAutoReplyHistory = await _appDbContext.FbIgAutoReplyHistoryRecords
                        .AsNoTracking()
                        .AsSplitQuery()
                        .OrderBy(x => x.CreatedAt)
                        .LastOrDefaultAsync(
                            x =>
                                x.FbIgAutoReplyId == _appDbContext.FbIgAutoReplies
                                    .Where(y => y.AutomationActionId == automationAction.Id)
                                    .Select(y => y.Id)
                                    .FirstOrDefault());

                    if (igAutoReplyHistory != null
                        && !string.IsNullOrEmpty(igAutoReplyHistory.ErrorMessage))
                    {
                        automationRecord.Status = AutomationHistoryStatus.Failed;
                    }

                    break;
            }

            await LogAutomationTriggered(
                userProfile.CompanyId,
                userProfile.Id,
                triggeredSource,
                automationAction,
                automationRecord);
        }

        public async Task AutomationAssignment(
            UserProfile userProfile,
            AutomationAction automationTriggerAction,
            bool isTriggerUpdate = true,
            string regexValue = null,
            int attempts = 3,
            string changedBy = null)
        {
            var conversation = await _userProfileService.GetConversationByUserProfileId(
                userProfile.CompanyId,
                userProfile.Id,
                "closed");

            var userProfileIdStatus = await GetUserProfileIdStatusByIdAsync(userProfile.CompanyId, userProfile.Id);

            if (userProfileIdStatus.ActiveStatus != ActiveStatus.Active)
            {
                return;
            }

            if (attempts <= 0)
            {
                return;
            }

            if (automationTriggerAction.AssignedStaff == null
                && automationTriggerAction.AssignedStaffId.HasValue)
            {
                automationTriggerAction.AssignedStaff = await _appDbContext.UserRoleStaffs
                    .FirstOrDefaultAsync(x => x.Id == automationTriggerAction.AssignedStaffId);
            }

            if (automationTriggerAction.AssignedTeam == null
                && automationTriggerAction.AssignedTeamId.HasValue)
            {
                automationTriggerAction.AssignedTeam = await _appDbContext.CompanyStaffTeams
                    .FirstOrDefaultAsync(x => x.Id == automationTriggerAction.AssignedTeamId);
            }

            switch (automationTriggerAction.AssignmentType)
            {
                case AssignmentType.QueueBased:
                    var staffIds = await _appDbContext.CompanyAssignmentQueues
                        .FirstOrDefaultAsync(x => x.CompanyId == conversation.CompanyId);

                    if (staffIds == null)
                    {
                        staffIds = new AssignmentQueue
                        {
                            CompanyId = conversation.CompanyId
                        };

                        _appDbContext.CompanyAssignmentQueues.Add(staffIds);
                    }

                    if (staffIds.StaffIds?.Count > 0)
                    {
                        var staffId = staffIds.StaffIds.FirstOrDefault();
                        var staff = await _appDbContext.UserRoleStaffs
                            .Include(x => x.Identity)
                            .FirstOrDefaultAsync(
                                x =>
                                    x.CompanyId == userProfile.CompanyId
                                    && x.IdentityId == staffId);

                        staffIds.StaffIds.Remove(staffId);
                        staffIds.StaffIds = staffIds.StaffIds.ToList();

                        await _appDbContext.SaveChangesAsync();

                        if (staff.Status == StaffStatus.Away)
                        {
                            _logger.LogInformation(
                                "[{MethodName}] Failed to assign, staff status away. " +
                                "Company Id: {CompanyId} " +
                                "Conversation Id: {ConversationId} " +
                                "Attempt number: {Attempts} " +
                                "Staff to be assigned: {StaffIdentityId}",
                                nameof(AutomationAssignment),
                                userProfile.CompanyId,
                                conversation.Id,
                                4 - attempts,
                                staff.IdentityId);
                            await AutomationAssignment(
                                userProfile,
                                automationTriggerAction,
                                isTriggerUpdate,
                                regexValue,
                                attempts - 1,
                                changedBy: changedBy);
                        }
                        else
                        {
                            await _conversationMessageService.ChangeConversationAssignee(
                                conversation,
                                staff,
                                isTriggerUpdate);

                            await _conversationMessageService.ChangeConversationAssignedTeam(
                                conversation,
                                companyTeam: null,
                                isTriggerUpdate);

                            _logger.LogInformation(
                                "[{MethodName}] Successfully assigned to staff. " +
                                "Company Id: {CompanyId} " +
                                "Conversation Id: {ConversationId} " +
                                "Attempt number: {Attempts} " +
                                "Staff assigned: {StaffIdentityId}",
                                nameof(AutomationAssignment),
                                userProfile.CompanyId,
                                conversation.Id,
                                4 - attempts,
                                staff.IdentityId);
                        }
                    }
                    else
                    {
                        var queueStaffIds = await _appDbContext.UserRoleStaffs
                            .Where(x => x.CompanyId == conversation.CompanyId && x.Status == StaffStatus.Active)
                            .Select(x => x.IdentityId)
                            .ToListAsync();

                        if (queueStaffIds.Count == 0)
                        {
                            _logger.LogInformation(
                                "[{MethodName}] Retry, no active staff found. " +
                                "Company Id: {CompanyId} " +
                                "Conversation Id: {ConversationId} " +
                                "Attempt number: {Attempts} ",
                                nameof(AutomationAssignment),
                                userProfile.CompanyId,
                                conversation.Id,
                                4 - attempts);
                        }

                        staffIds.StaffIds = queueStaffIds;
                        await _appDbContext.SaveChangesAsync();
                        _logger.LogInformation(
                            "[{MethodName}] Repopulate CompanyAssignmentQueues StaffIds. " +
                            "Company Id: {CompanyId} " +
                            "Conversation Id: {ConversationId} " +
                            "Attempt number: {Attempts} ",
                            nameof(AutomationAssignment),
                            userProfile.CompanyId,
                            conversation.Id,
                            4 - attempts);

                        await AutomationAssignment(
                            userProfile,
                            automationTriggerAction,
                            isTriggerUpdate,
                            regexValue,
                            attempts - 1,
                            changedBy: changedBy);
                    }

                    break;
                case AssignmentType.SpecificPerson:

                    await _conversationMessageService.ChangeConversationAssignee(
                        conversation,
                        automationTriggerAction.AssignedStaff,
                        isTriggerUpdate,
                        changedBy: changedBy);

                    _logger.LogInformation(
                        "[{MethodName}] Successfully assigned to staff. " +
                        "Company Id: {CompanyId} " +
                        "Conversation Id: {ConversationId} " +
                        "Attempt number: {Attempts} " +
                        "Staff assigned: {StaffIdentityId}",
                        nameof(AutomationAssignment),
                        userProfile.CompanyId,
                        conversation.Id,
                        4 - attempts,
                        automationTriggerAction.AssignedStaff != null ? automationTriggerAction.AssignedStaff.IdentityId : string.Empty);

                    await _conversationMessageService.ChangeConversationAssignedTeam(
                        conversation,
                        companyTeam: null,
                        isTriggerUpdate);

                    break;
                case AssignmentType.SpecificGroup:
                    if (automationTriggerAction.TeamAssignmentType.HasValue)
                    {
                        switch (automationTriggerAction.TeamAssignmentType)
                        {
                            case AssignmentType.SpecificPerson:
                                if (automationTriggerAction.AssignedStaff != null)
                                {
                                    // assigned to member of a team
                                    if (await _appDbContext.CompanyTeamMembers.AnyAsync(
                                            x =>
                                                x.CompanyTeamId == automationTriggerAction.AssignedTeam.Id
                                                && x.StaffId == automationTriggerAction.AssignedStaff.Id))
                                    {
                                        await _conversationMessageService.ChangeConversationAssignee(
                                            conversation,
                                            automationTriggerAction.AssignedStaff,
                                            isTriggerUpdate,
                                            changedBy: changedBy);
                                        _logger.LogInformation(
                                            "[{MethodName}] Successfully assigned to staff. " +
                                            "Company Id: {CompanyId} " +
                                            "Conversation Id: {ConversationId} " +
                                            "Attempt number: {Attempts} " +
                                            "Staff assigned: {StaffIdentityId}",
                                            nameof(AutomationAssignment),
                                            userProfile.CompanyId,
                                            conversation.Id,
                                            4 - attempts,
                                            automationTriggerAction.AssignedStaff.IdentityId);
                                    }
                                    else
                                    {
                                        await _conversationMessageService.ChangeConversationAssignee(
                                            conversation,
                                            null,
                                            isTriggerUpdate,
                                            changedBy: changedBy);
                                        _logger.LogInformation(
                                            "[{MethodName}] Could not find team member to assign to. " +
                                            "Company Id: {CompanyId} " +
                                            "Conversation Id: {ConversationId} " +
                                            "Attempt number: {Attempts} ",
                                            nameof(AutomationAssignment),
                                            userProfile.CompanyId,
                                            conversation.Id,
                                            4 - attempts);
                                    }
                                }

                                break;
                            case AssignmentType.QueueBased:
                                var teamMembers = await _appDbContext.CompanyTeamMembers
                                    .Where(
                                        x =>
                                            x.CompanyTeamId == automationTriggerAction.AssignedTeam.Id
                                            && x.Staff.Status == StaffStatus.Active)
                                    .Include(x => x.Staff.Identity)
                                    .ToListAsync();

                                automationTriggerAction.AssignedTeam.TeamAssignmentQueue = await _appDbContext
                                    .CompanyTeamAssignmentQueues
                                    .FirstOrDefaultAsync(
                                        x => x.CompanyTeamId == automationTriggerAction.AssignedTeam.Id);
                                try
                                {
                                    if (automationTriggerAction.AssignedTeam.TeamAssignmentQueue == null)
                                    {
                                        automationTriggerAction.AssignedTeam.TeamAssignmentQueue =
                                            new TeamAssignmentQueue
                                            {
                                                CompanyTeamId = automationTriggerAction.AssignedTeam.Id
                                            };

                                        _appDbContext.CompanyTeamAssignmentQueues.Add(
                                            automationTriggerAction.AssignedTeam.TeamAssignmentQueue);

                                        await _appDbContext.SaveChangesAsync();
                                    }

                                    if (automationTriggerAction.AssignedTeam.TeamAssignmentQueue.StaffIds?.Count > 0)
                                    {
                                        var staffId = automationTriggerAction.AssignedTeam
                                            .TeamAssignmentQueue
                                            .StaffIds
                                            .FirstOrDefault();

                                        var staff = await _appDbContext.UserRoleStaffs
                                            .Include(x => x.Identity)
                                            .FirstOrDefaultAsync(
                                                x =>
                                                    x.CompanyId == userProfile.CompanyId
                                                    && x.IdentityId == staffId);

                                        automationTriggerAction.AssignedTeam
                                            .TeamAssignmentQueue
                                            .StaffIds
                                            .Remove(staffId);

                                        automationTriggerAction.AssignedTeam
                                                .TeamAssignmentQueue
                                                .StaffIds = automationTriggerAction.AssignedTeam
                                                                .TeamAssignmentQueue
                                                                .StaffIds
                                                                .ToList();

                                        await _appDbContext.SaveChangesAsync();

                                        if (staff.Status == StaffStatus.Away)
                                        {
                                            _logger.LogInformation(
                                                "[{MethodName}] Failed to assign, staff status away. " +
                                                "Company Id: {CompanyId} " +
                                                "Conversation Id: {ConversationId} " +
                                                "Attempt number: {Attempts} " +
                                                "Staff to be assigned: {StaffIdentityId}",
                                                nameof(AutomationAssignment),
                                                userProfile.CompanyId,
                                                conversation.Id,
                                                4 - attempts,
                                                staff.IdentityId);
                                            await AutomationAssignment(
                                                userProfile,
                                                automationTriggerAction,
                                                isTriggerUpdate,
                                                regexValue,
                                                attempts - 1,
                                                changedBy: changedBy);
                                        }
                                        else
                                        {
                                            await _conversationMessageService.ChangeConversationAssignee(
                                                conversation,
                                                staff,
                                                false,
                                                changedBy: changedBy);
                                            _logger.LogInformation(
                                                "[{MethodName}] Successfully assigned to staff. " +
                                                "Company Id: {CompanyId} " +
                                                "Conversation Id: {ConversationId} " +
                                                "Attempt number: {Attempts} " +
                                                "Staff assigned: {StaffIdentityId}",
                                                nameof(AutomationAssignment),
                                                userProfile.CompanyId,
                                                conversation.Id,
                                                4 - attempts,
                                                staff.IdentityId);
                                        }
                                    }
                                    else
                                    {
                                        if (teamMembers.Count == 0)
                                        {
                                            _logger.LogInformation(
                                                "[{MethodName}] Retry, no active staff found. " +
                                                "Company Id: {CompanyId} " +
                                                "Conversation Id: {ConversationId} " +
                                                "Attempt number: {Attempts} ",
                                                nameof(AutomationAssignment),
                                                userProfile.CompanyId,
                                                conversation.Id,
                                                4 - attempts);
                                        }

                                        var queueStaffIds = teamMembers
                                            .Select(x => x.Staff.IdentityId)
                                            .ToList();

                                        automationTriggerAction.AssignedTeam
                                            .TeamAssignmentQueue
                                            .StaffIds = queueStaffIds;

                                        await _appDbContext.SaveChangesAsync();

                                        _logger.LogInformation(
                                            "[{MethodName}] Repopulate TeamAssignmentQueues StaffIds." +
                                            "Company Id: {CompanyId} " +
                                            "Conversation Id: {ConversationId} " +
                                            "Attempt number: {Attempts} " +
                                            "Team Id: {TeamId} ",
                                            nameof(AutomationAssignment),
                                            userProfile.CompanyId,
                                            conversation.Id,
                                            4 - attempts,
                                            automationTriggerAction.AssignedTeam.Id);

                                        await AutomationAssignment(
                                            userProfile,
                                            automationTriggerAction,
                                            isTriggerUpdate,
                                            changedBy: changedBy,
                                            attempts: attempts - 1);
                                    }
                                }
                                catch (Exception ex)
                                {
                                    _logger.LogError(
                                        ex,
                                        "[{MethodName}] Team queue-based assignment error in automation rule {AutomationRuleId} action" +
                                        " {AutomationActionId} for user profile {UserProfileId}: {ExceptionMessage}",
                                        nameof(AutomationAssignment),
                                        automationTriggerAction?.AssignmentRuleId,
                                        automationTriggerAction?.Id,
                                        userProfile?.Id,
                                        ex.Message);
                                }

                                break;
                            default:
                                await _conversationMessageService.ChangeConversationAssignee(
                                    conversation,
                                    null,
                                    false,
                                    changedBy: changedBy);

                                break;
                        }
                    }
                    else
                    {
                        await _conversationMessageService.ChangeConversationAssignee(
                            conversation,
                            null,
                            false,
                            changedBy: changedBy);
                    }

                    if (conversation.AssignedTeamId == automationTriggerAction.AssignedTeamId
                        && automationTriggerAction.TeamAssignmentType != AssignmentType.Unassigned)
                    {
                        return;
                    }

                    conversation = await _conversationMessageService.ChangeConversationAssignedTeam(
                        conversation,
                        automationTriggerAction.AssignedTeam,
                        isTriggerUpdate,
                        changedBy: changedBy);

                    _logger.LogInformation(
                        "[{MethodName}] Successfully assigned to team. " +
                        "Company Id: {CompanyId} " +
                        "Conversation Id: {ConversationId} " +
                        "Attempt number: {Attempts} " +
                        "Team assigned: {TeamId}",
                        nameof(AutomationAssignment),
                        userProfile.CompanyId,
                        conversation.Id,
                        4 - attempts,
                        automationTriggerAction.AssignedTeam.Id);
                    break;
                case AssignmentType.AssignWithRegExValue:
                    var regexAssignee = await _appDbContext.UserRoleStaffs
                        .FirstOrDefaultAsync(
                            x =>
                                x.CompanyId == userProfile.CompanyId
                                && x.QRCodeIdentity == regexValue);

                    if (regexAssignee != null)
                    {
                        await _conversationMessageService.ChangeConversationAssignee(
                            conversation,
                            regexAssignee,
                            isTriggerUpdate,
                            changedBy: changedBy);

                        await _conversationMessageService.ChangeConversationAssignedTeam(
                            conversation,
                            companyTeam: null,
                            isTriggerUpdate);

                        _logger.LogInformation(
                            "[{MethodName}] Successfully assigned to staff. " +
                            "Company Id: {CompanyId} " +
                            "Conversation Id: {ConversationId} " +
                            "Attempt number: {Attempts} " +
                            "Staff assigned: {StaffIdentityId}",
                            nameof(AutomationAssignment),
                            userProfile.CompanyId,
                            conversation.Id,
                            4 - attempts,
                            regexAssignee.IdentityId);
                    }

                    break;
                case AssignmentType.Unassigned:
                    await _conversationMessageService.ChangeConversationAssignee(
                        conversation,
                        null,
                        isTriggerUpdate,
                        changedBy: changedBy);

                    await _conversationMessageService.ChangeConversationAssignedTeam(
                        conversation,
                        companyTeam: null,
                        isTriggerUpdate);

                    _logger.LogInformation(
                        "[{MethodName}] Successfully unassigned. " +
                        "Company Id: {CompanyId} " +
                        "Conversation Id: {ConversationId} " +
                        "Attempt number: {Attempts} ",
                        nameof(AutomationAssignment),
                        userProfile.CompanyId,
                        conversation.Id,
                        4 - attempts);
                    break;
            }
        }

        private async Task AssignAssignee(
            Conversation conversation,
            AssignmentRule assignmentRule,
            ConversationMessage conversationMessage,
            bool isTriggerUpdate = true,
            int attempts = 3)
        {
            bool assigned = false;

            if (attempts <= 0)
            {
                return;
            }

            var userProfileIdStatus = await GetUserProfileIdStatusByIdAsync(
                conversation.CompanyId,
                conversation.UserProfileId);

            if (userProfileIdStatus.ActiveStatus != ActiveStatus.Active)
            {
                return;
            }

            switch (assignmentRule.AssignmentType)
            {
                // Queue-based or team queue-based
                case AssignmentType.QueueBased:
                    var companyAssignmentQueue = await _appDbContext.CompanyAssignmentQueues
                        .FirstOrDefaultAsync(x => x.CompanyId == conversation.CompanyId);

                    if (companyAssignmentQueue == null)
                    {
                        companyAssignmentQueue = new AssignmentQueue
                        {
                            CompanyId = conversation.CompanyId
                        };

                        _appDbContext.CompanyAssignmentQueues.Add(companyAssignmentQueue);
                    }

                    // Remove ids that have been removed from the assigned team
                    if (assignmentRule.AssignedTeamId.HasValue
                        && companyAssignmentQueue.StaffIds is not null)
                    {
                        var latestTeamMemberIdentityIdList = await _appDbContext.CompanyTeamMembers
                            .Where(x => x.CompanyTeamId == assignmentRule.AssignedTeamId.Value)
                            .Select(x => x.Staff.IdentityId)
                            .ToListAsync();

                        companyAssignmentQueue.StaffIds?
                            .RemoveAll(staffIdentityId => !latestTeamMemberIdentityIdList.Contains(staffIdentityId));
                    }

                    if (companyAssignmentQueue.StaffIds?.Count > 0)
                    {
                        var staffId = companyAssignmentQueue.StaffIds.FirstOrDefault();

                        var staff = await _appDbContext.UserRoleStaffs
                            .Where(x => x.CompanyId == conversation.CompanyId && x.IdentityId == staffId)
                            .Include(x => x.Identity)
                            .FirstOrDefaultAsync();

                        companyAssignmentQueue.StaffIds.Remove(staffId);
                        companyAssignmentQueue.StaffIds = companyAssignmentQueue.StaffIds.ToList();

                        await _appDbContext.SaveChangesAsync();

                        if (staff.Status == StaffStatus.Away)
                        {
                            _logger.LogInformation(
                                "[{MethodName}] Failed to assign, staff status away. " +
                                "Company Id: {CompanyId} " +
                                "Conversation Id: {ConversationId} " +
                                "Attempt number: {Attempts} " +
                                "Staff to be assigned: {StaffIdentityId}",
                                nameof(AssignAssignee),
                                conversation.CompanyId,
                                conversation.Id,
                                4 - attempts,
                                staff.IdentityId);
                            await AssignAssignee(
                                conversation,
                                assignmentRule,
                                conversationMessage,
                                isTriggerUpdate,
                                attempts - 1);
                        }
                        else
                        {
                            await _conversationMessageService.ChangeConversationAssignee(
                                conversation,
                                staff,
                                isTriggerUpdate);

                            await _conversationMessageService.ChangeConversationAssignedTeam(
                                conversation,
                                companyTeam: null,
                                isTriggerUpdate);

                            _logger.LogInformation(
                                "[{MethodName}] Successfully assigned to staff. " +
                                "Company Id: {CompanyId} " +
                                "Conversation Id: {ConversationId} " +
                                "Attempt number: {Attempts} " +
                                "Staff assigned: {StaffIdentityId}",
                                nameof(AutomationAssignment),
                                conversation.CompanyId,
                                conversation.Id,
                                4 - attempts,
                                staff.IdentityId);

                            assigned = true;
                        }
                    }
                    else
                    {
                        var latestTeamMemberIdList = await _appDbContext.CompanyTeamMembers
                            .Where(x => x.CompanyTeamId == assignmentRule.AssignedTeamId)
                            .Select(x => x.StaffId)
                            .ToListAsync();

                        var queueStaffIds =
                            await _appDbContext.UserRoleStaffs
                                .Where(
                                    staff =>
                                        ((assignmentRule.AssignedTeamId.HasValue
                                          && latestTeamMemberIdList.Any(x => x == staff.Id)) // Team queue-based
                                         || (!assignmentRule.AssignedTeamId.HasValue
                                             && staff.CompanyId == assignmentRule.CompanyId)) // Queue-based
                                        && staff.Status == StaffStatus.Active)
                                .Select(x => x.IdentityId)
                                .ToListAsync();

                        if (queueStaffIds.Count == 0)
                        {
                            _logger.LogInformation(
                                "[{MethodName}] Retry, no active staff found. " +
                                "Company Id: {CompanyId} " +
                                "Conversation Id: {ConversationId} " +
                                "Attempt number: {Attempts} ",
                                nameof(AssignAssignee),
                                conversation.CompanyId,
                                conversation.Id,
                                4 - attempts);
                        }

                        companyAssignmentQueue.StaffIds = queueStaffIds;

                        await _appDbContext.SaveChangesAsync();

                        await AssignAssignee(
                            conversation,
                            assignmentRule,
                            conversationMessage,
                            isTriggerUpdate,
                            attempts - 1);
                    }

                    break;
                case AssignmentType.SpecificPerson:
                    if (assignmentRule.AssignedStaff == null)
                    {
                        assignmentRule.AssignedStaff = await _appDbContext.UserRoleStaffs
                            .Where(x => x.Id == assignmentRule.AssignedStaffId).FirstOrDefaultAsync();
                    }

                    if (assignmentRule.AssignedStaff.Status == StaffStatus.Active ||
                        assignmentRule.AssignmentType == AssignmentType.SpecificPerson)
                    {
                        await _conversationMessageService.ChangeConversationAssignee(
                            conversation,
                            assignmentRule.AssignedStaff,
                            isTriggerUpdate);

                        await _conversationMessageService.ChangeConversationAssignedTeam(
                            conversation,
                            companyTeam: null,
                            isTriggerUpdate);

                        assigned = true;
                    }

                    break;
                case AssignmentType.SpecificGroup:
                    try
                    {
                        // assignmentRule.AssignedTeam.Members = await _appDbContext.CompanyTeamMembers.Where(x => x.CompanyTeamId == assignmentRule.AssignedTeam.Id && x.Staff.Identity.EmailConfirmed).Include(x => x.Staff.Identity).ToListAsync();
                        // assignmentRule.AssignedTeam.TeamAssignmentQueue = await _appDbContext.CompanyTeamAssignmentQueues.Where(x => x.CompanyTeamId == assignmentRule.AssignedTeam.Id).FirstOrDefaultAsync();

                        // conversation = await _conversationMessageService.ChangeConversationAssignedTeam(conversation, assignmentRule.AssignedTeam, isTriggerUpdate);

                        // if (assignmentRule.AssignedTeam.TeamAssignmentQueue == null)
                        // {
                        //    assignmentRule.AssignedTeam.TeamAssignmentQueue = new TeamAssignmentQueue { CompanyTeamId = assignmentRule.AssignedTeam.Id };
                        //    _appDbContext.CompanyTeamAssignmentQueues.Add(assignmentRule.AssignedTeam.TeamAssignmentQueue);
                        //    await _appDbContext.SaveChangesAsync();
                        // }

                        // if (assignmentRule.AssignedTeam.TeamAssignmentQueue.StaffIds?.Count > 0)
                        // {
                        //    var staffId = assignmentRule.AssignedTeam.TeamAssignmentQueue.StaffIds.FirstOrDefault();
                        //    var staff = await _appDbContext.UserRoleStaffs.Where(x => x.IdentityId == staffId).Include(x => x.Identity).FirstOrDefaultAsync();

                        // assignmentRule.AssignedTeam.TeamAssignmentQueue.StaffIds.Remove(staffId);
                        //    assignmentRule.AssignedTeam.TeamAssignmentQueue.StaffIds = assignmentRule.AssignedTeam.TeamAssignmentQueue.StaffIds.ToList();

                        // await _appDbContext.SaveChangesAsync();

                        // if (staff.Status == StaffStatus.Away || !staff.Identity.EmailConfirmed)
                        //    {
                        //        await AssignAssignee(conversation, assignmentRule, conversationMessage, isTriggerUpdate, attempts - 1);
                        //    }
                        //    else
                        //    {
                        //        await _conversationMessageService.ChangeConversationAssignee(conversation, staff, isTriggerUpdate);
                        //        assigned = true;
                        //    }
                        // }
                        // else
                        // {
                        //    var queue_staffIds = assignmentRule.AssignedTeam.Members.Select(x => x.Staff.IdentityId).ToList();
                        //    assignmentRule.AssignedTeam.TeamAssignmentQueue.StaffIds = queue_staffIds;
                        //    await _appDbContext.SaveChangesAsync();
                        //    await AssignAssignee(conversation, assignmentRule, conversationMessage, isTriggerUpdate, attempts - 1);
                        // }
                        // Delete
                        if (assignmentRule.TeamAssignmentType.HasValue)
                        {
                            switch (assignmentRule.TeamAssignmentType)
                            {
                                case AssignmentType.SpecificPerson:
                                    if (assignmentRule.AssignedStaff != null)
                                    {
                                        // assigned to member of a team
                                        if (await _appDbContext.CompanyTeamMembers.AnyAsync(
                                                x =>
                                                    x.CompanyTeamId == assignmentRule.AssignedTeam.Id
                                                    && x.StaffId == assignmentRule.AssignedStaff.Id))
                                        {
                                            await _conversationMessageService.ChangeConversationAssignee(
                                                conversation,
                                                assignmentRule.AssignedStaff,
                                                false);
                                        }
                                        else
                                        {
                                            await _conversationMessageService.ChangeConversationAssignee(
                                                conversation,
                                                null,
                                                false);
                                        }
                                    }

                                    break;
                                case AssignmentType.QueueBased:
                                    var teamMembers = await _appDbContext.CompanyTeamMembers
                                        .Where(
                                            x =>
                                                x.CompanyTeamId == assignmentRule.AssignedTeam.Id
                                                && x.Staff.Status == StaffStatus.Active)
                                        .Include(x => x.Staff.Identity)
                                        .ToListAsync();

                                    assignmentRule.AssignedTeam.TeamAssignmentQueue = await _appDbContext
                                        .CompanyTeamAssignmentQueues
                                        .FirstOrDefaultAsync(x => x.CompanyTeamId == assignmentRule.AssignedTeam.Id);
                                    try
                                    {
                                        if (assignmentRule.AssignedTeam.TeamAssignmentQueue == null)
                                        {
                                            assignmentRule.AssignedTeam.TeamAssignmentQueue = new TeamAssignmentQueue
                                            {
                                                CompanyTeamId = assignmentRule.AssignedTeam.Id
                                            };
                                            _appDbContext.CompanyTeamAssignmentQueues
                                                .Add(assignmentRule.AssignedTeam.TeamAssignmentQueue);

                                            await _appDbContext.SaveChangesAsync();
                                        }

                                        if (assignmentRule.AssignedTeam.TeamAssignmentQueue.StaffIds?.Count > 0)
                                        {
                                            var staffId = assignmentRule.AssignedTeam
                                                .TeamAssignmentQueue
                                                .StaffIds
                                                .FirstOrDefault();

                                            var staff = await _appDbContext.UserRoleStaffs
                                                .Where(
                                                    x =>
                                                        x.CompanyId == conversation.CompanyId
                                                        && x.IdentityId == staffId)
                                                .Include(x => x.Identity)
                                                .FirstOrDefaultAsync();

                                            assignmentRule.AssignedTeam
                                                .TeamAssignmentQueue
                                                .StaffIds
                                                .Remove(staffId);

                                            assignmentRule.AssignedTeam
                                                    .TeamAssignmentQueue
                                                    .StaffIds = assignmentRule.AssignedTeam
                                                                    .TeamAssignmentQueue
                                                                    .StaffIds
                                                                    .ToList();

                                            await _appDbContext.SaveChangesAsync();

                                            if (staff.Status == StaffStatus.Away)
                                            {
                                                _logger.LogInformation(
                                                    "[{MethodName}] Failed to assign, staff status away. " +
                                                    "Company Id: {CompanyId} " +
                                                    "Conversation Id: {ConversationId} " +
                                                    "Attempt number: {Attempts} " +
                                                    "Staff to be assigned: {StaffIdentityId}",
                                                    nameof(AssignAssignee),
                                                    conversation.CompanyId,
                                                    conversation.Id,
                                                    4 - attempts,
                                                    staff.IdentityId);
                                                await AssignAssignee(
                                                    conversation,
                                                    assignmentRule,
                                                    conversationMessage,
                                                    isTriggerUpdate,
                                                    attempts - 1);
                                            }
                                            else
                                            {
                                                await _conversationMessageService.ChangeConversationAssignee(
                                                    conversation,
                                                    staff,
                                                    false);
                                                _logger.LogInformation(
                                                    "[{MethodName}] Successfully assigned to staff. " +
                                                    "Company Id: {CompanyId} " +
                                                    "Conversation Id: {ConversationId} " +
                                                    "Attempt number: {Attempts} " +
                                                    "Staff assigned: {StaffIdentityId}",
                                                    nameof(AutomationAssignment),
                                                    conversation.CompanyId,
                                                    conversation.Id,
                                                    4 - attempts,
                                                    staff.IdentityId);
                                            }
                                        }
                                        else
                                        {
                                            if (teamMembers.Count == 0)
                                            {
                                                _logger.LogInformation(
                                                    "[{MethodName}] Retry, no active staff found. " +
                                                    "Company Id: {CompanyId} " +
                                                    "Conversation Id: {ConversationId} " +
                                                    "Attempt number: {Attempts} ",
                                                    nameof(AssignAssignee),
                                                    conversation.CompanyId,
                                                    conversation.Id,
                                                    attempts - 1);
                                            }

                                            var queueStaffIds = teamMembers
                                                .Select(x => x.Staff.IdentityId)
                                                .ToList();

                                            assignmentRule.AssignedTeam.TeamAssignmentQueue.StaffIds = queueStaffIds;

                                            await _appDbContext.SaveChangesAsync();

                                            await AssignAssignee(
                                                conversation,
                                                assignmentRule,
                                                conversationMessage,
                                                isTriggerUpdate,
                                                attempts - 1);
                                        }
                                    }
                                    catch (Exception ex)
                                    {
                                        _logger.LogError(
                                            ex,
                                            "[{MethodName}] Team queue-based assignment error in automation rule {AutomationRuleId}" +
                                            " for user profile {UserProfileId}: {ExceptionMessage}",
                                            nameof(AssignAssignee),
                                            assignmentRule.Id,
                                            conversation?.UserProfileId,
                                            ex.Message);
                                    }

                                    break;
                                default:
                                    await _conversationMessageService.ChangeConversationAssignee(
                                        conversation,
                                        null,
                                        false);

                                    // await _signalRService.SendTeamAssignedPushNotification(conversation);
                                    break;
                            }
                        }
                        else
                        {
                            await _conversationMessageService.ChangeConversationAssignee(conversation, null, false);
                        }

                        if (conversation.AssignedTeamId == assignmentRule.AssignedTeamId
                            && assignmentRule.TeamAssignmentType != AssignmentType.Unassigned)
                        {
                            return;
                        }

                        conversation = await _conversationMessageService.ChangeConversationAssignedTeam(
                            conversation,
                            assignmentRule.AssignedTeam,
                            isTriggerUpdate);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(
                            ex,
                            "[{MethodName}] Team assignment error in automation rule {AutomationRuleId}" +
                            " for user profile {UserProfileId}: {ExceptionMessage}",
                            nameof(AssignAssignee),
                            assignmentRule.Id,
                            conversation?.UserProfileId,
                            ex.Message);
                    }

                    break;
                case AssignmentType.Unassigned:
                    assigned = true;

                    await _conversationMessageService.ChangeConversationAssignee(
                        conversation,
                        null,
                        isTriggerUpdate);

                    await _conversationMessageService.ChangeConversationAssignedTeam(
                        conversation,
                        companyTeam: null,
                        isTriggerUpdate);

                    break;
            }

            if (assigned && isTriggerUpdate)
            {
                // try
                // {
                //    if (conversation.ActiveStatus == ActiveStatus.Active)
                //    {
                //        conversationMessage = await _appDbContext.ConversationMessages.Where(x => x.Id == conversationMessage.Id).FirstOrDefaultAsync();
                //        await _signalRService.SendNewMessagePushNotification(conversation, conversationMessage);
                //    }
                //    //await _emailNotificationService.NewMessageNotification(conversation, conversationMessage);
                // }
                // catch (Exception ex)
                // {
                //    _logger.LogError($"Notification error: {ex.Message}");
                // }
                try
                {
                    if (assignmentRule.AutomationActions.Any(
                            x => x.AutomatedTriggerType != AutomatedTriggerType.Assignment))
                    {
                        switch (assignmentRule.AutomationActions.FirstOrDefault().AutomatedTriggerType)
                        {
                            case AutomatedTriggerType.SendMessage:
                                if (conversation.Assignee != null)
                                {
                                    await _userProfileService.SetFieldValueByFieldNameSafe(
                                        conversation.UserProfileId,
                                        "ContactOwner",
                                        conversation.Assignee.IdentityId);
                                }

                                break;
                        }

                        await TriggerAction(
                            conversation.UserProfileId,
                            assignmentRule.AssignmentId);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(
                        ex,
                        "[{MethodName}] Automation rule {AutomationRuleId} post-assignment trigger update error for {UserProfileId}: {ExceptionMessage}",
                        nameof(AssignAssignee),
                        assignmentRule.AssignmentId,
                        conversation?.UserProfileId,
                        ex.Message);
                }
            }
        }

        private async Task LogAutomationTriggered(
            string companyId,
            string userProfileId,
            string triggeredSource,
            AutomationAction automationAction,
            AutomationActionRecord automationActionRecord)
        {
            await _auditHubAuditLogService.CreateAutomationTriggeredLogAsync(
                companyId,
                userProfileId,
                new AutomationTriggeredLogData(
                    triggeredSource,
                    automationAction.AutomatedTriggerType.ToString(),
                    automationActionRecord.Id,
                    "success",
                    null));
        }

        private async Task LogAutomationError(
            string companyId,
            string userProfileId,
            string triggeredSource,
            AutomationAction automationAction,
            string exceptionMessage)
        {
            await _auditHubAuditLogService.CreateAutomationTriggeredLogAsync(
                companyId,
                userProfileId,
                new AutomationTriggeredLogData(
                    triggeredSource,
                    automationAction.AutomatedTriggerType.ToString(),
                    null,
                    "failed",
                    exceptionMessage));
        }

        public async Task TriggerAutomationAction(
            string userProfileId,
            long assignmentId,
            long automationId,
            bool isSchedule = true,
            bool isSendMessage = true,
            string regexValue = null,
            long? conversationMessageId = null)
            => await TriggerAutomationAction(
                userProfileId,
                assignmentId,
                automationId,
                isSchedule,
                isSendMessage,
                regexValue,
                conversationMessageId,
                null);

        private async Task<(string UserProfileId, ActiveStatus? ActiveStatus)> GetUserProfileIdStatusByIdAsync(string companyId, string userProfileId)
        {
            var userProfile = await _appDbContext.UserProfiles
                .Where(x => x.CompanyId == companyId && x.Id == userProfileId)
                .Select(x
                    => new
                    {
                        x.Id,
                        x.ActiveStatus
                    })
                .FirstOrDefaultAsync();

            if (userProfile is not { ActiveStatus: ActiveStatus.Active })
            {
                _logger.LogWarning(
                    "[Automation] No active contact with id {ContactId} found in company {CompanyId}",
                    userProfileId,
                    companyId);
            }

            return (userProfile?.Id, userProfile?.ActiveStatus);
        }
    }
}