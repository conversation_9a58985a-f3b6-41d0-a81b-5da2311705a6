// Test cases are disabled due to tests need to be fixed
// Team: Team MM7

// using System.Diagnostics;
// using System.IdentityModel.Tokens.Jwt;
// using System.Security.Claims;
// using System.Text;
// using Auth0.AuthenticationApi;
// using Auth0.AuthenticationApi.Models;
// using Auth0.ManagementApi;
// using Auth0.ManagementApi.Models;
// using Auth0.ManagementApi.Models.Users;
// using Auth0.ManagementApi.Paging;
// using AutoMapper;
// using Hangfire;
// using Hangfire.MemoryStorage;
// using isRock.LineBot.Extensions;
// using Microsoft.AspNetCore.Http;
// using Microsoft.AspNetCore.Identity;
// using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
// using Microsoft.AspNetCore.Mvc;
// using Microsoft.EntityFrameworkCore;
// using Microsoft.Extensions.Caching.Distributed;
// using Microsoft.Extensions.Caching.Memory;
// using Microsoft.Extensions.Configuration;
// using Microsoft.Extensions.Logging;
// using Microsoft.Extensions.Logging.Abstractions;
// using Microsoft.Extensions.Options;
// using Microsoft.IdentityModel.Tokens;
// using Moq;
// using Newtonsoft.Json;
// using Sleekflow.Apis.ShareHub.Api;
// using Sleekflow.Apis.TenantHub.Api;
// using Sleekflow.Apis.TenantHub.Client;
// using Sleekflow.Apis.TenantHub.Model;
// using Sleekflow.Core.Tests.Tools;
// using Sleekflow.Powerflow.Apis.Controllers;
// using Sleekflow.Powerflow.Apis.ViewModels;
// using StackExchange.Redis;
// using Stripe;
// using Travis_backend.AccountAuthenticationDomain.Models;
// using Travis_backend.AccountAuthenticationDomain.Services;
// using Travis_backend.Auth0.Configuration;
// using Travis_backend.Auth0.Controllers;
// using Travis_backend.Auth0.Controllers.Webhook;
// using Travis_backend.Auth0.Models;
// using Travis_backend.Auth0.Services;
// using Travis_backend.Auth0.Services.Auth0;
// using Travis_backend.Cache;
// using Travis_backend.CommonDomain.Models;
// using Travis_backend.CommonDomain.Services;
// using Travis_backend.CompanyDomain.Services;
// using Travis_backend.Configuration;
// using Travis_backend.Controllers.TenantHubControllers;
// using Travis_backend.ConversationServices;
// using Travis_backend.Database;
// using Travis_backend.Database.Services;
// using Travis_backend.Enums;
// using Travis_backend.Helpers;
// using Travis_backend.Mapping;
// using Travis_backend.Services.Internal;
// using Travis_backend.SignalR;
// using Travis_backend.Auth0.Controllers;
// using Travis_backend.CommonDomain.Repositories;
// using Travis_backend.Database.Services;
// using Travis_backend.CommonDomain.Services;
// using Travis_backend.CompanyDomain.Repositories;
// using Travis_backend.ContactDomain.Repositories;
// using Travis_backend.ContactDomain.Services;
// using Travis_backend.**********************;
// using Travis_backend.FlowHubs;
// using Travis_backend.SubscriptionPlanDomain.Services;
// using AccessTokenResponse = Auth0.AuthenticationApi.Models.AccessTokenResponse;
// using Enum = System.Enum;
// using GlobalConfiguration = Hangfire.GlobalConfiguration;
// using Role = Auth0.ManagementApi.Models.Role;
// using User = Auth0.ManagementApi.Models.User;
//
// namespace Sleekflow.Core.Tests;
//
// [SetUpFixture]
// public abstract class Auth0RootTest
// {
//     private Stopwatch _stopwatch;
//     protected string SecretKey;
//
//     protected ApplicationDbContext _dbContext;
//     // protected BaseDbContext _dbContext;
//     protected BaseDbContext _baseDbContext;
//     protected IDbContextService _dbContextService;
//     protected AuthenticationApiClient _authenticaionApiClient;
//     protected ManagementApiClient _managementApiClient;
//     protected Auth0ActionEventController _auth0Event;
//     protected HttpClientManagementConnection _managementConnection;
//     protected HttpClient _httpClient;
//     protected SleekflowUserManager _userManager;
//
//     // protected RoleManager<IdentityRole> _roleManager;
//
//     // protected SleekflowSignInManager _signInManager;
//     protected IdentityOptions _options;
//     protected CoreService _coreService;
//     protected UserStore<ApplicationUser> _userStore;
//     protected JwtOptions _jwtOptions;
//     protected Auth0Config _auth0Config;
//     protected IMapper _mapper;
//
//     protected SleekflowClaimsTransformation _sleekflowClaimsTransformation;
//
//     // protected CustomAuthorizationHandler _customAuthorizationHandler;
//     protected InternalCmsDiveController _internalCmsDiveController;
//     protected InternalCmsAuth0Controller _internalCmsAuth0Controller;
//     protected AccountController _accountController;
//     protected Auth0AccountController _auth0AccountController;
//     protected EmailNotificationService _emailNotificationService;
//     protected IConfiguration _conf;
//     protected ITokenService _tokenService;
//
//     protected IManagementUsersApi _managementUsersApi;
//     protected IManagementCompaniesApi _managementCompaniesApi;
//     protected IManagementRolesApi _managementRolesApi;
//     protected IManagementFeaturesApi _managementFeaturesApi;
//     protected IManagementEnabledFeaturesApi _managementEnabledFeaturesApi;
//     protected ICompanyService _companyService;
//     protected IMfaService _mfaService;
//     protected ICountryService _countryService;
//     protected IFeaturesApi _featuresApi;
//     protected IEnabledFeaturesApi _enabledFeaturesApi;
//     protected IManagementIpWhitelistsApi _ipWhitelistsApi;
//
//     protected TenantHubController _tenantHubController;
//
//     [OneTimeSetUp]
//     public async Task OnInit()
//     {
//         try
//         {
//             _conf = AppConfigure.InitConfiguration();
//             var optionsBuilder = new DbContextOptionsBuilder<ApplicationDbContext>()
//                 .EnableSensitiveDataLogging()
//                 .UseQueryTrackingBehavior(QueryTrackingBehavior.NoTracking)
//                 // .UseInMemoryDatabase(databaseName:"UsersTest");                     // If you want to use inmemory database, enable it
//                 .UseSqlServer(
//                     _conf.GetConnectionString("DefaultConnection")); // If you want to use UAT database, enable it
//
//             var baseOptionsBuilder = new DbContextOptionsBuilder<BaseDbContext>()
//                 .EnableSensitiveDataLogging()
//                 .UseQueryTrackingBehavior(QueryTrackingBehavior.NoTracking)
//                 // .UseInMemoryDatabase(databaseName:"UsersTest");                     // If you want to use inmemory database, enable it
//                 .UseSqlServer(
//                     _conf.GetConnectionString("DefaultConnection")); // If you want to use UAT database, enable it
//
//             _baseDbContext = new BaseDbContext(baseOptionsBuilder.Options);
//             _dbContext = new ApplicationDbContext(optionsBuilder.Options);
//             // _dbContextService = new DbContextService(_conf, _baseDbContext, _baseDbContext);
//
//             var mockDbContextService = new Mock<IDbContextService>();
//
//             mockDbContextService.Setup(x => x.GetDbContext()).Returns(_baseDbContext);
//             _dbContextService = mockDbContextService.Object;
//
//             _userStore = new UserStore<ApplicationUser>(_dbContext);
//             var roleStore = new RoleStore<IdentityRole>(_dbContext);
//
//             _options = new IdentityOptions();
//
//             // Default Password settings.
//             _options.Password.RequireDigit = false;
//             _options.Password.RequireLowercase = false;
//             _options.Password.RequireNonAlphanumeric = false;
//             _options.Password.RequireUppercase = false;
//             _options.Password.RequiredLength = 6;
//             _options.ClaimsIdentity.RoleClaimType = _conf["Auth0:Namespace"] + _conf["Auth0:RoleClaimType"];
//             _options.ClaimsIdentity.UserIdClaimType = _conf["Auth0:Namespace"] + _conf["Auth0:UserIdClaimType"];
//             _options.ClaimsIdentity.UserNameClaimType = _conf["Auth0:Namespace"] + _conf["Auth0:UserNameClaimType"];
//             _options.ClaimsIdentity.EmailClaimType = _conf["Auth0:Namespace"] + _conf["Auth0:UserEmailClaimType"];
//
//             /*
//              * Mock the password hasher to make the parent UserManager work correctly when create password hash in db.
//              */
//             IOptions<IdentityOptions> idtOptionsAccessor = Options.Create(_options);
//             Mock<IPasswordHasher<ApplicationUser>> passwordHasher = new ();
//
//             SecretKey = _conf["Auth0:ActionIssuer"] + _conf["Auth0:ActionAudience"] + "+wsadz4gI_3DUXI8P";
//
//             _jwtOptions = (new JwtOptions()
//             {
//                 Key = "83A74F5963336617701F83BBB8A4201212C42A51E721DD51CAB28201ADEDF8BB",
//                 Issuer = "https://travis-crm-api-hk.azurewebsites.net",
//                 Audience = "https://travis-crm-api-hk.azurewebsites.net",
//                 Lifetime = 365
//             });
//
//             var auth0Config = _conf.GetSection("Auth0").Get<Auth0Config>();
//             var mapperConf = new MapperConfiguration(
//                 c =>
//                 {
//                     c.AddProfile<ViewModelToEntityMappingProfile>();
//                 });
//
//             _auth0Config = auth0Config;
//             Mock<IUserTwoFactorTokenProvider<ApplicationUser>> mockTokenProvider =
//                 new Mock<IUserTwoFactorTokenProvider<ApplicationUser>>();
//             mockTokenProvider
//                 .Setup(
//                     o => o.GenerateAsync(
//                         It.IsAny<string>(),
//                         It.IsAny<UserManager<ApplicationUser>>(),
//                         It.IsAny<ApplicationUser>()))
//                 .ReturnsAsync("diuLaMa");
//             mockTokenProvider
//                 .Setup(
//                     o => o.ValidateAsync(
//                         It.IsAny<string>(),
//                         It.IsAny<string>(),
//                         It.IsAny<UserManager<ApplicationUser>>(),
//                         It.IsAny<ApplicationUser>()))
//                 .ReturnsAsync(true);
//
//             _httpClient = new HttpClient();
//             _managementConnection = new HttpClientManagementConnection(
//                 _httpClient,
//                 new HttpClientManagementConnectionOptions()
//                 {
//                     NumberOfHttpRetries = 10
//                 });
//             _authenticaionApiClient = new AuthenticationApiClient(new Uri($"https://{auth0Config.Domain}"));
//             var token = await _authenticaionApiClient.GetTokenAsync(
//                 new ClientCredentialsTokenRequest()
//                 {
//                     ClientId = auth0Config.ClientId,
//                     ClientSecret = auth0Config.ClientSecret,
//                     // Audience = auth0Config.Audience
//                     Audience = "https://sleekflow-dev.eu.auth0.com/api/v2/"
//                 });
//
//             _managementApiClient =
//                 new ManagementApiClient(
//                     token.AccessToken,
//                     auth0Config.Domain,
//                     // new Uri(auth0Config.Audience).Host,
//                     // "https://sleekflow-dev.eu.auth0.com/api/v2/",
//                     _managementConnection);
//
//             var distributedCacheOptions =
//                 Options.Create<MemoryDistributedCacheOptions>(new MemoryDistributedCacheOptions());
//             IDistributedCache distributedCache = new MemoryDistributedCache(distributedCacheOptions);
//
//             var cacheManagerService = new CacheManagerService(
//                 ConnectionMultiplexer.Connect(
//                     "sleekflow-redis739dbd6c.redis.cache.windows.net:6380,password=LSpaOPbm5b308TOUYaMDQwfDVUQZV7OODAzCaBAySj0=,ssl=True,abortConnect=False"),
//                 NullLogger<CacheManagerService>.Instance
//             );
//
//             var companyInfoCacheService = new CompanyInfoCacheService(_dbContext, cacheManagerService);
//
//             var httpContext = new HttpContextAccessor();
//             var mockUserManagerLogger = new Mock<ILogger<SleekflowUserManager>>();
//             _mapper = new Mapper(mapperConf);
//             _userManager = new SleekflowUserManager(
//                 _userStore,
//                 idtOptionsAccessor,
//                 null,
//                 null!,
//                 null!,
//                 null!,
//                 null!,
//                 null!,
//                 mockUserManagerLogger.Object,
//                 auth0Config,
//                 _managementApiClient,
//                 _authenticaionApiClient,
//                 httpContext,
//                 cacheManagerService);
//
//             _userManager.PasswordHasher = passwordHasher.Object;
//             _userManager.RegisterTokenProvider("Default", mockTokenProvider.Object);
//             _userManager.RegisterTokenProvider(SleekflowTokenProviderOptions.InviteTokenProviderName, new InvitationTokenProvider());
//
//             // Mock<RoleManager<IdentityRole>> mockRoleManager = new Mock<RoleManager<IdentityRole>>();
//             // _roleManager = mockRoleManager.Object;
//
//             var mockCountryRepository = new Mock<ICountryRepository>();
//             var mockTokenServiceLogger = new Mock<ILogger<Auth0TokenService>>();
//             var mockDefaultSubscriptionPlanIdGetter = new Mock<IDefaultSubscriptionPlanIdGetter>();
//             _tokenService = new Auth0TokenService(mockTokenServiceLogger.Object, auth0Config, _userManager);
//             _countryService = new CountryService(mockCountryRepository.Object);
//
//             var mockCoreLogger = new Mock<ILogger<CoreService>>();
//             // _coreService = new CoreService(
//             /*   _dbContext,
//                _mapper,
//                _conf,
//                mockCoreLogger.Object,
//                null,
//                null,
//                null,
//                null,
//                null,
//                null,
//                null,
//                _userManager,
//                null,
//                Options.Create(_jwtOptions),
//                null,
//                null,
//                null,
//                _tokenService); */
//
//             var mockHttpClientFactory = new Mock<IHttpClientFactory>();
//             mockHttpClientFactory.Setup(x => x.CreateClient(It.IsAny<string>()))
//                 .Returns(new HttpClient());
//
//             // var mockCoreService = new Mock<CoreService>(
//             _coreService = new CoreService(
//                 _dbContext,
//                 _mapper,
//                 _conf,
//                 mockCoreLogger.Object,
//                 null,
//                 null,
//                 null,
//                 null,
//                 _userManager,
//                 Options.Create(_jwtOptions),
//                 null,
//                 null,
//                 null,
//                 null,
//                 _tokenService,
//                 mockHttpClientFactory.Object,
//                 _countryService,
//                 _dbContextService,
//                 new CompanyUsageCycleCalculator(),
//                 mockDefaultSubscriptionPlanIdGetter.Object,
//                 new Mock<ContactDeletionConfig>().Object,
//                 new Mock<IDistributedInvocationContextService>().Object,
//                 new UserProfileDuplicatedLogRepository(_dbContext) );
//             // {
//             // CallBase = true
//             // };
//
//             // mockCoreService.Setup(c => c.UpdateUserLastLoginAt(It.IsAny<string>(), It.IsAny<DateTime>()));
//             // _coreService = mockCoreService.Object;
//
//             var mockSignalRService = new Mock<ISignalRService>();
//             var mockActionEventLogger = new Mock<ILogger<Auth0ActionEventController>>();
//
//             var tenantHubHttpClientHandler = new HttpClientHandler();
//             var tenantHubHttpClient = new HttpClient(tenantHubHttpClientHandler)
//             {
//                 DefaultRequestHeaders =
//                 {
//                     {
//                         "X-Sleekflow-Key", _conf.GetValue<string>("TenantHub:Key")
//                     }
//                 }
//             };
//
//             var tenantHubConfig = new Configuration
//             {
//                 BasePath = _conf.GetValue<string>("TenantHub:Endpoint")
//             };
//
//             var jobStorage = new MemoryStorage();
//             GlobalConfiguration.Configuration.UseStorage(jobStorage);
//
//             _managementUsersApi = new ManagementUsersApi(
//                 tenantHubHttpClient,
//                 tenantHubConfig,
//                 tenantHubHttpClientHandler);
//             _managementRolesApi = new ManagementRolesApi(
//                 tenantHubHttpClient,
//                 tenantHubConfig,
//                 tenantHubHttpClientHandler);
//             _managementCompaniesApi = new ManagementCompaniesApi(
//                 tenantHubHttpClient,
//                 tenantHubConfig,
//                 tenantHubHttpClientHandler);
//             _managementFeaturesApi = new ManagementFeaturesApi(
//                 tenantHubHttpClient,
//                 tenantHubConfig,
//                 tenantHubHttpClientHandler);
//             _managementEnabledFeaturesApi = new ManagementEnabledFeaturesApi(
//                 tenantHubHttpClient,
//                 tenantHubConfig,
//                 tenantHubHttpClientHandler);
//
//             _auth0Event = new Auth0ActionEventController(
//                 _dbContext,
//                 mockSignalRService.Object,
//                 _mapper,
//                 mockActionEventLogger.Object,
//                 _userManager,
//                 _conf,
//                 _auth0Config!,
//                 _coreService,
//                 _managementEnabledFeaturesApi,
//                 _managementFeaturesApi,
//                 _ipWhitelistsApi);
//
//             var emailNotificationServiceLogger = new Mock<ILogger<EmailNotificationService>>();
//             _sleekflowClaimsTransformation =
//                 new SleekflowClaimsTransformation(idtOptionsAccessor, auth0Config, _userManager, null, null, null);
//
//             _emailNotificationService = new EmailNotificationService(
//                 _dbContext,
//                 _conf,
//                 emailNotificationServiceLogger.Object,
//                 null,
//                 null,
//                 null,
//                 null);
//
//             var mockMfaServiceLogger = new Mock<ILogger<Auth0MfaService>>();
//             _mfaService = new Auth0MfaService(
//                 mockMfaServiceLogger.Object,
//                 _userManager,
//                 _managementApiClient
//             );
//
//             var shareHubHttpClientHandler = new HttpClientHandler();
//             var shareHubClient = new HttpClient(shareHubHttpClientHandler)
//             {
//                 DefaultRequestHeaders =
//                 {
//                     {
//                         "X-Sleekflow-Key", _conf.GetValue<string>("ShareHub:Key")
//                     }
//                 }
//             };
//
//             var shareHubConfiguration = new Apis.ShareHub.Client.Configuration
//             {
//                 BasePath = _conf.GetValue<string>("ShareHub:Endpoint")
//             };
//
//             var qrCodeApi = new QrCodesApi(shareHubClient, shareHubConfiguration, shareHubHttpClientHandler);
//
//             var mockCompanyServiceLogger = new Mock<ILogger<CompanyService>>();
//             var mockCompanyUsageService = new Mock<ICompanyUsageService>();
//             var mockCompanyRepository = new Mock<ICompanyRepository>();
//             var cacheService = new Mock<ICacheManagerService>();
//             _companyService = new CompanyService(
//                 _dbContext,
//                 _conf,
//                 mockCompanyServiceLogger.Object,
//                 companyInfoCacheService,
//                 null,
//                 _mfaService,
//                 qrCodeApi,
//                 null,
//                 new Mock<IDbContextService>().Object,
//                 mockCompanyRepository.Object,
//                 mockCompanyUsageService.Object);
//
//             _tenantHubController = new TenantHubController(
//                 _userManager,
//                 _coreService,
//                 _managementRolesApi,
//                 _managementCompaniesApi,
//                 _managementEnabledFeaturesApi,
//                 _managementFeaturesApi,
//                 _managementUsersApi,
//                 cacheManagerService);
//
//             _internalCmsAuth0Controller = new InternalCmsAuth0Controller(_userManager, _dbContextService);
//
//             _auth0AccountController = new Auth0AccountController(
//                 _userManager,
//                 _auth0Config,
//                 _dbContextService,
//                 null,
//                 _coreService,
//                 _authenticaionApiClient,
//                 null,
//                 _featuresApi,
//                 _enabledFeaturesApi,
//                 idtOptionsAccessor,
//                 mockDefaultSubscriptionPlanIdGetter.Object,
//                 new Mock<StaffHooks>().Object
//             );
//         }
//         catch (Exception er)
//         {
//             throw new Exception(er.Message, er.InnerException);
//         }
//     }
//
//     [SetUp]
//     public async Task OnAuth0TestStart()
//     {
//         _stopwatch = Stopwatch.StartNew();
//         await TestContext.Progress.WriteLineAsync("Test start. ");
//     }
//
//     [TearDown]
//     public async Task OnAuth0TestEnd()
//     {
//         _stopwatch.Stop();
//         await TestContext.Progress.WriteLineAsync($"Test ended. Execute time: {_stopwatch.ElapsedMilliseconds}");
//     }
// }
//
// [TestFixture]
// public class Auth0ManagementTest : Auth0RootTest
// {
//     private int _testDataCreateCount = 1;
//
//     public async Task<UserCreateRequest> CreateUserRequest(ApplicationUser user)
//     {
//         var request = new UserCreateRequest()
//         {
//             UserId = user.Id,
//             UserName = user.UserName,
//             FullName = user.DisplayName,
//             FirstName = user.FirstName,
//             LastName = user.LastName,
//             Email = user.Email,
//             Connection = "Sleekflow-Username-Password-Authentication",
//             Password = "Pa55w0rd!",
//             AppMetadata = new
//             {
//                 Roles = JsonConvert.SerializeObject(await _userManager.GetRolesAsync(user)),
//             }
//         };
//
//         await TestContext.Progress.WriteLineAsync(
//             "Created User:\n" +
//             JsonConvert.SerializeObject(request, Formatting.Indented));
//
//         return request;
//     }
//
//     [Test]
//     [Category("Test Case with API Client")]
//     [TestCase("<EMAIL>", 5)]
//     public async Task SendDefaultVerificationEmailTest(string email, int sendCount = 1)
//     {
//         var user = await _userManager.FindByEmailAsync(email);
//         user.EmailConfirmed = false;
//         await _userManager.UpdateAsync(user);
//         for (int i = 0; i < sendCount; i++)
//         {
//             var verifyString = await _userManager.GenerateEmailConfirmationTokenAsync(user);
//             await TestContext.Progress.WriteLineAsync(
//                 $"Api Client result:\n{JsonConvert.SerializeObject(verifyString)}");
//
//             // await _emailNotificationService.SendAuth0EmailVerification(user, verifyUrl);
//             var jobStatus = JsonConvert.DeserializeObject<Job>(verifyString);
//
//             // Assert.That(verifyString, Is.Not.Null);
//             // Assert.That(jobStatus.Status, Is.EqualTo("pending"));
//         }
//     }
//
//     [Test]
//     [Category("Test Case with API Client")]
//     [TestCase(
//         false,
//         "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJkb21haW4iOiJwb3dlcmZsb3ctdGVzdC5qcC5hdXRoMC5jb20iLCJjbGllbnRfaWQiOiJabXl2Q3M2R0ZKRUhLTFNzcFRNblNLVW9XcmNUSlhRVCIsIm5iZiI6MTY2NzgwNzUyMCwiZXhwIjoxNjY4NDEyMzE1LCJpYXQiOjE2Njc4MDc1MjAsImlzcyI6InNsZWVrZmxvdyIsImF1ZCI6InNsZWVrZmxvdyJ9.Q5Dj0nOI-hu_8tbkz1A367qlDf4OdS7UYk2t53NQ3Rg")]
//     [TestCase(
//         false,
//         "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3N1ZXIiOiJzbGVla2Zsb3cuaW8iLCJhdWRpZW5jZSI6InNsZWVrZmxvdy5pbyIsImRvbWFpbiI6InNsZWVrZmxvdy1kZXYuZXUuYXV0aDAuY29tIiwiY2xpZW50X2lkIjoiUG8ycDBrOGZwZ3ZpeW9YcTQ4ZGw1TzRIRjJURWtyd1YifQ.su_pVstcRnnoHKagSNui-fCIHlU2_j71tLQ2HBUSuig")]
//     [TestCase(
//         false,
//         "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJkb21haW4iOiJzbGVla2Zsb3ctZGV2LmV1LmF1dGgwLmNvbSIsImNsaWVudF9pZCI6IktTZlZIc1VMRUo0b3kxTWlkMWw4Z3g0WUw3MFBkS1ZkIiwibmJmIjoxNjgwMTQ3NTk3LCJleHAiOjE2ODA3NTIzOTcsImlhdCI6MTY4MDE0NzU5NywiaXNzIjoic2xlZWtmbG93IiwiYXVkIjoic2xlZWtmbG93In0.J7zMkhaqk7r2ascQsNiBm7onQYLIXw_nLifuuprOJXE")]
//     [TestCase(
//         false,
//         "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzbGVla2Zsb3cuaW8iLCJhdWQiOiJzbGVla2Zsb3cuaW8iLCJkb21haW4iOiJzbGVla2Zsb3ctZGV2LmV1LmF1dGgwLmNvbSIsImNsaWVudF9pZCI6IlBvMnAwazhmcGd2aXlvWHE0OGRsNU80SEYyVEVrcndWIn0.sQGdoDzlNMdh92sYRaBjqpmmJ_1Du3NXxpAdfImRuns")]
//     [TestCase(
//         false,
//         "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzbGVla2Zsb3cuaW8iLCJhdWQiOiJodHRwczovL3NsZWVrZmxvdy1kZXYuZXUuYXV0aDAuY29tL2FwaS92Mi8iLCJkb21haW4iOiJzbGVla2Zsb3ctZGV2LmV1LmF1dGgwLmNvbSIsImNsaWVudF9pZCI6IlBvMnAwazhmcGd2aXlvWHE0OGRsNU80SEYyVEVrcndWIn0.lJlZ71PAGLwrhr6M9Gom6tp9LUI4vYKtTWcbeMII3Ko")]
//     [TestCase(
//         false,
//         "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJodHRwczovL2FwcC5zbGVla2Zsb3cuaW8vIiwiYXVkIjoiaHR0cHM6Ly9zbGVla2Zsb3ctZGV2LmV1LmF1dGgwLmNvbS9hcGkvdjIvIiwiZG9tYWluIjoic2xlZWtmbG93LWRldi5ldS5hdXRoMC5jb20iLCJjbGllbnRfaWQiOiJQbzJwMGs4ZnBndml5b1hxNDhkbDVPNEhGMlRFa3J3ViJ9.mkgPW4g6SOSMQU8oixV_lGt4phHLWkitgXC5TTnbBj4")]
//     public async Task Auth0EventTokenValidateTest(bool isValidToken, string inputJwt)
//     {
//         try
//         {
//             // Arrange
//             // await InitApiClient();
//             // var secretKey = _auth0Config.Audience + "+wsadz4gI_3DUXI8P";
//             // var secretKey = _conf["Auth0:ActionIssuer"] + _conf["Auth0:ActionAudience"] + "+wsadz4gI_3DUXI8P";
//             var testToken = FakeDataGenerator.CreateAuth0EventToken(
//                 _conf,
//                 SecretKey);
//
//             // Act
//             // Use jwtHandler.ReadJwtToken(inputJwt) to read the jwt content.
//             var jwtHandler = new JwtSecurityTokenHandler();
//             var tokenValidate = new TokenValidationParameters()
//             {
//                 ValidateLifetime = false, // Because there is no expiration in the generated token
//                 ValidateAudience = true, // Because there is no audiance in the generated token
//                 ValidateIssuer = true, // Because there is no issuer in the generated token
//                 ValidIssuer = _conf["Auth0:ActionIssuer"],
//                 ValidAudience = _conf["Auth0:ActionAudience"],
//                 RequireExpirationTime = true,
//                 RequireSignedTokens = true,
//                 IssuerSigningKey =
//                     new SymmetricSecurityKey(
//                         Encoding.UTF8.GetBytes(SecretKey)) // The same key as the one that generate the token
//             };
//             var createPrincipal = await jwtHandler.ValidateTokenAsync(testToken, tokenValidate);
//             // var remotePrincipal = await jwtHandler.ValidateTokenAsync(inputJwt, tokenValidate);
//
//             // Event controller test.
//             // var controllerValidateRes = await _auth0Event.ValidateToken(inputJwt);
//             var controllerValidateRes = await _auth0Event.ValidateToken(testToken);
//
//             // Assert
//             Assert.That(createPrincipal.IsValid, "createPrincipal.IsValid");
//             // Assert.That(isValidToken == remotePrincipal.IsValid, "isValidToken(Your input) == remotePrincipal.IsValid");
//             // Assert.That(isValidToken == controllerValidateRes, "isValidToken(Your input) == controllerValidate");
//             Assert.That(controllerValidateRes, Is.True, "isValidToken(Your input) == controllerValidate");
//         }
//         catch (Exception err)
//         {
//             await TestContext.Progress.WriteAsync($"Error: {err.Message}, \n{err.StackTrace}");
//             Assert.Fail();
//         }
//     }
//
//     /// <summary>
//     /// This Test is testing for OnSignUpEvent action flow on auth0.
//     /// And it must set the on_user_signed_up_webhook correctly in pulumi or in the auto link script.
//     /// </summary>
//     [Test]
//     [Category("Test Case with User Manager")]
//     public async Task Auth0ActionFlowsEventTest()
//     {
//         // Arrange
//         var throwError = false;
//         var secretKey = _conf["Auth0:ActionIssuer"] + _conf["Auth0:ActionAudience"] + "+wsadz4gI_3DUXI8P";
//         var eventToken = FakeDataGenerator.CreateAuth0EventToken(_conf, secretKey);
//         var genUser = FakeDataGenerator.GenUsers()[0];
//         var genUserEmail = genUser.Email;
//         var createRequest = SleekflowUserManager.ToCreateRequest(genUser);
//         createRequest.Password = SleekflowUserManager.GenerateRandomString(20);
//         createRequest.Connection = _auth0Config.DatabaseConnectionName;
//
//         var auth0User = await _managementApiClient.Users.CreateAsync(createRequest);
//         var auth0PwdEventUserTest = JsonConvert.DeserializeObject<User>(
//             @"{""user"":{""id"":""abc123"",""username"":""user1"",""email"":""<EMAIL>"",""last_password_reset"":""2019-02-27T14:14:29.206Z""}}");
//         var auth0PwdEventUser = FakeDataGenerator.ToEventRequest(genUser);
//
//         await TestContext.Progress.WriteLineAsync($"Created User on Auth0 {auth0User.Email}");
//         await TestContext.Progress.WriteLineAsync($"Created Token {eventToken}");
//
//         try
//         {
//             // Act
//             // Post-Login event
//             var validateTest = await _auth0Event.ValidateToken(eventToken);
//             Assert.That(validateTest, Is.True, "eventToken.IsValid");
//
//             var res =
//                 await _auth0Event.PostUserLogin("auth0", eventToken, auth0User);
//
//             var okRes = res.Result as OkObjectResult;
//             var findCreatedUser = await _userManager.Users.FirstOrDefaultAsync(
//                 u => u.Email == genUserEmail);
//             if (findCreatedUser is not null)
//             {
//                 genUser.Id = findCreatedUser.Id;
//             }
//
//             var auth0CreatedUser = await _managementApiClient.Users.GetAsync(
//                 (okRes?.Value as Auth0ActionEventController.PostUserLoginResponse)?.Auth0UserId);
//
//             await TestContext.Progress.WriteLineAsync($"Find User in DB result: {findCreatedUser?.Email}");
//             await TestContext.Progress.WriteLineAsync($"Find User in Auth0 result: {auth0CreatedUser?.Email}");
//
//             // Post-Password-Change event
//             var pwdRes = await _auth0Event.PostUserChangePassword(eventToken, auth0PwdEventUser);
//             var metaDataRes =
//                 (Auth0AppMetadata) JsonConvert.DeserializeObject<Auth0AppMetadata>(
//                     auth0CreatedUser.AppMetadata?.ToString());
//
//             // Assert
//             Assert.IsInstanceOf<User>(auth0PwdEventUserTest);
//             Assert.IsInstanceOf<OkObjectResult>(res.Result);
//             Assert.IsInstanceOf<OkObjectResult>(pwdRes);
//             Assert.IsNotNull(findCreatedUser);
//             Assert.That(findCreatedUser?.LastLoginAt, Is.EqualTo(DateTime.UtcNow).Within(1).Minutes);
//             Assert.IsAssignableFrom<Auth0ActionEventController.PostUserLoginResponse>(okRes.Value);
//             Assert.IsAssignableFrom<ApplicationUser>(findCreatedUser);
//             Assert.IsNull(auth0CreatedUser.UserMetadata);
//             Assert.IsNotEmpty(
//                 JsonConvert.DeserializeObject<Auth0AppMetadata>
//                     (auth0CreatedUser.AppMetadata?.ToString())?.SleekflowId);
//             Assert.That(findCreatedUser.Id, Is.EqualTo(metaDataRes.SleekflowId));
//         }
//         catch (Exception err)
//         {
//             await TestContext.Progress.WriteLineAsync($"Error occur: {err.Message}\n{err.StackTrace}");
//             throwError = true;
//         }
//
//         // Clean
//         // await InitApiClient(); // Reset the entity instance to avoid the duplicate tracked error.
//         await _userManager.DeleteAsync(genUser);
//         await TestContext.Progress.WriteLineAsync($"Sent delete user request. ");
//         Assert.IsFalse(throwError);
//     }
//
//     [Test]
//     [Category("Test Case with API Client")]
//     public async Task GetApiTokenTest()
//     {
//         var token = await _authenticaionApiClient.GetTokenAsync(
//             new ClientCredentialsTokenRequest()
//             {
//                 ClientId = _auth0Config.ClientId,
//                 ClientSecret = _auth0Config.ClientSecret,
//                 Audience = _auth0Config.Audience
//             });
//         Assert.That(token.AccessToken, Is.Not.EqualTo(null));
//     }
//
//     [Test]
//     [Category("Test Case with API Client")]
//     public async Task GetClientsTest()
//     {
//         // await InitApiClient();
//         var allClients = await _managementApiClient.Clients.GetAllAsync(new GetClientsRequest());
//
//         await TestContext.Progress.WriteLineAsync(allClients.ToJson());
//         Assert.That(allClients, Is.Not.EqualTo(null));
//     }
//
//     [Test]
//     [Category("Test Case with API Client")]
//     [TestCase("<EMAIL>", "P@ssw0rd!")]
//     [TestCase("<EMAIL>", "%L022b47", "https://sso-dev.sf.chat/")]
//     [TestCase("<EMAIL>", "P@ssw0rd!", "https://sso-dev.sf.chat/")]
//     [TestCase("<EMAIL>", "P@ssw0rd", "https://sso-dev.sf.chat/")]
//     public async Task LoginTest(string userEmail, string password, string? domain = null)
//     {
//         if (domain != null)
//             _authenticaionApiClient = new AuthenticationApiClient(new Uri(domain));
//         await TestContext.Progress.WriteLineAsync(
//             $"Logging in user {userEmail} to {_auth0Config.Audience}, client id: {_auth0Config.ClientId}");
//         var response = await LoginUser(userEmail, password);
//         await TestContext.Progress.WriteLineAsync($"{JsonConvert.SerializeObject(response, Formatting.Indented)}");
//
//         Assert.That(response.IdToken, Is.Not.Null);
//         Assert.That(response.AccessToken, Is.Not.Null);
//         Assert.That(response, Is.AssignableFrom<AccessTokenResponse>());
//     }
//
//     public ClaimsPrincipal TokenToUserLoginPrincipal(string token)
//     {
//         var jwtHandler = new JwtSecurityTokenHandler();
//         var jwtToken = jwtHandler.ReadJwtToken(token);
//         var identity = new Mock<ClaimsIdentity>();
//         identity.Setup(o => o.Claims).Returns(jwtToken.Claims);
//         identity.Setup(o => o.IsAuthenticated).Returns(true);
//
//         var principal = new Mock<ClaimsPrincipal>();
//         principal.Setup(o => o.Claims).Returns(identity.Object.Claims);
//         principal.Setup(o => o.Identity).Returns(identity.Object);
//         principal.Setup(o => o.FindFirst("sub"))
//             .Returns(identity.Object.Claims.FirstOrDefault(o => o.Type == "sub"));
//         principal.Setup(o => o.FindFirst("iss"))
//             .Returns(identity.Object.Claims.FirstOrDefault(o => o.Type == "iss"));
//
//         return principal.Object;
//     }
//
//     /*
//     [Test]
//     [Category("Test Case with User Manager")]
//     [TestCase("<EMAIL>", "P@ssw0rd!", "https://sso-dev.sf.chat/")]
//     [TestCase("<EMAIL>", "P@ssw0rd!")]
//     [TestCase("<EMAIL>", "%L022b47")]
//     [TestCase("<EMAIL>", "P@ssw0rd!")]
//     public async Task AuthorizationHandlerTest(string userEmail, string password, string? domain = null)
//     {
//         // await InitApiClient();
//
//         if (domain != null)
//             _authenticaionApiClient = new AuthenticationApiClient(new Uri(domain));
//
//         await TestContext.Progress.WriteLineAsync(
//             $"Logging in user {userEmail} to {_auth0Config.Audience}, client id: {_auth0Config.ClientId}");
//         var response = await LoginUser(userEmail, password);
//
//         await TestContext.Progress.WriteLineAsync($"{JsonConvert.SerializeObject(response, Formatting.Indented)}");
//
//         var principal = TokenToUserLoginPrincipal(response.AccessToken);
//         var claimTransformed = await _sleekflowClaimsTransformation.TransformAsync(principal);
//         var requirements = new[]
//         {
//             new DenyAnonymousAuthorizationRequirement()
//         };
//         //< Can do the authorization test like following:
//         //< var requirements = new[] { new RolesAuthorizationRequirement(new List<string>(){"InternalCmsUser"}) };
//         var context = new AuthorizationHandlerContext(requirements, claimTransformed, null);
//
//         // await _customAuthorizationHandler.HandleAsync(context);
//
//         Assert.That(context.HasFailed, Is.False);
//         Assert.That(response.IdToken, Is.Not.Null);
//         Assert.That(response.AccessToken, Is.Not.Null);
//         Assert.That(response, Is.AssignableFrom<AccessTokenResponse>());
//     }
//     */
//
//     private async Task<AccessTokenResponse> LoginUser(string userName, string password)
//     {
//         var response = await _authenticaionApiClient.GetTokenAsync(
//             new ResourceOwnerTokenRequest
//             {
//                 Audience = _auth0Config.Audience,
//                 ClientId = _auth0Config.ClientId,
//                 ClientSecret = _auth0Config.ClientSecret,
//                 Realm = "Sleekflow-Username-Password-Authentication",
//                 Password = password,
//                 Scope = "openid",
//                 Username = userName,
//             });
//         return response;
//     }
//
//     [Test]
//     [Category("Test Case with User Manager")]
//     [TestCase("a0dec50c-a151-4d6d-a7ca-a4e01700a41f")]
//     [TestCase("b13b6f48-82ba-4ea1-b9d8-63325a83415e")]
//     public async Task GetUserAuth0IdByIdTest(string id)
//     {
//         // await InitApiClient();
//         var user = await _userManager.FindByIdAsync(id);
//         await TestContext.Progress.WriteLineAsync(JsonConvert.SerializeObject(user));
//
//         Assert.IsAssignableFrom<ApplicationUser>(user);
//
//         // var auth0Id = await _userManager.GetUserIdAsync(user);
//         var auth0Id = await _userManager.GetAuth0UserIdAsync(user);
//
//         var authType = (auth0Id.Contains("auth0")) ? "auth0" : "google-oauth2";
//
//         Assert.That(auth0Id, Is.EqualTo($"{authType}|{id}"));
//     }
//
//     [Test]
//     [Category("Test Case with API Client")]
//     [TestCase("auth0|a0dec50c-a151-4d6d-a7ca-a4e01700a41f")] // = <EMAIL>
//     [TestCase("auth0|dcd8ebcc-6a04-4a77-b567-fcffe4a41ac9")] // = Sleekflow CSM
//     [TestCase("auth0|58752a81-e31c-4dd3-ae2f-d02f4cc205c2")] // = Peter
//     [TestCase("google-oauth2|113159227082341261672")]
//     public async Task Auth0GetUserTest(string userId)
//     {
//         // await InitApiClient();
//         var user = await _managementApiClient.Users.GetAsync(userId);
//
//         await TestContext.Progress.WriteLineAsync($"Found User\n{JsonConvert.SerializeObject(user)}");
//         Assert.IsAssignableFrom<User>(user);
//     }
//
//     [Test]
//     [Category("Test Case with User Manager")]
//     [TestCase("<EMAIL>")]
//     [TestCase("<EMAIL>")]
//     [TestCase("<EMAIL>")]
//     public void ValidEmailTrueTest(string email)
//     {
//         var isEmail = SleekflowUserManager.IsValidEmail(email);
//         Assert.True(isEmail);
//     }
//
//     [Test]
//     [Category("Test Case with User Manager")]
//     [TestCase("billy.kahong.chan")]
//     [TestCase("billy-chan")]
//     public void ValidEmailFalseTest(string email)
//     {
//         var isEmail = SleekflowUserManager.IsValidEmail(email);
//         Assert.False(isEmail);
//     }
//
//     [Test]
//     [Category("Test Case with User Manager")]
//     [TestCase(nameof(ApplicationUserRole.InternalCmsAdmin))]
//     [TestCase(nameof(ApplicationUserRole.InternalCmsUser))]
//     [TestCase(nameof(ApplicationUserRole.ResellerPortalUser))]
//     [TestCase(nameof(ApplicationUserRole.InternalCmsSalesUser))]
//     [TestCase(nameof(ApplicationUserRole.InternalCmsSuperUser))]
//     [TestCase(nameof(ApplicationUserRole.InternalCmsCustomerSuccessUser))]
//     public async Task GetUsersByRoleTest(string role)
//     {
//         // Search users by role
//         var results = await _userManager.GetUsersInRoleAsync(role);
//         await TestContext.Progress.WriteLineAsync(
//             $"Users in Role {role}:\n{JsonConvert.SerializeObject(results, Formatting.Indented)}");
//
//         Assert.IsAssignableFrom<List<ApplicationUser>>(results);
//         await Task.Delay(300);
//     }
//
//     [Test]
//     [Category("Test Case with User Manager")]
//     [TestCase("<EMAIL>")]
//     [TestCase("<EMAIL>")]
//     [TestCase("<EMAIL>")]
//     public async Task GetUserRolesTest(string email)
//     {
//         var user = await _userManager.FindByEmailAsync(email);
//
//         Assert.True(user != null);
//
//         if (user != null)
//         {
//             var result = await _userManager.GetRolesAsync(user);
//             await TestContext.Progress.WriteLineAsync(
//                 $"User {user.Email} return Roles: \n {JsonConvert.SerializeObject(result)}");
//             Assert.IsAssignableFrom<List<string>>(result);
//         }
//     }
//
//     [Test]
//     [Category("Test Case with User Manager")]
//     [TestCase("<EMAIL>")]
//     [TestCase("<EMAIL>")]
//     public async Task GetUserByEmailTest(string email)
//     {
//         var result = await _userManager.FindByEmailAsync(email);
//
//         await TestContext.Progress.WriteLineAsync(
//             $"Found user {email}:\n{JsonConvert.SerializeObject(result, Formatting.Indented)}");
//
//         Assert.That(result.Email, Is.EqualTo(email));
//         Assert.IsAssignableFrom<ApplicationUser>(result);
//     }
//
//     [Test]
//     [Category("Test Case with User Manager")]
//     [TestCase("asdfasda")]
//     [TestCase("asdasde2")]
//     [TestCase("siuhleung7-c")]
//     [TestCase("bugfix")]
//     [TestCase("<EMAIL>")]
//     public async Task GetUserByNameTest(string userName)
//     {
//         // Search by user name
//         var result = await _userManager.FindByNameAsync(userName);
//         await TestContext.Progress.WriteLineAsync(JsonConvert.SerializeObject(result));
//
//         Assert.That(result.UserName, Is.EqualTo(userName));
//         Assert.IsAssignableFrom<ApplicationUser>(result);
//     }
//
//     [Test]
//     [Category("Test Case with API Client")]
//     [TestCase("asdasde2")]
//     [TestCase("lewislaw")]
//     [TestCase("bugfix")]
//     [TestCase("siuhleung7-c")]
//     [TestCase("claud_trantow")]
//     [TestCase("vwong")]
//     public async Task GetUserByUserNameTest(string userName)
//     {
//         // Search by user name
//         var request = new GetUsersRequest()
//         {
//             Query = $"username:{userName}"
//         };
//         var result = await _managementApiClient.Users.GetAllAsync(request);
//         await TestContext.Progress.WriteLineAsync(JsonConvert.SerializeObject(result));
//
//         Assert.AreEqual(userName, result[0].UserName);
//         Assert.IsAssignableFrom<PagedList<User>>(result);
//     }
//
//     [Test]
//     [Category("Test Case with User Manager")]
//     [TestCase("<EMAIL>", "P@ssw0rd!")]
//     [TestCase("<EMAIL>", "P@ssw0rd!")]
//     public async Task ChangeUserPasswordTest(string email, string password)
//     {
//         // Arrange
//         await OnInit();
//         var user = await _userManager.FindByEmailAsync(email);
//
//         // Act
//         if (true)
//         {
//             var token = await _userManager.GeneratePasswordResetTokenAsync(user);
//
//             await Task.Delay(500);
//             var res = await _userManager.ResetPasswordAsync(user, token, password);
//             Console.WriteLine(res);
//             Console.WriteLine(res.Succeeded);
//
//             await Task.Delay(500);
//             var res2 = await _userManager.ResetPasswordAsync(user, password);
//
//             // Assert
//             Assert.IsTrue(res.Succeeded);
//             Assert.That(res2, Is.AssignableFrom<IdentityResult>());
//             await TestContext.Progress.WriteLineAsync("Password change completed");
//         }
//     }
//
//     [Test]
//     [Category("Test Case with User Manager")]
//     [TestCase("<EMAIL>", "P@ssw0rd!")]
//     public async Task ChangeUserPasswordErrorTest(string email, string password)
//     {
//         // Arrange
//         var user = await _userManager.FindByEmailAsync(email);
//
//         // Act
//         if (true)
//         {
//             var token = await _userManager.GeneratePasswordResetTokenAsync(user);
//
//             await Task.Delay(500);
//             Assert.That(
//                 async () => await _userManager.ResetPasswordAsync(user, password),
//                 Throws.Nothing);
//
//             await TestContext.Progress.WriteLineAsync("Password change Fail");
//         }
//     }
//
//     [Test]
//     [TestCase("<EMAIL>", "12345")]
//     public async Task WeakUserPasswordTest(string email, string password)
//     {
//         // Arrange
//         var user = await _userManager.FindByEmailAsync(email);
//
//         // Act & Assert
//         if (true)
//         {
//             try
//             {
//                 var result = await _userManager.ResetPasswordAsync(user, password);
//                 await TestContext.Progress.WriteLineAsync(
//                     $"{JsonConvert.SerializeObject(result.Errors, Formatting.Indented)}");
//
//                 Assert.That(result, Is.TypeOf<IdentityResult>());
//                 Assert.That(result.Succeeded, Is.False);
//                 Assert.That(result.Errors.Count(), Is.GreaterThan(0));
//             }
//             catch (Exception err)
//             {
//                 Assert.That(true);
//             }
//         }
//     }
//
//     [Test]
//     [Category("Test Case with User Manager")]
//     public async Task UsersCRUDTest()
//     {
//         await Task.Delay(800);
//         await OnInit();
//
//         // Create User
//         var users = FakeDataGenerator.GenUsers(_testDataCreateCount);
//         foreach (var user in users)
//         {
//             try
//             {
//                 var response = await _userManager.CreateAsync(user, "Pa55w0rd!");
//                 Assert.That(response, Is.EqualTo(IdentityResult.Success));
//                 await Task.Delay(300);
//
//                 // Make sure user has write into db.
//                 var dbGetUser = _userManager.Users.AsNoTracking().FirstOrDefault(u => u.Id == user.Id);
//                 Assert.That(dbGetUser?.Id, Is.EqualTo(user.Id));
//
//                 if (dbGetUser?.Id != null)
//                 {
//                     var mapUser = await _userManager.FindByIdAsync(dbGetUser.Id);
//                     Assert.That(mapUser.PhoneNumber, Is.EqualTo(user.PhoneNumber));
//                 }
//             }
//             catch (Exception e)
//             {
//                 await TestContext.Progress.WriteLineAsync(
//                     $"Insert user error: \n{e.Message}\n{JsonConvert.SerializeObject(user, Formatting.Indented)}\n");
//             }
//         }
//
//         await TestContext.Progress.WriteLineAsync($"{_testDataCreateCount} Users created.");
//         await TestContext.Progress.WriteLineAsync("Update users test ..");
//
//         // We may need to run the initialization code to avoid the currency failed.
//         await OnInit();
//         await Task.Delay(1000);
//
//         foreach (var item in users)
//         {
//             try
//             {
//                 var updateUser = await _userManager.FindByEmailAsync(item.Email);
//
//                 Assert.IsNotNull(updateUser);
//                 Assert.IsAssignableFrom<ApplicationUser>(updateUser);
//
//                 updateUser.FirstName = "Update test.";
//                 var upRes = await _userManager.UpdateAsync(updateUser);
//
//                 Assert.True(upRes.Succeeded, $"Update user failed.");
//                 Assert.That(upRes.Errors.Count(), Is.EqualTo(0));
//
//                 await Task.Delay(800);
//             }
//             catch (Exception e)
//             {
//                 await TestContext.Progress.WriteLineAsync(
//                     $"Update user error: \n{e.Message}\n{JsonConvert.SerializeObject(item, Formatting.Indented)}\n");
//             }
//         }
//
//         // Clean User after test complete.
//         await TestContext.Progress.WriteLineAsync("Cleaning Users ..");
//         await Task.Delay(800);
//
//         foreach (var user in users)
//         {
//             try
//             {
//                 await Task.Delay(800);
//                 await TestContext.Progress.WriteLineAsync($"Deleting user {user.DisplayName} ({user.Email})");
//
//                 var response = await _userManager.DeleteAsync(user);
//                 var auth0Id = await _userManager.GetAuth0UserIdAsync(user);
//                 await _managementApiClient.Users.DeleteAsync(auth0Id);
//                 await TestContext.Progress.WriteLineAsync($"Delete request sent.");
//                 Assert.That(response, Is.EqualTo(IdentityResult.Success));
//             }
//             catch (Exception er)
//             {
//                 await TestContext.Progress.WriteLineAsync($"{er.Message}\n{er.InnerException}");
//             }
//         }
//
//         await TestContext.Progress.WriteLineAsync("Users Cleaned.");
//     }
//
//     [Test]
//     [Category("Test Case with User Manager")]
//     public async Task GetRolesFromAuth0Test()
//     {
//         var result = await _userManager.GetAllRolesAsync();
//         await TestContext.Progress.WriteLineAsync($"{JsonConvert.SerializeObject(result)}");
//
//         Assert.IsAssignableFrom<List<Role>>(result);
//     }
//
//     [Test]
//     [Category("Test Case with User Manager")]
//     [TestCase(
//         "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6ImpHTFB3bXJwZmp1Ukg0RlRva1BJVCJ9.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************")]
//     [TestCase(
//         "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6ImpHTFB3bXJwZmp1Ukg0RlRva1BJVCJ9.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************_********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.JAsQyN8ovkcI4r-tKGRb0L1lO2uoUCJ3wStI-Se6CNDDkBgMOvqlRvXmCExzeNZ2eEX9Q6gHgqSCdmyITMRACS5OUdch2BEZtZoFhRzgew_VFlNz9bOzOlxPgxk9_yCOtuitq8HkTpPuEKvazIoANVuOpW95C1smR6jTh9t96vQxjajauB6HsXUbaMjlvhOt-W02qAm4Fl6xZHzE7jnzb7vE6nhXx1B5RpB2H6Ol5Pco_2yUyfpCi9IyqWh0Kfl1307Op_d0Q70n7Ydx-oJ7tsTuB9xPNmncJZJ-hHArFzEuXDrzXHXMmTyaBRh-uf0faMBZBtv63cr-pIOW4VyLSQ")]
//     public async Task JwtInformationTest(string inputJwt)
//     {
//         var jwtHandler = new JwtSecurityTokenHandler();
//         var jwtToken = jwtHandler.ReadJwtToken(inputJwt);
//         var identity = new ClaimsPrincipal(new ClaimsIdentity(jwtToken.Claims));
//
//         var id = identity.FindFirstValue(ClaimTypes.NameIdentifier);
//         var transformedClaims = await _sleekflowClaimsTransformation.TransformAsync(identity);
//
//         await TestContext.Progress.WriteLineAsync($"Read jwt token id: {transformedClaims.Claims}");
//
//         Assert.IsNotNull(transformedClaims.FindFirstValue(_options.ClaimsIdentity.UserIdClaimType));
//         Assert.IsNotNull(transformedClaims.FindFirstValue(_options.ClaimsIdentity.UserIdClaimType));
//         Assert.That(transformedClaims.FindFirstValue(_options.ClaimsIdentity.RoleClaimType), Is.Not.EqualTo(""));
//         Assert.IsNotNull(transformedClaims.FindFirstValue(_options.ClaimsIdentity.UserNameClaimType));
//         Assert.IsNotNull(transformedClaims.FindFirstValue(_options.ClaimsIdentity.EmailClaimType));
//     }
//
//     [Test]
//     [Category("Test Case with User Manager")]
//     [TestCase(
//         "63886c0ca53accfc3d457a58",
//         "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6ImpHTFB3bXJwZmp1Ukg0RlRva1BJVCJ9.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.H_5vE-bNlLQX66KAmVHYUqqH7eoYOcik38RHnhgROBbAloN01vNdRZUsPLL83Bkybqg7UAhtwXViw9gIQxOhuDCmcxuwSsEeAaKtlfxqdhr9AdryPE0hKrUDPnap5ax0iFLEzi7NcGIJU3zS1y04VwAiCkOjFIfMCeLlxtjCskhFFO9tYvULCGFFdsOv-2Qbc5BwklUsNfRUbRN-elBwfJbztqsZicc5PMy6xGQOMm5W1B-W-zg6Y6VkC2PXyHZ2XvEXsZZ1ST3FwPh9brtD_fvncAUL7t1SOo94vtoORDQ1vPiK4em0Ogl25r2sW_mTMYoJ4A9IPQVzat0JEZQxxw")]
//     [TestCase(
//         "4ff63df1-eeb5-4fcc-96cf-a76ff28289b0",
//         "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6ImpHTFB3bXJwZmp1Ukg0RlRva1BJVCJ9.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.jdOK0wnII7J67ZBZ2_PjCyXyLT0pP8S7-Mq6sBnyYuTqR-2pNrB6vrQlMl_rLwlAJgJ12lu7oEmsLFQ-VVm5OQE65yUV8c1M2k7yOYpojL0ptUr3cW8FkHPIfR-DTwBEii_1ij4XfUcEXKXuKzgLad7_z3Uxd2D9dt-9bzLJ6U0VeX_il3XAKxsj6xITKlLA_Q9-SUjC2DASzhtCfCJpVlOMqMKIof3tEgdmJU0it6dpB5COZn19ZWcTEI5C_0A0uWrVgpwv6qK4SI5BMA0jNwJ76nm1DzbhNz9ckMkvWf7h-BUItIPKnz7zOLkSRjkx5rVbkxTrVFnWlkRfoTha4g")]
//     public async Task JwtEmailtoUserIdTest(string userId, string inputJwt)
//     {
//         var jwtHandler = new JwtSecurityTokenHandler();
//         var jwtToken = jwtHandler.ReadJwtToken(inputJwt);
//         var identity = new ClaimsPrincipal(new ClaimsIdentity(jwtToken.Claims));
//
//         var transformedClaims = await _sleekflowClaimsTransformation.TransformAsync(identity);
//
//         var testuser = await _userManager.FindByIdAsync(userId);
//         await TestContext.Progress.WriteLineAsync($"Read jwt token id: {transformedClaims.Claims}");
//         await TestContext.Progress.WriteLineAsync(
//             $"Jwt token content: {JsonConvert.SerializeObject(jwtToken.Claims, Formatting.Indented)}");
//
//         Assert.That(transformedClaims.FindFirstValue(_options.ClaimsIdentity.UserIdClaimType), Is.EqualTo(userId));
//         Assert.That(
//             transformedClaims.FindFirstValue(_options.ClaimsIdentity.UserNameClaimType),
//             Is.EqualTo(testuser.UserName));
//
//         Assert.IsNotNull(transformedClaims.FindFirstValue(_options.ClaimsIdentity.UserIdClaimType));
//         Assert.That(transformedClaims.FindFirstValue(_options.ClaimsIdentity.RoleClaimType), Is.Not.EqualTo(""));
//         Assert.IsNotNull(transformedClaims.FindFirstValue(_options.ClaimsIdentity.UserNameClaimType));
//         Assert.IsNotNull(transformedClaims.FindFirstValue(_options.ClaimsIdentity.EmailClaimType));
//     }
//
//     [Test]
//     [Category("Test Case with User Manager")]
//     [TestCase(
//         "<EMAIL>",
//         "******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************")]
//     [TestCase(
//         "<EMAIL>",
//         "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6ImpHTFB3bXJwZmp1Ukg0RlRva1BJVCJ9.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************")]
//     [TestCase(
//         "<EMAIL>",
//         "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6ImpHTFB3bXJwZmp1Ukg0RlRva1BJVCJ9.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.YUwunF7g2ObvldCmF93NgwEMH-lldx8TJDjzVErt7MKuA-MayXnfLD4wdFQuyhU9zvqLhqAUVW5er9WngUGHmzFM4TdKHfKYfW-p808xX4HqDEWT3mCMPFUJ-GDh_4VyOFo5OGalLOFqwHln5hFMQMvL7Lfyc9JlVODk7j2mQ1-77hAAsbc2Xrpk5XynunnvUoH7QQqsh26h-_y0_9ZjlFAYRDlrYIE2OovmYpyFewwLHk5qBIHj1BEzMh4kbpi_lT8pk-nIKm2teCm_zuOHB0Qbxpc80tZU49l-x4JrIEUh-Tr898OjvPved0OwGGkBxeRwLObB0DmrDr0s0M08eQ")]
//     [TestCase(
//         "<EMAIL>",
//         "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6ImpHTFB3bXJwZmp1Ukg0RlRva1BJVCJ9.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************")]
//     [TestCase(
//         "<EMAIL>",
//         "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6ImpHTFB3bXJwZmp1Ukg0RlRva1BJVCJ9.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Ot3WglN1m_PIi0SFcuz_4cQWeI79J5NTmt9UqAzRpSZj1CghbJiAkNkUTZjMc0YKKmvvzzYUaq8p_OzM17RYsQxvclzJsZ1n601zG7JO2Razf2d3fPYX-11BViUul-9uY-hJLriMz7pPhLUrJgvm00DByMGQ9KkKd23oq73-BwNTdymW-VUHcy5Tkjd0wLitGUhIA5bAtqQ608ACIuylztkcbrAfIdo-Zhbi5ln6MINmUwjrzn2m44Y9-DdDronK965z762FX4KHfrPrnGOWPGuRaa_x9KwULq6xx3rs4OWjTdoilo79U9CNrHvOkd_3Yoybp5Yugy_GCB0_fD40NA")]
//     [TestCase(
//         "<EMAIL>",
//         "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6ImpHTFB3bXJwZmp1Ukg0RlRva1BJVCJ9.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.NyLqWSJNG4tQxvUrTv37j8ijeh_KrznUVNr30cQs8iQbLQ4Zwd1OxfFLH64a96VP6Wyqem9YbyVKqo8s8bQ0z8yBR6q42xVkAB7xH5XehvBKOfXoElRiAIvR3Pf0DooiZsDBXdXIGAIFZQEWSA1t_FAwJ1tlcft_6gLULKpA3FFA44YpmCyImWXxucW0o2Ff7B_S57ljlQHQ9wJOtEsg_Rig_gQFTbdlc5O_2HeP0f2Gh-sQlYnQ3cV5oqLBDTF756KrFEkrd58ByNaPBi1eqF2oB4d9Fsyqx5ke2ycJofzW_Nr_4b2M6A-RG3W3W_KKP4ZN7AKWfLzenU2wa8M7EA")]
//     public async Task JwtMultiIdentityTest(string email, string inputJwt)
//     {
//         var testUser = await _userManager.FindByEmailAsync(email);
//
//         var jwtHandler = new JwtSecurityTokenHandler();
//         var jwtToken = jwtHandler.ReadJwtToken(inputJwt);
//         var identity = new ClaimsPrincipal(new ClaimsIdentity(jwtToken.Claims));
//
//         var transformedClaims = await _sleekflowClaimsTransformation.TransformAsync(identity);
//
//         await TestContext.Progress.WriteLineAsync(
//             $"Read jwt token id: {transformedClaims.FindFirstValue(_options.ClaimsIdentity.UserIdClaimType)}");
//
//
//         Assert.That(
//             transformedClaims.FindFirstValue(_options.ClaimsIdentity.EmailClaimType),
//             Is.EqualTo(testUser.Email));
//         Assert.IsNotNull(transformedClaims.FindFirstValue(_options.ClaimsIdentity.UserIdClaimType));
//         Assert.That(transformedClaims.FindFirstValue(_options.ClaimsIdentity.RoleClaimType), Is.Not.EqualTo(""));
//         Assert.IsNotNull(transformedClaims.FindFirstValue(_options.ClaimsIdentity.UserNameClaimType));
//         Assert.IsNotNull(transformedClaims.FindFirstValue(_options.ClaimsIdentity.EmailClaimType));
//
//         Assert.That(
//             transformedClaims.FindFirstValue(_options.ClaimsIdentity.UserNameClaimType),
//             Is.EqualTo(testUser.UserName).Or.EqualTo(testUser.Email));
//         Assert.That(
//             transformedClaims.FindFirstValue(_options.ClaimsIdentity.EmailClaimType),
//             Is.EqualTo(testUser.Email));
//     }
//
//     [Test]
//     [Category("Test Case with User Manager")]
//     [TestCase(
//         "Auth0",
//         "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6ImpHTFB3bXJwZmp1Ukg0RlRva1BJVCJ9.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.H_5vE-bNlLQX66KAmVHYUqqH7eoYOcik38RHnhgROBbAloN01vNdRZUsPLL83Bkybqg7UAhtwXViw9gIQxOhuDCmcxuwSsEeAaKtlfxqdhr9AdryPE0hKrUDPnap5ax0iFLEzi7NcGIJU3zS1y04VwAiCkOjFIfMCeLlxtjCskhFFO9tYvULCGFFdsOv-2Qbc5BwklUsNfRUbRN-elBwfJbztqsZicc5PMy6xGQOMm5W1B-W-zg6Y6VkC2PXyHZ2XvEXsZZ1ST3FwPh9brtD_fvncAUL7t1SOo94vtoORDQ1vPiK4em0Ogl25r2sW_mTMYoJ4A9IPQVzat0JEZQxxw")]
//     [TestCase(
//         "Social",
//         "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6ImpHTFB3bXJwZmp1Ukg0RlRva1BJVCJ9.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************")]
//     [TestCase(
//         "Auth0",
//         "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6ImpHTFB3bXJwZmp1Ukg0RlRva1BJVCJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************")]
//     [TestCase(
//         "Auth0",
//         "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6ImpHTFB3bXJwZmp1Ukg0RlRva1BJVCJ9.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************")]
//     [TestCase(
//         "Auth0",
//         "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6ImpHTFB3bXJwZmp1Ukg0RlRva1BJVCJ9.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************_********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.JAsQyN8ovkcI4r-tKGRb0L1lO2uoUCJ3wStI-Se6CNDDkBgMOvqlRvXmCExzeNZ2eEX9Q6gHgqSCdmyITMRACS5OUdch2BEZtZoFhRzgew_VFlNz9bOzOlxPgxk9_yCOtuitq8HkTpPuEKvazIoANVuOpW95C1smR6jTh9t96vQxjajauB6HsXUbaMjlvhOt-W02qAm4Fl6xZHzE7jnzb7vE6nhXx1B5RpB2H6Ol5Pco_2yUyfpCi9IyqWh0Kfl1307Op_d0Q70n7Ydx-oJ7tsTuB9xPNmncJZJ-hHArFzEuXDrzXHXMmTyaBRh-uf0faMBZBtv63cr-pIOW4VyLSQ")]
//     [TestCase(
//         "Auth0",
//         "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6ImpHTFB3bXJwZmp1Ukg0RlRva1BJVCJ9.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************")]
//     public async Task JwtSocailUserTest(string Auth, string inputJwt)
//     {
//         var jwtHandler = new JwtSecurityTokenHandler();
//         var jwtToken = jwtHandler.ReadJwtToken(inputJwt);
//         var identity = new ClaimsPrincipal(new ClaimsIdentity(jwtToken.Claims));
//         var transformedClaims = await _sleekflowClaimsTransformation.TransformAsync(identity);
//
//         await TestContext.Progress.WriteLineAsync($"Read jwt token id: {transformedClaims.Claims}");
//
//
//         var userType = jwtToken.Subject.Contains("auth0") ? "Auth0" : "Social";
//
//         Console.WriteLine(userType);
//         Assert.That(userType, Is.EqualTo(Auth));
//     }
//
//     [Test]
//     [Category("Test Case with User Manager")]
//     public async Task AutoAddNewUserTest()
//     {
//         var hasThrowError = false;
//         var fakeUser = FakeDataGenerator.GenUsers(1).FirstOrDefault();
//         var sleekflowUserManager = (SleekflowUserManager) _userManager;
//         var createRequest = SleekflowUserManager.ToCreateRequest(fakeUser);
//         ApplicationUser removeUser = null;
//         createRequest.FullName = null;
//         createRequest.FirstName = null;
//         createRequest.LastName = null;
//         try
//         {
//             createRequest.Password = SleekflowUserManager.GenerateRandomString(32);
//             createRequest.Connection = _auth0Config.DatabaseConnectionName;
//
//             var createResponse = await _managementApiClient.Users.CreateAsync(createRequest);
//             var createUser = SleekflowUserManager.ToApplicationUser(createResponse);
//             await TestContext.Progress.WriteLineAsync(
//                 $"Auto create user:\n{JsonConvert.SerializeObject(createUser)}");
//             var testToken = FakeDataGenerator.CreateAccessToken(createUser, _options);
//             var principal = FakeDataGenerator.TokenToClaimsPrincipal(testToken);
//             var context = new Mock<HttpContext>();
//             context.Setup(c => c.User).Returns(principal);
//             sleekflowUserManager.HttpContextAccessor.HttpContext = context.Object;
//
//             var (result, applicationUser) =
//                 await sleekflowUserManager.CreateNewDbUserAndAssociateWithAuth0UserOnAuth0(createResponse); //test user
//             if (result == null)
//                 removeUser = await _userManager.FindByEmailAsync(createUser.Email);
//             else
//                 removeUser = createUser;
//
//             await Task.Delay(300);
//             var testUser = await _userManager.Users.AsNoTracking()
//                 .FirstOrDefaultAsync(u => u.Email == fakeUser.Email);
//
//             Assert.That(createResponse, Is.AssignableFrom<User>());
//             // Assert.That(removeUser.Id, Is.EqualTo(testUser.Id));
//             Assert.That(removeUser.EmailConfirmed, Is.False);
//             Assert.That(removeUser, Is.AssignableFrom<ApplicationUser>());
//
//             await TestContext.Progress.WriteLineAsync($"Remove user {fakeUser.UserName}");
//             if (removeUser != null)
//             {
//                 await _managementApiClient.Users.DeleteAsync(createResponse.UserId);
//                 await sleekflowUserManager.DeleteAsync(removeUser);
//             }
//         }
//         catch (Exception err)
//         {
//             await TestContext.Progress.WriteLineAsync($"{err.Message}\n{err.InnerException}");
//         }
//     }
//
//     /*
//     [Test]
//     [Category("Test Case with User Manager")]
//     public async Task CreateUserWithNullEmailTest()
//     {
//         var fakeUser = FakeDataGenerator.GenUsers().First();
//         fakeUser.Email = null;
//
//         var createRequest = _mapper.Map<UserCreateRequest>(fakeUser);
//         createRequest.Password = SleekflowUserManager.GenerateRandomString(32);
//         createRequest.Connection = _auth0Config.DatabaseConnectionName;
//
//         var createResult = await _managementApiClient.Users.CreateAsync(createRequest);
//
//     } */
//
//     [Test]
//     [Category("Test Case with User Manager")]
//     public async Task AutoCreateNewUserInClaimsTransformationTest()
//     {
//         var fakeUser = FakeDataGenerator.GenUsers().First();
//
//         try
//         {
//             var createRequest = SleekflowUserManager.ToCreateRequest(fakeUser);
//             createRequest.Password = SleekflowUserManager.GenerateRandomString(32);
//             createRequest.Connection = _auth0Config.DatabaseConnectionName;
//
//             var createdResult = await _userManager.CreateAsync(fakeUser, createRequest.Password);
//             await Task.Delay(300);
//             await TestContext.Progress.WriteLineAsync(
//                 createdResult.Succeeded
//                     ? $"Created fake user {fakeUser?.DisplayName} (Id: {fakeUser?.Id})"
//                     : "Create user failed.");
//             await TestContext.Progress.WriteLineAsync($"Creating user claims principal ..");
//
//             var createdAuth0User = await _userManager.FindByEmailAsync(fakeUser.Email);
//             var testToken = FakeDataGenerator.CreateAccessToken(createdAuth0User, _options);
//             var principal = FakeDataGenerator.TokenToClaimsPrincipal(testToken);
//             await Task.Delay(300);
//
//             await _sleekflowClaimsTransformation.TransformAsync(principal);
//
//             await TestContext.Progress.WriteLineAsync($"Transformation called.");
//             await TestContext.Progress.WriteLineAsync($"Finding test user in database..");
//             var testResult = await _userManager.Users.AsNoTracking().FirstOrDefaultAsync(u => u.Id == fakeUser.Id);
//
//             Assert.That(createdResult.Succeeded, Is.True, "Cannot create user.");
//             Assert.That(createdAuth0User, Is.Not.Null, "User not created in Auth0.");
//             Assert.That(testResult, Is.Not.Null, "User not created in AspNetUsers table.");
//
//             await TestContext.Progress.WriteLineAsync($"Test complete. Dispose the user manager now.");
//         }
//         catch (Exception err)
//         {
//             await TestContext.Progress.WriteLineAsync($"{err.Message},\n{err.StackTrace}");
//         }
//
//         await Task.Delay(1200);
//         await TestContext.Progress.WriteLineAsync($"Cleaning data ...");
//         Assert.That(async () => await _userManager.DeleteAsync(fakeUser), Throws.Nothing);
//     }
//
//     [Test]
//     [Obsolete]
//     [Category("Test Case with User Manager")]
//     [TestCase(
//         "<EMAIL>",
//         "aa9b1382-82a8-48eb-992e-76bbb3dc3c35",
//         "b9aa1f3b-d4dc-452b-a4d5-9679a7cadf24",
//         1,
//         15)]
//     [TestCase(
//         "<EMAIL>",
//         "b6d7e442-38ae-4b9a-b100-2951729768bc",
//         "b94c4492-7e67-4647-8672-ce8e14fae69f",
//         2,
//         15)]
//     [TestCase(
//         "<EMAIL>",
//         "aa9b1382-82a8-48eb-992e-76bbb3dc3c35",
//         "ca98fbbb-b87e-4e0f-9a22-e70d94060da4",
//         300,
//         15)]
//     [TestCase(
//         "<EMAIL>",
//         "b6d7e442-38ae-4b9a-b100-2951729768bc",
//         "77f995be-695a-440c-8aa4-bb5bbc305931",
//         433,
//         15)]
//     public async Task DiveLoginTokenTest(
//         string adminUserEmail,
//         string companyId,
//         string staffIdentityId,
//         long staffId,
//         int validDurationInMinute)
//     {
//         var user = await _userManager.FindByEmailAsync(adminUserEmail);
//         var testToken = FakeDataGenerator.CreateAccessToken(user, _options);
//         var principal = FakeDataGenerator.TokenToClaimsPrincipal(testToken);
//         var cmsDiveController =
//             new Mock<InternalCmsDiveController>(_dbContext, _userManager, _mapper, _conf, null, null)
//             {
//                 CallBase = true
//             };
//         var context = new Mock<HttpContext>();
//         context.Setup(c => c.User).Returns(principal);
//
//         _internalCmsDiveController = cmsDiveController.Object;
//         _internalCmsDiveController.ControllerContext = new ControllerContext()
//         {
//             HttpContext = context.Object
//         };
//
//         try
//         {
//             var secret = await _internalCmsDiveController.GetLoginAsSecret(
//                 new GetLoginAsSecretRequest()
//                 {
//                     CompanyId = companyId,
//                     StaffIdentityId = staffIdentityId,
//                     StaffId = staffId,
//                     ValidDurationInMinute = validDurationInMinute
//                 });
//             var secretResult = secret.Result as OkObjectResult;
//             var secretResponse = secretResult?.Value as GetLoginAsSecretResponse;
//             await TestContext.Progress.WriteLineAsync($"Generated secret: {secretResponse?.LoginAsSecret}");
//
//             var result =
//                 await _coreService.GetTokenWithLoginAsSecret(
//                     CmsLoginAsHelper.ParseLoginAsSecret(secretResponse?.LoginAsSecret));
//             await TestContext.Progress.WriteLineAsync($"Generated token: {result.AccessToken}");
//
//             var principalUser = TokenToUserLoginPrincipal(result.AccessToken);
//             var transformatedPrincipal = await _sleekflowClaimsTransformation.TransformAsync(principalUser);
//
//             var jwtHandler = new JwtSecurityTokenHandler();
//             var jwtToken = jwtHandler.ReadJwtToken(testToken);
//
//             Assert.That(
//                 transformatedPrincipal.FindFirstValue(_options.ClaimsIdentity.EmailClaimType),
//                 Is.EqualTo(
//                     jwtToken.Claims.FirstOrDefault(c => c.Type == _options.ClaimsIdentity.EmailClaimType)
//                         ?.Value)
//             );
//         }
//         catch (Exception e)
//         {
//             await TestContext.Progress.WriteLineAsync($"{e.Message}");
//         }
//     }
//
//     [Test]
//     [Category("Test Case with User Manager")]
//     [TestCase("<EMAIL>", "P@ssw0rd!")]
//     [TestCase("<EMAIL>", "P@ssw0rd!")]
//     [TestCase("<EMAIL>", "P@ssw0rd!", "https://sso-dev.sf.chat/")]
//     [TestCase("<EMAIL>", "%L022b47", "https://sso-dev.sf.chat/")]
//     [TestCase("<EMAIL>", "P@ssw0rd!", "https://sso-dev.sf.chat/")]
//     public async Task UserManagerGetUserTest(string userEmail, string password, string? domain = null)
//     {
//         if (domain != null)
//             _authenticaionApiClient = new AuthenticationApiClient(new Uri(domain));
//         await TestContext.Progress.WriteLineAsync(
//             $"Logging in user {userEmail} to {_auth0Config.Audience}, client id: {_auth0Config.ClientId}");
//         var response = await LoginUser(userEmail, password);
//         var principal = TokenToUserLoginPrincipal(response.AccessToken);
//         var transformedPrincipal = await _sleekflowClaimsTransformation.TransformAsync(principal);
//         var testUser = await _userManager.GetUserAsync(transformedPrincipal);
//
//         Assert.That(testUser.Id, Is.Not.Null);
//         Assert.That(testUser.Email, Is.EqualTo(userEmail));
//     }
//
//     [Test]
//     [Category("Test Case with User Manager")]
//     public async Task UserManagerGetUserWhenUserNotInDbTest()
//     {
//         var pwd = $"P@ssw0rd!";
//         var domain = "https://sso-dev.sf.chat/";
//         var fakeUser = FakeDataGenerator.GenUsers().First();
//         var createRequest = SleekflowUserManager.ToCreateRequest(fakeUser);
//
//         createRequest.Password = pwd;
//         createRequest.Connection = _auth0Config.DatabaseConnectionName;
//         _authenticaionApiClient = new AuthenticationApiClient(new Uri(domain));
//
//         var createResponse = await _managementApiClient.Users.CreateAsync(createRequest);
//
//         try
//         {
//             await TestContext.Progress.WriteLineAsync(
//                 $"Logging in user {fakeUser.Email} to {_auth0Config.Audience}, client id: {_auth0Config.ClientId}");
//             var response = await LoginUser(fakeUser.Email, pwd);
//             var principal = TokenToUserLoginPrincipal(response.AccessToken);
//             var testUser = await _userManager.GetUserAsync(fakeUser);
//
//             Assert.That(testUser.Id, Is.Not.Null);
//         }
//         catch (Exception err)
//         {
//             await TestContext.Progress.WriteLineAsync($"User create failed.\n {err.Message}");
//         }
//
//         await _managementApiClient.Users.DeleteAsync(createResponse.UserId);
//     }
//
//     [Test]
//     public async Task UserManagerAddPasswordTest()
//     {
//         var testUser = FakeDataGenerator.GenUsers().First();
//         var createRes = await _userManager.CreateAsync(testUser);
//
//         if (!createRes.Succeeded)
//             Assert.Fail();
//
//         await TestContext.Progress.WriteLineAsync($"Created test user {testUser.Email}");
//
//         await Task.Delay(300);
//
//         var user = await _userManager.FindByEmailAsync(testUser.Email);
//         var auth0User = await _userManager.GetAuth0Users(user);
//         var updateRes = await _managementApiClient.Users.UpdateAsync(
//             auth0User.First().UserId,
//             new UserUpdateRequest()
//             {
//                 EmailVerified = true
//             });
//
//         await TestContext.Progress.WriteLineAsync($"Adding password ..");
//         var resPwdResult = await _userManager.AddPasswordAsync(testUser, "Passw0rd!");
//         if (!resPwdResult.Succeeded)
//             Assert.Fail();
//
//         await TestContext.Progress.WriteLineAsync($"Testing login with new password");
//         await Task.Delay(300);
//         var loginRes = await LoginUser(testUser.Email, "Passw0rd!");
//         if (string.IsNullOrEmpty(loginRes.AccessToken))
//             Assert.Fail();
//
//         await Task.Delay(500);
//         await TestContext.Progress.WriteLineAsync($"Test completed. ");
//         await _userManager.DeleteAsync(user);
//     }
//
//     [Test]
//     public async Task DeleteIdentityUserTest()
//     {
//         var testUser = FakeDataGenerator.GenUsers().First();
//         var createRes = await _userManager.CreateAsync(testUser);
//         await TestContext.Progress.WriteLineAsync($"Created {testUser.Email}");
//
//         await Task.Delay(300);
//
//         if (!createRes.Succeeded)
//             Assert.Fail();
//
//         try
//         {
//             var user = await _userManager.FindByEmailAsync(testUser.Email);
//             await _coreService.DeleteIdentityUser(user.Id);
//         }
//         catch (Exception err)
//         {
//             await TestContext.Progress.WriteAsync($"Error has throw: {err.Message}");
//             Assert.Fail();
//         }
//
//         var isDeletedUser = await _userManager.FindByEmailAsync(testUser.Email);
//         Assert.That(isDeletedUser, Is.Null);
//     }
//
//     [Test]
//     public async Task UpdateUserNameTest()
//     {
//         var testUser = FakeDataGenerator.GenUsers().First();
//         var createRes = await _userManager.CreateAsync(testUser);
//         await TestContext.Progress.WriteLineAsync($"Created {testUser.Email}");
//
//         await Task.Delay(300);
//
//         if (!createRes.Succeeded)
//             Assert.Fail("Create request failed.");
//
//         var user = await _userManager.FindByEmailAsync(testUser.Email);
//         var userId = user.Id;
//         try
//         {
//             var newUserName = "UnitTest." + SleekflowUserManager.GenerateRandomString(5);
//             var result = await _userManager.SetUserNameAsync(user, newUserName);
//
//             var check = await _userManager.FindByIdAsync(userId);
//             var auth0Check = await _userManager.GetAuth0Users(user);
//             if (!result.Succeeded)
//             {
//                 await TestContext.Progress.WriteLineAsync(
//                     $"Error: {JsonConvert.SerializeObject(result, Formatting.Indented)}");
//             }
//
//             Assert.That(check.UserName, Is.EqualTo(newUserName));
//         }
//         catch (Exception err)
//         {
//             await TestContext.Progress.WriteAsync($"Error has throw: {err.Message}");
//             await TestContext.Progress.WriteLineAsync($"Delete user {user.Email}");
//             await _userManager.DeleteAsync(user);
//             Assert.Fail();
//         }
//
//         await TestContext.Progress.WriteLineAsync($"Delete user {user.Email}");
//         await _userManager.DeleteAsync(user);
//     }
//
//     public User ToAuth0EventUser(ApplicationUser user)
//     {
//         var result = new User()
//         {
//             UserId = $"auth0|{user.Id}",
//             UserName = user.UserName,
//             Email = user.Email,
//             FullName = user.DisplayName,
//             FirstName = user.FirstName,
//             LastName = user.LastName,
//             Picture = user.PictureUrl,
//             AppMetadata = new Auth0AppMetadata(
//                 new List<string>(),
//                 user.PhoneNumber,
//                 user.Id,
//                 true,
//                 null),
//             EmailVerified = user.EmailConfirmed
//         };
//
//         return result;
//     }
//
//     [Test]
//     public async Task TenantHubConnectionTest()
//     {
//         var allFeatures = await _managementFeaturesApi.ManagementFeaturesGetAllFeaturesPostAsync(body: new object());
//         await TestContext.Progress.WriteLineAsync($"{JsonConvert.SerializeObject(allFeatures)}");
//
//         Assert.IsNotNull(allFeatures);
//     }
//
//     [Test]
//     // [TestCase("<EMAIL>", "P@ssw0rd")]
//     [TestCase("<EMAIL>", "P@ssw0rd")]
//     public async Task TenantHubGet2FaTest(string tenantHubUser, string tenantHubPassword)
//     {
//         try
//         {
//             // var login = await LoginUser(tenantHubUser, tenantHubPassword);
//             // var loginAccessToken = login.AccessToken;
//             var user = await _userManager.FindByEmailAsync(tenantHubUser);
//             var userStaff = await _coreService.GetCompanyStaff(user);
//             var eventUser = ToAuth0EventUser(user);
//             var allAvailableFeatures =
//                 await _managementFeaturesApi.ManagementFeaturesGetAllFeaturesPostAsync(body: new object());
//             var allAvailableRoles = await _managementRolesApi.ManagementRolesGetAllRolesPostAsync(body: new object());
//             var mfaFeature = allAvailableFeatures.Data.Features.FirstOrDefault(f => f.Name == "2FA");
//             var userStaffRole =
//                 allAvailableRoles.Data.Roles.FirstOrDefault(r => r.Name == userStaff.RoleType.ToString());
//
//             foreach (var role in allAvailableRoles.Data.Roles)
//             {
//                 try
//                 {
//                     await TestContext.Progress.WriteLineAsync($"Enabling role: {role.Name}..");
//                     var result = await _managementEnabledFeaturesApi
//                         .ManagementEnabledFeaturesEnableFeatureForRolePostAsync(
//                             managementEnableFeatureForRoleInput: new ManagementEnableFeatureForRoleInput(
//                                 userStaff.CompanyId,
//                                 role.Id,
//                                 mfaFeature.Id));
//                 }
//                 catch (Exception err) { }
//             }
//
//             var mfaResult =
//                 await _managementEnabledFeaturesApi.ManagementEnabledFeaturesIsFeatureEnabledForRolePostAsync(
//                     managementIsFeatureEnabledForRoleInput: new ManagementIsFeatureEnabledForRoleInput(
//                         userStaff.CompanyId,
//                         userStaffRole.Id,
//                         mfaFeature.Id));
//             // await TestContext.Progress.WriteLineAsync($"{JsonConvert.SerializeObject(allAvailableFeatures)}");
//
//             Assert.That(mfaResult.Data.IsFeatureEnabled, Is.True);
//         }
//         catch (Exception err)
//         {
//             await TestContext.Progress.WriteLineAsync($"Error! {err.Message}\n{err.InnerException}");
//             Assert.Fail();
//         }
//     }
//
//     [Test]
//     // [TestCase("<EMAIL>", "P@ssw0rd")]
//     [TestCase("<EMAIL>", "P@ssw0rd")]
//     public async Task TenantHubSetEnableDisableTest(string tenantHubUser, string tenantHubPassword)
//     {
//         try
//         {
//             var user = await _userManager.FindByEmailAsync(tenantHubUser);
//             var eventUser = ToAuth0EventUser(user);
//             var userStaff = await _coreService.GetCompanyStaff(user);
//             var allAvailableFeatures =
//                 await _managementFeaturesApi.ManagementFeaturesGetAllFeaturesPostAsync(body: new object());
//             var mfaFeature = allAvailableFeatures.Data.Features.FirstOrDefault(f => f.Name == "2FA");
//             // var allAvailableRoles = await _rolesApi.CompanyStaffRolesGetAllCompanyStaffRolesPostAsync(body: new object());
//             var allAvailableRoles = await _managementRolesApi.ManagementRolesGetAllRolesPostAsync(body: new object());
//             var userStaffRole =
//                 allAvailableRoles.Data.Roles.FirstOrDefault(r => r.Name == userStaff.RoleType.ToString());
//             var eventToken = FakeDataGenerator.CreateAuth0EventToken(
//                 _conf,
//                 SecretKey);
//
//             Assert.That(mfaFeature, Is.Not.Null);
//             Assert.That(userStaffRole, Is.Not.Null);
//
//             await TestContext.Progress.WriteLineAsync($"Event user: \n{JsonConvert.SerializeObject(eventUser)}");
//             await TestContext.Progress.WriteLineAsync($"Disabling all features..");
//             try
//             {
//                 // Disable all Features first
//                 foreach (var roleItem in allAvailableRoles.Data.Roles)
//                 {
//                     // await _managementEnabledFeaturesApi.EnabledFeaturesDisableFeatureForCompanyStaffRolePostAsync(
//                     // disableFeatureForCompanyStaffRoleInput: new DisableFeatureForCompanyStaffRoleInput(
//                     await _managementEnabledFeaturesApi.ManagementEnabledFeaturesDisableFeatureForRolePostAsync(
//                         managementDisableFeatureForRoleInput: new ManagementDisableFeatureForRoleInput(
//                             userStaff.CompanyId,
//                             roleItem.Id,
//                             mfaFeature.Id));
//                     await TestContext.Progress.WriteLineAsync($"Disabled {roleItem.Name}.");
//                 }
//             } // Ignore the error. (If feature is already disabled, it will throw the error.)
//             catch { }
//
//             try
//             {
//                 await _managementEnabledFeaturesApi.ManagementEnabledFeaturesDisableFeatureForCompanyPostAsync(
//                     managementDisableFeatureForCompanyInput: new ManagementDisableFeatureForCompanyInput(
//                         userStaff.CompanyId,
//                         mfaFeature.Id));
//                 await TestContext.Progress.WriteLineAsync($"Disabled {userStaff.Company.CompanyName}.");
//             } // Ignore the error.
//             catch { }
//
//             await Task.Delay(300);
//
//             // Test start.
//             await TestContext.Progress.WriteLineAsync($"Starting the test..");
//             var companyFeature =
//                 await _managementEnabledFeaturesApi.ManagementEnabledFeaturesIsFeatureEnabledForCompanyPostAsync(
//                     managementIsFeatureEnabledForCompanyInput: new ManagementIsFeatureEnabledForCompanyInput(
//                         userStaff.CompanyId,
//                         mfaFeature.Id));
//
//             if (!companyFeature.Data.IsFeatureEnabled)
//             {
//                 var updateResult = await _managementEnabledFeaturesApi
//                     .ManagementEnabledFeaturesEnableFeatureForCompanyPostAsync(
//                         managementEnableFeatureForCompanyInput: new ManagementEnableFeatureForCompanyInput(
//                             userStaff.CompanyId,
//                             mfaFeature.Id));
//                 Assert.That(updateResult.Success, Is.True);
//                 await TestContext.Progress.WriteLineAsync($"Enabled Mfa feature of {userStaff.Company.CompanyName}.");
//             }
//
//             var mfaResult =
//                 await _managementEnabledFeaturesApi.ManagementEnabledFeaturesIsFeatureEnabledForRolePostAsync(
//                     managementIsFeatureEnabledForRoleInput: new ManagementIsFeatureEnabledForRoleInput(
//                         userStaff.CompanyId,
//                         userStaffRole.Id,
//                         mfaFeature.Id));
//
//             var staffAdminFeature =
//                 await _managementEnabledFeaturesApi.ManagementEnabledFeaturesIsFeatureEnabledForRolePostAsync(
//                     managementIsFeatureEnabledForRoleInput: new ManagementIsFeatureEnabledForRoleInput(
//                         userStaff.CompanyId,
//                         userStaffRole.Id,
//                         mfaFeature.Id));
//
//             try
//             {
//                 foreach (var roleItem in allAvailableRoles.Data.Roles)
//                 {
//                     var updateResult =
//                         await _managementEnabledFeaturesApi.ManagementEnabledFeaturesEnableFeatureForRolePostAsync(
//                             managementEnableFeatureForRoleInput: new ManagementEnableFeatureForRoleInput(
//                                 userStaff.CompanyId,
//                                 roleItem.Id,
//                                 mfaFeature.Id));
//                     Assert.That(updateResult.Success, Is.True);
//                     await TestContext.Progress.WriteLineAsync($"Enabled Mfa feature of role {roleItem.Description}.");
//                 }
//             }
//             catch { }
//
//             await Task.Delay(500);
//             foreach (var roleItem in allAvailableRoles.Data.Roles)
//             {
//                 var finalUpdateResult =
//                     await _managementEnabledFeaturesApi.ManagementEnabledFeaturesIsFeatureEnabledForRolePostAsync(
//                         managementIsFeatureEnabledForRoleInput: new ManagementIsFeatureEnabledForRoleInput(
//                             userStaff.CompanyId,
//                             roleItem.Id,
//                             mfaFeature.Id));
//
//                 await TestContext.Progress.WriteLineAsync(
//                     $"Enable feature success: {roleItem.Description} {finalUpdateResult.Data.IsFeatureEnabled}.");
//             }
//
//             // Checking final result.
//             var userMfaResult =
//                 await _managementEnabledFeaturesApi.ManagementEnabledFeaturesIsFeatureEnabledForRolePostAsync(
//                     managementIsFeatureEnabledForRoleInput: new ManagementIsFeatureEnabledForRoleInput(
//                         userStaff.CompanyId,
//                         userStaffRole.Id,
//                         mfaFeature.Id));
//
//             await TestContext.Progress.WriteLineAsync(
//                 $"Tenant Hub check by user id result: {userMfaResult.Data.IsFeatureEnabled}.");
//         }
//         catch (Exception err)
//         {
//             await TestContext.Progress.WriteLineAsync(
//                 $"Error! {err.Message}\n{err.InnerException}\n\n{err.StackTrace}");
//             Assert.Fail();
//         }
//     }
//
//     [Test]
//     [TestCase("<EMAIL>")]
//     public async Task TenantHubControllerTest(string username)
//     {
//         var user = await _userManager.FindByEmailAsync(username);
//         var userStaff = await _coreService.GetCompanyStaff(user);
//         var testToken = FakeDataGenerator.CreateAccessToken(user, _options);
//         var principal = FakeDataGenerator.TokenToClaimsPrincipal(testToken);
//         var mockHttpContext = new Mock<HttpContext>();
//
//         mockHttpContext.Setup(c => c.User).Returns(principal);
//
//         var mockControllerContext = new ControllerContext()
//         {
//             HttpContext = mockHttpContext.Object
//         };
//         _tenantHubController.ControllerContext = mockControllerContext;
//
//         var testRes = (await _tenantHubController.IsCompanyCreated(
//             new TenantHubController.IsCompanyCreatedRequest())).Result as OkObjectResult;
//
//         await TestContext.Progress.WriteLineAsync($"{JsonConvert.SerializeObject(testRes.Value)}");
//         Assert.That((testRes.Value as ManagementIsCompanyCreatedOutputOutput).Data.IsCompanyCreated, Is.True);
//
//         await TestContext.Progress.WriteLineAsync($"Test Get all features");
//         var allFeaturesRes = (await _tenantHubController.GetAllFeatures(
//             new TenantHubController.GetAllFeaturesRequest())).Result as OkObjectResult;
//         var featuresList = allFeaturesRes.Value as GetAllFeaturesOutputOutput;
//
//         Assert.That(featuresList.Data.Features.Any(o => o.Name == "2FA"), Is.True);
//
//         // Disable all features.
//         try
//         {
//             var res = await _tenantHubController.DisableFeatureForCompany(
//                 new TenantHubController.DisableFeatureForCompanyRequest()
//                 {
//                     FeatureName = "2FA"
//                 });
//             Assert.True(true);
//         }
//         catch { }
//
//         try
//         {
//             var res = await _tenantHubController.DisableFeatureForRole(
//                 new TenantHubController.DisableFeatureForRoleRequest()
//                 {
//                     FeatureName = "2FA", RoleName = userStaff.RoleType.ToString()
//                 });
//             Assert.True(true);
//         }
//         catch { }
//
//         // Enable 2FA Feature test.
//         var isEnabledForCompanyRes = (await _tenantHubController.IsFeatureEnabledForCompany(
//             new TenantHubController.IsFeatureEnabledForCompanyRequest()
//             {
//                 FeatureName = "2FA"
//             })).Result as OkObjectResult;
//         var isEnabledForCompany = isEnabledForCompanyRes.Value as IsFeatureEnabledForCompanyOutputOutput;
//         if (!isEnabledForCompany.Data.IsFeatureEnabled)
//         {
//             await TestContext.Progress.WriteLineAsync($"Feature for company is disabled. Enable it now");
//             var res = (await _tenantHubController.EnableFeatureForCompany(
//                 new TenantHubController.EnableFeatureForCompanyRequest()
//                 {
//                     FeatureName = "2FA"
//                 })).Result as OkObjectResult;
//
//             Assert.That((res.Value as EnableFeatureForCompanyOutputOutput).Success, Is.True);
//         }
//
//         var isEnabledFeatureForRoleRes = (await _tenantHubController.IsFeatureEnabledForRole(
//             new TenantHubController.IsFeatureEnabledForRoleRequest()
//             {
//                 FeatureName = "2FA", RoleName = userStaff.RoleType.ToString()
//             })).Result as OkObjectResult;
//         var isEnabledFeatureForRole = isEnabledFeatureForRoleRes.Value as IsFeatureEnabledForRoleOutputOutput;
//         if (!isEnabledFeatureForRole.Data.IsFeatureEnabled)
//         {
//             await TestContext.Progress.WriteLineAsync($"Feature for company is disabled. Enable it now");
//             var res = (await _tenantHubController.EnableFeatureForRole(
//                 new TenantHubController.EnableFeatureForRoleRequest()
//                 {
//                     FeatureName = "2FA", RoleName = userStaff.RoleType.ToString()
//                 })).Result as OkObjectResult;
//
//             Assert.That((res.Value as EnableFeatureForRoleOutputOutput).Success, Is.True);
//         }
//
//         var roleRes =
//             (await _tenantHubController.GetAllRoles(new TenantHubController.GetAllRolesRequest())).Result as
//             OkObjectResult;
//         var userRole =
//             (roleRes.Value as GetAllRolesOutputOutput).Data.Roles.FirstOrDefault(
//                 r => r.Name == userStaff.RoleType.ToString());
//         var testResult1 = await _managementEnabledFeaturesApi.ManagementEnabledFeaturesIsFeatureEnabledForRolePostAsync(
//             managementIsFeatureEnabledForRoleInput: new ManagementIsFeatureEnabledForRoleInput(
//                 userStaff.CompanyId,
//                 userRole.Id,
//                 featuresList.Data.Features.FirstOrDefault(r => r.Name == "2FA").Id));
//
//         // Check User Role is enabled
//         Assert.That(testResult1.Data.IsFeatureEnabled, Is.True);
//
//
//         // Final: Auth0 event test.
//         await TestContext.Progress.WriteLineAsync($"Final: Testing user isRequireMfa event");
//
//         var eventUser = ToAuth0EventUser(user);
//         var eventToken = FakeDataGenerator.CreateAuth0EventToken(
//             _conf,
//             SecretKey);
//
//         var isRequireMfaResult = (await _auth0Event.RequiresMfa(
//             eventToken,
//             new Auth0ActionEventController
//                 .RequiresMfaRequest(eventUser, "UnitTest", "auth0", "Sleekflow-Username-Password-Authentication")))
//             .Result as OkObjectResult;
//
//         var isRequireMfa = isRequireMfaResult.Value as Auth0ActionEventController.RequiresMfaResponse;
//
//         await TestContext.Progress.WriteLineAsync(
//             $"User {user.Email} is Require MFA = {isRequireMfa.IsMfaRequired.ToString()}");
//         Assert.That(isRequireMfa.IsMfaRequired, Is.True);
//     }
//
//     [Test]
//     [TestCase("<EMAIL>")]
//     public async Task SendMfaEnrollmentEmailTest(string userEmail)
//     {
//         var user = await _userManager.FindByEmailAsync(userEmail);
//         var result = await _userManager.SetTwoFactorEnabledAsync(user, true);
//
//         Assert.That(result.Succeeded, Is.True);
//     }
//
//     [Test]
//     [TestCase(StaffUserRole.Admin, "873a1d73-9c90-41f7-aaba-d0854324e820")]
//     [TestCase(StaffUserRole.Staff, "873a1d73-9c90-41f7-aaba-d0854324e820")]
//     public async Task FindEnrolledUsersByRole(StaffUserRole role, string companyId)
//     {
//         var staffs = await _dbContext.UserRoleStaffs
//             .Where(s => s.CompanyId == companyId && s.RoleType == role)
//             .Include(x => x.Identity)
//             .ToListAsync();
//
//         await TestContext.Progress.WriteLineAsync($"{staffs.Count} users found in role: {role}");
//         Assert.That(staffs.Count, Is.GreaterThan(0), $"{role} not found in company.");
//
//         foreach (var staff in staffs)
//         {
//             // var tmpUser = await _userManager.FindByIdAsync(staff.IdentityId);
//             await TestContext.Progress.WriteLineAsync($"{staff.Identity.Email} is in role: {staff.RoleType}");
//
//             Assert.That(staff.RoleType, Is.EqualTo(role));
//         }
//     }
//
//     [Test]
//     [TestCase("Admin", "873a1d73-9c90-41f7-aaba-d0854324e820")]
//     [TestCase("Staff", "873a1d73-9c90-41f7-aaba-d0854324e820")]
//     public async Task SendMfaEnrolledEmailsByRole(string role, string companyId)
//     {
//         var convertedRole = (StaffUserRole) Enum.Parse(typeof(StaffUserRole), role, true);
//         Assert.That(
//             async () => await _companyService.SendMfaEnrollmentEmailsByRole(convertedRole, companyId),
//             Throws.Nothing);
//     }
//
//     [Test]
//     [TestCase("identities.provider:auth0")]
//     [TestCase("email:b.chan*")]
//     [TestCase("email:<EMAIL> AND identities.provider:auth0")]
//     public async Task Auth0UsersSearchByQueryTest(string query)
//     {
//         string q = $"{query}";
//         // The search Query will be like as follow:
//         // string q = $"user_id:e54adc2a-5e80-413f-8402-f2bf607e55ed";
//         var objectResult = (await _internalCmsAuth0Controller.GetAuth0Users(
//             new GetAuth0UsersRequest()
//             {
//                 Query = q
//             })).Result as OkObjectResult;
//         var result = objectResult?.Value as IPagedList<User>;
//
//         var objectAllResult = (await _internalCmsAuth0Controller.GetAuth0Users(
//             new GetAuth0UsersRequest()
//             {
//                 Query = null, Page = 2, ItemPerPage = 10
//             })).Result as OkObjectResult;
//         var allResult = objectAllResult?.Value as IPagedList<User>;
//
//         await TestContext.Progress.WriteLineAsync($"returned result: {result?.Count}");
//         await TestContext.Progress.WriteLineAsync(
//             $"{JsonConvert.SerializeObject(result?.Paging, Formatting.Indented)}");
//         await TestContext.Progress.WriteLineAsync(
//             $"{JsonConvert.SerializeObject(allResult?.Paging, Formatting.Indented)}");
//         await TestContext.Progress.WriteLineAsync(
//             $"{JsonConvert.SerializeObject(objectResult?.Value, Formatting.Indented)}");
//
//         Assert.That(result?.Count > 0, "result.Count not > 0");
//     }
//
//     [Test]
//     public async Task Auth0UsersSearchTest()
//     {
//         // Search by multiple query
//         var objectResult = (await _internalCmsAuth0Controller.GetAuth0Users(
//             new GetAuth0UsersRequest()
//             {
//                 IsBlocked = true, IsSocial = false,
//             })).Result as OkObjectResult;
//         var result = objectResult?.Value as GetAuth0UsersResponse;
//
//         var objectResult2 = (await _internalCmsAuth0Controller.GetAuth0Users(
//             new GetAuth0UsersRequest()
//             {
//                 // UserId = "e54adc2a-5e80-413f-8402-f2bf607e55ed"
//                 Email = "<EMAIL>"
//             })).Result as OkObjectResult;
//         var result2 = objectResult2?.Value as GetAuth0UsersResponse;
//
//         Assert.That(result?.Users?.Count > 0, "[Search user]result.Count should be > 0");
//         Assert.That(result2?.Users?.Count > 0, "[Search user]result2.Count should be > 0");
//     }
//
//     [Test]
//     [TestCase("<EMAIL>")]
//     public async Task Auth0ApiMfaTest(string userEmail)
//     {
//         var user = await _userManager.FindByEmailAsync(userEmail);
//         var auth0Users = await _userManager.GetAuth0Users(user);
//         var mfaList = new List<AuthenticationMethod>();
//
//         foreach (var auth0User in auth0Users)
//         {
//             var mfaRespose = (await _internalCmsAuth0Controller.GetUserMfaList(
//                 new GetUserMfaRequest()
//                 {
//                     Auth0UserId = auth0User.UserId
//                 })).Result as OkObjectResult;
//
//             mfaList.AddRange((mfaRespose.Value as IPagedList<AuthenticationMethod>).ToList());
//
//             await TestContext.Progress.WriteLineAsync(
//                 $"{JsonConvert.SerializeObject(mfaRespose)}");
//         }
//
//         await TestContext.Progress.WriteLineAsync(
//             $"Final result:\n{JsonConvert.SerializeObject(mfaList, Formatting.Indented)}");
//     }
//
//     [Test]
//     [TestCase("<EMAIL>")]
//     public async Task Auth0UserLogs(string userEmail)
//     {
//         var user = await _userManager.FindByEmailAsync(userEmail);
//         var auth0UsersResponse = (await _internalCmsAuth0Controller.GetAuth0Users(
//             new GetAuth0UsersRequest()
//             {
//                 Email = userEmail, Provider = "auth0"
//             })).Result as OkObjectResult;
//         var auth0Users = auth0UsersResponse?.Value as GetAuth0UsersResponse;
//         // var logsHistory = new PagedList<LogEntry?>();
//         var logsHistory = new PagedList<dynamic?>();
//
//         foreach (var auth0User in auth0Users.Users)
//         {
//             /*
//             var logsResponse = await _managementApiClient.Users.GetLogsAsync(
//                 new GetUserLogsRequest()
//                 {
//                     UserId = auth0User.UserId
//                 });
//                 */
//             // var logsResponse = await _userManager.GetLogsAsync(auth0User.UserId);
//             var logsResponse = await _internalCmsAuth0Controller.GetUserLoginHistory(
//                 new GetUserHistoryRequest()
//                 {
//                     Auth0UserId = auth0User.UserId
//                 }) as OkObjectResult;
//             logsHistory.AddRange(
//                 (logsResponse?.Value as GetUserHistoryResponse)
//                 .Data.Select(
//                     d => new
//                     {
//                         d.Date, d.ClientName, d.Strategy, d.Description
//                     })
//                 .ToList());
//             logsHistory.Paging = (logsResponse?.Value as GetUserHistoryResponse)
//                 .PageInfo;
//         }
//
//         await TestContext.Progress.WriteLineAsync(
//             $"Final result:\n{JsonConvert.SerializeObject(logsHistory.Paging, Formatting.Indented)}");
//         await TestContext.Progress.WriteLineAsync(
//             $"Final result:\n{JsonConvert.SerializeObject(logsHistory, Formatting.Indented)}");
//     }
//
//     [Test]
//     [TestCase("<EMAIL>")]
//     public async Task BlockedStatusTest(string email)
//     {
//         var user = await _userManager.FindByEmailAsync(email);
//         var result = (await _internalCmsAuth0Controller.SetBlockedStatus(
//             new SetAuth0UserBlockStatusRequest()
//             {
//                 UserId = user.Id, IsBlocked = true
//             })).Result as OkObjectResult;
//
//         var checkIsBlocked = await _userManager.GetAuth0Users(user);
//
//         Assert.That(checkIsBlocked.FirstOrDefault()?.Blocked, Is.True.Or.False);
//         Assert.That(result?.StatusCode == 200);
//
//         var fallbackResult = (await _internalCmsAuth0Controller.SetBlockedStatus(
//             new SetAuth0UserBlockStatusRequest()
//             {
//                 UserId = user.Id, IsBlocked = false
//             })).Result as OkObjectResult;
//
//         Assert.That(fallbackResult?.StatusCode == 200);
//     }
//
//     public ControllerContext CreateUserPrincipalContext(ApplicationUser user)
//     {
//         var testToken = FakeDataGenerator.CreateAccessToken(user, _options);
//         var principal = FakeDataGenerator.TokenToClaimsPrincipal(testToken);
//         var mockHttpContext = new Mock<HttpContext>();
//
//         mockHttpContext.Setup(c => c.User).Returns(principal);
//
//         var mockControllerContext = new ControllerContext()
//         {
//             HttpContext = mockHttpContext.Object
//         };
//
//         return mockControllerContext;
//     }
//
//     [Test]
//     [TestCase("<EMAIL>", "<EMAIL>", false, false)]
//     [TestCase("<EMAIL>", "<EMAIL>", true, false)]
//     [TestCase("<EMAIL>", "<EMAIL>", false, false)]
//     public async Task Auth0MfaAdminTest(
//         string adminEmail,
//         string targetEmail,
//         bool accessIsDenied = true,
//         bool resetMfa = false)
//     {
//         var adminUser = await _userManager.FindByEmailAsync(adminEmail);
//         var targetUser = await _userManager.FindByEmailAsync(targetEmail);
//         var httpContext = CreateUserPrincipalContext(adminUser);
//
//         _auth0AccountController.ControllerContext = httpContext;
//
//         if (accessIsDenied)
//         {
//             var response = (await _auth0AccountController.GetMfaList(
//                 new Auth0AccountController.GetMfaListRequest()
//                 {
//                     UserId = targetUser.Id
//                 })).Result as UnauthorizedResult;
//
//             await TestContext.Progress.WriteLineAsync(
//                 $"Access denied result:\n{JsonConvert.SerializeObject(response, Formatting.Indented)}");
//             Assert.That(response, Is.Not.Null);
//         }
//         else
//         {
//             var targetUserStaff = await _coreService.GetCompanyStaff(targetUser);
//             var getAllFeaturesOutputOutput =
//                 await _featuresApi.FeaturesGetAllFeaturesPostAsync(body: new object());
//
//             var mfaFeature =
//                 getAllFeaturesOutputOutput.Data.Features.Find(f => f.Name == "2FA");
//
//             var isMfaEnabled =
//                 await _enabledFeaturesApi.EnabledFeaturesGetFeatureEnablementsPostAsync(
//                     getFeatureEnablementsInput: new GetFeatureEnablementsInput(mfaFeature!.Id, targetUserStaff.CompanyId));
//
//             var response = (await _auth0AccountController.GetMfaList(
//                 new Auth0AccountController.GetMfaListRequest()
//                 {
//                     UserId = targetUser.Id
//                 })).Result as OkObjectResult;
//             var result = response?.Value as List<Auth0AccountController.GetMfaListResponse>;
//
//             await TestContext.Progress.WriteLineAsync(
//                 $"Final result:\n{JsonConvert.SerializeObject(result, Formatting.Indented)}");
//
//             if (isMfaEnabled.Data.IsFeatureEnabledForCompany)
//             {
//                 Assert.That(result.Count > 0, "result should > 0");
//             }
//             else
//             {
//                 Assert.That(result, Is.Empty.Or.Null, "result should be empty because company feature is disabled");
//             }
//
//             if (resetMfa)
//             {
//                 var resetResponse = (_auth0AccountController.ResetMfa(
//                     new Auth0AccountController.ResetMfaRequest()
//                     {
//                         UserId = targetUser.Id, MfaId = result.FirstOrDefault().MfaId
//                     }).Result as ObjectResult);
//                 Assert.That(resetResponse.StatusCode, Is.EqualTo(200));
//             }
//         }
//     }
//
//     [Test]
//     [TestCase("<EMAIL>")]
//     public async Task InviteTokenProviderTest(string userEmail)
//     {
//         var targetUser = await _userManager.FindByEmailAsync(userEmail);
//         if (targetUser == null)
//         {
//             Assert.Fail();
//         }
//
//         var token = await _userManager.GenerateUserTokenAsync(
//             targetUser,
//             SleekflowTokenProviderOptions.InviteTokenProviderName,
//             "Invite Test");
//
//         await TestContext.Progress.WriteLineAsync($"Generated token: {token}");
//
//         Assert.That(token, Is.Not.Null);
//
//         var result = await _userManager.VerifyUserTokenAsync(
//             targetUser,
//             SleekflowTokenProviderOptions.InviteTokenProviderName,
//             "Invite Test",
//             token);
//
//         await TestContext.Progress.WriteLineAsync($"Verify token result: {result}");
//
//         Assert.That(result, Is.True);
//
//         try
//         {
//             var wrongResult = await _userManager.VerifyUserTokenAsync(
//                 targetUser,
//                 SleekflowTokenProviderOptions.InviteTokenProviderName,
//                 "Invite",
//                 "wrongToken");
//         }
//         catch (Exception e)
//         {
//             Assert.That(e.Message, Is.EqualTo("Invalid token."));
//         }
//     }
//
//
//     /*
//     [Test]
//     [Category("Test Case with User Manager")]
//     public async Task ADFSOnPostLoginTest()
//     {
//         var secretKey = _conf["Auth0:ActionIssuer"] + _conf["Auth0:ActionAudience"] + "+wsadz4gI_3DUXI8P";
//         var eventToken = FakeDataGenerator.CreateAuth0EventToken(_conf, secretKey);
//         var eventUser = FakeDataGenerator.ADFSEventUser();
//
//         var res =
//             await _auth0Event.PostUserLogin("adfs", eventToken, eventUser!);
//
//         var okRes = res.Result as OkObjectResult;
//         var response = okRes?.Value as Auth0ActionEventController.PostUserLoginResponse;
//
//         Assert.That(okRes?.StatusCode, Is.EqualTo(200), "Status code should be 200");
//         Assert.That(response, Is.Not.Null, "Response should not be null");
//     }
//
//     [Test]
//     [Category("Test Case with User Manager")]
//     public async Task OpenIDOnPostLoginTest()
//     {
//         var secretKey = _conf["Auth0:ActionIssuer"] + _conf["Auth0:ActionAudience"] + "+wsadz4gI_3DUXI8P";
//         var eventToken = FakeDataGenerator.CreateAuth0EventToken(_conf, secretKey);
//         var eventUser = FakeDataGenerator.OpenIDEventUser();
//
//         var res =
//             await _auth0Event.PostUserLogin("oidc", eventToken, eventUser!);
//
//         var okRes = res.Result as OkObjectResult;
//         var response = okRes?.Value as Auth0ActionEventController.PostUserLoginResponse;
//
//         Assert.That(okRes?.StatusCode, Is.EqualTo(200), "Status code should be 200");
//         Assert.That(response, Is.Not.Null, "Response should not be null");
//     }
//     */
// }
