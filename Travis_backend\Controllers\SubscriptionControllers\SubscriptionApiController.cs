using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Travis_backend.CommonDomain.ViewModels;
using Travis_backend.Extensions;
using Travis_backend.Infrastructures.Attributes;
using Travis_backend.SubscriptionPlanDomain.Models;
using Travis_backend.SubscriptionPlanDomain.Services;
using Travis_backend.SubscriptionPlanDomain.ViewModels;

namespace Travis_backend.Controllers.SubscriptionControllers;

/// <summary>
/// API Controller related to subscription plans / add-ons.
/// </summary>
[Route("subscription")]
[Authorize]
public class SubscriptionApiController : Controller
{
    /// <summary>
    /// ISubscriptionPlanService
    /// </summary>
    private readonly ISubscriptionPlanService _subscriptionPlanService;

    /// <summary>
    /// IHttpContextAccessor
    /// </summary>
    private readonly IHttpContextAccessor _httpContextAccessor;

    /// <summary>
    /// Initializes a new instance of the <see cref="SubscriptionApiController"/> class.
    /// </summary>
    /// <param name="subscriptionPlanService">ISubscriptionPlanService</param>
    /// <param name="httpContextAccessor">IHttpContextAccessor</param>
    public SubscriptionApiController(
        ISubscriptionPlanService subscriptionPlanService,
        IHttpContextAccessor httpContextAccessor)
    {
        _subscriptionPlanService = subscriptionPlanService;
        _httpContextAccessor = httpContextAccessor;
    }

    [AuthoriseUser]
    [HttpGet("plans")]
    public async Task<IActionResult> GetAvailableSubscriptionPlans()
    {
        var companyId = _httpContextAccessor?.HttpContext?.GetRequestCompanyId();
        var ipAddress = _httpContextAccessor?.HttpContext?.GetRequestIpAddress();

        var availableBasePlans = await _subscriptionPlanService.GetAvailableSubscriptionPlans(companyId, ipAddress);

        return Ok(availableBasePlans);
    }

    [AuthoriseUser]
    [HttpGet("plans/subscribe/{subscriptionPlanId}/add-ons")]
    public async Task<IActionResult> GetAvailableAddOnsForSubscriptionPlan(string subscriptionPlanId)
    {
        var companyId = _httpContextAccessor?.HttpContext?.GetRequestCompanyId();

        var availableBasePlans = await _subscriptionPlanService.GetAvailableAddOnsForSubscriptionPlan(companyId, subscriptionPlanId);

        return Ok(availableBasePlans);
    }

    [AuthoriseUser]
    [HttpGet("add-ons")]
    public async Task<IActionResult> GetAvailableAddOns()
    {
        var companyId = _httpContextAccessor?.HttpContext?.GetRequestCompanyId();
        var ipAddress = _httpContextAccessor?.HttpContext?.GetRequestIpAddress();

        var addOns = await _subscriptionPlanService.GetAvailableAddOns(companyId, ipAddress);

        return Ok(addOns);
    }

    [Obsolete("Due to the new requirements, this API are not able to response multiple plan details at same time, Use \"add-ons/{addOnType}/purchase-options\" instead. ")]
    [AuthoriseUser]
    [HttpGet("add-ons/purchase/{subscriptionPlanId}")]
    public async Task<IActionResult> GetPurchaseAddOnDetails(string subscriptionPlanId)
    {
        var companyId = _httpContextAccessor?.HttpContext?.GetRequestCompanyId();
        var ipAddress = _httpContextAccessor?.HttpContext?.GetRequestIpAddress();

        try
        {
            var addOnDetails = await _subscriptionPlanService.GetPurchaseAddOnDetails(companyId, subscriptionPlanId, ipAddress);
            return Ok(addOnDetails);
        }
        catch (ApplicationException exception)
        {
            return BadRequest(new ResponseViewModel(exception.Message));
        }
    }

    [AuthoriseUser]
    [HttpGet("add-ons/{addOnType}/purchase-options")]
    public async Task<IActionResult> GetAddOnPurchaseOptions(string addOnType)
    {
        var companyId = _httpContextAccessor?.HttpContext?.GetRequestCompanyId();
        var ipAddress = _httpContextAccessor?.HttpContext?.GetRequestIpAddress();

        try
        {
            var addOnDetails = await _subscriptionPlanService.GetAddOnPurchaseOptions(companyId, addOnType, ipAddress);
            return Ok(addOnDetails);
        }
        catch (ApplicationException exception)
        {
            return BadRequest(new ResponseViewModel(exception.Message));
        }
    }

    [AuthoriseUser]
    [HttpGet("support-services")]
    public async Task<IActionResult> GetAvailableSupportServices()
    {
        var companyId = _httpContextAccessor?.HttpContext?.GetRequestCompanyId();
        var ipAddress = _httpContextAccessor?.HttpContext?.GetRequestIpAddress();

        var addOns = await _subscriptionPlanService.GetAvailableSupportServices(companyId, ipAddress);

        return Ok(addOns);
    }

    [AuthoriseUser]
    [HttpPost("subscribe-plans")]
    public async Task<IActionResult> SubscribePlans([FromBody] SubscribePlanRequestModel request)
    {
        var companyId = _httpContextAccessor?.HttpContext?.GetRequestCompanyId();
        var email = _httpContextAccessor?.HttpContext?.GetRequestUserEmail();

        var result = await _subscriptionPlanService.SubscribePlans(companyId, email, request);

        return Ok(result);
    }

    [AuthoriseUser]
    [HttpPost("submit-cancellation-survey")]
    public async Task<IActionResult> SubmitCancellationSurvey([FromBody] SubmitSubscriptionCancellationSurveyRequest request)
    {
        var companyId = _httpContextAccessor?.HttpContext?.GetRequestCompanyId();

        try
        {
            await _subscriptionPlanService.SubmitSubscriptionCancellationSurveyAsync(companyId, request);
            return Ok();
        }
        catch (Exception exception)
        {
            return BadRequest(new ResponseViewModel(exception.Message));
        }
    }

    [AuthoriseUser]
    [HttpGet("migration/subscription-plan-id")]
    public async Task<IActionResult> GetMigrationSubscriptionPlanId()
    {
        try
        {
            var companyId = _httpContextAccessor?.HttpContext?.GetRequestCompanyId();
            var ipAddress = _httpContextAccessor?.HttpContext?.GetRequestIpAddress();

            var subscriptionPlanId = await _subscriptionPlanService.GetMigrationPlan(companyId, ipAddress);
            var responseResult = new SubscriptionPlanMigrationResponse(subscriptionPlanId);

            return Ok(responseResult);
        }
        catch (ApplicationException exception)
        {
            return BadRequest(new ResponseViewModel(exception.Message));
        }
    }
}