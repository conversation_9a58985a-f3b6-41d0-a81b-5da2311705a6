﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AutoMapper;
using AutoMapper.QueryableExtensions;
using Hangfire;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using ShopifySharp;
using ShopifySharp.Enums;
using ShopifySharp.Filters;
using ShopifySharp.Lists;
using Telegram.Bot.Exceptions;
using Travis_backend.AccountAuthenticationDomain.Models;
using Travis_backend.AccountAuthenticationDomain.ViewModels;
using Travis_backend.Cache;
using Travis_backend.Cache.Models.CacheKeyPatterns;
using Travis_backend.CommonDomain.ViewModels;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.CompanyDomain.Services;
using Travis_backend.Constants;
using Travis_backend.ConversationDomain.ViewModels;
using Travis_backend.ConversationServices;
using Travis_backend.Database;
using Travis_backend.Enums;
using Travis_backend.Extensions;
using Travis_backend.Helpers;
using Travis_backend.IntegrationServices;
using Travis_backend.IntegrationServices.Models;
using Travis_backend.IntegrationServices.ViewModels;
using Travis_backend.ShareInvitationDomain.ViewModels;
using Travis_backend.StripeIntegrationDomain.Models;
using Travis_backend.SubscriptionPlanDomain.Constants;
using Travis_backend.SubscriptionPlanDomain.Services;
using IShopifyIntegrationService = Travis_backend.IntegrationServices.IShopifyService;

namespace Travis_backend.Controllers.ShopifyIntegrationControllers
{
    [Route("company/[controller]")]
    [Authorize]
    public class ShopifyController : Controller
    {
        private readonly ApplicationDbContext _appDbContext;
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly IMapper _mapper;
        private readonly ILogger _logger;
        private readonly IConfiguration _configuration;
        private readonly ICompanyService _companyService;
        private readonly IShopifyIntegrationService _shopifyService;
        private readonly IShopifyAuthService _shopifyAuthService;
        private readonly ICoreService _coreService;
        private readonly ICacheManagerService _cacheManagerService;
        private readonly ILockService _lockService;
        private readonly ICompanyInfoCacheService _companyInfoCacheService;
        private readonly IDefaultSubscriptionPlanIdGetter _defaultSubscriptionPlanIdGetter;
        private readonly IShopifySqlService _shopifySqlService;

        public ShopifyController(
            ApplicationDbContext appDbContext,
            UserManager<ApplicationUser> userManager,
            IMapper mapper,
            IConfiguration configuration,
            ILogger<ShopifyController> logger,
            ICompanyService companyService,
            IShopifyIntegrationService shopifyService,
            IShopifyAuthService shopifyAuthService,
            ICoreService coreService,
            ICacheManagerService cacheManagerService,
            ILockService lockService,
            ICompanyInfoCacheService companyInfoCacheService,
            IDefaultSubscriptionPlanIdGetter defaultSubscriptionPlanIdGetter,
            IShopifySqlService shopifySqlService)
        {
            _userManager = userManager;
            _appDbContext = appDbContext;
            _mapper = mapper;
            _configuration = configuration;
            _logger = logger;
            _companyService = companyService;
            _shopifyService = shopifyService;
            _shopifyAuthService = shopifyAuthService;
            _coreService = coreService;
            _cacheManagerService = cacheManagerService;
            _lockService = lockService;
            _companyInfoCacheService = companyInfoCacheService;
            _defaultSubscriptionPlanIdGetter = defaultSubscriptionPlanIdGetter;
            _shopifySqlService = shopifySqlService;
        }

        /*
        [HttpGet]
        [AllowAnonymous]
        [Route("test/redirect")]
        public async Task<IActionResult> TestRedirect()
        {
            await Task.Delay(TimeSpan.FromSeconds(5));
            return Redirect("https://the-affordable.com/collections/affordable-facewear/products/still-house");
        }

        [HttpPost]
        [AllowAnonymous]
        [Route("test")]
        public async Task<IActionResult> TestGetShopifyProduction()
        {
            var shopifyConfig = await _appDbContext.ConfigShopifyConfigs.FirstOrDefaultAsync(x => x.Id == 38);

            var collectionServices = new CustomCollectionService(shopifyConfig.UsersMyShopifyUrl, shopifyConfig.AccessToken);
            var collections = await collectionServices.ListAsync();

            var service = new ProductService(shopifyConfig.UsersMyShopifyUrl, shopifyConfig.AccessToken);

            var page = await service.ListAsync(new ProductListFilter
            {
                Limit = 250,
                CollectionId = collections.Items.FirstOrDefault().Id
            });

            var productVariant = new ProductVariantService(shopifyConfig.UsersMyShopifyUrl, shopifyConfig.AccessToken);
            var variants = await productVariant.ListAsync(page.Items.FirstOrDefault().Id.Value);

            var checkoutService = new DraftOrderService(shopifyConfig.UsersMyShopifyUrl, shopifyConfig.AccessToken);
            var draftOrder = await checkoutService.CreateAsync(new DraftOrder() {
                Email = "<EMAIL>",
                LineItems = new List<DraftLineItem>()
                {
                    new DraftLineItem()
                    {
                        VariantId = variants.Items.FirstOrDefault()?.Id,
                        Quantity = 1
                    }
                }
            });

            // var checkoutService = new CheckoutService(shopifyConfig.UsersMyShopifyUrl, shopifyConfig.AccessToken);
            // var draftOrder = await checkoutService.CreateAsync(new Checkout() {
            //     Email = "<EMAIL>"
            // });

            return Ok(draftOrder);
        } */

        /// <summary>
        /// Return a redirection URL to Shopify grant screen upon checking the authenticity of installation request.
        /// </summary>
        [HttpGet]
        [AllowAnonymous]
        [Route("install")]
        public async Task<IActionResult> Install([FromQuery] string shop)
        {
            try
            {
                var shopifySecretKey = _configuration.GetValue<String>("Shopify:ShopifySecretKey");
                var qs = HttpContext.Request.QueryString.ToString();
                if (!AuthorizationService.IsAuthenticRequest(qs, shopifySecretKey))
                    throw new InvalidOperationException();
            }
            catch (Exception)
            {
                return BadRequest(new ResponseViewModel() { message = "Shopify installation request is inauthentic." });
            }

            dynamic response = new JObject();
            string authUrl = string.Empty;

            try
            {
                if (!(await AuthorizationService.IsValidShopDomainAsync(shop)))
                    throw new InvalidOperationException();

                authUrl = BuildAuthorizationUrl(shop);
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Shopify authorization URL construction failed with {ShopUrl}: {ExceptionMessage}",
                    shop,
                    ex.Message);

                return BadRequest(new ResponseViewModel() { message = $"Shopify authorization URL construction failed: {ex.Message}." });
            }

            response.url = authUrl;
            return Ok(response);
        }

        /// <summary>
        /// Build grant screen redirection URL according to Shopify standards.
        /// </summary>
        private string BuildAuthorizationUrl(string userShopifyUrl)
        {
            var appDomainName = _configuration.GetValue<String>("Values:AppDomainName");
            string redirectUrl = $"{appDomainName}/company/shopify/redirect/url";

            var scopes = new List<AuthorizationScope>()
            {
                AuthorizationScope.ReadCustomers,
                AuthorizationScope.WriteCustomers,
                AuthorizationScope.ReadProducts,
                AuthorizationScope.ReadOrders,
                AuthorizationScope.WriteOrders,
                AuthorizationScope.ReadInventory,
                AuthorizationScope.ReadDraftOrders,
                AuthorizationScope.WriteDraftOrders,
                AuthorizationScope.WriteInventory
            };

            //You can find your API key over at https://shopify.dev/tutorials/authenticate-a-private-app-with-shopify-admin

            var shopifyApiKey = _configuration.GetValue<String>("Shopify:ShopifyApiKey");
            var authUrl = AuthorizationService.BuildAuthorizationUrl(scopes, userShopifyUrl, shopifyApiKey, redirectUrl);

            return authUrl.AbsoluteUri;
        }

        [Obsolete("Use Install method.")]
        [HttpPost]
        [AllowAnonymous]
        [Route("authorization/url")]
        public IActionResult GetAuthorizationURL([FromBody] BuildAuthorizationURL buildAuthorizationURL)
        {
            var appDomainNameV1 = _configuration.GetValue<String>("Values:AppDomainNameV1");

            // A URL to redirect the user to after they've confirmed app installation.
            // This URL is required, and must be listed in your app's settings in your Shopify app dashboard.
            // It's case-sensitive too!
            string redirectUrl = $"{appDomainNameV1}/company/shopify/redirect/url";

            // An array of the Shopify access scopes your application needs to run.
            var scopes = new List<AuthorizationScope>()
            {
                AuthorizationScope.ReadCustomers,
                AuthorizationScope.WriteCustomers,
                AuthorizationScope.ReadProducts,
                AuthorizationScope.ReadOrders,
                AuthorizationScope.WriteOrders,
                AuthorizationScope.ReadInventory,
                AuthorizationScope.ReadDraftOrders,
                AuthorizationScope.WriteDraftOrders,
                AuthorizationScope.WriteInventory,
                AuthorizationScope.WriteCheckouts
            };

            // You can find your API key over at https://shopify.dev/tutorials/authenticate-a-private-app-with-shopify-admin
            var shopifyApiKey = _configuration.GetValue<String>("Shopify:ShopifyApiKey");

            // All AuthorizationService methods are static.
            var authUrl = AuthorizationService.BuildAuthorizationUrl(
                scopes,
                buildAuthorizationURL.UsersMyShopifyUrl,
                shopifyApiKey,
                redirectUrl);

            dynamic response = new JObject();
            response.url = authUrl.AbsoluteUri;

            return Ok(response);
        }

        [HttpPost]
        [AllowAnonymous]
        [Route("integrate/public")]
        public async Task<ActionResult<ShopifyIntegratedResponse>> Integrate([FromBody] IntegrateShoplineViewModel integrateShoplineViewModel)
        {
            try
            {
                var shopifyApiKey = _configuration.GetValue<String>("Shopify:ShopifyApiKey");
                var shopifySecretKey = _configuration.GetValue<String>("Shopify:ShopifySecretKey");
                var isNewUserCreated = false;
                var newUserPassword = string.Empty;

                string accessToken = await AuthorizationService.Authorize(
                    integrateShoplineViewModel.Code,
                    integrateShoplineViewModel.Shop,
                    shopifyApiKey,
                    shopifySecretKey);

                string userId;

                if (User.Identity is { IsAuthenticated: false })
                {
                    var service = new ShopService(integrateShoplineViewModel.Shop, accessToken);
                    var shop = await service.GetAsync();
                    var integratedUser = await _shopifyAuthService.Authenticate(shop);

                    userId = integratedUser.User.Id;
                    isNewUserCreated = integratedUser.IsNewUser;

                    var existing = await _appDbContext.UserRoleStaffs.Where(x => x.IdentityId == integratedUser.User.Id)
                        .FirstOrDefaultAsync();

                    if (existing == null)
                    {
                        try
                        {
                            await _coreService.CreateCompany(
                                integratedUser.User,
                                new RegisterCompanyViewModel
                                {
                                    CompanyName = shop.Name,
                                    PhoneNumber = shop.Phone,
                                    SubscriptionPlanId = _defaultSubscriptionPlanIdGetter.GetDefaultStartupPlanId(),
                                    IsShopifyAccount = true
                                });
                        }
                        catch (Exception ex)
                        {
                            return BadRequest(
                                new ResponseViewModel
                                {
                                    message = ex.Message
                                });
                        }
                    }
                }
                else
                {
                    userId = _userManager.GetUserId(User);
                }

                var companyUser = await _appDbContext.UserRoleStaffs.Where(x => x.IdentityId == userId)
                    .FirstOrDefaultAsync();

                if (await _appDbContext.ConfigShopifyConfigs
                        .AnyAsync(
                            x =>
                                x.CompanyId != companyUser.CompanyId
                                && x.UsersMyShopifyUrl == integrateShoplineViewModel.Shop))
                {
                    return BadRequest(
                        new ResponseViewModel()
                        {
                            message = $"You have installed this shopify store in another account"
                        });
                }

                var shopifyConfig = await _appDbContext.ConfigShopifyConfigs.Where(
                        x => x.CompanyId == companyUser.CompanyId &&
                             x.UsersMyShopifyUrl == integrateShoplineViewModel.Shop)
                    .FirstOrDefaultAsync();

                // The first Shopify store that is integrated will be the billing owner
                var hasShopifyBillingOwner = await _appDbContext.ConfigShopifyConfigs.AnyAsync(x => x.CompanyId == companyUser.CompanyId && x.IsShopifyBillingOwner);

                if (shopifyConfig == null)
                {
                    var isAnyStripeAccount = await _appDbContext.ConfigStripePaymentConfigs.AnyAsync(
                        c =>
                            c.CompanyId == companyUser.CompanyId &&
                            c.Status == StripePaymentRegistrationStatus.Registered);

                    shopifyConfig = new ShopifyConfig
                    {
                        Name = integrateShoplineViewModel.Shop.Replace(".myshopify.com", string.Empty),
                        CompanyId = companyUser.CompanyId,
                        AccessToken = accessToken,
                        UsersMyShopifyUrl = integrateShoplineViewModel.Shop,
                        Type = IntegrationType.Public,
                        PaymentLinkSetting = new ShopifyPaymentLinkSetting
                        {
                            IsPaymentLinkEnabled = true,
                            PaymentLinkOption = isAnyStripeAccount
                                ? ShopifyPaymentLinkOption.StripePaymentLink
                                : ShopifyPaymentLinkOption.ShopifyCheckoutLink
                        },
                        LastUpdatedAt = DateTime.UtcNow.AddMinutes(-1),
                        IsEnabledDiscounts = await _shopifyService.IsCompanyShopifyDiscountsEnabled(companyUser.CompanyId),
                        IsShopifyBillingOwner = !hasShopifyBillingOwner
                    };
                    _appDbContext.ConfigShopifyConfigs.Add(shopifyConfig);
                }

                shopifyConfig.AccessToken = accessToken;
                await _companyService.AddECommerceFields(companyUser.CompanyId);
                await _companyService.AddLeadSource(companyUser.CompanyId, "Shopify");
                await _appDbContext.SaveChangesAsync();

                await _shopifyService.RegisterNewWebhooks(shopifyConfig.CompanyId, shopifyConfig.Id);

                var user = await _userManager.FindByIdAsync(
                    userId
                    ?? throw new InvalidOperationException(
                        $"[ShopifyController/Integrate]Integration failed with shop: {integrateShoplineViewModel.Shop}"));
                if (user is null)
                {
                    throw new Exception(
                        $"[ShopifyController/Integrate]Cannot found the created user {userId} with Shop {integrateShoplineViewModel.Shop}");
                }

                await _companyInfoCacheService.RemoveCompanyInfoCache(companyUser.CompanyId);

                return Ok(new ShopifyIntegratedResponse()
                {
                    IntegratedUser = new ShopifyIntegratedUser()
                    {
                        Id = user.Id,
                        FirstName = user.FirstName,
                        LastName = user.LastName,
                        DisplayName = user.DisplayName,
                        UserName = user.UserName,
                        Email = user.Email,
                        EmailConfirmed = user.EmailConfirmed,
                    },
                    IsNewUserCreated = isNewUserCreated
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[Shopify {MethodName} endpoint] error: {ExceptionMessage}. Payload: {Payload}",
                    nameof(Integrate),
                    ex.Message,
                    JsonConvert.SerializeObject(integrateShoplineViewModel));

                return BadRequest(new ResponseViewModel { message = ex.Message });
            }
        }

        [HttpPost]
        [Route("integrate/outofbox")]
        public async Task<IActionResult> IntegrateOutOfBox([FromBody] OutOfBoxIntegrations outOfBoxIntegrations)
        {
            try
            {
                var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

                if (!outOfBoxIntegrations.Shop.Contains("myshopify.com"))
                {
                    return BadRequest(
                        new ResponseViewModel()
                        {
                            message = "Please provide the myshopify.com"
                        });
                }

                var shopifyConfig = await _appDbContext.ConfigShopifyConfigs.Where(
                        x => x.CompanyId == companyUser.CompanyId && x.UsersMyShopifyUrl == outOfBoxIntegrations.Shop)
                    .FirstOrDefaultAsync();

                if (shopifyConfig == null)
                {
                    var shopService =
                        new ShopService(outOfBoxIntegrations.Shop, outOfBoxIntegrations.PrivateAppPassword);

                    var shop = await shopService.GetAsync();

                    var isAnyStripeAccount = await _appDbContext.ConfigStripePaymentConfigs.AnyAsync(
                        c =>
                            c.CompanyId == companyUser.CompanyId &&
                            c.Status == StripePaymentRegistrationStatus.Registered);

                    shopifyConfig = new ShopifyConfig
                    {
                        Name = outOfBoxIntegrations.Shop.Replace(".myshopify.com", string.Empty),
                        CompanyId = companyUser.CompanyId,
                        AccessToken = outOfBoxIntegrations.PrivateAppPassword,
                        UsersMyShopifyUrl = outOfBoxIntegrations.Shop,
                        Currency = shop?.Currency,
                        Type = IntegrationType.OutOfBox,
                        LastUpdatedAt = DateTime.UtcNow.AddMinutes(-1),
                        IsEnabledDiscounts =
                            await _shopifyService.IsCompanyShopifyDiscountsEnabled(companyUser.CompanyId),
                        PaymentLinkSetting = new ShopifyPaymentLinkSetting
                        {
                            IsPaymentLinkEnabled = true,
                            PaymentLinkOption = isAnyStripeAccount
                                ? ShopifyPaymentLinkOption.StripePaymentLink
                                : ShopifyPaymentLinkOption.ShopifyCheckoutLink
                        }
                    };
                    _appDbContext.ConfigShopifyConfigs.Add(shopifyConfig);
                }

                shopifyConfig.AccessToken = outOfBoxIntegrations.PrivateAppPassword;
                await _companyService.AddECommerceFields(companyUser.CompanyId);
                await _companyService.AddLeadSource(companyUser.CompanyId, "Shopify");
                await _appDbContext.SaveChangesAsync();

                await _shopifyService.RegisterNewWebhooks(shopifyConfig.CompanyId, shopifyConfig.Id);

                await _companyInfoCacheService.RemoveCompanyInfoCache(companyUser.CompanyId);

                // var cronExpressionEveryFiveMins = $"*/5 * * * *";
                // RecurringJob.AddOrUpdate<IShopifyService>(shopifyConfig.JobId, x => x.CheckOrders(companyUser.CompanyId, shopifyConfig.Id), cronExpressionEveryFiveMins);

                return Ok(_mapper.Map<ShopifyConfigResponse>(shopifyConfig));
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[Shopify {MethodName} endpoint] error: {ExceptionMessage}",
                    nameof(IntegrateOutOfBox),
                    ex.Message);

                return BadRequest(
                    new ResponseViewModel
                    {
                        message = "Unable to add Shopify"
                    });
            }
        }

        [HttpGet]
        [Route("status/{Id}")]
        public async Task<IActionResult> GetShopify(long Id)
        {
            // var companyUser = await _appDbContext.UserRoleStaffs.Where(x => x.IdentityId == userId).FirstOrDefaultAsync();
            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            var shopifyConfig = await _appDbContext.ConfigShopifyConfigs.Where(x => x.Id == Id).FirstOrDefaultAsync();

            if (shopifyConfig == null)
            {
                return BadRequest();
            }

            try
            {
                var service = new ProductService(shopifyConfig.UsersMyShopifyUrl, shopifyConfig.AccessToken);

                var page = await service.ListAsync(
                    new ProductListFilter
                    {
                        Limit = 10,
                    });

                if (shopifyConfig.Status != ShopifyStatus.Syncing)
                {
                    shopifyConfig.Status = ShopifyStatus.Connected;
                }

                await _appDbContext.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[Shopify {MethodName} endpoint] error for Shopify config Id {ShopifyConfigId}: {ExceptionMessage}",
                    nameof(GetShopify),
                    Id,
                    ex.Message);

                shopifyConfig.Status = ShopifyStatus.Disconnected;
                await _appDbContext.SaveChangesAsync();
            }

            var response = _mapper.Map<ShopifyConfigResponse>(shopifyConfig);

            return Ok(response);
        }

        /// <summary>
        /// Get the list of shopify configs.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        [HttpGet]
        [Route("status/list")]
        public async Task<IActionResult> GetShopifyConfigList(
            [FromQuery]
            int limit = 100,
            [FromQuery]
            int offset = 0)
        {
            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
            if (companyUser == null)
            {
                return Unauthorized();
            }

            var listOfShopifyConfigs = await _appDbContext.ConfigShopifyConfigs.Include(x => x.BillRecord)
                .AsNoTracking()
                .Where(x => x.CompanyId == companyUser.CompanyId && x.Status != ShopifyStatus.Disconnected).Skip(offset)
                .Take(limit).ToListAsync(HttpContext.RequestAborted);

            var response = _mapper.Map<List<ShopifyConfigResponse>>(listOfShopifyConfigs);

            return Ok(response);
        }

        /// <summary>
        /// Update shopify config - store name, the status of the catalog displayed in inbox.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        [HttpPut]
        [Route("status/{shopifyConfigId}")]
        public async Task<IActionResult> UpdateShopifyConfig(
            [FromRoute]
            long shopifyConfigId,
            [FromBody]
            UpdateShopifyConfigRequest request)
        {
            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            if (companyUser == null)
            {
                return Unauthorized();
            }

            if (string.IsNullOrEmpty(request.Name))
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        message = "Name cannot be null or empty"
                    });
            }

            var shopifyConfig = await _shopifyService.UpdateShopifyConfig(
                shopifyConfigId,
                companyUser.CompanyId,
                request);

            if (shopifyConfig == null)
            {
                return NotFound(
                    new ResponseViewModel
                    {
                        message = $"No shopify config {shopifyConfigId} found"
                    });
            }

            await _companyInfoCacheService.RemoveCompanyInfoCache(companyUser.CompanyId);
            var response = _mapper.Map<ShopifyConfigResponse>(shopifyConfig);

            return Ok(response);
        }

        /// <summary>
        /// Config the supported country for shopify config.
        /// </summary>
        /// <param name="shopifyConfigId">Targeted Shopify Config Id.</param>
        /// <param name="request">Supported Country request.</param>
        /// <returns></returns>
        [HttpPut]
        [Route("country/{shopifyConfigId}")]
        public async Task<ActionResult<ShopifyConfigResponse>> UpdateSupportedCountry(
            [FromRoute]
            long shopifyConfigId,
            [FromBody]
            UpdateSupportedCountryRequest request)
        {
            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            if (companyUser == null)
            {
                return Unauthorized();
            }

            var shopifyConfig = await _shopifyService.UpdateShopifySupportedCountry(
                shopifyConfigId,
                companyUser.CompanyId,
                request);

            if (shopifyConfig == null)
            {
                return NotFound(
                    new ResponseViewModel
                    {
                        message = $"No shopify config {shopifyConfigId} found"
                    });
            }

            await _companyInfoCacheService.RemoveCompanyInfoCache(companyUser.CompanyId);
            var response = _mapper.Map<ShopifyConfigResponse>(shopifyConfig);

            return Ok(response);
        }

        [HttpGet]
        [Route("abandoned/statistics")]
        public async Task<IActionResult> GetShopifyAbandonedCartStatistics(
            [FromQuery(Name = "from")]
            DateTime? messageFrom = null,
            [FromQuery(Name = "to")]
            DateTime? messageTo = null)
        {
            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            if (companyUser == null)
            {
                return BadRequest();
            }

            // New start record recovery data from 2022-01-01
            if ((messageFrom.HasValue && messageFrom.Value < new DateTime(2022, 1, 1)) || !messageFrom.HasValue)
            {
                messageFrom = new DateTime(2022, 1, 1);
            }

            var abandonedCartStatistics = await _appDbContext.UserProfileShopifyAbandonedCarts
                .Where(x => x.CompanyId == companyUser.CompanyId)
                .WhereIf(messageFrom.HasValue, x => x.Date > messageFrom.Value)
                .WhereIf(messageTo.HasValue, x => x.Date < messageTo.Value)
                .ToListAsync(HttpContext.RequestAborted);

            var companySalesPerformance = abandonedCartStatistics.GroupBy(x => x.Status)
                .Select(
                    x => new Attribution()
                    {
                        From = messageFrom,
                        To = messageTo,
                        Status = x.Key.ToString(),
                        TotalPrice = x.Sum(y => y.TotalPrice),
                        TotalCount = x.Count(),
                    }).ToList();

            var teamSalesPerformance = abandonedCartStatistics
                .Where(x => x.TeamId.HasValue && x.Status == ConversionStatus.InfluencedSalesBySleekFlow)
                .GroupBy(x => x.TeamId)
                .Select(
                    x => new Attribution()
                    {
                        From = messageFrom,
                        To = messageTo,
                        TeamId = x.Key.ToString(),
                        TotalPrice = x.Sum(y => y.TotalPrice),
                        TotalCount = x.Count(),
                    }).ToList();

            var response = new ShopifyPerformance
            {
                CompanyPerformance = companySalesPerformance, TeamConversionPerformance = teamSalesPerformance
            };

            return Ok(response);
        }

        [HttpGet]
        [Route("order/statistics")]
        public async Task<IActionResult> GetShopifyOrderStatistics(
            [FromQuery(Name = "from")]
            DateTime? messageFrom = null,
            [FromQuery(Name = "to")]
            DateTime? messageTo = null)
        {
            // var companyUser = await _appDbContext.UserRoleStaffs.Where(x => x.IdentityId == userId).FirstOrDefaultAsync();
            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            if (companyUser == null)
            {
                return BadRequest();
            }

            // New start record recovery data from 2022-01-01
            if ((messageFrom.HasValue && messageFrom.Value < new DateTime(2022, 1, 1)) || !messageFrom.HasValue)
            {
                messageFrom = new DateTime(2022, 1, 1);
            }

            var getShopifyOrderStatisticsCacheKeyPattern =
                new GetShopifyOrderStatisticsCacheKeyPattern(companyUser.CompanyId, messageFrom, messageTo);

            var data = await _cacheManagerService.GetAndSaveCacheAsync(
                getShopifyOrderStatisticsCacheKeyPattern,
                async () =>
                {
                    var shopifyPerformance =
                        await GetShopifyPerformanceAsync(messageFrom, messageTo, companyUser);
                    return new ShopifyPerformanceResponse
                    {
                        TargetPeriod = shopifyPerformance.TargetPeriod,
                        PreviousPeriod = shopifyPerformance.PreviousPeriod
                    };
                });
            return Ok(data);
        }

        private async Task<(
                ShopifyPerformance TargetPeriod,
                ShopifyPerformance PreviousPeriod)>
            GetShopifyPerformanceAsync(DateTime? messageFrom, DateTime? messageTo, Staff companyUser)
        {
            var days = messageFrom - messageTo;
            ShopifyPerformance targetPerformance, previousPeriod;
            var isFromRawSql = _configuration.GetValue<bool>("SqlPerformance:IsShopifyOrderStatisticsEnabled");

            if (isFromRawSql)
            {
                targetPerformance =
                    await _shopifySqlService.RetrieveShopifyPerformanceAsync(messageFrom, messageTo, companyUser);
                previousPeriod = await _shopifySqlService.RetrieveShopifyPerformanceAsync(
                    messageFrom.Value.Add(days.Value).AddDays(-1),
                    messageFrom.Value.AddDays(-1),
                    companyUser);
            }
            else
            {
                targetPerformance = await RetrieveShopifyPerformance(messageFrom, messageTo, companyUser);
                previousPeriod = await RetrieveShopifyPerformance(
                    messageFrom.Value.Add(days.Value).AddDays(-1),
                    messageFrom.Value.AddDays(-1),
                    companyUser);
            }

            return (targetPerformance, previousPeriod);
        }

        private async Task<ShopifyPerformance> RetrieveShopifyPerformance(
            DateTime? messageFrom,
            DateTime? messageTo,
            Staff companyUser)
        {
            var externalSourceShopifyOrderRecords = await _appDbContext.UserProfileShopifyOrders.Where(
                    x => x.CompanyId == companyUser.CompanyId && x.ConversionStatus != ConversionStatus.SleekPay && x.ConversionStatus != ConversionStatus.ShopifyCheckoutLink)
                .WhereIf(messageFrom.HasValue, x => x.CreatedAt > messageFrom.Value)
                .WhereIf(messageTo.HasValue, x => x.CreatedAt < messageTo.Value).ToListAsync();

            var checkoutLinkPaidShopifyOrderRecords = await _appDbContext.UserProfileShopifyOrders.Where(
                    x => x.CompanyId == companyUser.CompanyId && x.ConversionStatus == ConversionStatus.ShopifyCheckoutLink && x.Payment == "paid")
                .WhereIf(messageFrom.HasValue, x => x.CreatedAt > messageFrom.Value)
                .WhereIf(messageTo.HasValue, x => x.CreatedAt < messageTo.Value).ToListAsync();

            var stripePaidPaymentRecords = await _appDbContext.StripePaymentRecords.Where(
                    x => x.CompanyId == companyUser.CompanyId && x.Status == StripePaymentStatus.Paid)
                .WhereIf(messageFrom.HasValue, x => x.CreatedAt > messageFrom.Value)
                .WhereIf(messageTo.HasValue, x => x.CreatedAt < messageTo.Value).ToListAsync();

            var companyPerformance = externalSourceShopifyOrderRecords.GroupBy(x => x.Payment)
                .Select(
                    x => new Attribution
                    {
                        From = messageFrom,
                        To = messageTo,
                        Status = x.Key.ToString(),
                        TotalPrice = x.Sum(y => y.TotalPrice),
                        TotalCount = x.Count(),
                    }).ToList();

            companyPerformance.AddRange(
                checkoutLinkPaidShopifyOrderRecords
                    .GroupBy(x => x.Payment)
                    .Select(
                        x => new Attribution
                        {
                            From = messageFrom,
                            To = messageTo,
                            Type = "CustomLink",
                            Status = "paid",
                            TotalPrice = x.Sum(y => y.TotalPrice),
                            TotalCount = x.Count(),
                        }).ToList());

            companyPerformance.AddRange(
                stripePaidPaymentRecords.GroupBy(x => x.Status)
                    .Select(
                        x => new Attribution
                        {
                            From = messageFrom,
                            To = messageTo,
                            Type = "CustomLink",
                            Status = "paid",
                            TotalPrice = x.Sum(y => y.AmountReceived),
                            TotalCount = x.Count()
                        }));

            var companyInfluencedSalesPerformance = externalSourceShopifyOrderRecords
                .Where(r => r.ConversionStatus == ConversionStatus.InfluencedSalesBySleekFlow && r.Payment == "paid")
                .GroupBy(r => r.ConversionStatus)
                .Select(
                    x => new Attribution
                    {
                        From = messageFrom,
                        To = messageTo,
                        Status = x.Key.ToString(),
                        TotalPrice = x.Sum(y => y.TotalPrice),
                        TotalCount = x.Count(),
                    }).ToList();

            var companyConversionPerformance = new List<Attribution>();

            var customPaymentLink = stripePaidPaymentRecords.GroupBy(x => x.Status)
                .Select(
                    x => new Attribution()
                    {
                        Type = "CustomLink",
                        From = messageFrom,
                        To = messageTo,
                        Status = "ConvertedBySleekFlow",
                        TotalPrice = x.Sum(y => y.AmountReceived),
                        TotalCount = x.Count()
                    }).ToList();

            customPaymentLink.AddRange(
                checkoutLinkPaidShopifyOrderRecords
                    .GroupBy(x => x.Payment)
                    .Select(
                        x => new Attribution
                        {
                            Type = "CustomLink",
                            From = messageFrom,
                            To = messageTo,
                            Status = "ConvertedBySleekFlow",
                            TotalPrice = x.Sum(y => y.TotalPrice),
                            TotalCount = x.Count(),
                        }).ToList());

            var convertedBySleekFlow =
                companyConversionPerformance.FirstOrDefault(x => x.Status == "ConvertedBySleekFlow");

            if (customPaymentLink.Any())
            {
                if (convertedBySleekFlow != null)
                {
                    convertedBySleekFlow.TotalPrice = customPaymentLink.FirstOrDefault()!.TotalPrice;
                    convertedBySleekFlow.TotalCount = customPaymentLink.FirstOrDefault()!.TotalCount;
                }
                else
                {
                    companyConversionPerformance.AddRange(customPaymentLink);
                }
            }

            var teamConversionPerformance = new List<Attribution>();

            teamConversionPerformance.AddRange(checkoutLinkPaidShopifyOrderRecords.Where(
                    x => x.TeamId.HasValue)
                .GroupBy(x => x.TeamId)
                .Select(
                    x => new Attribution
                    {
                        Type = "CustomLink",
                        From = messageFrom,
                        To = messageTo,
                        TeamId = x.Key.ToString(),
                        Status = "paid",
                        TotalPrice = x.Sum(y => y.TotalPrice),
                        TotalCount = x.Count(),
                    }).ToList());

            teamConversionPerformance.AddRange(
                stripePaidPaymentRecords.GroupBy(x => x.TeamId)
                    .Select(
                        x => new Attribution()
                        {
                            Type = "CustomLink",
                            From = messageFrom,
                            To = messageTo,
                            TeamId = x.Key.ToString(),
                            Status = "paid",
                            TotalPrice = x.Sum(y => y.AmountReceived),
                            TotalCount = x.Count()
                        }));

            var response = new ShopifyPerformance
            {
                StartDate = messageFrom,
                EndDate = messageTo,
                CompanyPerformance = companyPerformance,
                CompanyInfluencedSalesPerformance = companyInfluencedSalesPerformance,
                CompanyConversionPerformance = companyConversionPerformance,
                TeamConversionPerformance = teamConversionPerformance
            };

            return response;
        }

        [HttpGet]
        [Authorize]
        [Route("order/staff/statistics")]
        public async Task<IActionResult> GetSalesPerformance(
            [FromQuery(Name = "from")]
            DateTime? messageFrom = null,
            [FromQuery(Name = "to")]
            DateTime? messageTo = null,
            [FromQuery(Name = "TeamId")]
            long? teamId = null,
            [FromQuery(Name = "offset")]
            int offset = 0,
            [FromQuery(Name = "limit")]
            int limit = 10,
            [FromQuery(Name = "sortBy")]
            SalesAttributionSortParams? sortParams = null,
            [FromQuery(Name = "sortOrder")]
            string sortOrder = "desc",
            [FromQuery(Name = "isIncludeSystemStatistics")]
            bool isIncludeSystemStatistics = false)
        {
            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            if (companyUser == null)
            {
                return BadRequest();
            }

            // New start record recovery data from 2022-01-01
            if ((messageFrom.HasValue && messageFrom.Value < new DateTime(2022, 1, 1)) || !messageFrom.HasValue)
            {
                messageFrom = new DateTime(2022, 1, 1);
            }

            var orderStaffStatisticsV3CacheKeyPattern = new OrderStaffStatisticsV3CacheKeyPattern(
                companyUser.CompanyId,
                offset,
                limit,
                messageFrom,
                messageTo,
                teamId,
                sortParams,
                sortOrder,
                isIncludeSystemStatistics);

            var data = await _cacheManagerService.GetCacheAsync(orderStaffStatisticsV3CacheKeyPattern);
            if (!string.IsNullOrEmpty(data))
            {
                return Ok(JsonConvert.DeserializeObject<ShopifySalesAttributionResponse>(data));
            }

            if (_configuration.GetValue<bool>("SqlPerformance:IsSalesPerformanceEnabled"))
            {
                var (count, res) = await _shopifySqlService.GetSalesPerformanceAsync(
                    companyUser,
                    messageFrom,
                    messageTo,
                    teamId,
                    offset,
                    limit,
                    sortParams,
                    sortOrder);
                var staffIds = res.Select(r => r.StaffId);
                var staffWithoutCompanies = await _appDbContext.UserRoleStaffs
                    .Where(u => staffIds.Contains(u.Id))
                    .ProjectTo<StaffWithoutCompanyResponse>(_mapper.ConfigurationProvider)
                    .ToListAsync(HttpContext.RequestAborted);
                var shopifySalesAttributionResponse = new ShopifySalesAttributionResponse
                {
                    Count = count,
                    Data = res.Select(
                        r => new ShopifyStaffPerformanceResponse
                        {
                            Staff = staffWithoutCompanies.FirstOrDefault(x => x.StaffId == r.StaffId),
                            LinkSharedCount = r.LinkSharedCount,
                            LinkSharedClicks = r.LinkSharedClicks,
                            PaymentLinkSharedCount = r.PaymentLinkSharedCount,
                            PaymentLinkSharedClicks = r.PaymentLinkSharedClicks,
                            PaymentLinkSharedPaid = r.PaymentLinkSharedPaid,
                            PaymentLinkConvertedAmount = r.PaymentLinkConvertedAmount,
                        }).ToList()
                };

                if (isIncludeSystemStatistics && offset == 0)
                {
                    var systemShareLinkGenerationRecords = await _appDbContext.CoreShareLinkGenerationRecords
                        .Where(x => x.CompanyId == companyUser.CompanyId
                               && x.StaffId == null
                               && x.CreatedAt > messageFrom.Value
                               && x.ShareLinkType != ShareLinkType.StripeRegistrationLink)
                        .WhereIf(messageTo.HasValue, x => x.CreatedAt < messageTo.Value)
                        .ProjectTo<ShareLinkGenerationRecordStatistic>(_mapper.ConfigurationProvider)
                        .ToListAsync(HttpContext.RequestAborted);

                    var systemShareLinkTrackRecords = await _appDbContext.CoreShareLinkTrackingRecords
                        .Where(
                            x => x.CompanyId == companyUser.CompanyId
                                 && x.StaffId == null
                                 && x.TrackedAt > messageFrom.Value
                                 && x.ShareLinkType != ShareLinkType.StripeRegistrationLink)
                        .WhereIf(messageTo.HasValue, x => x.TrackedAt < messageTo.Value)
                        .ProjectTo<ShareLinkTrackRecordStatistic>(_mapper.ConfigurationProvider)
                        .ToListAsync(HttpContext.RequestAborted);

                    var systemStripePaymentRecords = await _appDbContext.StripePaymentRecords
                        .Where(x => x.CompanyId == companyUser.CompanyId
                                    && x.Status == StripePaymentStatus.Paid
                                    && x.CreatedAt > messageFrom.Value
                                    && x.StaffId == null)
                        .WhereIf(messageTo.HasValue, x => x.CreatedAt < messageTo.Value)
                        .ProjectTo<StripePaymentRecordStatistic>(_mapper.ConfigurationProvider)
                        .ToListAsync(HttpContext.RequestAborted);

                    shopifySalesAttributionResponse.Data.Insert(0, new ShopifyStaffPerformanceResponse
                    {
                        Staff = new StaffWithoutCompanyResponse
                        {
                            UserInfo = new UserInfoResponse
                            {
                                DisplayName = "System"
                            }
                        },
                        LinkSharedCount = systemShareLinkGenerationRecords.Count,
                        LinkSharedClicks = systemShareLinkTrackRecords.GroupBy(x => x.TrackingId).Count(),
                        PaymentLinkSharedCount = systemShareLinkGenerationRecords.Count(
                            x => x.ShareLinkType is ShareLinkType.ShopifyDraftOrder or ShareLinkType.StripePaymentLink),
                        PaymentLinkSharedClicks = systemShareLinkTrackRecords.Count(
                            x => x.ShareLinkType is ShareLinkType.ShopifyDraftOrder or ShareLinkType.StripePaymentLink),
                        PaymentLinkSharedPaid = systemStripePaymentRecords.Count,
                        PaymentLinkConvertedAmount = systemStripePaymentRecords.Sum(x => x.AmountReceived - x.RefundedAmount)
                    });
                }

                await _cacheManagerService.SaveCacheAsync(
                    orderStaffStatisticsV3CacheKeyPattern,
                    shopifySalesAttributionResponse);
                return Ok(shopifySalesAttributionResponse);
            }

            var staffs = await _appDbContext.UserRoleStaffs
                .Where(x => x.CompanyId == companyUser.CompanyId && x.Id != 1)
                .WhereIf(
                    teamId.HasValue,
                    x => _appDbContext.CompanyTeamMembers.Where(y => y.CompanyTeamId == teamId).Select(y => y.StaffId)
                        .Contains(x.Id))
                .Include(x => x.Identity)
                .Include(x => x.ProfilePicture)
                .OrderBy(x => x.Order)
                .ThenBy(x => x.Id)
                .ProjectTo<StaffWithoutCompanyResponse>(_mapper.ConfigurationProvider)
                .ToListAsync(HttpContext.RequestAborted);

            var companyGeneratedLink = await _appDbContext.CoreShareLinkGenerationRecords
                .Where(x => x.CompanyId == companyUser.CompanyId)
                .WhereIf(messageFrom.HasValue, x => x.CreatedAt > messageFrom.Value)
                .WhereIf(messageTo.HasValue, x => x.CreatedAt < messageTo.Value)
                .ProjectTo<ShareLinkGenerationRecordStatistic>(_mapper.ConfigurationProvider)
                .ToListAsync(HttpContext.RequestAborted);

            var companyTrackingRecord = await _appDbContext.CoreShareLinkTrackingRecords
                .Where(x => x.CompanyId == companyUser.CompanyId)
                .WhereIf(messageFrom.HasValue, x => x.TrackedAt > messageFrom.Value)
                .WhereIf(messageTo.HasValue, x => x.TrackedAt < messageTo.Value)
                .ProjectTo<ShareLinkTrackRecordStatistic>(_mapper.ConfigurationProvider)
                .ToListAsync(HttpContext.RequestAborted);

            var companyShopifyOrderRecords = await _appDbContext.UserProfileShopifyOrders
                .Where(
                    x => x.CompanyId == companyUser.CompanyId && x.Payment == "paid" &&
                         x.ConversionStatus != ConversionStatus.SleekPay)
                .WhereIf(messageFrom.HasValue, x => x.CreatedAt > messageFrom.Value)
                .WhereIf(messageTo.HasValue, x => x.CreatedAt < messageTo.Value)
                .ProjectTo<ShopifyOrderRecordStatistic>(_mapper.ConfigurationProvider)
                .ToListAsync(HttpContext.RequestAborted);

            var companyCustomLink = await _appDbContext.StripePaymentRecords
                .Where(x => x.CompanyId == companyUser.CompanyId && x.Status == StripePaymentStatus.Paid)
                .WhereIf(messageFrom.HasValue, x => x.CreatedAt > messageFrom.Value)
                .WhereIf(messageTo.HasValue, x => x.CreatedAt < messageTo.Value)
                .ProjectTo<StripePaymentRecordStatistic>(_mapper.ConfigurationProvider)
                .ToListAsync(HttpContext.RequestAborted);

            var response = new ShopifySalesAttributionResponse();

            if (isIncludeSystemStatistics && offset == 0)
            {
                var systemGenerationRecords = companyGeneratedLink.Where(
                        x => x.StaffId == null && x.ShareLinkType != ShareLinkType.StripeRegistrationLink)
                    .ToList();
                var systemTrackingRecords = companyTrackingRecord.Where(
                        x => x.StaffId == null && x.ShareLinkType != ShareLinkType.StripeRegistrationLink)
                    .ToList();
                var systemCustomLink = companyCustomLink.Where(x => x.StaffId == null).ToList();

                var systemResult = new ShopifyStaffPerformanceResponse
                {
                    Staff = new StaffWithoutCompanyResponse
                    {
                        UserInfo = new UserInfoResponse
                        {
                            DisplayName = "System"
                        }
                    },
                    LinkSharedCount = systemGenerationRecords.Count,
                    LinkSharedClicks = systemTrackingRecords.GroupBy(x => x.TrackingId).Count(),
                    PaymentLinkSharedCount = systemGenerationRecords.Count(
                        x => x.ShareLinkType is ShareLinkType.ShopifyDraftOrder or ShareLinkType.StripePaymentLink),
                    PaymentLinkSharedClicks = systemTrackingRecords.Count(
                        x => x.ShareLinkType is ShareLinkType.ShopifyDraftOrder or ShareLinkType.StripePaymentLink),
                    PaymentLinkSharedPaid = systemCustomLink.Count,
                    PaymentLinkConvertedAmount = systemCustomLink.Sum(x => x.AmountReceived - x.RefundedAmount)
                };

                response.Data.Add(systemResult);
            }

            foreach (var staff in staffs)
            {
                var staffResult = new ShopifyStaffPerformanceResponse();

                staffResult.Staff = staff;

                var staffGenerationRecords = companyGeneratedLink.Where(
                        x => x.StaffId == staff.StaffId && x.ShareLinkType != ShareLinkType.StripeRegistrationLink)
                    .ToList();
                var staffTrackingRecords = companyTrackingRecord.Where(
                        x => x.StaffId == staff.StaffId && x.ShareLinkType != ShareLinkType.StripeRegistrationLink)
                    .ToList();
                var staffShopifyOrderRecords =
                    companyShopifyOrderRecords.Where(x => x.StaffId == staff.StaffId && x.ConversionStatus == ConversionStatus.ShopifyCheckoutLink).ToList();
                var staffCustomLink = companyCustomLink.Where(x => x.StaffId == staff.StaffId).ToList();

                staffResult.LinkSharedCount = staffGenerationRecords.Count();
                staffResult.LinkSharedClicks = staffTrackingRecords.GroupBy(x => x.TrackingId).Count();
                staffResult.PaymentLinkSharedCount = staffGenerationRecords.Count(
                    x => x.ShareLinkType is ShareLinkType.ShopifyDraftOrder or ShareLinkType.StripePaymentLink);
                staffResult.PaymentLinkSharedClicks = staffTrackingRecords.Count(
                    x => x.ShareLinkType is ShareLinkType.ShopifyDraftOrder or ShareLinkType.StripePaymentLink);
                staffResult.PaymentLinkSharedPaid = staffShopifyOrderRecords.Count() + staffCustomLink.Count();
                staffResult.PaymentLinkConvertedAmount = staffShopifyOrderRecords.Sum(x => x.TotalPrice) +
                                                         staffCustomLink.Sum(x => x.AmountReceived - x.RefundedAmount);
                // staffResult.CustomPaymentLinkSharedCount = await customLinkStatistics.CountAsync();
                // staffResult.CustomPaymentLinkSharedAmount = await customLinkStatistics.SumAsync(x => x.AmountReceived);

                response.Data.Add(staffResult);
            }

            switch (sortParams)
            {
                case SalesAttributionSortParams.LinkSharedCount:
                    response.Data = sortOrder == "desc"
                        ? response.Data.OrderByDescending(x => x.LinkSharedCount).ToList()
                        : response.Data.OrderBy(x => x.LinkSharedCount).ToList();
                    break;
                case SalesAttributionSortParams.PaymentLinkConvertedAmount:
                    response.Data = sortOrder == "desc"
                        ? response.Data.OrderByDescending(x => x.PaymentLinkConvertedAmount).ToList()
                        : response.Data.OrderBy(x => x.PaymentLinkConvertedAmount).ToList();
                    break;
                case SalesAttributionSortParams.PaymentLinkSharedCount:
                    response.Data = sortOrder == "desc"
                        ? response.Data.OrderByDescending(x => x.PaymentLinkSharedCount).ToList()
                        : response.Data.OrderBy(x => x.PaymentLinkSharedCount).ToList();
                    break;
            }

            response.Count = response.Data.Count;
            response.Data = response.Data.Skip(offset).Take(limit).ToList();

            await _cacheManagerService.SaveCacheAsync(orderStaffStatisticsV3CacheKeyPattern, response);

            return Ok(response);
        }

        [HttpGet]
        [Route("usage/{Id}")]
        public async Task<ActionResult<ShopifyUsageResponse>> GetShopifyUsage(long Id)
        {
            // var companyUser = await _appDbContext.UserRoleStaffs.Where(x => x.IdentityId == userId).FirstOrDefaultAsync();
            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            var shopifyConfig = await _appDbContext.ConfigShopifyConfigs.Where(x => x.Id == Id).FirstOrDefaultAsync();

            if (shopifyConfig == null)
            {
                return BadRequest();
            }

            try
            {
                var orderService = new OrderService(shopifyConfig.UsersMyShopifyUrl, shopifyConfig.AccessToken);
                var customerService = new CustomerService(shopifyConfig.UsersMyShopifyUrl, shopifyConfig.AccessToken);

                var response = new ShopifyUsageResponse();
                response.OrderCount = await orderService.CountAsync(
                    new OrderCountFilter
                    {
                        Status = "any"
                    });
                response.CustomerCount = await customerService.CountAsync();

                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[Shopify {MethodName} endpoint] error for Shopify config Id {ShopifyConfigId}: {ExceptionMessage}",
                    nameof(GetShopifyUsage),
                    Id,
                    ex.Message);


                shopifyConfig.Status = ShopifyStatus.Disconnected;
                await _appDbContext.SaveChangesAsync();

                return BadRequest(
                    new ResponseViewModel
                    {
                        message = ex.Message
                    });
            }
        }

        [HttpPost]
        [Route("sync/{Id}")]
        public async Task<ActionResult<ResponseViewModel>> SyncShopify(long Id)
        {
            // var companyUser = await _appDbContext.UserRoleStaffs.Where(x => x.IdentityId == userId).FirstOrDefaultAsync();
            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            var key = $"shopify_sync_{Id}";
            var myLock = await _lockService.AcquireLockAsync(key, TimeSpan.FromSeconds(20));
            if (myLock == null)
            {
                return Ok(
                    new ResponseViewModel
                    {
                        message = "syncing"
                    });
            }

            var shopifyConfig = await _appDbContext.ConfigShopifyConfigs
                .Where(x => x.CompanyId == companyUser.CompanyId && x.Id == Id).FirstOrDefaultAsync();

            if (shopifyConfig == null)
            {
                return BadRequest();
            }

            BackgroundJob.Enqueue<IShopifyIntegrationService>(x => x.SyncShopify(shopifyConfig.CompanyId, shopifyConfig.Id));

            return Ok(
                new ResponseViewModel
                {
                    message = "syncing"
                });
        }

        [HttpPost]
        [Route("sync/setting/{Id}")]
        public async Task<ActionResult<ResponseViewModel>> SyncShopifyWithOptions(
            long Id,
            [FromBody]
            ShopifySyncSetting shopifySyncSetting = null)
        {
            // var companyUser = await _appDbContext.UserRoleStaffs.Where(x => x.IdentityId == userId).FirstOrDefaultAsync();
            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            var shopifyConfig = await _appDbContext.ConfigShopifyConfigs
                .Where(x => x.CompanyId == companyUser.CompanyId && x.Id == Id).FirstOrDefaultAsync();

            if (shopifyConfig == null)
            {
                return BadRequest();
            }

            if (shopifySyncSetting != null && shopifySyncSetting.SyncAllStatus.HasValue)
            {
                shopifyConfig.SyncAllStatus = shopifySyncSetting.SyncAllStatus.Value;
                shopifyConfig.SyncOnlyIfPhoneNumberExist = shopifySyncSetting.SyncOnlyIfPhoneNumberExist.Value;
                shopifyConfig.SyncOrderTags = shopifySyncSetting.SyncOrderTags.Value;
                shopifyConfig.SyncProductTags = shopifySyncSetting.SyncProductTags.Value;
                shopifyConfig.SyncCustomerTags = shopifySyncSetting.SyncCustomerTags.Value;
                await _appDbContext.SaveChangesAsync();
            }

            BackgroundJob.Enqueue<IShopifyIntegrationService>(x => x.SyncShopify(shopifyConfig.CompanyId, shopifyConfig.Id));

            return Ok(
                new ResponseViewModel
                {
                    message = "syncing"
                });
        }

        [HttpGet]
        [Route("webhook/{Id}")]
        public async Task<IActionResult> GetWebhook(long Id)
        {
            // var companyUser = await _appDbContext.UserRoleStaffs.Where(x => x.IdentityId == userId).FirstOrDefaultAsync();
            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            var shopifyConfig = await _appDbContext.ConfigShopifyConfigs
                .Where(x => x.CompanyId == companyUser.CompanyId && x.Id == Id).FirstOrDefaultAsync();

            if (shopifyConfig != null)
            {
                return Ok(await _shopifyService.ListWebhooks(shopifyConfig.CompanyId, shopifyConfig.Id));
            }

            return BadRequest();
        }

        [HttpDelete]
        [Route("delete/{Id}")]
        public async Task<IActionResult> DeleteShopify(long Id)
        {
            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            var shopifyConfigs = await _appDbContext.ConfigShopifyConfigs
                .Where(x => x.CompanyId == companyUser.CompanyId)
                .AsNoTracking()
                .ToListAsync();

            var shopifyConfig = shopifyConfigs
                .FirstOrDefault(x => x.Id == Id);

            if (shopifyConfig is null)
                return BadRequest(new ResponseViewModel {message = $"Cannot find Shopify config with ID: {Id}"});

            // Cannot delete the billing owner when there are other integrated shops
            if (shopifyConfig.IsShopifyBillingOwner && shopifyConfigs.Where(x => x.Id != Id).Any())
                return BadRequest(new ResponseViewModel {message = "You must nominate a new billing owner when you have other integrated shops"});

            // SleekFlow deletes app
            await _shopifyService.AppUninstalled(shopifyConfig.CompanyId, shopifyConfig.Id);

            _logger.LogInformation(
                "Run ShopifyService.AppUninstalled in /company/Shopify/delete/{Id} for company {CompanyId}",
                Id,
                companyUser.CompanyId);

            // Shopify deletes app
            try
            {
                var service = new ShopService(shopifyConfig.UsersMyShopifyUrl, shopifyConfig.AccessToken);
                var shop = await service.GetAsync();

                if (shop == null)
                    throw new ShopifyException($"Cannot retrieve Shopify shop {shopifyConfig.UsersMyShopifyUrl} with the given access token");

                await service.UninstallAppAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Unable to uninstall Shopify app {ShopifyUrl} with an API call: {ExceptionMessage}",
                    shopifyConfig.UsersMyShopifyUrl,
                    ex.Message);

                return BadRequest(new ResponseViewModel {message = "Shopify is having a problem uninstalling the app"});
            }

            return Ok(
                    new ResponseViewModel
                    {
                        message = "success"
                    });
        }

        [HttpGet]
        [AllowAnonymous]
        [Route("redirect/url")]
        public IActionResult Redirect(
            [FromQuery(Name = "code")]
            string code,
            [FromQuery(Name = "shop")]
            string myShopifyUrl)
        {
            try
            {
                dynamic response = new JObject();
                response.code = code;
                response.shop = myShopifyUrl;

                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[Shopify redirect] Code: {Code}, Retrieve account info error: {ExceptionString}",
                    code,
                    ex.ToString());

                return BadRequest(ex.Message);
            }
        }

        [HttpPost]
        [AllowAnonymous]
        [Route("outofbox/order")]
        public async Task<IActionResult> OutOfBox([FromBody] OutOfBoxIntegrations ofBoxIntegrations)
        {
            try
            {
                var service = new ProductService(ofBoxIntegrations.Shop, ofBoxIntegrations.PrivateAppPassword);

                var page = await service.ListAsync(
                    new ProductListFilter
                    {
                        Limit = 250,
                    });

                return Ok(page.Items);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        [HttpPost]
        [Obsolete("Splitting shopify order create/update webhook endpoint")]
        [AllowAnonymous]
        [Route("webhook/{companyId}/shopify/{shopifyId}/orders")]
        public async Task<IActionResult> OrderUpdatedWebhook(
            string companyId,
            long shopifyId,
            [FromBody]
            ShopifySharp.Order order)
        {
            try
            {
                // I find that so many identical payload will send multiple time
                // So try to avoid them
                var shopifyPayloadChecksum = SHA256Helper.sha256_hash(JsonConvert.SerializeObject(order));
                if (await _lockService.AcquireLockAsync(
                        $"ShopifyOrderPayload:{shopifyId}:{shopifyPayloadChecksum}",
                        TimeSpan.FromMinutes(30)) == null)
                {
                    return Ok();
                }

                _logger.LogInformation(
                    "Shopify payload: {CompanyId} {OrderUpdatedWebhookPayload}",
                    companyId,
                    JsonConvert.SerializeObject(order));

                var isOrderRecorded = await _appDbContext.UserProfileShopifyOrders.AnyAsync(o => o.OrderId == order.Id);

                BackgroundJob.Enqueue<IShopifyIntegrationService>(
                    x => x.UpsertOrders(
                        companyId,
                        shopifyId,
                        order,
                        !isOrderRecorded &&
                        (order.UpdatedAt == null ||
                         Math.Abs((order.UpdatedAt.Value - order.CreatedAt.Value).Seconds) <= 2) &&
                        (order.ClosedAt == null ||
                         Math.Abs((order.ClosedAt.Value - order.CreatedAt.Value).Seconds) <= 2) &&
                        (order.CancelledAt == null ||
                         Math.Abs((order.CancelledAt.Value - order.CreatedAt.Value).Seconds) <= 2),
                        true));

                return Ok();
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[Deprecated] Company {CompanyId} Shopify order updated webhook error: {ExceptionString}. Payload: {Payload}",
                    companyId,
                    ex.ToString(),
                    JsonConvert.SerializeObject(order));

                return Ok(ex.Message);
            }
        }

        [HttpPost]
        [AllowAnonymous]
        [Route("webhook/{companyId}/shopify/{shopifyId:long}/orders/create")]
        public async Task<IActionResult> WebhookOrderCreated(
            [FromRoute]
            string companyId,
            [FromRoute]
            long shopifyId,
            [FromBody]
            ShopifySharp.Order order)
        {
            try
            {
                _logger.LogInformation(
                    "Shopify order/create company {CompanyId}, payload: {@ShopifyOrderCreatedPayload}",
                    companyId,
                    order);

                string shopifyPayloadChecksum = SHA256Helper.sha256_hash(JsonConvert.SerializeObject(order));

                // Shopify might send identical payload.
                // Do not process identical payload within the 30 minutes timeframe
                var createOrderLock = await _lockService.AcquireLockAsync(
                    $"ShopifyCreateOrderPayload:{shopifyId}:{order.Id}:{order.OrderNumber}:{shopifyPayloadChecksum}",
                    TimeSpan.FromMinutes(30));

                if (createOrderLock is null)
                {
                    return Ok();
                }

                string taskId = BackgroundJob.Enqueue<IShopifyIntegrationService>(
                    svc => svc.UpsertOrders(
                        companyId,
                        shopifyId,
                        order,
                        true,
                        true));

                _logger.LogInformation(
                    "Shopify order/create company {CompanyId} order {OrderNo} queued for processing",
                    companyId,
                    order.Id);

                return Ok(
                    new
                    {
                        taskId
                    });
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    "Shopify order/create webhook error: {@ShopifyWebhookOrderCreatedException}, payload: {@ShopifyOrderCreatedPayload}",
                    ex,
                    order);

                return Ok(ex.Message);
            }
        }

        [HttpPost]
        [AllowAnonymous]
        [Route("webhook/{companyId}/shopify/{shopifyId:long}/orders/update")]
        public async Task<IActionResult> WebhookOrderUpdated(
            [FromRoute]
            string companyId,
            [FromRoute]
            long shopifyId,
            [FromBody]
            ShopifySharp.Order order)
        {
            try
            {
                _logger.LogInformation(
                    "Shopify order/update company {CompanyId}, payload: {@ShopifyOrderUpdatedPayload}",
                    companyId,
                    order);

                string shopifyPayloadChecksum = SHA256Helper.sha256_hash(JsonConvert.SerializeObject(order));

                // Shopify might send identical payload.
                // Do not process identical payload within the 15 seconds timeframe
                var updateOrderLock = await _lockService.AcquireLockAsync(
                    $"ShopifyUpdateOrderPayload:{shopifyId}:{order.Id}:{order.OrderNumber}:{shopifyPayloadChecksum}",
                    TimeSpan.FromSeconds(15));

                if (updateOrderLock is null)
                {
                    return Ok();
                }

                string taskId = BackgroundJob.Enqueue<IShopifyIntegrationService>(
                    svc => svc.UpsertOrders(
                        companyId,
                        shopifyId,
                        order,
                        false,
                        true));

                _logger.LogInformation(
                    "Shopify order/update company {CompanyId} order {OrderNo} queued for processing",
                    companyId,
                    order.Id);

                return Ok(
                    new
                    {
                        taskId
                    });
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    "Shopify order/update webhook error: {@ShopifyWebhookOrderUpdatedException}, payload: {@ShopifyOrderUpdatedPayload}",
                    ex,
                    order);

                return Ok(ex.Message);
            }
        }

        /// <summary>
        /// Register new webhooks.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        [ApiExplorerSettings(IgnoreApi = true)]
        [HttpPut]
        [Authorize]
        [Route("webhook/{companyId}/shopify/{shopifyId:long}/register-new-webhooks")]
        public async Task<IActionResult> RegisterNewWebhooks(
            [FromRoute]
            string companyId,
            [FromRoute]
            long shopifyId)
        {
            try
            {
                await _shopifyService.RegisterNewWebhooks(companyId, shopifyId);

                ListResult<ShopifySharp.Webhook> registeredWebhooks =
                    await _shopifyService.ListWebhooks(companyId, shopifyId);

                return Ok(registeredWebhooks);
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    "Register new shopify webhooks failed with exception: {@RegisterShopifyNewWebhooksException}",
                    ex);

                return StatusCode(StatusCodes.Status500InternalServerError, ex.Message);
            }
        }

        /// <summary>
        /// Register old webhooks (rollback).
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        [ApiExplorerSettings(IgnoreApi = true)]
        [HttpPut]
        [Authorize]
        [Route("webhook/{companyId}/shopify/{shopifyId:long}/rollback-old-webhooks")]
        public async Task<IActionResult> FallbackToOldWebhooks(
            [FromRoute]
            string companyId,
            [FromRoute]
            long shopifyId)
        {
            try
            {
                await _shopifyService.RegisterWebhook(companyId, shopifyId);

                ListResult<ShopifySharp.Webhook> registeredWebhooks =
                    await _shopifyService.ListWebhooks(companyId, shopifyId);

                return Ok(registeredWebhooks);
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    "Rollback shopify webhooks failed with exception: {@RollbackShopifyWebhooksException}",
                    ex);

                return StatusCode(StatusCodes.Status500InternalServerError, ex.Message);
            }
        }

        [ApiExplorerSettings(IgnoreApi = true)]
        [HttpPut]
        [Authorize]
        [Route("webhook/shopify/patch-new-webhooks")]
        public async Task<IActionResult> PatchAllShopifyIntegrationToNewWebHooks()
        {
            try
            {
                Dictionary<string, List<string>> results = await _shopifyService.PatchNewWebhookForAllCompanies();

                return Ok(
                    new
                    {
                        count = results.Count, data = results
                    });
            }
            catch (Exception ex)
            {
                _logger.LogError("Error when patching new Shopify webhooks for all companies: {@Exception}", ex);

                return StatusCode(StatusCodes.Status500InternalServerError, ex.Message);
            }
        }

        [ApiExplorerSettings(IgnoreApi = true)]
        [HttpPut]
        [Authorize]
        [Route("webhook/shopify/rollback-webhooks")]
        public async Task<IActionResult> RollbackAllShopifyIntegrationToOldWebHooks()
        {
            try
            {
                Dictionary<string, List<string>> results = await _shopifyService.RollbackWebhookForAllCompanies();

                return Ok(
                    new
                    {
                        count = results.Count, data = results
                    });
            }
            catch (Exception ex)
            {
                _logger.LogError("Error when rolling back Shopify webhooks for all companies: {@Exception}", ex);

                return StatusCode(StatusCodes.Status500InternalServerError, ex.Message);
            }
        }

        [HttpPost]
        [AllowAnonymous]
        [Route("webhook/{companyId}/shopify/{shopifyId:long}/customers")]
        public async Task<IActionResult> WebhookCustomers(
            string companyId,
            long shopifyId,
            [FromBody]
            ShopifySharp.Customer customer,
            [FromQuery(Name = "topic")]
            string topic = "update")
        {
            try
            {
                // I find that so many identical payload will send multiple time
                // So try to avoid them
                var shopifyPayloadChecksum = SHA256Helper.sha256_hash(JsonConvert.SerializeObject(customer));
                if (await _lockService.AcquireLockAsync(
                        $"ShopifyOrderPayload:{shopifyId}:{shopifyPayloadChecksum}",
                        TimeSpan.FromMinutes(30)) == null)
                {
                    return Ok();
                }

                _logger.LogInformation(
                    "Shopify customer webhook payload for company {CompanyId}: {Payload}",
                    companyId,
                    JsonConvert.SerializeObject(customer));

                var shopifyTrigger = (topic == "create")
                    ? AutomationType.ShopifyNewCustomerTrigger
                    : AutomationType.ShopifyUpdatedCustomerTrigger;
                BackgroundJob.Enqueue<IShopifyIntegrationService>(
                    x => x.CustomerUpdated(companyId, shopifyId, customer, shopifyTrigger));

                return Ok();
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Company {CompanyId} Shopify customer webhook error: {ExceptionMessage}. Payload: {Payload}",
                    companyId,
                    ex.Message,
                    JsonConvert.SerializeObject(customer));

                return Ok(ex.Message);
            }
        }

        [HttpPost]
        [AllowAnonymous]
        [Route("webhook/{companyId}/shopify/{shopifyId}/uninstalled")]
        public IActionResult WebhookAppUninstalled(string companyId, long shopifyId)
        {
            try
            {
                BackgroundJob.Enqueue<IShopifyIntegrationService>(x => x.AppUninstalled(companyId, shopifyId));

                _logger.LogInformation(
                    "Run ShopifyService.AppUninstalled in /company/Shopify/webhook/{CompanyId}/shopify/{ShopifyId}/uninstalled",
                    companyId,
                    shopifyId);

                return Ok();
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Company {CompanyId} config {ShopifyConfigId} Shopify app uninstalled webhook error: {ExceptionMessage}",
                    companyId,
                    shopifyId,
                    ex.Message);

                return Ok(ex.Message);
            }
        }

        [HttpPost]
        [Route("discount-setting")]
        public async Task<IActionResult> UpdateCompanyShopifyDiscountSetting(
            [FromBody]
            UpdateShopifyDiscountSettingRequest request)
        {
            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            if (companyUser == null)
            {
                return Unauthorized();
            }

            try
            {
                var shopifyConfigs =
                    await _shopifyService.UpdateCompanyShopifyDiscountSetting(
                        companyUser.CompanyId,
                        request.IsEnabledDiscounts);

                var response = new UpdateShopifyDiscountSettingResponse
                {
                    ShopifyDiscountSettings = _mapper.Map<List<ShopifyDiscountSetting>>(shopifyConfigs)
                };

                return Ok(response);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        [HttpGet]
        [Route("discount-setting")]
        public async Task<IActionResult> GetCompanyShopifyDiscountSetting()
        {
            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            if (companyUser == null)
            {
                return Unauthorized();
            }

            try
            {
                var response = new ShopifyDiscountSettingResponse
                {
                    IsEnabledDiscounts = await _shopifyService.IsCompanyShopifyDiscountsEnabled(companyUser.CompanyId)
                };

                return Ok(response);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        [HttpPost]
        [Route("product-message-template")]
        public async Task<IActionResult> CreateCompanyShopifyProductMessageTemplate(
            [FromBody]
            CreateShopifyProductMessageTemplateRequest request)
        {
            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            if (companyUser == null)
            {
                return Unauthorized();
            }

            try
            {
                await _shopifyService.CreateShopifyProductMessageTemplateAsync(
                    request.ShopifyConfigId,
                    companyUser.CompanyId,
                    request.MessageBody,
                    request.Params);

                return Ok(
                    new ResponseViewModel
                    {
                        message = "Template created"
                    });
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        [HttpPut]
        [Route("product-message-template")]
        public async Task<IActionResult> UpdateCompanyShopifyProductMessageTemplate(
            [FromBody]
            UpdateShopifyProductMessageTemplateRequest request)
        {
            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            if (companyUser == null)
            {
                return Unauthorized();
            }

            try
            {
                await _shopifyService.UpdateShopifyProductMessageTemplateAsync(
                    request.ShopifyProductMessageTemplateId,
                    companyUser.CompanyId,
                    request.MessageBody,
                    request.Params);

                return Ok(
                    new ResponseViewModel
                    {
                        message = "Template updated"
                    });
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        [HttpGet]
        [Route("product-message-templates")]
        public async Task<IActionResult> GetCompanyShopifyProductMessageTemplates([FromQuery] long shopifyConfigId)
        {
            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            if (companyUser == null)
            {
                return Unauthorized();
            }

            try
            {
                return Ok(
                    _mapper.Map<List<ShopifyProductMessageTemplateResponse>>(
                        await _shopifyService.GetShopifyProductMessageTemplatesAsync(
                            shopifyConfigId,
                            companyUser.CompanyId)));
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        [HttpGet]
        [Route("stripe-connection-status/{shopifyId}")]
        public async Task<IActionResult> GetShopifyStripeConnectionStatus(long shopifyId)
        {
            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            if (companyUser == null)
            {
                return Unauthorized();
            }

            try
            {
                return Ok(
                    new ShopifyStripeConnectionStatusResponse
                    {
                        StripeCurrencyConnectionStatuses = await _shopifyService.GetShopifyStripeConnectionStatusAsync(
                            shopifyId,
                            companyUser.CompanyId)
                    });
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Error getting shopify stripe connection status for Shopify config {ShopifyConfigId}: {ExceptionMessage}",
                    shopifyId,
                    ex.Message);

                return BadRequest(ex.Message);
            }
        }

        // [HttpPost]
        // [AllowAnonymous]
        // [Route("webhook/{companyId}/shopify/{shopifyId}/draftorders")]
        // public async Task<IActionResult> WebhookDraftOrders(string companyId, long shopifyId, [FromBody] ShopifySharp.DraftOrder draftOrder)
        // {
        //    try
        //    {
        //        _logger.LogError($"Shopify payload: {JsonConvert.SerializeObject(draftOrder)}");
        //        await _shopifyService.DraftOrderUpdated(companyId, shopifyId, draftOrder);

        // return Ok();
        //    }
        //    catch (Exception ex)
        //    {
        //        _logger.LogError($"Shopify Webhook Error: {ex.ToString()}\n{JsonConvert.SerializeObject(draftOrder)}");
        //        return Ok(ex.Message);
        //    }
        //}

        [HttpGet]
        [Route("billing-owner")]
        public async Task<IActionResult> GetBillingOwner()
        {
            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
            if (companyUser == null)
            {
                return Unauthorized();
            }

            var shopifyConfig = await _appDbContext.ConfigShopifyConfigs
                .Where(x => x.CompanyId == companyUser.CompanyId && x.IsShopifyBillingOwner)
                .FirstOrDefaultAsync();

            return Ok(new ShopifyBillingOwnerResponse {ShopifyConfigId = shopifyConfig?.Id});
        }

        [HttpPost]
        [Route("billing-owner/{shopifyId}")]
        public async Task<IActionResult> UpdateBillingOwner(long shopifyId)
        {
            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
            if (companyUser == null)
            {
                return Unauthorized();
            }

            var shopifyConfigs = await _appDbContext.ConfigShopifyConfigs
                .Where(x => x.CompanyId == companyUser.CompanyId)
                .ToListAsync();

            var newBillingOwner = shopifyConfigs
                .Where(x => x.Id == shopifyId)
                .FirstOrDefault();

            if (newBillingOwner is null)
                return BadRequest(new ResponseViewModel {message = "Cannot find Shopify config with ID: " + shopifyId});

            var currentBillingOwner = shopifyConfigs
                .Where(x => x.IsShopifyBillingOwner)
                .FirstOrDefault();

            // The user may have uninstalled the app belonging to a billing owner account from the Shopify console
            if (currentBillingOwner is not null)
            {
                if (currentBillingOwner.Id == newBillingOwner.Id)
                    return Ok(new ShopifyBillingOwnerResponse {ShopifyConfigId = shopifyId});

                currentBillingOwner.IsShopifyBillingOwner = false;
            }

            newBillingOwner.IsShopifyBillingOwner = true;

            await _appDbContext.SaveChangesAsync();
            return Ok(new ShopifyBillingOwnerResponse {ShopifyConfigId = newBillingOwner.Id});
        }
    }
}