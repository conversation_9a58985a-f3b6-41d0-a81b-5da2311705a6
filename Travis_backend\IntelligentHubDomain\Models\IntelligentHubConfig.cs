﻿using System.Collections.Generic;
using System.Collections.Immutable;
using Newtonsoft.Json;

namespace Travis_backend.IntelligentHubDomain.Models;

public class IntelligentHubConfig
{
    public static readonly ImmutableList<string> PriceableFeatureIds = new List<string>
    {
        "ai_features_total_usage"
    }.ToImmutableList();

    [JsonProperty("usage_limits")]
    public Dictionary<string, int> UsageLimits { get; set; }

    [JsonConstructor]
    public IntelligentHubConfig(Dictionary<string, int> usageLimits)
    {
        UsageLimits = usageLimits;
    }
}