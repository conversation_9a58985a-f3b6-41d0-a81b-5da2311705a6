using Pulumi;
using Pulumi.AzureNative.Resources;
using Sleekflow.Core.Infra.Components.Configs;
using Sleekflow.Core.Infra.Components.Models;
using Sleekflow.Core.Infra.Constants;
using Sleekflow.Core.Infra.Utils;
using Web = Pulumi.AzureNative.Web;
using Network = Pulumi.AzureNative.Network;
using Insights = Pulumi.AzureNative.Insights;

namespace Sleekflow.Core.Infra.Components.SleekflowCore;

public class SleekflowCore
{
    private readonly MyConfig _myConfig;
    private readonly List<EnvGroup> _envGroups;
    private readonly ServerConfig _serverConfig;
    private readonly ContainerRegistryOutput _containerRegistryOutput;

    public SleekflowCore(
        MyConfig myConfig,
        List<EnvGroup> envGroups,
        ServerConfig serverConfig,
        ContainerRegistryOutput containerRegistryOutput)
    {
        _myConfig = myConfig;
        _envGroups = envGroups;
        _serverConfig = serverConfig;
        _containerRegistryOutput = containerRegistryOutput;
    }

    public void InitSleekflowCore()
    {
        var imageName = Output.Format(
            $"{_containerRegistryOutput.Registry.LoginServer}/{ServiceNames.GetSleekflowPrefixedShortName(ServiceNames.SleekflowCore)}:{_myConfig.BuildTime}");
        var image = new DockerImages(
            ServiceNames.GetSleekflowPrefixedShortName(ServiceNames.SleekflowCore),
            imageName,
            _containerRegistryOutput
        ).InitDockerImages();

        foreach (var envGroup in _envGroups)
        {
            var resourceGroup = envGroup.ResourceGroup;
            var webApps = envGroup.WebApps;
            var blobStorage = envGroup.BlobStorage;
            var logAnalyticsWorkspace = envGroup.LogAnalyticsWorkspace;

            var regionalConfig = _serverConfig
                .RegionalConfigs
                .First(s => s.LocationName == envGroup.LocationName);

            var appInsights = new Insights.Component(
                ResourceUtils.GetName(
                    $"sleekflow-core-app-insight-{LocationNames.GetShortName(envGroup.LocationName)}",
                    _myConfig),
                new Insights.ComponentArgs
                {
                    ResourceGroupName = resourceGroup.Name,
                    ApplicationType = Insights.ApplicationType.Web,
                    FlowType = "Redfield",
                    RequestSource = "IbizaWebAppExtensionCreate",
                    Kind = "Web",
                    WorkspaceResourceId = logAnalyticsWorkspace.Id
                },
                new CustomResourceOptions
                {
                    Parent = resourceGroup
                });

            var skuConfig = regionalConfig.SkuConfig.SleekflowCore;

            var autoScaleConfig = regionalConfig.AutoScaleConfig.SleekflowCore;

            var plan = new Web.AppServicePlan(
                ResourceUtils.GetName(
                    $"sleekflow-core-plan-{LocationNames.GetShortName(envGroup.LocationName)}",
                    _myConfig),
                new Web.AppServicePlanArgs
                {
                    Kind = "Linux",
                    ResourceGroupName = resourceGroup.Name,
                    Sku = new Web.Inputs.SkuDescriptionArgs
                    {
                        Name = skuConfig.Name, Tier = skuConfig.Tier
                    },
                    Reserved = true,
                },
                new CustomResourceOptions
                {
                    Parent = resourceGroup
                });

            var webAppName =
                ResourceUtils.GetName(
                    envGroup.FormatAppName(ServiceNames.GetShortName(ServiceNames.SleekflowCore)),
                    _myConfig);

            var appSettings = AppSettingUtils.GetWebAppSettings(
                webAppName,
                envGroup.Redis,
                image,
                envGroup.LocationName,
                resourceGroup,
                appInsights,
                envGroup.SignalR,
                regionalConfig.SleekflowCoreConfig,
                _serverConfig.DefaultSleekflowCoreFrontDoorDomain,
                _containerRegistryOutput,
                logAnalyticsWorkspace);

            appSettings.Add(
                new Web.Inputs.NameValuePairArgs
                {
                    Name = "Hangfire__WorkerCount",
                    Value = regionalConfig.SleekflowCoreConfig.HangfireWorkerConfig.WorkerCount
                });

            appSettings.Add(
                new Web.Inputs.NameValuePairArgs
                {
                    Name = "HangfireQueues__DisableInstances",
                    Value = regionalConfig.SleekflowCoreConfig.HangfireQueuesConfig.DisableInstances
                });

            var app = new Web.WebApp(
                webAppName,
                new Web.WebAppArgs
                {
                    ServerFarmId = plan.Id,
                    Name = webAppName,
                    ResourceGroupName = resourceGroup.Name,
                    SiteConfig = new Web.Inputs.SiteConfigArgs
                    {
                        AlwaysOn = true, NumberOfWorkers = 1
                    },
                    Kind = "app,linux,container",
                    Reserved = true,
                    PublicNetworkAccess = "Enabled",
                    HttpsOnly = true,

                    // The following settings are swapped in the standby slot
                    // ClientAffinityEnabled = false,
                },
                new CustomResourceOptions
                {
                    Parent = resourceGroup
                });

            var slot = new Web.WebAppSlot(
                $"{webAppName}-slot-standby",
                new Web.WebAppSlotArgs
                {
                    ServerFarmId = plan.Id,
                    Name = webAppName,
                    Slot = "standby",
                    ClientAffinityEnabled = false,
                    ResourceGroupName = resourceGroup.Name,
                    SiteConfig = new Web.Inputs.SiteConfigArgs
                    {
                        AlwaysOn = true,
                        NumberOfWorkers = 1,
                        AppSettings = EnvironmentVariablesUtils.GetDeduplicateEnvironmentVariables(appSettings),
                        HealthCheckPath = "/__health",
                        ConnectionStrings = new[]
                        {
                            new Web.Inputs.ConnStringInfoArgs()
                            {
                                Name = "DefaultConnection",
                                ConnectionString = ConnectionStringUtils.GetSqlServerConnStr(
                                    resourceGroup.Name,
                                    envGroup.SqlServerProperties!,
                                    regionalConfig.SleekflowCoreConfig.GeoSqlDb),
                                Type = Web.ConnectionStringType.SQLAzure
                            },
                            new Web.Inputs.ConnStringInfoArgs()
                            {
                                Name = "ReadConnection",
                                ConnectionString = ConnectionStringUtils.GetSqlServerConnStr(
                                    resourceGroup.Name,
                                    envGroup.SqlServerProperties!,
                                    regionalConfig.SleekflowCoreConfig.GeoSqlDb,
                                    isReadOnly: true),
                                Type = Web.ConnectionStringType.SQLAzure
                            },
                            new Web.Inputs.ConnStringInfoArgs()
                            {
                                Name = "StorageConnectionString",
                                ConnectionString = ConnectionStringUtils.GetStorageConnStr(
                                    resourceGroup.Name,
                                    blobStorage.Name,
                                    regionalConfig.SleekflowCoreConfig.GeoStorage),
                                Type = Web.ConnectionStringType.Custom
                            },
                        },
                        LinuxFxVersion =
                            imageName.Apply(n => $"DOCKER|{n}")
                    },
                    Kind = "app,linux,container",
                    PublicNetworkAccess = "Enabled",
                    HttpsOnly = true,
                },
                new CustomResourceOptions
                {
                    Parent = resourceGroup
                });

            var webTest = Output.Tuple(envGroup.ClientConfig, resourceGroup.Name, appInsights.Name)
                .Apply(
                    values =>
                        new Insights.WebTest(
                            ResourceUtils.GetName(
                                $"sleekflow-core-app-insight-web-test-{LocationNames.GetShortName(envGroup.LocationName)}",
                                _myConfig),
                            new Insights.WebTestArgs
                            {
                                ResourceGroupName = resourceGroup.Name,
                                Frequency = 300,
                                Timeout = 120,
                                Enabled = true,
                                RetryEnabled = true,
                                WebTestName = $"{webAppName}-health-check",
                                SyntheticMonitorId = $"{webAppName}-health-check",
                                Locations = new[]
                                {
                                    new Insights.Inputs.WebTestGeolocationArgs
                                    {
                                        Location = "apac-hk-hkn-azr"
                                    },
                                    new Insights.Inputs.WebTestGeolocationArgs
                                    {
                                        Location = "us-va-ash-azr"
                                    },
                                    new Insights.Inputs.WebTestGeolocationArgs
                                    {
                                        Location = "apac-sg-sin-azr"
                                    },
                                },
                                Request = new Insights.Inputs.WebTestPropertiesRequestArgs
                                {
                                    HttpVerb = "POST", RequestUrl = $"https://{webAppName}.azurewebsites.net/__health"
                                },
                                Tags = new InputMap<string>
                                {
                                    {
                                        $"hidden-link:/subscriptions/{values.Item1.SubscriptionId}/resourceGroups/{values.Item2}/providers/microsoft.insights/components/{values.Item3}",
                                        "Resource"
                                    }
                                },
                                WebTestKind = Insights.WebTestKind.Standard
                            },
                            new CustomResourceOptions
                            {
                                Parent = resourceGroup
                            }));

            var subnet = new Network.Subnet(
                ResourceUtils.GetName(
                    $"sleekflow-core-app-subnet-{LocationNames.GetShortName(envGroup.LocationName)}",
                    _myConfig),
                new Network.SubnetArgs
                {
                    ResourceGroupName = resourceGroup.Name,
                    VirtualNetworkName = envGroup.VirtualNetwork.Name,
                    AddressPrefix = regionalConfig.VnetConfig.SleekflowCoreAddressPrefix,
                    Delegations =
                    {
                        new Network.Inputs.DelegationArgs
                        {
                            Name = "sleekflow-core-app-subnet-delegation", ServiceName = "Microsoft.Web/serverfarms"
                        }
                    },
                },
                new CustomResourceOptions
                {
                    Parent = resourceGroup
                });

            var webAppSwiftVirtualNetworkConnection = new Web.WebAppSwiftVirtualNetworkConnection(
                ResourceUtils.GetName(
                    $"sleekflow-core-app-swift-virtual-network-connection-{LocationNames.GetShortName(envGroup.LocationName)}",
                    _myConfig),
                new Web.WebAppSwiftVirtualNetworkConnectionArgs
                {
                    Name = app.Name, SubnetResourceId = subnet.Id, ResourceGroupName = resourceGroup.Name,
                },
                new CustomResourceOptions
                {
                    Parent = resourceGroup
                });

            var slotWebAppSwiftVirtualNetworkConnection = new Web.WebAppSwiftVirtualNetworkConnectionSlot(
                ResourceUtils.GetName(
                    $"sleekflow-core-app-standby-slot-swift-virtual-network-connection-{LocationNames.GetShortName(envGroup.LocationName)}",
                    _myConfig),
                new Web.WebAppSwiftVirtualNetworkConnectionSlotArgs
                {
                    Name = app.Name,
                    Slot = slot.Name.Apply(name => name.Split('/').Last()),
                    SubnetResourceId = subnet.Id,
                    ResourceGroupName = resourceGroup.Name,
                },
                new CustomResourceOptions
                {
                    Parent = resourceGroup
                });

            if (envGroup.LocationName == LocationNames.EastAsia)
            {
                var autoScaleSetting = new Insights.AutoscaleSetting(
                    $"{webAppName}-autoscale-setting-{LocationNames.GetShortName(envGroup.LocationName)}",
                    new Insights.AutoscaleSettingArgs
                    {
                        Enabled = true,
                        TargetResourceUri = plan.Id,
                        ResourceGroupName = resourceGroup.Name,
                        Profiles = new List<Insights.Inputs.AutoscaleProfileArgs>()
                        {
                            new Insights.Inputs.AutoscaleProfileArgs
                            {
                                Name = "DefaultAutoScalingRuleSets",
                                Capacity = new Insights.Inputs.ScaleCapacityArgs
                                {
                                    Default = autoScaleConfig.Capacity.Default,
                                    Maximum = autoScaleConfig.Capacity.Maximum,
                                    Minimum = autoScaleConfig.Capacity.Minimum,
                                },
                                Rules = new List<Insights.Inputs.ScaleRuleArgs>
                                {
                                    new Insights.Inputs.ScaleRuleArgs
                                    {
                                        ScaleAction = new Insights.Inputs.ScaleActionArgs
                                        {
                                            Direction = Insights.ScaleDirection.Increase,
                                            Type = Insights.ScaleType.ChangeCount,
                                            Value = autoScaleConfig.ScaleOutInstances,
                                            Cooldown = "PT10M"
                                        },
                                        MetricTrigger = new Insights.Inputs.MetricTriggerArgs
                                        {
                                            MetricName = "CpuPercentage",
                                            MetricNamespace = "microsoft.web/serverfarms",
                                            MetricResourceUri = plan.Id,
                                            Operator = Insights.ComparisonOperationType.GreaterThan,
                                            Statistic = Insights.MetricStatisticType.Sum,
                                            Threshold = 50,
                                            TimeAggregation = Insights.TimeAggregationType.Average,
                                            TimeGrain = "PT1M",
                                            TimeWindow = "PT5M",
                                            DividePerInstance = true
                                        }
                                    },
                                    new Insights.Inputs.ScaleRuleArgs
                                    {
                                        ScaleAction = new Insights.Inputs.ScaleActionArgs
                                        {
                                            Direction = Insights.ScaleDirection.Decrease,
                                            Type = Insights.ScaleType.ChangeCount,
                                            Value = autoScaleConfig.ScaleDownInstances,
                                            Cooldown = "PT1H"
                                        },
                                        MetricTrigger = new Insights.Inputs.MetricTriggerArgs
                                        {
                                            MetricName = "CpuPercentage",
                                            MetricNamespace = "microsoft.web/serverfarms",
                                            MetricResourceUri = plan.Id,
                                            Operator = Insights.ComparisonOperationType.LessThan,
                                            Statistic = Insights.MetricStatisticType.Sum,
                                            Threshold = 30,
                                            TimeAggregation = Insights.TimeAggregationType.Average,
                                            TimeGrain = "PT1M",
                                            TimeWindow = "PT15M",
                                            DividePerInstance = true
                                        }
                                    },
                                    new Insights.Inputs.ScaleRuleArgs
                                    {
                                        ScaleAction = new Insights.Inputs.ScaleActionArgs
                                        {
                                            Direction = Insights.ScaleDirection.Increase,
                                            Type = Insights.ScaleType.ChangeCount,
                                            Value = autoScaleConfig.ScaleOutInstances,
                                            Cooldown = "PT10M"
                                        },
                                        MetricTrigger = new Insights.Inputs.MetricTriggerArgs
                                        {
                                            MetricName = "MemoryPercentage",
                                            MetricNamespace = "microsoft.web/serverfarms",
                                            MetricResourceUri = plan.Id,
                                            Operator = Insights.ComparisonOperationType.GreaterThan,
                                            Statistic = Insights.MetricStatisticType.Sum,
                                            Threshold = 70,
                                            TimeAggregation = Insights.TimeAggregationType.Average,
                                            TimeGrain = "PT1M",
                                            TimeWindow = "PT5M",
                                            DividePerInstance = true
                                        }
                                    },
                                    new Insights.Inputs.ScaleRuleArgs
                                    {
                                        ScaleAction = new Insights.Inputs.ScaleActionArgs
                                        {
                                            Direction = Insights.ScaleDirection.Decrease,
                                            Type = Insights.ScaleType.ChangeCount,
                                            Value = autoScaleConfig.ScaleDownInstances,
                                            Cooldown = "PT1H"
                                        },
                                        MetricTrigger = new Insights.Inputs.MetricTriggerArgs
                                        {
                                            MetricName = "MemoryPercentage",
                                            MetricNamespace = "microsoft.web/serverfarms",
                                            MetricResourceUri = plan.Id,
                                            Operator = Insights.ComparisonOperationType.LessThan,
                                            Statistic = Insights.MetricStatisticType.Sum,
                                            Threshold = 50,
                                            TimeAggregation = Insights.TimeAggregationType.Average,
                                            TimeGrain = "PT1M",
                                            TimeWindow = "PT15M",
                                            DividePerInstance = true
                                        }
                                    }
                                },
                                Recurrence = new Insights.Inputs.RecurrenceArgs
                                {
                                    Frequency = Insights.RecurrenceFrequency.Week,
                                    Schedule = new Insights.Inputs.RecurrentScheduleArgs
                                    {
                                        Days = new[]
                                        {
                                            "Monday",
                                            "Tuesday",
                                            "Wednesday",
                                            "Thursday",
                                            "Friday",
                                            "Saturday",
                                            "Sunday"
                                        },
                                        Hours = 2,
                                        Minutes = 1,
                                        TimeZone = "UTC"
                                    }
                                }
                            },
                            new Insights.Inputs.AutoscaleProfileArgs
                            {
                                Name = "ScaleTo8MachinesWeekdaysAt8AM - Start",
                                Capacity = new Insights.Inputs.ScaleCapacityArgs
                                {
                                    Default = _myConfig.Name == "production" ? "8" : "2",
                                    Maximum = _myConfig.Name == "production" ? "8" : "2",
                                    Minimum = _myConfig.Name == "production" ? "8" : "2",
                                },
                                Rules = new InputList<Insights.Inputs.ScaleRuleArgs>(),
                                Recurrence = new Insights.Inputs.RecurrenceArgs
                                {
                                    Frequency = Insights.RecurrenceFrequency.Week,
                                    Schedule = new Insights.Inputs.RecurrentScheduleArgs
                                    {
                                        Days = new[]
                                        {
                                            "Monday",
                                            "Tuesday",
                                            "Wednesday",
                                            "Thursday",
                                            "Friday"
                                        },
                                        Hours = new[]
                                        {
                                            // GMT+8 8AM
                                            0
                                        },
                                        Minutes = new[]
                                        {
                                            0
                                        },
                                        TimeZone = "UTC"
                                    }
                                }
                            },
                            new Insights.Inputs.AutoscaleProfileArgs
                            {
                                Name = "ScaleTo8MachinesWeekdaysAt8AM - End",
                                Capacity = new Insights.Inputs.ScaleCapacityArgs
                                {
                                    Default = _myConfig.Name == "production" ? "8" : "2",
                                    Maximum = _myConfig.Name == "production" ? "8" : "2",
                                    Minimum = _myConfig.Name == "production" ? "8" : "2",
                                },
                                Rules = new InputList<Insights.Inputs.ScaleRuleArgs>(),
                                Recurrence = new Insights.Inputs.RecurrenceArgs
                                {
                                    Frequency = Insights.RecurrenceFrequency.Week,
                                    Schedule = new Insights.Inputs.RecurrentScheduleArgs
                                    {
                                        Days = new[]
                                        {
                                            "Monday",
                                            "Tuesday",
                                            "Wednesday",
                                            "Thursday",
                                            "Friday"
                                        },
                                        Hours = new[]
                                        {
                                            // GMT+8 8AM
                                            2
                                        },
                                        Minutes = new[]
                                        {
                                            0
                                        },
                                        TimeZone = "UTC"
                                    }
                                }
                            }
                        }
                    },
                    new CustomResourceOptions
                    {
                        Parent = resourceGroup
                    });
            }
            else
            {
                var autoScaleSetting = new Insights.AutoscaleSetting(
                    $"{webAppName}-autoscale-setting-{LocationNames.GetShortName(envGroup.LocationName)}",
                    new Insights.AutoscaleSettingArgs
                    {
                        Enabled = true,
                        TargetResourceUri = plan.Id,
                        ResourceGroupName = resourceGroup.Name,
                        Profiles = new List<Insights.Inputs.AutoscaleProfileArgs>()
                        {
                            new Insights.Inputs.AutoscaleProfileArgs
                            {
                                Name = "DefaultAutoScalingRuleSets",
                                Capacity = new Insights.Inputs.ScaleCapacityArgs
                                {
                                    Default = autoScaleConfig.Capacity.Default,
                                    Maximum = autoScaleConfig.Capacity.Maximum,
                                    Minimum = autoScaleConfig.Capacity.Minimum,
                                },
                                Rules = new List<Insights.Inputs.ScaleRuleArgs>
                                {
                                    new Insights.Inputs.ScaleRuleArgs
                                    {
                                        ScaleAction = new Insights.Inputs.ScaleActionArgs
                                        {
                                            Direction = Insights.ScaleDirection.Increase,
                                            Type = Insights.ScaleType.ChangeCount,
                                            Value = autoScaleConfig.ScaleOutInstances,
                                            Cooldown = "PT10M"
                                        },
                                        MetricTrigger = new Insights.Inputs.MetricTriggerArgs
                                        {
                                            MetricName = "CpuPercentage",
                                            MetricNamespace = "microsoft.web/serverfarms",
                                            MetricResourceUri = plan.Id,
                                            Operator = Insights.ComparisonOperationType.GreaterThan,
                                            Statistic = Insights.MetricStatisticType.Sum,
                                            Threshold = 50,
                                            TimeAggregation = Insights.TimeAggregationType.Average,
                                            TimeGrain = "PT1M",
                                            TimeWindow = "PT5M",
                                            DividePerInstance = true
                                        }
                                    },
                                    new Insights.Inputs.ScaleRuleArgs
                                    {
                                        ScaleAction = new Insights.Inputs.ScaleActionArgs
                                        {
                                            Direction = Insights.ScaleDirection.Decrease,
                                            Type = Insights.ScaleType.ChangeCount,
                                            Value = autoScaleConfig.ScaleDownInstances,
                                            Cooldown = "PT1H"
                                        },
                                        MetricTrigger = new Insights.Inputs.MetricTriggerArgs
                                        {
                                            MetricName = "CpuPercentage",
                                            MetricNamespace = "microsoft.web/serverfarms",
                                            MetricResourceUri = plan.Id,
                                            Operator = Insights.ComparisonOperationType.LessThan,
                                            Statistic = Insights.MetricStatisticType.Sum,
                                            Threshold = 30,
                                            TimeAggregation = Insights.TimeAggregationType.Average,
                                            TimeGrain = "PT1M",
                                            TimeWindow = "PT15M",
                                            DividePerInstance = true
                                        }
                                    },
                                    new Insights.Inputs.ScaleRuleArgs
                                    {
                                        ScaleAction = new Insights.Inputs.ScaleActionArgs
                                        {
                                            Direction = Insights.ScaleDirection.Increase,
                                            Type = Insights.ScaleType.ChangeCount,
                                            Value = autoScaleConfig.ScaleOutInstances,
                                            Cooldown = "PT10M"
                                        },
                                        MetricTrigger = new Insights.Inputs.MetricTriggerArgs
                                        {
                                            MetricName = "MemoryPercentage",
                                            MetricNamespace = "microsoft.web/serverfarms",
                                            MetricResourceUri = plan.Id,
                                            Operator = Insights.ComparisonOperationType.GreaterThan,
                                            Statistic = Insights.MetricStatisticType.Sum,
                                            Threshold = 70,
                                            TimeAggregation = Insights.TimeAggregationType.Average,
                                            TimeGrain = "PT1M",
                                            TimeWindow = "PT5M",
                                            DividePerInstance = true
                                        }
                                    },
                                    new Insights.Inputs.ScaleRuleArgs
                                    {
                                        ScaleAction = new Insights.Inputs.ScaleActionArgs
                                        {
                                            Direction = Insights.ScaleDirection.Decrease,
                                            Type = Insights.ScaleType.ChangeCount,
                                            Value = autoScaleConfig.ScaleDownInstances,
                                            Cooldown = "PT1H"
                                        },
                                        MetricTrigger = new Insights.Inputs.MetricTriggerArgs
                                        {
                                            MetricName = "MemoryPercentage",
                                            MetricNamespace = "microsoft.web/serverfarms",
                                            MetricResourceUri = plan.Id,
                                            Operator = Insights.ComparisonOperationType.LessThan,
                                            Statistic = Insights.MetricStatisticType.Sum,
                                            Threshold = 50,
                                            TimeAggregation = Insights.TimeAggregationType.Average,
                                            TimeGrain = "PT1M",
                                            TimeWindow = "PT15M",
                                            DividePerInstance = true
                                        }
                                    }
                                },
                                Recurrence = new Insights.Inputs.RecurrenceArgs
                                {
                                    Frequency = Insights.RecurrenceFrequency.Week,
                                    Schedule = new Insights.Inputs.RecurrentScheduleArgs
                                    {
                                        Days = new[]
                                        {
                                            "Monday",
                                            "Tuesday",
                                            "Wednesday",
                                            "Thursday",
                                            "Friday",
                                            "Saturday",
                                            "Sunday"
                                        },
                                        Hours = 0,
                                        Minutes = 5,
                                        TimeZone = "UTC"
                                    }
                                }
                            },
                        }
                    },
                    new CustomResourceOptions
                    {
                        Parent = resourceGroup
                    });
            }


            if (_myConfig.Name is EnvironmentNames.Production)
            {
                InitAlertRules(webAppName, envGroup, resourceGroup, plan, app, appInsights);
            }

            webApps.Add(ServiceNames.SleekflowCore, (webAppName, app));
        }
    }

    private static void InitAlertRules(
        string webAppName,
        EnvGroup envGroup,
        ResourceGroup resourceGroup,
        Web.AppServicePlan plan,
        Web.WebApp app,
        Insights.Component appInsights)
    {
        InitAlertRulesForCpuPercentage(webAppName, envGroup, resourceGroup, plan);
        InitAlertRulesForMemoryPercentage(webAppName, envGroup, resourceGroup, plan);
        InitAlertRulesForResponseTime(webAppName, envGroup, resourceGroup, app);
        InitAlertRulesForHttp5xx(webAppName, envGroup, resourceGroup, app);
        InitAlertRulesForRedisMemoryPercentage(webAppName, envGroup, resourceGroup);
        InitAlertRulesForRedisLatency(webAppName, envGroup, resourceGroup);
        InitAlertRulesForHangfireQueueLength(webAppName, envGroup, resourceGroup, appInsights);
    }

    private static void InitAlertRulesForCpuPercentage(
        string webAppName,
        EnvGroup envGroup,
        ResourceGroup resourceGroup,
        Web.AppServicePlan plan)
    {
        var criticalCpuMetricAlert = new Insights.MetricAlert(
            $"{webAppName}-avg-cpu-percentage-over-90-for-15-mins",
            new Insights.MetricAlertArgs
            {
                RuleName =
                    $"P1 ({LocationNames.GetShortName(envGroup.LocationName)}) - Average App Service CpuPercentage is over 90 for 15 minutes",
                ResourceGroupName = resourceGroup.Name, // Replace with the actual RG if needed
                Location = "global", // Use the location as specified in the ARM template
                Severity = 1,
                Enabled = true,
                Scopes =
                {
                    plan.Id
                },
                EvaluationFrequency = "PT1M",
                WindowSize = "PT15M",
                Criteria = new Insights.Inputs.MetricAlertSingleResourceMultipleMetricCriteriaArgs
                {
                    AllOf =
                    {
                        new Insights.Inputs.MetricCriteriaArgs
                        {
                            Threshold = 90,
                            Name = "Metric1",
                            MetricNamespace = "microsoft.web/serverfarms",
                            MetricName = "CpuPercentage",
                            Operator = Insights.Operator.GreaterThan,
                            TimeAggregation = Insights.AggregationTypeEnum.Average,
                            SkipMetricValidation = false,
                            CriterionType = "StaticThresholdCriterion",
                        }
                    },
                    OdataType = "Microsoft.Azure.Monitor.SingleResourceMultipleMetricCriteria"
                },
                AutoMitigate = true,
                TargetResourceType = "Microsoft.Web/serverfarms",
                TargetResourceRegion = envGroup.LocationName,
                Actions = new Insights.Inputs.MetricAlertActionArgs
                {
                    ActionGroupId = envGroup.CriticalActionGroup.Id // The action group to invoke when the alert fires
                },
            },
            new CustomResourceOptions
            {
                Parent = resourceGroup
            });

        var preventionCpuMetricAlert = new Insights.MetricAlert(
            $"{webAppName}-avg-cpu-percentage-over-80-for-30-mins",
            new Insights.MetricAlertArgs
            {
                RuleName =
                    $"P2 ({LocationNames.GetShortName(envGroup.LocationName)}) - Average App Service CpuPercentage is over 80 for 30 minutes",
                ResourceGroupName = resourceGroup.Name, // Replace with the actual RG if needed
                Location = "global", // Use the location as specified in the ARM template
                Severity = 2,
                Enabled = true,
                Scopes =
                {
                    plan.Id
                },
                EvaluationFrequency = "PT1M",
                WindowSize = "PT30M",
                Criteria = new Insights.Inputs.MetricAlertSingleResourceMultipleMetricCriteriaArgs
                {
                    AllOf =
                    {
                        new Insights.Inputs.MetricCriteriaArgs
                        {
                            Threshold = 80,
                            Name = "Metric1",
                            MetricNamespace = "microsoft.web/serverfarms",
                            MetricName = "CpuPercentage",
                            Operator = Insights.Operator.GreaterThan,
                            TimeAggregation = Insights.AggregationTypeEnum.Average,
                            SkipMetricValidation = false,
                            CriterionType = "StaticThresholdCriterion",
                        }
                    },
                    OdataType = "Microsoft.Azure.Monitor.SingleResourceMultipleMetricCriteria"
                },
                AutoMitigate = true,
                TargetResourceType = "Microsoft.Web/serverfarms",
                TargetResourceRegion = envGroup.LocationName,
                Actions = new Insights.Inputs.MetricAlertActionArgs
                {
                    ActionGroupId = envGroup.PreventionActionGroup.Id // The action group to invoke when the alert fires
                },
            },
            new CustomResourceOptions
            {
                Parent = resourceGroup
            });
    }

    private static void InitAlertRulesForMemoryPercentage(
        string webAppName,
        EnvGroup envGroup,
        ResourceGroup resourceGroup,
        Web.AppServicePlan plan)
    {
        var criticalMemoryMetricAlert = new Insights.MetricAlert(
            $"{webAppName}-avg-memory-percentage-over-90-for-15-mins",
            new Insights.MetricAlertArgs
            {
                RuleName =
                    $"P1 ({LocationNames.GetShortName(envGroup.LocationName)}) - Average App Service MemoryPercentage is over 90 for 15 minutes",
                ResourceGroupName = resourceGroup.Name, // Replace with the actual RG if needed
                Location = "global", // Use the location as specified in the ARM template
                Severity = 1,
                Enabled = true,
                Scopes =
                {
                    plan.Id
                },
                EvaluationFrequency = "PT1M",
                WindowSize = "PT15M",
                Criteria = new Insights.Inputs.MetricAlertSingleResourceMultipleMetricCriteriaArgs
                {
                    AllOf =
                    {
                        new Insights.Inputs.MetricCriteriaArgs
                        {
                            Threshold = 90,
                            Name = "Metric1",
                            MetricNamespace = "microsoft.web/serverfarms",
                            MetricName = "MemoryPercentage",
                            Operator = Insights.Operator.GreaterThan,
                            TimeAggregation = Insights.AggregationTypeEnum.Average,
                            SkipMetricValidation = false,
                            CriterionType = "StaticThresholdCriterion",
                        }
                    },
                    OdataType = "Microsoft.Azure.Monitor.SingleResourceMultipleMetricCriteria"
                },
                AutoMitigate = true,
                TargetResourceType = "Microsoft.Web/serverfarms",
                TargetResourceRegion = envGroup.LocationName,
                Actions = new Insights.Inputs.MetricAlertActionArgs
                {
                    ActionGroupId = envGroup.CriticalActionGroup.Id // The action group to invoke when the alert fires
                },
            },
            new CustomResourceOptions
            {
                Parent = resourceGroup
            });

        var preventionMemoryMetricAlert = new Insights.MetricAlert(
            $"{webAppName}-avg-memory-percentage-over-80-for-30-mins",
            new Insights.MetricAlertArgs
            {
                RuleName =
                    $"P2 ({LocationNames.GetShortName(envGroup.LocationName)}) - Average App Service MemoryPercentage is over 80 for 30 minutes",
                ResourceGroupName = resourceGroup.Name, // Replace with the actual RG if needed
                Location = "global", // Use the location as specified in the ARM template
                Severity = 2,
                Enabled = true,
                Scopes =
                {
                    plan.Id
                },
                EvaluationFrequency = "PT1M",
                WindowSize = "PT30M",
                Criteria = new Insights.Inputs.MetricAlertSingleResourceMultipleMetricCriteriaArgs
                {
                    AllOf =
                    {
                        new Insights.Inputs.MetricCriteriaArgs
                        {
                            Threshold = 80,
                            Name = "Metric1",
                            MetricNamespace = "microsoft.web/serverfarms",
                            MetricName = "MemoryPercentage",
                            Operator = Insights.Operator.GreaterThan,
                            TimeAggregation = Insights.AggregationTypeEnum.Average,
                            SkipMetricValidation = false,
                            CriterionType = "StaticThresholdCriterion",
                        }
                    },
                    OdataType = "Microsoft.Azure.Monitor.SingleResourceMultipleMetricCriteria"
                },
                AutoMitigate = true,
                TargetResourceType = "Microsoft.Web/serverfarms",
                TargetResourceRegion = envGroup.LocationName,
                Actions = new Insights.Inputs.MetricAlertActionArgs
                {
                    ActionGroupId = envGroup.PreventionActionGroup.Id // The action group to invoke when the alert fires
                },
            },
            new CustomResourceOptions
            {
                Parent = resourceGroup
            });
    }

    private static void InitAlertRulesForResponseTime(
        string webAppName,
        EnvGroup envGroup,
        ResourceGroup resourceGroup,
        Web.WebApp app)
    {
        var criticalAvgRespTimeMetricAlert = new Insights.MetricAlert(
            $"{webAppName}-avg-response-time-over-10-seconds-for-5-mins",
            new Insights.MetricAlertArgs
            {
                RuleName =
                    $"P1 ({LocationNames.GetShortName(envGroup.LocationName)}) - Average App Service ResponseTime is over 10 seconds for 5 minutes",
                ResourceGroupName = resourceGroup.Name, // Replace with the actual RG if needed
                Location = "global", // Use the location as specified in the ARM template
                Severity = 1,
                Enabled = true,
                Scopes =
                {
                    app.Id
                },
                EvaluationFrequency = "PT1M",
                WindowSize = "PT5M",
                Criteria = new Insights.Inputs.MetricAlertSingleResourceMultipleMetricCriteriaArgs
                {
                    AllOf =
                    {
                        new Insights.Inputs.MetricCriteriaArgs
                        {
                            Threshold = 10,
                            Name = "Metric1",
                            MetricNamespace = "microsoft.web/sites",
                            MetricName = "HttpResponseTime",
                            Operator = Insights.Operator.GreaterThan,
                            TimeAggregation = Insights.AggregationTypeEnum.Average,
                            SkipMetricValidation = false,
                            CriterionType = "StaticThresholdCriterion",
                        }
                    },
                    OdataType = "Microsoft.Azure.Monitor.SingleResourceMultipleMetricCriteria"
                },
                AutoMitigate = true,
                TargetResourceType = "Microsoft.Web/sites",
                TargetResourceRegion = envGroup.LocationName,
                Actions = new Insights.Inputs.MetricAlertActionArgs
                {
                    ActionGroupId = envGroup.CriticalActionGroup.Id // The action group to invoke when the alert fires
                },
            },
            new CustomResourceOptions
            {
                Parent = resourceGroup
            });

        var preventionAvgRespTimeMetricAlert = new Insights.MetricAlert(
            $"{webAppName}-avg-response-time-over-5-seconds-for-5-mins",
            new Insights.MetricAlertArgs
            {
                RuleName =
                    $"P2 ({LocationNames.GetShortName(envGroup.LocationName)}) - Average App Service ResponseTime is over 5 seconds for 5 minutes",
                ResourceGroupName = resourceGroup.Name, // Replace with the actual RG if needed
                Location = "global", // Use the location as specified in the ARM template
                Severity = 2,
                Enabled = true,
                Scopes =
                {
                    app.Id
                },
                EvaluationFrequency = "PT1M",
                WindowSize = "PT5M",
                Criteria = new Insights.Inputs.MetricAlertSingleResourceMultipleMetricCriteriaArgs
                {
                    AllOf =
                    {
                        new Insights.Inputs.MetricCriteriaArgs
                        {
                            Threshold = 5,
                            Name = "Metric1",
                            MetricNamespace = "microsoft.web/sites",
                            MetricName = "HttpResponseTime",
                            Operator = Insights.Operator.GreaterThan,
                            TimeAggregation = Insights.AggregationTypeEnum.Average,
                            SkipMetricValidation = false,
                            CriterionType = "StaticThresholdCriterion",
                        }
                    },
                    OdataType = "Microsoft.Azure.Monitor.SingleResourceMultipleMetricCriteria"
                },
                AutoMitigate = true,
                TargetResourceType = "Microsoft.Web/sites",
                TargetResourceRegion = envGroup.LocationName,
                Actions = new Insights.Inputs.MetricAlertActionArgs
                {
                    ActionGroupId = envGroup.PreventionActionGroup.Id // The action group to invoke when the alert fires
                },
            },
            new CustomResourceOptions
            {
                Parent = resourceGroup
            });
    }

    private static void InitAlertRulesForHttp5xx(
        string webAppName,
        EnvGroup envGroup,
        ResourceGroup resourceGroup,
        Web.WebApp app)
    {
        var criticalConnectionErrorMetricAlert = new Insights.MetricAlert(
            $"{webAppName}-connection-error-over-500-in-5-mins",
            new Insights.MetricAlertArgs
            {
                RuleName =
                    $"P1 ({LocationNames.GetShortName(envGroup.LocationName)}) - HTTP server connection errors occured over 500 times in 5 minutes",
                ResourceGroupName = resourceGroup.Name, // Replace with the actual RG if needed
                Location = "global", // Use the location as specified in the ARM template
                Severity = 1,
                Enabled = true,
                Scopes =
                {
                    app.Id
                },
                EvaluationFrequency = "PT1M",
                WindowSize = "PT5M",
                Criteria = new Insights.Inputs.MetricAlertSingleResourceMultipleMetricCriteriaArgs
                {
                    AllOf =
                    {
                        new Insights.Inputs.MetricCriteriaArgs
                        {
                            Threshold = 500,
                            Name = "Metric1",
                            MetricNamespace = "microsoft.web/sites",
                            MetricName = "Http5xx",
                            Operator = Insights.Operator.GreaterThan,
                            TimeAggregation = Insights.AggregationTypeEnum.Total,
                            SkipMetricValidation = false,
                            CriterionType = "StaticThresholdCriterion",
                        }
                    },
                    OdataType = "Microsoft.Azure.Monitor.SingleResourceMultipleMetricCriteria"
                },
                AutoMitigate = true,
                TargetResourceType = "Microsoft.Web/sites",
                TargetResourceRegion = envGroup.LocationName,
                Actions = new Insights.Inputs.MetricAlertActionArgs
                {
                    ActionGroupId = envGroup.CriticalActionGroup.Id // The action group to invoke when the alert fires
                },
            });

        var preventionConnectionErrorMetricAlert = new Insights.MetricAlert(
            $"{webAppName}-connection-error-over-100-in-5-mins",
            new Insights.MetricAlertArgs
            {
                RuleName =
                    $"P2 ({LocationNames.GetShortName(envGroup.LocationName)}) - HTTP server connection errors occured over 100 times in 5 minutes",
                ResourceGroupName = resourceGroup.Name, // Replace with the actual RG if needed
                Location = "global", // Use the location as specified in the ARM template
                Severity = 2,
                Enabled = true,
                Scopes =
                {
                    app.Id
                },
                EvaluationFrequency = "PT1M",
                WindowSize = "PT5M",
                Criteria = new Insights.Inputs.MetricAlertSingleResourceMultipleMetricCriteriaArgs
                {
                    AllOf =
                    {
                        new Insights.Inputs.MetricCriteriaArgs
                        {
                            Threshold = 100,
                            Name = "Metric1",
                            MetricNamespace = "microsoft.web/sites",
                            MetricName = "Http5xx",
                            Operator = Insights.Operator.GreaterThan,
                            TimeAggregation = Insights.AggregationTypeEnum.Total,
                            SkipMetricValidation = false,
                            CriterionType = "StaticThresholdCriterion",
                        }
                    },
                    OdataType = "Microsoft.Azure.Monitor.SingleResourceMultipleMetricCriteria"
                },
                AutoMitigate = true,
                TargetResourceType = "Microsoft.Web/sites",
                TargetResourceRegion = envGroup.LocationName,
                Actions = new Insights.Inputs.MetricAlertActionArgs
                {
                    ActionGroupId = envGroup.PreventionActionGroup.Id // The action group to invoke when the alert fires
                },
            });
    }

    private static void InitAlertRulesForRedisMemoryPercentage(
        string webAppName,
        EnvGroup envGroup,
        ResourceGroup resourceGroup)
    {
        foreach (var (redisKey, redisValue) in envGroup.Redis)
        {
            var redisShortName = RedisInstances.GetShortName(redisKey);
            var criticalRedisMemoryMetricAlert = new Insights.MetricAlert(
                $"{webAppName}-{redisShortName}-redis-avg-memory-percentage-over-90-for-15-mins",
                new Insights.MetricAlertArgs
                {
                    RuleName =
                        $"P1 ({LocationNames.GetShortName(envGroup.LocationName)}) - Redis {redisShortName} used memory percentage is over 90 for 15 minutes",
                    ResourceGroupName = resourceGroup.Name, // Replace with the actual RG if needed
                    Location = "global", // Use the location as specified in the ARM template
                    Severity = 1,
                    Enabled = true,
                    Scopes =
                    {
                        redisValue.Id
                    },
                    EvaluationFrequency = "PT1M",
                    WindowSize = "PT15M",
                    Criteria = new Insights.Inputs.MetricAlertSingleResourceMultipleMetricCriteriaArgs
                    {
                        AllOf =
                        {
                            new Insights.Inputs.MetricCriteriaArgs
                            {
                                Threshold = 90,
                                Name = "Metric1",
                                MetricNamespace = "microsoft.cache/redis",
                                MetricName = "usedmemorypercentage",
                                Operator = Insights.Operator.GreaterThan,
                                TimeAggregation = Insights.AggregationTypeEnum.Average,
                                SkipMetricValidation = false,
                                CriterionType = "StaticThresholdCriterion",
                            }
                        },
                        OdataType = "Microsoft.Azure.Monitor.SingleResourceMultipleMetricCriteria"
                    },
                    AutoMitigate = true,
                    TargetResourceType = "Microsoft.Cache/redis",
                    TargetResourceRegion = envGroup.LocationName,
                    Actions = new Insights.Inputs.MetricAlertActionArgs
                    {
                        ActionGroupId =
                            envGroup.CriticalActionGroup.Id // The action group to invoke when the alert fires
                    },
                },
                new CustomResourceOptions
                {
                    Parent = resourceGroup
                });

            var preventionRedisMemoryMetricAlert = new Insights.MetricAlert(
                $"{webAppName}-{redisShortName}-redis-avg-memory-percentage-over-80-for-15-mins",
                new Insights.MetricAlertArgs
                {
                    RuleName =
                        $"P2 ({LocationNames.GetShortName(envGroup.LocationName)}) - Redis {redisShortName} used memory percentage is over 90 for 15 minutes",
                    ResourceGroupName = resourceGroup.Name, // Replace with the actual RG if needed
                    Location = "global", // Use the location as specified in the ARM template
                    Severity = 2,
                    Enabled = true,
                    Scopes =
                    {
                        redisValue.Id
                    },
                    EvaluationFrequency = "PT1M",
                    WindowSize = "PT15M",
                    Criteria = new Insights.Inputs.MetricAlertSingleResourceMultipleMetricCriteriaArgs
                    {
                        AllOf =
                        {
                            new Insights.Inputs.MetricCriteriaArgs
                            {
                                Threshold = 80,
                                Name = "Metric1",
                                MetricNamespace = "microsoft.cache/redis",
                                MetricName = "usedmemorypercentage",
                                Operator = Insights.Operator.GreaterThan,
                                TimeAggregation = Insights.AggregationTypeEnum.Average,
                                SkipMetricValidation = false,
                                CriterionType = "StaticThresholdCriterion",
                            }
                        },
                        OdataType = "Microsoft.Azure.Monitor.SingleResourceMultipleMetricCriteria"
                    },
                    AutoMitigate = true,
                    TargetResourceType = "Microsoft.Cache/redis",
                    TargetResourceRegion = envGroup.LocationName,
                    Actions = new Insights.Inputs.MetricAlertActionArgs
                    {
                        ActionGroupId =
                            envGroup.PreventionActionGroup.Id // The action group to invoke when the alert fires
                    },
                },
                new CustomResourceOptions
                {
                    Parent = resourceGroup
                });
        }
    }

    private static void InitAlertRulesForRedisLatency(string webAppName, EnvGroup envGroup, ResourceGroup resourceGroup)
    {
        foreach (var (redisKey, redisValue) in envGroup.Redis)
        {
            var redisShortName = RedisInstances.GetShortName(redisKey);
            var criticalRedisLatencyMetricAlert = new Insights.MetricAlert(
                $"{webAppName}-{redisShortName}-redis-latency-over-40ms-for-5-mins",
                new Insights.MetricAlertArgs
                {
                    RuleName =
                        $"P1 ({LocationNames.GetShortName(envGroup.LocationName)}) - Redis {redisShortName} latency is over 40ms for 5 minutes",
                    ResourceGroupName = resourceGroup.Name, // Replace with the actual RG if needed
                    Location = "global", // Use the location as specified in the ARM template
                    Severity = 1,
                    Enabled = true,
                    Scopes =
                    {
                        redisValue.Id
                    },
                    EvaluationFrequency = "PT1M",
                    WindowSize = "PT5M",
                    Criteria = new Insights.Inputs.MetricAlertSingleResourceMultipleMetricCriteriaArgs
                    {
                        AllOf =
                        {
                            new Insights.Inputs.MetricCriteriaArgs
                            {
                                Threshold = 50000, // in microseconds, so 50ms
                                Name = "Metric1",
                                MetricNamespace = "microsoft.cache/redis",
                                MetricName = "cacheLatency",
                                Operator = Insights.Operator.GreaterThan,
                                TimeAggregation = Insights.AggregationTypeEnum.Average,
                                SkipMetricValidation = false,
                                CriterionType = "StaticThresholdCriterion",
                            }
                        },
                        OdataType = "Microsoft.Azure.Monitor.SingleResourceMultipleMetricCriteria"
                    },
                    AutoMitigate = true,
                    TargetResourceType = "Microsoft.Cache/redis",
                    TargetResourceRegion = envGroup.LocationName,
                    Actions = new Insights.Inputs.MetricAlertActionArgs
                    {
                        ActionGroupId =
                            envGroup.CriticalActionGroup.Id // The action group to invoke when the alert fires
                    },
                },
                new CustomResourceOptions
                {
                    Parent = resourceGroup
                });

            var preventionRedisLatencyMetricAlert = new Insights.MetricAlert(
                $"{webAppName}-{redisShortName}-redis-latency-over-25ms-for-5-mins",
                new Insights.MetricAlertArgs
                {
                    RuleName =
                        $"P2 ({LocationNames.GetShortName(envGroup.LocationName)}) - Redis {redisShortName} latency is over 25ms for 5 minutes",
                    ResourceGroupName = resourceGroup.Name, // Replace with the actual RG if needed
                    Location = "global", // Use the location as specified in the ARM template
                    Severity = 2,
                    Enabled = true,
                    Scopes =
                    {
                        redisValue.Id
                    },
                    EvaluationFrequency = "PT1M",
                    WindowSize = "PT5M",
                    Criteria = new Insights.Inputs.MetricAlertSingleResourceMultipleMetricCriteriaArgs
                    {
                        AllOf =
                        {
                            new Insights.Inputs.MetricCriteriaArgs
                            {
                                Threshold = 35000, // in microseconds, so 35ms
                                Name = "Metric1",
                                MetricNamespace = "microsoft.cache/redis",
                                MetricName = "cacheLatency",
                                Operator = Insights.Operator.GreaterThan,
                                TimeAggregation = Insights.AggregationTypeEnum.Average,
                                SkipMetricValidation = false,
                                CriterionType = "StaticThresholdCriterion",
                            }
                        },
                        OdataType = "Microsoft.Azure.Monitor.SingleResourceMultipleMetricCriteria"
                    },
                    AutoMitigate = true,
                    TargetResourceType = "Microsoft.Cache/redis",
                    TargetResourceRegion = envGroup.LocationName,
                    Actions = new Insights.Inputs.MetricAlertActionArgs
                    {
                        ActionGroupId =
                            envGroup.PreventionActionGroup.Id // The action group to invoke when the alert fires
                    },
                },
                new CustomResourceOptions
                {
                    Parent = resourceGroup
                });
        }
    }

    private static void InitAlertRulesForHangfireQueueLength(
        string webAppName,
        EnvGroup envGroup,
        ResourceGroup resourceGroup,
        Insights.Component appInsights)
    {
        var hangfireQueueMetricAlert = new Insights.MetricAlert(
            $"{webAppName}-hangfire-queue-length-over-2000-for-5-mins",
            new Insights.MetricAlertArgs
            {
                RuleName =
                    $"P1 ({LocationNames.GetShortName(envGroup.LocationName)}) - Hangfire Queue Length is over 2000 for past 5 minutes",
                ResourceGroupName = resourceGroup.Name,
                Location = "global",
                Severity = 1,
                Enabled = true,
                Scopes =
                {
                    appInsights.Id
                },
                EvaluationFrequency = "PT1M",
                WindowSize = "PT5M",
                Criteria = new Insights.Inputs.MetricAlertSingleResourceMultipleMetricCriteriaArgs
                {
                    AllOf =
                    {
                        new Insights.Inputs.MetricCriteriaArgs
                        {
                            Threshold = 2000,
                            Name = "Metric1",
                            MetricNamespace = "azure.applicationinsights",
                            MetricName = "hangfire_meters.trigger_type.processing",
                            Operator = Insights.Operator.GreaterThan,
                            TimeAggregation = Insights.AggregationTypeEnum.Average,
                            SkipMetricValidation = false,
                            CriterionType = "StaticThresholdCriterion",
                        }
                    },
                    OdataType = "Microsoft.Azure.Monitor.SingleResourceMultipleMetricCriteria"
                },
                AutoMitigate = true,
                TargetResourceType = "microsoft.insights/components",
                TargetResourceRegion = envGroup.LocationName,
                Actions = new Insights.Inputs.MetricAlertActionArgs
                {
                    ActionGroupId = envGroup.CriticalActionGroup.Id // The action group to invoke when the alert fires
                },
            },
            new CustomResourceOptions
            {
                Parent = resourceGroup
            });
    }
}