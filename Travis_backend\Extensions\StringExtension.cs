﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;

namespace Travis_backend.Extensions;

public static class StringExtension
{
    public static bool IsNullOrEmpty(
        this string src)
    {
        return string.IsNullOrEmpty(src);
    }

    public static bool IsNotNullAndEqual(
        this string src,
        string desc)
    {
        if (src == null)
        {
            return false;
        }

        return src == desc;
    }

    public static string SafeFormat(
        this string src,
        string[] parameter)
    {
        if (src == null)
        {
            return null;
        }

        var sb = new StringBuilder(src, src.Length * 2);

        for (var i = 0; i < parameter.Length; i++)
        {
            sb.Replace($"{{{i}}}", parameter[i]);
        }

        return sb.ToString();
    }

    public static bool IsNewValueUpdatedAndNotNull(
        this string existingValue,
        string newValue)
    {
        if (newValue == null)
        {
            return false;
        }

        return existingValue != newValue;
    }

    public static string RemoveInvalidFileNameChars(
        this string filename,
        string replaceInvalidCharsWith = "_")
    {
        if (filename == null)
        {
            return null;
        }

        var invalid = new string(Path.GetInvalidFileNameChars()) + new string(Path.GetInvalidPathChars());

        return invalid.Aggregate(filename, (current, c) => current.Replace(c.ToString(), replaceInvalidCharsWith));
    }

    public static string ReplaceFirst(this string text, string search, string replace)
    {
        int length = text.IndexOf(search);
        return length < 0 ? text : text.Substring(0, length) + replace + text.Substring(length + search.Length);
    }

    public static string EscapeSqlLikeValue(this string parameterValue)
        => parameterValue?
            .Replace("[", "[[]")
            .Replace("%", "[%]")
            .Replace("_", "[_]");

    public static bool EqualsIgnoreCase(this string str1, string str2)
    {
        return string.Equals(str1, str2, StringComparison.OrdinalIgnoreCase);
    }

    public static bool ContainsIgnoreCase(this string str1, string str2)
    {
        return str1.Contains(str2, StringComparison.OrdinalIgnoreCase);
    }

    public static bool ContainsIgnoreCase(this IEnumerable<string> source, string str)
    {
        return source.Contains(str, StringComparer.OrdinalIgnoreCase);
    }

    public static T ToEnum<T>(this string str)
    {
        return (T) System.Enum.Parse(typeof(T), str, true);
    }
}