using System;
using System.Collections.Generic;
using System.Linq;
using Microsoft.AspNetCore.Http;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.ContactDomain.Constants;
using Travis_backend.Infrastructures.Attributes;

namespace Travis_backend.Extensions;

public static class HttpContextExtensions
{
    public static Staff GetRequestCompanyStaff(this HttpContext httpContext)
    {
        return httpContext.Items["Staff"] as Staff;
    }

    public static string GetRequestCompanyId(this HttpContext httpContext)
    {
        return httpContext.Items["CompanyId"].ToString();
    }

    public static string GetRequestUserEmail(this HttpContext httpContext)
    {
        return httpContext.Items["Email"].ToString();
    }

    public static string GetRequestIpAddress(this HttpContext httpContext)
    {
        var ipAddress = httpContext.Connection?.RemoteIpAddress?.ToString();
        var via = httpContext.Request.Headers.ContainsKey("Via");
        if (via && httpContext.Request.Headers["Via"].ToString().ToLower().Contains("azure"))
        {
            ipAddress = ExtractValues(httpContext.Request.Headers["X-Forwarded-For"].ToString()).First();
        }

        return ipAddress;
    }

    public static string GetUpdateUserProfileTriggerSource(this HttpContext httpContext)
    {
        try
        {
            var endpoint = httpContext.GetEndpoint();
            return endpoint!.Metadata.GetMetadata<UpdateUserProfileTriggerSourceAttribute>()!.Source;
        }
        catch
        {
            return UpdateUserProfileTriggerSource.UserOperation;
        }
    }

    private static IEnumerable<string> ExtractValues(string? values)
    {
        return string.IsNullOrEmpty(values)
            ? Enumerable.Empty<string>()
            : values.Replace("\"", string.Empty).Split(',').Where(v => !string.IsNullOrEmpty(v)).ToList();
    }
}