﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Travis_backend.Enums;
using Travis_backend.Extensions;
using Travis_backend.SubscriptionPlanDomain.Constants;

namespace Travis_backend.SubscriptionPlanDomain.Models
{
    public class SubscriptionPlan
    {
        public string Id { get; set; }

        public string SubscriptionName { get; set; }

        public string Description { get; set; }

        public double Amount { get; set; }

        public int MaximumContact { get; set; }

        public int MaximumMessageSent { get; set; }

        public int IncludedAgents { get; set; }

        public int MaximumCampaignSent { get; set; }

        public bool MaximumChannel { get; set; }

        public string ExtraChatAgentPlan { get; set; }

        public int ExtraChatAgentPrice { get; set; }

        public int MaximumAutomation { get; set; }

        public int MaximumNumberOfChannel { get; set; }

        public int MaximumAPICall { get; set; }

        [MaxLength(10)]
        public string Currency { get; set; }

        [MaxLength(200)]
        public string StripePlanId { get; set; }

        public SubscriptionTier SubscriptionTier { get; set; }

        public int Version { get; set; }

        [MaxLength(32)]
        public string CountryTier { get; set; }

        public int? MaximumAgentsLimit { get; set; }

        public List<SubscriptionSpecificFunction> AvailableFunctions { get; set; }

        [NotMapped]
        public string SubscriptionInterval
        {
            get
            {
                return (SubscriptionTier, Id) switch
                {
                    var (_, id) when id.ContainsIgnoreCase("monthly") => "monthly",
                    var (_, id) when id.ContainsIgnoreCase("yearly") => "yearly",
                    var (_, id) when id.ContainsIgnoreCase("oneoff") => "oneoff",
                    (SubscriptionTier.Enterprise, _) => "yearly",
                    _ => string.Empty
                };
            }
        }

        [NotMapped]
        public string PlanTypeCode
        {
            get
            {
                return (SubscriptionTier, Id) switch
                {
                    (SubscriptionTier.Free, _) => PlanTypeCodes.BasePlan,
                    (SubscriptionTier.Pro, _) => PlanTypeCodes.BasePlan,
                    (SubscriptionTier.Premium, _) => PlanTypeCodes.BasePlan,
                    (SubscriptionTier.Enterprise, _) => PlanTypeCodes.BasePlan,
                    (SubscriptionTier.Agent, _) => PlanTypeCodes.Agents,
                    (SubscriptionTier.AddOn, var str) when str.ContainsIgnoreCase("contact") => PlanTypeCodes.Contacts,
                    (SubscriptionTier.AddOn, var str) when str.ContainsIgnoreCase("onboarding_support") => PlanTypeCodes.OnboardingSupport,
                    (SubscriptionTier.AddOn, var str) when str.ContainsIgnoreCase("business_consultancy_service") => PlanTypeCodes.BusinessConsultancyService,
                    (SubscriptionTier.AddOn, var str) when str.ContainsIgnoreCase("flow_builder_flow_enrolments") => PlanTypeCodes.FlowBuilderFlowEnrolments,
                    (SubscriptionTier.AddOn, var str) when str.ContainsIgnoreCase("whatsapp_phone_number") => PlanTypeCodes.WhatsAppPhoneNumber,
                    _ => string.Empty
                };
            }
        }
    }
}