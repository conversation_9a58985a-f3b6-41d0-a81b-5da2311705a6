using Pulumi;
using Pulumi.AzureNative.Resources;
using Sleekflow.Core.Infra.Components.Configs;
using Sleekflow.Core.Infra.Constants;
using Sleekflow.Core.Infra.Utils;
using Cache = Pulumi.AzureNative.Cache;
using Insights = Pulumi.AzureNative.Insights;

namespace Sleekflow.Core.Infra.Components;

public class Redis
{
    private readonly MyConfig _myConfig;
    private readonly string _locationName;
    private readonly ServerConfig _serverConfig;
    private readonly ResourceGroup _resourceGroup;

    public Redis(
        MyConfig myConfig,
        string locationName,
        ServerConfig serverConfig,
        ResourceGroup resourceGroup)
    {
        _myConfig = myConfig;
        _locationName = locationName;
        _serverConfig = serverConfig;
        _resourceGroup = resourceGroup;
    }

    public Dictionary<string, Cache.Redis> InitRedis()
    {
        var instances = new Dictionary<string, Cache.Redis>();
        foreach (var instance in RedisInstances.GetInstances())
        {
            var shortName = RedisInstances.GetShortName(instance);
            var instanceName= string.Equals(RedisInstances.Default, instance) ? string.Empty : $"{shortName}-";
            var skuConfig = _serverConfig.RegionalConfigs.First(s => s.LocationName == _locationName).SkuConfig.Redis[shortName];

            var redis = new Cache.Redis(
                ResourceUtils.GetName(
                    $"sleekflow-core-redis-{instanceName}{LocationNames.GetShortName(_locationName)}",
                    _myConfig),
                new Cache.RedisArgs
                {
                    ResourceGroupName = _resourceGroup.Name,
                    Location = _resourceGroup.Location,
                    Sku = new Cache.Inputs.SkuArgs
                    {
                        Name = skuConfig.Name,
                        Family = skuConfig.Family!,
                        Capacity = skuConfig.Capacity!
                    },
                    RedisConfiguration = new Cache.Inputs.RedisCommonPropertiesRedisConfigurationArgs(),
                    EnableNonSslPort = false,
                    RedisVersion = "6",
                },
                new CustomResourceOptions
                {
                    Parent = _resourceGroup
                });
            instances.Add(instance, redis);
        }
        return instances;
    }
}