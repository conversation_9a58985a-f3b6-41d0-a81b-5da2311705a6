using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Travis_backend.Cache;
using Travis_backend.Cache.Models.CacheKeyPatterns;
using Travis_backend.ConversationDomain.Models;
using Travis_backend.ConversationDomain.ViewModels;
using Travis_backend.Database;
using Travis_backend.Database.Services;
using Travis_backend.Enums;
using Travis_backend.Helpers;
using Travis_backend.MessageDomain.Models;

namespace Travis_backend.ConversationDomain.Services;

public interface IConversationSummaryService
{
    Task<List<ResultLine>> GetConversationSummaryCountAsync(
        IQueryable<Conversation> conversations,
        string status,
        string assignedTo,
        string type,
        long? teamId,
        CancellationToken cancellationToken = default);

    Task<List<ResultLine>> GetV3ConversationSummaryCountAsync(
        IQueryable<Conversation> conversations,
        string status,
        string assignedTo,
        string type,
        long? teamId,
        CancellationToken cancellationToken = default);

    Task<List<HashtagSummary>> GetHashtagSummaryCount(
        IQueryable<Conversation> conversations,
        string companyId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets an overview of a staff member's unread conversations, grouped by AssignedToMe, Collaborator, and Mentioned.
    /// </summary>
    Task<ConversationUnreadSummary> GetUnreadConversationSummaryAsync(
        string companyId,
        long staffId,
        CancellationToken cancellationToken = default);
}

public class ConversationSummaryService : IConversationSummaryService
{
    private readonly ILogger<ConversationSummaryService> _logger;
    private readonly ICacheManagerService _cacheManagerService;
    private readonly ILockService _lockService;
    private readonly IDbContextService _dbContextService;

    public ConversationSummaryService(
        ILogger<ConversationSummaryService> logger,
        ICacheManagerService cacheManagerService,
        ILockService lockService,
        IDbContextService dbContextService)
    {
        _logger = logger;
        _cacheManagerService = cacheManagerService;
        _lockService = lockService;
        _dbContextService = dbContextService;
    }

    public async Task<List<ResultLine>> GetConversationSummaryCountAsync(
        IQueryable<Conversation> conversations,
        string status,
        string assignedTo,
        string type,
        long? teamId,
        CancellationToken cancellationToken = default)
    {
        var results = new List<ResultLine>();

        var groupByStatus = await conversations
            .GroupBy(x => x.Status)
            .Select(
                g => new
                {
                    Status = g.Key, Count = g.Count()
                })
            .ToListAsync(cancellationToken: cancellationToken);

        switch (status)
        {
            case "all":
                var openCount = groupByStatus.Where(x => x.Status == "open").Sum(x => x.Count);
                results.Add(
                    new ResultLine
                    {
                        AssigneeId = assignedTo,
                        Status = "open",
                        Type = type,
                        TeamId = teamId,
                        Count = openCount
                    });
                var pendingCount = groupByStatus.Where(x => x.Status == "pending").Sum(x => x.Count);
                results.Add(
                    new ResultLine
                    {
                        AssigneeId = assignedTo,
                        Status = "pending",
                        Type = type,
                        TeamId = teamId,
                        Count = pendingCount
                    });
                var closedCount = groupByStatus.Where(x => x.Status == "closed").Sum(x => x.Count);
                results.Add(
                    new ResultLine
                    {
                        AssigneeId = assignedTo,
                        Status = "closed",
                        Type = type,
                        TeamId = teamId,
                        Count = closedCount
                    });

                break;
            default:
                var count = groupByStatus.Sum(x => x.Count);
                results.Add(
                    new ResultLine
                    {
                        AssigneeId = assignedTo,
                        Status = status,
                        Type = type,
                        TeamId = teamId,
                        Count = count
                    });

                break;
        }

        return results;
    }

    public async Task<List<ResultLine>> GetV3ConversationSummaryCountAsync(
        IQueryable<Conversation> conversations,
        string status,
        string assignedTo,
        string type,
        long? teamId,
        CancellationToken cancellationToken = default)
    {
        var results = new List<ResultLine>();

        var groupByStatus = await conversations
            .GroupBy(x => x.Status)
            .Select(
                g => new
                {
                    Status = g.Key, Count = g.Count()
                })
            .ToListAsync(cancellationToken: cancellationToken);

        var allTotalCount = groupByStatus.Sum(x => x.Count);
        results.Add(
            new ResultLine
            {
                AssigneeId = assignedTo, Status = "all", Type = "total", Count = allTotalCount
            });

        var openTotalCount = groupByStatus.Where(x => x.Status == "open").Sum(x => x.Count);
        results.Add(
            new ResultLine
            {
                AssigneeId = assignedTo, Status = "open", Type = "total", Count = openTotalCount
            });

        var closedTotalCount = groupByStatus
            .Where(x => x.Status == "closed")
            .Sum(x => x.Count);

        results.Add(
            new ResultLine
            {
                AssigneeId = assignedTo, Status = "closed", Type = "total", Count = closedTotalCount
            });

        var snoozedTotalCount = groupByStatus
            .Where(x => x.Status == "pending")
            .Sum(x => x.Count);

        results.Add(
            new ResultLine
            {
                AssigneeId = assignedTo, Status = "pending", Type = "total", Count = snoozedTotalCount
            });

        var groupByStatusAndAssigned = await conversations
            .GroupBy(
                x => new
                {
                    x.Status, HasAssigned = x.AssigneeId.HasValue
                })
            .Select(
                g => new
                {
                    Status = g.Key.Status, HasAssigned = g.Key.HasAssigned, Count = g.Count()
                })
            .ToListAsync(cancellationToken: cancellationToken);

        var groupByAllStatusAndAssigned = await conversations
            .GroupBy(
                x => new
                {
                    HasAssigned = x.AssigneeId.HasValue
                })
            .Select(
                g => new
                {
                    Status = "all", HasAssigned = g.Key.HasAssigned, Count = g.Count()
                })
            .ToListAsync(cancellationToken: cancellationToken);

        groupByStatusAndAssigned.AddRange(groupByAllStatusAndAssigned);

        var assignedCount = groupByStatusAndAssigned
            .Where(
                g =>
                    g.Status == status
                    && g.HasAssigned)
            .Sum(x => x.Count);

        results.Add(
            new ResultLine
            {
                AssigneeId = assignedTo,
                Status = status,
                Type = "assigned",
                TeamId = teamId,
                Count = assignedCount
            });

        var unassignedCount = groupByStatusAndAssigned
            .Where(
                g =>
                    g.Status == status
                    && !g.HasAssigned)
            .Sum(x => x.Count);

        results.Add(
            new ResultLine
            {
                AssigneeId = assignedTo,
                Status = status,
                Type = "unassignedCount",
                TeamId = teamId,
                Count = unassignedCount
            });

        return results;
    }

    public async Task<List<HashtagSummary>> GetHashtagSummaryCount(
        IQueryable<Conversation> conversations,
        string companyId,
        CancellationToken cancellationToken = default)
    {
        var results = new List<HashtagSummary>();
        var dbContext = _dbContextService.GetDbContext();

        var conversationHashtagSummaries = await dbContext.ConversationHashtags
            .Where(
                x => x.CompanyId == companyId && conversations
                    .Select(y => y.Id)
                    .Contains(x.ConversationId))
            .GroupBy(x => x.HashtagId)
            .Select(
                x => new HashtagSummary
                {
                    HashtagId = x.Key, Count = x.Count()
                })
            .ToListAsync(cancellationToken: cancellationToken);

        var companyDefinedHashtags = await dbContext.CompanyDefinedHashtags
            .Where(x => x.CompanyId == companyId)
            .Select(
                x => new
                {
                    x.Id, x.Hashtag
                })
            .ToDictionaryAsync(x => x.Id, x => x.Hashtag, cancellationToken: cancellationToken);

        foreach (var conversationHashtagSummary in conversationHashtagSummaries)
        {
            try
            {
                conversationHashtagSummary.Hashtag = companyDefinedHashtags[conversationHashtagSummary.HashtagId];
            }
            catch (Exception e)
            {
                _logger.LogError(
                    e,
                    "Get Hashtag Summary Count Mapping issue with HashtagId {HashtagId}",
                    conversationHashtagSummary.HashtagId);
            }
        }

        results.AddRange(conversationHashtagSummaries);

        return results;
    }

    public async Task<ConversationUnreadSummary> GetUnreadConversationSummaryAsync(
        string companyId,
        long staffId,
        CancellationToken cancellationToken = default)
    {
        // Add 1 seconds cache for unread record
        var conversationUnreadSummaryCacheKeyPattern = new ConversationUnreadSummaryCacheKeyPattern(companyId, staffId);

        var data = await _cacheManagerService.GetCacheAsync(conversationUnreadSummaryCacheKeyPattern);

        if (!string.IsNullOrEmpty(data))
        {
            return JsonConvert.DeserializeObject<ConversationUnreadSummary>(data);
        }

        var myLock = await _lockService.AcquireLockAsync(
            $"lock:ConversationUnreadSummary{conversationUnreadSummaryCacheKeyPattern.GenerateKeyPattern()}",
            TimeSpan.FromSeconds(1));

        if (myLock == null)
        {
            await Task.Delay(TimeSpan.FromMilliseconds(500), cancellationToken);
            return await GetUnreadConversationSummaryAsync(companyId, staffId, cancellationToken);
        }

        var results = new ConversationUnreadSummary();
        var dbContext = _dbContextService.GetDbContext();

        // Fetches all open and active conversations where the staff member is either the contact owner or a collaborator.
        var openConversationQ = dbContext.Conversations
            .Where(
                x =>
                    x.CompanyId == companyId
                    && x.Status == "open"
                    && x.ActiveStatus == ActiveStatus.Active
                    && (x.AssigneeId == staffId
                        || x.AdditionalAssignees.Any(y => y.AssigneeId == staffId && y.CompanyId == companyId)));

        // Fetch all conversations where the staff member was mentioned, and the message was updated within the last two days.
        var mentionedConversations = dbContext.ConversationMessages
            .Where(
                x =>
                    x.CompanyId == companyId
                    && x.Channel == "note"
                    && x.MessageAssigneeId == staffId
                    && x.UpdatedAt > DateTime.UtcNow.AddDays(-2))
            .Select(x => x.ConversationId)
            .Distinct();

        // Fetch unread records for conversations that match the open and active conversation for the given company and staff member and also the dlivery statuses
        var unreadCountGroupedByDeliveryType = await dbContext.ConversationUnreadRecords.Where(
                x =>
                    x.CompanyId == companyId &&
                    x.StaffId == staffId &&
                    openConversationQ.Select(c => c.Id).Contains(x.ConversationId) &&
                    ConversationHelper.UnreadNotificationDeliveryStatuses.Contains(x.NotificationDeliveryStatus))
            .Select(
                x => new
                {
                    x.ConversationId, x.NotificationDeliveryType
                })
            .ToListAsync(cancellationToken: cancellationToken);

        // The total count of unread conversations directly assigned to the staff member.
        results.AssignedToMe = unreadCountGroupedByDeliveryType
            .Where(x => x.NotificationDeliveryType == NotificationDeliveryType.Assignee)
            .GroupBy(x => x.ConversationId)
            .Count();

        // The total count of unread conversations where the staff member is a collaborator.
        results.Collaborator = unreadCountGroupedByDeliveryType
            .Where(x => x.NotificationDeliveryType == NotificationDeliveryType.Collaborator)
            .GroupBy(x => x.ConversationId)
            .Count();

        // The total count of unread conversations where the staff member is being mentioned to.
        if (mentionedConversations.Any())
        {
            results.Mentioned = await dbContext.ConversationUnreadRecords
                .Where(
                    x =>
                        x.CompanyId == companyId &&
                        x.StaffId == staffId &&
                        mentionedConversations.Contains(x.ConversationId) &&
                        x.NotificationDeliveryType == NotificationDeliveryType.Mentioned &&
                        ConversationHelper.UnreadNotificationDeliveryStatuses.Contains(x.NotificationDeliveryStatus))
                .GroupBy(x => x.ConversationId)
                .CountAsync(cancellationToken: cancellationToken);
        }
        else
        {
            results.Mentioned = 0;
        }

        // Add 1 seconds cache for unread record
        await _cacheManagerService.SaveCacheAsync(
            conversationUnreadSummaryCacheKeyPattern,
            results);

        return results;
    }
}