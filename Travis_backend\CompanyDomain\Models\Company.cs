﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Sleekflow.Apis.MessagingHub.Model;
using Travis_backend.AccountAuthenticationDomain.Models;
using Travis_backend.AnalyticsDomain.Models;
using Travis_backend.AutomationDomain.Models;
using Travis_backend.BlastDomain.Models;
using Travis_backend.ChannelDomain.Models;
using Travis_backend.CommonDomain.Models;
using Travis_backend.CompanyDomain.ViewModels;
using Travis_backend.ContactDomain.Models;
using Travis_backend.ConversationDomain.Models;
using Travis_backend.ConversationServices.Models;
using Travis_backend.CoreDomain.Models;
using Travis_backend.Enums;
using Travis_backend.IntegrationServices.Models;
using Travis_backend.InternalDomain.Models;
using Travis_backend.MessageDomain.Models;
using Travis_backend.Models.ChatChannelConfig;
using Travis_backend.ResellerDomain.Models;
using Travis_backend.ShareInvitationDomain.Models;
using Travis_backend.ShoplineIntegrationDomain.Models;
using Travis_backend.StripeIntegrationDomain.Models;
using Travis_backend.ZapierIntegrationDomain.Models;

namespace Travis_backend.CompanyDomain.Models
{
    public class Company : ICanSoftDelete
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();

        public string CompanyName { get; set; }

        public string SignalRGroupName { get; set; }

        public string TimeZoneInfoId { get; set; }

        public string lmref { get; set; }

        public IList<BillRecord> BillRecords { get; set; }

        public DateTime RegistrationDate { get; set; } = DateTime.UtcNow;

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        public List<FacebookConfig> FacebookConfigs { get; set; }

        public EmailConfig EmailConfig { get; set; }

        public long? EmailConfigId { get; set; }

        public List<WhatsAppConfig> WhatsAppConfigs { get; set; }

        public WeChatConfig WeChatConfig { get; set; }

        public long? WeChatConfigId { get; set; }

        public List<LineConfig> LineConfigs { get; set; }

        public List<ViberConfig> ViberConfigs { get; set; }

        public List<SMSConfig> SMSConfigs { get; set; }

        public List<ShoplineConfig> ShoplineConfigs { get; set; }

        public List<ShopifyConfig> ShopifyConfigs { get; set; }

        public List<InstagramConfig> InstagramConfigs { get; set; }

        public List<TelegramConfig> TelegramConfigs { get; set; }

        public List<StripePaymentConfig> StripePaymentConfigs { get; set; }

        public List<CompanyCustomField> CompanyCustomFields { get; set; }

        public List<CompanyCustomUserProfileField> CustomUserProfileFields { get; set; }

        public List<CompanyHashtag> CompanyHashtags { get; set; }

        public List<CompanyMessageTemplate> CompnayMessageTemplates { get; set; }

        public StorageConfig StorageConfig { get; set; }

        public IList<Company> SubCompanies { get; set; }

        public IList<FacebookSender> FacebookSenders { get; set; }

        public IList<WhatsAppSender> WhatsAppSenders { get; set; }

        public IList<WeChatSender> WeChatSenders { get; set; }

        public IList<EmailSender> EmailSenders { get; set; }

        public IList<UserDevice> UserDevices { get; set; }

        public IList<LineSender> LineSenders { get; set; }

        public IList<SMSSender> SMSSenders { get; set; }

        public IList<SandboxSender> SandboxSenders { get; set; }

        public IList<Guest> Guests { get; set; }

        public IList<WebClientSender> WebClientSenders { get; set; }

        public CompanyIconFile CompanyIconFile { get; set; }

        public IList<ProfilePictureFile> StaffProfilePictures { get; set; } = new List<ProfilePictureFile>();

        public IList<AssignmentRule> AssignmentRules { get; set; }

        public IList<AutomationAction> AutomationActions { get; set; }

        public AssignmentQueue ConversationAssignmentQueue { get; set; }

        public IList<RequestChannel> RequestChannels { get; set; }

        public IList<NotificationRecord> NotificationRecords { get; set; }

        public IList<ImportContactHistory> ImportContactHistories { get; set; }

        public IList<CompanyTeam> CompanyTeams { get; set; }

        public IList<CompanyQuickReply> QuickReplies { get; set; }

        public bool IsVIP { get; set; }

        public CompanyUsageLimitOffsetProfile UsageLimitOffsetProfile { get; set; }

        public int MaximumAgents { get; set; }

        public int MaximumWhatsappInstance { get; set; } // Cloud API

        public int? MaximumContacts { get; set; }

        public int? MaximumWhAutomatedMessages { get; set; }

        public int? MaximumAutomations { get; set; }

        public int? MaximumNumberOfChannel { get; set; }

        public string CompanyCountry { get; set; }

        public bool IsReplyWithStaffName { get; set; }

        public CompanySandbox CompanySandbox { get; set; }

        public List<CompanyAPIKey> CompanyAPIKeys { get; set; }

        public bool PushUnassignedConversationsNotifications { get; set; } = true;

        public bool PushUnassignedConversationsEmailNotifications { get; set; } = false;

        public bool PushAssignedConversationsNotifications { get; set; } = true;

        public bool PushAssignedConversationsEmailNotifications { get; set; } = false;

        public bool PushGroupAssignedConversationsNotifications { get; set; } = true;

        public bool PushGroupAssignedConversationsEmailNotifications { get; set; } = true;

        public List<ZapierPollingRecord> ZapierPollingRecords { get; set; }

        public List<ShareableInvitation> ShareableInvitations { get; set; }

        public int? SubscriptionTrialDays { get; set; }

        public bool IsFreeTrial { get; set; }

        public string ReferralCode { get; set; }

        public bool IsRemovedChannels { get; set; }

        public bool IsPaymentFailed { get; set; }

        public bool IsQRCodeMappingEnabled { get; set; }

        public List<RolePermission> RolePermission { get; set; }

        public List<TwilioUsageRecord> TwilioUsageRecords { get; set; }

        [NotMapped]
        public List<BusinessBalanceDto> WhatsappCloudApiUsageRecords { get; set; }

        public List<Segment> AnalyticSegments { get; set; }

        public CompanySetting CompanySetting { get; set; }

        public List<UserProfile> UserProfiles { get; set; }

        public List<Staff> Staffs { get; set; }

        public List<ShopifyOrderRecord> ShopifyOrderRecords { get; set; }

        public List<ShopifyAbandonedCart> ShopifyAbandonedCarts { get; set; }

        public List<WhatsApp360DialogConfig> WhatsApp360DialogConfigs { get; set; }

        public List<WhatsappCloudApiConfig> WhatsappCloudApiConfigs { get; set; }

        public List<WhatsApp360DialogUsageRecord> WhatsApp360DialogUsageRecords { get; set; }

        public List<CompanyPaymentFailedLog> CompanyPaymentFailedLogs { get; set; }

        public List<string> CommunicationTools { get; set; }

        [MaxLength(450)]
        public string AffiliateCustomerId { get; set; }

        [MaxLength(13)]
        public string DefaultInboxOrder { get; set; } = "desc";

        public BlastMessageConfig BlastMessageConfig { get; set; }

        #region Shopify

        public bool IsShopifyAccount { get; set; }

        public int? ShopifyOrderConversion { get; set; }

        public int MaximumShopifyStore { get; set; }

        public List<ShopifyProductRecord> ShopifyProductRecords { get; set; }

        public List<ShopifyCollectionRecord> ShopifyCollectionRecords { get; set; }

        #endregion

        #region SFCC

        public bool IsEnabledSFCC { get; set; }

        #endregion

        #region CMS

        public string CmsLeadSource { get; set; }

        public string CmsCompanyIndustry { get; set; }

        [ForeignKey(nameof(CmsCompanyOwnerId))]
        public ApplicationUser CmsCompanyOwner { get; set; }

        public string CmsCompanyOwnerId { get; set; }

        [ForeignKey(nameof(CmsActivationOwnerId))]
        public ApplicationUser CmsActivationOwner { get; set; }

        public string CmsActivationOwnerId { get; set; }

        public IList<CmsContactOwnerAssignLog> CmsContactOwnerChangeLogs { get; set; }

        public string CmsRemark { get; set; }

        public CmsHubSpotCompanyMap CmsHubSpotCompanyMap { get; set; }

        public CompanyWhatsapp360DialogTopUpConfig Whatsapp360DialogTopUpConfig { get; set; }

        #endregion

        #region Stripe

        public List<StripePaymentRecord> StripePaymentRecords { get; set; }

        public List<StripePaymentMessageTemplate> StripePaymentMessageTemplates { get; set; }

        public bool IsStripeIntegrationEnabled { get; set; }

        #endregion

        #region LinkTracking

        public List<ShareLinkTrackRecord> ShareLinkTrackingRecords { get; set; }

        public List<ShareLinkGenerationRecord> ShareLinkGenerationRecords { get; set; }

        #endregion

        public bool IsDeleted { get; set; } = false;

        [MaxLength(64)]
        public string SubscriptionCountryTier { get; set; }

        [MaxLength(64)]
        public string SubscriptionCurrency { get; set; }

        public CompanyType CompanyType { get; set; } = CompanyType.DirectClient;

        public ResellerCompanyProfile ResellerCompanyProfile { get; set; }

        public ResellerClientCompanyProfile ResellerClientCompanyProfile { get; set; }

        public CmsCompanyAdditionalInfo CmsCompanyAdditionalInfo { get; set; }

        public List<Conversation> CompanyConversations { get; set; }

        public List<ConversationMessage> CompanyConversationMessages { get; set; }

        #region Contact

        public bool IsExpressImportEnabled { get; set; }

        #endregion

        public List<UserPreference> UserPreferences { get; set; }

        #region PartnerStack

        public CmsPartnerStackCustomerMap CmsPartnerStackCustomerMap { get; set; }

        #endregion
    }
}