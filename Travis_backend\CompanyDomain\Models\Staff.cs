﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using Travis_backend.AccountAuthenticationDomain.Models;
using Travis_backend.AutomationDomain.Models;
using Travis_backend.Enums;

namespace Travis_backend.CompanyDomain.Models
{
    public class Staff
    {
        public long Id { get; set; }

        public string IdentityId { get; set; }

        public ApplicationUser Identity { get; set; }

        public string CompanyId { get; set; }

        public Company Company { get; set; }

        public string Role { get; set; }

        public StaffUserRole RoleType { get; set; }

        public string Locale { get; set; }

        public string TimeZoneInfoId { get; set; }

        public string Position { get; set; }

        public ProfilePictureFile ProfilePicture { get; set; }

        public StaffStatus Status { get; set; }

        public StaffNotificationSetting NotificationSetting { get; set; }

        public long? NotificationSettingId { get; set; }

        public int Order { get; set; }

        public bool IsShowName { get; set; }

        public string Message { get; set; }

        public bool IsNewlyRegistered { get; set; } = true;

        public List<RegisteredSession> RegisteredSessions { get; set; }

        public bool IsInboxDemoCompleted { get; set; }

        public bool IsWhatsappConsultationBooked { get; set; }

        #region QRCode mapping

        public string QRCodeIdentity { get; set; }

        public TargetedChannelModel QRCodeChannel { get; set; }

        #endregion

        #region ShopifyCurrency

        [MaxLength(10)]
        public string DefaultCurrency { get; set; }

        #endregion

        public UserPreference UserPreference { get; set; }

        // public Permission Permission { get; set; }
        // public Permission StaffPermission
        // {
        //    get
        //    {
        //        if (Permission == null)
        //            return new Permission();
        //        return Permission;
        //    }
        // }
    }

    public class StaffOverview
    {
        public long Id { get; set; }

        public string DisplayName { get; set; }

        public string FirstName { get; set; }

        public string LastName { get; set; }

        public string Email { get; set; }

        public StaffUserRole Role { get; set; }

        public ProfilePictureFile ProfilePicture { get; set; }

        public string StaffIdentityId { get; set; }

        public string Position { get; set; }

        public StaffStatus Status { get; set; }

        public DateTime CreatedAt { get; set; }
    }
}