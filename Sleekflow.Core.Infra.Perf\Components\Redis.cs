using Pulumi;
using Pulumi.AzureNative.Resources;
using Sleekflow.Core.Infra.Perf.Components.Configs;
using Sleekflow.Core.Infra.Perf.Constants;
using Sleekflow.Core.Infra.Perf.Utils;
using Cache = Pulumi.AzureNative.Cache;

namespace Sleekflow.Core.Infra.Perf.Components;

public class Redis
{
    private readonly MyConfig _myConfig;
    private readonly ResourceGroup _resourceGroup;
    private readonly string _locationName;

    public Redis(
        MyConfig myConfig,
        ResourceGroup resourceGroup,
        string locationName)
    {
        _myConfig = myConfig;
        _resourceGroup = resourceGroup;
        _locationName = locationName;
    }

    public Cache.Redis InitRedis()
    {
        return new Cache.Redis(
            ResourceUtils.GetName($"sleekflow-core-redis-{LocationNames.GetShortName(_locationName)}", _myConfig),
            new Cache.RedisArgs
            {
                ResourceGroupName = _resourceGroup.Name,
                Location = _resourceGroup.Location,
                Sku = new Cache.Inputs.SkuArgs
                {
                    Capacity = 1, Family = "C", Name = "Standard",
                },
                RedisConfiguration = new Cache.Inputs.RedisCommonPropertiesRedisConfigurationArgs(),
                EnableNonSslPort = false,
                RedisVersion = "6",
            },
            new CustomResourceOptions
            {
                Parent = _resourceGroup
            });
    }
}