#nullable enable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using PhoneNumbers;
using Sleekflow.Apis.FlowHub.Api;
using Sleekflow.Apis.FlowHub.Model;
using Travis_backend.AccountAuthenticationDomain.Models;
using Travis_backend.BackgroundTaskServices;
using Travis_backend.BackgroundTaskServices.Helpers;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.CompanyDomain.Services;
using Travis_backend.Constants;
using Travis_backend.ConversationServices;
using Travis_backend.Data.BackgroundTask;
using Travis_backend.Database;
using Travis_backend.Enums;
using Travis_backend.Extensions;
using Travis_backend.FlowHubs.Filters;
using Travis_backend.FlowHubs.Services;
using GetSchemafulObjectInput = Sleekflow.Apis.CrmHub.Model.GetSchemafulObjectInput;
using ISchemafulObjectsApi = Sleekflow.Apis.CrmHub.Api.ISchemafulObjectsApi;
using UsageLimit = Sleekflow.Apis.FlowHub.Model.UsageLimit;

namespace Travis_backend.FlowHubs.Controllers;

[Authorize]
[Route("FlowHub")]
[TypeFilter(typeof(FlowHubExceptionFilter))]
public class FlowHubController : ControllerBase
{
    private readonly UserManager<ApplicationUser> _userManager;
    private readonly ICoreService _coreService;
    private readonly ApplicationDbContext _appDbContext;
    private readonly IBlobsApi _blobsApi;
    private readonly IExecutionsApi _executionsApi;
    private readonly IFlowHubConfigsApi _flowHubConfigsApi;
    private readonly IWorkflowsApi _workflowsApi;
    private readonly IWorkflowGroupsApi _workflowGroupsApi;
    private readonly IStatesApi _statesApi;
    private readonly ISchemafulObjectsApi _schemafulObjectsApi;
    private readonly ICompanyTeamService _companyTeamService;
    private readonly ICompanySubscriptionService _companySubscriptionService;
    private readonly IConfiguration _configuration;
    private readonly IFlowHubScheduledWorkflowEnrollmentService _flowHubScheduledWorkflowEnrollmentService;
    private readonly IFlowHubService _flowHubService;
    private readonly IBackgroundTaskService _backgroundTaskService;

    public FlowHubController(
        UserManager<ApplicationUser> userManager,
        ICoreService coreService,
        ApplicationDbContext appDbContext,
        IBlobsApi blobsApi,
        IExecutionsApi executionsApi,
        IFlowHubConfigsApi flowHubConfigsApi,
        IWorkflowsApi workflowsApi,
        IWorkflowGroupsApi workflowGroupsApi,
        IStatesApi statesApi,
        ISchemafulObjectsApi schemafulObjectsApi,
        ICompanyTeamService companyTeamService,
        ICompanySubscriptionService companySubscriptionService,
        IConfiguration configuration,
        IFlowHubScheduledWorkflowEnrollmentService flowHubScheduledWorkflowEnrollmentService,
        IFlowHubService flowHubService,
        IBackgroundTaskService backgroundTaskService)
    {
        _userManager = userManager;
        _coreService = coreService;
        _appDbContext = appDbContext;
        _blobsApi = blobsApi;
        _executionsApi = executionsApi;
        _flowHubConfigsApi = flowHubConfigsApi;
        _workflowsApi = workflowsApi;
        _workflowGroupsApi = workflowGroupsApi;
        _statesApi = statesApi;
        _schemafulObjectsApi = schemafulObjectsApi;
        _companyTeamService = companyTeamService;
        _configuration = configuration;
        _flowHubScheduledWorkflowEnrollmentService = flowHubScheduledWorkflowEnrollmentService;
        _flowHubService = flowHubService;
        _backgroundTaskService = backgroundTaskService;
        _companySubscriptionService = companySubscriptionService;
    }

    private async Task<List<string>> GetTeamIdsBySleekflowStaffAsync(Staff sleekflowStaff)
    {
        var companyId = sleekflowStaff.CompanyId;
        var identityId = sleekflowStaff.IdentityId;

        var companyTeams = await _companyTeamService.GetCompanyteamFromStaffId(companyId, identityId);

        return companyTeams.Select(t => t.Id.ToString()).ToList();
    }

    public class CreateBlobDownloadSasUrlsRequest
    {
        [JsonProperty("blob_names")]
        public List<string> BlobNames { get; set; }

        [JsonProperty("blob_type")]
        public string BlobType { get; set; }
    }

    [HttpPost("Blobs/CreateBlobDownloadSasUrls")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<CreateBlobDownloadSasUrlsOutputOutput>> CreateBlobDownloadSasUrls(
        [FromBody]
        CreateBlobDownloadSasUrlsRequest createBlobDownloadSasUrlsRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var createBlobDownloadSasUrlsOutputOutput = await _blobsApi.BlobsCreateBlobDownloadSasUrlsPostAsync(
            createBlobDownloadSasUrlsInput: new CreateBlobDownloadSasUrlsInput(
                staff.CompanyId,
                createBlobDownloadSasUrlsRequest.BlobNames,
                createBlobDownloadSasUrlsRequest.BlobType));

        return Ok(createBlobDownloadSasUrlsOutputOutput);
    }

    public class CreateBlobUploadSasUrlsRequest
    {
        [JsonProperty("number_of_blobs")]
        public int NumberOfBlobs { get; set; }

        [JsonProperty("blob_type")]
        public string BlobType { get; set; }
    }

    [HttpPost("Blobs/CreateBlobUploadSasUrls")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<CreateBlobUploadSasUrlsOutputOutput>> CreateBlobUploadSasUrls(
        [FromBody]
        CreateBlobUploadSasUrlsRequest createBlobUploadSasUrlsRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var createBlobUploadSasUrlsOutputOutput = await _blobsApi.BlobsCreateBlobUploadSasUrlsPostAsync(
            createBlobUploadSasUrlsInput: new CreateBlobUploadSasUrlsInput(
                staff.CompanyId,
                createBlobUploadSasUrlsRequest.NumberOfBlobs,
                createBlobUploadSasUrlsRequest.BlobType));

        return Ok(createBlobUploadSasUrlsOutputOutput);
    }

    public class GetStateStepExecutionsRequest
    {
        [JsonProperty("continuation_token")]
        public string ContinuationToken { get; set; }

        [JsonProperty("limit")]
        public int Limit { get; set; }

        [JsonProperty("state_id")]
        public string StateId { get; set; }
    }

    [HttpPost("Executions/GetStateStepExecutions")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GetStateStepExecutionsOutputOutput>> GetStateStepExecutions(
        [FromBody]
        GetStateStepExecutionsRequest getStateStepExecutionsRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var getStateStepExecutionsOutputOutput = await _executionsApi.ExecutionsGetStateStepExecutionsPostAsync(
            getStateStepExecutionsInput: new GetStateStepExecutionsInput(
                staff.CompanyId,
                getStateStepExecutionsRequest.ContinuationToken,
                getStateStepExecutionsRequest.Limit,
                getStateStepExecutionsRequest.StateId));

        var contactIdToName = await GetContactIdToNameDictAsync(
            staff.CompanyId,
            getStateStepExecutionsOutputOutput.Data.StepExecutions
                .Where(se => se.StateIdentity.ObjectType is "Contact" or "Contact.Id")
                .GroupBy(se => se.StateIdentity.ObjectId)
                .Select(g => g.Key)
                .ToList());

        var phoneNumberToIdName = await GetPhoneNumberToIdNameDictAsync(
            staff.CompanyId,
            getStateStepExecutionsOutputOutput.Data.StepExecutions
                .Where(se => se.StateIdentity.ObjectType is "Contact.PhoneNumber")
                .GroupBy(se => se.StateIdentity.ObjectId)
                .Select(g =>
                    PhoneNumberUtil.ExtractPossibleNumber(
                        PhoneNumberUtil.NormalizeDigitsOnly(g.Key)))
                .ToList());

        var emailToIdName = await GetEmailToIdNameDictAsync(
            staff.CompanyId,
            getStateStepExecutionsOutputOutput.Data.StepExecutions
                .Where(se => se.StateIdentity.ObjectType is "Contact.Email")
                .GroupBy(se => se.StateIdentity.ObjectId)
                .Select(g => g.Key)
                .ToList());

        var schemafulObjectToIdName = await GetSchemafulObjectToNameDictAsync(
            staff.CompanyId,
            getStateStepExecutionsOutputOutput.Data.StepExecutions
                .Where(se => se.StateIdentity.ObjectType is "SchemafulObject")
                .GroupBy(se => se.StateIdentity.ObjectId)
                .Select(g => g.Key)
                .ToList());

        foreach (var stepExecution in getStateStepExecutionsOutputOutput.Data.StepExecutions)
        {
            ProfileDisplayName? profileIdMatch = null;
            ProfileDisplayName? phoneMatch = null;
            ProfileDisplayName? emailMatch = null;
            ProfileDisplayName? schemafulObjectMatch = null;

            stepExecution.StateIdentity.ObjectLabel = stepExecution.StateIdentity.ObjectType switch
            {
                "Contact" or "Contact.Id" => contactIdToName.TryGetValue(stepExecution.StateIdentity.ObjectId, out profileIdMatch)
                    ? profileIdMatch.GetDisplayName()
                    : string.Empty,
                "Contact.PhoneNumber" => phoneNumberToIdName.TryGetValue(
                    PhoneNumberUtil.ExtractPossibleNumber(
                        PhoneNumberUtil.NormalizeDigitsOnly(stepExecution.StateIdentity.ObjectId)), out phoneMatch)
                    ? phoneMatch.GetDisplayName()
                    : string.Empty,
                "Contact.Email" => emailToIdName.TryGetValue(stepExecution.StateIdentity.ObjectId, out emailMatch)
                    ? emailMatch.GetDisplayName()
                    : string.Empty,
                "SchemafulObject" => schemafulObjectToIdName.TryGetValue(stepExecution.StateIdentity.ObjectId, out schemafulObjectMatch)
                    ? schemafulObjectMatch.GetDisplayName()
                    : string.Empty,
                _ => stepExecution.StateIdentity.ObjectLabel
            };

            stepExecution.StateIdentity.ObjectResolvedId = stepExecution.StateIdentity.ObjectType switch
            {
                "Contact" or "Contact.Id" => profileIdMatch?.ProfileId ?? string.Empty,
                "Contact.PhoneNumber" => phoneMatch?.ProfileId ?? string.Empty,
                "Contact.Email" => emailMatch?.ProfileId ?? string.Empty,
                "SchemafulObject" => schemafulObjectMatch?.ProfileId ?? string.Empty,
                _ => stepExecution.StateIdentity.ObjectResolvedId
            };
        }

        return Ok(getStateStepExecutionsOutputOutput);
    }

    public class GetWorkflowExecutionsRequest
    {
        [JsonProperty("continuation_token")]
        public string ContinuationToken { get; set; }

        [JsonProperty("limit")]
        public int Limit { get; set; }

        [JsonProperty("filters")]
        public GetWorkflowExecutionsInputFilters Filters { get; set; }
    }

    [HttpPost("Executions/GetWorkflowExecutions")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GetWorkflowExecutionsOutputOutput>> GetWorkflowExecutions(
        [FromBody]
        GetWorkflowExecutionsRequest getWorkflowExecutionsRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var getWorkflowExecutionsOutputOutput = await _executionsApi.ExecutionsGetWorkflowExecutionsPostAsync(
            getWorkflowExecutionsInput: new GetWorkflowExecutionsInput(
                staff.CompanyId,
                getWorkflowExecutionsRequest.ContinuationToken,
                getWorkflowExecutionsRequest.Limit,
                getWorkflowExecutionsRequest.Filters));

        var contactIdToName = await GetContactIdToNameDictAsync(
            staff.CompanyId,
            getWorkflowExecutionsOutputOutput.Data.WorkflowExecutions
                .Where(se => se.StateIdentity.ObjectType is "Contact" or "Contact.Id")
                .GroupBy(se => se.StateIdentity.ObjectId)
                .Select(g => g.Key)
                .ToList());

        var phoneNumberToIdName = await GetPhoneNumberToIdNameDictAsync(
            staff.CompanyId,
            getWorkflowExecutionsOutputOutput.Data.WorkflowExecutions
                .Where(se => se.StateIdentity.ObjectType is "Contact.PhoneNumber")
                .GroupBy(se => se.StateIdentity.ObjectId)
                .Select(g =>
                    PhoneNumberUtil.ExtractPossibleNumber(
                        PhoneNumberUtil.NormalizeDigitsOnly(g.Key)))
                .ToList());

        var emailToIdName = await GetEmailToIdNameDictAsync(
            staff.CompanyId,
            getWorkflowExecutionsOutputOutput.Data.WorkflowExecutions
                .Where(se => se.StateIdentity.ObjectType is "Contact.Email")
                .GroupBy(se => se.StateIdentity.ObjectId)
                .Select(g => g.Key)
                .ToList());

        var schemafulObjectToIdName = await GetSchemafulObjectToNameDictAsync(
            staff.CompanyId,
            getWorkflowExecutionsOutputOutput.Data.WorkflowExecutions
                .Where(se => se.StateIdentity.ObjectType is "SchemafulObject")
                .GroupBy(se => se.StateIdentity.ObjectId)
                .Select(g => g.Key)
                .ToList());

        foreach (var stepExecution in getWorkflowExecutionsOutputOutput.Data.WorkflowExecutions)
        {
            ProfileDisplayName? profileIdMatch = null;
            ProfileDisplayName? phoneMatch = null;
            ProfileDisplayName? emailMatch = null;
            ProfileDisplayName? schemafulObjectMatch = null;

            stepExecution.StateIdentity.ObjectLabel = stepExecution.StateIdentity.ObjectType switch
            {
                "Contact" or "Contact.Id" => contactIdToName.TryGetValue(stepExecution.StateIdentity.ObjectId, out profileIdMatch)
                    ? profileIdMatch.GetDisplayName()
                    : string.Empty,
                "Contact.PhoneNumber" => phoneNumberToIdName.TryGetValue(
                    PhoneNumberUtil.ExtractPossibleNumber(
                        PhoneNumberUtil.NormalizeDigitsOnly(stepExecution.StateIdentity.ObjectId)),
                    out phoneMatch)
                    ? phoneMatch.GetDisplayName()
                    : string.Empty,
                "Contact.Email" => emailToIdName.TryGetValue(stepExecution.StateIdentity.ObjectId, out emailMatch)
                    ? emailMatch.GetDisplayName()
                    : string.Empty,
                "SchemafulObject" => schemafulObjectToIdName.TryGetValue(stepExecution.StateIdentity.ObjectId, out schemafulObjectMatch)
                    ? schemafulObjectMatch.GetDisplayName()
                    : string.Empty,
                _ => stepExecution.StateIdentity.ObjectLabel
            };

            stepExecution.StateIdentity.ObjectResolvedId = stepExecution.StateIdentity.ObjectType switch
            {
                "Contact" or "Contact.Id" => profileIdMatch?.ProfileId ?? string.Empty,
                "Contact.PhoneNumber" => phoneMatch?.ProfileId ?? string.Empty,
                "Contact.Email" => emailMatch?.ProfileId ?? string.Empty,
                "SchemafulObject" => schemafulObjectMatch?.ProfileId ?? string.Empty,
                _ => stepExecution.StateIdentity.ObjectResolvedId
            };
        }

        return Ok(getWorkflowExecutionsOutputOutput);
    }

    public class GetWorkflowExecutionRequest
    {
        [JsonProperty("state_id")]
        public string StateId { get; set; }
    }

    [HttpPost("Executions/GetWorkflowExecution")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GetWorkflowExecutionOutput>> GetWorkflowExecution(
        [FromBody]
        GetWorkflowExecutionRequest request)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (staff is null)
        {
            return Unauthorized();
        }

        var getWorkflowExecutionOutputOutput =
            await _executionsApi.ExecutionsGetWorkflowExecutionPostAsync(
                getWorkflowExecutionInput: new GetWorkflowExecutionInput(
                    staff.CompanyId,
                    request.StateId));

        switch (getWorkflowExecutionOutputOutput.Data.WorkflowExecution.StateIdentity.ObjectType)
        {
            case "Contact" or "Contact.Id":
                var contactId = getWorkflowExecutionOutputOutput.Data.WorkflowExecution.StateIdentity.ObjectId;

                var contactIdToName = await GetContactIdToNameDictAsync(
                    staff.CompanyId,
                    new List<string> { contactId });

                getWorkflowExecutionOutputOutput.Data.WorkflowExecution.StateIdentity.ObjectLabel =
                    contactIdToName.TryGetValue(contactId, out var profileIdMatch)
                        ? profileIdMatch.GetDisplayName()
                        : string.Empty;

                getWorkflowExecutionOutputOutput.Data.WorkflowExecution.StateIdentity.ObjectResolvedId =
                    contactIdToName.ContainsKey(contactId)
                        ? contactId
                        : string.Empty;

                break;

            case "Contact.PhoneNumber":
                var phoneNumber = PhoneNumberUtil.ExtractPossibleNumber(
                    PhoneNumberUtil.NormalizeDigitsOnly(
                        getWorkflowExecutionOutputOutput.Data.WorkflowExecution.StateIdentity.ObjectId));

                var phoneNumberToIdName = await GetPhoneNumberToIdNameDictAsync(
                    staff.CompanyId,
                    new List<string> { phoneNumber });

                getWorkflowExecutionOutputOutput.Data.WorkflowExecution.StateIdentity.ObjectLabel =
                    phoneNumberToIdName.TryGetValue(phoneNumber, out var phoneMatch)
                    ? phoneMatch.GetDisplayName()
                    : string.Empty;

                getWorkflowExecutionOutputOutput.Data.WorkflowExecution.StateIdentity.ObjectResolvedId =
                    phoneMatch?.ProfileId ?? string.Empty;

                break;

            case "Contact.Email":
                var email = getWorkflowExecutionOutputOutput.Data.WorkflowExecution.StateIdentity.ObjectId;

                var emailToIdName = await GetEmailToIdNameDictAsync(
                    staff.CompanyId,
                    new List<string> { email });

                getWorkflowExecutionOutputOutput.Data.WorkflowExecution.StateIdentity.ObjectLabel =
                    emailToIdName.TryGetValue(email, out var emailMatch)
                    ? emailMatch.GetDisplayName()
                    : string.Empty;

                getWorkflowExecutionOutputOutput.Data.WorkflowExecution.StateIdentity.ObjectResolvedId =
                    emailMatch?.ProfileId ?? string.Empty;

                break;

            case "SchemafulObject":
                var schemafulObjectId = getWorkflowExecutionOutputOutput.Data.WorkflowExecution.StateIdentity.ObjectId;

                var schemafulObjectToIdName = await GetSchemafulObjectToNameDictAsync(
                    staff.CompanyId,
                    new List<string>() { schemafulObjectId });

                getWorkflowExecutionOutputOutput.Data.WorkflowExecution.StateIdentity.ObjectLabel =
                    schemafulObjectToIdName.TryGetValue(schemafulObjectId, out var schemafulObjectMatch)
                        ? schemafulObjectMatch.GetDisplayName()
                        : string.Empty;

                getWorkflowExecutionOutputOutput.Data.WorkflowExecution.StateIdentity.ObjectResolvedId =
                    schemafulObjectMatch?.ProfileId ?? string.Empty;

                break;
        }

        return Ok(getWorkflowExecutionOutputOutput);
    }

    public class GetWorkflowExecutionsByStateRequest
    {
        [JsonProperty("continuation_token")]
        public string ContinuationToken { get; set; }

        [JsonProperty("limit")]
        public int Limit { get; set; }

        [JsonProperty("filters")]
        public GetWorkflowExecutionsByStateInputFilters Filters { get; set; }
    }

    [HttpPost("Executions/GetWorkflowExecutionsByState")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GetWorkflowExecutionsByStateOutputOutput>> GetWorkflowExecutionsByState(
        [FromBody]
        GetWorkflowExecutionsByStateRequest getWorkflowExecutionsByStateRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var getWorkflowExecutionsByStateOutputOutput =
            await _executionsApi.ExecutionsGetWorkflowExecutionsByStatePostAsync(
                getWorkflowExecutionsByStateInput: new GetWorkflowExecutionsByStateInput(
                    staff.CompanyId,
                    getWorkflowExecutionsByStateRequest.ContinuationToken,
                    getWorkflowExecutionsByStateRequest.Limit,
                    getWorkflowExecutionsByStateRequest.Filters));

        var contactIdToName = await GetContactIdToNameDictAsync(
            staff.CompanyId,
            getWorkflowExecutionsByStateOutputOutput.Data.WorkflowExecutions
                .Where(se => se.StateIdentity.ObjectType is "Contact" or "Contact.Id")
                .GroupBy(se => se.StateIdentity.ObjectId)
                .Select(g => g.Key)
                .ToList());

        var phoneNumberToIdName = await GetPhoneNumberToIdNameDictAsync(
            staff.CompanyId,
            getWorkflowExecutionsByStateOutputOutput.Data.WorkflowExecutions
                .Where(se => se.StateIdentity.ObjectType is "Contact.PhoneNumber")
                .GroupBy(se => se.StateIdentity.ObjectId)
                .Select(g =>
                    PhoneNumberUtil.ExtractPossibleNumber(
                        PhoneNumberUtil.NormalizeDigitsOnly(g.Key)))
                .ToList());

        var emailToIdName = await GetEmailToIdNameDictAsync(
            staff.CompanyId,
            getWorkflowExecutionsByStateOutputOutput.Data.WorkflowExecutions
                .Where(se => se.StateIdentity.ObjectType is "Contact.Email")
                .GroupBy(se => se.StateIdentity.ObjectId)
                .Select(g => g.Key)
                .ToList());

        var schemafulObjectToIdName = await GetSchemafulObjectToNameDictAsync(
            staff.CompanyId,
            getWorkflowExecutionsByStateOutputOutput.Data.WorkflowExecutions
                .Where(se => se.StateIdentity.ObjectType is "SchemafulObject")
                .GroupBy(se => se.StateIdentity.ObjectId)
                .Select(g => g.Key)
                .ToList());

        foreach (var stepExecution in getWorkflowExecutionsByStateOutputOutput.Data.WorkflowExecutions)
        {
            ProfileDisplayName? profileIdMatch = null;
            ProfileDisplayName? phoneMatch = null;
            ProfileDisplayName? emailMatch = null;
            ProfileDisplayName? schemafulObjectMatch = null;

            stepExecution.StateIdentity.ObjectLabel = stepExecution.StateIdentity.ObjectType switch
            {
                "Contact" or "Contact.Id" => contactIdToName.TryGetValue(stepExecution.StateIdentity.ObjectId, out profileIdMatch)
                    ? profileIdMatch.GetDisplayName()
                    : string.Empty,
                "Contact.PhoneNumber" => phoneNumberToIdName.TryGetValue(
                    PhoneNumberUtil.ExtractPossibleNumber(
                        PhoneNumberUtil.NormalizeDigitsOnly(stepExecution.StateIdentity.ObjectId)),
                    out phoneMatch)
                    ? phoneMatch.GetDisplayName()
                    : string.Empty,
                "Contact.Email" => emailToIdName.TryGetValue(stepExecution.StateIdentity.ObjectId, out emailMatch)
                    ? emailMatch.GetDisplayName()
                    : string.Empty,
                "SchemafulObject" => schemafulObjectToIdName.TryGetValue(stepExecution.StateIdentity.ObjectId, out schemafulObjectMatch)
                    ? schemafulObjectMatch.GetDisplayName()
                    : string.Empty,
                _ => stepExecution.StateIdentity.ObjectLabel
            };

            stepExecution.StateIdentity.ObjectResolvedId = stepExecution.StateIdentity.ObjectType switch
            {
                "Contact" or "Contact.Id" => profileIdMatch?.ProfileId ?? string.Empty,
                "Contact.PhoneNumber" => phoneMatch?.ProfileId ?? string.Empty,
                "Contact.Email" => emailMatch?.ProfileId ?? string.Empty,
                "SchemafulObject" => schemafulObjectMatch?.ProfileId ?? string.Empty,
                _ => stepExecution.StateIdentity.ObjectResolvedId
            };
        }

        return Ok(getWorkflowExecutionsByStateOutputOutput);
    }

    public class GetWorkflowExecutionStatisticsRequest
    {
        [JsonProperty("filters")]
        public GetWorkflowExecutionStatisticsInputFilters Filters { get; set; }
    }

    [HttpPost("Executions/GetWorkflowExecutionStatistics")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GetWorkflowExecutionStatisticsOutputOutput>> GetWorkflowExecutionStatistics(
        [FromBody]
        GetWorkflowExecutionStatisticsRequest getWorkflowExecutionStatisticsRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var getWorkflowExecutionStatisticsOutputOutput =
            await _executionsApi.ExecutionsGetWorkflowExecutionStatisticsPostAsync(
                getWorkflowExecutionStatisticsInput: new GetWorkflowExecutionStatisticsInput(
                    staff.CompanyId,
                    getWorkflowExecutionStatisticsRequest.Filters));

        return Ok(getWorkflowExecutionStatisticsOutputOutput);
    }

    public class GetWorkflowStepExecutionsRequest
    {
        [JsonProperty("continuation_token")]
        public string ContinuationToken { get; set; }

        [JsonProperty("limit")]
        public int Limit { get; set; }

        [JsonProperty("workflow_id")]
        public string WorkflowId { get; set; }

        [JsonProperty("workflow_versioned_id")]
        public string WorkflowVersionedId { get; set; }

        [JsonProperty("step_id")]
        public string StepId { get; set; }
    }

    [HttpPost("Executions/GetWorkflowStepExecutions")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GetWorkflowStepExecutionsOutputOutput>> GetWorkflowStepExecutions(
        [FromBody]
        GetWorkflowStepExecutionsRequest getWorkflowStepExecutionsRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var getWorkflowStepExecutionsOutputOutput = await _executionsApi.ExecutionsGetWorkflowStepExecutionsPostAsync(
            getWorkflowStepExecutionsInput: new GetWorkflowStepExecutionsInput(
                staff.CompanyId,
                getWorkflowStepExecutionsRequest.ContinuationToken,
                getWorkflowStepExecutionsRequest.Limit,
                getWorkflowStepExecutionsRequest.WorkflowId,
                getWorkflowStepExecutionsRequest.WorkflowVersionedId,
                getWorkflowStepExecutionsRequest.StepId));

        var contactIdToName = await GetContactIdToNameDictAsync(
            staff.CompanyId,
            getWorkflowStepExecutionsOutputOutput.Data.StepExecutions
                .Where(se => se.StateIdentity.ObjectType is "Contact" or "Contact.Id")
                .GroupBy(se => se.StateIdentity.ObjectId)
                .Select(g => g.Key)
                .ToList());

        var phoneNumberToIdName = await GetPhoneNumberToIdNameDictAsync(
            staff.CompanyId,
            getWorkflowStepExecutionsOutputOutput.Data.StepExecutions
                .Where(se => se.StateIdentity.ObjectType is "Contact.PhoneNumber")
                .GroupBy(se => se.StateIdentity.ObjectId)
                .Select(g =>
                    PhoneNumberUtil.ExtractPossibleNumber(
                        PhoneNumberUtil.NormalizeDigitsOnly(g.Key)))
                .ToList());

        var emailToIdName = await GetEmailToIdNameDictAsync(
            staff.CompanyId,
            getWorkflowStepExecutionsOutputOutput.Data.StepExecutions
                .Where(se => se.StateIdentity.ObjectType is "Contact.Email")
                .GroupBy(se => se.StateIdentity.ObjectId)
                .Select(g => g.Key)
                .ToList());

        var schemafulObjectToIdName = await GetSchemafulObjectToNameDictAsync(
            staff.CompanyId,
            getWorkflowStepExecutionsOutputOutput.Data.StepExecutions
                .Where(se => se.StateIdentity.ObjectType is "SchemafulObject")
                .GroupBy(se => se.StateIdentity.ObjectId)
                .Select(g => g.Key)
                .ToList());

        foreach (var stepExecution in getWorkflowStepExecutionsOutputOutput.Data.StepExecutions)
        {
            ProfileDisplayName? profileIdMatch = null;
            ProfileDisplayName? phoneMatch = null;
            ProfileDisplayName? emailMatch = null;
            ProfileDisplayName? schemafulObjectMatch = null;

            stepExecution.StateIdentity.ObjectLabel = stepExecution.StateIdentity.ObjectType switch
            {
                "Contact" or "Contact.Id" => contactIdToName.TryGetValue(stepExecution.StateIdentity.ObjectId, out profileIdMatch)
                    ? profileIdMatch.GetDisplayName()
                    : string.Empty,
                "Contact.PhoneNumber" => phoneNumberToIdName.TryGetValue(
                    PhoneNumberUtil.ExtractPossibleNumber(
                        PhoneNumberUtil.NormalizeDigitsOnly(stepExecution.StateIdentity.ObjectId)), out phoneMatch)
                    ? phoneMatch.GetDisplayName()
                    : string.Empty,
                "Contact.Email" => emailToIdName.TryGetValue(stepExecution.StateIdentity.ObjectId, out emailMatch)
                    ? emailMatch.GetDisplayName()
                    : string.Empty,
                "SchemafulObject" => schemafulObjectToIdName.TryGetValue(stepExecution.StateIdentity.ObjectId, out schemafulObjectMatch)
                    ? schemafulObjectMatch.GetDisplayName()
                    : string.Empty,
                _ => stepExecution.StateIdentity.ObjectLabel
            };

            stepExecution.StateIdentity.ObjectResolvedId = stepExecution.StateIdentity.ObjectType switch
            {
                "Contact" or "Contact.Id" => profileIdMatch?.ProfileId ?? string.Empty,
                "Contact.PhoneNumber" => phoneMatch?.ProfileId ?? string.Empty,
                "Contact.Email" => emailMatch?.ProfileId ?? string.Empty,
                "SchemafulObject" => schemafulObjectMatch?.ProfileId ?? string.Empty,
                _ => stepExecution.StateIdentity.ObjectResolvedId
            };
        }

        return Ok(getWorkflowStepExecutionsOutputOutput);
    }

    private async Task<Dictionary<string, ProfileDisplayName>> GetContactIdToNameDictAsync(
        string companyId,
        ICollection<string> contactIds)
    {
        if (contactIds is not { Count: > 0 })
        {
            return new Dictionary<string, ProfileDisplayName>();
        }

        var contactIdToName = await _appDbContext.UserProfiles
            .Where(up =>
                up.CompanyId == companyId
                && contactIds.Contains(up.Id)
                && up.ActiveStatus == ActiveStatus.Active)
            .Select(
                up => new
                {
                    up.Id,
                    up.FirstName,
                    up.LastName,
                    up.PhoneNumber,
                    up.Email
                })
            .ToDictionaryAsync(
                up => up.Id,
                up => new ProfileDisplayName(
                    up.Id,
                    up.FirstName + " " + up.LastName,
                    up.PhoneNumber,
                    up.Email));

        return contactIdToName;
    }

    private sealed record ProfileDisplayName(string ProfileId, string DisplayName, string PhoneNumber, string Email)
    {
        public string GetDisplayName()
        {
            if (!string.IsNullOrWhiteSpace(DisplayName))
            {
                return DisplayName;
            }

            if (!string.IsNullOrWhiteSpace(PhoneNumber))
            {
                return PhoneNumber;
            }

            if (!string.IsNullOrWhiteSpace(Email))
            {
                return Email;
            }

            return string.Empty;
        }
    }

    private async Task<Dictionary<string, ProfileDisplayName>> GetPhoneNumberToIdNameDictAsync(
        string companyId,
        ICollection<string> phoneNumbers)
    {
        if (phoneNumbers is not { Count: > 0 })
        {
            return new Dictionary<string, ProfileDisplayName>();
        }

        var phoneNumberToIdName = await _appDbContext.UserProfiles
            .Where(up =>
                up.CompanyId == companyId
                && phoneNumbers.Contains(up.PhoneNumber)
                && up.ActiveStatus == ActiveStatus.Active)
            .Select(
                up => new
                {
                    up.Id,
                    up.FirstName,
                    up.LastName,
                    up.PhoneNumber,
                    up.Email,
                })
            .ToDictionaryAsync(
                up => up.PhoneNumber,
                up => new ProfileDisplayName(
                    up.Id,
                    up.FirstName + " " + up.LastName,
                    up.PhoneNumber,
                    up.Email));

        return phoneNumberToIdName;
    }

    private async Task<Dictionary<string, ProfileDisplayName>> GetEmailToIdNameDictAsync(
        string companyId,
        ICollection<string> emails)
    {
        if (emails is not { Count: > 0 })
        {
            return new Dictionary<string, ProfileDisplayName>();
        }

        var emailToIdName = await _appDbContext.UserProfiles
            .Where(up =>
                up.CompanyId == companyId
                && emails.Contains(up.Email)
                && up.ActiveStatus == ActiveStatus.Active)
            .Select(
                up => new
                {
                    up.Id,
                    up.FirstName,
                    up.LastName,
                    up.PhoneNumber,
                    up.Email
                })
            .ToDictionaryAsync(
                up => up.Email,
                up => new ProfileDisplayName(
                    up.Id,
                    up.FirstName + " " + up.LastName,
                    up.PhoneNumber,
                    up.Email));

        return emailToIdName;
    }

    /// <summary>
    /// DEVS-7841 - State.Identity of schemaful object related triggers has been changed.
    /// Retain this method for handling old data.
    ///
    /// Get schemaful object referenced contact name.
    /// </summary>
    /// <param name="companyId">Company Id.</param>
    /// <param name="objectIds">Format [SchemaId]:[SchemafulObjectId].</param>
    /// <returns>ProfileDisplayName collection.</returns>
    private async Task<Dictionary<string, ProfileDisplayName>> GetSchemafulObjectToNameDictAsync(
        string companyId,
        ICollection<string> objectIds)
    {
        if (objectIds is not { Count: > 0 })
        {
            return new Dictionary<string, ProfileDisplayName>();
        }

        var objectIdToContactIdDict = new Dictionary<string, string>();
        foreach (var objectId in objectIds)
        {
            try
            {
                var parts = objectId.Split(':');
                var schemaId = parts[0];
                var schemafulObjectId = parts[1];

                var contactId = (await _schemafulObjectsApi.SchemafulObjectsGetSchemafulObjectPostAsync(
                        getSchemafulObjectInput: new GetSchemafulObjectInput(
                            companyId,
                            schemaId,
                            schemafulObjectId)))
                    .Data
                    .SchemafulObject
                    .SleekflowUserProfileId;

                objectIdToContactIdDict.Add(objectId, contactId);
            }
            catch (Exception)
            {
                // ignored
            }
        }

        var contactIdToNameDict = await GetContactIdToNameDictAsync(companyId, objectIdToContactIdDict.Values);

        var objectIdToNameDict = objectIdToContactIdDict
            .Where(kvp => contactIdToNameDict.ContainsKey(kvp.Value))
            .ToDictionary(
                kvp => kvp.Key,
                kvp => new ProfileDisplayName(
                    kvp.Value,
                    contactIdToNameDict[kvp.Value].DisplayName,
                    contactIdToNameDict[kvp.Value].PhoneNumber,
                    contactIdToNameDict[kvp.Value].Email));

        return objectIdToNameDict;
    }

    public class CancelWorkflowExecutionRequest
    {
        [JsonProperty("state_id")]
        public string StateId { get; set; }
    }

    [HttpPost("Executions/CancelWorkflowExecution")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GetWorkflowStepExecutionsOutputOutput>> CancelWorkflowExecution(
        [FromBody] CancelWorkflowExecutionRequest cancelWorkflowExecutionRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (staff is null)
        {
            return Unauthorized();
        }

        var cancelWorkflowExecutionOutput =
            await _executionsApi.ExecutionsCancelWorkflowExecutionPostAsync(
                cancelWorkflowExecutionInput: new CancelWorkflowExecutionInput(
                    staff.CompanyId,
                    cancelWorkflowExecutionRequest.StateId,
                    staff.Id.ToString(),
                    await GetTeamIdsBySleekflowStaffAsync(staff)));

        return Ok(cancelWorkflowExecutionOutput);
    }

    public class GetWorkflowExecutionUsagesRequest
    {
        [JsonProperty("filters")]
        public WorkflowExecutionUsageFilters? Filters { get; set; }

        [JsonProperty("limit")]
        public int Limit { get; set; }

        [JsonProperty("continuation_token")]
        public string? ContinuationToken { get; set; }
    }

    [HttpPost("Executions/GetWorkflowExecutionUsages")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GetWorkflowExecutionUsagesOutputOutput>> GetWorkflowExecutionUsages(
        [FromBody] GetWorkflowExecutionUsagesRequest request)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (staff is null)
        {
            return Unauthorized();
        }

        var getWorkflowExecutionUsagesOutputOutput =
            await _executionsApi.ExecutionsGetWorkflowExecutionUsagesPostAsync(
                getWorkflowExecutionUsagesInput: new GetWorkflowExecutionUsagesInput(
                    staff.CompanyId,
                    request.Filters,
                    request.Limit,
                    request.ContinuationToken));

        return Ok(getWorkflowExecutionUsagesOutputOutput);
    }

    public class GetUniqueWorkflowExecutionCountRequest
    {
        [Required]
        [JsonProperty("execution_from_date_time")]
        public DateTimeOffset ExecutionFromDateTime { get; set; }

        [Required]
        [JsonProperty("execution_to_date_time")]
        public DateTimeOffset ExecutionToDateTime { get; set; }

        [JsonProperty("workflow_type")]
        public string? WorkflowType { get; set; }
    }

    [HttpPost("Executions/GetUniqueWorkflowExecutionCount")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GetUniqueWorkflowExecutionCountOutputOutput>> GetUniqueWorkflowExecutionCount(
        [FromBody] GetUniqueWorkflowExecutionCountRequest request)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (staff is null)
        {
            return Unauthorized();
        }

        var getUniqueWorkflowExecutionCountOutputOutput =
            await _executionsApi.ExecutionsGetUniqueWorkflowExecutionCountPostAsync(
                getUniqueWorkflowExecutionCountInput: new GetUniqueWorkflowExecutionCountInput(
                    staff.CompanyId,
                    request.ExecutionFromDateTime,
                    request.ExecutionToDateTime,
                    request.WorkflowType));

        return Ok(getUniqueWorkflowExecutionCountOutputOutput);
    }

    public class ExportWorkflowExecutionUsagesToCsvRequest
    {
        [JsonProperty("filters")]
        public WorkflowExecutionUsageFilters? Filters { get; set; }
    }

    [HttpPost("Executions/ExportWorkflowExecutionUsagesToCsv")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<BackgroundTaskViewModel>> ExportWorkflowExecutionUsagesToCsv(
        [FromBody] ExportWorkflowExecutionUsagesToCsvRequest request)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (staff is null)
        {
            return Unauthorized();
        }

        var backgroundTask = await _backgroundTaskService.EnqueueExportFlowHubWorkflowExecutionUsagesToCsvTask(
            staff.IdentityId,
            staff.CompanyId,
            staff.Id,
            request.Filters);

        return Ok(backgroundTask.MapToResultViewModel());
    }

    public class EnrollFlowHubRequest
    {
    }

    [HttpPost("FlowHubConfigs/EnrollFlowHub")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<EnrollFlowHubOutputOutput>> EnrollFlowHub(
        [FromBody]
        EnrollFlowHubRequest enrollFlowHubRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var enrollFlowHubOutputOutput = await _flowHubConfigsApi.FlowHubConfigsEnrollFlowHubPostAsync(
            enrollFlowHubInput: new EnrollFlowHubInput(
                staff.CompanyId,
                staff.Id.ToString(),
                await GetTeamIdsBySleekflowStaffAsync(staff),
                _configuration.GetValue<string>("Values:DomainName")));

        return Ok(enrollFlowHubOutputOutput);
    }

    public class GetFlowHubConfigRequest
    {
    }

    [HttpPost("FlowHubConfigs/GetFlowHubConfig")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GetFlowHubConfigOutputOutput>> GetFlowHubConfig(
        [FromBody]
        GetFlowHubConfigRequest getFlowHubConfigRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var getFlowHubConfigOutputOutput = await _flowHubConfigsApi.FlowHubConfigsGetFlowHubConfigPostAsync(
            getFlowHubConfigInput: new GetFlowHubConfigInput(
                staff.CompanyId));

        return Ok(getFlowHubConfigOutputOutput);
    }

    public class UnenrollFlowHubRequest
    {
    }

    [HttpPost("FlowHubConfigs/UnenrollFlowHub")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<UnenrollFlowHubOutputOutput>> UnenrollFlowHub(
        [FromBody]
        UnenrollFlowHubRequest unenrollFlowHubRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var unenrollFlowHubOutputOutput = await _flowHubConfigsApi.FlowHubConfigsUnenrollFlowHubPostAsync(
            unenrollFlowHubInput: new UnenrollFlowHubInput(
                staff.CompanyId,
                staff.Id.ToString(),
                await GetTeamIdsBySleekflowStaffAsync(staff)));

        return Ok(unenrollFlowHubOutputOutput);
    }

    public class UpdateFlowHubConfigRequest
    {
    }

    [HttpPost("FlowHubConfigs/UpdateFlowHubConfig")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<UpdateFlowHubConfigOutputOutput>> UpdateFlowHubConfig(
        [FromBody]
        UpdateFlowHubConfigRequest updateFlowHubConfigRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        var usageLimit = await _flowHubService.GetUsageLimitForUpdateAsync(staff.CompanyId);

        var updateFlowHubConfigOutputOutput = await _flowHubConfigsApi.FlowHubConfigsUpdateFlowHubConfigPostAsync(
            updateFlowHubConfigInput: new UpdateFlowHubConfigInput(
                sleekflowCompanyId: staff.CompanyId,
                sleekflowStaffId: staff.Id.ToString(),
                sleekflowStaffTeamIds: await GetTeamIdsBySleekflowStaffAsync(staff),
                usageLimit: usageLimit,
                origin: _configuration.GetValue<string>("Values:DomainName") ));

        return Ok(updateFlowHubConfigOutputOutput);
    }

    public class GetObjectStatesRequest
    {
        [JsonProperty("object_id")]
        public string ObjectId { get; set; }

        [JsonProperty("object_type")]
        public string ObjectType { get; set; }

        [JsonProperty("continuation_token")]
        public string ContinuationToken { get; set; }

        [JsonProperty("limit")]
        public int Limit { get; set; }
    }

    [HttpPost("States/GetObjectStates")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GetObjectStatesOutputOutput>> GetObjectStates(
        [FromBody]
        GetObjectStatesRequest getObjectStatesRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var getObjectStatesOutputOutput = await _statesApi.StatesGetObjectStatesPostAsync(
            getObjectStatesInput: new GetObjectStatesInput(
                staff.CompanyId,
                getObjectStatesRequest.ObjectId,
                getObjectStatesRequest.ObjectType,
                getObjectStatesRequest.ContinuationToken,
                getObjectStatesRequest.Limit));

        return Ok(getObjectStatesOutputOutput);
    }

    public class GetStatesRequest
    {
        [JsonProperty("continuation_token")]
        public string ContinuationToken { get; set; }

        [JsonProperty("limit")]
        public int Limit { get; set; }

        [JsonProperty("filters")]
        public GetStatesInputFilters Filters { get; set; }
    }

    [HttpPost("States/GetStates")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GetStatesOutputOutput>> GetStates(
        [FromBody]
        GetStatesRequest getStatesRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var getStatesOutputOutput = await _statesApi.StatesGetStatesPostAsync(
            getStatesInput: new GetStatesInput(
                staff.CompanyId,
                getStatesRequest.ContinuationToken,
                getStatesRequest.Limit,
                getStatesRequest.Filters));

        return Ok(getStatesOutputOutput);
    }

    public class CreateWorkflowRequest
    {
        [JsonProperty("triggers")]
        public WorkflowTriggers Triggers { get; set; }

        [JsonProperty("steps")]
        public List<JObject> Steps { get; set; }

        [JsonProperty("workflow_enrollment_settings")]
        public WorkflowEnrollmentSettings WorkflowEnrollmentSettings { get; set; }

        [JsonProperty("workflow_schedule_settings")]
        public WorkflowScheduleSettings WorkflowScheduleSettings { get; set; }

        [JsonProperty("name")]
        public string Name { get; set; }

        [JsonProperty("workflow_type")]
        public string WorkflowType { get; set; }

        [JsonProperty("workflow_group_id")]
        public string WorkflowGroupId { get; set; }

        [JsonProperty("metadata")]
        public Dictionary<string, object> Metadata { get; set; }
    }

    [HttpPost("Workflows/CreateWorkflow")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<CreateWorkflowOutputOutput>> CreateWorkflow(
        [FromBody]
        CreateWorkflowRequest createWorkflowRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var createWorkflowOutputOutput = await _workflowsApi.WorkflowsCreateWorkflowPostAsync(
            createWorkflowInput: new CreateWorkflowInput(
                staff.CompanyId,
                createWorkflowRequest.Triggers,
                createWorkflowRequest.WorkflowEnrollmentSettings,
                createWorkflowRequest.WorkflowScheduleSettings,
                createWorkflowRequest.Steps.Select(s => (object) s).ToList(),
                createWorkflowRequest.Name,
                createWorkflowRequest.WorkflowType,
                createWorkflowRequest.WorkflowGroupId,
                createWorkflowRequest.Metadata,
                staff.Id.ToString(),
                await GetTeamIdsBySleekflowStaffAsync(staff)));

        return Ok(createWorkflowOutputOutput);
    }

    public class GetWorkflowWebhookTriggersRequest
    {
        [JsonProperty("workflow_id")]
        public string WorkflowId { get; set; }
    }

    [HttpPost("Workflows/GetWorkflowWebhookTriggers")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GetWorkflowWebhookTriggersOutputOutput>> GetWorkflowWebhookTriggers(
        [FromBody] GetWorkflowWebhookTriggersRequest getWorkflowWebhookTriggersRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (staff == null)
        {
            return Unauthorized();
        }

        var getWorkflowWebhookTriggersOutputOutput =
            await _workflowsApi.WorkflowsGetWorkflowWebhookTriggersPostAsync(
                getWorkflowWebhookTriggersInput: new GetWorkflowWebhookTriggersInput(
                    staff.CompanyId,
                    getWorkflowWebhookTriggersRequest.WorkflowId));

        return Ok(getWorkflowWebhookTriggersOutputOutput);
    }

    public class CreateWorkflowWebhookTriggerRequest
    {
        [JsonProperty("workflow_id")]
        public string WorkflowId { get; set; }

        [JsonProperty("object_id_expression")]
        public string ObjectIdExpression { get; set; }

        [JsonProperty("object_type")]
        public string ObjectType { get; set; }
    }

    [HttpPost("Workflows/CreateWorkflowWebhookTrigger")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<CreateWorkflowWebhookTriggerOutputOutput>> CreateWorkflowWebhookTrigger(
        [FromBody]
        CreateWorkflowWebhookTriggerRequest createWorkflowWebhookTriggerRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var createWorkflowWebhookTriggerOutputOutput =
            await _workflowsApi.WorkflowsCreateWorkflowWebhookTriggerPostAsync(
                createWorkflowWebhookTriggerInput: new CreateWorkflowWebhookTriggerInput(
                    staff.CompanyId,
                    createWorkflowWebhookTriggerRequest.WorkflowId,
                    createWorkflowWebhookTriggerRequest.ObjectIdExpression,
                    createWorkflowWebhookTriggerRequest.ObjectType,
                    staff.Id.ToString(),
                    await GetTeamIdsBySleekflowStaffAsync(staff)));

        return Ok(createWorkflowWebhookTriggerOutputOutput);
    }

    public class UpdateWorkflowWebhookTriggerRequest
    {
        [JsonProperty("workflow_webhook_trigger_id")]
        public string WorkflowWebhookTriggerId { get; set; }

        [JsonProperty("object_id_expression")]
        public string ObjectIdExpression { get; set; }

        [JsonProperty("object_type")]
        public string ObjectType { get; set; }
    }

    [HttpPost("Workflows/UpdateWorkflowWebhookTrigger")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<UpdateWorkflowWebhookTriggerOutputOutput>> UpdateWorkflowWebhookTrigger(
        [FromBody] UpdateWorkflowWebhookTriggerRequest updateWorkflowWebhookTriggerRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (staff == null)
        {
            return Unauthorized();
        }

        var updateWorkflowWebhookTriggerOutputOutput =
            await _workflowsApi.WorkflowsUpdateWorkflowWebhookTriggerPostAsync(
                updateWorkflowWebhookTriggerInput: new UpdateWorkflowWebhookTriggerInput(
                    staff.CompanyId,
                    updateWorkflowWebhookTriggerRequest.WorkflowWebhookTriggerId,
                    updateWorkflowWebhookTriggerRequest.ObjectIdExpression,
                    updateWorkflowWebhookTriggerRequest.ObjectType,
                    staff.Id.ToString(),
                    await GetTeamIdsBySleekflowStaffAsync(staff)));

        return Ok(updateWorkflowWebhookTriggerOutputOutput);
    }


    public class GetWorkflowWebhookUrlOutput
    {
        [JsonProperty("workflow_webhook_trigger_url")]
        public string WorkflowWebhookTriggerUrl { get; set; }

        public GetWorkflowWebhookUrlOutput(string workflowWebhookTriggerUrl)
        {
            WorkflowWebhookTriggerUrl = workflowWebhookTriggerUrl;
        }
    }

    [HttpGet("Workflows/GetWorkflowWebhookTriggerUrl")]
    public ActionResult<GetWorkflowWebhookUrlOutput> GetWorkflowWebhookTriggerUrl()
    {
        var workflowWebhookTriggerUrl = new Uri(_workflowsApi.Configuration.BasePath)
            .AppendPath("Public/e")
            .AbsoluteUri;

        return new GetWorkflowWebhookUrlOutput(workflowWebhookTriggerUrl);
    }

    public class DeleteVersionedWorkflowRequest
    {
        [JsonProperty("workflow_id")]
        public string WorkflowId { get; set; }

        [JsonProperty("workflow_versioned_id")]
        public string WorkflowVersionedId { get; set; }
    }

    [HttpPost("Workflows/DeleteVersionedWorkflow")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<DeleteVersionedWorkflowOutputOutput>> DeleteVersionedWorkflow(
        [FromBody]
        DeleteVersionedWorkflowRequest deleteVersionedWorkflowRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var deleteVersionedWorkflowOutputOutput = await _workflowsApi.WorkflowsDeleteVersionedWorkflowPostAsync(
            deleteVersionedWorkflowInput: new DeleteVersionedWorkflowInput(
                staff.CompanyId,
                deleteVersionedWorkflowRequest.WorkflowId,
                deleteVersionedWorkflowRequest.WorkflowVersionedId,
                staff.Id.ToString(),
                await GetTeamIdsBySleekflowStaffAsync(staff)));

        return Ok(deleteVersionedWorkflowOutputOutput);
    }

    public class DeleteWorkflowRequest
    {
        [JsonProperty("workflow_id")]
        public string WorkflowId { get; set; }
    }

    [HttpPost("Workflows/DeleteWorkflow")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<DeleteWorkflowOutputOutput>> DeleteWorkflow(
        [FromBody]
        DeleteWorkflowRequest deleteWorkflowRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var deleteWorkflowOutputOutput = await _workflowsApi.WorkflowsDeleteWorkflowPostAsync(
            deleteWorkflowInput: new DeleteWorkflowInput(
                staff.CompanyId,
                deleteWorkflowRequest.WorkflowId,
                staff.Id.ToString(),
                await GetTeamIdsBySleekflowStaffAsync(staff)));

        if (deleteWorkflowOutputOutput.Success)
        {
            _flowHubScheduledWorkflowEnrollmentService.CancelScheduledWorkflowEnrollment(
                staff.CompanyId,
                deleteWorkflowRequest.WorkflowId);
        }

        return Ok(deleteWorkflowOutputOutput);
    }

    public class ScheduleDeleteWorkflowsRequest
    {
        [JsonProperty("workflow_ids")]
        public List<string> WorkflowIds { get; set; }
    }

    [HttpPost("Workflows/ScheduleDeleteWorkflows")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<ScheduleDeleteWorkflowsOutputOutput>> ScheduleDeleteWorkflows(
        [FromBody]
        ScheduleDeleteWorkflowsRequest scheduleDeleteWorkflowsRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var scheduleDeleteWorkflowsOutputOutput = await _workflowsApi.WorkflowsScheduleDeleteWorkflowsPostAsync(
            scheduleDeleteWorkflowsInput: new ScheduleDeleteWorkflowsInput(
                staff.CompanyId,
                scheduleDeleteWorkflowsRequest.WorkflowIds,
                staff.Id.ToString(),
                await GetTeamIdsBySleekflowStaffAsync(staff)));

        if (scheduleDeleteWorkflowsOutputOutput.Success)
        {
            foreach (var workflowId in scheduleDeleteWorkflowsRequest.WorkflowIds)
            {
                _flowHubScheduledWorkflowEnrollmentService.CancelScheduledWorkflowEnrollment(staff.CompanyId, workflowId);
            }
        }

        return Ok(scheduleDeleteWorkflowsOutputOutput);
    }

    public class DisableWorkflowRequest
    {
        [JsonProperty("workflow_versioned_id")]
        public string WorkflowVersionedId { get; set; }
    }

    [HttpPost("Workflows/DisableWorkflow")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<DisableWorkflowOutputOutput>> DisableWorkflow(
        [FromBody]
        DisableWorkflowRequest disableWorkflowRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var disableWorkflowOutputOutput = await _workflowsApi.WorkflowsDisableWorkflowPostAsync(
            disableWorkflowInput: new DisableWorkflowInput(
                staff.CompanyId,
                disableWorkflowRequest.WorkflowVersionedId,
                staff.Id.ToString(),
                await GetTeamIdsBySleekflowStaffAsync(staff)));

        if (disableWorkflowOutputOutput.Success)
        {
            _flowHubScheduledWorkflowEnrollmentService.CancelScheduledWorkflowEnrollment(
                staff.CompanyId,
                disableWorkflowOutputOutput.Data.Workflow.WorkflowId);
        }

        return Ok(disableWorkflowOutputOutput);
    }

    public class DuplicateWorkflowRequest
    {
        [JsonProperty("workflow_id")]
        public string WorkflowId { get; set; }
    }

    [HttpPost("Workflows/DuplicateWorkflow")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<DuplicateWorkflowOutputOutput>> DuplicateWorkflow(
        [FromBody]
        DuplicateWorkflowRequest duplicateWorkflowRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var duplicateWorkflowOutputOutput = await _workflowsApi.WorkflowsDuplicateWorkflowPostAsync(
            duplicateWorkflowInput: new DuplicateWorkflowInput(
                staff.CompanyId,
                duplicateWorkflowRequest.WorkflowId,
                staff.Id.ToString(),
                await GetTeamIdsBySleekflowStaffAsync(staff)));

        return Ok(duplicateWorkflowOutputOutput);
    }

    public class EnableWorkflowRequest
    {
        [JsonProperty("workflow_versioned_id")]
        public string WorkflowVersionedId { get; set; }
    }

    [HttpPost("Workflows/EnableWorkflow")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<EnableWorkflowOutputOutput>> EnableWorkflow(
        [FromBody]
        EnableWorkflowRequest enableWorkflowRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var enableWorkflowOutputOutput = await _workflowsApi.WorkflowsEnableWorkflowPostAsync(
            enableWorkflowInput: new EnableWorkflowInput(
                staff.CompanyId,
                enableWorkflowRequest.WorkflowVersionedId,
                staff.Id.ToString(),
                await GetTeamIdsBySleekflowStaffAsync(staff)));

        if (enableWorkflowOutputOutput.Success)
        {
            _flowHubScheduledWorkflowEnrollmentService.EnqueueScheduledWorkflowEnrollment(enableWorkflowOutputOutput.Data.Workflow);
        }

        return Ok(enableWorkflowOutputOutput);
    }

    public class GetWorkflowRequest
    {
        [JsonProperty("workflow_id")]
        public string WorkflowId { get; set; }
    }

    [HttpPost("Workflows/GetWorkflow")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GetWorkflowOutputOutput>> GetWorkflow(
        [FromBody]
        GetWorkflowRequest getWorkflowRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var getWorkflowOutputOutput = await _workflowsApi.WorkflowsGetWorkflowPostAsync(
            getWorkflowInput: new GetWorkflowInput(
                staff.CompanyId,
                getWorkflowRequest.WorkflowId));

        return Ok(getWorkflowOutputOutput);
    }

    public class GetActiveWorkflowRequest
    {
        [JsonProperty("workflow_id")]
        public string WorkflowId { get; set; }
    }

    [HttpPost("Workflows/GetActiveWorkflow")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GetActiveWorkflowOutputOutput>> GetActiveWorkflow(
        [FromBody]
        GetActiveWorkflowRequest getActiveWorkflowRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var getActiveWorkflowOutputOutput = await _workflowsApi.WorkflowsGetActiveWorkflowPostAsync(
            getActiveWorkflowInput: new GetActiveWorkflowInput(
                staff.CompanyId,
                getActiveWorkflowRequest.WorkflowId));

        return Ok(getActiveWorkflowOutputOutput);
    }

    public class GetLatestWorkflowRequest
    {
        [JsonProperty("workflow_id")]
        public string WorkflowId { get; set; }
    }

    [HttpPost("Workflows/GetLatestWorkflow")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GetLatestWorkflowOutputOutput>> GetLatestWorkflow(
        [FromBody]
        GetLatestWorkflowRequest getLatestWorkflowRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var getLatestWorkflowOutputOutput = await _workflowsApi.WorkflowsGetLatestWorkflowPostAsync(
            getLatestWorkflowInput: new GetLatestWorkflowInput(
                staff.CompanyId,
                getLatestWorkflowRequest.WorkflowId));

        return Ok(getLatestWorkflowOutputOutput);
    }

    public class GetWorkflowsRequest
    {
        [JsonProperty("continuation_token")]
        public string ContinuationToken { get; set; }

        [JsonProperty("limit")]
        public int Limit { get; set; }

        [JsonProperty("search_name")]
        public string SearchName { get; set; }

        [JsonProperty("workflow_filters")]
        public WorkflowFilters WorkflowFilters { get; set; }
    }

    [HttpPost("Workflows/GetWorkflows")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GetWorkflowsOutputOutput>> GetWorkflows(
        [FromBody] GetWorkflowsRequest getWorkflowsRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var getWorkflowsOutputOutput = await _workflowsApi.WorkflowsGetWorkflowsPostAsync(
            getWorkflowsInput: new GetWorkflowsInput(
                staff.CompanyId,
                getWorkflowsRequest.ContinuationToken,
                getWorkflowsRequest.Limit,
                getWorkflowsRequest.SearchName,
                getWorkflowsRequest.WorkflowFilters));

        return Ok(getWorkflowsOutputOutput);
    }

    public class GetVersionedWorkflowRequest
    {
        [JsonProperty("versioned_workflow_id")]
        public string VersionedWorkflowId { get; set; }
    }

    [HttpPost("Workflows/GetVersionedWorkflow")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GetVersionedWorkflowOutputOutput>> GetVersionedWorkflow(
        [FromBody]
        GetVersionedWorkflowRequest request)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (staff == null)
        {
            return Unauthorized();
        }

        var getVersionedWorkflowOutputOutput = await _workflowsApi.WorkflowsGetVersionedWorkflowPostAsync(
            getVersionedWorkflowInput: new GetVersionedWorkflowInput(
                staff.CompanyId,
                request.VersionedWorkflowId));

        return Ok(getVersionedWorkflowOutputOutput);
    }

    public class CountWorkflowsRequest
    {
        [JsonProperty("workflow_type")]
        public string? WorkflowType { get; set; }
    }

    [HttpPost("Workflows/CountWorkflows")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<CountWorkflowsOutputOutput>> CountWorkflows(
        [FromBody]
        CountWorkflowsRequest countWorkflowsRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var countWorkflowsOutputOutput = await _workflowsApi.WorkflowsCountWorkflowsPostAsync(
            countWorkflowsInput: new CountWorkflowsInput(staff.CompanyId, countWorkflowsRequest.WorkflowType!));

        return Ok(countWorkflowsOutputOutput);
    }


    public class SwapWorkflowsRequest
    {
        [JsonProperty("source_workflow_versioned_id")]
        public string SourceWorkflowVersionedId { get; set; }

        [JsonProperty("target_workflow_versioned_id")]
        public string TargetWorkflowVersionedId { get; set; }
    }

    [HttpPost("Workflows/SwapWorkflows")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<SwapWorkflowsOutputOutput>> SwapWorkflows(
        [FromBody]
        SwapWorkflowsRequest swapWorkflowsRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var swapWorkflowsOutputOutput = await _workflowsApi.WorkflowsSwapWorkflowsPostAsync(
            swapWorkflowsInput: new SwapWorkflowsInput(
                staff.CompanyId,
                swapWorkflowsRequest.SourceWorkflowVersionedId,
                swapWorkflowsRequest.TargetWorkflowVersionedId,
                staff.Id.ToString(),
                await GetTeamIdsBySleekflowStaffAsync(staff)));

        if (swapWorkflowsOutputOutput.Success)
        {
            _flowHubScheduledWorkflowEnrollmentService.CancelScheduledWorkflowEnrollment(
                staff.CompanyId,
                swapWorkflowsOutputOutput.Data.TargetWorkflow.WorkflowId);

            _flowHubScheduledWorkflowEnrollmentService.EnqueueScheduledWorkflowEnrollment(swapWorkflowsOutputOutput.Data.TargetWorkflow);
        }

        return Ok(swapWorkflowsOutputOutput);
    }

    public class UpdateWorkflowRequest
    {
        [JsonProperty("workflow_id")]
        public string WorkflowId { get; set; }

        [JsonProperty("triggers")]
        public WorkflowTriggers Triggers { get; set; }

        [JsonProperty("workflow_enrollment_settings")]
        public WorkflowEnrollmentSettings WorkflowEnrollmentSettings { get; set; }

        [JsonProperty("workflow_schedule_settings")]
        public WorkflowScheduleSettings WorkflowScheduleSettings { get; set; }

        [JsonProperty("steps")]
        public List<JObject> Steps { get; set; }

        [JsonProperty("name")]
        public string Name { get; set; }

        [JsonProperty("workflow_group_id")]
        public string WorkflowGroupId { get; set; }

        [JsonProperty("metadata")]
        public Dictionary<string, object?> Metadata { get; set; }
    }

    [HttpPost("Workflows/UpdateWorkflow")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<UpdateWorkflowOutputOutput>> UpdateWorkflow(
        [FromBody]
        UpdateWorkflowRequest updateWorkflowRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var updateWorkflowOutputOutput = await _workflowsApi.WorkflowsUpdateWorkflowPostAsync(
            updateWorkflowInput: new UpdateWorkflowInput(
                staff.CompanyId,
                updateWorkflowRequest.WorkflowId,
                updateWorkflowRequest.Triggers,
                updateWorkflowRequest.WorkflowEnrollmentSettings,
                updateWorkflowRequest.WorkflowScheduleSettings,
                updateWorkflowRequest.Steps.Select(s => (object) s).ToList(),
                updateWorkflowRequest.Name,
                updateWorkflowRequest.WorkflowGroupId,
                staff.Id.ToString(),
                await GetTeamIdsBySleekflowStaffAsync(staff),
                updateWorkflowRequest.Metadata));

        return Ok(updateWorkflowOutputOutput);
    }

    public class GetManualEnrollmentStatusRequest
    {
        [JsonProperty("workflow_id")]
        public string WorkflowId { get; set; }
    }

    public class GetManualEnrollmentStatusResponse
    {
        [JsonProperty("status")]
        public string Status { get; set; }
    }

    [HttpPost("Workflows/GetManualEnrollmentStatus")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GetManualEnrollmentStatusResponse>> GetManualEnrollmentStatus(
        [FromBody]
        GetManualEnrollmentStatusRequest request)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (staff == null)
        {
            return Unauthorized();
        }

        var manualEnrollmentStatus = _flowHubScheduledWorkflowEnrollmentService.GetScheduledWorkflowEnrollmentStatus(
            staff.CompanyId,
            request.WorkflowId);

        return Ok(
            new GetManualEnrollmentStatusResponse
            {
                Status = manualEnrollmentStatus
            });
    }

    public class ManualEnrollWorkflowRequest
    {
        [Required]
        [JsonProperty("workflow_versioned_id")]
        public string WorkflowVersionedId { get; set; }

        [JsonProperty("user_profile_ids")]
        public List<string> UserProfileIds { get; set; }

        [JsonProperty("contact_list_ids")]
        public List<long> ContactListIds { get; set; }
    }

    public class ManualEnrollWorkflowResponse
    {
        [JsonProperty("success")]
        public bool Success { get; set; }

        [JsonProperty("message")]
        public string Message { get; set; }
    }

    [HttpPost("Workflows/ManualEnroll")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<ManualEnrollWorkflowResponse>> ManualEnrollWorkflow(
        [FromBody]
        ManualEnrollWorkflowRequest request)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (staff is null)
        {
            return Unauthorized();
        }

        if (request.UserProfileIds is not { Count: > 0 }
            && request.ContactListIds is not { Count: > 0 })
        {
            return BadRequest(
                new ManualEnrollWorkflowResponse
                {
                    Success = false,
                    Message = "At least one of user profile ids or contact list ids must be provided."
                });
        }

        var getVersionedWorkflowOutputOutput = await _workflowsApi.WorkflowsGetVersionedWorkflowPostAsync(
            getVersionedWorkflowInput: new GetVersionedWorkflowInput(
                staff.CompanyId,
                request.WorkflowVersionedId));

        var workflow = getVersionedWorkflowOutputOutput.Data.Workflow;

        if (workflow is null)
        {
            return BadRequest(
                new ManualEnrollWorkflowResponse
                {
                    Success = false,
                    Message = "Workflow not found."
                });
        }

        if (workflow is not { ActivationStatus: "Active" })
        {
            return BadRequest(
                new ManualEnrollWorkflowResponse
                {
                    Success = false,
                    Message = "Versioned workflow is not active."
                });
        }

        if (workflow is
            {
                Triggers.ContactManuallyEnrolled: null
            })
        {
            return BadRequest(
                new ManualEnrollWorkflowResponse
                {
                    Success = false,
                    Message = "Workflow does not support manual enrollment."
                });
        }

        _flowHubScheduledWorkflowEnrollmentService.EnqueueManualWorkflowEnrollment(
            workflow,
            staff,
            request.UserProfileIds,
            request.ContactListIds);

        return Ok(new ManualEnrollWorkflowResponse
        {
            Success = true
        });
    }

    public class CancelManualEnrollWorkflowRequest
    {
        [Required]
        [JsonProperty("workflow_id")]
        public string WorkflowId { get; set; }
    }

    [HttpPost("Workflows/CancelManualEnroll")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<ManualEnrollWorkflowResponse>> CancelManualEnrollWorkflow(
        [FromBody]
        CancelManualEnrollWorkflowRequest request)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (staff is null)
        {
            return Unauthorized();
        }

        _flowHubScheduledWorkflowEnrollmentService.CancelScheduledWorkflowEnrollment(
            staff.CompanyId,
            request.WorkflowId);

        return Ok(new ManualEnrollWorkflowResponse
        {
            Success = true
        });
    }

    public class AssignWorkflowToGroupRequest
    {
        [JsonProperty("workflow_versioned_id")]
        public string WorkflowVersionedId { get; set; }

        [JsonProperty("workflow_group_id")]
        public string WorkflowGroupId { get; set; }
    }

    [HttpPost("Workflows/AssignWorkflowToGroup")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<AssignWorkflowToGroupOutputOutput>> AssignWorkflowToGroup(
        [FromBody]
        AssignWorkflowToGroupRequest assignWorkflowToGroupRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (staff == null)
        {
            return Unauthorized();
        }

        var assignWorkflowToGroupOutputOutput =
            await _workflowsApi.WorkflowsAssignWorkflowToGroupPostAsync(
                assignWorkflowToGroupInput: new AssignWorkflowToGroupInput(
                    assignWorkflowToGroupRequest.WorkflowVersionedId,
                    assignWorkflowToGroupRequest.WorkflowGroupId,
                    staff.CompanyId,
                    staff.Id.ToString(),
                    await GetTeamIdsBySleekflowStaffAsync(staff)));

        return Ok(assignWorkflowToGroupOutputOutput);
    }

    public class GetWorkflowGroupsRequest
    {
    }

    [HttpPost("WorkflowGroups/GetWorkflowGroups")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GetWorkflowGroupsOutputOutput>> GetWorkflowGroups(
        [FromBody]
        GetWorkflowGroupsRequest getWorkflowGroupsRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (staff == null)
        {
            return Unauthorized();
        }

        var getWorkflowGroupsOutputOutput =
            await _workflowGroupsApi.WorkflowGroupsGetWorkflowGroupsPostAsync(
                getWorkflowGroupsInput: new GetWorkflowGroupsInput(staff.CompanyId));

        return Ok(getWorkflowGroupsOutputOutput);
    }

    public class CreateWorkflowGroupRequest
    {
        [JsonProperty("workflow_group_name")]
        public string WorkflowGroupName { get; set; }
    }

    [HttpPost("WorkflowGroups/CreateWorkflowGroup")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<CreateWorkflowGroupOutputOutput>> CreateWorkflowGroup(
        [FromBody]
        CreateWorkflowGroupRequest createWorkflowGroupRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (staff == null)
        {
            return Unauthorized();
        }

        var createWorkflowGroupOutputOutput =
            await _workflowGroupsApi.WorkflowGroupsCreateWorkflowGroupPostAsync(
                createWorkflowGroupInput: new CreateWorkflowGroupInput(
                    createWorkflowGroupRequest.WorkflowGroupName,
                    staff.CompanyId,
                    staff.Id.ToString(),
                    await GetTeamIdsBySleekflowStaffAsync(staff)));

        return Ok(createWorkflowGroupOutputOutput);
    }

    public class UpdateWorkflowGroupRequest
    {
        [JsonProperty("workflow_group_id")]
        public string WorkflowGroupId { get; set; }

        [JsonProperty("workflow_group_name")]
        public string WorkflowGroupName { get; set; }
    }

    [HttpPost("WorkflowGroups/UpdateWorkflowGroup")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<UpdateWorkflowGroupOutputOutput>> UpdateWorkflowGroup(
        [FromBody]
        UpdateWorkflowGroupRequest updateWorkflowGroupRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (staff == null)
        {
            return Unauthorized();
        }

        var updateWorkflowGroupOutputOutput =
            await _workflowGroupsApi.WorkflowGroupsUpdateWorkflowGroupPostAsync(
                updateWorkflowGroupInput: new UpdateWorkflowGroupInput(
                    updateWorkflowGroupRequest.WorkflowGroupId,
                    updateWorkflowGroupRequest.WorkflowGroupName,
                    staff.CompanyId,
                    staff.Id.ToString(),
                    await GetTeamIdsBySleekflowStaffAsync(staff)));

        return Ok(updateWorkflowGroupOutputOutput);
    }

    public class DeleteWorkflowGroupRequest
    {
        [JsonProperty("workflow_group_id")]
        public string WorkflowGroupId { get; set; }
    }

    [HttpPost("WorkflowGroups/DeleteWorkflowGroup")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<DeleteWorkflowGroupOutputOutput>> DeleteWorkflowGroup(
        [FromBody]
        DeleteWorkflowGroupRequest deleteWorkflowGroupRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (staff == null)
        {
            return Unauthorized();
        }

        var deleteWorkflowGroupOutputOutput =
            await _workflowGroupsApi.WorkflowGroupsDeleteWorkflowGroupPostAsync(
                deleteWorkflowGroupInput: new DeleteWorkflowGroupInput(
                    deleteWorkflowGroupRequest.WorkflowGroupId,
                    staff.CompanyId,
                    staff.Id.ToString(),
                    await GetTeamIdsBySleekflowStaffAsync(staff)));

        return Ok(deleteWorkflowGroupOutputOutput);
    }
}