﻿using System;
using Travis_backend.Enums;

namespace Travis_backend.InternalDomain.Models
{
    public class CompanySnapshotData
    {
        public string CompanyId { get; set; }

        public string CompanyName { get; set; }

        public string HubSpotCompanyObjectId { get; set; }

        public int WhatsAppChatApiCount { get; set; }

        public int WhatsAppOfficialChannelCount { get; set; }

        public int WhatsAppConfigCount { get; set; }

        public int WhatsApp360DialogConfigCount { get; set; }

        public int WhatsappCloudApiConfigCount { get; set; }

        public int LineCount { get; set; }

        public int LiveChat { get; set; }

        public int FacebookMessengerCount { get; set; }

        public int InstagramMessengerCount { get; set; }

        public int SmsCount { get; set; }

        public int EmailCount { get; set; }

        public int TelegramCount { get; set; }

        public int ViberCount { get; set; }

        public int WeChatCount { get; set; }

        public int ShoplineCount { get; set; }

        public int ShopifyCount { get; set; }

        public int ApiKeyCount { get; set; }

        public int ZapierIntegrationCount { get; set; }

        public int StripePaymentCount { get; set; }

        public int SupportTicketCount { get; set; }

        public int SupportTicketCountInPastTwoWeek { get; set; }

        public int SupportTicketCountInPastMonth { get; set; }

        public string ActivationOwnerName { get; set; }

        public string CmsCompanyOwnerOwnerName { get; set; }

        public string HubSpotCompanyOwnerId { get; set; }

        public string HubSpotActivationOwnerId { get; set; }

        public string CompanyCountry { get; set; }

        public string CmsLeadSource { get; set; }

        public string CmsCompanyIndustry { get; set; }

        public decimal MonthlyRecurringRevenue { get; set; }

        public string SubscriptionPlan { get; set; }

        public DateTime SubscriptionPlanStartDate { get; set; }

        public DateTime SubscriptionPlanEndDate { get; set; }

        public string AddOnPlans { get; set; }

        public bool IsBankTransfer { get; set; }

        public string CompanyOwnerName { get; set; }

        public string CompanyOwnerPhoneEmail { get; set; }

        public string CompanyOwnerPhoneNumber { get; set; }

        public int StaffCount { get; set; }

        public int StaffCountLimit { get; set; }

        public int ContactCount { get; set; }

        public int ContactCountLimit { get; set; }

        public int AutomationRuleCount { get; set; }

        public int LiveAutomationRuleCount { get; set; }

        public int AutomationRuleCountLimit { get; set; }

        public DateTime CreateAt { get; set; }

        public DateTime? LastStaffLoginAt { get; set; }

        public string TwilioAccountSid { get; set; }

        public decimal? TwilioBalance { get; set; }

        public int ConversationCount { get; set; }

        public int BroadcastCount { get; set; }

        public int NewEnquiriesInPastDay { get; set; }

        public int NewContactsInPastDay { get; set; }

        public int ActiveConversationsInPastDay { get; set; }

        public int NumberOfConnectedChannels { get; set; }

        public int TypesOfChannels { get; set; }

        public int TypesOfSoftwareIntegrations { get; set; }

        public float? ActiveConversationsDifference { get; set; }

        public float? NewContactsDifference { get; set; }

        public float? NewEnquiriesDifference { get; set; }

        public int BroadcastCountInPastMonth { get; set; }

        public int ActiveAgentsInPastDay { get; set; }

        public int PaymentFailedCount { get; set; }

        public int PaymentFailedCountInPastThreeMonth { get; set; }

        public DateTime? LastAgentMessageSentAt { get; set; }

        public DateTime? InitialPaidDate { get; set; }

        public int? LeadTimeForSignupToPaid { get; set; }

        public decimal? InitialMonthlyRecurringRevenue { get; set; }

        public int? InboxMessagesInPast7Days { get; set; }

        public CompanyType? CompanyType { get; set; }

        public bool IsDeleted { get; set; }

        public string CommunicationTools { get; set; }

        public string Industry { get; set; }

        public string OnlineShopSystem { get; set; }

        public string CompanySize { get; set; }

        public string CompanyEmailDomain { get; set; }

        public string CompanyWebsite { get; set; }

        public string ChurnReason { get; set; }

        public string CompanyTier { get; set; }

        public string InitialPaidSubscriptionPlanId { get; set; }

        public string LastPaidSubscriptionPlanId { get; set; }

        public decimal TotalRevenue { get; set; } = 0;

        public decimal TotalSubscriptionPlanRevenue { get; set; } = 0;

        public decimal TotalOneTimeSetupFeeRevenue { get; set; } = 0;

        public decimal TotalMarkupRevenue { get; set; } = 0;

        public int ActiveContactsInPastMonth { get; set; }

        public int ActiveContactsInCurrentMonth { get; set; }

        public int IntegrationCount { get; set; }
    }
}