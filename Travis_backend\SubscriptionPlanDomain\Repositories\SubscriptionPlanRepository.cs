using System.Collections.Generic;
using System.Collections.Immutable;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Travis_backend.Database;
using Travis_backend.Enums;
using Travis_backend.SubscriptionPlanDomain.Models;

namespace Travis_backend.SubscriptionPlanDomain.Repositories;

/// <inheritdoc />
public class SubscriptionPlanRepository : ISubscriptionPlanRepository
{
    /// <summary>
    /// ApplicationDbContext
    /// </summary>
    private readonly ApplicationDbContext _appDbCtx;

    /// <summary>
    /// Initializes a new instance of the <see cref="SubscriptionPlanRepository"/> class.
    /// </summary>
    /// <param name="appDbCtx">ApplicationDbContext</param>
    public SubscriptionPlanRepository(ApplicationDbContext appDbCtx)
    {
        _appDbCtx = appDbCtx;
    }

    /// <inheritdoc />
    public async Task<SubscriptionPlan> FindById(string id)
    {
        return await _appDbCtx.CoreSubscriptionPlans.FindAsync(id);
    }

    /// <inheritdoc />
    public async Task<SubscriptionPlan> FindByStripePlanIdAsync(string stripePlanId)
    {
        return await _appDbCtx.CoreSubscriptionPlans.FirstOrDefaultAsync(x => x.StripePlanId == stripePlanId);
    }

    /// <inheritdoc />
    public async Task<IEnumerable<SubscriptionPlan>> FindByIds(IEnumerable<string> ids)
    {
        return await _appDbCtx.CoreSubscriptionPlans.AsNoTracking()
            .Where(x => ids.Contains(x.Id))
            .ToListAsync();
    }

    /// <inheritdoc />
    public async Task<IEnumerable<SubscriptionPlan>> GetBaseSubscriptionPlans(int version, string countryTier, IEnumerable<string> currencies)
    {
        var tiers = ImmutableList.Create(SubscriptionTier.Pro, SubscriptionTier.Premium);

        return await _appDbCtx.CoreSubscriptionPlans.AsNoTracking()
            .Where(x =>
                x.Version == version &&
                x.CountryTier == countryTier &&
                currencies.Contains(x.Currency) &&
                tiers.Contains(x.SubscriptionTier))
            .ToListAsync();
    }

    /// <inheritdoc />
    public Task<SubscriptionPlan?> GetSubscriptionPlanAsync(SubscriptionTier subscriptionTier, string countryTier, string currency, string interval, int version)
    {
        return _appDbCtx.CoreSubscriptionPlans.AsNoTracking()
            .FirstOrDefaultAsync(x =>
                x.SubscriptionTier == subscriptionTier
                && x.CountryTier == countryTier
                && x.Currency == currency
                && x.Id.Contains(interval)
                && x.Version == version);
    }

    /// <inheritdoc />
    public async Task<SubscriptionPlan> GetContactAddOnPlan(SubscriptionTier subscriptionTier, string countryTier, string currency, string interval, int version)
    {
        return await GetAddOnPlanInternal("contact", subscriptionTier, countryTier, currency, interval, version);
    }

    /// <inheritdoc />
    public async Task<SubscriptionPlan> GetAgentAddOnPlan(SubscriptionTier subscriptionTier, string countryTier, string currency, string interval, int version)
    {
        return await GetAddOnPlanInternal("agent", subscriptionTier, countryTier, currency, interval, version);
    }

    /// <inheritdoc />
    public async Task<SubscriptionPlan> GetWhatsAppPhoneNumberAddOnPlan(string countryTier, string currency, string interval, int version)
    {
        return await _appDbCtx.CoreSubscriptionPlans.AsNoTracking()
            .FirstOrDefaultAsync(x =>
                x.Id.Contains("whatsapp_phone_number") &&
                !x.Id.Contains("pro") &&
                !x.Id.Contains("premium") &&
                !x.Id.Contains("enterprise") &&
                x.Id.Contains(interval) &&
                x.SubscriptionTier == SubscriptionTier.AddOn &&
                x.CountryTier == countryTier &&
                x.Currency == currency &&
                x.Version == version);
    }

    /// <inheritdoc />
    public async Task<SubscriptionPlan> GetBusinessConsultancyServicePlan(SubscriptionTier subscriptionTier, string countryTier, string currency, int version)
    {
        return await GetAddOnPlanInternal("business_consultancy_service", subscriptionTier, countryTier, currency, "yearly", version);
    }

    /// <inheritdoc />
    public async Task<SubscriptionPlan> GetOnboardingSupportPlan(SubscriptionTier subscriptionTier, string countryTier, string currency, int version)
    {
        return await GetAddOnPlanInternal("onboarding_support", subscriptionTier, countryTier, currency, "oneoff", version);
    }

    /// <inheritdoc />
    public async Task<IEnumerable<SubscriptionPlan>> GetFlowBuilderFlowEnrolmentsPlans(SubscriptionTier subscriptionTier, string countryTier, string currency, string interval, int version)
    {
        return await GetAddOnPlansInternalAsync("flow_builder_flow_enrolments", subscriptionTier, countryTier, currency, interval, version);
    }

    private async Task<SubscriptionPlan> GetAddOnPlanInternal(string idKeyword, SubscriptionTier subscriptionTier, string countryTier, string currency, string interval, int version)
    {
        var strSubscriptionTier = subscriptionTier.ToString();
        var addOnTiers = ImmutableList.Create(SubscriptionTier.AddOn, SubscriptionTier.Agent);

        return await _appDbCtx.CoreSubscriptionPlans.AsNoTracking()
            .FirstOrDefaultAsync(x =>
                x.Id.Contains(idKeyword) &&
                x.Id.Contains(strSubscriptionTier) &&
                x.Id.Contains(interval) &&
                addOnTiers.Contains(x.SubscriptionTier) &&
                x.CountryTier == countryTier &&
                x.Currency == currency &&
                x.Version == version);
    }

    private async Task<IEnumerable<SubscriptionPlan>> GetAddOnPlansInternalAsync(string idKeyword, SubscriptionTier subscriptionTier, string countryTier, string currency, string interval, int version)
    {
        var strSubscriptionTier = subscriptionTier.ToString();
        var addOnTiers = ImmutableList.Create(SubscriptionTier.AddOn, SubscriptionTier.Agent);

        return await _appDbCtx.CoreSubscriptionPlans.AsNoTracking()
            .Where(x =>
                x.Id.Contains(idKeyword) &&
                x.Id.Contains(strSubscriptionTier) &&
                x.Id.Contains(interval) &&
                addOnTiers.Contains(x.SubscriptionTier) &&
                x.CountryTier == countryTier &&
                x.Currency == currency &&
                x.Version == version)
            .ToListAsync();
    }
}