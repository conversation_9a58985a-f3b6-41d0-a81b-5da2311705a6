﻿using AutoMapper;
using Travis_backend.AccountAuthenticationDomain.Models;
using Travis_backend.ChannelDomain.Models;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.InternalDomain.Models;
using Travis_backend.InternalDomain.ViewModels;
using Travis_backend.MessageDomain.Models;

namespace Travis_backend.Mapping
{
    public class InternalCmsMappingProfile : Profile
    {
        public InternalCmsMappingProfile()
        {
            CreateMap<Company, CmsCompanyResponse>();

            CreateMap<BillRecord, CmsBillRecordDto>()
                .ForMember(
                    dest =>
                        dest.PurchaseStaffName,
                    opt =>
                        opt.MapFrom(src => src.PurchaseStaff.Identity.DisplayName))
                .ForMember(
                    dest =>
                        dest.SubscriptionPlanName,
                    opt =>
                        opt.MapFrom(src => src.SubscriptionPlan.SubscriptionName));

            CreateMap<BillRecord, CmsBillRecordLiteDto>();
            CreateMap<CmsSalesPaymentRecord, CmsSalesPaymentRecordDto>();
            CreateMap<CmsSalesPaymentRecordFile, CmsSalesPaymentRecordFileDto>();
            CreateMap<CmsSalesPaymentRecord, CmsSalesPaymentRecordSnapshotData>();
            CreateMap<WebClientSender, CmsWebClientSenderDto>();

            CreateMap<ApplicationUser, DivingUserInfoResponse>();
            CreateMap<ApplicationUser, CmsUserDto>();

            CreateMap<BillRecord, CmsAnalyticBillRecordDto>();
            CreateMap<CmsSalesPaymentRecord, CmsAnalyticSalesPaymentRecordDto>();
            CreateMap<CmsContactOwnerAssignLog, CmsContactOwnerAssignLogDto>()
                .ForMember(
                    dest =>
                        dest.FromContactOwnerName,
                    opt =>
                        opt.MapFrom(src => src.FromContactOwner.DisplayName))
                .ForMember(
                    dest =>
                        dest.ToContactOwnerName,
                    opt =>
                        opt.MapFrom(src => src.ToContactOwner.DisplayName))
                .ForMember(
                    dest =>
                        dest.AssignedByUserName,
                    opt =>
                        opt.MapFrom(src => src.AssignedByUser.DisplayName));

            CreateMap<Company, CmsCompanyAnalyticDto>()
                .ForMember(
                    dest =>
                        dest.CmsActivationOwnerName,
                    opt =>
                        opt.MapFrom(src => src.CmsActivationOwner.DisplayName))
                .ForMember(
                    dest =>
                        dest.CmsCompanyOwnerName,
                    opt =>
                        opt.MapFrom(src => src.CmsCompanyOwner.DisplayName));

            CreateMap<CmsWhatsappApplication, CmsWhatsappApplicationDto>()
                .ForMember(
                    dest =>
                        dest.CmsCompanyName,
                    opt =>
                        opt.MapFrom(src => src.Company.CompanyName))
                .ForMember(
                    dest =>
                        dest.Country,
                    opt =>
                        opt.MapFrom(src => src.Country ?? src.Company.CompanyCountry))
                .ForMember(
                    dest =>
                        dest.ContactOwnerName,
                    opt =>
                        opt.MapFrom(src => src.ContactOwner.DisplayName ?? null))
                .ForMember(
                    dest =>
                        dest.CreatedByUserName,
                    opt =>
                        opt.MapFrom(src => src.CreatedByUser.DisplayName ?? null))
                .ForMember(
                    dest =>
                        dest.UpdatedByUserName,
                    opt =>
                        opt.MapFrom(src => src.UpdatedByUser.DisplayName ?? null));

            CreateMap<WhatsApp360DialogUsageRecord, CmsWhatsApp360DialogUsageRecordViewModel>();
            CreateMap<WhatsApp360DialogUsageTransactionLog, WhatsApp360DialogUsageTransactionLogDto>();

            CreateMap<CmsBillRecordDto, CmsAnalyticBillRecordDto>();

            CreateMap<CmsCompanyAdditionalInfo, CmsCompanyAdditionalInfoViewModel>();
        }
    }
}