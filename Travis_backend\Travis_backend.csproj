﻿<Project Sdk="Microsoft.NET.Sdk.Web">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <AspNetCoreHostingModel>InProcess</AspNetCoreHostingModel>
        <UserSecretsId>be0b83fc-5edd-403a-98ff-83f8a5992c36</UserSecretsId>
        <IncludeOpenAPIAnalyzers>true</IncludeOpenAPIAnalyzers>
        <GenerateDocumentationFile>true</GenerateDocumentationFile>
        <NoWarn>1591</NoWarn>
        <Configurations>Debug;Release;QA;ETA;OBSOLETE_AUTH</Configurations>
        <Platforms>AnyCPU</Platforms>
    </PropertyGroup>

    <PropertyGroup>
        <AddRazorSupportForMvc>true</AddRazorSupportForMvc>
    </PropertyGroup>

    <PropertyGroup Condition=" '$(Configuration)' == 'Release' ">
        <PlatformTarget>x64</PlatformTarget>
        <ServerGarbageCollection>true</ServerGarbageCollection>
    </PropertyGroup>
    <PropertyGroup Condition=" '$(Configuration)' == 'QA' ">
        <DefineConstants>TRACE;DEBUG</DefineConstants>
    </PropertyGroup>
    <PropertyGroup Condition=" '$(Configuration)' == 'ETA' ">
        <DefineConstants>OBSOLETE_AUTH</DefineConstants>
    </PropertyGroup>
    <ItemGroup>
        <PackageReference Include="AutoMapper" Version="13.0.1" />
        <PackageReference Include="Azure.Monitor.OpenTelemetry.Exporter" Version="1.3.0" />
        <PackageReference Include="Azure.ResourceManager" Version="1.12.0" />
        <PackageReference Include="Azure.ResourceManager.Media" Version="1.3.0" />
        <PackageReference Include="Azure.Storage.Blobs" Version="12.21.2" />
        <PackageReference Include="CsvHelper" Version="33.0.1" />
        <PackageReference Include="DeepCloner" Version="0.10.4" />
        <PackageReference Include="Google.Cloud.Storage.V1" Version="4.10.0" />
        <PackageReference Include="Google.Cloud.Video.Transcoder.V1" Version="2.9.0" />
        <PackageReference Include="Hangfire" Version="1.8.14" />
        <PackageReference Include="Indicia.HubSpot" Version="1.3.0" />
        <PackageReference Include="IPAddressRange" Version="6.0.0" />
        <PackageReference Include="IPGeoLocation.IPGeolocation" Version="1.1.0" />
        <PackageReference Include="JsonSubTypes" Version="2.0.1" />
        <PackageReference Include="libphonenumber-csharp" Version="8.13.43" />
        <PackageReference Include="LinqKit.Core" Version="1.2.5" />
        <PackageReference Include="LinqKit.Microsoft.EntityFrameworkCore" Version="8.1.5" />
        <PackageReference Include="Microsoft.AspNetCore.AzureKeyVault.HostingStartup" Version="2.0.4" />
        <PackageReference Include="Microsoft.Azure.SignalR.Protocols" Version="1.26.0" />
        <PackageReference Include="Microsoft.EntityFrameworkCore.Proxies" Version="8.0.7" />
        <PackageReference Include="Microsoft.Extensions.Diagnostics.HealthChecks.EntityFrameworkCore" Version="8.0.7" />
        <PackageReference Include="Microsoft.IdentityModel.Protocols.OpenIdConnect" Version="8.0.1" />
        <PackageReference Include="Mime-Detective" Version="24.7.1" />
        <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
        <PackageReference Include="NJsonSchema" Version="11.0.2" />
        <PackageReference Include="NPOI" Version="2.5.6" />
        <PackageReference Include="Npoi.Mapper" Version="3.5.1" />
        <PackageReference Include="OpenTelemetry.Extensions.Hosting" Version="1.9.0" />
        <PackageReference Include="OpenTelemetry.Instrumentation.Hangfire" Version="1.9.0-beta.1" />
        <PackageReference Include="Polly" Version="8.4.1" />
        <PackageReference Include="Scrutor" Version="4.2.2" />
        <PackageReference Include="Sendgrid" Version="9.29.3" />
        <PackageReference Include="Serilog" Version="4.0.1" />
        <PackageReference Include="Serilog.AspNetCore" Version="8.0.2" />
        <PackageReference Include="Serilog.Enrichers.Environment" Version="3.0.1" />
        <PackageReference Include="Serilog.Enrichers.Span" Version="3.1.0" />
        <PackageReference Include="Serilog.Sinks.Async" Version="2.0.0" />
        <PackageReference Include="Serilog.Sinks.AzureAnalytics" Version="5.0.0" />
        <PackageReference Include="Serilog.Sinks.Console" Version="6.0.0" />
        <PackageReference Include="Serilog.Sinks.GoogleCloudLogging" Version="5.0.0" />
        <PackageReference Include="ShopifySharp" Version="6.14.0" />
        <PackageReference Include="SixLabors.ImageSharp" Version="3.1.5" />
        <PackageReference Include="StackExchange.Redis" Version="2.8.0" />
        <PackageReference Include="Swashbuckle.AspNetCore" Version="6.7.0" />
        <PackageReference Include="Swashbuckle.AspNetCore.Newtonsoft" Version="6.7.0" />
        <PackageReference Include="Swashbuckle.AspNetCore.Swagger" Version="6.7.0" />
        <PackageReference Include="Swashbuckle.AspNetCore.SwaggerGen" Version="6.7.0" />
        <PackageReference Include="Swashbuckle.AspNetCore.SwaggerUI" Version="6.7.0" />
        <PackageReference Include="System.Linq.Async" Version="6.0.1" />
        <PackageReference Include="Telegram.Bot" Version="16.0.0" />
        <PackageReference Include="TimeZoneConverter" Version="6.1.0" />
        <PackageReference Include="Twilio" Version="6.3.0" />
        <PackageReference Include="Twilio.AspNet.Core" Version="5.37.2" />
        <PackageReference Include="WABA360Dialog.NET" Version="1.0.4" />
        <PackageReference Include="WindowsAzure.Storage" Version="9.3.3" />
        <PackageReference Include="Stripe.net" Version="39.126.0" />
        <PackageReference Include="LineBotSDK" Version="2.3.30" />
        <PackageReference Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore" Version="8.0.7" />
        <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.7" />
        <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="8.0.7" />
        <PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="8.0.7" />
        <PackageReference Include="Microsoft.AspNet.WebApi.Client" Version="6.0.0" />
        <PackageReference Include="Microsoft.AspNetCore.Identity.UI" Version="8.0.7" />
        <PackageReference Include="Microsoft.AspNetCore.Mvc.Razor.RuntimeCompilation" Version="8.0.7" />
        <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.7">
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
            <PrivateAssets>all</PrivateAssets>
        </PackageReference>
        <PackageReference Include="Microsoft.EntityFrameworkCore" Version="8.0.7" />
        <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="8.0.7">
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
            <PrivateAssets>all</PrivateAssets>
        </PackageReference>
        <PackageReference Include="Microsoft.EntityFrameworkCore.Relational" Version="8.0.7" />
        <PackageReference Include="Microsoft.Data.SqlClient" Version="5.2.1" />
        <PackageReference Include="Microsoft.AspNetCore.StaticFiles" Version="2.2.0" />
        <PackageReference Include="Microsoft.ApplicationInsights.AspNetCore" Version="2.22.0" />
        <PackageReference Include="Microsoft.ApplicationInsights.EventCounterCollector" Version="2.22.0" />
        <PackageReference Include="Microsoft.ApplicationInsights.Profiler.AspNetCore" Version="2.7.1" />
        <PackageReference Include="Microsoft.ApplicationInsights.SnapshotCollector" Version="1.4.6" />
        <PackageReference Include="Microsoft.Azure.NotificationHubs" Version="4.2.0" />
        <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="8.0.1" />
        <PackageReference Include="Microsoft.AspNetCore.AzureAppServicesIntegration" Version="8.0.7" />
        <PackageReference Include="vng.zalo.ZaloCSharpSDK" Version="1.0.14" />
        <PackageReference Include="Google.Apis.Auth" Version="1.68.0" />
        <PackageReference Include="Azure.AI.TextAnalytics" Version="5.3.0" />
        <PackageReference Include="EPPlus" Version="5.5.3" />
        <PackageReference Include="NReco.VideoConverter.LT" Version="1.2.1" />
        <PackageReference Include="Microsoft.AspNetCore.Session" Version="2.2.0" />
        <PackageReference Include="Microsoft.Extensions.Caching.StackExchangeRedis" Version="8.0.7" />
        <PackageReference Include="RestSharp" Version="106.15.0" />
        <PackageReference Include="Svg" Version="3.4.7" />
        <PackageReference Include="Humanizer" Version="2.14.1" />
        <PackageReference Include="Microsoft.AspNetCore.SignalR.Protocols.NewtonsoftJson" Version="8.0.7" />
        <PackageReference Include="Microsoft.Azure.SignalR" Version="1.26.0" />
        <PackageReference Include="RandomNameGeneratorLibrary" Version="1.2.2" />
    </ItemGroup>
    <ItemGroup>
        <PackageReference Include="StyleCop.Analyzers" Version="1.2.0-beta.435" PrivateAssets="all" IncludeAssets="runtime; build; native; contentfiles; analyzers; buildtransitive" Condition="$(MSBuildProjectExtension) == '.csproj'" />
        <PackageReference Include="SonarAnalyzer.CSharp" Version="9.31.0.96804" PrivateAssets="all" IncludeAssets="runtime; build; native; contentfiles; analyzers; buildtransitive" Condition="$(MSBuildProjectExtension) == '.csproj'" />
    </ItemGroup>

    <!-- https://github.com/dotnet/runtime/issues/62329 -->
    <ItemGroup Condition="'$(OS)' == 'Windows_NT'">
        <RuntimeHostConfigurationOption Include="System.Globalization.AppLocalIcu" Value="********" />
        <PackageReference Include="Microsoft.ICU.ICU4C.Runtime" Version="********" />
    </ItemGroup>


    <ItemGroup>
        <WCFMetadata Include="Connected Services" />
    </ItemGroup>


    <ItemGroup>
        <Folder Include="Binaries\Sleekflow.Apis.CommerceHub" />
        <Folder Include="Binaries\Sleekflow.Apis.CommerceHub\netstandard2.0\" />
        <Folder Include="Binaries\Sleekflow.Apis.CrmHub" />
        <Folder Include="Binaries\Sleekflow.Apis.IntelligentHub" />
        <Folder Include="Binaries\Sleekflow.Apis.InternalIntegrationHub\" />
        <Folder Include="Binaries\Sleekflow.Apis.WebhookHub" />
        <Folder Include="Binaries\Sleekflow.Apis.PublicApiGateway" />
        <Folder Include="Binaries\Sleekflow.Apis.ShareHub" />
        <Folder Include="Binaries\Sleekflow.Apis.TicketingHub\" />
        <Folder Include="Binaries\Sleekflow.Apis.TicketingHub\" />
        <Folder Include="Binaries\Sleekflow.Apis.UserEventHub\netstandard2.0\" />
        <Folder Include="FFmpegexe" />
        <Folder Include="Connected Services\" />
        <Folder Include="Configuration\" />
        <Folder Include="NotificationHubs\" />
        <Folder Include="OpenTelemetry\" />
    </ItemGroup>
    <ItemGroup>
        <None Remove="Properties\PublishProfiles\FolderProfile 4.pubxml" />
        <None Remove="Models\Shopify\" />
        <None Remove="Svg" />
        <None Remove="Humanizer" />
        <None Remove="Microsoft.AspNetCore.SignalR.Protocols.NewtonsoftJson" />
        <None Remove="Microsoft.Azure.SignalR" />
        <None Remove="RandomNameGeneratorLibrary" />
    </ItemGroup>
    <ItemGroup>
        <Reference Include="GraphApi.Client">
          <HintPath>Binaries\GraphApi.Client\netstandard2.0\GraphApi.Client.dll</HintPath>
        </Reference>
        <Reference Include="Hangfire.Pro">
            <HintPath>Hangfire.Pro.Binaries\Hangfire.Pro.dll</HintPath>
        </Reference>
        <Reference Include="Hangfire.Pro.Redis">
            <HintPath>Hangfire.Pro.Binaries\Hangfire.Pro.Redis.dll</HintPath>
        </Reference>
        <Reference Include="Sleekflow.Apis.CommerceHub">
            <HintPath>Binaries\Sleekflow.Apis.CommerceHub\netstandard2.0\Sleekflow.Apis.CommerceHub.dll</HintPath>
        </Reference>
        <Reference Include="Sleekflow.Apis.CrmHub">
            <HintPath>Binaries\netstandard2.0\Sleekflow.Apis.CrmHub.dll</HintPath>
        </Reference>
        <Content Include="FFmpegexe\**">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </Content>
        <Reference Include="Sleekflow.Apis.MessagingHub">
            <HintPath>Binaries\Sleekflow.Apis.MessagingHub\netstandard2.0\Sleekflow.Apis.MessagingHub.dll</HintPath>
        </Reference>
        <Reference Include="Sleekflow.Apis.AuditHub">
            <HintPath>Binaries\Sleekflow.Apis.AuditHub\netstandard2.0\Sleekflow.Apis.AuditHub.dll</HintPath>
        </Reference>
        <Reference Include="Sleekflow.Apis.PublicApiGateway">
            <HintPath>Binaries\Sleekflow.Apis.PublicApiGateway\netstandard2.0\Sleekflow.Apis.PublicApiGateway.dll</HintPath>
        </Reference>
        <Reference Include="Sleekflow.Apis.FlowHub">
            <HintPath>Binaries\Sleekflow.Apis.FlowHub\netstandard2.0\Sleekflow.Apis.FlowHub.dll</HintPath>
        </Reference>
        <Reference Include="Sleekflow.Apis.IntelligentHub">
            <HintPath>Binaries\Sleekflow.Apis.IntelligentHub\netstandard2.0\Sleekflow.Apis.IntelligentHub.dll</HintPath>
        </Reference>
        <Reference Include="GraphApi.Client.Const">
            <HintPath>Binaries\GraphApi.Client\netstandard2.0\GraphApi.Client.Const.dll</HintPath>
        </Reference>
        <Reference Include="Sleekflow.Apis.TenantHub">
          <HintPath>Binaries\Sleekflow.Apis.TenantHub\netstandard2.0\Sleekflow.Apis.TenantHub.dll</HintPath>
        </Reference>
        <Reference Include="Sleekflow.Apis.UserEventHub">
            <HintPath>Binaries\Sleekflow.Apis.UserEventHub\netstandard2.0\Sleekflow.Apis.UserEventHub.dll</HintPath>
        </Reference>
        <Reference Include="Sleekflow.Apis.Sharehub">
            <HintPath>Binaries\Sleekflow.Apis.ShareHub\netstandard2.0\Sleekflow.Apis.ShareHub.dll</HintPath>
        </Reference>
        <Reference Include="Sleekflow.Apis.TicketingHub">
            <HintPath>Binaries\Sleekflow.Apis.TicketingHub\netstandard2.0\Sleekflow.Apis.TicketingHub.dll</HintPath>
        </Reference>
        <Reference Include="Sleekflow.Apis.FlowHub.Integrator">
            <HintPath>Binaries\Sleekflow.Apis.FlowHub.Integrator\netstandard2.0\Sleekflow.Apis.FlowHub.Integrator.dll</HintPath>
        </Reference>
        <Reference Include="Sleekflow.Apis.Webhookhub">
            <HintPath>Binaries\Sleekflow.Apis.WebhookHub\netstandard2.0\Sleekflow.Apis.WebhookHub.dll</HintPath>
        </Reference>
        <Reference Include="Sleekflow.Apis.InternalIntegrationHub">
            <HintPath>Binaries\Sleekflow.Apis.InternalIntegrationHub\netstandard2.0\Sleekflow.Apis.InternalIntegrationHub.dll</HintPath>
        </Reference>
    </ItemGroup>
    <ItemGroup Condition=" '$(Configuration)' != 'Release' Or '$(ExcludeBuildDbMigration)' == 'TRUE'">
        <None Remove="Migrations\**" />
        <Content Remove="Migrations\**" />
        <Compile Remove="Migrations\**" />
        <EmbeddedResource Remove="Migrations\**" />
    </ItemGroup>

    <PropertyGroup Condition="'$(SWAGGERGEN)' == 'TRUE'">
        <DefineConstants>SWAGGERGEN</DefineConstants>
    </PropertyGroup>

    <Target Name="SwaggerGen" AfterTargets="Build" Condition="'$(SWAGGERGEN)' == 'TRUE'">
        <MakeDir Directories=".swagger" />
        <Exec Command="dotnet tool restore" />
        <Exec Command="dotnet swagger tofile --output ./.swagger/api.yaml --yaml $(OutputPath)$(AssemblyName).dll api" WorkingDirectory="$(ProjectDir)" />
        <Exec Command="dotnet swagger tofile --output ./.swagger/webhooks.yaml --yaml $(OutputPath)$(AssemblyName).dll webhooks" WorkingDirectory="$(ProjectDir)" />
        <Exec Command="dotnet swagger tofile --output ./.swagger/internal.yaml --yaml $(OutputPath)$(AssemblyName).dll internal" WorkingDirectory="$(ProjectDir)" />
        <Exec Command="dotnet swagger tofile --output ./.swagger/reseller.yaml --yaml $(OutputPath)$(AssemblyName).dll reseller" WorkingDirectory="$(ProjectDir)" />
    </Target>
</Project>
