using Newtonsoft.Json;

namespace Sleekflow.Core.Infra.Perf.Components.Configs.SleekflowCore;

public class ValuesConfig
{
    [JsonProperty("app_domain_name")]
    public string AppDomainName { get; set; }

    [JsonProperty("app_domain_name_v1")]
    public string AppDomainNameV1 { get; set; }

    [JsonProperty("share_link_function")]
    public string ShareLinkFunction { get; set; }

    [Json<PERSON>roperty("sleekflow_api_gateway")]
    public string SleekflowApiGateway { get; set; }

    [JsonProperty("sleekflow_company_id")]
    public string SleekflowCompanyId { get; set; }

    [JsonProperty("sleekflow_pay_function")]
    public string SleekflowPayFunction { get; set; }

    public ValuesConfig(
        string appDomainName,
        string appDomainNameV1,
        string shareLinkFunction,
        string sleekflowApiGateway,
        string sleekflowCompanyId,
        string sleekflowPayFunction)
    {
        AppDomainName = appDomainName;
        AppDomainNameV1 = appDomainNameV1;
        ShareLinkFunction = shareLinkFunction;
        SleekflowApiGateway = sleekflowApiGateway;
        SleekflowCompanyId = sleekflowCompanyId;
        SleekflowPayFunction = sleekflowPayFunction;
    }
}