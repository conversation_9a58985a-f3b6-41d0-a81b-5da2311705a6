<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>

    <IsPackable>false</IsPackable>

    <Configurations>Debug;Release;QA;OBSOLETE_AUTH</Configurations>

    <Platforms>AnyCPU</Platforms>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Auth0.AuthenticationApi" Version="7.26.2" />
    <PackageReference Include="Auth0.Core" Version="7.26.2" />
    <PackageReference Include="Auth0.ManagementApi" Version="7.26.2" />
    <PackageReference Include="Bogus" Version="35.6.0" />
    <PackageReference Include="FluentAssertions" Version="6.12.0" />
    <PackageReference Include="Hangfire.MemoryStorage" Version="1.8.1.1" />
    <PackageReference Include="LazyCache" Version="2.4.0" />
    <PackageReference Include="LazyCache.AspNetCore" Version="2.4.0" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.7" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.Testing" Version="8.0.7" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.InMemory" Version="8.0.7" />
    <PackageReference Include="Microsoft.IdentityModel.Protocols.OpenIdConnect" Version="8.0.1" />
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.10.0" />
    <PackageReference Include="MockQueryable.NSubstitute" Version="7.0.1" />
    <PackageReference Include="Moq" Version="4.20.70" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="NSubstitute" Version="5.1.0" />
    <PackageReference Include="NUnit" Version="3.14.0" />
    <PackageReference Include="NUnit3TestAdapter" Version="4.6.0" />
    <PackageReference Include="NUnit.Analyzers" Version="3.10.0">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="coverlet.collector" Version="6.0.2">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="TngTech.ArchUnitNET" Version="0.10.6" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Sleekflow.Powerflow.Apis\Sleekflow.Powerflow.Apis.csproj" />
    <ProjectReference Include="..\Travis_backend.Auth0\Travis_backend.Auth0.csproj" />
    <ProjectReference Include="..\Travis_backend\Travis_backend.csproj" />
  </ItemGroup>

  <ItemGroup Condition="'$(OS)' == 'Windows_NT'">
    <RuntimeHostConfigurationOption Include="System.Globalization.AppLocalIcu" Value="********" />
    <PackageReference Include="Microsoft.ICU.ICU4C.Runtime" Version="********" />
  </ItemGroup>

  <ItemGroup>
    <Reference Include="GraphApi.Client">
      <HintPath>..\Travis_backend\Binaries\GraphApi.Client\netstandard2.0\GraphApi.Client.dll</HintPath>
    </Reference>
    <Reference Include="GraphApi.Client.Const">
      <HintPath>..\Travis_backend\Binaries\GraphApi.Client\netstandard2.0\GraphApi.Client.Const.dll</HintPath>
    </Reference>
    <Reference Include="Sleekflow.Apis.AuditHub">
      <HintPath>..\Travis_backend\Binaries\Sleekflow.Apis.AuditHub\netstandard2.0\Sleekflow.Apis.AuditHub.dll</HintPath>
    </Reference>
    <Reference Include="Sleekflow.Apis.MessagingHub">
      <HintPath>..\Travis_backend\Binaries\Sleekflow.Apis.MessagingHub\netstandard2.0\Sleekflow.Apis.MessagingHub.dll</HintPath>
    </Reference>
    <Reference Include="Sleekflow.Apis.FlowHub">
      <HintPath>..\Travis_backend\Binaries\Sleekflow.Apis.FlowHub\netstandard2.0\Sleekflow.Apis.FlowHub.dll</HintPath>
    </Reference>
    <Reference Include="Sleekflow.Apis.TenantHub">
      <HintPath>..\Travis_backend\Binaries\Sleekflow.Apis.TenantHub\netstandard2.0\Sleekflow.Apis.TenantHub.dll</HintPath>
    </Reference>
    <Reference Include="Sleekflow.Apis.Sharehub">
      <HintPath>..\Travis_backend\Binaries\Sleekflow.Apis.ShareHub\netstandard2.0\Sleekflow.Apis.ShareHub.dll</HintPath>
    </Reference>
  </ItemGroup>

  <ItemGroup>
    <Content Include="ImageHelper\ImageFiles\**" CopyToOutputDirectory="PreserveNewest"/>
  </ItemGroup>

</Project>
