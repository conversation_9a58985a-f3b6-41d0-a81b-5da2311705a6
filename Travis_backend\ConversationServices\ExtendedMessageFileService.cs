using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AutoMapper;
using AutoMapper.QueryableExtensions;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using RestSharp;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.Constants;
using Travis_backend.Database;
using Travis_backend.Extensions;
using Travis_backend.FileDomain.Services;
using Travis_backend.Helpers;
using Travis_backend.MessageDomain.Models;
using Travis_backend.MessageDomain.ViewModels;

namespace Travis_backend.ConversationServices;

public interface IExtendedMessageFileService
{
    Task<string> GetCompanyStorageConfigContainerNameAsync(string companyId);

    Task<ExtendedMessageFileResponse> UploadExtendedMessageFileAsync(
        string companyId,
        string channel,
        ExtendedMessageType extendedMessageType,
        string containerName,
        string pathName,
        IFormFile formFile,
        string mediaType,
        string displayName);

    Task<ExtendedMessageFileResponse> UploadExtendedMessageFileByUrlAsync(
        string companyId,
        string channel,
        ExtendedMessageType extendedMessageType,
        string containerName,
        string pathName,
        string fileUrl,
        string fileName);

    Task<DownloadFileResponse> DownloadFileAsync(string id, string mediaType, string filename, string mode = null);

    Task<List<ExtendedMessageFileResponse>> GetExtendedMessageFilesAsync(
        Staff companyUser,
        int offset,
        int limit,
        string mediaType = null);

    Task<bool> DeleteExtendedMessageFileAsync(Staff companyUser, string fileId);
}

public class ExtendedMessageFileService : IExtendedMessageFileService
{
    private readonly IUploadService _uploadService;
    private readonly ApplicationDbContext _appDbContext;
    private readonly IMapper _mapper;
    private readonly IConfiguration _configuration;
    private readonly IAzureBlobStorageService _azureBlobStorageService;

    public ExtendedMessageFileService(
        IUploadService uploadService,
        ApplicationDbContext appDbContext,
        IMapper mapper,
        IConfiguration configuration,
        IAzureBlobStorageService azureBlobStorageService)
    {
        _uploadService = uploadService;
        _appDbContext = appDbContext;
        _mapper = mapper;
        _configuration = configuration;
        _azureBlobStorageService = azureBlobStorageService;
    }

    public async Task<ExtendedMessageFileResponse> UploadExtendedMessageFileAsync(
        string companyId,
        string channel,
        ExtendedMessageType extendedMessageType,
        string containerName,
        string pathName,
        IFormFile formFile,
        string mediaType,
        string displayName)
    {
        var uploadFile = await _uploadService.UploadFile(containerName, pathName, formFile);

        var file = new ExtendedMessagePayloadFile()
        {
            CompanyId = companyId,
            Channel = channel,
            ExtendedMessageType = extendedMessageType,
            BlobContainer = containerName,
            BlobFilePath = pathName,
            Filename = formFile.FileName,
            MIMEType = formFile.ContentType,
            FileSize = uploadFile.FileSizeInByte,
            DisplayName = string.IsNullOrWhiteSpace(displayName) ? formFile.FileName : displayName,
            MediaType = mediaType
        };

        var domain = _configuration["Values:DomainName"];

        var url = BlobPublicUrlBuilder.GetExtendedMessagePayloadFileUrl(
            domain,
            file.Id,
            file.Filename,
            file.MediaType,
            channel == ChannelTypes.WhatsappCloudApi);

        file.Url = url;

        await _appDbContext.ExtendedMessagePayloadFiles.AddAsync(file);
        await _appDbContext.SaveChangesAsync();

        return _mapper.Map<ExtendedMessageFileResponse>(file);
    }

    public async Task<ExtendedMessageFileResponse> UploadExtendedMessageFileByUrlAsync(
        string companyId,
        string channel,
        ExtendedMessageType extendedMessageType,
        string containerName,
        string pathName,
        string fileUrl,
        string fileName)
    {
        var client = new RestClient(fileUrl);
        var request = new RestRequest(Method.GET);
        IRestResponse response = await client.ExecuteAsync(request);

        var uploadFileResult = await _uploadService.UploadFileByURL(containerName, pathName, fileUrl);
        var domain = _configuration["Values:DomainName"];

        var file = new ExtendedMessagePayloadFile()
        {
            CompanyId = companyId,
            Channel = channel,
            ExtendedMessageType = extendedMessageType,
            BlobContainer = containerName,
            BlobFilePath = pathName,
            Filename = fileName,
            MIMEType = response.ContentType,
            FileSize = uploadFileResult.FileSizeInByte,
            DisplayName = fileName
        };

        if (response.ContentType.Contains("image"))
        {
            file.MediaType = "image";
        }
        else if (response.ContentType.Contains("video"))
        {
            file.MediaType = "video";
        }
        else if (response.ContentType.Contains("audio"))
        {
            file.MediaType = "audio";
        }
        else
        {
            file.MediaType = "document";
        }

        var url = BlobPublicUrlBuilder.GetExtendedMessagePayloadFileUrl(
            domain,
            file.Id,
            file.Filename,
            file.MediaType,
            channel == ChannelTypes.WhatsappCloudApi);

        file.Url = url;

        await _appDbContext.ExtendedMessagePayloadFiles.AddAsync(file);
        await _appDbContext.SaveChangesAsync();

        return _mapper.Map<ExtendedMessageFileResponse>(file);
    }

    public async Task<string> GetCompanyStorageConfigContainerNameAsync(string companyId)
    {
        var containerName = await _appDbContext.ConfigStorageConfigs.Where(x => x.CompanyId == companyId)
            .Select(x => x.ContainerName)
            .FirstOrDefaultAsync();

        if (containerName == null)
        {
            throw new Exception("Storage Config Not Found");
        }

        return containerName;
    }

    public async Task<DownloadFileResponse> DownloadFileAsync(
        string id,
        string mediaType,
        string filename,
        string mode = null)
    {
        var file = await _appDbContext.ExtendedMessagePayloadFiles
            .AsNoTracking()
            .Where(x => x.Id == id && x.MediaType == mediaType && x.Filename == filename)
            .FirstOrDefaultAsync();

        if (file == null)
        {
            throw new Exception("not found");
        }

        try
        {
            if (mode is "redirect")
            {
                var sasLink = _azureBlobStorageService.GetAzureBlobSasUri(file.BlobFilePath, file.BlobContainer);

                var url = new Uri(sasLink).AbsoluteUri;

                return new DownloadFileResponse()
                {
                    Url = url
                };
            }

            var blob = await _azureBlobStorageService.DownloadFromAzureBlob(file.BlobFilePath, file.BlobContainer);

            return new DownloadFileResponse()
            {
                FileBlob = blob.ToArray(), FileName = file.Filename, MIMEType = file.MIMEType
            };
        }
        catch (Exception ex)
        {
            throw new Exception(ex.Message);
        }
    }

    public async Task<List<ExtendedMessageFileResponse>> GetExtendedMessageFilesAsync(
        Staff companyUser,
        int offset,
        int limit,
        string mediaType = null)
    {
        var mediaFiles = await _appDbContext.ExtendedMessagePayloadFiles
            .AsNoTracking()
            .WhereIf(!string.IsNullOrWhiteSpace(mediaType), x => x.MediaType == mediaType)
            .Where(x => x.CompanyId == companyUser.CompanyId)
            .Skip(offset)
            .Take(limit)
            .ProjectTo<ExtendedMessageFileResponse>(_mapper.ConfigurationProvider)
            .ToListAsync();

        return mediaFiles;
    }

    public async Task<bool> DeleteExtendedMessageFileAsync(Staff companyUser, string fileId)
    {
        var file = await _appDbContext.ExtendedMessagePayloadFiles.FirstOrDefaultAsync(
            x => x.Id == fileId && x.CompanyId == companyUser.CompanyId);

        if (file == null)
        {
            throw new Exception("file not found");
        }

        _appDbContext.ExtendedMessagePayloadFiles.Remove(file);

        return await _appDbContext.SaveChangesAsync() > 0;
    }
}