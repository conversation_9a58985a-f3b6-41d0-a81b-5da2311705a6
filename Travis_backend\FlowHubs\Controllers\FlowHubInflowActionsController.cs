using System;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;
using Sleekflow.Apis.CrmHub.Api;
using Sleekflow.Apis.CrmHub.Model;
using Travis_backend.AccountAuthenticationDomain.Models;
using Travis_backend.BackgroundTaskServices;
using Travis_backend.BackgroundTaskServices.Helpers;
using Travis_backend.BackgroundTaskServices.Models;
using Travis_backend.ConversationServices;
using Travis_backend.Data.BackgroundTask;
using Travis_backend.Database;
using Travis_backend.FlowHubs.Filters;
using Travis_backend.Models.BackgroundTask;

namespace Travis_backend.FlowHubs.Controllers;

[Authorize]
[Route("FlowHub/InflowActions")]
[TypeFilter(typeof(FlowHubExceptionFilter))]
public class FlowHubInflowActionsController : ControllerBase
{
    private readonly UserManager<ApplicationUser> _userManager;
    private readonly ICoreService _coreService;
    private readonly IInflowActionsApi _inflowActionsApi;
    private readonly IBackgroundTaskService _backgroundTaskService;
    private readonly ApplicationDbContext _appDbContext;

    private readonly JsonSerializerSettings _jsonSerializerSettings = new ()
    {
        NullValueHandling = NullValueHandling.Ignore,
        DateTimeZoneHandling = DateTimeZoneHandling.Utc,
        ContractResolver = new CamelCasePropertyNamesContractResolver(),
    };

    public FlowHubInflowActionsController(
        UserManager<ApplicationUser> userManager,
        ICoreService coreService,
        IInflowActionsApi inflowActionsApi,
        IBackgroundTaskService backgroundTaskService,
        ApplicationDbContext appDbContext)
    {
        _coreService = coreService;
        _userManager = userManager;
        _inflowActionsApi = inflowActionsApi;
        _backgroundTaskService = backgroundTaskService;
        _appDbContext = appDbContext;
    }

    public class GetSalesforceCustomObjectTypesRequest
    {
        [JsonProperty("salesforce_connection_id")]
        public string SalesforceConnectionId { get; set; }
    }

    [HttpPost("GetSalesforceCustomObjectTypes")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GetSalesforceCustomObjectTypesOutputOutput>> GetSalesforceCustomObjectTypes(
        [FromBody] GetSalesforceCustomObjectTypesRequest getSalesforceCustomObjectTypesRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var output = await _inflowActionsApi.InflowActionsGetSalesforceCustomObjectTypesPostAsync(
            getSalesforceCustomObjectTypesInput: new GetSalesforceCustomObjectTypesInput(
                staff.CompanyId,
                getSalesforceCustomObjectTypesRequest.SalesforceConnectionId));

        return Ok(output.Data.CustomObjectTypes);
    }

    public class LoopThroughAndEnrollSalesforceObjectsRequest
    {
        [JsonProperty("salesforce_connection_id")]
        public string SalesforceConnectionId { get; set; }

        [JsonProperty("entity_type_name")]
        public string EntityTypeName { get; set; }

        [JsonProperty("workflow_id")]
        public string WorkflowId { get; set; }

        [JsonProperty("workflow_versioned_id")]
        public string WorkflowVersionedId { get; set; }

        [JsonProperty("is_custom_object")]
        public bool IsCustomObject { get; set; }
    }

    [HttpPost("LoopThroughAndEnrollSalesforceObjects")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<LoopThroughAndEnrollSalesforceObjectsToFlowHubOutputOutput>>
        LoopThroughAndEnrollSalesforceObjects([FromBody] LoopThroughAndEnrollSalesforceObjectsRequest request)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var output = await _inflowActionsApi.InflowActionsLoopThroughAndEnrollSalesforceObjectsToFlowHubPostAsync(
            loopThroughAndEnrollSalesforceObjectsToFlowHubInput: new LoopThroughAndEnrollSalesforceObjectsToFlowHubInput(
                staff.CompanyId,
                request.SalesforceConnectionId,
                request.EntityTypeName,
                request.WorkflowId,
                request.WorkflowVersionedId,
                request.IsCustomObject));

        return Ok(output);
    }

    public class GetLoopThroughSalesforceObjectsProgressRequest
    {
        [JsonProperty("workflow_id")]
        public string WorkflowId { get; set; }

        [JsonProperty("workflow_versioned_id")]
        public string WorkflowVersionedId { get; set; }
    }

    [HttpPost("GetLoopThroughSalesforceObjectsProgress")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GetLoopThroughSalesforceObjectsProgressOutputOutput>>
        GetLoopThroughSalesforceObjectsProgress(
        [FromBody]
        GetLoopThroughSalesforceObjectsProgressRequest request)
    {
        var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (companyUser == null)
        {
            return Unauthorized();
        }

        var output =
            await _inflowActionsApi.InflowActionsGetLoopThroughSalesforceObjectsProgressPostAsync(
                getLoopThroughSalesforceObjectsProgressInput: new
                    GetLoopThroughSalesforceObjectsProgressInput(
                        companyUser.CompanyId,
                        request.WorkflowId,
                        request.WorkflowVersionedId));

        return Ok(output.Data);
    }

    public class TerminateLoopThroughSalesforceObjectsRequest
    {
        [JsonProperty("workflow_id")]
        public string WorkflowId { get; set; }

        [JsonProperty("workflow_versioned_id")]
        public string WorkflowVersionedId { get; set; }
    }

    [HttpPost("TerminateLoopThroughSalesforceObjects")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<TerminateLoopThroughSalesforceObjectsOutputOutput>>
        TerminateLoopThroughSalesforceObjects(
            [FromBody]
            TerminateLoopThroughSalesforceObjectsRequest request)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var output = await _inflowActionsApi.InflowActionsTerminateLoopThroughSalesforceObjectsPostAsync(
            terminateLoopThroughSalesforceObjectsInput: new TerminateLoopThroughSalesforceObjectsInput(
                staff.CompanyId,
                request.WorkflowId,
                request.WorkflowVersionedId));

        return Ok(output.Data);
    }

    public class LoopThroughAndEnrollContactsRequest
    {
        [JsonProperty("workflow_id")]
        public string WorkflowId { get; set; }

        [JsonProperty("workflow_versioned_id")]
        public string WorkflowVersionedId { get; set; }
    }

    [HttpPost("LoopThroughAndEnrollContacts")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<BackgroundTaskViewModel>>
        LoopThroughAndEnrollContacts([FromBody] LoopThroughAndEnrollContactsRequest request)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var task = await _backgroundTaskService.EnqueueLoopThroughAndEnrollContactsToFlowHubTask(
            staff.IdentityId,
            staff.CompanyId,
            staff.Id,
            request.WorkflowId,
            request.WorkflowVersionedId);

        return Ok(task.MapToResultViewModel());
    }

    public class GetLoopThroughContactsProgressRequest
    {
        [JsonProperty("workflow_id")]
        public string WorkflowId { get; set; }

        [JsonProperty("workflow_versioned_id")]
        public string WorkflowVersionedId { get; set; }
    }

    public class GetLoopThroughContactsProgressOutput
    {
        [JsonProperty("count")]
        public int Count { get; set; }

        [JsonProperty("last_update_time")]
        public DateTime LastUpdateTime { get; set; }

        [JsonProperty("status")]
        public string Status { get; set; }
    }

    [HttpPost("GetLoopThroughContactsProgress")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GetLoopThroughContactsProgressOutput>>
        GetLoopThroughContactsProgress(
            [FromBody]
            GetLoopThroughContactsProgressRequest request)
    {
        var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (companyUser == null)
        {
            return Unauthorized();
        }

        var loopThroughContactsBackgroundTaskTargetPayload = JsonConvert.SerializeObject(
            new
            {
                TargetType = BackgroundTaskTargetType.Contact.ToString(),
                FlowHubWorkflowId = request.WorkflowId,
                FlowHubWorkflowVersionedId = request.WorkflowVersionedId
            },
            _jsonSerializerSettings);

        // FE ensures that no new task can be enqueued if there's already an existing non-completed task,
        // thus we can assume that the latest task is the one to return
        var loopThroughContactsBackgroundTask = await _appDbContext.BackgroundTasks
            .Where(
                t => t.CompanyId == companyUser.CompanyId
                     && t.TaskType == BackgroundTaskType.LoopThroughAndEnrollContactsToFlowHub
                     && t.TargetPayload == loopThroughContactsBackgroundTaskTargetPayload)
            .OrderByDescending(t => t.CreatedAt)
            .FirstOrDefaultAsync();

        if (loopThroughContactsBackgroundTask is null)
        {
            return Ok(new GetLoopThroughContactsProgressOutput
            {
                Count = 0,
                LastUpdateTime = DateTime.MinValue,
                Status = "Pending"
            });
        }

        return Ok(new GetLoopThroughContactsProgressOutput
        {
            Count = loopThroughContactsBackgroundTask.Total,
            LastUpdateTime = loopThroughContactsBackgroundTask.UpdatedAt,
            Status = loopThroughContactsBackgroundTask.IsCompleted ? "Completed" : "Running"
        });
    }

    public class LoopThroughAndEnrollSchemafulObjectsRequest
    {
        [JsonProperty("schema_id")]
        public string SchemaId { get; set; }

        [JsonProperty("workflow_id")]
        public string FlowHubWorkflowId { get; set; }

        [JsonProperty("workflow_versioned_id")]
        public string FlowHubWorkflowVersionedId { get; set; }
    }

    [HttpPost("LoopThroughAndEnrollSchemafulObjects")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<LoopThroughAndEnrollSchemafulObjectsToFlowHubOutputOutput>>
        LoopThroughAndEnrollSchemafulObjects([FromBody] LoopThroughAndEnrollSchemafulObjectsRequest request)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var output = await _inflowActionsApi.InflowActionsLoopThroughAndEnrollSchemafulObjectsToFlowHubPostAsync(
            loopThroughAndEnrollSchemafulObjectsToFlowHubInput: new LoopThroughAndEnrollSchemafulObjectsToFlowHubInput(
                staff.CompanyId,
                request.SchemaId,
                request.FlowHubWorkflowId,
                request.FlowHubWorkflowVersionedId));

        return Ok(output);
    }

    public class GetLoopThroughSchemafulObjectsProgressRequest
    {
        [JsonProperty("workflow_id")]
        public string WorkflowId { get; set; }

        [JsonProperty("workflow_versioned_id")]
        public string WorkflowVersionedId { get; set; }
    }

    [HttpPost("GetLoopThroughSchemafulObjectsProgress")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GetLoopThroughSchemafulObjectsProgressOutputOutput>>
        GetLoopThroughSchemafulObjectsProgress(
        [FromBody]
        GetLoopThroughSchemafulObjectsProgressRequest request)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var output = await _inflowActionsApi.InflowActionsGetLoopThroughSchemafulObjectsProgressPostAsync(
            getLoopThroughSchemafulObjectsProgressInput: new GetLoopThroughSchemafulObjectsProgressInput(
                staff.CompanyId,
                request.WorkflowId,
                request.WorkflowVersionedId));

        return Ok(output.Data);
    }

    public class TerminateLoopThroughSchemafulObjectsRequest
    {
        [JsonProperty("workflow_id")]
        public string WorkflowId { get; set; }

        [JsonProperty("workflow_versioned_id")]
        public string WorkflowVersionedId { get; set; }
    }

    [HttpPost("TerminateLoopThroughSchemafulObjects")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<TerminateLoopThroughSchemafulObjectsOutputOutput>>
        TerminateLoopThroughSchemafulObjects(
            [FromBody]
            TerminateLoopThroughSchemafulObjectsRequest request)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var output = await _inflowActionsApi.InflowActionsTerminateLoopThroughSchemafulObjectsPostAsync(
            terminateLoopThroughSchemafulObjectsInput: new TerminateLoopThroughSchemafulObjectsInput(
                staff.CompanyId,
                request.WorkflowId,
                request.WorkflowVersionedId));

        return Ok(output.Data);
    }
}