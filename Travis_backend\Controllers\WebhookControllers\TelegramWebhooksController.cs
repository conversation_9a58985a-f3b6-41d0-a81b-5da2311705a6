﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using AutoMapper;
using Hangfire;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Polly;
using Telegram.Bot;
using Telegram.Bot.Types;
using Telegram.Bot.Types.Enums;
using Travis_backend.BackgroundTaskServices.Attributes;
using Travis_backend.Cache;
using Travis_backend.Constants;
using Travis_backend.ConversationDomain.ConversationResolver;
using Travis_backend.ConversationServices;
using Travis_backend.Database;
using Travis_backend.MessageDomain.Models;
using Travis_backend.MessageDomain.ViewModels;
using File = Telegram.Bot.Types.File;

namespace Travis_backend.Controllers.WebhookControllers
{
    /// <summary>
    /// Telegram Webhook.
    /// </summary>
    [Route("telegram")]
    [Produces("application/json")]
    public class TelegramWebhooksController : Controller
    {
        private readonly ILogger<TelegramWebhooksController> _logger;
        private readonly ApplicationDbContext _appDbContext;
        private readonly IConversationMessageService _conversationMessageService;
        private readonly IConversationResolver _conversationResolver;
        private readonly ILockService _lockService;

        public TelegramWebhooksController(
            ILogger<TelegramWebhooksController> logger,
            IMapper mapper,
            ApplicationDbContext appDbContext,
            IConversationMessageService conversationMessageService,
            IConversationResolver conversationResolver,
            ILockService lockService)
        {
            _logger = logger;
            _appDbContext = appDbContext;
            _conversationMessageService = conversationMessageService;
            _conversationResolver = conversationResolver;
            _lockService = lockService;
        }

        /// <summary>
        /// Telegram Webhook for receiving Telegram bot callback.
        /// </summary>
        /// <returns></returns>
        [HttpPost("webhook/{companyId}")]
        public IActionResult Webhook(
            [Required] [FromRoute]
            string companyId,
            [FromQuery]
            long telegramBotId,
            [FromBody]
            Update input)
        {
            _logger.LogInformation(
                "TelegramWebhook: for company: {CompanyId}, bot: {TelegramBotId}, webhook: {Webhook}",
                companyId,
                telegramBotId,
                JsonConvert.SerializeObject(input));

            try
            {
                Policy.Handle<Exception>()
                    .WaitAndRetry(
                        3,
                        sleepDurationProvider: i => TimeSpan.FromMilliseconds(100),
                        onRetry: (exception, _, retryCount, _) =>
                        {
                            _logger.LogError(
                                exception,
                                "TelegramWebhook: Enqueue error {ExceptionMessage} with retry {RetryCount}",
                                exception.Message,
                                retryCount);
                        })
                    .Execute(
                        () =>
                        {
                            BackgroundJob.Enqueue(() => HandleWebhook(companyId, telegramBotId, input));
                        });
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "TelegramWebhook Error: {ExceptionMessage}",
                    ex.Message);

                return BadRequest();
            }

            return Ok();
        }

        [ApiExplorerSettings(IgnoreApi = true)]
        [HangfireJobExpirationTimeout(timeoutInMinutes: 60)]
        [AutomaticRetry(
            Attempts = 10,
            OnAttemptsExceeded = AttemptsExceededAction.Fail,
            DelaysInSeconds =
            [
                60, // 1 minute
                120, // 2 minutes
                240, // 4 minutes
                480, // 8 minutes
                960, // 16 minutes
                1920, // 32 minutes
                3600, // 1 hour
                7200, // 2 hours
                14400, // 4 hours
                28800, // 8 hours
            ])]
        public async Task HandleWebhook(string companyId, long telegramBotId, Update input)
        {
            if (input.Type == UpdateType.Message)
            {
                var message = input.Message;
                var chat = message.Chat;

                var config = await _appDbContext.ConfigTelegramConfigs.FirstOrDefaultAsync(
                    x => x.TelegramBotId == telegramBotId && x.CompanyId == companyId);

                if (config == null)
                {
                    throw new Exception("Telegram config not found.");
                }

                var (conversation, myLock) = await _conversationResolver.GetConversationForReceivingMessageAsync(
                    companyId,
                    ChannelTypes.Telegram,
                    telegramBotId.ToString(),
                    chat.Id.ToString(),
                    null);

                var conversationMessage = new ConversationMessage()
                {
                    Channel = ChannelTypes.Telegram,
                    TelegramSender = conversation.TelegramUser,
                    MessageType = "text",
                    MessageUniqueID = $"{telegramBotId}-{chat.Id}-{message.MessageId}",
                    Status = MessageStatus.Received,
                    DeliveryType = DeliveryType.Normal,
                    IsSentFromSleekflow = false
                };
                var client = new TelegramBotClient(config.TelegramBotToken);

                File telegramFile = null;
                MemoryStream memoryStream = null;
                FileURLMessage messageFile = null;
                switch (input.Message.Type)
                {
                    case MessageType.Text:
                        conversationMessage.MessageType = "text";
                        conversationMessage.MessageContent = message.Text;

                        await _conversationMessageService.SendMessage(conversation, conversationMessage);

                        break;
                    case MessageType.Photo:
                        conversationMessage.MessageType = "file";
                        conversationMessage.MessageContent = message.Caption;

                        memoryStream = new MemoryStream();
                        telegramFile = await client.GetInfoAndDownloadFileAsync(
                            message.Photo
                                .First(x => x.FileSize == message.Photo.Max(p => p.FileSize))
                                .FileId,
                            memoryStream);
                        memoryStream.Position = 0;

                        messageFile = new FileURLMessage()
                        {
                            FileName = $"image_{DateTime.UtcNow}.jpg",
                            MIMEType = "image/jpeg",
                            FileStream = memoryStream
                        };

                        await _conversationMessageService.SendFileMessageByFBURL(
                            conversation,
                            conversationMessage,
                            new List<FileURLMessage>()
                            {
                                messageFile
                            });
                        break;

                    case MessageType.Document:
                        conversationMessage.MessageType = "file";
                        conversationMessage.MessageContent = message.Caption;

                        memoryStream = new MemoryStream();
                        telegramFile = await client.GetInfoAndDownloadFileAsync(
                            message.Document.FileId,
                            memoryStream);
                        memoryStream.Position = 0;

                        messageFile = new FileURLMessage()
                        {
                            FileName = message.Document.FileName,
                            MIMEType = message.Document.MimeType,
                            FileStream = memoryStream
                        };

                        await _conversationMessageService.SendFileMessageByFBURL(
                            conversation,
                            conversationMessage,
                            new List<FileURLMessage>()
                            {
                                messageFile
                            });

                        break;

                    case MessageType.Video:
                        conversationMessage.MessageType = "file";
                        conversationMessage.MessageContent = message.Caption;

                        memoryStream = new MemoryStream();
                        telegramFile = await client.GetInfoAndDownloadFileAsync(
                            message.Video.FileId,
                            memoryStream);
                        memoryStream.Position = 0;

                        messageFile = new FileURLMessage()
                        {
                            FileName = Path.GetFileName(telegramFile.FilePath),
                            MIMEType = message.Video.MimeType,
                            FileStream = memoryStream
                        };

                        await _conversationMessageService.SendFileMessageByFBURL(
                            conversation,
                            conversationMessage,
                            new List<FileURLMessage>()
                            {
                                messageFile
                            });

                        break;

                    case MessageType.Voice:
                        conversationMessage.MessageType = "file";
                        conversationMessage.MessageContent = message.Caption;

                        memoryStream = new MemoryStream();
                        telegramFile = await client.GetInfoAndDownloadFileAsync(
                            message.Voice.FileId,
                            memoryStream);
                        memoryStream.Position = 0;

                        messageFile = new FileURLMessage()
                        {
                            FileName = Path.GetFileName(telegramFile.FilePath),
                            MIMEType = message.Voice.MimeType,
                            FileStream = memoryStream
                        };

                        await _conversationMessageService.SendFileMessageByFBURL(
                            conversation,
                            conversationMessage,
                            new List<FileURLMessage>()
                            {
                                messageFile
                            });

                        break;

                    default:
                        conversationMessage.MessageType = "text";
                        conversationMessage.MessageContent = "<Unsupported Message Type>";
                        await _conversationMessageService.SendMessage(conversation, conversationMessage);
                        break;
                }

                await _lockService.ReleaseLockAsync(myLock);
            }
        }
    }
}