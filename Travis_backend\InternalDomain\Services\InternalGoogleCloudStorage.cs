﻿using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using CsvHelper;
using CsvHelper.Configuration;
using Google.Apis.Auth.OAuth2;
using Google.Cloud.Storage.V1;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Travis_backend.Constants;
using Travis_backend.InternalDomain.Models;
using Travis_backend.InternalDomain.ViewModels;

namespace Travis_backend.InternalDomain.Services;

public interface IInternalGoogleCloudStorage
{
    Task<bool> UploadCompanyDetailCsvAsync(List<CompanySnapshotTrimData> data, DateTime savedAt);

    Task<bool> UploadCompanyBillRecordCsvAsync(List<CompanyBillRecordSnapshotData> data, DateTime savedAt);

    Task<bool> UploadCmsSalesInputPaymentCsvAsync(List<CmsSalesPaymentRecordSnapshotData> data, DateTime savedAt);

    Task<bool> UploadCompanyOnboardingCsvAsync(List<CmsCompanyOnboardingProgress> data, DateTime savedAt);

    Task<bool> UploadSleekPayReportData(List<CmsSleekPayReportDataWithCompany> data, DateTime savedAt);

    Task<bool> UploadWhatsappCloudApiConversationUsageAnalyticsCsvAsync(
        List<WhatsappCloudApiDailyConversationUsageAnalyticsSnapshotData> data,
        DateTime savedAt);

    Task<bool> UploadFlowBuilderUsageCsvAsync(List<CompanyFlowBuilderUsageSnapshotData> data, DateTime savedAt);

    Task<List<CompanyFlowBuilderUsageSnapshotData>> DownloadFlowBuilderUsageCsvAsync(DateTime savedAt);

    Task UploadBugbqSquadMetricsCsvAsync(
        List<BugbqMessageMetricByCompanySummary> messageVolumeByCompanySummary,
        DateTime savedAt);
}

public class InternalGoogleCloudStorage : IInternalGoogleCloudStorage
{
    private readonly ILogger<InternalGoogleCloudStorage> _logger;
    private readonly IConfiguration _configuration;
    private const string BucketName = "sleekflow_sqldb";

    public InternalGoogleCloudStorage(
        IConfiguration configuration,
        ILogger<InternalGoogleCloudStorage> logger)
    {
        _configuration = configuration;
        _logger = logger;
    }

    public async Task<bool> UploadCompanyDetailCsvAsync(List<CompanySnapshotTrimData> data, DateTime savedAt)
    {
        return await UploadListsToGoogleCloud(data, savedAt, "companies/CompanyDetails");
    }

    public async Task<bool> UploadCompanyBillRecordCsvAsync(List<CompanyBillRecordSnapshotData> data, DateTime savedAt)
    {
        return await UploadListsToGoogleCloud(data, savedAt, "companies-bill-records/BillRecords");
    }

    public async Task<bool> UploadCmsSalesInputPaymentCsvAsync(
        List<CmsSalesPaymentRecordSnapshotData> data,
        DateTime savedAt)
    {
        return await UploadListsToGoogleCloud(data, savedAt, "cms-sales-input-payment/CmsSalesInputPayment");
    }

    public async Task<bool> UploadCompanyOnboardingCsvAsync(List<CmsCompanyOnboardingProgress> data, DateTime savedAt)
    {
        return await UploadListsToGoogleCloud(data, savedAt, "onboarding/CompanyOnboardingData");
    }

    public async Task<bool> UploadFlowBuilderUsageCsvAsync(
        List<CompanyFlowBuilderUsageSnapshotData> data,
        DateTime savedAt)
    {
        return await UploadListsToGoogleCloud(data, savedAt, "companies-flow-builder/FlowBuilderUsage");
    }

    public Task<List<CompanyFlowBuilderUsageSnapshotData>> DownloadFlowBuilderUsageCsvAsync(DateTime savedAt)
    {
        return DownloadListsFromGoogleCloudAsync<CompanyFlowBuilderUsageSnapshotData>(
            "companies-flow-builder/FlowBuilderUsage",
            savedAt);
    }

    public async Task<bool> UploadWhatsappCloudApiConversationUsageAnalyticsCsvAsync(
        List<WhatsappCloudApiDailyConversationUsageAnalyticsSnapshotData> data,
        DateTime savedAt)
    {
        return await UploadListsToGoogleCloud(data, savedAt, "whatsapp-cloud-api/ConversationUsageAnalytics");
    }

    public async Task UploadBugbqSquadMetricsCsvAsync(
        List<BugbqMessageMetricByCompanySummary> messageVolumeByCompanySummary,
        DateTime savedAt)
    {
        await UploadListsToGoogleCloud(messageVolumeByCompanySummary, savedAt, "squad-metrics/MessageVolumeByCompany");
    }

    public async Task<bool> UploadSleekPayReportData(List<CmsSleekPayReportDataWithCompany> data, DateTime savedAt)
    {
        return await UploadListsToGoogleCloud(data, savedAt, "sleekpay/SleekPayReportData");
    }

    private async Task<bool> UploadListsToGoogleCloud<T>(List<T> data, DateTime savedAt, string filePath)
    {
        var credential = _configuration["InternalGoogleCloud:Credential"];

        if (credential == null)
        {
            return false;
        }

        var ms = new MemoryStream();

        await using (var writer = new StreamWriter(ms, leaveOpen: true))
        {
            await using (var csv = new CsvWriter(writer, CultureInfo.InvariantCulture))
            {
                await csv.WriteRecordsAsync(data);
            }
        }

        ms.Position = 0;

        var snapshotDate = savedAt.ToString("yyyyMMdd");

        var googleCredential = GoogleCredential.FromJson(credential);

        var client = await StorageClient.CreateAsync(googleCredential);

        var result = client.UploadObject(
            BucketName,
            $"{filePath}-{GetExportedFilePrefix()}{snapshotDate}.csv",
            "text/csv",
            ms);

        return true;
    }

    private async Task<List<T>> DownloadListsFromGoogleCloudAsync<T>(string filePath, DateTime savedAt)
    {
        var credential = _configuration["InternalGoogleCloud:Credential"];

        if (credential == null)
        {
            return null;
        }

        try
        {
            var googleCredential = GoogleCredential.FromJson(credential);

            var client = await StorageClient.CreateAsync(googleCredential);

            var ms = new MemoryStream();
            var snapshotDate = savedAt.ToString("yyyyMMdd");
            var downloadedObject = await client.DownloadObjectAsync(
                BucketName,
                $"{filePath}-{GetExportedFilePrefix()}{snapshotDate}.csv",
                ms);

            ms.Position = 0;

            var csvReaderConfiguration = new CsvConfiguration(cultureInfo: CultureInfo.InvariantCulture)
            {
                HeaderValidated = _ => { }, MissingFieldFound = _ => { },
            };
            using var reader = new StreamReader(ms);

            using var csv = new CsvReader(reader, csvReaderConfiguration);

            var result = csv.GetRecords<T>();

            return result.ToList();
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Error downloading file from Google Cloud Storage: {Message}", e.Message);
        }

        return null;
    }

    private string GetExportedFilePrefix()
    {
        return (_configuration["SF_ENVIRONMENT"] ?? string.Empty) switch
        {
            LocationNames.EastUs => "us-",
            LocationNames.SouthEastAsia => "sg-",
            LocationNames.WestEurope => "weu-",
            LocationNames.UaeNorth => "uaen-",
            LocationNames.EastAsia => string.Empty,
            _ => string.Empty
        };
    }
}