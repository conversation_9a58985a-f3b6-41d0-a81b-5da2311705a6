using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.CompanyDomain.Repositories;
using Travis_backend.ConversationDomain.ViewModels;
using Travis_backend.Enums;

namespace Travis_backend.CompanyDomain.Services;

/// <inheritdoc />
public class CompanyStaffControllerService : ICompanyStaffControllerService
{
    /// <summary>
    /// UserRoleStaffRepository.
    /// </summary>
    private readonly IUserRoleStaffRepository _userRoleStaffRepository;

    /// <summary>
    /// Initializes a new instance of the <see cref="CompanyStaffControllerService"/> class.
    /// </summary>
    /// <param name="userRoleStaffRepository">UserRoleStaffRepository.</param>
    public CompanyStaffControllerService(IUserRoleStaffRepository userRoleStaffRepository)
    {
        _userRoleStaffRepository = userRoleStaffRepository;
    }

    /// <inheritdoc />
    public async Task<IEnumerable<StaffOverviewResponse>> GetCompanyStaffAsync(string companyId, CompanyType companyType, GetCompanyStaffRequestQuery requestQuery, int offset, int limit)
    {
        Staff companyOwnerStaff = null;
        IEnumerable<GetCompanyStaffsQueryResult> queryResult;

        var roles = requestQuery.Roles?.Select(x => (StaffUserRole) Enum.Parse(typeof(StaffUserRole), x, true));

        if (companyType != CompanyType.ResellerClient)
        {
            var result = await _userRoleStaffRepository.GetCompanyStaffAsync(
                companyId,
                requestQuery.SearchString,
                roles,
                requestQuery.Teams,
                requestQuery.JoinDateTime?.From,
                requestQuery.JoinDateTime?.To,
                offset,
                limit);

            companyOwnerStaff = await _userRoleStaffRepository.GetCompanyOwnerAsync(companyId);
            queryResult = result.Staffs;
        }
        else
        {
            var result = await _userRoleStaffRepository.GetResellerCompanyStaffAsync(
                companyId,
                requestQuery.SearchString,
                roles,
                requestQuery.Teams,
                requestQuery.JoinDateTime?.From,
                requestQuery.JoinDateTime?.To,
                offset,
                limit);

            companyOwnerStaff = await _userRoleStaffRepository.GetResellerClientCompanyOwnerAsync(companyId);
            queryResult = result.Staffs;
        }

        return queryResult.Select(
            x => new StaffOverviewResponse
            {
                Id = x.Staff.Id,
                StaffIdentityId = x.Staff.IdentityId,
                Username = x.Staff.Identity.UserName,
                DisplayName = x.Staff.Identity.DisplayName,
                FirstName = x.Staff.Identity.FirstName,
                LastName = x.Staff.Identity.LastName,
                Email = x.Staff.Identity.Email,
                Role = x.Staff.RoleType.ToString(),
                ProfilePicture = x.Staff.ProfilePicture,
                AssociatedTeamIds = x.Teams.Select(team => team.CompanyTeamId).ToList(),
                IsCompanyOwner = companyOwnerStaff != null && companyOwnerStaff.Id == x.Staff.Id,
                Position = x.Staff.Position,
                Status = x.Staff.Status.ToString(),
                CreatedAt = x.Staff.Identity.CreatedAt,
            });
    }

    /// <inheritdoc />
    public Task<int> CountCompanyStaffAsync(string companyId, CompanyType companyType, GetCompanyStaffRequestQuery requestQuery)
    {
        var roles = requestQuery.Roles?.Select(x => (StaffUserRole) Enum.Parse(typeof(StaffUserRole), x, true));

        if (companyType != CompanyType.ResellerClient)
        {
            return _userRoleStaffRepository.CountCompanyStaffAsync(
                companyId,
                requestQuery.SearchString,
                roles,
                requestQuery.Teams,
                requestQuery.JoinDateTime?.From,
                requestQuery.JoinDateTime?.To);
        }

        return _userRoleStaffRepository.CountResellerCompanyStaffAsync(
            companyId,
            requestQuery.SearchString,
            roles,
            requestQuery.Teams,
            requestQuery.JoinDateTime?.From,
            requestQuery.JoinDateTime?.To);
    }

    /// <inheritdoc />
    public async Task<bool> IsCompanyOwnerAsync(string companyId, string identityId)
    {
        var companyOwnerStaff = await _userRoleStaffRepository.GetCompanyOwnerAsync(companyId);
        return companyOwnerStaff.IdentityId.Equals(identityId, StringComparison.OrdinalIgnoreCase);
    }
}