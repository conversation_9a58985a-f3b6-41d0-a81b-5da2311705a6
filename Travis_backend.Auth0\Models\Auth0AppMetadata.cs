using Newtonsoft.Json;

namespace Travis_backend.Auth0.Models;

public class Auth0AppMetadata
{
    [JsonProperty("roles", NullValueHandling = NullValueHandling.Ignore)]
    public IList<string>? Roles { get; set; }

    [JsonProperty("phone_number", NullValueHandling = NullValueHandling.Ignore)]
    public string? PhoneNumber { get; set; }

    [JsonProperty("sleekflow_id", NullValueHandling = NullValueHandling.Ignore)]
    public string? SleekflowId { get; set; }

    [JsonProperty("tenanthub_user_id", NullValueHandling = NullValueHandling.Ignore)]
    public string? TenantHubUserId { get; set; }

    [JsonProperty("login_requires_email_verification", NullValueHandling = NullValueHandling.Ignore)]
    public bool? LoginRequiresEmailVerification { get; set; }

    [JsonProperty("login_as_user", NullValueHandling = NullValueHandling.Ignore)]
    public LoginAsUser? LoginAsUser { get; set; }

    [JsonConstructor]
    public Auth0AppMetadata(
        IList<string>? roles,
        string? phoneNumber,
        string? sleekflowId,
        bool? loginRequiresEmailVerification,
        LoginAsUser? loginAsUser,
        string? tenantHubUserId = null)
    {
        Roles = roles;
        PhoneNumber = phoneNumber;
        SleekflowId = sleekflowId;
        LoginRequiresEmailVerification = loginRequiresEmailVerification;
        LoginAsUser = loginAsUser;
        TenantHubUserId = tenantHubUserId;
    }
}

/// <summary>
///
/// </summary>
public class LoginAsUser
{
    [JsonProperty("company_id", NullValueHandling = NullValueHandling.Ignore)]
    public string? CompanyId { get; set; }

    [JsonProperty("staff_id", NullValueHandling = NullValueHandling.Ignore)]
    public long? StaffId { get; set; }

    [JsonProperty("user_id", NullValueHandling = NullValueHandling.Ignore)]
    public string? UserId { get; set; }

    [JsonProperty("tenanthub_user_id", NullValueHandling = NullValueHandling.Ignore)]
    public string? TenantHubUserId { get; set; }

    [JsonProperty("roles", NullValueHandling = NullValueHandling.Ignore)]
    public IList<string>? Roles { get; set; }

    [JsonProperty("expire_at", NullValueHandling = NullValueHandling.Ignore)]
    public DateTime? ExpireAt { get; set; }
}