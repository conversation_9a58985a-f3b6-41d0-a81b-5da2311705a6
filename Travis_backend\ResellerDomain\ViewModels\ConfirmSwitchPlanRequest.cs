using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using Travis_backend.Enums;

namespace Travis_backend.ResellerDomain.ViewModels;

public class ConfirmSwitchPlanRequest
{
    [Required]
    public string CompanyId { get; set; }

    [Required]
    public string FromSubscriptionPlanId { get; set; }

    [Required]
    [JsonConverter(typeof(StringEnumConverter))]
    public SubscriptionTier FromSubscriptionTier { get; set; }

    public string ToSubscriptionPlanId { get; set; }

    [JsonConverter(typeof(StringEnumConverter))]
    public SubscriptionTier ToSubscriptionTier { get; set; }

    [Required]
    [JsonConverter(typeof(StringEnumConverter))]
    public ChangePlanType ChangePlanType { get; set; }

    [Required]
    public int Month { get; set; }
}