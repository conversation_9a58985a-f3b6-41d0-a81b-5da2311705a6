using Travis_backend.ConversationDomain.Extensions;
using Travis_backend.ConversationDomain.Models;
using Travis_backend.ConversationDomain.ViewModels;
using Travis_backend.Enums;

namespace Travis_backend.ConversationDomain.ConversationAccessControl;

public interface IConversationAccessRule
{
    bool Evaluate(StaffAccessControlAggregate staff, Conversation conversation);
}

public class CollaboratorRule : IConversationAccessRule
{
    public bool Evaluate(StaffAccessControlAggregate staff, Conversation conversation)
    {
        return staff.RoleType switch
        {
            StaffUserRole.Admin => true,
            StaffUserRole.TeamAdmin => staff.IsAssignedAsCollaborator(conversation) ||
                                       staff.HasManagedTeamMemberInCollaborators(conversation),
            StaffUserRole.Staff => staff.IsAssignedAsCollaborator(conversation),
            _ => false
        };
    }
}

public class TeamOnlyRule : IConversationAccessRule
{
    public bool Evaluate(StaffAccessControlAggregate staff, Conversation conversation)
    {
        return staff.RoleType switch
        {
            StaffUserRole.Admin => true,
            StaffUserRole.TeamAdmin => staff.IsAssociatedWithTeam(conversation.AssignedTeamId),
            StaffUserRole.Staff => staff.IsAssociatedWithTeam(conversation.AssignedTeamId),
            _ => false
        };
    }
}

public class ContactOwnerAndTeamRule : IConversationAccessRule
{
    public bool Evaluate(StaffAccessControlAggregate staff, Conversation conversation)
    {
        return staff.RoleType switch
        {
            StaffUserRole.Admin => true,
            StaffUserRole.TeamAdmin => staff.IsAssociatedWithTeam(conversation.AssignedTeamId) ||
                                       staff.IsAssignedAsContactOwner(conversation),
            StaffUserRole.Staff => staff.IsAssignedAsContactOwner(conversation),
            _ => false
        };
    }
}

public class ContactOwnerOnlyRule : IConversationAccessRule
{
    public bool Evaluate(StaffAccessControlAggregate staff, Conversation conversation)
    {
        return staff.RoleType switch
        {
            StaffUserRole.Admin => true,
            StaffUserRole.TeamAdmin => staff.IsAssignedAsContactOwner(conversation),
            StaffUserRole.Staff => staff.IsAssignedAsContactOwner(conversation),
            _ => false
        };
    }
}

public class UnassignedRule : IConversationAccessRule
{
    public bool Evaluate(StaffAccessControlAggregate staff, Conversation conversation)
    {
        return conversation.IsUnassigned();
    }
}

public class MentionedRule : IConversationAccessRule
{
    public bool Evaluate(StaffAccessControlAggregate staff, Conversation conversation)
    {
        return conversation.HasMentioned(staff);
    }
}