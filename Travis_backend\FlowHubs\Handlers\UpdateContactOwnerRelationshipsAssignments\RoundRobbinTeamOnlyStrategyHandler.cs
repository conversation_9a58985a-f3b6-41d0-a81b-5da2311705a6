﻿using System.Threading.Tasks;
using Sleekflow.Apis.FlowHub.Model;
using Travis_backend.CompanyDomain.Services;
using Travis_backend.ContactDomain.Services;
using Travis_backend.ConversationServices;
using Travis_backend.FlowHubs.Constants;
using Travis_backend.FlowHubs.Exceptions;
using Travis_backend.FlowHubs.Interfaces;

namespace Travis_backend.FlowHubs.Handlers.UpdateContactOwnerRelationshipsAssignments;

public sealed class RoundRobbinTeamOnlyStrategyHandler : IUpdateContactOwnerRelationshipsAssignmentStrategyHandler
{
    private readonly ICompanyTeamService _companyTeamService;
    private readonly IUserProfileService _userProfileService;
    private readonly IConversationMessageService _conversationMessageService;

    public string StrategyName => UpdateContactOwnerRelationshipsAssignmentStrategy.RoundRobbin_TeamOnly;

    public RoundRobbinTeamOnlyStrategyHandler(
        ICompanyTeamService companyTeamService,
        IUserProfileService userProfileService,
        IConversationMessageService conversationMessageService)
    {
        _companyTeamService = companyTeamService;
        _userProfileService = userProfileService;
        _conversationMessageService = conversationMessageService;
    }

    public async Task HandleAsync(UpdateContactOwnerRelationshipsInput input)
    {
        var workflowVersionedId = input.StateIdentity.WorkflowVersionedId;
        var companyId = input.StateIdentity.SleekflowCompanyId;
        var contactId = input.ContactId;
        var teamId = long.Parse(input.TeamId);

        var conversation = await _userProfileService.GetConversationByUserProfileId(
            companyId,
            userProfileId: contactId,
            status: "closed");

        var team = await _companyTeamService.GetCompanyTeamFromTeamId(companyId, teamId);

        if (team is null)
        {
            throw new CompanyTeamNotFoundException(
                workflowVersionedId,
                companyId,
                teamId);
        }

        await _conversationMessageService.ChangeConversationAssignedTeam(
            conversation,
            team,
            isTriggerUpdate: true,
            changedBy: null);
    }
}