using System;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Travis_backend.Helpers;

namespace Travis_backend.PartnerStackIntegrationDomain.Models;

public class PartnerStackGroup
{
    [JsonProperty("created_at")]
    [JsonConverter(typeof(JsonConvertHelper.EpochConverter))]
    public DateTime CreatedAt { get; set; }

    [Json<PERSON>roperty("key")]
    public string Key { get; set; }

    [JsonProperty("updated_at")]
    [JsonConverter(typeof(JsonConvertHelper.EpochConverter))]
    public DateTime UpdatedAt { get; set; }

    [JsonProperty("archived")]
    public bool Archived { get; set; }

    [JsonProperty("default")]
    public bool Default { get; set; }

    [JsonProperty("features")]
    public JObject Features { get; set; }

    [JsonProperty("name")]
    public string Name { get; set; }

    [JsonProperty("slug")]
    public string Slug { get; set; }
}