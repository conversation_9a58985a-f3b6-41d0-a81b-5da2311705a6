using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Sleekflow.Apis.TicketingHub.Model;
using Travis_backend.CompanyDomain.Services;
using Travis_backend.TicketingHubDomain.Attributes;
using Travis_backend.TicketingHubDomain.Services;

namespace Travis_backend.TicketingHubDomain.Controllers;

[Route("TicketingHub/Internals")]
[TicketingHubAuthorization]
public class TicketingHubInternalController : ControllerBase
{
    private readonly ITicketingHubService _ticketingHubService;
    private readonly ICompanyTeamService _companyTeamService;

    public TicketingHubInternalController(
        ITicketingHubService ticketingHubService,
        ICompanyTeamService companyTeamService)
    {
        _ticketingHubService = ticketingHubService;
        _companyTeamService = companyTeamService;
    }

    [HttpPost]
    [Route("CreateTicketConversationIndicator")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<CreateTicketConversationIndicatorOutput>> CreateTicketConversationIndicator(
        [FromBody]
        CreateTicketConversationIndicatorInput input)
    {
        await _ticketingHubService.CreateTicketConversationIndicatorMessage(input.Ticket, input.Operation, input.TicketUser);
        return new CreateTicketConversationIndicatorOutput();
    }

    public class CreateTicketConversationIndicatorInput
    {
        [JsonProperty("ticket")]
        public TicketDto Ticket { get; set; }

        [JsonProperty("operation")]
        public string Operation { get; set; }

        [JsonProperty("ticket_user")]
        public TicketUser TicketUser { get; set; }


        [JsonConstructor]
        public CreateTicketConversationIndicatorInput(TicketDto ticket, string operation, TicketUser ticketUser)
        {
            Ticket = ticket;
            Operation = operation;
            TicketUser = ticketUser;
        }
    }

    public class CreateTicketConversationIndicatorOutput
    {
    }
}