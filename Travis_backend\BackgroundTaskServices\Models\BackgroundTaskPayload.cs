﻿using System;
using System.Collections.Generic;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.ContactDomain.ViewModels;
using Travis_backend.ConversationDomain.ViewModels;

namespace Travis_backend.BackgroundTaskServices.Models
{
    public abstract class BackgroundTaskPayload
    {
        public abstract string PayloadType { get; set; }
    }

    public class AddContactsToListBackgroundTaskPayload : BackgroundTaskPayload
    {
        public string CompanyId { get; set; }

        public long ListId { get; set; }

        public UserProfileIdsViewModel UserprofileViewModel { get; set; }

        public override string PayloadType { get; set; } = nameof(AddContactsToListBackgroundTaskPayload);
    }

    public class ImportContactsBackgroundTaskPayload : BackgroundTaskPayload
    {
        public long ListId { get; set; }

        public string CompanyId { get; set; }

        public ImportSpreadsheet ImportSpreadsheet { get; set; }

        public bool IsTriggerAutomation { get; set; }

        public override string PayloadType { get; set; } = nameof(ImportContactsBackgroundTaskPayload);

        public bool IsImportIntoList { get; set; }

        public long? ImportContactToListRecordId { get; set; }
    }

    public class BulkUpdateCustomFieldsBackgroundTaskPayload : BackgroundTaskPayload
    {
        public string CompanyId { get; set; }

        public long StaffId { get; set; }

        public BulkUpdateCustomFieldsViewModel BulkUpdateCustomFieldsViewModel { get; set; }

        public override string PayloadType { get; set; } = nameof(BulkUpdateCustomFieldsBackgroundTaskPayload);
    }

    public class ExportContactsListToCsvBackgroundTaskPayload : BackgroundTaskPayload
    {
        public string CompanyId { get; set; }

        public long StaffId { get; set; }

        public UserProfileIdsViewModel UserprofileViewModel { get; set; }

        public override string PayloadType { get; set; } = nameof(ExportContactsListToCsvBackgroundTaskPayload);
    }

    public class ExportBroadcastStatusToCsvBackgroundTaskPayload : BackgroundTaskPayload
    {
        public string CompanyId { get; set; }

        public long StaffId { get; set; }

        public string BroadcastTemplateId { get; set; }

        public override string PayloadType { get; set; } = nameof(ExportBroadcastStatusToCsvBackgroundTaskPayload);
    }

    public class ExportAnalyticListToCsvBackgroundTaskPayload : BackgroundTaskPayload
    {
        public string CompanyId { get; set; }

        public string StaffId { get; set; }

        public DateTime MessageFrom { get; set; }

        public DateTime MessageTo { get; set; }

        public string Status { get; set; }

        public string Channels { get; set; }

        public string ChannelIds { get; set; }

        public string Tags { get; set; }

        public long? TeamId { get; set; }

        public bool? IsTeamUnassigned { get; set; }

        public List<Condition> Conditions { get; set; }

        public override string PayloadType { get; set; } = nameof(ExportAnalyticListToCsvBackgroundTaskPayload);
    }

    public class ImportWhatsAppHistoryTaskPayLoad : BackgroundTaskPayload
    {
        public ImportWhatsAppHistoryViewModel ImportWhatsAppHistoryViewModel { get; set; }

        public override string PayloadType { get; set; } = nameof(ImportWhatsAppHistoryTaskPayLoad);
    }

    public class ImportGenericHistoryTaskPayLoad : BackgroundTaskPayload
    {
        public ImportGenericHistoryViewModel ImportGenericHistoryViewModel { get; set; }

        public override string PayloadType { get; set; } = nameof(ImportGenericHistoryTaskPayLoad);
    }

    public class ConvertCampaignLeadsToContactListTaskPayLoad : BackgroundTaskPayload
    {
        public string CampaignId { get; set; }

        public string NewContactListName { get; set; }

        public long ListId { get; set; }

        public string CompanyId { get; set; }

        public long StaffId { get; set; }

        public override string PayloadType { get; set; } = nameof(ConvertCampaignLeadsToContactListTaskPayLoad);
    }

    public class LoopThroughAndEnrollContactsToFlowHubTaskPayLoad : BackgroundTaskPayload
    {
        public string FlowHubWorkflowId { get; set; }

        public string FlowHubWorkflowVersionedId { get; set; }

        public override string PayloadType { get; set; } = nameof(LoopThroughAndEnrollContactsToFlowHubTaskPayLoad);
    }
}