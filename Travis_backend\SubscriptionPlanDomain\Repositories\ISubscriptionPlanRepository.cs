using System.Collections;
using System.Collections.Generic;
using System.Threading.Tasks;
using Travis_backend.Enums;
using Travis_backend.SubscriptionPlanDomain.Models;

namespace Travis_backend.SubscriptionPlanDomain.Repositories;

/// <summary>
/// Repository use to access to CoreSubscriptionPlan.
/// </summary>
public interface ISubscriptionPlanRepository
{
    /// <summary>
    /// Find SubscriptionPlan by Id.
    /// </summary>
    /// <param name="id">Subscription Plan Id.</param>
    /// <returns>SubscriptionPlan</returns>
    Task<SubscriptionPlan> FindById(string id);

    /// <summary>
    /// Find SubscriptionPlan by Stripe Plan Id.
    /// </summary>
    /// <param name="stripePlanId">Stripe Plan Id.</param>
    /// <returns>SubscriptionPlan.</returns>
    Task<SubscriptionPlan> FindByStripePlanIdAsync(string stripePlanId);

    /// <summary>
    /// Find SubscriptionPlans by Id.
    /// </summary>
    /// <param name="ids">Collection of Id.</param>
    /// <returns>Collection of SubscriptionPlans.</returns>
    Task<IEnumerable<SubscriptionPlan>> FindByIds(IEnumerable<string> ids);

    /// <summary>
    /// Get base subscription plans.
    /// </summary>
    /// <param name="version">Plans Version.</param>
    /// <param name="countryTier">Country Tier.</param>
    /// <param name="currencies">Currencies.</param>
    /// <returns>Collection of SubscriptionPlan.</returns>
    Task<IEnumerable<SubscriptionPlan>> GetBaseSubscriptionPlans(int version, string countryTier, IEnumerable<string> currencies);

    /// <summary>
    /// Get Subscription Plan.
    /// </summary>
    /// <param name="subscriptionTier">Subscription Tier.</param>
    /// <param name="countryTier">Country Tier.</param>
    /// <param name="currency">Currency.</param>
    /// <param name="interval">Interval</param>
    /// <param name="version">Version.</param>
    /// <returns>SubscriptionPlan.</returns>
    Task<SubscriptionPlan> GetSubscriptionPlanAsync(SubscriptionTier subscriptionTier, string countryTier, string currency, string interval, int version);

    /// <summary>
    /// Get contact add-on plan.
    /// </summary>
    /// <param name="subscriptionTier">Subscription Tier.</param>
    /// <param name="countryTier">Country Tier.</param>
    /// <param name="currency">Currency.</param>
    /// <param name="interval">Interval.</param>
    /// <param name="version">Version.</param>
    /// <returns>SubscriptionPlan.</returns>
    Task<SubscriptionPlan> GetContactAddOnPlan(SubscriptionTier subscriptionTier, string countryTier, string currency, string interval, int version);

    /// <summary>
    /// Get agent add-on plan.
    /// </summary>
    /// <param name="subscriptionTier">Subscription Tier.</param>
    /// <param name="countryTier">Country Tier.</param>
    /// <param name="currency">Currency.</param>
    /// <param name="interval">Interval.</param>
    /// <param name="version">Version.</param>
    /// <returns>SubscriptionPlan.</returns>
    Task<SubscriptionPlan> GetAgentAddOnPlan(SubscriptionTier subscriptionTier, string countryTier, string currency, string interval, int version);

    /// <summary>
    /// Get WhatsApp Phone Number Add-On Plan.
    /// </summary>
    /// <param name="countryTier">Country Tier.</param>
    /// <param name="currency">Currency.</param>
    /// <param name="interval">Interval.</param>
    /// <param name="version">Version.</param>
    /// <returns>SubscriptionPlan.</returns>
    Task<SubscriptionPlan> GetWhatsAppPhoneNumberAddOnPlan(string countryTier, string currency, string interval, int version);

    /// <summary>
    /// Get Business Consultancy Service plan.
    /// </summary>
    /// <param name="subscriptionTier">Subscription Tier.</param>
    /// <param name="countryTier">Country Tier.</param>
    /// <param name="currency">Currency.</param>
    /// <param name="version">Version.</param>
    /// <returns>SubscriptionPlan.</returns>
    Task<SubscriptionPlan> GetBusinessConsultancyServicePlan(SubscriptionTier subscriptionTier, string countryTier, string currency, int version);

    /// <summary>
    /// Get Onboarding Support plan.
    /// </summary>
    /// <param name="subscriptionTier">Subscription Tier.</param>
    /// <param name="countryTier">Country Tier.</param>
    /// <param name="currency">Currency.</param>
    /// <param name="version">Version.</param>
    /// <returns>SubscriptionPlan.</returns>
    Task<SubscriptionPlan> GetOnboardingSupportPlan(SubscriptionTier subscriptionTier, string countryTier, string currency, int version);

    /// <summary>
    /// Get Flow Builder Flow Enrolments Plan.
    /// </summary>
    /// <param name="subscriptionTier">Subscription Tier.</param>
    /// <param name="countryTier">Country Tier.</param>
    /// <param name="currency">Currency.</param>
    /// <param name="interval">Interval.</param>
    /// <param name="version">Version.</param>
    /// <returns>Subscription Plans.</returns>
    Task<IEnumerable<SubscriptionPlan>> GetFlowBuilderFlowEnrolmentsPlans(SubscriptionTier subscriptionTier, string countryTier, string currency, string interval, int version);
}