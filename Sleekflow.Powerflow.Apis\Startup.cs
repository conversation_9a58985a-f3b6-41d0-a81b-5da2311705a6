using System.Diagnostics.Metrics;
using System.IO.Compression;
using Auth0.ManagementApi;
using Azure.Monitor.OpenTelemetry.Exporter;
using Hangfire;
using Hangfire.Pro.Redis;
using Hangfire.States;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.ResponseCompression;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection.Extensions;
using Microsoft.IdentityModel.Tokens;
using Newtonsoft.Json;
using Sleekflow.Powerflow.Apis.Controllers;
using Sleekflow.Powerflow.Apis.Extensions;
using Sleekflow.Powerflow.Apis.Services;
using Sleekflow.Powerflow.Apis.Services.Campaigns;
using Sleekflow.Powerflow.Apis.Services.Conversation.Repositories;
using Sleekflow.Powerflow.Apis.Services.Conversation.Services;
using Sleekflow.Powerflow.Apis.Services.Conversation.Services.Export;
using Sleekflow.Powerflow.Apis.Services.Conversation.Services.Mapping;
using Sleekflow.Powerflow.Apis.Services.Repositories;
using Travis_backend.AccountAuthenticationDomain.Models;
using Travis_backend.AccountAuthenticationDomain.Services;
using Travis_backend.Auth0.Configuration;
using Travis_backend.Auth0.Exceptions;
using Travis_backend.Auth0.Services;
using Travis_backend.BackgroundTaskServices.Attributes;
using Travis_backend.Configuration;
using Travis_backend.Constants;
using Travis_backend.Database;
using Travis_backend.Database.Filters;
using Travis_backend.Database.Services;
using Travis_backend.Enums;
using Travis_backend.HealthChecks;
using Travis_backend.InternalDomain.Services;
using Travis_backend.OpenTelemetry.Meters;
using Travis_backend.SignalR;
using IInternalCommerceHubService = Sleekflow.Powerflow.Apis.Services.IInternalCommerceHubService;
using IInternalPublicApiGatewayService = Sleekflow.Powerflow.Apis.Services.IInternalPublicApiGatewayService;
using InternalCommerceHubService = Sleekflow.Powerflow.Apis.Services.InternalCommerceHubService;
using InternalPublicApiGatewayService = Sleekflow.Powerflow.Apis.Services.InternalPublicApiGatewayService;
using Task = System.Threading.Tasks.Task;

namespace Sleekflow.Powerflow.Apis
{
    public class Startup
    {
        public Startup(IConfiguration configuration, IWebHostEnvironment environment)
        {
            Environment = environment;
            Configuration = configuration;
        }

        readonly string MyAllowSpecificOrigins = "_myAllowSpecificOrigins";

        public IWebHostEnvironment Environment { get; }

        public IConfiguration Configuration { get; }

        // This method gets called by the runtime. Use this method to add services to the container.
        public void ConfigureServices(IServiceCollection services)
        {
            if (System.Environment.GetEnvironmentVariable("APPLICATIONINSIGHTS_CONNECTION_STRING") != null
                && !string.IsNullOrEmpty(
                    System.Environment.GetEnvironmentVariable("APPLICATIONINSIGHTS_CONNECTION_STRING")))
            {
                services.AddOpenTelemetry()
                    .WithMetrics(
                        metrics =>
                        {
                            metrics.AddMeter(BaseMeters.SleekflowCoreMeters);
                            metrics.AddAzureMonitorMetricExporter(
                                credential =>
                                {
                                    credential.ConnectionString = System.Environment.GetEnvironmentVariable(
                                        "APPLICATIONINSIGHTS_CONNECTION_STRING");
                                });
                        });
            }

            // Meters to track the usage of the application
            services.AddSingleton<IAutomationMeters, AutomationMeters>();
            services.AddSingleton<IConversationMeters, ConversationMeters>();
            services.AddSingleton<IMetaChannelConnectionMeters, MetaChannelConnectionMeters>();

            services.Configure<NotificationHubConfiguration>(Configuration.GetSection("NotificationHub"));

            services.Configure<DataProtectionTokenProviderOptions>(
                options => options.TokenLifespan = TimeSpan.FromDays(7));

            #region Auth0

            var auth0Settings = Configuration.GetSection("Auth0").Get<Auth0Config>();

            services.AddOptions<Auth0Config>().Bind(Configuration.GetSection("Auth0"));
            services.AddSingleton<Auth0Config>(auth0Settings);
            services.Configure<IdentityOptions>(
                options =>
                {
                    // Default Password settings.
                    options.Password.RequireDigit = false;
                    options.Password.RequireLowercase = false;
                    options.Password.RequireNonAlphanumeric = false;
                    options.Password.RequireUppercase = false;
                    options.Password.RequiredLength = 6;
                    options.ClaimsIdentity.RoleClaimType = auth0Settings.Namespace + auth0Settings.RoleClaimType;
                    options.ClaimsIdentity.UserIdClaimType = auth0Settings.Namespace + auth0Settings.UserIdClaimType;
                    options.ClaimsIdentity.UserNameClaimType =
                        auth0Settings.Namespace + auth0Settings.UserNameClaimType;
                    options.ClaimsIdentity.EmailClaimType = auth0Settings.Namespace + auth0Settings.UserEmailClaimType;
                });
            services.AddSingleton<IManagementConnection, HttpClientManagementConnection>();

            services.AddCors(
                options => options.AddPolicy(
                    MyAllowSpecificOrigins,
                    builder =>
                    {
                        builder.WithOrigins("*")
                            .AllowAnyHeader()
                            .AllowAnyMethod();
                    }));

            services.AddIdentity<ApplicationUser, IdentityRole>()
                .AddUserManager<SleekflowUserManager>()
                .AddEntityFrameworkStores<ApplicationDbContext>()
                .AddTokenProvider(
                    SleekflowTokenProviderOptions.InviteTokenProviderName,
                    typeof(InvitationTokenProvider))
                .AddDefaultTokenProviders();
            services.AddTransient<IClaimsTransformation, SleekflowClaimsTransformation>();

            services
                .AddAuthentication(
                    options =>
                    {
                        options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
                        options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
                    })
                .AddJwtBearer(
                    config =>
                    {
                        config.Authority = $"https://{Configuration["Auth0:Domain"]}/";
                        config.Audience = Configuration["Auth0:Audience"];
                        config.TokenValidationParameters = new TokenValidationParameters()
                        {
                            ValidateAudience = true,
                            ValidateIssuer = true,
                            ValidateIssuerSigningKey = true,
                            ValidIssuers = Configuration
                                .GetSection("Auth0:Issuers")
                                .Get<List<string>>(),
                        };
                        config.Events = new JwtBearerEvents()
                        {
                            OnMessageReceived = context =>
                            {
                                var accessToken = context.Request.Query["access_token"];
                                var path = context.HttpContext.Request.Path;
                                if (!string.IsNullOrEmpty(accessToken) &&
                                    path.StartsWithSegments("/chat"))
                                {
                                    context.Token = accessToken;
                                }

                                return Task.CompletedTask;
                            },
                            OnAuthenticationFailed = context =>
                            {
                                return Task.FromException(context.Exception);
                            }
                        };
                    });

            services
                .AddResponseCompression(
                    options =>
                    {
                        options.Providers.Add<GzipCompressionProvider>();
                        options.Providers.Add<BrotliCompressionProvider>();
                        options.EnableForHttps = true;
                    })
                .Configure<BrotliCompressionProviderOptions>(options => { options.Level = CompressionLevel.Fastest; })
                .Configure<GzipCompressionProviderOptions>(options => { options.Level = CompressionLevel.Fastest; });

            services.AddAuth0AuthenticationClient(
                config =>
                {
                    config.Domain = auth0Settings.Domain;
                    config.ClientId = auth0Settings.ClientId;
                    config.ClientSecret = auth0Settings.ClientSecret;
                    config.Audience = auth0Settings.Audience;
                });
            services.AddAuth0ManagementClient().AddManagementAccessToken();

            #endregion

            var hangfireWorkerCount = int.TryParse(
                Configuration["Hangfire:WorkerCount"],
                out var configuredHangfireWorkerCount)
                ? configuredHangfireWorkerCount
                : 20;

            services.AddSingleton<IHangfireMeters, HangfireMeters>();
            services.AddHangfire(
                (provider, config) =>
                {
                    config.UseRedisStorage(
                            Configuration["redis:connectionString"],
                            new RedisStorageOptions
                            {
                                Prefix = "hangfire:powerflow:"
                            })
                        .WithJobExpirationTimeout(TimeSpan.FromHours(8));

                    config.UseFilter(new BackgroundJobFilterAttribute(provider.GetService<IHangfireMeters>()));
                });
            services.AddHangfireServer(
                x =>
                {
                    x.WorkerCount = hangfireWorkerCount;

                    x.Queues = [HangfireQueues.High, HangfireQueues.Medium, HangfireQueues.Low, HangfireQueues.Default];
                });

            services.AddDbContext<ApplicationDbContext>(
                options =>
                    options
                        .UseSqlServer(new SqlConnection(Configuration.GetConnectionString("DefaultConnection"))));

            // Read only database context
            services.AddDbContext<ApplicationReadDbContext>(
                optionsAction =>
                    optionsAction.UseSqlServer(new SqlConnection(Configuration.GetConnectionString("ReadConnection"))));
            services.AddScoped<ISleekflowUserService, SleekflowUserService>();
            services.AddScoped<ReadOnlyEndpointAttribute>();
            services.AddScoped<IDbContextService, DbContextService>();
            services.AddScoped<IPersistenceContext, PersistenceContext>();

            services.AddSignalR()
                .AddNewtonsoftJsonProtocol(
                    options =>
                    {
                        options.PayloadSerializerSettings.DateTimeZoneHandling =
                            Newtonsoft.Json.DateTimeZoneHandling.Utc;
                        options.PayloadSerializerSettings.MaxDepth = 48;
                    });

            services.AddAutoMapper(typeof(Startup), typeof(Travis_backend.Startup));

            services.AddMvcCore()
                .UseSpecificControllers(
                    typeof(InternalControllerBase),
                    typeof(HealthCheckController))
                .AddNewtonsoftJson(
                    options =>
                    {
                        options.SerializerSettings.NullValueHandling = NullValueHandling.Ignore;
                        options.SerializerSettings.DateTimeZoneHandling = DateTimeZoneHandling.Utc;
                    });
            services.AddControllersWithViews();

            services.AddSingleton<IHttpContextAccessor, HttpContextAccessor>();

            Travis_backend.Startup.ConfigureSleekflowServices(services, Configuration);

            ConfigurePowerflowServices(services, Configuration);
        }

        private static void ConfigurePowerflowServices(IServiceCollection services, IConfiguration configuration)
        {
            // TODO Extract if possible
            // services.AddScoped<IInternalCompanyDataRepository, InternalCompanyDataRepository>();
            // services.AddScoped<IInternalAnalyticService, InternalAnalyticService>();
            // services.AddScoped<IInternalDataSnapshotService, InternalDataSnapshotService>();
            // services.AddScoped<IInternalGoogleCloudStorage, InternalGoogleCloudStorage>();
            // services.AddScoped<IInternalHubspotRepository, InternalHubspotRepository>();
            // services.AddScoped<IInternalHubSpotService, InternalHubSpotService>();

            services.AddScoped<IInternalCommerceHubService, InternalCommerceHubService>();
            services.AddScoped<IInternalPublicApiGatewayService, InternalPublicApiGatewayService>();
            services.AddScoped<IInternalWhatsappCloudApiService, InternalWhatsappCloudApiService>();
            services.AddScoped<IInternalCommerceHubService, InternalCommerceHubService>();
            services.AddScoped<IInternalPublicApiGatewayService, InternalPublicApiGatewayService>();

            #region PowerFlow - Campaign

            services.AddScoped<IInternalCampaignService, InternalCampaignService>();
            services.AddScoped<IInternalCampaignRepository, InternalCampaignRepository>();
            services.AddScoped<IInternalCampaignMappingService, InternalCampaignMappingService>();

            #endregion

            #region PowerFlow - Company

            services.AddScoped<IInternalCompanyRepository, InternalCompanyRepository>();

            #endregion

            #region PowerFlow - Export Conversation SnapShot

            services.AddScoped<IInternalConversationSnapshotService, InternalConversationSnapshotService>();
            services.AddScoped<IInternalConversationSnapshotExportService, InternalConversationSnapshotExportService>();
            services
                .AddScoped<IInternalConversationSnapshotMappingService, InternalConversationSnapshotMappingService>();
            services.AddScoped<IInternalConversationRepository, InternalConversationRepository>();

            #endregion

            #region PowerFlow - Channel

            services.AddScoped<IInternalFacebookChannelService, InternalFacebookChannelService>();
            services.AddScoped<IInternalInstagramChannelService, InternalInstagramChannelService>();

            #endregion

            #region PowerFlow - CrmHub

            services.AddScoped<IInternalCrmHubService, InternalCrmHubService>();

            #endregion
        }


        // This method gets called by the runtime. Use this method to configure the HTTP request pipeline.
        public void Configure(IApplicationBuilder app, IWebHostEnvironment env, ILogger<Startup> logger)
        {
            app.UseTravisBackendHealthChecks();
            app.UseResponseCompression();
            app.UseExceptionHandler("/error");
            app.UseClaimsTransformationExceptionHandler(); // Add this line

            if (!env.IsDevelopment())
            {
                // The default HSTS value is 30 days. You may want to change this for production scenarios, see https://aka.ms/aspnetcore-hsts.
                app.UseHsts();
            }

            app.UseCustomSwagger();

            app.UseStaticFiles();
            // app.UseCookiePolicy();

            app.UseRouting();
            app.UseCors(MyAllowSpecificOrigins);

            app.UseAuthentication();
            app.UseAuthorization();

            app.UseHangfireDashboard(
                "/hangfire",
                new DashboardOptions
                {
                    Authorization = new[]
                    {
                        new Travis_backend.Startup.MyAuthorizationFilter()
                    },
                    IgnoreAntiforgeryToken = true
                });

            app.UseEndpoints(
                endpoints =>
                {
                    endpoints.MapControllerRoute("default", "{controller=Home}/{action=Index}/{id?}");
                    endpoints.MapHub<Chat>("/chat");
                });
        }
    }
}