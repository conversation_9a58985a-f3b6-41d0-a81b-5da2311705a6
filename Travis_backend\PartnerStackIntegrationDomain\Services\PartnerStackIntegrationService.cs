using System;
using System.Net.Http;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json.Linq;
using Travis_backend.Constants;
using Travis_backend.PartnerStackIntegrationDomain.Clients;
using Travis_backend.PartnerStackIntegrationDomain.Models;

namespace Travis_backend.PartnerStackIntegrationDomain.Services;

public interface IPartnerStackIntegrationService
{
    Task<PartnerStackCustomer> RetrievePartnerStackCustomerByCustomerKey(
        string customerKey);

    Task<ListPartnerStackTransactionsData> ListPartnerStackTransactions(
        string customerKey = null,
        string customerExternalKey = null,
        string customerEmail = null,
        string productKey = null,
        string categoryKey = null,
        int limit = 10,
        string startingAfter = null,
        string endingBefore = null);

    Task<PartnerStackTransaction> CreatePartnerStackTransaction(
        string customerExternalKey,
        int amount,
        string currency,
        string categoryKey = null,
        string productKey = null,
        JObject metadata = null);

    Task<PartnerStackPartnership> RetrievePartnerStackPartnershipByUniqueIdentifier(
        string uniqueIdentifier);
}

public class PartnerStackIntegrationService : IPartnerStackIntegrationService
{
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly IConfiguration _configuration;
    private readonly ILogger<PartnerStackIntegrationService> _logger;

    private readonly string _publicKey;
    private readonly string _secretKey;

    public PartnerStackIntegrationService(
        IHttpClientFactory httpClientFactory,
        IConfiguration configuration,
        ILogger<PartnerStackIntegrationService> logger)
    {
        _httpClientFactory = httpClientFactory;
        _configuration = configuration;
        _logger = logger;
        _publicKey = _configuration.GetValue<string>("PartnerStack:PublicKey");
        _secretKey = _configuration.GetValue<string>("PartnerStack:SecretKey");
    }

    private bool CheckIsPartnerStackIntegrationEnabled()
    {
        return !string.IsNullOrEmpty(_publicKey) && !string.IsNullOrEmpty(_secretKey);
    }

    public async Task<PartnerStackCustomer> RetrievePartnerStackCustomerByCustomerKey(
        string customerKey)
    {
        try
        {
            if (!CheckIsPartnerStackIntegrationEnabled())
            {
                _logger.LogWarning(
                    "[PartnerStack Integration {Method}] PartnerStack integration is not enabled",
                    nameof(RetrievePartnerStackCustomerByCustomerKey));
                return null;
            }

            var httpClient = _httpClientFactory.CreateClient(HttpClientHandlerName.Default);
            var client = new PartnerStackIntegrationClient(httpClient, _publicKey, _secretKey);

            var response = await client.RetrievePartnerStackCustomerByCustomerKey(customerKey);
            return response.Data;
        }
        catch (Exception e)
        {
            _logger.LogError(
                e,
                "[PartnerStack Integration {Method}] Failed to retrieve Partner Stack customer by customer key: {CustomerKey}",
                nameof(RetrievePartnerStackCustomerByCustomerKey),
                customerKey);
        }

        return null;
    }

    public async Task<ListPartnerStackTransactionsData> ListPartnerStackTransactions(
        string customerKey = null,
        string customerExternalKey = null,
        string customerEmail = null,
        string productKey = null,
        string categoryKey = null,
        int limit = 10,
        string startingAfter = null,
        string endingBefore = null)
    {
        try
        {
            if (!CheckIsPartnerStackIntegrationEnabled())
            {
                _logger.LogWarning(
                    "[PartnerStack Integration {Method}] PartnerStack integration is not enabled",
                    nameof(ListPartnerStackTransactions));
                return null;
            }

            var httpClient = _httpClientFactory.CreateClient(HttpClientHandlerName.Default);
            var client = new PartnerStackIntegrationClient(httpClient, _publicKey, _secretKey);

            var response = await client.ListPartnerStackTransactions(
                customerKey,
                customerExternalKey,
                customerEmail,
                productKey,
                categoryKey,
                limit,
                startingAfter,
                endingBefore);
            return response.Data;
        }
        catch (Exception e)
        {
            _logger.LogError(
                e,
                "[PartnerStack Integration {Method}] Failed to list Partner Stack transactions",
                nameof(ListPartnerStackTransactions));
        }

        return null;
    }

    public async Task<PartnerStackTransaction> CreatePartnerStackTransaction(
        string customerExternalKey,
        int amount,
        string currency,
        string categoryKey = null,
        string productKey = null,
        JObject metadata = null)
    {
        try
        {
            if (!CheckIsPartnerStackIntegrationEnabled())
            {
                _logger.LogWarning(
                    "[PartnerStack Integration {Method}] PartnerStack integration is not enabled",
                    nameof(CreatePartnerStackTransaction));
                return null;
            }

            var httpClient = _httpClientFactory.CreateClient(HttpClientHandlerName.Default);
            var client = new PartnerStackIntegrationClient(httpClient, _publicKey, _secretKey);

            var response = await client.CreatePartnerStackTransaction(
                customerExternalKey,
                amount,
                currency,
                categoryKey,
                productKey,
                metadata);
            return response.Data;
        }
        catch (Exception e)
        {
            _logger.LogError(
                e,
                "[PartnerStack Integration {Method}] Failed to create Partner Stack transaction",
                nameof(CreatePartnerStackTransaction));
        }

        return null;
    }

    public async Task<PartnerStackPartnership> RetrievePartnerStackPartnershipByUniqueIdentifier(
        string uniqueIdentifier)
    {
        try
        {
            if (!CheckIsPartnerStackIntegrationEnabled())
            {
                _logger.LogWarning(
                    "[PartnerStack Integration {Method}] PartnerStack integration is not enabled",
                    nameof(RetrievePartnerStackPartnershipByUniqueIdentifier));
                return null;
            }

            var httpClient = _httpClientFactory.CreateClient(HttpClientHandlerName.Default);
            var client = new PartnerStackIntegrationClient(httpClient, _publicKey, _secretKey);

            var response = await client.RetrievePartnerStackPartnershipByUniqueIdentifier(uniqueIdentifier);
            return response.Data;
        }
        catch (Exception e)
        {
            _logger.LogError(
                e,
                "[PartnerStack Integration {Method}] Failed to retrieve Partner Stack partnership by unique identifier: {UniqueIdentifier}",
                nameof(RetrievePartnerStackPartnershipByUniqueIdentifier),
                uniqueIdentifier);
        }

        return null;
    }
}