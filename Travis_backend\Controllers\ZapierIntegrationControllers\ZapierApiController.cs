﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using AutoMapper;
using Hangfire;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.StaticFiles;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Newtonsoft.Json.Serialization;
using Sleekflow.Apis.CrmHub.Api;
using Sleekflow.Apis.CrmHub.Client;
using Sleekflow.Apis.CrmHub.Model;
using Sleekflow.Apis.MessagingHub.Model;
using Travis_backend.AccountAuthenticationDomain.Models;
using Travis_backend.AutomationDomain.Models;
using Travis_backend.Cache;
using Travis_backend.Cache.Models.CacheKeyPatterns;
using Travis_backend.ChannelDomain.Models;
using Travis_backend.ChannelDomain.Services;
using Travis_backend.ChannelDomain.ViewModels;
using Travis_backend.CommonDomain.ViewModels;
using Travis_backend.CompanyDomain.ApiKeyResolvers;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.Constants;
using Travis_backend.ContactDomain.Constants;
using Travis_backend.ContactDomain.Models;
using Travis_backend.ContactDomain.Services;
using Travis_backend.ContactDomain.ViewModels;
using Travis_backend.ConversationDomain.Models;
using Travis_backend.ConversationDomain.ViewModels;
using Travis_backend.ConversationServices;
using Travis_backend.CustomObjectDomain.Constants;
using Travis_backend.CustomObjectDomain.Helpers;
using Travis_backend.CustomObjectDomain.Services;
using Travis_backend.Data.WhatsappCloudApi;
using Travis_backend.Database;
using Travis_backend.Enums;
using Travis_backend.Exceptions;
using Travis_backend.Helpers;
using Travis_backend.Infrastructures.Attributes;
using Travis_backend.MessageDomain.Models;
using Travis_backend.MessageDomain.Models.Interfaces;
using Travis_backend.MessageDomain.ViewModels;
using Travis_backend.ZapierIntegrationDomain.Models;
using Travis_backend.ZapierIntegrationDomain.ViewModels;
using WABA360Dialog.ApiClient.Payloads.Enums;
using FacebookSender = Travis_backend.MessageDomain.Models.FacebookSender;
using LineSender = Travis_backend.MessageDomain.Models.LineSender;
using SMSSender = Travis_backend.MessageDomain.Models.SMSSender;
using WeChatSender = Travis_backend.MessageDomain.Models.WeChatSender;
using Whatsapp360DialogExtendedMessagePayload = Travis_backend.MessageDomain.Models.Whatsapp360DialogExtendedMessagePayload;
using WhatsApp360DialogSender = Travis_backend.MessageDomain.Models.WhatsApp360DialogSender;
using WhatsAppSender = Travis_backend.MessageDomain.Models.WhatsAppSender;

namespace Travis_backend.Controllers.ZapierIntegrationControllers
{
    [UpdateUserProfileTriggerSource(UpdateUserProfileTriggerSource.ZapierIntegration)]
    [TypeFilter(typeof(ApiKeyExceptionFilter))]
    public class ZapierApiController : Controller
    {
        private readonly ApplicationDbContext _appDbContext;
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly IMapper _mapper;
        private readonly ILogger _logger;
        private readonly IConfiguration _configuration;
        private readonly IConversationMessageService _conversationMessageService;
        private readonly IAzureBlobStorageService _azureBlobStorageService;
        private readonly IUserProfileService _userProfileService;
        private readonly ILockService _lockService;
        private readonly IAuditHubAuditLogService _auditHubAuditLogService;
        private readonly ICacheManagerService _cacheManagerService;
        private readonly IWhatsApp360DialogService _whatsApp360DialogService;
        private readonly IWhatsappCloudApiService _whatsappCloudApiService;
        private readonly ICustomObjectService _customObjectService;
        private readonly ISchemafulObjectsApi _schemafulObjectsApi;
        private readonly IApiKeyResolver _zapierApiKeyResolver;
        private readonly IContactWhatsappSenderLockService _contactWhatsappSenderLockService;

        public ZapierApiController(
            ApplicationDbContext appDbContext,
            UserManager<ApplicationUser> userManager,
            IMapper mapper,
            IConfiguration configuration,
            ILogger<ZapierApiController> logger,
            IConversationMessageService messagingService,
            IUserProfileService userProfileService,
            ILockService lockService,
            IAzureBlobStorageService azureBlobStorageService,
            ICacheManagerService cacheManagerService,
            IAuditHubAuditLogService auditHubAuditLogService,
            IWhatsApp360DialogService whatsApp360DialogService,
            IWhatsappCloudApiService whatsappCloudApiService,
            ISchemafulObjectsApi schemafulObjectsApi,
            ICustomObjectService customObjectService,
            ApiKeyResolverFactory apiKeyResolverFactory,
            IContactWhatsappSenderLockService contactWhatsappSenderLockService)
        {
            _userManager = userManager;
            _appDbContext = appDbContext;
            _mapper = mapper;
            _configuration = configuration;
            _logger = logger;
            _conversationMessageService = messagingService;
            _azureBlobStorageService = azureBlobStorageService;
            _userProfileService = userProfileService;
            _cacheManagerService = cacheManagerService;
            _lockService = lockService;
            _auditHubAuditLogService = auditHubAuditLogService;
            _whatsApp360DialogService = whatsApp360DialogService;
            _whatsappCloudApiService = whatsappCloudApiService;
            _customObjectService = customObjectService;
            _schemafulObjectsApi = schemafulObjectsApi;
            _zapierApiKeyResolver = apiKeyResolverFactory.GetApiKeyResolver(ApiKeyTypes.Zapier);
            _contactWhatsappSenderLockService = contactWhatsappSenderLockService;
        }

        #region Example

        /// <summary>
        /// Sample endpoint for test API Key authentication and usage.
        /// </summary>
        /// <param name="apikey">apiKey.</param>
        /// <returns>ApiUsage.</returns>
        [HttpPost]
        [Route("api/zapier/verifyEndpoint")]
        public async Task<IActionResult> VerifyEndpoint([FromQuery(Name = "apikey")] string apikey)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest();
            }

            var apiKey = await _zapierApiKeyResolver.AuthenticateAsync(apikey);
            await _zapierApiKeyResolver.AddUsageAsync(apiKey);

            var apiUsage = await _zapierApiKeyResolver.GetApiUsageAsync(apiKey);

            return Ok(apiUsage);
        }

        #endregion

        [HttpGet]
        [Route("api/zapier/me")]
        public async Task<IActionResult> GetKeyInfo([FromQuery(Name = "apikey")] string apikey)
        {
            try
            {
                var apiKey = await _zapierApiKeyResolver.AuthenticateAsync(apikey);

                var company = await _appDbContext.CompanyCompanies
                    .Where(x => x.Id == apiKey.CompanyId)
                    .FirstOrDefaultAsync();

                dynamic result = new JObject();
                result.result = "authenticated";
                result.companyName = company.CompanyName;
                result.permissions = string.Join(",", apiKey.Permissions ?? new List<string>());

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[Zapier {MethodName} endpoint] error: {ExceptionMessage}",
                    nameof(GetKeyInfo),
                    ex.Message);

                return BadRequest(
                    new ResponseViewModel
                    {
                        message = ex.Message
                    });
            }
        }

        [HttpPost]
        [Route("api/zapier/Message/Send")]
        public async Task<IActionResult> APIV1Send(
            [FromQuery(Name = "apikey")]
            string apikey,
            [FromBody]
            SendMessageFromAPIViewModel sendMessageFromAPIViewModel)
        {
            if (ModelState.IsValid)
            {
                var apiKey = await _zapierApiKeyResolver.AuthenticateAsync(apikey);

                _logger.LogInformation(
                    $"Zapier SendMessage: {apiKey.CompanyId}, {JsonConvert.SerializeObject(sendMessageFromAPIViewModel)}");

                var key = $"{apikey}_{sendMessageFromAPIViewModel.To}_{sendMessageFromAPIViewModel.From}";
                while (true)
                {
                    var mylock = await _lockService.AcquireLockAsync(key, TimeSpan.FromSeconds(2));
                    if (mylock == null)
                    {
                        await Task.Delay(TimeSpan.FromSeconds(5));
                    }
                    else
                    {
                        break;
                    }
                }

                var company = await (from _company in _appDbContext.CompanyCompanies
                    where _company.Id == apiKey.CompanyId
                    select _company).FirstOrDefaultAsync();

                var conversation = new Conversation
                {
                    MessageGroupName = company.SignalRGroupName, CompanyId = company.Id
                };

                var conversationMessage = new MessageDomain.Models.ConversationMessage
                {
                    Channel = sendMessageFromAPIViewModel.Channel,
                    MessageType = sendMessageFromAPIViewModel.MessageType,
                    MessageContent = sendMessageFromAPIViewModel.MessageContent,
                    DeliveryType = DeliveryType.AutomatedMessage,
                    CompanyId = company.Id,
                    AnalyticTags = sendMessageFromAPIViewModel.AnalyticTags,
                };

                switch (conversationMessage.Channel)
                {
                    case ChannelTypes.Facebook:
                        conversation = await _appDbContext.Conversations
                            .Include(x => x.facebookUser)
                            .FirstOrDefaultAsync(
                                x =>
                                    x.facebookUser.FacebookId == sendMessageFromAPIViewModel.FacebookReceiverId
                                    && x.CompanyId == company.Id);

                        if (conversation.facebookUser == null)
                        {
                            conversation.facebookUser = await _appDbContext.SenderFacebookSenders
                                .FirstOrDefaultAsync(
                                    x =>
                                        x.FacebookId == sendMessageFromAPIViewModel.FacebookReceiverId
                                        && x.CompanyId == company.Id);

                            if (conversation.facebookUser == null)
                            {
                                conversation.facebookUser = new FacebookSender
                                {
                                    FacebookId = sendMessageFromAPIViewModel.FacebookReceiverId, CompanyId = company.Id
                                };
                            }
                        }

                        conversationMessage.facebookReceiver = conversation.facebookUser;

                        break;
                    case ChannelTypes.WhatsappTwilio:
                        var whatsappUser = new WhatsAppSender();

                        var twilio_sender = await _appDbContext.ConfigWhatsAppConfigs
                            .FirstOrDefaultAsync(x => x.WhatsAppSender.Contains(sendMessageFromAPIViewModel.From));

                        if (twilio_sender == null)
                        {
                            return BadRequest(
                                new ResponseViewModel
                                {
                                    message = "no sender found"
                                });
                        }

                        whatsappUser = await _appDbContext.SenderWhatsappSenders
                            .FirstOrDefaultAsync(
                                x =>
                                    x.whatsAppId == $"whatsapp:+{sendMessageFromAPIViewModel.To}"
                                    && x.CompanyId == company.Id
                                    && x.InstanceId == twilio_sender.TwilioAccountId);

                        if (whatsappUser == null)
                        {
                            whatsappUser = new WhatsAppSender
                            {
                                whatsAppId = $"whatsapp:+{sendMessageFromAPIViewModel.To}",
                                InstanceId = twilio_sender.TwilioAccountId,
                                CompanyId = company.Id,
                                phone_number = sendMessageFromAPIViewModel.To,
                                name = sendMessageFromAPIViewModel.To
                            };
                        }

                        var existingConversation = await _appDbContext.Conversations
                            .Include(x => x.WhatsappUser)
                            .FirstOrDefaultAsync(
                                x =>
                                    x.WhatsappUser.whatsAppId == whatsappUser.whatsAppId
                                    && x.WhatsappUser.InstanceId == whatsappUser.InstanceId
                                    && x.CompanyId == company.Id);

                        if (existingConversation != null)
                        {
                            conversation = existingConversation;
                        }
                        else
                        {
                            conversation.WhatsappUser = whatsappUser;
                        }

                        conversationMessage.whatsappReceiver = conversation.WhatsappUser;

                        break;
                    case ChannelTypes.LiveChat:
                        if (!string.IsNullOrEmpty(sendMessageFromAPIViewModel.WebClientSenderId))
                        {
                            conversationMessage.WebClientSender = await _appDbContext.SenderWebClientSenders
                                .FirstOrDefaultAsync(
                                    x =>
                                        x.WebClientUUID == sendMessageFromAPIViewModel.WebClientSenderId
                                        && x.CompanyId == company.Id);

                            conversation.WebClient = conversationMessage.WebClientSender;
                        }
                        else if (!string.IsNullOrEmpty(sendMessageFromAPIViewModel.WebClientReceiverId))
                        {
                            if (conversationMessage.Sender == null)
                            {
                                return BadRequest(
                                    new ResponseViewModel
                                    {
                                        message = "Please login as staff to reply"
                                    });
                            }

                            conversationMessage.WebClientReceiver = await _appDbContext.SenderWebClientSenders
                                .FirstOrDefaultAsync(
                                    x =>
                                        x.WebClientUUID == sendMessageFromAPIViewModel.WebClientReceiverId
                                        && x.CompanyId == company.Id);

                            conversation.WebClient = conversationMessage.WebClientReceiver;
                        }

                        if (conversation.WebClient == null)
                        {
                            return BadRequest();
                        }

                        break;
                    case ChannelTypes.Wechat:
                        conversation = await _appDbContext.Conversations
                            .Include(x => x.WeChatUser)
                            .FirstOrDefaultAsync(
                                x =>
                                    x.WeChatUser.openid == sendMessageFromAPIViewModel.WeChatReceiverOpenId
                                    && x.CompanyId == company.Id);

                        if (conversation.WeChatUser == null)
                        {
                            var wechatId = await _appDbContext.ConfigWeChatConfigs
                                .Where(x => x.Id == company.WeChatConfigId)
                                .Select(x => x.AppId)
                                .FirstOrDefaultAsync();

                            conversation.WeChatUser = new WeChatSender
                            {
                                openid = sendMessageFromAPIViewModel.WeChatReceiverOpenId,
                                CompanyId = company.Id,
                                ChannelIdentityId = wechatId,
                            };
                        }

                        conversationMessage.WeChatReceiver = conversation.WeChatUser;

                        break;
                    case ChannelTypes.Line:
                        conversation = await _appDbContext.Conversations
                            .Include(x => x.LineUser)
                            .FirstOrDefaultAsync(
                                x =>
                                    x.LineUser.userId == sendMessageFromAPIViewModel.LineReceiverId
                                    && x.CompanyId == company.Id);

                        if (conversation.LineUser == null)
                        {
                            var lineChannelId = await _appDbContext.ConfigLineConfigs
                                .Where(x => x.CompanyId == company.Id)
                                .Select(x => x.ChannelID)
                                .FirstOrDefaultAsync();

                            conversation.LineUser = new LineSender
                            {
                                userId = sendMessageFromAPIViewModel.LineReceiverId,
                                CompanyId = company.Id,
                                ChannelIdentityId = lineChannelId,
                            };
                        }

                        conversationMessage.LineReceiver = conversation.LineUser;

                        break;
                    case ChannelTypes.Sms:
                        var smsSender = new SMSSender();

                        var smsConfig = await _appDbContext.ConfigSMSConfigs
                            .FirstOrDefaultAsync(x => x.SMSSender.Contains(sendMessageFromAPIViewModel.From));

                        if (smsConfig == null)
                        {
                            return BadRequest(
                                new ResponseViewModel
                                {
                                    message = "no sender found"
                                });
                        }
                        else
                        {
                            smsSender = await _appDbContext.SenderSMSSenders
                                .FirstOrDefaultAsync(
                                    x =>
                                        x.SMSId == $"+{sendMessageFromAPIViewModel.To}"
                                        && x.CompanyId == company.Id
                                        && x.InstanceId == smsConfig.TwilioAccountId);

                            if (smsSender == null)
                            {
                                smsSender = new SMSSender
                                {
                                    SMSId = $"+{sendMessageFromAPIViewModel.To}",
                                    InstanceId = smsConfig.TwilioAccountId,
                                    CompanyId = company.Id,
                                    phone_number = sendMessageFromAPIViewModel.To,
                                    name = sendMessageFromAPIViewModel.To
                                };
                            }
                        }

                        var existingSMSConversation = await _appDbContext.Conversations
                            .Include(x => x.SMSUser)
                            .FirstOrDefaultAsync(
                                x =>
                                    x.SMSUser.SMSId == smsSender.SMSId
                                    && x.SMSUser.InstanceId == smsSender.InstanceId
                                    && x.CompanyId == company.Id);

                        if (existingSMSConversation != null)
                        {
                            conversation = existingSMSConversation;
                        }
                        else
                        {
                            conversation.SMSUser = smsSender;
                        }

                        conversationMessage.SMSReceiver = conversation.SMSUser;

                        break;
                    case ChannelTypes.Whatsapp360Dialog:
                        var whatsApp360DialogConversationTupleForSending =
                            await PrepareWhatsApp360DialogMessageForSending(
                                sendMessageFromAPIViewModel.From,
                                sendMessageFromAPIViewModel.To,
                                sendMessageFromAPIViewModel.ExtendedMessageJson,
                                conversation,
                                conversationMessage);

                        conversation = whatsApp360DialogConversationTupleForSending.Conversation;
                        conversationMessage = whatsApp360DialogConversationTupleForSending.ConversationMessage;

                        break;
                    case ChannelTypes.WhatsappCloudApi:
                        var whatsappCloudApiConversationTupleForSending =
                            await PrepareWhatsAppCloudApiMessageForSending(
                                sendMessageFromAPIViewModel.From,
                                sendMessageFromAPIViewModel.To,
                                sendMessageFromAPIViewModel.ExtendedMessageJson,
                                conversation,
                                conversationMessage);

                        conversation = whatsappCloudApiConversationTupleForSending.Conversation;
                        conversationMessage = whatsappCloudApiConversationTupleForSending.ConversationMessage;

                        break;
                }

                try
                {
                    ILockService.Lock myLock = null;

                    if (sendMessageFromAPIViewModel.Channel is ChannelTypes.WhatsappCloudApi
                        or ChannelTypes.Whatsapp360Dialog or ChannelTypes.WhatsappTwilio)
                    {
                        myLock = await _contactWhatsappSenderLockService.GetSendWhatsappMessageLock(
                            company.Id,
                            sendMessageFromAPIViewModel.To,
                            sendMessageFromAPIViewModel.Channel,
                            sendMessageFromAPIViewModel.From);
                    }

                    IList<MessageDomain.Models.ConversationMessage> results;
                    if (sendMessageFromAPIViewModel.fileURLs?.Count > 0)
                    {
                        var provider = new FileExtensionContentTypeProvider();

                        var fileUrlMessages = new List<FileURLMessage>();
                        foreach (var url in sendMessageFromAPIViewModel.fileURLs)
                        {
                            if (!provider.TryGetContentType(url, out var contentType))
                            {
                                contentType = "application/octet-stream";
                            }

                            fileUrlMessages.Add(
                                new FileURLMessage
                                {
                                    FileName = Path.GetFileName(url), FileURL = url, MIMEType = contentType
                                });
                        }

                        // Send File Url Message
                        results = await _conversationMessageService.SendFileMessageByFBURL(
                            conversation,
                            conversationMessage,
                            fileUrlMessages);
                    }
                    else
                    {
                        results = sendMessageFromAPIViewModel.MessageType switch
                        {
                            "file" => await _conversationMessageService.SendFileMessage(
                                conversation,
                                conversationMessage,
                                new ConversationMessageViewModel
                                {
                                    files = sendMessageFromAPIViewModel.files
                                }),
                            "text" => await _conversationMessageService.SendMessage(conversation, conversationMessage),
                            "template" => await _conversationMessageService.SendMessage(
                                conversation,
                                conversationMessage),
                            "interactive" => await _conversationMessageService.SendMessage(
                                conversation,
                                conversationMessage),
                            "contacts" => await _conversationMessageService.SendMessage(
                                conversation,
                                conversationMessage),
                            "location" => await _conversationMessageService.SendMessage(
                                conversation,
                                conversationMessage),
                            "reaction" => await _conversationMessageService.SendMessage(
                                conversation,
                                conversationMessage),
                            _ => throw new ArgumentException("Unsupported message type.")
                        };
                    }

                    if (myLock != null && sendMessageFromAPIViewModel.Channel is ChannelTypes.WhatsappCloudApi
                            or ChannelTypes.Whatsapp360Dialog or ChannelTypes.WhatsappTwilio)
                    {
                        await _lockService.ReleaseLockAsync(myLock);
                    }

                    var conversationMessagesVM =
                        _mapper.Map<ConversationMessageResponseViewModel>(results.FirstOrDefault());

                    return Ok(conversationMessagesVM);
                }
                catch (Exception ex)
                {
                    _logger.LogError(
                        ex,
                        "[Zapier {MethodName}  endpoint] error, channel: {Channel}, from: {From}, to: {To}. {ExceptionMessage}",
                        nameof(APIV1Send),
                        sendMessageFromAPIViewModel?.Channel,
                        sendMessageFromAPIViewModel?.From,
                        sendMessageFromAPIViewModel?.To,
                        ex.Message);

                    if (ex is ContactWhatsappSenderLockException)
                    {
                        return BadRequest(
                            new ResponseViewModel
                            {
                                message = "Rejected"
                            });
                    }

                    return BadRequest(ex.Message);
                }
            }

            return BadRequest(
                new ResponseViewModel
                {
                    message = "Unknown error"
                });
        }

        [HttpPost]
        [Route("api/zapier/Contact/Add")]
        public async Task<IActionResult> ZapierAddContact(
            [FromQuery(Name = "apikey")]
            string apikey,
            [FromBody]
            ZapierViewModel zapierViewModel)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        message = $"Incorrect format: {ModelState}"
                    });
            }

            var apiKey = await _zapierApiKeyResolver.AuthenticateAsync(apikey);

            var company = await (from _company in _appDbContext.CompanyCompanies
                where _company.Id == apiKey.CompanyId
                select _company).FirstOrDefaultAsync();

            if (company == null)
            {
                return Unauthorized();
            }

            if (string.IsNullOrEmpty(zapierViewModel.Email) &&
                string.IsNullOrEmpty(zapierViewModel.WhatsAppPhoneNumber))
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        message = $"Email and phone number cannot be null"
                    });
            }

            var i = 0;
            var key = $"{apikey}_{zapierViewModel.Email}_{zapierViewModel.WhatsAppPhoneNumber}";
            while (true)
            {
                var myLock = await _lockService.AcquireLockAsync(key, TimeSpan.FromSeconds(5));

                if (myLock == null)
                {
                    await Task.Delay(TimeSpan.FromSeconds(5));
                    i++;
                    if (i > 2)
                    {
                        return BadRequest(
                            new ResponseViewModel
                            {
                                message = $"Rejected"
                            });
                    }
                }
                else
                {
                    break;
                }
            }

            _logger.LogInformation(
                "Zapier AddOrUpdateUserProfile for company {CompanyId}, Payload: {Payload}",
                company.Id,
                JsonConvert.SerializeObject(zapierViewModel));

            try
            {
                var whatsappPhoneNumberLock =
                    await _contactWhatsappSenderLockService.GetContactWhatsappPhoneNumberLockAsync(
                        apiKey.CompanyId,
                        zapierViewModel.WhatsAppPhoneNumber);

                var newProfileViewModel = _mapper.Map<NewProfileViewModel>(zapierViewModel);
                newProfileViewModel.UserProfileFields = new List<AddCustomFieldsViewModel>();

                try
                {
                    if (zapierViewModel.CustomFieldNames != null)
                    {
                        foreach (var fieldName in zapierViewModel.CustomFieldNames)
                        {
                            var customFieldName = fieldName;
                            var value = string.Empty;

                            if (fieldName.Contains(":"))
                            {
                                var fieldValue = fieldName.Split(":");
                                customFieldName = fieldValue[0];
                                value = fieldName.Substring(fieldName.IndexOf(":") + 1).Trim();
                            }
                            else
                            {
                                value = zapierViewModel.CustomFieldValues[
                                    zapierViewModel.CustomFieldNames.IndexOf(fieldName)];
                            }

                            newProfileViewModel.UserProfileFields.Add(
                                new AddCustomFieldsViewModel
                                {
                                    CustomFieldName = customFieldName, CustomValue = value
                                });
                            newProfileViewModel.Labels = zapierViewModel.Labels;
                            newProfileViewModel.ListIds = zapierViewModel.ListIds;
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(
                        ex,
                        "[Zapier {MethodName} endpoint] Company {CompanyId} error parsing custom field names. {ExceptionMessage}. CustomFieldNames: {Payload}",
                        nameof(ZapierAddContact),
                        company.Id,
                        ex.Message,
                        JsonConvert.SerializeObject(zapierViewModel.CustomFieldNames));
                }

                try
                {
                    var sleekflowCompanyId = _configuration.GetValue<string>("Values:SleekFlowCompanyId");

                    if (company.Id == sleekflowCompanyId)
                    {
                        if (newProfileViewModel.UserProfileFields.Any(
                                x => x.CustomFieldName.ToUpper() == "DEMO SCHEDULED"))
                        {
                            var user = await _userManager.FindByEmailAsync(newProfileViewModel.Email);

                            var userProfile = await _userProfileService.GetUserProfilesByFields(
                                company.Id,
                                new List<Condition>
                                {
                                    new Condition
                                    {
                                        FieldName = "email",
                                        ConditionOperator = SupportedOperator.Equals,
                                        Values = new List<string>
                                        {
                                            newProfileViewModel.Email
                                        }
                                    }
                                });

                            var contactOwner = string.Empty;

                            if (!string.IsNullOrEmpty(userProfile.UserProfiles?.FirstOrDefault()?.ContactOwnerId))
                            {
                                var _contactOwner = await _appDbContext.UserRoleStaffs
                                    .Include(x => x.Identity)
                                    .FirstOrDefaultAsync(
                                        x => x.IdentityId == userProfile.UserProfiles.FirstOrDefault().ContactOwnerId);

                                if (_contactOwner != null)
                                {
                                    contactOwner = _contactOwner.Identity.Email;
                                }
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(
                        ex,
                        "[Zapier {MethodName} endpoint] Company {CompanyId} error processing 'DEMO SCHEDULED' custom field. {ExceptionMessage}",
                        nameof(ZapierAddContact),
                        company.Id,
                        ex.Message);
                }


                List<UserProfile> targetUserProfiles =
                    await _userProfileService.AddOrUpdateUserProfile(company.Id, newProfileViewModel);

                if (whatsappPhoneNumberLock != null)
                {
                    await _lockService.ReleaseLockAsync(whatsappPhoneNumberLock);
                }

                await LogActivityLog(targetUserProfiles.FirstOrDefault(), zapierViewModel);

                var userProfileResponse =
                    _mapper.Map<UserProfileNoCompanyResponse>(targetUserProfiles.FirstOrDefault());

                return Ok(userProfileResponse);
            }
            catch (FormatException fex)
            {
                try
                {
                    var errorDetail = JsonConvert.DeserializeObject<ErrorDetail>(fex.Message);

                    if (await _appDbContext.UserProfiles
                            .AnyAsync(x => x.Id == errorDetail.UserProfileId && x.CompanyId == company.Id))
                    {
                        return BadRequest(
                            new ResponseViewModel()
                            {
                                ErrorCode = "DuplicateContact",
                                message = $"Duplicate contact found. ID: {errorDetail.UserProfileId}. Reason: {errorDetail.FieldName}"
                            });
                    }
                }
                catch (Exception)
                {
                    var splitMessage = fex.Message.Split(':');

                    if (splitMessage.Length != 2)
                    {
                        return BadRequest(
                            new ResponseViewModel()
                            {
                                message = fex.Message
                            });
                    }

                    var userProfileId = splitMessage[0];
                    var duplicateReason = splitMessage[1];

                    if (await _appDbContext.UserProfiles
                            .AnyAsync(x => x.Id == userProfileId && x.CompanyId == company.Id))
                    {
                        return BadRequest(
                            new ResponseViewModel()
                            {
                                ErrorCode = "DuplicateContact",
                                message = $"Duplicate contact found. ID: {userProfileId}. Reason: {duplicateReason}"
                            });
                    }
                }

                return BadRequest(
                    new ResponseViewModel()
                    {
                        message = fex.Message
                    });
            }
            catch (ContactWhatsappSenderLockException contactWhatsappSenderLockException)
            {
                _logger.LogError(
                    contactWhatsappSenderLockException,
                    "[Zapier {MethodName} endpoint] Company {CompanyId} Zapier Exception. {ExceptionMessage}",
                    nameof(ZapierAddContact),
                    company.Id,
                    contactWhatsappSenderLockException.Message);

                return BadRequest(
                    new ResponseViewModel
                    {
                        message = "Rejected"
                    });
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[Zapier {MethodName} endpoint] Company {CompanyId} general exception. {ExceptionMessage}",
                    nameof(ZapierAddContact),
                    company.Id,
                    ex.Message);

                return await ZapierUpdateContact(apikey, zapierViewModel);
            }
        }

        [HttpPost]
        [Route("api/zapier/contact/update")]
        public async Task<IActionResult> ZapierUpdateContact(
            [FromQuery(Name = "apikey")]
            string apikey,
            [FromBody]
            ZapierViewModel zapierViewModel)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        message = $"Incorrect format: {ModelState}"
                    });
            }

            var apiKey = await _zapierApiKeyResolver.AuthenticateAsync(apikey);

            var key = $"{apikey}_{zapierViewModel.Email}_{zapierViewModel.WhatsAppPhoneNumber}";
            while (true)
            {
                var mylock = await _lockService.AcquireLockAsync(key, TimeSpan.FromSeconds(2));
                if (mylock == null)
                {
                    await Task.Delay(TimeSpan.FromSeconds(5));
                }
                else
                {
                    break;
                }
            }

            var company = await (from _company in _appDbContext.CompanyCompanies
                where _company.Id == apiKey.CompanyId
                select _company).FirstOrDefaultAsync();

            if (company == null)
            {
                return Unauthorized();
            }

            try
            {
                var newProfileViewModel = _mapper.Map<NewProfileViewModel>(zapierViewModel);
                newProfileViewModel.UserProfileFields = new List<AddCustomFieldsViewModel>();
                try
                {
                    if (zapierViewModel.CustomFieldNames != null)
                    {
                        foreach (var fieldName in zapierViewModel.CustomFieldNames)
                        {
                            var customFieldName = fieldName;
                            var value = string.Empty;

                            if (fieldName.Contains(":"))
                            {
                                var fieldValue = fieldName.Split(":");
                                customFieldName = fieldValue[0];
                                value = fieldName.Substring(fieldName.IndexOf(":") + 1).Trim();
                            }
                            else
                            {
                                value = zapierViewModel.CustomFieldValues[
                                    zapierViewModel.CustomFieldNames.IndexOf(fieldName)];
                            }

                            newProfileViewModel.UserProfileFields.Add(
                                new AddCustomFieldsViewModel
                                {
                                    CustomFieldName = customFieldName, CustomValue = value
                                });
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(
                        ex,
                        "[Zapier {MethodName} endpoint] Company {CompanyId} error parsing custom field names. {ExceptionMessage}. CustomFieldNames: {Payload}",
                        nameof(ZapierUpdateContact),
                        company.Id,
                        ex.Message,
                        JsonConvert.SerializeObject(zapierViewModel.CustomFieldNames));
                }

                newProfileViewModel.WhatsAppPhoneNumber =
                    PhoneNumberHelper.FormatPhoneNumber(newProfileViewModel.WhatsAppPhoneNumber);

                List<UserProfile> targetUserProfiles = null;

                if (!string.IsNullOrEmpty(newProfileViewModel.WhatsAppPhoneNumber))
                {
                    targetUserProfiles = (await _userProfileService.GetUserProfilesByFields(
                        company.Id,
                        new List<Condition>
                        {
                            new Condition
                            {
                                FieldName = "phonenumber",
                                ConditionOperator = SupportedOperator.Equals,
                                Values = new List<string>
                                {
                                    newProfileViewModel.WhatsAppPhoneNumber.Replace(" ", string.Empty)
                                        .Replace("-", string.Empty)
                                        .Replace("(", string.Empty).Replace(")", string.Empty)
                                        .Replace("+", string.Empty)
                                },
                                NextOperator = SupportedNextOperator.And
                            }
                        })).UserProfiles;
                }

                if (!string.IsNullOrEmpty(newProfileViewModel.Email) &&
                    (targetUserProfiles == null || targetUserProfiles?.Count == 0))
                {
                    targetUserProfiles = (await _userProfileService.GetUserProfilesByFields(
                        company.Id,
                        new List<Condition>
                        {
                            new Condition
                            {
                                FieldName = "email",
                                ConditionOperator = SupportedOperator.Equals,
                                Values = new List<string>
                                {
                                    newProfileViewModel.Email
                                },
                                NextOperator = SupportedNextOperator.And
                            }
                        })).UserProfiles;
                }

                if ((targetUserProfiles == null || targetUserProfiles?.Count == 0) && newProfileViewModel
                        .UserProfileFields.Where(x => x.CustomFieldName.ToLower() == "asset id").Count() > 0)
                {
                    var assetId = newProfileViewModel.UserProfileFields
                        .Where(x => x.CustomFieldName.ToLower() == "asset id").FirstOrDefault();
                    targetUserProfiles = (await _userProfileService.GetUserProfilesByFields(
                        company.Id,
                        new List<Condition>
                        {
                            new Condition
                            {
                                FieldName = "Asset ID",
                                ConditionOperator = SupportedOperator.Equals,
                                Values = new List<string>
                                {
                                    assetId.CustomValue
                                },
                                NextOperator = SupportedNextOperator.Or
                            }
                        })).UserProfiles;
                }

                if ((targetUserProfiles == null || targetUserProfiles?.Count == 0) && newProfileViewModel
                        .UserProfileFields.Where(x => x.CustomFieldName.ToLower() == "wo#").Count() > 0)
                {
                    var wo = newProfileViewModel.UserProfileFields.Where(x => x.CustomFieldName.ToLower() == "wo#")
                        .FirstOrDefault();
                    targetUserProfiles = (await _userProfileService.GetUserProfilesByFields(
                        company.Id,
                        new List<Condition>
                        {
                            new Condition
                            {
                                FieldName = "wo#",
                                ConditionOperator = SupportedOperator.Equals,
                                Values = new List<string>
                                {
                                    wo.CustomValue
                                },
                                NextOperator = SupportedNextOperator.Or
                            }
                        })).UserProfiles;
                }

                if (targetUserProfiles == null || targetUserProfiles?.Count == 0)
                {
                    return BadRequest(
                        new ResponseViewModel
                        {
                            message = "not found"
                        });
                }

                if (targetUserProfiles?.Count() > 0)
                {
                    UserProfileNoCompanyResponse updateUserProfileResponse = new UserProfileNoCompanyResponse();
                    foreach (var targetUserProfile in targetUserProfiles)
                    {
                        if (!string.IsNullOrEmpty(newProfileViewModel.FirstName))
                        {
                            targetUserProfile.FirstName = newProfileViewModel.FirstName;
                        }

                        if (!string.IsNullOrEmpty(newProfileViewModel.LastName))
                        {
                            targetUserProfile.LastName = newProfileViewModel.LastName;
                        }

                        var updatedUserProfile = await _userProfileService.UpdateUserProfileCustomFields(
                            company.Id,
                            targetUserProfile.Id,
                            null,
                            newProfileViewModel.UserProfileFields);
                        await LogActivityLog(targetUserProfile, zapierViewModel);
                        updateUserProfileResponse = _mapper.Map<UserProfileNoCompanyResponse>(updatedUserProfile);
                    }

                    return Ok(updateUserProfileResponse);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[Zapier {MethodName} endpoint] Company {CompanyId} general exception. {ExceptionMessage}",
                    nameof(ZapierUpdateContact),
                    company.Id,
                    ex.Message);

                return BadRequest(
                    new ResponseViewModel
                    {
                        message = ex.Message
                    });
            }

            return BadRequest(
                new ResponseViewModel
                {
                    message = "Incorrect format"
                });
        }

        [HttpGet]
        [Route("api/zapier/contact")]
        public async Task<IActionResult> GetZapierUpdatedContact([FromQuery(Name = "apikey")] string apikey)
        {
            var apiKey = await _zapierApiKeyResolver.AuthenticateAsync(apikey);

            var company = await (from _company in _appDbContext.CompanyCompanies
                where _company.Id == apiKey.CompanyId
                select _company).Include(x => x.CustomUserProfileFields).FirstOrDefaultAsync();

            if (company == null)
            {
                return Unauthorized();
            }

            try
            {
                if (ModelState.IsValid)
                {
                    // _logger.LogError($"pi/zapier/contac: {Request.QueryString}");

                    var lastCall = await _appDbContext.CoreZapierPollingRecord
                        .FirstOrDefaultAsync(
                            x =>
                                x.CompanyId == company.Id
                                && x.ZaiperTrigger == "ContactUpdated");

                    if (lastCall == null)
                    {
                        lastCall = new ZapierPollingRecord
                        {
                            CompanyId = company.Id,
                            ZaiperTrigger = "ContactUpdated",
                            LastTriggeredDateTime = DateTime.UtcNow.AddMinutes(-5)
                        };
                        _appDbContext.CoreZapierPollingRecord.Add(lastCall);
                        await _appDbContext.SaveChangesAsync();

                        var userProile = await _appDbContext.UserProfiles
                            .Where(x => x.CompanyId == company.Id && x.ActiveStatus == ActiveStatus.Active)
                            .Include(x => x.CustomFields).OrderBy(x => x.CreatedAt).Take(1).ToListAsync(HttpContext.RequestAborted);
                        return Ok(await ToZapierResponse(company, userProile));
                    }

                    var updatedTime = await _userProfileService.GetUserProfilesByFields(
                        company.Id,
                        new List<Condition>
                        {
                            new Condition
                            {
                                FieldName = "updatedat",
                                ConditionOperator = SupportedOperator.HigherThan,
                                Values = new List<string>
                                {
                                    lastCall.LastTriggeredDateTime.ToString()
                                }
                            }
                        });
                    if (DateTime.UtcNow > lastCall.LastTriggeredDateTime.AddMinutes(10))
                    {
                        lastCall.LastTriggeredDateTime = DateTime.UtcNow;
                        await _appDbContext.SaveChangesAsync();
                    }

                    var response = await ToZapierResponse(company, updatedTime.UserProfiles);

                    if (response.Count == 0 && !lastCall.IsWorked)
                    {
                        var userProile = await _appDbContext.UserProfiles
                            .Where(x => x.CompanyId == company.Id && x.ActiveStatus == ActiveStatus.Active)
                            .Include(x => x.CustomFields).OrderBy(x => x.CreatedAt).Take(1).ToListAsync(HttpContext.RequestAborted);
                        return Ok(await ToZapierResponse(company, userProile));
                    }
                    else if (!lastCall.IsWorked)
                    {
                        lastCall.IsWorked = true;
                        await _appDbContext.SaveChangesAsync();
                    }

                    return Ok(response);
                }

                return BadRequest(
                    new ResponseViewModel
                    {
                        message = "Incorrect format"
                    });
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[Zapier {MethodName} endpoint] Company {CompanyId} {CompanyName} General Exception: {ExceptionString}",
                    nameof(GetZapierUpdatedContact),
                    company.Id,
                    company.CompanyName,
                    ex.ToString());

                return BadRequest(
                    new ResponseViewModel
                    {
                        message = "error"
                    });
            }
        }

        [HttpPost]
        [Route("api/zapier/{projectId}/hock")]
        public async Task<IActionResult> SubscriptionWebhook(
            [FromQuery(Name = "apikey")]
            string apikey,
            string projectId,
            [FromBody]
            RestHookURL restHockRequestBody)
        {
            var apiKey = await _zapierApiKeyResolver.AuthenticateAsync(apikey);

            try
            {
                var company = await (from _company in _appDbContext.CompanyCompanies
                    where _company.Id == apiKey.CompanyId
                    select _company).FirstOrDefaultAsync();

                if (company == null)
                {
                    return Unauthorized();
                }

                var webhookAutomation = new AssignmentRule
                {
                    CompanyId = company.Id,
                    AssignmentRuleName = "Zapier - Trigger",
                    AutomationType = AutomationType.ZapierContactUpdated,
                    Status = AutomationStatus.Live,
                    AutomationActions = new List<AutomationAction>
                    {
                        new AutomationAction
                        {
                            AutomatedTriggerType = AutomatedTriggerType.SendWebhook,
                            WebhookURL = restHockRequestBody.HockUrl
                        }
                    },
                    ZaiperSubscriptionId = projectId,
                    Order = 0,
                    IsContinue = true
                };
                _appDbContext.CompanyAssignmentRules.Add(webhookAutomation);
                await _appDbContext.SaveChangesAsync();
                // await _lockService.RemoveCompanyInfoCache(company.Id, "AssignmentRuleInfo");

                return Ok(
                    new ResthookResponse
                    {
                        SubscriptionData = new ZaiperSubscriptionData
                        {
                            Id = webhookAutomation.ZaiperSubscriptionId
                        }
                    });
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[Zapier {MethodName} endpoint] General exception, project id {ProjectId}, hook url {HookUrl}. {ExceptionMessage}",
                    nameof(SubscriptionWebhook),
                    projectId,
                    restHockRequestBody.HockUrl,
                    ex.Message);

                return BadRequest(ex);
            }
        }

        [HttpDelete]
        [Route("api/zapier/{projectId}/hock")]
        public async Task<IActionResult> UnubscriptionWebhook(
            [FromQuery(Name = "apikey")]
            string apikey,
            string projectId)
        {
            var apiKey = await _zapierApiKeyResolver.AuthenticateAsync(apikey);

            var company = await (from _company in _appDbContext.CompanyCompanies
                where _company.Id == apiKey.CompanyId
                select _company).FirstOrDefaultAsync();

            if (company == null)
            {
                return Unauthorized();
            }

            await _appDbContext.CompanyAssignmentRules
                .Where(x => x.CompanyId == apiKey.CompanyId && x.ZaiperSubscriptionId == projectId)
                .ExecuteDeleteAsync();

            return Ok();
        }

        [HttpPost]
        [Route("api/zapier/{projectId}/hock/{actionName}")]
        public async Task<IActionResult> SubscriptionMessageWebhook(
            [FromQuery(Name = "apikey")]
            string apikey,
            string projectId,
            string actionName,
            [FromBody]
            RestHookURL restHockRequestBody)
        {
            var apiKey = await _zapierApiKeyResolver.AuthenticateAsync(apikey);

            try
            {
                var company = await (from _company in _appDbContext.CompanyCompanies
                    where _company.Id == apiKey.CompanyId
                    select _company).FirstOrDefaultAsync();

                if (company == null)
                {
                    return Unauthorized();
                }

                var webhookAutomation = new AssignmentRule
                {
                    CompanyId = company.Id,
                    AssignmentRuleName = "Zapier - Action",
                    AutomationType = AutomationType.ZapierNewIncomingMessage,
                    Status = AutomationStatus.Live,
                    AutomationActions = new List<AutomationAction>
                    {
                        new AutomationAction
                        {
                            AutomatedTriggerType = AutomatedTriggerType.SendWebhook,
                            WebhookURL = restHockRequestBody.HockUrl
                        }
                    },
                    ZaiperSubscriptionId = projectId,
                    Order = 0,
                    IsContinue = true
                };

                webhookAutomation.AutomationType = actionName.ToLower() switch
                {
                    "message" => AutomationType.ZapierNewIncomingMessage,
                    "contact" => AutomationType.ZapierContactUpdated,
                    _ => webhookAutomation.AutomationType
                };

                _appDbContext.CompanyAssignmentRules.Add(webhookAutomation);
                await _appDbContext.SaveChangesAsync();
                // await _lockService.RemoveCompanyInfoCache(company.Id, "AssignmentRuleInfo");

                return Ok(
                    new ResthookResponse
                    {
                        SubscriptionData = new ZaiperSubscriptionData
                        {
                            Id = webhookAutomation.ZaiperSubscriptionId
                        }
                    });
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[Zapier {MethodName} endpoint] General exception, project id {ProjectId}, action name {ActionName}, hook url {HookUrl}. {ExceptionMessage}",
                    nameof(SubscriptionMessageWebhook),
                    projectId,
                    actionName,
                    restHockRequestBody.HockUrl,
                    ex.Message);

                return BadRequest(ex);
            }
        }

        [HttpGet]
        [Route("api/zapier/message")]
        public async Task<IActionResult> ZapierGetIncomingMessage([FromQuery(Name = "apikey")] string apikey)
        {
            var apiKey = await _zapierApiKeyResolver.AuthenticateAsync(apikey);

            var company = await (from _company in _appDbContext.CompanyCompanies
                where _company.Id == apiKey.CompanyId
                select _company).Include(x => x.CustomUserProfileFields).FirstOrDefaultAsync();

            if (company == null)
            {
                return Unauthorized();
            }

            var conversationMessage = await _appDbContext.ConversationMessages
                .Where(x => x.CompanyId == company.Id && !x.IsSentFromSleekflow && x.UploadedFiles.Any())
                .Include(x => x.UploadedFiles)
                .Include(x => x.EmailFrom)
                .Include(x => x.Sender)
                .Include(x => x.Receiver)
                .Include(x => x.SenderDevice)
                .Include(x => x.ReceiverDevice)
                .Include(x => x.facebookSender)
                .Include(x => x.facebookReceiver)
                .Include(x => x.whatsappSender)
                .Include(x => x.whatsappReceiver)
                .Include(x => x.WebClientSender)
                .Include(x => x.WebClientReceiver)
                .Include(x => x.MessageAssignee.Identity)
                .Include(x => x.WeChatSender)
                .Include(x => x.WeChatReceiver)
                .Include(x => x.LineSender)
                .Include(x => x.LineReceiver)
                .Include(x => x.SMSSender)
                .Include(x => x.SMSReceiver)
                .Include(x => x.InstagramReceiver)
                .Include(x => x.InstagramSender)
                .Include(x => x.ViberSender)
                .Include(x => x.ViberReceiver)
                .Include(x => x.TelegramSender)
                .Include(x => x.TelegramReceiver)
                .Include(x => x.Whatsapp360DialogSender)
                .Include(x => x.Whatsapp360DialogReceiver)
                .Include(x => x.Whatsapp360DialogExtendedMessagePayload)
                .Include(x => x.ExtendedMessagePayload)
                .OrderByDescending(x => x.Timestamp)
                .Take(1)
                .ToListAsync(HttpContext.RequestAborted);
            var responseVM = _mapper.Map<List<ConversationMessageWebhookResponse>>(conversationMessage);

            foreach (var message in responseVM)
            {
                try
                {
                    message.ContactEmail = await _appDbContext.Conversations.Where(x => x.Id == message.ConversationId)
                        .Select(x => x.UserProfile.Email).FirstOrDefaultAsync() ?? string.Empty;
                }
                catch (Exception ex)
                {
                    _logger.LogError(
                        ex,
                        "[Zapier {MethodName} endpoint] Company {CompanyId} Unable set contact email from conversation {ConversationId} profile. {ExceptionMessage}",
                        nameof(ZapierGetIncomingMessage),
                        company.Id,
                        message.ConversationId,
                        ex.Message);
                }

                message.MessageContent ??= "Message Body";
                message.UploadedFiles?.ForEach(
                    y => { y.Url = _azureBlobStorageService.GetAzureBlobSasUri(y.Filename, y.BlobContainer, 24 * 7); });
            }

            return Ok(responseVM);
        }

        public class ResthookResponse
        {
            public ZaiperSubscriptionData SubscriptionData { get; set; }
        }

        public class ZaiperSubscriptionData
        {
            public string Id { get; set; }
        }

        public class RestHookURL
        {
            [JsonProperty("hookUrl")]
            public string HockUrl { get; set; }
        }

        private async Task<bool> IsLockAcquired(string companyId, string to)
        {
            if (!string.IsNullOrEmpty(to))
            {
                return false;
            }

            var cache = await _cacheManagerService.GetCacheWithConstantKeyAsync(
                GetNewContactLockKey(
                    companyId,
                    PhoneNumberHelper.NormalizePhoneNumber(to)));

            return !string.IsNullOrEmpty(cache);
        }

        private string GetNewContactLockKey(string companyId, string phoneNumber)
        {
            return $"publicapi_add_contact_{companyId}_{phoneNumber}";
        }

        // Enhancement for below code block -> Extract this region to a new class

        #region 360Dialog

        private async Task<(Conversation Conversation, MessageDomain.Models.ConversationMessage ConversationMessage)>
            PrepareWhatsApp360DialogMessageForSending(
                string from,
                string to,
                string extendedMessageJson,
                Conversation conversation,
                MessageDomain.Models.ConversationMessage conversationMessage)
        {
            var companyId = conversation.CompanyId;

            var config = await FindWhatsApp360DialogConfig(companyId, from);

            if (config is null)
            {
                throw new NullReferenceException($"360Dialog Channel with Number {from} not found.");
            }

            var isLockAcquired = await IsLockAcquired(companyId, to);

            if (isLockAcquired)
            {
                await Task.Delay(5000);
            }

            // Logic for getting current WhatsApp 360 dialog Conversation is in Travis_backend\ConversationServices\ConversationMessageService.cs:7157
            // No need to search the current conversation here

            // Travis_backend\ConversationServices\ConversationMessageService.cs:6005
            // For sending message from SleekFlow conversationMessage Whatsapp360DialogSender null is fine
            var whatsApp360DialogSender = CreateNewWhatsApp360DialogSenderForConversation(companyId, config, to);
            conversation.WhatsApp360DialogUser = whatsApp360DialogSender;
            conversationMessage = await PrepareWhatsApp360DialogConversationMessageForSending(
                conversation,
                conversationMessage,
                extendedMessageJson);

            return (conversation, conversationMessage);
        }

        private async Task<MessageDomain.Models.ConversationMessage> PrepareWhatsApp360DialogConversationMessageForSending(
            Conversation conversation,
            MessageDomain.Models.ConversationMessage conversationMessage,
            string extendedMessageJson)
        {
            if (conversation.WhatsApp360DialogUser is null)
            {
                throw new NullReferenceException("conversation WhatsApp360DialogUser is not found");
            }

            conversationMessage.Whatsapp360DialogReceiver = conversation.WhatsApp360DialogUser;

            var messageType = conversationMessage.MessageType;

            conversationMessage.Whatsapp360DialogExtendedMessagePayload =
                _mapper.Map<Whatsapp360DialogExtendedMessagePayload>(
                    JsonConvert.DeserializeObject<Whatsapp360DialogExtendedMessagePayloadRequestViewModel>(
                        extendedMessageJson));

            switch (messageType)
            {
                case "template":
                    conversationMessage =
                        await PrepareWhatsApp360DialogTemplateMessageForSending(conversation, conversationMessage);

                    break;
                case "interactive":
                    if (conversationMessage.Whatsapp360DialogExtendedMessagePayload
                            ?.Whatsapp360DialogInteractiveObject is null)
                    {
                        throw new NullReferenceException("Interactive message format is invalid.");
                    }

                    break;
            }

            return conversationMessage;
        }

        private async Task<MessageDomain.Models.ConversationMessage> PrepareWhatsApp360DialogTemplateMessageForSending(
            Conversation conversation,
            MessageDomain.Models.ConversationMessage conversationMessage)
        {
            var companyId = conversationMessage.CompanyId;

            if (IsWhatsApp360DialogTemplateFormatValid(conversationMessage.Whatsapp360DialogExtendedMessagePayload))
            {
                if (conversation.WhatsApp360DialogUser.ChannelId is null)
                {
                    throw new NullReferenceException("Conversation WhatsApp360DialogUser ChannelId is null");
                }

                var template = await FindWhatsApp360DialogBusinessApiTemplateViewModel(
                    companyId,
                    conversation.WhatsApp360DialogUser.ChannelId.Value,
                    conversationMessage.Whatsapp360DialogExtendedMessagePayload);

                if (template is null)
                {
                    throw new NullReferenceException(
                        $"Template '{conversationMessage.Whatsapp360DialogExtendedMessagePayload.Whatsapp360DialogTemplateMessage.TemplateName}' not found.");
                }

                conversationMessage.MessageContent = template
                    .Components
                    .First(x => x.Type == TemplateComponentType.BODY)
                    .Text;

                conversationMessage.MessageContent = conversationMessage.MessageContent
                    .FormatParamToBodyText(
                        conversationMessage.Whatsapp360DialogExtendedMessagePayload.Whatsapp360DialogTemplateMessage
                            .Components);
            }
            else
            {
                throw new Exception("WhatsApp 360Dialog Template message format is invalid.");
            }

            return conversationMessage;
        }

        private static bool IsWhatsApp360DialogTemplateFormatValid(
            Whatsapp360DialogExtendedMessagePayload whatsapp360DialogExtendedMessagePayload)
        {
            if (whatsapp360DialogExtendedMessagePayload.Whatsapp360DialogTemplateMessage is null)
            {
                return false;
            }

            if (string.IsNullOrWhiteSpace(
                    whatsapp360DialogExtendedMessagePayload.Whatsapp360DialogTemplateMessage.TemplateNamespace))
            {
                return false;
            }

            if (string.IsNullOrWhiteSpace(
                    whatsapp360DialogExtendedMessagePayload.Whatsapp360DialogTemplateMessage.TemplateName))
            {
                return false;
            }

            return true;
        }

        private async Task<WhatsAppBusinessApiTemplateViewModel> FindWhatsApp360DialogBusinessApiTemplateViewModel(
            string companyId,
            long whatsapp360DialogConfigId,
            Whatsapp360DialogExtendedMessagePayload whatsapp360DialogExtendedMessagePayload)
        {
            var template = await FindWhatsApp360DialogBusinessApiTemplateViewModelFromCache(
                companyId,
                whatsapp360DialogConfigId,
                whatsapp360DialogExtendedMessagePayload);

            if (template is not null)
            {
                return template;
            }

            return await FindWhatsApp360DialogBusinessApiTemplateFrom360DialogApiClient(
                companyId,
                whatsapp360DialogConfigId,
                whatsapp360DialogExtendedMessagePayload);
        }

        private async Task<WhatsAppBusinessApiTemplateViewModel>
            FindWhatsApp360DialogBusinessApiTemplateFrom360DialogApiClient(
                string companyId,
                long whatsapp360DialogConfigId,
                Whatsapp360DialogExtendedMessagePayload whatsapp360DialogExtendedMessagePayload)
        {

            var whatsApp360DialogGetTemplateCacheKeyPattern = new WhatsApp360DialogGetTemplateCacheKeyPattern(companyId,
                whatsapp360DialogConfigId);

            var result = await _whatsApp360DialogService.GetWhatsApp360DialogTemplate(
                companyId,
                whatsapp360DialogConfigId,
                1000,
                0);

            await _cacheManagerService.SaveCacheAsync(
                whatsApp360DialogGetTemplateCacheKeyPattern,
                result,
                null,
                new JsonSerializerSettings
                {
                    DateTimeZoneHandling = DateTimeZoneHandling.Utc,
                    NullValueHandling = NullValueHandling.Ignore,
                    ContractResolver = new CamelCasePropertyNamesContractResolver()
                });

            return result.WhatsAppTemplates.FirstOrDefault(
                x =>
                    x.Name == whatsapp360DialogExtendedMessagePayload.Whatsapp360DialogTemplateMessage.TemplateName &&
                    x.Language == whatsapp360DialogExtendedMessagePayload.Whatsapp360DialogTemplateMessage.Language &&
                    x.Namespace == whatsapp360DialogExtendedMessagePayload.Whatsapp360DialogTemplateMessage
                        .TemplateNamespace);
        }

        private async Task<WhatsAppBusinessApiTemplateViewModel>
            FindWhatsApp360DialogBusinessApiTemplateViewModelFromCache(
                string companyId,
                long whatsapp360DialogConfigId,
                Whatsapp360DialogExtendedMessagePayload whatsapp360DialogExtendedMessagePayload)
        {
            var whatsApp360DialogGetTemplateCacheKeyPattern = new WhatsApp360DialogGetTemplateCacheKeyPattern( companyId,
                whatsapp360DialogConfigId);

            var cache = await _cacheManagerService.GetCacheAsync(whatsApp360DialogGetTemplateCacheKeyPattern);

            if (string.IsNullOrEmpty(cache))
            {
                return null;
            }

            var result = JsonConvert.DeserializeObject<GetWhatsApp360DialogTemplateResponse>(cache);

            return result.WhatsAppTemplates.FirstOrDefault(
                x =>
                    x.Name == whatsapp360DialogExtendedMessagePayload.Whatsapp360DialogTemplateMessage.TemplateName &&
                    x.Language == whatsapp360DialogExtendedMessagePayload.Whatsapp360DialogTemplateMessage.Language &&
                    x.Namespace == whatsapp360DialogExtendedMessagePayload.Whatsapp360DialogTemplateMessage
                        .TemplateNamespace);
        }

        private async Task<WhatsApp360DialogConfig> FindWhatsApp360DialogConfig(
            string companyId,
            string from)
        {
            return await _appDbContext
                .ConfigWhatsApp360DialogConfigs
                .AsNoTracking()
                .FirstOrDefaultAsync(x => x.CompanyId == companyId && x.WhatsAppPhoneNumber == from);
        }

        private static WhatsApp360DialogSender CreateNewWhatsApp360DialogSenderForConversation(
            string companyId,
            WhatsApp360DialogConfig config,
            string to)
        {
            var whatsApp360DialogSender = new WhatsApp360DialogSender()
            {
                WhatsAppId = PhoneNumberHelper.NormalizePhoneNumber(to),
                PhoneNumber = "+" + PhoneNumberHelper.NormalizePhoneNumber(to),
                CompanyId = companyId,
                ChannelId = config.Id,
                ChannelWhatsAppPhoneNumber = config.WhatsAppPhoneNumber,
            };

            return whatsApp360DialogSender;
        }

        #endregion

        // Enhancement for below code block -> Extract this region to a new class

        #region WhatsAppCloudApi

        private async Task<(Conversation Conversation, MessageDomain.Models.ConversationMessage ConversationMessage)>
            PrepareWhatsAppCloudApiMessageForSending(
                string from,
                string to,
                string extendedMessageJson,
                Conversation conversation,
                ConversationMessage conversationMessage)
        {
            var companyId = conversation.CompanyId;

            var whatsappCloudApiConfigExists = await IsWhatsappCloudApiConfigExists(companyId, from);

            if (!whatsappCloudApiConfigExists)
            {
                throw new NullReferenceException($"WhatsApp Cloud Api Channel with Number {from} not found.");
            }

            var isLockAcquired = await IsLockAcquired(companyId, to);

            if (isLockAcquired)
            {
                await Task.Delay(5000);
            }

            // Logic for getting current WhatsAppCloudApi Conversation is in
            // Travis_backend\ConversationServices\ConversationMessageService.cs:7175
            // No need to search the current conversation here

            var cloudApiSender = await FindWhatsAppCloudApiSender(companyId, to) ??
                                 CreateNewWhatsappCloudApiSenderForConversation(companyId, from, to);

            if (cloudApiSender.WhatsappChannelPhoneNumber != from)
            {
                cloudApiSender.WhatsappChannelPhoneNumber = from;
            }

            conversation.WhatsappCloudApiUser = cloudApiSender;

            conversationMessage = await PrepareWhatsAppCloudApiConversationMessageForSending(
                conversation,
                conversationMessage,
                extendedMessageJson);

            return (conversation, conversationMessage);
        }

        private async Task<ConversationMessage> PrepareWhatsAppCloudApiConversationMessageForSending(
            Conversation conversation,
            ConversationMessage conversationMessage,
            string extendedMessageJson)
        {
            if (conversation.WhatsappCloudApiUser is null)
            {
                throw new NullReferenceException("conversation WhatsappCloudApiUser is not found");
            }

            conversationMessage.WhatsappCloudApiSender = conversation.WhatsappCloudApiUser;
            conversationMessage.ChannelIdentityId = conversation.WhatsappCloudApiUser.WhatsappChannelPhoneNumber;

            var extendedMessagePayloadDetail =
                JsonConvert.DeserializeObject<ExtendedMessagePayloadDetail>(extendedMessageJson);

            if (extendedMessagePayloadDetail is not null)
            {
                conversationMessage.ExtendedMessagePayload = new ExtendedMessagePayload
                {
                    Channel = ChannelTypes.WhatsappCloudApi
                };

                conversationMessage.ExtendedMessagePayload.SetExtendedMessagePayloadDetailWithType(
                    extendedMessagePayloadDetail);
            }

            var messageType = conversationMessage.MessageType;

            switch (messageType)
            {
                case "template":
                    conversationMessage =
                        await PrepareWhatsAppCloudApiTemplateMessageForSending(conversation, conversationMessage);

                    break;
                case "interactive":
                    if (conversationMessage.ExtendedMessagePayload.ExtendedMessagePayloadDetail
                            .WhatsappCloudApiInteractiveObject is null)
                    {
                        throw new NullReferenceException("WhatsApp Cloud Api Interactive message format is invalid.");
                    }

                    break;
                case "contacts":
                    if (conversationMessage.ExtendedMessagePayload.ExtendedMessagePayloadDetail
                            .WhatsappCloudApiContactsObject is null)
                    {
                        throw new NullReferenceException("WhatsApp Cloud Api Contacts message format is invalid.");
                    }

                    break;
                case "location":
                    if (conversationMessage.ExtendedMessagePayload.ExtendedMessagePayloadDetail
                            .WhatsappCloudApiLocationObject is null)
                    {
                        throw new NullReferenceException("WhatsApp Cloud Api Location message format is invalid.");
                    }

                    break;
                case "reaction":
                    if (conversationMessage.ExtendedMessagePayload.ExtendedMessagePayloadDetail
                            .WhatsappCloudApiReactionObject is null)
                    {
                        throw new NullReferenceException("WhatsApp Cloud Api Reaction message format is invalid.");
                    }

                    break;
            }

            return conversationMessage;
        }

        private async Task<ConversationMessage> PrepareWhatsAppCloudApiTemplateMessageForSending(
            Conversation conversation,
            ConversationMessage conversationMessage)
        {
            var companyId = conversation.CompanyId;

            if (IsWhatsAppCloudApiTemplateFormatValid(
                    conversationMessage.ExtendedMessagePayload.ExtendedMessagePayloadDetail))
            {
                var messagingHubWabaId = await FindMessagingHubWabaId(
                    companyId,
                    conversation.WhatsappCloudApiUser.WhatsappChannelPhoneNumber);
                var template = await FindWhatsAppBusinessApiTemplate(
                    companyId,
                    messagingHubWabaId,
                    conversationMessage.ExtendedMessagePayload.ExtendedMessagePayloadDetail);

                if (template is null)
                {
                    throw new NullReferenceException(
                        $"Template '{conversationMessage.ExtendedMessagePayload.ExtendedMessagePayloadDetail.WhatsappCloudApiTemplateMessageObject.TemplateName}' not found.");
                }

                conversationMessage.MessageContent = template.Components.First(x => x.Type == "BODY").Text;
                conversationMessage.MessageContent =
                    conversationMessage.MessageContent.FormatWhatsappCloudApiTemplateParamToBodyText(
                        conversationMessage.ExtendedMessagePayload.ExtendedMessagePayloadDetail
                            .WhatsappCloudApiTemplateMessageObject.Components);
            }
            else
            {
                throw new NullReferenceException($"WhatsApp Cloud Api template message format is invalid.");
            }

            return conversationMessage;
        }

        private static bool IsWhatsAppCloudApiTemplateFormatValid(
            ExtendedMessagePayloadDetail extendedMessagePayloadDetail)
        {
            if (extendedMessagePayloadDetail is null)
            {
                return false;
            }

            if (extendedMessagePayloadDetail.WhatsappCloudApiTemplateMessageObject is null)
            {
                return false;
            }

            if (string.IsNullOrWhiteSpace(
                    extendedMessagePayloadDetail.WhatsappCloudApiTemplateMessageObject.TemplateName))
            {
                return false;
            }

            if (string.IsNullOrWhiteSpace(extendedMessagePayloadDetail.WhatsappCloudApiTemplateMessageObject.Language))
            {
                return false;
            }

            return true;
        }

        private async Task<WhatsappCloudApiTemplate> FindWhatsAppBusinessApiTemplate(
            string companyId,
            string messagingHubWabaId,
            ExtendedMessagePayloadDetail extendedMessagePayloadDetail)
        {
            var template = await FindWhatsAppBusinessApiTemplateFromCache(
                companyId,
                messagingHubWabaId,
                extendedMessagePayloadDetail);

            if (template is not null)
            {
                return template;
            }

            return await FindWhatsAppBusinessApiTemplateFromMessagingHub(
                companyId,
                messagingHubWabaId,
                extendedMessagePayloadDetail);
        }

        private async Task<WhatsappCloudApiTemplate> FindWhatsAppBusinessApiTemplateFromCache(
            string companyId,
            string messagingHubWabaId,
            ExtendedMessagePayloadDetail extendedMessagePayloadDetail)
        {
            var cacheKey = WhatsappCloudApiCacheKeyHelper.GetTemplateResponseCacheKey(companyId, messagingHubWabaId);
            var cache = await _cacheManagerService.GetCacheWithConstantKeyAsync(cacheKey);

            if (string.IsNullOrEmpty(cache))
            {
                return null;
            }

            var templateResponses = JsonConvert.DeserializeObject<List<WhatsappCloudApiTemplateResponse>>(cache);

            var templateResponse = templateResponses.FirstOrDefault(
                x => x.Name == extendedMessagePayloadDetail.WhatsappCloudApiTemplateMessageObject.TemplateName &&
                     x.Language == extendedMessagePayloadDetail.WhatsappCloudApiTemplateMessageObject.Language);

            return templateResponse == null
                ? null
                : new WhatsappCloudApiTemplate(
                    templateResponse.Id,
                    templateResponse.Name,
                    templateResponse.Components,
                    templateResponse.Category,
                    templateResponse.Status,
                    templateResponse.Language);
        }

        private async Task<WhatsappCloudApiTemplate> FindWhatsAppBusinessApiTemplateFromMessagingHub(
            string companyId,
            string messagingHubWabaId,
            ExtendedMessagePayloadDetail extendedMessagePayloadDetail)
        {
            var templateResponses = await _whatsappCloudApiService.GetTemplates(companyId, messagingHubWabaId);

            var templateResponse = templateResponses.FirstOrDefault(
                x => x.Name == extendedMessagePayloadDetail.WhatsappCloudApiTemplateMessageObject.TemplateName &&
                     x.Language == extendedMessagePayloadDetail.WhatsappCloudApiTemplateMessageObject.Language);

            return templateResponse == null
                ? null
                : new WhatsappCloudApiTemplate(
                    templateResponse.Id,
                    templateResponse.Name,
                    templateResponse.Components,
                    templateResponse.Category,
                    templateResponse.Status,
                    templateResponse.Language);
        }

        private async Task<string> FindMessagingHubWabaId(
            string companyId,
            string whatsappChannelPhoneNumber)
        {
            return await _appDbContext.ConfigWhatsappCloudApiConfigs
                .AsNoTracking()
                .Where(x => x.CompanyId == companyId && x.WhatsappPhoneNumber == whatsappChannelPhoneNumber)
                .Select(x => x.MessagingHubWabaId).FirstOrDefaultAsync();
        }

        private async Task<WhatsappCloudApiSender> FindWhatsAppCloudApiSender(
            string companyId,
            string to)
        {
            return await _appDbContext.WhatsappCloudApiSenders
                .AsNoTracking()
                .FirstOrDefaultAsync(
                    x =>
                        x.CompanyId == companyId
                        && x.WhatsappId == PhoneNumberHelper.NormalizePhoneNumber(to));
        }

        private static WhatsappCloudApiSender CreateNewWhatsappCloudApiSenderForConversation(
            string companyId,
            string from,
            string to)
        {
            return new WhatsappCloudApiSender
            {
                CompanyId = companyId,
                WhatsappId = PhoneNumberHelper.NormalizePhoneNumber(to),
                WhatsappUserDisplayName = "Anonymous",
                WhatsappChannelPhoneNumber = from,
            };
        }

        private async Task<bool> IsWhatsappCloudApiConfigExists(
            string companyId,
            string from)
        {
            var isConfigExists = await _appDbContext
                .ConfigWhatsappCloudApiConfigs
                .AsNoTracking()
                .AnyAsync(x => x.CompanyId == companyId && x.WhatsappPhoneNumber == from);

            return isConfigExists;
        }

        #endregion

        private async Task LogActivityLog(UserProfile userProfile, ZapierViewModel zapierViewModel)
        {
            if (zapierViewModel.ActivityLogs?.Count > 0)
            {
                foreach (var log in zapierViewModel.ActivityLogs)
                {
                    await _auditHubAuditLogService.CreateStaffManualAddedLogAsync(
                        userProfile.CompanyId,
                        userProfile.Id,
                        null,
                        log,
                        null);
                }
            }
        }

        private async Task<List<JObject>> ToZapierResponse(Company company, List<UserProfile> userProfiles)
        {
            var results = new List<JObject>();
            foreach (var userProfile in userProfiles)
            {
                JObject response = await _userProfileService.NormalizeUserProfileResponse(
                    company.CustomUserProfileFields,
                    userProfile);
                results.Add(response);
            }

            return results;
        }

        #region Custom Object

        [HttpPost]
        [Route("api/zapier/CustomObject/SchemafulObject/Create")]
        public async Task<IActionResult> CreateSchemafulObject(
            [FromQuery(Name = "apikey")]
            string apikey,
            [FromBody]
            SchemafulObjectViewModel schemafulObjectViewModel)
        {
            var apiKey = await _zapierApiKeyResolver.AuthenticateAsync(apikey);

            _logger.LogInformation(
                "Zapier - start processing create schemaful object. {Payload}",
                JsonConvert.SerializeObject(schemafulObjectViewModel));

            try
            {
                var schema = await _customObjectService.GetSchemaByUniqueName(
                    apiKey.CompanyId,
                    schemafulObjectViewModel.ObjectKey);

                if (schema is null)
                {
                    throw new Exception(
                        $"Custom Object not found. Object Key: {schemafulObjectViewModel.ObjectKey}");
                }

                var propertyValues = new Dictionary<string, object>();
                try
                {
                    foreach (var propertyValue in schemafulObjectViewModel.PropertyValues)
                    {
                        var colonIndex = propertyValue.IndexOf(":", StringComparison.Ordinal);

                        var propertyUniqueName = propertyValue[..colonIndex];
                        var rawValue = propertyValue[(colonIndex + 1)..];

                        var property = schema.Properties.First(p => p.UniqueName == propertyUniqueName);

                        propertyValues.Add(property.Id, PropertyValueParser.ParseStringValue(property, rawValue));
                    }
                }
                catch (Exception ex)
                {
                    throw new FormatException($"Errors when processing property values: {ex.Message}");
                }

                var normalizedPhoneNumber = PhoneNumberHelper.FormatPhoneNumber(
                    schemafulObjectViewModel.ReferencedContactPhoneNumber);

                var userProfileId = await _appDbContext.UserProfiles
                    .Where(
                        x => x.CompanyId == apiKey.CompanyId && x.ActiveStatus == ActiveStatus.Active &&
                             x.PhoneNumber == normalizedPhoneNumber)
                    .Select(x => x.Id)
                    .FirstOrDefaultAsync();

                if (string.IsNullOrEmpty(schemafulObjectViewModel.ReferencedContactPhoneNumber) ||
                    string.IsNullOrEmpty(userProfileId))
                {
                    throw new Exception(
                        $"Unable to find contact: [{schemafulObjectViewModel.ReferencedContactPhoneNumber}]");
                }

                var createSchemafulObjectOutputOutput =
                    await _schemafulObjectsApi.SchemafulObjectsCreateSchemafulObjectPostAsync(
                        createSchemafulObjectInput: new CreateSchemafulObjectInput(
                            schema.Id,
                            apiKey.CompanyId,
                            schemafulObjectViewModel.PrimaryPropertyValue,
                            propertyValues,
                            userProfileId,
                            string.Empty,
                            createdVia: CustomObjectAuditSources.ZapierIntegration));
                var schemafulObject = createSchemafulObjectOutputOutput.Data.SchemafulObject;

                var zapierSchemafulObjectResponse = new ZapierSchemafulObjectResponse(
                    schemafulObject.PrimaryPropertyValue,
                    PropertyNamingMapper.FormatPropertyValues(schema, schemafulObject.PropertyValues),
                    normalizedPhoneNumber,
                    userProfileId);

                return Ok(zapierSchemafulObjectResponse);
            }
            catch (FormatException fe)
            {
                _logger.LogError(
                    fe,
                    "[Zapier {MethodName} endpoint] Parse property value exception. {CompanyId} {ExceptionMessage}",
                    nameof(CreateSchemafulObject),
                    apiKey.CompanyId,
                    fe.Message);

                return BadRequest(
                    new ResponseViewModel
                    {
                        message = $"Invalid input detected: {fe.Message}"
                    });
            }
            catch (Exception ex)
            {
                var exceptionMessage = ex.Message;

                if (ex is SleekflowErrorCodeException sece)
                {
                    exceptionMessage += ParseSleekflowExceptionMessage(sece.Output.Data?.ToString());
                }

                _logger.LogError(
                    ex,
                    "[Zapier {MethodName} endpoint] General exception. {CompanyId} {ExceptionMessage}",
                    nameof(CreateSchemafulObject),
                    apiKey.CompanyId,
                    exceptionMessage);

                return BadRequest(
                    new ResponseViewModel
                    {
                        message = $"Execute failed: {exceptionMessage}"
                    });
            }
        }

        [HttpPost]
        [Route("api/zapier/CustomObject/SchemafulObject/Update")]
        public async Task<IActionResult> UpdateSchemafulObject(
            [FromQuery(Name = "apikey")]
            string apikey,
            [FromBody]
            SchemafulObjectViewModel schemafulObjectViewModel)
        {
            var apiKey = await _zapierApiKeyResolver.AuthenticateAsync(apikey);

            _logger.LogInformation(
                "Zapier - start processing update schemaful object. {Payload}",
                JsonConvert.SerializeObject(schemafulObjectViewModel));

            try
            {
                var schema = await _customObjectService.GetSchemaByUniqueName(
                    apiKey.CompanyId,
                    schemafulObjectViewModel.ObjectKey);

                if (schema is null)
                {
                    throw new Exception(
                        $"Custom Object not found. Object Key: {schemafulObjectViewModel.ObjectKey}");
                }

                var schemafulObject = await _customObjectService.GetSchemafulObjectByPrimaryPropertyValue(
                    apiKey.CompanyId,
                    schema.Id,
                    schemafulObjectViewModel.PrimaryPropertyValue);

                if (schemafulObject is null)
                {
                    return BadRequest($"Custom Object Record not found. Primary Property Value: {schemafulObjectViewModel.PrimaryPropertyValue}");
                }

                var propertyValues = new Dictionary<string, object>();
                try
                {
                    foreach (var propertyValue in schemafulObjectViewModel.PropertyValues)
                    {
                        var colonIndex = propertyValue.IndexOf(":", StringComparison.Ordinal);

                        var propertyUniqueName = propertyValue[..colonIndex];
                        var rawValue = propertyValue[(colonIndex + 1)..];

                        var property = schema.Properties.First(p => p.UniqueName == propertyUniqueName);

                        propertyValues.Add(property.Id, PropertyValueParser.ParseStringValue(property, rawValue));
                    }
                }
                catch (Exception ex)
                {
                    throw new FormatException($"Errors when processing property values: {ex.Message}");
                }

                var normalizedPhoneNumber = PhoneNumberHelper.FormatPhoneNumber(
                    schemafulObjectViewModel.ReferencedContactPhoneNumber);

                var userProfileId = await _appDbContext.UserProfiles
                    .Where(
                        x => x.CompanyId == apiKey.CompanyId && x.ActiveStatus == ActiveStatus.Active &&
                             x.PhoneNumber == normalizedPhoneNumber)
                    .Select(x => x.Id)
                    .FirstOrDefaultAsync();

                if (string.IsNullOrEmpty(schemafulObjectViewModel.ReferencedContactPhoneNumber) ||
                    string.IsNullOrEmpty(userProfileId))
                {
                    throw new Exception(
                        $"Unable to find contact: [{schemafulObjectViewModel.ReferencedContactPhoneNumber}]");
                }

                var updateSchemafulObjectOutputOutput =
                    await _schemafulObjectsApi.SchemafulObjectsUpdateSchemafulObjectPostAsync(
                        updateSchemafulObjectInput: new UpdateSchemafulObjectInput(
                            schemafulObject.Id,
                            schema.Id,
                            apiKey.CompanyId,
                            propertyValues,
                            userProfileId,
                            string.Empty,
                            updatedVia: CustomObjectAuditSources.ZapierIntegration));
                schemafulObject = updateSchemafulObjectOutputOutput.Data.SchemafulObject;

                var zapierSchemafulObjectResponse = new ZapierSchemafulObjectResponse(
                    schemafulObject.PrimaryPropertyValue,
                    PropertyNamingMapper.FormatPropertyValues(schema, schemafulObject.PropertyValues),
                    normalizedPhoneNumber,
                    userProfileId);

                return Ok(zapierSchemafulObjectResponse);
            }
            catch (FormatException fe)
            {
                _logger.LogError(
                    fe,
                    "[Zapier {MethodName} endpoint] Parse property value exception. {CompanyId} {ExceptionMessage}",
                    nameof(CreateSchemafulObject),
                    apiKey.CompanyId,
                    fe.Message);

                return BadRequest(
                    new ResponseViewModel
                    {
                        message = $"Invalid input detected: {fe.Message}"
                    });
            }
            catch (Exception ex)
            {
                var exceptionMessage = ex.Message;

                if (ex is SleekflowErrorCodeException sece)
                {
                    exceptionMessage += ParseSleekflowExceptionMessage(sece.Output.Data?.ToString());
                }

                _logger.LogError(
                    ex,
                    "[Zapier {MethodName} endpoint] General exception. {CompanyId} {ExceptionMessage}",
                    nameof(CreateSchemafulObject),
                    apiKey.CompanyId,
                    exceptionMessage);

                return BadRequest(
                    new ResponseViewModel
                    {
                        message = $"Execute failed: {exceptionMessage}"
                    });
            }
        }

        private static string ParseSleekflowExceptionMessage(string jsonString)
        {
            try
            {
                dynamic exceptionObject = JsonConvert.DeserializeObject(jsonString);

                return " " + exceptionObject![0].errorMessage.ToString();
            }
            catch
            {
                return string.Empty;
            }
        }

        #endregion
    }
}