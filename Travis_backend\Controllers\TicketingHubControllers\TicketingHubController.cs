using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Sleekflow.Apis.TicketingHub.Api;
using Sleekflow.Apis.TicketingHub.Model;
using Travis_backend.AccountAuthenticationDomain.Models;
using Travis_backend.ContactDomain.Services;
using Travis_backend.ConversationServices;
using Travis_backend.Enums;
using Travis_backend.TicketingHubDomain;
using Travis_backend.TicketingHubDomain.Services;
using Travis_backend.TicketingHubDomain.ViewModels;

namespace Travis_backend.Controllers.TicketingHubControllers;

[Authorize]
[Route("TicketingHub")]
[TypeFilter(typeof(TicketingHubExceptionFilter))]
public class TicketingHubController : ControllerBase
{
    private readonly ILogger<TicketingHubController> _logger;
    private readonly UserManager<ApplicationUser> _userManager;
    private readonly IBlobsApi _blobsApi;
    private readonly ITicketCompanyConfigsApi _ticketCompanyConfigsApi;
    private readonly ITicketingHubService _ticketingHubService;
    private readonly IUserProfileService _userProfileService;
    private readonly ICoreService _coreService;

    public TicketingHubController(
        ILogger<TicketingHubController> logger,
        UserManager<ApplicationUser> userManager,
        ITicketingHubService ticketingHubService,
        IUserProfileService userProfileService,
        ICoreService coreService,
        IBlobsApi blobsApi,
        ITicketCompanyConfigsApi ticketCompanyConfigsApi)
    {
        _logger = logger;
        _userManager = userManager;
        _ticketingHubService = ticketingHubService;
        _userProfileService = userProfileService;
        _coreService = coreService;
        _blobsApi = blobsApi;
        _ticketCompanyConfigsApi = ticketCompanyConfigsApi;
    }

    #region Ticketing Hub Company Config

    [HttpPost("TicketCompanyConfigs/GetTicketCompanyConfig")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GetTicketCompanyConfigOutputOutput>> GetTicketCompanyConfig()
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (staff == null)
        {
            return Unauthorized();
        }

        try
        {
            var getTicketCompanyConfigOutputOutput =
                await _ticketCompanyConfigsApi.TicketCompanyConfigsGetTicketCompanyConfigPostAsync(
                    getTicketCompanyConfigInput: new GetTicketCompanyConfigInput(staff.CompanyId));

            if (!getTicketCompanyConfigOutputOutput.Success)
            {
                return NotFound();
            }

            return Ok(getTicketCompanyConfigOutputOutput);
        }
        catch (Exception e)
        {
            return NotFound();
        }
    }

    [HttpPost("TicketCompanyConfigs/CreateTicketCompanyConfig")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<CreateTicketCompanyConfigOutputOutput>> CreateTicketCompanyConfig()
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (staff == null)
        {
            return Unauthorized();
        }

        try
        {
            var createTicketCompanyConfigOutputOutput =
                await _ticketCompanyConfigsApi.TicketCompanyConfigsCreateTicketCompanyConfigPostAsync(
                    createTicketCompanyConfigInput: new CreateTicketCompanyConfigInput(
                        staff.CompanyId,
                        false));

            return Ok(createTicketCompanyConfigOutputOutput);
        }
        catch (Exception e)
        {
            return NotFound();
        }
    }

    public record UpdateTicketCompanyConfigRequest
    {
        [JsonProperty("updated_properties")]
        public Dictionary<string, object?> UpdatedProperties { get; set; }
    }

    [HttpPost("TicketCompanyConfigs/UpdateTicketCompanyConfig")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<UpdateTicketCompanyConfigOutputOutput>> UpdateTicketCompanyConfig(
        [FromBody]
        UpdateTicketCompanyConfigRequest updateTicketCompanyConfigRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (staff == null || staff.RoleType != StaffUserRole.Admin)
        {
            return Unauthorized();
        }

        var ticketUser = await _ticketingHubService.GetTicketUserAsync(staff);

        try
        {
            var updateTicketCompanyConfigOutputOutput =
                await _ticketCompanyConfigsApi.TicketCompanyConfigsUpdateTicketCompanyConfigPostAsync(
                    updateTicketCompanyConfigInput: new UpdateTicketCompanyConfigInput(
                        staff.CompanyId,
                        updateTicketCompanyConfigRequest.UpdatedProperties,
                        ticketUser));

            return Ok(updateTicketCompanyConfigOutputOutput);
        }
        catch (Exception e)
        {
            return NotFound();
        }
    }

    #endregion

    #region Tickets

    public record CreateTicketRequest
    {
        [JsonProperty("user_profile_id")]
        public string UserProfileId { get; set; }

        [JsonProperty("title")]
        public string Title { get; set; }

        [JsonProperty("channel")]
        public Channel Channel { get; set; }

        [JsonProperty("priority_id")]
        public string PriorityId { get; set; }

        [JsonProperty("assignee_id")]
        public long AssigneeId { get; set; }

        [JsonProperty("associated_message_ids")]
        public List<long>? AssociatedMessageIds { get; set; }

        [JsonProperty("meta_data")]
        public Dictionary<string, object?> Metadata { get; set; }

        [JsonProperty("description")]
        public string Description { get; set; }

        [JsonProperty("medias")]
        public List<MediaDto> Medias { get; set; }

        [JsonProperty("type_id")]
        public string TypeId { get; set; }

        [JsonProperty("url")]
        public string Url { get; set; }

        [JsonProperty("due_date")]
        public DateTimeOffset? DueDate { get; set; }
    }

    [HttpPost("Tickets/CreateTicket")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<CreateTicketOutputOutput>> CreateTicket(
        [FromBody]
        CreateTicketRequest createTicketRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (staff == null)
        {
            return Unauthorized();
        }

        var ticketUser = await _ticketingHubService.GetTicketUserAsync(staff);

        await _userProfileService.GetConversationByUserProfileId(
            staff.CompanyId,
            createTicketRequest.UserProfileId);

        var createTicketOutputOutput =
            await _ticketingHubService.CreateTicketAsync(
                staff.CompanyId,
                createTicketRequest.UserProfileId,
                createTicketRequest.Title,
                createTicketRequest.Channel,
                createTicketRequest.PriorityId,
                createTicketRequest.AssigneeId,
                createTicketRequest.Metadata,
                createTicketRequest.Description,
                createTicketRequest.Medias,
                createTicketRequest.TypeId,
                createTicketRequest.Url,
                createTicketRequest.DueDate,
                staff.Id,
                createTicketRequest.AssociatedMessageIds,
                ticketUser);

        return Ok(createTicketOutputOutput);
    }

    public record GetTicketRequest
    {
        [JsonProperty("id")]
        public string Id { get; set; }
    }

    [HttpPost("Tickets/GetTicket")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GetTicketOutputOutput>> GetTicket([FromBody] GetTicketRequest getTicketRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (staff == null)
        {
            return Unauthorized();
        }

        var ticketUser = await _ticketingHubService.GetTicketUserAsync(staff);

        var result = await _ticketingHubService.GetTicketAsync(
            getTicketRequest.Id,
            staff.CompanyId,
            ticketUser);

        return Ok(result);
    }

    public class GetTicketsRequest
    {
        [JsonProperty("filter_groups")]
        public List<FilterGroup> FilterGroups { get; set; }

        [JsonProperty("sort")]
        public Sort Sort { get; set; }

        [JsonProperty("limit")]
        public int Limit { get; set; }

        [JsonProperty("continuation_token")]
        public string? ContinuationToken { get; set; }
    }

    [HttpPost("Tickets/GetTickets")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GetSchemafulTicketsOutputOutput>> GetTickets(
        [FromBody]
        GetTicketsRequest getTicketsRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (staff == null)
        {
            return Unauthorized();
        }

        var ticketUser = await _ticketingHubService.GetTicketUserAsync(staff);

        var result = await _ticketingHubService.GetTicketsAsync(
            staff.CompanyId,
            getTicketsRequest.FilterGroups,
            getTicketsRequest.Sort,
            getTicketsRequest.Limit,
            getTicketsRequest.ContinuationToken,
            ticketUser);

        return Ok(result);
    }

    public class GetTicketIdsRequest
    {
        [JsonProperty("filter_groups")]
        public List<FilterGroup> FilterGroups { get; set; }

        [JsonProperty("sort")]
        public Sort Sort { get; set; }
    }

    [HttpPost("Tickets/GetTicketIds")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GetTicketIdsOutputOutput>> GetTicketIds(
        [FromBody]
        GetTicketIdsRequest getTicketIdsRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (staff == null)
        {
            return Unauthorized();
        }

        var ticketUser = await _ticketingHubService.GetTicketUserAsync(staff);

        var result = await _ticketingHubService.GetTicketIdsAsync(
            staff.CompanyId,
            getTicketIdsRequest.FilterGroups,
            getTicketIdsRequest.Sort,
            ticketUser);

        return Ok(result);
    }

    public class GetTicketCountRequest
    {
        [JsonProperty("filter_groups")]
        public List<FilterGroup> FilterGroups { get; set; }

        [JsonProperty("group_bys")]
        public List<GroupBy> GroupBys { get; set; }
    }

    [HttpPost("Tickets/GetTicketCount")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GetTicketsOutputOutput>> GetTicketCount(
        [FromBody]
        GetTicketCountRequest getTicketCountRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (staff == null)
        {
            return Unauthorized();
        }

        var ticketUser = await _ticketingHubService.GetTicketUserAsync(staff);

        var result = await _ticketingHubService.GetTicketCountAsync(
            staff.CompanyId,
            getTicketCountRequest.FilterGroups,
            getTicketCountRequest.GroupBys,
            ticketUser);

        return Ok(result);
    }

    public record UpdateTicketRequest
    {
        [JsonProperty("id")]
        public string Id { get; set; }

        // //TODO: This seems redundant
        // [JsonProperty("sleekflow_staff_team_ids")]
        // public List<string>? SleekflowStaffTeamIds { get; set; }

        [JsonProperty("updated_properties")]
        public Dictionary<string, object?> UpdatedProperties { get; set; }
    }

    [HttpPost("Tickets/UpdateTicket")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<UpdateTicketOutputOutput>> UpdateTicket(
        [FromBody]
        UpdateTicketRequest updateTicketRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (staff == null)
        {
            return Unauthorized();
        }

        var ticketUser = await _ticketingHubService.GetTicketUserAsync(staff);

        var result = await _ticketingHubService.UpdateTicketAsync(
            updateTicketRequest.Id,
            staff.CompanyId,
            updateTicketRequest.UpdatedProperties,
            ticketUser);

        return Ok(result);
    }

    public record DeleteTicketRequest
    {
        [JsonProperty("id")]
        public string Id { get; set; }
    }

    [HttpPost("Tickets/DeleteTicket")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<DeleteTicketOutputOutput>> DeleteTicket(
        [FromBody]
        DeleteTicketRequest deleteTicketRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (staff == null)
        {
            return Unauthorized();
        }

        var ticketUser = await _ticketingHubService.GetTicketUserAsync(staff);

        var result = await _ticketingHubService.DeleteTicketAsync(
            deleteTicketRequest.Id,
            staff.CompanyId,
            ticketUser);

        return Ok(result);
    }

    public record DeleteTicketsRequest
    {
        [JsonProperty("ids")]
        public List<string> Ids { get; set; }
    }

    [HttpPost("Tickets/DeleteTickets")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<DeleteTicketOutputOutput>> DeleteTickets(
        [FromBody]
        DeleteTicketsRequest deleteTicketsRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (staff == null)
        {
            return Unauthorized();
        }

        var ticketUser = await _ticketingHubService.GetTicketUserAsync(staff);

        var result = await _ticketingHubService.DeleteTicketsAsync(
            deleteTicketsRequest.Ids,
            staff.CompanyId,
            ticketUser);

        return Ok(result);
    }

    #endregion

    #region Ticket Statuses

    [HttpPost("TicketStatuses/GetTicketStatuses")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GetTicketStatusesOutputOutput>> GetTicketStatuses()
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (staff == null)
        {
            return Unauthorized();
        }

        var ticketUser = await _ticketingHubService.GetTicketUserAsync(staff);

        var getTicketStatusesOutputOutput = await _ticketingHubService.GetTicketStatusesAsync(
            staff.CompanyId,
            ticketUser);

        return Ok(getTicketStatusesOutputOutput);
    }

    #endregion

    #region Ticket Priorities

    [HttpPost("TicketPriorities/GetTicketPriorities")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GetTicketPrioritiesOutputOutput>> GetTicketPriorities()
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (staff == null)
        {
            return Unauthorized();
        }

        var ticketUser = await _ticketingHubService.GetTicketUserAsync(staff);

        var getTicketPrioritiesOutputOutput =
            await _ticketingHubService.GetTicketPrioritiesAsync(staff.CompanyId, ticketUser);

        return Ok(getTicketPrioritiesOutputOutput);
    }

    #endregion

    #region Ticket Types

    public class GetTicketTypesRequest
    {
        [JsonProperty("filter_groups")]
        public List<FilterGroup> FilterGroups { get; set; }

        [JsonProperty("sort")]
        public Sort Sort { get; set; }

        [JsonProperty("limit")]
        public int Limit { get; set; }

        [JsonProperty("continuation_token")]
        public string? ContinuationToken { get; set; }
    }

    [HttpPost("TicketTypes/GetTicketTypes")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GetTicketTypesOutputOutput>> GetTicketTypes(
        [FromBody]
        GetTicketTypesRequest getTicketTypesRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (staff == null)
        {
            return Unauthorized();
        }

        var ticketUser = await _ticketingHubService.GetTicketUserAsync(staff);

        var getTicketTypesOutputOutput = await _ticketingHubService.GetTicketTypesAsync(
            staff.CompanyId,
            getTicketTypesRequest.FilterGroups,
            getTicketTypesRequest.Sort,
            getTicketTypesRequest.Limit,
            getTicketTypesRequest.ContinuationToken,
            ticketUser);

        return Ok(getTicketTypesOutputOutput);
    }

    public record GetTicketTypeRequest
    {
        [JsonProperty("id")]
        public string Id { get; set; }
    }

    [HttpPost("TicketTypes/GetTicketType")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GetTicketTypesOutputOutput>> GetTicketType(
        [FromBody]
        GetTicketTypeRequest getTicketTypeRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (staff == null)
        {
            return Unauthorized();
        }

        var ticketUser = await _ticketingHubService.GetTicketUserAsync(staff);

        var getTicketTypesOutputOutput = await _ticketingHubService.GetTicketTypeAsync(
            getTicketTypeRequest.Id,
            staff.CompanyId,
            ticketUser);

        return Ok(getTicketTypesOutputOutput);
    }

    public record CreateTicketTypeRequest
    {
        [JsonProperty("label")]
        public string Label { get; set; }
    }

    [HttpPost("TicketTypes/CreateTicketType")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GetTicketTypesOutputOutput>> CreateTicketType(
        [FromBody]
        CreateTicketTypeRequest createTicketTypeRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (staff == null)
        {
            return Unauthorized();
        }

        var ticketUser = await _ticketingHubService.GetTicketUserAsync(staff);

        var getTicketTypesOutputOutput = await _ticketingHubService.CreateTicketTypeAsync(
            staff.CompanyId,
            createTicketTypeRequest.Label,
            ticketUser);

        return Ok(getTicketTypesOutputOutput);
    }

    public record UpdateTicketTypeRequest
    {
        [JsonProperty("id")]
        public string Id { get; set; }

        [JsonProperty("label")]
        public string Label { get; set; }
    }

    [HttpPost("TicketTypes/UpdateTicketType")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GetTicketTypesOutputOutput>> UpdateTicketType(
        [FromBody]
        UpdateTicketTypeRequest updateTicketTypeRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (staff == null)
        {
            return Unauthorized();
        }

        var ticketUser = await _ticketingHubService.GetTicketUserAsync(staff);

        var getTicketTypesOutputOutput = await _ticketingHubService.UpdateTicketTypeAsync(
            updateTicketTypeRequest.Id,
            staff.CompanyId,
            updateTicketTypeRequest.Label,
            ticketUser);

        return Ok(getTicketTypesOutputOutput);
    }

    public record UpdateTicketTypeOrderRequest
    {
        [JsonProperty("ticket_types")]
        public List<TicketTypeArrangement> TicketTypes { get; set; }
    }

    [HttpPost("TicketTypes/UpdateTicketTypeOrder")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GetTicketTypesOutputOutput>> UpdateTicketTypeOrder(
        [FromBody]
        UpdateTicketTypeOrderRequest updateTicketTypeOrderRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (staff == null)
        {
            return Unauthorized();
        }

        var ticketUser = await _ticketingHubService.GetTicketUserAsync(staff);

        var getTicketTypesOutputOutput = await _ticketingHubService.UpdateTicketTypeOrderAsync(
            staff.CompanyId,
            updateTicketTypeOrderRequest.TicketTypes,
            ticketUser);

        return Ok(getTicketTypesOutputOutput);
    }

    public record DeleteTicketTypeRequest
    {
        [JsonProperty("id")]
        public string Id { get; set; }
    }

    [HttpPost("TicketTypes/DeleteTicketType")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GetTicketTypesOutputOutput>> DeleteTicketType(
        [FromBody]
        DeleteTicketTypeRequest deleteTicketTypeRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (staff == null)
        {
            return Unauthorized();
        }

        var ticketUser = await _ticketingHubService.GetTicketUserAsync(staff);

        var getTicketTypesOutputOutput = await _ticketingHubService.DeleteTicketTypeAsync(
            deleteTicketTypeRequest.Id,
            staff.CompanyId,
            ticketUser);

        return Ok(getTicketTypesOutputOutput);
    }

    #endregion

    #region Ticket Activities

    public class GetTicketActivitiesRequest
    {
        [JsonProperty("filter_groups")]
        public List<FilterGroup> FilterGroups { get; set; }

        [JsonProperty("sort")]
        public Sort Sort { get; set; }

        [JsonProperty("limit")]
        public int Limit { get; set; }

        [JsonProperty("continuation_token")]
        public string? ContinuationToken { get; set; }
    }

    [HttpPost("TicketActivities/GetTicketActivities")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GetSchemafulTicketActivitiesOutputOutput>> GetTicketActivities(
        [FromBody]
        GetTicketActivitiesRequest getTicketActivitiesRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (staff == null)
        {
            return Unauthorized();
        }

        var ticketUser = await _ticketingHubService.GetTicketUserAsync(staff);

        var result = await _ticketingHubService.GetTicketActivitiesAsync(
            staff.CompanyId,
            getTicketActivitiesRequest.FilterGroups,
            getTicketActivitiesRequest.Sort,
            getTicketActivitiesRequest.Limit,
            getTicketActivitiesRequest.ContinuationToken);

        return Ok(result);
    }

    #endregion

    #region Ticket Comment

    public class GetTicketCommentsRequest
    {
        [JsonProperty("filter_groups")]
        public List<FilterGroup> FilterGroups { get; set; }

        [JsonProperty("sort")]
        public Sort Sort { get; set; }

        [JsonProperty("limit")]
        public int Limit { get; set; }

        [JsonProperty("continuation_token")]
        public string? ContinuationToken { get; set; }
    }

    [HttpPost("TicketComments/GetTicketComments")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GetTicketCommentsOutputOutput>> GetTicketComments(
        [FromBody]
        GetTicketCommentsRequest getTicketCommentsRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var result = await _ticketingHubService.GetTicketCommentsAsync(
            staff.CompanyId,
            getTicketCommentsRequest.FilterGroups,
            getTicketCommentsRequest.Sort,
            getTicketCommentsRequest.Limit,
            getTicketCommentsRequest.ContinuationToken);

        return Ok(result);
    }

    public class CreateTicketCommentRequest
    {
        [JsonProperty("ticket_id")]
        public string TicketId { get; set; }

        [JsonProperty("content")]
        public string Content { get; set; }

        [JsonProperty("medias")]
        public List<TicketCommentMediaDto> Medias { get; set; }
    }

    [HttpPost("TicketComments/CreateTicketComment")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GetTicketCommentsOutputOutput>> CreateTicketComment(
        [FromBody]
        CreateTicketCommentRequest createTicketCommentRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var result = await _ticketingHubService.CreateTicketCommentAsync(
            staff.CompanyId,
            createTicketCommentRequest.TicketId,
            createTicketCommentRequest.Content,
            createTicketCommentRequest.Medias,
            staff.Id);

        return Ok(result);
    }

    public class DeleteTicketCommentRequest
    {
        [JsonProperty("id")]
        public string Id { get; set; }

        [JsonProperty("ticket_id")]
        public string TicketId { get; set; }
    }

    [HttpPost("TicketComments/DeleteTicketComment")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GetTicketCommentsOutputOutput>> DeleteTicketComment(
        [FromBody]
        DeleteTicketCommentRequest deleteTicketCommentRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var result = await _ticketingHubService.DeleteTicketCommentAsync(
            deleteTicketCommentRequest.Id,
            staff.CompanyId,
            deleteTicketCommentRequest.TicketId,
            staff.Id);

        return Ok(result);
    }

    #endregion

    #region Blobs

    public class CreateBlobDownloadSasUrlsRequest
    {
        [JsonProperty("blob_names")]
        public List<string> BlobNames { get; set; }

        [JsonProperty("blob_type")]
        public string BlobType { get; set; }
    }

    [HttpPost("Blobs/CreateBlobDownloadSasUrls")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<CreateBlobDownloadSasUrlsOutputOutput>> CreateBlobDownloadSasUrls(
        [FromBody]
        CreateBlobDownloadSasUrlsRequest createBlobDownloadSasUrlsRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (staff == null)
        {
            return Unauthorized();
        }

        var createBlobDownloadSasUrlsOutputOutput = await _blobsApi.BlobsCreateBlobDownloadSasUrlsPostAsync(
            createBlobDownloadSasUrlsInput: new CreateBlobDownloadSasUrlsInput(
                staff.CompanyId,
                createBlobDownloadSasUrlsRequest.BlobNames,
                createBlobDownloadSasUrlsRequest.BlobType));

        return Ok(createBlobDownloadSasUrlsOutputOutput);
    }

    public class CreateBlobUploadSasUrlsRequest
    {
        [JsonProperty("number_of_blobs")]
        public int NumberOfBlobs { get; set; }

        [JsonProperty("blob_type")]
        public string BlobType { get; set; }
    }

    [HttpPost("Blobs/CreateBlobUploadSasUrls")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<CreateBlobUploadSasUrlsOutputOutput>> CreateBlobUploadSasUrls(
        [FromBody]
        CreateBlobUploadSasUrlsRequest createBlobUploadSasUrlsRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (staff == null)
        {
            return Unauthorized();
        }

        var createBlobUploadSasUrlsOutputOutput = await _blobsApi.BlobsCreateBlobUploadSasUrlsPostAsync(
            createBlobUploadSasUrlsInput: new CreateBlobUploadSasUrlsInput(
                staff.CompanyId,
                createBlobUploadSasUrlsRequest.NumberOfBlobs,
                createBlobUploadSasUrlsRequest.BlobType));

        return Ok(createBlobUploadSasUrlsOutputOutput);
    }

    public class DeleteBlobsRequest
    {
        [JsonProperty("blob_names")]
        public List<string> BlobNames { get; set; }

        [JsonProperty("blob_type")]
        public string BlobType { get; set; }
    }

    [HttpPost("Blobs/DeleteBlobs")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<DeleteBlobsOutputOutput>> DeleteBlobs(
        [FromBody]
        DeleteBlobsRequest deleteBlobsRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (staff == null)
        {
            return Unauthorized();
        }

        var deleteBlobsOutputOutput = await _blobsApi.BlobsDeleteBlobsPostAsync(
            deleteBlobsInput: new DeleteBlobsInput(
                staff.CompanyId,
                deleteBlobsRequest.BlobNames,
                deleteBlobsRequest.BlobType));

        return Ok(deleteBlobsOutputOutput);
    }

    #endregion Blobs
}