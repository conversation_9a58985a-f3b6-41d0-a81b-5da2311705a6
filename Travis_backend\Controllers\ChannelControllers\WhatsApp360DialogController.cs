﻿using System;
using System.Linq;
using System.Threading.Tasks;
using AutoMapper;
using AutoMapper.QueryableExtensions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Travis_backend.AccountAuthenticationDomain.Models;
using Travis_backend.Cache;
using Travis_backend.ChannelDomain.Models;
using Travis_backend.ChannelDomain.ViewModels;
using Travis_backend.CommonDomain.ViewModels;
using Travis_backend.CompanyDomain.Services;
using Travis_backend.ConversationServices;
using Travis_backend.Database;
using Travis_backend.Enums;
using Travis_backend.Extensions;
using WABA360Dialog.ApiClient.Exceptions;

namespace Travis_backend.Controllers.ChannelControllers;

[Authorize]
[Route("company/whatsapp/360dialog")]
public class WhatsApp360DialogController : Controller
{
    private readonly UserManager<ApplicationUser> _userManager;
    private readonly IConfiguration _configuration;
    private readonly ApplicationDbContext _appDbContext;
    private readonly IMapper _mapper;
    private readonly IWhatsApp360DialogService _whatsApp360DialogService;
    private readonly ILogger _logger;
    private readonly ICompanyInfoCacheService _companyInfoCacheService;
    private readonly ICoreService _coreService;
    private readonly ICompanyUsageService _companyUsageService;

    public WhatsApp360DialogController(
        ApplicationDbContext appDbContext,
        UserManager<ApplicationUser> userManager,
        IConfiguration configuration,
        IMapper mapper,
        ILogger<WhatsApp360DialogController> logger,
        ICompanyInfoCacheService companyInfoCacheService,
        ICoreService coreService,
        IWhatsApp360DialogService whatsApp360DialogService,
        ICompanyUsageService companyUsageService)
    {
        _userManager = userManager;
        _configuration = configuration;
        _appDbContext = appDbContext;
        _mapper = mapper;
        _logger = logger;
        _companyInfoCacheService = companyInfoCacheService;
        _coreService = coreService;
        _whatsApp360DialogService = whatsApp360DialogService;
        _companyUsageService = companyUsageService;
    }

    /// <summary>
    /// All Get WhatsApp 360Dialog Channels.
    /// </summary>
    /// <returns></returns>
    [HttpGet("all")]
    public async ValueTask<ActionResult<WhatsApp360DialogByWabaConfigResponse>> GetWhatsApp360DialogChannels(
        [FromQuery]
        string phoneNumber,
        [FromQuery]
        string wabaAccountId,
        [FromQuery]
        string accountMode,
        [FromQuery]
        string wabaStatus,
        [FromQuery]
        string wabaBusinessStatus,
        [FromQuery]
        string wabaAccountType,
        [FromQuery]
        bool? isClient)
    {
        var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (companyUser == null)
        {
            return Unauthorized();
        }

        var channelConfigs = await _appDbContext.ConfigWhatsApp360DialogConfigs
            .AsNoTracking()
            .Where(x => x.CompanyId == companyUser.CompanyId)
            .WhereIf(!string.IsNullOrWhiteSpace(phoneNumber), x => x.WhatsAppPhoneNumber == phoneNumber)
            .WhereIf(!string.IsNullOrWhiteSpace(wabaAccountId), x => x.WabaAccountId == wabaAccountId)
            .WhereIf(!string.IsNullOrWhiteSpace(accountMode), x => x.AccountMode == accountMode)
            .WhereIf(!string.IsNullOrWhiteSpace(wabaStatus), x => x.WabaStatus == wabaStatus)
            .WhereIf(!string.IsNullOrWhiteSpace(wabaBusinessStatus), x => x.WabaBusinessStatus == wabaBusinessStatus)
            .WhereIf(!string.IsNullOrWhiteSpace(wabaAccountType), x => x.WabaAccountType == wabaAccountType)
            .WhereIf(isClient.HasValue, x => x.IsClient == isClient)
            .ProjectTo<WhatsApp360DialogConfigViewModel>(_mapper.ConfigurationProvider)
            .ToListAsync(HttpContext.RequestAborted);

        return Ok(
            new WhatsApp360DialogByWabaConfigResponse
            {
                WhatsApp360DialogConfigs = channelConfigs
            });
    }

    /// <summary>
    /// Get WhatsApp 360Dialog Channels Group by waba account.
    /// </summary>
    /// <returns></returns>
    [HttpGet("by-waba")]
    public async ValueTask<ActionResult<WhatsApp360DialogByWabaConfigResponse>>
        GetWhatsApp360DialogChannelsGroupByWaba()
    {
        var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (companyUser == null)
        {
            return Unauthorized();
        }

        var channelConfig = await _appDbContext.ConfigWhatsApp360DialogConfigs
            .AsNoTracking()
            .Where(x => x.CompanyId == companyUser.CompanyId)
            .ProjectTo<WhatsApp360DialogConfigViewModel>(_mapper.ConfigurationProvider)
            .ToListAsync(HttpContext.RequestAborted);

        var channelConfigByGroup = channelConfig.GroupBy(x => x.WabaAccountId)
            .Select(
                x => new WhatsApp360DialogConfigByWabaIdViewModel
                {
                    WabaAccountId = x.Key,
                    TemplateNamespace = x.FirstOrDefault()?.TemplateNamespace,
                    WhatsApp360DialogConfigs = x.ToList()
                }).ToList();

        return Ok(
            new WhatsApp360DialogConfigByWabaResponse
            {
                WhatsApp360DialogByWabaIdConfigs = channelConfigByGroup
            });
    }

    /// <summary>
    /// Get WhatsApp 360Dialog Channel.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpGet("{whatsApp360DialogConfigId}")]
    public async Task<ActionResult<WhatsApp360DialogConfigViewModel>> GetWhatsApp360DialogChannel(
        [FromRoute]
        long whatsApp360DialogConfigId)
    {
        var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (companyUser == null)
        {
            return Unauthorized();
        }

        var channelConfig = await _appDbContext.ConfigWhatsApp360DialogConfigs
            .AsNoTracking()
            .Where(x => x.Id == whatsApp360DialogConfigId && x.CompanyId == companyUser.CompanyId)
            .ProjectTo<WhatsApp360DialogConfigViewModel>(_mapper.ConfigurationProvider)
            .FirstOrDefaultAsync();

        return Ok(channelConfig);
    }

    /// <summary>
    /// Connect WhatsApp 360Dialog Channel (Our Client Only, non client will throw exception).
    /// </summary>
    /// <returns></returns>
    [HttpPost("connect")]
    [ProducesResponseType(200, Type = typeof(WhatsApp360DialogConfigViewModel))]
    [ProducesResponseType(400, Type = typeof(WhatsApp360DialogErrorResponse))]
    public async ValueTask<ActionResult<WhatsApp360DialogConfigViewModel>> ConnectWhatsApp360DialogChannel(
        [FromBody]
        ConnectWhatsApp360DialogChannelRequest request)
    {
        var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (companyUser == null)
        {
            return Unauthorized();
        }

        var companyUsage = await _companyUsageService.GetCompanyUsage(companyUser.CompanyId);

        if (companyUsage.totalChannelAdded > companyUsage.MaximumNumberOfChannel)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = $"Your subscription plan only could add {companyUsage.MaximumNumberOfChannel} channels"
                });
        }

        if (await _appDbContext.ConfigWhatsApp360DialogConfigs.AnyAsync(x => x.ApiKey == request.ApiKey))
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "This channel has already existed in Sleekflow!"
                });
        }

        WhatsApp360DialogConfig newWhatsApp360DialogConfig = null;
        try
        {
            newWhatsApp360DialogConfig = await _whatsApp360DialogService.ConnectChannel(
                companyUser.CompanyId,
                request.ChannelName,
                request.ApiKey);
        }
        catch (ApiClientException ex)
        {
            return BadRequest(
                new WhatsApp360DialogErrorResponse()
                {
                    Message = "Error during connect channel! The provided API Key is invalid.",
                    WhatsApp360DialogApiError = new WhatsApp360DialogApiError()
                    {
                        Error = ex.Error?.ToList(), Meta = ex.Meta
                    }
                });
        }
        catch (Exception ex)
        {
            return BadRequest(
                new ResponseViewModel()
                {
                    message = $"Error during connect channel! {ex.Message}"
                });
        }

        await _companyInfoCacheService.RemoveCompanyInfoCache(companyUser.CompanyId);

        return Ok(_mapper.Map<WhatsApp360DialogConfigViewModel>(newWhatsApp360DialogConfig));
    }

    /// <summary>
    /// Update channel config - channel name, api key.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPut("{whatsApp360DialogConfigId}")]
    public async Task<ActionResult<WhatsApp360DialogConfigViewModel>> UpdateChannel(
        [FromRoute]
        long whatsApp360DialogConfigId,
        [FromBody]
        UpdateWhatsApp360DialogConnectedChannelRequest request)
    {
        var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (companyUser == null)
        {
            return Unauthorized();
        }

        if (!await _appDbContext.ConfigWhatsApp360DialogConfigs.AnyAsync(
                x => x.CompanyId == companyUser.CompanyId && x.Id == whatsApp360DialogConfigId))
        {
            return BadRequest(
                new ResponseViewModel()
                {
                    message = "Channel not found."
                });
        }

        try
        {
            var whatsApp360DialogConfig = await _whatsApp360DialogService.UpdateChannel(
                companyUser.CompanyId,
                whatsApp360DialogConfigId,
                request.ChannelName,
                request.ApiKey);

            await _companyInfoCacheService.RemoveCompanyInfoCache(companyUser.CompanyId);

            return Ok(_mapper.Map<WhatsApp360DialogConfigViewModel>(whatsApp360DialogConfig));
        }
        catch (ApiClientException ex)
        {
            await _whatsApp360DialogService.UpdateChannelErrorStatus(
                companyUser.CompanyId,
                whatsApp360DialogConfigId,
                ex);
            return BadRequest(
                new WhatsApp360DialogErrorResponse()
                {
                    Message = "Error during Update 360 Dialog Channel.",
                    WhatsApp360DialogApiError = new WhatsApp360DialogApiError()
                    {
                        Error = ex.Error?.ToList(), Meta = ex.Meta
                    }
                });
        }
        catch (Exception e)
        {
            return BadRequest(
                new ResponseViewModel()
                {
                    message = "Error during Update 360 Dialog Channel."
                });
        }
    }

    /// <summary>
    /// Reconnect Channel using existing 360dialog Api Key.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost("{whatsApp360DialogConfigId}/reconnect")]
    public async Task<ActionResult<WhatsApp360DialogConfigViewModel>> ReconnectChannel(
        [FromRoute]
        long whatsApp360DialogConfigId)
    {
        var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (companyUser == null)
        {
            return Unauthorized();
        }

        if (!await _appDbContext.ConfigWhatsApp360DialogConfigs.AnyAsync(
                x => x.CompanyId == companyUser.CompanyId && x.Id == whatsApp360DialogConfigId))
        {
            return BadRequest(
                new ResponseViewModel()
                {
                    message = "Channel not found."
                });
        }

        try
        {
            var whatsApp360DialogConfig = await _whatsApp360DialogService.ReconnectChannel(
                companyUser.CompanyId,
                whatsApp360DialogConfigId);
            return Ok(_mapper.Map<WhatsApp360DialogConfigViewModel>(whatsApp360DialogConfig));
        }
        catch (ApiClientException ex)
        {
            await _whatsApp360DialogService.UpdateChannelErrorStatus(
                companyUser.CompanyId,
                whatsApp360DialogConfigId,
                ex);
            return BadRequest(
                new WhatsApp360DialogErrorResponse()
                {
                    Message = "Error during Update Api Key!",
                    WhatsApp360DialogApiError = new WhatsApp360DialogApiError()
                    {
                        Error = ex.Error?.ToList(), Meta = ex.Meta
                    }
                });
        }
        catch (Exception e)
        {
            return BadRequest(
                new ResponseViewModel()
                {
                    message = "Error during Update Api Key!",
                });
        }
    }

    /// <summary>
    /// Delete channel.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpDelete("{whatsApp360DialogConfigId}")]
    public async Task<ActionResult<ResponseViewModel>> DeleteChannel([FromRoute] long whatsApp360DialogConfigId)
    {
        var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (companyUser == null)
        {
            return Unauthorized();
        }

        if (companyUser.RoleType != StaffUserRole.Admin)
        {
            return BadRequest(
                new ResponseViewModel()
                {
                    message = "You have not permission to delete this channel"
                });
        }

        if (!await _appDbContext.ConfigWhatsApp360DialogConfigs.AnyAsync(
                x => x.CompanyId == companyUser.CompanyId && x.Id == whatsApp360DialogConfigId))
        {
            return BadRequest(
                new ResponseViewModel()
                {
                    message = "Channel not found."
                });
        }

        await _whatsApp360DialogService.RemoveChannel(companyUser.CompanyId, whatsApp360DialogConfigId);

        await _companyInfoCacheService.RemoveCompanyInfoCache(companyUser.CompanyId);

        return Ok(
            new ResponseViewModel()
            {
                message = "Channel deleted."
            });
    }

    /// <summary>
    /// Update channel channel.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost("{whatsApp360DialogConfigId}/status")]
    public async Task<ActionResult<ResponseViewModel>> UpdateChannel([FromRoute] long whatsApp360DialogConfigId)
    {
        var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (companyUser == null)
        {
            return Unauthorized();
        }

        if (!await _appDbContext.ConfigWhatsApp360DialogConfigs
                .AnyAsync(x => x.CompanyId == companyUser.CompanyId && x.Id == whatsApp360DialogConfigId))
        {
            return BadRequest(
                new ResponseViewModel()
                {
                    message = "Channel not found."
                });
        }

        var whatsApp360DialogConfig =
            await _whatsApp360DialogService.UpdateCompanyChannelStatus(
                companyUser.CompanyId,
                whatsApp360DialogConfigId);

        return Ok(_mapper.Map<WhatsApp360DialogConfigViewModel>(whatsApp360DialogConfig));
    }

    /// <summary>
    /// Create Onboarding Channel Info.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost("partner/onboarding/channel")]
    public async Task<ActionResult<CreateWhatsapp360DialogOnboardingChannelApiKeyResponse>> CreateOnboardingChannelInfo(
        [FromBody]
        CreateWhatsapp360DialogOnboardingChannelApiKeyRequest request)
    {
        var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (companyUser == null)
        {
            return Unauthorized();
        }

        try
        {
            var info = await _whatsApp360DialogService.CreateOnboardingChannelInfo(
                request.PartnerId,
                request.ClientId,
                request.ChannelId);

            return Ok(
                new CreateWhatsapp360DialogOnboardingChannelApiKeyResponse()
                {
                    Whatsapp360DialogOnboardingChannelInfo = info
                });
        }
        catch (ApiClientException ex)
        {
            return BadRequest(
                new WhatsApp360DialogErrorResponse()
                {
                    Message = "Error during create channel api key.",
                    WhatsApp360DialogApiError = new WhatsApp360DialogApiError()
                    {
                        Error = ex.Error?.ToList(), Meta = ex.Meta
                    }
                });
        }
        catch (Exception e)
        {
            return BadRequest(
                new ResponseViewModel()
                {
                    message = "Error during create channel api key."
                });
        }
    }
}