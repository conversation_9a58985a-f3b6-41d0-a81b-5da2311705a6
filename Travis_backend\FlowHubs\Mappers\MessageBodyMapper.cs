﻿using System;
using System.Collections.Generic;
using GraphApi.Client.Const.WhatsappCloudApi;
using Newtonsoft.Json;
using Sleekflow.Apis.FlowHub.Model;
using Sleekflow.Apis.MessagingHub.Model;
using Travis_backend.MessageDomain.Models;
using Travis_backend.MessageDomain.ViewModels;

namespace Travis_backend.FlowHubs.Mappers;

public static class MessageBodyMapper
{
    // ReSharper disable ConditionIsAlwaysTrueOrFalse
    // ReSharper disable ConstantConditionalAccessQualifier
    public static ExtendedMessagePayloadDetail ToCloudApiExtendedMessagePayloadDetail(
        string messageType,
        MessageBody messageBody)
    {
        ExtendedMessagePayloadDetail extendedMessagePayloadDetail;

        switch (messageType)
        {
            case "contacts":
            {
                var contacts = new List<WhatsappCloudApiContactObject>();

                if (messageBody.ContactsMessage != null)
                {
                    foreach (var cm in messageBody.ContactsMessage)
                    {
                        var addresses = new List<WhatsappCloudApiAddressObject>();
                        if (cm.Addresses != null)
                        {
                            foreach (var a in cm.Addresses)
                            {
                                addresses.Add(
                                    new WhatsappCloudApiAddressObject(
                                        a.Street,
                                        a.City,
                                        a.State,
                                        a.Zip,
                                        a.Country,
                                        a.CountryCode));
                            }
                        }

                        var emails = new List<WhatsappCloudApiEmailObject>();
                        if (cm.Emails != null)
                        {
                            foreach (var e in cm.Emails)
                            {
                                emails.Add(new WhatsappCloudApiEmailObject(e.Email, e.Type));
                            }
                        }

                        var name = new WhatsappCloudApiNameObject();
                        if (cm.Name != null)
                        {
                            name.FormattedName = cm.Name.FormattedName;
                            name.FirstName = cm.Name.FirstName;
                            name.LastName = cm.Name.LastName;
                            name.MiddleName = cm.Name.MiddleName;
                            name.Suffix = cm.Name.Suffix;
                            name.Prefix = cm.Name.Prefix;
                        }

                        var org = new WhatsappCloudApiOrgObject();
                        if (cm.Org != null)
                        {
                            org.Company = cm.Org.Company;
                            org.Title = cm.Org.Title;
                            org.Department = cm.Org.Department;
                        }

                        var imsObjects = new List<WhatsappCloudApiImsObject>();
                        if (cm.Ims != null)
                        {
                            foreach (var o in cm.Ims)
                            {
                                imsObjects.Add(new WhatsappCloudApiImsObject(o.Service, o.UserId));
                            }
                        }

                        var phones = new List<WhatsappCloudApiPhoneObject>();
                        if (cm.Phones != null)
                        {
                            foreach (var p in cm.Phones)
                            {
                                phones.Add(new WhatsappCloudApiPhoneObject(p.Phone, p.Type, p.WaId));
                            }
                        }

                        var urls = new List<WhatsappCloudApiUrlObject>();
                        if (cm.Urls != null)
                        {
                            foreach (var p in cm.Urls)
                            {
                                urls.Add(new WhatsappCloudApiUrlObject(p.Url, p.Type));
                            }
                        }

                        contacts.Add(
                            new WhatsappCloudApiContactObject(
                                addresses,
                                cm.Birthday,
                                emails,
                                name,
                                org,
                                imsObjects,
                                phones,
                                urls));
                    }
                }

                extendedMessagePayloadDetail = new ExtendedMessagePayloadDetail()
                {
                    WhatsappCloudApiContactsObject = contacts
                };
                break;
            }

            case "currency":
                throw new NotImplementedException();

            case "location":
            {
                var location = new WhatsappCloudApiLocationObject();

                if (messageBody.LocationMessage != null)
                {
                    location.Longitude = messageBody.LocationMessage.Longitude ?? 0;
                    location.Latitude = messageBody.LocationMessage.Latitude ?? 0;
                    location.Name = messageBody.LocationMessage.Name;
                    location.Address = messageBody.LocationMessage.Address;
                }

                extendedMessagePayloadDetail = new ExtendedMessagePayloadDetail()
                {
                    WhatsappCloudApiLocationObject = location
                };
                break;
            }

            case "reaction":
            {
                var reaction = new WhatsappCloudApiReactionObject();

                if (messageBody.ReactionMessage != null)
                {
                    reaction.MessageId = messageBody.ReactionMessage.MessageId;
                    reaction.Emoji = messageBody.ReactionMessage.Emoji;
                }

                extendedMessagePayloadDetail = new ExtendedMessagePayloadDetail()
                {
                    WhatsappCloudApiReactionObject = reaction
                };
                break;
            }

            case "interactive":
            {
                var interactiveObject = new WhatsappCloudApiInteractiveObject();

                if (messageBody.InteractiveMessage != null)
                {
                    interactiveObject.Type = messageBody.InteractiveMessage.Type;

                    if (messageBody.InteractiveMessage.Header != null)
                    {
                        interactiveObject.Header = new WhatsappCloudApiHeaderObject(
                            messageBody.InteractiveMessage.Header.Type,
                            messageBody.InteractiveMessage.Header.Text);

                        if (messageBody.InteractiveMessage.Header.Video != null)
                        {
                            interactiveObject.Header.Video = new WhatsappCloudApiMediaObject(
                                messageBody.InteractiveMessage.Header.Video?.Id,
                                messageBody.InteractiveMessage.Header.Video?.Link,
                                messageBody.InteractiveMessage.Header.Video?.Caption,
                                messageBody.InteractiveMessage.Header.Video?.Filename,
                                new WhatsappCloudApiProviderObject(
                                    messageBody.InteractiveMessage.Header.Video?.Provider?.Name,
                                    messageBody.InteractiveMessage.Header.Video?.Provider?.Type,
                                    new WhatsappCloudApiConfigObject(
                                        new WhatsappCloudApiBasicAuth(
                                            messageBody.InteractiveMessage.Header.Video?.Provider?.Config?.Basic
                                                ?.Username,
                                            messageBody.InteractiveMessage.Header.Video?.Provider?.Config?.Basic
                                                ?.Password),
                                        new WhatsappCloudApiBearerAuth(
                                            messageBody.InteractiveMessage.Header.Video?.Provider?.Config?.Bearer
                                                ?.Bearer)))
                            );
                        }

                        if (messageBody.InteractiveMessage.Header.Image != null)
                        {
                            interactiveObject.Header.Image = new WhatsappCloudApiMediaObject(
                                messageBody.InteractiveMessage.Header.Image?.Id,
                                messageBody.InteractiveMessage.Header.Image?.Link,
                                messageBody.InteractiveMessage.Header.Image?.Caption,
                                messageBody.InteractiveMessage.Header.Image?.Filename,
                                new WhatsappCloudApiProviderObject(
                                    messageBody.InteractiveMessage.Header.Image?.Provider?.Name,
                                    messageBody.InteractiveMessage.Header.Image?.Provider?.Type,
                                    new WhatsappCloudApiConfigObject(
                                        new WhatsappCloudApiBasicAuth(
                                            messageBody.InteractiveMessage.Header.Image?.Provider?.Config?.Basic
                                                ?.Username,
                                            messageBody.InteractiveMessage.Header.Image?.Provider?.Config?.Basic
                                                ?.Password),
                                        new WhatsappCloudApiBearerAuth(
                                            messageBody.InteractiveMessage.Header.Image?.Provider?.Config?.Bearer
                                                ?.Bearer)))
                            );
                        }

                        if (messageBody.InteractiveMessage.Header.Document != null)
                        {
                            interactiveObject.Header.Document = new WhatsappCloudApiMediaObject(
                                messageBody.InteractiveMessage.Header.Document?.Id,
                                messageBody.InteractiveMessage.Header.Document?.Link,
                                messageBody.InteractiveMessage.Header.Document?.Caption,
                                messageBody.InteractiveMessage.Header.Document?.Filename,
                                new WhatsappCloudApiProviderObject(
                                    messageBody.InteractiveMessage.Header.Document?.Provider?.Name,
                                    messageBody.InteractiveMessage.Header.Document?.Provider?.Type,
                                    new WhatsappCloudApiConfigObject(
                                        new WhatsappCloudApiBasicAuth(
                                            messageBody.InteractiveMessage.Header.Document?.Provider?.Config?.Basic
                                                ?.Username,
                                            messageBody.InteractiveMessage.Header.Document?.Provider?.Config?.Basic
                                                ?.Password),
                                        new WhatsappCloudApiBearerAuth(
                                            messageBody.InteractiveMessage.Header.Document?.Provider?.Config?.Bearer
                                                ?.Bearer)))
                            );
                        }
                    }

                    if (messageBody.InteractiveMessage.Body != null)
                    {
                        interactiveObject.Body = new WhatsappCloudApiTextBodyObject(
                            messageBody.InteractiveMessage.Body.Text);
                    }

                    if (messageBody.InteractiveMessage.Footer != null)
                    {
                        interactiveObject.Footer = new WhatsappCloudApiTextFooterObject(
                            messageBody.InteractiveMessage.Footer.Text);
                    }

                    if (messageBody.InteractiveMessage.Action != null)
                    {
                        var actionObject = new WhatsappCloudApiActionObject()
                        {
                            Button = messageBody.InteractiveMessage.Action.Button,
                            CatalogId = messageBody.InteractiveMessage.Action.CatalogId,
                            ProductRetailerId = messageBody.InteractiveMessage.Action.ProductRetailerId,
                        };

                        if (messageBody.InteractiveMessage.Action.Buttons != null)
                        {
                            var srcActionButtons = new List<WhatsappCloudApiButtonObject>();
                            foreach (var actionButton in messageBody.InteractiveMessage.Action.Buttons)
                            {
                                var srcButton = new WhatsappCloudApiButtonObject
                                {
                                    Type = actionButton.Type
                                };
                                if (actionButton.Reply != null)
                                {
                                    var srcActionButtonReply = new WhatsappCloudApiReplyObject
                                    {
                                        Id = actionButton.Reply.Id, Title = actionButton.Reply.Title
                                    };
                                    srcButton.Reply = srcActionButtonReply;
                                }

                                srcActionButtons.Add(srcButton);
                            }

                            actionObject.Buttons = srcActionButtons;
                        }

                        if (messageBody.InteractiveMessage.Action.Sections != null)
                        {
                            var srcActionSections = new List<WhatsappCloudApiSectionObject>();
                            foreach (var actionSection in messageBody.InteractiveMessage.Action.Sections)
                            {
                                var srcSection = new WhatsappCloudApiSectionObject
                                {
                                    Title = actionSection.Title
                                };

                                if (actionSection.ProductItems != null)
                                {
                                    var srcProductItems = new List<WhatsappCloudApiProductObject>();
                                    foreach (var sectionProductItem in actionSection.ProductItems)
                                    {
                                        var srcProductItem = new WhatsappCloudApiProductObject
                                        {
                                            ProductRetailerId = sectionProductItem.ProductRetailerId
                                        };
                                        srcProductItems.Add(srcProductItem);
                                    }

                                    srcSection.ProductItems = srcProductItems;
                                }

                                if (actionSection.Rows != null)
                                {
                                    var srcSectionRows = new List<WhatsappCloudApiRowsObject>();
                                    foreach (var sectionRow in actionSection.Rows)
                                    {
                                        var srcRow = new WhatsappCloudApiRowsObject
                                        {
                                            Id = sectionRow.Id,
                                            Title = sectionRow.Title,
                                            Description = sectionRow.Description
                                        };
                                        srcSectionRows.Add(srcRow);
                                    }

                                    srcSection.Rows = srcSectionRows;
                                }

                                srcActionSections.Add(srcSection);
                            }

                            actionObject.Sections = srcActionSections;
                        }

                        interactiveObject.Action = actionObject;
                    }
                }

                extendedMessagePayloadDetail = new ExtendedMessagePayloadDetail
                {
                    WhatsappCloudApiInteractiveObject = interactiveObject
                };
                break;
            }

            case "template":
            {
                var whatsappCloudApiTemplateMessageViewModel = new WhatsappCloudApiTemplateMessageViewModel();

                if (messageBody.TemplateMessage != null)
                {
                    whatsappCloudApiTemplateMessageViewModel.TemplateName = messageBody.TemplateMessage.TemplateName;
                    whatsappCloudApiTemplateMessageViewModel.Language = messageBody.TemplateMessage.Language;

                    if (messageBody.TemplateMessage.Components != null)
                    {
                        var components = new List<WhatsappCloudApiTemplateMessageComponentObject>();

                        foreach (var c in messageBody.TemplateMessage.Components)
                        {
                            var component =
                                new WhatsappCloudApiTemplateMessageComponentObject(
                                    c.Type,
                                    c.SubType,
                                    c.Index);

                            if (c.Parameters != null)
                            {
                                var parameters = new List<WhatsappCloudApiParameterObject>();

                                foreach (var p in c.Parameters)
                                {
                                    var parameter = new WhatsappCloudApiParameterObject(
                                        p.Type,
                                        p.Text,
                                        p.Payload);

                                    if (p.Image != null)
                                    {
                                        var imageMediaObject = new WhatsappCloudApiMediaObject
                                        {
                                            Id = p.Image.Id,
                                            Caption = p.Image.Caption,
                                            Filename = p.Image.Filename,
                                            Link = p.Image.Link
                                        };

                                        if (p.Image.Provider != null)
                                        {
                                            var imageProviderObject = new WhatsappCloudApiProviderObject(
                                                p.Image.Provider.Name,
                                                p.Image.Provider.Type);

                                            if (p.Image.Provider.Config != null)
                                            {
                                                var imageConfigObject = new WhatsappCloudApiConfigObject();

                                                if (p.Image.Provider.Config.Basic != null)
                                                {
                                                    imageConfigObject.Basic = new WhatsappCloudApiBasicAuth(
                                                        p.Image.Provider.Config.Basic.Username,
                                                        p.Image.Provider.Config.Basic.Password);
                                                }

                                                if (p.Image.Provider.Config.Bearer != null)
                                                {
                                                    imageConfigObject.Bearer = new WhatsappCloudApiBearerAuth(
                                                        p.Image.Provider.Config.Bearer.Bearer);
                                                }

                                                imageProviderObject.Config = imageConfigObject;
                                            }

                                            imageMediaObject.Provider = imageProviderObject;
                                        }

                                        parameter.Image = imageMediaObject;
                                    }

                                    if (p.Audio != null)
                                    {
                                        var audioMediaObject = new WhatsappCloudApiMediaObject
                                        {
                                            Id = p.Audio.Id,
                                            Caption = p.Audio.Caption,
                                            Filename = p.Audio.Filename,
                                            Link = p.Audio.Link
                                        };

                                        if (p.Audio.Provider != null)
                                        {
                                            var audioProviderObject = new WhatsappCloudApiProviderObject(
                                                p.Audio.Provider.Name,
                                                p.Audio.Provider.Type);

                                            if (p.Audio.Provider.Config != null)
                                            {
                                                var audioConfigObject = new WhatsappCloudApiConfigObject();

                                                if (p.Audio.Provider.Config.Basic != null)
                                                {
                                                    audioConfigObject.Basic = new WhatsappCloudApiBasicAuth(
                                                        p.Audio.Provider.Config.Basic.Username,
                                                        p.Audio.Provider.Config.Basic.Password);
                                                }

                                                if (p.Audio.Provider.Config.Bearer != null)
                                                {
                                                    audioConfigObject.Bearer = new WhatsappCloudApiBearerAuth(
                                                        p.Audio.Provider.Config.Bearer.Bearer);
                                                }

                                                audioProviderObject.Config = audioConfigObject;
                                            }

                                            audioMediaObject.Provider = audioProviderObject;
                                        }

                                        parameter.Audio = audioMediaObject;
                                    }

                                    if (p.Document != null)
                                    {
                                        var documentMediaObject = new WhatsappCloudApiMediaObject
                                        {
                                            Id = p.Document.Id,
                                            Caption = p.Document.Caption,
                                            Filename = p.Document.Filename,
                                            Link = p.Document.Link
                                        };

                                        if (p.Document.Provider != null)
                                        {
                                            var documentProviderObject = new WhatsappCloudApiProviderObject(
                                                p.Document.Provider.Name,
                                                p.Document.Provider.Type);

                                            if (p.Document.Provider.Config != null)
                                            {
                                                var documentConfigObject = new WhatsappCloudApiConfigObject();

                                                if (p.Document.Provider.Config.Basic != null)
                                                {
                                                    documentConfigObject.Basic = new WhatsappCloudApiBasicAuth(
                                                        p.Document.Provider.Config.Basic.Username,
                                                        p.Document.Provider.Config.Basic.Password);
                                                }

                                                if (p.Document.Provider.Config.Bearer != null)
                                                {
                                                    documentConfigObject.Bearer = new WhatsappCloudApiBearerAuth(
                                                        p.Document.Provider.Config.Bearer.Bearer);
                                                }

                                                documentProviderObject.Config = documentConfigObject;
                                            }

                                            documentMediaObject.Provider = documentProviderObject;
                                        }

                                        parameter.Document = documentMediaObject;
                                    }

                                    if (p.Video != null)
                                    {
                                        var videoMediaObject = new WhatsappCloudApiMediaObject
                                        {
                                            Id = p.Video.Id,
                                            Caption = p.Video.Caption,
                                            Filename = p.Video.Filename,
                                            Link = p.Video.Link
                                        };

                                        if (p.Video.Provider != null)
                                        {
                                            var videoProviderObject = new WhatsappCloudApiProviderObject(
                                                p.Video.Provider.Name,
                                                p.Video.Provider.Type);

                                            if (p.Video.Provider.Config != null)
                                            {
                                                var videoConfigObject = new WhatsappCloudApiConfigObject();

                                                if (p.Video.Provider.Config.Basic != null)
                                                {
                                                    videoConfigObject.Basic = new WhatsappCloudApiBasicAuth(
                                                        p.Video.Provider.Config.Basic.Username,
                                                        p.Video.Provider.Config.Basic.Password);
                                                }

                                                if (p.Video.Provider.Config.Bearer != null)
                                                {
                                                    videoConfigObject.Bearer = new WhatsappCloudApiBearerAuth(
                                                        p.Video.Provider.Config.Bearer.Bearer);
                                                }

                                                videoProviderObject.Config = videoConfigObject;
                                            }

                                            videoMediaObject.Provider = videoProviderObject;
                                        }

                                        parameter.Video = videoMediaObject;
                                    }

                                    if (p.Location != null)
                                    {
                                        parameter.Location = new WhatsappCloudApiLocationObject()
                                        {
                                            Longitude = p.Location.Longitude ?? 0,
                                            Latitude = p.Location.Latitude ?? 0,
                                            Name = p.Location.Name,
                                            Address = p.Location.Address,
                                        };
                                    }

                                    if (p.DateTime != null)
                                    {
                                        parameter.DateTime = new WhatsappCloudApiDateTimeObject()
                                        {
                                            FallbackValue = p.DateTime.FallbackValue,
                                        };
                                    }

                                    if (p.Action != null)
                                    {
                                        parameter.Action = new WhatsappCloudApiParameterActionObject();
                                    }

                                    parameters.Add(parameter);
                                }

                                component.Parameters = parameters;
                            }

                            components.Add(component);
                        }

                        whatsappCloudApiTemplateMessageViewModel.Components = components;
                    }
                }

                extendedMessagePayloadDetail = new ExtendedMessagePayloadDetail()
                {
                    WhatsappCloudApiTemplateMessageObject = whatsappCloudApiTemplateMessageViewModel
                };

                break;
            }

            default:
                throw new ArgumentOutOfRangeException(nameof(messageType));
        }

        return extendedMessagePayloadDetail;
    }

    // ReSharper disable ConditionIsAlwaysTrueOrFalse
    // ReSharper disable ConstantConditionalAccessQualifier
    public static (string MessageType, MessageBody MessageBody) ToMessageBody(
        ExtendedMessagePayloadDetail extendedMessagePayloadDetail)
    {
        var messageBody = new MessageBody();

        string messageType;
        if (extendedMessagePayloadDetail.WhatsappCloudApiContactsObject != null)
        {
            messageType = "contacts";
            var contactsMessageList = new List<ContactMessageObject>();

            foreach (var whatsappCloudApiContactObject in extendedMessagePayloadDetail.WhatsappCloudApiContactsObject)
            {
                var addresses = new List<ContactMessageObjectAddress>();
                if (whatsappCloudApiContactObject.Addresses != null)
                {
                    foreach (var a in whatsappCloudApiContactObject.Addresses)
                    {
                        var address = new ContactMessageObjectAddress(
                            a.Street,
                            a.City,
                            a.State,
                            a.Zip,
                            a.Country,
                            a.CountryCode);
                        addresses.Add(address);
                    }
                }

                var emails = new List<ContactMessageObjectEmail>();
                if (whatsappCloudApiContactObject.Emails != null)
                {
                    foreach (var e in whatsappCloudApiContactObject.Emails)
                    {
                        var email = new ContactMessageObjectEmail(e.Email, e.Type);
                        emails.Add(email);
                    }
                }

                ContactMessageObjectName name = null;
                if (whatsappCloudApiContactObject.Name != null)
                {
                    name = new ContactMessageObjectName(
                        whatsappCloudApiContactObject.Name.FormattedName,
                        whatsappCloudApiContactObject.Name.FirstName,
                        whatsappCloudApiContactObject.Name.LastName,
                        whatsappCloudApiContactObject.Name.MiddleName,
                        whatsappCloudApiContactObject.Name.Suffix,
                        whatsappCloudApiContactObject.Name.Prefix);
                }

                ContactMessageObjectOrg org = null;
                if (whatsappCloudApiContactObject.Org != null)
                {
                    org = new ContactMessageObjectOrg(
                        whatsappCloudApiContactObject.Org.Company,
                        whatsappCloudApiContactObject.Org.Title,
                        whatsappCloudApiContactObject.Org.Department);
                }

                var ims = new List<ContactMessageObjectIm>();
                if (whatsappCloudApiContactObject.Ims != null)
                {
                    foreach (var o in whatsappCloudApiContactObject.Ims)
                    {
                        var im = new ContactMessageObjectIm(o.Service, o.UserId);
                        ims.Add(im);
                    }
                }

                var phones = new List<ContactMessageObjectPhone>();
                if (whatsappCloudApiContactObject.Phones != null)
                {
                    foreach (var p in whatsappCloudApiContactObject.Phones)
                    {
                        var phone = new ContactMessageObjectPhone(p.Phone, p.Type, p.WaId);
                        phones.Add(phone);
                    }
                }

                var urls = new List<ContactMessageObjectUrl>();
                if (whatsappCloudApiContactObject.Urls != null)
                {
                    foreach (var p in whatsappCloudApiContactObject.Urls)
                    {
                        var url = new ContactMessageObjectUrl(p.Url, p.Type);
                        urls.Add(url);
                    }
                }

                var contactMessageObject = new ContactMessageObject(
                    addresses,
                    whatsappCloudApiContactObject.Birthday,
                    emails,
                    name,
                    org,
                    ims,
                    phones,
                    urls);

                contactsMessageList.Add(contactMessageObject);
            }

            messageBody.ContactsMessage = contactsMessageList;
        }
        else if (extendedMessagePayloadDetail.WhatsappCloudApiLocationObject != null)
        {
            messageType = "location";
            messageBody.LocationMessage = new LocationMessageObject(
                extendedMessagePayloadDetail.WhatsappCloudApiLocationObject.Latitude,
                extendedMessagePayloadDetail.WhatsappCloudApiLocationObject.Longitude,
                extendedMessagePayloadDetail.WhatsappCloudApiLocationObject.Name,
                extendedMessagePayloadDetail.WhatsappCloudApiLocationObject.Address);
        }
        else if (extendedMessagePayloadDetail.WhatsappCloudApiReactionObject != null)
        {
            messageType = "reaction";
            messageBody.ReactionMessage = new ReactionMessageObject(
                extendedMessagePayloadDetail.WhatsappCloudApiReactionObject.MessageId,
                extendedMessagePayloadDetail.WhatsappCloudApiReactionObject.Emoji);
        }
        else if (extendedMessagePayloadDetail.WhatsappCloudApiInteractiveObject != null)
        {
            messageType = "interactive";

            var interactiveMessage =
                MapInteractiveMessageObject(extendedMessagePayloadDetail.WhatsappCloudApiInteractiveObject);

            messageBody.InteractiveMessage = interactiveMessage;
        }
        else if (extendedMessagePayloadDetail.WhatsappCloudApiTemplateMessageObject != null)
        {
            messageType = "template";

            var templateMessageObject = new TemplateMessageObject
            {
                TemplateName = extendedMessagePayloadDetail.WhatsappCloudApiTemplateMessageObject.TemplateName,
                Language = extendedMessagePayloadDetail.WhatsappCloudApiTemplateMessageObject.Language,
            };

            if (extendedMessagePayloadDetail.WhatsappCloudApiTemplateMessageObject.Components != null)
            {
                var components = new List<TemplateMessageObjectComponent>();

                foreach (var c in extendedMessagePayloadDetail.WhatsappCloudApiTemplateMessageObject.Components)
                {
                    var component = new TemplateMessageObjectComponent(
                        c.Type,
                        c.SubType,
                        c.Index ?? 0
                    );

                    if (c.Parameters != null)
                    {
                        var parameters = new List<TemplateMessageObjectComponentParameter>();

                        foreach (var p in c.Parameters)
                        {
                            var parameter =
                                new TemplateMessageObjectComponentParameter
                                {
                                    Type = p.Type, Text = p.Text, Payload = p.Payload,
                                };

                            if (p.Image != null)
                            {
                                var imageMessageObject = new ImageMessageObject
                                {
                                    Id = p.Image.Id,
                                    Link = p.Image.Link,
                                    Caption = p.Image.Caption,
                                    Filename = p.Image.Filename
                                };

                                if (p.Image.Provider != null)
                                {
                                    var provider = new MediaMessageObjectProvider
                                    {
                                        Name = p.Image.Provider.Name, Type = p.Image.Provider.Type
                                    };

                                    if (p.Image.Provider.Config != null)
                                    {
                                        var config = new MediaMessageObjectProviderConfig();

                                        if (p.Image.Provider.Config.Basic != null)
                                        {
                                            var basic = new MediaMessageObjectProviderConfigBasic()
                                            {
                                                Username = p.Image.Provider.Config.Basic.Username,
                                                Password = p.Image.Provider.Config.Basic.Password,
                                            };

                                            config.Basic = basic;
                                        }

                                        if (p.Image.Provider.Config.Bearer != null)
                                        {
                                            var bearer = new MediaMessageObjectProviderConfigBearer
                                            {
                                                Bearer = p.Image.Provider.Config.Bearer.Bearer
                                            };

                                            config.Bearer = bearer;
                                        }
                                    }

                                    imageMessageObject.Provider = provider;
                                }

                                parameter.Image = imageMessageObject;
                            }

                            if (p.Audio != null)
                            {
                                var audioMessageObject = new AudioMessageObject
                                {
                                    Id = p.Audio.Id,
                                    Link = p.Audio.Link,
                                    Caption = p.Audio.Caption,
                                    Filename = p.Audio.Filename
                                };

                                if (p.Audio.Provider != null)
                                {
                                    var provider = new MediaMessageObjectProvider
                                    {
                                        Name = p.Audio.Provider.Name, Type = p.Audio.Provider.Type
                                    };

                                    if (p.Audio.Provider.Config != null)
                                    {
                                        var config = new MediaMessageObjectProviderConfig();

                                        if (p.Audio.Provider.Config.Basic != null)
                                        {
                                            var basic = new MediaMessageObjectProviderConfigBasic()
                                            {
                                                Username = p.Audio.Provider.Config.Basic.Username,
                                                Password = p.Audio.Provider.Config.Basic.Password,
                                            };

                                            config.Basic = basic;
                                        }

                                        if (p.Audio.Provider.Config.Bearer != null)
                                        {
                                            var bearer = new MediaMessageObjectProviderConfigBearer
                                            {
                                                Bearer = p.Audio.Provider.Config.Bearer.Bearer
                                            };

                                            config.Bearer = bearer;
                                        }
                                    }

                                    audioMessageObject.Provider = provider;
                                }

                                parameter.Audio = audioMessageObject;
                            }

                            if (p.Document != null)
                            {
                                var documentMessageObject = new DocumentMessageObject
                                {
                                    Id = p.Document.Id,
                                    Link = p.Document.Link,
                                    Caption = p.Document.Caption,
                                    Filename = p.Document.Filename
                                };

                                if (p.Document.Provider != null)
                                {
                                    var provider = new MediaMessageObjectProvider
                                    {
                                        Name = p.Document.Provider.Name, Type = p.Document.Provider.Type
                                    };

                                    if (p.Document.Provider.Config != null)
                                    {
                                        var config = new MediaMessageObjectProviderConfig();

                                        if (p.Document.Provider.Config.Basic != null)
                                        {
                                            var basic = new MediaMessageObjectProviderConfigBasic()
                                            {
                                                Username = p.Document.Provider.Config.Basic.Username,
                                                Password = p.Document.Provider.Config.Basic.Password,
                                            };

                                            config.Basic = basic;
                                        }

                                        if (p.Document.Provider.Config.Bearer != null)
                                        {
                                            var bearer = new MediaMessageObjectProviderConfigBearer
                                            {
                                                Bearer = p.Document.Provider.Config.Bearer.Bearer
                                            };

                                            config.Bearer = bearer;
                                        }
                                    }

                                    documentMessageObject.Provider = provider;
                                }

                                parameter.Document = documentMessageObject;
                            }

                            if (p.Video != null)
                            {
                                var videoMessageObject = new VideoMessageObject
                                {
                                    Id = p.Video.Id,
                                    Link = p.Video.Link,
                                    Caption = p.Video.Caption,
                                    Filename = p.Video.Filename
                                };

                                if (p.Video.Provider != null)
                                {
                                    var provider = new MediaMessageObjectProvider
                                    {
                                        Name = p.Video.Provider.Name, Type = p.Video.Provider.Type
                                    };

                                    if (p.Video.Provider.Config != null)
                                    {
                                        var config = new MediaMessageObjectProviderConfig();

                                        if (p.Video.Provider.Config.Basic != null)
                                        {
                                            var basic = new MediaMessageObjectProviderConfigBasic()
                                            {
                                                Username = p.Video.Provider.Config.Basic.Username,
                                                Password = p.Video.Provider.Config.Basic.Password,
                                            };

                                            config.Basic = basic;
                                        }

                                        if (p.Video.Provider.Config.Bearer != null)
                                        {
                                            var bearer = new MediaMessageObjectProviderConfigBearer
                                            {
                                                Bearer = p.Video.Provider.Config.Bearer.Bearer
                                            };

                                            config.Bearer = bearer;
                                        }
                                    }

                                    videoMessageObject.Provider = provider;
                                }

                                parameter.Video = videoMessageObject;
                            }

                            if (p.Location != null)
                            {
                                parameter.Location = new LocationMessageObject()
                                {
                                    Longitude = p.Location.Longitude,
                                    Latitude = p.Location.Latitude,
                                    Name = p.Location.Name,
                                    Address = p.Location.Address,
                                };
                            }

                            if (p.DateTime != null)
                            {
                                parameter.DateTime = new DateTimeMessageObject()
                                {
                                    FallbackValue = p.DateTime.FallbackValue,
                                };
                            }

                            if (p.Action != null)
                            {
                                parameter.Action = new ActionMessageObject(p.Action.FlowToken)
                                {
                                    FlowActionData = p.Action.FlowActionData != null
                                        ? JsonConvert.DeserializeObject<Dictionary<string, object>>(
                                            JsonConvert.SerializeObject(p.Action.FlowActionData))
                                        : null
                                };
                            }

                            parameters.Add(parameter);
                        }

                        component.Parameters = parameters;
                    }

                    components.Add(component);
                }

                templateMessageObject.Components = components;
            }

            messageBody.TemplateMessage = templateMessageObject;
        }
        else if (extendedMessagePayloadDetail.WhatsappCloudApiOrderObject != null)
        {
            messageType = "order";
            messageBody = null;
        }

        // TODO
        // public WhatsappCloudApiWebhookInteractiveReplyObject WhatsappCloudApiInteractiveReply { get; set; }
        // public WhatsappCloudApiWebhookTemplateButtonReplyObject WhatsappCloudApiTemplateButtonReply { get; set; }
        // public WhatsappCloudApiWebhookLocationObject WhatsappCloudApiLocationReply { get; set; }
        // public List<WhatsappCloudApiContactObject> WhatsappCloudApiContactsReply { get; set; }
        // public WhatsappCloudApiWebhookOrderObject WhatsappCloudApiOrderReply { get; set; }
        // public WhatsappCloudApiReactionObject WhatsappCloudApiReactionReply { get; set; }
        else if (extendedMessagePayloadDetail.WhatsappCloudApiInteractiveReply != null)
        {
            switch (extendedMessagePayloadDetail.WhatsappCloudApiInteractiveReply.Type)
            {
                case WhatsappCloudApiInteractiveReplyTypeConst.nfm_reply:
                    messageBody.InteractiveReplyMessage = new InteractiveReplyMessageObject()
                    {
                        Type = WhatsappCloudApiInteractiveReplyTypeConst.nfm_reply,
                        NfmReply = new NfmReplyMessageObject()
                        {
                            Name = extendedMessagePayloadDetail.WhatsappCloudApiInteractiveReply.NfmReply.Name,
                            Body = extendedMessagePayloadDetail.WhatsappCloudApiInteractiveReply.NfmReply.Body,
                            ResponseJson = extendedMessagePayloadDetail.WhatsappCloudApiInteractiveReply.NfmReply.ResponseJson
                        }
                    };
                    break;
                case WhatsappCloudApiInteractiveReplyTypeConst.button_reply:
                    messageBody.InteractiveReplyMessage = new InteractiveReplyMessageObject()
                    {
                        Type = WhatsappCloudApiInteractiveReplyTypeConst.button_reply,
                        ButtonReply = new ButtonReplyMessageObject()
                        {
                            Id = extendedMessagePayloadDetail.WhatsappCloudApiInteractiveReply.ButtonReply.Id,
                            Title = extendedMessagePayloadDetail.WhatsappCloudApiInteractiveReply.ButtonReply.Title
                        }
                    };
                    break;
                case WhatsappCloudApiInteractiveReplyTypeConst.list_reply:
                    messageBody.InteractiveReplyMessage = new InteractiveReplyMessageObject()
                    {
                        Type = WhatsappCloudApiInteractiveReplyTypeConst.list_reply,
                        ListReply = new ListReplyMessageObject()
                        {
                            Id = extendedMessagePayloadDetail.WhatsappCloudApiInteractiveReply.ListReply.Id,
                            Title = extendedMessagePayloadDetail.WhatsappCloudApiInteractiveReply.ListReply.Title,
                            Description = extendedMessagePayloadDetail.WhatsappCloudApiInteractiveReply.ListReply.Description
                        }
                    };
                    break;
            }

            messageType = "interactive_reply";
        }
        else if (extendedMessagePayloadDetail.WhatsappCloudApiTemplateButtonReply != null)
        {
            messageType = "template_reply";
            messageBody = null;
        }
        else if (extendedMessagePayloadDetail.WhatsappCloudApiLocationReply != null)
        {
            messageType = "location_reply";
            messageBody = null;
        }
        else if (extendedMessagePayloadDetail.WhatsappCloudApiContactsReply != null)
        {
            messageType = "contacts_reply";
            messageBody = null;
        }
        else if (extendedMessagePayloadDetail.WhatsappCloudApiOrderReply != null)
        {
            messageType = "order_reply";
            messageBody = null;
        }
        else if (extendedMessagePayloadDetail.WhatsappCloudApiReactionReply != null)
        {
            messageType = "reaction_reply";
            messageBody = null;
        }
        else if (extendedMessagePayloadDetail.WhatsappCloudApiReferral != null)
        {
            messageType = "referral";
            messageBody = null;
        }

        else
        {
            throw new ArgumentOutOfRangeException(nameof(ConversationMessage));
        }


        return (messageType, messageBody);
    }

    private static InteractiveMessageObject MapInteractiveMessageObject(WhatsappCloudApiInteractiveObject srcPayload)
    {
        var interactiveMessage = new InteractiveMessageObject
        {
            Type = srcPayload.Type,
        };

        if (srcPayload.Header != null)
        {
            var interactiveMessageObjectHeader = new InteractiveMessageObjectHeader
            {
                Type = srcPayload.Header.Type, Text = srcPayload.Header.Text,
            };

            if (srcPayload.Header.Video != null)
            {
                var videoMessageObject = new VideoMessageObject
                {
                    Id = srcPayload.Header.Video.Id,
                    Link = srcPayload.Header.Video.Link,
                    Caption = srcPayload.Header.Video.Caption,
                    Filename = srcPayload.Header.Video.Filename
                };

                if (srcPayload.Header.Video.Provider != null)
                {
                    var provider = new MediaMessageObjectProvider
                    {
                        Name = srcPayload.Header.Video.Provider.Name, Type = srcPayload.Header.Video.Provider.Type
                    };

                    if (srcPayload.Header.Video.Provider.Config != null)
                    {
                        var config = new MediaMessageObjectProviderConfig();

                        if (srcPayload.Header.Video.Provider.Config.Basic != null)
                        {
                            var basic = new MediaMessageObjectProviderConfigBasic()
                            {
                                Username = srcPayload.Header.Video.Provider.Config.Basic.Username,
                                Password = srcPayload.Header.Video.Provider.Config.Basic.Password,
                            };

                            config.Basic = basic;
                        }

                        if (srcPayload.Header.Video.Provider.Config.Bearer != null)
                        {
                            var bearer = new MediaMessageObjectProviderConfigBearer
                            {
                                Bearer = srcPayload.Header.Video.Provider.Config.Bearer.Bearer
                            };

                            config.Bearer = bearer;
                        }
                    }

                    videoMessageObject.Provider = provider;
                }

                interactiveMessageObjectHeader.Video = videoMessageObject;
            }

            if (srcPayload.Header.Image != null)
            {
                var imageMessageObject = new ImageMessageObject
                {
                    Id = srcPayload.Header.Image.Id,
                    Link = srcPayload.Header.Image.Link,
                    Caption = srcPayload.Header.Image.Caption,
                    Filename = srcPayload.Header.Image.Filename
                };

                if (srcPayload.Header.Image.Provider != null)
                {
                    var provider = new MediaMessageObjectProvider
                    {
                        Name = srcPayload.Header.Image.Provider.Name, Type = srcPayload.Header.Image.Provider.Type
                    };

                    if (srcPayload.Header.Image.Provider.Config != null)
                    {
                        var config = new MediaMessageObjectProviderConfig();

                        if (srcPayload.Header.Image.Provider.Config.Basic != null)
                        {
                            var basic = new MediaMessageObjectProviderConfigBasic()
                            {
                                Username = srcPayload.Header.Image.Provider.Config.Basic.Username,
                                Password = srcPayload.Header.Image.Provider.Config.Basic.Password,
                            };

                            config.Basic = basic;
                        }

                        if (srcPayload.Header.Image.Provider.Config.Bearer != null)
                        {
                            var bearer = new MediaMessageObjectProviderConfigBearer
                            {
                                Bearer = srcPayload.Header.Image.Provider.Config.Bearer.Bearer
                            };

                            config.Bearer = bearer;
                        }
                    }

                    imageMessageObject.Provider = provider;
                }

                interactiveMessageObjectHeader.Image = imageMessageObject;
            }

            if (srcPayload.Header.Document != null)
            {
                var documentMessageObject = new DocumentMessageObject
                {
                    Id = srcPayload.Header.Document.Id,
                    Link = srcPayload.Header.Document.Link,
                    Caption = srcPayload.Header.Document.Caption,
                    Filename = srcPayload.Header.Document.Filename
                };

                if (srcPayload.Header.Document.Provider != null)
                {
                    var provider = new MediaMessageObjectProvider
                    {
                        Name = srcPayload.Header.Document.Provider.Name, Type = srcPayload.Header.Document.Provider.Type
                    };

                    if (srcPayload.Header.Document.Provider.Config != null)
                    {
                        var config = new MediaMessageObjectProviderConfig();

                        if (srcPayload.Header.Document.Provider.Config.Basic != null)
                        {
                            var basic = new MediaMessageObjectProviderConfigBasic()
                            {
                                Username = srcPayload.Header.Document.Provider.Config.Basic.Username,
                                Password = srcPayload.Header.Document.Provider.Config.Basic.Password,
                            };

                            config.Basic = basic;
                        }

                        if (srcPayload.Header.Document.Provider.Config.Bearer != null)
                        {
                            var bearer = new MediaMessageObjectProviderConfigBearer
                            {
                                Bearer = srcPayload.Header.Document.Provider.Config.Bearer.Bearer
                            };

                            config.Bearer = bearer;
                        }
                    }

                    documentMessageObject.Provider = provider;
                }

                interactiveMessageObjectHeader.Document = documentMessageObject;
            }
        }

        if (srcPayload.Body != null)
        {
            interactiveMessage.Body = new InteractiveMessageObjectBody
            {
                Text = srcPayload.Body.Text
            };
        }

        if (srcPayload.Footer != null)
        {
            interactiveMessage.Footer = new InteractiveMessageObjectFooter
            {
                Text = srcPayload.Footer.Text
            };
        }

        if (srcPayload.Action != null)
        {
            var interactiveAction = new InteractiveMessageObjectAction
            {
                Button = srcPayload.Action.Button,
                CatalogId = srcPayload.Action.CatalogId,
                ProductRetailerId =
                    srcPayload
                        .Action
                        .ProductRetailerId
            };

            if (srcPayload.Action.Buttons != null)
            {
                var actionButtons = new List<InteractiveMessageObjectActionButton>();
                foreach (var button in srcPayload.Action
                             .Buttons)
                {
                    var actionButton = new InteractiveMessageObjectActionButton
                    {
                        Type = button.Type
                    };
                    if (button.Reply != null)
                    {
                        var actionButtonReply = new InteractiveMessageObjectActionButtonReply
                        {
                            Id = button.Reply.Id, Title = button.Reply.Title
                        };
                        actionButton.Reply = actionButtonReply;
                    }

                    actionButtons.Add(actionButton);
                }

                interactiveAction.Buttons = actionButtons;
            }

            if (srcPayload.Action.Sections != null)
            {
                var actionSections = new List<InteractiveMessageObjectActionSection>();
                foreach (var section in srcPayload
                             .Action
                             .Sections)
                {
                    var actionSection = new InteractiveMessageObjectActionSection
                    {
                        Title = section.Title
                    };

                    if (section.ProductItems != null)
                    {
                        var sectionProductItems = new List<InteractiveMessageObjectActionSectionProductItem>();
                        foreach (var productItem in section.ProductItems)
                        {
                            var sectionProductItem = new InteractiveMessageObjectActionSectionProductItem
                            {
                                ProductRetailerId = productItem.ProductRetailerId
                            };
                            sectionProductItems.Add(sectionProductItem);
                        }

                        actionSection.ProductItems = sectionProductItems;
                    }

                    if (section.Rows != null)
                    {
                        var sectionRows = new List<InteractiveMessageObjectActionSectionRow>();
                        foreach (var row in section.Rows)
                        {
                            var sectionRow = new InteractiveMessageObjectActionSectionRow
                            {
                                Id = row.Id, Title = row.Title, Description = row.Description
                            };
                            sectionRows.Add(sectionRow);
                        }

                        actionSection.Rows = sectionRows;
                    }

                    actionSections.Add(actionSection);
                }

                interactiveAction.Sections = actionSections;
            }

            interactiveMessage.Action = interactiveAction;
        }

        return interactiveMessage;
    }

    // ReSharper restore ConstantConditionalAccessQualifier
    // ReSharper restore ConditionIsAlwaysTrueOrFalse
}