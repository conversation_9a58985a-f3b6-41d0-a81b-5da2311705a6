﻿using System;
using System.Collections.Generic;
using System.Linq;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.Constants;
using Travis_backend.InternalDomain.Models;
using Travis_backend.InternalDomain.ViewModels;

namespace Travis_backend.Helpers
{
    public static class CurrencyConverter
    {
        public static decimal ConvertToUsd(decimal amount, string currency)
        {
            if (string.IsNullOrWhiteSpace(currency))
            {
                return amount;
            }

            return currency.ToLower() switch
            {
                "hkd" => amount / 7.85M,
                "sgd" => amount / 1.34M,
                "myr" => amount / 4.38M,
                "cny" => amount / 7.05M,
                "idr" => amount / 15000M,
                "eur" => amount / 1.00M,
                "gbp" => amount / 0.81M,
                "cad" => amount / 1.26M,
                "aud" => amount / 1.4M,
                "aed" => amount / 3.68M,
                "brl" => amount / 5.15M,
                "inr" => amount / 81.96M,
                _ => amount
            };
        }
    }

    public static class BillRecordRevenueCalculator
    {
        public static List<CmsDailyRevenueAnalyticDto> GetDailyAnalytics<T>(
            List<T> companies,
            DateTime start,
            DateTime end) where T : CmsCompanyAnalyticDto
        {
            var result = new List<CmsDailyRevenueAnalyticDto>();

            for (var date = start; date <= end; date = date.Add(TimeSpan.FromDays(1)))
            {
                var subscriptionPlanDistribution = ValidSubscriptionPlan.SubscriptionPlan.Select(
                    x => new CmsPlanDistributionDto()
                    {
                        PlanId = x
                    }).ToList();
                var addOnPlanDistribution = ValidSubscriptionPlan.CmsMrrAllAddOnWithoutRevenuePlan.Select(
                    x => new CmsPlanDistributionDto()
                    {
                        PlanId = x
                    }).ToList();

                var dailyAnalytic = new CmsDailyRevenueAnalyticDto()
                {
                    Date = date.ToString("yyyy-MM-dd"),
                    MonthlyRecurringRevenue = 0,
                    CompanyRevenueBreakDowns = new List<CmsCompanyRevenueBreakDownDto>()
                };

                companies.ForEach(
                    company =>
                    {
                        try
                        {
                            var revenueDetail = GetDetailedMonthlyRecurringRevenue(company.BillRecords, date);
                            var dailyRevenue = GetDailyRevenue(company.BillRecords, date);

                            if (revenueDetail == null)
                            {
                                return;
                            }

                            if (revenueDetail.MonthlyRecurringRevenue > 0)
                            {
                                dailyAnalytic.CompanyRevenueBreakDowns.Add(
                                    new CmsCompanyRevenueBreakDownDto()
                                    {
                                        CompanyId = company.Id,
                                        CompanyName = company.CompanyName,
                                        SubscriptionPlanId = revenueDetail.SubscriptionPlanRevenue?.PlanId,
                                        MonthlyRecurringRevenue = revenueDetail.MonthlyRecurringRevenue,
                                    });
                            }

                            var subscriptionDistribution = subscriptionPlanDistribution.FirstOrDefault(
                                planDistribution =>
                                    planDistribution.PlanId == revenueDetail.SubscriptionPlanRevenue?.PlanId);

                            if (subscriptionDistribution != null)
                            {
                                subscriptionDistribution.Count++;
                                subscriptionDistribution.MonthlyRecurringRevenue +=
                                    revenueDetail.SubscriptionPlanRevenue.MonthlyRecurringRevenue;
                            }

                            revenueDetail.AddOnRevenues.ForEach(
                                addOnPlanRevenue =>
                                {
                                    if (addOnPlanRevenue == null)
                                    {
                                        return;
                                    }

                                    var addOnDistribution = addOnPlanDistribution.FirstOrDefault(
                                        planDistribution => planDistribution.PlanId == addOnPlanRevenue.PlanId);

                                    if (addOnDistribution != null)
                                    {
                                        addOnDistribution.Count++;
                                        addOnDistribution.MonthlyRecurringRevenue +=
                                            addOnPlanRevenue.MonthlyRecurringRevenue;
                                    }
                                });

                            dailyAnalytic.MonthlyRecurringRevenue += revenueDetail.MonthlyRecurringRevenue;
                            dailyAnalytic.DailyRevenue += dailyRevenue.TotalRevenue;
                            dailyAnalytic.SubscriptionPlanRevenue += dailyRevenue.SubscriptionPlanRevenue;
                            dailyAnalytic.OneTimeSetupFeeRevenue += dailyRevenue.OneTimeSetupFeeRevenue;
                            dailyAnalytic.OneTimeSetupFeeRevenueBasedOnPaidAt +=
                                dailyRevenue.OneTimeSetupFeeRevenueBasedOnPaidAt;
                            dailyAnalytic.MarkupRevenue += dailyRevenue.MarkupRevenue;
                        }
                        catch (Exception e)
                        {
                            Console.WriteLine("CMS Analytic Error Company Id: " + company.Id);
                            Console.WriteLine(e.Message);
                        }
                    });

                dailyAnalytic.SubscriptionPlanDistribution =
                    subscriptionPlanDistribution.Where(x => x.Count > 0).ToList();
                dailyAnalytic.AddOnPlanDistribution = addOnPlanDistribution.Where(x => x.Count > 0).ToList();

                result.Add(dailyAnalytic);
            }

            return result;
        }

        public static List<CmsDailyRevenueDto> GetDailyRevenues<T>(
            List<T> companies,
            DateTime start,
            DateTime end)
            where T : CmsCompanyAnalyticDto
        {
            var result = new List<CmsDailyRevenueDto>();

            for (var date = start; date <= end; date = date.Add(TimeSpan.FromDays(1)))
            {
                var dailyRevenue = new CmsDailyRevenueDto
                {
                    Date = date.ToString("yyyy-MM-dd"),
                };

                companies.ForEach(
                    company =>
                    {
                        try
                        {
                            dailyRevenue.DailyRevenue = GetDailyRevenue(company.BillRecords, date).SubscriptionPlanRevenue;
                        }
                        catch (Exception e)
                        {
                            Console.WriteLine("CMS GetDailyRevenues Error Company Id: " + company.Id);
                            Console.WriteLine(e.Message);
                        }
                    });

                result.Add(dailyRevenue);
            }

            return result;
        }

        public static List<CmsDailyAccruedRevenueAnalyticDto> GetDailyAccruedAnalytics(
            List<CmsCompanyAnalyticDto> companies,
            List<CmsSleekPayCompanyAnalyticDto> sleekPayCompanies,
            List<CmsWhatsApp360DialogUsageTopUpTransactionLogDto> whatsapp360DialogTopUpLogs,
            List<CmsTwilioTopUpLogDto> twilioTopUpLogs,
            List<CmsWhatsappCloudApiTopUpDto> whatsappCloudApiTopUps,
            DateTime start,
            DateTime end)
        {
            var result = new List<CmsDailyAccruedRevenueAnalyticDto>();

            for (var date = start; date <= end; date = date.Add(TimeSpan.FromDays(1)))
            {
                var dailyAnalytic = new CmsDailyAccruedRevenueAnalyticDto()
                {
                    Date = date.ToString("yyyy-MM-dd"),
                    CmsAccruedRevenueBreakDowns = new List<CmsAccruedRevenueBreakDownDto>()
                };

                companies.ForEach(
                    company =>
                    {
                        try
                        {
                            var dailyRevenue =
                                GetDailyAccruedSubscriptionAndOneTimeSetupRevenue(company.BillRecords, date);

                            if (dailyRevenue.SubscriptionPlanRevenue > 0)
                            {
                                dailyAnalytic.CmsAccruedRevenueBreakDowns.Add(
                                    new CmsAccruedRevenueBreakDownDto()
                                    {
                                        CompanyId = company.Id,
                                        CompanyName = company.CompanyName,
                                        Type = "Subscription",
                                        DailyAccruedRevenue = dailyRevenue.SubscriptionPlanRevenue
                                    });

                                dailyAnalytic.SubscriptionPlanRevenue += dailyRevenue.SubscriptionPlanRevenue;
                            }

                            if (dailyRevenue.OneTimeSetupFeeRevenue > 0)
                            {
                                dailyAnalytic.CmsAccruedRevenueBreakDowns.Add(
                                    new CmsAccruedRevenueBreakDownDto()
                                    {
                                        CompanyId = company.Id,
                                        CompanyName = company.CompanyName,
                                        Type = "One-Time Revenue",
                                        DailyAccruedRevenue = dailyRevenue.OneTimeSetupFeeRevenue
                                    });

                                dailyAnalytic.OneTimeSetupFeeRevenue += dailyRevenue.OneTimeSetupFeeRevenue;
                            }
                        }
                        catch (Exception e)
                        {
                            Console.WriteLine(
                                "CMS Accrued Analytic Subscription & One-Time Revenue Error Company Id: " + company.Id);
                            Console.WriteLine(e.Message);
                        }
                    });

                sleekPayCompanies.ForEach(
                    company =>
                    {
                        try
                        {
                            var sleekPayDailyRevenue = GetDailyAccruedSleekPayRevenue(
                                company.CmsSleekPayReportDatas,
                                date);

                            if (sleekPayDailyRevenue != decimal.MinValue)
                            {
                                dailyAnalytic.CmsAccruedRevenueBreakDowns.Add(
                                    new CmsAccruedRevenueBreakDownDto()
                                    {
                                        CompanyId = company.CompanyId,
                                        CompanyName = company.CompanyName,
                                        Type = "SleekPay",
                                        DailyAccruedRevenue = sleekPayDailyRevenue
                                    });

                                dailyAnalytic.SleekPayRevenue += sleekPayDailyRevenue;
                            }
                        }
                        catch (Exception e)
                        {
                            Console.WriteLine("CMS Accrued Analytic SleekPay Error Company Id: " + company.CompanyId);
                            Console.WriteLine(e.Message);
                        }
                    });

                var removeCount = 0;

                foreach (var topUp in whatsapp360DialogTopUpLogs)
                {
                    try
                    {
                        if (topUp.CreatedAt.Date > date.Date)
                        {
                            break;
                        }

                        var whatsapp360DialogDailyRevenue = CurrencyConverter.ConvertToUsd(
                            topUp.Total,
                            topUp.Currency);

                        var existWhatsapp360DialogTopUpRevenueBreakDownDto
                            = dailyAnalytic.CmsAccruedRevenueBreakDowns
                                .FirstOrDefault(
                                    bd => bd.CompanyId == topUp.CompanyId
                                          && bd.Type == "Top-up"
                                          && bd.TopUpType == "360 Dialog");

                        if (existWhatsapp360DialogTopUpRevenueBreakDownDto != null)
                        {
                            existWhatsapp360DialogTopUpRevenueBreakDownDto.DailyAccruedRevenue +=
                                whatsapp360DialogDailyRevenue;
                        }
                        else
                        {
                            dailyAnalytic.CmsAccruedRevenueBreakDowns.Add(
                                new CmsAccruedRevenueBreakDownDto()
                                {
                                    CompanyId = topUp.CompanyId,
                                    CompanyName = topUp.CompanyName,
                                    Type = "Top-up",
                                    TopUpType = "360 Dialog",
                                    DailyAccruedRevenue = whatsapp360DialogDailyRevenue
                                });
                        }

                        dailyAnalytic.TopUpRevenue += whatsapp360DialogDailyRevenue;

                        removeCount++;
                    }
                    catch (Exception e)
                    {
                        Console.WriteLine("CMS Accrued Analytic 360 Dialog Error Company Id: " + topUp.CompanyId);
                        Console.WriteLine(e.Message);
                    }
                }

                if (removeCount > 0)
                {
                    whatsapp360DialogTopUpLogs.RemoveRange(0, removeCount);
                }

                removeCount = 0;

                foreach (var topUp in twilioTopUpLogs)
                {
                    try
                    {
                        if (topUp.CreatedAt.Date > date.Date)
                        {
                            break;
                        }

                        var twilioDailyRevenue = CurrencyConverter.ConvertToUsd(
                            topUp.TopUpAmount,
                            topUp.Currency);

                        var existTwilioTopUpRevenueBreakDownDto
                            = dailyAnalytic.CmsAccruedRevenueBreakDowns
                                .FirstOrDefault(
                                    bd => bd.CompanyId == topUp.CompanyId
                                          && bd.Type == "Top-up"
                                          && bd.TopUpType == "Twilio");

                        if (existTwilioTopUpRevenueBreakDownDto != null)
                        {
                            existTwilioTopUpRevenueBreakDownDto.DailyAccruedRevenue +=
                                twilioDailyRevenue;
                        }
                        else
                        {
                            dailyAnalytic.CmsAccruedRevenueBreakDowns.Add(
                                new CmsAccruedRevenueBreakDownDto()
                                {
                                    CompanyId = topUp.CompanyId,
                                    CompanyName = topUp.CompanyName,
                                    Type = "Top-up",
                                    TopUpType = "Twilio",
                                    DailyAccruedRevenue = twilioDailyRevenue
                                });
                        }

                        dailyAnalytic.TopUpRevenue += twilioDailyRevenue;

                        removeCount++;
                    }
                    catch (Exception e)
                    {
                        Console.WriteLine("CMS Accrued Analytic Twilio Error Company Id: " + topUp.CompanyId);
                        Console.WriteLine(e.Message);
                    }
                }

                if (removeCount > 0)
                {
                    twilioTopUpLogs.RemoveRange(0, removeCount);
                }

                removeCount = 0;

                foreach (var topUp in whatsappCloudApiTopUps)
                {
                    try
                    {
                        if (topUp.CreatedAt.Date > date.Date)
                        {
                            break;
                        }

                        var whatsappCloudApiDailyRevenue = CurrencyConverter.ConvertToUsd(
                            topUp.CreditAmount,
                            topUp.Currency);

                        var existWhatsappCloudApiTopUpRevenueBreakDownDto
                            = dailyAnalytic.CmsAccruedRevenueBreakDowns
                                .FirstOrDefault(
                                    bd => bd.FacebookBusinessId == topUp.FacebookBusinessId
                                          && bd.Type == "Top-up"
                                          && bd.TopUpType == "WhatsApp Cloud Api");

                        if (existWhatsappCloudApiTopUpRevenueBreakDownDto != null)
                        {
                            existWhatsappCloudApiTopUpRevenueBreakDownDto.DailyAccruedRevenue +=
                                whatsappCloudApiDailyRevenue;
                        }
                        else
                        {
                            dailyAnalytic.CmsAccruedRevenueBreakDowns.Add(
                                new CmsAccruedRevenueBreakDownDto()
                                {
                                    FacebookBusinessId = topUp.FacebookBusinessId,
                                    FacebookBusinessName = topUp.FacebookBusinessName,
                                    Type = "Top-up",
                                    TopUpType = "WhatsApp Cloud Api",
                                    DailyAccruedRevenue = whatsappCloudApiDailyRevenue,
                                    SleekFlowCompanyIds = topUp.SleekFlowCompanyIds,
                                    SleekFlowCompanyNames = topUp.SleekFlowCompanyNames
                                });
                        }

                        dailyAnalytic.TopUpRevenue += whatsappCloudApiDailyRevenue;

                        removeCount++;
                    }
                    catch (Exception e)
                    {
                        Console.WriteLine(
                            "CMS Accrued Analytic WhatsApp Cloud Api Error Facebook Business Id: " +
                            topUp.FacebookBusinessId);
                        Console.WriteLine(e.Message);
                    }
                }

                if (removeCount > 0)
                {
                    whatsappCloudApiTopUps.RemoveRange(0, removeCount);
                }

                result.Add(dailyAnalytic);
            }

            return result;
        }

        public static List<CmsDailyDistributionAnalyticDto> GetDailyDistributionAnalytics<T>(
            List<T> companies,
            DateTime start,
            DateTime end) where T : CmsCompanyAnalyticDto
        {
            var result = new List<CmsDailyDistributionAnalyticDto>();

            for (var date = start; date <= end; date = date.Add(TimeSpan.FromDays(1)))
            {
                var dailyAnalytic = new CmsDailyDistributionAnalyticDto()
                {
                    Date = date.ToString("yyyy-MM-dd"),
                    MonthlyRecurringRevenue = 0,
                    CountryDistribution = new List<CmsAnalyticDistributionDto>(),
                    LeadSourceDistribution = new List<CmsAnalyticDistributionDto>(),
                    IndustryDistribution = new List<CmsAnalyticDistributionDto>(),
                };

                companies.ForEach(
                    company =>
                    {
                        try
                        {
                            var revenueDetail = GetDetailedMonthlyRecurringRevenue(company.BillRecords, date);

                            if (revenueDetail == null)
                            {
                                return;
                            }

                            // Countries
                            var countriesDistribution =
                                dailyAnalytic.CountryDistribution.FirstOrDefault(
                                    d => d.AnalyticSourceName == company.CompanyCountry);

                            if (countriesDistribution == null)
                            {
                                countriesDistribution = new CmsAnalyticDistributionDto()
                                {
                                    AnalyticSourceName = company.CompanyCountry
                                };
                                dailyAnalytic.CountryDistribution.Add(countriesDistribution);
                            }

                            countriesDistribution.Count++;
                            countriesDistribution.MonthlyRecurringRevenue += revenueDetail.MonthlyRecurringRevenue;

                            // Lead Source
                            company.CmsLeadSource = string.IsNullOrEmpty(company.CmsLeadSource)
                                ? "Not Set"
                                : company.CmsLeadSource;
                            var leadSourceDistribution =
                                dailyAnalytic.LeadSourceDistribution.FirstOrDefault(
                                    d => d.AnalyticSourceName == company.CmsLeadSource);

                            if (leadSourceDistribution == null)
                            {
                                leadSourceDistribution = new CmsAnalyticDistributionDto()
                                {
                                    AnalyticSourceName = company.CmsLeadSource
                                };
                                dailyAnalytic.LeadSourceDistribution.Add(leadSourceDistribution);
                            }

                            leadSourceDistribution.Count++;
                            leadSourceDistribution.MonthlyRecurringRevenue += revenueDetail.MonthlyRecurringRevenue;

                            // Industry
                            company.CmsCompanyIndustry = string.IsNullOrEmpty(company.CmsCompanyIndustry)
                                ? "Not Set"
                                : company.CmsCompanyIndustry;
                            var companyIndustryDistribution =
                                dailyAnalytic.IndustryDistribution.FirstOrDefault(
                                    d => d.AnalyticSourceName == company.CmsCompanyIndustry);

                            if (companyIndustryDistribution == null)
                            {
                                companyIndustryDistribution = new CmsAnalyticDistributionDto()
                                {
                                    AnalyticSourceName = company.CmsCompanyIndustry
                                };
                                dailyAnalytic.IndustryDistribution.Add(companyIndustryDistribution);
                            }

                            companyIndustryDistribution.Count++;
                            companyIndustryDistribution.MonthlyRecurringRevenue +=
                                revenueDetail.MonthlyRecurringRevenue;

                            dailyAnalytic.MonthlyRecurringRevenue += revenueDetail.MonthlyRecurringRevenue;
                        }
                        catch (Exception e)
                        {
                            Console.WriteLine("CMS Analytic Error Company Id: " + company.Id);
                            Console.WriteLine(e.Message);
                        }
                    });

                result.Add(dailyAnalytic);
            }

            return result;
        }

        private static decimal CalculateMonthlyRecurringRevenue(
            BillRecord cmsBillingPeriodUsage,
            List<BillRecord> allValidBillingPeriodUsages)
        {
            if (cmsBillingPeriodUsage == null)
            {
                return 0M;
            }

            // Calculate Stripe Pay Amount
            var monthlyRecurringRevenue = CurrencyConverter.ConvertToUsd(
                (decimal) cmsBillingPeriodUsage.PayAmount,
                cmsBillingPeriodUsage.currency);

            // Calculate Cms Payment Input Amount
            monthlyRecurringRevenue += cmsBillingPeriodUsage.CmsSalesPaymentRecords.Sum(
                x => CurrencyConverter.ConvertToUsd(x.SubscriptionFee, x.Currency));

            // monthlyRecurringRevenue += cmsBillingPeriodUsage.CmsSalesPaymentRecords.Sum(x => CurrencyConverter.ConvertToUsd(x.OneTimeSetupFee, x.Currency));

            // To Monthly
            monthlyRecurringRevenue = CalculateFinalMonthlyRecurringRevenue(
                monthlyRecurringRevenue,
                cmsBillingPeriodUsage.PeriodStart,
                cmsBillingPeriodUsage.PeriodEnd);

            // Calculate Up / Down grade Plan (Add)
            // if (cmsBillingPeriodUsage.UpgradeFromBillRecordId.HasValue)
            // {
            //     monthlyRecurringRevenue += CalculateMonthlyRecurringRevenue(allValidBillingPeriodUsages.FirstOrDefault(x => x.Id == cmsBillingPeriodUsage.UpgradeFromBillRecordId), allValidBillingPeriodUsages);
            // }
            //
            // if (cmsBillingPeriodUsage.DowngradeFromBillRecordId.HasValue)
            // {
            //     monthlyRecurringRevenue += CalculateMonthlyRecurringRevenue(allValidBillingPeriodUsages.FirstOrDefault(x => x.Id == cmsBillingPeriodUsage.DowngradeFromBillRecordId), allValidBillingPeriodUsages);
            // }
            return monthlyRecurringRevenue;
        }

        public static List<string> GetPaymentBreakDowns(
            CmsBillRecordDto billingPeriodUsage,
            List<BillRecord> allValidBillingPeriodUsages)
        {
            var inputPaymentBreakDowns = new List<string>();

            if (billingPeriodUsage == null)
            {
                return inputPaymentBreakDowns;
            }

            // Calculate Stripe Pay Amount
            var total = CurrencyConverter.ConvertToUsd(
                (decimal) billingPeriodUsage.PayAmount,
                billingPeriodUsage.Currency);

            // Calculate Cms Payment Input Amount
            var totalInputPaymentSubscriptionFee =
                billingPeriodUsage.CmsSalesPaymentRecords.Sum(
                    x => CurrencyConverter.ConvertToUsd(x.SubscriptionFee, x.Currency));

            // totalInputPaymentSubscriptionFee += billingPeriodUsage.CmsSalesPaymentRecords.Sum(x => CurrencyConverter.ConvertToUsd(x.OneTimeSetupFee, x.Currency));
            inputPaymentBreakDowns.AddRange(
                billingPeriodUsage.CmsSalesPaymentRecords
                    .Where(x => x.SubscriptionFee != 0)
                    .Select(
                        x =>
                            $"Input Payment Subscription Fee: ${Math.Round(x.SubscriptionFee, 2)} {x.Currency.ToUpper()}"));

            // inputPaymentBreakDowns.AddRange(cmsBillingPeriodUsage.CmsSalesPaymentRecords
            //     .Where(x => x.OneTimeSetupFee != 0)
            //     .Select(x => $"Input Payment One Time Setup Fee: ${x.OneTimeSetupFee} {x.Currency.ToUpper()}"));
            var previousBillRecordTotalPrice = 0M;

            if (billingPeriodUsage.UpgradeFromBillRecordId.HasValue)
            {
                previousBillRecordTotalPrice = CalculateMonthlyRecurringRevenue(
                    allValidBillingPeriodUsages.FirstOrDefault(x => x.Id == billingPeriodUsage.UpgradeFromBillRecordId),
                    allValidBillingPeriodUsages);
            }
            else if (billingPeriodUsage.DowngradeFromBillRecordId.HasValue)
            {
                previousBillRecordTotalPrice = CalculateMonthlyRecurringRevenue(
                    allValidBillingPeriodUsages.FirstOrDefault(
                        x => x.Id == billingPeriodUsage.DowngradeFromBillRecordId),
                    allValidBillingPeriodUsages);
            }

            if (previousBillRecordTotalPrice != 0)
            {
                inputPaymentBreakDowns.Add(
                    $"Previous Bill Remaining Payment MRR: ${Math.Round(previousBillRecordTotalPrice, 2)} USD");
            }

            total += totalInputPaymentSubscriptionFee;
            total += previousBillRecordTotalPrice;

            inputPaymentBreakDowns.Insert(0, $"Total: $ {Math.Round(total, 2)} USD");

            return inputPaymentBreakDowns;
        }

        public static decimal SumMonthlyRecurringRevenue(
            List<BillRecord> cmsBillingPeriodUsages,
            DateTime validDate = default)
        {
            decimal mrr = 0;

            if (validDate == default)
            {
                validDate = DateTime.UtcNow;
            }

            cmsBillingPeriodUsages = cmsBillingPeriodUsages
                .Where(x => x.PeriodStart.Date <= validDate && x.PeriodEnd.Date >= validDate).ToList();

            var currentSubscriptionBillRecord = cmsBillingPeriodUsages.Where(
                    b => ValidSubscriptionPlan.SubscriptionPlan.Contains(b.SubscriptionPlanId))
                .OrderByDescending(br => br.created)
                .ThenByDescending(br => br.PayAmount)
                .FirstOrDefault();

            // Sum Subscription Bill Record
            mrr += CalculateMonthlyRecurringRevenue(currentSubscriptionBillRecord, cmsBillingPeriodUsages);

            // Sum All Add-On MRR
            cmsBillingPeriodUsages.Where(
                    b => ValidSubscriptionPlan.CmsMrrAllAddOnWithoutRevenuePlan.Contains(b.SubscriptionPlanId))
                .ToList()
                .ForEach(
                    billRecord => { mrr += CalculateMonthlyRecurringRevenue(billRecord, cmsBillingPeriodUsages); });

            return mrr;
        }

        // public static (decimal, DateTime?, decimal?) SumMonthlyRecurringRevenueWithInitialPaidDay(List<BillRecord> cmsBillingPeriodUsages, DateTime validDate = default)
        // {
        //     var initialPaidDate = GetInitialPaidDateTime(cmsBillingPeriodUsages);
        //     decimal? initialMrr = null;
        //
        //     if (initialPaidDate.HasValue)
        //         initialMrr = SumMonthlyRecurringRevenue(cmsBillingPeriodUsages, initialPaidDate.Value);
        //
        //     var mrr = SumMonthlyRecurringRevenue(cmsBillingPeriodUsages, validDate);
        //
        //     return (mrr, initialPaidDate, initialMrr);
        // }
        public static DateTime? GetInitialPaidDateTime(List<BillRecord> cmsBillingPeriodUsages)
        {
            DateTime? initialPaidDate = null;

            var orderedCmsBillingPeriodUsages = cmsBillingPeriodUsages.OrderBy(x => x.PeriodStart).ToList();

            foreach (var x in orderedCmsBillingPeriodUsages)
            {
                if (x.PayAmount > 0 || x.CmsSalesPaymentRecords != null &&
                    x.CmsSalesPaymentRecords.Any(p => p.SubscriptionFee > 0))
                {
                    initialPaidDate = x.PeriodStart;
                    break;
                }
            }

            return initialPaidDate;
        }

        public static DetailMonthlyRecurringRevenueDto GetDetailedMonthlyRecurringRevenue(
            List<CmsAnalyticBillRecordDto> cmsBillingPeriodUsages,
            DateTime validDate = default)
        {
            var result = new DetailMonthlyRecurringRevenueDto();

            if (validDate == default)
            {
                validDate = DateTime.UtcNow;
            }

            var allCmsBillingPeriodUsages = cmsBillingPeriodUsages
                .Where(x => x.PeriodStart.Date <= validDate && x.PeriodEnd >= validDate.Date).ToList();

            var realValidDate = validDate.Date.AddDays(1);

            if (realValidDate > DateTime.UtcNow)
            {
                realValidDate = DateTime.UtcNow;
            }

            var realValidBillingPeriodUsages = cmsBillingPeriodUsages
                .Where(x => x.PeriodStart <= realValidDate && x.PeriodEnd >= realValidDate).ToList();

            // Fix double count issue
            if (allCmsBillingPeriodUsages.Count > realValidBillingPeriodUsages.Count)
            {
                cmsBillingPeriodUsages = realValidBillingPeriodUsages;
            }
            else
            {
                cmsBillingPeriodUsages = allCmsBillingPeriodUsages;
            }

            if (cmsBillingPeriodUsages.Count == 0)
            {
                return null;
            }

            var currentSubscriptionBillRecord = cmsBillingPeriodUsages.Where(
                    b => ValidSubscriptionPlan.SubscriptionPlan.Contains(b.SubscriptionPlanId))
                .OrderByDescending(br => br.Created)
                .ThenByDescending(br => br.PayAmount)
                .FirstOrDefault();

            // Sum Subscription Bill Record
            var subscriptionRevenue = CalculateMonthlyRecurringRevenue(
                currentSubscriptionBillRecord,
                cmsBillingPeriodUsages);

            // Sum All Add-On MRR
            var addOnRevenues = cmsBillingPeriodUsages
                .Where(b => ValidSubscriptionPlan.CmsMrrAllAddOnWithoutRevenuePlan.Contains(b.SubscriptionPlanId))
                .Select(x => CalculateMonthlyRecurringRevenue(x, cmsBillingPeriodUsages))
                .ToList();

            result.AddOnRevenues = addOnRevenues;
            result.SubscriptionPlanRevenue = subscriptionRevenue;

            result.MonthlyRecurringRevenue = (subscriptionRevenue?.MonthlyRecurringRevenue ?? 0) +
                                             addOnRevenues.Sum(x => x.MonthlyRecurringRevenue);

            return result;
        }

        private static PlanAndRevenueDto CalculateMonthlyRecurringRevenue(
            CmsAnalyticBillRecordDto cmsBillingPeriodUsage,
            List<CmsAnalyticBillRecordDto> allValidBillingPeriodUsages)
        {
            var result = new PlanAndRevenueDto();

            if (cmsBillingPeriodUsage == null)
            {
                return null;
            }

            // Calculate Stripe Pay Amount
            var totalRevenue = CurrencyConverter.ConvertToUsd(
                (decimal) cmsBillingPeriodUsage.PayAmount,
                cmsBillingPeriodUsage.Currency);

            // Calculate Cms Payment Input Amount
            totalRevenue += cmsBillingPeriodUsage.CmsSalesPaymentRecords.Sum(
                x => CurrencyConverter.ConvertToUsd(x.SubscriptionFee, x.Currency));

            // totalRevenue += cmsBillingPeriodUsage.CmsSalesPaymentRecords.Sum(x => CurrencyConverter.ConvertToUsd(x.OneTimeSetupFee, x.Currency));

            // To Monthly
            var monthlyRecurringRevenue = CalculateFinalMonthlyRecurringRevenue(
                totalRevenue,
                cmsBillingPeriodUsage.PeriodStart,
                cmsBillingPeriodUsage.PeriodEnd);

            // Calculate Up / Down grade Plan (Add)
            // if (cmsBillingPeriodUsage.UpgradeFromBillRecordId.HasValue)
            // {
            //     var upgradeMrr = CalculateMonthlyRecurringRevenue(allValidBillingPeriodUsages.FirstOrDefault(x => x.Id == cmsBillingPeriodUsage.UpgradeFromBillRecordId), allValidBillingPeriodUsages);
            //
            //     monthlyRecurringRevenue += upgradeMrr?.MonthlyRecurringRevenue ?? 0;
            // }
            //
            // if (cmsBillingPeriodUsage.DowngradeFromBillRecordId.HasValue)
            // {
            //     var downgradeMrr = CalculateMonthlyRecurringRevenue(allValidBillingPeriodUsages.FirstOrDefault(x => x.Id == cmsBillingPeriodUsage.DowngradeFromBillRecordId), allValidBillingPeriodUsages);
            //     monthlyRecurringRevenue += downgradeMrr?.MonthlyRecurringRevenue ?? 0;
            // }
            result.PlanId = cmsBillingPeriodUsage.SubscriptionPlanId;
            result.MonthlyRecurringRevenue = monthlyRecurringRevenue;

            return result;
        }

        public static RevenueBreakdown GetDailyRevenue(
            List<CmsAnalyticBillRecordDto> cmsBillingPeriodUsages,
            DateTime validDate = default)
        {
            var revenueBreakdown = new RevenueBreakdown();

            if (validDate == default)
            {
                validDate = DateTime.UtcNow;
            }

            cmsBillingPeriodUsages = cmsBillingPeriodUsages
                .Where(x => x.PeriodStart.Date <= validDate && x.PeriodEnd >= validDate.Date).ToList();

            if (cmsBillingPeriodUsages.Count == 0)
            {
                return revenueBreakdown;
            }

            var currentSubscriptionBillRecord = cmsBillingPeriodUsages.Where(
                    b => ValidSubscriptionPlan.SubscriptionPlan.Contains(b.SubscriptionPlanId))
                .OrderByDescending(br => br.Created)
                .ThenByDescending(br => br.PayAmount)
                .FirstOrDefault();

            // Sum Subscription Bill Record
            var subscriptionRevenue = CalculateDailyRevenue(
                currentSubscriptionBillRecord,
                cmsBillingPeriodUsages,
                validDate);

            // Sum All Add-On MRR
            var addOnRevenues = cmsBillingPeriodUsages
                .Where(b => ValidSubscriptionPlan.CmsMrrAllAddOn.Contains(b.SubscriptionPlanId))
                .Select(x => CalculateDailyRevenue(x, cmsBillingPeriodUsages, validDate))
                .ToList();

            // Sum result
            if (subscriptionRevenue != null)
            {
                revenueBreakdown += subscriptionRevenue;
            }

            foreach (var addOnRevenue in addOnRevenues)
            {
                revenueBreakdown += addOnRevenue;
            }

            return revenueBreakdown;
        }

        public static RevenueBreakdown GetDailyAccruedSubscriptionAndOneTimeSetupRevenue(
            List<CmsAnalyticBillRecordDto> cmsBillingPeriodUsages,
            DateTime validDate = default)
        {
            var revenueBreakdown = new RevenueBreakdown();

            if (validDate == default)
            {
                validDate = DateTime.UtcNow;
            }

            // Get required CMS billing period usages
            cmsBillingPeriodUsages = cmsBillingPeriodUsages
                .Where(x => x.PeriodStart.Date <= validDate.Date && x.PeriodEnd.Date >= validDate.Date).ToList();

            if (cmsBillingPeriodUsages.Count == 0)
            {
                return revenueBreakdown;
            }

            var currentSubscriptionBillRecord = cmsBillingPeriodUsages.Where(
                    b => ValidSubscriptionPlan.SubscriptionPlan.Contains(b.SubscriptionPlanId))
                .OrderByDescending(br => br.Created)
                .ThenByDescending(br => br.PayAmount)
                .FirstOrDefault();

            // Sum Subscription Bill Record
            var subscriptionRevenue = CalculateDailyAccruedRevenue(
                currentSubscriptionBillRecord,
                validDate);

            // Sum All Add-On MRR
            var addOnRevenues = cmsBillingPeriodUsages
                .Where(b => ValidSubscriptionPlan.CmsMrrAllAddOnWithoutRevenuePlan.Contains(b.SubscriptionPlanId))
                .Select(x => CalculateDailyAccruedRevenue(x, validDate))
                .ToList();

            // Sum result
            if (subscriptionRevenue != null)
            {
                revenueBreakdown += subscriptionRevenue;
            }

            foreach (var addOnRevenue in addOnRevenues)
            {
                revenueBreakdown += addOnRevenue;
            }

            return revenueBreakdown;
        }

        public static decimal GetDailyAccruedSleekPayRevenue(
            List<CmsSleekPayReportData> cmsSleekPayReportData,
            DateTime validDate = default)
        {
            if (validDate == default)
            {
                validDate = DateTime.UtcNow;
            }

            cmsSleekPayReportData = cmsSleekPayReportData
                .Where(x => x.StartActivityDate.Date == validDate.Date).ToList();

            if (cmsSleekPayReportData.Count == 0)
            {
                return decimal.MinValue;
            }

            // Sum Net Earnings of CmsSleekPayReportData

            return cmsSleekPayReportData.Sum(
                reportData => CurrencyConverter.ConvertToUsd(
                    reportData.NetEarning,
                    reportData.Currency));
        }

        public static List<CmsCohortAnalysisDataDto> CalculateCohortAnalysisData(
            List<CmsDailyRevenueAnalyticDto> dailyRevenueAnalyticData,
            int year)
        {
            var result = new List<CmsCohortAnalysisDataDto>();

            var firstMonthDate = new DateTime(year, 1, 1);
            var lastMonthDate = new DateTime(year, 12, 31);

            var monthlyCompanyRevenueBreakDowns = new List<MonthlyCompanyRevenueBreakDowns>();

            for (var month = firstMonthDate.AddMonths(-1); month < lastMonthDate; month = month.AddMonths(1))
            {
                var monthlyString = month.ToString("yyyy-MM");

                monthlyCompanyRevenueBreakDowns.Add(
                    new MonthlyCompanyRevenueBreakDowns()
                    {
                        YearMonth = monthlyString,
                        CompanyRevenueBreakDowns = SummarizeBreakDowns(
                                dailyRevenueAnalyticData.Where(x => x.Date.StartsWith(monthlyString)).ToList())
                            .Where(x => !ValidSubscriptionPlan.FreePlans.Contains(x.SubscriptionPlanId)).ToList()
                    });
            }

            for (var month = 1; month <= 12; month++)
            {
                var currentMonthDateTime = new DateTime(year, month, 1);
                var lastMonthDateTime = currentMonthDateTime.AddMonths(-1);

                List<CmsCompanyRevenueBreakDownDto> companyRevenueBreakDowns;

                var currentMonth =
                    monthlyCompanyRevenueBreakDowns.First(x => x.YearMonth == currentMonthDateTime.ToString("yyyy-MM"));
                var previousMonth =
                    monthlyCompanyRevenueBreakDowns.FirstOrDefault(
                        x => x.YearMonth == lastMonthDateTime.ToString("yyyy-MM"));

                if (previousMonth != null)
                {
                    companyRevenueBreakDowns = currentMonth.CompanyRevenueBreakDowns
                        .Where(
                            x => !previousMonth.CompanyRevenueBreakDowns.Select(p => p.CompanyId).Contains(x.CompanyId))
                        .ToList();
                }
                else
                {
                    companyRevenueBreakDowns = currentMonth.CompanyRevenueBreakDowns;
                }

                var monthData = new CmsCohortAnalysisDataDto
                {
                    YearMonth = currentMonthDateTime.ToString("yyyy-MM"),
                    SubscriptionCount = companyRevenueBreakDowns.Count,
                    MonthlyRecurringRevenue = companyRevenueBreakDowns.Sum(x => x.MonthlyRecurringRevenue),
                    CompanyRevenueBreakDowns = companyRevenueBreakDowns.OrderBy(x => x.CompanyName).ToList()
                };

                if (month != 12)
                {
                    var remainingCompanyIds = companyRevenueBreakDowns.Select(p => p.CompanyId).ToList();

                    for (var forwardMonth = month + 1; forwardMonth <= 12; forwardMonth++)
                    {
                        var forwardMonthDateTime = new DateTime(year, forwardMonth, 1);

                        var forwardMonthData = monthlyCompanyRevenueBreakDowns.First(
                            x => x.YearMonth == forwardMonthDateTime.ToString("yyyy-MM"));

                        var forwardMonthBreakDowns = forwardMonthData.CompanyRevenueBreakDowns
                            .Where(x => remainingCompanyIds.Contains(x.CompanyId))
                            .ToList();

                        if (remainingCompanyIds.Count != forwardMonthBreakDowns.Count)
                        {
                            remainingCompanyIds = forwardMonthBreakDowns.Select(p => p.CompanyId).ToList();
                        }

                        monthData.ForwardMonths.Add(
                            new CmsCohortAnalysisDataForwardMonthDataDto()
                            {
                                ForwardMonth = forwardMonthDateTime.ToString("yyyy-MM"),
                                SubscriptionCount = forwardMonthBreakDowns.Count,
                                MonthlyRecurringRevenue = forwardMonthBreakDowns.Sum(x => x.MonthlyRecurringRevenue),
                                CompanyRevenueBreakDowns = forwardMonthBreakDowns.OrderBy(x => x.CompanyName).ToList(),
                            });
                    }
                }

                result.Add(monthData);
            }

            return result;
        }

        public static List<CmsCompanyRevenueBreakDownDto> SummarizeBreakDowns(
            List<CmsDailyRevenueAnalyticDto> breakDown)
        {
            var list = breakDown.SelectMany(x => x.CompanyRevenueBreakDowns)
                .Where(
                    x => x.MonthlyRecurringRevenue > 0 &&
                         !ValidSubscriptionPlan.FreePlans.Contains(x.SubscriptionPlanId))
                .GroupBy(x => x.CompanyId)
                .Select(x => { return x.OrderByDescending(dto => dto.MonthlyRecurringRevenue).First(); }).ToList();

            return list;
        }

        public static CmsCompanyAllTimeRevenueAnalyticData GetCompanyAllTimeRevenueAnalyticData(
            string companyId,
            List<CmsDailyRevenueAnalyticDto> dailyRevenueAnalytics)
        {
            var result = new CmsCompanyAllTimeRevenueAnalyticData()
            {
                CompanyId = companyId,
            };

            // Make sure the dailyRevenueAnalyticDto only contains data for one company, cover company's create date and dailyRevenueAnalytics is order by date
            foreach (var data in dailyRevenueAnalytics)
            {
                if (data.DailyRevenue > 0 && result.InitialPaidDate == null)
                {
                    result.InitialPaidDate = data.Date;
                    result.InitialPaidSubscriptionPlanId = data.CompanyRevenueBreakDowns
                        .FirstOrDefault(x => x.CompanyId == companyId)?.SubscriptionPlanId;
                    result.InitialMonthlyRecurringRevenue = decimal.Round(data.MonthlyRecurringRevenue, 2);
                }

                result.TotalSubscriptionPlanRevenue += decimal.Round(data.SubscriptionPlanRevenue, 2);
                result.TotalOneTimeSetupFeeRevenue += decimal.Round(data.OneTimeSetupFeeRevenue, 2);
                result.TotalMarkupRevenue += decimal.Round(data.MarkupRevenue, 2);
            }

            result.TotalRevenue += result.TotalSubscriptionPlanRevenue + result.TotalOneTimeSetupFeeRevenue +
                                   result.TotalMarkupRevenue;

            if (result.TotalRevenue > 0)
            {
                result.LastPaidSubscriptionPlanId = dailyRevenueAnalytics.LastOrDefault(
                        x => x.MonthlyRecurringRevenue > 0 && x.CompanyRevenueBreakDowns.Any(
                            c => c.CompanyId == companyId &&
                                 !ValidSubscriptionPlan.FreePlans.Contains(c.SubscriptionPlanId)))?
                    .CompanyRevenueBreakDowns.FirstOrDefault(x => x.CompanyId == companyId)?.SubscriptionPlanId;
            }

            return result;
        }

        private static RevenueBreakdown CalculateDailyRevenue(
            CmsAnalyticBillRecordDto cmsBillingPeriodUsage,
            List<CmsAnalyticBillRecordDto> allValidBillingPeriodUsages,
            DateTime validDate)
        {
            if (cmsBillingPeriodUsage == null)
            {
                return null;
            }

            var revenueBreakdown = new RevenueBreakdown();

            if (cmsBillingPeriodUsage.PayAmount > 0 && cmsBillingPeriodUsage.PeriodStart.Date == validDate.Date)
            {
                var revenue = CurrencyConverter.ConvertToUsd(
                    (decimal) cmsBillingPeriodUsage.PayAmount,
                    cmsBillingPeriodUsage.Currency);

                // One Off
                if (cmsBillingPeriodUsage.SubscriptionPlanId.Contains("oneoff"))
                {
                    revenueBreakdown.OneTimeSetupFeeRevenue += revenue;
                }

                // mark up
                else if (cmsBillingPeriodUsage.SubscriptionPlanId.Contains("markup"))
                {
                    revenueBreakdown.MarkupRevenue += revenue;
                }

                // sub
                else
                {
                    revenueBreakdown.SubscriptionPlanRevenue += revenue;
                }
            }

            // Calculate Cms Payment Input Amount
            foreach (var paymentRecord in cmsBillingPeriodUsage.CmsSalesPaymentRecords)
            {
                var paidDate = cmsBillingPeriodUsage.PeriodStart.Date;

                if (paymentRecord.CreatedAt.Date == validDate.Date)
                {
                    revenueBreakdown.OneTimeSetupFeeRevenue += CurrencyConverter.ConvertToUsd(
                        paymentRecord.OneTimeSetupFee,
                        paymentRecord.Currency);
                }

                if (paymentRecord.PaidAt != null && paymentRecord.PaidAt.Value.Date == validDate.Date)
                {
                    revenueBreakdown.OneTimeSetupFeeRevenueBasedOnPaidAt += CurrencyConverter.ConvertToUsd(
                        paymentRecord.OneTimeSetupFee,
                        paymentRecord.Currency);
                }

                if (paymentRecord.PaidAt != null && paidDate < paymentRecord.PaidAt)
                {
                    paidDate = paymentRecord.PaidAt.Value.Date;
                }

                if (paidDate == validDate.Date)
                {
                    revenueBreakdown.SubscriptionPlanRevenue += CurrencyConverter.ConvertToUsd(
                        paymentRecord.SubscriptionFee,
                        paymentRecord.Currency);
                }
            }

            return revenueBreakdown;
        }

        private static RevenueBreakdown CalculateDailyAccruedRevenue(
            CmsAnalyticBillRecordDto cmsBillingPeriodUsage,
            DateTime validDate)
        {
            if (cmsBillingPeriodUsage == null)
            {
                return null;
            }

            var revenueBreakdown = new RevenueBreakdown();

            if (cmsBillingPeriodUsage.PayAmount > 0)
            {
                var revenue = CurrencyConverter.ConvertToUsd(
                    (decimal) cmsBillingPeriodUsage.PayAmount,
                    cmsBillingPeriodUsage.Currency);

                // Subscription Fee
                var deltaDays = (cmsBillingPeriodUsage.PeriodEnd.Date - cmsBillingPeriodUsage.PeriodStart.Date).Days +
                                1;
                revenueBreakdown.SubscriptionPlanRevenue += revenue / deltaDays;
            }

            // Calculate Cms Payment Input Amount (One Time Setup Fee)
            foreach (var paymentRecord in cmsBillingPeriodUsage.CmsSalesPaymentRecords.Where(
                         paymentRecord => paymentRecord.CreatedAt.Date == validDate.Date))
            {
                revenueBreakdown.OneTimeSetupFeeRevenue += CurrencyConverter.ConvertToUsd(
                    paymentRecord.OneTimeSetupFee,
                    paymentRecord.Currency);
            }

            return revenueBreakdown;
        }

        private static decimal CalculateFinalMonthlyRecurringRevenue(
            decimal monthlyRecurringRevenue,
            DateTime periodStart,
            DateTime periodEnd)
        {
            if (monthlyRecurringRevenue == 0)
            {
                return 0;
            }

            // var month = (decimal)Math.Round((periodEnd - periodStart).TotalDays / 30, 1);
            // if (month <= 0) month = 1;
            //
            // return monthlyRecurringRevenue / month;
            return monthlyRecurringRevenue / GetMonthDiff(periodStart, periodEnd);
        }

        public static int GetMonthDiff(DateTime periodStart, DateTime periodEnd)
        {
            if (periodStart > periodEnd)
            {
                return 0;
            }

            periodStart = periodStart.Date;
            periodEnd = periodEnd.Date;

            int monthDiff = 1;

            while (periodEnd > periodStart.AddMonths(monthDiff))
            {
                monthDiff++;
            }

            monthDiff--;

            if ((periodEnd - periodStart.AddMonths(monthDiff)).TotalDays >= 28)
            {
                monthDiff++;
            }

            return monthDiff > 0 ? monthDiff : 1;
        }
    }
}