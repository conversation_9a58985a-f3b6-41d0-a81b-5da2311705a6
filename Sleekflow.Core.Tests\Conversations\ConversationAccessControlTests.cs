using Travis_backend.CompanyDomain.Models;
using Travis_backend.Constants;
using Travis_backend.ConversationDomain.ConversationAccessControl;
using Travis_backend.ConversationDomain.Models;
using Travis_backend.ConversationDomain.ViewModels;
using Travis_backend.Enums;
using Travis_backend.MessageDomain.Models;

namespace Sleekflow.Core.Tests.Conversations;

// reference https://docs.google.com/spreadsheets/d/1CL1c8744GMhn4uup1cdP98jFV3rWzQnE6yL90JvtM8k/edit?gid=1322051058#gid=1322051058
[TestFixture]
public class ConversationAccessControlTests
{
    private IConversationAccessControlManager _accessControlManager;

    private StaffAccessControlAggregate _adminA;
    private StaffAccessControlAggregate _teamAdminA;
    private StaffAccessControlAggregate _staffA;
    private StaffAccessControlAggregate _adminB;
    private StaffAccessControlAggregate _teamAdminB;
    private StaffAccessControlAggregate _staffB;
    private StaffAccessControlAggregate _adminC;
    private StaffAccessControlAggregate _adminInAnotherCompany;
    private StaffAccessControlAggregate _staffC;
    private StaffAccessControlAggregate _staffD;
    private TeamAccessControlAggregate _teamA;
    private TeamAccessControlAggregate _teamB;
    private string _companyId;
    private string _anotherCompanyId;


    [SetUp]
    public void Setup()
    {
        _accessControlManager= new ConversationAccessControlManager();

        _companyId = "sleekflow";
        _anotherCompanyId = "another_companyId";

        // Teams
        _teamA = new TeamAccessControlAggregate
        {
            Id = 1,
            TeamMemberStaffIds = new List<long>
            {
                1, 2, 3
            }
        };
        _teamB = new TeamAccessControlAggregate
        {
            Id = 2,
            TeamMemberStaffIds = new List<long>
            {
                4, 5, 6
            }
        };

        // Team A members
        _adminA = new StaffAccessControlAggregate
        {
            StaffId = 1,
            RoleType = StaffUserRole.Admin,
            CompanyId = _companyId,
            AssociatedTeams = new List<TeamAccessControlAggregate>
            {
                _teamA
            }
        };
        _teamAdminA = new  StaffAccessControlAggregate
        {
            StaffId = 2,
            RoleType = StaffUserRole.TeamAdmin,
            CompanyId = _companyId,
            AssociatedTeams = new List<TeamAccessControlAggregate>
            {
                _teamA
            }
        };
        _staffA = new StaffAccessControlAggregate
        {
            StaffId = 3,
            RoleType = StaffUserRole.Staff,
            CompanyId = _companyId,
            AssociatedTeams = new List<TeamAccessControlAggregate>
            {
                _teamA
            }
        };

        // Team B members
        _adminB = new StaffAccessControlAggregate
        {
            StaffId = 4,
            RoleType = StaffUserRole.Admin,
            CompanyId = _companyId,
            AssociatedTeams = new List<TeamAccessControlAggregate>
            {
                _teamB
            }
        };
        _teamAdminB = new StaffAccessControlAggregate
        {
            StaffId = 5,
            RoleType = StaffUserRole.TeamAdmin,
            CompanyId = _companyId,
            AssociatedTeams = new List<TeamAccessControlAggregate>
            {
                _teamB
            }
        };
        _staffB = new StaffAccessControlAggregate
        {
            StaffId = 6,
            RoleType = StaffUserRole.Staff,
            CompanyId = _companyId,
            AssociatedTeams = new List<TeamAccessControlAggregate>
            {
                _teamB
            }
        };

        // Others
        _adminC = new StaffAccessControlAggregate
        {
            StaffId = 7, RoleType = StaffUserRole.Admin, CompanyId = _companyId
        };
        _staffC = new StaffAccessControlAggregate
        {
            StaffId = 8, RoleType = StaffUserRole.Staff, CompanyId = _companyId
        };

        _adminInAnotherCompany = new StaffAccessControlAggregate
        {
            StaffId = 9, RoleType = StaffUserRole.Admin, CompanyId = _anotherCompanyId
        };

        _staffD = new StaffAccessControlAggregate
        {
            StaffId = 10, RoleType = StaffUserRole.Staff, CompanyId = _companyId
        };
    }

    #region Conversation is assigned to contact owner only

    [Test]
    public void HasAccess_AccessConversationAssignedToContactOwnerWithoutTeam_ReturnsExpectedResult()
    {
        var conversation = new Conversation { Id = "conversationC", CompanyId = _companyId, AssigneeId = _staffC.StaffId};

        // Team A members
        Assert.IsTrue(_accessControlManager.HasAccess(_adminA, conversation));
        Assert.IsFalse(_accessControlManager.HasAccess(_teamAdminA, conversation));
        Assert.IsFalse(_accessControlManager.HasAccess(_staffA, conversation));

        // Team B members
        Assert.IsTrue(_accessControlManager.HasAccess(_adminB, conversation));
        Assert.IsFalse(_accessControlManager.HasAccess(_teamAdminB, conversation));
        Assert.IsFalse(_accessControlManager.HasAccess(_staffB, conversation));

        // Others
        Assert.IsTrue(_accessControlManager.HasAccess(_adminC, conversation));
        Assert.IsTrue(_accessControlManager.HasAccess(_staffC, conversation));
    }

    #endregion

    #region Conversation is assigned to contact owner and team

    [Test]
    public void HasAccess_AccessConversationAssignedToContactOwnerWithTeam_ReturnsExpectedResult()
    {
        var conversation = new Conversation { Id = "conversationB", CompanyId = _companyId, AssignedTeamId = _teamB.Id, AssigneeId = _staffB.StaffId};

        // Team A members
        Assert.IsTrue(_accessControlManager.HasAccess(_adminA, conversation));
        Assert.IsFalse(_accessControlManager.HasAccess(_teamAdminA, conversation));
        Assert.IsFalse(_accessControlManager.HasAccess(_staffA, conversation));

        // Team B members
        Assert.IsTrue(_accessControlManager.HasAccess(_adminB, conversation));
        Assert.IsTrue(_accessControlManager.HasAccess(_teamAdminB, conversation));
        Assert.IsTrue(_accessControlManager.HasAccess(_staffB, conversation));

        // Others
        Assert.IsTrue(_accessControlManager.HasAccess(_adminC, conversation));
    }

    [Test]
    public void HasAccess_AccessConversationAssignedToContactOwnerWithDifferentTeam_ReturnsExpectedResult()
    {
        var conversation = new Conversation { Id = "conversationB", CompanyId = _companyId, AssignedTeamId = _teamA.Id, AssigneeId = _staffB.StaffId};

        // Team A members
        Assert.IsTrue(_accessControlManager.HasAccess(_adminA, conversation));
        Assert.IsTrue(_accessControlManager.HasAccess(_teamAdminA, conversation));
        Assert.IsFalse(_accessControlManager.HasAccess(_staffA, conversation));

        // Team B members
        Assert.IsTrue(_accessControlManager.HasAccess(_adminB, conversation));
        Assert.IsFalse(_accessControlManager.HasAccess(_teamAdminB, conversation));
        Assert.IsTrue(_accessControlManager.HasAccess(_staffB, conversation));

        // Others
        Assert.IsTrue(_accessControlManager.HasAccess(_adminC, conversation));
    }

    #endregion

    #region Conversation is assigned to team only

    [Test]
    public void HasAccess_AccessConversationAssignedToTeamOnly_ReturnsExpectedResult()
    {
        var conversation = new Conversation { Id = "conversationA", CompanyId = _companyId, AssignedTeamId = _teamA.Id};

        // Team A members
        Assert.IsTrue(_accessControlManager.HasAccess(_adminA, conversation));
        Assert.IsTrue(_accessControlManager.HasAccess(_teamAdminA, conversation));
        Assert.IsTrue(_accessControlManager.HasAccess(_staffA, conversation));

        // Team B members
        Assert.IsTrue(_accessControlManager.HasAccess(_adminB, conversation));
        Assert.IsFalse(_accessControlManager.HasAccess(_teamAdminB, conversation));
        Assert.IsFalse(_accessControlManager.HasAccess(_staffB, conversation));

        // Others
        Assert.IsTrue(_accessControlManager.HasAccess(_adminC, conversation));
    }

    #endregion

    #region Conversation is unassigned

    [Test]
    public void HasAccess_AccessUnassignedConversation_ReturnsExpectedResult()
    {
        var conversation = new Conversation { Id = "conversationC", CompanyId = _companyId};

        // Team A members
        Assert.IsTrue(_accessControlManager.HasAccess(_adminA, conversation));
        Assert.IsTrue(_accessControlManager.HasAccess(_teamAdminA, conversation));
        Assert.IsTrue(_accessControlManager.HasAccess(_staffA, conversation));

        // Team B members
        Assert.IsTrue(_accessControlManager.HasAccess(_adminB, conversation));
        Assert.IsTrue(_accessControlManager.HasAccess(_teamAdminB, conversation));
        Assert.IsTrue(_accessControlManager.HasAccess(_staffB, conversation));

        // Others
        Assert.IsTrue(_accessControlManager.HasAccess(_adminC, conversation));
        Assert.IsTrue(_accessControlManager.HasAccess(_staffC, conversation));
    }

    #endregion

    #region Conversation has collaborators

    [Test]
    public void HasAccess_AccessConversationHasCollaborator_ReturnsExpectedResult()
    {
        var collaborator = new AdditionalAssignee
        {
            Id = 1, CompanyId = _companyId, AssigneeId = _staffC.StaffId
        };
        var conversation = new Conversation
        {
            Id = "conversationC",
            CompanyId = _companyId,
            AssigneeId = _staffA.StaffId,
            AdditionalAssignees = new List<AdditionalAssignee>
            {
                collaborator
            }
        };

        // Team A members
        Assert.IsTrue(_accessControlManager.HasAccess(_adminA, conversation));
        Assert.IsFalse(_accessControlManager.HasAccess(_teamAdminA, conversation));
        Assert.IsTrue(_accessControlManager.HasAccess(_staffA, conversation));

        // Team B members
        Assert.IsTrue(_accessControlManager.HasAccess(_adminB, conversation));
        Assert.IsFalse(_accessControlManager.HasAccess(_teamAdminB, conversation));
        Assert.IsFalse(_accessControlManager.HasAccess(_staffB, conversation));

        // Others
        Assert.IsTrue(_accessControlManager.HasAccess(_adminC, conversation));
        Assert.IsTrue(_accessControlManager.HasAccess(_staffC, conversation));
        Assert.IsFalse(_accessControlManager.HasAccess(_staffD, conversation));
    }

    #endregion

    #region Conversation has mentioned staffs

    [Test]
    public void HasAccess_AccessConversationHasMentionedStaff_ReturnsExpectedResult()
    {
        var conversation = new Conversation
        {
            Id = "conversationC",
            CompanyId = _companyId,
            AssigneeId = _staffA.StaffId,
            ChatHistory = new List<ConversationMessage>()
            {
                new ()
                {
                    Channel = ChannelTypes.Note,
                    MessageAssigneeId = _staffC.StaffId,

                    UpdatedAt = DateTime.UtcNow.AddDays(-2).AddHours(1)
                },
                new ()
                {
                    Channel = ChannelTypes.Note,
                    MessageAssigneeId = _staffD.StaffId,
                    UpdatedAt = DateTime.UtcNow.AddDays(-2).AddHours(-1)
                }
            }
        };

        // Team A members
        Assert.IsTrue(_accessControlManager.HasAccess(_adminA, conversation));
        Assert.IsFalse(_accessControlManager.HasAccess(_teamAdminA, conversation));
        Assert.IsTrue(_accessControlManager.HasAccess(_staffA, conversation));

        // Team B members
        Assert.IsTrue(_accessControlManager.HasAccess(_adminB, conversation));
        Assert.IsFalse(_accessControlManager.HasAccess(_teamAdminB, conversation));
        Assert.IsFalse(_accessControlManager.HasAccess(_staffB, conversation));

        // Others
        Assert.IsTrue(_accessControlManager.HasAccess(_adminC, conversation));
        Assert.IsTrue(_accessControlManager.HasAccess(_staffC, conversation));
        Assert.IsFalse(_accessControlManager.HasAccess(_staffD, conversation));
    }

    #endregion

    #region Conversation is assigned to contact owner only who is a team admin

    [Test]
    public void HasAccess_AccessConversationAssignedToContactOwnerOnlyWhoIsTeamAdmin_ReturnsExpectedResult()
    {
        var conversation = new Conversation { Id = "conversationD", CompanyId = _companyId, AssigneeId = _teamAdminA.StaffId };

        // Team A members
        Assert.IsTrue(_accessControlManager.HasAccess(_adminA, conversation));
        Assert.IsTrue(_accessControlManager.HasAccess(_teamAdminA, conversation));
        Assert.IsFalse(_accessControlManager.HasAccess(_staffA, conversation));

        // Team B members
        Assert.IsTrue(_accessControlManager.HasAccess(_adminB, conversation));
        Assert.IsFalse(_accessControlManager.HasAccess(_teamAdminB, conversation));
        Assert.IsFalse(_accessControlManager.HasAccess(_staffB, conversation));

        // Others
        Assert.IsTrue(_accessControlManager.HasAccess(_adminC, conversation));
        Assert.IsFalse(_accessControlManager.HasAccess(_staffC, conversation));
    }

    #endregion

    #region Conversation has collaborator who is a team member

    [Test]
    public void HasAccess_AccessConversationHasCollaboratorWhoIsTeamMember_ReturnsExpectedResult()
    {
        var collaborator = new AdditionalAssignee
        {
            Id = 1, CompanyId = _companyId, AssigneeId = _staffA.StaffId
        };
        var conversation = new Conversation
        {
            Id = "conversationE",
            CompanyId = _companyId,
            AssigneeId = _staffC.StaffId,
            AdditionalAssignees = new List<AdditionalAssignee>
            {
                collaborator
            }
        };

        // Team A members
        Assert.IsTrue(_accessControlManager.HasAccess(_adminA, conversation));
        Assert.IsTrue(_accessControlManager.HasAccess(_teamAdminA, conversation));
        Assert.IsTrue(_accessControlManager.HasAccess(_staffA, conversation));

        // Team B members
        Assert.IsTrue(_accessControlManager.HasAccess(_adminB, conversation));
        Assert.IsFalse(_accessControlManager.HasAccess(_teamAdminB, conversation));
        Assert.IsFalse(_accessControlManager.HasAccess(_staffB, conversation));

        // Others
        Assert.IsTrue(_accessControlManager.HasAccess(_adminC, conversation));
        Assert.IsTrue(_accessControlManager.HasAccess(_staffC, conversation));
        Assert.IsFalse(_accessControlManager.HasAccess(_staffD, conversation));
    }

    #endregion

    #region Conversation assigned to team with multiple collaborators

    [Test]
    public void HasAccess_AccessConversationAssignedToTeamWithMultipleCollaborators_ReturnsExpectedResult()
    {
        var collaborators = new List<AdditionalAssignee>
        {
            new() { Id = 1, CompanyId = _companyId, AssigneeId = _staffC.StaffId },
            new() { Id = 3, CompanyId = _companyId, AssigneeId = _staffB.StaffId }

        };
        var conversation = new Conversation
        {
            Id = "conversationF",
            CompanyId = _companyId,
            AssignedTeamId = _teamA.Id,
            AdditionalAssignees = collaborators
        };

        // Team A members
        Assert.IsTrue(_accessControlManager.HasAccess(_adminA, conversation));
        Assert.IsTrue(_accessControlManager.HasAccess(_teamAdminA, conversation));
        Assert.IsTrue(_accessControlManager.HasAccess(_staffA, conversation));

        // Team B members
        Assert.IsTrue(_accessControlManager.HasAccess(_adminB, conversation));
        Assert.IsTrue(_accessControlManager.HasAccess(_teamAdminB, conversation));
        Assert.IsTrue(_accessControlManager.HasAccess(_staffB, conversation));

        // Others
        Assert.IsTrue(_accessControlManager.HasAccess(_adminC, conversation));
        Assert.IsTrue(_accessControlManager.HasAccess(_staffC, conversation));
        Assert.IsFalse(_accessControlManager.HasAccess(_staffD, conversation));
    }

    #endregion

    #region Conversation with mixed mentioned staffs and collaborators

    [Test]
    public void HasAccess_AccessConversationWithMixedMentionedStaffsAndCollaborators_ReturnsExpectedResult()
    {
        var collaborator = new AdditionalAssignee
        {
            Id = 1, CompanyId = _companyId, AssigneeId = _staffB.StaffId
        };
        var conversation = new Conversation
        {
            Id = "conversationG",
            CompanyId = _companyId,
            AssigneeId = _staffA.StaffId,
            AdditionalAssignees = new List<AdditionalAssignee> { collaborator },
            ChatHistory = new List<ConversationMessage>()
            {
                new ()
                {
                    Channel = ChannelTypes.Note,
                    MessageAssigneeId = _staffC.StaffId,

                    UpdatedAt = DateTime.UtcNow.AddDays(-2).AddHours(1)
                },
                new ()
                {
                    Channel = ChannelTypes.Note,
                    MessageAssigneeId = _staffD.StaffId,
                    UpdatedAt = DateTime.UtcNow.AddDays(-2).AddHours(-1)
                }
            }
        };

        // Team A members
        Assert.IsTrue(_accessControlManager.HasAccess(_adminA, conversation));
        Assert.IsFalse(_accessControlManager.HasAccess(_teamAdminA, conversation));
        Assert.IsTrue(_accessControlManager.HasAccess(_staffA, conversation));

        // Team B members
        Assert.IsTrue(_accessControlManager.HasAccess(_adminB, conversation));
        Assert.IsTrue(_accessControlManager.HasAccess(_teamAdminB, conversation));
        Assert.IsTrue(_accessControlManager.HasAccess(_staffB, conversation));

        // Others
        Assert.IsTrue(_accessControlManager.HasAccess(_adminC, conversation));
        Assert.IsTrue(_accessControlManager.HasAccess(_staffC, conversation));
        Assert.IsFalse(_accessControlManager.HasAccess(_staffD, conversation));
    }

    #endregion

    #region Access attempt from staff in another company

    [Test]
    public void HasAccess_AccessAttemptFromStaffInAnotherCompany_ReturnsFalse()
    {
        var conversation = new Conversation { Id = "conversationH", CompanyId = _companyId, AssignedTeamId = _teamA.Id };

        Assert.IsFalse(_accessControlManager.HasAccess(_adminInAnotherCompany, conversation));
    }

    #endregion

    #region Conversation with null AssignedTeamId and AssigneeId

    [Test]
    public void HasAccess_AccessConversationWithNullAssignedTeamIdAndAssigneeId_ReturnsExpectedResult()
    {
        var conversation = new Conversation { Id = "conversationI", CompanyId = _companyId, AssignedTeamId = null, AssigneeId = null };

        // All staff should have access to unassigned conversations
        Assert.IsTrue(_accessControlManager.HasAccess(_adminA, conversation));
        Assert.IsTrue(_accessControlManager.HasAccess(_teamAdminA, conversation));
        Assert.IsTrue(_accessControlManager.HasAccess(_staffA, conversation));
        Assert.IsTrue(_accessControlManager.HasAccess(_adminB, conversation));
        Assert.IsTrue(_accessControlManager.HasAccess(_teamAdminB, conversation));
        Assert.IsTrue(_accessControlManager.HasAccess(_staffB, conversation));
        Assert.IsTrue(_accessControlManager.HasAccess(_adminC, conversation));
        Assert.IsTrue(_accessControlManager.HasAccess(_staffC, conversation));
        Assert.IsTrue(_accessControlManager.HasAccess(_staffD, conversation));
    }

    #endregion
}