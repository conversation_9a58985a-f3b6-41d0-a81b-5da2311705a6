﻿using System;
using System.Collections.Generic;
using System.Collections.Immutable;
using System.Linq;
using System.Threading.Tasks;
using AutoMapper;
using Hangfire;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using Stripe.Checkout;
using Travis_backend.CommonDomain.Services;
using Travis_backend.Commons.Models;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.CompanyDomain.Repositories;
using Travis_backend.CompanyDomain.Services;
using Travis_backend.Database;
using Travis_backend.Enums;
using Travis_backend.Extensions;
using Travis_backend.Infrastructures.Options;
using Travis_backend.InternalDomain.Services;
using Travis_backend.Services;
using Travis_backend.StripeIntegrationDomain.Services;
using Travis_backend.SubscriptionPlanDomain.Constants;
using Travis_backend.SubscriptionPlanDomain.Enums;
using Travis_backend.SubscriptionPlanDomain.Models;
using Travis_backend.SubscriptionPlanDomain.Repositories;
using Travis_backend.SubscriptionPlanDomain.ViewModels;

namespace Travis_backend.SubscriptionPlanDomain.Services
{
    public interface ISubscriptionPlanService
    {
        #region Unused Methods

        Task<bool> IsFreeTier(string planId);

        Task<bool> IsPaidPlan(string planId);

        Task<bool> IsProTier(string planId);

        Task<bool> IsPremiumTier(string planId);

        Task<bool> IsEnterpriseTier(string planId);

        Task<bool> IsAgentPlan(string planId);

        Task<bool> IsAddOn(string planId);

        Task<List<SubscriptionPlan>> GetPlanByCurrency(string currency);

        Task<List<SubscriptionPlan>> GetPlanByTeir(SubscriptionTier subscriptionTier);

        Task<List<SubscriptionPlan>> GetPlanByCurrencyAndTeir(string currency, SubscriptionTier subscriptionTier);

        #endregion

        /// <summary>
        /// Get SubscriptionPlan by PlanId.
        /// </summary>
        /// <param name="planId">Plan Id.</param>
        /// <returns>SubscriptionPlan.</returns>
        Task<SubscriptionPlan> GetSubscriptionPlanAsync(string planId);

        /// <summary>
        /// Get SubscriptionPlans by PlanId.
        /// </summary>
        /// <param name="subscriptionPlanIds">PlanId.</param>
        /// <returns>Collection of SubscriptionPlan.</returns>
        Task<IEnumerable<SubscriptionPlan>> GetSubscriptionPlansAsync(IEnumerable<string> subscriptionPlanIds);

        /// <summary>
        /// Get SubscriptionPlan by Stripe Plan Id.
        /// </summary>
        /// <param name="stripePlanId">StripePlanId, normally known as Stripe's PriceId.</param>
        /// <returns>SubscriptionPlan.</returns>
        Task<SubscriptionPlan> GetSubscriptionPlanByStripePlanIdAsync(string stripePlanId);

        /// <summary>
        /// Get available base plans for a company to subscribe.
        /// </summary>
        /// <param name="companyId">Company Id.</param>
        /// <param name="ipAddress">IP Address.</param>
        /// <returns>Available Base Subscription Plans.</returns>
        Task<GetAvailableSubscriptionPlansResponseModel> GetAvailableSubscriptionPlans(string companyId, string ipAddress);

        /// <summary>
        /// Get available add-ons for selected subscription plans.
        /// </summary>
        /// <param name="companyId">Company Id.</param>
        /// <param name="subscriptionPlanId">Subscription Plan Id.</param>
        /// <returns>Add-Ons.</returns>
        Task<GetAddOnsForSubscriptionPlanResponseModel> GetAvailableAddOnsForSubscriptionPlan(
            string companyId,
            string subscriptionPlanId);

        /// <summary>
        /// Get available add-ons for company to subscribe.
        /// </summary>
        /// <param name="companyId">Company Id.</param>
        /// <param name="ipAddress">IP Address.</param>
        /// <returns>Collection of Add-On Plans.</returns>
        Task<IEnumerable<AvailableAddOnResponseModel>> GetAvailableAddOns(string companyId, string ipAddress);

        /// <summary>
        /// Get available support services for company to purchase or subscribe.
        /// </summary>
        /// <param name="companyId">Company Id</param>
        /// <param name="ipAddress"></param>
        /// <returns>Collection of Support Services.</returns>
        Task<IEnumerable<AvailableAddOnResponseModel>> GetAvailableSupportServices(string companyId, string ipAddress);

        /// <summary>
        /// Get selected to purchase add-on details.
        /// </summary>
        /// <param name="companyId">Company Id.</param>
        /// <param name="subscriptionPlanId">Subscription Plan Id.</param>
        /// <param name="ipAddress">IP Address</param>
        /// <returns>Purchase Add-On Details.</returns>
        [Obsolete("Due to the new requirements, this method are not able to response multiple plan details at same time, Use GetAddOnPurchaseOptions instead. ")]
        Task<PurchaseAddOnDetailsResponseModel> GetPurchaseAddOnDetails(string companyId, string subscriptionPlanId, string ipAddress);

        /// <summary>
        /// Get purchase details and options for selected add-on type.
        /// </summary>
        /// <param name="companyId">Company Id.</param>
        /// <param name="addOnType">AddOn Type.</param>
        /// <param name="ipAddress">IP Address.</param>
        /// <returns>Purchase Add-On Details.</returns>
        Task<AddOnPurchaseOptionsResponseModel> GetAddOnPurchaseOptions(string companyId, string addOnType, string ipAddress);

        /// <summary>
        /// Check if company are able to purchase selected SubscriptionPlan.
        /// </summary>
        /// <param name="companyId">Company Id.</param>
        /// <param name="subscriptionPlan">Subscription Plan.</param>
        /// <exception cref="ApplicationException">ApplicationException with the reason.</exception>
        /// <returns>No Return.</returns>
        Task CheckIsAddOnPurchasable(string companyId, SubscriptionPlan subscriptionPlan);

        Task<SubscribePlanResponseModel> SubscribePlans(string companyId, string email, SubscribePlanRequestModel subscribePlansRequest);

        /// <summary>
        /// Get feature id of subscription plan.
        /// </summary>
        /// <param name="subscriptionPlan">Subscription Plan.</param>
        /// <returns>Feature Id.</returns>
        string GetFeatureId(SubscriptionPlan subscriptionPlan);

        /// <summary>
        /// Submit a subscription cancellation survey.
        /// </summary>
        /// <param name="companyId">Company Id.</param>
        /// <param name="cancellationSurvey">Cancellation survey data.</param>
        /// <returns>A task that represents the asynchronous operation. </returns>
        Task SubmitSubscriptionCancellationSurveyAsync(string companyId, SubmitSubscriptionCancellationSurveyRequest cancellationSurvey);

        /// <summary>
        /// Get migration plan id for legacy plan to v10.
        /// </summary>
        /// <param name="companyId">Company Id.</param>
        /// <param name="ipAddress">IP Address.</param>
        /// <returns>Subscription Plan Id.</returns>
        Task<string> GetMigrationPlan(string companyId, string ipAddress);
    }

    public class SubscriptionPlanService : ISubscriptionPlanService
    {
        #region Dependencies & Constructors

        /// <summary>
        /// ICompanyService.
        /// </summary>
        private readonly ICompanyService _companyService;

        /// <summary>
        /// ICompanySubscriptionService.
        /// </summary>
        private readonly ICompanySubscriptionService _companySubscriptionService;

        /// <summary>
        /// ICompanyUsageService.
        /// </summary>
        private readonly ICompanyUsageService _companyUsageService;

        /// <summary>
        /// IStripeServicesFactory.
        /// </summary>
        private readonly IStripeServicesFactory _stripeServicesFactory;

        /// <summary>
        /// IPlanDefinitionService
        /// </summary>
        private readonly IPlanDefinitionService _planDefinitionService;

        /// <summary>
        /// IFeatureQuantityService.
        /// </summary>
        private readonly IFeatureQuantityService _featureQuantityService;

        /// <summary>
        /// ICountryService.
        /// </summary>
        private readonly ICountryService _countryService;

        /// <summary>
        /// IStripeCustomerService.
        /// </summary>
        private readonly IStripeCustomerService _stripeCustomerService;

        /// <summary>
        /// ISubscriptionCountryTierDeterminer.
        /// </summary>
        private readonly ISubscriptionCountryTierDeterminer _subscriptionCountryTierDeterminer;

        /// <summary>
        /// ISubscriptionPlanRepository.
        /// </summary>
        private readonly ISubscriptionPlanRepository _subscriptionPlanRepository;

        /// <summary>
        /// ICustomSubscriptionPlanTranslationMapRepository.
        /// </summary>
        private readonly ICustomSubscriptionPlanTranslationMapRepository _customSubscriptionPlanTranslationMapRepository;

        /// <summary>
        /// ICancelSubscriptionRecordRepository.
        /// </summary>
        private readonly ICancelSubscriptionRecordRepository _cancelSubscriptionRecordRepository;

        /// <summary>
        /// IBackgroundJobClient.
        /// </summary>
        private readonly IBackgroundJobClient _backgroundJobClient;

        /// <summary>
        /// FeatureFlagsOptions
        /// </summary>
        private readonly FeatureFlagsOptions _featureFlagsOptions;

        private readonly ApplicationDbContext _appDbContext;

        private readonly ILogger _logger;

        public SubscriptionPlanService(
            ApplicationDbContext appDbContext,
            ILogger<SubscriptionPlanService> logger,
            ICountryService countryService,
            ISubscriptionCountryTierDeterminer subscriptionCountryTierDeterminer,
            ISubscriptionPlanRepository subscriptionPlanRepository,
            IFeatureQuantityService featureQuantityService,
            IPlanDefinitionService planDefinitionService,
            ICompanyService companyService,
            ICompanySubscriptionService companySubscriptionService,
            ICompanyUsageService companyUsageService,
            IStripeServicesFactory stripeServicesFactory,
            IStripeCustomerService stripeCustomerService,
            IOptions<FeatureFlagsOptions> featureFlagsOptions,
            ICustomSubscriptionPlanTranslationMapRepository customSubscriptionPlanTranslationMapRepository,
            ICancelSubscriptionRecordRepository cancelSubscriptionRecordRepository,
            IBackgroundJobClient backgroundJobClient)
        {
            _planDefinitionService = planDefinitionService;
            _companyService = companyService;
            _companySubscriptionService = companySubscriptionService;
            _companyUsageService = companyUsageService;
            _stripeServicesFactory = stripeServicesFactory;
            _stripeCustomerService = stripeCustomerService;
            _customSubscriptionPlanTranslationMapRepository = customSubscriptionPlanTranslationMapRepository;
            _cancelSubscriptionRecordRepository = cancelSubscriptionRecordRepository;
            _backgroundJobClient = backgroundJobClient;
            _featureQuantityService = featureQuantityService;
            _countryService = countryService;
            _subscriptionCountryTierDeterminer = subscriptionCountryTierDeterminer;
            _subscriptionPlanRepository = subscriptionPlanRepository;
            _featureFlagsOptions = featureFlagsOptions.Value;
            _appDbContext = appDbContext;
            _logger = logger;
        }

        #endregion

        #region Unused Methods

        public async Task<bool> IsPaidPlan(string planId)
        {
            return await _appDbContext.CoreSubscriptionPlans.AnyAsync(
                x => x.Id == planId &&
                     (x.SubscriptionTier == SubscriptionTier.Premium ||
                      x.SubscriptionTier == SubscriptionTier.Pro ||
                      x.SubscriptionTier == SubscriptionTier.Enterprise));
        }

        public async Task<bool> IsFreeTier(string planId)
        {
            return await _appDbContext.CoreSubscriptionPlans.AnyAsync(
                x => x.Id == planId &&
                     x.SubscriptionTier == SubscriptionTier.Free);
        }

        public async Task<bool> IsProTier(string planId)
        {
            return await _appDbContext.CoreSubscriptionPlans.AnyAsync(
                x => x.Id == planId &&
                     x.SubscriptionTier == SubscriptionTier.Pro);
        }

        public async Task<bool> IsPremiumTier(string planId)
        {
            return await _appDbContext.CoreSubscriptionPlans.AnyAsync(
                x => x.Id == planId &&
                     x.SubscriptionTier == SubscriptionTier.Premium);
        }

        public async Task<bool> IsEnterpriseTier(string planId)
        {
            return await _appDbContext.CoreSubscriptionPlans.AnyAsync(
                x => x.Id == planId &&
                     x.SubscriptionTier == SubscriptionTier.Enterprise);
        }

        public async Task<bool> IsAgentPlan(string planId)
        {
            return await _appDbContext.CoreSubscriptionPlans.AnyAsync(
                x => x.Id == planId &&
                     x.SubscriptionTier == SubscriptionTier.Agent);
        }

        public async Task<bool> IsAddOn(string planId)
        {
            return await _appDbContext.CoreSubscriptionPlans.AnyAsync(
                x => x.Id == planId &&
                     x.SubscriptionTier == SubscriptionTier.AddOn);
        }

        public async Task<List<SubscriptionPlan>> GetPlanByCurrency(string currency)
        {
            return await _appDbContext.CoreSubscriptionPlans.Where(x => x.Currency == currency).ToListAsync();
        }

        public async Task<List<SubscriptionPlan>> GetPlanByTeir(SubscriptionTier subscriptionTier)
        {
            return await _appDbContext.CoreSubscriptionPlans.Where(x => x.SubscriptionTier == subscriptionTier)
                .ToListAsync();
        }

        public async Task<List<SubscriptionPlan>> GetPlanByCurrencyAndTeir(
            string currency,
            SubscriptionTier subscriptionTier)
        {
            return await _appDbContext.CoreSubscriptionPlans
                .Where(x => x.Currency == currency && x.SubscriptionTier == subscriptionTier).ToListAsync();
        }

        #endregion

        /// <inheritdoc />
        public async Task<SubscriptionPlan> GetSubscriptionPlanAsync(string planId)
        {
            return await _subscriptionPlanRepository.FindById(planId);
        }

        /// <inheritdoc />
        public Task<IEnumerable<SubscriptionPlan>> GetSubscriptionPlansAsync(IEnumerable<string> subscriptionPlanIds)
        {
            return _subscriptionPlanRepository.FindByIds(subscriptionPlanIds);
        }

        /// <inheritdoc />
        public async Task<SubscriptionPlan> GetSubscriptionPlanByStripePlanIdAsync(string stripePlanId)
        {
            var customPlanMap = await _customSubscriptionPlanTranslationMapRepository.FindBySourceSubscriptionPlanIdAsync(stripePlanId);

            if (customPlanMap != null)
            {
                return await _subscriptionPlanRepository.FindById(customPlanMap.DestinationSubscriptionPlanId);
            }

            return await _subscriptionPlanRepository.FindByStripePlanIdAsync(stripePlanId);
        }

        /// <inheritdoc />
        public async Task<GetAvailableSubscriptionPlansResponseModel> GetAvailableSubscriptionPlans(string companyId, string ipAddress)
        {
            var subscriptionInfo = await _companyService.GetCompanySubscriptionInfo(companyId);

            var (countryTier, availableCurrencies) = await _subscriptionCountryTierDeterminer.DetermineAsync(companyId, ipAddress);

            var subscriptionPlans = await _subscriptionPlanRepository.GetBaseSubscriptionPlans(SubscriptionPlanVersions.Version10, countryTier.ToString(), availableCurrencies);

            var availableSubscriptionPlansInfo = subscriptionPlans.Select(
                x => new AvailableSubscriptionPlanInfoResponseModel
                {
                    Id = x.Id,
                    SubscriptionTier = x.SubscriptionTier.ToString().ToLower(),
                    SubscriptionInterval = x.SubscriptionInterval,
                    Currency = x.Currency,
                    Amount = x.Amount
                }).ToList();

            var highlightedFeatureInfos = new Dictionary<string, SubscriptionTierHighlightedFeatureInfo>();

            #region Calculate for Highlighted Features

            var tieredSubscriptionPlans = subscriptionPlans.GroupBy(x => x.SubscriptionTier).Select(x => x.First()).ToList();

            foreach (var subscriptionPlan in tieredSubscriptionPlans)
            {
                var planAgentFeatureQuantity = await _featureQuantityService.GetPlanFeatureQuantity(subscriptionPlan.Id, FeatureId.Agents);
                var planContactsFeatureQuantity = await _featureQuantityService.GetPlanFeatureQuantity(subscriptionPlan.Id, FeatureId.Contacts);
                var planBroadcastFeatureQuantity = await _featureQuantityService.GetPlanFeatureQuantity(subscriptionPlan.Id, FeatureId.BroadcastMessages);
                var planFlowBuilderActiveFlowFeatureQuantity = await _featureQuantityService.GetPlanFeatureQuantity(subscriptionPlan.Id, FeatureId.FlowBuilderMaxActiveWorkflowCount);
                var planAutomationFeatureQuantity = await _featureQuantityService.GetPlanFeatureQuantity(subscriptionPlan.Id, FeatureId.Automations);

                var highlightedFeatureInfo = new SubscriptionTierHighlightedFeatureInfo(
                    planAgentFeatureQuantity.Quantity,
                    (int?) planAgentFeatureQuantity.MaximumQuantity,
                    planContactsFeatureQuantity.Quantity,
                    planBroadcastFeatureQuantity.Quantity,
                    planFlowBuilderActiveFlowFeatureQuantity.Quantity,
                    planAutomationFeatureQuantity.Quantity);

                highlightedFeatureInfos[subscriptionPlan.SubscriptionTier.ToString().ToLower()] = highlightedFeatureInfo;
            }

            #endregion

            var featureDetails = new GetSubscriptionPlansFeatureDetails();

            #region Calculate for Features Details

            IEnumerable<SubscriptionTier> proToEnterpriseTiers = ImmutableList.Create(SubscriptionTier.Pro, SubscriptionTier.Premium, SubscriptionTier.Enterprise);

            featureDetails.WabaPhoneNumberLicenseFee = new Dictionary<string, IDictionary<string, decimal>>();

            foreach (var subscriptionTier in proToEnterpriseTiers)
            {
                var currencyPrice = new Dictionary<string, decimal>();

                foreach (var currency in availableCurrencies)
                {
                    var whatsAppPhoneNumberAddOnPlan = await _subscriptionPlanRepository.GetWhatsAppPhoneNumberAddOnPlan(countryTier.ToString(), currency, "monthly", SubscriptionPlanVersions.Version10);
                    var whatsAppPhoneNumberAddOnPlanDefinition = await _planDefinitionService.GetPlanDefinitionAsync(whatsAppPhoneNumberAddOnPlan.Id);
                    currencyPrice[currency] = whatsAppPhoneNumberAddOnPlanDefinition.PlanAmounts.Single(x => x.CurrencyIsoCode == currency).Amount;
                }

                featureDetails.WabaPhoneNumberLicenseFee[subscriptionTier.ToString().ToLower()] = currencyPrice;
            }

            #endregion

            var result = new GetAvailableSubscriptionPlansResponseModel();
            result.AvailableCurrencies = availableCurrencies;
            result.AvailablePlans = availableSubscriptionPlansInfo;
            result.CurrentSubscriptionPlanTier = subscriptionInfo.SubscriptionTier.ToString().ToLower();
            result.CurrentSubscriptionPlanInterval = subscriptionInfo.SubscriptionInterval;
            result.HighlightedFeatures = highlightedFeatureInfos;
            result.FeaturesDetails = featureDetails;

            return result;
        }

        /// <inheritdoc />
        public async Task<GetAddOnsForSubscriptionPlanResponseModel> GetAvailableAddOnsForSubscriptionPlan(
            string companyId,
            string subscriptionPlanId)
        {
            ICollection<string> supportServicesTypes = ImmutableHashSet.Create(PlanTypeCodes.OnboardingSupport, PlanTypeCodes.BusinessConsultancyService);

            var companySubscriptionInfo = await _companyService.GetCompanySubscriptionInfo(companyId);
            var selectedPlan = await _subscriptionPlanRepository.FindById(subscriptionPlanId);

            #region Prepare Available Add-On Types

            ISet<string> addOnTypes = new HashSet<string>
            {
                PlanTypeCodes.Contacts,
                PlanTypeCodes.Agents,
            };

            if (_featureFlagsOptions.FlowBuilderMonetisation.IsEnabled)
            {
                addOnTypes.Add(PlanTypeCodes.FlowBuilderFlowEnrolments);
            }

            addOnTypes.UnionWith(supportServicesTypes);

            #endregion

            var addOnDetails = new List<GetAddOnsForSubscriptionPlanAddOnDetailsResponseModel>();

            foreach (var addOnType in addOnTypes)
            {
                var addOnPlans = await GetAddOnPlansAsync(
                    selectedPlan.SubscriptionTier,
                    selectedPlan.CountryTier,
                    selectedPlan.Currency,
                    selectedPlan.SubscriptionInterval,
                    addOnType);

                if (!addOnPlans.Any())
                {
                    _logger.LogWarning(
                        "No {AddOnType} plans found for selected plan '{SelectedPlanId}'",
                        addOnType,
                        selectedPlan.Id);
                    continue;
                }

                var addOn = addOnPlans.First();

                var firstAddOnPlanDefinition = await _planDefinitionService.GetPlanDefinitionAsync(addOn.Id);
                var featureId = firstAddOnPlanDefinition.FeatureQuantities.First().FeatureId;

                #region Exclude Support Services if Purchased

                if (supportServicesTypes.ContainsIgnoreCase(addOnType))
                {
                    var hasFeature = await _featureQuantityService.HasFeature(companyId, featureId);

                    if (hasFeature)
                    {
                        continue;
                    }
                }

                #endregion

                var featureQuantityInfo = await _featureQuantityService.GetFeatureQuantityInfoAsync(companyId, featureId);
                var selectedBasePlanFeatureQuantity = await _featureQuantityService.GetPlanFeatureQuantity(selectedPlan.Id, featureId);
                var planFeatureUnitQuantity = await _featureQuantityService.GetPlanFeatureQuantity(addOn.Id, featureId);

                var defaultPurchaseUnit = 0;
                var purchasableUnit = GetAddOnPurchasableUnit(selectedBasePlanFeatureQuantity, new FeatureQuantityInfo(string.Empty, string.Empty, selectedBasePlanFeatureQuantity.Quantity, 0), addOn);

                if (addOnType == PlanTypeCodes.Agents)
                {
                    var currentAgentFeatureQuantity = await _featureQuantityService.GetFeatureQuantityAsync(companyId, featureId);
                    defaultPurchaseUnit = Math.Max(0, currentAgentFeatureQuantity - selectedBasePlanFeatureQuantity.Quantity);
                }

                ICollection<GetAddOnsForSubscriptionPlanAddOnOptionResponseModel> addOnOptions = new List<GetAddOnsForSubscriptionPlanAddOnOptionResponseModel>();

                foreach (var addOnPlan in addOnPlans)
                {
                    var addOnPlanFeatureUnitQuantity = await _featureQuantityService.GetPlanFeatureQuantity(addOnPlan.Id, featureId);

                    #region Exclude Flow Enrolment Add-On

                    if (addOnType == PlanTypeCodes.FlowBuilderFlowEnrolments)
                    {
                        //// The Flow Enrolment Add-On is not expected to cancelled if the tier is not changed
                        //// Thus, exclude the enrolment add-on that quantity is lower or equal to current purchased quantity
                        if (companySubscriptionInfo.SubscriptionTier == selectedPlan.SubscriptionTier)
                        {
                            if (addOnPlanFeatureUnitQuantity.Quantity <= featureQuantityInfo.PurchasedQuantity)
                            {
                                continue;
                            }
                        }
                    }

                    #endregion

                    var option = new GetAddOnsForSubscriptionPlanAddOnOptionResponseModel(
                        addOnPlan.Id,
                        addOnPlanFeatureUnitQuantity.Quantity,
                        purchasableUnit,
                        addOnPlan.Amount);

                    addOnOptions.Add(option);
                }

                if (!addOnOptions.Any())
                {
                    //// Skip Add-On if Options not available.
                    continue;
                }

                GetAddOnsForSubscriptionPlanAddOnDetailsResponseModel addOnDetail = new()
                {
                    Id = addOn.Id,
                    Type = addOnType,
                    Amount = addOn.Amount,
                    Currency = addOn.Currency,
                    SubscriptionInterval = addOn.SubscriptionInterval,
                    BaseQuantity = planFeatureUnitQuantity.Quantity,
                    DefaultPurchaseQuantity = defaultPurchaseUnit,
                    DefaultPurchaseUnit = defaultPurchaseUnit,
                    MaxPurchaseQuantity = purchasableUnit,
                    Options = addOnOptions
                };

                addOnDetails.Add(addOnDetail);
            }

            GetAddOnsForSubscriptionPlanResponseModel result = new();
            result.AvailableAddOns = addOnDetails;

            return result;
        }

        /// <inheritdoc />
        public async Task<IEnumerable<AvailableAddOnResponseModel>> GetAvailableAddOns(string companyId, string ipAddress)
        {
            _logger.LogInformation("Get Available Add-Ons for Company, CompanyId: {CompanyId}", companyId);

            var subscriptionInfo = await _companyService.GetCompanySubscriptionInfo(companyId);

            #region Prepare Add-On Types

            var addOnTypes = new List<string> { PlanTypeCodes.Agents, PlanTypeCodes.Contacts };

            if (_featureFlagsOptions.FlowBuilderMonetisation.IsEnabled)
            {
                addOnTypes.Add(PlanTypeCodes.FlowBuilderFlowEnrolments);
            }

            #endregion

            #region Handle for Enteprise Tier

            if (subscriptionInfo.SubscriptionTier is SubscriptionTier.Enterprise)
            {
                return addOnTypes.Select(x => new AvailableAddOnResponseModel
                    {
                        Id = string.Empty,
                        Type = x,
                        Amount = 0,
                        Currency = "USD",
                        BaseQuantity = 0,
                        PurchasedQuantity = 0,
                        Status = BrowseAddOnStatusCode.ContactSupport,
                    });
            }

            #endregion

            //// Take USD as default currency if the company not lock to any currency yet.
            var (countryTier, currency) = await _subscriptionCountryTierDeterminer.DetermineAsync(companyId, ipAddress, "USD");

            _logger.LogInformation(
                "Company Subscription Info, BasePlanId: {BasePlanId}, Tier: {Tier}, CountryTier: {CountryTier}, Currency: {Currency}",
                subscriptionInfo.BasePlanId,
                subscriptionInfo.SubscriptionTier,
                countryTier,
                currency
            );

            var baseSubscriptionBillRecord = await _companySubscriptionService.GetCurrentBaseSubscriptionBillRecord(companyId);

            var subscriptionPlans = new List<SubscriptionPlan>();

            foreach (var addOnType in addOnTypes)
            {
                var addOnPlans = await GetAddOnPlansAsync(
                    subscriptionInfo.SubscriptionTier,
                    countryTier.ToString(),
                    currency,
                    "monthly",
                    addOnType);

                if (!addOnPlans.Any())
                {
                    _logger.LogWarning("No {AddOnType} plans found for company: {CompanyId}", addOnType, companyId);
                    continue;
                }

                if (addOnType == PlanTypeCodes.FlowBuilderFlowEnrolments)
                {
                    addOnPlans = addOnPlans.OrderBy(x => x.Amount).Take(1);
                }

                subscriptionPlans.AddRange(addOnPlans);
            }

            IList<AvailableAddOnResponseModel> result = new List<AvailableAddOnResponseModel>();

            foreach (var plan in subscriptionPlans)
            {
                _logger.LogInformation("Retrieve Information for Add-On Plan, PlanId: {PlanId}", plan.Id);

                var addOnPlanDefinition = await _planDefinitionService.GetPlanDefinitionAsync(plan.Id);
                var addOnFeatureId = addOnPlanDefinition.FeatureQuantities.First().FeatureId;

                var planFeatureUnitQuantity = await _featureQuantityService.GetPlanFeatureQuantity(plan.Id, addOnFeatureId);
                var purchasedQuantity = await _featureQuantityService.GetAddOnsFeatureQuantityAsync(companyId, addOnFeatureId);
                var status = await GetAddOnResponseStatus(baseSubscriptionBillRecord, subscriptionInfo, companyId, addOnFeatureId);

                var addOn = new AvailableAddOnResponseModel
                {
                    Id = plan.Id,
                    Type = plan.PlanTypeCode,
                    Amount = plan.Amount,
                    Currency = plan.Currency,
                    BaseQuantity = planFeatureUnitQuantity.Quantity,
                    PurchasedQuantity = purchasedQuantity,
                    Status = status,
                };

                result.Add(addOn);
            }

            return result;
        }

        /// <inheritdoc />
        public async Task<IEnumerable<AvailableAddOnResponseModel>> GetAvailableSupportServices(string companyId, string ipAddress)
        {
            var subscriptionInfo = await _companyService.GetCompanySubscriptionInfo(companyId);

            #region Handle for Enteprise Tier

            if (subscriptionInfo.SubscriptionTier is SubscriptionTier.Enterprise)
            {
                //// Hard coded this because there is no actual subscription plan in DB, but still need to display it on web.
                return new[]
                {
                    new AvailableAddOnResponseModel
                    {
                        Id = string.Empty,
                        Type = FeatureId.OnboardingSupport,
                        Amount = 499,
                        Currency = "USD",
                        BaseQuantity = 0,
                        PurchasedQuantity = 0,
                        Status = BrowseSupportServiceStatusCode.IncludedInPlan,
                    },
                    new AvailableAddOnResponseModel
                    {
                        Id = string.Empty,
                        Type = FeatureId.BusinessConsultancyService,
                        Amount = 5988,
                        Currency = "USD",
                        BaseQuantity = 0,
                        PurchasedQuantity = 0,
                        Status = BrowseSupportServiceStatusCode.IncludedInPlan,
                    }
                };
            }

            #endregion

            //// Take USD as default currency if the company not lock to any currency yet.
            var (countryTier, currency) = await _subscriptionCountryTierDeterminer.DetermineAsync(companyId, ipAddress, "USD");

            _logger.LogInformation(
                "Company Subscription Info, BasePlanId: {BasePlanId}, Tier: {Tier}, CountryTier: {CountryTier}, Currency: {Currency}",
                subscriptionInfo.BasePlanId,
                subscriptionInfo.SubscriptionTier,
                countryTier,
                currency
            );

            var baseSubscriptionBillRecord = await _companySubscriptionService.GetCurrentBaseSubscriptionBillRecord(companyId);

            ICollection<SubscriptionPlan> subscriptionPlans = new List<SubscriptionPlan>
            {
                await _subscriptionPlanRepository.GetOnboardingSupportPlan(
                    subscriptionInfo.SubscriptionTier,
                    countryTier.ToString(),
                    currency,
                    SubscriptionPlanVersions.Version10),
                await _subscriptionPlanRepository.GetBusinessConsultancyServicePlan(
                    subscriptionInfo.SubscriptionTier,
                    countryTier.ToString(),
                    currency,
                    SubscriptionPlanVersions.Version10)
            };

            IList<AvailableAddOnResponseModel> result = new List<AvailableAddOnResponseModel>();

            foreach (var plan in subscriptionPlans)
            {
                _logger.LogInformation("Retrieve Information for Support Services Plans, PlanId: {PlanId}", plan.Id);

                var featureId = GetFeatureId(plan);
                var planFeatureUnitQuantity = await _featureQuantityService.GetPlanFeatureQuantity(plan.Id, featureId);
                var featureQuantityInfo = await _featureQuantityService.GetFeatureQuantityInfoAsync(companyId, featureId);
                var status = await GetSupportServicesResponseStatus(baseSubscriptionBillRecord, featureQuantityInfo);

                var addOn = new AvailableAddOnResponseModel
                {
                    Id = plan.Id,
                    Type = featureId,
                    Amount = plan.Amount,
                    Currency = plan.Currency,
                    BaseQuantity = planFeatureUnitQuantity.Quantity,
                    PurchasedQuantity = featureQuantityInfo.PurchasedQuantity,
                    Status = status,
                };

                result.Add(addOn);
            }

            return result;
        }

        /// <inheritdoc />
        public async Task<PurchaseAddOnDetailsResponseModel> GetPurchaseAddOnDetails(string companyId, string subscriptionPlanId, string ipAddress)
        {
            _logger.LogInformation("Get Add-On Details for Purchase. CompanyId: {CompanyId}, SubscriptionPlanId: {SubscriptionPlanId}", companyId, subscriptionPlanId);

            var subscriptionPlan = await _subscriptionPlanRepository.FindById(subscriptionPlanId);

            if (subscriptionPlan == null)
            {
                throw new ApplicationException("Subscription Plan Not Found");
            }

            var subscriptionInfo = await _companyService.GetCompanySubscriptionInfo(companyId);

            _logger.LogInformation(
                "GetPurchaseAddOnDetails - Company Subscription Info, BasePlanId: {BasePlanId}, Tier: {Tier}, CountryTier: {CountryTier}, Currency: {Currency}",
                subscriptionInfo.BasePlanId,
                subscriptionInfo.SubscriptionTier.ToString(),
                subscriptionInfo.SubscriptionCountryTier,
                subscriptionInfo.Currency
            );

            await CheckIsAddOnPurchasable(companyId, subscriptionPlan);

            var featureId = GetFeatureId(subscriptionPlan);
            var basePlanFeatureQuantity = await _featureQuantityService.GetPlanFeatureQuantity(subscriptionInfo.BasePlanId, featureId);
            var planFeatureUnitQuantity = await _featureQuantityService.GetPlanFeatureQuantity(subscriptionPlan.Id, featureId);
            var featureQuantityInfo = await _featureQuantityService.GetFeatureQuantityInfoAsync(companyId, featureId);
            var purchasableQuantity = basePlanFeatureQuantity.MaximumQuantity.HasValue ? basePlanFeatureQuantity.MaximumQuantity.Value - featureQuantityInfo.TotalQuantity : long.MaxValue;

            return new PurchaseAddOnDetailsResponseModel
            {
                SubscriptionPlanId = subscriptionPlan.Id,
                Type = featureId,
                SubscriptionPlanTier = subscriptionInfo.SubscriptionTier.ToString(),
                TotalQuantity = featureQuantityInfo.TotalQuantity,
                QuantityFromBasePlan = featureQuantityInfo.IncludedQuantity,
                QuantityFromPurchasedAddOn = featureQuantityInfo.PurchasedQuantity,
                MaxPurchasableQuantity = purchasableQuantity,
                SubscriptionPlanUnitAmount = subscriptionPlan.Amount,
                SubscriptionPlanCurrency = subscriptionPlan.Currency,
                BaseQuantity = planFeatureUnitQuantity.Quantity
            };
        }

        /// <inheritdoc />
        public async Task<AddOnPurchaseOptionsResponseModel> GetAddOnPurchaseOptions(string companyId, string addOnType, string ipAddress)
        {
            //// Special handle interval for WhatsAppPhoneNumber Add-On
            var subscriptionInfo = await _companyService.GetCompanySubscriptionInfo(companyId);
            var interval = addOnType.EqualsIgnoreCase(PlanTypeCodes.WhatsAppPhoneNumber) ? subscriptionInfo.SubscriptionInterval : "monthly";

            //// Take USD as default currency if the company not lock to any currency yet.
            var (countryTier, currency) = await _subscriptionCountryTierDeterminer.DetermineAsync(companyId, ipAddress, "USD");

            var addOnPlans = await GetAddOnPlansAsync(subscriptionInfo.SubscriptionTier, countryTier.ToString(), currency, interval, addOnType);

            var firstAddOnPlanDefinition = await _planDefinitionService.GetPlanDefinitionAsync(addOnPlans.First().Id);
            var featureId = firstAddOnPlanDefinition.FeatureQuantities.First().FeatureId;

            var basePlanFeatureQuantity = await _featureQuantityService.GetPlanFeatureQuantity(subscriptionInfo.BasePlanId, featureId);
            var featureQuantityInfo = await _featureQuantityService.GetFeatureQuantityInfoAsync(companyId, featureId);

            ICollection<AddOnPurchaseDetailsOptionResponseModel> options = new List<AddOnPurchaseDetailsOptionResponseModel>(addOnPlans.Count());

            foreach (var plan in addOnPlans)
            {
                if (addOnType == PlanTypeCodes.FlowBuilderFlowEnrolments)
                {
                    var planFeatureQuantity = await _featureQuantityService.GetPlanFeatureQuantity(plan.Id, FeatureId.FlowBuilderFlowEnrolment);

                    if (planFeatureQuantity.Quantity <= featureQuantityInfo.PurchasedQuantity)
                    {
                        //// Skip the options for downgrade the flow enrolment limit.
                        continue;
                    }
                }

                var planFeatureUnitQuantity = await _featureQuantityService.GetPlanFeatureQuantity(plan.Id, featureId);

                var purchasableUnit = GetAddOnPurchasableUnit(basePlanFeatureQuantity, featureQuantityInfo, plan);

                var option = new AddOnPurchaseDetailsOptionResponseModel(plan.Id, planFeatureUnitQuantity.Quantity, purchasableUnit, plan.Amount);

                options.Add(option);
            }

            var currentUsageCycle = await _companySubscriptionService.GetMonthlyUsageCycleAsync(companyId);
            var currentCycleUsage = await _companyUsageService.GetCurrentCycleFeatureUsageAsync(companyId, featureId);

            AddOnPurchaseOptionsResponseModel result = new AddOnPurchaseOptionsResponseModel
            {
                Type = addOnType,
                BasePlanIncludedQuantity = featureQuantityInfo.IncludedQuantity,
                PurchasedQuantity = featureQuantityInfo.PurchasedQuantity,
                TotalQuantity = featureQuantityInfo.TotalQuantity,
                Currency = currency,
                CurrentUsageCycle = currentUsageCycle,
                CurrentCycleUsage = currentCycleUsage,
                Options = options
            };

            return result;
        }

        /// <inheritdoc />
        public async Task CheckIsAddOnPurchasable(string companyId, SubscriptionPlan subscriptionPlan)
        {
            var eligibleAddOns = ImmutableList.Create(SubscriptionTier.AddOn, SubscriptionTier.Agent);

            if (!eligibleAddOns.Contains(subscriptionPlan.SubscriptionTier))
            {
                throw new ApplicationException($"{subscriptionPlan.Id} is not an add-on.");
            }

            _logger.LogInformation(
                "Check if company able to purchase add-on. CompanyId: {CompanyId} SubscriptionPlanId: {SubscriptionPlanId}, Version: {Version}",
                companyId,
                subscriptionPlan.Id,
                subscriptionPlan.Version
            );

            if (subscriptionPlan.Version == SubscriptionPlanVersions.Version10)
            {
                var subscriptionInfo = await _companyService.GetCompanySubscriptionInfo(companyId);
                var companySubscriptionTier = subscriptionInfo.SubscriptionTier;

                if (companySubscriptionTier != SubscriptionTier.Pro && companySubscriptionTier != SubscriptionTier.Premium && companySubscriptionTier != SubscriptionTier.Enterprise)
                {
                    throw new ApplicationException("Invalid company SubscriptionTier.");
                }
            }
        }

        /// <inheritdoc />
        public async Task<SubscribePlanResponseModel> SubscribePlans(string companyId, string email, SubscribePlanRequestModel subscribePlansRequest)
        {
            #region Validate Subscription Request

            var addOnIds = subscribePlansRequest.AddOns?.Select(x => x.SubscriptionPlanId).ToList();

            if (addOnIds.GroupBy(x => x).Any(x => x.Count() > 1))
            {
                throw new ApplicationException("Repeated Subscription Plans.");
            }

            var baseSubscriptionBillRecord = await _companySubscriptionService.GetCurrentBaseSubscriptionBillRecord(companyId);

            if (baseSubscriptionBillRecord.IsOnCancelling)
            {
                throw new ApplicationException("Unable to change subscription plan as current subscription plan are on cancelling.");
            }

            var baseSubscriptionPlan = await _subscriptionPlanRepository.FindById(subscribePlansRequest.BaseSubscriptionPlanId);
            var addOns = await _subscriptionPlanRepository.FindByIds(addOnIds);

            var selectedCountryTier = baseSubscriptionPlan.CountryTier;
            var selectedCurrency = baseSubscriptionPlan.Currency;

            if (addOns.Any(x => !x.CountryTier.EqualsIgnoreCase(selectedCountryTier)))
            {
                throw new ApplicationException("Inconsistent Selected Plans Tier.");
            }
            else if (addOns.Any(x => !x.Currency.EqualsIgnoreCase(selectedCurrency)))
            {
                throw new ApplicationException("Inconsistent Selected Plans Currency.");
            }

            var companySubscriptionInfo = await _companyService.GetCompanySubscriptionInfo(companyId);
            var hasSubscriptionCountryTier = companySubscriptionInfo.HasSubscriptionCountryTier;
            var isSubscriptionCountryTierMatch = companySubscriptionInfo.IsSubscriptionCountryTierMatch(selectedCountryTier, selectedCurrency);

            if (hasSubscriptionCountryTier && !isSubscriptionCountryTierMatch)
            {
                throw new ApplicationException("Country Selected Plans Currency Not Matched.");
            }

            #endregion

            string customerId = await _companySubscriptionService.GetStripeCheckoutCustomerId(companyId);

            var metadata = new Dictionary<string, string>();
            metadata["companyId"] = companyId;

            var stripeSubscriptionItems = subscribePlansRequest.AddOns
                .Join(
                    addOns,
                    addOnFromReq => addOnFromReq.SubscriptionPlanId,
                    addOnFromDb => addOnFromDb.Id,
                    (addOnFromReq, addOnFromDb) => GetStripeSubscriptionDataItem(addOnFromDb.StripePlanId, addOnFromReq.Quantity))
                .Append(GetStripeSubscriptionDataItem(baseSubscriptionPlan.StripePlanId, 1))
                .ToList();

            var stripeSubscriptionData = new SessionSubscriptionDataOptions
            {
                Items = stripeSubscriptionItems, Metadata = metadata
            };

            var checkoutSession = new SessionCreateOptions
            {
                Customer = customerId,
                SubscriptionData = stripeSubscriptionData,
                SuccessUrl = subscribePlansRequest.SuccessUrl,
                CancelUrl = subscribePlansRequest.CancelUrl,
                PaymentMethodTypes = new List<string> { "card" },
                ClientReferenceId = companyId,
                AllowPromotionCodes = true,
                Metadata = metadata,
            };

            if (string.IsNullOrWhiteSpace(customerId))
            {
                var countryCode = _countryService.GetCountryCodeByCurrency(selectedCurrency);
                var invoiceFooter = await _companySubscriptionService.GetSleekFlowStripeInvoiceFooter(countryCode);

                if (string.IsNullOrWhiteSpace(invoiceFooter))
                {
                    checkoutSession.CustomerEmail = email;
                }
                else
                {
                    checkoutSession.Customer = await _stripeCustomerService.CreateAsync(email, invoiceFooter);
                }
            }

            try
            {
                var sessionService = _stripeServicesFactory.CreateSessionService();
                var session = await sessionService.CreateAsync(checkoutSession);

                return new SubscribePlanResponseModel
                {
                    RedirectUrl = session.Url
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Error while creating Stripe Checkout Session. RequestBody: {RequestBody}, CreateSessionOptions: {CreateSessionOptions}",
                    JsonConvert.SerializeObject(subscribePlansRequest),
                    JsonConvert.SerializeObject(checkoutSession));

                throw;
            }
        }

        /// <inheritdoc />
        public string GetFeatureId(SubscriptionPlan subscriptionPlan)
        {
            if (subscriptionPlan.SubscriptionTier == SubscriptionTier.Agent)
            {
                return FeatureId.Agents;
            }
            else if (subscriptionPlan.Id.ContainsIgnoreCase("contact"))
            {
                return FeatureId.Contacts;
            }
            else if (subscriptionPlan.Id.ContainsIgnoreCase("onboarding_support"))
            {
                return FeatureId.OnboardingSupport;
            }
            else if (subscriptionPlan.Id.ContainsIgnoreCase("business_consultancy_service"))
            {
                return FeatureId.BusinessConsultancyService;
            }

            return string.Empty;
        }

        /// <inheritdoc />
        public async Task SubmitSubscriptionCancellationSurveyAsync(string companyId, SubmitSubscriptionCancellationSurveyRequest cancellationSurvey)
        {
            CancelSubscriptionRecord cancelSubscriptionRecord = new CancelSubscriptionRecord();
            cancelSubscriptionRecord.CompanyId = companyId;
            cancelSubscriptionRecord.resaon = cancellationSurvey.Reason;
            cancelSubscriptionRecord.others = cancellationSurvey.AdditionalComment;

            await _cancelSubscriptionRecordRepository.CreateAsync(cancelSubscriptionRecord);

            _backgroundJobClient.Enqueue<IInternalHubSpotService>(
                x => x.UpdateCompanyChurnInfomation(companyId, cancellationSurvey.Reason, cancellationSurvey.AdditionalComment));
        }

        /// <inheritdoc />
        public async Task<string> GetMigrationPlan(string companyId, string ipAddress)
        {
            var companySubscriptionInfo = await _companyService.GetCompanySubscriptionInfo(companyId);
            var currentBaseSubscriptionPlan = await _subscriptionPlanRepository.FindById(companySubscriptionInfo.BasePlanId);

            if (currentBaseSubscriptionPlan.Version >= SubscriptionPlanVersions.Version10 || currentBaseSubscriptionPlan.SubscriptionTier != SubscriptionTier.Pro)
            {
                throw new ApplicationException("Invalid Subscription Plan for Migration.");
            }

            var (subscriptionCountryTier, currencies) = await _subscriptionCountryTierDeterminer.DetermineAsync(companyId, ipAddress);
            var currency = currencies.Single(x => string.Equals(x, currentBaseSubscriptionPlan.Currency, StringComparison.OrdinalIgnoreCase));

            var newSubscriptionPlan = await _subscriptionPlanRepository.GetSubscriptionPlanAsync(
                currentBaseSubscriptionPlan.SubscriptionTier,
                subscriptionCountryTier.ToString(),
                currency,
                companySubscriptionInfo.SubscriptionInterval,
                SubscriptionPlanVersions.Version10);


            return newSubscriptionPlan.Id;
        }

        private async Task<string> GetAddOnResponseStatus(BillRecord baseSubscriptionBillRecord, CompanySubscriptionInfo companySubscriptionInfo, string companyId, string featureId)
        {
            var validTiers = ImmutableList.Create(SubscriptionTier.Pro, SubscriptionTier.Premium);

            if (!validTiers.Contains(companySubscriptionInfo.SubscriptionTier))
            {
                return BrowseAddOnStatusCode.SubscriptionTierNotEligibleToPurchase;
            }
            else if (baseSubscriptionBillRecord.IsOnCancelling)
            {
                return BrowseAddOnStatusCode.BaseSubscriptionPlanOnCancelling;
            }

            var isReachedPurchaseLimit = await _featureQuantityService.IsFeatureReachedPurchaseLimit(companyId, companySubscriptionInfo.BasePlanId, featureId);

            if (isReachedPurchaseLimit)
            {
                return BrowseAddOnStatusCode.ReachPurchaseLimit;
            }

            return BrowseAddOnStatusCode.Success;
        }

        private async Task<string> GetSupportServicesResponseStatus(BillRecord baseSubscriptionBillRecord, FeatureQuantityInfo featureQuantityInfo)
        {
            if (featureQuantityInfo.IncludedQuantity > 0)
            {
                return BrowseSupportServiceStatusCode.IncludedInPlan;
            }
            else if (baseSubscriptionBillRecord.IsOnCancelling)
            {
                return BrowseSupportServiceStatusCode.BaseSubscriptionPlanOnCancelling;
            }
            else if (featureQuantityInfo.PurchasedQuantity > 0)
            {
                return BrowseSupportServiceStatusCode.Purchased;
            }

            return BrowseSupportServiceStatusCode.Available;
        }

        private SessionSubscriptionDataItemOptions GetStripeSubscriptionDataItem(string planId, long quantity)
        {
            return new SessionSubscriptionDataItemOptions
            {
                Plan = planId, Quantity = quantity
            };
        }

        private async Task<IEnumerable<SubscriptionPlan>> GetAddOnPlansAsync(
            SubscriptionTier subscriptionTier,
            string countryTier,
            string currency,
            string interval,
            string addOnType)
        {
            if (addOnType.EqualsIgnoreCase(PlanTypeCodes.Agents))
            {
                var result = await _subscriptionPlanRepository.GetAgentAddOnPlan(
                    subscriptionTier,
                    countryTier,
                    currency,
                    interval,
                    SubscriptionPlanVersions.Latest);

                return result == null ? Enumerable.Empty<SubscriptionPlan>() : ImmutableList.Create(result);
            }
            else if (addOnType.EqualsIgnoreCase(PlanTypeCodes.Contacts))
            {
                var result = await _subscriptionPlanRepository.GetContactAddOnPlan(
                    subscriptionTier,
                    countryTier,
                    currency,
                    interval,
                    SubscriptionPlanVersions.Latest);

                return result == null ? Enumerable.Empty<SubscriptionPlan>() : ImmutableList.Create(result);
            }
            else if (addOnType.EqualsIgnoreCase(PlanTypeCodes.FlowBuilderFlowEnrolments))
            {
                var result = await _subscriptionPlanRepository.GetFlowBuilderFlowEnrolmentsPlans(
                    subscriptionTier,
                    countryTier,
                    currency,
                    interval,
                    SubscriptionPlanVersions.Latest);

                return result.OrderBy(x => x.Amount).ToImmutableList();
            }
            else if (addOnType.EqualsIgnoreCase(PlanTypeCodes.OnboardingSupport))
            {
                var result = await _subscriptionPlanRepository.GetOnboardingSupportPlan(
                    subscriptionTier,
                    countryTier,
                    currency,
                    SubscriptionPlanVersions.Latest);

                return ImmutableList.Create(result);
            }
            else if (addOnType.EqualsIgnoreCase(PlanTypeCodes.BusinessConsultancyService))
            {
                var result = await _subscriptionPlanRepository.GetBusinessConsultancyServicePlan(
                    subscriptionTier,
                    countryTier,
                    currency,
                    SubscriptionPlanVersions.Latest);

                return ImmutableList.Create(result);
            }
            else if (addOnType.ContainsIgnoreCase(PlanTypeCodes.WhatsAppPhoneNumber))
            {
                var result = await _subscriptionPlanRepository.GetWhatsAppPhoneNumberAddOnPlan(
                    countryTier,
                    currency,
                    interval,
                    SubscriptionPlanVersions.Latest);

                return result == null ? Enumerable.Empty<SubscriptionPlan>() : ImmutableList.Create(result);
            }

            return Enumerable.Empty<SubscriptionPlan>();
        }

        private long GetAddOnPurchasableUnit(
            FeatureQuantity basePlanFeatureQuantity,
            FeatureQuantityInfo currentFeatureQuantityInfo,
            SubscriptionPlan purchasingPlan)
        {
            if (purchasingPlan.PlanTypeCode == PlanTypeCodes.FlowBuilderFlowEnrolments)
            {
                return 1;
            }
            else if (basePlanFeatureQuantity.MaximumQuantity.HasValue)
            {
                return basePlanFeatureQuantity.MaximumQuantity.Value - currentFeatureQuantityInfo.TotalQuantity;
            }

            return FeatureQuantityConstants.UnlimitedQuantity;
        }
    }
}