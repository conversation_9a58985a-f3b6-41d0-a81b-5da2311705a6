﻿using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Travis_backend.CompanyDomain.ViewModels;
using Travis_backend.ContactDomain.ViewModels;
using Travis_backend.CoreDomain.Models;
using Travis_backend.Enums;
using Travis_backend.Helpers;

namespace Sleekflow.Powerflow.Apis.ViewModels;

public class GetCompanyRequest
{
    public string CompanyName { get; set; }

    public string CompanyId { get; set; }

    public List<string> CompanyIds { get; set; } = new ();

    public string StaffEmail { get; set; }

    public string StaffName { get; set; }

    public string StaffPhoneNumber { get; set; }

    public RangeInput StaffCountRange { get; set; }

    public RangeInput UserContactCountRange { get; set; }

    public RangeInput MonthlyRecurringRevenueRange { get; set; }

    public PercentageRangeInput StaffLimitPercentageRange { get; set; }

    public PercentageRangeInput UserContactLimitPercentageRange { get; set; }

    public DateRangeInput CreateDateRange { get; set; }

    public DateRangeInput StaffLastLoginDateRange { get; set; }

    public List<string> BillPlanIds { get; set; } = new ();

    public List<string> AddOnPlanIds { get; set; } = new ();

    public List<string> ConnectedChannels { get; set; } = new ();

    public List<string> CompanyOwnerIds { get; set; } = new ();

    public List<string> ActivationOwnerIds { get; set; } = new ();

    public List<string> Countries { get; set; } = new ();

    public List<string> Industries { get; set; } = new ();

    public string HubSpotIndustry { get; set; }

    public List<string> LeadSources { get; set; } = new ();

    public List<CompanyType> CompanyTypes { get; set; } = new ();

    // Flow Hub Data
    public RangeInput EnrolmentsRange { get; set; }

    public PercentageRangeInput EnrolmentPercentageRange { get; set; }

    public RangeInput TotalFlowsRange { get; set; }

    public RangeInput ActiveFlowsRange { get; set; }

    public RangeInput EnrolmentsLimitRange { get; set; }

    public RangeInput TotalFlowsLimitRange { get; set; }

    public RangeInput ActiveFlowsLimitRange { get; set; }

    public RangeInput MaxNodesLimitRange { get; set; }

    public RangeInput FlowEnrolmentAddOnMrrRange { get; set; }

    public bool IsGetPurchasedFlowEnrolmentAddOnCompany { get; set; }

    public string Sort { get; set; } = "desc"; // or asc

    public bool IsFilter { get; set; } = false;

    public bool IsGetRecentlyCreatedCompany { get; set; } = false;

    public bool IsGetAllCompany { get; set; } = false;

    public bool IsGetBankTransferEndDay { get; set; } = false;

    public bool IsGetStripeCancelledCompany { get; set; } = false;

    public bool IsExcludeUserProfileCount { get; set; } = false;

    public bool IsOrConditionFilter { get; set; } = false;

    public bool IsFlowHubDataRequired { get; set; } = false;

    public bool IsAllowCacheFlowHubData { get; set; } = true;
}

public class GetCompanyIdNamePairsRequest
{
    public List<string> CompanyIds { get; set; }
}

public class UpdateCompanyTypeRequest
{
    public string CompanyId { get; set; }

    public CompanyType CompanyType { get; set; }
}

public class GetCmsCompanyAdditionalInfosRequest
{
    public List<string> CompanyIds { get; set; }

    public bool HasChurnReason { get; set; } = false;

    public bool HasTier { get; set; } = false;

    public bool HasAllTimeRevenueAnalyticData { get; set; } = true;

    public bool AllowCache { get; set; } = true;
}

public class UpdateCompaniesAllTimeRevenueAnalyticDataRequest
{
    public List<string> CompanyIds { get; set; }
}

public class GetWhatsappInstanceRequest
{
}

public class SetCmsCompanyRemarkRequest
{
    [Required]
    public string CompanyId { get; set; }

    public string Remark { get; set; }
}

public class SetCompanyLeadSourceRequest
{
    [Required]
    public string LeadSource { get; set; }

    [Required]
    public string CompanyId { get; set; }
}

public class SetCompanyIndustryRequest
{
    [Required]
    public string Industry { get; set; }

    [Required]
    public string CompanyId { get; set; }
}

public class SetCompanySubscriptionPlanRequest
{
    [Required]
    public string CompanyId { get; set; }

    [Required]
    public string SubscriptionPlanId { get; set; }

    [Required]
    public DateTime PeriodStart { get; set; }

    [Required]
    public DateTime PeriodEnd { get; set; }

    public long? UpgradeFromBillRecordId { get; set; }

    public long? DowngradeFromBillRecordId { get; set; }

    public bool IsTriggerHubSpot { get; set; } = true;
}

public class AddCompanyAddOnRequest
{
    [Required]
    public string CompanyId { get; set; }

    [Required]
    public string AddOnPlanId { get; set; }

    [Required]
    public DateTime PeriodStart { get; set; }

    [Required]
    public DateTime PeriodEnd { get; set; }

    public long? Quantity { get; set; }

    public long? UpgradeFromBillRecordId { get; set; }

    public long? DowngradeFromBillRecordId { get; set; }

    public bool IsTriggerHubSpot { get; set; } = true;
}

public class UpdateBillRecordRequest
{
    [Required]
    public string CompanyId { get; set; }

    [Required]
    public long BillRecordId { get; set; }

    public string SubscriptionPlanId { get; set; }

    public DateTime? PeriodStart { get; set; }

    public DateTime? PeriodEnd { get; set; }

    public long? Quantity { get; set; }

    public long? UpgradeFromBillRecordId { get; set; }

    public long? DowngradeFromBillRecordId { get; set; }
}

public class RemoveBillRecordRequest
{
    [Required]
    public string CompanyId { get; set; }

    [Required]
    public long BillRecordId { get; set; }
}

public class SetTwilioVerificationStatusRequest
{
    [Required]
    public string CompanyId { get; set; }

    [Required]
    public string TwilioAccountId { get; set; }

    [Required]
    public bool IsVerified { get; set; }
}

public class SetCompanyLimitRequest
{
    [Required]
    public string CompanyId { get; set; }

    public int? AgentCount { get; set; }

    public int? ContactLimit { get; set; }

    public int? WhatsappCount { get; set; }

    public int? MaximumAutomations { get; set; }

    public int? MaximumCampaignMessages { get; set; }

    public int? MaximumNumberOfChannel { get; set; }

    public int? MaximumShopifyStore { get; set; }
}

public class UpdateCompanyUsageLimitOffsetProfileRequest
{
    public string CompanyId { get; set; }

    public CompanyUsageLimitOffsetProfile CompanyUsageLimitOffsetProfile { get; set; } =
        new CompanyUsageLimitOffsetProfile();
}

public class AddTwilioCreditRequest
{
    [Required]
    public string CompanyId { get; set; }

    [Required]
    public string TwilioAccountId { get; set; }

    [Required]
    public decimal AddCreditValue { get; set; }

    public string InvoiceId { get; set; }

    [Required]
    public bool IsInternalTestingUse { get; set; }
}

public class GetTwilioTopUpLogRequest
{
    public string TwilioAccountSid { get; set; }

    public long? TwilioUsageRecordId { get; set; }
}

public class UpdateTwilioSubAccountRequest
{
    [Required]
    public string TwilioAccountId { get; set; }
}

public class ImportCompanyStaffsByCsvRequest : ImportSpreadsheetViewModel
{
    [Required]
    public string CompanyId { get; set; }

    [Description("Set it to true if you want to import with enterprise login type such as ADFS, SAML, etc.")]
    public bool IsEnterpriseUsers { get; set; } = false;
}

public class InvestigateCompanyRequest
{
    [Required]
    public string CompanyId { get; set; }
}

public class DeleteCompanyRequest
{
    [Required]
    public string CompanyId { get; set; }

    public bool IsDeleteInBackground { get; set; } = true;
}

public class DeleteSandboxRequest
{
    [Required]
    public string CompanyId { get; set; }
}

public class ReEnqueueBackgroundTaskRequest
{
    [Required]
    public string CompanyId { get; set; }

    [Required]
    public long BackgroundTaskId { get; set; }
}

public class ActivateCompanyQrCodeRequest
{
    [Required]
    public string CompanyId { get; set; }
}

public class SyncAiUsageLimitToCompanyAccountRequest
{
    [Required]
    public string CompanyId { get; set; }
}

public class ExportConversationSnapshotRequest
{
    [Required]
    public string CompanyId { get; set; }

    public int? Offset { get; set; }

    public int? Limit { get; set; }

    public DateTimeOffset? Start { get; set; } = null;

    public DateTimeOffset? End { get; set; } = null;

    public string FileFormat { get; set; } = "csv";
}

public class GetCompanyStaffsRequest
{
    [Required]
    public string CompanyId { get; set; }
}

public class ChangeCompanyOwnerRequest
{
    [Required]
    public string CompanyId { get; set; }

    [Required]
    public long StaffId { get; set; }
}

public class AddTwilioSubAccountRequest
{
    [Required]
    public string CompanyId { get; set; }

    [Required]
    public string Name { get; set; }

    [Required]
    public string AccountSID { get; set; }

    [Required]
    public string AccountSecret { get; set; }

    [Required]
    public string PhoneNumber { get; set; }
}

public class GetOrUpdateTwilioUsageRecordRequest
{
    [Required]
    public string CompanyId { get; set; }

    [Required]
    public string TwilioAccountId { get; set; }
}

public class DeleteTwilioUsageRecordRequest
{
    [Required]
    public long TwilioUsageRecordId { get; set; }

    [Required]
    public string CompanyId { get; set; }

    [Required]
    public string TwilioAccountId { get; set; }
}

public class DeleteCompanyStaffRequest
{
    [Required]
    public string CompanyId { get; set; }

    [Required]
    public long StaffId { get; set; }
}

public class SyncStaffContactToSleekflowAccountRequest
{
    [Required]
    public string CompanyId { get; set; }
}

public class BulkSyncStaffContactToSleekflowAccountRequest
{
    [Required]
    public List<string> CompanyIds { get; set; }
}

public class GetResetStaffPasswordUrlRequest
{
    public string Email { get; set; }

    [Required]
    public string UserId { get; set; }
}

public class GetCompanyPublicApiKeysRequest
{
    [Required]
    public string CompanyId { get; set; }
}

public class GetCompanyDetailsRequest
{
    [Required]
    public string CompanyId { get; set; }
}

public class UpdateCompanyDetailRequest
{
    [Required]
    public string CompanyId { get; set; }

    [Required]
    public string CompanyName { get; set; }
}

public class DeleteCompanyDetailCacheRequest
{
    [Required]
    public string CompanyId { get; set; }
}

public class IssueCompanyPublicApiKeyRequest : CoreIssueAPIKey
{
}

public class DeleteCompanyPublicApiKeyRequest
{
    [Required]
    public string CompanyId { get; set; }

    [Required]
    public string ApiKey { get; set; }
}

public class SyncWFacebookHistoryRequest
{
    [Required]
    public string CompanyId { get; set; }

    [Required]
    public string PageId { get; set; }
}

public class GetLoginAsSecretRequest
{
    [Required]
    public string CompanyId { get; set; }

    [Required]
    public long StaffId { get; set; }

    [Required]
    public string StaffIdentityId { get; set; }

    public int ValidDurationInMinute { get; set; } = 15;
}

public class SetCompanyTrialDaysRequest
{
    [Required]
    public string CompanyId { get; set; }

    public int? TrialDays { get; set; }
}

public class RangeInput
{
    public int From { private get; set; }

    public int To { private get; set; }

    [JsonIgnore]
    public int RangeFrom => From;

    [JsonIgnore]
    public int RangeTo
    {
        get
        {
            if (From > To)
            {
                return Int32.MaxValue;
            }

            return To;
        }
    }
}

public class PercentageRangeInput
{
    public decimal From { private get; set; }

    public decimal To { private get; set; }

    [JsonIgnore]
    public decimal RangeFrom => From / (decimal) 100.0;

    [JsonIgnore]
    public decimal RangeTo
    {
        get
        {
            if (From > To)
            {
                return 9999999;
            }

            return To / (decimal) 100.0;
        }
    }
}

public class DateRangeInput
{
    public DateTime? From { get; set; }

    public DateTime? To { get; set; }

    [JsonIgnore]
    public DateTime DateRangeFrom
    {
        get
        {
            if (!From.HasValue && To.HasValue)
            {
                return new DateTime(1970, 1, 1, 0, 0, 0);
            }

            return From ?? DateTime.UtcNow;
        }
    }

    [JsonIgnore]
    public DateTime DateRangeTo
    {
        get
        {
            if (From.HasValue && !To.HasValue)
            {
                return DateTime.UtcNow;
            }

            return To?.EndOfDay() ?? DateTime.UtcNow;
        }
    }
}

public class GetStaffListRequest
{
    public List<string> CompanyIds { get; set; }
}

public class ResendInvitationEmailRequest
{
    [Required]
    public string CompanyId { get; set; }

    [Required]
    public string UserId { get; set; }
}

public class ToggleStripeIntegrationRequest
{
    [Required]
    public string CompanyId { get; set; }

    [Required]
    public bool IsEnabled { get; set; }
}

public class ToggleExpressImportRequest
{
    [Required]
    public string CompanyId { get; set; }

    [Required]
    public bool IsEnabled { get; set; }
}