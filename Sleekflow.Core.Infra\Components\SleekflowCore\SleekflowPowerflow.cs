using Pulumi;
using Sleekflow.Core.Infra.Components.Configs;
using Sleekflow.Core.Infra.Components.Models;
using Sleekflow.Core.Infra.Constants;
using Sleekflow.Core.Infra.Utils;
using Web = Pulumi.AzureNative.Web;
using Network = Pulumi.AzureNative.Network;
using Insights = Pulumi.AzureNative.Insights;

namespace Sleekflow.Core.Infra.Components.SleekflowCore;

public class SleekflowPowerflow
{
    private readonly MyConfig _myConfig;
    private readonly List<EnvGroup> _envGroups;
    private readonly ServerConfig _serverConfig;
    private readonly ContainerRegistryOutput _containerRegistryOutput;

    public SleekflowPowerflow(
        MyConfig myConfig,
        List<EnvGroup> envGroups,
        ServerConfig serverConfig,
        ContainerRegistryOutput containerRegistryOutput)
    {
        _myConfig = myConfig;
        _envGroups = envGroups;
        _serverConfig = serverConfig;
        _containerRegistryOutput = containerRegistryOutput;
    }

    public void InitSleekflowPowerflow()
    {
        var imageName = Output.Format(
            $"{_containerRegistryOutput.Registry.LoginServer}/{ServiceNames.GetSleekflowPrefixedShortName(ServiceNames.SleekflowPowerflow)}:{_myConfig.BuildTime}");

        var image = new DockerImages(
            ServiceNames.GetSleekflowPrefixedShortName(ServiceNames.SleekflowPowerflow),
            imageName,
            _containerRegistryOutput
        ).InitDockerImages();

        foreach (var envGroup in _envGroups)
        {
            var resourceGroup = envGroup.ResourceGroup;
            var webApps = envGroup.WebApps;
            var blobStorage = envGroup.BlobStorage;
            var logAnalyticsWorkspace = envGroup.LogAnalyticsWorkspace;

            var regionalConfig = _serverConfig
                .RegionalConfigs
                .First(s => s.LocationName == envGroup.LocationName);

            var appInsights = new Insights.Component(
                ResourceUtils.GetName(
                    $"sleekflow-powerflow-app-insight-{LocationNames.GetShortName(envGroup.LocationName)}",
                    _myConfig),
                new Insights.ComponentArgs
                {
                    ResourceGroupName = resourceGroup.Name,
                    ApplicationType = Insights.ApplicationType.Web,
                    FlowType = "Redfield",
                    RequestSource = "IbizaWebAppExtensionCreate",
                    Kind = "Web",
                    WorkspaceResourceId = logAnalyticsWorkspace.Id
                },
                new CustomResourceOptions
                {
                    Parent = resourceGroup
                });

            var skuConfig = regionalConfig.SkuConfig.SleekflowPowerflow;

            var plan = new Web.AppServicePlan(
                ResourceUtils.GetName(
                    $"sleekflow-powerflow-plan-{LocationNames.GetShortName(envGroup.LocationName)}",
                    _myConfig),
                new Web.AppServicePlanArgs
                {
                    Kind = "Linux",
                    ResourceGroupName = resourceGroup.Name,
                    Sku = new Web.Inputs.SkuDescriptionArgs
                    {
                        Name = skuConfig.Name, Tier = skuConfig.Tier
                    },
                    Reserved = true,
                },
                new CustomResourceOptions
                {
                    Parent = resourceGroup
                });

            var powerflowWebAppName =
                ResourceUtils.GetName(
                    envGroup.FormatAppName(ServiceNames.GetShortName(ServiceNames.SleekflowPowerflow)),
                    _myConfig);

            var sleekflowCore = envGroup.WebApps[ServiceNames.SleekflowCore];

            var appSettings = AppSettingUtils.GetWebAppSettings(
                sleekflowCore.Name,
                envGroup.Redis,
                image,
                envGroup.LocationName,
                resourceGroup,
                appInsights,
                envGroup.SignalR,
                regionalConfig.SleekflowCoreConfig,
                _serverConfig.DefaultSleekflowCoreFrontDoorDomain,
                _containerRegistryOutput,
                logAnalyticsWorkspace);

            appSettings.Add(
                new Web.Inputs.NameValuePairArgs
                {
                    Name = "Hangfire__WorkerCount",
                    Value = regionalConfig.SleekflowCoreConfig.HangfireWorkerConfig.WorkerCount
                });

            var app = new Web.WebApp(
                powerflowWebAppName,
                new Web.WebAppArgs
                {
                    ServerFarmId = plan.Id,
                    Name = powerflowWebAppName,
                    ClientAffinityEnabled = false,
                    ResourceGroupName = resourceGroup.Name,
                    SiteConfig = new Web.Inputs.SiteConfigArgs
                    {
                        AlwaysOn = true,
                        NumberOfWorkers = 1,
                        AppSettings = EnvironmentVariablesUtils.GetDeduplicateEnvironmentVariables(appSettings),
                        HealthCheckPath = "/__health",
                        Cors = new Web.Inputs.CorsSettingsArgs
                        {
                            SupportCredentials = true,
                            AllowedOrigins = new[]
                            {
                                "http://localhost:3000",
                                "https://localhost:3000",
                                "https://powerflow.sleekflow.io",
                                "https://powerflowdev.z7.web.core.windows.net"
                            }
                        },
                        ConnectionStrings = new[]
                        {
                            new Web.Inputs.ConnStringInfoArgs()
                            {
                                Name = "DefaultConnection",
                                ConnectionString = ConnectionStringUtils.GetSqlServerConnStr(
                                    resourceGroup.Name,
                                    envGroup.SqlServerProperties!,
                                    regionalConfig.SleekflowCoreConfig.GeoSqlDb),
                                Type = Web.ConnectionStringType.SQLAzure
                            },
                            new Web.Inputs.ConnStringInfoArgs()
                            {
                                Name = "ReadConnection",
                                ConnectionString = ConnectionStringUtils.GetSqlServerConnStr(
                                    resourceGroup.Name,
                                    envGroup.SqlServerProperties!,
                                    regionalConfig.SleekflowCoreConfig.GeoSqlDb,
                                    isReadOnly: true),
                                Type = Web.ConnectionStringType.SQLAzure
                            },
                            new Web.Inputs.ConnStringInfoArgs()
                            {
                                Name = "StorageConnectionString",
                                ConnectionString = ConnectionStringUtils.GetStorageConnStr(
                                    resourceGroup.Name,
                                    blobStorage.Name,
                                    regionalConfig.SleekflowCoreConfig.GeoStorage),
                                Type = Web.ConnectionStringType.Custom
                            },
                        },
                        LinuxFxVersion =
                            imageName.Apply(n => $"DOCKER|{n}")
                    },
                    Kind = "app,linux,container",
                    Reserved = true,
                    PublicNetworkAccess = "Enabled",
                    HttpsOnly = true,
                },
                new CustomResourceOptions
                {
                    Parent = resourceGroup
                });

            var webTest = Output.Tuple(envGroup.ClientConfig, resourceGroup.Name, appInsights.Name)
                .Apply(
                    values =>
                        new Insights.WebTest(
                            ResourceUtils.GetName(
                                $"sleekflow-powerflow-app-insight-web-test-{LocationNames.GetShortName(envGroup.LocationName)}",
                                _myConfig),
                            new Insights.WebTestArgs
                            {
                                ResourceGroupName = resourceGroup.Name,
                                Frequency = 300,
                                Timeout = 120,
                                Enabled = true,
                                RetryEnabled = true,
                                WebTestName = $"{powerflowWebAppName}-health-check",
                                SyntheticMonitorId = $"{powerflowWebAppName}-health-check",
                                Locations = new[]
                                {
                                    new Insights.Inputs.WebTestGeolocationArgs
                                    {
                                        Location = "apac-hk-hkn-azr"
                                    },
                                    new Insights.Inputs.WebTestGeolocationArgs
                                    {
                                        Location = "us-va-ash-azr"
                                    },
                                    new Insights.Inputs.WebTestGeolocationArgs
                                    {
                                        Location = "apac-sg-sin-azr"
                                    },
                                },
                                Request = new Insights.Inputs.WebTestPropertiesRequestArgs
                                {
                                    HttpVerb = "POST",
                                    RequestUrl = $"https://{powerflowWebAppName}.azurewebsites.net/__health"
                                },
                                Tags = new InputMap<string>
                                {
                                    {
                                        $"hidden-link:/subscriptions/{values.Item1.SubscriptionId}/resourceGroups/{values.Item2}/providers/microsoft.insights/components/{values.Item3}",
                                        "Resource"
                                    }
                                },
                                WebTestKind = Insights.WebTestKind.Standard
                            },
                            new CustomResourceOptions
                            {
                                Parent = resourceGroup
                            }));

            var subnet = new Network.Subnet(
                ResourceUtils.GetName(
                    $"sleekflow-powerflow-app-subnet-{LocationNames.GetShortName(envGroup.LocationName)}",
                    _myConfig),
                new Network.SubnetArgs
                {
                    ResourceGroupName = resourceGroup.Name,
                    VirtualNetworkName = envGroup.VirtualNetwork.Name,
                    AddressPrefix = regionalConfig.VnetConfig.SleekflowPowerflowAddressPrefix,
                    Delegations =
                    {
                        new Network.Inputs.DelegationArgs
                        {
                            Name = "sleekflow-powerflow-app-subnet-delegation",
                            ServiceName = "Microsoft.Web/serverfarms"
                        }
                    },
                },
                new CustomResourceOptions
                {
                    Parent = resourceGroup
                });

            var webAppSwiftVirtualNetworkConnection = new Web.WebAppSwiftVirtualNetworkConnection(
                ResourceUtils.GetName(
                    $"sleekflow-powerflow-app-swift-virtual-network-connection-{LocationNames.GetShortName(envGroup.LocationName)}",
                    _myConfig),
                new Web.WebAppSwiftVirtualNetworkConnectionArgs
                {
                    Name = app.Name, SubnetResourceId = subnet.Id, ResourceGroupName = resourceGroup.Name,
                },
                new CustomResourceOptions
                {
                    Parent = resourceGroup
                });

            webApps.Add(ServiceNames.SleekflowPowerflow, (powerflowWebAppName, app));
        }
    }
}