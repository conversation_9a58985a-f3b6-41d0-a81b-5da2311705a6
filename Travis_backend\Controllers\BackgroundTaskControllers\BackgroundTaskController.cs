﻿using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AutoMapper;
using Hangfire;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Travis_backend.AccountAuthenticationDomain.Models;
using Travis_backend.BackgroundTaskServices;
using Travis_backend.BackgroundTaskServices.Helpers;
using Travis_backend.CommonDomain.ViewModels;
using Travis_backend.ConversationServices;
using Travis_backend.Data.BackgroundTask;
using Travis_backend.Database;
using Travis_backend.Extensions;
using Travis_backend.Models.BackgroundTask;

namespace Travis_backend.Controllers.BackgroundTaskControllers
{
    /// <summary>
    /// Background Task API.
    /// </summary>
    [Route("/background-task/")]
    [Authorize]
    public class BackgroundTaskController : Controller
    {
        private readonly ApplicationDbContext _appDbContext;
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly IMapper _mapper;
        private readonly ILogger _logger;
        private readonly IConfiguration _configuration;
        private readonly ICoreService _coreService;

        public BackgroundTaskController(
            ApplicationDbContext appDbContext,
            UserManager<ApplicationUser> userManager,
            IMapper mapper,
            IConfiguration configuration,
            ILogger<BackgroundTaskController> logger,
            ICoreService coreService)
        {
            _appDbContext = appDbContext;
            _userManager = userManager;
            _mapper = mapper;
            _configuration = configuration;
            _logger = logger;
            _coreService = coreService;
        }

        /// <summary>
        /// Re-enqueue job.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        [HttpPost("{backgroundTaskId}/re-enqueue")]
        public async Task<ActionResult<List<BackgroundTaskViewModel>>> ReEnqueueBackgroundTask(
            [FromRoute]
            long backgroundTaskId)
        {
            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            if (companyUser == null)
            {
                return Unauthorized();
            }

            var backgroundTask = await _appDbContext.BackgroundTasks
                .AsNoTracking()
                .Where(x => x.Id == backgroundTaskId && x.StaffId == companyUser.Id)
                .FirstOrDefaultAsync();

            if (backgroundTask == null)
            {
                return BadRequest(
                    new ResponseViewModel()
                    {
                        message = "Background Task not found."
                    });
            }

            if (backgroundTask.IsCompleted)
            {
                return BadRequest(
                    new ResponseViewModel()
                    {
                        message = "Background Task has completed."
                    });
            }

            BackgroundJob.Enqueue<IBackgroundTaskService>(x => x.Handle(backgroundTask.Id));

            return Ok(backgroundTask.MapToResultViewModel());
        }

        /// <summary>
        /// Get Detail of a Background Task with Result Payload.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        [HttpGet("{backgroundTaskId}")]
        public async Task<ActionResult<List<BackgroundTaskViewModel>>> GetBackgroundTask(
            [FromRoute]
            long backgroundTaskId)
        {
            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            if (companyUser == null)
            {
                return Unauthorized();
            }

            var backgroundTask = await _appDbContext.BackgroundTasks
                .AsNoTracking()
                .Where(x => x.Id == backgroundTaskId && x.StaffId == companyUser.Id)
                .FirstOrDefaultAsync();

            if (backgroundTask == null)
            {
                return BadRequest(
                    new ResponseViewModel()
                    {
                        message = "Not found."
                    });
            }

            return Ok(backgroundTask.MapToResultViewModel());
        }

        /// <summary>
        /// Get List of Background Task by paging, isCompleted, isDismissed.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        [HttpGet("list")]
        public async Task<ActionResult<List<BackgroundTaskViewModel>>> GetBackgroundTasks(
            [FromQuery(Name = "offset")]
            int offset = 0,
            [FromQuery(Name = "limit")]
            int limit = 20,
            [FromQuery]
            bool? isCompleted = null,
            [FromQuery]
            bool? isDismissed = null)
        {
            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            if (companyUser == null)
            {
                return Unauthorized();
            }

            var userBackgroundTasks = await _appDbContext.BackgroundTasks
                .AsNoTracking()
                .Where(x => x.CompanyId == companyUser.CompanyId && x.StaffId == companyUser.Id)
                .WhereIf(isCompleted.HasValue, x => x.IsCompleted == isCompleted)
                .WhereIf(isDismissed.HasValue, x => x.IsDismissed == isDismissed)
                .OrderByDescending(x => x.CreatedAt)
                .Select(
                    x => new BackgroundTask()
                    {
                        Id = x.Id,
                        CompanyId = x.CompanyId,
                        StaffId = x.StaffId,
                        UserId = x.UserId,
                        Total = x.Total,
                        Progress = x.Progress,
                        IsCompleted = x.IsCompleted,
                        IsDismissed = x.IsDismissed,
                        StartedAt = x.StartedAt,
                        CompletedAt = x.CompletedAt,
                        TaskType = x.TaskType,
                        TargetPayload = x.TargetPayload,
                        ResultPayload = x.ResultPayload,
                        ErrorMessage = x.ErrorMessage,
                        CreatedAt = x.CreatedAt,
                        UpdatedAt = x.UpdatedAt,
                    })
                .Skip(offset)
                .Take(limit)
                .ToListAsync();

            var result = userBackgroundTasks.Select(x => x.MapToResultViewModel()).ToList();

            return Ok(result);
        }

        /// <summary>
        /// Get Dismiss a background task for no showing it at UI.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        [HttpPost("dismiss")]
        public async Task<ActionResult<List<BackgroundTaskViewModel>>> DismissBackgroundTask(
            [FromBody]
            BackgroundTaskRequest request)
        {
            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            if (companyUser == null)
            {
                return Unauthorized();
            }

            var userBackgroundTask = await _appDbContext.BackgroundTasks
                .Where(
                    x => x.Id == request.BackgroundTaskId && x.CompanyId == companyUser.CompanyId &&
                         x.StaffId == companyUser.Id)
                .FirstOrDefaultAsync();

            if (userBackgroundTask == null)
            {
                return BadRequest(
                    new ResponseViewModel()
                    {
                        message = "Not found."
                    });
            }

            userBackgroundTask.IsDismissed = true;
            await _appDbContext.SaveChangesAsync();

            return Ok(userBackgroundTask.MapToResultViewModel());
        }
    }
}