﻿using System;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Sleekflow.Apis.FlowHub.Model;
using Travis_backend.CompanyDomain.Services;
using Travis_backend.ContactDomain.Services;
using Travis_backend.ConversationServices;
using Travis_backend.Enums;
using Travis_backend.FlowHubs.Constants;
using Travis_backend.FlowHubs.Exceptions;
using Travis_backend.FlowHubs.Interfaces;

namespace Travis_backend.FlowHubs.Handlers.UpdateContactOwnerRelationshipsAssignments;

public sealed class CompanyRoundRobbinAllStaffsStrategyHandler : IUpdateContactOwnerRelationshipsAssignmentStrategyHandler
{
    public string StrategyName => UpdateContactOwnerRelationshipsAssignmentStrategy.Company_RoundRobbin_All_Staffs;

    private readonly ICompanyService _companyService;
    private readonly IUserProfileService _userProfileService;
    private readonly IConversationMessageService _conversationMessageService;
    private readonly ILogger<CompanyRoundRobbinAllStaffsStrategyHandler> _logger;

    public CompanyRoundRobbinAllStaffsStrategyHandler(
        ICompanyService companyService,
        IUserProfileService userProfileService,
        IConversationMessageService conversationMessageService,
        ILogger<CompanyRoundRobbinAllStaffsStrategyHandler> logger)
    {
        _companyService = companyService;
        _userProfileService = userProfileService;
        _conversationMessageService = conversationMessageService;
        _logger = logger;
    }

    public async Task HandleAsync(UpdateContactOwnerRelationshipsInput input)
    {
        var workflowVersionedId = input.StateIdentity.WorkflowVersionedId;
        var companyId = input.StateIdentity.SleekflowCompanyId;
        var contactId = input.ContactId;
        var assignmentCounter = input.AssignmentCounter!.Value;

        var conversation = await _userProfileService.GetConversationByUserProfileId(
            companyId,
            userProfileId: contactId,
            status: "closed");

        var staffs = await _companyService.GetAllStaffsAsync(companyId);

        var activeStaffs = staffs
            .Where(s => s.Status is StaffStatus.Active)
            .ToList();

        if (activeStaffs.Count == 0)
        {
            _logger.LogWarning(
                "[{HandlerName}] Failed to assign {UserProfileId} for workflow {WorkflowVersionedId} due to no active staffs in company {CompanyId}",
                nameof(CompanyRoundRobbinAllStaffsStrategyHandler),
                contactId,
                workflowVersionedId,
                companyId);

            return;
        }

        var idx = Convert.ToInt32(assignmentCounter % activeStaffs.Count);

        await _conversationMessageService.ChangeConversationAssignee(
            conversation,
            assignee: activeStaffs[idx]);

        await _conversationMessageService.ChangeConversationAssignedTeam(
            conversation,
            companyTeam: null);
    }
}