﻿using System;
using System.Collections.Generic;
using System.Diagnostics.Metrics;
using System.Linq;
using Microsoft.Extensions.Logging;
using Travis_backend.Enums;

namespace Travis_backend.OpenTelemetry.Meters;

public interface IHangfireMeters : IBasicMeters
{
}

public class HangfireMeters : BaseMeters, IHangfireMeters
{
    private const string HangfireMeterName = "hangfire_meters.trigger_type";
    private readonly Dictionary<HangfireStateTypes, Counter<int>> _hangfireCounters;
    private readonly ILogger<HangfireMeters> _logger;

    public HangfireMeters(IMeterFactory meterFactory, ILogger<HangfireMeters> logger)
        : base(meterFactory, logger)
    {
        _logger = logger;
        if (IsMeterEnabled)
        {
            _hangfireCounters = _hangfireStateTypes.ToDictionary(
                pair => pair.Key,
                pair => CreateCounter<int>($"{HangfireMeterName}.{pair.Value}"));
        }
    }

    protected override Counter<int> GetCounter<T>(T name, string? option = null)
    {
        if (name is HangfireStateTypes stateType && _hangfireCounters.TryGetValue(stateType, out var counter))
        {
            _logger.LogInformation(
                "[HangfireStateTypes] {HangfireMeterType} Has Incremented",
                name);

            return counter;
        }

        throw new ArgumentOutOfRangeException(nameof(name), name, null);
    }

    private readonly Dictionary<HangfireStateTypes, string> _hangfireStateTypes = new ()
    {
        {
            HangfireStateTypes.Enqueued, "enqueued"
        },
        {
            HangfireStateTypes.Scheduled, "scheduled"
        },
        {
            HangfireStateTypes.Processing, "processing"
        },
        {
            HangfireStateTypes.Succeeded, "succeeded"
        },
        {
            HangfireStateTypes.Failed, "failed"
        },
        {
            HangfireStateTypes.Deleted, "deleted"
        },
        {
            HangfireStateTypes.Awaiting, "awaiting"
        },
    };
}