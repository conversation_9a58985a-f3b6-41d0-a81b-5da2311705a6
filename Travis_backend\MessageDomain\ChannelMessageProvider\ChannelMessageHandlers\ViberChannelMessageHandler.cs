using System;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Travis_backend.ChannelDomain.Clients;
using Travis_backend.ChannelDomain.Models;
using Travis_backend.ChannelDomain.ViewModels;
using Travis_backend.Constants;
using Travis_backend.ConversationDomain.Models;
using Travis_backend.Database;
using Travis_backend.Enums;
using Travis_backend.MessageDomain.Models;
using Travis_backend.OpenTelemetry.Meters;
using Travis_backend.OpenTelemetry.Meters.Constants;

namespace Travis_backend.MessageDomain.ChannelMessageProvider.ChannelMessageHandlers;

public class ViberChannelMessageHandler : IChannelMessageHandler
{
    private readonly ApplicationDbContext _appDbContext;
    private readonly IConfiguration _configuration;
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly IConversationMeters _conversationMeters;

    public string ChannelType => ChannelTypes.Viber;

    public ViberChannelMessageHandler(
        ApplicationDbContext appDbContext,
        IConfiguration configuration,
        IHttpClientFactory httpClientFactory,
        IConversationMeters conversationMeters)
    {
        _appDbContext = appDbContext;
        _configuration = configuration;
        _httpClientFactory = httpClientFactory;
        _conversationMeters = conversationMeters;
    }

    public async ValueTask<ConversationMessage> SendChannelMessageAsync(
        Conversation conversation,
        ConversationMessage conversationMessage)
    {
        if (conversationMessage.ViberReceiver == null || !string.IsNullOrEmpty(conversationMessage.MessageUniqueID))
        {
            return conversationMessage;
        }

        string messageContent = conversationMessage.MessageContent;

        try
        {
            var viberConfig = await _appDbContext.ConfigViberConfigs.FirstOrDefaultAsync(
                x => x.CompanyId == conversationMessage.CompanyId &&
                     x.ViberBotId == conversationMessage.ViberReceiver.ViberBotId);

            if (viberConfig == null)
            {
                throw new Exception("Viber Channel not found.");
            }

            var httpClient = _httpClientFactory.CreateClient(HttpClientHandlerName.Default);
            var viberBotClient = new ViberBotClient(httpClient, viberConfig.ViberBotToken);

            ViberSendMessageResponse viberBotResponse = null;
            switch (conversationMessage.MessageType)
            {
                case "text":
                    viberBotResponse = await viberBotClient.SendMessageAsync(
                        conversationMessage.ViberReceiver.ViberUserId,
                        new ViberTextMessage()
                        {
                            Text = messageContent
                        },
                        viberConfig.ViberBotSenderName,
                        viberConfig.IconUrl);

                    break;
                case "file":
                    if (conversationMessage.UploadedFiles != null && conversationMessage.UploadedFiles.Count > 0)
                    {
                        var domainName = _configuration.GetValue<String>("Values:DomainName");

                        foreach (var uploadedFile in conversationMessage.UploadedFiles)
                        {
                            var fileName = Path.GetFileName(uploadedFile.Filename);
                            var url =
                                $"{domainName}/Message/File/Private/{uploadedFile.FileId}/{fileName}?mode=redirect";

                            if (uploadedFile.MIMEType.ToLower() == "image/jpeg"
                                || uploadedFile.MIMEType.ToLower() == "image/png"
                                || uploadedFile.MIMEType.ToLower() == "image/gif")
                            {
                                viberBotResponse = await viberBotClient.SendMessageAsync(
                                    conversationMessage.ViberReceiver.ViberUserId,
                                    new ViberPictureMessage()
                                    {
                                        MediaUrl = url,
                                    },
                                    viberConfig.ViberBotSenderName,
                                    viberConfig.IconUrl);
                            }
                            else if (uploadedFile.MIMEType.ToLower() == "video/mp4")
                            {
                                viberBotResponse = await viberBotClient.SendMessageAsync(
                                    conversationMessage.ViberReceiver.ViberUserId,
                                    new ViberVideoMessage()
                                    {
                                        MediaUrl = url, SizeInByte = uploadedFile.FileSize ?? 1024
                                    },
                                    viberConfig.ViberBotSenderName,
                                    viberConfig.IconUrl);
                            }
                            else
                            {
                                viberBotResponse = await viberBotClient.SendMessageAsync(
                                    conversationMessage.ViberReceiver.ViberUserId,
                                    new ViberFileMessage()
                                    {
                                        MediaUrl = url,
                                        FileName = uploadedFile.Filename,
                                        SizeInByte = uploadedFile.FileSize ?? 1024
                                    },
                                    viberConfig.ViberBotSenderName,
                                    viberConfig.IconUrl);
                            }

                            // Send caption
                            if (!string.IsNullOrEmpty(messageContent)
                                && viberBotResponse.Status == ViberApiResponseStatus.Ok)
                            {
                                await viberBotClient.SendMessageAsync(
                                    conversationMessage.ViberReceiver.ViberUserId,
                                    new ViberTextMessage()
                                    {
                                        Text = messageContent
                                    },
                                    viberConfig.ViberBotSenderName,
                                    viberConfig.IconUrl);
                            }
                        }
                    }

                    break;
                default:
                    throw new ArgumentException("Unsupported MessageType.");
            }

            conversationMessage.MessageUniqueID = viberBotResponse.MessageToken.ToString();

            if (viberBotResponse.Status == ViberApiResponseStatus.Ok)
            {
                conversationMessage.Status = MessageStatus.Sent;

                _conversationMeters.IncrementCounter(ChannelTypes.Viber, ConversationMeterOptions.SendSuccess);
            }
            else
            {
                conversationMessage.Status = MessageStatus.Failed;
                conversationMessage.ChannelStatusMessage = viberBotResponse.StatusMessage;

                _conversationMeters.IncrementCounter(ChannelTypes.Viber, ConversationMeterOptions.SendFailed);
            }
        }
        catch (Exception e)
        {
            conversationMessage.Status = MessageStatus.Failed;
            conversationMessage.ChannelStatusMessage = e.Message;
        }

        await _appDbContext.SaveChangesAsync();

        return conversationMessage;
    }
}