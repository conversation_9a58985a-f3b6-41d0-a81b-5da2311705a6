using System.Collections.Generic;
using Newtonsoft.Json;

namespace Travis_backend.TicketingHubDomain.Models.**********************;

public class StatusIndicatorComponent : ConversationIndicatorComponent
{
    [JsonProperty("status")]
    public string Status { get; set; }

    [JsonConstructor]
    public StatusIndicatorComponent(
        string status,
        string interpolationKey,
        string i18NKey)
        : base("status", interpolationKey, i18NKey, null, null, null)
    {
        Status = status;
    }
}