namespace Travis_backend.Database.Services;

public interface IDbContextService
{
    public BaseDbContext GetDbContext();
}

public class DbContextService : IDbContextService
{
    private readonly IPersistenceContext _persistenceContext;
    private readonly ApplicationDbContext _applicationDbContext;
    private readonly ApplicationReadDbContext _applicationReadDbContext;

    /// <summary>
    /// Initializes a new instance of the <see cref="DbContextService"/> class.
    /// To obtain either the read or readwrite instance base on the persistence context.
    /// </summary>
    /// <param name="persistenceContext">The scooped instance to determine and configure the database context.</param>
    /// <param name="applicationDbContext">Default application database context - readwrite. </param>
    /// <param name="applicationReadDbContext">Variance application database context - read only.</param>
    public DbContextService(
        IPersistenceContext persistenceContext,
        ApplicationDbContext applicationDbContext,
        ApplicationReadDbContext applicationReadDbContext)
    {
        _persistenceContext = persistenceContext;
        _applicationDbContext = applicationDbContext;
        _applicationReadDbContext = applicationReadDbContext;
    }

    /// <summary>
    /// To obtain the db context base on the persistence context.
    /// </summary>
    /// <returns>BaseDbContext.</returns>
    public BaseDbContext GetDbContext()
    {
        return _persistenceContext.IsReadWriteTransaction ? _applicationDbContext : _applicationReadDbContext;
    }
}