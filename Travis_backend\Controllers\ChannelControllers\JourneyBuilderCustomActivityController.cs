﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using Hangfire;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json.Linq;
using Travis_backend.AccountAuthenticationDomain.Models;
using Travis_backend.CommonDomain.ViewModels;
using Travis_backend.CompanyDomain.ApiKeyResolvers;
using Travis_backend.Constants;
using Travis_backend.ConversationServices;
using Travis_backend.Enums;
using Travis_backend.Helpers;
using Travis_backend.InternalDomain.ViewModels;
using Travis_backend.MessageDomain.ViewModels;
using Travis_backend.Services.JourneyBuilder;

namespace Travis_backend.Controllers.ChannelControllers
{
    [ApiController]
    [Route("[controller]")]
    [TypeFilter(typeof(ApiKeyExceptionFilter))]
    public class JourneyBuilderCustomActivityController : Controller
    {
        private readonly ILogger<JourneyBuilderCustomActivityController> _logger;
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly ICoreService _coreService;
        private readonly IJourneyBuilderService _journeyBuilderService;
        private readonly IConfiguration _configuration;
        private readonly IApiKeyResolver _journeyBuilderApiKeyResolver;

        public JourneyBuilderCustomActivityController(
            ILogger<JourneyBuilderCustomActivityController> logger,
            UserManager<ApplicationUser> userManager,
            ICoreService coreService,
            IJourneyBuilderService journeyBuilderService,
            IConfiguration configuration,
            ApiKeyResolverFactory apiKeyResolverFactory)
        {
            _logger = logger;
            _userManager = userManager;
            _coreService = coreService;
            _journeyBuilderService = journeyBuilderService;
            _configuration = configuration;
            _journeyBuilderApiKeyResolver = apiKeyResolverFactory.GetApiKeyResolver(ApiKeyTypes.JourneyBuilder);
        }

        // Get or generate API key for Journey Builder
        [HttpPost]
        [Authorize]
        [Route("APIKey/generate")]
        public async Task<IActionResult> GenerateApiKey()
        {
            try
            {
                var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
                if (companyUser.RoleType != StaffUserRole.Admin)
                {
                    return Unauthorized();
                }

                var companyApiKey = await _journeyBuilderApiKeyResolver.GetCompanyApiKeyAsync(companyUser.CompanyId);
                var apiKey = companyApiKey != null
                    ? companyApiKey.APIKey
                    : (await _journeyBuilderApiKeyResolver.GenerateAsync(companyUser.CompanyId, null)).APIKey;

                dynamic result = new JObject();

                result.result = "authenticated";
                result.apiKey = apiKey;
                result.companyName = companyUser.Company.CompanyName;
                result.endpointUrl = _configuration.GetValue<string>("Salesforce:CustomActivityWebApp");

                return Ok(result);
            }
            catch (Exception e)
            {
                return BadRequest(e.Message);
            }
        }

        [HttpPost]
        [Authorize]
        [Route("GetEndpointUrl")]
        public async Task<IActionResult> GetEndpointUrl()
        {
            try
            {
                dynamic result = new JObject();
                result.endpointUrl = _configuration.GetValue<string>("Salesforce:CustomActivityWebApp");

                return Ok(result);
            }
            catch (Exception e)
            {
                return BadRequest(e.Message);
            }
        }

        /// <summary>
        /// Sample endpoint for test API Key authentication and usage.
        /// </summary>
        /// <param name="apiKey">apiKey.</param>
        /// <returns>ApiUsage.</returns>
        [HttpPost]
        [Route("verifyEndpoint")]
        public async Task<IActionResult> VerifyEndpoint(
            [FromQuery]
            string apiKey)
        {
            var journeyBuilderApiKey = await _journeyBuilderApiKeyResolver.AuthenticateAsync(apiKey);
            await _journeyBuilderApiKeyResolver.AddUsageAsync(journeyBuilderApiKey);

            var apiUsage = await _journeyBuilderApiKeyResolver.GetApiUsageAsync(journeyBuilderApiKey);

            return Ok(apiUsage);
        }

        [HttpPost]
        [Route("save")]
        public ActionResult<SuccessResponse> SaveActivity()
        {
            return new SuccessResponse(true);
        }

        [HttpPost]
        [Route("publish")]
        public ActionResult<SuccessResponse> PublishActivity()
        {
            return new SuccessResponse(true);
        }

        [HttpPost]
        [Route("validate")]
        public ActionResult<SuccessResponse> ValidateActivity()
        {
            return new SuccessResponse(true);
        }

        [HttpPost]
        [Route("stop")]
        public ActionResult<SuccessResponse> StopActivity()
        {
            return new SuccessResponse(true);
        }

        /// <summary>
        /// Upload Media File.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        [HttpPost]
        [Route("upload/{apiKey}")]
        public async Task<List<string>> UploadFiles(
            string apiKey,
            [FromForm] UploadExtendedMessageFileViewModel uploadExtendedMessageFileViewModel)
        {
            await _journeyBuilderApiKeyResolver.AuthenticateAsync(apiKey);

            var urls = await _journeyBuilderService.GetFileUrl(apiKey, uploadExtendedMessageFileViewModel);

            return urls;
        }

        [HttpPost]
        [Route("execute")]
        public async Task<ActionResult<ResponseViewModel>> SendMessage()
        {
            using var streamReader = new StreamReader(Request.Body);
            var body = await streamReader.ReadToEndAsync();
            _logger.LogInformation("[SFMC] [execute] request body: {RequestBody}", body);

            try
            {
                // Assuming requestJson is a string containing the JSON request body
                JObject requestObject = JObject.Parse(body);
                JArray inArgumentsArray = (JArray) requestObject["inArguments"];

                if (inArgumentsArray != null && inArgumentsArray.Count > 0)
                {
                    List<JourneyBuilderMessageViewModel> inArgumentsList =
                        inArgumentsArray.ToObject<List<JourneyBuilderMessageViewModel>>();
                    var sendMessageForm = inArgumentsList[0];

                    var journeyBuilderApiKey = await _journeyBuilderApiKeyResolver.AuthenticateAsync(sendMessageForm.ApiKey);
                    await _journeyBuilderApiKeyResolver.AddUsageAsync(journeyBuilderApiKey);

                    ValidatePhoneNumber(sendMessageForm.To);

                    BackgroundJob.Enqueue<IJourneyBuilderService>(
                        x => x.SendCloudApiMessage(sendMessageForm));

                    return Ok();
                }

                return BadRequest(
                    new ResponseViewModel
                    {
                        code = 400, message = "Empty inArgument"
                    });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[SFMC] [execute] failed: {RequestBody}", body);
                return BadRequest(
                    new ResponseViewModel
                    {
                        code = 400, message = ex.Message
                    });
            }
        }

        private void ValidatePhoneNumber(string phoneNumber)
        {
            var normalizedPhoneNumber = PhoneNumberHelper.NormalizePhoneNumber(phoneNumber);

            if (!PhoneNumberHelper.IsValidPhoneNumberContainsCountryCode(normalizedPhoneNumber))
            {
                throw new ArgumentException($"Invalid phone number: {phoneNumber}");
            }
        }
    }
}