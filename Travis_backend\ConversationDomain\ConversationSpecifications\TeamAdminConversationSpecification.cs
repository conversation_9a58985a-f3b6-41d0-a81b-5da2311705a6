using System;
using System.Linq;
using System.Linq.Expressions;
using LinqKit;
using Travis_backend.ConversationDomain.Extensions;
using Travis_backend.ConversationDomain.Models;
using Travis_backend.ConversationDomain.ViewModels;
using static LinqKit.PredicateBuilder;

namespace Travis_backend.ConversationDomain;

public class TeamAdminConversationSpecification : ISpecification<Conversation>
{
    private readonly StaffAccessControlAggregate _staff;

    public TeamAdminConversationSpecification(StaffAccessControlAggregate staff)
    {
        _staff = staff;
    }

    public Expression<Func<Conversation, bool>> ToExpression()
    {
        var managedTeamIds = _staff.AssociatedTeams.Select(team => team.Id).ToList();
        var teamMemberStaffIds = _staff.AssociatedTeams.SelectMany(team => team.TeamMemberStaffIds).ToList();
        var conversation = New<Conversation>(false);

        conversation = conversation.IsUnassigned()
            .Or(conversation.IsAssignedToAnyAssociatedTeam(managedTeamIds))
            .Or(conversation.IsAssignedToStaff(_staff.StaffId))
            .Or(conversation.HasTeamMemberAsCollaborator(teamMemberStaffIds))
            .Or(conversation.HasSpecificStaffAsCollaborator(_staff.StaffId))
            .Or(conversation.HasMentionedStaff(_staff.StaffId));

        return conversation;
    }

    public bool IsSatisfiedBy(Conversation conversation)
    {
        return conversation is not null && ToExpression().Compile().Invoke(conversation);
    }
}