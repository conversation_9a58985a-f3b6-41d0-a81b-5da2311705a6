using System;
using System.Linq;
using System.Threading.Tasks;
using Hangfire;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Travis_backend.Database;
using Travis_backend.Enums;
using Travis_backend.Extensions;
using Travis_backend.MigrationJobs.Attributes;
using Travis_backend.StripeIntegrationDomain.Services;

namespace Travis_backend.Controllers.MigrationJobControllers;

[ApiController]
[MigrationJobAuthorization]
[ApiExplorerSettings(IgnoreApi = true)]
[Route("api/migration/subscriptions")]
public class SubscriptionMigrationController : ControllerBase
{
    private readonly IStripeInvoiceService _stripeInvoiceService;

    private readonly ApplicationDbContext _appDbContext;

    private readonly ILogger<SubscriptionMigrationController> _logger;

    public SubscriptionMigrationController(
        IStripeInvoiceService stripeInvoiceService,
        ApplicationDbContext appDbContext,
        ILogger<SubscriptionMigrationController> logger)
    {
        _stripeInvoiceService = stripeInvoiceService;
        _appDbContext = appDbContext;
        _logger = logger;
    }

    [HttpGet]
    [Route("patch-stripe-subscription-item-id")]
    public IActionResult PatchBillRecordSubscriptionItemId([FromQuery] string stripeSubscriptionId = null, [FromQuery] int limit = 250)
    {
        var jobId = BackgroundJob.Enqueue(() => PatchBillRecordSubscriptionItemIdAsync(stripeSubscriptionId, limit));
        return Ok(
            new
            {
                JobId = jobId
            });
    }

    public async Task PatchBillRecordSubscriptionItemIdAsync(string stripeSubscriptionId, int limit)
    {
        var now = DateTime.UtcNow;

        var billRecords = await _appDbContext.CompanyBillRecords
            .Where(x =>
                !string.IsNullOrWhiteSpace(x.stripe_subscriptionId) &&
                string.IsNullOrWhiteSpace(x.StripeSubscriptionItemId) &&
                x.Status != BillStatus.Inactive &&
                x.Status != BillStatus.Terminated &&
                x.PeriodStart <= now && now < x.PeriodEnd)
            .WhereIf(!string.IsNullOrWhiteSpace(stripeSubscriptionId), x => x.stripe_subscriptionId == stripeSubscriptionId)
            .Take(limit)
            .ToListAsync();

        foreach (var invoiceBillRecords in billRecords.GroupBy(x => x.invoice_Id))
        {
            try
            {
                var invoice = await _stripeInvoiceService.GetInvoiceAsync(invoiceBillRecords.Key);

                foreach (var billRecord in invoiceBillRecords)
                {
                    var lineItem = invoice.Lines.FirstOrDefault(x => x.Id == billRecord.stripeId);

                    if (lineItem == null)
                    {
                        _logger.LogWarning(
                            "[{MethodName}] Cannot find InvoiceLineItem from StripeInvoice. " +
                            "BillRecordId: {BillRecordId}, StripeInvoiceId: {StripeInvoiceId}, InvoiceLineItemId: {InvoiceLineItemId}",
                            nameof(PatchBillRecordSubscriptionItemIdAsync),
                            billRecord.Id,
                            invoiceBillRecords.Key,
                            billRecord.stripeId);

                        continue;
                    }

                    billRecord.StripeSubscriptionItemId = lineItem.SubscriptionItem;
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(
                    ex,
                    "[{MethodName}] Error During Patch SubscriptionItemId." +
                    "StripeInvoiceId: {StripeInvoiceId}. " +
                    "ExceptionMessage: {ExceptionMessage}",
                    nameof(PatchBillRecordSubscriptionItemIdAsync),
                    invoiceBillRecords.Key,
                    ex.Message);
            }
        }

        await _appDbContext.SaveChangesAsync();
    }
}