using System;
using System.Collections.Generic;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.Enums;

namespace Travis_backend.ConversationDomain.ViewModels;

public class StaffAccessControlAggregate
{
    private RolePermission _rolePermission;

    public long StaffId { get; set; }

    public string CompanyId { get; set; }

    [JsonConverter(typeof(StringEnumConverter))]
    public StaffStatus Status { get; set; }

    [JsonConverter(typeof(StringEnumConverter))]
    public StaffUserRole RoleType { get; set; }

    public RolePermission RolePermission
    {
        get => _rolePermission;
        set
        {
            if (value != null && RoleType != value.StaffUserRole)
            {
                throw new InvalidOperationException("Role Permission RoleType must match Staff RoleType");
            }

            _rolePermission = value;
        }
    }

    public List<TeamAccessControlAggregate> AssociatedTeams { get; set; } = new ();
}

public class TeamAccessControlAggregate
{
    public long Id { get; set; }

    public List<long> TeamMemberStaffIds { get; set; }
}