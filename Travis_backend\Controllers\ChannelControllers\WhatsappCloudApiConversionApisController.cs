using System;
using System.ComponentModel.DataAnnotations;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Sleekflow.Apis.MessagingHub.Model;
using Travis_backend.AccountAuthenticationDomain.Models;
using Travis_backend.ConversationServices;
using Travis_backend.Exceptions;
using Travis_backend.Filters;

namespace Travis_backend.Controllers.ChannelControllers;

[Authorize]
[Route("company/whatsapp/cloudapi/conversion-api")]
[TypeFilter(typeof(MessagingHubExceptionFilter))]
[TypeFilter(typeof(ModelStateValidationFilter))]
public class WhatsappCloudApiConversionApisController : ControllerBase
{
    private readonly ICoreService _coreService;
    private readonly UserManager<ApplicationUser> _userManager;
    private readonly IWhatsappCloudApiService _whatsappCloudApiService;

    public WhatsappCloudApiConversionApisController(
        ICoreService coreService,
        UserManager<ApplicationUser> userManager,
        IWhatsappCloudApiService whatsappCloudApiService)
    {
        _coreService = coreService;
        _userManager = userManager;
        _whatsappCloudApiService = whatsappCloudApiService;
    }

    public record SetupWabaDatasetRequest(
        [Required]
        [JsonProperty("messaging_hub_waba_id")]
        string MessagingHubWabaId,
        [Required]
        [JsonProperty("dataset_name")]
        string DatasetName);

    [HttpPost("SetupWabaDataset")]
    [Consumes("application/json")]
    [Produces("application/json")]
    [ProducesResponseType(typeof(WabaDto), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<ActionResult<WabaDto>> SetupWabaDatasetAsync(
        [FromBody] [Required] SetupWabaDatasetRequest request)
    {
        var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (companyUser == null)
        {
            return Unauthorized();
        }

        try
        {
            var result = await _whatsappCloudApiService.SetupWabaDatasetAsync(
                companyUser.CompanyId,
                request.MessagingHubWabaId,
                request.DatasetName,
                companyUser.IdentityId);

            return Ok(result);
        }
        catch (Exception e)
        {
            if (e is SleekflowUiException sleekflowUiException && sleekflowUiException.GetErrorCode() is 404)
            {
                return NotFound(sleekflowUiException.Message);
            }

            return BadRequest(e.Message);
        }
    }
}