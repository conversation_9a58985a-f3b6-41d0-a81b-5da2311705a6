﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Google.Api.Gax.ResourceNames;
using Google.Cloud.Video.Transcoder.V1;

namespace Travis_backend.FileDomain.Services;

public interface IGoogleTranscoderTemplateService
{
    Task<JobTemplate> CreateHdTemplateAsync(
        string templateId,
        string projectId,
        string location,
        TranscoderServiceClient transcoderServiceClient);

    Task<JobTemplate> CreateHdTemplateNoAudioAsync(
        string templateId,
        string projectId,
        string location,
        TranscoderServiceClient transcoderServiceClient);

    Task<JobTemplate> CreateTemplateAudioOnlyAsync(
        string templateId,
        string projectId,
        string location,
        TranscoderServiceClient transcoderServiceClient);

    Task<IList<JobTemplate>> ListAllTemplatesAsync(
        string projectId,
        string location,
        TranscoderServiceClient transcoderServiceClient);

    Task DeleteJobTemplateAsync(
        string templateId,
        string projectId,
        string location,
        TranscoderServiceClient transcoderServiceClient);

    Task<bool> IsTemplateExistAsync(
        string templateId,
        string projectId,
        string location,
        TranscoderServiceClient transcoderServiceClient);

    Task<JobTemplate> GetJobTemplateAsync(
        string templateId,
        string projectId,
        string location,
        TranscoderServiceClient transcoderServiceClient);
}

public class GoogleTranscoderTemplateService : IGoogleTranscoderTemplateService
{
    public GoogleTranscoderTemplateService()
    {
    }

    public async Task<JobTemplate> CreateHdTemplateAsync(
        string templateId,
        string projectId,
        string location,
        TranscoderServiceClient transcoderServiceClient)
    {
        var videoStream = new VideoStream
        {
            H264 = new VideoStream.Types.H264CodecSettings
            {
                BitrateBps = 2500000, FrameRate = 60, HeightPixels = 720, WidthPixels = 1280
            }
        };

        var audioStream = new AudioStream
        {
            Codec = "aac", BitrateBps = 64000
        };


        var elementaryStream1 = new ElementaryStream
        {
            Key = "video_stream", VideoStream = videoStream
        };

        var elementaryStream2 = new ElementaryStream
        {
            Key = "audio_stream", AudioStream = audioStream
        };

        var muxStream = new MuxStream
        {
            Key = "hd",
            Container = "mp4",
            ElementaryStreams =
            {
                "video_stream", "audio_stream"
            }
        };

        var jobConfig = new JobConfig
        {
            ElementaryStreams =
            {
                elementaryStream1, elementaryStream2
            },
            MuxStreams =
            {
                muxStream
            }
        };

        var newJobTemplate = new JobTemplate
        {
            Config = jobConfig
        };

        var locationName = new LocationName(projectId, location);
        var jobTemplate =
            await transcoderServiceClient.CreateJobTemplateAsync(locationName, newJobTemplate, templateId);
        return jobTemplate;
    }


    public async Task<JobTemplate> CreateHdTemplateNoAudioAsync(
        string templateId,
        string projectId,
        string location,
        TranscoderServiceClient transcoderServiceClient)
    {
        var videoStream = new VideoStream
        {
            H264 = new VideoStream.Types.H264CodecSettings
            {
                BitrateBps = 2500000, FrameRate = 60, HeightPixels = 720, WidthPixels = 1280
            }
        };

        var elementaryStream = new ElementaryStream
        {
            Key = "video_stream", VideoStream = videoStream
        };

        var muxStream = new MuxStream
        {
            Key = "hd",
            Container = "mp4",
            ElementaryStreams =
            {
                "video_stream"
            }
        };

        var jobConfig = new JobConfig
        {
            ElementaryStreams =
            {
                elementaryStream
            },
            MuxStreams =
            {
                muxStream
            }
        };

        var newJobTemplate = new JobTemplate
        {
            Config = jobConfig
        };

        var locationName = new LocationName(projectId, location);
        var jobTemplate =
            await transcoderServiceClient.CreateJobTemplateAsync(locationName, newJobTemplate, templateId);
        return jobTemplate;
    }

    public async Task<JobTemplate> CreateTemplateAudioOnlyAsync(
        string templateId,
        string projectId,
        string location,
        TranscoderServiceClient transcoderServiceClient)
    {
        var audioStream = new AudioStream
        {
            Codec = "aac", BitrateBps = 64000
        };

        var elementaryStream = new ElementaryStream
        {
            Key = "audio_stream", AudioStream = audioStream
        };

        var muxStream = new MuxStream
        {
            Key = "audio",
            Container = "mp4",
            ElementaryStreams =
            {
                "audio_stream"
            }
        };

        var jobConfig = new JobConfig
        {
            ElementaryStreams =
            {
                elementaryStream
            },
            MuxStreams =
            {
                muxStream
            }
        };

        var newJobTemplate = new JobTemplate
        {
            Config = jobConfig
        };

        var locationName = new LocationName(projectId, location);
        var jobTemplate =
            await transcoderServiceClient.CreateJobTemplateAsync(locationName, newJobTemplate, templateId);
        return jobTemplate;
    }

    public async Task<IList<JobTemplate>> ListAllTemplatesAsync(
        string projectId,
        string location,
        TranscoderServiceClient transcoderServiceClient)
    {
        var locationName = new LocationName(projectId, location);

        var templatesResponse = transcoderServiceClient.ListJobTemplatesAsync(locationName);

        return await templatesResponse.ToListAsync();
    }

    public async Task DeleteJobTemplateAsync(
        string templateId,
        string projectId,
        string location,
        TranscoderServiceClient transcoderServiceClient)
    {
        var name = JobTemplateName.FromProjectLocationJobTemplate(projectId, location, templateId);

        await transcoderServiceClient.DeleteJobTemplateAsync(name);
    }

    public async Task<bool> IsTemplateExistAsync(
        string templateId,
        string projectId,
        string location,
        TranscoderServiceClient transcoderServiceClient)
    {
        try
        {
            var jobTemplate = await GetJobTemplateAsync(templateId, projectId, location, transcoderServiceClient);
            return jobTemplate != null;
        }
        catch (Exception e)
        {
            return false;
        }
    }

    public async Task<JobTemplate> GetJobTemplateAsync(
        string templateId,
        string projectId,
        string location,
        TranscoderServiceClient transcoderServiceClient)
    {
        var templateName = JobTemplateName.FromProjectLocationJobTemplate(projectId, location, templateId);

        var jobTemplate = await transcoderServiceClient.GetJobTemplateAsync(templateName);

        return jobTemplate;
    }
}