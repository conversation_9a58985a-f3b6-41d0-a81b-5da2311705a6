using Newtonsoft.Json;

namespace Sleekflow.Core.Infra.Perf.Components.Configs.SleekflowCore;

public class SqlPerformanceConfig
{
    [JsonProperty("from_raw_sql")]
    public string FromRawSql { get; set; }

    [JsonProperty("is_and_condition_enabled")]
    public string IsAndConditionEnabled { get; set; }

    [JsonProperty("is_or_condition_enabled")]
    public string IsOrConditionEnabled { get; set; }

    [JsonProperty("is_shopify_order_statistics_enabled")]
    public string IsShopifyOrderStatisticsEnabled { get; set; }

    [JsonProperty("is_sales_performance_enabled")]
    public string IsSalesPerformanceEnabled { get; set; }

    public SqlPerformanceConfig(
        string fromRawSql,
        string isAndConditionEnabled,
        string isOrConditionEnabled,
        string isShopifyOrderStatisticsEnabled,
        string isSalesPerformanceEnabled)
    {
        FromRawSql = fromRawSql;
        IsAndConditionEnabled = isAndConditionEnabled;
        IsOrConditionEnabled = isOrConditionEnabled;
        IsShopifyOrderStatisticsEnabled = isShopifyOrderStatisticsEnabled;
        IsSalesPerformanceEnabled = isSalesPerformanceEnabled;
    }
}