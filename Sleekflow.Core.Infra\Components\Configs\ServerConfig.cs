using Newtonsoft.Json;

namespace Sleekflow.Core.Infra.Components.Configs;

public class ServerConfig
{
    public CorsConfig CorsConfig { get; set; }

    public string DefaultSleekflowCoreDomain { get; set; }

    public string DefaultSleekflowCoreFrontDoorDomain { get; set; }

    public List<RegionalConfig> RegionalConfigs { get; set; }

    public ServerConfig()
    {
        var config = new Pulumi.Config("sleekflow");
        DefaultSleekflowCoreDomain = config.Require("default_sleekflow_core_domain");
        DefaultSleekflowCoreFrontDoorDomain = config.Require("default_sleekflow_core_front_door_domain");
        CorsConfig = JsonConvert.DeserializeObject<CorsConfig>(config.Require("cors"))!;
        RegionalConfigs = JsonConvert.DeserializeObject<List<RegionalConfig>>(config.Require("regional_configs"))!;
    }
}