﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Threading.Tasks;
using AutoMapper;
using Hangfire;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Sleekflow.Apis.CrmHub.Api;
using Sleekflow.Apis.CrmHub.Model;
using Travis_backend.Cache;
using Travis_backend.ChannelDomain.Helpers;
using Travis_backend.ChannelDomain.Repositories;
using Travis_backend.CommonDomain.Repositories;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.CompanyDomain.Services;
using Travis_backend.Constants;
using Travis_backend.ContactDomain.Services;
using Travis_backend.ContactDomain.ViewModels;
using Travis_backend.ConversationDomain.ViewModels;
using Travis_backend.ConversationServices.Models;
using Travis_backend.ConversationServices.ViewModels;
using Travis_backend.Database;
using Travis_backend.Enums;
using Travis_backend.Extensions;
using Travis_backend.FacebookInstagramIntegrationDomain.Models;
using Travis_backend.FacebookInstagramIntegrationDomain.ViewModels;
using Travis_backend.Helpers;
using Travis_backend.SignalR;

namespace Travis_backend.ConversationServices
{
    public interface ILeadAdsServiceService
    {
        Task<string> ConnectFacebookLeadAdsAsync(
            string companyId,
            string pageId,
            string pageName,
            string pageAccessToken,
            string businessIntegrationSystemUserAccessToken);

        Task RemoveFacebookLeadAdsAsync(
            string companyId,
            string pageId);

        Task AdHocLeadGenCheckPeriodically(string pageId, string formId);

        Task AdHocLeadGenCheckPeriodicallyV2(string pageId);

        Task NewLeadAds(Change change, bool trigger = true);

        /// <summary>
        /// Refresh FacebookConfigs status for lead ads by fetching latest token info from Facebook.
        /// For more information,
        /// see <see href="https://developers.facebook.com/docs/facebook-login/guides/%20access-tokens/debugging">this</see>.
        /// </summary>
        /// <param name="companyId">Company ID.</param>
        /// <returns>A list of FacebookConfig.</returns>
        Task<List<FacebookConfig>> RefreshLeadAdsFacebookConfigsStatusByCompanyIdAsync(string companyId);

        /// <summary>
        /// Refresh FacebookConfig status for lead ads stored on SleekFlow by fetching latest token info from Facebook.
        /// For more information,
        /// see <see href="https://developers.facebook.com/docs/facebook-login/guides/%20access-tokens/debugging">this</see>.
        /// </summary>
        /// <param name="pageId">Page ID.</param>
        /// <returns>FacebookConfig.</returns>
        Task<FacebookConfig> RefreshLeadAdsFacebookConfigStatusByPageIdAsync(string pageId);

        /// <summary>
        /// Update Facebook Lead Ads Page Name on SleekFlow side. Will not affect Facebook side.
        /// </summary>
        /// <param name="companyId">Company ID.</param>
        /// <param name="pageId">Page ID.</param>
        /// <param name="pageName">Page Name.</param>
        /// <returns><see cref="Task"/>.</returns>
        Task UpdateFacebookLeadAdsPageNameAsync(
            string companyId,
            string pageId,
            string pageName);

        /// <summary>
        /// Get useful info of a Facebook Lead Gen Form from Facebook Graph API.
        /// </summary>
        /// <param name="httpClient">HttpClient.</param>
        /// <param name="pageId">Page ID.</param>
        /// <param name="pageAccessToken">Page access token.</param>
        /// <param name="formId">Facebook form ID.</param>
        /// <returns><see cref="FacebookLeadGenForm"/>.</returns>
        Task<FacebookLeadGenForm?> GetFacebookLeadGenForm(
            HttpClient httpClient,
            string pageId,
            string pageAccessToken,
            string formId);

        Task FetchAndSyncFacebookLeadGenFormsAsync(
            long facebookConfigId,
            string companyId,
            string pageId,
            string pageAccessToken);
    }

    public class LeadAdsService : ILeadAdsServiceService
    {
        private const string FacebookGraphApiLeadGenFormsUrlTemplate =
            "https://graph.facebook.com/{0}/leadgen_forms?fields=id,questions,name&access_token={1}";

        private readonly ApplicationDbContext _appDbContext;
        private readonly IFacebookService _facebookService;
        private readonly IMapper _mapper;
        private readonly IConfiguration _configuration;
        private readonly ILogger<LeadAdsService> _logger;
        private readonly ISignalRService _signalRService;
        private readonly IUserProfileService _userProfileService;
        private readonly ICompanyService _companyService;
        private readonly IHttpClientFactory _httpClientFactory;
        private readonly IFacebookConfigRepository _facebookConfigRepository;
        private readonly IInstagramChannelRepository _instagramChannelRepository;
        private readonly ICacheManagerService _cacheManagerService;
        private readonly ICompanyInfoCacheService _companyInfoCacheService;
        private readonly ISchemasApi _schemasApi;
        private readonly ISchemafulObjectsApi _schemafulObjectsApi;
        private readonly IIntegrationAlertConfigRepository _integrationAlertConfigRepository;

        public LeadAdsService(
            ApplicationDbContext appDbContext,
            IMapper mapper,
            IConfiguration configuration,
            ILogger<LeadAdsService> logger,
            ISignalRService signalRService,
            IUserProfileService userProfileService,
            ICompanyService companyService,
            IHttpClientFactory httpClientFactory,
            IFacebookConfigRepository facebookConfigRepository,
            IInstagramChannelRepository instagramChannelRepository,
            IFacebookService facebookService,
            ICacheManagerService cacheManagerService,
            ICompanyInfoCacheService companyInfoCacheService,
            ISchemasApi schemasApi,
            ISchemafulObjectsApi schemafulObjectsApi,
            IIntegrationAlertConfigRepository integrationAlertConfigRepository)
        {
            _appDbContext = appDbContext;
            _mapper = mapper;
            _configuration = configuration;
            _logger = logger;
            _signalRService = signalRService;
            _userProfileService = userProfileService;
            _companyService = companyService;
            _httpClientFactory = httpClientFactory;
            _facebookConfigRepository = facebookConfigRepository;
            _instagramChannelRepository = instagramChannelRepository;
            _facebookService = facebookService;
            _cacheManagerService = cacheManagerService;
            _companyInfoCacheService = companyInfoCacheService;
            _schemasApi = schemasApi;
            _schemafulObjectsApi = schemafulObjectsApi;
            _integrationAlertConfigRepository = integrationAlertConfigRepository;
        }

        public async Task<string> ConnectFacebookLeadAdsAsync(
            string companyId,
            string pageId,
            string pageName,
            string pageAccessToken,
            string businessIntegrationSystemUserAccessToken)
        {
            try
            {
                return await ConnectLeadAdsAsync(
                    companyId,
                    pageId,
                    pageName,
                    pageAccessToken,
                    businessIntegrationSystemUserAccessToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[{MethodName}] Company {CompanyId} An error occurred while trying to connect to a Facebook Lead Ads - PageId: {PageId}, PageName: {PageName}, PageAccessToken: {PageAccessToken}. Exception Message: {ExceptionMessage}",
                    nameof(ConnectFacebookLeadAdsAsync),
                    companyId,
                    pageId,
                    pageName,
                    pageAccessToken,
                    ex.Message);

                throw;
            }
        }

        private async Task<string> ConnectLeadAdsAsync(
            string companyId,
            string pageId,
            string pageName,
            string pageAccessToken,
            string businessIntegrationSystemUserAccessToken)
        {
            // Facebook Login For Business.
            var facebookLeadAds =
                await ConnectLeadAdsWithFacebookLoginForBusinessAsync(
                    companyId,
                    pageId,
                    pageName,
                    pageAccessToken,
                    businessIntegrationSystemUserAccessToken);

            if (facebookLeadAds.Status == FacebookStatus.Loading)
            {
                var response = await _httpClientFactory.CreateClient(HttpClientHandlerName.Default).GetAsync(
                    $"https://graph.facebook.com/{facebookLeadAds.PageId}/conversations?" +
                    $"access_token={facebookLeadAds.PageAccessToken}");
                if (response.IsSuccessStatusCode)
                {
                    facebookLeadAds.Status = FacebookStatus.Authenticated;
                    await _appDbContext.SaveChangesAsync();
                }
            }

            await _companyInfoCacheService.RemoveCompanyInfoCache(companyId);
            await _cacheManagerService.DeleteCacheWithConstantKeyAsync("connected_facebook_webhook_entry_ids");

            var leadSourceCustomFields = await _appDbContext.CompanyCustomUserProfileFields
                .Where(x => x.CompanyId == companyId && x.FieldName == "LeadSource")
                .Include(x => x.CustomUserProfileFieldOptions)
                .ThenInclude(x => x.CustomUserProfileFieldOptionLinguals).FirstOrDefaultAsync();

            if (leadSourceCustomFields != null)
            {
                if (leadSourceCustomFields.CustomUserProfileFieldOptions
                        .Where(x => x.Value == "Facebook Lead Ad")
                        .Count() == 0)
                {
                    leadSourceCustomFields.CustomUserProfileFieldOptions.Add(
                        new CustomUserProfileFieldOption
                        {
                            Value = "Facebook Lead Ad",
                            CustomUserProfileFieldOptionLinguals = new List<CustomUserProfileFieldOptionLingual>()
                            {
                                new CustomUserProfileFieldOptionLingual
                                {
                                    DisplayName = "Facebook Lead Ad", Language = "en"
                                },
                                new CustomUserProfileFieldOptionLingual
                                {
                                    DisplayName = "Facebook Lead Ad", Language = "zh-HK"
                                }
                            },
                            Order = 8
                        });

                    var other = leadSourceCustomFields.CustomUserProfileFieldOptions
                        .Where(x => x.Value == "Others")
                        .FirstOrDefault();

                    if (other != null)
                    {
                        other.Order = 9;
                    }
                }

                await _appDbContext.SaveChangesAsync();
            }

            var formId = await _appDbContext.CompanyCustomUserProfileFields
                .Where(x => x.CompanyId == companyId && x.FieldName == "Facebook Form ID")
                .FirstOrDefaultAsync();

            if (formId == null)
            {
                _appDbContext.CompanyCustomUserProfileFields.Add(
                    new CompanyCustomUserProfileField
                    {
                        CompanyId = companyId,
                        FieldName = "Facebook Form ID",
                        Type = FieldDataType.SingleLineText,
                        Order = 99,
                        CustomUserProfileFieldLinguals = new List<CustomUserProfileFieldLingual>()
                        {
                            new CustomUserProfileFieldLingual
                            {
                                DisplayName = "Facebook Form ID", Language = "en"
                            },
                            new CustomUserProfileFieldLingual
                            {
                                DisplayName = "Facebook Form ID", Language = "zh-hk"
                            }
                        },
                        IsDeletable = false,
                        IsEditable = false
                    });
            }

            await _appDbContext.SaveChangesAsync();
            await _companyInfoCacheService.RemoveCompanyInfoCache(companyId);

            // Initial fetch and sync FB Lead Gen Forms
            await FetchAndSyncFacebookLeadGenFormsAsync(
                facebookLeadAds.Id,
                facebookLeadAds.CompanyId,
                facebookLeadAds.PageId,
                facebookLeadAds.PageAccessToken);

            // Periodic fetch and sync FB Lead Gen Forms
            RecurringJob.AddOrUpdate<ILeadAdsServiceService>(
                facebookLeadAds.Id.ToString(),
                x => x.FetchAndSyncFacebookLeadGenFormsAsync(
                    facebookLeadAds.Id,
                    facebookLeadAds.CompanyId,
                    facebookLeadAds.PageId,
                    facebookLeadAds.PageAccessToken),
                "0 * * * *");

            // Periodic fetch and sync FB leads
            RecurringJob.AddOrUpdate<ILeadAdsServiceService>(
                facebookLeadAds.PageId,
                x => x.AdHocLeadGenCheckPeriodicallyV2(facebookLeadAds.PageId),
                "*/15 * * * *");

            return await GetFacebookPageSubscriptionStatusAsync(
                facebookLeadAds.PageId,
                facebookLeadAds.PageAccessToken);
        }

        #region Facebook Login For Business

        private async Task<FacebookConfig> ConnectLeadAdsWithFacebookLoginForBusinessAsync(
            string companyId,
            string pageId,
            string pageName,
            string pageAccessToken,
            string businessIntegrationSystemUserAccessToken)
        {
            var existingFacebookConfig = await _facebookConfigRepository.FindFacebookConfigAsync(companyId, pageId);
            var facebookConfigExists = existingFacebookConfig is not null;

            FacebookConfig facebookLeadAds;

            if (string.IsNullOrEmpty(businessIntegrationSystemUserAccessToken))
            {
                throw new ArgumentException(
                    "Facebook Business Id or Business Integration System User Access Token could not be null or empty");
            }

            var facebookBusinessId =
                await _facebookService.GetBusinessIdAssociatedWithPageAsync(
                    pageId,
                    businessIntegrationSystemUserAccessToken);

            if (facebookConfigExists) // The page has been connected as a facebook channel or facebook lead ads before
            {
                var hasLeadAdsConnected = existingFacebookConfig.SubscribedFields.Contains("leadgen");

                if (hasLeadAdsConnected)
                {
                    facebookLeadAds = await ReconnectExistingLeadAdsWithFacebookLoginForBusinessAsync(
                        pageAccessToken,
                        facebookBusinessId,
                        businessIntegrationSystemUserAccessToken,
                        existingFacebookConfig);
                }
                else
                {
                    facebookLeadAds = await ConnectLeadAdsToFacebookChannelWithFacebookLoginForBusinessAsync(
                        pageName,
                        pageAccessToken,
                        facebookBusinessId,
                        businessIntegrationSystemUserAccessToken,
                        existingFacebookConfig);
                }
            }
            else // The page has not been connected as a facebook channel and facebook lead ads before
            {
                facebookLeadAds =
                    await CreateNewLeadAdsWithFacebookLoginForBusinessAsync(
                        companyId,
                        pageId,
                        pageName,
                        pageAccessToken,
                        facebookBusinessId,
                        businessIntegrationSystemUserAccessToken);
            }

            return facebookLeadAds;
        }

        private async Task<FacebookConfig> ReconnectExistingLeadAdsWithFacebookLoginForBusinessAsync(
            string pageAccessToken,
            string facebookBusinessId,
            string businessIntegrationSystemUserAccessToken,
            FacebookConfig existingFacebookLeadAds)
        {
            existingFacebookLeadAds.FacebookBusinessId = facebookBusinessId;
            existingFacebookLeadAds.BusinessIntegrationSystemUserAccessToken = businessIntegrationSystemUserAccessToken;
            existingFacebookLeadAds.PageAccessToken = pageAccessToken;
            existingFacebookLeadAds.Status = FacebookStatus.Loading;
            existingFacebookLeadAds.IsV2LeadAdsConnection = true;

            await UpdateFacebookLeadAdsConfigAsync(existingFacebookLeadAds);

            await _facebookConfigRepository.UpdateFacebookConfigAsync(existingFacebookLeadAds);

            return existingFacebookLeadAds;
        }

        private async Task<FacebookConfig> ConnectLeadAdsToFacebookChannelWithFacebookLoginForBusinessAsync(
            string pageName,
            string pageAccessToken,
            string facebookBusinessId,
            string businessIntegrationSystemUserAccessToken,
            FacebookConfig existingFacebookChannel)
        {
            existingFacebookChannel.PageName = pageName;
            existingFacebookChannel.PageAccessToken = pageAccessToken;
            existingFacebookChannel.FacebookBusinessId = facebookBusinessId;
            existingFacebookChannel.BusinessIntegrationSystemUserAccessToken = businessIntegrationSystemUserAccessToken;
            existingFacebookChannel.IsV2LeadAdsConnection = true;

            await UpdateFacebookLeadAdsConfigAsync(existingFacebookChannel);

            await _facebookConfigRepository.UpdateFacebookConfigAsync(existingFacebookChannel);

            return existingFacebookChannel;
        }

        private async Task<FacebookConfig> CreateNewLeadAdsWithFacebookLoginForBusinessAsync(
            string companyId,
            string pageId,
            string pageName,
            string pageAccessToken,
            string facebookBusinessId,
            string businessIntegrationSystemUserAccessToken)
        {
            var facebookLeadAds = new FacebookConfig
            {
                PageId = pageId,
                PageName = pageName,
                CompanyId = companyId,
                PageAccessToken = pageAccessToken,
                FacebookBusinessId = facebookBusinessId,
                BusinessIntegrationSystemUserAccessToken = businessIntegrationSystemUserAccessToken,
                IsV2LeadAdsConnection = true
            };

            facebookLeadAds.SubscribedFields = await AssignFacebookLeadAdsSubscribedFieldsAsync(facebookLeadAds);

            await _facebookConfigRepository.CreateFacebookConfigAsync(facebookLeadAds);

            return facebookLeadAds;
        }

        #endregion

        #region Connect Lead Ads

        private async Task UpdateFacebookPageAccessTokenAsync(
            string shortLivedPageAccessToken,
            FacebookConfig facebookLeadAds)
        {
            var longLivedPageAccessToken =
                await ExchangeShortLivedAccessTokenToLongLivedAccessTokenAsync(shortLivedPageAccessToken);

            facebookLeadAds.PageAccessToken = longLivedPageAccessToken.AccessToken;

            if (longLivedPageAccessToken.ExpiresIn > 0)
            {
                facebookLeadAds.ExpireDate = DateTime.UtcNow.AddSeconds(longLivedPageAccessToken.ExpiresIn - 10);
            }
        }

        private async Task<FacebookAppAccessToken> ExchangeShortLivedAccessTokenToLongLivedAccessTokenAsync(
            string shortLivedPageAccessToken)
        {
            var clientId = _configuration.GetValue<string>("Facebook:ClientId");
            var clientSecret = _configuration.GetValue<string>("Facebook:ClientSecret");

            var exchangeShortLivedAccessTokenToLongLivedAccessTokenUri =
                FacebookAccessTokenUriBuilder.GetExchangeShortLivedAccessTokenToLongLivedAccessTokenUri(
                    clientId,
                    clientSecret,
                    shortLivedPageAccessToken);

            var httpClient = _httpClientFactory.CreateClient(HttpClientHandlerName.Default);

            var response = await httpClient.GetAsync(exchangeShortLivedAccessTokenToLongLivedAccessTokenUri);

            if (!response.IsSuccessStatusCode)
            {
                var responseContent = await response.Content.ReadAsStringAsync();
                var errorResponse = JObject.Parse(responseContent).ToString(Formatting.None);

                var errorMessage =
                    $"Failed to Exchange Short Lived Page Access Token To Long Lived Page Access Token. Error Response: {errorResponse}, Request Url: {exchangeShortLivedAccessTokenToLongLivedAccessTokenUri}";

                throw new Exception(errorMessage);
            }

            var content = await response.Content.ReadAsStringAsync();
            var tokenResponse = JsonConvert.DeserializeObject<FacebookAppAccessToken>(content);

            return tokenResponse;
        }

        private async Task UpdateFacebookLeadAdsConfigAsync(FacebookConfig facebookLeadAds)
        {
            facebookLeadAds.SubscribedFields = await AssignFacebookLeadAdsSubscribedFieldsAsync(facebookLeadAds);

            await SubscribeFacebookPageAsync(
                facebookLeadAds.PageId,
                facebookLeadAds.PageAccessToken,
                facebookLeadAds.SubscribedFields);

            facebookLeadAds.ConnectedDateTime = DateTime.UtcNow;
        }

        private async Task<string> AssignFacebookLeadAdsSubscribedFieldsAsync(FacebookConfig facebookChannel)
        {
            var instagramChannel = await _instagramChannelRepository.FindInstagramChannelByPageIdAsync(
                facebookChannel.CompanyId,
                facebookChannel.PageId);

            var facebookChannelCurrentlySubscribedFields = facebookChannel.SubscribedFields != null
                ? facebookChannel.SubscribedFields.Split(",").ToList()
                : new List<string>();

            var allFacebookMessagingFieldsSubscribed = FacebookInstagramChannelSubscribedFieldsHelper
                .FacebookSubscribedFields
                .All(
                    field => facebookChannelCurrentlySubscribedFields
                        .Any(subscribedField => subscribedField.Equals(field, StringComparison.OrdinalIgnoreCase)));

            var facebookInstagramChannelShouldBeSubscribed = new FacebookInstagramChannelShouldBeSubscribed(
                allFacebookMessagingFieldsSubscribed,
                instagramChannel is not null,
                true);

            var subscribedFields = FacebookInstagramChannelSubscribedFieldsHelper
                .ResolveFacebookInstagramChannelsSubscribedFields(facebookInstagramChannelShouldBeSubscribed);

            return string.Join(",", subscribedFields);
        }

        private async Task SubscribeFacebookPageAsync(string pageId, string pageAccessToken, string subscribedFields)
        {
            var httpClient = _httpClientFactory.CreateClient(HttpClientHandlerName.Default);

            var appSubscribeFacebookPageUri =
                FacebookPageAppSubscriptionUriBuilder.GetAppSubscribeFacebookPageUri(
                    pageId,
                    pageAccessToken,
                    subscribedFields);

            var response = await httpClient.PostAsync(appSubscribeFacebookPageUri, null);

            if (!response.IsSuccessStatusCode)
            {
                var responseContent = await response.Content.ReadAsStringAsync();
                var errorResponse = JObject.Parse(responseContent).ToString(Formatting.None);

                var errorMessage =
                    $"Failed to subscribe the Facebook page. Error Response: {errorResponse}, Request Url: {appSubscribeFacebookPageUri}";

                throw new Exception(errorMessage);
            }
        }

        private async Task UnsubscribeFacebookPageAsync(string pageId, string pageAccessToken)
        {
            var httpClient = _httpClientFactory.CreateClient(HttpClientHandlerName.Default);

            var appUnsubscribeFacebookPageUri =
                FacebookPageAppSubscriptionUriBuilder.GetAppUnsubscribeFacebookPageUri(pageId, pageAccessToken);

            var response = await httpClient.DeleteAsync(appUnsubscribeFacebookPageUri);

            if (!response.IsSuccessStatusCode)
            {
                var responseContent = await response.Content.ReadAsStringAsync();
                var errorResponse = JObject.Parse(responseContent).ToString(Formatting.None);

                var errorMessage =
                    $"Failed to unsubscribe the Facebook page. Error Response: {errorResponse}, Request Url: {appUnsubscribeFacebookPageUri}";

                throw new Exception(errorMessage);
            }
        }

        private async Task<string> GetFacebookPageSubscriptionStatusAsync(string pageId, string pageAccessToken)
        {
            var httpClient = _httpClientFactory.CreateClient(HttpClientHandlerName.Default);

            var getFacebookPageAppSubscriptionsStatusUri =
                FacebookPageAppSubscriptionUriBuilder.GetFacebookPageAppSubscriptionsStatusUri(pageId, pageAccessToken);

            var response = await httpClient.GetAsync(getFacebookPageAppSubscriptionsStatusUri);

            if (response.IsSuccessStatusCode)
            {
                return await response.Content.ReadAsStringAsync();
            }

            var responseContent = await response.Content.ReadAsStringAsync();
            var errorResponse = JObject.Parse(responseContent).ToString(Formatting.None);

            var errorMessage =
                $"Failed to unsubscribe the Facebook page. Error Response: {errorResponse}, Request Url: {getFacebookPageAppSubscriptionsStatusUri}";

            throw new Exception(errorMessage);
        }

        #endregion

        public async Task RemoveFacebookLeadAdsAsync(string companyId, string pageId)
        {
            try
            {
                await RemoveLeadAdsAsync(companyId, pageId);
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[{MethodName}] Company {CompanyId} An error occurred while trying to removes a Facebook Lead Ads - PageId: {PageId}. Exception Message: {ExceptionMessage}",
                    nameof(ConnectFacebookLeadAdsAsync),
                    companyId,
                    pageId,
                    ex.Message);

                throw;
            }
        }

        #region Remove Lead Ads

        private async Task RemoveLeadAdsAsync(
            string companyId,
            string pageId)
        {
            var facebookConfig = await _facebookConfigRepository.GetFacebookConfigAsync(
                companyId,
                pageId);

            var hasLeadAdsConnected = facebookConfig.SubscribedFields.Contains("leadgen");

            if (hasLeadAdsConnected)
            {
                facebookConfig.SubscribedFields =
                    RemoveFacebookLeadAdsSubscribedFields(facebookConfig.SubscribedFields);
            }

            // If there is no more subscribed fields for the facebook config we will remove it
            if (string.IsNullOrEmpty(facebookConfig.SubscribedFields))
            {
                try
                {
                    // Check that there is an existing subscription for the page before deleting it from Facebook’s side.
                    var pageSubStatusAsString =
                    await GetFacebookPageSubscriptionStatusAsync(pageId, facebookConfig.PageAccessToken);
                    var pageSubStatus = JObject.Parse(pageSubStatusAsString);

                    if (pageSubStatus.ContainsKey("data")
                        && pageSubStatus.GetValue("data") is JArray dataArray
                        && dataArray.ToObject<object[]>().Length > 0)
                    {
                        await UnsubscribeFacebookPageAsync(pageId, facebookConfig.PageAccessToken);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(
                        ex,
                        "[{MethodName}] Company {CompanyId} An error occurred while trying to unsubscribe a Facebook Lead Ads - PageId: {PageId}. Exception Message: {ExceptionMessage}",
                        nameof(RemoveLeadAdsAsync),
                        companyId,
                        pageId,
                        ex.Message);
                }

                var leadAdsNotificationConfig = await _appDbContext.FacebookLeadAdsNotificationConfigs
                    .FirstOrDefaultAsync(x =>
                        x.CompanyId == facebookConfig.CompanyId
                        && x.FacebookConfigId == facebookConfig.Id);

                if (leadAdsNotificationConfig is not null)
                {
                    _appDbContext.FacebookLeadAdsNotificationConfigs.Remove(leadAdsNotificationConfig);
                }

                _appDbContext.ConfigFacebookConfigs.RemoveRange(facebookConfig);
            }
            else
            {
                await SubscribeFacebookPageAsync(
                    pageId,
                    facebookConfig.PageAccessToken,
                    facebookConfig.SubscribedFields);
            }

            await _appDbContext.SaveChangesAsync();
            await _companyInfoCacheService.RemoveCompanyInfoCache(companyId);
            await _cacheManagerService.DeleteCacheWithConstantKeyAsync("connected_facebook_webhook_entry_ids");

            var company = await GetCompanyAsync(companyId);

            await _signalRService.SignalROnChannelDeleted(
                company,
                new ChannelSignal
                {
                    ChannelName = "facebook"
                });
        }

        private static string RemoveFacebookLeadAdsSubscribedFields(string subscribedFields)
        {
            if (subscribedFields.Contains(",leadgen"))
            {
                subscribedFields = subscribedFields.Replace(",leadgen", string.Empty);
            }

            if (subscribedFields.Contains("leadgen"))
            {
                subscribedFields = subscribedFields.Replace("leadgen", string.Empty);
            }

            return subscribedFields;
        }

        #endregion

        public async Task AdHocLeadGenCheckPeriodically(string pageId, string formId)
        {
            var hasConfig = await _appDbContext
                .ConfigFacebookConfigs
                .AnyAsync(
                    x => x.PageId == pageId && x.SubscribedFields.Contains("leadgen"));

            if (!hasConfig)
            {
                _logger.LogInformation(
                    "AdHocLeadGenCheckPeriodically for Facebook page {PageId} is removed",
                    pageId);

                RecurringJob.RemoveIfExists(pageId);

                return;
            }

            var config = await CheckFacebookLeadAdsConnectionStatusAsync(pageId);

            try
            {
                var httpClient = _httpClientFactory.CreateClient(HttpClientHandlerName.Default);

                var leadFormsResponse = await httpClient.GetStringAsync(
                    $"https://graph.facebook.com/v13.0/{pageId}/leadgen_forms?fields=id,created_time&access_token={config.PageAccessToken}");

                var leadForms = JsonConvert.DeserializeObject<LeadFormsResult>(leadFormsResponse);

                while (true)
                {
                    foreach (var leadForm in leadForms.Data)
                    {
                        BackgroundJob.Enqueue(() => FetchLeadForm(leadForm.Id, config));
                    }

                    if (string.IsNullOrEmpty(leadForms.Paging?.next) || pageId == "1995960893975134")
                    {
                        break;
                    }

                    leadFormsResponse = await httpClient.GetStringAsync(leadForms.Paging.next);
                    leadForms = JsonConvert.DeserializeObject<LeadFormsResult>(leadFormsResponse);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "AdHocLeadGenCheckPeriodically for Facebook page {PageId} error {ExceptionMessage}",
                    pageId,
                    ex.Message);
            }
        }

        private async Task<FacebookConfig> CheckFacebookLeadAdsConnectionStatusAsync(string pageId)
        {
            // Check whether the Facebook connection is still alive.
            FacebookConfig config = null;
            try
            {
                config = await RefreshLeadAdsFacebookConfigStatusByPageIdAsync(pageId);
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "AdHocLeadGenCheckPeriodically: Error refreshing Facebook Lead Ads status for page {PageId}",
                    pageId);

                return config;
            }

            if (config.Status == FacebookStatus.Invalid)
            {
                var helpCenterUrl = _configuration.GetValue<string>("IntegrationAlert:FacebookLeadAdsHelpCenterUrl");
                if (string.IsNullOrWhiteSpace(helpCenterUrl))
                {
                    _logger.LogWarning(
                        "[LeadAdsService] {MethodName}: Missing IntegrationAlert:FacebookLeadAdsHelpCenterUrl",
                        nameof(CheckFacebookLeadAdsConnectionStatusAsync));
                }

                BackgroundJob.Enqueue<EmailNotificationService>(
                    b => b.IntegrationDisconnected(
                        config.CompanyId,
                        "Facebook Lead Ads",
                        helpCenterUrl));

                BackgroundJob.Enqueue(
                    () => SendFacebookLeadAdsDisconnectedWhatsAppNotification(config.CompanyId));

                RecurringJob.RemoveIfExists(pageId);

                _logger.LogInformation(
                    "AdHocLeadGenCheckPeriodically for Facebook page {PageId} is removed",
                    pageId);

                return config;
            }

            return config;
        }

        public async Task SendFacebookLeadAdsDisconnectedWhatsAppNotification(string companyId)
        {
            var alertConfig = await _integrationAlertConfigRepository
                .FindConfigByCompanyIdAsync(companyId);

            if (alertConfig is null
                || alertConfig.PhoneNumbers is null
                || alertConfig.PhoneNumbers.Count == 0)
            {
                return;
            }

            var endpoint = _configuration.GetValue<string>("IntegrationAlert:Endpoint");
            if (string.IsNullOrWhiteSpace(endpoint))
            {
                _logger.LogError(
                    "[LeadAdsService] {MethodName}: Missing IntegrationAlert:Endpoint",
                    nameof(SendFacebookLeadAdsDisconnectedWhatsAppNotification));

                return;
            }

            var apiKey = _configuration.GetValue<string>("IntegrationAlert:ApiKey");
            if (string.IsNullOrWhiteSpace(apiKey))
            {
                _logger.LogError(
                    "[LeadAdsService] {MethodName}: Missing IntegrationAlert:ApiKey",
                    nameof(SendFacebookLeadAdsDisconnectedWhatsAppNotification));

                return;
            }

            var requestUrl = $"{endpoint}?apiKey={apiKey}";

            var vm = new IntegrationDisconnectedNotificationViewModel
            {
                IntegrationName = "Facebook Lead Ads",
                ToPhoneNumbers = alertConfig.PhoneNumbers
            };

            try
            {
                var httpClient = _httpClientFactory.CreateClient(HttpClientHandlerName.Default);

                var response = await httpClient.PostAsJsonAsync(requestUrl, vm);
                if (!response.IsSuccessStatusCode)
                {
                    throw new Exception(await response.Content.ReadAsStringAsync());
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[LeadAdsService] {MethodName}: Failed to send phone notification for Facebook Lead Ads Disconnection error {ExceptionMessage}",
                    nameof(SendFacebookLeadAdsDisconnectedWhatsAppNotification),
                    ex.Message);
            }
        }

        public async Task AdHocLeadGenCheckPeriodicallyV2(string pageId)
        {
            var hasConfig = await _appDbContext
                .ConfigFacebookConfigs
                .AnyAsync(
                    x => x.PageId == pageId && x.SubscribedFields.Contains("leadgen"));

            if (!hasConfig)
            {
                _logger.LogInformation(
                    "AdHocLeadGenCheckPeriodicallyV2 for Facebook page {PageId} is removed",
                    pageId);

                RecurringJob.RemoveIfExists(pageId);

                return;
            }

            // Verify the Facebook connection is still alive.
            var config = await CheckFacebookLeadAdsConnectionStatusAsync(pageId);

            try
            {
                var httpClient = _httpClientFactory.CreateClient(HttpClientHandlerName.Default);

                var leadFormsResponse = await httpClient.GetStringAsync(
                    string.Format(
                        FacebookGraphApiLeadGenFormsUrlTemplate,
                        pageId,
                        config.PageAccessToken));

                var leadForms = JsonConvert.DeserializeObject<LeadFormsResult>(leadFormsResponse);

                while (true)
                {
                    foreach (var leadForm in leadForms.Data)
                    {
                        BackgroundJob.Enqueue(() => FetchLeadFormV2(leadForm.Id, config));
                    }

                    if (string.IsNullOrEmpty(leadForms.Paging?.next) || pageId == "1995960893975134")
                    {
                        break;
                    }

                    leadFormsResponse = await httpClient.GetStringAsync(leadForms.Paging.next);
                    leadForms = JsonConvert.DeserializeObject<LeadFormsResult>(leadFormsResponse);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "AdHocLeadGenCheckPeriodicallyV2 for Facebook page {PageId} error {ExceptionMessage}",
                    pageId,
                    ex.Message);
            }
        }

        public async Task FetchLeadForm(string formId, FacebookConfig? config)
        {
            var facebookLeadAdsFormWithCompletedSetup =
                await _appDbContext.FacebookLeadAdsForms.FirstOrDefaultAsync(
                    f => f.FacebookConfigId == config.Id
                         && f.FacebookFormId == formId
                         && f.SetupStatus == FacebookLeadAdsFormSetupStatus.Completed);
            if (facebookLeadAdsFormWithCompletedSetup is not null)
            {
                await FetchLeadFormV2(formId, config);
            }

            var httpClient = _httpClientFactory.CreateClient(HttpClientHandlerName.Default);

            var leadResponse = await httpClient.GetStringAsync(
                $"https://graph.facebook.com/{formId}/leads?fields=id,created_time,field_data,form_id&access_token={config.PageAccessToken}");
            var leadGens = JsonConvert.DeserializeObject<LeadFormResult>(leadResponse);

            while (true)
            {
                foreach (var lead in leadGens.Data.Where(
                             x => x.CreatedTime.ToUniversalTime() > DateTime.UtcNow.AddMinutes(-30)))
                {
                    await SaveLeadGen(lead, config.CompanyId);
                }

                if (string.IsNullOrEmpty(leadGens.Paging?.next))
                {
                    break;
                }

                leadResponse = await httpClient.GetStringAsync(leadGens.Paging.next);
                leadGens = JsonConvert.DeserializeObject<LeadFormResult>(leadResponse);
            }
        }

        public async Task FetchLeadFormV2(string formId, FacebookConfig facebookConfig)
        {
            await FetchAndSyncFacebookLeadGenFormsAsync(
                facebookConfig.Id,
                facebookConfig.CompanyId,
                facebookConfig.PageId,
                facebookConfig.PageAccessToken);

            var httpClient = _httpClientFactory.CreateClient(HttpClientHandlerName.Default);

            var leadFormResponse = await httpClient.GetStringAsync(
                $"https://graph.facebook.com/{formId}/leads?fields=id,created_time,field_data,form_id&access_token={facebookConfig.PageAccessToken}");
            var leadForm = JsonConvert.DeserializeObject<LeadFormResult>(leadFormResponse);

            var facebookLeadAdsFormWithCompletedSetup = await _appDbContext.FacebookLeadAdsForms
                .AsNoTracking()
                .Include(f => f.FieldMappings)
                .FirstOrDefaultAsync(
                    f =>
                        f.CompanyId == facebookConfig.CompanyId
                        && f.FacebookConfigId == facebookConfig.Id
                        && f.FacebookFormId == formId
                        && f.SetupStatus == FacebookLeadAdsFormSetupStatus.Completed);

            if (facebookLeadAdsFormWithCompletedSetup is null)
            {
                return;
            }

            while (true)
            {
                foreach (var lead in leadForm.Data.Where(
                             x => x.CreatedTime.ToUniversalTime() > DateTime.UtcNow.AddMinutes(-30)))
                {
                    await SyncContactAndCustomObjectWithLead(
                        facebookLeadAdsFormWithCompletedSetup,
                        facebookConfig.PageId,
                        facebookConfig.PageAccessToken,
                        lead,
                        facebookConfig.CompanyId);
                }

                if (string.IsNullOrEmpty(leadForm.Paging?.next))
                {
                    break;
                }

                leadFormResponse = await httpClient.GetStringAsync(leadForm.Paging.next);
                leadForm = JsonConvert.DeserializeObject<LeadFormResult>(leadFormResponse);
            }
        }

        public async Task NewLeadAds(Change change, bool trigger = true)
        {
            // var MessagesResponse = await Client.GetStringAsync($"https://graph.facebook.com/{change.Value.ThreadId}?fields=messages&access_token={messengerAccessToken}");
            var facebookConfig = await _appDbContext.ConfigFacebookConfigs.Where(x => x.PageId == change.Value.PageId)
                .FirstOrDefaultAsync();

            if (facebookConfig == null)
            {
                return;
            }

            var company = await _appDbContext.CompanyCompanies.Where(x => x.Id == facebookConfig.CompanyId)
                .FirstOrDefaultAsync();

            var messengerAccessToken = facebookConfig.PageAccessToken;

            var httpClient = _httpClientFactory.CreateClient(HttpClientHandlerName.Default);

            try
            {
                var leadResponse = await httpClient.GetStringAsync(
                    $"https://graph.facebook.com/v13.0/{change.Value.LeadGenId}?access_token={messengerAccessToken}");
                var lead = JsonConvert.DeserializeObject<LeadInfo>(leadResponse);

                lead.FormId = change.Value.FormId;

                var facebookLeadAdsFormWithCompletedSetup = await _appDbContext.FacebookLeadAdsForms
                    .AsNoTracking()
                    .Include(f => f.FieldMappings)
                    .FirstOrDefaultAsync(f => f.FacebookConfigId == facebookConfig.Id
                                                                && f.SetupStatus == FacebookLeadAdsFormSetupStatus.Completed);
                if (facebookLeadAdsFormWithCompletedSetup is not null)
                {
                    await SyncContactAndCustomObjectWithLead(
                        facebookLeadAdsFormWithCompletedSetup,
                        facebookConfig.PageId,
                        facebookConfig.PageAccessToken,
                        lead,
                        facebookLeadAdsFormWithCompletedSetup.CompanyId);

                    return;
                }

                if (!facebookConfig.IsV2LeadAdsConnection)
                {
                    // work for sleekflow
                    await SaveLeadGen(lead, company.Id);
                }

                _logger.LogInformation(
                    "LeadResult: {LeadResponse}",
                    JsonConvert.SerializeObject(leadResponse));
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[{MethodName}] error for page ID {ChangeValuePageId}, form ID {ChangeValueFormId}," +
                    " lead gen id: {ChangeValueLeadGenId}, ex: {ExceptionMessage}",
                    nameof(NewLeadAds),
                    change.Value.PageId,
                    change.Value.FormId,
                    change.Value.LeadGenId,
                    ex.Message);
            }
        }

        private async Task SaveLeadGen(LeadInfo? lead, string companyId)
        {
            try
            {
                if (lead == null)
                    return;

                //Id fields
                var email = lead.FieldData.FirstOrDefault(x => x.Name.ContainsIgnoreCase("email")) ??
                            lead.FieldData.FirstOrDefault(x => x.Name.Contains("電郵"));

                var phoneNumber = lead.FieldData.FirstOrDefault(x => x.Name.Contains("phone_number")) ??
                                  lead.FieldData.FirstOrDefault(x => x.Name.Contains("Phone Number")) ??
                                  lead.FieldData.FirstOrDefault(x => x.Name.Contains("電話"));

                var fullName = lead.FieldData.FirstOrDefault(x => x.Name.Contains("full_name")) ??
                               lead.FieldData.FirstOrDefault(x => x.Name.Contains("Full Name")) ??
                               lead.FieldData.FirstOrDefault(x => x.Name.Contains("全名"));

                var companyName = lead.FieldData.FirstOrDefault(x => x.Name.Contains("company_name")) ??
                                  lead.FieldData.FirstOrDefault(x => x.Name.Contains("Company Name")) ??
                                  lead.FieldData.FirstOrDefault(x => x.Name.Contains("公司名稱"));

                var firstName = lead.FieldData.FirstOrDefault(x => x.Name.Contains("first_name")) ??
                                lead.FieldData.FirstOrDefault(x => x.Name.Contains("First Name"));

                var lastName = lead.FieldData.FirstOrDefault(x => x.Name.Contains("last_name")) ??
                               lead.FieldData.FirstOrDefault(x => x.Name.Contains("Last Name"));

                var country = lead.FieldData.FirstOrDefault(x => x.Name.ContainsIgnoreCase("country"));

                var jobTitle = lead.FieldData.FirstOrDefault(x => x.Name.Contains("job_title")) ??
                               lead.FieldData.FirstOrDefault(x => x.Name.Contains("Job Title"));

                var newProfile = new NewProfileViewModel
                {
                    UserProfileFields = new List<AddCustomFieldsViewModel>()
                    {
                        new AddCustomFieldsViewModel
                        {
                            CustomFieldName = "LeadSource", CustomValue = "Facebook Lead Ad"
                        }
                    }
                };

                if (email != null)
                {
                    newProfile.Email = email.Values.FirstOrDefault();
                }

                if (phoneNumber != null)
                {
                    newProfile.WhatsAppPhoneNumber = phoneNumber.Values.FirstOrDefault();

                    var contactCountry =
                        await _appDbContext.CompanyCompanies.Where(x => x.Id == companyId)
                            .Select(x => x.CompanyCountry)
                            .FirstOrDefaultAsync();

                    newProfile.WhatsAppPhoneNumber =
                        PhoneNumberHelper.GetValidPhoneNumberWithCountryCodeForImportContact(
                            newProfile.WhatsAppPhoneNumber,
                            contactCountry);
                }

                //Find the existing contact
                //If the contact created date is older than lead gen date then we skip
                var existingContact = await _appDbContext.UserProfiles
                    .Where(x => x.CompanyId == companyId)
                    .WhereIf(
                        !string.IsNullOrEmpty(newProfile.WhatsAppPhoneNumber),
                        x => x.PhoneNumber == newProfile.WhatsAppPhoneNumber)
                    .WhereIf(
                        string.IsNullOrEmpty(newProfile.WhatsAppPhoneNumber) && !string.IsNullOrEmpty(newProfile.Email),
                        x => x.Email == newProfile.Email)
                    .Select(
                        x => new
                        {
                            x.Id, x.PhoneNumber, x.Email, x.CreatedAt
                        }).FirstOrDefaultAsync();

                if (existingContact != null &&
                    existingContact.CreatedAt.ToUniversalTime() > lead.CreatedTime.ToUniversalTime())
                    return;

                // system fields list
                var defaultFields = new List<string>
                {
                    "full_name",
                    "Full Name",
                    "email",
                    "Email",
                    "phone_number",
                    "Phone Number",
                    "company_name",
                    "Company Name",
                    "first_name",
                    "First Name",
                    "last_name",
                    "Last Name",
                    "country",
                    "Country",
                    "Job_title",
                    "Job Title",
                    "全名",
                    "電郵地址",
                    "電話號碼",
                    "公司名稱",
                };

                foreach (var field in lead.FieldData)
                {
                    if (defaultFields.Contains(field.Name))
                    {
                        continue;
                    }

                    // Add new fields if not exist in company
                    if (!await _companyService.IsCustomUserProfileFieldExist(companyId, field.Name))
                    {
                        await _companyService.UpdateCustomUserProfileFields(
                            companyId,
                            new List<CompanyCustomUserProfileField>
                            {
                                new CompanyCustomUserProfileField
                                {
                                    FieldName = field.Name,
                                    Order = 999,
                                    Type = FieldDataType.SingleLineText,
                                    IsEditable = false,
                                    IsDeletable = false,
                                    CustomUserProfileFieldLinguals = new List<CustomUserProfileFieldLingual>
                                    {
                                        new CustomUserProfileFieldLingual
                                        {
                                            DisplayName = field.Name, Language = "en"
                                        }
                                    }
                                }
                            });
                    }

                    newProfile.UserProfileFields.Add(
                        new AddCustomFieldsViewModel
                        {
                            CustomFieldName = field.Name, CustomValue = field.Values.FirstOrDefault()
                        });
                }

                if (fullName != null)
                {
                    newProfile.FirstName = fullName.Values.FirstOrDefault();
                }

                if (lastName != null)
                {
                    newProfile.LastName = lastName.Values.FirstOrDefault();
                }

                if (firstName != null)
                {
                    newProfile.FirstName = firstName.Values.FirstOrDefault();
                }

                if (companyName != null)
                {
                    newProfile.UserProfileFields.Add(
                        new AddCustomFieldsViewModel
                        {
                            CustomFieldName = "CompanyName", CustomValue = companyName.Values.FirstOrDefault()
                        });
                }

                if (country != null)
                {
                    newProfile.UserProfileFields.Add(
                        new AddCustomFieldsViewModel
                        {
                            CustomFieldName = "Country", CustomValue = country.Values.FirstOrDefault()
                        });
                }

                if (jobTitle != null)
                {
                    newProfile.UserProfileFields.Add(
                        new AddCustomFieldsViewModel
                        {
                            CustomFieldName = "JobTitle", CustomValue = jobTitle.Values.FirstOrDefault()
                        });
                }

                newProfile.UserProfileFields.Add(
                    new AddCustomFieldsViewModel
                    {
                        CustomFieldName = "Facebook Form ID", CustomValue = lead.FormId
                    });

                await _userProfileService.AddOrUpdateUserProfile(companyId, newProfile);
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[LeadAds {MethodName}] Company {CompanyId} unable to add Lead Ads for form id {FormId}: {ExceptionMessage}. Payload {LeadGenPayload}",
                    nameof(SaveLeadGen),
                    companyId,
                    lead?.FormId,
                    ex.Message,
                    JsonConvert.SerializeObject(lead));
            }
        }

        private async Task SyncContactAndCustomObjectWithLead(
            FacebookLeadAdsForm facebookLeadAdsForm,
            string pageId,
            string pageAccessToken,
            LeadInfo? lead,
            string companyId)
        {
            try
            {
                if (lead == null)
                {
                    return;
                }

                var facebookLeadAdsFormFieldMappings = facebookLeadAdsForm.FieldMappings;

                var schema = (await _schemasApi.SchemasGetSchemaPostAsync(
                    getSchemaInput: new GetSchemaInput(
                        sleekflowCompanyId: companyId,
                        id: facebookLeadAdsForm.CrmHubSchemaId))).Data.Schema;

                var leadFieldData = lead.FieldData;

                var form = await GetFacebookLeadGenForm(
                    _httpClientFactory.CreateClient(HttpClientHandlerName.Default),
                    pageId,
                    pageAccessToken,
                    lead.FormId);
                var formQuestions = form!.Questions;

                var newUserProfile = new NewProfileViewModel
                {
                    UserProfileFields = new List<AddCustomFieldsViewModel>
                    {
                        new()
                        {
                            CustomFieldName = "LeadSource", CustomValue = "Facebook Lead Ad"
                        }
                    }
                };

                var phoneNumberFieldId = (await _appDbContext.CompanyCustomUserProfileFields
                    .FirstOrDefaultAsync(x => x.CompanyId == companyId
                                            && x.Type == FieldDataType.PhoneNumber
                                            && x.IsDeletable == false)).Id;
                var phoneNumberFieldMapping = facebookLeadAdsFormFieldMappings
                    .Find(m => m.SleekflowFieldId == phoneNumberFieldId);
                if (phoneNumberFieldMapping is null)
                {
                    // do not sync if phone number is not mapped
                    return;
                }

                var phoneNumberFieldQuestion = formQuestions
                    .Find(q => q.Id == phoneNumberFieldMapping.FacebookLeadAdsFormFieldId);
                if (phoneNumberFieldQuestion is null)
                {
                    // do not sync if field mapped for phone number is no longer present in the form
                    return;
                }

                var phoneNumberLeadFieldDatum = leadFieldData.Find(f => f.Name == phoneNumberFieldQuestion.Key);
                if (phoneNumberLeadFieldDatum is null)
                {
                    // do not sync if field mapped for phone number is no longer present in the lead
                    return;
                }

                var leadPhoneNumber = phoneNumberLeadFieldDatum.Values.FirstOrDefault();
                var contactCountry =
                    await _appDbContext.CompanyCompanies.Where(x => x.Id == companyId)
                        .Select(x => x.CompanyCountry)
                        .FirstOrDefaultAsync();
                newUserProfile.WhatsAppPhoneNumber =
                    PhoneNumberHelper.GetValidPhoneNumberWithCountryCodeForImportContact(
                        leadPhoneNumber,
                        contactCountry);

                var schemafulObjectPropertyValues = new Dictionary<string, object?>();

                var fieldMappingsExceptPhoneNumber = facebookLeadAdsFormFieldMappings
                    .Where(m => m.SleekflowFieldId != phoneNumberFieldId)
                    .ToList();
                foreach (var fieldMapping in fieldMappingsExceptPhoneNumber)
                {
                    var question = formQuestions.Find(q => q.Id == fieldMapping.FacebookLeadAdsFormFieldId);
                    if (question is null)
                    {
                        continue;
                    }

                    var fieldDatum = leadFieldData.Find(f => f.Name == question.Key);
                    if (fieldDatum is null)
                    {
                        continue;
                    }

                    var fieldDatumValue = fieldDatum.Values.FirstOrDefault();

                    switch (fieldMapping.SleekflowFieldType)
                    {
                        case SleekflowFieldType.CompanyCustomUserProfileField:
                            var userProfileCustomField = await _appDbContext.CompanyCustomUserProfileFields
                                .AsNoTracking()
                                .FirstOrDefaultAsync(f => f.Id == fieldMapping.SleekflowFieldId);
                            if (userProfileCustomField is null)
                            {
                                // skip if field has been deleted
                                continue;
                            }

                            newUserProfile.UserProfileFields.Add(
                                new AddCustomFieldsViewModel
                                {
                                    CustomFieldName = userProfileCustomField.FieldName,
                                    CustomValue = fieldDatumValue
                                });

                            break;
                        case SleekflowFieldType.CrmHubSchemaProperty:
                            var schemaProperty = schema.Properties.Find(p => p.Id == fieldMapping.SleekflowFieldId);
                            if (schemaProperty is null)
                            {
                                // skip if property has been deleted
                                continue;
                            }

                            schemafulObjectPropertyValues.Add(schemaProperty.Id, fieldDatumValue);

                            break;
                        case SleekflowFieldType.UserProfileFirstNameField:
                            newUserProfile.FirstName = fieldDatumValue;

                            break;
                        case SleekflowFieldType.UserProfileLastNameField:
                            newUserProfile.LastName = fieldDatumValue;

                            break;
                    }
                }

                var userProfile =
                    (await _userProfileService.AddOrUpdateUserProfile(companyId, newUserProfile)).FirstOrDefault();
                if (userProfile is null)
                {
                    throw new Exception("Failed to add or update user profile with lead");
                }

                await _schemafulObjectsApi.SchemafulObjectsCreateSchemafulObjectPostAsync(
                    createSchemafulObjectInput: new CreateSchemafulObjectInput(
                        schemaId: schema.Id,
                        sleekflowCompanyId: companyId,
                        primaryPropertyValue: lead.Id,
                        propertyValues: schemafulObjectPropertyValues,
                        sleekflowUserProfileId: userProfile.Id,
                        sleekflowStaffId: string.Empty));
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[FacebookLeadAds {MethodName}] " +
                    "Unable to sync contact and custom object with lead," +
                    "CompanyId: {CompanyId}," +
                    "Form id: {FormId}," +
                    "Exception: {ExceptionMessage}," +
                    "Payload: {LeadGenPayload}",
                    nameof(SaveLeadGen),
                    companyId,
                    lead?.FormId,
                    ex.Message,
                    JsonConvert.SerializeObject(lead));
            }
        }

        private async Task<Company> GetCompanyAsync(string companyId)
        {
            var company = await _appDbContext.CompanyCompanies
                .Where(c => c.Id == companyId)
                .FirstOrDefaultAsync();

            return company;
        }

        /// <summary>
        /// Refresh FacebookConfigs status for lead ads stored on SleekFlow by fetching latest token info from Facebook.
        /// For more information,
        /// see <see href="https://developers.facebook.com/docs/facebook-login/guides/%20access-tokens/debugging">this</see>.
        /// </summary>
        /// <param name="companyId">Company ID.</param>
        /// <returns>A list of FacebookConfig.</returns>
        public async Task<List<FacebookConfig>> RefreshLeadAdsFacebookConfigsStatusByCompanyIdAsync(string companyId)
        {
            var clientId = _configuration.GetValue<string>("Facebook:ClientId");
            var clientSecret = _configuration.GetValue<string>("Facebook:ClientSecret");
            var httpClient = _httpClientFactory.CreateClient(HttpClientHandlerName.Default);

            var isStatusChanged = false;

            var leadAdsFacebookConfigs = await _appDbContext.ConfigFacebookConfigs
                .Where(x => x.CompanyId == companyId
                    && x.SubscribedFields.Contains("leadgen"))
                .ToListAsync();

            foreach (var config in leadAdsFacebookConfigs)
            {
                TokenInfoResult tokenInfo;

                try
                {
                    var response = await httpClient.GetAsync(
                        $"https://graph.facebook.com/v13.0/debug_token?input_token={config.PageAccessToken}&access_token={clientId}|{clientSecret}");

                    if (!response.IsSuccessStatusCode)
                    {
                        var errorContent = await response.Content.ReadAsStringAsync();
                        var errorMessage = JObject.Parse(errorContent).ToString(Formatting.None);

                        _logger.LogError(
                            "[{MethodName}][{CompanyId}]: SleekFlow app access token is invalid error {ErrorMessage}",
                            nameof(RefreshLeadAdsFacebookConfigsStatusByCompanyIdAsync),
                            companyId,
                            errorMessage);

                        throw new Exception(errorMessage);
                    }

                    var tokenInfoResponse = await response.Content.ReadAsStringAsync();
                    tokenInfo = JsonConvert.DeserializeObject<TokenInfoResult>(tokenInfoResponse);
                }
                catch (Exception ex)
                {
                    _logger.LogError(
                        ex,
                        "[{MethodName}][{CompanyId}]: Facebook debug_token endpoint with config {ConfigId} error {ExceptionMessage}",
                        nameof(RefreshLeadAdsFacebookConfigsStatusByCompanyIdAsync),
                        companyId,
                        config.Id,
                        ex.Message);

                    throw;
                }

                if (tokenInfo?.Data == null)
                {
                    config.Status = FacebookStatus.Invalid;
                    isStatusChanged = true;

                    _logger.LogError(
                        "[{MethodName}][{CompanyId}]: Token info is null for config {ConfigId}",
                        nameof(RefreshLeadAdsFacebookConfigsStatusByCompanyIdAsync),
                        companyId,
                        config.Id);

                    continue;
                }

                if (!tokenInfo.Data.IsValid)
                {
                    config.Status = FacebookStatus.Invalid;
                    isStatusChanged = true;

                    _logger.LogInformation(
                        "[{MethodName}][{CompanyId}]: Page access token is invalid for config {ConfigId}",
                        nameof(RefreshLeadAdsFacebookConfigsStatusByCompanyIdAsync),
                        companyId,
                        config.Id);
                }

                if (tokenInfo.Data.Scopes == null
                    || !tokenInfo.Data.Scopes.Contains("leads_retrieval"))
                {
                    config.Status = FacebookStatus.Invalid;
                    isStatusChanged = true;

                    _logger.LogInformation(
                        "[{MethodName}][{CompanyId}]: Page access token has no correct scope for config {ConfigId}",
                        nameof(RefreshLeadAdsFacebookConfigsStatusByCompanyIdAsync),
                        companyId,
                        config.Id);
                }

                if (tokenInfo.Data.IsValid
                    && tokenInfo.Data.Scopes.Contains("leads_retrieval")
                    && config.Status == FacebookStatus.Invalid)
                {
                    config.Status = FacebookStatus.Loading;
                    isStatusChanged = true;

                    _logger.LogInformation(
                        "[{MethodName}][{CompanyId}]: Change invalid status to loading status for config {ConfigId}",
                        nameof(RefreshLeadAdsFacebookConfigsStatusByCompanyIdAsync),
                        companyId,
                        config.Id);
                }
            }

            if (isStatusChanged)
            {
                await _appDbContext.SaveChangesAsync();
                await _companyInfoCacheService.RemoveCompanyInfoCache(companyId);
            }

            return leadAdsFacebookConfigs;
        }

        /// <summary>
        /// Refresh FacebookConfig status for lead ads stored on SleekFlow by fetching latest token info from Facebook.
        /// For more information,
        /// see <see href="https://developers.facebook.com/docs/facebook-login/guides/%20access-tokens/debugging">this</see>.
        /// </summary>
        /// <param name="pageId">Page ID.</param>
        /// <returns>FacebookConfig.</returns>
        public async Task<FacebookConfig> RefreshLeadAdsFacebookConfigStatusByPageIdAsync(string pageId)
        {
            var clientId = _configuration.GetValue<string>("Facebook:ClientId");
            var clientSecret = _configuration.GetValue<string>("Facebook:ClientSecret");
            var httpClient = _httpClientFactory.CreateClient(HttpClientHandlerName.Default);

            var isStatusChanged = false;

            var leadAdsFacebookConfig = await _appDbContext.ConfigFacebookConfigs
                .Where(x => x.PageId == pageId
                    && x.SubscribedFields.Contains("leadgen"))
                .FirstOrDefaultAsync();

            if (leadAdsFacebookConfig is null)
            {
                throw new InvalidOperationException($"FacebookConfig with page ID {pageId} not found.");
            }

            TokenInfoResult tokenInfo;
            try
            {
                var response = await httpClient.GetAsync(
                    $"https://graph.facebook.com/v13.0/debug_token?input_token={leadAdsFacebookConfig.PageAccessToken}&access_token={clientId}|{clientSecret}");

                if (!response.IsSuccessStatusCode)
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    var errorMessage = JObject.Parse(errorContent).ToString(Formatting.None);

                    _logger.LogError(
                        "[{MethodName}][{CompanyId}]: SleekFlow app access token is invalid error {ErrorMessage}",
                        nameof(RefreshLeadAdsFacebookConfigStatusByPageIdAsync),
                        leadAdsFacebookConfig.CompanyId,
                        errorMessage);

                    throw new Exception(errorMessage);
                }

                var tokenInfoResponse = await response.Content.ReadAsStringAsync();
                tokenInfo = JsonConvert.DeserializeObject<TokenInfoResult>(tokenInfoResponse);
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[{MethodName}][{CompanyId}]: Facebook debug_token endpoint with config {ConfigId} error {ExceptionMessage}",
                    nameof(RefreshLeadAdsFacebookConfigStatusByPageIdAsync),
                    leadAdsFacebookConfig.CompanyId,
                    leadAdsFacebookConfig.Id,
                    ex.Message);

                throw;
            }

            if (tokenInfo?.Data == null)
            {
                leadAdsFacebookConfig.Status = FacebookStatus.Invalid;
                isStatusChanged = true;

                _logger.LogError(
                    "[{MethodName}][{CompanyId}]: Token info is null for config {ConfigId}",
                    nameof(RefreshLeadAdsFacebookConfigStatusByPageIdAsync),
                    leadAdsFacebookConfig.CompanyId,
                    leadAdsFacebookConfig.Id);
            }
            else if (!tokenInfo.Data.IsValid)
            {
                leadAdsFacebookConfig.Status = FacebookStatus.Invalid;
                isStatusChanged = true;

                _logger.LogInformation(
                    "[{MethodName}][{CompanyId}]: Page access token is invalid for config {ConfigId}",
                    nameof(RefreshLeadAdsFacebookConfigStatusByPageIdAsync),
                    leadAdsFacebookConfig.CompanyId,
                    leadAdsFacebookConfig.Id);
            }
            else if (tokenInfo.Data.Scopes == null
                || !tokenInfo.Data.Scopes.Contains("leads_retrieval"))
            {
                leadAdsFacebookConfig.Status = FacebookStatus.Invalid;
                isStatusChanged = true;

                _logger.LogInformation(
                    "[{MethodName}][{CompanyId}]: Page access token has no correct scope for config {ConfigId}",
                    nameof(RefreshLeadAdsFacebookConfigStatusByPageIdAsync),
                    leadAdsFacebookConfig.CompanyId,
                    leadAdsFacebookConfig.Id);
            }
            else if (tokenInfo.Data.IsValid
                && tokenInfo.Data.Scopes.Contains("leads_retrieval")
                && leadAdsFacebookConfig.Status == FacebookStatus.Invalid)
            {
                leadAdsFacebookConfig.Status = FacebookStatus.Loading;
                isStatusChanged = true;

                _logger.LogInformation(
                    "[{MethodName}][{CompanyId}]: Change invalid status to loading status for config {ConfigId}",
                    nameof(RefreshLeadAdsFacebookConfigStatusByPageIdAsync),
                    leadAdsFacebookConfig.CompanyId,
                    leadAdsFacebookConfig.Id);
            }

            if (isStatusChanged)
            {
                await _appDbContext.SaveChangesAsync();
                await _companyInfoCacheService.RemoveCompanyInfoCache(leadAdsFacebookConfig.CompanyId);
            }

            return leadAdsFacebookConfig;
        }

        /// <summary>
        /// Update Facebook Lead Ads Page Name on SleekFlow side. Will not affect Facebook side.
        /// </summary>
        /// <param name="companyId">Company ID.</param>
        /// <param name="pageId">Page ID.</param>
        /// <param name="pageName">Page Name.</param>
        /// <returns><see cref="Task"/>.</returns>
        public async Task UpdateFacebookLeadAdsPageNameAsync(
            string companyId,
            string pageId,
            string pageName)
        {
            var facebookConfig = await _facebookConfigRepository.GetFacebookConfigAsync(companyId, pageId);

            facebookConfig.PageName = pageName;

            await _facebookConfigRepository.UpdateFacebookConfigAsync(facebookConfig);

            await _companyInfoCacheService.RemoveCompanyInfoCache(companyId);
        }

        /// <summary>
        /// Get useful info of a Facebook Lead Gen Form from Facebook Graph API.
        /// </summary>
        /// <param name="httpClient">HttpClient.</param>
        /// <param name="pageId">Page ID.</param>
        /// <param name="pageAccessToken">Page access token.</param>
        /// <param name="formId">Facebook form ID.</param>
        /// <returns><see cref="FacebookLeadGenForm"/>.</returns>
        public async Task<FacebookLeadGenForm?> GetFacebookLeadGenForm(
            HttpClient httpClient,
            string pageId,
            string pageAccessToken,
            string formId)
        {
            var facebookLeadGenFormsPayload = await GetFacebookLeadGenFormsPayload(
                httpClient,
                pageId,
                pageAccessToken);

            return facebookLeadGenFormsPayload.Data.Find(f => f.Id == formId);
        }

        public async Task FetchAndSyncFacebookLeadGenFormsAsync(
            long facebookConfigId,
            string companyId,
            string pageId,
            string pageAccessToken)
        {
            var hasConfig = await _appDbContext
                .ConfigFacebookConfigs
                .AnyAsync(
                    x => x.PageId == pageId && x.SubscribedFields.Contains("leadgen"));

            if (!hasConfig)
            {
                _logger.LogInformation(
                    "FetchAndSyncFacebookLeadGenFormsAsync for Facebook config {FacebookConfigId} is removed",
                    facebookConfigId);

                RecurringJob.RemoveIfExists(facebookConfigId.ToString());

                return;
            }

            var facebookLeadGenFormsPayload = await GetFacebookLeadGenFormsPayload(
                _httpClientFactory.CreateClient(HttpClientHandlerName.Default),
                pageId,
                pageAccessToken);

            var facebookLeadGenForms = facebookLeadGenFormsPayload.Data;
            foreach (var facebookLeadGenForm in facebookLeadGenForms)
            {
                if (!await _appDbContext.FacebookLeadAdsForms.AnyAsync(
                        f => f.CompanyId == companyId
                             && f.FacebookConfigId == facebookConfigId
                             && f.FacebookFormId == facebookLeadGenForm.Id))
                {
                    // Create Lead Ads Form if not yet persisted
                    await _appDbContext.FacebookLeadAdsForms.AddAsync(
                        new FacebookLeadAdsForm
                        {
                            CompanyId = companyId,
                            FacebookConfigId = facebookConfigId,
                            FacebookFormId = facebookLeadGenForm.Id,
                            FacebookFormName = facebookLeadGenForm.Name
                        });
                }
            }

            var facebookFormIds = facebookLeadGenForms.Select(f => f.Id).ToList();
            // Delete Lead Ads Forms that are no longer in Facebook
            var deprecatedFacebookLeadAdsForms = await _appDbContext.FacebookLeadAdsForms
                .Where(f => f.CompanyId == companyId
                            && f.FacebookConfigId == facebookConfigId
                            && !facebookFormIds.Contains(f.FacebookFormId))
                .ToListAsync();
            _appDbContext.FacebookLeadAdsForms.RemoveRange(deprecatedFacebookLeadAdsForms);

            await _appDbContext.SaveChangesAsync();
        }

        private async Task<FacebookLeadGenFormsPayload> GetFacebookLeadGenFormsPayload(
            HttpClient httpClient,
            string pageId,
            string pageAccessToken)
        {
            var response = await httpClient.GetAsync(
                string.Format(
                    FacebookGraphApiLeadGenFormsUrlTemplate,
                    pageId,
                    pageAccessToken));

            if (!response.IsSuccessStatusCode)
            {
                throw new Exception(await response.Content.ReadAsStringAsync());
            }

            var jsonContent = await response.Content.ReadAsStringAsync();
            return JsonConvert.DeserializeObject<FacebookLeadGenFormsPayload>(jsonContent);
        }
    }
}