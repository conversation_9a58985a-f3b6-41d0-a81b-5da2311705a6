using System;
using System.Collections.Generic;
using System.Linq;
using Newtonsoft.Json;
using Travis_backend.SubscriptionPlanDomain.Models.Common;

namespace Travis_backend.SubscriptionPlanDomain.Models;

public class PlanDefinition
{
    [JsonProperty("id")]
    public string Id { get; } = string.Empty;

    [JsonProperty("names")]
    public List<Multilingual> Names { get; }

    [JsonProperty("descriptions")]
    public List<Multilingual> Descriptions { get; }

    [JsonProperty("feature_quantities")]
    public List<FeatureQuantity> FeatureQuantities { get; }

    [JsonProperty("plan_amounts")]
    public List<Price> PlanAmounts { get; }

    [JsonProperty("plan_type")]
    public string PlanType { get; }

    [JsonProperty("tier")]
    public string Tier { get; }

    [JsonProperty("version")]
    public string Version { get; }

    [JsonProperty("record_statuses")]
    public List<string> RecordStatuses { get; }

    [JsonProperty("metadata")]
    public Dictionary<string, object> Metadata { get; }

    [JsonProperty("created_by")]
    public string? CreatedBy { get; }

    [JsonProperty("updated_by")]
    public string? UpdatedBy { get; }

    [JsonProperty("subscription_country_tier")]
    public string SubscriptionCountryTier { get; }

    [JsonProperty("created_at")]
    public DateTimeOffset CreatedAt { get; }

    [JsonProperty("updated_at")]
    public DateTimeOffset UpdatedAt { get; }

    public PlanDefinition(
        List<Multilingual> names,
        List<Multilingual> descriptions,
        List<FeatureQuantity> featureQuantities,
        List<Price> planAmounts,
        string planType,
        string tier,
        string version,
        List<string> recordStatuses,
        Dictionary<string, object> metadata,
        string createdBy,
        string updatedBy,
        string subscriptionCountryTier,
        DateTimeOffset createdAt,
        DateTimeOffset updatedAt)
    {
        Names = names;
        Descriptions = descriptions;
        FeatureQuantities = featureQuantities;
        PlanAmounts = planAmounts;
        PlanType = planType;
        Tier = tier;
        Version = version ?? string.Empty;
        RecordStatuses = recordStatuses;
        Metadata = metadata;
        CreatedBy = createdBy;
        UpdatedBy = updatedBy;
        SubscriptionCountryTier = subscriptionCountryTier;
        CreatedAt = createdAt;
        UpdatedAt = updatedAt;
    }

    public override bool Equals(object obj)
    {
        if (obj == null || GetType() != obj.GetType())
        {
            return false;
        }

        var anotherPlan = (PlanDefinition)obj;

        return Id == anotherPlan.Id &&
               Names.SequenceEqual(anotherPlan.Names) &&
               Descriptions.SequenceEqual(anotherPlan.Descriptions) &&
               FeatureQuantities.SequenceEqual(anotherPlan.FeatureQuantities) &&
               PlanAmounts.SequenceEqual(anotherPlan.PlanAmounts) &&
               PlanType == anotherPlan.PlanType &&
               Tier == anotherPlan.Tier &&
               Version == anotherPlan.Version &&
               RecordStatuses.SequenceEqual(anotherPlan.RecordStatuses) &&
               Metadata.SequenceEqual(anotherPlan.Metadata) &&
               CreatedBy == anotherPlan.CreatedBy &&
               UpdatedBy == anotherPlan.UpdatedBy;
    }

    public override int GetHashCode()
    {
        var hashCode = 17;
        hashCode = (hashCode * 23) + (Id?.GetHashCode() ?? 0);
        hashCode = (hashCode * 23) + (Names?.GetHashCode() ?? 0);
        hashCode = (hashCode * 23) + (Descriptions?.GetHashCode() ?? 0);
        hashCode = (hashCode * 23) + (FeatureQuantities?.GetHashCode() ?? 0);
        hashCode = (hashCode * 23) + (PlanAmounts?.GetHashCode() ?? 0);
        hashCode = (hashCode * 23) + (PlanType?.GetHashCode() ?? 0);
        hashCode = (hashCode * 23) + (Tier?.GetHashCode() ?? 0);
        hashCode = (hashCode * 23) + (Version?.GetHashCode() ?? 0);
        hashCode = (hashCode * 23) + (RecordStatuses?.GetHashCode() ?? 0);
        hashCode = (hashCode * 23) + (Metadata?.GetHashCode() ?? 0);
        hashCode = (hashCode * 23) + (CreatedBy?.GetHashCode() ?? 0);
        hashCode = (hashCode * 23) + (UpdatedBy?.GetHashCode() ?? 0);
        hashCode = (hashCode * 23) + (SubscriptionCountryTier?.GetHashCode() ?? 0);
        return hashCode;
    }
}