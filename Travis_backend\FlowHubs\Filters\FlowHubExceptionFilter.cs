using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.Extensions.Logging;
using Sleekflow.Apis.FlowHub.Client;
using Travis_backend.CommonDomain.ViewModels;
using Travis_backend.FlowHubs.Exceptions;

namespace Travis_backend.FlowHubs.Filters;

public class FlowHubExceptionFilter : IExceptionFilter
{
    private readonly ILogger<FlowHubExceptionFilter> _logger;

    public FlowHubExceptionFilter(ILogger<FlowHubExceptionFilter> logger)
    {
        _logger = logger;
    }

    public void OnException(ExceptionContext context)
    {
        _logger.LogError(
            context.Exception,
            "Caught an exception in FlowHubExceptionFilter. Request path: {RequestPath}. Error message: {ErrorMessage}",
            context.HttpContext.Request.Path,
            context.Exception.Message);

        if (context.Exception is SleekflowValidationException sleekflowValidationException)
        {
            context.Result = new BadRequestObjectResult(sleekflowValidationException.Output);
            return;
        }

        if (context.Exception is SleekflowErrorCodeException sleekflowErrorCodeException)
        {
            context.Result = new BadRequestObjectResult(sleekflowErrorCodeException.Output);
            return;
        }

        if (context.Exception is ContactNotFoundException contactNotFoundException)
        {
            context.Result = new BadRequestObjectResult(
                new ResponseViewModel(contactNotFoundException.Message));
            return;
        }

        throw context.Exception;
    }
}