﻿using System.Linq;
using System.Threading.Tasks;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.Constants;
using Travis_backend.MessageDomain.Models;
using Travis_backend.MessageDomain.Repositories;

namespace Travis_backend.BroadcastDomain.Services;

public interface IBroadcastChannelService
{
    Task MapChannelIdentityIdAsync(CompanyMessageTemplate companyMessageTemplate);
}

public class BroadcastChannelService : IBroadcastChannelService
{
    private readonly IChannelIdentityIdRepository _channelIdentityIdRepository;

    public BroadcastChannelService(
        IChannelIdentityIdRepository channelIdentityIdRepository)
    {
        _channelIdentityIdRepository = channelIdentityIdRepository;
    }

    public async Task MapChannelIdentityIdAsync(CompanyMessageTemplate companyMessageTemplate)
    {
        var targetedChannel = companyMessageTemplate.TargetedChannelWithIds?
            .FirstOrDefault();

        if (targetedChannel is not null)
        {
            var channelType = targetedChannel.channel?.Replace("twilio_whatsapp", ChannelTypes.WhatsappTwilio);

            companyMessageTemplate.TargetedChannel = new TargetedChannel
            {
                ChannelType = channelType,
                ChannelIdentityId = await _channelIdentityIdRepository.GetChannelIdentityIdByTargetedChannelModel(
                    companyMessageTemplate.CompanyId,
                    channelType,
                    targetedChannel.ids?.FirstOrDefault())
            };
        }
        else
        {
            companyMessageTemplate.TargetedChannel = new TargetedChannel();
        }

        var channelMessage = companyMessageTemplate.CampaignChannelMessages?
            .FirstOrDefault();

        if (channelMessage is not null)
        {
            channelMessage.TargetedChannel = companyMessageTemplate.TargetedChannel;
        }
    }
}