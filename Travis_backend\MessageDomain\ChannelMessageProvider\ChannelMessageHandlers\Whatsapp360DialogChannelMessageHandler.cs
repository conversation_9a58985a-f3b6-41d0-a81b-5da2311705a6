using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Hangfire;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using NReco.VideoConverter;
using Travis_backend.CompanyDomain.Services;
using Travis_backend.Constants;
using Travis_backend.ConversationDomain.Models;
using Travis_backend.ConversationServices;
using Travis_backend.Database;
using Travis_backend.FileDomain.Services;
using Travis_backend.Helpers;
using Travis_backend.MessageDomain.Models;
using Travis_backend.OpenTelemetry.Meters;
using Travis_backend.OpenTelemetry.Meters.Constants;
using WABA360Dialog;
using WABA360Dialog.ApiClient.Exceptions;
using WABA360Dialog.ApiClient.Payloads.Converters;
using WABA360Dialog.ApiClient.Payloads.Enums;
using WABA360Dialog.ApiClient.Payloads.Models.MessageObjects;
using MessageStatus = Travis_backend.MessageDomain.Models.MessageStatus;

namespace Travis_backend.MessageDomain.ChannelMessageProvider.ChannelMessageHandlers;

public class Whatsapp360DialogChannelMessageHandler : IChannelMessageHandler
{
    private readonly ApplicationDbContext _appDbContext;
    private readonly ILogger<Whatsapp360DialogChannelMessageHandler> _logger;
    private readonly IConfiguration _configuration;
    private readonly IMediaProcessService _mediaProcessService;
    private readonly IConversationMeters _conversationMeters;

    public Whatsapp360DialogChannelMessageHandler(
        ApplicationDbContext appDbContext,
        ILogger<Whatsapp360DialogChannelMessageHandler> logger,
        IConfiguration configuration,
        IMediaProcessService mediaProcessService,
        IConversationMeters conversationMeters)
    {
        _appDbContext = appDbContext;
        _logger = logger;
        _configuration = configuration;
        _mediaProcessService = mediaProcessService;
        _conversationMeters = conversationMeters;
    }

    public string ChannelType => ChannelTypes.Whatsapp360Dialog;

    public async ValueTask<ConversationMessage> SendChannelMessageAsync(
        Conversation conversation,
        ConversationMessage conversationMessage)
    {
        if (conversationMessage.Whatsapp360DialogReceiver == null ||
            !string.IsNullOrEmpty(conversationMessage.MessageUniqueID))
        {
            return conversationMessage;
        }

        conversationMessage.ChannelIdentityId = conversationMessage.Whatsapp360DialogReceiver.ChannelIdentityId;

        var whatsApp360DialogConfig = await _appDbContext.ConfigWhatsApp360DialogConfigs.AsNoTracking()
            .FirstOrDefaultAsync(
                x => x.Id == conversation.WhatsApp360DialogUser.ChannelId && x.CompanyId == conversation.CompanyId);

        if (whatsApp360DialogConfig.IsSuspended)
        {
            conversationMessage.Status = MessageStatus.OutOfCredit;
            conversationMessage.IsSentFromSleekflow = true;
            conversation.LastMessageChannelId = whatsApp360DialogConfig.Id;
            conversation.LastChannelIdentityId = conversationMessage.ChannelIdentityId;

            return conversationMessage;
        }

        var client = new WABA360DialogApiClient(whatsApp360DialogConfig.ApiKey);

        try
        {
            // Check contact status
            if (conversation.WhatsApp360DialogUser.ContactStatus != ContactStatus.valid.GetString())
            {
                var checkContactResponse = await client.CheckContactsAsync(
                    new[]
                    {
                        conversation.WhatsApp360DialogUser.PhoneNumber
                    },
                    Blocking.wait);

                var contactStatus = checkContactResponse.Contacts
                    .First(x => x.Input == conversation.WhatsApp360DialogUser.PhoneNumber);

                conversation.WhatsApp360DialogUser.ContactStatus = contactStatus.Status.GetString();

                if (contactStatus.WaId != null
                    && conversation.WhatsApp360DialogUser.WhatsAppId != contactStatus.WaId)
                {
                    conversation.WhatsApp360DialogUser.WhatsAppId = contactStatus.WaId;
                }

                conversation.WhatsApp360DialogUser.LastContactStatusCheckDate = DateTime.UtcNow;
            }

            WABA360Dialog.ApiClient.Payloads.SendMessageResponse messageResponse = null;

            switch (conversationMessage.MessageType)
            {
                case "text":
                    messageResponse = await client.SendMessageAsync(
                        MessageObjectFactory.CreateTextMessage(
                            conversation.WhatsApp360DialogUser.WhatsAppId,
                            conversationMessage.MessageContent,
                            WhatsApp360DialogExtendedMessageHelper.CheckTextContainsUrl(
                                conversationMessage.MessageContent)));

                    break;
                case "template":
                    messageResponse = await client.SendMessageAsync(
                        MessageObjectFactory.CreateTemplateMessage(
                            conversation.WhatsApp360DialogUser.WhatsAppId,
                            conversationMessage.Whatsapp360DialogExtendedMessagePayload
                                .Whatsapp360DialogTemplateMessage.TemplateNamespace,
                            conversationMessage.Whatsapp360DialogExtendedMessagePayload
                                .Whatsapp360DialogTemplateMessage.TemplateName,
                            conversationMessage.Whatsapp360DialogExtendedMessagePayload
                                .Whatsapp360DialogTemplateMessage.Language,
                            conversationMessage.Whatsapp360DialogExtendedMessagePayload
                                .Whatsapp360DialogTemplateMessage.Components));

                    break;
                case "interactive":
                    messageResponse = await client.SendMessageAsync(
                        MessageObjectFactory.CreateInteractiveMessage(
                            conversation.WhatsApp360DialogUser.WhatsAppId,
                            conversationMessage.Whatsapp360DialogExtendedMessagePayload
                                .Whatsapp360DialogInteractiveObject));

                    break;
                case "file":
                    var domainName = _configuration.GetValue<String>("Values:DomainName");

                    foreach (var uploadedFile in conversationMessage.UploadedFiles)
                    {
                        if (uploadedFile != null)
                        {
                            var url = $"{domainName}/Message/File/Private/{uploadedFile.FileId}";

                            if (uploadedFile.MIMEType.Contains("video"))
                            {
                                if (!uploadedFile.MIMEType.Contains("mp4"))
                                {
                                    await _mediaProcessService.ProcessMedia(
                                        conversation.Id,
                                        uploadedFile,
                                        Format.mp4);
                                }

                                messageResponse = await client.SendMessageAsync(
                                    MessageObjectFactory.CreateVideoMessageByLink(
                                        conversation.WhatsApp360DialogUser.WhatsAppId,
                                        url,
                                        conversationMessage.MessageContent));
                            }
                            else if ((uploadedFile.MIMEType.Contains("audio") &&
                                      !uploadedFile.MIMEType.Contains("mp3")) ||
                                     Path.GetExtension(uploadedFile.Filename) == ".webm" ||
                                     Path.GetExtension(uploadedFile.Filename) == ".bin")
                            {
                                await _mediaProcessService.ProcessMedia(conversation.Id, uploadedFile, "mp3");

                                conversationMessage.MessageContent = null;

                                messageResponse = await client.SendMessageAsync(
                                    MessageObjectFactory.CreateAudioMessageByLink(
                                        conversation.WhatsApp360DialogUser.WhatsAppId,
                                        url));
                            }
                            else if (uploadedFile.MIMEType.Contains("image") &&
                                     !uploadedFile.MIMEType.Contains("gif"))
                            {
                                if (uploadedFile.MIMEType == "image/webp")
                                {
                                    conversationMessage.MessageContent = null;

                                    messageResponse = await client.SendMessageAsync(
                                        MessageObjectFactory.CreateStickerMessageByLink(
                                            conversation.WhatsApp360DialogUser.WhatsAppId,
                                            url));
                                }
                                else
                                {
                                    messageResponse = await client.SendMessageAsync(
                                        MessageObjectFactory.CreateImageMessageByLink(
                                            conversation.WhatsApp360DialogUser.WhatsAppId,
                                            url,
                                            conversationMessage.MessageContent));
                                }
                            }
                            else
                            {
                                messageResponse = await client.SendMessageAsync(
                                    MessageObjectFactory.CreateDocumentMessageByLink(
                                        conversation.WhatsApp360DialogUser.WhatsAppId,
                                        Path.GetFileName(uploadedFile.Filename),
                                        url,
                                        conversationMessage.MessageContent));
                            }
                        }
                    }

                    break;
            }

            conversationMessage.MessageUniqueID = messageResponse.CreatedMessages.FirstOrDefault()?.Id;
            conversationMessage.Status = MessageStatus.Sent;

            _conversationMeters.IncrementCounter(ChannelTypes.Whatsapp360Dialog, ConversationMeterOptions.SendSuccess);

            _logger.LogInformation(
                "Outgoing MessageId From 360Dialog: {ConversationMessageMessageUniqueID}",
                conversationMessage.MessageUniqueID);

            if (whatsApp360DialogConfig.ChannelErrorStatus.HasValue)
            {
                BackgroundJob.Enqueue<IWhatsApp360DialogService>(
                    x => x.ClearChannelErrorStatus(whatsApp360DialogConfig.CompanyId, whatsApp360DialogConfig.Id));
            }
        }
        catch (ApiClientException ex)
        {
            conversationMessage.Status = MessageStatus.Failed;
            conversationMessage.Metadata ??= new Dictionary<string, object>();
            var errors = new List<ConversationMessageError>();

            _conversationMeters.IncrementCounter(ChannelTypes.Whatsapp360Dialog, ConversationMeterOptions.SendFailed);

            if (ex.Error != null)
            {
                conversationMessage.ChannelStatusMessage = string.Join(
                    ';',
                    ex.Error.Select(
                            x =>
                                $"{x.Code}-{x.Details}{(!string.IsNullOrWhiteSpace(x.Title) ? "," + x.Title : null)}")
                        .ToList());

                var messages = ex.Error.Select(
                    x => new ConversationMessageError
                    {
                        Code = x.Code.ToString(),
                        Message = x.Details,
                        InnerError = JsonConvert.SerializeObject(ex.Error)
                    }).ToList();

                errors.AddRange(messages);

                if (ex.Error.Any(x => x.Code == 1014) && conversationMessage.DeliveryType != DeliveryType.Broadcast)
                {
                    BackgroundJob.Enqueue<IWhatsApp360DialogService>(
                        x => x.UpdateChannelErrorStatus(
                            whatsApp360DialogConfig.CompanyId,
                            whatsApp360DialogConfig.Id,
                            ex.Error,
                            ex.Meta,
                            ex.RequestPath,
                            ex.HttpStatusCode,
                            ex.RequestBody,
                            ex.ResponseBody));
                }

                conversationMessage.Metadata.Add("errorDetail", ex.Error);
            }
            else if (ex.Meta != null)
            {
                switch (ex.Meta)
                {
                    case { HttpCode: 401, DeveloperMessage: "Invalid api key" }:
                        conversationMessage.ChannelStatusMessage = "401-Invalid api key";

                        break;
                    case { HttpCode: > 500 }:
                        conversationMessage.ChannelStatusMessage = "500-Internal Server Error";

                        break;

                    default:
                        conversationMessage.ChannelStatusMessage =
                            $"{ex.Meta.HttpCode}-{ex.Meta?.DeveloperMessage}";

                        break;
                }

                errors.Add(
                    new ConversationMessageError
                    {
                        Code = ex.Meta?.HttpCode.ToString(),
                        Message = ex.Meta?.DeveloperMessage,
                        InnerError = JsonConvert.SerializeObject(ex.Meta)
                    });

                if (conversationMessage.DeliveryType != DeliveryType.Broadcast)
                {
                    BackgroundJob.Enqueue<IWhatsApp360DialogService>(
                        x => x.UpdateChannelErrorStatus(
                            whatsApp360DialogConfig.CompanyId,
                            whatsApp360DialogConfig.Id,
                            ex.Error,
                            ex.Meta,
                            ex.RequestPath,
                            ex.HttpStatusCode,
                            ex.RequestBody,
                            ex.ResponseBody));
                }
            }

            conversationMessage.Metadata.Add("errors", errors);

            _logger.LogError(
                ex,
                "360Dialog Send Message error (CompanyId:{CompanyId};ConversationId:{ConversationId};PhoneNumber{WhatsApp360DialogUserWhatsAppId}): " +
                "Request body: {RequestBody}, Response body: {ResponseBody}",
                conversation?.CompanyId,
                conversation?.Id,
                conversation?.WhatsApp360DialogUser?.WhatsAppId,
                ex.RequestBody,
                ex.ResponseBody);
        }
        catch (Exception ex)
        {
            conversationMessage.Status = MessageStatus.Failed;
            conversationMessage.ChannelStatusMessage = ex.Message;

            _conversationMeters.IncrementCounter(ChannelTypes.Whatsapp360Dialog, ConversationMeterOptions.SendFailed);

            _logger.LogError(
                ex,
                "360Dialog Send Message error (CompanyId:{CompanyId};ConversationId:{ConversationId};PhoneNumber{WhatsApp360DialogUserWhatsAppId}): {ExceptionMessage}",
                conversation?.CompanyId,
                conversation?.Id,
                conversation?.WhatsApp360DialogUser?.WhatsAppId,
                ex.Message);
        }

        conversationMessage.IsSentFromSleekflow = true;
        conversation.LastMessageChannelId = whatsApp360DialogConfig.Id;
        conversation.LastChannelIdentityId = conversationMessage.ChannelIdentityId;

        return conversationMessage;
    }
}