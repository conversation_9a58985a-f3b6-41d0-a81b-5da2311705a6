using System;
using System.ComponentModel.DataAnnotations;

namespace Travis_backend.InternalDomain.ViewModels;

public class UpdatePartnerStackCustomerKeyRequest
{
    [Required]
    public string CompanyId { get; set; }

    [Required]
    public string CustomerKey { get; set; }
}

public class SyncMrrToPartnerStackRequest
{
    [Required]
    public string CompanyId { get; set; }
}

public class UpdatePartnerStackPartnerKeyRequest
{
    [Required]
    public string CompanyId { get; set; }

    [Required]
    public string PartnerKey { get; set; }
}

public class UpdatePartnerStackCustomerCommissionConfigRequest
{
    [Required]
    public string CompanyId { get; set; }

    public string SyncType { get; set; }

    public int? IndividualCommissionRate { get; set; }

    public string CommissionEndDate { get; set; }
}

public class SyncPartnerStackPartnerInformationRequest
{
    [Required]
    public string CompanyId { get; set; }
}