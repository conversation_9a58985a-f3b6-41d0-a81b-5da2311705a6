using System;
using System.Collections.Generic;
using System.Linq;
using Travis_backend.Enums;

namespace Travis_backend.ResellerDomain.ViewModels;

public class ResellerClientCompanyUsageDetails
{
    public string ClientCompanyId { get; set; }

    public string ClientCompanyName { get; set; }

    public string ClientProfileId { get; set; }

    public string ClientCompanyLogoLink { get; set; }

    public SubscriptionPlanDto CurrentSubscriptionPlan { get; set; }

    public List<BillRecordDto> CurrentAddOnBillRecords { get; set; }

    public DateTime CreatedAt { get; set; }

    public List<BillRecordDto> BillRecords { get; set; }

    public DateTime? PlanStart => BillRecords?.Where(x => x.SubscriptionPlanId == CurrentSubscriptionPlan?.Id)
        .OrderBy(x => x.PeriodStart).Select(x => x.PeriodStart).FirstOrDefault();

    public DateTime? PlanExpire => BillRecords?.Where(x => x.SubscriptionPlanId == CurrentSubscriptionPlan?.Id)
        .OrderByDescending(x => x.PeriodEnd).Select(x => x.PeriodEnd).FirstOrDefault();

    public string Status => CompanyStatus == "Deleted"
        ? "Deleted"
        : BillRecords?
            .Where(
                x => x.SubscriptionPlanId == CurrentSubscriptionPlan.Id)
            .Select(
                x => x.SubscriptionPlanId
                    .Contains("free")
                    ? "Trial"
                    : "Active")
            .FirstOrDefault();

    public int WhatsAppConfigCount { get; set; }

    public int WhatsApp360DialogConfigCount { get; set; }

    public int InstagramConfigCount { get; set; }

    public int FacebookConfigCount { get; set; }

    public int WebClientSenderCount { get; set; }

    public int LineConfigCount { get; set; }

    public int SMSConfigCount { get; set; }

    public int ShoplineConfigCount { get; set; }

    public int ShopifyConfigCount { get; set; }

    public int TelegramConfigCount { get; set; }

    public int ViberConfigCount { get; set; }

    public int WeChatConfigCount { get; set; }

    public int EmailConfigCount { get; set; }

    public int TotalChannelAdded => WhatsAppConfigCount + WhatsApp360DialogConfigCount + InstagramConfigCount +
                                    FacebookConfigCount + LineConfigCount + SMSConfigCount + ShoplineConfigCount +
                                    ShopifyConfigCount + TelegramConfigCount + ViberConfigCount + WeChatConfigCount +
                                    EmailConfigCount + TotalWhatsappInstance;

    public int TotalContacts { get; set; }

    public int TotalAgents { get; set; }

    public int TotalWhatsappInstance { get; set; }

    public int MaximumContacts { get; set; }

    public int MaximumAgents { get; set; }

    public int MaximumWhatsappInstance { get; set; }

    public string StaffLoginUsedPercentage => MaximumAgents < 1
        ? "100"
        : (Convert.ToDouble(TotalAgents) / Convert.ToDouble(MaximumAgents) * 100).ToString("0");

    public string CustomerContactsUsedPercentage => MaximumContacts < 1
        ? "100"
        : (Convert.ToDouble(TotalContacts) / Convert.ToDouble(MaximumContacts) * 100).ToString("0");

    public string WhatsappNumberUsedPercentage => MaximumWhatsappInstance < 1
        ? "100"
        : (Convert.ToDouble(TotalWhatsappInstance) / Convert.ToDouble(MaximumWhatsappInstance) * 100).ToString("0");

    public ResellerClientStaffInformation ContactOwner { get; set; }

    public ResellerStaffInformation Assignee { get; set; }

    public AnyDowngradePlan AnyDowngradePlan { get; set; }

    public AddOnToPay StaffAddOnToPay { get; set; }

    public AddOnToPay ContactAddOnToPay { get; set; }

    public bool? IsCustomized => BillRecords?.Exists(x => x.Status == BillStatus.Active && x.IsCustomized);

    public decimal MonthlyRecurringRevenue { get; set; }

    public string CompanyStatus { get; set; }
}