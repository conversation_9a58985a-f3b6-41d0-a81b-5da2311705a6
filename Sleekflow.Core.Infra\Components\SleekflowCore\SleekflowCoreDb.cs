using Pulumi;
using Pulumi.AzureNative.Resources;
using Sleekflow.Core.Infra.Components.Configs;
using Sleekflow.Core.Infra.Components.Models;
using Sleekflow.Core.Infra.Constants;
using Sleekflow.Core.Infra.Utils;
using Random = Pulumi.Random;
using Sql = Pulumi.AzureNative.Sql;
using Network = Pulumi.AzureNative.Network;
using Insights = Pulumi.AzureNative.Insights;

namespace Sleekflow.Core.Infra.Components.SleekflowCore;

public class SleekflowCoreDb
{
    private readonly MyConfig _myConfig;
    private readonly ServerConfig _serverConfig;
    private readonly List<EnvGroup> _envGroups;

    public SleekflowCoreDb(
        MyConfig myConfig,
        ServerConfig serverConfig,
        List<EnvGroup> envGroups)
    {
        _myConfig = myConfig;
        _serverConfig = serverConfig;
        _envGroups = envGroups;
    }

    public void InitSleekflowCoreDb()
    {
        foreach (var envGroup in _envGroups)
        {
            var resourceGroup = envGroup.ResourceGroup;

            var regionalConfig = _serverConfig
                .RegionalConfigs
                .FirstOrDefault(s => s.LocationName == envGroup.LocationName)!;

            var sqlDbConfig = regionalConfig.SqlDbConfig;

            var adminUserName = new Random.RandomId(
                ResourceUtils.GetName(
                    $"sleekflow-core-sql-server-random-name-{LocationNames.GetShortName(envGroup.LocationName)}",
                    _myConfig),
                new Random.RandomIdArgs
                {
                    ByteLength = 4,
                    Keepers =
                    {
                        {
                            "name", resourceGroup.Name
                        },
                        {
                            "name_secret", sqlDbConfig!.AdministratorLoginRandomSecret
                        }
                    },
                },
                new CustomResourceOptions
                {
                    Parent = resourceGroup
                });

            var adminPassword = new Random.RandomId(
                ResourceUtils.GetName(
                    $"sleekflow-core-sql-server-random-password-{LocationNames.GetShortName(envGroup.LocationName)}",
                    _myConfig),
                new Random.RandomIdArgs
                {
                    ByteLength = 32,
                    Keepers =
                    {
                        {
                            "name", resourceGroup.Name
                        },
                        {
                            "password_secret", sqlDbConfig.AdministratorLoginRandomPasswordSecret
                        }
                    },
                },
                new CustomResourceOptions
                {
                    Parent = resourceGroup
                });

            if (sqlDbConfig is null)
            {
                throw new Exception("DbConfig not found");
            }

            var administratorLogin = adminUserName.Hex.Apply(h => "s" + h);
            var administratorLoginPassword = adminPassword.Hex.Apply(h => "s" + h + "*##!");

            var server = new Sql.Server(
                ResourceUtils.GetName(
                    $"sleekflow-core-sql-server-{LocationNames.GetShortName(envGroup.LocationName)}",
                    _myConfig),
                new Sql.ServerArgs
                {
                    ResourceGroupName = resourceGroup.Name,
                    AdministratorLogin = administratorLogin,
                    AdministratorLoginPassword = administratorLoginPassword,
                    Version = "12.0"
                },
                new CustomResourceOptions
                {
                    Parent = resourceGroup
                });

            var skuConfig = regionalConfig.SkuConfig.SleekflowCoreDb;

            var database = new Sql.Database(
                ResourceUtils.GetName(
                    $"sleekflow-core-sql-db-{LocationNames.GetShortName(envGroup.LocationName)}",
                    _myConfig),
                new Sql.DatabaseArgs
                {
                    ResourceGroupName = resourceGroup.Name,
                    ServerName = server.Name,
                    Sku = new Sql.Inputs.SkuArgs
                    {
                        Name = skuConfig.Name,
                        Tier = skuConfig.Tier,
                        Family = skuConfig.Family!,
                        Capacity = skuConfig.Capacity!
                    },
                    ReadScale =
                        string.Equals(sqlDbConfig.IsReadScaleEnable, "true", StringComparison.OrdinalIgnoreCase)
                            ? "Enabled"
                            : "Disabled",
                    HighAvailabilityReplicaCount = sqlDbConfig.HighAvailabilityReplicaCount
                },
                new CustomResourceOptions
                {
                    Parent = resourceGroup
                });

            InitAlertRules(envGroup, resourceGroup, database);

            var subnet = new Network.Subnet(
                ResourceUtils.GetName(
                    $"sleekflow-core-sql-server-subnet-{LocationNames.GetShortName(envGroup.LocationName)}",
                    _myConfig),
                new Network.SubnetArgs
                {
                    ResourceGroupName = resourceGroup.Name,
                    VirtualNetworkName = envGroup.VirtualNetwork.Name,
                    AddressPrefix = regionalConfig.VnetConfig.SleekflowCoreDbAddressPrefix,
                    PrivateEndpointNetworkPolicies = "Disabled"
                },
                new CustomResourceOptions
                {
                    Parent = resourceGroup
                });

            var privateEndpoint = new Network.PrivateEndpoint(
                ResourceUtils.GetName(
                    $"sleekflow-core-sql-server-private-endpoint-{LocationNames.GetShortName(envGroup.LocationName)}",
                    _myConfig),
                new Network.PrivateEndpointArgs
                {
                    ResourceGroupName = resourceGroup.Name,
                    Subnet = new Network.Inputs.SubnetArgs
                    {
                        Id = subnet.Id
                    },
                    PrivateLinkServiceConnections = new Network.Inputs.PrivateLinkServiceConnectionArgs
                    {
                        Name = "sleekflow-core-sql-db-private-link",
                        PrivateLinkServiceId = server.Id,
                        GroupIds = new[]
                        {
                            "sqlServer"
                        }
                    }
                },
                new CustomResourceOptions
                {
                    Parent = resourceGroup
                });

            var privateRecordSet = new Network.PrivateRecordSet(
                ResourceUtils.GetName(
                    $"sleekflow-core-sql-server-private-record-set-{LocationNames.GetShortName(envGroup.LocationName)}",
                    _myConfig),
                new Network.PrivateRecordSetArgs
                {
                    ResourceGroupName = resourceGroup.Name,
                    ARecords = new[]
                    {
                        new Network.Inputs.ARecordArgs
                        {
                            Ipv4Address = Output
                                .Tuple(
                                    privateEndpoint.ProvisioningState,
                                    privateEndpoint.CustomDnsConfigs)
                                .Apply(t => t.Item2[0].IpAddresses[0])
                        }
                    },
                    RelativeRecordSetName = server.Name,
                    PrivateZoneName = envGroup.PrivateZone.Name,
                    RecordType = "A",
                    Ttl = 10,
                },
                new CustomResourceOptions
                {
                    Parent = resourceGroup, DependsOn = envGroup.VirtualNetwork,
                });

            var privateDnsZoneGroupName =
                $"sleekflow-core-sql-server-private-dns-zone-group-{LocationNames.GetShortName(envGroup.LocationName)}";
            var privateDnsZoneGroup = new Network.PrivateDnsZoneGroup(
                privateDnsZoneGroupName,
                new Network.PrivateDnsZoneGroupArgs
                {
                    Name = privateDnsZoneGroupName,
                    ResourceGroupName = resourceGroup.Name,
                    PrivateEndpointName = privateEndpoint.Name,
                    PrivateDnsZoneConfigs = new Network.Inputs.PrivateDnsZoneConfigArgs
                    {
                        Name = envGroup.PrivateZone.Name, PrivateDnsZoneId = envGroup.PrivateZone.Id
                    }
                },
                new CustomResourceOptions
                {
                    Parent = resourceGroup, DependsOn = envGroup.VirtualNetwork
                });

            foreach (var whitelistIpRange in sqlDbConfig.WhitelistIpRanges)
            {
                var _ = new Sql.FirewallRule(
                    ResourceUtils.GetName(
                        $"sleekflow-core-sql-server-{LocationNames.GetShortName(envGroup.LocationName)}-{whitelistIpRange.StartIpAddress.Replace(".", "-")}-{whitelistIpRange.EndIpAddress.Replace(".", "-")}",
                        _myConfig),
                    new Sql.FirewallRuleArgs
                    {
                        ResourceGroupName = resourceGroup.Name,
                        ServerName = server.Name,
                        StartIpAddress = whitelistIpRange.StartIpAddress,
                        EndIpAddress = whitelistIpRange.EndIpAddress
                    },
                    new CustomResourceOptions
                    {
                        Parent = resourceGroup
                    });
            }

            envGroup.SqlServerProperties = new SqlServerProperties(
                server,
                database,
                administratorLogin,
                administratorLoginPassword);
        }
    }

    private static void InitAlertRules(
        EnvGroup envGroup,
        ResourceGroup resourceGroup,
        Sql.Database sqlDatabase)
    {
        var name = $"sleekflow-core-database-{LocationNames.GetShortName(envGroup.LocationName)}";

        InitAlertRulesForCpuPercentage(envGroup, resourceGroup, sqlDatabase, name);
        InitAlertRulesForServiceAvailability(envGroup, resourceGroup, sqlDatabase, name);
    }

    private static void InitAlertRulesForCpuPercentage(EnvGroup envGroup, ResourceGroup resourceGroup, Sql.Database sqlDatabase,
        string name)
    {
        var criticalCpuMetricAlert = new Insights.MetricAlert(
            $"{name}-avg-cpu-percentage-over-95-for-5-mins",
            new Insights.MetricAlertArgs
            {
                RuleName =
                    $"P1 ({name}) - Average Sql Server CpuPercentage is over 95 for 5 minutes",
                ResourceGroupName = resourceGroup.Name, // Replace with the actual RG if needed
                Location = "global", // Use the location as specified in the ARM template
                Severity = 1,
                Enabled = true,
                Scopes =
                {
                    sqlDatabase.Id
                },
                EvaluationFrequency = "PT1M",
                WindowSize = "PT5M",
                Criteria = new Insights.Inputs.MetricAlertSingleResourceMultipleMetricCriteriaArgs
                {
                    AllOf =
                    {
                        new Insights.Inputs.MetricCriteriaArgs
                        {
                            Threshold = 95,
                            Name = "Metric1",
                            MetricNamespace = "Microsoft.Sql/servers/databases",
                            MetricName = "cpu_percent",
                            Operator = Insights.Operator.GreaterThan,
                            TimeAggregation = Insights.AggregationTypeEnum.Average,
                            SkipMetricValidation = false,
                            CriterionType = "StaticThresholdCriterion",
                        }
                    },
                    OdataType = "Microsoft.Azure.Monitor.SingleResourceMultipleMetricCriteria"
                },
                AutoMitigate = true,
                TargetResourceType = "Microsoft.Sql/servers/databases",
                TargetResourceRegion = envGroup.LocationName,
                Actions = new Insights.Inputs.MetricAlertActionArgs
                {
                    ActionGroupId = envGroup.CriticalActionGroup.Id // The action group to invoke when the alert fires
                },
            },
            new CustomResourceOptions
            {
                Parent = resourceGroup
            });

        var preventionCpuMetricAlert = new Insights.MetricAlert(
            $"{name}-avg-cpu-percentage-over-80-for-15-mins",
            new Insights.MetricAlertArgs
            {
                RuleName =
                    $"P2 ({name}) - Average Sql Server CpuPercentage is over 80 for 15 minutes",
                ResourceGroupName = resourceGroup.Name, // Replace with the actual RG if needed
                Location = "global", // Use the location as specified in the ARM template
                Severity = 2,
                Enabled = true,
                Scopes =
                {
                    sqlDatabase.Id
                },
                EvaluationFrequency = "PT1M",
                WindowSize = "PT15M",
                Criteria = new Insights.Inputs.MetricAlertSingleResourceMultipleMetricCriteriaArgs
                {
                    AllOf =
                    {
                        new Insights.Inputs.MetricCriteriaArgs
                        {
                            Threshold = 80,
                            Name = "Metric1",
                            MetricNamespace = "Microsoft.Sql/servers/databases",
                            MetricName = "cpu_percent",
                            Operator = Insights.Operator.GreaterThan,
                            TimeAggregation = Insights.AggregationTypeEnum.Average,
                            SkipMetricValidation = false,
                            CriterionType = "StaticThresholdCriterion",
                        }
                    },
                    OdataType = "Microsoft.Azure.Monitor.SingleResourceMultipleMetricCriteria"
                },
                AutoMitigate = true,
                TargetResourceType = "Microsoft.Sql/servers/databases",
                TargetResourceRegion = envGroup.LocationName,
                Actions = new Insights.Inputs.MetricAlertActionArgs
                {
                    ActionGroupId = envGroup.PreventionActionGroup.Id // The action group to invoke when the alert fires
                },
            },
            new CustomResourceOptions
            {
                Parent = resourceGroup
            });
    }

    private static void InitAlertRulesForServiceAvailability(EnvGroup envGroup, ResourceGroup resourceGroup, Sql.Database sqlDatabase, string name)
    {
        var criticalServiceAvailabilityMetricAlert = new Insights.MetricAlert(
            $"{name}-avg-service-availability-below-80-for-5-mins",
            new Insights.MetricAlertArgs
            {
                RuleName =
                    $"P1 ({name}) - Average Sql Server availability is below 80 for 5 minutes",
                ResourceGroupName = resourceGroup.Name, // Replace with the actual RG if needed
                Location = "global", // Use the location as specified in the ARM template
                Severity = 1,
                Enabled = true,
                Scopes =
                {
                    sqlDatabase.Id
                },
                EvaluationFrequency = "PT1M",
                WindowSize = "PT5M",
                Criteria = new Insights.Inputs.MetricAlertSingleResourceMultipleMetricCriteriaArgs
                {
                    AllOf =
                    {
                        new Insights.Inputs.MetricCriteriaArgs
                        {
                            Threshold = 80,
                            Name = "Metric1",
                            MetricNamespace = "Microsoft.Sql/servers/databases",
                            MetricName = "availability",
                            Operator = Insights.Operator.LessThan,
                            TimeAggregation = Insights.AggregationTypeEnum.Average,
                            SkipMetricValidation = false,
                            CriterionType = "StaticThresholdCriterion",
                        }
                    },
                    OdataType = "Microsoft.Azure.Monitor.SingleResourceMultipleMetricCriteria"
                },
                AutoMitigate = true,
                TargetResourceType = "Microsoft.Sql/servers/databases",
                TargetResourceRegion = envGroup.LocationName,
                Actions = new Insights.Inputs.MetricAlertActionArgs
                {
                    ActionGroupId = envGroup.CriticalActionGroup.Id // The action group to invoke when the alert fires
                },
            },
            new CustomResourceOptions
            {
                Parent = resourceGroup
            });

        var preventionServiceAvailabilityMetricAlert = new Insights.MetricAlert(
            $"{name}-avg-service-availability-below-95-for-5-mins",
            new Insights.MetricAlertArgs
            {
                RuleName =
                    $"P2 ({name}) - Average Sql Server availability is below 95 for 5 minutes",
                ResourceGroupName = resourceGroup.Name, // Replace with the actual RG if needed
                Location = "global", // Use the location as specified in the ARM template
                Severity = 2,
                Enabled = true,
                Scopes =
                {
                    sqlDatabase.Id
                },
                EvaluationFrequency = "PT1M",
                WindowSize = "PT5M",
                Criteria = new Insights.Inputs.MetricAlertSingleResourceMultipleMetricCriteriaArgs
                {
                    AllOf =
                    {
                        new Insights.Inputs.MetricCriteriaArgs
                        {
                            Threshold = 95,
                            Name = "Metric1",
                            MetricNamespace = "Microsoft.Sql/servers/databases",
                            MetricName = "availability",
                            Operator = Insights.Operator.LessThan,
                            TimeAggregation = Insights.AggregationTypeEnum.Average,
                            SkipMetricValidation = false,
                            CriterionType = "StaticThresholdCriterion",
                        }
                    },
                    OdataType = "Microsoft.Azure.Monitor.SingleResourceMultipleMetricCriteria"
                },
                AutoMitigate = true,
                TargetResourceType = "Microsoft.Sql/servers/databases",
                TargetResourceRegion = envGroup.LocationName,
                Actions = new Insights.Inputs.MetricAlertActionArgs
                {
                    ActionGroupId = envGroup.PreventionActionGroup.Id // The action group to invoke when the alert fires
                },
            },
            new CustomResourceOptions
            {
                Parent = resourceGroup
            });
    }
}