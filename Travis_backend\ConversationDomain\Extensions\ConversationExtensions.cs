using System;
using System.Collections.Generic;
using System.Linq;
using Travis_backend.Constants;
using Travis_backend.ConversationDomain.Models;
using Travis_backend.ConversationDomain.ViewModels;
using Travis_backend.Enums;

namespace Travis_backend.ConversationDomain.Extensions;

public static class ConversationExtensions
{
    public static bool IsAssignedToTeamOnly(this Conversation conversation)
    {
        return conversation?.AssignedTeamId is not null && conversation?.AssigneeId is null;
    }

    public static bool IsAssignedToContactOwnerAndTeam(this Conversation conversation)
    {
        return conversation?.AssignedTeamId is not null && conversation?.AssigneeId is not null;
    }

    public static bool IsAssignedToContactOwnerOnly(this Conversation conversation)
    {
        return conversation?.AssignedTeamId is null && conversation?.AssigneeId is not null;
    }

    public static bool IsUnassigned(this Conversation conversation)
    {
        return conversation?.AssignedTeamId is null && conversation?.AssigneeId is null;
    }

    public static bool HasCollaborators(this Conversation conversation)
    {
        return conversation?.AdditionalAssignees?.Any() ?? false;
    }

    public static bool HasCollaborator(this Conversation conversation, StaffAccessControlAggregate staff)
    {
        return conversation?.AdditionalAssignees?.Select(x => x.AssigneeId)
            .Contains(staff.StaffId) ?? false;
    }

    public static bool HasMentionedStaffs(this Conversation conversation)
    {
        return conversation?.ChatHistory?.Any(
            y => y.Channel == ChannelTypes.Note
                 && y.MessageAssigneeId is not null
                 && y.UpdatedAt > DateTime.UtcNow.AddDays(-2)) ?? false;
    }

    public static bool HasMentioned(this Conversation conversation, StaffAccessControlAggregate staff)
    {
        return conversation?.ChatHistory?.Any(
            y => y.Channel == ChannelTypes.Note
                 && y.MessageAssigneeId == staff.StaffId
                 && y.UpdatedAt > DateTime.UtcNow.AddDays(-2)) ?? false;
    }

    public static bool IsAssociatedWithTeam(this StaffAccessControlAggregate staff, long? teamId)
    {
        if(teamId is null)
        {
            return false;
        }

        return staff?.AssociatedTeams?.Select(x => x.Id).ToList().Contains(teamId.Value) ?? false;
    }

    public static bool IsContactOwnerTeamAdmin(this StaffAccessControlAggregate staff, Conversation conversation)
    {
        if (conversation.AssigneeId is null || staff.RoleType != StaffUserRole.TeamAdmin)
        {
            return false;
        }

        // Get the set of associated team member IDs and check if it contains the conversation assignee
        return staff.GetAssociatedTeamMemberIds().Contains(conversation.AssigneeId.Value);
    }

    public static bool HasManagedTeamMemberInCollaborators(this StaffAccessControlAggregate staff, Conversation conversation)
    {
        if (staff.RoleType == StaffUserRole.Staff)
        {
            return false;
        }

        // Check if any collaborator is a team member
        return conversation.AdditionalAssignees != null &&
               conversation.AdditionalAssignees.Any(collaborator =>
                   collaborator.AssigneeId.HasValue
                   && staff.GetAssociatedTeamMemberIds().Contains(collaborator.AssigneeId.Value));
    }

    public static HashSet<long> GetAssociatedTeamMemberIds(
        this StaffAccessControlAggregate staff)
    {
        return staff.AssociatedTeams
            .SelectMany(x => x.TeamMemberStaffIds)
            .Where(id => id != staff.StaffId) // Exclude the current staff member
            .ToHashSet();
    }

    public static bool IsAssignedAsContactOwner(
        this StaffAccessControlAggregate staff,
        Conversation conversation)
    {
        return conversation?.AssigneeId == staff?.StaffId;
    }

    public static bool IsAssignedAsCollaborator(
        this StaffAccessControlAggregate staffAccessControlAggregate,
        Conversation conversation)
    {
        return conversation?.AdditionalAssignees?
            .Select(x => x.AssigneeId)
            .Contains(staffAccessControlAggregate.StaffId) ?? false;
    }
}