using System;
using System.Collections.Generic;
using System.Collections.Immutable;
using Travis_backend.SubscriptionPlanDomain.Constants;
using Travis_backend.SubscriptionPlanDomain.Models;
using Travis_backend.SubscriptionPlanDomain.Models.Common;

namespace Travis_backend.SubscriptionPlanDomain.Repositories;

public interface IPlanDefinitionRepository
{
    List<PlanDefinition> GetAll();
}

public class PlanDefinitionRepository : IPlanDefinitionRepository
{
    public static string[] SupportedCurrencies { get; } =
    {
        "aed", "aud", "brl", "cad", "cny", "eur", "gbp", "hkd", "idr", "inr", "myr", "sgd", "usd"
    };

    private const string V10 = "v10";
    private const string V9 = "v9";
    private const string V8 = "v8";
    private const string V7 = "v7";
    private const string V6 = "v6";
    private const string V5 = "v5";
    private const string V4 = "v4";
    private const string V3 = "v3";
    private const string V2 = "v2";
    private const string V0 = "v0";

    private readonly List<PlanDefinition> _allPlanDefinitions = new List<PlanDefinition>
    {
        new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "whatsapp", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "whatsapp",
                    Quantity = 1
                }
            },
            new List<Price>
            {
                new Price("USD", 29),
                new Price("AUD", 39),
                new Price("CAD", 29),
                new Price("CNY", 189),
                new Price("EUR", 29),
                new Price("GBP", 19),
                new Price("HKD", 229),
                new Price("IDR", 409000),
                new Price("MYR", 119),
                new Price("SGD", 39),
            },
            PlanTypes.AddOns,
            null,
            V8,
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "whatsapp", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "whatsapp",
                    Quantity = 1
                }
            },
            new List<Price>
            {
                new Price("USD", 39),
            },
            PlanTypes.AddOns,
            null,
            V0,
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new PlanDefinition(
                new List<Multilingual>
                {
                    new Multilingual("en", "whatsapp_phone_number", true)
                },
                new List<Multilingual>(),
                new List<FeatureQuantity>
                {
                    new FeatureQuantity
                    {
                        FeatureId = "whatsapp_phone_number",
                        Quantity = 1
                    }
                },
                new List<Price>
                {
                    new Price("USD", 15),
                    new Price("AED", 55),
                    new Price("AUD", 23),
                    new Price("BRL", 79),
                    new Price("CAD", 19),
                    new Price("CNY", 109),
                    new Price("EUR", 15),
                    new Price("GBP", 13),
                    new Price("HKD", 119),
                    new Price("IDR", 235000),
                    new Price("INR", 698),
                    new Price("MYR", 69),
                    new Price("SGD", 19),
                },
                PlanTypes.AddOns,
                null,
                V9,
                new List<string>
                {
                    "Active"
                },
                new Dictionary<string, object>(),
                string.Empty,
                string.Empty,
                null,
                DateTimeOffset.UtcNow,
                DateTimeOffset.UtcNow),
        new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "whatsapp_phone_number_yearly", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "whatsapp_phone_number",
                    Quantity = 1
                }
            },
            new List<Price>
            {
                new Price("SGD", 228),
                new Price("IDR", 2820000),
                new Price("BRL", 948),
                new Price("USD", 180),
                new Price("AED", 660),
                new Price("AUD", 276),
                new Price("CAD", 228),
                new Price("CNY", 1308),
                new Price("EUR", 180),
                new Price("GBP", 156),
                new Price("HKD", 1428),
                new Price("INR", 7698),
                new Price("MYR", 828),
            },
            PlanTypes.AddOns,
            null,
            V9,
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "shopify_integration", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "shopify",
                    Quantity = 1
                }
            },
            new List<Price>
            {
                new Price("USD", 49),
                new Price("AED", 179),
                new Price("AUD", 89),
                new Price("BRL", 269),
                new Price("CAD", 79),
                new Price("CNY", 369),
                new Price("EUR", 49),
                new Price("GBP", 49),
                new Price("HKD", 399),
                new Price("IDR", 699000),
                new Price("INR", 2098),
                new Price("MYR", 209),
                new Price("SGD", 69),
            },
            PlanTypes.AddOns,
            null,
            V9,
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "addon_shopify_monthly", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "shopify",
                    Quantity = 1
                }
            },
            new List<Price>
            {
                new Price("USD", 49),
            },
            PlanTypes.AddOns,
            null,
            V8,
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "addon_shopify_yearly", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "shopify",
                    Quantity = 1
                }
            },
            new List<Price>
            {
                new Price("USD", 588),
            },
            PlanTypes.AddOns,
            null,
            V8,
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "additional_2000_contact", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "contacts",
                    Quantity = 2000
                }
            },
            new List<Price>
            {
                new Price("USD", 49),
            },
            PlanTypes.AddOns,
            null,
            V0,
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "additional_2000_contact", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "contacts",
                    Quantity = 2000
                }
            },
            new List<Price>
            {
                new Price("USD", 49),
                new Price("HKD", 399),
                new Price("SGD", 69),
                new Price("CNY", 319),
                new Price("MYR", 209),
                new Price("IDR", 699000),
                new Price("EUR", 49),
                new Price("GBP", 39),
                new Price("CAD", 79),
                new Price("AUD", 89),
            },
            PlanTypes.AddOns,
            null,
            V8,
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "additional_2000_contact", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "contacts",
                    Quantity = 2000
                }
            },
            new List<Price>
            {
                new Price("USD", 49),
                new Price("AED", 179),
                new Price("AUD", 89),
                new Price("CAD", 79),
                new Price("CNY", 369),
                new Price("EUR", 49),
                new Price("GBP", 49),
                new Price("HKD", 399),
                new Price("SGD", 69),
            },
            PlanTypes.AddOns,
            null,
            V9,
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "additional_5000_contact", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "contacts",
                    Quantity = 5000
                }
            },
            new List<Price>
            {
                new Price("BRL", 269),
                new Price("IDR", 699000),
                new Price("INR", 698),
                new Price("MYR", 209),
            },
            PlanTypes.AddOns,
            null,
            V9,
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "sensitive_data_masking", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "sensitive_data_masking",
                    Quantity = 1
                }
            },
            new List<Price>
            {
                new Price("USD", 99),
            },
            PlanTypes.AddOns,
            null,
            V0,
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "sensitive_data_masking", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "sensitive_data_masking",
                    Quantity = 1
                }
            },
            new List<Price>
            {
                new Price("USD", 99),
                new Price("AUD", 139),
                new Price("CAD", 129),
                new Price("CNY", 629),
                new Price("EUR", 89),
                new Price("GBP", 79),
                new Price("HKD", 799),
                new Price("IDR", 1390000),
                new Price("MYR", 419),
                new Price("SGD", 139),
            },
            PlanTypes.AddOns,
            null,
            V8,
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "sensitive_data_masking", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "sensitive_data_masking",
                    Quantity = 1
                }
            },
            new List<Price>
            {
                new Price("USD", 99),
                new Price("AED", 369),
                new Price("AUD", 149),
                new Price("BRL", 539),
                new Price("CAD", 129),
                new Price("CNY", 709),
                new Price("EUR", 99),
                new Price("GBP", 89),
                new Price("HKD", 779),
                new Price("IDR", 1390000),
                new Price("INR", 4198),
                new Price("MYR", 439),
                new Price("SGD", 139),
            },
            PlanTypes.AddOns,
            null,
            V9,
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "consultation", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "consultation",
                    Quantity = 1
                }
            },
            new List<Price>
            {
                new Price("USD", 299),
                new Price("AUD", 399),
                new Price("CAD", 379),
                new Price("CNY", 1899),
                new Price("EUR", 269),
                new Price("GBP", 229),
                new Price("HKD", 2299),
                new Price("IDR", 4200000),
                new Price("MYR", 1249),
                new Price("SGD", 399),
            },
            PlanTypes.AddOns,
            null,
            V8,
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "support_and_consultation", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "support_and_consultation",
                    Quantity = 1
                }
            },
            new List<Price>
            {
                new Price("USD", 499),
                new Price("AUD", 699),
                new Price("CAD", 639),
                new Price("CNY", 3199),
                new Price("EUR", 449),
                new Price("GBP", 379),
                new Price("HKD", 3899),
                new Price("IDR", 6900000),
                new Price("MYR", 1999),
                new Price("SGD", 649),
            },
            PlanTypes.AddOns,
            null,
            V8,
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "priority_support", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "priority_support",
                    Quantity = 1
                }
            },
            new List<Price>
            {
                new Price("USD", 299),
                new Price("AUD", 399),
                new Price("CAD", 379),
                new Price("CNY", 1899),
                new Price("EUR", 269),
                new Price("GBP", 229),
                new Price("HKD", 2299),
                new Price("IDR", 4200000),
                new Price("MYR", 1249),
                new Price("SGD", 399),
            },
            PlanTypes.AddOns,
            null,
            V8,
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "priority_support_yearly", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "priority_support",
                    Quantity = 1
                }
            },
            new List<Price>
            {
                new Price("USD", 1188),
                new Price("AED", 4428),
                new Price("AUD", 1788),
                new Price("BRL", 6468),
                new Price("CAD", 1548),
                new Price("CNY", 8508),
                new Price("EUR", 1188),
                new Price("GBP", 1068),
                new Price("HKD", 9348),
                new Price("IDR", 16680000),
                new Price("INR", 4199),
                new Price("MYR", 5268),
                new Price("SGD", 1668),
            },
            PlanTypes.AddOns,
            null,
            V9,
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "agent_premium_monthy", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "agents",
                    Quantity = 1
                }
            },
            new List<Price>
            {
                new Price("USD", 39),
                new Price("AUD", 59),
                new Price("CAD", 49),
                new Price("CNY", 249),
                new Price("EUR", 39),
                new Price("GBP", 29),
                new Price("HKD", 299),
                new Price("IDR", 549000),
                new Price("MYR", 159),
                new Price("SGD", 59),
            },
            PlanTypes.AddOns,
            null,
            V8,
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "agent_premium_yearly", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "agents",
                    Quantity = 1
                }
            },
            new List<Price>
            {
                new Price("USD", 468),
                new Price("AUD", 708),
                new Price("CAD", 588),
                new Price("CNY", 2988),
                new Price("EUR", 468),
                new Price("GBP", 348),
                new Price("HKD", 3588),
                new Price("IDR", 6588000),
                new Price("MYR", 1908),
                new Price("SGD", 708),
            },
            PlanTypes.AddOns,
            null,
            V8,
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "agent_pro_monthly", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "agents",
                    Quantity = 1
                }
            },
            new List<Price>
            {
                new Price("USD", 19),
                new Price("AUD", 29),
                new Price("CAD", 29),
                new Price("CNY", 119),
                new Price("EUR", 19),
                new Price("GBP", 19),
                new Price("HKD", 149),
                new Price("IDR", 269000),
                new Price("MYR", 79),
                new Price("SGD", 29),
            },
            PlanTypes.AddOns,
            null,
            V8,
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "agent_pro_yearly", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "agents",
                    Quantity = 1
                }
            },
            new List<Price>
            {
                new Price("USD", 228),
                new Price("AUD", 348),
                new Price("CAD", 348),
                new Price("CNY", 1428),
                new Price("EUR", 228),
                new Price("GBP", 228),
                new Price("HKD", 1788),
                new Price("IDR", 3228000),
                new Price("MYR", 948),
                new Price("SGD", 348),
            },
            PlanTypes.AddOns,
            null,
            V8,
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "agent_premium_monthly", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "agents",
                    Quantity = 1
                }
            },
            new List<Price>
            {
                new Price("USD", 39),
                new Price("AED", 139),
                new Price("AUD", 59),
                new Price("BRL", 209),
                new Price("CAD", 59),
                new Price("CNY", 279),
                new Price("EUR", 39),
                new Price("GBP", 39),
                new Price("HKD", 299),
                new Price("IDR", 549000),
                new Price("MYR", 159),
                new Price("SGD", 59),
            },
            PlanTypes.AddOns,
            null,
            V9,
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "agent_premium_monthy", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "agents",
                    Quantity = 1
                }
            },
            new List<Price>
            {
                new Price("INR", 1698),
            },
            PlanTypes.AddOns,
            null,
            V9,
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "agent_pro_monthly", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "agents",
                    Quantity = 1
                }
            },
            new List<Price>
            {
                new Price("USD", 19),
                new Price("AED", 69),
                new Price("AUD", 29),
                new Price("BRL", 109),
                new Price("CAD", 29),
                new Price("CNY", 139),
                new Price("EUR", 19),
                new Price("GBP", 19),
                new Price("HKD", 149),
                new Price("IDR", 269000),
                new Price("INR", 898),
                new Price("MYR", 79),
                new Price("SGD", 29),
            },
            PlanTypes.AddOns,
            null,
            V9,
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "chatbot_automation_support_basic", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "chatbot_automation_support_basic",
                    Quantity = 1
                }
            },
            new List<Price>
            {
                new Price("USD", 3999),
                new Price("AED", 14689),
                new Price("AUD", 5899),
                new Price("BRL", 21499),
                new Price("CAD", 5179),
                new Price("CNY", 28469),
                new Price("EUR", 3969),
                new Price("GBP", 3359),
                new Price("HKD", 31399),
                new Price("IDR", 60052000),
                new Price("INR", 169399),
                new Price("MYR", 17749),
                new Price("SGD", 5619),
            },
            PlanTypes.AddOns,
            null,
            V9,
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "chatbot_automation_support_basic_oneoff", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "chatbot_automation_support_basic",
                    Quantity = 1
                }
            },
            new List<Price>
            {
                new Price("USD", 3999),
                new Price("AED", 14689),
                new Price("AUD", 5899),
                new Price("BRL", 21499),
                new Price("CAD", 5179),
                new Price("CNY", 28469),
                new Price("EUR", 3969),
                new Price("GBP", 3359),
                new Price("HKD", 31399),
                new Price("IDR", 60052000),
                new Price("INR", 169399),
                new Price("MYR", 17749),
                new Price("SGD", 5619),
            },
            PlanTypes.AddOns,
            null,
            V9,
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "chatbot_automation_support_intermediate", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "chatbot_automation_support_intermediate",
                    Quantity = 1
                }
            },
            new List<Price>
            {
                new Price("USD", 9999),
                new Price("AED", 36729),
                new Price("AUD", 14749),
                new Price("BRL", 53759),
                new Price("CAD", 12959),
                new Price("CNY", 71159),
                new Price("EUR", 9919),
                new Price("GBP", 8389),
                new Price("HKD", 78499),
                new Price("IDR", 150153000),
                new Price("INR", 423599),
                new Price("MYR", 44369),
                new Price("SGD", 14049),
            },
            PlanTypes.AddOns,
            null,
            V9,
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "chatbot_automation_support_intermediate_oneoff", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "chatbot_automation_support_intermediate",
                    Quantity = 1
                }
            },
            new List<Price>
            {
                new Price("USD", 9999),
                new Price("AED", 36729),
                new Price("AUD", 14749),
                new Price("BRL", 53759),
                new Price("CAD", 12959),
                new Price("CNY", 71159),
                new Price("EUR", 9919),
                new Price("GBP", 8389),
                new Price("HKD", 78499),
                new Price("IDR", 150153000),
                new Price("INR", 423599),
                new Price("MYR", 44369),
                new Price("SGD", 14049),
            },
            PlanTypes.AddOns,
            null,
            V9,
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "chatbot_automation_support_advanced", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "chatbot_automation_support_advanced",
                    Quantity = 1
                }
            },
            new List<Price>
            {
                new Price("USD", 19999),
                new Price("AED", 73459),
                new Price("AUD", 29489),
                new Price("BRL", 107519),
                new Price("CAD", 25909),
                new Price("CNY", 142309),
                new Price("EUR", 19839),
                new Price("GBP", 16779),
                new Price("HKD", 156999),
                new Price("IDR", 300320000),
                new Price("INR", 847199),
                new Price("MYR", 88739),
                new Price("SGD", 28089),
            },
            PlanTypes.AddOns,
            null,
            V9,
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "chatbot_automation_support_advanced_oneoff", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "chatbot_automation_support_advanced",
                    Quantity = 1
                }
            },
            new List<Price>
            {
                new Price("USD", 19999),
                new Price("AED", 73459),
                new Price("AUD", 29489),
                new Price("BRL", 107519),
                new Price("CAD", 25909),
                new Price("CNY", 142309),
                new Price("EUR", 19839),
                new Price("GBP", 16779),
                new Price("HKD", 156999),
                new Price("IDR", 300320000),
                new Price("INR", 847199),
                new Price("MYR", 88739),
                new Price("SGD", 28089),
            },
            PlanTypes.AddOns,
            null,
            V9,
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "chatbot_automation_support_enterprise", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "chatbot_automation_support_enterprise",
                    Quantity = 1
                }
            },
            new List<Price>
            {
                new Price("USD", 29999),
                new Price("AED", 110189),
                new Price("AUD", 44229),
                new Price("BRL", 161269),
                new Price("CAD", 38859),
                new Price("CNY", 213459),
                new Price("EUR", 29759),
                new Price("GBP", 25169),
                new Price("HKD", 235499),
                new Price("IDR", 450488000),
                new Price("INR", 1270799),
                new Price("MYR", 133109),
                new Price("SGD", 42129),
            },
            PlanTypes.AddOns,
            null,
            V9,
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "chatbot_automation_support_enterprise_oneoff", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "chatbot_automation_support_enterprise",
                    Quantity = 1
                }
            },
            new List<Price>
            {
                new Price("USD", 29999),
                new Price("AED", 110189),
                new Price("AUD", 44229),
                new Price("BRL", 161269),
                new Price("CAD", 38859),
                new Price("CNY", 213459),
                new Price("EUR", 29759),
                new Price("GBP", 25169),
                new Price("HKD", 235499),
                new Price("IDR", 450488000),
                new Price("INR", 1270799),
                new Price("MYR", 133109),
                new Price("SGD", 42129),
            },
            PlanTypes.AddOns,
            null,
            V9,
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "hubspot_integration", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "hubspot",
                    Quantity = 1
                }
            },
            new List<Price>
            {
                new Price("USD", 199),
                new Price("AED", 739),
                new Price("AUD", 299),
                new Price("BRL", 1099),
                new Price("CAD", 259),
                new Price("CNY", 1449),
                new Price("EUR", 199),
                new Price("GBP", 169),
                new Price("HKD", 1599),
                new Price("IDR", 3000000),
                new Price("INR", 8499),
                new Price("MYR", 889),
                new Price("SGD", 279),
            },
            PlanTypes.AddOns,
            null,
            V9,
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "internal_custom_subscription_payment", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "internal_custom_subscription_payment",
                    Quantity = 1
                }
            },
            new List<Price>
            {
                new Price("USD", 0),
                new Price("AED", 0),
                new Price("AUD", 0),
                new Price("BRL", 0),
                new Price("CAD", 0),
                new Price("CNY", 0),
                new Price("EUR", 0),
                new Price("GBP", 0),
                new Price("HKD", 0),
                new Price("IDR", 0),
                new Price("INR", 0),
                new Price("MYR", 0),
                new Price("SGD", 0),
            },
            PlanTypes.AddOns,
            null,
            V9,
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "onboarding_support_oneoff", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "onboarding_support",
                    Quantity = 1
                }
            },
            new List<Price>
            {
                new Price("USD", 499),
                new Price("AED", 1799),
                new Price("AUD", 739),
                new Price("BRL", 2699),
                new Price("CAD", 649),
                new Price("CNY", 3629),
                new Price("EUR", 499),
                new Price("GBP", 419),
                new Price("HKD", 3999),
                new Price("IDR", 7400000),
                new Price("INR", 21199),
                new Price("MYR", 2199),
                new Price("SGD", 699),
            },
            PlanTypes.AddOns,
            null,
            V9,
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "payment_integration", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "payment_integration",
                    Quantity = 1
                }
            },
            new List<Price>
            {
                new Price("USD", 99),
                new Price("AED", 369),
                new Price("AUD", 149),
                new Price("BRL", 539),
                new Price("CAD", 129),
                new Price("CNY", 709),
                new Price("EUR", 99),
                new Price("GBP", 89),
                new Price("HKD", 779),
                new Price("IDR", 1390000),
                new Price("INR", 4198),
                new Price("MYR", 439),
                new Price("SGD", 139),
            },
            PlanTypes.AddOns,
            null,
            V9,
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "salesforce_commerce_cloud", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "salesforce_commerce_cloud",
                    Quantity = 1
                }
            },
            new List<Price>
            {
                new Price("USD", 999),
                new Price("AED", 3669),
                new Price("AUD", 1479),
                new Price("BRL", 5379),
                new Price("CAD", 1299),
                new Price("CNY", 7119),
                new Price("EUR", 999),
                new Price("GBP", 839),
                new Price("HKD", 7849),
                new Price("IDR", 15002000),
                new Price("INR", 42399),
                new Price("MYR", 4439),
                new Price("SGD", 1409),
            },
            PlanTypes.AddOns,
            null,
            V9,
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "salesforce_integration", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "salesforce_integration",
                    Quantity = 1
                }
            },
            new List<Price>
            {
                new Price("USD", 499),
                new Price("AED", 1839),
                new Price("AUD", 739),
                new Price("BRL", 2689),
                new Price("CAD", 649),
                new Price("CNY", 3559),
                new Price("EUR", 499),
                new Price("GBP", 419),
                new Price("HKD", 3919),
                new Price("IDR", 7494000),
                new Price("INR", 21199),
                new Price("MYR", 2219),
                new Price("SGD", 709),
            },
            PlanTypes.AddOns,
            null,
            V9,
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "salesforce_marketing_cloud", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "salesforce_marketing_cloud",
                    Quantity = 1
                }
            },
            new List<Price>
            {
                new Price("USD", 999),
                new Price("AED", 3669),
                new Price("AUD", 1479),
                new Price("BRL", 5379),
                new Price("CAD", 1299),
                new Price("CNY", 7119),
                new Price("EUR", 999),
                new Price("GBP", 839),
                new Price("HKD", 7849),
                new Price("IDR", 15002000),
                new Price("INR", 42399),
                new Price("MYR", 4439),
                new Price("SGD", 1409),
            },
            PlanTypes.AddOns,
            null,
            V9,
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "unlimited_channels", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "channels",
                    Quantity = 50000000
                }
            },
            new List<Price>
            {
                new Price("USD", 199),
                new Price("AED", 739),
                new Price("AUD", 299),
                new Price("BRL", 1099),
                new Price("CAD", 259),
                new Price("CNY", 1449),
                new Price("EUR", 199),
                new Price("GBP", 169),
                new Price("HKD", 1599),
                new Price("IDR", 3000000),
                new Price("INR", 8498),
                new Price("MYR", 889),
                new Price("SGD", 279),
            },
            PlanTypes.AddOns,
            null,
            V9,
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "unlimited_contact", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "contacts",
                    Quantity = 50000000
                }
            },
            new List<Price>
            {
                new Price("USD", 499),
                new Price("AED", 1799),
                new Price("AUD", 739),
                new Price("BRL", 2699),
                new Price("CAD", 649),
                new Price("CNY", 3399),
                new Price("EUR", 499),
                new Price("GBP", 419),
                new Price("HKD", 3999),
                new Price("IDR", 7400000),
                new Price("MYR", 2199),
                new Price("SGD", 699),
            },
            PlanTypes.AddOns,
            null,
            V9,
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "whatsapp_qr_code", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "whatsapp_qr_code",
                    Quantity = 1
                }
            },
            new List<Price>
            {
                new Price("USD", 99),
                new Price("AED", 369),
                new Price("AUD", 149),
                new Price("BRL", 539),
                new Price("CAD", 129),
                new Price("CNY", 709),
                new Price("EUR", 99),
                new Price("GBP", 89),
                new Price("HKD", 779),
                new Price("IDR", 1390000),
                new Price("INR", 4198),
                new Price("MYR", 439),
                new Price("SGD", 139),
            },
            PlanTypes.AddOns,
            null,
            V9,
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "premium_monthly", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "contacts",
                    Quantity = 40000
                },
                new FeatureQuantity
                {
                    FeatureId = "messages",
                    Quantity = 50000000
                },
                new FeatureQuantity
                {
                    FeatureId = "agents",
                    Quantity = 5
                },
                new FeatureQuantity
                {
                    FeatureId = "campaigns",
                    Quantity = 50000000
                },
                new FeatureQuantity
                {
                    FeatureId = "automations",
                    Quantity = 999
                },
                new FeatureQuantity
                {
                    FeatureId = "channels",
                    Quantity = 10
                },
                new FeatureQuantity
                {
                    FeatureId = "api_calls",
                    Quantity = 100000
                },
                new FeatureQuantity
                {
                    FeatureId = "priority_support",
                    Quantity = 1
                },
                new FeatureQuantity
                {
                    FeatureId = "chatbot_automation_support_basic",
                    Quantity = 1
                },
                new FeatureQuantity
                {
                    FeatureId = "ai_features_total_usage",
                    Quantity = 2000
                },
                new FeatureQuantity
                {
                    FeatureId = FeatureId.FacebookLeadAds,
                    Quantity = 1
                },
                new FeatureQuantity
                {
                    FeatureId = FeatureId.MakeIntegration,
                    Quantity = 1
                },
                new FeatureQuantity
                {
                    FeatureId = FeatureId.ZapierIntegration,
                    Quantity = 1
                },
                new(FeatureId.FlowBuilderMaxActiveWorkflowCount, 25),
                new(FeatureId.FlowBuilderMaxNodesPerWorkflowCount, 100),
                new(FeatureId.FlowBuilderFlowEnrolment, 3000, 10000),
            },
            new List<Price>
            {
                new Price("IDR", 4890000),
                new Price("INR", 14999),
            },
            PlanTypes.Subscription,
            "premium",
            V9,
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "premium_monthly", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "contacts",
                    Quantity = 10000
                },
                new FeatureQuantity
                {
                    FeatureId = "messages",
                    Quantity = 50000000
                },
                new FeatureQuantity
                {
                    FeatureId = "agents",
                    Quantity = 5
                },
                new FeatureQuantity
                {
                    FeatureId = "campaigns",
                    Quantity = 50000000
                },
                new FeatureQuantity
                {
                    FeatureId = "automations",
                    Quantity = 999
                },
                new FeatureQuantity
                {
                    FeatureId = "channels",
                    Quantity = 10
                },
                new FeatureQuantity
                {
                    FeatureId = "api_calls",
                    Quantity = 100000
                },
                new FeatureQuantity
                {
                    FeatureId = "priority_support",
                    Quantity = 1
                },
                new FeatureQuantity
                {
                    FeatureId = "chatbot_automation_support_basic",
                    Quantity = 1
                },
                new FeatureQuantity
                {
                    FeatureId = "ai_features_total_usage",
                    Quantity = 2000
                },
                new FeatureQuantity
                {
                    FeatureId = FeatureId.FacebookLeadAds,
                    Quantity = 1
                },
                new FeatureQuantity
                {
                    FeatureId = FeatureId.MakeIntegration,
                    Quantity = 1
                },
                new FeatureQuantity
                {
                    FeatureId = FeatureId.ZapierIntegration,
                    Quantity = 1
                },
                new(FeatureId.FlowBuilderMaxActiveWorkflowCount, 25),
                new(FeatureId.FlowBuilderMaxNodesPerWorkflowCount, 100),
                new(FeatureId.FlowBuilderFlowEnrolment, 3000, 10000),
            },
            new List<Price>
            {
                new Price("USD", 349),
                new Price("AED", 1399),
                new Price("AUD", 519),
                new Price("CAD", 459),
                new Price("CNY", 2539),
                new Price("EUR", 349),
                new Price("GBP", 299),
                new Price("HKD", 2799),
                new Price("SGD", 479),
            },
            PlanTypes.Subscription,
            "premium",
            V9,
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "premium_monthly", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "contacts",
                    Quantity = 20000
                },
                new FeatureQuantity
                {
                    FeatureId = "messages",
                    Quantity = 50000000
                },
                new FeatureQuantity
                {
                    FeatureId = "agents",
                    Quantity = 5
                },
                new FeatureQuantity
                {
                    FeatureId = "campaigns",
                    Quantity = 50000000
                },
                new FeatureQuantity
                {
                    FeatureId = "automations",
                    Quantity = 999
                },
                new FeatureQuantity
                {
                    FeatureId = "channels",
                    Quantity = 10
                },
                new FeatureQuantity
                {
                    FeatureId = "api_calls",
                    Quantity = 100000
                },
                new FeatureQuantity
                {
                    FeatureId = "priority_support",
                    Quantity = 1
                },
                new FeatureQuantity
                {
                    FeatureId = "chatbot_automation_support_basic",
                    Quantity = 1
                },
                new FeatureQuantity
                {
                    FeatureId = "ai_features_total_usage",
                    Quantity = 2000
                },
                new FeatureQuantity
                {
                    FeatureId = FeatureId.FacebookLeadAds,
                    Quantity = 1
                },
                new FeatureQuantity
                {
                    FeatureId = FeatureId.MakeIntegration,
                    Quantity = 1
                },
                new FeatureQuantity
                {
                    FeatureId = FeatureId.ZapierIntegration,
                    Quantity = 1
                },
                new(FeatureId.FlowBuilderMaxActiveWorkflowCount, 25),
                new(FeatureId.FlowBuilderMaxNodesPerWorkflowCount, 100),
                new(FeatureId.FlowBuilderFlowEnrolment, 3000, 10000),
            },
            new List<Price>
            {
                new Price("MYR", 1459),
                new Price("BRL", 1899),
            },
            PlanTypes.Subscription,
            "premium",
            V9,
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "premium_yearly", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "contacts",
                    Quantity = 10000
                },
                new FeatureQuantity
                {
                    FeatureId = "messages",
                    Quantity = 50000000
                },
                new FeatureQuantity
                {
                    FeatureId = "agents",
                    Quantity = 5
                },
                new FeatureQuantity
                {
                    FeatureId = "campaigns",
                    Quantity = 50000000
                },
                new FeatureQuantity
                {
                    FeatureId = "automations",
                    Quantity = 999
                },
                new FeatureQuantity
                {
                    FeatureId = "channels",
                    Quantity = 10
                },
                new FeatureQuantity
                {
                    FeatureId = "api_calls",
                    Quantity = 1200000
                },
                new FeatureQuantity
                {
                    FeatureId = "onboarding_support",
                    Quantity = 1
                },
                new FeatureQuantity
                {
                    FeatureId = "chatbot_automation_support_basic",
                    Quantity = 1
                },
                new FeatureQuantity
                {
                    FeatureId = "priority_support",
                    Quantity = 1
                },
                new FeatureQuantity
                {
                    FeatureId = "ai_features_total_usage",
                    Quantity = 2000
                },
                new FeatureQuantity
                {
                    FeatureId = FeatureId.FacebookLeadAds,
                    Quantity = 1
                },
                new FeatureQuantity
                {
                    FeatureId = FeatureId.MakeIntegration,
                    Quantity = 1
                },
                new FeatureQuantity
                {
                    FeatureId = FeatureId.ZapierIntegration,
                    Quantity = 1
                },
                new(FeatureId.FlowBuilderMaxActiveWorkflowCount, 25),
                new(FeatureId.FlowBuilderMaxNodesPerWorkflowCount, 100),
                new(FeatureId.FlowBuilderFlowEnrolment, 3000, 10000),
            },
            new List<Price>
            {
                new Price("USD", 3588),
                new Price("AED", 11988),
                new Price("AUD", 5388),
                new Price("CAD", 4668),
                new Price("CNY", 23988),
                new Price("EUR", 3588),
                new Price("GBP", 3108),
                new Price("HKD", 28788),
                new Price("SGD", 4908),
            },
            PlanTypes.Subscription,
            "premium",
            V9,
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "premium_yearly", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "contacts",
                    Quantity = 20000
                },
                new FeatureQuantity
                {
                    FeatureId = "messages",
                    Quantity = 50000000
                },
                new FeatureQuantity
                {
                    FeatureId = "agents",
                    Quantity = 5
                },
                new FeatureQuantity
                {
                    FeatureId = "campaigns",
                    Quantity = 50000000
                },
                new FeatureQuantity
                {
                    FeatureId = "automations",
                    Quantity = 999
                },
                new FeatureQuantity
                {
                    FeatureId = "channels",
                    Quantity = 10
                },
                new FeatureQuantity
                {
                    FeatureId = "api_calls",
                    Quantity = 1200000
                },
                new FeatureQuantity
                {
                    FeatureId = "onboarding_support",
                    Quantity = 1
                },
                new FeatureQuantity
                {
                    FeatureId = "chatbot_automation_support_basic",
                    Quantity = 1
                },
                new FeatureQuantity
                {
                    FeatureId = "priority_support",
                    Quantity = 1
                },
                new FeatureQuantity
                {
                    FeatureId = "ai_features_total_usage",
                    Quantity = 2000
                },
                new FeatureQuantity
                {
                    FeatureId = FeatureId.FacebookLeadAds,
                    Quantity = 1
                },
                new FeatureQuantity
                {
                    FeatureId = FeatureId.MakeIntegration,
                    Quantity = 1
                },
                new FeatureQuantity
                {
                    FeatureId = FeatureId.ZapierIntegration,
                    Quantity = 1
                },
                new(FeatureId.FlowBuilderMaxActiveWorkflowCount, 25),
                new(FeatureId.FlowBuilderMaxNodesPerWorkflowCount, 100),
                new(FeatureId.FlowBuilderFlowEnrolment, 3000, 10000),
            },
            new List<Price>
            {
                new Price("MYR", 14988),
                new Price("BRL", 19188),
            },
            PlanTypes.Subscription,
            "premium",
            V9,
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "premium_yearly", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "contacts",
                    Quantity = 40000
                },
                new FeatureQuantity
                {
                    FeatureId = "messages",
                    Quantity = 50000000
                },
                new FeatureQuantity
                {
                    FeatureId = "agents",
                    Quantity = 5
                },
                new FeatureQuantity
                {
                    FeatureId = "campaigns",
                    Quantity = 50000000
                },
                new FeatureQuantity
                {
                    FeatureId = "automations",
                    Quantity = 999
                },
                new FeatureQuantity
                {
                    FeatureId = "channels",
                    Quantity = 10
                },
                new FeatureQuantity
                {
                    FeatureId = "api_calls",
                    Quantity = 1200000
                },
                new FeatureQuantity
                {
                    FeatureId = "ai_features_total_usage",
                    Quantity = 2000
                },
                new FeatureQuantity
                {
                    FeatureId = FeatureId.FacebookLeadAds,
                    Quantity = 1
                },
                new FeatureQuantity
                {
                    FeatureId = FeatureId.MakeIntegration,
                    Quantity = 1
                },
                new FeatureQuantity
                {
                    FeatureId = FeatureId.ZapierIntegration,
                    Quantity = 1
                },
                new(FeatureId.FlowBuilderMaxActiveWorkflowCount, 25),
                new(FeatureId.FlowBuilderMaxNodesPerWorkflowCount, 100),
                new(FeatureId.FlowBuilderFlowEnrolment, 3000, 10000),
            },
            new List<Price>
            {
                new Price("IDR", 50280000),
                new Price("INR", 155988),
            },
            PlanTypes.Subscription,
            "premium",
            V9,
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "pro_monthly", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "contacts",
                    Quantity = 2000
                },
                new FeatureQuantity
                {
                    FeatureId = "messages",
                    Quantity = 5000
                },
                new FeatureQuantity
                {
                    FeatureId = "agents",
                    Quantity = 3
                },
                new FeatureQuantity
                {
                    FeatureId = "campaigns",
                    Quantity = 5000
                },
                new FeatureQuantity
                {
                    FeatureId = "automations",
                    Quantity = 10
                },
                new FeatureQuantity
                {
                    FeatureId = "channels",
                    Quantity = 5
                },
                new FeatureQuantity
                {
                    FeatureId = "api_calls",
                    Quantity = 5000
                },
                new FeatureQuantity
                {
                    FeatureId = "ai_features_total_usage",
                    Quantity = 2000
                },
                new FeatureQuantity
                {
                    FeatureId = FeatureId.FacebookLeadAds,
                    Quantity = 1
                },
                new FeatureQuantity
                {
                    FeatureId = FeatureId.MakeIntegration,
                    Quantity = 1
                },
                new FeatureQuantity
                {
                    FeatureId = FeatureId.ZapierIntegration,
                    Quantity = 1
                },
                new(FeatureId.FlowBuilderMaxActiveWorkflowCount, 3),
                new(FeatureId.FlowBuilderMaxNodesPerWorkflowCount, 25),
                new(FeatureId.FlowBuilderFlowEnrolment, 500, 3000),
            },
            new List<Price>
            {
                new Price("USD", 99),
                new Price("AUD", 149),
                new Price("CAD", 129),
                new Price("CNY", 729),
                new Price("EUR", 99),
                new Price("GBP", 89),
                new Price("HKD", 799),
                new Price("SGD", 139),
            },
            PlanTypes.Subscription,
            "pro",
            V9,
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "pro_monthly", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "contacts",
                    Quantity = 4000
                },
                new FeatureQuantity
                {
                    FeatureId = "messages",
                    Quantity = 5000
                },
                new FeatureQuantity
                {
                    FeatureId = "agents",
                    Quantity = 3
                },
                new FeatureQuantity
                {
                    FeatureId = "campaigns",
                    Quantity = 5000
                },
                new FeatureQuantity
                {
                    FeatureId = "automations",
                    Quantity = 10
                },
                new FeatureQuantity
                {
                    FeatureId = "channels",
                    Quantity = 5
                },
                new FeatureQuantity
                {
                    FeatureId = "api_calls",
                    Quantity = 5000
                },
                new FeatureQuantity
                {
                    FeatureId = "ai_features_total_usage",
                    Quantity = 2000
                },
                new FeatureQuantity
                {
                    FeatureId = FeatureId.FacebookLeadAds,
                    Quantity = 1
                },
                new FeatureQuantity
                {
                    FeatureId = FeatureId.MakeIntegration,
                    Quantity = 1
                },
                new FeatureQuantity
                {
                    FeatureId = FeatureId.ZapierIntegration,
                    Quantity = 1
                },
                new(FeatureId.FlowBuilderMaxActiveWorkflowCount, 3),
                new(FeatureId.FlowBuilderMaxNodesPerWorkflowCount, 25),
                new(FeatureId.FlowBuilderFlowEnrolment, 500, 3000),
            },
            new List<Price>
            {
                new Price("AED", 699),
            },
            PlanTypes.Subscription,
            "pro",
            V9,
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "pro_monthly", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "contacts",
                    Quantity = 5000
                },
                new FeatureQuantity
                {
                    FeatureId = "messages",
                    Quantity = 5000
                },
                new FeatureQuantity
                {
                    FeatureId = "agents",
                    Quantity = 3
                },
                new FeatureQuantity
                {
                    FeatureId = "campaigns",
                    Quantity = 5000
                },
                new FeatureQuantity
                {
                    FeatureId = "automations",
                    Quantity = 10
                },
                new FeatureQuantity
                {
                    FeatureId = "channels",
                    Quantity = 5
                },
                new FeatureQuantity
                {
                    FeatureId = "api_calls",
                    Quantity = 5000
                },
                new FeatureQuantity
                {
                    FeatureId = "ai_features_total_usage",
                    Quantity = 2000
                },
                new FeatureQuantity
                {
                    FeatureId = FeatureId.FacebookLeadAds,
                    Quantity = 1
                },
                new FeatureQuantity
                {
                    FeatureId = FeatureId.MakeIntegration,
                    Quantity = 1
                },
                new FeatureQuantity
                {
                    FeatureId = FeatureId.ZapierIntegration,
                    Quantity = 1
                },
                new(FeatureId.FlowBuilderMaxActiveWorkflowCount, 3),
                new(FeatureId.FlowBuilderMaxNodesPerWorkflowCount, 25),
                new(FeatureId.FlowBuilderFlowEnrolment, 500, 3000),
            },
            new List<Price>
            {
                new Price("BRL", 539),
                new Price("IDR", 1390000),
                new Price("MYR", 409),
            },
            PlanTypes.Subscription,
            "pro",
            V9,
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "pro_monthly", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "contacts",
                    Quantity = 10000
                },
                new FeatureQuantity
                {
                    FeatureId = "messages",
                    Quantity = 5000
                },
                new FeatureQuantity
                {
                    FeatureId = "agents",
                    Quantity = 3
                },
                new FeatureQuantity
                {
                    FeatureId = "campaigns",
                    Quantity = 5000
                },
                new FeatureQuantity
                {
                    FeatureId = "automations",
                    Quantity = 10
                },
                new FeatureQuantity
                {
                    FeatureId = "channels",
                    Quantity = 5
                },
                new FeatureQuantity
                {
                    FeatureId = "api_calls",
                    Quantity = 5000
                },
                new FeatureQuantity
                {
                    FeatureId = "ai_features_total_usage",
                    Quantity = 2000
                },
                new FeatureQuantity
                {
                    FeatureId = FeatureId.FacebookLeadAds,
                    Quantity = 1
                },
                new FeatureQuantity
                {
                    FeatureId = FeatureId.MakeIntegration,
                    Quantity = 1
                },
                new FeatureQuantity
                {
                    FeatureId = FeatureId.ZapierIntegration,
                    Quantity = 1
                },
                new(FeatureId.FlowBuilderMaxActiveWorkflowCount, 3),
                new(FeatureId.FlowBuilderMaxNodesPerWorkflowCount, 25),
                new(FeatureId.FlowBuilderFlowEnrolment, 500, 3000),
            },
            new List<Price>
            {
                new Price("INR", 4199),
            },
            PlanTypes.Subscription,
            "pro",
            V9,
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "pro_yearly", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "contacts",
                    Quantity = 2000
                },
                new FeatureQuantity
                {
                    FeatureId = "messages",
                    Quantity = 60000
                },
                new FeatureQuantity
                {
                    FeatureId = "agents",
                    Quantity = 3
                },
                new FeatureQuantity
                {
                    FeatureId = "campaigns",
                    Quantity = 60000
                },
                new FeatureQuantity
                {
                    FeatureId = "automations",
                    Quantity = 10
                },
                new FeatureQuantity
                {
                    FeatureId = "channels",
                    Quantity = 5
                },
                new FeatureQuantity
                {
                    FeatureId = "api_calls",
                    Quantity = 60000
                },
                new FeatureQuantity
                {
                    FeatureId = "onboarding_support",
                    Quantity = 1
                },
                new FeatureQuantity
                {
                    FeatureId = "chatbot_automation_support_basic",
                    Quantity = 1
                },
                new FeatureQuantity
                {
                    FeatureId = "ai_features_total_usage",
                    Quantity = 2000
                },
                new FeatureQuantity
                {
                    FeatureId = FeatureId.FacebookLeadAds,
                    Quantity = 1
                },
                new FeatureQuantity
                {
                    FeatureId = FeatureId.MakeIntegration,
                    Quantity = 1
                },
                new FeatureQuantity
                {
                    FeatureId = FeatureId.ZapierIntegration,
                    Quantity = 1
                },
                new(FeatureId.FlowBuilderMaxActiveWorkflowCount, 3),
                new(FeatureId.FlowBuilderMaxNodesPerWorkflowCount, 25),
                new(FeatureId.FlowBuilderFlowEnrolment, 500, 3000),
            },
            new List<Price>
            {
                new Price("USD", 948),
                new Price("AUD", 1428),
                new Price("CAD", 1308),
                new Price("CNY", 6588),
                new Price("EUR", 948),
                new Price("GBP", 828),
                new Price("HKD", 7188),
                new Price("SGD", 1308),
            },
            PlanTypes.Subscription,
            "pro",
            V9,
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "pro_yearly", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "contacts",
                    Quantity = 4000
                },
                new FeatureQuantity
                {
                    FeatureId = "messages",
                    Quantity = 60000
                },
                new FeatureQuantity
                {
                    FeatureId = "agents",
                    Quantity = 3
                },
                new FeatureQuantity
                {
                    FeatureId = "campaigns",
                    Quantity = 60000
                },
                new FeatureQuantity
                {
                    FeatureId = "automations",
                    Quantity = 10
                },
                new FeatureQuantity
                {
                    FeatureId = "channels",
                    Quantity = 5
                },
                new FeatureQuantity
                {
                    FeatureId = "api_calls",
                    Quantity = 60000
                },
                new FeatureQuantity
                {
                    FeatureId = "onboarding_support",
                    Quantity = 1
                },
                new FeatureQuantity
                {
                    FeatureId = "chatbot_automation_support_basic",
                    Quantity = 1
                },
                new FeatureQuantity
                {
                    FeatureId = "ai_features_total_usage",
                    Quantity = 2000
                },
                new FeatureQuantity
                {
                    FeatureId = FeatureId.FacebookLeadAds,
                    Quantity = 1
                },
                new FeatureQuantity
                {
                    FeatureId = FeatureId.MakeIntegration,
                    Quantity = 1
                },
                new FeatureQuantity
                {
                    FeatureId = FeatureId.ZapierIntegration,
                    Quantity = 1
                },
                new(FeatureId.FlowBuilderMaxActiveWorkflowCount, 3),
                new(FeatureId.FlowBuilderMaxNodesPerWorkflowCount, 25),
                new(FeatureId.FlowBuilderFlowEnrolment, 500, 3000),
            },
            new List<Price>
            {
                new Price("AED", 7188),
            },
            PlanTypes.Subscription,
            "pro",
            V9,
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "pro_yearly", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "contacts",
                    Quantity = 5000
                },
                new FeatureQuantity
                {
                    FeatureId = "messages",
                    Quantity = 60000
                },
                new FeatureQuantity
                {
                    FeatureId = "agents",
                    Quantity = 3
                },
                new FeatureQuantity
                {
                    FeatureId = "campaigns",
                    Quantity = 60000
                },
                new FeatureQuantity
                {
                    FeatureId = "automations",
                    Quantity = 10
                },
                new FeatureQuantity
                {
                    FeatureId = "channels",
                    Quantity = 5
                },
                new FeatureQuantity
                {
                    FeatureId = "api_calls",
                    Quantity = 60000
                },
                new FeatureQuantity
                {
                    FeatureId = "onboarding_support",
                    Quantity = 1
                },
                new FeatureQuantity
                {
                    FeatureId = "chatbot_automation_support_basic",
                    Quantity = 1
                },
                new FeatureQuantity
                {
                    FeatureId = "ai_features_total_usage",
                    Quantity = 2000
                },
                new FeatureQuantity
                {
                    FeatureId = FeatureId.FacebookLeadAds,
                    Quantity = 2000
                },
                new FeatureQuantity
                {
                    FeatureId = FeatureId.MakeIntegration,
                    Quantity = 1
                },
                new FeatureQuantity
                {
                    FeatureId = FeatureId.ZapierIntegration,
                    Quantity = 1
                },
                new(FeatureId.FlowBuilderMaxActiveWorkflowCount, 3),
                new(FeatureId.FlowBuilderMaxNodesPerWorkflowCount, 25),
                new(FeatureId.FlowBuilderFlowEnrolment, 500, 3000),
            },
            new List<Price>
            {
                new Price("BRL", 5148),
                new Price("IDR", 13080000),
                new Price("MYR", 3948),
            },
            PlanTypes.Subscription,
            "pro",
            V9,
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "pro_yearly", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "contacts",
                    Quantity = 10000
                },
                new FeatureQuantity
                {
                    FeatureId = "messages",
                    Quantity = 60000
                },
                new FeatureQuantity
                {
                    FeatureId = "agents",
                    Quantity = 3
                },
                new FeatureQuantity
                {
                    FeatureId = "campaigns",
                    Quantity = 60000
                },
                new FeatureQuantity
                {
                    FeatureId = "automations",
                    Quantity = 10
                },
                new FeatureQuantity
                {
                    FeatureId = "channels",
                    Quantity = 5
                },
                new FeatureQuantity
                {
                    FeatureId = "api_calls",
                    Quantity = 60000
                },
                new FeatureQuantity
                {
                    FeatureId = "onboarding_support",
                    Quantity = 1
                },
                new FeatureQuantity
                {
                    FeatureId = "chatbot_automation_support_basic",
                    Quantity = 1
                },
                new FeatureQuantity
                {
                    FeatureId = "ai_features_total_usage",
                    Quantity = 2000
                },
                new FeatureQuantity
                {
                    FeatureId = FeatureId.FacebookLeadAds,
                    Quantity = 1
                },
                new FeatureQuantity
                {
                    FeatureId = FeatureId.MakeIntegration,
                    Quantity = 1
                },
                new FeatureQuantity
                {
                    FeatureId = FeatureId.ZapierIntegration,
                    Quantity = 1
                },
                new(FeatureId.FlowBuilderMaxActiveWorkflowCount, 3),
                new(FeatureId.FlowBuilderMaxNodesPerWorkflowCount, 25),
                new(FeatureId.FlowBuilderFlowEnrolment, 500, 3000),
            },
            new List<Price>
            {
                new Price("INR", 40788),
            },
            PlanTypes.Subscription,
            "pro",
            V9,
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "additional_15k_broadcast", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "campaigns",
                    Quantity = 15000
                },
            },
            new List<Price>
            {
                new Price("GBP", 110),
            },
            PlanTypes.AddOns,
            null,
            V8,
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "premium_monthly", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "contacts",
                    Quantity = 10000
                },
                new FeatureQuantity
                {
                    FeatureId = "messages",
                    Quantity = 50000000
                },
                new FeatureQuantity
                {
                    FeatureId = "agents",
                    Quantity = 5
                },
                new FeatureQuantity
                {
                    FeatureId = "campaigns",
                    Quantity = 50000000
                },
                new FeatureQuantity
                {
                    FeatureId = "automations",
                    Quantity = 999
                },
                new FeatureQuantity
                {
                    FeatureId = "channels",
                    Quantity = 999
                },
                new FeatureQuantity
                {
                    FeatureId = "api_calls",
                    Quantity = 100000
                },
                new FeatureQuantity
                {
                    FeatureId = "ai_features_total_usage",
                    Quantity = 2000
                },
                new FeatureQuantity
                {
                    FeatureId = FeatureId.FacebookLeadAds,
                    Quantity = 1
                },
                new FeatureQuantity
                {
                    FeatureId = FeatureId.MakeIntegration,
                    Quantity = 1
                },
                new FeatureQuantity
                {
                    FeatureId = FeatureId.ZapierIntegration,
                    Quantity = 1
                },
                new(FeatureId.FlowBuilderMaxActiveWorkflowCount, 25),
                new(FeatureId.FlowBuilderMaxNodesPerWorkflowCount, 100),
                new(FeatureId.FlowBuilderFlowEnrolment, 3000, 10000),
            },
            new List<Price>
            {
                new Price("USD", 349),
                new Price("AUD", 479),
                new Price("CAD", 439),
                new Price("CNY", 2239),
                new Price("EUR", 309),
                new Price("GBP", 269),
                new Price("HKD", 2799),
                new Price("IDR", 4890000),
                new Price("MYR", 1459),
                new Price("SGD", 479),
            },
            PlanTypes.Subscription,
            "premium",
            V8,
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "premium_yearly", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "contacts",
                    Quantity = 10000
                },
                new FeatureQuantity
                {
                    FeatureId = "messages",
                    Quantity = 50000000
                },
                new FeatureQuantity
                {
                    FeatureId = "agents",
                    Quantity = 5
                },
                new FeatureQuantity
                {
                    FeatureId = "campaigns",
                    Quantity = 50000000
                },
                new FeatureQuantity
                {
                    FeatureId = "automations",
                    Quantity = 999
                },
                new FeatureQuantity
                {
                    FeatureId = "channels",
                    Quantity = 999
                },
                new FeatureQuantity
                {
                    FeatureId = "api_calls",
                    Quantity = 1200000
                },
                new FeatureQuantity
                {
                    FeatureId = "ai_features_total_usage",
                    Quantity = 2000
                },
                new FeatureQuantity
                {
                    FeatureId = FeatureId.FacebookLeadAds,
                    Quantity = 1
                },
                new FeatureQuantity
                {
                    FeatureId = FeatureId.MakeIntegration,
                    Quantity = 1
                },
                new FeatureQuantity
                {
                    FeatureId = FeatureId.ZapierIntegration,
                    Quantity = 1
                },
                new(FeatureId.FlowBuilderMaxActiveWorkflowCount, 25),
                new(FeatureId.FlowBuilderMaxNodesPerWorkflowCount, 100),
                new(FeatureId.FlowBuilderFlowEnrolment, 3000, 10000),
            },
            new List<Price>
            {
                new Price("USD", 3588),
                new Price("AUD", 5028),
                new Price("CAD", 4548),
                new Price("CNY", 23148),
                new Price("EUR", 3228),
                new Price("GBP", 2748),
                new Price("HKD", 28788),
                new Price("IDR", 50280000),
                new Price("MYR", 14988),
                new Price("SGD", 4908),
            },
            PlanTypes.Subscription,
            "premium",
            V8,
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "pro_monthly", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "contacts",
                    Quantity = 2000
                },
                new FeatureQuantity
                {
                    FeatureId = "messages",
                    Quantity = 5000
                },
                new FeatureQuantity
                {
                    FeatureId = "agents",
                    Quantity = 3
                },
                new FeatureQuantity
                {
                    FeatureId = "campaigns",
                    Quantity = 5000
                },
                new FeatureQuantity
                {
                    FeatureId = "automations",
                    Quantity = 5
                },
                new FeatureQuantity
                {
                    FeatureId = "channels",
                    Quantity = 999
                },
                new FeatureQuantity
                {
                    FeatureId = "api_calls",
                    Quantity = 5000
                },
                new FeatureQuantity
                {
                    FeatureId = "ai_features_total_usage",
                    Quantity = 2000
                },
                new FeatureQuantity
                {
                    FeatureId = FeatureId.FacebookLeadAds,
                    Quantity = 1
                },
                new FeatureQuantity
                {
                    FeatureId = FeatureId.MakeIntegration,
                    Quantity = 1
                },
                new FeatureQuantity
                {
                    FeatureId = FeatureId.ZapierIntegration,
                    Quantity = 1
                },
                new(FeatureId.FlowBuilderMaxActiveWorkflowCount, 3),
                new(FeatureId.FlowBuilderMaxNodesPerWorkflowCount, 25),
                new(FeatureId.FlowBuilderFlowEnrolment, 500, 3000),
            },
            new List<Price>
            {
                new Price("USD", 99),
                new Price("AUD", 139),
                new Price("CAD", 129),
                new Price("CNY", 639),
                new Price("EUR", 89),
                new Price("GBP", 79),
                new Price("HKD", 799),
                new Price("IDR", 1390000),
                new Price("MYR", 409),
                new Price("SGD", 139),
            },
            PlanTypes.Subscription,
            "pro",
            V8,
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "pro_yearly", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "contacts",
                    Quantity = 2000
                },
                new FeatureQuantity
                {
                    FeatureId = "messages",
                    Quantity = 60000
                },
                new FeatureQuantity
                {
                    FeatureId = "agents",
                    Quantity = 3
                },
                new FeatureQuantity
                {
                    FeatureId = "campaigns",
                    Quantity = 60000
                },
                new FeatureQuantity
                {
                    FeatureId = "automations",
                    Quantity = 5
                },
                new FeatureQuantity
                {
                    FeatureId = "channels",
                    Quantity = 999
                },
                new FeatureQuantity
                {
                    FeatureId = "api_calls",
                    Quantity = 60000
                },
                new FeatureQuantity
                {
                    FeatureId = "ai_features_total_usage",
                    Quantity = 2000
                },
                new FeatureQuantity
                {
                    FeatureId = FeatureId.FacebookLeadAds,
                    Quantity = 1
                },
                new FeatureQuantity
                {
                    FeatureId = FeatureId.MakeIntegration,
                    Quantity = 1
                },
                new FeatureQuantity
                {
                    FeatureId = FeatureId.ZapierIntegration,
                    Quantity = 1
                },
                new(FeatureId.FlowBuilderMaxActiveWorkflowCount, 3),
                new(FeatureId.FlowBuilderMaxNodesPerWorkflowCount, 25),
                new(FeatureId.FlowBuilderFlowEnrolment, 500, 3000),
            },
            new List<Price>
            {
                new Price("USD", 948),
                new Price("AUD", 1308),
                new Price("CAD", 1188),
                new Price("CNY", 6108),
                new Price("EUR", 828),
                new Price("GBP", 708),
                new Price("HKD", 7188),
                new Price("IDR", 13080000),
                new Price("MYR", 3948),
                new Price("SGD", 1308),
            },
            PlanTypes.Subscription,
            "pro",
            V8,
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "premium_monthly", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "contacts",
                    Quantity = 10000
                },
                new FeatureQuantity
                {
                    FeatureId = "messages",
                    Quantity = 5000000
                },
                new FeatureQuantity
                {
                    FeatureId = "agents",
                    Quantity = 5
                },
                new FeatureQuantity
                {
                    FeatureId = "campaigns",
                    Quantity = 50000000
                },
                new FeatureQuantity
                {
                    FeatureId = "automations",
                    Quantity = 999
                },
                new FeatureQuantity
                {
                    FeatureId = "channels",
                    Quantity = 999
                },
                new FeatureQuantity
                {
                    FeatureId = "api_calls",
                    Quantity = 100000
                },
                new FeatureQuantity
                {
                    FeatureId = "ai_features_total_usage",
                    Quantity = 2000
                },
                new FeatureQuantity
                {
                    FeatureId = FeatureId.FacebookLeadAds,
                    Quantity = 1
                },
                new FeatureQuantity
                {
                    FeatureId = FeatureId.MakeIntegration,
                    Quantity = 1
                },
                new FeatureQuantity
                {
                    FeatureId = FeatureId.ZapierIntegration,
                    Quantity = 1
                },
                new(FeatureId.FlowBuilderMaxActiveWorkflowCount, 25),
                new(FeatureId.FlowBuilderMaxNodesPerWorkflowCount, 100),
                new(FeatureId.FlowBuilderFlowEnrolment, 3000, 10000),
            },
            new List<Price>
            {
                new Price("USD", 349),
            },
            PlanTypes.Subscription,
            "premium",
            V7,
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "premium_monthly_sg", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "contacts",
                    Quantity = 10000
                },
                new FeatureQuantity
                {
                    FeatureId = "messages",
                    Quantity = 50000000
                },
                new FeatureQuantity
                {
                    FeatureId = "agents",
                    Quantity = 5
                },
                new FeatureQuantity
                {
                    FeatureId = "campaigns",
                    Quantity = 50000000
                },
                new FeatureQuantity
                {
                    FeatureId = "automations",
                    Quantity = 999
                },
                new FeatureQuantity
                {
                    FeatureId = "channels",
                    Quantity = 999
                },
                new FeatureQuantity
                {
                    FeatureId = "api_calls",
                    Quantity = 100000
                },
                new FeatureQuantity
                {
                    FeatureId = "ai_features_total_usage",
                    Quantity = 2000
                },
                new FeatureQuantity
                {
                    FeatureId = FeatureId.FacebookLeadAds,
                    Quantity = 1
                },
                new FeatureQuantity
                {
                    FeatureId = FeatureId.MakeIntegration,
                    Quantity = 1
                },
                new FeatureQuantity
                {
                    FeatureId = FeatureId.ZapierIntegration,
                    Quantity = 1
                },
                new(FeatureId.FlowBuilderMaxActiveWorkflowCount, 25),
                new(FeatureId.FlowBuilderMaxNodesPerWorkflowCount, 100),
                new(FeatureId.FlowBuilderFlowEnrolment, 3000, 10000),
            },
            new List<Price>
            {
                new Price("SGD", 479),
            },
            PlanTypes.Subscription,
            "premium",
            V7,
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "premium_monthly_usd_custom_rglobalcar", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "contacts",
                    Quantity = 10000
                },
                new FeatureQuantity
                {
                    FeatureId = "messages",
                    Quantity = 50000000
                },
                new FeatureQuantity
                {
                    FeatureId = "agents",
                    Quantity = 10
                },
                new FeatureQuantity
                {
                    FeatureId = "campaigns",
                    Quantity = 50000000
                },
                new FeatureQuantity
                {
                    FeatureId = "automations",
                    Quantity = 999
                },
                new FeatureQuantity
                {
                    FeatureId = "channels",
                    Quantity = 999
                },
                new FeatureQuantity
                {
                    FeatureId = "api_calls",
                    Quantity = 100000
                },
                new FeatureQuantity
                {
                    FeatureId = "ai_features_total_usage",
                    Quantity = 2000
                },
                new FeatureQuantity
                {
                    FeatureId = FeatureId.FacebookLeadAds,
                    Quantity = 1
                },
                new FeatureQuantity
                {
                    FeatureId = FeatureId.MakeIntegration,
                    Quantity = 1
                },
                new FeatureQuantity
                {
                    FeatureId = FeatureId.ZapierIntegration,
                    Quantity = 1
                },
                new(FeatureId.FlowBuilderMaxActiveWorkflowCount, 25),
                new(FeatureId.FlowBuilderMaxNodesPerWorkflowCount, 100),
                new(FeatureId.FlowBuilderFlowEnrolment, 3000, 10000),
            },
            new List<Price>
            {
                new Price("USD", 79),
            },
            PlanTypes.Subscription,
            "premium",
            V7,
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "premium_yearly", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "contacts",
                    Quantity = 10000
                },
                new FeatureQuantity
                {
                    FeatureId = "messages",
                    Quantity = 5000000
                },
                new FeatureQuantity
                {
                    FeatureId = "agents",
                    Quantity = 5
                },
                new FeatureQuantity
                {
                    FeatureId = "campaigns",
                    Quantity = 50000000
                },
                new FeatureQuantity
                {
                    FeatureId = "automations",
                    Quantity = 999
                },
                new FeatureQuantity
                {
                    FeatureId = "channels",
                    Quantity = 999
                },
                new FeatureQuantity
                {
                    FeatureId = "api_calls",
                    Quantity = 1200000
                },
                new FeatureQuantity
                {
                    FeatureId = "ai_features_total_usage",
                    Quantity = 2000
                },
                new FeatureQuantity
                {
                    FeatureId = FeatureId.FacebookLeadAds,
                    Quantity = 1
                },
                new FeatureQuantity
                {
                    FeatureId = FeatureId.MakeIntegration,
                    Quantity = 1
                },
                new FeatureQuantity
                {
                    FeatureId = FeatureId.ZapierIntegration,
                    Quantity = 1
                },
                new(FeatureId.FlowBuilderMaxActiveWorkflowCount, 25),
                new(FeatureId.FlowBuilderMaxNodesPerWorkflowCount, 100),
                new(FeatureId.FlowBuilderFlowEnrolment, 3000, 10000),
            },
            new List<Price>
            {
                new Price("USD", 3588),
            },
            PlanTypes.Subscription,
            "premium",
            V7,
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "pro_monthly", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "contacts",
                    Quantity = 2000
                },
                new FeatureQuantity
                {
                    FeatureId = "messages",
                    Quantity = 5000
                },
                new FeatureQuantity
                {
                    FeatureId = "agents",
                    Quantity = 3
                },
                new FeatureQuantity
                {
                    FeatureId = "campaigns",
                    Quantity = 5000
                },
                new FeatureQuantity
                {
                    FeatureId = "automations",
                    Quantity = 5
                },
                new FeatureQuantity
                {
                    FeatureId = "channels",
                    Quantity = 999
                },
                new FeatureQuantity
                {
                    FeatureId = "api_calls",
                    Quantity = 5000
                },
                new FeatureQuantity
                {
                    FeatureId = "ai_features_total_usage",
                    Quantity = 2000
                },
                new FeatureQuantity
                {
                    FeatureId = FeatureId.FacebookLeadAds,
                    Quantity = 1
                },
                new FeatureQuantity
                {
                    FeatureId = FeatureId.MakeIntegration,
                    Quantity = 1
                },
                new FeatureQuantity
                {
                    FeatureId = FeatureId.ZapierIntegration,
                    Quantity = 1
                },
                new(FeatureId.FlowBuilderMaxActiveWorkflowCount, 3),
                new(FeatureId.FlowBuilderMaxNodesPerWorkflowCount, 25),
                new(FeatureId.FlowBuilderFlowEnrolment, 500, 3000),
            },
            new List<Price>
            {
                new Price("USD", 99),
            },
            PlanTypes.Subscription,
            "pro",
            V7,
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "pro_monthly", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "contacts",
                    Quantity = 2000
                },
                new FeatureQuantity
                {
                    FeatureId = "messages",
                    Quantity = 5000
                },
                new FeatureQuantity
                {
                    FeatureId = "agents",
                    Quantity = 3
                },
                new FeatureQuantity
                {
                    FeatureId = "campaigns",
                    Quantity = 5000
                },
                new FeatureQuantity
                {
                    FeatureId = "automations",
                    Quantity = 999
                },
                new FeatureQuantity
                {
                    FeatureId = "channels",
                    Quantity = 999
                },
                new FeatureQuantity
                {
                    FeatureId = "api_calls",
                    Quantity = 5000
                },
                new FeatureQuantity
                {
                    FeatureId = "ai_features_total_usage",
                    Quantity = 2000
                },
                new FeatureQuantity
                {
                    FeatureId = FeatureId.FacebookLeadAds,
                    Quantity = 1
                },
                new FeatureQuantity
                {
                    FeatureId = FeatureId.MakeIntegration,
                    Quantity = 1
                },
                new FeatureQuantity
                {
                    FeatureId = FeatureId.ZapierIntegration,
                    Quantity = 1
                },
                new(FeatureId.FlowBuilderMaxActiveWorkflowCount, 3),
                new(FeatureId.FlowBuilderMaxNodesPerWorkflowCount, 25),
                new(FeatureId.FlowBuilderFlowEnrolment, 500, 3000),
            },
            new List<Price>
            {
                new Price("HKD", 780),
                new Price("SGD", 139),
            },
            PlanTypes.Subscription,
            "pro",
            V7,
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "pro_yearly", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "contacts",
                    Quantity = 2000
                },
                new FeatureQuantity
                {
                    FeatureId = "messages",
                    Quantity = 60000
                },
                new FeatureQuantity
                {
                    FeatureId = "agents",
                    Quantity = 3
                },
                new FeatureQuantity
                {
                    FeatureId = "campaigns",
                    Quantity = 60000
                },
                new FeatureQuantity
                {
                    FeatureId = "automations",
                    Quantity = 999
                },
                new FeatureQuantity
                {
                    FeatureId = "channels",
                    Quantity = 999
                },
                new FeatureQuantity
                {
                    FeatureId = "api_calls",
                    Quantity = 60000
                },
                new FeatureQuantity
                {
                    FeatureId = "ai_features_total_usage",
                    Quantity = 2000
                },
                new FeatureQuantity
                {
                    FeatureId = FeatureId.FacebookLeadAds,
                    Quantity = 1
                },
                new FeatureQuantity
                {
                    FeatureId = FeatureId.MakeIntegration,
                    Quantity = 1
                },
                new FeatureQuantity
                {
                    FeatureId = FeatureId.ZapierIntegration,
                    Quantity = 1
                },
                new(FeatureId.FlowBuilderMaxActiveWorkflowCount, 3),
                new(FeatureId.FlowBuilderMaxNodesPerWorkflowCount, 25),
                new(FeatureId.FlowBuilderFlowEnrolment, 500, 3000),
            },
            new List<Price>
            {
                new Price("USD", 948),
            },
            PlanTypes.Subscription,
            "pro",
            V7,
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "premium_monthly", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "contacts",
                    Quantity = 20000
                },
                new FeatureQuantity
                {
                    FeatureId = "messages",
                    Quantity = 5000000
                },
                new FeatureQuantity
                {
                    FeatureId = "agents",
                    Quantity = 5
                },
                new FeatureQuantity
                {
                    FeatureId = "campaigns",
                    Quantity = 50000000
                },
                new FeatureQuantity
                {
                    FeatureId = "automations",
                    Quantity = 999
                },
                new FeatureQuantity
                {
                    FeatureId = "channels",
                    Quantity = 999
                },
                new FeatureQuantity
                {
                    FeatureId = "api_calls",
                    Quantity = 100000
                },
                new FeatureQuantity
                {
                    FeatureId = "ai_features_total_usage",
                    Quantity = 2000
                },
                new FeatureQuantity
                {
                    FeatureId = FeatureId.FacebookLeadAds,
                    Quantity = 1
                },
                new FeatureQuantity
                {
                    FeatureId = FeatureId.MakeIntegration,
                    Quantity = 1
                },
                new FeatureQuantity
                {
                    FeatureId = FeatureId.ZapierIntegration,
                    Quantity = 1
                },
                new(FeatureId.FlowBuilderMaxActiveWorkflowCount, 25),
                new(FeatureId.FlowBuilderMaxNodesPerWorkflowCount, 100),
                new(FeatureId.FlowBuilderFlowEnrolment, 3000, 10000),
            },
            new List<Price>
            {
                new Price("USD", 349),
            },
            PlanTypes.Subscription,
            "premium",
            V6,
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "premium_yearly", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "contacts",
                    Quantity = 20000
                },
                new FeatureQuantity
                {
                    FeatureId = "messages",
                    Quantity = 5000000
                },
                new FeatureQuantity
                {
                    FeatureId = "agents",
                    Quantity = 5
                },
                new FeatureQuantity
                {
                    FeatureId = "campaigns",
                    Quantity = 50000000
                },
                new FeatureQuantity
                {
                    FeatureId = "automations",
                    Quantity = 999
                },
                new FeatureQuantity
                {
                    FeatureId = "channels",
                    Quantity = 999
                },
                new FeatureQuantity
                {
                    FeatureId = "api_calls",
                    Quantity = 1200000
                },
                new FeatureQuantity
                {
                    FeatureId = "ai_features_total_usage",
                    Quantity = 2000
                },
                new FeatureQuantity
                {
                    FeatureId = FeatureId.FacebookLeadAds,
                    Quantity = 1
                },
                new FeatureQuantity
                {
                    FeatureId = FeatureId.MakeIntegration,
                    Quantity = 1
                },
                new FeatureQuantity
                {
                    FeatureId = FeatureId.ZapierIntegration,
                    Quantity = 1
                },
                new(FeatureId.FlowBuilderMaxActiveWorkflowCount, 25),
                new(FeatureId.FlowBuilderMaxNodesPerWorkflowCount, 100),
                new(FeatureId.FlowBuilderFlowEnrolment, 3000, 10000),
            },
            new List<Price>
            {
                new Price("USD", 3588),
            },
            PlanTypes.Subscription,
            "premium",
            V6,
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "pro_monthly", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "contacts",
                    Quantity = 5000
                },
                new FeatureQuantity
                {
                    FeatureId = "messages",
                    Quantity = 5000
                },
                new FeatureQuantity
                {
                    FeatureId = "agents",
                    Quantity = 3
                },
                new FeatureQuantity
                {
                    FeatureId = "campaigns",
                    Quantity = 5000
                },
                new FeatureQuantity
                {
                    FeatureId = "automations",
                    Quantity = 5
                },
                new FeatureQuantity
                {
                    FeatureId = "channels",
                    Quantity = 999
                },
                new FeatureQuantity
                {
                    FeatureId = "api_calls",
                    Quantity = 5000
                },
                new FeatureQuantity
                {
                    FeatureId = "ai_features_total_usage",
                    Quantity = 2000
                },
                new FeatureQuantity
                {
                    FeatureId = FeatureId.FacebookLeadAds,
                    Quantity = 1
                },
                new FeatureQuantity
                {
                    FeatureId = FeatureId.MakeIntegration,
                    Quantity = 1
                },
                new FeatureQuantity
                {
                    FeatureId = FeatureId.ZapierIntegration,
                    Quantity = 1
                },
                new(FeatureId.FlowBuilderMaxActiveWorkflowCount, 3),
                new(FeatureId.FlowBuilderMaxNodesPerWorkflowCount, 25),
                new(FeatureId.FlowBuilderFlowEnrolment, 500, 3000),
            },
            new List<Price>
            {
                new Price("USD", 99),
            },
            PlanTypes.Subscription,
            "pro",
            V6,
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "pro_yearly", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "contacts",
                    Quantity = 5000
                },
                new FeatureQuantity
                {
                    FeatureId = "messages",
                    Quantity = 5000
                },
                new FeatureQuantity
                {
                    FeatureId = "agents",
                    Quantity = 3
                },
                new FeatureQuantity
                {
                    FeatureId = "campaigns",
                    Quantity = 60000
                },
                new FeatureQuantity
                {
                    FeatureId = "automations",
                    Quantity = 5
                },
                new FeatureQuantity
                {
                    FeatureId = "channels",
                    Quantity = 999
                },
                new FeatureQuantity
                {
                    FeatureId = "api_calls",
                    Quantity = 60000
                },
                new FeatureQuantity
                {
                    FeatureId = "ai_features_total_usage",
                    Quantity = 2000
                },
                new FeatureQuantity
                {
                    FeatureId = FeatureId.FacebookLeadAds,
                    Quantity = 1
                },
                new FeatureQuantity
                {
                    FeatureId = FeatureId.MakeIntegration,
                    Quantity = 1
                },
                new FeatureQuantity
                {
                    FeatureId = FeatureId.ZapierIntegration,
                    Quantity = 1
                },
                new(FeatureId.FlowBuilderMaxActiveWorkflowCount, 3),
                new(FeatureId.FlowBuilderMaxNodesPerWorkflowCount, 25),
                new(FeatureId.FlowBuilderFlowEnrolment, 500, 3000),
            },
            new List<Price>
            {
                new Price("USD", 948),
            },
            PlanTypes.Subscription,
            "pro",
            V6,
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "pro_yearly", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "contacts",
                    Quantity = 5000
                },
                new FeatureQuantity
                {
                    FeatureId = "messages",
                    Quantity = 60000
                },
                new FeatureQuantity
                {
                    FeatureId = "agents",
                    Quantity = 3
                },
                new FeatureQuantity
                {
                    FeatureId = "campaigns",
                    Quantity = 60000
                },
                new FeatureQuantity
                {
                    FeatureId = "automations",
                    Quantity = 5
                },
                new FeatureQuantity
                {
                    FeatureId = "channels",
                    Quantity = 999
                },
                new FeatureQuantity
                {
                    FeatureId = "api_calls",
                    Quantity = 60000
                },
                new FeatureQuantity
                {
                    FeatureId = "ai_features_total_usage",
                    Quantity = 2000
                },
                new FeatureQuantity
                {
                    FeatureId = FeatureId.FacebookLeadAds,
                    Quantity = 1
                },
                new FeatureQuantity
                {
                    FeatureId = FeatureId.MakeIntegration,
                    Quantity = 1
                },
                new FeatureQuantity
                {
                    FeatureId = FeatureId.ZapierIntegration,
                    Quantity = 1
                },
                new(FeatureId.FlowBuilderMaxActiveWorkflowCount, 3),
                new(FeatureId.FlowBuilderMaxNodesPerWorkflowCount, 25),
                new(FeatureId.FlowBuilderFlowEnrolment, 500, 3000),
            },
            new List<Price>
            {
                new Price("USD", 948),
                new Price("HKD", 7350),
            },
            PlanTypes.Subscription,
            "pro",
            V4,
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "pro_yearly", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "contacts",
                    Quantity = 5000
                },
                new FeatureQuantity
                {
                    FeatureId = "messages",
                    Quantity = 60000
                },
                new FeatureQuantity
                {
                    FeatureId = "agents",
                    Quantity = 3
                },
                new FeatureQuantity
                {
                    FeatureId = "campaigns",
                    Quantity = 60000
                },
                new FeatureQuantity
                {
                    FeatureId = "automations",
                    Quantity = 5
                },
                new FeatureQuantity
                {
                    FeatureId = "channels",
                    Quantity = 999
                },
                new FeatureQuantity
                {
                    FeatureId = "api_calls",
                    Quantity = 60000
                },
                new FeatureQuantity
                {
                    FeatureId = "ai_features_total_usage",
                    Quantity = 2000
                },
                new FeatureQuantity
                {
                    FeatureId = FeatureId.FacebookLeadAds,
                    Quantity = 1
                },
                new FeatureQuantity
                {
                    FeatureId = FeatureId.MakeIntegration,
                    Quantity = 1
                },
                new FeatureQuantity
                {
                    FeatureId = FeatureId.ZapierIntegration,
                    Quantity = 1
                },
                new(FeatureId.FlowBuilderMaxActiveWorkflowCount, 3),
                new(FeatureId.FlowBuilderMaxNodesPerWorkflowCount, 25),
                new(FeatureId.FlowBuilderFlowEnrolment, 500, 3000),
            },
            new List<Price>
            {
                new Price("USD", 948),
            },
            PlanTypes.Subscription,
            "pro",
            V5,
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "premium_monthly", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "contacts",
                    Quantity = 500000000
                },
                new FeatureQuantity
                {
                    FeatureId = "messages",
                    Quantity = 1000000
                },
                new FeatureQuantity
                {
                    FeatureId = "agents",
                    Quantity = 5
                },
                new FeatureQuantity
                {
                    FeatureId = "campaigns",
                    Quantity = 50000000
                },
                new FeatureQuantity
                {
                    FeatureId = "automations",
                    Quantity = 999
                },
                new FeatureQuantity
                {
                    FeatureId = "channels",
                    Quantity = 999
                },
                new FeatureQuantity
                {
                    FeatureId = "api_calls",
                    Quantity = 100000
                },
                new FeatureQuantity
                {
                    FeatureId = "ai_features_total_usage",
                    Quantity = 2000
                },
                new FeatureQuantity
                {
                    FeatureId = FeatureId.FacebookLeadAds,
                    Quantity = 1
                },
                new FeatureQuantity
                {
                    FeatureId = FeatureId.MakeIntegration,
                    Quantity = 1
                },
                new FeatureQuantity
                {
                    FeatureId = FeatureId.ZapierIntegration,
                    Quantity = 1
                },
                new(FeatureId.FlowBuilderMaxActiveWorkflowCount, 25),
                new(FeatureId.FlowBuilderMaxNodesPerWorkflowCount, 100),
                new(FeatureId.FlowBuilderFlowEnrolment, 3000, 10000),
            },
            new List<Price>
            {
                new Price("USD", 349),
            },
            PlanTypes.Subscription,
            "premium",
            V4,
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "premium_yearly", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "contacts",
                    Quantity = 500000000
                },
                new FeatureQuantity
                {
                    FeatureId = "messages",
                    Quantity = 1000000
                },
                new FeatureQuantity
                {
                    FeatureId = "agents",
                    Quantity = 5
                },
                new FeatureQuantity
                {
                    FeatureId = "campaigns",
                    Quantity = 50000000
                },
                new FeatureQuantity
                {
                    FeatureId = "automations",
                    Quantity = 999
                },
                new FeatureQuantity
                {
                    FeatureId = "channels",
                    Quantity = 999
                },
                new FeatureQuantity
                {
                    FeatureId = "api_calls",
                    Quantity = 1200000
                },
                new FeatureQuantity
                {
                    FeatureId = "ai_features_total_usage",
                    Quantity = 2000
                },
                new FeatureQuantity
                {
                    FeatureId = FeatureId.FacebookLeadAds,
                    Quantity = 1
                },
                new FeatureQuantity
                {
                    FeatureId = FeatureId.MakeIntegration,
                    Quantity = 1
                },
                new FeatureQuantity
                {
                    FeatureId = FeatureId.ZapierIntegration,
                    Quantity = 1
                },
                new(FeatureId.FlowBuilderMaxActiveWorkflowCount, 25),
                new(FeatureId.FlowBuilderMaxNodesPerWorkflowCount, 100),
                new(FeatureId.FlowBuilderFlowEnrolment, 3000, 10000),
            },
            new List<Price>
            {
                new Price("USD", 3588),
            },
            PlanTypes.Subscription,
            "premium",
            V4,
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "premium", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "contacts",
                    Quantity = 500000000
                },
                new FeatureQuantity
                {
                    FeatureId = "messages",
                    Quantity = 20000
                },
                new FeatureQuantity
                {
                    FeatureId = "agents",
                    Quantity = 5
                },
                new FeatureQuantity
                {
                    FeatureId = "campaigns",
                    Quantity = 20000
                },
                new FeatureQuantity
                {
                    FeatureId = "automations",
                    Quantity = 999
                },
                new FeatureQuantity
                {
                    FeatureId = "channels",
                    Quantity = 999
                },
                new FeatureQuantity
                {
                    FeatureId = "api_calls",
                    Quantity = 100000
                },
                new FeatureQuantity
                {
                    FeatureId = "ai_features_total_usage",
                    Quantity = 2000
                },
                new FeatureQuantity
                {
                    FeatureId = FeatureId.FacebookLeadAds,
                    Quantity = 1
                },
                new FeatureQuantity
                {
                    FeatureId = FeatureId.MakeIntegration,
                    Quantity = 1
                },
                new FeatureQuantity
                {
                    FeatureId = FeatureId.ZapierIntegration,
                    Quantity = 1
                },
                new(FeatureId.FlowBuilderMaxActiveWorkflowCount, 25),
                new(FeatureId.FlowBuilderMaxNodesPerWorkflowCount, 100),
                new(FeatureId.FlowBuilderFlowEnrolment, 3000, 10000),
            },
            new List<Price>
            {
                new Price("USD", 299),
            },
            PlanTypes.Subscription,
            "premium",
            V3,
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "pro", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "contacts",
                    Quantity = 5000
                },
                new FeatureQuantity
                {
                    FeatureId = "messages",
                    Quantity = 5000
                },
                new FeatureQuantity
                {
                    FeatureId = "agents",
                    Quantity = 3
                },
                new FeatureQuantity
                {
                    FeatureId = "campaigns",
                    Quantity = 5000
                },
                new FeatureQuantity
                {
                    FeatureId = "automations",
                    Quantity = 5
                },
                new FeatureQuantity
                {
                    FeatureId = "channels",
                    Quantity = 999
                },
                new FeatureQuantity
                {
                    FeatureId = "api_calls",
                    Quantity = 5000
                },
                new FeatureQuantity
                {
                    FeatureId = "ai_features_total_usage",
                    Quantity = 2000
                },
                new FeatureQuantity
                {
                    FeatureId = FeatureId.FacebookLeadAds,
                    Quantity = 1
                },
                new FeatureQuantity
                {
                    FeatureId = FeatureId.MakeIntegration,
                    Quantity = 1
                },
                new FeatureQuantity
                {
                    FeatureId = FeatureId.ZapierIntegration,
                    Quantity = 1
                },
                new(FeatureId.FlowBuilderMaxActiveWorkflowCount, 3),
                new(FeatureId.FlowBuilderMaxNodesPerWorkflowCount, 25),
                new(FeatureId.FlowBuilderFlowEnrolment, 500, 3000),
            },
            new List<Price>
            {
                new Price("USD", 99),
            },
            PlanTypes.Subscription,
            "pro",
            V3,
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "standard", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "contacts",
                    Quantity = 500
                },
                new FeatureQuantity
                {
                    FeatureId = "messages",
                    Quantity = 100
                },
                new FeatureQuantity
                {
                    FeatureId = "agents",
                    Quantity = 1
                },
                new FeatureQuantity
                {
                    FeatureId = "campaigns",
                    Quantity = 100
                },
                new FeatureQuantity
                {
                    FeatureId = "automations",
                    Quantity = 1
                },
                new FeatureQuantity
                {
                    FeatureId = "channels",
                    Quantity = 999
                },
                new FeatureQuantity
                {
                    FeatureId = "ai_features_total_usage",
                    Quantity = 2000
                },
                new FeatureQuantity
                {
                    FeatureId = FeatureId.FacebookLeadAds,
                    Quantity = 1
                },
                new FeatureQuantity
                {
                    FeatureId = FeatureId.MakeIntegration,
                    Quantity = 1
                },
                new FeatureQuantity
                {
                    FeatureId = FeatureId.ZapierIntegration,
                    Quantity = 1
                },
            },
            new List<Price>
            {
                new Price("USD", 49),
            },
            PlanTypes.Subscription,
            "pro",
            V3,
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "agent_premium", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "agents",
                    Quantity = 1
                },
            },
            new List<Price>
            {
                new Price("USD", 39),
            },
            PlanTypes.AddOns,
            null,
            V2,
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "agent_premium", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "agents",
                    Quantity = 5
                },
            },
            new List<Price>
            {
                new Price("HKD", 300),
            },
            PlanTypes.AddOns,
            null,
            V2,
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "agent_premium_39", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "agents",
                    Quantity = 1
                },
            },
            new List<Price>
            {
                new Price("USD", 39),
            },
            PlanTypes.AddOns,
            null,
            V2,
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "agent_premium_39_yearly", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "agents",
                    Quantity = 1
                },
            },
            new List<Price>
            {
                new Price("USD", 468),
            },
            PlanTypes.AddOns,
            null,
            V2,
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "agent_pro", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "agents",
                    Quantity = 1
                },
            },
            new List<Price>
            {
                new Price("USD", 19),
            },
            PlanTypes.AddOns,
            null,
            V2,
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "agent_pro_yearly", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "agents",
                    Quantity = 1
                },
            },
            new List<Price>
            {
                new Price("USD", 228),
            },
            PlanTypes.AddOns,
            null,
            V2,
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "agent_standard", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "agents",
                    Quantity = 1
                },
            },
            new List<Price>
            {
                new Price("USD", 9),
            },
            PlanTypes.AddOns,
            null,
            V2,
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "agent_standard_yearly", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "agents",
                    Quantity = 1
                },
            },
            new List<Price>
            {
                new Price("USD", 228),
            },
            PlanTypes.AddOns,
            null,
            V2,
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "premium", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "contacts",
                    Quantity = 500000000
                },
                new FeatureQuantity
                {
                    FeatureId = "messages",
                    Quantity = 1000000
                },
                new FeatureQuantity
                {
                    FeatureId = "agents",
                    Quantity = 5
                },
                new FeatureQuantity
                {
                    FeatureId = "campaigns",
                    Quantity = 1000000
                },
                new FeatureQuantity
                {
                    FeatureId = "automations",
                    Quantity = 999
                },
                new FeatureQuantity
                {
                    FeatureId = "channels",
                    Quantity = 999
                },
                new FeatureQuantity
                {
                    FeatureId = "api_calls",
                    Quantity = 100000
                },
                new FeatureQuantity
                {
                    FeatureId = "ai_features_total_usage",
                    Quantity = 2000
                },
                new FeatureQuantity
                {
                    FeatureId = FeatureId.FacebookLeadAds,
                    Quantity = 1
                },
                new FeatureQuantity
                {
                    FeatureId = FeatureId.MakeIntegration,
                    Quantity = 1
                },
                new FeatureQuantity
                {
                    FeatureId = FeatureId.ZapierIntegration,
                    Quantity = 1
                },
                new(FeatureId.FlowBuilderMaxActiveWorkflowCount, 25),
                new(FeatureId.FlowBuilderMaxNodesPerWorkflowCount, 100),
                new(FeatureId.FlowBuilderFlowEnrolment, 3000, 10000),
            },
            new List<Price>
            {
                new Price("USD", 299),
            },
            PlanTypes.Subscription,
            "premium",
            V2,
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "pro", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "contacts",
                    Quantity = 5000
                },
                new FeatureQuantity
                {
                    FeatureId = "messages",
                    Quantity = 5000
                },
                new FeatureQuantity
                {
                    FeatureId = "agents",
                    Quantity = 3
                },
                new FeatureQuantity
                {
                    FeatureId = "campaigns",
                    Quantity = 5000
                },
                new FeatureQuantity
                {
                    FeatureId = "automations",
                    Quantity = 999
                },
                new FeatureQuantity
                {
                    FeatureId = "channels",
                    Quantity = 999
                },
                new FeatureQuantity
                {
                    FeatureId = "api_calls",
                    Quantity = 5000
                },
                new FeatureQuantity
                {
                    FeatureId = "ai_features_total_usage",
                    Quantity = 2000
                },
                new FeatureQuantity
                {
                    FeatureId = FeatureId.FacebookLeadAds,
                    Quantity = 1
                },
                new FeatureQuantity
                {
                    FeatureId = FeatureId.MakeIntegration,
                    Quantity = 1
                },
                new FeatureQuantity
                {
                    FeatureId = FeatureId.ZapierIntegration,
                    Quantity = 1
                },
                new(FeatureId.FlowBuilderMaxActiveWorkflowCount, 3),
                new(FeatureId.FlowBuilderMaxNodesPerWorkflowCount, 25),
                new(FeatureId.FlowBuilderFlowEnrolment, 500, 3000),
            },
            new List<Price>
            {
                new Price("USD", 99),
            },
            PlanTypes.Subscription,
            "pro",
            V2,
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "standard", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "contacts",
                    Quantity = 500
                },
                new FeatureQuantity
                {
                    FeatureId = "messages",
                    Quantity = 100
                },
                new FeatureQuantity
                {
                    FeatureId = "agents",
                    Quantity = 1
                },
                new FeatureQuantity
                {
                    FeatureId = "campaigns",
                    Quantity = 100
                },
                new FeatureQuantity
                {
                    FeatureId = "automations",
                    Quantity = 1
                },
                new FeatureQuantity
                {
                    FeatureId = "channels",
                    Quantity = 999
                },
                new FeatureQuantity
                {
                    FeatureId = "ai_features_total_usage",
                    Quantity = 2000
                },
                new FeatureQuantity
                {
                    FeatureId = FeatureId.FacebookLeadAds,
                    Quantity = 1
                },
                new FeatureQuantity
                {
                    FeatureId = FeatureId.MakeIntegration,
                    Quantity = 1
                },
                new FeatureQuantity
                {
                    FeatureId = FeatureId.ZapierIntegration,
                    Quantity = 1
                },
            },
            new List<Price>
            {
                new Price("USD", 39),
            },
            PlanTypes.Subscription,
            "pro",
            V2,
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "pro", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "messages",
                    Quantity = 5000
                },
                new FeatureQuantity
                {
                    FeatureId = "agents",
                    Quantity = 1
                },
                new FeatureQuantity
                {
                    FeatureId = "automations",
                    Quantity = 1
                },
                new FeatureQuantity
                {
                    FeatureId = "channels",
                    Quantity = 3
                },
                new FeatureQuantity
                {
                    FeatureId = "api_calls",
                    Quantity = 5000
                },
                new FeatureQuantity
                {
                    FeatureId = "ai_features_total_usage",
                    Quantity = 2000
                },
                new FeatureQuantity
                {
                    FeatureId = FeatureId.FacebookLeadAds,
                    Quantity = 1
                },
                new FeatureQuantity
                {
                    FeatureId = FeatureId.MakeIntegration,
                    Quantity = 1
                },
                new FeatureQuantity
                {
                    FeatureId = FeatureId.ZapierIntegration,
                    Quantity = 1
                },
                new(FeatureId.FlowBuilderMaxActiveWorkflowCount, 3),
                new(FeatureId.FlowBuilderMaxNodesPerWorkflowCount, 25),
                new(FeatureId.FlowBuilderFlowEnrolment, 500, 3000),
            },
            new List<Price>
            {
                new Price("USD", 39),
            },
            PlanTypes.Subscription,
            "pro",
            V0,
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "demo", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "contacts",
                    Quantity = 50000000
                },
                new FeatureQuantity
                {
                    FeatureId = "messages",
                    Quantity = 100
                },
                new FeatureQuantity
                {
                    FeatureId = "agents",
                    Quantity = 12
                },
                new FeatureQuantity
                {
                    FeatureId = "campaigns",
                    Quantity = 100
                },
                new FeatureQuantity
                {
                    FeatureId = "automations",
                    Quantity = 999
                },
                new FeatureQuantity
                {
                    FeatureId = "channels",
                    Quantity = 999
                },
                new FeatureQuantity
                {
                    FeatureId = "api_calls",
                    Quantity = 999
                },
                new FeatureQuantity
                {
                    FeatureId = FeatureId.MakeIntegration,
                    Quantity = 1
                },
                new FeatureQuantity
                {
                    FeatureId = FeatureId.ZapierIntegration,
                    Quantity = 1
                },
                new(FeatureId.FlowBuilderMaxActiveWorkflowCount, 3),
                new(FeatureId.FlowBuilderMaxNodesPerWorkflowCount, 25),
                new(FeatureId.FlowBuilderFlowEnrolment, 50, 50),
                new(FeatureId.AI, 2000),
            },
            new List<Price>
            {
                new Price("USD", 0),
            },
            PlanTypes.Subscription,
            "free",
            V0,
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "enterprise", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "contacts",
                    Quantity = 100000
                },
                new FeatureQuantity
                {
                    FeatureId = "messages",
                    Quantity = 50000000
                },
                new FeatureQuantity
                {
                    FeatureId = "agents",
                    Quantity = 5
                },
                new FeatureQuantity
                {
                    FeatureId = "campaigns",
                    Quantity = 50000000
                },
                new FeatureQuantity
                {
                    FeatureId = "automations",
                    Quantity = 999
                },
                new FeatureQuantity
                {
                    FeatureId = "channels",
                    Quantity = 999
                },
                new FeatureQuantity
                {
                    FeatureId = "api_calls",
                    Quantity = 5000000
                },
                new FeatureQuantity
                {
                    FeatureId = "onboarding_support",
                    Quantity = 1
                },
                new FeatureQuantity
                {
                    FeatureId = "chatbot_automation_support_basic",
                    Quantity = 1
                },
                new FeatureQuantity
                {
                    FeatureId = "priority_support",
                    Quantity = 1
                },
                new FeatureQuantity
                {
                    FeatureId = "ai_features_total_usage",
                    Quantity = 2000
                },
                new FeatureQuantity
                {
                    FeatureId = FeatureId.FacebookLeadAds,
                    Quantity = 1
                },
                new FeatureQuantity
                {
                    FeatureId = FeatureId.MakeIntegration,
                    Quantity = 1
                },
                new FeatureQuantity
                {
                    FeatureId = FeatureId.ZapierIntegration,
                    Quantity = 1
                },
                new(FeatureId.FlowBuilderMaxActiveWorkflowCount, 50),
                new(FeatureId.FlowBuilderMaxNodesPerWorkflowCount, 200),
                new(FeatureId.FlowBuilderFlowEnrolment, 10000),
            },
            new List<Price>
            {
                new Price("USD", 999),
            },
            PlanTypes.Subscription,
            "enterprise",
            V0,
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "free", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "contacts",
                    Quantity = 100
                },
                new FeatureQuantity
                {
                    FeatureId = "messages",
                    Quantity = 200
                },
                new FeatureQuantity
                {
                    FeatureId = "agents",
                    Quantity = 1
                },
                new FeatureQuantity
                {
                    FeatureId = "channels",
                    Quantity = 1
                },
            },
            new List<Price>
            {
                new Price("USD", 0),
            },
            PlanTypes.Subscription,
            "free",
            V0,
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "freemium", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "contacts",
                    Quantity = 100
                },
                new FeatureQuantity
                {
                    FeatureId = "messages",
                    Quantity = 100
                },
                new FeatureQuantity
                {
                    FeatureId = "agents",
                    Quantity = 3
                },
                new FeatureQuantity
                {
                    FeatureId = "campaigns",
                    Quantity = 100
                },
                new FeatureQuantity
                {
                    FeatureId = "automations",
                    Quantity = 5
                },
                new FeatureQuantity
                {
                    FeatureId = "channels",
                    Quantity = 999
                },
                new(FeatureId.FlowBuilderMaxActiveWorkflowCount, 3),
                new(FeatureId.FlowBuilderMaxNodesPerWorkflowCount, 25),
                new(FeatureId.FlowBuilderFlowEnrolment, 50, 50),
                new(FeatureId.AI, 2000),
            },
            new List<Price>
            {
                new Price("USD", 0),
            },
            PlanTypes.Subscription,
            "free",
            V0,
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "price_1GshWTF8rFCN7uzNNRefZtTU", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "contacts",
                    Quantity = 20000
                },
                new FeatureQuantity
                {
                    FeatureId = "messages",
                    Quantity = 5000000
                },
                new FeatureQuantity
                {
                    FeatureId = "api_calls",
                    Quantity = 100000000
                },
            },
            new List<Price>
            {
                new Price("USD", 0),
            },
            PlanTypes.Subscription,
            "pro",
            V0,
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "price_1M7aEhF8rFCN7uzNXj8rBusz", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "whatsapp_phone_number",
                    Quantity = 1
                }
            },
            new List<Price>
            {
                new Price("IDR", 235000),
            },
            PlanTypes.AddOns,
            null,
            V9,
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "price_1M7aEhF8rFCN7uzN9Zjg3XUM", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "whatsapp_phone_number",
                    Quantity = 1
                }
            },
            new List<Price>
            {
                new Price("USD", 15),
            },
            PlanTypes.AddOns,
            null,
            V9,
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "price_1M7aEhF8rFCN7uzNMKG3HmNB", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "whatsapp_phone_number",
                    Quantity = 1
                }
            },
            new List<Price>
            {
                new Price("HKD", 119),
            },
            PlanTypes.AddOns,
            null,
            V9,
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "price_1NJ7AbF8rFCN7uzN521Aq2wX", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "whatsapp_phone_number",
                    Quantity = 1
                }
            },
            new List<Price>
            {
                new Price("MYR", 69),
            },
            PlanTypes.AddOns,
            null,
            V9,
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new PlanDefinition(
            new List<Multilingual>
            {
                new Multilingual("en", "price_1NGcAjF8rFCN7uzN4Z5GJpWy", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new FeatureQuantity
                {
                    FeatureId = "hubspot",
                    Quantity = 1
                }
            },
            new List<Price>
            {
                new Price("SGD", 150),
            },
            PlanTypes.AddOns,
            null,
            V0,
            new List<string>
            {
                "Active"
            },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
    };

    #region Onboarding Support Plans Definitions

    private readonly IEnumerable<PlanDefinition> _onboardingSupportPlanDefinitions = new List<PlanDefinition>
    {
        #region V10 Tier1 Onboarding Support Oneoff Plans
        new(
            new List<Multilingual>
            {
                new("en", "onboarding_support_pro_oneoff", true),
                new("en", "onboarding_support_premium_oneoff", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new("onboarding_support", 1)
            },
            new List<Price>
            {
                new("USD", 499),
                new("HKD", 3999),
                new("CNY", 3629),
                new("EUR", 499),
                new("GBP", 419),
                new("CAD", 649),
                new("AUD", 739),
                new("AED", 1799),
            },
            PlanTypes.AddOns,
            null,
            V10,
            new List<string> { "Active" },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            "Tier1",
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow
        ),
        #endregion
        #region V10 Tier2 Onboarding Support Oneoff Plans
        new(
            new List<Multilingual>
            {
                new("en", "onboarding_support_pro_oneoff", true),
                new("en", "onboarding_support_premium_oneoff", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new("onboarding_support", 1)
            },
            new List<Price>
            {
                new("USD", 499),
                new("SGD", 699),
                new("EUR", 499),
            },
            PlanTypes.AddOns,
            null,
            V10,
            new List<string> { "Active" },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            "Tier2",
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow
        ),
        #endregion
        #region V10 Tier3 Onboarding Support Oneoff Plans
        new(
            new List<Multilingual>
            {
                new("en", "onboarding_support_pro_oneoff", true),
                new("en", "onboarding_support_premium_oneoff", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new("onboarding_support", 1)
            },
            new List<Price>
            {
                new("USD", 499),
                new("BRL", 2699),
            },
            PlanTypes.AddOns,
            null,
            V10,
            new List<string> { "Active" },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            "Tier3",
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow
        ),
        #endregion
        #region V10 Tier4 Onboarding Support Oneoff Plans
        new(
            new List<Multilingual>
            {
                new("en", "onboarding_support_pro_oneoff", true),
                new("en", "onboarding_support_premium_oneoff", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new("onboarding_support", 1)
            },
            new List<Price>
            {
                new("USD", 499),
                new("MYR", 2299),
            },
            PlanTypes.AddOns,
            null,
            V10,
            new List<string> { "Active" },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            "Tier4",
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow
        ),
        #endregion
        #region V10 Tier5 Onboarding Support Oneoff Plans
        new(
            new List<Multilingual>
            {
                new("en", "onboarding_support_pro_oneoff", true),
                new("en", "onboarding_support_premium_oneoff", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new("onboarding_support", 1)
            },
            new List<Price>
            {
                new("USD", 499),
                new("IDR", 7990000),
                new("INR", 41699),
            },
            PlanTypes.AddOns,
            null,
            V10,
            new List<string> { "Active" },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            "Tier5",
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow
        ),
        #endregion
    };

    #endregion

    #region Business Consultancy Service Definitions

    public readonly IEnumerable<PlanDefinition> _businessConsultancyServicePlanDefinitions = new List<PlanDefinition>
    {
        #region V10 Tier1 Onboarding Support Yearly Plans
        new(
            new List<Multilingual>
            {
                new("en", "business_consultancy_service_pro_yearly", true),
                new("en", "business_consultancy_service_premium_yearly", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new(FeatureId.BusinessConsultancyService, 1)
            },
            new List<Price>
            {
                new("USD", 5988),
                new("HKD", 46908),
                new("CNY", 43908),
                new("EUR", 5628),
                new("GBP", 4908),
                new("CAD", 8268),
                new("AUD", 9348),
                new("AED", 22068),
            },
            PlanTypes.AddOns,
            null,
            V10,
            new List<string> { "Active" },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            "Tier1",
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow
        ),
        #endregion
        #region V10 Tier2 Onboarding Support Yearly Plans
        new(
            new List<Multilingual>
            {
                new("en", "business_consultancy_service_pro_yearly", true),
                new("en", "business_consultancy_service_premium_yearly", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new(FeatureId.BusinessConsultancyService, 1)
            },
            new List<Price>
            {

                new("USD", 5988),
                new("SGD", 8268),
                new("EUR", 5628),
            },
            PlanTypes.AddOns,
            null,
            V10,
            new List<string> { "Active" },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            "Tier2",
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow
        ),
        #endregion
        #region V10 Tier3 Onboarding Support Yearly Plans
        new(
            new List<Multilingual>
            {
                new("en", "business_consultancy_service_pro_yearly", true),
                new("en", "business_consultancy_service_premium_yearly", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new(FeatureId.BusinessConsultancyService, 1)
            },
            new List<Price>
            {

                new("USD", 5988),
                new("BRL", 29748),
            },
            PlanTypes.AddOns,
            null,
            V10,
            new List<string> { "Active" },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            "Tier3",
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow
        ),
        #endregion
        #region V10 Tier4 Onboarding Support Yearly Plans
        new(
            new List<Multilingual>
            {
                new("en", "business_consultancy_service_pro_yearly", true),
                new("en", "business_consultancy_service_premium_yearly", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new(FeatureId.BusinessConsultancyService, 1)
            },
            new List<Price>
            {

                new("USD", 5988),
                new("MYR", 28548),
            },
            PlanTypes.AddOns,
            null,
            V10,
            new List<string> { "Active" },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            "Tier4",
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow
        ),
        #endregion,
        #region V10 Tier4 Onboarding Support Yearly Plans
        new(
            new List<Multilingual>
            {
                new("en", "business_consultancy_service_pro_yearly", true),
                new("en", "business_consultancy_service_premium_yearly", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new(FeatureId.BusinessConsultancyService, 1)
            },
            new List<Price>
            {

                new("USD", 5988),
                new("IDR", 95880000),
                new("INR", 500388),
            },
            PlanTypes.AddOns,
            null,
            V10,
            new List<string> { "Active" },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            "Tier5",
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow
        ),
        #endregion
    };

    #endregion

    public PlanDefinitionRepository()
    {
        _allPlanDefinitions.AddRange(BaseSubscriptionPlanDefinitions.BasePlanDefinitions);
        _allPlanDefinitions.AddRange(AdditionalAgentAddOnPlanDefinitions.AgentPlanDefinitions);
        _allPlanDefinitions.AddRange(AdditionalContactsAddOnPlanDefinitions.AdditionalContactPlanDefinitions);
        _allPlanDefinitions.AddRange(WhatsAppPhoneNumberAddOnPlanDefinitions.WhatsAppPhoneNumberDefinitions);
        _allPlanDefinitions.AddRange(_onboardingSupportPlanDefinitions);
        _allPlanDefinitions.AddRange(_businessConsultancyServicePlanDefinitions);
        _allPlanDefinitions.AddRange(FlowBuilderFlowEnrolmentsPlanDefinitions.FlowEnrolmentsDefinitions);
    }

    public List<PlanDefinition> GetAll()
    {
        return _allPlanDefinitions;
    }
}