﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Hangfire;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Polly;
using Travis_backend.BackgroundTaskServices.Attributes;
using Travis_backend.Cache;
using Travis_backend.ChannelDomain.Models;
using Travis_backend.Constants;
using Travis_backend.ConversationDomain.ConversationResolver;
using Travis_backend.ConversationDomain.Models;
using Travis_backend.ConversationServices;
using Travis_backend.Database;
using Travis_backend.MessageDomain.Models;
using Travis_backend.MessageDomain.ViewModels;
using Twilio.AspNet.Common;
using Twilio.AspNet.Core;
using Twilio.TwiML;

namespace Travis_backend.Controllers.WebhookControllers
{
    public class TwilioSMSWebhooksController : TwilioController
    {
        private readonly ApplicationDbContext _appDbContext;
        private readonly ILogger<TwilioSMSWebhooksController> _logger;
        private readonly IConversationMessageService _messagingService;
        private readonly IConversationResolver _conversationResolver;
        private readonly ILockService _lockService;

        public TwilioSMSWebhooksController(
            ApplicationDbContext appDbContext,
            ILogger<TwilioSMSWebhooksController> logger,
            IConversationMessageService messagingService,
            IConversationResolver conversationResolver,
            ILockService lockService)
        {
            _appDbContext = appDbContext;
            _logger = logger;
            _messagingService = messagingService;
            _conversationResolver = conversationResolver;
            _lockService = lockService;
        }

        [HttpPost]
        [Route("sms/twilio/webhook")]
        public async Task<IActionResult> Post(string companyId, SmsRequest incomingMessage, int numMedia)
        {
            var messagingResponse = new MessagingResponse();
            try
            {
                var twilioConfig = await _appDbContext.ConfigSMSConfigs
                    .Where(x => x.TwilioAccountId.Contains(incomingMessage.AccountSid)).FirstOrDefaultAsync();

                if (twilioConfig == null)
                {
                    return TwiML(messagingResponse);
                }

                companyId = twilioConfig.CompanyId;

                _logger.LogInformation(
                    "Twilio SMS webhook: {TwilioPayload}",
                    JsonConvert.SerializeObject(incomingMessage));

                Policy.Handle<Exception>()
                    .WaitAndRetry(
                        3,
                        sleepDurationProvider: i => TimeSpan.FromMilliseconds(100),
                        onRetry: (exception, _, retryCount, _) =>
                        {
                            _logger.LogError(
                                exception,
                                "Twilio SMS webhook: Enqueue error {ExceptionMessage} with retry {RetryCount}",
                                exception.Message,
                                retryCount);
                        })
                    .Execute(
                        () =>
                        {
                            BackgroundJob.Enqueue(() => HandleTwilioSmsWebhook(companyId, incomingMessage, numMedia, twilioConfig));
                        });
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Twilio SMS webhook error {TwilioPayload}",
                    JsonConvert.SerializeObject(incomingMessage));

                return BadRequest();
            }

            return TwiML(messagingResponse);
        }

        [ApiExplorerSettings(IgnoreApi = true)]
        [HangfireJobExpirationTimeout(timeoutInMinutes: 60)]
        [AutomaticRetry(
            Attempts = 10,
            OnAttemptsExceeded = AttemptsExceededAction.Fail,
            DelaysInSeconds =
            [
                60, // 1 minute
                120, // 2 minutes
                240, // 4 minutes
                480, // 8 minutes
                960, // 16 minutes
                1920, // 32 minutes
                3600, // 1 hour
                7200, // 2 hours
                14400, // 4 hours
                28800, // 8 hours
            ])]
        public async Task HandleTwilioSmsWebhook(string companyId, SmsRequest incomingMessage, int numMedia,
            SMSConfig twilioConfig)
        {
            var isSentFromSleekflowSms = incomingMessage.From == twilioConfig.SMSSender;
            Conversation conversation;
            SMSSender smsSender;
            SMSSender smsReceiver;
            ILockService.Lock myLock;
            if (isSentFromSleekflowSms)
            {
                (conversation, myLock) = await _conversationResolver.GetConversationForReceivingMessageAsync(
                    companyId,
                    ChannelTypes.Sms,
                    twilioConfig.TwilioAccountId,
                    incomingMessage.To);

                smsReceiver = conversation.SMSUser;

                smsSender = await _appDbContext.SenderSMSSenders.FirstOrDefaultAsync(
                    x => x.SMSId == incomingMessage.From && x.CompanyId == companyId);
            }
            else
            {
                (conversation, myLock) = await _conversationResolver.GetConversationForReceivingMessageAsync(
                    companyId,
                    ChannelTypes.Sms,
                    twilioConfig.TwilioAccountId,
                    incomingMessage.From);

                smsSender = conversation.SMSUser;

                smsReceiver =
                    await _appDbContext.SenderSMSSenders.FirstOrDefaultAsync(x => x.SMSId == incomingMessage.To && x.CompanyId == companyId);
            }

            var conversationMessage = new ConversationMessage()
            {
                MessageContent = incomingMessage.Body,
                Channel = ChannelTypes.Sms,
                SMSSender = smsSender,
                SMSReceiver = smsReceiver,
                MessageType = "text",
                MessageUniqueID = incomingMessage.SmsSid,
                IsSentFromSleekflow = false
            };

            if (numMedia > 0)
            {
                conversationMessage.MessageType = "file";
                var fuleURL = new List<FileURLMessage>();
                for (var i = 0; i < numMedia; i++)
                {
                    var mediaUrl = Request.Form[$"MediaUrl{i}"];
                    var MIMEType = Request.Form[$"MediaContentType{i}"];
                    var filename = System.IO.Path.GetFileName(mediaUrl);

                    fuleURL.Add(
                        new FileURLMessage()
                        {
                            FileName = filename, FileURL = mediaUrl, MIMEType = MIMEType
                        });
                }

                var result = await _messagingService.SendFileMessageByFBURL(
                    conversation,
                    conversationMessage,
                    fuleURL);
            }
            else
            {
                var result = await _messagingService.SendMessage(conversation, conversationMessage);
            }

            await _lockService.ReleaseLockAsync(myLock);
        }
    }
}