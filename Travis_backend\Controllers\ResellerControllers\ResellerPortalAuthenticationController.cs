using System;
using System.Collections.Generic;
using System.IdentityModel.Tokens.Jwt;
using System.Linq;
using System.Security.Claims;
using System.Text;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.IdentityModel.Tokens;
using Travis_backend.AccountAuthenticationDomain.Models;
using Travis_backend.AccountAuthenticationDomain.ViewModels;
using Travis_backend.CommonDomain.Models;
using Travis_backend.CommonDomain.ViewModels;
using Travis_backend.Configuration;
using Travis_backend.Database;
using Travis_backend.Helpers;
using Travis_backend.ResellerDomain.Constants;
using Travis_backend.ResellerDomain.Models;
using Travis_backend.ResellerDomain.Services;
using Travis_backend.ResellerDomain.ViewModels;

namespace Travis_backend.Controllers.ResellerControllers;

/// <summary>
/// Reseller Portal Login and Role Managements.
/// </summary>
[Route("/reseller/auth/[action]")]
public class ResellerPortalAuthenticationController : ResellerBaseController
{
    private readonly ApplicationDbContext _appDbContext;
    private readonly RoleManager<IdentityRole> _roleManager;
    private readonly SignInManager<ApplicationUser> _signInManager;
    private readonly JwtOptions _options;
    private readonly IMapper _mapper;
    private readonly ILogger<ResellerPortalAuthenticationController> _logger;
    private readonly IPowerflowManageResellerRepository _powerflowManageResellerRepository;
    private readonly IResellerPortalRepository _resellerPortalRepository;

    public ResellerPortalAuthenticationController(
        UserManager<ApplicationUser> userManager,
        RoleManager<IdentityRole> roleManager,
        SignInManager<ApplicationUser> signInManager,
        ApplicationDbContext appDbContext,
        IOptions<JwtOptions> options,
        IMapper mapper,
        ILogger<ResellerPortalAuthenticationController> logger,
        IPowerflowManageResellerRepository powerflowManageResellerRepository,
        IResellerPortalRepository resellerPortalRepository)
        : base(userManager, appDbContext)
    {
        _roleManager = roleManager;
        _signInManager = signInManager;
        _appDbContext = appDbContext;
        _mapper = mapper;
        _options = options.Value;
        _logger = logger;
        _powerflowManageResellerRepository = powerflowManageResellerRepository;
        _resellerPortalRepository = resellerPortalRepository;
    }

    /// <summary>
    /// Get special Token for login to Portal.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    [AllowAnonymous]
    public async Task<ActionResult<AuthenticationResponse>> Login([FromBody] LoginViewModel loginModel)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Invalid Form"
                });
        }

        // Try Login By UserName
        var loginResult = await _signInManager.PasswordSignInAsync(
            loginModel.Email,
            loginModel.Password,
            isPersistent: false,
            lockoutOnFailure: false);

        if (!loginResult.Succeeded)
        {
            // Try Login By Email if UserName is cannot login
            var emailUser = await _userManager.FindByEmailAsync(loginModel.Email);

            if (!string.IsNullOrEmpty(emailUser?.UserName))
            {
                loginResult = await _signInManager.PasswordSignInAsync(
                    emailUser?.UserName,
                    loginModel.Password,
                    isPersistent: false,
                    lockoutOnFailure: false);
                loginModel.Email = emailUser?.UserName;
            }

            if (!loginResult.Succeeded)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        message = "Username Or Password is not correct."
                    });
            }
        }

        var user = await _userManager.FindByNameAsync(loginModel.Email);
        user.LastLoginAt = DateTime.UtcNow;
        await _appDbContext.SaveChangesAsync();

        var utcNow = DateTime.UtcNow;

        // Check have certain role before gen token
        var roles = await _userManager.GetRolesAsync(user);

        if (roles.All(x => x != ApplicationUserRole.ResellerPortalUser))
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Not reseller portal user"
                });
        }

        var claims = new List<Claim>
        {
            new Claim(JwtRegisteredClaimNames.Sub, user.Id),
            new Claim(JwtRegisteredClaimNames.UniqueName, user.UserName),
            new Claim(JwtRegisteredClaimNames.Jti, Guid.NewGuid().ToString()),
            new Claim(JwtRegisteredClaimNames.Iat, utcNow.ToString())
        };

        foreach (var role in roles)
        {
            claims.Add(new Claim(ClaimTypes.Role, role));
        }

        var signingKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(_options.Key));
        var signingCredentials = new SigningCredentials(signingKey, SecurityAlgorithms.HmacSha256);

        var jwt = new JwtSecurityToken(
            signingCredentials: signingCredentials,
            claims: claims,
            notBefore: utcNow,
            expires: utcNow.AddDays(60),
            audience: _options.Audience,
            issuer: _options.Issuer);

        var response = _mapper.Map<AuthenticationResponse>(user);
        var accessToken = new JwtSecurityTokenHandler().WriteToken(jwt);
        response.AccessToken = accessToken;

        return Ok(response);
    }

    [HttpPost]
    [AllowAnonymous]
    public async Task<ActionResult<AuthenticationResponse>> GetTokenWithLoginAsSecret(
        [FromBody]
        LoginWithLoginAsSecretViewModel loginModel)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Invalid format"
                });
        }

        var secret = CmsLoginAsHelper.ParseLoginAsSecret(loginModel.LoginAsSecret);

        if (secret is { IsValid: false })
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Invalid"
                });
        }

        var user = await _userManager.Users.FirstOrDefaultAsync(x => x.Id == secret.StaffIdentityId);

        var utcNow = DateTime.UtcNow;

        var roles = await _userManager.GetRolesAsync(user);

        var claims = new List<Claim>()
        {
            new Claim(JwtRegisteredClaimNames.Sub, user.Id),
            new Claim(JwtRegisteredClaimNames.UniqueName, user.UserName),
            new Claim(JwtRegisteredClaimNames.Jti, Guid.NewGuid().ToString()),
            new Claim(JwtRegisteredClaimNames.Iat, utcNow.ToString()),
        };

        foreach (var role in roles)
        {
            claims.Add(new Claim(ClaimTypes.Role, role));
        }

        var signingKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(_options.Key));
        var signingCredentials = new SigningCredentials(signingKey, SecurityAlgorithms.HmacSha256);
        var jwt = new JwtSecurityToken(
            signingCredentials: signingCredentials,
            claims: claims,
            notBefore: utcNow,
            expires: secret.ExpireAt,
            audience: _options.Audience,
            issuer: _options.Issuer);

        var response = _mapper.Map<AuthenticationResponse>(user);
        response.AccessToken = new JwtSecurityTokenHandler().WriteToken(jwt);
        try
        {
            response.FirstName = user.FirstName;
            response.LastName = user.LastName;
            response.SignalRGroupName = await _appDbContext.CompanyCompanies.AsSplitQuery()
                .Where(
                    x => _appDbContext.UserRoleStaffs.Where(y => y.Id == secret.StaffId).Select(y => y.CompanyId)
                        .FirstOrDefault() == x.Id)
                .Select(x => x.SignalRGroupName)
                .FirstOrDefaultAsync();
            response.AssociatedCompanyIds = await _appDbContext.UserRoleStaffs.Where(x => x.IdentityId == user.Id)
                .Select(x => x.CompanyId).ToListAsync();
            _logger.LogInformation($"Staff Login: {response.Id}, {response.Email}, {response.SignalRGroupName}");
        }
        catch (Exception ex)
        {
            _logger.LogInformation($"User Login: {response.Id}, {response.Email}, error: {ex.ToString()}");
        }

        return Ok(response);
    }

    /// <summary>
    /// Register Reseller Staff Account.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    [Authorize(Roles = ApplicationUserRole.ResellerPortalUser)]
    public async Task<ActionResult<ResellerStaffInformation>> RegisterAccount(
        [FromBody]
        RegisterResellerAccountViewModel registerModel)
    {
        if (ModelState.IsValid && User.Identity.IsAuthenticated)
        {
            var user = await GetCurrentValidResellerUser(
                new List<string>()
                {
                    ApplicationUserRole.ResellerPortalUser
                });

            if (user == null)
            {
                return Unauthorized();
            }

            try
            {
                var resellerCompany = await _appDbContext.ResellerStaffs.Include(x => x.ResellerCompanyProfile)
                    .ThenInclude(x => x.Company).Where(x => x.IdentityId == user.Id)
                    .Select(x => x.ResellerCompanyProfile.Company).FirstOrDefaultAsync();

                if (resellerCompany == null)
                {
                    return BadRequest(
                        new ResponseViewModel()
                        {
                            message = "Reseller Company Not Found"
                        });
                }

                var resellerStaffVm = new ResellerStaffViewModel()
                {
                    DisplayName = registerModel.DisplayName,
                    Username = registerModel.Username,
                    Email = registerModel.Email,
                    Password = registerModel.Password,
                    FirstName = registerModel.FirstName,
                    LastName = registerModel.LastName,
                    PhoneNumber = registerModel.PhoneNumber,
                    Position = registerModel.Position,
                    ResellerCompanyId = resellerCompany.Id
                };

                var resellerStaffResponse =
                    await _powerflowManageResellerRepository.CreateResellerStaff(resellerStaffVm);

                if (!resellerStaffResponse.IsSuccess)
                {
                    return BadRequest(
                        new ResponseViewModel()
                        {
                            message = resellerStaffResponse.ErrorMsg
                        });
                }

                await _resellerPortalRepository.AddResellerActivityLog(
                    new ResellerActivityLog
                    {
                        ResellerCompanyProfileId = resellerCompany.ResellerCompanyProfile.Id,
                        CompanyId = resellerStaffVm.ResellerCompanyId,
                        CreatedByUserId = user.Id,
                        Category = ResellerActivityLogCategory.User,
                        Action = $"Create New User - {resellerStaffVm.Username}"
                    });

                var resellerStaffInfo = (ResellerStaffInformation) resellerStaffResponse.Data;

                return resellerStaffInfo;
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Register Reseller Account error email:{Email}, username: {Username}",
                    registerModel.Email,
                    registerModel.Username);

                return BadRequest(
                    new ResponseViewModel()
                    {
                        message = ex.Message
                    });
            }
        }

        return BadRequest();
    }

    /// <summary>
    /// Check User Role of the User.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpGet]
    [Authorize]
    public async Task<ActionResult> CheckUserRole()
    {
        if (User.Identity == null || !User.Identity.IsAuthenticated)
        {
            return BadRequest();
        }

        var user = await GetCurrentValidResellerUser(
            new List<string>()
            {
                ApplicationUserRole.ResellerPortalUser
            });

        return Ok(user != null);
    }
}