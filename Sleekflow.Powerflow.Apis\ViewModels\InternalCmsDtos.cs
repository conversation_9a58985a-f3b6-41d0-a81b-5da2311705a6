﻿using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using Sleekflow.Apis.FlowHub.Model;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.ConversationDomain.ViewModels;
using Travis_backend.Enums;
using Travis_backend.InternalDomain.Models;
using Travis_backend.InternalDomain.ViewModels;
using Travis_backend.MessageDomain.Models;
using Travis_backend.ResellerDomain.Models;
using Travis_backend.ResellerDomain.ViewModels;
using Travis_backend.StripeIntegrationDomain.Models;

namespace Sleekflow.Powerflow.Apis.ViewModels;

public class CmsCompanyResponse : CompanyResponse
{
    public UserInfoResponse CmsCompanyOwner { get; set; }

    public List<CmsContactOwnerAssignLogDto> CmsCompanyOwnerChangeLogs { get; set; }

    public UserInfoResponse CmsActivationOwner { get; set; }

    public List<CmsContactOwnerAssignLogDto> CmsActivationOwnerChangeLogs { get; set; }

    public List<StripePaymentConfig> StripePaymentConfigs { get; set; }

    public CmsLiveChatConfig LiveChatConfig { get; set; }

    public string CmsRemark { get; set; }

    public string CmsLeadSource { get; set; }

    public string CmsCompanyIndustry { get; set; }

    public CmsHubSpotCompanyMapDto CmsHubSpotCompanyMap { get; set; }

    public List<CmsWhatsApp360DialogUsageRecordViewModel> CmsWhatsApp360DialogUsageRecords { get; set; }

    public CompanyWhatsapp360DialogTopUpConfigViewModel CompanyWhatsapp360DialogTopUpConfig { get; set; }

    public CmsResellerResponse ResellerInfo { get; set; }

    public CmsResellerClientResponse ResellerClientInfo { get; set; }

    public int? SubscriptionTrialDays { get; set; }

    public bool IsDeleted { get; set; }

    public DateTime? UpdatedAt { get; set; }

    public CmsCompanyAdditionalInfoViewModel CmsCompanyAdditionalInfo { get; set; }

    public CmsPartnerStackCustomerMapDto CmsPartnerStackCustomerMap { get; set; }
}

public class CmsResellerResponse
{
    public ResellerBasicInformation ResellerInformation { get; set; }

    public TransactionLogsResponse ResellerTransactionLogs { get; set; }

    public List<string> ResellerCompanyIds { get; set; }
}

public class CmsResellerClientResponse
{
    public ResellerBasicInformation ResellerInformation { get; set; }
}

public class CmsLiveChatConfig
{
    public string Host { get; set; }

    public int RecentUserCount { get; set; }

    public DateTime CreateAt { get; set; }
}

public class CmsContactOwnerAssignLogDto
{
    public long Id { get; set; }

    public string FromContactOwnerId { get; set; }

    public string FromContactOwnerName { get; set; }

    public string ToContactOwnerId { get; set; }

    public string ToContactOwnerName { get; set; }

    public string AssignedByUserId { get; set; }

    public string AssignedByUserName { get; set; }

    public DateTime CreatedAt { get; set; }
}

public class CmsWebClientSenderDto
{
    public long Id { get; set; }

    public string WebClientUUID { get; set; }

    public string IPAddress { get; set; }

    public string Name { get; set; }

    public string locale { get; set; }

    public string SignalRConnectionId { get; set; }

    public string Device { get; set; }

    public string BrowserLocale { get; set; }

    [JsonConverter(typeof(StringEnumConverter))]
    public DeviceModel DeviceModel { get; set; }

    [JsonConverter(typeof(StringEnumConverter))]
    public OnlineStatus OnlineStatus { get; set; }

    public DateTime? CreatedAt { get; set; }

    public DateTime? UpdatedAt { get; set; }
}

public class FacebookConnectedDto
{
    public string CompanyId { get; set; }

    public string CompanyName { get; set; }

    public string SubscriptionPlanId { get; set; }

    public string FacebookPageId { get; set; }

    public string FacebookPageName { get; set; }

    public string LastStatus { get; set; }

    public DateTime CreatedAt { get; set; }

    public string Type { get; set; }
}

public class GetCmsCompaniesResponse
{
    public List<CmsCompanyListItemView> Companies { get; set; }
}

public class GetCmsCompanyAdditionalInfoResponse
{
    public List<CmsCompanyAdditionalInfoViewModel> CmsCompanyAdditionalInfos { get; set; }
}

public class CmsCompanyListItemView
{
    public string Id { get; set; }

    public string CompanyName { get; set; }

    public int? UserProfileCount { get; set; }

    public int StaffCount { get; set; }

    public string CompanyOwnerName { get; set; }

    public string ActivationOwnerName { get; set; }

    public CmsCompanyListConnectedChannelView ConnectedChannel { get; set; }

    public DateTime CreateAt { get; set; }

    public string SubscriptionPlanId { get; set; }

    public List<string> AddOnPlanIds { get; set; }

    public DateTime? SubscriptionPlanEndDay { get; set; }

    public decimal MonthlyRecurringRevenue { get; set; }

    public DateTime? LastLoginAt { get; set; }

    // This field is using in hubspot, not related to server location
    public string CompanyCountry { get; set; }

    public string CmsLeadSource { get; set; }

    public string CmsCompanyIndustry { get; set; }

    public string HubSpotIndustry { get; set; }

    public string HubSpotCompanyObjectId { get; set; }

    [JsonIgnore]
    public List<BillRecord> BillRecords { get; set; }

    [JsonIgnore]
    public bool IsBankTransfer { get; set; }

    [JsonIgnore]
    public bool IsStripePlanCancelled { get; set; }

    public bool IsDeleted { get; set; }

    public CompanyType CompanyType { get; set; }

    public int NumOfWorkflows { get; set; }

    public int NumOfActiveWorkflows { get; set; }

    public int NumOfEnrolments { get; set; }

    public decimal EnrolmentPercentage { get; set; }

    public int MaximumNumOfWorkflows { get; set; }

    public int MaximumNumOfActiveWorkflows { get; set; }

    public int MaximumNumOfNodesPerWorkflow { get; set; }

    public int MaximumNumOfMonthlyWorkflowExecutions { get; set; }

    public bool IsPurchasedFlowEnrolmentAddOn { get; set; }

    public decimal FlowEnrolmentAddOnMrr { get; set; }
}

public class CmsTwilioUsageDto
{
    public string CompanyId { get; set; }

    public string CompanyName { get; set; }

    // Twilio Response
    public string TwilioAccountId { get; set; }

    public string FriendlyName { get; set; }

    public DateTime? DateCreated { get; set; }

    public DateTime? DateUpdated { get; set; }

    public string SubAccountStatus { get; set; }

    // Twilio Usage Record
    public long TwilioUsageRecordId { get; set; }

    public decimal? TotalCreditValue { get; set; }

    public decimal? TotalPrice { get; set; }

    public string Currency { get; set; }

    public decimal? Balance => TotalCreditValue - TotalPrice;

    // Whatsapp Config
    public long? WhatsAppConfigId { get; set; }

    public string WhatsAppSender { get; set; }

    public string ChannelName { get; set; }

    public DateTime? ConnectedDateTime { get; set; }
}

public class CmsCompanyListConnectedChannelView
{
    public int WhatsAppConfigCount { get; set; }

    public int InstagramConfigCount { get; set; }

    public int FacebookConfigCount { get; set; }

    public int WhatsappChatAPIConfigCount { get; set; }

    public int Whatsapp360DialogConfigCount { get; set; }

    public int WebClientSenderCount { get; set; }

    public int LineConfigCount { get; set; }

    public int SMSConfigCount { get; set; }

    public int ShoplineConfigCount { get; set; }

    public int ShopifyConfigCount { get; set; }

    public int TelegramConfigCount { get; set; }

    public int ViberConfigCount { get; set; }

    public int WeChatConfigCount { get; set; }

    public int EmailConfigCount { get; set; }

    public int StripePaymentConfigCount { get; set; }

    public int WhatsappCloudApiConfigCount { get; set; }
}

public class CompanyPublicApiKeyDto
{
    public string APIKey { get; set; }

    public int? CallLimit { get; set; }

    public int Calls { get; set; }

    public DateTime CreatedAt { get; set; }
}

public class CmsCompanyStaffDto
{
    public long StaffId { get; set; }

    public string UserId { get; set; }

    public string FirstName { get; set; }

    public string LastName { get; set; }

    public string DisplayName { get; set; }

    public string UserName { get; set; }

    public string Email { get; set; }

    public string PhoneNumber { get; set; }

    public string Position { get; set; }

    public DateTime CreatedAt { get; set; }

    public DateTime LastLoginAt { get; set; }

    public bool EmailConfirmed { get; set; }

    [JsonConverter(typeof(StringEnumConverter))]
    public StaffStatus Status { get; set; }

    [JsonConverter(typeof(StringEnumConverter))]
    public StaffUserRole RoleType { get; set; }

    public string TimeZoneInfoId { get; set; }

    public MyTimeZoneInfo TimeZoneInfo { get; set; }

    public int Order { get; set; }

    public List<CmsCompanyTeamDto> AssociatedTeams { get; set; } = new ();
}

public class CmsCompanyTeamDto
{
    public long Id { get; set; }

    public string TeamName { get; set; }

    public List<long> TeamMemberStaffId { get; set; }
}

public class DivingUserInfoResponse : UserInfoResponse
{
    public DateTime DiveAt { get; set; }
}

public class TwilioTopUpLogDto
{
    public long Id { get; set; }

    public long TwilioUsageRecordId { get; set; }

    public string CompanyId { get; set; }
    public string CompanyName { get; set; }
    public string TwilioAccountSid { get; set; }

    public decimal TopUpAmount { get; set; }

    public string InternalUserId { get; set; }

    public string InternalUserName { get; set; }

    public long? AmountTotal { get; set; }

    public string InvoiceId { get; set; }


    public string CustomerId { get; set; }

    public string CustomerEmail { get; set; }

    public bool IsInternalTestingUse { get; set; }

    public string Currency { get; set; }

    [JsonConverter(typeof(StringEnumConverter))]
    public TwilioTopUpMethod Method { get; set; }

    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
}

public class CompanyWhatsapp360DialogTopUpConfigViewModel
{
    public long Id { get; set; }

    public string ClientId { get; set; }

    public string PartnerId { get; set; }

    public TopUpMode TopUpMode { get; set; }
}

public class CmsCompanyStaffData
{
    public string CompanyId { get; set; }

    public string CompanyName { get; set; }

    public string SubscriptionPlanId { get; set; }

    public long StaffId { get; set; }

    public string UserId { get; set; }

    public string Email { get; set; }

    public string PhoneNumber { get; set; }

    public string FirstName { get; set; }

    public string LastName { get; set; }

    public string DisplayName { get; set; }

    public string UserRole { get; set; }

    public string IsOwner { get; set; }

    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    public DateTime LastLoginAt { get; set; } = DateTime.UtcNow;
}