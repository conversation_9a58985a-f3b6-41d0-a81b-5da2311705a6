using Newtonsoft.Json;

namespace Travis_backend.InternalDomain.ViewModels;

public class CmsCompanyFlowHubData
{
    public string CompanyId { get; set; }

    public int NumOfWorkflows { get; set; }

    public int NumOfActiveWorkflows { get; set; }

    public int NumOfEnrolments { get; set; }

    public decimal EnrolmentPercentage { get; set; }

    public int BaseMaximumNumOfWorkflows { get; set; }

    public int BaseMaximumNumOfActiveWorkflows { get; set; }

    public int BaseMaximumNumOfNodesPerWorkflow { get; set; }

    public int BaseMaximumNumOfMonthlyWorkflowExecutions { get; set; }

    public int MaximumNumOfActiveWorkflowsOffset { get; set; }

    public int MaximumNumOfNodesPerWorkflowOffset { get; set; }

    public int MaximumNumOfMonthlyWorkflowExecutionsOffset { get; set; }

    [JsonIgnore]
    public int MaximumNumOfWorkflows => BaseMaximumNumOfWorkflows;

    [JsonIgnore]
    public int MaximumNumOfActiveWorkflows => BaseMaximumNumOfActiveWorkflows + MaximumNumOfActiveWorkflowsOffset;

    [JsonIgnore]
    public int MaximumNumOfNodesPerWorkflow => BaseMaximumNumOfNodesPerWorkflow + MaximumNumOfNodesPerWorkflowOffset;

    [JsonIgnore]
    public int MaximumNumOfMonthlyWorkflowExecutions =>
        BaseMaximumNumOfMonthlyWorkflowExecutions + MaximumNumOfMonthlyWorkflowExecutionsOffset;
}