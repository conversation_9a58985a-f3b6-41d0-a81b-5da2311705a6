﻿using System;
using Travis_backend.CommonDomain.Models;

namespace Travis_backend.InternalDomain.Models
{
    public class CmsLoginAsHistory : Entity<long>, IHasCreationDate
    {
        public string CompanyId { get; set; }

        public long LoginToStaffId { get; set; }

        public string LoginToStaffIdentityId { get; set; }

        public string CreatedByUserId { get; set; }

        public DateTime CreatedAt { get; } = DateTime.UtcNow;
    }
}