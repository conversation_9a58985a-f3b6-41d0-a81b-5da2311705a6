﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Reflection.Metadata;
using System.Threading.Tasks;
using Hangfire;
using Humanizer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Polly;
using Travis_backend.BackgroundTaskServices.Attributes;
using Travis_backend.Cache;
using Travis_backend.ChannelDomain.Models;
using Travis_backend.Constants;
using Travis_backend.ConversationDomain.ConversationResolver;
using Travis_backend.ConversationDomain.Models;
using Travis_backend.ConversationServices;
using Travis_backend.Database;
using Travis_backend.Enums;
using Travis_backend.Helpers;
using Travis_backend.MessageDomain.Models;
using Travis_backend.MessageDomain.ViewModels;
using Travis_backend.SignalR;
using WABA360Dialog;
using WABA360Dialog.ApiClient.Payloads.Converters;
using WABA360Dialog.ApiClient.Payloads.Enums;
using WABA360Dialog.ApiClient.Payloads.Models;
using WABA360Dialog.ApiClient.Payloads.Models.WebhookObjects;
using MessageStatus = WABA360Dialog.ApiClient.Payloads.Enums.MessageStatus;

namespace Travis_backend.Controllers.WebhookControllers;

[AllowAnonymous]
[Route("/whatsapp/360dialog")]
public class WhatsApp360DialogWebhooksController : Controller
{
    private const string ErrorMessageContent = "<Unsupported Message Type>";

    private readonly ApplicationDbContext _appDbContext;
    private readonly ILogger _logger;
    private readonly IConversationMessageService _conversationMessageService;
    private readonly IWhatsappTemplateQuickReplyCallbackService _whatsappTemplateQuickReplyCallbackService;
    private readonly ILockService _lockService;
    private readonly ISignalRService _signalRService;
    private readonly IConversationResolver _conversationResolver;

    public WhatsApp360DialogWebhooksController(
        ApplicationDbContext appDbContext,
        ILogger<WhatsApp360DialogWebhooksController> logger,
        IConversationMessageService conversationMessageService,
        ILockService lockService,
        ISignalRService signalRService,
        IWhatsappTemplateQuickReplyCallbackService whatsappTemplateQuickReplyCallbackService,
        IConversationResolver conversationResolver)
    {
        _appDbContext = appDbContext;
        _logger = logger;
        _conversationMessageService = conversationMessageService;
        _lockService = lockService;
        _signalRService = signalRService;
        _whatsappTemplateQuickReplyCallbackService = whatsappTemplateQuickReplyCallbackService;
        _conversationResolver = conversationResolver;
    }

    [HttpPost("webhook")]
    public async Task<ActionResult> Webhook()
    {
        WhatsApp360DialogWebhookHeaderCredential credential =
            WhatsApp360DialogHelper.ParesCredential(
                HttpContext.Request.Headers[WhatsApp360DialogHelper.CredentialHeaderName]);

        if (credential == null)
        {
            return BadRequest();
        }

        var payload = await new StreamReader(HttpContext.Request.Body).ReadToEndAsync();

        _logger.LogInformation(
            "WhatsApp360DialogWebhook - Webhook: {Payload}",
            payload);

        try
        {
            Policy.Handle<Exception>()
                .WaitAndRetry(
                    3,
                    sleepDurationProvider: i => TimeSpan.FromMilliseconds(100),
                    onRetry: (exception, _, retryCount, _) =>
                    {
                        _logger.LogError(
                            exception,
                            "WhatsApp360DialogWebhook: Enqueue error {ExceptionMessage} with retry {RetryCount}",
                            exception.Message,
                            retryCount);
                    })
                .Execute(
                    () =>
                    {
                        var webhookPayload = JsonConvert.DeserializeObject<WABA360DialogWebhookPayload>(payload, WhatsApp360DialogHelper.GetWebhookJsonSerializerSettings());

                        BackgroundJob.Enqueue(() => HandleWebhookAsync(credential, webhookPayload));
                    });
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "WhatsApp360DialogWebhook enqueue {OperationName} error for Company {CompanyId} config {360DialogConfigId}. {ExceptionMessage}",
                nameof(HandleWebhookAsync),
                credential.CompanyId,
                credential.WhatsApp360DialogConfigId,
                ex.Message);

            return BadRequest();
        }

        return Ok();
    }

    [ApiExplorerSettings(IgnoreApi = true)]
    [HangfireJobExpirationTimeout(timeoutInMinutes: 60)]
    [AutomaticRetry(
        Attempts = 10,
        OnAttemptsExceeded = AttemptsExceededAction.Fail,
        DelaysInSeconds =
        [
            60, // 1 minute
            120, // 2 minutes
            240, // 4 minutes
            480, // 8 minutes
            960, // 16 minutes
            1920, // 32 minutes
            3600, // 1 hour
            7200, // 2 hours
            14400, // 4 hours
            28800, // 8 hours
        ])]
    public async Task HandleWebhookAsync(
        WhatsApp360DialogWebhookHeaderCredential credential,
        WABA360DialogWebhookPayload webhookPayload)
    {
        var config = await _appDbContext.ConfigWhatsApp360DialogConfigs
            .AsNoTracking()
            .Include(x => x.Company)
            .Where(x => x.CompanyId == credential.CompanyId && x.Id == credential.WhatsApp360DialogConfigId)
            .Select(
                x => new Whatsapp360dialogLiteConfig
                {
                    Id = x.Id,
                    ApiKey = x.ApiKey,
                    WhatsAppPhoneNumber = x.WhatsAppPhoneNumber,
                    CompanyId = x.CompanyId,
                    SignalRGroupName = x.Company.SignalRGroupName,
                    IsOptInEnable = x.IsOptInEnable,
                    OptInConfig = x.OptInConfig,
                    IsBlastMessageEnabled = x.Company.BlastMessageConfig.IsEnabled,
                })
            .FirstOrDefaultAsync();

        if (config == null)
        {
            return;
        }

        var client = new WABA360DialogApiClient(config.ApiKey);

        // Message Status
        if (webhookPayload.Messages != null)
        {
            foreach (var message in webhookPayload.Messages)
            {
                var (conversation, myLock) = await _conversationResolver.GetConversationForReceivingMessageAsync(
                    credential.CompanyId,
                    ChannelTypes.Whatsapp360Dialog,
                    config.WhatsAppPhoneNumber,
                    message.From,
                    webhookPayload.Contacts
                        .FirstOrDefault(x => x.WaId == PhoneNumberHelper.NormalizeWhatsappPhoneNumber(message.From))
                        ?.Profile?.Name);

                var sender = conversation.WhatsApp360DialogUser;

                ConversationMessage conversationMessage = null;
                FileURLMessage messageFile = null;

                // Repack the webhook received message to conversation message
                switch (message.Type)
                {
                    case WebhookMessageType.text:
                        var textMessage = message.Text.Body;

                        if (message.Referral != null)
                        {
                            // Referral Text Format: Url (SourceType: SourceId)
                            var referralText = $"{message.Referral.SourceUrl}";
                            if (message.Referral is { SourceType: not null, SourceId: not null })
                            {
                                referralText +=
                                    $" ({message.Referral.SourceType.Transform(To.TitleCase)}: {message.Referral.SourceId})";
                            }

                            textMessage = referralText + "\n" + textMessage;
                        }

                        conversationMessage = new ConversationMessage()
                        {
                            Channel = ChannelTypes.Whatsapp360Dialog,
                            Whatsapp360DialogSender = sender,
                            MessageType = "text",
                            MessageUniqueID = message.Id,
                            MessageContent = textMessage,
                            IsSentFromSleekflow = false,
                            Timestamp = long.Parse(message.Timestamp),
                            QuotedMsgId = message.Context?.Id
                        };

                        break;
                    case WebhookMessageType.audio:
                        var audioMediaResponse = await client.GetMediaAsync(message.Audio.Id);

                        var audioExtension = MimeTypeMap.GetExtension(audioMediaResponse.ContentType.MediaType);

                        messageFile = new FileURLMessage()
                        {
                            FileName =
                                !string.IsNullOrEmpty(Path.GetExtension(audioMediaResponse.ContentDisposition.FileName))
                                    ? audioMediaResponse.ContentDisposition.FileName
                                    : audioMediaResponse.ContentDisposition.FileName + audioExtension,
                            MIMEType = audioMediaResponse.ContentType.MediaType,
                            FileStream = new MemoryStream(audioMediaResponse.FileBytes)
                        };

                        conversationMessage = new ConversationMessage()
                        {
                            Channel = ChannelTypes.Whatsapp360Dialog,
                            Whatsapp360DialogSender = sender,
                            MessageType = "file",
                            MessageUniqueID = message.Id,
                            MessageContent = null,
                            IsSentFromSleekflow = false,
                            Timestamp = long.Parse(message.Timestamp),
                            QuotedMsgId = message.Context?.Id
                        };

                        break;
                    case WebhookMessageType.voice:
                        var voiceMediaResponse = await client.GetMediaAsync(message.Voice.Id);

                        var voiceExtension = MimeTypeMap.GetExtension(voiceMediaResponse.ContentType.MediaType);

                        messageFile = new FileURLMessage()
                        {
                            FileName =
                                !string.IsNullOrEmpty(Path.GetExtension(voiceMediaResponse.ContentDisposition.FileName))
                                    ? voiceMediaResponse.ContentDisposition.FileName
                                    : voiceMediaResponse.ContentDisposition.FileName + voiceExtension,
                            MIMEType = voiceMediaResponse.ContentType.MediaType,
                            FileStream = new MemoryStream(voiceMediaResponse.FileBytes)
                        };

                        conversationMessage = new ConversationMessage()
                        {
                            Channel = ChannelTypes.Whatsapp360Dialog,
                            Whatsapp360DialogSender = sender,
                            MessageType = "file",
                            MessageUniqueID = message.Id,
                            MessageContent = null,
                            IsSentFromSleekflow = false,
                            Timestamp = long.Parse(message.Timestamp),
                            QuotedMsgId = message.Context?.Id
                        };

                        break;
                    case WebhookMessageType.video:
                        var videoMediaResponse = await client.GetMediaAsync(message.Video.Id);

                        var videoExtension = MimeTypeMap.GetExtension(videoMediaResponse.ContentType.MediaType);

                        messageFile = new FileURLMessage()
                        {
                            FileName =
                                !string.IsNullOrEmpty(Path.GetExtension(videoMediaResponse.ContentDisposition.Name))
                                    ? videoMediaResponse.ContentDisposition.FileName
                                    : videoMediaResponse.ContentDisposition.FileName + videoExtension,
                            MIMEType = videoMediaResponse.ContentType.MediaType,
                            FileStream = new MemoryStream(videoMediaResponse.FileBytes)
                        };

                        conversationMessage = new ConversationMessage()
                        {
                            Channel = ChannelTypes.Whatsapp360Dialog,
                            Whatsapp360DialogSender = sender,
                            MessageType = "file",
                            MessageUniqueID = message.Id,
                            MessageContent = message.Video.Caption,
                            IsSentFromSleekflow = false,
                            Timestamp = long.Parse(message.Timestamp),
                            QuotedMsgId = message.Context?.Id
                        };

                        break;
                    case WebhookMessageType.document:
                        var documentMediaResponse = await client.GetMediaAsync(message.Document.Id);

                        messageFile = new FileURLMessage()
                        {
                            FileName = FilenameHelper.FormatFileName(message.Document.Filename, documentMediaResponse.ContentType.MediaType),
                            MIMEType = documentMediaResponse.ContentType.MediaType,
                            FileStream = new MemoryStream(documentMediaResponse.FileBytes)
                        };

                        conversationMessage = new ConversationMessage()
                        {
                            Channel = ChannelTypes.Whatsapp360Dialog,
                            Whatsapp360DialogSender = sender,
                            MessageType = "file",
                            MessageUniqueID = message.Id,
                            MessageContent = message.Document.Caption,
                            IsSentFromSleekflow = false,
                            Timestamp = long.Parse(message.Timestamp),
                            QuotedMsgId = message.Context?.Id
                        };

                        break;
                    case WebhookMessageType.image:
                        var imageMediaResponse = await client.GetMediaAsync(message.Image.Id);
                        var imageExtension = MimeTypeMap.GetExtension(imageMediaResponse.ContentType.MediaType);

                        messageFile = new FileURLMessage()
                        {
                            FileName =
                                !string.IsNullOrEmpty(Path.GetExtension(imageMediaResponse.ContentDisposition.FileName))
                                    ? imageMediaResponse.ContentDisposition.FileName
                                    : imageMediaResponse.ContentDisposition.FileName + imageExtension,
                            MIMEType = imageMediaResponse.ContentType.MediaType,
                            FileStream = new MemoryStream(imageMediaResponse.FileBytes)
                        };

                        conversationMessage = new ConversationMessage()
                        {
                            Channel = ChannelTypes.Whatsapp360Dialog,
                            Whatsapp360DialogSender = sender,
                            MessageType = "file",
                            MessageUniqueID = message.Id,
                            MessageContent = message.Image.Caption,
                            IsSentFromSleekflow = false,
                            Timestamp = long.Parse(message.Timestamp),
                            QuotedMsgId = message.Context?.Id
                        };

                        break;
                    case WebhookMessageType.sticker:
                        var stickerMediaResponse = await client.GetMediaAsync(message.Sticker.Id);

                        messageFile = new FileURLMessage()
                        {
                            FileName = stickerMediaResponse.ContentDisposition.FileName + ".webp",
                            MIMEType = stickerMediaResponse.ContentType.MediaType,
                            FileStream = new MemoryStream(stickerMediaResponse.FileBytes)
                        };

                        conversationMessage = new ConversationMessage()
                        {
                            Channel = ChannelTypes.Whatsapp360Dialog,
                            Whatsapp360DialogSender = sender,
                            MessageType = "file",
                            MessageUniqueID = message.Id,
                            IsSentFromSleekflow = false,
                            Timestamp = long.Parse(message.Timestamp),
                            QuotedMsgId = message.Context?.Id
                        };

                        break;
                    case WebhookMessageType.interactive:
                        var interactiveMessageContent = string.Empty;

                        if (message.InteractiveReply.Type == InteractiveReplyType.button_reply)
                        {
                            interactiveMessageContent = message.InteractiveReply.ButtonReply.Title;
                        }
                        else if (message.InteractiveReply.Type == InteractiveReplyType.list_reply)
                        {
                            interactiveMessageContent = message.InteractiveReply.ListReply.Title;
                        }

                        conversationMessage = new ConversationMessage()
                        {
                            Channel = ChannelTypes.Whatsapp360Dialog,
                            Whatsapp360DialogSender = sender,
                            MessageType = "text",
                            MessageUniqueID = message.Id,
                            MessageContent = interactiveMessageContent,
                            IsSentFromSleekflow = false,
                            Timestamp = long.Parse(message.Timestamp),
                            QuotedMsgId = message.Context?.Id,
                            Whatsapp360DialogExtendedMessagePayload = new Whatsapp360DialogExtendedMessagePayload()
                            {
                                ReplyPayload = new Whatsapp360DialogExtendedMessageReplyPayload()
                                {
                                    InteractiveReply = message.InteractiveReply
                                }
                            }
                        };

                        break;
                    case WebhookMessageType.button: // Template Quick Reply button

                        // OptIn
                        if (config.IsOptInEnable && config.OptInConfig != null)
                        {
                            if (!string.IsNullOrEmpty(message.TemplateButtonReply.Text) &&
                                message.TemplateButtonReply.Text == config.OptInConfig.ReadMoreTemplateButtonMessage)
                            {
                                if (await _appDbContext.ConversationMessages.AnyAsync(
                                        x => x.Whatsapp360DialogReceiver.Id == sender.Id &&
                                             DateTime.UtcNow.AddDays(-1) < x.CreatedAt &&
                                             x.DeliveryType == DeliveryType.ReadMore))
                                {
                                    var lastUndeliveredMessage = await _appDbContext.ConversationMessages.Where(
                                            x => x.Whatsapp360DialogReceiver.Id == sender.Id &&
                                                 x.Status == MessageDomain.Models.MessageStatus.Undelivered)
                                        .OrderByDescending(x => x.Timestamp)
                                        .FirstOrDefaultAsync();

                                    if (lastUndeliveredMessage != null)
                                    {
                                        BackgroundJob.Enqueue<IConversationMessageService>(
                                            x => x.SendWhatsapp360DialogReadMoreMessage(
                                                lastUndeliveredMessage.CompanyId,
                                                lastUndeliveredMessage.ConversationId,
                                                config.Id,
                                                lastUndeliveredMessage.Id));
                                    }
                                }
                            }
                        }

                        conversationMessage = new ConversationMessage()
                        {
                            Channel = ChannelTypes.Whatsapp360Dialog,
                            Whatsapp360DialogSender = sender,
                            MessageType = "text",
                            MessageUniqueID = message.Id,
                            MessageContent = message.TemplateButtonReply.Text,
                            IsSentFromSleekflow = false,
                            Timestamp = long.Parse(message.Timestamp),
                            QuotedMsgId = message.Context?.Id,
                            Whatsapp360DialogExtendedMessagePayload = new Whatsapp360DialogExtendedMessagePayload()
                            {
                                ReplyPayload = new Whatsapp360DialogExtendedMessageReplyPayload()
                                {
                                    TemplateButtonReply = message.TemplateButtonReply
                                }
                            }
                        };

                        break;

                    // case WebhookMessageType.contacts:
                    case WebhookMessageType.location:
                        var locationMessage = $"Shared a Location:\n" +
                                              (!string.IsNullOrWhiteSpace(message.Location.Address)
                                                  ? $"Address: {message.Location.Address}\n"
                                                  : string.Empty) +
                                              (!string.IsNullOrWhiteSpace(message.Location.Name)
                                                  ? $"Name: {message.Location.Name}\n"
                                                  : string.Empty) +
                                              $"https://maps.google.com/?q={message.Location.Latitude},{message.Location.Longitude}";

                        conversationMessage = new ConversationMessage()
                        {
                            Channel = ChannelTypes.Whatsapp360Dialog,
                            Whatsapp360DialogSender = sender,
                            MessageType = "text",
                            MessageUniqueID = message.Id,
                            MessageContent = locationMessage,
                            IsSentFromSleekflow = false,
                            Timestamp = long.Parse(message.Timestamp),
                            QuotedMsgId = message.Context?.Id,
                            Whatsapp360DialogExtendedMessagePayload = new Whatsapp360DialogExtendedMessagePayload()
                            {
                                ReplyPayload = new Whatsapp360DialogExtendedMessageReplyPayload()
                                {
                                    Location = message.Location
                                }
                            }
                        };

                        break;
                    case WebhookMessageType.contacts:
                        string contactMessage = string.Empty;

                        foreach (var contact in message.Contacts)
                        {
                            var phoneNumbers = string.Empty;
                            if (contact.Phones != null && contact.Phones.Count() > 0)
                            {
                                phoneNumbers = string.Join(',', contact.Phones.Select(x => x.Phone));
                            }

                            // var email = "";
                            // if (contact.Phones == null)
                            //     email = string.Join(',', contact.Emails.Select(x => x.Email));
                            contactMessage += $"{contact.Name?.FormattedName}: {phoneNumbers}\n";
                        }

                        conversationMessage = new ConversationMessage()
                        {
                            Channel = ChannelTypes.Whatsapp360Dialog,
                            Whatsapp360DialogSender = sender,
                            MessageType = "text", // actually contacts
                            MessageUniqueID = message.Id,
                            MessageContent = contactMessage,
                            IsSentFromSleekflow = false,
                            Timestamp = long.Parse(message.Timestamp),
                            QuotedMsgId = message.Context?.Id,
                            Whatsapp360DialogExtendedMessagePayload = new Whatsapp360DialogExtendedMessagePayload()
                            {
                                ReplyPayload = new Whatsapp360DialogExtendedMessageReplyPayload()
                                {
                                    Contacts = message.Contacts.ToList()
                                }
                            }
                        };

                        break;
                    case WebhookMessageType.order:
                        var orderMessage = $"{message.Order.Text}\n" +
                                           $"----- Order -----\n" +
                                           $"Catalog Id: {message.Order.CatalogId}\n";

                        foreach (var productItem in message.Order.ProductItems)
                        {
                            orderMessage +=
                                $"Product: {productItem.ProductRetailerId} x {productItem.Quantity} x {productItem.ItemPrice} {productItem.Currency}\n";
                        }

                        conversationMessage = new ConversationMessage()
                        {
                            Channel = ChannelTypes.Whatsapp360Dialog,
                            Whatsapp360DialogSender = sender,
                            MessageType = "text", // actually order
                            MessageUniqueID = message.Id,
                            MessageContent = orderMessage,
                            IsSentFromSleekflow = false,
                            Timestamp = long.Parse(message.Timestamp),
                            QuotedMsgId = message.Context?.Id,
                            Whatsapp360DialogExtendedMessagePayload = new Whatsapp360DialogExtendedMessagePayload()
                            {
                                ReplyPayload = new Whatsapp360DialogExtendedMessageReplyPayload()
                                {
                                    Order = message.Order
                                }
                            }
                        };

                        break;
                    default:
                        conversationMessage = new ConversationMessage()
                        {
                            Channel = ChannelTypes.Whatsapp360Dialog,
                            Whatsapp360DialogSender = sender,
                            MessageType = "text",
                            MessageUniqueID = message.Id,
                            MessageContent = ErrorMessageContent,
                            IsSentFromSleekflow = false,
                            Timestamp = long.Parse(message.Timestamp),
                            //TODO: update the possible error messages related stuff here!
                            ChannelStatusMessage = JsonConvert.SerializeObject(message),
                        };

                        break;
                }

                // PDD-7538 https://app.clickup.com/t/9008009945/PDD-7538
                // Display <Unsupported Message Type> when receive incoming message with types that un-handled by 360Dialog.
                if (message.Errors != null && message.Errors.Any() && string.IsNullOrEmpty(message.Text.Body))
                {
                    conversationMessage.MessageContent = ErrorMessageContent;

                    conversationMessage.Metadata ??= new Dictionary<string, object>();
                    conversationMessage.Metadata.Add("errors", message.Errors.Select(x => new ConversationMessageError
                    {
                        Code = x.Code.ToString(),
                        Message = x.Details,
                        InnerError = x
                    }).ToList());
                }

                conversationMessage.Status = MessageDomain.Models.MessageStatus.Received;
                IList<ConversationMessage> sentMessages;

                if (!string.IsNullOrEmpty(message.Timestamp))
                {
                    var time = MyDateTimeUtil.UnixTimeStampToDateTimeUTC(
                        conversationMessage.Timestamp * 1000); // second to Milliseconds
                    conversationMessage.CreatedAt = time;
                    conversationMessage.UpdatedAt = time;
                }

                if (messageFile != null)
                {
                    sentMessages = await _conversationMessageService.SendFileMessageByFBURL(
                        conversation,
                        conversationMessage,
                        new List<FileURLMessage>()
                        {
                            messageFile
                        });
                }
                else
                {
                    sentMessages = await _conversationMessageService.SendMessage(conversation, conversationMessage);
                }

                if (myLock != null)
                {
                    await _lockService.ReleaseLockAsync(myLock);
                }

                if (message.Type == WebhookMessageType.button && message.TemplateButtonReply.Payload != null &&
                    sentMessages is { Count : > 0 })
                {
                    var sendMessage = sentMessages.First();
                    await _whatsappTemplateQuickReplyCallbackService.TryEnqueueCallbackActionAsync(
                        sendMessage.CompanyId,
                        sendMessage.QuotedMsgId,
                        sendMessage.MessageContent,
                        sendMessage.Id);
                }
            }
        }

        // Message Status
        if (webhookPayload.Statuses != null)
        {
            foreach (var status in webhookPayload.Statuses)
            {
                var conversationMessage = await _appDbContext.ConversationMessages
                    .Include(x => x.Whatsapp360DialogSender)
                    .Include(x => x.Whatsapp360DialogReceiver)
                    .Include(x => x.Whatsapp360DialogExtendedMessagePayload)
                    .Include(x => x.UploadedFiles)
                    .FirstOrDefaultAsync(x => x.MessageUniqueID == status.Id && x.CompanyId == credential.CompanyId);

                if (config.IsBlastMessageEnabled is true && conversationMessage == null)
                {
                    return;
                }

                if (conversationMessage == null)
                {
                    throw new InvalidOperationException("No conversation message found.");
                }

                switch (status.Status)
                {
                    case MessageStatus.sent:
                        if (conversationMessage.Status is not MessageDomain.Models.MessageStatus.Sending)
                        {
                            continue;
                        }

                        conversationMessage.Status = MessageDomain.Models.MessageStatus.Sent;

                        break;
                    case MessageStatus.delivered:
                        if (conversationMessage.Status is MessageDomain.Models.MessageStatus.Read or MessageDomain.Models.MessageStatus.Failed)
                        {
                            continue;
                        }

                        conversationMessage.Status = MessageDomain.Models.MessageStatus.Received;

                        break;
                    case MessageStatus.read:
                        if (conversationMessage.Status is MessageDomain.Models.MessageStatus.Read or MessageDomain.Models.MessageStatus.Deleted)
                        {
                            continue;
                        }

                        conversationMessage.Status = MessageDomain.Models.MessageStatus.Read;

                        break;
                    case MessageStatus.failed:
                        conversationMessage.Status = MessageDomain.Models.MessageStatus.Failed;

                        if (status.Errors != null && status.Errors.Any())
                        {
                            conversationMessage.ChannelStatusMessage = string.Join(
                                ';',
                                //TODO: update the possible error messages related stuff here!
                                status.Errors.Select(
                                        x =>
                                            $"{x.Code} - {x.Details}{(!string.IsNullOrWhiteSpace(x.Title) ? x.Title : null)}")
                                    .ToList());
                        }

                        if (config.IsOptInEnable && status.Errors.Any(x => x.Code == 470))
                        {
                            conversationMessage.Status = MessageDomain.Models.MessageStatus.Undelivered;
                            await _conversationMessageService.SendWhatsapp360DialogOptInButtonMessage(
                                conversationMessage.CompanyId,
                                conversationMessage.ConversationId,
                                config.Id);
                        }

                        break;
                    case MessageStatus.deleted:
                        conversationMessage.Status = MessageDomain.Models.MessageStatus.Deleted;

                        break;
                }

                _logger.LogInformation(
                    $"Message 360Dialog Statues Update: {conversationMessage.MessageUniqueID}, {status.Status}");

                await _appDbContext.SaveChangesAsync();
                await _signalRService.SignalROnMessageStatusChanged(conversationMessage);
            }
        }
    }
}