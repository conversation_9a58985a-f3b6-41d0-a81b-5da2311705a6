﻿using System;
using System.Collections.Generic;
using Travis_backend.ChannelDomain.Models;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.Enums;
using Travis_backend.Models.ChatChannelConfig;

namespace Travis_backend.InternalDomain.Models
{
    public class CmsCompanyDetail
    {
        public CmsHubSpotCompanyMap CmsHubSpotCompanyMap { get; set; }

        public string CompanyId { get; set; }

        public string CompanyName { get; set; }

        public CmsCompanyAdditionalInfo CmsCompanyAdditionalInfo { get; set; }

        public DateTime CreateAt { get; set; }

        public string CompanyCountry { get; set; }

        public string CmsLeadSource { get; set; }

        public string CmsCompanyIndustry { get; set; }

        public int WhatsAppConfigCount { get; set; }

        public int WhatsApp360DialogConfigCount { get; set; }

        public int WhatsappCloudApiConfigCount { get; set; }

        public List<WhatsappCloudApiConfig> WhatsappCloudApiConfigs { get; set; } = new List<WhatsappCloudApiConfig>();

        public int InstagramConfigCount { get; set; }

        public int FacebookConfigCount { get; set; }

        public int WhatsappChatAPIConfigCount { get; set; }

        public int WebClientSenderCount { get; set; }

        public int LineConfigCount { get; set; }

        public int SMSConfigCount { get; set; }

        public int ShoplineConfigCount { get; set; }

        public int ShopifyConfigCount { get; set; }

        public int TelegramConfigCount { get; set; }

        public int ViberConfigCount { get; set; }

        public int WeChatConfigCount { get; set; }

        public int EmailConfigCount { get; set; }

        public int NumberOfConnectedChannels => WhatsAppConfigCount +
                                                WhatsappChatAPIConfigCount +
                                                InstagramConfigCount +
                                                FacebookConfigCount +
                                                WebClientSenderCount +
                                                LineConfigCount +
                                                SMSConfigCount +
                                                TelegramConfigCount +
                                                ViberConfigCount +
                                                WeChatConfigCount +
                                                EmailConfigCount;

        public int TypesOfChannels => (WhatsAppConfigCount > 0 ? 1 : 0) +
                                      (WhatsappChatAPIConfigCount > 0 ? 1 : 0) +
                                      (InstagramConfigCount > 0 ? 1 : 0) +
                                      (FacebookConfigCount > 0 ? 1 : 0) +
                                      (WebClientSenderCount > 0 ? 1 : 0) +
                                      (LineConfigCount > 0 ? 1 : 0) +
                                      (SMSConfigCount > 0 ? 1 : 0) +
                                      (TelegramConfigCount > 0 ? 1 : 0) +
                                      (ViberConfigCount > 0 ? 1 : 0) +
                                      (WeChatConfigCount > 0 ? 1 : 0) +
                                      (EmailConfigCount > 0 ? 1 : 0);

        public string ActivationOwnerId { get; set; }

        public string ActivationOwnerName { get; set; }

        public string CmsCompanyOwnerOwnerName { get; set; }

        public string CmsCompanyOwnerOwnerId { get; set; }

        public string HubSpotCompanyOwnerId { get; set; }

        public string HubSpotActivationOwnerId { get; set; }

        public Staff Owner { get; set; }

        public DateTime? LastStaffLoginAt { get; set; }

        public List<BillRecord> BillRecords { get; set; } = new ();

        public int ContactCount { get; set; }

        public int? MaximumContacts { get; set; }

        public int StaffCount { get; set; }

        public int MaximumAgents { get; set; }

        public int AutomationCount { get; set; }

        public int LiveAutomationCount { get; set; }

        public int? MaximumAutomations { get; set; }

        public TwilioUsageRecord TwilioUsageRecord { get; set; }

        public int ConversationCount { get; set; }

        public int BroadcastCount { get; set; }

        public int BroadcastCountInPastMonth { get; set; }

        public int NewEnquiriesInPastDay { get; set; }

        public float? NewEnquiriesDifferenceInPercentage { get; set; }

        public int NewContactsInPastDay { get; set; }

        public float? NewContactsDifferenceInPercentage { get; set; }

        public int ActiveConversationsInPastDay { get; set; }

        public float? ActiveConversationsDifferenceInPercentage { get; set; }

        public int ApiKeyCount { get; set; }

        public int ZapierIntegrationCount { get; set; }

        public int StripePaymentCount { get; set; }

        public int TypesOfSoftwareIntegrations => (ShopifyConfigCount > 0 ? 1 : 0) +
                                                  (ShoplineConfigCount > 0 ? 1 : 0) +
                                                  (ApiKeyCount > 0 ? 1 : 0) +
                                                  (StripePaymentCount > 0 ? 1 : 0) +
                                                  (ZapierIntegrationCount > 0 ? 1 : 0);

        public int SupportTicketCount { get; set; }

        public int SupportTicketCountInPastTwoWeek { get; set; }

        public int SupportTicketCountInPastMonth { get; set; }

        public int ActiveAgentsInPastDay { get; set; }

        public int PaymentFailedCount { get; set; }

        public int PaymentFailedCountInPastThreeMonth { get; set; }

        public DateTime? LastAgentMessageSentAt { get; set; }

        public DateTime? InitialPaidDate { get; set; }

        public int? InboxMessagesInPast7Days { get; set; }

        public WhatsApp360DialogUsageRecord WhatsApp360DialogUsageRecord { get; set; }

        public CompanyType CompanyType { get; set; }

        public bool IsDeleted { get; set; }

        public decimal Whatsapp360dialogBalance { get; set; }

        public List<string> CommunicationTools { get; set; }

        public string Industry { get; set; }

        public string OnlineShopSystem { get; set; }

        public string CompanySize { get; set; }

        public string CompanyWebsite { get; set; }

        public int ActiveContactsInPastMonth { get; set; }

        public int ActiveContactsInCurrentMonth { get; set; }

        public int IntegrationCount { get; set; }
    }
}