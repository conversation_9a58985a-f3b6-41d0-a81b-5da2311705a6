using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using SendGrid;
using SendGrid.Helpers.Mail;
using Travis_backend.Constants;
using Travis_backend.ConversationDomain.Models;
using Travis_backend.ConversationServices;
using Travis_backend.Database;
using Travis_backend.MessageDomain.Models;
using Travis_backend.OpenTelemetry.Meters;
using Travis_backend.OpenTelemetry.Meters.Constants;

namespace Travis_backend.MessageDomain.ChannelMessageProvider.ChannelMessageHandlers;

public class EmailChannelMessageHandler : IChannelMessageHandler
{
    private readonly ApplicationDbContext _appDbContext;
    private readonly IConfiguration _configuration;
    private readonly ILogger<EmailChannelMessageHandler> _logger;
    private readonly IAzureBlobStorageService _azureBlobStorageService;
    private readonly IConversationMeters _conversationMeters;

    public string ChannelType => ChannelTypes.Email;

    public EmailChannelMessageHandler(
        ApplicationDbContext appDbContext,
        IConfiguration configuration,
        ILogger<EmailChannelMessageHandler> logger,
        IAzureBlobStorageService azureBlobStorageService,
        IConversationMeters conversationMeters)
    {
        _appDbContext = appDbContext;
        _configuration = configuration;
        _logger = logger;
        _azureBlobStorageService = azureBlobStorageService;
        _conversationMeters = conversationMeters;
    }

    public async ValueTask<ConversationMessage> SendChannelMessageAsync(
        Conversation conversation,
        ConversationMessage conversationMessage)
    {
        try
        {
            if (IsCSReply(conversation, conversationMessage))
            {
                var emailConfig = await _appDbContext.CompanyCompanies.Where(x => x.Id == conversation.CompanyId)
                    .Select(x => x.EmailConfig).FirstOrDefaultAsync();

                var client = new SendGridClient(emailConfig.SendGridKey);
                var msg = new SendGridMessage();

                if (conversation.EmailAddress.To.Contains(emailConfig.Domain))
                {
                    var toList = SplitEmail(conversation.EmailAddress.To);

                    if (toList.Count > 1)
                    {
                        foreach (var to in toList)
                        {
                            if (to.Email.Contains(emailConfig.Domain))
                            {
                                msg.SetFrom(to);
                            }
                            else
                            {
                                msg.AddCc(to);
                                conversationMessage.EmailTo += $"{to.Email},";
                            }
                        }
                    }
                    else
                    {
                        msg.SetFrom(emailConfig.Email);
                        msg.AddCc(toList[0]);

                        // msg.SetFrom(toList[0]);
                    }

                    if (conversation.EmailAddress.CC != null)
                    {
                        var CCs = SplitEmail(conversation.EmailAddress.CC);
                        msg.AddCcs(CCs);
                    }
                }
                else if (conversation.EmailAddress.CC != null &&
                         conversation.EmailAddress.CC.Contains(emailConfig.Domain))
                {
                    var CCs = SplitEmail(conversation.EmailAddress.CC);

                    if (CCs.Count() > 1)
                    {
                        foreach (var cc in CCs)
                        {
                            if (cc.Email.Contains(emailConfig.Domain))
                            {
                                msg.SetFrom(emailConfig.Email);
                                msg.AddCc(cc);

                                // msg.SetFrom(cc);
                            }
                            else
                            {
                                msg.AddCc(cc);
                                conversationMessage.EmailCC += $"{cc.Email},";
                            }
                        }
                    }
                    else
                    {
                        msg.SetFrom(emailConfig.Email);

                        if (CCs.Count > 0)
                        {
                            msg.AddCc(CCs[0]);
                        }

                        // msg.SetFrom(CCs[0]);
                    }

                    var toList = SplitEmail(conversation.EmailAddress.To);
                    msg.AddCcs(toList);
                    conversationMessage.EmailCC += $"{conversation.EmailAddress.To},";
                }
                else
                {
                    return conversationMessage;
                }

                conversationMessage.EmailTo = conversation.EmailAddress.Email;

                var recipients = new List<EmailAddress>
                {
                    new EmailAddress(conversationMessage.EmailTo)
                };
                msg.AddTos(recipients);

                if (conversationMessage.Subject != null)
                {
                    msg.SetSubject(conversationMessage.Subject);
                }
                else
                {
                    var oldMessage = await _appDbContext.ConversationMessages
                        .Where(
                            x =>
                                x.ConversationId == conversation.Id &&
                                x.Channel == ChannelTypes.Email)
                        .OrderByDescending(x => x.CreatedAt)
                        .Skip(1)
                        .FirstOrDefaultAsync();

                    var subject = $"Re: {oldMessage.Subject}";
                    msg.SetSubject(subject);
                    conversationMessage.Subject = subject;
                }

                if (conversationMessage.MessageContent != null)
                {
                    if (conversationMessage.TranslationResults != null)
                    {
                        var messageContent = conversationMessage.TranslationResults
                            .FirstOrDefault().translations
                            .FirstOrDefault().text;

                        msg.AddContent(MimeType.Text, messageContent);
                    }
                    else
                    {
                        msg.AddContent(MimeType.Text, conversationMessage.MessageContent);
                    }
                }

                if (conversationMessage.UploadedFiles != null && conversationMessage.UploadedFiles.Count > 0)
                {
                    var domainName = _configuration.GetValue<String>("Values:DomainName");

                    foreach (var uploadedFile in conversationMessage.UploadedFiles)
                    {
                        var stream = await _azureBlobStorageService.DownloadFromAzureBlob(
                            uploadedFile.Filename,
                            uploadedFile.BlobContainer);

                        var filename = Path.GetFileNameWithoutExtension(uploadedFile.Filename);
                        var extension = Path.GetExtension(uploadedFile.Filename);

                        if (string.IsNullOrEmpty(extension))
                        {
                            switch (uploadedFile.MIMEType)
                            {
                                case "video/mp4":
                                    extension = "mp4";

                                    break;
                                case "image/jpeg":
                                    extension = "jpg";

                                    break;
                                case "image/png":
                                    extension = "png";

                                    break;
                            }
                        }

                        filename = $"{filename}.{extension}";

                        msg.AddAttachment(
                            filename,
                            Convert.ToBase64String(stream.ToArray()),
                            uploadedFile.MIMEType);
                    }
                }

                var response = await client.SendEmailAsync(msg);

                if (response.StatusCode == HttpStatusCode.BadRequest)
                {
                    conversationMessage.Status = MessageStatus.Failed;

                    _conversationMeters.IncrementCounter(ChannelTypes.Email, ConversationMeterOptions.SendFailed);
                }
                else
                {
                    conversationMessage.Status = MessageStatus.Read;

                    _conversationMeters.IncrementCounter(ChannelTypes.Email, ConversationMeterOptions.SendSuccess);
                }
            }
            else
            {
                // if (conversation.EmailAddress.Email.Contains(conversation.Company.EmailConfig.Domain))
                // {
                conversationMessage.IsSentFromSleekflow = true;
                conversationMessage.Status = MessageStatus.Sent;

                _conversationMeters.IncrementCounter(ChannelTypes.Email, ConversationMeterOptions.SendSuccess);

                // }
            }

            await _appDbContext.SaveChangesAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "[{MethodName}] Conversation {ConversationId} error for message {MessageId}. {ExceptionMessage}",
                nameof(SendChannelMessageAsync),
                conversation.Id,
                conversationMessage.Id,
                ex.Message);
        }

        return conversationMessage;
    }

    private bool IsCSReply(Conversation conversation, ConversationMessage conversationMessage)
    {
        if (conversationMessage.Sender != null
            || conversationMessage.DeliveryType == DeliveryType.Broadcast
            || conversationMessage.DeliveryType == DeliveryType.AutomatedMessage
            || conversationMessage.DeliveryType == DeliveryType.FlowHubAction
            || conversationMessage.MessageType == MessageTypes.System
            || conversationMessage.IsSentFromSleekflow)
        {
            return true;
        }

        var companyEmailConfigDomain = _appDbContext.CompanyCompanies
            .Where(x => x.Id == conversation.CompanyId).Select(x => x.EmailConfig.Domain).AsNoTracking()
            .FirstOrDefault();

        if (companyEmailConfigDomain == null)
        {
            throw new Exception($"No email config found");
        }

        return !conversationMessage.EmailTo.Contains(companyEmailConfigDomain) &&
               conversationMessage.EmailCC != null &&
               !conversationMessage.EmailCC.Contains(companyEmailConfigDomain);
    }

    private List<EmailAddress> SplitEmail(string EmailAddress)
    {
        List<EmailAddress> emails = new List<EmailAddress>();
        var CCs = EmailAddress.Split(",");

        if (CCs.Count() > 1)
        {
            foreach (var cc in CCs)
            {
                var ccEmail = cc;

                if (cc.Contains("<"))
                {
                    ccEmail = ccEmail.Substring(
                        ccEmail.IndexOf('<') + 1,
                        ccEmail.IndexOf('>') - ccEmail.IndexOf('<') - 1);
                }

                emails.Add(
                    new EmailAddress
                    {
                        Email = ccEmail
                    });
            }
        }
        else
        {
            emails.Add(
                new EmailAddress
                {
                    Email = CCs.FirstOrDefault()
                });
        }

        return emails;
    }
}