using System;
using System.Collections.Generic;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Travis_backend.Helpers;

namespace Travis_backend.PartnerStackIntegrationDomain.Models;

public class PartnerStackPartnership
{
    [JsonProperty("created_at")]
    [JsonConverter(typeof(JsonConvertHelper.EpochConverter))]
    public DateTime CreatedAt { get; set; }

    [JsonProperty("key")]
    public string Key { get; set; }

    [JsonProperty("updated_at")]
    [JsonConverter(typeof(JsonConvertHelper.EpochConverter))]
    public DateTime UpdatedAt { get; set; }

    [JsonProperty("email")]
    public string Email { get; set; }

    [JsonProperty("field_data")]
    public JObject FieldData { get; set; }

    [JsonProperty("fields")]
    public List<PartnerStackField> Fields { get; set; }

    [JsonProperty("first_name")]
    public string FirstName { get; set; }

    [JsonProperty("fraud_flagged")]
    public bool FraudFlagged { get; set; }

    [JsonProperty("last_name")]
    public string LastName { get; set; }

    [JsonProperty("manager_email")]
    public string ManagerEmail { get; set; }

    [JsonProperty("manager_name")]
    public string ManagerName { get; set; }

    [JsonProperty("meta")]
    public JObject Meta { get; set; }

    [JsonProperty("partner_key")]
    public string PartnerKey { get; set; }

    [JsonProperty("tags")]
    public List<string> Tags { get; set; }

    [JsonProperty("group")]
    public PartnerStackGroup Group { get; set; }

    [JsonProperty("tier")]
    public PartnerStackTier Tier { get; set; }

    [JsonProperty("address")]
    public PartnerStackAddress Address { get; set; }

    [JsonProperty("approved_status")]
    public string ApprovedStatus { get; set; }

    [JsonProperty("team")]
    public PartnerStackTeam Team { get; set; }
}