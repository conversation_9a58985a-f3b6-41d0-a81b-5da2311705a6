﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using System.Net.Http;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Serialization;
using Hangfire;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Polly;
using Travis_backend.BackgroundTaskServices.Attributes;
using Travis_backend.Cache;
using Travis_backend.ChannelDomain.ViewModels;
using Travis_backend.Constants;
using Travis_backend.ConversationDomain.ConversationResolver;
using Travis_backend.ConversationServices;
using Travis_backend.Database;
using Travis_backend.Helpers;
using Travis_backend.MessageDomain.Models;
using Travis_backend.MessageDomain.ViewModels;

namespace Travis_backend.Controllers.WebhookControllers
{
    public class WeChatWebhooksController : Controller
    {
        private readonly ApplicationDbContext _appDbContext;
        private readonly ILogger<WeChatWebhooksController> _logger;
        private readonly IConversationMessageService _messagingService;
        private readonly IConversationResolver _conversationResolver;
        private readonly ILockService _lockService;
        private readonly IHttpClientFactory _httpClientFactory;

        public WeChatWebhooksController(
            ApplicationDbContext appDbContext,
            ILogger<WeChatWebhooksController> logger,
            IConversationMessageService messagingService,
            IHttpClientFactory httpClientFactory,
            IConversationResolver conversationResolver,
            ILockService lockService)
        {
            _appDbContext = appDbContext;
            _logger = logger;
            _messagingService = messagingService;
            _conversationResolver = conversationResolver;
            _lockService = lockService;
            _httpClientFactory = httpClientFactory;
        }

        [HttpGet]
        [Route("wechat/Webhook/{companyId}")]
        public IActionResult Authenticate(string companyId, [FromQuery] WxSignatureParameter signatureParameter)
        {
            try
            {
                _logger.LogDebug("Action is [Authenticate]");
                _logger.LogDebug("Need to verify validity of the URL");

                _logger.LogInformation(
                    "WeChat verifying Company {CompanyId} Connection with signature: {Signature}, timestamp: {Timestamp}, nonce: {Nonce}, echostr: {Echostr}",
                    companyId,
                    signatureParameter.Signature,
                    signatureParameter.Timestamp,
                    signatureParameter.Nonce,
                    signatureParameter.Echostr);

                if (CheckSignature(signatureParameter, companyId.Replace("-", string.Empty)))
                {
                    _logger.LogDebug("URL is valid");
                    string echostr = signatureParameter.Echostr;
                    _logger.LogDebug(string.Format("Return echostr: {0}", echostr));
                    return Ok(echostr);
                }
                else
                {
                    _logger.LogDebug("Authentication fail");
                    return BadRequest("Fail to authenticate");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Company {CompanyId} WeChat authenticate error. {ExceptionMessage}",
                    companyId,
                    ex.Message);

                return BadRequest(ex.ToString());
            }
        }

        [HttpPost]
        [Route("wechat/Webhook/{companyId}")]
        public async Task<IActionResult> Post(string companyId)
        {
            var xmlString = await new StreamReader(HttpContext.Request.Body).ReadToEndAsync();
            var serializer = new XmlSerializer(typeof(WeChatMessage));

            using var stringReader = new StringReader(xmlString);
            var receivedMessage = (WeChatMessage) serializer.Deserialize(stringReader);

            _logger.LogInformation(
                "Wechat webhook payload: {XmlBody}, {Payload}",
                xmlString,
                JsonConvert.SerializeObject(receivedMessage));

            try
            {
                Policy.Handle<Exception>()
                    .WaitAndRetry(
                        3,
                        sleepDurationProvider: i => TimeSpan.FromMilliseconds(100),
                        onRetry: (exception, _, retryCount, _) =>
                        {
                            _logger.LogError(
                                exception,
                                "Wechat webhook: Enqueue error {ExceptionMessage} with retry {RetryCount}",
                                exception.Message,
                                retryCount);
                        })
                    .Execute(
                        () =>
                        {
                            BackgroundJob.Enqueue(() => HandleWechatWebhook(companyId, xmlString, receivedMessage));
                        });
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Wechat webhook error: {ExceptionMessage}",
                    ex.Message);

                return BadRequest();
            }

            return Ok();
        }

        [ApiExplorerSettings(IgnoreApi = true)]
        [HangfireJobExpirationTimeout(timeoutInMinutes: 60)]
        [AutomaticRetry(
            Attempts = 10,
            OnAttemptsExceeded = AttemptsExceededAction.Fail,
            DelaysInSeconds =
            [
                60, // 1 minute
                120, // 2 minutes
                240, // 4 minutes
                480, // 8 minutes
                960, // 16 minutes
                1920, // 32 minutes
                3600, // 1 hour
                7200, // 2 hours
                14400, // 4 hours
                28800, // 8 hours
            ])]
        public async Task HandleWechatWebhook(string companyId, string xmlString, WeChatMessage receivedMessage)
        {
            try
            {
                var company = await _appDbContext.CompanyCompanies.Include(x => x.WeChatConfig)
                    .FirstOrDefaultAsync(x => x.Id == companyId);

                var isFromMe = receivedMessage.FromUserName == company.WeChatConfig.WebChatId;
                var openId = isFromMe ? receivedMessage.ToUserName : receivedMessage.FromUserName;

                var (conversation, myLock) = await _conversationResolver.GetConversationForReceivingMessageAsync(
                    companyId,
                    ChannelTypes.Wechat,
                    company.WeChatConfig.ChannelIdentityId,
                    openId,
                    null);

                var conversationMessage = new ConversationMessage()
                {
                    MessageContent = receivedMessage.Content,
                    Channel = ChannelTypes.Wechat,
                    WeChatSender = isFromMe ? null : conversation.WeChatUser,
                    WeChatReceiver = isFromMe ? conversation.WeChatUser : null,
                    MessageType = "text",
                    MessageUniqueID = receivedMessage.MsgId,
                };

                try
                {
                    var timestamp = long.Parse(receivedMessage.CreateTime);
                    var datetime = MyDateTimeUtil.UnixTimeStampToDateTimeUTC(timestamp * 1000);
                    conversationMessage.Timestamp = timestamp;
                    conversationMessage.CreatedAt = datetime;
                    conversationMessage.UpdatedAt = datetime;
                }
                catch (Exception e)
                {
                    _logger.LogError(e, "Failed to parse timestamp from WeChat message");
                }

                switch (receivedMessage.MsgType)
                {
                    case "text":
                        var result = await _messagingService.SendMessage(conversation, conversationMessage);
                        break;
                    case "image":
                    case "voice":
                        conversationMessage.MessageType = "file";
                        conversationMessage.MessageContent = receivedMessage.Content;
                        var fileURLs = new List<FileURLMessage>();

                        if (string.IsNullOrEmpty(company.WeChatConfig.AccessToken) ||
                            DateTime.UtcNow > company.WeChatConfig.TokenExpireAt)
                        {
                            var httpClient = _httpClientFactory.CreateClient(HttpClientHandlerName.Default);

                            var getAccessTokenResponse = await httpClient.GetStringAsync(
                                $"https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid={company.WeChatConfig.AppId}&secret={company.WeChatConfig.AppSecret}");
                            var accessToken =
                                JsonConvert.DeserializeObject<AccessTokenResponse>(getAccessTokenResponse);
                            company.WeChatConfig.AccessToken = accessToken.access_token;
                            company.WeChatConfig.TokenExpireAt =
                                DateTime.UtcNow.AddSeconds(accessToken.expires_in - 30);
                            await _appDbContext.SaveChangesAsync();
                        }

                        fileURLs.Add(
                            new FileURLMessage()
                            {
                                FileName = receivedMessage.MediaId,
                                FileURL = (receivedMessage.MsgType == "voice")
                                    ? $"https://api.weixin.qq.com/cgi-bin/media/get?access_token={company.WeChatConfig.AccessToken}&media_id={receivedMessage.MediaId}"
                                    : receivedMessage.PicUrl,
                                MIMEType = (receivedMessage.MsgType == "voice")
                                    ? $"audio/{receivedMessage.Format}"
                                    : "image/jpeg"
                            });

                        var fileresult = await _messagingService.SendFileMessageByFBURL(
                            conversation,
                            conversationMessage,
                            fileURLs);

                        break;
                    default:
                        return;
                }

                await _lockService.ReleaseLockAsync(myLock);
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Company {CompanyId} WeChat webhook error: {ExceptionMessage}",
                    companyId,
                    ex.Message);
                throw;
            }
        }

        [HttpGet]
        [Route("wechat/ExperimentalWebhook/{companyId}")]
        public IActionResult ExperimentalAuthenticate(string companyId, [FromQuery] WxSignatureParameter signatureParameter)
        {
            try
            {
                _logger.LogInformation(
                    "WeChat verifying Company {CompanyId} Experimental Connection with signature: {Signature}, timestamp: {Timestamp}, nonce: {Nonce}, echostr: {Echostr}",
                    companyId,
                    signatureParameter.Signature,
                    signatureParameter.Timestamp,
                    signatureParameter.Nonce,
                    signatureParameter.Echostr);

                if (CheckSignature(signatureParameter, companyId.Replace("-", string.Empty)))
                {
                    _logger.LogDebug("URL is valid from Company {CompanyId} Experimental Connection", companyId);
                    var echostr = signatureParameter.Echostr;
                    _logger.LogDebug("Return echostr for {CompanyId} from Experimental Connection: {echostr}", companyId, echostr);
                    return Ok(echostr);
                }

                _logger.LogInformation("Check Signature fail from Company {CompanyId} Experimental Connection", companyId);
                if (!string.IsNullOrEmpty(signatureParameter.Echostr))
                {
                    return Ok(signatureParameter.Echostr);
                }

                return BadRequest("Fail to authenticate");
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Company {CompanyId} WeChat authenticate error. {ExceptionMessage} from Experimental Connection",
                    companyId,
                    ex.Message);

                if (!string.IsNullOrEmpty(signatureParameter.Echostr))
                {
                    return Ok(signatureParameter.Echostr);
                }

                return BadRequest(ex.ToString());
            }
        }

        [HttpPost]
        [Route("wechat/ExperimentalWebhook/{companyId}")]
        public async Task<IActionResult> ReceiveExperimentalMessageWebhook(string companyId)
        {
            try
            {
                var xmlString = await new StreamReader(HttpContext.Request.Body).ReadToEndAsync();
                var serializer = new XmlSerializer(typeof(WeChatMessage));

                using var stringReader = new StringReader(xmlString);
                var receivedMessage = (WeChatMessage)serializer.Deserialize(stringReader);

                _logger.LogDebug("Action is [Reply]");

                _logger.LogInformation(
                    "WeChat webhook payload: {Payload} from Company {CompanyId} Experimental Connection",
                    JsonConvert.SerializeObject(receivedMessage), companyId);

                var company = await _appDbContext.CompanyCompanies.Include(x => x.WeChatConfig)
                    .FirstOrDefaultAsync(x => x.Id == companyId);

                var isFromMe = receivedMessage.FromUserName == company.WeChatConfig.WebChatId;
                var openId = isFromMe ? receivedMessage.ToUserName : receivedMessage.FromUserName;

                var (conversation, myLock) = await _conversationResolver.GetConversationForReceivingMessageAsync(
                    companyId,
                    ChannelTypes.Wechat,
                    company.WeChatConfig.ChannelIdentityId,
                    openId,
                    null);

                var conversationMessage = new ConversationMessage()
                {
                    MessageContent = receivedMessage.Content,
                    Channel = ChannelTypes.Wechat,
                    WeChatSender = isFromMe ? null : conversation.WeChatUser,
                    WeChatReceiver = isFromMe ? conversation.WeChatUser : null,
                    MessageType = "text",
                    MessageUniqueID = receivedMessage.MsgId
                };

                switch (receivedMessage.MsgType)
                {
                    case "text":
                        var result = await _messagingService.SendMessage(conversation, conversationMessage);
                        break;
                    case "image":
                    case "voice":
                        conversationMessage.MessageType = "file";
                        conversationMessage.MessageContent = receivedMessage.Content;
                        var fileURLs = new List<FileURLMessage>();

                        if (string.IsNullOrEmpty(company.WeChatConfig.AccessToken) ||
                            DateTime.UtcNow > company.WeChatConfig.TokenExpireAt)
                        {
                            var httpClient = _httpClientFactory.CreateClient(HttpClientHandlerName.Default);

                            var getAccessTokenResponse = await httpClient.GetStringAsync(
                                $"https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid={company.WeChatConfig.AppId}&secret={company.WeChatConfig.AppSecret}");
                            var accessToken =
                                JsonConvert.DeserializeObject<AccessTokenResponse>(getAccessTokenResponse);
                            company.WeChatConfig.AccessToken = accessToken.access_token;
                            company.WeChatConfig.TokenExpireAt =
                                DateTime.UtcNow.AddSeconds(accessToken.expires_in - 30);
                            await _appDbContext.SaveChangesAsync();
                        }

                        fileURLs.Add(
                            new FileURLMessage()
                            {
                                FileName = receivedMessage.MediaId,
                                FileURL = (receivedMessage.MsgType == "voice")
                                    ? $"https://api.weixin.qq.com/cgi-bin/media/get?access_token={company.WeChatConfig.AccessToken}&media_id={receivedMessage.MediaId}"
                                    : receivedMessage.PicUrl,
                                MIMEType = (receivedMessage.MsgType == "voice")
                                    ? $"audio/{receivedMessage.Format}"
                                    : "image/jpeg"
                            });

                        var fileresult = await _messagingService.SendFileMessageByFBURL(
                            conversation,
                            conversationMessage,
                            fileURLs);

                        break;

                    // var replyContent = string.Format("您輸入了：{0}", receivedMessage.Content);

                    // var replyXML = $"<xml><ToUserName><![CDATA[{receivedMessage.FromUserName}]]></ToUserName><FromUserName><![CDATA[{receivedMessage.ToUserName}]]></FromUserName><CreateTime>{DateTime.UtcNow.Ticks.ToString()}</CreateTime><MsgType><![CDATA[text]]></MsgType><Content><![CDATA[{replyContent}]]></Content></xml>";

                    // return Ok(replyXML);
                    default:
                        return BadRequest("Message type not support");
                }

                await _lockService.ReleaseLockAsync(myLock);
                return Ok();
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Company {CompanyId} WeChat webhook error: {ExceptionMessage} from Experimental Message Webhook",
                    companyId,
                    ex.Message);

                return BadRequest(ex.Message);
            }
        }

        private bool CheckSignature(WxSignatureParameter signatureParameter, string token)
        {
            string signature = signatureParameter.Signature;
            string timestamp = signatureParameter.Timestamp;
            string nonce = signatureParameter.Nonce;

            ArrayList list = new ArrayList
            {
                token, timestamp, nonce
            };
            list.Sort(new WxComparer());
            string raw = string.Empty;
            for (int i = 0; i < list.Count; ++i)
            {
                raw += list[i];
            }

            string hash = string.Empty;
            SHA1 sha = new SHA1CryptoServiceProvider();
            ASCIIEncoding enc = new ASCIIEncoding();
            byte[] dataToHash = enc.GetBytes(raw);
            byte[] dataHashed = sha.ComputeHash(dataToHash);
            hash = BitConverter.ToString(dataHashed).Replace("-", string.Empty);
            hash = hash.ToLower();

            return signature == hash;
        }
    }
}