﻿using Microsoft.AspNetCore.Identity;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using Sharprompt;
using Sleekflow.Core.DataMigrator.Constants;
using Sleekflow.Core.DataMigrator.Contexts;
using Sleekflow.Core.DataMigrator.Migrations;
using Sleekflow.Core.DataMigrator.MigrationScripts;
using Travis_backend.AccountAuthenticationDomain.Models;
using Travis_backend.AnalyticsDomain.Models;
using Travis_backend.AutomationDomain.Models;
using Travis_backend.BlastDomain.Models;
using Travis_backend.ChannelDomain.Models;
using Travis_backend.CommonDomain.Models;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.ContactDomain.Models;
using Travis_backend.ConversationDomain.Models;
using Travis_backend.ConversationServices.Models;
using Travis_backend.CoreDomain.Models;
using Travis_backend.Database;
using Travis_backend.DemoDomain.Models;
using Travis_backend.FileDomain.Models;
using Travis_backend.IntegrationServices.Models;
using Travis_backend.InternalDomain.Models;
using Travis_backend.MessageDomain.Models;
using Travis_backend.Models.ChatChannelConfig;
using Travis_backend.ResellerDomain.Models;
using Travis_backend.ShareInvitationDomain.Models;
using Travis_backend.SleekflowCrmHubDomain.Models;
using Travis_backend.StripeIntegrationDomain.Models;
using Travis_backend.SubscriptionPlanDomain.Models;
using Travis_backend.SupportTicketDomain.Models;
using static Sleekflow.Core.DataMigrator.Constants.Operations;
using static Sleekflow.Core.DataMigrator.Constants.MigrationOptions;

namespace Sleekflow.Core.DataMigrator;

public static class Program
{
    public static async Task<int> Main(string[] args)
    {

        var originEnvName = "OriginalConnectionStringProdHk";
        // var migrationEnvName = "MigrationConnectionStringProdUs";
        // var migrationEnvName = "MigrationConnectionStringProdSg";
        var migrationEnvName = "MigrationConnectionStringProdUaen";
        var originalDbContextOptions = InitDbContext(originEnvName);
        var migrationDbContextOptions = InitDbContext(migrationEnvName);
        var operation = Prompt.Select(
            "Select your operation",
            new[]
            {
                Exit,
                CoreMigrations,
                CompanyMigrations,
                SelectiveMigration
            });

        if (operation == Exit)
        {
            return 0;
        }

        var configuration = new Configurations(
            new OriginalContext(originalDbContextOptions),
            new MigrationContext(migrationDbContextOptions));

        if (operation == CoreMigrations)
        {
            var coreMigrationOperations = InitCoreMigrationOperation(configuration);
            TriggerMigrationOperations(coreMigrationOperations, string.Empty);
            return 0;
        }

        var companyId = Prompt.Input<string>("Please Input The Targeted Company Id ");

        var companyMigrationOperations = InitMigrationCompanyOperations(configuration);
        var nonCoreMigrationOperations = InitNonCoreMigrationOperations(configuration);

        var allMigrationOperations = companyMigrationOperations.Union(nonCoreMigrationOperations)
            .ToDictionary(x => x.Key, x => x.Value);

        if (configuration.DisableForeignKeyConstrain)
        {
            await DisableForeignKey(configuration);
        }

        switch (operation)
        {
            case CompanyMigrations:
                TriggerMigrationOperations(allMigrationOperations, companyId);
                var connectionString = Environment.GetEnvironmentVariable(originEnvName);
                var patchCompanyQuickReplyLinguals = new PatchCompanyQuickReplyLinguals(configuration);
                await patchCompanyQuickReplyLinguals.PatchQuickReplyIdAsync(connectionString, companyId);

                var patchLastMessageId = new PatchLastMessageId(configuration);
                await patchLastMessageId.PatchLastMessageIdAsync(companyId);
                break;
            case SelectiveMigration:
            {
                var option = Prompt.Select("Please Select an Table ", allMigrationOperations.Select(m => m.Key));
                if (!allMigrationOperations.TryGetValue(option, out var migration))
                {
                    Console.WriteLine($"Unable to Locate Table Information {option}");
                    return 0;
                }

                Console.WriteLine($"Initialize Selective Migration of {option}");
                var status = await migration.ExecuteAsync(companyId);
                Console.WriteLine($"Selective Migration Status {status}");
                break;
            }
        }

        if (configuration.DisableForeignKeyConstrain)
        {
            await EnableForeignKey(configuration);
        }

        return 0;
    }

    private static async Task DisableForeignKey(Configurations configurations)
    {
        var allTableNames = GetAllTableNames();
        foreach (var tableName in allTableNames)
        {
            await configurations.MigrationContext.Database.ExecuteSqlRawAsync(
                $"ALTER TABLE {tableName} NOCHECK CONSTRAINT ALL");
        }
    }

    private static async Task EnableForeignKey(Configurations configurations)
    {
        var allTableNames = MigrationOptions.GetAllTableNames();
        foreach (var tableName in allTableNames)
        {
            await configurations.MigrationContext.Database.ExecuteSqlRawAsync(
                $"ALTER TABLE {tableName} WITH CHECK CHECK CONSTRAINT ALL");
        }
    }

    private static DbContextOptions<ApplicationDbContext> InitDbContext(string environmentVariableName)
    {
        var connectionString = Environment.GetEnvironmentVariable(environmentVariableName);
        Console.WriteLine($"Connection String ({environmentVariableName}): \n{connectionString}");
        var optionBuilder = new DbContextOptionsBuilder<ApplicationDbContext>();
        optionBuilder.UseSqlServer(new SqlConnection(connectionString));
        return optionBuilder.Options;
    }

    private static void TriggerMigrationOperations(
        Dictionary<string, BaseMigration> migrationOperations,
        string companyId)
    {
        foreach (var (option, migration) in migrationOperations)
        {
            try
            {
                Console.WriteLine("============================");
                Console.WriteLine($"Initialize Migration of {option}");
                var status = migration.ExecuteAsync(companyId).Result;
                Console.WriteLine($"Migration Status {status}");
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
            }
        }
    }

    private static Dictionary<string, BaseMigration> InitCoreMigrationOperation(Configurations configurations)
    {
        return new Dictionary<string, BaseMigration>
        {
            {
                CoreEmailConfigs, new FullSingleTableMigration<CoreEmailConfig>(configurations)
            },
            {
                CoreSubscriptionPlans, new FullSingleTableMigration<SubscriptionPlan>(configurations)
            },
            {
                CoreTwilioTopupPlans, new FullSingleTableMigration<TwilioTopUpPlan>(configurations)
            },
            {
                CoreTwilioTopupRecords, new FullSingleTableMigration<TwilioTopupRecord>(configurations)
            },
            {
                CoreEmailNotificationTemplates, new FullSingleTableMigration<EmailNotificationTemplate>(configurations)
            },
            {
                CoreTwilioConfigs, new FullSingleTableMigration<CoreTwilioConfig>(configurations)
            },
            {
                CoreCustomFields, new FullSingleTableMigration<CoreCustomField>(configurations)
            },
            {
                CoreSandboxTwilioConfigs, new FullSingleTableMigration<CoreSandboxTwilioConfig>(configurations)
            },
            {
                CoreWhatsappRegistrationFiles, new FullSingleTableMigration<WhatsappRegistrationsFile>(configurations)
            },
            {
                CoreCountries, new FullSingleTableMigration<Country>(configurations)
            },
            {
                CoreWhatsApp360DialogPartnerAuthCredentials,
                new FullSingleTableMigration<CoreWhatsApp360DialogPartnerAuthCredential>(configurations)
            },
            {
                CoreResellerStripeTopUpOptions, new FullSingleTableMigration<ResellerStripeTopUpOption>(configurations)
            },
            {
                UserStaffNotificationSettings, new FullSingleTableMigration<StaffNotificationSetting>(configurations)
            },
            {
                AspNetRoles, new FullSingleTableMigration<IdentityRole>(configurations)
            },
        };
    }

    private static Dictionary<string, BaseMigration> InitMigrationCompanyOperations(Configurations configurations)
    {
        return new Dictionary<string, BaseMigration>
        {
            {
                UserRoleGuests, new PartialSingleTableMigrationWithCompanyId<Guest>(configurations)
            },
            {
                UserRoleAdmins, new PartialSingleTableMigrationWithCompanyId<Admin>(configurations)
            },
            {
                AspNetUsers, new AspNetUsersMigration(configurations)
            },
            {
                CompanyCompanies, new CompanyCompaniesMigration(configurations)
            },
            {
                UserRoleStaffs, new PartialSingleTableMigrationWithCompanyId<Staff>(configurations)
            },
            {
                AspNetUserRoles, new AspNetUserRolesMigration(configurations)
            },
            // {
            //     CompanyBillRecords, new PartialSingleTableMigrationWithCompanyId<BillRecord>(configurations)
            // },
            {
                CompanyBillRecords, new CompanyBillRecordsMigrationDuplicateKey(configurations)
            }
        };
    }

    private static Dictionary<string, BaseMigration> InitNonCoreMigrationOperations(Configurations configurations)
    {
        return new Dictionary<string, BaseMigration>
        {
            {
                CompanyStaffTeams, new PartialSingleTableMigrationWithCompanyId<CompanyTeam>(configurations)
            },
            {
                CompanyAssignmentRules, new PartialSingleTableMigrationWithCompanyId<AssignmentRule>(configurations)
            },
            {
                CompanyAutomationActions, new PartialSingleTableMigrationWithCompanyId<AutomationAction>(configurations)
            },
            {
                CompanySandboxes, new PartialSingleTableMigrationWithCompanyId<CompanySandbox>(configurations)
            },
            {
                CompanyAPIKeys, new PartialSingleTableMigrationWithCompanyId<CompanyAPIKey>(configurations)
            },
            {
                CompanyRequestChannels, new PartialSingleTableMigrationWithCompanyId<RequestChannel>(configurations)
            },
            {
                CompanyIconFiles, new PartialSingleTableMigrationWithCompanyId<CompanyIconFile>(configurations)
            },
            {
                CompanyShareableInvitations,
                new PartialSingleTableMigrationWithCompanyId<ShareableInvitation>(configurations)
            },
            {
                CompanyRolePermissions, new PartialSingleTableMigrationWithCompanyId<RolePermission>(configurations)
            },
            {
                CompanyTwilioUsageRecords,
                new PartialSingleTableMigrationWithCompanyId<TwilioUsageRecord>(configurations)
            },
            {
                CompanyAnalyticSegment, new PartialSingleTableMigrationWithCompanyId<Segment>(configurations)
            },
            {
                CompanyCustomUserProfileFields,
                new PartialSingleTableMigrationWithCompanyId<CompanyCustomUserProfileField>(configurations)
            },
            {
                UserUserDevices, new PartialSingleTableMigrationWithCompanyId<UserDevice>(configurations)
            },
            {
                ConfigInstagramConfigs, new PartialSingleTableMigrationWithCompanyId<InstagramConfig>(configurations)
            },
            {
                ConfigStorageConfigs, new PartialSingleTableMigrationWithCompanyId<StorageConfig>(configurations)
            },
            {
                ConfigDialogflowServiceAccountConfigs,
                new PartialSingleTableMigrationWithCompanyId<DialogflowServiceAccountConfig>(configurations)
            },
            {
                SenderWhatsApp360DialogSenders,
                new PartialSingleTableMigrationWithCompanyId<WhatsApp360DialogSender>(configurations)
            },
            {
                SenderInstagramSenders, new PartialSingleTableMigrationWithCompanyId<InstagramSender>(configurations)
            },
            {
                SenderSandboxSenders, new PartialSingleTableMigrationWithCompanyId<SandboxSender>(configurations)
            },
            {
                SenderSMSSenders, new PartialSingleTableMigrationWithCompanyId<SMSSender>(configurations)
            },
            {
                SenderFacebookSenders, new PartialSingleTableMigrationWithCompanyId<FacebookSender>(configurations)
            },
            {
                SenderWhatsappSenders, new PartialSingleTableMigrationWithCompanyId<WhatsAppSender>(configurations)
            },
            {
                SenderEmailSenders, new PartialSingleTableMigrationWithCompanyId<EmailSender>(configurations)
            },
            {
                SenderWebClientSenders, new PartialSingleTableMigrationWithCompanyId<WebClientSender>(configurations)
            },
            {
                SenderWeChatSenders, new PartialSingleTableMigrationWithCompanyId<WeChatSender>(configurations)
            },
            {
                SenderLineSender, new PartialSingleTableMigrationWithCompanyId<LineSender>(configurations)
            },
            {
                SenderViberSenders, new PartialSingleTableMigrationWithCompanyId<ViberSender>(configurations)
            },
            {
                SenderTelegramSenders, new PartialSingleTableMigrationWithCompanyId<TelegramSender>(configurations)
            },
            {
                UserProfiles, new PartialSingleTableMigrationWithCompanyId<UserProfile>(configurations)
            },
            {
                UserProfilePictureFiles,
                new PartialSingleTableMigrationWithCompanyId<UserProfilePictureFile>(configurations)
            },
            {
                UserProfileShopifyOrders,
                new PartialSingleTableMigrationWithCompanyId<ShopifyOrderRecord>(configurations)
            },
            {
                UserProfileShopifyAbandonedCarts,
                new PartialSingleTableMigrationWithCompanyId<ShopifyAbandonedCart>(configurations)
            },
            {
                Conversations, new PartialSingleTableMigrationWithCompanyId<Conversation>(configurations)
            },
            {
                WhatsappCloudApiSenders,
                new PartialSingleTableMigrationWithCompanyId<WhatsappCloudApiSender>(configurations)
            },
            {
                CoreCancelSubscriptionRecords,
                new PartialSingleTableMigrationWithCompanyId<CancelSubscriptionRecord>(configurations)
            },
            {
                CompanyNotificationRecords,
                new PartialSingleTableMigrationWithCompanyId<NotificationRecord>(configurations)
            },
            {
                CompanyImportContactHistories,
                new PartialSingleTableMigrationWithCompanyId<ImportContactHistory>(configurations)
            },
            {
                CompanyQuickReplies, new PartialSingleTableMigrationWithCompanyId<CompanyQuickReply>(configurations)
            },
            {
                CompanyQuickReplyFiles, new CompanyQuickReplyFilesMigration(configurations)
            },
            {
                AnalyticsRecords, new PartialSingleTableMigrationWithCompanyId<AnalyticsRecord>(configurations)
            },
            {
                CmsSalesPaymentRecords,
                new PartialSingleTableMigrationWithCompanyId<CmsSalesPaymentRecord>(configurations)
            },
            {
                CmsContactOwnerAssignLogs,
                new PartialSingleTableMigrationWithCompanyId<CmsContactOwnerAssignLog>(configurations)
            },
            {
                CmsWhatsappApplications,
                new PartialSingleTableMigrationWithCompanyId<CmsWhatsappApplication>(configurations)
            },
            {
                CmsLoginAsHistories, new PartialSingleTableMigrationWithCompanyId<CmsLoginAsHistory>(configurations)
            },
            {
                CmsCompanyAdditionalInfos,
                new PartialSingleTableMigrationWithCompanyId<CmsCompanyAdditionalInfo>(configurations)
            },
            {
                SupportTickets, new PartialSingleTableMigrationWithCompanyId<SupportTicket>(configurations)
            },
            {
                TwilioTopUpLogs, new PartialSingleTableMigrationWithCompanyId<TwilioTopUpLog>(configurations)
            },
            {
                CompanyPaymentFailedLogs,
                new PartialSingleTableMigrationWithCompanyId<CompanyPaymentFailedLog>(configurations)
            },
            {
                FbIgAutoReplyFiles, new FbIgAutoReplyFilesMigration(configurations)
            },
            {
                IcebreakerHistoryRecords,
                new PartialSingleTableMigrationWithCompanyId<IcebreakerHistoryRecord>(configurations)
            },
            {
                ConfigWhatsApp360DialogConfigs,
                new PartialSingleTableMigrationWithCompanyId<WhatsApp360DialogConfig>(configurations)
            },
            {
                WhatsApp360DialogTemplateBookmarks,
                new PartialSingleTableMigrationWithCompanyId<WhatsApp360DialogTemplateBookmark>(configurations)
            },
            {
                CompanyWhatsApp360DialogUsageRecords,
                new PartialSingleTableMigrationWithCompanyId<WhatsApp360DialogUsageRecord>(configurations)
            },
            {
                CompanyWhatsApp360DialogUsageTransactionLogs,
                new PartialSingleTableMigrationWithCompanyId<WhatsApp360DialogUsageTransactionLog>(
                    configurations)
            },
            {
                WhatsApp360DialogMediaFiles,
                new PartialSingleTableMigrationWithCompanyId<WhatsApp360DialogMediaFile>(configurations)
            },
            {
                CompanyWhatsapp360DialogTopUpConfigs,
                new PartialSingleTableMigrationWithCompanyId<CompanyWhatsapp360DialogTopUpConfig>(configurations)
            },
            {
                ResellerCompanyProfiles,
                new PartialSingleTableMigrationWithCompanyId<ResellerCompanyProfile>(configurations)
            },
            {
                StripeCompanyLogos, new FullSingleTableMigration<StripeCompanyLogo>(configurations)
            },
            {
                AnalyticsEmailNotificationConfigs,
                new PartialSingleTableMigrationWithCompanyId<AnalyticsEmailNotificationConfig>(configurations)
            },
            {
                BlastMessageConfigs, new PartialSingleTableMigrationWithCompanyId<BlastMessageConfig>(configurations)
            },
            {
                BlastMessageTemplates,
                new PartialSingleTableMigrationWithCompanyId<BlastMessageTemplate>(configurations)
            },
            {
                MigrationOptions.ChatHistoryBackupConfig,
                new PartialSingleTableMigrationWithCompanyId<ChatHistoryBackupConfig>(configurations)
            },
            {
                CompanyRegionalInfos, new PartialSingleTableMigrationWithCompanyId<CompanyRegionalInfo>(configurations)
            },
            {
                ConfigFacebookConfigs, new PartialSingleTableMigrationWithCompanyId<FacebookConfig>(configurations)
            },
            {
                ConfigLineConfigs, new PartialSingleTableMigrationWithCompanyId<LineConfig>(configurations)
            },
            {
                ConfigStripePaymentConfigs,
                new PartialSingleTableMigrationWithCompanyId<StripePaymentConfig>(configurations)
            },
            {
                ConfigViberConfigs, new PartialSingleTableMigrationWithCompanyId<ViberConfig>(configurations)
            },
            {
                ConfigWhatsappCloudApiConfigs,
                new PartialSingleTableMigrationWithCompanyId<WhatsappCloudApiConfig>(configurations)
            },
            {
                ConfigWhatsAppConfigs, new PartialSingleTableMigrationWithCompanyId<WhatsAppConfig>(configurations)
            },
            {
                ConfigSMSConfigs, new PartialSingleTableMigrationWithCompanyId<SMSConfig>(configurations)
            },
            {
                ConfigTelegramConfigs, new PartialSingleTableMigrationWithCompanyId<TelegramConfig>(configurations)
            },
            {
                ConfigShopifyConfigs, new PartialSingleTableMigrationWithCompanyId<ShopifyConfig>(configurations)
            },

            {
                DemoConversations, new PartialSingleTableMigrationWithCompanyId<DemoConversation>(configurations)
            },
            {
                DemoConversationMessages,
                new PartialSingleTableMigrationWithCompanyId<DemoConversationMessage>(configurations)
            },
            {
                ExtendedMessagePayloadFiles,
                new PartialSingleTableMigrationWithCompanyId<ExtendedMessagePayloadFile>(configurations)
            },
            {
                StripePaymentMessageTemplates,
                new PartialSingleTableMigrationWithCompanyId<StripePaymentMessageTemplate>(configurations)
            },
            {
                ImportWhatsappHistoryRecords,
                new PartialSingleTableMigrationWithCompanyId<ImportWhatsappHistoryRecord>(configurations)
            },
            {
                WhatsappCloudApiWabaConnections,
                new PartialSingleTableMigrationWithCompanyId<WhatsappCloudApiWabaConnection>(configurations)
            },
            {
                ShopifyProductRecords,
                new PartialSingleTableMigrationWithCompanyId<ShopifyProductRecord>(configurations)
            },
            {
                StripePaymentReportExportRecords,
                new PartialSingleTableMigrationWithCompanyId<StripePaymentReportExportRecord>(configurations)
            },
            {
                ShopifyCollectionRecords,
                new PartialSingleTableMigrationWithCompanyId<ShopifyCollectionRecord>(configurations)
            },
            {
                ShopifyProductMessageTemplates,
                new PartialSingleTableMigrationWithCompanyId<ShopifyProductMessageTemplate>(configurations)
            },
            {
                StripePaymentRecords, new PartialSingleTableMigrationWithCompanyId<StripePaymentRecord>(configurations)
            },
            {
                WhatsAppCloudApiTemplateBookmarks,
                new PartialSingleTableMigrationWithCompanyId<WhatsAppCloudApiTemplateBookmark>(configurations)
            },
            {
                WhatsappTemplateQuickReplyCallbacks,
                new PartialSingleTableMigrationWithCompanyId<WhatsappTemplateQuickReplyCallback>(configurations)
            },
            {
                IcebreakerReplyRules, new FullSingleTableMigration<IcebreakerReplyRule>(configurations)
            },
            {
                CmsCurrencyExchangeRates, new FullSingleTableMigration<CmsCurrencyExchangeRate>(configurations)
            },
            {
                Cms360DialogItemCosts, new FullSingleTableMigration<Cms360DialogItemCost>(configurations)
            },
            {
                FacebookOtnTopics, new FullSingleTableMigration<FacebookOTNTopic>(configurations)
            },
            {
                ConfigEmailConfigs, new FullSingleTableMigration<EmailConfig>(configurations)
            },
            {
                CompanyCustomUserProfileFieldOptions,
                new DoubleTablesMigration<CustomUserProfileFieldOption, CompanyCustomUserProfileField>(
                    configurations,
                    TableJoinInfo.GetTableJoinEntry(CompanyCustomUserProfileFieldOptions).JoinAttributeSelf,
                    TableJoinInfo.GetTableJoinEntry(CompanyCustomUserProfileFieldOptions).JoinAttributeOpponent)
            },
            {
                CustomSubscriptionPlanTranslationMaps,
                new CustomSubscriptionPlanTranslationMapsMigration(configurations)
            },
            {
                CompanyMessageTemplates,
                new PartialSingleTableMigrationWithCompanyId<CompanyMessageTemplate>(configurations)
            },
            {
                CampaignAutomationActions,
                new PartialSingleTableMigrationWithCompanyId<CampaignAutomationAction>(configurations)
            },
            {
                UserRegisteredSessions, new DoubleTablesMigration<RegisteredSession, Staff>(
                    configurations,
                    TableJoinInfo.GetTableJoinEntry(UserRegisteredSessions).JoinAttributeSelf,
                    TableJoinInfo.GetTableJoinEntry(UserRegisteredSessions).JoinAttributeOpponent)
            },
            {
                CompanyAssignmentUploadedFiles, new DoubleTablesMigration<AssignmentUploadedFile, AutomationAction>(
                    configurations,
                    TableJoinInfo.GetTableJoinEntry(CompanyAssignmentUploadedFiles).JoinAttributeSelf,
                    TableJoinInfo.GetTableJoinEntry(CompanyAssignmentUploadedFiles).JoinAttributeOpponent)
            },
            {
                CompanyTeamMembers, new DoubleTablesMigration<TeamMember, Staff>(
                    configurations,
                    TableJoinInfo.GetTableJoinEntry(CompanyTeamMembers).JoinAttributeSelf,
                    TableJoinInfo.GetTableJoinEntry(CompanyTeamMembers).JoinAttributeOpponent)
            },
            {
                CompanyShareableInvitationRecords, new DoubleTablesMigration<ShareableInvitationRecord, Staff>(
                    configurations,
                    TableJoinInfo.GetTableJoinEntry(CompanyShareableInvitationRecords).JoinAttributeSelf,
                    TableJoinInfo.GetTableJoinEntry(CompanyShareableInvitationRecords).JoinAttributeOpponent)
            },
            {
                BroadcastCompaignHistories, new DoubleTablesMigration<BroadcastHistory, CompanyMessageTemplate>(
                    configurations,
                    TableJoinInfo.GetTableJoinEntry(BroadcastCompaignHistories).JoinAttributeSelf,
                    TableJoinInfo.GetTableJoinEntry(BroadcastCompaignHistories).JoinAttributeOpponent)
            },
            {
                CampaignUploadedFiles, new DoubleTablesMigration<CampaignUploadedFile, CompanyMessageTemplate>(
                    configurations,
                    TableJoinInfo.GetTableJoinEntry(CampaignUploadedFiles).JoinAttributeSelf,
                    TableJoinInfo.GetTableJoinEntry(CampaignUploadedFiles).JoinAttributeOpponent)
            },
            {
                CampaignChannelMessages, new DoubleTablesMigration<CampaignChannelMessage, CompanyMessageTemplate>(
                    configurations,
                    TableJoinInfo.GetTableJoinEntry(CampaignChannelMessages).JoinAttributeSelf,
                    TableJoinInfo.GetTableJoinEntry(CampaignChannelMessages).JoinAttributeOpponent)
            },
            {
                CompanyCustomUserProfileFieldLinguals,
                new DoubleTablesMigration<CustomUserProfileFieldLingual, CompanyCustomUserProfileField>(
                    configurations,
                    TableJoinInfo.GetTableJoinEntry(CompanyCustomUserProfileFieldLinguals).JoinAttributeSelf,
                    TableJoinInfo.GetTableJoinEntry(CompanyCustomUserProfileFieldLinguals).JoinAttributeOpponent)
            },
            {
                CompanyCompanyCustomFields,
                new PartialSingleTableMigrationWithCompanyId<CompanyCustomField>(configurations)
            },
            {
                CompanyCustomFieldFieldLinguals,
                new DoubleTablesMigration<CompanyCustomFieldFieldLingual, CompanyCustomField>(
                    configurations,
                    TableJoinInfo.GetTableJoinEntry(CompanyCustomFieldFieldLinguals).JoinAttributeSelf,
                    TableJoinInfo.GetTableJoinEntry(CompanyCustomFieldFieldLinguals).JoinAttributeOpponent)
            },
            {
                UserProfileCustomFields,
                new PartialSingleTableMigrationWithCompanyId<UserProfileCustomField>(configurations)
            },
            {
                CrmHubEntities, new DoubleTablesMigration<CrmHubEntity, UserProfile>(
                    configurations,
                    TableJoinInfo.GetTableJoinEntry(CrmHubEntities).JoinAttributeSelf,
                    TableJoinInfo.GetTableJoinEntry(CrmHubEntities).JoinAttributeOpponent)
            },
            {
                CmsSalesPaymentRecordFiles, new DoubleTablesMigration<CmsSalesPaymentRecordFile, CmsSalesPaymentRecord>(
                    configurations,
                    TableJoinInfo.GetTableJoinEntry(CmsSalesPaymentRecordFiles).JoinAttributeSelf,
                    TableJoinInfo.GetTableJoinEntry(CmsSalesPaymentRecordFiles).JoinAttributeOpponent)
            },
            {
                SupportTicketFiles, new DoubleTablesMigration<SupportTicketFile, SupportTicket>(
                    configurations,
                    TableJoinInfo.GetTableJoinEntry(SupportTicketFiles).JoinAttributeSelf,
                    TableJoinInfo.GetTableJoinEntry(SupportTicketFiles).JoinAttributeOpponent)
            },
            {
                ConversationAdditionalAssignees, new PartialSingleTableMigrationWithCompanyId<AdditionalAssignee>(configurations)
            },
            {
                CompanyDefinedHashtags, new PartialSingleTableMigrationWithCompanyId<CompanyHashtag>(configurations)
            },
            {
                ConversationHashtags, new PartialSingleTableMigrationWithCompanyId<ConversationHashtag>(configurations)
            },
            {
                CompanyImportedUserProfiles, new DoubleTablesMigration<ImportedUserProfile, UserProfile>(
                    configurations,
                    TableJoinInfo.GetTableJoinEntry(CompanyImportedUserProfiles).JoinAttributeSelf,
                    TableJoinInfo.GetTableJoinEntry(CompanyImportedUserProfiles).JoinAttributeOpponent)
            },
            {
                FbIgAutoReplies, new DoubleTablesMigration<FbIgAutoReply, AutomationAction>(
                    configurations,
                    TableJoinInfo.GetTableJoinEntry(FbIgAutoReplies).JoinAttributeSelf,
                    TableJoinInfo.GetTableJoinEntry(FbIgAutoReplies).JoinAttributeOpponent)
            },
            {
                FbIgIcebreakers, new DoubleTablesMigration<FbIgIcebreaker, AssignmentRule>(
                    configurations,
                    TableJoinInfo.GetTableJoinEntry(FbIgIcebreakers).JoinAttributeSelf,
                    TableJoinInfo.GetTableJoinEntry(FbIgIcebreakers).JoinAttributeOpponent)
            },
            {
                ConversationBookmarks, new DoubleTablesMigration<ConversationBookmark, Conversation>(
                    configurations,
                    TableJoinInfo.GetTableJoinEntry(ConversationBookmarks).JoinAttributeSelf,
                    TableJoinInfo.GetTableJoinEntry(ConversationBookmarks).JoinAttributeOpponent)
            },
            {
                ResellerClientCompanyProfiles,
                new DoubleTablesMigration<ResellerClientCompanyProfile, ResellerCompanyProfile>(
                    configurations,
                    TableJoinInfo.GetTableJoinEntry(ResellerClientCompanyProfiles).JoinAttributeSelf,
                    TableJoinInfo.GetTableJoinEntry(ResellerClientCompanyProfiles).JoinAttributeOpponent)
            },
            {
                ResellerStaffs, new DoubleTablesMigration<ResellerStaff, ResellerCompanyProfile>(
                    configurations,
                    TableJoinInfo.GetTableJoinEntry(ResellerStaffs).JoinAttributeSelf,
                    TableJoinInfo.GetTableJoinEntry(ResellerStaffs).JoinAttributeOpponent)
            },
            {
                ResellerTransactionLogs, new DoubleTablesMigration<ResellerTransactionLog, ResellerCompanyProfile>(
                    configurations,
                    TableJoinInfo.GetTableJoinEntry(ResellerTransactionLogs).JoinAttributeSelf,
                    TableJoinInfo.GetTableJoinEntry(ResellerTransactionLogs).JoinAttributeOpponent)
            },
            {
                ResellerProfileLogos, new DoubleTablesMigration<ResellerProfileLogo, ResellerCompanyProfile>(
                    configurations,
                    TableJoinInfo.GetTableJoinEntry(ResellerProfileLogos).JoinAttributeSelf,
                    TableJoinInfo.GetTableJoinEntry(ResellerProfileLogos).JoinAttributeOpponent)
            },
            {
                ShopifyCollectionProductRecords,
                new DoubleTablesMigration<ShopifyCollectionProductRecord, ShopifyProductRecord>(
                    configurations,
                    TableJoinInfo.GetTableJoinEntry(ShopifyCollectionProductRecords).JoinAttributeSelf,
                    TableJoinInfo.GetTableJoinEntry(ShopifyCollectionProductRecords).JoinAttributeOpponent)
            },
            {
                SenderWebClientIPAddressInfos, new DoubleTablesMigration<IPAddressInfo, WebClientSender>(
                    configurations,
                    TableJoinInfo.GetTableJoinEntry(SenderWebClientIPAddressInfos).JoinAttributeSelf,
                    TableJoinInfo.GetTableJoinEntry(SenderWebClientIPAddressInfos).JoinAttributeOpponent)
            },
            {
                FacebookUserOneTimeTokens, new DoubleTablesMigration<FacebookUserOneTimeToken, FacebookSender>(
                    configurations,
                    TableJoinInfo.GetTableJoinEntry(FacebookUserOneTimeTokens).JoinAttributeSelf,
                    TableJoinInfo.GetTableJoinEntry(FacebookUserOneTimeTokens).JoinAttributeOpponent)
            },
            {
                ConfigWeChatConfigs, new DoubleTablesMigration<WeChatConfig, ProfilePictureFile>(
                    configurations,
                    TableJoinInfo.GetTableJoinEntry(ConfigWeChatConfigs).JoinAttributeSelf,
                    TableJoinInfo.GetTableJoinEntry(ConfigWeChatConfigs).JoinAttributeOpponent)
            },
            {
                CompanyQuickReplyLinguals, new DoubleTablesMigration<CompanyQuickReplyLingual, CompanyQuickReply>(
                    configurations,
                    TableJoinInfo.GetTableJoinEntry(CompanyQuickReplyLinguals).JoinAttributeSelf,
                    TableJoinInfo.GetTableJoinEntry(CompanyQuickReplyLinguals).JoinAttributeOpponent)
            },
            {
                TwilioTemplateBookmarkRecords, new DoubleTablesMigration<TwilioTemplateBookmarkRecord, WhatsAppConfig>(
                    configurations,
                    TableJoinInfo.GetTableJoinEntry(TwilioTemplateBookmarkRecords).JoinAttributeSelf,
                    TableJoinInfo.GetTableJoinEntry(TwilioTemplateBookmarkRecords).JoinAttributeOpponent)
            },
            {
                ConversationWhatsappHistories,
                new DoubleTablesMigration<ConversationWhatsappSenderHistory, Conversation>(
                    configurations,
                    TableJoinInfo.GetTableJoinEntry(ConversationWhatsappHistories).JoinAttributeSelf,
                    TableJoinInfo.GetTableJoinEntry(ConversationWhatsappHistories).JoinAttributeOpponent)
            },
            {
                ConversationMessages, new ConversationMessagesMigrationDuplicateKey(configurations)
            },

            {
                ExtendedMessagePayloads, new DoubleTablesMigration<ExtendedMessagePayload, ConversationMessage>(
                    configurations,
                    TableJoinInfo.GetTableJoinEntry(ExtendedMessagePayloads).JoinAttributeSelf,
                    TableJoinInfo.GetTableJoinEntry(ExtendedMessagePayloads).JoinAttributeOpponent)
            },
            {
                ConversationMessageUploadedFiles, new ConversationMessageUploadedFilesMigration(
                    configurations)
            },
            {
                MigrationOptions.Whatsapp360DialogExtendedMessagePayload,
                new DoubleTablesMigration<Whatsapp360DialogExtendedMessagePayload, ConversationMessage>(
                    configurations,
                    TableJoinInfo.GetTableJoinEntry(MigrationOptions.Whatsapp360DialogExtendedMessagePayload)
                        .JoinAttributeSelf,
                    TableJoinInfo.GetTableJoinEntry(MigrationOptions.Whatsapp360DialogExtendedMessagePayload)
                        .JoinAttributeOpponent)
            },
            // {
            //     ExtendedMessagePayloads, new ExtendedMessagePayloadsMigrationDuplicateKey(configurations)
            // },
            //
            // {
            //     ConversationMessageUploadedFiles, new ConversationMessageUploadedFilesMigrationDuplicateKey(configurations)
            // },
            {
                FbIgAutoReplyHistoryRecords, new FbIgAutoReplyHistoryRecordsMigration(configurations)
            },
            {
                CompanyCustomUserProfileFieldOptionLinguals,
                new CompanyCustomUserProfileFieldOptionLingualsMigration(configurations)
            },
            {
                CompanyAutomationHistories,
                new PartialSingleTableMigrationWithCompanyId<AutomationHistory>(configurations)
            },
            {
                CompanyAutomationActionRecords, new DoubleTablesMigration<AutomationActionRecord, Staff>(
                    configurations,
                    TableJoinInfo.GetTableJoinEntry(CompanyAutomationActionRecords).JoinAttributeSelf,
                    TableJoinInfo.GetTableJoinEntry(CompanyAutomationActionRecords).JoinAttributeOpponent)
            },
            // {
            //     ConversationUnreadRecords,
            //     new PartialSingleTableMigrationWithCompanyId<ConversationUnreadRecord>(configurations)
            // },
            {
                ConversationUnreadRecords,
                new ConversationUnreadRecordsMigrationDuplicateKey(configurations)
            },
        };
    }
}