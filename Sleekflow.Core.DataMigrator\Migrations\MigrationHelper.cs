﻿using System.Text;
using Microsoft.EntityFrameworkCore;
using Sleekflow.Core.DataMigrator.Contexts;
using Travis_backend.Extensions;

namespace Sleekflow.Core.DataMigrator.Migrations;

public class MigrationHelper<T> where T : class
{
    private readonly OriginalContext _context;
    private readonly MigrationContext _migrationContext;

    public MigrationHelper(OriginalContext context, MigrationContext migrationContext)
    {
        _context = context;
        _migrationContext = migrationContext;
    }

    public List<T> FilterResultItems(
        IEnumerable<T> originQueryable,
        HashSet<string> migratedIds,
        bool filterDuplicate,
        string companyId)
    {
        var results = new List<T>();
        var originList = originQueryable.ToList();

        Console.WriteLine("Start Filtering Items to Migrate");
        foreach (var originItem in originList)
        {
            var itemId = GetEntryId(originItem);
            var migrateFlag = true;
            if (!string.IsNullOrEmpty(companyId))
            {
                var itemCompanyId = GetCompanyId(originItem);
                migrateFlag = itemCompanyId == companyId;
            }

            if (filterDuplicate)
            {
                if (!string.IsNullOrEmpty(itemId) && !migratedIds.Contains(itemId) && migrateFlag)
                {
                    results.Add(originItem);
                }
            }
            else
            {
                if (!string.IsNullOrEmpty(itemId) && migrateFlag)
                {
                    results.Add(originItem);
                }
            }
        }

        return results;
    }

    public async Task<int> MigrationSaveChangesAsync(List<T> migrationItems, DbSet<T> dbSetMigration, MigrationContext migrationContext)
    {
        var resultBulk = new List<T>();
        var status = 0;
        var type = typeof(T);

        for (var i = 0; i < migrationItems.Count; i++)
        {
            resultBulk.Add(migrationItems[i]);
            if (i % 10 == 0 && i != 0)
            {
                await using var transaction = await migrationContext.Database.BeginTransactionAsync();
                try
                {

                    await dbSetMigration.AddRangeAsync(resultBulk);
                    resultBulk.Clear();
                    var entityEntries = _migrationContext.ChangeTracker.Entries().ToList();
                    foreach (var entityEntry in entityEntries.Where(
                                 entityEntry =>
                                     entityEntry.Entity.GetType() != type))
                    {
                        entityEntry.State = EntityState.Unchanged;
                    }

                    Console.WriteLine($"Finalize Saving {status} / {migrationItems.Count}");
                    var tableName = GetTableName(typeof(T));
                    if (BaseMigration.HasIdentity(tableName))
                    {
                        await migrationContext.Database.ExecuteSqlRawAsync($"SET IDENTITY_INSERT dbo.{tableName} ON");
                    }

                    status += await _migrationContext.SaveChangesAsync();
                    await transaction.CommitAsync();
                }
                catch (Exception e)
                {
                    Console.WriteLine(e);
                    var entityEntries = _migrationContext.ChangeTracker.Entries().ToList();
                    foreach (var entityEntry in entityEntries)
                    {
                        entityEntry.State = EntityState.Detached;
                    }
                }

            }
        }

        if (!resultBulk.IsNullOrEmpty())
        {
            await using var transaction = await migrationContext.Database.BeginTransactionAsync();
            await dbSetMigration.AddRangeAsync(resultBulk);
            resultBulk.Clear();
            var entityEntries = _migrationContext.ChangeTracker.Entries().ToList();
            foreach (var entityEntry in entityEntries.Where(
                         entityEntry =>
                             entityEntry.Entity.GetType() != type))
            {
                entityEntry.State = EntityState.Unchanged;
            }

            if (BaseMigration.HasIdentity(GetTableName(typeof(T))))
            {
                // Console.WriteLine($"Turning IDENTITY_INSERT on: {GetTableName(typeof(T))}");
                await migrationContext.Database.ExecuteSqlRawAsync($"SET IDENTITY_INSERT dbo.{GetTableName(typeof(T))} ON");
            }

            status += await _migrationContext.SaveChangesAsync();
            await transaction.CommitAsync();
        }

        return status;
    }


    public HashSet<string> FilterMigrationId(IEnumerable<T> migrationQueryable, string companyId)
    {
        var migratedIds = new HashSet<string>();
        foreach (var migratedItem in migrationQueryable)
        {
            var entryId = GetEntryId(migratedItem);
            if (companyId.IsNullOrEmpty() && !entryId.IsNullOrEmpty())
            {
                migratedIds.Add(entryId);
            }
            else if (!companyId.IsNullOrEmpty() && !entryId.IsNullOrEmpty())
            {
                var itemCompanyId = GetCompanyId(migratedItem);
                if (itemCompanyId == companyId)
                {
                    migratedIds.Add(entryId);
                }
            }
        }

        return migratedIds;
    }

    public string? GetTableName(Type t)
    {
        return _context.Model.FindEntityType(t)?.GetTableName();
    }

    public string GetAcronym(string input)
    {
        var acronymBuilder = new StringBuilder();

        foreach (var t in input.Where(char.IsUpper))
        {
            acronymBuilder.Append(t);
        }

        return acronymBuilder.ToString();
    }

    private static string GetEntryId(T item)
    {
        var value = item.GetType()?.GetProperty("Id")?.GetValue(item);
        return value switch
        {
            long l => l.ToString(),
            string s => s,
            _ => string.Empty
        };
    }

    private static string GetCompanyId(T item)
    {
        return (string) item.GetType()?.GetProperty("CompanyId")?.GetValue(item)!;
    }
}