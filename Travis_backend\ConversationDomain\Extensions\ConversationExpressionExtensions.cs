using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using LinqKit;
using Travis_backend.Constants;
using Travis_backend.ConversationDomain.Models;

namespace Travis_backend.ConversationDomain.Extensions;

public static class ConversationExpressionExtensions
{
    public static Expression<Func<Conversation, bool>> IsAssignedToAnyAssociatedTeam(
        this ExpressionStarter<Conversation> expressionStarter,
        IEnumerable<long> teamIds)
    {
        return conversation =>
            conversation.AssignedTeamId.HasValue && teamIds.Contains(conversation.AssignedTeamId.Value);
    }

    public static Expression<Func<Conversation, bool>> IsAssignedToTeam(
        this ExpressionStarter<Conversation> expressionStarter,
        long teamId)
    {
        return conversation => conversation.AssignedTeamId.HasValue && teamId == conversation.AssignedTeamId.Value;
    }

    public static Expression<Func<Conversation, bool>> HasTeamMemberAsCollaborator(
        this ExpressionStarter<Conversation> expressionStarter,
        IEnumerable<long> teamMemberIds)
    {
        return conversation => conversation.AdditionalAssignees != null &&
            conversation.AdditionalAssignees.Any(
                collaborator => teamMemberIds.Contains((long) collaborator.AssigneeId));
    }

    public static Expression<Func<Conversation, bool>> HasSpecificStaffAsCollaborator(
        this ExpressionStarter<Conversation> expressionStarter,
        long staffId)
    {
        return conversation => conversation.AdditionalAssignees != null &&
                               conversation.AdditionalAssignees.Any(
                                   collaborator => (long) collaborator.AssigneeId == staffId);
    }

    public static Expression<Func<Conversation, bool>> HasMentionedStaff(
        this ExpressionStarter<Conversation> expressionStarter,
        long staffId)
    {
        return conversation => conversation.ChatHistory != null &&
                               conversation.ChatHistory.Any(
                                   y => y.Channel == ChannelTypes.Note
                                        && y.MessageAssigneeId == staffId
                                        && y.UpdatedAt > DateTime.UtcNow.AddDays(-2));
    }

    public static Expression<Func<Conversation, bool>> IsAssignedToStaff(
        this ExpressionStarter<Conversation> expressionStarter,
        long staffId)
    {
        return conversation => conversation.AssigneeId == staffId;
    }

    public static Expression<Func<Conversation, bool>> IsUnassigned(
        this ExpressionStarter<Conversation> expressionStarter)
    {
        return conversation => !conversation.AssignedTeamId.HasValue && !conversation.AssigneeId.HasValue;
    }

    public static Expression<Func<Conversation, bool>> IsAssignedToTeamInbox(
        this ExpressionStarter<Conversation> expressionStarter)
    {
        return conversation => conversation.AssignedTeamId.HasValue && !conversation.AssigneeId.HasValue;
    }
}