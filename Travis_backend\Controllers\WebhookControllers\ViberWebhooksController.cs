﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Net.Http;
using System.Threading.Tasks;
using AutoMapper;
using Hangfire;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Polly;
using Travis_backend.BackgroundTaskServices.Attributes;
using Travis_backend.Cache;
using Travis_backend.ChannelDomain.ViewModels;
using Travis_backend.Constants;
using Travis_backend.ConversationDomain.ConversationResolver;
using Travis_backend.ConversationServices;
using Travis_backend.Database;
using Travis_backend.Enums;
using Travis_backend.Helpers;
using Travis_backend.MessageDomain.Models;
using Travis_backend.MessageDomain.ViewModels;
using Travis_backend.SignalR;

namespace Travis_backend.Controllers.WebhookControllers
{
    /// <summary>
    /// Viber Webhook.
    /// </summary>
    [Route("viber")]
    [Produces("application/json")]
    public class ViberWebhooksController : Controller
    {
        private readonly ILogger<ViberWebhooksController> _logger;
        private readonly IMapper _mapper;
        private readonly ApplicationDbContext _appDbContext;
        private readonly IConversationMessageService _conversationMessageService;
        private readonly ISignalRService _signalRService;
        private readonly IConversationResolver _conversationResolver;
        private readonly ILockService _lockService;

        public ViberWebhooksController(
            ILogger<ViberWebhooksController> logger,
            IMapper mapper,
            ApplicationDbContext appDbContext,
            IConversationMessageService conversationMessageService,
            ISignalRService signalRService,
            IConversationResolver conversationResolver,
            ILockService lockService)
        {
            _logger = logger;
            _mapper = mapper;
            _appDbContext = appDbContext;
            _conversationMessageService = conversationMessageService;
            _signalRService = signalRService;
            _conversationResolver = conversationResolver;
            _lockService = lockService;
        }

        /// <summary>
        /// Viber Webhook Api for receiving Viber bot callback.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        [HttpPost("webhook/{companyId}")]
        public async Task<IActionResult> ViberWebhook(
            [Required] [FromRoute]
            string companyId,
            [FromQuery]
            string viberBotId,
            [FromBody]
            ViberWebhookCallback input)
        {
            _logger.LogInformation(
                "ViberWebhook: from company: {CompanyId}, bot: {TelegramBotId}, webhook: {Webhook}",
                companyId,
                viberBotId,
                JsonConvert.SerializeObject(input));

            try
            {
                Policy.Handle<Exception>()
                    .WaitAndRetry(
                        3,
                        sleepDurationProvider: i => TimeSpan.FromMilliseconds(100),
                        onRetry: (exception, _, retryCount, _) =>
                        {
                            _logger.LogError(
                                exception,
                                "ViberWebhook: Enqueue error {ExceptionMessage} with retry {RetryCount}",
                                exception.Message,
                                retryCount);
                        })
                    .Execute(
                        () =>
                        {
                            BackgroundJob.Enqueue(() => HandleWebhook(companyId, viberBotId, input));
                        });
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "ViberWebhook Error: {ExceptionMessage}",
                    ex.Message);

                return BadRequest();
            }

            return Ok();
        }

        [ApiExplorerSettings(IgnoreApi = true)]
        [HangfireJobExpirationTimeout(timeoutInMinutes: 60)]
        [AutomaticRetry(
            Attempts = 10,
            OnAttemptsExceeded = AttemptsExceededAction.Fail,
            DelaysInSeconds =
            [
                60, // 1 minute
                120, // 2 minutes
                240, // 4 minutes
                480, // 8 minutes
                960, // 16 minutes
                1920, // 32 minutes
                3600, // 1 hour
                7200, // 2 hours
                14400, // 4 hours
                28800, // 8 hours
            ])]
        public async Task HandleWebhook(string companyId, string viberBotId, ViberWebhookCallback input)
        {
            var webhookCallback = MapCallbackToSpecificType(input);

            var viberConfig = await _appDbContext.ConfigViberConfigs.AsNoTracking()
                .FirstOrDefaultAsync(x => x.CompanyId == companyId && x.ViberBotId == viberBotId);

            if (viberConfig == null)
            {
                throw new Exception("Viber config not found.");
            }

            // For Viber webhook setup
            if (webhookCallback is ViberWebhookSetupCallback)
            {
                return;
            }

            switch (webhookCallback)
            {
                case ViberReceiveMessageCallback callback:
                {
                    var (conversation, myLock) = await _conversationResolver.GetConversationForReceivingMessageAsync(
                        companyId,
                        ChannelTypes.Viber,
                        viberBotId,
                        callback.Sender.Id,
                        callback.Sender.Name,
                        JsonConvert.SerializeObject(input));
                    var conversationMessage = new ConversationMessage()
                    {
                        CompanyId = companyId,
                        Channel = ChannelTypes.Viber,
                        ViberSender = conversation.ViberUser,
                        MessageType = "text",
                        MessageUniqueID = callback.MessageToken.ToString(),
                        Status = MessageStatus.Received,
                        DeliveryType = DeliveryType.Normal,
                        IsSentFromSleekflow = false
                    };

                    switch (callback.Message.Type)
                    {
                        case ViberMessageType.Text:
                            conversationMessage.MessageContent = callback.Message.Text;

                            await _conversationMessageService.SendMessage(conversation, conversationMessage);

                            break;

                        case ViberMessageType.Picture:
                            conversationMessage.MessageType = "file";
                            conversationMessage.MessageContent = callback.Message.Text;

                            var imageFiles = new List<FileURLMessage>();

                            imageFiles.Add(
                                new FileURLMessage()
                                {
                                    FileName =
                                        $"image_{DateTime.UtcNow}.jpg", // Pictures is always .jpg when viber user send to bot
                                    MIMEType = "image/jpeg",
                                    FileURL = callback.Message.Media
                                });

                            await _conversationMessageService.SendFileMessageByFBURL(
                                conversation,
                                conversationMessage,
                                imageFiles);

                            break;

                        case ViberMessageType.Video:
                        case ViberMessageType.File:
                            conversationMessage.MessageType = "file";

                            conversationMessage.MessageContent = callback.Message.Text;

                            var fileMessages = new List<FileURLMessage>();

                            fileMessages.Add(
                                new FileURLMessage()
                                {
                                    FileName = callback.Message.FileName,
                                    MIMEType = MimeTypeMap.GetMimeType(callback.Message.FileName),
                                    FileURL = callback.Message.Media
                                });

                            await _conversationMessageService.SendFileMessageByFBURL(
                                conversation,
                                conversationMessage,
                                fileMessages);

                            break;
                        case ViberMessageType.Location:
                            var locationMessage = $"Shared a Location:\n" +
                                                  $"https://maps.google.com/?q={callback.Message.Location.Lat},{callback.Message.Location.Lon}";

                            conversationMessage.MessageContent = locationMessage;

                            await _conversationMessageService.SendMessage(conversation, conversationMessage);
                            break;
                    }

                    await _lockService.ReleaseLockAsync(myLock);

                    break;
                }

                case ViberMessageDeliveredCallback callback:
                {
                    // _logger.LogInformation($"Webhook Event({callback.GetType().Name})");
                    var message = await _appDbContext.ConversationMessages
                        .Include(x => x.ViberSender)
                        .Include(x => x.ViberReceiver)
                        .Include(x => x.UploadedFiles)
                        .Where(x => x.CompanyId == companyId && x.MessageUniqueID == callback.MessageToken.ToString())
                        .FirstOrDefaultAsync();

                    if (message == null)
                    {
                        throw new Exception("Message not found.");
                    }

                    message.Status = MessageStatus.Received;
                    message.UpdatedAt = DateTime.UtcNow.ToUniversalTime();

                    await _appDbContext.SaveChangesAsync();
                    await _signalRService.SignalROnMessageStatusChanged(message);
                    break;
                }

                case ViberMessageSeenCallback callback:
                {
                    // _logger.LogInformation($"Webhook Event({callback.GetType().Name})");

                    // Seen All Messages
                    // var messages = await _appDbContext.ConversationMessages
                    //     .Where(x =>
                    //         x.IsSentFromSleekflow &&
                    //         x.Status != MessageStatus.Undelivered &&
                    //         x.CreatedAt <= _appDbContext.ConversationMessages
                    //             .Where(m => m.MessageUniqueID == callback.MessageToken.ToString())
                    //             .Select(m => m.CreatedAt).FirstOrDefault())
                    //     .ToListAsync();
                    var message = await _appDbContext.ConversationMessages
                        .Include(x => x.ViberSender)
                        .Include(x => x.ViberReceiver)
                        .Include(x => x.UploadedFiles)
                        .Where(x => x.CompanyId == companyId && x.MessageUniqueID == callback.MessageToken.ToString())
                        .FirstOrDefaultAsync();

                    if (message == null)
                    {
                        throw new Exception("Message not found.");
                    }

                    message.Status = MessageStatus.Read;
                    message.UpdatedAt = DateTime.UtcNow.ToUniversalTime();

                    await _appDbContext.SaveChangesAsync();
                    await _signalRService.SignalROnMessageStatusChanged(message);

                    break;
                }

                case ViberSendMessageFailedCallback callback:
                {
                    // _logger.LogInformation($"Webhook Event({callback.GetType().Name})");
                    var message = await _appDbContext.ConversationMessages
                        .Include(x => x.ViberSender)
                        .Include(x => x.ViberReceiver)
                        .Include(x => x.UploadedFiles)
                        .Where(x => x.CompanyId == companyId && x.MessageUniqueID == callback.MessageToken.ToString())
                        .FirstOrDefaultAsync();

                    if (message == null)
                    {
                        throw new Exception("Message not found.");
                    }

                    message.Status = MessageStatus.Failed;
                    message.UpdatedAt = DateTime.UtcNow.ToUniversalTime();

                    await _appDbContext.SaveChangesAsync();

                    await _signalRService.SignalROnMessageStatusChanged(message);

                    break;
                }

                case ViberSubscribedCallback callback:
                {
                    await _conversationResolver.GetConversationForReceivingMessageAsync(
                        companyId,
                        ChannelTypes.Viber,
                        viberBotId,
                        callback.User.Id,
                        callback.User.Name,
                        JsonConvert.SerializeObject(input));

                    break;
                }

                case ViberUnsubscribedCallback callback:
                {
                    _logger.LogInformation($"Webhook Event({callback.GetType().Name})");

                    await _conversationResolver.GetConversationForReceivingMessageAsync(
                        companyId,
                        ChannelTypes.Viber,
                        viberBotId,
                        callback.UserId,
                        null,
                        JsonConvert.SerializeObject(input));

                    break;
                }
            }
        }

        private IViberCallback MapCallbackToSpecificType(ViberWebhookCallback input)
        {
            return input.Event switch
            {
                ViberWebhookCallbackEventType.Message => _mapper.Map<ViberReceiveMessageCallback>(input),
                ViberWebhookCallbackEventType.Delivered => _mapper.Map<ViberMessageDeliveredCallback>(input),
                ViberWebhookCallbackEventType.Seen => _mapper.Map<ViberMessageSeenCallback>(input),
                ViberWebhookCallbackEventType.Failed => _mapper.Map<ViberSendMessageFailedCallback>(input),
                ViberWebhookCallbackEventType.Subscribed => _mapper.Map<ViberSubscribedCallback>(input),
                ViberWebhookCallbackEventType.Unsubscribed => _mapper.Map<ViberUnsubscribedCallback>(input),
                ViberWebhookCallbackEventType.ConversationStarted => _mapper.Map<ViberConversationStartedCallback>(
                    input),
                ViberWebhookCallbackEventType.Webhook => _mapper.Map<ViberWebhookSetupCallback>(input),
                _ => throw new ArgumentOutOfRangeException()
            };
        }
    }
}