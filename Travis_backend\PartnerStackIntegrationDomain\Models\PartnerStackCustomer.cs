using System;
using System.Collections.Generic;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Travis_backend.Helpers;

namespace Travis_backend.PartnerStackIntegrationDomain.Models;

public class PartnerStackCustomer
{
    [JsonProperty("created_at")]
    [JsonConverter(typeof(JsonConvertHelper.EpochConverter))]
    public DateTime CreatedAt { get; set; }

    [JsonProperty("key")]
    public string Key { get; set; }

    [JsonProperty("updated_at")]
    [JsonConverter(typeof(JsonConvertHelper.EpochConverter))]
    public DateTime UpdatedAt { get; set; }

    [JsonProperty("customer_key")]
    public string CustomerKey { get; set; }

    [JsonProperty("email")]
    public string Email { get; set; }

    [JsonProperty("field_data")]
    public JObject FieldData { get; set; }

    [JsonProperty("fields")]
    public List<PartnerStackField> Fields { get; set; }

    [JsonProperty("meta")]
    public JObject Meta { get; set; }

    [JsonProperty("name")]
    public string Name { get; set; }

    [JsonProperty("partner_key")]
    public string PartnerKey { get; set; }

    [JsonProperty("partnership_key")]
    public string PartnershipKey { get; set; }

    [JsonProperty("provider_key")]
    public string ProviderKey { get; set; }

    [JsonProperty("source_key")]
    public string SourceKey { get; set; }

    [JsonProperty("source_type")]
    public string SourceType { get; set; }

    [JsonProperty("test")]
    public bool Test { get; set; }
}