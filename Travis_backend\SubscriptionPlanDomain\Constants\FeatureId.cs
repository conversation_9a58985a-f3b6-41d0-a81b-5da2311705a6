namespace Travis_backend.SubscriptionPlanDomain.Constants;

public static class FeatureId
{
    public const string Agents = "agents";

    public const string Contacts = "contacts";

    public const string Channels = "channels";

    public const string ApiCalls = "api_calls";

    public const string Automations = "automations";

    public const string BroadcastMessages = "campaigns";

    public const string OnboardingSupport = "onboarding_support";

    public const string BusinessConsultancyService = "business_consultancy_service";

    public const string FlowBuilderMaxActiveWorkflowCount = "flow_builder_max_active_workflow_count";

    public const string FlowBuilderMaxNodesPerWorkflowCount = "flow_builder_max_nodes_per_workflow_count";

    public const string FlowBuilderFlowEnrolment = "flow_builder_flow_enrolment";

    public const string WhatsAppPhoneNumber = "whatsapp_phone_number";

    public const string CustomObject = "custom_object";

    public const string CustomCatalog = "custom_catalog";

    public const string StripePaymentIntegration = "payment_integration";

    public const string WhatsAppQrCode = "whatsapp_qr_code";

    public const string ShopifyIntegration = "shopify";

    public const string HubSpotIntegration = "hubspot";

    public const string SalesforceIntegration = "salesforce_integration";

    public const string SalesforceMarketingCloud = "salesforce_marketing_cloud";

    public const string SalesforceCommerceCloud = "salesforce_commerce_cloud";

    public const string SensitiveDateMasking = "sensitive_data_masking";

    public const string MicrosoftDynamics365Integration = "microsoft_dynamics365_integration";

    public const string FacebookLeadAds = "facebook_lead_ads";

    public const string MakeIntegration = "make_integration";

    public const string ZapierIntegration = "zapier_integration";

    public const string AI = "ai_features_total_usage";
}