using System.ComponentModel.DataAnnotations.Schema;
using Travis_backend.CommonDomain.Models;
using Travis_backend.CompanyDomain.Models;

namespace Travis_backend.InternalDomain.Models;

public class CmsCompanyAdditionalInfo : DateAuditedEntity<long>
{
    public string CompanyId { get; set; }

    [ForeignKey(nameof(CompanyId))]
    public Company Company { get; set; }

    public string ChurnReason { get; set; }

    public string CompanyTier { get; set; }

    public string HubSpotCompanyIndustry { get; set; }

    public CmsCompanyAllTimeRevenueAnalyticData AllTimeRevenueAnalyticData { get; set; }
}