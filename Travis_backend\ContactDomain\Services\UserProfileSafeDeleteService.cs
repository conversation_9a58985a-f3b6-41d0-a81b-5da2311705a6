#nullable enable
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Threading.Tasks;
using Hangfire;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Travis_backend.Cache;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.CompanyDomain.Services;
using Travis_backend.ContactDomain.Constants;
using Travis_backend.ContactDomain.Models;
using Travis_backend.Database;
using Travis_backend.Database.Services;
using Travis_backend.Enums;
using Travis_backend.Exceptions;
using Travis_backend.FlowHubs;
using Travis_backend.FlowHubs.Models;
using Travis_backend.Helpers;
using Travis_backend.SignalR;

namespace Travis_backend.ContactDomain.Services;

public interface IUserProfileSafeDeleteService
{
    /// <summary>
    /// Set UserProfile.ActiveStatus to Inactivate
    /// Set Conversation.ActiveStatus to Inactivate
    /// Push to UserProfileDeletionBuffer.
    /// </summary>
    /// <param name="staff">Deleted by.</param>
    /// <param name="companyId">Company Id.</param>
    /// <param name="userProfileIds">UserProfile Ids.</param>
    /// <param name="triggerContext">Trigger Context.</param>
    /// <returns>If succeed.</returns>
    Task<bool> SoftDeleteUserProfilesAsync(
        Staff? staff,
        string companyId,
        HashSet<string> userProfileIds,
        UserProfileDeletionTriggerContext triggerContext);

    /// <summary>
    /// Hard delete UserProfiles which have exceeded the date time of UserProfileDeletionBuffer.ScheduledHardDeleteAt.
    /// Utilize <see cref="IUserProfileService.DeleteUserProfileLinked"/>.
    /// </summary>
    /// <returns>Task.</returns>
    Task CleanUpExceededBufferPeriodUserProfilesAsync();

    /// <summary>
    /// Initiative hard delete the soft deleted UserProfiles.
    /// </summary>
    /// <param name="companyId">Company Id.</param>
    /// <param name="userProfileIds">UserProfile Ids.</param>
    /// <param name="context">UserProfileDeletionTriggerContext.</param>
    /// <returns>If succeed.</returns>
    Task<int> HardDeleteSoftDeletedUserProfilesAsync(
        string companyId,
        HashSet<string> userProfileIds,
        UserProfileDeletionTriggerContext context);

    Task<List<UserProfileDeletionBuffer>> GetSoftDeletedUserProfilesAsync(
        string companyId,
        int offset = 0,
        int limit = 20,
        string orderBy = "DeletedAt",
        string direction = "DESC");

    Task<List<string>> GetSoftDeletedUserProfileIdsAsync(string companyId);

    Task<long> CountSoftDeletedUserProfilesAsync(string companyId);

    Task<int> RecoverSoftDeletedUserProfilesAsync(
        string companyId,
        HashSet<string> userProfileIds,
        UserProfileRecoveryTriggerContext triggerContext);

    Task<ILockService.Lock> GetContactSafeDeletionConcurrentCallLockAsync(
        string companyId,
        string methodName);

    Task ReleaseContactSafeDeletionConcurrentCallLockAsync(
        ILockService.Lock @lock);
}

public class UserProfileSafeDeleteService : IUserProfileSafeDeleteService
{
    private readonly IDbContextService _dbContextService;
    private readonly ILogger<UserProfileSafeDeleteService> _logger;
    private readonly IContactDeletionConfig _contactDeletionConfig;
    private readonly ISignalRService _signalRService;
    private readonly IUserProfileHooks _userProfileHooks;
    private readonly ICompanyUsageService _companyUsageService;
    private readonly IServiceProvider _serviceProvider;
    private readonly ILockService _lockService;

    public UserProfileSafeDeleteService(
        IDbContextService dbContextService,
        ILogger<UserProfileSafeDeleteService> logger,
        IContactDeletionConfig contactDeletionConfig,
        ISignalRService signalRService,
        IUserProfileHooks userProfileHooks,
        ICompanyUsageService companyUsageService,
        IServiceProvider serviceProvider,
        ILockService lockService)
    {
        _dbContextService = dbContextService;
        _logger = logger;
        _contactDeletionConfig = contactDeletionConfig;
        _signalRService = signalRService;
        _userProfileHooks = userProfileHooks;
        _companyUsageService = companyUsageService;
        _serviceProvider = serviceProvider;
        _lockService = lockService;
    }

    public async Task<bool> SoftDeleteUserProfilesAsync(
        Staff? staff,
        string companyId,
        HashSet<string> userProfileIds,
        UserProfileDeletionTriggerContext triggerContext)
    {
        ValidateStaffHaveSafeDeletePermission(staff);

        var dbContext = _dbContextService.GetDbContext();
        foreach (var userProfileIdsChunk in userProfileIds.Chunk(200))
        {
            var deletableUserProfileIds = await GetDeletableUserProfileIds(dbContext, companyId, userProfileIdsChunk);

            _logger.LogInformation(
                "{ServiceName} Company {CompanyId}, staff {StaffIdentityId} of name {StaffUserName} is deleting user profiles: {UserProfileIds}",
                nameof(UserProfileSafeDeleteService),
                staff?.CompanyId,
                staff?.IdentityId,
                staff?.Identity?.UserName,
                JsonConvert.SerializeObject(deletableUserProfileIds));

            await dbContext.Conversations
                .Where(
                    x =>
                        x.CompanyId == companyId
                        && deletableUserProfileIds.Contains(x.UserProfileId))
                .ExecuteUpdateAsync(
                    conversation =>
                        conversation.SetProperty(c => c.ActiveStatus, ActiveStatus.Inactive));

            await dbContext.UserProfiles
                .Where(
                    x =>
                        x.CompanyId == companyId
                        && deletableUserProfileIds.Contains(x.Id))
                .ExecuteUpdateAsync(
                    profile =>
                        profile.SetProperty(p => p.ActiveStatus, ActiveStatus.Inactive));

            await InsertUserProfileDeletionBuffersAsync(deletableUserProfileIds, companyId, staff?.Id);

            // publish signalR & audit log
            BackgroundJob.Enqueue(
                () => ExecutePostSoftDeleteUserProfilesActionsAsync(
                    companyId,
                    deletableUserProfileIds,
                    triggerContext));
        }

        return true;
    }

    [AutomaticRetry(
        Attempts = 200,
        DelaysInSeconds = new[]
        {
            5
        })]
    public async Task CleanUpExceededBufferPeriodUserProfilesAsync()
    {
        var dbContext = _dbContextService.GetDbContext();
        dbContext.Database.SetCommandTimeout(180);

        while (true)
        {
            var now = DateTime.UtcNow;
            var toBeDeletedUserProfiles = await dbContext.UserProfileDeletionBuffers
                .Include(x => x.UserProfile)
                .Where(
                    x =>
                        now > x.ScheduledHardDeleteAt &&
                        x.UserProfile.ActiveStatus == ActiveStatus.Inactive)
                .OrderBy(x => x.ScheduledHardDeleteAt)
                .Take(500)
                .Select(x => x.UserProfile)
                .ToListAsync();

            if (!toBeDeletedUserProfiles.Any())
            {
                break;
            }

            var hardDeleteResult= await HardDeleteUserProfilesAsync(
                toBeDeletedUserProfiles,
                new UserProfileDeletionTriggerContext(UpdateUserProfileTriggerSource.ContactSafeDeletionCleaner, null));

            _logger.LogInformation(
                "Clean up result: {MethodName} {DeletedUserProfileIds}",
                nameof(CleanUpExceededBufferPeriodUserProfilesAsync),
                JsonConvert.SerializeObject(hardDeleteResult.DeletedUserProfileIds));

            if (hardDeleteResult.Exceptions.Any())
            {
                throw new AggregateException(hardDeleteResult.Exceptions);
            }
        }

        await RemoveRecoveredUserProfilesAsync();
    }

    public async Task<int> HardDeleteSoftDeletedUserProfilesAsync(
        string companyId,
        HashSet<string> userProfileIds,
        UserProfileDeletionTriggerContext context)
    {
        ValidateStaffHaveSafeDeletePermission(context.Staff);

        var dbContext = _dbContextService.GetDbContext();

        var deletedCount = 0;
        foreach (var userProfileIdsChunk in userProfileIds.Chunk(200))
        {
            var softDeletedUserProfiles = await dbContext.UserProfileDeletionBuffers
                .Include(x => x.UserProfile)
                .Where(
                    x =>
                        x.CompanyId == companyId &&
                        x.UserProfile.ActiveStatus == ActiveStatus.Inactive &&
                        userProfileIdsChunk.Contains(x.UserProfileId))
                .Select(x => x.UserProfile)
                .ToListAsync();

            var hardDeleteResult = await HardDeleteUserProfilesAsync(softDeletedUserProfiles, context);

            _logger.LogInformation(
                "{MethodName} Executed hard delete. Performer: {CompanyId} {StaffIdentityId} {StaffUserName}. Result: {UserProfileIds}",
                nameof(HardDeleteSoftDeletedUserProfilesAsync),
                companyId,
                context.Staff?.IdentityId,
                context.Staff?.Identity.DisplayName,
                JsonConvert.SerializeObject(hardDeleteResult.DeletedUserProfileIds));

            deletedCount += hardDeleteResult.DeletedUserProfileIds.Count;
        }

        return deletedCount;
    }

    public async Task<List<UserProfileDeletionBuffer>> GetSoftDeletedUserProfilesAsync(
        string companyId,
        int offset = 0,
        int limit = 20,
        string orderBy = "DeletedAt",
        string direction = "DESC")
    {
        var dbContext = _dbContextService.GetDbContext();

        Expression<Func<UserProfileDeletionBuffer, object>> orderByClause = orderBy switch
        {
            "FirstName" => s => s.UserProfile.FirstName,
            "LastName" => s => s.UserProfile.LastName,
            "FullName" => s => s.UserProfile.FullName,
            "PhoneNumber" => s => s.UserProfile.PhoneNumber,
            "Email" => s => s.UserProfile.Email,
            "DeletedBy" => s => s.DeletedByStaff.Identity.DisplayName,
            "DeletedAt" => s => s.DeletedAt,
            "ScheduledHardDeleteAt" => s => s.ScheduledHardDeleteAt,
            _ => throw new NotSupportedException($"Not supported sorting field: {orderBy}")
        };

        var query = dbContext.UserProfileDeletionBuffers
            .Where(x =>
                x.CompanyId == companyId &&
                x.UserProfile.ActiveStatus == ActiveStatus.Inactive);

        query = direction.ToUpper() == "ASC"
            ? query.OrderBy(orderByClause)
            : query.OrderByDescending(orderByClause);

        var userProfileDeletionBuffers = await query
            .Skip(offset)
            .Take(limit)
            .Include(x => x.UserProfile)
            .Include(x => x.DeletedByStaff)
            .ThenInclude(s => s.Identity)
            .ToListAsync();

        return userProfileDeletionBuffers;
    }

    public async Task<List<string>> GetSoftDeletedUserProfileIdsAsync(string companyId)
    {
        var dbContext = _dbContextService.GetDbContext();

        return await dbContext.UserProfileDeletionBuffers
            .Where(
                x =>
                    x.CompanyId == companyId &&
                    x.UserProfile.ActiveStatus == ActiveStatus.Inactive)
            .Select(x => x.UserProfileId)
            .ToListAsync();
    }

    public async Task<long> CountSoftDeletedUserProfilesAsync(string companyId)
    {
        var dbContext = _dbContextService.GetDbContext();

        return await dbContext.UserProfileDeletionBuffers.Where(
                x =>
                    x.CompanyId == companyId &&
                    x.UserProfile.ActiveStatus == ActiveStatus.Inactive)
            .CountAsync();
    }

    public async Task<int> RecoverSoftDeletedUserProfilesAsync(
        string companyId,
        HashSet<string> userProfileIds,
        UserProfileRecoveryTriggerContext triggerContext)
    {
        var dbContext = _dbContextService.GetDbContext();

        var companyUsage = await _companyUsageService.GetCompanyUsage(companyId);
        var recoverCapability = int.Max(0, companyUsage.MaximumContacts - companyUsage.totalContacts);
        if (recoverCapability < userProfileIds.Count)
        {
            userProfileIds = userProfileIds.Take(recoverCapability).ToHashSet();
        }

        var recoveredCount = 0;
        foreach (var userProfileIdsChunk in userProfileIds.Chunk(200))
        {
            try
            {
                var recoverableUserProfileIds =
                    await GetRecoverableUserProfileIdsAsync(dbContext, companyId, userProfileIdsChunk);

                await dbContext.Conversations
                    .Where(
                        x =>
                            x.CompanyId == companyId
                            && recoverableUserProfileIds.Contains(x.UserProfileId))
                    .ExecuteUpdateAsync(
                        conversation =>
                            conversation.SetProperty(c => c.ActiveStatus, ActiveStatus.Active));

                await dbContext.UserProfiles
                    .Where(
                        x =>
                            x.CompanyId == companyId
                            && recoverableUserProfileIds.Contains(x.Id))
                    .ExecuteUpdateAsync(
                        profile =>
                            profile.SetProperty(p => p.ActiveStatus, ActiveStatus.Active));

                await dbContext.UserProfileDeletionBuffers
                    .Where(x =>
                        x.CompanyId == companyId &&
                        recoverableUserProfileIds.Contains(x.UserProfileId))
                    .ExecuteDeleteAsync();

                await ExecutePostRecoverUserProfilesActionsAsync(
                    companyId,
                    recoverableUserProfileIds.ToList(),
                    triggerContext);

                recoveredCount += recoverableUserProfileIds.Count;
            }
            catch (Exception e)
            {
                _logger.LogError(
                    e,
                    "Error when recover soft deleted UserProfiles. {CompanyId} {UserProfileIds}",
                    companyId,
                    JsonConvert.SerializeObject(userProfileIdsChunk));
            }
        }

        return recoveredCount;
    }

    public async Task<ILockService.Lock> GetContactSafeDeletionConcurrentCallLockAsync(
        string companyId,
        string methodName)
    {
        var lockKey = $"{companyId}_{methodName}_concurrent_lock_key";

        var @lock = await _lockService.AcquireLockAsync(lockKey, TimeSpan.FromMinutes(10));
        if (@lock == null)
        {
            throw new ContactSafeDeletionConcurrentCallException($"Concurrent call of {methodName} is not allowed.");
        }

        return @lock;
    }

    public async Task ReleaseContactSafeDeletionConcurrentCallLockAsync(
        ILockService.Lock @lock)
    {
        await _lockService.ReleaseLockAsync(@lock);
    }

    private void ValidateStaffHaveSafeDeletePermission(Staff? staff)
    {
        if (staff == null)
        {
            return;
        }

        if (staff.RoleType == StaffUserRole.Staff)
        {
            throw new Exception("Unable to delete, insufficient permissions.");
        }

        if (staff.CompanyId == "a784fd97-3a7a-47e6-ae44-480dadf641f7")
        {
            throw new Exception("Contact delete is disabled for this company");
        }

        // COSMAX & VITAE
        if (staff.CompanyId is "4c543d7e-bb9c-43f0-87b7-cb9fe90870b0"
                or "820a5a3f-1f6a-4d58-8247-cb8b81655c80"
            && staff.RoleType == StaffUserRole.TeamAdmin)
        {
            throw new Exception("Unable to delete, insufficient permissions.");
        }
    }

    private async Task<(List<string> DeletedUserProfileIds, List<Exception> Exceptions)>
        HardDeleteUserProfilesAsync(List<UserProfile> userProfiles, UserProfileDeletionTriggerContext context)
    {
        var maxDegreeOfParallelism =
            context.TriggerSource == UpdateUserProfileTriggerSource.ContactSafeDeletionCleaner ? 8 : 3;

        var deletedUserProfileIds = new ConcurrentBag<string>();
        var exceptions = new ConcurrentQueue<Exception>();

        await Parallel.ForEachAsync(
            userProfiles,
            new ParallelOptions
            {
                MaxDegreeOfParallelism = maxDegreeOfParallelism
            },
            async (userProfile, _) =>
            {
                try
                {
                    using var scope = _serviceProvider.CreateScope();
                    var userProfileService = scope.ServiceProvider.GetRequiredService<IUserProfileService>();

                    // batch publish audit logs later
                    await userProfileService.DeleteUserProfileLinked(userProfile, context, false);

                    deletedUserProfileIds.Add(userProfile.Id);
                }
                catch (Exception e)
                {
                    _logger.LogError(
                        e,
                        "Errors when {MethodName}. {UserProfileId}",
                        nameof(HardDeleteUserProfilesAsync),
                        userProfile.Id);

                    exceptions.Enqueue(e);
                }
            });

        await _dbContextService.GetDbContext().UserProfileDeletionBuffers
            .Where(x => deletedUserProfileIds.Contains(x.UserProfileId))
            .ExecuteDeleteAsync();

        return (deletedUserProfileIds.ToList(), exceptions.ToList());
    }

    public async Task ExecutePostSoftDeleteUserProfilesActionsAsync(
        string companyId,
        List<string> userProfileIds,
        UserProfileDeletionTriggerContext triggerContext)
    {
        var dbContext = _dbContextService.GetDbContext();

        var company = await dbContext.CompanyCompanies
            .FirstAsync(x => x.Id == companyId);

        foreach (var userProfileId in userProfileIds)
        {
            var userProfile = await dbContext.UserProfiles
                .FirstOrDefaultAsync(x => x.Id == userProfileId);

            var conversation = await dbContext.Conversations
                .FirstOrDefaultAsync(x => x.UserProfileId == userProfileId);

            if (userProfile == null || conversation == null)
            {
                userProfileIds.Remove(userProfileId);
                return;
            }

            await _signalRService.SignalROnUserProfileDeleted(
                conversation,
                userProfile,
                company);
        }

        await _userProfileHooks.OnUserProfilesSoftDeletedAsync(
            companyId,
            userProfileIds,
            triggerContext.Staff?.IdentityId,
            () => Task.FromResult(new OnUserProfilesSoftDeletedData(
                triggerContext.Staff?.MapToStaffOverView(),
                triggerContext.TriggerSource)));
    }

    private async Task ExecutePostRecoverUserProfilesActionsAsync(
        string companyId,
        List<string> userProfileIds,
        UserProfileRecoveryTriggerContext triggerContext)
    {
        if (userProfileIds.Count == 0)
        {
            return;
        }

        await _userProfileHooks.OnUserProfilesRecoveredAsync(
            companyId,
            userProfileIds.ToList(),
            triggerContext.Staff?.IdentityId,
            () => Task.FromResult(new OnUserProfilesRecoveredData(
                triggerContext.Staff?.MapToStaffOverView(),
                triggerContext.TriggerSource)));
    }

    private async Task ExecutePostHardDeleteUserProfilesActionsAsync(
        string companyId,
        List<string> userProfileIds,
        UserProfileDeletionTriggerContext triggerContext)
    {
        if (userProfileIds.Count == 0)
        {
            return;
        }

        await _userProfileHooks.OnUserProfilesDeletedAsync(
            companyId,
            userProfileIds.ToList(),
            triggerContext.Staff?.IdentityId,
            () => Task.FromResult(new OnUserProfilesDeletedData(
                triggerContext.Staff?.MapToStaffOverView(),
                triggerContext.TriggerSource)));
    }

    private async Task RemoveRecoveredUserProfilesAsync()
    {
        var dbContext = _dbContextService.GetDbContext();
        dbContext.Database.SetCommandTimeout(180);

        while (true)
        {
            var recoveredUserProfileIds = await dbContext.UserProfileDeletionBuffers
                .Where(x =>
                    x.UserProfile.ActiveStatus == ActiveStatus.Active)
                .OrderBy(x => x.Id)
                .Take(500)
                .Select(x => x.UserProfileId)
                .ToListAsync();

            if (!recoveredUserProfileIds.Any())
            {
                break;
            }

            _logger.LogInformation(
                "Start {MethodName}: {UserProfileIds}",
                nameof(RemoveRecoveredUserProfilesAsync),
                JsonConvert.SerializeObject(recoveredUserProfileIds));

            try
            {
                await dbContext.Conversations
                    .Where(x => recoveredUserProfileIds.Contains(x.UserProfileId))
                    .ExecuteUpdateAsync(
                        conversation =>
                            conversation.SetProperty(c => c.ActiveStatus, ActiveStatus.Active));

                await dbContext.UserProfiles
                    .Where(x => recoveredUserProfileIds.Contains(x.Id))
                    .ExecuteUpdateAsync(
                        profile =>
                            profile.SetProperty(p => p.ActiveStatus, ActiveStatus.Active));

                await dbContext.UserProfileDeletionBuffers
                    .Where(x => recoveredUserProfileIds.Contains(x.UserProfileId))
                    .ExecuteDeleteAsync();
            }
            catch (Exception e)
            {
                _logger.LogError(
                    e,
                    "Errors when {MethodName}: {UserProfileIds}",
                    nameof(RemoveRecoveredUserProfilesAsync),
                    JsonConvert.SerializeObject(recoveredUserProfileIds));

                break;
            }
        }
    }

    private static async Task<List<string>> GetDeletableUserProfileIds(BaseDbContext dbContext, string companyId, ICollection<string> userProfileIds)
    {
        return await dbContext.UserProfiles
            .Where(
                profile =>
                    profile.CompanyId == companyId &&
                    profile.ActiveStatus == ActiveStatus.Active &&
                    userProfileIds.Contains(profile.Id))
            .Select(profile => profile.Id)
            .ToListAsync();
    }

    private async Task<List<string>> GetRecoverableUserProfileIdsAsync(BaseDbContext dbContext, string companyId, ICollection<string> userProfileIds)
    {
        return await dbContext.UserProfileDeletionBuffers
            .Where(
                buffer =>
                    buffer.CompanyId == companyId &&
                    userProfileIds.Contains(buffer.UserProfileId))
            .Select(buffer => buffer.UserProfileId)
            .ToListAsync();
    }

    private async Task InsertUserProfileDeletionBuffersAsync(List<string> userProfileIds, string companyId, long? staffId)
    {
        var dbContext = _dbContextService.GetDbContext();

        var deletedAt = DateTime.UtcNow;
        var scheduledPermanentDeleteAt = deletedAt.AddDays(_contactDeletionConfig.DeleteBufferDays);

        var existedUserProfileIds = await dbContext.UserProfileDeletionBuffers
            .Where(
                x =>
                    x.CompanyId == companyId &&
                    userProfileIds.Contains(x.UserProfileId))
            .Select(x => x.UserProfileId)
            .ToListAsync();

        var toBeAddedUserProfileIds = userProfileIds.Except(existedUserProfileIds).ToList();

        // upsert new records
        var userProfileDeletionBuffers = toBeAddedUserProfileIds
            .Select(
                x => new UserProfileDeletionBuffer
                {
                    UserProfileId = x,
                    CompanyId = companyId,
                    DeletedAt = deletedAt,
                    ScheduledHardDeleteAt = scheduledPermanentDeleteAt,
                    DeletedByStaffId = staffId
                })
            .ToList();

        await dbContext.UserProfileDeletionBuffers
            .AddRangeAsync(userProfileDeletionBuffers);

        await dbContext.SaveChangesAsync();

        // update existed records
        if (existedUserProfileIds.Any())
        {
            await dbContext.UserProfileDeletionBuffers
                .Where(
                    x =>
                        x.CompanyId == companyId &&
                        existedUserProfileIds.Contains(x.UserProfileId))
                .ExecuteUpdateAsync(
                    x => x
                        .SetProperty(buffer => buffer.DeletedAt, deletedAt)
                        .SetProperty(buffer => buffer.ScheduledHardDeleteAt, scheduledPermanentDeleteAt)
                        .SetProperty(buffer => buffer.DeletedByStaffId, staffId));
        }
    }
}