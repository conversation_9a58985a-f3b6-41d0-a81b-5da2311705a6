﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Newtonsoft.Json;
using PhoneNumbers;
using Sleekflow.Apis.MessagingHub.Model;
using Travis_backend.ChannelDomain.Models;
using Travis_backend.ChannelDomain.Models.Interfaces;
using Travis_backend.ChannelDomain.ViewModels.Interfaces;
using Travis_backend.CommonDomain.Models;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.Constants;
using Travis_backend.Extensions;

namespace Travis_backend.Models.ChatChannelConfig;

public class WhatsappCloudApiConfig : DateAuditedEntity<long>, IMessagingChannel
{
    [JsonIgnore]
    [ForeignKey(nameof(CompanyId))]
    public Company Company { get; set; }

    public string CompanyId { get; set; }

    public string ChannelName { get; set; }

    public string MessagingHubWabaPhoneNumberId { get; set; }

    public string MessagingHubWabaId { get; set; }

    public string WhatsappPhoneNumber { get; set; }

    public string WhatsappDisplayName { get; set; }

    public string FacebookPhoneNumberId { get; set; }

    public string FacebookBusinessId { get; set; }

    public string FacebookWabaId { get; set; }

    public string FacebookWabaName { get; set; }

    public string FacebookWabaBusinessName { get; set; }

    public WabaDto Waba { get; set; }

    public WabaPhoneNumberDto WabaPhoneNumber { get; set; }

    public ProductCatalogSetting ProductCatalogSetting { get; set; }

    public string TemplateNamespace { get; set; }

    public bool IsOptInEnable { get; set; }

    public WhatsappCloudApiOptInConfig OptInConfig { get; set; }

    [NotMapped]
    public string ChannelType => ChannelTypes.WhatsappCloudApi;

    // [NotMapped]
    // public string ChannelIdentityId => WhatsappPhoneNumber;
    [MaxLength(400)]
    public string ChannelIdentityId { get; set; }

    [NotMapped]
    public string ChannelDisplayName => ChannelName;

    [NotMapped]
    public WhatsappOfficialChannelAccessLevel AccessLevel
    {
        get
        {
            var accountMode = WabaPhoneNumber.FacebookPhoneNumberAccountMode?.ToLower();
            var wabaStatus = Waba.FacebookWabaBusinessVerificationStatus?.ToLower();

            if (string.IsNullOrWhiteSpace(accountMode) || string.IsNullOrWhiteSpace(wabaStatus))
            {
                return WhatsappOfficialChannelAccessLevel.Unknown;
            }

            if (accountMode == "sandbox" && wabaStatus != "verified")
            {
                return WhatsappOfficialChannelAccessLevel.BasicTrial;
            }

            if (accountMode == "sandbox" && wabaStatus == "verified")
            {
                return WhatsappOfficialChannelAccessLevel.ExpandedTrial;
            }

            if (accountMode == "live" && wabaStatus != "verified")
            {
                return WhatsappOfficialChannelAccessLevel.BasicTrial;
            }

            return WhatsappOfficialChannelAccessLevel.Unknown;
        }
    }

    public WhatsappCloudApiConfig()
    {
    }

    public WhatsappCloudApiConfig(
        string companyId,
        string channelName,
        WabaDto waba,
        WabaPhoneNumberDto wabaPhoneNumber)
    {
        CompanyId = companyId;
        ChannelName = channelName;

        MessagingHubWabaPhoneNumberId = wabaPhoneNumber.Id;
        MessagingHubWabaId = waba.Id;

        WhatsappPhoneNumber = PhoneNumberUtil.Normalize(wabaPhoneNumber.FacebookPhoneNumber);
        ChannelIdentityId = WhatsappPhoneNumber;

        FacebookPhoneNumberId = wabaPhoneNumber.FacebookPhoneNumberId;

        FacebookBusinessId = waba.FacebookWabaBusinessId;
        FacebookWabaId = waba.FacebookWabaId;
        TemplateNamespace = waba.FacebookWabaMessageTemplateNamespace;

        SetWabaDetail(waba);
        SetWabaPhoneNumberDetail(wabaPhoneNumber);
    }

    public void SetWabaPhoneNumberDetail(WabaPhoneNumberDto wabaPhoneNumber)
    {
        if (FacebookPhoneNumberId != wabaPhoneNumber.FacebookPhoneNumberId)
        {
            throw new Exception("Id field should not be different.");
        }

        if (WhatsappDisplayName.IsNewValueUpdatedAndNotNull(wabaPhoneNumber.FacebookPhoneNumberVerifiedName))
        {
            WhatsappDisplayName = wabaPhoneNumber.FacebookPhoneNumberVerifiedName;
        }

        WabaPhoneNumber = wabaPhoneNumber;
    }

    public void SetWabaDetail(WabaDto waba)
    {
        if (FacebookBusinessId != waba.FacebookWabaBusinessId)
        {
            throw new Exception("Id field should not be different.");
        }

        if (FacebookWabaId != waba.FacebookWabaId)
        {
            throw new Exception("Id field should not be different.");
        }

        if (FacebookWabaName.IsNewValueUpdatedAndNotNull(waba.FacebookWabaName))
        {
            FacebookWabaName = waba.FacebookWabaName;
        }

        if (FacebookWabaBusinessName.IsNewValueUpdatedAndNotNull(waba.FacebookWabaBusinessName))
        {
            FacebookWabaBusinessName = waba.FacebookWabaBusinessName;
        }

        Waba = waba;
    }
}

public class WhatsappCloudApiWabaConnection : DateAuditedEntity<long>
{
    [ForeignKey(nameof(CompanyId))]
    public Company Company { get; set; }

    public string CompanyId { get; set; }

    public string MessagingHubWabaId { get; set; }

    public string FacebookWabaName { get; set; }

    public string FacebookWabaId { get; set; }

    public string FacebookBusinessId { get; set; }

    public string FacebookWabaBusinessName { get; set; }

    public WhatsappCloudApiWabaConnection()
    {
    }

    public WhatsappCloudApiWabaConnection(string companyId, WabaDto waba)
    {
        CompanyId = companyId;

        MessagingHubWabaId = waba.Id;
        FacebookWabaId = waba.FacebookWabaId;
        FacebookWabaName = waba.FacebookWabaName;
        FacebookWabaName = waba.FacebookWabaBusinessId;
        FacebookWabaBusinessName = waba.FacebookWabaBusinessName;
    }

    public void UpdateWabaDetail(WabaDto waba)
    {
        if (MessagingHubWabaId.IsNewValueUpdatedAndNotNull(waba.Id))
        {
            MessagingHubWabaId = waba.Id;
        }

        if (FacebookWabaId.IsNewValueUpdatedAndNotNull(waba.FacebookWabaId))
        {
            FacebookWabaId = waba.FacebookWabaId;
        }

        if (FacebookWabaName.IsNewValueUpdatedAndNotNull(waba.FacebookWabaName))
        {
            FacebookWabaName = waba.FacebookWabaName;
        }

        if (FacebookBusinessId.IsNewValueUpdatedAndNotNull(waba.FacebookWabaBusinessId))
        {
            FacebookWabaName = waba.FacebookWabaBusinessId;
        }

        if (FacebookWabaBusinessName.IsNewValueUpdatedAndNotNull(waba.FacebookWabaBusinessName))
        {
            FacebookWabaBusinessName = waba.FacebookWabaBusinessName;
        }
    }
}

public class WhatsappCloudApiOptInConfig
{
    public string TemplateName { get; set; }

    public string Language { get; set; }

    public string TemplateMessageContent { get; set; }

    public string ReadMoreTemplateButtonMessage { get; set; }
}

public class WhatsappCloudApiConfigViewModel : IMessagingChannelDto
{
    public long Id { get; set; }

    public string CompanyId { get; set; }

    public string ChannelName { get; set; }

    public string MessagingHubWabaPhoneNumberId { get; set; }

    public string MessagingHubWabaId { get; set; }

    public string WhatsappPhoneNumber { get; set; }

    public string FacebookDisplayPhoneNumber { get; set; }

    public string WhatsappDisplayName { get; set; }

    public string FacebookWabaName { get; set; }

    public string FacebookWabaBusinessName { get; set; }

    public string FacebookWabaBusinessId { get; set; }

    public string FacebookWabaId { get; set; }

    public string FacebookPhoneNumberId { get; set; }

    public string TemplateNamespace { get; set; }

    public string FacebookWabaBusinessVerificationStatus { get; set; }

    public bool? FacebookPhoneNumberIsPinEnabled { get; set; }

    public string
        FacebookPhoneNumberStatus
    {
        get;
        set;
    } // enum {PENDING, DELETED, MIGRATED, BANNED, RESTRICTED, RATE_LIMITED, FLAGGED, CONNECTED, DISCONNECTED, UNKNOWN, UNVERIFIED}

    public string FacebookPhoneNumberQualityRating { get; set; }

    public string FacebookPhoneNumberNameStatus { get; set; }

    public string FacebookPhoneNumberNewNameStatus { get; set; }

    public string FacebookPhoneNumberAccountMode { get; set; } // SANDBOX, LIVE

    public string FacebookPhoneNumberCodeVerificationStatus { get; set; }

    public string FacebookPhoneNumberIsOfficialBusinessAccount { get; set; }
    public string
        FacebookPhoneNumberMessagingLimitTier
    {
        get;
        set;
    } // TIER_50, TIER_250, TIER_1K, TIER_10K, TIER_100K, TIER_UNLIMITED
    public string FacebookProductCatalogId { get; set; }
    public string FacebookProductCatalogName { get; set; }

    public string FacebookDatasetId { get; set; }

    public string FacebookDatasetName { get; set; }
    public WhatsappOfficialChannelAccessLevel AccessLevel { get; set; }

    public WabaDtoPhoneNumberQualityScore FacebookPhoneNumberQualityScore { get; set; }

    public bool IsOptInEnable { get; set; }

    public WhatsappCloudApiOptInConfig OptInConfig { get; set; }

    public ProductCatalogSetting ProductCatalogSetting { get; set; }

    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

    public string ChannelType { get; set; }

    public string ChannelIdentityId { get; set; }

    public string ChannelDisplayName { get; set; }
}

public class ProductCatalogSetting
{
    public bool HasEnabledProductCatalog { get; set; } = false;
    public bool HasEnabledAutoSendStripePaymentUrl { get; set; }
}

public class WhatsAppCloudApiTemplateBookmark : Entity<long>, IHasCreationDate, IHasUpdateDate
{
    [MaxLength(36)]
    public string CompanyId { get; set; }

    public string MessagingHubWabaId { get; set; }

    public string TemplateId { get; set; }

    public string TemplateName { get; set; }

    public string TemplateLanguage { get; set; }

    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
}

public class WhatsAppCloudApiTemplateBookmarkResponse
{
    public string CompanyId { get; set; }

    public string MessagingHubWabaId { get; set; }

    public string TemplateId { get; set; }

    public string TemplateName { get; set; }

    public string TemplateLanguage { get; set; }

    public DateTime CreatedAt { get; set; }

    public DateTime UpdatedAt { get; set; }
}