using System.Collections.Generic;
using System.Threading.Tasks;
using <PERSON>e;

namespace Travis_backend.StripeIntegrationDomain.Services;

/// <summary>
/// Stripe's subscription service.
/// </summary>
public interface IStripeSubscriptionService
{
    /// <summary>
    /// Get Stripe Subscription.
    /// </summary>
    /// <param name="stripeSubscriptionId">Stripe's Subscription Id.</param>
    /// <returns>Stripe's Subscription Object.</returns>
    Task<Subscription> GetSubscriptionAsync(string stripeSubscriptionId);

    /// <summary>
    /// Cancel Stripe's Subscription.
    /// </summary>
    /// <param name="stripeSubscriptionId">Stripe's Subscription Id.</param>
    /// <param name="prorate">Prorate.</param>
    /// <param name="invoiceNow">Invoice Now.</param>
    /// <returns>Stripe's Subscription Object.</returns>
    Task<Subscription> CancelSubscriptionAsync(string stripeSubscriptionId, bool prorate = true, bool invoiceNow = false);

    /// <summary>
    /// Check if the subscription is cancelled with prorate.
    /// </summary>
    /// <param name="customerId">Stripe's Customer Id.</param>
    /// <param name="subscriptionId">Stripe's Subscription Id.</param>
    /// <param name="subscriptionPlanIds">Stripe's Subscription Plan Ids.</param>
    /// <returns>Boolean indicate whether the subscription is cancelled with prorate.</returns>
    Task<bool> IsSubscriptionCancelledWithProrate(string customerId, string subscriptionId, IEnumerable<string> subscriptionPlanIds);

    /// <summary>
    /// Delete a SubscriptionItem from Subscription.
    /// </summary>
    /// <param name="stripeSubscriptionId">Stripe's Subscription Id.</param>
    /// <param name="subscriptionItemId">Stripe's SubscriptionItem Id.</param>
    /// <returns>Task Object.</returns>
    Task DeleteSubscriptionItemAsync(string stripeSubscriptionId, string subscriptionItemId);
}