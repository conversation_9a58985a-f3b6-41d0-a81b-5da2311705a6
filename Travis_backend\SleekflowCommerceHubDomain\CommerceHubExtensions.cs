using System.Net.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Sleekflow.Apis.CommerceHub.Api;
using Travis_backend.Constants;
using Travis_backend.InternalDomain.Services;
using Travis_backend.SleekflowCommerceHubDomain.HttpClientHandlers;
using Travis_backend.SleekflowCommerceHubDomain.Services;

namespace Travis_backend.SleekflowCommerceHubDomain;

public static class CommerceHubExtensions
{
    public static IServiceCollection RegisterCommerceHubServices(
        this IServiceCollection services,
        IConfiguration configuration)
    {
        var commerceHubConfig = new Sleekflow.Apis.CommerceHub.Client.Configuration
        {
            BasePath = configuration.GetValue<string>("CommerceHub:Endpoint")
        };

        services.AddSingleton(commerceHubConfig);
        services.AddScoped<CommerceHubJobs>();

        services.AddTransient<CommerceHubHttpClientHandler>();
        services.AddHttpClient(
            HttpClientHandlerName.CommerceHub,
            (sp, client) =>
            {
                var config = sp.GetRequiredService<IConfiguration>();
                client.DefaultRequestHeaders.TryAddWithoutValidation(
                    "X-Sleekflow-Key",
                    config.GetValue<string>("CommerceHub:Key"));
            });

        services.AddScoped<IBlobsApi, BlobsApi>(
            sp =>
                new (
                    sp.GetRequiredService<IHttpClientFactory>()
                        .CreateClient(HttpClientHandlerName.CommerceHub),
                    sp.GetRequiredService<Sleekflow.Apis.CommerceHub.Client.Configuration>(),
                    sp.GetRequiredService<CommerceHubHttpClientHandler>()));

        services.AddScoped<ICartsApi, CartsApi>(
            sp =>
                new (
                    sp.GetRequiredService<IHttpClientFactory>()
                        .CreateClient(HttpClientHandlerName.CommerceHub),
                    sp.GetRequiredService<Sleekflow.Apis.CommerceHub.Client.Configuration>(),
                    sp.GetRequiredService<CommerceHubHttpClientHandler>()));

        services.AddScoped<ICategoriesApi, CategoriesApi>(
            sp =>
                new (
                    sp.GetRequiredService<IHttpClientFactory>()
                        .CreateClient(HttpClientHandlerName.CommerceHub),
                    sp.GetRequiredService<Sleekflow.Apis.CommerceHub.Client.Configuration>(),
                    sp.GetRequiredService<CommerceHubHttpClientHandler>()));

        services.AddScoped<ICurrenciesApi, CurrenciesApi>(
            sp =>
                new (
                    sp.GetRequiredService<IHttpClientFactory>()
                        .CreateClient(HttpClientHandlerName.CommerceHub),
                    sp.GetRequiredService<Sleekflow.Apis.CommerceHub.Client.Configuration>(),
                    sp.GetRequiredService<CommerceHubHttpClientHandler>()));

        services.AddScoped<ICustomCatalogsApi, CustomCatalogsApi>(
            sp =>
                new (
                    sp.GetRequiredService<IHttpClientFactory>()
                        .CreateClient(HttpClientHandlerName.CommerceHub),
                    sp.GetRequiredService<Sleekflow.Apis.CommerceHub.Client.Configuration>(),
                    sp.GetRequiredService<CommerceHubHttpClientHandler>()));

        services.AddScoped<ILanguagesApi, LanguagesApi>(
            sp =>
                new (
                    sp.GetRequiredService<IHttpClientFactory>()
                        .CreateClient(HttpClientHandlerName.CommerceHub),
                    sp.GetRequiredService<Sleekflow.Apis.CommerceHub.Client.Configuration>(),
                    sp.GetRequiredService<CommerceHubHttpClientHandler>()));

        services.AddScoped<IOrdersApi, OrdersApi>(
            sp =>
                new (
                    sp.GetRequiredService<IHttpClientFactory>()
                        .CreateClient(HttpClientHandlerName.CommerceHub),
                    sp.GetRequiredService<Sleekflow.Apis.CommerceHub.Client.Configuration>(),
                    sp.GetRequiredService<CommerceHubHttpClientHandler>()));

        services.AddScoped<IPaymentProviderConfigsApi, PaymentProviderConfigsApi>(
            sp =>
                new (
                    sp.GetRequiredService<IHttpClientFactory>()
                        .CreateClient(HttpClientHandlerName.CommerceHub),
                    sp.GetRequiredService<Sleekflow.Apis.CommerceHub.Client.Configuration>(),
                    sp.GetRequiredService<CommerceHubHttpClientHandler>()));

        services.AddScoped<IPaymentsApi, PaymentsApi>(
            sp =>
                new (
                    sp.GetRequiredService<IHttpClientFactory>()
                        .CreateClient(HttpClientHandlerName.CommerceHub),
                    sp.GetRequiredService<Sleekflow.Apis.CommerceHub.Client.Configuration>(),
                    sp.GetRequiredService<CommerceHubHttpClientHandler>()));

        services.AddScoped<IProductsApi, ProductsApi>(
            sp =>
                new (
                    sp.GetRequiredService<IHttpClientFactory>()
                        .CreateClient(HttpClientHandlerName.CommerceHub),
                    sp.GetRequiredService<Sleekflow.Apis.CommerceHub.Client.Configuration>(),
                    sp.GetRequiredService<CommerceHubHttpClientHandler>()));

        services.AddScoped<IProductVariantsApi, ProductVariantsApi>(
            sp =>
                new (
                    sp.GetRequiredService<IHttpClientFactory>()
                        .CreateClient(HttpClientHandlerName.CommerceHub),
                    sp.GetRequiredService<Sleekflow.Apis.CommerceHub.Client.Configuration>(),
                    sp.GetRequiredService<CommerceHubHttpClientHandler>()));

        services.AddScoped<IStoresApi, StoresApi>(
            sp =>
                new (
                    sp.GetRequiredService<IHttpClientFactory>()
                        .CreateClient(HttpClientHandlerName.CommerceHub),
                    sp.GetRequiredService<Sleekflow.Apis.CommerceHub.Client.Configuration>(),
                    sp.GetRequiredService<CommerceHubHttpClientHandler>()));

        services.AddScoped<IWebhooksApi, WebhooksApi>(
            sp =>
                new (
                    sp.GetRequiredService<IHttpClientFactory>()
                        .CreateClient(HttpClientHandlerName.CommerceHub),
                    sp.GetRequiredService<Sleekflow.Apis.CommerceHub.Client.Configuration>(),
                    sp.GetRequiredService<CommerceHubHttpClientHandler>()));

        services.AddScoped<ICustomCatalogConfigsApi, CustomCatalogConfigsApi>(
            sp =>
                new (
                    sp.GetRequiredService<IHttpClientFactory>()
                        .CreateClient(HttpClientHandlerName.CommerceHub),
                    sp.GetRequiredService<Sleekflow.Apis.CommerceHub.Client.Configuration>(),
                    sp.GetRequiredService<CommerceHubHttpClientHandler>()));

        return services;
    }
}