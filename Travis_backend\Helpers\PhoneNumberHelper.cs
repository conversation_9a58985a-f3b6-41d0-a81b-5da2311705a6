﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using PhoneNumbers;

namespace Travis_backend.Helpers
{
    public class PhoneNumberHelper
    {
        private static readonly PhoneNumberUtil PhoneUtil = PhoneNumberUtil.GetInstance();
        private static readonly Lazy<Dictionary<string, string>> Mappings = new (BuildMappings);

        public static readonly Lazy<IReadOnlyList<CountryCodeInfo>> CountryCodeInfoList =
            new (CountryCodeInfo.BuildList);

        public class CountryCodeInfo
        {
            private CountryCodeInfo(string countryName, string countryCode, string isoCode2, string isoCode3)
            {
                CountryName = countryName;
                CountryCode = countryCode;
                IsoCode2 = isoCode2;
                IsoCode3 = isoCode3;
            }

            public string CountryName { get; set; }

            public string CountryCode { get; set; }

            public string IsoCode2 { get; set; }

            public string IsoCode3 { get; set; }

            public static List<CountryCodeInfo> BuildList()
            {
                return new List<CountryCodeInfo>()
                {
                    new ("United States", "1", "US", "USA"),
                    new ("Andorra", "376", "AD", "AND"),
                    new ("United Arab Emirates", "971", "AE", "ARE"),
                    new ("Afghanistan", "93", "AF", "AFG"),
                    new ("Antigua and Barbuda", "1268", "AG", "ATG"),
                    new ("Antigua & Barbuda", "1268", "AG", "ATG"),
                    new ("Anguilla", "1264", "AI", "AIA"),
                    new ("Albania", "355", "AL", "ALB"),
                    new ("Armenia", "374", "AM", "ARM"),
                    new ("Netherlands Antilles", "599", "AN", "ANT"),
                    new ("Angola", "244", "AO", "AGO"),
                    new ("Antarctica", "672", "AQ", "ATA"),
                    new ("Argentina", "54", "AR", "ARG"),
                    new ("American Samoa", "1684", "AS", "ASM"),
                    new ("Austria", "43", "AT", "AUT"),
                    new ("Australia", "61", "AU", "AUS"),
                    new ("Aruba", "297", "AW", "ABW"),
                    new ("Azerbaijan", "994", "AZ", "AZE"),
                    new ("Bosnia And Herzegovina", "387", "BA", "BIH"),
                    new ("Bosnia & Herzegovina", "387", "BA", "BIH"),
                    new ("Barbados", "1246", "BB", "BRB"),
                    new ("Bangladesh", "880", "BD", "BGD"),
                    new ("Belgium", "32", "BE", "BEL"),
                    new ("Burkina Faso", "226", "BF", "BFA"),
                    new ("Bulgaria", "359", "BG", "BGR"),
                    new ("Bahrain", "973", "BH", "BHR"),
                    new ("Burundi", "257", "BI", "BDI"),
                    new ("Benin", "229", "BJ", "BEN"),
                    new ("Saint Barthelemy", "590", "BL", "BLM"),
                    new ("Bermuda", "1441", "BM", "BMU"),
                    new ("Brunei", "673", "BN", "BRN"),
                    new ("Bolivia", "591", "BO", "BOL"),
                    new ("Brazil", "55", "BR", "BRA"),
                    new ("Bahamas", "1242", "BS", "BHS"),
                    new ("Bhutan", "975", "BT", "BTN"),
                    new ("Botswana", "267", "BW", "BWA"),
                    new ("Belarus", "375", "BY", "BLR"),
                    new ("Belize", "501", "BZ", "BLZ"),
                    new ("Canada", "1", "CA", "CAN"),
                    new ("Cocos Islands", "61", "CC", "CCK"),
                    new ("Democratic Republic of the Congo", "243", "CD", "COD"),
                    new ("Congo (DRC)", "243", "CD", "COD"),
                    new ("Central African Republic", "236", "CF", "CAF"),
                    new ("Republic of the Congo", "242", "CG", "COG"),
                    new ("Switzerland", "41", "CH", "CHE"),
                    new ("Ivory Coast", "225", "CI", "CIV"),
                    new ("Côte d’Ivoire", "225", "CI", "CIV"),
                    new ("Cook Islands", "682", "CK", "COK"),
                    new ("Chile", "56", "CL", "CHL"),
                    new ("Cameroon", "237", "CM", "CMR"),
                    new ("China", "86", "CN", "CHN"),
                    new ("Colombia", "57", "CO", "COL"),
                    new ("Costa Rica", "506", "CR", "CRI"),
                    new ("Cuba", "53", "CU", "CUB"),
                    new ("Cape Verde", "238", "CV", "CPV"),
                    new ("Curacao", "599", "CW", "CUW"),
                    new ("Christmas Island", "61", "CX", "CXR"),
                    new ("Cyprus", "357", "CY", "CYP"),
                    new ("Czechia", "420", "CZ", "CZE"),
                    new ("Germany", "49", "DE", "DEU"),
                    new ("Djibouti", "253", "DJ", "DJI"),
                    new ("Denmark", "45", "DK", "DNK"),
                    new ("Dominica", "1767", "DM", "DMA"),
                    new ("Dominican Republic", "1809", "DO", "DOM"),
                    new ("Dominican Republic", "1829", "DO", "DOM"),
                    new ("Dominican Republic", "1849", "DO", "DOM"),
                    new ("Algeria", "213", "DZ", "DZA"),
                    new ("Ecuador", "593", "EC", "ECU"),
                    new ("Estonia", "372", "EE", "EST"),
                    new ("Egypt", "20", "EG", "EGY"),
                    new ("Western Sahara", "212", "EH", "ESH"),
                    new ("Eritrea", "291", "ER", "ERI"),
                    new ("Spain", "34", "ES", "ESP"),
                    new ("Ethiopia", "251", "ET", "ETH"),
                    new ("Finland", "358", "FI", "FIN"),
                    new ("Fiji", "679", "FJ", "FJI"),
                    new ("Falkland Islands", "500", "FK", "FLK"),
                    new ("Micronesia", "691", "FM", "FSM"),
                    new ("Faroe Islands", "298", "FO", "FRO"),
                    new ("France", "33", "FR", "FRA"),
                    new ("Gabon", "241", "GA", "GAB"),
                    new ("United Kingdom", "44", "GB", "GBR"),
                    new ("Grenada", "1473", "GD", "GRD"),
                    new ("Georgia", "995", "GE", "GEO"),
                    new ("Guernsey", "441481", "GG", "GGY"),
                    new ("Ghana", "233", "GH", "GHA"),
                    new ("Gibraltar", "350", "GI", "GIB"),
                    new ("Greenland", "299", "GL", "GRL"),
                    new ("Gambia", "220", "GM", "GMB"),
                    new ("Guinea", "224", "GN", "GIN"),
                    new ("Equatorial Guinea", "240", "GQ", "GNQ"),
                    new ("Greece", "30", "GR", "GRC"),
                    new ("Guatemala", "502", "GT", "GTM"),
                    new ("Guam", "1671", "GU", "GUM"),
                    new ("Guinea Bissau", "245", "GW", "GNB"),
                    new ("Guyana", "592", "GY", "GUY"),
                    new ("Hong Kong SAR", "852", "HK", "HKG"),
                    new ("Hong Kong", "852", "HK", "HKG"),
                    new ("Honduras", "504", "HN", "HND"),
                    new ("Croatia", "385", "HR", "HRV"),
                    new ("Haiti", "509", "HT", "HTI"),
                    new ("Hungary", "36", "HU", "HUN"),
                    new ("Indonesia", "62", "ID", "IDN"),
                    new ("Ireland", "353", "IE", "IRL"),
                    new ("Israel", "972", "IL", "ISR"),
                    new ("Isle of Man", "441624", "IM", "IMN"),
                    new ("India", "91", "IN", "IND"),
                    new ("British Indian Ocean Territory", "246", "IO", "IOT"),
                    new ("Iraq", "964", "IQ", "IRQ"),
                    new ("Iran", "98", "IR", "IRN"),
                    new ("Iceland", "354", "IS", "ISL"),
                    new ("Italy", "39", "IT", "ITA"),
                    new ("Jersey", "441534", "JE", "JEY"),
                    new ("Jamaica", "1876", "JM", "JAM"),
                    new ("Jordan", "962", "JO", "JOR"),
                    new ("Japan", "81", "JP", "JPN"),
                    new ("Kenya", "254", "KE", "KEN"),
                    new ("Kyrgyzstan", "996", "KG", "KGZ"),
                    new ("Cambodia", "855", "KH", "KHM"),
                    new ("Democratic Kampuchea", "855", "KH", "KHM"),
                    new ("Kiribati", "686", "KI", "KIR"),
                    new ("Comoros", "269", "KM", "COM"),
                    new ("Saint Kitts and Nevis", "1869", "KN", "KNA"),
                    new ("North Korea", "850", "KP", "PRK"),
                    new ("Korea", "82", "KR", "KOR"),
                    new ("South Korea", "82", "KR", "KOR"),
                    new ("Kuwait", "965", "KW", "KWT"),
                    new ("Cayman Islands", "1345", "KY", "CYM"),
                    new ("Kazakhstan", "7", "KZ", "KAZ"),
                    new ("Laos", "856", "LA", "LAO"),
                    new ("Lebanon", "961", "LB", "LBN"),
                    new ("Saint Lucia", "1758", "LC", "LCA"),
                    new ("Liechtenstein", "423", "LI", "LIE"),
                    new ("Sri Lanka", "94", "LK", "LKA"),
                    new ("Liberia", "231", "LR", "LBR"),
                    new ("Lesotho", "266", "LS", "LSO"),
                    new ("Lithuania", "370", "LT", "LTU"),
                    new ("Luxembourg", "352", "LU", "LUX"),
                    new ("Latvia", "371", "LV", "LVA"),
                    new ("Libya", "218", "LY", "LBY"),
                    new ("Morocco", "212", "MA", "MAR"),
                    new ("Monaco", "377", "MC", "MCO"),
                    new ("Moldova", "373", "MD", "MDA"),
                    new ("Montenegro", "382", "ME", "MNE"),
                    new ("Saint Martin", "590", "MF", "MAF"),
                    new ("Madagascar", "261", "MG", "MDG"),
                    new ("Marshall Islands", "692", "MH", "MHL"),
                    new ("North Macedonia", "389", "MK", "MKD"),
                    new ("Macedonia", "389", "MK", "MKD"),
                    new ("Mali", "223", "ML", "MLI"),
                    new ("Myanmar", "95", "MM", "MMR"),
                    new ("Burma", "95", "MM", "MMR"),
                    new ("Mongolia", "976", "MN", "MNG"),
                    new ("Macao", "853", "MO", "MAC"),
                    new ("Macao SAR", "853", "MO", "MAC"),
                    new ("Macau", "853", "MO", "MAC"),
                    new ("Macau SAR", "853", "MO", "MAC"),
                    new ("Northern Mariana Islands", "1670", "MP", "MNP"),
                    new ("Northern Mariana Is.", "1670", "MP", "MNP"),
                    new ("Mauritania", "222", "MR", "MRT"),
                    new ("Montserrat", "1664", "MS", "MSR"),
                    new ("Malta", "356", "MT", "MLT"),
                    new ("Mauritius", "230", "MU", "MUS"),
                    new ("Maldives", "960", "MV", "MDV"),
                    new ("Malawi", "265", "MW", "MWI"),
                    new ("Mexico", "52", "MX", "MEX"),
                    new ("Malaysia", "60", "MY", "MYS"),
                    new ("Mozambique", "258", "MZ", "MOZ"),
                    new ("Namibia", "264", "NA", "NAM"),
                    new ("New Caledonia", "687", "NC", "NCL"),
                    new ("Niger", "227", "NE", "NER"),
                    new ("Nigeria", "234", "NG", "NGA"),
                    new ("Nicaragua", "505", "NI", "NIC"),
                    new ("Netherlands", "31", "NL", "NLD"),
                    new ("Norway", "47", "NO", "NOR"),
                    new ("Nepal", "977", "NP", "NPL"),
                    new ("Nauru", "674", "NR", "NRU"),
                    new ("Niue", "683", "NU", "NIU"),
                    new ("New Zealand", "64", "NZ", "NZL"),
                    new ("Oman", "968", "OM", "OMN"),
                    new ("Panama", "507", "PA", "PAN"),
                    new ("Peru", "51", "PE", "PER"),
                    new ("French Polynesia", "689", "PF", "PYF"),
                    new ("Papua New Guinea", "675", "PG", "PNG"),
                    new ("Philippines", "63", "PH", "PHL"),
                    new ("Pakistan", "92", "PK", "PAK"),
                    new ("Poland", "48", "PL", "POL"),
                    new ("Saint Pierre and Miquelon", "508", "PM", "SPM"),
                    new ("Pitcairn", "64", "PN", "PCN"),
                    new ("Puerto Rico", "1787", "PR", "PRI"),
                    new ("Puerto Rico", "1939", "PR", "PRI"),
                    new ("Palestine", "970", "PS", "PSE"),
                    new ("Portugal", "351", "PT", "PRT"),
                    new ("Palau", "680", "PW", "PLW"),
                    new ("Paraguay", "595", "PY", "PRY"),
                    new ("Qatar", "974", "QA", "QAT"),
                    new ("Réunion", "262", "RE", "REU"),
                    new ("Reunion", "262", "RE", "REU"),
                    new ("Romania", "40", "RO", "ROU"),
                    new ("Serbia", "381", "RS", "SRB"),
                    new ("Russia", "7", "RU", "RUS"),
                    new ("Rwanda", "250", "RW", "RWA"),
                    new ("Saudi Arabia", "966", "SA", "SAU"),
                    new ("Solomon Islands", "677", "SB", "SLB"),
                    new ("Seychelles", "248", "SC", "SYC"),
                    new ("Sudan", "249", "SD", "SDN"),
                    new ("Sweden", "46", "SE", "SWE"),
                    new ("Singapore", "65", "SG", "SGP"),
                    new ("Saint Helena", "290", "SH", "SHN"),
                    new ("Slovenia", "386", "SI", "SVN"),
                    new ("Svalbard and Jan Mayen", "47", "SJ", "SJM"),
                    new ("Slovakia", "421", "SK", "SVK"),
                    new ("Sierra Leone", "232", "SL", "SLE"),
                    new ("San Marino", "378", "SM", "SMR"),
                    new ("Senegal", "221", "SN", "SEN"),
                    new ("Somalia", "252", "SO", "SOM"),
                    new ("Suriname", "597", "SR", "SUR"),
                    new ("South Sudan", "211", "SS", "SSD"),
                    new ("Sao Tome and Principe", "239", "ST", "STP"),
                    new ("El Salvador", "503", "SV", "SLV"),
                    new ("Sint Maarten", "1721", "SX", "SXM"),
                    new ("Syria", "963", "SY", "SYR"),
                    new ("Swaziland", "268", "SZ", "SWZ"),
                    new ("Turks and Caicos Islands", "1649", "TC", "TCA"),
                    new ("Chad", "235", "TD", "TCD"),
                    new ("Togo", "228", "TG", "TGO"),
                    new ("Thailand", "66", "TH", "THA"),
                    new ("Tajikistan", "992", "TJ", "TJK"),
                    new ("Tokelau", "690", "TK", "TKL"),
                    new ("East Timor", "670", "TL", "TLS"),
                    new ("Turkmenistan", "993", "TM", "TKM"),
                    new ("Tunisia", "216", "TN", "TUN"),
                    new ("Tonga", "676", "TO", "TON"),
                    new ("Turkey", "90", "TR", "TUR"),
                    new ("Trinidad And Tobago", "1868", "TT", "TTO"),
                    new ("Trinidad & Tobago", "1868", "TT", "TTO"),
                    new ("Tuvalu", "688", "TV", "TUV"),
                    new ("Taiwan", "886", "TW", "TWN"),
                    new ("Tanzania", "255", "TZ", "TZA"),
                    new ("Ukraine", "380", "UA", "UKR"),
                    new ("Uganda", "256", "UG", "UGA"),
                    new ("Uruguay", "598", "UY", "URY"),
                    new ("Uzbekistan", "998", "UZ", "UZB"),
                    new ("Vatican", "379", "VA", "VAT"),
                    new ("Saint Vincent and the Grenadines", "1784", "VC", "VCT"),
                    new ("Venezuela", "58", "VE", "VEN"),
                    new ("British Virgin Islands", "1284", "VG", "VGB"),
                    new ("U.S. Virgin Islands", "1340", "VI", "VIR"),
                    new ("Vietnam", "84", "VN", "VNM"),
                    new ("Vanuatu", "678", "VU", "VUT"),
                    new ("Wallis and Futuna", "681", "WF", "WLF"),
                    new ("Samoa", "685", "WS", "WSM"),
                    new ("Kosovo", "383", "XK", "XKX"),
                    new ("Yemen", "967", "YE", "YEM"),
                    new ("Mayotte", "262", "YT", "MYT"),
                    new ("South Africa", "27", "ZA", "ZAF"),
                    new ("Zambia", "260", "ZM", "ZMB"),
                    new ("Zimbabwe", "263", "ZW", "ZWE"),
                };
            }
        }

        public static Dictionary<string, string> BuildMappings()
        {
            var dictionary = new Dictionary<string, string>();

            dictionary.Add("US", "1");
            dictionary.Add("AC", "247");
            dictionary.Add("AD", "376");
            dictionary.Add("AE", "971");
            dictionary.Add("AF", "93");
            dictionary.Add("AG", "1-268");
            dictionary.Add("AI", "1-264");
            dictionary.Add("AL", "355");
            dictionary.Add("AM", "374");
            dictionary.Add("AN", "599");
            dictionary.Add("AO", "244");
            dictionary.Add("AR", "54");
            dictionary.Add("AS", "1-684");
            dictionary.Add("AT", "43");
            dictionary.Add("AU", "61");
            dictionary.Add("AW", "297");
            dictionary.Add("AX", "358-18");
            dictionary.Add("AZ", "994"); // or 374-97
            dictionary.Add("BA", "387");
            dictionary.Add("BB", "1-246");
            dictionary.Add("BD", "880");
            dictionary.Add("BE", "32");
            dictionary.Add("BF", "226");
            dictionary.Add("BG", "359");
            dictionary.Add("BH", "973");
            dictionary.Add("BI", "257");
            dictionary.Add("BJ", "229");
            dictionary.Add("BM", "1-441");
            dictionary.Add("BN", "673");
            dictionary.Add("BO", "591");
            dictionary.Add("BR", "55");
            dictionary.Add("BS", "1-242");
            dictionary.Add("BT", "975");
            dictionary.Add("BW", "267");
            dictionary.Add("BY", "375");
            dictionary.Add("BZ", "501");
            dictionary.Add("CA", "1");
            dictionary.Add("CC", "61");
            dictionary.Add("CD", "243");
            dictionary.Add("CF", "236");
            dictionary.Add("CG", "242");
            dictionary.Add("CH", "41");
            dictionary.Add("CI", "225");
            dictionary.Add("CK", "682");
            dictionary.Add("CL", "56");
            dictionary.Add("CM", "237");
            dictionary.Add("CN", "86");
            dictionary.Add("CO", "57");
            dictionary.Add("CR", "506");
            dictionary.Add("CS", "381");
            dictionary.Add("CU", "53");
            dictionary.Add("CV", "238");
            dictionary.Add("CX", "61");
            dictionary.Add("CY", "357"); // or 90-392
            dictionary.Add("CZ", "420");
            dictionary.Add("DE", "49");
            dictionary.Add("DJ", "253");
            dictionary.Add("DK", "45");
            dictionary.Add("DM", "1-767");
            dictionary.Add("DO", "1-809"); // and 1-829?
            dictionary.Add("DZ", "213");
            dictionary.Add("EC", "593");
            dictionary.Add("EE", "372");
            dictionary.Add("EG", "20");
            dictionary.Add("EH", "212");
            dictionary.Add("ER", "291");
            dictionary.Add("ES", "34");
            dictionary.Add("ET", "251");
            dictionary.Add("FI", "358");
            dictionary.Add("FJ", "679");
            dictionary.Add("FK", "500");
            dictionary.Add("FM", "691");
            dictionary.Add("FO", "298");
            dictionary.Add("FR", "33");
            dictionary.Add("GA", "241");
            dictionary.Add("GB", "44");
            dictionary.Add("GD", "1-473");
            dictionary.Add("GE", "995");
            dictionary.Add("GF", "594");
            dictionary.Add("GG", "44");
            dictionary.Add("GH", "233");
            dictionary.Add("GI", "350");
            dictionary.Add("GL", "299");
            dictionary.Add("GM", "220");
            dictionary.Add("GN", "224");
            dictionary.Add("GP", "590");
            dictionary.Add("GQ", "240");
            dictionary.Add("GR", "30");
            dictionary.Add("GT", "502");
            dictionary.Add("GU", "1-671");
            dictionary.Add("GW", "245");
            dictionary.Add("GY", "592");
            dictionary.Add("HK", "852");
            dictionary.Add("HN", "504");
            dictionary.Add("HR", "385");
            dictionary.Add("HT", "509");
            dictionary.Add("HU", "36");
            dictionary.Add("ID", "62");
            dictionary.Add("IE", "353");
            dictionary.Add("IL", "972");
            dictionary.Add("IM", "44");
            dictionary.Add("IN", "91");
            dictionary.Add("IO", "246");
            dictionary.Add("IQ", "964");
            dictionary.Add("IR", "98");
            dictionary.Add("IS", "354");
            dictionary.Add("IT", "39");
            dictionary.Add("JE", "44");
            dictionary.Add("JM", "1-876");
            dictionary.Add("JO", "962");
            dictionary.Add("JP", "81");
            dictionary.Add("KE", "254");
            dictionary.Add("KG", "996");
            dictionary.Add("KH", "855");
            dictionary.Add("KI", "686");
            dictionary.Add("KM", "269");
            dictionary.Add("KN", "1-869");
            dictionary.Add("KP", "850");
            dictionary.Add("KR", "82");
            dictionary.Add("KW", "965");
            dictionary.Add("KY", "1-345");
            dictionary.Add("LA", "856");
            dictionary.Add("LB", "961");
            dictionary.Add("LC", "1-758");
            dictionary.Add("LI", "423");
            dictionary.Add("LK", "94");
            dictionary.Add("LR", "231");
            dictionary.Add("LS", "266");
            dictionary.Add("LT", "370");
            dictionary.Add("LU", "352");
            dictionary.Add("LV", "371");
            dictionary.Add("LY", "218");
            dictionary.Add("MA", "212");
            dictionary.Add("MC", "377");
            dictionary.Add("MD", "373"); // or 373-533
            dictionary.Add("ME", "382");
            dictionary.Add("MG", "261");
            dictionary.Add("MH", "692");
            dictionary.Add("MK", "389");
            dictionary.Add("ML", "223");
            dictionary.Add("MM", "95");
            dictionary.Add("MN", "976");
            dictionary.Add("MO", "853");
            dictionary.Add("MP", "1-670");
            dictionary.Add("MQ", "596");
            dictionary.Add("MR", "222");
            dictionary.Add("MS", "1-664");
            dictionary.Add("MT", "356");
            dictionary.Add("MU", "230");
            dictionary.Add("MV", "960");
            dictionary.Add("MW", "265");
            dictionary.Add("MX", "52");
            dictionary.Add("MY", "60");
            dictionary.Add("MZ", "258");
            dictionary.Add("NA", "264");
            dictionary.Add("NC", "687");
            dictionary.Add("NE", "227");
            dictionary.Add("NF", "672");
            dictionary.Add("NG", "234");
            dictionary.Add("NI", "505");
            dictionary.Add("NL", "31");
            dictionary.Add("NO", "47");
            dictionary.Add("NP", "977");
            dictionary.Add("NR", "674");
            dictionary.Add("NU", "683");
            dictionary.Add("NZ", "64");
            dictionary.Add("OM", "968");
            dictionary.Add("PA", "507");
            dictionary.Add("PE", "51");
            dictionary.Add("PF", "689");
            dictionary.Add("PG", "675");
            dictionary.Add("PH", "63");
            dictionary.Add("PK", "92");
            dictionary.Add("PL", "48");
            dictionary.Add("PM", "508");
            dictionary.Add("PR", "1-787"); // and 1-939 ?
            dictionary.Add("PS", "970");
            dictionary.Add("PT", "351");
            dictionary.Add("PW", "680");
            dictionary.Add("PY", "595");
            dictionary.Add("QA", "974");
            dictionary.Add("RE", "262");
            dictionary.Add("RO", "40");
            dictionary.Add("RS", "381");
            dictionary.Add("RU", "7");
            dictionary.Add("RW", "250");
            dictionary.Add("SA", "966");
            dictionary.Add("SB", "677");
            dictionary.Add("SC", "248");
            dictionary.Add("SD", "249");
            dictionary.Add("SE", "46");
            dictionary.Add("SG", "65");
            dictionary.Add("SH", "290");
            dictionary.Add("SI", "386");
            dictionary.Add("SJ", "47");
            dictionary.Add("SK", "421");
            dictionary.Add("SL", "232");
            dictionary.Add("SM", "378");
            dictionary.Add("SN", "221");
            dictionary.Add("SO", "252");
            dictionary.Add("SR", "597");
            dictionary.Add("ST", "239");
            dictionary.Add("SV", "503");
            dictionary.Add("SY", "963");
            dictionary.Add("SZ", "268");
            dictionary.Add("TA", "290");
            dictionary.Add("TC", "1-649");
            dictionary.Add("TD", "235");
            dictionary.Add("TG", "228");
            dictionary.Add("TH", "66");
            dictionary.Add("TJ", "992");
            dictionary.Add("TK", "690");
            dictionary.Add("TL", "670");
            dictionary.Add("TM", "993");
            dictionary.Add("TN", "216");
            dictionary.Add("TO", "676");
            dictionary.Add("TR", "90");
            dictionary.Add("TT", "1-868");
            dictionary.Add("TV", "688");
            dictionary.Add("TW", "886");
            dictionary.Add("TZ", "255");
            dictionary.Add("UA", "380");
            dictionary.Add("UG", "256");
            dictionary.Add("UY", "598");
            dictionary.Add("UZ", "998");
            dictionary.Add("VA", "379");
            dictionary.Add("VC", "1-784");
            dictionary.Add("VE", "58");
            dictionary.Add("VG", "1-284");
            dictionary.Add("VI", "1-340");
            dictionary.Add("VN", "84");
            dictionary.Add("VU", "678");
            dictionary.Add("WF", "681");
            dictionary.Add("WS", "685");
            dictionary.Add("YE", "967");
            dictionary.Add("YT", "262");
            dictionary.Add("ZA", "27");
            dictionary.Add("ZM", "260");
            dictionary.Add("ZW", "263");
            return dictionary;
        }

        // TODO change to use phone number instead due to current param: phoneCode(input is Country Code, ex "1")
        // and Canada phone number have Area Code as well
        // ref. https://www.quora.com/How-would-you-differentiate-a-Canadian-phone-number-from-an-American-phone-number

        // 1. Get Country Code from phone number
        // 2. Check Country Code is 1
        //     2.1 Check is Canada phone number(using Area code) return "CA", else return "US"
        // 3. else return Mappings.Value.Where(d => d.Value == phoneCode).Select(d => d.Key).FirstOrDefault();
        public static Dictionary<string, string> CanadaAreaCodeCityMapping = new Dictionary<string, string>()
        {
            {
                "204", "Manitoba"
            },
            {
                "226", "London"
            },
            {
                "236", "Vancouver"
            },
            {
                "249", "Sudbury"
            },
            {
                "250", "Kelowna"
            },
            {
                "289", "Hamilton"
            },
            {
                "306", "Saskatchewan"
            },
            {
                "343", "Ottawa"
            },
            {
                "365", "Hamilton"
            },
            {
                "367", "Quebec"
            },
            {
                "403", "Calgary"
            },
            {
                "416", "Toronto"
            },
            {
                "418", "Quebec"
            },
            {
                "431", "Manitoba"
            },
            {
                "437", "Toronto"
            },
            {
                "438", "Montreal"
            },
            {
                "450", "Granby"
            },
            {
                "506", "New Brunswick"
            },
            {
                "514", "Montreal"
            },
            {
                "519", "London"
            },
            {
                "548", "London"
            },
            {
                "579", "Granby"
            },
            {
                "581", "Quebec"
            },
            {
                "587", "Calgary"
            },
            {
                "604", "Vancouver"
            },
            {
                "613", "Ottawa"
            },
            {
                "639", "Saskatchewan"
            },
            {
                "647", "Toronto"
            },
            {
                "705", "Sudbury"
            },
            {
                "709", "Newfoundland/Labrador"
            },
            {
                "778", "Vancouver"
            },
            {
                "780", "Edmonton"
            },
            {
                "782", "Nova Scotia/PE Island"
            },
            {
                "807", "Kenora"
            },
            {
                "819", "Sherbrooke"
            },
            {
                "825", "Calgary"
            },
            {
                "867", "Northern Canada"
            },
            {
                "873", "Sherbrooke"
            },
            {
                "902", "Nova Scotia/PE Island"
            },
            {
                "905", "Hamilton"
            }
        };

        public static string GetCountryCode(PhoneNumber phoneNumber)
        {
            var phoneCode = phoneNumber.CountryCode;

            if (phoneCode == 7)
            {
                var initialDigit = phoneNumber.NationalNumber.ToString()[..1];

                return initialDigit switch
                {
                    "3" or "4" or "8" or "9" => "RU",
                    "6" or "7" => "KZ",
                    _ => "RU"
                };
            }

            if (phoneCode != 1)
            {
                var countyCode = Mappings.Value.Where(d => d.Value == phoneCode.ToString()).Select(d => d.Key)
                    .FirstOrDefault();

                return countyCode;
            }

            var areaCode = phoneNumber.NationalNumber.ToString().Substring(0, 3);

            if (CanadaAreaCodeCityMapping.Keys.Contains(areaCode))
            {
                return "CA";
            }

            return "US";
        }

        public static string GetPhoneNumber(string text)
        {
            var index = text.IndexOf("@", StringComparison.Ordinal);
            if (index == -1)
            {
                return text;
            }

            return text.Substring(0, index);
        }

        public static string NormalizePhoneNumber(string phoneNumber)
        {
            phoneNumber = FormatPhoneNumber(phoneNumber);

            if (string.IsNullOrWhiteSpace(phoneNumber))
            {
                return null;
            }

            return new Regex("[^0-9]", RegexOptions.Compiled).Replace(phoneNumber, string.Empty);
        }

        public static string NormalizeWhatsappPhoneNumber(string phoneNumber)
        {
            phoneNumber = NormalizePhoneNumber(phoneNumber);

            if (phoneNumber == null)
            {
                return null;
            }

            // Mexico Number
            if (phoneNumber.StartsWith("52") && phoneNumber.Length == 13)
            {
                return phoneNumber.Remove(2, 1);
            }

            // UK Phone Number
            if (phoneNumber.StartsWith("440") && phoneNumber.Length == 13)
            {
                return phoneNumber.Remove(2, 1);
            }

            // UAE Phone Number
            if (phoneNumber.StartsWith("9710") && phoneNumber.Length == 13)
            {
                return phoneNumber.Remove(3, 1);
            }

            return phoneNumber;
        }

        public static string GetValidPhoneNumberWithCountryCodeForImportContact(
            string phoneNumber,
            string countryName = null,
            string countryId = null)
        {
            var normalizedPhoneNumber = NormalizePhoneNumber(phoneNumber);

            if (string.IsNullOrWhiteSpace(normalizedPhoneNumber))
            {
                return null;
            }

            if (IsValidPhoneNumberContainsCountryCode(normalizedPhoneNumber))
            {
                return normalizedPhoneNumber;
            }

            if (countryName != null)
            {
                var parseResult = ParsePhoneNumberValidForCountryName(normalizedPhoneNumber, countryName);

                return parseResult.IsValid ? parseResult.PhoneNumberWithCountryCode : null;
            }

            if (countryId != null)
            {
                var parseResult = ParsePhoneNumberValidForCountryId(normalizedPhoneNumber, countryId);

                return parseResult.IsValid ? parseResult.PhoneNumberWithCountryCode : null;
            }

            return null;
        }

        public static bool IsValidPhoneNumberContainsCountryCode(string phoneNumber)
        {
            try
            {
                var normalizePhoneNumber = "+" + NormalizePhoneNumber(phoneNumber);

                var phone = PhoneUtil.Parse(normalizePhoneNumber,null);

                var isValidNumber = PhoneUtil.IsValidNumber(phone);

                if (!isValidNumber && phone.CountryCode.ToString() == "55" && phone.NationalNumber.ToString().Length == 10)
                {
                    return true;
                }

                if (!isValidNumber && phone.CountryCode.ToString() == "852")
                {
                    //Temporary fix for HK 4,7,8 mobile phone number
                    var nationalNumber = phone.NationalNumber.ToString();

                    if (nationalNumber.Length == 8)
                    {
                        if (nationalNumber.StartsWith("4"))
                        {
                            return true;
                        }

                        if (nationalNumber.StartsWith("7"))
                        {
                            return true;
                        }

                        if (nationalNumber.StartsWith("8"))
                        {
                            return true;
                        }
                    }
                }

                if (isValidNumber)
                {
                    return true;
                }

                // List of country codes that PhoneNumberUtil are confident to be correct
                var countryCodes = new List<string> { "1", "44", "1", "61", "33", "49", "81", "86", "91", "55" };

                if (countryCodes.Contains(phone.CountryCode.ToString()))
                {
                    return false;
                }

                // PhoneNumberUtil are less confident, use regular expression to validate
                // Regular expression pattern for WhatsApp ID validation
                var pattern = @"^\+[1-9]\d{8,19}$";

                // Check if the WhatsApp ID matches the pattern
                return Regex.IsMatch(normalizePhoneNumber, pattern);

            }
            catch
            {
                return false;
            }
        }

        public static bool IsE164FormatPhoneNumber(string phoneNumber)
        {
            if (string.IsNullOrWhiteSpace(phoneNumber))
            {
                return false;
            }

            // E.164 format: +[country code][number]
            // Regular expression to match E.164 format
            const string e164Pattern = @"^\+[1-9]\d{1,14}$";

            return Regex.IsMatch(phoneNumber, e164Pattern);
        }

        public static bool IsValidE164FormatPhoneNumber(string phoneNumber)
        {
            if (string.IsNullOrWhiteSpace(phoneNumber))
            {
                return false;
            }

            if(!IsE164FormatPhoneNumber(phoneNumber))
            {
                return false;
            }

            try
            {
                var phoneNumberUtil = PhoneNumberUtil.GetInstance();
                var phoneNumberProto = phoneNumberUtil.Parse(phoneNumber, null);

                // Check if the number is valid for its region
                if (!phoneNumberUtil.IsValidNumber(phoneNumberProto))
                {
                    return false;
                }

                // Check if the formatted number matches the input
                string formattedNumber = phoneNumberUtil.Format(phoneNumberProto, PhoneNumberFormat.E164);
                return formattedNumber == phoneNumber;
            }
            catch (NumberParseException)
            {
                return false;
            }
        }

        public class ParsePhoneNumberCountryCodeResult
        {
            public bool IsValid { get; set; }

            public string PhoneNumberWithCountryCode { get; set; }
        }

        public static ParsePhoneNumberCountryCodeResult ParsePhoneNumberValidForCountryName(
            string phoneNumber,
            string countryName)
        {
            var countryInfo = CountryCodeInfoList.Value.FirstOrDefault(x => x.CountryName == countryName);
            if (countryInfo == null)
            {
                return new ParsePhoneNumberCountryCodeResult()
                {
                    IsValid = false, PhoneNumberWithCountryCode = null
                };
            }

            PhoneNumber phone = PhoneUtil.Parse(phoneNumber, countryInfo.IsoCode2);
            if (!PhoneUtil.IsValidNumber(phone))
            {
                if (phone.CountryCode.ToString() == "55" && phone.NationalNumber.ToString().Length == 10)
                {
                    return new ParsePhoneNumberCountryCodeResult()
                    {
                        IsValid = true, PhoneNumberWithCountryCode = $"{phone.CountryCode}{phone.NationalNumber}"
                    };
                }

                return new ParsePhoneNumberCountryCodeResult()
                {
                    IsValid = false, PhoneNumberWithCountryCode = null
                };
            }

            return new ParsePhoneNumberCountryCodeResult()
            {
                IsValid = true, PhoneNumberWithCountryCode = $"{phone.CountryCode}{phone.NationalNumber}"
            };
        }

        private static ParsePhoneNumberCountryCodeResult ParsePhoneNumberValidForCountryId(
            string phoneNumber,
            string countryId)
        {
            PhoneNumber phone = PhoneUtil.Parse(phoneNumber, countryId);
            if (!PhoneUtil.IsValidNumber(phone))
            {
                return new ParsePhoneNumberCountryCodeResult()
                {
                    IsValid = false, PhoneNumberWithCountryCode = null
                };
            }

            return new ParsePhoneNumberCountryCodeResult()
            {
                IsValid = true, PhoneNumberWithCountryCode = $"{phone.CountryCode}{phone.NationalNumber}"
            };
        }

        public static string FormatPhoneNumber(string phoneNumberStr)
        {
            try
            {
                if (!string.IsNullOrEmpty(phoneNumberStr))
                {
                    var phoneNumberUtil = PhoneNumberUtil.GetInstance();
                    var phoneNumber = phoneNumberUtil.Parse($"+{phoneNumberStr}", null);
                    if (phoneNumberUtil.IsValidNumberForRegion(phoneNumber, GetCountryCode(phoneNumber)))
                    {
                        phoneNumberStr = $"{phoneNumber.CountryCode}{phoneNumber.NationalNumber}";
                    }
                }
            }
            catch
            {
                // ignored
            }

            return phoneNumberStr;
        }

        public static string MaskingPhoneNumber(string phoneNumberStr)
        {
            phoneNumberStr = phoneNumberStr?.Length switch
            {
                >= 4 => phoneNumberStr[..^4] + "****",
                > 0 => new string('*', phoneNumberStr.Length),
                _ => "****"
            };

            return phoneNumberStr;
        }

        public static string MaskingEmail(string emailStr)
        {
            emailStr = emailStr?.Length switch
            {
                >= 4 => $"****{emailStr[4..]}",
                > 0 => new string('*', emailStr.Length),
                _ => "****"
            };

            return emailStr;
        }

        public static string ToE164Format(string phoneNumber)
        {
            if (string.IsNullOrEmpty(phoneNumber))
            {
                return phoneNumber;
            }

            var formattedPhoneNumber = phoneNumber.Replace("+", string.Empty);

            var isAllDigit = Regex.IsMatch(formattedPhoneNumber, @"^\d+$");

            try
            {
                var phoneNumberUtil = PhoneNumberUtil.GetInstance();
                phoneNumberUtil.Parse($"+{phoneNumber}", null);
            }
            catch
            {
                // Phone number parsing failed, return original number
                // Since Twilio have different type of channel address -> Reference: https://www.twilio.com/docs/messaging/channels#understand-channel-addresses
                // SMS/MMS -> +1234567890
                // RCS -> brand_name_n9c2bvqq_agent
                // WhatsApp -> whatsapp:+1234567890
                // Facebook Messenger -> XYZXYZXYZ (messenger_page_id or messenger_user_id)
                return isAllDigit ? $"+{phoneNumber}" : phoneNumber;
            }

            try
            {
                var normalisedPhoneNumber = NormalizeWhatsappPhoneNumber(phoneNumber);
                return $"+{normalisedPhoneNumber}";
            }
            catch
            {
                // WhatsApp normalization failed, return the parsed phone number
            }

            return isAllDigit ? $"+{phoneNumber}" : phoneNumber;
        }
    }
}