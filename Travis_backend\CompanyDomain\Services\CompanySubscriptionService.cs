using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Travis_backend.Commons.Models;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.CompanyDomain.Repositories;
using Travis_backend.Constants;
using Travis_backend.Database;
using Travis_backend.Enums;
using Travis_backend.SubscriptionPlanDomain.Enums;

namespace Travis_backend.CompanyDomain.Services
{
    public interface ICompanySubscriptionService
    {
        /// <summary>
        /// Get Company Current Base Subscription BillRecord.
        /// </summary>
        /// <param name="companyId">Company Id.</param>
        /// <returns>Base Subscription BillRecord.</returns>
        Task<BillRecord> GetCurrentBaseSubscriptionBillRecord(string companyId);

        Task<SubscriptionTier?> GetCompanySubscriptionTierAsync(string companyId);

        Task<bool> IsCompanySubscriptionRecord(string companyId, string stripePlanId);

        Task<bool> IsHubspotIntegrationFreeTrialEligible(string companyId);

        Task<bool> IsSalesforceCrmFreeTrialEligible(string companyId);

        /// <summary>
        /// Get Valid BillRecords with 'Active' Status.
        /// </summary>
        /// <param name="companyId">Company Id.</param>
        /// <param name="extraPredication">Optional predication for query.</param>
        /// <param name="afterQueryPredicate">Optional predication to filter data after query from DB.</param>
        /// <returns>Collection of BillRecords.</returns>
        Task<IEnumerable<BillRecord>> GetActiveBillRecordsAsync(
            string companyId,
            Expression<Func<BillRecord, bool>> extraPredication = null,
            Func<BillRecord, bool> afterQueryPredicate = null);

        /// <summary>
        /// Get company subscribed add-on BillRecord.
        /// </summary>
        /// <param name="companyId">Company Id.</param>
        /// <param name="type">Subscription Plan Type.</param>
        /// <returns>Collection of BillRecord.</returns>
        Task<IEnumerable<BillRecord>> GetSubscribedAddOnBillRecords(string companyId, SubscriptionPlanType type);

        /// <summary>
        /// Get Company's Stripe Checkout CustomerId.
        /// </summary>
        /// <param name="companyId">CompanyId.</param>
        /// <returns>Stripe's Customer Id.</returns>
        Task<string> GetStripeCheckoutCustomerId(string companyId);

        /// <summary>
        /// Retrieve SleekFlow's Stripe Invoice Footer.
        /// </summary>
        /// <param name="countryCode">Country Code.</param>
        /// <returns>String of Invoice Footer.</returns>
        Task<string> GetSleekFlowStripeInvoiceFooter(string countryCode);

        /// <summary>
        /// Set BillRecord Cancellation DateTime.
        /// </summary>
        /// <param name="subscriptionId">Subscription Id.</param>
        /// <param name="subscriptionPlanId">Subscription Plan Id.</param>
        /// <param name="dateTime">Cancel DateTime.</param>
        /// <returns>Number of Affected Row.</returns>
        Task<int> SetBillRecordCancellationDateTime(string subscriptionId, string subscriptionPlanId, DateTime dateTime);

        /// <summary>
        /// Get Company's Monthly Usage Cycle.
        /// </summary>
        /// <param name="companyId">Company Id.</param>
        /// <returns>Usage Cycle in Tuple of DateTime.</returns>
        Task<DateTimeRange> GetMonthlyUsageCycleAsync(string companyId);

        /// <summary>
        /// Set Stripe Subscription's BillRecords status to 'Terminate'.
        /// </summary>
        /// <param name="companyId">Company Id.</param>
        /// <param name="stripeSubscriptionId">Stripe Subscription Id.</param>
        /// <param name="stripeSubscriptionItemId">Stripe SubscriptionItem Id.</param>
        /// <returns>Affected Rows.</returns>
        Task<int> TerminateBillRecords(string companyId, string stripeSubscriptionId, string stripeSubscriptionItemId = null);
    }

    public class CompanySubscriptionService : ICompanySubscriptionService
    {
        /// <summary>
        /// ICompanyRepository.
        /// </summary>
        private readonly ICompanyRepository _companyRepository;

        /// <summary>
        /// ICompanyBillRecordRepository.
        /// </summary>
        private readonly ICompanyBillRecordRepository _companyBillRecordRepository;

        /// <summary>
        /// ICompanyRegionalInfoRepository.
        /// </summary>
        private readonly ICompanyRegionalInfoRepository _companyRegionalInfoRepository;

        /// <summary>
        /// IUsageCycleCalculator.
        /// </summary>
        private readonly IUsageCycleCalculator _usageCycleCalculator;

        private readonly ApplicationDbContext _appDbContext;

        private readonly ILogger<CompanySubscriptionService> _logger;

        public CompanySubscriptionService(
            ICompanyRepository companyRepository,
            ICompanyBillRecordRepository companyBillRecordRepository,
            ICompanyRegionalInfoRepository companyRegionalInfoRepository,
            IUsageCycleCalculator usageCycleCalculator,
            ApplicationDbContext applicationDbContext,
            ILogger<CompanySubscriptionService> logger)
        {
            _companyRepository = companyRepository;
            _companyBillRecordRepository = companyBillRecordRepository;
            _companyRegionalInfoRepository = companyRegionalInfoRepository;
            _usageCycleCalculator = usageCycleCalculator;
            _appDbContext = applicationDbContext;
            _logger = logger;
        }

        /// <inheritdoc />
        public async Task<BillRecord> GetCurrentBaseSubscriptionBillRecord(string companyId)
        {
            return await _companyBillRecordRepository.GetCompanyCurrentBaseSubscriptionBillRecord(companyId);
        }

        public async Task<SubscriptionTier?> GetCompanySubscriptionTierAsync(string companyId)
        {
            var subscriptionPlanBillRecord = await _appDbContext.CompanyBillRecords
                .Include(br => br.SubscriptionPlan)
                .OrderByDescending(br => br.created)
                .ThenByDescending(br => br.PayAmount)
                .FirstOrDefaultAsync(
                    br => br.CompanyId == companyId
                          && br.Status != BillStatus.Inactive
                          && br.Status != BillStatus.Terminated
                          && ValidSubscriptionPlan.SubscriptionPlan.Contains(br.SubscriptionPlanId)
                          && br.PeriodStart < DateTime.UtcNow);

            return subscriptionPlanBillRecord?.SubscriptionPlan.SubscriptionTier;
        }

        public Task<bool> IsCompanySubscriptionRecord(string companyId, string stripePlanId)
            => _appDbContext.CompanyBillRecords
                .AnyAsync(
                    x =>
                        x.CompanyId == companyId
                        && x.SubscriptionPlanId == stripePlanId);

        public async Task<bool> IsHubspotIntegrationFreeTrialEligible(string companyId)
        {
            var isAnyHubspotBillRecord = await _appDbContext.CompanyBillRecords
                .Include(x => x.SubscriptionPlan)
                .AnyAsync(
                    x =>
                        x.CompanyId == companyId
                        && ValidSubscriptionPlan.HubspotIntegrationAddOns.Contains(x.SubscriptionPlanId));

            return !isAnyHubspotBillRecord;
        }

        public async Task<bool> IsSalesforceCrmFreeTrialEligible(string companyId)
        {
            var isAnySalesforceCrmBillRecord = await _appDbContext.CompanyBillRecords
                .Include(x => x.SubscriptionPlan)
                .AnyAsync(
                    x =>
                        x.CompanyId == companyId
                        && ValidSubscriptionPlan.SalesforceCrmIntegrationAddOns.Contains(x.SubscriptionPlanId));

            return !isAnySalesforceCrmBillRecord;
        }

        /// <inheritdoc />
        public async Task<IEnumerable<BillRecord>> GetActiveBillRecordsAsync(
            string companyId,
            Expression<Func<BillRecord, bool>> extraPredication = null,
            Func<BillRecord, bool> afterQueryPredicate = null)
        {
            var queryResult = await _companyBillRecordRepository.GetActiveBillRecordAsync(companyId, extraPredication);
            return afterQueryPredicate != null ? queryResult.Where(afterQueryPredicate) : queryResult;
        }

        /// <inheritdoc />
        public async Task<IEnumerable<BillRecord>> GetSubscribedAddOnBillRecords(string companyId, SubscriptionPlanType type)
        {
            if (type == SubscriptionPlanType.BasePlan)
            {
                throw new ArgumentException("Invalid SubscriptionPlanType");
            }

            if (type == SubscriptionPlanType.Agent)
            {
                //// Using SubscriptionTier as condition to reduce SQL query size.
                return await _companyBillRecordRepository.FindActiveByCompanyIdAndPlanTier(companyId, SubscriptionTier.Agent);
            }

            //// Add-On are not identifiable by using SubscriptionTier, use whitelist to query
            var subscriptionPlanIds = GetSubscriptionPlanIds(type);
            return await _companyBillRecordRepository.FindActiveByCompanyIdAndPlanIds(companyId, subscriptionPlanIds);
        }

        /// <inheritdoc />
        public async Task<string> GetStripeCheckoutCustomerId(string companyId)
        {
            var company = await _companyRepository.FindById(companyId);

            if (!string.IsNullOrWhiteSpace(company.AffiliateCustomerId))
            {
                return company.AffiliateCustomerId;
            }

            var lastValidPaymentBillRecord = await _companyBillRecordRepository.GetLastValidPaymentBillRecord(companyId);

            return lastValidPaymentBillRecord?.customerId;
        }

        /// <inheritdoc />
        public async Task<string> GetSleekFlowStripeInvoiceFooter(string countryCode)
        {
            var regionalInfo = await _companyRegionalInfoRepository.FindSleekFlowRegionalInfoByCountryCode(countryCode);

            if (regionalInfo == null)
            {
                return null;
            }

            return "Bill from \n" +
                   $"Company: {regionalInfo.RegisteredCompanyName} \n" +
                   $"Company Registered Number: {regionalInfo.RegisteredBusinessNumber} \n" +
                   $"Company Address: {regionalInfo.CompanyAddress}";
        }

        /// <inheritdoc />
        public async Task<int> SetBillRecordCancellationDateTime(string subscriptionId, string subscriptionPlanId, DateTime dateTime)
        {
            var billRecord = await _companyBillRecordRepository.GetLatestStripeSubscriptionBillRecord(subscriptionId, subscriptionPlanId);

            _logger.LogInformation(
                "Update Stripe Subscription Cancel DateTime. StripeSubscriptionId: {StripeSubscriptionId}, SubscriptionPlanId: {SubscriptionPlanId} BillRecordId: {BillRecordId}",
                subscriptionId,
                subscriptionPlanId,
                billRecord.Id);

            return await _companyBillRecordRepository.UpdateStipeSubscriptionCancelDateTime(billRecord.Id, dateTime);
        }

        /// <inheritdoc />
        public async Task<DateTimeRange> GetMonthlyUsageCycleAsync(string companyId)
        {
            var billRecord = await GetCurrentBaseSubscriptionBillRecord(companyId);

            if (billRecord == null)
            {
                //// Handling for various cases like client failed to pay for new period and the startup plan unable to generate without login or access in PowerFlow.
                var now = DateTime.UtcNow;
                return new(now, now.AddMonths(1));
            }
            else if (billRecord.IsUsageCycleApplied)
            {
                return new(billRecord.UsageCycleStart!.Value, billRecord.UsageCycleEnd!.Value);
            }

            var usageCycle = _usageCycleCalculator.GetUsageCycle(billRecord.PeriodStart, billRecord.PeriodEnd);
            return new (usageCycle.From, usageCycle.To);
        }

        /// <inheritdoc />
        public async Task<int> TerminateBillRecords(string companyId, string stripeSubscriptionId, string stripeSubscriptionItemId = null)
        {
            ArgumentException.ThrowIfNullOrEmpty(stripeSubscriptionId);

            return await _companyBillRecordRepository.TerminateBillRecords(companyId, stripeSubscriptionId, stripeSubscriptionItemId);
        }

        private IEnumerable<string> GetSubscriptionPlanIds(SubscriptionPlanType type)
        {
            return type switch
            {
                SubscriptionPlanType.BasePlan => ValidSubscriptionPlan.SubscriptionPlan,
                SubscriptionPlanType.Agent => ValidSubscriptionPlan.AgentPlan,
                SubscriptionPlanType.Contact => ValidSubscriptionPlan.AdditionalContactAddOns,
                _ => throw new NotSupportedException($"Not Supported SubscriptionPlanType: {type.ToString()}")
            };
        }
    }
}