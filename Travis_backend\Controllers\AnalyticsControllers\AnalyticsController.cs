﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using AutoMapper;
using Hangfire;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Travis_backend.AccountAuthenticationDomain.Models;
using Travis_backend.AnalyticsDomain.Models;
using Travis_backend.AnalyticsDomain.Services;
using Travis_backend.AnalyticsDomain.ViewModels;
using Travis_backend.BackgroundTaskServices;
using Travis_backend.BackgroundTaskServices.Helpers;
using Travis_backend.CommonDomain.ViewModels;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.CompanyDomain.Services;
using Travis_backend.Constants;
using Travis_backend.ContactDomain.Services;
using Travis_backend.ContactDomain.ViewModels;
using Travis_backend.ConversationDomain.ViewModels;
using Travis_backend.ConversationServices;
using Travis_backend.Data.BackgroundTask;
using Travis_backend.Database;
using Travis_backend.Enums;
using Travis_backend.Helpers;


namespace Travis_backend.Controllers.AnalyticsControllers
{
    [Authorize]
    [Route("Company/[controller]")]
    public class AnalyticsController : Controller
    {
        private readonly ApplicationDbContext _appDbContext;
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly SignInManager<ApplicationUser> _signInManager;
        private readonly IMapper _mapper;
        private readonly IEmailNotificationService _emailNotificationService;
        private readonly ILogger _logger;
        private readonly IConfiguration _configuration;
        private readonly ICompanyService _companyService;
        private readonly IConversationMessageService _conversationMessageService;
        private readonly IUserProfileService _userProfileService;
        private readonly IAnalyticsService _analyticsService;
        private readonly IBackgroundTaskService _backgroundTaskService;
        private readonly ICoreService _coreService;

        public AnalyticsController(
            ApplicationDbContext appDbContext,
            UserManager<ApplicationUser> userManager,
            SignInManager<ApplicationUser> signInManager,
            IMapper mapper,
            IEmailNotificationService emailNotificationService,
            IConfiguration configuration,
            ILogger<AnalyticsController> logger,
            ICompanyService companyService,
            IConversationMessageService conversationMessageService,
            IUserProfileService userProfileService,
            IAnalyticsService analyticsService,
            IBackgroundTaskService backgroundTaskService,
            ICoreService coreService)
        {
            _userManager = userManager;
            _signInManager = signInManager;
            _appDbContext = appDbContext;
            _mapper = mapper;
            _emailNotificationService = emailNotificationService;
            _configuration = configuration;
            _logger = logger;
            _companyService = companyService;
            _conversationMessageService = conversationMessageService;
            _userProfileService = userProfileService;
            _analyticsService = analyticsService;
            _backgroundTaskService = backgroundTaskService;
            _coreService = coreService;
        }

        [HttpPost]
        [Route("data")]
        public async Task<IActionResult> GetDataResult(
            [FromQuery(Name = "StaffId")]
            string staffId = null,
            [FromQuery(Name = "status")]
            string status = null,
            [FromQuery(Name = "channel")]
            string channels = null,
            [FromQuery(Name = "from")]
            DateTime? messageFrom = null,
            [FromQuery(Name = "to")]
            DateTime? messageTo = null,
            [FromQuery(Name = "channelIds")]
            string channelIds = null,
            [FromQuery(Name = "tags")]
            string tags = null,
            [FromQuery(Name = "teamId")]
            long? teamId = null,
            [FromQuery(Name = "isTeamUnassigned")]
            bool? isTeamUnassigned = null,
            [FromBody]
            List<Condition> conditions = null)
        {
            // var companyUser = await _appDbContext.UserRoleStaffs.Where(x => x.IdentityId == userId).Include(x => x.RegisteredSessions).FirstOrDefaultAsync();
            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            var result = await _analyticsService.GetSummaryData(
                companyUser.CompanyId,
                staffId,
                teamId,
                messageFrom,
                messageTo,
                conditions);
            var response = _mapper.Map<AnalyticsResponse>(result);

            return Ok(response);
        }

        [HttpPost]
        [Route("export/background")]
        public async Task<ActionResult<BackgroundTaskViewModel>> ExportStatisticsDataInBackground(
            [FromQuery(Name = "StaffId")]
            string staffId = null,
            [FromQuery(Name = "status")]
            string status = null,
            [FromQuery(Name = "channel")]
            string channels = null,
            [FromQuery(Name = "from")]
            DateTime? messageFrom = null,
            [FromQuery(Name = "to")]
            DateTime? messageTo = null,
            [FromQuery(Name = "channelIds")]
            string channelIds = null,
            [FromQuery(Name = "tags")]
            string tags = null,
            [FromQuery(Name = "teamId")]
            long? teamId = null,
            [FromQuery(Name = "isTeamUnassigned")]
            bool? isTeamUnassigned = null,
            [FromBody]
            List<Condition> conditions = null)
        {
            // var companyUser = await _appDbContext.UserRoleStaffs.Where(x => x.IdentityId == userId).Include(x => x.Company).FirstOrDefaultAsync();
            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            if (companyUser == null)
            {
                return Unauthorized();
            }

            if (!messageFrom.HasValue && !messageTo.HasValue)
            {
                var now = DateTime.UtcNow;
                var startOfMonth = new DateTime(now.Year, now.Month, 1);

                messageFrom = companyUser.Company.CreatedAt > startOfMonth
                    ? companyUser.Company.CreatedAt
                    : startOfMonth;
                messageTo = now;
            }

            var backgroundTask = await _backgroundTaskService.EnqueueExportStatisticsDataToCsvTask(
                companyUser.IdentityId,
                companyUser.CompanyId,
                companyUser.Id,
                staffId,
                messageFrom.Value,
                messageTo.Value,
                status,
                channels,
                channelIds,
                tags,
                teamId,
                isTeamUnassigned,
                conditions);

            return Ok(backgroundTask.MapToResultViewModel());
        }

        [HttpPost]
        [Route("segment/create")]
        public async Task<IActionResult> CreateSegment([FromBody] Segment segment)
        {
            try
            {
                var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

                if (companyUser == null)
                {
                    return Unauthorized();
                }

                segment.CompanyId = companyUser.CompanyId;
                segment.SavedById = companyUser.Id;
                _appDbContext.CompanyAnalyticSegment.Add(segment);
                await _appDbContext.SaveChangesAsync();

                var responseViewModel = _mapper.Map<SegmentResponse>(segment);
                return Ok(responseViewModel);
            }
            catch (Exception ex)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        message = ex.Message
                    });
            }
        }

        [HttpGet]
        [Route("segment")]
        public async Task<IActionResult> ListExistingSegment()
        {
            try
            {
                var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

                if (companyUser == null)
                {
                    return Unauthorized();
                }

                var segments = await _appDbContext.CompanyAnalyticSegment
                    .Where(x => x.CompanyId == companyUser.CompanyId).ToListAsync(HttpContext.RequestAborted);

                var responseViewModel = _mapper.Map<List<SegmentResponse>>(segments);
                return Ok(responseViewModel);
            }
            catch (Exception ex)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        message = ex.Message
                    });
            }
        }

        [HttpPut]
        [Route("segment/update/{segmentId}")]
        public async Task<IActionResult> UpdateSegment(long segmentId, [FromBody] Segment newSegment)
        {
            try
            {
                var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

                if (companyUser == null)
                {
                    return Unauthorized();
                }

                var segment = await _appDbContext.CompanyAnalyticSegment
                    .Where(x => x.CompanyId == companyUser.CompanyId && x.Id == segmentId).FirstOrDefaultAsync();

                segment.Conditions = newSegment.Conditions;
                segment.Name = newSegment.Name;
                segment.UpdatedAt = DateTime.UtcNow;
                segment.Status = newSegment.Status;
                segment.SavedById = companyUser.Id;
                await _appDbContext.SaveChangesAsync();

                var responseViewModel = _mapper.Map<SegmentResponse>(segment);
                return Ok(responseViewModel);
            }
            catch (Exception ex)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        message = ex.Message
                    });
            }
        }

        [HttpDelete]
        [Route("segment/{segmentId}")]
        public async Task<IActionResult> DeleteSegment(long segmentId)
        {
            try
            {
                var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

                if (companyUser == null)
                {
                    return Unauthorized();
                }

                var segment = await _appDbContext.CompanyAnalyticSegment
                    .Where(x => x.CompanyId == companyUser.CompanyId && x.Id == segmentId).FirstOrDefaultAsync();
                _appDbContext.CompanyAnalyticSegment.Remove(segment);
                await _appDbContext.SaveChangesAsync();

                return Ok(
                    new ResponseViewModel
                    {
                        message = "deleted"
                    });
            }
            catch (Exception ex)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        message = ex.Message
                    });
            }
        }

#if DEBUG
        public class AnalyticsEmailNotificationRegistrationField
        {
            public string EmailAddress { get; set; }

            public string Param { get; set; }
        }
#endif
    }
}