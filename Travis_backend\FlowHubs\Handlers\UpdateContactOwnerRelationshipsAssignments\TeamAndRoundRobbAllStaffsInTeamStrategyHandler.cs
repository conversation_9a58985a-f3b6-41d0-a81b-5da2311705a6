﻿using System;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Sleekflow.Apis.FlowHub.Model;
using Travis_backend.CompanyDomain.Services;
using Travis_backend.ContactDomain.Services;
using Travis_backend.ConversationServices;
using Travis_backend.FlowHubs.Constants;
using Travis_backend.FlowHubs.Exceptions;
using Travis_backend.FlowHubs.Interfaces;

namespace Travis_backend.FlowHubs.Handlers.UpdateContactOwnerRelationshipsAssignments;

public sealed class TeamAndRoundRobbAllStaffsInTeamStrategyHandler : IUpdateContactOwnerRelationshipsAssignmentStrategyHandler
{
    private readonly ICompanyTeamService _companyTeamService;
    private readonly IUserProfileService _userProfileService;
    private readonly IConversationMessageService _conversationMessageService;
    private readonly ILogger<TeamAndRoundRobbAllStaffsInTeamStrategyHandler> _logger;

    public string StrategyName => UpdateContactOwnerRelationshipsAssignmentStrategy.Team_And_RoundRobbin_All_Staffs_In_Team;

    public TeamAndRoundRobbAllStaffsInTeamStrategyHandler(
        ICompanyTeamService companyTeamService,
        IUserProfileService userProfileService,
        IConversationMessageService conversationMessageService,
        ILogger<TeamAndRoundRobbAllStaffsInTeamStrategyHandler> logger)
    {
        _companyTeamService = companyTeamService;
        _userProfileService = userProfileService;
        _conversationMessageService = conversationMessageService;
        _logger = logger;
    }

    public async Task HandleAsync(UpdateContactOwnerRelationshipsInput input)
    {
        var workflowVersionedId = input.StateIdentity.WorkflowVersionedId;
        var companyId = input.StateIdentity.SleekflowCompanyId;
        var contactId = input.ContactId;
        var teamId = long.Parse(input.TeamId);
        var assignmentCounter = input.AssignmentCounter!.Value;

        var conversation = await _userProfileService.GetConversationByUserProfileId(
            companyId,
            contactId,
            status: "closed");

        var team = await _companyTeamService.GetCompanyTeamFromTeamId(companyId, teamId);

        if (team is null)
        {
            throw new CompanyTeamNotFoundException(
                workflowVersionedId,
                companyId,
                teamId);
        }

        await _conversationMessageService.ChangeConversationAssignedTeam(
            conversation,
            team,
            isTriggerUpdate: true,
            changedBy: null);

        var activeTeamMemberStaffs = await _companyTeamService.GetActiveTeamMembersAsync(teamId);

        if (activeTeamMemberStaffs.Count == 0)
        {
            _logger.LogWarning(
                "[{HandlerName}] Failed to assign {UserProfileId} for workflow {WorkflowVersionedId} due to no active members in team {TeamId} of company {CompanyId}",
                nameof(TeamAndRoundRobbAllStaffsInTeamStrategyHandler),
                contactId,
                workflowVersionedId,
                team.Id,
                companyId);

            return;
        }

        var idx = Convert.ToInt32(assignmentCounter % activeTeamMemberStaffs.Count);

        await _conversationMessageService.ChangeConversationAssignee(
            conversation,
            assignee: activeTeamMemberStaffs[idx]);
    }
}