﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Travis_backend.Database;
using Travis_backend.Extensions;
using Travis_backend.InternalDomain.Models;

namespace Travis_backend.InternalDomain.Services;

public interface IInternalSquadMetricsRepository
{
    #region BugBq

    Task<List<BugbqMessageMetricByCompany>> GetMessageVolumeByCompany(string period, DateTime start, DateTime end);

    Task<List<BugbqMessageMetric>> GetMessageVolume(string period, DateTime start, DateTime end);

    Task<List<BugbqMessageMetric>> GetFailedMessageVolume(string period, DateTime start, DateTime end);

    Task<List<BugbqMessageMetricByCompany>> GetFailedMessageVolumeByCompany(
        string period,
        DateTime start,
        DateTime end);

    Task<List<BugbqMessageMetricByCompanySummary>> GetMessageVolumeByCompanySummary(
        string period,
        DateTime start,
        DateTime end);

    Task<List<BugbqMessageMetric>> GetPaidCustomerMessageVolume(string period, DateTime start, DateTime end);

    #endregion
}

public class InternalSquadMetricsRepository : IInternalSquadMetricsRepository
{
    private readonly ApplicationDbContext _appDbContext;
    private readonly ILogger<InternalSquadMetricsRepository> _logger;

    public InternalSquadMetricsRepository(
        ApplicationDbContext appDbContext,
        ILogger<InternalSquadMetricsRepository> logger)
    {
        _appDbContext = appDbContext;
        _logger = logger;
    }

    public async Task<List<BugbqMessageMetric>> GetMessageVolume(string period, DateTime start, DateTime end)
    {
        var sql = $"""
                   select '{period}' as period,
                          r.channel,
                          IIF(r.IsSentFromSleekflow = 1, 'Sent', 'Received') as Direction,
                          case
                              when DeliveryType = 0 then 'Inbox'
                              when DeliveryType = 1 then 'Broadcast'
                              when DeliveryType = 2 then 'AutomatedMessage'
                              when DeliveryType = 5  then 'FlowHubAction'
                              when DeliveryType = 3 then 'ReadMore'
                              when DeliveryType = 4 then 'QuickReply'
                              when DeliveryType = 90 then 'PaymentLink'
                              end                                            as DeliveryType,
                          r.Count                                            as MessageCount
                   from (select channel, IsSentFromSleekflow, DeliveryType, count(*) as Count
                         from ConversationMessages cm
                         where timestamp >= {start.ToUnixTime()}
                           and Timestamp < {end.ToUnixTime()}
                         group by channel, IsSentFromSleekflow, DeliveryType) as r
                   where r.channel in (
                                       'instagram',
                                       'facebook',
                                       'whatsapp',
                                       'whatsapp360dialog',
                                       'whatsappcloudapi',
                                       'web',
                                       'line',
                                       'sms',
                                       'telegram',
                                       'viber',
                                       'wechat',
                                       'email');
                   """;

        var result = await RawSqlQuery(
            sql,
            reader => new BugbqMessageMetric
            {
                Period = reader[0].ToString(),
                Channel = reader[1].ToString(),
                Direction = reader[2].ToString(),
                DeliveryType = reader[3].ToString(),
                MessageCount = (int) reader[4],
            });

        return result;
    }

    public async Task<List<BugbqMessageMetricByCompany>> GetMessageVolumeByCompany(
        string period,
        DateTime start,
        DateTime end)
    {
        var sql = $"""
                   select '{period}' as period,
                          r.CompanyId,
                          cc.CompanyName,
                          r.channel,
                          IIF(r.IsSentFromSleekflow = 1, 'Sent', 'Received') as Direction,
                          case
                              when DeliveryType = 0 then 'Inbox'
                              when DeliveryType = 1 then 'Broadcast'
                              when DeliveryType = 2 then 'AutomatedMessage'
                              when DeliveryType = 3 then 'ReadMore'
                              when DeliveryType = 4 then 'QuickReply'
                              when DeliveryType = 5 then 'FlowHubAction'
                              when DeliveryType = 90 then 'PaymentLink'
                              end                                            as DeliveryType,
                          r.Count                                            as MessageCount
                   from (select CompanyId, channel, IsSentFromSleekflow, DeliveryType, count(*) as Count
                         from ConversationMessages cm
                         where timestamp >= {start.ToUnixTime()}
                           and Timestamp < {end.ToUnixTime()}
                         group by CompanyId, channel, IsSentFromSleekflow, DeliveryType) as r
                   left join CompanyCompanies cc on cc.Id = r.CompanyId
                   where r.channel in (
                                       'instagram',
                                       'facebook',
                                       'whatsapp',
                                       'whatsapp360dialog',
                                       'whatsappcloudapi',
                                       'web',
                                       'line',
                                       'sms',
                                       'telegram',
                                       'viber',
                                       'wechat',
                                       'email');
                   """;

        var result = await RawSqlQuery(
            sql,
            reader => new BugbqMessageMetricByCompany
            {
                Period = reader[0].ToString(),
                CompanyId = reader[1].ToString(),
                CompanyName = reader[2].ToString(),
                Channel = reader[3].ToString(),
                Direction = reader[4].ToString(),
                DeliveryType = reader[5].ToString(),
                MessageCount = (int) reader[6],
            });

        return result;
    }

    public async Task<List<BugbqMessageMetric>> GetFailedMessageVolume(string period, DateTime start, DateTime end)
    {
        var sql = $"""
                   select '{period}' as period,
                          r.CompanyId,
                          cc.CompanyName,
                          r.channel,
                          IIF(r.IsSentFromSleekflow = 1, 'Sent', 'Received') as Direction,
                          case
                              when DeliveryType = 0 then 'Inbox'
                              when DeliveryType = 1 then 'Broadcast'
                              when DeliveryType = 2 then 'AutomatedMessage'
                              when DeliveryType = 5  then 'FlowHubAction'
                              when DeliveryType = 3 then 'ReadMore'
                              when DeliveryType = 4 then 'QuickReply'
                              when DeliveryType = 90 then 'PaymentLink'
                              end                                            as DeliveryType,
                          r.Count                                            as MessageCount
                   from (select CompanyId, channel, IsSentFromSleekflow, DeliveryType, count(*) as Count
                         from ConversationMessages cm
                         where timestamp >= {start.ToUnixTime()}
                           and Timestamp < {end.ToUnixTime()}
                         group by CompanyId, channel, IsSentFromSleekflow, DeliveryType) as r
                   left join CompanyCompanies cc on cc.Id = r.CompanyId
                   where r.channel in (
                                       'instagram',
                                       'facebook',
                                       'whatsapp',
                                       'whatsapp360dialog',
                                       'whatsappcloudapi',
                                       'web',
                                       'line',
                                       'sms',
                                       'telegram',
                                       'viber',
                                       'wechat',
                                       'email');
                   """;

        var result = await RawSqlQuery(
            sql,
            reader => new BugbqMessageMetric
            {
                Period = reader[0].ToString(),
                Channel = reader[1].ToString(),
                Direction = reader[2].ToString(),
                DeliveryType = reader[3].ToString(),
                MessageCount = (int) reader[4],
            });

        return result;
    }

    public async Task<List<BugbqMessageMetricByCompany>> GetFailedMessageVolumeByCompany(
        string period,
        DateTime start,
        DateTime end)
    {
        var sql = $"""
                   select '{period}' as period,
                          r.CompanyId,
                          cc.CompanyName,
                          r.channel,
                          IIF(r.IsSentFromSleekflow = 1, 'Sent', 'Received') as Direction,
                          case
                              when DeliveryType = 0 then 'Inbox'
                              when DeliveryType = 1 then 'Broadcast'
                              when DeliveryType = 2 then 'AutomatedMessage'
                              when DeliveryType = 5  then 'FlowHubAction'
                              when DeliveryType = 3 then 'ReadMore'
                              when DeliveryType = 4 then 'QuickReply'
                              when DeliveryType = 90 then 'PaymentLink'
                              end                                            as DeliveryType,
                          r.Count                                            as MessageCount
                   from (select CompanyId, channel, IsSentFromSleekflow, DeliveryType, count(*) as Count
                         from ConversationMessages cm
                         where timestamp >= {start.ToUnixTime()}
                           and Timestamp < {end.ToUnixTime()}
                           and Status = 4
                         group by CompanyId, channel, IsSentFromSleekflow, DeliveryType) as r
                   left join CompanyCompanies cc on cc.Id = r.CompanyId
                   where r.channel in (
                                       'instagram',
                                       'facebook',
                                       'whatsapp',
                                       'whatsapp360dialog',
                                       'whatsappcloudapi',
                                       'web',
                                       'line',
                                       'sms',
                                       'telegram',
                                       'viber',
                                       'wechat',
                                       'email')
                   """;

        var result = await RawSqlQuery(
            sql,
            reader => new BugbqMessageMetricByCompany
            {
                Period = reader[0].ToString(),
                CompanyId = reader[1].ToString(),
                CompanyName = reader[2].ToString(),
                Channel = reader[3].ToString(),
                Direction = reader[4].ToString(),
                DeliveryType = reader[5].ToString(),
                MessageCount = (int) reader[6],
            });

        return result;
    }

    public async Task<List<BugbqMessageMetricByCompanySummary>> GetMessageVolumeByCompanySummary(
        string period,
        DateTime start,
        DateTime end)
    {
        var messageVolumeByCompany = await GetMessageVolumeByCompany(
            period,
            start,
            end);

        var failedMessageVolumeByCompany = await GetFailedMessageVolumeByCompany(
            period,
            start,
            end);

        var bugbqMessageMetricByCompanySummary = messageVolumeByCompany.Select(
                x => new BugbqMessageMetricByCompanySummary
                {
                    Period = x.Period,
                    CompanyId = x.CompanyId,
                    CompanyName = x.CompanyName,
                    Channel = x.Channel,
                    Direction = x.Direction,
                    DeliveryType = x.DeliveryType,
                    TotalMessageCount = x.MessageCount,
                    SuccessMessageCount = x.MessageCount,
                    FailedMessageCount = 0,
                })
            .ToList();

        foreach (var failedMessageVolumn in failedMessageVolumeByCompany)
        {
            var existingBugbqMessageMetricByCompany = bugbqMessageMetricByCompanySummary.FirstOrDefault(
                x =>
                     x.Period == failedMessageVolumn.Period &&
                     x.CompanyId == failedMessageVolumn.CompanyId &&
                     x.Channel == failedMessageVolumn.Channel &&
                     x.Direction == failedMessageVolumn.Direction &&
                     x.DeliveryType == failedMessageVolumn.DeliveryType);

            if (existingBugbqMessageMetricByCompany != null)
            {
                existingBugbqMessageMetricByCompany.TotalMessageCount += failedMessageVolumn.MessageCount;
                existingBugbqMessageMetricByCompany.FailedMessageCount = failedMessageVolumn.MessageCount;
            }
            else
            {
                bugbqMessageMetricByCompanySummary.Add(
                    new BugbqMessageMetricByCompanySummary
                    {
                        Period = failedMessageVolumn.Period,
                        CompanyId = failedMessageVolumn.CompanyId,
                        CompanyName = failedMessageVolumn.CompanyName,
                        Channel = failedMessageVolumn.Channel,
                        Direction = failedMessageVolumn.Direction,
                        DeliveryType = failedMessageVolumn.DeliveryType,
                        TotalMessageCount = failedMessageVolumn.MessageCount,
                        SuccessMessageCount = 0,
                        FailedMessageCount = failedMessageVolumn.MessageCount,
                    });
            }
        }

        return bugbqMessageMetricByCompanySummary;
    }

    public async Task<List<BugbqMessageMetric>> GetPaidCustomerMessageVolume(
        string period,
        DateTime start,
        DateTime end)
    {
        var sql = $"""
                   select '{period}' as period,
                          r.channel,
                          IIF(r.IsSentFromSleekflow = 1, 'Sent', 'Received') as Direction,
                          case
                              when DeliveryType = 0 then 'Inbox'
                              when DeliveryType = 1 then 'Broadcast'
                              when DeliveryType = 2 then 'AutomatedMessage'
                              when DeliveryType = 5  then 'FlowHubAction'
                              when DeliveryType = 3 then 'ReadMore'
                              when DeliveryType = 4 then 'QuickReply'
                              when DeliveryType = 90 then 'PaymentLink'
                              end                                            as DeliveryType,
                          r.Count                                            as MessageCount
                   from (select channel, IsSentFromSleekflow, DeliveryType, count(*) as Count
                         from ConversationMessages
                         where
                             CompanyId in (select distinct CompanyId
                                           from CompanyBillRecords
                                           where PeriodEnd >= ('{period}-01')
                                             and SubscriptionPlanId in (
                                                                        'sleekflow_pro',
                                                                        'sleekflow_enterprise',
                                                                        'sleekflow_v2_standard',
                                                                        'sleekflow_v2_pro',
                                                                        'sleekflow_v2_premium',
                                                                        'sleekflow_v3_standard',
                                                                        'sleekflow_v3_pro',
                                                                        'sleekflow_v3_premium',
                                                                        'sleekflow_v4_premium_monthly',
                                                                        'sleekflow_v4_premium_yearly',
                                                                        'sleekflow_v4_pro_yearly',
                                                                        'sleekflow_lalamove_operation',
                                                                        'sleekflow_lalamove_sales',
                                                                        'sleekflow_v5_pro_yearly',
                                                                        'sleekflow_v4_pro_yearly_hkd',
                                                                        'sleekflow_v6_pro_monthly',
                                                                        'sleekflow_v6_pro_yearly',
                                                                        'sleekflow_v6_premium_monthly',
                                                                        'sleekflow_v6_premium_yearly',
                                                                        'sleekflow_v7_pro_monthly',
                                                                        'sleekflow_v7_pro_yearly',
                                                                        'sleekflow_v7_premium_monthly',
                                                                        'sleekflow_v7_premium_yearly',
                                                                        'price_1JPNV5F8rFCN7uzNZ0rZkqSp',
                                                                        'price_1GshWTF8rFCN7uzNNRefZtTU',
                                                                        'price_1K9mdNF8rFCN7uzNA7mm3R7v',
                                                                        'price_1K9h5wF8rFCN7uzNJhxS8zps',
                                                                        'sleekflow_v7_premium_monthly_usd_custom_rglobalcar',
                                                                        'sleekflow_v8_pro_monthly',
                                                                        'sleekflow_v8_pro_yearly',
                                                                        'sleekflow_v8_premium_monthly',
                                                                        'sleekflow_v8_premium_yearly',
                                                                        'sleekflow_v8_pro_monthly_hkd',
                                                                        'sleekflow_v8_pro_yearly_hkd',
                                                                        'sleekflow_v8_premium_monthly_hkd',
                                                                        'sleekflow_v8_premium_yearly_hkd',
                                                                        'sleekflow_v8_pro_monthly_sgd',
                                                                        'sleekflow_v8_pro_yearly_sgd',
                                                                        'sleekflow_v8_premium_monthly_sgd',
                                                                        'sleekflow_v8_premium_yearly_sgd',
                                                                        'sleekflow_v8_pro_monthly_cny',
                                                                        'sleekflow_v8_pro_yearly_cny',
                                                                        'sleekflow_v8_premium_monthly_cny',
                                                                        'sleekflow_v8_premium_yearly_cny',
                                                                        'sleekflow_v8_pro_monthly_myr',
                                                                        'sleekflow_v8_pro_yearly_myr',
                                                                        'sleekflow_v8_premium_monthly_myr',
                                                                        'sleekflow_v8_premium_yearly_myr',
                                                                        'sleekflow_v8_pro_monthly_idr',
                                                                        'sleekflow_v8_pro_yearly_idr',
                                                                        'sleekflow_v8_premium_monthly_idr',
                                                                        'sleekflow_v8_premium_yearly_idr',
                                                                        'sleekflow_v8_pro_monthly_eur',
                                                                        'sleekflow_v8_pro_yearly_eur',
                                                                        'sleekflow_v8_premium_monthly_eur',
                                                                        'sleekflow_v8_premium_yearly_eur',
                                                                        'sleekflow_v8_agent_premium_monthy_eur',
                                                                        'sleekflow_v8_agent_premium_yearly_eur',
                                                                        'sleekflow_v8_pro_monthly_gbp',
                                                                        'sleekflow_v8_pro_yearly_gbp',
                                                                        'sleekflow_v8_premium_monthly_gbp',
                                                                        'sleekflow_v8_premium_yearly_gbp',
                                                                        'sleekflow_v8_pro_monthly_cad',
                                                                        'sleekflow_v8_pro_yearly_cad',
                                                                        'sleekflow_v8_premium_monthly_cad',
                                                                        'sleekflow_v8_premium_yearly_cad',
                                                                        'sleekflow_v8_pro_monthly_aud',
                                                                        'sleekflow_v8_pro_yearly_aud',
                                                                        'sleekflow_v8_premium_monthly_aud',
                                                                        'sleekflow_v8_premium_yearly_aud',
                                                                        'sleekflow_v9_pro_monthly',
                                                                        'sleekflow_v9_pro_yearly',
                                                                        'sleekflow_v9_premium_monthly',
                                                                        'sleekflow_v9_premium_yearly',
                                                                        'sleekflow_v9_pro_monthly_hkd',
                                                                        'sleekflow_v9_pro_yearly_hkd',
                                                                        'sleekflow_v9_premium_monthly_hkd',
                                                                        'sleekflow_v9_premium_yearly_hkd',
                                                                        'sleekflow_v9_pro_monthly_sgd',
                                                                        'sleekflow_v9_pro_yearly_sgd',
                                                                        'sleekflow_v9_premium_monthly_sgd',
                                                                        'sleekflow_v9_premium_yearly_sgd',
                                                                        'sleekflow_v9_pro_monthly_cny',
                                                                        'sleekflow_v9_pro_yearly_cny',
                                                                        'sleekflow_v9_premium_monthly_cny',
                                                                        'sleekflow_v9_premium_yearly_cny',
                                                                        'sleekflow_v9_pro_monthly_myr',
                                                                        'sleekflow_v9_pro_yearly_myr',
                                                                        'sleekflow_v9_premium_monthly_myr',
                                                                        'sleekflow_v9_premium_yearly_myr',
                                                                        'sleekflow_v9_pro_monthly_idr',
                                                                        'sleekflow_v9_pro_yearly_idr',
                                                                        'sleekflow_v9_premium_monthly_idr',
                                                                        'sleekflow_v9_premium_yearly_idr',
                                                                        'sleekflow_v9_pro_monthly_eur',
                                                                        'sleekflow_v9_pro_yearly_eur',
                                                                        'sleekflow_v9_premium_monthly_eur',
                                                                        'sleekflow_v9_premium_yearly_eur',
                                                                        'sleekflow_v9_agent_premium_monthly_eur',
                                                                        'sleekflow_v9_pro_monthly_gbp',
                                                                        'sleekflow_v9_pro_yearly_gbp',
                                                                        'sleekflow_v9_premium_monthly_gbp',
                                                                        'sleekflow_v9_premium_yearly_gbp',
                                                                        'sleekflow_v9_pro_monthly_cad',
                                                                        'sleekflow_v9_pro_yearly_cad',
                                                                        'sleekflow_v9_premium_monthly_cad',
                                                                        'sleekflow_v9_premium_yearly_cad',
                                                                        'sleekflow_v9_pro_monthly_aud',
                                                                        'sleekflow_v9_pro_yearly_aud',
                                                                        'sleekflow_v9_premium_monthly_aud',
                                                                        'sleekflow_v9_premium_yearly_aud',
                                                                        'sleekflow_v9_pro_monthly_aed',
                                                                        'sleekflow_v9_pro_yearly_aed',
                                                                        'sleekflow_v9_premium_monthly_aed',
                                                                        'sleekflow_v9_premium_yearly_aed',
                                                                        'sleekflow_v9_pro_monthly_brl',
                                                                        'sleekflow_v9_pro_yearly_brl',
                                                                        'sleekflow_v9_premium_monthly_brl',
                                                                        'sleekflow_v9_premium_yearly_brl',
                                                                        'sleekflow_v9_pro_monthly_inr',
                                                                        'sleekflow_v9_pro_yearly_inr',
                                                                        'sleekflow_v9_premium_monthly_inr',
                                                                        'sleekflow_v9_premium_yearly_inr'
                                               ))
                             and          timestamp >= {start.ToUnixTime()}
                           and Timestamp < ${end.ToUnixTime()}
                         group by channel, IsSentFromSleekflow, DeliveryType) as r
                   where r.channel in (
                                       'instagram',
                                       'facebook',
                                       'whatsapp',
                                       'whatsapp360dialog',
                                       'whatsappcloudapi',
                                       'web',
                                       'line',
                                       'sms',
                                       'telegram',
                                       'viber',
                                       'wechat',
                                       'email');
                   """;

        var result = await RawSqlQuery(
            sql,
            reader => new BugbqMessageMetric
            {
                Period = reader[0].ToString(),
                Channel = reader[1].ToString(),
                Direction = reader[2].ToString(),
                DeliveryType = reader[3].ToString(),
                MessageCount = (int) reader[4],
            });

        return result;
    }

    private async Task<List<T>> RawSqlQuery<T>(string query, Func<DbDataReader, T> map)
    {
        await using var command = _appDbContext.Database.GetDbConnection().CreateCommand();
        command.CommandText = query;
        command.CommandType = CommandType.Text;

        await _appDbContext.Database.OpenConnectionAsync();

        await using var result = await command.ExecuteReaderAsync();
        var entities = new List<T>();

        while (await result.ReadAsync())
        {
            entities.Add(map(result));
        }

        return entities;
    }
}