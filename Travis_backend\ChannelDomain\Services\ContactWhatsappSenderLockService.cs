using System;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Travis_backend.Cache;
using Travis_backend.Constants;
using Travis_backend.Database;
using Travis_backend.Exceptions;
using Travis_backend.Helpers;
using Travis_backend.MessageDomain.Models.Interfaces;

namespace Travis_backend.ChannelDomain.Services;

public interface IContactWhatsappSenderLockService
{
    Task<ILockService.Lock> GetContactWhatsappPhoneNumberLockAsync(string companyId, string whatsappPhoneNumber);

    Task<ILockService.Lock> GetSendWhatsappMessageLock(
        string companyId,
        string whatsappPhoneNumber,
        string channelType,
        string channelIdentityId);
}

public class ContactWhatsappSenderLockService : IContactWhatsappSenderLockService
{
    private readonly ApplicationDbContext _appDbContext;
    private readonly ILockService _lockService;
    private readonly ILogger<ContactWhatsappSenderLockService> _logger;

    public ContactWhatsappSenderLockService(ApplicationDbContext appDbContext, ILockService lockService, ILogger<ContactWhatsappSenderLockService> logger)
    {
        _appDbContext = appDbContext;
        _lockService = lockService;
        _logger = logger;
    }

    public async Task<ILockService.Lock> GetContactWhatsappPhoneNumberLockAsync(
        string companyId,
        string whatsappPhoneNumber)
    {
        ILockService.Lock whatsappPhoneNumberLock = null;
        var lockAttempt = 0;

        if (!string.IsNullOrEmpty(whatsappPhoneNumber))
        {
            var originalWhatsappPhoneNumber = whatsappPhoneNumber;
            whatsappPhoneNumber = PhoneNumberHelper.NormalizeWhatsappPhoneNumber(whatsappPhoneNumber);

            if (string.IsNullOrEmpty(whatsappPhoneNumber))
            {
                throw new ContactWhatsappSenderLockException(
                    ContactWhatsappSenderLockExceptionErrorCode.IncorrectWhatsappPhoneNumberFormat,
                    $"Incorrect Whatsapp Phone Number Format {originalWhatsappPhoneNumber}");
            }

            // race condition from receiving messages from multiple possible whatsapp channels
            var sender = await GetWhatsappChannelsSenderAsync(companyId, whatsappPhoneNumber);

            while (sender == null)
            {
                if (lockAttempt > 2)
                {
                    throw new ContactWhatsappSenderLockException(
                        ContactWhatsappSenderLockExceptionErrorCode.LockAttemptsExceeded,
                        "Get WhatsApp Cloud Api Lock Exceeded 3 Attempts");
                }

                // To do: need to handle Twilio and whatsapp360dialog case as well
                // but existing receive message webhook locking requires channel specific key as well, and we cannot wait for acquiring lock for all channels
                whatsappPhoneNumberLock = await _lockService.AcquireLockAsync(
                    WhatsappCloudApiCacheKeyHelper.GetAddNewContactRedLockKey(companyId, whatsappPhoneNumber),
                    TimeSpan.FromSeconds(10));

                if (whatsappPhoneNumberLock == null)
                {
                    await Task.Delay(TimeSpan.FromSeconds(10));
                    lockAttempt++;

                    sender = await GetWhatsappChannelsSenderAsync(companyId, whatsappPhoneNumber);
                }
                else
                {
                    // Lock acquired
                    _logger.LogInformation(
                        "[{MethodName}] Company {CompanyId} locking contact WhatsApp phone number {ContactWhatsappNumber} successfully",
                        nameof(GetContactWhatsappPhoneNumberLockAsync),
                        companyId,
                        whatsappPhoneNumber);
                    break;
                }
            }
        }

        return whatsappPhoneNumberLock;
    }

    public async Task<ILockService.Lock> GetSendWhatsappMessageLock(
        string companyId,
        string whatsappPhoneNumber,
        string channelType,
        string channelIdentityId)
    {
        ILockService.Lock whatsappPhoneNumberLock = null;
        var lockAttempt = 0;
        var originalWhatsappPhoneNumber = whatsappPhoneNumber;
        whatsappPhoneNumber = PhoneNumberHelper.NormalizeWhatsappPhoneNumber(whatsappPhoneNumber);

        if (string.IsNullOrEmpty(whatsappPhoneNumber))
        {
            throw new ContactWhatsappSenderLockException(
                ContactWhatsappSenderLockExceptionErrorCode.IncorrectWhatsappPhoneNumberFormat,
                $"Incorrect Whatsapp Phone Number Format {originalWhatsappPhoneNumber}");
        }

        var sender = await GetWhatsappChannelsSenderAsync(companyId, whatsappPhoneNumber);

        while (sender == null)
        {
            if (lockAttempt > 2)
            {
                throw new ContactWhatsappSenderLockException(
                    ContactWhatsappSenderLockExceptionErrorCode.LockAttemptsExceeded,
                    "Get Whatsapp Phone Number Lock Exceeded 3 Attempts");
            }

            switch (channelType)
            {
                case ChannelTypes.WhatsappCloudApi:
                    whatsappPhoneNumberLock = await _lockService.AcquireLockAsync(
                        WhatsappCloudApiCacheKeyHelper.GetAddNewContactRedLockKey(companyId, whatsappPhoneNumber),
                        TimeSpan.FromSeconds(10));

                    break;
                case ChannelTypes.Whatsapp360Dialog:
                    whatsappPhoneNumberLock = await _lockService.AcquireLockAsync(
                        WhatsApp360DialogCacheKeyHelper.GetAddNewContactRedLockKey(
                            companyId,
                            whatsappPhoneNumber),
                        TimeSpan.FromSeconds(10));

                    break;
                case ChannelTypes.WhatsappTwilio:
                    var channelWhatsappSender = await _appDbContext.ConfigWhatsAppConfigs
                        .Where(x => x.CompanyId == companyId && x.ChannelIdentityId == channelIdentityId)
                        .Select(x => x.WhatsAppSender)
                        .FirstOrDefaultAsync();

                    if (string.IsNullOrEmpty(channelWhatsappSender))
                    {
                        throw new ContactWhatsappSenderLockException(
                            ContactWhatsappSenderLockExceptionErrorCode.ChannelNotFound,
                            $"Whatsapp Twilio Channel {channelIdentityId} Not Found");
                    }

                    whatsappPhoneNumberLock = await _lockService.AcquireLockAsync(
                        $"{channelWhatsappSender}_{whatsappPhoneNumber}",
                        TimeSpan.FromSeconds(10));

                    break;
            }

            if (whatsappPhoneNumberLock == null)
            {
                await Task.Delay(TimeSpan.FromSeconds(10));
                lockAttempt++;

                sender = await GetWhatsappChannelsSenderAsync(companyId, whatsappPhoneNumber);
            }
            else
            {
                // Lock acquired
                _logger.LogInformation(
                    "[{MethodName}] Company {CompanyId} locking contact WhatsApp phone number {ContactWhatsappNumber} for channel {ChannelWhatsappPhoneNumber} successfully",
                    nameof(GetSendWhatsappMessageLock),
                    companyId,
                    whatsappPhoneNumber,
                    channelIdentityId);
                break;
            }
        }

        return whatsappPhoneNumberLock;
    }

    private async Task<IMessagingChannelUser> GetWhatsappChannelsSenderAsync(
        string companyId,
        string whatsappPhoneNumber)
    {
        // Get sender from WhatsappCloudApiSenders, Whatsapp360DialogUsers, SenderWhatsappSenders these 3 channels
        var sender = (await _appDbContext.WhatsappCloudApiSenders
            .Where(
                x => x.CompanyId == companyId
                     && x.WhatsappId == whatsappPhoneNumber
                     && x.UserProfileId != null
                     && x.ConversationId != null)
            .FirstOrDefaultAsync() ?? (IMessagingChannelUser) await _appDbContext.UserProfiles
            .Where(
                x => x.CompanyId == companyId &&
                     x.PhoneNumber == whatsappPhoneNumber &&
                     x.WhatsApp360DialogUser.CompanyId == companyId &&
                     x.WhatsApp360DialogUser.WhatsAppId == whatsappPhoneNumber)
            .Select(x => x.WhatsApp360DialogUser)
            .FirstOrDefaultAsync()) ?? await _appDbContext.SenderWhatsappSenders
            .FirstOrDefaultAsync(
                x =>
                    x.whatsAppId == $"whatsapp:+{whatsappPhoneNumber.Replace("@c.us", string.Empty)
                        .Replace("whatsapp:+", string.Empty)}"
                    && x.CompanyId == companyId);

        return sender;
    }
}