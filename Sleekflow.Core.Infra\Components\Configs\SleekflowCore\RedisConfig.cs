using Newtonsoft.Json;

namespace Sleekflow.Core.Infra.Components.Configs.SleekflowCore;

public class RedisConfig
{
    [JsonProperty("connection_string")]
    public string ConnectionString { get; set; }

    [JsonProperty("prefix_group_num")]
    public string? PrefixGroupNum { get; set; }

    public RedisConfig(string connectionString, string? prefixGroupNum)
    {
        ConnectionString = connectionString;
        PrefixGroupNum = prefixGroupNum;
    }
}