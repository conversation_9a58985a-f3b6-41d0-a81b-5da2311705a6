﻿using Travis_backend.Enums;
using Travis_backend.Helpers;

namespace Sleekflow.Powerflow.Apis.Helpers;

public static class CmsCacheKeyHelper
{
    public const string GetSelectionsKey = "Internal_GetAllCmsSelectionsResponse";
    public const string GetAllCompaniesKey = "Internal_GetCompanies_IsGetAllCompany";

    public static string GetCmsAnalyticKey(
        DateTime start,
        DateTime end,
        int timezoneHourOffset,
        bool excludeMarkupPlan,
        CompanyType? companyType)
    {
        return
            $"Internal_GetCmsAnalytic_{start:yyyyMMdd}_{end:yyyyMMdd}_{timezoneHourOffset}_{excludeMarkupPlan}_{companyType}";
    }

    public static string GetCmsAccruedAnalyticKey(
        DateTime start,
        DateTime end,
        int timezoneHourOffset,
        bool excludeMarkupPlan,
        CompanyType? companyType)
    {
        return
            $"Internal_GetCmsAccruedAnalytic_{start:yyyyMMdd}_{end:yyyyMMdd}_{timezoneHourOffset}_{excludeMarkupPlan}_{companyType}";
    }

    public static string GetCmsAnalyticBySalesKey(
        DateTime start,
        DateTime end,
        int timezoneHourOffset,
        bool excludeMarkupPlan)
    {
        return
            $"Internal_GetCmsAnalyticBySales_{start:yyyyMMdd}_{end:yyyyMMdd}_{timezoneHourOffset}_{excludeMarkupPlan}";
    }

    public static string GetCmsAnalyticByCompanyOwnerKey(
        DateTime start,
        DateTime end,
        int timezoneHourOffset,
        bool excludeMarkupPlan)
    {
        return
            $"Internal_GetCmsAnalyticByCompanyOwner_{start:yyyyMMdd}_{end:yyyyMMdd}_{timezoneHourOffset}_{excludeMarkupPlan}";
    }

    public static string GetCmsAnalyticByActivationOwnerIdKey(
        DateTime start,
        DateTime end,
        int timezoneHourOffset,
        bool excludeMarkupPlan,
        string ownerId)
    {
        return
            $"Internal_GetCmsAnalyticBySalesV2_{start:yyyyMMdd}_{end:yyyyMMdd}_{timezoneHourOffset}_{excludeMarkupPlan}_{ownerId}";
    }

    public static string GetCmsAnalyticByCompanyOwnerIdKey(
        DateTime start,
        DateTime end,
        int timezoneHourOffset,
        bool excludeMarkupPlan,
        string ownerId)
    {
        return
            $"Internal_GetCmsAnalyticByCompanyOwnerV2_{start:yyyyMMdd}_{end:yyyyMMdd}_{timezoneHourOffset}_{excludeMarkupPlan}_{ownerId}";
    }

    public static string GetCmsAnalyticByActivationOwnerIdsKey(
        DateTime start,
        DateTime end,
        int timezoneHourOffset,
        bool excludeMarkupPlan,
        List<string> ownerIds)
    {
        if (ownerIds is { Count: > 0 })
        {
            return
                $"Internal_GetCmsAnalyticBySalesV2_{start:yyyyMMdd}_{end:yyyyMMdd}_{timezoneHourOffset}_{excludeMarkupPlan}_{ListOfStringHash(ownerIds)}";
        }

        return
            $"Internal_GetCmsAnalyticBySalesV2_{start:yyyyMMdd}_{end:yyyyMMdd}_{timezoneHourOffset}_{excludeMarkupPlan}";
    }

    public static string GetCmsAnalyticByCompanyOwnerIdsKey(
        DateTime start,
        DateTime end,
        int timezoneHourOffset,
        bool excludeMarkupPlan,
        List<string> ownerIds)
    {
        if (ownerIds is { Count: > 0 })
        {
            return
                $"Internal_GetCmsAnalyticByCompanyOwnerV2_{start:yyyyMMdd}_{end:yyyyMMdd}_{timezoneHourOffset}_{excludeMarkupPlan}_{ListOfStringHash(ownerIds)}";
        }

        return
            $"Internal_GetCmsAnalyticByCompanyOwnerV2_{start:yyyyMMdd}_{end:yyyyMMdd}_{timezoneHourOffset}_{excludeMarkupPlan}";
    }

    public static string GetCmsAnalyticByPartnerStackGroupNamesKey(
        DateTime start,
        DateTime end,
        int timezoneHourOffset,
        bool excludeMarkupPlan,
        List<string> groupNames)
    {
        return groupNames is { Count: > 0 } ? $"Internal_GetCmsAnalyticByPartnerStackGroupNames_{start:yyyyMMdd}_{end:yyyyMMdd}_{timezoneHourOffset}_{excludeMarkupPlan}_{ListOfStringHash(groupNames)}" : $"Internal_GetCmsAnalyticByPartnerStackGroupNames_{start:yyyyMMdd}_{end:yyyyMMdd}_{timezoneHourOffset}_{excludeMarkupPlan}";
    }

    public static string GetCmsCompanyRevenueStatusBreakdownsKey(DateTime start, DateTime end, int timezoneHourOffset)
    {
        return $"Internal_GetCmsCompanyRevenueStatusBreakdowns_{start:yyyyMMdd}_{end:yyyyMMdd}_{timezoneHourOffset}";
    }

    public static string GetCmsCohortAnalysisDataKey(int year)
    {
        return $"Internal_GetCmsCohortAnalysisDataKey_{year}";
    }

    public static string GetAllPartnerChannelsByClientId(string partnerId)
    {
        return $"Internal_GetAllPartnerChannelsByClientId_{partnerId}";
    }

    public static string GetCmsCompaniesChurnReasonKey(
        List<string> companyIds,
        bool hasChurnReason,
        bool hasTier,
        bool hasAllTimeRevenueAnalyticData)
    {
        if (companyIds is { Count: > 0 })
        {
            return
                $"Internal_GetCompaniesChurnReasonKey_{ListOfStringHash(companyIds)}_{hasChurnReason}_{hasTier}_{hasAllTimeRevenueAnalyticData}";
        }

        return $"Internal_GetCompaniesChurnReasonKey_{hasChurnReason}_{hasTier}_{hasAllTimeRevenueAnalyticData}";
    }

    public static string GetCmsGetSubscriptionPlansKey()
    {
        return $"Internal_GetSubscriptionPlans";
    }

    public static string GetAllWhatsAppCloudApiBalance()
    {
        return $"Internal_GetAllWhatsAppCloudApiBalance";
    }

    public static string GetAllWhatsappCloudApiWabas()
    {
        return $"Internal_GetAllWhatsappCloudApiWabas";
    }

    public static string GetConnectedWabasByCompanyId(string companyId)
    {
        return $"Internal_GetConnectedWabasBy{companyId}";
    }

    private static string ListOfStringHash(List<string> stringList)
    {
        return SHA256Helper.sha256_hash(string.Join(',', stringList.OrderBy(x => x).ToArray()));
    }

    public static string GetCmsCompaniesFlowHubData()
    {
        return "Internal_GetCmsCompaniesFlowHubData";
    }
}