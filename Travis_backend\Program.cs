﻿using System.Collections.Generic;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Hosting;
using Serilog;
using Serilog.Enrichers.Span;
using Serilog.Events;
using Serilog.Sinks.GoogleCloudLogging;
using Travis_backend.Configuration;
using Travis_backend.LogEnrichers;

namespace Travis_backend
{
    public class Program
    {
        public static void Main(string[] args)
        {
            BuildLogger("SleekflowCore");

            CreateHostBuilder(args).Build().Run();
        }

        public static void BuildLogger(string name)
        {
            var loggerConfig = new LoggerConfig();
            var loggerConfiguration = new LoggerConfiguration()
                .MinimumLevel.Information()
#if DEBUG
                .MinimumLevel.Override("Microsoft.EntityFrameworkCore.Database", LogEventLevel.Debug)
#else
                .MinimumLevel.Override("Microsoft", LogEventLevel.Warning)
#endif
                .Enrich.WithSpan()
                .Enrich.FromLogContext()
                .Enrich.WithMachineName()
                .Enrich.WithLogTemplate()
                .WriteTo.Async(
                    wt => wt.Console(
                        outputTemplate:
                        "[{Timestamp:HH:mm:ss} {Level:u3} {MachineName}][{SfRequestId}][{SourceContext}] {Message:lj}{NewLine}{Exception}"));
            if (loggerConfig.IsLogAnalyticsEnabled == "TRUE" || loggerConfig.IsGoogleCloudLoggingEnabled == "TRUE")
            {
                if (loggerConfig.IsLogAnalyticsEnabled == "TRUE")
                {
                    loggerConfiguration = loggerConfiguration
                        .WriteTo.AzureAnalytics(
                            loggerConfig.WorkspaceId,
                            loggerConfig.AuthenticationId,
                            name,
                            flattenObject: false);
                }

                if (loggerConfig.IsGoogleCloudLoggingEnabled == "TRUE")
                {
                    loggerConfiguration = loggerConfiguration
                        .WriteTo.GoogleCloudLogging(
                            new GoogleCloudLoggingSinkOptions(
                                projectId: loggerConfig.GoogleCloudProjectId,
                                googleCredentialJson: loggerConfig.GoogleCloudCredentialJson,
                                serviceName: name,
                                logName: name,
                                useSourceContextAsLogName: false,
                                labels: new Dictionary<string, string>()
                                {
                                    {
                                        "PROJECT", "SLEEKFLOW"
                                    },
                                    {
                                        "SF_ENVIRONMENT", loggerConfig.Sfenvironment
                                    }
                                }));
                }
            }
#if QA
            loggerConfiguration.WriteTo.File(new CompactJsonFormatter(),"logs/backend.txt");
#endif
            Log.Logger = loggerConfiguration.CreateLogger();
        }

        public static IHostBuilder CreateHostBuilder(string[] args) =>
            Host.CreateDefaultBuilder(args)
                .UseSerilog()
                .ConfigureWebHostDefaults(webBuilder => { webBuilder.UseStartup<Startup>(); });
    }
}