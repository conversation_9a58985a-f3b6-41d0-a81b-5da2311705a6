using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Travis_backend.Database;
using Travis_backend.SubscriptionPlanDomain.Models;
using Travis_backend.SubscriptionPlanDomain.Repositories;

namespace Travis_backend.SubscriptionPlanDomain.Services;

public interface IPlanDefinitionService
{
    Task<PlanDefinition> GetPlanDefinitionAsync(string sleekflowSubscriptionPlanId);

    Task<List<PlanDefinition>> GetPlanDefinitionsAsync(List<string> sleekflowSubscriptionPlanIds);

    Task<bool> IsPlanDefinitionExistAsync(string sleekflowSubscriptionPlanId);
}

public class PlanDefinitionService : IPlanDefinitionService
{
    private readonly IPlanDefinitionRepository _planDefinitionRepository;
    private readonly ApplicationDbContext _applicationDbContext;

    private static readonly IEnumerable<Regex> _planIdRegexes = new List<Regex>
    {
        new(@"^sleekflow_(?<version>v\d+)_country(?<countryTier>tier\d+)_(?<name>\S+)_(?<currency>[a-zA-Z]{3})$")
    };

    public PlanDefinitionService(
        IPlanDefinitionRepository planDefinitionRepository,
        ApplicationDbContext applicationDbContext)
    {
        _planDefinitionRepository = planDefinitionRepository;
        _applicationDbContext = applicationDbContext;
    }

    public async Task<PlanDefinition> GetPlanDefinitionAsync(string sleekflowSubscriptionPlanId)
    {
        var (version, currency, name, countryTier) = await GetPlanDefinitionInfo(sleekflowSubscriptionPlanId);

        return _planDefinitionRepository.GetAll().Find(
            p => p.Version == version &&
                 p.PlanAmounts.Any(a => a.CurrencyIsoCode.ToLower() == currency) &&
                 p.Names.Any(n => n.LanguageIsoCode == "en" && n.Value == name) &&
                 string.Equals(p.SubscriptionCountryTier, countryTier, StringComparison.OrdinalIgnoreCase) &&
                 p.RecordStatuses.Contains("Active"));
    }

    public async Task<List<PlanDefinition>> GetPlanDefinitionsAsync(List<string> sleekflowSubscriptionPlanIds)
    {
        var planDefinitions = new List<PlanDefinition>();

        foreach (var sleekflowSubscriptionPlanId in sleekflowSubscriptionPlanIds)
        {
            var planDefinition = await GetPlanDefinitionAsync(sleekflowSubscriptionPlanId);

            if (planDefinition != null)
            {
                planDefinitions.Add(planDefinition);
            }
        }

        return planDefinitions;
    }

    public async Task<bool> IsPlanDefinitionExistAsync(string sleekflowSubscriptionPlanId)
    {
        var (version, currency, name, countryTier) = await GetPlanDefinitionInfo(sleekflowSubscriptionPlanId);

        return _planDefinitionRepository.GetAll().Any(
            p => p.Version == version &&
                p.PlanAmounts.Any(a => a.CurrencyIsoCode.ToLower() == currency) &&
                p.Names.Any(n => n.LanguageIsoCode == "en" && n.Value == name) &&
                string.Equals(p.SubscriptionCountryTier, countryTier, StringComparison.OrdinalIgnoreCase) &&
                p.RecordStatuses.Contains("Active"));
    }

    private async Task<(string Version,
            string Currency,
            string Name,
            string CountryTier)>
            GetPlanDefinitionInfo(string sleekflowSubscriptionPlanId)
    {
        bool isRegexMatched = false;
        string version = null;
        string currency = null;
        string name = null;
        string countryTier = null;

        foreach (var regex in _planIdRegexes)
        {
            var match = regex.Match(sleekflowSubscriptionPlanId);

            if (match.Success)
            {
                isRegexMatched = true;
                version = match.Groups["version"].Value;
                countryTier = match.Groups["countryTier"].Value;
                name = match.Groups["name"].Value;
                currency = match.Groups["currency"].Value;
            }
        }

        if (!isRegexMatched)
        {
            var idParts = sleekflowSubscriptionPlanId.Split('_');

            switch (idParts.Length)
            {
                case >= 4 when idParts[0] == "sleekflow" &&
                               idParts[1].StartsWith("v") &&
                               idParts[^1].Length == 3 &&
                               PlanDefinitionRepository.SupportedCurrencies.Contains(idParts[^1]):
                    version = idParts[1];
                    currency = idParts[^1];
                    name = string.Join("_", idParts.Skip(2).Take(idParts.Length - 3));
                    break;
                case >= 2 when idParts[0] == "sleekflow":
                    name = string.Join("_", idParts.Skip(1));
                    break;
            }
        }

        var nameParts = name?.Split('_');
        if (nameParts?.Length >= 2 && nameParts[0].StartsWith("v"))
        {
            version = nameParts[0];
            name = string.Join("_", nameParts[1..]);
        }

        if (version is null)
        {
            var sleekflowSubscriptionPlan =
                await _applicationDbContext.CoreSubscriptionPlans.FirstOrDefaultAsync(
                    p => p.Id == sleekflowSubscriptionPlanId);

            version = sleekflowSubscriptionPlan == null ? "v0" : "v" + sleekflowSubscriptionPlan.Version;
        }

        if (currency is null)
        {
            var sleekflowSubscriptionPlan =
                await _applicationDbContext.CoreSubscriptionPlans.FirstOrDefaultAsync(
                    p => p.Id == sleekflowSubscriptionPlanId);

            currency = sleekflowSubscriptionPlan == null ? "usd" : sleekflowSubscriptionPlan.Currency;
        }

        if (name is null && sleekflowSubscriptionPlanId.Contains("price_"))
        {
            name = sleekflowSubscriptionPlanId;
        }

        return (version, currency, name, countryTier);
    }
}