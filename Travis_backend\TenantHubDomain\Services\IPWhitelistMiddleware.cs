using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Identity;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using NetTools;
using Newtonsoft.Json;
using Sleekflow.Apis.TenantHub.Api;
using Sleekflow.Apis.TenantHub.Model;
using Travis_backend.AccountAuthenticationDomain.Models;
using Travis_backend.Cache;
using Travis_backend.Cache.Models.CacheKeyPatterns;
using Travis_backend.ConversationServices;
using Travis_backend.Extensions;
using Travis_backend.TenantHubDomain.Exceptions;

namespace Travis_backend.TenantHubDomain.Services;

public class IPWhitelistMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<IPWhitelistMiddleware> _logger;

    public IPWhitelistMiddleware(
        RequestDelegate next,
        ILogger<IPWhitelistMiddleware> logger)
    {
        _next = next;
        _logger = logger;
    }

    private static List<string> ExtractValues(string? values)
    {
        return string.IsNullOrEmpty(values)
            ? new List<string>()
            : values.Replace("\"", string.Empty).Split(',').Where(v => !string.IsNullOrEmpty(v)).ToList();
    }

    public async Task InvokeAsync(
        HttpContext context,
        IManagementEnabledFeaturesApi managementEnabledFeaturesApi,
        IManagementFeaturesApi managementFeaturesApi,
        IManagementIpWhitelistsApi ipWhitelistsApi,
        UserManager<ApplicationUser> userManager,
        ICoreService coreService,
        ICacheManagerService cacheManagerService,
        IConfiguration configuration)
    {
        if (
            !IsAccessiblePath(context)
            && context.User.Identity is not null
            && context.User.Identity.IsAuthenticated)
        {
            var checkingRation = new Random();
            var getPercentage = checkingRation.Next(1, 100);

            // 75% chance to skip the IP whitelist checking (except for /company)
            if (
                (context.Request.Path.Value is not null
                && !context.Request.Path.Value.Contains("/company"))
                && getPercentage > 25)
            {
                await _next(context);
                return;
            }

            // Check if login as user (No need to check expire time and user role because it will be auto clean up at claims transformation)
            var isLoginAsUser = context.User.Claims.FirstOrDefault(c => c.Type == "IsLoginAsUser")?.Value;
            if (isLoginAsUser is not null && isLoginAsUser.Equals("true"))
            {
                await _next(context);
                return;
            }

            var remoteIp = context.Connection.RemoteIpAddress?.ToString();
            var via = context.Request.Headers.ContainsKey("Via");
            if (via && context.Request.Headers["Via"].ToString().ToLower().Contains("azure"))
            {
                remoteIp = ExtractValues(context.Request.Headers["X-Forwarded-For"].ToString()).First();
            }

            var userId = userManager.GetUserId(context.User);
            var ipWhitelistCacheKeyPatternUser = string.IsNullOrEmpty(remoteIp)
                ? new IpWhitelistCacheKeyPattern(userId, null)
                : new IpWhitelistCacheKeyPattern(userId, remoteIp);

            var cacheData = await cacheManagerService.GetCacheAsync(ipWhitelistCacheKeyPatternUser);
            if (!string.IsNullOrEmpty(cacheData))
            {
                var res = bool.Parse(cacheData);
                if (!res)
                {
                    _logger.LogError(
                        "Ip {S} has blocked (In cache). User id: {UserId}",
                        remoteIp,
                        userId);
                    throw new NotAllowedIpException($"Ip {remoteIp} has blocked.");
                }
            }
            else
            {
                var user = await userManager.GetUserAsync(context.User);
                _logger.LogDebug("Request from Remote IP address: {RemoteIp}", remoteIp);

                var staff = await coreService.GetCompanyStaff(user);

                if (staff is null)
                {
                    await cacheManagerService.SaveCacheAsync(
                        ipWhitelistCacheKeyPatternUser,
                        true.ToString(),
                        TimeSpan.FromMinutes(15));
                }
                else
                {
                    var ipWhitelistCacheKeyPatternCompany = new IpWhitelistCacheKeyPattern(staff.CompanyId);

                    try
                    {
                        var cacheString = await cacheManagerService.GetCacheAsync(ipWhitelistCacheKeyPatternCompany);

                        if (cacheString is null || cacheString.IsNullOrEmpty())
                        {
                            await cacheManagerService.SaveCacheAsync(
                                ipWhitelistCacheKeyPatternUser,
                                true.ToString(),
                                TimeSpan.FromMinutes(15));
                        }
                        else
                        {
                            var ipConfigs = JsonConvert.DeserializeObject<IpWhitelistSettings>(cacheString);
                            var isAllowedIp = ipConfigs.IpRangeDetails
                                .Select(item => IPAddressRange.Parse(item.IpRange))
                                .Any(compareIp => compareIp.Contains(IPAddressRange.Parse(remoteIp)));

                            await cacheManagerService.SaveCacheAsync(
                                ipWhitelistCacheKeyPatternUser,
                                isAllowedIp.ToString().ToLower(),
                                TimeSpan.FromMinutes(5));

                            if (!isAllowedIp)
                            {
                                _logger.LogError(
                                    "Ip {S} has blocked. Company id: {StaffCompanyId}, User id: {UserId}",
                                    remoteIp,
                                    staff.CompanyId,
                                    userId);
                                throw new NotAllowedIpException($"Ip {remoteIp} has blocked.");
                            }
                        }
                    }
                    catch (Exception e)
                    {
                        _logger.LogError(
                            "[{IpWhitelistMiddlewareName}] Error when validate the ip address: {RemoteIP}, {EMessage}, userId: {UserId}, companyId: {CompanyId}\n" +
                            "Connection: {Connection}, Header: {Header}",
                            nameof(IPWhitelistMiddleware),
                            remoteIp,
                            e.Message,
                            userId,
                            staff.CompanyId,
                            context.Connection.RemoteIpAddress?.ToString(),
                            context.Request?.Headers?["X-Forwarded-For"].ToString());
                        throw;
                    }
                }
            }
        }

        await _next(context);
    }

    /// <summary>
    /// Skip whitelists checking for following path:
    /// auth0/account/RegisterAccountCompany
    /// auth0/account/IsCompanyRegistered
    /// all AllowAnonymous
    /// </summary>
    /// <param name="context"></param>
    /// <returns></returns>
    public bool IsAccessiblePath(HttpContext context)
    {
        var endpoint = context.GetEndpoint();
        var isAllowAnonymous = endpoint?.Metadata?.GetMetadata<IAllowAnonymous>() != null;
        var isRegisterAccountCompany = context.Request.Path.Value != null && context.Request.Path.Value.Contains("RegisterAccountCompany");
        var isCompanyRegistered = context.Request.Path.Value != null && context.Request.Path.Value.Contains("IsCompanyRegistered");

        return isRegisterAccountCompany || isCompanyRegistered || isAllowAnonymous;
    }
}

public static class IPWhitelistMiddlewareExtensions
{
    public static IApplicationBuilder UseIPWhitelistMiddleware(this IApplicationBuilder builder)
    {
        builder.UseMiddleware<IPWhitelistExceptionHandlerMiddleware>();
        builder.UseMiddleware<IPWhitelistMiddleware>();
        return builder;
    }
}