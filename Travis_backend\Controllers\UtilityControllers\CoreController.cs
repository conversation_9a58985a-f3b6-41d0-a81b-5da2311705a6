﻿using System;
using System.Collections.Generic;
using System.IdentityModel.Tokens.Jwt;
using System.IO;
using System.Linq;
using System.Security.Claims;
using System.Text;
using System.Threading.Tasks;
using AutoMapper;
using Hangfire;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.IdentityModel.Tokens;
using Newtonsoft.Json.Linq;
using Stripe;
using Travis_backend.AccountAuthenticationDomain.Models;
using Travis_backend.AccountAuthenticationDomain.ViewModels;
using Travis_backend.CommonDomain.ViewModels;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.CompanyDomain.Services;
using Travis_backend.ContactDomain.Models;
using Travis_backend.ContactDomain.Services;
using Travis_backend.ContactDomain.ViewModels;
using Travis_backend.ConversationServices;
using Travis_backend.CoreDomain.Models;
using Travis_backend.CoreDomain.ViewModels;
using Travis_backend.Database;
using Travis_backend.Enums;
using Travis_backend.Extensions;
using Travis_backend.FileDomain.Services;
using Travis_backend.Helpers;
using Travis_backend.InternalDomain.ViewModels;
using Travis_backend.NotificationHubs;
using Travis_backend.SignalR;
using Travis_backend.StripeIntegrationDomain.Clients;
using Travis_backend.SubscriptionPlanDomain.Constants;
using Travis_backend.SubscriptionPlanDomain.Models;

namespace Travis_backend.Controllers.UtilityControllers
{
    [Authorize]
    public class CoreController : Controller
    {
        // GET: /<controller>/
        private readonly ApplicationDbContext _appDbContext;
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly SignInManager<ApplicationUser> _signInManager;
        private readonly IMapper _mapper;
        private readonly ILogger _logger;
        private readonly IConfiguration _configuration;
        private readonly IUploadService _uploadService;
        private readonly IEmailNotificationService _emailNotificationService;
        private readonly IUserProfileService _userProfileService;
        private readonly ICompanyUsageService _companyUsageService;
        private readonly ITwilioService _twilioService;
        private readonly ICoreService _coreService;
        private readonly ICompanyService _companyService;
        private readonly ISignalRService _signalRService;
        private readonly IAuditHubAuditLogService _auditHubAuditLogService;
        private readonly IStripeClients _stripeClients;

        public CoreController(
            ApplicationDbContext appDbContext,
            UserManager<ApplicationUser> userManager,
            SignInManager<ApplicationUser> signInManager,
            IMapper mapper,
            IConfiguration configuration,
            ILogger<CoreController> logger,
            IUploadService uploadService,
            IEmailNotificationService emailNotificationService,
            IUserProfileService userProfileService,
            ICompanyUsageService companyUsageService,
            ITwilioService twilioService,
            ICoreService coreService,
            ICompanyService companyService,
            ISignalRService signalRService,
            IAuditHubAuditLogService auditHubAuditLogService,
            IStripeClients stripeClients)
        {
            _userManager = userManager;
            _signInManager = signInManager;
            _appDbContext = appDbContext;
            _mapper = mapper;
            _configuration = configuration;
            _logger = logger;
            _uploadService = uploadService;
            _emailNotificationService = emailNotificationService;
            _userProfileService = userProfileService;
            _companyUsageService = companyUsageService;
            _twilioService = twilioService;
            _coreService = coreService;
            _companyService = companyService;
            _signalRService = signalRService;
            _auditHubAuditLogService = auditHubAuditLogService;
            _stripeClients = stripeClients;
        }

        [HttpGet]
        [AllowAnonymous]
        [Route("Core/leadAds/{pageId}")]
        public IActionResult AdhocLeadAds(string pageId)
        {
            RecurringJob.AddOrUpdate<ILeadAdsServiceService>(
                pageId,
                x => x.AdHocLeadGenCheckPeriodically(pageId, null),
                "*/15 * * * *");

            return Ok(
                new ResponseViewModel
                {
                    message = $"Done"
                });
        }

        [HttpGet]
        [AllowAnonymous]
        [Route("Core/migrate/brazil-phone-number-format")]
        public async Task<IActionResult> MigratePhoneNumberFormat()
        {
            BackgroundJob.Enqueue(() => MigratePhoneNumberFormatAction());
            return Ok();
        }

        [AutomaticRetry(
            Attempts = 1000,
            DelaysInSeconds = new[]
            {
                5
            })]
        public async Task MigratePhoneNumberFormatAction()
        {
            var fixFormatNumbers = await _appDbContext.UserProfiles
                .Where(x => x.PhoneNumber.StartsWith("55") && x.PhoneNumber.Length == 12).Select(
                    x => new
                    {
                        x.CompanyId, x.Id, x.PhoneNumber
                    })
                .AsNoTracking()
                .ToListAsync();

            foreach (var fixFormatNumber in fixFormatNumbers)
            {
                var oldPhoneNumber = fixFormatNumber.PhoneNumber;

                if (!fixFormatNumber.PhoneNumber.StartsWith("55") || fixFormatNumber.PhoneNumber.Length != 12)
                {
                    continue;
                }

                var correctPhoneNumber =
                    PhoneNumberHelper.GetValidPhoneNumberWithCountryCodeForImportContact(oldPhoneNumber, "Brazil");

                if (oldPhoneNumber == correctPhoneNumber)
                {
                    continue;
                }

                // Merge duplicate contact
                if (await _appDbContext.UserProfiles.AnyAsync(
                        x => x.CompanyId == fixFormatNumber.CompanyId && x.PhoneNumber == correctPhoneNumber))
                {
                    var fromUserProfileId = await _appDbContext.UserProfiles
                        .Where(x => x.CompanyId == fixFormatNumber.CompanyId && x.PhoneNumber == oldPhoneNumber)
                        .Select(x => x.Id).FirstOrDefaultAsync();
                    var toUserProfileId = await _appDbContext.UserProfiles
                        .Where(x => x.CompanyId == fixFormatNumber.CompanyId && x.PhoneNumber == correctPhoneNumber)
                        .Select(x => x.Id).FirstOrDefaultAsync();

                    await _userProfileService.DuplicatedUserProfileIdsToBeRemoved(
                        new MergeDuplicatedContactRequestV2(
                            fixFormatNumber.CompanyId,
                            new List<MergeDuplicatedFromTo>()
                            {
                                new MergeDuplicatedFromTo(fromUserProfileId, toUserProfileId)
                            },
                            true));

                    continue;
                }

                // Update the phone number
                await _userProfileService.SetFieldValueByFieldName(
                    fixFormatNumber.Id,
                    "phonenumber",
                    correctPhoneNumber);
                await _appDbContext.UserProfiles.Where(x => x.Id == fixFormatNumber.Id)
                    .ExecuteUpdateAsync(
                        calls => calls.SetProperty(
                            p => p.PhoneNumber, correctPhoneNumber));

                await _auditHubAuditLogService.CreateStaffManualAddedLogAsync(
                    fixFormatNumber.CompanyId,
                    fixFormatNumber.Id,
                    null,
                    $"Profile Information updated:\nPhone Number: new value: [{correctPhoneNumber}]",
                    null);
            }
        }

        [HttpGet]
        [AllowAnonymous]
        [Route("Core/updateshopifyfield")]
        public async Task<IActionResult> UpdateShopifyFields()
        {
            var companyIds = await _appDbContext.ConfigShopifyConfigs.Select(x => x.CompanyId).ToListAsync(HttpContext.RequestAborted);
            BackgroundJob.Enqueue(() => AddFields(companyIds));
            return Ok();
        }

        public async Task<List<string>> AddFields(List<string> companyIds)
        {
            companyIds = companyIds.Distinct().ToList();
            foreach (var companyId in companyIds)
            {
                await _companyService.AddECommerceFields(companyId);
            }

            return companyIds;
        }

        [HttpGet]
        [Route("Core/EmailNotificationTemplate")]
        public async Task<IActionResult> GetEmailTemplate([FromForm] CoreViewModel attachmentViewModel)
        {
            if (ModelState.IsValid && User.Identity.IsAuthenticated)
            {
                var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
                companyUser.Company.StorageConfig =
                    await _appDbContext.ConfigStorageConfigs.FirstOrDefaultAsync(
                        x => x.CompanyId == companyUser.CompanyId);

                if (string.IsNullOrEmpty(companyUser.CompanyId))
                {
                    return BadRequest();
                }

                List<string> results = new List<string>();
                var emails = (await _appDbContext.CoreEmailNotificationTemplates
                        .ToListAsync(HttpContext.RequestAborted))
                    .GroupBy(x => x.NotificationType)
                    .OrderBy(x => x.Key)
                    .ToList();

                foreach (var email in emails)
                {
                    results.Add($"{email.Key}: {email.OrderByDescending(x => x.Id).FirstOrDefault().EmailSubject}");
                }

                return Ok(results);
            }

            return BadRequest();
        }

        [HttpPost]
        [Route("Core/EmailNotificationTemplate")]
        public async Task<IActionResult> AddEmailTemplate([FromForm] CoreViewModel attachmentViewModel)
        {
            if (ModelState.IsValid && User.Identity.IsAuthenticated)
            {
                var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

                if (companyUser == null)
                {
                    return Unauthorized();
                }

                companyUser.Company.StorageConfig =
                    await _appDbContext.ConfigStorageConfigs.FirstOrDefaultAsync(
                        x => x.CompanyId == companyUser.CompanyId);

                foreach (IFormFile file in attachmentViewModel.files)
                {
                    var fileStrem = file.OpenReadStream();
                    StreamReader reader = new StreamReader(fileStrem);
                    string text = reader.ReadToEnd();

                    var newuUploadedFile = new EmailNotificationTemplate();
                    newuUploadedFile.NotificationName = attachmentViewModel.NotificationName;
                    newuUploadedFile.NotificationType = attachmentViewModel.NotificationType;
                    newuUploadedFile.EmailTemplate = text;
                    newuUploadedFile.EmailSubject = attachmentViewModel.Subject;
                    _appDbContext.CoreEmailNotificationTemplates.Add(newuUploadedFile);

                    break;
                }

                await _appDbContext.SaveChangesAsync();

                // var staffResponse = _mapper.Map<StaffResponse>(targetUploadProfileStaff);
                return Ok(
                    new ResponseViewModel
                    {
                        message = "success"
                    });
            }

            return BadRequest();
        }

        //
        // [HttpGet]
        // [AllowAnonymous]
        // [Route("Core/UpdateAllConversionStatus")]
        // public async Task<IActionResult> UpdateAllConversionStatus()
        // {
        //     BackgroundJob.Enqueue(() => UpdateConversionStatus());
        //
        //     return Ok();
        // }
        //
        // public async Task UpdateConversionStatus()
        // {
        //     long id = 0;
        //
        //     do
        //     {
        //         var orders = await _appDbContext.UserProfileShopifyOrders
        //             .Where(x => x.Id > 0 && x.ConversionStatus == ConversionStatus.Converted || x.ConversionStatus != ConversionStatus.NoStatus)
        //             .OrderBy(x => x.Id).Take(500).ToListAsync();
        //
        //         foreach (var order in orders)
        //         {
        //             var shopifyOrderConversionChat = 7;
        //
        //             var isTalkedToSleekFlowWithInTheConversationWindows = await _appDbContext.ConversationMessages.AnyAsync(x =>
        //                 x.Conversation.UserProfileId == order.UserProfileId &&
        //                 x.CreatedAt < order.CreatedAt &&
        //                 order.CreatedAt.AddDays(-shopifyOrderConversionChat) < x.CreatedAt);
        //
        //             if (isTalkedToSleekFlowWithInTheConversationWindows)
        //             {
        //                 var conversation = await _appDbContext.Conversations.FirstOrDefaultAsync(x => x.UserProfileId == order.UserProfileId);
        //
        //                 if (conversation != null)
        //                 {
        //                     order.TeamId = conversation.AssignedTeamId;
        //
        //                     if (!order.StaffId.HasValue && conversation.AssigneeId.HasValue)
        //                     {
        //                         order.StaffId = conversation.AssigneeId;
        //                     }
        //                 }
        //
        //                 order.ConversionStatus = ConversionStatus.ConvertedBySleekFlow;
        //             }
        //
        //             if (order.Tags != null && order.Tags.Contains("sleekflow"))
        //                 order.ConversionStatus = ConversionStatus.SleekPay;
        //
        //             await _appDbContext.SaveChangesAsync();
        //             id = order.Id;
        //         }
        //     } while (_appDbContext.UserProfileShopifyOrders.Any(x => x.Id > id && x.ConversionStatus == ConversionStatus.Converted || x.ConversionStatus != ConversionStatus.NoStatus));
        // }

        public class NotificationTestRequest
        {
            public string userId { get; set; }

            public string companyId { get; set; }
        }

        [HttpPost]
        [AllowAnonymous]
        [Route("Core/Notification/Test")]
        public async Task<IActionResult> NotificationTest([FromBody] NotificationTestRequest notificationTest)
        {
            await _signalRService.TestNotification(notificationTest.userId, notificationTest.companyId);
            return Ok(
                new ResponseViewModel
                {
                    message = "ok"
                });
        }

        [HttpPost]
        [AllowAnonymous]
        [Route("Core/Notification/TestReg/{id}")]
        public async Task<IActionResult> NotificationRegTest(
            string id,
            [FromBody]
            DeviceRegistration deviceUpdate)
        {
            await _signalRService.TestRegNotification(id, deviceUpdate);
            return Ok(
                new ResponseViewModel
                {
                    message = "ok"
                });
        }

        // [HttpPost]
        // [Route("Core/CustomFields")]
        // public async Task<IActionResult> AddCustomFields([FromBody] List<AddCoreCustomFieldViewModel> addCoreCustomFieldViewModels)
        // {
        //    if (User.Identity.IsAuthenticated)
        //    {
        //        var userId = _userManager.GetUserId(User);
        //        var companyUser = await _appDbContext.UserRoleStaffs.Where(x => x.IdentityId == userId).Include(x => x.Company.CompanyCustomFields).ThenInclude(cust => cust.CompanyCustomFieldFieldLinguals).FirstOrDefaultAsync();

        // if (companyUser != null)
        //        {
        //            var customFields = _mapper.Map<List<CoreCustomField>>(addCoreCustomFieldViewModels);

        // foreach (var customFeild in customFields)
        //            {
        //                if (!_appDbContext.CoreCustomFields.Select(x => x.FieldName).Contains(customFeild.FieldName))
        //                    _appDbContext.CoreCustomFields.Add(customFeild);
        //                else
        //                {
        //                    var existing = _appDbContext.CoreCustomFields.Where(x => x.FieldName.Contains(customFeild.FieldName)).FirstOrDefault();
        //                    existing.Category = customFeild.Category;
        //                    existing.Value = customFeild.Value;
        //                    existing.IsEditable = customFeild.IsEditable;
        //                    existing.IsVisible = customFeild.IsVisible;
        //                    existing.Type = customFeild.Type;
        //                }
        //            }

        // await _appDbContext.SaveChangesAsync();

        // var companyVM = _mapper.Map<List<CoreCustomField>>(customFields);
        //            return Ok(companyVM);
        //        }

        // }
        //    return BadRequest();
        // }

        [HttpPost]
        [AllowAnonymous]
        [Route("Canny/CreateCannyToken")]
        public IActionResult CreateCannyToken([FromBody] CannyUser cannyUser)
        {
            if (ModelState.IsValid)
            {
                try
                {
                    var privateKey = "39a2b9f6-3e54-d145-d455-76adc85118a5";

                    var claims = new Claim[]
                    {
                        new Claim("email", cannyUser.Email),
                        new Claim("id", cannyUser.Id),
                        new Claim("name", cannyUser.Name)
                    };

                    if (!string.IsNullOrEmpty(cannyUser.AvatarURL))
                    {
                        claims.Append(new Claim("avatarURL", cannyUser.AvatarURL));
                    }

                    var signingKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(privateKey));
                    var signingCredentials = new SigningCredentials(signingKey, SecurityAlgorithms.HmacSha256);
                    var jwt = new JwtSecurityToken(
                        signingCredentials: signingCredentials,
                        claims: claims);

                    dynamic response = new JObject();
                    response.access_token = new JwtSecurityTokenHandler().WriteToken(jwt);
                    return Ok(response);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, ex.Message);
                    return NotFound(ex.Message);
                }
            }

            return BadRequest(ModelState);
        }

        // [HttpGet]
        // [Route("Core/ResendWelcome/{companyId}")]
        // public async Task<IActionResult> ResendWelcomeMessage(string companyId)
        // {
        //    //var company = _appDbContext.CompanyCompanies.Where(x => x.Id == companyId).FirstOrDefault();
        //    await _emailNotificationService.SendWelcomeOnBoardNotification(companyId);
        //    return Ok();
        // }

        // [HttpGet]
        // [AllowAnonymous]
        // [Route("Core/Fix/Duplicate/Conversation")]
        // public async Task<IActionResult> FixDuplication()
        // {
        //    do
        //    {
        //        var duplicateConversations = _appDbContext.Conversations.GroupBy(x => new { x.CompanyId, x.UserProfileId })
        //          .Where(g => g.Count() > 1)
        //          .Select(y => y.Key)
        //          .Take(100)
        //          .ToList();

        // foreach (var duplicateConversation in duplicateConversations)
        //        {
        //            var conversations = await _appDbContext.Conversations.Where(x => x.UserProfileId == duplicateConversation.UserProfileId).ToListAsync();
        //            var messageA = await _appDbContext.ConversationMessages.Where(X => X.ConversationId == conversations.FirstOrDefault().Id).CountAsync();
        //            var messageB = await _appDbContext.ConversationMessages.Where(X => X.ConversationId == conversations.LastOrDefault().Id).CountAsync();
        //            if (messageA > messageB)
        //            {
        //                var merge = _appDbContext.ConversationMessages.Where(x => x.ConversationId == conversations.LastOrDefault().Id);
        //                await merge.ForEachAsync(x => x.ConversationId = conversations.FirstOrDefault().Id);
        //                var broadcast = _appDbContext.BroadcastCompaignHistories.Where(x => x.ConversationId == conversations.LastOrDefault().Id);
        //                await broadcast.ForEachAsync(x => x.ConversationId = conversations.FirstOrDefault().Id);
        //                var additionalAssignees = _appDbContext.ConversationAdditionalAssignees.Where(x => x.ConversationId == conversations.LastOrDefault().Id);
        //                await additionalAssignees.ForEachAsync(x => x.ConversationId = conversations.FirstOrDefault().Id);
        //                _appDbContext.ConversationHashtags.RemoveRange(_appDbContext.ConversationHashtags.Where(x => x.ConversationId == conversations.LastOrDefault().Id));
        //                _appDbContext.Conversations.RemoveRange(_appDbContext.Conversations.Where(x => x.Id == conversations.LastOrDefault().Id));
        //            }
        //            else
        //            {
        //                var merge = _appDbContext.ConversationMessages.Where(x => x.ConversationId == conversations.FirstOrDefault().Id);
        //                await merge.ForEachAsync(x => x.ConversationId = conversations.LastOrDefault().Id);
        //                var broadcast = _appDbContext.BroadcastCompaignHistories.Where(x => x.ConversationId == conversations.FirstOrDefault().Id);
        //                await broadcast.ForEachAsync(x => x.ConversationId = conversations.LastOrDefault().Id);
        //                var additionalAssignees = _appDbContext.ConversationAdditionalAssignees.Where(x => x.ConversationId == conversations.FirstOrDefault().Id);
        //                await additionalAssignees.ForEachAsync(x => x.ConversationId = conversations.LastOrDefault().Id);
        //                _appDbContext.ConversationHashtags.RemoveRange(_appDbContext.ConversationHashtags.Where(x => x.ConversationId == conversations.FirstOrDefault().Id));
        //                _appDbContext.Conversations.RemoveRange(_appDbContext.Conversations.Where(x => x.Id == conversations.FirstOrDefault().Id));
        //            }
        //        }

        // await _appDbContext.SaveChangesAsync();
        //    } while (_appDbContext.Conversations.GroupBy(x => new { x.CompanyId, x.UserProfileId }).Where(g => g.Count() > 1).Count() > 0);

        // return Ok();
        // }

        // [HttpGet]
        // [AllowAnonymous]
        // [Route("Core/Fix/Duplicate/Conversation")]
        // public async Task<IActionResult> FixDuplication()
        // {
        //    do
        //    {
        //        var duplicateConversations = _appDbContext.ConversationHashtags.GroupBy(x => new { x.ConversationId, x.HashtagId })
        //          .Where(g => g.Count() > 1)
        //          .Select(y => y.Key)
        //          .Take(100)
        //          .ToList();

        // foreach (var duplicateConversation in duplicateConversations)
        //        {
        //            var conversationHashtags = await _appDbContext.ConversationHashtags.Where(x => x.ConversationId == duplicateConversation.ConversationId && x.HashtagId == duplicateConversation.HashtagId).ToListAsync();
        //            if (conversationHashtags.Count > 0)
        //                _appDbContext.ConversationHashtags.RemoveRange(conversationHashtags.LastOrDefault());
        //        }

        // await _appDbContext.SaveChangesAsync();
        //    } while (_appDbContext.ConversationHashtags.GroupBy(x => new { x.ConversationId, x.HashtagId }).Where(g => g.Count() > 1).Count() > 0);

        // return Ok();
        // }

        // [HttpPost]
        // [Route("Core/load/{pageId}")]
        // public IActionResult ReloadFacebook(string pageId)
        // {
        //    var facebookConfig = _appDbContext.ConfigFacebookConfigs.Where(x => x.PageId == pageId).FirstOrDefault();
        //    if (facebookConfig != null)
        //        BackgroundJob.Enqueue<IConversationMessageService>(x => x.FetchConversationsBackground(facebookConfig.PageId, true));

        // return Ok(_mapper.Map<FacebookConfigViewModel>(facebookConfig));
        // }

        // [HttpPost]
        // [Route("Core/add/shopify/abandoned")]
        // public async Task<IActionResult> AddAbandonedCart()
        // {
        //    var shopifyIntegrations = await _appDbContext.ConfigShopifyConfigs.ToListAsync();
        //    foreach (var shopify in shopifyIntegrations)
        //    {
        //        await _companyService.AddECommerceFields(shopify.CompanyId);
        //    }

        // return Ok();
        // }
        /*
        [HttpGet]
        [Route("Core/Fix")]
        public async Task<IActionResult> Fix()
        {
            var nullNames = await _appDbContext.UserProfiles.Where(x => string.IsNullOrEmpty(x.FirstName) && x.FacebookAccount != null).Include(x => x.FacebookAccount).ToListAsync();
            try
            {
                foreach (var nullname in nullNames)
                {
                    if (!string.IsNullOrEmpty(nullname.FacebookAccount?.name))
                    {
                        nullname.FirstName = nullname.FacebookAccount.name;
                    }
                }
                await _appDbContext.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, ex.Message);
            }

            return Ok(new ResponseViewModel
            {
                message = $"Updated: {nullNames.Count};" +
                $"{string.Join(",", nullNames.Select(x => $"'{x.Id}'"))}"
            });
        } */

        [HttpGet]
        [AllowAnonymous]
        [Route("Core/migratePhoneNumber")]
        public async Task<IActionResult> MigratePhoneNumber()
        {
            BackgroundJob.Enqueue(() => MigratePhoneNumberRun());

            return Ok();
        }

        public async Task MigratePhoneNumberRun()
        {
            do
            {
                var migratePhoneNumber = (from _userProfiles in _appDbContext.UserProfiles
                    join _field in _appDbContext.UserProfileCustomFields on _userProfiles.Id equals _field.UserProfileId
                    where _userProfiles.PhoneNumber != _field.Value &&
                          _field.CompanyDefinedField.FieldName == "phonenumber"
                    select new
                    {
                        _userProfiles, _field
                    }).Take(500);

                if (!migratePhoneNumber.Any())
                {
                    break;
                }

                await migratePhoneNumber.ForEachAsync(x => x._userProfiles.PhoneNumber = x._field.Value);
                await _appDbContext.SaveChangesAsync();
            }
            while (true);
        }

        [HttpGet]
        [AllowAnonymous]
        [Route("Core/migrateEmail")]
        public async Task<IActionResult> MigrateEmail()
        {
            BackgroundJob.Enqueue(() => MigrateEmailRun());

            return Ok();
        }

        public async Task MigrateEmailRun()
        {
            do
            {
                var migrateEmail = (from _userProfiles in _appDbContext.UserProfiles
                    join _field in _appDbContext.UserProfileCustomFields on _userProfiles.Id equals _field.UserProfileId
                    where _userProfiles.Email != _field.Value && !_field.Value.Contains("\n") &&
                          _field.CompanyDefinedField.FieldName == "email"
                    select new
                    {
                        _userProfiles, _field
                    }).Take(500);

                if (!migrateEmail.Any())
                {
                    break;
                }

                foreach (var email in migrateEmail)
                {
                    _logger.LogInformation($"{email._userProfiles.Email} = {email._field.Value}");
                }

                await migrateEmail.ForEachAsync(x => x._userProfiles.Email = x._field.Value);
                await _appDbContext.SaveChangesAsync();
            }
            while (true);
        }

        // [HttpGet]
        // [Route("Core/Statistics/{companyId}")]
        // public IActionResult GetAzure(string companyId)
        // {
        //    var conversations = _appDbContext.ConversationMessages.Where(x => _appDbContext.Conversations.Where(y => y.CompanyId == companyId && y.ActiveStatus == ActiveStatus.Active && x.CreatedAt > DateTime.UtcNow.Date.AddDays(-30)).Select(y => y.Id).Contains(x.ConversationId)).OrderBy(x => x.CreatedAt).AsEnumerable();
        //    var groupConversation = conversations.GroupBy(x => x.CreatedAt.Date);
        //    string response = "Date,WhatsApp,Facebook,Online Bot\n";

        // foreach (var date in groupConversation)
        //    {
        //        var bychannel = date.GroupBy(x => x.Channel);
        //        var whatsappGroup = bychannel.Where(x => x.Key == ChannelTypes.WhatsappTwilio).FirstOrDefault();
        //        var facebookGroup = bychannel.Where(x => x.Key == ChannelTypes.Facebook).FirstOrDefault();
        //        var webGroup = bychannel.Where(x => x.Key == ChannelTypes.LiveChat).FirstOrDefault();

        // int whatsappCount = (whatsappGroup != null) ? whatsappGroup.GroupBy(x => x.ConversationId).Count() : 0;
        //        int facebookCount = (facebookGroup != null) ? facebookGroup.GroupBy(x => x.ConversationId).Count() : 0;
        //        int webCount = (webGroup != null) ? webGroup.GroupBy(x => x.ConversationId).Count() : 0;

        // response += $"{date.Key.ToString("dd/MM/yyyy")},{whatsappCount},{facebookCount},{webCount}\n";
        //    }

        // return File(Encoding.UTF8.GetBytes(response), "text/csv", $"Mabelle_{DateTime.Now.ToString("dd/MM/yyyy")}.csv");
        // }

        // [HttpGet]
        // [Route("Core/Fix/LastChannel/{companyId}")]
        // public async Task<IActionResult> FixLastChannel(string companyId)
        // {
        //    var userProfiles = await _userProfileService.GetUserProfilesByFields(companyId, new List<Condition> { new Condition { FieldName = "LastChannel", ConditionOperator = SupportedOperator.IsNull }, new Condition { FieldName = "ContactOwner", ConditionOperator = SupportedOperator.IsNotNull } });
        //    foreach (var userProfile in userProfiles.UserProfiles)
        //    {
        //        var value = userProfile.CustomFields.Where(x => x.CompanyDefinedField.FieldName == "ContactOwner").FirstOrDefault();
        //        var staffInfo = await _appDbContext.UserRoleStaffs.Where(x => x.CompanyId == userProfile.CompanyId && (x.IdentityId == value.Value || x.Identity.DisplayName == value.Value)).FirstOrDefaultAsync();

        // if (staffInfo != null)
        //        {
        //            var assignedTeam = await _appDbContext.CompanyStaffTeams.Where(x => x.Members.Where(y => y.StaffId == staffInfo.Id).Count() > 0).FirstOrDefaultAsync();
        //            if (assignedTeam != null)
        //            {
        //                await _userProfileService.SetFieldValueByFieldNameNotSaved(userProfile, "AssignedTeam", assignedTeam.Id.ToString());

        // var teamDefaultChannelId = new List<string>();
        //                if (assignedTeam.DefaultChannels?.Count > 0)
        //                {
        //                    foreach (var defaultchannel in assignedTeam.DefaultChannels)
        //                        teamDefaultChannelId.AddRange(defaultchannel.ids);

        // if (teamDefaultChannelId?.Count > 0)
        //                    {
        //                        if (userProfile.WhatsAppAccountId.HasValue)
        //                        {
        //                            userProfile.WhatsAppAccount = await _appDbContext.SenderWhatsappSenders.Where(x => x.Id == userProfile.WhatsAppAccountId).FirstOrDefaultAsync();
        //                            if (!teamDefaultChannelId.Contains(ConversationHelper.GetInstanceId(userProfile.WhatsAppAccount)))
        //                            {
        //                                if (!userProfile.WhatsAppAccount.whatsAppId.Contains("@g.us"))
        //                                {
        //                                    var twilioInstance = teamDefaultChannelId.FirstOrDefault().Split(";", StringSplitOptions.RemoveEmptyEntries);
        //                                    var instanceId = twilioInstance[0];
        //                                    var instanceSender = "";
        //                                    if (twilioInstance.Count() > 1)
        //                                        instanceSender = twilioInstance[1];

        // var isTwilio = (_appDbContext.ConfigWhatsAppConfigs.Where(x => x.TwilioAccountId == instanceId && x.CompanyId == userProfile.CompanyId).Count() > 0);
        //                                    var isChatAPI = (_appDbContext.ConfigWSChatAPIConfigs.Where(x => x.WSChatAPIInstance == instanceId && x.CompanyId == userProfile.CompanyId).Count() > 0);

        // userProfile.WhatsAppAccount.InstanceId = instanceId;
        //                                    userProfile.WhatsAppAccount.InstaneSender = instanceSender;

        // if (isTwilio)
        //                                        userProfile.WhatsAppAccount.whatsAppId = $"whatsapp:+{userProfile.WhatsAppAccount.whatsAppId.Replace("@c.us", "").Replace("whatsapp:+", "")}";
        //                                    else if (isChatAPI)
        //                                        userProfile.WhatsAppAccount.whatsAppId = $"{userProfile.WhatsAppAccount.whatsAppId.Replace("@c.us", "").Replace("whatsapp:+", "")}@c.us";

        // await _userProfileService.SetFieldValueByFieldNameNotSaved(userProfile, "LastChannel", ChannelTypes.WhatsappTwilio);
        //                                    await _appDbContext.SaveChangesAsync();
        //                                }
        //                            }
        //                            else
        //                            {
        //                                await _userProfileService.SetFieldValueByFieldNameNotSaved(userProfile, "LastChannel", ChannelTypes.WhatsappTwilio);
        //                                await _appDbContext.SaveChangesAsync();
        //                            }

        // }

        // }
        //                }
        //            }
        //        }
        //    }
        //    return Ok();
        // }

        // [HttpGet]
        // [Route("Core/Statistics/Juno/{companyId}")]
        // public IActionResult GetJuno(string companyId)
        // {
        //    var conversations = _appDbContext.ConversationMessages.Where(x => _appDbContext.Conversations.Where(y => y.CompanyId == companyId && y.ActiveStatus == ActiveStatus.Active && x.CreatedAt > DateTime.UtcNow.Date.AddDays(-60)).Select(y => y.Id).Contains(x.ConversationId)).OrderBy(x => x.CreatedAt).AsEnumerable();
        //    var groupConversation = conversations.GroupBy(x => x.CreatedAt.Date);
        //    string response = "Date,Inbound Message,Campaign Message,Replied Campaign\n";

        // foreach (var date in groupConversation)
        //    {
        //        var inboundMessages = date.Where(x => !x.IsSentFromSleekflow && _appDbContext.ConversationMessages.Where(y => y.ConversationId == x.ConversationId && y.CreatedAt.Date < x.CreatedAt.Date).Count() == 0).AsEnumerable().GroupBy(x => x.ConversationId).Count();
        //        var campaignMessages = date.Where(x => x.DeliveryType == DeliveryType.Broadcast).ToList();

        // var repliedCampaign = 0;
        //        foreach (var campaignMessage in campaignMessages)
        //        {
        //            if (_appDbContext.ConversationMessages.Where(x => x.ConversationId == campaignMessage.ConversationId && x.DeliveryType == DeliveryType.Normal && campaignMessage.CreatedAt < x.CreatedAt && !x.IsSentFromSleekflow).Count() > 0)
        //                repliedCampaign += 1;
        //        }

        // response += $"{date.Key.ToString("dd/MM/yyyy")},{inboundMessages},{campaignMessages.Count},{repliedCampaign}\n";
        //    }

        // return File(Encoding.UTF8.GetBytes(response), "text/csv", $"Mabelle_{DateTime.Now.ToString("dd/MM/yyyy")}.csv");
        // }

        // [HttpDelete]
        // [Route("Core/removeCompany")]
        // public IActionResult RemoveCompany([FromBody] List<string> companyIds)
        // {
        //    foreach (var companyId in companyIds)
        //        BackgroundJob.Enqueue<ICoreService>(x => x.RemoveCompanyData(companyId));
        //    return Ok();
        // }

        // [HttpPost]
        // [Route("Core/migrateDatetimeToTimeShort")]
        // public IActionResult Migration()
        // {
        //    BackgroundJob.Enqueue(() => MigrationNow());
        //    return Ok();
        // }

        // public async Task MigrationNow()
        // {
        //    do
        //    {
        //        var migrations = _appDbContext.ConversationMessages.Where(x => x.FrondendTimestamp == 0).OrderByDescending(x => x.Id).Take(300);
        //        await migrations.ForEachAsync(x =>
        //        {
        //            x.FrondendTimestamp = ((DateTimeOffset)x.CreatedAt).ToUnixTimeMilliseconds();
        //        });
        //        await _appDbContext.SaveChangesAsync();
        //    } while (true);
        // }

        // [HttpGet]
        // [Route("Core/fix/conversationMesage")]
        // public async Task<IActionResult> FixConversationMessage()
        // {
        //    do
        //    {
        //        string Id = "";
        //        try
        //        {
        //            var fix = await _appDbContext.ConversationMessages.Where(x => string.IsNullOrEmpty(x.CompanyId)).Include(x => x.Conversation).Take(1000).ToListAsync();
        //            Id = fix.FirstOrDefault().ConversationId;
        //            fix.ForEach(x => x.CompanyId = x.Conversation.CompanyId);
        //            await _appDbContext.SaveChangesAsync();
        //        }
        //        catch (Exception ex)
        //        {
        //            _logger.LogError($"{Id} {ex.Message}");
        //        }
        //    } while (_appDbContext.ConversationMessages.Where(x => string.IsNullOrEmpty(x.CompanyId)).Count() > 0);
        //    return Ok();
        // }

        // [HttpGet]
        // [Route("Core/fix/lastcontact")]
        // public async Task<IActionResult> FixLastContact([FromQuery(Name = "CompanyId")] string companyId)
        // {
        //    try
        //    {
        //        //do
        //        //{
        //        var userProfiles = await _appDbContext.UserProfiles.Where(x => x.CompanyId == companyId && (!x.LastContact.HasValue || !x.LastContactFromCustomers.HasValue)).ToListAsync();
        //        foreach (var userProfile in userProfiles)
        //        {
        //            var conversation = await _appDbContext.Conversations.Where(x => x.UserProfileId == userProfile.Id).FirstOrDefaultAsync();
        //            if (conversation != null)
        //            {
        //                var fromCompany = await _appDbContext.ConversationMessages.Where(x => x.Channel != ChannelTypes.Note && x.ConversationId == conversation.Id && x.IsSentFromSleekflow).OrderByDescending(x => x.CreatedAt).FirstOrDefaultAsync();
        //                if (fromCompany != null)
        //                {
        //                    userProfile.LastContact = fromCompany.CreatedAt;
        //                    await _userProfileService.SetFieldValueByFieldNameNotSaved(userProfile, "LastChannel", fromCompany.Channel);
        //                    await _userProfileService.SetFieldValueByFieldNameNotSaved(userProfile, "LastContact", userProfile.LastContact.Value.ToString("o"));
        //                }
        //                var fromUser = await _appDbContext.ConversationMessages.Where(x => x.Channel != ChannelTypes.Note && x.ConversationId == conversation.Id && !x.IsSentFromSleekflow).OrderByDescending(x => x.CreatedAt).FirstOrDefaultAsync();
        //                if (fromUser != null)
        //                {
        //                    userProfile.LastContactFromCustomers = fromUser.CreatedAt;
        //                    await _userProfileService.SetFieldValueByFieldNameNotSaved(userProfile, "LastChannel", fromUser.Channel);
        //                    await _userProfileService.SetFieldValueByFieldNameNotSaved(userProfile, "LastContactFromCustomers", userProfile.LastContactFromCustomers.Value.ToString("o"));
        //                }
        //            }
        //            await _appDbContext.SaveChangesAsync();
        //            //    }
        //            //} while (_appDbContext.UserProfiles.Where(x => !x.LastContact.HasValue || !x.LastContactFromCustomers.HasValue).Count() > 0);
        //        }
        //    }
        //    catch (Exception ex)
        //    {
        //        _logger.LogError($"{ex.Message}");
        //    }
        //    return Ok();
        // }

        // [HttpGet]
        // [Route("Core/fix/contactOwner")]
        // public async Task<IActionResult> FixContactOwner([FromQuery(Name = "CompanyId")] string companyId)
        // {
        //    try
        //    {
        //        var userProfiles = await _userProfileService.GetUserProfilesByFields(companyId, new List<Condition> { new Condition { FieldName = "ContactOwner", ConditionOperator = SupportedOperator.IsNull } });
        //        foreach (var userProfile in userProfiles.UserProfiles)
        //        {
        //            var conversation = await _appDbContext.Conversations.Where(x => x.UserProfileId == userProfile.Id && x.AssigneeId.HasValue).Include(x => x.Assignee).FirstOrDefaultAsync();
        //            if (conversation == null)
        //                continue;

        // await _userProfileService.SetFieldValueByFieldNameSafe(userProfile, "ContactOwner", conversation.Assignee.IdentityId, false);
        //        }
        //    }
        //    catch (Exception ex)
        //    {
        //        _logger.LogError($"{ex.Message}");
        //    }
        //    return Ok();
        // }

        // [HttpGet]
        // [Route("Core/fix/twilioSender")]
        // public async Task<IActionResult> FixTwilioSender()
        // {
        //    try
        //    {
        //        var whatsAppConfigs = await _appDbContext.ConfigWhatsAppConfigs.ToListAsync();
        //        foreach (var whatsappConfig in whatsAppConfigs)
        //        {
        //            var whatsappSenders = _appDbContext.SenderWhatsappSenders.Where(x => x.InstanceId == whatsappConfig.TwilioAccountId && string.IsNullOrEmpty(x.InstaneSender));
        //            await whatsappSenders.ForEachAsync(x => x.InstaneSender = whatsappConfig.WhatsAppSender);
        //            await _appDbContext.SaveChangesAsync();
        //        }
        //    }
        //    catch (Exception ex)
        //    {
        //        _logger.LogError($"{ex.Message}");
        //    }
        //    return Ok();
        // }

        // [HttpGet]
        // [Route("Core/fixlastchannel")]
        // public async Task<IActionResult> FixLastChannel()
        // {
        //    var conversations = await _appDbContext.Conversations.Where(x => string.IsNullOrEmpty(x.LastMessageChannel) && x.UserProfile.CustomFields.Where(x => x.CompanyDefinedField.FieldName.ToLower() == "lastchannel").Count() == 0 && x.ChatHistory.Where(x => x.Channel != ChannelTypes.Note).Count() > 0).ToListAsync();
        //    foreach (var conversation in conversations)
        //    {
        //        var lastMessageChannel = await _appDbContext.ConversationMessages.Where(x => x.ConversationId == conversation.Id && x.Channel != ChannelTypes.Note).OrderByDescending(x => x.CreatedAt).Select(x => x.Channel).Take(1).FirstOrDefaultAsync();
        //        conversation.LastMessageChannel = lastMessageChannel;
        //        await _userProfileService.SetFieldValueByFieldNameSafe(conversation.UserProfileId, "LastChannel", conversation.LastMessageChannel, false);
        //        await _appDbContext.SaveChangesAsync();
        //        //var count = _appDbContext.Conversations.Where(x => !string.IsNullOrEmpty(x.LastMessageChannel) && x.UserProfile.CustomFields.Where(x => x.CompanyDefinedField.FieldName.ToLower() == "lastchannel").Count() == 0).Count();
        //    }

        // conversations = await _appDbContext.Conversations.Where(x => !string.IsNullOrEmpty(x.LastMessageChannel) && x.UserProfile.CustomFields.Where(x => x.CompanyDefinedField.FieldName.ToLower() == "lastchannel").Count() == 0).ToListAsync();
        //    foreach (var conversation in conversations)
        //    {
        //        await _userProfileService.SetFieldValueByFieldNameSafe(conversation.UserProfileId, "LastChannel", conversation.LastMessageChannel, false);
        //        //var count = _appDbContext.Conversations.Where(x => !string.IsNullOrEmpty(x.LastMessageChannel) && x.UserProfile.CustomFields.Where(x => x.CompanyDefinedField.FieldName.ToLower() == "lastchannel").Count() == 0).Count();
        //    }
        //    return Ok();
        // }

        // [HttpGet]
        // [Route("Core/fixlastAssignedTeam")]
        // public async Task<IActionResult> FixAssignedTeam()
        // {
        //    var conversations = await _appDbContext.Conversations.Where(x => x.AssignedTeamId.HasValue && x.UserProfile.CustomFields.Where(x => x.CompanyDefinedField.FieldName.ToLower() == "assignedteam").Count() == 0).ToListAsync();
        //    foreach (var conversation in conversations)
        //    {
        //        await _userProfileService.SetFieldValueByFieldNameSafe(conversation.UserProfileId, "AssignedTeam", conversation.AssignedTeamId.ToString(), false);
        //        //var count = _appDbContext.Conversations.Where(x => !string.IsNullOrEmpty(x.LastMessageChannel) && x.UserProfile.CustomFields.Where(x => x.CompanyDefinedField.FieldName.ToLower() == "lastchannel").Count() == 0).Count();
        //    }
        //    return Ok();
        // }

        // [HttpGet]
        // [Route("Core/AddReferralCode")]
        // public async Task<IActionResult> AddReferralCode()
        // {
        //    var companies = await _appDbContext.CompanyCompanies.Where(x => string.IsNullOrEmpty(x.ReferralCode)).ToListAsync();
        //    foreach (var company in companies)
        //    {
        //        var newPromotion = new Promotion
        //        {
        //            CompanyId = company.Id,
        //            PromotionCode = ConversationHelper.RandomString(8),
        //            ExpiryDate = DateTime.MaxValue,
        //            ExtraAgents = 1,
        //            ExtraAutomatedMessages = 0,
        //            ExtraContacts = 0
        //        };
        //        if (_appDbContext.CorePromotions.Where(X => X.PromotionCode == newPromotion.PromotionCode && X.CompanyId == company.Id).Count() > 0)
        //            company.ReferralCode = newPromotion.PromotionCode;
        //        else if (_appDbContext.CorePromotions.Where(X => X.PromotionCode == newPromotion.PromotionCode).Count() > 0)
        //        {
        //            newPromotion.PromotionCode = $"{ConversationHelper.RandomString(8)}";
        //            _appDbContext.CorePromotions.Add(newPromotion);
        //        }
        //        else
        //            _appDbContext.CorePromotions.Add(newPromotion);

        // company.ReferralCode = newPromotion.PromotionCode;
        //        await _appDbContext.SaveChangesAsync();
        //    }
        //    return Ok();
        // }

        // [HttpGet]
        // [Route("Core/generatePromotionCode")]
        // public async Task<IActionResult> GetPromotionCode()
        // {
        //    try
        //    {
        //        List<string> results = new List<string>();
        //        var promoteCodes = await _appDbContext.CorePromotions.Where(x => x.CompanyId == null || x.RedeemRecords.Count() > 0).ToListAsync();
        //        results.Add($"====  Promote Code ====");
        //        foreach (var promoteCode in promoteCodes)
        //        {
        //            var redeemed = await _appDbContext.CoreRedeemPromotionRecords.Where(x => x.PromotionId == promoteCode.Id).ToListAsync();
        //            results.Add($"{promoteCode.PromotionCode}: Expiry Date [{promoteCode.ExpiryDate}], ExtraAgent: {promoteCode.ExtraAgents}, ExtraAutomatedMessages: {promoteCode.ExtraAutomatedMessages}, ExtraContacts: {promoteCode.ExtraContacts}, Total redeemed: {redeemed.Count}");
        //            results.Add($"====== Redeemed ======");

        // foreach (var rede in redeemed)
        //            {
        //                var company = await _appDbContext.CompanyCompanies.Where(x => x.Id == rede.CompanyId).FirstOrDefaultAsync();
        //                var staff = await _appDbContext.UserRoleStaffs.Where(X => X.CompanyId == company.Id).Include(x => x.Identity).OrderBy(x => x.Id).FirstOrDefaultAsync();
        //                var userCount = _appDbContext.UserRoleStaffs.Where(X => X.CompanyId == company.Id && X.Id != 1).Include(x => x.Identity).OrderBy(x => x.Id).Count();
        //                var usage = await _companyUsageService.GetCompanyUsage(company.Id);

        // var record = $"{company.Id}, [{company.CompanyName}], staffId: {staff.IdentityId}, email: {staff.Identity.Email}, plan: {usage.billingPeriodUsages.FirstOrDefault().BillRecord.SubscriptionPlan.Id}, totalContact {usage.totalContacts}/{usage.MaximumContacts}, maxAutomatedMessage: {usage.billingPeriodUsages.FirstOrDefault().TotalMessagesSentFromSleekflow}/{usage.MaximumAutomatedMessages}, channels: {usage.totalChannelAdded}/{usage.MaximumNumberOfChannel}, users: {usage.totalAgents}/{usage.MaximumAgents} ,{usage.billingPeriodUsages.FirstOrDefault().BillRecord.PeriodStart} - {usage.billingPeriodUsages.FirstOrDefault().BillRecord.PeriodEnd}, WhatsApp Quota: {company.MaximumWhatsappInstance}, IsVIP = {company.IsVIP}";

        // var lastCancelReson = await _appDbContext.CoreCancelSubscriptionRecords.Where(X => X.CompanyId == company.Id).OrderByDescending(x => x.Id).FirstOrDefaultAsync();
        //                if (lastCancelReson != null)
        //                    record += $", Cancel: {lastCancelReson.resaon} {lastCancelReson.others}";

        // results.Add(record);
        //            }

        // results.Add($"============");
        //        }
        //        return Ok(results);
        //    }
        //    catch (Exception ex)
        //    {
        //        return BadRequest(ex.ToString());
        //    }
        // }

        [HttpPost]
        [Route("Core/generatePromotionCode")]
        public async Task<IActionResult> GeneratePromotionCode([FromBody] PromotionCodeViewModel promotionCodeViewModel)
        {
            try
            {
                var newPromotion = new Promotion
                {
                    CompanyId = promotionCodeViewModel.companyId,
                    PromotionCode = promotionCodeViewModel.promotionCode.ToUpper().Replace(" ", string.Empty),
                    ExpiryDate = promotionCodeViewModel.expirydateHKT.AddHours(-8),
                    ExtraAgents = promotionCodeViewModel.extraAgents,
                    ExtraAutomatedMessages = promotionCodeViewModel.extraAutomatedMessages,
                    ExtraContacts = promotionCodeViewModel.extraContacts
                };

                _appDbContext.CorePromotions.Add(newPromotion);
                await _appDbContext.SaveChangesAsync();
                return Ok(newPromotion);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.ToString());
            }
        }

        // [HttpGet]
        // [Route("Core/ScheduleDisconnect")]
        // public async Task<IActionResult> ScheduleDisconnect()
        // {
        //    await _coreService.DeleteFreemiumInstances();
        //    return Ok();
        // }

        // [HttpGet]
        // [Route("Core/GetWhatsappInstance")]
        // public async Task<IActionResult> GetWhatsappInstance()
        // {
        //    try
        //    {
        //        List<string> results = new List<string>();
        //        var usedInstances = await _appDbContext.ConfigWSChatAPIConfigs.Where(x => x.IsConnected == true && x.IsDeleted == false).OrderBy(x => x.CompanyId).ToListAsync();

        // results.Add($"==== Used instances ====");
        //        foreach (var usedInstance in usedInstances)
        //        {
        //            var company = await _appDbContext.CompanyCompanies.Where(x => x.Id == usedInstance.CompanyId).FirstOrDefaultAsync();
        //            if (company.Id == "47bbfa27-29b8-4d5b-b953-5bebaa3fba28")
        //                _logger.LogError("error");

        // var usage = await _companyUsageService.GetCompanyUsage(company.Id);
        //            _logger.LogError(company.Id);

        // results.Add($"{company.Id}, [{company.CompanyName}] used whatsapp instance id: {usedInstance.WSChatAPIInstance}, {usedInstance.Name} ,created: {usedInstance.CreatedAt.AddHours(8)} ,phone: {usedInstance.WhatsAppSender}, Plan: {usage.billingPeriodUsages.FirstOrDefault().BillRecord.SubscriptionPlan?.Id}, last Status: {usedInstance.Status.ToString()}");
        //        }

        // results.Add($"==== Dedicated instances ====");
        //        var dadicatedInstances = await _appDbContext.ConfigWSChatAPIConfigs.Where(x => x.IsConnected == false && x.IsDeleted == false).OrderBy(x => x.CompanyId).ToListAsync();
        //        foreach (var usedInstance in dadicatedInstances)
        //        {
        //            var company = await _appDbContext.CompanyCompanies.Where(x => x.Id == usedInstance.CompanyId).FirstOrDefaultAsync();
        //            var usage = await _companyUsageService.GetCompanyUsage(company.Id);
        //            results.Add($"{company.Id}, [{company.CompanyName}] dedicated whatsapp instance id: {usedInstance.WSChatAPIInstance}, {usedInstance.Name}, created: {usedInstance.CreatedAt.AddHours(8)} , phone: {usedInstance.WhatsAppSender}, Plan: {usage.billingPeriodUsages.FirstOrDefault().BillRecord.SubscriptionPlan?.Id}, last Status: {usedInstance.Status.ToString()}");
        //        }

        // results.Add($"==== Unused instances ====");
        //        var freeInstance = _appDbContext.CoreWhatsappChatAPIInstances.Where(x => !usedInstances.Select(y => y.WSChatAPIInstance).Contains(x.InstanceId) && !dadicatedInstances.Select(y => y.WSChatAPIInstance).Contains(x.InstanceId)).Select(x => x.InstanceId);
        //        var freeInstanceResult = string.Join(", ", freeInstance.ToArray());
        //        results.Add($"free instance: {freeInstanceResult}");

        // return Ok(results);
        //    }
        //    catch (Exception ex)
        //    {
        //        _logger.LogError($"{ex.Message}");
        //    }
        //    return BadRequest();
        // }

        // [HttpGet]
        // [Route("Core/GetAllChatAPIList")]
        // public IActionResult GetWhatsappInstanceList()
        // {
        //    try
        //    {
        //        List<string> results = new List<string>();

        // var freeInstance = _appDbContext.CoreWhatsappChatAPIInstances.OrderBy(x => x.InstanceId).Select(x => x.InstanceId);
        //        var freeInstanceResult = string.Join(", ", freeInstance.ToArray());
        //        results.Add($"Instances: {freeInstanceResult}");

        // return Ok(results);
        //    }
        //    catch (Exception ex)
        //    {
        //        _logger.LogError($"{ex.Message}");
        //    }
        //    return BadRequest();
        // }

        // [HttpGet]
        // [Route("Core/GetFacebookConnected")]
        // public async Task<IActionResult> GetFacebookConnected()
        // {
        //    try
        //    {
        //        List<string> results = new List<string>();
        //        var usedInstances = _appDbContext.ConfigFacebookConfigs.OrderBy(x => x.CompanyId);

        // results.Add($"==== Connected Messenger ====");
        //        foreach (var usedInstance in usedInstances.Where(x => x.SubscribedFields.Contains("messages")).ToList())
        //        {
        //            var company = await _appDbContext.CompanyCompanies.Where(x => x.Id == usedInstance.CompanyId).FirstOrDefaultAsync();
        //            results.Add($"{company.Id}, [{company.CompanyName}] used whatsapp page id: {usedInstance.PageId}, page Name: {usedInstance.PageName}, created: {usedInstance.ConnectedDateTime.AddHours(8)}");
        //        }

        // results.Add($"==== Connected Lead Ads ====");
        //        foreach (var usedInstance in usedInstances.Where(x => x.SubscribedFields.Contains("leadgen")).ToList())
        //        {
        //            var company = await _appDbContext.CompanyCompanies.Where(x => x.Id == usedInstance.CompanyId).FirstOrDefaultAsync();
        //            results.Add($"{company.Id}, [{company.CompanyName}] used whatsapp page id: {usedInstance.PageId}, page Name: {usedInstance.PageName}, created: {usedInstance.ConnectedDateTime.AddHours(8)}");
        //        }

        // return Ok(results);
        //    }
        //    catch (Exception ex)
        //    {
        //        _logger.LogError($"{ex.Message}");
        //    }
        //    return BadRequest();
        // }

        public class SetFreeTiral
        {
            public string CompanyId { get; set; }

            public DateTime? TrialEnd { get; set; }

            public int? agentCount { get; set; }

            public int? ContactLimit { get; set; }

            public int? WhatsappCount { get; set; }

            public string SubscriptionPlanId { get; set; }

            public bool? IsVIP { get; set; }

            public int? MaximumAutomations { get; set; }

            public int? MaximumCampaignMessages { get; set; }
        }

        // [HttpPost]
        // [Route("core/password/reset")]
        // public async Task<IActionResult> ResetPassword([FromBody] ResetPasswordRequestViewModel resetPasswordRequestViewModel)
        // {
        //    var userId = _userManager.GetUserId(User);
        //    var companyUser = await _appDbContext.UserRoleStaffs.Where(x => x.IdentityId == userId).FirstOrDefaultAsync();
        //    if (companyUser.Id != 1)
        //        return BadRequest();

        // if (ModelState.IsValid)
        //    {
        //        var userIdentity = await _userManager.FindByEmailAsync(resetPasswordRequestViewModel.Email);
        //        if (userIdentity == null)
        //            return BadRequest(new ResponseViewModel { message = "user not found" });

        // var code = await _userManager.GeneratePasswordResetTokenAsync(userIdentity);
        //        var appDomainName = _configuration.GetValue<String>("Values:AppDomainName");

        // var url = $"{appDomainName}/reset/password?userId={userIdentity.Id}&code={System.Web.HttpUtility.UrlEncode(code)}";

        // dynamic response = new JObject();
        //        response.url = url;
        //        return Ok(response);
        //    }
        //    return BadRequest(ModelState);
        // }

        // [HttpGet]
        // [Route("Core/GetAllTwilioUsage")]
        // public async Task<IActionResult> GetTwilioUsage()
        // {
        //    var str = "";
        //    try
        //    {
        //        var twilioConfig = _appDbContext.CompanyTwilioUsageRecords.ToList();
        //        foreach (var usage in twilioConfig)
        //        {
        //            var companyName = await _appDbContext.CompanyCompanies.Where(x => x.Id == usage.CompanyId).FirstOrDefaultAsync();
        //            str += $"AccountSID: {usage.TwilioAccountId} {companyName.CompanyName}, companyId: {companyName.Id}, {usage.Description}, {usage.Start} - {usage.End} ${usage.TotalPrice} / {usage.TotalCreditValue}, Balance: {(usage.TotalCreditValue - usage.TotalPrice)}\n";
        //        }

        // var coreTwilioConfig = await _appDbContext.CoreTwilioConfigs.FirstOrDefaultAsync();
        //        TwilioClient.Init(coreTwilioConfig.AccountSID, coreTwilioConfig.AccountSecret);

        // var accounts = AccountResource.Read();

        // foreach (var twilioId in accounts.Where(x => !twilioConfig.Select(x => x.TwilioAccountId).Contains(x.Sid)))
        //        {
        //            str += $"Not on SleekFlow, AccountSID: {twilioId.Sid} {twilioId.FriendlyName}\n";
        //        }
        //    }

        // catch (Exception ex)
        //    {
        //        str += ex.Message;
        //    }

        // return Ok(str);
        // }

        // [HttpPost]
        // [Route("Core/SetTwilioCredit")]
        // public async Task<IActionResult> SetTwilioCredit([FromBody] SetTwilioCreditModel twilioCredit)
        // {
        //    var record = await _appDbContext.CompanyTwilioUsageRecords.Where(x => x.TwilioAccountId == twilioCredit.TwilioAccountId).FirstOrDefaultAsync();
        //    if (record == null)
        //    {
        //        if (string.IsNullOrEmpty(twilioCredit.CompanyId))
        //        {
        //            return Ok("Please provide companyId");
        //        }
        //        record = new TwilioUsageRecord { CompanyId = twilioCredit.CompanyId, TwilioAccountId = twilioCredit.TwilioAccountId };
        //        _appDbContext.CompanyTwilioUsageRecords.Add(record);
        //    }
        //    record.TotalCreditValue = twilioCredit.CreditValue;
        //    await _appDbContext.SaveChangesAsync();

        // var coreTwilio = await _appDbContext.CoreTwilioConfigs.FirstOrDefaultAsync();
        //    try
        //    {
        //        TwilioClient.Init(coreTwilio.AccountSID, coreTwilio.AccountSecret);

        // var account = AccountResource.Update(
        //            status: AccountResource.StatusEnum.Active,
        //            pathSid: twilioCredit.TwilioAccountId
        //        );
        //    } catch (Exception ex)
        //    {
        //        _logger.LogError($"Set TwilioCreidt: {ex.ToString()}");
        //    }

        // return Ok(record);
        // }

        // [HttpPost]
        // [Route("Core/Twilio/Active")]
        // public async Task<IActionResult> ActiveTwilioSubAccount([FromBody] SetTwilioCreditModel twilioCredit)
        // {
        //    var coreTwilioConfig = await _appDbContext.CoreTwilioConfigs.FirstOrDefaultAsync();
        //    TwilioClient.Init(coreTwilioConfig.AccountSID, coreTwilioConfig.AccountSecret);

        // var account = AccountResource.Update(
        //        status: AccountResource.StatusEnum.Active,
        //        pathSid: twilioCredit.TwilioAccountId
        //    );

        // return Ok(account.Sid);
        // }

        // [HttpPost]
        // [Route("Core/Twilio/Suspend")]
        // public async Task<IActionResult> SuspendTwilioSubAccount([FromBody] SetTwilioCreditModel twilioCredit)
        // {
        //    var coreTwilioConfig = await _appDbContext.CoreTwilioConfigs.FirstOrDefaultAsync();
        //    TwilioClient.Init(coreTwilioConfig.AccountSID, coreTwilioConfig.AccountSecret);

        // var account = AccountResource.Update(
        //        status: AccountResource.StatusEnum.Suspended,
        //        pathSid: twilioCredit.TwilioAccountId
        //    );

        // return Ok(account.Sid);
        // }

        // public class SetTwilioCreditModel
        // {
        //    public string TwilioAccountId { get; set; }
        //    public string CompanyId { get; set; }
        //    public decimal CreditValue { get; set; }
        // }

        // [HttpPost]
        // [Route("Core/Company/Trial")]
        // public async Task<IActionResult> SetTrial([FromForm] SetFreeTiral setFreeTiral)
        // {
        //    try
        //    {
        //        var company = await _appDbContext.CompanyCompanies.Where(x => x.Id == setFreeTiral.CompanyId).Include(x => x.BillRecords).FirstOrDefaultAsync();
        //        if (setFreeTiral.IsVIP.HasValue)
        //            company.IsVIP = setFreeTiral.IsVIP.Value;
        //        if (setFreeTiral.agentCount.HasValue)
        //            company.MaximumAgents = setFreeTiral.agentCount.Value;
        //        if (setFreeTiral.WhatsappCount.HasValue)
        //            company.MaximumWhatsappInstance = setFreeTiral.WhatsappCount.Value;
        //        if (setFreeTiral.ContactLimit.HasValue)
        //            company.MaximumContacts = setFreeTiral.ContactLimit.Value;
        //        if (setFreeTiral.MaximumAutomations.HasValue)
        //            company.MaximumAutomations = setFreeTiral.MaximumAutomations.Value;
        //        if (setFreeTiral.MaximumCampaignMessages.HasValue)
        //            company.MaximumWhAutomatedMessages = setFreeTiral.MaximumCampaignMessages.Value;

        // if (!string.IsNullOrEmpty(setFreeTiral.SubscriptionPlanId))
        //        {
        //            company.IsFreeTrial = false;
        //            var newBill = new BillRecord()
        //            {
        //                SubscriptionPlanId = setFreeTiral.SubscriptionPlanId,
        //                PayAmount = 0,
        //                CompanyId = company.Id,
        //                Status = BillStatus.Active,
        //                PaymentStatus = PaymentStatus.FreeOfCharge,
        //                PeriodStart = DateTime.UtcNow,
        //                PeriodEnd = setFreeTiral.TrialEnd.Value
        //            };
        //            company.BillRecords.Add(newBill);

        // company.MaximumContacts = null;
        //            company.MaximumWhAutomatedMessages = null;
        //            company.MaximumAutomations = null;

        // var subscriptionPlan = await _appDbContext.CoreSubscriptionPlans.Where(x => x.Id == setFreeTiral.SubscriptionPlanId).FirstOrDefaultAsync();
        //            var apiKey = _appDbContext.CompanyAPIKeys.Where(x => x.CompanyId == company.Id);
        //            await apiKey.ForEachAsync(x =>
        //            {
        //                x.CallLimit = subscriptionPlan.MaximumAPICall;
        //                x.Calls = 0;
        //            });
        //        }
        //        await _appDbContext.SaveChangesAsync();

        // return Ok(_mapper.Map<CompanyResponse>(company));
        //    }
        //    catch (Exception ex)
        //    {
        //        _logger.LogError($"{ex.Message}");
        //    }
        //    return BadRequest();
        // }

        // [HttpGet]
        // [Route("Core/Company")]
        // public async Task<IActionResult> GetCompany([FromQuery(Name = "offset")] int offset = 0, [FromQuery(Name = "limit")] int limit = 0, [FromQuery(Name = "companyname")] string companyName = "", [FromQuery(Name = "companyid")] string companyid = "", [FromQuery(Name = "email")] string email = "")
        // {
        //    try
        //    {
        //        List<string> results = new List<string>();
        //        var companies = (from company in _appDbContext.CompanyCompanies
        //                         orderby company.CreatedAt descending
        //                         select company);

        // if (!string.IsNullOrEmpty(companyName))
        //            companies = (from company in companies
        //                         where company.CompanyName.Contains(companyName)
        //                         orderby company.CreatedAt descending
        //                         select company);

        // if (!string.IsNullOrEmpty(companyid))
        //            companies = (from company in companies
        //                         where company.Id == companyid
        //                         orderby company.CreatedAt descending
        //                         select company);

        // if (!string.IsNullOrEmpty(email))
        //            companies = (from company in companies
        //                         where _appDbContext.UserRoleStaffs.Where(X => X.Identity.Email == email).Select(x => x.CompanyId).Contains(company.Id)
        //                         orderby company.CreatedAt descending
        //                         select company);

        // foreach (var company in companies.Skip(offset).Take(limit).ToList())
        //        {
        //            var staff = await _appDbContext.UserRoleStaffs.Where(X => X.CompanyId == company.Id).Include(x => x.Identity).OrderBy(x => x.Id).FirstOrDefaultAsync();
        //            var userCount = _appDbContext.UserRoleStaffs.Where(X => X.CompanyId == company.Id && X.Id != 1).Include(x => x.Identity).OrderBy(x => x.Id).Count();
        //            var usage = await _companyUsageService.GetCompanyUsage(company.Id);

        // var record = $"{company.Id}, [{company.CompanyName}], staffId: {staff.IdentityId}, email: {staff.Identity.Email}, {staff.Identity.PhoneNumber}, plan: {usage.billingPeriodUsages.FirstOrDefault().BillRecord.SubscriptionPlan.Id}, totalContact {usage.totalContacts}/{usage.MaximumContacts}, maxAutomatedMessage: {usage.billingPeriodUsages.FirstOrDefault().TotalMessagesSentFromSleekflow}/{usage.MaximumAutomatedMessages}, channels: {usage.totalChannelAdded}/{usage.MaximumNumberOfChannel}, users: {usage.totalAgents}/{usage.MaximumAgents} ,{usage.billingPeriodUsages.FirstOrDefault().BillRecord.PeriodStart} - {usage.billingPeriodUsages.FirstOrDefault().BillRecord.PeriodEnd}, WhatsApp Quota: {company.MaximumWhatsappInstance}, IsVIP = {company.IsVIP}";

        // var lastCancelReson = await _appDbContext.CoreCancelSubscriptionRecords.Where(X => X.CompanyId == companyid).OrderByDescending(x => x.Id).FirstOrDefaultAsync();
        //            if (lastCancelReson != null)
        //                record += $", Cancel: {lastCancelReson.resaon} {lastCancelReson.others}";

        // results.Add(record);
        //        }

        // return Ok(results);
        //    }
        //    catch (Exception ex)
        //    {
        //        _logger.LogError($"{ex.Message}");
        //    }
        //    return BadRequest();
        // }

        // [HttpPost]
        // [Route("Core/InvestigateCompany")]
        // public async Task<IActionResult> InvestigateCompany([FromQuery(Name = "companyId")] string companyId)
        // {
        //    try
        //    {
        //        var adminAccount = await _appDbContext.UserRoleStaffs.Where(x => x.Id == 1).FirstOrDefaultAsync();
        //        adminAccount.CompanyId = companyId;
        //        await _appDbContext.SaveChangesAsync();

        // return Ok(new ResponseViewModel { message = "done" });
        //    }
        //    catch (Exception ex)
        //    {
        //        _logger.LogError($"{ex.Message}");
        //    }
        //    return BadRequest();
        // }

        // [HttpGet]
        // [Route("Core/TargetedChannelMigrations")]
        // public async Task<IActionResult> TargetChannelMigrations()
        // {
        //    try
        //    {
        //        var migrationCampaign = await _appDbContext.CompanyMessageTemplates.ToListAsync();
        //        foreach (var campaign in migrationCampaign)
        //        {
        //            if (campaign.TargetedChannelWithIds == null || campaign.TargetedChannelWithIds?.Count == 0)
        //            {
        //                if (campaign.TargetedChannels?.Count > 0)
        //                {
        //                    var newTargett = new List<TargetedChannelModel>();
        //                    foreach (var channel in campaign.TargetedChannels)
        //                        newTargett.Add(new TargetedChannelModel { channel = channel });

        // if (newTargett.Count > 0)
        //                    {
        //                        campaign.TargetedChannelWithIds = newTargett;
        //                        await _appDbContext.SaveChangesAsync();
        //                    }
        //                }
        //            }
        //        }

        // var migrationAssignmentRule = await _appDbContext.CompanyAssignmentRules.ToListAsync();
        //        foreach (var rule in migrationAssignmentRule)
        //        {
        //            if (rule.TargetedChannelWithIds == null || rule.TargetedChannelWithIds?.Count == 0)
        //            {
        //                if (rule.TargetedChannels?.Count > 0)
        //                {
        //                    var newTargett = new List<TargetedChannelModel>();
        //                    foreach (var channel in rule.TargetedChannels)
        //                        newTargett.Add(new TargetedChannelModel { channel = channel });

        // if (newTargett.Count > 0)
        //                    {
        //                        rule.TargetedChannelWithIds = newTargett;
        //                        await _appDbContext.SaveChangesAsync();
        //                    }
        //                }
        //            }
        //        }

        // return Ok();
        //    }
        //    catch (Exception ex)
        //    {
        //        _logger.LogError($"{ex.Message}");
        //        return BadRequest(ex);
        //    }
        // }

        // [HttpGet]
        // [Route("core/list/{groupId}/addall")]
        // public async Task<IActionResult> TargetChannelMigrations(long groupId)
        // {
        //    var group = await _appDbContext.CompanyImportContactHistories.Where(x => x.Id == groupId).Include(x => x.ImportedUserProfiles).Include(x => x.ImportedFrom.Identity).FirstOrDefaultAsync();
        //    if (group == null)
        //        return NotFound(new ResponseViewModel { message = $"GroupID: {groupId} not found." });

        // var allContacts = await _appDbContext.UserProfiles.Where(x => x.CompanyId == group.CompanyId &&
        //                        x.ActiveStatus == ActiveStatus.Active &&
        //                ((_appDbContext.Conversations.Where(y => y.CompanyId == group.CompanyId && y.ActiveStatus == ActiveStatus.Active).Select(y => y.UserProfileId).Contains(x.Id)) ||
        //                 _appDbContext.Conversations.Where(y => y.CompanyId == group.CompanyId && y.UserProfileId == x.Id).Count() == 0)).Select(x => x.Id).ToListAsync();

        // foreach (var contact in allContacts.Where(x => !group.ImportedUserProfiles.Select(x => x.UserProfileId).Contains(x)))
        //    {
        //        group.ImportedUserProfiles.Add(new ImportedUserProfile { UserProfileId = contact });
        //    }
        //    await _appDbContext.SaveChangesAsync();

        // return Ok();
        // }

        // [HttpGet]
        // [Route("core/fixnullphonenumber/{companyId}")]
        // public async Task<IActionResult> fixnullphonenumber(string companyId)
        // {
        //    var userProfiles = await _userProfileService.GetUserProfilesByFields(companyId, new List<Condition> { new Condition {
        //        FieldName = "PhoneNumber",
        //        ConditionOperator = SupportedOperator.IsNull
        //    }});

        // foreach (var userProfile in userProfiles.UserProfiles)
        //    {
        //        if (!string.IsNullOrEmpty(userProfile.WhatsAppAccount?.phone_number))
        //        {
        //            await _userProfileService.SetFieldValueByFieldNameSafe(userProfile.Id, "phonenumber", userProfile.WhatsAppAccount.phone_number);
        //        }
        //    }

        // return Ok();
        // }

        // [HttpGet]
        // [Route("core/twilio/{messageSID}")]
        // public async Task<IActionResult> getMessageStatus(string messageSID)
        // {
        //    var coreTwilioConfig = await _appDbContext.CoreTwilioConfigs.FirstOrDefaultAsync();
        //    TwilioClient.Init(coreTwilioConfig.AccountSID, coreTwilioConfig.AccountSecret);
        //    var fetchMessage = MessageResource.Fetch(messageSID);
        //    return Ok(fetchMessage);
        // }

        // [HttpGet]
        // [Route("core/deleteNotifcation")]
        // public async Task<IActionResult> DeleteNotification()
        // {
        //    do
        //    {
        //        var notifcations = await _appDbContext.CompanyNotificationRecords.Where(x => x.CompanyId == "727edaae-933f-402b-857c-db89af3956bd" && x.EventName == "OnImportFailed").Take(300).ToListAsync();
        //        _appDbContext.CompanyNotificationRecords.RemoveRange(notifcations);
        //        await _appDbContext.SaveChangesAsync();
        //    } while (_appDbContext.CompanyNotificationRecords.Where(x => x.CompanyId == "727edaae-933f-402b-857c-db89af3956bd" && x.EventName == "OnImportFailed").Count() > 0);
        //    return Ok();
        // }

        /*
        [HttpGet]
        [Route("core/MigrateActivityLog")]
        public async Task<IActionResult> MigrateActivityLog()
        {
            var conversationRemarks = await _appDbContext.ConversationRemarks.ToListAsync();
            foreach (var conversationRemark in conversationRemarks)
            {
                var conversation = await _appDbContext.Conversations.Where(x => x.Id == conversationRemark.ConversationId).FirstOrDefaultAsync();
                var convert = _mapper.Map<UserProfileRemark>(conversationRemark);
                convert.Id = 0;
                convert.UserProfileId = conversation.UserProfileId;
                _appDbContext.UserProfileRemarks.Add(convert);
            }
            await _appDbContext.SaveChangesAsync();
            return Ok();
        } */

        // [HttpGet]
        // [Route("core/addReferralCodeSleekFlow")]
        // public async Task<IActionResult> AddReferralCodeSleekFlow()
        // {
        //    var allCompanies = await _appDbContext.CompanyCompanies.ToListAsync();
        //    foreach (var company in allCompanies)
        //    {
        //        var owner = await _appDbContext.UserRoleStaffs.Where(x => x.CompanyId == company.Id).OrderBy(x => x.Order).Include(x => x.Identity).Take(1).FirstOrDefaultAsync();
        //        var companyId = _configuration.GetValue<String>("Values:SleekFlowCompanyId");

        // var userProfiles = await _userProfileService.GetUserProfilesByFields(companyId, new List<Condition> {
        //                new Condition { FieldName = "PhoneNumber", Values = new List<string> { owner.Identity.PhoneNumber }, ConditionOperator = SupportedOperator.Equals }
        //            });

        // if (userProfiles.UserProfiles.FirstOrDefault() == null)
        //            userProfiles = await _userProfileService.GetUserProfilesByFields(companyId, new List<Condition> {
        //                new Condition { FieldName = "Email", Values = new List<string> { owner.Identity.Email }, ConditionOperator = SupportedOperator.Equals }
        //            });

        // if (userProfiles.TotalResult == 0)
        //            continue;

        // var headers = new List<ImportHeader>();
        //        var fields = new List<string>();

        // if (!string.IsNullOrEmpty(company.ReferralCode))
        //        {
        //            await _userProfileService.SetFieldValueByFieldNameNotSaved(userProfiles.UserProfiles.FirstOrDefault(), "Referral Code", company.ReferralCode);
        //            await _appDbContext.SaveChangesAsync();
        //        }
        //    }
        //    return Ok();
        // }

        [HttpGet]
        [AllowAnonymous]
        [Route("core/addListsFields")]
        public async Task<IActionResult> AddLabels()
        {
            var fuckCompanies = await _appDbContext.CompanyCompanies
                .Where(x => !x.CustomUserProfileFields.Any(y => y.FieldName == "Lists"))
                .Include(x => x.CustomUserProfileFields).ToListAsync(HttpContext.RequestAborted);
            foreach (var fuckCompany in fuckCompanies)
            {
                fuckCompany.CustomUserProfileFields.Add(
                    new CompanyCustomUserProfileField
                    {
                        FieldName = "Lists",
                        Type = FieldDataType.Lists,
                        Order = 1,
                        CustomUserProfileFieldLinguals = new List<CustomUserProfileFieldLingual>()
                        {
                            new CustomUserProfileFieldLingual
                            {
                                DisplayName = "Lists", Language = "en"
                            },
                            new CustomUserProfileFieldLingual
                            {
                                DisplayName = "名單", Language = "zh-hk"
                            }
                        },
                        IsDeletable = false,
                        IsEditable = false,
                        IsDefault = true,
                        IsVisible = true
                    });
                fuckCompany.CustomUserProfileFields.Where(x => x.Order >= 1).ToList()
                    .ForEach(x => x.Order = x.Order + 1);
                await _appDbContext.SaveChangesAsync();
            }

            return Ok();
        }

        // [HttpGet]
        // [Route("core/addLabelsFields")]
        // public async Task<IActionResult> AddLabels()
        // {
        //    var fuckCompanies = await _appDbContext.CompanyCompanies.Where(x => x.CustomUserProfileFields.Where(y => y.FieldName == "Labels").Count() == 0).Include(x => x.CustomUserProfileFields).ToListAsync();
        //    foreach (var fuckCompany in fuckCompanies)
        //    {
        //        fuckCompany.CustomUserProfileFields.Add(new CompanyCustomUserProfileField
        //        {
        //            FieldName = "Labels",
        //            Type = FieldDataType.Labels,
        //            Order = 0,
        //            CustomUserProfileFieldLinguals = new List<CustomUserProfileFieldLingual>() {
        //                                    new CustomUserProfileFieldLingual { DisplayName = "Labels", Language = "en" },
        //                                    new CustomUserProfileFieldLingual { DisplayName = "標籤", Language = "zh-hk" }
        //                                },
        //            IsDeletable = false,
        //            IsEditable = false,
        //            IsDefault = true,
        //            IsVisible = false
        //        });
        //        await _appDbContext.SaveChangesAsync();
        //    }
        //    return Ok();
        // }

        // [HttpGet]
        // [Route("core/addbackFields")]
        // public async Task<IActionResult> AddFeilds()
        // {
        //    var fuckCompany = await _appDbContext.CompanyCompanies.Where(x => x.Id == "420d7ea3-6174-4fe6-984e-fcd320072e54").Include(x => x.CustomUserProfileFields).FirstOrDefaultAsync();
        //    fuckCompany.CustomUserProfileFields.Add(new CompanyCustomUserProfileField
        //    {
        //        FieldName = "LastChannel",
        //        Type = FieldDataType.Channel,
        //        Order = 10,
        //        CustomUserProfileFieldLinguals = new List<CustomUserProfileFieldLingual>() {
        //                     new CustomUserProfileFieldLingual { DisplayName = "Last Channel", Language = "en" },
        //                     new CustomUserProfileFieldLingual { DisplayName = "最後聯繫頻道", Language = "zh-hk" }
        //               },
        //        IsEditable = false,
        //        IsDeletable = false
        //    });
        //    fuckCompany.CustomUserProfileFields.Add(new CompanyCustomUserProfileField
        //    {
        //        FieldName = "LastContact",
        //        Type = FieldDataType.DateTime,
        //        Order = 11,
        //        CustomUserProfileFieldLinguals = new List<CustomUserProfileFieldLingual>() {
        //                     new CustomUserProfileFieldLingual { DisplayName = "Last Contact From You", Language = "en" },
        //                     new CustomUserProfileFieldLingual { DisplayName = "最後聯絡時間", Language = "zh-hk" }
        //               },
        //        IsEditable = false,
        //        IsDeletable = false
        //    });
        //    fuckCompany.CustomUserProfileFields.Add(new CompanyCustomUserProfileField
        //    {
        //        FieldName = "LastContactFromCustomers",
        //        Type = FieldDataType.DateTime,
        //        Order = 12,
        //        CustomUserProfileFieldLinguals = new List<CustomUserProfileFieldLingual>() {
        //                     new CustomUserProfileFieldLingual { DisplayName = "Last Contact From Customers", Language = "en" },
        //                     new CustomUserProfileFieldLingual { DisplayName = "客戶的最後聯絡時間", Language = "zh-hk" }
        //               },
        //        IsEditable = false,
        //        IsDeletable = false
        //    });
        //    await _appDbContext.SaveChangesAsync();

        // return Ok();
        // }

        // [HttpPost]
        // [Route("Core/IssueAPIKey")]
        // public async Task<IActionResult> IssueAPIKey([FromBody] CoreIssueAPIKey coreIssueAPIKey)
        // {
        //    if (User.Identity.IsAuthenticated)
        //    {
        //        var userId = _userManager.GetUserId(User);

        // var keyseed = Guid.NewGuid().ToString();
        //        var APIKey = "";

        // using (var sha256 = SHA256.Create())
        //        {
        //            byte[] saltedPasswordAsBytes = Encoding.UTF8.GetBytes(keyseed);
        //            APIKey = RemoveSpecialCharacters(Convert.ToBase64String(sha256.ComputeHash(saltedPasswordAsBytes)));
        //        }

        // var companyAPIKey = new CompanyAPIKey { APIKey = APIKey, CompanyId = coreIssueAPIKey.CompanyId, Permissions = coreIssueAPIKey.APIPremissions };
        //        _appDbContext.CompanyAPIKeys.Add(companyAPIKey);
        //        await _appDbContext.SaveChangesAsync();
        //        return Ok(new ResponseViewModel { message = $"{APIKey}" });
        //    }
        //    return BadRequest();
        // }

        // [HttpDelete]
        // [Route("core/delete/instance/{instanceId}")]
        // public async Task<IActionResult> ScheduleLoad(string instanceId)
        // {
        //    var apiUrl = _configuration.GetValue<String>("ChatAPI:APIUrl");
        //    var apiKey = _configuration.GetValue<String>("ChatAPI:APIKey");

        // HttpClient client = new HttpClient();
        //    var chatapiResponse = await client.PostAsJsonAsync($"{apiUrl}/deleteInstance", new DeleteInstanceViewModel { uid = apiKey, instanceId = instanceId });
        //    if (chatapiResponse.IsSuccessStatusCode)
        //    {
        //        await _appDbContext.ConfigWSChatAPIConfigs.Where(x => x.WSChatAPIInstance == instanceId).ForEachAsync(x => x.IsDeleted = true);
        //        _appDbContext.CoreWhatsappChatAPIInstances.RemoveRange(_appDbContext.CoreWhatsappChatAPIInstances.Where(x => x.InstanceId == instanceId));

        // await _appDbContext.SaveChangesAsync();
        //        return Ok(await chatapiResponse.Content.ReadAsStringAsync());
        //    }
        //    return BadRequest();
        // }

        // [HttpGet]
        // [Route("core/twilio/addAllTemplate")]
        // public async Task<IActionResult> AddAllTemplate()
        // {
        //    var twilios = await _appDbContext.ConfigWhatsAppConfigs.ToListAsync();
        //    foreach (var twilio in twilios)
        //    {
        //        var company = await _appDbContext.CompanyCompanies.Where(x => x.Id == twilio.CompanyId).FirstOrDefaultAsync();
        //        try
        //        {
        //            await _twilioService.AddNewTemplate(twilio.TwilioAccountId, twilio.TwilioSecret, new CreateTemplateViewModel
        //            {
        //                Name = "optin_1",
        //                Category = "ACCOUNT_UPDATE",
        //                Languages = new List<LanguageElement> {
        //                        new LanguageElement { Language = "en", Content = $"*Notification*\nHello there! You received a new message from {company.CompanyName}.",
        //                            Components = new List<Component> { new Component { Type = "BUTTONS", Buttons = new List<Button> { new Button { Text = "Read more", Type = "QUICK_REPLY"}}}}},
        //                        new LanguageElement { Language = "zh_HK", Content = $"*新訊息通知*\n你好! 你收到來自{company.CompanyName}的新訊息。",
        //                            Components = new List<Component> { new Component { Type = "BUTTONS", Buttons = new List<Button> { new Button { Text = "查看更多", Type = "QUICK_REPLY" }}}}}
        //                    }
        //            });
        //        } catch
        //        {

        // }
        //        try
        //        {
        //            await _twilioService.AddNewTemplate(twilio.TwilioAccountId, twilio.TwilioSecret, new CreateTemplateViewModel
        //            {
        //                Name = "optin_2",
        //                Category = "ACCOUNT_UPDATE",
        //                Languages = new List<LanguageElement> {
        //                        new LanguageElement { Language = "en", Content = $"Hello there! You received an update from {company.CompanyName}.",
        //                            Components = new List<Component> { new Component { Type = "BUTTONS", Buttons = new List<Button> { new Button { Text = "Read more", Type = "QUICK_REPLY"}}}}},
        //                        new LanguageElement { Language = "zh_HK", Content = $"你好! 以下為一則來自{company.CompanyName}的新訊息。",
        //                            Components = new List<Component> { new Component { Type = "BUTTONS", Buttons = new List<Button> { new Button { Text = "查看更多", Type = "QUICK_REPLY" }}}}}
        //                    }
        //            });
        //        }
        //        catch { }
        //        try
        //        {
        //            await _twilioService.AddNewTemplate(twilio.TwilioAccountId, twilio.TwilioSecret, new CreateTemplateViewModel
        //            {
        //                Name = "optin_3",
        //                Category = "ACCOUNT_UPDATE",
        //                Languages = new List<LanguageElement> {
        //                        new LanguageElement { Language = "en", Content = $"*Notification*\nHello there! You received a new message from {twilio.Name}.",
        //                            Components = new List<Component> { new Component { Type = "BUTTONS", Buttons = new List<Button> { new Button { Text = "Read more", Type = "QUICK_REPLY"}}}}},
        //                        new LanguageElement { Language = "zh_HK", Content = $"*新訊息通知*\n你好! 你收到來自{twilio.Name}的新訊息。",
        //                            Components = new List<Component> { new Component { Type = "BUTTONS", Buttons = new List<Button> { new Button { Text = "查看更多", Type = "QUICK_REPLY" }}}}}
        //                    }
        //            });
        //        }
        //        catch { }
        //        try
        //        {
        //            await _twilioService.AddNewTemplate(twilio.TwilioAccountId, twilio.TwilioSecret, new CreateTemplateViewModel
        //            {
        //                Name = "optin_4",
        //                Category = "ACCOUNT_UPDATE",
        //                Languages = new List<LanguageElement> {
        //                        new LanguageElement { Language = "en", Content = $"Hello there! You received an update from {twilio.Name}.",
        //                            Components = new List<Component> { new Component { Type = "BUTTONS", Buttons = new List<Button> { new Button { Text = "Read more", Type = "QUICK_REPLY"}}}}},
        //                        new LanguageElement { Language = "zh_HK", Content = $"你好! 以下為一則來自{twilio.Name}的新訊息。",
        //                            Components = new List<Component> { new Component { Type = "BUTTONS", Buttons = new List<Button> { new Button { Text = "查看更多", Type = "QUICK_REPLY" }}}}}
        //                    }
        //            });
        //        }
        //        catch { }
        //        try
        //        {
        //            await _twilioService.AddNewTemplate(twilio.TwilioAccountId, twilio.TwilioSecret, new CreateTemplateViewModel
        //            {
        //                Name = "greetings_hello_1",
        //                Category = "ACCOUNT_UPDATE",
        //                Languages = new List<LanguageElement> {
        //                        new LanguageElement { Language = "en", Content = "Hello {{1}}" },
        //                        new LanguageElement { Language = "zh_HK", Content = "你好 {{1}}" }
        //                    }
        //            });
        //        }
        //        catch { }
        //    }
        //    return Ok();
        // }

        // [HttpPost]
        // [Route("core/twilio/subaccount/{companyId}")]
        // public async Task<IActionResult> ConnectTwilioWhatsapp(string companyId, [FromBody] ConnectTwilioViewModel connectTwilioViewModel)
        // {
        //    if (ModelState.IsValid)
        //    {
        //        var userId = _userManager.GetUserId(User);
        //        var companyUser = await _appDbContext.UserRoleStaffs.Where(x => x.IdentityId == userId).Include(x => x.Company).FirstOrDefaultAsync();

        // if (companyUser == null)
        //            return Unauthorized();

        // var companyUsage = await _companyUsageService.GetCompanyUsage(companyId);
        //        if (companyUsage.totalChannelAdded > companyUsage.billingPeriodUsages.FirstOrDefault()?.BillRecord.SubscriptionPlan.MaximumNumberOfChannel)
        //        {
        //            _logger.LogWarning($"Connecting WhatsApp with QRcode, companyId: {companyUser.CompanyId}, userId: {companyUser.IdentityId}");
        //            return BadRequest(new ResponseViewModel { message = $"Your subscription plan only could add {companyUsage.MaximumNumberOfChannel} channels" });
        //        }

        // var existing = _appDbContext.ConfigWhatsAppConfigs.Where(x => x.CompanyId == companyId && x.TwilioAccountId == connectTwilioViewModel.AccountSID && x.WhatsAppSender == connectTwilioViewModel.PhoneNumber).FirstOrDefault();
        //        if (existing == null)
        //        {
        //            existing = new WhatsAppConfig();
        //            _appDbContext.ConfigWhatsAppConfigs.Add(existing);
        //        }

        // existing.Name = connectTwilioViewModel.Name;
        //        existing.CompanyId = companyId;
        //        existing.TwilioAccountId = connectTwilioViewModel.AccountSID;
        //        existing.TwilioSecret = connectTwilioViewModel.AccountSecret;
        //        existing.WhatsAppSender = connectTwilioViewModel.PhoneNumber;
        //        existing.TriggerValue = connectTwilioViewModel.TriggerValue;
        //        existing.IsSubaccount = true;

        // TwilioClient.Init(existing.TwilioAccountId, existing.TwilioSecret);

        // try
        //        {
        //            var account = AccountResource.Fetch(pathSid: existing.TwilioAccountId);

        // if (account.Status == AccountResource.StatusEnum.Active)
        //            {
        //                await _appDbContext.SaveChangesAsync();

        // if (_appDbContext.CompanySandboxes.Where(x => x.CompanyId == companyId).Count() > 0)
        //                    BackgroundJob.Enqueue<ICompanyService>(x => x.DeleteSandbox(companyId));

        // var domainName = _configuration.GetValue<String>("Values:DomainName");

        // var callbackUrl = new Uri($"{domainName}/twilio/usage");
        //                string triggerValue = $"+{existing.TriggerValue.Value}";
        //                var trigger = TriggerResource.Create(callbackUrl,
        //                                                     triggerValue,
        //                                                     TriggerResource.UsageCategoryEnum.Totalprice,
        //                                                     existing.TwilioAccountId, Twilio.Http.HttpMethod.Post,
        //                                                     "Trigger if $20 credit left",
        //                                                     TriggerResource.RecurringEnum.Alltime,
        //                                                     TriggerResource.TriggerFieldEnum.Price);

        // existing.SID = trigger.Sid;
        //                await _appDbContext.SaveChangesAsync();

        // var companyName = await _appDbContext.CompanyCompanies.Where(x => x.Id == companyId).Select(x => x.CompanyName).FirstOrDefaultAsync();

        // try
        //                {
        //                    await _twilioService.AddNewTemplate(existing.TwilioAccountId, existing.TwilioSecret, new CreateTemplateViewModel
        //                    {
        //                        Name = "optin_1_",
        //                        Category = "ACCOUNT_UPDATE",
        //                        Languages = new List<LanguageElement> {
        //                        new LanguageElement { Language = "en", Content = $"*Notification*\nHello there! You received a new message from {companyName}.",
        //                            Components = new List<Component> { new Component { Type = "BUTTONS", Buttons = new List<Button> { new Button { Text = "Read more", Type = "QUICK_REPLY"}}}}},
        //                        new LanguageElement { Language = "zh_HK", Content = $"*新訊息通知*\n你好! 你收到來自{companyName}的新訊息。",
        //                            Components = new List<Component> { new Component { Type = "BUTTONS", Buttons = new List<Button> { new Button { Text = "查看更多", Type = "QUICK_REPLY" }}}}}
        //                    }
        //                    });
        //                    await _twilioService.AddNewTemplate(existing.TwilioAccountId, existing.TwilioSecret, new CreateTemplateViewModel
        //                    {
        //                        Name = "optin_2_",
        //                        Category = "ACCOUNT_UPDATE",
        //                        Languages = new List<LanguageElement> {
        //                        new LanguageElement { Language = "en", Content = $"Hello there! You received an update from {companyName}.",
        //                            Components = new List<Component> { new Component { Type = "BUTTONS", Buttons = new List<Button> { new Button { Text = "Read more", Type = "QUICK_REPLY"}}}}},
        //                        new LanguageElement { Language = "zh_HK", Content = $"你好! 以下為一則來自{companyName}的新訊息。",
        //                            Components = new List<Component> { new Component { Type = "BUTTONS", Buttons = new List<Button> { new Button { Text = "查看更多", Type = "QUICK_REPLY" }}}}}
        //                    }
        //                    });
        //                    await _twilioService.AddNewTemplate(existing.TwilioAccountId, existing.TwilioSecret, new CreateTemplateViewModel
        //                    {
        //                        Name = "optin_3",
        //                        Category = "ACCOUNT_UPDATE",
        //                        Languages = new List<LanguageElement> {
        //                        new LanguageElement { Language = "en", Content = $"*Notification*\nHello there! You received a new message from {existing.Name}.",
        //                            Components = new List<Component> { new Component { Type = "BUTTONS", Buttons = new List<Button> { new Button { Text = "Read more", Type = "QUICK_REPLY"}}}}},
        //                        new LanguageElement { Language = "zh_HK", Content = $"*新訊息通知*\n你好! 你收到來自{existing.Name}的新訊息。",
        //                            Components = new List<Component> { new Component { Type = "BUTTONS", Buttons = new List<Button> { new Button { Text = "查看更多", Type = "QUICK_REPLY" }}}}}
        //                    }
        //                    });
        //                    await _twilioService.AddNewTemplate(existing.TwilioAccountId, existing.TwilioSecret, new CreateTemplateViewModel
        //                    {
        //                        Name = "optin_4",
        //                        Category = "ACCOUNT_UPDATE",
        //                        Languages = new List<LanguageElement> {
        //                        new LanguageElement { Language = "en", Content = $"Hello there! You received an update from {existing.Name}.",
        //                            Components = new List<Component> { new Component { Type = "BUTTONS", Buttons = new List<Button> { new Button { Text = "Read more", Type = "QUICK_REPLY"}}}}},
        //                        new LanguageElement { Language = "zh_HK", Content = $"你好! 以下為一則來自{existing.Name}的新訊息。",
        //                            Components = new List<Component> { new Component { Type = "BUTTONS", Buttons = new List<Button> { new Button { Text = "查看更多", Type = "QUICK_REPLY" }}}}}
        //                    }
        //                    });
        //                    await _twilioService.AddNewTemplate(existing.TwilioAccountId, existing.TwilioSecret, new CreateTemplateViewModel
        //                    {
        //                        Name = "greetings_1",
        //                        Category = "ACCOUNT_UPDATE",
        //                        Languages = new List<LanguageElement> {
        //                        new LanguageElement { Language = "en", Content = "Hello {{1}}" },
        //                        new LanguageElement { Language = "zh_HK", Content = "你好 {{1}}" }
        //                    }
        //                    });
        //                }
        //                catch (Exception ex)
        //                {
        //                    _logger.LogError($"Add default template error: {ex.ToString()}");
        //                }

        // var response = _mapper.Map<WhatsAppConfigViewModel>(existing);
        //                return Ok(response);
        //            }
        //            return BadRequest(account);
        //        }
        //        catch (Exception ex)
        //        {
        //            return BadRequest(new ResponseViewModel { message = $"Failed to authenticate, ex: {ex.Message}" });
        //        }
        //    }
        //    return BadRequest(ModelState);
        // }

        // [HttpPost]
        // [Route("Core/MigrateChatAPI")]
        // public async Task<IActionResult> MigrateChatAPI()
        // {
        //    if (User.Identity.IsAuthenticated)
        //    {
        //        var domainName = _configuration.GetValue<String>("Values:DomainName");
        //        if (domainName != "https://sleekflow-prod-api.azurewebsites.net")
        //            ChatAPIInstance.WSInstances = new List<WSInstance>
        //                {
        //                    new WSInstance { id = "81852", apiUrl = "https://eu59.chat-api.com/instance81852/", token = "2yv6p805vpfwm7nv" },

        // new WSInstance { id = "136493", apiUrl = "https://eu136.chat-api.com/instance136493/", token = "azzvnb4jwqc6t50e" },
        //                    new WSInstance { id = "136494", apiUrl = "https://eu136.chat-api.com/instance136494/", token = "ph5y9ypdrx0o7ht5" },
        //                    new WSInstance { id = "136495", apiUrl = "https://eu136.chat-api.com/instance136495/", token = "wlpulgmvywdva9u7" },
        //                    //new WSInstance { id = "132313", apiUrl = "https://eu83.chat-api.com/instance132313/", token = "l0lftb7jowhea3me" },
        //                    //new WSInstance { id = "108576", apiUrl = "https://eu66.chat-api.com/instance108576/", token = "0ozkfw1xinmgpgwf" },
        //                    //new WSInstance { id = "132313", apiUrl = "https://eu83.chat-api.com/instance132313/", token = "l0lftb7jowhea3me" },
        //                };

        // foreach (var instance in ChatAPIInstance.WSInstances)
        //        {
        //            _appDbContext.CoreWhatsappChatAPIInstances.Add(new CoreWhatsappChatAPIInstance { InstanceId = instance.id, APIUrl = instance.apiUrl, Token = instance.token });
        //            await _appDbContext.SaveChangesAsync();
        //        }

        // return Ok();
        //    }
        //    return BadRequest();
        // }

        // [HttpPost]
        // [Route("Core/Fix")]
        // public async Task<IActionResult> Fix()
        // {
        //    if (User.Identity.IsAuthenticated)
        //    {
        //        //var duplicatedValue = _appDbContext.UserProfileCustomFields.AsEnumerable().GroupBy(x => new { x.UserProfileId, x.CompanyDefinedFieldId });
        //        //var duplcates = duplicatedValue.Where(x => x.Count() > 1).ToList();

        // var duplcates = _appDbContext.UserProfileCustomFields.AsEnumerable().GroupBy(x => new { x.UserProfileId, x.CompanyDefinedFieldId })
        //              .Where(g => g.Count() > 1)
        //              .Select(y => new { Element = y.Key, Counter = y.Count() })
        //              .ToList();

        // foreach (var duplicate in duplcates)
        //        {
        //            var tobeDelete = await _appDbContext.UserProfileCustomFields.Where(x => x.UserProfileId == duplicate.Element.UserProfileId && x.CompanyDefinedFieldId == duplicate.Element.CompanyDefinedFieldId).Skip(1).ToListAsync();
        //            _appDbContext.UserProfileCustomFields.RemoveRange(tobeDelete);
        //        }
        //        await _appDbContext.SaveChangesAsync();
        //        return Ok();
        //    }
        //    return BadRequest();
        // }

        // [HttpPost]
        // [Route("Core/FixCountry")]
        // public IActionResult FixCountry([FromQuery(Name = "companyId")] string companyId)
        // {
        //    BackgroundJob.Enqueue<CoreService>(x => x.AddCountry(companyId));
        //    return Ok();
        // }

        // [HttpPost]
        // [Route("Core/Fix")]
        // public async Task<IActionResult> Fix()
        // {
        //    if (User.Identity.IsAuthenticated)
        //    {
        //        var whatsappConfigs = await _appDbContext.ConfigWSChatAPIConfigs.Where(x => !x.IsDeleted).OrderBy(x => x.WSChatAPIInstance).ToListAsync();
        //        HttpClient client = new HttpClient();
        //        foreach (var whatsappConfig in whatsappConfigs)
        //        {
        //            try
        //            {
        //                var settings = new disableDialogsArchiveViewModel();
        //                settings.disableDialogsArchive = true;
        //                settings.ackNotificationsOn = true;
        //                settings.videoUploadOn = true;

        // var response = await client.PostAsJsonAsync($"{whatsappConfig.WSChatAPIURL}/settings?token={whatsappConfig.WSChatAPIKey}", settings);
        //                if (response.IsSuccessStatusCode)
        //                {
        //                    var result = await response.Content.ReadAsStringAsync();
        //                    _logger.LogError(result);
        //                }
        //                else
        //                    _logger.LogError(whatsappConfig.WSChatAPIInstance);
        //            }
        //            catch (Exception ex)
        //            {
        //                _logger.LogError(ex, ex.Message);
        //            }
        //        }

        // //var userProfiles = await _appDbContext.UserProfiles.Where(x => x.CompanyId == "471a6289-b9b7-43c3-b6ad-395a1992baea" && (x.LastContact == null || x.LastContactFromCustomers == null) && x.ActiveStatus == ActiveStatus.Active).ToListAsync();
        //        //foreach (var userProfile in userProfiles)
        //        //{
        //        //    var lastContact = _userProfileService.GetCustomFieldByFieldName(userProfile.Id, "LastContact");
        //        //    var lastCustomerContact = _userProfileService.GetCustomFieldByFieldName(userProfile.Id, "LastContactFromCustomers");

        // //    if (lastContact != null && !userProfile.LastContact.HasValue)
        //        //        userProfile.LastContact = Convert.ToDateTime(lastContact.Value).ToUniversalTime();

        // //    if (lastCustomerContact != null && !userProfile.LastContactFromCustomers.HasValue)
        //        //        userProfile.LastContactFromCustomers = Convert.ToDateTime(lastCustomerContact.Value).ToUniversalTime();

        // //    await _appDbContext.SaveChangesAsync();
        //        //}

        // //var duplcates = await _appDbContext.UserProfiles.Where(x => x.CompanyId == "c22c40a9-02a2-47cc-9145-25fdb756d6d3" && x.WhatsAppAccount.whatsAppId.Contains("@c.us")).ToListAsync();

        // //foreach (var userProfile in duplcates)
        //        //{
        //        //    await _userProfileService.SwitchWhatsappChannel(userProfile.Id, new ChangeChatAPIInstance { InstanceId = "AC1fc368e79a37f6a9e87657dd3d75a2e5" });
        //        //}
        //        return Ok();
        //    }
        //    return BadRequest();
        // }

        // [HttpPost]
        // [Route("Core/Facebook/Fix")]
        // public async Task<IActionResult> FixFacebook()
        // {
        //     HttpClient Client = new HttpClient();
        //
        //     var facebookConfigs = await _appDbContext.ConfigFacebookConfigs.Where(x => x.SubscribedFields.Contains("messages")).ToListAsync();
        //     foreach (var facebookConfig in facebookConfigs)
        //     {
        //         try
        //         {
        //             try
        //             {
        //                 await Client.PostAsync($"https://graph.facebook.com/{facebookConfig.PageId}/subscribed_apps?" +
        //                                                         $"access_token={facebookConfig.PageAccessToken}" +
        //                                                         $"&subscribed_fields={facebookConfig.SubscribedFields},message_echoes,standby", null);
        //             }
        //             catch (Exception ex)
        //             {
        //                 _logger.LogError($"Unsubsribed Error: {ex.ToString()}");
        //             }
        //         }
        //         catch (Exception ex)
        //         {
        //             _logger.LogError($"Unsubsribed Error: {ex.ToString()}");
        //         }
        //     }
        //
        //     return Ok();
        // }

        [HttpPost]
        [AllowAnonymous]
        [Route("Core/ImportSubscriptionPlan")]
        public async Task<IActionResult> ImportStaff([FromForm] ImportSpreadsheetViewModel importSpreadsheetViewModel)
        {
            ImportSpreadsheet importSpreadsheet = SerializeCSV(importSpreadsheetViewModel);

            var stripeClient = _stripeClients.GetStripeClient();

            foreach (var record in importSpreadsheet.records)
            {
                try
                {
                    var importObject = new ImportUserProfileObject(importSpreadsheet.headers, record.fields);

                    var description = importObject.GetValueFromList("Description");
                    var planId = importObject.GetValueFromList("PlanId");
                    var amount = importObject.GetValueFromList("Amount");
                    var currency = importObject.GetValueFromList("Currency");
                    var product = importObject.GetValueFromList("Product");
                    var productId = importObject.GetValueFromList("ProductId");
                    var agentPlanId = importObject.GetValueFromList("AgentPlanId");
                    var agentPlanAmount = importObject.GetValueFromList("AgentPlanAmount");
                    var numberOfContacts = importObject.GetValueFromList("NumberOfContacts");
                    var numberOfChannels = importObject.GetValueFromList("NumberOfChannels");
                    var countryTier = importObject.GetValueFromList("CountryTier");
                    var includedAgents = importObject.GetValueFromList("IncludedAgents");
                    var maximumAgentsLimit = importObject.GetValueFromList("MaximumAgentsLimit");
                    var strIncludedApiCallLimit = importObject.GetValueFromList("IncludedApiCallLimit");

                    if (double.Parse(amount) == 0)
                    {
                        continue;
                    }

                    int agentPlanAmountInt = 0;
                    int.TryParse(agentPlanAmount, out agentPlanAmountInt);
                    var newPlan = await _appDbContext.CoreSubscriptionPlans.Where(x => x.Id == planId)
                        .FirstOrDefaultAsync();
                    if (newPlan == null)
                    {
                        newPlan = new SubscriptionPlan
                        {
                            Id = planId,
                            StripePlanId = planId,
                            SubscriptionName = description,
                            Description = description,
                            Amount = double.Parse(amount),
                            ExtraChatAgentPlan = agentPlanId,
                            ExtraChatAgentPrice = agentPlanAmountInt,
                            Currency = currency
                        };
                        _appDbContext.CoreSubscriptionPlans.Add(newPlan);
                    }

                    var productService = new ProductService(stripeClient);
                    try
                    {
                        var existingProduct = productService.Get(productId);
                    }
                    catch
                    {
                        // Create
                        var createProductOptions = new ProductCreateOptions
                        {
                            Name = product, Id = productId
                        };

                        var newProduct = productService.Create(createProductOptions);

                        _logger.LogInformation(
                            "Stripe new product created, product id: {NewProductId}",
                            newProduct.Id);
                    }

                    var stripePricePlanId = string.Empty;
                    var interval = planId.Contains("yearly")
                        ? "year"
                        : (planId.Contains("oneoff") ? "oneoff" : "month");
                    var priceService = new PriceService(stripeClient);
                    try
                    {
                        _logger.LogInformation("Retrieving Stripe Price with LookupKey: {LookupKey}", newPlan.StripePlanId);

                        var priceListOptions = new PriceListOptions
                        {
                            LookupKeys = new List<string>
                            {
                                newPlan.StripePlanId
                            }
                        };

                        var prices = await priceService.ListAsync(priceListOptions);
                        stripePricePlanId = prices.Single().Id;
                    }
                    catch
                    {
                        var priceCreateOptions = new PriceCreateOptions
                        {
                            Nickname = description,
                            UnitAmount = long.Parse(amount) * 100,
                            Currency = currency,
                            LookupKey = planId,
                            Product = productId,
                        };

                        if (interval != "oneoff")
                        {
                            priceCreateOptions.Recurring = new PriceRecurringOptions
                            {
                                Interval = interval
                            };
                        }

                        var price = priceService.Create(priceCreateOptions);

                        _logger.LogInformation(
                            "New price created for Stripe plan {PlanId} and product {ProductId}, new price id: {PriceId}",
                            priceCreateOptions.LookupKey,
                            priceCreateOptions.Product,
                            price.Id);

                        stripePricePlanId = price.Id;
                    }

                    newPlan.Id = planId;
                    newPlan.SubscriptionName = description;
                    newPlan.StripePlanId = stripePricePlanId;
                    newPlan.Description = description;
                    newPlan.Amount = double.Parse(amount);
                    newPlan.ExtraChatAgentPlan = agentPlanId;
                    newPlan.ExtraChatAgentPrice = agentPlanAmountInt;
                    newPlan.Currency = currency;
                    newPlan.Version = SubscriptionPlanVersions.Version10;
                    newPlan.CountryTier = countryTier;

                    var maximumContact = 0;
                    var maximumNumberOfChannels = 0;
                    int.TryParse(numberOfContacts, out maximumContact);
                    int.TryParse(numberOfChannels, out maximumNumberOfChannels);
                    newPlan.MaximumContact = maximumContact;
                    newPlan.MaximumNumberOfChannel = maximumNumberOfChannels;
                    newPlan.MaximumAgentsLimit = string.IsNullOrWhiteSpace(maximumAgentsLimit) ? null : int.Parse(maximumAgentsLimit);

                    newPlan.AvailableFunctions = new List<SubscriptionSpecificFunction>();

                    if (newPlan.Id.Contains("agent"))
                    {
                        newPlan.SubscriptionTier = SubscriptionTier.Agent;
                    }
                    else if (newPlan.Id.ContainsIgnoreCase("additional") && newPlan.Id.ContainsIgnoreCase("contacts"))
                    {
                        newPlan.SubscriptionTier = SubscriptionTier.AddOn;
                    }
                    else if (newPlan.Id.ContainsIgnoreCase("onboarding_support"))
                    {
                        newPlan.SubscriptionTier = SubscriptionTier.AddOn;
                    }
                    else if (newPlan.Id.ContainsIgnoreCase("business_consultancy_service"))
                    {
                        newPlan.SubscriptionTier = SubscriptionTier.AddOn;
                    }
                    else if (newPlan.Id.ContainsIgnoreCase("whatsapp_phone_number"))
                    {
                        newPlan.SubscriptionTier = SubscriptionTier.AddOn;
                    }
                    else if (newPlan.Id.ContainsIgnoreCase("flow_builder_flow_enrolments"))
                    {
                        newPlan.SubscriptionTier = SubscriptionTier.AddOn;
                    }
                    else if (newPlan.Id.Contains("premium") && interval == "year")
                    {
                        newPlan.IncludedAgents = string.IsNullOrEmpty(includedAgents) ? 5 : int.Parse(includedAgents);
                        newPlan.MaximumAPICall = int.TryParse(strIncludedApiCallLimit, out int includedApiCallLimit) ? includedApiCallLimit : 1200000;
                        newPlan.MaximumAPICall = 1200000;
                        newPlan.MaximumAutomation = 999;
                        newPlan.MaximumCampaignSent = 50000000;
                        newPlan.MaximumMessageSent = 50000000;
                        newPlan.SubscriptionTier = SubscriptionTier.Premium;

                        newPlan.AvailableFunctions.Add(SubscriptionSpecificFunction.AdvancedUserTeamSettings);
                        newPlan.AvailableFunctions.Add(SubscriptionSpecificFunction.AnalyticsDashboard);
                        newPlan.AvailableFunctions.Add(SubscriptionSpecificFunction.PrioritySupport);
                        newPlan.AvailableFunctions.Add(SubscriptionSpecificFunction.ChatSupport);
                        newPlan.AvailableFunctions.Add(SubscriptionSpecificFunction.OnboardingSupport);
                    }
                    else if (newPlan.Id.Contains("premium"))
                    {
                        newPlan.IncludedAgents = string.IsNullOrEmpty(includedAgents) ? 5 : int.Parse(includedAgents);
                        newPlan.MaximumAPICall = int.TryParse(strIncludedApiCallLimit, out int includedApiCallLimit) ? includedApiCallLimit : 100000;
                        newPlan.MaximumAutomation = 999;
                        newPlan.MaximumCampaignSent = 50000000;
                        newPlan.MaximumMessageSent = 50000000;
                        newPlan.SubscriptionTier = SubscriptionTier.Premium;

                        newPlan.AvailableFunctions.Add(SubscriptionSpecificFunction.AdvancedUserTeamSettings);
                        newPlan.AvailableFunctions.Add(SubscriptionSpecificFunction.AnalyticsDashboard);
                        newPlan.AvailableFunctions.Add(SubscriptionSpecificFunction.PrioritySupport);
                        newPlan.AvailableFunctions.Add(SubscriptionSpecificFunction.ChatSupport);
                    }
                    else if (newPlan.Id.Contains("pro") && interval == "year")
                    {
                        newPlan.IncludedAgents = string.IsNullOrEmpty(includedAgents) ? 3 : int.Parse(includedAgents);
                        newPlan.MaximumAPICall = int.TryParse(strIncludedApiCallLimit, out int includedApiCallLimit) ? includedApiCallLimit : 0;
                        newPlan.MaximumAutomation = 10;
                        newPlan.MaximumCampaignSent = 60000;
                        newPlan.MaximumMessageSent = 60000;
                        newPlan.SubscriptionTier = SubscriptionTier.Pro;

                        newPlan.AvailableFunctions.Add(SubscriptionSpecificFunction.PrioritySupport);
                        newPlan.AvailableFunctions.Add(SubscriptionSpecificFunction.ChatSupport);
                        newPlan.AvailableFunctions.Add(SubscriptionSpecificFunction.OnboardingSupport);
                    }
                    else if (newPlan.Id.Contains("pro"))
                    {
                        newPlan.IncludedAgents = string.IsNullOrEmpty(includedAgents) ? 3 : int.Parse(includedAgents);
                        newPlan.MaximumAPICall = int.TryParse(strIncludedApiCallLimit, out int includedApiCallLimit) ? includedApiCallLimit : 0;
                        newPlan.MaximumAutomation = 10;
                        newPlan.MaximumCampaignSent = 5000;
                        newPlan.MaximumMessageSent = 5000;
                        newPlan.SubscriptionTier = SubscriptionTier.Pro;
                    }
                    else
                    {
                        newPlan.SubscriptionTier = SubscriptionTier.AddOn;
                    }

                    await _appDbContext.SaveChangesAsync();
                }
                catch (Exception ex)
                {
                    _logger.LogError(
                        ex,
                        "Error saving new subscription plan. {ExceptionMessage}",
                        ex.Message);
                }
            }

            return Ok();
        }

        // [HttpPost]
        // [Route("Core/Import/Staff/{companyId}")]
        // public async Task<IActionResult> ImportStaff(string companyId, [FromForm] ImportSpreadsheetViewModel importSpreadsheetViewModel)
        // {
        //    var senderId = _userManager.GetUserId(User);
        //    var company = _appDbContext.CompanyCompanies.Where(x => x.Id == companyId).FirstOrDefault();

        // ImportSpreadsheet importSpreadsheet = SerializeCSV(company, importSpreadsheetViewModel);

        // foreach (var record in importSpreadsheet.records)
        //    {
        //        try
        //        {
        //            var importObject = new ImportUserProfileObject(importSpreadsheet.headers, record.fields);

        // var username = importObject.GetValueFromList("Username");
        //            var firstName = importObject.GetValueFromList("First Name");
        //            var lastName = importObject.GetValueFromList("Last Name");
        //            var email = importObject.GetValueFromList("Email");
        //            var password = importObject.GetValueFromList("Password");
        //            var rolestr = importObject.GetValueFromList("Role");
        //            var role = (StaffUserRole)Enum.Parse(typeof(StaffUserRole), rolestr.Replace(" ", ""), true);

        // var position = importObject.GetValueFromList("Position");
        //            var groupToAdd = importObject.GetValueFromList("Group");
        //            if (string.IsNullOrEmpty(groupToAdd))
        //                groupToAdd = importObject.GetValueFromList("Team");

        // if (string.IsNullOrEmpty(username))
        //                username = email;

        // var identityResult = await _userManager.CreateAsync(new ApplicationUser { UserName = username, Email = email, FirstName = firstName, LastName = lastName, DisplayName = $"{firstName} {lastName}", EmailConfirmed = true });
        //            if (identityResult.Succeeded)
        //            {
        //                var user = await _userManager.FindByNameAsync(username);

        // await _userManager.AddPasswordAsync(user, password);

        // var staff = new Staff { CompanyId = company.Id, IdentityId = user.Id, Identity = user, Locale = "en", RoleType = role, Position = position, TimeZoneInfoId = company.TimeZoneInfoId, NotificationSettingId = 1 };
        //                await _appDbContext.UserRoleStaffs.AddAsync(staff);

        // if (!string.IsNullOrEmpty(groupToAdd))
        //                {
        //                    var groups = groupToAdd.Split(";", StringSplitOptions.RemoveEmptyEntries);
        //                    foreach (var group in groups)
        //                    {
        //                        var team = await _appDbContext.CompanyStaffTeams.Where(x => x.CompanyId == companyId && x.TeamName == group.Trim()).Include(x => x.Members).FirstOrDefaultAsync();
        //                        if (team == null)
        //                        {
        //                            team = new CompanyTeam { CompanyId = companyId, TeamName = group.Trim() };
        //                            _appDbContext.CompanyStaffTeams.Add(team);
        //                        }
        //                        team.Members.Add(new TeamMember { Staff = staff });
        //                    }
        //                }

        // await _appDbContext.SaveChangesAsync();
        //            }
        //            else if (!string.IsNullOrEmpty(groupToAdd))
        //            {
        //                var user = await _userManager.FindByNameAsync(username);
        //                var token = await _userManager.GeneratePasswordResetTokenAsync(user);
        //                await _userManager.ResetPasswordAsync(user, token, password);

        // var staff = await _appDbContext.UserRoleStaffs.Where(x => x.CompanyId == companyId && x.IdentityId == user.Id).FirstOrDefaultAsync();
        //                if (staff == null)
        //                {
        //                    staff = new Staff { CompanyId = company.Id, IdentityId = user.Id, Identity = user, Locale = "en", RoleType = role, Position = position, TimeZoneInfoId = company.TimeZoneInfoId, NotificationSettingId = 1 };
        //                    await _appDbContext.UserRoleStaffs.AddAsync(staff);
        //                    await _appDbContext.SaveChangesAsync();
        //                }
        //                else
        //                {
        //                    if (staff.RoleType != role)
        //                    {
        //                        staff.RoleType = role;
        //                        await _appDbContext.SaveChangesAsync();
        //                    }
        //                }

        // if (!string.IsNullOrEmpty(groupToAdd))
        //                {
        //                    var groups = groupToAdd.Split(";", StringSplitOptions.RemoveEmptyEntries);
        //                    foreach (var group in groups)
        //                    {
        //                        var team = await _appDbContext.CompanyStaffTeams.Where(x => x.CompanyId == companyId && x.TeamName == group.Trim()).Include(x => x.Members).FirstOrDefaultAsync();
        //                        if (team == null)
        //                        {
        //                            team = new CompanyTeam { CompanyId = companyId, TeamName = group.Trim() };
        //                            _appDbContext.CompanyStaffTeams.Add(team);
        //                        }
        //                        if (team.Members.Where(x => x.StaffId == staff.Id).Count() == 0)
        //                        {
        //                            team.Members.Add(new TeamMember { Staff = staff });
        //                            await _appDbContext.SaveChangesAsync();
        //                        }
        //                    }
        //                }
        //            }
        //            else
        //                continue;
        //        }
        //        catch (Exception ex)
        //        {
        //            _logger.LogError(ex, ex.Message);
        //        }
        //    }
        //    return Ok(importSpreadsheet);
        // }

        // [HttpPost]
        // [Route("Core/twilio/usage")]
        // public async Task<IActionResult> GetTwilioUsage([FromBody] TwilioUsageRequest twilioUsageRequest)
        // {
        //    var senderId = _userManager.GetUserId(User);

        // var config = await _appDbContext.ConfigWhatsAppConfigs.Where(x => x.TwilioAccountId == twilioUsageRequest.SubAccountSID).FirstOrDefaultAsync();
        //    TwilioClient.Init(config.TwilioAccountId, config.TwilioSecret);

        // var records = RecordResource.Read(category: RecordResource.CategoryEnum.Totalprice);

        // string results = "";
        //    foreach (var record in records)
        //    {
        //        if (record.Price > 0)
        //            _logger.LogError("here");
        //        results += $"{record.StartDate} - {record.EndDate} {record.Description} {record.Price} {record.PriceUnit}" + "\n";
        //    }

        // return Ok(results);
        // }

        // public class TwilioUsageRequest
        // {
        //    public string SubAccountSID { get; set; }
        // }

        public class ImportUserProfileObject
        {
            public List<ImportHeader> _headers { get; set; }

            public List<string> _fields { get; set; }

            public ImportUserProfileObject(List<ImportHeader> headers, List<string> fields)
            {
                _headers = headers;
                _fields = fields;
            }

            public ImportHeader GetHeaderFromList(string field)
            {
                try
                {
                    return _headers[_fields.FindIndex(x => x == field)];
                }
                catch (Exception)
                {
                    return null;
                }
            }

            public string GetValueFromList(string key)
            {
                try
                {
                    return _fields[GetIndexFromHeader(key).Value];
                }
                catch (Exception)
                {
                    return null;
                }
            }

            public int? GetIndexFromHeader(string key)
            {
                try
                {
                    return (_headers.FindIndex(x => x.HeaderName.ToLower() == key.ToLower()));
                }
                catch (Exception)
                {
                    return null;
                }
            }
        }

        private ImportSpreadsheet SerializeCSV(ImportSpreadsheetViewModel importSpreadsheetViewModel)
        {
            ImportSpreadsheet importSpreadsheet = new ImportSpreadsheet()
            {
                ImportName = importSpreadsheetViewModel.ImportName
            };

            foreach (IFormFile file in importSpreadsheetViewModel.files)
            {
                using (var reader = new StreamReader(file.OpenReadStream()))
                {
                    while (reader.Peek() >= 0)
                    {
                        if (importSpreadsheet.headers.Count == 0)
                        {
                            int columnNumber = 0;
                            var headers = reader.ReadLine().Split(",", StringSplitOptions.RemoveEmptyEntries).ToList();
                            foreach (var header in headers)
                            {
                                importSpreadsheet.headers.Add(
                                    new ImportHeader
                                    {
                                        HeaderName = header, IsValid = true, CsvFileColumnNumber = columnNumber
                                    });
                                columnNumber++;
                            }
                        }
                        else
                        {
                            var line = reader.ReadLine();
                            var records = line.Split(",").ToList();
                            var isNull = true;
                            foreach (var record in records)
                            {
                                if (!string.IsNullOrEmpty(record))
                                {
                                    isNull = false;
                                    break;
                                }
                            }

                            if (string.IsNullOrEmpty(line) || isNull)
                            {
                                break;
                            }

                            Microsoft.VisualBasic.FileIO.TextFieldParser parser =
                                new Microsoft.VisualBasic.FileIO.TextFieldParser(new StringReader(line));

                            // You can also read from a file
                            // TextFieldParser parser = new TextFieldParser("mycsvfile.csv");

                            parser.HasFieldsEnclosedInQuotes = true;
                            parser.SetDelimiters(",");

                            string[] fields;

                            while (!parser.EndOfData)
                            {
                                fields = parser.ReadFields();
                                importSpreadsheet.records.Add(
                                    new ImportRecord
                                    {
                                        fields = fields.ToList()
                                    });
                            }

                            parser.Close();
                        }
                    }
                }
            }

            return importSpreadsheet;
        }

        /*
        [HttpGet]
        [Route("Core/TimeSpan")]
        public async Task<IActionResult> GetTimeSpan()
        {
            if (User.Identity.IsAuthenticated)
            {
                dynamic dynamic = new JObject();
                dynamic.hour = TimeSpan.FromHours(1);
                dynamic.days = TimeSpan.FromDays(30);
                dynamic.minutes = TimeSpan.FromMinutes(30);
                dynamic.seconds = TimeSpan.FromSeconds(30);
                return Ok(dynamic);
            }
            return BadRequest();
        } */

        /*
        [HttpGet]
        [Route("Core/AddToListJason")]
        public async Task<IActionResult> AddToListJason()
        {
            if (User.Identity.IsAuthenticated)
            {
                //10841
                var failedMessages = await _appDbContext.ConversationMessages.Where(X => X.Status == MessageStatus.Failed && X.CompanyId == "156b38c5-a43a-4c0d-9789-b5512779bbcd").ToListAsync();
                var userProfilesIds = new List<string>();
                foreach (var failedMessage in failedMessages)
                {
                    var conversation = await _appDbContext.Conversations.Where(x => x.Id == failedMessage.ConversationId).FirstOrDefaultAsync();
                    if (!userProfilesIds.Contains(conversation.UserProfileId))
                        userProfilesIds.Add(conversation.UserProfileId);
                }

                await _userProfileService.AddToUserList("156b38c5-a43a-4c0d-9789-b5512779bbcd", 10841, new UserProfileIdsViewModel { UserProfileIds = userProfilesIds });

                return Ok(userProfilesIds);
            }
            return BadRequest();
        } */

        // [HttpGet]
        // [Route("Core/fixSnooze")]
        // public async Task<IActionResult> AddToListJason()
        // {
        //    var conversations = await _appDbContext.Conversations.Where(x => x.Status == "pending").Include(x => x.Assignee).ToListAsync();
        //    foreach (var conversaion in conversations)
        //    {
        //        TimeSpan ts = conversaion.SnoozeUntil.Value - DateTime.UtcNow;
        //        if (ts.TotalSeconds > 0)
        //            BackgroundJob.Schedule<IConversationMessageService>(x => x.ChangeConversationStatus(conversaion.Id, conversaion.Assignee.IdentityId, new StatusViewModel { Status = "open" }, true), ts);
        //    }
        //    return Ok();
        // }

        // public static string RemoveSpecialCharacters(string str)
        // {
        //    return Regex.Replace(str, "[^a-zA-Z0-9_.]+", "", RegexOptions.Compiled);
        // }

        // [HttpPost]
        // [Route("Core/SendEmail")]
        // public async Task<IActionResult> SendMessage([FromBody] EmailSender)
        // {

        // }

        // public class SendEmail
        // {
        //    public string from { get; set; }
        //    public string to { get; set; }
        //    public string message { get; set; }
        // }
    }
}