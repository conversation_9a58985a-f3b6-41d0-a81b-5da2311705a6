config:
  pulumi:template: azure-csharp
  sleekflow:name: production
  sleekflow:cors:
    domains:
      - https://app.sleekflow.io
      - https://beta.sleekflow.io
      - https://cn.sleekflow.io
      - https://chat.sleekflow.io
      - https://next.sleekflow.io
      - https://v1.sleekflow.io
      - https://sleekflow.stoplight.io
      - https://apidoc.sleekflow.io
  sleekflow:default_sleekflow_core_domain: api.sleekflow.io
  sleekflow:default_sleekflow_core_front_door_domain: https://sleekflow-core-production-hac3h0azhvcub0aq.z01.azurefd.net
  sleekflow:regional_configs:
    - location_name: eastasia
      sku_config:
        sleekflow_core:
          name: P3V3
          tier: PremiumV3
        sleekflow_core_db:
          name: HS_S_Gen5
          tier: Hyperscale
          family: Gen5
          capacity: 2
        sleekflow_powerflow:
          name: P1V3
          tier: PremiumV3
        sleekflow_sleek_pay:
          name: P0V3
          tier: PremiumV3
        sleekflow_core_worker:
          name: P2V3
          tier: PremiumV3
        redis:
          default:
            name: Standard
            family: C
            capacity: 1
          caching:
            name: Premium
            family: P
            capacity: 3
      sql_db_config:
        administrator_login_random_secret: 5F83ECEC-A80B-4DBE-A568-A8EACA7671F6
        administrator_login_password_random_secret: 4C4FD6D4-92CB-439E-AA2E-7F1D0BACF15A
        whitelist_ip_ranges:
          - start_ip_address: **************
            end_ip_address: **************
          - start_ip_address: **************
            end_ip_address: **************
          - start_ip_address: *************
            end_ip_address: *************
          - start_ip_address: *************
            end_ip_address: *************
          - start_ip_address: **************
            end_ip_address: **************
        is_read_scale_enable: "false"
        high_availability_replica_count: 0
      vnet:
        default_address_space: ********/16
        default_subnet_address_prefix: ********/24
        sleekflow_core_db_address_prefix: ********/24
        sleekflow_core_address_prefix: ********/24
        sleekflow_powerflow_address_prefix: ********/24
        sleekflow_sleek_pay_address_prefix: ********/24
        sleekflow_core_worker_address_prefix: ********/24
      auto_scale_config:
        sleekflow_core:
          capacity:
            default: "6"
            maximum: "20"
            minimum: "6"
          scale_out_instances: "1"
          scale_down_instances: "1"
        core_worker:
          capacity:
            default: "5"
            maximum: "20"
            minimum: "5"
          scale_out_instances: "1"
          scale_down_instances: "1"
      sleekflow_core_config:
        aspnetcore_environment: production
        logger:
          gcp_is_enabled: "TRUE"
          gcp_project_id: my-production-project-405815
          gcp_credential_json: ************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
        audit_hub:
          endpoint: https://sleekflow-a6aeg3bth3f5fneq.z01.azurefd.net/v1/audit-hub
          key: 3Ri7RqtKBR1sXHCGXLoGiasTBVqE23pzPXUQVnKPw3v5BX4zr2SM9HcXFGlUkWMq
        auth0:
          action_audience: https://api.sleekflow.io/
          action_issuer: https://sso.sleekflow.io/
          audience: https://api.sleekflow.io
          client_id: txLGs6X2eN17XOXuxZpDakoxpyxoOlHW
          client_secret: FNsmpbetisCz7xg9aALvE3v1WXdZzxhoY6-fDf4RDSbroZ_zHIYsxb31oUs20GfO
          database_connection_name: Sleekflow-Username-Password-Authentication
          domain: sleekflow.eu.auth0.com
          http_retries: 10
          issuers:
            - https://sso.sleekflow.io/
            - https://sleekflow.eu.auth0.com
          namespace: https://app.sleekflow.io/
          role_claim_type: roles
          user_email_claim_type: email
          user_id_claim_type: user_id
          username_claim_type: user_name
          tenant_hub_secret_key: 6a9_nBt?)R#@_he=v2Eo!K3B3Ae0ao`x*I`m}ZSX*S~hsQ7bQD]k#gh8r38ad,9o
          health_check:
            is_enabled: "false"
            client_id: OrxDhNRbAyWXKoSVoMuS0hIWNo3v2An0
            client_secret: NXaeck_K8gPII9HngFq3RsWKJ4QIaKq5bZM2YmkNxnq07lDEzS05Rei6l9lK14Oj
            username: *******
            password: ********************************
        azure:
          media_service:
            account_name: sfmediaproduction
            client_id: 048e63db-bf3d-442e-bf1e-d30b2fd4b555
            client_secret: ****************************************
            resource_group: sleekflow-resource-group-production853b96c8
            subscription_id: c19c9b56-93e9-4d4c-bc81-838bd3f72ad6
            tenant_id: d66fa1cc-347d-42e9-9444-19c5fd0bbcce
          text_analytics_credentials: 226f2d8217704dd6b223b4a1fd6e51c6
          text_analytics_url: https://sleekflow-prod-sp-text-analytics.cognitiveservices.azure.com/
        application_insights:
          is_telemetry_tracer_enabled: "true"
          is_sampling_disabled: "false"
        chat_api:
          api_key: qVH3fqZufwPZaPlAmsIJMwcxuHI2
          api_url: https://us-central1-app-chat-api-com.cloudfunctions.net
        commerce_hub:
          endpoint: https://sleekflow-a6aeg3bth3f5fneq.z01.azurefd.net/v1/commerce-hub
          key: w7zJUyirdVnNom4M6YTyxgtbTQDQL2gxHPRbKy9sSXaraX6GC4Ru8I1ejjpFS0LnSnJrRxAtO9YxxrRMGqj2H5EycUywMgQc0RhKWmQvmAe93j5jMJqunIG8aYilMp8Q
        crm_hub:
          endpoint: https://sleekflow-a6aeg3bth3f5fneq.z01.azurefd.net/v1/crm-hub
          key: a12da7c775d00cada5b1ee611d3f6dca
        data_snapshot:
          is_enable: "true"
        development:
          redirect_fb_webhook: https://9633-42-200-140-118.ngrok.io/facebook/Webhook
        environment_features:
          is_recurring_job_enabled: "true"
        epplus:
          excel_package:
            license_context: NonCommercial
        facebook:
          client_id: ***************
          client_secret: ********************************
        ffmpeg:
          ffmpeg_exe_name: /usr/bin/ffmpeg
        flow_hub:
          endpoint: https://sleekflow-a6aeg3bth3f5fneq.z01.azurefd.net/v1/flow-hub
          key: BWijifKMPGviFQpYEkZuLjHOTpySNVawLYhiFKHEcmJyUDQPYlKuYUmJqgXVGGHP
        geo_sql_db:
          name: sleekflow-crm-prod
        geo_storage:
          connection_string: DefaultEndpointsProtocol=https;AccountName=sleekflowstorage;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net
        hub_spot:
          internal_hub_spot_api_key: ********************************************
          is_enable: "true"
        instrumentation_engine_extension_version: disabled
        internal_google_cloud:
***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
        general_google_cloud:
********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
          project_id: my-production-project-405815
          server_location: asia-east1
          google_storage_bucket_name: sleekflow-transcoder-prod-asia
        ip_look_up:
          key: 8fc198c42a094d588657c6f05c707518
        intelligent_hub:
          endpoint: https://sleekflow-a6aeg3bth3f5fneq.z01.azurefd.net/v1/intelligent-hub
          key: BWijifKMPGviFQpYEkZuLjHOTpySNVawLYhiFKHEcmJyUDQPYlKuYUmJqgXVGGHP
        webhook_hub:
          endpoint: https://sleekflow-a6aeg3bth3f5fneq.z01.azurefd.net/v1/webhook-hub
          key: BWijifKMPGviFQpYEkZuLjHOTpySNVawLYhiFKHEcmJyUDQPYlKuYUmJqgXVGGHP
          auth_secret_key: O19dnDlZhSZkZ5TigIXiolCTqSl461kyBCotXGOJGUMA6eiHfyxRQVwQTFN5qMr1
        message_hub:
          endpoint: https://sleekflow-a6aeg3bth3f5fneq.z01.azurefd.net/v1/messaging-hub
          key: a12da7c775d00cada5b1ee611d3f6dca
        mixpanel:
          token: ef72dd13a0ffccb584cfdf75d3160e25
        mobile_app_management_extension_version: latest
        notification_hub:
          connection_string: Endpoint=sb://sleekflowproduction.servicebus.windows.net/;SharedAccessKeyName=DefaultFullSharedAccessSignature;SharedAccessKey=pbzIBQQoNB8rGgrHdt1vmCIuTIB/Aaey5iION3eiCbQ=
          hub_name: SleekflowProduction
        public_api_gateway:
          endpoint: https://sleekflow-a6aeg3bth3f5fneq.z01.azurefd.net/v1/public-api-gateway
          key: a12da7c775d00cada5b1ee611d3f6dca
        redis:
          connection_string: sleekflow-sp.redis.cache.windows.net:6380,password=rzUiDMeStpK4kX1eA9qRUXFVqFGK8A9Yapa02fzR1i4=,ssl=True,abortConnect=False
          prefix_group_num: 10
        reseller:
          domain_name: https://partner.sleekflow.io
        rewardful:
          api_secret: ********************************
        salesforce:
          custom_active_web_app: https://sleekflow-sfmc-jb-ca-app.nicemeadow-64d75f44.eastasia.azurecontainerapps.io
        share_hub:
          endpoint: https://sleekflow-a6aeg3bth3f5fneq.z01.azurefd.net/v1/share-hub
          key: a12da7c775d00cada5b1ee611d3f6dca
        shopify:
          shopify_api_key: b1781c9e361cfc851ee8a857fd046cca
          shopify_secret_key: 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
        sleek_pay:
          domain_name: https://pay-eas.sleekflow.io
          shopify_graphql_admin_api_version: 2022-10
          stripe:
            public_keys:
              default: pk_live_51Klpk8I2ilBBY57ZWqA8WkA92DuaoRtBs8DAWWLGBfW0d8QJPQ9lclAVI1fyn5Yz6kiqqWP6vRQkMhcxtfW0Y4IY00auBhCQhm
              gb: pk_live_51LBDmrD9JBREupA8JMPwf4ubmrols7gf1znOkUEMs5zgwtpWii8mHGai7aPeBAbwdaW686fgcBDqYNDdhxEeV99Y00azIhNXet
              hk: pk_live_51Klpk8I2ilBBY57ZWqA8WkA92DuaoRtBs8DAWWLGBfW0d8QJPQ9lclAVI1fyn5Yz6kiqqWP6vRQkMhcxtfW0Y4IY00auBhCQhm
              my: pk_live_51LBDj8GBiw7eM4IGRMGeHyy6IxH4zSYIF6HojR3UU804u6U7GbN7Gi75quqzTuvfwSRqdShrAueFmWJmxtlZRmIP00oJX2bagF
              sg: pk_live_51L8iI0HRmviISgVcyuZQ49yoIRkGUqNGpi0nCZClx1BWNE89CFOjunhoSZzAZsFuRhRf0kOz69GVZEVFHUO9Uzke00J8MJnDJL
            secret_keys:
              default: ***********************************************************************************************************
              gb: ***********************************************************************************************************
              hk: ***********************************************************************************************************
              my: ***********************************************************************************************************
              sg: ***********************************************************************************************************
            connect_webhook_secrets:
              gb: whsec_ezIPHvqS1nM8I5n47gqNOxFpuf5aRkWt
              hk: whsec_zKwhjxpEoPvj9bWr5PEd9yVUc72YULGZ
              my: whsec_vZH7rx0MreePLeSACnQAZmJlPhBViDpA
              sg: whsec_nT96HTze4ZF1k1qDQxrDClG7651fWd9m
            report_webhook_secrets:
              gb: whsec_A4SYa7riH8m8laXqhWHDKcPqLtyWvSzt
              hk: whsec_l0BecEe7oxvoDeQfnp4eyYU2aEHZ9Slc
              my: whsec_eIEWkMKQNwx4XmbUm59ZfIZToubia745
              sg: whsec_a7t4jCyqlIxRJyz3JFgvQtfjiWKzNzIv
            webhook_secrets:
              default: whsec_M1t14iBlrwX5GlUBNlgo8HIZSpg2MJfH
              gb: whsec_18yy26DPPOMNptQuBAtYYGRtVGy5z4Vq
              hk: whsec_M1t14iBlrwX5GlUBNlgo8HIZSpg2MJfH
              my: whsec_LcRi77TarvpZs69MVuoWZ8fF425Cepid
              sg: whsec_alsKPYFjDCK8lEKeP7QPXJAAf6ZjeDs7
        snapshot_debugger_extension_version: disabled
        sql_performance:
          from_raw_sql: "true"
          is_and_condition_enabled: "true"
          is_or_condition_enabled: "true"
          is_conversation_analytics_condition_enabled: "false"
          is_shopify_order_statistics_enabled: "false"
          is_sales_performance_enabled: "false"
        stripe:
          stripe_public_key: pk_live_J0gdFoKSpThotTX5jHLKf5OJ00AMt1g3j8
          stripe_report_key: ***********************************************************************************************************
          stripe_secret_key: ******************************************
          stripe_webhook_secret: whsec_p9bgQl6uYXmlgRQf8vYkdaFuh8WiJIlK
        stripe_payment:
          stripe_payment_secret_key_gb: ***********************************************************************************************************
          stripe_payment_secret_key_hk: ***********************************************************************************************************
          stripe_payment_secret_key_my: ***********************************************************************************************************
          stripe_payment_secret_key_sg: ***********************************************************************************************************
        stripe_report:
          stripe_report_webhook_secret_gb: whsec_3W17qqv4KOaD3MrUYydhh0S69Kfq8e0s
          stripe_report_webhook_secret_hk: whsec_p9bgQl6uYXmlgRQf8vYkdaFuh8WiJIlK
          stripe_report_webhook_secret_my: whsec_x0HOEz7Wx3OSvddWSFAIKsIDJYRdiPrC
          stripe_report_webhook_secret_sg: whsec_X7m1uFBMi0soo65ZAMfBHfGIQaCO582s
        tenant_hub:
          endpoint: https://sleekflow-a6aeg3bth3f5fneq.z01.azurefd.net/v1/tenant-hub
          key: BWijifKMPGviFQpYEkZuLjHOTpySNVawLYhiFKHEcmJyUDQPYlKuYUmJqgXVGGHP
          is_enable_tenant_logic: "true"
        ticketing_hub:
          endpoint: https://sleekflow-a6aeg3bth3f5fneq.z01.azurefd.net/v1/ticketing-hub
          key: wLYhiFKHEcmJyUDQPYlKuYUmJqgXVGGHPBWijifKMPGviFQpYEkZuLjHOTpySNVa
          is_enable_ticketing_logic: "false"
        test_swaping: 1
        token:
          audience: https://sleekflow-prod-api.azurewebsites.net
        tokens:
          audience: https://travis-crm-api-hk.azurewebsites.net
          issuer: https://sleekflow-prod-api.azurewebsites.net
          key: 9C91A5D4BF0A3D803FE4A07550C1A5D9D55BEFE1F1226C2C5D22F29D9CED8036
          lifetime: 60
        user_event_hub:
          endpoint: https://sleekflow-a6aeg3bth3f5fneq.z01.azurefd.net/v1/user-event-hub
          key: wLYhiFKHEcmJyUDQPYlKuYUmJqgXVGGHPBWijifKMPGviFQpYEkZuLjHOTpySNVa
        values:
          app_domain_name: https://app.sleekflow.io
          app_domain_name_v1: https://v1.sleekflow.io
          share_link_function: https://share.sleekflow.io
          sleekflow_api_gateway: https://sleekflow-a6aeg3bth3f5fneq.z01.azurefd.net
          sleekflow_company_id: 471a6289-b9b7-43c3-b6ad-395a1992baea
        website_http_logging_retention_days: 5
        website_node_default_version: 6.9.1
        whatsapp_cloud_api_template:
          default_image_blob_id: sleekflow.png
        xdt_microsoft_application_insights_base_extensions: disabled
        xdt_microsoft_application_insight_mode: default
        global_pricing:
          is_feature_enabled: "true"
          plan_migration_incentives_start_date: "2024-11-20"
          plan_migration_incentives_end_date: "2025-02-20"
        feature_flags:
          - feature_name: FlowBuilderMonetisation
            is_enabled: "true"
          - feature_name: CancelledSubscriptionTermination
            is_enabled: "true"
        contact_safe_deletion:
          is_feature_enabled: "true"
        partner_stack:
          public_key: pk_bvEwuCDvoz9Qh11y48006zWXdCgsvInk
          secret_key: sk_KnZllhAJOasP09CPsMOOoiUqKAO81nMy
        facebook_lead_ads_disconnected_notification:
          from_phone_number: "17209612030"
          template_name: "integration_disconnect_noti"
          reconnect_url: "https://app.sleekflow.io/en/integrations/facebook-lead-ad"
          endpoint: "https://api.sleekflow.io/api/notifications/integration-disconnected"
          api_key: "iVgMmTo7nYH2tfVqMjqWlzfFjapJyNqOicfHXaX13A"
          host_company_id: "8d9eaafa-ab5b-47d3-8e0a-3920ac7c05cd"
        hangfire_worker:
          worker_count: 5
        internal_integration_hub:
          endpoint: "https://sleekflow-a6aeg3bth3f5fneq.z01.azurefd.net/v1/internal-integration-hub"
          key: LxiRidS8SN9XY2rqu2GpQPih9kuxfNbcn5nxewNwmHmxdMDundJVbf4BfwZVMcjX
        hangfire_queues:
          disable_instances: "low,default"
        integration_alert:
          endpoint: "https://api.sleekflow.io/api/notifications/integration-disconnected"
          api_key: "iVgMmTo7nYH2tfVqMjqWlzfFjapJyNqOicfHXaX13A"
          host_company_id: "8d9eaafa-ab5b-47d3-8e0a-3920ac7c05cd"
          from_phone_number: "17209612030"
          template_name: "integration_disconnect_noti"
          facebook_lead_ads_help_center_url: "https://help.sleekflow.io/integrations/facebook-lead-ads-integration"
    - location_name: eastus
      sku_config:
        sleekflow_core:
          name: P3V3
          tier: PremiumV3
        sleekflow_core_db:
          name: HS_PRMS
          tier: Hyperscale
          family: PRMS
          capacity: 2
        sleekflow_powerflow:
          name: P1V3
          tier: PremiumV3
        sleekflow_sleek_pay:
          name: P0V3
          tier: PremiumV3
        redis:
          default:
            name: Standard
            family: C
            capacity: 1
          caching:
            name: Standard
            family: C
            capacity: 1
      sql_db_config:
        administrator_login_random_secret: ec7a20cc-e365-471a-b419-9e80e0d979bd
        administrator_login_password_random_secret: 414e7364-39c7-4e3f-8790-55571de61dc1
        whitelist_ip_ranges:
          - start_ip_address: **************
            end_ip_address: **************
          - start_ip_address: **************
            end_ip_address: **************
          - start_ip_address: *************
            end_ip_address: *************
          - start_ip_address: *************
            end_ip_address: *************
          - start_ip_address: **************
            end_ip_address: **************
        is_read_scale_enable: "false"
        high_availability_replica_count: 0
      vnet:
        default_address_space: ********/16
        default_subnet_address_prefix: ********/24
        sleekflow_core_db_address_prefix: ********/24
        sleekflow_core_address_prefix: ********/24
        sleekflow_powerflow_address_prefix: ********/24
        sleekflow_sleek_pay_address_prefix: ********/24
        sleekflow_core_worker_address_prefix: ********/24
      auto_scale_config:
        sleekflow_core:
          capacity:
            default: "3"
            maximum: "20"
            minimum: "2"
          scale_out_instances: "1"
          scale_down_instances: "1"
      sleekflow_core_config:
        aspnetcore_environment: production
        logger:
          gcp_is_enabled: "TRUE"
          gcp_project_id: my-production-project-405815
          gcp_credential_json: ************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
        audit_hub:
          endpoint: https://sleekflow-a6aeg3bth3f5fneq.z01.azurefd.net/v1/audit-hub
          key: 3Ri7RqtKBR1sXHCGXLoGiasTBVqE23pzPXUQVnKPw3v5BX4zr2SM9HcXFGlUkWMq
        auth0:
          action_audience: https://api.sleekflow.io/
          action_issuer: https://sso.sleekflow.io/
          audience: https://api.sleekflow.io
          client_id: txLGs6X2eN17XOXuxZpDakoxpyxoOlHW
          client_secret: FNsmpbetisCz7xg9aALvE3v1WXdZzxhoY6-fDf4RDSbroZ_zHIYsxb31oUs20GfO
          database_connection_name: Sleekflow-Username-Password-Authentication
          domain: sleekflow.eu.auth0.com
          http_retries: 10
          issuers:
            - https://sso.sleekflow.io/
            - https://sleekflow.eu.auth0.com
          namespace: https://app.sleekflow.io/
          role_claim_type: roles
          user_email_claim_type: email
          user_id_claim_type: user_id
          username_claim_type: user_name
          tenant_hub_secret_key: 6a9_nBt?)R#@_he=v2Eo!K3B3Ae0ao`x*I`m}ZSX*S~hsQ7bQD]k#gh8r38ad,9o
          health_check:
            is_enabled: "false"
            client_id: OrxDhNRbAyWXKoSVoMuS0hIWNo3v2An0
            client_secret: NXaeck_K8gPII9HngFq3RsWKJ4QIaKq5bZM2YmkNxnq07lDEzS05Rei6l9lK14Oj
            username: *******
            password: dsfogDGSDGeodfhghdfo23o2
        azure:
          media_service:
            account_name: sfmediaproduction
            client_id: 048e63db-bf3d-442e-bf1e-d30b2fd4b555
            client_secret: ****************************************
            resource_group: sleekflow-resource-group-production853b96c8
            subscription_id: c19c9b56-93e9-4d4c-bc81-838bd3f72ad6
            tenant_id: d66fa1cc-347d-42e9-9444-19c5fd0bbcce
          text_analytics_credentials: 226f2d8217704dd6b223b4a1fd6e51c6
          text_analytics_url: https://sleekflow-prod-sp-text-analytics.cognitiveservices.azure.com/
        application_insights:
          is_telemetry_tracer_enabled: "true"
          is_sampling_disabled: "false"
        chat_api:
          api_key: qVH3fqZufwPZaPlAmsIJMwcxuHI2
          api_url: https://us-central1-app-chat-api-com.cloudfunctions.net
        commerce_hub:
          endpoint: https://sleekflow-a6aeg3bth3f5fneq.z01.azurefd.net/v1/commerce-hub
          key: w7zJUyirdVnNom4M6YTyxgtbTQDQL2gxHPRbKy9sSXaraX6GC4Ru8I1ejjpFS0LnSnJrRxAtO9YxxrRMGqj2H5EycUywMgQc0RhKWmQvmAe93j5jMJqunIG8aYilMp8Q
        crm_hub:
          endpoint: https://sleekflow-a6aeg3bth3f5fneq.z01.azurefd.net/v1/crm-hub
          key: a12da7c775d00cada5b1ee611d3f6dca
        data_snapshot:
          is_enable: "true"
        development:
          redirect_fb_webhook: https://9633-42-200-140-118.ngrok.io/facebook/Webhook
        environment_features:
          is_recurring_job_enabled: "true"
        epplus:
          excel_package:
            license_context: NonCommercial
        facebook:
          client_id: ***************
          client_secret: ********************************
        ffmpeg:
          ffmpeg_exe_name: /usr/bin/ffmpeg
        flow_hub:
          endpoint: https://sleekflow-a6aeg3bth3f5fneq.z01.azurefd.net/v1/flow-hub
          key: BWijifKMPGviFQpYEkZuLjHOTpySNVawLYhiFKHEcmJyUDQPYlKuYUmJqgXVGGHP
        hub_spot:
          internal_hub_spot_api_key: ********************************************
          is_enable: "true"
        instrumentation_engine_extension_version: disabled
        internal_google_cloud:
***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
        general_google_cloud:
********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
          project_id: my-production-project-405815
          server_location: us-west1
          google_storage_bucket_name: sleekflow-transcoder-prod-us
        ip_look_up:
          key: 8fc198c42a094d588657c6f05c707518
        intelligent_hub:
          endpoint: https://sleekflow-a6aeg3bth3f5fneq.z01.azurefd.net/v1/intelligent-hub
          key: BWijifKMPGviFQpYEkZuLjHOTpySNVawLYhiFKHEcmJyUDQPYlKuYUmJqgXVGGHP
        webhook_hub:
          endpoint: https://sleekflow-a6aeg3bth3f5fneq.z01.azurefd.net/v1/webhook-hub
          key: BWijifKMPGviFQpYEkZuLjHOTpySNVawLYhiFKHEcmJyUDQPYlKuYUmJqgXVGGHP
          auth_secret_key: O19dnDlZhSZkZ5TigIXiolCTqSl461kyBCotXGOJGUMA6eiHfyxRQVwQTFN5qMr1
        message_hub:
          endpoint: https://sleekflow-a6aeg3bth3f5fneq.z01.azurefd.net/v1/messaging-hub
          key: a12da7c775d00cada5b1ee611d3f6dca
        mixpanel:
          token: ef72dd13a0ffccb584cfdf75d3160e25
        mobile_app_management_extension_version: latest
        notification_hub:
          connection_string: Endpoint=sb://sleekflowproduction.servicebus.windows.net/;SharedAccessKeyName=DefaultFullSharedAccessSignature;SharedAccessKey=pbzIBQQoNB8rGgrHdt1vmCIuTIB/Aaey5iION3eiCbQ=
          hub_name: SleekflowProduction
        public_api_gateway:
          endpoint: https://sleekflow-a6aeg3bth3f5fneq.z01.azurefd.net/v1/public-api-gateway
          key: a12da7c775d00cada5b1ee611d3f6dca
        reseller:
          domain_name: https://partner.sleekflow.io
        rewardful:
          api_secret: ********************************
        salesforce:
          custom_active_web_app: https://sleekflow-sfmc-jb-ca-app.nicemeadow-64d75f44.eastasia.azurecontainerapps.io
        share_hub:
          endpoint: https://sleekflow-a6aeg3bth3f5fneq.z01.azurefd.net/v1/share-hub
          key: a12da7c775d00cada5b1ee611d3f6dca
        shopify:
          shopify_api_key: b1781c9e361cfc851ee8a857fd046cca
          shopify_secret_key: 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
        sleek_pay:
          domain_name: https://pay-eus.sleekflow.io
          shopify_graphql_admin_api_version: 2022-10
          stripe:
            public_keys:
              default: pk_live_51Klpk8I2ilBBY57ZWqA8WkA92DuaoRtBs8DAWWLGBfW0d8QJPQ9lclAVI1fyn5Yz6kiqqWP6vRQkMhcxtfW0Y4IY00auBhCQhm
              gb: pk_live_51LBDmrD9JBREupA8JMPwf4ubmrols7gf1znOkUEMs5zgwtpWii8mHGai7aPeBAbwdaW686fgcBDqYNDdhxEeV99Y00azIhNXet
              hk: pk_live_51Klpk8I2ilBBY57ZWqA8WkA92DuaoRtBs8DAWWLGBfW0d8QJPQ9lclAVI1fyn5Yz6kiqqWP6vRQkMhcxtfW0Y4IY00auBhCQhm
              my: pk_live_51LBDj8GBiw7eM4IGRMGeHyy6IxH4zSYIF6HojR3UU804u6U7GbN7Gi75quqzTuvfwSRqdShrAueFmWJmxtlZRmIP00oJX2bagF
              sg: pk_live_51L8iI0HRmviISgVcyuZQ49yoIRkGUqNGpi0nCZClx1BWNE89CFOjunhoSZzAZsFuRhRf0kOz69GVZEVFHUO9Uzke00J8MJnDJL
            secret_keys:
              default: ***********************************************************************************************************
              gb: ***********************************************************************************************************
              hk: ***********************************************************************************************************
              my: ***********************************************************************************************************
              sg: ***********************************************************************************************************
            connect_webhook_secrets:
              gb: whsec_1Y4PPXsK0I82TxYe4eFgeEpD3NlcRVDh
              hk: whsec_pRWAtlyrEAl5H2jD93F9FIgvbBHhZ0AU
              my: whsec_pLaEbRIwoIh4EQ5NpZmnWBblaiZzs8WB
              sg: whsec_RuZRexGsmryy7HESTlKaEZEVDR4zl7fz
            report_webhook_secrets:
              gb: whsec_G61y3pSfbivpuSNGuH7goZRwUfJcC3Rv
              hk: whsec_hFE0OkkMkZL3lWc9ryV57gQdYgQaX0Zq
              my: whsec_Vt4sfbO5hS3R8fhOQ3wa5bULlu4BkY2W
              sg: whsec_G50hdWUpMCnIN2OQ5alvrWXxV1xLNWju
            webhook_secrets:
              default: whsec_H00E3HxrHk9JbwAdDWHosR9Ia9RfyaQ9
              gb: whsec_uBlcoIsfySs4jOYbBHNSK5eFGpKG4aOs
              hk: whsec_H00E3HxrHk9JbwAdDWHosR9Ia9RfyaQ9
              my: whsec_dNasCBFB0h4gBshesaNGoU92ROEiRQQm
              sg: whsec_WGPMGY9cswabcZr8R6ZB9D7q2rfXLSz2
        snapshot_debugger_extension_version: disabled
        sql_performance:
          from_raw_sql: "true"
          is_and_condition_enabled: "true"
          is_or_condition_enabled: "true"
          is_conversation_analytics_condition_enabled: "false"
          is_shopify_order_statistics_enabled: "false"
          is_sales_performance_enabled: "false"
        stripe:
          stripe_public_key: pk_live_J0gdFoKSpThotTX5jHLKf5OJ00AMt1g3j8
          stripe_report_key: ***********************************************************************************************************
          stripe_secret_key: ******************************************
          stripe_webhook_secret: whsec_p9bgQl6uYXmlgRQf8vYkdaFuh8WiJIlK
        stripe_payment:
          stripe_payment_secret_key_gb: ***********************************************************************************************************
          stripe_payment_secret_key_hk: ***********************************************************************************************************
          stripe_payment_secret_key_my: ***********************************************************************************************************
          stripe_payment_secret_key_sg: ***********************************************************************************************************
        stripe_report:
          stripe_report_webhook_secret_gb: whsec_3W17qqv4KOaD3MrUYydhh0S69Kfq8e0s
          stripe_report_webhook_secret_hk: whsec_hfzwC1g9yLudWNLuj2nYs6162qLWwdzA
          stripe_report_webhook_secret_my: whsec_x0HOEz7Wx3OSvddWSFAIKsIDJYRdiPrC
          stripe_report_webhook_secret_sg: whsec_X7m1uFBMi0soo65ZAMfBHfGIQaCO582s
        tenant_hub:
          endpoint: https://sleekflow-a6aeg3bth3f5fneq.z01.azurefd.net/v1/tenant-hub
          key: BWijifKMPGviFQpYEkZuLjHOTpySNVawLYhiFKHEcmJyUDQPYlKuYUmJqgXVGGHP
          is_enable_tenant_logic: "true"
        ticketing_hub:
          endpoint: https://sleekflow-a6aeg3bth3f5fneq.z01.azurefd.net/v1/ticketing-hub
          key: wLYhiFKHEcmJyUDQPYlKuYUmJqgXVGGHPBWijifKMPGviFQpYEkZuLjHOTpySNVa
          is_enable_ticketing_logic: "false"
        test_swaping: 1
        token:
          audience: https://sleekflow-prod-api.azurewebsites.net
        tokens:
          audience: https://travis-crm-api-hk.azurewebsites.net
          issuer: https://sleekflow-prod-api.azurewebsites.net
          key: 9C91A5D4BF0A3D803FE4A07550C1A5D9D55BEFE1F1226C2C5D22F29D9CED8036
          lifetime: 60
        user_event_hub:
          endpoint: https://sleekflow-a6aeg3bth3f5fneq.z01.azurefd.net/v1/user-event-hub
          key: wLYhiFKHEcmJyUDQPYlKuYUmJqgXVGGHPBWijifKMPGviFQpYEkZuLjHOTpySNVa
        values:
          app_domain_name: https://app.sleekflow.io
          app_domain_name_v1: https://v1.sleekflow.io
          share_link_function: https://share.sleekflow.io
          sleekflow_api_gateway: https://sleekflow-a6aeg3bth3f5fneq.z01.azurefd.net
          sleekflow_company_id: 471a6289-b9b7-43c3-b6ad-395a1992baea
        website_http_logging_retention_days: 5
        website_node_default_version: 6.9.1
        whatsapp_cloud_api_template:
          default_image_blob_id: sleekflow.png
        xdt_microsoft_application_insights_base_extensions: disabled
        xdt_microsoft_application_insight_mode: default
        global_pricing:
          is_feature_enabled: "true"
          plan_migration_incentives_start_date: "2024-11-20"
          plan_migration_incentives_end_date: "2025-02-20"
        feature_flags:
          - feature_name: FlowBuilderMonetisation
            is_enabled: "true"
          - feature_name: CancelledSubscriptionTermination
            is_enabled: "true"
        contact_safe_deletion:
          is_feature_enabled: "true"
        partner_stack:
          public_key: pk_bvEwuCDvoz9Qh11y48006zWXdCgsvInk
          secret_key: sk_KnZllhAJOasP09CPsMOOoiUqKAO81nMy
        facebook_lead_ads_disconnected_notification:
          from_phone_number: "17209612030"
          template_name: "integration_disconnect_noti"
          reconnect_url: "https://app.sleekflow.io/en/integrations/facebook-lead-ad"
          endpoint: "https://api.sleekflow.io/api/notifications/integration-disconnected"
          api_key: "iVgMmTo7nYH2tfVqMjqWlzfFjapJyNqOicfHXaX13A"
          host_company_id: "8d9eaafa-ab5b-47d3-8e0a-3920ac7c05cd"
        hangfire_worker:
          worker_count: 20
        internal_integration_hub:
          endpoint: "https://sleekflow-a6aeg3bth3f5fneq.z01.azurefd.net/v1/internal-integration-hub"
          key: LxiRidS8SN9XY2rqu2GpQPih9kuxfNbcn5nxewNwmHmxdMDundJVbf4BfwZVMcjX
        hangfire_queues:
          disable_instances: ""
        integration_alert:
          endpoint: "https://api.sleekflow.io/api/notifications/integration-disconnected"
          api_key: "iVgMmTo7nYH2tfVqMjqWlzfFjapJyNqOicfHXaX13A"
          host_company_id: "8d9eaafa-ab5b-47d3-8e0a-3920ac7c05cd"
          from_phone_number: "17209612030"
          template_name: "integration_disconnect_noti"
          facebook_lead_ads_help_center_url: "https://help.sleekflow.io/integrations/facebook-lead-ads-integration"
    - location_name: southeastasia
      sku_config:
        sleekflow_core:
          name: P3V3
          tier: PremiumV3
        sleekflow_core_db:
          name: HS_PRMS
          tier: Hyperscale
          family: PRMS
          capacity: 2
        sleekflow_powerflow:
          name: P1V3
          tier: PremiumV3
        sleekflow_sleek_pay:
          name: P0V3
          tier: PremiumV3
        redis:
          default:
            name: Standard
            family: C
            capacity: 1
          caching:
            name: Standard
            family: C
            capacity: 1
      sql_db_config:
        administrator_login_random_secret: ec7a20cc-e365-471a-b419-9e80e0d979bd
        administrator_login_password_random_secret: 414e7364-39c7-4e3f-8790-55571de61dc1
        whitelist_ip_ranges:
          - start_ip_address: **************
            end_ip_address: **************
          - start_ip_address: **************
            end_ip_address: **************
          - start_ip_address: *************
            end_ip_address: *************
          - start_ip_address: *************
            end_ip_address: *************
          - start_ip_address: **************
            end_ip_address: **************
        is_read_scale_enable: "false"
        high_availability_replica_count: 0
      vnet:
        default_address_space: ********/16
        default_subnet_address_prefix: ********/24
        sleekflow_core_db_address_prefix: ********/24
        sleekflow_core_address_prefix: ********/24
        sleekflow_powerflow_address_prefix: ********/24
        sleekflow_sleek_pay_address_prefix: ********/24
        sleekflow_core_worker_address_prefix: ********/24
      auto_scale_config:
        sleekflow_core:
          capacity:
            default: "3"
            maximum: "10"
            minimum: "2"
          scale_out_instances: "1"
          scale_down_instances: "1"
      sleekflow_core_config:
        aspnetcore_environment: production
        logger:
          gcp_is_enabled: "TRUE"
          gcp_project_id: my-production-project-405815
          gcp_credential_json: ************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
        audit_hub:
          endpoint: https://sleekflow-a6aeg3bth3f5fneq.z01.azurefd.net/v1/audit-hub
          key: 3Ri7RqtKBR1sXHCGXLoGiasTBVqE23pzPXUQVnKPw3v5BX4zr2SM9HcXFGlUkWMq
        auth0:
          action_audience: https://api.sleekflow.io/
          action_issuer: https://sso.sleekflow.io/
          audience: https://api.sleekflow.io
          client_id: txLGs6X2eN17XOXuxZpDakoxpyxoOlHW
          client_secret: FNsmpbetisCz7xg9aALvE3v1WXdZzxhoY6-fDf4RDSbroZ_zHIYsxb31oUs20GfO
          database_connection_name: Sleekflow-Username-Password-Authentication
          domain: sleekflow.eu.auth0.com
          http_retries: 10
          issuers:
            - https://sso.sleekflow.io/
            - https://sleekflow.eu.auth0.com
          namespace: https://app.sleekflow.io/
          role_claim_type: roles
          user_email_claim_type: email
          user_id_claim_type: user_id
          username_claim_type: user_name
          tenant_hub_secret_key: 6a9_nBt?)R#@_he=v2Eo!K3B3Ae0ao`x*I`m}ZSX*S~hsQ7bQD]k#gh8r38ad,9o
          health_check:
            is_enabled: "false"
            client_id: OrxDhNRbAyWXKoSVoMuS0hIWNo3v2An0
            client_secret: NXaeck_K8gPII9HngFq3RsWKJ4QIaKq5bZM2YmkNxnq07lDEzS05Rei6l9lK14Oj
            username: *******
            password: ogihsaw38090SDFn4sdglnloksd3
        azure:
          media_service:
            account_name: sfmediaproduction
            client_id: 048e63db-bf3d-442e-bf1e-d30b2fd4b555
            client_secret: ****************************************
            resource_group: sleekflow-resource-group-production853b96c8
            subscription_id: c19c9b56-93e9-4d4c-bc81-838bd3f72ad6
            tenant_id: d66fa1cc-347d-42e9-9444-19c5fd0bbcce
          text_analytics_credentials: 226f2d8217704dd6b223b4a1fd6e51c6
          text_analytics_url: https://sleekflow-prod-sp-text-analytics.cognitiveservices.azure.com/
        application_insights:
          is_telemetry_tracer_enabled: "true"
          is_sampling_disabled: "false"
        chat_api:
          api_key: qVH3fqZufwPZaPlAmsIJMwcxuHI2
          api_url: https://us-central1-app-chat-api-com.cloudfunctions.net
        commerce_hub:
          endpoint: https://sleekflow-a6aeg3bth3f5fneq.z01.azurefd.net/v1/commerce-hub
          key: w7zJUyirdVnNom4M6YTyxgtbTQDQL2gxHPRbKy9sSXaraX6GC4Ru8I1ejjpFS0LnSnJrRxAtO9YxxrRMGqj2H5EycUywMgQc0RhKWmQvmAe93j5jMJqunIG8aYilMp8Q
        crm_hub:
          endpoint: https://sleekflow-a6aeg3bth3f5fneq.z01.azurefd.net/v1/crm-hub
          key: a12da7c775d00cada5b1ee611d3f6dca
        data_snapshot:
          is_enable: "true"
        development:
          redirect_fb_webhook: https://9633-42-200-140-118.ngrok.io/facebook/Webhook
        epplus:
          excel_package:
            license_context: NonCommercial
        facebook:
          client_id: ***************
          client_secret: ********************************
        environment_features:
          is_recurring_job_enabled: "true"
        ffmpeg:
          ffmpeg_exe_name: /usr/bin/ffmpeg
        flow_hub:
          endpoint: https://sleekflow-a6aeg3bth3f5fneq.z01.azurefd.net/v1/flow-hub
          key: BWijifKMPGviFQpYEkZuLjHOTpySNVawLYhiFKHEcmJyUDQPYlKuYUmJqgXVGGHP
        hub_spot:
          internal_hub_spot_api_key: ********************************************
          is_enable: "true"
        instrumentation_engine_extension_version: disabled
        internal_google_cloud:
***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
        general_google_cloud:
********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
          project_id: my-production-project-405815
          server_location: asia-southeast1
          google_storage_bucket_name: sleekflow-transcoder-prod-asia
        ip_look_up:
          key: 8fc198c42a094d588657c6f05c707518
        intelligent_hub:
          endpoint: https://sleekflow-a6aeg3bth3f5fneq.z01.azurefd.net/v1/intelligent-hub
          key: BWijifKMPGviFQpYEkZuLjHOTpySNVawLYhiFKHEcmJyUDQPYlKuYUmJqgXVGGHP
        webhook_hub:
          endpoint: https://sleekflow-a6aeg3bth3f5fneq.z01.azurefd.net/v1/webhook-hub
          key: BWijifKMPGviFQpYEkZuLjHOTpySNVawLYhiFKHEcmJyUDQPYlKuYUmJqgXVGGHP
          auth_secret_key: O19dnDlZhSZkZ5TigIXiolCTqSl461kyBCotXGOJGUMA6eiHfyxRQVwQTFN5qMr1
        message_hub:
          endpoint: https://sleekflow-a6aeg3bth3f5fneq.z01.azurefd.net/v1/messaging-hub
          key: a12da7c775d00cada5b1ee611d3f6dca
        mixpanel:
          token: ef72dd13a0ffccb584cfdf75d3160e25
        mobile_app_management_extension_version: latest
        notification_hub:
          connection_string: Endpoint=sb://sleekflowproduction.servicebus.windows.net/;SharedAccessKeyName=DefaultFullSharedAccessSignature;SharedAccessKey=pbzIBQQoNB8rGgrHdt1vmCIuTIB/Aaey5iION3eiCbQ=
          hub_name: SleekflowProduction
        public_api_gateway:
          endpoint: https://sleekflow-a6aeg3bth3f5fneq.z01.azurefd.net/v1/public-api-gateway
          key: a12da7c775d00cada5b1ee611d3f6dca
        reseller:
          domain_name: https://partner.sleekflow.io
        rewardful:
          api_secret: ********************************
        salesforce:
          custom_active_web_app: https://sleekflow-sfmc-jb-ca-app.nicemeadow-64d75f44.eastasia.azurecontainerapps.io
        share_hub:
          endpoint: https://sleekflow-a6aeg3bth3f5fneq.z01.azurefd.net/v1/share-hub
          key: a12da7c775d00cada5b1ee611d3f6dca
        shopify:
          shopify_api_key: b1781c9e361cfc851ee8a857fd046cca
          shopify_secret_key: 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
        sleek_pay:
          domain_name: https://pay-seas.sleekflow.io
          shopify_graphql_admin_api_version: 2022-10
          stripe:
            public_keys:
              default: pk_live_51Klpk8I2ilBBY57ZWqA8WkA92DuaoRtBs8DAWWLGBfW0d8QJPQ9lclAVI1fyn5Yz6kiqqWP6vRQkMhcxtfW0Y4IY00auBhCQhm
              gb: pk_live_51LBDmrD9JBREupA8JMPwf4ubmrols7gf1znOkUEMs5zgwtpWii8mHGai7aPeBAbwdaW686fgcBDqYNDdhxEeV99Y00azIhNXet
              hk: pk_live_51Klpk8I2ilBBY57ZWqA8WkA92DuaoRtBs8DAWWLGBfW0d8QJPQ9lclAVI1fyn5Yz6kiqqWP6vRQkMhcxtfW0Y4IY00auBhCQhm
              my: pk_live_51LBDj8GBiw7eM4IGRMGeHyy6IxH4zSYIF6HojR3UU804u6U7GbN7Gi75quqzTuvfwSRqdShrAueFmWJmxtlZRmIP00oJX2bagF
              sg: pk_live_51L8iI0HRmviISgVcyuZQ49yoIRkGUqNGpi0nCZClx1BWNE89CFOjunhoSZzAZsFuRhRf0kOz69GVZEVFHUO9Uzke00J8MJnDJL
            secret_keys:
              default: ***********************************************************************************************************
              gb: ***********************************************************************************************************
              hk: ***********************************************************************************************************
              my: ***********************************************************************************************************
              sg: ***********************************************************************************************************
            connect_webhook_secrets:
              gb: whsec_B5fvREYgoT8e1u7E0flXMfmNy7KPHQlK
              hk: whsec_WoK63GEga9W01kSTrIk4apaS6gUttEZ7
              my: whsec_Z2mQjJmDb4R1F0marevcdazHqWVibHnC
              sg: whsec_FuLLx8OO1J4uVG0a370s7lJXhx1ysUC5
            report_webhook_secrets:
              gb: whsec_BVpxQgs2lNwV1eTJ7vgiVwkWq130CcFB
              hk: whsec_GvqNkPJXRxt8Mi128sCjxfpB1vet5gNI
              my: whsec_kpIej0SlxWBs3YwSfZJTwcnuwZL25XKD
              sg: whsec_E0vf2HLi6WQ6eskqUG30T3LlrVpAr8VU
            webhook_secrets:
              default: whsec_kzZHRUl79JrZBLBAowSFB2MvdDbp5CNW
              gb: whsec_UjnDz1mtCmrYmFM3wgZ6lwpZkw7KB76m
              hk: whsec_kzZHRUl79JrZBLBAowSFB2MvdDbp5CNW
              my: whsec_mbvPz0yfHBiLKXSAq3A5zyoBHMht7VhP
              sg: whsec_Jt2Ndph8WwtcCbSOjmT2sojvzQi3vn1I
        snapshot_debugger_extension_version: disabled
        sql_performance:
          from_raw_sql: "true"
          is_and_condition_enabled: "true"
          is_or_condition_enabled: "true"
          is_conversation_analytics_condition_enabled: "false"
          is_shopify_order_statistics_enabled: "false"
          is_sales_performance_enabled: "false"
        stripe:
          stripe_public_key: pk_live_J0gdFoKSpThotTX5jHLKf5OJ00AMt1g3j8
          stripe_report_key: ***********************************************************************************************************
          stripe_secret_key: ******************************************
          stripe_webhook_secret: whsec_p9bgQl6uYXmlgRQf8vYkdaFuh8WiJIlK
        stripe_payment:
          stripe_payment_secret_key_gb: ***********************************************************************************************************
          stripe_payment_secret_key_hk: ***********************************************************************************************************
          stripe_payment_secret_key_my: ***********************************************************************************************************
          stripe_payment_secret_key_sg: ***********************************************************************************************************
        stripe_report:
          stripe_report_webhook_secret_gb: whsec_CjK5k25wvGLABwioYyUn15oVjSTddRiM
          stripe_report_webhook_secret_hk: whsec_HUGUHd3OOOPYpNiqUXEwxlIza8FTD4Uw
          stripe_report_webhook_secret_my: whsec_UCoU8kDOlHkAb4htQqQ8qzao2zxRGsiD
          stripe_report_webhook_secret_sg: whsec_JVBOEahEu0hZYKYvMgD3EsCAdchgJgEm
        tenant_hub:
          endpoint: https://sleekflow-a6aeg3bth3f5fneq.z01.azurefd.net/v1/tenant-hub
          key: BWijifKMPGviFQpYEkZuLjHOTpySNVawLYhiFKHEcmJyUDQPYlKuYUmJqgXVGGHP
          is_enable_tenant_logic: "true"
        ticketing_hub:
          endpoint: https://sleekflow-a6aeg3bth3f5fneq.z01.azurefd.net/v1/ticketing-hub
          key: wLYhiFKHEcmJyUDQPYlKuYUmJqgXVGGHPBWijifKMPGviFQpYEkZuLjHOTpySNVa
          is_enable_ticketing_logic: "false"
        test_swaping: 1
        token:
          audience: https://sleekflow-prod-api.azurewebsites.net
        tokens:
          audience: https://travis-crm-api-hk.azurewebsites.net
          issuer: https://sleekflow-prod-api.azurewebsites.net
          key: 9C91A5D4BF0A3D803FE4A07550C1A5D9D55BEFE1F1226C2C5D22F29D9CED8036
          lifetime: 60
        user_event_hub:
          endpoint: https://sleekflow-a6aeg3bth3f5fneq.z01.azurefd.net/v1/user-event-hub
          key: wLYhiFKHEcmJyUDQPYlKuYUmJqgXVGGHPBWijifKMPGviFQpYEkZuLjHOTpySNVa
        values:
          app_domain_name: https://app.sleekflow.io
          app_domain_name_v1: https://v1.sleekflow.io
          share_link_function: https://share.sleekflow.io
          sleekflow_api_gateway: https://sleekflow-a6aeg3bth3f5fneq.z01.azurefd.net
          sleekflow_company_id: 471a6289-b9b7-43c3-b6ad-395a1992baea
        website_http_logging_retention_days: 5
        website_node_default_version: 6.9.1
        whatsapp_cloud_api_template:
          default_image_blob_id: sleekflow.png
        xdt_microsoft_application_insights_base_extensions: disabled
        xdt_microsoft_application_insight_mode: default
        global_pricing:
          is_feature_enabled: "true"
          plan_migration_incentives_start_date: "2024-11-20"
          plan_migration_incentives_end_date: "2025-02-20"
        feature_flags:
          - feature_name: FlowBuilderMonetisation
            is_enabled: "true"
          - feature_name: CancelledSubscriptionTermination
            is_enabled: "true"
        contact_safe_deletion:
          is_feature_enabled: "true"
        partner_stack:
          public_key: pk_bvEwuCDvoz9Qh11y48006zWXdCgsvInk
          secret_key: sk_KnZllhAJOasP09CPsMOOoiUqKAO81nMy
        facebook_lead_ads_disconnected_notification:
          from_phone_number: "17209612030"
          template_name: "integration_disconnect_noti"
          reconnect_url: "https://app.sleekflow.io/en/integrations/facebook-lead-ad"
          endpoint: "https://api.sleekflow.io/api/notifications/integration-disconnected"
          api_key: "iVgMmTo7nYH2tfVqMjqWlzfFjapJyNqOicfHXaX13A"
          host_company_id: "8d9eaafa-ab5b-47d3-8e0a-3920ac7c05cd"
        hangfire_worker:
          worker_count: 20
        internal_integration_hub:
          endpoint: "https://sleekflow-a6aeg3bth3f5fneq.z01.azurefd.net/v1/internal-integration-hub"
          key: LxiRidS8SN9XY2rqu2GpQPih9kuxfNbcn5nxewNwmHmxdMDundJVbf4BfwZVMcjX
        hangfire_queues:
          disable_instances: ""
        integration_alert:
          endpoint: "https://api.sleekflow.io/api/notifications/integration-disconnected"
          api_key: "iVgMmTo7nYH2tfVqMjqWlzfFjapJyNqOicfHXaX13A"
          host_company_id: "8d9eaafa-ab5b-47d3-8e0a-3920ac7c05cd"
          from_phone_number: "17209612030"
          template_name: "integration_disconnect_noti"
          facebook_lead_ads_help_center_url: "https://help.sleekflow.io/integrations/facebook-lead-ads-integration"
    - location_name: uaenorth
      sku_config:
        sleekflow_core:
          name: P3V3
          tier: PremiumV3
        sleekflow_core_db:
          name: GP_Gen5_2
          tier: GeneralPurpose
          family: Gen5
          capacity: 2
        sleekflow_powerflow:
          name: P1V3
          tier: PremiumV3
        sleekflow_sleek_pay:
          name: P0V3
          tier: PremiumV3
        redis:
          default:
            name: Standard
            family: C
            capacity: 1
          caching:
            name: Standard
            family: C
            capacity: 1
      sql_db_config:
        administrator_login_random_secret: 28CAB1E5-EA75-43A3-8B56-F3212B17242D
        administrator_login_password_random_secret: 80262C93-7146-419E-A82E-CBE9EA65157E
        whitelist_ip_ranges:
          - start_ip_address: **************
            end_ip_address: **************
          - start_ip_address: **************
            end_ip_address: **************
          - start_ip_address: *************
            end_ip_address: *************
          - start_ip_address: *************
            end_ip_address: *************
          - start_ip_address: **************
            end_ip_address: **************
        is_read_scale_enable: "false"
        high_availability_replica_count: 0
      vnet:
        default_address_space: ********/16
        default_subnet_address_prefix: ********/24
        sleekflow_core_db_address_prefix: ********/24
        sleekflow_core_address_prefix: ********/24
        sleekflow_powerflow_address_prefix: ********/24
        sleekflow_sleek_pay_address_prefix: ********/24
        sleekflow_core_worker_address_prefix: ********/24
      auto_scale_config:
        sleekflow_core:
          capacity:
            default: "3"
            maximum: "10"
            minimum: "2"
          scale_out_instances: "1"
          scale_down_instances: "1"
      sleekflow_core_config:
        aspnetcore_environment: production
        logger:
          gcp_is_enabled: "TRUE"
          gcp_project_id: my-production-project-405815
          gcp_credential_json: ************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
        audit_hub:
          endpoint: https://sleekflow-a6aeg3bth3f5fneq.z01.azurefd.net/v1/audit-hub
          key: 3Ri7RqtKBR1sXHCGXLoGiasTBVqE23pzPXUQVnKPw3v5BX4zr2SM9HcXFGlUkWMq
        auth0:
          action_audience: https://api.sleekflow.io/
          action_issuer: https://sso.sleekflow.io/
          audience: https://api.sleekflow.io
          client_id: txLGs6X2eN17XOXuxZpDakoxpyxoOlHW
          client_secret: FNsmpbetisCz7xg9aALvE3v1WXdZzxhoY6-fDf4RDSbroZ_zHIYsxb31oUs20GfO
          database_connection_name: Sleekflow-Username-Password-Authentication
          domain: sleekflow.eu.auth0.com
          http_retries: 10
          issuers:
            - https://sso.sleekflow.io/
            - https://sleekflow.eu.auth0.com
          namespace: https://app.sleekflow.io/
          role_claim_type: roles
          user_email_claim_type: email
          user_id_claim_type: user_id
          username_claim_type: user_name
          tenant_hub_secret_key: 6a9_nBt?)R#@_he=v2Eo!K3B3Ae0ao`x*I`m}ZSX*S~hsQ7bQD]k#gh8r38ad,9o
          health_check:
            is_enabled: "false"
            client_id: OrxDhNRbAyWXKoSVoMuS0hIWNo3v2An0
            client_secret: NXaeck_K8gPII9HngFq3RsWKJ4QIaKq5bZM2YmkNxnq07lDEzS05Rei6l9lK14Oj
            username: *******
            password: dfuiogho2420h0bjnbj3##$
        azure:
          media_service:
            account_name: sfmediaproduction
            client_id: 048e63db-bf3d-442e-bf1e-d30b2fd4b555
            client_secret: ****************************************
            resource_group: sleekflow-resource-group-production853b96c8
            subscription_id: c19c9b56-93e9-4d4c-bc81-838bd3f72ad6
            tenant_id: d66fa1cc-347d-42e9-9444-19c5fd0bbcce
          text_analytics_credentials: 226f2d8217704dd6b223b4a1fd6e51c6
          text_analytics_url: https://sleekflow-prod-sp-text-analytics.cognitiveservices.azure.com/
        application_insights:
          is_telemetry_tracer_enabled: "true"
          is_sampling_disabled: "false"
        chat_api:
          api_key: qVH3fqZufwPZaPlAmsIJMwcxuHI2
          api_url: https://us-central1-app-chat-api-com.cloudfunctions.net
        commerce_hub:
          endpoint: https://sleekflow-a6aeg3bth3f5fneq.z01.azurefd.net/v1/commerce-hub
          key: w7zJUyirdVnNom4M6YTyxgtbTQDQL2gxHPRbKy9sSXaraX6GC4Ru8I1ejjpFS0LnSnJrRxAtO9YxxrRMGqj2H5EycUywMgQc0RhKWmQvmAe93j5jMJqunIG8aYilMp8Q
        crm_hub:
          endpoint: https://sleekflow-a6aeg3bth3f5fneq.z01.azurefd.net/v1/crm-hub
          key: a12da7c775d00cada5b1ee611d3f6dca
        data_snapshot:
          is_enable: "true"
        development:
          redirect_fb_webhook: https://9633-42-200-140-118.ngrok.io/facebook/Webhook
        epplus:
          excel_package:
            license_context: NonCommercial
        facebook:
          client_id: ***************
          client_secret: ********************************
        environment_features:
          is_recurring_job_enabled: "true"
        ffmpeg:
          ffmpeg_exe_name: /usr/bin/ffmpeg
        flow_hub:
          endpoint: https://sleekflow-a6aeg3bth3f5fneq.z01.azurefd.net/v1/flow-hub
          key: BWijifKMPGviFQpYEkZuLjHOTpySNVawLYhiFKHEcmJyUDQPYlKuYUmJqgXVGGHP
        hub_spot:
          internal_hub_spot_api_key: ********************************************
          is_enable: "true"
        instrumentation_engine_extension_version: disabled
        internal_google_cloud:
***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
        general_google_cloud:
********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
          project_id: my-production-project-405815
          server_location: europe-west1
          google_storage_bucket_name: sleekflow-transcoder-prod-eu
        ip_look_up:
          key: 8fc198c42a094d588657c6f05c707518
        intelligent_hub:
          endpoint: https://sleekflow-a6aeg3bth3f5fneq.z01.azurefd.net/v1/intelligent-hub
          key: BWijifKMPGviFQpYEkZuLjHOTpySNVawLYhiFKHEcmJyUDQPYlKuYUmJqgXVGGHP
        webhook_hub:
          endpoint: https://sleekflow-a6aeg3bth3f5fneq.z01.azurefd.net/v1/webhook-hub
          key: BWijifKMPGviFQpYEkZuLjHOTpySNVawLYhiFKHEcmJyUDQPYlKuYUmJqgXVGGHP
          auth_secret_key: O19dnDlZhSZkZ5TigIXiolCTqSl461kyBCotXGOJGUMA6eiHfyxRQVwQTFN5qMr1
        message_hub:
          endpoint: https://sleekflow-a6aeg3bth3f5fneq.z01.azurefd.net/v1/messaging-hub
          key: a12da7c775d00cada5b1ee611d3f6dca
        mixpanel:
          token: ef72dd13a0ffccb584cfdf75d3160e25
        mobile_app_management_extension_version: latest
        notification_hub:
          connection_string: Endpoint=sb://sleekflowproduction.servicebus.windows.net/;SharedAccessKeyName=DefaultFullSharedAccessSignature;SharedAccessKey=pbzIBQQoNB8rGgrHdt1vmCIuTIB/Aaey5iION3eiCbQ=
          hub_name: SleekflowProduction
        public_api_gateway:
          endpoint: https://sleekflow-a6aeg3bth3f5fneq.z01.azurefd.net/v1/public-api-gateway
          key: a12da7c775d00cada5b1ee611d3f6dca
        reseller:
          domain_name: https://partner.sleekflow.io
        rewardful:
          api_secret: ********************************
        salesforce:
          custom_active_web_app: https://sleekflow-sfmc-jb-ca-app.nicemeadow-64d75f44.eastasia.azurecontainerapps.io
        share_hub:
          endpoint: https://sleekflow-a6aeg3bth3f5fneq.z01.azurefd.net/v1/share-hub
          key: a12da7c775d00cada5b1ee611d3f6dca
        shopify:
          shopify_api_key: b1781c9e361cfc851ee8a857fd046cca
          shopify_secret_key: 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
        sleek_pay:
          domain_name: https://pay-uaen.sleekflow.io
          shopify_graphql_admin_api_version: 2022-10
          stripe:
            public_keys:
              default: pk_live_51Klpk8I2ilBBY57ZWqA8WkA92DuaoRtBs8DAWWLGBfW0d8QJPQ9lclAVI1fyn5Yz6kiqqWP6vRQkMhcxtfW0Y4IY00auBhCQhm
              gb: pk_live_51LBDmrD9JBREupA8JMPwf4ubmrols7gf1znOkUEMs5zgwtpWii8mHGai7aPeBAbwdaW686fgcBDqYNDdhxEeV99Y00azIhNXet
              hk: pk_live_51Klpk8I2ilBBY57ZWqA8WkA92DuaoRtBs8DAWWLGBfW0d8QJPQ9lclAVI1fyn5Yz6kiqqWP6vRQkMhcxtfW0Y4IY00auBhCQhm
              my: pk_live_51LBDj8GBiw7eM4IGRMGeHyy6IxH4zSYIF6HojR3UU804u6U7GbN7Gi75quqzTuvfwSRqdShrAueFmWJmxtlZRmIP00oJX2bagF
              sg: pk_live_51L8iI0HRmviISgVcyuZQ49yoIRkGUqNGpi0nCZClx1BWNE89CFOjunhoSZzAZsFuRhRf0kOz69GVZEVFHUO9Uzke00J8MJnDJL
            secret_keys:
              default: ***********************************************************************************************************
              gb: ***********************************************************************************************************
              hk: ***********************************************************************************************************
              my: ***********************************************************************************************************
              sg: ***********************************************************************************************************
            connect_webhook_secrets:
              gb: whsec_EBiq16MdLFdP8i8z5n5k45ynWbvTLm8i
              hk: whsec_waMEaNT1gw7JnUv459xPoxtxAnLCM3tb
              my: whsec_OCANDKJlyC8k6naVus9cCiXpA8vJJ6JK
              sg: whsec_GKm4Y2BbXtcE7ua3o9U6FpcuhwoLJh5S
            report_webhook_secrets:
              gb: whsec_qWL8URs9yzjOrTf1czMvicmFfvCb9cyG
              hk: whsec_EohLaxIwwjbawewleqYTuBfe2vdXVCSK
              my: whsec_NGI2nxVQ9RreP3eGWKWcwhSCH4dwDv0J
              sg: whsec_rcRikd4y59vVzehoUs8WJ9NiANbci9UU
            webhook_secrets:
              default: whsec_Ai1PLy3H91y8TBhRQgrAkTFdbNdSGylM
              gb: whsec_pzvaG1sxzGfCdkoTcoesiEai8uaiCf8w
              hk: whsec_Ai1PLy3H91y8TBhRQgrAkTFdbNdSGylM
              my: whsec_IfPzyBL2EylK2edydo80q27IIf8lyBdJ
              sg: whsec_6mKKWxBGG247QOGYzCEsfNWfhWVkx2oq
        snapshot_debugger_extension_version: disabled
        sql_performance:
          from_raw_sql: "true"
          is_and_condition_enabled: "true"
          is_or_condition_enabled: "true"
          is_conversation_analytics_condition_enabled: "false"
          is_shopify_order_statistics_enabled: "false"
          is_sales_performance_enabled: "false"
        stripe:
          stripe_public_key: pk_live_J0gdFoKSpThotTX5jHLKf5OJ00AMt1g3j8
          stripe_report_key: ***********************************************************************************************************
          stripe_secret_key: ******************************************
          stripe_webhook_secret: whsec_p9bgQl6uYXmlgRQf8vYkdaFuh8WiJIlK
        stripe_payment:
          stripe_payment_secret_key_gb: ***********************************************************************************************************
          stripe_payment_secret_key_hk: ***********************************************************************************************************
          stripe_payment_secret_key_my: ***********************************************************************************************************
          stripe_payment_secret_key_sg: ***********************************************************************************************************
        stripe_report:
          stripe_report_webhook_secret_gb:
          stripe_report_webhook_secret_hk: whsec_zsmCxJPQP9KEEZc9K94QAB8vrbTdFlP3
          stripe_report_webhook_secret_my:
          stripe_report_webhook_secret_sg:
        tenant_hub:
          endpoint: https://sleekflow-a6aeg3bth3f5fneq.z01.azurefd.net/v1/tenant-hub
          key: BWijifKMPGviFQpYEkZuLjHOTpySNVawLYhiFKHEcmJyUDQPYlKuYUmJqgXVGGHP
          is_enable_tenant_logic: "true"
        ticketing_hub:
          endpoint: https://sleekflow-a6aeg3bth3f5fneq.z01.azurefd.net/v1/ticketing-hub
          key: wLYhiFKHEcmJyUDQPYlKuYUmJqgXVGGHPBWijifKMPGviFQpYEkZuLjHOTpySNVa
          is_enable_ticketing_logic: "false"
        test_swaping: 1
        token:
          audience: https://sleekflow-prod-api.azurewebsites.net
        tokens:
          audience: https://travis-crm-api-hk.azurewebsites.net
          issuer: https://sleekflow-prod-api.azurewebsites.net
          key: 9C91A5D4BF0A3D803FE4A07550C1A5D9D55BEFE1F1226C2C5D22F29D9CED8036
          lifetime: 60
        user_event_hub:
          endpoint: https://sleekflow-a6aeg3bth3f5fneq.z01.azurefd.net/v1/user-event-hub
          key: wLYhiFKHEcmJyUDQPYlKuYUmJqgXVGGHPBWijifKMPGviFQpYEkZuLjHOTpySNVa
        values:
          app_domain_name: https://app.sleekflow.io
          app_domain_name_v1: https://v1.sleekflow.io
          share_link_function: https://share.sleekflow.io
          sleekflow_api_gateway: https://sleekflow-a6aeg3bth3f5fneq.z01.azurefd.net
          sleekflow_company_id: 471a6289-b9b7-43c3-b6ad-395a1992baea
        website_http_logging_retention_days: 5
        website_node_default_version: 6.9.1
        whatsapp_cloud_api_template:
          default_image_blob_id: sleekflow.png
        xdt_microsoft_application_insights_base_extensions: disabled
        xdt_microsoft_application_insight_mode: default
        global_pricing:
          is_feature_enabled: "true"
          plan_migration_incentives_start_date: "2024-11-20"
          plan_migration_incentives_end_date: "2025-02-20"
        feature_flags:
          - feature_name: FlowBuilderMonetisation
            is_enabled: "true"
          - feature_name: CancelledSubscriptionTermination
            is_enabled: "true"
        contact_safe_deletion:
          is_feature_enabled: "true"
        partner_stack:
          public_key: pk_bvEwuCDvoz9Qh11y48006zWXdCgsvInk
          secret_key: sk_KnZllhAJOasP09CPsMOOoiUqKAO81nMy
        facebook_lead_ads_disconnected_notification:
          from_phone_number: "17209612030"
          template_name: "integration_disconnect_noti"
          reconnect_url: "https://app.sleekflow.io/en/integrations/facebook-lead-ad"
          endpoint: "https://api.sleekflow.io/api/notifications/integration-disconnected"
          api_key: "iVgMmTo7nYH2tfVqMjqWlzfFjapJyNqOicfHXaX13A"
          host_company_id: "8d9eaafa-ab5b-47d3-8e0a-3920ac7c05cd"
        hangfire_worker:
          worker_count: 20
        internal_integration_hub:
          endpoint: "https://sleekflow-a6aeg3bth3f5fneq.z01.azurefd.net/v1/internal-integration-hub"
          key: LxiRidS8SN9XY2rqu2GpQPih9kuxfNbcn5nxewNwmHmxdMDundJVbf4BfwZVMcjX
        hangfire_queues:
          disable_instances: ""
        integration_alert:
          endpoint: "https://api.sleekflow.io/api/notifications/integration-disconnected"
          api_key: "iVgMmTo7nYH2tfVqMjqWlzfFjapJyNqOicfHXaX13A"
          host_company_id: "8d9eaafa-ab5b-47d3-8e0a-3920ac7c05cd"
          from_phone_number: "17209612030"
          template_name: "integration_disconnect_noti"
          facebook_lead_ads_help_center_url: "https://help.sleekflow.io/integrations/facebook-lead-ads-integration"
    - location_name: westeurope
      sku_config:
        sleekflow_core:
          name: P3V3
          tier: PremiumV3
        sleekflow_core_db:
          name: HS_PRMS
          tier: Hyperscale
          family: PRMS
          capacity: 2
        sleekflow_powerflow:
          name: P1V3
          tier: PremiumV3
        sleekflow_sleek_pay:
          name: P0V3
          tier: PremiumV3
        redis:
          default:
            name: Standard
            family: C
            capacity: 1
          caching:
            name: Standard
            family: C
            capacity: 1
      sql_db_config:
        administrator_login_random_secret: E91B4511-0EE5-4380-9681-B5232FA9599C
        administrator_login_password_random_secret: 4A610075-E03C-454F-8E27-8C34C6118F22
        whitelist_ip_ranges:
          - start_ip_address: **************
            end_ip_address: **************
          - start_ip_address: **************
            end_ip_address: **************
          - start_ip_address: *************
            end_ip_address: *************
          - start_ip_address: *************
            end_ip_address: *************
          - start_ip_address: **************
            end_ip_address: **************
        is_read_scale_enable: "false"
        high_availability_replica_count: 0
      vnet:
        default_address_space: ********/16
        default_subnet_address_prefix: ********/24
        sleekflow_core_db_address_prefix: ********/24
        sleekflow_core_address_prefix: ********/24
        sleekflow_powerflow_address_prefix: ********/24
        sleekflow_sleek_pay_address_prefix: ********/24
        sleekflow_core_worker_address_prefix: ********/24
      auto_scale_config:
        sleekflow_core:
          capacity:
            default: "3"
            maximum: "10"
            minimum: "2"
          scale_out_instances: "1"
          scale_down_instances: "1"
      sleekflow_core_config:
        aspnetcore_environment: production
        logger:
          gcp_is_enabled: "TRUE"
          gcp_project_id: my-production-project-405815
          gcp_credential_json: ************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
        audit_hub:
          endpoint: https://sleekflow-a6aeg3bth3f5fneq.z01.azurefd.net/v1/audit-hub
          key: 3Ri7RqtKBR1sXHCGXLoGiasTBVqE23pzPXUQVnKPw3v5BX4zr2SM9HcXFGlUkWMq
        auth0:
          action_audience: https://api.sleekflow.io/
          action_issuer: https://sso.sleekflow.io/
          audience: https://api.sleekflow.io
          client_id: txLGs6X2eN17XOXuxZpDakoxpyxoOlHW
          client_secret: FNsmpbetisCz7xg9aALvE3v1WXdZzxhoY6-fDf4RDSbroZ_zHIYsxb31oUs20GfO
          database_connection_name: Sleekflow-Username-Password-Authentication
          domain: sleekflow.eu.auth0.com
          http_retries: 10
          issuers:
            - https://sso.sleekflow.io/
            - https://sleekflow.eu.auth0.com
          namespace: https://app.sleekflow.io/
          role_claim_type: roles
          user_email_claim_type: email
          user_id_claim_type: user_id
          username_claim_type: user_name
          tenant_hub_secret_key: 6a9_nBt?)R#@_he=v2Eo!K3B3Ae0ao`x*I`m}ZSX*S~hsQ7bQD]k#gh8r38ad,9o
          health_check:
            is_enabled: "false"
            client_id: OrxDhNRbAyWXKoSVoMuS0hIWNo3v2An0
            client_secret: NXaeck_K8gPII9HngFq3RsWKJ4QIaKq5bZM2YmkNxnq07lDEzS05Rei6l9lK14Oj
            username: *******
            password: dfuiogho2420h0bjnbj3##$
#            username: *******
#            password: XJgL22*ZkVX`Jw*,!j*`B~SdZ~r>
        azure:
          media_service:
            account_name: sfmediaproduction
            client_id: 048e63db-bf3d-442e-bf1e-d30b2fd4b555
            client_secret: ****************************************
            resource_group: sleekflow-resource-group-production853b96c8
            subscription_id: c19c9b56-93e9-4d4c-bc81-838bd3f72ad6
            tenant_id: d66fa1cc-347d-42e9-9444-19c5fd0bbcce
          text_analytics_credentials: 226f2d8217704dd6b223b4a1fd6e51c6
          text_analytics_url: https://sleekflow-prod-sp-text-analytics.cognitiveservices.azure.com/
        application_insights:
          is_telemetry_tracer_enabled: "true"
          is_sampling_disabled: "false"
        chat_api:
          api_key: qVH3fqZufwPZaPlAmsIJMwcxuHI2
          api_url: https://us-central1-app-chat-api-com.cloudfunctions.net
        commerce_hub:
          endpoint: https://sleekflow-a6aeg3bth3f5fneq.z01.azurefd.net/v1/commerce-hub
          key: w7zJUyirdVnNom4M6YTyxgtbTQDQL2gxHPRbKy9sSXaraX6GC4Ru8I1ejjpFS0LnSnJrRxAtO9YxxrRMGqj2H5EycUywMgQc0RhKWmQvmAe93j5jMJqunIG8aYilMp8Q
        crm_hub:
          endpoint: https://sleekflow-a6aeg3bth3f5fneq.z01.azurefd.net/v1/crm-hub
          key: a12da7c775d00cada5b1ee611d3f6dca
        data_snapshot:
          is_enable: "true"
        development:
          redirect_fb_webhook: https://9633-42-200-140-118.ngrok.io/facebook/Webhook
        environment_features:
          is_recurring_job_enabled: "true"
        epplus:
          excel_package:
            license_context: NonCommercial
        facebook:
          client_id: ***************
          client_secret: ********************************
        ffmpeg:
          ffmpeg_exe_name: /usr/bin/ffmpeg
        flow_hub:
          endpoint: https://sleekflow-a6aeg3bth3f5fneq.z01.azurefd.net/v1/flow-hub
          key: BWijifKMPGviFQpYEkZuLjHOTpySNVawLYhiFKHEcmJyUDQPYlKuYUmJqgXVGGHP
        hub_spot:
          internal_hub_spot_api_key: ********************************************
          is_enable: "true"
        instrumentation_engine_extension_version: disabled
        internal_google_cloud:
***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
        general_google_cloud:
********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
          project_id: my-production-project-405815
          server_location: europe-west1
          google_storage_bucket_name: sleekflow-transcoder-prod-eu
        ip_look_up:
          key: 8fc198c42a094d588657c6f05c707518
        intelligent_hub:
          endpoint: https://sleekflow-a6aeg3bth3f5fneq.z01.azurefd.net/v1/intelligent-hub
          key: BWijifKMPGviFQpYEkZuLjHOTpySNVawLYhiFKHEcmJyUDQPYlKuYUmJqgXVGGHP
        webhook_hub:
          endpoint: https://sleekflow-a6aeg3bth3f5fneq.z01.azurefd.net/v1/webhook-hub
          key: BWijifKMPGviFQpYEkZuLjHOTpySNVawLYhiFKHEcmJyUDQPYlKuYUmJqgXVGGHP
          auth_secret_key: O19dnDlZhSZkZ5TigIXiolCTqSl461kyBCotXGOJGUMA6eiHfyxRQVwQTFN5qMr1
        message_hub:
          endpoint: https://sleekflow-a6aeg3bth3f5fneq.z01.azurefd.net/v1/messaging-hub
          key: a12da7c775d00cada5b1ee611d3f6dca
        mixpanel:
          token: ef72dd13a0ffccb584cfdf75d3160e25
        mobile_app_management_extension_version: latest
        notification_hub:
          connection_string: Endpoint=sb://sleekflowproduction.servicebus.windows.net/;SharedAccessKeyName=DefaultFullSharedAccessSignature;SharedAccessKey=pbzIBQQoNB8rGgrHdt1vmCIuTIB/Aaey5iION3eiCbQ=
          hub_name: SleekflowProduction
        public_api_gateway:
          endpoint: https://sleekflow-a6aeg3bth3f5fneq.z01.azurefd.net/v1/public-api-gateway
          key: a12da7c775d00cada5b1ee611d3f6dca
        reseller:
          domain_name: https://partner.sleekflow.io
        rewardful:
          api_secret: ********************************
        salesforce:
          custom_active_web_app: https://sleekflow-sfmc-jb-ca-app.nicemeadow-64d75f44.eastasia.azurecontainerapps.io
        share_hub:
          endpoint: https://sleekflow-a6aeg3bth3f5fneq.z01.azurefd.net/v1/share-hub
          key: a12da7c775d00cada5b1ee611d3f6dca
        shopify:
          shopify_api_key: b1781c9e361cfc851ee8a857fd046cca
          shopify_secret_key: 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
        sleek_pay:
          domain_name: https://pay-weu.sleekflow.io
          shopify_graphql_admin_api_version: 2022-10
          stripe:
            public_keys:
              default: pk_live_51Klpk8I2ilBBY57ZWqA8WkA92DuaoRtBs8DAWWLGBfW0d8QJPQ9lclAVI1fyn5Yz6kiqqWP6vRQkMhcxtfW0Y4IY00auBhCQhm
              gb: pk_live_51LBDmrD9JBREupA8JMPwf4ubmrols7gf1znOkUEMs5zgwtpWii8mHGai7aPeBAbwdaW686fgcBDqYNDdhxEeV99Y00azIhNXet
              hk: pk_live_51Klpk8I2ilBBY57ZWqA8WkA92DuaoRtBs8DAWWLGBfW0d8QJPQ9lclAVI1fyn5Yz6kiqqWP6vRQkMhcxtfW0Y4IY00auBhCQhm
              my: pk_live_51LBDj8GBiw7eM4IGRMGeHyy6IxH4zSYIF6HojR3UU804u6U7GbN7Gi75quqzTuvfwSRqdShrAueFmWJmxtlZRmIP00oJX2bagF
              sg: pk_live_51L8iI0HRmviISgVcyuZQ49yoIRkGUqNGpi0nCZClx1BWNE89CFOjunhoSZzAZsFuRhRf0kOz69GVZEVFHUO9Uzke00J8MJnDJL
            secret_keys:
              default: ***********************************************************************************************************
              gb: ***********************************************************************************************************
              hk: ***********************************************************************************************************
              my: ***********************************************************************************************************
              sg: ***********************************************************************************************************
            connect_webhook_secrets:
              gb: whsec_FwYzGl5svpvtWjGGlCxylnkNtj9zV6oi
              hk: whsec_DuyJdjOu5dgO4Eb7WMP2eJtODF9cu600
              my: whsec_wo8FvTUknuTzHlPyrjX2rAojElgbPKMQ
              sg: whsec_BmRPxQNCXPS9IIpm1OGy8smX43XW5MAS
            report_webhook_secrets:
              gb: whsec_AmA9JEIeh9BZubJgxgiaw316O9muCcnj
              hk: whsec_aggDtxmHnMZD5F3d98bFJZKEtU71FCvS
              my: whsec_tweb6JWaHfioQObOKZqDtXlGZCYV7voO
              sg: whsec_fepc8laPqmpgB15ehh1ice0izm7GKhIC
            webhook_secrets:
              default: whsec_Xc70UAaHw14yRFbXp3GAnOnXiMcnoS91
              gb: whsec_jEZauU8OY8U83SkqlUFhwjTTL55VtlhH
              hk: whsec_Xc70UAaHw14yRFbXp3GAnOnXiMcnoS91
              my: whsec_MHwR6obPOPU0cMuU3u3eHPnMek6GceeR
              sg: whsec_F3gvkRZhnuIc4uWpEpjQpYrzarM31rKf
        snapshot_debugger_extension_version: disabled
        sql_performance:
          from_raw_sql: "true"
          is_and_condition_enabled: "true"
          is_or_condition_enabled: "true"
          is_conversation_analytics_condition_enabled: "false"
          is_shopify_order_statistics_enabled: "false"
          is_sales_performance_enabled: "false"
        stripe:
          stripe_public_key: pk_live_J0gdFoKSpThotTX5jHLKf5OJ00AMt1g3j8
          stripe_report_key: ***********************************************************************************************************
          stripe_secret_key: ******************************************
          stripe_webhook_secret: whsec_Zlwg0UYmHuWygdGPY5HqAvQH1DiZ7oRl
        stripe_payment:
          stripe_payment_secret_key_gb: ***********************************************************************************************************
          stripe_payment_secret_key_hk: ***********************************************************************************************************
          stripe_payment_secret_key_my: ***********************************************************************************************************
          stripe_payment_secret_key_sg: ***********************************************************************************************************
        stripe_report:
          stripe_report_webhook_secret_gb: whsec_3W17qqv4KOaD3MrUYydhh0S69Kfq8e0s
          stripe_report_webhook_secret_hk: whsec_hfzwC1g9yLudWNLuj2nYs6162qLWwdzA
          stripe_report_webhook_secret_my: whsec_x0HOEz7Wx3OSvddWSFAIKsIDJYRdiPrC
          stripe_report_webhook_secret_sg: whsec_X7m1uFBMi0soo65ZAMfBHfGIQaCO582s
        tenant_hub:
          endpoint: https://sleekflow-a6aeg3bth3f5fneq.z01.azurefd.net/v1/tenant-hub
          key: BWijifKMPGviFQpYEkZuLjHOTpySNVawLYhiFKHEcmJyUDQPYlKuYUmJqgXVGGHP
          is_enable_tenant_logic: "true"
        ticketing_hub:
          endpoint: https://sleekflow-a6aeg3bth3f5fneq.z01.azurefd.net/v1/ticketing-hub
          key: wLYhiFKHEcmJyUDQPYlKuYUmJqgXVGGHPBWijifKMPGviFQpYEkZuLjHOTpySNVa
          is_enable_ticketing_logic: "false"
        test_swaping: 1
        token:
          audience: https://sleekflow-prod-api.azurewebsites.net
        tokens:
          audience: https://travis-crm-api-hk.azurewebsites.net
          issuer: https://sleekflow-prod-api.azurewebsites.net
          key: 9C91A5D4BF0A3D803FE4A07550C1A5D9D55BEFE1F1226C2C5D22F29D9CED8036
          lifetime: 60
        user_event_hub:
          endpoint: https://sleekflow-a6aeg3bth3f5fneq.z01.azurefd.net/v1/user-event-hub
          key: wLYhiFKHEcmJyUDQPYlKuYUmJqgXVGGHPBWijifKMPGviFQpYEkZuLjHOTpySNVa
        values:
          app_domain_name: https://app.sleekflow.io
          app_domain_name_v1: https://v1.sleekflow.io
          share_link_function: https://share.sleekflow.io
          sleekflow_api_gateway: https://sleekflow-a6aeg3bth3f5fneq.z01.azurefd.net
          sleekflow_company_id: 471a6289-b9b7-43c3-b6ad-395a1992baea
        website_http_logging_retention_days: 5
        website_node_default_version: 6.9.1
        whatsapp_cloud_api_template:
          default_image_blob_id: sleekflow.png
        xdt_microsoft_application_insights_base_extensions: disabled
        xdt_microsoft_application_insight_mode: default
        global_pricing:
          is_feature_enabled: "true"
          plan_migration_incentives_start_date: "2024-11-20"
          plan_migration_incentives_end_date: "2025-02-20"
        feature_flags:
          - feature_name: FlowBuilderMonetisation
            is_enabled: "true"
          - feature_name: CancelledSubscriptionTermination
            is_enabled: "true"
        contact_safe_deletion:
          is_feature_enabled: "true"
        partner_stack:
          public_key: pk_bvEwuCDvoz9Qh11y48006zWXdCgsvInk
          secret_key: sk_KnZllhAJOasP09CPsMOOoiUqKAO81nMy
        facebook_lead_ads_disconnected_notification:
          from_phone_number: "17209612030"
          template_name: "integration_disconnect_noti"
          reconnect_url: "https://app.sleekflow.io/en/integrations/facebook-lead-ad"
          endpoint: "https://api.sleekflow.io/api/notifications/integration-disconnected"
          api_key: "iVgMmTo7nYH2tfVqMjqWlzfFjapJyNqOicfHXaX13A"
          host_company_id: "8d9eaafa-ab5b-47d3-8e0a-3920ac7c05cd"
        hangfire_worker:
          worker_count: 20
        internal_integration_hub:
          endpoint: "https://sleekflow-a6aeg3bth3f5fneq.z01.azurefd.net/v1/internal-integration-hub"
          key: LxiRidS8SN9XY2rqu2GpQPih9kuxfNbcn5nxewNwmHmxdMDundJVbf4BfwZVMcjX
        hangfire_queues:
          disable_instances: ""
        integration_alert:
          endpoint: "https://api.sleekflow.io/api/notifications/integration-disconnected"
          api_key: "iVgMmTo7nYH2tfVqMjqWlzfFjapJyNqOicfHXaX13A"
          host_company_id: "8d9eaafa-ab5b-47d3-8e0a-3920ac7c05cd"
          from_phone_number: "17209612030"
          template_name: "integration_disconnect_noti"
          facebook_lead_ads_help_center_url: "https://help.sleekflow.io/integrations/facebook-lead-ads-integration"