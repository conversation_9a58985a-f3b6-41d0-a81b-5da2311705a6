using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Hangfire;
using Sleekflow.Apis.AuditHub.Api;
using Sleekflow.Apis.AuditHub.Model;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.**********************;
using Travis_backend.Enums;

namespace Travis_backend.FlowHubs;

public interface IStaffHooks
{
    Task OnStaffAddedToTeamsAsync(
        string companyId,
        long staffId,
        List<CompanyTeam> companyTeams);

    Task OnStaffRemovedFromTeamsAsync(
        string companyId,
        long staffId,
        List<CompanyTeam> companyTeams);

    Task OnStaffStatusUpdatedAsync(
        string companyId,
        long staffId,
        StaffStatus status);

    Task OnStaffRoleUpdatedAsync(
        string companyId,
        long staffId,
        StaffUserRole role);

    Task OnStaff2FaRevokedAsync(
        string companyId,
        long staffId);

    Task OnStaffDeletedAsync(
        string companyId,
        long staffId);

    Task OnTeamDeletedAsync(
        string companyId,
        CompanyTeam team);
}

public class StaffHooks : IStaffHooks
{
    private readonly IDistributedInvocationContextService _distributedInvocationContextService;

    public StaffHooks(IDistributedInvocationContextService distributedInvocationContextService)
    {
        _distributedInvocationContextService = distributedInvocationContextService;
    }

    public Task OnStaffAddedToTeamsAsync(
        string companyId,
        long staffId,
        List<CompanyTeam> companyTeams)
    {
        BackgroundJob.Enqueue<ISystemAuditLogsApi>(
            x => x.SystemAuditLogsCreateSystemAuditLogPostAsync(
                null,
                _distributedInvocationContextService.GetSerializedContextHeader(),
                new CreateSystemAuditLogInput(
                    null,
                    "staff-added-to-teams",
                    new StaffAddedToTeamsSystemLogData(
                        staffId.ToString(),
                        companyTeams.Select(x => x.Id.ToString()).ToList(),
                        companyTeams.Select(x => x.TeamName.ToString()).ToList())),
                CancellationToken.None));

        return Task.CompletedTask;
    }

    public Task OnStaffRemovedFromTeamsAsync(
        string companyId,
        long staffId,
        List<CompanyTeam> companyTeams)
    {
        BackgroundJob.Enqueue<ISystemAuditLogsApi>(
            x => x.SystemAuditLogsCreateSystemAuditLogPostAsync(
                null,
                _distributedInvocationContextService.GetSerializedContextHeader(),
                new CreateSystemAuditLogInput(
                    null,
                    "staff-removed-from-teams",
                    new StaffRemovedFromTeamSystemLogData(
                        staffId.ToString(),
                        companyTeams.Select(x => x.Id.ToString()).ToList(),
                        companyTeams.Select(x => x.TeamName.ToString()).ToList())),
                CancellationToken.None));

        return Task.CompletedTask;
    }

    public Task OnStaffStatusUpdatedAsync(
        string companyId,
        long staffId,
        StaffStatus status)
    {
        BackgroundJob.Enqueue<ISystemAuditLogsApi>(
            x => x.SystemAuditLogsCreateSystemAuditLogPostAsync(
                null,
                _distributedInvocationContextService.GetSerializedContextHeader(),
                new CreateSystemAuditLogInput(
                    null,
                    "staff-status-updated",
                    new StaffStatusUpdatedSystemLogData(
                        staffId.ToString(),
                        Enum.GetName(status))),
                CancellationToken.None));

        return Task.CompletedTask;
    }

    public Task OnStaffRoleUpdatedAsync(
        string companyId,
        long staffId,
        StaffUserRole role)
    {
        BackgroundJob.Enqueue<ISystemAuditLogsApi>(
            x => x.SystemAuditLogsCreateSystemAuditLogPostAsync(
                null,
                _distributedInvocationContextService.GetSerializedContextHeader(),
                new CreateSystemAuditLogInput(
                    null,
                    "staff-role-updated",
                    new StaffRoleUpdatedSystemLogData(
                        staffId.ToString(),
                        Enum.GetName(role))),
                CancellationToken.None));

        return Task.CompletedTask;
    }

    public Task OnStaff2FaRevokedAsync(string companyId, long staffId)
    {
        BackgroundJob.Enqueue<ISystemAuditLogsApi>(
            x => x.SystemAuditLogsCreateSystemAuditLogPostAsync(
                null,
                _distributedInvocationContextService.GetSerializedContextHeader(),
                new CreateSystemAuditLogInput(
                    null,
                    "staff-2fa-revoked",
                    new Staff2faRevokedSystemLogData(
                        staffId.ToString())),
                CancellationToken.None));

        return Task.CompletedTask;
    }

    public Task OnStaffDeletedAsync(string companyId, long staffId)
    {
        BackgroundJob.Enqueue<ISystemAuditLogsApi>(
            x => x.SystemAuditLogsCreateSystemAuditLogPostAsync(
                null,
                _distributedInvocationContextService.GetSerializedContextHeader(),
                new CreateSystemAuditLogInput(
                    null,
                    "staff-deleted",
                    new StaffDeletedSystemLogData(
                        staffId.ToString())),
                CancellationToken.None));

        return Task.CompletedTask;
    }

    public Task OnTeamDeletedAsync(string companyId, CompanyTeam team)
    {
        BackgroundJob.Enqueue<ISystemAuditLogsApi>(
            x => x.SystemAuditLogsCreateSystemAuditLogPostAsync(
                null,
                _distributedInvocationContextService.GetSerializedContextHeader(),
                new CreateSystemAuditLogInput(
                    null,
                    "team-deleted",
                    new TeamDeletedSystemLogData(
                        team.Id.ToString(),
                        team.TeamName)),
                CancellationToken.None));

        return Task.CompletedTask;
    }
}

public interface IUserProfileLabelHooks
{
    public const string
        UserProfileLabelCreated = "user-profile-label-created"; // LabelName, LabelColor

    public const string
        UserProfileLabelUpdated = "user-profile-label-updated"; // LabelName, LabelColor

    public const string
        UserProfileLabelDeleted = "user-profile-label-deleted"; // LabelName

    Task OnUserProfileLabelCreatedAsync(
        string companyId,
        CompanyHashtag hashtag);

    Task OnUserProfileLabelUpdatedAsync(
        string companyId,
        CompanyHashtag hashtag,
        string distributedInvocationContext);

    Task OnUserProfileLabelDeletedAsync(
        string companyId,
        CompanyHashtag hashtag);
}

public class UserProfileLabelHooks : IUserProfileLabelHooks
{
    private readonly IDistributedInvocationContextService _distributedInvocationContextService;

    public UserProfileLabelHooks(IDistributedInvocationContextService distributedInvocationContextService)
    {
        _distributedInvocationContextService = distributedInvocationContextService;
    }

    public Task OnUserProfileLabelCreatedAsync(
        string companyId,
        CompanyHashtag hashtag)
    {
        BackgroundJob.Enqueue<ISystemAuditLogsApi>(
            x => x.SystemAuditLogsCreateSystemAuditLogPostAsync(
                null,
                _distributedInvocationContextService.GetSerializedContextHeader(),
                new CreateSystemAuditLogInput(
                    null,
                    "user-profile-label-created",
                    new UserProfileLabelCreatedsSystemLogData(
                        hashtag.Hashtag,
                        Enum.GetName(hashtag.HashTagColor))),
                CancellationToken.None));

        return Task.CompletedTask;
    }

    public Task OnUserProfileLabelUpdatedAsync(
        string companyId,
        CompanyHashtag hashtag,
        string distributedInvocationContext)
    {
        BackgroundJob.Enqueue<ISystemAuditLogsApi>(
            x => x.SystemAuditLogsCreateSystemAuditLogPostAsync(
                null,
                distributedInvocationContext,
                new CreateSystemAuditLogInput(
                    null,
                    "user-profile-label-updated",
                    new UserProfileLabelUpdatedSystemLogData(
                        hashtag.Hashtag,
                        Enum.GetName(hashtag.HashTagColor))),
                CancellationToken.None));

        return Task.CompletedTask;
    }

    public Task OnUserProfileLabelDeletedAsync(
        string companyId,
        CompanyHashtag hashtag)
    {
        BackgroundJob.Enqueue<ISystemAuditLogsApi>(
            x => x.SystemAuditLogsCreateSystemAuditLogPostAsync(
                null,
                _distributedInvocationContextService.GetSerializedContextHeader(),
                new CreateSystemAuditLogInput(
                    null,
                    "user-profile-label-deleted",
                    new UserProfileLabelDeletedSystemLogData(
                        hashtag.Hashtag)),
                CancellationToken.None));

        return Task.CompletedTask;
    }
}

public interface IMessageTemplateHooks
{
    public const string
        MessageTemplateUnbookmarked =
            "message-template-unbookmarked"; // TemplateId, ChannelType, ChannelIdentityId, TemplateName

    Task OnMessageTemplateUnbookmarkedAsync(
        string companyId,
        string templateId,
        string channelType,
        string channelIdentityId,
        string templateName);
}

public class MessageTemplateHooks : IMessageTemplateHooks
{
    private readonly IDistributedInvocationContextService _distributedInvocationContextService;

    public MessageTemplateHooks(IDistributedInvocationContextService distributedInvocationContextService)
    {
        _distributedInvocationContextService = distributedInvocationContextService;
    }

    public Task OnMessageTemplateUnbookmarkedAsync(
        string companyId,
        string templateId,
        string channelType,
        string channelIdentityId,
        string templateName)
    {
        BackgroundJob.Enqueue<ISystemAuditLogsApi>(
            x => x.SystemAuditLogsCreateSystemAuditLogPostAsync(
                null,
                _distributedInvocationContextService.GetSerializedContextHeader(),
                new CreateSystemAuditLogInput(
                    null,
                    "message-template-unbookmarked",
                    new MessageTemplateUnbookmarkedSystemLogData(
                        templateId,
                        channelType,
                        channelIdentityId,
                        templateName)),
                CancellationToken.None));

        return Task.CompletedTask;
    }
}

public interface IUserProfileListHooks
{
    public const string
        UserProfileListCreated = "user-profile-list-created"; // ListId, ListName

    public const string
        UserProfileListDeleted = "user-profile-list-deleted"; // ListId, ListName

    public const string
        UserProfileListExported = "user-profile-list-exported"; // ListId, ListName

    Task OnUserProfileListCreatedAsync(
        string listId,
        string listName);

    Task OnUserProfileListDeletedAsync(
        string listId,
        string listName);

    Task OnUserProfileListExportedAsync(
        string listId,
        string listName);
}

public class UserProfileListHooks : IUserProfileListHooks
{
    private readonly IDistributedInvocationContextService _distributedInvocationContextService;

    public UserProfileListHooks(IDistributedInvocationContextService distributedInvocationContextService)
    {
        _distributedInvocationContextService = distributedInvocationContextService;
    }

    public Task OnUserProfileListCreatedAsync(string listId, string listName)
    {
        BackgroundJob.Enqueue<ISystemAuditLogsApi>(
            x => x.SystemAuditLogsCreateSystemAuditLogPostAsync(
                null,
                _distributedInvocationContextService.GetSerializedContextHeader(),
                new CreateSystemAuditLogInput(
                    null,
                    "user-profile-list-created",
                    new UserProfileListCreatedSystemLogData(
                        listId,
                        listName)),
                CancellationToken.None));

        return Task.CompletedTask;
    }

    public Task OnUserProfileListDeletedAsync(string listId, string listName)
    {
        BackgroundJob.Enqueue<ISystemAuditLogsApi>(
            x => x.SystemAuditLogsCreateSystemAuditLogPostAsync(
                null,
                _distributedInvocationContextService.GetSerializedContextHeader(),
                new CreateSystemAuditLogInput(
                    null,
                    "user-profile-list-deleted",
                    new UserProfileListDeletedSystemLogData(
                        listId,
                        listName)),
                CancellationToken.None));

        return Task.CompletedTask;
    }

    public Task OnUserProfileListExportedAsync(string listId, string listName)
    {
        BackgroundJob.Enqueue<ISystemAuditLogsApi>(
            x => x.SystemAuditLogsCreateSystemAuditLogPostAsync(
                null,
                _distributedInvocationContextService.GetSerializedContextHeader(),
                new CreateSystemAuditLogInput(
                    null,
                    "user-profile-list-exported",
                    new UserProfileListExportedSystemLogData(
                        listId,
                        listName)),
                CancellationToken.None));

        return Task.CompletedTask;
    }
}

public interface IBroadcastHooks
{
    Task OnBroadcastDeletedAsync(
        string companyId,
        BroadcastHistory broadcast);
}

public class BroadcastHooks : IBroadcastHooks
{
    private readonly IDistributedInvocationContextService _distributedInvocationContextService;

    public BroadcastHooks(IDistributedInvocationContextService distributedInvocationContextService)
    {
        _distributedInvocationContextService = distributedInvocationContextService;
    }

    public Task OnBroadcastDeletedAsync(
        string companyId,
        BroadcastHistory broadcast)
    {
        BackgroundJob.Enqueue<ISystemAuditLogsApi>(
            x => x.SystemAuditLogsCreateSystemAuditLogPostAsync(
                null,
                _distributedInvocationContextService.GetSerializedContextHeader(),
                new CreateSystemAuditLogInput(
                    null,
                    "broadcast-deleted",
                    new BroadcastDeletedSystemLogData(
                        broadcast.Id.ToString(),
                        broadcast.BroadcastCampaign.TemplateName)),
                CancellationToken.None));

        return Task.CompletedTask;
    }
}