using System;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Telegram.Bot;
using Telegram.Bot.Types;
using Travis_backend.Constants;
using Travis_backend.ConversationDomain.Models;
using Travis_backend.Database;
using Travis_backend.MessageDomain.Models;
using Travis_backend.OpenTelemetry.Meters;
using Travis_backend.OpenTelemetry.Meters.Constants;

namespace Travis_backend.MessageDomain.ChannelMessageProvider.ChannelMessageHandlers;

public class TelegramChannelMessageHandler : IChannelMessageHandler
{
    private readonly ApplicationDbContext _appDbContext;
    private readonly IConfiguration _configuration;
    private readonly ILogger<TelegramChannelMessageHandler> _logger;
    private readonly IConversationMeters _conversationMeters;

    public string ChannelType => ChannelTypes.Telegram;

    public TelegramChannelMessageHandler(
        ApplicationDbContext appDbContext,
        IConfiguration configuration,
        ILogger<TelegramChannelMessageHandler> logger,
        IConversationMeters conversationMeters)
    {
        _appDbContext = appDbContext;
        _configuration = configuration;
        _logger = logger;
        _conversationMeters = conversationMeters;
    }

    public async ValueTask<ConversationMessage> SendChannelMessageAsync(
        Conversation conversation,
        ConversationMessage conversationMessage)
    {
        // Send by user
        if (conversationMessage.TelegramReceiver == null ||
            !string.IsNullOrEmpty(conversationMessage.MessageUniqueID))
        {
            return conversationMessage;
        }

        var telegramConfig = await _appDbContext.ConfigTelegramConfigs.FirstOrDefaultAsync(
            x => x.CompanyId == conversation.CompanyId &&
                 x.TelegramBotId == conversationMessage.TelegramReceiver.TelegramBotId);

        string messageContent = string.Empty;

        if (conversationMessage.TranslationResults != null)
        {
            messageContent = conversationMessage.TranslationResults.FirstOrDefault().translations.FirstOrDefault()
                .text;
        }
        else
        {
            messageContent = conversationMessage.MessageContent;
        }

        bool isMessageSent = false;
        Message response = null;
        string channelStatusMessage = null;

        try
        {
            if (telegramConfig == null)
            {
                throw new Exception("Telegram Channel not found.");
            }

            var telegramBotClient = new TelegramBotClient(telegramConfig.TelegramBotToken);

            switch (conversationMessage.MessageType)
            {
                case "text":
                    response = await telegramBotClient.SendTextMessageAsync(
                        conversationMessage.TelegramReceiver.TelegramChatId,
                        messageContent);

                    break;
                case "file":
                    if (conversationMessage.UploadedFiles != null && conversationMessage.UploadedFiles.Count > 0)
                    {
                        var domainName = _configuration.GetValue<String>("Values:DomainName");

                        foreach (var uploadedFile in conversationMessage.UploadedFiles)
                        {
                            var fileName = Path.GetFileName(uploadedFile.Filename);

                            if (uploadedFile.MIMEType.Contains("jpg") || uploadedFile.MIMEType.Contains("jpeg"))
                            {
                                response = await telegramBotClient.SendPhotoAsync(
                                    conversationMessage.TelegramReceiver.TelegramChatId,
                                    photo: $"{domainName}/Message/File/Private/{uploadedFile.FileId}/{fileName}",
                                    caption: messageContent);
                            }
                            else
                            {
                                response = await telegramBotClient.SendDocumentAsync(
                                    conversationMessage.TelegramReceiver.TelegramChatId,
                                    $"{domainName}/Message/File/Private/{uploadedFile.FileId}/{fileName}",
                                    caption: messageContent);
                            }
                        }
                    }

                    break;
                default:
                    throw new ArgumentException("Unsupported MessageType.");
            }

            isMessageSent = true;
        }
        catch (Exception e)
        {
            _logger.LogError(
                $"Error Send Telegram Message to {conversationMessage.ConversationId}, {e.GetType().Name} : {e.Message}");

            isMessageSent = false;

            channelStatusMessage = e.Message;
        }

        if (isMessageSent)
        {
            conversationMessage.MessageUniqueID = $"{response.Chat.Id}-{response.MessageId}";
            conversationMessage.Status = MessageStatus.Received;

            _conversationMeters.IncrementCounter(ChannelTypes.Telegram, ConversationMeterOptions.SendSuccess);
        }
        else
        {
            conversationMessage.Status = MessageStatus.Failed;
            conversationMessage.ChannelStatusMessage = channelStatusMessage;

            _conversationMeters.IncrementCounter(ChannelTypes.Telegram, ConversationMeterOptions.SendFailed);
        }

        await _appDbContext.SaveChangesAsync();

        return conversationMessage;
    }
}