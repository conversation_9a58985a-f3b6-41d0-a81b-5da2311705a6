﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using GraphApi.Client.Const.WhatsappCloudApi;
using Hangfire;
using Humanizer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Polly;
using Sleekflow.Apis.MessagingHub.Model;
using Travis_backend.BackgroundTaskServices.Attributes;
using Travis_backend.Cache;
using Travis_backend.Constants;
using Travis_backend.ConversationDomain.ConversationResolver;
using Travis_backend.ConversationDomain.Services;
using Travis_backend.ConversationServices;
using Travis_backend.Database;
using Travis_backend.Extensions;
using Travis_backend.FlowHubs;
using Travis_backend.Helpers;
using Travis_backend.MessageDomain.Models;
using Travis_backend.MessageDomain.ViewModels;
using Travis_backend.SignalR;
using Travis_backend.Telemetries;
using Travis_backend.Telemetries.Constants;

namespace Travis_backend.Controllers.WebhookControllers;

[AllowAnonymous]
[Route("/whatsapp/cloudapi/webhook")]
public class WhatsappCloudApiWebhooksController : Controller
{
    private readonly ApplicationDbContext _appDbContext;
    private readonly ILogger<WhatsappCloudApiWebhooksController> _logger;
    private readonly IConversationMessageService _conversationMessageService;
    private readonly ILockService _lockService;
    private readonly ISignalRService _signalRService;
    private readonly IWhatsappCloudApiProductCatalogService _whatsappCloudApiProductCatalogService;
    private readonly IExtendedMessageFileService _extendedMessageFileService;
    private readonly IConversationService _conversationService;
    private readonly IConversationResolver _conversationResolver;
    private readonly IWhatsappCloudApiService _whatsappCloudApiService;
    private readonly IApplicationInsightsTelemetryTracer _applicationInsightsTelemetryTracer;
    private readonly IUserProfileHooks _userProfileHooks;

    public WhatsappCloudApiWebhooksController(
        ApplicationDbContext appDbContext,
        ILogger<WhatsappCloudApiWebhooksController> logger,
        IConversationMessageService conversationMessageService,
        ILockService lockService,
        ISignalRService signalRService,
        IWhatsappCloudApiProductCatalogService whatsappCloudApiProductCatalogService,
        IExtendedMessageFileService extendedMessageFileService,
        IConversationResolver conversationResolver,
        IConversationService conversationService,
        IWhatsappCloudApiService whatsappCloudApiService,
        IApplicationInsightsTelemetryTracer applicationInsightsTelemetryTracer,
        IUserProfileHooks userProfileHooks)
    {
        _appDbContext = appDbContext;
        _logger = logger;
        _conversationMessageService = conversationMessageService;
        _lockService = lockService;
        _signalRService = signalRService;
        _whatsappCloudApiProductCatalogService = whatsappCloudApiProductCatalogService;
        _extendedMessageFileService = extendedMessageFileService;
        _conversationResolver = conversationResolver;
        _conversationService = conversationService;
        _whatsappCloudApiService = whatsappCloudApiService;
        _applicationInsightsTelemetryTracer = applicationInsightsTelemetryTracer;
        _userProfileHooks = userProfileHooks;
    }

    [HttpPost("{companyId}/{messagingHubPhoneNumberId}")]
    public async Task<ActionResult> Webhook(
        [FromRoute]
        string companyId,
        [FromRoute]
        string messagingHubPhoneNumberId,
        [FromQuery(Name = "verify_code")]
        string verifyCode)
    {
        var payload = await new StreamReader(HttpContext.Request.Body).ReadToEndAsync();

        _logger.LogInformation(
            "WhatsappCloudApiWebhook - Webhook: {Payload}",
            payload);

        try
        {
            var webhookPayload = JsonConvert.DeserializeObject<WhatsappCloudApiWebhookTravisMessage>(payload);

            // await HandleWebhookAsync(companyId, messagingHubPhoneNumberId, webhookPayload);
            Policy.Handle<Exception>()
                .WaitAndRetry(
                    3,
                    sleepDurationProvider: i => TimeSpan.FromMilliseconds(100),
                    onRetry: (exception, _, retryCount, _) =>
                    {
                        _logger.LogError(
                            exception,
                            "WhatsappCloudApiWebhook: Enqueue error {ExceptionMessage} with retry {RetryCount}",
                            exception.Message,
                            retryCount);
                    })
                .Execute(
                    () =>
                    {
                        BackgroundJob.Enqueue(
                            HangfireQueues.High,
                            () => HandleWebhookAsync(companyId, messagingHubPhoneNumberId, webhookPayload));

                        _applicationInsightsTelemetryTracer.TraceEvent(
                            TraceEventNames.HangfireBackgroundJobEnqueued,
                            new Dictionary<string, string>()
                            {
                                {
                                    "channel_type", ChannelTypes.WhatsappCloudApi
                                }
                            });
                    });
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "WhatsappCloudApiWebhook error: {ExceptionMessage}",
                ex.Message);

            _applicationInsightsTelemetryTracer.TraceEvent(
                TraceEventNames.HangfireBackgroundJobEnqueueFailed,
                new Dictionary<string, string>()
                {
                    {
                        "channel_type", ChannelTypes.WhatsappCloudApi
                    }
                });

            return BadRequest();
        }

        return Ok();
    }

    [ApiExplorerSettings(IgnoreApi = true)]
    [HangfireJobExpirationTimeout(timeoutInMinutes: 60)]
    [AutomaticRetry(
        Attempts = 10,
        OnAttemptsExceeded = AttemptsExceededAction.Fail,
        DelaysInSeconds =
        [
            60, // 1 minute
            120, // 2 minutes
            240, // 4 minutes
            480, // 8 minutes
            960, // 16 minutes
            1920, // 32 minutes
            3600, // 1 hour
            7200, // 2 hours
            14400, // 4 hours
            28800, // 8 hours
        ])]
    public async Task HandleWebhookAsync(
        string companyId,
        string messagingHubPhoneNumberId,
        WhatsappCloudApiWebhookTravisMessage webhookPayload)
    {
        _applicationInsightsTelemetryTracer.TraceEvent(
            TraceEventNames.HangfireBackgroundJobStarted,
            new Dictionary<string, string>()
            {
                {
                    "channel_type", ChannelTypes.WhatsappCloudApi
                }
            });

        var config = await _appDbContext.ConfigWhatsappCloudApiConfigs
            .AsNoTracking()
            .FirstOrDefaultAsync(
                x => x.CompanyId == companyId && x.MessagingHubWabaPhoneNumberId == messagingHubPhoneNumberId);

        if (config == null)
        {
            _logger.LogWarning(
                "[Cloud API Webhook] Cloud API channel not found for company {CompanyId}, messagingHubPhoneNumberId {MessagingHubPhoneNumberId}",
                companyId,
                messagingHubPhoneNumberId);
            return;
        }

        var webhookMessagePayload = webhookPayload.Value;

        if (webhookMessagePayload.Messages != null)
        {
            foreach (var message in webhookMessagePayload.Messages)
            {
                var (conversation, myLock) = await _conversationResolver.GetConversationForReceivingMessageAsync(
                    companyId,
                    ChannelTypes.WhatsappCloudApi,
                    config.WhatsappPhoneNumber,
                    message.From,
                    webhookMessagePayload.Contacts.FirstOrDefault(
                            x => x.WaId == PhoneNumberHelper.NormalizeWhatsappPhoneNumber(message.From))
                        ?.Profile?.Name);

                var conversationMessage = new ConversationMessage
                {
                    Channel = ChannelTypes.WhatsappCloudApi,
                    WhatsappCloudApiSender = conversation.WhatsappCloudApiUser,
                    MessageUniqueID = message.Id,
                    IsSentFromSleekflow = false,
                    Timestamp = long.Parse(message.Timestamp),
                    ChannelIdentityId = config.WhatsappPhoneNumber,
                    QuotedMsgId = message.Context?.Id,
                    Status = MessageStatus.Received,
                };

                if (!string.IsNullOrEmpty(message.Timestamp))
                {
                    var time = MyDateTimeUtil.UnixTimeStampToDateTimeUTC(conversationMessage.Timestamp * 1000);
                    conversationMessage.CreatedAt = time;
                    conversationMessage.UpdatedAt = time;

                    // Webhook performance check
                    var timeDiff = DateTime.UtcNow - time;
                    if (timeDiff > TimeSpan.FromSeconds(30))
                    {
                        _logger.LogWarning(
                            "[Cloud API Webhook] Incoming message is delayed over {TimeDiff} seconds. Message id {MessageId}, Company Id {Company}",
                            timeDiff.TotalSeconds,
                            message.Id,
                            companyId);
                    }
                }

                FileURLMessage messageFile = null;
                var tryRunOptin = false;

                // Repack the webhook received message to conversation message
                switch (message.Type)
                {
                    case WhatsappCloudApiWebhookMessageTypeConst.text:
                        var textMessage = message.Text.Body;

                        if (message.Referral != null)
                        {
                            // Referral Text Format: Url (SourceType: SourceId)
                            var referralText = $"{message.Referral.SourceUrl}";

                            if (message.Referral is { SourceType: not null, SourceId: not null })
                            {
                                referralText +=
                                    $" ({message.Referral.SourceType.Transform(To.TitleCase)}: {message.Referral.SourceId})";
                            }

                            textMessage = referralText + "\n" + textMessage;

                            conversationMessage.ExtendedMessagePayload = new ExtendedMessagePayload();

                            conversationMessage.ExtendedMessagePayload.SetExtendedMessagePayloadDetailWithType(
                                new ExtendedMessagePayloadDetail
                                {
                                    WhatsappCloudApiReferral = message.Referral
                                });

                            conversationMessage.ExtendedMessagePayload.ExtendedMessagePayloadDetail
                                .WhatsappCloudApiReferral = message.Referral;
                        }

                        conversationMessage.MessageType = "text";
                        conversationMessage.MessageContent = textMessage;

                        break;
                    case WhatsappCloudApiWebhookMessageTypeConst.audio:
                        var audioFile = await _whatsappCloudApiService.GetWhatsappCloudApiMediaAsync(
                            companyId,
                            messagingHubPhoneNumberId,
                            message.Audio.Id);
                        var audioExtension = MimeTypeMap.GetExtension(message.Audio.MimeType);

                        messageFile = new FileURLMessage()
                        {
                            FileName = !string.IsNullOrEmpty(Path.GetExtension(message.Audio.MimeType))
                                ? message.Audio.Id
                                : message.Audio.Id + audioExtension,
                            MIMEType = message.Audio.MimeType,
                            FileURL = audioFile.Data.BlobUrl
                        };

                        conversationMessage.MessageType = "file";
                        conversationMessage.MessageContent = null;

                        break;
                    case WhatsappCloudApiWebhookMessageTypeConst.voice:
                        var voiceFile = await _whatsappCloudApiService.GetWhatsappCloudApiMediaAsync(
                            companyId,
                            messagingHubPhoneNumberId,
                            message.Voice.Id);
                        var voiceExtension = MimeTypeMap.GetExtension(message.Voice.MimeType);

                        messageFile = new FileURLMessage()
                        {
                            FileName = !string.IsNullOrEmpty(Path.GetExtension(message.Voice.MimeType))
                                ? message.Voice.Id
                                : message.Voice.Id + voiceExtension,
                            MIMEType = message.Voice.MimeType,
                            FileURL = voiceFile.Data.BlobUrl
                        };

                        conversationMessage.MessageType = "file";
                        conversationMessage.MessageContent = null;

                        break;
                    case WhatsappCloudApiWebhookMessageTypeConst.video:
                        var videoFile = await _whatsappCloudApiService.GetWhatsappCloudApiMediaAsync(
                            companyId,
                            messagingHubPhoneNumberId,
                            message.Video.Id);
                        var videoExtension = MimeTypeMap.GetExtension(message.Video.MimeType);

                        messageFile = new FileURLMessage()
                        {
                            FileName = !string.IsNullOrEmpty(Path.GetExtension(message.Video.MimeType))
                                ? message.Video.Id
                                : message.Video.Id + videoExtension,
                            MIMEType = message.Video.MimeType,
                            FileURL = videoFile.Data.BlobUrl
                        };

                        conversationMessage.MessageType = "file";
                        conversationMessage.MessageContent = message.Video.Caption;

                        break;
                    case WhatsappCloudApiWebhookMessageTypeConst.document:
                        var documentExtension = String.Empty;

                        try
                        {
                            documentExtension = MimeTypeMap.GetExtension(message.Document.MimeType);
                        }
                        catch (ArgumentException ex)
                        {
                            _logger.LogError(
                                ex,
                                "Get file extension error {ErrorMessage} for message {MessageWebhook}",
                                ex.Message,
                                JsonConvert.SerializeObject(message));
                        }

                        var documentFile = await _whatsappCloudApiService.GetWhatsappCloudApiMediaAsync(
                            companyId,
                            messagingHubPhoneNumberId,
                            message.Document.Id);

                        messageFile = new FileURLMessage()
                        {
                            FileName = FilenameHelper.FormatFileName(
                                message.Document.Filename,
                                message.Document.MimeType),
                            MIMEType = message.Document.MimeType,
                            FileURL = documentFile.Data.BlobUrl
                        };

                        conversationMessage.MessageType = "file";
                        conversationMessage.MessageContent = message.Document.Caption;

                        break;
                    case WhatsappCloudApiWebhookMessageTypeConst.image:
                        var imageFile = await _whatsappCloudApiService.GetWhatsappCloudApiMediaAsync(
                            companyId,
                            messagingHubPhoneNumberId,
                            message.Image.Id);
                        var imageExtension = MimeTypeMap.GetExtension(message.Image.MimeType);

                        messageFile = new FileURLMessage()
                        {
                            FileName = !string.IsNullOrEmpty(Path.GetExtension(message.Image.MimeType))
                                ? message.Image.Id
                                : message.Image.Id + imageExtension,
                            MIMEType = message.Image.MimeType,
                            FileURL = imageFile.Data.BlobUrl
                        };

                        conversationMessage.MessageType = "file";
                        conversationMessage.MessageContent = message.Image.Caption;

                        break;
                    case WhatsappCloudApiWebhookMessageTypeConst.sticker:
                        var stickerFile = await _whatsappCloudApiService.GetWhatsappCloudApiMediaAsync(
                            companyId,
                            messagingHubPhoneNumberId,
                            message.Sticker.Id);
                        var stickerExtension = MimeTypeMap.GetExtension(message.Sticker.MimeType);

                        messageFile = new FileURLMessage()
                        {
                            FileName = message.Sticker.Id + ".webp",
                            MIMEType = message.Sticker.MimeType,
                            FileURL = stickerFile.Data.BlobUrl
                        };

                        conversationMessage.MessageType = "file";

                        break;
                    case WhatsappCloudApiWebhookMessageTypeConst.interactive:
                        var interactiveMessageContent = string.Empty;

                        if (message.Interactive.Type == WhatsappCloudApiInteractiveReplyTypeConst.button_reply)
                        {
                            interactiveMessageContent = message.Interactive.ButtonReply.Title;
                        }
                        else if (message.Interactive.Type == WhatsappCloudApiInteractiveReplyTypeConst.list_reply)
                        {
                            interactiveMessageContent = message.Interactive.ListReply.Title;
                        }
                        else if (message.Interactive is
                                 {
                                     Type: WhatsappCloudApiInteractiveReplyTypeConst.nfm_reply,
                                     NfmReply.Name: "flow"
                                 })
                        {
                            interactiveMessageContent = message.Interactive.NfmReply.Body; // sent

                            var flowSubmissionDataDict =
                                JsonConvert.DeserializeObject<Dictionary<string, object>>(
                                    message.Interactive.NfmReply.ResponseJson);

                            if (flowSubmissionDataDict != null)
                            {
                                if (WhatsappCloudApiFlowSubmissionHelper.TryParseWhatsappCloudApiFlowSubmission(
                                        flowSubmissionDataDict,
                                        out var whatsappCloudApiFlowSubmission))
                                {
                                    conversationMessage.Metadata ??= new Dictionary<string, object>();

                                    conversationMessage.Metadata.TryAdd(
                                        "whatsappcloudapi:flow_submission",
                                        whatsappCloudApiFlowSubmission);

                                    _appDbContext.Entry(conversationMessage).Property(x => x.Metadata)
                                        .IsModified = true;
                                }
                                else
                                {
                                    await _lockService.ReleaseLockAsync(myLock);
                                    continue;
                                }
                            }
                            else
                            {
                                await _lockService.ReleaseLockAsync(myLock);
                                continue;
                            }
                        }

                        conversationMessage.MessageType = "text";
                        conversationMessage.MessageContent = interactiveMessageContent;
                        conversationMessage.ExtendedMessagePayload = new ExtendedMessagePayload();

                        conversationMessage.ExtendedMessagePayload.SetExtendedMessagePayloadDetailWithType(
                            new ExtendedMessagePayloadDetail
                            {
                                WhatsappCloudApiInteractiveReply = message.Interactive
                            });

                        break;
                    case WhatsappCloudApiWebhookMessageTypeConst.button: // Template Quick Reply button

                        // OptIn TODO
                        tryRunOptin = config.IsOptInEnable && config.OptInConfig != null;


                        conversationMessage.MessageType = "text";
                        conversationMessage.MessageContent = message.Button.Text;
                        conversationMessage.ExtendedMessagePayload = new ExtendedMessagePayload();

                        conversationMessage.ExtendedMessagePayload.SetExtendedMessagePayloadDetailWithType(
                            new ExtendedMessagePayloadDetail
                            {
                                WhatsappCloudApiTemplateButtonReply = message.Button
                            });

                        break;

                    // case WhatsappCloudApiWebhookMessageTypeConst.contacts:
                    case WhatsappCloudApiWebhookMessageTypeConst.location:
                        var locationMessage = $"Shared a Location:\n" +
                                              (!string.IsNullOrWhiteSpace(message.Location.Address)
                                                  ? $"Address: {message.Location.Address}\n"
                                                  : string.Empty) +
                                              (!string.IsNullOrWhiteSpace(message.Location.Name)
                                                  ? $"Name: {message.Location.Name}\n"
                                                  : string.Empty) +
                                              $"https://maps.google.com/?q={message.Location.Latitude},{message.Location.Longitude}";

                        conversationMessage.MessageType = "text";
                        conversationMessage.MessageContent = locationMessage;
                        conversationMessage.ExtendedMessagePayload = new ExtendedMessagePayload();

                        conversationMessage.ExtendedMessagePayload.SetExtendedMessagePayloadDetailWithType(
                            new ExtendedMessagePayloadDetail
                            {
                                WhatsappCloudApiLocationReply = message.Location
                            });

                        break;
                    case WhatsappCloudApiWebhookMessageTypeConst.contacts:
                        string contactMessage = string.Empty;

                        foreach (var contact in message.Contacts)
                        {
                            var phoneNumbers = string.Empty;

                            if (contact.Phones is { Count: > 0 })
                            {
                                foreach (var phone in contact.Phones)
                                {
                                    contactMessage += $"{contact.Name?.FormattedName}: {phone.Phone}\n";
                                }
                            }
                        }

                        conversationMessage.MessageType = "text";
                        conversationMessage.MessageContent = contactMessage;
                        conversationMessage.ExtendedMessagePayload = new ExtendedMessagePayload();

                        conversationMessage.ExtendedMessagePayload.SetExtendedMessagePayloadDetailWithType(
                            new ExtendedMessagePayloadDetail
                            {
                                WhatsappCloudApiContactsReply = message.Contacts
                            });

                        break;
                    case WhatsappCloudApiWebhookMessageTypeConst.order:
                        var wabaDto = config.Waba;
                        var phoneNumberDto = config.WabaPhoneNumber;

                        var whatsappCloudApiOrderDetailObject =
                            new WhatsappCloudApiOrderDetailObject(message.Order);

                        if (phoneNumberDto != null && config.ProductCatalogSetting is
                                { HasEnabledProductCatalog: true })
                        {
                            if (wabaDto.WabaProductCatalog != null &&
                                wabaDto.WabaProductCatalog.FacebookProductCatalogId == message.Order.CatalogId)
                            {
                                whatsappCloudApiOrderDetailObject.CatalogName =
                                    wabaDto.WabaProductCatalog.FacebookProductCatalogName;
                            }

                            try
                            {
                                var fetchedProductItems =
                                    await _whatsappCloudApiProductCatalogService.GetProductCatalogProductItemsAsync(
                                        companyId,
                                        config.MessagingHubWabaId,
                                        false);

                                var containerName = await _appDbContext.ConfigStorageConfigs
                                    .Where(x => x.CompanyId == companyId).Select(x => x.ContainerName)
                                    .FirstOrDefaultAsync();

                                foreach (var productItem in whatsappCloudApiOrderDetailObject.ProductItems)
                                {
                                    var fetchedItem = fetchedProductItems.FirstOrDefault(
                                        x => x.RetailerId == productItem.ProductRetailerId);
                                    productItem.Name = fetchedItem.Name;
                                    productItem.Availability = fetchedItem.Availability;
                                    productItem.Inventory = fetchedItem.Inventory;

                                    if (!string.IsNullOrEmpty(fetchedItem.ImageUrl))
                                    {
                                        productItem.ImageUrl = fetchedItem.ImageUrl;

                                        var uploadExtendedMessageFileByUrlResponse =
                                            await _extendedMessageFileService
                                                .UploadExtendedMessageFileByUrlAsync(
                                                    companyId,
                                                    ChannelTypes.WhatsappCloudApi,
                                                    ExtendedMessageType.WhatsappCloudApiOrderMessage,
                                                    containerName,
                                                    BlobUploadPathNameBuilder.GetExtendedMessagePayloadFilePath(
                                                        fetchedItem.Name),
                                                    fetchedItem.ImageUrl,
                                                    fetchedItem.Name);

                                        productItem.ImageUrl = uploadExtendedMessageFileByUrlResponse.Url;
                                    }

                                    productItem.Description = fetchedItem.Description;
                                }
                            }
                            catch (Exception ex)
                            {
                                _logger.LogError(
                                    ex,
                                    "Error occured when getting product item for order webhook {MessageOrderPayload} for phone number id {MessagingHubPhoneNumberId} {ExceptionString}",
                                    JsonConvert.SerializeObject(message.Order),
                                    messagingHubPhoneNumberId,
                                    ex.ToString());
                            }
                        }

                        conversationMessage.MessageType = "order";
                        conversationMessage.MessageContent = message.Order.Text;
                        conversationMessage.ExtendedMessagePayload = new ExtendedMessagePayload();

                        conversationMessage.ExtendedMessagePayload.SetExtendedMessagePayloadDetailWithType(
                            new ExtendedMessagePayloadDetail
                            {
                                WhatsappCloudApiOrderObject = whatsappCloudApiOrderDetailObject
                            });

                        if (config.ProductCatalogSetting is { HasEnabledProductCatalog: true } &&
                            config.ProductCatalogSetting is { HasEnabledAutoSendStripePaymentUrl: true } &&
                            conversationMessage.ExtendedMessagePayload is
                                { ExtendedMessagePayloadDetail.WhatsappCloudApiOrderObject: { } } &&
                            conversationMessage.ExtendedMessagePayload.ExtendedMessagePayloadDetail
                                .WhatsappCloudApiOrderObject.ProductItems.Any())
                        {
                            BackgroundJob.Enqueue<IWhatsappCloudApiProductCatalogService>(
                                x => x.SendPaymentLinkFromOrderMessageAsync(
                                    conversationMessage.ExtendedMessagePayload.ExtendedMessagePayloadDetail
                                        .WhatsappCloudApiOrderObject.ProductItems,
                                    message.Id));
                        }

                        break;
                    case WhatsappCloudApiWebhookMessageTypeConst.reaction:
                        conversationMessage.MessageType = "reaction";
                        conversationMessage.MessageContent = message.Reaction.Emoji;
                        conversationMessage.QuotedMsgId = message.Reaction.MessageId;
                        conversationMessage.ExtendedMessagePayload = new ExtendedMessagePayload();

                        conversationMessage.ExtendedMessagePayload.SetExtendedMessagePayloadDetailWithType(
                            new ExtendedMessagePayloadDetail
                            {
                                WhatsappCloudApiReactionReply = message.Reaction
                            });

                        break;
                    case "request_welcome":
                        conversationMessage.MessageType = "request_welcome";

                        break;
                    default:
                        conversationMessage.MessageType = "text";
                        conversationMessage.MessageContent = "<Unsupported Message Type>";

                        break;
                }

                _applicationInsightsTelemetryTracer.TraceEvent(
                    TraceEventNames.MessageReceived,
                    new Dictionary<string, string>()
                    {
                        {
                            "channel_type", ChannelTypes.WhatsappCloudApi
                        },
                        {
                            "channel_message_type", message.Type
                        },
                        {
                            "message_id", message.Id
                        },
                        {
                            "message_type", conversationMessage.MessageType
                        },
                    });

                IList<ConversationMessage> receivedMessages;

                if (messageFile != null)
                {
                    receivedMessages = await _conversationMessageService.SendFileMessageByFBURL(
                        conversation,
                        conversationMessage,
                        new List<FileURLMessage>()
                        {
                            messageFile
                        });
                }
                else
                {
                    receivedMessages = await _conversationMessageService.SendMessage(
                        conversation,
                        conversationMessage);
                }

                if (myLock != null)
                {
                    await _lockService.ReleaseLockAsync(myLock);
                }

                var receivedMessage = receivedMessages.FirstOrDefault();

                if (!tryRunOptin || receivedMessage == null)
                {
                    continue;
                }

                try
                {
                    var targetedUndeliveredMessage =
                        await _conversationMessageService.GetOptInReplyTargetUndeliveredMessage(receivedMessage);

                    if (targetedUndeliveredMessage != null)
                    {
                        BackgroundJob.Enqueue<IConversationMessageService>(
                            x => x.SendWhatsappCloudApiReadMoreMessage(
                                targetedUndeliveredMessage.CompanyId,
                                targetedUndeliveredMessage.ConversationId,
                                config.Id,
                                targetedUndeliveredMessage.Id));
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(
                        ex,
                        "Handle WhatsappCloudApiWebhook error: {CompanyId} {MessagingHubPhoneNumberId} {ExceptionMessage} {WebhookPayload}",
                        companyId,
                        messagingHubPhoneNumberId,
                        ex.Message,
                        webhookPayload);

                    _applicationInsightsTelemetryTracer.TraceEvent(
                        TraceEventNames.HangfireBackgroundJobEnqueueFailed,
                        new Dictionary<string, string>()
                        {
                            {
                                "channel_type", ChannelTypes.WhatsappCloudApi
                            }
                        });

                    throw;
                }

                // if (message.Type == WhatsappCloudApiWebhookMessageTypeConst.Button && message.Button.Payload != null && sentMessages is { Count : > 0 } )
                // {
                //     var sendMessage = sentMessages.First();
                //     await _whatsappTemplateQuickReplyCallbackService.TryEnqueueCallbackActionAsync(sendMessage.CompanyId, sendMessage.QuotedMsgId, sendMessage.MessageContent, sendMessage.Id);
                // }
            }
        }

        // // Message Status
        if (webhookMessagePayload.Statuses != null)
        {
            foreach (var status in webhookMessagePayload.Statuses)
            {
                var conversationMessage = await _appDbContext.ConversationMessages
                    .Include(x => x.ExtendedMessagePayload)
                    .Include(x => x.UploadedFiles)
                    .FirstOrDefaultAsync(x => x.MessageUniqueID == status.Id && x.CompanyId == companyId);

                if (conversationMessage == null)
                {
                    if (!string.IsNullOrEmpty(status.BizOpaqueCallbackData) &&
                        status.BizOpaqueCallbackData.TryGetQueryStringFieldValue(
                            "sleekflow_core_message_id",
                            out var sleekflowCoreMessageId) &&
                        long.TryParse(sleekflowCoreMessageId, out var messageId))
                    {
                        conversationMessage = await _appDbContext.ConversationMessages
                            .Include(x => x.ExtendedMessagePayload)
                            .Include(x => x.UploadedFiles)
                            .FirstOrDefaultAsync(x => x.Id == messageId && x.CompanyId == companyId);

                        if (conversationMessage != null)
                        {
                            conversationMessage.MessageUniqueID = status.Id;
                            await _appDbContext.SaveChangesAsync();
                        }
                    }

                    if (conversationMessage == null)
                    {
                        throw new InvalidOperationException($"No conversation message found");
                    }
                }

                // Webhook performance check
                if (!string.IsNullOrEmpty(status.Timestamp))
                {
                    var time = MyDateTimeUtil.UnixTimeStampToDateTimeUTC(long.Parse(status.Timestamp) * 1000);
                    var timeDiff = DateTime.UtcNow - time;

                    if (timeDiff > TimeSpan.FromMinutes(1))
                    {
                        _logger.LogWarning(
                            "[Cloud API Webhook] Status is delayed over {TimeDiff} seconds. Message id {MessageId}, Status {Status}, Company Id {Company}",
                            timeDiff.TotalSeconds,
                            status.Id,
                            status.Status,
                            companyId);
                    }
                }

                var initialMessageStatus = conversationMessage.Status;

                switch (status.Status)
                {
                    case WhatsappCloudApiMessageStatusConst.sent:
                        if (conversationMessage.Status is MessageStatus.Sending or MessageStatus.Failed)
                        {
                            conversationMessage.Status = MessageStatus.Sent;
                        }
                        else
                        {
                            continue;
                        }

                        break;
                    case WhatsappCloudApiMessageStatusConst.delivered:
                        if (conversationMessage.Status is MessageStatus.Read)
                        {
                            continue;
                        }

                        conversationMessage.Status = MessageStatus.Received;

                        break;
                    case WhatsappCloudApiMessageStatusConst.read:
                        if (conversationMessage.Status is MessageStatus.Read or MessageStatus.Deleted)
                        {
                            continue;
                        }

                        conversationMessage.Status = MessageStatus.Read;

                        break;
                    case WhatsappCloudApiMessageStatusConst.failed:
                        conversationMessage.Status = MessageStatus.Failed;

                        if (status.Errors != null && status.Errors.Any())
                        {
                            conversationMessage.ChannelStatusMessage = string.Join(
                                ';',
                                status.Errors.Select(
                                        x =>
                                            $"{x.Code} - {x.Details}{(!string.IsNullOrWhiteSpace(x.Details) ? " " : null)}{(!string.IsNullOrWhiteSpace(x.Title) ? x.Title : null)}")
                                    .ToList());

                            conversationMessage.Metadata ??= new Dictionary<string, object>();

                            conversationMessage.Metadata.Add(
                                "errors",
                                status.Errors.Select(
                                    x => new ConversationMessageError
                                    {
                                        Code = x.Code.ToString(),
                                        Message =
                                            $"{x.Details}{(!string.IsNullOrWhiteSpace(x.Details) ? " " : null)}{(!string.IsNullOrWhiteSpace(x.Title) ? x.Title : null)}",
                                        InnerError = x
                                    }).ToList());
                        }

                        if (config.IsOptInEnable && status.Errors.Any(x => x.Code == 131047))
                        {
                            conversationMessage.Status = MessageStatus.Undelivered;

                            await _conversationMessageService.SendWhatsappCloudApiOptInButtonMessage(
                                conversationMessage.CompanyId,
                                conversationMessage.ConversationId,
                                config.Id,
                                conversationMessage.Id);
                        }

                        break;
                    case WhatsappCloudApiMessageStatusConst.deleted:
                        conversationMessage.Status = MessageStatus.Deleted;

                        break;
                }

                _logger.LogInformation(
                    "Cloud Message Statues Update: {MessageUniqueID}, {MessageStatus}",
                    conversationMessage.MessageUniqueID,
                    status.Status);

                // DEVS - 676 Ads Click to Whatsapp: Add the conversation metadata object to the message for displaying 24 hr messaging windows
                if (status.Conversation != null)
                {
                    if (status.Status == WhatsappCloudApiMessageStatusConst.sent)
                    {
                        await _conversationService.UpdateConversationMetadataAsync(
                            companyId,
                            conversationMessage.ConversationId,
                            $"whatsappcloudapi:conversation:{config.WhatsappPhoneNumber}",
                            status.Conversation);
                    }

                    if (conversationMessage.Metadata == null)
                    {
                        conversationMessage.Metadata = new Dictionary<string, object>()
                        {
                            {
                                "whatsappcloudapi:conversation", status.Conversation
                            }
                        };
                    }
                    else
                    {
                        conversationMessage.Metadata["whatsappcloudapi:conversation"] = status.Conversation;
                    }

                    _appDbContext.Entry(conversationMessage).Property(x => x.Metadata)
                        .IsModified = true;
                }

                if (status.Pricing != null)
                {
                    if (conversationMessage.Metadata == null)
                    {
                        conversationMessage.Metadata = new Dictionary<string, object>()
                        {
                            {
                                "whatsappcloudapi:pricing", status.Pricing
                            }
                        };
                    }
                    else
                    {
                        conversationMessage.Metadata["whatsappcloudapi:pricing"] = status.Pricing;
                    }

                    _appDbContext.Entry(conversationMessage).Property(x => x.Metadata)
                        .IsModified = true;
                }

                _applicationInsightsTelemetryTracer.TraceEvent(
                    TraceEventNames.MessageStatusChanged,
                    new Dictionary<string, string>
                    {
                        {
                            "channel_type", ChannelTypes.WhatsappCloudApi
                        },
                        {
                            "channel_status_type", status.Type
                        },
                        {
                            "status_type", conversationMessage.Status.ToString()
                        },
                        {
                            "message_id", status.Id
                        },
                    });

                await _appDbContext.SaveChangesAsync();
                await _signalRService.SignalROnMessageStatusChanged(conversationMessage);

                if (conversationMessage.Status != initialMessageStatus)
                {
                    var conversation = await _appDbContext.Conversations
                        .AsNoTracking()
                        .FirstOrDefaultAsync(x => x.Id == conversationMessage.ConversationId);

                    if (conversation is not null)
                    {
                        await _userProfileHooks.OnMessageStatusUpdatedAsync(
                            conversation.CompanyId,
                            conversation.UserProfileId,
                            conversation,
                            conversationMessage);
                    }
                }
            }
        }
    }
}