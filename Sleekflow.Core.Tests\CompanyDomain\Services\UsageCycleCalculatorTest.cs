using System.Globalization;
using NSubstitute;
using Travis_backend.CompanyDomain.Services;

namespace Sleekflow.Core.Tests.CompanyDomain.Services;

public class UsageCycleCalculatorTest
{
    [Test, Description("Test GetUsageCycle for DateTime")]
    // normal cases
    [TestCase("2000-01-10T00:00", "2000-05-10T00:00", "2000-03-05T10:00", "2000-02-10T00:00", "2000-03-10T00:00")]
    [TestCase("2000-01-10T00:00", "2000-05-10T00:00", "2000-03-11T10:00", "2000-03-10T00:00", "2000-04-10T00:00")]
    // across year
    [TestCase("2000-01-10T00:00", "2001-05-10T00:00", "2001-01-05T10:00", "2000-12-10T00:00", "2001-01-10T00:00")]
    [TestCase("2000-01-10T00:00", "2001-05-10T00:00", "2001-01-11T10:00", "2001-01-10T00:00", "2001-02-10T00:00")]
    // start day is the last day of the month
    [TestCase("2000-01-31T00:00", "2000-06-30T00:00", "2000-03-05T10:00", "2000-02-29T00:00", "2000-03-31T00:00")]
    [TestCase("2000-01-31T00:00", "2000-06-30T00:00", "2000-01-31T10:00", "2000-01-31T00:00", "2000-02-29T00:00")]
    [TestCase("2000-01-31T00:00", "2001-06-30T00:00", "2000-12-20T10:00", "2000-11-30T00:00", "2000-12-31T00:00")]
    [TestCase("2000-01-31T00:00", "2001-06-30T00:00", "2001-01-20T10:00", "2000-12-31T00:00", "2001-01-31T00:00")]
    [TestCase("2000-01-31T00:00", "2001-06-30T00:00", "2001-02-28T10:00", "2001-02-28T00:00", "2001-03-31T00:00")]
    public void Test_GetUsageCycleDateTime_Success(string periodStart, string periodEnd, string now, string expectedFrom, string expectedTo)
    {
        //// Arrange
        var timeProvider = Substitute.For<TimeProvider>();
        timeProvider.GetUtcNow().Returns(x => ParseDate(now));

        //// Act
        var calculator = new UsageCycleCalculator(timeProvider);
        var (actualFrom, actualTo) = calculator.GetUsageCycle(ParseDate(periodStart), ParseDate(periodEnd));

        //// Assert
        Assert.That(actualFrom, Is.EqualTo(ParseDate(expectedFrom)));
        Assert.That(actualTo, Is.EqualTo(ParseDate(expectedTo)));
    }

    private static DateTime ParseDate(string dateString)
    {
        return DateTime.ParseExact(dateString, "yyyy-MM-ddTHH:mm", CultureInfo.InvariantCulture);
    }
}