﻿using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AutoMapper;
using Hangfire;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Travis_backend.AccountAuthenticationDomain.Models;
using Travis_backend.Cache;
using Travis_backend.CommonDomain.ViewModels;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.CompanyDomain.Services;
using Travis_backend.ConversationServices;
using Travis_backend.Database;
using Travis_backend.Enums;
using Travis_backend.Extensions;
using Travis_backend.FlowHubs;
using Travis_backend.MessageDomain.ViewModels;


namespace Travis_backend.Controllers.CompanyManagementControllers
{
    [Authorize]
    [Route("Company/[controller]")]
    public class TagsController : Controller
    {
        private readonly ApplicationDbContext _appDbContext;
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly SignInManager<ApplicationUser> _signInManager;
        private readonly IMapper _mapper;
        private readonly ILogger _logger;
        private readonly IConfiguration _configuration;
        private readonly ICompanyService _companyService;
        private readonly ICompanyInfoCacheService _companyInfoCacheService;
        private readonly ICoreService _coreService;
        private readonly IUserProfileLabelHooks _userProfileLabelHooks;

        public TagsController(
            ApplicationDbContext appDbContext,
            UserManager<ApplicationUser> userManager,
            SignInManager<ApplicationUser> signInManager,
            IMapper mapper,
            IConfiguration configuration,
            ILogger<TagsController> logger,
            ICompanyService companyService,
            ICompanyInfoCacheService companyInfoCacheService,
            ICoreService coreService,
            IUserProfileLabelHooks userProfileLabelHooks)
        {
            _userManager = userManager;
            _signInManager = signInManager;
            _appDbContext = appDbContext;
            _mapper = mapper;
            _configuration = configuration;
            _logger = logger;
            _companyService = companyService;
            _companyInfoCacheService = companyInfoCacheService;
            _coreService = coreService;
            _userProfileLabelHooks = userProfileLabelHooks;
        }

        [HttpGet]
        public async Task<IActionResult> GetCompanyCompanyHashTags([FromQuery] HashTagType? hashTagType = null)
        {
            var companyUser = await _coreService.GetCompanyStaff(
                await _userManager.GetUserAsync(User));

            if (companyUser == null)
            {
                return Unauthorized();
            }

            companyUser.Company.CompanyHashtags = await _appDbContext.CompanyDefinedHashtags
                .WhereIf(
                    hashTagType != null,
                    x => x.HashTagType == hashTagType.Value)
                .Where(x => x.CompanyId == companyUser.CompanyId)
                .ToListAsync(HttpContext.RequestAborted);

            var companyHashTags = _mapper.Map<List<CompanyHashtagResponse>>(companyUser.Company.CompanyHashtags);

            var conversationHashTagCountDict = await _appDbContext.ConversationHashtags
                .Where(
                    x => _appDbContext.CompanyDefinedHashtags
                        .Where(y =>
                            y.CompanyId == companyUser.CompanyId
                            && (!hashTagType.HasValue || y.HashTagType == hashTagType.Value))
                        .Select(y => y.Id)
                        .Contains(x.Hashtag.Id))
                .GroupBy(x => x.Hashtag.Id)
                .ToDictionaryAsync(
                    g => g.Key,
                    g => g.Count());

            companyHashTags.ForEach(x =>
            {
                x.Count = conversationHashTagCountDict.TryGetValue(x.Id, out var count) ? count : 0;
            });

            companyHashTags = companyHashTags
                .OrderBy(x => x.HashTagColor)
                .ThenBy(x => x.Hashtag)
                .ThenByDescending(x => x.Count)
                .ToList();

            return Ok(companyHashTags);

        }

        [HttpPost]
        public async Task<IActionResult> AddCompanyHashTags(
            [FromBody]
            List<ConversationHashtagViewModel> conversationTagViewModels)
        {
            var companyUser = await _coreService.GetCompanyStaff(
                await _userManager.GetUserAsync(User));

            if (companyUser == null)
            {
                return Unauthorized();
            }

            var hashTagChanges = new List<HashTagChange>();
            var modifiedHashtags = new List<CompanyHashtag>();

            foreach (var conversationTagVm in conversationTagViewModels)
            {
                if (string.IsNullOrEmpty(conversationTagVm.Hashtag))
                {
                    continue;
                }

                _logger.LogInformation(
                    "Creating company label, CompanyId: {CompanyId} StaffId: {StaffId} Removed label: {HashtagNormalized}",
                    companyUser.CompanyId,
                    companyUser.IdentityId,
                    conversationTagVm.HashtagNormalized);

                var companyHashTag = await _appDbContext.CompanyDefinedHashtags
                    .FirstOrDefaultAsync(
                        x =>
                            x.CompanyId == companyUser.CompanyId
                            && (x.Id == conversationTagVm.Id
                                || x.Hashtag.ToLower() == conversationTagVm.HashtagNormalized));

                if (companyHashTag is null)
                {
                    companyHashTag = new CompanyHashtag
                    {
                        CompanyId = companyUser.CompanyId,
                        Hashtag = conversationTagVm.Hashtag,
                        HashTagType = conversationTagVm.HashTagType
                    };

                    _appDbContext.CompanyDefinedHashtags.Add(companyHashTag);
                }

                if (conversationTagVm.HashTagColor.HasValue)
                {
                    companyHashTag.HashTagColor = conversationTagVm.HashTagColor.Value;
                }

                var isDuplicateExistingHashTag = await _appDbContext.CompanyDefinedHashtags
                    .CountAsync(
                        x =>
                            x.CompanyId == companyUser.CompanyId
                            && x.Hashtag.ToLower() == conversationTagVm.HashtagNormalized
                            && x.Id != companyHashTag.Id) > 0;

                if (isDuplicateExistingHashTag)
                {
                    return BadRequest(
                        new ResponseViewModel
                        {
                            message = "Duplicated"
                        });
                }

                if (!string.IsNullOrWhiteSpace(conversationTagVm.Id) &&
                    !string.Equals(conversationTagVm.Hashtag, companyHashTag.Hashtag))
                {
                    hashTagChanges.Add(new (companyHashTag.Hashtag, conversationTagVm.Hashtag));

                    modifiedHashtags.Add(companyHashTag);
                }

                companyHashTag.Hashtag = conversationTagVm.Hashtag;
            }

            await _appDbContext.SaveChangesAsync();

            if (hashTagChanges.Count > 0)
            {
                BackgroundJob.Enqueue<IAutomationRuleService>(
                    svc =>
                        svc.OnCompanyHashTagsChangedAsync(
                            companyUser.CompanyId,
                            hashTagChanges,
                            HashTagOperationType.Update));

                foreach (var modifiedHashTag in modifiedHashtags)
                {
                    await _userProfileLabelHooks.OnUserProfileLabelCreatedAsync(
                        companyUser.CompanyId,
                        modifiedHashTag);
                }
            }

            var companyHashTags = _mapper.Map<List<CompanyHashtagResponse>>(
                await _appDbContext.CompanyDefinedHashtags
                    .Where(x => x.CompanyId == companyUser.CompanyId)
                    .ToListAsync());

            var conversationHashTagCountDict = await _appDbContext.ConversationHashtags
                .Where(
                    x => _appDbContext.CompanyDefinedHashtags
                        .Where(y => y.CompanyId == companyUser.CompanyId)
                        .Select(y => y.Id)
                        .Contains(x.Hashtag.Id))
                .GroupBy(x => x.Hashtag.Id)
                .ToDictionaryAsync(
                    g => g.Key,
                    g => g.Count());

            companyHashTags.ForEach(x =>
            {
                x.Count = conversationHashTagCountDict.TryGetValue(x.Id, out var count) ? count : 0;
            });

            companyHashTags = companyHashTags
                .OrderBy(x => x.HashTagColor)
                .ThenBy(x => x.Hashtag)
                .ThenByDescending(x => x.Count)
                .ToList();

            await _companyInfoCacheService.RemoveCompanyInfoCache(companyUser.CompanyId);

            return Ok(companyHashTags);

        }

        [HttpDelete]
        public async Task<IActionResult> DeleteCompanyHashTags(
            [FromBody] List<ConversationHashtagViewModel> conversationTagViewModels)
        {
            var companyUser = await _coreService.GetCompanyStaff(
                await _userManager.GetUserAsync(User));

            if (companyUser is not {RoleType: StaffUserRole.Admin})
            {
                _logger.LogInformation(
                    "Permission denied to remove company label, CompanyId: {CompanyId}, StaffId: {StaffId}, Staff Role: {StaffRole}",
                    companyUser.CompanyId,
                    companyUser.IdentityId,
                    companyUser.RoleType);
                return Unauthorized();
            }

            companyUser.Company.CompanyHashtags = await _appDbContext.CompanyDefinedHashtags
                .Where(x => x.CompanyId == companyUser.CompanyId)
                .ToListAsync();

            var removedHashTags = new List<CompanyHashtag>();

            foreach (var userProfileCustomField in conversationTagViewModels)
            {
                _logger.LogWarning(
                    "Deleting company label, CompanyId: {CompanyId} StaffId: {StaffId} Removed label: {HashtagNormalized}",
                    companyUser.CompanyId,
                    companyUser.IdentityId,
                    userProfileCustomField.HashtagNormalized);

                var companyHashTag = companyUser.Company.CompanyHashtags
                    .FirstOrDefault(x => x.Hashtag.ToLower() == userProfileCustomField.HashtagNormalized);

                if (companyHashTag is not null)
                {
                    _appDbContext.ConversationHashtags
                        .RemoveRange(
                            _appDbContext.ConversationHashtags
                                .Where(x => x.Hashtag.Id == companyHashTag.Id));

                    _appDbContext.CompanyDefinedHashtags.Remove(companyHashTag);
                    companyUser.Company.CompanyHashtags.Remove(companyHashTag);

                    removedHashTags.Add(companyHashTag);

                    var topics = await _appDbContext.FacebookOtnTopics
                        .Where(x => ((string) (object) x.HashTagIds).Contains(companyHashTag.Id))
                        .ToListAsync();

                    foreach (var topic in topics)
                    {
                        topic.HashTagIds.Remove(companyHashTag.Id);
                        _appDbContext.Entry(topic).Property(x => x.HashTagIds).IsModified = true;
                    }

                    await _appDbContext.SaveChangesAsync();
                }
            }

            BackgroundJob.Enqueue<IAutomationRuleService>(
                svc =>
                    svc.OnCompanyHashTagsChangedAsync(
                        companyUser.CompanyId,
                        conversationTagViewModels
                            .Select(x =>
                                new HashTagChange(x.Hashtag, string.Empty))
                            .ToList(),
                        HashTagOperationType.Delete));

            foreach (var removedHashtag in removedHashTags)
            {
                await _userProfileLabelHooks.OnUserProfileLabelDeletedAsync(
                    companyUser.CompanyId,
                    removedHashtag);
            }

            await _companyInfoCacheService.RemoveCompanyInfoCache(companyUser.CompanyId);

            var companyHashTags = _mapper.Map<List<CompanyHashtagResponse>>(companyUser.Company.CompanyHashtags);

            var conversationHashTagCountDict = await _appDbContext.ConversationHashtags
                .Where(
                    x => _appDbContext.CompanyDefinedHashtags
                        .Where(y => y.CompanyId == companyUser.CompanyId)
                        .Select(y => y.Id)
                        .Contains(x.Hashtag.Id))
                .GroupBy(x => x.Hashtag.Id)
                .ToDictionaryAsync(
                    g => g.Key,
                    g => g.Count());

            companyHashTags.ForEach(x =>
            {
                x.Count = conversationHashTagCountDict.TryGetValue(x.Id, out var count) ? count : 0;
            });

            companyHashTags = companyHashTags
                .OrderBy(x => x.HashTagColor)
                .ThenBy(x => x.Hashtag)
                .ThenByDescending(x => x.Count)
                .ToList();

            return Ok(companyHashTags);

        }
    }
}