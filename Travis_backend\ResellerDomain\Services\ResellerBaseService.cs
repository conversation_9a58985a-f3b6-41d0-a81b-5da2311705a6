using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.Constants;
using Travis_backend.Database;
using Travis_backend.Enums;
using Travis_backend.ResellerDomain.Constants;
using Travis_backend.ResellerDomain.Models;
using Travis_backend.SubscriptionPlanDomain.Models;

namespace Travis_backend.ResellerDomain.Services;

public interface IResellerBaseService
{
    Dictionary<string, string> InitialiseResellerSubscriptionPlanConfig(string countryTier);

    string SetTransactionAction(SubscriptionPlan subscriptionPlan);

    Task<ResellerCompanyProfile> CheckAndUpdateResellerSubscriptionPlanConfig(
        ResellerCompanyProfile resellerCompanyProfile);

    bool ValidatePassword(string password);

    Task SetSubscriptionPlanMaximumUsage(Company company);

    Task AddResellerActivityLog(ResellerActivityLog resellerActivityLog);

    Task<ResponseWrapper> AddResellerStaffsToClientCompanies(
        ResellerCompanyProfile resellerCompanyProfile,
        ResellerStaff resellerStaff = null,
        Company clientCompany = null);
}

public class ResellerBaseService : IResellerBaseService
{
    private readonly ApplicationDbContext _appDbContext;
    private readonly ILogger<ResellerBaseService> _logger;

    public ResellerBaseService(ApplicationDbContext appDbContext, ILogger<ResellerBaseService> logger)
    {
        _appDbContext = appDbContext;
        _logger = logger;
    }

    public Dictionary<string, string> InitialiseResellerSubscriptionPlanConfig(string countryTier)
    {
        if (!ResellerCountryTier.GetAllCountryTiers.Contains(countryTier))
        {
            countryTier = ResellerCountryTier.Tier1;
        }

        var countryTierId = "country" + countryTier.ToLower();
        var additionalContactsNumber = "2000";

        if (countryTier is ResellerCountryTier.Tier4 or ResellerCountryTier.Tier5)
        {
            additionalContactsNumber = "5000";
        }

        var resellerSubscriptionPlanConfig = new Dictionary<string, string>
        {
            {
                ResellerSubscriptionPlanIds.SubscriptionFreePlanId, "sleekflow_v10_startup"
            },
            {
                ResellerSubscriptionPlanIds.SubscriptionProPlanId, $"sleekflow_v10_{countryTierId}_pro_monthly_usd"
            },
            {
                ResellerSubscriptionPlanIds.SubscriptionPremiumPlanId,
                $"sleekflow_v10_{countryTierId}_premium_monthly_usd"
            },
            {
                ResellerSubscriptionPlanIds.AddOnAgentProPlanId, $"sleekflow_v10_{countryTierId}_agent_pro_monthly_usd"
            },
            {
                ResellerSubscriptionPlanIds.AddOnAgentPremiumPlanId,
                $"sleekflow_v10_{countryTierId}_agent_premium_monthly_usd"
            },
            {
                ResellerSubscriptionPlanIds.AddOnCustomerContactProPlanId,
                $"sleekflow_v10_{countryTierId}_additional_{additionalContactsNumber}_contacts_pro_monthly_usd"
            },
            {
                ResellerSubscriptionPlanIds.AddOnCustomerContactPremiumPlanId,
                $"sleekflow_v10_{countryTierId}_additional_{additionalContactsNumber}_contacts_premium_monthly_usd"
            },
            {
                ResellerSubscriptionPlanIds.AddOnWhatsAppPhoneNumberProPlanId,
                $"sleekflow_v10_{countryTierId}_whatsapp_phone_number_monthly_usd"
            },
            {
                ResellerSubscriptionPlanIds.AddOnWhatsAppPhoneNumberPremiumPlanId,
                $"sleekflow_v10_{countryTierId}_whatsapp_phone_number_monthly_usd"
            }
        };

        return resellerSubscriptionPlanConfig;
    }

    public string SetTransactionAction(SubscriptionPlan subscriptionPlan)
    {
        var action = string.Empty;

        if (subscriptionPlan.Id.Contains("yearly"))
        {
            action += "Yearly";
        }
        else
        {
            action += "Monthly";
        }

        switch (subscriptionPlan.SubscriptionTier)
        {
            case SubscriptionTier.Free:
                action += " Startup Plan";
                break;
            case SubscriptionTier.Pro:
                action += " Pro Plan";
                break;
            case SubscriptionTier.Premium:
                action += " Premium Plan";
                break;
            case SubscriptionTier.Enterprise:
                action += " Enterprise Plan";
                break;
            case SubscriptionTier.AddOn:
            case SubscriptionTier.Agent:
                if (ValidSubscriptionPlan.AgentPlan.Contains(subscriptionPlan.Id))
                {
                    action += " Additional Staff Log-in Add-on";
                }
                else if (ValidSubscriptionPlan.AdditionalContactAddOns.Contains(subscriptionPlan.Id))
                {
                    action += $" Additional {subscriptionPlan.MaximumContact} Contacts Add-on";
                }
                else if (ValidSubscriptionPlan.WhatsAppPhoneNumberAddOns.Contains(subscriptionPlan.Id))
                {
                    action += " Additional WhatsApp Number Add-on";
                }
                else
                {
                    action += " Additional Add-on";
                }

                break;
            case SubscriptionTier.MarkUpLog:
            default:
                break;
        }

        return action;
    }

    public async Task<ResellerCompanyProfile> CheckAndUpdateResellerSubscriptionPlanConfig(
        ResellerCompanyProfile resellerCompanyProfile)
    {
        var isNeedUpdate = false;

        if (string.IsNullOrWhiteSpace(resellerCompanyProfile.CountryTier))
        {
            resellerCompanyProfile.CountryTier = ResellerCountryTier.Tier1;
            isNeedUpdate = true;
        }

        var defaultResellerSubscriptionPlanConfig = InitialiseResellerSubscriptionPlanConfig(
            resellerCompanyProfile.CountryTier);

        Dictionary<string, string> updatedSubscriptionPlanConfig;

        if (resellerCompanyProfile.ResellerSubscriptionPlanConfig == null ||
            resellerCompanyProfile.ResellerSubscriptionPlanConfig.Count == 0)
        {
            updatedSubscriptionPlanConfig = defaultResellerSubscriptionPlanConfig;
            isNeedUpdate = true;
        }
        else
        {
            updatedSubscriptionPlanConfig = resellerCompanyProfile.ResellerSubscriptionPlanConfig;
        }

        foreach (var subscriptionPlanId in ResellerSubscriptionPlanIds.GetAllSubscriptionPlanIds)
        {
            if (updatedSubscriptionPlanConfig.TryGetValue(subscriptionPlanId, out var planId))
            {
                if (!string.IsNullOrWhiteSpace(planId))
                {
                    continue;
                }

                updatedSubscriptionPlanConfig[subscriptionPlanId] = defaultResellerSubscriptionPlanConfig[subscriptionPlanId];
                isNeedUpdate = true;
            }
            else
            {
                updatedSubscriptionPlanConfig.Add(
                    subscriptionPlanId,
                    defaultResellerSubscriptionPlanConfig[subscriptionPlanId]);
                isNeedUpdate = true;
            }
        }

        if (!isNeedUpdate)
        {
            return resellerCompanyProfile;
        }

        var resellerProfileToUpdate = await _appDbContext.ResellerCompanyProfiles
            .FirstOrDefaultAsync(x => x.CompanyId == resellerCompanyProfile.CompanyId);

        resellerProfileToUpdate.CountryTier = resellerCompanyProfile.CountryTier;
        resellerProfileToUpdate.ResellerSubscriptionPlanConfig = new Dictionary<string, string>(updatedSubscriptionPlanConfig);
        resellerProfileToUpdate.UpdatedAt = DateTime.UtcNow;

        await _appDbContext.SaveChangesAsync();

        return resellerProfileToUpdate;
    }

    public bool ValidatePassword(string password)
    {
        var validationRegex = new Regex(@"^(?=.*[0-9])(?=.*[a-z])(?=.*[A-Z])(?=.*\W)(?!.* ).{8,}$");

        return validationRegex.IsMatch(password);
    }

    public async Task SetSubscriptionPlanMaximumUsage(
        Company company)
    {
        var companyToUpdate = await _appDbContext.CompanyCompanies
            .FirstOrDefaultAsync(x => x.Id == company.Id);

        companyToUpdate.MaximumAutomations = null;
        companyToUpdate.MaximumAgents = 0;
        companyToUpdate.MaximumContacts = null;
        companyToUpdate.MaximumWhAutomatedMessages = null;
        companyToUpdate.MaximumWhatsappInstance = 0;

        await _appDbContext.SaveChangesAsync();
    }

    public async Task AddResellerActivityLog(ResellerActivityLog resellerActivityLog)
    {
        if (string.IsNullOrEmpty(resellerActivityLog.ResellerCompanyProfileId))
        {
            var resellerCompanyProfileId = await _appDbContext.ResellerStaffs
                .AsNoTracking()
                .Where(x => x.IdentityId == resellerActivityLog.CreatedByUserId)
                .Select(x => x.ResellerCompanyProfileId)
                .FirstOrDefaultAsync();

            resellerActivityLog.ResellerCompanyProfileId = resellerCompanyProfileId;
        }

        await _appDbContext.ResellerActivityLogs.AddAsync(resellerActivityLog);

        await _appDbContext.SaveChangesAsync();
    }

    public async Task<ResponseWrapper> AddResellerStaffsToClientCompanies(
        ResellerCompanyProfile resellerCompanyProfile,
        ResellerStaff resellerStaff = null,
        Company clientCompany = null)
    {
        var responseWrapper = new ResponseWrapper()
        {
            IsSuccess = false
        };

        // Add all reseller staffs to a client company's user role staffs
        if (resellerStaff == null &&
            clientCompany != null)
        {
            var resellerStaffsToAdd = await _appDbContext.ResellerStaffs
                .Where(x => x.ResellerCompanyProfileId == resellerCompanyProfile.Id).Include(x => x.ProfilePicture)
                .ToListAsync();

            if (resellerStaffsToAdd.Count == 0)
            {
                responseWrapper.ErrorMsg = "Reseller Staffs Not Found In Reseller Profile";

                _logger.LogWarning(
                    "[{MethodName}] Reseller profile {ResellerCompanyProfileId} staffs Not Found",
                    nameof(AddResellerStaffsToClientCompanies),
                    resellerCompanyProfile.Id);

                return responseWrapper;
            }

            var clientCompanyStaffs = await _appDbContext.UserRoleStaffs.AsNoTracking()
                .Where(x => x.CompanyId == clientCompany.Id).ToListAsync();
            var staffListToAdd = new List<Staff>();

            foreach (var resellerStaffToAdd in resellerStaffsToAdd)
            {
                if (clientCompanyStaffs.Exists(x => x.IdentityId == resellerStaffToAdd.IdentityId))
                {
                    continue;
                }

                var staffToAdd = new Staff()
                {
                    IdentityId = resellerStaffToAdd.IdentityId,
                    CompanyId = clientCompany.Id,
                    Company = clientCompany,
                    Role = "Reseller Staff",
                    RoleType = StaffUserRole.Admin,
                    Locale = resellerStaffToAdd.Locale,
                    TimeZoneInfoId = resellerStaffToAdd.TimeZoneInfoId,
                    Position = resellerStaffToAdd.Position,
                    ProfilePicture = resellerStaffToAdd.ProfilePicture,
                    Status = StaffStatus.Active,
                    Order = clientCompanyStaffs.Exists(x => x.Order == 0) ? 1 : 0
                };
                staffListToAdd.Add(staffToAdd);
            }

            await _appDbContext.UserRoleStaffs.AddRangeAsync(staffListToAdd);
            await _appDbContext.SaveChangesAsync();

            responseWrapper.IsSuccess = true;
            responseWrapper.Data = staffListToAdd;

            return responseWrapper;
        }

        // Add reseller staff to all their client companies
        if (resellerStaff != null && clientCompany == null)
        {
            var clientCompanyProfiles = await _appDbContext.ResellerClientCompanyProfiles.Include(x => x.ClientCompany)
                .Where(x => x.ResellerCompanyProfileId == resellerCompanyProfile.Id).ToListAsync();

            if (!clientCompanyProfiles.Any())
            {
                responseWrapper.ErrorMsg = "No Client Company Profile for Reseller Profile";

                _logger.LogWarning(
                    "Reseller Staff {ResellerStaffId}'s Client Company Profiles {ResellerCompanyProfileId} Not Found",
                    resellerStaff.Id,
                    resellerCompanyProfile?.Id);

                return responseWrapper;
            }

            var staffListToAdd = new List<Staff>();
            foreach (var clientCompanyProfile in clientCompanyProfiles)
            {
                var staffToAdd = new Staff()
                {
                    IdentityId = resellerStaff.IdentityId,
                    CompanyId = clientCompanyProfile.ClientCompanyId,
                    Company = clientCompanyProfile.ClientCompany,
                    Role = "Reseller Staff",
                    RoleType = StaffUserRole.Admin,
                    Locale = resellerStaff.Locale,
                    TimeZoneInfoId = resellerStaff.TimeZoneInfoId,
                    Position = resellerStaff.Position,
                    ProfilePicture = resellerStaff.ProfilePicture,
                    Status = StaffStatus.Active,
                    Order = await _appDbContext.UserRoleStaffs.AnyAsync(
                        x => x.CompanyId == clientCompanyProfile.ClientCompanyId && x.Order == 0)
                        ? 1
                        : 0
                };
                staffListToAdd.Add(staffToAdd);
            }

            await _appDbContext.UserRoleStaffs.AddRangeAsync(staffListToAdd);
            await _appDbContext.SaveChangesAsync();

            responseWrapper.IsSuccess = true;
            responseWrapper.Data = staffListToAdd;
            return responseWrapper;
        }

        responseWrapper.IsSuccess = false;
        responseWrapper.ErrorMsg = "Something goes wrong with adding reseller staff to client company";

        _logger.LogWarning(
            "Something went wrong with adding reseller staff {ResellerStaffId} to client company {ClientCompanyId}",
            resellerStaff?.Id,
            clientCompany?.Id);

        return responseWrapper;
    }
}