using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Threading.Tasks;
using AutoMapper;
using Hangfire;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Travis_backend.Cache;
using Travis_backend.Cache.Models.CacheKeyPatterns;
using Travis_backend.ChannelDomain.Helpers;
using Travis_backend.ChannelDomain.Repositories;
using Travis_backend.ChannelDomain.ViewModels.Facebook;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.CompanyDomain.Services;
using Travis_backend.CompanyDomain.ViewModels;
using Travis_backend.Constants;
using Travis_backend.ConversationDomain.ViewModels;
using Travis_backend.ConversationServices;
using Travis_backend.ConversationServices.Models;
using Travis_backend.Database;
using Travis_backend.Extensions;
using Travis_backend.Helpers;
using Travis_backend.OpenTelemetry.Meters;
using Travis_backend.OpenTelemetry.Meters.Constants;
using Travis_backend.SignalR;

namespace Travis_backend.ChannelDomain.Services.Facebook;

public interface IFacebookChannelService
{
    Task<string> ConnectFacebookChannelAsync(
        string companyId,
        string pageId,
        string pageName,
        string pageAccessToken,
        string staffIdentityId,
        string businessIntegrationSystemUserAccessToken);

    Task RemoveFacebookChannelAsync(string companyId, string pageId);

    Task<FacebookConfigViewModel> UpdateFacebookChannelAsync(
        string companyId,
        string pageId,
        ChannelRenameViewModel channelRenameViewModel);
}

public class FacebookChannelService : IFacebookChannelService
{
    private readonly IFacebookConfigRepository _facebookConfigRepository;
    private readonly IInstagramChannelRepository _instagramChannelRepository;
    private readonly IFacebookService _facebookService;
    private readonly ICompanyUsageService _companyUsageService;
    private readonly IConfiguration _configuration;
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly ICompanyService _companyService;
    private readonly ISignalRService _signalRService;
    private readonly ICacheManagerService _cacheManagerService;
    private readonly ICompanyInfoCacheService _companyInfoCacheService;
    private readonly ApplicationDbContext _appDbContext;
    private readonly ILogger<IFacebookChannelService> _logger;
    private readonly IMapper _mapper;
    private readonly ILeadAdsServiceService _leadAdsService;
    private readonly IMetaChannelConnectionMeters _metaChannelConnectionMeters;

    public FacebookChannelService(
        IConfiguration configuration,
        IFacebookConfigRepository facebookConfigRepository,
        IInstagramChannelRepository instagramChannelRepository,
        ILogger<FacebookChannelService> logger,
        IHttpClientFactory httpClientFactory,
        ICompanyService companyService,
        ISignalRService signalRService,
        ICacheManagerService cacheManagerService,
        ICompanyInfoCacheService companyInfoCacheService,
        ApplicationDbContext appDbContext,
        IMapper mapper,
        ICompanyUsageService companyUsageService,
        IFacebookService facebookService,
        ILeadAdsServiceService leadAdsService,
        IMetaChannelConnectionMeters metaChannelConnectionMeters)
    {
        _httpClientFactory = httpClientFactory;
        _companyService = companyService;
        _configuration = configuration;
        _facebookConfigRepository = facebookConfigRepository;
        _instagramChannelRepository = instagramChannelRepository;
        _signalRService = signalRService;
        _cacheManagerService = cacheManagerService;
        _companyInfoCacheService = companyInfoCacheService;
        _appDbContext = appDbContext;
        _mapper = mapper;
        _companyUsageService = companyUsageService;
        _facebookService = facebookService;
        _logger = logger;
        _leadAdsService = leadAdsService;
        _metaChannelConnectionMeters = metaChannelConnectionMeters;
    }

    #region Connect Channel

    public async Task<string> ConnectFacebookChannelAsync(
        string companyId,
        string pageId,
        string pageName,
        string pageAccessToken,
        string staffIdentityId,
        string businessIntegrationSystemUserAccessToken)
    {
        try
        {
            return await ConnectChannelAsync(
                companyId,
                pageId,
                pageName,
                pageAccessToken,
                staffIdentityId,
                businessIntegrationSystemUserAccessToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "[{MethodName}] Company {CompanyId} An error occurred while trying to connect to a Facebook channel - PageId: {PageId}, PageName: {PageName}, PageAccessToken:{PageAccessToken}. Exception Message: {ExceptionMessage}",
                nameof(ConnectFacebookChannelAsync),
                companyId,
                pageId,
                pageName,
                pageAccessToken,
                ex.Message);

            throw;
        }
    }

    private async Task<string> ConnectChannelAsync(
        string companyId,
        string pageId,
        string pageName,
        string pageAccessToken,
        string staffIdentityId,
        string businessIntegrationSystemUserAccessToken)
    {
        // Don't check for duplicate channel for facebook channel
        // Tim gor: A use case will be channel status disconnected,
        // and the client need to reconnect again to enable the status is active

        var connectChannelType = ResolveConnectChannelType(businessIntegrationSystemUserAccessToken);
        var facebookChannel = connectChannelType switch
        {
            // Re-enable standard Facebook Login due to issues with Facebook Login for Business.
            // Note: SleekFlow app continues to use Facebook Login for Business where it is operational.
            FacebookLogin => await ConnectChannelWithFacebookLoginAsync(
                companyId,
                pageId,
                pageName,
                pageAccessToken,
                staffIdentityId),
            FacebookLoginForBusiness => await ConnectChannelWithFacebookLoginForBusinessAsync(
                companyId,
                pageId,
                pageName,
                pageAccessToken,
                staffIdentityId,
                businessIntegrationSystemUserAccessToken),
            _ => throw new NotImplementedException()
        };

        // Actions after connected to the facebook channel
        await PostChannelConnectionActionsAsync(facebookChannel);
        return await GetFacebookPageSubscriptionStatusAsync(pageId, facebookChannel.PageAccessToken);
    }

    #region Facebook Login

    private async Task<FacebookConfig> ConnectChannelWithFacebookLoginAsync(
        string companyId,
        string pageId,
        string pageName,
        string shortLivedPageAccessToken,
        string staffIdentityId)
    {
        var existingFacebookChannel = await _facebookConfigRepository.FindFacebookConfigAsync(companyId, pageId);
        var facebookChannelExists = existingFacebookChannel is not null;

        FacebookConfig facebookChannel;

        if (facebookChannelExists)
        {
            // Check if a business integration user access token exists to avoid reconnecting
            // channels already connected via Facebook Login for Business.
            if (IsFacebookChannelConnectedWithFacebookLoginForBusiness(existingFacebookChannel))
            {
                return existingFacebookChannel;
            }

            facebookChannel = await ReconnectExistingFacebookChannelWithFacebookLoginAsync(
                shortLivedPageAccessToken,
                pageName,
                existingFacebookChannel);
        }
        else
        {
            await _companyUsageService.EnsureNotExceedingChannelLimitAsync(
                companyId,
                staffIdentityId);

            facebookChannel =
                await CreateNewFacebookChannelWithFacebookLoginAsync(
                    companyId,
                    pageId,
                    pageName,
                    shortLivedPageAccessToken);
        }

        return facebookChannel;
    }

    private async Task<FacebookConfig> CreateNewFacebookChannelWithFacebookLoginAsync(
        string companyId,
        string pageId,
        string pageName,
        string shortLivedPageAccessToken)
    {
        var facebookChannel = new FacebookConfig
        {
            PageId = pageId,
            PageName = pageName,
            CompanyId = companyId,
            Name = pageName
        };

        var longLivedPageAccessToken =
            await ExchangeShortLivedAccessTokenToLongLivedAccessTokenAsync(shortLivedPageAccessToken);

        facebookChannel.PageAccessToken = longLivedPageAccessToken.AccessToken;

        if (longLivedPageAccessToken.ExpiresIn > 0)
        {
            facebookChannel.ExpireDate = DateTime.UtcNow.AddSeconds(longLivedPageAccessToken.ExpiresIn - 10);
        }

        await UpdateFacebookChannelConfigAsync(facebookChannel);

        await _facebookConfigRepository.CreateFacebookConfigAsync(facebookChannel);
        return facebookChannel;
    }

    private async Task<FacebookConfig> ReconnectExistingFacebookChannelWithFacebookLoginAsync(
        string shortLivedPageAccessToken,
        string pageName,
        FacebookConfig existingFacebookChannel)
    {
        var longLivedPageAccessToken =
            await ExchangeShortLivedAccessTokenToLongLivedAccessTokenAsync(shortLivedPageAccessToken);

        existingFacebookChannel.PageAccessToken = longLivedPageAccessToken.AccessToken;

        if (longLivedPageAccessToken.ExpiresIn > 0)
        {
            existingFacebookChannel.ExpireDate = DateTime.UtcNow.AddSeconds(longLivedPageAccessToken.ExpiresIn - 10);
        }

        existingFacebookChannel.PageName = pageName;

        await UpdateFacebookChannelConfigAsync(existingFacebookChannel);

        await _facebookConfigRepository.UpdateFacebookConfigAsync(existingFacebookChannel);

        return existingFacebookChannel;
    }

    private static bool IsFacebookChannelConnectedWithFacebookLoginForBusiness(FacebookConfig facebookConfig)
    {
        return !string.IsNullOrEmpty(facebookConfig.BusinessIntegrationSystemUserAccessToken);
    }

    #endregion

    #region Facebook Login For Business

    private async Task<FacebookConfig> ConnectChannelWithFacebookLoginForBusinessAsync(
        string companyId,
        string pageId,
        string pageName,
        string pageAccessToken,
        string staffIdentityId,
        string businessIntegrationSystemUserAccessToken)
    {
        var existingFacebookChannel = await _facebookConfigRepository.FindFacebookConfigAsync(companyId, pageId);
        var facebookChannelExists = existingFacebookChannel is not null;

        FacebookConfig facebookChannel;

        if (string.IsNullOrEmpty(businessIntegrationSystemUserAccessToken))
        {
            throw new ArgumentException(
                "Business Integration System User Access Token could not be null or empty while connecting channel with Facebook Login For Business");
        }

        var facebookBusinessId = await _facebookService.GetBusinessIdAssociatedWithPageAsync(pageId, businessIntegrationSystemUserAccessToken);

        if (facebookChannelExists)
        {
            facebookChannel = await ReconnectExistingFacebookChannelWithFacebookLoginForBusinessAsync(
                pageAccessToken,
                pageName,
                facebookBusinessId,
                businessIntegrationSystemUserAccessToken,
                existingFacebookChannel);
        }
        else
        {
            await _companyUsageService.EnsureNotExceedingChannelLimitAsync(
                companyId,
                staffIdentityId);

            facebookChannel = await CreateNewFacebookChannelWithFacebookLoginForBusinessAsync(
                companyId,
                pageId,
                pageName,
                pageAccessToken,
                facebookBusinessId,
                businessIntegrationSystemUserAccessToken);
        }

        return facebookChannel;
    }

    private async Task<FacebookConfig> CreateNewFacebookChannelWithFacebookLoginForBusinessAsync(
        string companyId,
        string pageId,
        string pageName,
        string pageAccessToken,
        string facebookBusinessId,
        string businessIntegrationSystemAccessToken)
    {
        var facebookChannel = new FacebookConfig
        {
            PageId = pageId,
            PageName = pageName,
            Name = pageName,
            CompanyId = companyId,
            PageAccessToken = pageAccessToken,
            FacebookBusinessId = facebookBusinessId,
            BusinessIntegrationSystemUserAccessToken = businessIntegrationSystemAccessToken,
        };

        await UpdateFacebookChannelConfigAsync(facebookChannel);

        await _facebookConfigRepository.CreateFacebookConfigAsync(facebookChannel);
        return facebookChannel;
    }

    private async Task<FacebookConfig> ReconnectExistingFacebookChannelWithFacebookLoginForBusinessAsync(
        string pageAccessToken,
        string pageName,
        string facebookBusinessId,
        string businessIntegrationSystemUserAccessToken,
        FacebookConfig existingFacebookChannel)
    {
        existingFacebookChannel.PageName = pageName;
        existingFacebookChannel.PageAccessToken = pageAccessToken;
        existingFacebookChannel.FacebookBusinessId = facebookBusinessId;
        existingFacebookChannel.BusinessIntegrationSystemUserAccessToken = businessIntegrationSystemUserAccessToken;

        await UpdateFacebookChannelConfigAsync(existingFacebookChannel);

        await _facebookConfigRepository.UpdateFacebookConfigAsync(existingFacebookChannel);

        return existingFacebookChannel;
    }

    #endregion

    private const string FacebookLogin = "FacebookLogin";
    private const string FacebookLoginForBusiness = "FacebookLoginForBusiness";

    private static string ResolveConnectChannelType(string businessIntegrationSystemUserAccessToken)
    {
        var facebookLoginType = businessIntegrationSystemUserAccessToken.IsNullOrEmpty()
            ? FacebookLogin
            : FacebookLoginForBusiness;

        return facebookLoginType;
    }

    private async Task PostChannelConnectionActionsAsync(
        FacebookConfig facebookConfig)
    {
        if (HasLeadAdsConnected(facebookConfig.SubscribedFields))
        {
            facebookConfig.IsV2LeadAdsConnection = true;
            await _appDbContext.SaveChangesAsync();

            RecurringJob.AddOrUpdate<ILeadAdsServiceService>(
                facebookConfig.PageId,
                x => x.AdHocLeadGenCheckPeriodicallyV2(facebookConfig.PageId),
                "*/15 * * * *");
        }

        var isSandBoxCompany = await _appDbContext.CompanySandboxes
            .AnyAsync(x => x.CompanyId == facebookConfig.CompanyId);

        if (isSandBoxCompany)
        {
            await _companyService.DeleteSandbox(facebookConfig.CompanyId);
        }

        BackgroundJob.Enqueue<IConversationMessageService>(
            x => x.FetchConversationsBackground(facebookConfig.PageId, 10, false));

        var company = await GetCompanyAsync(facebookConfig.CompanyId);

        await _signalRService.SignalROnChannelAdded(
            company,
            new ChannelSignal
            {
                ChannelName = ChannelTypes.Facebook
            });

        await _companyInfoCacheService.RemoveCompanyInfoCache(facebookConfig.CompanyId);
        var connectedFacebookWebhookCacheKeyPattern = new ConnectedFacebookWebhookCacheKeyPattern();
        await _cacheManagerService.DeleteCacheAsync(connectedFacebookWebhookCacheKeyPattern);
    }

    private async Task UpdateFacebookChannelConfigAsync(FacebookConfig facebookChannel)
    {
        facebookChannel.ChannelIdentityId = facebookChannel.PageId;

        facebookChannel.SubscribedFields = await AssignFacebookChannelSubscribedFieldsAsync(
            facebookChannel);

        await SubscribeFacebookPageAsync(
            facebookChannel.PageId,
            facebookChannel.PageAccessToken,
            facebookChannel.SubscribedFields);

        await UpdateMessengerProfileWhitelistDomainAsync(
            facebookChannel.PageAccessToken);

        facebookChannel.ConnectedDateTime = DateTime.UtcNow;
    }

    private async Task<FacebookAppAccessToken> ExchangeShortLivedAccessTokenToLongLivedAccessTokenAsync(
        string shortLivedPageAccessToken)
    {
        var clientId = _configuration.GetValue<string>("Facebook:ClientId");
        var clientSecret = _configuration.GetValue<string>("Facebook:ClientSecret");

        var exchangeShortLivedAccessTokenToLongLivedAccessTokenUri =
            FacebookAccessTokenUriBuilder.GetExchangeShortLivedAccessTokenToLongLivedAccessTokenUri(
                clientId,
                clientSecret,
                shortLivedPageAccessToken);

        var httpClient = _httpClientFactory.CreateClient(HttpClientHandlerName.Default);

        var response = await httpClient.GetAsync(exchangeShortLivedAccessTokenToLongLivedAccessTokenUri);

        if (!response.IsSuccessStatusCode)
        {
            var responseContent = await response.Content.ReadAsStringAsync();
            var errorResponse = JObject.Parse(responseContent).ToString(Formatting.None);

            var errorMessage =
                $"Failed to Exchange Short Lived Page Access Token To Long Lived Page Access Token. Error Response: {errorResponse}, Request Url: {exchangeShortLivedAccessTokenToLongLivedAccessTokenUri}";

            throw new Exception(errorMessage);
        }

        var content = await response.Content.ReadAsStringAsync();
        var tokenResponse = JsonConvert.DeserializeObject<FacebookAppAccessToken>(content);
        return tokenResponse;
    }

    private async Task<string> AssignFacebookChannelSubscribedFieldsAsync(FacebookConfig facebookChannel)
    {
        var instagramChannel = await _instagramChannelRepository.FindInstagramChannelByPageIdAsync(
            facebookChannel.CompanyId,
            facebookChannel.PageId);

        var facebookInstagramChannelShouldBeSubscribed = new FacebookInstagramChannelShouldBeSubscribed(
            true,
            instagramChannel is not null,
            HasLeadAdsConnected(facebookChannel.SubscribedFields));

        var subscribedFields = FacebookInstagramChannelSubscribedFieldsHelper
            .ResolveFacebookInstagramChannelsSubscribedFields(facebookInstagramChannelShouldBeSubscribed);

        return string.Join(",", subscribedFields);
    }

    private async Task SubscribeFacebookPageAsync(string pageId, string pageAccessToken, string subscribedFields)
    {
        var httpClient = _httpClientFactory.CreateClient(HttpClientHandlerName.Default);

        var appSubscribeFacebookPageUri =
            FacebookPageAppSubscriptionUriBuilder.GetAppSubscribeFacebookPageUri(
                pageId,
                pageAccessToken,
                subscribedFields);

        var httpResponseMessage = await httpClient.SendAsync(new HttpRequestMessage(HttpMethod.Post, appSubscribeFacebookPageUri));

        if (!httpResponseMessage.IsSuccessStatusCode)
        {
            _metaChannelConnectionMeters.IncrementCounter(MetaChannelConnectionApis.PageIdSubscribedApps, MetaChannelConnectionApiCallResults.Failure);
            var responseContent = await httpResponseMessage.Content.ReadAsStringAsync();
            var errorResponse = JObject.Parse(responseContent).ToString(Formatting.None);
            var errorMessage =
                $"Failed to subscribe the Facebook page. Error Response: {errorResponse}, Request Url: {appSubscribeFacebookPageUri}";

            throw new Exception(errorMessage);
        }

        _metaChannelConnectionMeters.IncrementCounter(
            MetaChannelConnectionApis.PageIdSubscribedApps,
            MetaChannelConnectionApiCallResults.Success);
    }

    // If sleekflow is not included in 'whitelisted_domains', this could disrupt the message flow of the webhook.
    private async Task UpdateMessengerProfileWhitelistDomainAsync(string accessToken)
    {
        var domainName = _configuration.GetValue<string>("Values:DomainName");

        var whitelistDomain = new List<string>
        {
            domainName
        };

        var properties = new
        {
            whitelisted_domains = whitelistDomain
        };

        var updateMessengerProfileUri =
            FacebookMessengerProfileUriBuilder.GetUpdateMessengerProfileUri(accessToken);

        var httpClient = _httpClientFactory.CreateClient(HttpClientHandlerName.Default);

        var response = await httpClient.PostAsJsonAsync(updateMessengerProfileUri, properties);

        if (!response.IsSuccessStatusCode)
        {
            var responseContent = await response.Content.ReadAsStringAsync();
            var errorResponse = JObject.Parse(responseContent).ToString(Formatting.None);
            var errorMessage =
                $"Failed to update the messenger profile. Error Response: {errorResponse}, Request Url: {updateMessengerProfileUri}";

            _logger.LogError(errorMessage);
        }
    }

    private async Task<string> GetFacebookPageSubscriptionStatusAsync(string pageId, string pageAccessToken)
    {
        var httpClient = _httpClientFactory.CreateClient(HttpClientHandlerName.Default);

        var getFacebookPageAppSubscriptionsStatusUri =
            FacebookPageAppSubscriptionUriBuilder.GetFacebookPageAppSubscriptionsStatusUri(pageId, pageAccessToken);

        var response = await httpClient.GetAsync(getFacebookPageAppSubscriptionsStatusUri);

        if (response.IsSuccessStatusCode)
        {
            return await response.Content.ReadAsStringAsync();
        }

        var responseContent = await response.Content.ReadAsStringAsync();
        var errorResponse = JObject.Parse(responseContent).ToString(Formatting.None);
        var errorMessage =
            $"Failed to unsubscribe the Facebook page. Error Response: {errorResponse}, Request Url: {getFacebookPageAppSubscriptionsStatusUri}";

        throw new Exception(errorMessage);
    }

    public async Task<FacebookConfigViewModel> UpdateFacebookChannelAsync(
        string companyId,
        string pageId,
        ChannelRenameViewModel channelRenameViewModel)
    {
        var facebookChannel = await _facebookConfigRepository.GetFacebookConfigAsync(
            companyId,
            pageId);

        if (!string.IsNullOrEmpty(channelRenameViewModel.Name))
        {
            facebookChannel.PageName = channelRenameViewModel.Name;
            facebookChannel.Name = channelRenameViewModel.Name;
        }

        if (channelRenameViewModel.IsShowInWidget.HasValue)
        {
            facebookChannel.IsShowInWidget =
                channelRenameViewModel.IsShowInWidget.HasValue;
        }

        await _facebookConfigRepository.UpdateFacebookConfigAsync(
            facebookChannel);

        await _companyInfoCacheService.RemoveCompanyInfoCache(companyId);

        return _mapper.Map<FacebookConfigViewModel>(facebookChannel);
    }

    private static bool HasLeadAdsConnected(string subscribedEvent)
    {
        if (string.IsNullOrWhiteSpace(subscribedEvent))
        {
            return false;
        }

        const string leadGenEvent = "leadgen";

        return subscribedEvent.Contains(leadGenEvent);
    }

    #endregion

    #region Remove Channel

    public async Task RemoveFacebookChannelAsync(string companyId, string pageId)
    {
        try
        {
            await RemoveChannelAsync(companyId, pageId);
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "[{MethodName}] Company {CompanyId} An error occurred while trying to remove a Facebook channel - PageId: {PageId}. Exception Message: {ExceptionMessage}",
                nameof(RemoveFacebookChannelAsync),
                companyId,
                pageId,
                ex.Message);

            throw;
        }
    }

    private async Task RemoveChannelAsync(string companyId, string pageId)
    {
        var facebookChannel = await _facebookConfigRepository.GetFacebookConfigAsync(
            companyId,
            pageId);

        await UnsubscribeFacebookPageAsync(
            facebookChannel.PageId,
            facebookChannel.PageAccessToken);

        await _facebookConfigRepository.DeleteFacebookConfigAsync(facebookChannel);

        var company = await GetCompanyAsync(companyId);

        await _signalRService.SignalROnChannelDeleted(
            company,
            new ChannelSignal
            {
                ChannelName = ChannelTypes.Facebook
            });

        await _companyInfoCacheService.RemoveCompanyInfoCache(companyId);

        var connectedFacebookWebhookCacheKeyPattern = new ConnectedFacebookWebhookCacheKeyPattern();
        await _cacheManagerService.DeleteCacheAsync(connectedFacebookWebhookCacheKeyPattern);
    }

    private async Task UnsubscribeFacebookPageAsync(string pageId, string pageAccessToken)
    {
        var httpClient = _httpClientFactory.CreateClient(HttpClientHandlerName.Default);

        var appUnsubscribeFacebookPageUri =
            FacebookPageAppSubscriptionUriBuilder.GetAppUnsubscribeFacebookPageUri(pageId, pageAccessToken);

        var response = await httpClient.DeleteAsync(appUnsubscribeFacebookPageUri);

        if (!response.IsSuccessStatusCode)
        {
            var responseContent = await response.Content.ReadAsStringAsync();
            var errorResponse = JObject.Parse(responseContent).ToString(Formatting.None);
            var errorMessage =
                $"Failed to unsubscribe the Facebook page. Error Response: {errorResponse}, Request Url: {appUnsubscribeFacebookPageUri}";

            _logger.LogError(errorMessage);
        }
    }

    #endregion

    private async Task<Company> GetCompanyAsync(string companyId)
    {
        var company = await _appDbContext.CompanyCompanies
            .Where(c => c.Id == companyId)
            .FirstOrDefaultAsync();
        return company;
    }
}