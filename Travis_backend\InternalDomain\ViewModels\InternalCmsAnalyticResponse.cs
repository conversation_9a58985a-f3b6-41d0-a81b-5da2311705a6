﻿using System.Collections.Generic;

namespace Travis_backend.InternalDomain.ViewModels
{
    public class GetCmsAnalyticResponse
    {
        public List<CmsDailyRevenueAnalyticDto> DailyAnalytics { get; set; } = new ();
    }

    public class GetCmsCohortAnalysisDataResponse
    {
        public List<CmsCohortAnalysisDataDto> MonthlyCohortAnalysisData { get; set; } = new ();
    }

    public class CompanyRevenueStatusBreakdownsResponse
    {
        public List<CompanyRevenueStatusBreakdown> CompanyRevenueStatusBreakdowns { get; set; } = new ();
    }

    public class GetCmsAnalyticBySalesResponse
    {
        public List<CmsDailyAnalyticByContactOwnerDto> ByContactOwnerDailyAnalytics { get; set; } = new ();
    }

    public class GetCmsAnalyticByOwnerResponse
    {
        public CmsDailyAnalyticByContactOwnerDto ByOwnerDailyAnalytic { get; set; } = new ();
    }

    public class GetCmsDistributionAnalyticResponse
    {
        public List<CmsDailyDistributionAnalyticDto> DailyDistributionAnalytics { get; set; } = new ();
    }

    public class CmsCohortAnalysisDataDto
    {
        public string YearMonth { get; set; }

        public int SubscriptionCount { get; set; }

        public decimal MonthlyRecurringRevenue { get; set; }

        public List<CmsCompanyRevenueBreakDownDto> CompanyRevenueBreakDowns { get; set; } = new ();

        public List<CmsCohortAnalysisDataForwardMonthDataDto> ForwardMonths { get; set; } = new ();
    }

    public class CmsCohortAnalysisDataForwardMonthDataDto
    {
        public string ForwardMonth { get; set; }

        public int SubscriptionCount { get; set; }

        public decimal MonthlyRecurringRevenue { get; set; }

        public List<CmsCompanyRevenueBreakDownDto> CompanyRevenueBreakDowns { get; set; } = new ();
    }

    public class CompanyRevenueStatusBreakdown
    {
        public string CompanyId { get; set; }

        public string CompanyName { get; set; }

        public string SubscriptionPlanId { get; set; }

        public decimal MonthlyRecurringRevenue { get; set; }

        public decimal MrrDiff { get; set; }

        public decimal MrrDiffPercent { get; set; }

        public CompanyRevenueStatus Status { get; set; }
    }

    public enum CompanyRevenueStatus
    {
        New,
        Return,
        Increase,
        Sustain,
        Decrease,
        Churn,
        ChurnedInMiddle
    }
}