using System.Globalization;
using System.Text;
using CsvHelper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Build.Framework;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using Travis_backend.AccountAuthenticationDomain.Models;
using Travis_backend.CommonDomain.ViewModels;
using Travis_backend.Configuration;
using Travis_backend.Constants;
using Travis_backend.Database;
using Travis_backend.Helpers;
using Travis_backend.MessageDomain.Models;

namespace Sleekflow.Powerflow.Apis.Controllers;

[Route("/internal/conversation-analytic/[action]")]
[Authorize(Roles = ApplicationUserRole.InternalCmsSuperUser)] // Basic Role Requirement
public class InternalConversationAnalyticController : InternalControllerBase
{
    private readonly ApplicationDbContext _appDbContext;
    private readonly ILogger<InternalConversationAnalyticController> _logger;

    public InternalConversationAnalyticController(
        UserManager<ApplicationUser> userManager,
        ILogger<InternalConversationAnalyticController> logger,
        ApplicationDbContext appDbContext)
        : base(userManager)
    {
        _logger = logger;
        _appDbContext = appDbContext;
    }

    /// <summary>
    /// Sandro (c39b40f9-38c3-4e2a-bc53-af46389363a9)
    /// Maje (c3c8fa8c-c64f-4f94-873d-250845bf71cf)
    /// Claudie Pierlot (5edfbcc3-2179-405f-b860-0afd7d2356e3)
    /// </summary>
    [HttpPost]
#if DEBUG
    [AllowAnonymous]
#endif
    public async Task<ActionResult> GetAdvancedAnalyticData(
        [FromBody]
        GetAdvanceAnalyticDataRequest request)
    {
        var company =
            await _appDbContext.CompanyCompanies
                .Where(x => x.Id == request.CompanyId)
                .FirstOrDefaultAsync();

        var fromWithTimeZone = request.Period.Start.StartOfDay(company.TimeZoneInfoId);
        var toWithTimeZone = request.Period.End.StartOfDay(company.TimeZoneInfoId);

        request.Period.Start = fromWithTimeZone;
        request.Period.End = toWithTimeZone;

        var resultC = new List<CompanyAdvancedConversationAnalyticData>();

        var conversationMessageQuery =
            _appDbContext.ConversationMessages.Where(x => x.CompanyId == company.Id);

        var resultT = new List<CompanyAdvancedConversationAnalyticDataByPeriod>();

        var cusData = new CompanyAdvancedConversationMessageAnalyticData();

        cusData.TotalMessageSent = conversationMessageQuery.Count(
            x => x.CreatedAt >= request.Period.Start && x.CreatedAt <= request.Period.End &&
                 x.IsSentFromSleekflow &&
                 (x.Status != MessageStatus.Failed || x.Status != MessageStatus.Undelivered));

        cusData.TotalMessageRead = conversationMessageQuery.Count(
            x => x.CreatedAt >= request.Period.Start && x.CreatedAt <= request.Period.End &&
                 x.IsSentFromSleekflow &&
                 (x.Status == MessageStatus.Read));

        cusData.TotalUniqueConversation = conversationMessageQuery.Where(
                x => x.CreatedAt >= request.Period.Start && x.CreatedAt <= request.Period.End)
            .Select(x => x.ConversationId).Distinct().Count();

        var companyAdvancedConversationMessageResponseTimeAnalyticData =
            new CompanyAdvancedConversationAnalyticDataByPeriod
            {
                Start = request.Period.Start.ToString("yyyy/MM/dd"),
                End = request.Period.End.ToString("yyyy/MM/dd"),
                CompanyAdvancedConversationMessageAnalyticData = cusData
            };

        // Response Time By Day
        for (var dt = request.Period.Start; dt <= request.Period.End; dt = dt.AddDays(1))
        {
            var i1 = dt;

            var conversationMessagesQuery2 = _appDbContext.ConversationMessages
                .Where(x => x.CompanyId == company.Id)
                .Where(
                    x => x.CreatedAt >= i1 && x.CreatedAt <= i1.AddDays(1) &&
                         (x.Status != MessageStatus.Failed || x.Status != MessageStatus.Undelivered) &&
                         x.Channel != "note");

            // Response Time By Day
            var conversationAvgResponseTimeSum = 0L;
            var conversationResponseTimeCount = 0;

            var conversationMessages = await conversationMessagesQuery2
                .Select(
                    c => new
                    {
                        ConversationId = c.ConversationId,
                        IsSentFromSleekflow = c.IsSentFromSleekflow,
                        TimeStamp = c.Timestamp,
                        DeliveryType = c.DeliveryType
                    })
                .ToListAsync();

            var conversationsWithMessages = conversationMessages
                .OrderBy(c => c.TimeStamp)
                .GroupBy(x => x.ConversationId)
                .Select(
                    g => new
                    {
                        conversationId = g.Key,
                        messages = g.Select(
                                c => new
                                {
                                    IsSentFromSleekflow = c.IsSentFromSleekflow,
                                    TimeStamp = c.TimeStamp,
                                    DeliveryType = c.DeliveryType
                                })
                            .OrderBy(x => x.TimeStamp)
                            .ToList(),
                    })
                .ToList();

            var totalMessageReplyCount = 0;
            var unReplyMessageCount = 0;

            // Looping Conversations
            foreach (var conversation in conversationsWithMessages)
            {
                var receivedTime = 0L;
                var replyTime = 0L;

                var responseTimeSum = 0L;

                int messageReplyCount = 0;

                // Looping Messages In Conversation
                foreach (var messages in conversation.messages)
                {
                    // Set Customer Receive Time
                    if (receivedTime == 0L && messages.IsSentFromSleekflow)
                    {
                        receivedTime = messages.TimeStamp;
                    }

                    // Set Reply Time
                    if (receivedTime != 0L && replyTime == 0L && !messages.IsSentFromSleekflow)
                    {
                        replyTime = messages.TimeStamp;
                    }

                    if (receivedTime != 0L && replyTime != 0L)
                    {
                        // Calculate response time
                        responseTimeSum += replyTime - receivedTime;
                        messageReplyCount++;

                        // Reset Receive, Reply Time
                        receivedTime = 0;
                        replyTime = 0;
                    }
                }

                if (responseTimeSum > 0L)
                {
                    conversationAvgResponseTimeSum += responseTimeSum / messageReplyCount;
                    conversationResponseTimeCount++;
                }

                if (receivedTime != 0L && replyTime == 0L)
                    unReplyMessageCount++;
                else
                    totalMessageReplyCount += messageReplyCount;
            }

            // Calculate response times

            companyAdvancedConversationMessageResponseTimeAnalyticData.CusDataResponseTimeDaily.Add(
                new CompanyAdvancedConversationMessageResponseTimeAnalyticData
                {
                    Date = i1.ToString("yyyy/MM/dd"),
                    AverageResponseTimeTimestamp = conversationResponseTimeCount > 0
                        ? conversationAvgResponseTimeSum / conversationResponseTimeCount
                        : 0,
                    TotalMessageReplyCount = totalMessageReplyCount,
                    UnReplyMessageCount = unReplyMessageCount,
                    TotalConversationCount = conversationsWithMessages.Count()
                });

        }

        companyAdvancedConversationMessageResponseTimeAnalyticData
                .CompanyAdvancedConversationMessageResponseTimeAnalyticData =
            new CompanyAdvancedConversationMessageResponseTimeAnalyticData
            {
                Date = null,
                AverageResponseTimeTimestamp =
                    (long) companyAdvancedConversationMessageResponseTimeAnalyticData.CusDataResponseTimeDaily
                        .Average(
                            x => x.AverageResponseTimeTimestamp),
                TotalMessageReplyCount =
                    companyAdvancedConversationMessageResponseTimeAnalyticData.CusDataResponseTimeDaily.Sum(
                        x => x.TotalMessageReplyCount),
                UnReplyMessageCount =
                    companyAdvancedConversationMessageResponseTimeAnalyticData.CusDataResponseTimeDaily.Sum(
                        x => x.UnReplyMessageCount),
                TotalConversationCount =
                    companyAdvancedConversationMessageResponseTimeAnalyticData.CusDataResponseTimeDaily.Sum(
                        x => x.TotalConversationCount),
            };


        resultT.Add(companyAdvancedConversationMessageResponseTimeAnalyticData);

        resultC.Add(
            new CompanyAdvancedConversationAnalyticData()
            {
                Id = company.Id, CompanyName = company.CompanyName, CusDataByPeriods = resultT,
            });

        return Ok(resultC);
    }

    /// <summary>
    /// Get Company's response time to client's message by the metrics of each of the Sleekflow User
    /// </summary>
    /// <returns></returns>
    [HttpPost]
    public async Task<ActionResult> GetMessagingAnalyticBySleekflowUser(
        [FromBody] GetMessagingAnalyticBySleekflowUserInput input)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Invalid Form"
                });
        }

        if (await GetCurrentValidInternalUser(
                new List<string>()
                {
                    ApplicationUserRole.InternalCmsTeamLead,
                    ApplicationUserRole.InternalCmsCustomerSuccessUser,
                    ApplicationUserRole.InternalCmsSalesUser
                }) == null)
        {
            return Unauthorized();
        }

        var start = input.Start.Date.AddHours(input.TimezoneHourOffset);
        var end = input.End.Date.AddHours(input.TimezoneHourOffset);

        var conversationIds = await _appDbContext.Conversations
            .Where(
                x => x.CompanyId == input.CompanyId
                     && x.UpdatedTime >= start
                     && x.UpdatedTime <= end)
            .Select(x => x.Id)
            .ToListAsync();

        var allMessagesResponseAnalytics = new Dictionary<string, List<TimeSpan>>();
        var newCustomerAnalytics = new Dictionary<string, List<TimeSpan>>();

        foreach (var conversationId in conversationIds)
        {
            //Obtain the list of messages
            var conversationMessages =
                await _appDbContext.ConversationMessages
                    .Where(
                        x =>
                            x.ConversationId == conversationId
                            && x.IsFromImport == false
                            && x.IsSandbox == false
                            && x.CreatedAt >= start
                            && x.CreatedAt <= end
                            && x.MessageType  != MessageTypes.System
                            && x.DeliveryType != DeliveryType.Broadcast
                            && x.DeliveryType != DeliveryType.AutomatedMessage
                            && x.DeliveryType != DeliveryType.FlowHubAction
                    )
                    .OrderBy(x => x.Id)
                    .Select(
                        x => new
                        {
                            IsSentFromSleekflow = x.IsSentFromSleekflow, SenderId = x.SenderId, CreatedAt = x.CreatedAt
                        })
                    .ToListAsync();

            if (!conversationMessages.Any())
            {
                continue;
            }

            var isExistingCustomer = await _appDbContext.ConversationMessages
                .AnyAsync(
                    x =>
                        x.ConversationId == conversationId
                        && x.IsSandbox == false
                        && x.CreatedAt <= start
                        && x.MessageType  != MessageTypes.System
                        && x.DeliveryType != DeliveryType.Broadcast
                        && x.DeliveryType != DeliveryType.AutomatedMessage
                        && x.DeliveryType != DeliveryType.FlowHubAction);

            //Initial Message
            for (var day = start.Date; day.Date <= end.Date; day = day.AddDays(1))
            {
                var currentDateConversationMessages = conversationMessages
                    .Where(x => x.CreatedAt.Date == day.Date)
                    .OrderBy(x => x.CreatedAt)
                    .ToList();

                if (!currentDateConversationMessages.Any())
                {
                    continue;
                }

                var previousMessage = currentDateConversationMessages[0];
                var previousMessageIsSentFromSleekflow = previousMessage.IsSentFromSleekflow;

                for (var i = 1; i < currentDateConversationMessages.Count; i++)
                {
                    var currentDateConversationMessage = currentDateConversationMessages[i];
                    var isSentFromSleekflow = currentDateConversationMessage.IsSentFromSleekflow;
                    var senderUserIdentityId = currentDateConversationMessage.SenderId;

                    if (isSentFromSleekflow && senderUserIdentityId == null)
                    {
                        continue;
                    }

                    if (isSentFromSleekflow
                        && !previousMessageIsSentFromSleekflow
                        && senderUserIdentityId != null)
                    {
                        //Add the pair in the result list
                        var timespan = currentDateConversationMessage.CreatedAt.Subtract(previousMessage.CreatedAt);

                        if (allMessagesResponseAnalytics.ContainsKey(senderUserIdentityId))
                        {
                            allMessagesResponseAnalytics[currentDateConversationMessage.SenderId].Add(timespan);
                            //Generate a timestamp
                        }
                        else
                        {
                            allMessagesResponseAnalytics.Add(
                                currentDateConversationMessage.SenderId,
                                new List<TimeSpan>()
                                {
                                    timespan
                                });
                        }

                        if (!isExistingCustomer)
                        {
                            if (newCustomerAnalytics.ContainsKey(senderUserIdentityId))
                            {
                                newCustomerAnalytics[currentDateConversationMessage.SenderId].Add(timespan);
                                //Generate a timestamp
                            }
                            else
                            {
                                newCustomerAnalytics.Add(
                                    currentDateConversationMessage.SenderId,
                                    new List<TimeSpan>()
                                    {
                                        timespan
                                    });
                            }

                            isExistingCustomer = true;
                        }
                    }

                    previousMessageIsSentFromSleekflow = isSentFromSleekflow;
                }
            }
        }

        var outputResults = new List<SleekflowUserCmsAnalytics>();
        foreach (var analytics in allMessagesResponseAnalytics)
        {
            var sleekflowUserIdentityId = analytics.Key;
            var analyticsResult = analytics.Value;

            var averageResponseTime = new TimeSpan((long) analyticsResult.Select(ts => ts.Ticks).Average());
            if (outputResults.Any(x => x.IdentityId == analytics.Key))
            {
                var outputResult = outputResults.First(x => x.IdentityId == analytics.Key);
                outputResult.AverageMessageResponseTime = averageResponseTime;
            }
            else
            {
                var staffName = (await
                                    _appDbContext.Users.FirstOrDefaultAsync(x => x.Id == sleekflowUserIdentityId))
                                ?.DisplayName ??
                                string.Empty;
                outputResults.Add(
                    new SleekflowUserCmsAnalytics
                    {
                        IdentityId = sleekflowUserIdentityId,
                        Name = staffName,
                        AverageMessageResponseTime = averageResponseTime
                    });
            }
        }

        foreach (var analytics in newCustomerAnalytics)
        {
            var sleekflowUserIdentityId = analytics.Key;
            var analyticsResult = analytics.Value;

            var averageResponseTime = new TimeSpan((long) analyticsResult.Select(ts => ts.Ticks).Average());

            if (outputResults.Any(x => x.IdentityId == analytics.Key))
            {
                var outputResult = outputResults.First(x => x.IdentityId == analytics.Key);
                outputResult.NewCustomerResponseTime = averageResponseTime;
            }
            else
            {
                var staffName = (await
                                    _appDbContext.Users.FirstOrDefaultAsync(x => x.Id == sleekflowUserIdentityId))
                                ?.DisplayName ??
                                string.Empty;
                outputResults.Add(
                    new SleekflowUserCmsAnalytics
                    {
                        IdentityId = sleekflowUserIdentityId,
                        Name = staffName,
                        NewCustomerResponseTime = averageResponseTime
                    });
            }
        }

        var ms = new MemoryStream();

        await using (var writer = new StreamWriter(ms, Encoding.UTF8, leaveOpen: true))
        {
            await using (var csv = new CsvWriter(writer, CultureInfo.InvariantCulture))
            {
                await csv.WriteRecordsAsync(outputResults);
            }
        }

        return File(ms.ToArray(), "text/csv", "result.csv");
    }


    public class SleekflowUserCmsAnalytics
    {
        public string IdentityId { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public TimeSpan AverageMessageResponseTime { get; set; } = new TimeSpan();
        public TimeSpan NewCustomerResponseTime { get; set; } = new TimeSpan();
    }

    public class GetMessagingAnalyticBySleekflowUserInput
    {
        [Required]
        public string CompanyId { get; set; }

        public DateTime Start { get; set; }

        public DateTime End { get; set; }

        public int TimezoneHourOffset { get; set; } = 8;
    }

    public record GetAdvanceAnalyticDataRequest(string CompanyId, AdvancedConversationMessageAnalyticPeriod Period);

    public class AdvancedConversationMessageAnalyticPeriod
    {
        public DateTime Start { get; set; }
        public DateTime End { get; set; }

        public AdvancedConversationMessageAnalyticPeriod(DateTime start, DateTime end)
        {
            Start = start;
            End = end;
        }
    }

    public class CompanyAdvancedConversationAnalyticData
    {
        public string Id { get; set; }
        public string CompanyName { get; set; }
        public List<CompanyAdvancedConversationAnalyticDataByPeriod> CusDataByPeriods { get; set; } = new ();
    }

    public class CompanyAdvancedConversationAnalyticDataByPeriod
    {
        public string Start { get; set; }
        public string End { get; set; }

        public CompanyAdvancedConversationMessageAnalyticData CompanyAdvancedConversationMessageAnalyticData
        {
            get;
            set;
        }

        public CompanyAdvancedConversationMessageResponseTimeAnalyticData
            CompanyAdvancedConversationMessageResponseTimeAnalyticData { get; set; }

        [JsonIgnore]
        public List<CompanyAdvancedConversationMessageResponseTimeAnalyticData> CusDataResponseTimeDaily
        {
            get;
            set;
        } = new ();
    }

    public class CompanyAdvancedConversationMessageAnalyticData
    {
        public int TotalMessageSent { get; set; }
        public int TotalMessageRead { get; set; }
        public double TotalMessageReadRate => (double) TotalMessageRead / (double) TotalMessageSent;

        public int TotalUniqueConversation { get; set; }
    }

    public class CompanyAdvancedConversationMessageResponseTimeAnalyticData
    {
        public string Date { get; set; }
        public long AverageResponseTimeTimestamp { get; set; }
        public TimeSpan AverageResponseTime => TimeSpan.FromSeconds(AverageResponseTimeTimestamp);
        public int TotalMessageReplyCount { get; set; }
        public int UnReplyMessageCount { get; set; }
        public int TotalConversationCount { get; set; }
        public double ReplyRate => (double) TotalMessageReplyCount / (double) TotalConversationCount;
    }
}