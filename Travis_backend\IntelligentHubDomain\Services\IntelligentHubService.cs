﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Sleekflow.Apis.IntelligentHub.Api;
using Sleekflow.Apis.IntelligentHub.Model;
using Travis_backend.CompanyDomain.Services;
using Travis_backend.SubscriptionPlanDomain.Services;

namespace Travis_backend.IntelligentHubDomain.Services;

public interface IIntelligentHubService
{
    Task RefreshIntelligentHubConfigAsync(string companyId);

    Task<Dictionary<string, int>> InitializeIntelligentHubConfig(string companyId);
}

public class IntelligentHubService : IIntelligentHubService
{
    private readonly ILogger<IntelligentHubService> _logger;
    private readonly IIntelligentHubConfigsApi _intelligentHubConfigsApi;
    private readonly ICompanyUsageService _companyUsageService;
    private readonly IPlanDefinitionService _planDefinitionService;

    public IntelligentHubService(
        IIntelligentHubConfigsApi intelligentHubConfigsApi,
        ICompanyUsageService companyUsageService,
        ILogger<IntelligentHubService> logger,
        IPlanDefinitionService planDefinitionService)
    {
        _intelligentHubConfigsApi = intelligentHubConfigsApi;
        _companyUsageService = companyUsageService;
        _logger = logger;
        _planDefinitionService = planDefinitionService;
    }

    public async Task RefreshIntelligentHubConfigAsync(string companyId)
    {
        _logger.LogInformation("Update IntelligentHub config for Company {CompanyId}", companyId);

        var billingPeriodUsages =
            await _companyUsageService.GetBillingPeriodUsages(companyId, false);
        var currentSubscriptionPlan = billingPeriodUsages.FirstOrDefault()?.BillRecord.SubscriptionPlan;

        var planDefinition = await _planDefinitionService.GetPlanDefinitionAsync(currentSubscriptionPlan?.Id);

        var usageLimits = new Dictionary<string, int>();
        foreach (var priceableFeatureId in Models.IntelligentHubConfig.PriceableFeatureIds)
        {
            usageLimits.Add(
                priceableFeatureId,
                planDefinition.FeatureQuantities.FirstOrDefault(fq => fq.FeatureId == priceableFeatureId)!.Quantity);
        }

        var getIntelligentHubConfigOutputOutput = await _intelligentHubConfigsApi.IntelligentHubConfigsGetIntelligentHubConfigPostAsync(
            getIntelligentHubConfigInput: new GetIntelligentHubConfigInput(companyId));

        // ReSharper disable once ConditionIsAlwaysTrueOrFalse - false positive
        if (getIntelligentHubConfigOutputOutput.Data.IntelligentHubConfig == null)
        {
            await InitializeIntelligentHubConfig(companyId);

            return;
        }

        var updateIntelligentHubConfigOutputOutput = await _intelligentHubConfigsApi.IntelligentHubConfigsUpdateIntelligentHubConfigPostAsync(
            updateIntelligentHubConfigInput: new UpdateIntelligentHubConfigInput(
                companyId,
                usageLimits));

        if (!updateIntelligentHubConfigOutputOutput.Success)
        {
            _logger.LogError(
                "Update IntelligentHub config error: company id {CompanyId}",
                companyId);
        }
    }

    public async Task<Dictionary<string, int>> InitializeIntelligentHubConfig(string companyId)
    {
        _logger.LogInformation("Initialize IntelligentHub config for Company {CompanyId}", companyId);

        try
        {
            var billingPeriodUsages =
                await _companyUsageService.GetBillingPeriodUsages(companyId, false);
            var currentSubscriptionPlan = billingPeriodUsages.FirstOrDefault()?.BillRecord.SubscriptionPlan;

            var planDefinition = await _planDefinitionService.GetPlanDefinitionAsync(currentSubscriptionPlan?.Id);

            var UsageLimits = new Dictionary<string, int>();
            foreach (var priceableFeatureId in Models.IntelligentHubConfig.PriceableFeatureIds)
            {
                var featureQuantity =
                    planDefinition.FeatureQuantities.FirstOrDefault(fq => fq.FeatureId == priceableFeatureId);
                UsageLimits.Add(priceableFeatureId, featureQuantity?.Quantity ?? 0);
            }

            var intelligentHubConfigOutputOutput = await _intelligentHubConfigsApi.IntelligentHubConfigsInitializeIntelligentHubConfigPostAsync(
                initializeIntelligentHubConfigInput: new InitializeIntelligentHubConfigInput(
                    companyId,
                    UsageLimits));

            if (!intelligentHubConfigOutputOutput.Success)
            {
                throw new Exception(intelligentHubConfigOutputOutput.Message);
            }

            return intelligentHubConfigOutputOutput.Data.IntelligentHubConfig.UsageLimits;
        }
        catch (Exception e)
        {
            _logger.LogError(
                "Failed to initialize IntelligentHub Config. {CompanyId} {Message}",
                companyId,
                e.Message);
            throw;
        }
    }
}