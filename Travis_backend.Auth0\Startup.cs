using System.Diagnostics.Metrics;
using System.IO.Compression;
using Auth0.ManagementApi;
using Azure.Monitor.OpenTelemetry.Exporter;
using Hangfire;
using Hangfire.Pro.Redis;
using Microsoft.ApplicationInsights.DependencyCollector;
using Microsoft.ApplicationInsights.Extensibility.EventCounterCollector;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.ResponseCompression;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection.Extensions;
using Microsoft.IdentityModel.Tokens;
using Newtonsoft.Json;
using OpenTelemetry;
using OpenTelemetry.Metrics;
using OpenTelemetry.Trace;
using Stripe;
using Travis_backend.AccountAuthenticationDomain.Models;
using Travis_backend.AccountAuthenticationDomain.Services;
using Travis_backend.Auth0.Configuration;
using Travis_backend.Auth0.Exceptions;
using Travis_backend.Auth0.HealthChecks;
using Travis_backend.Auth0.Services;
using Travis_backend.Auth0.Services.Auth0;
using Travis_backend.BackgroundTaskServices.Attributes;
using Travis_backend.Cache;
using Travis_backend.CommonDomain.Models;
using Travis_backend.CompanyDomain.Services;
using Travis_backend.Configuration;
using Travis_backend.Constants;
using Travis_backend.Database;
using Travis_backend.Database.Filters;
using Travis_backend.Database.Services;
using Travis_backend.**********************;
using Travis_backend.HealthChecks;
using Travis_backend.Infrastructures.Middlewares;
using Travis_backend.Middlewares;
using Travis_backend.Middlewares.RateLimitingMiddlewares;
using Travis_backend.OpenTelemetry.Meters;
using Travis_backend.Services.Internal;
using Travis_backend.SignalR;
using Travis_backend.Telemetries.TelemetryProcessor;
using Travis_backend.TenantHubDomain.Services;
using Task = System.Threading.Tasks.Task;

namespace Travis_backend.Auth0
{
    public class Startup
    {
        public Startup(IConfiguration configuration, IWebHostEnvironment environment)
        {
            Environment = environment;
            Configuration = configuration;
        }

        readonly string MyAllowSpecificOrigins = "_myAllowSpecificOrigins";

        public IWebHostEnvironment Environment { get; }

        public IConfiguration Configuration { get; }

        // This method gets called by the runtime. Use this method to add services to the container.
        public void ConfigureServices(IServiceCollection services)
        {
            if (System.Environment.GetEnvironmentVariable("APPLICATIONINSIGHTS_CONNECTION_STRING") != null
                && !string.IsNullOrEmpty(
                    System.Environment.GetEnvironmentVariable("APPLICATIONINSIGHTS_CONNECTION_STRING")))
            {
                services.AddApplicationInsightsTelemetry(
                    options =>
                    {
                        var isSamplingDisabled =
                            System.Environment.GetEnvironmentVariable("APPLICATIONINSIGHTS_IS_SAMPLING_DISABLED");

                        if (isSamplingDisabled != null && string.Equals(
                                isSamplingDisabled,
                                "true",
                                StringComparison.OrdinalIgnoreCase))
                        {
                            options.EnableAdaptiveSampling = false;
                        }
                    });
                services.AddApplicationInsightsTelemetryProcessor<RemoveSucceedSqlCommandTelemetryProcessor>();
                services.AddServiceProfiler();
                services.AddSnapshotCollector(
                    config => { config.ThresholdForSnapshotting = 50; });
                services.ConfigureTelemetryModule<DependencyTrackingTelemetryModule>(
                    (module, o) => { module.EnableSqlCommandTextInstrumentation = true; });
                services.ConfigureTelemetryModule<EventCounterCollectionModule>(
                    (module, o) =>
                    {
                        // Removes all default counters, if any.
                        module.Counters.Clear();

                        // https://learn.microsoft.com/en-us/dotnet/core/diagnostics/available-counters
                        // @formatter:off
                        module.Counters.Add(new EventCounterCollectionRequest("System.Runtime", "time-in-gc"));
                        module.Counters.Add(new EventCounterCollectionRequest("System.Runtime", "alloc-rate"));
                        module.Counters.Add(new EventCounterCollectionRequest("System.Runtime", "cpu-usage"));
                        module.Counters.Add(new EventCounterCollectionRequest("System.Runtime", "exception-count"));
                        module.Counters.Add(new EventCounterCollectionRequest("System.Runtime", "gc-heap-size"));
                        module.Counters.Add(new EventCounterCollectionRequest("System.Runtime", "gen-0-gc-count"));
                        module.Counters.Add(new EventCounterCollectionRequest("System.Runtime", "gen-0-size"));
                        module.Counters.Add(new EventCounterCollectionRequest("System.Runtime", "gen-1-gc-count"));
                        module.Counters.Add(new EventCounterCollectionRequest("System.Runtime", "gen-1-size"));
                        module.Counters.Add(new EventCounterCollectionRequest("System.Runtime", "gen-2-gc-count"));
                        module.Counters.Add(new EventCounterCollectionRequest("System.Runtime", "gen-2-size"));
                        module.Counters.Add(new EventCounterCollectionRequest("System.Runtime", "loh-size"));
                        module.Counters.Add(new EventCounterCollectionRequest("System.Runtime", "poh-size"));
                        module.Counters.Add(new EventCounterCollectionRequest("System.Runtime", "gc-fragmentation"));
                        module.Counters.Add(new EventCounterCollectionRequest("System.Runtime", "threadpool-completed-items-count"));
                        module.Counters.Add(new EventCounterCollectionRequest("System.Runtime", "threadpool-queue-length"));
                        module.Counters.Add(new EventCounterCollectionRequest("System.Runtime", "threadpool-thread-count"));
                        module.Counters.Add(new EventCounterCollectionRequest("System.Runtime", "working-set"));
                        module.Counters.Add(new EventCounterCollectionRequest("System.Runtime", "gc-committed"));
                        module.Counters.Add(new EventCounterCollectionRequest("Microsoft.AspNetCore.Hosting", "current-requests"));
                        module.Counters.Add(new EventCounterCollectionRequest("Microsoft.AspNetCore.Hosting", "failed-requests"));
                        module.Counters.Add(new EventCounterCollectionRequest("Microsoft.AspNetCore.Hosting", "requests-per-second"));
                        module.Counters.Add(new EventCounterCollectionRequest("Microsoft.AspNetCore.Hosting", "total-requests"));
                        module.Counters.Add(new EventCounterCollectionRequest("Microsoft.AspNetCore.Http.Connections", "connections-duration"));
                        module.Counters.Add(new EventCounterCollectionRequest("Microsoft.AspNetCore.Http.Connections", "current-connections"));
                        module.Counters.Add(new EventCounterCollectionRequest("Microsoft.AspNetCore.Http.Connections", "connections-started"));
                        module.Counters.Add(new EventCounterCollectionRequest("Microsoft.AspNetCore.Http.Connections", "connections-stopped"));
                        module.Counters.Add(new EventCounterCollectionRequest("Microsoft.AspNetCore.Http.Connections", "connections-timed-out"));
                        module.Counters.Add(new EventCounterCollectionRequest("Microsoft-AspNetCore-Server-Kestrel", "connection-queue-length"));
                        module.Counters.Add(new EventCounterCollectionRequest("Microsoft-AspNetCore-Server-Kestrel", "connections-per-second"));
                        module.Counters.Add(new EventCounterCollectionRequest("Microsoft-AspNetCore-Server-Kestrel", "current-connections"));
                        module.Counters.Add(new EventCounterCollectionRequest("Microsoft-AspNetCore-Server-Kestrel", "current-tls-handshakes"));
                        module.Counters.Add(new EventCounterCollectionRequest("Microsoft-AspNetCore-Server-Kestrel", "current-upgraded-requests"));
                        module.Counters.Add(new EventCounterCollectionRequest("Microsoft-AspNetCore-Server-Kestrel", "failed-tls-handshakes"));
                        module.Counters.Add(new EventCounterCollectionRequest("Microsoft-AspNetCore-Server-Kestrel", "request-queue-length"));
                        module.Counters.Add(new EventCounterCollectionRequest("Microsoft-AspNetCore-Server-Kestrel", "tls-handshakes-per-second"));
                        module.Counters.Add(new EventCounterCollectionRequest("Microsoft-AspNetCore-Server-Kestrel", "total-connections"));
                        module.Counters.Add(new EventCounterCollectionRequest("Microsoft-AspNetCore-Server-Kestrel", "total-tls-handshakes"));
                        module.Counters.Add(new EventCounterCollectionRequest("System.Net.Http", "requests-started"));
                        module.Counters.Add(new EventCounterCollectionRequest("System.Net.Http", "requests-started-rate"));
                        module.Counters.Add(new EventCounterCollectionRequest("System.Net.Http", "requests-failed"));
                        module.Counters.Add(new EventCounterCollectionRequest("System.Net.Http", "requests-failed-rate"));
                        module.Counters.Add(new EventCounterCollectionRequest("System.Net.Http", "current-requests"));

                        // @formatter:on
                    });

                services.AddOpenTelemetry()
                    .WithMetrics(
                        metrics =>
                        {
                            metrics.AddMeter(BaseMeters.SleekflowCoreMeters);
                            metrics.AddAzureMonitorMetricExporter(
                                credential =>
                                {
                                    credential.ConnectionString = System.Environment.GetEnvironmentVariable(
                                        "APPLICATIONINSIGHTS_CONNECTION_STRING");
                                });
                        })
                    .WithTracing(
                        builder =>
                        {
                            builder.AddHangfireInstrumentation();
                        });
            }

            // Meters to track the usage of the application
            services.AddSingleton<IAutomationMeters, AutomationMeters>();
            services.AddSingleton<IConversationMeters, ConversationMeters>();
            services.AddSingleton<IMetaChannelConnectionMeters, MetaChannelConnectionMeters>();

            services.Configure<NotificationHubConfiguration>(Configuration.GetSection("NotificationHub"));

            services.AddControllersWithViews();
            services.AddRazorPages();

            services.Configure<CookiePolicyOptions>(
                options =>
                {
                    // This lambda determines whether user consent for non-essential cookies is needed for a given request.
                    options.CheckConsentNeeded = context => true;
                    options.MinimumSameSitePolicy = SameSiteMode.None;
                });

            services.ConfigureApplicationCookie(
                options => { options.Cookie.SameSite = SameSiteMode.None; });

            #region Auth0

            var auth0Settings = Configuration.GetSection("Auth0").Get<Auth0Config>();

            services.AddOptions<Auth0Config>().Bind(Configuration.GetSection("Auth0"));
            services.AddSingleton<Auth0Config>(auth0Settings);
            services.Configure<IdentityOptions>(
                options =>
                {
                    // Default Password settings.
                    options.Password.RequireDigit = false;
                    options.Password.RequireLowercase = false;
                    options.Password.RequireNonAlphanumeric = false;
                    options.Password.RequireUppercase = false;
                    options.Password.RequiredLength = 6;
                    options.ClaimsIdentity.RoleClaimType = auth0Settings.Namespace + auth0Settings.RoleClaimType;
                    options.ClaimsIdentity.UserIdClaimType = auth0Settings.Namespace + auth0Settings.UserIdClaimType;
                    options.ClaimsIdentity.UserNameClaimType =
                        auth0Settings.Namespace + auth0Settings.UserNameClaimType;
                    options.ClaimsIdentity.EmailClaimType = auth0Settings.Namespace + auth0Settings.UserEmailClaimType;
                });
            services.AddSingleton<IManagementConnection, HttpClientManagementConnection>();

            services.AddCors(
                options => options.AddPolicy(
                    MyAllowSpecificOrigins,
                    builder =>
                    {
                        // builder.WithOrigins("https://app.sleekflow.io",
                        //                    "https://d2k21dzzygonnm.cloudfront.net",
                        //                    "https://ds3954owgium6.cloudfront.net/",
                        //                    "http://localhost:3000")
                        //                .AllowAnyHeader()
                        //                .AllowAnyMethod();
                        builder.WithOrigins("*")
                            .AllowAnyHeader()
                            .AllowAnyMethod();
                    }));

            var hangfireWorkerCount = int.TryParse(
                Configuration["Hangfire:WorkerCount"],
                out var configuredHangfireWorkerCount)
                ? configuredHangfireWorkerCount
                : 20;

            var hangfireQueue = new List<string>()
            {
                HangfireQueues.High, HangfireQueues.Medium, HangfireQueues.Low, HangfireQueues.Default
            };

            if (!string.IsNullOrEmpty(Configuration["HangfireQueues:DisableInstances"]))
            {
                var disableInstances = Configuration["HangfireQueues:DisableInstances"].Split(",").ToList();
                foreach (var disableInstance in disableInstances)
                {
                    hangfireQueue.Remove(disableInstance);
                }
            }

            services.AddSingleton<IHangfireMeters, HangfireMeters>();
            services.AddSingleton<IBackgroundJobQueueService, BackgroundJobQueueService>();
            services.AddHangfire(
                (provider, config) =>
                {
                    config.UseSerializerSettings(
                        new JsonSerializerSettings()
                        {
                            ReferenceLoopHandling = ReferenceLoopHandling.Ignore, MaxDepth = 80,
                        });

                    config.UseFilter(new BackgroundJobFilterAttribute(provider.GetService<IHangfireMeters>()));
                });

            var connectionString = Configuration["redis:connectionString"];
            var prefixGroupNum = Configuration["redis:prefixGroupNum"] == null ? 1 : int.Parse(Configuration["redis:prefixGroupNum"]!);
            foreach (var redisStorage in BackgroundJobQueueInitializer.ConfigureHangfire(prefixGroupNum, connectionString))
            {
                services.AddHangfireServer(
                    (sp, x) =>
                    {
                        x.WorkerCount = hangfireWorkerCount;
                        x.Queues = hangfireQueue.ToArray();
                    },
                    redisStorage);
            }

            services.AddDbContext<ApplicationDbContext>(
                dbContextOptionsBuilder =>
                    dbContextOptionsBuilder
                        .UseSqlServer(
                            new SqlConnection(Configuration.GetConnectionString("DefaultConnection")),
                            sqlOptions =>
                                sqlOptions.EnableRetryOnFailure(10, TimeSpan.FromSeconds(30), null)));

            services.AddIdentity<ApplicationUser, IdentityRole>()
                .AddUserManager<SleekflowUserManager>()
                .AddEntityFrameworkStores<ApplicationDbContext>()
                .AddTokenProvider(
                    SleekflowTokenProviderOptions.InviteTokenProviderName,
                    typeof(InvitationTokenProvider))
                .AddDefaultTokenProviders();

            // Read only database context
            services.AddDbContext<ApplicationReadDbContext>(
                optionsAction =>
                    optionsAction.UseSqlServer(new SqlConnection(Configuration.GetConnectionString("ReadConnection"))));
            services.AddScoped<ISleekflowUserService, SleekflowUserService>();
            services.AddScoped<ReadOnlyEndpointAttribute>();
            services.AddScoped<IDbContextService, DbContextService>();
            services.AddScoped<IPersistenceContext, PersistenceContext>();

            services.AddTransient<IClaimsTransformation, SleekflowClaimsTransformation>();
            services.Configure<DataProtectionTokenProviderOptions>(
                opts => opts.TokenLifespan =
                    TimeSpan.FromDays(30)); // Default token expiration of 1 day, extend to 30 days

            #endregion

            services.AddSignalR()
                .AddNewtonsoftJsonProtocol(
                    options =>
                    {
                        options.PayloadSerializerSettings.DateTimeZoneHandling =
                            Newtonsoft.Json.DateTimeZoneHandling.Utc;
                        options.PayloadSerializerSettings.MaxDepth = 48;
                    })
                .AddAzureSignalR(serviceOptions =>
                {
                    serviceOptions.ClaimsProvider = context => context.User.Claims.Where(
                        c => new List<string>
                        {
                            "https://app.sleekflow.io/name",
                            "https://app.sleekflow.io/user_id",
                            "https://app.sleekflow.io/user_name",
                            "https://app.sleekflow.io/email",
                            "https://app.sleekflow.io/login_as_user_id",
                            "https://app.sleekflow.io/login_as_user",
                            "sub",
                        }.Exists(v => v == c.Type));
                });

            services.AddAutoMapper(typeof(Startup));
            services.AddAutoMapper(typeof(Travis_backend.Startup));
            services.AddMvc()
                .AddNewtonsoftJson(
                    options =>
                    {
                        options.SerializerSettings.NullValueHandling = NullValueHandling.Ignore;
                        options.SerializerSettings.DateTimeZoneHandling = DateTimeZoneHandling.Utc;
                    });

            services.AddTransient<TokenManagerMiddleware>();
            services.AddTransient<ITokenManager, TokenManager>();
            services.AddSingleton<IHttpContextAccessor, HttpContextAccessor>();

            services.AddScoped<IAuth0CompanyService, Auth0CompanyService>();

            var jwtSection = Configuration.GetSection("Tokens");
            var jwtOptions = new JwtOptions();
            jwtSection.Bind(jwtOptions);
            services
                .AddAuthentication(
                    options =>
                    {
                        options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
                        options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
                    })
                .AddJwtBearer(
                    config =>
                    {
                        config.Authority = $"https://{Configuration["Auth0:Domain"]}/";
                        config.Audience = Configuration["Auth0:Audience"];
                        config.TokenValidationParameters = new TokenValidationParameters()
                        {
                            ValidateAudience = true,
                            ValidateIssuer = true,
                            ValidateIssuerSigningKey = true,
                            ValidIssuers = Configuration
                                .GetSection("Auth0:Issuers")
                                .Get<List<string>>(),
                        };
                        config.Events = new JwtBearerEvents()
                        {
                            OnMessageReceived = context =>
                            {
                                var accessToken = context.Request.Query["access_token"];
                                var path = context.HttpContext.Request.Path;
                                if (!string.IsNullOrEmpty(accessToken) &&
                                    path.StartsWithSegments("/chat"))
                                {
                                    context.Token = accessToken;
                                }

                                return Task.CompletedTask;
                            },
                            OnAuthenticationFailed = context => { return Task.FromException(context.Exception); }
                        };
                    });

            services
                .AddResponseCompression(
                    options =>
                    {
                        options.Providers.Add<GzipCompressionProvider>();
                        options.Providers.Add<BrotliCompressionProvider>();
                        options.EnableForHttps = true;
                    })
                .Configure<BrotliCompressionProviderOptions>(options => { options.Level = CompressionLevel.Fastest; })
                .Configure<GzipCompressionProviderOptions>(options => { options.Level = CompressionLevel.Fastest; });

            services.Configure<JwtOptions>(jwtSection);

            services.AddAuth0AuthenticationClient(
                config =>
                {
                    config.Domain = auth0Settings.Domain;
                    config.ClientId = auth0Settings.ClientId;
                    config.ClientSecret = auth0Settings.ClientSecret;
                    config.Audience = auth0Settings.Audience;
                });
            services.AddAuth0ManagementClient().AddManagementAccessToken();

            Travis_backend.Startup.ConfigureSleekflowServices(services, Configuration);
            services.AddSingleton<IAuth0HealthCheckConfig, Auth0HealthCheckConfig>();

            // var isAuth0HealthCheckEnabled = System.Environment.GetEnvironmentVariable(
            //     "Auth0:HealthCheck:IsEnabled") ?? "false";
            // if (string.Equals(isAuth0HealthCheckEnabled, "true", StringComparison.OrdinalIgnoreCase))
            // {
            //     services.AddHealthChecks().AddCheck<Auth0HealthCheck>(
            //         "Auth0HealthCheck",
            //         tags: new[]
            //         {
            //             HealthCheckTags.Ready
            //         });
            // }

            var auth0Descriptor = new ServiceDescriptor(
                typeof(ITokenService),
                typeof(Auth0TokenService),
                ServiceLifetime.Scoped);
            services.Replace(auth0Descriptor);

            var auth0MfaDescriptor = new ServiceDescriptor(
                typeof(IMfaService),
                typeof(Auth0MfaService),
                ServiceLifetime.Scoped);

            services.Replace(auth0MfaDescriptor);
        }

        // This method gets called by the runtime. Use this method to configure the HTTP request pipeline.
        public void Configure(IApplicationBuilder app, IWebHostEnvironment env, ILogger<Startup> logger)
        {
            app.UseTravisBackendHealthChecks();
            app.UseResponseCompression();
            app.UseExceptionHandler("/error");
            app.UseClaimsTransformationExceptionHandler(); // Add this line

            if (!env.IsDevelopment())
            {
                // The default HSTS value is 30 days. You may want to change this for production scenarios, see https://aka.ms/aspnetcore-hsts.
                app.UseHsts();
            }

            app.UseCustomSwagger();

            app.UseStaticFiles();
            app.UseCookiePolicy();

            app.UseRouting();
            app.UseCors(MyAllowSpecificOrigins);

            app.UseAuthentication();
            app.UseAuthorization();

            app.UseLoggingMiddleware();
            app.UseDistributedInvocationMiddleware();
            app.UseMiddleware<AuthoriseUserMiddleware>();
            app.UseIPWhitelistMiddleware();

            var hangfireStorages = BackgroundJobQueueInitializer.GetHangfireStorages();
            for (var i = 0; i < hangfireStorages.Count; i++)
            {
                var dashboardPrefix = i == 0 ? "/hangfire" : $"/hangfire{i+1}";
                app.UseHangfireDashboard(
                    dashboardPrefix,
                    new DashboardOptions
                    {
                        Authorization = new[]
                        {
                            new Travis_backend.Startup.MyAuthorizationFilter()
                        },
                        IgnoreAntiforgeryToken = true
                    }, hangfireStorages[i]);
            }

            app.UseAzureSignalR(
                routes => { routes.MapHub<Chat>("/chat"); });

            app.UseTokenBucketLimiter();
            app.UseEndpoints(
                endpoints =>
                {
                    endpoints.MapControllerRoute("default", "{controller=Home}/{action=Index}/{id?}");

                    // endpoints.MapControllers();
                });

            Travis_backend.Startup.ConfigureSleekflowRecurringJobs(env, Configuration);
        }
    }
}