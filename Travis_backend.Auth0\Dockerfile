﻿FROM sleekflow-core-common:1.0.0 AS publish

WORKDIR "/src/Travis_backend.Auth0"
RUN dotnet publish "Travis_backend.Auth0.csproj" -c Release -o /app/publish --self-contained /p:ExcludeBuildDbMigration=TRUE

FROM mcr.microsoft.com/dotnet/aspnet:8.0.7 AS base
WORKDIR /app
EXPOSE 80
EXPOSE 443
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "Travis_backend.Auth0.dll"]

# Installing Dependencies
RUN apt-get update
RUN apt-get install -y ffmpeg