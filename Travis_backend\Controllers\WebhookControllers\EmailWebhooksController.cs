﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Travis_backend.Cache;
using Travis_backend.Constants;
using Travis_backend.ConversationDomain.ConversationResolver;
using Travis_backend.ConversationDomain.Models;
using Travis_backend.ConversationServices;
using Travis_backend.Database;
using Travis_backend.Enums;
using Travis_backend.Helpers;
using Travis_backend.MessageDomain.Models;
using Travis_backend.MessageDomain.ViewModels;

namespace Travis_backend.Controllers.WebhookControllers
{
    public class EmailWebhooksController : Controller
    {
        private readonly ApplicationDbContext _appDbContext;
        private readonly ILogger _logger;
        private readonly IConversationMessageService _messagingService;
        private readonly IConversationResolver _conversationResolver;
        private readonly ILockService _lockService;

        public EmailWebhooksController(
            ApplicationDbContext appDbContext,
            ILogger<EmailWebhooksController> logger,
            IConversationMessageService messagingService,
            IConversationResolver conversationResolver,
            ILockService lockService)
        {
            _appDbContext = appDbContext;
            _logger = logger;
            _messagingService = messagingService;
            _conversationResolver = conversationResolver;
            _lockService = lockService;
        }

        [HttpPost]
        [Route("email/webhook/{companyId}")]
        public async Task<IActionResult> Post(string companyId)
        {
            try
            {
                var sg_event_id = Request.Form["sg_event_id"];
                var header = Request.Form["headers"];
                var subject = Request.Form["SUBJECT"];
                var CHARSETS = Request.Form["CHARSETS"];
                var TO = Request.Form["TO"];
                var CC = Request.Form["CC"];
                var FROM = Request.Form["FROM"];
                var text = Request.Form["TEXT"];
                var ATTACHMENTS = Request.Form["ATTACHMENTS"];
                var charsets = Request.Form["charsets"];

                _logger.LogInformation(
                    $"{sg_event_id}_{header}_{subject}_{CHARSETS}_{TO}_{CC}_{FROM}_{text}_{ATTACHMENTS}_{charsets}");

                var emailCharsets = JsonConvert.DeserializeObject<EmailCharsets>(charsets);
                var message = $"Encoded {emailCharsets.Text}; {text};";
                switch (emailCharsets.Text)
                {
                    case "gb18030":
                    case "big5":
                        var outputFile = TempFileHelper.GetTempFileFullPath("media");

                        try
                        {
                            System.IO.File.WriteAllText(outputFile, text, Encoding.GetEncoding(emailCharsets.Text));
                            var str = System.IO.File.ReadAllText(outputFile, Encoding.UTF8);

                            text = str;
                        }
                        catch (Exception ex)
                        {
                            message += $"Convert Error: {ex.Message}";
                            text = Encoding.UTF8.GetString(Encoding.GetEncoding(emailCharsets.Text).GetBytes(text));
                        }
                        finally
                        {
                            TempFileHelper.DeleteFileWithRetry(outputFile);
                        }

                        break;
                    default:
                        text = Encoding.UTF8.GetString(Encoding.GetEncoding(emailCharsets.Text).GetBytes(text));
                        break;
                }

                var attachmentCount = 0;
                int.TryParse(ATTACHMENTS, out attachmentCount);

                string emailAddress = FROM.ToString();
                string extracted_email = emailAddress.Substring(
                    emailAddress.IndexOf('<') + 1,
                    emailAddress.IndexOf('>') - emailAddress.IndexOf('<') - 1);
                string name = emailAddress.Substring(0, emailAddress.IndexOf('<') - 1);

                string UniqueID = sha256_hash(header);

                var domainOfTo = TO.ToString().Substring(TO.ToString().LastIndexOf('@') + 1).Replace(">", string.Empty);

                var company = await _appDbContext.CompanyCompanies
                    .Include(x => x.EmailConfig)
                    .FirstOrDefaultAsync(x => x.Id == companyId);

                var (conversation, myLock) = await _conversationResolver.GetConversationForReceivingMessageAsync(
                    companyId,
                    ChannelTypes.Email,
                    company.EmailConfig?.Email,
                    extracted_email,
                    name);

                conversation.EmailAddress.CC = CC;
                conversation.EmailAddress.To = TO;

                var conversationMessage = new ConversationMessage()
                {
                    MessageContent = $"Subject: {subject}\nFrom: {conversation.EmailAddress.Email}\nTo: {TO}\nCC: {CC}\n\n{text}",
                    Channel = ChannelTypes.Email,
                    EmailFrom = conversation.EmailAddress,
                    EmailTo = TO,
                    EmailCC = CC,
                    MessageType = "text",
                    Subject = subject,
                    MessageUniqueID = UniqueID,
                    ChannelStatusMessage = message
                };

                if (!await _appDbContext.ConversationMessages
                        .AnyAsync(x => x.MessageUniqueID == conversationMessage.MessageUniqueID))
                {
                    if (attachmentCount > 0)
                    {
                        conversationMessage.MessageType = "file";
                        var conversationMessageViewModel = new ConversationMessageViewModel()
                        {
                            files = new List<IFormFile>()
                        };
                        for (var i = 1; i <= attachmentCount; i++)
                        {
                            var file = Request.Form.Files[$"attachment{i}"];
                            conversationMessageViewModel.files.Add(file);
                        }

                        try
                        {
                            await _messagingService.SendFileMessage(
                                conversation,
                                conversationMessage,
                                conversationMessageViewModel);
                        }
                        catch
                        {
                            conversationMessage.MessageType = "text";
                            await _messagingService.SendMessage(conversation, conversationMessage);
                        }
                    }
                    else
                    {
                        await _messagingService.SendMessage(conversation, conversationMessage);
                    }

                    await _lockService.ReleaseLockAsync(myLock);

                    return Ok();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Receive email error: {CompanyId}. {ExceptionMessage}",
                    companyId,
                    ex.Message);
            }

            return BadRequest();
        }

        private String sha256_hash(string value)
        {
            StringBuilder Sb = new StringBuilder();

            using (var hash = System.Security.Cryptography.SHA256.Create())
            {
                Encoding enc = Encoding.UTF8;
                Byte[] result = hash.ComputeHash(enc.GetBytes(value));

                foreach (Byte b in result)
                {
                    Sb.Append(b.ToString("x2"));
                }
            }

            return Sb.ToString();
        }
    }
}