﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Polly;
using Travis_backend.ChannelDomain.Models;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.CoreDomain.Models;
using Travis_backend.Database;
using Travis_backend.Enums;
using Travis_backend.Extensions;
using Travis_backend.Helpers;
using Travis_backend.InternalDomain.ViewModels;
using Travis_backend.SubscriptionPlanDomain.Models;
using WABA360Dialog;
using WABA360Dialog.PartnerClient.Exceptions;
using WABA360Dialog.PartnerClient.Models;
using WABA360Dialog.PartnerClient.Payloads;
using WABA360Dialog.PartnerClient.Payloads.Models;

namespace Travis_backend.ConversationServices;

public interface ICoreWhatsApp360DialogUsageService
{
    Task<bool> EnablePartnerPayment(string companyId, string partnerId, string clientId, decimal markupPercentage = 0);

    Task<bool> EnableDirectPayment(string companyId, string partnerId, string clientId);

    Task CalculateUsage(long usageId);

    Task ReCalculateUsageFromTransactionLogs(long usageId);

    Task NewPhoneNumberAdded(string clientId, string channelId);

    Task InitialPhoneNumberBillings(long usageRecordId, string clientId);

    Task CurrentPhoneNumberBillings(List<string> clientIds = null);

    Task MonthlyPhoneNumberBillings(List<string> clientIds = null);

    Task MonthlyConversationBillings(List<string> clientIds = null);

    Task DailyConversationBillings(List<string> clientIds = null);

    Task UpdateDirectPaymentAccountBalance(List<string> clientIds = null);

    Task PreviousMonthlyConversationBillings(string clientId, string period, long? usageRecordId = null);

    Task<List<Whatsapp360dialogClientUsage>> GetAllPartnerClientsUsage(
        DateTime start,
        DateTime end,
        List<string> partnerIds = null,
        List<string> clientIds = null);
}

public class CoreWhatsApp360DialogUsageService : ICoreWhatsApp360DialogUsageService
{
    private readonly ApplicationDbContext _appDbContext;
    private readonly ILogger<CoreWhatsApp360DialogUsageService> _logger;
    private readonly ICoreWhatsApp360DialogPartnerAuthService _coreWhatsApp360DialogPartnerAuthService;

    public CoreWhatsApp360DialogUsageService(
        ApplicationDbContext appDbContext,
        ILogger<CoreWhatsApp360DialogUsageService> logger,
        ICoreWhatsApp360DialogPartnerAuthService coreWhatsApp360DialogPartnerAuthService)
    {
        _appDbContext = appDbContext;
        _logger = logger;
        _coreWhatsApp360DialogPartnerAuthService = coreWhatsApp360DialogPartnerAuthService;
    }

    public async Task<bool> EnablePartnerPayment(
        string companyId,
        string partnerId,
        string clientId,
        decimal markupPercentage = 0)
    {
        if (await _appDbContext.CompanyWhatsApp360DialogUsageRecords.AnyAsync(
                x => x.PartnerId == partnerId
                     && x.Waba360DialogClientId == clientId
                     && x.TopUpMode == TopUpMode.PartnerPayment))
        {
            return false;
        }

        var company = await _appDbContext.CompanyCompanies
            .AsNoTracking()
            .Include(x => x.WhatsApp360DialogUsageRecords)
            .FirstOrDefaultAsync(x => x.Id == companyId);

        if (company == null)
        {
            return false;
        }

        if (company.WhatsApp360DialogUsageRecords.Any(
                x => x.PartnerId == partnerId && x.Waba360DialogClientId == clientId &&
                     x.TopUpMode == TopUpMode.PartnerPayment && x.CompanyId != null))
        {
            return false;
        }

        var credential =
            await _coreWhatsApp360DialogPartnerAuthService.GetWhatsApp360DialogPartnerAuthCredentialsAsync(partnerId);
        var token = await _coreWhatsApp360DialogPartnerAuthService.GetAuthTokenAsync(partnerId);

        var partnerApiClient = new WABA360DialogPartnerClient(new PartnerInfo(token.PartnerId), token.AccessToken);
        var clientsResponse = await partnerApiClient.GetPartnerClientsAsync(
            filters: new GetPartnerClientsFilter()
            {
                Id = clientId
            });

        var clientInfo = clientsResponse.Clients.FirstOrDefault(x => x.Id == clientId);

        if (clientInfo == null)
        {
            return false;
        }

        // var topUpConfig = new CompanyWhatsapp360DialogTopUpConfig()
        // {
        //     PartnerId = credential.PartnerId,
        //     ClientId = clientInfo.Id,
        //     TopUpMode = credential.TopUpMode,
        //     CompanyId = company.Id
        // };
        //
        // _appDbContext.CompanyWhatsapp360DialogTopUpConfigs.Add(topUpConfig);
        var usageRecord = new WhatsApp360DialogUsageRecord()
        {
            Waba360DialogClientId = clientInfo.Id,
            Waba360DialogClientName = clientInfo.Name,
            Waba360DialogClientCreatedAt = clientInfo.CreatedAt,
            TopUpMode = TopUpMode.PartnerPayment,
            CanGenerateMarkupRecord = true,
            PartnerId = credential.PartnerId,
            Currency = "USD",
            Credit = 0,
            Used = 0,
            UpcomingCharges = 0,
            PendingCharges = 0,
            CompanyId = company.Id,
            MarkupPercentage = markupPercentage,
        };

        _appDbContext.CompanyWhatsApp360DialogUsageRecords.Add(usageRecord);

        await _appDbContext.SaveChangesAsync();

        await InitialPhoneNumberBillings(usageRecord.Id, usageRecord.Waba360DialogClientId);
        await CurrentPhoneNumberBillings(
            new List<string>()
            {
                usageRecord.Waba360DialogClientId
            });

        return true;
    }

    public async Task<bool> EnableDirectPayment(string companyId, string partnerId, string clientId)
    {
        var company = await _appDbContext.CompanyCompanies
            .AsNoTracking()
            .Include(x => x.WhatsApp360DialogUsageRecords)
            .FirstOrDefaultAsync(x => x.Id == companyId);

        if (company == null)
        {
            return false;
        }

        if (company.WhatsApp360DialogUsageRecords.Any(
                x => x.PartnerId == partnerId && x.Waba360DialogClientId == clientId &&
                     x.TopUpMode == TopUpMode.DirectPayment))
        {
            return false;
        }

        var credential =
            await _coreWhatsApp360DialogPartnerAuthService.GetWhatsApp360DialogPartnerAuthCredentialsAsync(partnerId);
        var token = await _coreWhatsApp360DialogPartnerAuthService.GetAuthTokenAsync(partnerId);

        var partnerApiClient = new WABA360DialogPartnerClient(new PartnerInfo(token.PartnerId), token.AccessToken);
        var clientsResponse = await partnerApiClient.GetPartnerClientsAsync(
            filters: new GetPartnerClientsFilter()
            {
                Id = clientId
            });

        var clientInfo = clientsResponse.Clients.FirstOrDefault(x => x.Id == clientId);

        if (clientInfo == null)
        {
            return false;
        }

        var today = DateTime.UtcNow;

        var clientBalanceResponse = await partnerApiClient.GetClientBalanceAsync(
            clientId,
            today.StartOfMonth().ToUnixTime(),
            today.EndOfMonth().AddDays(1).ToUnixTime(),
            "month");

        var usageRecord = new WhatsApp360DialogUsageRecord()
        {
            Waba360DialogClientId = clientInfo.Id,
            Waba360DialogClientName = clientInfo.Name,
            Waba360DialogClientCreatedAt = clientInfo.CreatedAt,
            PartnerId = credential.PartnerId,
            Currency = clientBalanceResponse.Currency.ToUpper(),
            DirectPaymentBalance = clientBalanceResponse.Balance,
            CompanyId = company.Id,
            TopUpMode = TopUpMode.DirectPayment,
            CanGenerateMarkupRecord = false
        };

        _appDbContext.CompanyWhatsApp360DialogUsageRecords.Add(usageRecord);

        await _appDbContext.SaveChangesAsync();

        return true;
    }

    public async Task CalculateUsage(long usageId)
    {
        var usageRecord =
            await _appDbContext.CompanyWhatsApp360DialogUsageRecords.FirstOrDefaultAsync(x => x.Id == usageId);

        if (usageRecord == null)
        {
            return;
        }

        var balanceBefore = usageRecord.Balance;

        var transactionLogs = await _appDbContext.CompanyWhatsApp360DialogUsageTransactionLogs
            .Where(x => x.WhatsApp360DialogUsageRecordId == usageId && !x.IsMarkedInUsageRecord)
            .OrderBy(x => x.CreatedAt)
            .ToListAsync();

        var currencyExchangeRates = await _appDbContext.CmsCurrencyExchangeRates
            .ToListAsync();

        foreach (var transactionLog in transactionLogs)
        {
            var exchangeRate = currencyExchangeRates.First(
                x => x.CurrencyFrom == transactionLog.Currency && x.CurrencyTo == usageRecord.Currency);
            transactionLog.ToUsageCurrencyExchangeRate = exchangeRate.ExchangeRate;

            switch (transactionLog.TransactionType)
            {
                case WhatsApp360DialogUsageTransactionType.TopUp:
                    usageRecord.Credit += transactionLog.Total * transactionLog.ToUsageCurrencyExchangeRate;

                    break;
                case WhatsApp360DialogUsageTransactionType.GeneralFee:
                    usageRecord.Used += transactionLog.Total * transactionLog.ToUsageCurrencyExchangeRate;

                    break;
                case WhatsApp360DialogUsageTransactionType.ConversationUsage:
                    usageRecord.Used += transactionLog.Total * transactionLog.ToUsageCurrencyExchangeRate;

                    break;
                case WhatsApp360DialogUsageTransactionType.PeriodPhoneNumberFee:
                    usageRecord.Used += transactionLog.Total * transactionLog.ToUsageCurrencyExchangeRate;

                    break;
                case WhatsApp360DialogUsageTransactionType.NewPhoneNumberFee:
                    usageRecord.Used += transactionLog.Total * transactionLog.ToUsageCurrencyExchangeRate;

                    break;
                default:
                    throw new ArgumentOutOfRangeException();
            }

            transactionLog.IsMarkedInUsageRecord = true;

            transactionLog.UpdatedAt = DateTime.UtcNow;
            usageRecord.UpdatedAt = DateTime.UtcNow;
        }

        await _appDbContext.SaveChangesAsync();

        if (balanceBefore != usageRecord.Balance)
        {
            await OnBalanceChanged(usageRecord.Waba360DialogClientId);
        }
    }

    public async Task OnBalanceChanged(string clientId)
    {
        var usages = await _appDbContext
            .CompanyWhatsApp360DialogUsageRecords
            .AsNoTracking()
            .Where(x => x.Waba360DialogClientId == clientId && x.CompanyId != null)
            .ToListAsync();

        // Suspend All
        if (usages.All(x => x.Balance < 0))
        {
            await _appDbContext
                .ConfigWhatsApp360DialogConfigs
                .Where(x => x.ClientId == clientId && !x.IsSuspended)
                .ExecuteUpdateAsync(calls => calls.SetProperty(p => p.IsSuspended, true));
        }

        // Activate All
        else if (usages.All(x => x.Balance > 0))
        {
            await _appDbContext
                .ConfigWhatsApp360DialogConfigs
                .Where(x => x.ClientId == clientId && x.IsSuspended)
                .ExecuteUpdateAsync(calls => calls.SetProperty(p => p.IsSuspended, false));
        }
    }

    public async Task ReCalculateUsageFromTransactionLogs(long usageId)
    {
        var usageRecord = await _appDbContext.CompanyWhatsApp360DialogUsageRecords.FirstAsync(x => x.Id == usageId);

        await _appDbContext.CompanyWhatsApp360DialogUsageTransactionLogs
            .Where(x => x.WhatsApp360DialogUsageRecordId == usageId)
            .ExecuteUpdateAsync(calls => calls.SetProperty(p => p.IsMarkedInUsageRecord, false));

        usageRecord.Credit = 0;
        usageRecord.Used = 0;
        await _appDbContext.SaveChangesAsync();

        await CalculateUsage(usageId);
    }

    public async Task CurrentPhoneNumberBillings(List<string> clientIds = null)
    {
        // should be run at every start of the month, then calculate billing of starting month
        var currentBillingPeriod = DateTime.UtcNow.ToString("yyyy-MM");

        var usageRecordsConfigs = await _appDbContext.CompanyWhatsApp360DialogUsageRecords
            .WhereIf(clientIds is { Count: > 0 }, x => clientIds.Contains(x.Waba360DialogClientId))
            .Where(
                x => x.LastPhoneNumberBillingPeriod != currentBillingPeriod &&
                     x.TopUpMode == TopUpMode.PartnerPayment && x.CompanyId != null)
            .ToListAsync();

        var currencyExchangeRates = await _appDbContext.CmsCurrencyExchangeRates
            .ToListAsync();

        var phoneNumberCost = await _appDbContext.Cms360DialogItemCosts
            .FirstAsync(x => x.Cms360DialogCostItemType == Cms360DialogCostItemType.PhoneNumber);

        foreach (var usageRecord in usageRecordsConfigs)
        {
            try
            {
                var token = await _coreWhatsApp360DialogPartnerAuthService.GetAuthTokenAsync(usageRecord.PartnerId);

                var partnerApiClient = new WABA360DialogPartnerClient(
                    new PartnerInfo(token.PartnerId),
                    token.AccessToken);
                var channelsResponse = await partnerApiClient.GetPartnerChannelsAsync(
                    filters: new GetPartnerChannelsFilter()
                    {
                        ClientId = usageRecord.Waba360DialogClientId
                    });
                var chargeableClientChannels = channelsResponse.PartnerChannels.Where(
                    x => x.ClientId == usageRecord.Waba360DialogClientId &&
                         (!x.TerminatedAt.HasValue || DateTime.UtcNow < x.TerminatedAt.Value)).ToList();

                usageRecord.CurrentPhoneNumberPeriodUsage = new CurrentPhoneNumberPeriodUsage
                {
                    ChargeablePhoneNumberQuantity = chargeableClientChannels.Count,
                    Currency = phoneNumberCost.Currency,
                    UnitPrice = phoneNumberCost.UnitPrice,
                    ChannelIds = chargeableClientChannels.Select(x => x.Id).ToList(),
                    BillingPeriod = currentBillingPeriod,
                    UpdatedAt = DateTime.UtcNow
                };

                var exchangeRate = currencyExchangeRates.First(
                    x => x.CurrencyFrom == phoneNumberCost.Currency.ToUpper() && x.CurrencyTo == usageRecord.Currency.ToUpper());

                usageRecord.UpcomingCharges = usageRecord.CurrentPhoneNumberPeriodUsage.ChargeablePhoneNumberQuantity *
                                              usageRecord.CurrentPhoneNumberPeriodUsage.UnitPrice *
                                              exchangeRate.ExchangeRate;

                await _appDbContext.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[CoreWhatsApp360DialogUsage {MethodName}] Company {CompanyId} error syncing current phone number billings for usage record id {UsageRecordId}" +
                    "for 360dialog client id {Waba360DialogClientId}. {ExceptionMessage}",
                    nameof(CurrentPhoneNumberBillings),
                    usageRecord.CompanyId,
                    usageRecord.Id,
                    usageRecord.Waba360DialogClientId,
                    ex.Message);
            }
        }
    }

    public async Task InitialPhoneNumberBillings(long usageRecordId, string clientId)
    {
        // should be run at every start of the month, then calculate billing of starting month
        var usageRecordsConfigs = await _appDbContext.CompanyWhatsApp360DialogUsageRecords
            .Where(
                x => x.Id == usageRecordId && x.Waba360DialogClientId == clientId &&
                     x.TopUpMode == TopUpMode.PartnerPayment && x.CompanyId != null)
            .ToListAsync();

        var phoneNumberCost = await _appDbContext.Cms360DialogItemCosts
            .FirstAsync(x => x.Cms360DialogCostItemType == Cms360DialogCostItemType.PhoneNumber);

        var newBillingPeriod = DateTime.UtcNow.AddMonths(-1).ToString("yyyy-MM");
        var lastBillingPeriodDateTime = DateTime.ParseExact(newBillingPeriod + "-01", "yyyy-MM-dd", null).AddMonths(1)
            .AddDays(-1).EndOfDay();

        foreach (var usageRecord in usageRecordsConfigs)
        {
            try
            {
                var token = await _coreWhatsApp360DialogPartnerAuthService.GetAuthTokenAsync(usageRecord.PartnerId);

                var partnerApiClient = new WABA360DialogPartnerClient(
                    new PartnerInfo(token.PartnerId),
                    token.AccessToken);
                var channelsResponse = await partnerApiClient.GetPartnerChannelsAsync(
                    filters: new GetPartnerChannelsFilter()
                    {
                        ClientId = usageRecord.Waba360DialogClientId
                    });
                var chargeableClientChannels = channelsResponse.PartnerChannels
                    .Where(x => x.ClientId == usageRecord.Waba360DialogClientId).ToList();

                foreach (var newClientChannel in chargeableClientChannels)
                {
                    var billingDate = newClientChannel.BillingStartedAt.Value;

                    while (billingDate <= lastBillingPeriodDateTime)
                    {
                        if (newClientChannel.TerminatedAt.HasValue && billingDate > newClientChannel.TerminatedAt.Value)
                        {
                            break;
                        }

                        var currentBillingPeriod = billingDate.ToString("yyyy-MM");

                        await PreviousMonthlyConversationBillings(clientId, currentBillingPeriod, usageRecordId);
                        var isFirstMonth = billingDate == newClientChannel.BillingStartedAt.Value;

                        if (isFirstMonth)
                        {
                            var daysInMonth = DateTime.DaysInMonth(
                                newClientChannel.BillingStartedAt.Value.Year,
                                newClientChannel.BillingStartedAt.Value.Month);

                            var remainingDateInMonth = daysInMonth - newClientChannel.BillingStartedAt.Value.Day + 1;
                            var remainingDatePercentage = (float) remainingDateInMonth / daysInMonth;

                            var transactionLog = new WhatsApp360DialogUsageTransactionLog
                            {
                                Total = Math.Round(
                                    phoneNumberCost.UnitPrice * (decimal) remainingDatePercentage,
                                    2), // with pro rata
                                Currency = phoneNumberCost.Currency,
                                TransactionType = WhatsApp360DialogUsageTransactionType.NewPhoneNumberFee,
                                CompanyId = usageRecord.CompanyId,
                                IsMarkedInUsageRecord = false,
                                BillingPeriod = currentBillingPeriod,
                                NewPhoneNumberFee = new Whatsapp360DialogNewPhoneNumberFee
                                {
                                    UnitPrice = phoneNumberCost.UnitPrice,
                                    BillingPeriod = currentBillingPeriod,
                                    DaysInMonth = daysInMonth,
                                    RemainingDateInMonth = remainingDateInMonth,
                                    BillingStartedAt = newClientChannel.BillingStartedAt.Value,
                                    ChannelId = newClientChannel.Id,
                                    PhoneNumber = newClientChannel?.SetupInfo?.PhoneNumber
                                }
                            };

                            usageRecord.TransactionLogs.Add(transactionLog);
                        }
                        else
                        {
                            var transactionLog = new WhatsApp360DialogUsageTransactionLog
                            {
                                Total = phoneNumberCost.UnitPrice,
                                Currency = phoneNumberCost.Currency,
                                TransactionType = WhatsApp360DialogUsageTransactionType.PeriodPhoneNumberFee,
                                CompanyId = usageRecord.CompanyId,
                                PhoneNumberPeriodUsage = new Whatsapp360DialogPhoneNumberPeriodUsage
                                {
                                    ChargeablePhoneNumberQuantity = 1,
                                    UnitPrice = phoneNumberCost.UnitPrice,
                                    BillingPeriod = currentBillingPeriod,
                                    ChannelIds = new List<string>()
                                    {
                                        newClientChannel.Id
                                    },
                                    PhoneNumbers = new List<string>()
                                    {
                                        newClientChannel?.SetupInfo?.PhoneNumber
                                    },
                                },
                                IsMarkedInUsageRecord = false,
                                BillingPeriod = currentBillingPeriod
                            };

                            usageRecord.TransactionLogs.Add(transactionLog);
                        }

                        var nextBillingDate = billingDate.AddMonths(1);
                        billingDate = new DateTime(nextBillingDate.Year, nextBillingDate.Month, 1);
                    }
                }

                usageRecord.UpdatedAt = DateTime.UtcNow;

                await _appDbContext.SaveChangesAsync();
                await CalculateUsage(usageRecord.Id);
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[CoreWhatsApp360DialogUsage {MethodName}] Company {CompanyId} error initializing phone number billings " +
                    "for usage record id {UsageRecordId} for 360dialog client id {Waba360DialogClientId}. {ExceptionMessage}",
                    nameof(InitialPhoneNumberBillings),
                    usageRecord.CompanyId,
                    usageRecordId,
                    usageRecord.Waba360DialogClientId,
                    ex.Message);
            }
        }
    }

    public async Task NewPhoneNumberAdded(string clientId, string channelId)
    {
        // should be run at every start of the month, then calculate billing of starting month
        var currentBillingPeriodDateTime = DateTime.UtcNow;
        var currentBillingPeriod = currentBillingPeriodDateTime.ToString("yyyy-MM");

        var usageRecord = await _appDbContext.CompanyWhatsApp360DialogUsageRecords
            .Where(
                x => x.Waba360DialogClientId == clientId && x.TopUpMode == TopUpMode.PartnerPayment &&
                     x.CompanyId != null)
            .FirstOrDefaultAsync();

        var phoneNumberCost = await _appDbContext.Cms360DialogItemCosts
            .FirstAsync(x => x.Cms360DialogCostItemType == Cms360DialogCostItemType.PhoneNumber);

        var token = await _coreWhatsApp360DialogPartnerAuthService.GetAuthTokenAsync(usageRecord.PartnerId);

        var partnerApiClient = new WABA360DialogPartnerClient(new PartnerInfo(token.PartnerId), token.AccessToken);
        var channelsResponse = await partnerApiClient.GetPartnerChannelsAsync(
            filters: new GetPartnerChannelsFilter()
            {
                ClientId = usageRecord.Waba360DialogClientId
            });
        var chargeableClientChannels = channelsResponse.PartnerChannels.Where(
            x => x.ClientId == usageRecord.Waba360DialogClientId &&
                 (!x.TerminatedAt.HasValue || DateTime.UtcNow < x.TerminatedAt.Value)).ToList();

        var newClientChannel = chargeableClientChannels.FirstOrDefault(x => x.Id == channelId);

        if (newClientChannel == null)
        {
            return;
        }

        if (usageRecord.CurrentPhoneNumberPeriodUsage.ChannelIds.Contains(channelId))
        {
            return;
        }

        var daysInMonth = DateTime.DaysInMonth(
            newClientChannel.BillingStartedAt.Value.Year,
            newClientChannel.BillingStartedAt.Value.Month);

        var remainingDateInMonth = daysInMonth - newClientChannel.BillingStartedAt.Value.Day + 1;
        var remainingDatePercentage = (float) remainingDateInMonth / daysInMonth;

        var transactionLog = new WhatsApp360DialogUsageTransactionLog
        {
            Total = phoneNumberCost.UnitPrice * (decimal) remainingDatePercentage, // with pro rata
            Currency = phoneNumberCost.Currency,
            TransactionType = WhatsApp360DialogUsageTransactionType.NewPhoneNumberFee,
            CompanyId = usageRecord.CompanyId,
            IsMarkedInUsageRecord = false,
            BillingPeriod = currentBillingPeriod,
            NewPhoneNumberFee = new Whatsapp360DialogNewPhoneNumberFee
            {
                UnitPrice = phoneNumberCost.UnitPrice,
                BillingPeriod = currentBillingPeriod,
                DaysInMonth = daysInMonth,
                RemainingDateInMonth = remainingDateInMonth,
                BillingStartedAt = newClientChannel.BillingStartedAt.Value,
                ChannelId = newClientChannel.Id,
                PhoneNumber = newClientChannel?.SetupInfo?.PhoneNumber
            }
        };

        usageRecord.TransactionLogs.Add(transactionLog);

        usageRecord.UpdatedAt = DateTime.UtcNow;

        await _appDbContext.SaveChangesAsync();

        await CalculateUsage(usageRecord.Id);

        await CurrentPhoneNumberBillings(
            new List<string>()
            {
                clientId
            });
    }

    public async Task MonthlyPhoneNumberBillings(List<string> clientIds = null)
    {
        // should be run at every start of the month, then calculate billing of starting month
        var newBillingPeriod = DateTime.UtcNow.AddMonths(-1).ToString("yyyy-MM");
        var currentBillingPeriod = DateTime.UtcNow.ToString("yyyy-MM");
        var lastBillingPeriodDateTime = DateTime.ParseExact(newBillingPeriod + "-01", "yyyy-MM-dd", null);
        var currentBillingPeriodDateTime = DateTime.ParseExact(currentBillingPeriod + "-01", "yyyy-MM-dd", null);

        var usageRecordsConfigs = await _appDbContext.CompanyWhatsApp360DialogUsageRecords
            .WhereIf(clientIds is { Count: > 0 }, x => clientIds.Contains(x.Waba360DialogClientId))
            .Where(
                x => x.LastPhoneNumberBillingPeriod != newBillingPeriod && x.TopUpMode == TopUpMode.PartnerPayment &&
                     x.CompanyId != null)
            .ToListAsync();

        var phoneNumberCost = await _appDbContext.Cms360DialogItemCosts
            .FirstAsync(x => x.Cms360DialogCostItemType == Cms360DialogCostItemType.PhoneNumber);

        foreach (var usageRecord in usageRecordsConfigs)
        {
            try
            {
                var token = await _coreWhatsApp360DialogPartnerAuthService.GetAuthTokenAsync(usageRecord.PartnerId);

                var partnerApiClient = new WABA360DialogPartnerClient(
                    new PartnerInfo(token.PartnerId),
                    token.AccessToken);
                var channelsResponse = await partnerApiClient.GetPartnerChannelsAsync(
                    filters: new GetPartnerChannelsFilter()
                    {
                        ClientId = usageRecord.Waba360DialogClientId
                    });
                var clientChannel = channelsResponse.PartnerChannels
                    .Where(x => x.ClientId == usageRecord.Waba360DialogClientId).ToList();
                var periodClientChannels = clientChannel.Where(
                    x => x.BillingStartedAt < lastBillingPeriodDateTime && !x.TerminatedAt.HasValue).ToList();
                var terminatedButRunningClientChannels = clientChannel
                    .Where(x => x.TerminatedAt.HasValue && lastBillingPeriodDateTime < x.TerminatedAt).ToList();
                var newAddedClientChannels = clientChannel.Where(
                    x => x.BillingStartedAt > lastBillingPeriodDateTime &&
                         x.BillingStartedAt < currentBillingPeriodDateTime).ToList();

                // Period
                {
                    var fullPeriodClientChannels = new List<PartnerChannel>();
                    fullPeriodClientChannels.AddRange(periodClientChannels);
                    fullPeriodClientChannels.AddRange(terminatedButRunningClientChannels);

                    var transactionLog = new WhatsApp360DialogUsageTransactionLog
                    {
                        Total = fullPeriodClientChannels.Count * phoneNumberCost.UnitPrice,
                        Currency = phoneNumberCost.Currency,
                        TransactionType = WhatsApp360DialogUsageTransactionType.PeriodPhoneNumberFee,
                        CompanyId = usageRecord.CompanyId,
                        PhoneNumberPeriodUsage = new Whatsapp360DialogPhoneNumberPeriodUsage
                        {
                            ChargeablePhoneNumberQuantity = fullPeriodClientChannels.Count,
                            UnitPrice = phoneNumberCost.UnitPrice,
                            BillingPeriod = newBillingPeriod,
                            ChannelIds = fullPeriodClientChannels.Select(x => x.Id).ToList(),
                            PhoneNumbers = fullPeriodClientChannels.Select(x => x?.SetupInfo?.PhoneNumber).ToList(),
                        },
                        IsMarkedInUsageRecord = false,
                        BillingPeriod = newBillingPeriod
                    };

                    usageRecord.TransactionLogs.Add(transactionLog);
                }

                // new
                {
                    foreach (var newClientChannel in newAddedClientChannels)
                    {
                        var daysInMonth = DateTime.DaysInMonth(
                            newClientChannel.BillingStartedAt.Value.Year,
                            newClientChannel.BillingStartedAt.Value.Month);

                        var remainingDateInMonth = daysInMonth - newClientChannel.BillingStartedAt.Value.Day + 1;
                        var remainingDatePercentage = (float) remainingDateInMonth / daysInMonth;

                        var transactionLog = new WhatsApp360DialogUsageTransactionLog
                        {
                            Total = phoneNumberCost.UnitPrice * (decimal) remainingDatePercentage, // with pro rata
                            Currency = phoneNumberCost.Currency,
                            TransactionType = WhatsApp360DialogUsageTransactionType.NewPhoneNumberFee,
                            CompanyId = usageRecord.CompanyId,
                            IsMarkedInUsageRecord = false,
                            BillingPeriod = newBillingPeriod,
                            NewPhoneNumberFee = new Whatsapp360DialogNewPhoneNumberFee
                            {
                                UnitPrice = phoneNumberCost.UnitPrice,
                                BillingPeriod = newBillingPeriod,
                                DaysInMonth = daysInMonth,
                                RemainingDateInMonth = remainingDateInMonth,
                                BillingStartedAt = newClientChannel.BillingStartedAt.Value,
                                ChannelId = newClientChannel.Id,
                                PhoneNumber = newClientChannel?.SetupInfo?.PhoneNumber
                            }
                        };
                        usageRecord.TransactionLogs.Add(transactionLog);
                    }
                }

                usageRecord.LastPhoneNumberBillingPeriod = newBillingPeriod;

                usageRecord.UpdatedAt = DateTime.UtcNow;

                await _appDbContext.SaveChangesAsync();

                await CalculateUsage(usageRecord.Id);
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[CoreWhatsApp360DialogUsage {MethodName}] Company {CompanyId} error updating monthly billings for" +
                    " record id {UsageRecordId} for 360dialog client id {Waba360DialogClientId}. {ExceptionMessage}",
                    nameof(MonthlyPhoneNumberBillings),
                    usageRecord.CompanyId,
                    usageRecord.Id,
                    usageRecord.Waba360DialogClientId,
                    ex.Message);
            }
        }
    }

    public async Task DailyConversationBillings(List<string> clientIds = null)
    {
        var currentBillingPeriodDateTime = DateTime.UtcNow;
        var currentBillingPeriod = currentBillingPeriodDateTime.ToString("yyyy-MM");

        var usageRecordsConfigs = await _appDbContext.CompanyWhatsApp360DialogUsageRecords
            .WhereIf(clientIds is { Count: > 0 }, x => clientIds.Contains(x.Waba360DialogClientId))
            .Where(x => x.TopUpMode == TopUpMode.PartnerPayment)
            .ToListAsync();

        var currencyExchangeRates = await _appDbContext.CmsCurrencyExchangeRates
            .ToListAsync();

        foreach (var usageRecord in usageRecordsConfigs)
        {
            var balanceBefore = usageRecord.Balance;

            try
            {
                var token = await _coreWhatsApp360DialogPartnerAuthService.GetAuthTokenAsync(usageRecord.PartnerId);

                var partnerApiClient = new WABA360DialogPartnerClient(
                    new PartnerInfo(token.PartnerId),
                    token.AccessToken);
                var clientBalance = await partnerApiClient.GetClientBalanceAsync(
                    usageRecord.Waba360DialogClientId,
                    currentBillingPeriodDateTime.StartOfMonth().ToUnixTime(),
                    currentBillingPeriodDateTime.EndOfMonth().AddDays(1).ToUnixTime(),
                    "month");

                var usage = clientBalance.Usage.First(x => x.PeriodDate.ToString("yyyy-MM") == currentBillingPeriod);

                usageRecord.CurrentConversationPeriodUsage = new Whatsapp360DialogConversationPeriodUsage
                {
                    BusinessInitiatedPaidQuantity = usage.BusinessInitiatedPaidQuantity,
                    BusinessInitiatedPrice = usage.BusinessInitiatedPrice,
                    BusinessInitiatedQuantity = usage.BusinessInitiatedQuantity,
                    FreeEntryPoint = usage.FreeEntryPoint,
                    FreeQuantity = usage.FreeQuantity,
                    FreeTier = usage.FreeTier,
                    AuthenticationPaidQuantity = usage.AuthenticationPaidQuantity,
                    AuthenticationPrice = usage.AuthenticationPrice,
                    AuthenticationQuantity = usage.AuthenticationQuantity,
                    MarketingPaidQuantity = usage.MarketingPaidQuantity,
                    MarketingPrice = usage.MarketingPrice,
                    MarketingQuantity = usage.MarketingQuantity,
                    PaidQuantity = usage.PaidQuantity,
                    PeriodDate = usage.PeriodDate,
                    Quantity = usage.Quantity,
                    ServicePaidQuantity = usage.ServicePaidQuantity,
                    ServicePrice = usage.ServicePrice,
                    ServiceQuantity = usage.ServiceQuantity,
                    TotalPrice = usage.TotalPrice,
                    UserInitiatedPaidQuantity = usage.UserInitiatedPaidQuantity,
                    UserInitiatedPrice = usage.UserInitiatedPrice,
                    UserInitiatedQuantity = usage.UserInitiatedQuantity,
                    UtilityPaidQuantity = usage.UtilityPaidQuantity,
                    UtilityPrice = usage.UtilityPrice,
                    UtilityQuantity = usage.UtilityQuantity,
                    BillingPeriod = currentBillingPeriod
                };


                var exchangeRate = currencyExchangeRates.First(
                    x => x.CurrencyFrom == clientBalance.Currency.ToUpper() && x.CurrencyTo == usageRecord.Currency);

                usageRecord.PendingCharges =
                    usageRecord.CurrentConversationPeriodUsage.TotalPrice * exchangeRate.ExchangeRate;

                if (usageRecord.PendingCharges < 0)
                {
                    usageRecord.PendingCharges = 0;
                }

                usageRecord.UpdatedAt = DateTime.UtcNow;
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[CoreWhatsApp360DialogUsage {MethodName}] Company {CompanyId} error updating daily conversation billings for " +
                    "record id {UsageRecordId} for 360dialog client id {Waba360DialogClientId}. {ExceptionMessage}",
                    nameof(DailyConversationBillings),
                    usageRecord.CompanyId,
                    usageRecord.Id,
                    usageRecord.Waba360DialogClientId,
                    ex.Message);


                usageRecord.CurrentConversationPeriodUsage = null;
                usageRecord.PendingCharges = 0;
            }

            await _appDbContext.SaveChangesAsync();

            // Change from positive to negative / negative to positive
            if (usageRecord.Balance > 0 && balanceBefore < 0 || usageRecord.Balance < 0 && balanceBefore > 0)
            {
                await OnBalanceChanged(usageRecord.Waba360DialogClientId);
            }
        }
    }

    public async Task UpdateDirectPaymentAccountBalance(List<string> clientIds = null)
    {
        var currentBillingPeriodDateTime = DateTime.UtcNow;

        var usageRecordsConfigs = await _appDbContext.CompanyWhatsApp360DialogUsageRecords
            .WhereIf(clientIds is { Count: > 0 }, x => clientIds.Contains(x.Waba360DialogClientId))
            .Where(x => x.TopUpMode == TopUpMode.DirectPayment)
            .ToListAsync();

        foreach (var usageRecord in usageRecordsConfigs)
        {
            try
            {
                var token = await _coreWhatsApp360DialogPartnerAuthService.GetAuthTokenAsync(usageRecord.PartnerId);

                var partnerApiClient = new WABA360DialogPartnerClient(
                    new PartnerInfo(token.PartnerId),
                    token.AccessToken);
                var clientBalance = await partnerApiClient.GetClientBalanceAsync(
                    usageRecord.Waba360DialogClientId,
                    currentBillingPeriodDateTime.StartOfMonth().ToUnixTime(),
                    currentBillingPeriodDateTime.EndOfMonth().AddDays(1).ToUnixTime(),
                    "month");

                usageRecord.DirectPaymentBalance = clientBalance.Balance;
                usageRecord.Currency = clientBalance.Currency;
                usageRecord.UpdatedAt = DateTime.UtcNow;
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[CoreWhatsApp360DialogUsage {MethodName}] Company {CompanyId} error updating direct payment account" +
                    " balance for usage record id {UsageRecordId} for 360dialog client id {Waba360DialogClientId}. {ExceptionMessage}",
                    nameof(UpdateDirectPaymentAccountBalance),
                    usageRecord.CompanyId,
                    usageRecord.Id,
                    usageRecord.Waba360DialogClientId,
                    ex.Message);
            }

            await _appDbContext.SaveChangesAsync();
        }
    }

    public async Task MonthlyConversationBillings(List<string> clientIds = null)
    {
        // should be run at every start of the month, then calculate billing of last month
        var newBillingPeriodDateTime = DateTime.UtcNow.AddMonths(-1);
        var newBillingPeriod = newBillingPeriodDateTime.ToString("yyyy-MM");

        var usageRecordsConfigs = await _appDbContext.CompanyWhatsApp360DialogUsageRecords
            .WhereIf(clientIds is { Count: > 0 }, x => clientIds.Contains(x.Waba360DialogClientId))
            .Where(
                x => x.LastConversationBillingPeriod != newBillingPeriod && x.TopUpMode == TopUpMode.PartnerPayment &&
                     x.CompanyId != null)
            .ToListAsync();

        var conversationMakeupBillRecords = new List<ConversationMakeupBillRecord>();

        foreach (var usageRecord in usageRecordsConfigs)
        {
            try
            {
                var token = await _coreWhatsApp360DialogPartnerAuthService.GetAuthTokenAsync(usageRecord.PartnerId);

                var partnerApiClient = new WABA360DialogPartnerClient(
                    new PartnerInfo(token.PartnerId),
                    token.AccessToken);
                var clientBalance = await partnerApiClient.GetClientBalanceAsync(
                    usageRecord.Waba360DialogClientId,
                    newBillingPeriodDateTime.StartOfMonth().ToUnixTime(),
                    newBillingPeriodDateTime.EndOfMonth().AddDays(1).ToUnixTime(),
                    "month");

                var usage = clientBalance.Usage.First(x => x.PeriodDate.ToString("yyyy-MM") == newBillingPeriod);

                if (usage.TotalPrice < 0)
                {
                    usage.TotalPrice = 0;
                }

                var tranLog = new WhatsApp360DialogUsageTransactionLog
                {
                    Total = usage.TotalPrice,
                    Currency = clientBalance.Currency.ToUpper(),
                    TransactionType = WhatsApp360DialogUsageTransactionType.ConversationUsage,
                    CompanyId = usageRecord.CompanyId,
                    ConversationPeriodUsage = new Whatsapp360DialogConversationPeriodUsage
                    {
                        TotalPrice = usage.TotalPrice,
                        BusinessInitiatedPaidQuantity = usage.BusinessInitiatedPaidQuantity,
                        BusinessInitiatedPrice = usage.BusinessInitiatedPrice,
                        BusinessInitiatedQuantity = usage.BusinessInitiatedQuantity,
                        FreeEntryPoint = usage.FreeEntryPoint,
                        FreeQuantity = usage.FreeQuantity,
                        FreeTier = usage.FreeTier,
                        PaidQuantity = usage.PaidQuantity,
                        Quantity = usage.Quantity,
                        UserInitiatedPaidQuantity = usage.UserInitiatedPaidQuantity,
                        UserInitiatedPrice = usage.UserInitiatedPrice,
                        UserInitiatedQuantity = usage.UserInitiatedQuantity,
                        PeriodDate = usage.PeriodDate,
                        BillingPeriod = newBillingPeriod
                    },
                    IsMarkedInUsageRecord = false,
                    BillingPeriod = newBillingPeriod
                };

                usageRecord.TransactionLogs.Add(tranLog);
                usageRecord.LastConversationBillingPeriod = newBillingPeriod;
                await _appDbContext.SaveChangesAsync();

                if (usageRecord.CanGenerateMarkupRecord)
                {
                    conversationMakeupBillRecords.Add(
                        new ConversationMakeupBillRecord
                        {
                            Id = tranLog.Id,
                            CompanyId = usageRecord.CompanyId,
                            TotalPrice = usage.TotalPrice,
                            Currency = clientBalance.Currency.ToUpper(),
                            BillingPeriod = newBillingPeriod,
                            Waba360DialogClientId = usageRecord.Waba360DialogClientId
                        });
                }

                await CalculateUsage(usageRecord.Id);
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[CoreWhatsApp360DialogUsage {MethodName}] Company {CompanyId} error updating monthly billings for " +
                    "record id {UsageRecordId} for 360dialog client id {Waba360DialogClientId}. {ExceptionMessage}",
                    nameof(MonthlyConversationBillings),
                    usageRecord.CompanyId,
                    usageRecord.Id,
                    usageRecord.Waba360DialogClientId,
                    ex.Message);
            }
        }

        await CreateMarkBillRecord(conversationMakeupBillRecords);
    }

    public async Task PreviousMonthlyConversationBillings(string clientId, string period, long? usageRecordId = null)
    {
        // should be run at every start of the month, then calculate billing of last month
        var billingPeriodDateTime = DateTime.ParseExact(period + "-01", "yyyy-MM-dd", null);
        var billingPeriod = billingPeriodDateTime.ToString("yyyy-MM");

        if (DateTime.UtcNow <= billingPeriodDateTime.EndOfMonth())
        {
            return;
        }

        var usageRecord = await _appDbContext.CompanyWhatsApp360DialogUsageRecords
            .WhereIf(usageRecordId != null, record => record.Id == usageRecordId)
            .FirstOrDefaultAsync(
                x => x.Waba360DialogClientId == clientId && x.TopUpMode == TopUpMode.PartnerPayment &&
                     x.CompanyId != null);

        var conversationMakeupBillRecords = new List<ConversationMakeupBillRecord>();

        try
        {
            var token = await _coreWhatsApp360DialogPartnerAuthService.GetAuthTokenAsync(usageRecord.PartnerId);

            var partnerApiClient = new WABA360DialogPartnerClient(new PartnerInfo(token.PartnerId), token.AccessToken);
            var clientBalance = await partnerApiClient.GetClientBalanceAsync(
                usageRecord.Waba360DialogClientId,
                billingPeriodDateTime.StartOfMonth().ToUnixTime(),
                billingPeriodDateTime.EndOfMonth().AddDays(1).ToUnixTime(),
                "month");

            var usage = clientBalance.Usage.First(x => x.PeriodDate.ToString("yyyy-MM") == billingPeriod);

            if (usage.TotalPrice < 0)
            {
                usage.TotalPrice = 0;
            }

            var existingTransactionLog =
                await _appDbContext.CompanyWhatsApp360DialogUsageTransactionLogs.FirstOrDefaultAsync(
                    x =>
                        x.TransactionType == WhatsApp360DialogUsageTransactionType.ConversationUsage
                        && x.BillingPeriod == billingPeriod && x.WhatsApp360DialogUsageRecordId == usageRecord.Id);

            var hasExistingTransactionLog = existingTransactionLog != null;

            if (hasExistingTransactionLog)
            {
                var markupBillRecord = await _appDbContext.CompanyBillRecords.Where(
                        x => x.SubscriptionPlanId == "sleekflow_markup_360dialog_conversation" &&
                             x.PeriodStart == billingPeriodDateTime.StartOfMonth() &&
                             x.PeriodEnd == billingPeriodDateTime.EndOfMonth().AddDays(1) &&
                             x.CompanyId == usageRecord.CompanyId)
                    .FirstOrDefaultAsync();

                if (markupBillRecord != null)
                {
                    _appDbContext.CompanyBillRecords.Remove(markupBillRecord);
                }

                _appDbContext.CompanyWhatsApp360DialogUsageTransactionLogs.Remove(existingTransactionLog);
                await _appDbContext.SaveChangesAsync();
            }

            var tranLog = new WhatsApp360DialogUsageTransactionLog
            {
                Total = usage.TotalPrice,
                Currency = clientBalance.Currency.ToUpper(),
                TransactionType = WhatsApp360DialogUsageTransactionType.ConversationUsage,
                CompanyId = usageRecord.CompanyId,
                ConversationPeriodUsage = new Whatsapp360DialogConversationPeriodUsage
                {
                    TotalPrice = usage.TotalPrice,
                    BusinessInitiatedPaidQuantity = usage.BusinessInitiatedPaidQuantity,
                    BusinessInitiatedPrice = usage.BusinessInitiatedPrice,
                    BusinessInitiatedQuantity = usage.BusinessInitiatedQuantity,
                    FreeEntryPoint = usage.FreeEntryPoint,
                    FreeQuantity = usage.FreeQuantity,
                    FreeTier = usage.FreeTier,
                    PaidQuantity = usage.PaidQuantity,
                    Quantity = usage.Quantity,
                    UserInitiatedPaidQuantity = usage.UserInitiatedPaidQuantity,
                    UserInitiatedPrice = usage.UserInitiatedPrice,
                    UserInitiatedQuantity = usage.UserInitiatedQuantity,
                    PeriodDate = usage.PeriodDate,
                    BillingPeriod = billingPeriod
                },
                IsMarkedInUsageRecord = false,
                BillingPeriod = billingPeriod
            };

            usageRecord.TransactionLogs.Add(tranLog);

            usageRecord.UpdatedAt = DateTime.UtcNow;

            await _appDbContext.SaveChangesAsync();

            if (usageRecord.CanGenerateMarkupRecord)
            {
                conversationMakeupBillRecords.Add(
                    new ConversationMakeupBillRecord
                    {
                        Id = tranLog.Id,
                        CompanyId = usageRecord.CompanyId,
                        TotalPrice = usage.TotalPrice,
                        Currency = clientBalance.Currency.ToUpper(),
                        BillingPeriod = billingPeriod,
                        Waba360DialogClientId = usageRecord.Waba360DialogClientId
                    });
            }

            if (hasExistingTransactionLog)
            {
                await ReCalculateUsageFromTransactionLogs(usageRecord.Id);
            }
            else
            {
                await CalculateUsage(usageRecord.Id);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "[CoreWhatsApp360DialogUsage {MethodName}] Company {CompanyId} error recalculating previous monthly" +
                " billings for record id {UsageRecordId} for 360dialog client id {Waba360DialogClientId}. {ExceptionMessage}",
                nameof(PreviousMonthlyConversationBillings),
                usageRecord.CompanyId,
                usageRecord.Id,
                usageRecord.Waba360DialogClientId,
                ex.Message);
        }

        await CreateMarkBillRecord(conversationMakeupBillRecords);
    }

    public async Task<List<Whatsapp360dialogClientUsage>> GetAllPartnerClientsUsage(
        DateTime start,
        DateTime end,
        List<string> partnerIds = null,
        List<string> clientIds = null)
    {
        var partners = await _appDbContext.CoreWhatsApp360DialogPartnerAuthCredentials
            .AsNoTracking()
            .WhereIf(partnerIds != null, credential => partnerIds.Contains(credential.PartnerId))
            .Where(x => x.IsInUse)
            .ToListAsync();

        var result = new List<Whatsapp360dialogClientUsage>();

        foreach (var partner in partners.DistinctBy(x => x.PartnerId))
        {
            var token = await _coreWhatsApp360DialogPartnerAuthService.GetAuthTokenAsync(partner.PartnerId);

            var partnerClients = await GetAllPartnerClients(partner.PartnerId);

            if (clientIds != null)
            {
                partnerClients = partnerClients.Where(x => clientIds.Contains(x.Id)).ToList();
            }

            foreach (var client in partnerClients)
            {
                var clientUsage = new Whatsapp360dialogClientUsage()
                {
                    PartnerId = partner.PartnerId,
                    ClientId = client.Id,
                    ClientName = client.Name,
                    CreatedAt = client.CreatedAt,
                    Payment = partner.TopUpMode.ToString()
                };

                try
                {
                    var partnerApiClient = new WABA360DialogPartnerClient(
                        new PartnerInfo(token.PartnerId),
                        token.AccessToken);
                    GetClientBalanceResponse clientBalance = null;

                    var executeResult = await Policy
                        .Handle<PartnerClientException>()
                        .WaitAndRetryAsync(3, sleepDurationProvider: i => TimeSpan.FromSeconds(5))
                        .ExecuteAndCaptureAsync(
                            async () =>
                            {
                                clientBalance = await partnerApiClient.GetClientBalanceAsync(
                                    client.Id,
                                    start.StartOfMonth().ToUnixTime(),
                                    start.EndOfMonth().AddDays(1).ToUnixTime(),
                                    "month");
                            });

                    if (clientBalance == null)
                    {
                        continue;
                    }

                    var usages = clientBalance.Usage.Where(x => x.PeriodDate <= start && x.PeriodDate < end).ToList();

                    clientUsage.TotalUserInitiatedConversationQuantity = usages.Sum(x => x.UserInitiatedQuantity);
                    clientUsage.TotalBusinessInitiatedConversationQuantity =
                        usages.Sum(x => x.BusinessInitiatedQuantity);
                    clientUsage.TotalUserInitiatedConversationPaidQuantity =
                        usages.Sum(x => x.UserInitiatedPaidQuantity);
                    clientUsage.TotalBusinessInitiatedConversationPaidQuantity =
                        usages.Sum(x => x.BusinessInitiatedPaidQuantity);
                    clientUsage.TotalUserInitiatedPrice = usages.Sum(x => x.UserInitiatedPrice);
                    clientUsage.TotalBusinessInitiatedPrice = usages.Sum(x => x.BusinessInitiatedPrice);
                    clientUsage.TotalPrice = usages.Sum(x => x.TotalPrice);
                    clientUsage.IsSuccess = true;
                }
                catch (Exception e)
                {
                    clientUsage.IsSuccess = false;
                }

                result.Add(clientUsage);
            }
        }

        return result;
    }

    private async ValueTask CreateMarkBillRecord(List<ConversationMakeupBillRecord> conversationMakeupBillRecords)
    {
        if (conversationMakeupBillRecords.Count == 0)
        {
            return;
        }

        var cmsEuroToUsdExchangeRateRecord =
            await _appDbContext.CmsCurrencyExchangeRates.FirstAsync(
                x => x.CurrencyFrom == "EUR" && x.CurrencyTo == "USD");
        var cmsEuroToUsdExchangeRate = cmsEuroToUsdExchangeRateRecord.ExchangeRate;
        var currentEuroToUsdExchange = await CurrencyExchangeHelper.GetExchangeRateAsync("EUR", "USD");
        var markupPercentage = cmsEuroToUsdExchangeRate - currentEuroToUsdExchange;

        if (!await _appDbContext.CoreSubscriptionPlans.AnyAsync(x => x.Id == "sleekflow_markup_360dialog_conversation"))
        {
            _appDbContext.CoreSubscriptionPlans.Add(
                new SubscriptionPlan
                {
                    Id = "sleekflow_markup_360dialog_conversation",
                    SubscriptionName = "360Dialog Conversation Markup",
                    Description =
                        "360Dialog Conversation Markup, calculated every start of the month and current exchange rate",
                    Amount = 0,
                    MaximumContact = 0,
                    MaximumMessageSent = 0,
                    IncludedAgents = 0,
                    MaximumCampaignSent = 0,
                    MaximumChannel = false,
                    ExtraChatAgentPlan = null,
                    ExtraChatAgentPrice = 0,
                    MaximumAutomation = 0,
                    MaximumNumberOfChannel = 0,
                    MaximumAPICall = 0,
                    Currency = "usd",
                    StripePlanId = "sleekflow_markup_360dialog_conversation",
                    SubscriptionTier = SubscriptionTier.MarkUpLog,
                    Version = 0,
                });

            await _appDbContext.SaveChangesAsync();
        }

        foreach (var makeupBillRecord in conversationMakeupBillRecords)
        {
            var billingPeriodDateTime = DateTime.ParseExact(makeupBillRecord.BillingPeriod + "-01", "yyyy-MM-dd", null);

            if (makeupBillRecord.Currency.ToUpper() != "EUR")
            {
                continue;
            }

            var payAmount = (double) Math.Round(makeupBillRecord.TotalPrice * markupPercentage, 2);

            if (payAmount < 0)
            {
                payAmount = 0;
            }

            var billRecord = new BillRecord
            {
                SubscriptionPlanId = "sleekflow_markup_360dialog_conversation",
                CompanyId = makeupBillRecord.CompanyId,
                PayAmount = payAmount,
                currency = "usd",
                Status = BillStatus.Active,
                PaymentStatus = PaymentStatus.Paid,
                PeriodStart = billingPeriodDateTime.StartOfMonth(),
                PeriodEnd = billingPeriodDateTime.EndOfMonth().AddDays(1),
                metadata = new Dictionary<string, string>
                {
                    {
                        "WhatsApp360DialogUsageTransactionLogId", makeupBillRecord.Id.ToString()
                    },
                    {
                        "BillingPeriod", makeupBillRecord.BillingPeriod
                    },
                    {
                        "ClientId", makeupBillRecord.Waba360DialogClientId
                    },
                    {
                        "CurrentEuroExchange", currentEuroToUsdExchange.ToString()
                    },
                    {
                        "MarkupPercentage", markupPercentage.ToString()
                    },
                }
            };

            _appDbContext.CompanyBillRecords.Add(billRecord);
        }

        await _appDbContext.SaveChangesAsync();
    }

    private async Task<List<WhatsAppBusinessApiClient>> GetAllPartnerClients(string partnerId)
    {
        var partnerAuthToken = await _coreWhatsApp360DialogPartnerAuthService.GetAuthTokenAsync(partnerId);
        var partnerClient = new WABA360DialogPartnerClient(
            new PartnerInfo(partnerAuthToken.PartnerId),
            partnerAuthToken.AccessToken);

        // Get all channels
        var allClients = new List<WhatsAppBusinessApiClient>();
        var fetched = 0;
        var done = false;
        const int limit = 1000;

        do
        {
            var partnerClientsResponse = await partnerClient.GetPartnerClientsAsync(limit, fetched);

            if (!partnerClientsResponse.Clients.Any())
            {
                break;
            }

            fetched += limit;

            allClients.AddRange(partnerClientsResponse.Clients);

            done = fetched >= partnerClientsResponse.Total;
        }
        while (!done);

        return allClients;
    }
}