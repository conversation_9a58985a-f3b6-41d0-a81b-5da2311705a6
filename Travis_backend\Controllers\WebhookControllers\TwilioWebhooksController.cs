﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using AutoMapper;
using Hangfire;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Primitives;
using Newtonsoft.Json;
using Polly;
using Travis_backend.Cache;
using Travis_backend.ChannelDomain.ViewModels;
using Travis_backend.CommonDomain.ViewModels;
using Travis_backend.Constants;
using Travis_backend.ContactDomain.Models;
using Travis_backend.ConversationDomain.ConversationResolver;
using Travis_backend.ConversationDomain.Models;
using Travis_backend.ConversationServices;
using Travis_backend.CoreDomain.Models;
using Travis_backend.Database;
using Travis_backend.Enums;
using Travis_backend.Extensions;
using Travis_backend.FileDomain.Models;
using Travis_backend.Helpers;
using Travis_backend.MessageDomain.Models;
using Travis_backend.MessageDomain.ViewModels;
using Travis_backend.SignalR;
using Twilio.AspNet.Common;
using Twilio.AspNet.Core;
using Twilio.TwiML;

namespace Travis_backend.Controllers.WebhookControllers
{
    public class TwilioWebhooksController : TwilioController
    {
        private readonly ApplicationDbContext _appDbContext;
        private readonly ILogger<TwilioWebhooksController> _logger;
        private readonly IMapper _mapper;
        private readonly IConversationMessageService _messagingService;
        private readonly ISignalRService _signalRService;
        private readonly ILockService _lockService;
        private readonly IConversationResolver _conversationResolver;
        private readonly IHttpClientFactory _httpClientFactory;

        public TwilioWebhooksController(
            ApplicationDbContext appDbContext,
            IMapper mapper,
            ILogger<TwilioWebhooksController> logger,
            IConversationMessageService messagingService,
            ISignalRService signalRService,
            ILockService lockService,
            IConversationResolver conversationResolver,
            IHttpClientFactory httpClientFactory)
        {
            _appDbContext = appDbContext;
            _logger = logger;
            _mapper = mapper;
            _messagingService = messagingService;
            _signalRService = signalRService;
            _lockService = lockService;
            _conversationResolver = conversationResolver;
            _httpClientFactory = httpClientFactory;
        }

        [HttpPost]
        [Route("twilio/usage")]
        public async Task<IActionResult> UsageTrigger([FromBody] TwilioResponseViewModel twilioResponseViewModel)
        {
            _logger.LogInformation(
                "Twilio Usage Trigger: {Payload}",
                JsonConvert.SerializeObject(twilioResponseViewModel));

            var twilio = await _appDbContext.ConfigWhatsAppConfigs
                .Where(x => x.TwilioAccountId == twilioResponseViewModel.AccountSid).FirstOrDefaultAsync();

            if (twilio == null)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        message = "Not found"
                    });
            }

            if (twilio.IdempotencyToken == twilioResponseViewModel.IdempotencyToken)
            {
                return Ok();
            }

            twilio.IsRequireTopup = true;
            int currentValue, triggerValue = 0;
            int.TryParse(twilioResponseViewModel.CurrentValue, out currentValue);
            twilio.CurrentValue = currentValue;
            int.TryParse(twilioResponseViewModel.TriggerValue, out triggerValue);
            twilio.TriggerValue = triggerValue;
            twilio.IdempotencyToken = twilioResponseViewModel.IdempotencyToken;
            await _appDbContext.SaveChangesAsync();

            return Ok();
        }

        [HttpPost]
        [Route("whatsapp/twilio/webhook")]
        public async Task<IActionResult> Post(string companyId, SmsRequest incomingMessage, int numMedia)
        {
            var messagingResponse = new MessagingResponse();

            // Return Spam
            if (incomingMessage.Body == "1" && incomingMessage.To.Contains("64522442"))
            {
                return TwiML(messagingResponse);
            }

            _logger.LogInformation(
                "WhatsappTwilioWebhook - Webhook: {payload}",
                JsonConvert.SerializeObject(incomingMessage));

            // var existing = await _appDbContext.ConversationMessages.Where(x => x.MessageUniqueID == incomingMessage.SmsSid).FirstOrDefaultAsync();
            var profileName = Request.Form["ProfileName"];
            var buttonText = Request.Form["ButtonText"];
            var buttonPayload = Request.Form["ButtonPayload"];
            var referralSourceUrl = Request.Form["ReferralSourceUrl"];
            var fileURLs = new List<FileURLMessage>();

            if (!string.IsNullOrEmpty(referralSourceUrl))
            {
                incomingMessage.Body = $"Link: {referralSourceUrl}\n\n{incomingMessage.Body}";
            }

            if (numMedia > 0)
            {
                for (var i = 0; i < numMedia; i++)
                {
                    var mediaUrl = Request.Form[$"MediaUrl{i}"];
                    var MIMEType = Request.Form[$"MediaContentType{i}"];

                    // according to log, twilio no longer use incomingMessage.body as filename
                    var filename = await GetFileNameByUrlAsync(mediaUrl);
                    filename = FilenameHelper.FormatFileName(filename, MIMEType);

                    fileURLs.Add(
                        new FileURLMessage()
                        {
                            FileName = filename, FileURL = mediaUrl, MIMEType = MIMEType
                        });
                }
            }

            try
            {
                try
                {
                    // Sandbox
                    if (await _appDbContext.CoreSandboxTwilioConfigs
                            .AnyAsync(x => x.PhoneNumber == incomingMessage.To))
                    {
                        messagingResponse = await ReceiveSandboxMessage(companyId, incomingMessage, numMedia);

                        return TwiML(messagingResponse);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(
                        ex,
                        "WhatsappTwilioWebhook company id {CompanyId} Twilio receive sandbox message error: {ExceptionMessage}",
                        companyId,
                        ex.Message);
                }

                Policy.Handle<Exception>()
                    .WaitAndRetry(
                        3,
                        sleepDurationProvider: i => TimeSpan.FromMilliseconds(100),
                        onRetry: (exception, _, retryCount, _) =>
                        {
                            _logger.LogError(
                                exception,
                                "WhatsappTwilioWebhook: Enqueue error {ExceptionMessage} with retry {RetryCount}",
                                exception.Message,
                                retryCount);
                        })
                    .Execute(
                        () =>
                        {
                            BackgroundJob.Enqueue(
                                () => ReceiveTwilioMessage(
                                    companyId,
                                    incomingMessage,
                                    numMedia,
                                    profileName,
                                    buttonText,
                                    fileURLs));
                        });
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Twilio WhatsApp webhook error {TwilioPayload}",
                    JsonConvert.SerializeObject(incomingMessage));

                return BadRequest();
            }

            return TwiML(messagingResponse);
        }

        [ApiExplorerSettings(IgnoreApi = true)]
        private async Task<string> GetFileNameByUrlAsync(StringValues mediaUrl)
        {
            var client = _httpClientFactory.CreateClient(HttpClientHandlerName.Default);
            using var response = await client.SendAsync(new HttpRequestMessage(HttpMethod.Get, mediaUrl));
            return response
                .Content
                .Headers
                .ContentDisposition?
                .FileName?
                .Replace("\"", string.Empty) ?? Path.GetFileName(mediaUrl);
        }

        [HttpPost]
        [Route("twilio/webhook/status")]
        public IActionResult StatusCallback(SmsStatusCallbackRequest smsStatusCallback)
        {
            try
            {
                BackgroundJob.Enqueue(() => UpdateTwilioMessageStatus(smsStatusCallback));

                return Ok();
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Twilio StatusCallback Error: {TwilioPayload}",
                    JsonConvert.SerializeObject(smsStatusCallback));

                return Ok();
            }
        }

        #region worker

        [ApiExplorerSettings(IgnoreApi = true)]
        [AutomaticRetry(
            Attempts = 10,
            OnAttemptsExceeded = AttemptsExceededAction.Fail,
            DelaysInSeconds =
            [
                60, // 1 minute
                120, // 2 minutes
                240, // 4 minutes
                480, // 8 minutes
                960, // 16 minutes
                1920, // 32 minutes
                3600, // 1 hour
                7200, // 2 hours
                14400, // 4 hours
                28800, // 8 hours
            ])]
        public async Task ReceiveTwilioMessage(
            string companyId,
            SmsRequest incomingMessage,
            int numMedia,
            string profileName,
            string buttonText,
            List<FileURLMessage> fileURLs)
        {
            var twilioConfig = await _appDbContext.ConfigWhatsAppConfigs.Where(
                    x => x.TwilioAccountId == incomingMessage.AccountSid && x.WhatsAppSender == incomingMessage.To)
                .FirstOrDefaultAsync();

            var company = await _appDbContext.CompanyCompanies.Where(x => x.Id == twilioConfig.CompanyId)
                .FirstOrDefaultAsync();
            companyId = twilioConfig.CompanyId;

            // Get Conversation with contact Whatsapp Sender and Lock
            var (conversation, myLock) = await _conversationResolver.GetConversationForReceivingMessageAsync(
                companyId,
                ChannelTypes.WhatsappTwilio,
                incomingMessage.To,
                incomingMessage.From,
                profileName);

            var whatsappSender = conversation.WhatsappUser;

            var whatsappReceiver = await _appDbContext.SenderWhatsappSenders.Where(
                    x => x.whatsAppId == incomingMessage.To && x.CompanyId == companyId &&
                         x.InstanceId == twilioConfig.TwilioAccountId && x.InstaneSender == twilioConfig.WhatsAppSender)
                .FirstOrDefaultAsync();
            if (whatsappReceiver == null)
            {
                whatsappReceiver = new WhatsAppSender
                {
                    whatsAppId = incomingMessage.To,
                    CompanyId = companyId,
                    phone_number = incomingMessage.To.Replace("whatsapp:+", string.Empty),
                    name = incomingMessage.To.Replace("whatsapp:", string.Empty),
                    InstanceId = incomingMessage.AccountSid,
                    InstaneSender = incomingMessage.To
                };
            }
            else
            {
                whatsappReceiver.phone_number = incomingMessage.To.Replace("whatsapp:+", string.Empty);
                whatsappReceiver.name = incomingMessage.To.Replace("whatsapp:", string.Empty);

                // whatsappSender.InstanceId = incomingMessage.AccountSid;
                // whatsappSender.InstaneSender = incomingMessage.To;
            }

            if (!string.IsNullOrEmpty(buttonText))
            {
                if (await _appDbContext.ConversationMessages
                        .AnyAsync(
                            x => x.whatsappReceiver.whatsAppId == whatsappSender.whatsAppId &&
                                 DateTime.UtcNow.AddDays(-1) < x.CreatedAt && x.DeliveryType == DeliveryType.ReadMore))
                {
                    var readmoreMessage = await _appDbContext.ConversationMessages
                        .Where(
                            x =>
                                x.whatsappReceiver.whatsAppId == whatsappSender.whatsAppId &&
                                x.Status == MessageStatus.Undelivered)
                        .OrderByDescending(x => x.Timestamp)
                        .Include(x => x.UploadedFiles)
                        .FirstOrDefaultAsync();
                    if (readmoreMessage != null)
                    {
                        var existingFiles = new List<UploadedFile>();
                        if (readmoreMessage.UploadedFiles.Count > 0)
                        {
                            existingFiles = _mapper.Map<List<UploadedFile>>(readmoreMessage.UploadedFiles);
                        }

                        try
                        {
                            BackgroundJob.Enqueue<IConversationMessageService>(
                                x =>
                                    x.SendReadMoreMessage(
                                        readmoreMessage.CompanyId,
                                        readmoreMessage.ConversationId,
                                        whatsappReceiver.Id,
                                        readmoreMessage.MessageContent,
                                        null,
                                        existingFiles,
                                        DeliveryType.AutomatedMessage));
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(
                                ex,
                                "CompanyId: {CompanyId}, ConversationId: {ConversationId}, Resend failed message error: {ExceptionString}",
                                readmoreMessage.CompanyId,
                                readmoreMessage.ConversationId,
                                ex.ToString());
                        }
                        finally
                        {
                            readmoreMessage.Status = MessageStatus.Failed;
                            await _appDbContext.SaveChangesAsync();
                        }
                    }
                }
            }

            if (myLock != null)
            {
                await _lockService.ReleaseLockAsync(myLock);
            }

            var conversationMessage = new ConversationMessage()
            {
                MessageContent = incomingMessage.Body,
                Channel = ChannelTypes.WhatsappTwilio,
                whatsappSender = whatsappSender,
                whatsappReceiver = whatsappReceiver,
                MessageType = "text",
                MessageUniqueID = incomingMessage.SmsSid

                // Price = -0.005
            };

            if (!string.IsNullOrEmpty(buttonText))
            {
                conversationMessage.MessageContent = buttonText;
            }

            if (numMedia > 0)
            {
                foreach (var file in fileURLs)
                {
                    file.FileName = FilenameHelper.FormatFileName(
                        file.FileName,
                        file.MIMEType);
                }

                conversationMessage.MessageType = "file";
                _logger.LogInformation(
                    "Receive File Message {WebhookPayload} and {FileURLs} for {Conversation} and {ConversationMessage}",
                    JsonConvert.SerializeObject(incomingMessage),
                    JsonConvert.SerializeObject(fileURLs),
                    JsonConvert.SerializeObject(conversation),
                    JsonConvert.SerializeObject(conversationMessage));

                var result = await _messagingService.SendFileMessageByFBURL(
                    conversation,
                    conversationMessage,
                    fileURLs);
            }
            else
            {
                if (string.IsNullOrEmpty(conversationMessage.MessageContent))
                {
                    conversationMessage.MessageContent = "<Unsupported Message Type>";
                }

                var result = await _messagingService.SendMessage(conversation, conversationMessage);
            }
        }

        [ApiExplorerSettings(IgnoreApi = true)]
        [AutomaticRetry(
            Attempts = 10,
            OnAttemptsExceeded = AttemptsExceededAction.Fail,
            DelaysInSeconds =
            [
                60, // 1 minute
                120, // 2 minutes
                240, // 4 minutes
                480, // 8 minutes
                960, // 16 minutes
                1920, // 32 minutes
                3600, // 1 hour
                7200, // 2 hours
                14400, // 4 hours
                28800, // 8 hours
            ])]
        public async Task UpdateTwilioMessageStatus(SmsStatusCallbackRequest smsStatusCallback)
        {
            if (!await _appDbContext.ConversationMessages
                    .AnyAsync(x => x.MessageUniqueID == smsStatusCallback.SmsSid))
            {
                throw new InvalidOperationException("No conversation message found.");
            }

            _logger.LogInformation(
                "Update Twilio Message Status: {SmsStatusCallbackSmsSid} [{SmsStatusCallbackMessageStatus}] Error Code: {SmsStatusCallbackErrorCode}",
                smsStatusCallback.SmsSid,
                smsStatusCallback.MessageStatus,
                smsStatusCallback.ErrorCode);

            // Ignore queued status, since status update is not required for Sending messages
            if (smsStatusCallback.MessageStatus == "queued")
            {
                return;
            }

            var statusUpdateLockKey = $"{ChannelTypes.WhatsappTwilio}_message_status_update_{smsStatusCallback.SmsSid}";

            ILockService.Lock statusUpdateLock = null;

            while (true)
            {
                statusUpdateLock = await _lockService.AcquireLockAsync(statusUpdateLockKey, TimeSpan.FromSeconds(2));

                if (statusUpdateLock == null)
                {
                    await Task.Delay(TimeSpan.FromSeconds(2));
                }
                else
                {
                    break;
                }
            }

            var message = await _appDbContext.ConversationMessages
                .Where(x => x.MessageUniqueID == smsStatusCallback.SmsSid)
                .Include(x => x.whatsappReceiver)
                .Include(x => x.UploadedFiles)
                .Include(x => x.ExtendedMessagePayload)
                .FirstOrDefaultAsync();

            switch (smsStatusCallback.MessageStatus)
            {
                case "sent":
                    if (message.Status is MessageStatus.Sent
                        or MessageStatus.Read
                        or MessageStatus.Received
                        or MessageStatus.Undelivered
                        or MessageStatus.Failed)
                    {
                        if (statusUpdateLock != null)
                        {
                            await _lockService.ReleaseLockAsync(statusUpdateLock);
                        }

                        return;
                    }

                    message.Status = MessageStatus.Sent;
                    break;
                case "delivered":
                    if (message.Status is MessageStatus.Read)
                    {
                        if (statusUpdateLock != null)
                        {
                            await _lockService.ReleaseLockAsync(statusUpdateLock);
                        }

                        return;
                    }

                    message.Status = MessageStatus.Received;

                    break;
                case "read":
                    message.Status = MessageStatus.Read;
                    break;
                case "failed":
                    if (smsStatusCallback.ErrorCode == "63016" && await IsTwilioOptInEnabledAsync(message))
                    {
                        goto case "undelivered";
                    }

                    message.Status = MessageStatus.Failed;
                    message.ChannelStatusMessage = smsStatusCallback.ErrorCode;

                    message.Metadata ??= new Dictionary<string, object>();
                    message.Metadata.Add(
                        "errors",
                        new List<ConversationMessageError>
                        {
                            new ()
                            {
                                Code = smsStatusCallback.ErrorCode,
                                Message = smsStatusCallback.ChannelStatusMessage,
                                InnerError = new
                                {
                                    ErrorCode = smsStatusCallback.ErrorCode,
                                    ChannelStatusMessage = smsStatusCallback.ChannelStatusMessage,
                                    MessageStatus = smsStatusCallback.MessageStatus,
                                }
                            }
                        });

                    break;
                case "undelivered":
                    message.Status = MessageStatus.Undelivered;
                    message.ChannelStatusMessage = smsStatusCallback.ErrorCode;

                    message.Metadata ??= new Dictionary<string, object>();
                    message.Metadata.Add(
                        "errors",
                        new List<ConversationMessageError>
                        {
                            new ()
                            {
                                Code = smsStatusCallback.ErrorCode,
                                Message = smsStatusCallback.ChannelStatusMessage,
                                InnerError = new
                                {
                                    ErrorCode = smsStatusCallback.ErrorCode,
                                    ChannelStatusMessage = smsStatusCallback.ChannelStatusMessage,
                                    MessageStatus = smsStatusCallback.MessageStatus,
                                }
                            }
                        });

                    await _appDbContext.SaveChangesAsync();
                    await _signalRService.SignalROnMessageStatusChanged(message);

                    try
                    {
                        if (smsStatusCallback.ErrorCode == "63016")
                        {
                            await _messagingService.SendOptInMessageTwilio(message);
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(
                            ex,
                            "Error sending Twilio opt-in message for message id {CallbackSmsSid}. {ExceptionMessage}",
                            smsStatusCallback.SmsSid,
                            ex.Message);
                    }

                    break;
            }

            // skip save and signalr when message is undelivered, as it should be handled in the previous case
            if (message.Status == MessageStatus.Undelivered)
            {
                return;
            }

            await _appDbContext.SaveChangesAsync();
            await _signalRService.SignalROnMessageStatusChanged(message);

            if (statusUpdateLock != null)
            {
                await _lockService.ReleaseLockAsync(statusUpdateLock);
            }
        }

        [ApiExplorerSettings(IgnoreApi = true)]
        public async Task<MessagingResponse> ReceiveSandboxMessage(
            string companyId,
            SmsRequest incomingMessage,
            int numMedia)
        {
            // _logger.LogError($"Received Sandbox Message: {JsonConvert.SerializeObject(incomingMessage)}");
            var profileName = incomingMessage.From.Replace("whatsapp:+", string.Empty);
            try
            {
                profileName = Request.Form["ProfileName"];
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[{MethodName}] Company {CompanyId} sandbox read ProfileName error: {ExceptionString}",
                    nameof(ReceiveSandboxMessage),
                    companyId,
                    ex.ToString());
            }

            var messagingResponse = new MessagingResponse();
            var sandbox = new CompanySandbox();

            var exit_match = Regex.Match(incomingMessage.Body?.ToLower(), "exit");
            if (exit_match.Success)
            {
                sandbox = await _appDbContext.CompanySandboxes
                    .Where(
                        x => x.SandboxSenders
                            .Where(y => y.phone_number == incomingMessage.From.Replace("whatsapp:+", string.Empty))
                            .Count() > 0)
                    .Include(x => x.SandboxSenders).FirstOrDefaultAsync();
                sandbox.JoinedCount -= 1;
                sandbox.SandboxSenders.Remove(
                    sandbox.SandboxSenders.Where(
                            x => x.phone_number == incomingMessage.From.Replace("whatsapp:+", string.Empty))
                        .FirstOrDefault());
                await _appDbContext.SaveChangesAsync();
                var company = await _appDbContext.CompanyCompanies.Where(x => x.Id == sandbox.CompanyId)
                    .FirstOrDefaultAsync();
                messagingResponse.Message(
                    $"👋 You're disconnected from this WhatsApp Sandbox.\n\nYou can now join another WhatsApp Sandbox by scanning a new QR code.\n\nFeel free to book a video call here to start connecting to WhatsApp:\nhttps://sleekflow.io/connect-whatsapp");
                return messagingResponse;
            }

            if (!string.IsNullOrEmpty(incomingMessage.Body))
            {
                var match = Regex.Match(
                    incomingMessage.Body,
                    "Hi SleekFlow! Please deliver my message to the test account of (.*)");
                if (match.Success)
                {
                    // Add to sandbox
                    var identify = match.Groups[1].Value;
                    sandbox = await _appDbContext.CompanySandboxes.Where(x => x.MappingIdentity == identify)
                        .Include(x => x.SandboxSenders).FirstOrDefaultAsync();

                    if (sandbox.JoinedCount > 0)
                    {
                        return messagingResponse.Message(
                            $"You have already added 1 number to this WhatsApp sandbox. Please “exit” the previous number to join again!");
                    }
                    else if (sandbox.SandboxSenders
                                 .Where(x => x.phone_number == incomingMessage.From.Replace("whatsapp:+", string.Empty))
                                 .Count() >
                             0)
                    {
                        return messagingResponse.Message(
                            "You’re already in this WhatsApp sandbox. Simply send message to test, or type “exit” at any time to stop.");
                    }
                    else if (await _appDbContext.SenderSandboxSenders
                                 .AnyAsync(x => x.phone_number == incomingMessage.From.Replace("whatsapp:+", string.Empty)))
                    {
                        return messagingResponse.Message(
                            "You’re already in another WhatsApp sandbox. Simply send message to test, or type “exit” at any time to stop.");
                    }
                    else
                    {
                        var sandboxSender = new SandboxSender
                        {
                            CompanyId = sandbox.CompanyId,
                            name = profileName,
                            phone_number = incomingMessage.From.Replace("whatsapp:+", string.Empty)
                        };
                        sandbox.SandboxSenders.Add(sandboxSender);

                        var _conversation = await _appDbContext.Conversations
                            .Where(
                                x => x.CompanyId == sandbox.CompanyId &&
                                     x.WhatsappUser.whatsAppId == incomingMessage.From)
                            .Include(x => x.UserProfile.WhatsAppAccount).Include(x => x.WhatsappUser)
                            .FirstOrDefaultAsync();

                        if (_conversation == null)
                        {
                            var userProfile = new UserProfile
                            {
                                CompanyId = sandbox.CompanyId,
                                FirstName = sandboxSender.name,
                                ActiveStatus = ActiveStatus.Active,
                                WhatsAppAccount = new WhatsAppSender
                                {
                                    whatsAppId = incomingMessage.From,
                                    name = sandboxSender.name,
                                    phone_number = sandboxSender.phone_number
                                },
                                IsSandbox = true
                            };

                            _conversation = new Conversation
                            {
                                CompanyId = sandbox.CompanyId,
                                UserProfile = userProfile,
                                WhatsappUser = userProfile.WhatsAppAccount,
                                MessageGroupName = sandbox.CompanyId,
                                ActiveStatus = ActiveStatus.Active,
                                IsSandbox = true
                            };

                            _appDbContext.UserProfiles.Add(userProfile);
                            _appDbContext.Conversations.Add(_conversation);
                        }

                        _conversation.IsSandbox = true;
                        _conversation.UserProfile.IsSandbox = true;
                        sandbox.JoinedCount += 1;
                        await _appDbContext.SaveChangesAsync();

                        var company = await _appDbContext.CompanyCompanies.Where(x => x.Id == sandbox.CompanyId)
                            .FirstOrDefaultAsync();

                        messagingResponse.Message(
                            $"✅ You’ve successfully connected to SleekFlow's WhatsApp test account!\n\nSend message here and reply on SleekFlow to explore how inbox works 🚀\n\nFeel free to book a video call here to start connecting to WhatsApp:\nhttps://sleekflow.io/connect-whatsapp\n.\nReply “exit” at any time to stop");
                    }
                }
            }

            sandbox = await _appDbContext.CompanySandboxes
                .Where(
                    x => x.SandboxSenders.Any(
                        y => y.phone_number == incomingMessage.From.Replace("whatsapp:+", string.Empty)))
                .Include(x => x.SandboxSenders).FirstOrDefaultAsync();

            if (sandbox == null)
            {
                return messagingResponse.Message(
                    "It looks like that you're currently not in any of our WhatsApp Sandbox account.\n\nPlease head to app.sleekflow.io and scan the QR code to join!✌️🏻");
            }

            var conversation = await _appDbContext.Conversations
                .Where(x => x.CompanyId == sandbox.CompanyId && x.WhatsappUser.whatsAppId == incomingMessage.From)
                .Include(x => x.WhatsappUser).FirstOrDefaultAsync();

            var conversationMessage = new ConversationMessage()
            {
                MessageContent = incomingMessage.Body,
                Channel = ChannelTypes.WhatsappTwilio,
                whatsappReceiver = conversation.WhatsappUser,
                MessageType = "text",
                MessageUniqueID = incomingMessage.SmsSid,
                IsSandbox = true
            };

            if (numMedia > 0)
            {
                conversationMessage.MessageType = "file";
                var fileURLs = new List<FileURLMessage>();

                for (var i = 0; i < numMedia; i++)
                {
                    var mediaUrl = Request.Form[$"MediaUrl{i}"];
                    var MIMEType = Request.Form[$"MediaContentType{i}"];
                    var filename = Path.GetFileName(mediaUrl);

                    if (Path.HasExtension(incomingMessage.Body))
                    {
                        filename = conversationMessage.MessageContent;
                    }
                    else
                    {
                        switch (MIMEType)
                        {
                            case "application/pdf":
                                filename += ".pdf";

                                break;
                            case "video/mp4":
                                filename += ".mp4";

                                break;
                        }
                    }

                    fileURLs.Add(
                        new FileURLMessage()
                        {
                            FileName = filename, FileURL = mediaUrl, MIMEType = MIMEType
                        });
                }

                var result = await _messagingService.SendFileMessageByFBURL(
                    conversation,
                    conversationMessage,
                    fileURLs);
            }
            else
            {
                var result = await _messagingService.SendMessage(conversation, conversationMessage);
            }

            return messagingResponse;
        }

        #endregion

        [ApiExplorerSettings(IgnoreApi = true)]
        private async Task<bool> IsTwilioOptInEnabledAsync(ConversationMessage message)
        {
            if (message is null)
            {
                return false;
            }

            var companySetting = await _appDbContext.CompanyCompanies
                .AsNoTracking()
                .Where(x => x.Id == message.CompanyId)
                .Select(x => x.CompanySetting)
                .FirstOrDefaultAsync();

            if (companySetting is null)
            {
                return false;
            }

            var whatsappReceiver = await _appDbContext.SenderWhatsappSenders
                .AsNoTracking()
                .Where(x => x.Id == message.whatsappReceiverId)
                .FirstOrDefaultAsync();

            if (whatsappReceiver is null)
            {
                return false;
            }

            var config = await _appDbContext.ConfigWhatsAppConfigs
                .AsNoTracking()
                .Where(x => x.TwilioAccountId == whatsappReceiver.InstanceId)
                .WhereIf(
                    !string.IsNullOrEmpty(whatsappReceiver.InstaneSender),
                    x => x.WhatsAppSender == whatsappReceiver.InstaneSender)
                .FirstOrDefaultAsync();

            return !string.IsNullOrEmpty(config?.ReadMoreTemplateMessage) &&
                   companySetting.IsOptInOn;
        }
    }
}