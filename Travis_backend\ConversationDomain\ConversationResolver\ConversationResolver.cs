﻿using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Threading.Tasks;
using AutoMapper;
using Hangfire;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Telegram.Bot;
using Travis_backend.Cache;
using Travis_backend.ChannelDomain.Clients;
using Travis_backend.ChannelDomain.Models;
using Travis_backend.ChannelDomain.ViewModels;
using Travis_backend.Constants;
using Travis_backend.ContactDomain.Constants;
using Travis_backend.ContactDomain.Models;
using Travis_backend.ContactDomain.Services;
using Travis_backend.ContactDomain.ViewModels;
using Travis_backend.ConversationDomain.Models;
using Travis_backend.ConversationServices;
using Travis_backend.Database;
using Travis_backend.Enums;
using Travis_backend.Extensions;
using Travis_backend.FlowHubs;
using Travis_backend.FlowHubs.Models;
using Travis_backend.Helpers;
using Travis_backend.MessageDomain.Models;
using Travis_backend.MessageDomain.Models.Interfaces;
using Travis_backend.SignalR;
using WABA360Dialog.ApiClient.Payloads.Converters;
using WABA360Dialog.ApiClient.Payloads.Enums;

namespace Travis_backend.ConversationDomain.ConversationResolver;

public interface IConversationResolver
{
    #region for Message/Send API

    /// <summary>
    /// For Receive Message
    /// </summary>
    Task<Conversation> GetConversationAsync(
        string companyId,
        string channel,
        string channelIdentityId,
        string channelUserIdentityId);

    Task<Conversation> GetConversationAsync(
        string companyId,
        string conversationId,
        string channel,
        string channelIdentityId,
        string channelUserIdentityId);

    /// <summary>
    /// For Message Send
    /// </summary>
    Task<Conversation> GetConversationByChannelAsync(
        string companyId,
        string conversationId,
        string channel);

    #endregion

    #region ConversationMessageService
    Task<Conversation> GetConversationAsync(
        Conversation conversation,
        ConversationMessage conversationMessage,
        int attempts = 3);
    #endregion

    Task<(Conversation Conversation, ILockService.Lock MyLock)> GetConversationForReceivingMessageAsync(
        string companyId,
        string channel,
        string channelIdentityId,
        string channelUserIdentityId,
        string channelUserName = null,
        string webhookString = null);
}

public class ConversationResolver : IConversationResolver
{
    private readonly ApplicationDbContext _appDbContext;
    private readonly ILogger<ConversationResolver> _logger;
    private readonly IUserProfileService _userProfileService;
    private readonly ISignalRService _signalRService;
    private readonly IUserProfileHooks _userProfileHooks;
    private readonly ILockService _lockService;
    private readonly IConfiguration _configuration;
    private readonly IAzureBlobStorageService _azureBlobStorageService;
    private readonly IMapper _mapper;
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly IUserProfileSafeDeleteService _userProfileSafeDeleteService;
    private readonly ITwilioService _twilioService;

    public ConversationResolver(
        ApplicationDbContext appDbContext,
        ILogger<ConversationResolver> logger,
        IUserProfileService userProfileService,
        ISignalRService signalRService,
        IUserProfileHooks userProfileHooks,
        ILockService lockService,
        IConfiguration configuration,
        IAzureBlobStorageService azureBlobStorageService,
        IMapper mapper,
        IHttpClientFactory httpClientFactory,
        IUserProfileSafeDeleteService userProfileSafeDeleteService,
        ITwilioService twilioService)
    {
        _appDbContext = appDbContext;
        _logger = logger;
        _userProfileService = userProfileService;
        _signalRService = signalRService;
        _userProfileHooks = userProfileHooks;
        _lockService = lockService;
        _configuration = configuration;
        _azureBlobStorageService = azureBlobStorageService;
        _mapper = mapper;
        _httpClientFactory = httpClientFactory;
        _userProfileSafeDeleteService = userProfileSafeDeleteService;
        _twilioService = twilioService;
    }

    public async Task<Conversation> GetConversationAsync(
        string companyId,
        string channel,
        string channelIdentityId,
        string channelUserIdentityId)
    {
        Conversation conversation = null;

        try
        {
            switch (channel)
            {
                case ChannelTypes.WhatsappCloudApi:
                    conversation = await _appDbContext.Conversations
                        .Where(
                            x => x.CompanyId == companyId
                                 && x.WhatsappCloudApiUser != null
                                 && x.WhatsappCloudApiUser.CompanyId == companyId
                                 && x.WhatsappCloudApiUser.WhatsappId == channelUserIdentityId)
                        .Include(x => x.WhatsappCloudApiUser)
                        .OrderByDescending(x => x.UpdatedTime)
                        .FirstOrDefaultAsync();

                    if (conversation != null && !string.IsNullOrEmpty(channelIdentityId) && conversation.WhatsappCloudApiUser.WhatsappChannelPhoneNumber != channelIdentityId)
                    {
                        var userProfile = await _userProfileService.SwitchUserProfileMessagingChannel(
                            companyId,
                            conversation.UserProfileId,
                            channel,
                            channelIdentityId);
                        conversation.WhatsappCloudApiUser = userProfile.WhatsappCloudApiUser;
                    }

                    break;
                case ChannelTypes.Whatsapp360Dialog:
                    conversation = await _appDbContext.Conversations.Where(
                            x => x.CompanyId == companyId
                                 && x.WhatsApp360DialogUserId.HasValue
                                 && x.WhatsApp360DialogUser.CompanyId == companyId
                                 && x.WhatsApp360DialogUser.WhatsAppId == channelUserIdentityId)
                        .Include(x => x.WhatsApp360DialogUser).FirstOrDefaultAsync();

                    if (conversation != null && !string.IsNullOrEmpty(channelIdentityId) && conversation.WhatsApp360DialogUser.ChannelIdentityId != channelIdentityId)
                    {
                        var userProfile = await _userProfileService.SwitchUserProfileMessagingChannel(
                            companyId,
                            conversation.UserProfileId,
                            channel,
                            channelIdentityId);
                        conversation.WhatsApp360DialogUser = userProfile.WhatsApp360DialogUser;
                    }

                    break;
                case ChannelTypes.WhatsappTwilio:
                case "twilio_whatsapp":
                    conversation = await _appDbContext.Conversations.Where(
                            x => x.CompanyId == companyId && x.WhatsappUserId.HasValue &&
                                 x.WhatsappUser.whatsAppId == channelUserIdentityId)
                        .Include(x => x.WhatsappUser)
                        .OrderByDescending(x => x.UpdatedTime)
                        .FirstOrDefaultAsync() ?? new Conversation()
                    {
                        CompanyId = companyId,
                        MessageGroupName = companyId
                    };

                    conversation = await HandleConversationWhatsappSenderAsync(
                        conversation,
                        companyId,
                        channelUserIdentityId,
                        channelIdentityId);

                    break;

                case ChannelTypes.Facebook:
                    conversation = await _appDbContext.Conversations
                        .Where(
                            x => x.CompanyId == companyId && x.facebookUserId.HasValue &&
                                 x.facebookUser.pageId == channelIdentityId &&
                                 x.facebookUser.FacebookId == channelUserIdentityId).Include(x => x.facebookUser)
                        .FirstOrDefaultAsync();

                    break;
                case ChannelTypes.Instagram:
                    conversation = await _appDbContext.Conversations
                        .Where(
                            x => x.CompanyId == companyId && x.InstagramUserId.HasValue &&
                                 x.InstagramUser.InstagramId == channelUserIdentityId).Include(x => x.InstagramUser)
                        .FirstOrDefaultAsync();

                    break;
                case ChannelTypes.Line:
                    conversation = await _appDbContext.Conversations
                        .Where(
                            x => x.CompanyId == companyId && x.LineUserId.HasValue && x.LineUser.ChannelIdentityId == channelIdentityId && x.LineUser.userId == channelUserIdentityId).Include(x => x.LineUser)
                        .FirstOrDefaultAsync();

                    break;
                case ChannelTypes.Telegram:
                    var parseTelegramChatIdSuccess = long.TryParse(channelUserIdentityId, out var telegramChatId);
                    var parseTelegramBotIdSuccess = long.TryParse(channelIdentityId, out var telegramBotId);
                    if (parseTelegramChatIdSuccess && parseTelegramBotIdSuccess)
                    {
                        conversation = await _appDbContext.Conversations.Where(
                            x => x.CompanyId == companyId &&
                                 x.TelegramUser.TelegramBotId == telegramBotId &&
                                 x.TelegramUser.TelegramChatId == telegramChatId).FirstOrDefaultAsync();
                    }
                    else
                    {
                        throw new Exception("unable to parse telegram chat id and telegram bot id");
                    }

                    break;
                case ChannelTypes.Sms:
                    conversation = await _appDbContext.Conversations.Where(
                            x => x.CompanyId == companyId && x.SMSUserId.HasValue && x.SMSUser.InstanceId == channelIdentityId && x.SMSUser.SMSId == channelUserIdentityId)
                        .Include(x => x.SMSUser)
                        .FirstOrDefaultAsync();

                    if (conversation == null)
                    {
                        var userProfile = await _appDbContext.UserProfiles
                            .Include(x => x.SMSUser)
                            .FirstOrDefaultAsync(
                                x =>
                                    x.SMSUserId.HasValue &&
                                    x.SMSUser.InstanceId == channelIdentityId &&
                                    x.SMSUser.SMSId == channelUserIdentityId
                                    && x.CompanyId == companyId);

                        if (userProfile != null)
                        {
                            conversation = await _appDbContext.Conversations
                                .FirstOrDefaultAsync(
                                    x =>
                                        x.UserProfileId == userProfile.Id
                                        && x.CompanyId == companyId);

                            conversation.SMSUserId = userProfile.SMSUserId;
                            conversation.SMSUser = userProfile.SMSUser;
                            await _appDbContext.SaveChangesAsync();
                        }
                    }

                    break;
                case ChannelTypes.Wechat:
                    conversation = await _appDbContext.Conversations
                        .Where(
                            x => x.CompanyId == companyId
                                 && x.WeChatUserId.HasValue
                                 && x.WeChatUser.ChannelIdentityId == channelIdentityId
                                 && x.WeChatUser.openid == channelUserIdentityId)
                        .Include(x => x.WeChatUser)
                        .FirstOrDefaultAsync();

                    break;

                case ChannelTypes.Viber:
                    conversation = await _appDbContext.Conversations.Where(
                        x => x.CompanyId == companyId && x.ViberUserId.HasValue &&
                             x.ViberUser.ViberUserId == channelUserIdentityId &&
                             x.ViberUser.ViberBotId == channelIdentityId).FirstOrDefaultAsync();
                    break;
                case "naive":
                case ChannelTypes.Email:
                    conversation = await _appDbContext.Conversations
                        .Where(
                            x =>
                                x.EmailAddressId.HasValue &&
                                x.EmailAddress.ChannelIdentityId == channelIdentityId &&
                                x.EmailAddress.Email == channelUserIdentityId
                                && x.CompanyId == companyId)
                        .Include(x => x.EmailAddress)
                        .FirstOrDefaultAsync();

                    break;
                default:
                    throw new ArgumentOutOfRangeException();
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unable to get conversation with sender from {CompanyId}, {Channel} {ChannelIdentityId} {ChannelUserIdentityId}", companyId, channel, channelIdentityId, channelUserIdentityId);

            throw;
        }

        if (conversation == null)
        {
            throw new NullReferenceException();
        }

        return conversation;
    }

    public async Task<Conversation> GetConversationAsync(
        string companyId,
        string conversationId,
        string channel,
        string channelIdentityId,
        string channelUserIdentityId)
    {
        Conversation conversation;

        try
        {
            var conversationQueryable = _appDbContext.Conversations.Where(
                conversations => conversations.Id == conversationId && conversations.CompanyId == companyId);

            switch (channel)
            {
                case ChannelTypes.WhatsappCloudApi:
                    conversation = await conversationQueryable.Include(x => x.WhatsappCloudApiUser).FirstOrDefaultAsync();

                    if (conversation is { WhatsappCloudApiUser: null })
                    {
                        conversation.WhatsappCloudApiUser = ConstructWhatsappCloudApiSender(
                            companyId,
                            conversation.Id,
                            conversation.UserProfileId,
                            channelIdentityId,
                            channelUserIdentityId);
                    }
                    else if (conversation != null && !string.IsNullOrEmpty(channelIdentityId) && conversation.WhatsappCloudApiUser.WhatsappChannelPhoneNumber != channelIdentityId)
                    {
                        var userProfile = await _userProfileService.SwitchUserProfileMessagingChannel(
                            companyId,
                            conversation.UserProfileId,
                            channel,
                            channelIdentityId);
                        conversation.WhatsappCloudApiUser = userProfile.WhatsappCloudApiUser;
                    }

                    break;
                case ChannelTypes.Whatsapp360Dialog:
                    conversation = await conversationQueryable.Include(x => x.WhatsApp360DialogUser).FirstOrDefaultAsync();

                    if (conversation is {WhatsApp360DialogUser: null})
                    {
                        var whatsapp360DialogConfig =
                            await _appDbContext.ConfigWhatsApp360DialogConfigs.FirstOrDefaultAsync(
                                x => x.CompanyId == companyId && x.WhatsAppPhoneNumber == channelIdentityId);

                        if (whatsapp360DialogConfig != null)
                        {
                            conversation.WhatsApp360DialogUser = await ConstructWhatsapp360DialogSenderAsync(
                                companyId,
                                whatsapp360DialogConfig.Id,
                                whatsapp360DialogConfig.WhatsAppPhoneNumber,
                                channelUserIdentityId);
                        }
                    }
                    else if (conversation != null && !string.IsNullOrEmpty(channelIdentityId) && conversation.WhatsApp360DialogUser.ChannelIdentityId != channelIdentityId)
                    {
                        var userProfile = await _userProfileService.SwitchUserProfileMessagingChannel(
                            companyId,
                            conversation.UserProfileId,
                            channel,
                            channelIdentityId);
                        conversation.WhatsApp360DialogUser = userProfile.WhatsApp360DialogUser;
                    }

                    break;
                case ChannelTypes.WhatsappTwilio:
                case "twilio_whatsapp":
                    conversation = await conversationQueryable.Include(x => x.WhatsappUser).FirstOrDefaultAsync() ?? new Conversation()
                    {
                        CompanyId = companyId,
                    };

                    conversation = await HandleConversationWhatsappSenderAsync(
                        conversation,
                        companyId,
                        channelUserIdentityId,
                        channelIdentityId);

                    break;
                case ChannelTypes.Facebook:
                    conversation = await conversationQueryable.Include(x => x.facebookUser).FirstOrDefaultAsync();

                    if (conversation is { facebookUser: null })
                    {
                        conversation.facebookUser = await _appDbContext.SenderFacebookSenders.FirstOrDefaultAsync(
                            x => x.pageId == channelIdentityId && x.FacebookId == channelUserIdentityId &&
                                 x.CompanyId == companyId) ?? ConstructFacebookSender(
                            companyId,
                            channelIdentityId,
                            channelUserIdentityId);
                    }

                    break;
                case ChannelTypes.Instagram:
                    conversation = await conversationQueryable.Include(x => x.InstagramUser).FirstOrDefaultAsync();

                    if (conversation is { InstagramUser: null })
                    {
                        conversation.InstagramUser = await _appDbContext.SenderInstagramSenders.FirstOrDefaultAsync(
                            x => x.InstagramPageId == channelIdentityId && x.InstagramId == channelUserIdentityId &&
                                 x.CompanyId == companyId) ?? await ConstructInstagramSenderAsync(
                            companyId,
                            channelIdentityId,
                            channelUserIdentityId);
                    }

                    break;
                case ChannelTypes.Line:
                    conversation = await conversationQueryable.Include(x => x.LineUser).FirstOrDefaultAsync();

                    if (conversation is { LineUser: null })
                    {
                        conversation.LineUser =
                            await _appDbContext.SenderLineSender.FirstOrDefaultAsync(
                                x => x.CompanyId == companyId && x.ChannelIdentityId == channelIdentityId &&
                                     x.userId == channelUserIdentityId) ?? ConstructLineSender(
                                companyId,
                                channelIdentityId,
                                channelUserIdentityId);
                    }

                    break;
                case ChannelTypes.Telegram:
                    conversation = await conversationQueryable.Include(x => x.TelegramUser).FirstOrDefaultAsync();

                    if (conversation is { TelegramUser: null } && long.TryParse(channelIdentityId, out var telegramBotId) && long.TryParse(channelUserIdentityId, out var telegramChatId))
                    {
                        conversation.TelegramUser =
                            await _appDbContext.SenderTelegramSenders.FirstOrDefaultAsync(
                                x => x.CompanyId == companyId
                                     && x.TelegramBotId == telegramBotId
                                     && x.TelegramChatId == telegramChatId) ?? ConstructTelegramSender(
                                companyId,
                                telegramBotId,
                                telegramChatId);
                    }

                    break;
                case ChannelTypes.Sms:
                    conversation = await conversationQueryable.Include(x => x.SMSUser).FirstOrDefaultAsync();

                    if (conversation is { SMSUser: null })
                    {
                        conversation.SMSUser =
                            await _appDbContext.SenderSMSSenders.FirstOrDefaultAsync(
                                x => x.CompanyId == companyId && x.InstanceId == channelIdentityId &&
                                     x.phone_number == channelUserIdentityId) ?? ConstructSmsSender(
                                companyId,
                                channelIdentityId,
                                channelUserIdentityId);
                    }

                    break;
                case ChannelTypes.Wechat:
                    conversation = await conversationQueryable.Include(x => x.WeChatUser).FirstOrDefaultAsync();

                    if (conversation is { WeChatUser: null })
                    {
                        conversation.WeChatUser = await _appDbContext.SenderWeChatSenders.FirstOrDefaultAsync(
                            x =>
                                x.ChannelIdentityId == channelIdentityId
                                && x.openid == channelUserIdentityId
                                && x.CompanyId == companyId) ?? ConstructWeChatSender(
                            companyId,
                            channelIdentityId,
                            channelUserIdentityId);
                    }

                    break;
                case ChannelTypes.Viber:
                    conversation = await conversationQueryable.Include(x => x.ViberUser).FirstOrDefaultAsync();

                    if (conversation is {ViberUser: null})
                    {
                        conversation.ViberUser =
                            await _appDbContext.SenderViberSenders.FirstOrDefaultAsync(
                                x => x.CompanyId == companyId && x.ViberBotId == channelIdentityId &&
                                     x.ViberUserId == channelUserIdentityId) ?? ConstructViberSender(
                                companyId,
                                channelIdentityId,
                                channelUserIdentityId);
                    }

                    break;
                case "naive":
                case ChannelTypes.Email:
                    conversation = await conversationQueryable.Include(x => x.EmailAddress).FirstOrDefaultAsync();

                    if (conversation is { EmailAddress: null } )
                    {
                        conversation.EmailAddress =
                            await _appDbContext.SenderEmailSenders.FirstOrDefaultAsync(
                                x => x.CompanyId == companyId && x.ChannelIdentityId == channelIdentityId &&
                                     x.Email == channelUserIdentityId) ?? ConstructEmailSender(
                                companyId,
                                channelIdentityId,
                                channelUserIdentityId);
                    }

                    break;
                case ChannelTypes.LiveChat:
                    conversation = await conversationQueryable.Include(x => x.WebClient).FirstOrDefaultAsync();

                    if (!string.IsNullOrEmpty(channelIdentityId))
                    {
                        conversation.WebClient = await _appDbContext.SenderWebClientSenders
                            .Where(
                                x =>
                                    x.WebClientUUID == channelIdentityId
                                    && x.CompanyId == companyId)
                            .FirstOrDefaultAsync();
                    }
                    else if (!string.IsNullOrEmpty(channelUserIdentityId))
                    {
                        conversation.WebClient = await _appDbContext.SenderWebClientSenders
                            .Where(
                                x =>
                                    x.WebClientUUID == channelUserIdentityId
                                    && x.CompanyId == companyId)
                            .FirstOrDefaultAsync();
                    }

                    break;
                default:
                    throw new ArgumentOutOfRangeException();
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unable to get conversation with sender from {CompanyId} {ConversationId}, {Channel}", companyId, conversationId, channel);

            throw;
        }

        if (conversation == null)
        {
            throw new NullReferenceException();
        }

        return conversation;
    }

    public async Task<Conversation> GetConversationByChannelAsync(
        string companyId,
        string conversationId,
        string channel)
    {
        Conversation conversation = null;
        var conversationQueryable = _appDbContext.Conversations.Where(
            conversations => conversations.Id == conversationId && conversations.CompanyId == companyId);

        switch (channel)
        {
            case ChannelTypes.WhatsappCloudApi:
                conversation = await conversationQueryable.Include(x => x.WhatsappCloudApiUser).FirstOrDefaultAsync();
                break;
            case ChannelTypes.Whatsapp360Dialog:
                conversation = await conversationQueryable.Include(x => x.WhatsApp360DialogUser).FirstOrDefaultAsync();
                break;
            case ChannelTypes.WhatsappTwilio:
            case "twilio_whatsapp":
                conversation = await conversationQueryable.Include(x => x.WhatsappUser).FirstOrDefaultAsync();
                break;
            case ChannelTypes.Facebook:
                conversation = await conversationQueryable.Include(x => x.facebookUser).FirstOrDefaultAsync();
                break;
            case ChannelTypes.Instagram:
                conversation = await conversationQueryable.Include(x => x.InstagramUser).FirstOrDefaultAsync();
                break;
            case ChannelTypes.Line:
                conversation = await conversationQueryable.Include(x => x.LineUser).FirstOrDefaultAsync();
                break;
            case ChannelTypes.Telegram:
                conversation = await conversationQueryable.Include(x => x.TelegramUser).FirstOrDefaultAsync();
                break;
            case ChannelTypes.Sms:
                conversation = await conversationQueryable.Include(x => x.SMSUser).FirstOrDefaultAsync();
                break;
            case ChannelTypes.Wechat:
                conversation = await conversationQueryable.Include(x => x.WeChatUser).FirstOrDefaultAsync();
                break;
            case ChannelTypes.Viber:
                conversation = await conversationQueryable.Include(x => x.ViberUser).FirstOrDefaultAsync();
                break;
            case "naive":
            case ChannelTypes.Email:
                conversation = await conversationQueryable.Include(x => x.EmailAddress).FirstOrDefaultAsync();
                break;
            case ChannelTypes.LiveChat:
                conversation = await conversationQueryable.Include(x => x.WebClient).FirstOrDefaultAsync();
                break;
            default:
                conversation = await conversationQueryable.FirstOrDefaultAsync();
                break;
        }

        if (conversation == null)
        {
            throw new NullReferenceException("Conversation not found");
        }

        var isMessagingChannelUserExisted = true;
        switch (channel)
        {
            case ChannelTypes.WhatsappCloudApi:
                isMessagingChannelUserExisted = conversation.WhatsappCloudApiUser != null;
                break;
            case ChannelTypes.Whatsapp360Dialog:
                isMessagingChannelUserExisted = conversation.WhatsApp360DialogUser != null;
                break;
            case ChannelTypes.WhatsappTwilio:
            case "twilio_whatsapp":
                isMessagingChannelUserExisted = conversation.WhatsappUser != null;
                break;
            case ChannelTypes.Facebook:
                isMessagingChannelUserExisted = conversation.facebookUser != null;
                break;
            case ChannelTypes.Instagram:
                isMessagingChannelUserExisted = conversation.InstagramUser != null;
                break;
            case ChannelTypes.Line:
                isMessagingChannelUserExisted = conversation.LineUser != null;
                break;
            case ChannelTypes.Telegram:
                isMessagingChannelUserExisted = conversation.TelegramUser != null;
                break;
            case ChannelTypes.Sms:
                isMessagingChannelUserExisted = conversation.SMSUser != null;
                break;
            case ChannelTypes.Wechat:
                isMessagingChannelUserExisted = conversation.WeChatUser != null;
                break;
            case ChannelTypes.Viber:
                isMessagingChannelUserExisted = conversation.ViberUser != null;
                break;
            case "naive":
            case ChannelTypes.Email:
                isMessagingChannelUserExisted = conversation.EmailAddress != null;
                break;
            case ChannelTypes.LiveChat:
                isMessagingChannelUserExisted = conversation.WebClient != null;
                break;
        }

        if (!isMessagingChannelUserExisted)
        {
            throw new NullReferenceException("Conversation does not contains sender for the messaging channel");
        }

        return conversation;
    }

    public async Task<Conversation> GetConversationAsync(
        Conversation conversation,
        ConversationMessage conversationMessage,
        int attempts = 3)
    {
        if (attempts <= 0)
        {
            _logger.LogError(
                "[{MethodName}] Unable to get conversation, conversation id: {ConversationId}, user profile id: {UserProfileId}",
                nameof(GetConversationAsync),
                conversation?.Id,
                conversationMessage?.Id);

            throw new Exception("Unable to get conversation");
        }

        var companyId = await _appDbContext.CompanyCompanies
            .WhereIf(
                !string.IsNullOrEmpty(conversation.CompanyId),
                x => x.Id == conversation.CompanyId)
            .WhereIf(
                !string.IsNullOrEmpty(conversation.MessageGroupName),
                x => x.Id == conversation.MessageGroupName)
            .Select(x => x.Id)
            .AsNoTracking()
            .FirstOrDefaultAsync();

        if (companyId == null)
        {
            return null;
        }

        if (conversation.IsSandbox)
        {
            // Sandbox
            if (conversation.WhatsappUser != null
                && !string.IsNullOrEmpty(conversation.WhatsappUser?.phone_number))
            {
                BackgroundJob.Enqueue<IUserProfileService>(
                    x => x.SetFieldValueByFieldNameSafe(
                        conversation.UserProfileId,
                        "phonenumber",
                        conversation.WhatsappUser.phone_number,
                        true));

                // await _userProfileService.SetFieldValueByFieldNameSafe(conversation.UserProfileId, "phonenumber", conversation.WhatsappUser.phone_number, true);
            }

            conversation.UserProfile = await _appDbContext.UserProfiles
                .Include(x => x.WhatsAppAccount)
                .FirstOrDefaultAsync(x => x.Id == conversation.UserProfileId);

            conversation.CompanyId = companyId;

            return conversation;
        }

        List<Conversation> existingConversations = new List<Conversation>();

        if (conversation.NaiveUser != null)
        {
            existingConversations = await _appDbContext.Conversations
                .Include(x => x.UserProfile)
                .Include(x => x.NaiveUser)
                .Where(c => (c.NaiveUser == conversation.NaiveUser) && c.CompanyId == companyId)
                .ToListAsync();
        }
        else if (conversation.facebookUser != null)
        {
            existingConversations = await _appDbContext.Conversations
                .Include(x => x.UserProfile)
                .Include(x => x.facebookUser)
                .Where(c => c.facebookUser.FacebookId == conversation.facebookUser.FacebookId && c.CompanyId == companyId)
                .ToListAsync();
        }
        else if (conversation.WhatsappUser != null)
        {
            existingConversations = await _appDbContext.Conversations
                .Include(x => x.UserProfile)
                .Include(x => x.WhatsappUser)
                .Include(x => x.WhatsAppSenderHistories)
                .Where(
                    c => c.WhatsappUser.whatsAppId == conversation.WhatsappUser.whatsAppId &&
                         c.WhatsappUser.InstanceId == conversation.WhatsappUser.InstanceId &&
                         c.WhatsappUser.CompanyId == companyId && c.CompanyId == companyId)
                .ToListAsync();

            if (existingConversations.Count == 0)
            {
                var user = await _userProfileService.AddOrUpdateUserProfile(
                    companyId,
                    new NewProfileViewModel
                    {
                        FirstName = conversation.WhatsappUser.name,
                        LastName = null,
                        WhatsAppPhoneNumber = conversation.WhatsappUser.phone_number,
                    });

                string newConversationStatus = GetNewConversationStatus(conversationMessage);

                existingConversations.Add(
                    await _userProfileService.GetConversationByUserProfileId(
                        companyId,
                        user.First().Id,
                        newConversationStatus,
                        false));
            }
        }
        else if (conversation.EmailAddress != null)
        {
            existingConversations = await _appDbContext.Conversations
                .Include(x => x.UserProfile)
                .Include(x => x.EmailAddress)
                .Where(c => c.EmailAddress.Email == conversation.EmailAddress.Email && c.CompanyId == companyId)
                .ToListAsync();

            if (existingConversations.FirstOrDefault() == null)
            {
                var existingUserProfile = await (
                        from _userProfile in _appDbContext.UserProfiles
                        where _userProfile.EmailAddress.Email == conversation.EmailAddress.Email
                              && _userProfile.CompanyId == companyId
                        select _userProfile)
                    .FirstOrDefaultAsync();

                if (existingUserProfile != null)
                {
                    conversation.EmailAddress = conversation.EmailAddress;
                    conversation.UserProfile = existingUserProfile;
                }
            }

            // }
        }
        else if (conversation.WebClient != null)
        {
            existingConversations = await _appDbContext.Conversations
                .Include(x => x.UserProfile)
                .Include(x => x.WebClient)
                .Where(
                    c => c.WebClient.WebClientUUID == conversation.WebClient.WebClientUUID &&
                         c.CompanyId == companyId)
                .ToListAsync();
        }
        else if (conversation.WeChatUser != null)
        {
            existingConversations = await _appDbContext.Conversations
                .Include(x => x.UserProfile)
                .Include(X => X.WeChatUser)
                .Where(
                    c => c.WeChatUser.openid == conversation.WeChatUser.openid
                         && c.CompanyId == companyId
                         && c.WeChatUser.ChannelIdentityId == conversation.WeChatUser.ChannelIdentityId)
                .ToListAsync();
        }
        else if (conversation.LineUser != null)
        {
            existingConversations = await _appDbContext.Conversations
                .Include(x => x.UserProfile)
                .Include(x => x.LineUser)
                .Where(c => c.LineUser.userId == conversation.LineUser.userId && c.CompanyId == companyId)
                .ToListAsync();
        }
        else if (conversation.ViberUser != null)
        {
            existingConversations = await _appDbContext.Conversations
                .Include(x => x.UserProfile)
                .Include(x => x.ViberUser)
                .Where(
                    c => c.CompanyId == companyId
                         && c.ViberUser.ViberUserId == conversation.ViberUser.ViberUserId
                         && c.ViberUser.ViberBotId == conversation.ViberUser.ViberBotId)
                .ToListAsync();
        }
        else if (conversation.TelegramUser != null)
        {
            existingConversations = await _appDbContext.Conversations
                .Include(x => x.UserProfile)
                .Include(x => x.TelegramUser)
                .Where(
                    c => c.CompanyId == companyId
                    && c.TelegramUser.TelegramBotId == conversation.TelegramUser.TelegramBotId
                    && c.TelegramUser.TelegramChatId == conversation.TelegramUser.TelegramChatId)
                .ToListAsync();
        }
        else if (conversation.WhatsApp360DialogUser != null)
        {
            existingConversations = await _appDbContext.Conversations
                .Include(x => x.UserProfile)
                .Include(x => x.WhatsApp360DialogUser)
                .Where(
                    c =>
                        c.WhatsApp360DialogUser.WhatsAppId == conversation.WhatsApp360DialogUser.WhatsAppId
                        && c.WhatsApp360DialogUser.CompanyId == companyId
                        && c.CompanyId == companyId)
                .ToListAsync();

            if (existingConversations.Count == 0)
            {
                var user = await _userProfileService.AddOrUpdateUserProfile(
                    companyId,
                    new NewProfileViewModel
                    {
                        FirstName = conversation.WhatsApp360DialogUser.Name,
                        LastName = null,
                        WhatsAppPhoneNumber = conversation.WhatsApp360DialogUser.PhoneNumber,
                    });

                string newConversationStatus = GetNewConversationStatus(conversationMessage);

                existingConversations.Add(
                    await _userProfileService.GetConversationByUserProfileId(
                        companyId,
                        user.First().Id,
                        newConversationStatus,
                        false));
            }
        }
        else if (conversation.WhatsappCloudApiUser != null &&
                 !string.IsNullOrWhiteSpace(conversation.WhatsappCloudApiUser.WhatsappId))
        {
            existingConversations = await _appDbContext.Conversations
                .Include(x => x.UserProfile)
                .Include(x => x.WhatsappCloudApiUser)
                .Where(
                    c =>
                        c.CompanyId == companyId
                        && c.WhatsappCloudApiUser.CompanyId == companyId
                        && c.WhatsappCloudApiUser.WhatsappId == conversation.WhatsappCloudApiUser.WhatsappId)
                .ToListAsync();

            if (existingConversations.Count == 0)
            {
                var userProfileId = await _appDbContext.WhatsappCloudApiSenders
                    .Where(
                        x => x.UserProfileId != null
                             && x.WhatsappId == conversation.WhatsappCloudApiUser.WhatsappId
                             && x.CompanyId == companyId)
                    .Select(x => x.UserProfileId)
                    .FirstOrDefaultAsync();

                // Create Conversation & UserProfile if one or either not exist
                var newConversationStatus = GetNewConversationStatus(conversationMessage);

                if (userProfileId == null)
                {
                    // Create User Profile and Conversation
                    var user = await _userProfileService.AddOrUpdateUserProfile(
                        companyId,
                        new NewProfileViewModel
                        {
                            FirstName = conversation.WhatsappCloudApiUser.WhatsappUserDisplayName,
                            LastName = null,
                            WhatsAppPhoneNumber = conversation.WhatsappCloudApiUser.WhatsappId,
                        });

                    existingConversations.Add(
                        await _userProfileService.GetConversationByUserProfileId(
                            companyId,
                            user.First().Id,
                            newConversationStatus,
                            false));
                }
                else
                {
                    // Create Conversation for UserProfile without conversation
                    existingConversations.Add(
                        await _userProfileService.GetConversationByUserProfileId(
                            companyId,
                            userProfileId,
                            newConversationStatus,
                            false));
                }
            }
        }
        else if (conversation.SMSUser != null)
        {
            existingConversations = await _appDbContext.Conversations
                .Include(x => x.UserProfile)
                .Include(x => x.SMSUser)
                .Where(c => c.SMSUser.SMSId == conversation.SMSUser.SMSId && c.CompanyId == companyId)
                .ToListAsync();

            if (existingConversations.Count == 0)
            {
                existingConversations = await _appDbContext.Conversations
                    .Include(x => x.UserProfile)
                    .Include(x => x.SMSUser)
                    .Where(
                        c => (c.WhatsappUser.whatsAppId ==
                              $"{conversation.SMSUser.SMSId.Replace("+", string.Empty)}@c.us" ||
                              c.WhatsappUser.whatsAppId == $"whatsapp:{conversation.SMSUser.SMSId}") &&
                             c.CompanyId == companyId)
                    .ToListAsync();

                if (existingConversations.FirstOrDefault() != null)
                {
                    existingConversations.FirstOrDefault().SMSUser = conversation.SMSUser;
                    existingConversations.FirstOrDefault().UserProfile.SMSUser = conversation.SMSUser;
                    await _appDbContext.SaveChangesAsync();

                    await _signalRService.SignalROnUserProfileUpdated(
                        existingConversations.FirstOrDefault().UserProfile);
                }
                else
                {
                    var existingUserProfile = await _appDbContext.UserProfiles.Where(
                            u => u.SMSUser.SMSId == conversation.SMSUser.SMSId &&
                                            u.CompanyId == companyId)
                        .FirstOrDefaultAsync();

                    if (existingUserProfile != null)
                    {
                        conversation.UserProfile = existingUserProfile;
                    }
                }
            }
        }
        else if (conversation.InstagramUser != null)
        {
            existingConversations = await _appDbContext.Conversations
                .Include(x => x.UserProfile)
                .Include(x => x.InstagramUser)
                .Where(
                    c => c.InstagramUser.InstagramId == conversation.InstagramUser.InstagramId &&
                         c.CompanyId == companyId)
                .ToListAsync();
        }
        else
        {
            existingConversations = await _appDbContext.Conversations
                .Where(c => c.Id == conversation.Id)
                .Include(x=>x.UserProfile)
                .ToListAsync();
        }

        if (existingConversations.FirstOrDefault() == null)
        {
            try
            {
                conversation.CompanyId = companyId;
                _appDbContext.Conversations.Add(conversation);
                await _appDbContext.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Create Conversation Error: Conversation {ConversationPayload}; Exception: {ExceptionString}",
                    JsonConvert.SerializeObject(conversation),
                    ex.ToString());

                return await GetConversationAsync(conversation, conversationMessage, attempts - 1);
            }

            await _signalRService.SignalROnConversationAdded(conversation);
        }
        else
        {
            conversation = existingConversations.FirstOrDefault();
        }

        if (conversation.UserProfile == null)
        {
            try
            {
                // create profile
                UserProfile userProfile = new UserProfile
                {
                    CompanyId = companyId,
                    WhatsAppAccount = conversation.WhatsappUser,
                    FacebookAccount = conversation.facebookUser,
                    WeChatUser = conversation.WeChatUser,
                    LineUser = conversation.LineUser,
                    ViberUser = conversation.ViberUser,
                    TelegramUser = conversation.TelegramUser,
                    WebClient = conversation.WebClient,
                    EmailAddress = conversation.EmailAddress,
                    UserDevice = conversation.UserDevice,
                    RegisteredUser = conversation.NaiveUser,
                    SMSUser = conversation.SMSUser,
                    InstagramUser = conversation.InstagramUser,
                    WhatsApp360DialogUser = conversation.WhatsApp360DialogUser,
                    WhatsappCloudApiUser = conversation.WhatsappCloudApiUser,
                };

                if (conversation.WebClient != null)
                {
                    userProfile.ActiveStatus = ActiveStatus.Inactive;
                }

                if (userProfile.InstagramUser != null)
                {
                    userProfile.FirstName = userProfile.InstagramUser.Username;
                }

                conversation.UserProfile = userProfile;
                await _signalRService.SignalROnUserProfileAdded(userProfile);

                if ((conversationMessage.IsSentFromSleekflow
                     || !string.IsNullOrEmpty(conversationMessage.MessageUniqueID))
                    && conversation.WhatsappUserId.HasValue)
                {
                    userProfile.FirstName = conversation.WhatsappUser.phone_number;
                }

                if ((conversationMessage.IsSentFromSleekflow ||
                     !string.IsNullOrEmpty(conversationMessage.MessageUniqueID)) &&
                    conversation.WhatsApp360DialogUserId.HasValue)
                {
                    if (!string.IsNullOrEmpty(conversation.WhatsApp360DialogUser?.Name))
                    {
                        userProfile.FirstName = conversation.WhatsApp360DialogUser.Name;
                    }
                    else if (!string.IsNullOrEmpty(conversation.WhatsApp360DialogUser?.PhoneNumber))
                    {
                        userProfile.FirstName = conversation.WhatsApp360DialogUser.PhoneNumber;
                    }
                }

                if ((conversationMessage.IsSentFromSleekflow ||
                     !string.IsNullOrEmpty(conversationMessage.MessageUniqueID)) &&
                    conversation.WhatsappCloudApiUser != null)
                {
                    if (!string.IsNullOrEmpty(conversation.WhatsappCloudApiUser?.WhatsappUserDisplayName))
                    {
                        userProfile.FirstName = conversation.WhatsappCloudApiUser.WhatsappUserDisplayName;
                    }
                    else if (!string.IsNullOrEmpty(conversation.WhatsappCloudApiUser?.WhatsappId))
                    {
                        userProfile.FirstName = conversation.WhatsappCloudApiUser.WhatsappId;
                    }
                }

                await _appDbContext.SaveChangesAsync();

                BackgroundJob.Schedule<IAutomationService>(
                    x => x.NewContactTrigger(userProfile.Id),
                    TimeSpan.FromSeconds(30));

                await _userProfileHooks.OnUserProfileCreatedAsync(
                    companyId,
                    userProfile.Id,
                    null,
                    () => Task.FromResult(new OnUserProfileCreatedData(userProfile)));

                Dictionary<string, string> userProfileFieldsDict = new ()
                {
                    ["Subscriber"] = "true", ["firstname"] = userProfile.FirstName ?? string.Empty
                };

                var importUserProfile = userProfileFieldsDict.ToImportUserProfileObject();

                userProfile = await _userProfileService.BulkSetFields(
                    userProfile,
                    null,
                    importUserProfile);
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[{MethodName}] Company {CompanyId} create user profile error for conversation {ConversationId}",
                    nameof(GetConversationAsync),
                    companyId,
                    conversation?.Id);

                return await GetConversationAsync(conversation, conversationMessage, attempts - 1);
            }
        }
        else
        {
            // Duplicated responsibility
            if (conversation.WhatsappUser != null
                && conversation.UserProfile.FirstName == conversation.WhatsappUser?.phone_number
                && !string.IsNullOrWhiteSpace(conversation.WhatsappUser?.name))
            {
                conversation.UserProfile.FirstName = conversation.WhatsappUser.name;
            }

            if (conversation.WhatsApp360DialogUser != null
                && conversation.UserProfile.FirstName == conversation.WhatsApp360DialogUser?.WhatsAppId
                && !string.IsNullOrWhiteSpace(conversation.WhatsApp360DialogUser?.Name))
            {
                conversation.UserProfile.FirstName = conversation.WhatsApp360DialogUser?.Name;
            }

            if (conversation.WhatsappCloudApiUser?.WhatsappUserDisplayName != null
                && conversation.UserProfile.FirstName == conversation.WhatsappCloudApiUser.WhatsappId
                && !string.IsNullOrWhiteSpace(conversation.WhatsappCloudApiUser?.WhatsappUserDisplayName))
            {
                conversation.UserProfile.FirstName = conversation.WhatsappCloudApiUser?.WhatsappUserDisplayName;
            }

            if (conversation.UserProfile.ActiveStatus == ActiveStatus.Inactive)
            {
                await TryRecoverSoftDeletedUserProfileAsync(conversation);
            }
        }

        try
        {
            if (conversation.WhatsappUser != null
                && !string.IsNullOrEmpty(conversation.WhatsappUser?.phone_number))
            {
                if (conversation.UserProfile.PhoneNumber == null
                    || conversation.UserProfile.PhoneNumber != conversation.WhatsappUser.phone_number)
                {
                    await _userProfileService.SetFieldValueByFieldNameNotSaved(
                        conversation.UserProfile,
                        "phonenumber",
                        conversation.WhatsappUser.phone_number);
                }
            }

            if (conversation.WhatsApp360DialogUser != null &&
                !string.IsNullOrEmpty(conversation.WhatsApp360DialogUser?.PhoneNumber))
            {
                if (conversation.UserProfile.PhoneNumber == null
                    || conversation.UserProfile.PhoneNumber != conversation.WhatsApp360DialogUser?.WhatsAppId)
                {
                    await _userProfileService.SetFieldValueByFieldNameNotSaved(
                        conversation.UserProfile,
                        "phonenumber",
                        PhoneNumberHelper.NormalizePhoneNumber(conversation.WhatsApp360DialogUser.PhoneNumber));
                }
            }

            if (conversation.WhatsappCloudApiUser != null
                && !string.IsNullOrEmpty(conversation.WhatsappCloudApiUser?.WhatsappId))
            {
                if (conversation.UserProfile.PhoneNumber == null
                    || conversation.UserProfile.PhoneNumber != conversation.WhatsappCloudApiUser?.WhatsappId)
                {
                    await _userProfileService.SetFieldValueByFieldNameNotSaved(
                        conversation.UserProfile,
                        "phonenumber",
                        PhoneNumberHelper.NormalizePhoneNumber(conversation.WhatsappCloudApiUser.WhatsappId));
                }
            }

            if (conversation.EmailAddress != null)
            {
                if (conversation.UserProfile.Email == null
                    || conversation.UserProfile.Email != conversation.EmailAddress.Email)
                {
                    await _userProfileService.SetFieldValueByFieldNameNotSaved(
                        conversation.UserProfile,
                        "email",
                        conversation.EmailAddress.Email);
                }

                // BackgroundJob.Enqueue<IUserProfileService>(x => x.SetFieldValueByFieldNameSafe(conversation.UserProfile.Id, "email", conversation.EmailAddress.Email));
            }

            try
            {
                if (conversation.WhatsappUserId.HasValue && conversation.WhatsappUser != null)
                {
                    if (!conversation.WhatsAppSenderHistories.Any(
                            x => x.InstanceId == conversation.WhatsappUser.InstanceId &&
                                 x.InstanceSender == conversation.WhatsappUser.InstaneSender))
                    {
                        conversation.WhatsAppSenderHistories.Add(
                            new ConversationWhatsappSenderHistory()
                            {
                                ConversationId = conversation.Id,
                                InstanceSender = conversation.WhatsappUser.InstaneSender,
                                InstanceId = conversation.WhatsappUser.InstanceId
                            });
                        await _appDbContext.SaveChangesAsync();
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[{MethodName}] Company {CompanyId} unable to add whatsapp sender history",
                    nameof(GetConversationAsync),
                    companyId);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "[{MethodName}] Company {CompanyId} unable to save phoneNumber",
                nameof(GetConversationAsync),
                companyId);
        }

        return conversation;
    }

    public async Task<(Conversation Conversation, ILockService.Lock MyLock)> GetConversationForReceivingMessageAsync(
        string companyId,
        string channel,
        string channelIdentityId,
        string channelUserIdentityId,
        string channelUserName = null,
        string webhookString = null)
    {
        Conversation conversation;
        ILockService.Lock myLock;
        switch (channel)
        {
            case ChannelTypes.WhatsappCloudApi:
                (var whatsappCloudApiSender, myLock) = await GetConversationWhatsappCloudApiSenderAndLockAsync(
                    companyId,
                    channelIdentityId,
                    channelUserIdentityId,
                    channelUserName);

                conversation = new Conversation
                {
                    MessageGroupName = companyId,
                    WhatsappCloudApiUser = whatsappCloudApiSender,
                    ActiveStatus = ActiveStatus.Active,
                    CompanyId = companyId,
                    LastMessageChannelId = await _appDbContext.ConfigWhatsappCloudApiConfigs
                        .Where(x => x.CompanyId == companyId && x.WhatsappPhoneNumber == channelIdentityId)
                        .Select(x => x.Id).FirstOrDefaultAsync(),
                    LastChannelIdentityId = channelIdentityId
                };

                break;
            case ChannelTypes.Whatsapp360Dialog:
                var configId = await _appDbContext.ConfigWhatsApp360DialogConfigs
                    .Where(y => y.CompanyId == companyId && y.WhatsAppPhoneNumber == channelIdentityId)
                    .Select(y => y.Id).FirstOrDefaultAsync();
                (var whatsApp360DialogSender, myLock) = await GetConversationWhatsapp360DialogSenderAndLockAsync(
                    companyId,
                    configId,
                    channelIdentityId,
                    channelUserIdentityId,
                    channelUserName);

                conversation = new Conversation
                {
                    MessageGroupName = companyId,
                    WhatsApp360DialogUser = whatsApp360DialogSender,
                    ActiveStatus = ActiveStatus.Active,
                    CompanyId = companyId,
                    LastMessageChannelId = configId,
                    LastChannelIdentityId = channelIdentityId
                };
                break;
            case ChannelTypes.WhatsappTwilio:
                (var whatsappSender, myLock) = await GetConversationWhatsappSenderAndLockAsync(
                    companyId,
                    channelIdentityId,
                    channelUserIdentityId,
                    channelUserName);

                conversation = new Conversation
                {
                    MessageGroupName = companyId,
                    WhatsappUser = whatsappSender,
                    CompanyId = companyId,
                    ActiveStatus = ActiveStatus.Active,
                    LastMessageChannelId = await _appDbContext.ConfigWhatsAppConfigs
                        .Where(x => x.CompanyId == companyId && x.WhatsAppSender == channelIdentityId).Select(x => x.Id)
                        .FirstOrDefaultAsync(),
                    LastChannelIdentityId = channelIdentityId
                };
                break;
            case ChannelTypes.Sms:
                (var smsSender, myLock) = await GetConversationSmsSenderAndLockAsync(
                    companyId,
                    channelIdentityId,
                    channelUserIdentityId);

                conversation = new Conversation
                {
                    MessageGroupName = companyId,
                    SMSUser = smsSender,
                    CompanyId = companyId,
                    ActiveStatus = ActiveStatus.Active
                };
                break;
            case ChannelTypes.Facebook:
                (var facebookSender, myLock) = await GetConversationFacebookSenderAndLockAsync(
                    companyId,
                    channelIdentityId,
                    channelUserIdentityId,
                    channelUserName);

                conversation = new Conversation()
                {
                    MessageGroupName = companyId,
                    facebookUser = facebookSender,
                    CompanyId = companyId,
                    ActiveStatus = ActiveStatus.Active,
                    LastChannelIdentityId = channelIdentityId
                };
                break;
            case ChannelTypes.Instagram:
                (var instagramSender, myLock) = await GetConversationInstagramSenderAndLockAsync(
                    companyId,
                    channelIdentityId,
                    channelUserIdentityId,
                    channelUserName);

                conversation = new Conversation()
                {
                    MessageGroupName = companyId,
                    InstagramUser = instagramSender,
                    CompanyId = companyId,
                    ActiveStatus = ActiveStatus.Active,
                    LastChannelIdentityId = channelIdentityId
                };
                break;
            case ChannelTypes.Wechat:
                (var wechatSender, myLock) = await GetConversationWechatSenderAndLockAsync(
                    companyId,
                    channelIdentityId,
                    channelUserIdentityId);

                conversation = new Conversation()
                {
                    MessageGroupName = companyId,
                    WeChatUser = wechatSender,
                    CompanyId = companyId,
                    ActiveStatus = ActiveStatus.Active,
                    LastChannelIdentityId = channelIdentityId
                };
                break;
            case ChannelTypes.Telegram:
                (var telegramSender, myLock) = await GetConversationTelegramSenderAndLockAsync(
                    companyId,
                    channelIdentityId,
                    channelUserIdentityId);

                conversation = new Conversation()
                {
                    TelegramUser = telegramSender,
                    MessageGroupName = companyId,
                    CompanyId = companyId,
                    ActiveStatus = ActiveStatus.Active,
                    LastChannelIdentityId = channelIdentityId
                };
                break;
            case ChannelTypes.Line:
                (var lineSender, myLock) = await GetConversationLineSenderAndLockAsync(
                    companyId,
                    channelIdentityId,
                    channelUserIdentityId);

                conversation = new Conversation()
                {
                    LineUser = lineSender,
                    MessageGroupName = companyId,
                    CompanyId = companyId,
                    ActiveStatus = ActiveStatus.Active,
                    LastChannelIdentityId = channelIdentityId
                };
                break;
            case ChannelTypes.Viber:
                (var viberSender, myLock) = await GetConversationViberSenderAndLockAsync(
                    companyId,
                    channelIdentityId,
                    channelUserIdentityId,
                    webhookString);

                conversation = new Conversation()
                {
                    ViberUser = viberSender,
                    MessageGroupName = companyId,
                    CompanyId = companyId,
                    ActiveStatus = ActiveStatus.Active,
                    LastChannelIdentityId = channelIdentityId
                };
                break;
            case ChannelTypes.Email:
                (var emailSender, myLock) = await GetConversationEmailSenderAndLockAsync(
                    companyId,
                    channelIdentityId,
                    channelUserIdentityId,
                    channelUserName);
                conversation = new Conversation()
                {
                    EmailAddress = emailSender,
                    MessageGroupName = companyId,
                    CompanyId = companyId,
                    ActiveStatus = ActiveStatus.Active,
                    LastChannelIdentityId = channelIdentityId
                };
                break;
            default:
                throw new NotImplementedException();
        }

        return (conversation, myLock);
    }

    private async Task<(WhatsappCloudApiSender WhatsappCloudApiSender, ILockService.Lock MyLock)> GetConversationWhatsappCloudApiSenderAndLockAsync(
        string companyId,
        string channelWhatsappPhoneNumber,
        string whatsappId,
        string whatsappUserDisplayName)
    {
        var messageFrom = PhoneNumberHelper.NormalizeWhatsappPhoneNumber(whatsappId);

        var sender = await _appDbContext.WhatsappCloudApiSenders
            .Where(
                x => x.CompanyId == companyId
                     && x.WhatsappId == messageFrom
                     && x.UserProfileId != null)
            .FirstOrDefaultAsync();

        ILockService.Lock myLock = null;

        // DEVS-5422 Brazilian phone number fix (https://app.clickup.com/t/9008009945/DEVS-5422):
        // Check if there is existing 12 digits / 13 digits WhatsAppId from DB for avoiding duplicate conversations
        if (sender == null && whatsappId.StartsWith("55") && whatsappId.Length is 12 or 13)
        {
            var brazilianPhoneNumberSender = await ResolveBrazilianPhoneNumberSenderAsync(
                companyId,
                whatsappId,
                ChannelTypes.WhatsappCloudApi);

            if (brazilianPhoneNumberSender is WhatsappCloudApiSender existingCloudApiSender)
            {
                sender = existingCloudApiSender;
            }
        }

        // Fix for ticket PDD-4836. Caused by AddBackWhatsappCloudApiSenderToUserProfile()
        // which will set CloudApiSender.ConversationId to null if no Conversation linked to the UserProfile when connecting WABA
        if (sender != null && string.IsNullOrEmpty(sender.ConversationId))
        {
            sender.ConversationId = await _appDbContext.Conversations
                .Where(x => x.CompanyId == companyId && x.UserProfileId == sender.UserProfileId)
                .Select(x => x.Id)
                .FirstOrDefaultAsync();
        }

        // Create or wait for creation flow
        if (sender == null || sender?.ConversationId == null)
        {
            while (sender == null || sender?.ConversationId == null)
            {
                myLock = await _lockService.AcquireLockAsync(
                    WhatsappCloudApiCacheKeyHelper.GetAddNewContactRedLockKey(companyId, messageFrom),
                    TimeSpan.FromSeconds(10));

                if (myLock == null)
                {
                    await Task.Delay(TimeSpan.FromSeconds(10));

                    sender = await _appDbContext.WhatsappCloudApiSenders
                        .Where(
                            x => x.CompanyId == companyId
                                 && x.WhatsappId == messageFrom
                                 && x.ConversationId != null
                                 && x.UserProfileId != null)
                        .FirstOrDefaultAsync();
                }
                else
                {
                    // Lock acquired
                    _logger.LogInformation(
                        "[{MethodName}] Company {CompanyId} locking contact WhatsApp phone number {ContactWhatsappNumber} for channel {ChannelWhatsappPhoneNumber} successfully",
                        nameof(GetConversationWhatsappCloudApiSenderAndLockAsync),
                        companyId,
                        whatsappId,
                        channelWhatsappPhoneNumber);
                    break;
                }
            }

            // Final Retrieve
            sender ??= await _appDbContext.WhatsappCloudApiSenders
                           .Where(
                               x => x.CompanyId == companyId
                                    && x.WhatsappId == messageFrom
                                    && x.ConversationId != null
                                    && x.UserProfileId != null)
                           .FirstOrDefaultAsync();

            if (sender == null)
            {
                sender = new WhatsappCloudApiSender()
                {
                    CompanyId = companyId,
                    WhatsappId = messageFrom,
                    WhatsappUserDisplayName = whatsappUserDisplayName,
                    WhatsappChannelPhoneNumber = channelWhatsappPhoneNumber,
                };
            }
        }
        else if (sender.WhatsappChannelPhoneNumber != channelWhatsappPhoneNumber)
        {
            sender.WhatsappChannelPhoneNumber = channelWhatsappPhoneNumber;
        }

        if (sender.WhatsappUserDisplayName != whatsappUserDisplayName)
        {
            sender.WhatsappUserDisplayName = whatsappUserDisplayName;
        }

        return (sender, myLock);
    }

    private async Task<(WhatsApp360DialogSender WhatsApp360DialogSender, ILockService.Lock MyLock)> GetConversationWhatsapp360DialogSenderAndLockAsync(
        string companyId,
        long configId,
        string channelWhatsappPhoneNumber,
        string whatsappId,
        string whatsappUserDisplayName)
    {
        var messageFrom = PhoneNumberHelper.NormalizeWhatsappPhoneNumber(whatsappId);

        var sender = await _appDbContext.UserProfiles
            .Where(
                x => x.CompanyId == companyId
                     && x.PhoneNumber == messageFrom
                     && x.WhatsApp360DialogUser.CompanyId == companyId
                     && x.WhatsApp360DialogUser.WhatsAppId == messageFrom)
            .Select(x => x.WhatsApp360DialogUser)
            .FirstOrDefaultAsync();

        // DEVS-5422 Brazilian phone number fix (https://app.clickup.com/t/9008009945/DEVS-5422):
        // Check if there is existing 12 digits / 13 digits WhatsAppId from DB for avoiding duplicate conversations
        if (sender == null && whatsappId.StartsWith("55") && whatsappId.Length is 12 or 13)
        {
             var brazilianPhoneNumberSender = await ResolveBrazilianPhoneNumberSenderAsync(companyId, whatsappId, ChannelTypes.Whatsapp360Dialog);
             if (brazilianPhoneNumberSender is WhatsApp360DialogSender existing360DialogSender)
             {
                 sender = existing360DialogSender;
             }
        }

        var whatsappUsername = whatsappUserDisplayName;

        ILockService.Lock myLock = null;
        if (sender == null)
        {
            while (true)
            {
                // Add random delay 100-500 ms to avoid locking at the same time
                await Task.Delay(TimeSpan.FromMilliseconds(new Random().Next(1, 5) * 100));
                myLock = await _lockService.AcquireLockAsync(
                    WhatsApp360DialogCacheKeyHelper.GetAddNewContactRedLockKey(
                        companyId,
                        messageFrom),
                    TimeSpan.FromSeconds(10));
                if (myLock == null)
                {
                    await Task.Delay(TimeSpan.FromSeconds(10));
                }
                else
                {
                    break;
                }
            }

            sender = await _appDbContext.SenderWhatsApp360DialogSenders
                .Where(
                    x => x.CompanyId == companyId &&
                         x.WhatsAppId == messageFrom &&
                         x.ChannelId == configId)
                .FirstOrDefaultAsync() ?? new WhatsApp360DialogSender()
            {
                WhatsAppId = messageFrom,
                PhoneNumber = "+" + messageFrom,
                CompanyId = companyId,
                Name = whatsappUsername,
                ContactStatus = ContactStatus.valid.GetString(),
                ChannelId = configId,
                ChannelWhatsAppPhoneNumber = channelWhatsappPhoneNumber,
            };

        }
        else if (sender.ChannelId != configId)
        {
            sender = new WhatsApp360DialogSender()
            {
                WhatsAppId = messageFrom,
                PhoneNumber = "+" + messageFrom,
                CompanyId = companyId,
                Name = whatsappUsername,
                ContactStatus = ContactStatus.valid.GetString(),
                ChannelId = configId,
                ChannelWhatsAppPhoneNumber = channelWhatsappPhoneNumber,
            };
        }

        if (sender.Name != whatsappUsername)
        {
            sender.Name = whatsappUsername;
        }

        return (sender, myLock);
    }

    private async Task<IMessagingChannelUser> ResolveBrazilianPhoneNumberSenderAsync(string companyId, string whatsappId, string channelType)
    {
        var existingSenderWhatsappId = whatsappId;

        if (whatsappId.Length == 12)
        {
            existingSenderWhatsappId = whatsappId.Insert(4, "9");
        }
        else if (whatsappId.Length == 13 && whatsappId.Substring(4, 1) == "9")
        {
            existingSenderWhatsappId = whatsappId.Remove(4, 1);
        }

        IMessagingChannelUser sender = channelType switch
        {
            ChannelTypes.WhatsappCloudApi => await _appDbContext.WhatsappCloudApiSenders
                .Where(x => x.CompanyId == companyId && x.WhatsappId == existingSenderWhatsappId)
                .FirstOrDefaultAsync(),
            ChannelTypes.Whatsapp360Dialog => await _appDbContext.UserProfiles
                .Where(
                    x => x.CompanyId == companyId && x.PhoneNumber == whatsappId
                                                  && x.WhatsApp360DialogUser.CompanyId == companyId
                                                  && x.WhatsApp360DialogUser.WhatsAppId == existingSenderWhatsappId)
                .Select(x => x.WhatsApp360DialogUser)
                .FirstOrDefaultAsync(),
            _ => null
        };

        return sender;
    }

    private async Task<(WhatsAppSender WhatsappSender, ILockService.Lock MyLock)>
        GetConversationWhatsappSenderAndLockAsync(
            string companyId,
            string channelWhatsappPhoneNumber,
            string whatsappId,
            string whatsappUserDisplayName)
    {
        var twilioConfig = await _appDbContext.ConfigWhatsAppConfigs.Where(
                            x => x.CompanyId == companyId && x.WhatsAppSender == channelWhatsappPhoneNumber)
                        .FirstOrDefaultAsync();
        ILockService.Lock myLock = null;

        while (true)
        {
            myLock = await _lockService.AcquireLockAsync(
                $"{channelWhatsappPhoneNumber}_{whatsappId}",
                TimeSpan.FromSeconds(10));
            if (myLock == null)
            {
                await Task.Delay(TimeSpan.FromSeconds(10));
            }
            else
            {
                break;
            }
        }

        var whatsappSender = await _appDbContext.SenderWhatsappSenders.Where(
                x => x.whatsAppId == whatsappId && x.CompanyId == companyId &&
                     x.InstanceId == twilioConfig.TwilioAccountId && x.InstaneSender == twilioConfig.WhatsAppSender)
            .FirstOrDefaultAsync();

        if (whatsappSender == null)
        {
            whatsappSender = new WhatsAppSender
            {
                whatsAppId = whatsappId,
                CompanyId = companyId,
                phone_number = whatsappId.Replace("whatsapp:+", string.Empty),
                name = whatsappUserDisplayName,
                InstanceId = twilioConfig.TwilioAccountId,
                InstaneSender = channelWhatsappPhoneNumber
            };
        }
        else
        {
            whatsappSender.phone_number = whatsappId.Replace("whatsapp:+", string.Empty);
            whatsappSender.name = whatsappId.Replace("whatsapp:", string.Empty);

            // whatsappSender.InstanceId = incomingMessage.AccountSid;
            // whatsappSender.InstaneSender = incomingMessage.To;
        }

        whatsappSender.name = whatsappUserDisplayName;

        return (whatsappSender, myLock);
    }

    private async Task<(SMSSender SmsSender, ILockService.Lock MyLock)>
        GetConversationSmsSenderAndLockAsync(
            string companyId,
            string twilioAccountId,
            string smsId)
    {
        ILockService.Lock myLock = null;

        try
        {
            while (true)
            {
                var lockId = $"{twilioAccountId}_{smsId}";

                myLock = await _lockService.AcquireLockAsync(lockId, TimeSpan.FromSeconds(10));

                if (myLock == null)
                {
                    await Task.Delay(TimeSpan.FromSeconds(2));
                }
                else
                {
                    break;
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "[{MethodName}] Unable to lock GetConversationSmsSenderAndLockAsync for {TwilioAccountId} {SmsId}: {ExceptionMessage}",
                nameof(GetConversationInstagramSenderAndLockAsync),
                twilioAccountId,
                smsId,
                ex.Message);
        }

        // Since Twilio have different type of channel address -> Reference: https://www.twilio.com/docs/messaging/channels#understand-channel-addresses
        // SMS/MMS -> +**********
        // RCS -> brand_name_n9c2bvqq_agent
        // WhatsApp -> whatsapp:+**********
        // Facebook Messenger -> XYZXYZXYZ (messenger_page_id or messenger_user_id)

        // Remove the '+' prefix and normalize the phone number
        var phoneNumber = smsId.Replace("+", string.Empty);

        // Only normalise E.164 format phone number
        if (PhoneNumberHelper.IsE164FormatPhoneNumber(smsId))
        {
            var originalPhoneNumber = phoneNumber;
            var normalizedPhoneNumber = PhoneNumberHelper.NormalizePhoneNumber(originalPhoneNumber);
            if (await _twilioService.ArePhoneNumbersEquivalentInTwilioAsync(originalPhoneNumber, normalizedPhoneNumber, companyId))
            {
                smsId = PhoneNumberHelper.ToE164Format(normalizedPhoneNumber);
                phoneNumber = normalizedPhoneNumber;
            }
        }

        var smsSender = await GetOrCreateSmsSender(companyId, twilioAccountId, smsId, phoneNumber);
        return (smsSender, myLock);
    }

    private async Task<SMSSender> GetOrCreateSmsSender(string companyId, string twilioAccountId, string smsId, string phoneNumber)
    {
        // Try to find existing SMSSender
        var smsSender = await _appDbContext.SenderSMSSenders
            .FirstOrDefaultAsync(x =>
                x.SMSId == smsId &&
                x.CompanyId == companyId);

        // If not found, create new SMSSender
        if (smsSender == null)
        {
            smsSender = new SMSSender
            {
                SMSId = smsId,
                CompanyId = companyId,
                phone_number = phoneNumber,
                name = phoneNumber,
                InstanceId = twilioAccountId
            };
        }
        else // Update existing SMSSender
        {
            smsSender.phone_number = phoneNumber;
            smsSender.name = phoneNumber;
            smsSender.InstanceId = twilioAccountId;
        }

        return smsSender;
    }

    private async Task<(FacebookSender FacebookSender, ILockService.Lock MyLock)>
        GetConversationFacebookSenderAndLockAsync(
            string companyId,
            string facebookPageId,
            string facebookId,
            string facebookUserName)
    {
        ILockService.Lock myLock = null;

        var facebookSender = await _appDbContext.SenderFacebookSenders.FirstOrDefaultAsync(
            x => x.CompanyId == companyId
                 && x.pageId == facebookPageId
                 && x.FacebookId == facebookId
                 && x.first_name != null);

        if (facebookSender != null)
        {
            return (facebookSender, myLock);
        }

        try
        {
            while (true)
            {
                var lockId = $"{facebookPageId}_{facebookId}";

                myLock = await _lockService.AcquireLockAsync(lockId, TimeSpan.FromSeconds(10));

                if (myLock == null)
                {
                    await Task.Delay(TimeSpan.FromSeconds(2));
                }
                else
                {
                    break;
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "[{MethodName}] Unable to lock GetConversationFacebookSenderAndLockAsync for {FacebookPageId} {FacebookId}: {ExceptionMessage}",
                nameof(GetConversationFacebookSenderAndLockAsync),
                facebookPageId,
                facebookId,
                ex.Message);
        }

        facebookSender =  await _appDbContext.SenderFacebookSenders.FirstOrDefaultAsync(
            x => x.CompanyId == companyId &&
                 x.FacebookId == facebookId &&
                 x.pageId == facebookPageId) ?? new FacebookSender()
        {
            CompanyId = companyId,
            FacebookId = facebookId,
            pageId = facebookPageId,
            first_name = facebookUserName,
            name = facebookUserName
        };

        if (facebookSender.Id == 0)
        {
            // Create new sender
            await _appDbContext.SenderFacebookSenders.AddAsync(facebookSender);
            await _appDbContext.SaveChangesAsync();
        }

        return (facebookSender, myLock);
    }

    private async Task<(InstagramSender InstagramSender, ILockService.Lock MyLock)>
        GetConversationInstagramSenderAndLockAsync(
            string companyId,
            string instagramPageId,
            string instagramId,
            string instagramUserName)
    {
        ILockService.Lock myLock = null;

        var instagramSender = await _appDbContext.SenderInstagramSenders.Where(
                x => x.CompanyId == companyId && x.InstagramPageId == instagramPageId && x.InstagramId == instagramId && x.Name != null)
            .FirstOrDefaultAsync();

        if (instagramSender != null)
        {
            return (instagramSender, myLock);
        }

        try
        {
            while (true)
            {
                var lockId = $"{instagramPageId}_{instagramId}";

                myLock = await _lockService.AcquireLockAsync(lockId, TimeSpan.FromSeconds(10));

                if (myLock == null)
                {
                    await Task.Delay(TimeSpan.FromSeconds(2));
                }
                else
                {
                    break;
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "[{MethodName}] Unable to lock GetConversationInstagramSenderAndLockAsync for {InstagramPageId} {InstagramId}: {ExceptionMessage}",
                nameof(GetConversationInstagramSenderAndLockAsync),
                instagramPageId,
                instagramId,
                ex.Message);
        }

        instagramSender = await _appDbContext.SenderInstagramSenders.FirstOrDefaultAsync(
            x => x.CompanyId == companyId &&
                 x.InstagramId == instagramId &&
                 x.InstagramPageId == instagramPageId) ?? new InstagramSender()
        {
            CompanyId = companyId,
            InstagramPageId = instagramPageId,
            InstagramId = instagramId,
            Username = instagramUserName
        };

        return (instagramSender, myLock);
    }

    private async Task<(WeChatSender WeChatSender, ILockService.Lock MyLock)> GetConversationWechatSenderAndLockAsync(
        string companyId,
        string wechatAppId,
        string wechatOpenId)
    {
        ILockService.Lock myLock = null;

        try
        {
            while (true)
            {
                var lockId = $"{wechatAppId}_{wechatOpenId}";

                myLock = await _lockService.AcquireLockAsync(lockId, TimeSpan.FromSeconds(10));

                if (myLock == null)
                {
                    await Task.Delay(TimeSpan.FromSeconds(2));
                }
                else
                {
                    break;
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "[{MethodName}] Unable to lock GetConversationWechatSenderAndLockAsync for {WechatAppId} {WeChatOpenId}: {ExceptionMessage}",
                nameof(GetConversationInstagramSenderAndLockAsync),
                wechatAppId,
                wechatOpenId,
                ex.Message);
        }

        var existingSender =
            await _appDbContext.SenderWeChatSenders.FirstOrDefaultAsync(
                x => x.openid == wechatOpenId && x.CompanyId == companyId && x.ChannelIdentityId == wechatAppId);

        var wechatConfig = await _appDbContext.CompanyCompanies.Where(x => x.Id == companyId)
            .Include(x => x.WeChatConfig).Select(x => x.WeChatConfig).FirstOrDefaultAsync();

        var newSenderInfo = await GetWeChatSenderAsync(wechatConfig, wechatOpenId);

        if (existingSender == null)
        {
            try
            {
                if (string.IsNullOrEmpty(wechatConfig.AccessToken) ||
                    DateTime.UtcNow > wechatConfig.TokenExpireAt)
                {
                    wechatConfig = await RefreshWechatConfigAccessTokenAsync(wechatConfig);
                }

                var httpClient = _httpClientFactory.CreateClient(HttpClientHandlerName.Default);

                var userProfileResponse = await httpClient.GetStringAsync(
                    $"https://api.weixin.qq.com/cgi-bin/user/info?access_token={wechatConfig.AccessToken}&openid={wechatOpenId}&lang=zh_CN");

                var userProfileData = JsonConvert.DeserializeObject<WeChatSender>(userProfileResponse);
                if (string.IsNullOrEmpty(userProfileData.openid))
                {
                    userProfileData.openid = wechatOpenId;
                }

                if (!string.IsNullOrEmpty(newSenderInfo.headimgurl))
                {
                    var newUploadedFile = await _userProfileService.UploadSocialProfilePicture(
                        companyId,
                        ChannelTypes.Wechat,
                        newSenderInfo.openid,
                        newSenderInfo.headimgurl);

                    if (newUploadedFile == null)
                    {
                        _logger.LogWarning(
                            "WeChat pic upload fail for openid {WeChatOpenId}",
                            newSenderInfo.openid);
                    }
                    else
                    {
                        _appDbContext.UserProfilePictureFiles.Add(newUploadedFile);

                        var domainName = _configuration.GetValue<string>("Values:DomainName");

                        newSenderInfo.headimgurl =
                            $"{domainName}/userprofile/picture/{newUploadedFile.ProfilePictureId}";
                    }
                }

                existingSender = newSenderInfo;
                if (string.IsNullOrEmpty(existingSender.nickname))
                {
                    // var personGenerator = new PersonNameGenerator();
                    // var name = personGenerator.GenerateRandomFirstName();
                    var name = await GetWeChatNameAsync(companyId);
                    existingSender.nickname = name;
                }

                existingSender.CompanyId = companyId;
                existingSender.ChannelIdentityId = wechatAppId;

                await _appDbContext.SenderWeChatSenders.AddAsync(existingSender);
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Company {CompanyId} unable to save WeChat profile picture record for openid {WeChatOpenId}",
                    companyId,
                    wechatOpenId);
            }
        }
        else
        {
            var domainName = _configuration.GetValue<String>("Values:DomainName");
            if (!string.IsNullOrEmpty(newSenderInfo.nickname))
            {
                existingSender.nickname = newSenderInfo.nickname;
            }

            if (string.IsNullOrEmpty(existingSender.nickname))
            {
                // var personGenerator = new PersonNameGenerator();
                // var name = personGenerator.GenerateRandomFirstName();
                var name = await GetWeChatNameAsync(companyId);
                existingSender.nickname = name;
            }

            if (!string.IsNullOrEmpty(newSenderInfo.headimgurl))
            {
                existingSender.headimgurl = newSenderInfo.headimgurl;
            }

            if (!existingSender.headimgurl?.Contains(domainName) ?? false)
            {
                try
                {
                    var newuUploadedFile = await _userProfileService.UploadSocialProfilePicture(
                        companyId,
                        ChannelTypes.Wechat,
                        newSenderInfo.openid,
                        newSenderInfo.headimgurl);

                    if (newuUploadedFile == null)
                    {
                        _logger.LogWarning(
                            "WeChat pic upload fail for existing user of openid {WeChatOpenId}",
                            newSenderInfo.openid);
                    }
                    else
                    {
                        _appDbContext.UserProfilePictureFiles.Add(newuUploadedFile);
                        existingSender.headimgurl =
                            $"{domainName}/userprofile/picture/{newuUploadedFile.ProfilePictureId}";
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(
                        ex,
                        "Company {CompanyId} unable to save WeChat profile picture record for existing user of openid {WeChatOpenId}",
                        companyId,
                        newSenderInfo.openid);
                }
            }
        }

        return (existingSender, myLock);
    }

    private async Task<WeChatSender> GetWeChatSenderAsync(WeChatConfig weChatConfig, string wechatOpenId)
    {
        if (string.IsNullOrEmpty(weChatConfig.AccessToken) ||
            DateTime.UtcNow > weChatConfig.TokenExpireAt)
        {
            weChatConfig = await RefreshWechatConfigAccessTokenAsync(weChatConfig);
        }

        var httpClient = _httpClientFactory.CreateClient(HttpClientHandlerName.Default);

        var userProfileResponse = await httpClient.GetStringAsync(
            $"https://api.weixin.qq.com/cgi-bin/user/info?access_token={weChatConfig.AccessToken}&openid={wechatOpenId}&lang=zh_CN");

        var userProfileData = JsonConvert.DeserializeObject<WeChatSender>(userProfileResponse);
        if (string.IsNullOrEmpty(userProfileData.openid))
        {
            userProfileData.openid = wechatOpenId;
        }

        return userProfileData;
    }

    private async Task<WeChatConfig> RefreshWechatConfigAccessTokenAsync(WeChatConfig weChatConfig)
    {
        var httpClient = _httpClientFactory.CreateClient(HttpClientHandlerName.Default);

        var getAccessTokenResponse = await httpClient.GetStringAsync(
            $"https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid={weChatConfig.AppId}&secret={weChatConfig.AppSecret}");

        var accessToken = JsonConvert.DeserializeObject<AccessTokenResponse>(getAccessTokenResponse);
        weChatConfig.AccessToken = accessToken.access_token;
        weChatConfig.TokenExpireAt = DateTime.UtcNow.AddSeconds(accessToken.expires_in - 30);
        await _appDbContext.SaveChangesAsync();
        return weChatConfig;
    }

    private async Task<string> GetWeChatNameAsync(string companyId)
    {
        var timeZoneId = await _appDbContext.CompanyCompanies.Where(x => x.Id == companyId)
            .Select(x => x.TimeZoneInfoId).FirstOrDefaultAsync();
        var date = DateTime.UtcNow.ToDisplayTime(timeZoneId);

        var name = $"WeChat Visitor at {date.ToString("HH:mm")} on {date.ToString("MMM dd")}";

        return name;
    }

    private async Task<(TelegramSender TelegramSender, ILockService.Lock MyLock)>
        GetConversationTelegramSenderAndLockAsync(
            string companyId,
            string telegramBotId,
            string telegramChatId)
    {
        ILockService.Lock myLock = null;

        try
        {
            while (true)
            {
                var lockId = $"{telegramBotId}_{telegramChatId}";

                myLock = await _lockService.AcquireLockAsync(lockId, TimeSpan.FromSeconds(10));

                if (myLock == null)
                {
                    await Task.Delay(TimeSpan.FromSeconds(2));
                }
                else
                {
                    break;
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "[{MethodName}] Unable to lock GetConversationTelegramSenderAndLockAsync for {TelegramBotId} {TelegramChatId}: {ExceptionMessage}",
                nameof(GetConversationInstagramSenderAndLockAsync),
                telegramBotId,
                telegramChatId,
                ex.Message);
        }

        if (!long.TryParse(telegramChatId, out var chatId))
        {
            throw new Exception("telegram chat id is not long type");
        }

        if (!long.TryParse(telegramBotId, out var botId))
        {
            throw new Exception("telegram chat id is not long type");
        }

        var config =
            await _appDbContext.ConfigTelegramConfigs.FirstOrDefaultAsync(
                x => x.CompanyId == companyId && x.TelegramBotId == botId);

        var sender = await _appDbContext.SenderTelegramSenders.FirstOrDefaultAsync(
            x => x.CompanyId == companyId
                 && x.TelegramBotId == botId
                 && x.TelegramChatId == chatId);

        if (sender != null)
        {
            return (sender, myLock);
        }

        var client = new TelegramBotClient(config.TelegramBotToken);

        // Get User info before insert
        var telegramChatInfo = await client.GetChatAsync(chatId);
        var newTelegramSender = new TelegramSender()
        {
            CompanyId = companyId,
            TelegramChatId = telegramChatInfo.Id,
            FirstName = telegramChatInfo.FirstName,
            LastName = telegramChatInfo.LastName,

            // PicturePhotoId = telegramChatInfo.Photo?.SmallFileId,
            TelegramBotId = botId,
            Type = Enum.GetName(telegramChatInfo.Type),
        };

        if (telegramChatInfo.Photo != null)
        {
            try
            {
                var profilePhotoMemoryStream = new MemoryStream();
                var photoId = telegramChatInfo.Photo.SmallFileId;

                var photoInfo = await client.GetInfoAndDownloadFileAsync(photoId, profilePhotoMemoryStream);
                profilePhotoMemoryStream.Position = 0;

                var extension = Path.GetExtension(photoInfo.FilePath);
                var filePath =
                    $"UserProfile/telegram/{DateTime.UtcNow.ToString("o")}/{chatId}{Path.GetExtension(photoInfo.FilePath)}";

                await _azureBlobStorageService.UploadFileAsBlob(
                    profilePhotoMemoryStream,
                    filePath,
                    config.CompanyId,
                    MimeTypeMap.GetMimeType(extension));

                var url = _azureBlobStorageService.GetAzureBlobSasUri(
                    filePath,
                    config.CompanyId,
                    87600); // blob sas link valid for 10 years

                newTelegramSender.PicturePhotoId = photoId;
                newTelegramSender.PicturePhotoUrl = url;
            }
            catch (Exception e)
            {
                _logger.LogError(
                    e,
                    "Telegram HandleWebhook Get Profile Pic Error: {ExceptionMessage}",
                    e.Message);
            }
        }

        await _appDbContext.SenderTelegramSenders.AddAsync(newTelegramSender);
        await _appDbContext.SaveChangesAsync();
        sender = newTelegramSender;


        return (sender, myLock);
    }

    private async Task<(LineSender LineSender, ILockService.Lock MyLock)>
        GetConversationLineSenderAndLockAsync(
            string companyId,
            string lineChannelId,
            string lineUserId)
    {
        ILockService.Lock myLock = null;

        try
        {
            while (true)
            {
                var lockId = $"{lineChannelId}_{lineUserId}";

                myLock = await _lockService.AcquireLockAsync(lockId, TimeSpan.FromSeconds(10));

                if (myLock == null)
                {
                    await Task.Delay(TimeSpan.FromSeconds(2));
                }
                else
                {
                    break;
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "[{MethodName}] Unable to lock GetConversationLineSenderAndLockAsync for {LineChannelId} {LineUserId}: {ExceptionMessage}",
                nameof(GetConversationLineSenderAndLockAsync),
                lineChannelId,
                lineUserId,
                ex.Message);
        }

        var lineConfig =
            await _appDbContext.ConfigLineConfigs.FirstOrDefaultAsync(
                x => x.CompanyId == companyId && x.ChannelID == lineChannelId);

        var lineSender = await _appDbContext.SenderLineSender
            .Where(x => x.userId == lineUserId && x.CompanyId == companyId)
            .FirstOrDefaultAsync();

        try
        {
            if (lineSender == null)
            {
                var bot = new isRock.LineBot.Bot(lineConfig.ChannelAccessToken);
                var botUserProfile = bot.GetUserInfo(lineUserId);
                lineSender = _mapper.Map<LineSender>(botUserProfile);
                lineSender.CompanyId = lineConfig.CompanyId;
                lineSender.ChannelIdentityId = lineConfig.ChannelIdentityId;
                _appDbContext.SenderLineSender.Add(lineSender);
                await _appDbContext.SaveChangesAsync();
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "GetUserInfo error for LINE user id {LineUserId}. {ExceptionMessage}",
                lineUserId,
                ex.Message);

            lineSender = new LineSender
            {
                CompanyId = lineConfig.CompanyId,
                userId = lineUserId,
                displayName = "No Name"
            };
            _appDbContext.SenderLineSender.Add(lineSender);
            await _appDbContext.SaveChangesAsync();
        }

        try
        {
            if (!string.IsNullOrEmpty(lineSender.pictureUrl) &&
                lineSender.pictureUrl.Contains("line-scdn.net"))
            {
                var newUploadedFile = await _userProfileService.UploadSocialProfilePicture(
                    companyId,
                    ChannelTypes.Line,
                    lineSender.pictureUrl,
                    lineSender.pictureUrl);

                if (newUploadedFile == null)
                {
                    _logger.LogWarning(
                        "LINE user profile pic upload fail for LINE sender id {LineUserId}",
                        lineSender.userId);
                }
                else
                {
                    _appDbContext.UserProfilePictureFiles.Add(newUploadedFile);

                    var domainName = _configuration.GetValue<string>("Values:DomainName");
                    lineSender.pictureUrl =
                        $"{domainName}/userprofile/picture/{newUploadedFile.ProfilePictureId}";
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(
                "Error saving LINE user profile pic upload for LINE sender id {LineUserId} {ExceptionMessage}",
                lineSender.userId,
                ex.Message);
        }

        return (lineSender, myLock);
    }

    private async Task<(ViberSender ViberSender, ILockService.Lock MyLock)>
        GetConversationViberSenderAndLockAsync(
            string companyId,
            string viberBotId,
            string viberUserId,
            string webhookString)
    {
        ILockService.Lock myLock = null;

        try
        {
            while (true)
            {
                var lockId = $"{viberBotId}_{viberUserId}";

                myLock = await _lockService.AcquireLockAsync(lockId, TimeSpan.FromSeconds(10));

                if (myLock == null)
                {
                    await Task.Delay(TimeSpan.FromSeconds(2));
                }
                else
                {
                    break;
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "[{MethodName}] Unable to lock GetConversationViberSenderAndLockAsync for {ViberBotId} {ViberUserId}: {ExceptionMessage}",
                nameof(GetConversationInstagramSenderAndLockAsync),
                viberBotId,
                viberUserId,
                ex.Message);
        }

        var webhookCallback = MapCallbackToSpecificType(JsonConvert.DeserializeObject<ViberWebhookCallback>(webhookString));

        var viberConfig =
            await _appDbContext.ConfigViberConfigs.FirstOrDefaultAsync(
                x => x.CompanyId == companyId && x.ViberBotId == viberBotId);
        var httpClient = _httpClientFactory.CreateClient(HttpClientHandlerName.Default);
        var viberBotClient = new ViberBotClient(httpClient, viberConfig.ViberBotToken);

        var viberSender = await _appDbContext.SenderViberSenders.FirstOrDefaultAsync(
            x => x.CompanyId == companyId
                 && x.ViberUserId == viberUserId
                 && x.ViberBotId == viberBotId);

        if (viberSender == null)
        {
            // Get User info before insert
            var viberUserResponse = await viberBotClient.GetUserDetailsAsync(viberUserId);

            var viberUser = webhookCallback switch
            {
                ViberReceiveMessageCallback callback => viberUserResponse.Status == ViberApiResponseStatus.Ok
                    ? viberUserResponse.User
                    : callback.Sender,
                ViberSubscribedCallback callback => viberUserResponse.Status == ViberApiResponseStatus.Ok
                    ? viberUserResponse.User
                    : callback.User,
                _ => throw new ArgumentOutOfRangeException(
                    $"{webhookCallback.GetType()} is not supported for getting viber sender from callback for conversation")
            };

            var newViberSender = new ViberSender
            {
                CompanyId = companyId,
                ViberUserId = viberUser.Id,
                DisplayName = viberUser.Name,
                PictureUrl = viberUser.Avatar,
                Language = viberUser.Language,
                Country = viberUser.Country,
                ViberVersion = viberUser.ViberVersion,
                ApiVersion = viberUser.ApiVersion,
                MobileCountryCode = viberUser.MobileCountryCode,
                MobileNetworkCode = viberUser.MobileNetworkCode,
                ViberBotId = viberBotId,
                IsSubscribed = true,
            };

            await _appDbContext.SenderViberSenders.AddAsync(newViberSender);
            await _appDbContext.SaveChangesAsync();
            viberSender = newViberSender;
        }
        else
        {
            switch (webhookCallback)
            {
                case ViberReceiveMessageCallback:
                case ViberSubscribedCallback:
                    if (!viberSender.IsSubscribed)
                    {
                        viberSender.IsSubscribed = true;
                        await _appDbContext.SaveChangesAsync();
                    }

                    break;

                case ViberUnsubscribedCallback:
                    if (viberSender.IsSubscribed)
                    {
                        viberSender.IsSubscribed = false;
                        await _appDbContext.SaveChangesAsync();
                    }

                    break;
            }
        }

        return (viberSender, myLock);
    }

    private async Task<(EmailSender EmailSender, ILockService.Lock MyLock)>
        GetConversationEmailSenderAndLockAsync(
            string companyId,
            string channelEmailAddress,
            string contactEmailAddress,
            string contactEmailName)
    {
        ILockService.Lock myLock = null;

        try
        {
            while (true)
            {
                var lockId = $"{channelEmailAddress}_{contactEmailAddress}";

                myLock = await _lockService.AcquireLockAsync(lockId, TimeSpan.FromSeconds(10));

                if (myLock == null)
                {
                    await Task.Delay(TimeSpan.FromSeconds(2));
                }
                else
                {
                    break;
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "[{MethodName}] Unable to lock GetConversationEmailSenderAndLockAsync for {ChannelEmailAddress} {ContactEmailAddress}: {ExceptionMessage}",
                nameof(GetConversationEmailSenderAndLockAsync),
                channelEmailAddress,
                contactEmailAddress,
                ex.Message);
        }

        var emailSender = await _appDbContext.SenderEmailSenders
            .FirstOrDefaultAsync(
                x =>
                    x.Email == contactEmailAddress
                    && x.CompanyId == companyId) ?? new EmailSender()
        {
            Email = contactEmailAddress, Name = contactEmailName, CompanyId = companyId
        };

        return (emailSender, myLock);
    }

    private IViberCallback MapCallbackToSpecificType(ViberWebhookCallback input)
    {
        return input.Event switch
        {
            ViberWebhookCallbackEventType.Message => _mapper.Map<ViberReceiveMessageCallback>(input),
            ViberWebhookCallbackEventType.Delivered => _mapper.Map<ViberMessageDeliveredCallback>(input),
            ViberWebhookCallbackEventType.Seen => _mapper.Map<ViberMessageSeenCallback>(input),
            ViberWebhookCallbackEventType.Failed => _mapper.Map<ViberSendMessageFailedCallback>(input),
            ViberWebhookCallbackEventType.Subscribed => _mapper.Map<ViberSubscribedCallback>(input),
            ViberWebhookCallbackEventType.Unsubscribed => _mapper.Map<ViberUnsubscribedCallback>(input),
            ViberWebhookCallbackEventType.ConversationStarted => _mapper.Map<ViberConversationStartedCallback>(
                input),
            ViberWebhookCallbackEventType.Webhook => _mapper.Map<ViberWebhookSetupCallback>(input),
            _ => throw new ArgumentOutOfRangeException()
        };
    }

    private WhatsappCloudApiSender ConstructWhatsappCloudApiSender(
        string companyId,
        string conversationId,
        string userProfileId,
        string whatsappChannelPhoneNumber,
        string whatsappId,
        string whatsappUserDisplayName = null)
    {
        var whatsappCloudApiSender = new WhatsappCloudApiSender()
        {
            CompanyId = companyId,
            ConversationId = conversationId,
            UserProfileId = userProfileId,
            WhatsappChannelPhoneNumber = whatsappChannelPhoneNumber,
            WhatsappId = whatsappId,
            WhatsappUserDisplayName = whatsappUserDisplayName
        };

        return whatsappCloudApiSender;
    }

    private async Task<WhatsApp360DialogSender> ConstructWhatsapp360DialogSenderAsync(
        string companyId,
        long channelId,
        string channelWhatsppPhoneNumber,
        string whatsappId)
    {
        var whatsapp360DialogSender = new WhatsApp360DialogSender()
        {
            CompanyId = companyId,
            ChannelId = channelId,
            ChannelWhatsAppPhoneNumber = channelWhatsppPhoneNumber,
            WhatsAppId = whatsappId,
            PhoneNumber = $"+{whatsappId}"
        };

        return whatsapp360DialogSender;
    }

    private async Task<Conversation> HandleConversationWhatsappSenderAsync(Conversation conversation, string companyId, string whatsappReceiverId, string channelIdentityId)
    {
        if (conversation is { WhatsappUser: null })
        {
            var twilioConfig = await _appDbContext.ConfigWhatsAppConfigs
                .FirstOrDefaultAsync(x => x.CompanyId == companyId);

            if (twilioConfig == null)
            {
                throw new NullReferenceException("Twilio Config not found");
            }

            var whatsappSender = await _appDbContext.SenderWhatsappSenders
                .FirstOrDefaultAsync(
                    x =>
                        x.whatsAppId == $"whatsapp:+{whatsappReceiverId
                            .Replace("@c.us", string.Empty)
                            .Replace("whatsapp:+", string.Empty)}"
                        && x.CompanyId == companyId
                        && x.InstanceId == twilioConfig.TwilioAccountId);

            if (whatsappSender != null)
            {
                await _appDbContext.Conversations
                    .Where(x => x.CompanyId == companyId && x.Id == conversation.Id).ExecuteUpdateAsync(
                        x => x.SetProperty(y => y.WhatsappUserId, whatsappSender.Id));
            }
            else
            {
                if (await _appDbContext.SenderWhatsappSenders.AnyAsync(
                        x => x.whatsAppId == $"whatsapp:+{whatsappReceiverId
                            .Replace("@c.us", string.Empty)
                            .Replace("whatsapp:+", string.Empty)}"
                             && x.CompanyId == companyId))
                {
                    whatsappSender = (await _userProfileService.SwitchUserProfileMessagingChannel(
                        companyId,
                        conversation.UserProfileId,
                        ChannelTypes.WhatsappTwilio,
                        channelIdentityId)).WhatsAppAccount;
                }
                else
                {
                    whatsappSender = ConstructWhatsappSender(
                        companyId,
                        twilioConfig.TwilioAccountId,
                        twilioConfig.WhatsAppSender,
                        $"whatsapp:+{whatsappReceiverId}");
                }
            }

            conversation.WhatsappUser = whatsappSender;
        }
        else if (!string.IsNullOrEmpty(channelIdentityId) && conversation.WhatsappUser.ChannelIdentityId != channelIdentityId)
        {
            await _userProfileService.SwitchUserProfileMessagingChannel(
                companyId,
                conversation.UserProfileId,
                ChannelTypes.WhatsappTwilio,
                channelIdentityId);
        }

        // if (conversation.WhatsappUser == null)
        //    conversation.WhatsappUser = new WhatsAppSender { whatsAppId = conversationMessageViewModel.WhatsappReceiverId, CompanyId = company.Id };
        if (conversation.WhatsappUser.whatsAppId.Contains("@")
            && string.IsNullOrEmpty(conversation.WhatsappUser.phone_number))
        {
            conversation.WhatsappUser.phone_number =
                PhoneNumberHelper.GetPhoneNumber(conversation.WhatsappUser.whatsAppId);

            conversation.WhatsappUser.name = conversation.WhatsappUser.phone_number;
        }

        return conversation;
    }

    private WhatsAppSender ConstructWhatsappSender(string companyId, string instanceId, string instanceSender, string whatsappId)
    {
        var whatsappSender = new WhatsAppSender()
        {
            whatsAppId = whatsappId, CompanyId = companyId, InstanceId = instanceId, InstaneSender = instanceSender
        };

        return whatsappSender;
    }

    private FacebookSender ConstructFacebookSender(
        string companyId,
        string facebookPageId,
        string facebookId)
    {
        var facebookSender = new FacebookSender()
        {
            CompanyId = companyId, FacebookId = facebookId, pageId = facebookPageId
        };

        return facebookSender;
    }

    private async Task<InstagramSender> ConstructInstagramSenderAsync(
        string companyId,
        string instagramPageId,
        string instagramId)
    {
        var instagramSender = new InstagramSender()
        {
            CompanyId = companyId,
            InstagramPageId = instagramPageId,
            InstagramId = instagramId,
            PageId = await _appDbContext.ConfigInstagramConfigs
                .Where(x => x.CompanyId == companyId && x.InstagramPageId == instagramPageId).Select(x => x.PageId)
                .FirstOrDefaultAsync()
        };

        return instagramSender;
    }

    private SMSSender ConstructSmsSender(string companyId, string smsChannelIdentityId, string smsId)
    {
        var smsSender = new SMSSender
        {
            InstanceId = smsChannelIdentityId,
            SMSId = smsId,
            phone_number = smsId.Replace("+", string.Empty),
            name = smsId.Replace("+", string.Empty),
            CompanyId = companyId
        };

        return smsSender;
    }

    private WeChatSender ConstructWeChatSender(string companyId, string wechatChannelIdentityId, string openId)
    {
        var wechatSender = new WeChatSender()
        {
            openid = openId, ChannelIdentityId = wechatChannelIdentityId, CompanyId = companyId
        };

        return wechatSender;
    }

    private ViberSender ConstructViberSender(string companyId, string viberChannelIdentityId, string viberUserId)
    {
        var viberSender = new ViberSender()
        {
            CompanyId = companyId, ViberUserId = viberUserId, ViberBotId = viberChannelIdentityId
        };

        return viberSender;
    }

    private EmailSender ConstructEmailSender(string companyId, string emailChannelIdentityId, string userEmailAddress)
    {
        var emailSender = new EmailSender()
        {
            CompanyId = companyId, Email = userEmailAddress, ChannelIdentityId = emailChannelIdentityId
        };

        return emailSender;
    }

    private LineSender ConstructLineSender(string companyId, string lineChannelIdentityId,  string userId)
    {
        var lineUser = new LineSender
        {
            userId = userId,
            CompanyId = companyId,
            ChannelIdentityId = lineChannelIdentityId
        };

        return lineUser;
    }

    private TelegramSender ConstructTelegramSender(
        string companyId,
        long telegramChannelIdentityId,
        long telegramChatId)
    {
        var telegramSender = new TelegramSender()
        {
            CompanyId = companyId, TelegramBotId = telegramChannelIdentityId, TelegramChatId = telegramChatId
        };

        return telegramSender;
    }

    // For new conversation initiated by message delivery type of automated message (e.g. send via API) / broadcast,
    // set conversations status as closed
    private static string GetNewConversationStatus(ConversationMessage conversationMessage)
    {
        return conversationMessage.DeliveryType.Equals(DeliveryType.AutomatedMessage) ||
               conversationMessage.DeliveryType.Equals(DeliveryType.Broadcast) ||
               conversationMessage.DeliveryType.Equals(DeliveryType.FlowHubAction) ||
               conversationMessage.MessageType.Equals(MessageTypes.System) ||
               conversationMessage.IsFromImport
            ? "closed"
            : "open";
    }

    private async Task TryRecoverSoftDeletedUserProfileAsync(Conversation conversation)
    {
        try
        {
            if (conversation.WebClient != null)
            {
                return;
            }

            await _userProfileSafeDeleteService.RecoverSoftDeletedUserProfilesAsync(
                conversation.CompanyId,
                new HashSet<string>() { conversation.UserProfileId },
                new UserProfileRecoveryTriggerContext(
                    UpdateUserProfileTriggerSource.IncomingMessage,
                    null));
        }
        catch (Exception e)
        {
            _logger.LogError(
                e,
                "Errors when recover contact by {ServiceName} {CompanyId} {ConversationId} {UserProfileId}",
                nameof(ConversationResolver),
                conversation.CompanyId,
                conversation.Id,
                conversation.UserProfile?.Id);
        }
    }

}