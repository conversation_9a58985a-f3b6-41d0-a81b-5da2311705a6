using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using Auth0.AuthenticationApi;
using Auth0.AuthenticationApi.Models;
using Auth0.ManagementApi.Models.Users;
using Auth0.ManagementApi.Paging;
using Hangfire;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using Sleekflow.Apis.TenantHub.Api;
using Sleekflow.Apis.TenantHub.Model;
using Travis_backend.AccountAuthenticationDomain.Models;
using Travis_backend.AccountAuthenticationDomain.Services;
using Travis_backend.AccountAuthenticationDomain.ViewModels;
using Travis_backend.Auth0.Configuration;
using Travis_backend.Auth0.Services;
using Travis_backend.CommonDomain.ViewModels;
using Travis_backend.ConversationServices;
using Travis_backend.Database;
using Travis_backend.Database.Filters;
using Travis_backend.Database.Services;
using Travis_backend.Enums;
using Travis_backend.FlowHubs;
using Travis_backend.Infrastructures.Options;
using Travis_backend.SubscriptionPlanDomain.Constants;
using Travis_backend.SubscriptionPlanDomain.Services;
using Travis_backend.TenantHubDomain.Exceptions;
using AccessTokenResponse = Auth0.AuthenticationApi.Models.AccessTokenResponse;
using Auth0User = Auth0.ManagementApi.Models.User;

namespace Travis_backend.Auth0.Controllers;

[Authorize]
[Route("auth0/account/[action]")]
public class Auth0AccountController : Controller
{
    // private readonly ApplicationDbContext _appDbContext;
    private readonly BaseDbContext _appDbContext;
    private readonly SleekflowUserManager _userManager;
    private readonly Auth0Config _auth0Config;
    private readonly ILogger _logger;
    private readonly ICoreService _coreService;
    private readonly IAuthenticationApiClient _authenticationApiClient;
    private readonly IWebHostEnvironment _environment;
    private readonly IFeaturesApi _featuresApi;
    private readonly IEnabledFeaturesApi _enabledFeaturesApi;
    private readonly IOptions<IdentityOptions> _identityOptions;
    private readonly IStaffHooks _staffHooks;

    private readonly IDefaultSubscriptionPlanIdGetter _defaultSubscriptionPlanIdGetter;

    public Auth0AccountController(
        UserManager<ApplicationUser> userManager,
        Auth0Config auth0Config,
        // ApplicationDbContext appDbContext,
        IDbContextService dbContextService,
        ILogger<Auth0AccountController> logger,
        ICoreService coreService,
        IAuthenticationApiClient authenticationApiClient,
        IWebHostEnvironment environment,
        IFeaturesApi featuresApi,
        IEnabledFeaturesApi enabledFeaturesApi,
        IOptions<IdentityOptions> identityOptions,
        IDefaultSubscriptionPlanIdGetter defaultSubscriptionPlanIdGetter,
        IStaffHooks staffHooks)
    {
        _userManager = (SleekflowUserManager) userManager;
        // _appDbContext = appDbContext;
        _appDbContext = dbContextService.GetDbContext();
        _logger = logger;
        _auth0Config = auth0Config;
        _coreService = coreService;
        _authenticationApiClient = authenticationApiClient;
        _environment = environment;
        _featuresApi = featuresApi;
        _enabledFeaturesApi = enabledFeaturesApi;
        _identityOptions = identityOptions;
        _defaultSubscriptionPlanIdGetter = defaultSubscriptionPlanIdGetter;
        _staffHooks = staffHooks;
    }

#if DEBUG
    public class Auth0UserLoginRequestViewModel
    {
        /// <summary>
        /// User Name or Email.
        /// </summary>
        /// <example>BillyChan.</example>
        [Required]
        [JsonProperty("username")]
        [Display(Name = "UserName")]
        [Description("User Name or Email")]
        public string? UserName { get; set; }

        /// <summary>
        /// User Password.
        /// </summary>
        /// <example>myPassword.</example>
        [Required]
        [JsonProperty("password")]
        [Display(Name = "Password")]
        public string? Password { get; set; }
    }

    public class Auth0UserLoginResponse
    {
        [JsonProperty("idToken")]
        public string IdToken { get; set; }

        [JsonProperty("accessToken")]
        public string AccessToken { get; set; }

        [JsonProperty("expiresIn")]
        public int ExpiresIn { get; set; }

        public Auth0UserLoginResponse(string idToken, string accessToken, int expiresIn)
        {
            IdToken = idToken;
            AccessToken = accessToken;
            ExpiresIn = expiresIn;
        }
    }

    [HttpPost]
    [AllowAnonymous]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(typeof(Auth0UserLoginResponse), StatusCodes.Status200OK)]
    public async Task<ActionResult<Auth0UserLoginResponse>> GetUserToken(
        [FromBody] Auth0UserLoginRequestViewModel loginViewModel)
    {
        if (!_environment.IsEnvironment("Development"))
        {
            return BadRequest();
        }

        try
        {
            var result = await _authenticationApiClient.GetTokenAsync(
                new ResourceOwnerTokenRequest()
                {
                    Scope = "openid",
                    Audience = _auth0Config.Audience,
                    ClientId = _auth0Config.ClientId,
                    ClientSecret = _auth0Config.ClientSecret,
                    Realm = _auth0Config.DatabaseConnectionName,
                    Username = loginViewModel.UserName,
                    Password = loginViewModel.Password
                });

            return Ok(
                new Auth0UserLoginResponse(
                    idToken: result.IdToken,
                    accessToken: result.AccessToken,
                    expiresIn: result.ExpiresIn));
        }
        catch (Exception err)
        {
            return Unauthorized(
                new ResponseViewModel()
                {
                    message = err.Message
                });
        }
    }
#endif

    public class Auth0ResetPasswordRequestViewModel
    {
        [Required]
        [EmailAddress]
        [Display(Name = "Email")]
        public string Email { get; set; }
    }

    /// <summary>
    /// This API will replace /account/password/reset.
    /// </summary>
    /// <param name="requestViewModel"></param>
    /// <returns></returns>
    [HttpPost]
    [AllowAnonymous]
    [ProducesResponseType(typeof(ResponseViewModel), StatusCodes.Status200OK)]
    public async Task<IActionResult> RequestPasswordReset(
        [FromBody]
        Auth0ResetPasswordRequestViewModel requestViewModel)
    {
        if (ModelState.IsValid)
        {
            try
            {
                var message = await _userManager.RequestPasswordReset(requestViewModel.Email);
                return Ok(
                    new ResponseViewModel()
                    {
                        message = message
                    });
            }
            catch (Exception err)
            {
                return BadRequest(
                    new ResponseViewModel()
                    {
                        message = err.Message
                    });
            }
        }
        else
        {
            return BadRequest(
                new ResponseViewModel()
                {
                    message = ModelState.Values.ToList()[0].Errors[0].ErrorMessage
                });
        }
    }

    /// <summary>
    /// Check if User is completed the registration.
    /// </summary>
    /// <returns>boolean.</returns>
    [HttpGet]
    [Obsolete("Should delete it after finish the Data center pickup")]
    [ProducesResponseType(typeof(bool), StatusCodes.Status200OK)]
    [ServiceFilter(typeof(ReadOnlyEndpointAttribute))]
    public async Task<IActionResult> IsCompanyRegistered()
    {
        try
        {
            var id = _userManager.GetUserId(User);
            var staff = await _appDbContext.UserRoleStaffs.FirstOrDefaultAsync(u => u.IdentityId == id);
            return Ok(staff != null);
        }
        catch (Exception err)
        {
            if (err is NotAllowedIpException)
            {
                throw;
            }

            return BadRequest(err);
        }
    }

    /// <summary>
    /// Company registration for Auth0 flow.
    /// </summary>
    public class RegisterAuth0CompanyViewModel
    {
        [Display(Name = "First Name")]
        public string? FirstName { get; set; }

        [Display(Name = "Last Name")]
        public string? LastName { get; set; }

        [Display(Name = "Phone Number")]
        public string PhoneNumber { get; set; }

        [Display(Name = "Company Industry")]
        public string Industry { get; set; }

        [Display(Name = "Online Shop System")]
        public string OnlineShopSystem { get; set; }

        [Display(Name = "Company Website")]
        public string CompanyWebsite { get; set; }

        [Required]
        public string CompanyName { get; set; }

        public string TimeZoneInfoId { get; set; }

        public string CompanySize { get; set; }

        public string SubscriptionPlanId { get; set; } = "sleekflow_free";

        public string lmref { get; set; }

        public string HeardFrom { get; set; }

        public string PromotionCode { get; set; }

        public string WebClientUUID { get; set; }

        public string Referral { get; set; }

        public CompanyType CompanyType { get; set; } = CompanyType.DirectClient;

        public List<string> CommunicationTools { get; set; }

        public bool? IsAgreeMarketingConsent { get; set; }
    }

    public class RegisterCompanyResponse
    {
        public string Id { get; set; }

        public string Email { get; set; }

        public string UserName { get; set; }

        public string FirstName { get; set; }

        public string LastName { get; set; }

        public string SignalRGroupName { get; set; }

        public bool IsShopifyAccount { get; set; }

        public List<string> AssociatedCompanyIds { get; set; }
    }

    /// <summary>
    /// Register the new company after auth0 user has create.
    /// </summary>
    /// <param name="registerAuth0CompanyViewModel"></param>
    /// <returns>AuthenticationResponse.</returns>
    // [AllowAnonymous]
    [HttpPost]
    [Obsolete("Should delete it after finish the Data center pickup")]
    [ProducesResponseType(typeof(RegisterCompanyResponse), StatusCodes.Status200OK)]
    public async Task<IActionResult> RegisterAccountCompany(
        [FromBody]
        RegisterAuth0CompanyViewModel registerAuth0CompanyViewModel)
    {
        if (ModelState.IsValid)
        {
            var dbUser = await _userManager.GetUserAsync(User);
            try
            {
                var isSocialLogin = _userManager.GetClaimAuth0ConnectionStrategy() != "auth0";

                dbUser.EmailConfirmed = isSocialLogin;

                // If Social Login, we will not update the name
                if (!isSocialLogin)
                {
                    dbUser.FirstName = registerAuth0CompanyViewModel.FirstName;
                    dbUser.LastName = registerAuth0CompanyViewModel.LastName;
                    dbUser.DisplayName =
                        $"{registerAuth0CompanyViewModel.FirstName} {registerAuth0CompanyViewModel.LastName}";
                }

                dbUser.PhoneNumber = registerAuth0CompanyViewModel.PhoneNumber;
                dbUser.IsAgreeMarketingConsent = registerAuth0CompanyViewModel.IsAgreeMarketingConsent;

                var updateResult = await _userManager.UpdateAsync(dbUser);
                if (updateResult.Succeeded)
                {
                    await _coreService.CreateCompany(
                        dbUser,
                        new RegisterCompanyViewModel
                        {
                            CompanyName = registerAuth0CompanyViewModel.CompanyName,
                            CompanySize = registerAuth0CompanyViewModel.CompanySize,
                            PromotionCode = registerAuth0CompanyViewModel.PromotionCode,
                            PhoneNumber = registerAuth0CompanyViewModel.PhoneNumber,
                            SubscriptionPlanId = registerAuth0CompanyViewModel.SubscriptionPlanId,
                            HeardFrom = registerAuth0CompanyViewModel.HeardFrom,
                            lmref = registerAuth0CompanyViewModel.lmref,
                            TimeZoneInfoId = registerAuth0CompanyViewModel.TimeZoneInfoId,
                            WebClientUUID = registerAuth0CompanyViewModel.WebClientUUID,
                            Referral = registerAuth0CompanyViewModel.Referral,
                            CompanyType = registerAuth0CompanyViewModel.CompanyType,
                            Industry = registerAuth0CompanyViewModel.Industry,
                            OnlineShopSystem = registerAuth0CompanyViewModel.OnlineShopSystem,
                            CommunicationTools = registerAuth0CompanyViewModel.CommunicationTools,
                            CompanyWebsite = registerAuth0CompanyViewModel.CompanyWebsite,
                        });
                }
                else
                {
                    foreach (var updateResultError in updateResult.Errors)
                    {
                        _logger.LogError(
                            "Unable to update the user, {Description} {Code}",
                            updateResultError.Description,
                            updateResultError.Code);
                    }

                    throw new Exception();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Create company error: {ExceptionMessage}",
                    ex.Message);

                return BadRequest(
                    new ResponseViewModel
                    {
                        message = ex.Message
                    });
            }

            var response = new RegisterCompanyResponse()
            {
                Id = dbUser.Id,
                Email = dbUser.Email,
                UserName = dbUser.UserName,
                FirstName = dbUser.FirstName,
                LastName = dbUser.LastName,
            };
            try
            {
                var staffId = await _coreService.GetCompanyStaff(dbUser);
                var freeBills = _appDbContext.CompanyBillRecords.Where(
                    x =>
                        x.CompanyId == staffId.CompanyId && x.SubscriptionPlanId == "sleekflow_free").ToList();
                if (freeBills.Count > 0)
                {
                    // change all free to freemium
                    foreach (var free in freeBills)
                    {
                        free.SubscriptionPlanId = _defaultSubscriptionPlanIdGetter.GetDefaultStartupPlanId();
                        free.Status = BillStatus.Active;
                    }

                    await _appDbContext.SaveChangesAsync();
                }

                response.SignalRGroupName = staffId.Company.SignalRGroupName;
                response.IsShopifyAccount = staffId.Company.IsShopifyAccount;
                response.AssociatedCompanyIds = await _appDbContext.UserRoleStaffs
                    .Where(x => x.IdentityId == dbUser.Id)
                    .Select(x => x.CompanyId)
                    .ToListAsync();
                _logger.LogInformation($"Staff Login: {response.Id}, {response.Email}, {response.SignalRGroupName}");
            }
            catch (Exception ex)
            {
                _logger.LogInformation($"User Login: {response.Id}, {response.Email}, error: {ex.ToString()}");
            }

            return Ok(response);
        }
        else
        {
            return BadRequest(ModelState);
        }
    }

    public class Auth0CompleteInvitationViewModel
    {
        [JsonProperty("sleekflow_user_id")]
        public string? SleekflowUserId { get; set; }

        [JsonProperty("tenanthub_user_id")]
        public string? TenantHubUserId { get; set; }

        [JsonProperty("user_name")]
        public string UserName { get; set; }

        [JsonProperty("display_name")]
        public string DisplayName { get; set; }

        [JsonProperty("first_name")]
        public string FirstName { get; set; }

        [JsonProperty("last_name")]
        public string LastName { get; set; }

        [JsonProperty("phone_number")]
        public string PhoneNumber { get; set; }

        [JsonProperty("password")]
        public string Password { get; set; }

        [JsonProperty("token")]
        public string Token { get; set; }

        [JsonProperty("position")]
        public string Position { get; set; }

        [JsonProperty("time_zone_info_id")]
        public string TimeZoneInfoId { get; set; }

        [JsonProperty("location")]
        public string Location { get; set; }
    }

    public class Auth0CompleteInvitationResponse
    {
        [JsonProperty("message")]
        public string Message { get; set; }

        [JsonProperty("updated_users")]
        public List<Auth0User>? UpdatedUsers { get; set; }

        [JsonConstructor]
        public Auth0CompleteInvitationResponse(string message, List<Auth0User>? updatedUsers = null)
        {
            this.Message = message;
            this.UpdatedUsers = updatedUsers;
        }
    }

    public class SyncEmailVerifiedInput
    {
        [Required]
        [JsonProperty("sleekflow_user_id")]
        public string SleekflowUserId { get; set; }

        public SyncEmailVerifiedInput(string sleekflowUserId)
        {
            SleekflowUserId = sleekflowUserId;
        }
    }

    [HttpPost]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]

    public async Task<IActionResult> SyncEmailVerified(
        [FromBody]
        SyncEmailVerifiedInput syncEmailVerifiedInput)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(
                new ResponseViewModel()
                {
                    message = ModelState?.First().Value?.Errors.First().ErrorMessage
                });
        }

        var auth0Users = await _userManager.GetAuth0UsersById(syncEmailVerifiedInput.SleekflowUserId);
        var auth0User = auth0Users.Find(
            u => u.Identities.Any(i => i.Connection == _auth0Config.DatabaseConnectionName));
        var user = await _userManager.FindByIdAsync(syncEmailVerifiedInput.SleekflowUserId);
        _logger.LogInformation(
            "Obtained asp.net user information {User} {Auth0Usser}",
            JsonConvert.SerializeObject(user),
            JsonConvert.SerializeObject(auth0User));
        if (user is null || auth0User is null)
        {
            return BadRequest(
                new ResponseViewModel()
                {
                    message = "User not found"
                });
        }

        user.LastLoginAt = DateTime.Now;
        user.EmailConfirmed = auth0User.EmailVerified.GetValueOrDefault();
        await _userManager.UpdateDbUserAsync(user);

        return Ok(new ResponseViewModel() { message = "ok" });
    }

    /// <summary>
    /// Replace "account/invitation/complete".
    /// </summary>
    /// <param name="completeInvitationViewModel"></param>
    /// <returns></returns>
    [HttpPost]
    [AllowAnonymous]
    [ProducesResponseType(typeof(AccessTokenResponse), StatusCodes.Status200OK)]
    public async Task<Auth0CompleteInvitationResponse> CompleteInvitation(
        [FromBody]
        Auth0CompleteInvitationViewModel completeInvitationViewModel)
    {
        if (ModelState.IsValid)
        {
            try
            {
                var dbUser = await _userManager.FindByIdAsync(completeInvitationViewModel.SleekflowUserId);
                if (dbUser == null)
                {
                    _logger.LogError("[CompleteInvitation] User not found. UserId: {UserId}", completeInvitationViewModel.SleekflowUserId);
                    return
                        new Auth0CompleteInvitationResponse(
                            "user not found");
                }

                var validateResult = await _userManager.VerifyUserTokenAsync(
                    dbUser,
                    SleekflowTokenProviderOptions.InviteTokenProviderName,
                    "InviteUserToken",
                    completeInvitationViewModel.Token);

                _logger.LogInformation(
                    "[CompleteInvitation] User {Name} is completing invitation. Token validate result {ValidateResult}. Token:\n{Token}",
                    completeInvitationViewModel.UserName,
                    validateResult,
                    completeInvitationViewModel.Token);

                if (!validateResult)
                {
                    _logger.LogError(
                        "[CompleteInvitation] Invalid token of UserId: {UserId}, Token:\n{Token}",
                        completeInvitationViewModel.SleekflowUserId,
                        completeInvitationViewModel.Token);
                    return
                        new Auth0CompleteInvitationResponse("Invalid token");
                }

                // Check token before update
                var resetPasswordResult = await _userManager.ResetPasswordAsync(
                    dbUser,
                    completeInvitationViewModel.Password);
                if (!resetPasswordResult.Succeeded)
                {
                    _logger.LogError(
                        "[CompleteInvitation] {Name} reset password failure. Errors: {SerializeObject}",
                        completeInvitationViewModel.UserName,
                        JsonConvert.SerializeObject(resetPasswordResult.Errors));

                    return
                        new Auth0CompleteInvitationResponse(resetPasswordResult.Errors.First().Description);
                }

                var changeUserNameResult =
                    await _userManager.SetUserNameAsync(dbUser, completeInvitationViewModel.UserName);
                if (!changeUserNameResult.Succeeded)
                {
                    _logger.LogError(
                        "[CompleteInvitation] {UserId} set username failure, Errors: {Errors}",
                        completeInvitationViewModel.SleekflowUserId,
                        JsonConvert.SerializeObject(changeUserNameResult.Errors));

                    return
                        new Auth0CompleteInvitationResponse(changeUserNameResult.Errors.First().Description);
                }

                dbUser.DisplayName = completeInvitationViewModel.DisplayName;
                dbUser.FirstName = completeInvitationViewModel.FirstName;
                dbUser.LastName = completeInvitationViewModel.LastName;
                dbUser.PhoneNumber = completeInvitationViewModel.PhoneNumber;
                dbUser.InviteToken = string.Empty;
                dbUser.InviteTokenExpireAt = null;

                var updateResult = await _userManager.UpdateAndGetAsync(dbUser);
                if (!updateResult.IdentityResult.Succeeded)
                {
                    _logger.LogError(
                        "[CompleteInvitation] Update user {UserId} failure, Errors: {Errors}",
                        completeInvitationViewModel.SleekflowUserId,
                        JsonConvert.SerializeObject(updateResult.IdentityResult.Errors));

                    return
                        new Auth0CompleteInvitationResponse(updateResult.IdentityResult.Errors.First().Description);
                }

                var staff = await _appDbContext.UserRoleStaffs
                    .Where(x => x.IdentityId == dbUser.Id)
                    .Include(x=>x.Company)
                    .FirstAsync();

                staff.Position = completeInvitationViewModel.Position;
                staff.TimeZoneInfoId = string.IsNullOrEmpty(completeInvitationViewModel.TimeZoneInfoId)
                    ? staff.Company.TimeZoneInfoId
                    : completeInvitationViewModel.TimeZoneInfoId;

                await _appDbContext.SaveChangesAsync();
                await _coreService.UpdatePlanFreeToFreemium(staff);

                BackgroundJob.Enqueue<ICoreService>(
                    x =>
                        x.AddToSleekFlowCRM(staff.CompanyId, dbUser, "User", null, null, null, null));

                return new Auth0CompleteInvitationResponse("ok", updateResult.Auth0Users);
            }
            catch (Exception err)
            {
                _logger.LogError(
                    err,
                    "[CompleteInvitation] General exception: {ExceptionMessage}",
                    err.Message);

                return new Auth0CompleteInvitationResponse(err.Message);
            }
        }

        if (!ModelState.IsValid)
        {
            var model = JsonConvert.SerializeObject(completeInvitationViewModel);
            _logger.LogError(
                "[CompleteInvitation] Invalid model state: {@ModelState}, Payload: {Payload}",
                ModelState,
                model);
        }

        return new Auth0CompleteInvitationResponse(ModelState?.First().Value?.Errors.First().ErrorMessage);
    }

    public class ResetMfaRequest
    {
        [JsonRequired]
        [JsonProperty("user_id")]
        public string? UserId { get; set; }

        [JsonRequired]
        [JsonProperty("mfa_id")]
        public string? MfaId { get; set; }
    }

    [HttpPost]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<ActionResult> ResetMfa([FromBody] ResetMfaRequest resetMfaRequest)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(
                new ResponseViewModel()
                {
                    message = ModelState?.First().Value?.Errors.First().ErrorMessage
                });
        }

        var companyRequestUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (companyRequestUser.RoleType != StaffUserRole.Admin)
        {
            return Unauthorized();
        }

        var user = await _userManager.FindByIdAsync(resetMfaRequest.UserId!);
        if (user is null)
        {
            return BadRequest(
                new ResponseViewModel()
                {
                    message = "User not found"
                });
        }

        var userStaff = await _coreService.GetCompanyStaff(user);
        if (userStaff?.CompanyId != companyRequestUser.CompanyId)
        {
            return Unauthorized();
        }

        var a0Users = await _userManager.GetAuth0Users(user);
        var isRevoked = false;

        foreach (var a0User in a0Users)
        {
            isRevoked = await _userManager.ResetMfaAsync(
                a0User.UserId,
                resetMfaRequest.MfaId);

            if (isRevoked)
            {
                break;
            }
        }

        if (!isRevoked)
        {
            return BadRequest(new ResponseViewModel() { message ="Mfa Reset failed" });
        }

        await _staffHooks.OnStaff2FaRevokedAsync(userStaff.CompanyId, userStaff.Id);
        return Ok(new ResponseViewModel() { message = "ok" });
    }

    public class GetMfaListRequest
    {
        [JsonRequired]
        [JsonProperty("user_id")]
        public string? UserId { get; set; }
    }

    public class GetMfaListResponse
    {
        [JsonProperty("mfa_id")]
        public string? MfaId { get; set; }

        [JsonProperty("mfa_type")]
        public string? MfaType { get; set; }

        [JsonProperty("confirmed")]
        public bool? Confirmed { get; set; }

        [JsonProperty("phone_number")]
        public string? PhoneNumber { get; set; }

        [JsonProperty("email")]
        public string? Email { get; set; }

        [JsonProperty("created_at")]
        public DateTime? CreatedAt { get; set; }

        [JsonProperty("enrolled_at")]
        public DateTime? EnrolledAt { get; set; }
    }

    [HttpPost]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<ActionResult<List<GetMfaListResponse>>> GetMfaList(
        [FromBody]
        GetMfaListRequest getMfaListRequest)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(
                new ResponseViewModel()
                {
                    message = ModelState?.First().Value?.Errors?.First().ErrorMessage
                });
        }

        var companyRequestUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (companyRequestUser.RoleType != StaffUserRole.Admin)
        {
            return Unauthorized();
        }

        var user = await _userManager.FindByIdAsync(getMfaListRequest.UserId!);
        if (user is null)
        {
            return BadRequest(
                new ResponseViewModel()
                {
                    message = "User not found"
                });
        }

        var userStaff = await _coreService.GetCompanyStaff(user);
        if (userStaff?.CompanyId != companyRequestUser.CompanyId)
        {
            return Unauthorized();
        }

        var getAllFeaturesOutputOutput =
            await _featuresApi.FeaturesGetAllFeaturesPostAsync(body: new object());

        var mfaFeature =
            getAllFeaturesOutputOutput.Data.Features.Find(f => f.Name == "2FA");

        if (mfaFeature is null)
        {
            _logger.LogError(
                "[{MethodName}], Error return when try to get features from TenantHub. Comapny: {CompanyId}, User: {UserId}",
                nameof(GetMfaList),
                companyRequestUser.CompanyId,
                companyRequestUser.IdentityId);

            return BadRequest(
                new ResponseViewModel()
                {
                    message = "Cannot found 2FA feature"
                });
        }

        var isMfaEnabled =
            await _enabledFeaturesApi.EnabledFeaturesGetFeatureEnablementsPostAsync(
                getFeatureEnablementsInput: new GetFeatureEnablementsInput(mfaFeature!.Id, companyRequestUser.CompanyId));

        if (!isMfaEnabled.Data.IsFeatureEnabledForCompany)
        {
            return Ok(new object());
        }

        var a0Users = await _userManager.GetAuth0Users(user);
        var mfaList = new PagedList<AuthenticationMethod>();

        foreach (var a0User in a0Users)
        {
            var mfa = await _userManager.GetMfaAsync(a0User.UserId);
            mfaList.AddRange(mfa);
        }

        if (mfaList.Count == 0)
        {
            return Ok(new object());
        }

        var result = new List<GetMfaListResponse>();
        foreach (var item in mfaList)
        {
           result.Add(new GetMfaListResponse()
           {
               MfaId = item.Id,
               MfaType = item.Type,
               Confirmed = item.Confirmed,
               PhoneNumber = item.PhoneNumber,
               Email = item.Email,
               CreatedAt = item.CreatedAt,
               EnrolledAt = item.EnrolledAt
           });
        }

        return Ok(result);
    }
}