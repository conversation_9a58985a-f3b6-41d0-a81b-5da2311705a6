using System.Threading.Tasks;
using Travis_backend.Constants;
using Travis_backend.ConversationDomain.Models;
using Travis_backend.Database;
using Travis_backend.MessageDomain.Models;
using Travis_backend.OpenTelemetry.Meters;
using Travis_backend.OpenTelemetry.Meters.Constants;

namespace Travis_backend.MessageDomain.ChannelMessageProvider.ChannelMessageHandlers;

public class WebChannelMessageHandler : IChannelMessageHandler
{
    private readonly ApplicationDbContext _appDbContext;
    private readonly IConversationMeters _conversationMeters;

    public string ChannelType => ChannelTypes.LiveChat;

    public WebChannelMessageHandler(ApplicationDbContext appDbContext, IConversationMeters conversationMeters)
    {
        _appDbContext = appDbContext;
        _conversationMeters = conversationMeters;
    }

    public async ValueTask<ConversationMessage> SendChannelMessageAsync(
        Conversation conversation,
        ConversationMessage conversationMessage)
    {
        conversationMessage.Status = MessageStatus.Read;
        await _appDbContext.SaveChangesAsync();

        _conversationMeters.IncrementCounter(ChannelTypes.LiveChat, ConversationMeterOptions.SendSuccess);

        return conversationMessage;
    }
}