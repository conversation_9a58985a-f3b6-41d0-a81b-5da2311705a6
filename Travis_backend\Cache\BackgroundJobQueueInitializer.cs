﻿using System;
using System.Collections.Generic;
using Hangfire;
using Hangfire.Pro.Redis;

namespace Travis_backend.Cache;

public static class BackgroundJobQueueInitializer
{
    private static readonly List<RedisStorage> Storages = new ();

    public static List<RedisStorage> ConfigureHangfire(int prefixGroupNum, string connectionString)
    {
        for (var i = 0; i < prefixGroupNum; i++)
        {
            var redisStorage = new RedisStorage(
                connectionString,
                new RedisStorageOptions
                {
                    Prefix = $"{{hangfire:sleekflow-{i}:}}"
                });
            Storages.Add(redisStorage);
            GlobalConfiguration.Configuration.UseStorage(redisStorage)
                .WithJobExpirationTimeout(TimeSpan.FromMinutes(30));
        }

        return Storages;
    }

    public static List<RedisStorage> GetHangfireStorages()
    {
        return Storages;
    }
}