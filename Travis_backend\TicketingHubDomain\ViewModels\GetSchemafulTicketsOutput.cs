using System;
using System.Collections.Generic;
using System.Runtime.Serialization;
using Newtonsoft.Json;
using Sleekflow.Apis.TicketingHub.Model;

namespace Travis_backend.TicketingHubDomain.ViewModels;

public class GetSchemafulTicketsOutputOutput : GetTicketsOutputOutput
{
    [DataMember(Name = "data", EmitDefaultValue = true)]
    public new GetSchemafulTicketsOutput Data { get; set; }

    public GetSchemafulTicketsOutputOutput(
        bool success,
        GetSchemafulTicketsOutput data,
        string message,
        DateTimeOffset dateTime,
        int httpStatusCode,
        int? errorCode,
        Dictionary<string, object> errorContext,
        string requestId)
        : base(success, new GetTicketsOutput(), message, dateTime, httpStatusCode, errorCode, errorContext, requestId)
    {
        Data = data;
    }
}

public class GetSchemafulTicketsOutput : GetTicketsOutput
{
    [JsonProperty("tickets")]
    public new List<GetSchemafulTicketDto> Tickets { get; set; }

    public GetSchemafulTicketsOutput(List<GetSchemafulTicketDto> tickets, int count, string continuationToken)
        : base(new List<TicketDto>(), count, continuationToken)
    {
        Tickets = tickets;
    }
}

public class GetSchemafulTicketDto : TicketDto
{
    [JsonProperty("conversation_id")]
    public string ConversationId { get; set; }

    [JsonProperty("user_profile")]
    public TicketUserProfileViewModel UserProfile { get; set; }

    public GetSchemafulTicketDto(
        string id,
        string sleekflowCompanyId,
        long externalId,
        string title,
        string description,
        string statusId,
        string priorityId,
        string typeId,
        Channel channel,
        List<MediaDto> medias,
        DateTimeOffset? dueDate,
        string sleekflowUserProfileId,
        string assigneeId,
        List<string> assignedTeamIds,
        List<long> associatedMessageIds,
        string url,
        string resolutionTime,
        DateTimeOffset? resolvedAt,
        DateTimeOffset createdAt,
        string conversationId,
        TicketUserProfileViewModel userProfile)
        : base(
            sleekflowCompanyId,
            externalId,
            title,
            description,
            statusId,
            priorityId,
            typeId,
            channel,
            medias,
            dueDate,
            sleekflowUserProfileId,
            assigneeId,
            assignedTeamIds,
            associatedMessageIds,
            url,
            resolutionTime,
            resolvedAt,
            createdAt,
            id)
    {
        ConversationId = conversationId;
        UserProfile = userProfile;
    }
}