﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using AutoMapper;
using Indicia.HubSpot.Api;
using Indicia.HubSpot.Api.Companies;
using Indicia.HubSpot.Api.Contacts;
using Indicia.HubSpot.Api.Tickets;
using Indicia.HubSpot.Core.Crud;
using Indicia.HubSpot.Core.Search.Dto;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using RestSharp;
using Travis_backend.Database;
using Travis_backend.InternalDomain.Models;
using Travis_backend.InternalDomain.ViewModels;

namespace Travis_backend.InternalDomain.Services
{
    public interface IInternalHubspotRepository
    {
        // Company
        Task<InternalHubSpotCompany> GetCompanyObjectAsync(string hubSpotCompanyId);

        Task<InternalHubSpotCompany> GetCompanyObjectByCompanyIdAsync(string companyId);

        Task<InternalHubSpotCompany> GetCompanyObjectIdByCompanyIdAsync(string companyId);

        Task<InternalHubSpotCompany> GetCompanyObjectIdByDomainAsync(string email);

        Task<InternalHubSpotCompany> GetCompanyObjectByPartnerStackLeadKeyAsync(string leadKey);

        Task<InternalHubSpotCompany> GetCompanyObjectByPartnerStackDealKeyAsync(string dealKey);

        Task<InternalHubSpotCompany> GetCompanyObjectByPartnerStackPartnerKeyAsync(string partnerKey);

        Task<InternalHubSpotCompany> CreateOrUpdateCompanyObjectAsync(InternalHubSpotCompany hubSpotCompany);

        Task<InternalHubSpotCompany> CreateCompanyObjectAsync(InternalHubSpotCompany hubSpotCompany);

        Task<InternalHubSpotCompany> UpdateCompanyObjectAsync(InternalHubSpotCompany hubSpotCompany);

        Task<int> BulkUpdateCompanyObjectAsync(InternalHubSpotCompany[] hubSpotCompanies);

        Task<int> BulkUpdateAnalyticCompanyObjectAsync(InternalHubSpotAnalyticCompany[] hubSpotCompanies);

        ValueTask<bool> AssociateChildCompanyToParentCompanyAsync(
            InternalHubSpotCompany childHubSpotCompanyObject,
            InternalHubSpotCompany parentHubSpotCompanyObject);

        // Contact
        Task<InternalHubSpotContact> GetContactObjectAsync(string hubSpotContactId);

        Task<InternalHubSpotContact> GetContactObjectByEmailAsync(string email);

        Task<InternalHubSpotContact> GetContactObjectIdByEmailAsync(string email);

        Task<InternalHubSpotContact> CreateOrUpdateContactObjectAsync(
            InternalHubSpotContact hubSpotContact,
            InternalHubSpotCompany associatedHubSpotCompanyObject = null);

        Task<InternalHubSpotContact> CreateContactObjectAsync(
            InternalHubSpotContact hubSpotContact,
            InternalHubSpotCompany associatedHubSpotCompanyObject = null);

        Task<InternalHubSpotContact> UpdateContactObjectAsync(
            InternalHubSpotContact hubSpotContact,
            InternalHubSpotCompany associatedHubSpotCompanyObject = null);

        ValueTask<bool> AssociateContactToCompanyAsync(
            InternalHubSpotContact hubSpotContact,
            InternalHubSpotCompany associatedHubSpotCompanyObject);

        ValueTask<bool> UnassociateContactToCompanyAsync(
            InternalHubSpotContact hubSpotContact,
            InternalHubSpotCompany associatedHubSpotCompanyObject);

        // Ticket
        Task<T> GetTicketObjectAsync<T>(string ticketObjectId) where T : HubSpotTicketObject, new();

        Task<T> CreateTicketAsync<T>(
            T ticket,
            InternalHubSpotCompany associateHubSpotCompanyObject = null,
            InternalHubSpotContact associateHubSpotContactObject = null) where T : HubSpotTicketObject, new();

        // Task<T> UpdateTicketAsync<T>(T ticket) where T : HubSpotTicketObject, new();
        Task<bool> UpdateTicketAsync<T>(string ticketObjectId, T payload) where T : new();

        Task<bool> AttachFileToTicketAsync(string ticketObjectId, List<long> fileId);

        // Contact Owner
        Task<InternalHubSpotOwnerDto> GetHubSpotContactOwnersAsync();

        // Api Usage
        Task<IntegrationLimitResponse> GetHubSpotApiUsageAsync();

        // File
        Task<HubSpotUploadFileResponse> UploadFileToHubSpotAsync(string fileName, byte[] fileBytes);
    }

    public class InternalHubspotRepository : IInternalHubspotRepository
    {
        private readonly ApplicationDbContext _appDbContext;
        private readonly IConfiguration _configuration;
        private readonly ILogger<InternalHubspotRepository> _logger;
        private readonly IHubSpotApi _hubSpotApi;

        public InternalHubspotRepository(
            ApplicationDbContext appDbContext,
            IConfiguration configuration,
            ILogger<InternalHubspotRepository> logger,
            IHubSpotApi hubSpotApi)
        {
            _appDbContext = appDbContext;
            _configuration = configuration;
            _logger = logger;
            _hubSpotApi = hubSpotApi;
        }

        #region Company Object

        public async Task<InternalHubSpotCompany> GetCompanyObjectAsync(string hubSpotCompanyId)
        {
            var companyObject = await _hubSpotApi
                .GetCompanyApi<InternalHubSpotCompany>()
                .ReadAsync(
                    hubSpotCompanyId,
                    new ReadParameters()
                    {
                        IdProperty = null,
                        Properties = InternalHubSpotCompany.Property.AllProperties,
                        NotFoundReturnsNull = true
                    });

            return companyObject;
        }

        public async Task<InternalHubSpotCompany> GetCompanyObjectByCompanyIdAsync(string companyId)
        {
            var hubspotCompanyMapping =
                await _appDbContext.CmsHubSpotCompanyMaps.FirstOrDefaultAsync(x => x.CompanyId == companyId);

            if (hubspotCompanyMapping == null)
            {
                return await GetCompanyObjectByCompanyIdAtHubSpot(companyId);
            }

            return await GetCompanyObjectAsync(hubspotCompanyMapping.HubSpotCompanyObjectId);
        }

        public async Task<InternalHubSpotCompany> GetCompanyObjectIdByCompanyIdAsync(string companyId)
        {
            var companyObjectId = await _appDbContext.CmsHubSpotCompanyMaps.AsNoTracking()
                .FirstOrDefaultAsync(x => x.CompanyId == companyId);

            if (companyObjectId == null)
            {
                var companyObject = await GetCompanyObjectByCompanyIdAtHubSpot(companyId);

                if (companyObject != null)
                {
                    return new InternalHubSpotCompany()
                    {
                        Id = companyObject.Id
                    };
                }
                else
                {
                    return null;
                }
            }

            return new InternalHubSpotCompany()
            {
                Id = companyObjectId.HubSpotCompanyObjectId
            };
        }

        public async Task<InternalHubSpotCompany> GetCompanyObjectIdByDomainAsync(string domain)
        {
            if (string.IsNullOrWhiteSpace(domain))
            {
                return null;
            }

            var companyResult = await _hubSpotApi
                .GetCompanyApi<InternalHubSpotCompany>()
                .SearchAsync(
                    new SearchParameters()
                    {
                        Properties = InternalHubSpotCompany.Property.IdProperties,
                        FilterGroups = new List<SearchParameters.FilterGroup>()
                        {
                            new ()
                            {
                                Filters = new List<SearchParameters.Filter>()
                                {
                                    new ()
                                    {
                                        PropertyName = InternalHubSpotCompany.Property.Domain,
                                        Operator = SearchParameters.FilterOperator.EqualTo,
                                        Value = domain
                                    }
                                }
                            }
                        }
                    });

            var companyWithoutSync = companyResult.Results.FirstOrDefault(x => x.SleekflowCompanyId == null);

            if (companyWithoutSync != null)
            {
                return companyWithoutSync;
            }

            return companyResult.Results.FirstOrDefault();
        }

        public async Task<InternalHubSpotCompany> GetCompanyObjectByPartnerStackLeadKeyAsync(string leadKey)
        {
            if (string.IsNullOrWhiteSpace(leadKey))
            {
                return null;
            }

            var companyResult = await _hubSpotApi
                .GetCompanyApi<InternalHubSpotCompany>()
                .SearchAsync(
                    new SearchParameters()
                    {
                        Properties = InternalHubSpotCompany.Property.IdProperties,
                        FilterGroups = new List<SearchParameters.FilterGroup>()
                        {
                            new ()
                            {
                                Filters = new List<SearchParameters.Filter>()
                                {
                                    new ()
                                    {
                                        PropertyName = InternalHubSpotCompany.Property.PartnerStackLeadKey,
                                        Operator = SearchParameters.FilterOperator.EqualTo,
                                        Value = leadKey
                                    }
                                }
                            }
                        }
                    });

            return companyResult.Results.FirstOrDefault();
        }

        public async Task<InternalHubSpotCompany> GetCompanyObjectByPartnerStackDealKeyAsync(string dealKey)
        {
            if (string.IsNullOrWhiteSpace(dealKey))
            {
                return null;
            }

            var companyResult = await _hubSpotApi
                .GetCompanyApi<InternalHubSpotCompany>()
                .SearchAsync(
                    new SearchParameters()
                    {
                        Properties = InternalHubSpotCompany.Property.IdProperties,
                        FilterGroups = new List<SearchParameters.FilterGroup>()
                        {
                            new ()
                            {
                                Filters = new List<SearchParameters.Filter>()
                                {
                                    new ()
                                    {
                                        PropertyName = InternalHubSpotCompany.Property.PartnerStackDealKey,
                                        Operator = SearchParameters.FilterOperator.EqualTo,
                                        Value = dealKey
                                    }
                                }
                            }
                        }
                    });

            return companyResult.Results.FirstOrDefault();
        }

        public async Task<InternalHubSpotCompany> GetCompanyObjectByPartnerStackPartnerKeyAsync(string partnerKey)
        {
            if (string.IsNullOrWhiteSpace(partnerKey))
            {
                return null;
            }

            var companyResult = await _hubSpotApi
                .GetCompanyApi<InternalHubSpotCompany>()
                .SearchAsync(
                    new SearchParameters()
                    {
                        Properties = InternalHubSpotCompany.Property.IdProperties,
                        FilterGroups = new List<SearchParameters.FilterGroup>()
                        {
                            new ()
                            {
                                Filters = new List<SearchParameters.Filter>()
                                {
                                    new ()
                                    {
                                        PropertyName = InternalHubSpotCompany.Property.PartnerStackPartnerKey,
                                        Operator = SearchParameters.FilterOperator.EqualTo,
                                        Value = partnerKey
                                    },
                                    new ()
                                    {
                                        PropertyName = InternalHubSpotCompany.Property.PartnerStackDealKey,
                                        Operator = SearchParameters.FilterOperator.DoesNotHavePropertyValue
                                    },
                                    new ()
                                    {
                                        PropertyName = InternalHubSpotCompany.Property.PartnerStackLeadKey,
                                        Operator = SearchParameters.FilterOperator.DoesNotHavePropertyValue
                                    },
                                    new ()
                                    {
                                        PropertyName = InternalHubSpotCompany.Property.CustomerKey,
                                        Operator = SearchParameters.FilterOperator.DoesNotHavePropertyValue
                                    },
                                }
                            }
                        }
                    });

            return companyResult.Results.FirstOrDefault();
        }

        public async Task<InternalHubSpotCompany> CreateOrUpdateCompanyObjectAsync(
            InternalHubSpotCompany hubSpotCompany)
        {
            // Create
            if (string.IsNullOrWhiteSpace(hubSpotCompany.Id))
            {
                return await CreateCompanyObjectAsync(hubSpotCompany);
            }

            // Update
            return await UpdateCompanyObjectAsync(hubSpotCompany);
        }

        public async Task<InternalHubSpotCompany> CreateCompanyObjectAsync(InternalHubSpotCompany hubSpotCompany)
        {
            var hubSpotCompanyObject = await _hubSpotApi
                .GetCompanyApi<InternalHubSpotCompany>()
                .CreateAsync(hubSpotCompany);

            if (hubSpotCompany.SleekflowCompanyId != null &&
                !await _appDbContext.CmsHubSpotCompanyMaps.AnyAsync(
                    x => x.CompanyId == hubSpotCompany.SleekflowCompanyId &&
                         x.HubSpotCompanyObjectId == hubSpotCompanyObject.Id))
            {
                // Add Mapping
                _appDbContext.CmsHubSpotCompanyMaps.Add(
                    new CmsHubSpotCompanyMap()
                    {
                        CompanyId = hubSpotCompany.SleekflowCompanyId,
                        HubSpotCompanyObjectId = hubSpotCompanyObject.Id,
                        LastSyncAt = DateTime.UtcNow,
                        CreatedAt = DateTime.UtcNow,
                    });

                await _appDbContext.SaveChangesAsync();
            }

            return hubSpotCompanyObject;
        }

        public async Task<InternalHubSpotCompany> UpdateCompanyObjectAsync(InternalHubSpotCompany hubSpotCompany)
        {
            var hubSpotCompanyObject = await _hubSpotApi
                .GetCompanyApi<InternalHubSpotCompany>()
                .UpdateAsync(hubSpotCompany);

            if (hubSpotCompany.SleekflowCompanyId != null)
            {
                var cmsHubSpotCompanyMap =
                    await _appDbContext.CmsHubSpotCompanyMaps.FirstOrDefaultAsync(
                        x => x.CompanyId == hubSpotCompany.SleekflowCompanyId);

                if (cmsHubSpotCompanyMap == null)
                {
                    cmsHubSpotCompanyMap = new CmsHubSpotCompanyMap()
                    {
                        CompanyId = hubSpotCompany.SleekflowCompanyId, HubSpotCompanyObjectId = hubSpotCompany.Id,
                    };

                    _appDbContext.CmsHubSpotCompanyMaps.Add(cmsHubSpotCompanyMap);
                }

                cmsHubSpotCompanyMap.LastSyncAt = DateTime.UtcNow;

                if (hubSpotCompanyObject.Id != cmsHubSpotCompanyMap.HubSpotCompanyObjectId)
                {
                    cmsHubSpotCompanyMap.HubSpotCompanyObjectId = hubSpotCompany.Id;
                }

                await _appDbContext.SaveChangesAsync();
            }

            return hubSpotCompanyObject;
        }

        public async Task<int> BulkUpdateCompanyObjectAsync(InternalHubSpotCompany[] hubSpotCompanies)
        {
            var result = await _hubSpotApi.GetObjectApi<InternalHubSpotCompany>()
                .BatchUpdateAsync(hubSpotCompanies);

            await _appDbContext.CmsHubSpotCompanyMaps
                .Where(x => hubSpotCompanies.Select(c => c.SleekflowCompanyId).Contains(x.CompanyId))
                .ExecuteUpdateAsync(
                    calls => calls.SetProperty(
                        p => p.LastSyncAt,
                        DateTime.UtcNow));

            await _appDbContext.SaveChangesAsync();

            return hubSpotCompanies.Length;
        }

        public async Task<int> BulkUpdateAnalyticCompanyObjectAsync(InternalHubSpotAnalyticCompany[] hubSpotCompanies)
        {
            var result = await _hubSpotApi.GetObjectApi<InternalHubSpotAnalyticCompany>()
                .BatchUpdateAsync(hubSpotCompanies);

            return hubSpotCompanies.Length;
        }

        public async ValueTask<bool> AssociateChildCompanyToParentCompanyAsync(
            InternalHubSpotCompany childHubSpotCompanyObject,
            InternalHubSpotCompany parentHubSpotCompanyObject)
        {
            if (childHubSpotCompanyObject.Id == null || parentHubSpotCompanyObject.Id == null)
            {
                return false;
            }

            var request = new AssociationChildCompanyToParentCompanyRequest
            {
                FromObjectId = long.Parse(childHubSpotCompanyObject.Id),
                ToObjectId = long.Parse(parentHubSpotCompanyObject.Id)
            };

            await _hubSpotApi.Client
                .ExecuteAsync<AssociationChildCompanyToParentCompanyResponse,
                    AssociationChildCompanyToParentCompanyRequest>(
                    "crm-associations/v1/associations",
                    request,
                    Method.PUT,
                    CancellationToken.None);

            return true;
        }

        private async Task<InternalHubSpotCompany> GetCompanyObjectByCompanyIdAtHubSpot(string companyId)
        {
            var companyResult = await _hubSpotApi
                .GetCompanyApi<InternalHubSpotCompany>()
                .SearchAsync(
                    new SearchParameters()
                    {
                        Properties = InternalHubSpotCompany.Property.IdProperties,
                        FilterGroups = new List<SearchParameters.FilterGroup>()
                        {
                            new ()
                            {
                                Filters = new List<SearchParameters.Filter>()
                                {
                                    new ()
                                    {
                                        PropertyName = InternalHubSpotCompany.Property.SleekflowCompanyId,
                                        Operator = SearchParameters.FilterOperator.EqualTo,
                                        Value = companyId
                                    }
                                }
                            }
                        }
                    });

            var hubspotCompany = companyResult.Results.FirstOrDefault();

            return hubspotCompany;
        }

        #endregion

        public async Task<InternalHubSpotContact> GetContactObjectAsync(string hubSpotContactId)
        {
            var contactObject = await _hubSpotApi
                .GetContactApi<InternalHubSpotContact>()
                .ReadAsync(
                    hubSpotContactId,
                    new ReadParameters()
                    {
                        IdProperty = null,
                        Properties = InternalHubSpotContact.Property.AllProperties,
                        NotFoundReturnsNull = true
                    });

            return contactObject;
        }

        public async Task<InternalHubSpotContact> GetContactObjectByEmailAsync(string email)
        {
            var contactObject = await _hubSpotApi
                .GetContactApi<InternalHubSpotContact>()
                .ReadAsync(
                    email,
                    new ReadParameters()
                    {
                        IdProperty = "email",
                        Properties = InternalHubSpotContact.Property.AllProperties,
                        NotFoundReturnsNull = true
                    });

            return contactObject;
        }

        public async Task<InternalHubSpotContact> GetContactObjectIdByEmailAsync(string email)
        {
            var contactObjectId = await _appDbContext.CmsHubSpotUserContactMaps
                .Where(x => x.Email == email)
                .Select(
                    x => new InternalHubSpotContact()
                    {
                        Id = x.HubSpotContactObjectId
                    })
                .FirstOrDefaultAsync();

            if (contactObjectId == null)
            {
                var contactObject = await GetContactObjectByEmailAsync(email);

                if (contactObject != null)
                {
                    contactObjectId = new InternalHubSpotContact()
                    {
                        Id = contactObject.Id
                    };
                }
            }

            return contactObjectId;
        }

        public async Task<InternalHubSpotContact> CreateOrUpdateContactObjectAsync(
            InternalHubSpotContact hubSpotContact,
            InternalHubSpotCompany associatedHubSpotCompanyObject = null)
        {
            if (hubSpotContact.Id == null)
            {
                return await CreateContactObjectAsync(hubSpotContact, associatedHubSpotCompanyObject);
            }

            hubSpotContact.Email = null; // avoid update email field in HubSpot

            return await UpdateContactObjectAsync(hubSpotContact, associatedHubSpotCompanyObject);
        }

        public async Task<InternalHubSpotContact> CreateContactObjectAsync(
            InternalHubSpotContact hubSpotContact,
            InternalHubSpotCompany associatedHubSpotCompanyObject = null)
        {
            var hubSpotContactObject = await _hubSpotApi
                .GetContactApi<InternalHubSpotContact>()
                .CreateAsync(hubSpotContact);

            if (associatedHubSpotCompanyObject != null)
            {
                await AssociateContactToCompanyAsync(hubSpotContactObject, associatedHubSpotCompanyObject);
            }

            if (!await _appDbContext.CmsHubSpotUserContactMaps.AnyAsync(
                    x => x.Email == hubSpotContact.Email && x.HubSpotContactObjectId == hubSpotContactObject.Id))
            {
                // Add Mapping
                _appDbContext.CmsHubSpotUserContactMaps.Add(
                    new CmsHubSpotUserContactMap()
                    {
                        Email = hubSpotContact.Email,
                        HubSpotContactObjectId = hubSpotContactObject.Id,
                        LastSyncAt = DateTime.UtcNow,
                        CreatedAt = DateTime.UtcNow,
                    });

                await _appDbContext.SaveChangesAsync();
            }

            return hubSpotContactObject;
        }

        public async Task<InternalHubSpotContact> UpdateContactObjectAsync(
            InternalHubSpotContact hubSpotContact,
            InternalHubSpotCompany associatedHubSpotCompanyObject = null)
        {
            try
            {
                var hubSpotContactObject = await _hubSpotApi
                    .GetContactApi<InternalHubSpotContact>()
                    .UpdateAsync(hubSpotContact);

                if (associatedHubSpotCompanyObject != null)
                {
                    await AssociateContactToCompanyAsync(hubSpotContactObject, associatedHubSpotCompanyObject);
                }

                if (hubSpotContact.Email != null)
                {
                    var userContactMap = await _appDbContext.CmsHubSpotUserContactMaps.FirstOrDefaultAsync(
                        x =>
                            x.Email == hubSpotContact.Email && x.HubSpotContactObjectId == hubSpotContactObject.Id);

                    if (userContactMap == null)
                    {
                        // Add Mapping
                        _appDbContext.CmsHubSpotUserContactMaps.Add(
                            new CmsHubSpotUserContactMap()
                            {
                                Email = hubSpotContact.Email,
                                HubSpotContactObjectId = hubSpotContactObject.Id,
                                LastSyncAt = DateTime.UtcNow,
                                CreatedAt = DateTime.UtcNow,
                            });
                    }
                    else
                    {
                        userContactMap.LastSyncAt = DateTime.UtcNow;
                    }

                    await _appDbContext.SaveChangesAsync();
                }

                return hubSpotContactObject;
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[{MethodName}] error: {ExceptionMessage}",
                    nameof(UpdateContactObjectAsync),
                    ex.Message);

                throw;
            }
        }

        public async ValueTask<bool> AssociateContactToCompanyAsync(
            InternalHubSpotContact hubSpotContact,
            InternalHubSpotCompany associatedHubSpotCompanyObject)
        {
            if (hubSpotContact.Id == null || associatedHubSpotCompanyObject.Id == null)
            {
                return false;
            }

            await _hubSpotApi
                .GetContactApi<InternalHubSpotContact>()
                .AssociateAsync(hubSpotContact, associatedHubSpotCompanyObject);

            _logger.LogInformation(
                "[{MethodName}] Associate Contact (Id: {ContactId}, Email: {ContactEmail}) to Company (Id: {CompanyId}, SleekflowCompanyId: {SleekflowCompanyId})",
                nameof(AssociateContactToCompanyAsync),
                hubSpotContact.Id,
                hubSpotContact.Email,
                associatedHubSpotCompanyObject.Id,
                associatedHubSpotCompanyObject.SleekflowCompanyId);

            return true;
        }

        public async ValueTask<bool> UnassociateContactToCompanyAsync(
            InternalHubSpotContact hubSpotContact,
            InternalHubSpotCompany associatedHubSpotCompanyObject)
        {
            if (hubSpotContact.Id == null || associatedHubSpotCompanyObject.Id == null)
            {
                return false;
            }

            await _hubSpotApi
                .GetContactApi<InternalHubSpotContact>()
                .UnassociateAsync(hubSpotContact, associatedHubSpotCompanyObject);

            _logger.LogInformation(
                "[{MethodName}] UnAssociate Contact (Id: {ContactId}, Email: {ContactEmail}) to Company (Id: {CompanyId}, SleekflowCompanyId: {SleekflowCompanyId})",
                nameof(UnassociateContactToCompanyAsync),
                hubSpotContact.Id,
                hubSpotContact.Email,
                associatedHubSpotCompanyObject.Id,
                associatedHubSpotCompanyObject.SleekflowCompanyId);

            return true;
        }

        public async Task<T> GetTicketObjectAsync<T>(string ticketObjectId) where T : HubSpotTicketObject, new()
        {
            var whatsappApplicationTicketObject = await _hubSpotApi
                .GetTicketApi<T>()
                .ReadAsync(
                    ticketObjectId,
                    new ReadParameters()
                    {
                        IdProperty = null,
                        Properties = HubSpotWhatsAppApplicationTicket.Property.AllProperties,
                        NotFoundReturnsNull = true
                    });

            return whatsappApplicationTicketObject;
        }

        public async Task<T> CreateTicketAsync<T>(
            T ticket,
            InternalHubSpotCompany associateHubSpotCompanyObject = null,
            InternalHubSpotContact associateHubSpotContactObject = null) where T : HubSpotTicketObject, new()
        {
            var ticketObject = await _hubSpotApi
                .GetTicketApi<T>()
                .CreateAsync(ticket);

            if (associateHubSpotCompanyObject != null)
            {
                await _hubSpotApi
                    .GetTicketApi<T>()
                    .AssociateAsync(ticketObject, associateHubSpotCompanyObject);
            }

            if (associateHubSpotContactObject != null)
            {
                await _hubSpotApi
                    .GetTicketApi<T>()
                    .AssociateAsync(ticketObject, associateHubSpotContactObject);
            }

            return ticketObject;
        }

        public async Task<bool> UpdateTicketAsync<T>(string ticketObjectId, T payload) where T : new()
        {
            var internalHubSpotOwnerDto =
                await _hubSpotApi.Client.ExecuteAsync<UpdateWhatsAppApplicationTicketResponse, T>(
                    $"/crm/v3/objects/tickets/{ticketObjectId}",
                    payload,
                    Method.PATCH,
                    CancellationToken.None);

            return true;
        }

        public async Task<bool> AttachFileToTicketAsync(string ticketObjectId, List<long> fileId)
        {
            var payload = AttachFileToTicketRequest.CreateAttachFileToTicket(Int64.Parse(ticketObjectId), fileId);

            var result = await _hubSpotApi.Client.ExecuteAsync<AttachFileToTicketResponse, AttachFileToTicketRequest>(
                $"/engagements/v1/engagements",
                payload,
                Method.POST,
                CancellationToken.None);

            return true;
        }

        public async Task<T> UpdateTicketAsync<T>(T ticket) where T : HubSpotTicketObject, new()
        {
            var whatsappApplicationTicketObject = await _hubSpotApi
                .GetTicketApi<T>()
                .UpdateAsync(ticket);

            return whatsappApplicationTicketObject;
        }

        public async Task<InternalHubSpotOwnerDto> GetHubSpotContactOwnersAsync()
        {
            var internalHubSpotOwnerDto = await _hubSpotApi.Client.ExecuteAsync<InternalHubSpotOwnerDto>(
                "/crm/v3/owners/",
                Method.GET,
                CancellationToken.None,
                new Dictionary<string, string>()
                {
                    {
                        "limit", "200"
                    }
                });

            return internalHubSpotOwnerDto;
        }

        public async Task<IntegrationLimitResponse> GetHubSpotApiUsageAsync()
        {
            var response = await _hubSpotApi.Client.ExecuteAsync<List<IntegrationLimitResponse>>(
                "/integrations/v1/limit/daily",
                Method.GET,
                CancellationToken.None);

            return response.FirstOrDefault();
        }

        public async Task<HubSpotUploadFileResponse> UploadFileToHubSpotAsync(string fileName, byte[] fileBytes)
        {
            if (string.IsNullOrWhiteSpace(_configuration["HubSpot:InternalHubSpotApiKey"]))
            {
                return null;
            }

            var client = new RestClient($"https://api.hubapi.com/files/v3/files");
            var request = new RestRequest(Method.POST);
            request.AddFile("file", fileBytes, fileName);
            request.AddParameter("fileName", fileName);
            request.AddParameter("folderId", HubSpotSupportTicket.AttachmentFolderId);
            request.AddParameter("options", "{\"access\": \"PRIVATE\", \"duplicateValidationStrategy\": \"NONE\"}");
            request.AddHeader("Authorization", $"Bearer {_configuration["HubSpot:InternalHubSpotApiKey"]}");

            var response = await client.ExecuteAsync<HubSpotUploadFileResponse>(request);

            return response.Data;
        }
    }
}