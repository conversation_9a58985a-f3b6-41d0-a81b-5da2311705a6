﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using AutoMapper;
using AutoMapper.QueryableExtensions;
using Hangfire;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Travis_backend.AccountAuthenticationDomain.Models;
using Travis_backend.AccountAuthenticationDomain.Services;
using Travis_backend.AutomationDomain.Models;
using Travis_backend.Cache;
using Travis_backend.Cache.Models.CacheKeyPatterns;
using Travis_backend.CommonDomain.Repositories;
using Travis_backend.CommonDomain.ViewModels;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.CompanyDomain.ViewModels;
using Travis_backend.Constants;
using Travis_backend.ConversationDomain.ConversationAccessControl;
using Travis_backend.ConversationDomain.Models;
using Travis_backend.ConversationDomain.Services;
using Travis_backend.ConversationDomain.ViewModels;
using Travis_backend.ConversationServices;
using Travis_backend.Database;
using Travis_backend.Database.Filters;
using Travis_backend.Database.Services;
using Travis_backend.Enums;
using Travis_backend.Extensions;
using Travis_backend.Filters;
using Travis_backend.Helpers;
using Travis_backend.MessageDomain.Models;
using Travis_backend.MessageDomain.ViewModels;
using Travis_backend.PiiMasking.Models;
using Travis_backend.PiiMaskingDomain.Services;
using Travis_backend.SignalR;
using Travis_backend.StripeIntegrationDomain.Services;

namespace Travis_backend.Controllers.ConversationControllers
{
    [Authorize]
    [TypeFilter(typeof(TaskCanceledExceptionFilter))]
    public class ConversationController : Controller
    {
        private readonly ApplicationDbContext _appDbContext;
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly IMapper _mapper;
        private readonly ILogger _logger;
        private readonly IConfiguration _configuration;
        private readonly IConversationMessageService _conversationMessageService;
        private readonly ISignalRService _signalRService;
        private readonly IAutomationService _automationService;
        private readonly ICacheManagerService _cacheManagerService;
        private readonly ILockService _lockService;
        private readonly IConversationHashtagService _conversationHashtagService;
        private readonly IConversationAssigneeService _conversationAssigneeService;
        private readonly IConversationSummaryService _conversationSummaryService;
        private readonly ICoreService _coreService;
        private readonly ISleekPayService _sleekPayService;
        private readonly IAuditHubAuditLogService _auditHubAuditLogService;
        private readonly IConversationService _conversationService;
        private readonly ISleekflowUserService _sleekflowUserService;
        private readonly IDbContextService _dbContextService;
        private readonly IPiiMaskingService _piiMaskingService;
        private readonly IAccessControlAggregationService _accessControlAggregationService;
        private readonly IConversationAccessControlManager _conversationAccessControlManager;

        public ConversationController(
            ApplicationDbContext appDbContext,
            UserManager<ApplicationUser> userManager,
            IMapper mapper,
            IConfiguration configuration,
            ILogger<ConversationController> logger,
            IConversationMessageService messagingService,
            ISignalRService signalRService,
            IAutomationService automationService,
            ICacheManagerService cacheManagerService,
            ILockService lockService,
            IConversationHashtagService conversationHashtagService,
            IConversationAssigneeService conversationAssigneeService,
            IConversationSummaryService conversationSummaryService,
            ICoreService coreService,
            ISleekPayService sleekPayService,
            IAuditHubAuditLogService auditHubAuditLogService,
            IConversationService conversationService,
            ISleekflowUserService sleekflowUserService,
            IDbContextService dbContextService,
            IPiiMaskingService piiMaskingService,
            IAccessControlAggregationService accessControlAggregationService,
            IConversationAccessControlManager conversationAccessControlManager)
        {
            _userManager = userManager;
            _appDbContext = appDbContext;
            _mapper = mapper;
            _configuration = configuration;
            _logger = logger;
            _conversationMessageService = messagingService;
            _signalRService = signalRService;
            _automationService = automationService;
            _cacheManagerService = cacheManagerService;
            _lockService = lockService;
            _conversationHashtagService = conversationHashtagService;
            _conversationAssigneeService = conversationAssigneeService;
            _coreService = coreService;
            _conversationSummaryService = conversationSummaryService;
            _sleekPayService = sleekPayService;
            _auditHubAuditLogService = auditHubAuditLogService;
            _conversationService = conversationService;
            _sleekflowUserService = sleekflowUserService;
            _dbContextService = dbContextService;
            _piiMaskingService = piiMaskingService;
            _accessControlAggregationService = accessControlAggregationService;
            _conversationAccessControlManager = conversationAccessControlManager;
        }

        /// <summary>
        /// Send typing signal to server.
        /// </summary>
        /// <param name="conversationTypingObject">Json object to send. <b>ConversationId</b></param>
        /// <returns></returns>
        [HttpPost]
        [Route("conversation/typing")]
        [ServiceFilter(typeof(ReadOnlyEndpointAttribute))]
        public async Task<ActionResult<ResponseViewModel>> ConversationTyping(
            [FromBody]
            ConversationTypingObject conversationTypingObject)
        {
            if (!User.Identity.IsAuthenticated)
            {
                return BadRequest();
            }

            var applicationUser = await _sleekflowUserService.GetUserAsync(User);
            var companyUser = await _coreService.GetCompanyStaff(applicationUser);

            if (companyUser == null)
            {
                return Unauthorized();
            }

            conversationTypingObject.StaffId = companyUser.IdentityId;

            await _signalRService.SignalROnConversationTyping(
                companyUser.CompanyId,
                conversationTypingObject);

            return Ok(
                new ResponseViewModel
                {
                    message = "typing.."
                });
        }

        /// <summary>
        /// Change conversation assignee.
        /// </summary>
        /// <param name="conversationId">Targeted ConversationId.</param>
        /// <param name="staffAssignmentViewModel">Staff Assignment Object.</param>
        /// <returns></returns>
        [HttpPost]
        [Route("Conversation/Assign/{conversationId}")]
        public async Task<ActionResult<ConversationAssignResponseViewModel>> StaffAssignment(
            string conversationId,
            [FromBody]
            StaffAssignmentViewModel staffAssignmentViewModel)
        {
            var companyUser = await _coreService.GetCompanyStaff(
                await _userManager.GetUserAsync(User));

            if (companyUser == null)
            {
                return Unauthorized();
            }

            var conversation = await _appDbContext.Conversations
                .Include(x => x.Assignee)
                .Include(x => x.UserProfile)
                .Include(x => x.AdditionalAssignees)
                .ThenInclude(x => x.Assignee.Identity)
                .FirstOrDefaultAsync(
                    x =>
                        x.Id == conversationId
                        && x.CompanyId == companyUser.CompanyId);

            _logger.LogInformation(
                "[{Method}]: In Company {CompanyId}, User {CompanyUserIdentityId} updating Conversation {ConversationId} Assignment with Request {request}, Current Assignment {ConversationAssignmentViewModel}",
                nameof(StaffAssignment),
                companyUser.CompanyId,
                companyUser.IdentityId,
                conversationId,
                JsonConvert.SerializeObject(staffAssignmentViewModel),
                JsonConvert.SerializeObject(
                    new
                    {
                        AssigneeId = conversation?.Assignee?.IdentityId,
                        TeamId = conversation?.AssignedTeamId,
                        AdditionalAssigneeIds =
                            conversation?.AdditionalAssignees?.Select(x => x.Assignee?.IdentityId).ToList()
                    }));

            if (conversation != null)
            {
                if (staffAssignmentViewModel.AssignmentType.HasValue
                    && staffAssignmentViewModel.AssignmentType != AssignmentType.AdditionalAssignee)
                {
                    // OTO cannot takeover the conversation if the conversation already have contact owner
                    if (companyUser.CompanyId == "60cf7ef2-b249-47af-af18-476962ae5c89"
                        && conversation.AssigneeId.HasValue
                        && companyUser.RoleType == StaffUserRole.Staff)
                    {
                        return BadRequest(
                            new ResponseViewModel
                            {
                                message = $"Unable to takeover the conversation"
                            });
                    }

                    Staff assignee = null;

                    if (!string.IsNullOrEmpty(staffAssignmentViewModel.StaffId))
                    {
                        assignee = await _appDbContext.UserRoleStaffs
                            .Include(x => x.Identity)
                            .FirstOrDefaultAsync(
                                x =>
                                    x.CompanyId == companyUser.CompanyId
                                    && x.IdentityId == staffAssignmentViewModel.StaffId);
                    }

                    if (staffAssignmentViewModel.TeamId.HasValue)
                    {
                        var team = await _appDbContext.CompanyStaffTeams
                            .FirstOrDefaultAsync(
                                x =>
                                    x.CompanyId == companyUser.CompanyId
                                    && x.Id == staffAssignmentViewModel.TeamId);

                        await _automationService.AutomationAssignment(
                            conversation.UserProfile,
                            new AutomationAction
                            {
                                AutomatedTriggerType = AutomatedTriggerType.Assignment,
                                AssignmentType = AssignmentType.SpecificGroup,
                                AssignedTeamId = team.Id,
                                AssignedTeam = team,
                                TeamAssignmentType = staffAssignmentViewModel.TeamAssignmentType,
                                AssignedStaffId = assignee?.Id,
                                AssignedStaff = assignee,
                            },
                            changedBy: companyUser.IdentityId,
                            isTriggerUpdate: true);

                        conversation = await _appDbContext.Conversations
                            .Include(x => x.Assignee)
                            .Include(x => x.UserProfile)
                            .FirstOrDefaultAsync(
                                x =>
                                    x.Id == conversationId
                                    && x.CompanyId == companyUser.CompanyId);

                        if (assignee != null
                            && (assignee.Status == StaffStatus.Away
                                || conversation.AssigneeId != assignee.Id))
                        {
                            conversation = await _conversationMessageService.ChangeConversationAssignee(
                                conversation,
                                assignee,
                                changedBy: companyUser.IdentityId);
                        }
                    }
                    else
                    {
                        var company = await _appDbContext.CompanyCompanies
                            .FirstOrDefaultAsync(x => x.Id == companyUser.CompanyId);

                        company.CompanySetting ??= new CompanySetting();

                        // hardcode for OTO, to company unassigned, not team unassigned
                        if ((companyUser.CompanyId == "60cf7ef2-b249-47af-af18-476962ae5c89"
                             || (companyUser.Company.CompanySetting?.IsUnassignedToAll ?? false))
                            && assignee == null)
                        {
                            conversation.AssignedTeamId = null;
                        }

                        if (conversation.AssigneeId != assignee?.Id)
                        {
                            conversation = await _conversationMessageService.ChangeConversationAssignee(
                                conversation,
                                assignee,
                                changedBy: companyUser.IdentityId);
                        }

                        await _appDbContext.SaveChangesAsync();
                    }

                    // if (assignee != null)
                    // {
                    //     BackgroundJob.Enqueue<IEmailNotificationService>(x => x.NewAssigneeNotification(conversation.Id));
                    // }
                    // else
                    // {
                    //     BackgroundJob.Enqueue<IEmailNotificationService>(x => x.SendUnAssigneeNotification(companyUser.IdentityId, conversation.Id));
                    // }
                }

                if (staffAssignmentViewModel.AdditionalAssigneeIds != null
                    && (staffAssignmentViewModel.AdditionalAssigneeIds?.Count > 0
                        || conversation.AdditionalAssignees.Count > 0))
                {
                    var toRemove = conversation.AdditionalAssignees
                        .Where(x => !staffAssignmentViewModel.AdditionalAssigneeIds
                            .Contains(x.Assignee.IdentityId))
                        .ToList();

                    await _conversationAssigneeService.RemoveAdditionalAssignees(
                        conversation,
                        toRemove.Select(x => (long) x.AssigneeId).ToList(),
                        companyUser,
                        true);

                    // foreach (var remove in toRemove)
                    // {
                    //    _appDbContext.ConversationAdditionalAssignees.Remove(remove);
                    //    await _appDbContext.SaveChangesAsync();
                    //    await _signalRService.SignalROnConversationAssigneeDeleted(conversation);
                    // }
                    var toAdd = staffAssignmentViewModel.AdditionalAssigneeIds
                        .Where(y => !conversation.AdditionalAssignees
                            .Select(x => x.Assignee.IdentityId)
                            .Contains(y))
                        .ToList();

                    await _conversationAssigneeService.AddAdditionalAssignees(
                        conversation,
                        await _appDbContext.UserRoleStaffs
                            .Where(
                                x =>
                                    x.CompanyId == companyUser.CompanyId
                                    && toAdd.Contains(x.IdentityId))
                            .Select(x => x.Id)
                            .ToListAsync(),
                        companyUser,
                        true);

                    // foreach (var add in toAdd)
                    // {
                    //    var staff = await _appDbContext.UserRoleStaffs.Where(x => x.IdentityId == add).FirstOrDefaultAsync();
                    //    conversation.AdditionalAssignees.Add(new AdditionalAssignee { ConversationId = conversationId, AssigneeId = staff.Id });
                    //    await _appDbContext.SaveChangesAsync();
                    //    await _signalRService.SignalROnConversationAdditionalAssigneeChanged(conversation, staff.IdentityId);
                    // }
                }

                var responseVm = _mapper.Map<ConversationAssignResponseViewModel>(conversation);

                _logger.LogInformation(
                    "[{Method}] (done): In Company {CompanyId}, User {CompanyUserIdentityId} updated Conversation {ConversationId} Assignment with Request {request}, New Assignment {ConversationAssignmentViewModel}",
                    nameof(StaffAssignment),
                    companyUser.CompanyId,
                    companyUser.IdentityId,
                    conversationId,
                    JsonConvert.SerializeObject(staffAssignmentViewModel),
                    JsonConvert.SerializeObject(
                        new
                        {
                            AssigneeId = conversation?.Assignee?.IdentityId,
                            TeamId = conversation?.AssignedTeamId,
                            AdditionalAssigneeIds =
                                conversation?.AdditionalAssignees?.Select(x => x.Assignee?.IdentityId).ToList()
                        }));

                return Ok(responseVm);
            }

            return BadRequest();
        }

        /// <summary>
        /// Change conversation assignee.
        /// </summary>
        /// <param name="conversationId">Targeted ConversationId.</param>
        /// <param name="staffAssignmentViewModel">Staff Assignment Object.</param>
        /// <returns></returns>
        [HttpPost]
        [Route("v2/conversation/assignee/{conversationId}")]
        public async Task<ActionResult<ConversationAssignResponseViewModel>> UpdateAssignee(
            string conversationId,
            [FromBody]
            StaffAssigneeChangeViewModel staffAssignmentViewModel)
        {
            // var companyUser = _appDbContext.UserRoleStaffs.Where(x => x.IdentityId == userId).Include(x => x.Identity).FirstOrDefault();
            var companyUser = await _coreService.GetCompanyStaff(
                await _userManager.GetUserAsync(User));

            if (companyUser == null)
            {
                return Unauthorized();
            }

            var conversation = await _appDbContext.Conversations
                .Include(x => x.Assignee)
                .Include(x => x.UserProfile)
                .Include(x => x.AdditionalAssignees)
                .ThenInclude(x => x.Assignee.Identity)
                .FirstOrDefaultAsync(
                    x =>
                        x.Id == conversationId
                        && x.CompanyId == companyUser.CompanyId);

            if (conversation == null)
            {
                return BadRequest();
            }

            _logger.LogInformation(
                "[{Method}]: In Company {CompanyId}, User {CompanyUserIdentityId} updating Conversation {ConversationId} Assignment with Request {request}, Current Assignment {ConversationAssignmentViewModel}",
                nameof(UpdateAssignee),
                companyUser.CompanyId,
                companyUser.IdentityId,
                conversationId,
                JsonConvert.SerializeObject(staffAssignmentViewModel),
                JsonConvert.SerializeObject(
                    new
                    {
                        AssigneeId = conversation?.Assignee?.IdentityId,
                        TeamId = conversation?.AssignedTeamId,
                        AdditionalAssigneeIds =
                            conversation?.AdditionalAssignees?.Select(x => x.Assignee?.IdentityId).ToList()
                    }));

            if (staffAssignmentViewModel.AssignmentType.HasValue &&
                staffAssignmentViewModel.AssignmentType != AssignmentType.AdditionalAssignee)
            {
                Staff assignee = null;

                if (!string.IsNullOrEmpty(staffAssignmentViewModel.StaffId))
                {
                    assignee = await _appDbContext.UserRoleStaffs
                        .Include(x => x.Identity)
                        .FirstOrDefaultAsync(
                            x =>
                                x.CompanyId == companyUser.CompanyId
                                && x.IdentityId == staffAssignmentViewModel.StaffId);
                }

                if (staffAssignmentViewModel.TeamId.HasValue)
                {
                    var team = await _appDbContext.CompanyStaffTeams.Where(
                            x => x.CompanyId == companyUser.CompanyId && x.Id == staffAssignmentViewModel.TeamId)
                        .FirstOrDefaultAsync();

                    await _automationService.AutomationAssignment(
                        conversation.UserProfile,
                        new AutomationAction
                        {
                            AutomatedTriggerType = AutomatedTriggerType.Assignment,
                            AssignmentType = AssignmentType.SpecificGroup,
                            AssignedTeamId = team.Id,
                            AssignedTeam = team,
                            TeamAssignmentType = staffAssignmentViewModel.TeamAssignmentType,
                            AssignedStaffId = assignee?.Id,
                            AssignedStaff = assignee,
                        },
                        changedBy: companyUser.IdentityId,
                        isTriggerUpdate: true);

                    conversation = await _appDbContext.Conversations
                        .Where(x => x.Id == conversationId && x.CompanyId == companyUser.CompanyId)
                        .Include(x => x.Assignee)
                        .Include(x => x.UserProfile)
                        .FirstOrDefaultAsync();

                    if (assignee != null &&
                        (assignee.Status == StaffStatus.Away || conversation.AssigneeId != assignee?.Id))
                    {
                        conversation = await _conversationMessageService.ChangeConversationAssignee(
                            conversation,
                            assignee,
                            changedBy: companyUser.IdentityId);
                    }
                }
                else
                {
                    conversation.AssignedTeamId = null;

                    if (conversation.AssigneeId != assignee?.Id)
                    {
                        conversation = await _conversationMessageService.ChangeConversationAssignee(
                            conversation,
                            assignee,
                            changedBy: companyUser.IdentityId);
                    }

                    // Bug Fix - Remove the assigned team from the current conversation but remain the same contact owner should update the modifiedAt
                    conversation.ModifiedAt = DateTime.UtcNow;

                    await _appDbContext.SaveChangesAsync();
                    await _signalRService.SignalROnConversationAssignTeamChanged(conversation);
                }
            }

            _logger.LogInformation(
                "[{Method}] (done): In Company {CompanyId}, User {CompanyUserIdentityId} updated Conversation {ConversationId} Assignment with Request {request}, New Assignment {ConversationAssignmentViewModel}",
                nameof(UpdateAssignee),
                companyUser.CompanyId,
                companyUser.IdentityId,
                conversationId,
                JsonConvert.SerializeObject(staffAssignmentViewModel),
                JsonConvert.SerializeObject(
                    new
                    {
                        AssigneeId = conversation?.Assignee?.IdentityId,
                        TeamId = conversation?.AssignedTeamId,
                        AdditionalAssigneeIds =
                            conversation?.AdditionalAssignees?.Select(x => x.Assignee?.IdentityId).ToList()
                    }));

            var responseVm = _mapper.Map<ConversationAssignResponseViewModel>(conversation);

            return Ok(responseVm);
        }

        /// <summary>
        /// Change conversation collaborator.
        /// </summary>
        /// <param name="conversationId">Targeted ConversationId.</param>
        /// <param name="staffCollaboratorChangeViewModel">Staff Assignment Object.</param>
        /// <returns></returns>
        [HttpPost]
        [Route("v2/conversation/collaborator/{conversationId}")]
        public async Task<ActionResult<ConversationAssignResponseViewModel>> UpdateCollaborator(
            string conversationId,
            [FromBody]
            StaffCollaboratorChangeViewModel staffCollaboratorChangeViewModel)
        {
            // var companyUser = _appDbContext.UserRoleStaffs.Where(x => x.IdentityId == userId).Include(x => x.Identity).FirstOrDefault();
            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            if (companyUser == null)
            {
                return Unauthorized();
            }

            var conversation = await _appDbContext.Conversations
                .Include(x => x.Assignee)
                .Include(x => x.UserProfile)
                .Include(x => x.AdditionalAssignees)
                .ThenInclude(x => x.Assignee.Identity)
                .FirstOrDefaultAsync(
                    x =>
                        x.Id == conversationId
                        && x.CompanyId == companyUser.CompanyId);

            if (conversation == null)
            {
                return BadRequest();
            }

            _logger.LogInformation(
                "[{Method}]: In Company {CompanyId}, User {CompanyUserIdentityId} updating Conversation {ConversationId} Collaborator with Request {request}, Current Assignment {ConversationAssignmentViewModel}",
                nameof(UpdateCollaborator),
                companyUser.CompanyId,
                companyUser.IdentityId,
                conversationId,
                JsonConvert.SerializeObject(staffCollaboratorChangeViewModel),
                JsonConvert.SerializeObject(
                    new
                    {
                        AssigneeId = conversation?.Assignee?.IdentityId,
                        TeamId = conversation?.AssignedTeamId,
                        AdditionalAssigneeIds =
                            conversation?.AdditionalAssignees?.Select(x => x.Assignee?.IdentityId).ToList()
                    }));

            if (staffCollaboratorChangeViewModel.AdditionalAssigneeIds != null
                && (staffCollaboratorChangeViewModel.AdditionalAssigneeIds?.Count > 0
                    || conversation.AdditionalAssignees.Count > 0))
            {
                var toRemove = conversation.AdditionalAssignees
                    .Where(
                        x => !staffCollaboratorChangeViewModel.AdditionalAssigneeIds
                            .Contains(x.Assignee.IdentityId))
                    .ToList();

                await _conversationAssigneeService.RemoveAdditionalAssignees(
                    conversation,
                    toRemove.Select(x => (long) x.AssigneeId).ToList(),
                    companyUser,
                    true);

                var toAdd = staffCollaboratorChangeViewModel.AdditionalAssigneeIds
                    .Where(y => !conversation.AdditionalAssignees
                        .Select(x => x.Assignee.IdentityId)
                        .Contains(y))
                    .ToList();

                await _conversationAssigneeService.AddAdditionalAssignees(
                    conversation,
                    await _appDbContext.UserRoleStaffs
                        .Where(
                            x =>
                                x.CompanyId == companyUser.CompanyId
                                && toAdd.Contains(x.IdentityId))
                        .Select(x => x.Id)
                        .ToListAsync(),
                    companyUser,
                    true);
            }

            _logger.LogInformation(
                "[{Method}]: In Company {CompanyId}, User {CompanyUserIdentityId} updating Conversation {ConversationId} Collaborator with Request {request}, Current Assignment {ConversationAssignmentViewModel}",
                nameof(UpdateCollaborator),
                companyUser.CompanyId,
                companyUser.IdentityId,
                conversationId,
                JsonConvert.SerializeObject(staffCollaboratorChangeViewModel),
                JsonConvert.SerializeObject(
                    new
                    {
                        AssigneeId = conversation?.Assignee?.IdentityId,
                        TeamId = conversation?.AssignedTeamId,
                        AdditionalAssigneeIds =
                            conversation?.AdditionalAssignees?.Select(x => x.Assignee?.IdentityId).ToList()
                    }));

            var responseVm = _mapper.Map<ConversationAssignResponseViewModel>(conversation);

            return Ok(responseVm);
        }

        /// <summary>
        /// Update conversation status.
        /// </summary>
        /// <param name="conversationId">Targeted ConversationId.</param>
        /// <param name="statusView">Conversation Status Update Object.</param>
        /// <returns></returns>
        [HttpPost]
        [Route("Conversation/Status/{conversationId}")]
        public async Task<ActionResult<ConversationStatusResponseViewModel>> UpdateStatus(
            string conversationId,
            [FromBody]
            StatusViewModel statusView)
        {
            var companyUser = await _coreService.GetCompanyStaff(
                await _userManager.GetUserAsync(User));

            if (companyUser == null)
            {
                return BadRequest();
            }

            try
            {
                var conversation = await _conversationMessageService.ChangeConversationStatus(
                    conversationId,
                    companyUser.IdentityId,
                    statusView);

                var responseVm = _mapper.Map<ConversationStatusResponseViewModel>(conversation);

                return Ok(responseVm);
            }
            catch (Exception ex)
            {
                return NotFound(
                    new ResponseViewModel
                    {
                        message = ex.Message
                    });
            }
        }

        /// <summary>
        /// Mark as unread.
        /// </summary>
        /// <param name="conversationId">Targeted ConversationId.</param>
        /// <returns></returns>
        [HttpPost]
        [Route("Conversation/Unread/{conversationId}")]
        public async Task<ActionResult<ConversationStatusResponseViewModel>> UnreadConversation(string conversationId)
        {
            var companyUser = await _coreService.GetCompanyStaff(
                await _userManager.GetUserAsync(User));

            if (companyUser == null)
            {
                return BadRequest();
            }

            try
            {
                // var conversation = await _appDbContext.Conversations.Where(x => x.Id == conversationId && x.CompanyId == companyUser.CompanyId).Include(x => x.UserProfile).Include(x => x.Assignee.Identity).FirstOrDefaultAsync();
                // conversation.UnreadMessageCount = 1;
                // await _appDbContext.SaveChangesAsync();
                var conversation = await _conversationMessageService.MarkConversationAsUnread(
                    conversationId,
                    companyUser);

                var responseVm = _mapper.Map<ConversationStatusResponseViewModel>(conversation);
                return Ok(responseVm);
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[{MethodName} endpoint] Error marking unread for conversation {ConversationId}: {ExceptionMessage}",
                    nameof(UnreadConversation),
                    conversationId,
                    ex.Message);

                return NotFound(
                    new ResponseViewModel
                    {
                        message = ex.Message
                    });
            }
        }

        /// <summary>
        /// Bookmark the conversation.
        /// </summary>
        /// <param name="conversationId">Targeted ConversationId.</param>
        /// <param name="bookmark"><b>true</b> or. <b>false</b></param>
        /// <returns></returns>
        [HttpPost]
        [Route("Conversation/Bookmark/{conversationId}")]
        public async Task<ActionResult<ConversationStatusResponseViewModel>> BookmarkConversation(
            string conversationId,
            [FromQuery(Name = "bookmark")]
            bool bookmark)
        {
            var companyUser = await _coreService.GetCompanyStaff(
                await _userManager.GetUserAsync(User));

            if (companyUser == null)
            {
                return Unauthorized();
            }

            try
            {
                var conversation = await _appDbContext.Conversations
                    .Include(x => x.UserProfile)
                    .Include(x => x.Assignee.Identity)
                    .Include(x => x.ConversationBookmarks)
                    .FirstOrDefaultAsync(
                        x =>
                            x.Id == conversationId
                            && x.CompanyId == companyUser.CompanyId);

                if (bookmark
                    && !conversation.ConversationBookmarks.Any(x => x.StaffId == companyUser.Id))
                {
                    var conversationBookmark = new ConversationBookmark()
                    {
                        ConversationId = conversation.Id,
                        StaffId = companyUser.Id
                    };

                    conversation.ConversationBookmarks.Add(conversationBookmark);
                }
                else if (!bookmark
                         && conversation.ConversationBookmarks.Any(x => x.StaffId == companyUser.Id))
                {
                    await _appDbContext.ConversationBookmarks
                        .Where(
                            x =>
                                x.ConversationId == conversation.Id
                                && x.StaffId == companyUser.Id)
                        .ExecuteDeleteAsync();
                }

                await _appDbContext.SaveChangesAsync();
                conversation.IsBookmarked = bookmark;

                var responseVm = _mapper.Map<ConversationStatusResponseViewModel>(conversation);

                // become personal bookmark
                await _signalRService.SignalROnConversationBookmarkStatusChanged(companyUser.IdentityId, conversation);
                await _cacheManagerService.DeleteCacheWithConstantKeyAsync($":1:conversation:{conversationId}");

                return Ok(responseVm);
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[{MethodName} endpoint] Error toggling bookmark {BookmarkToggle} conversation {ConversationId}: {ExceptionMessage}",
                    nameof(BookmarkConversation),
                    bookmark,
                    conversationId,
                    ex.Message);

                return NotFound(
                    new ResponseViewModel
                    {
                        message = ex.Message
                    });
            }
        }

        /// <summary>
        /// Get Conversation Remark.
        /// </summary>
        /// <param name="conversationId">Targeted ConversationId.</param>
        /// <param name="offset">for pagination.</param>
        /// <param name="limit">for pagination.</param>
        /// <returns></returns>
        [Obsolete("Use AuditHub instead")]
        [HttpGet]
        [Route("Conversation/Remark/{conversationId}")]
        public async Task<ActionResult<List<RemarkResponse>>> GetRemarks(
            string conversationId,
            [FromQuery(Name = "offset")]
            int offset = 0,
            [FromQuery(Name = "limit")]
            int limit = 10)
        {
            var companyUser = await _coreService.GetCompanyStaff(
                await _userManager.GetUserAsync(User));

            if (companyUser == null)
            {
                return Unauthorized();
            }

            var userProfileId = await _appDbContext.Conversations
                .Where(
                    x =>
                        x.Id == conversationId
                        && x.CompanyId == companyUser.CompanyId)
                .Select(x => x.UserProfileId)
                .FirstOrDefaultAsync();

            if (userProfileId == null)
            {
                return NotFound(
                    new ResponseViewModel
                    {
                        message = "conversation not found"
                    });
            }

            var responseVm = await _auditHubAuditLogService.GetUserProfileAuditLogsAsync(
                companyUser.CompanyId,
                userProfileId,
                offset,
                limit);

            return Ok(responseVm);
        }

        /// <summary>
        /// Send Note to Conversation.
        /// </summary>
        /// <param name="conversationId">Targeted ConversationId.</param>
        /// <param name="conversationNoteViewModel">Send Conversation Note Request.</param>
        /// <returns></returns>
        [HttpPost]
        [Route("Conversation/Note/{conversationId}")]
        public async Task<ActionResult<ConversationMessageResponseViewModel>> AddNote(
            string conversationId,
            [FromForm]
            ConversationNoteViewModel conversationNoteViewModel)
        {
            var companyUser = await _coreService.GetCompanyStaff(
                await _userManager.GetUserAsync(User));

            if (companyUser == null)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        message = "error"
                    });
            }

            if (!ModelState.IsValid)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        message = "Incorrect format"
                    });
            }

            try
            {
                var conversationMessage = _mapper.Map<ConversationMessage>(conversationNoteViewModel);

                conversationMessage = await _conversationMessageService.SendConversationNote(
                    companyUser.CompanyId,
                    conversationId,
                    companyUser.IdentityId,
                    conversationMessage,
                    conversationNoteViewModel);

                var conversationMessageVm = _mapper.Map<ConversationMessageResponseViewModel>(conversationMessage);

                var conversationLastChannelModel = await _appDbContext.Conversations.Select(
                    x => new
                    {
                        Id = x.Id,
                        LastMessageChannel = x.LastMessageChannel,
                        LastChannelIdentityId = x.LastChannelIdentityId
                    }).FirstOrDefaultAsync(x => x.Id == conversationId);

                conversationMessageVm.Metadata.Add(
                    "conversationLastMessageChannel",
                    conversationLastChannelModel.LastMessageChannel);
                conversationMessageVm.Metadata.Add(
                    "conversationLastChannelIdentityId",
                    conversationLastChannelModel.LastChannelIdentityId);

                return Ok(conversationMessageVm);
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[{MethodName} endpoint] Error adding note to conversation {ConversationId}: {ExceptionMessage}",
                    nameof(AddNote),
                    conversationId,
                    ex.Message);

                return BadRequest(
                    new ResponseViewModel
                    {
                        message = ex.Message
                    });
            }
        }

        /// <summary>
        /// Get conversations with filter.
        /// </summary>
        /// <param name="assignedTo"><b>all</b>: List all manageable conversations (For <b>Admin</b> and <b>TeamAdmin</b> role only)<br />
        /// <b>unassigned</b>: List all unassigned conversations (For <b>admin</b> can see all unassigned chat; <b>TeamAdmin</b> and <b>Staff</b> can see unassigned chat under their teams)<br />
        /// <b>mentioned</b>: List all conversations that mentioned you<br />
        /// <b>collaborator</b>: List all conversations that you are collaborator<br />
        /// <b>team</b>: List all team conversations or team unassigned chat, if <b>isTeamUnassigned</b> = true then only return the team unassigned chat<br />
        /// <b>{staffId}</b>: List all conversations assigned to this user like:. <b>77b68a4e-d1a5-4433-a161-a768c06a6988</b></param>
        /// <param name="offset">For pagination.</param>
        /// <param name="limit">For pagination.</param>
        /// <param name="status">[ <b>all, open, pending, closed, scheduled</b> ].</param>
        /// <param name="channels">General channel name like: <b>whatsapp,whatsapp360dialog,facebook</b> ;Separated by. <b>,</b></param>
        /// <param name="afterUpdatedAt">Get conversations that updated time is after this value (UTC).</param>
        /// <param name="afterModifiedAt">Get conversations that modified time is after this value (UTC).</param>
        /// <param name="channelIds">Specify the channel id like:<br />
        /// Twilio: AC1fc368e79a37f6a9e87657dd3d75a2e5;whatsapp:+8526452244<br />
        /// 360Dialog: 175.
        /// </param>
        /// <param name="tags">Label filter.</param>
        /// <param name="teamId">TeamId, assignedTo = team.</param>
        /// <param name="isTeamUnassigned">Show only unassigned chats.</param>
        /// <param name="isUnread">Show only is unread chats.</param>
        /// <param name="orderBy">Order by. <b>asc or desc</b></param>
        /// <returns></returns>
        [HttpGet]
        [Route("Conversations/{assignedTo}")]
        public async Task<ActionResult<List<ConversationNoCompanyResponseViewModel>>> GetConversations(
            string assignedTo = "all",
            [FromQuery(Name = "offset")]
            int offset = 0,
            [FromQuery(Name = "limit")]
            int limit = 10,
            [FromQuery(Name = "status")]
            string status = "open",
            [FromQuery(Name = "channel")]
            string channels = null,
            [FromQuery(Name = "afterUpdatedAt")]
            DateTime? afterUpdatedAt = null,
            [FromQuery(Name = "afterModifiedAt")]
            DateTime? afterModifiedAt = null,
            [FromQuery(Name = "channelIds")]
            string channelIds = null,
            [FromQuery(Name = "tags")]
            string tags = null,
            [FromQuery(Name = "teamId")]
            long? teamId = null,
            [FromQuery(Name = "isTeamUnassigned")]
            bool? isTeamUnassigned = null,
            [FromQuery(Name = "isUnread")]
            bool? isUnread = null,
            [FromQuery(Name = "orderBy")]
            string orderBy = "desc")
        {
            var companyUser = await _coreService.GetCompanyStaff(
                await _userManager.GetUserAsync(User));

            if (companyUser == null)
            {
                return Unauthorized();
            }

            var formattedConversations = await _conversationMessageService.GetFormattedConversations(
                companyUser,
                assignedTo,
                offset,
                limit,
                status,
                channels,
                afterUpdatedAt,
                afterModifiedAt,
                channelIds,
                tags,
                teamId,
                isTeamUnassigned,
                isUnread,
                orderBy);

            await MaskConversationWithCountViewModelAsync(
                formattedConversations,
                companyUser.CompanyId,
                companyUser.RoleType);

            return Ok(formattedConversations.Data);
        }

        /// <summary>
        /// Get conversations with filter.
        /// </summary>
        /// <param name="assignedTo"><b>all</b>: List all manageable conversations (For <b>Admin</b> and <b>TeamAdmin</b> role only)<br />
        /// <b>unassigned</b>: List all unassigned conversations (For <b>admin</b> can see all unassigned chat; <b>TeamAdmin</b> and <b>Staff</b> can see unassigned chat under their teams)<br />
        /// <b>mentioned</b>: List all conversations that mentioned you<br />
        /// <b>collaborator</b>: List all conversations that you are collaborator<br />
        /// <b>team</b>: List all team conversations or team unassigned chat, if <b>isTeamUnassigned</b> = true then only return the team unassigned chat<br />
        /// <b>{staffId}</b>: List all conversations assigned to this user like:. <b>77b68a4e-d1a5-4433-a161-a768c06a6988</b></param>
        /// <param name="offset">For pagination.</param>
        /// <param name="limit">For pagination.</param>
        /// <param name="status">[ <b>all, open, pending, closed, scheduled</b> ].</param>
        /// <param name="channels">General channel name like: <b>whatsapp,whatsapp360dialog,facebook</b> ;Separated by. <b>,</b></param>
        /// <param name="afterUpdatedAt">Get conversations that updated time is after this value (UTC).</param>
        /// <param name="afterModifiedAt">Get conversations that modified time is after this value (UTC).</param>
        /// <param name="channelIds">Specify the channel id like:<br />
        /// Twilio: AC1fc368e79a37f6a9e87657dd3d75a2e5;whatsapp:+8526452244<br />
        /// 360Dialog: 175.
        /// </param>
        /// <param name="tags">Label filter</param>
        /// <param name="teamId">TeamId, assignedTo = team</param>
        /// <param name="isTeamUnassigned">Show only unassigned chats</param>
        /// <param name="isUnread">Show only is unread chats</param>
        /// <param name="orderBy">Order by <b>asc or desc</b></param>
        /// <param name="isAssigned">(Optional) Show assigned or unassigned chats</param>
        /// <param name="isCollaborator">(Optional) Show collaborating chats</param>
        /// <param name="behaviourVersion"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("v3/conversations/{assignedTo}")]
        public async Task<ActionResult<ConversationWithCountViewModel>> GetConversationsV3(
            string assignedTo = "all",
            [FromQuery(Name = "offset")]
            int offset = 0,
            [FromQuery(Name = "limit")]
            int limit = 10,
            [FromQuery(Name = "status")]
            string status = "open",
            [FromQuery(Name = "channel")]
            string channels = null,
            [FromQuery(Name = "afterUpdatedAt")]
            DateTime? afterUpdatedAt = null,
            [FromQuery(Name = "afterModifiedAt")]
            DateTime? afterModifiedAt = null,
            [FromQuery(Name = "channelIds")]
            string channelIds = null,
            [FromQuery(Name = "tags")]
            string tags = null,
            [FromQuery(Name = "teamId")]
            long? teamId = null,
            [FromQuery(Name = "isTeamUnassigned")]
            bool? isTeamUnassigned = null,
            [FromQuery(Name = "isUnread")]
            bool? isUnread = null,
            [FromQuery(Name = "orderBy")]
            string orderBy = "desc",
            [FromQuery(Name = "isAssigned")]
            bool? isAssigned = null,
            [FromQuery(Name = "isCollaborator")]
            bool? isCollaborator = null,
            [FromQuery(Name = "behaviourVersion")]
            string behaviourVersion = "2")
        {
            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            if (companyUser == null)
            {
                return Unauthorized();
            }

            var formattedConversations = await _conversationMessageService.GetFormattedConversations(
                companyUser,
                assignedTo,
                offset,
                limit,
                status,
                channels,
                afterUpdatedAt,
                afterModifiedAt,
                channelIds,
                tags,
                teamId,
                isTeamUnassigned,
                isUnread,
                orderBy,
                behaviourVersion,
                isAssigned,
                isCollaborator);

            await MaskConversationWithCountViewModelAsync(
                formattedConversations,
                companyUser.CompanyId,
                companyUser.RoleType);

            return Ok(formattedConversations);
        }

        private async Task MaskConversationWithCountViewModelAsync(
            ConversationWithCountViewModel viewModel,
            string companyId,
            StaffUserRole role)
        {
            if (!await _piiMaskingService.IsConfiguredAsync(companyId)
                || !Enum.TryParse(role.ToString(), out MaskingRoles maskingRole))
            {
                return;
            }

            foreach (var message in viewModel.Data.SelectMany(x => x.LastMessage))
            {
                if (message is not null
                    && !message.IsSentFromSleekflow
                    && message.MessageContent is not null)
                {
                    message.MessageContent = await _piiMaskingService.MaskPiiIfEnabledAsync(
                    companyId,
                    message.MessageContent,
                    MaskingLocations.IncomingMessage,
                    maskingRole);
                }
            }
        }

        /// <summary>
        /// Get conversation detail.
        /// </summary>
        /// <param name="conversationId">Targeted Conversation Id.</param>
        /// <param name="cancellationToken">Cancellation Token</param>
        /// <returns></returns>
        [HttpGet]
        [Route("Conversation/{conversationId}")]
        [ServiceFilter(typeof(ReadOnlyEndpointAttribute))]
        public async Task<ActionResult<ConversationNoCompanyResponseViewModel>> GetConversations(
            string conversationId,
            CancellationToken cancellationToken = default)
        {
            var applicationUser = await _sleekflowUserService.GetUserAsync(User);
            var companyUser = await _coreService.GetCompanyStaff(applicationUser);

            if (companyUser == null)
            {
                return Unauthorized();
            }

            try
            {
                _logger.LogInformation(
                    "[GetConversation]: ConversationId {ConversationId} CompanyId {CompanyId} StaffId {StaffId}",
                    conversationId,
                    companyUser.CompanyId,
                    companyUser.Id);
            }
            catch (Exception e)
            {
                // ignored
            }

            var response = await GetConversationData(conversationId, companyUser, cancellationToken);

            await MaskConversationNoCompanyResponseViewModelAsync(response, companyUser.CompanyId, companyUser.RoleType);

            try
            {
                return Ok(response);
            }
            catch (TaskCanceledException)
            {
                throw;
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[{MethodName} endpoint] Error getting detail for conversation {ConversationId}: {ExceptionMessage}",
                    nameof(GetConversations),
                    conversationId,
                    ex.Message);

                return BadRequest(ex.Message);
            }
        }

        private async Task MaskConversationNoCompanyResponseViewModelAsync(
            ConversationNoCompanyResponseViewModel vm,
            string companyId,
            StaffUserRole role)
        {
            if (vm is null
                || !await _piiMaskingService.IsConfiguredAsync(companyId)
                || !Enum.TryParse(role.ToString(), out MaskingRoles maskingRole))
            {
                return;
            }

            if (vm.LastMessage is not null)
            {
                foreach (var message in vm.LastMessage)
                {
                    if (message is not null
                        && !message.IsSentFromSleekflow
                        && message.MessageContent is not null)
                    {
                        message.MessageContent = await _piiMaskingService.MaskPiiIfEnabledAsync(
                            companyId,
                            message.MessageContent,
                            MaskingLocations.IncomingMessage,
                            maskingRole);
                    }
                }
            }

            if (!string.IsNullOrWhiteSpace(vm.UserProfile?.FirstName))
            {
                vm.UserProfile.FirstName = await _piiMaskingService.MaskPiiIfEnabledAsync(
                    companyId,
                    vm.UserProfile.FirstName,
                    MaskingLocations.Contact,
                    maskingRole);
            }

            if (!string.IsNullOrWhiteSpace(vm.UserProfile?.LastName))
            {
                vm.UserProfile.LastName = await _piiMaskingService.MaskPiiIfEnabledAsync(
                    companyId,
                    vm.UserProfile.LastName,
                    MaskingLocations.Contact,
                    maskingRole);
            }

            if (vm.UserProfile?.CustomFields is null)
            {
                return;
            }

            var companyCustomUserProfileFieldIdToType = await _appDbContext.CompanyCustomUserProfileFields
                .Where(x => x.CompanyId == companyId)
                .ToDictionaryAsync(x => x.Id, x => x.Type);

            foreach (var field in vm.UserProfile.CustomFields)
            {
                var type = companyCustomUserProfileFieldIdToType.GetValueOrDefault(field.CompanyDefinedFieldId);

                if (field.Value is null
                    || (type != FieldDataType.SingleLineText
                        && type != FieldDataType.MultiLineText
                        && type != FieldDataType.Number
                        && type != FieldDataType.PhoneNumber
                        && type != FieldDataType.Email))
                {
                    continue;
                }

                field.Value = await _piiMaskingService.MaskPiiIfEnabledAsync(
                    companyId,
                    field.Value,
                    MaskingLocations.Contact,
                    maskingRole);
            }
        }

        private async Task<ConversationNoCompanyResponseViewModel> GetConversationData(
            string conversationId,
            Staff companyUser,
            CancellationToken cancellationToken = default)
        {
            // var adminCacheKey = $"conversation_details_{companyUser.CompanyId}:{conversationId}";
            var adminCacheKeyPattern = new ConversationDetailsAdminCacheKeyPattern(companyUser.CompanyId, conversationId);
            var personalCacheKeyPattern = new ConversationDetailsPersonalCacheKeyPattern(companyUser.Id, conversationId);

            var personalCacheKey = $"conversation_details_{companyUser.Id}:{conversationId}";

            // Admin can share the cache
            if (companyUser.RoleType == StaffUserRole.Admin)
            {
                var adminCacheData = await _cacheManagerService.GetCacheAsync(adminCacheKeyPattern);

                if (!string.IsNullOrEmpty(adminCacheData))
                {
                    return JsonConvert.DeserializeObject<ConversationNoCompanyResponseViewModel>(adminCacheData);
                }
            }

            var personalCacheData = await _cacheManagerService.GetCacheAsync(personalCacheKeyPattern);

            if (!string.IsNullOrEmpty(personalCacheData))
            {
                return JsonConvert.DeserializeObject<ConversationNoCompanyResponseViewModel>(personalCacheData);
            }

            try
            {
                var conversation =
                    await _conversationService.GetConversationDetails(
                        companyUser.CompanyId,
                        companyUser.Id,
                        companyUser.RoleType,
                        conversationId,
                        cancellationToken);

                if (companyUser.RoleType == StaffUserRole.Admin)
                {
                    await _cacheManagerService.SaveCacheAsync(
                        adminCacheKeyPattern,
                        conversation);
                }
                else
                {
                    await _cacheManagerService.SaveCacheAsync(
                        personalCacheKeyPattern,
                        conversation);
                }

                return conversation;
            }
            catch (EntryPointNotFoundException notFoundEx)
            {
                throw new Exception("not found");
            }
        }

        /// <summary>
        /// Search message in a conversation.
        /// </summary>
        /// <param name="conversationId">Target Conversation Id.</param>
        /// <param name="keywords">Message Keyword.</param>
        /// <param name="offset">For pagination.</param>
        /// <param name="limit">For pagination.</param>
        /// <returns></returns>
        [HttpGet]
        [Route("Conversation/Search/Message/{conversationId}")]
        public async Task<ActionResult<ConversationMessageResponseViewModel>> SearchConversationMessages(
            string conversationId,
            [FromQuery(Name = "keywords")]
            string keywords,
            [FromQuery(Name = "offset")]
            int offset = 0,
            [FromQuery(Name = "limit")]
            int limit = 10)
        {
            var companyUser = await _coreService.GetCompanyStaff(
                await _userManager.GetUserAsync(User));

            if (companyUser == null)
            {
                return Unauthorized();
            }

            _logger.LogInformation(
                "SearchConversationMessagesAsync: ConversationId {ConversationId}",
                conversationId);

            var isExistingConversation = await _appDbContext.Conversations
                .AnyAsync(x => x.Id == conversationId);

            if (!isExistingConversation)
            {
                return NotFound(
                    new ResponseViewModel
                    {
                        message = "conversation not found"
                    });
            }

            var conversationMessageQuery =
                from conversationMessages in _appDbContext.ConversationMessages
                where conversationMessages.ConversationId == conversationId &&
                      conversationMessages.CompanyId == companyUser.CompanyId &&
                      conversationMessages.MessageContent.Contains(keywords)
                orderby conversationMessages.Id descending
                select conversationMessages;

            var responseVm = await conversationMessageQuery
                .Include(x => x.UploadedFiles)
                .Include(x => x.EmailFrom)
                .Include(x => x.Sender)
                .Include(x => x.Receiver)
                .Include(x => x.SenderDevice)
                .Include(x => x.ReceiverDevice)
                .Include(x => x.facebookSender)
                .Include(x => x.facebookReceiver)
                .Include(x => x.whatsappSender)
                .Include(x => x.whatsappReceiver)
                .Include(x => x.WebClientSender)
                .Include(x => x.WebClientReceiver)
                .Include(x => x.MessageAssignee.Identity)
                .Include(x => x.WeChatSender)
                .Include(x => x.WeChatReceiver)
                .Include(x => x.LineSender)
                .Include(x => x.LineReceiver)
                .Include(x => x.SMSSender)
                .Include(x => x.SMSReceiver)
                .Include(x => x.ViberSender)
                .Include(x => x.ViberReceiver)
                .Include(x => x.TelegramSender)
                .Include(x => x.TelegramReceiver)
                .Include(x => x.Whatsapp360DialogSender)
                .Include(x => x.Whatsapp360DialogReceiver)
                .Include(x => x.Whatsapp360DialogExtendedMessagePayload)
                .Include(x => x.ExtendedMessagePayload)
                .Skip(offset)
                .Take(limit)
                .ProjectTo<ConversationMessageResponseViewModel>(_mapper.ConfigurationProvider)
                .ToListAsync(HttpContext.RequestAborted);

            await _sleekPayService.AddSleekPayRecord(responseVm);

            return Ok(responseVm);
        }

        /// <summary>
        /// Search message with conversation filter.
        /// </summary>
        /// <param name="assignedTo"><b>all</b>: List all manageable conversations (For <b>Admin</b> and <b>TeamAdmin</b> role only)<br />
        /// <b>unassigned</b>: List all unassigned conversations (For <b>admin</b> can see all unassigned chat; <b>TeamAdmin</b> and <b>Staff</b> can see unassigned chat under their teams)<br />
        /// <b>mentioned</b>: List all conversations that mentioned you<br />
        /// <b>collaborator</b>: List all conversations that you are collaborator<br />
        /// <b>team</b>: List all team conversations or team unassigned chat, if <b>isTeamUnassigned</b> = true then only return the team unassigned chat<br />
        /// <b>{staffId}</b>: List all conversations assigned to this user like:. <b>77b68a4e-d1a5-4433-a161-a768c06a6988</b></param>
        /// <param name="keywords">Message Keyword.</param>
        /// <param name="offset">For pagination.</param>
        /// <param name="limit">For pagination.</param>
        /// <param name="status">[ <b>all, open, pending, closed, scheduled</b> ].</param>
        /// <param name="channels">General channel name like: <b>whatsapp,whatsapp360dialog,facebook</b> ;Separated by. <b>,</b></param>
        /// <param name="channelIds">Specify the channel id like:<br />
        /// Twilio: AC1fc368e79a37f6a9e87657dd3d75a2e5;whatsapp:+8526452244<br />
        /// 360Dialog: 175.
        /// </param>
        /// <returns></returns>
        [HttpGet]
        [Route("v2/Conversation/{assignedTo}/Search/Message")]
        public async Task<ActionResult<List<ConversationNoCompanyResponseViewModel>>> SearchConversationsMessagesV2(
            string assignedTo,
            [FromQuery(Name = "keywords")]
            string keywords,
            [FromQuery(Name = "offset")]
            int offset = 0,
            [FromQuery(Name = "limit")]
            int limit = 10,
            [FromQuery(Name = "status")]
            string status = "open",
            [FromQuery(Name = "channel")]
            string channels = null,
            [FromQuery(Name = "channelIds")]
            string channelIds = null,
            [FromQuery(Name = "teamId")]
            long? teamId = null)
        {
            try
            {
                var companyUser = await _coreService.GetCompanyStaff(
                    await _userManager.GetUserAsync(User));

                if (companyUser == null)
                {
                    return Unauthorized();
                }

                var searchConversationMessages = await _conversationMessageService.SearchConversationMessagesAsync(companyUser, assignedTo, keywords, offset, limit, status, channels, channelIds, teamId);
                return Ok(searchConversationMessages.Data);
            }
            catch (Exception ex)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        message = ex.Message
                    });
            }
        }

        /// <summary>
        /// Search message with conversation filter.
        /// </summary>
        /// <param name="assignedTo"><b>all</b>: List all manageable conversations (For <b>Admin</b> and <b>TeamAdmin</b> role only)<br />
        /// <b>unassigned</b>: List all unassigned conversations (For <b>admin</b> can see all unassigned chat; <b>TeamAdmin</b> and <b>Staff</b> can see unassigned chat under their teams)<br />
        /// <b>mentioned</b>: List all conversations that mentioned you<br />
        /// <b>collaborator</b>: List all conversations that you are collaborator<br />
        /// <b>team</b>: List all team conversations or team unassigned chat, if <b>isTeamUnassigned</b> = true then only return the team unassigned chat<br />
        /// <b>{staffId}</b>: List all conversations assigned to this user like:. <b>77b68a4e-d1a5-4433-a161-a768c06a6988</b></param>
        /// <param name="keywords">Message Keyword.</param>
        /// <param name="offset">For pagination.</param>
        /// <param name="limit">For pagination.</param>
        /// <param name="status">[ <b>all, open, pending, closed, scheduled</b> ].</param>
        /// <param name="channels">General channel name like: <b>whatsapp,whatsapp360dialog,facebook</b> ;Separated by. <b>,</b></param>
        /// <param name="channelIds">Specify the channel id like:<br />
        /// Twilio: AC1fc368e79a37f6a9e87657dd3d75a2e5;whatsapp:+8526452244<br />
        /// 360Dialog: 175.
        /// </param>
        /// <returns></returns>
        [HttpGet]
        [Route("v3/Conversation/{assignedTo}/Search/Message")]
        public async Task<ActionResult<ConversationWithCountViewModel>> SearchConversationsMessagesV3(
            string assignedTo,
            [FromQuery(Name = "keywords")]
            string keywords,
            [FromQuery(Name = "offset")]
            int offset = 0,
            [FromQuery(Name = "limit")]
            int limit = 10,
            [FromQuery(Name = "status")]
            string status = "open",
            [FromQuery(Name = "channel")]
            string channels = null,
            [FromQuery(Name = "channelIds")]
            string channelIds = null,
            [FromQuery(Name = "teamId")]
            long? teamId = null,
            [FromQuery(Name = "behaviourVersion")]
            string behaviourVersion = "2",
            [FromQuery(Name = "isAllowCache")]
            bool isAllowCache = true)
        {
            try
            {
                var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

                if (companyUser == null)
                {
                    return Unauthorized();
                }

                var searchConversationMessages = await _conversationMessageService.SearchConversationMessagesAsync(companyUser, assignedTo, keywords, offset, limit, status, channels, channelIds, teamId, behaviourVersion, isAllowCache);
                return Ok(searchConversationMessages);
            }
            catch (Exception ex)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        message = ex.Message
                    });
            }
        }

        /// <summary>
        /// Get Message of the Conversation.
        /// </summary>
        /// <param name="conversationId">Targeted ConversationId.</param>
        /// <param name="offset">for pagination.</param>
        /// <param name="limit">for pagination.</param>
        /// <param name="beforeMessageId">[Deprecated] Filter by messageId: list messages before this Id.</param>
        /// <param name="afterMessageId">[Deprecated] Filter by messageId: list messages after this Id.</param>
        /// <param name="afterTimestamp">Filter by message's timestamp: list messages after this timestamp.</param>
        /// <param name="beforeTimestamp">Filter by message's timestamp: list messages before this timestamp.</param>
        /// <param name="channels">General channel name like: <b>whatsapp,whatsapp360dialog,facebook</b> ;Separated by. <b>,</b></param>
        /// <param name="channelIds">Specify the channel id like:<br />
        /// Twilio: AC1fc368e79a37f6a9e87657dd3d75a2e5;whatsapp:+8526452244<br />
        /// 360Dialog: 175.
        /// </param>
        /// <param name="IsFromUser">Show incoming message only.</param>
        /// <param name="isFromImport">Show message only from backup history.</param>
        /// <param name="order">OderBy: <b>asc</b> or. <b>desc</b></param>
        /// <param name="isGetFileOnly">if true will return file message only.</param>
        /// <returns></returns>
        [HttpGet]
        [ServiceFilter(typeof(ReadOnlyEndpointAttribute))]
        [Route("Conversation/Message/{conversationId}")]
        public async Task<ActionResult<List<ConversationMessageResponseViewModel>>> GetConversationMessages(
            string conversationId,
            [FromQuery(Name = "offset")]
            int offset = 0,
            [FromQuery(Name = "limit")]
            int limit = 10,
            [FromQuery(Name = "beforeMessageId")]
            long? beforeMessageId = null,
            [FromQuery(Name = "afterMessageId")]
            long? afterMessageId = null,
            [FromQuery(Name = "afterTimestamp")]
            long? afterTimestamp = null,
            [FromQuery(Name = "beforeTimestamp")]
            long? beforeTimestamp = null,
            [FromQuery(Name = "channel")]
            string channels = null,
            [FromQuery(Name = "channelIds")]
            string channelIds = null,
            [FromQuery(Name = "IsFromUser")]
            bool? IsFromUser = null,
            [FromQuery(Name = "IsFromImport")]
            bool? isFromImport = null,
            [FromQuery(Name = "order")]
            string order = "desc",
            [FromQuery(Name = "isGetFileOnly")]
            bool isGetFileOnly = false)
        {
            var companyUser = await _coreService.GetCompanyStaff(
                await _userManager.GetUserAsync(User));

            if (companyUser == null)
            {
                return Unauthorized();
            }

            var dbContext = _dbContextService.GetDbContext();

            var whatsappIds = new List<string>();
            var twilioSenderIds = new List<string>();
            var whatsapp360dialogDefaultChannelIds = new List<long>();
            var whatsappCloudDefaultChannelIds = new List<string>();

            var rolePermission = await dbContext.CompanyRolePermissions
                .AsNoTracking()
                .FirstOrDefaultAsync(
                    x =>
                        x.CompanyId == companyUser.CompanyId
                        && x.StaffUserRole == companyUser.RoleType);

            if (rolePermission != null &&
                rolePermission.Permission.IsShowDefaultChannelMessagesOnly)
            {
                var associatedTeams = await dbContext.CompanyStaffTeams
                    .Where(x => x.Members.Any(y => y.StaffId == companyUser.Id))
                    .AsNoTracking()
                    .ToListAsync(HttpContext.RequestAborted);

                foreach (var associatedTeam in associatedTeams)
                {
                    if (associatedTeam.DefaultChannels?.Count > 0)
                    {
                        foreach (var defaultChannel in associatedTeam.DefaultChannels
                                     .Where(
                                         x =>
                                             x.channel != ChannelTypes.Whatsapp360Dialog
                                             && x.channel != ChannelTypes.WhatsappCloudApi))
                        {
                            foreach (var id in defaultChannel.ids)
                            {
                                var twilioInstance = id.Split(";", StringSplitOptions.RemoveEmptyEntries);
                                whatsappIds.Add(twilioInstance[0]);
                                if (twilioInstance.Count() > 1)
                                {
                                    twilioSenderIds.Add(twilioInstance[1].Replace(": ", ":+"));
                                }
                            }
                        }

                        foreach (var defaultChannel in associatedTeam.DefaultChannels
                                     .Where(x => x.channel == ChannelTypes.Whatsapp360Dialog))
                        {
                            foreach (var channelId in defaultChannel.ids)
                            {
                                var validLong = long.TryParse(channelId, out var whatsapp360dialogChannelId);
                                if (validLong)
                                {
                                    whatsapp360dialogDefaultChannelIds.Add(whatsapp360dialogChannelId);
                                }
                            }
                        }

                        foreach (var defaultChannel in associatedTeam.DefaultChannels
                                     .Where(x => x.channel == ChannelTypes.WhatsappCloudApi))
                        {
                            foreach (var channelId in defaultChannel.ids)
                            {
                                whatsappCloudDefaultChannelIds.Add(channelId);
                            }
                        }
                    }
                }
            }

            if (afterTimestamp.HasValue)
            {
                order = "asc";
            }

            var conversationMessageCacheKeyPattern = new
                ConversationMessageCacheKeyPattern(
                    companyUser.Id,
                    conversationId,
                    offset,
                    limit,
                    beforeMessageId,
                    afterMessageId,
                    afterTimestamp,
                    beforeTimestamp,
                    channels,
                    channelIds,
                    IsFromUser,
                    order,
                    isGetFileOnly);

            var data = await _cacheManagerService.GetCacheAsync(conversationMessageCacheKeyPattern);

            if ((beforeTimestamp.HasValue
                 || afterTimestamp.HasValue
                 || !IsFromUser.HasValue)
                && !string.IsNullOrEmpty(data))
            {
                var result = JsonConvert.DeserializeObject<List<ConversationMessageResponseViewModel>>(data);
                return Ok(result);
            }

            try
            {
                _logger.LogInformation(
                    "Company {CompanyId} user {CompanyUserId} GetConversationMessages: ConversationId {ConversationId}",
                    companyUser.CompanyId,
                    companyUser.Id,
                    conversationId);

                var resultQueryable = dbContext.ConversationMessages.Where(
                    conversationMessages => conversationMessages.ConversationId == conversationId &&
                                            conversationMessages.CompanyId == companyUser.CompanyId);

                if (beforeTimestamp.HasValue)
                {
                    resultQueryable = resultQueryable.Where(
                        conversationMessages => conversationMessages.Timestamp <= beforeTimestamp);
                }

                if (afterTimestamp.HasValue)
                {
                    resultQueryable = resultQueryable.Where(
                        conversationMessages => conversationMessages.Timestamp >= afterTimestamp);
                }

                var channelList = new List<string>();

                if (!string.IsNullOrEmpty(channels))
                {
                    channelList = channels
                        .Split(",")
                        .ToList();
                }

                var channelIdList = new List<string>();

                if (!string.IsNullOrEmpty(channelIds))
                {
                    channelIdList = channelIds
                        .Split(",")
                        .ToList();
                }

                HashSet<long> filteredMessagedIds;

                if (whatsappIds.Count == 0
                    && whatsappCloudDefaultChannelIds.Count == 0
                    && whatsapp360dialogDefaultChannelIds.Count == 0
                    && twilioSenderIds.Count == 0)
                {
                    filteredMessagedIds = (await resultQueryable
                            .Select(x => x.Id)
                            .ToListAsync(HttpContext.RequestAborted))
                        .ToHashSet();
                }
                else
                {
                    filteredMessagedIds = await _conversationService.FilterConversationMessage(
                        resultQueryable,
                        whatsappIds,
                        whatsapp360dialogDefaultChannelIds,
                        whatsappCloudDefaultChannelIds,
                        twilioSenderIds);
                }


                var defaultChannelList = new string[]
                {
                    ChannelTypes.WhatsappTwilio,
                    ChannelTypes.Whatsapp360Dialog,
                    ChannelTypes.WhatsappCloudApi,
                    ChannelTypes.Instagram,
                    ChannelTypes.Facebook
                };

                if (channelIdList.Count > 0
                    && defaultChannelList.Any(x => channelList.Contains(x)))
                {
                    try
                    {
                        var newWhatsappId = new List<string>();
                        var newInstanceSender = new List<string>();
                        var newWhatsapp360dialogDefaultChannelIds = new List<long>();
                        var newWhatsappCloudApiDefaultChannelIds = new List<string>();

                        if (channelList.Contains(ChannelTypes.WhatsappTwilio))
                        {
                            foreach (var myChannelId in channelIdList)
                            {
                                var twilioInstance = myChannelId.Split(";", StringSplitOptions.RemoveEmptyEntries);
                                newWhatsappId.Add(twilioInstance[0]);
                                if (twilioInstance.Count() > 1)
                                {
                                    newInstanceSender.Add(twilioInstance[1].Replace(": ", ":+"));
                                }
                            }
                        }

                        if (channelList.Contains(ChannelTypes.Whatsapp360Dialog))
                        {
                            var whatsapp360DialogChannelId = channelIdList
                                .Select(x => long.TryParse(x, out var id) ? id : 0)
                                .Where(x => x != 0)
                                .Distinct()
                                .ToList();

                            newWhatsapp360dialogDefaultChannelIds.AddRange(whatsapp360DialogChannelId);
                        }

                        if (channelList.Contains(ChannelTypes.WhatsappCloudApi))
                        {
                            var whatsappCloudApiChannelId = channelIdList
                                .Distinct()
                                .ToList();

                            newWhatsappCloudApiDefaultChannelIds.AddRange(whatsappCloudApiChannelId);
                        }

                        if (channelList.Contains(ChannelTypes.Instagram))
                        {
                            var instagramIds = channelIdList
                                .Distinct()
                                .ToList();

                            newWhatsappId.AddRange(instagramIds);
                        }

                        if (channelList.Contains(ChannelTypes.Facebook))
                        {
                            var facebookIds = channelIdList
                                .Distinct()
                                .ToList();

                            newWhatsappId.AddRange(facebookIds);
                        }

                        var newFilteredMessagedIds = await _conversationService.FilterConversationMessage(
                            resultQueryable,
                            newWhatsappId,
                            newWhatsapp360dialogDefaultChannelIds,
                            newWhatsappCloudApiDefaultChannelIds,
                            newInstanceSender);

                        // PDD-4977, PDD-4704
                        // if(newFilteredMessagedIds.Count > 0)
                        filteredMessagedIds.IntersectWith(newFilteredMessagedIds);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(
                            ex,
                            "Unable to parse TargetedChannelModel {ExceptionMessage}",
                            ex.Message);
                    }
                }
                else
                {
                    if (channelList.Contains(ChannelTypes.Wechat))
                    {
                        filteredMessagedIds = (await resultQueryable
                                .Where(x => x.Channel == ChannelTypes.Wechat)
                                .Select(x => x.Id)
                                .ToListAsync(HttpContext.RequestAborted))
                            .ToHashSet();
                    }
                }

                var resultConversationMessagesQ = dbContext.ConversationMessages
                    .Where(x => filteredMessagedIds.Contains(x.Id));

                if (IsFromUser.HasValue)
                {
                    resultConversationMessagesQ = resultConversationMessagesQ
                        .Where(x => x.IsSentFromSleekflow == !IsFromUser.Value);
                }

                if (isFromImport.HasValue)
                {
                    resultConversationMessagesQ = resultConversationMessagesQ
                        .Where(x => x.IsFromImport == isFromImport.Value);
                }

                resultConversationMessagesQ = resultConversationMessagesQ
                    .OrderByDescending(x => x.Timestamp);

                switch (order)
                {
                    case "asc":
                        resultConversationMessagesQ = resultConversationMessagesQ
                            .OrderBy(x => x.Timestamp);

                        break;
                }

                var response = await resultConversationMessagesQ
                    .WhereIf(
                        isGetFileOnly,
                        message => message.MessageType == "file")
                    .Skip(offset)
                    .Take(limit)
                    .AsNoTracking()
                    .ProjectTo<ConversationMessageResponseViewModel>(_mapper.ConfigurationProvider)
                    .ToListAsync(HttpContext.RequestAborted);

                await _sleekPayService.AddSleekPayRecord(response);

                // reset unread message
                if (offset == 0 &&
                    (await dbContext.Conversations
                         .AnyAsync(
                             x =>
                                 x.Id == conversationId
                                 && x.UnreadMessageCount > 0)
                     || await dbContext.ConversationUnreadRecords
                         .AnyAsync(
                             x =>
                                 x.CompanyId == companyUser.CompanyId
                                 && x.ConversationId == conversationId
                                 && x.StaffId == companyUser.Id
                                 && x.NotificationDeliveryStatus != NotificationDeliveryStatus.Read)))
                {
                    BackgroundJob.Enqueue<IConversationMessageService>(
                        x => x.ReadConversation(
                            conversationId,
                            companyUser,
                            companyUser.CompanyId == "21da963c-a525-478d-8ec7-d473791a482e"));
                }

                await MaskConversationMessageResponseViewModelsAsync(
                    response,
                    companyUser.CompanyId,
                    companyUser.RoleType);

                if (beforeTimestamp.HasValue
                    || afterTimestamp.HasValue
                    || !IsFromUser.HasValue)
                {
                    await _conversationMessageService.AddConversationMessageCache(
                        conversationId,
                        conversationMessageCacheKeyPattern,
                        response);
                }

                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[{MethodName} endpoint] error: {ExceptionMessage}",
                    nameof(GetConversationMessages),
                    ex.Message);

                return BadRequest(
                    new ResponseViewModel
                    {
                        message = "error"
                    });
            }
        }

        private async Task MaskConversationMessageResponseViewModelsAsync(
            List<ConversationMessageResponseViewModel> viewModels,
            string companyId,
            StaffUserRole role)
        {
            if (!await _piiMaskingService.IsConfiguredAsync(companyId)
                || !Enum.TryParse(role.ToString(), out MaskingRoles maskingRole))
            {
                return;
            }

            foreach (var viewModel in viewModels)
            {
                if (!viewModel.IsSentFromSleekflow && viewModel.MessageContent is not null)
                {
                    viewModel.MessageContent = await _piiMaskingService.MaskPiiIfEnabledAsync(
                        companyId,
                        viewModel.MessageContent,
                        MaskingLocations.IncomingMessage,
                        maskingRole);
                }
            }
        }

        /// <summary>
        /// Mark conversation as <b>Read</b> status [Not personal].
        /// </summary>
        /// <param name="conversationId"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("Conversation/Read/{conversationId}")]
        public async Task<ActionResult<ResponseViewModel>> ReadConversation(string conversationId)
        {
            var companyUser = await _coreService.GetCompanyStaff(
                await _userManager.GetUserAsync(User));

            await _conversationMessageService.ReadConversation(conversationId, companyUser);

            return Ok(
                new ResponseViewModel
                {
                    message = "success"
                });
        }

        /// <summary>
        /// Get conversation messages by timestamp (mainly used to get missing messages after signalr reconnected).
        /// </summary>
        /// <param name="offset">For pagination.</param>
        /// <param name="limit">For pagination.</param>
        /// <param name="afterTimestamp">Filter by message's timestamp: list messages after this timestamp.</param>
        /// <param name="beforeTimestamp">Filter by message's timestamp: list messages before this timestamp.</param>
        /// <param name="channels">General channel name like: <b>whatsapp,whatsapp360dialog,facebook</b> ;Separated by. <b>,</b></param>
        /// <param name="channelIds">Specify the channel id like:<br />
        /// Twilio: AC1fc368e79a37f6a9e87657dd3d75a2e5;whatsapp:+8526452244<br />
        /// 360Dialog: 175.
        /// </param>
        /// <param name="IsFromUser">Show incoming message only.</param>
        /// <returns></returns>
        [HttpGet]
        [Route("v2/Conversation/Message")]
        [ServiceFilter(typeof(ReadOnlyEndpointAttribute))]
        public async Task<ActionResult<MissingMessageViewModel>> GetMessagesV2(
            [FromQuery(Name = "offset")]
            int offset = 0,
            [FromQuery(Name = "limit")]
            int limit = 10,
            [FromQuery(Name = "afterTimestamp")]
            long? afterTimestamp = null,
            [FromQuery(Name = "beforeTimestamp")]
            long? beforeTimestamp = null,
            [FromQuery(Name = "channel")]
            string channels = null,
            [FromQuery(Name = "channelIds")]
            string channelIds = null,
            [FromQuery(Name = "IsFromUser")]
            bool? IsFromUser = null)
        {
            if (beforeTimestamp > 9999999999)
            {
                beforeTimestamp = beforeTimestamp / 1000;
            }

            if (afterTimestamp > 9999999999)
            {
                afterTimestamp = afterTimestamp / 1000;
            }

            var gap = beforeTimestamp - afterTimestamp;

            // Only allow get for maximum 2 hours
            if (gap > 60 * 60 * 72)
            {
                return Ok(new MissingMessageViewModel());
            }

            if (User.Identity is not { IsAuthenticated: true })
            {
                return Ok(new MissingMessageViewModel());
            }

            var applicationUser = await _sleekflowUserService.GetUserAsync(User);
            var companyUser = await _coreService.GetCompanyStaff(applicationUser);

            if (limit > 10)
            {
                limit = 10;
            }

            if (companyUser == null)
            {
                return Ok(new MissingMessageViewModel());
            }

            var signalrMessageCacheKeyPattern = new SignalrMessageCacheKeyPattern(companyUser.Id, offset, limit,
                afterTimestamp, beforeTimestamp, channels, channelIds, IsFromUser);

            var data = await _cacheManagerService.GetCacheAsync(signalrMessageCacheKeyPattern);

            if (!string.IsNullOrEmpty(data))
            {
                return Ok(JsonConvert.DeserializeObject<MissingMessageViewModel>(data));
            }

            while (true)
            {
                var myLock = await _lockService.AcquireLockAsync(
                    $"lock:v2_signalr_message_{signalrMessageCacheKeyPattern.GenerateKeyPattern()}",
                    TimeSpan.FromSeconds(60));

                if (myLock == null)
                {
                    var cacheData = await _cacheManagerService.GetCacheAsync(signalrMessageCacheKeyPattern);

                    if (cacheData != null)
                    {
                        return Ok(JsonConvert.DeserializeObject<MissingMessageViewModel>(cacheData));
                    }

                    await Task.Delay(5000);
                }
                else
                {
                    break;
                }
            }

            try
            {
                // Only For CTF
                var whatsappIds = new List<string>();
                var twilioSenderIds = new List<string>();
                var whatsappCloudDefaultChannelIds = new List<string>();

                var dbContext = _dbContextService.GetDbContext();

                var rolePermission = await dbContext.CompanyRolePermissions
                    .AsNoTracking()
                    .FirstOrDefaultAsync(
                        x =>
                            x.CompanyId == companyUser.CompanyId
                            && x.StaffUserRole == companyUser.RoleType);

                var whatsapp360dialogDefaultChannelIds = new List<long>();

                if (rolePermission != null
                    && rolePermission.Permission.IsShowDefaultChannelMessagesOnly)
                {
                    var associatedTeams = await dbContext.CompanyStaffTeams
                        .Where(x => x.Members.Any(y => y.StaffId == companyUser.Id))
                        .AsNoTracking()
                        .ToListAsync(HttpContext.RequestAborted);

                    foreach (var assoicatedTeam in associatedTeams)
                    {
                        if (assoicatedTeam.DefaultChannels?.Count > 0)
                        {

                            foreach (var defaultchannel in assoicatedTeam.DefaultChannels
                                         .Where(
                                             x =>
                                                 x.channel != ChannelTypes.Whatsapp360Dialog
                                                 && x.channel != ChannelTypes.WhatsappCloudApi))
                            {
                                foreach (var id in defaultchannel.ids)
                                {
                                    var twilioInstance = id.Split(";", StringSplitOptions.RemoveEmptyEntries);
                                    whatsappIds.Add(twilioInstance[0]);

                                    if (twilioInstance.Count() > 1)
                                    {
                                        twilioSenderIds.Add(twilioInstance[1].Replace(": ", ":+"));
                                    }
                                }
                            }

                            foreach (var defaultchannel in assoicatedTeam.DefaultChannels
                                         .Where(x => x.channel == ChannelTypes.Whatsapp360Dialog))
                            {
                                foreach (var channelId in defaultchannel.ids)
                                {
                                    var validLong = long.TryParse(channelId, out var whatsapp360dialogChannelId);
                                    if (validLong)
                                    {
                                        whatsapp360dialogDefaultChannelIds.Add(whatsapp360dialogChannelId);
                                    }
                                }
                            }

                            foreach (var defaultChannel in assoicatedTeam.DefaultChannels
                                         .Where(x => x.channel == ChannelTypes.WhatsappCloudApi))
                            {
                                foreach (var channelId in defaultChannel.ids)
                                {
                                    whatsappCloudDefaultChannelIds.Add(channelId);
                                }
                            }
                        }
                    }
                }

                try
                {
                    var messagesQueryable = dbContext.ConversationMessages
                        .Where(
                            conversationMessages => conversationMessages.CompanyId == companyUser.CompanyId
                                                    && conversationMessages.Conversation.ActiveStatus ==
                                                    ActiveStatus.Active)
                        .OrderByDescending(conversationMessages => conversationMessages.Timestamp);

                    if (beforeTimestamp.HasValue)
                    {
                        messagesQueryable = messagesQueryable
                            .Where(conversationMessages => conversationMessages.Timestamp <= beforeTimestamp)
                            .OrderByDescending(conversationMessages => conversationMessages.Timestamp);
                    }

                    if (afterTimestamp.HasValue)
                    {
                        messagesQueryable = messagesQueryable
                            .Where(conversationMessages => conversationMessages.Timestamp >= afterTimestamp)
                            .OrderByDescending(conversationMessages => conversationMessages.Timestamp);
                    }

                    var channelList = new List<string>();

                    if (!string.IsNullOrEmpty(channels))
                    {
                        channelList = channels
                            .Split(",")
                            .ToList();
                    }

                    var channelIdList = new List<string>();

                    if (!string.IsNullOrEmpty(channelIds))
                    {
                        channelIdList = channelIds
                            .Split(",")
                            .ToList();
                    }

                    if (channelIdList.Count > 0)
                    {
                        try
                        {
                            var newWhatsappId = new List<string>();
                            var newInstanceSender = new List<string>();
                            var newWhatsapp360dialogDefaultChannelIds = new List<long>();

                            if (channelList.Contains(ChannelTypes.WhatsappTwilio))
                            {
                                foreach (var myChannelId in channelIdList)
                                {
                                    var twilioInstance = myChannelId.Split(";", StringSplitOptions.RemoveEmptyEntries);
                                    newWhatsappId.Add(twilioInstance[0]);

                                    if (twilioInstance.Count() > 1)
                                    {
                                        newInstanceSender.Add(twilioInstance[1].Replace(": ", ":+"));
                                    }
                                }
                            }

                            if (channelList.Contains(ChannelTypes.Whatsapp360Dialog))
                            {
                                var whatsapp360DialogChannelId = channelIdList
                                    .Select(x => long.TryParse(x, out var id) ? id : 0)
                                    .Where(x => x != 0)
                                    .Distinct()
                                    .ToList();

                                newWhatsapp360dialogDefaultChannelIds.AddRange(whatsapp360DialogChannelId);
                            }

                            if (channelList.Contains(ChannelTypes.WhatsappCloudApi))
                            {
                                var whatsappCloudApiChannelIds = channelIdList
                                    .Distinct()
                                    .ToList();

                                whatsappCloudDefaultChannelIds.AddRange(whatsappCloudApiChannelIds);
                            }

                            if (newWhatsappId.Count > 0
                                && newWhatsapp360dialogDefaultChannelIds.Count > 0)
                            {
                                messagesQueryable = messagesQueryable.Where(
                                        conversationMessage =>
                                            newWhatsappId.Contains(conversationMessage.whatsappReceiver.InstanceId) ||
                                            newWhatsappId.Contains(conversationMessage.facebookReceiver.pageId) ||
                                            newWhatsappId.Contains(
                                                conversationMessage.InstagramReceiver.InstagramPageId) ||
                                            newWhatsapp360dialogDefaultChannelIds.Contains(
                                                conversationMessage.Whatsapp360DialogReceiver.ChannelId.Value) ||
                                            newWhatsapp360dialogDefaultChannelIds.Contains(
                                                conversationMessage.Whatsapp360DialogSender.ChannelId.Value) ||
                                            conversationMessage.Channel == ChannelTypes.Note ||
                                            conversationMessage.Channel == ChannelTypes.LiveChat)
                                    .OrderByDescending(conversationMessage => conversationMessage.Timestamp);

                                if (newInstanceSender.Count > 0)
                                {
                                    messagesQueryable = messagesQueryable.Where(
                                            conversationMessage =>
                                                conversationMessage.whatsappReceiver.InstaneSender == null ||
                                                newInstanceSender.Contains(
                                                    conversationMessage.whatsappReceiver.InstaneSender) ||
                                                conversationMessage.Channel == ChannelTypes.Note ||
                                                conversationMessage.Channel == ChannelTypes.LiveChat)
                                        .OrderByDescending(conversationMessage => conversationMessage.Timestamp);
                                }
                            }
                            else if (newWhatsappId.Count > 0)
                            {
                                messagesQueryable = messagesQueryable.Where(
                                        conversationMessage =>
                                            newWhatsappId.Contains(conversationMessage.whatsappReceiver.InstanceId) ||
                                            newWhatsappId.Contains(conversationMessage.facebookReceiver.pageId) ||
                                            newWhatsappId.Contains(
                                                conversationMessage.InstagramReceiver.InstagramPageId) ||
                                            conversationMessage.Channel == ChannelTypes.Note ||
                                            conversationMessage.Channel == ChannelTypes.LiveChat)
                                    .OrderByDescending(conversationMessage => conversationMessage.Timestamp);

                                if (twilioSenderIds.Count > 0)
                                {
                                    messagesQueryable = messagesQueryable.Where(
                                            conversationMessage =>
                                                conversationMessage.whatsappReceiver.InstaneSender == null ||
                                                newInstanceSender.Contains(
                                                    conversationMessage.whatsappReceiver.InstaneSender) ||
                                                conversationMessage.Channel == ChannelTypes.Note ||
                                                conversationMessage.Channel == ChannelTypes.LiveChat)
                                        .OrderByDescending(conversationMessage => conversationMessage.Timestamp);
                                }
                            }
                            else if (newWhatsapp360dialogDefaultChannelIds.Count > 0)
                            {
                                messagesQueryable = messagesQueryable.Where(
                                        conversationMessage =>
                                            newWhatsapp360dialogDefaultChannelIds.Contains(
                                                conversationMessage.Whatsapp360DialogReceiver.ChannelId.Value) ||
                                            newWhatsapp360dialogDefaultChannelIds.Contains(
                                                conversationMessage.Whatsapp360DialogSender.ChannelId.Value) ||
                                            conversationMessage.Channel == ChannelTypes.Note ||
                                            conversationMessage.Channel == ChannelTypes.LiveChat)
                                    .OrderByDescending(conversationMessage => conversationMessage.Timestamp);
                            }

                            if (whatsappCloudDefaultChannelIds.Count > 0)
                            {
                                if (whatsappCloudDefaultChannelIds.Count > 0)
                                {
                                    messagesQueryable = messagesQueryable
                                        .Where(
                                            conversationMessage =>
                                                whatsappCloudDefaultChannelIds.Contains(
                                                    conversationMessage.ChannelIdentityId) ||
                                                conversationMessage.Channel == ChannelTypes.Note)
                                        .OrderByDescending(conversationMessage => conversationMessage.Timestamp);
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(
                                ex,
                                "Unable to parse TargetedChannelModel {ExceptionMessage}",
                                ex.Message);
                        }
                    }

                    if (IsFromUser.HasValue)
                    {
                        messagesQueryable = messagesQueryable
                            .Where(conversationMessage => conversationMessage.IsSentFromSleekflow == !IsFromUser.Value)
                            .OrderByDescending(conversationMessage => conversationMessage.Timestamp);
                    }

                    if (whatsappIds.Count > 0 && whatsapp360dialogDefaultChannelIds.Count > 0)
                    {
                        messagesQueryable = messagesQueryable.Where(
                                conversationMessage =>
                                    whatsappIds.Contains(conversationMessage.whatsappReceiver.InstanceId) ||
                                    whatsappIds.Contains(conversationMessage.facebookReceiver.pageId) ||
                                    whatsapp360dialogDefaultChannelIds.Contains(
                                        conversationMessage.Whatsapp360DialogReceiver.ChannelId.Value) ||
                                    whatsapp360dialogDefaultChannelIds.Contains(
                                        conversationMessage.Whatsapp360DialogSender.ChannelId.Value) ||
                                    conversationMessage.Channel == ChannelTypes.Note ||
                                    conversationMessage.Channel == ChannelTypes.LiveChat)
                            .OrderByDescending(conversationMessage => conversationMessage.Timestamp);

                        if (twilioSenderIds.Count > 0)
                        {
                            messagesQueryable = messagesQueryable.Where(
                                    conversationMessage =>
                                        twilioSenderIds.Contains(conversationMessage.whatsappReceiver.InstaneSender) ||
                                        string.IsNullOrEmpty(conversationMessage.whatsappReceiver.InstaneSender) ||
                                        conversationMessage.Channel == ChannelTypes.Note ||
                                        conversationMessage.Channel == ChannelTypes.LiveChat)
                                .OrderByDescending(conversationMessage => conversationMessage.Timestamp);
                        }
                    }
                    else if (whatsappIds.Count > 0)
                    {
                        messagesQueryable = messagesQueryable.Where(
                                conversationMessage =>
                                    whatsappIds.Contains(conversationMessage.whatsappReceiver.InstanceId) ||
                                    whatsappIds.Contains(conversationMessage.facebookReceiver.pageId) ||
                                    conversationMessage.Channel == ChannelTypes.Note ||
                                    conversationMessage.Channel == ChannelTypes.LiveChat)
                            .OrderByDescending(conversationMessage => conversationMessage.Timestamp);

                        if (twilioSenderIds.Count > 0)
                        {
                            messagesQueryable = messagesQueryable.Where(
                                    conversationMessage =>
                                        twilioSenderIds.Contains(conversationMessage.whatsappReceiver.InstaneSender) ||
                                        string.IsNullOrEmpty(conversationMessage.whatsappReceiver.InstaneSender) ||
                                        conversationMessage.Channel == ChannelTypes.Note ||
                                        conversationMessage.Channel == ChannelTypes.LiveChat)
                                .OrderByDescending(conversationMessage => conversationMessage.Timestamp);
                        }
                    }
                    else if (whatsapp360dialogDefaultChannelIds.Count > 0)
                    {
                        messagesQueryable = messagesQueryable.Where(
                                conversationMessage =>
                                    whatsapp360dialogDefaultChannelIds.Contains(
                                        conversationMessage.Whatsapp360DialogReceiver.ChannelId.Value) ||
                                    whatsapp360dialogDefaultChannelIds.Contains(
                                        conversationMessage.Whatsapp360DialogSender.ChannelId.Value) ||
                                    conversationMessage.Channel == ChannelTypes.Note ||
                                    conversationMessage.Channel == ChannelTypes.LiveChat)
                            .OrderByDescending(conversationMessage => conversationMessage.Timestamp);
                    }

                    var response = await messagesQueryable
                        .Include(x => x.UploadedFiles)
                        .Include(x => x.EmailFrom)
                        .Include(x => x.Sender)
                        .Include(x => x.facebookSender)
                        .Include(x => x.facebookReceiver)
                        .Include(x => x.whatsappSender)
                        .Include(x => x.whatsappReceiver)
                        .Include(x => x.WebClientSender)
                        .Include(x => x.WebClientReceiver)
                        .Include(x => x.MessageAssignee.Identity)
                        .Include(x => x.WeChatSender)
                        .Include(x => x.WeChatReceiver)
                        .Include(x => x.LineSender)
                        .Include(x => x.LineReceiver)
                        .Include(x => x.SMSSender)
                        .Include(x => x.SMSReceiver)
                        .Include(x => x.InstagramSender)
                        .Include(x => x.InstagramReceiver)
                        .Include(x => x.ViberSender)
                        .Include(x => x.ViberReceiver)
                        .Include(x => x.TelegramSender)
                        .Include(x => x.TelegramReceiver)
                        .Include(x => x.Whatsapp360DialogSender)
                        .Include(x => x.Whatsapp360DialogReceiver)
                        .Include(x => x.Whatsapp360DialogExtendedMessagePayload)
                        .Include(x => x.ExtendedMessagePayload)
                        .OrderByDescending(x => x.Timestamp)
                        .Skip(offset).Take(limit)
                        .ProjectTo<ConversationMessageResponseViewModel>(_mapper.ConfigurationProvider)
                        .ToListAsync(HttpContext.RequestAborted);

                    var responseVm = new MissingMessageViewModel();
                    responseVm.Messages = response;
                    responseVm.HasNext = await messagesQueryable
                        .Skip(limit + offset)
                        .AnyAsync(); // false; // result.Skip(limit + offset).Any();

                    await _sleekPayService.AddSleekPayRecord(responseVm.Messages);

                    await _cacheManagerService.SaveCacheAsync(
                        signalrMessageCacheKeyPattern,
                        responseVm);

                    return Ok(responseVm);
                }
                catch (Exception ex)
                {
                    return Ok(new MissingMessageViewModel());
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "v2/conversation/Message Key: {Key} {ExceptionString}",
                    $"v2_signalr_message_{signalrMessageCacheKeyPattern.GenerateKeyPattern()}",
                    ex.ToString());

                return Ok(new MissingMessageViewModel());
            }
        }

        /// <summary>
        /// Set Tags to Conversation (Will remove others tags).
        /// </summary>
        /// <param name="conversationId">Targeted ConversationId.</param>
        /// <param name="conversationTagViewModels">Conversation Tag Object.</param>
        /// <returns></returns>
        [HttpPost]
        [Route("Conversation/Tags/{conversationId}")]
        public async Task<ActionResult<ConversationNoCompanyResponseViewModel>> SetHashTags(
            string conversationId,
            [FromBody]
            List<ConversationHashtagViewModel> conversationTagViewModels)
        {
            var companyUser = await _coreService.GetCompanyStaff(
                await _userManager.GetUserAsync(User));

            if (companyUser == null)
            {
                return Unauthorized();
            }

            try
            {
                var conversation = await _conversationHashtagService.SetConversationHashtag(
                    companyUser.CompanyId,
                    conversationId,
                    companyUser.Id,
                    conversationTagViewModels);

                var conversationResponses =
                    _mapper.Map<ConversationNoCompanyResponseViewModel>(conversation);

                return Ok(conversationResponses);
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[{MethodName} endpoint] error for conversation {ConversationId} in company {CompanyId}: {ExceptionMessage}",
                    nameof(SetHashTags),
                    conversationId,
                    companyUser.CompanyId,
                    ex.Message);

                return BadRequest(
                    new ResponseViewModel
                    {
                        message = ex.Message
                    });
            }
        }

        /// <summary>
        /// Add Tags to Conversation.
        /// </summary>
        /// <param name="conversationId">Targeted ConversationId.</param>
        /// <param name="conversationTagViewModels">Conversation Tag Object.</param>
        /// <returns></returns>
        [HttpPost]
        [Route("Conversation/Tags/Add/{conversationId}")]
        public async Task<ActionResult<ConversationNoCompanyResponseViewModel>> AddHashTags(
            string conversationId,
            [FromBody]
            List<ConversationHashtagViewModel> conversationTagViewModels)
        {
            var companyUser = await _coreService.GetCompanyStaff(
                await _userManager.GetUserAsync(User));

            if (companyUser == null)
            {
                return Unauthorized();
            }

            try
            {
                var conversation = await _conversationHashtagService.AddConversationHashtag(
                    companyUser.CompanyId,
                    conversationId,
                    companyUser.Id,
                    conversationTagViewModels);
                var conversationResponses =
                    await _conversationService.GetConversationDetails(
                        companyUser.CompanyId,
                        companyUser.Id,
                        companyUser.RoleType,
                        conversationId);

                return Ok(conversationResponses);
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[{MethodName} endpoint] error for conversation {ConversationId} in company {CompanyId}: {ExceptionMessage}",
                    nameof(AddHashTags),
                    conversationId,
                    companyUser.CompanyId,
                    ex.Message);

                return BadRequest(
                    new ResponseViewModel
                    {
                        message = ex.Message
                    });
            }
        }

        /// <summary>
        /// Remove Tags from Conversation.
        /// </summary>
        /// <param name="conversationId">Targeted ConversationId.</param>
        /// <param name="conversationTagViewModels">Conversation Tag Object.</param>
        /// <returns></returns>
        [HttpPost]
        [Route("Conversation/Tags/Remove/{conversationId}")]
        public async Task<ActionResult<ConversationNoCompanyResponseViewModel>> RemoveHashTags(
            string conversationId,
            [FromBody]
            List<ConversationHashtagViewModel> conversationTagViewModels)
        {
            var companyUser = await _coreService.GetCompanyStaff(
                await _userManager.GetUserAsync(User));

            if (companyUser == null)
            {
                return Unauthorized();
            }

            try
            {
                var conversation = await _conversationHashtagService.RemoveConversationHashtag(
                    companyUser.CompanyId,
                    conversationId,
                    companyUser.Id,
                    conversationTagViewModels);

                var conversationResponses =
                    await _conversationService.GetConversationDetails(
                        companyUser.CompanyId,
                        companyUser.Id,
                        companyUser.RoleType,
                        conversationId);

                return Ok(conversationResponses);
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[{MethodName} endpoint] error for conversation {ConversationId} in company {CompanyId}: {ExceptionMessage}",
                    nameof(RemoveHashTags),
                    conversationId,
                    companyUser.CompanyId,
                    ex.Message);

                return BadRequest(
                    new ResponseViewModel
                    {
                        message = ex.Message
                    });
            }
        }

        public int GetAssignedTeam(
            string assignedId,
            List<OpenActiveConversationAssigneesAndAdditionalAssigneesViewModel>
                openActiveConversationAssigneesAndAdditionalAssigneesList)
        {
            return openActiveConversationAssigneesAndAdditionalAssigneesList
                .Count(
                    x =>
                        x.Assignee.IdentityId == assignedId
                        || x.AdditionalAssignees!.Any(y => y.Assignee.IdentityId == assignedId));
        }

        /// <summary>
        /// V2 Get conversation count summary details for open, pending and closed; hashtags summary; unread summary.
        /// </summary>
        /// <param name="assignedTo"><b>all</b>: List all manageable conversations (For <b>Admin</b> and <b>TeamAdmin</b> role only)<br />
        /// <b>unassigned</b>: List all unassigned conversations (For <b>admin</b> can see all unassigned chat; <b>TeamAdmin</b> and <b>Staff</b> can see unassigned chat under their teams)<br />
        /// <b>mentioned</b>: List all conversations that mentioned you<br />
        /// <b>collaborator</b>: List all conversations that you are collaborator<br />
        /// <b>team</b>: List all team conversations or team unassigned chat, if <b>isTeamUnassigned</b> = true then only return the team unassigned chat<br />
        /// <b>{staffId}</b>: List all conversations assigned to this user like:. <b>77b68a4e-d1a5-4433-a161-a768c06a6988</b></param>
        /// <param name="status">[ <b>all, open, pending, closed, scheduled</b> ].</param>
        /// <param name="channels">General channel name like: <b>whatsapp,whatsapp360dialog,facebook</b> ;Separated by. <b>,</b></param>
        /// <param name="afterUpdatedAt">Get conversations that updated time is after this value (UTC).</param>
        /// <param name="afterModifiedAt">Get conversations that modified time is after this value (UTC).</param>
        /// <param name="channelIds">Specify the channel id like:<br />
        /// Twilio: AC1fc368e79a37f6a9e87657dd3d75a2e5;whatsapp:+8526452244<br />
        /// 360Dialog: 175.
        /// </param>
        /// <param name="tags">Label filter.</param>
        /// <param name="teamId">TeamId, assignedTo = team.</param>
        /// <param name="isTeamUnassigned">Show only unassigned chats.</param>
        /// <param name="isUnread">Show only is unread chats.</param>
        /// <returns></returns>
        [HttpGet]
        [ServiceFilter(typeof(ReadOnlyEndpointAttribute))]
        [Route("v2/conversation/summary/{assignedTo}")]
        public async Task<ActionResult<ConversationSummary>> GetV2ConversationSummary(
            string assignedTo = "all",
            [FromQuery(Name = "status")]
            string status = "open",
            [FromQuery(Name = "channel")]
            string channels = null,
            [FromQuery(Name = "afterUpdatedAt")]
            DateTime? afterUpdatedAt = null,
            [FromQuery(Name = "afterModifiedAt")]
            DateTime? afterModifiedAt = null,
            [FromQuery(Name = "channelIds")]
            string channelIds = null,
            [FromQuery(Name = "tags")]
            string tags = null,
            [FromQuery(Name = "teamId")]
            long? teamId = null,
            [FromQuery(Name = "isTeamUnassigned")]
            bool? isTeamUnassigned = null,
            [FromQuery(Name = "isUnread")]
            bool? isUnread = null,
            CancellationToken cancellationToken = default)
        {
            // var companyUser = await _appDbContext.UserRoleStaffs.Where(x => x.IdentityId == userId).FirstOrDefaultAsync();
            var applicationUser = await _sleekflowUserService.GetUserAsync(User);
            var companyUser = await _coreService.GetCompanyStaff(applicationUser);

            if (companyUser == null)
            {
                return Unauthorized();
            }

            var results = new ConversationSummary();

            var assignedConversationCacheKeyPattern = new AssignedConversationV2CacheKeyPattern(companyUser.Id, status, assignedTo, channels, afterUpdatedAt, afterModifiedAt, channelIds, tags, teamId, isTeamUnassigned, isUnread);

            var data = await _cacheManagerService.GetCacheAsync(assignedConversationCacheKeyPattern);

            if (!string.IsNullOrEmpty(data))
            {
                return Ok(JsonConvert.DeserializeObject<ConversationSummary>(data));
            }

            var myConversation = await _conversationMessageService.GetConversations(
                companyUser.CompanyId,
                companyUser,
                "all",
                assignedTo,
                channels,
                afterUpdatedAt,
                afterModifiedAt,
                channelIds,
                tags,
                teamId,
                isTeamUnassigned,
                isUnread);

            var myCount = await _conversationSummaryService.GetConversationSummaryCountAsync(
                myConversation,
                "all",
                assignedTo,
                "assigned",
                teamId,
                cancellationToken);

            results.ConversationSummaries.AddRange(myCount);

            try
            {
                myConversation = await _conversationMessageService.GetConversations(
                    companyUser.CompanyId,
                    companyUser,
                    status,
                    assignedTo,
                    channels,
                    afterUpdatedAt,
                    afterModifiedAt,
                    channelIds,
                    tags,
                    teamId,
                    isTeamUnassigned,
                    isUnread);

                var hashtagSummaries = await _conversationSummaryService.GetHashtagSummaryCount(
                    myConversation,
                    companyUser.CompanyId,
                    cancellationToken);

                results.HashtagSummaries.AddRange(hashtagSummaries);
            }
            catch (TaskCanceledException)
            {
                throw;
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[{MethodName} endpoint] Unable to get hashtag usage: {ExceptionMessage}",
                    nameof(GetV2ConversationSummary),
                    ex.Message);
            }

            var unread = await _conversationMessageService.GetConversations(
                companyUser.CompanyId,
                companyUser,
                status,
                assignedTo,
                channels,
                afterUpdatedAt,
                afterModifiedAt,
                channelIds,
                tags,
                teamId,
                isTeamUnassigned,
                true);

            results.UnreadCount = await unread.CountAsync(cancellationToken: cancellationToken);

            await _cacheManagerService.SaveCacheAsync(assignedConversationCacheKeyPattern, results);

            return Ok(results);
        }

        /// <summary>
        /// V2 Get conversation count summary general, to get all general summary for you, unassigned, mentioned, team unassigned, all and all others staffs.
        /// </summary>
        /// <param name="status">[ <b>all, open, pending, closed, scheduled</b> ].</param>
        /// <param name="channels">General channel name like: <b>whatsapp,whatsapp360dialog,facebook</b> ;Separated by. <b>,</b></param>
        /// <param name="afterUpdatedAt">Get conversations that updated time is after this value (UTC).</param>
        /// <param name="afterModifiedAt">Get conversations that modified time is after this value (UTC).</param>
        /// <param name="channelIds">Specify the channel id like:<br />
        /// Twilio: AC1fc368e79a37f6a9e87657dd3d75a2e5;whatsapp:+8526452244<br />
        /// 360Dialog: 175.
        /// </param>
        /// <param name="tags">Label filter.</param>
        /// <param name="teamId">TeamId, assignedTo = team.</param>
        /// <param name="isTeamUnassigned">Show only unassigned chats.</param>
        /// <param name="isUnread">Show only is unread chats.</param>
        /// <returns></returns>
        [HttpGet]
        [Route("v2/conversation/summary")]
        [ServiceFilter(typeof(ReadOnlyEndpointAttribute))]
        public async Task<ActionResult<List<ResultLine>>> GetV2ConversationSummary(
            [FromQuery(Name = "status")]
            string status = "open",
            [FromQuery(Name = "channel")]
            string channels = null,
            [FromQuery(Name = "afterUpdatedAt")]
            DateTime? afterUpdatedAt = null,
            [FromQuery(Name = "afterModifiedAt")]
            DateTime? afterModifiedAt = null,
            [FromQuery(Name = "channelIds")]
            string channelIds = null,
            [FromQuery(Name = "tags")]
            string tags = null,
            [FromQuery(Name = "teamId")]
            long? teamId = null,
            [FromQuery(Name = "isTeamUnassigned")]
            bool? isTeamUnassigned = null,
            [FromQuery(Name = "isUnread")]
            bool? isUnread = null)
        {
            var applicationUser = await _sleekflowUserService.GetUserAsync(User);
            var companyUser = await _coreService.GetCompanyStaff(applicationUser);

            if (companyUser == null)
            {
                return Unauthorized();
            }

            var conversationAllCacheKeyPattern = new ConversationAllCacheKeyPattern(companyUser.Id, status, channels,
                afterUpdatedAt, afterModifiedAt, channelIds, tags, teamId, isTeamUnassigned, isUnread);

            var data = await _cacheManagerService.GetCacheAsync(conversationAllCacheKeyPattern);

            if (!string.IsNullOrEmpty(data))
            {
                return Ok(JsonConvert.DeserializeObject<List<ResultLine>>(data));
            }

            try
            {
                var results = new List<ResultLine>();
                var dbContext = _dbContextService.GetDbContext();
                if (teamId.HasValue)
                {
                    switch (companyUser.RoleType)
                    {
                        case StaffUserRole.Admin:
                        case StaffUserRole.TeamAdmin:
                        case StaffUserRole.DemoAdmin:
                            var inTeams = await dbContext.CompanyStaffTeams
                                .Include(x => x.Members)
                                .ThenInclude(x => x.Staff)
                                .FirstOrDefaultAsync(
                                    x =>
                                        x.CompanyId == companyUser.CompanyId
                                        && x.Id == teamId.Value);

                            foreach (var member in inTeams.Members)
                            {
                                var memberConversation = await _conversationMessageService.GetConversations(
                                    companyUser.CompanyId,
                                    companyUser,
                                    status,
                                    member.Staff.IdentityId,
                                    channels,
                                    afterUpdatedAt,
                                    afterModifiedAt,
                                    channelIds,
                                    tags,
                                    teamId,
                                    isTeamUnassigned,
                                    isUnread);

                                results.Add(
                                    new ResultLine
                                    {
                                        Type = "assigned",
                                        AssigneeId = member.Staff.IdentityId,
                                        Count = await memberConversation.CountAsync(),
                                        Status = status,
                                        TeamId = teamId
                                    });
                            }

                            break;
                    }
                }
                else
                {
                    var myConversation = await _conversationMessageService.GetConversations(
                        companyUser.CompanyId,
                        companyUser,
                        status,
                        companyUser.IdentityId,
                        channels,
                        afterUpdatedAt,
                        afterModifiedAt,
                        channelIds,
                        tags,
                        teamId,
                        isTeamUnassigned,
                        isUnread);

                    results.Add(
                        new ResultLine
                        {
                            Type = "assigned",
                            AssigneeId = companyUser.IdentityId,
                            Count = await myConversation.CountAsync(),
                            Status = "open"
                        });

                    var unassignedConversation = await _conversationMessageService.GetConversations(
                        companyUser.CompanyId,
                        companyUser,
                        status,
                        "unassigned",
                        channels,
                        afterUpdatedAt,
                        afterModifiedAt,
                        channelIds,
                        tags,
                        teamId,
                        isTeamUnassigned,
                        isUnread);

                    results.Add(
                        new ResultLine
                        {
                            Type = "unassigned",
                            Count = await unassignedConversation.CountAsync(),
                            Status = status
                        });

                    var mentionedConversation = await _conversationMessageService.GetConversations(
                        companyUser.CompanyId,
                        companyUser,
                        status,
                        "mentioned",
                        channels,
                        afterUpdatedAt,
                        afterModifiedAt,
                        channelIds,
                        tags,
                        teamId,
                        isTeamUnassigned,
                        isUnread);

                    results.Add(
                        new ResultLine
                        {
                            Type = "mentioned",
                            Count = await mentionedConversation.CountAsync(),
                            Status = status
                        });

                    var inTeams = await dbContext.CompanyStaffTeams
                        .Where(
                            x =>
                                x.CompanyId == companyUser.CompanyId
                                && x.Members
                                    .Select(m => m.Staff.IdentityId)
                                    .Contains(companyUser.IdentityId))
                        .ToListAsync(HttpContext.RequestAborted);

                    foreach (var team in inTeams)
                    {
                        var teamUnassignedConversation = await _conversationMessageService.GetConversations(
                            companyUser.CompanyId,
                            companyUser,
                            status,
                            "team",
                            channels,
                            afterUpdatedAt,
                            afterModifiedAt,
                            channelIds,
                            tags,
                            team.Id,
                            true,
                            isUnread);

                        results.Add(
                            new ResultLine
                            {
                                Type = "teamUnassigned",
                                TeamId = team.Id,
                                Count = await teamUnassignedConversation.CountAsync(),
                                Status = status
                            });
                    }

                    switch (companyUser.RoleType)
                    {
                        case StaffUserRole.Admin:
                        case StaffUserRole.TeamAdmin:
                        case StaffUserRole.DemoAdmin:
                            var allConversation = await _conversationMessageService.GetConversations(
                                companyUser.CompanyId,
                                companyUser,
                                status,
                                "all",
                                channels,
                                afterUpdatedAt,
                                afterModifiedAt,
                                channelIds,
                                tags,
                                teamId,
                                isTeamUnassigned,
                                isUnread);

                            results.Add(
                                new ResultLine
                                {
                                    Type = "all",
                                    Count = await allConversation.CountAsync(),
                                    Status = status
                                });

                            if (companyUser.RoleType != StaffUserRole.TeamAdmin)
                            {
                                var noTeamStaffs = await dbContext.UserRoleStaffs
                                    .Where(
                                        x =>
                                            x.CompanyId == companyUser.CompanyId
                                            && !dbContext.CompanyTeamMembers
                                                .Any(y => y.StaffId == x.Id))
                                    .ToListAsync(HttpContext.RequestAborted);

                                foreach (var noTeamStaff in noTeamStaffs)
                                {
                                    var noTeamStaffConversation = await _conversationMessageService.GetConversations(
                                        companyUser.CompanyId,
                                        companyUser,
                                        status,
                                        noTeamStaff.IdentityId,
                                        channels,
                                        afterUpdatedAt,
                                        afterModifiedAt,
                                        channelIds,
                                        tags,
                                        teamId,
                                        isTeamUnassigned,
                                        isUnread);

                                    results.Add(
                                        new ResultLine
                                        {
                                            Type = "assigned",
                                            AssigneeId = noTeamStaff.IdentityId,
                                            Count = await noTeamStaffConversation.CountAsync(),
                                            Status = status
                                        });
                                }
                            }

                            break;
                    }
                }

                await _cacheManagerService.SaveCacheAsync(
                    conversationAllCacheKeyPattern,
                    results);

                return Ok(results);
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[{MethodName} endpoint] Error for key {Key}: {ExceptionMessage}",
                    nameof(GetV2ConversationSummary),
                    $"all_{conversationAllCacheKeyPattern.GenerateKeyPattern()}",
                    ex.Message);

                return BadRequest(
                    new ResponseViewModel
                    {
                        message = ex.Message
                    });
            }
        }

        /// <summary>
        /// Export conversation.
        /// </summary>
        /// <param name="exportConversationViewModel">Export Conversation Object.</param>
        /// <returns></returns>
        [HttpPost]
        [Route("conversation/export")]
        public async Task<IActionResult> ExportConversations(
            [FromBody]
            ExportConversationViewModel exportConversationViewModel)
        {
            try
            {
                var companyUser = await _coreService.GetCompanyStaff(
                    await _userManager.GetUserAsync(User));

                if (companyUser == null)
                {
                    return Unauthorized();
                }

                var header =
                    "\"MessageId\",\"Channel\",\"MessageType\",\"Sender\",\"Receiver\",\"MessageContent\",\"MediaType\",\"MediaURL\",\"Date\"";
                var exportResult = string.Empty;

                var conversation = await _appDbContext.Conversations
                    .Include(x => x.UserProfile)
                    .FirstOrDefaultAsync(x => x.Id == exportConversationViewModel.ConversationId);

                exportResult += $"{header}\n";

                var conversationMessagesQ = _appDbContext.ConversationMessages
                    .Where(x => x.ConversationId == exportConversationViewModel.ConversationId);

                if (exportConversationViewModel.Start.HasValue)
                {
                    conversationMessagesQ = conversationMessagesQ
                        .Where(x => x.UpdatedAt > exportConversationViewModel.Start);
                }

                if (exportConversationViewModel.End.HasValue)
                {
                    conversationMessagesQ = conversationMessagesQ
                        .Where(x => x.UpdatedAt < exportConversationViewModel.End);
                }

                var conversationMessages = await conversationMessagesQ
                    .Include(y => y.UploadedFiles)
                    .Include(y => y.MessageAssignee.Identity)
                    .Include(y => y.EmailFrom)
                    .Include(y => y.Sender)
                    .Include(y => y.Receiver)
                    .Include(y => y.SenderDevice)
                    .Include(y => y.ReceiverDevice)
                    .Include(y => y.facebookSender)
                    .Include(y => y.facebookReceiver)
                    .Include(y => y.whatsappSender)
                    .Include(y => y.whatsappReceiver)
                    .Include(y => y.WebClientSender)
                    .Include(y => y.WebClientReceiver)
                    .Include(y => y.MessageAssignee.Identity)
                    .Include(y => y.WeChatSender)
                    .Include(y => y.WeChatReceiver)
                    .Include(y => y.LineSender)
                    .Include(y => y.LineReceiver)
                    .Include(x => x.LineReceiver)
                    .Include(x => x.LineSender)
                    .ToListAsync();

                string chaanel = string.Empty;

                foreach (var conversationMessage in conversationMessages)
                {
                    string sender = string.Empty;
                    string receiver = string.Empty;

                    switch (conversationMessage.Channel)
                    {
                        case ChannelTypes.WhatsappTwilio:
                            if (conversationMessage.whatsappSenderId.HasValue)
                            {
                                sender =
                                    $"{conversationMessage.whatsappSender?.name} ({conversationMessage.whatsappSender?.phone_number})";
                            }
                            else
                            {
                                try
                                {
                                    var twilioSender = await _appDbContext.SenderWhatsappSenders
                                        .FirstOrDefaultAsync(
                                            x =>
                                                x.whatsAppId == conversationMessage.whatsappReceiver.InstaneSender);

                                    if (twilioSender != null)
                                    {
                                        sender = $"{twilioSender?.name} ({twilioSender?.phone_number})";
                                    }
                                }
                                catch (Exception ex)
                                {
                                    _logger.LogError(
                                        ex,
                                        "[{MethodName} endpoint] Error retrieving WhatsApp sender for Conversation {ConversationId} " +
                                        "based on conversation message whatsapp receiver instance sender {InstanceSender}: {ExceptionMessage}",
                                        nameof(ExportConversations),
                                        exportConversationViewModel?.ConversationId,
                                        conversationMessage?.whatsappReceiver?.InstaneSender,
                                        ex.Message);
                                }
                            }

                            receiver =
                                $"{conversationMessage.whatsappReceiver?.name} ({conversationMessage.whatsappReceiver?.phone_number})";
                            break;
                    }

                    string mediaType = string.Empty;
                    string mediaURL = string.Empty;

                    if (conversationMessage.UploadedFiles?.Count > 0)
                    {
                        var domainName = _configuration.GetValue<string>("Values:DomainName");

                        mediaType = conversationMessage.UploadedFiles.First().MIMEType;
                        mediaURL = $"{domainName}/Message/File/Private/{conversationMessage.UploadedFiles.First().FileId}";
                    }

                    if (!string.IsNullOrEmpty(conversationMessage.MessageContent))
                    {
                        if (conversationMessage.MessageContent.Contains(Environment.NewLine))
                        {
                            conversationMessage.MessageContent = conversationMessage.MessageContent
                                .Replace(Environment.NewLine, "  ");
                        }

                        if (conversationMessage.MessageContent.Contains("\r"))
                        {
                            conversationMessage.MessageContent = conversationMessage.MessageContent
                                .Replace("\r", string.Empty);
                        }

                        if (conversationMessage.MessageContent.Contains("\""))
                        {
                            conversationMessage.MessageContent = conversationMessage.MessageContent
                                .Replace("\"", "\"\"");
                        }
                    }

                    var record =
                        $"\"{conversationMessage.Id}\",\"{conversationMessage.Channel}\",\"{conversationMessage.MessageType}\",\"{sender}\",\"{receiver}\",\"{conversationMessage.MessageContent}\",\"{mediaType}\",\"{mediaURL}\",\"{conversationMessage.CreatedAt.ToString("u")}\"";
                    exportResult += $"{record}\n";

                    if (conversationMessage.Channel != ChannelTypes.Note)
                    {
                        chaanel = ConversationHelper.GetChannelName(conversationMessage.Channel);
                    }
                }

                return File(
                    Encoding.UTF8.GetBytes(exportResult),
                    "text/csv",
                    $"{conversation.UserProfile.FirstName} ({chaanel}) Exported at {DateTime.UtcNow.Date.ToString("d")}.csv");
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[{MethodName} endpoint] Conversation {ConversationId} export fail: {ExceptionMessage}",
                    nameof(ExportConversations),
                    exportConversationViewModel.ConversationId,
                    ex.Message);
            }

            return BadRequest(
                new ResponseViewModel
                {
                    message = "error"
                });
        }

        /// <summary>
        /// V3 Get conversation count summary details for open, pending and closed; hashtags summary; unread summary (Will exclude the collaborator from assignedToMe).
        /// </summary>
        /// <param name="assignedTo"><b>all</b>: List all manageable conversations (For <b>Admin</b> and <b>TeamAdmin</b> role only)<br />
        /// <b>unassigned</b>: List all unassigned conversations (For <b>admin</b> can see all unassigned chat; <b>TeamAdmin</b> and <b>Staff</b> can see unassigned chat under their teams)<br />
        /// <b>mentioned</b>: List all conversations that mentioned you<br />
        /// <b>collaborator</b>: List all conversations that you are collaborator<br />
        /// <b>team</b>: List all team conversations or team unassigned chat, if <b>isTeamUnassigned</b> = true then only return the team unassigned chat<br />
        /// <b>{staffId}</b>: List all conversations assigned to this user like:. <b>77b68a4e-d1a5-4433-a161-a768c06a6988</b></param>
        /// <param name="status">[ <b>open, pending, closed, scheduled</b> ].</param>
        /// <param name="channels">General channel name like: <b>whatsapp,whatsapp360dialog,facebook</b> ;Separated by. <b>,</b></param>
        /// <param name="afterUpdatedAt">Get conversations that updated time is after this value (UTC).</param>
        /// <param name="afterModifiedAt">Get conversations that modified time is after this value (UTC).</param>
        /// <param name="channelIds">Specify the channel id like:<br />
        /// Twilio: AC1fc368e79a37f6a9e87657dd3d75a2e5;whatsapp:+8526452244<br />
        /// 360Dialog: 175.
        /// </param>
        /// <param name="tags">Label filter</param>
        /// <param name="teamId">TeamId, assignedTo = team</param>
        /// <param name="isTeamUnassigned">Show only unassigned chats</param>
        /// <param name="isUnread">Show only is unread chats</param>
        /// <param name="isCollaborator">(Optional) Show collaborating chats</param>
        /// <param name="behaviourVersion"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("v3/conversation/summary/{assignedTo}")]
        public async Task<ActionResult<ConversationSummary>> GetV3ConversationSummary(
            string assignedTo = "all",
            [FromQuery(Name = "status")]
            string status = "open",
            [FromQuery(Name = "channel")]
            string channels = null,
            [FromQuery(Name = "afterUpdatedAt")]
            DateTime? afterUpdatedAt = null,
            [FromQuery(Name = "afterModifiedAt")]
            DateTime? afterModifiedAt = null,
            [FromQuery(Name = "channelIds")]
            string channelIds = null,
            [FromQuery(Name = "tags")]
            string tags = null,
            [FromQuery(Name = "teamId")]
            long? teamId = null,
            [FromQuery(Name = "isTeamUnassigned")]
            bool? isTeamUnassigned = null,
            [FromQuery(Name = "isUnread")]
            bool? isUnread = null,
            [FromQuery(Name = "isCollaborator")]
            bool? isCollaborator = null,
            [FromQuery(Name = "behaviourVersion")]
            string behaviourVersion = "2",
            CancellationToken cancellationToken = default)
        {
            var companyUser = await _coreService.GetCompanyStaff(
                await _userManager.GetUserAsync(User));

            if (companyUser == null)
            {
                return Unauthorized();
            }

            var results = new ConversationSummary();

            var assignedConversationV3CacheKeyPattern = new AssignedConversationV3CacheKeyPattern(companyUser.Id, status,
                assignedTo, channels, afterUpdatedAt, afterModifiedAt, channelIds,
                tags, teamId, isTeamUnassigned, isUnread, behaviourVersion);
            var data = await _cacheManagerService.GetCacheAsync(assignedConversationV3CacheKeyPattern);

            if (!string.IsNullOrEmpty(data))
            {
                return Ok(JsonConvert.DeserializeObject<ConversationSummary>(data));
            }

            var myConversation = await _conversationMessageService.GetConversations(
                companyUser.CompanyId,
                companyUser,
                "all",
                assignedTo,
                channels,
                afterUpdatedAt,
                afterModifiedAt,
                channelIds,
                tags,
                teamId,
                isTeamUnassigned,
                isUnread,
                version: behaviourVersion,
                isCollaborator: isCollaborator);

            var myCount = await _conversationSummaryService.GetV3ConversationSummaryCountAsync(
                myConversation,
                status,
                assignedTo,
                "assigned",
                teamId,
                cancellationToken);

            results.ConversationSummaries.AddRange(myCount);

            try
            {
                myConversation = await _conversationMessageService.GetConversations(
                    companyUser.CompanyId,
                    companyUser,
                    status,
                    assignedTo,
                    channels,
                    afterUpdatedAt,
                    afterModifiedAt,
                    channelIds,
                    tags,
                    teamId,
                    isTeamUnassigned,
                    isUnread,
                    version: behaviourVersion,
                    isCollaborator: isCollaborator);

                var hashtagSummaries = await _conversationSummaryService.GetHashtagSummaryCount(
                    myConversation,
                    companyUser.CompanyId,
                    cancellationToken);

                results.HashtagSummaries.AddRange(hashtagSummaries);
            }
            catch (TaskCanceledException)
            {
                throw;
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[{MethodName} endpoint] Error getting hashtag summary count for key {Key}: {ExceptionMessage}",
                    nameof(GetV3ConversationSummary),
                    $"assigned_{assignedConversationV3CacheKeyPattern.GenerateKeyPattern()}",
                    ex.Message);

                return BadRequest(
                    new ResponseViewModel
                    {
                        message = ex.Message
                    });
            }

            await _cacheManagerService.SaveCacheAsync(
                assignedConversationV3CacheKeyPattern,
                results);

            return Ok(results);
        }

        /// <summary>
        /// To get personal unread summary for assignedToMe and collaborator.
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("v3/conversation/unreadSummary")]
        [ServiceFilter(typeof(ReadOnlyEndpointAttribute))]
        public async Task<ActionResult<ConversationUnreadSummary>> GetUnreadSummary(CancellationToken cancellationToken = default)
        {
            var applicationUser = await _sleekflowUserService.GetUserAsync(User);
            var companyUser = await _coreService.GetCompanyStaff(applicationUser);

            if (companyUser == null)
            {
                return Unauthorized();
            }

            var results = await _conversationSummaryService.GetUnreadConversationSummaryAsync(
                companyUser.CompanyId,
                companyUser.Id,
                cancellationToken);

            return Ok(results);
        }

        /// <summary>
        /// Call this API when you read the conversation.
        /// </summary>
        /// <param name="conversationId">Targeted ConversationId.</param>
        /// <returns></returns>
        [HttpPost]
        [Route("v3/conversation/read/{conversationId}")]
        public async Task<ActionResult<ResponseViewModel>> ReadConversationPersonally(string conversationId)
        {
            var companyUser = await _coreService.GetCompanyStaff(
                await _userManager.GetUserAsync(User));

            if (companyUser == null)
            {
                return Unauthorized();
            }

            await _conversationMessageService.ReadConversationPersonally(conversationId, companyUser);

            return Ok(
                new ResponseViewModel()
                {
                    message = "success"
                });
        }

        /// <summary>
        /// Set the conversation to unread.
        /// </summary>
        /// <param name="conversationId">Targeted ConversationId.</param>
        /// <returns></returns>
        [HttpPost]
        [Route("v3/conversation/unread/{conversationId}")]
        public async Task<ActionResult<ResponseViewModel>> UnreadConversationPersonally(string conversationId)
        {
            var companyUser = await _coreService.GetCompanyStaff(
                await _userManager.GetUserAsync(User));

            if (companyUser == null)
            {
                return Unauthorized();
            }

            var responseVm = await _conversationMessageService.MarkConversationAsUnread(
                conversationId,
                companyUser);

            return Ok(responseVm);
        }

        /// <summary>
        /// Check the conversation is accessible by the user.
        /// </summary>
        /// <param name="request">Targeted ConversationId.</param>
        /// <returns></returns>
        [HttpPost]
        [Route("conversation/accessible")]
        public async Task<ActionResult<ResponseViewModel<IsConversationAccessibleResult>>> IsConversationAccessibleToUser(
            [FromBody]
            IsConversationAccessibleRequest request)
        {
            try
            {
                var companyUser = await _coreService.GetCompanyStaff(
                    await _userManager.GetUserAsync(User));

                if (companyUser == null)
                {
                    return Unauthorized();
                }

                var aggregatedConversation = await _accessControlAggregationService
                    .GetAggregatedConversationAsync(conversationId: request.ConversationId);
                var aggregatedStaff = await _accessControlAggregationService.GetStaffAccessControlAggregateAsync(staff: companyUser);
                _logger.LogInformation("[{MethodName}] Conversation {ConversationId} Staff:{StaffPayload}, conversation team Id:{ConversationTeamId}, conversation collaborator:{ConversationCollaborator}, conversation assignee:{ConversationAssignee}",
                    nameof(IsConversationAccessibleToUser),
                    request.ConversationId,
                    JsonConvert.SerializeObject(aggregatedStaff, new JsonSerializerSettings
                    {
                        PreserveReferencesHandling = PreserveReferencesHandling.Objects
                    }),
                    aggregatedConversation.AssignedTeamId,
                    aggregatedConversation.AdditionalAssignees.Select(col => col.AssigneeId).ToList(),
                    aggregatedConversation.AssigneeId);
                var hasControl = _conversationAccessControlManager.HasAccess(aggregatedStaff, aggregatedConversation);
                var result = new IsConversationAccessibleResult(hasControl);

                return Ok(new ResponseViewModel<IsConversationAccessibleResult>(result, 200));
            }
            catch (Exception e)
            {
                return Ok(new ResponseViewModel<IsConversationAccessibleResult>(
                    new IsConversationAccessibleResult(true),
                    500)
                {
                    ErrorMessage = e.Message
                });
            }
        }
    }
}