using System;
using System.Collections.Generic;
using Travis_backend.SubscriptionPlanDomain.Models;

namespace Travis_backend.SubscriptionPlanDomain.Repositories;

public interface IFeatureRepository
{
    List<Feature> GetAllAsync();
}

public class FeatureRepository : IFeatureRepository
{
    private readonly List<Feature> _allFeatures = new List<Feature>
    {
        new Feature(
            "whatsapp_phone_number",
            "whatsapp_phone_number",
            "description",
            new List<string>(),
            true,
            string.Empty,
            string.Empty,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new Feature(
            "shopify",
            "shopify",
            "shopify subscription features including integration",
            new List<string>(),
            true,
            string.Empty,
            string.Empty,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new Feature(
            "whatsapp",
            "whatsapp",
            string.Empty,
            new List<string>(),
            true,
            string.Empty,
            string.Empty,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new Feature(
            "contacts",
            "contacts",
            "capability to create contacts",
            new List<string>(),
            true,
            string.Empty,
            string.Empty,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new Feature(
            "sensitive_data_masking",
            "sensitive_data_masking",
            string.Empty,
            new List<string>(),
            true,
            string.Empty,
            string.Empty,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new Feature(
            "support_and_consultation",
            "support_and_consultation",
            string.Empty,
            new List<string>(),
            true,
            string.Empty,
            string.Empty,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new Feature(
            "priority_support",
            "priority_support",
            string.Empty,
            new List<string>(),
            true,
            string.Empty,
            string.Empty,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new Feature(
            "consultation",
            "consultation",
            string.Empty,
            new List<string>(),
            true,
            string.Empty,
            string.Empty,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new Feature(
            "chatbot_automation_support_basic",
            "chatbot_automation_support_basic",
            "basic chatbot automation support",
            new List<string>(),
            true,
            string.Empty,
            string.Empty,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new Feature(
            "chatbot_automation_support_intermediate",
            "chatbot_automation_support_intermediate",
            "intermediate chatbot automation support",
            new List<string>(),
            true,
            string.Empty,
            string.Empty,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new Feature(
            "chatbot_automation_support_advanced",
            "chatbot_automation_support_advanced",
            "advanced chatbot automation support",
            new List<string>(),
            true,
            string.Empty,
            string.Empty,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new Feature(
            "chatbot_automation_support_enterprise",
            "chatbot_automation_support_enterprise",
            "enterprise chatbot automation support",
            new List<string>(),
            true,
            string.Empty,
            string.Empty,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new Feature(
            "internal_custom_subscription_payment",
            "internal_custom_subscription_payment",
            "internal custom subscription payment",
            new List<string>(),
            true,
            string.Empty,
            string.Empty,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new Feature(
            "onboarding_support",
            "onboarding_support",
            "client onboarding support",
            new List<string>(),
            true,
            string.Empty,
            string.Empty,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new Feature(
            "payment_integration",
            "payment_integration",
            "payment integration",
            new List<string>(),
            true,
            string.Empty,
            string.Empty,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new Feature(
            "salesforce_commerce_cloud",
            "salesforce_commerce_cloud",
            "salesforce commerce cloud integration",
            new List<string>(),
            true,
            string.Empty,
            string.Empty,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new Feature(
            "salesforce_integration",
            "salesforce_integration",
            "salesforce integration",
            new List<string>(),
            true,
            string.Empty,
            string.Empty,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new Feature(
            "salesforce_marketing_cloud",
            "salesforce_marketing_cloud",
            "salesforce marketing cloud integration",
            new List<string>(),
            true,
            string.Empty,
            string.Empty,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new Feature(
            "hubspot",
            "hubspot",
            "hubspot integration",
            new List<string>(),
            true,
            string.Empty,
            string.Empty,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new Feature(
            "channels",
            "channels",
            "capability to integrate channels",
            new List<string>(),
            true,
            string.Empty,
            string.Empty,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new Feature(
            "whatsapp_qr_code",
            "whatsapp_qr_code",
            "whatsapp qr code",
            new List<string>(),
            true,
            string.Empty,
            string.Empty,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new Feature(
            "messages",
            "messages",
            "capability to send messages",
            new List<string>(),
            true,
            string.Empty,
            string.Empty,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new Feature(
            "agents",
            "agents",
            "capability to signup agents",
            new List<string>(),
            true,
            string.Empty,
            string.Empty,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new Feature(
            "campaigns",
            "campaigns",
            "capability to send campaigns",
            new List<string>(),
            true,
            string.Empty,
            string.Empty,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new Feature(
            "automations",
            "automations",
            "capability to create automations",
            new List<string>(),
            true,
            string.Empty,
            string.Empty,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new Feature(
            "api_calls",
            "api_calls",
            "capability to make API calls",
            new List<string>(),
            true,
            string.Empty,
            string.Empty,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new Feature(
            "ai_features_total_usage",
            "ai_features_total_usage",
            "capability to use AI related features",
            new List<string>(),
            true,
            string.Empty,
            string.Empty,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
    };

    public List<Feature> GetAllAsync()
    {
        return _allFeatures;
    }
}