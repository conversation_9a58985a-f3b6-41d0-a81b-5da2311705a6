using Pulumi;
using Pulumi.AzureNative.Resources;
using Pulumi.AzureNative.Web;
using Sleekflow.Core.Infra.Perf.Constants;
using Cache = Pulumi.AzureNative.Cache;

namespace Sleekflow.Core.Infra.Perf.Components.Models;

public class EnvGroup
{
    public string LocationName { get; set; }

    public Cache.Redis Redis { get; set; }

    public ResourceGroup ResourceGroup { get; set; }

    public Dictionary<string, WebApp> WebApps { get; set; }

    public EnvGroup(
        string locationName,
        Cache.Redis redis,
        ResourceGroup resourceGroup,
        Dictionary<string, WebApp> webApps)
    {
        LocationName = locationName;
        Redis = redis;
        ResourceGroup = resourceGroup;
        WebApps = webApps;
    }

    public virtual string FormatAppName(string appName)
    {
        var containerAppName = $"sleekflow-{appName}-app";
        return $"{containerAppName}-{LocationNames.GetShortName(LocationName)}";
    }

    public Output<string> FormatSfEnvironment()
    {
        return FormatSfEnvironment(Output.Create(LocationName));
    }

    protected virtual Output<string> FormatSfEnvironment(Output<string> locationName)
    {
        return locationName;
    }
}