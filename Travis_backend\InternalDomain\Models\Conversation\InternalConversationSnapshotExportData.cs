using System;
using System.Text.RegularExpressions;
using CsvHelper.Configuration.Attributes;
using Travis_backend.Constants;
using Travis_backend.Helpers;
using Travis_backend.MessageDomain.ViewModels;

namespace Travis_backend.InternalDomain.Models.Conversation;

public class InternalConversationSnapshotExportDto : ConversationMessageResponseViewModel
{
    public string SmsSenderNumber { get; set; }

    public string Whatsapp360DialogSenderNumber { get; set; }

    private string WhatsappCloudApiSenderNumber => IsSentFromSleekflow
        ? WhatsappCloudApiReceiver?.WhatsappChannelPhoneNumber
        : WhatsappCloudApiSender?.WhatsappId;

    private string WhatsappTwilioSenderNumber
    {
        get
        {
            const string pattern = @"whatsapp:\+(?<number>\d+)";

            if (!IsSentFromSleekflow)
            {
                return whatsappSender?.phone_number;
            }

            if (whatsappReceiver is null)
            {
                return null;
            }

            var match = Regex.Match(whatsappReceiver.InstanceId, pattern);

            return !match.Success
                ? new string(whatsappReceiver.InstanceId)
                : match.Groups["number"].Value;
        }
    }

    public DateTime DisplayDateTime { get; set; }

    [Name("DateTime (ascending order)")]
    public string FormattedDisplayDateTime => DisplayDateTime.ToString("yyyy-MM-dd HH:mm:ss");

    [Name("Conversation Id")]
    public string ConversationId => base.ConversationId;

    // If no phone number -> Nil
    [Name("Sender number")]
    public string SenderNumber
    {
        get
        {
            return base.Channel.ToLower() switch
            {
                ChannelTypes.Sms => SmsSenderNumber,
                ChannelTypes.Note => "Nil",
                ChannelTypes.Facebook => "Nil",
                ChannelTypes.Instagram => "Nil",
                ChannelTypes.Wechat => "Nil",
                ChannelTypes.Line => "Nil",
                ChannelTypes.LiveChat => "Nil",
                ChannelTypes.Viber => "Nil",
                ChannelTypes.Telegram => "Nil",
                ChannelTypes.WhatsappTwilio => WhatsappTwilioSenderNumber,
                ChannelTypes.Whatsapp360Dialog => Whatsapp360DialogSenderNumber,
                ChannelTypes.WhatsappCloudApi => WhatsappCloudApiSenderNumber,
                _ => null
            };
        }
    }

    [Name("Sender name")]
    public string SenderName { get; set; }

    // If the message is sent from SleekFlow then it is an outbound message
    // If the message is sent from end user then it is an inbound message
    [Name("Inbound/outbound")]
    public string InboundOutbound => IsSentFromSleekflow ? "outbound" : "inbound";

    [Name("Message type")]
    public new string MessageType => base.MessageType;

    [Name("Text")]
    public string Text => MessageContent;

    // If null -> empty
    [Name("Media URL")]
    public string MediaUrl { get; set; }

    // Channel Type -> Whatsapp (whatsappcloudapi, whatsapp360dialog, whatsapp (Twilio) ), Wechat, etc.
    [Name("Channel")]
    public new string Channel => ConversationHelper.GetChannelName(base.Channel);

    // Channel name
    [Name("Channel name")]
    public new string ChannelName { get; set; }
}