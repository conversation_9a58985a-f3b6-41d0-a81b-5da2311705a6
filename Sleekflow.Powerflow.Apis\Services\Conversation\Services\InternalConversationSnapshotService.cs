using Microsoft.EntityFrameworkCore;
using Sleekflow.Powerflow.Apis.Services.Conversation.Repositories;
using Travis_backend.Database;
using Travis_backend.Helpers;
using Travis_backend.MessageDomain.Models;

namespace Sleekflow.Powerflow.Apis.Services.Conversation.Services;

public interface IInternalConversationSnapshotService
{
    Task<List<ConversationMessage>> FindConversationSnapshotsAsync(
        string companyId,
        string conversationId = null,
        DateTimeOffset? startDate = null,
        DateTimeOffset? endDate = null,
        int? offset = null,
        int? limit = null);
}

public class InternalConversationSnapshotService : IInternalConversationSnapshotService
{
    private readonly ApplicationDbContext _appDbContext;
    private readonly IInternalConversationRepository _internalConversationRepository;

    public InternalConversationSnapshotService(
        ApplicationDbContext appDbContext,
        IInternalConversationRepository internalConversationRepository)
    {
        _appDbContext = appDbContext;
        _internalConversationRepository = internalConversationRepository;
    }

    public async Task<List<ConversationMessage>> FindConversationSnapshotsAsync(
        string companyId,
        string conversationId = null,
        DateTimeOffset? startDate = null,
        DateTimeOffset? endDate = null,
        int? offset = null,
        int? limit = null)
    {
        offset ??= 0;

        var history = new List<ConversationMessage>();

        var timeZoneId = await _appDbContext.CompanyCompanies
            .Where(x => x.Id == companyId)
            .Select(x => x.TimeZoneInfoId)
            .FirstOrDefaultAsync();

        startDate = startDate?.SetTimeZoneUtcOffset(timeZoneId);
        endDate = endDate?.SetTimeZoneUtcOffset(timeZoneId);

        while (true)
        {
            // Get 500 records each time
            var conversationMessages = await _internalConversationRepository.FindConversationMessagesAsync(
                companyId: companyId,
                conversationId: conversationId,
                startDate: startDate,
                endDate: endDate,
                offset: offset,
                limit: 500,
                enableNoTracking: true);

            history.AddRange(conversationMessages);
            offset += conversationMessages.Count;

            if (conversationMessages.Count == 0)
            {
                break;
            }

            if (!limit.HasValue || history.Count < limit)
            {
                continue;
            }

            history = history.Take(limit.Value).ToList();

            break;
        }

        return history;
    }
}