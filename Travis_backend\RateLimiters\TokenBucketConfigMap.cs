﻿using System.Collections.Generic;
using System.Net.Http;
using Travis_backend.RateLimiters.Models;

namespace Travis_backend.RateLimiters;

public class TokenBucketConfigMap : RateLimiterConfigMap
{
    public TokenBucketConfigMap()
    {
        BypassRateLimiterPath = new HashSet<string>()
        {
            new RequestInfo(HttpMethod.Get, "app/feature-info").ToString(),
            new RequestInfo(HttpMethod.Get, "ipaddress").ToString(),
            new RequestInfo(HttpMethod.Get, "location-info").ToString(),
            new RequestInfo(HttpMethod.Get, "timezone").ToString(),
            new RequestInfo(HttpMethod.Get, "api/HealthCheck").ToString(),
            new RequestInfo(HttpMethod.Get, "error").ToString(),
            new RequestInfo(HttpMethod.Post, "error").ToString(),
            new RequestInfo(HttpMethod.Delete, "error").ToString(),
            new RequestInfo(HttpMethod.Put, "error").ToString(),
            new RequestInfo(HttpMethod.Patch, "error").ToString(),
            new RequestInfo(HttpMethod.Options, "error").ToString(),
        };

        BypassRateLimiterPrefix = new HashSet<string>()
        {
            "/FlowHub/"
        };

        ExactPathRateLimiterOptions = new Dictionary<string, IRateLimiterOption>
        {
            {
                new RequestInfo(HttpMethod.Get, "Company/StaffOverviews").ToString(),
                new TokenBucketLimiterOption(100, 1, false)
            },
            {
                new RequestInfo(HttpMethod.Post, "UserProfile/Search").ToString(),
                new TokenBucketLimiterOption(100, 1, true)
            }
        };

        PathPatternRateLimiterOptions = new Dictionary<string, IRateLimiterOption>
        {
            {
                new RequestInfo(HttpMethod.Get, "Company/Staff/{staffId}").ToString(),
                new TokenBucketLimiterOption(100, 1, false)
            },
            {
                new RequestInfo(HttpMethod.Get, "subscription/add-ons").ToString(),
                new TokenBucketLimiterOption(100, 1, false)
            },
            {
                new RequestInfo(HttpMethod.Post, "Company/Automation/{fbIgAutoReplyId}/dmreplay").ToString(),
                new TokenBucketLimiterOption(100, 1, false)
            },
            {
                new RequestInfo(HttpMethod.Post, "api/conversation/{conversationId}/collaborators").ToString(),
                new TokenBucketLimiterOption(100, 1, false)
            },
        };

        DefaultLimiterOption = new TokenBucketLimiterOption(3000, 1, false);
    }
}