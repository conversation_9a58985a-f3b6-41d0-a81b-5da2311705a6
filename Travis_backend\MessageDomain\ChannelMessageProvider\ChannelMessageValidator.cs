using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.Constants;
using Travis_backend.Database;
using Travis_backend.Enums;
using Travis_backend.MessageDomain.Models;
using Travis_backend.MessageDomain.Repositories;
using Travis_backend.MessageDomain.Services;

namespace Travis_backend.MessageDomain.ChannelMessageProvider;

public interface IChannelMessageValidator
{
    ValueTask<(bool IsValid, string ErrorMessage)> ValidateConversationMessage(ConversationMessage message);

    ValueTask<(bool IsValid, int ErrorCode, string ErrorMessage)> ValidateConversationMessageChannel(
        ConversationMessage message,
        Staff staff);
}

public class ChannelMessageValidator : IChannelMessageValidator
{
    private readonly ILogger<ChannelMessageValidator> _logger;
    private readonly ApplicationDbContext _appDbContext;
    private readonly IMessagingChannelService _channelService;
    private readonly IChannelIdentityIdRepository _channelIdentityIdRepository;

    public ChannelMessageValidator(
        ILogger<ChannelMessageValidator> logger,
        ApplicationDbContext appDbContext,
        IMessagingChannelService channelService,
        IChannelIdentityIdRepository channelIdentityIdRepository)
    {
        _logger = logger;
        _appDbContext = appDbContext;
        _channelService = channelService;
        _channelIdentityIdRepository = channelIdentityIdRepository;
    }

    public async ValueTask<(bool IsValid, string ErrorMessage)> ValidateConversationMessage(ConversationMessage message)
    {
        if (!string.IsNullOrEmpty(message.MessageChecksum)
            && await _appDbContext.ConversationMessages
                .AnyAsync(
                    x =>
                        x.CompanyId == message.CompanyId
                        && x.MessageChecksum == message.MessageChecksum))
        {
            return (false, $"message with checksum: {message.MessageChecksum} sent before");
        }


        switch (message.Channel)
        {
            case ChannelTypes.Facebook:
            case ChannelTypes.Instagram:
                if (message.MessageType == "interactive" && message.ExtendedMessagePayload == null)
                {
                    return (false, "Please Add Extend Message Payload for Interactive Message");
                }

                break;
            case ChannelTypes.WhatsappCloudApi:
                if (message.MessageType == "template"
                    && message.ExtendedMessagePayload
                        ?.ExtendedMessagePayloadDetail?.WhatsappCloudApiTemplateMessageObject == null)
                {
                    return (false, "Please Add Extend Message Payload for template Message");
                }

                if (message.MessageType == "interactive"
                    && message.ExtendedMessagePayload
                        ?.ExtendedMessagePayloadDetail?.WhatsappCloudApiInteractiveObject == null)
                {
                    return (false, "Please Add Extend Message Payload for template Message");
                }

                break;
            case ChannelTypes.Whatsapp360Dialog:
                if (message.MessageType == "template"
                    && message.Whatsapp360DialogExtendedMessagePayload
                        ?.Whatsapp360DialogTemplateMessage == null)
                {
                    return (false, "Please Add Extend Message Payload for template Message");
                }

                if (message.MessageType == "interactive"
                    && message.Whatsapp360DialogExtendedMessagePayload
                        ?.Whatsapp360DialogInteractiveObject == null)
                {
                    return (false, "Please Add Extend Message Payload for interactive Message");
                }

                break;
        }

        if (message.MessageType == "text" && string.IsNullOrWhiteSpace(message.MessageContent))
        {
            return (false, "Message Content cannot be empty");
        }

        return (true, null);
    }

    public async ValueTask<(bool IsValid, int ErrorCode, string ErrorMessage)> ValidateConversationMessageChannel(
        ConversationMessage message,
        Staff staff)
    {
        if (message.Channel is ChannelTypes.Note or ChannelTypes.LiveChat)
        {
            return (true, 0, null);
        }

        if (string.IsNullOrEmpty(message.Channel) && string.IsNullOrEmpty(message.ChannelIdentityId))
        {
            return (false, ErrorCodeConstant.SfConversationMessageInvalidDefaultChannelIdentity, "Missing Channel Type or ChannelIdentityId");
        }

        if (!await _channelService.IsChannelExistedAsync(
                message.CompanyId,
                message.Channel,
                message.ChannelIdentityId))
        {
            return (false, ErrorCodeConstant.SfConversationMessageInvalidDefaultChannel, "Channel does not existed");
        }

        if (!await ValidateChannelPermission(
                staff,
                message.Channel,
                message.ChannelIdentityId))
        {
            return (false, ErrorCodeConstant.SfConversationMessageNoDefaultChannelPermission, "Do not have permission to use this channel");
        }

        return (true, 0, null);
    }

    private async Task<bool> ValidateChannelPermission(
        Staff staff,
        string channelType,
        string channelIdentityId)
    {
        if (staff.RoleType != StaffUserRole.Admin)
        {
            var rolePermission = await _appDbContext.CompanyRolePermissions
                .Where(x => x.CompanyId == staff.CompanyId && x.StaffUserRole == staff.RoleType)
                .FirstOrDefaultAsync();

            if (rolePermission != null && rolePermission.Permission.IsShowDefaultChannelMessagesOnly)
            {
                var staffInTeamIds = await _appDbContext.CompanyTeamMembers
                    .Where(x => x.StaffId == staff.Id)
                    .Select(x => x.CompanyTeamId)
                    .ToListAsync();

                var teamDefaultChannelList = await _appDbContext.CompanyStaffTeams
                    .AsNoTracking()
                    .Where(x => staffInTeamIds.Contains(x.Id))
                    .Select(x => x.DefaultChannels)
                    .ToListAsync();

                if (!teamDefaultChannelList.Any(x => x.Any(y => y.ids.Any())))
                {
                    return true;
                }

                switch (channelType)
                {
                    case ChannelTypes.Facebook:
                    case ChannelTypes.Instagram:
                    case ChannelTypes.WhatsappCloudApi:
                    case ChannelTypes.WhatsappTwilio:
                    {
                        var channels = await _channelIdentityIdRepository.GetChannelIdByChannelIdentityId(
                            channelType,
                            new List<string>
                            {
                                channelIdentityId
                            });
                        return teamDefaultChannelList.Any(
                            x =>
                                x.Any(
                                    y =>
                                        y.channel == channelType && y.ids.Intersect(channels).Any()));
                    }

                    case ChannelTypes.Whatsapp360Dialog:
                    {
                        var channels = await _appDbContext.ConfigWhatsApp360DialogConfigs
                            .Where(x => x.ChannelIdentityId == channelIdentityId)
                            .Select(x => x.Id.ToString())
                            .ToListAsync();

                        return teamDefaultChannelList.Any(
                            x =>
                                x.Any(
                                    y =>
                                        y.channel == channelType && y.ids.Intersect(channels).Any()));
                    }

                    case ChannelTypes.Email:
                    case ChannelTypes.Line:
                    case ChannelTypes.Note:
                    case ChannelTypes.Sms:
                    case ChannelTypes.Telegram:
                    case ChannelTypes.Wechat:
                    case ChannelTypes.Viber:
                    case ChannelTypes.LiveChat:
                        return true;

                    default:
                        return false;
                }
            }
        }

        return true;
    }
}