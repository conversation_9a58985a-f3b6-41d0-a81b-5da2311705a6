﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;
using AutoMapper;
using Hangfire;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.OpenApi.Extensions;
using Newtonsoft.Json;
using Sleekflow.Apis.AuditHub.Model;
using Travis_backend.Cache;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.ConversationDomain.Models;
using Travis_backend.ConversationServices;
using Travis_backend.Database;
using Travis_backend.Enums;
using Travis_backend.FlowHubs;
using Travis_backend.FlowHubs.Models;
using Travis_backend.MessageDomain.ViewModels;
using Travis_backend.SignalR;

namespace Travis_backend.ConversationDomain.Services
{
    public interface IConversationHashtagService
    {
        Task<Conversation> SetConversationHashtag(
            string companyId,
            string conversationId,
            long? staffId,
            List<ConversationHashtagViewModel> conversationTagViewModels,
            bool isTriggerAutomation = true);

        Task<Conversation> AddConversationHashtag(
            string companyId,
            string conversationId,
            long? staffId,
            List<ConversationHashtagViewModel> conversationTagViewModels,
            bool isTriggerAutomation = true);

        Task<Conversation> RemoveConversationHashtag(
            string companyId,
            string conversationId,
            long? staffId,
            List<ConversationHashtagViewModel> conversationTagViewModels,
            bool isTriggerAutomation = true);
    }

    public class ConversationHashtagService : IConversationHashtagService
    {
        private readonly ApplicationDbContext _appDbContext;
        private readonly ILogger _logger;
        private readonly ICacheManagerService _cacheManagerService;
        private readonly ILockService _lockService;
        private readonly ICompanyInfoCacheService _companyInfoCacheService;
        private readonly IUserProfileHooks _userProfileHooks;
        private readonly ISignalRService _signalRService;

        public ConversationHashtagService(
            ApplicationDbContext appDbContext,
            ILogger<ConversationHashtagService> logger,
            ICacheManagerService cacheManagerService,
            ILockService lockService,
            ICompanyInfoCacheService companyInfoCacheService,
            IUserProfileHooks userProfileHooks,
            ISignalRService signalRService)
        {
            _appDbContext = appDbContext;
            _logger = logger;
            _cacheManagerService = cacheManagerService;
            _lockService = lockService;
            _companyInfoCacheService = companyInfoCacheService;
            _userProfileHooks = userProfileHooks;
            _signalRService = signalRService;
        }

        public async Task<Conversation> RemoveConversationHashtag(
            string companyId,
            string conversationId,
            long? staffId,
            List<ConversationHashtagViewModel> conversationTagViewModels,
            bool isTriggerAutomation = true)
        {
            var conversation = await _appDbContext.Conversations
                .FirstOrDefaultAsync(
                    c =>
                        c.CompanyId == companyId
                        && c.Id == conversationId);

            if (conversation == null)
            {
                throw new Exception("conversation not found");
            }

            var checksum = GenerateChecksumForConversationTagViewModels(conversationTagViewModels);

            var addConversationLock = await _lockService.AcquireLockAsync(
                $"remove_conversation_hashtag_{conversationId}_{checksum}",
                TimeSpan.FromSeconds(2));

            if (addConversationLock == null)
            {
                return conversation;
            }

            var labelsRemoved = new List<LabelLogData>();
            var isChanged = false;

            foreach (var userProfileCustomField in conversationTagViewModels)
            {
                var companyHashTag = await _appDbContext.CompanyDefinedHashtags
                    .FirstOrDefaultAsync(
                        x =>
                            x.CompanyId == companyId
                            && x.Hashtag == userProfileCustomField.HashtagNormalized);

                if (companyHashTag == null)
                {
                    continue;
                }

                _logger.LogInformation(
                    "[RemovedConversationLabel] {CompanyId} {StaffId} {UserProfileId} {ConversationId} {Label}",
                    conversation.CompanyId,
                    staffId,
                    conversation.UserProfileId,
                    conversation.Id,
                    userProfileCustomField.Hashtag);

                await _appDbContext.ConversationHashtags.Where(
                        x => x.CompanyId == companyId &&
                             x.ConversationId == conversationId && x.HashtagId == companyHashTag.Id)
                    .ExecuteDeleteAsync();

                isChanged = true;
                labelsRemoved.Add(new LabelLogData(companyHashTag.Hashtag));
            }

            conversation.conversationHashtags = await _appDbContext.ConversationHashtags
                .Include(conversationHashtag => conversationHashtag.Hashtag)
                .Where(x => x.CompanyId == companyId && x.ConversationId == conversationId)
                .OrderByDescending(x => x.Id)
                .ToListAsync();

            if (isChanged)
            {
                await _signalRService.SignalROnConversationStatusChanged(conversation);

                await _userProfileHooks.OnConversationLabelsRemovedAsync(
                    conversation.CompanyId,
                    conversation.UserProfileId,
                    staffId.ToString(),
                    () =>
                    {
                        var allHashtags = conversation.conversationHashtags
                            .Select(x => x.Hashtag)
                            .ToList();

                        var labelToHashtagDict = allHashtags
                            .GroupBy(ht => ht.Hashtag)
                            .ToDictionary(g => g.Key, g => g.First());

                        return Task.FromResult(
                            new OnConversationLabelsRemovedData(
                                labelsRemoved
                                    .Select(
                                        l => new LabelData(
                                            l.Label,
                                            labelToHashtagDict.GetValueOrDefault(l.Label)?
                                                .HashTagColor
                                                .GetDisplayName(),
                                            labelToHashtagDict.GetValueOrDefault(l.Label)?
                                                .HashTagType
                                                .GetDisplayName()))
                                    .ToList(),
                                allHashtags
                                    .Select(
                                        l => new LabelData(
                                            l.Hashtag,
                                            l.HashTagColor.GetDisplayName(),
                                            l.HashTagType.GetDisplayName()))
                                    .ToList()));
                    });

                if (isTriggerAutomation)
                {
                    BackgroundJob.Enqueue<IAutomationService>(
                        x =>
                            x.HashtagChangedTrigger(
                                conversation.UserProfileId,
                                conversationTagViewModels));
                }
            }

            await _cacheManagerService.DeleteCacheWithConstantKeyAsync($":1:conversation:{conversationId}");

            return conversation;
        }

        public async Task<Conversation> AddConversationHashtag(
            string companyId,
            string conversationId,
            long? staffId,
            List<ConversationHashtagViewModel> conversationTagViewModels,
            bool isTriggerAutomation = true)
        {
            var conversation = await _appDbContext.Conversations
                .FirstOrDefaultAsync(
                    c =>
                        c.CompanyId == companyId
                        && c.Id == conversationId);

            if (conversation == null)
            {
                throw new Exception("conversation not found");
            }

            var checksum = GenerateChecksumForConversationTagViewModels(conversationTagViewModels);

            var addConversationLock = await _lockService.AcquireLockAsync(
                $"add_conversation_hashtag_{conversationId}_{checksum}",
                TimeSpan.FromSeconds(2));

            if (addConversationLock == null)
            {
                return conversation;
            }

            var labelsAdded = new List<LabelLogData>();
            var isChanged = false;

            foreach (var userProfileCustomField in conversationTagViewModels)
            {
                if (string.IsNullOrEmpty(userProfileCustomField.Hashtag))
                {
                    continue;
                }

                _logger.LogInformation(
                    "[AddConversationLabel] {CompanyId} {StaffId} {UserProfileId} {ConversationId} {Label}",
                    conversation.CompanyId,
                    staffId,
                    conversation.UserProfileId,
                    conversation.Id,
                    userProfileCustomField.Hashtag);

                var companyHashTag = await _appDbContext.CompanyDefinedHashtags
                    .FirstOrDefaultAsync(
                        x =>
                            x.CompanyId == companyId
                            && x.Hashtag == userProfileCustomField.HashtagNormalized);

                if (companyHashTag == null)
                {
                    companyHashTag = new CompanyHashtag
                    {
                        CompanyId = companyId,
                        Hashtag = userProfileCustomField.Hashtag,
                        HashTagType = userProfileCustomField.HashTagType
                    };

                    await _appDbContext.CompanyDefinedHashtags.AddAsync(companyHashTag);
                    await _appDbContext.SaveChangesAsync();

                    await _companyInfoCacheService.RemoveCompanyInfoCache(companyId);
                }
                else if (companyHashTag.HashTagType == HashTagType.Normal
                         && userProfileCustomField.HashTagType == HashTagType.Shopify)
                {
                    companyHashTag.HashTagType = HashTagType.Shopify;
                }

                if (userProfileCustomField.HashTagColor.HasValue)
                {
                    companyHashTag.HashTagColor = userProfileCustomField.HashTagColor.Value;
                }

                //Check if the conversation already has the hashtag
                var conversationHashtag = await _appDbContext.ConversationHashtags
                    .AnyAsync(
                        x => x.CompanyId == companyId && x.ConversationId == conversationId &&
                             x.HashtagId == companyHashTag.Id);

                if (conversationHashtag)
                {
                    continue;
                }

                await _appDbContext.ConversationHashtags.AddAsync(
                    new ConversationHashtag()
                    {
                        ConversationId = conversationId, CompanyId = companyId, HashtagId = companyHashTag.Id
                    });

                // Save the changes
                await _appDbContext.SaveChangesAsync();

                isChanged = true;
                labelsAdded.Add(new LabelLogData(companyHashTag.Hashtag));
            }

            conversation.conversationHashtags = await _appDbContext.ConversationHashtags
                .Include(conversationHashtag => conversationHashtag.Hashtag)
                .Where(x => x.CompanyId == companyId && x.ConversationId == conversationId)
                .OrderByDescending(x => x.Id)
                .ToListAsync();

            if (isChanged)
            {
                await _signalRService.SignalROnConversationStatusChanged(conversation);

                await _userProfileHooks.OnConversationLabelsAddedAsync(
                    conversation.CompanyId,
                    conversation.UserProfileId,
                    staffId.ToString(),
                    () =>
                    {
                        var allHashtags = conversation.conversationHashtags
                            .Select(x => x.Hashtag)
                            .ToList();
                        var labelToHashtagDict = allHashtags
                            .GroupBy(ht => ht.Hashtag)
                            .ToDictionary(g => g.Key, g => g.First());

                        return Task.FromResult(
                            new OnConversationLabelsAddedData(
                                labelsAdded
                                    .Select(
                                        l => new LabelData(
                                            l.Label,
                                            labelToHashtagDict.GetValueOrDefault(l.Label)?
                                                .HashTagColor
                                                .GetDisplayName(),
                                            labelToHashtagDict.GetValueOrDefault(l.Label)?
                                                .HashTagType
                                                .GetDisplayName()))
                                    .ToList(),
                                allHashtags
                                    .Select(
                                        l => new LabelData(
                                            l.Hashtag,
                                            l.HashTagColor.GetDisplayName(),
                                            l.HashTagType.GetDisplayName()))
                                    .ToList()));
                    });

                if (isTriggerAutomation)
                {
                    BackgroundJob.Enqueue<IAutomationService>(
                        x =>
                            x.HashtagChangedTrigger(
                                conversation.UserProfileId,
                                conversationTagViewModels));
                }
            }

            await _cacheManagerService.DeleteCacheWithConstantKeyAsync($":1:conversation:{conversationId}");

            return conversation;
        }

        public async Task<Conversation> SetConversationHashtag(
            string companyId,
            string conversationId,
            long? staffId,
            List<ConversationHashtagViewModel> conversationTagViewModels,
            bool isTriggerAutomation = true)
        {
            var conversation = await _appDbContext.Conversations
                .FirstOrDefaultAsync(
                    c =>
                        c.CompanyId == companyId
                        && c.Id == conversationId);

            if (conversation == null)
            {
                throw new Exception("conversation not found");
            }

            var checksum = GenerateChecksumForConversationTagViewModels(conversationTagViewModels);

            var addConversationLock = await _lockService.AcquireLockAsync(
                $"set_conversation_hashtag_{conversationId}_{checksum}",
                TimeSpan.FromSeconds(2));

            if (addConversationLock == null)
            {
                return conversation;
            }

            var labelsAdded = new List<LabelLogData>();

            // if the labels are the same, return
            if ((await _appDbContext.ConversationHashtags
                    .Where(y => y.ConversationId == conversation.Id)
                    .Select(x => x.Hashtag.Hashtag)
                    .ToListAsync())
                .SequenceEqual(conversationTagViewModels.Select(x => x.HashtagNormalized)))
            {
                return conversation;
            }

            // Remove all existing labels
            var labelsRemoved = await _appDbContext.ConversationHashtags
                .Where(x => x.CompanyId == companyId && x.ConversationId == conversationId)
                .Select(x => new LabelLogData(x.Hashtag.Hashtag))
                .ToListAsync();

            if (labelsRemoved.Count > 0)
            {
                _logger.LogInformation(
                    "[SetConversationLabel] [RemoveAllExistingLabel] {CompanyId} {StaffId} {UserProfileId} {ConversationId} {Labels}",
                    conversation.CompanyId,
                    staffId,
                    conversation.UserProfileId,
                    conversation.Id,
                    string.Join(", ", labelsRemoved.Select(x => x.Label)));

                await _appDbContext.ConversationHashtags
                    .Where(x => x.CompanyId == companyId && x.ConversationId == conversationId)
                    .ExecuteDeleteAsync();

                conversation.conversationHashtags = await _appDbContext.ConversationHashtags
                    .Include(conversationHashtag => conversationHashtag.Hashtag)
                    .Where(x => x.CompanyId == companyId && x.ConversationId == conversationId)
                    .OrderByDescending(x => x.Id)
                    .ToListAsync();
            }

            // Add new labels
            foreach (var userProfileCustomField in conversationTagViewModels)
            {
                if (string.IsNullOrEmpty(userProfileCustomField.Hashtag))
                {
                    continue;
                }

                _logger.LogInformation(
                    "[SetConversationLabel] [AddNewLabel] {CompanyId} {StaffId} {UserProfileId} {ConversationId} {Labels}",
                    conversation.CompanyId,
                    staffId,
                    conversation.UserProfileId,
                    conversation.Id,
                    userProfileCustomField.Hashtag);

                var companyHashtag = await _appDbContext.CompanyDefinedHashtags
                    .FirstOrDefaultAsync(
                        x =>
                            x.CompanyId == companyId
                            && x.Hashtag == userProfileCustomField.HashtagNormalized);

                if (companyHashtag == null)
                {
                    companyHashtag = new CompanyHashtag
                    {
                        CompanyId = companyId,
                        Hashtag = userProfileCustomField.Hashtag,
                        HashTagType = userProfileCustomField.HashTagType
                    };

                    await _appDbContext.CompanyDefinedHashtags.AddAsync(companyHashtag);
                    await _appDbContext.SaveChangesAsync();

                    await _companyInfoCacheService.RemoveCompanyInfoCache(companyId);
                }
                else if (companyHashtag.HashTagType == HashTagType.Normal
                         && userProfileCustomField.HashTagType == HashTagType.Shopify)
                {
                    companyHashtag.HashTagType = HashTagType.Shopify;
                }

                if (userProfileCustomField.HashTagColor.HasValue)
                {
                    companyHashtag.HashTagColor = userProfileCustomField.HashTagColor.Value;
                }

                //Check if the conversation already has the hashtag
                var conversationHashtag = await _appDbContext.ConversationHashtags
                    .AnyAsync(
                        x => x.CompanyId == companyId && x.ConversationId == conversationId &&
                             x.HashtagId == companyHashtag.Id);

                if (conversationHashtag)
                {
                    continue;
                }

                await _appDbContext.ConversationHashtags.AddAsync(
                    new ConversationHashtag()
                    {
                        ConversationId = conversationId, CompanyId = companyId, HashtagId = companyHashtag.Id
                    });

                // Save the changes
                await _appDbContext.SaveChangesAsync();

                labelsAdded.Add(new LabelLogData(companyHashtag.Hashtag));
            }

            conversation.conversationHashtags = await _appDbContext.ConversationHashtags
                .Include(conversationHashtag => conversationHashtag.Hashtag)
                .Where(x => x.CompanyId == companyId && x.ConversationId == conversationId)
                .OrderByDescending(x => x.Id)
                .ToListAsync();

            await _signalRService.SignalROnConversationStatusChanged(conversation);

            await _userProfileHooks.OnConversationLabelsSetAsync(
                conversation.CompanyId,
                conversation.UserProfileId,
                staffId.ToString(),
                () =>
                {
                    var allHashtags = conversation.conversationHashtags
                        .Select(x => x.Hashtag)
                        .ToList();
                    var labelToHashtagDict = allHashtags
                        .GroupBy(ht => ht.Hashtag)
                        .ToDictionary(g => g.Key, g => g.First());

                    return Task.FromResult(
                        new OnConversationLabelsSetData(
                            labelsRemoved
                                .Select(
                                    l => new LabelData(
                                        l.Label,
                                        labelToHashtagDict.GetValueOrDefault(l.Label)?
                                            .HashTagColor
                                            .GetDisplayName(),
                                        labelToHashtagDict.GetValueOrDefault(l.Label)?
                                            .HashTagType
                                            .GetDisplayName()))
                                .ToList(),
                            labelsAdded
                                .Select(
                                    l => new LabelData(
                                        l.Label,
                                        labelToHashtagDict.GetValueOrDefault(l.Label)?
                                            .HashTagColor
                                            .GetDisplayName(),
                                        labelToHashtagDict.GetValueOrDefault(l.Label)?
                                            .HashTagType
                                            .GetDisplayName()))
                                .ToList(),
                            allHashtags
                                .Select(
                                    l => new LabelData(
                                        l.Hashtag,
                                        l.HashTagColor.GetDisplayName(),
                                        l.HashTagType.GetDisplayName()))
                                .ToList()));
                });

            if (isTriggerAutomation)
            {
                BackgroundJob.Enqueue<IAutomationService>(
                    x =>
                        x.HashtagChangedTrigger(
                            conversation.UserProfileId,
                            conversationTagViewModels));
            }

            await _cacheManagerService.DeleteCacheWithConstantKeyAsync($":1:conversation:{conversationId}");

            return conversation;
        }

        private static string GenerateChecksumForConversationTagViewModels(
            List<ConversationHashtagViewModel> conversationTagViewModels)
        {
            var serializedData = JsonConvert.SerializeObject(conversationTagViewModels);
            using var sha256Hash = SHA256.Create();

            var bytes = sha256Hash.ComputeHash(Encoding.UTF8.GetBytes(serializedData));
            var builder = new StringBuilder();
            foreach (var t in bytes)
            {
                builder.Append(t.ToString("x2"));
            }

            return builder.ToString();
        }
    }
}