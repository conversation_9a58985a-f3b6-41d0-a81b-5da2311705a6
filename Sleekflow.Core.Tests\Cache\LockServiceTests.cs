﻿using System.Collections.Concurrent;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Logging.Abstractions;
using Moq;
using StackExchange.Redis;
using Travis_backend.Cache;

namespace Sleekflow.Core.Tests.Cache;

public class LockServiceTests
{
    // UAT
    private const string RedisConnectionString =
        "sleekflow-sp-uat.redis.cache.windows.net:6380,password=Ld6bAceqgYpA64Qu5B7QzFqx3GhRZolAKAzCaB5f6Fw=,ssl=True,abortConnect=False";

    private ILockService _lockService;
    private const string RedisConnectionStringLocal = "127.0.0.1:6379";

    public LockServiceTests()
    {
    }

    [SetUp]
    public void Setup()
    {

        _lockService = new LockService(NullLogger<LockService>.Instance,
            new Mock<IDistributedCache>().Object,
            ConnectionMultiplexer.Connect(
                RedisConnectionStringLocal));
    }

    [TearDown]
    public void TearDown()
    {
    }

    [Test]
    public async Task LockTest()
    {
        var lock1 = await _lockService.AcquireLockAsync(
            nameof(LockTest),
            TimeSpan.FromSeconds(1));

        var lock2 = await _lockService.AcquireLockAsync(
            nameof(LockTest),
            TimeSpan.FromSeconds(1));

        Thread.Sleep(2000);

        var lock3 = await _lockService.AcquireLockAsync(
            nameof(LockTest),
            TimeSpan.FromSeconds(1));

        Assert.That(lock1, Is.Not.Null);
        Assert.That(lock2, Is.Null);
        Assert.That(lock3, Is.Not.Null);
    }

    [Test]
    public async Task LockAndReleaseTest()
    {
        var lock1 = await _lockService.AcquireLockAsync(
            nameof(LockAndReleaseTest),
            TimeSpan.FromSeconds(1));

        await _lockService.ReleaseLockAsync(
            lock1!);

        var lock2 = await _lockService.AcquireLockAsync(
            nameof(LockAndReleaseTest),
            TimeSpan.FromSeconds(1));

        Assert.That(lock1, Is.Not.Null);
        Assert.That(lock2, Is.Not.Null);
    }

    [Test]
    public async Task LockAndReleaseConcurrentlyTest()
    {
        var lock1 = await _lockService.AcquireLockAsync(
            nameof(LockAndReleaseConcurrentlyTest),
            TimeSpan.FromSeconds(1));

        Thread.Sleep(2000);

        var lock2 = await _lockService.AcquireLockAsync(
            nameof(LockAndReleaseConcurrentlyTest),
            TimeSpan.FromSeconds(1));

        Assert.That(lock1, Is.Not.Null);
        Assert.That(lock1!.ETag, Is.Not.Null);
        Assert.That(lock2, Is.Not.Null);
        Assert.That(lock2!.ETag, Is.Not.Null);

        Assert.DoesNotThrowAsync(
            async () =>
            {
                Assert.That(
                    await _lockService.ReleaseLockAsync(lock1!),
                    Is.EqualTo(0));

                Assert.That(
                    await _lockService.ReleaseLockAsync(lock2!),
                    Is.EqualTo(1));
            });
    }

    [Test]
    public async Task LockConcurrentlyTest()
    {
        var sessionId = Guid.NewGuid();

        var tasks = new List<Task<ILockService.Lock?>>();

        for (int i = 0; i < 1000; i++)
        {
            tasks.Add(
                _lockService.AcquireLockAsync(
                    nameof(LockConcurrentlyTest)+sessionId,
                    TimeSpan.FromSeconds(1))
            );
        }

        var locks = await Task.WhenAll(tasks);

        Assert.That(locks.Count(l => l != null), Is.EqualTo(1));
        Assert.That(locks.Count(l => l == null), Is.EqualTo(999));
    }

    [Test]
    public async Task LockConcurrentlyWithParallelForTest()
    {
        var sessionId = Guid.NewGuid();

        var tasks = new ConcurrentBag<Task<ILockService.Lock?>>();

        Parallel.For(
            0,
            1000,
            new ParallelOptions()
            {
                MaxDegreeOfParallelism = 10
            },
            ((i, state) =>
            {
                tasks.Add(
                    _lockService.AcquireLockAsync(
                        nameof(LockConcurrentlyWithParallelForTest) + sessionId,
                        TimeSpan.FromSeconds(1000))
                );
            }));

        var locks = await Task.WhenAll(tasks);

        Assert.That(locks.Count(l => l != null), Is.EqualTo(1));
        Assert.That(locks.Count(l => l == null), Is.EqualTo(999));
    }

    [Test]
    public async Task LockConcurrentlyTestWithParallelForAndTwoConnectionTest()
    {
        var sessionId = Guid.NewGuid();

        var lockService2 = new LockService(NullLogger<LockService>.Instance,
            new Mock<IDistributedCache>().Object,
            ConnectionMultiplexer.Connect(
                RedisConnectionStringLocal));

        var tasks = new ConcurrentBag<Task<ILockService.Lock?>>();

        Parallel.For(
            0,
            10000,
            new ParallelOptions()
            {
                MaxDegreeOfParallelism = 100
            },
            ((i, state) =>
            {
                if (i % 2 == 0)
                {
                    tasks.Add(
                        _lockService.AcquireLockAsync(
                            nameof(LockConcurrentlyTestWithParallelForAndTwoConnectionTest) + sessionId,
                            TimeSpan.FromSeconds(1000))
                    );
                }
                else
                {
                    tasks.Add(
                        lockService2.AcquireLockAsync(
                            nameof(LockConcurrentlyTestWithParallelForAndTwoConnectionTest) + sessionId,
                            TimeSpan.FromSeconds(1000))
                    );
                }
            }));

        var locks = await Task.WhenAll(tasks);

        Assert.That(locks.Count(l => l != null), Is.EqualTo(1));
        Assert.That(locks.Count(l => l == null), Is.EqualTo(9999));
    }

    [Test]
    [TestCase(10000, 20)]
    [TestCase(10000, 50)]
    [TestCase(10000, 100)]
    [TestCase(10000, 500)]
    [TestCase(25000, 20)]
    [TestCase(25000, 50)]
    [TestCase(25000, 100)]
    [TestCase(25000, 500)]
    public async Task LockConcurrentlyTestWithParallelForAndTwoConnectionTest_Parallelism2(
        int lockCount,
        int cacheConnectionCount)
    {
        var tasks = new ConcurrentBag<Task<ILockService.Lock?>>();

        var lockCountPerConnection = lockCount / cacheConnectionCount;

        var sessionId = Guid.NewGuid();

        Parallel.For(
            0,
            cacheConnectionCount,
            new ParallelOptions()
            {
                MaxDegreeOfParallelism = cacheConnectionCount
            },
            ((i, state) =>
            {
                Parallel.For(
                    0,
                    lockCountPerConnection,
                    new ParallelOptions()
                    {
                        MaxDegreeOfParallelism = cacheConnectionCount
                    },
                    ((i, state) =>
                    {
                        tasks.Add(
                            _lockService.AcquireLockAsync(
                                nameof(LockConcurrentlyTestWithParallelForAndTwoConnectionTest_Parallelism2) +
                                $"{sessionId},lockCount:{lockCount},cacheConnectionCount:{cacheConnectionCount}",
                                TimeSpan.FromSeconds(1000))
                        );
                    }));
            }));

        var locks = await Task.WhenAll(tasks);

        Assert.That(locks.Count(l => l != null), Is.EqualTo(1));
        Assert.That(locks.Count(l => l == null), Is.EqualTo(lockCount - 1));
    }
}