<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Sleekflow.Apis.InternalIntegrationHub</name>
    </assembly>
    <members>
        <member name="T:Sleekflow.Apis.InternalIntegrationHub.Api.INetSuiteExternalApiSync">
            <summary>
            Represents a collection of functions to interact with the API endpoints
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Api.INetSuiteExternalApiSync.NetSuiteExternalCreatePaymentPost(System.String,System.String,Sleekflow.Apis.InternalIntegrationHub.Model.CreatePaymentInput)">
            <summary>
            
            </summary>
            <exception cref="T:Sleekflow.Apis.InternalIntegrationHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="xSleekflowDistributedInvocationContext"> (optional)</param>
            <param name="createPaymentInput"> (optional)</param>
            <returns>CreatePaymentOutputOutput</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Api.INetSuiteExternalApiSync.NetSuiteExternalCreatePaymentPostWithHttpInfo(System.String,System.String,Sleekflow.Apis.InternalIntegrationHub.Model.CreatePaymentInput)">
            <summary>
            
            </summary>
            <remarks>
            
            </remarks>
            <exception cref="T:Sleekflow.Apis.InternalIntegrationHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="xSleekflowDistributedInvocationContext"> (optional)</param>
            <param name="createPaymentInput"> (optional)</param>
            <returns>ApiResponse of CreatePaymentOutputOutput</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Api.INetSuiteExternalApiSync.NetSuiteExternalGetPaymentPost(System.String,System.String,Sleekflow.Apis.InternalIntegrationHub.Model.GetPaymentInput)">
            <summary>
            
            </summary>
            <exception cref="T:Sleekflow.Apis.InternalIntegrationHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="xSleekflowDistributedInvocationContext"> (optional)</param>
            <param name="getPaymentInput"> (optional)</param>
            <returns>GetPaymentOutputOutput</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Api.INetSuiteExternalApiSync.NetSuiteExternalGetPaymentPostWithHttpInfo(System.String,System.String,Sleekflow.Apis.InternalIntegrationHub.Model.GetPaymentInput)">
            <summary>
            
            </summary>
            <remarks>
            
            </remarks>
            <exception cref="T:Sleekflow.Apis.InternalIntegrationHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="xSleekflowDistributedInvocationContext"> (optional)</param>
            <param name="getPaymentInput"> (optional)</param>
            <returns>ApiResponse of GetPaymentOutputOutput</returns>
        </member>
        <member name="T:Sleekflow.Apis.InternalIntegrationHub.Api.INetSuiteExternalApiAsync">
            <summary>
            Represents a collection of functions to interact with the API endpoints
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Api.INetSuiteExternalApiAsync.NetSuiteExternalCreatePaymentPostAsync(System.String,System.String,Sleekflow.Apis.InternalIntegrationHub.Model.CreatePaymentInput,System.Threading.CancellationToken)">
            <summary>
            
            </summary>
            <remarks>
            
            </remarks>
            <exception cref="T:Sleekflow.Apis.InternalIntegrationHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="xSleekflowDistributedInvocationContext"> (optional)</param>
            <param name="createPaymentInput"> (optional)</param>
            <param name="cancellationToken">Cancellation Token to cancel the request.</param>
            <returns>Task of CreatePaymentOutputOutput</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Api.INetSuiteExternalApiAsync.NetSuiteExternalCreatePaymentPostWithHttpInfoAsync(System.String,System.String,Sleekflow.Apis.InternalIntegrationHub.Model.CreatePaymentInput,System.Threading.CancellationToken)">
            <summary>
            
            </summary>
            <remarks>
            
            </remarks>
            <exception cref="T:Sleekflow.Apis.InternalIntegrationHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="xSleekflowDistributedInvocationContext"> (optional)</param>
            <param name="createPaymentInput"> (optional)</param>
            <param name="cancellationToken">Cancellation Token to cancel the request.</param>
            <returns>Task of ApiResponse (CreatePaymentOutputOutput)</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Api.INetSuiteExternalApiAsync.NetSuiteExternalGetPaymentPostAsync(System.String,System.String,Sleekflow.Apis.InternalIntegrationHub.Model.GetPaymentInput,System.Threading.CancellationToken)">
            <summary>
            
            </summary>
            <remarks>
            
            </remarks>
            <exception cref="T:Sleekflow.Apis.InternalIntegrationHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="xSleekflowDistributedInvocationContext"> (optional)</param>
            <param name="getPaymentInput"> (optional)</param>
            <param name="cancellationToken">Cancellation Token to cancel the request.</param>
            <returns>Task of GetPaymentOutputOutput</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Api.INetSuiteExternalApiAsync.NetSuiteExternalGetPaymentPostWithHttpInfoAsync(System.String,System.String,Sleekflow.Apis.InternalIntegrationHub.Model.GetPaymentInput,System.Threading.CancellationToken)">
            <summary>
            
            </summary>
            <remarks>
            
            </remarks>
            <exception cref="T:Sleekflow.Apis.InternalIntegrationHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="xSleekflowDistributedInvocationContext"> (optional)</param>
            <param name="getPaymentInput"> (optional)</param>
            <param name="cancellationToken">Cancellation Token to cancel the request.</param>
            <returns>Task of ApiResponse (GetPaymentOutputOutput)</returns>
        </member>
        <member name="T:Sleekflow.Apis.InternalIntegrationHub.Api.INetSuiteExternalApi">
            <summary>
            Represents a collection of functions to interact with the API endpoints
            </summary>
        </member>
        <member name="T:Sleekflow.Apis.InternalIntegrationHub.Api.NetSuiteExternalApi">
            <summary>
            Represents a collection of functions to interact with the API endpoints
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Api.NetSuiteExternalApi.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.InternalIntegrationHub.Api.NetSuiteExternalApi"/> class.
            **IMPORTANT** This will also create an instance of HttpClient, which is less than ideal.
            It's better to reuse the <see href="https://docs.microsoft.com/en-us/dotnet/architecture/microservices/implement-resilient-applications/use-httpclientfactory-to-implement-resilient-http-requests#issues-with-the-original-httpclient-class-available-in-net">HttpClient and HttpClientHandler</see>.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Api.NetSuiteExternalApi.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.InternalIntegrationHub.Api.NetSuiteExternalApi"/> class.
            **IMPORTANT** This will also create an instance of HttpClient, which is less than ideal.
            It's better to reuse the <see href="https://docs.microsoft.com/en-us/dotnet/architecture/microservices/implement-resilient-applications/use-httpclientfactory-to-implement-resilient-http-requests#issues-with-the-original-httpclient-class-available-in-net">HttpClient and HttpClientHandler</see>.
            </summary>
            <param name="basePath">The target service's base path in URL format.</param>
            <exception cref="T:System.ArgumentException"></exception>
            <returns></returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Api.NetSuiteExternalApi.#ctor(Sleekflow.Apis.InternalIntegrationHub.Client.Configuration)">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.InternalIntegrationHub.Api.NetSuiteExternalApi"/> class using Configuration object.
            **IMPORTANT** This will also create an instance of HttpClient, which is less than ideal.
            It's better to reuse the <see href="https://docs.microsoft.com/en-us/dotnet/architecture/microservices/implement-resilient-applications/use-httpclientfactory-to-implement-resilient-http-requests#issues-with-the-original-httpclient-class-available-in-net">HttpClient and HttpClientHandler</see>.
            </summary>
            <param name="configuration">An instance of Configuration.</param>
            <exception cref="T:System.ArgumentNullException"></exception>
            <returns></returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Api.NetSuiteExternalApi.#ctor(System.Net.Http.HttpClient,System.Net.Http.HttpClientHandler)">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.InternalIntegrationHub.Api.NetSuiteExternalApi"/> class.
            </summary>
            <param name="client">An instance of HttpClient.</param>
            <param name="handler">An optional instance of HttpClientHandler that is used by HttpClient.</param>
            <exception cref="T:System.ArgumentNullException"></exception>
            <returns></returns>
            <remarks>
            Some configuration settings will not be applied without passing an HttpClientHandler.
            The features affected are: Setting and Retrieving Cookies, Client Certificates, Proxy settings.
            </remarks>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Api.NetSuiteExternalApi.#ctor(System.Net.Http.HttpClient,System.String,System.Net.Http.HttpClientHandler)">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.InternalIntegrationHub.Api.NetSuiteExternalApi"/> class.
            </summary>
            <param name="client">An instance of HttpClient.</param>
            <param name="basePath">The target service's base path in URL format.</param>
            <param name="handler">An optional instance of HttpClientHandler that is used by HttpClient.</param>
            <exception cref="T:System.ArgumentNullException"></exception>
            <exception cref="T:System.ArgumentException"></exception>
            <returns></returns>
            <remarks>
            Some configuration settings will not be applied without passing an HttpClientHandler.
            The features affected are: Setting and Retrieving Cookies, Client Certificates, Proxy settings.
            </remarks>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Api.NetSuiteExternalApi.#ctor(System.Net.Http.HttpClient,Sleekflow.Apis.InternalIntegrationHub.Client.Configuration,System.Net.Http.HttpClientHandler)">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.InternalIntegrationHub.Api.NetSuiteExternalApi"/> class using Configuration object.
            </summary>
            <param name="client">An instance of HttpClient.</param>
            <param name="configuration">An instance of Configuration.</param>
            <param name="handler">An optional instance of HttpClientHandler that is used by HttpClient.</param>
            <exception cref="T:System.ArgumentNullException"></exception>
            <returns></returns>
            <remarks>
            Some configuration settings will not be applied without passing an HttpClientHandler.
            The features affected are: Setting and Retrieving Cookies, Client Certificates, Proxy settings.
            </remarks>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Api.NetSuiteExternalApi.#ctor(Sleekflow.Apis.InternalIntegrationHub.Client.ISynchronousClient,Sleekflow.Apis.InternalIntegrationHub.Client.IAsynchronousClient,Sleekflow.Apis.InternalIntegrationHub.Client.IReadableConfiguration)">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.InternalIntegrationHub.Api.NetSuiteExternalApi"/> class
            using a Configuration object and client instance.
            </summary>
            <param name="client">The client interface for synchronous API access.</param>
            <param name="asyncClient">The client interface for asynchronous API access.</param>
            <param name="configuration">The configuration object.</param>
            <exception cref="T:System.ArgumentNullException"></exception>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Api.NetSuiteExternalApi.Dispose">
            <summary>
            Disposes resources if they were created by us
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Api.NetSuiteExternalApi.ApiClient">
            <summary>
            Holds the ApiClient if created
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Api.NetSuiteExternalApi.AsynchronousClient">
            <summary>
            The client for accessing this underlying API asynchronously.
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Api.NetSuiteExternalApi.Client">
            <summary>
            The client for accessing this underlying API synchronously.
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Api.NetSuiteExternalApi.GetBasePath">
            <summary>
            Gets the base path of the API client.
            </summary>
            <value>The base path</value>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Api.NetSuiteExternalApi.Configuration">
            <summary>
            Gets or sets the configuration object
            </summary>
            <value>An instance of the Configuration</value>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Api.NetSuiteExternalApi.ExceptionFactory">
            <summary>
            Provides a factory method hook for the creation of exceptions.
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Api.NetSuiteExternalApi.NetSuiteExternalCreatePaymentPost(System.String,System.String,Sleekflow.Apis.InternalIntegrationHub.Model.CreatePaymentInput)">
            <summary>
             
            </summary>
            <exception cref="T:Sleekflow.Apis.InternalIntegrationHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="xSleekflowDistributedInvocationContext"> (optional)</param>
            <param name="createPaymentInput"> (optional)</param>
            <returns>CreatePaymentOutputOutput</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Api.NetSuiteExternalApi.NetSuiteExternalCreatePaymentPostWithHttpInfo(System.String,System.String,Sleekflow.Apis.InternalIntegrationHub.Model.CreatePaymentInput)">
            <summary>
             
            </summary>
            <exception cref="T:Sleekflow.Apis.InternalIntegrationHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="xSleekflowDistributedInvocationContext"> (optional)</param>
            <param name="createPaymentInput"> (optional)</param>
            <returns>ApiResponse of CreatePaymentOutputOutput</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Api.NetSuiteExternalApi.NetSuiteExternalCreatePaymentPostAsync(System.String,System.String,Sleekflow.Apis.InternalIntegrationHub.Model.CreatePaymentInput,System.Threading.CancellationToken)">
            <summary>
             
            </summary>
            <exception cref="T:Sleekflow.Apis.InternalIntegrationHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="xSleekflowDistributedInvocationContext"> (optional)</param>
            <param name="createPaymentInput"> (optional)</param>
            <param name="cancellationToken">Cancellation Token to cancel the request.</param>
            <returns>Task of CreatePaymentOutputOutput</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Api.NetSuiteExternalApi.NetSuiteExternalCreatePaymentPostWithHttpInfoAsync(System.String,System.String,Sleekflow.Apis.InternalIntegrationHub.Model.CreatePaymentInput,System.Threading.CancellationToken)">
            <summary>
             
            </summary>
            <exception cref="T:Sleekflow.Apis.InternalIntegrationHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="xSleekflowDistributedInvocationContext"> (optional)</param>
            <param name="createPaymentInput"> (optional)</param>
            <param name="cancellationToken">Cancellation Token to cancel the request.</param>
            <returns>Task of ApiResponse (CreatePaymentOutputOutput)</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Api.NetSuiteExternalApi.NetSuiteExternalGetPaymentPost(System.String,System.String,Sleekflow.Apis.InternalIntegrationHub.Model.GetPaymentInput)">
            <summary>
             
            </summary>
            <exception cref="T:Sleekflow.Apis.InternalIntegrationHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="xSleekflowDistributedInvocationContext"> (optional)</param>
            <param name="getPaymentInput"> (optional)</param>
            <returns>GetPaymentOutputOutput</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Api.NetSuiteExternalApi.NetSuiteExternalGetPaymentPostWithHttpInfo(System.String,System.String,Sleekflow.Apis.InternalIntegrationHub.Model.GetPaymentInput)">
            <summary>
             
            </summary>
            <exception cref="T:Sleekflow.Apis.InternalIntegrationHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="xSleekflowDistributedInvocationContext"> (optional)</param>
            <param name="getPaymentInput"> (optional)</param>
            <returns>ApiResponse of GetPaymentOutputOutput</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Api.NetSuiteExternalApi.NetSuiteExternalGetPaymentPostAsync(System.String,System.String,Sleekflow.Apis.InternalIntegrationHub.Model.GetPaymentInput,System.Threading.CancellationToken)">
            <summary>
             
            </summary>
            <exception cref="T:Sleekflow.Apis.InternalIntegrationHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="xSleekflowDistributedInvocationContext"> (optional)</param>
            <param name="getPaymentInput"> (optional)</param>
            <param name="cancellationToken">Cancellation Token to cancel the request.</param>
            <returns>Task of GetPaymentOutputOutput</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Api.NetSuiteExternalApi.NetSuiteExternalGetPaymentPostWithHttpInfoAsync(System.String,System.String,Sleekflow.Apis.InternalIntegrationHub.Model.GetPaymentInput,System.Threading.CancellationToken)">
            <summary>
             
            </summary>
            <exception cref="T:Sleekflow.Apis.InternalIntegrationHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="xSleekflowDistributedInvocationContext"> (optional)</param>
            <param name="getPaymentInput"> (optional)</param>
            <param name="cancellationToken">Cancellation Token to cancel the request.</param>
            <returns>Task of ApiResponse (GetPaymentOutputOutput)</returns>
        </member>
        <member name="T:Sleekflow.Apis.InternalIntegrationHub.Api.INetSuiteInternalApiSync">
            <summary>
            Represents a collection of functions to interact with the API endpoints
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Api.INetSuiteInternalApiSync.NetSuiteInternalCreateCustomerPost(System.String,System.String,Sleekflow.Apis.InternalIntegrationHub.Model.CreateCustomerInput)">
            <summary>
            
            </summary>
            <exception cref="T:Sleekflow.Apis.InternalIntegrationHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="xSleekflowDistributedInvocationContext"> (optional)</param>
            <param name="createCustomerInput"> (optional)</param>
            <returns>CreateCustomerOutputOutput</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Api.INetSuiteInternalApiSync.NetSuiteInternalCreateCustomerPostWithHttpInfo(System.String,System.String,Sleekflow.Apis.InternalIntegrationHub.Model.CreateCustomerInput)">
            <summary>
            
            </summary>
            <remarks>
            
            </remarks>
            <exception cref="T:Sleekflow.Apis.InternalIntegrationHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="xSleekflowDistributedInvocationContext"> (optional)</param>
            <param name="createCustomerInput"> (optional)</param>
            <returns>ApiResponse of CreateCustomerOutputOutput</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Api.INetSuiteInternalApiSync.NetSuiteInternalCreateEmployeeManuallyPost(System.String,System.String,Sleekflow.Apis.InternalIntegrationHub.Model.CreateEmployeeManuallyInput)">
            <summary>
            
            </summary>
            <exception cref="T:Sleekflow.Apis.InternalIntegrationHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="xSleekflowDistributedInvocationContext"> (optional)</param>
            <param name="createEmployeeManuallyInput"> (optional)</param>
            <returns>CreateEmployeeManuallyOutputOutput</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Api.INetSuiteInternalApiSync.NetSuiteInternalCreateEmployeeManuallyPostWithHttpInfo(System.String,System.String,Sleekflow.Apis.InternalIntegrationHub.Model.CreateEmployeeManuallyInput)">
            <summary>
            
            </summary>
            <remarks>
            
            </remarks>
            <exception cref="T:Sleekflow.Apis.InternalIntegrationHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="xSleekflowDistributedInvocationContext"> (optional)</param>
            <param name="createEmployeeManuallyInput"> (optional)</param>
            <returns>ApiResponse of CreateEmployeeManuallyOutputOutput</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Api.INetSuiteInternalApiSync.NetSuiteInternalCreateInvoicePost(System.String,System.String,Sleekflow.Apis.InternalIntegrationHub.Model.CreateInvoiceInput)">
            <summary>
            
            </summary>
            <exception cref="T:Sleekflow.Apis.InternalIntegrationHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="xSleekflowDistributedInvocationContext"> (optional)</param>
            <param name="createInvoiceInput"> (optional)</param>
            <returns>CreateInvoiceOutputOutput</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Api.INetSuiteInternalApiSync.NetSuiteInternalCreateInvoicePostWithHttpInfo(System.String,System.String,Sleekflow.Apis.InternalIntegrationHub.Model.CreateInvoiceInput)">
            <summary>
            
            </summary>
            <remarks>
            
            </remarks>
            <exception cref="T:Sleekflow.Apis.InternalIntegrationHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="xSleekflowDistributedInvocationContext"> (optional)</param>
            <param name="createInvoiceInput"> (optional)</param>
            <returns>ApiResponse of CreateInvoiceOutputOutput</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Api.INetSuiteInternalApiSync.NetSuiteInternalCreatePaymentFromStripePost(System.String,System.String,Sleekflow.Apis.InternalIntegrationHub.Model.CreatePaymentFromStripeInput)">
            <summary>
            
            </summary>
            <exception cref="T:Sleekflow.Apis.InternalIntegrationHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="xSleekflowDistributedInvocationContext"> (optional)</param>
            <param name="createPaymentFromStripeInput"> (optional)</param>
            <returns>CreatePaymentFromStripeOutputOutput</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Api.INetSuiteInternalApiSync.NetSuiteInternalCreatePaymentFromStripePostWithHttpInfo(System.String,System.String,Sleekflow.Apis.InternalIntegrationHub.Model.CreatePaymentFromStripeInput)">
            <summary>
            
            </summary>
            <remarks>
            
            </remarks>
            <exception cref="T:Sleekflow.Apis.InternalIntegrationHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="xSleekflowDistributedInvocationContext"> (optional)</param>
            <param name="createPaymentFromStripeInput"> (optional)</param>
            <returns>ApiResponse of CreatePaymentFromStripeOutputOutput</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Api.INetSuiteInternalApiSync.NetSuiteInternalUpdateCustomerPost(System.String,System.String,Sleekflow.Apis.InternalIntegrationHub.Model.UpdateCustomerInput)">
            <summary>
            
            </summary>
            <exception cref="T:Sleekflow.Apis.InternalIntegrationHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="xSleekflowDistributedInvocationContext"> (optional)</param>
            <param name="updateCustomerInput"> (optional)</param>
            <returns>UpdateCustomerOutputOutput</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Api.INetSuiteInternalApiSync.NetSuiteInternalUpdateCustomerPostWithHttpInfo(System.String,System.String,Sleekflow.Apis.InternalIntegrationHub.Model.UpdateCustomerInput)">
            <summary>
            
            </summary>
            <remarks>
            
            </remarks>
            <exception cref="T:Sleekflow.Apis.InternalIntegrationHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="xSleekflowDistributedInvocationContext"> (optional)</param>
            <param name="updateCustomerInput"> (optional)</param>
            <returns>ApiResponse of UpdateCustomerOutputOutput</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Api.INetSuiteInternalApiSync.NetSuiteInternalUpdateEmployeeManuallyPost(System.String,System.String,Sleekflow.Apis.InternalIntegrationHub.Model.UpdateEmployeeManuallyInput)">
            <summary>
            
            </summary>
            <exception cref="T:Sleekflow.Apis.InternalIntegrationHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="xSleekflowDistributedInvocationContext"> (optional)</param>
            <param name="updateEmployeeManuallyInput"> (optional)</param>
            <returns>UpdateEmployeeManuallyOutputOutput</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Api.INetSuiteInternalApiSync.NetSuiteInternalUpdateEmployeeManuallyPostWithHttpInfo(System.String,System.String,Sleekflow.Apis.InternalIntegrationHub.Model.UpdateEmployeeManuallyInput)">
            <summary>
            
            </summary>
            <remarks>
            
            </remarks>
            <exception cref="T:Sleekflow.Apis.InternalIntegrationHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="xSleekflowDistributedInvocationContext"> (optional)</param>
            <param name="updateEmployeeManuallyInput"> (optional)</param>
            <returns>ApiResponse of UpdateEmployeeManuallyOutputOutput</returns>
        </member>
        <member name="T:Sleekflow.Apis.InternalIntegrationHub.Api.INetSuiteInternalApiAsync">
            <summary>
            Represents a collection of functions to interact with the API endpoints
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Api.INetSuiteInternalApiAsync.NetSuiteInternalCreateCustomerPostAsync(System.String,System.String,Sleekflow.Apis.InternalIntegrationHub.Model.CreateCustomerInput,System.Threading.CancellationToken)">
            <summary>
            
            </summary>
            <remarks>
            
            </remarks>
            <exception cref="T:Sleekflow.Apis.InternalIntegrationHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="xSleekflowDistributedInvocationContext"> (optional)</param>
            <param name="createCustomerInput"> (optional)</param>
            <param name="cancellationToken">Cancellation Token to cancel the request.</param>
            <returns>Task of CreateCustomerOutputOutput</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Api.INetSuiteInternalApiAsync.NetSuiteInternalCreateCustomerPostWithHttpInfoAsync(System.String,System.String,Sleekflow.Apis.InternalIntegrationHub.Model.CreateCustomerInput,System.Threading.CancellationToken)">
            <summary>
            
            </summary>
            <remarks>
            
            </remarks>
            <exception cref="T:Sleekflow.Apis.InternalIntegrationHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="xSleekflowDistributedInvocationContext"> (optional)</param>
            <param name="createCustomerInput"> (optional)</param>
            <param name="cancellationToken">Cancellation Token to cancel the request.</param>
            <returns>Task of ApiResponse (CreateCustomerOutputOutput)</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Api.INetSuiteInternalApiAsync.NetSuiteInternalCreateEmployeeManuallyPostAsync(System.String,System.String,Sleekflow.Apis.InternalIntegrationHub.Model.CreateEmployeeManuallyInput,System.Threading.CancellationToken)">
            <summary>
            
            </summary>
            <remarks>
            
            </remarks>
            <exception cref="T:Sleekflow.Apis.InternalIntegrationHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="xSleekflowDistributedInvocationContext"> (optional)</param>
            <param name="createEmployeeManuallyInput"> (optional)</param>
            <param name="cancellationToken">Cancellation Token to cancel the request.</param>
            <returns>Task of CreateEmployeeManuallyOutputOutput</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Api.INetSuiteInternalApiAsync.NetSuiteInternalCreateEmployeeManuallyPostWithHttpInfoAsync(System.String,System.String,Sleekflow.Apis.InternalIntegrationHub.Model.CreateEmployeeManuallyInput,System.Threading.CancellationToken)">
            <summary>
            
            </summary>
            <remarks>
            
            </remarks>
            <exception cref="T:Sleekflow.Apis.InternalIntegrationHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="xSleekflowDistributedInvocationContext"> (optional)</param>
            <param name="createEmployeeManuallyInput"> (optional)</param>
            <param name="cancellationToken">Cancellation Token to cancel the request.</param>
            <returns>Task of ApiResponse (CreateEmployeeManuallyOutputOutput)</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Api.INetSuiteInternalApiAsync.NetSuiteInternalCreateInvoicePostAsync(System.String,System.String,Sleekflow.Apis.InternalIntegrationHub.Model.CreateInvoiceInput,System.Threading.CancellationToken)">
            <summary>
            
            </summary>
            <remarks>
            
            </remarks>
            <exception cref="T:Sleekflow.Apis.InternalIntegrationHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="xSleekflowDistributedInvocationContext"> (optional)</param>
            <param name="createInvoiceInput"> (optional)</param>
            <param name="cancellationToken">Cancellation Token to cancel the request.</param>
            <returns>Task of CreateInvoiceOutputOutput</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Api.INetSuiteInternalApiAsync.NetSuiteInternalCreateInvoicePostWithHttpInfoAsync(System.String,System.String,Sleekflow.Apis.InternalIntegrationHub.Model.CreateInvoiceInput,System.Threading.CancellationToken)">
            <summary>
            
            </summary>
            <remarks>
            
            </remarks>
            <exception cref="T:Sleekflow.Apis.InternalIntegrationHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="xSleekflowDistributedInvocationContext"> (optional)</param>
            <param name="createInvoiceInput"> (optional)</param>
            <param name="cancellationToken">Cancellation Token to cancel the request.</param>
            <returns>Task of ApiResponse (CreateInvoiceOutputOutput)</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Api.INetSuiteInternalApiAsync.NetSuiteInternalCreatePaymentFromStripePostAsync(System.String,System.String,Sleekflow.Apis.InternalIntegrationHub.Model.CreatePaymentFromStripeInput,System.Threading.CancellationToken)">
            <summary>
            
            </summary>
            <remarks>
            
            </remarks>
            <exception cref="T:Sleekflow.Apis.InternalIntegrationHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="xSleekflowDistributedInvocationContext"> (optional)</param>
            <param name="createPaymentFromStripeInput"> (optional)</param>
            <param name="cancellationToken">Cancellation Token to cancel the request.</param>
            <returns>Task of CreatePaymentFromStripeOutputOutput</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Api.INetSuiteInternalApiAsync.NetSuiteInternalCreatePaymentFromStripePostWithHttpInfoAsync(System.String,System.String,Sleekflow.Apis.InternalIntegrationHub.Model.CreatePaymentFromStripeInput,System.Threading.CancellationToken)">
            <summary>
            
            </summary>
            <remarks>
            
            </remarks>
            <exception cref="T:Sleekflow.Apis.InternalIntegrationHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="xSleekflowDistributedInvocationContext"> (optional)</param>
            <param name="createPaymentFromStripeInput"> (optional)</param>
            <param name="cancellationToken">Cancellation Token to cancel the request.</param>
            <returns>Task of ApiResponse (CreatePaymentFromStripeOutputOutput)</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Api.INetSuiteInternalApiAsync.NetSuiteInternalUpdateCustomerPostAsync(System.String,System.String,Sleekflow.Apis.InternalIntegrationHub.Model.UpdateCustomerInput,System.Threading.CancellationToken)">
            <summary>
            
            </summary>
            <remarks>
            
            </remarks>
            <exception cref="T:Sleekflow.Apis.InternalIntegrationHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="xSleekflowDistributedInvocationContext"> (optional)</param>
            <param name="updateCustomerInput"> (optional)</param>
            <param name="cancellationToken">Cancellation Token to cancel the request.</param>
            <returns>Task of UpdateCustomerOutputOutput</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Api.INetSuiteInternalApiAsync.NetSuiteInternalUpdateCustomerPostWithHttpInfoAsync(System.String,System.String,Sleekflow.Apis.InternalIntegrationHub.Model.UpdateCustomerInput,System.Threading.CancellationToken)">
            <summary>
            
            </summary>
            <remarks>
            
            </remarks>
            <exception cref="T:Sleekflow.Apis.InternalIntegrationHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="xSleekflowDistributedInvocationContext"> (optional)</param>
            <param name="updateCustomerInput"> (optional)</param>
            <param name="cancellationToken">Cancellation Token to cancel the request.</param>
            <returns>Task of ApiResponse (UpdateCustomerOutputOutput)</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Api.INetSuiteInternalApiAsync.NetSuiteInternalUpdateEmployeeManuallyPostAsync(System.String,System.String,Sleekflow.Apis.InternalIntegrationHub.Model.UpdateEmployeeManuallyInput,System.Threading.CancellationToken)">
            <summary>
            
            </summary>
            <remarks>
            
            </remarks>
            <exception cref="T:Sleekflow.Apis.InternalIntegrationHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="xSleekflowDistributedInvocationContext"> (optional)</param>
            <param name="updateEmployeeManuallyInput"> (optional)</param>
            <param name="cancellationToken">Cancellation Token to cancel the request.</param>
            <returns>Task of UpdateEmployeeManuallyOutputOutput</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Api.INetSuiteInternalApiAsync.NetSuiteInternalUpdateEmployeeManuallyPostWithHttpInfoAsync(System.String,System.String,Sleekflow.Apis.InternalIntegrationHub.Model.UpdateEmployeeManuallyInput,System.Threading.CancellationToken)">
            <summary>
            
            </summary>
            <remarks>
            
            </remarks>
            <exception cref="T:Sleekflow.Apis.InternalIntegrationHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="xSleekflowDistributedInvocationContext"> (optional)</param>
            <param name="updateEmployeeManuallyInput"> (optional)</param>
            <param name="cancellationToken">Cancellation Token to cancel the request.</param>
            <returns>Task of ApiResponse (UpdateEmployeeManuallyOutputOutput)</returns>
        </member>
        <member name="T:Sleekflow.Apis.InternalIntegrationHub.Api.INetSuiteInternalApi">
            <summary>
            Represents a collection of functions to interact with the API endpoints
            </summary>
        </member>
        <member name="T:Sleekflow.Apis.InternalIntegrationHub.Api.NetSuiteInternalApi">
            <summary>
            Represents a collection of functions to interact with the API endpoints
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Api.NetSuiteInternalApi.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.InternalIntegrationHub.Api.NetSuiteInternalApi"/> class.
            **IMPORTANT** This will also create an instance of HttpClient, which is less than ideal.
            It's better to reuse the <see href="https://docs.microsoft.com/en-us/dotnet/architecture/microservices/implement-resilient-applications/use-httpclientfactory-to-implement-resilient-http-requests#issues-with-the-original-httpclient-class-available-in-net">HttpClient and HttpClientHandler</see>.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Api.NetSuiteInternalApi.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.InternalIntegrationHub.Api.NetSuiteInternalApi"/> class.
            **IMPORTANT** This will also create an instance of HttpClient, which is less than ideal.
            It's better to reuse the <see href="https://docs.microsoft.com/en-us/dotnet/architecture/microservices/implement-resilient-applications/use-httpclientfactory-to-implement-resilient-http-requests#issues-with-the-original-httpclient-class-available-in-net">HttpClient and HttpClientHandler</see>.
            </summary>
            <param name="basePath">The target service's base path in URL format.</param>
            <exception cref="T:System.ArgumentException"></exception>
            <returns></returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Api.NetSuiteInternalApi.#ctor(Sleekflow.Apis.InternalIntegrationHub.Client.Configuration)">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.InternalIntegrationHub.Api.NetSuiteInternalApi"/> class using Configuration object.
            **IMPORTANT** This will also create an instance of HttpClient, which is less than ideal.
            It's better to reuse the <see href="https://docs.microsoft.com/en-us/dotnet/architecture/microservices/implement-resilient-applications/use-httpclientfactory-to-implement-resilient-http-requests#issues-with-the-original-httpclient-class-available-in-net">HttpClient and HttpClientHandler</see>.
            </summary>
            <param name="configuration">An instance of Configuration.</param>
            <exception cref="T:System.ArgumentNullException"></exception>
            <returns></returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Api.NetSuiteInternalApi.#ctor(System.Net.Http.HttpClient,System.Net.Http.HttpClientHandler)">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.InternalIntegrationHub.Api.NetSuiteInternalApi"/> class.
            </summary>
            <param name="client">An instance of HttpClient.</param>
            <param name="handler">An optional instance of HttpClientHandler that is used by HttpClient.</param>
            <exception cref="T:System.ArgumentNullException"></exception>
            <returns></returns>
            <remarks>
            Some configuration settings will not be applied without passing an HttpClientHandler.
            The features affected are: Setting and Retrieving Cookies, Client Certificates, Proxy settings.
            </remarks>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Api.NetSuiteInternalApi.#ctor(System.Net.Http.HttpClient,System.String,System.Net.Http.HttpClientHandler)">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.InternalIntegrationHub.Api.NetSuiteInternalApi"/> class.
            </summary>
            <param name="client">An instance of HttpClient.</param>
            <param name="basePath">The target service's base path in URL format.</param>
            <param name="handler">An optional instance of HttpClientHandler that is used by HttpClient.</param>
            <exception cref="T:System.ArgumentNullException"></exception>
            <exception cref="T:System.ArgumentException"></exception>
            <returns></returns>
            <remarks>
            Some configuration settings will not be applied without passing an HttpClientHandler.
            The features affected are: Setting and Retrieving Cookies, Client Certificates, Proxy settings.
            </remarks>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Api.NetSuiteInternalApi.#ctor(System.Net.Http.HttpClient,Sleekflow.Apis.InternalIntegrationHub.Client.Configuration,System.Net.Http.HttpClientHandler)">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.InternalIntegrationHub.Api.NetSuiteInternalApi"/> class using Configuration object.
            </summary>
            <param name="client">An instance of HttpClient.</param>
            <param name="configuration">An instance of Configuration.</param>
            <param name="handler">An optional instance of HttpClientHandler that is used by HttpClient.</param>
            <exception cref="T:System.ArgumentNullException"></exception>
            <returns></returns>
            <remarks>
            Some configuration settings will not be applied without passing an HttpClientHandler.
            The features affected are: Setting and Retrieving Cookies, Client Certificates, Proxy settings.
            </remarks>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Api.NetSuiteInternalApi.#ctor(Sleekflow.Apis.InternalIntegrationHub.Client.ISynchronousClient,Sleekflow.Apis.InternalIntegrationHub.Client.IAsynchronousClient,Sleekflow.Apis.InternalIntegrationHub.Client.IReadableConfiguration)">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.InternalIntegrationHub.Api.NetSuiteInternalApi"/> class
            using a Configuration object and client instance.
            </summary>
            <param name="client">The client interface for synchronous API access.</param>
            <param name="asyncClient">The client interface for asynchronous API access.</param>
            <param name="configuration">The configuration object.</param>
            <exception cref="T:System.ArgumentNullException"></exception>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Api.NetSuiteInternalApi.Dispose">
            <summary>
            Disposes resources if they were created by us
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Api.NetSuiteInternalApi.ApiClient">
            <summary>
            Holds the ApiClient if created
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Api.NetSuiteInternalApi.AsynchronousClient">
            <summary>
            The client for accessing this underlying API asynchronously.
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Api.NetSuiteInternalApi.Client">
            <summary>
            The client for accessing this underlying API synchronously.
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Api.NetSuiteInternalApi.GetBasePath">
            <summary>
            Gets the base path of the API client.
            </summary>
            <value>The base path</value>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Api.NetSuiteInternalApi.Configuration">
            <summary>
            Gets or sets the configuration object
            </summary>
            <value>An instance of the Configuration</value>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Api.NetSuiteInternalApi.ExceptionFactory">
            <summary>
            Provides a factory method hook for the creation of exceptions.
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Api.NetSuiteInternalApi.NetSuiteInternalCreateCustomerPost(System.String,System.String,Sleekflow.Apis.InternalIntegrationHub.Model.CreateCustomerInput)">
            <summary>
             
            </summary>
            <exception cref="T:Sleekflow.Apis.InternalIntegrationHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="xSleekflowDistributedInvocationContext"> (optional)</param>
            <param name="createCustomerInput"> (optional)</param>
            <returns>CreateCustomerOutputOutput</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Api.NetSuiteInternalApi.NetSuiteInternalCreateCustomerPostWithHttpInfo(System.String,System.String,Sleekflow.Apis.InternalIntegrationHub.Model.CreateCustomerInput)">
            <summary>
             
            </summary>
            <exception cref="T:Sleekflow.Apis.InternalIntegrationHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="xSleekflowDistributedInvocationContext"> (optional)</param>
            <param name="createCustomerInput"> (optional)</param>
            <returns>ApiResponse of CreateCustomerOutputOutput</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Api.NetSuiteInternalApi.NetSuiteInternalCreateCustomerPostAsync(System.String,System.String,Sleekflow.Apis.InternalIntegrationHub.Model.CreateCustomerInput,System.Threading.CancellationToken)">
            <summary>
             
            </summary>
            <exception cref="T:Sleekflow.Apis.InternalIntegrationHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="xSleekflowDistributedInvocationContext"> (optional)</param>
            <param name="createCustomerInput"> (optional)</param>
            <param name="cancellationToken">Cancellation Token to cancel the request.</param>
            <returns>Task of CreateCustomerOutputOutput</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Api.NetSuiteInternalApi.NetSuiteInternalCreateCustomerPostWithHttpInfoAsync(System.String,System.String,Sleekflow.Apis.InternalIntegrationHub.Model.CreateCustomerInput,System.Threading.CancellationToken)">
            <summary>
             
            </summary>
            <exception cref="T:Sleekflow.Apis.InternalIntegrationHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="xSleekflowDistributedInvocationContext"> (optional)</param>
            <param name="createCustomerInput"> (optional)</param>
            <param name="cancellationToken">Cancellation Token to cancel the request.</param>
            <returns>Task of ApiResponse (CreateCustomerOutputOutput)</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Api.NetSuiteInternalApi.NetSuiteInternalCreateEmployeeManuallyPost(System.String,System.String,Sleekflow.Apis.InternalIntegrationHub.Model.CreateEmployeeManuallyInput)">
            <summary>
             
            </summary>
            <exception cref="T:Sleekflow.Apis.InternalIntegrationHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="xSleekflowDistributedInvocationContext"> (optional)</param>
            <param name="createEmployeeManuallyInput"> (optional)</param>
            <returns>CreateEmployeeManuallyOutputOutput</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Api.NetSuiteInternalApi.NetSuiteInternalCreateEmployeeManuallyPostWithHttpInfo(System.String,System.String,Sleekflow.Apis.InternalIntegrationHub.Model.CreateEmployeeManuallyInput)">
            <summary>
             
            </summary>
            <exception cref="T:Sleekflow.Apis.InternalIntegrationHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="xSleekflowDistributedInvocationContext"> (optional)</param>
            <param name="createEmployeeManuallyInput"> (optional)</param>
            <returns>ApiResponse of CreateEmployeeManuallyOutputOutput</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Api.NetSuiteInternalApi.NetSuiteInternalCreateEmployeeManuallyPostAsync(System.String,System.String,Sleekflow.Apis.InternalIntegrationHub.Model.CreateEmployeeManuallyInput,System.Threading.CancellationToken)">
            <summary>
             
            </summary>
            <exception cref="T:Sleekflow.Apis.InternalIntegrationHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="xSleekflowDistributedInvocationContext"> (optional)</param>
            <param name="createEmployeeManuallyInput"> (optional)</param>
            <param name="cancellationToken">Cancellation Token to cancel the request.</param>
            <returns>Task of CreateEmployeeManuallyOutputOutput</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Api.NetSuiteInternalApi.NetSuiteInternalCreateEmployeeManuallyPostWithHttpInfoAsync(System.String,System.String,Sleekflow.Apis.InternalIntegrationHub.Model.CreateEmployeeManuallyInput,System.Threading.CancellationToken)">
            <summary>
             
            </summary>
            <exception cref="T:Sleekflow.Apis.InternalIntegrationHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="xSleekflowDistributedInvocationContext"> (optional)</param>
            <param name="createEmployeeManuallyInput"> (optional)</param>
            <param name="cancellationToken">Cancellation Token to cancel the request.</param>
            <returns>Task of ApiResponse (CreateEmployeeManuallyOutputOutput)</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Api.NetSuiteInternalApi.NetSuiteInternalCreateInvoicePost(System.String,System.String,Sleekflow.Apis.InternalIntegrationHub.Model.CreateInvoiceInput)">
            <summary>
             
            </summary>
            <exception cref="T:Sleekflow.Apis.InternalIntegrationHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="xSleekflowDistributedInvocationContext"> (optional)</param>
            <param name="createInvoiceInput"> (optional)</param>
            <returns>CreateInvoiceOutputOutput</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Api.NetSuiteInternalApi.NetSuiteInternalCreateInvoicePostWithHttpInfo(System.String,System.String,Sleekflow.Apis.InternalIntegrationHub.Model.CreateInvoiceInput)">
            <summary>
             
            </summary>
            <exception cref="T:Sleekflow.Apis.InternalIntegrationHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="xSleekflowDistributedInvocationContext"> (optional)</param>
            <param name="createInvoiceInput"> (optional)</param>
            <returns>ApiResponse of CreateInvoiceOutputOutput</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Api.NetSuiteInternalApi.NetSuiteInternalCreateInvoicePostAsync(System.String,System.String,Sleekflow.Apis.InternalIntegrationHub.Model.CreateInvoiceInput,System.Threading.CancellationToken)">
            <summary>
             
            </summary>
            <exception cref="T:Sleekflow.Apis.InternalIntegrationHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="xSleekflowDistributedInvocationContext"> (optional)</param>
            <param name="createInvoiceInput"> (optional)</param>
            <param name="cancellationToken">Cancellation Token to cancel the request.</param>
            <returns>Task of CreateInvoiceOutputOutput</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Api.NetSuiteInternalApi.NetSuiteInternalCreateInvoicePostWithHttpInfoAsync(System.String,System.String,Sleekflow.Apis.InternalIntegrationHub.Model.CreateInvoiceInput,System.Threading.CancellationToken)">
            <summary>
             
            </summary>
            <exception cref="T:Sleekflow.Apis.InternalIntegrationHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="xSleekflowDistributedInvocationContext"> (optional)</param>
            <param name="createInvoiceInput"> (optional)</param>
            <param name="cancellationToken">Cancellation Token to cancel the request.</param>
            <returns>Task of ApiResponse (CreateInvoiceOutputOutput)</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Api.NetSuiteInternalApi.NetSuiteInternalCreatePaymentFromStripePost(System.String,System.String,Sleekflow.Apis.InternalIntegrationHub.Model.CreatePaymentFromStripeInput)">
            <summary>
             
            </summary>
            <exception cref="T:Sleekflow.Apis.InternalIntegrationHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="xSleekflowDistributedInvocationContext"> (optional)</param>
            <param name="createPaymentFromStripeInput"> (optional)</param>
            <returns>CreatePaymentFromStripeOutputOutput</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Api.NetSuiteInternalApi.NetSuiteInternalCreatePaymentFromStripePostWithHttpInfo(System.String,System.String,Sleekflow.Apis.InternalIntegrationHub.Model.CreatePaymentFromStripeInput)">
            <summary>
             
            </summary>
            <exception cref="T:Sleekflow.Apis.InternalIntegrationHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="xSleekflowDistributedInvocationContext"> (optional)</param>
            <param name="createPaymentFromStripeInput"> (optional)</param>
            <returns>ApiResponse of CreatePaymentFromStripeOutputOutput</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Api.NetSuiteInternalApi.NetSuiteInternalCreatePaymentFromStripePostAsync(System.String,System.String,Sleekflow.Apis.InternalIntegrationHub.Model.CreatePaymentFromStripeInput,System.Threading.CancellationToken)">
            <summary>
             
            </summary>
            <exception cref="T:Sleekflow.Apis.InternalIntegrationHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="xSleekflowDistributedInvocationContext"> (optional)</param>
            <param name="createPaymentFromStripeInput"> (optional)</param>
            <param name="cancellationToken">Cancellation Token to cancel the request.</param>
            <returns>Task of CreatePaymentFromStripeOutputOutput</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Api.NetSuiteInternalApi.NetSuiteInternalCreatePaymentFromStripePostWithHttpInfoAsync(System.String,System.String,Sleekflow.Apis.InternalIntegrationHub.Model.CreatePaymentFromStripeInput,System.Threading.CancellationToken)">
            <summary>
             
            </summary>
            <exception cref="T:Sleekflow.Apis.InternalIntegrationHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="xSleekflowDistributedInvocationContext"> (optional)</param>
            <param name="createPaymentFromStripeInput"> (optional)</param>
            <param name="cancellationToken">Cancellation Token to cancel the request.</param>
            <returns>Task of ApiResponse (CreatePaymentFromStripeOutputOutput)</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Api.NetSuiteInternalApi.NetSuiteInternalUpdateCustomerPost(System.String,System.String,Sleekflow.Apis.InternalIntegrationHub.Model.UpdateCustomerInput)">
            <summary>
             
            </summary>
            <exception cref="T:Sleekflow.Apis.InternalIntegrationHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="xSleekflowDistributedInvocationContext"> (optional)</param>
            <param name="updateCustomerInput"> (optional)</param>
            <returns>UpdateCustomerOutputOutput</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Api.NetSuiteInternalApi.NetSuiteInternalUpdateCustomerPostWithHttpInfo(System.String,System.String,Sleekflow.Apis.InternalIntegrationHub.Model.UpdateCustomerInput)">
            <summary>
             
            </summary>
            <exception cref="T:Sleekflow.Apis.InternalIntegrationHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="xSleekflowDistributedInvocationContext"> (optional)</param>
            <param name="updateCustomerInput"> (optional)</param>
            <returns>ApiResponse of UpdateCustomerOutputOutput</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Api.NetSuiteInternalApi.NetSuiteInternalUpdateCustomerPostAsync(System.String,System.String,Sleekflow.Apis.InternalIntegrationHub.Model.UpdateCustomerInput,System.Threading.CancellationToken)">
            <summary>
             
            </summary>
            <exception cref="T:Sleekflow.Apis.InternalIntegrationHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="xSleekflowDistributedInvocationContext"> (optional)</param>
            <param name="updateCustomerInput"> (optional)</param>
            <param name="cancellationToken">Cancellation Token to cancel the request.</param>
            <returns>Task of UpdateCustomerOutputOutput</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Api.NetSuiteInternalApi.NetSuiteInternalUpdateCustomerPostWithHttpInfoAsync(System.String,System.String,Sleekflow.Apis.InternalIntegrationHub.Model.UpdateCustomerInput,System.Threading.CancellationToken)">
            <summary>
             
            </summary>
            <exception cref="T:Sleekflow.Apis.InternalIntegrationHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="xSleekflowDistributedInvocationContext"> (optional)</param>
            <param name="updateCustomerInput"> (optional)</param>
            <param name="cancellationToken">Cancellation Token to cancel the request.</param>
            <returns>Task of ApiResponse (UpdateCustomerOutputOutput)</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Api.NetSuiteInternalApi.NetSuiteInternalUpdateEmployeeManuallyPost(System.String,System.String,Sleekflow.Apis.InternalIntegrationHub.Model.UpdateEmployeeManuallyInput)">
            <summary>
             
            </summary>
            <exception cref="T:Sleekflow.Apis.InternalIntegrationHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="xSleekflowDistributedInvocationContext"> (optional)</param>
            <param name="updateEmployeeManuallyInput"> (optional)</param>
            <returns>UpdateEmployeeManuallyOutputOutput</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Api.NetSuiteInternalApi.NetSuiteInternalUpdateEmployeeManuallyPostWithHttpInfo(System.String,System.String,Sleekflow.Apis.InternalIntegrationHub.Model.UpdateEmployeeManuallyInput)">
            <summary>
             
            </summary>
            <exception cref="T:Sleekflow.Apis.InternalIntegrationHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="xSleekflowDistributedInvocationContext"> (optional)</param>
            <param name="updateEmployeeManuallyInput"> (optional)</param>
            <returns>ApiResponse of UpdateEmployeeManuallyOutputOutput</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Api.NetSuiteInternalApi.NetSuiteInternalUpdateEmployeeManuallyPostAsync(System.String,System.String,Sleekflow.Apis.InternalIntegrationHub.Model.UpdateEmployeeManuallyInput,System.Threading.CancellationToken)">
            <summary>
             
            </summary>
            <exception cref="T:Sleekflow.Apis.InternalIntegrationHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="xSleekflowDistributedInvocationContext"> (optional)</param>
            <param name="updateEmployeeManuallyInput"> (optional)</param>
            <param name="cancellationToken">Cancellation Token to cancel the request.</param>
            <returns>Task of UpdateEmployeeManuallyOutputOutput</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Api.NetSuiteInternalApi.NetSuiteInternalUpdateEmployeeManuallyPostWithHttpInfoAsync(System.String,System.String,Sleekflow.Apis.InternalIntegrationHub.Model.UpdateEmployeeManuallyInput,System.Threading.CancellationToken)">
            <summary>
             
            </summary>
            <exception cref="T:Sleekflow.Apis.InternalIntegrationHub.Client.ApiException">Thrown when fails to make API call</exception>
            <param name="X_API_VERSION"> (optional)</param>
            <param name="xSleekflowDistributedInvocationContext"> (optional)</param>
            <param name="updateEmployeeManuallyInput"> (optional)</param>
            <param name="cancellationToken">Cancellation Token to cancel the request.</param>
            <returns>Task of ApiResponse (UpdateEmployeeManuallyOutputOutput)</returns>
        </member>
        <member name="T:Sleekflow.Apis.InternalIntegrationHub.Client.CustomJsonCodec">
            <summary>
            To Serialize/Deserialize JSON using our custom logic, but only when ContentType is JSON.
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Client.CustomJsonCodec.Serialize(System.Object)">
            <summary>
            Serialize the object into a JSON string.
            </summary>
            <param name="obj">Object to be serialized.</param>
            <returns>A JSON string.</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Client.CustomJsonCodec.Deserialize(System.Net.Http.HttpResponseMessage,System.Type)">
            <summary>
            Deserialize the JSON string into a proper object.
            </summary>
            <param name="response">The HTTP response.</param>
            <param name="type">Object type.</param>
            <returns>Object representation of the JSON string.</returns>
        </member>
        <member name="T:Sleekflow.Apis.InternalIntegrationHub.Client.ApiClient">
            <summary>
            Provides a default implementation of an Api client (both synchronous and asynchronous implementations),
            encapsulating general REST accessor use cases.
            </summary>
            <remarks>
            The Dispose method will manage the HttpClient lifecycle when not passed by constructor.
            </remarks>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Client.ApiClient.SerializerSettings">
            <summary>
            Specifies the settings on a <see cref="T:Newtonsoft.Json.JsonSerializer" /> object.
            These settings can be adjusted to accommodate custom serialization rules.
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Client.ApiClient.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.InternalIntegrationHub.Client.ApiClient" />, defaulting to the global configurations' base url.
            **IMPORTANT** This will also create an instance of HttpClient, which is less than ideal.
            It's better to reuse the <see href="https://docs.microsoft.com/en-us/dotnet/architecture/microservices/implement-resilient-applications/use-httpclientfactory-to-implement-resilient-http-requests#issues-with-the-original-httpclient-class-available-in-net">HttpClient and HttpClientHandler</see>.
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Client.ApiClient.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.InternalIntegrationHub.Client.ApiClient" />.
            **IMPORTANT** This will also create an instance of HttpClient, which is less than ideal.
            It's better to reuse the <see href="https://docs.microsoft.com/en-us/dotnet/architecture/microservices/implement-resilient-applications/use-httpclientfactory-to-implement-resilient-http-requests#issues-with-the-original-httpclient-class-available-in-net">HttpClient and HttpClientHandler</see>.
            </summary>
            <param name="basePath">The target service's base path in URL format.</param>
            <exception cref="T:System.ArgumentException"></exception>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Client.ApiClient.#ctor(System.Net.Http.HttpClient,System.Net.Http.HttpClientHandler)">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.InternalIntegrationHub.Client.ApiClient" />, defaulting to the global configurations' base url.
            </summary>
            <param name="client">An instance of HttpClient.</param>
            <param name="handler">An optional instance of HttpClientHandler that is used by HttpClient.</param>
            <exception cref="T:System.ArgumentNullException"></exception>
            <remarks>
            Some configuration settings will not be applied without passing an HttpClientHandler.
            The features affected are: Setting and Retrieving Cookies, Client Certificates, Proxy settings.
            </remarks>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Client.ApiClient.#ctor(System.Net.Http.HttpClient,System.String,System.Net.Http.HttpClientHandler)">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.InternalIntegrationHub.Client.ApiClient" />.
            </summary>
            <param name="client">An instance of HttpClient.</param>
            <param name="basePath">The target service's base path in URL format.</param>
            <param name="handler">An optional instance of HttpClientHandler that is used by HttpClient.</param>
            <exception cref="T:System.ArgumentNullException"></exception>
            <exception cref="T:System.ArgumentException"></exception>
            <remarks>
            Some configuration settings will not be applied without passing an HttpClientHandler.
            The features affected are: Setting and Retrieving Cookies, Client Certificates, Proxy settings.
            </remarks>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Client.ApiClient.Dispose">
            <summary>
            Disposes resources if they were created by us
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Client.ApiClient.PrepareMultipartFormDataContent(Sleekflow.Apis.InternalIntegrationHub.Client.RequestOptions)">
            Prepares multipart/form-data content
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Client.ApiClient.NewRequest(System.Net.Http.HttpMethod,System.String,Sleekflow.Apis.InternalIntegrationHub.Client.RequestOptions,Sleekflow.Apis.InternalIntegrationHub.Client.IReadableConfiguration)">
            <summary>
            Provides all logic for constructing a new HttpRequestMessage.
            At this point, all information for querying the service is known. Here, it is simply
            mapped into the a HttpRequestMessage.
            </summary>
            <param name="method">The http verb.</param>
            <param name="path">The target path (or resource).</param>
            <param name="options">The additional request options.</param>
            <param name="configuration">A per-request configuration object. It is assumed that any merge with
            GlobalConfiguration has been done before calling this method.</param>
            <returns>[private] A new HttpRequestMessage instance.</returns>
            <exception cref="T:System.ArgumentNullException"></exception>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Client.ApiClient.GetAsync``1(System.String,Sleekflow.Apis.InternalIntegrationHub.Client.RequestOptions,Sleekflow.Apis.InternalIntegrationHub.Client.IReadableConfiguration,System.Threading.CancellationToken)">
            <summary>
            Make a HTTP GET request (async).
            </summary>
            <param name="path">The target path (or resource).</param>
            <param name="options">The additional request options.</param>
            <param name="configuration">A per-request configuration object. It is assumed that any merge with
            GlobalConfiguration has been done before calling this method.</param>
            <param name="cancellationToken">Token that enables callers to cancel the request.</param>
            <returns>A Task containing ApiResponse</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Client.ApiClient.PostAsync``1(System.String,Sleekflow.Apis.InternalIntegrationHub.Client.RequestOptions,Sleekflow.Apis.InternalIntegrationHub.Client.IReadableConfiguration,System.Threading.CancellationToken)">
            <summary>
            Make a HTTP POST request (async).
            </summary>
            <param name="path">The target path (or resource).</param>
            <param name="options">The additional request options.</param>
            <param name="configuration">A per-request configuration object. It is assumed that any merge with
            GlobalConfiguration has been done before calling this method.</param>
            <param name="cancellationToken">Token that enables callers to cancel the request.</param>
            <returns>A Task containing ApiResponse</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Client.ApiClient.PutAsync``1(System.String,Sleekflow.Apis.InternalIntegrationHub.Client.RequestOptions,Sleekflow.Apis.InternalIntegrationHub.Client.IReadableConfiguration,System.Threading.CancellationToken)">
            <summary>
            Make a HTTP PUT request (async).
            </summary>
            <param name="path">The target path (or resource).</param>
            <param name="options">The additional request options.</param>
            <param name="configuration">A per-request configuration object. It is assumed that any merge with
            GlobalConfiguration has been done before calling this method.</param>
            <param name="cancellationToken">Token that enables callers to cancel the request.</param>
            <returns>A Task containing ApiResponse</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Client.ApiClient.DeleteAsync``1(System.String,Sleekflow.Apis.InternalIntegrationHub.Client.RequestOptions,Sleekflow.Apis.InternalIntegrationHub.Client.IReadableConfiguration,System.Threading.CancellationToken)">
            <summary>
            Make a HTTP DELETE request (async).
            </summary>
            <param name="path">The target path (or resource).</param>
            <param name="options">The additional request options.</param>
            <param name="configuration">A per-request configuration object. It is assumed that any merge with
            GlobalConfiguration has been done before calling this method.</param>
            <param name="cancellationToken">Token that enables callers to cancel the request.</param>
            <returns>A Task containing ApiResponse</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Client.ApiClient.HeadAsync``1(System.String,Sleekflow.Apis.InternalIntegrationHub.Client.RequestOptions,Sleekflow.Apis.InternalIntegrationHub.Client.IReadableConfiguration,System.Threading.CancellationToken)">
            <summary>
            Make a HTTP HEAD request (async).
            </summary>
            <param name="path">The target path (or resource).</param>
            <param name="options">The additional request options.</param>
            <param name="configuration">A per-request configuration object. It is assumed that any merge with
            GlobalConfiguration has been done before calling this method.</param>
            <param name="cancellationToken">Token that enables callers to cancel the request.</param>
            <returns>A Task containing ApiResponse</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Client.ApiClient.OptionsAsync``1(System.String,Sleekflow.Apis.InternalIntegrationHub.Client.RequestOptions,Sleekflow.Apis.InternalIntegrationHub.Client.IReadableConfiguration,System.Threading.CancellationToken)">
            <summary>
            Make a HTTP OPTION request (async).
            </summary>
            <param name="path">The target path (or resource).</param>
            <param name="options">The additional request options.</param>
            <param name="configuration">A per-request configuration object. It is assumed that any merge with
            GlobalConfiguration has been done before calling this method.</param>
            <param name="cancellationToken">Token that enables callers to cancel the request.</param>
            <returns>A Task containing ApiResponse</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Client.ApiClient.PatchAsync``1(System.String,Sleekflow.Apis.InternalIntegrationHub.Client.RequestOptions,Sleekflow.Apis.InternalIntegrationHub.Client.IReadableConfiguration,System.Threading.CancellationToken)">
            <summary>
            Make a HTTP PATCH request (async).
            </summary>
            <param name="path">The target path (or resource).</param>
            <param name="options">The additional request options.</param>
            <param name="configuration">A per-request configuration object. It is assumed that any merge with
            GlobalConfiguration has been done before calling this method.</param>
            <param name="cancellationToken">Token that enables callers to cancel the request.</param>
            <returns>A Task containing ApiResponse</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Client.ApiClient.Get``1(System.String,Sleekflow.Apis.InternalIntegrationHub.Client.RequestOptions,Sleekflow.Apis.InternalIntegrationHub.Client.IReadableConfiguration)">
            <summary>
            Make a HTTP GET request (synchronous).
            </summary>
            <param name="path">The target path (or resource).</param>
            <param name="options">The additional request options.</param>
            <param name="configuration">A per-request configuration object. It is assumed that any merge with
            GlobalConfiguration has been done before calling this method.</param>
            <returns>A Task containing ApiResponse</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Client.ApiClient.Post``1(System.String,Sleekflow.Apis.InternalIntegrationHub.Client.RequestOptions,Sleekflow.Apis.InternalIntegrationHub.Client.IReadableConfiguration)">
            <summary>
            Make a HTTP POST request (synchronous).
            </summary>
            <param name="path">The target path (or resource).</param>
            <param name="options">The additional request options.</param>
            <param name="configuration">A per-request configuration object. It is assumed that any merge with
            GlobalConfiguration has been done before calling this method.</param>
            <returns>A Task containing ApiResponse</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Client.ApiClient.Put``1(System.String,Sleekflow.Apis.InternalIntegrationHub.Client.RequestOptions,Sleekflow.Apis.InternalIntegrationHub.Client.IReadableConfiguration)">
            <summary>
            Make a HTTP PUT request (synchronous).
            </summary>
            <param name="path">The target path (or resource).</param>
            <param name="options">The additional request options.</param>
            <param name="configuration">A per-request configuration object. It is assumed that any merge with
            GlobalConfiguration has been done before calling this method.</param>
            <returns>A Task containing ApiResponse</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Client.ApiClient.Delete``1(System.String,Sleekflow.Apis.InternalIntegrationHub.Client.RequestOptions,Sleekflow.Apis.InternalIntegrationHub.Client.IReadableConfiguration)">
            <summary>
            Make a HTTP DELETE request (synchronous).
            </summary>
            <param name="path">The target path (or resource).</param>
            <param name="options">The additional request options.</param>
            <param name="configuration">A per-request configuration object. It is assumed that any merge with
            GlobalConfiguration has been done before calling this method.</param>
            <returns>A Task containing ApiResponse</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Client.ApiClient.Head``1(System.String,Sleekflow.Apis.InternalIntegrationHub.Client.RequestOptions,Sleekflow.Apis.InternalIntegrationHub.Client.IReadableConfiguration)">
            <summary>
            Make a HTTP HEAD request (synchronous).
            </summary>
            <param name="path">The target path (or resource).</param>
            <param name="options">The additional request options.</param>
            <param name="configuration">A per-request configuration object. It is assumed that any merge with
            GlobalConfiguration has been done before calling this method.</param>
            <returns>A Task containing ApiResponse</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Client.ApiClient.Options``1(System.String,Sleekflow.Apis.InternalIntegrationHub.Client.RequestOptions,Sleekflow.Apis.InternalIntegrationHub.Client.IReadableConfiguration)">
            <summary>
            Make a HTTP OPTION request (synchronous).
            </summary>
            <param name="path">The target path (or resource).</param>
            <param name="options">The additional request options.</param>
            <param name="configuration">A per-request configuration object. It is assumed that any merge with
            GlobalConfiguration has been done before calling this method.</param>
            <returns>A Task containing ApiResponse</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Client.ApiClient.Patch``1(System.String,Sleekflow.Apis.InternalIntegrationHub.Client.RequestOptions,Sleekflow.Apis.InternalIntegrationHub.Client.IReadableConfiguration)">
            <summary>
            Make a HTTP PATCH request (synchronous).
            </summary>
            <param name="path">The target path (or resource).</param>
            <param name="options">The additional request options.</param>
            <param name="configuration">A per-request configuration object. It is assumed that any merge with
            GlobalConfiguration has been done before calling this method.</param>
            <returns>A Task containing ApiResponse</returns>
        </member>
        <member name="T:Sleekflow.Apis.InternalIntegrationHub.Client.ApiException">
            <summary>
            API Exception
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Client.ApiException.ErrorCode">
            <summary>
            Gets or sets the error code (HTTP status code)
            </summary>
            <value>The error code (HTTP status code).</value>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Client.ApiException.ErrorContent">
            <summary>
            Gets or sets the error content (body json object)
            </summary>
            <value>The error content (Http response body).</value>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Client.ApiException.Headers">
            <summary>
            Gets or sets the HTTP headers
            </summary>
            <value>HTTP headers</value>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Client.ApiException.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.InternalIntegrationHub.Client.ApiException"/> class.
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Client.ApiException.#ctor(System.Int32,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.InternalIntegrationHub.Client.ApiException"/> class.
            </summary>
            <param name="errorCode">HTTP status code.</param>
            <param name="message">Error message.</param>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Client.ApiException.#ctor(System.Int32,System.String,System.Object,Sleekflow.Apis.InternalIntegrationHub.Client.Multimap{System.String,System.String})">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.InternalIntegrationHub.Client.ApiException"/> class.
            </summary>
            <param name="errorCode">HTTP status code.</param>
            <param name="message">Error message.</param>
            <param name="errorContent">Error content.</param>
            <param name="headers">HTTP Headers.</param>
        </member>
        <member name="T:Sleekflow.Apis.InternalIntegrationHub.Client.IApiResponse">
            <summary>
            Provides a non-generic contract for the ApiResponse wrapper.
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Client.IApiResponse.ResponseType">
            <summary>
            The data type of <see cref="P:Sleekflow.Apis.InternalIntegrationHub.Client.IApiResponse.Content"/>
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Client.IApiResponse.Content">
            <summary>
            The content of this response
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Client.IApiResponse.StatusCode">
            <summary>
            Gets or sets the status code (HTTP status code)
            </summary>
            <value>The status code.</value>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Client.IApiResponse.Headers">
            <summary>
            Gets or sets the HTTP headers
            </summary>
            <value>HTTP headers</value>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Client.IApiResponse.ErrorText">
            <summary>
            Gets or sets any error text defined by the calling client.
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Client.IApiResponse.Cookies">
            <summary>
            Gets or sets any cookies passed along on the response.
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Client.IApiResponse.RawContent">
            <summary>
            The raw content of this response
            </summary>
        </member>
        <member name="T:Sleekflow.Apis.InternalIntegrationHub.Client.ApiResponse`1">
            <summary>
            API Response
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Client.ApiResponse`1.StatusCode">
            <summary>
            Gets or sets the status code (HTTP status code)
            </summary>
            <value>The status code.</value>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Client.ApiResponse`1.Headers">
            <summary>
            Gets or sets the HTTP headers
            </summary>
            <value>HTTP headers</value>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Client.ApiResponse`1.Data">
            <summary>
            Gets or sets the data (parsed HTTP body)
            </summary>
            <value>The data.</value>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Client.ApiResponse`1.ErrorText">
            <summary>
            Gets or sets any error text defined by the calling client.
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Client.ApiResponse`1.Cookies">
            <summary>
            Gets or sets any cookies passed along on the response.
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Client.ApiResponse`1.ResponseType">
            <summary>
            The content of this response
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Client.ApiResponse`1.Content">
            <summary>
            The data type of <see cref="P:Sleekflow.Apis.InternalIntegrationHub.Client.ApiResponse`1.Content"/>
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Client.ApiResponse`1.RawContent">
            <summary>
            The raw content
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Client.ApiResponse`1.#ctor(System.Net.HttpStatusCode,Sleekflow.Apis.InternalIntegrationHub.Client.Multimap{System.String,System.String},`0,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.InternalIntegrationHub.Client.ApiResponse`1" /> class.
            </summary>
            <param name="statusCode">HTTP status code.</param>
            <param name="headers">HTTP headers.</param>
            <param name="data">Data (parsed HTTP body)</param>
            <param name="rawContent">Raw content.</param>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Client.ApiResponse`1.#ctor(System.Net.HttpStatusCode,Sleekflow.Apis.InternalIntegrationHub.Client.Multimap{System.String,System.String},`0)">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.InternalIntegrationHub.Client.ApiResponse`1" /> class.
            </summary>
            <param name="statusCode">HTTP status code.</param>
            <param name="headers">HTTP headers.</param>
            <param name="data">Data (parsed HTTP body)</param>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Client.ApiResponse`1.#ctor(System.Net.HttpStatusCode,`0,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.InternalIntegrationHub.Client.ApiResponse`1" /> class.
            </summary>
            <param name="statusCode">HTTP status code.</param>
            <param name="data">Data (parsed HTTP body)</param>
            <param name="rawContent">Raw content.</param>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Client.ApiResponse`1.#ctor(System.Net.HttpStatusCode,`0)">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.InternalIntegrationHub.Client.ApiResponse`1" /> class.
            </summary>
            <param name="statusCode">HTTP status code.</param>
            <param name="data">Data (parsed HTTP body)</param>
        </member>
        <member name="T:Sleekflow.Apis.InternalIntegrationHub.Client.ClientUtils">
            <summary>
            Utility functions providing some benefit to API client consumers.
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Client.ClientUtils.SanitizeFilename(System.String)">
            <summary>
            Sanitize filename by removing the path
            </summary>
            <param name="filename">Filename</param>
            <returns>Filename</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Client.ClientUtils.ParameterToMultiMap(System.String,System.String,System.Object)">
            <summary>
            Convert params to key/value pairs.
            Use collectionFormat to properly format lists and collections.
            </summary>
            <param name="collectionFormat">The swagger-supported collection format, one of: csv, tsv, ssv, pipes, multi</param>
            <param name="name">Key name.</param>
            <param name="value">Value object.</param>
            <returns>A multimap of keys with 1..n associated values.</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Client.ClientUtils.ParameterToString(System.Object,Sleekflow.Apis.InternalIntegrationHub.Client.IReadableConfiguration)">
            <summary>
            If parameter is DateTime, output in a formatted string (default ISO 8601), customizable with Configuration.DateTime.
            If parameter is a list, join the list with ",".
            Otherwise just return the string.
            </summary>
            <param name="obj">The parameter (header, path, query, form).</param>
            <param name="configuration">An optional configuration instance, providing formatting options used in processing.</param>
            <returns>Formatted string.</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Client.ClientUtils.Base64Encode(System.String)">
            <summary>
            Encode string in base64 format.
            </summary>
            <param name="text">string to be encoded.</param>
            <returns>Encoded string.</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Client.ClientUtils.ReadAsBytes(System.IO.Stream)">
            <summary>
            Convert stream to byte array
            </summary>
            <param name="inputStream">Input stream to be converted</param>
            <returns>Byte array</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Client.ClientUtils.SelectHeaderContentType(System.String[])">
            <summary>
            Select the Content-Type header's value from the given content-type array:
            if JSON type exists in the given array, use it;
            otherwise use the first one defined in 'consumes'
            </summary>
            <param name="contentTypes">The Content-Type array to select from.</param>
            <returns>The Content-Type header to use.</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Client.ClientUtils.SelectHeaderAccept(System.String[])">
            <summary>
            Select the Accept header's value from the given accepts array:
            if JSON exists in the given array, use it;
            otherwise use all of them (joining into a string)
            </summary>
            <param name="accepts">The accepts array to select from.</param>
            <returns>The Accept header to use.</returns>
        </member>
        <member name="F:Sleekflow.Apis.InternalIntegrationHub.Client.ClientUtils.JsonRegex">
            <summary>
            Provides a case-insensitive check that a provided content type is a known JSON-like content type.
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Client.ClientUtils.IsJsonMime(System.String)">
            <summary>
            Check if the given MIME is a JSON MIME.
            JSON MIME examples:
               application/json
               application/json; charset=UTF8
               APPLICATION/JSON
               application/vnd.company+json
            </summary>
            <param name="mime">MIME</param>
            <returns>Returns True if MIME type is json.</returns>
        </member>
        <member name="T:Sleekflow.Apis.InternalIntegrationHub.Client.Configuration">
            <summary>
            Represents a set of configuration settings
            </summary>
        </member>
        <member name="F:Sleekflow.Apis.InternalIntegrationHub.Client.Configuration.Version">
            <summary>
            Version of the package.
            </summary>
            <value>Version of the package.</value>
        </member>
        <member name="F:Sleekflow.Apis.InternalIntegrationHub.Client.Configuration.ISO8601_DATETIME_FORMAT">
            <summary>
            Identifier for ISO 8601 DateTime Format
            </summary>
            <remarks>See https://msdn.microsoft.com/en-us/library/az4se3k1(v=vs.110).aspx#Anchor_8 for more information.</remarks>
        </member>
        <member name="F:Sleekflow.Apis.InternalIntegrationHub.Client.Configuration.DefaultExceptionFactory">
            <summary>
            Default creation of exceptions for a given method name and response object
            </summary>
        </member>
        <member name="F:Sleekflow.Apis.InternalIntegrationHub.Client.Configuration._basePath">
            <summary>
            Defines the base path of the target API server.
            Example: http://localhost:3000/v1/
            </summary>
        </member>
        <member name="F:Sleekflow.Apis.InternalIntegrationHub.Client.Configuration._apiKey">
            <summary>
            Gets or sets the API key based on the authentication name.
            This is the key and value comprising the "secret" for accessing an API.
            </summary>
            <value>The API key.</value>
        </member>
        <member name="F:Sleekflow.Apis.InternalIntegrationHub.Client.Configuration._apiKeyPrefix">
            <summary>
            Gets or sets the prefix (e.g. Token) of the API key based on the authentication name.
            </summary>
            <value>The prefix of the API key.</value>
        </member>
        <member name="F:Sleekflow.Apis.InternalIntegrationHub.Client.Configuration._servers">
            <summary>
            Gets or sets the servers defined in the OpenAPI spec.
            </summary>
            <value>The servers</value>
        </member>
        <member name="F:Sleekflow.Apis.InternalIntegrationHub.Client.Configuration._operationServers">
            <summary>
            Gets or sets the operation servers defined in the OpenAPI spec.
            </summary>
            <value>The operation servers</value>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Client.Configuration.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.InternalIntegrationHub.Client.Configuration" /> class
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Client.Configuration.#ctor(System.Collections.Generic.IDictionary{System.String,System.String},System.Collections.Generic.IDictionary{System.String,System.String},System.Collections.Generic.IDictionary{System.String,System.String},System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.InternalIntegrationHub.Client.Configuration" /> class
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Client.Configuration.BasePath">
            <summary>
            Gets or sets the base path for API access.
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Client.Configuration.DefaultHeader">
            <summary>
            Gets or sets the default header.
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Client.Configuration.DefaultHeaders">
            <summary>
            Gets or sets the default headers.
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Client.Configuration.Timeout">
            <summary>
            Gets or sets the HTTP timeout (milliseconds) of ApiClient. Default to 100000 milliseconds.
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Client.Configuration.Proxy">
            <summary>
            Gets or sets the proxy
            </summary>
            <value>Proxy.</value>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Client.Configuration.UserAgent">
            <summary>
            Gets or sets the HTTP user agent.
            </summary>
            <value>Http user agent.</value>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Client.Configuration.Username">
            <summary>
            Gets or sets the username (HTTP basic authentication).
            </summary>
            <value>The username.</value>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Client.Configuration.Password">
            <summary>
            Gets or sets the password (HTTP basic authentication).
            </summary>
            <value>The password.</value>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Client.Configuration.GetApiKeyWithPrefix(System.String)">
            <summary>
            Gets the API key with prefix.
            </summary>
            <param name="apiKeyIdentifier">API key identifier (authentication scheme).</param>
            <returns>API key with prefix.</returns>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Client.Configuration.ClientCertificates">
            <summary>
            Gets or sets certificate collection to be sent with requests.
            </summary>
            <value>X509 Certificate collection.</value>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Client.Configuration.AccessToken">
             <summary>
             Gets or sets the access token for OAuth2 authentication.
            
             This helper property simplifies code generation.
             </summary>
             <value>The access token.</value>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Client.Configuration.TempFolderPath">
            <summary>
            Gets or sets the temporary folder path to store the files downloaded from the server.
            </summary>
            <value>Folder path.</value>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Client.Configuration.DateTimeFormat">
            <summary>
            Gets or sets the date time format used when serializing in the ApiClient
            By default, it's set to ISO 8601 - "o", for others see:
            https://msdn.microsoft.com/en-us/library/az4se3k1(v=vs.110).aspx
            and https://msdn.microsoft.com/en-us/library/8kb3ddd4(v=vs.110).aspx
            No validation is done to ensure that the string you're providing is valid
            </summary>
            <value>The DateTimeFormat string</value>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Client.Configuration.ApiKeyPrefix">
             <summary>
             Gets or sets the prefix (e.g. Token) of the API key based on the authentication name.
            
             Whatever you set here will be prepended to the value defined in AddApiKey.
            
             An example invocation here might be:
             <example>
             ApiKeyPrefix["Authorization"] = "Bearer";
             </example>
             … where ApiKey["Authorization"] would then be used to set the value of your bearer token.
            
             <remarks>
             OAuth2 workflows should set tokens via AccessToken.
             </remarks>
             </summary>
             <value>The prefix of the API key.</value>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Client.Configuration.ApiKey">
            <summary>
            Gets or sets the API key based on the authentication name.
            </summary>
            <value>The API key.</value>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Client.Configuration.Servers">
            <summary>
            Gets or sets the servers.
            </summary>
            <value>The servers.</value>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Client.Configuration.OperationServers">
            <summary>
            Gets or sets the operation servers.
            </summary>
            <value>The operation servers.</value>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Client.Configuration.GetServerUrl(System.Int32)">
            <summary>
            Returns URL based on server settings without providing values
            for the variables
            </summary>
            <param name="index">Array index of the server settings.</param>
            <return>The server URL.</return>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Client.Configuration.GetServerUrl(System.Int32,System.Collections.Generic.Dictionary{System.String,System.String})">
            <summary>
            Returns URL based on server settings.
            </summary>
            <param name="index">Array index of the server settings.</param>
            <param name="inputVariables">Dictionary of the variables and the corresponding values.</param>
            <return>The server URL.</return>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Client.Configuration.GetOperationServerUrl(System.String,System.Int32)">
            <summary>
            Returns URL based on operation server settings.
            </summary>
            <param name="operation">Operation associated with the request path.</param>
            <param name="index">Array index of the server settings.</param>
            <return>The operation server URL.</return>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Client.Configuration.GetOperationServerUrl(System.String,System.Int32,System.Collections.Generic.Dictionary{System.String,System.String})">
            <summary>
            Returns URL based on operation server settings.
            </summary>
            <param name="operation">Operation associated with the request path.</param>
            <param name="index">Array index of the server settings.</param>
            <param name="inputVariables">Dictionary of the variables and the corresponding values.</param>
            <return>The operation server URL.</return>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Client.Configuration.GetServerUrl(System.Collections.Generic.IList{System.Collections.Generic.IReadOnlyDictionary{System.String,System.Object}},System.Int32,System.Collections.Generic.Dictionary{System.String,System.String})">
            <summary>
            Returns URL based on server settings.
            </summary>
            <param name="servers">Dictionary of server settings.</param>
            <param name="index">Array index of the server settings.</param>
            <param name="inputVariables">Dictionary of the variables and the corresponding values.</param>
            <return>The server URL.</return>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Client.Configuration.ToDebugReport">
            <summary>
            Returns a string with essential information for debugging.
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Client.Configuration.AddApiKey(System.String,System.String)">
            <summary>
            Add Api Key Header.
            </summary>
            <param name="key">Api Key name.</param>
            <param name="value">Api Key value.</param>
            <returns></returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Client.Configuration.AddApiKeyPrefix(System.String,System.String)">
            <summary>
            Sets the API key prefix.
            </summary>
            <param name="key">Api Key name.</param>
            <param name="value">Api Key value.</param>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Client.Configuration.MergeConfigurations(Sleekflow.Apis.InternalIntegrationHub.Client.IReadableConfiguration,Sleekflow.Apis.InternalIntegrationHub.Client.IReadableConfiguration)">
            <summary>
            Merge configurations.
            </summary>
            <param name="first">First configuration.</param>
            <param name="second">Second configuration.</param>
            <return>Merged configuration.</return>
        </member>
        <member name="T:Sleekflow.Apis.InternalIntegrationHub.Client.ExceptionFactory">
            <summary>
            A delegate to ExceptionFactory method
            </summary>
            <param name="methodName">Method name</param>
            <param name="response">Response</param>
            <returns>Exceptions</returns>
        </member>
        <member name="T:Sleekflow.Apis.InternalIntegrationHub.Client.FileParameter">
            <summary>
            Represents a File passed to the API as a Parameter, allows using different backends for files
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Client.FileParameter.Name">
            <summary>
            The filename
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Client.FileParameter.ContentType">
            <summary>
            The content type of the file
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Client.FileParameter.Content">
            <summary>
            The content of the file
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Client.FileParameter.#ctor(System.IO.Stream)">
            <summary>
            Construct a FileParameter just from the contents, will extract the filename from a filestream
            </summary>
            <param name="content"> The file content </param>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Client.FileParameter.#ctor(System.String,System.IO.Stream)">
            <summary>
            Construct a FileParameter from name and content
            </summary>
            <param name="filename">The filename</param>
            <param name="content">The file content</param>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Client.FileParameter.#ctor(System.String,System.String,System.IO.Stream)">
            <summary>
            Construct a FileParameter from name and content
            </summary>
            <param name="filename">The filename</param>
            <param name="contentType">The content type of the file</param>
            <param name="content">The file content</param>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Client.FileParameter.op_Implicit(System.IO.Stream)~Sleekflow.Apis.InternalIntegrationHub.Client.FileParameter">
            <summary>
            Implicit conversion of stream to file parameter. Useful for backwards compatibility.
            </summary>
            <param name="s">Stream to convert</param>
            <returns>FileParameter</returns>
        </member>
        <member name="T:Sleekflow.Apis.InternalIntegrationHub.Client.GlobalConfiguration">
            <summary>
            <see cref="T:Sleekflow.Apis.InternalIntegrationHub.Client.GlobalConfiguration"/> provides a compile-time extension point for globally configuring
            API Clients.
            </summary>
            <remarks>
            A customized implementation via partial class may reside in another file and may
            be excluded from automatic generation via a .openapi-generator-ignore file.
            </remarks>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Client.GlobalConfiguration.#ctor">
            <inheritdoc />
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Client.GlobalConfiguration.#ctor(System.Collections.Generic.IDictionary{System.String,System.String},System.Collections.Generic.IDictionary{System.String,System.String},System.Collections.Generic.IDictionary{System.String,System.String},System.String)">
            <inheritdoc />
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Client.GlobalConfiguration.Instance">
            <summary>
            Gets or sets the default Configuration.
            </summary>
            <value>Configuration.</value>
        </member>
        <member name="T:Sleekflow.Apis.InternalIntegrationHub.Client.IApiAccessor">
            <summary>
            Represents configuration aspects required to interact with the API endpoints.
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Client.IApiAccessor.Configuration">
            <summary>
            Gets or sets the configuration object
            </summary>
            <value>An instance of the Configuration</value>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Client.IApiAccessor.GetBasePath">
            <summary>
            Gets the base path of the API client.
            </summary>
            <value>The base path</value>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Client.IApiAccessor.ExceptionFactory">
            <summary>
            Provides a factory method hook for the creation of exceptions.
            </summary>
        </member>
        <member name="T:Sleekflow.Apis.InternalIntegrationHub.Client.IAsynchronousClient">
             <summary>
             Contract for Asynchronous RESTful API interactions.
            
             This interface allows consumers to provide a custom API accessor client.
             </summary>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Client.IAsynchronousClient.GetAsync``1(System.String,Sleekflow.Apis.InternalIntegrationHub.Client.RequestOptions,Sleekflow.Apis.InternalIntegrationHub.Client.IReadableConfiguration,System.Threading.CancellationToken)">
            <summary>
            Executes a non-blocking call to some <paramref name="path"/> using the GET http verb.
            </summary>
            <param name="path">The relative path to invoke.</param>
            <param name="options">The request parameters to pass along to the client.</param>
            <param name="configuration">Per-request configurable settings.</param>
            <param name="cancellationToken">Cancellation Token to cancel the request.</param>
            <typeparam name="T">The return type.</typeparam>
            <returns>A task eventually representing the response data, decorated with <see cref="T:Sleekflow.Apis.InternalIntegrationHub.Client.ApiResponse`1"/></returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Client.IAsynchronousClient.PostAsync``1(System.String,Sleekflow.Apis.InternalIntegrationHub.Client.RequestOptions,Sleekflow.Apis.InternalIntegrationHub.Client.IReadableConfiguration,System.Threading.CancellationToken)">
            <summary>
            Executes a non-blocking call to some <paramref name="path"/> using the POST http verb.
            </summary>
            <param name="path">The relative path to invoke.</param>
            <param name="options">The request parameters to pass along to the client.</param>
            <param name="configuration">Per-request configurable settings.</param>
            <param name="cancellationToken">Cancellation Token to cancel the request.</param>
            <typeparam name="T">The return type.</typeparam>
            <returns>A task eventually representing the response data, decorated with <see cref="T:Sleekflow.Apis.InternalIntegrationHub.Client.ApiResponse`1"/></returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Client.IAsynchronousClient.PutAsync``1(System.String,Sleekflow.Apis.InternalIntegrationHub.Client.RequestOptions,Sleekflow.Apis.InternalIntegrationHub.Client.IReadableConfiguration,System.Threading.CancellationToken)">
            <summary>
            Executes a non-blocking call to some <paramref name="path"/> using the PUT http verb.
            </summary>
            <param name="path">The relative path to invoke.</param>
            <param name="options">The request parameters to pass along to the client.</param>
            <param name="configuration">Per-request configurable settings.</param>
            <param name="cancellationToken">Cancellation Token to cancel the request.</param>
            <typeparam name="T">The return type.</typeparam>
            <returns>A task eventually representing the response data, decorated with <see cref="T:Sleekflow.Apis.InternalIntegrationHub.Client.ApiResponse`1"/></returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Client.IAsynchronousClient.DeleteAsync``1(System.String,Sleekflow.Apis.InternalIntegrationHub.Client.RequestOptions,Sleekflow.Apis.InternalIntegrationHub.Client.IReadableConfiguration,System.Threading.CancellationToken)">
            <summary>
            Executes a non-blocking call to some <paramref name="path"/> using the DELETE http verb.
            </summary>
            <param name="path">The relative path to invoke.</param>
            <param name="options">The request parameters to pass along to the client.</param>
            <param name="configuration">Per-request configurable settings.</param>
            <param name="cancellationToken">Cancellation Token to cancel the request.</param>
            <typeparam name="T">The return type.</typeparam>
            <returns>A task eventually representing the response data, decorated with <see cref="T:Sleekflow.Apis.InternalIntegrationHub.Client.ApiResponse`1"/></returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Client.IAsynchronousClient.HeadAsync``1(System.String,Sleekflow.Apis.InternalIntegrationHub.Client.RequestOptions,Sleekflow.Apis.InternalIntegrationHub.Client.IReadableConfiguration,System.Threading.CancellationToken)">
            <summary>
            Executes a non-blocking call to some <paramref name="path"/> using the HEAD http verb.
            </summary>
            <param name="path">The relative path to invoke.</param>
            <param name="options">The request parameters to pass along to the client.</param>
            <param name="configuration">Per-request configurable settings.</param>
            <param name="cancellationToken">Cancellation Token to cancel the request.</param>
            <typeparam name="T">The return type.</typeparam>
            <returns>A task eventually representing the response data, decorated with <see cref="T:Sleekflow.Apis.InternalIntegrationHub.Client.ApiResponse`1"/></returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Client.IAsynchronousClient.OptionsAsync``1(System.String,Sleekflow.Apis.InternalIntegrationHub.Client.RequestOptions,Sleekflow.Apis.InternalIntegrationHub.Client.IReadableConfiguration,System.Threading.CancellationToken)">
            <summary>
            Executes a non-blocking call to some <paramref name="path"/> using the OPTIONS http verb.
            </summary>
            <param name="path">The relative path to invoke.</param>
            <param name="options">The request parameters to pass along to the client.</param>
            <param name="configuration">Per-request configurable settings.</param>
            <param name="cancellationToken">Cancellation Token to cancel the request.</param>
            <typeparam name="T">The return type.</typeparam>
            <returns>A task eventually representing the response data, decorated with <see cref="T:Sleekflow.Apis.InternalIntegrationHub.Client.ApiResponse`1"/></returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Client.IAsynchronousClient.PatchAsync``1(System.String,Sleekflow.Apis.InternalIntegrationHub.Client.RequestOptions,Sleekflow.Apis.InternalIntegrationHub.Client.IReadableConfiguration,System.Threading.CancellationToken)">
            <summary>
            Executes a non-blocking call to some <paramref name="path"/> using the PATCH http verb.
            </summary>
            <param name="path">The relative path to invoke.</param>
            <param name="options">The request parameters to pass along to the client.</param>
            <param name="configuration">Per-request configurable settings.</param>
            <param name="cancellationToken">Cancellation Token to cancel the request.</param>
            <typeparam name="T">The return type.</typeparam>
            <returns>A task eventually representing the response data, decorated with <see cref="T:Sleekflow.Apis.InternalIntegrationHub.Client.ApiResponse`1"/></returns>
        </member>
        <member name="T:Sleekflow.Apis.InternalIntegrationHub.Client.IReadableConfiguration">
            <summary>
            Represents a readable-only configuration contract.
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Client.IReadableConfiguration.AccessToken">
            <summary>
            Gets the access token.
            </summary>
            <value>Access token.</value>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Client.IReadableConfiguration.ApiKey">
            <summary>
            Gets the API key.
            </summary>
            <value>API key.</value>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Client.IReadableConfiguration.ApiKeyPrefix">
            <summary>
            Gets the API key prefix.
            </summary>
            <value>API key prefix.</value>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Client.IReadableConfiguration.BasePath">
            <summary>
            Gets the base path.
            </summary>
            <value>Base path.</value>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Client.IReadableConfiguration.DateTimeFormat">
            <summary>
            Gets the date time format.
            </summary>
            <value>Date time format.</value>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Client.IReadableConfiguration.DefaultHeader">
            <summary>
            Gets the default header.
            </summary>
            <value>Default header.</value>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Client.IReadableConfiguration.DefaultHeaders">
            <summary>
            Gets the default headers.
            </summary>
            <value>Default headers.</value>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Client.IReadableConfiguration.TempFolderPath">
            <summary>
            Gets the temp folder path.
            </summary>
            <value>Temp folder path.</value>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Client.IReadableConfiguration.Timeout">
            <summary>
            Gets the HTTP connection timeout (in milliseconds)
            </summary>
            <value>HTTP connection timeout.</value>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Client.IReadableConfiguration.Proxy">
            <summary>
            Gets the proxy.
            </summary>
            <value>Proxy.</value>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Client.IReadableConfiguration.UserAgent">
            <summary>
            Gets the user agent.
            </summary>
            <value>User agent.</value>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Client.IReadableConfiguration.Username">
            <summary>
            Gets the username.
            </summary>
            <value>Username.</value>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Client.IReadableConfiguration.Password">
            <summary>
            Gets the password.
            </summary>
            <value>Password.</value>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Client.IReadableConfiguration.OperationServers">
            <summary>
            Get the servers associated with the operation.
            </summary>
            <value>Operation servers.</value>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Client.IReadableConfiguration.GetApiKeyWithPrefix(System.String)">
            <summary>
            Gets the API key with prefix.
            </summary>
            <param name="apiKeyIdentifier">API key identifier (authentication scheme).</param>
            <returns>API key with prefix.</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Client.IReadableConfiguration.GetOperationServerUrl(System.String,System.Int32)">
            <summary>
            Gets the Operation server url at the provided index.
            </summary>
            <param name="operation">Operation server name.</param>
            <param name="index">Index of the operation server settings.</param>
            <returns></returns>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Client.IReadableConfiguration.ClientCertificates">
            <summary>
            Gets certificate collection to be sent with requests.
            </summary>
            <value>X509 Certificate collection.</value>
        </member>
        <member name="T:Sleekflow.Apis.InternalIntegrationHub.Client.ISynchronousClient">
             <summary>
             Contract for Synchronous RESTful API interactions.
            
             This interface allows consumers to provide a custom API accessor client.
             </summary>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Client.ISynchronousClient.Get``1(System.String,Sleekflow.Apis.InternalIntegrationHub.Client.RequestOptions,Sleekflow.Apis.InternalIntegrationHub.Client.IReadableConfiguration)">
            <summary>
            Executes a blocking call to some <paramref name="path"/> using the GET http verb.
            </summary>
            <param name="path">The relative path to invoke.</param>
            <param name="options">The request parameters to pass along to the client.</param>
            <param name="configuration">Per-request configurable settings.</param>
            <typeparam name="T">The return type.</typeparam>
            <returns>The response data, decorated with <see cref="T:Sleekflow.Apis.InternalIntegrationHub.Client.ApiResponse`1"/></returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Client.ISynchronousClient.Post``1(System.String,Sleekflow.Apis.InternalIntegrationHub.Client.RequestOptions,Sleekflow.Apis.InternalIntegrationHub.Client.IReadableConfiguration)">
            <summary>
            Executes a blocking call to some <paramref name="path"/> using the POST http verb.
            </summary>
            <param name="path">The relative path to invoke.</param>
            <param name="options">The request parameters to pass along to the client.</param>
            <param name="configuration">Per-request configurable settings.</param>
            <typeparam name="T">The return type.</typeparam>
            <returns>The response data, decorated with <see cref="T:Sleekflow.Apis.InternalIntegrationHub.Client.ApiResponse`1"/></returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Client.ISynchronousClient.Put``1(System.String,Sleekflow.Apis.InternalIntegrationHub.Client.RequestOptions,Sleekflow.Apis.InternalIntegrationHub.Client.IReadableConfiguration)">
            <summary>
            Executes a blocking call to some <paramref name="path"/> using the PUT http verb.
            </summary>
            <param name="path">The relative path to invoke.</param>
            <param name="options">The request parameters to pass along to the client.</param>
            <param name="configuration">Per-request configurable settings.</param>
            <typeparam name="T">The return type.</typeparam>
            <returns>The response data, decorated with <see cref="T:Sleekflow.Apis.InternalIntegrationHub.Client.ApiResponse`1"/></returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Client.ISynchronousClient.Delete``1(System.String,Sleekflow.Apis.InternalIntegrationHub.Client.RequestOptions,Sleekflow.Apis.InternalIntegrationHub.Client.IReadableConfiguration)">
            <summary>
            Executes a blocking call to some <paramref name="path"/> using the DELETE http verb.
            </summary>
            <param name="path">The relative path to invoke.</param>
            <param name="options">The request parameters to pass along to the client.</param>
            <param name="configuration">Per-request configurable settings.</param>
            <typeparam name="T">The return type.</typeparam>
            <returns>The response data, decorated with <see cref="T:Sleekflow.Apis.InternalIntegrationHub.Client.ApiResponse`1"/></returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Client.ISynchronousClient.Head``1(System.String,Sleekflow.Apis.InternalIntegrationHub.Client.RequestOptions,Sleekflow.Apis.InternalIntegrationHub.Client.IReadableConfiguration)">
            <summary>
            Executes a blocking call to some <paramref name="path"/> using the HEAD http verb.
            </summary>
            <param name="path">The relative path to invoke.</param>
            <param name="options">The request parameters to pass along to the client.</param>
            <param name="configuration">Per-request configurable settings.</param>
            <typeparam name="T">The return type.</typeparam>
            <returns>The response data, decorated with <see cref="T:Sleekflow.Apis.InternalIntegrationHub.Client.ApiResponse`1"/></returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Client.ISynchronousClient.Options``1(System.String,Sleekflow.Apis.InternalIntegrationHub.Client.RequestOptions,Sleekflow.Apis.InternalIntegrationHub.Client.IReadableConfiguration)">
            <summary>
            Executes a blocking call to some <paramref name="path"/> using the OPTIONS http verb.
            </summary>
            <param name="path">The relative path to invoke.</param>
            <param name="options">The request parameters to pass along to the client.</param>
            <param name="configuration">Per-request configurable settings.</param>
            <typeparam name="T">The return type.</typeparam>
            <returns>The response data, decorated with <see cref="T:Sleekflow.Apis.InternalIntegrationHub.Client.ApiResponse`1"/></returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Client.ISynchronousClient.Patch``1(System.String,Sleekflow.Apis.InternalIntegrationHub.Client.RequestOptions,Sleekflow.Apis.InternalIntegrationHub.Client.IReadableConfiguration)">
            <summary>
            Executes a blocking call to some <paramref name="path"/> using the PATCH http verb.
            </summary>
            <param name="path">The relative path to invoke.</param>
            <param name="options">The request parameters to pass along to the client.</param>
            <param name="configuration">Per-request configurable settings.</param>
            <typeparam name="T">The return type.</typeparam>
            <returns>The response data, decorated with <see cref="T:Sleekflow.Apis.InternalIntegrationHub.Client.ApiResponse`1"/></returns>
        </member>
        <member name="T:Sleekflow.Apis.InternalIntegrationHub.Client.Multimap`2">
            <summary>
            A dictionary in which one key has many associated values.
            </summary>
            <typeparam name="TKey">The type of the key</typeparam>
            <typeparam name="TValue">The type of the value associated with the key.</typeparam>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Client.Multimap`2.#ctor">
            <summary>
            Empty Constructor.
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Client.Multimap`2.#ctor(System.Collections.Generic.IEqualityComparer{`0})">
            <summary>
            Constructor with comparer.
            </summary>
            <param name="comparer"></param>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Client.Multimap`2.GetEnumerator">
            <summary>
            To get the enumerator.
            </summary>
            <returns>Enumerator</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Client.Multimap`2.System#Collections#IEnumerable#GetEnumerator">
            <summary>
            To get the enumerator.
            </summary>
            <returns>Enumerator</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Client.Multimap`2.Add(System.Collections.Generic.KeyValuePair{`0,System.Collections.Generic.IList{`1}})">
            <summary>
            Add values to Multimap
            </summary>
            <param name="item">Key value pair</param>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Client.Multimap`2.Add(Sleekflow.Apis.InternalIntegrationHub.Client.Multimap{`0,`1})">
            <summary>
            Add Multimap to Multimap
            </summary>
            <param name="multimap">Multimap</param>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Client.Multimap`2.Clear">
            <summary>
            Clear Multimap
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Client.Multimap`2.Contains(System.Collections.Generic.KeyValuePair{`0,System.Collections.Generic.IList{`1}})">
            <summary>
            Determines whether Multimap contains the specified item.
            </summary>
            <param name="item">Key value pair</param>
            <exception cref="T:System.NotImplementedException">Method needs to be implemented</exception>
            <returns>true if the Multimap contains the item; otherwise, false.</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Client.Multimap`2.CopyTo(System.Collections.Generic.KeyValuePair{`0,System.Collections.Generic.IList{`1}}[],System.Int32)">
            <summary>
             Copy items of the Multimap to an array,
                starting at a particular array index.
            </summary>
            <param name="array">The array that is the destination of the items copied
                from Multimap. The array must have zero-based indexing.</param>
            <param name="arrayIndex">The zero-based index in array at which copying begins.</param>
            <exception cref="T:System.NotImplementedException">Method needs to be implemented</exception>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Client.Multimap`2.Remove(System.Collections.Generic.KeyValuePair{`0,System.Collections.Generic.IList{`1}})">
            <summary>
            Removes the specified item from the Multimap.
            </summary>
            <param name="item">Key value pair</param>
            <returns>true if the item is successfully removed; otherwise, false.</returns>
            <exception cref="T:System.NotImplementedException">Method needs to be implemented</exception>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Client.Multimap`2.Count">
            <summary>
            Gets the number of items contained in the Multimap.
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Client.Multimap`2.IsReadOnly">
            <summary>
            Gets a value indicating whether the Multimap is read-only.
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Client.Multimap`2.Add(`0,System.Collections.Generic.IList{`1})">
            <summary>
            Adds an item with the provided key and value to the Multimap.
            </summary>
            <param name="key">The object to use as the key of the item to add.</param>
            <param name="value">The object to use as the value of the item to add.</param>
            <exception cref="T:System.InvalidOperationException">Thrown when couldn't add the value to Multimap.</exception>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Client.Multimap`2.ContainsKey(`0)">
            <summary>
            Determines whether the Multimap contains an item with the specified key.
            </summary>
            <param name="key">The key to locate in the Multimap.</param>
            <returns>true if the Multimap contains an item with
                the key; otherwise, false.</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Client.Multimap`2.Remove(`0)">
            <summary>
            Removes item with the specified key from the Multimap.
            </summary>
            <param name="key">The key to locate in the Multimap.</param>
            <returns>true if the item is successfully removed; otherwise, false.</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Client.Multimap`2.TryGetValue(`0,System.Collections.Generic.IList{`1}@)">
            <summary>
            Gets the value associated with the specified key.
            </summary>
            <param name="key">The key whose value to get.</param>
            <param name="value">When this method returns, the value associated with the specified key, if the
                key is found; otherwise, the default value for the type of the value parameter.
                This parameter is passed uninitialized.</param>
            <returns> true if the object that implements Multimap contains
                an item with the specified key; otherwise, false.</returns>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Client.Multimap`2.Item(`0)">
            <summary>
            Gets or sets the item with the specified key.
            </summary>
            <param name="key">The key of the item to get or set.</param>
            <returns>The value of the specified key.</returns>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Client.Multimap`2.Keys">
            <summary>
            Gets a System.Collections.Generic.ICollection containing the keys of the Multimap.
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Client.Multimap`2.Values">
            <summary>
            Gets a System.Collections.Generic.ICollection containing the values of the Multimap.
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Client.Multimap`2.CopyTo(System.Array,System.Int32)">
            <summary>
             Copy the items of the Multimap to an System.Array,
                starting at a particular System.Array index.
            </summary>
            <param name="array">The one-dimensional System.Array that is the destination of the items copied
                from Multimap. The System.Array must have zero-based indexing.</param>
            <param name="index">The zero-based index in array at which copying begins.</param>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Client.Multimap`2.Add(`0,`1)">
            <summary>
            Adds an item with the provided key and value to the Multimap.
            </summary>
            <param name="key">The object to use as the key of the item to add.</param>
            <param name="value">The object to use as the value of the item to add.</param>
            <exception cref="T:System.InvalidOperationException">Thrown when couldn't add value to Multimap.</exception>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Client.Multimap`2.TryRemove(`0,System.Collections.Generic.IList{`1}@)">
            Helper method to encapsulate generator differences between dictionary types.
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Client.Multimap`2.TryAdd(`0,System.Collections.Generic.IList{`1})">
            Helper method to encapsulate generator differences between dictionary types.
        </member>
        <member name="T:Sleekflow.Apis.InternalIntegrationHub.Client.OpenAPIDateConverter">
            <summary>
            Formatter for 'date' openapi formats ss defined by full-date - RFC3339
            see https://github.com/OAI/OpenAPI-Specification/blob/master/versions/3.0.0.md#data-types
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Client.OpenAPIDateConverter.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.InternalIntegrationHub.Client.OpenAPIDateConverter" /> class.
            </summary>
        </member>
        <member name="T:Sleekflow.Apis.InternalIntegrationHub.Client.RequestOptions">
            <summary>
            A container for generalized request inputs. This type allows consumers to extend the request functionality
            by abstracting away from the default (built-in) request framework (e.g. RestSharp).
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Client.RequestOptions.PathParameters">
            <summary>
            Parameters to be bound to path parts of the Request's URL
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Client.RequestOptions.QueryParameters">
            <summary>
            Query parameters to be applied to the request.
            Keys may have 1 or more values associated.
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Client.RequestOptions.HeaderParameters">
            <summary>
            Header parameters to be applied to to the request.
            Keys may have 1 or more values associated.
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Client.RequestOptions.FormParameters">
            <summary>
            Form parameters to be sent along with the request.
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Client.RequestOptions.FileParameters">
            <summary>
            File parameters to be sent along with the request.
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Client.RequestOptions.Cookies">
            <summary>
            Cookies to be sent along with the request.
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Client.RequestOptions.Data">
            <summary>
            Any data associated with a request body.
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Client.RequestOptions.#ctor">
            <summary>
            Constructs a new instance of <see cref="T:Sleekflow.Apis.InternalIntegrationHub.Client.RequestOptions"/>
            </summary>
        </member>
        <member name="T:Sleekflow.Apis.InternalIntegrationHub.Client.RetryConfiguration">
            <summary>
            Configuration class to set the polly retry policies to be applied to the requests.
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Client.RetryConfiguration.RetryPolicy">
            <summary>
            Retry policy
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Client.RetryConfiguration.AsyncRetryPolicy">
            <summary>
            Async retry policy
            </summary>
        </member>
        <member name="T:Sleekflow.Apis.InternalIntegrationHub.Client.WebRequestPathBuilder">
            <summary>
            A URI builder
            </summary>
        </member>
        <member name="T:Sleekflow.Apis.InternalIntegrationHub.Model.AbstractOpenAPISchema">
            <summary>
             Abstract base class for oneOf, anyOf schemas in the OpenAPI specification
            </summary>
        </member>
        <member name="F:Sleekflow.Apis.InternalIntegrationHub.Model.AbstractOpenAPISchema.SerializerSettings">
            <summary>
             Custom JSON serializer
            </summary>
        </member>
        <member name="F:Sleekflow.Apis.InternalIntegrationHub.Model.AbstractOpenAPISchema.AdditionalPropertiesSerializerSettings">
            <summary>
             Custom JSON serializer for objects with additional properties
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Model.AbstractOpenAPISchema.ActualInstance">
            <summary>
            Gets or Sets the actual instance
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Model.AbstractOpenAPISchema.IsNullable">
            <summary>
            Gets or Sets IsNullable to indicate whether the instance is nullable
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Model.AbstractOpenAPISchema.SchemaType">
            <summary>
            Gets or Sets the schema type, which can be either `oneOf` or `anyOf`
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.AbstractOpenAPISchema.ToJson">
            <summary>
            Converts the instance into JSON string.
            </summary>
        </member>
        <member name="T:Sleekflow.Apis.InternalIntegrationHub.Model.BillItem">
            <summary>
            BillItem
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.BillItem.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.InternalIntegrationHub.Model.BillItem" /> class.
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.BillItem.#ctor(System.String,System.Decimal)">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.InternalIntegrationHub.Model.BillItem" /> class.
            </summary>
            <param name="billRecordId">billRecordId (required).</param>
            <param name="amount">amount (required).</param>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Model.BillItem.BillRecordId">
            <summary>
            Gets or Sets BillRecordId
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Model.BillItem.Amount">
            <summary>
            Gets or Sets Amount
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.BillItem.ToString">
            <summary>
            Returns the string presentation of the object
            </summary>
            <returns>String presentation of the object</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.BillItem.ToJson">
            <summary>
            Returns the JSON string presentation of the object
            </summary>
            <returns>JSON string presentation of the object</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.BillItem.Equals(System.Object)">
            <summary>
            Returns true if objects are equal
            </summary>
            <param name="input">Object to be compared</param>
            <returns>Boolean</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.BillItem.Equals(Sleekflow.Apis.InternalIntegrationHub.Model.BillItem)">
            <summary>
            Returns true if BillItem instances are equal
            </summary>
            <param name="input">Instance of BillItem to be compared</param>
            <returns>Boolean</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.BillItem.GetHashCode">
            <summary>
            Gets the hash code
            </summary>
            <returns>Hash code</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.BillItem.Validate(System.ComponentModel.DataAnnotations.ValidationContext)">
            <summary>
            To validate all properties of the instance
            </summary>
            <param name="validationContext">Validation context</param>
            <returns>Validation Result</returns>
        </member>
        <member name="T:Sleekflow.Apis.InternalIntegrationHub.Model.CreateCustomerInput">
            <summary>
            CreateCustomerInput
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.CreateCustomerInput.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.InternalIntegrationHub.Model.CreateCustomerInput" /> class.
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.CreateCustomerInput.#ctor(System.String,System.String,System.String,System.String,System.String,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.InternalIntegrationHub.Model.CreateCustomerInput" /> class.
            </summary>
            <param name="companyId">companyId (required).</param>
            <param name="companyName">companyName (required).</param>
            <param name="companyCountry">companyCountry (required).</param>
            <param name="companyOwnerEmail">companyOwnerEmail.</param>
            <param name="companyOwnerPhone">companyOwnerPhone.</param>
            <param name="salesRepEmail">salesRepEmail.</param>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Model.CreateCustomerInput.CompanyId">
            <summary>
            Gets or Sets CompanyId
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Model.CreateCustomerInput.CompanyName">
            <summary>
            Gets or Sets CompanyName
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Model.CreateCustomerInput.CompanyCountry">
            <summary>
            Gets or Sets CompanyCountry
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Model.CreateCustomerInput.CompanyOwnerEmail">
            <summary>
            Gets or Sets CompanyOwnerEmail
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Model.CreateCustomerInput.CompanyOwnerPhone">
            <summary>
            Gets or Sets CompanyOwnerPhone
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Model.CreateCustomerInput.SalesRepEmail">
            <summary>
            Gets or Sets SalesRepEmail
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.CreateCustomerInput.ToString">
            <summary>
            Returns the string presentation of the object
            </summary>
            <returns>String presentation of the object</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.CreateCustomerInput.ToJson">
            <summary>
            Returns the JSON string presentation of the object
            </summary>
            <returns>JSON string presentation of the object</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.CreateCustomerInput.Equals(System.Object)">
            <summary>
            Returns true if objects are equal
            </summary>
            <param name="input">Object to be compared</param>
            <returns>Boolean</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.CreateCustomerInput.Equals(Sleekflow.Apis.InternalIntegrationHub.Model.CreateCustomerInput)">
            <summary>
            Returns true if CreateCustomerInput instances are equal
            </summary>
            <param name="input">Instance of CreateCustomerInput to be compared</param>
            <returns>Boolean</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.CreateCustomerInput.GetHashCode">
            <summary>
            Gets the hash code
            </summary>
            <returns>Hash code</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.CreateCustomerInput.Validate(System.ComponentModel.DataAnnotations.ValidationContext)">
            <summary>
            To validate all properties of the instance
            </summary>
            <param name="validationContext">Validation context</param>
            <returns>Validation Result</returns>
        </member>
        <member name="T:Sleekflow.Apis.InternalIntegrationHub.Model.CreateCustomerOutputOutput">
            <summary>
            CreateCustomerOutputOutput
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.CreateCustomerOutputOutput.#ctor(System.Boolean,System.Object,System.String,System.DateTimeOffset,System.Int32,System.Nullable{System.Int32},System.Collections.Generic.Dictionary{System.String,System.Object},System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.InternalIntegrationHub.Model.CreateCustomerOutputOutput" /> class.
            </summary>
            <param name="success">success.</param>
            <param name="data">data.</param>
            <param name="message">message.</param>
            <param name="dateTime">dateTime.</param>
            <param name="httpStatusCode">httpStatusCode.</param>
            <param name="errorCode">errorCode.</param>
            <param name="errorContext">errorContext.</param>
            <param name="requestId">requestId.</param>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Model.CreateCustomerOutputOutput.Success">
            <summary>
            Gets or Sets Success
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Model.CreateCustomerOutputOutput.Data">
            <summary>
            Gets or Sets Data
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Model.CreateCustomerOutputOutput.Message">
            <summary>
            Gets or Sets Message
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Model.CreateCustomerOutputOutput.DateTime">
            <summary>
            Gets or Sets DateTime
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Model.CreateCustomerOutputOutput.HttpStatusCode">
            <summary>
            Gets or Sets HttpStatusCode
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Model.CreateCustomerOutputOutput.ErrorCode">
            <summary>
            Gets or Sets ErrorCode
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Model.CreateCustomerOutputOutput.ErrorContext">
            <summary>
            Gets or Sets ErrorContext
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Model.CreateCustomerOutputOutput.RequestId">
            <summary>
            Gets or Sets RequestId
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.CreateCustomerOutputOutput.ToString">
            <summary>
            Returns the string presentation of the object
            </summary>
            <returns>String presentation of the object</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.CreateCustomerOutputOutput.ToJson">
            <summary>
            Returns the JSON string presentation of the object
            </summary>
            <returns>JSON string presentation of the object</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.CreateCustomerOutputOutput.Equals(System.Object)">
            <summary>
            Returns true if objects are equal
            </summary>
            <param name="input">Object to be compared</param>
            <returns>Boolean</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.CreateCustomerOutputOutput.Equals(Sleekflow.Apis.InternalIntegrationHub.Model.CreateCustomerOutputOutput)">
            <summary>
            Returns true if CreateCustomerOutputOutput instances are equal
            </summary>
            <param name="input">Instance of CreateCustomerOutputOutput to be compared</param>
            <returns>Boolean</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.CreateCustomerOutputOutput.GetHashCode">
            <summary>
            Gets the hash code
            </summary>
            <returns>Hash code</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.CreateCustomerOutputOutput.Validate(System.ComponentModel.DataAnnotations.ValidationContext)">
            <summary>
            To validate all properties of the instance
            </summary>
            <param name="validationContext">Validation context</param>
            <returns>Validation Result</returns>
        </member>
        <member name="T:Sleekflow.Apis.InternalIntegrationHub.Model.CreateEmployeeManuallyInput">
            <summary>
            CreateEmployeeManuallyInput
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.CreateEmployeeManuallyInput.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.InternalIntegrationHub.Model.CreateEmployeeManuallyInput" /> class.
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.CreateEmployeeManuallyInput.#ctor(System.Collections.Generic.List{Sleekflow.Apis.InternalIntegrationHub.Model.CreateEmployeeRequest})">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.InternalIntegrationHub.Model.CreateEmployeeManuallyInput" /> class.
            </summary>
            <param name="employees">employees (required).</param>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Model.CreateEmployeeManuallyInput.Employees">
            <summary>
            Gets or Sets Employees
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.CreateEmployeeManuallyInput.ToString">
            <summary>
            Returns the string presentation of the object
            </summary>
            <returns>String presentation of the object</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.CreateEmployeeManuallyInput.ToJson">
            <summary>
            Returns the JSON string presentation of the object
            </summary>
            <returns>JSON string presentation of the object</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.CreateEmployeeManuallyInput.Equals(System.Object)">
            <summary>
            Returns true if objects are equal
            </summary>
            <param name="input">Object to be compared</param>
            <returns>Boolean</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.CreateEmployeeManuallyInput.Equals(Sleekflow.Apis.InternalIntegrationHub.Model.CreateEmployeeManuallyInput)">
            <summary>
            Returns true if CreateEmployeeManuallyInput instances are equal
            </summary>
            <param name="input">Instance of CreateEmployeeManuallyInput to be compared</param>
            <returns>Boolean</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.CreateEmployeeManuallyInput.GetHashCode">
            <summary>
            Gets the hash code
            </summary>
            <returns>Hash code</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.CreateEmployeeManuallyInput.Validate(System.ComponentModel.DataAnnotations.ValidationContext)">
            <summary>
            To validate all properties of the instance
            </summary>
            <param name="validationContext">Validation context</param>
            <returns>Validation Result</returns>
        </member>
        <member name="T:Sleekflow.Apis.InternalIntegrationHub.Model.CreateEmployeeManuallyOutput">
            <summary>
            CreateEmployeeManuallyOutput
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.CreateEmployeeManuallyOutput.#ctor(System.Int32,System.Collections.Generic.List{System.String})">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.InternalIntegrationHub.Model.CreateEmployeeManuallyOutput" /> class.
            </summary>
            <param name="successCount">successCount.</param>
            <param name="failedEmployeeIds">failedEmployeeIds.</param>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Model.CreateEmployeeManuallyOutput.SuccessCount">
            <summary>
            Gets or Sets SuccessCount
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Model.CreateEmployeeManuallyOutput.FailedEmployeeIds">
            <summary>
            Gets or Sets FailedEmployeeIds
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.CreateEmployeeManuallyOutput.ToString">
            <summary>
            Returns the string presentation of the object
            </summary>
            <returns>String presentation of the object</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.CreateEmployeeManuallyOutput.ToJson">
            <summary>
            Returns the JSON string presentation of the object
            </summary>
            <returns>JSON string presentation of the object</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.CreateEmployeeManuallyOutput.Equals(System.Object)">
            <summary>
            Returns true if objects are equal
            </summary>
            <param name="input">Object to be compared</param>
            <returns>Boolean</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.CreateEmployeeManuallyOutput.Equals(Sleekflow.Apis.InternalIntegrationHub.Model.CreateEmployeeManuallyOutput)">
            <summary>
            Returns true if CreateEmployeeManuallyOutput instances are equal
            </summary>
            <param name="input">Instance of CreateEmployeeManuallyOutput to be compared</param>
            <returns>Boolean</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.CreateEmployeeManuallyOutput.GetHashCode">
            <summary>
            Gets the hash code
            </summary>
            <returns>Hash code</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.CreateEmployeeManuallyOutput.Validate(System.ComponentModel.DataAnnotations.ValidationContext)">
            <summary>
            To validate all properties of the instance
            </summary>
            <param name="validationContext">Validation context</param>
            <returns>Validation Result</returns>
        </member>
        <member name="T:Sleekflow.Apis.InternalIntegrationHub.Model.CreateEmployeeManuallyOutputOutput">
            <summary>
            CreateEmployeeManuallyOutputOutput
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.CreateEmployeeManuallyOutputOutput.#ctor(System.Boolean,Sleekflow.Apis.InternalIntegrationHub.Model.CreateEmployeeManuallyOutput,System.String,System.DateTimeOffset,System.Int32,System.Nullable{System.Int32},System.Collections.Generic.Dictionary{System.String,System.Object},System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.InternalIntegrationHub.Model.CreateEmployeeManuallyOutputOutput" /> class.
            </summary>
            <param name="success">success.</param>
            <param name="data">data.</param>
            <param name="message">message.</param>
            <param name="dateTime">dateTime.</param>
            <param name="httpStatusCode">httpStatusCode.</param>
            <param name="errorCode">errorCode.</param>
            <param name="errorContext">errorContext.</param>
            <param name="requestId">requestId.</param>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Model.CreateEmployeeManuallyOutputOutput.Success">
            <summary>
            Gets or Sets Success
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Model.CreateEmployeeManuallyOutputOutput.Data">
            <summary>
            Gets or Sets Data
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Model.CreateEmployeeManuallyOutputOutput.Message">
            <summary>
            Gets or Sets Message
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Model.CreateEmployeeManuallyOutputOutput.DateTime">
            <summary>
            Gets or Sets DateTime
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Model.CreateEmployeeManuallyOutputOutput.HttpStatusCode">
            <summary>
            Gets or Sets HttpStatusCode
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Model.CreateEmployeeManuallyOutputOutput.ErrorCode">
            <summary>
            Gets or Sets ErrorCode
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Model.CreateEmployeeManuallyOutputOutput.ErrorContext">
            <summary>
            Gets or Sets ErrorContext
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Model.CreateEmployeeManuallyOutputOutput.RequestId">
            <summary>
            Gets or Sets RequestId
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.CreateEmployeeManuallyOutputOutput.ToString">
            <summary>
            Returns the string presentation of the object
            </summary>
            <returns>String presentation of the object</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.CreateEmployeeManuallyOutputOutput.ToJson">
            <summary>
            Returns the JSON string presentation of the object
            </summary>
            <returns>JSON string presentation of the object</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.CreateEmployeeManuallyOutputOutput.Equals(System.Object)">
            <summary>
            Returns true if objects are equal
            </summary>
            <param name="input">Object to be compared</param>
            <returns>Boolean</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.CreateEmployeeManuallyOutputOutput.Equals(Sleekflow.Apis.InternalIntegrationHub.Model.CreateEmployeeManuallyOutputOutput)">
            <summary>
            Returns true if CreateEmployeeManuallyOutputOutput instances are equal
            </summary>
            <param name="input">Instance of CreateEmployeeManuallyOutputOutput to be compared</param>
            <returns>Boolean</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.CreateEmployeeManuallyOutputOutput.GetHashCode">
            <summary>
            Gets the hash code
            </summary>
            <returns>Hash code</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.CreateEmployeeManuallyOutputOutput.Validate(System.ComponentModel.DataAnnotations.ValidationContext)">
            <summary>
            To validate all properties of the instance
            </summary>
            <param name="validationContext">Validation context</param>
            <returns>Validation Result</returns>
        </member>
        <member name="T:Sleekflow.Apis.InternalIntegrationHub.Model.CreateEmployeeRequest">
            <summary>
            CreateEmployeeRequest
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.CreateEmployeeRequest.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.InternalIntegrationHub.Model.CreateEmployeeRequest" /> class.
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.CreateEmployeeRequest.#ctor(System.String,System.String,System.String,System.String,System.String,System.Nullable{System.Boolean},Sleekflow.Apis.InternalIntegrationHub.Model.Supervisor,Sleekflow.Apis.InternalIntegrationHub.Model.DefaultExpenseReportCurrency,Sleekflow.Apis.InternalIntegrationHub.Model.Subsidiary)">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.InternalIntegrationHub.Model.CreateEmployeeRequest" /> class.
            </summary>
            <param name="id">id.</param>
            <param name="externalId">externalId.</param>
            <param name="firstName">firstName (required).</param>
            <param name="lastName">lastName (required).</param>
            <param name="email">email (required).</param>
            <param name="issalesrep">issalesrep.</param>
            <param name="supervisor">supervisor.</param>
            <param name="defaultExpenseReportCurrency">defaultExpenseReportCurrency (required).</param>
            <param name="subsidiary">subsidiary (required).</param>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Model.CreateEmployeeRequest.Id">
            <summary>
            Gets or Sets Id
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Model.CreateEmployeeRequest.ExternalId">
            <summary>
            Gets or Sets ExternalId
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Model.CreateEmployeeRequest.FirstName">
            <summary>
            Gets or Sets FirstName
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Model.CreateEmployeeRequest.LastName">
            <summary>
            Gets or Sets LastName
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Model.CreateEmployeeRequest.Email">
            <summary>
            Gets or Sets Email
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Model.CreateEmployeeRequest.Issalesrep">
            <summary>
            Gets or Sets Issalesrep
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Model.CreateEmployeeRequest.Supervisor">
            <summary>
            Gets or Sets Supervisor
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Model.CreateEmployeeRequest.DefaultExpenseReportCurrency">
            <summary>
            Gets or Sets DefaultExpenseReportCurrency
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Model.CreateEmployeeRequest.Subsidiary">
            <summary>
            Gets or Sets Subsidiary
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.CreateEmployeeRequest.ToString">
            <summary>
            Returns the string presentation of the object
            </summary>
            <returns>String presentation of the object</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.CreateEmployeeRequest.ToJson">
            <summary>
            Returns the JSON string presentation of the object
            </summary>
            <returns>JSON string presentation of the object</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.CreateEmployeeRequest.Equals(System.Object)">
            <summary>
            Returns true if objects are equal
            </summary>
            <param name="input">Object to be compared</param>
            <returns>Boolean</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.CreateEmployeeRequest.Equals(Sleekflow.Apis.InternalIntegrationHub.Model.CreateEmployeeRequest)">
            <summary>
            Returns true if CreateEmployeeRequest instances are equal
            </summary>
            <param name="input">Instance of CreateEmployeeRequest to be compared</param>
            <returns>Boolean</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.CreateEmployeeRequest.GetHashCode">
            <summary>
            Gets the hash code
            </summary>
            <returns>Hash code</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.CreateEmployeeRequest.Validate(System.ComponentModel.DataAnnotations.ValidationContext)">
            <summary>
            To validate all properties of the instance
            </summary>
            <param name="validationContext">Validation context</param>
            <returns>Validation Result</returns>
        </member>
        <member name="T:Sleekflow.Apis.InternalIntegrationHub.Model.CreateInvoiceInput">
            <summary>
            CreateInvoiceInput
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.CreateInvoiceInput.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.InternalIntegrationHub.Model.CreateInvoiceInput" /> class.
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.CreateInvoiceInput.#ctor(System.String,System.String,System.Decimal,System.Decimal,System.Decimal,System.Nullable{System.DateTimeOffset},System.Nullable{System.DateTimeOffset},System.Nullable{System.Int32},System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.InternalIntegrationHub.Model.CreateInvoiceInput" /> class.
            </summary>
            <param name="billRecordId">billRecordId (required).</param>
            <param name="companyId">companyId (required).</param>
            <param name="subscriptionFee">subscriptionFee (required).</param>
            <param name="oneTimeSetupFee">oneTimeSetupFee (required).</param>
            <param name="whatsappCreditAmount">whatsappCreditAmount (required).</param>
            <param name="subscriptionStartDate">subscriptionStartDate.</param>
            <param name="subscriptionEndDate">subscriptionEndDate.</param>
            <param name="paymentTerm">paymentTerm.</param>
            <param name="currency">currency (required).</param>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Model.CreateInvoiceInput.BillRecordId">
            <summary>
            Gets or Sets BillRecordId
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Model.CreateInvoiceInput.CompanyId">
            <summary>
            Gets or Sets CompanyId
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Model.CreateInvoiceInput.SubscriptionFee">
            <summary>
            Gets or Sets SubscriptionFee
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Model.CreateInvoiceInput.OneTimeSetupFee">
            <summary>
            Gets or Sets OneTimeSetupFee
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Model.CreateInvoiceInput.WhatsappCreditAmount">
            <summary>
            Gets or Sets WhatsappCreditAmount
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Model.CreateInvoiceInput.SubscriptionStartDate">
            <summary>
            Gets or Sets SubscriptionStartDate
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Model.CreateInvoiceInput.SubscriptionEndDate">
            <summary>
            Gets or Sets SubscriptionEndDate
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Model.CreateInvoiceInput.PaymentTerm">
            <summary>
            Gets or Sets PaymentTerm
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Model.CreateInvoiceInput.Currency">
            <summary>
            Gets or Sets Currency
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.CreateInvoiceInput.ToString">
            <summary>
            Returns the string presentation of the object
            </summary>
            <returns>String presentation of the object</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.CreateInvoiceInput.ToJson">
            <summary>
            Returns the JSON string presentation of the object
            </summary>
            <returns>JSON string presentation of the object</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.CreateInvoiceInput.Equals(System.Object)">
            <summary>
            Returns true if objects are equal
            </summary>
            <param name="input">Object to be compared</param>
            <returns>Boolean</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.CreateInvoiceInput.Equals(Sleekflow.Apis.InternalIntegrationHub.Model.CreateInvoiceInput)">
            <summary>
            Returns true if CreateInvoiceInput instances are equal
            </summary>
            <param name="input">Instance of CreateInvoiceInput to be compared</param>
            <returns>Boolean</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.CreateInvoiceInput.GetHashCode">
            <summary>
            Gets the hash code
            </summary>
            <returns>Hash code</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.CreateInvoiceInput.Validate(System.ComponentModel.DataAnnotations.ValidationContext)">
            <summary>
            To validate all properties of the instance
            </summary>
            <param name="validationContext">Validation context</param>
            <returns>Validation Result</returns>
        </member>
        <member name="T:Sleekflow.Apis.InternalIntegrationHub.Model.CreateInvoiceOutputOutput">
            <summary>
            CreateInvoiceOutputOutput
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.CreateInvoiceOutputOutput.#ctor(System.Boolean,System.Object,System.String,System.DateTimeOffset,System.Int32,System.Nullable{System.Int32},System.Collections.Generic.Dictionary{System.String,System.Object},System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.InternalIntegrationHub.Model.CreateInvoiceOutputOutput" /> class.
            </summary>
            <param name="success">success.</param>
            <param name="data">data.</param>
            <param name="message">message.</param>
            <param name="dateTime">dateTime.</param>
            <param name="httpStatusCode">httpStatusCode.</param>
            <param name="errorCode">errorCode.</param>
            <param name="errorContext">errorContext.</param>
            <param name="requestId">requestId.</param>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Model.CreateInvoiceOutputOutput.Success">
            <summary>
            Gets or Sets Success
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Model.CreateInvoiceOutputOutput.Data">
            <summary>
            Gets or Sets Data
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Model.CreateInvoiceOutputOutput.Message">
            <summary>
            Gets or Sets Message
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Model.CreateInvoiceOutputOutput.DateTime">
            <summary>
            Gets or Sets DateTime
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Model.CreateInvoiceOutputOutput.HttpStatusCode">
            <summary>
            Gets or Sets HttpStatusCode
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Model.CreateInvoiceOutputOutput.ErrorCode">
            <summary>
            Gets or Sets ErrorCode
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Model.CreateInvoiceOutputOutput.ErrorContext">
            <summary>
            Gets or Sets ErrorContext
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Model.CreateInvoiceOutputOutput.RequestId">
            <summary>
            Gets or Sets RequestId
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.CreateInvoiceOutputOutput.ToString">
            <summary>
            Returns the string presentation of the object
            </summary>
            <returns>String presentation of the object</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.CreateInvoiceOutputOutput.ToJson">
            <summary>
            Returns the JSON string presentation of the object
            </summary>
            <returns>JSON string presentation of the object</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.CreateInvoiceOutputOutput.Equals(System.Object)">
            <summary>
            Returns true if objects are equal
            </summary>
            <param name="input">Object to be compared</param>
            <returns>Boolean</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.CreateInvoiceOutputOutput.Equals(Sleekflow.Apis.InternalIntegrationHub.Model.CreateInvoiceOutputOutput)">
            <summary>
            Returns true if CreateInvoiceOutputOutput instances are equal
            </summary>
            <param name="input">Instance of CreateInvoiceOutputOutput to be compared</param>
            <returns>Boolean</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.CreateInvoiceOutputOutput.GetHashCode">
            <summary>
            Gets the hash code
            </summary>
            <returns>Hash code</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.CreateInvoiceOutputOutput.Validate(System.ComponentModel.DataAnnotations.ValidationContext)">
            <summary>
            To validate all properties of the instance
            </summary>
            <param name="validationContext">Validation context</param>
            <returns>Validation Result</returns>
        </member>
        <member name="T:Sleekflow.Apis.InternalIntegrationHub.Model.CreatePaymentFromStripeInput">
            <summary>
            CreatePaymentFromStripeInput
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.CreatePaymentFromStripeInput.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.InternalIntegrationHub.Model.CreatePaymentFromStripeInput" /> class.
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.CreatePaymentFromStripeInput.#ctor(System.Collections.Generic.List{Sleekflow.Apis.InternalIntegrationHub.Model.BillItem},System.String,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.InternalIntegrationHub.Model.CreatePaymentFromStripeInput" /> class.
            </summary>
            <param name="items">items (required).</param>
            <param name="currency">currency (required).</param>
            <param name="companyId">companyId (required).</param>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Model.CreatePaymentFromStripeInput.Items">
            <summary>
            Gets or Sets Items
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Model.CreatePaymentFromStripeInput.Currency">
            <summary>
            Gets or Sets Currency
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Model.CreatePaymentFromStripeInput.CompanyId">
            <summary>
            Gets or Sets CompanyId
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.CreatePaymentFromStripeInput.ToString">
            <summary>
            Returns the string presentation of the object
            </summary>
            <returns>String presentation of the object</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.CreatePaymentFromStripeInput.ToJson">
            <summary>
            Returns the JSON string presentation of the object
            </summary>
            <returns>JSON string presentation of the object</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.CreatePaymentFromStripeInput.Equals(System.Object)">
            <summary>
            Returns true if objects are equal
            </summary>
            <param name="input">Object to be compared</param>
            <returns>Boolean</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.CreatePaymentFromStripeInput.Equals(Sleekflow.Apis.InternalIntegrationHub.Model.CreatePaymentFromStripeInput)">
            <summary>
            Returns true if CreatePaymentFromStripeInput instances are equal
            </summary>
            <param name="input">Instance of CreatePaymentFromStripeInput to be compared</param>
            <returns>Boolean</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.CreatePaymentFromStripeInput.GetHashCode">
            <summary>
            Gets the hash code
            </summary>
            <returns>Hash code</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.CreatePaymentFromStripeInput.Validate(System.ComponentModel.DataAnnotations.ValidationContext)">
            <summary>
            To validate all properties of the instance
            </summary>
            <param name="validationContext">Validation context</param>
            <returns>Validation Result</returns>
        </member>
        <member name="T:Sleekflow.Apis.InternalIntegrationHub.Model.CreatePaymentFromStripeOutputOutput">
            <summary>
            CreatePaymentFromStripeOutputOutput
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.CreatePaymentFromStripeOutputOutput.#ctor(System.Boolean,System.Object,System.String,System.DateTimeOffset,System.Int32,System.Nullable{System.Int32},System.Collections.Generic.Dictionary{System.String,System.Object},System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.InternalIntegrationHub.Model.CreatePaymentFromStripeOutputOutput" /> class.
            </summary>
            <param name="success">success.</param>
            <param name="data">data.</param>
            <param name="message">message.</param>
            <param name="dateTime">dateTime.</param>
            <param name="httpStatusCode">httpStatusCode.</param>
            <param name="errorCode">errorCode.</param>
            <param name="errorContext">errorContext.</param>
            <param name="requestId">requestId.</param>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Model.CreatePaymentFromStripeOutputOutput.Success">
            <summary>
            Gets or Sets Success
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Model.CreatePaymentFromStripeOutputOutput.Data">
            <summary>
            Gets or Sets Data
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Model.CreatePaymentFromStripeOutputOutput.Message">
            <summary>
            Gets or Sets Message
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Model.CreatePaymentFromStripeOutputOutput.DateTime">
            <summary>
            Gets or Sets DateTime
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Model.CreatePaymentFromStripeOutputOutput.HttpStatusCode">
            <summary>
            Gets or Sets HttpStatusCode
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Model.CreatePaymentFromStripeOutputOutput.ErrorCode">
            <summary>
            Gets or Sets ErrorCode
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Model.CreatePaymentFromStripeOutputOutput.ErrorContext">
            <summary>
            Gets or Sets ErrorContext
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Model.CreatePaymentFromStripeOutputOutput.RequestId">
            <summary>
            Gets or Sets RequestId
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.CreatePaymentFromStripeOutputOutput.ToString">
            <summary>
            Returns the string presentation of the object
            </summary>
            <returns>String presentation of the object</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.CreatePaymentFromStripeOutputOutput.ToJson">
            <summary>
            Returns the JSON string presentation of the object
            </summary>
            <returns>JSON string presentation of the object</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.CreatePaymentFromStripeOutputOutput.Equals(System.Object)">
            <summary>
            Returns true if objects are equal
            </summary>
            <param name="input">Object to be compared</param>
            <returns>Boolean</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.CreatePaymentFromStripeOutputOutput.Equals(Sleekflow.Apis.InternalIntegrationHub.Model.CreatePaymentFromStripeOutputOutput)">
            <summary>
            Returns true if CreatePaymentFromStripeOutputOutput instances are equal
            </summary>
            <param name="input">Instance of CreatePaymentFromStripeOutputOutput to be compared</param>
            <returns>Boolean</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.CreatePaymentFromStripeOutputOutput.GetHashCode">
            <summary>
            Gets the hash code
            </summary>
            <returns>Hash code</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.CreatePaymentFromStripeOutputOutput.Validate(System.ComponentModel.DataAnnotations.ValidationContext)">
            <summary>
            To validate all properties of the instance
            </summary>
            <param name="validationContext">Validation context</param>
            <returns>Validation Result</returns>
        </member>
        <member name="T:Sleekflow.Apis.InternalIntegrationHub.Model.CreatePaymentInput">
            <summary>
            CreatePaymentInput
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.CreatePaymentInput.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.InternalIntegrationHub.Model.CreatePaymentInput" /> class.
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.CreatePaymentInput.#ctor(System.Collections.Generic.List{Sleekflow.Apis.InternalIntegrationHub.Model.CreatePaymentRequest})">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.InternalIntegrationHub.Model.CreatePaymentInput" /> class.
            </summary>
            <param name="payments">payments (required).</param>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Model.CreatePaymentInput.Payments">
            <summary>
            Gets or Sets Payments
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.CreatePaymentInput.ToString">
            <summary>
            Returns the string presentation of the object
            </summary>
            <returns>String presentation of the object</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.CreatePaymentInput.ToJson">
            <summary>
            Returns the JSON string presentation of the object
            </summary>
            <returns>JSON string presentation of the object</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.CreatePaymentInput.Equals(System.Object)">
            <summary>
            Returns true if objects are equal
            </summary>
            <param name="input">Object to be compared</param>
            <returns>Boolean</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.CreatePaymentInput.Equals(Sleekflow.Apis.InternalIntegrationHub.Model.CreatePaymentInput)">
            <summary>
            Returns true if CreatePaymentInput instances are equal
            </summary>
            <param name="input">Instance of CreatePaymentInput to be compared</param>
            <returns>Boolean</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.CreatePaymentInput.GetHashCode">
            <summary>
            Gets the hash code
            </summary>
            <returns>Hash code</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.CreatePaymentInput.Validate(System.ComponentModel.DataAnnotations.ValidationContext)">
            <summary>
            To validate all properties of the instance
            </summary>
            <param name="validationContext">Validation context</param>
            <returns>Validation Result</returns>
        </member>
        <member name="T:Sleekflow.Apis.InternalIntegrationHub.Model.CreatePaymentOutputOutput">
            <summary>
            CreatePaymentOutputOutput
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.CreatePaymentOutputOutput.#ctor(System.Boolean,System.Object,System.String,System.DateTimeOffset,System.Int32,System.Nullable{System.Int32},System.Collections.Generic.Dictionary{System.String,System.Object},System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.InternalIntegrationHub.Model.CreatePaymentOutputOutput" /> class.
            </summary>
            <param name="success">success.</param>
            <param name="data">data.</param>
            <param name="message">message.</param>
            <param name="dateTime">dateTime.</param>
            <param name="httpStatusCode">httpStatusCode.</param>
            <param name="errorCode">errorCode.</param>
            <param name="errorContext">errorContext.</param>
            <param name="requestId">requestId.</param>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Model.CreatePaymentOutputOutput.Success">
            <summary>
            Gets or Sets Success
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Model.CreatePaymentOutputOutput.Data">
            <summary>
            Gets or Sets Data
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Model.CreatePaymentOutputOutput.Message">
            <summary>
            Gets or Sets Message
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Model.CreatePaymentOutputOutput.DateTime">
            <summary>
            Gets or Sets DateTime
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Model.CreatePaymentOutputOutput.HttpStatusCode">
            <summary>
            Gets or Sets HttpStatusCode
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Model.CreatePaymentOutputOutput.ErrorCode">
            <summary>
            Gets or Sets ErrorCode
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Model.CreatePaymentOutputOutput.ErrorContext">
            <summary>
            Gets or Sets ErrorContext
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Model.CreatePaymentOutputOutput.RequestId">
            <summary>
            Gets or Sets RequestId
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.CreatePaymentOutputOutput.ToString">
            <summary>
            Returns the string presentation of the object
            </summary>
            <returns>String presentation of the object</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.CreatePaymentOutputOutput.ToJson">
            <summary>
            Returns the JSON string presentation of the object
            </summary>
            <returns>JSON string presentation of the object</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.CreatePaymentOutputOutput.Equals(System.Object)">
            <summary>
            Returns true if objects are equal
            </summary>
            <param name="input">Object to be compared</param>
            <returns>Boolean</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.CreatePaymentOutputOutput.Equals(Sleekflow.Apis.InternalIntegrationHub.Model.CreatePaymentOutputOutput)">
            <summary>
            Returns true if CreatePaymentOutputOutput instances are equal
            </summary>
            <param name="input">Instance of CreatePaymentOutputOutput to be compared</param>
            <returns>Boolean</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.CreatePaymentOutputOutput.GetHashCode">
            <summary>
            Gets the hash code
            </summary>
            <returns>Hash code</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.CreatePaymentOutputOutput.Validate(System.ComponentModel.DataAnnotations.ValidationContext)">
            <summary>
            To validate all properties of the instance
            </summary>
            <param name="validationContext">Validation context</param>
            <returns>Validation Result</returns>
        </member>
        <member name="T:Sleekflow.Apis.InternalIntegrationHub.Model.CreatePaymentRequest">
            <summary>
            CreatePaymentRequest
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.CreatePaymentRequest.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.InternalIntegrationHub.Model.CreatePaymentRequest" /> class.
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.CreatePaymentRequest.#ctor(System.String,System.String,System.Decimal,System.Decimal,System.Decimal,System.String,System.DateTimeOffset,System.String,System.String,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.InternalIntegrationHub.Model.CreatePaymentRequest" /> class.
            </summary>
            <param name="customerId">customerId (required).</param>
            <param name="billRecordId">billRecordId (required).</param>
            <param name="subscriptionFee">subscriptionFee (required).</param>
            <param name="oneTimeSetupFee">oneTimeSetupFee (required).</param>
            <param name="whatsappCreditAmount">whatsappCreditAmount (required).</param>
            <param name="currency">currency (required).</param>
            <param name="paymentDate">paymentDate (required).</param>
            <param name="invoiceId">invoiceId (required).</param>
            <param name="paymentTerms">paymentTerms (required).</param>
            <param name="salesRepId">salesRepId (required).</param>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Model.CreatePaymentRequest.CustomerId">
            <summary>
            Gets or Sets CustomerId
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Model.CreatePaymentRequest.BillRecordId">
            <summary>
            Gets or Sets BillRecordId
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Model.CreatePaymentRequest.SubscriptionFee">
            <summary>
            Gets or Sets SubscriptionFee
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Model.CreatePaymentRequest.OneTimeSetupFee">
            <summary>
            Gets or Sets OneTimeSetupFee
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Model.CreatePaymentRequest.WhatsappCreditAmount">
            <summary>
            Gets or Sets WhatsappCreditAmount
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Model.CreatePaymentRequest.Currency">
            <summary>
            Gets or Sets Currency
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Model.CreatePaymentRequest.PaymentDate">
            <summary>
            Gets or Sets PaymentDate
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Model.CreatePaymentRequest.InvoiceId">
            <summary>
            Gets or Sets InvoiceId
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Model.CreatePaymentRequest.PaymentTerms">
            <summary>
            Gets or Sets PaymentTerms
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Model.CreatePaymentRequest.SalesRepId">
            <summary>
            Gets or Sets SalesRepId
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.CreatePaymentRequest.ToString">
            <summary>
            Returns the string presentation of the object
            </summary>
            <returns>String presentation of the object</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.CreatePaymentRequest.ToJson">
            <summary>
            Returns the JSON string presentation of the object
            </summary>
            <returns>JSON string presentation of the object</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.CreatePaymentRequest.Equals(System.Object)">
            <summary>
            Returns true if objects are equal
            </summary>
            <param name="input">Object to be compared</param>
            <returns>Boolean</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.CreatePaymentRequest.Equals(Sleekflow.Apis.InternalIntegrationHub.Model.CreatePaymentRequest)">
            <summary>
            Returns true if CreatePaymentRequest instances are equal
            </summary>
            <param name="input">Instance of CreatePaymentRequest to be compared</param>
            <returns>Boolean</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.CreatePaymentRequest.GetHashCode">
            <summary>
            Gets the hash code
            </summary>
            <returns>Hash code</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.CreatePaymentRequest.Validate(System.ComponentModel.DataAnnotations.ValidationContext)">
            <summary>
            To validate all properties of the instance
            </summary>
            <param name="validationContext">Validation context</param>
            <returns>Validation Result</returns>
        </member>
        <member name="T:Sleekflow.Apis.InternalIntegrationHub.Model.DefaultExpenseReportCurrency">
            <summary>
            DefaultExpenseReportCurrency
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.DefaultExpenseReportCurrency.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.InternalIntegrationHub.Model.DefaultExpenseReportCurrency" /> class.
            </summary>
            <param name="id">id.</param>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Model.DefaultExpenseReportCurrency.Id">
            <summary>
            Gets or Sets Id
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.DefaultExpenseReportCurrency.ToString">
            <summary>
            Returns the string presentation of the object
            </summary>
            <returns>String presentation of the object</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.DefaultExpenseReportCurrency.ToJson">
            <summary>
            Returns the JSON string presentation of the object
            </summary>
            <returns>JSON string presentation of the object</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.DefaultExpenseReportCurrency.Equals(System.Object)">
            <summary>
            Returns true if objects are equal
            </summary>
            <param name="input">Object to be compared</param>
            <returns>Boolean</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.DefaultExpenseReportCurrency.Equals(Sleekflow.Apis.InternalIntegrationHub.Model.DefaultExpenseReportCurrency)">
            <summary>
            Returns true if DefaultExpenseReportCurrency instances are equal
            </summary>
            <param name="input">Instance of DefaultExpenseReportCurrency to be compared</param>
            <returns>Boolean</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.DefaultExpenseReportCurrency.GetHashCode">
            <summary>
            Gets the hash code
            </summary>
            <returns>Hash code</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.DefaultExpenseReportCurrency.Validate(System.ComponentModel.DataAnnotations.ValidationContext)">
            <summary>
            To validate all properties of the instance
            </summary>
            <param name="validationContext">Validation context</param>
            <returns>Validation Result</returns>
        </member>
        <member name="T:Sleekflow.Apis.InternalIntegrationHub.Model.GetPaymentInput">
            <summary>
            GetPaymentInput
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.GetPaymentInput.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.InternalIntegrationHub.Model.GetPaymentInput" /> class.
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.GetPaymentInput.#ctor(System.String,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.InternalIntegrationHub.Model.GetPaymentInput" /> class.
            </summary>
            <param name="billRecordId">billRecordId (required).</param>
            <param name="customerId">customerId (required).</param>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Model.GetPaymentInput.BillRecordId">
            <summary>
            Gets or Sets BillRecordId
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Model.GetPaymentInput.CustomerId">
            <summary>
            Gets or Sets CustomerId
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.GetPaymentInput.ToString">
            <summary>
            Returns the string presentation of the object
            </summary>
            <returns>String presentation of the object</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.GetPaymentInput.ToJson">
            <summary>
            Returns the JSON string presentation of the object
            </summary>
            <returns>JSON string presentation of the object</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.GetPaymentInput.Equals(System.Object)">
            <summary>
            Returns true if objects are equal
            </summary>
            <param name="input">Object to be compared</param>
            <returns>Boolean</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.GetPaymentInput.Equals(Sleekflow.Apis.InternalIntegrationHub.Model.GetPaymentInput)">
            <summary>
            Returns true if GetPaymentInput instances are equal
            </summary>
            <param name="input">Instance of GetPaymentInput to be compared</param>
            <returns>Boolean</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.GetPaymentInput.GetHashCode">
            <summary>
            Gets the hash code
            </summary>
            <returns>Hash code</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.GetPaymentInput.Validate(System.ComponentModel.DataAnnotations.ValidationContext)">
            <summary>
            To validate all properties of the instance
            </summary>
            <param name="validationContext">Validation context</param>
            <returns>Validation Result</returns>
        </member>
        <member name="T:Sleekflow.Apis.InternalIntegrationHub.Model.GetPaymentOutput">
            <summary>
            GetPaymentOutput
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.GetPaymentOutput.#ctor(System.Collections.Generic.List{Sleekflow.Apis.InternalIntegrationHub.Model.GetPaymentResponse})">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.InternalIntegrationHub.Model.GetPaymentOutput" /> class.
            </summary>
            <param name="payments">payments.</param>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Model.GetPaymentOutput.Payments">
            <summary>
            Gets or Sets Payments
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.GetPaymentOutput.ToString">
            <summary>
            Returns the string presentation of the object
            </summary>
            <returns>String presentation of the object</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.GetPaymentOutput.ToJson">
            <summary>
            Returns the JSON string presentation of the object
            </summary>
            <returns>JSON string presentation of the object</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.GetPaymentOutput.Equals(System.Object)">
            <summary>
            Returns true if objects are equal
            </summary>
            <param name="input">Object to be compared</param>
            <returns>Boolean</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.GetPaymentOutput.Equals(Sleekflow.Apis.InternalIntegrationHub.Model.GetPaymentOutput)">
            <summary>
            Returns true if GetPaymentOutput instances are equal
            </summary>
            <param name="input">Instance of GetPaymentOutput to be compared</param>
            <returns>Boolean</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.GetPaymentOutput.GetHashCode">
            <summary>
            Gets the hash code
            </summary>
            <returns>Hash code</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.GetPaymentOutput.Validate(System.ComponentModel.DataAnnotations.ValidationContext)">
            <summary>
            To validate all properties of the instance
            </summary>
            <param name="validationContext">Validation context</param>
            <returns>Validation Result</returns>
        </member>
        <member name="T:Sleekflow.Apis.InternalIntegrationHub.Model.GetPaymentOutputOutput">
            <summary>
            GetPaymentOutputOutput
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.GetPaymentOutputOutput.#ctor(System.Boolean,Sleekflow.Apis.InternalIntegrationHub.Model.GetPaymentOutput,System.String,System.DateTimeOffset,System.Int32,System.Nullable{System.Int32},System.Collections.Generic.Dictionary{System.String,System.Object},System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.InternalIntegrationHub.Model.GetPaymentOutputOutput" /> class.
            </summary>
            <param name="success">success.</param>
            <param name="data">data.</param>
            <param name="message">message.</param>
            <param name="dateTime">dateTime.</param>
            <param name="httpStatusCode">httpStatusCode.</param>
            <param name="errorCode">errorCode.</param>
            <param name="errorContext">errorContext.</param>
            <param name="requestId">requestId.</param>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Model.GetPaymentOutputOutput.Success">
            <summary>
            Gets or Sets Success
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Model.GetPaymentOutputOutput.Data">
            <summary>
            Gets or Sets Data
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Model.GetPaymentOutputOutput.Message">
            <summary>
            Gets or Sets Message
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Model.GetPaymentOutputOutput.DateTime">
            <summary>
            Gets or Sets DateTime
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Model.GetPaymentOutputOutput.HttpStatusCode">
            <summary>
            Gets or Sets HttpStatusCode
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Model.GetPaymentOutputOutput.ErrorCode">
            <summary>
            Gets or Sets ErrorCode
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Model.GetPaymentOutputOutput.ErrorContext">
            <summary>
            Gets or Sets ErrorContext
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Model.GetPaymentOutputOutput.RequestId">
            <summary>
            Gets or Sets RequestId
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.GetPaymentOutputOutput.ToString">
            <summary>
            Returns the string presentation of the object
            </summary>
            <returns>String presentation of the object</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.GetPaymentOutputOutput.ToJson">
            <summary>
            Returns the JSON string presentation of the object
            </summary>
            <returns>JSON string presentation of the object</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.GetPaymentOutputOutput.Equals(System.Object)">
            <summary>
            Returns true if objects are equal
            </summary>
            <param name="input">Object to be compared</param>
            <returns>Boolean</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.GetPaymentOutputOutput.Equals(Sleekflow.Apis.InternalIntegrationHub.Model.GetPaymentOutputOutput)">
            <summary>
            Returns true if GetPaymentOutputOutput instances are equal
            </summary>
            <param name="input">Instance of GetPaymentOutputOutput to be compared</param>
            <returns>Boolean</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.GetPaymentOutputOutput.GetHashCode">
            <summary>
            Gets the hash code
            </summary>
            <returns>Hash code</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.GetPaymentOutputOutput.Validate(System.ComponentModel.DataAnnotations.ValidationContext)">
            <summary>
            To validate all properties of the instance
            </summary>
            <param name="validationContext">Validation context</param>
            <returns>Validation Result</returns>
        </member>
        <member name="T:Sleekflow.Apis.InternalIntegrationHub.Model.GetPaymentResponse">
            <summary>
            GetPaymentResponse
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.GetPaymentResponse.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.InternalIntegrationHub.Model.GetPaymentResponse" /> class.
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.GetPaymentResponse.#ctor(System.String,System.String,System.Decimal,System.Decimal,System.Decimal,System.String,System.DateTimeOffset,System.String,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.InternalIntegrationHub.Model.GetPaymentResponse" /> class.
            </summary>
            <param name="customerId">customerId (required).</param>
            <param name="billRecordId">billRecordId (required).</param>
            <param name="subscriptionFee">subscriptionFee (required).</param>
            <param name="oneTimeSetupFee">oneTimeSetupFee (required).</param>
            <param name="whatsappCreditAmount">whatsappCreditAmount (required).</param>
            <param name="currency">currency (required).</param>
            <param name="paymentDate">paymentDate (required).</param>
            <param name="invoiceId">invoiceId (required).</param>
            <param name="paymentTerms">paymentTerms (required).</param>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Model.GetPaymentResponse.CustomerId">
            <summary>
            Gets or Sets CustomerId
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Model.GetPaymentResponse.BillRecordId">
            <summary>
            Gets or Sets BillRecordId
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Model.GetPaymentResponse.SubscriptionFee">
            <summary>
            Gets or Sets SubscriptionFee
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Model.GetPaymentResponse.OneTimeSetupFee">
            <summary>
            Gets or Sets OneTimeSetupFee
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Model.GetPaymentResponse.WhatsappCreditAmount">
            <summary>
            Gets or Sets WhatsappCreditAmount
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Model.GetPaymentResponse.Currency">
            <summary>
            Gets or Sets Currency
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Model.GetPaymentResponse.PaymentDate">
            <summary>
            Gets or Sets PaymentDate
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Model.GetPaymentResponse.InvoiceId">
            <summary>
            Gets or Sets InvoiceId
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Model.GetPaymentResponse.PaymentTerms">
            <summary>
            Gets or Sets PaymentTerms
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.GetPaymentResponse.ToString">
            <summary>
            Returns the string presentation of the object
            </summary>
            <returns>String presentation of the object</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.GetPaymentResponse.ToJson">
            <summary>
            Returns the JSON string presentation of the object
            </summary>
            <returns>JSON string presentation of the object</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.GetPaymentResponse.Equals(System.Object)">
            <summary>
            Returns true if objects are equal
            </summary>
            <param name="input">Object to be compared</param>
            <returns>Boolean</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.GetPaymentResponse.Equals(Sleekflow.Apis.InternalIntegrationHub.Model.GetPaymentResponse)">
            <summary>
            Returns true if GetPaymentResponse instances are equal
            </summary>
            <param name="input">Instance of GetPaymentResponse to be compared</param>
            <returns>Boolean</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.GetPaymentResponse.GetHashCode">
            <summary>
            Gets the hash code
            </summary>
            <returns>Hash code</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.GetPaymentResponse.Validate(System.ComponentModel.DataAnnotations.ValidationContext)">
            <summary>
            To validate all properties of the instance
            </summary>
            <param name="validationContext">Validation context</param>
            <returns>Validation Result</returns>
        </member>
        <member name="T:Sleekflow.Apis.InternalIntegrationHub.Model.Subsidiary">
            <summary>
            Subsidiary
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.Subsidiary.#ctor(System.String,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.InternalIntegrationHub.Model.Subsidiary" /> class.
            </summary>
            <param name="id">id.</param>
            <param name="refName">refName.</param>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Model.Subsidiary.Id">
            <summary>
            Gets or Sets Id
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Model.Subsidiary.RefName">
            <summary>
            Gets or Sets RefName
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.Subsidiary.ToString">
            <summary>
            Returns the string presentation of the object
            </summary>
            <returns>String presentation of the object</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.Subsidiary.ToJson">
            <summary>
            Returns the JSON string presentation of the object
            </summary>
            <returns>JSON string presentation of the object</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.Subsidiary.Equals(System.Object)">
            <summary>
            Returns true if objects are equal
            </summary>
            <param name="input">Object to be compared</param>
            <returns>Boolean</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.Subsidiary.Equals(Sleekflow.Apis.InternalIntegrationHub.Model.Subsidiary)">
            <summary>
            Returns true if Subsidiary instances are equal
            </summary>
            <param name="input">Instance of Subsidiary to be compared</param>
            <returns>Boolean</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.Subsidiary.GetHashCode">
            <summary>
            Gets the hash code
            </summary>
            <returns>Hash code</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.Subsidiary.Validate(System.ComponentModel.DataAnnotations.ValidationContext)">
            <summary>
            To validate all properties of the instance
            </summary>
            <param name="validationContext">Validation context</param>
            <returns>Validation Result</returns>
        </member>
        <member name="T:Sleekflow.Apis.InternalIntegrationHub.Model.Supervisor">
            <summary>
            Supervisor
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.Supervisor.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.InternalIntegrationHub.Model.Supervisor" /> class.
            </summary>
            <param name="id">id.</param>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Model.Supervisor.Id">
            <summary>
            Gets or Sets Id
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.Supervisor.ToString">
            <summary>
            Returns the string presentation of the object
            </summary>
            <returns>String presentation of the object</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.Supervisor.ToJson">
            <summary>
            Returns the JSON string presentation of the object
            </summary>
            <returns>JSON string presentation of the object</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.Supervisor.Equals(System.Object)">
            <summary>
            Returns true if objects are equal
            </summary>
            <param name="input">Object to be compared</param>
            <returns>Boolean</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.Supervisor.Equals(Sleekflow.Apis.InternalIntegrationHub.Model.Supervisor)">
            <summary>
            Returns true if Supervisor instances are equal
            </summary>
            <param name="input">Instance of Supervisor to be compared</param>
            <returns>Boolean</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.Supervisor.GetHashCode">
            <summary>
            Gets the hash code
            </summary>
            <returns>Hash code</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.Supervisor.Validate(System.ComponentModel.DataAnnotations.ValidationContext)">
            <summary>
            To validate all properties of the instance
            </summary>
            <param name="validationContext">Validation context</param>
            <returns>Validation Result</returns>
        </member>
        <member name="T:Sleekflow.Apis.InternalIntegrationHub.Model.UpdateCustomerInput">
            <summary>
            UpdateCustomerInput
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.UpdateCustomerInput.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.InternalIntegrationHub.Model.UpdateCustomerInput" /> class.
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.UpdateCustomerInput.#ctor(System.String,System.String,System.String,System.String,System.String,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.InternalIntegrationHub.Model.UpdateCustomerInput" /> class.
            </summary>
            <param name="companyId">companyId (required).</param>
            <param name="companyName">companyName.</param>
            <param name="companyCountry">companyCountry.</param>
            <param name="companyOwnerEmail">companyOwnerEmail.</param>
            <param name="companyOwnerPhone">companyOwnerPhone.</param>
            <param name="salesRepEmail">salesRepEmail.</param>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Model.UpdateCustomerInput.CompanyId">
            <summary>
            Gets or Sets CompanyId
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Model.UpdateCustomerInput.CompanyName">
            <summary>
            Gets or Sets CompanyName
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Model.UpdateCustomerInput.CompanyCountry">
            <summary>
            Gets or Sets CompanyCountry
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Model.UpdateCustomerInput.CompanyOwnerEmail">
            <summary>
            Gets or Sets CompanyOwnerEmail
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Model.UpdateCustomerInput.CompanyOwnerPhone">
            <summary>
            Gets or Sets CompanyOwnerPhone
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Model.UpdateCustomerInput.SalesRepEmail">
            <summary>
            Gets or Sets SalesRepEmail
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.UpdateCustomerInput.ToString">
            <summary>
            Returns the string presentation of the object
            </summary>
            <returns>String presentation of the object</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.UpdateCustomerInput.ToJson">
            <summary>
            Returns the JSON string presentation of the object
            </summary>
            <returns>JSON string presentation of the object</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.UpdateCustomerInput.Equals(System.Object)">
            <summary>
            Returns true if objects are equal
            </summary>
            <param name="input">Object to be compared</param>
            <returns>Boolean</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.UpdateCustomerInput.Equals(Sleekflow.Apis.InternalIntegrationHub.Model.UpdateCustomerInput)">
            <summary>
            Returns true if UpdateCustomerInput instances are equal
            </summary>
            <param name="input">Instance of UpdateCustomerInput to be compared</param>
            <returns>Boolean</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.UpdateCustomerInput.GetHashCode">
            <summary>
            Gets the hash code
            </summary>
            <returns>Hash code</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.UpdateCustomerInput.Validate(System.ComponentModel.DataAnnotations.ValidationContext)">
            <summary>
            To validate all properties of the instance
            </summary>
            <param name="validationContext">Validation context</param>
            <returns>Validation Result</returns>
        </member>
        <member name="T:Sleekflow.Apis.InternalIntegrationHub.Model.UpdateCustomerOutputOutput">
            <summary>
            UpdateCustomerOutputOutput
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.UpdateCustomerOutputOutput.#ctor(System.Boolean,System.Object,System.String,System.DateTimeOffset,System.Int32,System.Nullable{System.Int32},System.Collections.Generic.Dictionary{System.String,System.Object},System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.InternalIntegrationHub.Model.UpdateCustomerOutputOutput" /> class.
            </summary>
            <param name="success">success.</param>
            <param name="data">data.</param>
            <param name="message">message.</param>
            <param name="dateTime">dateTime.</param>
            <param name="httpStatusCode">httpStatusCode.</param>
            <param name="errorCode">errorCode.</param>
            <param name="errorContext">errorContext.</param>
            <param name="requestId">requestId.</param>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Model.UpdateCustomerOutputOutput.Success">
            <summary>
            Gets or Sets Success
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Model.UpdateCustomerOutputOutput.Data">
            <summary>
            Gets or Sets Data
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Model.UpdateCustomerOutputOutput.Message">
            <summary>
            Gets or Sets Message
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Model.UpdateCustomerOutputOutput.DateTime">
            <summary>
            Gets or Sets DateTime
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Model.UpdateCustomerOutputOutput.HttpStatusCode">
            <summary>
            Gets or Sets HttpStatusCode
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Model.UpdateCustomerOutputOutput.ErrorCode">
            <summary>
            Gets or Sets ErrorCode
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Model.UpdateCustomerOutputOutput.ErrorContext">
            <summary>
            Gets or Sets ErrorContext
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Model.UpdateCustomerOutputOutput.RequestId">
            <summary>
            Gets or Sets RequestId
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.UpdateCustomerOutputOutput.ToString">
            <summary>
            Returns the string presentation of the object
            </summary>
            <returns>String presentation of the object</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.UpdateCustomerOutputOutput.ToJson">
            <summary>
            Returns the JSON string presentation of the object
            </summary>
            <returns>JSON string presentation of the object</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.UpdateCustomerOutputOutput.Equals(System.Object)">
            <summary>
            Returns true if objects are equal
            </summary>
            <param name="input">Object to be compared</param>
            <returns>Boolean</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.UpdateCustomerOutputOutput.Equals(Sleekflow.Apis.InternalIntegrationHub.Model.UpdateCustomerOutputOutput)">
            <summary>
            Returns true if UpdateCustomerOutputOutput instances are equal
            </summary>
            <param name="input">Instance of UpdateCustomerOutputOutput to be compared</param>
            <returns>Boolean</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.UpdateCustomerOutputOutput.GetHashCode">
            <summary>
            Gets the hash code
            </summary>
            <returns>Hash code</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.UpdateCustomerOutputOutput.Validate(System.ComponentModel.DataAnnotations.ValidationContext)">
            <summary>
            To validate all properties of the instance
            </summary>
            <param name="validationContext">Validation context</param>
            <returns>Validation Result</returns>
        </member>
        <member name="T:Sleekflow.Apis.InternalIntegrationHub.Model.UpdateEmployeeManuallyInput">
            <summary>
            UpdateEmployeeManuallyInput
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.UpdateEmployeeManuallyInput.#ctor(System.Collections.Generic.Dictionary{System.String,Sleekflow.Apis.InternalIntegrationHub.Model.UpdateEmployeeRequest},System.Collections.Generic.Dictionary{System.String,Sleekflow.Apis.InternalIntegrationHub.Model.CreateEmployeeRequest})">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.InternalIntegrationHub.Model.UpdateEmployeeManuallyInput" /> class.
            </summary>
            <param name="updateSupervisors">updateSupervisors.</param>
            <param name="updateEmployeeInfo">updateEmployeeInfo.</param>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Model.UpdateEmployeeManuallyInput.UpdateSupervisors">
            <summary>
            Gets or Sets UpdateSupervisors
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Model.UpdateEmployeeManuallyInput.UpdateEmployeeInfo">
            <summary>
            Gets or Sets UpdateEmployeeInfo
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.UpdateEmployeeManuallyInput.ToString">
            <summary>
            Returns the string presentation of the object
            </summary>
            <returns>String presentation of the object</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.UpdateEmployeeManuallyInput.ToJson">
            <summary>
            Returns the JSON string presentation of the object
            </summary>
            <returns>JSON string presentation of the object</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.UpdateEmployeeManuallyInput.Equals(System.Object)">
            <summary>
            Returns true if objects are equal
            </summary>
            <param name="input">Object to be compared</param>
            <returns>Boolean</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.UpdateEmployeeManuallyInput.Equals(Sleekflow.Apis.InternalIntegrationHub.Model.UpdateEmployeeManuallyInput)">
            <summary>
            Returns true if UpdateEmployeeManuallyInput instances are equal
            </summary>
            <param name="input">Instance of UpdateEmployeeManuallyInput to be compared</param>
            <returns>Boolean</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.UpdateEmployeeManuallyInput.GetHashCode">
            <summary>
            Gets the hash code
            </summary>
            <returns>Hash code</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.UpdateEmployeeManuallyInput.Validate(System.ComponentModel.DataAnnotations.ValidationContext)">
            <summary>
            To validate all properties of the instance
            </summary>
            <param name="validationContext">Validation context</param>
            <returns>Validation Result</returns>
        </member>
        <member name="T:Sleekflow.Apis.InternalIntegrationHub.Model.UpdateEmployeeManuallyOutput">
            <summary>
            UpdateEmployeeManuallyOutput
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.UpdateEmployeeManuallyOutput.#ctor(System.Int32,System.Int32,System.Collections.Generic.List{System.String})">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.InternalIntegrationHub.Model.UpdateEmployeeManuallyOutput" /> class.
            </summary>
            <param name="updatedSupervisorsCount">updatedSupervisorsCount.</param>
            <param name="updatedEmployeeInfoCount">updatedEmployeeInfoCount.</param>
            <param name="failedEmployeeIds">failedEmployeeIds.</param>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Model.UpdateEmployeeManuallyOutput.UpdatedSupervisorsCount">
            <summary>
            Gets or Sets UpdatedSupervisorsCount
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Model.UpdateEmployeeManuallyOutput.UpdatedEmployeeInfoCount">
            <summary>
            Gets or Sets UpdatedEmployeeInfoCount
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Model.UpdateEmployeeManuallyOutput.FailedEmployeeIds">
            <summary>
            Gets or Sets FailedEmployeeIds
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.UpdateEmployeeManuallyOutput.ToString">
            <summary>
            Returns the string presentation of the object
            </summary>
            <returns>String presentation of the object</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.UpdateEmployeeManuallyOutput.ToJson">
            <summary>
            Returns the JSON string presentation of the object
            </summary>
            <returns>JSON string presentation of the object</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.UpdateEmployeeManuallyOutput.Equals(System.Object)">
            <summary>
            Returns true if objects are equal
            </summary>
            <param name="input">Object to be compared</param>
            <returns>Boolean</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.UpdateEmployeeManuallyOutput.Equals(Sleekflow.Apis.InternalIntegrationHub.Model.UpdateEmployeeManuallyOutput)">
            <summary>
            Returns true if UpdateEmployeeManuallyOutput instances are equal
            </summary>
            <param name="input">Instance of UpdateEmployeeManuallyOutput to be compared</param>
            <returns>Boolean</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.UpdateEmployeeManuallyOutput.GetHashCode">
            <summary>
            Gets the hash code
            </summary>
            <returns>Hash code</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.UpdateEmployeeManuallyOutput.Validate(System.ComponentModel.DataAnnotations.ValidationContext)">
            <summary>
            To validate all properties of the instance
            </summary>
            <param name="validationContext">Validation context</param>
            <returns>Validation Result</returns>
        </member>
        <member name="T:Sleekflow.Apis.InternalIntegrationHub.Model.UpdateEmployeeManuallyOutputOutput">
            <summary>
            UpdateEmployeeManuallyOutputOutput
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.UpdateEmployeeManuallyOutputOutput.#ctor(System.Boolean,Sleekflow.Apis.InternalIntegrationHub.Model.UpdateEmployeeManuallyOutput,System.String,System.DateTimeOffset,System.Int32,System.Nullable{System.Int32},System.Collections.Generic.Dictionary{System.String,System.Object},System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.InternalIntegrationHub.Model.UpdateEmployeeManuallyOutputOutput" /> class.
            </summary>
            <param name="success">success.</param>
            <param name="data">data.</param>
            <param name="message">message.</param>
            <param name="dateTime">dateTime.</param>
            <param name="httpStatusCode">httpStatusCode.</param>
            <param name="errorCode">errorCode.</param>
            <param name="errorContext">errorContext.</param>
            <param name="requestId">requestId.</param>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Model.UpdateEmployeeManuallyOutputOutput.Success">
            <summary>
            Gets or Sets Success
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Model.UpdateEmployeeManuallyOutputOutput.Data">
            <summary>
            Gets or Sets Data
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Model.UpdateEmployeeManuallyOutputOutput.Message">
            <summary>
            Gets or Sets Message
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Model.UpdateEmployeeManuallyOutputOutput.DateTime">
            <summary>
            Gets or Sets DateTime
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Model.UpdateEmployeeManuallyOutputOutput.HttpStatusCode">
            <summary>
            Gets or Sets HttpStatusCode
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Model.UpdateEmployeeManuallyOutputOutput.ErrorCode">
            <summary>
            Gets or Sets ErrorCode
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Model.UpdateEmployeeManuallyOutputOutput.ErrorContext">
            <summary>
            Gets or Sets ErrorContext
            </summary>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Model.UpdateEmployeeManuallyOutputOutput.RequestId">
            <summary>
            Gets or Sets RequestId
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.UpdateEmployeeManuallyOutputOutput.ToString">
            <summary>
            Returns the string presentation of the object
            </summary>
            <returns>String presentation of the object</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.UpdateEmployeeManuallyOutputOutput.ToJson">
            <summary>
            Returns the JSON string presentation of the object
            </summary>
            <returns>JSON string presentation of the object</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.UpdateEmployeeManuallyOutputOutput.Equals(System.Object)">
            <summary>
            Returns true if objects are equal
            </summary>
            <param name="input">Object to be compared</param>
            <returns>Boolean</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.UpdateEmployeeManuallyOutputOutput.Equals(Sleekflow.Apis.InternalIntegrationHub.Model.UpdateEmployeeManuallyOutputOutput)">
            <summary>
            Returns true if UpdateEmployeeManuallyOutputOutput instances are equal
            </summary>
            <param name="input">Instance of UpdateEmployeeManuallyOutputOutput to be compared</param>
            <returns>Boolean</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.UpdateEmployeeManuallyOutputOutput.GetHashCode">
            <summary>
            Gets the hash code
            </summary>
            <returns>Hash code</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.UpdateEmployeeManuallyOutputOutput.Validate(System.ComponentModel.DataAnnotations.ValidationContext)">
            <summary>
            To validate all properties of the instance
            </summary>
            <param name="validationContext">Validation context</param>
            <returns>Validation Result</returns>
        </member>
        <member name="T:Sleekflow.Apis.InternalIntegrationHub.Model.UpdateEmployeeRequest">
            <summary>
            UpdateEmployeeRequest
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.UpdateEmployeeRequest.#ctor(Sleekflow.Apis.InternalIntegrationHub.Model.Supervisor)">
            <summary>
            Initializes a new instance of the <see cref="T:Sleekflow.Apis.InternalIntegrationHub.Model.UpdateEmployeeRequest" /> class.
            </summary>
            <param name="supervisor">supervisor.</param>
        </member>
        <member name="P:Sleekflow.Apis.InternalIntegrationHub.Model.UpdateEmployeeRequest.Supervisor">
            <summary>
            Gets or Sets Supervisor
            </summary>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.UpdateEmployeeRequest.ToString">
            <summary>
            Returns the string presentation of the object
            </summary>
            <returns>String presentation of the object</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.UpdateEmployeeRequest.ToJson">
            <summary>
            Returns the JSON string presentation of the object
            </summary>
            <returns>JSON string presentation of the object</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.UpdateEmployeeRequest.Equals(System.Object)">
            <summary>
            Returns true if objects are equal
            </summary>
            <param name="input">Object to be compared</param>
            <returns>Boolean</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.UpdateEmployeeRequest.Equals(Sleekflow.Apis.InternalIntegrationHub.Model.UpdateEmployeeRequest)">
            <summary>
            Returns true if UpdateEmployeeRequest instances are equal
            </summary>
            <param name="input">Instance of UpdateEmployeeRequest to be compared</param>
            <returns>Boolean</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.UpdateEmployeeRequest.GetHashCode">
            <summary>
            Gets the hash code
            </summary>
            <returns>Hash code</returns>
        </member>
        <member name="M:Sleekflow.Apis.InternalIntegrationHub.Model.UpdateEmployeeRequest.Validate(System.ComponentModel.DataAnnotations.ValidationContext)">
            <summary>
            To validate all properties of the instance
            </summary>
            <param name="validationContext">Validation context</param>
            <returns>Validation Result</returns>
        </member>
    </members>
</doc>
