using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using AutoMapper;
using Hangfire;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Travis_backend.Constants;
using Travis_backend.ConversationServices;
using Travis_backend.Database;
using Travis_backend.Enums;
using Travis_backend.Helpers;
using Travis_backend.InternalDomain.Models;
using Travis_backend.InternalDomain.ViewModels;
using Travis_backend.PartnerStackIntegrationDomain.Clients;
using Travis_backend.PartnerStackIntegrationDomain.Services;
using Travis_backend.ResellerDomain.Models;
using Travis_backend.ResellerDomain.ViewModels;

namespace Travis_backend.InternalDomain.Services;

public interface IInternalPartnerStackService
{
    Task<ResponseWrapper> CreateOrUpdatePartnerStackCustomerMapping(string companyId, string customerKey);

    Task<ResponseWrapper> PowerflowSyncMrrToPartnerStack(string companyId);

    Task<ResponseWrapper> UpdatePartnerStackPartnerKey(string companyId, string partnerKey);

    Task SyncPartnerStackCustomerKeyFromHubSpotContact(string companyId, string userEmail);

    Task SyncAllMrrToPartnerStack();

    Task SyncPartnerStackPartnerKeyFromHubSpotContact(string companyId, string userEmail);

    Task<ResponseWrapper> PowerflowUpdatePartnerStackIndividualCommissionConfig(
        string companyId,
        string syncType,
        int? individualCommissionRate,
        string commissionEndDate);

    Task<ResponseWrapper> SyncPartnerStackPartnerInformation(string companyId);
}

public class InternalPartnerStackService : IInternalPartnerStackService
{
    private readonly ApplicationDbContext _appDbContext;
    private readonly IMapper _mapper;
    private readonly ILogger<InternalPartnerStackService> _logger;
    private readonly IPartnerStackIntegrationService _partnerStackIntegrationService;
    private readonly IInternalHubspotRepository _internalHubspotRepository;
    private readonly IConfiguration _configuration;

    public InternalPartnerStackService(
        ApplicationDbContext appDbContext,
        IMapper mapper,
        ILogger<InternalPartnerStackService> logger,
        IPartnerStackIntegrationService partnerStackIntegrationService,
        IInternalHubspotRepository internalHubspotRepository,
        IConfiguration configuration)
    {
        _appDbContext = appDbContext;
        _mapper = mapper;
        _logger = logger;
        _partnerStackIntegrationService = partnerStackIntegrationService;
        _internalHubspotRepository = internalHubspotRepository;
        _configuration = configuration;
    }

    public async Task<ResponseWrapper> CreateOrUpdatePartnerStackCustomerMapping(
        string companyId,
        string customerKey)
    {
        var response = new ResponseWrapper
        {
            IsSuccess = false
        };

        try
        {
            var company = await _appDbContext.CompanyCompanies
                .FirstOrDefaultAsync(x => x.Id == companyId);

            if (company == null || company.IsDeleted)
            {
                response.ErrorMsg = "Error: Company not found or deleted";
                return response;
            }

            // Check if the customer key already mapped to a company
            var existedPartnerStackCustomerMap = await _appDbContext.CmsPartnerStackCustomerMaps
                .FirstOrDefaultAsync(x => x.PartnerStackCustomerKey == customerKey);

            if (existedPartnerStackCustomerMap != null)
            {
                response.ErrorMsg = "Error: Customer key already mapped to a company";
                return response;
            }

            // Check if the Partner Stack customer exists
            var partnerStackCustomer =
                await _partnerStackIntegrationService.RetrievePartnerStackCustomerByCustomerKey(customerKey);

            if (partnerStackCustomer == null)
            {
                response.ErrorMsg = "Error: PartnerStack customer not found";
                return response;
            }

            // Check if the Partner Stack customer is a staff of the company
            var existedCompanyStaff = await _appDbContext.UserRoleStaffs
                .Include(x => x.Identity)
                .FirstOrDefaultAsync(x => x.CompanyId == companyId && x.Identity.Email == partnerStackCustomer.Email);

            if (existedCompanyStaff == null)
            {
                response.ErrorMsg = "Error: PartnerStack customer is not a staff of the company";
                return response;
            }

            // Check if the Partner Stack partner exists
            var partnerStackPartner =
                await _partnerStackIntegrationService.RetrievePartnerStackPartnershipByUniqueIdentifier(
                    partnerStackCustomer.PartnerKey);

            if (partnerStackPartner == null)
            {
                response.ErrorMsg = "Error: PartnerStack partner not found";
                return response;
            }

            var newPartnerStackPartnerInformation = new PartnerStackPartnerInformation
            {
                PartnerKey = partnerStackPartner.PartnerKey,
                GroupName = partnerStackPartner.Group.Name,
                TeamName = partnerStackPartner.Team.Name
            };

            // Update the company's Partner Stack customer mapping
            var currentCompanyPartnerStackCustomerMap = await _appDbContext.CmsPartnerStackCustomerMaps
                .FirstOrDefaultAsync(x => x.CompanyId == companyId);

            if (currentCompanyPartnerStackCustomerMap != null)
            {
                currentCompanyPartnerStackCustomerMap.PartnerStackCustomerKey = customerKey;
                currentCompanyPartnerStackCustomerMap.PartnerStackPartnerInformation = newPartnerStackPartnerInformation;
                currentCompanyPartnerStackCustomerMap.UpdatedAt = DateTime.UtcNow;
                response.Data = _mapper.Map<CmsPartnerStackCustomerMapDto>(currentCompanyPartnerStackCustomerMap);
            }
            else
            {
                var newCompanyPartnerStackCustomerMap = new CmsPartnerStackCustomerMap
                {
                    CompanyId = companyId,
                    PartnerStackCustomerKey = customerKey,
                    IndividualCommissionConfig = new IndividualCommissionConfig
                    {
                        SyncType = PartnerStackConstants.MrrSyncType,
                        IndividualCommissionRate = null,
                        CommissionEndDate = null
                    },
                    PartnerStackPartnerInformation = newPartnerStackPartnerInformation
                };

                await _appDbContext.CmsPartnerStackCustomerMaps.AddAsync(newCompanyPartnerStackCustomerMap);
                response.Data = _mapper.Map<CmsPartnerStackCustomerMapDto>(newCompanyPartnerStackCustomerMap);
            }

            await _appDbContext.SaveChangesAsync();

            response.IsSuccess = true;
            return response;
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "[PartnerStack {MethodName}] Create or Update Partner Stack Customer Mapping failed, Company ID: {CompanyID}, Customer Key: {CustomerKey}: {ExceptionMessage}",
                nameof(CreateOrUpdatePartnerStackCustomerMapping),
                companyId,
                customerKey,
                ex.Message);

            response.ErrorMsg = "Error: Failed to create or update Partner Stack Customer Mapping";
            return response;
        }
    }

    public async Task<ResponseWrapper> PowerflowSyncMrrToPartnerStack(string companyId)
    {
        var response = new ResponseWrapper
        {
            IsSuccess = false
        };

        try
        {
            // Check if the company got Partner Stack customer mapping
            var existedPartnerStackCustomerMap = await _appDbContext.CmsPartnerStackCustomerMaps
                .Include(x => x.Company)
                .FirstOrDefaultAsync(x => x.CompanyId == companyId);

            if (existedPartnerStackCustomerMap == null)
            {
                response.ErrorMsg = "Error: Company does not have Partner Stack customer mapping";
                return response;
            }

            // Check if the company exists or not deleted
            if (existedPartnerStackCustomerMap.Company == null ||
                existedPartnerStackCustomerMap.Company.IsDeleted)
            {
                response.ErrorMsg = "Error: Company not found or deleted";
                return response;
            }

            var syncResult = await SyncMrrToPartnerStack(existedPartnerStackCustomerMap);

            if (syncResult.Message.Contains("Success"))
            {
                response.IsSuccess = true;
            }

            response.Data = syncResult.Message;
            return response;
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "[PartnerStack {MethodName}] Sync Mrr to PartnerStack failed, Company ID: {CompanyID}: {ExceptionMessage}",
                nameof(PowerflowSyncMrrToPartnerStack),
                companyId,
                ex.Message);

            response.ErrorMsg = "Error: Failed to sync MRR to PartnerStack";
            return response;
        }
    }

    public async Task<ResponseWrapper> UpdatePartnerStackPartnerKey(string companyId, string partnerKey)
    {
        var response = new ResponseWrapper
        {
            IsSuccess = false
        };

        try
        {
            var resellerProfile = await _appDbContext.ResellerCompanyProfiles
                .Include(x => x.Company)
                .FirstOrDefaultAsync(x => x.CompanyId == companyId);

            // Check if the reseller profile exists
            if (resellerProfile == null)
            {
                response.ErrorMsg = "Error: Reseller profile not found";
                return response;
            }

            // Check if the company exists or not deleted
            if (resellerProfile.Company == null || resellerProfile.Company.IsDeleted)
            {
                response.ErrorMsg = "Error: Company not found or deleted";
                return response;
            }

            // Check if the Partner Stack partner key is already mapped to a reseller company
            var existedPartnerStackPartnerResellerProfile = await _appDbContext.ResellerCompanyProfiles
                .FirstOrDefaultAsync(x => x.PartnerStackPartnerKey == partnerKey);

            if (existedPartnerStackPartnerResellerProfile != null)
            {
                response.ErrorMsg = "Error: PartnerStack partner key already mapped to a reseller company";
                return response;
            }

            // Check if the Partner Stack partnership exists
            var partnerStackPartnership =
                await _partnerStackIntegrationService.RetrievePartnerStackPartnershipByUniqueIdentifier(partnerKey);

            if (partnerStackPartnership == null)
            {
                response.ErrorMsg = "Error: PartnerStack partnership not found";
                return response;
            }

            // Check if the Partner Stack partnership email is a reseller staff of the company
            var existedResellerStaff = await _appDbContext.UserRoleStaffs
                .Include(x => x.Identity)
                .FirstOrDefaultAsync(
                    x => x.CompanyId == companyId && x.Identity.Email == partnerStackPartnership.Email);

            if (existedResellerStaff == null)
            {
                response.ErrorMsg = "Error: PartnerStack partnership is not a staff of the company";
                return response;
            }

            // Update the company's Partner Stack partnership key
            resellerProfile.PartnerStackPartnerKey = partnerKey;
            resellerProfile.UpdatedAt = DateTime.UtcNow;

            await _appDbContext.SaveChangesAsync();

            response.IsSuccess = true;
            response.Data = _mapper.Map<ResellerProfileInformation>(resellerProfile);
            return response;
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "[PartnerStack {MethodName}] Update Partner Stack Partner Key failed, Company ID: {CompanyID}: {ExceptionMessage}",
                nameof(UpdatePartnerStackPartnerKey),
                companyId,
                ex.Message);

            response.ErrorMsg = "Error: Failed to update Partner Stack Partner Key";
            return response;
        }
    }

    private async Task<SyncMrrToPartnerStackDto> SyncMrrToPartnerStack(
        CmsPartnerStackCustomerMap cmsPartnerStackCustomerMap)
    {
        if (cmsPartnerStackCustomerMap.IndividualCommissionConfig == null)
        {
            cmsPartnerStackCustomerMap.IndividualCommissionConfig = new IndividualCommissionConfig
            {
                SyncType = PartnerStackConstants.MrrSyncType, IndividualCommissionRate = null, CommissionEndDate = null
            };

            await _appDbContext.SaveChangesAsync();
        }

        // Check if the Partner Stack customer exists
        var partnerStackCustomer = await _partnerStackIntegrationService.RetrievePartnerStackCustomerByCustomerKey(
            cmsPartnerStackCustomerMap.PartnerStackCustomerKey);

        if (partnerStackCustomer == null)
        {
            return new SyncMrrToPartnerStackDto
            {
                SleekFlowCompanyId = cmsPartnerStackCustomerMap.CompanyId,
                SleekFlowCompanyName = cmsPartnerStackCustomerMap.Company.CompanyName ?? string.Empty,
                Message = "Error: PartnerStack customer not found"
            };
        }

        // Get Latest 10 Transactions form PartnerStack
        var partnerStackTransactions = await _partnerStackIntegrationService.ListPartnerStackTransactions(
            customerExternalKey: cmsPartnerStackCustomerMap.PartnerStackCustomerKey);

        if (partnerStackTransactions == null)
        {
            return new SyncMrrToPartnerStackDto
            {
                SleekFlowCompanyId = cmsPartnerStackCustomerMap.CompanyId,
                SleekFlowCompanyName = cmsPartnerStackCustomerMap.Company.CompanyName ?? string.Empty,
                Message = "Error: Failed to get PartnerStack transactions"
            };
        }

        var currentDateTime = DateTime.UtcNow;
        var currentQuarterMonths = GetCurrentQuarterMonths(currentDateTime);
        const int syncDayOfMonth = 28;

        var newTransactions = new List<CreatePartnerStackTransactionRequest>();

        var companyBillRecords = await _appDbContext.CompanyBillRecords
            .Include(x => x.CmsSalesPaymentRecords)
            .Where(
                x => x.CompanyId == cmsPartnerStackCustomerMap.CompanyId &&
                     x.Status != BillStatus.Inactive)
            .OrderByDescending(br => br.created)
            .AsNoTracking()
            .ToListAsync();

        var dailyRevenueAnalyticDtos = new List<CmsDailyRevenueDto>();

        if (cmsPartnerStackCustomerMap.IndividualCommissionConfig.SyncType == PartnerStackConstants.OneTimeOffSyncType)
        {
            var firstMonthThisQuarter = currentQuarterMonths[0];
            var thisQuarterFirstSyncDateTime = new DateTime(
                currentDateTime.Year,
                firstMonthThisQuarter,
                syncDayOfMonth,
                0,
                0,
                0,
                DateTimeKind.Utc);
            var start = thisQuarterFirstSyncDateTime.AddMonths(-1);
            var end = currentDateTime.Date.AddHours(8);

            dailyRevenueAnalyticDtos = BillRecordRevenueCalculator.GetDailyRevenues(
                new List<CmsCompanyAnalyticDto>
                {
                    new ()
                    {
                        Id = cmsPartnerStackCustomerMap.CompanyId,
                        CompanyName = cmsPartnerStackCustomerMap.Company.CompanyName,
                        BillRecords = _mapper.Map<List<CmsAnalyticBillRecordDto>>(companyBillRecords)
                    }
                },
                start,
                end);
        }

        foreach (var month in currentQuarterMonths)
        {
            var categoryKey = GetCurrentMonthlySyncCategoryKey(
                cmsPartnerStackCustomerMap.IndividualCommissionConfig.SyncType,
                cmsPartnerStackCustomerMap.IndividualCommissionConfig.IndividualCommissionRate);

            var currentMonthSyncDateTime = new DateTime(currentDateTime.Year, month, syncDayOfMonth, 0, 0, 0, DateTimeKind.Utc);

            // If the company created after the sync date, skip the sync
            if (currentMonthSyncDateTime < cmsPartnerStackCustomerMap.Company.CreatedAt.Date)
            {
                continue;
            }

            // If the sync date is in the future, stop the sync
            if (currentMonthSyncDateTime > currentDateTime)
            {
                break;
            }

            if (cmsPartnerStackCustomerMap.IndividualCommissionConfig.CommissionEndDate.HasValue &&
                currentMonthSyncDateTime >
                cmsPartnerStackCustomerMap.IndividualCommissionConfig.CommissionEndDate.Value)
            {
                categoryKey = GetCurrentMonthlySyncCategoryKey(
                    cmsPartnerStackCustomerMap.IndividualCommissionConfig.SyncType,
                    0);
            }

            // Check if the transaction already exists
            var productKey = GetCurrentMonthlySyncProductKey(
                cmsPartnerStackCustomerMap.IndividualCommissionConfig.SyncType,
                currentMonthSyncDateTime.ToString("yyyy-MM-dd"));

            var existedTransaction = partnerStackTransactions.Items
                .Find(x => x.ProductKey == productKey);

            if (existedTransaction != null)
            {
                continue;
            }

            int transactionAmount;

            if (cmsPartnerStackCustomerMap.IndividualCommissionConfig.SyncType == PartnerStackConstants.MrrSyncType)
            {
                var currentMonthMrr = BillRecordRevenueCalculator.SumMonthlyRecurringRevenue(
                    companyBillRecords,
                    currentMonthSyncDateTime);

                transactionAmount = Convert.ToInt32(Math.Round(currentMonthMrr, 2) * 100);
            }
            else
            {
                var start = currentMonthSyncDateTime.AddMonths(-1);
                var end = currentMonthSyncDateTime.AddDays(-1);
                var totalRevenueInLastMonth = dailyRevenueAnalyticDtos
                    .Where(
                        x => string.CompareOrdinal(x.Date, start.Date.ToString("yyyy-MM-dd")) >= 0 &&
                             string.CompareOrdinal(x.Date, end.ToString("yyyy-MM-dd")) <= 0).Sum(
                        cmsDailyRevenueAnalyticDto => cmsDailyRevenueAnalyticDto.DailyRevenue);

                transactionAmount = Convert.ToInt32(Math.Round(totalRevenueInLastMonth, 2) * 100);
            }

            // Create a new transaction
            var newTransaction = new CreatePartnerStackTransactionRequest(
                cmsPartnerStackCustomerMap.PartnerStackCustomerKey,
                transactionAmount,
                "USD",
                categoryKey,
                productKey);

            newTransactions.Add(newTransaction);
        }

        if (newTransactions.Count == 0)
        {
            return new SyncMrrToPartnerStackDto
            {
                SleekFlowCompanyId = cmsPartnerStackCustomerMap.CompanyId,
                SleekFlowCompanyName = cmsPartnerStackCustomerMap.Company.CompanyName ?? string.Empty,
                Message = "Success: No new MRR to sync"
            };
        }

        var succeedCount = 0;
        var errorCount = 0;
        var errorProductKeys = new List<string>();
        var syncMrrTransactionToPartnerStackResults = new List<SyncMrrTransactionToPartnerStackResult>();

        foreach (var newTransaction in newTransactions)
        {
            var createdTransaction = await _partnerStackIntegrationService.CreatePartnerStackTransaction(
                newTransaction.CustomerExternalKey,
                newTransaction.Amount,
                newTransaction.Currency,
                newTransaction.CategoryKey,
                newTransaction.ProductKey);

            if (createdTransaction == null)
            {
                _logger.LogError(
                    "[PartnerStack {MethodName}] Company ID: {CompanyID}: Failed to sync mrr to PartnerStack, Product Key: {ProductKey}",
                    nameof(SyncMrrToPartnerStack),
                    cmsPartnerStackCustomerMap.CompanyId,
                    newTransaction.ProductKey);

                errorCount++;
                errorProductKeys.Add(newTransaction.ProductKey);
                syncMrrTransactionToPartnerStackResults.Add(
                    new SyncMrrTransactionToPartnerStackResult
                    {
                        IsSuccess = false,
                        PartnerStackTransactionCategoryKey = newTransaction.CategoryKey,
                        PartnerStackTransactionProductKey = newTransaction.ProductKey,
                    });
            }
            else
            {
                succeedCount++;
                syncMrrTransactionToPartnerStackResults.Add(
                    new SyncMrrTransactionToPartnerStackResult
                    {
                        IsSuccess = true,
                        PartnerStackTransactionCategoryKey = newTransaction.CategoryKey,
                        PartnerStackTransactionProductKey = newTransaction.ProductKey,
                    });
            }
        }

        _logger.LogInformation(
            "SyncMrrToPartnerStack Completed:\n" +
            "--------------------------------------------\n" +
            "Total Mrr Synced: {TotalCount}\n" +
            "Number of Sync Succeed: {SucceedCount}\n" +
            "Number of Sync Error: {ErrorCount}\n\n",
            newTransactions.Count,
            succeedCount,
            errorCount);

        _logger.LogInformation(
            "SyncMrrToPartnerStack Sync Error Product Key: {ErrorProductKeys}",
            string.Join(", ", errorProductKeys));

        return new SyncMrrToPartnerStackDto
        {
            SleekFlowCompanyId = cmsPartnerStackCustomerMap.CompanyId,
            SleekFlowCompanyName = cmsPartnerStackCustomerMap.Company.CompanyName ?? string.Empty,
            Message = "Success: MRR sync to PartnerStack completed",
            Results = syncMrrTransactionToPartnerStackResults
        };
    }

    public async Task SyncPartnerStackCustomerKeyFromHubSpotContact(string companyId, string userEmail)
    {
        try
        {
            var company = await _appDbContext.CompanyCompanies
                .FirstOrDefaultAsync(x => x.Id == companyId);

            // Check if the company exists or not deleted
            if (company == null || company.IsDeleted)
            {
                return;
            }

            // Check if the HubSpot Contact exists and has customer key
            var existingContact =
                await _internalHubspotRepository.GetContactObjectByEmailAsync(userEmail);

            if (existingContact == null || string.IsNullOrEmpty(existingContact.CustomerKey))
            {
                return;
            }

            // Check if the customer key already mapped to a company
            var existedPartnerStackCustomerMap = await _appDbContext.CmsPartnerStackCustomerMaps
                .FirstOrDefaultAsync(x => x.PartnerStackCustomerKey == existingContact.CustomerKey);

            if (existedPartnerStackCustomerMap != null)
            {
                _logger.LogWarning(
                    "[PartnerStack {MethodName}] A company is already mapped with PartnerStack customer with customer key: {CustomerKey}",
                    nameof(SyncPartnerStackCustomerKeyFromHubSpotContact),
                    existingContact.CustomerKey);
                return;
            }

            // Check if the Partner Stack customer exists
            var partnerStackCustomer =
                await _partnerStackIntegrationService.RetrievePartnerStackCustomerByCustomerKey(
                    existingContact.CustomerKey);

            if (partnerStackCustomer == null)
            {
                _logger.LogWarning(
                    "[PartnerStack {MethodName}] PartnerStack customer not found with customer key: {CustomerKey}",
                    nameof(SyncPartnerStackCustomerKeyFromHubSpotContact),
                    existingContact.CustomerKey);
                return;
            }

            // Check if the Partner Stack customer is a staff of the company
            var existedCompanyStaff = await _appDbContext.UserRoleStaffs
                .Include(x => x.Identity)
                .FirstOrDefaultAsync(x => x.CompanyId == companyId && x.Identity.Email == partnerStackCustomer.Email);

            if (existedCompanyStaff == null)
            {
                _logger.LogWarning(
                    "[PartnerStack {MethodName}] PartnerStack customer is not a staff in the company with, Customer key: {CustomerKey}, Company ID: {CompanyID}",
                    nameof(SyncPartnerStackCustomerKeyFromHubSpotContact),
                    existingContact.CustomerKey,
                    companyId);
                return;
            }

            // Update the company's Partner Stack customer mapping
            var currentCompanyPartnerStackCustomerMap = await _appDbContext.CmsPartnerStackCustomerMaps
                .FirstOrDefaultAsync(x => x.CompanyId == companyId);

            if (currentCompanyPartnerStackCustomerMap != null)
            {
                currentCompanyPartnerStackCustomerMap.PartnerStackCustomerKey = existingContact.CustomerKey;
                currentCompanyPartnerStackCustomerMap.UpdatedAt = DateTime.UtcNow;
            }
            else
            {
                var newCompanyPartnerStackCustomerMap = new CmsPartnerStackCustomerMap
                {
                    CompanyId = companyId, PartnerStackCustomerKey = existingContact.CustomerKey
                };

                await _appDbContext.CmsPartnerStackCustomerMaps.AddAsync(newCompanyPartnerStackCustomerMap);
            }

            await _appDbContext.SaveChangesAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "[PartnerStack {MethodName}] Sync PartnerStack customer key from HubSpot contact failed, Company ID: {CompanyID}, Email: {Email}: {ExceptionMessage}",
                nameof(SyncPartnerStackCustomerKeyFromHubSpotContact),
                companyId,
                userEmail,
                ex.Message);
        }
    }

    public async Task SyncAllMrrToPartnerStack()
    {
        _logger.LogInformation("SyncAllMrrToPartnerStack Started");
        var cmsPartnerStackCustomerMaps = await _appDbContext.CmsPartnerStackCustomerMaps
            .Include(x => x.Company)
            .ToListAsync();
        var succeedCount = 0;
        var errorCount = 0;
        var errorCompanyIds = new List<string>();
        var syncMrrToPartnerStackResults = new List<SyncMrrToPartnerStackDto>();

        foreach (var cmsPartnerStackCustomerMap in cmsPartnerStackCustomerMaps)
        {
            if (cmsPartnerStackCustomerMap.Company == null ||
                cmsPartnerStackCustomerMap.Company.IsDeleted)
            {
                continue;
            }

            try
            {
                var syncResult = await SyncMrrToPartnerStack(cmsPartnerStackCustomerMap);

                if (syncResult.Message.Contains("Success"))
                {
                    succeedCount++;
                }
                else
                {
                    _logger.LogError(
                        "[PartnerStack {MethodName}] Company ID: {CompanyID}: {ErrorMessage}",
                        nameof(SyncAllMrrToPartnerStack),
                        cmsPartnerStackCustomerMap.CompanyId,
                        syncResult);

                    errorCount++;
                    errorCompanyIds.Add(cmsPartnerStackCustomerMap.CompanyId);
                }

                syncMrrToPartnerStackResults.Add(syncResult);
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[PartnerStack {MethodName}] Company ID: {CompanyID}: {ExceptionMessage}",
                    nameof(SyncAllMrrToPartnerStack),
                    cmsPartnerStackCustomerMap.CompanyId,
                    ex.Message);

                errorCount++;
                errorCompanyIds.Add(cmsPartnerStackCustomerMap.CompanyId);
            }
        }

        _logger.LogInformation(
            "SyncAllMrrToPartnerStack Completed:\n" +
            "--------------------------------------------\n" +
            "Total Company: {TotalCompanyCount}\n" +
            "Number of Sync Succeed: {SucceedCount}\n" +
            "Number of Sync Error: {ErrorCount}\n\n",
            cmsPartnerStackCustomerMaps.Count,
            succeedCount,
            errorCount);

        _logger.LogInformation(
            "SyncAllMrrToPartnerStack Sync Error Company: {ErrorCompanyIds}",
            string.Join(", ", errorCompanyIds));

        var serverLocation = _configuration["SF_ENVIRONMENT"] ?? string.Empty;

        BackgroundJob.Enqueue<IEmailNotificationService>(
            x => x.SendSystemAlertToSlackChannel(
                $"[PartnerStack] Monthly Sync MRR to PartnerStack Completed (Server: {serverLocation})",
                GetPartnerStackNotificationEmailMessage(
                    cmsPartnerStackCustomerMaps.Count,
                    succeedCount,
                    errorCount,
                    string.Join(", ", errorCompanyIds),
                    syncMrrToPartnerStackResults),
                "partner-stack",
                "notification",
                null,
                false));
    }

    public async Task SyncPartnerStackPartnerKeyFromHubSpotContact(string companyId, string userEmail)
    {
        try
        {
            var resellerProfile = await _appDbContext.ResellerCompanyProfiles
                .Include(x => x.Company)
                .FirstOrDefaultAsync(x => x.CompanyId == companyId);

            // Check if the reseller profile exists
            if (resellerProfile == null)
            {
                _logger.LogWarning(
                    "[PartnerStack {MethodName}] Reseller profile not found with Company ID: {CompanyID}",
                    nameof(SyncPartnerStackPartnerKeyFromHubSpotContact),
                    companyId);
                return;
            }

            // Check if the company exists or not deleted
            if (resellerProfile.Company == null || resellerProfile.Company.IsDeleted)
            {
                _logger.LogWarning(
                    "[PartnerStack {MethodName}] Company not found with ID: {CompanyID}",
                    nameof(SyncPartnerStackPartnerKeyFromHubSpotContact),
                    companyId);
                return;
            }

            // Check if the HubSpot Contact exists
            var existingContact =
                await _internalHubspotRepository.GetContactObjectByEmailAsync(userEmail);

            if (existingContact == null)
            {
                _logger.LogWarning(
                    "[PartnerStack {MethodName}] HubSpot Contact not found with email: {Email}",
                    nameof(SyncPartnerStackPartnerKeyFromHubSpotContact),
                    userEmail);
                return;
            }

            // Check if HubSpot Contact has a partner key
            if (string.IsNullOrEmpty(existingContact.PartnerStackPartnerKey))
            {
                _logger.LogWarning(
                    "[PartnerStack {MethodName}] HubSpot Contact partner key is empty with contact id: {ContactID}",
                    nameof(SyncPartnerStackPartnerKeyFromHubSpotContact),
                    existingContact.Id);
                return;
            }

            // Check if the partner key already mapped to a company
            var existedPartnerStackPartnerResellerProfile = await _appDbContext.ResellerCompanyProfiles
                .FirstOrDefaultAsync(x => x.PartnerStackPartnerKey == existingContact.PartnerStackPartnerKey);

            if (existedPartnerStackPartnerResellerProfile != null)
            {
                _logger.LogWarning(
                    "[PartnerStack {MethodName}] A company is already mapped with PartnerStack partner with partner key: {PartnerKey}",
                    nameof(SyncPartnerStackPartnerKeyFromHubSpotContact),
                    existingContact.PartnerStackPartnerKey);
                return;
            }

            // Check if the Partner Stack partnership exists
            var partnerStackPartnership =
                await _partnerStackIntegrationService.RetrievePartnerStackPartnershipByUniqueIdentifier(
                    existingContact.PartnerStackPartnerKey);

            if (partnerStackPartnership == null)
            {
                _logger.LogWarning(
                    "[PartnerStack {MethodName}] PartnerStack partnership not found with partner key: {PartnerKey}",
                    nameof(SyncPartnerStackPartnerKeyFromHubSpotContact),
                    existingContact.PartnerStackPartnerKey);
                return;
            }

            // Check if the Partner Stack partner is a staff of the company
            var existedCompanyStaff = await _appDbContext.UserRoleStaffs
                .Include(x => x.Identity)
                .FirstOrDefaultAsync(x => x.CompanyId == companyId && x.Identity.Email == existingContact.Email);

            if (existedCompanyStaff == null)
            {
                _logger.LogWarning(
                    "[PartnerStack {MethodName}] PartnerStack partner is not a staff in the company with, Partner key: {PartnerKey}, Company ID: {CompanyID}",
                    nameof(SyncPartnerStackPartnerKeyFromHubSpotContact),
                    existingContact.PartnerStackPartnerKey,
                    companyId);
                return;
            }

            // Update the company's Partner Stack partnership key
            resellerProfile.PartnerStackPartnerKey = existingContact.PartnerStackPartnerKey;
            resellerProfile.UpdatedAt = DateTime.UtcNow;

            await _appDbContext.SaveChangesAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "[PartnerStack {MethodName}] Sync PartnerStack partner key from HubSpot contact failed, Company ID: {CompanyID}, Email: {Email}: {ExceptionMessage}",
                nameof(SyncPartnerStackPartnerKeyFromHubSpotContact),
                companyId,
                userEmail,
                ex.Message);
        }
    }

    public async Task<ResponseWrapper> PowerflowUpdatePartnerStackIndividualCommissionConfig(
        string companyId,
        string syncType,
        int? individualCommissionRate,
        string commissionEndDate)
    {
        var response = new ResponseWrapper
        {
            IsSuccess = false
        };

        try
        {
            var company = await _appDbContext.CompanyCompanies
                .FirstOrDefaultAsync(x => x.Id == companyId);

            if (company == null || company.IsDeleted)
            {
                response.ErrorMsg = "Error: Company not found or deleted";
                return response;
            }

            // Check if the company got Partner Stack customer mapping
            var existedPartnerStackCustomerMap = await _appDbContext.CmsPartnerStackCustomerMaps
                .FirstOrDefaultAsync(x => x.CompanyId == companyId);

            if (existedPartnerStackCustomerMap == null)
            {
                response.ErrorMsg = "Error: Company does not have Partner Stack customer mapping";
                return response;
            }

            var newIndividualCommissionConfig = new IndividualCommissionConfig
            {
                SyncType = PartnerStackConstants.GetAllSyncType.Contains(syncType)
                    ? syncType
                    : PartnerStackConstants.MrrSyncType,
                IndividualCommissionRate = individualCommissionRate,
                CommissionEndDate = !string.IsNullOrWhiteSpace(commissionEndDate)
                    ? DateTime.Parse(commissionEndDate, CultureInfo.InvariantCulture)
                    : null
            };

            existedPartnerStackCustomerMap.IndividualCommissionConfig = newIndividualCommissionConfig;
            existedPartnerStackCustomerMap.UpdatedAt = DateTime.UtcNow;

            await _appDbContext.SaveChangesAsync();

            response.IsSuccess = true;
            response.Data = _mapper.Map<CmsPartnerStackCustomerMapDto>(existedPartnerStackCustomerMap);
            return response;
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "[PartnerStack {MethodName}] Update Partner Stack individual commission config failed, Company ID: {CompanyID}: {ExceptionMessage}",
                nameof(PowerflowUpdatePartnerStackIndividualCommissionConfig),
                companyId,
                ex.Message);

            response.ErrorMsg = "Error: Failed to update Partner Stack individual commission config";
            return response;
        }
    }

    public async Task<ResponseWrapper> SyncPartnerStackPartnerInformation(
        string companyId)
    {
        var response = new ResponseWrapper
        {
            IsSuccess = false
        };

        try
        {
            // Check if the company got Partner Stack customer mapping
            var existedPartnerStackCustomerMap = await _appDbContext.CmsPartnerStackCustomerMaps
                .FirstOrDefaultAsync(x => x.CompanyId == companyId);

            if (existedPartnerStackCustomerMap == null)
            {
                response.ErrorMsg = "Error: Company does not have Partner Stack customer mapping";
                return response;
            }

            // Check if the Partner Stack customer exists
            var partnerStackCustomer =
                await _partnerStackIntegrationService.RetrievePartnerStackCustomerByCustomerKey(
                    existedPartnerStackCustomerMap.PartnerStackCustomerKey);

            if (partnerStackCustomer == null)
            {
                response.ErrorMsg = "Error: PartnerStack customer not found";
                return response;
            }

            // Check if the Partner Stack partner exists
            var partnerStackPartner =
                await _partnerStackIntegrationService.RetrievePartnerStackPartnershipByUniqueIdentifier(
                    partnerStackCustomer.PartnerKey);

            if (partnerStackPartner == null)
            {
                response.ErrorMsg = "Error: PartnerStack partner not found";
                return response;
            }

            var newPartnerStackPartnerInformation = new PartnerStackPartnerInformation
            {
                PartnerKey = partnerStackPartner.PartnerKey,
                GroupName = partnerStackPartner.Group.Name,
                TeamName = partnerStackPartner.Team.Name
            };

            existedPartnerStackCustomerMap.PartnerStackPartnerInformation = newPartnerStackPartnerInformation;
            existedPartnerStackCustomerMap.UpdatedAt = DateTime.UtcNow;

            await _appDbContext.SaveChangesAsync();

            response.IsSuccess = true;
            response.Data = _mapper.Map<CmsPartnerStackCustomerMapDto>(existedPartnerStackCustomerMap);
            return response;
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "[PartnerStack {MethodName}] Sync Partner Stack partner information failed, Company ID: {CompanyID}: {ExceptionMessage}",
                nameof(SyncPartnerStackPartnerInformation),
                companyId,
                ex.Message);

            response.ErrorMsg = "Error: Failed to sync Partner Stack partner information";
            return response;
        }
    }

    private static List<int> GetCurrentQuarterMonths(DateTime dateTime)
    {
        return dateTime.Month switch
        {
            1 or 2 or 3 => new List<int>
            {
                1, 2, 3
            },
            4 or 5 or 6 => new List<int>
            {
                4, 5, 6
            },
            7 or 8 or 9 => new List<int>
            {
                7, 8, 9
            },
            10 or 11 or 12 => new List<int>
            {
                10, 11, 12
            },
            _ => new List<int>()
        };
    }

    private static string GetCurrentMonthlySyncCategoryKey(
        string syncType,
        int? individualCommissionRate)
    {
        var baseCategoryKey = syncType == PartnerStackConstants.MrrSyncType
            ? PartnerStackConstants.MonthlyMrrSyncCategoryKey
            : PartnerStackConstants.OneTimeOffSyncCategoryKey;

        var categoryKey = !individualCommissionRate.HasValue
            ? baseCategoryKey
            : $"{baseCategoryKey}_Commission_{individualCommissionRate.Value}%";

        return categoryKey;
    }

    private static string GetCurrentMonthlySyncProductKey(string syncType, string date)
    {
        var baseProductKey = syncType == PartnerStackConstants.MrrSyncType
            ? PartnerStackConstants.MonthlyMrrSyncCategoryKey
            : PartnerStackConstants.OneTimeOffSyncCategoryKey;

        return $"{baseProductKey}_{date}";
    }

    private static string GetPartnerStackNotificationEmailMessage(
        int totalCompanySynced,
        int succeedCount,
        int errorCount,
        string errorCompanyIds,
        List<SyncMrrToPartnerStackDto> syncMrrToPartnerStackResults)
    {
        var sb = new StringBuilder();

        sb.Append(
            "<div>" +
            "<span style='color:#0f6593;'><b>Monthly Sync MRR to PartnerStack Completed:</b></span><br/>" +
            "<hr />" +
            $"<span style='color:#0f103d;'>Total Company Synced: <b>{totalCompanySynced}</b></span><br/>" +
            $"<span style='color:#0f103d;'>Number of Sync Succeed: <b>{succeedCount}</b></span><br/>" +
            $"<span style='color:#0f103d;'>Number of Sync Error: <b>{errorCount}</b></span><br/>" +
            "<hr />" +
            $"<div style='color:#0f103d;'>Sync Error Company IDs: {errorCompanyIds}</div><br/>" +
            "</div>");

        if (syncMrrToPartnerStackResults.Count == 0)
        {
            return sb.ToString();
        }

        sb.Append("<div>");

        foreach (var syncResult in syncMrrToPartnerStackResults)
        {
            sb.Append(
                "<div>" +
                "<span style='color:#0f6593;'><b>Company Info</b></span><br/>" +
                $"<span style='color:#0f103d;'>Company Id: <b>{syncResult.SleekFlowCompanyId}</b></span><br/>" +
                $"<span style='color:#0f103d;'>Company Name: <b>{syncResult.SleekFlowCompanyName}</b></span><br/>" +
                $"<span style='color:#0f103d;'>Open In: <a clicktracking=off href='https://powerflow.sleekflow.io/companies/detail/{syncResult.SleekFlowCompanyId}' target='_blank'>Powerflow</a><br/>" +
                $"<span style='color:#0f103d;'>Sync Result: <b>{syncResult.Message}</b></span><br/>");

            if (syncResult.Results == null || syncResult.Results.Count == 0)
            {
                sb.Append(
                    "</div>" +
                    "<hr />");
                continue;
            }

            sb.Append(
                "<span style='color:#0f103d;'>Sync PartnerStack Transactions Results: </span><br/>" +
                "<table style='border-collapse: collapse; width: 100%;'>" +
                "<tr>" +
                "<th style='border: 1px solid #dddddd; text-align: left; padding: 4px;'>PartnerStack Category Key</th>" +
                "<th style='border: 1px solid #dddddd; text-align: left; padding: 4px;'>PartnerStack Product Key</th>" +
                "<th style='border: 1px solid #dddddd; text-align: left; padding: 4px;'>Result</th>" +
                "</tr>");

            foreach (var syncMrrTransactionResult in syncResult.Results)
            {
                var isSuccessString = syncMrrTransactionResult.IsSuccess ? "Succeed" : "Failed";

                sb.Append(
                    "<tr>" +
                    $"<td style='border: 1px solid #dddddd; text-align: left; padding: 4px;'>{syncMrrTransactionResult.PartnerStackTransactionCategoryKey}</td>" +
                    $"<td style='border: 1px solid #dddddd; text-align: left; padding: 4px;'>{syncMrrTransactionResult.PartnerStackTransactionProductKey}</td>" +
                    $"<td style='border: 1px solid #dddddd; text-align: left; padding: 4px;'>{isSuccessString}</td>" +
                    "</tr>");
            }

            sb.Append(
                "</table>" +
                "</div>" +
                "<hr />");
        }

        sb.Append("</div>");

        return sb.ToString();
    }
}