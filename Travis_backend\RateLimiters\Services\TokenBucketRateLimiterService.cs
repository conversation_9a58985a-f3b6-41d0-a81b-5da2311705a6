﻿using System;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using StackExchange.Redis;
using Travis_backend.Cache.Models;
using Travis_backend.Constants;
using Travis_backend.RateLimiters.Models;

namespace Travis_backend.RateLimiters.Services;

public class TokenBucketRateLimiterService : IRateLimiterService
{
    private readonly IConnectionMultiplexer _connectionMultiplexer;
    private readonly ILogger<TokenBucketRateLimiterService> _logger;
    private readonly IRateLimiterOptionService _rateLimiterOptionService;

    private static LuaScript AllowLimitScript => LuaScript.Prepare(
        """
        local current_time = redis.call('TIME')
        local trim_time = tonumber(current_time[1]) - @window
        redis.call('ZREMRANGEBYSCORE', @key, 0, trim_time)
        local request_count = redis.call('ZCARD',@key)

        if request_count < tonumber(@max_requests) then
            redis.call('ZADD', @key, current_time[1], current_time[1] .. current_time[2])
            redis.call('EXPIRE', @key, @window)
            return 0
        end
        return 1
        """);

    private static LuaScript RemainingLimitScript => LuaScript.Prepare(
        """
        local current_time = redis.call('TIME')
        local trim_time = tonumber(current_time[1]) - @window
        redis.call('ZREMRANGEBYSCORE', @key, 0, trim_time)
        local request_count = redis.call('ZCARD', @key)
        local max_requests = tonumber(@max_requests)
        local remaining_requests = max_requests - request_count
        if request_count < max_requests then
            redis.call('ZADD', @key, current_time[1], current_time[1] .. current_time[2])
            redis.call('EXPIRE', @key, @window)
            return remaining_requests - 1
        end
        return remaining_requests
        """);

    public TokenBucketRateLimiterService(
        IConnectionMultiplexer connectionMultiplexer,
        ILogger<TokenBucketRateLimiterService> logger,
        IRateLimiterOptionService rateLimiterOptionService)
    {
        _connectionMultiplexer = connectionMultiplexer;
        _logger = logger;
        _rateLimiterOptionService = rateLimiterOptionService;
        _rateLimiterOptionService.InitializeLimiterOption(RateLimiterType.TokenBucket);
    }

    public async Task<bool> AllowRequestAsync(
        string userId,
        RequestInfo requestInfo,
        IRateLimiterOption rateLimiterOption)
    {
        var tokenBucketLimiterOption = (TokenBucketLimiterOption) rateLimiterOption;
        if (tokenBucketLimiterOption.WindowSeconds <= 0
            || tokenBucketLimiterOption.MaxRequestsAllowedWithinWindow <= 0)
        {
            return false;
        }

        var requestKey = $"{CachePrefixType.RateLimit}-{userId}-{requestInfo.Method}-{requestInfo.Path}";
        var isHitLimit = false;

        try
        {
            var database = _connectionMultiplexer.GetDatabase();

            var result = (int) await database.ScriptEvaluateAsync(
                AllowLimitScript,
                new
                {
                    key = requestKey,
                    window = tokenBucketLimiterOption.WindowSeconds,
                    max_requests = tokenBucketLimiterOption.MaxRequestsAllowedWithinWindow
                });

            isHitLimit = result == 1;
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "[Evaluate Lua Script] error occured when evaluate Token Limit Lua Script: {Message}",
                ex.ToString());
        }

        return !isHitLimit;
    }

    public async Task<int> GetRemainingLimitAsync(
        string userId,
        RequestInfo requestInfo,
        IRateLimiterOption rateLimiterOption)
    {
        var tokenBucketLimiterOption = (TokenBucketLimiterOption) rateLimiterOption;
        if (tokenBucketLimiterOption.WindowSeconds <= 0
            || tokenBucketLimiterOption.MaxRequestsAllowedWithinWindow <= 0)
        {
            return 0;
        }

        var requestKey = $"{CachePrefixType.RateLimit}-{userId}-{requestInfo.Method}-{requestInfo.Path}";

        try
        {
            var database = _connectionMultiplexer.GetDatabase();

            var result = (int) await database.ScriptEvaluateAsync(
                RemainingLimitScript,
                new
                {
                    key = requestKey,
                    window = tokenBucketLimiterOption.WindowSeconds,
                    max_requests = tokenBucketLimiterOption.MaxRequestsAllowedWithinWindow
                });

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "[Evaluate Lua Script] error occured when evaluate Token Limit Lua Script: {Message}",
                ex.ToString());
        }

        return 0;
    }
}