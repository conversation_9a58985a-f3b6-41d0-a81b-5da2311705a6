using System;
using System.Collections.Generic;

namespace Travis_backend.SubscriptionPlanDomain.Models;

public class GetAddOnsForSubscriptionPlanAddOnDetailsResponseModel
{
    [Obsolete("Use Options.Id Instead.")]
    public string Id { get; set; }

    public string Type { get; set; }

    [Obsolete("Use Options.Amount Instead.")]
    public double Amount { get; set; }

    public string Currency { get; set; }

    public string SubscriptionInterval { get; set; }

    [Obsolete("Use Options.QuantityPerUnit Instead.")]
    public int BaseQuantity { get; set; }

    [Obsolete("Use DefaultPurchaseUnit Instead.")]
    public int DefaultPurchaseQuantity { get; set; }

    public int DefaultPurchaseUnit { get; set; }

    [Obsolete("Use Options.PurchasableUnit Instead.")]
    public long? MaxPurchaseQuantity { get; set; }

    public IEnumerable<GetAddOnsForSubscriptionPlanAddOnOptionResponseModel> Options { get; set; }
}