#nullable enable

using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Sleekflow.Apis.InternalIntegrationHub.Api;
using Sleekflow.Apis.InternalIntegrationHub.Model;

namespace Travis_backend.InternalIntegrationHubDomain.Services;

public interface IInternalIntegrationService
{
    public Task CreateCustomerAsync(
        string companyId,
        string companyName,
        string companyCountry,
        string? companyOwnerEmail,
        string? companyOwnerPhone,
        string? salesRepEmail);

    public Task UpdateCustomerAsync(
        string companyId,
        string companyName,
        string companyCountry,
        string? companyOwnerEmail,
        string? companyOwnerPhone,
        string? salesRepEmail);

    public Task CreateInvoiceAsync(
        string billRecordId,
        string companyId,
        decimal subscriptionFee,
        decimal oneTimeSetupFee,
        decimal whatsappCreditAmount,
        DateTime? subscriptionStartDate,
        DateTime? subscriptionEndDate,
        int? paymentTerm,
        string currency = "USD");

    public Task CreatePaymentFromStripeAsync(
        string companyId,
        string currency,
        List<BillItem> items);
}

public class InternalIntegrationService : IInternalIntegrationService
{
    private readonly INetSuiteInternalApi _netSuiteApi;

    public InternalIntegrationService(INetSuiteInternalApi netSuiteApi)
    {
        _netSuiteApi = netSuiteApi;
    }

    public async Task CreateCustomerAsync(
        string companyId,
        string companyName,
        string companyCountry,
        string? companyOwnerEmail,
        string? companyOwnerPhone,
        string? salesRepEmail)
    {
        var input = new CreateCustomerInput(
            companyId,
            companyName,
            companyCountry,
            companyOwnerEmail,
            companyOwnerPhone,
            salesRepEmail);
        await _netSuiteApi.NetSuiteInternalCreateCustomerPostAsync(createCustomerInput: input);
    }

    public async Task UpdateCustomerAsync(
        string companyId,
        string companyName,
        string companyCountry,
        string? companyOwnerEmail,
        string? companyOwnerPhone,
        string? salesRepEmail)
    {
        var input = new UpdateCustomerInput(
            companyId,
            companyName,
            companyCountry,
            companyOwnerEmail,
            companyOwnerPhone,
            salesRepEmail);
        await _netSuiteApi.NetSuiteInternalUpdateCustomerPostAsync(updateCustomerInput: input);
    }

    public async Task CreateInvoiceAsync(
        string billRecordId,
        string companyId,
        decimal subscriptionFee,
        decimal oneTimeSetupFee,
        decimal whatsappCreditAmount,
        DateTime? subscriptionStartDate,
        DateTime? subscriptionEndDate,
        int? paymentTerm,
        string currency = "USD")
    {
        var input = new CreateInvoiceInput(
            billRecordId,
            companyId,
            subscriptionFee,
            oneTimeSetupFee,
            whatsappCreditAmount,
            subscriptionStartDate,
            subscriptionEndDate,
            paymentTerm,
            currency);
        await _netSuiteApi.NetSuiteInternalCreateInvoicePostAsync(createInvoiceInput: input);
    }

    public async Task CreatePaymentFromStripeAsync(string companyId, string currency, List<BillItem> items)
    {
        var input = new CreatePaymentFromStripeInput(
            items,
            currency,
            companyId);
        await _netSuiteApi.NetSuiteInternalCreatePaymentFromStripePostAsync(createPaymentFromStripeInput: input);
    }
}