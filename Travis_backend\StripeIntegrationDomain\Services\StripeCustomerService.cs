using System.Threading.Tasks;
using Stripe;

namespace Travis_backend.StripeIntegrationDomain.Services;

/// <inheritdoc />
public class StripeCustomerService : IStripeCustomerService
{
    /// <summary>
    /// Stripe's Customer Service.
    /// </summary>
    private readonly CustomerService _customerService;

    /// <summary>
    /// Initializes a new instance of the <see cref="StripeCustomerService"/> class.
    /// </summary>
    /// <param name="stripeServicesFactory">IStripeServicesFactory.</param>
    public StripeCustomerService(IStripeServicesFactory stripeServicesFactory)
    {
        _customerService = stripeServicesFactory.CreateCustomerService();
    }

    /// <inheritdoc />
    public async Task<string> CreateAsync(string email, string invoiceFooter)
    {
        var createCustomerOptions = new CustomerCreateOptions
        {
            Email = email,
            InvoiceSettings = new CustomerInvoiceSettingsOptions
            {
                Footer = invoiceFooter
            }
        };

        var customer = await _customerService.CreateAsync(createCustomerOptions);

        return customer.Id;
    }
}