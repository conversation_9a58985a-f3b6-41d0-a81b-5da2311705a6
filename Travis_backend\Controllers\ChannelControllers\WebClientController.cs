﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Travis_backend.AccountAuthenticationDomain.Models;
using Travis_backend.Cache;
using Travis_backend.Cache.Models.CacheKeyPatterns;
using Travis_backend.ChannelDomain.ViewModels;
using Travis_backend.CommonDomain.ViewModels;
using Travis_backend.CompanyDomain.Services;
using Travis_backend.Constants;
using Travis_backend.ContactDomain.Constants;
using Travis_backend.ContactDomain.Models;
using Travis_backend.ContactDomain.Services;
using Travis_backend.ContactDomain.ViewModels;
using Travis_backend.ConversationDomain.Models;
using Travis_backend.ConversationDomain.ViewModels;
using Travis_backend.ConversationServices;
using Travis_backend.Database;
using Travis_backend.Enums;
using Travis_backend.Helpers;
using Travis_backend.Infrastructures.Attributes;
using Travis_backend.MessageDomain.Models;
using Travis_backend.MessageDomain.ViewModels;
using Travis_backend.SignalR;

namespace Travis_backend.Controllers.ChannelControllers
{
    public class WebClientController : Controller
    {
        private readonly ApplicationDbContext _appDbContext;
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly SignInManager<ApplicationUser> _signInManager;
        private readonly IMapper _mapper;
        private readonly ILogger _logger;
        private readonly IConfiguration _configuration;
        private readonly IConversationMessageService _conversationMessageService;
        private readonly ICompanyService _companyService;
        private readonly IUserProfileService _userProfileService;
        private readonly ISignalRService _signalRService;
        private readonly IAzureBlobStorageService _azureBlobStorageService;
        private readonly ICacheManagerService _cacheManagerService;
        private readonly ILockService _lockService;

        public WebClientController(
            ApplicationDbContext appDbContext,
            UserManager<ApplicationUser> userManager,
            SignInManager<ApplicationUser> signInManager,
            IMapper mapper,
            IConfiguration configuration,
            ILogger<WebClientController> logger,
            IConversationMessageService messagingService,
            ICompanyService companyService,
            IUserProfileService userProfileService,
            ISignalRService signalRService,
            IAzureBlobStorageService azureBlobStorageService,
            ICacheManagerService cacheManagerService,
            ILockService lockService)
        {
            _userManager = userManager;
            _signInManager = signInManager;
            _appDbContext = appDbContext;
            _mapper = mapper;
            _configuration = configuration;
            _logger = logger;
            _conversationMessageService = messagingService;
            _companyService = companyService;
            _userProfileService = userProfileService;
            _signalRService = signalRService;
            _azureBlobStorageService = azureBlobStorageService;
            _cacheManagerService = cacheManagerService;
            _lockService = lockService;
        }

        [HttpPost]
        [Route("WebClient/Init")]
        public async Task<ActionResult<WebClientInitResponse>> InitWebClient([FromBody] WebClientViewModel webClientViewModel)
        {
            if (!ModelState.IsValid)
            {
                _logger.LogWarning(
                    "InitWebClient ModelState Error: {ModelState}",
                    JsonConvert.SerializeObject(ModelState));

                return BadRequest(ModelState);
            }

            if (webClientViewModel == null)
            {
                _logger.LogWarning(
                    "InitWebClient payload is null: {RequestHost} {RequestPath} {RequestQueryString}",
                    Request.Host,
                    Request.Path,
                    Request.QueryString);

                return BadRequest(
                    new ResponseViewModel
                    {
                        message = "payload is null"
                    });
            }

            var initWebClientCacheKeyPattern = new InitWebClientCacheKeyPattern(webClientViewModel.CompanyId, webClientViewModel.WebClientUUID);
            var lockKey = $"InitWebClient_{initWebClientCacheKeyPattern.GenerateKeyPattern()}";
            var cachedData = await _cacheManagerService.GetCacheAsync(initWebClientCacheKeyPattern);

            //Return cached data directly
            if (!string.IsNullOrEmpty(cachedData))
            {
                return JsonConvert.DeserializeObject<WebClientInitResponse>(cachedData);
            }

            if (!await _appDbContext.CompanyCompanies.AnyAsync(x => x.Id == webClientViewModel.CompanyId))
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        message = "no company found."
                    });
            }

            try
            {
                var firstInit = false;

                var webClient = _mapper.Map<WebClientSender>(webClientViewModel);
                webClient.IPAddress = HttpContext.Connection.RemoteIpAddress?.ToString();
                var company = await _appDbContext.CompanyCompanies.Where(x => x.Id == webClientViewModel.CompanyId)
                    .FirstOrDefaultAsync();

                ILockService.Lock myLock = null;

                myLock = await _lockService.AcquireLockAsync(
                    $"lock:InitWebClient_{lockKey}",
                    TimeSpan.FromSeconds(5));

                if (myLock == null)
                {
                    await Task.Delay(5000);
                    cachedData = await _cacheManagerService.GetCacheAsync(initWebClientCacheKeyPattern);

                    // Return cached data directly
                    if (!string.IsNullOrEmpty(cachedData))
                    {
                        return JsonConvert.DeserializeObject<WebClientInitResponse>(cachedData);
                    }

                    _logger.LogWarning("Init WebClient locked: {CacheKey}", lockKey);

                    return BadRequest(new ResponseViewModel()
                    {
                        message = $"Init WebClient locked: {lockKey}"
                    });
                }

                var existingClient = await _appDbContext.SenderWebClientSenders
                    .Where(x => x.WebClientUUID == webClient.WebClientUUID && x.CompanyId == company.Id)
                    .Include(x => x.IPAddressInfos).FirstOrDefaultAsync();

                if (existingClient == null)
                {
                    existingClient = webClient;
                    existingClient.Name = webClient.Name;
                    await _appDbContext.SenderWebClientSenders.AddAsync(existingClient);
                    await _appDbContext.SaveChangesAsync();
                    firstInit = true;
                }

                existingClient.SignalRConnectionId = webClient.SignalRConnectionId;
                existingClient.locale = webClient.Name;
                existingClient.CompanyId = webClient.CompanyId;
                existingClient.Device = webClient.Device;
                existingClient.DeviceModel = webClient.DeviceModel;
                existingClient.IPAddress = webClient.IPAddress;
                existingClient.UpdatedAt = DateTime.UtcNow;
                existingClient.OnlineStatus = OnlineStatus.Online;

                await _signalRService.SendLiveChatOnlineStatus(_mapper.Map<WebClientResponse>(existingClient));

                if (!existingClient.CreatedAt.HasValue)
                {
                    existingClient.CreatedAt = DateTime.UtcNow;
                }

                await _appDbContext.SaveChangesAsync();
                if (webClientViewModel.IPAddressInfo != null &&
                    !string.IsNullOrEmpty(webClientViewModel.IPAddressInfo.WebPath))
                {
                    webClientViewModel.IPAddressInfo.IPAddressType =
                        HttpContext.Connection.RemoteIpAddress?.AddressFamily switch
                        {
                            System.Net.Sockets.AddressFamily.InterNetwork => "IPv4",
                            System.Net.Sockets.AddressFamily.InterNetworkV6 => "IPv6",
                            _ => webClientViewModel.IPAddressInfo.IPAddressType
                        };

                    webClientViewModel.IPAddressInfo.IPAddress = existingClient.IPAddress;
                    if (!await _appDbContext.SenderWebClientIPAddressInfos
                            .AnyAsync(x => x.WebClientSenderId == existingClient.Id)
                        || await _appDbContext.SenderWebClientIPAddressInfos
                            .Where(x => x.WebClientSenderId == existingClient.Id)
                            .OrderByDescending(x => x.Id)
                            .Take(1)
                            .AnyAsync(
                                x =>
                                    x.IPAddress != existingClient.IPAddress
                                    || x.WebPath != webClientViewModel.IPAddressInfo.WebPath))
                    {
                        existingClient.IPAddressInfos.Add(webClientViewModel.IPAddressInfo);
                        await _appDbContext.SaveChangesAsync();

                        // Webpath changed
                        await _signalRService.SignalROnWebclientInfoAdded(
                            webClientViewModel.WebClientUUID,
                            webClientViewModel.IPAddressInfo);
                    }
                }

                await _appDbContext.SaveChangesAsync();
                var responseVm = new WebClientInitResponse
                {
                    WebClient = _mapper.Map<WebClientResponse>(existingClient)
                };

                await _cacheManagerService.SaveCacheAsync(initWebClientCacheKeyPattern,  responseVm, TimeSpan.FromMinutes(1));

                if (!firstInit)
                {
                    return Ok(responseVm);
                }

                var userProfile = new UserProfile
                {
                    CompanyId = company.Id,
                    WebClient = existingClient,
                    FirstName = existingClient.Name,
                    ActiveStatus = ActiveStatus.Inactive
                };

                var conversation = new Conversation
                {
                    CompanyId = company.Id,
                    MessageGroupName = company.SignalRGroupName,
                    WebClient = existingClient,
                    UserProfile = userProfile,
                    ActiveStatus = ActiveStatus.Inactive
                };

                _appDbContext.UserProfiles.Add(userProfile);
                _appDbContext.Conversations.Add(conversation);
                await _appDbContext.SaveChangesAsync();

                return Ok(responseVm);
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Cannot init webclient: {ExceptionMessage}: Payload: {Payload}",
                    ex.Message,
                    JsonConvert.SerializeObject(webClientViewModel));

                return BadRequest(ex);
            }
        }

        [HttpPost]
        [Route("WebClient/track")]
        public async Task<IActionResult> RecordWebClientFootprint([FromBody] WebClientViewModel webClientViewModel)
        {
            return Ok();

            // if (!ModelState.IsValid)
            // {
            //     _logger.LogWarning(
            //         "WebClient/track ModelState Error: {ModelState}",
            //         JsonConvert.SerializeObject(ModelState));
            //
            //     return BadRequest(ModelState);
            // }
            //
            // if (webClientViewModel == null)
            // {
            //     _logger.LogWarning(
            //         "WebClient/track payload is null: {RequestHost} {RequestPath} {RequestQueryString}",
            //         Request.Host,
            //         Request.Path,
            //         Request.QueryString);
            //
            //     return BadRequest(broadcast_statistics
            //         new ResponseViewModel
            //         {
            //             message = "payload is null"
            //         });
            // }
            //
            // if (!await _appDbContext.CompanyCompanies.AnyAsync(x => x.Id == webClientViewModel.CompanyId))
            // {
            //     return BadRequest(
            //         new ResponseViewModel
            //         {
            //             message = "no company found."
            //         });
            // }
            //
            // if (!await _appDbContext.SenderWebClientSenders.AnyAsync(
            //         x => x.CompanyId == webClientViewModel.CompanyId &&
            //              x.WebClientUUID == webClientViewModel.WebClientUUID))
            // {
            //     return BadRequest(
            //         new ResponseViewModel
            //         {
            //             message = "no webclient found."
            //         });
            // }
            //
            // try
            // {
            //     var webClient = _mapper.Map<WebClientSender>(webClientViewModel);
            //     webClient.IPAddress = HttpContext.Connection.RemoteIpAddress?.ToString();
            //
            //     var existingClientId = await _appDbContext.SenderWebClientSenders
            //         .Where(x => x.WebClientUUID == webClient.WebClientUUID && x.CompanyId == webClient.CompanyId)
            //         .Select(x => x.Id).FirstOrDefaultAsync();
            //
            //     if (webClientViewModel.IPAddressInfo != null &&
            //         !string.IsNullOrEmpty(webClientViewModel.IPAddressInfo.WebPath))
            //     {
            //         webClientViewModel.IPAddressInfo.IPAddressType =
            //             HttpContext.Connection.RemoteIpAddress?.AddressFamily switch
            //             {
            //                 System.Net.Sockets.AddressFamily.InterNetwork => "IPv4",
            //                 System.Net.Sockets.AddressFamily.InterNetworkV6 => "IPv6",
            //                 _ => webClientViewModel.IPAddressInfo.IPAddressType
            //             };
            //
            //         webClientViewModel.IPAddressInfo.IPAddress = webClient.IPAddress;
            //         if (!await _appDbContext.SenderWebClientIPAddressInfos
            //                 .AnyAsync(x => x.WebClientSenderId == existingClientId)
            //             || await _appDbContext.SenderWebClientIPAddressInfos
            //                 .Where(x => x.WebClientSenderId == existingClientId)
            //                 .OrderByDescending(x => x.Id)
            //                 .Take(1)
            //                 .AnyAsync(
            //                     x =>
            //                         x.IPAddress != webClient.IPAddress
            //                         || x.WebPath != webClientViewModel.IPAddressInfo.WebPath))
            //         {
            //             webClientViewModel.IPAddressInfo.WebClientSenderId = existingClientId;
            //             await _appDbContext.SenderWebClientIPAddressInfos.AddAsync(webClientViewModel.IPAddressInfo);
            //             await _appDbContext.SaveChangesAsync();
            //
            //             // Webpath changed
            //             await _signalRService.SignalROnWebclientInfoAdded(
            //                 webClientViewModel.WebClientUUID,
            //                 webClientViewModel.IPAddressInfo);
            //         }
            //     }
            //
            //     var responseVm = new WebClientInitResponse
            //     {
            //         WebClient = _mapper.Map<WebClientResponse>(webClient)
            //     };
            //
            //     return Ok(responseVm);
            // }
            // catch (Exception ex)
            // {
            //     _logger.LogError(
            //         ex,
            //         "Cannot track webclient: {ExceptionMessage}: Payload: {Payload}",
            //         ex.Message,
            //         JsonConvert.SerializeObject(webClientViewModel));
            //
            //     return BadRequest(ex);
            // }
        }

        [HttpPost]
        [Route("WebClient/{companyId}/popup/{webClientUUID}")]
        public async Task<IActionResult> PopupMessage(
            string companyId,
            string webClientUUID,
            [FromBody]
            WebClientPopupMessageRequest webClientPopupMessageRequest)
        {
            if (!await _appDbContext.CompanyCompanies.AnyAsync(x => x.Id == companyId))
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        message = "no company found."
                    });
            }

            var company = await _appDbContext.CompanyCompanies.Where(x => x.Id == companyId).FirstOrDefaultAsync();
            if (company == null)
            {
                return NotFound(
                    new ResponseViewModel
                    {
                        message = "no company found."
                    });
            }

            var webclient = await _appDbContext.SenderWebClientSenders
                .Where(x => x.WebClientUUID == webClientUUID && x.CompanyId == companyId).FirstOrDefaultAsync();
            if (webclient == null)
            {
                return NotFound(
                    new ResponseViewModel
                    {
                        message = "no livechat found."
                    });
            }

            var conversation = new Conversation
            {
                CompanyId = company.Id,
                MessageGroupName = company.SignalRGroupName,
                WebClient = webclient,
                ActiveStatus = ActiveStatus.Inactive
            };

            var conversationMessage = new ConversationMessage
            {
                CompanyId = company.Id,
                WebClientReceiver = webclient,
                MessageContent = webClientPopupMessageRequest.PopupMessage,
                Channel = ChannelTypes.LiveChat,
                MessageType = "text",
                IsSentFromSleekflow = true
            };

            var results = await _conversationMessageService.SendMessage(conversation, conversationMessage, false);

            return Ok(
                new ResponseViewModel
                {
                    message = "success"
                });
        }

        [HttpPost]
        [UpdateUserProfileTriggerSource(UpdateUserProfileTriggerSource.WebClientConnection)]
        [Route("WebClient/{companyId}/Connect/{webClientUUID}")]
        public async Task<IActionResult> ConnectUserProfile(
            string companyId,
            string webClientUUID,
            [FromBody]
            WebClientConnectRequest webClientConnectRequest)
        {
            if (!await _appDbContext.CompanyCompanies.AnyAsync(x => x.Id == companyId))
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        message = "no company found."
                    });
            }

            var webclient = await _appDbContext.SenderWebClientSenders
                .Where(x => x.WebClientUUID == webClientUUID && x.CompanyId == companyId).FirstOrDefaultAsync();

            if (webclient == null)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        message = "not found"
                    });
            }

            var targetUserProfile = await _userProfileService.AddOrUpdateUserProfile(
                companyId,
                new NewProfileViewModel
                {
                    PhoneNumber = webClientConnectRequest.PhoneNumber,
                    Email = webClientConnectRequest.Email,
                    FirstName = webClientConnectRequest.Name
                },
                false);

            if (targetUserProfile.Count() == 0)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        message = $"Failed"
                    });
            }

            var existingWebClientProfile = await _appDbContext.UserProfiles
                .Where(x => x.WebClientId == webclient.Id && x.CompanyId == companyId).FirstOrDefaultAsync();
            if (targetUserProfile.FirstOrDefault().Id != existingWebClientProfile.Id)
            {
                await _userProfileService.MergeWebClientIntoUserProfile(
                    companyId,
                    webclient,
                    targetUserProfile.FirstOrDefault(),
                    existingWebClientProfile);
            }

            return Ok(
                new ResponseViewModel
                {
                    message = "success"
                });
        }

        [HttpGet]
        [Route("WebClient/{companyId}")]
        public async Task<IActionResult> GetCompanyInfo(string companyId)
        {
            if (!await _appDbContext.CompanyCompanies.AnyAsync(x => x.Id == companyId))
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        message = "no company found."
                    });
            }

            var company = await _appDbContext.CompanyCompanies.Where(x => x.Id == companyId)
                .Include(x => x.CompanyIconFile).FirstOrDefaultAsync();

            if (company != null)
            {
                company.CompanyCustomFields = await _appDbContext.CompanyCompanyCustomFields
                    .Where(x => x.CompanyId == company.Id).Include(x => x.CompanyCustomFieldFieldLinguals)
                    .ToListAsync(HttpContext.RequestAborted);

                var response = _mapper.Map<CompanyResponse>(company);

                if (!string.IsNullOrEmpty(company.TimeZoneInfoId))
                {
                    response.TimeZoneInfo = TimeZoneHelper.GetTimeZoneById(company.TimeZoneInfoId);
                }

                // _logger.LogError("Weclinet GetCompanyInfo:" + JsonConvert.SerializeObject(response));
                return Ok(response);
            }

            return BadRequest(
                new ResponseViewModel
                {
                    message = "no result"
                });
        }

        [HttpGet]
        [Route("WebClient/{companyId}/staff")]
        public async Task<IActionResult> GetStaffAllInfo(string companyId)
        {
            if (!await _appDbContext.CompanyCompanies.AnyAsync(x => x.Id == companyId))
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        message = "no company found."
                    });
            }

            var company = await _appDbContext.CompanyCompanies.Where(x => x.Id == companyId).FirstOrDefaultAsync();

            if (company != null)
            {
                var staffs = await _appDbContext.UserRoleStaffs.Where(x => x.Company == company)
                    .Include(x => x.Identity).Include(x => x.ProfilePicture).ToListAsync(HttpContext.RequestAborted);
                var staffsVM = _mapper.Map<List<StaffWithoutCompanyResponse>>(staffs);

                // _logger.LogError("Weclinet GetStaffAllInfo:" + JsonConvert.SerializeObject(staffsVM));
                return Ok(staffsVM);
            }

            return BadRequest(
                new ResponseViewModel
                {
                    message = "no result"
                });
        }

        [HttpGet]
        [Route("WebClient/{companyId}/Staff/{staffId}")]
        public async Task<IActionResult> GetStaffInfo(string companyId, string staffId)
        {
            if (!await _appDbContext.CompanyCompanies.AnyAsync(x => x.Id == companyId))
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        message = "no company found."
                    });
            }

            var company = await _appDbContext.CompanyCompanies.Where(x => x.Id == companyId).FirstOrDefaultAsync();

            if (company != null)
            {
                var staffs = await _appDbContext.UserRoleStaffs.Where(x => x.Company == company)
                    .Include(x => x.Identity).Include(x => x.ProfilePicture).Where(x => x.IdentityId == staffId)
                    .FirstOrDefaultAsync();
                var staffsVM = _mapper.Map<StaffWithoutCompanyResponse>(staffs);

                // _logger.LogError("Weclinet GetStaffInfo:" + JsonConvert.SerializeObject(staffsVM));
                return Ok(staffsVM);
            }

            return BadRequest(
                new ResponseViewModel
                {
                    message = "no result"
                });
        }

        [HttpGet]
        [Route("WebClient/{companyId}/Messages/{webClientUUID}")]
        public async Task<IActionResult> GetWebClientMessage(
            string companyId,
            string webClientUUID,
            [FromQuery(Name = "offset")]
            int offset = 0,
            [FromQuery(Name = "limit")]
            int limit = 10)
        {
            if (!await _appDbContext.CompanyCompanies.AnyAsync(x => x.Id == companyId))
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        message = "no company found."
                    });
            }

            var webclient = await _appDbContext.SenderWebClientSenders
                .Where(x => x.WebClientUUID == webClientUUID && x.CompanyId == companyId).FirstOrDefaultAsync();

            if (webclient == null)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        message = "not found"
                    });
            }

            var result = await (from conversationMessages in _appDbContext.ConversationMessages
                    .Include(x => x.UploadedFiles).Include(x => x.Sender).Include(x => x.Receiver)
                    .Include(x => x.WebClientSender).Include(x => x.WebClientReceiver)
                where (conversationMessages.WebClientReceiverId == webclient.Id ||
                       conversationMessages.WebClientSenderId == webclient.Id) &&
                      conversationMessages.CompanyId == companyId &&
                      conversationMessages.Channel == ChannelTypes.LiveChat
                orderby conversationMessages.Timestamp descending
                select conversationMessages).Skip(offset).Take(limit).ToListAsync(HttpContext.RequestAborted);

            var responseVM = _mapper.Map<List<ConversationMessageResponseViewModel>>(result);

            // _logger.LogError("Weclinet GetWebClientMessage:" + JsonConvert.SerializeObject(responseVM));
            return Ok(responseVM);
        }

        [HttpPost]
        [Route("WebClient/{companyId}/Message")]
        public async Task<IActionResult> Send(
            string companyId,
            [FromBody]
            ConversationMessageViewModel conversationMessageViewModel)
        {
            if (!await _appDbContext.CompanyCompanies.AnyAsync(x => x.Id == companyId))
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        message = "no company found."
                    });
            }

            var webClient = await _appDbContext.SenderWebClientSenders.Where(
                    x => x.WebClientUUID == conversationMessageViewModel.WebClientSenderId &&
                         x.CompanyId == companyId)
                .FirstOrDefaultAsync();

            if (webClient == null)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        message = "not found"
                    });
            }

            var conversationMessage = _mapper.Map<ConversationMessage>(conversationMessageViewModel);
            var conversation = _mapper.Map<Conversation>(conversationMessageViewModel);

            conversationMessage.Channel = ChannelTypes.LiveChat;

            var company = await (from _company in _appDbContext.CompanyCompanies
                where _company.Id == webClient.CompanyId
                select _company).FirstOrDefaultAsync();

            conversation.MessageGroupName = company.SignalRGroupName;

            switch (conversationMessage.Channel)
            {
                case ChannelTypes.LiveChat:
                    if (!string.IsNullOrEmpty(conversationMessageViewModel.WebClientSenderId))
                    {
                        conversationMessage.WebClientSender = await _appDbContext.SenderWebClientSenders
                            .FirstOrDefaultAsync(
                                x =>
                                    x.WebClientUUID == conversationMessageViewModel.WebClientSenderId
                                    && x.CompanyId == company.Id);

                        conversation.WebClient = conversationMessage.WebClientSender;
                    }
                    else if (!string.IsNullOrEmpty(conversationMessageViewModel.WebClientReceiverId))
                    {
                        if (conversationMessage.Sender == null)
                        {
                            return BadRequest(
                                new ResponseViewModel
                                {
                                    message = "Please login as staff to reply"
                                });
                        }

                        conversationMessage.WebClientReceiver = await _appDbContext.SenderWebClientSenders
                            .FirstOrDefaultAsync(
                                x =>
                                    x.WebClientUUID == conversationMessageViewModel.WebClientReceiverId
                                    && x.CompanyId == company.Id);

                        conversation.WebClient = conversationMessage.WebClientReceiver;
                    }

                    if (conversation.WebClient == null)
                    {
                        return BadRequest();
                    }

                    break;
            }

            var results = await _conversationMessageService.SendMessage(conversation, conversationMessage);

            var conversationMessagesVM = _mapper.Map<List<ConversationMessageResponseViewModel>>(results);

            // _logger.LogError("Weclinet Send:" + JsonConvert.SerializeObject(conversationMessagesVM));
            return Ok(conversationMessagesVM);
        }

        [HttpPost]
        [Route("WebClient/{companyId}/Message/File")]
        public async Task<IActionResult> SendFile(
            string companyId,
            [FromForm]
            ConversationMessageViewModel conversationMessageViewModel)
        {
            if (!await _appDbContext.CompanyCompanies.AnyAsync(x => x.Id == companyId))
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        message = "no company found."
                    });
            }

            if (!string.IsNullOrEmpty(conversationMessageViewModel.MessageChecksum) &&
                await _appDbContext.ConversationMessages.AnyAsync(
                    x =>
                        x.CompanyId == companyId &&
                        x.MessageChecksum == conversationMessageViewModel.MessageChecksum))
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        message =
                            $"message with checksum: {conversationMessageViewModel.MessageChecksum} sent before."
                    });
            }

            var webClient = await _appDbContext.SenderWebClientSenders.Where(
                    x => x.WebClientUUID == conversationMessageViewModel.WebClientSenderId &&
                         x.CompanyId == companyId)
                .FirstOrDefaultAsync();

            if (webClient == null)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        message = "not found"
                    });
            }

            var conversationMessage = _mapper.Map<ConversationMessage>(conversationMessageViewModel);
            var conversation = _mapper.Map<Conversation>(conversationMessageViewModel);

            conversationMessage.Channel = ChannelTypes.LiveChat;

            var company = await (from _company in _appDbContext.CompanyCompanies
                where _company.Id == webClient.CompanyId
                select _company).FirstOrDefaultAsync();

            conversation.MessageGroupName = company.SignalRGroupName;

            switch (conversationMessage.Channel)
            {
                case ChannelTypes.LiveChat:
                    if (!string.IsNullOrEmpty(conversationMessageViewModel.WebClientSenderId))
                    {
                        conversationMessage.WebClientSender = await _appDbContext.SenderWebClientSenders
                            .FirstOrDefaultAsync(
                                x =>
                                    x.WebClientUUID == conversationMessageViewModel.WebClientSenderId
                                    && x.CompanyId == company.Id);

                        conversation.WebClient = conversationMessage.WebClientSender;
                    }
                    else if (!string.IsNullOrEmpty(conversationMessageViewModel.WebClientReceiverId))
                    {
                        if (conversationMessage.Sender == null)
                        {
                            return BadRequest(
                                new ResponseViewModel
                                {
                                    message = "Please login as staff to reply"
                                });
                        }

                        conversationMessage.WebClientReceiver = await _appDbContext.SenderWebClientSenders
                            .FirstOrDefaultAsync(
                                x =>
                                    x.WebClientUUID == conversationMessageViewModel.WebClientReceiverId
                                    && x.CompanyId == company.Id);

                        conversation.WebClient = conversationMessage.WebClientReceiver;
                    }

                    if (conversation.WebClient == null)
                    {
                        return BadRequest();
                    }

                    break;
            }

            var results = await _conversationMessageService.SendFileMessage(
                conversation,
                conversationMessage,
                conversationMessageViewModel);
            if (results.Count > 0)
            {
                var conversationMessagesVM = _mapper.Map<List<ConversationMessageResponseViewModel>>(results);

                // _logger.LogError("Weclinet SendFile:" + JsonConvert.SerializeObject(conversationMessagesVM));
                return Ok(conversationMessagesVM);
            }

            return BadRequest(
                new ResponseViewModel
                {
                    message = "error"
                });
        }

        [HttpGet]
        [Route("WebClient/{companyId}/Messages/{webClientUUID}/File/{fileId}")]
        public async Task<IActionResult> GetFile(string companyId, string webClientUUID, string fileId)
        {
            if (!await _appDbContext.CompanyCompanies.AnyAsync(x => x.Id == companyId))
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        message = "no company found."
                    });
            }

            var webclient = await _appDbContext.SenderWebClientSenders
                .Where(x => x.WebClientUUID == webClientUUID && x.CompanyId == companyId).FirstOrDefaultAsync();

            if (webclient == null)
            {
                return Unauthorized();
            }

            try
            {
                var company = await _appDbContext.CompanyCompanies.Where(x => x.Id == companyId)
                    .Include(x => x.StorageConfig).FirstOrDefaultAsync();

                if (company == null)
                {
                    return BadRequest(
                        new ResponseViewModel
                        {
                            message = "not found"
                        });
                }

                var file = await _appDbContext.ConversationMessageUploadedFiles
                    .FirstOrDefaultAsync(
                        x =>
                            x.BlobContainer == company.StorageConfig.ContainerName
                            && x.FileId == fileId);

                if (file == null)
                {
                    return NotFound();
                }

                var stream = await _azureBlobStorageService.DownloadFromAzureBlob(file.Filename, file.BlobContainer);
                var extension = Path.GetExtension(file.Filename);

                if (string.IsNullOrEmpty(extension))
                {
                    switch (file.MIMEType)
                    {
                        case "video/mp4":
                            extension = "mp4";
                            break;
                        case "image/jpeg":
                            extension = "jpg";
                            break;
                        case "image/png":
                            extension = "png";
                            break;
                    }
                }

                var filename = Path.GetFileNameWithoutExtension(file.Filename);
                var fileType = string.IsNullOrEmpty(file.MIMEType) ? "application/octet-stream" : file.MIMEType;

                var fileDownloadName =
                    $"{filename}{((string.IsNullOrEmpty(extension) && file.MIMEType == "video/mp4") ? ".mp4" : $".{extension}")}";

                // var data = stream.ToArray();
                // string encodeFilename = HttpUtility.UrlEncode(fileDownloadName, Encoding.UTF8);
                // Response.Headers.Add($"Content-Disposition", $"attachment; filename={Uri.EscapeUriString(Path.GetFileName(file.Filename))}; filename*=UTF-8\"{Uri.EscapeUriString(fileDownloadName)}");
                // return new FileStreamResult(new MemoryStream(data), fileType);
                fileDownloadName = fileDownloadName.Replace(" ", string.Empty).Replace("(", string.Empty)
                    .Replace(")", string.Empty).Replace("[", string.Empty)
                    .Replace("]", string.Empty);

                var res = File(stream.ToArray(), $"{fileType}", fileDownloadName);
                res.EnableRangeProcessing = true;
                return res;
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        public async Task SetWebClientOffline(string webclientUUID, string companyId)
        {
            if (await _appDbContext.SenderWebClientSenders.AnyAsync(
                    x => x.WebClientUUID == webclientUUID && x.CompanyId == companyId &&
                         x.OnlineStatus == OnlineStatus.Online))
            {
                await _appDbContext.SenderWebClientSenders
                    .Where(
                        x =>
                            x.WebClientUUID == webclientUUID
                            && x.CompanyId == companyId)
                    .ExecuteUpdateAsync(
                        sender =>
                            sender.SetProperty(s => s.OnlineStatus, OnlineStatus.Offline));

                await _appDbContext.SaveChangesAsync();
            }
        }
    }
}