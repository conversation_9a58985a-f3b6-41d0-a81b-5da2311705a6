using System;
using System.Collections.Generic;
using System.Collections.Immutable;
using System.Linq;
using Travis_backend.SubscriptionPlanDomain.Constants;
using Travis_backend.SubscriptionPlanDomain.Models;
using Travis_backend.SubscriptionPlanDomain.Models.Common;

namespace Travis_backend.SubscriptionPlanDomain.Repositories;

public static class BaseSubscriptionPlanDefinitions
{
    private const string V10 = "v10";

    #region V10 Base Plans Feature Quantities

    private static readonly List<FeatureQuantity> V10Tier1ProMonthlyFeatureQuantities = new()
    {
        new(FeatureId.Contacts, 2000),
        new("messages", 5000),
        new(FeatureId.Agents, 3, 10),
        new(FeatureId.BroadcastMessages, 5000),
        new(FeatureId.Automations, 10),
        new(FeatureId.Channels, 5),
        new(FeatureId.ApiCalls, 5_000),
        new(FeatureId.AI, 2000),
        new(FeatureId.FlowBuilderMaxActiveWorkflowCount, 3),
        new(FeatureId.FlowBuilderMaxNodesPerWorkflowCount, 25),
        new(FeatureId.FlowBuilderFlowEnrolment, 500, 3000),
        new(FeatureId.CustomCatalog, FeatureQuantityConstants.UnlimitedQuantity),
        new(FeatureId.CustomObject, 1),
        new(FeatureId.StripePaymentIntegration, 1),
        new(FeatureId.ShopifyIntegration, 1),
        new(FeatureId.FacebookLeadAds, 1),
        new(FeatureId.ZapierIntegration, 1),
        new(FeatureId.MakeIntegration, 1),
    };

    private static readonly List<FeatureQuantity> V10Tier1PremiumMonthlyFeatureQuantities = new()
    {
        new(FeatureId.Contacts, 10000),
        new("messages", 50000000),
        new(FeatureId.Agents, 5, 20),
        new(FeatureId.BroadcastMessages, 300_000),
        new(FeatureId.Automations, FeatureQuantityConstants.UnlimitedQuantity),
        new(FeatureId.Channels, 10),
        new(FeatureId.ApiCalls, 100_000),
        new(FeatureId.AI, 2000),
        new("chatbot_automation_support_basic", 1),
        new(FeatureId.FlowBuilderMaxActiveWorkflowCount, 25),
        new(FeatureId.FlowBuilderMaxNodesPerWorkflowCount, 100),
        new(FeatureId.FlowBuilderFlowEnrolment, 3000, 10000),
        new(FeatureId.CustomCatalog, FeatureQuantityConstants.UnlimitedQuantity),
        new(FeatureId.CustomObject, 3),
        new(FeatureId.StripePaymentIntegration, 1),
        new(FeatureId.ShopifyIntegration, 1),
        new(FeatureId.HubSpotIntegration, 1),
        new(FeatureId.WhatsAppQrCode, 1),
        new(FeatureId.FacebookLeadAds, 1),
        new(FeatureId.ZapierIntegration, 1),
        new(FeatureId.MakeIntegration, 1),
    };

    private static readonly List<FeatureQuantity> V10Tier1ProYearlyFeatureQuantities = new()
    {
        new(FeatureId.Contacts, 2000),
        new("messages", 60000),
        new(FeatureId.Agents, 3, 10),
        new(FeatureId.BroadcastMessages, 60_000),
        new(FeatureId.Automations, 10),
        new(FeatureId.Channels, 5),
        new(FeatureId.ApiCalls, 60_000),
        new(FeatureId.OnboardingSupport, 1, 1),
        new(FeatureId.AI, 2000),
        new("chatbot_automation_support_basic", 1),
        new(FeatureId.FlowBuilderMaxActiveWorkflowCount, 3),
        new(FeatureId.FlowBuilderMaxNodesPerWorkflowCount, 25),
        new(FeatureId.FlowBuilderFlowEnrolment, 500, 3000),
        new(FeatureId.CustomCatalog, FeatureQuantityConstants.UnlimitedQuantity),
        new(FeatureId.CustomObject, 1),
        new(FeatureId.StripePaymentIntegration, 1),
        new(FeatureId.ShopifyIntegration, 1),
        new(FeatureId.FacebookLeadAds, 1),
        new(FeatureId.ZapierIntegration, 1),
        new(FeatureId.MakeIntegration, 1),
    };

    private static readonly List<FeatureQuantity> V10Tier1PremiumYearlyFeatureQuantities = new()
    {
        new(FeatureId.Contacts, 10000),
        new("messages", 50000000),
        new(FeatureId.Agents, 5, 20),
        new(FeatureId.BroadcastMessages, 3_600_000),
        new(FeatureId.Automations, FeatureQuantityConstants.UnlimitedQuantity),
        new(FeatureId.Channels, 10),
        new(FeatureId.ApiCalls, 1_200_000),
        new(FeatureId.OnboardingSupport, 1, 1),
        new(FeatureId.AI, 2000),
        new("chatbot_automation_support_basic", 1),
        new(FeatureId.FlowBuilderMaxActiveWorkflowCount, 25),
        new(FeatureId.FlowBuilderMaxNodesPerWorkflowCount, 100),
        new(FeatureId.FlowBuilderFlowEnrolment, 3000, 10000),
        new(FeatureId.CustomCatalog, FeatureQuantityConstants.UnlimitedQuantity),
        new(FeatureId.CustomObject, 3),
        new(FeatureId.StripePaymentIntegration, 1),
        new(FeatureId.ShopifyIntegration, 1),
        new(FeatureId.HubSpotIntegration, 1),
        new(FeatureId.WhatsAppQrCode, 1),
        new(FeatureId.FacebookLeadAds, 1),
        new(FeatureId.ZapierIntegration, 1),
        new(FeatureId.MakeIntegration, 1),
    };

    private static readonly List<FeatureQuantity> V10Tier2ProMonthlyFeatureQuantities = V10Tier1ProMonthlyFeatureQuantities;

    private static readonly List<FeatureQuantity> V10Tier2PremiumMonthlyFeatureQuantities = V10Tier1PremiumMonthlyFeatureQuantities;

    private static readonly List<FeatureQuantity> V10Tier2ProYearlyFeatureQuantities = V10Tier1ProYearlyFeatureQuantities;

    private static readonly List<FeatureQuantity> V10Tier2PremiumYearlyFeatureQuantities = V10Tier1PremiumYearlyFeatureQuantities;

    private static readonly List<FeatureQuantity> V10Tier3ProMonthlyFeatureQuantities = new ()
    {
        new(FeatureId.Contacts, 5000),
        new("messages", 5000),
        new(FeatureId.Agents, 5, 10),
        new(FeatureId.BroadcastMessages, 5000),
        new(FeatureId.Automations, 10),
        new(FeatureId.Channels, 5),
        new(FeatureId.ApiCalls, 5_000),
        new("ai_features_total_usage", 2000),
        new(FeatureId.FlowBuilderMaxActiveWorkflowCount, 3),
        new(FeatureId.FlowBuilderMaxNodesPerWorkflowCount, 25),
        new(FeatureId.FlowBuilderFlowEnrolment, 500, 3000),
        new(FeatureId.CustomCatalog, FeatureQuantityConstants.UnlimitedQuantity),
        new(FeatureId.CustomObject, 1),
        new(FeatureId.StripePaymentIntegration, 1),
        new(FeatureId.ShopifyIntegration, 1),
        new(FeatureId.FacebookLeadAds, 1),
        new(FeatureId.ZapierIntegration, 1),
        new(FeatureId.MakeIntegration, 1),
    };

    private static readonly List<FeatureQuantity> V10Tier3PremiumMonthlyFeatureQuantities = new()
    {
        new(FeatureId.Contacts, 20_000),
        new("messages", 50000000),
        new(FeatureId.Agents, 20, 20),
        new(FeatureId.BroadcastMessages, 300_000),
        new(FeatureId.Automations, FeatureQuantityConstants.UnlimitedQuantity),
        new(FeatureId.Channels, 10),
        new(FeatureId.ApiCalls, 100_000),
        new(FeatureId.AI, 2000),
        new("chatbot_automation_support_basic", 1),
        new(FeatureId.FlowBuilderMaxActiveWorkflowCount, 25),
        new(FeatureId.FlowBuilderMaxNodesPerWorkflowCount, 100),
        new(FeatureId.FlowBuilderFlowEnrolment, 3000, 10000),
        new(FeatureId.CustomCatalog, FeatureQuantityConstants.UnlimitedQuantity),
        new(FeatureId.CustomObject, 3),
        new(FeatureId.StripePaymentIntegration, 1),
        new(FeatureId.ShopifyIntegration, 1),
        new(FeatureId.HubSpotIntegration, 1),
        new(FeatureId.WhatsAppQrCode, 1),
        new(FeatureId.FacebookLeadAds, 1),
        new(FeatureId.ZapierIntegration, 1),
        new(FeatureId.MakeIntegration, 1),
    };

    private static readonly List<FeatureQuantity> V10Tier3ProYearlyFeatureQuantities = new()
    {
        new(FeatureId.Contacts, 5000),
        new("messages", 60000),
        new(FeatureId.Agents, 5, 10),
        new(FeatureId.BroadcastMessages, 60_000),
        new(FeatureId.Automations, 10),
        new(FeatureId.Channels, 5),
        new(FeatureId.ApiCalls, 60_000),
        new(FeatureId.OnboardingSupport, 1, 1),
        new(FeatureId.AI, 2000),
        new("chatbot_automation_support_basic", 1),
        new(FeatureId.FlowBuilderMaxActiveWorkflowCount, 3),
        new(FeatureId.FlowBuilderMaxNodesPerWorkflowCount, 25),
        new(FeatureId.FlowBuilderFlowEnrolment, 500, 3000),
        new(FeatureId.CustomCatalog, FeatureQuantityConstants.UnlimitedQuantity),
        new(FeatureId.CustomObject, 1),
        new(FeatureId.StripePaymentIntegration, 1),
        new(FeatureId.ShopifyIntegration, 1),
        new(FeatureId.FacebookLeadAds, 1),
        new(FeatureId.ZapierIntegration, 1),
        new(FeatureId.MakeIntegration, 1),
    };

    private static readonly List<FeatureQuantity> V10Tier3PremiumYearlyFeatureQuantities = new()
    {
        new(FeatureId.Contacts, 20_000),
        new("messages", 50000000),
        new(FeatureId.Agents, 20, 20),
        new(FeatureId.BroadcastMessages, 3_600_000),
        new(FeatureId.Automations, FeatureQuantityConstants.UnlimitedQuantity),
        new(FeatureId.Channels, 10),
        new(FeatureId.ApiCalls, 1_200_000),
        new(FeatureId.OnboardingSupport, 1, 1),
        new(FeatureId.AI, 2000),
        new("chatbot_automation_support_basic", 1),
        new(FeatureId.FlowBuilderMaxActiveWorkflowCount, 25),
        new(FeatureId.FlowBuilderMaxNodesPerWorkflowCount, 100),
        new(FeatureId.FlowBuilderFlowEnrolment, 3000, 10000),
        new(FeatureId.CustomCatalog, FeatureQuantityConstants.UnlimitedQuantity),
        new(FeatureId.CustomObject, 3),
        new(FeatureId.StripePaymentIntegration, 1),
        new(FeatureId.ShopifyIntegration, 1),
        new(FeatureId.HubSpotIntegration, 1),
        new(FeatureId.WhatsAppQrCode, 1),
        new(FeatureId.FacebookLeadAds, 1),
        new(FeatureId.ZapierIntegration, 1),
        new(FeatureId.MakeIntegration, 1),
    };

    private static readonly List<FeatureQuantity> V10Tier4ProMonthlyFeatureQuantities = new()
    {
        new(FeatureId.Contacts, 5000),
        new("messages", 5000),
        new(FeatureId.Agents, 3, 10),
        new(FeatureId.BroadcastMessages, 5000),
        new(FeatureId.Automations, 10),
        new(FeatureId.Channels, 5),
        new(FeatureId.ApiCalls, 5_000),
        new(FeatureId.AI, 2000),
        new(FeatureId.FlowBuilderMaxActiveWorkflowCount, 3),
        new(FeatureId.FlowBuilderMaxNodesPerWorkflowCount, 25),
        new(FeatureId.FlowBuilderFlowEnrolment, 500, 3000),
        new(FeatureId.CustomCatalog, FeatureQuantityConstants.UnlimitedQuantity),
        new(FeatureId.CustomObject, 1),
        new(FeatureId.StripePaymentIntegration, 1),
        new(FeatureId.ShopifyIntegration, 1),
        new(FeatureId.FacebookLeadAds, 1),
        new(FeatureId.ZapierIntegration, 1),
        new(FeatureId.MakeIntegration, 1),
    };

    private static readonly List<FeatureQuantity> V10Tier4PremiumMonthlyFeatureQuantities = new()
    {
        new(FeatureId.Contacts, 20_000),
        new("messages", 50000000),
        new(FeatureId.Agents, 5, 20),
        new(FeatureId.BroadcastMessages, 300_000),
        new(FeatureId.Automations, FeatureQuantityConstants.UnlimitedQuantity),
        new(FeatureId.Channels, 10),
        new(FeatureId.ApiCalls, 100_000),
        new(FeatureId.AI, 2000),
        new("chatbot_automation_support_basic", 1),
        new(FeatureId.FlowBuilderMaxActiveWorkflowCount, 25),
        new(FeatureId.FlowBuilderMaxNodesPerWorkflowCount, 100),
        new(FeatureId.FlowBuilderFlowEnrolment, 3000, 10000),
        new(FeatureId.CustomCatalog, FeatureQuantityConstants.UnlimitedQuantity),
        new(FeatureId.CustomObject, 3),
        new(FeatureId.StripePaymentIntegration, 1),
        new(FeatureId.ShopifyIntegration, 1),
        new(FeatureId.HubSpotIntegration, 1),
        new(FeatureId.WhatsAppQrCode, 1),
        new(FeatureId.FacebookLeadAds, 1),
        new(FeatureId.ZapierIntegration, 1),
        new(FeatureId.MakeIntegration, 1),
    };

    private static readonly List<FeatureQuantity> V10Tier4ProYearlyFeatureQuantities = new()
    {
        new(FeatureId.Contacts, 5000),
        new("messages", 60000),
        new(FeatureId.Agents, 3, 10),
        new(FeatureId.BroadcastMessages, 60_000),
        new(FeatureId.Automations, 10),
        new(FeatureId.Channels, 5),
        new(FeatureId.ApiCalls, 60_000),
        new(FeatureId.OnboardingSupport, 1, 1),
        new(FeatureId.AI, 2000),
        new("chatbot_automation_support_basic", 1),
        new(FeatureId.FlowBuilderMaxActiveWorkflowCount, 3),
        new(FeatureId.FlowBuilderMaxNodesPerWorkflowCount, 25),
        new(FeatureId.FlowBuilderFlowEnrolment, 500, 3000),
        new(FeatureId.CustomCatalog, FeatureQuantityConstants.UnlimitedQuantity),
        new(FeatureId.CustomObject, 1),
        new(FeatureId.StripePaymentIntegration, 1),
        new(FeatureId.ShopifyIntegration, 1),
        new(FeatureId.FacebookLeadAds, 1),
        new(FeatureId.ZapierIntegration, 1),
        new(FeatureId.MakeIntegration, 1),
    };

    private static readonly List<FeatureQuantity> V10Tier4PremiumYearlyFeatureQuantities = new()
    {
        new(FeatureId.Contacts, 20_000),
        new("messages", 50000000),
        new(FeatureId.Agents, 5, 20),
        new(FeatureId.BroadcastMessages, 3_600_000),
        new(FeatureId.Automations, FeatureQuantityConstants.UnlimitedQuantity),
        new(FeatureId.Channels, 10),
        new(FeatureId.ApiCalls, 1_200_000),
        new(FeatureId.OnboardingSupport, 1, 1),
        new(FeatureId.AI, 2000),
        new("chatbot_automation_support_basic", 1),
        new(FeatureId.FlowBuilderMaxActiveWorkflowCount, 25),
        new(FeatureId.FlowBuilderMaxNodesPerWorkflowCount, 100),
        new(FeatureId.FlowBuilderFlowEnrolment, 3000, 10000),
        new(FeatureId.CustomCatalog, FeatureQuantityConstants.UnlimitedQuantity),
        new(FeatureId.CustomObject, 3),
        new(FeatureId.StripePaymentIntegration, 1),
        new(FeatureId.ShopifyIntegration, 1),
        new(FeatureId.HubSpotIntegration, 1),
        new(FeatureId.WhatsAppQrCode, 1),
        new(FeatureId.FacebookLeadAds, 1),
        new(FeatureId.ZapierIntegration, 1),
        new(FeatureId.MakeIntegration, 1),
    };

    private static readonly List<FeatureQuantity> V10Tier5ProMonthlyFeatureQuantities = V10Tier4ProMonthlyFeatureQuantities;

    private static readonly List<FeatureQuantity> V10Tier5PremiumMonthlyFeatureQuantities = new()
    {
        new(FeatureId.Contacts, 40_000),
        new("messages", 50000000),
        new(FeatureId.Agents, 5, 20),
        new(FeatureId.BroadcastMessages, 300_000),
        new(FeatureId.Automations, FeatureQuantityConstants.UnlimitedQuantity),
        new(FeatureId.Channels, 10),
        new(FeatureId.ApiCalls, 100_000),
        new(FeatureId.AI, 2000),
        new("chatbot_automation_support_basic", 1),
        new(FeatureId.FlowBuilderMaxActiveWorkflowCount, 25),
        new(FeatureId.FlowBuilderMaxNodesPerWorkflowCount, 100),
        new(FeatureId.FlowBuilderFlowEnrolment, 3000, 10000),
        new(FeatureId.CustomCatalog, FeatureQuantityConstants.UnlimitedQuantity),
        new(FeatureId.CustomObject, 3),
        new(FeatureId.StripePaymentIntegration, 1),
        new(FeatureId.ShopifyIntegration, 1),
        new(FeatureId.HubSpotIntegration, 1),
        new(FeatureId.WhatsAppQrCode, 1),
        new(FeatureId.FacebookLeadAds, 1),
        new(FeatureId.ZapierIntegration, 1),
        new(FeatureId.MakeIntegration, 1),
    };

    private static readonly List<FeatureQuantity> V10Tier5ProYearlyFeatureQuantities = V10Tier4ProYearlyFeatureQuantities;

    private static readonly List<FeatureQuantity> V10Tier5PremiumYearlyFeatureQuantities = new()
    {
        new(FeatureId.Contacts, 40_000),
        new("messages", 50000000),
        new(FeatureId.Agents, 5, 20),
        new(FeatureId.BroadcastMessages, 3_600_000),
        new(FeatureId.Automations, FeatureQuantityConstants.UnlimitedQuantity),
        new(FeatureId.Channels, 10),
        new(FeatureId.ApiCalls, 1_200_000),
        new(FeatureId.OnboardingSupport, 1, 1),
        new(FeatureId.AI, 2000),
        new("chatbot_automation_support_basic", 1),
        new(FeatureId.FlowBuilderMaxActiveWorkflowCount, 25),
        new(FeatureId.FlowBuilderMaxNodesPerWorkflowCount, 100),
        new(FeatureId.FlowBuilderFlowEnrolment, 3000, 10000),
        new(FeatureId.CustomCatalog, FeatureQuantityConstants.UnlimitedQuantity),
        new(FeatureId.CustomObject, 3),
        new(FeatureId.StripePaymentIntegration, 1),
        new(FeatureId.ShopifyIntegration, 1),
        new(FeatureId.HubSpotIntegration, 1),
        new(FeatureId.WhatsAppQrCode, 1),
        new(FeatureId.FacebookLeadAds, 1),
        new(FeatureId.ZapierIntegration, 1),
        new(FeatureId.MakeIntegration, 1),
    };

    #endregion

    public static readonly IEnumerable<PlanDefinition> BasePlanDefinitions = new List<PlanDefinition>
    {
        #region V10 Startup Base Plan
        new(
            new List<Multilingual>
            {
                new("en", "startup", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new(FeatureId.Agents, 3, 3),
                new(FeatureId.Contacts, 100, 100),
                new(FeatureId.BroadcastMessages, 100),
                new(FeatureId.Channels, 5),
                new(FeatureId.Automations, 5),
                new(FeatureId.FlowBuilderMaxActiveWorkflowCount, 3),
                new(FeatureId.FlowBuilderMaxNodesPerWorkflowCount, 25),
                new(FeatureId.FlowBuilderFlowEnrolment, 50, 50),
                new(FeatureId.AI, 2000),
            },
            new List<Price> { new("USD", 0) },
            PlanTypes.Subscription,
            "free",
            V10,
            new List<string> { "Active" },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow
        ),
        #endregion
        #region V10 Tier1 Monthly Base Plans
        new(
            new List<Multilingual>
            {
                new("en", "pro_monthly", true)
            },
            new List<Multilingual>(),
            V10Tier1ProMonthlyFeatureQuantities,
            new List<Price>
            {
                new("USD", 199),
                new("HKD", 1599),
                new("CNY", 1439),
                new("EUR", 189),
                new("GBP", 169),
                new("CAD", 279),
                new("AUD", 309),
                new("AED", 729),
            },
            PlanTypes.Subscription,
            "pro",
            V10,
            new List<string> { "Active" },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            "Tier1",
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new(
            new List<Multilingual>
            {
                new("en", "premium_monthly", true)
            },
            new List<Multilingual>(),
            V10Tier1PremiumMonthlyFeatureQuantities,
            new List<Price>
            {
                new("USD", 399),
                new("HKD", 2999),
                new("CNY", 2879),
                new("EUR", 369),
                new("GBP", 319),
                new("CAD", 539),
                new("AUD", 609),
                new("AED", 1499),
            },
            PlanTypes.Subscription,
            "premium",
            V10,
            new List<string> { "Active" },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            "Tier1",
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        #endregion
        #region V10 Tier1 Yearly Base Plans
        new(
            new List<Multilingual>
            {
                new("en", "pro_yearly", true)
            },
            new List<Multilingual>(),
            V10Tier1ProYearlyFeatureQuantities,
            new List<Price>
            {
                new("USD", 1788),
                new("HKD", 14388),
                new("CNY", 12948),
                new("EUR", 1908),
                new("GBP", 1668),
                new("CAD", 2868),
                new("AUD", 3228),
                new("AED", 7188),
            },
            PlanTypes.Subscription,
            "pro",
            V10,
            new List<string> { "Active" },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            "Tier1",
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new(
            new List<Multilingual>
            {
                new("en", "premium_yearly", true)
            },
            new List<Multilingual>(),
            V10Tier1PremiumYearlyFeatureQuantities,
            new List<Price>
            {
                new("USD", 4188),
                new("HKD", 31188),
                new("CNY", 30228),
                new("EUR", 3708),
                new("GBP", 3228),
                new("CAD", 5508),
                new("AUD", 6108),
                new("AED", 15588),
            },
            PlanTypes.Subscription,
            "premium",
            V10,
            new List<string> { "Active" },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            "Tier1",
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        #endregion
        #region V10 Tier2 Monthly Base Plans
        new(
            new List<Multilingual>
            {
                new("en", "pro_monthly", true)
            },
            new List<Multilingual>(),
            V10Tier2ProMonthlyFeatureQuantities,
            new List<Price>
            {
                new("USD", 199),
                new("SGD", 269),
                new("EUR", 179),
            },
            PlanTypes.Subscription,
            "pro",
            V10,
            new List<string> { "Active" },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            "Tier2",
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new(
            new List<Multilingual>
            {
                new("en", "premium_monthly", true)
            },
            new List<Multilingual>(),
            V10Tier2PremiumMonthlyFeatureQuantities,
            new List<Price>
            {
                new("USD", 349),
                new("SGD", 479),
                new("EUR", 329),
            },
            PlanTypes.Subscription,
            "premium",
            V10,
            new List<string> { "Active" },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            "Tier2",
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        #endregion
        #region V10 Tier2 Yearly Base Plans
        new(
            new List<Multilingual>
            {
                new("en", "pro_yearly", true)
            },
            new List<Multilingual>(),
            V10Tier2ProYearlyFeatureQuantities,
            new List<Price>
            {
                new("USD", 1788),
                new("SGD", 2388),
                new("EUR", 1668),
            },
            PlanTypes.Subscription,
            "pro",
            V10,
            new List<string> { "Active" },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            "Tier2",
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new(
            new List<Multilingual>
            {
                new("en", "premium_yearly", true)
            },
            new List<Multilingual>(),
            V10Tier2PremiumYearlyFeatureQuantities,
            new List<Price>
            {
                new("USD", 3588),
                new("SGD", 4908),
                new("EUR", 3468),
            },
            PlanTypes.Subscription,
            "premium",
            V10,
            new List<string> { "Active" },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            "Tier2",
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        #endregion
        #region V10 Tier3 Monthly Base Plans
        new(
            new List<Multilingual>
            {
                new("en", "pro_monthly", true)
            },
            new List<Multilingual>(),
            V10Tier3ProMonthlyFeatureQuantities,
            new List<Price>
            {
                new("USD", 199),
                new("BRL", 1079),
            },
            PlanTypes.Subscription,
            "pro",
            V10,
            new List<string> { "Active" },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            "Tier3",
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new(
            new List<Multilingual>
            {
                new("en", "premium_monthly", true)
            },
            new List<Multilingual>(),
            V10Tier3PremiumMonthlyFeatureQuantities,
            new List<Price>
            {
                new("USD", 349),
                new("BRL", 1899),
            },
            PlanTypes.Subscription,
            "premium",
            V10,
            new List<string> { "Active" },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            "Tier3",
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        #endregion
        #region V10 Tier3 Yearly Base Plans
        new(
            new List<Multilingual>
            {
                new("en", "pro_yearly", true)
            },
            new List<Multilingual>(),
            V10Tier3ProYearlyFeatureQuantities,
            new List<Price>
            {
                new("USD", 1788),
                new("BRL", 9588),
            },
            PlanTypes.Subscription,
            "pro",
            V10,
            new List<string> { "Active" },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            "Tier3",
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new(
            new List<Multilingual>
            {
                new("en", "premium_yearly", true)
            },
            new List<Multilingual>(),
            V10Tier3PremiumYearlyFeatureQuantities,
            new List<Price>
            {
                new("USD", 3588),
                new("BRL", 19068),
            },
            PlanTypes.Subscription,
            "premium",
            V10,
            new List<string> { "Active" },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            "Tier3",
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        #endregion
        #region V10 Tier4 Monthly Base Plans
        new(
            new List<Multilingual>
            {
                new("en", "pro_monthly", true)
            },
            new List<Multilingual>(),
            V10Tier4ProMonthlyFeatureQuantities,
            new List<Price>
            {
                new("USD", 199),
                new("MYR", 899),
            },
            PlanTypes.Subscription,
            "pro",
            V10,
            new List<string> { "Active" },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            "Tier4",
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new(
            new List<Multilingual>
            {
                new("en", "premium_monthly", true)
            },
            new List<Multilingual>(),
            V10Tier4PremiumMonthlyFeatureQuantities,
            new List<Price>
            {
                new("USD", 349),
                new("MYR", 1599),
            },
            PlanTypes.Subscription,
            "premium",
            V10,
            new List<string> { "Active" },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            "Tier4",
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        #endregion
        #region V10 Tier4 Yearly Base Plans
        new(
            new List<Multilingual>
            {
                new("en", "pro_yearly", true)
            },
            new List<Multilingual>(),
            V10Tier4ProYearlyFeatureQuantities,
            new List<Price>
            {
                new("USD", 1788),
                new("MYR", 8388),
            },
            PlanTypes.Subscription,
            "pro",
            V10,
            new List<string> { "Active" },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            "Tier4",
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new(
            new List<Multilingual>
            {
                new("en", "premium_yearly", true)
            },
            new List<Multilingual>(),
            V10Tier4PremiumYearlyFeatureQuantities,
            new List<Price>
            {
                new("USD", 3588),
                new("MYR", 16788),
            },
            PlanTypes.Subscription,
            "premium",
            V10,
            new List<string> { "Active" },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            "Tier4",
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        #endregion
        #region V10 Tier5 Monthly Base Plans
        new(
            new List<Multilingual>
            {
                new("en", "pro_monthly", true)
            },
            new List<Multilingual>(),
            V10Tier5ProMonthlyFeatureQuantities,
            new List<Price>
            {
                new("USD", 199),
                new("IDR", 2790000),
                new("INR", 16599),
            },
            PlanTypes.Subscription,
            "pro",
            V10,
            new List<string> { "Active" },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            "Tier5",
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new(
            new List<Multilingual>
            {
                new("en", "premium_monthly", true)
            },
            new List<Multilingual>(),
            V10Tier5PremiumMonthlyFeatureQuantities,
            new List<Price>
            {
                new("USD", 349),
                new("IDR", 4890000),
                new("INR", 28999),
            },
            PlanTypes.Subscription,
            "premium",
            V10,
            new List<string> { "Active" },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            "Tier5",
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        #endregion
        #region V10 Tier5 Yearly Base Plans
        new(
            new List<Multilingual>
            {
                new("en", "pro_yearly", true)
            },
            new List<Multilingual>(),
            V10Tier5ProYearlyFeatureQuantities,
            new List<Price>
            {
                new("USD", 1788),
                new("IDR", 28680000),
                new("INR", 149388),
            },
            PlanTypes.Subscription,
            "pro",
            V10,
            new List<string> { "Active" },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            "Tier5",
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        new(
            new List<Multilingual>
            {
                new("en", "premium_yearly", true)
            },
            new List<Multilingual>(),
            V10Tier5PremiumYearlyFeatureQuantities,
            new List<Price>
            {
                new("USD", 3588),
                new("IDR", 50280000),
                new("INR", 299988),
            },
            PlanTypes.Subscription,
            "premium",
            V10,
            new List<string> { "Active" },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            "Tier5",
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        #endregion
        #region V10 Enterprise Base Plan
        new PlanDefinition(
            new List<Multilingual>
            {
                new("en", "enterprise", true)
            },
            new List<Multilingual>(),
            new List<FeatureQuantity>
            {
                new(FeatureId.Agents, 10),
                new(FeatureId.Contacts, 40_000),
                new("messages", 50_000_000),
                new(FeatureId.BroadcastMessages, FeatureQuantityConstants.UnlimitedQuantity),
                new(FeatureId.Automations, 999),
                new(FeatureId.Channels, FeatureQuantityConstants.UnlimitedQuantity),
                new(FeatureId.ApiCalls, 120_000_000),
                new(FeatureId.OnboardingSupport, 1),
                new(FeatureId.BusinessConsultancyService, 1),
                new("chatbot_automation_support_basic", 1),
                new(FeatureId.AI, 2000),
                new(FeatureId.FlowBuilderMaxActiveWorkflowCount, 50),
                new(FeatureId.FlowBuilderMaxNodesPerWorkflowCount, 200),
                new(FeatureId.FlowBuilderFlowEnrolment, 10000, FeatureQuantityConstants.UnlimitedQuantity),
                new(FeatureId.CustomCatalog, FeatureQuantityConstants.UnlimitedQuantity),
                new(FeatureId.CustomObject, 10),
                new(FeatureId.StripePaymentIntegration, 1),
                new(FeatureId.ShopifyIntegration, 1),
                new(FeatureId.HubSpotIntegration, 1),
                new(FeatureId.SalesforceIntegration, 1),
                new(FeatureId.SalesforceMarketingCloud, 1),
                new(FeatureId.SalesforceCommerceCloud, 1),
                new(FeatureId.MicrosoftDynamics365Integration, 1),
                new(FeatureId.FacebookLeadAds, 1),
                new(FeatureId.ZapierIntegration, 1),
                new(FeatureId.MakeIntegration, 1),
            },
            new List<Price>
            {
                new("USD", 999),
            },
            PlanTypes.Subscription,
            "enterprise",
            V10,
            new List<string> { "Active" },
            new Dictionary<string, object>(),
            string.Empty,
            string.Empty,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow),
        #endregion
    };
}