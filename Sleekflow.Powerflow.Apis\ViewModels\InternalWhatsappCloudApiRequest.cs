using System.ComponentModel.DataAnnotations;
using GraphApi.Client.Const.WhatsappCloudApi;
using Microsoft.AspNetCore.Mvc.ModelBinding.Validation;
using Newtonsoft.Json;
using Sleekflow.Apis.MessagingHub.Model;

namespace Sleekflow.Powerflow.Apis.ViewModels;

public class CreateWhatsappCloudApiTransactionLogRequest
{
    [Required]
    [JsonProperty("sleekflow_company_id")]
    public string CompanyId { get; set; }

    [Required]
    [JsonProperty("facebook_business_id")]
    public string FacebookBusinessId { get; set; }

    [Required]
    [JsonProperty("add_credit_value")]
    public decimal AddCreditValue { get; set; }

    [JsonProperty("Metadata")]
    public Dictionary<string, object> Metadata { get; set; }

    [JsonProperty("reseller_transaction_log_detail")]
    public string ResellerTransactionLogDetail { get; set; }
}

public class GetWhatsAppCloudApiUsageRecordsRequest
{
    [Required]
    [JsonProperty("sleekflow_company_id")]
    public string CompanyId { get; set; }
}

public class GetAllWhatsAppCloudApiBalancesRequest
{
    public bool AllowCache { get; set; } = true;
}

public class GetAllWhatsappCloudApiWabasRequest
{
    public bool AllowCache { get; set; } = true;
}

public class UpdateWhatsappCloudApiBusinessBalanceMarkupProfileRequest
{
    [Required, JsonProperty("business_balance_id")]
    public string BusinessBalanceId { get; set; }

    [Required, JsonProperty("markup_profile")]
    public MarkupProfile MarkupProfile { get; set; }

    [JsonProperty("last_recalculate_business_balance_transaction_log_date_time")]
    public DateTime? LastRecalculateBusinessBalanceTransactionLogDateTime { get; set; }
}

public class AddBackWhatsappCloudApiSenderToUserProfileRequest
{
    [Required]
    [JsonProperty("company_id")]
    public string CompanyId { get; set; }

    [JsonProperty("is_do_in_background")]
    public bool IsDoInBackground { get; set; } = true;
}

public class PatchBusinessBalanceMarkupProfile
{
    [Required]
    [JsonProperty("white_list_sleekflow_company_ids")]
    public List<string> WhiteListSleekflowCompanyIds { get; set; }

    [Required]
    [JsonProperty("markup_profile")]
    public MarkupProfile MarkupProfile { get; set; }

    [Required]
    [JsonProperty("effective_date")]
    public DateTimeOffset EffectiveDate { get; set; }
}

public record GetWhatsappCloudApiConversationUsageAnalyticRequest(
    [Required, JsonProperty("facebook_business_id")]
    string FacebookBusinessId,
    [Required, JsonProperty("start")]
    DateTime Start,
    [Required, JsonProperty("end")]
    DateTime End,
    [Required, JsonProperty("granularity")]
    string Granularity = WhatsappConversationAnalyticGranularityConst.MONTHLY);

public record GetAllWhatsappCloudApiConversationUsageAnalyticsRequest(
    [Required, JsonProperty("start")]
    DateTime Start,
    [Required, JsonProperty("end")]
    DateTime End,
    [Required, JsonProperty("granularity")]
    string Granularity);

public record GetFilteredManagementWhatsappCloudApiBusinessBalanceTransactionLogsRequest(
    [ValidateNever, JsonProperty("business_balance_transaction_log_filter")]
    BusinessBalanceTransactionLogFilter BusinessBalanceTransactionLogFilter,
    [JsonProperty("Limit")]
    int Limit = 1000,
    [JsonProperty("continuation_token")]
    string ContinuationToken = null);

public record TriggerWhatsappPhoneNumberOtpRequest(
    [Required]
    string CompanyId,
    [Required]
    string FacebookWabaId,
    [Required]
    string FacebookPhoneNumber,
    [Required]
    string FacebookPhoneNumberId,
    [Required]
    string CodeMethod,
    [Required]
    string Language = "en_US");

public record VerifyWhatsappPhoneNumberOtpRequest(
    [Required]
    string CompanyId,
    [Required]
    string FacebookWabaId,
    [Required]
    string FacebookPhoneNumber,
    [Required]
    string FacebookPhoneNumberId,
    [Required]
    string Code);

public record RegisterWhatsappPhoneNumberRequest(
    [Required]
    string CompanyId,
    [Required]
    string FacebookWabaId,
    [Required]
    string FacebookPhoneNumber,
    [Required]
    string FacebookPhoneNumberId,
    string Pin);

public record DeregisterWhatsAppPhoneNumberRequest(
    [Required]
    string CompanyId,
    [Required]
    string FacebookWabaId,
    [Required]
    string FacebookPhoneNumberId);

public record GetConnectedWabasByCompanyIdRequest(
    [Required]
    string CompanyId,
    [Required]
    bool AllowCache);

public record DisassociateFacebookBusinessAccountFromCompanyRequest(
    [Required]
    string CompanyId,
    [Required]
    string FacebookBusinessId);


public record GetWhatsappPhoneNumberBusinessProfileRequest(
    [Required]
    string CompanyId,
    [Required]
    string MessagingWabaId,
    [Required]
    string MessagingPhoneNumberId);

public record UpdateWhatsappPhoneNumberBusinessProfileRequest(
    [Required]
    string CompanyId,
    [Required]
    string MessagingHubWabaId,
    [Required]
    string MessagingHubPhoneNumberId,
    [Required]
    [ValidateNever]
    UpdatePhoneNumberBusinessProfileRequest UpdatePhoneNumberBusinessProfile);

public record GetMonthlyWhatsappCloudApiConversationUsageAnalyticsRequest(
    [Required]
    [JsonProperty("start")]
    DateTime Start,
    [Required]
    [JsonProperty("end")]
    DateTime End
);
