<Project Sdk="Microsoft.NET.Sdk.Web">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <Nullable>enable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
        <RootNamespace>Travis_backend.Auth0</RootNamespace>
        <IncludeOpenAPIAnalyzers>true</IncludeOpenAPIAnalyzers>
        <GenerateDocumentationFile>true</GenerateDocumentationFile>
        <Configurations>Debug;Release;QA;OBSOLETE_AUTH</Configurations>
        <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
        <EnableDefaultContentItems>false</EnableDefaultContentItems>
        <Platforms>AnyCPU</Platforms>
    </PropertyGroup>

    <PropertyGroup Condition=" '$(Configuration)' == 'Release' ">
        <PlatformTarget>x64</PlatformTarget>
        <ServerGarbageCollection>true</ServerGarbageCollection>
    </PropertyGroup>
    <PropertyGroup Condition=" '$(Configuration)' == 'QA' ">
        <DefineConstants>TRACE;DEBUG</DefineConstants>
    </PropertyGroup>
    <ItemGroup>
        <PackageReference Include="Auth0.AuthenticationApi" Version="7.26.2" />
        <PackageReference Include="Auth0.Core" Version="7.26.2" />
        <PackageReference Include="Auth0.ManagementApi" Version="7.26.2" />
        <PackageReference Include="AutoMapper" Version="13.0.1" />
        <PackageReference Include="LazyCache" Version="2.4.0" />
        <PackageReference Include="LazyCache.AspNetCore" Version="2.4.0" />
        <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="8.0.1" />
        <PackageReference Include="Microsoft.IdentityModel.Protocols.OpenIdConnect" Version="8.0.1" />
        <PackageReference Include="Microsoft.VisualStudio.Web.CodeGeneration.Design" Version="8.0.3" />
        <PackageReference Include="Swashbuckle.AspNetCore" Version="6.7.0" />
    </ItemGroup>
    <ItemGroup>
        <PackageReference Include="StyleCop.Analyzers" Version="1.2.0-beta.435" PrivateAssets="all" IncludeAssets="runtime; build; native; contentfiles; analyzers; buildtransitive" Condition="$(MSBuildProjectExtension) == '.csproj'" />
        <PackageReference Include="SonarAnalyzer.CSharp" Version="9.31.0.96804" PrivateAssets="all" IncludeAssets="runtime; build; native; contentfiles; analyzers; buildtransitive" Condition="$(MSBuildProjectExtension) == '.csproj'" />
    </ItemGroup>

    <!-- https://github.com/dotnet/runtime/issues/62329 -->
    <ItemGroup Condition="'$(OS)' == 'Windows_NT'">
        <RuntimeHostConfigurationOption Include="System.Globalization.AppLocalIcu" Value="********" />
        <PackageReference Include="Microsoft.ICU.ICU4C.Runtime" Version="********" />
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\Travis_backend\Travis_backend.csproj" />
    </ItemGroup>

    <ItemGroup>
        <AssemblyAttribute Include="System.Runtime.CompilerServices.InternalsVisibleTo">
            <_Parameter1>SleekFlowBackendTest</_Parameter1>
        </AssemblyAttribute>
    </ItemGroup>

    <ItemGroup>
        <Reference Include="GraphApi.Client.Const">
            <HintPath>..\Travis_backend\Binaries\GraphApi.Client\netstandard2.0\GraphApi.Client.Const.dll</HintPath>
        </Reference>
        <Reference Include="Hangfire.Pro">
            <HintPath>..\Travis_backend\Hangfire.Pro.Binaries\Hangfire.Pro.dll</HintPath>
        </Reference>
        <Reference Include="Hangfire.Pro.Redis">
            <HintPath>..\Travis_backend\Hangfire.Pro.Binaries\Hangfire.Pro.Redis.dll</HintPath>
        </Reference>
        <Reference Include="Sleekflow.Apis.AuditHub">
            <HintPath>..\Travis_backend\Binaries\Sleekflow.Apis.AuditHub\netstandard2.0\Sleekflow.Apis.AuditHub.dll</HintPath>
        </Reference>
        <Reference Include="Sleekflow.Apis.CommerceHub">
            <HintPath>..\Travis_backend\Binaries\Sleekflow.Apis.CommerceHub\netstandard2.0\Sleekflow.Apis.CommerceHub.dll</HintPath>
        </Reference>
        <Reference Include="Sleekflow.Apis.CrmHub">
            <HintPath>..\Travis_backend\Binaries\Sleekflow.Apis.CrmHub\netstandard2.0\Sleekflow.Apis.CrmHub.dll</HintPath>
        </Reference>
        <Reference Include="Sleekflow.Apis.MessagingHub">
            <HintPath>..\Travis_backend\Binaries\Sleekflow.Apis.MessagingHub\netstandard2.0\Sleekflow.Apis.MessagingHub.dll</HintPath>
        </Reference>
        <Reference Include="Sleekflow.Apis.FlowHub">
            <HintPath>..\Travis_backend\Binaries\Sleekflow.Apis.FlowHub\netstandard2.0\Sleekflow.Apis.FlowHub.dll</HintPath>
        </Reference>
        <Reference Include="Sleekflow.Apis.IntelligentHub">
            <HintPath>..\Travis_backend\Binaries\Sleekflow.Apis.IntelligentHub\netstandard2.0\Sleekflow.Apis.IntelligentHub.dll</HintPath>
        </Reference>
        <Reference Include="Sleekflow.Apis.PublicApiGateway">
            <HintPath>..\Travis_backend\Binaries\Sleekflow.Apis.PublicApiGateway\netstandard2.0\Sleekflow.Apis.PublicApiGateway.dll</HintPath>
        </Reference>
        <Reference Include="Sleekflow.Apis.TenantHub">
            <HintPath>..\Travis_backend\Binaries\Sleekflow.Apis.TenantHub\netstandard2.0\Sleekflow.Apis.TenantHub.dll</HintPath>
        </Reference>
        <Reference Include="Sleekflow.Apis.WebhookHub">
            <HintPath>..\Travis_backend\Binaries\Sleekflow.Apis.WebhookHub\netstandard2.0\Sleekflow.Apis.WebhookHub.dll</HintPath>
        </Reference>
    </ItemGroup>

    <ItemGroup>
        <None Update="appsettings.json">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
    </ItemGroup>

    <ItemGroup>
        <Content Include="..\.dockerignore">
            <Link>.dockerignore</Link>
        </Content>
    </ItemGroup>

    <ItemGroup>
        <Content Update="web.config">
            <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
            <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
        </Content>
    </ItemGroup>

</Project>