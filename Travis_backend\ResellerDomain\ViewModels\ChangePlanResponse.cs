using System;
using System.ComponentModel.DataAnnotations.Schema;
using Travis_backend.Enums;

namespace Travis_backend.ResellerDomain.ViewModels;

public class ChangePlanResponse
{
    public string CompanyId { get; set; }

    public string CompanyName { get; set; }

    public ChangePlanType ChangePlanType { get; set; }

    public string FromSubscriptionPlanId { get; set; }

    [Column(TypeName = "decimal(18,5)")]
    public decimal? FromSubscriptionPlanPricing { get; set; }

    public DateTime? CurrentSubscriptionExpiryDate { get; set; }

    public string ToSubscriptionPlanId { get; set; }

    [Column(TypeName = "decimal(18,5)")]
    public decimal? ToSubscriptionPlanPricing { get; set; }

    [Column(TypeName = "decimal(18,5)")]
    public decimal? RefundAmount { get; set; }

    [Column(TypeName = "decimal(18,5)")]
    public decimal TotalAmount { get; set; }

    [Column(TypeName = "decimal(18,5)")]
    public string Currency { get; set; }

    public decimal CurrentBalance { get; set; }

    public DateTime? EffectiveDate { get; set; }

    public DateTime? ChargeDate { get; set; }
}