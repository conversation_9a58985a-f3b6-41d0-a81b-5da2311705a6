using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;
using Hangfire;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Sleekflow.Apis.CommerceHub.Api;
using Sleekflow.Apis.CommerceHub.Model;
using Travis_backend.AccountAuthenticationDomain.Models;
using Travis_backend.CompanyDomain.Services;
using Travis_backend.ConversationServices;
using Travis_backend.Database;
using Travis_backend.Filters;
using Travis_backend.SleekflowCommerceHubDomain.Models;
using Travis_backend.SleekflowCommerceHubDomain.Services;
using Price = Sleekflow.Apis.CommerceHub.Model.Price;
using Staff = Travis_backend.CompanyDomain.Models.Staff;
using UserProfile = Sleekflow.Apis.CommerceHub.Model.UserProfile;

namespace Travis_backend.Controllers.SleekflowControllers;

[Authorize]
[Route("CommerceHub")]
[TypeFilter(typeof(CommerceHubExceptionFilter))]
public class CommerceHubController : ControllerBase
{
    private readonly ICoreService _coreService;
    private readonly ILogger<CommerceHubController> _logger;
    private readonly UserManager<ApplicationUser> _userManager;
    private readonly IBlobsApi _blobsApi;
    private readonly ICartsApi _cartsApi;
    private readonly ICategoriesApi _categoriesApi;
    private readonly ICurrenciesApi _currenciesApi;
    private readonly ICustomCatalogsApi _customCatalogsApi;
    private readonly ILanguagesApi _languagesApi;
    private readonly IOrdersApi _ordersApi;
    private readonly IPaymentProviderConfigsApi _paymentProviderConfigsApi;
    private readonly IPaymentsApi _paymentsApi;
    private readonly IProductsApi _productsApi;
    private readonly IProductVariantsApi _productVariantsApi;
    private readonly IStoresApi _storesApi;
    private readonly ICompanyTeamService _companyTeamService;
    private readonly ApplicationDbContext _appDbContext;
    private readonly IConfiguration _configuration;
    private readonly ICustomCatalogConfigsApi _customCatalogConfigsApi;

    public CommerceHubController(
        ICoreService coreService,
        ILogger<CommerceHubController> logger,
        UserManager<ApplicationUser> userManager,
        IBlobsApi blobsApi,
        ICartsApi cartsApi,
        ICategoriesApi categoriesApi,
        ICurrenciesApi currenciesApi,
        ICustomCatalogsApi customCatalogsApi,
        ILanguagesApi languagesApi,
        IOrdersApi ordersApi,
        IPaymentProviderConfigsApi paymentProviderConfigsApi,
        IPaymentsApi paymentsApi,
        IProductsApi productsApi,
        IProductVariantsApi productVariantsApi,
        IStoresApi storesApi,
        ICompanyTeamService companyTeamService,
        ApplicationDbContext appDbContext,
        IConfiguration configuration,
        ICustomCatalogConfigsApi customCatalogConfigsApi)
    {
        _logger = logger;
        _coreService = coreService;
        _userManager = userManager;
        _blobsApi = blobsApi;
        _cartsApi = cartsApi;
        _categoriesApi = categoriesApi;
        _currenciesApi = currenciesApi;
        _customCatalogsApi = customCatalogsApi;
        _languagesApi = languagesApi;
        _ordersApi = ordersApi;
        _paymentProviderConfigsApi = paymentProviderConfigsApi;
        _paymentsApi = paymentsApi;
        _productsApi = productsApi;
        _productVariantsApi = productVariantsApi;
        _storesApi = storesApi;
        _companyTeamService = companyTeamService;
        _appDbContext = appDbContext;
        _configuration = configuration;
        _customCatalogConfigsApi = customCatalogConfigsApi;
    }

    private async Task<List<string>> GetTeamIdsBySleekflowStaffAsync(Staff sleekflowStaff)
    {
        var companyId = sleekflowStaff.CompanyId;
        var identityId = sleekflowStaff.IdentityId;

        var companyTeams = await _companyTeamService.GetCompanyteamFromStaffId(companyId, identityId);

        return companyTeams.Select(t => t.Id.ToString()).ToList();
    }

    public class CreateUploadBlobSasUrlsRequest
    {
        [JsonProperty("store_id")]
        public string StoreId;

        [JsonProperty("number_of_blobs")]
        public int NumberOfBlobs;

        [JsonProperty("blob_type")]
        public string BlobType;
    }

    [HttpPost("Blobs/CreateUploadBlobSasUrls")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<CreateUploadBlobSasUrlsOutput>> CreateUploadBlobSasUrls(
        [FromBody]
        CreateUploadBlobSasUrlsRequest createUploadBlobSasUrlsRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var createUploadBlobSasUrlsOutputOutput = await _blobsApi.BlobsCreateUploadBlobSasUrlsPostAsync(
            createUploadBlobSasUrlsInput: new CreateUploadBlobSasUrlsInput(
                staff.CompanyId,
                createUploadBlobSasUrlsRequest.StoreId,
                createUploadBlobSasUrlsRequest.NumberOfBlobs,
                createUploadBlobSasUrlsRequest.BlobType));

        return Ok(createUploadBlobSasUrlsOutputOutput);
    }

    public class ClearCartRequest
    {
        [JsonProperty("store_id")]
        public string StoreId { get; set; }

        [JsonProperty("user_profile_id")]
        public string UserProfileId { get; set; }
    }

    [HttpPost("Carts/ClearCart")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<ClearCartOutputOutput>> ClearCart(
        [FromBody]
        ClearCartRequest clearCartRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var clearCartOutputOutput = await _cartsApi.CartsClearCartPostAsync(
            clearCartInput: new ClearCartInput(
                staff.CompanyId,
                clearCartRequest.UserProfileId,
                clearCartRequest.StoreId,
                staff.Id.ToString(),
                await GetTeamIdsBySleekflowStaffAsync(staff)));

        return Ok(clearCartOutputOutput);
    }

    public class GetCalculatedCartRequest
    {
        [JsonProperty("store_id")]
        public string StoreId { get; set; }

        [JsonProperty("user_profile_id")]
        public string UserProfileId { get; set; }

        [JsonProperty("currency_iso_code")]
        public string CurrencyIsoCode { get; set; }

        [JsonProperty("language_iso_code")]
        public string LanguageIsoCode { get; set; }
    }

    [HttpPost("Carts/GetCalculatedCart")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GetCalculatedCartOutputOutput>> GetCalculatedCart(
        [FromBody]
        GetCalculatedCartRequest getCalculatedCartRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var getCalculatedCartOutputOutput = await _cartsApi.CartsGetCalculatedCartPostAsync(
            getCalculatedCartInput: new GetCalculatedCartInput(
                staff.CompanyId,
                getCalculatedCartRequest.UserProfileId,
                getCalculatedCartRequest.StoreId,
                getCalculatedCartRequest.CurrencyIsoCode,
                getCalculatedCartRequest.LanguageIsoCode));

        return Ok(getCalculatedCartOutputOutput);
    }

    public class GetCartRequest
    {
        [JsonProperty("user_profile_id")]
        public string UserProfileId { get; set; }

        [JsonProperty("store_id")]
        public string StoreId { get; set; }
    }

    [HttpPost("Carts/GetCart")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GetCartOutputOutput>> GetCart(
        [FromBody]
        GetCartRequest getCartRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var getCartOutputOutput = await _cartsApi.CartsGetCartPostAsync(
            getCartInput: new GetCartInput(
                staff.CompanyId,
                getCartRequest.UserProfileId,
                getCartRequest.StoreId));

        return Ok(getCartOutputOutput);
    }

    public class GetUserCartsRequest
    {
        [JsonProperty("user_profile_id")]
        public string UserProfileId { get; set; }
    }

    [HttpPost("Carts/GetUserCarts")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GetUserCartsOutputOutput>> GetUserCarts(
        [FromBody]
        GetUserCartsRequest getUserCartsRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var getUserCartsOutputOutput = await _cartsApi.CartsGetUserCartsPostAsync(
            getUserCartsInput: new GetUserCartsInput(
                staff.CompanyId,
                getUserCartsRequest.UserProfileId,
                "Active"));

        return Ok(getUserCartsOutputOutput);
    }

    public class UpdateCartRequest
    {
        [JsonProperty("store_id")]
        public string StoreId { get; set; }

        [JsonProperty("user_profile_id")]
        public string UserProfileId { get; set; }

        [JsonProperty("line_items")]
        public List<CartLineItem> LineItems { get; set; }

        [JsonProperty("cart_discount")]
        public Discount CartDiscount { get; set; }
    }

    [HttpPost("Carts/UpdateCart")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<UpdateCartOutputOutput>> UpdateCart(
        [FromBody]
        UpdateCartRequest updateCartRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var updateCartOutputOutput = await _cartsApi.CartsUpdateCartPostAsync(
            updateCartInput: new UpdateCartInput(
                staff.CompanyId,
                updateCartRequest.UserProfileId,
                updateCartRequest.StoreId,
                updateCartRequest.LineItems,
                updateCartRequest.CartDiscount));

        return Ok(updateCartOutputOutput);
    }

    public class UpdateCartItemRequest
    {
        [JsonProperty("store_id")]
        public string StoreId { get; set; }

        [JsonProperty("user_profile_id")]
        public string UserProfileId { get; set; }

        [JsonProperty("product_variant_id")]
        public string ProductVariantId { get; set; }

        [JsonProperty("product_id")]
        public string ProductId { get; set; }

        [JsonProperty("quantity")]
        public int Quantity { get; set; }
    }

    [HttpPost("Carts/UpdateCartItem")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<UpdateCartItemOutputOutput>> UpdateCartItem(
        [FromBody]
        UpdateCartItemRequest updateCartItemRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var updateCartItemOutputOutput = await _cartsApi.CartsUpdateCartItemPostAsync(
            updateCartItemInput: new UpdateCartItemInput(
                staff.CompanyId,
                updateCartItemRequest.UserProfileId,
                updateCartItemRequest.StoreId,
                updateCartItemRequest.ProductVariantId,
                updateCartItemRequest.ProductId,
                updateCartItemRequest.Quantity));

        return Ok(updateCartItemOutputOutput);
    }

    public class CreateCategoryRequest
    {
        [JsonProperty("store_id")]
        public string StoreId { get; set; }

        [JsonProperty("names")]
        public List<Multilingual> Names { get; set; }

        [JsonProperty("Descriptions")]
        public List<Description> Descriptions { get; set; }

        [JsonProperty("platform_data")]
        public PlatformData PlatformData { get; set; }

        [JsonProperty("metadata")]
        public Dictionary<string, object> Metadata { get; set; }
    }

    [HttpPost("Categories/CreateCategory")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<CreateCategoryOutputOutput>> CreateCategory(
        [FromBody]
        CreateCategoryRequest createCategoryRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var createCategoryOutputOutput = await _categoriesApi.CategoriesCreateCategoryPostAsync(
            createCategoryInput: new CreateCategoryInput(
                staff.CompanyId,
                createCategoryRequest.StoreId,
                createCategoryRequest.Names,
                createCategoryRequest.Descriptions,
                createCategoryRequest.PlatformData,
                createCategoryRequest.Metadata,
                staff.Id.ToString(),
                await GetTeamIdsBySleekflowStaffAsync(staff)));

        return Ok(createCategoryOutputOutput);
    }

    public class DeleteCategoryRequest
    {
        [JsonProperty("id")]
        public string Id { get; set; }

        [JsonProperty("store_id")]
        public string StoreId { get; set; }
    }

    [HttpPost("Categories/DeleteCategory")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<DeleteCategoryOutputOutput>> DeleteCategory(
        [FromBody]
        DeleteCategoryRequest deleteCategoryRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var deleteCategoryOutputOutput = await _categoriesApi.CategoriesDeleteCategoryPostAsync(
            deleteCategoryInput: new DeleteCategoryInput(
                deleteCategoryRequest.Id,
                deleteCategoryRequest.StoreId,
                staff.CompanyId,
                staff.Id.ToString(),
                await GetTeamIdsBySleekflowStaffAsync(staff)));

        return Ok(deleteCategoryOutputOutput);
    }

    public class GetCategoriesRequest
    {
        [JsonProperty("continuation_token")]
        public string ContinuationToken { get; set; }

        [JsonProperty("store_id")]
        public string StoreId { get; set; }

        [JsonProperty("limit")]
        public int Limit { get; set; }
    }

    [HttpPost("Categories/GetCategories")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GetCategoriesOutputOutput>> GetCategories(
        [FromBody]
        GetCategoriesRequest getCategoriesRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var getCategoriesOutputOutput = await _categoriesApi.CategoriesGetCategoriesPostAsync(
            getCategoriesInput: new GetCategoriesInput(
                getCategoriesRequest.ContinuationToken,
                staff.CompanyId,
                getCategoriesRequest.StoreId,
                getCategoriesRequest.Limit));

        return Ok(getCategoriesOutputOutput);
    }

    public class UpdateCategoryRequest
    {
        [JsonProperty("id")]
        public string Id;

        [JsonProperty("store_id")]
        public string StoreId { get; set; }

        [JsonProperty("names")]
        public List<Multilingual> Names;

        [JsonProperty("descriptions")]
        public List<Description> Descriptions;
    }

    [HttpPost("Categories/UpdateCategory")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<UpdateCategoryOutputOutput>> UpdateCategory(
        [FromBody]
        UpdateCategoryRequest updateCategoryRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var updateCategoryOutputOutput = await _categoriesApi.CategoriesUpdateCategoryPostAsync(
            updateCategoryInput: new UpdateCategoryInput(
                updateCategoryRequest.Id,
                updateCategoryRequest.StoreId,
                staff.CompanyId,
                updateCategoryRequest.Names,
                updateCategoryRequest.Descriptions,
                staff.Id.ToString(),
                await GetTeamIdsBySleekflowStaffAsync(staff)));

        return Ok(updateCategoryOutputOutput);
    }

    public class SearchCategoriesRequest
    {
        [JsonProperty("sleekflow_company_id")]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("store_id")]
        public string StoreId { get; set; }

        [JsonProperty("filter_groups")]
        public List<SearchFilterGroup> FilterGroups { get; set; }

        [JsonProperty("limit")]
        public int Limit { get; set; }

        [JsonProperty("continuation_token")]
        public string ContinuationToken { get; set; }

        [JsonProperty("search_text")]
        public string SearchText { get; set; }
    }

    [HttpPost("Categories/SearchCategories")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<SearchCategoriesOutputOutput>> SearchCategories(
        [FromBody]
        SearchCategoriesRequest searchCategoriesRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var searchCategoriesOutputOutput = await _categoriesApi.CategoriesSearchCategoriesPostAsync(
            searchCategoriesInput: new SearchCategoriesInput(
                staff.CompanyId,
                searchCategoriesRequest.StoreId,
                searchCategoriesRequest.FilterGroups,
                searchCategoriesRequest.ContinuationToken,
                searchCategoriesRequest.Limit,
                searchCategoriesRequest.SearchText));

        return Ok(searchCategoriesOutputOutput);
    }

    public class GetCurrenciesRequest
    {
    }

    [HttpPost("Currencies/GetCurrencies")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GetCurrenciesOutputOutput>> GetCurrencies(
        [FromBody]
        GetCurrenciesRequest getCurrenciesRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var getCurrenciesOutputOutput = await _currenciesApi.CurrenciesGetCurrenciesPostAsync(
            body: new object());

        return Ok(getCurrenciesOutputOutput);
    }

    public class GetSupportedCurrenciesRequest
    {
        [JsonProperty("store_id")]
        public string StoreId { get; set; }
    }

    [HttpPost("Currencies/GetSupportedCurrencies")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GetSupportedCurrenciesOutputOutput>> GetSupportedCurrencies(
        [FromBody]
        GetSupportedCurrenciesRequest getSupportedCurrenciesRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var getSupportedCurrenciesOutputOutput = await _currenciesApi.CurrenciesGetSupportedCurrenciesPostAsync(
            getSupportedCurrenciesInput: new GetSupportedCurrenciesInput(
                staff.CompanyId,
                getSupportedCurrenciesRequest.StoreId));

        return Ok(getSupportedCurrenciesOutputOutput);
    }

    public class GetCustomCatalogFilesRequest
    {
        [JsonProperty("store_id")]
        public string StoreId { get; set; }
    }

    [HttpPost("CustomCatalogs/GetCustomCatalogFiles")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GetCustomCatalogFilesOutputOutput>> GetCustomCatalogFiles(
        [FromBody]
        GetCustomCatalogFilesRequest getCustomCatalogFilesRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var getCustomCatalogFilesOutputOutput = await _customCatalogsApi.CustomCatalogsGetCustomCatalogFilesPostAsync(
            getCustomCatalogFilesInput: new GetCustomCatalogFilesInput(
                staff.CompanyId,
                getCustomCatalogFilesRequest.StoreId));

        return Ok(getCustomCatalogFilesOutputOutput);
    }

    [HttpGet("CustomCatalogs/GetCsvTemplate")]
    public async Task<ActionResult> GetCsvTemplate()
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var endpoint = _configuration.GetValue<string>("CommerceHub:Endpoint");

        return new RedirectResult(
            endpoint + "/CustomCatalogs/GetCsvTemplate",
            false);
    }

    [HttpGet("CustomCatalogs/GetCsvTemplateSample")]
    public async Task<ActionResult> GetCsvTemplateSample()
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var endpoint = _configuration.GetValue<string>("CommerceHub:Endpoint");

        return new RedirectResult(
            endpoint + "/CustomCatalogs/GetCsvTemplateSample",
            false);
    }

    public class ProcessCustomCatalogCsvRequest
    {
        [JsonProperty("sleekflow_company_id")]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("store_id")]
        public string StoreId { get; set; }

        [JsonProperty("blob_name")]
        public string BlobName { get; set; }
    }

    [HttpPost("CustomCatalogs/ProcessCustomCatalogCsv")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<ProcessCustomCatalogCsvOutputOutput>> ProcessCustomCatalogCsv(
        [FromBody]
        ProcessCustomCatalogCsvRequest processCustomCatalogCsvRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var processCustomCatalogCsvOutputOutput =
            await _customCatalogsApi.CustomCatalogsProcessCustomCatalogCsvPostAsync(
                processCustomCatalogCsvInput: new ProcessCustomCatalogCsvInput(
                    staff.CompanyId,
                    processCustomCatalogCsvRequest.StoreId,
                    processCustomCatalogCsvRequest.BlobName,
                    staff.Id.ToString(),
                    await GetTeamIdsBySleekflowStaffAsync(staff)));

        return Ok(processCustomCatalogCsvOutputOutput);
    }

    [HttpPost("Languages/GetLanguages")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GetLanguagesOutputOutput>> GetLanguages()
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var getLanguagesOutputOutput = await _languagesApi.LanguagesGetLanguagesPostAsync(
            body: new object());

        return Ok(getLanguagesOutputOutput);
    }

    public class CreateDraftOrderRequest
    {
        [JsonProperty("user_profile_id")]
        public string UserProfileId { get; set; }

        [JsonProperty("store_id")]
        public string StoreId { get; set; }

        [JsonProperty("line_items")]
        public List<OrderLineItemInputDto> LineItems { get; set; }

        [JsonProperty("discount")]
        public Discount Discount { get; set; }

        [JsonProperty("country_iso_code")]
        public string CountryIsoCode { get; set; }

        [JsonProperty("language_iso_code")]
        public string LanguageIsoCode { get; set; }

        [JsonProperty("currency_iso_code")]
        public string CurrencyIsoCode { get; set; }

        [JsonProperty("metadata")]
        public Dictionary<string, object> Metadata { get; set; }
    }

    [HttpPost("Orders/CreateDraftOrder")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<CreateDraftOrderOutputOutput>> CreateDraftOrder(
        [FromBody]
        CreateDraftOrderRequest createDraftOrderRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var userProfile = await _appDbContext.UserProfiles.FirstOrDefaultAsync(
            up =>
                up.Id == createDraftOrderRequest.UserProfileId
                && up.CompanyId == staff.CompanyId);

        var createDraftOrderOutputOutput = await _ordersApi.OrdersCreateDraftOrderPostAsync(
            createDraftOrderInput: new CreateDraftOrderInput(
                staff.CompanyId,
                createDraftOrderRequest.UserProfileId,
                new UserProfile(
                    userProfile.FirstName,
                    userProfile.LastName,
                    userProfile.PhoneNumber,
                    userProfile.Email),
                createDraftOrderRequest.StoreId,
                createDraftOrderRequest.CurrencyIsoCode,
                createDraftOrderRequest.CountryIsoCode,
                createDraftOrderRequest.LanguageIsoCode,
                staff.Id.ToString(),
                await GetTeamIdsBySleekflowStaffAsync(staff)));

        return Ok(createDraftOrderOutputOutput);
    }

    public class GetUserOrdersRequest
    {
        [JsonProperty("user_profile_id")]
        public string UserProfileId { get; set; }
    }

    [HttpPost("Orders/GetUserOrders")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GetUserOrdersOutputOutput>> GetUserOrders(
        [FromBody]
        GetUserOrdersRequest getUserOrdersRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var getUserOrdersOutputOutput = await _ordersApi.OrdersGetUserOrdersPostAsync(
            getUserOrdersInput: new GetUserOrdersInput(
                staff.CompanyId,
                getUserOrdersRequest.UserProfileId));

        return Ok(getUserOrdersOutputOutput);
    }

    [HttpPost("Orders/GetMyUserOrders")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GetUserOrdersOutputOutput>> GetMyUserOrders()
    {
        var user = await _userManager.GetUserAsync(User);
        if (user == null)
        {
            return Unauthorized();
        }

        var getMyUserOrdersOutputOutput = await _ordersApi.OrdersGetUserOrdersPostAsync(
            getUserOrdersInput: new GetUserOrdersInput(
                user.CompanyId,
                user.Id));

        return Ok(getMyUserOrdersOutputOutput);
    }

    public class UpdateOrderRequest
    {
        [JsonProperty("id")]
        public string Id { get; set; }

        [JsonProperty("user_profile_id")]
        public string UserProfileId { get; set; }

        [JsonProperty("store_id")]
        public string StoreId { get; set; }

        [JsonProperty("line_items")]
        public List<OrderLineItemInputDto> LineItems { get; set; }

        [JsonProperty("discount")]
        public Discount Discount { get; set; }

        [JsonProperty("country_iso_code")]
        public string CountryIsoCode { get; set; }

        [JsonProperty("language_iso_code")]
        public string LanguageIsoCode { get; set; }

        [JsonProperty("currency_iso_code")]
        public string CurrencyIsoCode { get; set; }

        [JsonProperty("metadata")]
        public Dictionary<string, object> Metadata { get; set; }
    }

    [HttpPost("Orders/UpdateOrder")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<UpdateOrderOutputOutput>> UpdateOrder(
        [FromBody]
        UpdateOrderRequest updateOrderRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var userProfile = await _appDbContext.UserProfiles.FirstOrDefaultAsync(
            up =>
                up.Id == updateOrderRequest.UserProfileId
                && up.CompanyId == staff.CompanyId);

        var updateOrderOutputOutput = await _ordersApi.OrdersUpdateOrderPostAsync(
            updateOrderInput: new UpdateOrderInput(
                updateOrderRequest.Id,
                staff.Id.ToString(),
                await GetTeamIdsBySleekflowStaffAsync(staff),
                staff.CompanyId,
                updateOrderRequest.UserProfileId,
                new UserProfile(
                    userProfile.FirstName,
                    userProfile.LastName,
                    userProfile.PhoneNumber,
                    userProfile.Email),
                updateOrderRequest.StoreId,
                updateOrderRequest.LineItems,
                updateOrderRequest.Discount,
                updateOrderRequest.CountryIsoCode,
                updateOrderRequest.LanguageIsoCode,
                updateOrderRequest.CurrencyIsoCode,
                updateOrderRequest.Metadata));

        return Ok(updateOrderOutputOutput);
    }

    public class CreateStripePaymentProviderConfigRequest
    {
        [JsonProperty("api_key")]
        public string ApiKey { get; set; }

        [JsonProperty("connect_webhook_secret")]
        public string ConnectWebhookSecret { get; set; }

        [JsonProperty("payment_webhook_secret")]
        public string PaymentWebhookSecret { get; set; }

        [JsonProperty("application_fee_rate")]
        public decimal ApplicationFeeRate { get; set; }

        [JsonProperty("is_shipping_enabled")]
        public bool IsShippingEnabled { get; set; }

        [JsonProperty("shipping_allowed_country_iso_codes")]
        public List<string> ShippingAllowedCountryIsoCodes { get; set; }

        [JsonProperty("is_inventory_enabled")]
        public bool IsInventoryEnabled { get; set; }

        [JsonProperty("staff_id")]
        public string SleekflowStaffId { get; set; }

        [JsonProperty("sleekflow_staff_team_ids")]
        public List<string> SleekflowStaffTeamIds { get; set; }
    }

    [HttpPost("PaymentProviderConfigs/CreateStripePaymentProviderConfig")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<CreateStripePaymentProviderConfigOutputOutput>> CreateStripePaymentProviderConfig(
        [FromBody]
        CreateStripePaymentProviderConfigRequest createStripePaymentProviderConfigRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var createStripePaymentProviderConfigOutputOutput = await _paymentProviderConfigsApi
            .PaymentProviderConfigsCreateStripePaymentProviderConfigPostAsync(
                createStripePaymentProviderConfigInput: new CreateStripePaymentProviderConfigInput(
                    staff.CompanyId,
                    createStripePaymentProviderConfigRequest.ApiKey,
                    createStripePaymentProviderConfigRequest.ConnectWebhookSecret,
                    createStripePaymentProviderConfigRequest.PaymentWebhookSecret,
                    createStripePaymentProviderConfigRequest.ApplicationFeeRate,
                    createStripePaymentProviderConfigRequest.IsShippingEnabled,
                    createStripePaymentProviderConfigRequest.ShippingAllowedCountryIsoCodes,
                    createStripePaymentProviderConfigRequest.IsInventoryEnabled,
                    staff.Id.ToString(),
                    await GetTeamIdsBySleekflowStaffAsync(staff)));

        return Ok(createStripePaymentProviderConfigOutputOutput);
    }

    public class CreateStripeConnectPaymentProviderConfigRequest
    {
        [JsonProperty("platform_country")]
        public string PlatformCountry { get; set; }

        [JsonProperty("application_fee_rate")]
        public decimal ApplicationFeeRate { get; set; }

        [JsonProperty("is_shipping_enabled")]
        public bool IsShippingEnabled { get; set; }

        [JsonProperty("shipping_allowed_country_iso_codes")]
        public List<string> ShippingAllowedCountryIsoCodes { get; set; }

        [JsonProperty("is_inventory_enabled")]
        public bool IsInventoryEnabled { get; set; }
    }

    [HttpPost("PaymentProviderConfigs/CreateStripeConnectPaymentProviderConfig")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<CreateStripeConnectPaymentProviderConfigOutputOutput>>
        CreateStripeConnectPaymentProviderConfig(
            [FromBody]
            CreateStripeConnectPaymentProviderConfigRequest createStripeConnectPaymentProviderConfigRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var createStripeConnectPaymentProviderConfigOutputOutput = await _paymentProviderConfigsApi
            .PaymentProviderConfigsCreateStripeConnectPaymentProviderConfigPostAsync(
                createStripeConnectPaymentProviderConfigInput: new CreateStripeConnectPaymentProviderConfigInput(
                    staff.CompanyId,
                    createStripeConnectPaymentProviderConfigRequest.PlatformCountry,
                    createStripeConnectPaymentProviderConfigRequest.ApplicationFeeRate,
                    createStripeConnectPaymentProviderConfigRequest.IsShippingEnabled,
                    createStripeConnectPaymentProviderConfigRequest.ShippingAllowedCountryIsoCodes,
                    createStripeConnectPaymentProviderConfigRequest.IsInventoryEnabled,
                    staff.Id.ToString(),
                    await GetTeamIdsBySleekflowStaffAsync(staff)));

        return Ok(createStripeConnectPaymentProviderConfigOutputOutput);
    }

    public class DeletePaymentProviderConfigRequest
    {
        [JsonProperty("id")]
        public string Id { get; set; }
    }

    [HttpPost("PaymentProviderConfigs/DeletePaymentProviderConfig")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<DeletePaymentProviderConfigOutputOutput>> DeletePaymentProviderConfig(
        [FromBody]
        DeletePaymentProviderConfigRequest deletePaymentProviderConfigRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var deletePaymentProviderConfigOutputOutput =
            await _paymentProviderConfigsApi.PaymentProviderConfigsDeletePaymentProviderConfigPostAsync(
                deletePaymentProviderConfigInput: new DeletePaymentProviderConfigInput(
                    staff.CompanyId,
                    deletePaymentProviderConfigRequest.Id,
                    staff.Id.ToString(),
                    await GetTeamIdsBySleekflowStaffAsync(staff)));

        return Ok(deletePaymentProviderConfigOutputOutput);
    }

    public class GetPaymentProviderConfigsRequest
    {
    }

    [HttpPost("PaymentProviderConfigs/GetPaymentProviderConfigs")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GetPaymentProviderConfigsOutputOutput>> GetPaymentProviderConfigs(
        [FromBody]
        GetPaymentProviderConfigsRequest getPaymentProviderConfigsRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var getPaymentProviderConfigsOutputOutput =
            await _paymentProviderConfigsApi.PaymentProviderConfigsGetPaymentProviderConfigsPostAsync(
                getPaymentProviderConfigsInput: new GetPaymentProviderConfigsInput(
                    staff.CompanyId,
                    staff.Id.ToString(),
                    await GetTeamIdsBySleekflowStaffAsync(staff)));

        return Ok(getPaymentProviderConfigsOutputOutput);
    }

    public class LinkPaymentProviderConfigRequest
    {
        [JsonProperty("payment_provider_config_id")]
        public string PaymentProviderConfigId { get; set; }

        [JsonProperty("store_id")]
        public string StoreId { get; set; }

        [JsonProperty("currency_iso_codes")]
        public List<string> CurrencyIsoCodes { get; set; }
    }

    [HttpPost("PaymentProviderConfigs/LinkPaymentProviderConfig")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<LinkPaymentProviderConfigOutputOutput>> LinkPaymentProviderConfig(
        [FromBody]
        LinkPaymentProviderConfigRequest linkPaymentProviderConfigRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var linkPaymentProviderConfigOutputOutput =
            await _paymentProviderConfigsApi.PaymentProviderConfigsLinkPaymentProviderConfigPostAsync(
                linkPaymentProviderConfigInput: new LinkPaymentProviderConfigInput(
                    linkPaymentProviderConfigRequest.PaymentProviderConfigId,
                    linkPaymentProviderConfigRequest.StoreId,
                    linkPaymentProviderConfigRequest.CurrencyIsoCodes,
                    staff.CompanyId,
                    staff.Id.ToString(),
                    await GetTeamIdsBySleekflowStaffAsync(staff)));

        return Ok(linkPaymentProviderConfigOutputOutput);
    }

    public class CreateOrderPaymentRequest
    {
        [JsonProperty("user_profile_id")]
        public string UserProfileId { get; set; }

        [JsonProperty("order_id")]
        public string OrderId { get; set; }

        [JsonProperty("payment_provider_name")]
        public string PaymentProviderName { get; set; }

        [JsonProperty("language_iso_code")]
        public string LanguageIsoCode { get; set; }

        [JsonProperty("expired_at")]
        public DateTimeOffset ExpiredAt { get; set; }
    }

    [HttpPost("Payments/CreateOrderPayment")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<CreateOrderPaymentOutputOutput>> CreateOrderPayment(
        [FromBody]
        CreateOrderPaymentRequest createOrderPaymentRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var createOrderPaymentOutputOutput =
            await _paymentsApi.PaymentsCreateOrderPaymentPostAsync(
                createOrderPaymentInput: new CreateOrderPaymentInput(
                    staff.CompanyId,
                    createOrderPaymentRequest.UserProfileId,
                    createOrderPaymentRequest.OrderId,
                    createOrderPaymentRequest.PaymentProviderName,
                    createOrderPaymentRequest.LanguageIsoCode,
                    createOrderPaymentRequest.ExpiredAt,
                    staff.Id.ToString(),
                    await GetTeamIdsBySleekflowStaffAsync(staff)));

        return Ok(createOrderPaymentOutputOutput);
    }

    public class RefundOrderPaymentRequest
    {
        [JsonProperty("user_profile_id")]
        public string UserProfileId { get; set; }

        [JsonProperty("order_id")]
        public string OrderId { get; set; }

        [JsonProperty("payment_provider_name")]
        public string PaymentProviderName { get; set; }

        [JsonProperty("amount")]
        public decimal Amount { get; set; }

        [JsonProperty("reason")]
        public string Reason { get; set; }
    }

    [HttpPost("Payments/RefundOrderPayment")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<RefundOrderPaymentOutputOutput>> RefundOrderPayment(
        [FromBody]
        RefundOrderPaymentRequest refundOrderPaymentRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var refundOrderPaymentOutputOutput =
            await _paymentsApi.PaymentsRefundOrderPaymentPostAsync(
                refundOrderPaymentInput: new RefundOrderPaymentInput(
                    staff.CompanyId,
                    refundOrderPaymentRequest.UserProfileId,
                    refundOrderPaymentRequest.OrderId,
                    refundOrderPaymentRequest.PaymentProviderName,
                    refundOrderPaymentRequest.Amount,
                    refundOrderPaymentRequest.Reason,
                    staff.Id.ToString(),
                    await GetTeamIdsBySleekflowStaffAsync(staff)));

        return Ok(refundOrderPaymentOutputOutput);
    }

    public class CreateDefaultProductRequest
    {
        [JsonProperty("store_id")]
        public string StoreId { get; set; }

        [JsonProperty("category_ids")]
        public List<string> CategoryIds { get; set; }

        [JsonProperty("sku")]
        public string Sku { get; set; }

        [JsonProperty("url")]
        public string Url { get; set; }

        [JsonProperty("names")]
        public List<Multilingual> Names { get; set; }

        [JsonProperty("descriptions")]
        public List<Description> Descriptions { get; set; }

        [JsonProperty("images")]
        public List<ImageDto> Images { get; set; }

        [JsonProperty("prices")]
        public List<Price> Prices { get; set; }

        [JsonProperty("attributes")]
        public List<ProductVariantAttribute> Attributes { get; set; }

        [JsonProperty("platform_data")]
        public PlatformData PlatformData { get; set; }

        [JsonProperty("metadata")]
        public Dictionary<string, object> Metadata { get; set; }
    }

    [HttpPost("Products/CreateDefaultProduct")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<CreateDefaultProductOutputOutput>> CreateDefaultProduct(
        [FromBody]
        CreateDefaultProductRequest createDefaultProductRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var createDefaultProductOutputOutput = await _productsApi.ProductsCreateDefaultProductPostAsync(
            createDefaultProductInput: new CreateDefaultProductInput(
                staff.CompanyId,
                createDefaultProductRequest.StoreId,
                createDefaultProductRequest.Prices,
                createDefaultProductRequest.Attributes,
                staff.Id.ToString(),
                await GetTeamIdsBySleekflowStaffAsync(staff),
                createDefaultProductRequest.CategoryIds,
                createDefaultProductRequest.Sku,
                createDefaultProductRequest.Url,
                createDefaultProductRequest.Names,
                createDefaultProductRequest.Descriptions,
                createDefaultProductRequest.Images,
                createDefaultProductRequest.Metadata));

        return Ok(createDefaultProductOutputOutput);
    }

    public class CreateProductRequest
    {
        [JsonProperty("store_id")]
        public string StoreId { get; set; }

        [JsonProperty("category_ids")]
        public List<string> CategoryIds { get; set; }

        [JsonProperty("sku")]
        public string Sku { get; set; }

        [JsonProperty("url")]
        public string Url { get; set; }

        [JsonProperty("names")]
        public List<Multilingual> Names { get; set; }

        [JsonProperty("descriptions")]
        public List<Description> Descriptions { get; set; }

        [JsonProperty("images")]
        public List<ImageDto> Images { get; set; }

        [JsonProperty("product_variants")]
        public List<CreateProductInputProductVariant> ProductVariants { get; set; }

        [JsonProperty("platform_data")]
        public PlatformData PlatformData { get; set; }

        [JsonProperty("metadata")]
        public Dictionary<string, object> Metadata { get; set; }

        [JsonProperty("is_view_enabled")]
        public bool IsViewEnabled { get; set; }
    }

    [HttpPost("Products/CreateProduct")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<CreateProductOutputOutput>> CreateProduct(
        [FromBody]
        CreateProductRequest createProductRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var createProductOutputOutput = await _productsApi.ProductsCreateProductPostAsync(
            createProductInput: new CreateProductInput(
                staff.CompanyId,
                createProductRequest.StoreId,
                createProductRequest.ProductVariants,
                createProductRequest.PlatformData,
                createProductRequest.IsViewEnabled,
                staff.Id.ToString(),
                await GetTeamIdsBySleekflowStaffAsync(staff),
                createProductRequest.CategoryIds,
                createProductRequest.Sku,
                createProductRequest.Url,
                createProductRequest.Names,
                createProductRequest.Descriptions,
                createProductRequest.Images,
                createProductRequest.Metadata));

        return Ok(createProductOutputOutput);
    }

    public class DeleteProductRequest
    {
        [JsonProperty("id")]
        public string Id { get; set; }

        [JsonProperty("store_id")]
        public string StoreId { get; set; }
    }

    [HttpPost("Products/DeleteProduct")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<DeleteProductOutputOutput>> DeleteProduct(
        [FromBody]
        DeleteProductRequest deleteProductRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var deleteProductOutputOutput = await _productsApi.ProductsDeleteProductPostAsync(
            deleteProductInput: new DeleteProductInput(
                deleteProductRequest.Id,
                deleteProductRequest.StoreId,
                staff.CompanyId,
                staff.Id.ToString(),
                await GetTeamIdsBySleekflowStaffAsync(staff)));

        return Ok(deleteProductOutputOutput);
    }

    public class DeleteProductsRequest
    {
        [JsonProperty("store_id")]
        public string StoreId { get; set; }

        [JsonProperty("product_ids")]
        public List<string> ProductIds { get; set; }
    }

    [HttpPost("Products/DeleteProducts")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<DeleteProductsOutputOutput>> DeleteProducts(
        [FromBody]
        DeleteProductsRequest deleteProductsRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var deleteProductsOutputOutput = await _productsApi.ProductsDeleteProductsPostAsync(
            deleteProductsInput: new DeleteProductsInput(
                staff.CompanyId,
                deleteProductsRequest.StoreId,
                deleteProductsRequest.ProductIds,
                staff.Id.ToString(),
                await GetTeamIdsBySleekflowStaffAsync(staff)));

        return Ok(deleteProductsOutputOutput);
    }

    public class DuplicateProductsRequest
    {
        [JsonProperty("store_id")]
        public string StoreId { get; set; }

        [JsonProperty("product_ids")]
        public List<string> ProductIds { get; set; }
    }

    [HttpPost("Products/DuplicateProducts")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<DuplicateProductsOutputOutput>> DuplicateProducts(
        [FromBody]
        DuplicateProductsRequest duplicateProductsRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var duplicateProductsOutputOutput = await _productsApi.ProductsDuplicateProductsPostAsync(
            duplicateProductsInput: new DuplicateProductsInput(
                staff.CompanyId,
                duplicateProductsRequest.StoreId,
                duplicateProductsRequest.ProductIds,
                staff.Id.ToString(),
                await GetTeamIdsBySleekflowStaffAsync(staff)));

        return Ok(duplicateProductsOutputOutput);
    }

    public class GetProductRequest
    {
        [JsonProperty("store_id")]
        public string StoreId { get; set; }

        [JsonProperty("product_id")]
        public string ProductId { get; set; }
    }

    [HttpPost("Products/GetProduct")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GetProductOutputOutput>> GetProduct(
        [FromBody]
        GetProductRequest getProductRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var getProductOutputOutput = await _productsApi.ProductsGetProductPostAsync(
            getProductInput: new GetProductInput(
                staff.CompanyId,
                getProductRequest.StoreId,
                getProductRequest.ProductId));

        return Ok(getProductOutputOutput);
    }

    public class GetProductMessagePreviewRequest
    {
        [JsonProperty("store_id")]
        public string StoreId { get; set; }

        [JsonProperty("product_variant_id")]
        public string ProductVariantId { get; set; }

        [JsonProperty("product_id")]
        public string ProductId { get; set; }

        [JsonProperty("currency_iso_code")]
        public string CurrencyIsoCode { get; set; }

        [JsonProperty("language_iso_code")]
        public string LanguageIsoCode { get; set; }
    }

    [HttpPost("Products/GetProductMessagePreview")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GetProductMessagePreviewOutputOutput>> GetProductMessagePreview(
        [FromBody]
        GetProductMessagePreviewRequest getProductMessagePreviewRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var getProductMessagePreviewOutputOutput = await _productsApi.ProductsGetProductMessagePreviewPostAsync(
            getProductMessagePreviewInput: new GetProductMessagePreviewInput(
                staff.CompanyId,
                getProductMessagePreviewRequest.StoreId,
                getProductMessagePreviewRequest.ProductVariantId,
                getProductMessagePreviewRequest.ProductId,
                getProductMessagePreviewRequest.CurrencyIsoCode,
                getProductMessagePreviewRequest.LanguageIsoCode));

        return Ok(getProductMessagePreviewOutputOutput);
    }

    public class GetProductsRequest
    {
        [JsonProperty("continuation_token")]
        public string ContinuationToken { get; set; }

        [JsonProperty("store_id")]
        public string StoreId { get; set; }

        [JsonProperty("limit")]
        public int Limit { get; set; }

        [JsonProperty("filter_groups")]
        public List<GetProductsInputFilterGroup> FilterGroups { get; set; }

        [JsonProperty("sorts")]
        public List<Sort> Sorts { get; set; }
    }

    [HttpPost("Products/GetProducts")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GetProductsOutputOutput>> GetProducts(
        [FromBody]
        GetProductsRequest getProductsRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var getProductsOutputOutput = await _productsApi.ProductsGetProductsPostAsync(
            getProductsInput: new GetProductsInput(
                getProductsRequest.ContinuationToken,
                staff.CompanyId,
                getProductsRequest.StoreId,
                getProductsRequest.Limit,
                getProductsRequest.FilterGroups,
                getProductsRequest.Sorts));

        return Ok(getProductsOutputOutput);
    }

    public class UpdateDefaultProductRequest
    {
        [JsonProperty("id")]
        public string Id { get; set; }

        [JsonProperty("url")]
        public string Url { get; set; }

        [JsonProperty("store_id")]
        public string StoreId { get; set; }

        [JsonProperty("category_ids")]
        public List<string> CategoryIds { get; set; }

        [JsonProperty("names")]
        public List<Multilingual> Names { get; set; }

        [JsonProperty("descriptions")]
        public List<Description> Descriptions { get; set; }

        [JsonProperty("images")]
        public List<ImageDto> Images { get; set; }

        [JsonProperty("prices")]
        public List<Price> Prices { get; set; }

        [JsonProperty("attributes")]
        public List<ProductVariantAttribute> Attributes { get; set; }

        [JsonProperty("metadata")]
        public Dictionary<string, object> Metadata { get; set; }

        [JsonProperty("is_view_enabled")]
        public bool IsViewEnabled { get; set; }

        [JsonProperty("sku")]
        public string Sku { get; set; }
    }

    [HttpPost("Products/UpdateDefaultProduct")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<UpdateDefaultProductOutputOutput>> UpdateDefaultProduct(
        [FromBody]
        UpdateDefaultProductRequest updateDefaultProductRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var updateDefaultProductOutputOutput = await _productsApi.ProductsUpdateDefaultProductPostAsync(
            updateDefaultProductInput: new UpdateDefaultProductInput(
                updateDefaultProductRequest.Id,
                staff.CompanyId,
                updateDefaultProductRequest.StoreId,
                updateDefaultProductRequest.IsViewEnabled,
                updateDefaultProductRequest.Prices,
                updateDefaultProductRequest.Attributes,
                staff.Id.ToString(),
                await GetTeamIdsBySleekflowStaffAsync(staff),
                updateDefaultProductRequest.CategoryIds,
                updateDefaultProductRequest.Sku,
                updateDefaultProductRequest.Url,
                updateDefaultProductRequest.Names,
                updateDefaultProductRequest.Descriptions,
                updateDefaultProductRequest.Images,
                updateDefaultProductRequest.Metadata));

        return Ok(updateDefaultProductOutputOutput);
    }

    public class UpdateProductRequest
    {
        [JsonProperty("id")]
        public string Id { get; set; }

        [JsonProperty("store_id")]
        public string StoreId { get; set; }

        [JsonProperty("category_ids")]
        public List<string> CategoryIds { get; set; }

        [JsonProperty("url")]
        public string Url { get; set; }

        [JsonProperty("names")]
        public List<Multilingual> Names { get; set; }

        [JsonProperty("descriptions")]
        public List<Description> Descriptions { get; set; }

        [JsonProperty("images")]
        public List<ImageDto> Images { get; set; }

        [JsonProperty("sku")]
        public string Sku { get; set; }

        [JsonProperty("is_view_enabled")]
        public bool IsViewEnabled { get; set; }
    }

    [HttpPost("Products/UpdateProduct")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<UpdateProductOutputOutput>> UpdateProduct(
        [FromBody]
        UpdateProductRequest updateProductRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var updateProductOutputOutput = await _productsApi.ProductsUpdateProductPostAsync(
            updateProductInput: new UpdateProductInput(
                updateProductRequest.Id,
                staff.CompanyId,
                updateProductRequest.StoreId,
                updateProductRequest.IsViewEnabled,
                staff.Id.ToString(),
                await GetTeamIdsBySleekflowStaffAsync(staff),
                updateProductRequest.CategoryIds,
                updateProductRequest.Sku,
                updateProductRequest.Url,
                updateProductRequest.Names,
                updateProductRequest.Descriptions,
                updateProductRequest.Images));

        return Ok(updateProductOutputOutput);
    }

    public class SearchProductsRequest
    {
        [JsonProperty("store_id")]
        public string StoreId { get; set; }

        [JsonProperty("filter_groups")]
        public List<SearchFilterGroup> FilterGroups { get; set; }

        [JsonProperty("limit")]
        public int Limit { get; set; }

        [JsonProperty("continuation_token")]
        public string ContinuationToken { get; set; }

        [JsonProperty("search_text")]
        public string SearchText { get; set; }
    }

    [HttpPost("Products/SearchProducts")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<SearchProductsOutputOutput>> SearchProducts(
        [FromBody]
        SearchProductsRequest searchProductsRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var searchProductsOutputOutput = await _productsApi.ProductsSearchProductsPostAsync(
            searchProductsInput: new SearchProductsInput(
                staff.CompanyId,
                searchProductsRequest.StoreId,
                searchProductsRequest.FilterGroups,
                searchProductsRequest.ContinuationToken,
                searchProductsRequest.Limit,
                searchProductsRequest.SearchText));

        return Ok(searchProductsOutputOutput);
    }

    public class CreateProductVariantRequest
    {
        [JsonProperty("sku")]
        public string Sku { get; set; }

        [JsonProperty("prices")]
        public List<Price> Prices { get; set; }

        [JsonProperty("attributes")]
        public List<ProductVariantAttribute> Attributes { get; set; }

        [JsonProperty("names")]
        public List<Multilingual> Names { get; set; }

        [JsonProperty("descriptions")]
        public List<Description> Descriptions { get; set; }

        [JsonProperty("images")]
        public List<ImageDto> Images { get; set; }

        [JsonProperty("platform_data")]
        public PlatformData PlatformData { get; set; }

        [JsonProperty("category_ids")]
        public List<string> CategoryIds { get; set; }

        [JsonProperty("store_id")]
        public string StoreId { get; set; }

        [JsonProperty("product_id")]
        public string ProductId { get; set; }

        [JsonProperty("url")]
        public string Url { get; set; }
    }

    [HttpPost("ProductVariants/CreateProductVariant")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<CreateProductVariantOutputOutput>> CreateProductVariant(
        [FromBody]
        CreateProductVariantRequest createProductVariantRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var createProductVariantOutputOutput = await _productVariantsApi.ProductVariantsCreateProductVariantPostAsync(
            createProductVariantInput: new CreateProductVariantInput(
                staff.CompanyId,
                createProductVariantRequest.StoreId,
                createProductVariantRequest.ProductId,
                staff.Id.ToString(),
                await GetTeamIdsBySleekflowStaffAsync(staff),
                createProductVariantRequest.Sku,
                createProductVariantRequest.Url,
                createProductVariantRequest.Prices,
                0,
                createProductVariantRequest.Attributes,
                createProductVariantRequest.Names,
                createProductVariantRequest.Descriptions,
                createProductVariantRequest.Images,
                createProductVariantRequest.PlatformData));

        return Ok(createProductVariantOutputOutput);
    }

    public class DeleteProductVariantRequest
    {
        [JsonProperty("id")]
        public string Id { get; set; }

        [JsonProperty("store_id")]
        public string StoreId { get; set; }

        [JsonProperty("product_id")]
        public string ProductId { get; set; }
    }

    [HttpPost("ProductVariants/DeleteProductVariant")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<DeleteProductVariantOutputOutput>> DeleteProductVariant(
        [FromBody]
        DeleteProductVariantRequest deleteProductVariantRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var deleteProductVariantOutputOutput = await _productVariantsApi.ProductVariantsDeleteProductVariantPostAsync(
            deleteProductVariantInput: new DeleteProductVariantInput(
                deleteProductVariantRequest.Id,
                deleteProductVariantRequest.StoreId,
                staff.CompanyId,
                deleteProductVariantRequest.ProductId,
                staff.Id.ToString(),
                await GetTeamIdsBySleekflowStaffAsync(staff)));

        return Ok(deleteProductVariantOutputOutput);
    }

    public class GetProductVariantsRequest
    {
        [JsonProperty("store_id")]
        public string StoreId { get; set; }

        [JsonProperty("product_id")]
        public string ProductId { get; set; }
    }

    [HttpPost("ProductVariants/GetProductVariants")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GetProductVariantsOutputOutput>> GetProductVariants(
        [FromBody]
        GetProductVariantsRequest getProductVariantsRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var getProductVariantsOutputOutput = await _productVariantsApi.ProductVariantsGetProductVariantsPostAsync(
            getProductVariantsInput: new GetProductVariantsInput(
                staff.CompanyId,
                getProductVariantsRequest.StoreId,
                getProductVariantsRequest.ProductId));

        return Ok(getProductVariantsOutputOutput);
    }

    public class UpdateProductVariantRequest
    {
        [JsonProperty("id")]
        public string Id { get; set; }

        [JsonProperty("store_id")]
        public string StoreId { get; set; }

        [JsonProperty("category_ids")]
        public List<string> CategoryIds { get; set; }

        [JsonProperty("product_id")]
        public string ProductId { get; set; }

        [JsonProperty("sku")]
        public string Sku { get; set; }

        [JsonProperty("prices")]
        public List<Price> Prices { get; set; }

        [JsonProperty("position")]
        public int Position { get; set; }

        [JsonProperty("attributes")]
        public List<ProductVariantAttribute> Attributes { get; set; }

        [JsonProperty("names")]
        public List<Multilingual> Names { get; set; }

        [JsonProperty("descriptions")]
        public List<Description> Descriptions { get; set; }

        [JsonProperty("images")]
        public List<ImageDto> Images { get; set; }

        [JsonProperty("platformData")]
        public PlatformData PlatformData { get; set; }

        [JsonProperty("url")]
        public string Url { get; set; }
    }

    [HttpPost("ProductVariants/UpdateProductVariant")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<UpdateProductVariantOutputOutput>> UpdateProductVariant(
        [FromBody]
        UpdateProductVariantRequest updateProductVariantRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var updateProductVariantOutputOutput = await _productVariantsApi.ProductVariantsUpdateProductVariantPostAsync(
            updateProductVariantInput: new UpdateProductVariantInput(
                updateProductVariantRequest.Id,
                staff.CompanyId,
                updateProductVariantRequest.StoreId,
                updateProductVariantRequest.ProductId,
                staff.Id.ToString(),
                await GetTeamIdsBySleekflowStaffAsync(staff),
                updateProductVariantRequest.Sku,
                updateProductVariantRequest.Url,
                updateProductVariantRequest.Prices,
                updateProductVariantRequest.Position,
                updateProductVariantRequest.Attributes,
                updateProductVariantRequest.Names,
                updateProductVariantRequest.Descriptions,
                updateProductVariantRequest.Images,
                updateProductVariantRequest.PlatformData));

        return Ok(updateProductVariantOutputOutput);
    }

    public class CreateStoreRequest
    {
        [JsonProperty("names")]
        public List<Multilingual> Names { get; set; }

        [JsonProperty("descriptions")]
        public List<Description> Descriptions { get; set; }

        [JsonProperty("is_view_enabled")]
        public bool IsViewEnabled { get; set; }

        [JsonProperty("is_payment_enabled")]
        public bool IsPaymentEnabled { get; set; }

        [JsonProperty("languages")]
        public List<LanguageInputDto> Languages { get; set; }

        [JsonProperty("currencies")]
        public List<CurrencyInputDto> Currencies { get; set; }

        [JsonProperty("template_dict")]
        public StoreTemplateDict TemplateDict { get; set; }

        [JsonProperty("metadata")]
        public Dictionary<string, object> Metadata { get; set; }
    }

    [HttpPost("Stores/CreateStore")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<CreateStoreOutputOutput>> CreateStore(
        [FromBody]
        CreateStoreRequest createStoreRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var createStoreOutputOutput = await _storesApi.StoresCreateStorePostAsync(
            createStoreInput: new CreateStoreInput(
                staff.CompanyId,
                staff.Id.ToString(),
                await GetTeamIdsBySleekflowStaffAsync(staff),
                createStoreRequest.Names,
                createStoreRequest.Descriptions,
                createStoreRequest.IsViewEnabled,
                createStoreRequest.IsPaymentEnabled,
                createStoreRequest.Languages,
                createStoreRequest.Currencies,
                createStoreRequest.TemplateDict,
                createStoreRequest.Metadata));

        return Ok(createStoreOutputOutput);
    }

    public class DeleteStoreRequest
    {
        [JsonProperty("id")]
        public string Id;
    }

    [HttpPost("Stores/DeleteStore")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<DeleteStoreOutputOutput>> DeleteStore(
        [FromBody]
        DeleteStoreRequest deleteStoreRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var deleteStoreOutputOutput = await _storesApi.StoresDeleteStorePostAsync(
            deleteStoreInput: new DeleteStoreInput(
                deleteStoreRequest.Id,
                staff.CompanyId,
                staff.Id.ToString(),
                await GetTeamIdsBySleekflowStaffAsync(staff)));

        return Ok(deleteStoreOutputOutput);
    }

    public class GetStoreRequest
    {
        [JsonProperty("id")]
        public string Id { get; set; }
    }

    [HttpPost("Stores/GetStore")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GetStoreOutput>> GetStore(
        [FromBody]
        GetStoreRequest getStoreRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var getStoreOutputOutput = await _storesApi.StoresGetStorePostAsync(
            getStoreInput: new GetStoreInput(
                getStoreRequest.Id,
                staff.CompanyId));

        return Ok(getStoreOutputOutput);
    }

    public class GetStoresRequest
    {
        [JsonProperty("continuation_token")]
        public string ContinuationToken { get; set; }

        [JsonProperty("limit")]
        public int Limit { get; set; }

        [JsonProperty("is_view_enabled")]
        public bool? IsViewEnabled { get; set; }

        [JsonProperty("is_payment_enabled")]
        public bool? IsPaymentEnabled { get; set; }
    }

    [HttpPost("Stores/GetStores")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GetStoresOutputOutput>> GetStores(
        [FromBody]
        GetStoresRequest getStoresRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var getStoresOutputOutput = await _storesApi.StoresGetStoresPostAsync(
            getStoresInput: new GetStoresInput(
                getStoresRequest.ContinuationToken,
                staff.CompanyId,
                getStoresRequest.IsViewEnabled,
                getStoresRequest.IsPaymentEnabled,
                getStoresRequest.Limit));

        return Ok(getStoresOutputOutput);
    }

    public class PreviewStoreTemplatesRequest
    {
        [JsonProperty("store_id")]
        public string StoreId { get; set; }
    }

    [HttpPost("Stores/PreviewStoreTemplates")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<PreviewStoreTemplatesOutputOutput>> PreviewStoreTemplates(
        [FromBody]
        PreviewStoreTemplatesRequest previewStoreTemplatesRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var previewStoreTemplatesOutputOutput = await _storesApi.StoresPreviewStoreTemplatesPostAsync(
            previewStoreTemplatesInput: new PreviewStoreTemplatesInput(
                previewStoreTemplatesRequest.StoreId,
                staff.CompanyId));

        return Ok(previewStoreTemplatesOutputOutput);
    }

    public class PreviewTemplateRequest
    {
        [JsonProperty("store_id")]
        public string StoreId { get; set; }

        [JsonProperty("template_id")]
        public string TemplateName { get; set; }

        [JsonProperty("templates")]
        public List<Multilingual> Templates { get; set; }
    }

    [HttpPost("Stores/PreviewTemplate")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<PreviewTemplateOutputOutput>> PreviewTemplate(
        [FromBody]
        PreviewTemplateRequest previewTemplateRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var previewTemplateOutputOutput = await _storesApi.StoresPreviewTemplatePostAsync(
            previewTemplateInput: new PreviewTemplateInput(
                previewTemplateRequest.StoreId,
                staff.CompanyId,
                previewTemplateRequest.TemplateName,
                previewTemplateRequest.Templates));

        return Ok(previewTemplateOutputOutput);
    }

    public class UpdateStoreRequest
    {
        [JsonProperty("id")]
        public string Id;

        [JsonProperty("names")]
        public List<Multilingual> Names { get; set; }

        [JsonProperty("descriptions")]
        public List<Description> Descriptions { get; set; }

        [JsonProperty("is_view_enabled")]
        public bool IsViewEnabled { get; set; }

        [JsonProperty("is_payment_enabled")]
        public bool IsPaymentEnabled { get; set; }

        [JsonProperty("languages")]
        public List<LanguageInputDto> Languages { get; set; }

        [JsonProperty("currencies")]
        public List<CurrencyInputDto> Currencies { get; set; }

        [JsonProperty("template_dict")]
        public StoreTemplateDict TemplateDict { get; set; }

        [JsonProperty("metadata")]
        public Dictionary<string, object> Metadata { get; set; }
    }

    [HttpPost("Stores/UpdateStore")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<UpdateStoreOutputOutput>> UpdateStore(
        [FromBody]
        UpdateStoreRequest updateStoreRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var updateStoreOutputOutput = await _storesApi.StoresUpdateStorePostAsync(
            updateStoreInput: new UpdateStoreInput(
                updateStoreRequest.Id,
                staff.CompanyId,
                staff.Id.ToString(),
                await GetTeamIdsBySleekflowStaffAsync(staff),
                updateStoreRequest.Names,
                updateStoreRequest.Descriptions,
                updateStoreRequest.IsViewEnabled,
                updateStoreRequest.IsPaymentEnabled,
                updateStoreRequest.Languages,
                updateStoreRequest.Currencies,
                updateStoreRequest.TemplateDict,
                updateStoreRequest.Metadata));

        return Ok(updateStoreOutputOutput);
    }

    [HttpPost("WebhookOnPaymentCompleted")]
    [Consumes("application/json")]
    [Produces("application/json")]
    [AllowAnonymous]
    public IActionResult WebhookOnPaymentCompleted(
        [FromBody]
        OnPaymentCompletedWebhookPayload onPaymentCompletedWebhookPayload)
    {
        try
        {
            _logger.LogInformation(
                "Started WebhookOnPaymentCompleted onPaymentCompletedWebhookPayload=[{}].",
                JsonConvert.SerializeObject(onPaymentCompletedWebhookPayload));

            BackgroundJob.Enqueue<CommerceHubJobs>(x => x.OnPaymentCompleted(onPaymentCompletedWebhookPayload));

            return Ok();
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "Failed WebhookOnPaymentCompleted onPaymentCompletedWebhookPayload=[{}].",
                JsonConvert.SerializeObject(onPaymentCompletedWebhookPayload));

            return Ok(ex.Message);
        }
    }

    public class GetCustomCatalogLimitsRequest
    {
    }

    [HttpPost("GetCustomCatalogLimits")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GetCustomCatalogLimitsOutputOutput>> GetCustomCatalogLimits(
        [FromBody]
        GetCustomCatalogLimitsRequest getCustomCatalogLimitsRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var getCustomCatalogLimitsOutputOutput = await _customCatalogConfigsApi
            .CustomCatalogConfigsGetCustomCatalogLimitsPostAsync(
                getCustomCatalogLimitsInput:
                new GetCustomCatalogLimitsInput(staff.CompanyId));
        return Ok(getCustomCatalogLimitsOutputOutput);
    }
}