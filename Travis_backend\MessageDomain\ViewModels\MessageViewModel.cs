﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.IO;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc.ModelBinding.Validation;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Sleekflow.Apis.MessagingHub.Model;
using Travis_backend.CommonDomain.Models;
using Travis_backend.Constants;
using Travis_backend.ConversationDomain.ViewModels;
using Travis_backend.Enums;
using Travis_backend.JsonConverters;
using Travis_backend.MessageDomain.Models;
using Travis_backend.StripeIntegrationDomain.Models;
using WABA360Dialog.ApiClient.Payloads.Models.MessageObjects.InteractiveObjects;
using TemplateMessageComponentObject =
    WABA360Dialog.ApiClient.Payloads.Models.MessageObjects.InteractiveObjects.TemplateMessageComponentObject;
using WhatsAppLanguage = WABA360Dialog.Common.Enums.WhatsAppLanguage;

namespace Travis_backend.MessageDomain.ViewModels
{
    public class ConversationTypingObject
    {
        public string ConversationId { get; set; }

        public string StaffId { get; set; }
    }

    public class SendMessageFromAPIWithBodyViewModel
    {
        public string MessageChecksum { get; set; }

        [Required]
        public string Channel { get; set; } = "naive";

        public string From { get; set; }

        public string To { get; set; }

        public string ConversationId { get; set; }

        public string FacebookReceiverId { get; set; }

        public string WebClientSenderId { get; set; }

        public string WebClientReceiverId { get; set; }

        public string WeChatReceiverOpenId { get; set; }

        public string LineReceiverId { get; set; }

        public string Subject { get; set; }

        [Required]
        public string MessageType { get; set; }

        public string MessageContent { get; set; }

        public IList<string> fileURLs { get; set; }

        // quoteMessage ChatAPI
        public string QuotedMsgId { get; set; }

        public string StaffUserId { get; set; }

        public string AssigneeId { get; set; }

        public List<string> AnalyticTags { get; set; }

        [ValidateNever]
        public SendMessageFromAPIExtendedMessageViewModel ExtendedMessage { get; set; }
    }

    public class SendMessageFromAPIExtendedMessageViewModel : Whatsapp360DialogExtendedMessagePayloadRequestViewModel
    {
    }

    public class SendMessageFromAPIViewModel
    {
        public string MessageChecksum { get; set; }

        public string Channel { get; set; } = "naive";

        public string From { get; set; }

        public string To { get; set; }

        public string ConversationId { get; set; }

        public string FacebookReceiverId { get; set; }

        public string WebClientSenderId { get; set; }

        public string WebClientReceiverId { get; set; }

        public string WeChatReceiverOpenId { get; set; }

        public string LineReceiverId { get; set; }

        public string Subject { get; set; }

        public string MessageType { get; set; }

        public string MessageContent { get; set; }

        public IList<IFormFile> files { get; set; }

        public IList<string> fileURLs { get; set; }

        // quoteMessage ChatAPI
        public string QuotedMsgId { get; set; }

        public string StaffUserId { get; set; }

        public string AssigneeId { get; set; }

        public string ExtendedMessageJson { get; set; } // ref. Whatsapp360DialogExtendedMessagePayloadRequestViewModel

        public List<string> AnalyticTags { get; set; }
    }

    public class ForwardMessageViewModel
    {
        public List<long> MessageIds { get; set; }

        public List<string> ConversationIds { get; set; }
    }

    public record ForwardMessageInput
    {
        public List<ForwardConversationMessage> ForwardConversationMessages { get; set; }
    }

    public record ForwardConversationMessage
    {
        public string ConversationId { get; set; }

        public long MessageId { get; set; }

        public string ChannelIdentityId { get; set; }

        public string ChannelType { get; set; }
    }

    public class DeleteMessageInput
    {
        public List<long> MessageIds { get; set; }
    }

    public class ExtendedConversationMessageViewModel : ConversationMessageViewModel
    {
        public Whatsapp360DialogExtendedMessagePayloadRequestViewModel Whatsapp360DialogExtendedMessagePayload
        {
            get;
            set;
        }

        public ExtendedMessagePayloadViewModel ExtendedMessagePayload { get; set; }
    }

    public class ConversationMessageViewModel
    {
        [Required]
        public string ConversationId { get; set; }

        public string MessageChecksum { get; set; }

        public string Channel { get; set; } = "naive"; // facebook; wechat

        public string MessageGroupName { get; set; }


        public string ReceiverDeviceUUID { get; set; }

        public string FacebookReceiverId { get; set; }

        public string WhatsappReceiverId { get; set; }

        public string Whatsapp360dialogReceiverId { get; set; }

        public string ReceiverId { get; set; }

        public string WebClientSenderId { get; set; }

        public string WebClientReceiverId { get; set; }

        public string WeChatReceiverOpenId { get; set; }

        public string LineReceiverId { get; set; }

        public string SMSReceiverId { get; set; }

        public string ViberReceiverId { get; set; }

        public string WhatsappCloudApiReceiverId { get; set; }

        public long? TelegramReceiverId { get; set; }

        public string EmailFrom { get; set; }

        public string EmailTo { get; set; }

        public string EmailCC { get; set; }

        public string Subject { get; set; }

        public string MessageType { get; set; }

        public string MessageContent { get; set; }

        public IList<IFormFile> files { get; set; }

        public IList<string> fileURLs { get; set; }

        public IList<string> fileNames { get; set; }

        public long? LocalTimestamp { get; set; }

        // quoteMessage ChatAPI
        public string QuotedMsgId { get; set; }

        public long? QuickReplyId { get; set; }

        public DateTime? ScheduleSentAt { get; set; }

        [Obsolete("Replaced with ChannelIdentityId")]
        public long? ChannelId { get; set; }

        public string ChannelIdentityId { get; set; }

        public string MessageTag { get; set; }

        public List<string> AnalyticTags { get; set; }

        #region SleekPay

        public string PaymentIntentId { get; set; }

        #endregion
    }

    public class Whatsapp360DialogExtendedMessagePayloadRequestViewModel
    {
        public Whatsapp360DialogTemplateMessageViewModel Whatsapp360DialogTemplateMessage { get; set; }

        public InteractiveObject Whatsapp360DialogInteractiveObject { get; set; }

        #region Cloud Api

        public WhatsappCloudApiTemplateMessageViewModel WhatsappCloudApiTemplateMessageObject { get; set; }

        public WhatsappCloudApiInteractiveObject WhatsappCloudApiInteractiveObject { get; set; }

        public List<WhatsappCloudApiContactObject> WhatsappCloudApiContactsObject { get; set; }

        public WhatsappCloudApiLocationObject WhatsappCloudApiLocationObject { get; set; }

        public WhatsappCloudApiReactionObject WhatsappCloudApiReactionObject { get; set; }

        #endregion

        #region TwilioContentAPI

        public WhatsappTwilioContentApiObject WhatsappTwilioContentApiObject { get; set; }

        #endregion
    }

    public class Whatsapp360DialogExtendedMessagePayloadViewModel
    {
        public long? Id { get; set; }

        public Whatsapp360DialogTemplateMessageViewModel Whatsapp360DialogTemplateMessage { get; set; }

        public InteractiveObject Whatsapp360DialogInteractiveObject { get; set; }

        public Whatsapp360DialogExtendedMessageReplyPayload ReplyPayload { get; set; }
    }

    public class Whatsapp360DialogTemplateMessageViewModel
    {
        public string TemplateNamespace { get; set; }

        public string TemplateName { get; set; }

        public WhatsAppLanguage Language { get; set; }

        public List<TemplateMessageComponentObject> Components { get; set; }
    }

    public class WhatsappCloudApiTemplateMessageViewModel
    {
        public string TemplateId { get; set; }

        public string TemplateName { get; set; }

        public string Language { get; set; }

        public List<WhatsappCloudApiTemplateMessageComponentObject> Components { get; set; }
    }

    public class MissingMessageViewModel
    {
        public List<ConversationMessageResponseViewModel> Messages { get; set; } =
            new List<ConversationMessageResponseViewModel>();

        public bool HasNext { get; set; }
    }

    public class ConversationMessageResponseViewModel
    {
        public long? Id { get; set; }

        public string CompanyId { get; set; }

        public string ConversationId { get; set; }

        public string MessageUniqueID { get; set; }

        public string MessageChecksum { get; set; }

        public string Channel { get; set; } = "naive";

        public string MessageChannel { get; set; }

        public UserInfoResponse? Sender { get; set; }

        public FacebookInfoResponse? facebookSender { get; set; }

        public FacebookInfoResponse? facebookReceiver { get; set; }

        public WhatsAppSenderResponse? whatsappSender { get; set; }

        public WhatsAppSenderResponse? whatsappReceiver { get; set; }

        public WebClientResponse? WebClientSender { get; set; }

        public WebClientResponse? WebClientReceiver { get; set; }

        public WeChatUserInfoResponse? WeChatSender { get; set; }

        public WeChatUserInfoResponse? WeChatReceiver { get; set; }

        public LineSenderResponse? LineSender { get; set; }

        public LineSenderResponse? LineReceiver { get; set; }

        public SMSSenderResponse? SMSSender { get; set; }

        public SMSSenderResponse? SMSReceiver { get; set; }

        public InstagramSenderResponse? InstagramSender { get; set; }

        public InstagramSenderResponse? InstagramReceiver { get; set; }

        public WhatsApp360DialogSenderResponse? Whatsapp360DialogSender { get; set; }

        public WhatsApp360DialogSenderResponse? Whatsapp360DialogReceiver { get; set; }

        public ConversationMessageDynamicChannelSender? DynamicChannelSender { get; set; }

        public WhatsappCloudApiSenderResponse? WhatsappCloudApiSender => !IsSentFromSleekflow
            ? WhatsappCloudApiSenderResponse.MapFrom(DynamicChannelSender)
            : null;

        public WhatsappCloudApiSenderResponse? WhatsappCloudApiReceiver => IsSentFromSleekflow
            ? WhatsappCloudApiSenderResponse.MapFrom(DynamicChannelSender)
            : null;

        public TelegramSenderResponse? TelegramSender { get; set; }

        public TelegramSenderResponse? TelegramReceiver { get; set; }

        public ViberSenderResponse? ViberSender { get; set; }

        public ViberSenderResponse? ViberReceiver { get; set; }

        // Email
        public EmailSenderResponse? EmailFrom { get; set; }

        public string EmailTo { get; set; }

        public string EmailCC { get; set; }

        public string Subject { get; set; }

        public StaffWithoutCompanyResponse? MessageAssignee { get; set; }

        public string MessageType { get; set; }

        public string DeliveryType { get; set; }

        public string MessageContent { get; set; }

        public List<UploadedFileResponse>? UploadedFiles { get; set; } = new List<UploadedFileResponse>();

        // public ConversationTicket ConversationTicket { get; set; }
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        public long Timestamp { get; set; } = DateTimeOffset.UtcNow.ToUnixTimeSeconds();

        public long? LocalTimestamp { get; set; }

        public string Status { get; set; }

        public string ChannelName
        {
            get
            {
                try
                {
                    if (Channel.ToLower() == ChannelTypes.WhatsappTwilio)
                    {
                        if (whatsappSender != null && whatsappSender.Id.Contains("whatsapp"))
                        {
                            return "whatsapp_twilio";
                        }
                        else if (whatsappReceiver != null && whatsappReceiver.Id.Contains("whatsapp"))
                        {
                            return "whatsapp_twilio";
                        }
                        else
                        {
                            return "whatsapp_chatapi";
                        }
                    }

                    return Channel;
                }
                catch
                {
                    return Channel;
                }
            }
        }

        public bool IsSentFromSleekflow { get; set; }

        public string ChannelStatusMessage { get; set; }

        public string QuotedMsgBody { get; set; }

        public string QuotedMsgId { get; set; }

        public bool IsSandbox { get; set; }

        public string StoryURL { get; set; }

        public DateTime? ScheduleSentAt { get; set; }

        public Whatsapp360DialogExtendedMessagePayloadViewModel? Whatsapp360DialogExtendedMessagePayload { get; set; }

        public long? SleekPayRecordId { get; set; }

        [NotMapped]
        public StripePaymentRecordResponse SleekPayRecord { get; set; }

        public string ChannelIdentityId { get; set; }

        public ExtendedMessagePayloadViewModel? ExtendedMessagePayload { get; set; }

        [JsonConverter(typeof(PreserveCasingDictionaryConverter))]
        public Dictionary<string, object> Metadata { get; set; }

        public List<string> AnalyticTags { get; set; }

        public bool IsFromImport { get; set; }
    }

    public class ConversationMessageWebhookResponse : ConversationMessageResponseViewModel
    {
        // Contact email for Nutrition Kitchen
        public string ContactEmail { get; set; } = string.Empty;

        public string FacebookSenderId => facebookSender?.Id;

        public string FacebookReceiverId => facebookReceiver?.Id;

        public string WhatsappFromId => whatsappSender?.Id;

        public string WhatsappFrom => whatsappSender?.phone_number;

        public string WhatsappFromName => whatsappSender?.name;

        public string WhatsappToId => whatsappReceiver?.Id;

        public string WhatsappTo => whatsappReceiver?.phone_number;

        public string WhatsappToName => whatsappReceiver?.name;

        public string WeChatFromId => WeChatSender?.openid;

        public string WeChatToId => WeChatReceiver?.openid;

        public string LineFromId => LineSender?.userId;

        public string LineToId => LineReceiver?.userId;

        public string Whatsapp360DialogFrom
        {
            get
            {
                if (IsSentFromSleekflow)
                {
                    return Whatsapp360DialogReceiver?.ChannelWhatsAppPhoneNumber;
                }

                return Whatsapp360DialogSender?.WhatsAppId;
            }
        }

        public string Whatsapp360DialogTo
        {
            get
            {
                if (IsSentFromSleekflow)
                {
                    return Whatsapp360DialogReceiver?.WhatsAppId;
                }

                return Whatsapp360DialogSender?.ChannelWhatsAppPhoneNumber;
            }
        }

        public string WhatsappCloudApiFrom
        {
            get
            {
                if (IsSentFromSleekflow)
                {
                    return WhatsappCloudApiReceiver?.WhatsappChannelPhoneNumber;
                }

                return WhatsappCloudApiSender?.WhatsappId;
            }
        }

        public string WhatsappCloudApiTo
        {
            get
            {
                if (IsSentFromSleekflow)
                {
                    return WhatsappCloudApiReceiver?.WhatsappId;
                }

                return WhatsappCloudApiSender?.WhatsappChannelPhoneNumber;
            }
        }

        public string To
        {
            get
            {
                return Channel switch
                {
                    ChannelTypes.Sms => null,
                    ChannelTypes.Facebook => null,
                    ChannelTypes.Instagram => null,
                    ChannelTypes.Wechat => null,
                    ChannelTypes.Line => null,
                    ChannelTypes.LiveChat => null,
                    ChannelTypes.Viber => null,
                    ChannelTypes.Telegram => null,
                    ChannelTypes.WhatsappTwilio => WhatsappTo,
                    ChannelTypes.Whatsapp360Dialog => Whatsapp360DialogTo,
                    ChannelTypes.WhatsappCloudApi => WhatsappCloudApiTo,
                    _ => null
                };
            }
        }

        public string From
        {
            get
            {
                return Channel switch
                {
                    ChannelTypes.Sms => null,
                    ChannelTypes.Facebook => null,
                    ChannelTypes.Instagram => null,
                    ChannelTypes.Wechat => null,
                    ChannelTypes.Line => null,
                    ChannelTypes.LiveChat => null,
                    ChannelTypes.Viber => null,
                    ChannelTypes.Telegram => null,
                    ChannelTypes.WhatsappTwilio => WhatsappFrom,
                    ChannelTypes.Whatsapp360Dialog => Whatsapp360DialogFrom,
                    ChannelTypes.WhatsappCloudApi => WhatsappCloudApiFrom,
                    _ => null
                };
            }
        }
    }

    public class UploadedFileResponse
    {
        public string FileId { get; set; } = Guid.NewGuid().ToString();

        public string Channel { get; set; } = "naive"; // facebook; wechat

        public string MessageTopic { get; set; }

        public string SenderDeviceUUID { get; set; }

        public UserDevice SenderDevice { get; set; }

        public string SenderId { get; set; }

        public UserInfoResponse Sender { get; set; }

        public string BlobContainer { get; set; }

        public string Filename { get; set; }

        public string Url { get; set; }

        public string MIMEType { get; set; }

        public long? FileSize { get; set; }

        public JObject? Metadata { get; set; }
    }

    public class ConversationHashtagViewModel
    {
        public string Id { get; set; }

        public string Hashtag { get; set; }

        public HashTagColor? HashTagColor { get; set; }

        public string HashtagNormalized { get { return string.IsNullOrEmpty(Hashtag) ? null : Hashtag.ToLower(); } }

        public HashTagType HashTagType { get; set; }
    }

    public class ConversationHashtagResponse
    {
        public string Id { get; set; }

        [JsonIgnore]
        public string ConversationId { get; set; }

        public string Hashtag { get; set; }

        public string HashTagColor { get; set; }

        public string HashtagNormalized { get { return string.IsNullOrEmpty(Hashtag) ? null : Hashtag.ToLower(); } }

        public string HashTagType { get; set; }
    }

    public class CompanyHashtagResponse
    {
        public string Id { get; set; }

        public string Hashtag { get; set; }

        public string HashTagColor { get; set; }

        public int? Count { get; set; }

        public string HashTagType { get; set; }
    }

    public class FileURLMessage
    {
        public string FileName { get; set; }

        public string MIMEType { get; set; }

        public string FileURL { get; set; }

        public Stream FileStream { get; set; }
    }

    public class FileMessageNameAndPath
    {
        private string FileName { get; }

        private string FilePath { get; }

        public FileMessageNameAndPath(string fileName, string filePath)
        {
            FileName = fileName;
            FilePath = filePath;
        }

        // Deconstruct method for destructuring
        public void Deconstruct(out string fileName, out string filePath)
        {
            fileName = FileName;
            filePath = FilePath;
        }
    }

    // public class ConversationTicketViewModel
    // {
    //    public string OperatorId { get; set; }
    //    public string Priority { get; set; } = "normal";
    //    public string Type { get; set; } = "task";
    //    public string Subject { get; set; }
    //    public RequestComment Comment { get; set; }
    //    public string StaffRemarks { get; set; }
    //    public IList<TicketCustomField> CustomFields { get; set; }
    //    public string Status { get; set; } = "open";
    // }
    public class RemarkViewModel
    {
        public string Remarks { get; set; }
    }

    public class ConversationNoteViewModel
    {
        public string MessageChecksum { get; set; }

        public string ConversationId { get; set; }

        public string AssigneeId { get; set; }

        public string Channel { get; set; } = ChannelTypes.Note; // facebook; wechat

        public MessageChannelType MessageChannel { get; set; } = MessageChannelType.note;

        public string MessageGroupName { get; set; }

        public string Subject { get; set; }

        public string MessageType { get; set; }

        /// <summary>
        /// MessageBody.
        /// </summary>
        public string MessageContent { get; set; }

        /// <summary>
        /// Attachment.
        /// </summary>
        public IList<IFormFile> files { get; set; }

        public List<string> fileUrls { get; set; }

        public List<string> fileNames { get; set; }

        public long? QuickReplyId { get; set; }

        public long? LocalTimestamp { get; set; }
    }

    public class ExportConversationViewModel
    {
        public string ConversationId { get; set; }

        public DateTime? Start { get; set; }

        public DateTime? End { get; set; }
    }

    public class IsConversationAccessibleRequest
    {
        public string ConversationId { get; set; }
    }

    public class IsConversationAccessibleResult
    {
        public bool IsAccessible { get; set; }

        public IsConversationAccessibleResult(bool isAccessible)
        {
            IsAccessible = isAccessible;
        }
    }

    public class SendFacebookOTNTopicViewModel
    {
        public string TopicId { get; set; } = Guid.NewGuid().ToString();

        [Required]
        public string PageId { get; set; }

        [Required]
        public string FacebookReceiverId { get; set; }

        [Required]
        public string ConversationId { get; set; }

        [Required]
        public string MessageCheckSum { get; set; }

        [Required]
        public string Topic { get; set; }

        [Required]
        [StringLength(65, ErrorMessage = "The title value cannot exceed 65 characters.")]
        public string Title { get; set; }

        public List<string> HashTagIds { get; set; }

        public string MessageTag { get; set; }
    }

    public class SendFacebookOTNTopicResponse
    {
        public string Id { get; set; }

        public string PageId { get; set; }

        public string FacebookReceiverId { get; set; }

        public string Topic { get; set; }

        public string TopicStatus { get; set; }

        public List<string> HashTagIds { get; set; }

        public bool? IsOtnRequestSent { get; set; }

        public bool? PermissionNotEnabled { get; set; }
    }

    public class GetFacebookOTNTopicViewModel
    {
        [Required]
        public string PageId { get; set; }

        [Required]
        public string FacebookReceiverId { get; set; }
    }

    public class FacebookOTNTopicTokenResponse
    {
        public string Id { get; set; }

        public string PageId { get; set; }

        public string FacebookReceiverId { get; set; }

        public string Topic { get; set; }

        public string TopicStatus { get; set; }

        public int TokenNumber { get; set; }

        public DateTime? ValidUntil { get; set; }
    }

    public class ExtendedMessagePayloadViewModel
    {
        public string Id { get; set; }

        [MaxLength(50)]
        public string Channel { get; set; }

        public ExtendedMessageType ExtendedMessageType { get; set; }

        [MaxLength(50)]
        public string FacebookOTNTopicId { get; set; }

        public ExtendedMessagePayloadDetail ExtendedMessagePayloadDetail { get; set; }
    }

    public class FacebookOtnTokenNumber
    {
        public string PageId { get; set; }

        public string FacebookReceiverId { get; set; }

        public int TokenNumber { get; set; }
    }

    public class ConversationMessageAckRequest
    {
        public List<long> MessageIds { get; set; }
    }

    public class JourneyBuilderMessageViewModel : SendMessageFromAPIViewModel
    {
        public string ApiKey { get; set; }

        public ExtendedMessagePayloadDetail TemplatePayload { get; set; }
    }
}