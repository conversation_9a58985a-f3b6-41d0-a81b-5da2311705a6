﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Threading.Tasks;
using AutoMapper;
using Hangfire;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Travis_backend.AccountAuthenticationDomain.Models;
using Travis_backend.ChannelDomain.Models;
using Travis_backend.ChannelDomain.ViewModels;
using Travis_backend.CommonDomain.ViewModels;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.CompanyDomain.Services;
using Travis_backend.Constants;
using Travis_backend.ContactDomain.ViewModels;
using Travis_backend.Controllers.CompanyManagementControllers;
using Travis_backend.ConversationServices;
using Travis_backend.Database;
using Travis_backend.FileDomain.Models;
using Travis_backend.InternalDomain.Models;
using Travis_backend.InternalDomain.Services;
using Twilio;
using Twilio.Rest.Api.V2010;

namespace Travis_backend.Controllers.ChannelControllers
{
    [AllowAnonymous]
    [Route("twilio/whatsapp")]
    public class RegisterWhatsappController : Controller
    {
        private readonly ApplicationDbContext _appDbContext;
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly SignInManager<ApplicationUser> _signInManager;
        private readonly IMapper _mapper;
        private readonly ILogger<RegisterWhatsappController> _logger;
        private readonly IConfiguration _configuration;
        private readonly ICompanyService _companyService;
        private readonly IAzureBlobStorageService _azureBlobStorageService;
        private readonly IEmailNotificationService _emailNotificationService;
        private readonly IInternalHubSpotService _internalHubSpotService;
        private readonly ICoreService _coreService;
        private readonly IHttpClientFactory _httpClientFactory;

        public RegisterWhatsappController(
            ApplicationDbContext appDbContext,
            UserManager<ApplicationUser> userManager,
            SignInManager<ApplicationUser> signInManager,
            IMapper mapper,
            IConfiguration configuration,
            ILogger<RegisterWhatsappController> logger,
            ICompanyService companyService,
            IAzureBlobStorageService azureBlobStorageService,
            IEmailNotificationService emailNotificationService,
            IInternalHubSpotService internalHubSpotService,
            ICoreService coreService,
            IHttpClientFactory httpClientFactory)
        {
            _userManager = userManager;
            _signInManager = signInManager;
            _appDbContext = appDbContext;
            _mapper = mapper;
            _configuration = configuration;
            _logger = logger;
            _companyService = companyService;
            _azureBlobStorageService = azureBlobStorageService;
            _emailNotificationService = emailNotificationService;
            _internalHubSpotService = internalHubSpotService;
            _coreService = coreService;
            _httpClientFactory = httpClientFactory;
        }

        [HttpGet]
        [Route("businessId/verify")]
        public async Task<IActionResult> BusinessIdVerification([FromQuery(Name = "BusinessId")] string businessId)
        {
            var fbConfig = await _appDbContext.ConfigFacebookConfigs.OrderBy(x => x.Id).FirstOrDefaultAsync();

            var httpClient = _httpClientFactory.CreateClient(HttpClientHandlerName.Default);

            var verifyResponse =
                await httpClient.GetAsync(
                    $"https://graph.facebook.com/{businessId}?access_token={fbConfig.PageAccessToken}");

            if (verifyResponse.IsSuccessStatusCode)
            {
                dynamic response = JsonConvert.DeserializeObject(await verifyResponse.Content.ReadAsStringAsync());
                return Ok(response);
            }

            return BadRequest(
                new ResponseViewModel
                {
                    message = "failed"
                });
        }

        [HttpPost]
        [Route("register")]
        public async Task<ActionResult<WhatsappRegistrationResponseViewModel>> ReigsterWhatsapp(
            [FromForm]
            WhatsappRegistrationViewModel applyWhatsappViewModel)
        {
            if (ModelState.IsValid)
            {
                var fileURLs = new List<string>();

                var domainName = _configuration.GetValue<String>("Values:DomainName");

                if (applyWhatsappViewModel.files != null)
                {
                    foreach (IFormFile file in applyWhatsappViewModel.files)
                    {
                        try
                        {
                            var filename = $"upload/{file.FileName}";
                            var url = _azureBlobStorageService.UploadFileAsBlob(
                                file.OpenReadStream(),
                                filename,
                                "whatsappregistrations").Result;

                            if (url == null)
                            {
                                return null;
                            }

                            var newuUploadedFile = new WhatsappRegistrationsFile();
                            newuUploadedFile.Filename = filename;
                            newuUploadedFile.BlobContainer = "whatsappregistrations";
                            newuUploadedFile.Url = url;
                            newuUploadedFile.MIMEType = file.ContentType;
                            _appDbContext.CoreWhatsappRegistrationFiles.Add(newuUploadedFile);

                            fileURLs.Add($"{domainName}/twilio/whatsapp/file/{newuUploadedFile.FileId}");
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(
                                ex,
                                "Upload/import attachment for WhatsApp Registration error for company {CompanyName}, email {Email}, filename {FileName}. {ExceptionMessage}",
                                applyWhatsappViewModel.CompanyName,
                                applyWhatsappViewModel.Email,
                                file.FileName,
                                ex.Message);
                        }
                    }
                }

                applyWhatsappViewModel.fileURLs = fileURLs;

                if (applyWhatsappViewModel.ProofOfAddress != null)
                {
                    try
                    {
                        var filename = $"upload/{applyWhatsappViewModel.ProofOfAddress.FileName}";
                        var url = _azureBlobStorageService.UploadFileAsBlob(
                            applyWhatsappViewModel.ProofOfAddress.OpenReadStream(),
                            filename,
                            "whatsappregistrations").Result;

                        if (url == null)
                        {
                            return null;
                        }

                        var newuUploadedFile = new WhatsappRegistrationsFile();
                        newuUploadedFile.Filename = filename;
                        newuUploadedFile.BlobContainer = "whatsappregistrations";
                        newuUploadedFile.Url = url;
                        newuUploadedFile.MIMEType = applyWhatsappViewModel.ProofOfAddress.ContentType;
                        _appDbContext.CoreWhatsappRegistrationFiles.Add(newuUploadedFile);

                        var newUploaded = $"{domainName}/twilio/whatsapp/file/{newuUploadedFile.FileId}";
                        applyWhatsappViewModel.ProofOfAddressURLs = newUploaded;
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(
                            ex,
                            "Upload/import proof of address attachment for WhatsApp Registration error for company {CompanyName}, email {Email}, filename {FileName}. {ExceptionMessage}",
                            applyWhatsappViewModel.CompanyName,
                            applyWhatsappViewModel.Email,
                            applyWhatsappViewModel.ProofOfAddress.FileName,
                            ex.Message);
                    }
                }

                if (applyWhatsappViewModel.IdentifyDocument != null)
                {
                    try
                    {
                        var filename = $"upload/{applyWhatsappViewModel.IdentifyDocument.FileName}";
                        var url = _azureBlobStorageService.UploadFileAsBlob(
                            applyWhatsappViewModel.IdentifyDocument.OpenReadStream(),
                            filename,
                            "whatsappregistrations").Result;

                        if (url == null)
                        {
                            return null;
                        }

                        var newuUploadedFile = new WhatsappRegistrationsFile();
                        newuUploadedFile.Filename = filename;
                        newuUploadedFile.BlobContainer = "whatsappregistrations";
                        newuUploadedFile.Url = url;
                        newuUploadedFile.MIMEType = applyWhatsappViewModel.IdentifyDocument.ContentType;
                        _appDbContext.CoreWhatsappRegistrationFiles.Add(newuUploadedFile);

                        var newUploaded = $"{domainName}/twilio/whatsapp/file/{newuUploadedFile.FileId}";
                        applyWhatsappViewModel.identifyDocumentURLs = newUploaded;
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(
                            ex,
                            "Upload/import identify document attachment for WhatsApp Registration error for company {CompanyName}, email {Email}, filename {FileName}. {ExceptionMessage}",
                            applyWhatsappViewModel.CompanyName,
                            applyWhatsappViewModel.Email,
                            applyWhatsappViewModel.IdentifyDocument.FileName,
                            ex.Message);
                    }
                }

                if (applyWhatsappViewModel.LetterOfAuthorization != null)
                {
                    try
                    {
                        var filename = $"upload/{applyWhatsappViewModel.LetterOfAuthorization.FileName}";
                        var url = _azureBlobStorageService.UploadFileAsBlob(
                            applyWhatsappViewModel.LetterOfAuthorization.OpenReadStream(),
                            filename,
                            "whatsappregistrations").Result;

                        if (url == null)
                        {
                            return null;
                        }

                        var newuUploadedFile = new WhatsappRegistrationsFile();
                        newuUploadedFile.Filename = filename;
                        newuUploadedFile.BlobContainer = "whatsappregistrations";
                        newuUploadedFile.Url = url;
                        newuUploadedFile.MIMEType = applyWhatsappViewModel.LetterOfAuthorization.ContentType;
                        _appDbContext.CoreWhatsappRegistrationFiles.Add(newuUploadedFile);

                        var newUploaded = $"{domainName}/twilio/whatsapp/file/{newuUploadedFile.FileId}";
                        applyWhatsappViewModel.LetterOfAuthorizationURLs = newUploaded;
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(
                            ex,
                            "Upload/import letter of authorization attachment for WhatsApp Registration error for company {CompanyName}, email {Email}, filename {FileName}. {ExceptionMessage}",
                            applyWhatsappViewModel.CompanyName,
                            applyWhatsappViewModel.Email,
                            applyWhatsappViewModel.LetterOfAuthorization.FileName,
                            ex.Message);
                    }
                }

                await _appDbContext.SaveChangesAsync();

                var config = await _appDbContext.CoreTwilioConfigs.FirstOrDefaultAsync();
                TwilioClient.Init(config.AccountSID, config.AccountSecret);
                var accountSid = string.Empty;

                string userId = null;
                Staff companyUser = null;
                try
                {
                    _logger.LogInformation(
                        "[{MethodName}] Creating twilio usage record for company {CompanyName}",
                        nameof(ReigsterWhatsapp),
                        applyWhatsappViewModel.CompanyName);

                    var account = AccountResource.Create(friendlyName: applyWhatsappViewModel.CompanyName);
                    accountSid = account.Sid;

                    if (User.Identity.IsAuthenticated)
                    {
                        companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

                        _logger.LogInformation(
                            "[{MethodName}] Authenticated Id: {UserId}",
                            nameof(ReigsterWhatsapp),
                            userId);

                        if (companyUser == null)
                        {
                            await CreateTwilioUsageRecord(
                                applyWhatsappViewModel,
                                accountSid,
                                applyWhatsappViewModel.ReferenceId);
                        }
                        else
                        {
                            await CreateTwilioUsageRecord(applyWhatsappViewModel, accountSid, companyUser.CompanyId);
                        }
                    }
                    else if (!string.IsNullOrEmpty(applyWhatsappViewModel.ReferenceId))
                    {
                        _logger.LogWarning(
                            "[{MethodName}] Not Authenticated: Id: {RegisterWhatsAppReferenceId}",
                            nameof(ReigsterWhatsapp),
                            applyWhatsappViewModel.ReferenceId);

                        await CreateTwilioUsageRecord(
                            applyWhatsappViewModel,
                            accountSid,
                            applyWhatsappViewModel.ReferenceId);
                    }
                    else
                    {
                        _logger.LogWarning(
                            "No twilio record created for company {CompanyName}, email {Email}",
                            applyWhatsappViewModel.CompanyName,
                            applyWhatsappViewModel.Email);
                        // await _emailNotificationService.WhatsappStepToProceed(applyWhatsappViewModel);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(
                        ex,
                        "[WhatsApp Registration Error] Unable to create Twilio usage record for company {CompanyName}, email {Email}: {ExceptionMessage}",
                        applyWhatsappViewModel.CompanyName,
                        applyWhatsappViewModel.Email,
                        ex.Message);
                }

                if (!string.IsNullOrEmpty(applyWhatsappViewModel.ReferenceId))
                {
                    BackgroundJob.Enqueue<ICoreService>(
                        x => x.AddToSleekFlowCRM(
                            applyWhatsappViewModel.ReferenceId,
                            new ApplicationUser
                            {
                                Email = applyWhatsappViewModel.Email,
                                PhoneNumber = applyWhatsappViewModel.PhoneNumber,
                                FirstName = applyWhatsappViewModel.FirstName,
                                LastName = applyWhatsappViewModel.LastName,
                            },
                            null,
                            null,
                            new List<ImportHeader>
                            {
                                new ImportHeader
                                {
                                    HeaderName = "CompanyName"
                                },
                                new ImportHeader
                                {
                                    HeaderName = "Facebook Business Manager Id"
                                },
                                new ImportHeader
                                {
                                    HeaderName = "WhatsApp Application Status"
                                },
                                new ImportHeader
                                {
                                    HeaderName = "Facebook Verification"
                                },
                                new ImportHeader
                                {
                                    HeaderName = "Business Supporting Document"
                                },
                                new ImportHeader
                                {
                                    HeaderName = "Account SID"
                                },
                                new ImportHeader
                                {
                                    HeaderName = "Proof of Address"
                                },
                                new ImportHeader
                                {
                                    HeaderName = "Identity Document"
                                },
                                new ImportHeader
                                {
                                    HeaderName = "Letter of Authorization"
                                }
                            },
                            new List<string>
                            {
                                applyWhatsappViewModel.CompanyName,
                                applyWhatsappViewModel.FacebookBusinessManagerId,
                                "Applied to SleekFlow",
                                applyWhatsappViewModel.CompletedFacebookVerifications,
                                string.Join(", ", applyWhatsappViewModel.fileURLs.ToArray()),
                                accountSid,
                                applyWhatsappViewModel.ProofOfAddressURLs,
                                applyWhatsappViewModel.identifyDocumentURLs,
                                applyWhatsappViewModel.LetterOfAuthorizationURLs
                            },
                            null));
                }

                var cmsWhatsappApplicationFiles = applyWhatsappViewModel
                    .fileURLs
                    .Select(
                        f => new CmsWhatsappApplicationFile()
                        {
                            Url = f, Type = "Supporting Documents"
                        })
                    .ToList();

                if (applyWhatsappViewModel.ProofOfAddressURLs != "N/A")
                {
                    cmsWhatsappApplicationFiles.Add(
                        new CmsWhatsappApplicationFile()
                        {
                            Url = applyWhatsappViewModel.ProofOfAddressURLs, Type = "Proof Of Address"
                        });
                }

                if (applyWhatsappViewModel.identifyDocumentURLs != "N/A")
                {
                    cmsWhatsappApplicationFiles.Add(
                        new CmsWhatsappApplicationFile()
                        {
                            Url = applyWhatsappViewModel.identifyDocumentURLs, Type = "Identify Document"
                        });
                }

                if (applyWhatsappViewModel.LetterOfAuthorizationURLs != "N/A")
                {
                    cmsWhatsappApplicationFiles.Add(
                        new CmsWhatsappApplicationFile()
                        {
                            Url = applyWhatsappViewModel.LetterOfAuthorizationURLs, Type = "Letter Of Authorization"
                        });
                }

                var applicationRecord = new CmsWhatsappApplication()
                {
                    ReferenceId = applyWhatsappViewModel.ReferenceId,
                    ContactPersonName = $"{applyWhatsappViewModel.FirstName} {applyWhatsappViewModel.LastName}",
                    CompanyName = applyWhatsappViewModel.CompanyName,
                    ContactPersonEmail = applyWhatsappViewModel.Email,
                    ContactPersonPhoneNumber = applyWhatsappViewModel.PhoneNumber,
                    FacebookBusinessManagerId = applyWhatsappViewModel.FacebookBusinessManagerId,
                    CompletedFacebookVerifications =
                        applyWhatsappViewModel.CompletedFacebookVerifications.ToLower() == "true",
                    CompanyWebsite = applyWhatsappViewModel.CompanyWebsite,
                    PreferStartWith = applyWhatsappViewModel.PreferStartWith,
                    TwilioAccountSid = accountSid,
                    Files = cmsWhatsappApplicationFiles,
                    CreatedByUserId = userId,
                    CompanyId = companyUser?.CompanyId,
                };

                _appDbContext.CmsWhatsappApplications.Add(applicationRecord);

                await _appDbContext.SaveChangesAsync();

                // Add HubSpot Ticket
                var applyWhatsappViewModelForHubSpot =
                    _mapper.Map<WhatsappRegistrationWithoutFileViewModel>(applyWhatsappViewModel);
                BackgroundJob.Enqueue<IInternalHubSpotService>(
                    x => x.CreateWhatsAppApplicationTicket(
                        applyWhatsappViewModelForHubSpot,
                        applicationRecord.Id,
                        companyUser != null ? companyUser.Id : null));

                // Internal Note
                await _emailNotificationService.WhatsappRegistrationInternalSlack(
                    companyUser?.CompanyId,
                    applyWhatsappViewModel,
                    accountSid);

                return Ok(_mapper.Map<WhatsappRegistrationResponseViewModel>(applicationRecord));
            }

            return BadRequest(
                new ResponseViewModel
                {
                    message = "Incorrect format"
                });
        }

        private async Task CreateTwilioUsageRecord(
            WhatsappRegistrationViewModel applyWhatsappViewModel,
            string accountSid,
            string companyId)
        {
            _logger.LogInformation(
                "Created Twilio Records sid: {TwilioAccountSid}, companyId: {CompanyId}",
                accountSid,
                companyId);

            _appDbContext.CompanyTwilioUsageRecords.Add(
                new TwilioUsageRecord
                {
                    CompanyId = companyId,
                    TwilioAccountId = accountSid,
                    IsVerified = (applyWhatsappViewModel.CompletedFacebookVerifications == "True") ? true : false
                });

            await _appDbContext.SaveChangesAsync();

            // var timeSpan = TimeSpan.FromHours(1);
            //  await _emailNotificationService.SendDripEmailV2(companyId, NotificationType.twilio_step_to_proceed_en, null, null);
        }

        [HttpPost]
        [Route("register/sms")]
        public async Task<IActionResult> ReigsterSMS([FromForm] SMSRegistrationViewModel applyWhatsappViewModel)
        {
            if (ModelState.IsValid)
            {
                var fileURLs = new List<string>();

                var domainName = _configuration.GetValue<String>("Values:DomainName");
                if (applyWhatsappViewModel.files != null)
                {
                    foreach (IFormFile file in applyWhatsappViewModel.files)
                    {
                        try
                        {
                            var filename = $"upload/{file.FileName}";
                            var url = await _azureBlobStorageService.UploadFileAsBlob(
                                file.OpenReadStream(),
                                filename,
                                "smsregistrations");

                            if (url == null)
                            {
                                return null;
                            }

                            var newuUploadedFile = new WhatsappRegistrationsFile();
                            newuUploadedFile.Filename = filename;
                            newuUploadedFile.BlobContainer = "whatsappregistrations";
                            newuUploadedFile.Url = url;
                            newuUploadedFile.MIMEType = file.ContentType;
                            _appDbContext.CoreWhatsappRegistrationFiles.Add(newuUploadedFile);

                            fileURLs.Add($"{domainName}/twilio/whatsapp/file/{newuUploadedFile.FileId}");
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(
                                ex,
                                "Upload/import attachment for SMS Registration error for company {CompanyName}, email {Email}, filename {FileName}. {ExceptionMessage}",
                                applyWhatsappViewModel.CompanyName,
                                applyWhatsappViewModel.Email,
                                file.FileName,
                                ex.Message);
                        }
                    }
                }

                applyWhatsappViewModel.fileURLs = fileURLs;
                await _appDbContext.SaveChangesAsync();

                var config = await _appDbContext.CoreTwilioConfigs.FirstOrDefaultAsync();
                TwilioClient.Init(config.AccountSID, config.AccountSecret);
                var accountSid = string.Empty;

                try
                {
                    var account = AccountResource.Create(friendlyName: applyWhatsappViewModel.CompanyName);
                    accountSid = account.Sid;

                    if (User.Identity.IsAuthenticated)
                    {
                        var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

                        _appDbContext.CompanyTwilioUsageRecords.Add(
                            new TwilioUsageRecord
                            {
                                CompanyId = companyUser.CompanyId, TwilioAccountId = accountSid
                            });
                        await _appDbContext.SaveChangesAsync();
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(
                        ex,
                        "Couldn't create/add sub-account during SMS Registration error for company {CompanyName}, email {Email}: {ExceptionMessage}",
                        applyWhatsappViewModel.CompanyName,
                        applyWhatsappViewModel.Email,
                        ex.Message);
                }

                BackgroundJob.Enqueue<ICoreService>(
                    x => x.AddToSleekFlowCRM(
                        applyWhatsappViewModel.ReferenceId,
                        new ApplicationUser
                        {
                            Email = applyWhatsappViewModel.Email,
                            PhoneNumber = applyWhatsappViewModel.PhoneNumber,
                            FirstName = applyWhatsappViewModel.FirstName,
                            LastName = applyWhatsappViewModel.LastName,
                        },
                        null,
                        null,
                        new List<ImportHeader>
                        {
                            new ImportHeader
                            {
                                HeaderName = "CompanyName"
                            },
                            new ImportHeader
                            {
                                HeaderName = "WhatsApp Application Status"
                            },
                            new ImportHeader
                            {
                                HeaderName = "Business Supporting Document"
                            },
                            new ImportHeader
                            {
                                HeaderName = "Account SID"
                            }
                        },
                        new List<string>
                        {
                            applyWhatsappViewModel.CompanyName,
                            "Applied to SleekFlow",
                            string.Join(", ", applyWhatsappViewModel.fileURLs.ToArray()),
                            accountSid
                        },
                        null));

                await _emailNotificationService.SMSRegistrationNotifrication(applyWhatsappViewModel);

                return Ok();
            }

            return BadRequest(
                new ResponseViewModel
                {
                    message = "Incorrect format"
                });
        }

        [HttpGet]
        [AllowAnonymous]
        [Route("File/{filenameId}")]
        public async Task<IActionResult> GetAzureBlob(string filenameId)
        {
            var file = await _appDbContext.CoreWhatsappRegistrationFiles
                .FirstOrDefaultAsync(x => x.FileId == filenameId);

            var stream = await _azureBlobStorageService.DownloadFromAzureBlob(file.Filename, file.BlobContainer);
            var extension = Path.GetExtension(file.Filename);
            if (string.IsNullOrEmpty(extension))
            {
                switch (file.MIMEType)
                {
                    case "video/mp4":
                        extension = "mp4";
                        break;
                    case "image/jpeg":
                        extension = "jpg";
                        break;
                    case "image/png":
                        extension = "png";
                        break;
                }
            }

            var filename = Path.GetFileNameWithoutExtension(file.Filename);
            var res = File(
                stream.ToArray(),
                (string.IsNullOrEmpty(file.MIMEType)) ? "application/octet-stream" : file.MIMEType,
                $"{filename}{((string.IsNullOrEmpty(extension) && file.MIMEType == "video/mp4") ? ".mp4" : $".{extension}")}");
            res.EnableRangeProcessing = true;
            return res;
        }

        [HttpPost]
        [AllowAnonymous]
        [Route("verification")]
        public async Task<ActionResult<ResponseViewModel>> UpdateFacebookVerification(
            [FromBody]
            UpdateFacebookVerificationViewModel viewModel)
        {
            BackgroundJob.Enqueue<ICoreService>(
                x => x.AddInfoToSleekFlowCRM(
                    viewModel.ReferenceId,
                    new ApplicationUser
                    {
                        Email = viewModel.Email
                    },
                    new List<ImportHeader>
                    {
                        new ImportHeader
                        {
                            HeaderName = "Facebook Verification"
                        }
                    },
                    new List<string>
                    {
                        viewModel.IsVerified.ToString()
                    }));

            var twilioUsages = await _appDbContext.CompanyTwilioUsageRecords
                .Where(x => x.CompanyId == viewModel.ReferenceId)
                .ToListAsync();
            foreach (var twilioUsageRecord in twilioUsages)
            {
                twilioUsageRecord.IsVerified = viewModel.IsVerified;
            }

            var cmsWhatsappApplications = await _appDbContext.CmsWhatsappApplications
                .Where(x => x.ReferenceId == viewModel.ReferenceId || x.CompanyId == viewModel.ReferenceId)
                .ToListAsync();

            foreach (var cmsWhatsappApplication in cmsWhatsappApplications)
            {
                cmsWhatsappApplication.CompletedFacebookVerifications = viewModel.IsVerified;

                if (cmsWhatsappApplication.HubSpotTicketId != null)
                {
                    // Add HubSpot Ticket
                    BackgroundJob.Enqueue<IInternalHubSpotService>(
                        x => x.UpdateWhatsAppApplicationTicket(
                            cmsWhatsappApplication.HubSpotTicketId,
                            null,
                            viewModel.IsVerified,
                            null));
                }
            }

            var updatedRows = await _appDbContext.SaveChangesAsync();

            return Ok(
                new ResponseViewModel
                {
                    message = "success"
                });
        }
    }
}