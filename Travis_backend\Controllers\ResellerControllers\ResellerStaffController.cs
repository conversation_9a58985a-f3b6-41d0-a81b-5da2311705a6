using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Travis_backend.AccountAuthenticationDomain.Models;
using Travis_backend.Cache;
using Travis_backend.CommonDomain.ViewModels;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.CompanyDomain.Services;
using Travis_backend.CompanyDomain.ViewModels;
using Travis_backend.Configuration;
using Travis_backend.ConversationDomain.ViewModels;
using Travis_backend.ConversationServices;
using Travis_backend.Database;
using Travis_backend.FileDomain.Services;
using Travis_backend.Helpers;
using Travis_backend.ResellerDomain.Constants;
using Travis_backend.ResellerDomain.Models;
using Travis_backend.ResellerDomain.Services;
using Travis_backend.ResellerDomain.ViewModels;

namespace Travis_backend.Controllers.ResellerControllers;

/// <summary>
/// Reseller Portal Manage Own Staff.
/// </summary>
[Route("reseller/staff/[action]")]
public class ResellerStaffController : ResellerBaseController
{
    private readonly ApplicationDbContext _appDbContext;
    private readonly IMapper _mapper;
    private readonly ILogger _logger;
    private readonly IResellerPortalRepository _resellerPortalRepository;
    private readonly IUploadService _uploadService;
    private readonly IPowerflowManageResellerRepository _powerflowManageResellerRepository;
    private readonly RoleManager<IdentityRole> _roleManager;
    private readonly IAzureBlobStorageService _azureBlobStorageService;
    private readonly ICompanyTeamService _companyTeamService;
    private readonly ICompanyInfoCacheService _companyInfoCacheService;

    public ResellerStaffController(
        ApplicationDbContext appDbContext,
        UserManager<ApplicationUser> userManager,
        IMapper mapper,
        ILogger<ResellerAccountController> logger,
        IResellerPortalRepository resellerPortalRepository,
        IUploadService uploadService,
        IPowerflowManageResellerRepository powerflowManageResellerRepository,
        IAzureBlobStorageService azureBlobStorageService,
        RoleManager<IdentityRole> roleManager,
        ICompanyTeamService companyTeamService,
        ICompanyInfoCacheService companyInfoCacheService)
        : base(userManager, appDbContext)
    {
        _appDbContext = appDbContext;
        _mapper = mapper;
        _logger = logger;
        _resellerPortalRepository = resellerPortalRepository;
        _uploadService = uploadService;
        _powerflowManageResellerRepository = powerflowManageResellerRepository;
        _roleManager = roleManager;
        _azureBlobStorageService = azureBlobStorageService;
        _companyTeamService = companyTeamService;
        _companyInfoCacheService = companyInfoCacheService;
    }

    /// <summary>
    /// Update Reseller Staff information By Reseller Admin.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    [Authorize(Roles = ApplicationUserRole.ResellerPortalUser)]
    public async Task<ActionResult<ResellerStaffInformation>> UpdateResellerStaffAccount(
        [FromBody]
        UpdateResellerAccountViewModel updateViewModel)
    {
        if (User.Identity.IsAuthenticated)
        {
            var user = await GetCurrentValidResellerUser(
                new List<string>()
                {
                    ApplicationUserRole.ResellerPortalUser
                });

            if (user == null)
            {
                return Unauthorized();
            }

            if (string.IsNullOrEmpty(updateViewModel.Email) && string.IsNullOrEmpty(updateViewModel.Username))
            {
                return BadRequest(
                    new ResponseViewModel()
                    {
                        message = "Email and Username cannot be both empty"
                    });
            }

            var resellerStaff = await _appDbContext.ResellerStaffs.Include(x => x.ResellerCompanyProfile)
                .FirstOrDefaultAsync(x => x.IdentityId == user.Id);

            if (resellerStaff == null)
            {
                return BadRequest(
                    new ResponseViewModel()
                    {
                        message = "You are not a reseller admin staff"
                    });
            }

            ApplicationUser userToUpdate = new ApplicationUser();

            if (!string.IsNullOrEmpty(updateViewModel.Email))
            {
                userToUpdate = await _userManager.FindByEmailAsync(updateViewModel.Email);
            }
            else if (!string.IsNullOrEmpty(updateViewModel.Username))
            {
                userToUpdate = await _userManager.FindByNameAsync(updateViewModel.Username);
            }

            var resellerStaffToUpdate = await _appDbContext.ResellerStaffs.Include(x => x.ProfilePicture)
                .FirstOrDefaultAsync(
                    x => x.IdentityId == userToUpdate.Id &&
                         x.ResellerCompanyProfileId == resellerStaff.ResellerCompanyProfileId);

            if (resellerStaffToUpdate == null)
            {
                return BadRequest(
                    new ResponseViewModel()
                    {
                        message = "Reseller Staff to be updated not found"
                    });
            }

            if (updateViewModel.DisplayName != null)
            {
                userToUpdate.DisplayName = updateViewModel.DisplayName;
            }

            if (updateViewModel.PhoneNumber != null)
            {
                userToUpdate.PhoneNumber = updateViewModel.PhoneNumber;
            }

            if (updateViewModel.Position != null)
            {
                resellerStaffToUpdate.Position = updateViewModel.Position;
            }

            if (updateViewModel.FirstName != null)
            {
                userToUpdate.FirstName = updateViewModel.FirstName;
            }

            if (updateViewModel.LastName != null)
            {
                userToUpdate.LastName = updateViewModel.LastName;
            }

            await _appDbContext.SaveChangesAsync();

            var resellerStaffInfo = new ResellerStaffInformation()
            {
                Id = resellerStaff.Id,
                IdentityId = userToUpdate.Id,
                UserInfo = _mapper.Map<UserInfoResponse>(userToUpdate),
                ResellerProfileId = resellerStaff.ResellerCompanyProfileId,
                ResellerProfileInformation =
                    _mapper.Map<ResellerProfileInformation>(resellerStaff.ResellerCompanyProfile),
                ResellerCompanyId = resellerStaff.ResellerCompanyProfile.CompanyId,
                CompanyResponse = _mapper.Map<CompanyResponse>(resellerStaff.ResellerCompanyProfile.Company),
                Locale = resellerStaffToUpdate.Locale,
                Position = resellerStaffToUpdate.Position,
                TimeZoneInfoId = resellerStaffToUpdate.TimeZoneInfoId,
                ProfilePictureURL = resellerStaffToUpdate.ProfilePicture != null
                    ? _azureBlobStorageService.GetAzureBlobSasUri(
                        resellerStaffToUpdate.ProfilePicture.Filename,
                        resellerStaffToUpdate.ProfilePicture.BlobContainer)
                    : null
            };

            return resellerStaffInfo;
        }

        return Unauthorized();
    }

    /// <summary>
    /// Reseller Staff upload own profile picture and apply to account in client companies.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    [Authorize(Roles = ApplicationUserRole.ResellerPortalUser)]
    public async Task<ActionResult> UploadProfilePictureFile([FromForm] ProfilePictureViewModel attachmentViewModel)
    {
        if (ModelState.IsValid && User.Identity.IsAuthenticated)
        {
            var user = await GetCurrentValidResellerUser(
                new List<string>()
                {
                    ApplicationUserRole.ResellerPortalUser
                });

            if (user == null)
            {
                return Unauthorized();
            }

            var resellerStaff = await _appDbContext.ResellerStaffs.Include(x => x.ResellerCompanyProfile)
                .FirstOrDefaultAsync(x => x.IdentityId == user.Id);

            if (resellerStaff == null)
            {
                return BadRequest(
                    new ResponseViewModel()
                    {
                        message = "Reseller Staff Not Found"
                    });
            }

            if (string.IsNullOrEmpty(resellerStaff.ResellerCompanyProfileId))
            {
                return BadRequest();
            }

            foreach (IFormFile file in attachmentViewModel.files)
            {
                var fileName = $"ProfilePicture/{resellerStaff.Id}/{DateTime.UtcNow.ToString("o")}/{file.FileName}";
                var uploadFileResult = await _uploadService.UploadImage(
                    resellerStaff.ResellerCompanyProfile.CompanyId,
                    fileName,
                    file);

                if (uploadFileResult?.Url == null)
                {
                    return BadRequest(
                        new ResponseViewModel
                        {
                            message = "upload failure"
                        });
                }

                var newUploadedFile = new ProfilePictureFile();
                newUploadedFile.Filename = fileName;
                newUploadedFile.CompanyId = resellerStaff.ResellerCompanyProfile.CompanyId;
                newUploadedFile.BlobContainer = resellerStaff.ResellerCompanyProfile.CompanyId;
                newUploadedFile.Url = uploadFileResult.Url;
                newUploadedFile.MIMEType = file.ContentType;
                _appDbContext.UserStaffProfilePictures.Add(newUploadedFile);

                resellerStaff.ProfilePicture = newUploadedFile;
                await _appDbContext.SaveChangesAsync();

                var staffInClientCompanies = await _appDbContext.UserRoleStaffs.Where(x => x.IdentityId == user.Id)
                    .Include(x => x.ProfilePicture).ToListAsync();

                foreach (var staffInClientCompany in staffInClientCompanies)
                {
                    staffInClientCompany.ProfilePicture = newUploadedFile;
                    await _appDbContext.SaveChangesAsync();
                }

                break;
            }

            return Ok(
                new ResponseViewModel()
                {
                    message = "Upload success"
                });
        }

        return BadRequest();
    }

    /// <summary>
    /// Reseller Staff remove own profile picture and apply to account in client companies.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    [Authorize(Roles = ApplicationUserRole.ResellerPortalUser)]
    public async Task<ActionResult> RemoveResellerStaffProfilePicture()
    {
        if (User.Identity.IsAuthenticated)
        {
            var user = await GetCurrentValidResellerUser(
                new List<string>()
                {
                    ApplicationUserRole.ResellerPortalUser
                });

            if (user == null)
            {
                return Unauthorized();
            }

            var resellerStaff = await _appDbContext.ResellerStaffs.Include(x => x.ProfilePicture)
                .Include(x => x.ResellerCompanyProfile).FirstOrDefaultAsync(x => x.IdentityId == user.Id);

            if (resellerStaff == null)
            {
                return BadRequest(
                    new ResponseViewModel()
                    {
                        message = "Reseller Staff Not Found"
                    });
            }

            if (resellerStaff.ProfilePicture == null)
            {
                return BadRequest(
                    new ResponseViewModel()
                    {
                        message = "Profile Picture Not Found"
                    });
            }

            _appDbContext.UserStaffProfilePictures.Remove(resellerStaff.ProfilePicture);

            var staffInClientCompanies = await _appDbContext.UserRoleStaffs.Where(x => x.IdentityId == user.Id)
                .Include(x => x.ProfilePicture).ToListAsync();

            foreach (var staffInClientCompany in staffInClientCompanies)
            {
                if (staffInClientCompany.ProfilePicture != null)
                {
                    _appDbContext.UserStaffProfilePictures.Remove(staffInClientCompany.ProfilePicture);
                }
            }

            await _appDbContext.SaveChangesAsync();

            return Ok(
                new ResponseViewModel()
                {
                    message = "Remove Profile Picture Success"
                });
        }

        return Unauthorized();
    }

    /// <summary>
    /// Reseller Staff upload reseller company logo.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    [Authorize(Roles = ApplicationUserRole.ResellerPortalUser)]
    public async Task<ActionResult<ProfileLogoLinkDto>> UploadResellerCompanyLogo(
        [FromForm]
        UpdateCompanyLogoViewModel updateCompanyLogoViewModel)
    {
        if (ModelState.IsValid && User.Identity.IsAuthenticated)
        {
            var user = await GetCurrentValidResellerUser(
                new List<string>()
                {
                    ApplicationUserRole.ResellerPortalUser
                });

            if (user == null)
            {
                return Unauthorized();
            }

            var resellerProfile = await _appDbContext.ResellerCompanyProfiles.Include(x => x.Company)
                .FirstOrDefaultAsync(x => x.Id == updateCompanyLogoViewModel.CompanyProfileId);

            if (resellerProfile == null)
            {
                return BadRequest(
                    new ResponseViewModel()
                    {
                        message = "Reseller Profile Not Found"
                    });
            }

            var updateResponse = await _resellerPortalRepository.UpdateResellerProfileLogo(
                resellerProfile,
                updateCompanyLogoViewModel.CompanyLogo);

            if (!updateResponse.IsSuccess)
            {
                return BadRequest(
                    new ResponseViewModel()
                    {
                        message = updateResponse.ErrorMsg
                    });
            }

            var profileLogoLinkDto = new ProfileLogoLinkDto()
            {
                ResellerProfileId = resellerProfile.Id,
                ResellerCompanyName = resellerProfile.Company.CompanyName,
                AccessUrl = (string) updateResponse.Data
            };

            await _companyInfoCacheService.RemoveCompanyInfoCache(resellerProfile.CompanyId);
            return Ok(profileLogoLinkDto);
        }

        return BadRequest();
    }

    /// <summary>
    /// Reseller Staff get own information.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    [Authorize(Roles = ApplicationUserRole.ResellerPortalUser)]
    public async Task<ActionResult<ResellerStaffInformation>> GetResellerStaffOwnInformation()
    {
        if (User.Identity.IsAuthenticated)
        {
            var user = await GetCurrentValidResellerUser(
                new List<string>()
                {
                    ApplicationUserRole.ResellerPortalUser
                });

            if (user == null)
            {
                return Unauthorized();
            }

            var response = await _resellerPortalRepository.GetResellerStaffOwnProfile(user);

            if (!response.IsSuccess)
            {
                return BadRequest(
                    new ResponseViewModel()
                    {
                        message = response.ErrorMsg
                    });
            }

            var resellerStaffInformation = (ResellerStaffInformation) response.Data;

            return resellerStaffInformation;
        }

        return Unauthorized();
    }

    /// <summary>
    /// Get User My User Info.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    [Authorize(Roles = ApplicationUserRole.ResellerPortalUser)]
    public async Task<ActionResult<UserAndRoleDto>> GetResellerPortalUser()
    {
        var user = await GetCurrentValidResellerUser(
            new List<string>()
            {
                ApplicationUserRole.ResellerPortalUser
            });

        if (user == null)
        {
            return Unauthorized();
        }

        var roles = await _userManager.GetRolesAsync(user);

        return Ok(
            new UserAndRoleDto
            {
                User = _mapper.Map<UserInfoResponse>(user), Roles = roles.ToList()
            });
    }

    /// <summary>
    /// Search Staff Name for autocomplete in setting assignee.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    [Authorize(Roles = ApplicationUserRole.ResellerPortalUser)]
    public async Task<ActionResult<List<SearchStaffInformation>>> SearchStaffNameForAutoComplete(
        [FromBody]
        SearchResellerStaffRequest request)
    {
        if (User.Identity.IsAuthenticated)
        {
            var user = await GetCurrentValidResellerUser(
                new List<string>()
                {
                    ApplicationUserRole.ResellerPortalUser
                });

            if (user == null)
            {
                return Unauthorized();
            }

            var resellerProfileResponse = await _resellerPortalRepository.GetUserIdentityReseller(user);

            if (!resellerProfileResponse.IsSuccess)
            {
                return BadRequest(
                    new ResponseViewModel()
                    {
                        message = resellerProfileResponse.ErrorMsg
                    });
            }

            var resellerProfileId = (string) resellerProfileResponse.Data;

            var resellerStaffs = await _appDbContext.ResellerStaffs.AsNoTracking()
                .Include(x => x.ResellerCompanyProfile).ThenInclude(x => x.Company).Include(x => x.ProfilePicture)
                .Where(x => x.ResellerCompanyProfileId == resellerProfileId).ToListAsync();
            var resellerIdentityIds = resellerStaffs.Select(x => x.IdentityId).ToList();
            var searchIdentityIds = await _appDbContext.Users.AsNoTracking()
                .Where(x => x.DisplayName.Contains(request.Name) && resellerIdentityIds.Contains(x.Id))
                .Select(x => x.Id).ToListAsync();
            resellerStaffs = resellerStaffs.Where(x => searchIdentityIds.Contains(x.IdentityId)).ToList();

            var response = new List<SearchStaffInformation>();

            foreach (var resellerStaff in resellerStaffs)
            {
                var resellerUser = await _userManager.FindByIdAsync(resellerStaff.IdentityId);

                var resellerStaffInfo = new SearchStaffInformation()
                {
                    Id = resellerStaff.Id,
                    IdentityId = resellerStaff.IdentityId,
                    ResellerProfileId = resellerStaff.ResellerCompanyProfileId,
                    DisplayName = resellerUser.DisplayName,
                    ProfilePictureURL = resellerStaff.ProfilePicture != null
                        ? _azureBlobStorageService.GetAzureBlobSasUri(
                            resellerStaff.ProfilePicture.Filename,
                            resellerStaff.ProfilePicture.BlobContainer)
                        : null
                };

                response.Add(resellerStaffInfo);
            }

            return Ok(response);
        }

        return Unauthorized();
    }

    /// <summary>
    /// Get all reseller staff info in own reseller company.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    [Authorize(Roles = ApplicationUserRole.ResellerPortalUser)]
    public async Task<ActionResult<List<ResellerStaffInformation>>> GetAllUsersInOwnResellerCompany()
    {
        var result = new List<ResellerStaffInformation>();

        var currentUser = await GetCurrentValidResellerUser(
            new List<string>()
            {
                ApplicationUserRole.ResellerPortalUser
            });

        if (currentUser == null)
        {
            return Unauthorized();
        }

        var resellerCompanyProfile = await _appDbContext.ResellerCompanyProfiles.AsNoTracking()
            .Include(x => x.ResellerStaffs).Include(x => x.Company).Where(
                x => x.ResellerStaffs != null && x.ResellerStaffs.Any(y => y.IdentityId == currentUser.Id))
            .FirstOrDefaultAsync();

        if (resellerCompanyProfile == null || resellerCompanyProfile.ResellerStaffs == null)
        {
            return BadRequest(
                new ResponseViewModel()
                {
                    message = "Reseller Company Profile Not Found"
                });
        }

        foreach (var resellerStaff in resellerCompanyProfile.ResellerStaffs)
        {
            var user = await _userManager.FindByIdAsync(resellerStaff.IdentityId);

            var resellerStaffInfo = new ResellerStaffInformation()
            {
                Id = resellerStaff.Id,
                IdentityId = resellerStaff.IdentityId,
                UserInfo = _mapper.Map<UserInfoResponse>(user),
                ResellerProfileId = resellerStaff.ResellerCompanyProfileId,
                ResellerProfileInformation =
                    _mapper.Map<ResellerProfileInformation>(resellerStaff.ResellerCompanyProfile),
                ResellerCompanyId = resellerStaff.ResellerCompanyProfile.CompanyId,
                CompanyResponse = _mapper.Map<CompanyResponse>(resellerStaff.ResellerCompanyProfile.Company),
                Locale = resellerStaff.Locale,
                Position = resellerStaff.Position,
                TimeZoneInfoId = resellerStaff.TimeZoneInfoId,
                TimeZoneInfo = TimeZoneHelper.GetTimeZoneById(resellerStaff.TimeZoneInfoId),
                ProfilePictureURL = resellerStaff.ProfilePicture != null
                    ? _azureBlobStorageService.GetAzureBlobSasUri(
                        resellerStaff.ProfilePicture.Filename,
                        resellerStaff.ProfilePicture.BlobContainer)
                    : null
            };

            if (resellerStaff.ResellerCompanyProfile.Company.TimeZoneInfoId != null)
            {
                resellerStaffInfo.CompanyResponse.TimeZoneInfo =
                    TimeZoneHelper.GetTimeZoneById(resellerStaff.ResellerCompanyProfile.Company.TimeZoneInfoId);
            }

            result.Add(resellerStaffInfo);
        }

        return Ok(result);
    }

    [HttpPost]
    [Authorize(Roles = ApplicationUserRole.ResellerPortalUser)]
    public async Task<ActionResult> DeleteStaff([FromBody] RemoveResellerStaffRequest request)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(
                new ResponseViewModel()
                {
                    message = "Email cannot be null"
                });
        }

        var currentUser = await GetCurrentValidResellerUser(
            new List<string>()
            {
                ApplicationUserRole.ResellerPortalUser
            });
        if (currentUser == null)
        {
            return Unauthorized();
        }

        var currentStaff = await _appDbContext.ResellerStaffs.AsNoTracking().Include(x => x.ResellerCompanyProfile)
            .FirstOrDefaultAsync(x => x.IdentityId == currentUser.Id);

        if (currentStaff == null)
        {
            return BadRequest(
                new ResponseViewModel()
                {
                    message = "Current User is not a reseller staff"
                });
        }

        // Check the staff to be deleted is not the current user
        if (request.Email == currentUser.Email)
        {
            return BadRequest(
                new ResponseViewModel()
                {
                    message = "You cannot delete yourself"
                });
        }

        // Check if the staff to be deleted is the last staff of the reseller company
        var resellerCompanyProfile = await _appDbContext.ResellerCompanyProfiles
            .AsNoTracking()
            .Include(x => x.ResellerStaffs)
            .Where(
                x => x.ResellerStaffs != null
                     && x.ResellerStaffs.Any(y => y.IdentityId == currentUser.Id))
            .FirstOrDefaultAsync();

        if (resellerCompanyProfile == null || resellerCompanyProfile.ResellerStaffs == null)
        {
            return BadRequest(
                new ResponseViewModel()
                {
                    message = "Reseller Company Profile Not Found"
                });
        }

        if (resellerCompanyProfile.ResellerStaffs.Count <= 1)
        {
            return BadRequest(
                new ResponseViewModel()
                {
                    message = "Cannot delete the last staff"
                });
        }

        var userToRemove = await _userManager.FindByEmailAsync(request.Email);

        if (userToRemove == null)
        {
            return BadRequest(
                new ResponseViewModel()
                {
                    message = "User not found"
                });
        }

        var resellerStaffToRemove = await _appDbContext.ResellerStaffs.Include(x => x.ProfilePicture)
            .Where(x => x.IdentityId == userToRemove.Id).ToListAsync();
        var profilePictures = resellerStaffToRemove.Where(x => x.ProfilePicture != null).Select(x => x.ProfilePicture)
            .ToList();
        var userRoleStaffsToRemove = await _appDbContext.UserRoleStaffs.Include(x => x.ProfilePicture)
            .Where(x => x.IdentityId == userToRemove.Id).ToListAsync();

        try
        {
            if (profilePictures.Any())
            {
                _appDbContext.UserStaffProfilePictures.RemoveRange(profilePictures);
            }

            _appDbContext.UserRoleStaffs.RemoveRange(userRoleStaffsToRemove);
            _appDbContext.ResellerStaffs.RemoveRange(resellerStaffToRemove);
            await _appDbContext.SaveChangesAsync();
            await _userManager.DeleteAsync(userToRemove);

            await _resellerPortalRepository.AddResellerActivityLog(
                new ResellerActivityLog
                {
                    ResellerCompanyProfileId = resellerCompanyProfile.Id,
                    CompanyId = userToRemove.CompanyId,
                    CreatedByUserId = currentUser.Id,
                    Category = ResellerActivityLogCategory.User,
                    Action = $"Remove User - {userToRemove.UserName}"
                });
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "Delete reseller staff error, email:{Email}. {ExceptionMessage}",
                request.Email,
                ex.Message);
        }

        return Ok(
            new ResponseViewModel()
            {
                message = "Delete Reseller Staff Successfully"
            });
    }

    /// <summary>
    /// Reseller Staff Update own information.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    [Authorize(Roles = ApplicationUserRole.ResellerPortalUser)]
    public async Task<ActionResult<ResellerStaffInformation>> UpdateOwnAccountInfo(
        [FromBody]
        ResellerStaffInfoViewModel request)
    {
        if (User.Identity.IsAuthenticated)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(
                    new ResponseViewModel()
                    {
                        message = "InValid Data"
                    });
            }

            var currentUser = await GetCurrentValidResellerUser(
                new List<string>()
                {
                    ApplicationUserRole.ResellerPortalUser
                });

            if (currentUser == null)
            {
                return Unauthorized();
            }

            var resellerStaff = await _appDbContext.ResellerStaffs.Include(x => x.ResellerCompanyProfile)
                .ThenInclude(x => x.Company).FirstOrDefaultAsync(x => x.IdentityId == currentUser.Id);

            if (resellerStaff == null)
            {
                return BadRequest(
                    new ResponseViewModel()
                    {
                        message = "Not a reseller staff"
                    });
            }

            if (!string.IsNullOrEmpty(request.Position))
            {
                resellerStaff.Position = request.Position;
            }

            if (!string.IsNullOrEmpty(request.Username))
            {
                var existing = await _userManager.FindByNameAsync(request.Username);
                if (existing != null && existing.Id != currentUser.Id)
                {
                    return BadRequest(
                        new ResponseViewModel
                        {
                            message = "Username has been used"
                        });
                }

                currentUser.UserName = request.Username;
                currentUser.NormalizedUserName = request.Username.ToUpper();
            }

            if (!string.IsNullOrEmpty(request.DisplayName))
            {
                currentUser.DisplayName = request.DisplayName;
            }

            if (!string.IsNullOrEmpty(request.FirstName))
            {
                currentUser.FirstName = request.FirstName;
            }

            if (!string.IsNullOrEmpty(request.LastName))
            {
                currentUser.LastName = request.LastName;
            }

            if (!string.IsNullOrEmpty(request.PhoneNumber))
            {
                currentUser.PhoneNumber = request.PhoneNumber;
            }

            if (!string.IsNullOrEmpty(request.TimeZoneInfoId))
            {
                resellerStaff.TimeZoneInfoId = request.TimeZoneInfoId;
            }

            if (!string.IsNullOrEmpty(request.CompanyName))
            {
                resellerStaff.ResellerCompanyProfile.Company.CompanyName = request.CompanyName;
            }

            if (!string.IsNullOrEmpty(request.CompanyTimeZoneInfoId))
            {
                resellerStaff.ResellerCompanyProfile.Company.TimeZoneInfoId = request.TimeZoneInfoId;
            }

            if (request.TeamNames != null)
            {
                resellerStaff.TeamNames = request.TeamNames;
            }

            await _appDbContext.SaveChangesAsync();

            return Ok(
                new ResponseViewModel()
                {
                    message = "Update Success"
                });
        }

        return Unauthorized();
    }

    /// <summary>
    /// Update password.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    [Authorize(Roles = ApplicationUserRole.ResellerPortalUser)]
    public async Task<ActionResult> ResetPassword([FromBody] ResetResellerStaffPasswordViewModel request)
    {
        if (ModelState.IsValid && User.Identity.IsAuthenticated)
        {
            var currentUser = await GetCurrentValidResellerUser(
                new List<string>()
                {
                    ApplicationUserRole.ResellerPortalUser
                });

            if (currentUser == null)
            {
                return Unauthorized();
            }

            var token = await _userManager.GeneratePasswordResetTokenAsync(currentUser);
            var result = await _userManager.ResetPasswordAsync(currentUser, token, request.Password);

            if (result.Succeeded)
            {
                return Ok(
                    new ResponseViewModel()
                    {
                        message = "success"
                    });
            }

            return BadRequest(
                new ResponseViewModel()
                {
                    message = result.Errors.ToString()
                });
        }

        return BadRequest();
    }
}