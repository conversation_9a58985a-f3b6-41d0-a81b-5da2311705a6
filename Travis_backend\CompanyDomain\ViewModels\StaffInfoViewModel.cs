using System.Collections.Generic;
using Travis_backend.AutomationDomain.Models;
using Travis_backend.Enums;

namespace Travis_backend.CompanyDomain.ViewModels;

public class StaffInfoViewModel
{
    public string StaffName { get; set; }

    public int? TimeZone { get; set; }

    public string Position { get; set; }

    public string TimeZoneInfoId { get; set; }

    public string PhoneNumber { get; set; }

    public string FirstName { get; set; }

    public string LastName { get; set; }

    public string Username { get; set; }

    public StaffUserRole? StaffRole { get; set; }

    public StaffStatus? Status { get; set; }

    public List<long> TeamIds { get; set; }

    public bool? IsShowName { get; set; }

    public string Message { get; set; }

    public string QRCodeIdentity { get; set; }

    public TargetedChannelModel QRCodeChannel { get; set; }

    public string DefaultCurrency { get; set; }
}