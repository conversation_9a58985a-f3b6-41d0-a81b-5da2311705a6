﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Sleekflow.Apis.CrmHub.Api;
using Sleekflow.Apis.CrmHub.Model;
using Travis_backend.AccountAuthenticationDomain.Models;
using Travis_backend.ChannelDomain.Repositories;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.CompanyDomain.Services;
using Travis_backend.Constants;
using Travis_backend.ConversationDomain.ViewModels;
using Travis_backend.ConversationServices;
using Travis_backend.ConversationServices.Models;
using Travis_backend.CustomObjectDomain.Constants;
using Travis_backend.Database;
using Travis_backend.FacebookInstagramIntegrationDomain.Models;
using Travis_backend.FacebookInstagramIntegrationDomain.ViewModels;

namespace Travis_backend.Controllers.FacebookIntegrationControllers;

[Authorize]
[Route("FacebookLeadAds")]
public class FacebookLeadAdsController : Controller
{
    private const string FormSchemaRelationshipType = "one-to-many";
    private const string FormSchemaCategory = "facebook_lead_ads";
    private const string LeadIdPrimaryPropertyDisplayName = "Lead ID";
    private const string LeadIdPrimaryPropertyUniqueName = "lead_id";
    private const string UserProfileFirstNameFieldId = "FirstName";
    private const string UserProfileFirstNameFieldDisplayName = "First Name";
    private const string UserProfileLastNameFieldId = "LastName";
    private const string UserProfileLastNameFieldDisplayName = "Last Name";

    private readonly ApplicationDbContext _appDbContext;
    private readonly ICoreService _coreService;
    private readonly UserManager<ApplicationUser> _userManager;
    private readonly IMapper _mapper;
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly ILogger<FacebookLeadAdsController> _logger;
    private readonly IFacebookConfigRepository _facebookConfigRepository;
    private readonly IFacebookLeadAdsFormRepository _facebookLeadAdsFormRepository;
    private readonly ILeadAdsServiceService _leadAdsService;
    private readonly ISchemasApi _schemasApi;
    private readonly ICompanyTeamService _companyTeamService;

    public FacebookLeadAdsController(
        ApplicationDbContext appDbContext,
        ICoreService coreService,
        UserManager<ApplicationUser> userManager,
        IMapper mapper,
        IHttpClientFactory httpClientFactory,
        ILogger<FacebookLeadAdsController> logger,
        IFacebookConfigRepository facebookConfigRepository,
        IFacebookLeadAdsFormRepository facebookLeadAdsFormRepository,
        ILeadAdsServiceService leadAdsService,
        ISchemasApi schemasApi,
        ICompanyTeamService companyTeamService)
    {
        _appDbContext = appDbContext;
        _coreService = coreService;
        _userManager = userManager;
        _mapper = mapper;
        _httpClientFactory = httpClientFactory;
        _logger = logger;
        _facebookConfigRepository = facebookConfigRepository;
        _facebookLeadAdsFormRepository = facebookLeadAdsFormRepository;
        _leadAdsService = leadAdsService;
        _schemasApi = schemasApi;
        _companyTeamService = companyTeamService;
    }

    [HttpGet]
    [Route("FacebookLeadAdsConnections")]
    public async Task<IActionResult> GetFacebookLeadAdsConnections()
    {
        var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (companyUser == null)
        {
            return Unauthorized();
        }

        // only authenticated facebook pages subscribed to leadgen should be configured
        var facebookConfigs = (await _facebookConfigRepository.GetFacebookConfigsAsync(
            companyUser.CompanyId,
            FacebookStatus.Authenticated)).Where(f => f.SubscribedFields.Contains("leadgen")).ToList();

        var authenticatedFacebookConfigs = new List<FacebookConfig>();
        foreach (var facebookConfig in facebookConfigs)
        {
            var refreshedFacebookConfig =
                await _leadAdsService.RefreshLeadAdsFacebookConfigStatusByPageIdAsync(facebookConfig.PageId);
            if (refreshedFacebookConfig.Status != FacebookStatus.Authenticated)
            {
                continue;
            }

            try
            {
                await _leadAdsService.FetchAndSyncFacebookLeadGenFormsAsync(
                    refreshedFacebookConfig.Id,
                    refreshedFacebookConfig.CompanyId,
                    refreshedFacebookConfig.PageId,
                    refreshedFacebookConfig.PageAccessToken);
                authenticatedFacebookConfigs.Add(refreshedFacebookConfig);
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Failed to fetch and sync Facebook LeadGen forms for " +
                    "CompanyId: {CompanyId}, FacebookConfigId: {FacebookConfigId}. Error: {ErrorContent}",
                    facebookConfig.CompanyId,
                    facebookConfig.Id,
                    ex.Message);
            }
        }

        var connectionVMs = new List<FacebookLeadAdsConnectionViewModel>();
        foreach (var authenticatedFacebookConfig in authenticatedFacebookConfigs)
        {
            List<FacebookLeadAdsForm> facebookLeadAdsForms;
            try
            {
                facebookLeadAdsForms =
                    await _facebookLeadAdsFormRepository.GetFacebookLeadAdsFormsAsync(
                        companyUser.CompanyId,
                        authenticatedFacebookConfig.Id);
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Failed to retrieve Facebook Lead Ads forms for " +
                    "CompanyId: {CompanyId}, FacebookConfigId: {FacebookConfigId}. Error: {ErrorContent}",
                    companyUser.CompanyId,
                    authenticatedFacebookConfig.Id,
                    ex.Message);

                continue;
            }

            var formVMs = new List<FacebookLeadAdsFormViewModel>();
            foreach (var form in facebookLeadAdsForms)
            {
                var formVM = _mapper.Map<FacebookLeadAdsFormViewModel>(form);

                if (form.CrmHubSchemaId != null)
                {
                    var schema = (await _schemasApi.SchemasGetSchemaPostAsync(
                        getSchemaInput: new GetSchemaInput(
                            sleekflowCompanyId: companyUser.CompanyId,
                            id: form.CrmHubSchemaId))).Data.Schema;
                    formVM.CrmHubSchema = new FacebookLeadAdsFormCrmHubSchemaViewModel
                    {
                        Id = schema.Id,
                        DisplayName = schema.DisplayName
                    };
                }

                formVMs.Add(formVM);
            }

            connectionVMs.Add(
                new FacebookLeadAdsConnectionViewModel
                {
                    Id = authenticatedFacebookConfig.Id,
                    PageId = authenticatedFacebookConfig.PageId,
                    Name = authenticatedFacebookConfig.PageId,
                    ConnectedDate = authenticatedFacebookConfig.ConnectedDateTime,
                    Forms = formVMs,
                    ConfigurationStatus = facebookLeadAdsForms.Count == 0 ||
                                          facebookLeadAdsForms.Exists(f => f.SetupStatus != FacebookLeadAdsFormSetupStatus.Completed)
                        ? FacebookLeadAdsConnectionConfigurationStatus.InProgress
                        : FacebookLeadAdsConnectionConfigurationStatus.Completed
                });
        }

        return Ok(connectionVMs);
    }

    [HttpGet]
    [Route("FacebookLeadAdsForms/{id}/FieldMappings")]
    public async Task<IActionResult> GetFacebookLeadAdsFormFieldMappings(
        [FromRoute] long id)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(ModelState);
        }

        var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (companyUser == null)
        {
            return Unauthorized();
        }

        var companyId = companyUser.CompanyId;

        var facebookLeadAdsForm = await _facebookLeadAdsFormRepository.GetFacebookLeadAdsFormAsync(
            id,
            companyId);

        if (facebookLeadAdsForm == null)
        {
            return NotFound();
        }

        var fieldMappings = facebookLeadAdsForm.FieldMappings
            .ToList();

        if (fieldMappings.Count == 0)
        {
            return Ok(new List<FacebookLeadAdsFormFieldMappingViewModel>());
        }

        var facebookConfig = await _facebookConfigRepository.GetFacebookConfigByIdAsync(
            facebookLeadAdsForm.FacebookConfigId,
            companyId);

        if (facebookConfig == null)
        {
            return NotFound();
        }

        try
        {
            var form = await _leadAdsService.GetFacebookLeadGenForm(
                _httpClientFactory.CreateClient(HttpClientHandlerName.Default),
                facebookConfig.PageId,
                facebookConfig.PageAccessToken,
                facebookLeadAdsForm.FacebookFormId);

            if (form == null)
            {
                return NotFound();
            }

            var facebookLeadAdsFormFieldIds = fieldMappings
                .Select(m => m.FacebookLeadAdsFormFieldId)
                .ToList();
            var facebookLeadAdsFormFields = _mapper.Map<List<FacebookLeadAdsFormFacebookFieldViewModel>>(
                form.Questions.Where(q => facebookLeadAdsFormFieldIds.Contains(q.Id)));

            var fieldMappingVMs = new List<FacebookLeadAdsFormFieldMappingViewModel>();
            var sleekflowFieldIds = fieldMappings
                .Select(m => m.SleekflowFieldId)
                .ToList();

            var companyCustomUserProfileFields = await _appDbContext.CompanyCustomUserProfileFields
                .AsNoTracking()
                .Include(f => f.CustomUserProfileFieldLinguals)
                .Include(f => f.CustomUserProfileFieldOptions)
                .Where(f => f.CompanyId == companyUser.CompanyId
                            && sleekflowFieldIds.Contains(f.Id))
                .ToListAsync();

            SchemaDto schema = null; // Could be null if only contact fields are mapped
            if (facebookLeadAdsForm.CrmHubSchemaId != null)
            {
                schema = (await _schemasApi.SchemasGetSchemaPostAsync(
                    getSchemaInput: new GetSchemaInput(
                        sleekflowCompanyId: companyId,
                        id: facebookLeadAdsForm.CrmHubSchemaId))).Data.Schema;
            }

            foreach (var fieldMapping in fieldMappings)
            {
                var facebookLeadAdsFormField = facebookLeadAdsFormFields
                    .Find(f => f.Id == fieldMapping.FacebookLeadAdsFormFieldId);

                switch (fieldMapping.SleekflowFieldType)
                {
                    case SleekflowFieldType.CompanyCustomUserProfileField:
                        var companyCustomUserProfileField = companyCustomUserProfileFields
                            .Find(f => f.Id == fieldMapping.SleekflowFieldId);
                        if (facebookLeadAdsFormField != null && companyCustomUserProfileField != null)
                        {
                            fieldMappingVMs.Add(
                                new FacebookLeadAdsFormFieldMappingViewModel
                                {
                                    Id = fieldMapping.Id,
                                    FacebookLeadAdsFormField = facebookLeadAdsFormField,
                                    SleekflowField = new FacebookLeadAdsFormMappedSleekflowFieldViewModel
                                    {
                                        SleekflowFieldType = SleekflowFieldType.CompanyCustomUserProfileField,
                                        CompanyCustomUserProfileField = _mapper.Map<CompanyCustomUserProfileFieldViewModel>(
                                            companyCustomUserProfileField)
                                    }
                                });
                        }

                        break;
                    case SleekflowFieldType.CrmHubSchemaProperty:
                        if (schema != null)
                        {
                            var schemaProperty = schema.Properties
                                .Find(p => p.Id == fieldMapping.SleekflowFieldId);
                            if (facebookLeadAdsFormField != null && schemaProperty != null)
                            {
                                fieldMappingVMs.Add(
                                    new FacebookLeadAdsFormFieldMappingViewModel
                                    {
                                        Id = fieldMapping.Id,
                                        FacebookLeadAdsFormField = facebookLeadAdsFormField,
                                        SleekflowField = new FacebookLeadAdsFormMappedSleekflowFieldViewModel
                                        {
                                            SleekflowFieldType = SleekflowFieldType.CrmHubSchemaProperty,
                                            CrmHubSchemaProperty = new CrmHubSchemaPropertyViewModel
                                            {
                                                DataType = schemaProperty.DataType,
                                                DisplayName = schemaProperty.DisplayName,
                                                DisplayOrder = schemaProperty.DisplayOrder,
                                                Id = schemaProperty.Id,
                                                IsPinned = schemaProperty.IsPinned,
                                                IsRequired = schemaProperty.IsRequired,
                                                IsSearchable = schemaProperty.IsSearchable,
                                                IsVisible = schemaProperty.IsVisible,
                                                UniqueName = schemaProperty.UniqueName
                                            }
                                        }
                                    });
                            }
                        }

                        break;
                    case SleekflowFieldType.UserProfileFirstNameField:
                        fieldMappingVMs.Add(
                            new FacebookLeadAdsFormFieldMappingViewModel
                            {
                                Id = fieldMapping.Id,
                                FacebookLeadAdsFormField = facebookLeadAdsFormField,
                                SleekflowField = new FacebookLeadAdsFormMappedSleekflowFieldViewModel
                                {
                                    SleekflowFieldType = SleekflowFieldType.UserProfileFirstNameField,
                                    UserProfileFirstNameField = new UserProfileFirstNameFieldViewModel
                                    {
                                        DisplayName = UserProfileFirstNameFieldDisplayName,
                                        Id = UserProfileFirstNameFieldId
                                    }
                                }
                            });

                        break;
                    case SleekflowFieldType.UserProfileLastNameField:
                        fieldMappingVMs.Add(
                            new FacebookLeadAdsFormFieldMappingViewModel
                            {
                                Id = fieldMapping.Id,
                                FacebookLeadAdsFormField = facebookLeadAdsFormField,
                                SleekflowField = new FacebookLeadAdsFormMappedSleekflowFieldViewModel
                                {
                                    SleekflowFieldType = SleekflowFieldType.UserProfileLastNameField,
                                    UserProfileLastNameField = new UserProfileLastNameFieldViewModel
                                    {
                                        DisplayName = UserProfileLastNameFieldDisplayName,
                                        Id = UserProfileLastNameFieldId
                                    }
                                }
                            });

                        break;
                }
            }

            var fieldMappingsVM = new FacebookLeadAdsFormFieldMappingsViewModel
            {
                FieldMappings = fieldMappingVMs
            };
            if (schema != null)
            {
                fieldMappingsVM.CrmHubSchema = new FacebookLeadAdsFormCrmHubSchemaViewModel
                {
                    Id = schema.Id,
                    DisplayName = schema.DisplayName
                };
            }

            return Ok(fieldMappingsVM);
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "Failed to retrieve Facebook Lead Ads form fields for" +
                "CompanyId: {CompanyId}, FacebookLeadAdsFormId: {FacebookLeadAdsFormId}. Error: {ErrorContent}",
                companyUser.CompanyId,
                facebookLeadAdsForm.Id,
                ex.Message);

            return BadRequest("Failed to retrieve Facebook Lead Ads form fields.");
        }
    }

    public class UpdateFacebookLeadAdsFormFieldMappingsRequest
    {
        public string SchemaDisplayName { get; set; }

        public List<FacebookLeadAdsFormFieldMappingDto> FacebookLeadAdsFormFieldMappings { get; set; }
    }

    [HttpPut]
    [Route("FacebookLeadAdsForms/{id}/FieldMappings")]
    public async Task<IActionResult> UpdateFacebookLeadAdsFormFieldMappings(
        [FromRoute] long id,
        [FromBody] UpdateFacebookLeadAdsFormFieldMappingsRequest request)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(ModelState);
        }

        var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (companyUser == null)
        {
            return Unauthorized();
        }

        var facebookLeadAdsForm = await _facebookLeadAdsFormRepository.GetFacebookLeadAdsFormAsync(
            id,
            companyUser.CompanyId);

        if (facebookLeadAdsForm == null)
        {
            return BadRequest("The specified facebook lead ads form does not exist.");
        }

        var fieldMappings = facebookLeadAdsForm.FieldMappings;
        var requestFieldMappings = request.FacebookLeadAdsFormFieldMappings;
        var requestCustomUserProfileFieldMappings = requestFieldMappings
            .Where(m => m.SleekflowFieldType == SleekflowFieldType.CompanyCustomUserProfileField)
            .ToList();

        var requestFieldMappingCustomUserProfileFieldIds = requestCustomUserProfileFieldMappings
            .Select(m => m.SleekflowFieldId)
            .ToList();

        // clear mappings of un-mapped fields
        fieldMappings.RemoveAll(
            m =>
                (m.SleekflowFieldType == SleekflowFieldType.CompanyCustomUserProfileField
                 || m.SleekflowFieldType == SleekflowFieldType.UserProfileFirstNameField
                 || m.SleekflowFieldType == SleekflowFieldType.UserProfileLastNameField)
                 && !requestFieldMappingCustomUserProfileFieldIds.Contains(m.SleekflowFieldId));

        // create or update contact field mappings
        foreach (var requestCustomUserProfileFieldMapping in requestCustomUserProfileFieldMappings)
        {
            var existingFieldMapping = fieldMappings
                .Find(m =>
                    m.SleekflowFieldId == requestCustomUserProfileFieldMapping.SleekflowFieldId);
            if (existingFieldMapping == null)
            {
                fieldMappings.Add(new FacebookLeadAdsFormFieldMapping
                {
                    CompanyId = companyUser.CompanyId,
                    FacebookLeadAdsFormFieldId = requestCustomUserProfileFieldMapping.FacebookLeadAdsFormFieldId,
                    SleekflowFieldId = requestCustomUserProfileFieldMapping.SleekflowFieldId,
                    SleekflowFieldType = SleekflowFieldType.CompanyCustomUserProfileField
                });
            }
            else
            {
                existingFieldMapping.FacebookLeadAdsFormFieldId =
                    requestCustomUserProfileFieldMapping.FacebookLeadAdsFormFieldId;
            }
        }

        var sleekflowStaff = new SleekflowStaff(
            sleekflowStaffId: companyUser.Id.ToString(),
            sleekflowStaffTeamIds: await GetTeamIdsBySleekflowStaffAsync(companyUser));

        var requestSchemaPropertyFieldMappings =
            requestFieldMappings
                .Where(m =>
                    m.SleekflowFieldType == SleekflowFieldType.CrmHubSchemaProperty)
                .ToList();
        var requestSchemaProperties = requestSchemaPropertyFieldMappings
            .Select(m => m.SchemaProperty)
            .ToList();
        var schemaId = facebookLeadAdsForm.CrmHubSchemaId;
        if (string.IsNullOrEmpty(schemaId))
        {
            // first-time configuration; create schema before mappings
            var createdSchema = (await _schemasApi.SchemasCreateSchemaPostAsync(
                createSchemaInput: new CreateSchemaInput(
                    sleekflowCompanyId: companyUser.CompanyId,
                    displayName: request.SchemaDisplayName,
                    uniqueName: facebookLeadAdsForm.FacebookFormId,
                    relationshipType: FormSchemaRelationshipType,
                    primaryPropertyInput: new PrimaryPropertyInput(
                        displayName: LeadIdPrimaryPropertyDisplayName,
                        uniqueName: LeadIdPrimaryPropertyUniqueName,
                        dataType: new IDataType(SchemaPropertyDataTypes.SingleLineText),
                        isVisible: true,
                        isPinned: true,
                        isSearchable: true,
                        primaryPropertyConfig: new PrimaryPropertyConfig(
                            isAutoGenerated: false),
                        createdBy: sleekflowStaff),
                    propertyInputs: requestSchemaProperties.Select(
                        p => new PropertyInput(
                            displayName: p.DisplayName,
                            uniqueName: p.UniqueName,
                            dataType: new IDataType(SchemaPropertyDataTypes.SingleLineText),
                            isRequired: false,
                            isVisible: true,
                            isPinned: true,
                            isSearchable: true,
                            displayOrder: p.DisplayOrder,
                            createdBy: sleekflowStaff)).ToList(),
                    schemaAccessibilitySettings: new SchemaAccessibilitySettings(
                        category: FormSchemaCategory)))).Data.Schema;

            // create mappings for first-time configuration
            var schemaProperties = createdSchema.Properties;
            foreach (var schemaProperty in schemaProperties)
            {
                var schemaPropertyFieldMapping = requestSchemaPropertyFieldMappings.Find(
                    m => m.SchemaProperty.UniqueName == schemaProperty.UniqueName);
                facebookLeadAdsForm.FieldMappings.Add(new FacebookLeadAdsFormFieldMapping
                {
                    CompanyId = companyUser.CompanyId,
                    FacebookLeadAdsFormFieldId = schemaPropertyFieldMapping.FacebookLeadAdsFormFieldId,
                    SleekflowFieldId = schemaProperty.Id,
                    SleekflowFieldType = SleekflowFieldType.CrmHubSchemaProperty
                });
            }

            facebookLeadAdsForm.CrmHubSchemaId = createdSchema.Id;
        }
        else
        {
            var requestSchemaPropertyIds = requestSchemaProperties
                .Where(p => p.Id != null)
                .Select(p => p.Id);

            // clear mappings of removed fields
            fieldMappings.RemoveAll(m =>
                m.SleekflowFieldType == SleekflowFieldType.CrmHubSchemaProperty
                && !requestSchemaPropertyIds.Contains(m.SleekflowFieldId));

            var updatedSchema = (await _schemasApi.SchemasUpdateSchemaPostAsync(
                updateSchemaInput: new UpdateSchemaInput(
                    sleekflowCompanyId: companyUser.CompanyId,
                    id: schemaId,
                    displayName: request.SchemaDisplayName,
                    primaryPropertyDisplayName: LeadIdPrimaryPropertyDisplayName,
                    isEnabled: true,
                    properties: requestSchemaProperties.Select(
                        p => new Property(
                            id: p.Id,
                            displayName: p.DisplayName,
                            uniqueName: p.UniqueName,
                            dataType: new IDataType(SchemaPropertyDataTypes.SingleLineText),
                            isRequired: false,
                            isVisible: true,
                            isPinned: true,
                            isSearchable: true,
                            displayOrder: p.DisplayOrder,
                            createdBy: sleekflowStaff)).ToList()))).Data.Schema;

            foreach (var requestSchemaPropertyFieldMapping in requestSchemaPropertyFieldMappings)
            {
                if (requestSchemaPropertyFieldMapping.SchemaProperty.Id != null)
                {
                    // previously created and mapped field, to be updated
                    var fieldMappingToUpdate = fieldMappings.Find(
                        m =>
                            m.SleekflowFieldId == requestSchemaPropertyFieldMapping.SchemaProperty.Id);
                    fieldMappingToUpdate.FacebookLeadAdsFormFieldId =
                        requestSchemaPropertyFieldMapping.FacebookLeadAdsFormFieldId;
                }
                else
                {
                    // newly created field, to be mapped
                    var createdSchemaProperty = updatedSchema.Properties.Find(
                        p => p.UniqueName == requestSchemaPropertyFieldMapping.SchemaProperty.UniqueName);
                    facebookLeadAdsForm.FieldMappings.Add(
                        new FacebookLeadAdsFormFieldMapping
                        {
                            CompanyId = companyUser.CompanyId,
                            FacebookLeadAdsFormFieldId = requestSchemaPropertyFieldMapping.FacebookLeadAdsFormFieldId,
                            SleekflowFieldId = createdSchemaProperty.Id,
                            SleekflowFieldType = SleekflowFieldType.CrmHubSchemaProperty
                        });
                }
            }
        }

        var requestUserProfileFirstNameFieldMapping =
            requestFieldMappings
                .Find(m =>
                    m.SleekflowFieldType == SleekflowFieldType.UserProfileFirstNameField
                    && m.SleekflowFieldId == UserProfileFirstNameFieldId);
        if (requestUserProfileFirstNameFieldMapping != null)
        {
            var existingUserProfileFirstNameFieldMapping =
                fieldMappings
                    .Find(m =>
                        m.SleekflowFieldType == SleekflowFieldType.UserProfileFirstNameField
                        && m.SleekflowFieldId == UserProfileFirstNameFieldId);
            if (existingUserProfileFirstNameFieldMapping == null)
            {
                fieldMappings.Add(new FacebookLeadAdsFormFieldMapping
                {
                    CompanyId = companyUser.CompanyId,
                    FacebookLeadAdsFormFieldId = requestUserProfileFirstNameFieldMapping.FacebookLeadAdsFormFieldId,
                    SleekflowFieldId = UserProfileFirstNameFieldId,
                    SleekflowFieldType = SleekflowFieldType.UserProfileFirstNameField
                });
            }
            else
            {
                existingUserProfileFirstNameFieldMapping.FacebookLeadAdsFormFieldId =
                    requestUserProfileFirstNameFieldMapping.FacebookLeadAdsFormFieldId;
            }
        }

        var requestUserProfileLastNameFieldMapping =
            requestFieldMappings
                .Find(m =>
                    m.SleekflowFieldType == SleekflowFieldType.UserProfileLastNameField
                    && m.SleekflowFieldId == UserProfileLastNameFieldId);
        if (requestUserProfileLastNameFieldMapping != null)
        {
            var existingUserProfileLastNameFieldMapping =
                fieldMappings
                    .Find(m =>
                        m.SleekflowFieldType == SleekflowFieldType.UserProfileLastNameField
                        && m.SleekflowFieldId == UserProfileLastNameFieldId);
            if (existingUserProfileLastNameFieldMapping == null)
            {
                fieldMappings.Add(new FacebookLeadAdsFormFieldMapping
                {
                    CompanyId = companyUser.CompanyId,
                    FacebookLeadAdsFormFieldId = requestUserProfileLastNameFieldMapping.FacebookLeadAdsFormFieldId,
                    SleekflowFieldId = UserProfileLastNameFieldId,
                    SleekflowFieldType = SleekflowFieldType.UserProfileLastNameField
                });
            }
            else
            {
                existingUserProfileLastNameFieldMapping.FacebookLeadAdsFormFieldId =
                    requestUserProfileLastNameFieldMapping.FacebookLeadAdsFormFieldId;
            }
        }

        // Validation done on FE; if it's passed then we can assume that the form is properly setup
        facebookLeadAdsForm.SetupStatus = FacebookLeadAdsFormSetupStatus.Completed;

        await _appDbContext.SaveChangesAsync();

        return Ok();
    }

    [HttpGet]
    [Route("FacebookLeadAdsForms/{id}/FacebookFields")]
    public async Task<IActionResult> GetFacebookLeadAdsFormFacebookFields([FromRoute] long id)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(ModelState);
        }

        var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (companyUser == null)
        {
            return Unauthorized();
        }

        var facebookLeadAdsForm = await _facebookLeadAdsFormRepository.GetFacebookLeadAdsFormAsync(
            id,
            companyUser.CompanyId);

        if (facebookLeadAdsForm == null)
        {
            return NotFound();
        }

        var facebookConfig = await _facebookConfigRepository.GetFacebookConfigByIdAsync(
            facebookLeadAdsForm.FacebookConfigId,
            companyUser.CompanyId);

        if (facebookConfig == null)
        {
            return NotFound();
        }

        try
        {
            var form = await _leadAdsService.GetFacebookLeadGenForm(
                _httpClientFactory.CreateClient(HttpClientHandlerName.Default),
                facebookConfig.PageId,
                facebookConfig.PageAccessToken,
                facebookLeadAdsForm.FacebookFormId);

            if (form == null)
            {
                return NotFound();
            }

            return Ok(_mapper.Map<List<FacebookLeadAdsFormFacebookFieldViewModel>>(form.Questions));
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "Failed to retrieve Facebook Lead Ads form fields for" +
                "CompanyId: {CompanyId}, FacebookLeadAdsFormId: {FacebookLeadAdsFormId}. Error: {ErrorContent}",
                companyUser.CompanyId,
                facebookLeadAdsForm.Id,
                ex.Message);

            return BadRequest("Failed to retrieve Facebook Lead Ads form fields.");
        }
    }

    private async Task<List<string>> GetTeamIdsBySleekflowStaffAsync(Staff sleekflowStaff)
    {
        var companyId = sleekflowStaff.CompanyId;
        var identityId = sleekflowStaff.IdentityId;

        var companyTeams = await _companyTeamService.GetCompanyteamFromStaffId(companyId, identityId);

        return companyTeams.Select(t => t.Id.ToString()).ToList();
    }
}