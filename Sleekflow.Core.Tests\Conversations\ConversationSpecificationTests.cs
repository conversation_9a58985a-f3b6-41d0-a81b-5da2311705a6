using Microsoft.EntityFrameworkCore;
using ShopifySharp;
using Sleekflow.Apis.MessagingHub.Model;
using Stripe;
using Travis_backend.AutomationDomain.Models;
using Travis_backend.ChannelDomain.ViewModels;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.Constants;
using Travis_backend.ContactDomain.Models;
using Travis_backend.ConversationDomain;
using Travis_backend.ConversationDomain.Models;
using Travis_backend.ConversationDomain.ViewModels;
using Travis_backend.Enums;
using Travis_backend.MessageDomain.Models;
using Charge = Stripe.Charge;
using Customer = Stripe.Customer;

namespace Sleekflow.Core.Tests.Conversations;

[TestFixture]
public class ConversationSpecificationTests
{
    private StaffAccessControlAggregate _adminA;
    private StaffAccessControlAggregate _teamAdminA;
    private StaffAccessControlAggregate _staffA;
    private StaffAccessControlAggregate _adminB;
    private StaffAccessControlAggregate _teamAdminB;
    private StaffAccessControlAggregate _staffB;
    private StaffAccessControlAggregate _adminC;
    private StaffAccessControlAggregate _adminInAnotherCompany;
    private StaffAccessControlAggregate _staffC;
    private StaffAccessControlAggregate _staffD;
    private TeamAccessControlAggregate _teamA;
    private TeamAccessControlAggregate _teamB;
    private string _companyId;
    private string _anotherCompanyId;

    private DbContextOptions<ConversationDbContext> _options;

    public class ConversationDbContext : DbContext
    {
        public DbSet<Conversation> Conversations { get; set; }

        public ConversationDbContext(DbContextOptions<ConversationDbContext> options)
            : base(options)
        {
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<Conversation>();

            var entityTypes =
                modelBuilder.Model.GetEntityTypes()
                    .ToList(); // Use ToList() to avoid modifying the collection while iterating

            foreach (var entityType in entityTypes)
            {
                // Skip entities that are already configured as keyless
                if (entityType.IsKeyless)
                {
                    continue;
                }

                // Check if the entity has a primary key defined
                var primaryKey = entityType.FindPrimaryKey();

                if (primaryKey == null)
                {
                    // Ignore the entity
                    modelBuilder.Ignore(entityType.ClrType);
                }
            }

            modelBuilder.Entity<Conversation>()
                .Ignore(c => c.Metadata);

            modelBuilder.Entity<ConversationMessage>()
                .HasOne(cm => cm.Conversation)
                .WithMany(c => c.ChatHistory)
                .OnDelete(DeleteBehavior.Cascade);

            modelBuilder.Entity<ConversationMessage>()
                .Ignore(c => c.Metadata);

            modelBuilder.Ignore<WabaDto>();
            modelBuilder.Ignore<WabaPhoneNumberDto>();
            modelBuilder.Ignore<WhatsappCloudApiActionMessageParameterObject>();
            modelBuilder.Ignore<WhatsappCloudApiActionObject>();
            modelBuilder.Ignore<WhatsappCloudApiConfigObject>();
            modelBuilder.Ignore<WhatsappCloudApiContactObject>();
            modelBuilder.Ignore<WhatsappCloudApiHeaderObject>();
            modelBuilder.Ignore<WhatsappCloudApiInteractiveObject>();
            modelBuilder.Ignore<WhatsappCloudApiMediaObject>();
            modelBuilder.Ignore<WhatsappCloudApiParameterActionObject>();
            modelBuilder.Ignore<WhatsappCloudApiParameterObject>();
            modelBuilder.Ignore<WhatsappCloudApiTemplateMessageCardComponentObject>();
            modelBuilder.Ignore<WhatsappCloudApiTemplateMessageCardObject>();
            modelBuilder.Ignore<WhatsappCloudApiTemplateMessageComponentObject>();
            modelBuilder.Ignore<WhatsappCloudApiWebhookInteractiveReplyObject>();
            modelBuilder.Ignore<WhatsappCloudApiWebhookOrderObject>();
            modelBuilder.Ignore<WhatsappCloudApiLocationObject>();
            modelBuilder.Ignore<WhatsappCloudApiWebhookLocationObject>();
            modelBuilder.Ignore<WhatsappCloudApiWebhookReferralObject>();
            modelBuilder.Ignore<WhatsappCloudApiReactionObject>();
            modelBuilder.Ignore<WhatsappCloudApiWebhookTemplateButtonReplyObject>();
            modelBuilder.Ignore<ContactReplyMessage>();
            modelBuilder.Ignore<CronJobObject>();
            modelBuilder.Ignore<DefaultAction>();
            modelBuilder.Ignore<Element>();
            modelBuilder.Ignore<StripeResponse>();
            modelBuilder.Ignore<WhatsappCloudApiOrderDetailObject>();
            modelBuilder.Ignore<Fulfillment>();
            modelBuilder.Ignore<MetaField>();
            modelBuilder.Ignore<Account>();
            modelBuilder.Ignore<ApplicationFee>();
            modelBuilder.Ignore<BalanceTransaction>();
            modelBuilder.Ignore<CashBalance>();
            modelBuilder.Ignore<Charge>();
            modelBuilder.Ignore<Coupon>();
            modelBuilder.Ignore<Customer>();
            modelBuilder.Ignore<Invoice>();
            modelBuilder.Ignore<PaymentIntent>();
            modelBuilder.Ignore<CreateTemplateWithContentApiResponse>();
            modelBuilder.Ignore<BillRecord>();
            modelBuilder.Ignore<BroadcastHistory>();
            modelBuilder.Ignore<Company>();
            modelBuilder.Ignore<UserProfile>();
            modelBuilder.Ignore<WhatsappTwilioContentApiObject>();

            base.OnModelCreating(modelBuilder);
        }
    }

    [SetUp]
    public void Setup()
    {
        _options = new DbContextOptionsBuilder<ConversationDbContext>()
            .UseInMemoryDatabase("TestConversationDb")
            .Options;

        _companyId = "sleekflow";
        _anotherCompanyId = "another_companyId";

        // Teams
        _teamA = new TeamAccessControlAggregate
        {
            Id = 1,
            TeamMemberStaffIds = new List<long>
            {
                1, 2, 3
            }
        };
        _teamB = new TeamAccessControlAggregate
        {
            Id = 2,
            TeamMemberStaffIds = new List<long>
            {
                4, 5, 6
            }
        };

        // Team A members
        _adminA = new StaffAccessControlAggregate
        {
            StaffId = 1,
            RoleType = StaffUserRole.Admin,
            CompanyId = _companyId,
            AssociatedTeams = new List<TeamAccessControlAggregate>
            {
                _teamA
            }
        };
        _teamAdminA = new StaffAccessControlAggregate
        {
            StaffId = 2,
            RoleType = StaffUserRole.TeamAdmin,
            CompanyId = _companyId,
            AssociatedTeams = new List<TeamAccessControlAggregate>
            {
                _teamA
            }
        };
        _staffA = new StaffAccessControlAggregate
        {
            StaffId = 3,
            RoleType = StaffUserRole.Staff,
            CompanyId = _companyId,
            AssociatedTeams = new List<TeamAccessControlAggregate>
            {
                _teamA
            }
        };

        // Team B members
        _adminB = new StaffAccessControlAggregate
        {
            StaffId = 4,
            RoleType = StaffUserRole.Admin,
            CompanyId = _companyId,
            AssociatedTeams = new List<TeamAccessControlAggregate>
            {
                _teamB
            }
        };
        _teamAdminB = new StaffAccessControlAggregate
        {
            StaffId = 5,
            RoleType = StaffUserRole.TeamAdmin,
            CompanyId = _companyId,
            AssociatedTeams = new List<TeamAccessControlAggregate>
            {
                _teamB
            }
        };
        _staffB = new StaffAccessControlAggregate
        {
            StaffId = 6,
            RoleType = StaffUserRole.Staff,
            CompanyId = _companyId,
            AssociatedTeams = new List<TeamAccessControlAggregate>
            {
                _teamB
            }
        };

        // Others
        _adminC = new StaffAccessControlAggregate
        {
            StaffId = 7, RoleType = StaffUserRole.Admin, CompanyId = _companyId
        };
        _staffC = new StaffAccessControlAggregate
        {
            StaffId = 8, RoleType = StaffUserRole.Staff, CompanyId = _companyId
        };

        _adminInAnotherCompany = new StaffAccessControlAggregate
        {
            StaffId = 9, RoleType = StaffUserRole.Admin, CompanyId = _anotherCompanyId
        };

        _staffD = new StaffAccessControlAggregate
        {
            StaffId = 10, RoleType = StaffUserRole.Staff, CompanyId = _companyId
        };
    }

    [TearDown]
    public void TearDown()
    {
        using (var context = new ConversationDbContext(_options))
        {
            context.Database.EnsureDeleted();
        }
    }

    [Test]
    public void HealthCheck_CanConnectToDatabase()
    {
        using (var context = new ConversationDbContext(_options))
        {
            // Ensure the database can be connected to and queried
            Assert.DoesNotThrow(() => context.Conversations.ToList());
        }

        // Team Admin A As the collaborator
        var collaborator = new AdditionalAssignee
        {
            Id = 2, CompanyId = _companyId, AssigneeId = _teamAdminA.StaffId
        };


        var teamAdminAAsCollaboratorAssignedTeamBAndNoContactOwnerConversation = new Conversation
        {
            Id = "teamAdminAAsCollaboratorAssignedTeamBAndNoContactOwnerConversation",
            CompanyId = _companyId,
            Status = null,
            ActiveStatus = ActiveStatus.Active,
            ChatHistory = null,
            UpdatedTime = default,
            ModifiedAt = default,
            CreatedAt = null,
            AssigneeId = null,
            AdditionalAssignees = [collaborator],
            AssignedTeamId = _teamB.Id
        };

        var specification = new TeamAdminConversationSpecification(_teamAdminA);

        var specResult =
            specification.IsSatisfiedBy(teamAdminAAsCollaboratorAssignedTeamBAndNoContactOwnerConversation);

        Assert.That(specResult, Is.EqualTo(true));
    }

    [Test]
    public void Conversation_TeamAdminConversationSpecification_SatisfiesSpecification()
    {
        using (var context = new ConversationDbContext(_options))
        {
            context.Database.EnsureDeleted();
        }

        // success test cases
        var unassignedConversation = new Conversation
        {
            Id = "unassignedConversation",
            CompanyId = _companyId,
            Status = null,
            ActiveStatus = ActiveStatus.Active,
            ChatHistory = null,
            UpdatedTime = default,
            ModifiedAt = default,
            CreatedAt = null,
            AssigneeId = null,
            AdditionalAssignees = null,
            AssignedTeamId = null
        };

        // Team Inbox Conversation in the same team
        var teamAConversation = new Conversation
        {
            Id = "_teamAConversation",
            CompanyId = _companyId,
            Status = null,
            ActiveStatus = ActiveStatus.Active,
            ChatHistory = null,
            UpdatedTime = default,
            ModifiedAt = default,
            CreatedAt = null,
            AssigneeId = null,
            AdditionalAssignees = null,
            AssignedTeamId = _teamA.Id
        };

        // Team Admin A assigned as contact owner with no team value
        var teamAdminAAsContactOwnerWithNoAssignedTeamConversation = new Conversation
        {
            Id = "teamAdminAAsContactOwnerWithNoAssignedTeamConversation",
            CompanyId = _companyId,
            Status = null,
            ActiveStatus = ActiveStatus.Active,
            ChatHistory = null,
            UpdatedTime = default,
            ModifiedAt = default,
            CreatedAt = null,
            AssigneeId = _teamAdminA.StaffId,
            AdditionalAssignees = null,
            AssignedTeamId = null
        };

        // Team Admin A assigned as contact owner but assigned team is not the same team (Team B)
        var teamAdminAAsContactOwnerAssignedTeamBConversation = new Conversation
        {
            Id = "teamAdminAAsContactOwnerAssignedTeamBConversation",
            CompanyId = _companyId,
            Status = null,
            ActiveStatus = ActiveStatus.Active,
            ChatHistory = null,
            UpdatedTime = default,
            ModifiedAt = default,
            CreatedAt = null,
            AssigneeId = _teamAdminA.StaffId,
            AdditionalAssignees = null,
            AssignedTeamId = _teamB.Id
        };

        // Team Admin A assigned as contact owner but assigned team is not the same team (Team B)
        var teamAdminAAsContactOwnerAssignedTeamAConversation = new Conversation
        {
            Id = "teamAdminAAsContactOwnerAssignedTeamAConversation",
            CompanyId = _companyId,
            Status = null,
            ActiveStatus = ActiveStatus.Active,
            ChatHistory = null,
            UpdatedTime = default,
            ModifiedAt = default,
            CreatedAt = null,
            AssigneeId = _teamAdminA.StaffId,
            AdditionalAssignees = null,
            AssignedTeamId = _teamB.Id
        };

        // Team Admin A As the collaborator
        var collaborator = new AdditionalAssignee
        {
            CompanyId = _companyId, AssigneeId = _teamAdminA.StaffId
        };

        var teamAdminAAsCollaboratorWithNoAssignedTeamAndContactOwnerConversation = new Conversation
        {
            Id = "teamAdminAAsCollaboratorWithNoAssignedTeamAndContactOwnerConversation",
            CompanyId = _companyId,
            Status = null,
            ActiveStatus = ActiveStatus.Active,
            ChatHistory = null,
            UpdatedTime = default,
            ModifiedAt = default,
            CreatedAt = null,
            AssigneeId = null,
            AdditionalAssignees = [collaborator],
            AssignedTeamId = null
        };

        var teamAdminAAsCollaboratorWithNoAssignedTeamAndContactOwnerAsTeamAdminBConversation = new Conversation
        {
            Id = "teamAdminAAsCollaboratorWithNoAssignedTeamAndContactOwnerAsTeamAdminBConversation",
            CompanyId = _companyId,
            Status = null,
            ActiveStatus = ActiveStatus.Active,
            ChatHistory = null,
            UpdatedTime = default,
            ModifiedAt = default,
            CreatedAt = null,
            AssigneeId = _teamAdminB.StaffId,
            AdditionalAssignees = [GetCollaborator(_companyId, _teamAdminA.StaffId)],
            AssignedTeamId = null
        };

        var teamAdminAAsCollaboratorAssignedTeamBAndNoContactOwnerConversation = new Conversation
        {
            Id = "teamAdminAAsCollaboratorAssignedTeamBAndNoContactOwnerConversation",
            CompanyId = _companyId,
            Status = null,
            ActiveStatus = ActiveStatus.Active,
            ChatHistory = null,
            UpdatedTime = default,
            ModifiedAt = default,
            CreatedAt = null,
            AssigneeId = null,
            AdditionalAssignees = [GetCollaborator(_companyId, _teamAdminA.StaffId)],
            AssignedTeamId = _teamB.Id
        };

        var teamAdminAAsCollaboratorAssignedTeamBAndTeamAdminBAsContactOwnerConversation = new Conversation
        {
            Id = "teamAdminAAsCollaboratorAssignedTeamBAndTeamAdminBAsContactOwnerConversation",
            CompanyId = _companyId,
            Status = null,
            ActiveStatus = ActiveStatus.Active,
            ChatHistory = null,
            UpdatedTime = default,
            ModifiedAt = default,
            CreatedAt = null,
            AssigneeId = _teamAdminB.StaffId,
            AdditionalAssignees = [GetCollaborator(_companyId, _teamAdminA.StaffId)],
            AssignedTeamId = _teamB.Id
        };

        var teamAdminAAsCollaboratorAssignedTeamAAndNoContactOwnerConversation = new Conversation
        {
            Id = "teamAdminAAsCollaboratorAssignedTeamAAndNoContactOwnerConversation",
            CompanyId = _companyId,
            Status = null,
            ActiveStatus = ActiveStatus.Active,
            ChatHistory = null,
            UpdatedTime = default,
            ModifiedAt = default,
            CreatedAt = null,
            AssigneeId = null,
            AdditionalAssignees = [GetCollaborator(_companyId, _teamAdminA.StaffId)],
            AssignedTeamId = _teamA.Id
        };

        var teamAdminAAsCollaboratorAssignedTeamAAndTeamAdminAAsContactOwnerConversation = new Conversation
        {
            Id = "teamAdminAAsCollaboratorAssignedTeamAAndTeamAdminAAsContactOwnerConversation",
            CompanyId = _companyId,
            Status = null,
            ActiveStatus = ActiveStatus.Active,
            ChatHistory = null,
            UpdatedTime = default,
            ModifiedAt = default,
            CreatedAt = null,
            AssigneeId = _teamAdminA.StaffId,
            AdditionalAssignees = [GetCollaborator(_companyId, _teamAdminA.StaffId)],
            AssignedTeamId = _teamA.Id
        };

        var teamAdminATeamMemberAsCollaboratorWithNoAssignedTeamAndContactOwnerConversation = new Conversation
        {
            Id = "teamAdminATeamMemberAsCollaboratorWithNoAssignedTeamAndContactOwnerConversation",
            CompanyId = _companyId,
            Status = null,
            ActiveStatus = ActiveStatus.Active,
            ChatHistory = null,
            UpdatedTime = default,
            ModifiedAt = default,
            CreatedAt = null,
            AssigneeId = null,
            AdditionalAssignees = [GetCollaborator(_companyId, _staffA.StaffId)],
            AssignedTeamId = null
        };

        var teamAdminATeamMemberAsCollaboratorWithNoAssignedTeamAndTeamAdminBAsContactOwnerConversation = new Conversation
        {
            Id = "teamAdminATeamMemberAsCollaboratorWithNoAssignedTeamAndTeamAdminBAsContactOwnerConversation",
            CompanyId = _companyId,
            Status = null,
            ActiveStatus = ActiveStatus.Active,
            ChatHistory = null,
            UpdatedTime = default,
            ModifiedAt = default,
            CreatedAt = null,
            AssigneeId = _teamAdminB.StaffId,
            AdditionalAssignees = [GetCollaborator(_companyId, _staffA.StaffId)],
            AssignedTeamId = _teamB.Id
        };

        var teamAdminATeamMemberAsCollaboratorAssignedTeamBndNoContactOwnerConversation = new Conversation
        {
            Id = "teamAdminATeamMemberAsCollaboratorAssignedTeamBndNoContactOwner",
            CompanyId = _companyId,
            Status = null,
            ActiveStatus = ActiveStatus.Active,
            ChatHistory = null,
            UpdatedTime = default,
            ModifiedAt = default,
            CreatedAt = null,
            AssigneeId = null,
            AdditionalAssignees = [GetCollaborator(_companyId, _staffA.StaffId)],
            AssignedTeamId = _teamB.Id
        };

        var teamAdminATeamMemberAsCollaboratorAssignedTeamBndTeamAdminBAsContactOwnerConversation = new Conversation
        {
            Id = "teamAdminATeamMemberAsCollaboratorAssignedTeamBndTeamAdminBAsContactOwnerConversation",
            CompanyId = _companyId,
            Status = null,
            ActiveStatus = ActiveStatus.Active,
            ChatHistory = null,
            UpdatedTime = default,
            ModifiedAt = default,
            CreatedAt = null,
            AssigneeId = null,
            AdditionalAssignees = [GetCollaborator(_companyId, _staffA.StaffId)],
            AssignedTeamId = _teamB.Id
        };


        var teamAdminAIsBeingMentionedWithNoAssignedTeamAndTeamAdminBAsContactOwnerConversation = new Conversation
        {
            Id = "teamAdminAIsBeingMentionedWithNoAssignedTeamAndContactOwnerConversation",
            CompanyId = _companyId,
            Status = null,
            ActiveStatus = ActiveStatus.Active,
            ChatHistory = GetChatHistory(_teamAdminA.StaffId),
            UpdatedTime = default,
            ModifiedAt = default,
            CreatedAt = null,
            AssigneeId = _teamAdminB.StaffId,
            AdditionalAssignees = null,
            AssignedTeamId = null
        };

        var teamBConversation = new Conversation
        {
            Id = "teamBConversation",
            CompanyId = _companyId,
            Status = null,
            ActiveStatus = ActiveStatus.Active,
            ChatHistory = null,
            UpdatedTime = default,
            ModifiedAt = default,
            CreatedAt = null,
            AssigneeId = null,
            AdditionalAssignees = null,
            AssignedTeamId = _teamB.Id
        };

        var teamAdminBWithNoAssignedTeamConversation = new Conversation
        {
            Id = "teamAdminBWithNoAssignedTeamConversation",
            CompanyId = _companyId,
            Status = null,
            ActiveStatus = ActiveStatus.Active,
            ChatHistory = null,
            UpdatedTime = default,
            ModifiedAt = default,
            CreatedAt = null,
            AssigneeId = _teamAdminB.StaffId,
            AdditionalAssignees = null,
            AssignedTeamId = null
        };

        var teamAdminAIsBeingMentionedButNotWithinTwoDaysWithAssignedTeamBndNoContactOwnerConversation =
            new Conversation
            {
                Id = "teamAdminAIsBeingMentionedButNotWithinTwoDaysWithAssignedTeamBndNoContactOwnerConversation",
                CompanyId = _companyId,
                Status = null,
                ActiveStatus = ActiveStatus.Active,
                ChatHistory = new List<ConversationMessage>{
                    new ()
                    {
                        Channel = ChannelTypes.Note,
                        MessageAssigneeId = _teamAdminA.StaffId,
                        UpdatedAt = DateTime.UtcNow.AddDays(-3)
                    }
                },
                UpdatedTime = default,
                ModifiedAt = default,
                CreatedAt = null,
                AssigneeId = null,
                AdditionalAssignees = null,
                AssignedTeamId = _teamB.Id
            };

        var teamAdminBIsBeingMentionedWithAssignedTeamBndNoContactOwnerConversation =
            new Conversation
            {
                Id = "teamAdminBIsBeingMentionedWithAssignedTeamBndNoContactOwnerConversation",
                CompanyId = _companyId,
                Status = null,
                ActiveStatus = ActiveStatus.Active,
                ChatHistory = new List<ConversationMessage>{
                    new ()
                    {
                        Channel = ChannelTypes.Note,
                        MessageAssigneeId = _teamAdminB.StaffId,
                        UpdatedAt = DateTime.UtcNow.AddDays(-2).AddSeconds(1)
                    }
                },
                UpdatedTime = default,
                ModifiedAt = default,
                CreatedAt = null,
                AssigneeId = null,
                AdditionalAssignees = null,
                AssignedTeamId = _teamB.Id
            };

        var conversationsToBeTested = new List<Conversation>();

        var canViewConversation = new List<Conversation>
        {
            unassignedConversation,
            teamAConversation,
            teamAdminAAsContactOwnerWithNoAssignedTeamConversation,
            teamAdminAAsContactOwnerAssignedTeamBConversation,
            teamAdminAAsContactOwnerAssignedTeamAConversation,
            teamAdminAAsCollaboratorWithNoAssignedTeamAndContactOwnerConversation,
            teamAdminAAsCollaboratorWithNoAssignedTeamAndContactOwnerAsTeamAdminBConversation,
            teamAdminAAsCollaboratorAssignedTeamBAndNoContactOwnerConversation,
            teamAdminAAsCollaboratorAssignedTeamBAndTeamAdminBAsContactOwnerConversation,
            teamAdminAAsCollaboratorAssignedTeamAAndNoContactOwnerConversation,
            teamAdminAAsCollaboratorAssignedTeamAAndTeamAdminAAsContactOwnerConversation,
            teamAdminATeamMemberAsCollaboratorWithNoAssignedTeamAndContactOwnerConversation,
            teamAdminATeamMemberAsCollaboratorWithNoAssignedTeamAndTeamAdminBAsContactOwnerConversation,
            teamAdminATeamMemberAsCollaboratorAssignedTeamBndNoContactOwnerConversation,
            teamAdminATeamMemberAsCollaboratorAssignedTeamBndTeamAdminBAsContactOwnerConversation,
            teamAdminAIsBeingMentionedWithNoAssignedTeamAndTeamAdminBAsContactOwnerConversation
        };

        var cannotViewConversation = new List<Conversation>
        {
            teamBConversation,
            teamAdminBWithNoAssignedTeamConversation,
            teamAdminAIsBeingMentionedButNotWithinTwoDaysWithAssignedTeamBndNoContactOwnerConversation,
            teamAdminBIsBeingMentionedWithAssignedTeamBndNoContactOwnerConversation
        };

        using (var context = new ConversationDbContext(_options))
        {
            conversationsToBeTested.AddRange(canViewConversation);
            conversationsToBeTested.AddRange(cannotViewConversation);
            context.Conversations.AddRange(conversationsToBeTested);
            context.SaveChanges();
        }

        var specification = new TeamAdminConversationSpecification(_teamAdminA);

        var specResult =
            specification.IsSatisfiedBy(teamAdminAIsBeingMentionedButNotWithinTwoDaysWithAssignedTeamBndNoContactOwnerConversation);

        // Act
        List<Conversation> result;
        using (
            var context = new ConversationDbContext(_options))
        {
            // Include related entities and apply specification
            result = context.Conversations
                .Where(specification.ToExpression())
                .ToList();
        }

        // Assert.That(result.Select(r => r.Id).ToList(), Is.EquivalentTo(expectedConversationIds), "The expected conversations do not match the actual results.");

        // Assert
        Assert.That(result.Count, Is.EqualTo(16), "Expected 6 conversations to match the specification.");
    }

    private AdditionalAssignee GetCollaborator(string companyId, long assigneeId)
    {
        return new AdditionalAssignee
        {
            CompanyId = companyId, AssigneeId = assigneeId
        };
    }

    private List<ConversationMessage> GetChatHistory(long messageAssigneeId)
    {
        return
        [
            new ()
            {
                Channel = ChannelTypes.Note,
                MessageAssigneeId = messageAssigneeId,
                UpdatedAt = DateTime.UtcNow.AddDays(-2).AddSeconds(1)
            }
        ];
    }
}