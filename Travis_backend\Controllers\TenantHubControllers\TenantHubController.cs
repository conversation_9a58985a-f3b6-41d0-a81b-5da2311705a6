using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Google.Protobuf.WellKnownTypes;
using Hangfire;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Sleekflow.Apis.TenantHub.Api;
using Sleekflow.Apis.TenantHub.Model;
using Travis_backend.AccountAuthenticationDomain.Models;
using Travis_backend.Cache;
using Travis_backend.Cache.Models.CacheKeyPatterns;
using Travis_backend.CompanyDomain.Services;
using Travis_backend.Constants;
using Travis_backend.ConversationServices;
using Travis_backend.Enums;
using Travis_backend.TenantHubDomain.Filters;
using Enum = System.Enum;

namespace Travis_backend.Controllers.TenantHubControllers;

[Authorize]
[Route("TenantHub")]
[TypeFilter(typeof(TenantHubExceptionFilter))]
public class TenantHubController : Controller
{
    private readonly UserManager<ApplicationUser> _userManager;
    private readonly ICoreService _coreService;
    private readonly IManagementRolesApi _managementRolesApi;
    private readonly IManagementCompaniesApi _managementCompaniesApi;
    private readonly IManagementEnabledFeaturesApi _managementEnabledFeaturesApi;
    private readonly IManagementFeaturesApi _managementFeaturesApi;
    private readonly IManagementUsersApi _managementUsersApi;
    private readonly ICacheManagerService _cacheManagerService;

    public TenantHubController(
        UserManager<ApplicationUser> userManager,
        ICoreService coreService,
        IManagementRolesApi managementRolesApi,
        IManagementCompaniesApi managementCompaniesApi,
        IManagementEnabledFeaturesApi managementEnabledFeaturesApi,
        IManagementFeaturesApi managementFeaturesApi,
        IManagementUsersApi managementUsersApi,
        ICacheManagerService cacheManagerService)
    {
        _userManager = userManager;
        _coreService = coreService;
        _managementRolesApi = managementRolesApi;
        _managementCompaniesApi = managementCompaniesApi;
        _managementEnabledFeaturesApi = managementEnabledFeaturesApi;
        _managementFeaturesApi = managementFeaturesApi;
        _managementUsersApi = managementUsersApi;
        _cacheManagerService = cacheManagerService;
    }

    public class IsCompanyCreatedRequest
    {
    }

    [HttpPost]
    [Route("Companies/IsCompanyCreated")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<ManagementIsCompanyCreatedOutputOutput>> IsCompanyCreated(
        [FromBody]
        IsCompanyCreatedRequest isCompanyCreatedRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var managementIsCompanyCreatedOutputOutput =
            await _managementCompaniesApi.ManagementCompaniesIsCompanyCreatedPostAsync(
                managementIsCompanyCreatedInput: new ManagementIsCompanyCreatedInput(staff.CompanyId));

        return Ok(managementIsCompanyCreatedOutputOutput);
    }

    public class DisableFeatureForCompanyRequest
    {
        [JsonProperty("feature_name")]
        public string FeatureName { get; set; }
    }

    [HttpPost]
    [Route("EnabledFeatures/DisableFeatureForCompany")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<ManagementDisableFeatureForCompanyOutputOutput>> DisableFeatureForCompany(
        [FromBody]
        DisableFeatureForCompanyRequest disableFeatureForCompanyRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var managementGetAllFeaturesOutputOutput = await _managementFeaturesApi.ManagementFeaturesGetAllFeaturesPostAsync(body: new object());
        var feature =
            managementGetAllFeaturesOutputOutput.Data.Features.First(f => f.Name == disableFeatureForCompanyRequest.FeatureName);

        var managementDisableFeatureForCompanyOutputOutput =
            await _managementEnabledFeaturesApi.ManagementEnabledFeaturesDisableFeatureForCompanyPostAsync(
                managementDisableFeatureForCompanyInput: new ManagementDisableFeatureForCompanyInput(
                    staff.CompanyId,
                    feature.Id));

        if (disableFeatureForCompanyRequest.FeatureName == TenantHubFeatures.IpWhitelist)
        {
            var ipWhiteListCacheKeyPattern = new IpWhitelistCacheKeyPattern(staff.CompanyId);
            await _cacheManagerService.DeleteCacheAsync(ipWhiteListCacheKeyPattern);
        }

        return Ok(managementDisableFeatureForCompanyOutputOutput);
    }

    public class EnableFeatureForCompanyRequest
    {
        [JsonProperty("feature_name")]
        public string FeatureName { get; set; }
    }

    [HttpPost]
    [Route("EnabledFeatures/EnableFeatureForCompany")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<ManagementEnableFeatureForCompanyOutputOutput>> EnableFeatureForCompany(
        [FromBody]
        EnableFeatureForCompanyRequest enableFeatureForCompanyRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var managementGetAllFeaturesOutputOutput = await _managementFeaturesApi.ManagementFeaturesGetAllFeaturesPostAsync(body: new object());
        var feature =
            managementGetAllFeaturesOutputOutput.Data.Features.First(f => f.Name == enableFeatureForCompanyRequest.FeatureName);

        var managementEnableFeatureForCompanyOutputOutput =
            await _managementEnabledFeaturesApi.ManagementEnabledFeaturesEnableFeatureForCompanyPostAsync(
                managementEnableFeatureForCompanyInput: new ManagementEnableFeatureForCompanyInput(
                    staff.CompanyId,
                    feature.Id));

        if (enableFeatureForCompanyRequest.FeatureName is "2FA")
        {
            var featureEnablements =
                await _managementEnabledFeaturesApi.ManagementEnabledFeaturesGetFeatureEnablementsPostAsync(
                    managementGetFeatureEnablementsInput: new ManagementGetFeatureEnablementsInput(
                        feature.Id,
                        staff.CompanyId));

            IEnumerable<string> featureEnabledRoles = featureEnablements.Data.IsFeatureEnabledForRolesDict
                .Where(x => x.Value == true)
                .Select(x => x.Key);

            foreach (string role in featureEnabledRoles)
            {
                StaffUserRole staffUserRole = (StaffUserRole) Enum.Parse(typeof(StaffUserRole), role, true);
                BackgroundJob.Enqueue<ICompanyService>(x => x.SendMfaEnrollmentEmailsByRole(staffUserRole, staff.CompanyId));
            }
        }

        return Ok(managementEnableFeatureForCompanyOutputOutput);
    }

    public class GetEnabledFeaturesForCompanyRequest
    {
        [JsonProperty("feature_name")]
        public string FeatureName { get; set; }
    }

    [HttpPost]
    [Route("EnabledFeatures/GetEnabledFeaturesForCompany")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<ManagementGetEnabledFeaturesForCompanyOutputOutput>> GetEnabledFeaturesForCompany(
        [FromBody]
        GetEnabledFeaturesForCompanyRequest getEnabledFeaturesForCompanyRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var managementGetEnabledFeaturesForCompanyOutputOutput =
            await _managementEnabledFeaturesApi.ManagementEnabledFeaturesGetEnabledFeaturesForCompanyPostAsync(
                managementGetEnabledFeaturesForCompanyInput: new ManagementGetEnabledFeaturesForCompanyInput(
                    staff.CompanyId));

        return Ok(managementGetEnabledFeaturesForCompanyOutputOutput);
    }

    public class IsFeatureEnabledForCompanyRequest
    {
        [JsonProperty("feature_name")]
        public string FeatureName { get; set; }
    }

    [HttpPost]
    [Route("EnabledFeatures/IsFeatureEnabledForCompany")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<ManagementIsFeatureEnabledForCompanyOutputOutput>> IsFeatureEnabledForCompany(
        [FromBody]
        IsFeatureEnabledForCompanyRequest isFeatureEnabledForCompanyRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var managementGetAllFeaturesOutputOutput = await _managementFeaturesApi.ManagementFeaturesGetAllFeaturesPostAsync(body: new object());
        var feature =
            managementGetAllFeaturesOutputOutput.Data.Features.First(
                f => f.Name == isFeatureEnabledForCompanyRequest.FeatureName);

        var managementIsFeatureEnabledForCompanyOutputOutput =
            await _managementEnabledFeaturesApi.ManagementEnabledFeaturesIsFeatureEnabledForCompanyPostAsync(
                managementIsFeatureEnabledForCompanyInput: new ManagementIsFeatureEnabledForCompanyInput(
                    staff.CompanyId,
                    feature.Id));

        return Ok(managementIsFeatureEnabledForCompanyOutputOutput);
    }

    public class DisableFeatureForRoleRequest
    {
        [JsonProperty("feature_name")]
        public string FeatureName { get; set; }

        [JsonProperty("role_name")]
        public string RoleName { get; set; }
    }

    [HttpPost]
    [Route("EnabledFeatures/DisableFeatureForRole")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<ManagementDisableFeatureForRoleOutputOutput>> DisableFeatureForRole(
        [FromBody]
        DisableFeatureForRoleRequest disableFeatureForRoleRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var managementGetAllFeaturesOutputOutput = await _managementFeaturesApi.ManagementFeaturesGetAllFeaturesPostAsync(body: new object());
        var feature =
            managementGetAllFeaturesOutputOutput.Data.Features.First(f => f.Name == disableFeatureForRoleRequest.FeatureName);

        var managementGetAllRolesOutputOutput = await _managementRolesApi.ManagementRolesGetAllRolesPostAsync(body: new object());
        var role =
            managementGetAllRolesOutputOutput.Data.Roles.First(f => f.Name == disableFeatureForRoleRequest.RoleName);

        var managementDisableFeatureForRoleOutputOutput =
            await _managementEnabledFeaturesApi.ManagementEnabledFeaturesDisableFeatureForRolePostAsync(
                managementDisableFeatureForRoleInput: new ManagementDisableFeatureForRoleInput(
                    staff.CompanyId,
                    role.Id,
                    feature.Id));

        return Ok(managementDisableFeatureForRoleOutputOutput);
    }

    public class EnableFeatureForRoleRequest
    {
        [JsonProperty("feature_name")]
        public string FeatureName { get; set; }

        [JsonProperty("role_name")]
        public string RoleName { get; set; }
    }

    [HttpPost]
    [Route("EnabledFeatures/EnableFeatureForRole")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<ManagementEnableFeatureForRoleOutputOutput>> EnableFeatureForRole(
        [FromBody]
        EnableFeatureForRoleRequest enableFeatureForRoleRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var managementGetAllFeaturesOutputOutput = await _managementFeaturesApi.ManagementFeaturesGetAllFeaturesPostAsync(body: new object());
        var feature =
            managementGetAllFeaturesOutputOutput.Data.Features.First(f => f.Name == enableFeatureForRoleRequest.FeatureName);

        var managementGetAllRolesOutputOutput = await _managementRolesApi.ManagementRolesGetAllRolesPostAsync(body: new object());
        var role =
            managementGetAllRolesOutputOutput.Data.Roles.First(f => f.Name == enableFeatureForRoleRequest.RoleName);

        var managementEnableFeatureForRoleOutputOutput =
            await _managementEnabledFeaturesApi.ManagementEnabledFeaturesEnableFeatureForRolePostAsync(
                managementEnableFeatureForRoleInput: new ManagementEnableFeatureForRoleInput(
                    staff.CompanyId,
                    role.Id,
                    feature.Id));

        if (enableFeatureForRoleRequest.FeatureName is "2FA")
        {
            // Check and send 2FA enrollment email
            var staffUserRole = (StaffUserRole) Enum.Parse(typeof(StaffUserRole), role.Name, true);
            BackgroundJob.Enqueue<ICompanyService>(
                x => x.SendMfaEnrollmentEmailsByRole(staffUserRole, staff.CompanyId));
        }

        return Ok(managementEnableFeatureForRoleOutputOutput);
    }

    public class GetEnabledFeaturesForRoleRequest
    {
        [JsonProperty("feature_name")]
        public string FeatureName { get; set; }

        [JsonProperty("role_name")]
        public string RoleName { get; set; }
    }

    [HttpPost]
    [Route("EnabledFeatures/GetEnabledFeaturesForRole")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<ManagementGetEnabledFeaturesForRoleOutputOutput>> GetEnabledFeaturesForRole(
        [FromBody]
        GetEnabledFeaturesForRoleRequest getEnabledFeaturesForRoleRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var managementGetAllRolesOutputOutput = await _managementRolesApi.ManagementRolesGetAllRolesPostAsync(body: new object());
        var role =
            managementGetAllRolesOutputOutput.Data.Roles.First(f => f.Name == getEnabledFeaturesForRoleRequest.RoleName);

        var managementGetEnabledFeaturesForRoleOutputOutput =
            await _managementEnabledFeaturesApi.ManagementEnabledFeaturesGetEnabledFeaturesForRolePostAsync(
                managementGetEnabledFeaturesForRoleInput: new ManagementGetEnabledFeaturesForRoleInput(
                    staff.CompanyId,
                    role.Id));

        return Ok(managementGetEnabledFeaturesForRoleOutputOutput);
    }

    public class IsFeatureEnabledForRoleRequest
    {
        [JsonProperty("feature_name")]
        public string FeatureName { get; set; }

        [JsonProperty("role_name")]
        public string RoleName { get; set; }
    }

    [HttpPost]
    [Route("EnabledFeatures/IsFeatureEnabledForRole")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<ManagementIsFeatureEnabledForRoleOutputOutput>> IsFeatureEnabledForRole(
        [FromBody]
        IsFeatureEnabledForRoleRequest isFeatureEnabledForRoleRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var managementGetAllFeaturesOutputOutput = await _managementFeaturesApi.ManagementFeaturesGetAllFeaturesPostAsync(body: new object());
        var feature =
            managementGetAllFeaturesOutputOutput.Data.Features.First(f => f.Name == isFeatureEnabledForRoleRequest.FeatureName);

        var managementGetAllRolesOutputOutput = await _managementRolesApi.ManagementRolesGetAllRolesPostAsync(body: new object());
        var role =
            managementGetAllRolesOutputOutput.Data.Roles.First(f => f.Name == isFeatureEnabledForRoleRequest.RoleName);

        var managementIsFeatureEnabledForRoleOutputOutput =
            await _managementEnabledFeaturesApi.ManagementEnabledFeaturesIsFeatureEnabledForRolePostAsync(
                managementIsFeatureEnabledForRoleInput: new ManagementIsFeatureEnabledForRoleInput(
                    staff.CompanyId,
                    role.Id,
                    feature.Id));

        return Ok(managementIsFeatureEnabledForRoleOutputOutput);
    }

    public class IsFeatureEnabledForUserRequest
    {
        [JsonProperty("feature_name")]
        public string FeatureName { get; set; }
    }

    [HttpPost]
    [Route("EnabledFeatures/IsFeatureEnabledForUser")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult> IsFeatureEnabledForUser(
        [FromBody]
        IsFeatureEnabledForUserRequest isFeatureEnabledForUserRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var managementGetAllFeaturesOutputOutput = await _managementFeaturesApi.ManagementFeaturesGetAllFeaturesPostAsync(body: new object());
        var feature =
            managementGetAllFeaturesOutputOutput.Data.Features.First(f => f.Name == isFeatureEnabledForUserRequest.FeatureName);

        var managementIsFeatureEnabledForUserOutputOutput =
            await _managementEnabledFeaturesApi.ManagementEnabledFeaturesIsFeatureEnabledForUserPostAsync(
                managementIsFeatureEnabledForUserInput: new ManagementIsFeatureEnabledForUserInput(
                    staff.IdentityId,
                    feature.Id,
                    staff.CompanyId));

        return Ok(managementIsFeatureEnabledForUserOutputOutput);
    }

    public class GetFeatureEnablementsRequest
    {
        [JsonProperty("feature_name")]
        public string FeatureName { get; set; }
    }

    [HttpPost]
    [Route("EnabledFeatures/GetFeatureEnablements")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<ManagementGetFeatureEnablementsOutputOutput>> GetFeatureEnablements(
        [FromBody]
        GetFeatureEnablementsRequest getFeatureEnablementsRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var managementGetAllFeaturesOutputOutput = await _managementFeaturesApi.ManagementFeaturesGetAllFeaturesPostAsync(body: new object());
        var feature =
            managementGetAllFeaturesOutputOutput.Data.Features.First(f => f.Name == getFeatureEnablementsRequest.FeatureName);

        var managementGetFeatureEnablementsOutputOutput =
            await _managementEnabledFeaturesApi.ManagementEnabledFeaturesGetFeatureEnablementsPostAsync(
                managementGetFeatureEnablementsInput: new ManagementGetFeatureEnablementsInput(
                    feature.Id,
                    staff.CompanyId));

        return Ok(managementGetFeatureEnablementsOutputOutput);
    }

    public class CreateFeatureRequest
    {
        [JsonProperty("name")]
        public string Name { get; set; }

        [JsonProperty("description")]
        public string Description { get; set; }

        [JsonProperty("categories")]
        public List<string> Categories { get; set; }

        [JsonProperty("is_enabled")]
        public bool IsEnabled { get; set; }
    }

    [HttpPost]
    [Route("Features/CreateFeature")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<ManagementCreateFeatureOutputOutput>> CreateFeature(
        [FromBody]
        CreateFeatureRequest createFeatureRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var managementCreateFeatureOutputOutput =
            await _managementFeaturesApi.ManagementFeaturesCreateFeaturePostAsync(
                managementCreateFeatureInput: new ManagementCreateFeatureInput(
                    createFeatureRequest.Name,
                    createFeatureRequest.Description,
                    createFeatureRequest.Categories,
                    createFeatureRequest.IsEnabled));

        return Ok(managementCreateFeatureOutputOutput);
    }

    public class GetAllFeaturesRequest
    {
    }

    [HttpPost]
    [Route("Features/GetAllFeatures")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GetAllFeaturesOutputOutput>> GetAllFeatures(
        [FromBody]
        GetAllFeaturesRequest getAllFeaturesRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var managementGetAllFeaturesOutputOutput =
            await _managementFeaturesApi.ManagementFeaturesGetAllFeaturesPostAsync(
                body: new object());

        return Ok(managementGetAllFeaturesOutputOutput);
    }

    public class GetFeatureRequest
    {
        [JsonProperty("feature_name")]
        public string FeatureName { get; set; }
    }

    [HttpPost]
    [Route("Features/GetFeature")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<ManagementGetFeatureEnablementsOutputOutput>> GetFeature(
        [FromBody]
        GetFeatureRequest getFeatureRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var managementGetAllFeaturesOutputOutput = await _managementFeaturesApi.ManagementFeaturesGetAllFeaturesPostAsync(body: new object());
        var feature =
            managementGetAllFeaturesOutputOutput.Data.Features.First(f => f.Name == getFeatureRequest.FeatureName);

        var managementGetFeatureOutputOutput =
            await _managementFeaturesApi.ManagementFeaturesGetFeaturePostAsync(
                managementGetFeatureInput: new ManagementGetFeatureInput(feature.Id));

        return Ok(managementGetFeatureOutputOutput);
    }

    public class GetAllRolesRequest
    {
        [JsonProperty("feature_name")]
        public string FeatureName { get; set; }
    }

    [HttpPost]
    [Route("Users/GetAllRoles")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<ManagementGetAllRolesOutputOutput>> GetAllRoles(
        [FromBody]
        GetAllRolesRequest getAllRolesRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var managementGetAllRolesOutputOutput =
            await _managementRolesApi.ManagementRolesGetAllRolesPostAsync(
                body: new object());

        return Ok(managementGetAllRolesOutputOutput);
    }

    public class IsUserCreatedRequest
    {
        [JsonProperty("feature_name")]
        public string FeatureName { get; set; }
    }

    [HttpPost]
    [Route("Users/IsUserCreated")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<ManagementIsUserCreatedOutputOutput>> IsUserCreated(
        [FromBody]
        IsUserCreatedRequest isUserCreatedRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var managementIsUserCreatedOutputOutput =
            await _managementUsersApi.ManagementUsersIsUserCreatedPostAsync(
                managementIsUserCreatedInput: new ManagementIsUserCreatedInput(staff.CompanyId));

        return Ok(managementIsUserCreatedOutputOutput);
    }
}