using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using Travis_backend.ContactDomain.Models;
using Travis_backend.Enums;
using static Travis_backend.Constants.OrderCondition;

namespace Travis_backend.Helpers;

using static SupportedOperator;

public static class ConditionHelper
{
    public static string ConditionClause(
        SupportedOperator condition,
        string field,
        string parameterPrefix = default,
        string rangeFrom = default,
        string rangeTo = default,
        List<string> includeParameter = default,
        bool needTypeConversion = true)
    {
        return condition switch
        {
            // Contains => $"LOWER({field}) LIKE @{parameterPrefix} ",
            // IsNotContains => $"LOWER({field}) NOT LIKE @{parameterPrefix} ",
            Contains => $"{field} LIKE @{parameterPrefix} ",
            IsNotContains => $"{field} NOT LIKE @{parameterPrefix} ",
            IsNull => $"{field} IS NULL ",
            IsNotNull => $"{field} IS NOT NULL ",
            HigherThan => $"{field} > @{parameterPrefix} ",
            LessThan => $"{field} < @{parameterPrefix} ",
            IsBetween => $"{field} > @{rangeFrom} AND {field} < @{rangeTo} ",
            IsNotBetween => $"({field} < @{rangeFrom} OR {field} > @{rangeTo}) ",

            // Supported Conditions
            // Included => $"LOWER({field}) IN ({string.Join(",", includeParameter.Select(i => $"@{i}"))}) ",
            IncludeSubQuery => $"LOWER({field}) IN {parameterPrefix} ",
            Included => $"{field} IN ({string.Join(",", includeParameter.Select(i => $"@{i}"))}) ",

            // NotIncluded => $"LOWER({field}) NOT IN ({string.Join(",", includeParameter.Select(i => $"@{i}"))}) ",
            NotIncluded => $"{field} NOT IN ({string.Join(",", includeParameter.Select(i => $"@{i}"))}) ",
            Equal => $"{field} = @{parameterPrefix} ",
            SupportedOperator.Equals => $"{field} = @{parameterPrefix} ",
            NotEqual => $"{field} != @{parameterPrefix} ",
            GroupBy => $"GROUP BY {field} ",
            IsNotNullOrEmpty => $"{field} IS NOT NULL AND {field} != '' ",
            StringIsNullOrEmpty => $"({field} IS NULL OR {field} = '' COLLATE latin1_general_100_ci_as) ",
            StringIsNotNullOrEmpty => $"{field} != '' COLLATE latin1_general_100_ci_as ",
            DateTimeBefore => $"CONVERT(VARCHAR(19), {field}, 120) < @{parameterPrefix} ",
            DateTimeAfter => $"CONVERT(VARCHAR(19), {field}, 120) > @{parameterPrefix} ",
            TimeBefore => needTypeConversion
                ? $"CONVERT(VARCHAR(5), CONVERT(DATETIME, LEFT({field}, 19)) AT TIME ZONE 'UTC' AT TIME ZONE @{parameterPrefix}, 108) " +
                  $"BETWEEN '00:00' AND @{rangeTo} "
                : $"CONVERT(VARCHAR(5), {field} AT TIME ZONE 'UTC' AT TIME ZONE @{parameterPrefix}, 108) " +
                  $"BETWEEN '00:00' AND @{rangeTo} ",
            TimeAfter => needTypeConversion
                ? $"CONVERT(VARCHAR(5), CONVERT(DATETIME, LEFT({field}, 19)) AT TIME ZONE 'UTC' AT TIME ZONE @{parameterPrefix}, 108) " +
                  $"BETWEEN @{rangeTo} AND '23:59' "
                : $"CONVERT(VARCHAR(5), {field} AT TIME ZONE 'UTC' AT TIME ZONE @{parameterPrefix}, 108) " +
                  $"BETWEEN @{rangeTo} AND '23:59' ",
            TimeBetween => needTypeConversion
                ? $"CONVERT(VARCHAR(5), CONVERT(DATETIME, LEFT({field}, 19)) AT TIME ZONE 'UTC' AT TIME ZONE @{parameterPrefix}, 108) " +
                  $"BETWEEN @{rangeFrom} AND @{rangeTo} "
                : $"CONVERT(VARCHAR(5), {field} AT TIME ZONE 'UTC' AT TIME ZONE @{parameterPrefix}, 108) " +
                  $"BETWEEN @{rangeFrom} AND @{rangeTo} ",
            TimeNotBetween => needTypeConversion
                ? $"CONVERT(VARCHAR(5), CONVERT(DATETIME, LEFT({field}, 19)) AT TIME ZONE 'UTC' AT TIME ZONE @{parameterPrefix}, 108) " +
                  $"NOT BETWEEN @{rangeFrom} AND @{rangeTo} "
                : $"CONVERT(VARCHAR(5), {field} AT TIME ZONE 'UTC' AT TIME ZONE @{parameterPrefix}, 108) " +
                  $"NOT BETWEEN @{rangeFrom} AND @{rangeTo} ",
            DateTimeBetween =>
                $"CONVERT(VARCHAR(19), {field}, 120) >= @{rangeFrom} AND CONVERT(VARCHAR(19), {field}, 120) < @{rangeTo} ",
            DayOfWeek =>

                // Only support standard time format: 2020-12-13T11:30:41.0000000Z
                needTypeConversion
                    ? $"DATEPART(WEEKDAY, CONVERT(DATETIME, LEFT({field}, 19)) AT TIME ZONE 'UTC' AT TIME ZONE @{parameterPrefix}) " +
                      $"IN ({string.Join(",", includeParameter.Select(i => $"@{i}"))}) "
                    : $"DATEPART(WEEKDAY, {field} AT TIME ZONE 'UTC' AT TIME ZONE @{parameterPrefix}) " +
                      $"IN ({string.Join(",", includeParameter.Select(i => $"@{i}"))}) ",
            // NumberHigherThan/NumberLessThan only use in Custom Field cuz the colum is varchar type
            NumberHigherThan => $"CAST(REPLACE({field}, ',', '') AS FLOAT) > @{parameterPrefix} ",
            NumberLessThan => $"CAST(REPLACE({field}, ',', '') AS FLOAT) < @{parameterPrefix} ",
            LikeOrMultipleValues => $"({string.Join(" OR ", includeParameter.Select(i => $"{field} LIKE @{i}"))}) ",
            IsNotLikeOrMultipleValues => $"({string.Join(" OR ", includeParameter.Select(i => $"{field} NOT LIKE @{i}"))}) ",
            _ => throw new Exception()
        };
    }

    public static Dictionary<string, string> ConcatConditionParameters(
        Dictionary<string, string> original,
        Dictionary<string, string> joining)
    {
        return original
            .Concat(joining)
            .ToLookup(x => x.Key, x => x.Value)
            .ToDictionary(x => x.Key, g => g.First());
    }

    // ExtractingMultipleParameters
    public static (List<string> ParameterPrefix, Dictionary<string, string> ConditionParameters)
        ConstructingIncludes<T>(string prefix, List<T> values)
    {
        var parameterPrefix = new List<string>();
        var conditionParameters = new Dictionary<string, string>();
        for (var i = 0; i < values.Count; i++)
        {
            var currentPrefix = $"{prefix}_{i}";
            parameterPrefix.Add(currentPrefix);
            conditionParameters.Add(currentPrefix, values[i].ToString());
        }

        return (parameterPrefix, conditionParameters);
    }

    public static Expression<Func<UserProfile, object>> GetOrderedByExpression(string sortBy)
    {
        switch (sortBy)
        {
            case UpdatedAt:
                return up => up.UpdatedAt;
            case CreatedAt:
                return up => up.CreatedAt;
            case FirstName:
                return up => up.FirstName;
            case LastName:
                return up => up.LastName;
            case LastContact:
                return up => up.LastContact;
            case LastContactFromCustomers:
                return up => up.LastContactFromCustomers;
            default:
                throw new ArgumentException("Invalid sortBy value", nameof(sortBy));
        }

        ;
    }
}