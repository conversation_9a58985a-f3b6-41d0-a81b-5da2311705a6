﻿using System.Globalization;
using System.Reflection;
using Travis_backend.Controllers.SleekflowControllers;

namespace Sleekflow.Core.Tests.IntelligentHubs;

public class IntelligentHubUsageTests
{
    [Test]
    // normal cases
    [TestCase("2000-01-10T00:00", "2000-05-10T00:00", "2000-03-05T10:00", "2000-02-10T00:00", "2000-03-10T00:00")]
    [TestCase("2000-01-10T00:00", "2000-05-10T00:00", "2000-03-11T10:00", "2000-03-10T00:00", "2000-04-10T00:00")]
    // across year
    [TestCase("2000-01-10T00:00", "2001-05-10T00:00", "2001-01-05T10:00", "2000-12-10T00:00", "2001-01-10T00:00")]
    [TestCase("2000-01-10T00:00", "2001-05-10T00:00", "2001-01-11T10:00", "2001-01-10T00:00", "2001-02-10T00:00")]
    // start day is the last day of the month
    [TestCase("2000-01-31T00:00", "2000-06-30T00:00", "2000-03-05T10:00", "2000-02-29T00:00", "2000-03-31T00:00")]
    [TestCase("2000-01-31T00:00", "2000-06-30T00:00", "2000-01-31T10:00", "2000-01-31T00:00", "2000-02-29T00:00")]
    [TestCase("2000-01-31T00:00", "2001-06-30T00:00", "2000-12-20T10:00", "2000-11-30T00:00", "2000-12-31T00:00")]
    [TestCase("2000-01-31T00:00", "2001-06-30T00:00", "2001-01-20T10:00", "2000-12-31T00:00", "2001-01-31T00:00")]
    [TestCase("2000-01-31T00:00", "2001-06-30T00:00", "2001-02-28T10:00", "2001-02-28T00:00", "2001-03-31T00:00")]
    public void GetCurrentUsageCycle_Test(
        string periodStart,
        string periodEnd,
        string now,
        string expectedFrom,
        string expectedTo)
    {
        var method = typeof(IntelligentHubController).GetMethod(nameof(IntelligentHubController.GetCurrentUsageCycle), BindingFlags.NonPublic | BindingFlags.Static);

        var result = method!.Invoke(
            null,
            new object[]
        {
            ParseDate(periodStart),
            ParseDate(periodEnd),
            ParseDate(now)
        })!;

        var (actualFrom, actualTo) = ((DateTimeOffset, DateTimeOffset)) result;

        Assert.That(actualFrom, Is.EqualTo(ParseDate(expectedFrom)));
        Assert.That(actualTo, Is.EqualTo(ParseDate(expectedTo)));
    }

    private static DateTimeOffset ParseDate(string dateString)
    {
        return DateTimeOffset.ParseExact(dateString, "yyyy-MM-ddTHH:mm", CultureInfo.InvariantCulture);
    }
}