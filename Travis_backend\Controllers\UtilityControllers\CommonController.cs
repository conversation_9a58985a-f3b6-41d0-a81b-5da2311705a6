using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Travis_backend.AccountAuthenticationDomain.Models;
using Travis_backend.Cache;
using Travis_backend.CommonDomain.ViewModels;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.ConversationServices;
using Travis_backend.Database;
using Travis_backend.Services;

namespace Travis_backend.Controllers.UtilityControllers;

[Authorize]
public class CommonController : Controller
{
    private readonly ICoreService _coreService;
    private readonly ICacheManagerService _cacheManagerService;
    private readonly IIpLocationService _iipLocationService;
    private readonly ILogger<CommonController> _logger;
    private readonly UserManager<ApplicationUser> _userManager;
    private readonly ApplicationDbContext _appDbContext;

    public CommonController(
        ICoreService coreService,
        ICacheManagerService cacheManagerService,
        IIpLocationService iipLocationService,
        ILogger<CommonController> logger,
        UserManager<ApplicationUser> userManager,
        ApplicationDbContext appDbContext)
    {
        _coreService = coreService;
        _cacheManagerService = cacheManagerService;
        _iipLocationService = iipLocationService;
        _logger = logger;
        _userManager = userManager;
        _appDbContext = appDbContext;
    }

    [HttpGet("country")]
    public async Task<ActionResult<List<CustomUserProfileFieldOption>>> GetCountryList()
    {
        return Ok(await _coreService.GetCountryList());
    }

    [AllowAnonymous]
    [HttpGet("ipaddress")]
    public ActionResult<IpAddressResponse> GetIpAddress()
    {
        var ipAddress = GetIpaddress();

        return Ok(
            new IpAddressResponse()
            {
                IpAddress = ipAddress
            });
    }

    [AllowAnonymous]
    [HttpGet("timezone")]
    public async Task<ActionResult<MyTimeZoneInfo>> GetTimeZone()
    {
        var ipAddress = GetIpaddress();

        if (!string.IsNullOrEmpty(ipAddress))
        {
            return Ok(await _iipLocationService.GetTimeZoneByIpaddressAsync(ipAddress));
        }

        _logger.LogError("Can not obtain ip address from request.");
        throw new Exception("Can not obtain ip address from request.");
    }

    [AllowAnonymous]
    [HttpGet("location-info")]
    public async Task<ActionResult> GetLocationInfo()
    {
        var ipAddress = GetIpaddress();

        if (!string.IsNullOrEmpty(ipAddress))
        {
            return Ok(await _iipLocationService.GetLocationInfoByIpaddressAsync(ipAddress));
        }

        _logger.LogError("Can not obtain ip address from request.");
        throw new Exception("Can not obtain ip address from request.");
    }

    [HttpGet("webapp/version/{id}")]
    public async Task<ActionResult<WebAppVersion>> GetWebAppVersion(string id)
    {

        var staff = await _appDbContext.UserRoleStaffs
            .Include(staff => staff.Identity)
            .FirstOrDefaultAsync(u => u.IdentityId == id);
        if (staff is null)
        {
            return Ok(
                new WebAppVersion()
                {
                    Version = "v1"
                });
        }

        var key = $"webapp_version_{id}";
        var result = staff.Identity.DefaultWebAppVersion ?? await _cacheManagerService.GetCacheWithConstantKeyAsync(key);

        // Aureus
        if (staff.CompanyId == "a99a72ca-202b-4080-990d-8b140c604311")
        {
            return Ok(
                result == null
                    ? new WebAppVersion()
                    {
                        Version = "v1"
                    }
                    : new WebAppVersion()
                    {
                        Version = result
                    });
        }

        return Ok(
            result == null
                ? new WebAppVersion()
                {
                    Version = "v2"
                }
                : new WebAppVersion()
                {
                    Version = result
                });
    }

    [HttpPost("webapp/version/{id}")]
    public async Task<ActionResult<WebAppVersion>> GetWebAppVersion(string id, [FromBody] WebAppVersion webAppVersion)
    {
        // We are not need to check Redsis cache here, because we are moved it to db.
        var user = await _userManager.FindByIdAsync(id);
        if (user is null)
        {
            return BadRequest(
                new ResponseViewModel()
                {
                    message = "User not found."
                });
        }

        user.DefaultWebAppVersion = webAppVersion.Version;
        var result = await _userManager.UpdateAsync(user);
        if (!result.Succeeded)
        {
            _logger.LogError(
                "Update default webapp version of user id: {Id} Failed. {SerializeObject}",
                id, JsonConvert.SerializeObject(result.Errors));
            throw new Exception(result.Errors?.FirstOrDefault()?.Description);
        }

        return Ok(
            new WebAppVersion()
            {
                Version = webAppVersion.Version
            });
    }

    private static List<string> ExtractValues(string? values)
    {
        return string.IsNullOrEmpty(values)
            ? new List<string>()
            : values.Replace("\"", string.Empty).Split(',').Where(v => !string.IsNullOrEmpty(v)).ToList();
    }

    private string GetIpaddress()
    {
        var ipAddress = HttpContext?.Connection?.RemoteIpAddress?.ToString();
        var via = HttpContext.Request.Headers.ContainsKey("Via");
        if (via && HttpContext.Request.Headers["Via"].ToString().ToLower().Contains("azure"))
        {
            ipAddress = ExtractValues(HttpContext.Request.Headers["X-Forwarded-For"].ToString()).First();
        }

        return ipAddress;
    }
}