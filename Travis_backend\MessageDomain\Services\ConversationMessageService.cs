﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.IO.Compression;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using AutoMapper;
using AutoMapper.QueryableExtensions;
using Force.DeepCloner;
using GraphApi.Client.Const.WhatsappCloudApi;
using Hangfire;
using isRock.LineBot;
using LinqKit;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Http.Extensions;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using NReco.VideoConverter;
using RestSharp;
using SendGrid;
using SendGrid.Helpers.Mail;
using SixLabors.ImageSharp;
using SixLabors.ImageSharp.Formats.Jpeg;
using SixLabors.ImageSharp.Formats.Png;
using Sleekflow.Apis.AuditHub.Model;
using Sleekflow.Apis.FlowHub.Model;
using Sleekflow.Apis.MessagingHub.Api;
using Sleekflow.Apis.MessagingHub.Client;
using Sleekflow.Apis.MessagingHub.Model;
using Telegram.Bot;
using Travis_backend.AutomationDomain.Models;
using Travis_backend.Cache;
using Travis_backend.ChannelDomain.Clients;
using Travis_backend.ChannelDomain.Models;
using Travis_backend.ChannelDomain.ViewModels;
using Travis_backend.ClientCustomDomain.Services;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.CompanyDomain.Services;
using Travis_backend.CompanyDomain.ViewModels;
using Travis_backend.Constants;
using Travis_backend.ContactDomain.Models;
using Travis_backend.ContactDomain.Services;
using Travis_backend.ContactDomain.ViewModels;
using Travis_backend.ConversationDomain.ConversationResolver;
using Travis_backend.ConversationDomain.Models;
using Travis_backend.ConversationDomain.Services;
using Travis_backend.ConversationDomain.ViewModels;
using Travis_backend.Database;
using Travis_backend.Enums;
using Travis_backend.Extensions;
using Travis_backend.ConversationServices.Models;
using Travis_backend.ConversationServices.ViewModels;
using Travis_backend.FileDomain.Models;
using Travis_backend.FileDomain.Services;
using Travis_backend.FileDomain.ViewModels;
using Travis_backend.FlowHubs;
using Travis_backend.FlowHubs.Models;
using Travis_backend.Helpers;
using Travis_backend.MessageDomain.ChannelMessageProvider;
using Travis_backend.MessageDomain.Models;
using Travis_backend.MessageDomain.ViewModels;
using Travis_backend.SignalR;
using Travis_backend.StripeIntegrationDomain.Services;
using Twilio;
using Twilio.Rest.Api.V2010.Account;
using Twilio.Types;
using WABA360Dialog;
using WABA360Dialog.ApiClient.Exceptions;
using WABA360Dialog.ApiClient.Payloads.Converters;
using WABA360Dialog.ApiClient.Payloads.Enums;
using WABA360Dialog.ApiClient.Payloads.Models.MessageObjects;
using WABA360Dialog.ApiClient.Payloads.Models.MessageObjects.InteractiveObjects;
using static Travis_backend.ContactDomain.Services.UserProfileService;
using File = System.IO.File;
using Message = Telegram.Bot.Types.Message;
using MessageStatus = Travis_backend.MessageDomain.Models.MessageStatus;
using SendMessageResponse = Travis_backend.ConversationServices.ViewModels.SendMessageResponse;
using GraphApi.Client.ApiClients;
using Microsoft.AspNetCore.StaticFiles;
using Microsoft.Data.SqlClient;
using Polly;
using Sleekflow.Apis.PublicApiGateway.Model;
using Sleekflow.Apis.TicketingHub.Api;
using Sleekflow.Apis.TicketingHub.Model;
using SixLabors.ImageSharp.Formats;
using Travis_backend.BroadcastDomain.Exceptions;
using Travis_backend.Cache.Models.CacheKeyPatterns;
using Travis_backend.ConversationDomain;
using Travis_backend.ConversationDomain.ConversationAccessControl;
using Travis_backend.Database.Services;
using Travis_backend.MessageDomain.Services;
using Travis_backend.OpenTelemetry.Meters;
using Travis_backend.OpenTelemetry.Meters.Constants;
using Travis_backend.PiiMaskingDomain.Services;
using Travis_backend.Telemetries;
using Travis_backend.Telemetries.Constants;
using WhatsappCloudApiParameterObject = Sleekflow.Apis.MessagingHub.Model.WhatsappCloudApiParameterObject;
using WhatsappCloudApiTemplateMessageComponentObject = Sleekflow.Apis.MessagingHub.Model.WhatsappCloudApiTemplateMessageComponentObject;

namespace Travis_backend.ConversationServices
{
    public interface IConversationMessageService
    {
        Task FetchConversationsBackground(string pageId, int limit = 10, bool getAll = false);

        Task FetchConversationsIGBackground(string pageId, int limit = 10, bool getAll = false);

        Task<IList<ConversationMessage>> SendMessage(
            Conversation conversation,
            ConversationMessage conversationMessage,
            bool triggerActivity = true,
            bool EnableChatbot = false,
            bool EnableTranslation = false);

        Task<IList<ConversationMessage>> SendFileMessageByFBURL(
            Conversation conversation,
            ConversationMessage conversationMessage,
            List<FileURLMessage> fileURLMessages,
            bool triggerActivity = true,
            bool EnableChatbot = false,
            bool EnableTranslation = false);

        Task<IList<ConversationMessage>> SendFileMessage(
            Conversation conversation,
            ConversationMessage conversationMessage,
            ConversationMessageViewModel fileMessageViewModel,
            bool triggerActivity = true,
            bool EnableChatbot = false,
            bool EnableTranslation = false);

        Task<IList<ConversationMessage>> FetchInstagramMessage(Entry entry, FBMessaging messaging);

        Task<IList<ConversationMessage>> FetchFacebookMessage(Entry entry, FBMessaging messaging);

        Task FetchByThreadId(Change change, bool trigger = true);

        Task FetchByMessageId(
            string pageId,
            string messageId,
            bool trigger = true,
            int attempts = 3,
            Conversation conversation = null);

        Task<IList<ConversationMessage>> FetchFacebookPostback(Entry entry, FBMessaging messaging);

        Task IGFetchByThreadId(Change change, bool trigger = true);

        Task IGFetchByMessageId(string pageId, string messageId, bool trigger = true, int attempts = 3);

        // Task<IList<ConversationMessage>> SendConversationTicket(Conversation conversation, ConversationTicket conversationTicket);
        Task<Conversation> ChangeConversationAssignedTeam(
            Conversation conversation,
            CompanyTeam companyTeam,
            bool isTriggerUpdate = true,
            string changedBy = null);

        Task<Conversation> ChangeConversationAssignee(
            Conversation conversation,
            Staff assignee,
            bool isTriggerUpdate = true,
            string changedBy = null);

        Task<Conversation> ChangeConversationStatus(
            string conversationId,
            string staffId,
            StatusViewModel statusViewModel,
            bool setUnread = false);

        // Task<IList<ConversationMessage>> SendWelcomeMessage(Conversation conversation, ConversationMessage conversationMessage);
        // Task<IList<ConversationMessage>> SendAutoReplyMessage(Conversation conversation, ConversationMessage conversationMessage);
        Task<List<string>> FormatParams(UserProfile userProfile, List<string> template_params);

        Task<List<string>> FormatParamsWithPaymentUrl(
            UserProfile userProfile,
            List<string> template_params,
            string paymentUrl = null);

        Task<string> SendAutomatedMessage(Conversation conversation, AutomationAction automationRule);

        Task<ConversationMessage> SendConversationNote(
            string companyId,
            string conversationId,
            string staffId,
            ConversationMessage conversationMessage,
            ConversationNoteViewModel conversationNoteViewModel);

        // Task<Conversation> SetConversationHashtag(string compddnyId, string conversationId, long? staffId, List<ConversationHashtagViewModel> conversationTagViewModels);
        // Task<Conversation> AddConversationHashtag(string companyId, string conversationId, long? staffId, List<ConversationHashtagViewModel> conversationTagViewModels);
        // Task<Conversation> RemoveConversationHashtag(string companyId, string conversationId, long? staffId, List<ConversationHashtagViewModel> conversationTagViewModels);
        // Task<ConversationRemark> AddActivityLogs(string companyId, string conversationId, string staffId, List<ReamrksViewModel> conversationReamrksViewModel);
        Task RemoveScheduledMessage(string companyId, DeleteMessageInput input);

        Task DeleteMessage(string companyId, DeleteMessageInput input);

        Task<ConversationWithCountViewModel> GetFormattedConversations(
            Staff companyUser,
            string assignedTo,
            int offset,
            int limit,
            string status,
            string channels,
            DateTime? afterUpdatedAt,
            DateTime? afterModifiedAt,
            string channelIds,
            string tags,
            long? teamId,
            bool? isTeamUnassigned,
            bool? isUnread,
            string orderBy,
            string version = "1",
            bool? isAssigned = null,
            bool? isCollaborator = null);

        Task<IQueryable<Conversation>> GetConversations(
            string companyId,
            Staff staff,
            string status = "all",
            string assignedTo = "all",
            string channels = null,
            DateTime? afterUpdatedAt = null,
            DateTime? afterModifiedAt = null,
            string channelIds = null,
            string tags = null,
            long? teamId = null,
            bool? isTeamUnassigned = null,
            bool? isUnread = null,
            string orderBy = null,
            string version = "1",
            bool? isAssigned = null,
            bool? isCollaborator = null);

        IOrderedQueryable<ConversationMessage> GetConversationMessagesHistory(
            string companyId,
            string staffId = null,
            string status = null,
            string channels = null,
            DateTime? messageFrom = null,
            DateTime? messageTo = null,
            string channelIds = null,
            string tags = null,
            long? teamId = null,
            bool? isTeamUnassigned = null);

        IQueryable<ConversationMessage> GetConversationMessage(
            string conversationId,
            string companyId,
            long? beforeMessageId = null,
            long? afterMessageId = null,
            long? beforeTimestamp = null,
            long? afterTimestamp = null,
            string channels = null,
            string channelIds = null,
            bool? IsFromUser = null);

        Task SendReadMoreMessage(
            string companyId,
            string conversationId,
            long? whatsappSenderId,
            string messageContent,
            string contentSid = null,
            List<UploadedFile> uploadedFiles = null,
            DeliveryType deliveryType = DeliveryType.ReadMore);

        Task SendWhatsapp360DialogOptInButtonMessage(
            string companyId,
            string conversationId,
            long whatsapp360dialogConfigId);

        Task SendWhatsapp360DialogReadMoreMessage(
            string companyId,
            string conversationId,
            long whatsapp360dialogConfigId,
            long conversationMessageId);

        Task SendWhatsappCloudApiOptInButtonMessage(
            string companyId,
            string conversationId,
            long whatsappCloudApiConfigId,
            long conversationMessageId);

        Task SendWhatsappCloudApiReadMoreMessage(
            string companyId,
            string conversationId,
            long whatsappCloudApiConfigId,
            long conversationMessageId);

        Task FbSaveOtnNotifyMeMessage(string pageId, FBMessaging message);

        Task ReadConversation(string conversationId, Staff companyUser, bool isForEveryone = false);

        Task ReadConversationPersonally(string conversationId, Staff companyUser);

        Task<ConversationStatusResponseViewModel> MarkConversationAsUnread(string conversationId, Staff companyUser);

        Task<bool> SendOptInMessageTwilio(ConversationMessage message);

        Task AddFbAdClickToMessenger(Entry entry, FBMessaging messaging);

        Task<ConversationWithCountViewModel> SearchConversationMessagesAsync(
            Staff companyUser,
            string assignedTo,
            string keywords,
            int offset,
            int limit,
            string status,
            string channels,
            string channelIds,
            long? teamId,
            string behaviourVersion = "1",
            bool isAllowCache = true);

        Task<List<ConversationMessage>> GetConversationLastMessagesAsync(
            string sleekflowCompanyId,
            string userProfileId,
            int offSet,
            int limit);

        Task<ConversationMessage?> GetOptInReplyTargetUndeliveredMessage(
            ConversationMessage receivedMessage);

        T ExtractValueFromConversationMessageMetadata<T>(ConversationMessage conversationMessage, string key);

        Task<ConversationMessage> SendConversationIndicatorMessage(
            Conversation conversation,
            ConversationMessage conversationMessage);

        Task AddConversationMessageCache<T>(
            string conversationId,
            ConversationMessageCacheKeyPattern cacheKeyPattern,
            T data);

        Task AddConversationMessageV2Cache<T>(
            string companyId,
            ConversationMessageV2CacheKeyPattern cacheKeyPattern,
            T data);

        Task AddGetConversationMessagesCache<T>(
            string conversationId,
            GetConversationMessagesCacheKeyPattern cacheKeyPattern,
            T data);

        Task RemoveConversationMessageV2Cache(string companyId);

        Task RemoveConversationMessageCache(string conversationId);

        Task RemoveGetConversationMessagesCache(string conversationId);

        Task RemoveCache(string companyId, string conversationId);
    }


    public class ConversationMessageService : IConversationMessageService
    {
        private readonly ApplicationDbContext _appDbContext;
        private readonly IMapper _mapper;
        private readonly IConfiguration _configuration;
        private readonly ILogger<ConversationMessageService> _logger;
        private readonly ISignalRService _signalRService;
        private readonly IUserProfileService _userProfileService;
        private readonly IUploadService _uploadService;
        private readonly IAzureBlobStorageService _azureBlobStorageService;
        private readonly IMediaEncodingService _mediaEncodingService;
        private readonly IEmailNotificationService _emailNotificationService;
        private readonly ICompanyService _companyService;
        private readonly ICompanyUsageService _companyUsageService;
        private readonly IMediaProcessService _mediaProcessService;
        private readonly ICacheManagerService _cacheManagerService;
        private readonly ILockService _lockService;
        private readonly IConversationAssigneeService _conversationAssigneeService;
        private readonly ISleekPayService _sleekPayService;
        private readonly IConversationService _conversationService;
        private readonly IUserProfileHooks _userProfileHooks;
        private readonly IZDotComService _zDotComService;
        private readonly IHttpClientFactory _httpClientFactory;
        private readonly IConversationResolver _conversationResolver;
        private readonly IChannelMessageProvider _channelMessageProvider;
        private readonly IMessageChannelSwitcher _messageChannelSwitcher;
        private readonly IDbContextService _dbContextService;
        private readonly IFlowHubService _flowHubService;
        private readonly IPiiMaskingService _piiMaskingService;
        private readonly ITicketingHubMessageService _ticketingHubMessageService;
        private readonly IConversationMeters _conversationMeters;
        private readonly bool _isEnableTicketingLogic;
        private readonly IMeasureQueryTimeService _measureQueryTimeService;
        private readonly IAccessControlAggregationService _accessControlAggregationService;
        private readonly IApplicationInsightsTelemetryTracer _applicationInsightsTelemetryTracer;


        public ConversationMessageService(
            ApplicationDbContext appDbContext,
            IMapper mapper,
            IConfiguration configuration,
            ILogger<ConversationMessageService> logger,
            ISignalRService signalRService,
            IUserProfileService userProfileService,
            IUploadService uploadService,
            IAzureBlobStorageService azureBlobStorageService,
            IMediaEncodingService mediaEncodingService,
            IEmailNotificationService emailNotificationService,
            ICompanyService companyService,
            ICompanyUsageService companyUsageService,
            IMediaProcessService mediaProcessService,
            ICacheManagerService cacheManagerService,
            ILockService lockService,
            IConversationAssigneeService conversationAssigneeService,
            ISleekPayService sleekPayService,
            IConversationService conversationService,
            IUserProfileHooks userProfileHooks,
            IZDotComService zDotComService,
            IHttpClientFactory httpClientFactory,
            IConversationResolver conversationResolver,
            IChannelMessageProvider channelMessageProvider,
            IMessageChannelSwitcher messageChannelSwitcher,
            IDbContextService dbContextService,
            IFlowHubService flowHubService,
            IPiiMaskingService piiMaskingService,
            ITicketingHubMessageService ticketingHubMessageService,
            IConversationMeters conversationMeters,
            IMeasureQueryTimeService measureQueryTimeService,
            IAccessControlAggregationService accessControlAggregationService,
            IApplicationInsightsTelemetryTracer applicationInsightsTelemetryTracer)
        {
            _appDbContext = appDbContext;
            _mapper = mapper;
            _configuration = configuration;

            // _translateAPI = RestService.For<TranslateAPI>("https://api.cognitive.microsofttranslator.com/");
            _logger = logger;
            _signalRService = signalRService;
            _userProfileService = userProfileService;
            _uploadService = uploadService;
            _azureBlobStorageService = azureBlobStorageService;
            _mediaEncodingService = mediaEncodingService;
            _emailNotificationService = emailNotificationService;
            _companyService = companyService;
            _companyUsageService = companyUsageService;
            _mediaProcessService = mediaProcessService;
            _cacheManagerService = cacheManagerService;
            _lockService = lockService;
            _conversationAssigneeService = conversationAssigneeService;
            _sleekPayService = sleekPayService;
            _conversationService = conversationService;
            _userProfileHooks = userProfileHooks;
            _zDotComService = zDotComService;
            _httpClientFactory = httpClientFactory;
            _conversationResolver = conversationResolver;
            _channelMessageProvider = channelMessageProvider;
            _messageChannelSwitcher = messageChannelSwitcher;
            _dbContextService = dbContextService;
            _flowHubService = flowHubService;
            _piiMaskingService = piiMaskingService;
            _ticketingHubMessageService = ticketingHubMessageService;
            _conversationMeters = conversationMeters;
            _isEnableTicketingLogic = _configuration.GetValue<bool>("TicketingHub:IsEnableTicketingLogic");
            _measureQueryTimeService = measureQueryTimeService;
            _accessControlAggregationService = accessControlAggregationService;
            _applicationInsightsTelemetryTracer = applicationInsightsTelemetryTracer;
        }

        public async Task<ConversationWithCountViewModel> GetFormattedConversations(
            Staff companyUser,
            string assignedTo,
            int offset,
            int limit,
            string status,
            string channels,
            DateTime? afterUpdatedAt,
            DateTime? afterModifiedAt,
            string channelIds,
            string tags,
            long? teamId,
            bool? isTeamUnassigned,
            bool? isUnread,
            string orderBy,
            string version = "1",
            bool? isAssigned = null,
            bool? isCollaborator = null)
        {
            try
            {
                _logger.LogInformation(
                    $"GetConversations_{companyUser.CompanyId}_{companyUser.Id}_{assignedTo}_{offset}_{limit}_{status}_{channels}_{afterUpdatedAt}_{afterModifiedAt}_{channelIds}_{tags}_{teamId}_{isTeamUnassigned}_{isUnread}_{orderBy}_{isAssigned}_{isCollaborator}");

                if (limit > 100)
                {
                    limit = 100;
                }

                var conversationMessageV2CacheKeyPattern = new ConversationMessageV2CacheKeyPattern(
                    companyUser.Id,
                    assignedTo,
                    offset,
                    limit,
                    status,
                    channels,
                    afterUpdatedAt,
                    afterModifiedAt,
                    channelIds,
                    tags,
                    teamId,
                    isTeamUnassigned,
                    isUnread,
                    orderBy,
                    version,
                    isAssigned,
                    isCollaborator);

                var data = await _cacheManagerService.GetCacheAsync(conversationMessageV2CacheKeyPattern);

                if (!string.IsNullOrEmpty(data))
                {
                    var result = JsonConvert.DeserializeObject<ConversationWithCountViewModel>(data);
                    return result;
                }

                if (companyUser != null)
                {
                    _logger.LogInformation(
                        $"{companyUser.IdentityId} GetConversations: assignTo {assignedTo}, offset: {offset}, limit: {limit}");

                    try
                    {
                        var conversationsQueryable = await GetConversations(
                            companyUser.CompanyId,
                            companyUser,
                            status,
                            assignedTo,
                            channels,
                            afterUpdatedAt,
                            afterModifiedAt,
                            channelIds,
                            tags,
                            teamId,
                            isTeamUnassigned,
                            isUnread,
                            orderBy,
                            version,
                            isAssigned,
                            isCollaborator);

                        var channelList = new List<string>();

                        if (!string.IsNullOrEmpty(channels))
                        {
                            channelList = channels
                                .Split(",")
                                .ToList();
                        }

                        var channelIdList = new List<string>();

                        if (!string.IsNullOrEmpty(channelIds))
                        {
                            channelIdList = channelIds
                                .Split(",")
                                .ToList();
                        }

                        var conversationIds = await conversationsQueryable
                            .Select(c => c.Id)
                            .Skip(offset)
                            .Take(limit)
                            .ToListAsync();

                        var conversations = conversationIds.Any()
                            ? await conversationsQueryable
                                .Where(c => conversationIds.Contains(c.Id))
                                .Include(x => x.UserProfile)
                                .Include(x => x.facebookUser)
                                .Include(x => x.WhatsappUser)
                                .Include(x => x.Assignee.Identity)
                                .Include(x => x.EmailAddress)
                                .Include(x => x.WebClient)
                                .Include(x => x.WeChatUser)
                                .Include(x => x.LineUser)
                                .Include(x => x.SMSUser)
                                .Include(x => x.InstagramUser)
                                .Include(x => x.WhatsApp360DialogUser)
                                .Include(x => x.WhatsappCloudApiUser)
                                .Include(x => x.TelegramUser)
                                .Include(x => x.ViberUser)
                                .Include(x => x.AssignedTeam)
                                .ToListAsync()
                            : new List<Conversation>();

                        // Bookmarks
                        var bookmarkedConversationIds =
                            conversationIds.Any()
                                ? (await _appDbContext.ConversationBookmarks
                                    .Where(
                                        x =>
                                            conversationIds.Contains(x.ConversationId)
                                            && x.StaffId == companyUser.Id)
                                    .Select(x => x.ConversationId)
                                    .ToListAsync())
                                .ToHashSet()
                                : new HashSet<string>();
                        conversations.ForEach(
                            x => x.IsBookmarked = bookmarkedConversationIds.Contains(x.Id));

                        // Unread messages
                        var conversationIdToUnreadCount =
                            conversationIds.Any()
                                ? await _appDbContext.ConversationUnreadRecords
                                    .Where(
                                        x =>
                                            x.CompanyId == companyUser.CompanyId
                                            && conversationIds.Contains(x.ConversationId)
                                            && x.StaffId == companyUser.Id
                                            && x.NotificationDeliveryStatus != NotificationDeliveryStatus.Read)
                                    .GroupBy(x => x.ConversationId)
                                    .Select(
                                        g => new
                                        {
                                            ConversationId = g.Key,
                                            Count = g.Count()
                                        })
                                    .ToDictionaryAsync(x => x.ConversationId, x => x.Count)
                                : new Dictionary<string, int>();
                        conversations
                            .Where(x => x.UnreadMessageCount == 0)
                            .ForEach(
                                x => x.UnreadMessageCount = conversationIdToUnreadCount.TryGetValue(x.Id, out var value)
                                    ? value
                                    : 0);

                        var associatedTeams = await _appDbContext.CompanyStaffTeams
                            .Where(x => x.Members.Any(y => y.StaffId == companyUser.Id))
                            .ToListAsync();

                        var whatsappIds = new List<string>();
                        var twilioSenderIds = new List<string>();
                        var whatsapp360dialogDefaultChannelIds = new List<long>();
                        var whatsappCloudDefaultChannelIds = new List<string>();

                        foreach (var associatedTeam in associatedTeams)
                        {
                            if (associatedTeam.DefaultChannels?.Count > 0)
                            {
                                foreach (var defaultChannel in associatedTeam.DefaultChannels
                                             .Where(
                                                 x =>
                                                     x.channel != ChannelTypes.Whatsapp360Dialog
                                                     && x.channel != ChannelTypes.WhatsappCloudApi))
                                {
                                    channelList.Add(defaultChannel.channel);

                                    if (defaultChannel.ids != null)
                                    {
                                        foreach (var id in defaultChannel.ids)
                                        {
                                            var twilioInstance = id.Split(";", StringSplitOptions.RemoveEmptyEntries);
                                            whatsappIds.Add(twilioInstance[0]);

                                            if (twilioInstance.Count() > 1)
                                            {
                                                twilioSenderIds.Add(twilioInstance[1].Replace(": ", ":+"));
                                            }
                                        }
                                    }
                                }

                                foreach (var defaultChannel in associatedTeam.DefaultChannels
                                             .Where(x => x.channel == ChannelTypes.Whatsapp360Dialog))
                                {
                                    foreach (var channelId in defaultChannel.ids)
                                    {
                                        var validLong = long.TryParse(channelId, out var whatsapp360dialogChannelId);

                                        if (validLong)
                                        {
                                            whatsapp360dialogDefaultChannelIds.Add(whatsapp360dialogChannelId);
                                        }
                                    }
                                }

                                foreach (var defaultChannel in associatedTeam.DefaultChannels
                                             .Where(x => x.channel == ChannelTypes.WhatsappCloudApi))
                                {
                                    foreach (var channelId in defaultChannel.ids)
                                    {
                                        whatsappCloudDefaultChannelIds.Add(channelId);
                                    }
                                }
                            }
                        }

                        var conversationViewModels = _mapper.Map<List<ConversationNoCompanyResponseViewModel>>(conversations);

                        var collaborators =
                            conversationIds.Any()
                                ? await _appDbContext.ConversationAdditionalAssignees
                                    .Where(
                                        y => conversationViewModels
                                            .Select(x => x.ConversationId)
                                            .Contains(y.ConversationId))
                                    .Include(y => y.Assignee.Identity)
                                    .ProjectTo<AdditionalAssigneeResponse>(_mapper.ConfigurationProvider)
                                    .ToListAsync()
                                : new List<AdditionalAssigneeResponse>();
                        conversationViewModels.ForEach(
                            x =>
                                x.AdditionalAssignees = collaborators
                                    .Where(y => y.ConversationId == x.ConversationId)
                                    .ToList());

                        var hashtags =
                            conversationIds.Any()
                                ? await _appDbContext.ConversationHashtags
                                    .Where(
                                        y => conversationViewModels
                                            .Select(x => x.ConversationId)
                                            .Contains(y.ConversationId))
                                    .Include(y => y.Hashtag)
                                    .OrderByDescending(x => x.Id)
                                    .ProjectTo<ConversationHashtagResponse>(_mapper.ConfigurationProvider)
                                    .ToListAsync()
                                : new List<ConversationHashtagResponse>();
                        conversationViewModels.ForEach(
                            x =>
                                x.ConversationHashtags = hashtags
                                    .Where(y => y.ConversationId == x.ConversationId)
                                    .ToList());

                        var rolePermission = await _appDbContext.CompanyRolePermissions
                            .FirstOrDefaultAsync(
                                x =>
                                    x.CompanyId == companyUser.CompanyId
                                    && x.StaffUserRole == companyUser.RoleType);


                        var filterLastMessage = rolePermission != null &&
                                                rolePermission.Permission.IsShowDefaultChannelMessagesOnly &&
                                                companyUser.RoleType != StaffUserRole.Admin;

                        var isUpdatedLastMessageId = false;

                        foreach (var conversationViewModel in conversationViewModels)
                        {
                            isUpdatedLastMessageId = await _conversationService.GetConversationLastMessage(
                                conversationViewModel,
                                filterLastMessage,
                                whatsappIds,
                                whatsapp360dialogDefaultChannelIds,
                                channelIdList,
                                channelList,
                                twilioSenderIds,
                                isUpdatedLastMessageId,
                                whatsappCloudDefaultChannelIds);

                            if (conversationViewModel.UnreadMessageCount > 0
                                && filterLastMessage
                                && conversationViewModel.LastMessageId.HasValue
                                && (whatsappIds.Any()
                                    || whatsapp360dialogDefaultChannelIds.Any()
                                    || whatsappCloudDefaultChannelIds.Any()))
                            {
                                // not to display blue circle in frontend for hiding unread messages according to the role when setting to show default channel message only
                                var lastMessage = await _appDbContext.ConversationMessages
                                    .AsNoTracking()
                                    .Include(x => x.whatsappReceiver)
                                    .Include(x => x.whatsappSender)
                                    .Include(x => x.Whatsapp360DialogReceiver)
                                    .Include(x => x.Whatsapp360DialogSender)
                                    .Include(x => x.facebookReceiver)
                                    .Include(x => x.facebookSender)
                                    .Include(x => x.InstagramReceiver)
                                    .Include(x => x.InstagramSender)
                                    .FirstOrDefaultAsync(x => x.Id == conversationViewModel.LastMessageId);

                                switch (lastMessage.Channel)
                                {
                                    case ChannelTypes.WhatsappTwilio:
                                        if ((lastMessage.whatsappReceiver != null &&
                                             !whatsappIds.Contains(lastMessage.whatsappReceiver.InstanceId)) ||
                                            (lastMessage.whatsappSender != null &&
                                             !whatsappIds.Contains(lastMessage.whatsappSender.InstanceId)))
                                        {
                                            conversationViewModel.UnreadMessageCount = 0;
                                        }

                                        break;
                                    case ChannelTypes.Whatsapp360Dialog:
                                        if ((lastMessage.Whatsapp360DialogReceiver != null &&
                                             lastMessage.Whatsapp360DialogReceiver.ChannelId.HasValue &&
                                             !whatsapp360dialogDefaultChannelIds.Contains(
                                                 lastMessage.Whatsapp360DialogReceiver.ChannelId.Value)) ||
                                            (lastMessage.Whatsapp360DialogSender != null &&
                                             lastMessage.Whatsapp360DialogSender.ChannelId.HasValue &&
                                             !whatsapp360dialogDefaultChannelIds.Contains(
                                                 lastMessage.Whatsapp360DialogSender.ChannelId.Value)))
                                        {
                                            conversationViewModel.UnreadMessageCount = 0;
                                        }

                                        break;
                                    case ChannelTypes.WhatsappCloudApi:
                                        if (!whatsappCloudDefaultChannelIds.Contains(lastMessage.ChannelIdentityId))
                                        {
                                            conversationViewModel.UnreadMessageCount = 0;
                                        }

                                        break;
                                    case ChannelTypes.Facebook:
                                        if ((lastMessage.facebookReceiver != null &&
                                             lastMessage.facebookReceiver.pageId != null &&
                                             !whatsappIds.Contains(lastMessage.facebookReceiver.pageId)) ||
                                            (lastMessage.facebookSender != null &&
                                             lastMessage.facebookSender.pageId != null &&
                                             !whatsappIds.Contains(lastMessage.facebookSender.pageId)))
                                        {
                                            conversationViewModel.UnreadMessageCount = 0;
                                        }

                                        break;
                                    case ChannelTypes.Instagram:
                                        if ((lastMessage.InstagramReceiver != null &&
                                             lastMessage.InstagramReceiver.InstagramPageId != null &&
                                             !whatsappIds.Contains(lastMessage.InstagramReceiver.InstagramPageId)) ||
                                            (lastMessage.InstagramSender != null &&
                                             lastMessage.InstagramSender.InstagramPageId != null &&
                                             !whatsappIds.Contains(lastMessage.InstagramSender.InstagramPageId)))
                                        {
                                            conversationViewModel.UnreadMessageCount = 0;
                                        }

                                        break;
                                }
                            }
                        }

                        if (isUpdatedLastMessageId)
                        {
                            await _appDbContext.SaveChangesAsync();
                        }

                        var conversationIdsForMappedResult = conversationViewModels
                            .Select(x => x.ConversationId)
                            .ToList();


                        var firstMessageIdsForMappedResult = await _appDbContext.ConversationMessages
                            .Where(y => conversationIdsForMappedResult.Contains(y.ConversationId))
                            .GroupBy(y => y.ConversationId)
                            .Select(g => new
                            {
                                ConversationId = g.Key,
                                Id = g.Min(y => y.Id) // Get the smallest Id (first message) per conversation
                            })
                            .ToDictionaryAsync(x => x.ConversationId, x => x.Id); // Convert to dictionary for performance

                        foreach (var item in conversationViewModels)
                        {
                            if (firstMessageIdsForMappedResult.TryGetValue(item.ConversationId, out var firstMessageId))
                            {
                                item.FirstMessageId = firstMessageId;
                            }
                            else
                            {
                                // Handle the situation when no message is found for the conversation
                            }
                        }

                        var results = new ConversationWithCountViewModel
                        {
                            Data = conversationViewModels,
                            Count = await conversationsQueryable.CountAsync()
                        };

                        if (conversationIds.Any() && _isEnableTicketingLogic)
                        {
                            var userProfileIds = results.Data.Select(x => x.UserProfile.Id).ToList();
                            var ticketCount = await _ticketingHubMessageService.GetTicketingUserProfileCountAsync(
                                companyUser.CompanyId,
                                userProfileIds);

                            if (ticketCount != null)
                            {
                                results.Data = results.Data.GroupJoin(
                                ticketCount,
                                result => result.UserProfile.Id,
                                count => count.SleekflowUserProfileId,
                                (result, countGroup) => new ConversationNoCompanyResponseViewModel
                                {
                                    ConversationId = result.ConversationId,
                                    CompanyId = result.CompanyId,
                                    ConversationChannels = result.ConversationChannels,
                                    MessageGroupName = result.MessageGroupName,
                                    UserProfile = result.UserProfile,
                                    Status = result.Status,
                                    Assignee = result.Assignee,
                                    AdditionalAssignees = result.AdditionalAssignees,
                                    ConversationHashtags = result.ConversationHashtags,
                                    LastMessage = result.LastMessage,
                                    Messages = result.Messages,
                                    UpdatedTime = result.UpdatedTime,
                                    ModifiedAt = result.ModifiedAt,
                                    UnreadMessageCount = result.UnreadMessageCount,
                                    SnoozeUntil = result.SnoozeUntil,
                                    FirstMessageId = result.FirstMessageId,
                                    LastMessageId = result.LastMessageId,
                                    LastMessageChannel = result.LastMessageChannel,
                                    LastChannelIdentityId = result.LastChannelIdentityId,
                                    AssignedTeam = result.AssignedTeam,
                                    IsSandbox = result.IsSandbox,
                                    IsBookmarked = result.IsBookmarked,
                                    Metadata = result.Metadata,
                                    TicketCount = countGroup.Select(x => x.Count).DefaultIfEmpty(0).FirstOrDefault()
                                }).ToList();
                            }
                        }

                        await AddConversationMessageV2Cache(
                            companyUser.CompanyId,
                            conversationMessageV2CacheKeyPattern,
                            results);


                        return results;
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(
                            ex,
                            "[{MethodName}] inner error: {ExceptionMessage}",
                            nameof(GetFormattedConversations),
                            ex.Message);

                        return new ConversationWithCountViewModel();
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[{MethodName}] outer error: {ExceptionMessage}",
                    nameof(GetFormattedConversations),
                    ex.Message);

                return new ConversationWithCountViewModel();
            }

            return new ConversationWithCountViewModel();
        }

        public async Task SendReadMoreMessage(
            string companyId,
            string conversationId,
            long? whatsappSenderId,
            string messageContent,
            string contentSid = null,
            List<UploadedFile> uploadedFiles = null,
            DeliveryType deliveryType = DeliveryType.ReadMore)
        {
            if (uploadedFiles == null)
            {
                uploadedFiles = new List<UploadedFile>();
            }
            else
            {
                uploadedFiles.ForEach(x => x.FileId = Guid.NewGuid().ToString());
            }

            var conversation = await _appDbContext.Conversations
                .Include(x => x.WhatsappUser)
                .FirstOrDefaultAsync(
                    x =>
                        x.CompanyId == companyId
                        && x.Id == conversationId);

            var conversationMessage = new ConversationMessage
            {
                Channel = ChannelTypes.WhatsappTwilio,
                MessageType = (uploadedFiles.Count > 0) ? "file" : "text",
                MessageContent = messageContent,
                whatsappReceiver = conversation.WhatsappUser,
                whatsappReceiverId = conversation.WhatsappUserId,
                whatsappSenderId = whatsappSenderId,
                whatsappSender = await _appDbContext.SenderWhatsappSenders
                    .FirstOrDefaultAsync(x => x.Id == whatsappSenderId),
                IsSentFromSleekflow = true,
                DeliveryType = deliveryType,
                UploadedFiles = uploadedFiles
            };

            // if content api opt-in
            if (!string.IsNullOrEmpty(contentSid))
            {
                conversationMessage.ExtendedMessagePayload = new ExtendedMessagePayload()
                {
                    ExtendedMessageType = ExtendedMessageType.TwilioContentApi,
                    ExtendedMessagePayloadDetail = new ExtendedMessagePayloadDetail()
                    {
                        WhatsappTwilioContentApiObject = new WhatsappTwilioContentApiObject()
                        {
                            ContentSid = contentSid
                        }
                    }
                };
            }

            await SendMessage(conversation, conversationMessage);
        }

        public async Task<string> SendAutomatedMessage(Conversation conversation, AutomationAction automationAction)
        {
            var systemChannel = new List<string>
            {
                ChannelTypes.Note,
                "system"
            };

            var conversationMessages = await _appDbContext.ConversationMessages
                .AsNoTracking()
                .OrderByDescending(x => x.Id)
                .FirstOrDefaultAsync(x =>
                    x.ConversationId == conversation.Id
                    && !systemChannel.Contains(x.Channel));

            if (conversationMessages == null)
            {
                var firstWhatsappTwilio = await _appDbContext.ConfigWhatsAppConfigs
                    .AsNoTracking()
                    .Where(x => x.CompanyId == conversation.CompanyId)
                    .OrderBy(x => x.ConnectedDateTime)
                    .Select(
                        x => new
                        {
                            x.Id,
                            x.ConnectedDateTime
                        })
                    .FirstOrDefaultAsync();

                var firstWhatsapp360dialog = await _appDbContext.ConfigWhatsApp360DialogConfigs
                    .AsNoTracking()
                    .Where(x => x.CompanyId == conversation.CompanyId)
                    .OrderBy(x => x.CreatedAt)
                    .Select(
                        x => new
                        {
                            x.Id,
                            x.CreatedAt
                        })
                    .FirstOrDefaultAsync();

                var firstWhatsappCloudApi = await _appDbContext.ConfigWhatsappCloudApiConfigs
                    .AsNoTracking()
                    .Where(x => x.CompanyId == conversation.CompanyId)
                    .OrderBy(x => x.CreatedAt)
                    .Select(
                        x => new
                        {
                            x.Id,
                            x.CreatedAt
                        })
                    .FirstOrDefaultAsync();

                var isUseTwilio = false;
                var isUse360Dialog = false;
                var isUseCloudApi = false;

                if (firstWhatsappTwilio != null
                    && firstWhatsapp360dialog != null
                    && firstWhatsappCloudApi != null)
                {
                    isUseTwilio = firstWhatsappTwilio.ConnectedDateTime <= firstWhatsapp360dialog.CreatedAt &&
                                  firstWhatsappTwilio.ConnectedDateTime < firstWhatsappCloudApi.CreatedAt;

                    isUse360Dialog = firstWhatsappTwilio.ConnectedDateTime >= firstWhatsapp360dialog.CreatedAt &&
                                     firstWhatsapp360dialog.CreatedAt < firstWhatsappCloudApi.CreatedAt;

                    isUseCloudApi = firstWhatsappCloudApi.CreatedAt <= firstWhatsappTwilio.ConnectedDateTime &&
                                    firstWhatsappCloudApi.CreatedAt <= firstWhatsapp360dialog.CreatedAt;
                }
                else if (firstWhatsappTwilio != null)
                {
                    isUseTwilio = true;
                }
                else if (firstWhatsapp360dialog != null)
                {
                    isUse360Dialog = true;
                }
                else if (firstWhatsappCloudApi != null)
                {
                    isUseCloudApi = true;
                }

                conversationMessages = new ConversationMessage();

                if (conversation.WhatsappUser != null
                    && isUseTwilio)
                {
                    conversationMessages.Channel = ChannelTypes.WhatsappTwilio;
                }
                else if (conversation.WhatsApp360DialogUser != null
                         && conversation.WhatsApp360DialogUser.ChannelId.HasValue
                         && isUse360Dialog)
                {
                    conversationMessages.Channel = ChannelTypes.Whatsapp360Dialog;
                }
                else if (conversation.WhatsappCloudApiUser != null
                         && isUseCloudApi)
                {
                    conversationMessages.Channel = ChannelTypes.WhatsappCloudApi;
                }
                else if (conversation.SMSUser != null)
                {
                    conversationMessages.Channel = ChannelTypes.Sms;
                }
            }
            else
            {
                conversationMessages.Id = 0;
            }

            var messageContent = automationAction.MessageContent;

            var templateParams = new List<string>();

            if (automationAction.MessageParams?.Count > 0)
            {
                templateParams = await FormatParams(conversation.UserProfile, automationAction.MessageParams);
            }

            if (templateParams.Count > 0
                && !string.IsNullOrWhiteSpace(automationAction.MessageContent))
            {
                messageContent = string.Format(
                    automationAction.MessageContent,
                    templateParams
                        .Select(x => x.ToString())
                        .ToArray());
            }

            if (automationAction.TargetedChannelWithIds?.Count > 0)
            {
                foreach (var channel in automationAction.TargetedChannelWithIds)
                {
                    switch (channel.channel)
                    {
                        case ChannelTypes.WhatsappTwilio:
                        case "twilio_whatsapp":
                            if (conversation.UserProfile.WhatsAppAccount == null)
                            {
                                return null;
                            }

                            if (channel.ids?.Count > 0)
                            {
                                if (!channel.ids.Contains(
                                        ConversationHelper.GetInstanceId(conversation.UserProfile.WhatsAppAccount)))
                                {
                                    await _userProfileService.SwitchWhatsappChannel(
                                        conversation.UserProfileId,
                                        new ChangeChatAPIInstance
                                        {
                                            InstanceId = channel.ids.FirstOrDefault()
                                        });

                                    conversation = await _appDbContext.Conversations
                                        .Include(x => x.NaiveUser)
                                        .Include(x => x.facebookUser)
                                        .Include(X => X.EmailAddress)
                                        .Include(x => x.WhatsappUser)
                                        .Include(x => x.InstagramUser)
                                        .Include(x => x.WebClient)
                                        .Include(x => x.WeChatUser)
                                        .Include(x => x.LineUser)
                                        .Include(x => x.WhatsApp360DialogUser)
                                        .Include(x => x.WhatsappCloudApiUser)
                                        .Include(x => x.TelegramUser)
                                        .Include(x => x.ViberUser)
                                        .Include(x => x.SMSUser)
                                        .FirstOrDefaultAsync(x => x.Id == conversation.Id);
                                }
                            }

                            if (conversationMessages.Channel != ChannelTypes.WhatsappTwilio)
                            {
                                conversationMessages.Channel = ChannelTypes.WhatsappTwilio;
                            }

                            break;
                        case ChannelTypes.Whatsapp360Dialog:
                            if (conversation.UserProfile.WhatsApp360DialogUser == null)
                            {
                                return null;
                            }

                            if (channel.ids?.Count > 0)
                            {
                                if (conversation.UserProfile.WhatsApp360DialogUser.ChannelId == null
                                    || !channel.ids
                                            .Select(long.Parse)
                                            .Contains(conversation.UserProfile.WhatsApp360DialogUser.ChannelId.Value))
                                {
                                    await _userProfileService.SwitchWhatsapp360DialogChannel(
                                        conversation.CompanyId,
                                        conversation.UserProfileId,
                                        long.Parse(channel.ids.First()));

                                    conversation = await _appDbContext.Conversations
                                        .Include(x => x.NaiveUser)
                                        .Include(x => x.facebookUser)
                                        .Include(x => x.InstagramUser)
                                        .Include(x => x.EmailAddress)
                                        .Include(x => x.WhatsappUser)
                                        .Include(x => x.WebClient)
                                        .Include(x => x.WeChatUser)
                                        .Include(x => x.LineUser)
                                        .Include(x => x.SMSUser)
                                        .Include(x => x.ViberUser)
                                        .Include(x => x.TelegramUser)
                                        .Include(x => x.WhatsApp360DialogUser)
                                        .Include(x => x.WhatsappCloudApiUser)
                                        .FirstOrDefaultAsync(x => x.Id == conversation.Id);
                                }
                            }

                            if (conversationMessages.Channel != ChannelTypes.Whatsapp360Dialog)
                            {
                                conversationMessages.Channel = ChannelTypes.Whatsapp360Dialog;
                            }

                            break;
                        case ChannelTypes.WhatsappCloudApi:
                            if (conversation.UserProfile.WhatsappCloudApiUser == null)
                            {
                                return null;
                            }

                            if (channel.ids?.Count > 0)
                            {
                                if (conversation.UserProfile.WhatsappCloudApiUser.WhatsappChannelPhoneNumber == null
                                    || !channel.ids.Contains(
                                        await _appDbContext.ConfigWhatsappCloudApiConfigs
                                            .AsNoTracking()
                                            .Where(
                                                x =>
                                                    x.CompanyId == conversation.CompanyId
                                                    && x.WhatsappPhoneNumber == conversation.UserProfile.WhatsappCloudApiUser.WhatsappChannelPhoneNumber)
                                            .Select(x => x.WhatsappPhoneNumber)
                                            .FirstOrDefaultAsync()))
                                {
                                    await _userProfileService.SwitchWhatsappCloudApiChannel(
                                        conversation.CompanyId,
                                        conversation.UserProfileId,
                                        await _appDbContext.ConfigWhatsappCloudApiConfigs.AsNoTracking()
                                            .Where(
                                                x =>
                                                    x.CompanyId == conversation.CompanyId
                                                    && x.WhatsappPhoneNumber == channel.ids.FirstOrDefault())
                                            .Select(x => x.WhatsappPhoneNumber)
                                            .FirstOrDefaultAsync());


                                    conversation = await _appDbContext.Conversations
                                        .Include(x => x.NaiveUser)
                                        .Include(x => x.facebookUser)
                                        .Include(x => x.InstagramUser)
                                        .Include(x => x.EmailAddress)
                                        .Include(x => x.WhatsappUser)
                                        .Include(x => x.WebClient)
                                        .Include(x => x.WeChatUser)
                                        .Include(x => x.LineUser)
                                        .Include(x => x.SMSUser)
                                        .Include(x => x.ViberUser)
                                        .Include(x => x.TelegramUser)
                                        .Include(x => x.WhatsApp360DialogUser)
                                        .Include(x => x.WhatsappCloudApiUser)
                                        .FirstOrDefaultAsync(x => x.Id == conversation.Id);
                                }
                            }

                            if (conversationMessages.Channel != ChannelTypes.WhatsappCloudApi)
                            {
                                conversationMessages.Channel = ChannelTypes.WhatsappCloudApi;
                            }

                            break;
                        case ChannelTypes.Sms:
                            if (conversationMessages.Channel != ChannelTypes.Sms)
                            {
                                conversationMessages.Channel = ChannelTypes.Sms;
                            }

                            break;
                    }
                }
            }
            else
            {
                await HandlePossiblePost360DialogMigrationAsync(conversation, conversationMessages);
            }

            conversation.conversationHashtags = await _appDbContext.ConversationHashtags
                .Where(x => x.ConversationId == conversation.Id)
                .ToListAsync();

            var preparedReplyMessage = PrepareReplyMessage(conversation, conversationMessages, messageContent);

            if (automationAction.UploadedFiles.Count > 0)
            {
                preparedReplyMessage.MessageType = "file";
                preparedReplyMessage.UploadedFiles = _mapper.Map<List<UploadedFile>>(automationAction.UploadedFiles);
            }
            else if (automationAction.WhatsApp360DialogExtendedAutomationMessages is { Count: > 0 })
            {
                // 360 Dialog Template / interactive
                var whatsapp360DialogConfigId = conversation.WhatsApp360DialogUser?.ChannelId;
                whatsapp360DialogConfigId ??= conversation.LastMessageChannelId;

                if (whatsapp360DialogConfigId != null)
                {
                    var wabaAccountId = await _appDbContext.ConfigWhatsApp360DialogConfigs
                        .Where(
                            x =>
                                x.Id == whatsapp360DialogConfigId
                                && x.CompanyId == conversation.CompanyId)
                        .Select(x => x.WabaAccountId)
                        .FirstOrDefaultAsync();

                    var extendedAutomationMessage =
                        automationAction.WhatsApp360DialogExtendedAutomationMessages.FirstOrDefault(
                            x => x.WabaAccountId == wabaAccountId);

                    if (extendedAutomationMessage != null)
                    {
                        switch (extendedAutomationMessage.MessageType)
                        {
                            case "template":
                                preparedReplyMessage.Whatsapp360DialogExtendedMessagePayload =
                                    new Whatsapp360DialogExtendedMessagePayload()
                                    {
                                        Whatsapp360DialogTemplateMessage =
                                            JsonConvert.DeserializeObject<Whatsapp360DialogTemplateMessageViewModel>(
                                                JsonConvert.SerializeObject(
                                                    extendedAutomationMessage.Whatsapp360DialogTemplateMessage)),
                                    };

                                preparedReplyMessage.Whatsapp360DialogExtendedMessagePayload
                                        .Whatsapp360DialogTemplateMessage.Components =
                                    preparedReplyMessage.Whatsapp360DialogExtendedMessagePayload
                                        .Whatsapp360DialogTemplateMessage.Components.Format(templateParams);
                                preparedReplyMessage.MessageType = extendedAutomationMessage.MessageType;

                                break;
                            case "interactive":
                                preparedReplyMessage.Whatsapp360DialogExtendedMessagePayload =
                                    new Whatsapp360DialogExtendedMessagePayload()
                                    {
                                        Whatsapp360DialogInteractiveObject =
                                            JsonConvert.DeserializeObject<InteractiveObject>(
                                                JsonConvert.SerializeObject(
                                                    extendedAutomationMessage.Whatsapp360DialogInteractiveObject)),
                                    };

                                preparedReplyMessage.Whatsapp360DialogExtendedMessagePayload
                                        .Whatsapp360DialogInteractiveObject =
                                    preparedReplyMessage.Whatsapp360DialogExtendedMessagePayload
                                        .Whatsapp360DialogInteractiveObject.Format(templateParams);
                                preparedReplyMessage.MessageType = extendedAutomationMessage.MessageType;

                                break;
                        }
                    }
                }
            }
            else if (automationAction.ExtendedAutomationMessage?.Channel == ChannelTypes.WhatsappCloudApi &&
                     automationAction.ExtendedAutomationMessage?.WhatsappCloudApiByWabaExtendedAutomationMessages is
                     { Count: > 0 })
            {
                var whatsappChannelPhoneNumber = conversation.WhatsappCloudApiUser.WhatsappChannelPhoneNumber;

                var whatsappCloudApiConfig = await _appDbContext.ConfigWhatsappCloudApiConfigs
                    .AsNoTracking()
                    .FirstOrDefaultAsync(
                        x =>
                            x.CompanyId == conversation.CompanyId
                            && x.WhatsappPhoneNumber == whatsappChannelPhoneNumber);

                whatsappCloudApiConfig ??= await _appDbContext.ConfigWhatsappCloudApiConfigs
                    .AsNoTracking()
                    .FirstOrDefaultAsync(
                        x =>
                            x.CompanyId == conversation.CompanyId
                            && x.Id == conversation.LastMessageChannelId);

                if (whatsappCloudApiConfig != null)
                {
                    var byWabaExtendedAutomationMessage = automationAction
                        .ExtendedAutomationMessage?
                        .WhatsappCloudApiByWabaExtendedAutomationMessages
                        .FirstOrDefault(x => x.MessagingHubWabaId == whatsappCloudApiConfig.MessagingHubWabaId)
                        .DeepClone();

                    if (byWabaExtendedAutomationMessage != null)
                    {
                        switch (automationAction.ExtendedAutomationMessage.MessageType)
                        {
                            case "template":
                                preparedReplyMessage.MessageType = "template";

                                preparedReplyMessage.ExtendedMessagePayload = new ExtendedMessagePayload()
                                {
                                    Channel = ChannelTypes.WhatsappCloudApi,
                                    ExtendedMessageType = ExtendedMessageType.WhatsappCloudApiTemplateMessage,
                                    ExtendedMessagePayloadDetail = new ExtendedMessagePayloadDetail()
                                    {
                                        WhatsappCloudApiTemplateMessageObject = byWabaExtendedAutomationMessage
                                            .ExtendedMessagePayloadDetail.WhatsappCloudApiTemplateMessageObject
                                    }
                                };

                                preparedReplyMessage.ExtendedMessagePayload.ExtendedMessagePayloadDetail
                                        .WhatsappCloudApiTemplateMessageObject.Components =
                                    preparedReplyMessage.ExtendedMessagePayload.ExtendedMessagePayloadDetail
                                        .WhatsappCloudApiTemplateMessageObject.Components.Format(templateParams);

                                break;
                            case "interactive":
                                preparedReplyMessage.MessageType = "interactive";

                                preparedReplyMessage.ExtendedMessagePayload = new ExtendedMessagePayload()
                                {
                                    Channel = ChannelTypes.WhatsappCloudApi,
                                    ExtendedMessageType = ExtendedMessageType.WhatsappCloudApiInteractiveMessage,
                                    ExtendedMessagePayloadDetail = new ExtendedMessagePayloadDetail()
                                    {
                                        WhatsappCloudApiInteractiveObject = byWabaExtendedAutomationMessage
                                            .ExtendedMessagePayloadDetail.WhatsappCloudApiInteractiveObject
                                    }
                                };

                                preparedReplyMessage.ExtendedMessagePayload.ExtendedMessagePayloadDetail
                                        .WhatsappCloudApiInteractiveObject =
                                    preparedReplyMessage.ExtendedMessagePayload.ExtendedMessagePayloadDetail
                                        .WhatsappCloudApiInteractiveObject.Format(templateParams);

                                break;
                            case "contacts":
                                preparedReplyMessage.MessageType = "contacts";

                                preparedReplyMessage.ExtendedMessagePayload = new ExtendedMessagePayload()
                                {
                                    Channel = ChannelTypes.WhatsappCloudApi,
                                    ExtendedMessageType = ExtendedMessageType.WhatsappCloudApiContactsMessage,
                                    ExtendedMessagePayloadDetail = new ExtendedMessagePayloadDetail()
                                    {
                                        WhatsappCloudApiContactsObject = byWabaExtendedAutomationMessage
                                            .ExtendedMessagePayloadDetail.WhatsappCloudApiContactsObject
                                    }
                                };

                                break;
                            case "location":
                                preparedReplyMessage.MessageType = "location";

                                preparedReplyMessage.ExtendedMessagePayload = new ExtendedMessagePayload()
                                {
                                    Channel = ChannelTypes.WhatsappCloudApi,
                                    ExtendedMessageType = ExtendedMessageType.WhatsappCloudApiLocationMessage,
                                    ExtendedMessagePayloadDetail = new ExtendedMessagePayloadDetail()
                                    {
                                        WhatsappCloudApiLocationObject = byWabaExtendedAutomationMessage
                                            .ExtendedMessagePayloadDetail.WhatsappCloudApiLocationObject
                                    }
                                };

                                break;
                            case "reaction":
                                preparedReplyMessage.MessageType = "reaction";

                                preparedReplyMessage.ExtendedMessagePayload = new ExtendedMessagePayload()
                                {
                                    Channel = ChannelTypes.WhatsappCloudApi,
                                    ExtendedMessageType = ExtendedMessageType.WhatsappCloudApiReactionMessage,
                                    ExtendedMessagePayloadDetail = new ExtendedMessagePayloadDetail()
                                    {
                                        WhatsappCloudApiReactionObject = byWabaExtendedAutomationMessage
                                            .ExtendedMessagePayloadDetail.WhatsappCloudApiReactionObject
                                    }
                                };

                                preparedReplyMessage.ExtendedMessagePayload.ExtendedMessagePayloadDetail
                                    .WhatsappCloudApiReactionObject.MessageId ??= conversationMessages.MessageUniqueID;

                                break;
                        }
                    }
                }
            }

            var sentResult = await SendMessage(
                conversation,
                preparedReplyMessage,
                triggerActivity: true,
                EnableChatbot: false,
                EnableTranslation: false);

            return messageContent;
        }

        private async Task HandlePossiblePost360DialogMigrationAsync(
            Conversation conversation,
            ConversationMessage conversationMessage)
        {
            if (string.Equals(conversationMessage.Channel, ChannelTypes.Whatsapp360Dialog, StringComparison.OrdinalIgnoreCase) &&
                conversation.WhatsApp360DialogUser.ChannelId.HasValue)
            {
                // Check if 360 dialog channel still exists
                var is360DialogChannelStillExist = await _appDbContext.ConfigWhatsApp360DialogConfigs
                    .AsNoTracking()
                    .AnyAsync(
                        config =>
                            config.CompanyId == conversation.CompanyId &&
                            config.Id == conversation.WhatsApp360DialogUser.ChannelId.Value);

                // Check if target 360 dialog channel has been migrated to Cloud API
                var cloudApiConfigMigratedFrom360Dialog = await _appDbContext.ConfigWhatsappCloudApiConfigs
                    .AsNoTracking()
                    .FirstOrDefaultAsync(
                        config =>
                            config.CompanyId == conversation.CompanyId &&
                            config.WhatsappPhoneNumber == conversation.WhatsApp360DialogUser.ChannelWhatsAppPhoneNumber);

                // No migration of target 360 dialog channel found
                if (is360DialogChannelStillExist &&
                    cloudApiConfigMigratedFrom360Dialog is null)
                {
                    return;
                }

                // Target 360 dialog channel has been migrated to Cloud API
                if (cloudApiConfigMigratedFrom360Dialog is not null)
                {
                    var userProfile = await _userProfileService.SwitchWhatsappCloudApiChannel(
                        conversation.CompanyId,
                        conversation.UserProfileId,
                        cloudApiConfigMigratedFrom360Dialog.WhatsappPhoneNumber);
                    conversation.WhatsappCloudApiUser = userProfile.WhatsappCloudApiUser;
                    conversationMessage.Channel = ChannelTypes.WhatsappCloudApi;

                    return;
                }

                // Don't have Cloud API channel migrated from 360 dialog, check if company has other 360 dialog instances
                var default360DialogChannelConfig = await _appDbContext.ConfigWhatsApp360DialogConfigs
                    .AsNoTracking()
                    .OrderBy(config => config.CreatedAt)
                    .FirstOrDefaultAsync(config => config.CompanyId == conversation.CompanyId);

                if (default360DialogChannelConfig is not null)
                {
                    var userProfile = await _userProfileService.SwitchWhatsapp360DialogChannel(
                        conversation.CompanyId,
                        conversation.UserProfileId,
                        default360DialogChannelConfig.Id);
                    conversation.WhatsApp360DialogUser = userProfile.WhatsApp360DialogUser;

                    return;
                }

                // Final resort, ideally should not reach here
                var lastChannel = await _appDbContext.UserProfileCustomFields
                    .AsNoTracking()
                    .Where(f =>
                        f.CompanyDefinedField.FieldName.ToLower() == "LastChannel" &&
                        f.UserProfileId == conversation.UserProfileId)
                    .Select(f => f.Value)
                    .FirstOrDefaultAsync();

                if (!string.IsNullOrWhiteSpace(lastChannel))
                {
                    conversationMessage.Channel = lastChannel;
                }
            }
        }

        public async Task<ConversationMessage> SendConversationNote(
            string companyId,
            string conversationId,
            string staffId,
            ConversationMessage conversationMessage,
            ConversationNoteViewModel conversationNoteViewModel)
        {
            if (string.IsNullOrEmpty(conversationMessage.MessageContent)
                && (conversationNoteViewModel?.files == null || conversationNoteViewModel.files.IsNullOrEmpty())
                && (conversationNoteViewModel?.fileUrls == null || conversationNoteViewModel.fileUrls.IsNullOrEmpty())
                && (conversationNoteViewModel?.fileNames == null || conversationNoteViewModel.fileNames.IsNullOrEmpty())
                )
            {
                return null;
            }

            var conversation = await _appDbContext.Conversations
                .FirstOrDefaultAsync(
                    x =>
                        x.CompanyId == companyId
                        && x.Id == conversationId);

            conversationMessage.Channel = ChannelTypes.Note;
            conversationMessage.ConversationId = conversationId;
            conversationMessage.CompanyId = companyId;
            conversationMessage.IsSentFromSleekflow = true;
            conversationMessage.LocalTimestamp = conversationNoteViewModel?.LocalTimestamp;

            if (conversation == null)
            {
                throw new Exception("conversation not found");
            }

            if (!string.IsNullOrEmpty(conversationNoteViewModel?.AssigneeId))
            {
                conversationMessage.MessageAssignee = await _appDbContext.UserRoleStaffs
                    .Include(x => x.Identity)
                    .FirstOrDefaultAsync(
                        x =>
                            x.CompanyId == companyId
                            && x.IdentityId == conversationNoteViewModel.AssigneeId);
            }

            if (!string.IsNullOrEmpty(staffId))
            {
                conversationMessage.SenderId = staffId;

                conversationMessage.Sender = await _appDbContext.UserRoleStaffs
                    .Where(
                        x =>
                            x.CompanyId == companyId
                            && x.IdentityId == staffId)
                    .Include(x => x.Identity)
                    .Select(x => x.Identity)
                    .FirstOrDefaultAsync();
            }

            await _appDbContext.ConversationMessages.AddAsync(conversationMessage);
            var fileURLMessages = new List<FileURLMessage>();

            if (conversationNoteViewModel?.QuickReplyId != null)
            {
                var quickReply = await _appDbContext.CompanyQuickReplies
                    .Include(x => x.QuickReplyFile)
                    .Include(x => x.CompanyQuickReplyLinguals)
                    .FirstOrDefaultAsync(
                        x =>
                            x.Id == conversationNoteViewModel.QuickReplyId.Value
                            && x.CompanyId == companyId);

                if (quickReply.QuickReplyFile != null)
                {
                    conversationMessage.DeliveryType = DeliveryType.QuickReply;
                    conversationMessage.MessageType = "file";

                    var provider = new FileExtensionContentTypeProvider();
                    string contentType;

                    var domainName = _configuration.GetValue<string>("Values:DomainName");

                    var url =
                        $"{domainName}/company/quickReply/attachment/private/{quickReply.QuickReplyFile.QuickReplyFileId}";

                    if (!provider.TryGetContentType(url, out contentType))
                    {
                        contentType = "application/octet-stream";
                    }

                    switch (conversationMessage.Channel)
                    {
                        case ChannelTypes.Facebook:
                        case ChannelTypes.Instagram:
                        case ChannelTypes.Wechat:
                            conversationMessage.MessageContent = null;

                            break;
                    }

                    fileURLMessages.Add(
                        new FileURLMessage
                        {
                            FileName = Path.GetFileName(quickReply.QuickReplyFile.Filename),
                            FileURL = url,
                            MIMEType = contentType
                        });

                    await HandleMessageFileUpload(
                        companyId,
                        conversationId,
                        conversationMessage,
                        fileURLMessages,
                        conversation);
                }
            }
            else if (conversationNoteViewModel?.files != null)
            {
                foreach (IFormFile file in conversationNoteViewModel.files)
                {
                    var fileName = $"Conversation/{conversationId}/{DateTime.UtcNow.ToString("o")}/{file.FileName}";
                    var uploadFileResult = await _uploadService.UploadFile(companyId, fileName, file);

                    var newuUploadedFile = _mapper.Map<UploadedFile>(conversationMessage);
                    newuUploadedFile.Filename = fileName;
                    newuUploadedFile.FileSize = uploadFileResult.FileSizeInByte;
                    newuUploadedFile.BlobContainer = companyId;

                    var domainName = _configuration.GetValue<String>("Values:DomainName");
                    newuUploadedFile.Url = $"{domainName}/Message/File/Private/{newuUploadedFile.FileId}";
                    newuUploadedFile.MIMEType = file.ContentType;

                    if (newuUploadedFile.MIMEType.Contains("image"))
                    {
                        try
                        {
                            var imageSize = await ImageHelper.GetImageSizeAsync(file.OpenReadStream());

                            var dimension = new
                            {
                                width = imageSize.Width,
                                height = imageSize.Height
                            };

                            newuUploadedFile.Metadata = JsonConvert.SerializeObject(dimension);
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(
                                ex,
                                "[{MethodName}] Company {CompanyId}, Conversation {ConversationId}, File {FileName} !!! Image File Metadata Error: {ExceptionMessage} !!!",
                                nameof(SendConversationNote),
                                companyId,
                                conversationId,
                                file.FileName,
                                ex.Message);
                        }
                    }

                    conversationMessage.UploadedFiles.Add(newuUploadedFile);

                    if ((newuUploadedFile.MIMEType.Contains("audio") && !newuUploadedFile.MIMEType.Contains("mpeg") &&
                         !newuUploadedFile.MIMEType.Contains("mp3")) ||
                        Path.GetExtension(newuUploadedFile.Filename) == ".webm" ||
                        Path.GetExtension(newuUploadedFile.Filename) == ".bin")
                    {
                        await _appDbContext.SaveChangesAsync();
                        await _mediaProcessService.ProcessMedia(conversation.Id, newuUploadedFile, "mp3");
                    }
                }
            }
            else if (conversationNoteViewModel?.fileUrls != null)
            {
                var provider = new FileExtensionContentTypeProvider();
                string contentType;

                for (var i = 0; i < conversationNoteViewModel.fileUrls.Count; i++)
                {
                    var url = conversationNoteViewModel.fileUrls[i];
                    var fileName = Path.GetFileName(url);
                    if (conversationNoteViewModel.fileNames != null)
                    {
                        fileName = conversationNoteViewModel.fileNames[i];
                    }

                    if (!provider.TryGetContentType(url, out contentType))
                    {
                        contentType = "application/octet-stream";
                    }

                    fileURLMessages.Add(
                        new FileURLMessage
                        {
                            FileName = fileName,
                            FileURL = url,
                            MIMEType = contentType
                        });
                }


                await HandleMessageFileUpload(
                    companyId,
                    conversationId,
                    conversationMessage,
                    fileURLMessages,
                    conversation);
            }

            // assign the conversation to the assignee
            // conversation.Assignee = conversationMessage.MessageAssignee;
            conversation.UpdatedTime = DateTime.UtcNow;
            conversation.ModifiedAt = DateTime.UtcNow;

            conversationMessage.Visibility = "private";
            conversationMessage.Status = MessageStatus.Read;
            await _appDbContext.SaveChangesAsync();

            conversation.LastMessageId = conversationMessage.Id;
            conversation.ActiveStatus = ActiveStatus.Active;

            await _appDbContext.SaveChangesAsync();

            await RemoveCache(conversation.CompanyId, conversation.Id);


            // await SignalRPublishToSubscriberConversationStatusChanged(conversation);
            await _signalRService.SignalROnConversationNoteReceived(conversation, conversationMessage);
            await _emailNotificationService.NewNoteNotification(conversation, conversationMessage);

            await _userProfileHooks.OnMessageStatusUpdatedAsync(
                        conversation.CompanyId,
                        conversation.UserProfileId,
                        conversation,
                        conversationMessage);

            return conversationMessage;
        }

        private async Task HandleMessageFileUpload(string companyId, string conversationId,
            ConversationMessage conversationMessage, List<FileURLMessage> fileURLMessages, Conversation conversation)
        {
            foreach (var file in fileURLMessages)
            {
                UploadFileResult uploadFileResult = null;

                var fileNameAndPath = ConstructFileMessageNameAndPathForUpload(
                    file,
                    conversation.Id);

                var (fileName, filePath) = fileNameAndPath;

                // Download File Url to stream
                if (!string.IsNullOrEmpty(file.FileURL))
                {
                    var client = new RestClient(file.FileURL);
                    var request = new RestRequest(Method.GET);
                    IRestResponse response = await client.ExecuteAsync(request);

                    file.MIMEType = response.ContentType;

                    var fileStream = client.DownloadData(request);
                    var fileIn = new MemoryStream(fileStream);

                    file.FileStream = fileIn;
                    file.FileStream.Position = 0;
                    file.FileURL = null;
                }

                if (file.MIMEType == "audio/ogg" || file.MIMEType == "audio/mp4" ||
                    file.FileName.Contains("audioclip"))
                {
                    var inputFile = TempFileHelper.GetTempFileFullPath("conversation");
                    var outputFile = TempFileHelper.GetTempFileFullPath("conversation");

                    try
                    {
                        await File.WriteAllBytesAsync(inputFile, ((MemoryStream)file.FileStream).ToArray());

                        try
                        {
                            var ffMpegConverter = _mediaEncodingService.GetFfMpegConverterInstance();
                            await _mediaEncodingService.ConvertMediaByGoogleTranscoder(
                                inputFile,
                                outputFile,
                                "mp3",
                                ffMpegConverter);
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(
                                ex,
                                "[{MethodName}] !!!!! FFMPEG ERROR !!!!!! Company {CompanyId}, Conversation {ConversationId}, File name {FileName}. {ExceptionMessage}",
                                nameof(SendConversationNote),
                                companyId,
                                conversationId,
                                file.FileName,
                                ex.Message);
                        }

                        var outputStream = File.OpenRead(outputFile);
                        file.FileStream = outputStream;
                        file.MIMEType = "audio/mpeg";
                        filePath = $"{filePath}.mp3";

                        uploadFileResult = await _uploadService.UploadFileBySteam(
                            companyId,
                            filePath,
                            file.FileStream,
                            file.MIMEType);
                    }
                    finally
                    {
                        TempFileHelper.DeleteFileWithRetry(inputFile);
                        TempFileHelper.DeleteFileWithRetry(inputFile);
                    }
                }
                else
                {
                    if (string.IsNullOrEmpty(file.FileURL))
                    {
                        uploadFileResult = await _uploadService.UploadFileBySteam(
                            companyId,
                            filePath,
                            file.FileStream,
                            file.MIMEType);
                    }
                    else
                    {
                        uploadFileResult = await _uploadService.UploadFileByURL(
                            companyId,
                            filePath,
                            file.FileURL); // unreachable
                    }
                }

                if (uploadFileResult?.Url == null)
                {
                    return;
                }

                var newuUploadedFile = _mapper.Map<UploadedFile>(conversationMessage);
                newuUploadedFile.Filename = filePath;
                newuUploadedFile.FileSize = uploadFileResult.FileSizeInByte;
                newuUploadedFile.BlobContainer = companyId;

                var domainName = _configuration.GetValue<String>("Values:DomainName");
                newuUploadedFile.Url = $"{domainName}/Message/File/Private/{newuUploadedFile.FileId}";
                newuUploadedFile.MIMEType = file.MIMEType;

                conversationMessage.UploadedFiles.Add(newuUploadedFile);
            }
        }

        public async Task<ConversationMessage> SendConversationIndicatorMessage(
            Conversation conversation,
            ConversationMessage conversationMessage)
        {
            if (!conversationMessage.Metadata.ContainsKey("conversation_indicator"))
            {
                throw new Exception("Conversation indicator is missing");
            }

            await _appDbContext.ConversationMessages.AddAsync(conversationMessage);
            await _appDbContext.SaveChangesAsync();

            await RemoveCache(conversation.CompanyId, conversation.Id);

            await _signalRService.SignalROnMessageReceived(conversation, conversationMessage);

            return conversationMessage;
        }

        /*
        public async Task<IList<ConversationMessage>> SendConversationTicket(Conversation conversation, ConversationTicket conversationTicket)
        {
            _logger.LogInformation("send message");
            List<ConversationMessage> results = new List<ConversationMessage>();

            try
            {
                var conversationMessage = new ConversationMessage()
                {
                    ConversationId = conversation.Id,
                    MessageContent = $"Task: {conversationTicket.Subject}",
                    Channel = "native",
                    MessageType = "ticket",
                    Subject = conversationTicket.Subject,
                    ConversationTicket = conversationTicket
                };

                _appDbContext.ConversationMessages.Add(conversationMessage);
                conversation.UpdatedTime = DateTime.UtcNow;

                await _appDbContext.SaveChangesAsync();

                await _signalRService.SignalROnMessageReceived(conversation, conversationMessage);
                results.Add(conversationMessage);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, ex.Message);
            }

            return results;
        } */

        private async Task MessageCompletion(
            Conversation conversation,
            ConversationMessage conversationMessage,
            bool isTriggerUpdate)
        {
            try
            {
                var isCSReply = await IsCSReply(conversation, conversationMessage);

                try
                {
                    try
                    {
                        if (conversation.ActiveStatus == ActiveStatus.Inactive
                            && !(conversationMessage.Channel == ChannelTypes.LiveChat
                                 && conversationMessage.Sender == null
                                 && conversationMessage.WebClientSender == null))
                        {
                            conversation.ActiveStatus = ActiveStatus.Active;
                        }

                        if (conversation.UserProfile.ActiveStatus == ActiveStatus.Inactive
                            && conversationMessage.Channel == ChannelTypes.LiveChat
                            && conversationMessage.WebClientSender != null)
                        {
                            conversation.UserProfile.ActiveStatus = ActiveStatus.Active;
                        }

                        if (isCSReply
                            || conversationMessage.IsSentFromSleekflow)
                        {
                            conversationMessage.IsSentFromSleekflow = true;

                            if (conversationMessage.DeliveryType == DeliveryType.Normal &&
                                !string.IsNullOrEmpty(conversationMessage.SenderId))
                            {
                                // If reply the message mark is read
                                var staff = await _appDbContext.UserRoleStaffs.FirstOrDefaultAsync(
                                    x =>
                                        x.CompanyId == conversation.CompanyId
                                        && x.IdentityId == conversationMessage.SenderId);

                                await ReadConversation(conversation.Id, staff, true);
                            }
                        }
                        else if (isTriggerUpdate &&
                                 !conversationMessage.IsSentFromSleekflow &&
                                 conversationMessage.DeliveryType == DeliveryType.Normal)
                        {
                            conversation.UnreadMessageCount = conversation.UnreadMessageCount + 1;
                        }

                        await _appDbContext.SaveChangesAsync();

                        // if (!conversation.IsNewCreatedConversation)
                        // await _signalRService.SignalROnMessageReceived(conversation, conversationMessage, isCSReply);
                        // else
                        //    conversation.IsNewCreatedConversation = false;
                        if (conversation.UpdatedTime < conversationMessage.CreatedAt ||
                            !conversation.LastMessageId.HasValue)
                        {
                            conversation.UpdatedTime = conversationMessage.CreatedAt;
                            conversation.LastMessageId = conversationMessage.Id;
                            conversation.IsUnreplied = !conversationMessage.IsSentFromSleekflow;
                        }

                        conversation.ModifiedAt = DateTime.UtcNow;
                        await _appDbContext.SaveChangesAsync();

                        // await ConversationAssignment(conversation, conversationMessage, isTriggerUpdate);
                        conversation = await ChangeConversationStatus(
                            conversation,
                            conversationMessage,
                            isTriggerUpdate);

                        // if (isTriggerUpdate)
                        // {
                        //    if (!isCSReply)
                        //    {
                        //        await NewMessageAction(conversation, conversationMessage);
                        //    }
                        // }
                        await _signalRService.SignalROnMessageReceived(conversation, conversationMessage, isCSReply);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(
                            ex,
                            "MessageCompletion Error {CompanyId} {ConversationId} {MessageId}",
                            conversation.CompanyId,
                            conversation.Id,
                            conversationMessage.Id);
                    }

                    var watch = Stopwatch.StartNew();
                    await ConversationLastActivity(conversation, conversationMessage, isTriggerUpdate);
                    watch.Stop();

                    _logger.LogInformation(
                        $"[ConversationLastActivity] Preparation executed in: {watch.ElapsedMilliseconds} ms");

                    if (conversationMessage.Channel != ChannelTypes.Note ||
                        conversation.LastMessageChannel != conversationMessage.Channel)
                    {
                        conversation.LastMessageChannel = conversationMessage.Channel;
                        conversation.LastChannelIdentityId = conversationMessage.ChannelIdentityId;
                    }

                    await _appDbContext.SaveChangesAsync();
                }
                catch (Exception ex)
                {
                    _logger.LogError(
                        ex,
                        "MessageCompletion ConversationLastActivity Error {CompanyId} {ConversationId} {MessageId}",
                        conversation.CompanyId,
                        conversation.Id,
                        conversationMessage.Id);
                }

                if (isCSReply)
                {
                    await _userProfileHooks.OnMessageSentAsync(
                        conversation.CompanyId,
                        conversation.UserProfileId,
                        conversation,
                        conversationMessage);
                }
                else
                {
                    await _userProfileHooks.OnMessageReceivedAsync(
                        conversation.CompanyId,
                        conversation.UserProfileId,
                        conversation,
                        conversationMessage);
                }


                if (!isCSReply && isTriggerUpdate)
                {
                    _logger.LogInformation(
                        "Trigger new message {ConversationId}, messageId: {ConversationMessageId}",
                        conversation.Id,
                        conversationMessage.Id);

                    switch (conversation.IsNewCreatedConversation)
                    {
                        case true:
                            if (await _appDbContext.CompanyAssignmentRules
                                    .AnyAsync(
                                        x =>
                                            x.CompanyId == conversation.CompanyId
                                            && x.AutomationType == AutomationType.NewContactMessage
                                            && x.Status == AutomationStatus.Live))
                            {
                                BackgroundJob.Enqueue<IAutomationService>(
                                    x => x.NewContactMessageTrigger(conversation.Id, conversationMessage.Id, true));
                            }
                            else if (await _appDbContext.CompanyAssignmentRules
                                         .AnyAsync(
                                             x =>
                                                 x.CompanyId == conversation.CompanyId
                                                 && x.AutomationType == AutomationType.MessageReceived
                                                 && x.Status == AutomationStatus.Live))
                            {
                                BackgroundJob.Enqueue<IAutomationService>(
                                    x => x.NewMessageTrigger(conversation.Id, conversationMessage.Id, true));
                            }
                            else if (await _appDbContext.CompanyAssignmentRules
                                         .AnyAsync(
                                             x =>
                                                 x.CompanyId == conversation.CompanyId
                                                 && (x.AutomationType == AutomationType.QRCodeAssigneeMapping
                                                     || x.AutomationType == AutomationType.QRCodeAssignTeamMapping)
                                                 && x.Status == AutomationStatus.Live))
                            {
                                BackgroundJob.Enqueue<IAutomationService>(
                                    x => x.NewMessageTrigger(conversation.Id, conversationMessage.Id, true));
                            }

                            break;
                        default:
                            if (await _appDbContext.CompanyAssignmentRules
                                    .AnyAsync(
                                    x =>
                                        x.CompanyId == conversation.CompanyId
                                        && x.AutomationType == AutomationType.MessageReceived
                                        && x.Status == AutomationStatus.Live))
                            {
                                BackgroundJob.Enqueue<IAutomationService>(
                                    x => x.NewMessageTrigger(conversation.Id, conversationMessage.Id, true));
                            }
                            else if (await _appDbContext.CompanyAssignmentRules
                                        .AnyAsync(
                                            x =>
                                                x.CompanyId == conversation.CompanyId
                                                && (x.AutomationType == AutomationType.QRCodeAssigneeMapping
                                                    || x.AutomationType == AutomationType.QRCodeAssignTeamMapping)
                                                && x.Status == AutomationStatus.Live))
                            {
                                BackgroundJob.Enqueue<IAutomationService>(
                                    x => x.NewMessageTrigger(conversation.Id, conversationMessage.Id, true));
                            }

                            break;
                    }
                }
                else if (!isCSReply
                         && conversationMessage.DeliveryType == DeliveryType.Normal)
                {
                    _logger.LogInformation(
                        "Not Trigger new message {ConversationId}, messageId: {ConversationMessageId}",
                        conversation.Id,
                        conversationMessage.Id);

                    if (conversation.IsNewCreatedConversation
                        && !conversation.AssigneeId.HasValue
                        && !conversation.AssignedTeamId.HasValue)
                    {
                        switch (conversation.IsNewCreatedConversation)
                        {
                            case true:
                                if (await _appDbContext.CompanyAssignmentRules
                                        .AnyAsync(
                                            x =>
                                            x.CompanyId == conversation.CompanyId
                                            && x.AutomationType == AutomationType.NewContactMessage
                                            && x.Status == AutomationStatus.Live))
                                {
                                    BackgroundJob.Enqueue<IAutomationService>(
                                        x => x.NewContactMessageTrigger(
                                            conversation.Id,
                                            conversationMessage.Id,
                                            false));
                                }
                                else if (await _appDbContext.CompanyAssignmentRules
                                            .AnyAsync(
                                                x =>
                                                    x.CompanyId == conversation.CompanyId
                                                    && x.AutomationType == AutomationType.MessageReceived
                                                    && x.Status == AutomationStatus.Live))
                                {
                                    BackgroundJob.Enqueue<IAutomationService>(
                                        x => x.NewMessageTrigger(conversation.Id, conversationMessage.Id, false));
                                }

                                break;
                            default:
                                if (await _appDbContext.CompanyAssignmentRules
                                        .AnyAsync(
                                            x =>
                                                x.CompanyId == conversation.CompanyId
                                                && x.AutomationType == AutomationType.MessageReceived
                                                && x.Status == AutomationStatus.Live))
                                {
                                    BackgroundJob.Enqueue<IAutomationService>(
                                        x => x.NewMessageTrigger(conversation.Id, conversationMessage.Id, false));
                                }

                                break;
                        }
                    }
                }

                if (isCSReply
                    && !string.IsNullOrEmpty(conversationMessage.SenderId)
                    && (conversationMessage.DeliveryType is DeliveryType.Normal or DeliveryType.QuickReply))
                {
                    BackgroundJob.Enqueue<IAutomationService>(
                        x => x.OutgoingMessageTrigger(conversation.Id, conversationMessage.Id, true));
                }

                if (!isCSReply
                    && conversationMessage.DeliveryType == DeliveryType.Normal)
                {
                    if (!conversation.AssigneeId.HasValue
                        && !conversation.AssignedTeamId.HasValue)
                    {
                        // Default assignment rule
                        if (conversation.IsSandbox)
                        {
                            BackgroundJob.Enqueue<IAutomationService>(
                                x => x.ConversationAssignmentTrigger(conversation.Id, conversationMessage.Id));
                        }
                        else if (conversation.Status.ToLower() == "open")
                        {
                            if (await _appDbContext.CompanyAssignmentRules
                                    .CountAsync(
                                        x =>
                                            x.CompanyId == conversation.CompanyId
                                            && x.Status == AutomationStatus.Live) == 1)
                            {
                                BackgroundJob.Enqueue<IAutomationService>(
                                    x => x.ConversationAssignmentTrigger(conversation.Id, conversationMessage.Id));
                            }
                            else
                            {
                                BackgroundJob.Schedule<IAutomationService>(
                                    x => x.ConversationAssignmentTrigger(conversation.Id, conversationMessage.Id),
                                    TimeSpan.FromSeconds(10));
                            }
                        }
                    }
                }

                if (conversation.IsNewCreatedConversation)
                {
                    conversation.IsNewCreatedConversation = false;
                    await _appDbContext.SaveChangesAsync();
                }

                await RemoveCache(conversation.CompanyId, conversation.Id);

                // await ConversationAssignment(conversation, conversationMessage);
            }
            catch (DbUpdateException e)
            {
                _logger.LogError(
                    e,
                    "Complete Message Error: {CompanyId} {ConversationId} {MessageId}",
                    conversation.CompanyId,
                    conversation.Id,
                    conversationMessage.Id);
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Complete Message Error: {CompanyId} {ConversationId} {MessageId}",
                    conversation.CompanyId,
                    conversation.Id,
                    conversationMessage.Id);
            }
        }

        public async Task<IList<ConversationMessage>> SendMessage(
            Conversation conversation,
            ConversationMessage conversationMessage,
            bool triggerActivity = true,
            bool EnableChatbot = false,
            bool EnableTranslation = false)
        {
            var watch = Stopwatch.StartNew();

            _logger.LogInformation(
                "[Send Message] [Conversation:{ConversationId}] [{Conversation}] [{ConversationMessage}] [{ConversationMessageChannel}] started",
                conversation.Id,
                JsonConvert.SerializeObject(
                    conversation,
                    new JsonSerializerSettings
                    {
                        ReferenceLoopHandling = ReferenceLoopHandling.Ignore,
                        NullValueHandling = NullValueHandling.Ignore,
                    }),
                JsonConvert.SerializeObject(
                    conversationMessage,
                    new JsonSerializerSettings
                    {
                        ReferenceLoopHandling = ReferenceLoopHandling.Ignore,
                        NullValueHandling = NullValueHandling.Ignore,
                    }),
                conversationMessage.Channel);

            List<ConversationMessage> results = new List<ConversationMessage>();

            try
            {
                if (!string.IsNullOrEmpty(conversationMessage.MessageUniqueID))
                {
                    if (await _appDbContext.ConversationMessages
                            .AnyAsync(x => x.MessageUniqueID == conversationMessage.MessageUniqueID))
                    {
                        return results;
                    }
                }

                conversation = await _conversationResolver.GetConversationAsync(conversation, conversationMessage);
                conversationMessage = await GetConversationMessage(conversation, conversationMessage);

                if (conversationMessage.DeliveryType == DeliveryType.Broadcast)
                {
                    var response = await _companyUsageService.GetCompanyUsage(conversation.CompanyId);

                    if (response.billingPeriodUsages.FirstOrDefault()?.TotalMessagesSentFromSleekflow >
                        response.MaximumAutomatedMessages)
                    {
                        throw new BroadcastMessageExceedingLimitException(
                            conversation.CompanyId,
                            conversation.UserProfileId);
                    }
                }

                _appDbContext.ConversationMessages.Add(conversationMessage);
                await _appDbContext.SaveChangesAsync();

                _applicationInsightsTelemetryTracer.TraceEvent(
                    TraceEventNames.MessageStored,
                    new Dictionary<string, string>
                    {
                        {
                            "channel_type", ChannelTypes.WhatsappCloudApi
                        },
                        {
                            "direction", conversationMessage.IsSentFromSleekflow ? "sent" : "received"
                        },
                    });

                watch = Stopwatch.StartNew();

                conversationMessage = await SendOrScheduleMessage(conversation, conversationMessage);

                watch.Stop();

                _logger.LogInformation(
                    "[Send Message] [Conversation:{ConversationId}] [{Conversation}] [{ConversationMessage}] [{ConversationMessageChannel}] Channel sent executed in: {ElapsedMilliseconds} ms",
                    conversation.Id,
                    JsonConvert.SerializeObject(
                        conversation,
                        new JsonSerializerSettings
                        {
                            ReferenceLoopHandling = ReferenceLoopHandling.Ignore,
                            NullValueHandling = NullValueHandling.Ignore,
                        }),
                    JsonConvert.SerializeObject(
                        conversationMessage,
                        new JsonSerializerSettings
                        {
                            ReferenceLoopHandling = ReferenceLoopHandling.Ignore,
                            NullValueHandling = NullValueHandling.Ignore,
                        }),
                    conversationMessage.Channel,
                    watch.ElapsedMilliseconds);

                watch = Stopwatch.StartNew();
                results.Add(conversationMessage);

                await MessageCompletion(conversation, conversationMessage, triggerActivity);

                watch.Stop();

                _logger.LogInformation(
                    "[Send Message] [Conversation:{ConversationId}] [{Conversation}] [{ConversationMessage}] [{ConversationMessageChannel}] Completion executed in: {ElapsedMilliseconds} ms",
                    conversation.Id,
                    JsonConvert.SerializeObject(
                        conversation,
                        new JsonSerializerSettings
                        {
                            ReferenceLoopHandling = ReferenceLoopHandling.Ignore,
                            NullValueHandling = NullValueHandling.Ignore,
                        }),
                    JsonConvert.SerializeObject(
                        conversationMessage,
                        new JsonSerializerSettings
                        {
                            ReferenceLoopHandling = ReferenceLoopHandling.Ignore,
                            NullValueHandling = NullValueHandling.Ignore,
                        }),
                    conversationMessage.Channel,
                    watch.ElapsedMilliseconds);
            }
            catch (DbUpdateException e)
            {
                _logger.LogError(
                    e,
                    "[DbUpdateException] [Send Message Error] {CompanyId} {ConversationId} {MessageId} [{Conversation}] [{ConversationMessage}]",
                    conversation.CompanyId,
                    conversation.Id,
                    conversationMessage.Id,
                    JsonConvert.SerializeObject(
                        conversation,
                        new JsonSerializerSettings
                        {
                            ReferenceLoopHandling = ReferenceLoopHandling.Ignore,
                            NullValueHandling = NullValueHandling.Ignore,
                        }),
                    JsonConvert.SerializeObject(
                        conversationMessage,
                        new JsonSerializerSettings
                        {
                            ReferenceLoopHandling = ReferenceLoopHandling.Ignore,
                            NullValueHandling = NullValueHandling.Ignore,
                        }));

                _conversationMeters.IncrementCounter(conversationMessage.Channel, ConversationMeterOptions.ProcessMessageFailed);

                throw;
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[Exception] [Send Message Error] {CompanyId} {ConversationId} {MessageId} [{Conversation}] [{ConversationMessage}]",
                    conversation.CompanyId,
                    conversation.Id,
                    conversationMessage.Id,
                    JsonConvert.SerializeObject(
                        conversation,
                        new JsonSerializerSettings
                        {
                            ReferenceLoopHandling = ReferenceLoopHandling.Ignore,
                            NullValueHandling = NullValueHandling.Ignore,
                        }),
                    JsonConvert.SerializeObject(
                        conversationMessage,
                        new JsonSerializerSettings
                        {
                            ReferenceLoopHandling = ReferenceLoopHandling.Ignore,
                            NullValueHandling = NullValueHandling.Ignore,
                        }));

                _conversationMeters.IncrementCounter(conversationMessage.Channel, ConversationMeterOptions.ProcessMessageFailed);

                throw;
            }

            _conversationMeters.IncrementCounter(conversationMessage.Channel, ConversationMeterOptions.ProcessMessageSuccess);

            return results;
        }

        public async Task<IList<ConversationMessage>> SendFileMessage(
            Conversation conversation,
            ConversationMessage conversationMessage,
            ConversationMessageViewModel fileMessageViewModel,
            bool triggerActivity = true,
            bool EnableChatbot = false,
            bool EnableTranslation = false)
        {
            var watch = Stopwatch.StartNew();

            _logger.LogInformation(
                "[Send File Message] [Conversation:{ConversationId}] [{ConversationMessageChannel}] started",
                conversation.Id,
                conversationMessage.Channel);

            List<ConversationMessage> results = new List<ConversationMessage>();

            try
            {
                conversation = await _conversationResolver.GetConversationAsync(conversation, conversationMessage);
                conversationMessage = await GetConversationMessage(conversation, conversationMessage);

                if (conversationMessage.DeliveryType == DeliveryType.Broadcast)
                {
                    var response = await _companyUsageService.GetCompanyUsage(conversation.CompanyId);

                    if (response.billingPeriodUsages.FirstOrDefault()?.TotalMessagesSentFromSleekflow >
                        response.MaximumAutomatedMessages)
                    {
                        throw new BroadcastMessageExceedingLimitException(
                            conversation.CompanyId,
                            conversation.UserProfileId);
                    }
                }

                List<UploadedFile> uploadedFiles = new List<UploadedFile>();

                if (fileMessageViewModel.files != null)
                {
                    foreach (IFormFile file in fileMessageViewModel.files)
                    {
                        var newuUploadedFile = _mapper.Map<UploadedFile>(conversationMessage);

                        var fileName =
                            $"Conversation/{conversation.Id}/{DateTime.UtcNow.ToString("o")}/{newuUploadedFile.FileId}/{file.FileName}";

                        var uploadFileResult = await _uploadService.UploadFile(conversation.CompanyId, fileName, file);

                        if (uploadFileResult?.Url == null)
                        {
                            return null;
                        }

                        newuUploadedFile.Filename = fileName;
                        newuUploadedFile.FileSize = uploadFileResult.FileSizeInByte;
                        newuUploadedFile.BlobContainer = conversation.CompanyId;

                        var domainName = _configuration.GetValue<String>("Values:DomainName");
                        newuUploadedFile.Url = $"{domainName}/Message/File/Private/{newuUploadedFile.FileId}/{file.FileName}";
                        newuUploadedFile.MIMEType = file.ContentType;

                        if (newuUploadedFile.MIMEType.Contains("image"))
                        {
                            try
                            {
                                var imageSize = await ImageHelper.GetImageSizeAsync(file.OpenReadStream());

                                var dimension = new
                                {
                                    width = imageSize.Width,
                                    height = imageSize.Height
                                };

                                newuUploadedFile.Metadata = JsonConvert.SerializeObject(dimension);
                            }
                            catch (Exception ex)
                            {
                                _logger.LogError(
                                    ex,
                                    "[{MethodName}] Company {CompanyId}, Conversation {ConversationId}, File {FileName} !!! Image File Metadata Error: {ExceptionMessage} !!!",
                                    nameof(SendConversationNote),
                                    conversation.CompanyId,
                                    conversation.Id,
                                    file.FileName,
                                    ex.Message);
                            }
                        }

                        conversationMessage.UploadedFiles.Add(newuUploadedFile);
                    }
                }

                // conversation = ChangeConversationStatus(conversation, conversationMessage);
                _appDbContext.ConversationMessages.Add(conversationMessage);

                _applicationInsightsTelemetryTracer.TraceEvent(
                    TraceEventNames.MessageStored,
                    new Dictionary<string, string>
                    {
                        {
                            "channel_type", ChannelTypes.WhatsappCloudApi
                        },
                        {
                            "direction", conversationMessage.IsSentFromSleekflow ? "sent" : "received"
                        },
                    });

                await _appDbContext.SaveChangesAsync();

                watch.Stop();

                _logger.LogInformation(
                    "[Send File Message] [Conversation:{ConversationId}] [{ConversationMessageChannel}] Preparation executed in: {ElapsedMilliseconds} ms",
                    conversation.Id,
                    conversationMessage.Channel,
                    watch.ElapsedMilliseconds);

                watch = Stopwatch.StartNew();

                conversationMessage = await SendOrScheduleMessage(conversation, conversationMessage);

                await _appDbContext.SaveChangesAsync();

                watch.Stop();

                _logger.LogInformation(
                    "[Send File Message] [Conversation:{ConversationId}] [{ConversationMessageChannel}] Channel sent executed in: {ElapsedMilliseconds} ms",
                    conversation.Id,
                    conversationMessage.Channel,
                    watch.ElapsedMilliseconds);

                watch = Stopwatch.StartNew();

                results.Add(conversationMessage);

                await MessageCompletion(conversation, conversationMessage, triggerActivity);

                watch.Stop();

                _logger.LogInformation(
                    "[Send File Message] [Conversation:{ConversationId}] [{ConversationMessageChannel}] Completion executed in: {ElapsedMilliseconds} ms",
                    conversation.Id,
                    conversationMessage.Channel,
                    watch.ElapsedMilliseconds);
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[DbUpdateException] [Send File Message Error] {CompanyId} {ConversationId} {MessageId}",
                    conversation.CompanyId,
                    conversation.Id,
                    conversationMessage.Id);

                _conversationMeters.IncrementCounter(conversationMessage.Channel, ConversationMeterOptions.ProcessMessageFailed);

                throw;
            }

            _conversationMeters.IncrementCounter(conversationMessage.Channel, ConversationMeterOptions.ProcessMessageSuccess);

            return results;
        }

        public async Task<IList<ConversationMessage>> SendFileMessageByFBURL(
            Conversation conversation,
            ConversationMessage conversationMessage,
            List<FileURLMessage> fileURLMessages,
            bool triggerActivity = true,
            bool EnableChatbot = false,
            bool EnableTranslation = false)
        {
            var watch = Stopwatch.StartNew();

            _logger.LogInformation(
                "[Send File Message URL] [Conversation:{ConversationId}] [{Conversation}] [{ConversationMessage}] [{ConversationMessageChannel}] started ms",
                conversation.Id,
                JsonConvert.SerializeObject(
                    conversation,
                    new JsonSerializerSettings
                    {
                        ReferenceLoopHandling = ReferenceLoopHandling.Ignore,
                        NullValueHandling = NullValueHandling.Ignore,
                    }),
                JsonConvert.SerializeObject(
                    conversationMessage,
                    new JsonSerializerSettings
                    {
                        ReferenceLoopHandling = ReferenceLoopHandling.Ignore,
                        NullValueHandling = NullValueHandling.Ignore,
                    }),
                conversationMessage.Channel);

            List<ConversationMessage> results = new List<ConversationMessage>();

            try
            {
                conversation = await _conversationResolver.GetConversationAsync(conversation, conversationMessage);
                conversationMessage = await GetConversationMessage(conversation, conversationMessage);

                if (conversationMessage.DeliveryType == DeliveryType.Broadcast &&
                    !string.IsNullOrEmpty(conversationMessage.SenderId))
                {
                    var response = await _companyUsageService.GetCompanyUsage(conversation.CompanyId);

                    if (response.billingPeriodUsages.FirstOrDefault()?.TotalMessagesSentFromSleekflow >
                        response.MaximumAutomatedMessages)
                    {
                        throw new BroadcastMessageExceedingLimitException(
                            conversation.CompanyId,
                            conversation.UserProfileId);
                    }
                }

                List<UploadedFile> uploadedFiles = new List<UploadedFile>();

                foreach (FileURLMessage file in fileURLMessages)
                {
                    // var fileName = $"Conversation/{conversation.Id}/{DateTime.UtcNow:o}";
                    UploadFileResult uploadFileResult = null;
                    string fileMetadata = null;
                    var fileNameAndPath = ConstructFileMessageNameAndPathForUpload(
                        file,
                        conversation.Id);

                    var (fileName, filePath) = fileNameAndPath;

                    // Download File Url to stream
                    if (!string.IsNullOrEmpty(file.FileURL))
                    {
                        var client = new RestClient(file.FileURL);
                        var request = new RestRequest(Method.GET);
                        IRestResponse response = await client.ExecuteAsync(request);

                        if (!(conversationMessage.Channel == ChannelTypes.Instagram
                              && response.ContentType != file.MIMEType
                              && file.MIMEType == "audio/ogg"))
                        {
                            file.MIMEType = response.ContentType;
                        }

                        var fileStream = client.DownloadData(request);
                        var fileIn = new MemoryStream(fileStream);

                        file.FileStream = fileIn;
                        file.FileStream.Position = 0;
                        file.FileURL = null;
                    }

                    if (file.MIMEType.Contains("image"))
                    {
                        try
                        {
                            var imageSize = await ImageHelper.GetImageSizeAsync(file.FileStream, file.MIMEType);

                            var dimension = new
                            {
                                width = imageSize.Width,
                                height = imageSize.Height
                            };

                            fileMetadata = JsonConvert.SerializeObject(dimension);

                            file.FileStream.Position = 0;
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(
                                ex,
                                "[{MethodName}]Conversation Id: {ConversationId}, Company Id: {ConversationCompanyId}, file: {FileName}|{FileMimeType} !!! Image File Metadata Error: !!!\\n{ExMessage}",
                                nameof(SendFileMessageByFBURL),
                                conversation?.Id,
                                conversation?.CompanyId,
                                file?.FileName,
                                file?.MIMEType,
                                ex.Message);
                        }
                    }

                    if (file.MIMEType == "audio/ogg"
                        || file.MIMEType == "audio/mp4"
                        || file.MIMEType == "audio/x-m4a"
                        || (file.FileName != null && file.FileName.Contains("audioclip")))
                    {
                        var inputFile = TempFileHelper.GetTempFileFullPath("conversation");
                        var outputFile = TempFileHelper.GetTempFileFullPath("conversation");

                        try
                        {
                            await File.WriteAllBytesAsync(inputFile, ((MemoryStream)file.FileStream).ToArray());

                            try
                            {
                                var ffMpegConverter = _mediaEncodingService.GetFfMpegConverterInstance();
                                await _mediaEncodingService.ConvertMediaByGoogleTranscoder(inputFile, outputFile, "mp3", ffMpegConverter);
                            }
                            catch (Exception ex)
                            {
                                _logger.LogError(
                                    ex,
                                    "[{MethodName}] !!!!! FFMPEG ERROR !!!!!! Company {CompanyId}, Conversation {ConversationId}, File name {FileName}. {ExceptionMessage}",
                                    nameof(SendConversationNote),
                                    conversation?.CompanyId,
                                    conversation?.Id,
                                    file.FileName,
                                    ex.Message);
                            }

                            var outputStream = File.OpenRead(outputFile);
                            file.FileStream = outputStream;
                            file.MIMEType = "audio/mpeg";

                            file.FileName = Path.ChangeExtension(file.FileName, ".mp3");
                            fileNameAndPath = ConstructFileMessageNameAndPathForUpload(
                                file,
                                conversation.Id);
                            (fileName, filePath) = fileNameAndPath;

                            uploadFileResult = await _uploadService.UploadFileBySteam(
                                conversation.CompanyId,
                                filePath,
                                file.FileStream,
                                file.MIMEType);
                        }
                        finally
                        {
                            TempFileHelper.DeleteFileWithRetry(inputFile);
                            TempFileHelper.DeleteFileWithRetry(outputFile);
                        }
                    }
                    else
                    {
                        fileNameAndPath = ConstructFileMessageNameAndPathForUpload(
                            file,
                            conversation.Id);
                        (fileName, filePath) = fileNameAndPath;

                        if (string.IsNullOrEmpty(file.FileURL))
                        {
                            uploadFileResult = await _uploadService.UploadFileBySteam(
                                conversation.CompanyId,
                                filePath,
                                file.FileStream,
                                file.MIMEType);
                        }
                        else
                        {
                            uploadFileResult = await _uploadService.UploadFileByURL(
                                conversation.CompanyId,
                                filePath,
                                file.FileURL); // unreachable
                        }
                    }

                    if (uploadFileResult?.Url == null)
                    {
                        return null;
                    }

                    var newuUploadedFile = _mapper.Map<UploadedFile>(conversationMessage);
                    newuUploadedFile.Filename = filePath; // actually db is storing file path, rather than just file name
                    newuUploadedFile.FileSize = uploadFileResult.FileSizeInByte;
                    newuUploadedFile.BlobContainer = conversation.CompanyId;

                    var domainName = _configuration.GetValue<String>("Values:DomainName");
                    newuUploadedFile.Url = $"{domainName}/Message/File/Private/{newuUploadedFile.FileId}/{fileName}";
                    newuUploadedFile.MIMEType = file.MIMEType;
                    newuUploadedFile.Metadata = fileMetadata;

                    conversationMessage.UploadedFiles.Add(newuUploadedFile);
                }

                watch.Stop();

                _logger.LogInformation(
                    "[Send File Message URL] [Conversation:{ConversationId}] [{ConversationMessageChannel}] Preparation executed in: {ElapsedMilliseconds} ms",
                    conversation.Id,
                    conversationMessage.Channel,
                    watch.ElapsedMilliseconds);

                watch = Stopwatch.StartNew();

                // conversation = ChangeConversationStatus(conversation, conversationMessage);
                _appDbContext.ConversationMessages.Add(conversationMessage);
                await _appDbContext.SaveChangesAsync();

                _applicationInsightsTelemetryTracer.TraceEvent(
                    TraceEventNames.MessageStored,
                    new Dictionary<string, string>
                    {
                        {
                            "channel_type", ChannelTypes.WhatsappCloudApi
                        },
                        {
                            "direction", conversationMessage.IsSentFromSleekflow ? "sent" : "received"
                        },
                    });

                conversationMessage = await SendOrScheduleMessage(conversation, conversationMessage);

                var isMessageStatusChanged = false;
                if (conversationMessage.Status < MessageStatus.Sent
                    && !string.IsNullOrEmpty(conversationMessage.MessageUniqueID))
                {
                    conversationMessage.Status = MessageStatus.Sent;
                    isMessageStatusChanged = true;
                }

                await _appDbContext.SaveChangesAsync();

                if (isMessageStatusChanged)
                {
                    await _userProfileHooks.OnMessageStatusUpdatedAsync(
                        conversation.CompanyId,
                        conversation.UserProfileId,
                        conversation,
                        conversationMessage);
                }

                results.Add(conversationMessage);

                await MessageCompletion(conversation, conversationMessage, triggerActivity);

                watch.Stop();

                _logger.LogInformation(
                    "[Send File Message URL] [Conversation:{ConversationId}] [{ConversationMessageChannel}] Completion executed in: {ElapsedMilliseconds} ms",
                    conversation.Id,
                    conversationMessage.Channel,
                    watch.ElapsedMilliseconds);
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[DbUpdateException] [Send File URL Message Error] {CompanyId} {ConversationId} {MessageId}",
                    conversation.CompanyId,
                    conversation.Id,
                    conversationMessage.Id);

                _conversationMeters.IncrementCounter(conversationMessage.Channel, ConversationMeterOptions.ProcessMessageFailed);

                throw;
            }

            _conversationMeters.IncrementCounter(conversationMessage.Channel, ConversationMeterOptions.ProcessMessageSuccess);

            return results;
        }

        private FileMessageNameAndPath ConstructFileMessageNameAndPathForUpload(FileURLMessage fileUrlMessage, string conversationId)
        {
            // Create the base path using conversationId and current UTC timestamp
            var basePath = $"Conversation/{conversationId}/{DateTime.UtcNow:o}";

            // Determine the file name,
            // 1. fileName not empty with MimeType
            // 2. fileName not empty without MimeType (likely coming from FB IG), and then will use url to get MimeType after this function
            // 3. fileName empty with MimeType (from FB IG as well)
            string fileName;
            if (!string.IsNullOrEmpty(fileUrlMessage.FileName))
            {
                fileName = FilenameHelper.FormatFileName(fileUrlMessage.FileName, fileUrlMessage.MIMEType);
            }
            else
            {
                var extension = MimeTypeMap.GetExtension(fileUrlMessage.MIMEType, false);
                fileName = $"{Guid.NewGuid()}{extension}";
            }

            // Combine the base path and file name
            var fullPath = $"{basePath}/{fileName}";

            return new FileMessageNameAndPath(fileName, fullPath);
        }

        private async Task<ConversationMessage> SendOrScheduleMessage(
            Conversation conversation,
            ConversationMessage conversationMessage)
        {
            if (conversationMessage.ScheduleSentAt.HasValue)
            {
                // Schedule Message
                var ts = conversationMessage.ScheduleSentAt.Value - DateTime.UtcNow;

                if (ts.TotalSeconds > 0)
                {
                    var jobId = BackgroundJob.Schedule(
                        () => SendMessageToChannels(conversation.Id, conversationMessage.Id),
                        ts);
                    conversationMessage.JobId = jobId;
                    conversationMessage.Status = MessageStatus.Scheduled;
                    await _appDbContext.SaveChangesAsync();
                }
                else
                {
                    conversationMessage = await SendMessageToChannels(conversation, conversationMessage);
                }
            }
            else
            {
                // Send Message
                conversationMessage = await SendMessageToChannels(conversation, conversationMessage);
            }

            return conversationMessage;
        }

        public async Task<ConversationMessage> SendMessageToChannels(string conversationId, long conversationMessageId)
        {
            var conversation = await _appDbContext.Conversations
                .Include(x => x.UserProfile)
                .Include(x => x.ViberUser)
                .Include(x => x.TelegramUser)
                .Include(x => x.InstagramUser)
                .Include(x => x.WhatsappUser)
                .Include(x => x.facebookUser)
                .Include(x => x.WhatsappUser)
                .Include(x => x.NaiveUser.Identity)
                .Include(x => x.Assignee.Identity)
                .Include(x => x.EmailAddress)
                .Include(x => x.WebClient)
                .Include(x => x.WeChatUser)
                .Include(x => x.LineUser)
                .Include(x => x.SMSUser)
                .Include(x => x.WhatsApp360DialogUser)
                .Include(x => x.WhatsappCloudApiUser)
                .FirstOrDefaultAsync(x => x.Id == conversationId);

            // Split one-to-many query
            conversation.conversationHashtags = await _appDbContext.ConversationHashtags
                .Include(x => x.Hashtag)
                .Where(x => x.ConversationId == conversationId)
                .ToListAsync();

            conversation.AdditionalAssignees = await _appDbContext.ConversationAdditionalAssignees
                .Include(x => x.Assignee.Identity)
                .Where(x => x.ConversationId == conversationId)
                .ToListAsync();

            conversation.UserProfile.CustomFields = await _appDbContext.UserProfileCustomFields
                .Where(x => x.UserProfileId == conversation.UserProfileId)
                .ToListAsync();

            var conversationMessage = await _appDbContext.ConversationMessages
                .Include(x => x.UploadedFiles)
                .Include(x => x.EmailFrom)
                .Include(x => x.Sender)
                .Include(x => x.Receiver)
                .Include(x => x.SenderDevice)
                .Include(x => x.ReceiverDevice)
                .Include(x => x.facebookSender)
                .Include(x => x.facebookReceiver)
                .Include(x => x.whatsappSender)
                .Include(x => x.whatsappReceiver)
                .Include(x => x.Whatsapp360DialogSender)
                .Include(x => x.Whatsapp360DialogReceiver)
                .Include(x => x.WebClientSender)
                .Include(x => x.WebClientReceiver)
                .Include(x => x.MessageAssignee.Identity)
                .Include(x => x.WeChatSender)
                .Include(x => x.WeChatReceiver)
                .Include(x => x.LineSender)
                .Include(x => x.LineReceiver)
                .Include(x => x.SMSSender)
                .Include(x => x.SMSReceiver)
                .Include(x => x.InstagramSender)
                .Include(x => x.InstagramReceiver)
                .Include(x => x.Whatsapp360DialogExtendedMessagePayload)
                .Include(x => x.ExtendedMessagePayload)
                .FirstOrDefaultAsync(x => x.Id == conversationMessageId);

            if (conversationMessage.Status == MessageStatus.Scheduled)
            {
                conversationMessage = await SendMessageToChannels(conversation, conversationMessage);

                // Update Sent Time to Schedule time
                if (conversationMessage.ScheduleSentAt != null)
                {
                    conversationMessage.CreatedAt = conversationMessage.ScheduleSentAt.Value;
                    conversationMessage.UpdatedAt = conversationMessage.ScheduleSentAt.Value;
                    conversationMessage.Timestamp =
                        new DateTimeOffset(conversationMessage.ScheduleSentAt.Value).ToUnixTimeSeconds();
                    conversationMessage.FrondendTimestamp =
                        new DateTimeOffset(conversationMessage.ScheduleSentAt.Value).ToUnixTimeMilliseconds();
                }

                await _appDbContext.SaveChangesAsync();

                await _signalRService.SignalROnMessageStatusChanged(conversationMessage);

                await MessageCompletion(conversation, conversationMessage, false);
            }

            return conversationMessage;
        }

        public async Task<ConversationMessage> SendMessageToChannels(
            Conversation conversation,
            ConversationMessage conversationMessage)
        {
            var (conv, message) =
                await _messageChannelSwitcher.SwitchDefaultChannelIfRequiredAsync(
                    conversation,
                    conversationMessage);

            var channelMessageHandler = _channelMessageProvider.GetChannelMessageHandler(message.Channel);

            return await channelMessageHandler.SendChannelMessageAsync(conv, message);
        }

        public async Task<IList<ConversationMessage>> FetchFacebookPostback(Entry entry, FBMessaging messaging)
        {
            _logger.LogInformation(
                "[{MethodName}] {EntryId}, MessageId: {MessagingPostbackTitle}",
                nameof(FetchFacebookPostback),
                entry.Id,
                messaging.postback.Title);

            try
            {
                while (true)
                {
                    var myLock = await _lockService.AcquireLockAsync(
                        $"{entry.Id}_{messaging.recipient?.id}",
                        TimeSpan.FromSeconds(2));

                    if (myLock == null)
                    {
                        await Task.Delay(TimeSpan.FromSeconds(2));
                    }
                    else
                    {
                        break;
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[{MethodName}] acquire lock error: {ExceptionMessage}",
                    nameof(FetchFacebookPostback),
                    ex.Message);
            }

            List<ConversationMessage> results = new List<ConversationMessage>();

            try
            {
                var httpClient = _httpClientFactory.CreateClient(HttpClientHandlerName.Default);

                var facebookConfig = await _appDbContext.ConfigFacebookConfigs
                    .FirstOrDefaultAsync(x => x.PageId == entry.Id);


                var company = await _appDbContext.CompanyCompanies
                    .FirstOrDefaultAsync(x => x.Id == facebookConfig.CompanyId);

                var pageId = facebookConfig.PageId;
                var messengerAccessToken = facebookConfig.PageAccessToken;

                _logger.LogInformation(
                    "Fetch conversation by threadId for Facebook page {FacebookPageId}",
                    pageId);

                var fbconversationsResponse = await httpClient.GetStringAsync(
                    $"https://graph.facebook.com/{pageId}/conversations?fields=senders,updated_time&access_token={messengerAccessToken}");

                var fbconversations = JsonConvert.DeserializeObject<ConversationResult>(fbconversationsResponse);

                var threadIds = fbconversations.data
                    .Where(x => x.senders.data
                        .Select(y => y.Id)
                        .Contains(messaging.sender.id))
                    .ToList();

                if (threadIds.Count > 0)
                {
                    foreach (var fbconversation in threadIds)
                    {
                        BackgroundJob.Enqueue(
                            () => FetchByThreadId(
                                new Change
                                {
                                    Field = "conversations",
                                    Value = new Value
                                    {
                                        ThreadId = fbconversation.id,
                                        PageId = pageId
                                    }
                                },
                                true));
                    }
                }
                else
                {
                    _logger.LogWarning(
                        "[{MethodName}] Postback conversation not found for {SenderFacebookId} in {FacebookConfigPageId}",
                        nameof(FetchFacebookPostback),
                        messaging.sender.id,
                        facebookConfig.PageId);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[{MethodName}] Postback conversation error {EntryPayload} {MessagePayload}",
                    nameof(FetchFacebookPostback),
                    JsonConvert.SerializeObject(entry),
                    JsonConvert.SerializeObject(messaging));
            }

            return results;
        }

        public async Task FetchConversationsIGBackground(string pageId, int limit = 10, bool getAll = false)
        {
            InstagramConfig instagramConfig = await _appDbContext.ConfigInstagramConfigs
                .FirstOrDefaultAsync(x => x.InstagramPageId == pageId);

            if (instagramConfig == null)
            {
                return;
            }

            var response = await _companyUsageService.GetCompanyUsage(instagramConfig.CompanyId);

            if (response.billingPeriodUsages.FirstOrDefault()?.TotalMessagesSentFromSleekflow >
                response.MaximumAutomatedMessages)
            {
                return;
            }

            if (response.totalContacts > response.MaximumContacts)
            {
                return;
            }

            try
            {
                _logger.LogInformation(
                    "Instagram background fetch conversation: {InstagramConfigPageId}",
                    instagramConfig.PageId);

                if (!getAll && instagramConfig.Status == FacebookStatus.Syncing)
                {
                    return;
                }

                var company = await _appDbContext.CompanyCompanies
                    .FirstOrDefaultAsync(x => x.Id == instagramConfig.CompanyId);

                await FetchIGConversationByURL(
                    instagramConfig,
                    $"https://graph.facebook.com/{instagramConfig.PageId}/conversations?fields=participants,updated_time&limit={limit}&platform=instagram&access_token={instagramConfig.PageAccessToken}",
                    getAll);

                instagramConfig.Status = FacebookStatus.Authenticated;
                await _appDbContext.SaveChangesAsync();

                // BackgroundJob.Schedule<IConversationMessageService>(x => x.FetchConversationsIGBackground(pageId, false), TimeSpan.FromSeconds(60));
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[{MethodName}] Sync instagram error: {InstagramPageId}",
                    nameof(FetchConversationsIGBackground),
                    pageId);

                instagramConfig.Status = FacebookStatus.Authenticated;
                await _appDbContext.SaveChangesAsync();

                throw;
            }

            // facebookConfig.Status = FacebookStatus.Authenticated;
            // await _appDbContext.SaveChangesAsync();
        }

        public async Task FetchIGConversationByURL(InstagramConfig instagramConfig, string url, bool getAll = false)
        {
            var httpClient = _httpClientFactory.CreateClient(HttpClientHandlerName.Default);

            var conversationResponse = await httpClient.GetAsync(url);

            if (conversationResponse.IsSuccessStatusCode)
            {
                instagramConfig.Status = FacebookStatus.Syncing;
                await _appDbContext.SaveChangesAsync();

                var conversations =
                    JsonConvert.DeserializeObject<ConversationResult>(
                        await conversationResponse.Content.ReadAsStringAsync());

                foreach (var conversation in conversations.data)
                {
                    try
                    {
                        await IGFetchByThreadId(
                            new Change
                            {
                                Field = "conversations",
                                Value = new Value
                                {
                                    ThreadId = conversation.id,
                                    PageId = instagramConfig.InstagramPageId
                                }
                            },
                            false);

                        _logger.LogInformation(
                            "Instagram fetched contact: {InstagramConversationId} in page: {InstagramConfigPageId}",
                            conversation.id,
                            instagramConfig.PageId);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(
                            ex,
                            "[{MethodName}] Instagram fetched failed: {InstagramConversationId} in page: {InstagramConfigPageId}: Exception: {ExceptionString}",
                            nameof(FetchIGConversationByURL),
                            conversation.id,
                            instagramConfig.PageId,
                            ex.ToString());

                        instagramConfig.Status = FacebookStatus.Authenticated;
                        await _appDbContext.SaveChangesAsync();

                        throw;
                    }

                    await Task.Delay(5000);
                }

                if (getAll)
                {
                    try
                    {
                        if (conversations.paging?.next != null)
                        {
                            _logger.LogInformation(
                                "Instagram {InstagramConfigPageId} fetched next page",
                                instagramConfig.PageId);

                            await FetchIGConversationByURL(instagramConfig, conversations.paging?.next, getAll);
                        }
                        else
                        {
                            instagramConfig.Status = FacebookStatus.Authenticated;
                            await _appDbContext.SaveChangesAsync();

                            return;
                        }
                    }
                    catch (Exception ex)
                    {
                        instagramConfig.Status = FacebookStatus.Authenticated;
                        await _appDbContext.SaveChangesAsync();

                        _logger.LogError(
                            ex,
                            "[{MethodName}] Instagram {InstagramConfigPageId} fetch next page error: {ExceptionString}",
                            nameof(FetchIGConversationByURL),
                            instagramConfig.PageId,
                            ex.ToString());

                        return;
                    }
                }

                instagramConfig.Status = FacebookStatus.Authenticated;
                await _appDbContext.SaveChangesAsync();
            }
        }

        public async Task<IList<ConversationMessage>> FetchInstagramMessage(Entry entry, FBMessaging messaging)
        {
            var results = new List<ConversationMessage>();

            var instagramConfig = await _appDbContext.ConfigInstagramConfigs
                .FirstOrDefaultAsync(x => x.InstagramPageId == entry.Id);

            if (instagramConfig == null)
            {
                return results;
            }

            var instagramPageId = entry.Id;
            string instagramId;

            var isSentFromSleekflowPage = entry.Id == messaging.sender.id;

            if (isSentFromSleekflowPage)
            {
                instagramId = messaging.recipient.id;
            }
            else
            {
                instagramId = messaging.sender.id;
            }

            var isInitialMessage = false;
            // handle the profile information first
            if (!await _appDbContext.SenderInstagramSenders.AnyAsync(
                    x => x.CompanyId == instagramConfig.CompanyId &&
                         x.InstagramId == instagramId &&
                         x.InstagramPageId == instagramConfig.InstagramPageId &&
                         x.PageId == instagramConfig.PageId))
            {
                isInitialMessage = true;
                await _appDbContext.SenderInstagramSenders.AddAsync(
                    new InstagramSender()
                    {
                        CompanyId = instagramConfig.CompanyId,
                        InstagramPageId = instagramConfig.InstagramPageId,
                        PageId = instagramConfig.PageId,
                        InstagramId = instagramId
                    });
                await _appDbContext.SaveChangesAsync();
            }

            _logger.LogInformation(
                "[{MethodName}] {EntryId}, MessageId: {MessageMid}",
                nameof(FetchInstagramMessage),
                entry.Id,
                messaging.message.mid);

            try
            {

                var pageId = instagramConfig.PageId;
                var messengerAccessToken = instagramConfig.PageAccessToken;

                if (await _appDbContext.ConversationMessages
                        .AnyAsync(x => x.MessageUniqueID == messaging.message.mid))
                {
                    return results;
                }

                var (conversation, myLock) = await _conversationResolver.GetConversationForReceivingMessageAsync(
                    instagramConfig.CompanyId,
                    ChannelTypes.Instagram,
                    instagramPageId,
                    instagramId,
                    null);

                await IGSaveMessage(entry.Id, messaging, conversation, myLock, true);
                await IGFetchByMessageId(entry.Id, messaging.message.mid, true);

                // Update IG profile
                if (await _appDbContext.SenderInstagramSenders.AnyAsync(
                        x => x.CompanyId == instagramConfig.CompanyId &&
                             x.InstagramId == instagramId &&
                             x.InstagramPageId == instagramConfig.InstagramPageId &&
                             x.PageId == instagramConfig.PageId &&
                             x.Name == null))
                    await IGFetchFacebookUserProfile(
                        instagramConfig.CompanyId,
                        await _appDbContext.SenderInstagramSenders.FirstOrDefaultAsync(
                            x => x.CompanyId == instagramConfig.CompanyId &&
                                 x.InstagramId == instagramId &&
                                 x.InstagramPageId == instagramConfig.InstagramPageId &&
                                 x.PageId == instagramConfig.PageId &&
                                 x.Name == null),
                        instagramConfig.PageAccessToken);

                if (isInitialMessage)
                {
                    _logger.LogInformation(
                        "Fetch instagram conversation by threadId for {InstagramConfigPageId}",
                        pageId);

                    var httpClient = _httpClientFactory.CreateClient(HttpClientHandlerName.Default);

                    var igConversationsResponse = await httpClient.GetStringAsync(
                        $"https://graph.facebook.com/{pageId}/conversations?fields=participants,updated_time&access_token={messengerAccessToken}&limit=50&platform=instagram");

                    var igConversations =
                        JsonConvert.DeserializeObject<InstagramConversationResult>(igConversationsResponse);

                    foreach (var fbconversation in igConversations.Data
                                 .Where(x => x.Participants.Data
                                     .Select(y => y.Id)
                                     .Contains(messaging.sender.id)))
                    {
                        BackgroundJob.Enqueue(
                            () => IGFetchByThreadId(
                                new Change
                                {
                                    Field = "conversations",
                                    Value = new Value
                                    {
                                        ThreadId = fbconversation.Id,
                                        PageId = instagramPageId
                                    }
                                },
                                true));
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[{MethodName}] Error Entry: {EntryPayload}: {ExceptionString}",
                    nameof(FetchInstagramMessage),
                    JsonConvert.SerializeObject(entry),
                    ex.ToString());
            }

            return results;
        }

        public async Task<IList<ConversationMessage>> FetchFacebookMessage(Entry entry, FBMessaging messaging)
        {
            var results = new List<ConversationMessage>();
            var facebookConfig = await _appDbContext.ConfigFacebookConfigs
                .FirstOrDefaultAsync(x => x.PageId == entry.Id);

            if (facebookConfig == null)
            {
                return results;
            }

            var pageId = entry.Id;
            string facebookId;

            var isSentFromSleekflowPage = entry.Id == messaging.sender.id;

            if (isSentFromSleekflowPage)
            {
                facebookId = messaging.recipient.id;
            }
            else
            {
                facebookId = messaging.sender.id;
            }

            _logger.LogInformation(
                "[{MethodName}] {EntryId}, MessageId: {MessageMid}",
                nameof(FetchFacebookMessage),
                entry.Id,
                messaging.message.mid);

            try
            {
                var messengerAccessToken = facebookConfig.PageAccessToken;

                var existingMessage = await _appDbContext.ConversationMessages
                    .FirstOrDefaultAsync(x => x.MessageUniqueID == messaging.message.mid);
                if (existingMessage != null)
                {
                    if (existingMessage.IsSentFromSleekflow
                        && existingMessage.Status == MessageStatus.Sent)
                    {
                        existingMessage.Status = MessageStatus.Received;
                        await _appDbContext.SaveChangesAsync();
                    }

                    return results;
                }

                var existingFbSender = await _appDbContext.SenderFacebookSenders
                    .AnyAsync(
                        x =>
                            x.FacebookId == messaging.sender.id
                            && x.CompanyId == facebookConfig.CompanyId
                            && x.pageId == pageId);

                var (conversation, myLock) = await _conversationResolver.GetConversationForReceivingMessageAsync(
                    facebookConfig.CompanyId,
                    ChannelTypes.Facebook,
                    pageId,
                    facebookId,
                    null);

                await FBSaveMessage(entry.Id, messaging, conversation, myLock, true);
                await FetchByMessageId(entry.Id, messaging.message.mid, true);

                // var fetchRecord = _appDbContext.FacebookFetchedIds.Any(x => x.ThreadId == $"{messaging.sender.id}{pageId}").Count();
                if (!existingFbSender) // || fetchRecord == 0)
                {
                    var httpClient = _httpClientFactory.CreateClient(HttpClientHandlerName.Default);

                    var fbconversationsResponse = await httpClient.GetStringAsync(
                        $"https://graph.facebook.com/{pageId}/conversations?fields=senders,updated_time&access_token={messengerAccessToken}");
                    var fbconversations = JsonConvert.DeserializeObject<ConversationResult>(fbconversationsResponse);

                    foreach (var fbconversation in fbconversations.data
                                 .Where(x => x.senders.data
                                     .Select(y => y.Id)
                                     .Contains(messaging.sender.id)))
                    {
                        BackgroundJob.Enqueue(
                            () => FetchByThreadId(
                                new Change
                                {
                                    Field = "conversations",
                                    Value = new Value
                                    {
                                        ThreadId = fbconversation.id,
                                        PageId = pageId
                                    }
                                },
                                true));
                    }
                }

                // await _appDbContext.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[{MethodName}] Error Entry: {EntryPayload}: {ExceptionString}",
                    nameof(FetchFacebookMessage),
                    JsonConvert.SerializeObject(entry),
                    ex.ToString());
            }

            return results;
        }

        public async Task FetchConversationsBackground(string pageId, int limit = 10, bool getAll = false)
        {
            FacebookConfig facebookConfig = await _appDbContext.ConfigFacebookConfigs
                .FirstOrDefaultAsync(x => x.PageId == pageId);

            if (facebookConfig == null)
            {
                return;
            }

            var response = await _companyUsageService.GetCompanyUsage(facebookConfig.CompanyId);

            if (response.billingPeriodUsages.FirstOrDefault()?.TotalMessagesSentFromSleekflow >
                response.MaximumAutomatedMessages)
            {
                return;
            }

            if (response.totalContacts > response.MaximumContacts)
            {
                return;
            }

            try
            {
                _logger.LogInformation(
                    "Facebook background fetch conversations: {FacebookConfigPageId}",
                    facebookConfig.PageId);

                if (!getAll
                    && facebookConfig.Status == FacebookStatus.Syncing)
                {
                    return;
                }

                // var limit = 100;
                await FetchConversationByURL(
                    facebookConfig,
                    $"https://graph.facebook.com/{facebookConfig.PageId}/conversations?fields=senders,updated_time&access_token={facebookConfig.PageAccessToken}&limit={limit}",
                    getAll);

                facebookConfig.Status = FacebookStatus.Authenticated;
                await _appDbContext.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Sync Facebook error for {FacebookConfigPageId}: {ExceptionMessage}",
                    facebookConfig.PageId,
                    ex.Message);

                facebookConfig.Status = FacebookStatus.Authenticated;
                await _appDbContext.SaveChangesAsync();

                throw;
            }
        }

        private async Task<FacebookSender> FetchFacebookUserProfile(
            string companyId,
            FacebookSender fbSender,
            string pageId,
            string token)
        {
            try
            {
                if (string.IsNullOrEmpty(fbSender.profile_pic)
                    || fbSender.profile_pic.Contains("https://platform-lookaside.fbsbx.com"))
                {
                    var httpClient = _httpClientFactory.CreateClient(HttpClientHandlerName.Default);

                    var profileDataResponse = await httpClient.GetStringAsync(
                        $"https://graph.facebook.com/{fbSender.FacebookId}?access_token={token}&fields=profile_pic,gender,locale,first_name,last_name");
                    var profileData = JsonConvert.DeserializeObject<FacebookUserProfile>(profileDataResponse);

                    var newuUploadedFile = await _userProfileService.UploadSocialProfilePicture(
                        companyId,
                        ChannelTypes.Facebook,
                        fbSender.FacebookId,
                        profileData.profile_pic);

                    if (newuUploadedFile == null)
                    {
                        _logger.LogWarning(
                            "Facebook profile pic upload fail for {SenderFacebookId}",
                            fbSender.FacebookId);
                    }
                    else
                    {
                        _appDbContext.UserProfilePictureFiles.Add(newuUploadedFile);

                        var domainName = _configuration.GetValue<String>("Values:DomainName");
                        fbSender.profile_pic = $"{domainName}/userprofile/picture/{newuUploadedFile.ProfilePictureId}";
                    }

                    fbSender.first_name = profileData.first_name;
                    fbSender.last_name = profileData.last_name;
                    fbSender.locale = profileData.locale;
                    fbSender.gender = profileData.gender;
                    fbSender.pageId = pageId;

                    await _appDbContext.SaveChangesAsync();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Fetch Facebook Profile Picture error for {SenderFacebookId}: {ExceptionMessage}",
                    fbSender.FacebookId,
                    ex.Message);
            }

            return fbSender;
        }

        /// <summary>
        /// User profile will only be updated when profile picture, name or username is null or empty, or name is 'No Name'.
        /// </summary>
        /// <remarks>
        /// Reduce roundtrips/frequent fetching with the conditions because fetching occurs for every messages.
        /// </remarks>
        /// <param name="companyId">Company ID.</param>
        /// <param name="igSender">Instagram sender.</param>
        /// <param name="token">Page access token.</param>
        private async Task<InstagramSender> IGFetchFacebookUserProfile(
            string companyId,
            InstagramSender igSender,
            string token)
        {
            try
            {
                if (string.IsNullOrEmpty(igSender.ProfilePic)
                    || string.IsNullOrEmpty(igSender.Username)
                    || string.IsNullOrEmpty(igSender.Name)
                    || igSender.Name == "No Name"
                    || igSender.ProfilePic.Contains("https://scontent-hkg4-1.xx.fbcdn.net"))
                {
                    var httpClient = _httpClientFactory.CreateClient(HttpClientHandlerName.Default);

                    var profileDataResponse = await httpClient.GetStringAsync(
                        $"https://graph.facebook.com/{igSender.InstagramId}?access_token={token}&fields=name,profile_pic,username");
                    var profileData = JsonConvert.DeserializeObject<IGProfile>(profileDataResponse);

                    if (!string.IsNullOrEmpty(profileData?.ProfilePic))
                    {
                        var newUploadedFile = await _userProfileService.UploadSocialProfilePicture(
                            companyId,
                            ChannelTypes.Instagram,
                            igSender.InstagramId,
                            profileData.ProfilePic);

                        if (newUploadedFile == null)
                        {
                            _logger.LogWarning(
                                "Instagram profile pic upload fail for {SenderInstagramId}",
                                igSender.InstagramId);
                        }
                        else
                        {
                            _appDbContext.UserProfilePictureFiles.Add(newUploadedFile);

                            var domainName = _configuration.GetValue<string>("Values:DomainName");
                            igSender.ProfilePic = $"{domainName}/userprofile/picture/{newUploadedFile.ProfilePictureId}";
                        }
                    }

                    igSender.Name = string.IsNullOrEmpty(profileData?.Name) ? "No Name" : profileData.Name;
                    igSender.Username = profileData?.Username;

                    var userProfileName = string.IsNullOrEmpty(igSender.Name) || igSender.Name == "No Name"
                        ? igSender.Username
                        : igSender.Name;

                    await _appDbContext.UserProfiles.Where(x => x.CompanyId == igSender.CompanyId &&
                                                                x.InstagramUserId == igSender.Id &&
                                                                string.IsNullOrEmpty(x.FirstName))
                        .ExecuteUpdateAsync(
                            key =>
                                key
                                    .SetProperty(k => k.FirstName, userProfileName)
                                    .SetProperty(k => k.FullName, userProfileName));

                    await _appDbContext.SaveChangesAsync();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "{CompanyId} Fetch Instagram Profile Picture error for {SenderFacebookId}: {ExceptionMessage}",
                    companyId,
                    igSender.InstagramId,
                    ex.Message);
            }

            return igSender;
        }

        public async Task FetchConversationByURL(FacebookConfig facebookConfig, string url, bool getAll = false)
        {
            var httpClient = _httpClientFactory.CreateClient(HttpClientHandlerName.Default);

            var conversationResponse = await httpClient.GetAsync(url);

            if (conversationResponse.IsSuccessStatusCode)
            {
                facebookConfig.Status = FacebookStatus.Syncing;
                await _appDbContext.SaveChangesAsync();

                var conversations =
                    JsonConvert.DeserializeObject<ConversationResult>(
                        await conversationResponse.Content.ReadAsStringAsync());

                foreach (var conversation in conversations.data)
                {
                    if (!await _appDbContext.FacebookFetchedIds
                            .AnyAsync(
                                x =>
                                    x.ThreadId == $"{conversation.id}{facebookConfig.PageId}")
                                    || getAll)
                    {
                        if (!await _appDbContext.FacebookFetchedIds
                                .AnyAsync(x => x.ThreadId == $"{conversation.id}{facebookConfig.PageId}"))
                        {
                            _appDbContext.FacebookFetchedIds.Add(
                                new FetchedThread
                                {
                                    ThreadId = $"{conversation.id}{facebookConfig.PageId}"
                                });

                            await _appDbContext.SaveChangesAsync();
                        }

                        try
                        {
                            await FetchByThreadId(
                                new Change
                                {
                                    Field = "conversations",
                                    Value = new Value
                                    {
                                        ThreadId = conversation.id,
                                        PageId = facebookConfig.PageId
                                    }
                                },
                                false);

                            _logger.LogInformation(
                                "Facebook fetched conversation: {FacebookConversationId} in page: {FacebookConfigPageId}",
                                conversation.id,
                                facebookConfig.PageId);
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(
                                ex,
                                "Facebook fetched conversation failure: {FacebookConversationId} in page: {FacebookConfigPageId}: Exception: {ExceptionString}",
                                conversation.id,
                                facebookConfig.PageId,
                                ex.ToString());

                            facebookConfig.Status = FacebookStatus.Authenticated;
                            await _appDbContext.SaveChangesAsync();

                            throw;
                        }

                        // Thread.Sleep(5000);
                        await Task.Delay(5000);
                    }
                }

                if (getAll)
                {
                    try
                    {
                        if (conversations.paging?.next != null)
                        {
                            _logger.LogInformation(
                                "Fetch next page Facebook conversation for page {FacebookConfigPageId}",
                                facebookConfig?.PageId);

                            await FetchConversationByURL(facebookConfig, conversations.paging?.next, getAll);
                        }
                        else
                        {
                            facebookConfig.Status = FacebookStatus.Authenticated;
                            await _appDbContext.SaveChangesAsync();

                            return;
                        }
                    }
                    catch (Exception ex)
                    {
                        facebookConfig.Status = FacebookStatus.Authenticated;
                        await _appDbContext.SaveChangesAsync();

                        _logger.LogError(
                            ex,
                            "[Facebook {MethodName}] Page id {FacebookPageId} error fetching conversation next page. {ExceptionString}",
                            nameof(FetchConversationByURL),
                            facebookConfig.PageId,
                            ex.ToString());

                        return;
                    }
                }

                facebookConfig.Status = FacebookStatus.Authenticated;
                await _appDbContext.SaveChangesAsync();
            }
        }

        public async Task IGFetchByThreadId(Change change, bool trigger = true)
        {
            try
            {
                _logger.LogInformation(
                    "[Instagram] Fetching conversation by Id, {ChangeValueThreadId}",
                    change.Value.ThreadId);

                List<ConversationMessage> results = new List<ConversationMessage>();

                var instagramConfig = await _appDbContext.ConfigInstagramConfigs
                    .FirstOrDefaultAsync(x => x.InstagramPageId == change.Value.PageId);

                var messengerAccessToken = instagramConfig.PageAccessToken;

                var httpClient = _httpClientFactory.CreateClient(HttpClientHandlerName.Default);

                var messagesResponse = await httpClient.GetStringAsync(
                    $"https://graph.facebook.com/{change.Value.ThreadId}?fields=messages,participants&access_token={messengerAccessToken}");
                var messageIds = JsonConvert.DeserializeObject<IGThreadResult>(messagesResponse);

                var messagesData = messageIds.Messages.Data.Take(10);

                if (trigger)
                {
                    await IGFetchByMessageId(
                        change.Value.PageId,
                        messagesData.LastOrDefault().Id,
                        trigger);

                    trigger = false;
                }

                foreach (var messageId in messagesData)
                {
                    if (await _appDbContext.ConversationMessages
                            .AnyAsync(x => x.MessageUniqueID == messageId.Id))
                    {
                        continue;
                    }

                    await IGFetchByMessageId(change.Value.PageId, messageId.Id, trigger);
                    trigger = false;

                    // Thread.Sleep(3000);
                    await Task.Delay(3000);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[Instagram {MethodName}] Page id {InstagramPageId} error fetching conversation by Id: {ExceptionMessage}",
                    nameof(IGFetchByMessageId),
                    change.Value.PageId,
                    ex.Message);
            }
        }

        public async Task FetchByThreadId(Change change, bool trigger = true)
        {
            try
            {
                _logger.LogInformation(
                    "[Facebook] Fetching conversation by Id, {ChangeValueThreadId}",
                    change.Value.ThreadId);

                List<ConversationMessage> results = new List<ConversationMessage>();

                var facebookConfig = await _appDbContext.ConfigFacebookConfigs
                    .FirstOrDefaultAsync(x => x.PageId == change.Value.PageId);

                var messengerAccessToken = facebookConfig.PageAccessToken;

                var httpClient = _httpClientFactory.CreateClient(HttpClientHandlerName.Default);

                var MessagesResponse = await httpClient.GetStringAsync(
                    $"https://graph.facebook.com/{change.Value.ThreadId}?fields=messages&access_token={messengerAccessToken}");
                var MessageIds = JsonConvert.DeserializeObject<ConversationMesasgeIds>(MessagesResponse);

                var messagesData = MessageIds.messages.data.Take(10);

                if (trigger)
                {
                    await FetchByMessageId(
                        change.Value.PageId,
                        messagesData.LastOrDefault().id,
                        trigger);

                    trigger = false;
                }

                foreach (var messageId in messagesData)
                {
                    if (await _appDbContext.ConversationMessages
                            .AnyAsync(x => x.MessageUniqueID == messageId.id))
                    {
                        continue;
                    }

                    await FetchByMessageId(change.Value.PageId, messageId.id, trigger);
                    trigger = false;

                    // Thread.Sleep(5000);
                    await Task.Delay(5000);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[Facebook {MethodName}] Page id {FacebookPageId} error Fetching conversation by Id, {ExceptionMessage}",
                    nameof(FetchByThreadId),
                    change.Value.PageId,
                    ex.Message);
            }
        }

        public async Task FetchByMessageId(
            string pageId,
            string messageId,
            bool trigger = true,
            int attempts = 3,
            Conversation conversation = null)
        {
            if (attempts < 1)
            {
                _logger.LogError(
                    "[{MethodName}] Page id {FacebookPageId} unable to add this message: {MessageId}",
                    nameof(FetchByMessageId),
                    pageId,
                    messageId);

                return;
            }

            _logger.LogInformation(
                "[{MethodName}] [Facebook] Page ID: {PageId}, Message ID: {MessageId}",
                nameof(FetchByMessageId),
                pageId,
                messageId);

            List<ConversationMessage> results = new List<ConversationMessage>();

            try
            {
                var existingMessage = await _appDbContext.ConversationMessages
                    .FirstOrDefaultAsync(x => x.MessageUniqueID == messageId);
                if (existingMessage != null)
                {
                    if (existingMessage.IsSentFromSleekflow
                        && existingMessage.Status == MessageStatus.Sent)
                    {
                        existingMessage.Status = MessageStatus.Received;
                        await _appDbContext.SaveChangesAsync();
                    }

                    return;
                }

                var httpClient = _httpClientFactory.CreateClient(HttpClientHandlerName.Default);
                var facebookConfig = await _appDbContext.ConfigFacebookConfigs
                    .FirstOrDefaultAsync(x => x.PageId == pageId);


                var company = await _appDbContext.CompanyCompanies
                    .FirstOrDefaultAsync(x => x.Id == facebookConfig.CompanyId);

                var messengerAccessToken = facebookConfig.PageAccessToken;

                var messengerMessageResponse = await httpClient.GetStringAsync(
                    $"https://graph.facebook.com/{messageId}?fields=message,from,to,created_time&access_token={messengerAccessToken}");
                var messengerMessage = JsonConvert.DeserializeObject<MessengerMessage>(messengerMessageResponse);

                var fbSender = await _appDbContext.SenderFacebookSenders
                    .FirstOrDefaultAsync(
                        x =>
                            x.FacebookId == messengerMessage.from.Id
                            && x.CompanyId == facebookConfig.CompanyId
                            && x.pageId == pageId);

                var fbReceiver = await _appDbContext.SenderFacebookSenders
                    .FirstOrDefaultAsync(
                        x =>
                            x.FacebookId == messengerMessage.to.data.FirstOrDefault().Id
                            && x.CompanyId == facebookConfig.CompanyId
                            && x.pageId == pageId);

                if (fbSender == null)
                {
                    fbSender = _mapper.Map<FacebookSender>(messengerMessage.from);
                    fbSender.CompanyId = facebookConfig.CompanyId;
                }

                if (fbReceiver == null)
                {
                    fbReceiver = _mapper.Map<FacebookSender>(messengerMessage.to.data.FirstOrDefault());
                    fbReceiver.CompanyId = facebookConfig.CompanyId;
                }

                fbSender.pageId = pageId;
                fbReceiver.pageId = pageId;
                var isSentFromSleekflow = messengerMessage.from.Id == pageId;

                if (trigger
                    && fbSender.first_name == "Guest")
                {
                    var fbconversationsResponse = await httpClient.GetStringAsync(
                        $"https://graph.facebook.com/{pageId}/conversations?fields=senders,updated_time&access_token={messengerAccessToken}");
                    var fbconversations = JsonConvert.DeserializeObject<ConversationResult>(fbconversationsResponse);

                    foreach (var fbconversation in fbconversations.data
                                 .Where(x => x.senders.data
                                     .Select(y => y.Id)
                                     .Contains(messengerMessage.from.Id)))
                    {
                        BackgroundJob.Enqueue(
                            () => FetchByThreadId(
                                new Change
                                {
                                    Field = "conversations",
                                    Value = new Value
                                    {
                                        ThreadId = fbconversation.id,
                                        PageId = pageId
                                    }
                                },
                                false));
                    }
                }

                var conversationMessage = new ConversationMessage()
                {
                    CompanyId = company.Id,
                    MessageContent = messengerMessage.message,
                    Channel = ChannelTypes.Facebook,
                    facebookSender = fbSender,
                    facebookReceiver = fbReceiver,
                    MessageType = "text",
                    MessageUniqueID = messengerMessage.id,
                    CreatedAt = messengerMessage.created_time.ToUniversalTime(),
                    UpdatedAt = messengerMessage.created_time.ToUniversalTime(),
                    Status = isSentFromSleekflow ? MessageStatus.Sent : MessageStatus.Received,
                };

                conversation ??= new Conversation
                {
                    MessageGroupName = company.SignalRGroupName,
                    CompanyId = company.Id,
                    facebookUser = (conversationMessage.facebookSender?.FacebookId != facebookConfig.PageId)
                        ? conversationMessage.facebookSender
                        : conversationMessage.facebookReceiver,
                    ActiveStatus = ActiveStatus.Active
                };

                var now = DateTime.UtcNow;
                var t = now.Subtract(conversationMessage.CreatedAt);
                trigger = t.TotalMinutes < 5;

                AttachmentMessage attachementData = null;

                try
                {
                    var attachementDataResponse = await httpClient.GetStringAsync(
                        $"https://graph.facebook.com/{messageId}/attachments?access_token={messengerAccessToken}");
                    attachementData = JsonConvert.DeserializeObject<AttachmentMessage>(attachementDataResponse);
                }
                catch (Exception ex)
                {
                    _logger.LogError(
                        ex,
                        "[{MethodName}] Error when fetching attachments in message {MessageId}: {ExceptionMessage}",
                        nameof(FetchByMessageId),
                        messageId,
                        ex.Message);
                }

                Shares shareData = null;

                try
                {
                    var shareDataResponse = await httpClient.GetStringAsync(
                        $"https://graph.facebook.com/{messageId}/shares?access_token={messengerAccessToken}");
                    shareData = JsonConvert.DeserializeObject<Shares>(shareDataResponse);
                }
                catch (Exception ex)
                {
                    _logger.LogError(
                        ex,
                        "[{MethodName}] Page id {FacebookPageId} Error when fetching shares for message {MessageId}: {ExceptionMessage}",
                        nameof(FetchByMessageId),
                        pageId,
                        messageId,
                        ex.Message);
                }

                if (await _appDbContext.ConversationMessages
                        .AnyAsync(x => x.MessageUniqueID == messageId))
                {
                    return;
                }

                if (attachementData.data?.Count > 0)
                {
                    var files = new List<FileURLMessage>();
                    conversationMessage.MessageType = "file";

                    foreach (var attachment in attachementData.data)
                    {
                        string fileUrl = string.Empty;

                        // File Message
                        switch (attachment.mime_type)
                        {
                            case "image/jpeg":
                                fileUrl = attachment.image_data.url;

                                break;
                            case "video/mp4":
                                fileUrl = attachment.video_data.url;

                                break;
                            default:
                                fileUrl = attachment.file_url;

                                break;
                        }

                        files.Add(
                            new FileURLMessage()
                            {
                                FileName = attachment.name,
                                FileURL = fileUrl,
                                MIMEType = attachment.mime_type
                            });
                    }

                    var result = await SendFileMessageByFBURL(conversation, conversationMessage, files, trigger);
                    results.AddRange(result);
                }
                else if (shareData?.Data?.Count > 0)
                {
                    var files = new List<FileURLMessage>();
                    conversationMessage.MessageType = "file";

                    foreach (var share in shareData.Data)
                    {
                        try
                        {
                            foreach (var templete in share?.Template?.Payload?.Product?.Elements?.Data)
                            {
                                string fileUrl = templete.ImageUrl.ToString();

                                files.Add(
                                    new FileURLMessage()
                                    {
                                        FileName = $"{templete.Name} ${templete.Price}",
                                        FileURL = fileUrl,
                                        MIMEType = "image/jpeg"
                                    });
                            }
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(
                                ex,
                                "[Facebook {MethodName}] Page id {FacebookPageId} Fetch product template error for message id {MessageId}. {ExceptionMessage}",
                                nameof(FetchByMessageId),
                                pageId,
                                messageId,
                                ex.Message);

                            conversationMessage.MessageType = "text";
                        }
                    }

                    var result = await SendFileMessageByFBURL(conversation, conversationMessage, files, trigger);
                    results.AddRange(result);
                }
                else
                {
                    if (string.IsNullOrEmpty(conversationMessage.MessageContent))
                    {
                        conversationMessage.MessageContent = "<Unsupported Message Type>";
                    }

                    var result = await SendMessage(conversation, conversationMessage, trigger, false, false);
                    results.AddRange(result);
                }

                if (conversationMessage.facebookSender?.FacebookId != pageId)
                {
                    await FetchFacebookUserProfile(
                        company.Id,
                        conversationMessage.facebookSender,
                        pageId,
                        messengerAccessToken);
                }

                if (conversationMessage.facebookReceiver?.FacebookId != pageId)
                {
                    await FetchFacebookUserProfile(
                        company.Id,
                        conversationMessage.facebookReceiver,
                        pageId,
                        messengerAccessToken);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[Facebook {MethodName}] Page id {FacebookPageId} error: {ExceptionToString}",
                    nameof(FetchByMessageId),
                    pageId,
                    ex.ToString());

                if (ex.Message == "Unable to get conversation")
                {
                    // Thread.Sleep(3000);
                    await Task.Delay(3000);

                    BackgroundJob.Enqueue(
                        () => FetchByMessageId(pageId, messageId, trigger, attempts - 1, conversation));
                }
            }

            // return results;
        }

        public async Task IGFetchByMessageId(string pageId, string messageId, bool trigger = true, int attempts = 3)
        {
            if (attempts < 1)
            {
                _logger.LogError(
                    "[Instagram {MethodName}] Page id {InstagramPageId} Unable to add this message: {MessageId}",
                    nameof(IGFetchByMessageId),
                    pageId,
                    messageId);

                return;
            }

            _logger.LogInformation(
                "[Instagram {MethodName}] [Instagram] Page ID: {PageId}, Message ID: {MessageId}",
                nameof(IGFetchByMessageId),
                pageId,
                messageId);

            List<ConversationMessage> results = new List<ConversationMessage>();

            try
            {
                if (await _appDbContext.ConversationMessages
                        .AnyAsync(x => x.MessageUniqueID == messageId))
                {
                    return;
                }

                var httpClient = _httpClientFactory.CreateClient(HttpClientHandlerName.Default);

                var instagramConfig = await _appDbContext.ConfigInstagramConfigs.FirstOrDefaultAsync(x => x.InstagramPageId == pageId);


                var company = await _appDbContext.CompanyCompanies.FirstOrDefaultAsync(x => x.Id == instagramConfig.CompanyId);

                var messengerAccessToken = instagramConfig.PageAccessToken;

                var MessengerMessageResponse = await httpClient.GetStringAsync(
                    $"https://graph.facebook.com/{messageId}?fields=message,from,to,created_time&access_token={messengerAccessToken}");
                var messengerMessage = JsonConvert.DeserializeObject<MessengerMessage>(MessengerMessageResponse);

                var instagramSender = await _appDbContext.SenderInstagramSenders
                    .FirstOrDefaultAsync(
                        x =>
                            x.InstagramId == messengerMessage.from.Id
                            && x.CompanyId == company.Id
                            && x.InstagramPageId == pageId);

                InstagramSender instagramReceiver = null;

                if (instagramSender == null)
                {
                    instagramSender = _mapper.Map<InstagramSender>(messengerMessage.from);
                    instagramSender.CompanyId = company.Id;
                }

                if (messengerMessage.to.data.Count > 0)
                {
                    instagramReceiver = await _appDbContext.SenderInstagramSenders
                        .FirstOrDefaultAsync(
                            x =>
                                x.InstagramId == messengerMessage.to.data.FirstOrDefault().Id
                                && x.CompanyId == company.Id
                                && x.InstagramPageId == pageId);

                    if (instagramReceiver == null)
                    {
                        instagramReceiver = _mapper.Map<InstagramSender>(messengerMessage.to.data.FirstOrDefault());
                        instagramReceiver.CompanyId = company.Id;
                        instagramReceiver.InstagramPageId = pageId;
                    }
                }

                if (trigger && instagramSender.Name == "Guest")
                {
                    var igConversationsResponse = await httpClient.GetStringAsync(
                        $"https://graph.facebook.com/{pageId}/conversations?fields=participants,updated_time&access_token={messengerAccessToken}&limit=50&platform=instagram");

                    var igConversations =
                        JsonConvert.DeserializeObject<InstagramConversationResult>(igConversationsResponse);

                    foreach (var fbconversation in igConversations.Data
                                 .Where(x => x.Participants.Data
                                     .Select(y => y.Id)
                                     .Contains(messengerMessage.from.Id)))
                    {
                        BackgroundJob.Enqueue(
                            () => IGFetchByThreadId(
                                new Change
                                {
                                    Field = "conversations",
                                    Value = new Value
                                    {
                                        ThreadId = fbconversation.Id,
                                        PageId = pageId
                                    }
                                },
                                true));
                    }
                }

                instagramSender.InstagramPageId = pageId;
                var isSentFromSleekflow = messengerMessage.from.Id == instagramConfig.InstagramPageId;

                var conversationMessage = new ConversationMessage()
                {
                    MessageContent = messengerMessage.message,
                    Channel = ChannelTypes.Instagram,
                    InstagramSender = instagramSender,
                    InstagramReceiver = instagramReceiver,
                    MessageType = "text",
                    MessageUniqueID = messengerMessage.id,
                    CreatedAt = messengerMessage.created_time.ToUniversalTime(),
                    UpdatedAt = messengerMessage.created_time.ToUniversalTime(),
                    Status = isSentFromSleekflow ? MessageStatus.Sent : MessageStatus.Received
                };

                DateTimeOffset dateTimeOffset = conversationMessage.CreatedAt;
                conversationMessage.Timestamp = dateTimeOffset.ToUnixTimeSeconds();
                conversationMessage.FrondendTimestamp = dateTimeOffset.ToUnixTimeMilliseconds();

                var now = DateTime.UtcNow;
                var t = now.Subtract(conversationMessage.CreatedAt);
                trigger = t.TotalMinutes < 5;

                conversationMessage.IsSentFromSleekflow =
                    (conversationMessage.InstagramSender?.InstagramId == instagramConfig.InstagramPageId)
                        ? true
                        : false;

                var conversation = new Conversation
                {
                    MessageGroupName = company.SignalRGroupName,
                    InstagramUser =
                        (conversationMessage.InstagramSender?.InstagramId != instagramConfig.InstagramPageId ||
                         messengerMessage.to.data.Count == 0)
                            ? conversationMessage.InstagramSender
                            : conversationMessage.InstagramReceiver,
                    ActiveStatus = ActiveStatus.Active
                };

                AttachmentMessage attachementData = null;

                try
                {
                    var attachmentDataResponse = await httpClient.GetStringAsync(
                        $"https://graph.facebook.com/{messageId}/attachments?access_token={messengerAccessToken}");
                    attachementData = JsonConvert.DeserializeObject<AttachmentMessage>(attachmentDataResponse);
                }
                catch (Exception ex)
                {
                    _logger.LogError(
                        ex,
                        "[Instagram {MethodName}] Page id {InstagramPageId} error when fetching attachment for message {MessageId}: {ExceptionMessage}",
                        nameof(IGFetchByMessageId),
                        pageId,
                        messageId,
                        ex.Message);
                }

                Shares shareData = null;

                try
                {
                    var shareDataResponse = await httpClient.GetStringAsync(
                        $"https://graph.facebook.com/{messageId}/shares?access_token={messengerAccessToken}");
                    shareData = JsonConvert.DeserializeObject<Shares>(shareDataResponse);
                }
                catch (Exception ex)
                {
                    _logger.LogError(
                        ex,
                        "[Instagram {MethodName}] Page id {Instagram} error when fetching shares for message {MessageId}: {ExceptionMessage}",
                        nameof(IGFetchByMessageId),
                        pageId,
                        messageId,
                        ex.Message);
                }

                if (await _appDbContext.ConversationMessages
                        .AnyAsync(x => x.MessageUniqueID == messageId))
                {
                    return;
                }

                if (attachementData.data?.Count > 0)
                {
                    var files = new List<FileURLMessage>();
                    conversationMessage.MessageType = "file";

                    foreach (var attachment in attachementData.data)
                    {
                        string fileUrl = string.Empty;

                        // File Message
                        switch (attachment.mime_type)
                        {
                            case "image/jpeg":
                                fileUrl = attachment.image_data.url;

                                break;
                            case "video/mp4":
                                fileUrl = attachment.video_data.url;

                                break;
                            default:
                                if (!string.IsNullOrEmpty(attachment.image_data?.url))
                                {
                                    fileUrl = attachment.image_data?.url;
                                    attachment.mime_type = "image/jpeg";
                                }
                                else if (!string.IsNullOrEmpty(attachment.video_data?.url))
                                {
                                    fileUrl = attachment.video_data?.url;
                                    attachment.mime_type = "video/mp4";
                                }
                                else
                                {
                                    fileUrl = attachment.file_url;
                                }

                                if (string.IsNullOrEmpty(attachment.name))
                                {
                                    attachment.name = DateTime.UtcNow.ToString("dd-MM-yyyy HH:mm:ss");
                                }

                                break;
                        }

                        files.Add(
                            new FileURLMessage()
                            {
                                FileName = attachment.name,
                                FileURL = fileUrl,
                                MIMEType = attachment.mime_type
                            });
                    }

                    var result = await SendFileMessageByFBURL(conversation, conversationMessage, files, trigger);
                    results.AddRange(result);
                }
                else if (shareData?.Data?.Count > 0)
                {
                    var files = new List<FileURLMessage>();
                    conversationMessage.MessageType = "file";

                    foreach (var share in shareData.Data)
                    {
                        try
                        {
                            foreach (var templete in share?.Template?.Payload?.Product?.Elements?.Data)
                            {
                                string fileUrl = templete.ImageUrl.ToString();

                                files.Add(
                                    new FileURLMessage()
                                    {
                                        FileName = $"{templete.Name} ${templete.Price}",
                                        FileURL = fileUrl,
                                        MIMEType = "image/jpeg"
                                    });
                            }
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(
                                ex,
                                "[Instagram {MethodName}] Page id {InstagramkPageId} Fetch product template error for message id {MessageId}. {ExceptionMessage}",
                                nameof(IGFetchByMessageId),
                                pageId,
                                messageId,
                                ex.Message);

                            conversationMessage.MessageType = "text";
                        }
                    }

                    var result = await SendFileMessageByFBURL(conversation, conversationMessage, files, trigger);
                    results.AddRange(result);
                }
                else
                {
                    if (string.IsNullOrEmpty(conversationMessage.MessageContent))
                    {
                        conversationMessage.MessageContent = "<Unsupported Message Type>";
                    }

                    var result = await SendMessage(conversation, conversationMessage, trigger, false, false);
                    results.AddRange(result);
                }

                if (conversationMessage.InstagramSender?.InstagramId != pageId)
                {
                    await IGFetchFacebookUserProfile(
                        company.Id,
                        conversationMessage.InstagramSender,
                        messengerAccessToken);
                }

                if (conversationMessage.InstagramReceiver?.InstagramId != pageId)
                {
                    await IGFetchFacebookUserProfile(
                        company.Id,
                        conversationMessage.InstagramReceiver,
                        messengerAccessToken);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[Instagram {MethodName}] Page id {InstagramPageId} error for message id {MessageId}: {ExceptionString}",
                    nameof(IGFetchByMessageId),
                    pageId,
                    messageId,
                    ex.ToString());

                if (ex.Message == "Unable to get conversation")
                {
                    // Thread.Sleep(3000);
                    await Task.Delay(3000);
                    BackgroundJob.Enqueue(() => IGFetchByMessageId(pageId, messageId, trigger, attempts - 1));
                }
            }

            // return results;
        }

        public async Task FBSaveMessage(string pageId, FBMessaging message, Conversation conversation, ILockService.Lock myLock, bool trigger = true)
        {
            _logger.LogInformation(
                "[{MethodName}] [Facebook] Page ID: {PageId}, Message ID: {FacebookMessageMid}",
                nameof(FBSaveMessage),
                pageId,
                message.message.mid);

            List<ConversationMessage> results = new List<ConversationMessage>();

            try
            {
                if (await _appDbContext.ConversationMessages
                        .AnyAsync(x => x.MessageUniqueID == message.message.mid))
                {
                    return;
                }

                var facebookConfig = await _appDbContext.ConfigFacebookConfigs
                    .FirstOrDefaultAsync(x => x.PageId == pageId);

                var company = await _appDbContext.CompanyCompanies
                    .FirstOrDefaultAsync(x => x.Id == facebookConfig.CompanyId);

                var isSentFromSleekflowPage = pageId != message.recipient?.id && pageId == message.sender.id;
                FacebookSender facebookReceiver = null;
                FacebookSender facebookSender = null;

                if (!isSentFromSleekflowPage) // contact is sender
                {
                    facebookSender = conversation.facebookUser;

                    facebookReceiver = await _appDbContext.SenderFacebookSenders
                        .FirstOrDefaultAsync(
                            x =>
                                x.FacebookId == pageId
                                && x.CompanyId == company.Id
                                && x.pageId == pageId) ?? new FacebookSender
                                {
                                    FacebookId = pageId,
                                    pageId = pageId,
                                    CompanyId = company.Id
                                };

                    if (facebookReceiver.Id == 0)
                    {
                        // Save page sender for future use
                        await _appDbContext.SenderFacebookSenders.AddAsync(facebookReceiver);
                        await _appDbContext.SaveChangesAsync();
                    }
                }
                else // page is sender
                {
                    facebookSender = await _appDbContext.SenderFacebookSenders
                        .FirstOrDefaultAsync(
                            x =>
                                x.FacebookId == pageId
                                && x.CompanyId == company.Id
                                && x.pageId == pageId) ?? new FacebookSender
                                {
                                    FacebookId = pageId,
                                    pageId = pageId,
                                    CompanyId = company.Id
                                };

                    if (facebookSender.Id == 0)
                    {
                        // Save page sender for future use
                        await _appDbContext.SenderFacebookSenders.AddAsync(facebookSender);
                        await _appDbContext.SaveChangesAsync();
                    }

                    facebookReceiver = conversation.facebookUser;
                }

                // Use existing facebook sender
                if (conversation.facebookUser.Id == 0)
                {
                    conversation.facebookUser = await _appDbContext.SenderFacebookSenders.FirstOrDefaultAsync(
                        x => x.CompanyId == conversation.CompanyId &&
                             x.FacebookId == conversation.facebookUser.FacebookId &&
                             x.pageId == conversation.facebookUser.pageId);
                }

                if (facebookSender?.FacebookId != pageId)
                {
                    facebookSender = await FetchFacebookUserProfile(
                        company.Id,
                        facebookSender,
                        pageId,
                        facebookConfig.PageAccessToken);
                }

                if (facebookReceiver?.FacebookId != pageId)
                {
                    facebookReceiver = await FetchFacebookUserProfile(
                        company.Id,
                        facebookReceiver,
                        pageId,
                        facebookConfig.PageAccessToken);
                }

                if (!string.IsNullOrEmpty(message.message.mid))
                {
                    var conversationMessage = new ConversationMessage()
                    {
                        MessageContent = message.message.text,
                        Channel = ChannelTypes.Facebook,
                        facebookSender = facebookSender,
                        facebookReceiver = facebookReceiver,
                        MessageType = "text",
                        MessageUniqueID = message.message.mid,
                        CreatedAt = DateTime.UtcNow,
                        UpdatedAt = DateTime.UtcNow,
                        Status = MessageStatus.Sent
                    };

                    conversationMessage.IsSentFromSleekflow =
                        (conversationMessage.facebookSender?.FacebookId == facebookConfig.PageId) ? true : false;

                    if (!conversationMessage.IsSentFromSleekflow)
                    {
                        conversationMessage.Status = MessageStatus.Received;
                    }

                    DateTimeOffset dateTimeOffset = conversationMessage.CreatedAt;
                    conversationMessage.Timestamp = dateTimeOffset.ToUnixTimeSeconds();
                    conversationMessage.FrondendTimestamp = dateTimeOffset.ToUnixTimeMilliseconds();

                    var now = DateTime.UtcNow;
                    var t = now.Subtract(conversationMessage.CreatedAt);
                    trigger = t.TotalMinutes < 5;

                    var userProfile = await _appDbContext.UserProfiles
                        .AsSplitQuery()
                        .FirstOrDefaultAsync(
                            x =>
                                x.CompanyId == company.Id
                                && x.FacebookAccountId.HasValue
                                && _appDbContext.SenderFacebookSenders
                                    .Where(
                                        y =>
                                            y.FacebookId == conversation.facebookUser.FacebookId &&
                                            y.CompanyId == company.Id
                                            && y.pageId == pageId)
                                    .Select(y => y.Id)

                                    .Contains(x.FacebookAccountId.Value));

                    if (userProfile != null)
                    {
                        conversation.UserProfile = userProfile;
                    }

                    if (await _appDbContext.ConversationMessages
                            .AnyAsync(x => x.MessageUniqueID == message.message.mid))
                    {
                        return;
                    }

                    try
                    {
                        conversationMessage.StoryURL = message.message.ReplyTo?.Story?.Url;
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(
                            ex,
                            "[Facebook {MethodName}] Page id {FacebookPageId} Cannot get story url for message {FacebookMessageMid}: {ExceptionMessage}",
                            nameof(FBSaveMessage),
                            pageId,
                            message.message.mid,
                            ex.Message);
                    }

                    if (await _appDbContext.ConversationMessages
                            .AnyAsync(
                                x => x.MessageUniqueID == message.message.mid))
                    {
                        return;
                    }

                    if (message.message.IsUnsupported)
                    {
                        conversationMessage.MessageContent = "<Unsupported Message Type>";
                    }

                    // interactive message: generic template (Carousel), button template.
                    // Unable to get the payload detail from FetchByMessageId function,
                    // so add the extended payload here
                    if (message.message.attachments != null &&
                        message.message.attachments
                            .Any(x => x.type == "template"))
                    {
                        conversationMessage.MessageType = "interactive";

                        conversationMessage.ExtendedMessagePayload = new ExtendedMessagePayload()
                        {
                            Channel = ChannelTypes.Facebook,
                            ExtendedMessageType = ExtendedMessageType.FacebookInteractiveMessage,
                            ExtendedMessagePayloadDetail = new ExtendedMessagePayloadDetail()
                            {
                                FacebookMessagePayload = new MessageData()
                                {
                                    attachment = message.message.attachments.First()
                                }
                            }
                        };

                        var result = await SendMessage(conversation, conversationMessage, trigger, false, false);
                        results.AddRange(result);
                    }
                    else if (message.message.attachments?.Count > 0)
                    {
                        var files = new List<FileURLMessage>();
                        conversationMessage.MessageType = "file";

                        foreach (var attachment in message.message.attachments)
                        {
                            var uri = new Uri(attachment.payload.url);
                            var fileName = Path.GetFileName(uri.LocalPath);

                            // File Message
                            switch (attachment.type)
                            {
                                case FacebookWebhookAttachmentType.Fallback:
                                    conversationMessage.MessageType = "text";
                                    conversationMessage.MessageContent = message.message.text;

                                    break;
                                case FacebookWebhookAttachmentType.IgReel:
                                case FacebookWebhookAttachmentType.Reel:
                                    conversationMessage.MessageContent = attachment.payload.title;

                                    files.Add(
                                        new FileURLMessage()
                                        {
                                            FileName = fileName,
                                            FileURL = attachment.payload.url
                                        });
                                    break;
                                case FacebookWebhookAttachmentType.Audio:
                                    files.Add(
                                        new FileURLMessage()
                                        {
                                            FileName = fileName,
                                            FileURL = attachment.payload.url,
                                            MIMEType = "audio/mp4"
                                        });

                                    break;
                                case FacebookWebhookAttachmentType.Image:
                                case FacebookWebhookAttachmentType.Video:
                                case FacebookWebhookAttachmentType.File:
                                case FacebookWebhookAttachmentType.Share:
                                case FacebookWebhookAttachmentType.StoryMention:
                                    files.Add(
                                        new FileURLMessage()
                                        {
                                            FileName = fileName,
                                            FileURL = attachment.payload.url
                                        });

                                    break;
                            }
                        }

                        if (files.Any())
                        {
                            var result = await SendFileMessageByFBURL(conversation, conversationMessage, files, trigger);
                            results.AddRange(result);
                        }
                        else
                        {
                            var result = await SendMessage(conversation, conversationMessage, trigger, false, false);
                            results.AddRange(result);
                        }
                    }
                    else
                    {
                        var result = await SendMessage(conversation, conversationMessage, trigger, false, false);
                        results.AddRange(result);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[Facebook {MethodName}] Page id {FacebookPageId} error when processing {FacebookMessageMid}: {ExceptionString}",
                    nameof(FBSaveMessage),
                    pageId,
                    message?.message?.mid,
                    ex.ToString());
            }

            if (myLock != null)
            {
                await _lockService.ReleaseLockAsync(myLock);
            }

            // return results;
        }

        public async Task FbSaveOtnNotifyMeMessage(string pageId, FBMessaging message)
        {
            var facebookConfig = await _appDbContext.ConfigFacebookConfigs
                .FirstOrDefaultAsync(x => x.PageId == pageId);
            if (facebookConfig == null)
            {
                return;
            }

            var company = await _appDbContext.CompanyCompanies
                .FirstOrDefaultAsync(x => x.Id == facebookConfig.CompanyId);
            if (company == null)
            {
                return;
            }

            var facebookSender = await _appDbContext.SenderFacebookSenders
                .FirstOrDefaultAsync(
                    x =>
                        x.FacebookId == message.sender.id
                        && x.CompanyId == company.Id
                        && x.pageId == pageId);

            FacebookSender facebookReceiver = null;

            if (facebookSender == null)
            {
                facebookSender = new FacebookSender
                {
                    FacebookId = message.sender.id,
                    pageId = facebookConfig.PageId
                };

                facebookSender.CompanyId = company.Id;
            }

            if (message.recipient != null)
            {
                facebookReceiver = await _appDbContext.SenderFacebookSenders
                    .FirstOrDefaultAsync(
                        x =>
                            x.FacebookId == message.recipient.id
                            && x.CompanyId == company.Id
                            && x.pageId == pageId);

                if (facebookReceiver == null)
                {
                    facebookReceiver = new FacebookSender
                    {
                        FacebookId = message.recipient.id,
                        pageId = facebookConfig.PageId
                    };

                    facebookReceiver.CompanyId = company.Id;
                }
            }

            facebookSender.pageId = pageId;

            if (facebookSender?.FacebookId != pageId)
            {
                facebookSender = await FetchFacebookUserProfile(
                    company.Id,
                    facebookSender,
                    pageId,
                    facebookConfig.PageAccessToken);
            }

            if (facebookReceiver?.FacebookId != pageId)
            {
                facebookReceiver = await FetchFacebookUserProfile(
                    company.Id,
                    facebookReceiver,
                    pageId,
                    facebookConfig.PageAccessToken);
            }

            var conversationMessage = new ConversationMessage()
            {
                MessageContent = "Notify Me",
                Channel = ChannelTypes.Facebook,
                facebookSender = facebookSender,
                facebookReceiver = facebookReceiver,
                MessageType = "text",
                MessageUniqueID = Guid.NewGuid().ToString() + "-FbOtnNotifyMe",
                CompanyId = company.Id,
                Status = MessageStatus.Sent,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow,
                IsSentFromSleekflow = false
            };

            var conversation = new Conversation
            {
                CompanyId = company.Id,
                MessageGroupName = company.SignalRGroupName,
                facebookUser =
                    (conversationMessage.facebookSender?.FacebookId != facebookConfig.PageId ||
                     message.recipient == null)
                        ? conversationMessage.facebookSender
                        : conversationMessage.facebookReceiver,
                ActiveStatus = ActiveStatus.Active
            };

            conversationMessage.Conversation = await _conversationResolver.GetConversationAsync(conversation, conversationMessage);

            DateTimeOffset dateTimeOffset = conversationMessage.CreatedAt;
            conversationMessage.Timestamp = dateTimeOffset.ToUnixTimeSeconds();
            conversationMessage.FrondendTimestamp = dateTimeOffset.ToUnixTimeMilliseconds();

            await _appDbContext.ConversationMessages.AddAsync(conversationMessage);
            await _appDbContext.SaveChangesAsync();

            var now = DateTime.UtcNow;
            var t = now.Subtract(conversationMessage.CreatedAt);
            var trigger = t.TotalMinutes < 5;

            await MessageCompletion(conversationMessage.Conversation, conversationMessage, trigger);
        }

        public async Task IGSaveMessage(string pageId, FBMessaging message, Conversation conversation, ILockService.Lock myLock, bool trigger = true)
        {
            _logger.LogInformation(
                "[{MethodName}] [Instagram] Page ID: {PageId}, Message ID: {InstagramMessageId}",
                nameof(IGSaveMessage),
                pageId,
                message.message.mid);

            List<ConversationMessage> results = new List<ConversationMessage>();

            try
            {
                if (await _appDbContext.ConversationMessages
                        .AnyAsync(x => x.MessageUniqueID == message.message.mid))
                {
                    return;
                }

                var instagramConfig = await _appDbContext.ConfigInstagramConfigs
                    .FirstOrDefaultAsync(x => x.InstagramPageId == pageId);


                var company = await _appDbContext.CompanyCompanies
                    .FirstOrDefaultAsync(x => x.Id == instagramConfig.CompanyId);

                var instagramSender = await _appDbContext.SenderInstagramSenders
                    .FirstOrDefaultAsync(
                        x => x.InstagramId == message.sender.id
                             && x.CompanyId == instagramConfig.CompanyId
                             && x.InstagramPageId == instagramConfig.InstagramPageId
                             && x.PageId == instagramConfig.PageId);

                InstagramSender instagramReceiver = null;

                if (instagramSender == null)
                {
                    instagramSender = new InstagramSender
                    {
                        InstagramId = message.sender.id,
                        InstagramPageId = instagramConfig.InstagramPageId,
                        PageId = instagramConfig.PageId
                    };

                    instagramSender.CompanyId = company.Id;
                }

                if (message.recipient != null)
                {
                    instagramReceiver = await _appDbContext.SenderInstagramSenders
                        .FirstOrDefaultAsync(
                            x =>
                                x.InstagramId == message.recipient.id
                                && x.CompanyId == instagramConfig.CompanyId
                                && x.InstagramPageId == instagramConfig.InstagramPageId
                                && x.PageId == instagramConfig.PageId);

                    if (instagramReceiver == null)
                    {
                        instagramReceiver = new InstagramSender
                        {
                            InstagramId = message.recipient.id,
                            InstagramPageId = instagramConfig.InstagramPageId,
                            PageId = instagramConfig.PageId
                        };

                        instagramReceiver.CompanyId = company.Id;
                    }
                }

                instagramSender.InstagramPageId = pageId;

                if (instagramSender?.InstagramId != pageId)
                {
                    instagramSender = await IGFetchFacebookUserProfile(
                        company.Id,
                        instagramSender,
                        instagramConfig.PageAccessToken);
                }

                if (instagramReceiver?.InstagramId != pageId)
                {
                    instagramReceiver = await IGFetchFacebookUserProfile(
                        company.Id,
                        instagramReceiver,
                        instagramConfig.PageAccessToken);
                }

                var conversationMessage = new ConversationMessage()
                {
                    MessageContent = message.message.text,
                    Channel = ChannelTypes.Instagram,
                    InstagramSender = instagramSender,
                    InstagramReceiver = instagramReceiver,
                    MessageType = "text",
                    MessageUniqueID = message.message.mid,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                };

                DateTimeOffset dateTimeOffset = conversationMessage.CreatedAt;
                conversationMessage.Timestamp = dateTimeOffset.ToUnixTimeSeconds();
                conversationMessage.FrondendTimestamp = dateTimeOffset.ToUnixTimeMilliseconds();

                var now = DateTime.UtcNow;
                var t = now.Subtract(conversationMessage.CreatedAt);
                trigger = t.TotalMinutes < 5;

                conversationMessage.IsSentFromSleekflow =
                    (conversationMessage.InstagramSender?.InstagramId == instagramConfig.InstagramPageId)
                        ? true
                        : false;

                var userProfile = await _appDbContext.UserProfiles
                    .AsSplitQuery()
                    .FirstOrDefaultAsync(
                        x =>
                            x.CompanyId == company.Id
                            && x.InstagramUserId.HasValue
                            && _appDbContext.SenderInstagramSenders
                                .Where(
                                    y =>
                                        y.InstagramId == conversation.InstagramUser.InstagramId
                                        && y.CompanyId == company.Id
                                        && y.InstagramPageId == pageId)
                                .Select(y => y.Id)
                                .Contains(x.InstagramUserId.Value));
                if (userProfile != null)
                {
                    conversation.UserProfile = userProfile;
                }

                try
                {
                    conversationMessage.StoryURL = message.message.ReplyTo?.Story?.Url;
                }
                catch (Exception ex)
                {
                    _logger.LogError(
                        ex,
                        "[Instagram {MethodName}] Page id {InstagramPageId} Cannot get story url for {InstagramMessageId}: {ExceptionMessage}",
                        nameof(IGSaveMessage),
                        pageId,
                        message.message.mid,
                        ex.Message);
                }

                if (await _appDbContext.ConversationMessages
                        .AnyAsync(x => x.MessageUniqueID == message.message.mid))
                {
                    return;
                }

                if (message.message.IsUnsupported)
                {
                    conversationMessage.MessageContent = "<Unsupported Message Type>";
                }

                // interactive message: generic template (Carousel).
                // Unable to get the payload detail from IGFetchByMessageId function,
                // so add the extended payload here
                if (message.message.attachments != null &&
                    message.message.attachments.Any(x => x.type == "template"))
                {
                    conversationMessage.MessageType = "interactive";

                    conversationMessage.ExtendedMessagePayload = new ExtendedMessagePayload()
                    {
                        Channel = ChannelTypes.Instagram,
                        ExtendedMessageType = ExtendedMessageType.InstagramInteractiveMessage,
                        ExtendedMessagePayloadDetail = new ExtendedMessagePayloadDetail()
                        {
                            FacebookMessagePayload = new MessageData()
                            {
                                attachment = message.message.attachments.First()
                            }
                        }
                    };

                    await SendMessage(conversation, conversationMessage, trigger, false, false);
                }
                else if (message.message.attachments?.Count > 0)
                {
                    var files = new List<FileURLMessage>();
                    conversationMessage.MessageType = "file";

                    foreach (var attachment in message.message.attachments)
                    {
                        string fileUrl;

                        switch (attachment.type)
                        {
                            case FacebookWebhookAttachmentType.Fallback:
                                conversationMessage.MessageType = "text";
                                conversationMessage.MessageContent = message.message.text;

                                break;
                            case FacebookWebhookAttachmentType.IgReel:
                            case FacebookWebhookAttachmentType.Reel:
                                conversationMessage.MessageContent = attachment.payload.title;
                                fileUrl = attachment.payload.url;

                                files.Add(
                                    new FileURLMessage()
                                    {
                                        FileName = Path.GetFileName(fileUrl),
                                        FileURL = fileUrl
                                    });
                                break;
                            case FacebookWebhookAttachmentType.Audio:
                                fileUrl = attachment.payload.url;

                                files.Add(
                                    new FileURLMessage()
                                    {
                                        FileName = string.Empty,
                                        FileURL = fileUrl,
                                        MIMEType = "audio/ogg"
                                    });
                                break;
                            case FacebookWebhookAttachmentType.Share:
                            case FacebookWebhookAttachmentType.StoryMention:
                            case FacebookWebhookAttachmentType.Video:
                            case FacebookWebhookAttachmentType.Image:
                            case FacebookWebhookAttachmentType.File:
                                fileUrl = attachment.payload.url;
                                files.Add(
                                    new FileURLMessage()
                                    {
                                        FileName = Guid.NewGuid().ToString(),
                                        FileURL = fileUrl,
                                        MIMEType = string.Empty
                                    });
                                break;
                        }
                    }

                    if (files.Any())
                    {
                        var result = await SendFileMessageByFBURL(conversation, conversationMessage, files, trigger);
                        results.AddRange(result);
                    }
                    else
                    {
                        var result = await SendMessage(conversation, conversationMessage, trigger, false, false);
                        results.AddRange(result);
                    }
                }
                else
                {
                    var result = await SendMessage(conversation, conversationMessage, trigger, false, false);
                    results.AddRange(result);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[Instagram {MethodName}] Page id {InstagramPageId} error when processing {InstagramMessageId}: {ExceptionString}",
                    nameof(IGSaveMessage),
                    pageId,
                    message?.message?.mid,
                    ex.ToString());
            }

            if (myLock != null)
            {
                await _lockService.ReleaseLockAsync(myLock);
            }

            // return results;
        }

        public async Task<Conversation> ChangeConversationAssignedTeam(
            Conversation conversation,
            CompanyTeam companyTeam,
            bool isTriggerUpdate = true,
            string changedBy = null)
        {
            var isChangeAssignee =
                conversation.AssignedTeamId != companyTeam?.Id
                || !conversation.AssigneeId.HasValue;

            if (conversation.AssignedTeamId.HasValue
                && conversation.AssignedTeam == null)
            {
                conversation.AssignedTeam = await _appDbContext.CompanyStaffTeams
                    .FirstOrDefaultAsync(x => x.Id == conversation.AssignedTeamId);
            }

            if (!isChangeAssignee)
            {
                return conversation;
            }

            TeamData newTeam = null;

            if (companyTeam is not null)
            {
                newTeam = new TeamData(
                    companyTeam.Id,
                    companyTeam.TeamName);
            }

            await _userProfileService.SetFieldValueByFieldNameSafe(
                conversation.UserProfileId,
                "AssignedTeam",
                newTeam?.TeamId.ToString(),
                isTriggerUpdate);

            conversation.AssignedTeamId = newTeam?.TeamId;
            conversation.ModifiedAt = DateTime.UtcNow;
            await _appDbContext.SaveChangesAsync();

            if (conversation.ActiveStatus == ActiveStatus.Inactive)
            {
                return conversation;
            }

            await _cacheManagerService.DeleteCacheWithConstantKeyAsync($":1:conversation:{conversation.Id}");
            await _signalRService.SignalROnConversationAssignTeamChanged(conversation);

            return conversation;
        }

        public async Task<Conversation> ChangeConversationAssignee(
            Conversation conversation,
            Staff assignee,
            bool isTriggerUpdate = true,
            string changedBy = null)
        {
            var oldConversationListener = await _signalRService.GetSignalRTargetUserIds(
                conversation.CompanyId,
                conversation.Id,
                conversation.AssignedTeamId,
                conversation.AssigneeId,
                !conversation.AssigneeId.HasValue,
                isGetActiveUserOnly: false);

            var isChangeAssignee =
                conversation.AssigneeId != assignee?.Id
                                  || (conversation.AssigneeId.HasValue && assignee == null);

            if (!isChangeAssignee)
            {
                return conversation;
            }

            var originalAssignee = conversation.AssigneeId;

            if (conversation.AssigneeId != assignee?.Id)
            {
                // log assignee changed
                await _userProfileHooks.OnConversationAssigneeChangedAsync(
                    conversation.CompanyId,
                    conversation.UserProfileId,
                    changedBy,
                    async () =>
                    {
                        var originalAssigneeData = conversation.AssigneeId.HasValue ?
                            await _appDbContext.UserRoleStaffs
                        .Where(x => x.Id == conversation.AssigneeId)
                        .Select(x => new StaffData(x.Id.ToString(), x.IdentityId, x.Identity.DisplayName))
                        .FirstOrDefaultAsync() :
                            null;

                        var newAssigneeData = assignee != null
                            ? await _appDbContext.UserRoleStaffs.Where(x => x.Id == assignee.Id)
                                .Select(x => new StaffData(x.Id.ToString(), x.IdentityId, x.Identity.DisplayName))
                                .FirstOrDefaultAsync()
                            : null;
                        return new OnConversationAssigneeChangedData(
                            originalAssigneeData,
                            newAssigneeData,
                            conversation.Id);
                    });
            }

            conversation.Assignee = assignee;
            conversation.ModifiedAt = DateTime.UtcNow;
            await _appDbContext.SaveChangesAsync();

            if (assignee != null)
            {
                var rolePermission = await _appDbContext.CompanyRolePermissions
                    .FirstOrDefaultAsync(
                        x =>
                            x.CompanyId == conversation.CompanyId
                            && x.StaffUserRole == assignee.RoleType);

                if (rolePermission != null
                    && rolePermission.Permission.AddAsCollaboratorWhenAssignedToOthers)
                {
                    try
                    {
                        // Add orignal assignee to additioanl assignee
                        if (originalAssignee.HasValue)
                        {
                            if (!_appDbContext.ConversationAdditionalAssignees.Any(
                                    x => x.ConversationId == conversation.Id &&
                                         x.AssigneeId == originalAssignee))
                            {
                                conversation = await _conversationAssigneeService.AddAdditionalAssignees(
                                    conversation,
                                    new List<long>
                                    {
                                        originalAssignee.Value
                                    });
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(
                            ex,
                            "[{MethodName}] Add to additional assignee error for conversation {ConversationId}: {ExceptionString}",
                            nameof(ChangeConversationAssignee),
                            conversation?.Id,
                            ex.ToString());
                    }
                }
            }


            try
            {
                // Add new assignee to additional assignee
                if (conversation.AssigneeId.HasValue
                    && conversation.AdditionalAssignees
                        .Any(x => x.AssigneeId == conversation.AssigneeId))
                {
                    conversation = await _conversationAssigneeService.RemoveAdditionalAssignees(
                        conversation,
                        new List<long>
                        {
                            conversation.AssigneeId.Value
                        });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[{MethodName}] Remove additional assignee error for conversation {ConversationId}: {ExceptionString}",
                    nameof(ChangeConversationAssignee),
                    conversation?.Id,
                    ex.ToString());
            }

            var preUpdatedUserProfile = await _flowHubService.GetUserProfileDictAsync(
                conversation.CompanyId,
                conversation.UserProfileId);

            if (conversation.Assignee != null)
            {
                await _userProfileService.SetFieldValueByFieldNameSafe(
                    conversation.UserProfileId,
                    "ContactOwner",
                    assignee.IdentityId,
                    true);
                await _signalRService.SignalROnConversationAssigneeDeleted(conversation, oldConversationListener);
                await _signalRService.SignalROnConversationAssigneeChanged(conversation, isTriggerUpdate);
            }
            else
            {
                BackgroundJob.Enqueue<IUserProfileService>(
                    x => x.SetFieldValueByFieldNameSafe(
                    conversation.UserProfileId,
                    "ContactOwner",
                    string.Empty,
                    true));

                await _signalRService.SignalROnConversationAssigneeDeleted(conversation);
            }

            if (!conversation.AssignedTeamId.HasValue)
            {
                // BackgroundJob.Enqueue<IUserProfileService>(x =>
                //     x.SetFieldValueByFieldNameSafe(conversation.UserProfileId, "AssignedTeam", "", true));
                await _userProfileService.SetFieldValueByFieldNameSafe(
                    conversation.UserProfileId,
                    "AssignedTeam",
                    string.Empty,
                    true);
            }

            await _userProfileHooks.OnUserProfileFieldChangedAsync(
                conversation.CompanyId,
                conversation.UserProfileId,
                changedBy,
                async () =>
                {
                    var postUpdatedUserProfile = await _flowHubService.GetUserProfileDictAsync(
                        conversation.CompanyId,
                        conversation.UserProfileId);

                    return new OnUserProfileFieldChangedData(
                        postUpdatedUserProfile,
                        preUpdatedUserProfile,
                        "Updated");
                });

            if (conversation.ActiveStatus == ActiveStatus.Inactive)
            {
                return conversation;
            }

            try
            {
                if (conversation.ActiveStatus == ActiveStatus.Active
                    && isChangeAssignee
                    && conversation.Status == "open"
                    && DateTime.UtcNow.AddHours(-1) < conversation.UpdatedTime)
                {
                    var conversationMessage = await _appDbContext.ConversationMessages
                        .OrderByDescending(x => x.CreatedAt)
                        .FirstOrDefaultAsync(
                            message =>
                                message.ConversationId == conversation.Id
                                && message.Channel != ChannelTypes.Note);

                    if (!conversationMessage.IsSentFromSleekflow)
                    {
                        await _signalRService.SendNewMessagePushNotification(conversation, conversationMessage);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[{MethodName}] Send new message push push notification error for conversation {ConversationId}: {ExceptionMessage}",
                    nameof(ChangeConversationAssignee),
                    conversation?.Id,
                    ex.Message);
            }

            await _cacheManagerService.DeleteCacheWithConstantKeyAsync($":1:conversation:{conversation.Id}");

            return conversation;
        }

        public async Task<Conversation> ChangeConversationStatus(
            string conversationId,
            string staffId,
            StatusViewModel statusViewModel,
            bool setUnread = false)
        {
            var conversation = await _appDbContext.Conversations
                .Include(x => x.UserProfile)
                .Include(x => x.Assignee.Identity)
                .FirstOrDefaultAsync(x => x.Id == conversationId);

            if (conversation == null)
            {
                throw new EntryPointNotFoundException("conversation not found");
            }

            // setUnread flag use for snooze function.
            if (conversation.Status == "closed" && statusViewModel.Status == "open" && setUnread)
            {
                // not allow to update the status to open from snooze if conversation status is closed
                return conversation;
            }

            if (conversation != null)
            {
                switch (statusViewModel.Status.ToLower())
                {
                    case "open":
                    case "pending":
                    case "closed":
                        if (conversation.Status != statusViewModel.Status)
                        {
                            // _appDbContext.ConversationActivityLogs.Add(new ConversationActivityLog
                            // {
                            //    ConversationId = conversation.Id,
                            //    LogMessage = $"Status: {conversation.Status} to {statusViewModel.Status}",
                            //    Status = "done",
                            //    Type = "StatusChanged",
                            //    ModifiedBy = await _appDbContext.UserRoleStaffs.Where(x => x.IdentityId == staffId).FirstOrDefaultAsync()
                            // });
                            var oldStatus = GetStatusName(conversation.Status);
                            var newStatus = GetStatusName(statusViewModel.Status);

                            conversation.ModifiedAt = DateTime.UtcNow;
                            var conversationStatusModifiedAt = conversation.ModifiedAt;

                            conversation.Status = statusViewModel.Status;
                            conversation.SnoozeUntil = null;

                            await _appDbContext.SaveChangesAsync();

                            if (setUnread)
                            {
                                conversation.UnreadMessageCount = 1;

                                // conversation.UpdatedTime = DateTime.UtcNow;
                                await _appDbContext.SaveChangesAsync();

                                await _signalRService.SendSnoozeCompletedReminder(conversation);

                                try
                                {
                                    await SendConversationNote(
                                        conversation.CompanyId,
                                        conversation.Id,
                                        null,
                                        new ConversationMessage
                                        {
                                            Channel = ChannelTypes.Note,
                                            MessageType = "text",
                                            MessageContent = "Reminder"
                                        },
                                        null);
                                }
                                catch (Exception ex)
                                {
                                    _logger.LogError(
                                        ex,
                                        "Note reminder error {sleekflow_company_id} {sleekflow_conversation_id}",
                                        conversation.CompanyId,
                                        conversationId);
                                }
                            }

                            if (statusViewModel.Status.ToLower() == "pending")
                            {
                                var timeSpan = new TimeSpan();

                                if (statusViewModel.SnoozeUntil.HasValue)
                                {
                                    conversation.SnoozeUntil = statusViewModel.SnoozeUntil.Value;
                                    timeSpan = conversation.SnoozeUntil.Value - DateTime.UtcNow;
                                }
                                else
                                {
                                    switch (statusViewModel.SnoozeOptions)
                                    {
                                        case SnoozeOptions.OneHour:
                                            timeSpan = TimeSpan.FromHours(1);

                                            break;
                                        case SnoozeOptions.ThreeHour:
                                            timeSpan = TimeSpan.FromHours(3);

                                            break;
                                        case SnoozeOptions.OneDay:
                                            timeSpan = TimeSpan.FromDays(1);

                                            break;
                                        case SnoozeOptions.OneWeek:
                                            timeSpan = TimeSpan.FromDays(7);

                                            break;
                                        case SnoozeOptions.OneMonth:
                                            timeSpan = TimeSpan.FromDays(30);

                                            break;
                                    }

                                    conversation.SnoozeUntil = DateTime.UtcNow + timeSpan;
                                }

                                await _appDbContext.SaveChangesAsync();

                                BackgroundJob.Schedule<IConversationMessageService>(
                                        x => x.ChangeConversationStatus(
                                            conversationId,
                                            staffId,
                                            new StatusViewModel
                                            {
                                                Status = "open"
                                            },
                                            true),
                                        timeSpan);

                                await _userProfileHooks.OnConversationStatusChangedAsync(
                                    conversation.CompanyId,
                                    conversation.UserProfileId,
                                    staffId,
                                    () => Task.FromResult(
                                        new OnConversationStatusChangedData(
                                            oldStatus,
                                            newStatus,
                                            conversation.SnoozeUntil?.ToUniversalTime(),
                                            conversationId)));
                            }
                            else
                            {
                                await _userProfileHooks.OnConversationStatusChangedAsync(
                                    conversation.CompanyId,
                                    conversation.UserProfileId,
                                    staffId,
                                    () => Task.FromResult(
                                        new OnConversationStatusChangedData(
                                            oldStatus,
                                            newStatus,
                                            null,
                                            conversationId)));

                                // Custom Module for Z.Com Backup Chat History
                                var shouldBackupChatHistory = await _zDotComService.ShouldBackupChatHistoryAsync(
                                    conversation,
                                    oldStatus,
                                    newStatus,
                                    conversationStatusModifiedAt);

                                if (shouldBackupChatHistory)
                                {
                                    BackgroundJob.Enqueue<IZDotComService>(x => x.CreateChatHistoryBackupAndSendEmailAsync(conversationId, staffId));
                                }
                            }

                            var headers = new List<ImportHeader>
                            {
                                new ImportHeader
                                {
                                    HeaderName = "ConversationStatus"
                                }
                            };

                            var fields = new List<string>
                            {
                                statusViewModel.Status
                            };
                            var importUserProfile = new ImportUserProfileObject(headers, fields);

                            var header = new ImportUserProfileObject(headers, fields);

                            BackgroundJob.Enqueue<IAutomationService>(
                                x => x.CustomFieldsBulkChangedTrigger(
                                    conversation.UserProfileId,
                                    importUserProfile,
                                    importUserProfile._fields));

                            await _signalRService.SignalROnConversationStatusChanged(conversation);

                            await _cacheManagerService.DeleteCacheWithConstantKeyAsync($":1:conversation:{conversationId}");
                        }

                        return conversation;
                }
            }

            return conversation;
        }

        private string GetStatusName(string status)
        {
            switch (status.ToLower())
            {
                case "open":
                    return "Open";
                case "pending":
                    return "Snooze";
                case "closed":
                    return "Closed";
            }

            return status;
        }

        public bool IsConversationStatusChangedFromClosedToOpen(string oldStatus, string newStatus)
        {
            return oldStatus.ToLower() == "closed" && newStatus.ToLower() == "open";
        }

        public bool IsConversationStatusChangedToClosed(string newStatus)
        {
            return newStatus.ToLower() == "closed";
        }

        public async Task<MemoryStream> DownloadUploadedFile(UploadedFile file)
        {
            var fileMemoryStream = await _azureBlobStorageService.DownloadFromAzureBlob(
                file.Filename,
                file.BlobContainer);
            return fileMemoryStream;
        }

        public async ValueTask<Conversation> ChangeConversationStatus(
            Conversation conversation,
            ConversationMessage conversationMessage,
            bool isTriggerUpdate)
        {
            try
            {
                if (!isTriggerUpdate
                    && conversationMessage.Channel == ChannelTypes.LiveChat)
                {
                    return conversation;
                }

                if (conversationMessage.DeliveryType == DeliveryType.Broadcast
                    || conversationMessage.DeliveryType == DeliveryType.AutomatedMessage
                    || conversationMessage.DeliveryType == DeliveryType.FlowHubAction
                    || conversationMessage.DeliveryType == DeliveryType.ReadMore
                    || conversationMessage.MessageType == MessageTypes.System
                    || conversationMessage.IsFromImport)
                {
                    return conversation;
                }

                if ((conversation.UpdatedTime > DateTime.UtcNow.AddDays(-7) && conversation.Status != "open")
                    || conversation.Status == "pending")
                {
                    await _userProfileHooks.OnConversationStatusChangedAsync(
                        conversation.CompanyId,
                        conversation.UserProfileId,
                        null,
                        () => Task.FromResult(
                            new OnConversationStatusChangedData(
                                conversation.Status,
                                GetStatusName("open"),
                                null,
                                conversation.Id)));

                    if (IsConversationStatusChangedFromClosedToOpen(conversation.Status, "open"))
                    {
                        // conversationMessage will not be null
                        // prevent incoming message that reopens the conversation missing from chat history backup
                        conversation.StatusChangedFromClosedToOpenAt =
                            conversationMessage.CreatedAt.AddMilliseconds(-200);
                    }

                    conversation.Status = "open";
                    conversation.SnoozeUntil = null;

                    await _appDbContext.SaveChangesAsync();
                    await _signalRService.SignalROnConversationStatusChanged(conversation);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[{MethodName}] Conversation {ConversationId} error: {ExceptionString}",
                    nameof(ChangeConversationStatus),
                    conversation?.Id,
                    ex.ToString());
            }

            return conversation;
        }

        private async ValueTask<bool> IsCSReply(Conversation conversation, ConversationMessage conversationMessage)
        {
            if (conversationMessage.Sender != null
                || conversationMessage.DeliveryType == DeliveryType.Broadcast
                || conversationMessage.DeliveryType == DeliveryType.AutomatedMessage
                || conversationMessage.DeliveryType == DeliveryType.FlowHubAction
                || conversationMessage.MessageType == MessageTypes.System
                || conversationMessage.IsSentFromSleekflow)
            {
                return true;
            }

            switch (conversationMessage.Channel)
            {
                case "naive":
                    return !string.IsNullOrEmpty(conversationMessage.ReceiverId)
                           && !string.IsNullOrEmpty(conversationMessage.ReceiverDeviceUUID);
                case ChannelTypes.Email:
                    var companyEmailConfigDomain = await _appDbContext.CompanyCompanies
                        .Where(x => x.Id == conversation.CompanyId)
                        .Select(x => x.EmailConfig.Domain)
                        .AsNoTracking()
                        .FirstOrDefaultAsync();

                    if (companyEmailConfigDomain == null)
                    {
                        throw new Exception($"No email config found");
                    }

                    return !conversationMessage.EmailTo.Contains(companyEmailConfigDomain)
                           && conversationMessage.EmailCC != null
                           && !conversationMessage.EmailCC.Contains(companyEmailConfigDomain);
                case ChannelTypes.Facebook:
                    return conversationMessage.facebookSender?.pageId ==
                           conversationMessage.facebookSender?.FacebookId;
                case ChannelTypes.Instagram:
                    return conversationMessage.InstagramSender?.InstagramPageId ==
                           conversationMessage.InstagramSender?.InstagramId;
                case ChannelTypes.WhatsappTwilio:
                    // Sandbox must be false
                    if (conversationMessage.IsSandbox)
                    {
                        return false;
                    }

                    var whatsAppConfigSender = await _appDbContext.ConfigWhatsAppConfigs
                        .Where(x => x.CompanyId == conversation.CompanyId)
                        .Select(x => x.WhatsAppSender)
                        .AsNoTracking()
                        .ToListAsync();

                    if (!whatsAppConfigSender.Any())
                    {
                        throw new Exception($"No WhatsApp Sender found");
                    }

                    return whatsAppConfigSender.Contains(conversationMessage.whatsappSender?.whatsAppId);
                case ChannelTypes.Whatsapp360Dialog:
                    return conversationMessage.Whatsapp360DialogSender == null;
                case ChannelTypes.WhatsappCloudApi:
                    return conversationMessage.IsSentFromSleekflow;
                case ChannelTypes.LiveChat:
                    return conversationMessage.Sender == null
                           && conversationMessage.WebClientSender == null;
                case ChannelTypes.Wechat:
                    return conversationMessage.WeChatSender == null;
                case ChannelTypes.Line:
                    return conversationMessage.LineSender == null;
                case ChannelTypes.Viber:
                    return conversationMessage.ViberSender == null;
                case ChannelTypes.Telegram:
                    return conversationMessage.TelegramSender == null;
                case ChannelTypes.Sms:
                    {
                        var smsConfigSender = await _appDbContext.ConfigSMSConfigs
                            .Where(x => x.CompanyId == conversation.CompanyId)
                            .Select(x => x.SMSSender)
                            .AsNoTracking()
                            .ToListAsync();

                        if (!smsConfigSender.Any())
                        {
                            throw new Exception($"No WhatsApp Sender found");
                        }

                        return smsConfigSender.Contains(conversationMessage.SMSSender?.SMSId);
                    }
            }

            return false;
        }

        public async Task SendWhatsapp360DialogOptInButtonMessage(
            string companyId,
            string conversationId,
            long whatsapp360dialogConfigId)
        {
            var conversation = await _appDbContext.Conversations
                .Include(x => x.WhatsApp360DialogUser)
                .FirstOrDefaultAsync(
                    x =>
                        x.Id == conversationId &&
                        x.CompanyId == companyId);

            var optInConfig = await _appDbContext.ConfigWhatsApp360DialogConfigs
                .Where(x => x.Id == whatsapp360dialogConfigId && x.IsOptInEnable)
                .Select(x => x.OptInConfig)
                .FirstOrDefaultAsync();

            if (conversation == null
                || optInConfig == null)
            {
                return;
            }

            var conversationMessage = new ConversationMessage
            {
                Channel = ChannelTypes.Whatsapp360Dialog,
                MessageType = "template",
                MessageContent = optInConfig.TemplateMessageContent,
                Whatsapp360DialogReceiver = conversation.WhatsApp360DialogUser,
                Whatsapp360DialogReceiverId = conversation.WhatsApp360DialogUserId,
                IsSentFromSleekflow = true,
                Whatsapp360DialogExtendedMessagePayload = new Whatsapp360DialogExtendedMessagePayload()
                {
                    Whatsapp360DialogTemplateMessage = new Whatsapp360DialogTemplateMessageViewModel()
                    {
                        Language = optInConfig.Language,
                        TemplateName = optInConfig.TemplateName,
                        TemplateNamespace = optInConfig.TemplateNamespace
                    }
                },
                DeliveryType = DeliveryType.ReadMore,
            };

            await SendMessage(conversation, conversationMessage);
        }

        public async Task SendWhatsapp360DialogReadMoreMessage(
            string companyId,
            string conversationId,
            long whatsapp360dialogConfigId,
            long conversationMessageId)
        {
            var conversation = await _appDbContext.Conversations
                .Include(x => x.WhatsApp360DialogUser)
                .FirstOrDefaultAsync(
                    x =>
                        x.Id == conversationId
                        && x.CompanyId == companyId);

            var undeliveredConversationMessage = await _appDbContext.ConversationMessages
                .AsNoTracking()
                .Include(x => x.UploadedFiles)
                .Include(x => x.Whatsapp360DialogExtendedMessagePayload)
                .FirstOrDefaultAsync(
                    x =>
                        x.Id == conversationMessageId
                        && x.Status == MessageStatus.Undelivered);

            var optInConfig = await _appDbContext.ConfigWhatsApp360DialogConfigs
                .Where(
                    x =>
                        x.Id == whatsapp360dialogConfigId
                        && x.IsOptInEnable)
                .Select(x => x.OptInConfig)
                .FirstOrDefaultAsync();

            if (conversation == null
                || optInConfig == null
                || undeliveredConversationMessage == null)
            {
                return;
            }

            if (undeliveredConversationMessage.Whatsapp360DialogExtendedMessagePayload != null)
            {
                undeliveredConversationMessage.Whatsapp360DialogExtendedMessagePayload.Id = 0;
                undeliveredConversationMessage.Whatsapp360DialogExtendedMessagePayload.ConversationMessageId = 0;
            }

            var conversationMessage = new ConversationMessage
            {
                Channel = ChannelTypes.Whatsapp360Dialog,
                MessageType = undeliveredConversationMessage.MessageType,
                MessageContent = undeliveredConversationMessage.MessageContent,
                Whatsapp360DialogReceiver = undeliveredConversationMessage.Whatsapp360DialogReceiver,
                Whatsapp360DialogReceiverId = undeliveredConversationMessage.Whatsapp360DialogReceiverId,
                UploadedFiles = undeliveredConversationMessage.UploadedFiles,
                Whatsapp360DialogExtendedMessagePayload =
                    undeliveredConversationMessage.Whatsapp360DialogExtendedMessagePayload,
                IsSentFromSleekflow = true,
                DeliveryType = DeliveryType.AutomatedMessage,
            };

            if (conversationMessage.UploadedFiles is { Count: > 0 })
            {
                foreach (var conversationMessageUploadedFile in conversationMessage.UploadedFiles)
                {
                    conversationMessageUploadedFile.Id = 0;
                    conversationMessageUploadedFile.ConversationMessageId = conversationMessage.Id;
                    conversationMessageUploadedFile.FileId = Guid.NewGuid().ToString();
                }
            }

            await SendMessage(conversation, conversationMessage);
        }

        public async Task SendWhatsappCloudApiOptInButtonMessage(
            string companyId,
            string conversationId,
            long whatsappCloudApiConfigId,
            long conversationMessageId)
        {
            var conversation = await _appDbContext.Conversations
                .Include(x => x.WhatsappCloudApiUser)
                .FirstOrDefaultAsync(
                    x =>
                        x.Id == conversationId
                        && x.CompanyId == companyId);

            var optInConfig = await _appDbContext.ConfigWhatsappCloudApiConfigs
                .Where(
                    x =>
                        x.Id == whatsappCloudApiConfigId
                        && x.IsOptInEnable)
                .Select(x => x.OptInConfig)
                .FirstOrDefaultAsync();

            if (conversation == null
                || optInConfig == null)
            {
                return;
            }

            var extendedMessagePayload = new ExtendedMessagePayload()
            {
                Channel = ChannelTypes.WhatsappCloudApi
            };

            extendedMessagePayload.SetExtendedMessagePayloadDetailWithType(
                new ExtendedMessagePayloadDetail
                {
                    WhatsappCloudApiTemplateMessageObject = new WhatsappCloudApiTemplateMessageViewModel
                    {
                        TemplateName = optInConfig.TemplateName,
                        Language = optInConfig.Language,
                    },
                });

            var conversationMessage = new ConversationMessage
            {
                Channel = ChannelTypes.WhatsappCloudApi,
                MessageType = "template",
                MessageContent = optInConfig.TemplateMessageContent,
                WhatsappCloudApiReceiver = conversation.WhatsappCloudApiUser,
                IsSentFromSleekflow = true,
                ExtendedMessagePayload = extendedMessagePayload,
                DeliveryType = DeliveryType.ReadMore,
                Metadata = new Dictionary<string, object>()
                {
                    {
                        "targetedConversationMessageId", conversationMessageId.ToString()
                    },
                }
            };

            await SendMessage(conversation, conversationMessage);
        }

        public async Task SendWhatsappCloudApiReadMoreMessage(
            string companyId,
            string conversationId,
            long whatsappCloudApiConfigId,
            long conversationMessageId)
        {
            var conversation = await _appDbContext.Conversations
                .Include(x => x.WhatsappCloudApiUser)
                .FirstOrDefaultAsync(
                    x =>
                        x.Id == conversationId
                        && x.CompanyId == companyId);

            var undeliveredConversationMessage = await _appDbContext.ConversationMessages
                .AsNoTracking()
                .Include(x => x.UploadedFiles)
                .Include(x => x.ExtendedMessagePayload)
                .FirstOrDefaultAsync(
                    x =>
                        x.Id == conversationMessageId
                        && x.Status == MessageStatus.Undelivered);

            var optInConfig = await _appDbContext.ConfigWhatsappCloudApiConfigs
                .Where(
                    x =>
                        x.Id == whatsappCloudApiConfigId
                        && x.IsOptInEnable)
                .Select(x => x.OptInConfig)
                .FirstOrDefaultAsync();

            if (conversation == null
                || optInConfig == null
                || undeliveredConversationMessage == null)
            {
                return;
            }

            if (undeliveredConversationMessage.ExtendedMessagePayload != null)
            {
                undeliveredConversationMessage.ExtendedMessagePayload.Id = Guid.NewGuid().ToString();
                undeliveredConversationMessage.ExtendedMessagePayload.ConversationMessageId = 0;
            }

            var conversationMessage = new ConversationMessage
            {
                Channel = ChannelTypes.WhatsappCloudApi,
                MessageType = undeliveredConversationMessage.MessageType,
                MessageContent = undeliveredConversationMessage.MessageContent,
                WhatsappCloudApiReceiver = undeliveredConversationMessage.WhatsappCloudApiReceiver,
                UploadedFiles = undeliveredConversationMessage.UploadedFiles,
                ExtendedMessagePayload = undeliveredConversationMessage.ExtendedMessagePayload,
                IsSentFromSleekflow = true,
                DeliveryType = DeliveryType.AutomatedMessage,
            };

            if (conversationMessage.UploadedFiles is { Count: > 0 })
            {
                foreach (var conversationMessageUploadedFile in conversationMessage.UploadedFiles)
                {
                    conversationMessageUploadedFile.Id = 0;
                    conversationMessageUploadedFile.ConversationMessageId = conversationMessage.Id;
                    conversationMessageUploadedFile.FileId = Guid.NewGuid().ToString();
                }
            }

            await SendMessage(conversation, conversationMessage);
        }

        private async Task<ConversationMessage> GetConversationMessage(
            Conversation conversation,
            ConversationMessage conversationMessage)
        {
            conversationMessage.ConversationId = conversation.Id;
            conversationMessage.CompanyId = conversation.CompanyId;

            // Add Quote message to QuotedMsgBody
            try
            {
                if (!string.IsNullOrEmpty(conversationMessage.QuotedMsgId)
                    && string.IsNullOrEmpty(conversationMessage.QuotedMsgBody))
                {
                    conversationMessage.QuotedMsgBody = await _appDbContext.ConversationMessages
                        .Where(x => x.MessageUniqueID == conversationMessage.QuotedMsgId)
                        .Select(x => x.MessageContent)
                        .FirstOrDefaultAsync();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex, "Add quote message body error for conversation {ConversationId}",
                    conversation?.Id);
            }

            switch (conversationMessage.Channel)
            {
                case "naive":
                    var senderDevice = await _appDbContext.UserUserDevices
                        .Where(x => x.DeviceUUID == conversationMessage.SenderDeviceUUID)
                        .ToListAsync();
                    var receiverDevice = await _appDbContext.UserUserDevices
                        .Where(x => x.DeviceUUID == conversationMessage.ReceiverDeviceUUID)
                        .ToListAsync();

                    var sender = senderDevice.FirstOrDefault();
                    var receiver = receiverDevice.FirstOrDefault();

                    sender.CompanyId = conversation.CompanyId;
                    sender.CompanyId = conversation.CompanyId;

                    conversationMessage.SenderDevice = senderDevice.FirstOrDefault();
                    conversationMessage.ReceiverDevice = receiverDevice.FirstOrDefault();

                    break;
                case ChannelTypes.Facebook:
                    if (conversationMessage.facebookSender == null)
                    {
                        var facebookConfig = await _appDbContext.ConfigFacebookConfigs
                            .FirstOrDefaultAsync(x => x.PageId == conversationMessage.facebookReceiver.pageId);
                        conversationMessage.facebookSender = await _appDbContext.SenderFacebookSenders
                            .FirstOrDefaultAsync(x => x.FacebookId == conversationMessage.facebookReceiver.pageId);

                        if (conversationMessage.facebookSender == null)
                        {
                            conversationMessage.facebookSender = new FacebookSender
                            {
                                FacebookId = conversationMessage.facebookReceiver.pageId,
                                pageId = conversationMessage.facebookReceiver.pageId,
                                name = facebookConfig.PageName
                            };
                        }
                    }

                    if (conversation.UserProfile.FirstName == "Anonymous"
                        || string.IsNullOrEmpty(conversation.UserProfile.FirstName))
                    {
                        if (!string.IsNullOrEmpty(conversation.facebookUser?.name))
                        {
                            conversation.UserProfile.FirstName = conversation.facebookUser.name;
                        }

                        if (!string.IsNullOrEmpty(conversation.facebookUser?.first_name))
                        {
                            conversation.UserProfile.FirstName = conversation.facebookUser?.first_name;
                            conversation.UserProfile.LastName = conversation.facebookUser?.last_name;
                        }
                    }

                    conversation.facebookUser.CompanyId = conversation.CompanyId;


                    // conversationMessage.facebookSender.pageId = conversation.Company.FacebookConfigs.PageId;
                    // conversationMessage.facebookReceiver.pageId = conversation.Company.FacebookConfigs.PageId;
                    break;
                case ChannelTypes.Instagram:
                    if (conversationMessage.InstagramSender == null)
                    {
                        var instagramConfig = await _appDbContext.ConfigInstagramConfigs
                            .FirstOrDefaultAsync(
                                x => x.InstagramPageId == conversationMessage.InstagramReceiver.InstagramPageId);

                        conversationMessage.InstagramSender = await _appDbContext.SenderInstagramSenders
                            .FirstOrDefaultAsync(
                                x => x.InstagramId == conversationMessage.InstagramReceiver.InstagramPageId);

                        if (conversationMessage.InstagramSender == null)
                        {
                            conversationMessage.InstagramSender = new InstagramSender
                            {
                                InstagramId = conversationMessage.InstagramReceiver.InstagramId,
                                InstagramPageId = conversationMessage.InstagramReceiver.InstagramPageId,
                                PageId = conversationMessage.InstagramReceiver.PageId,
                                Name = instagramConfig.PageName
                            };
                        }
                    }

                    if (conversation.UserProfile.FirstName == "Anonymous"
                        || string.IsNullOrEmpty(conversation.UserProfile.FirstName))
                    {
                        if (!string.IsNullOrEmpty(conversation.InstagramUser?.Username))
                        {
                            conversation.UserProfile.FirstName = conversation.InstagramUser?.Username;
                        }

                        if (!string.IsNullOrEmpty(conversation.InstagramUser?.Name))
                        {
                            conversation.UserProfile.FirstName = conversation.InstagramUser?.Name;
                        }
                    }

                    conversation.InstagramUser.CompanyId = conversation.CompanyId;

                    break;
                case ChannelTypes.WhatsappTwilio:
                    // if (conversationMessage.whatsappSender == null)
                    // {
                    //    if (conversationMessage.whatsappReceiver.whatsAppId.Contains("whatsapp:+"))
                    //    {
                    //        try
                    //        {
                    //            conversationMessage.whatsappSender = await _appDbContext.SenderWhatsappSenders.Where(x => x.whatsAppId == conversation.Company.WhatsAppConfigs.FirstOrDefault().WhatsAppSender).FirstOrDefaultAsync();
                    //            if (conversationMessage.whatsappSender == null)
                    //            {
                    //                conversationMessage.whatsappSender = new WhatsAppSender { whatsAppId = conversation.Company.WhatsAppConfigs.FirstOrDefault().WhatsAppSender, name = conversation.Company.CompanyName };
                    //            }
                    //        }
                    //        catch (Exception ex)
                    //        {
                    //            _logger.LogError("Get whatsapp sender error" + ex.ToString());
                    //        }
                    //    }
                    //    else if (conversationMessage.whatsappReceiver.whatsAppId.Contains("@"))
                    //    {
                    //        var ChatAPIConfig = await _appDbContext.ConfigWSChatAPIConfigs.Where(x => x.CompanyId == conversation.CompanyId && !x.IsDeleted).FirstOrDefaultAsync();
                    //        conversationMessage.whatsappSender = await _appDbContext.SenderWhatsappSenders.Where(x => x.whatsAppId == ChatAPIConfig.WhatsAppSender).FirstOrDefaultAsync();
                    //        if (conversationMessage.whatsappSender == null)
                    //        {
                    //            conversationMessage.whatsappSender = new WhatsAppSender { whatsAppId = ChatAPIConfig.WhatsAppSender };
                    //        }
                    //    }
                    // }
                    if (!string.IsNullOrEmpty(conversation.WhatsappUser.name)
                        && (string.IsNullOrEmpty(conversation.UserProfile.FirstName)
                            || conversation.UserProfile.FirstName == "Anonymous"
                            || conversation.UserProfile.FirstName == conversation.WhatsappUser?.phone_number))
                    {
                        conversation.UserProfile.FirstName = conversation.WhatsappUser.name;
                    }

                    var phoneNumberUtil = PhoneNumbers.PhoneNumberUtil.GetInstance();

                    try
                    {
                        var phoneNumber = phoneNumberUtil.Parse(
                            conversation.UserProfile.FirstName
                                .Replace(" ", string.Empty)
                                .ToLower(),
                            null);

                        if (phoneNumber != null
                            && phoneNumber.HasCountryCode)
                        {
                            conversation.UserProfile.FirstName = conversation.WhatsappUser.name;
                        }
                    }
                    catch (Exception)
                    {
                        try
                        {
                            var sanitizedFirstName = conversation.UserProfile.FirstName
                                .Replace("@c.us", string.Empty)
                                .Replace("@g.us", string.Empty)
                                .TrimStart()
                                .TrimEnd();

                            var hasNonNumericCharacters = Regex.Matches(sanitizedFirstName, "[^\\d\\+\\-\\s]+").Any();

                            var phoneNumber = phoneNumberUtil.Parse($"+{sanitizedFirstName}", null);

                            if (!hasNonNumericCharacters &&
                                phoneNumber != null &&
                                phoneNumber.HasCountryCode)
                            {
                                conversation.UserProfile.FirstName = conversation.WhatsappUser.name;
                            }
                        }
                        catch (Exception ex)
                        {
                            // noop, user profile firstname should remain as it is
                            _logger.LogInformation(
                                ex,
                                "User profile first name {FirstName} is not replaced with whatsapp name {WhatsappName}",
                                conversation.UserProfile.FirstName,
                                conversation.WhatsappUser.name);
                        }
                    }

                    if (string.IsNullOrEmpty(conversation.UserProfile.FirstName))
                    {
                        conversation.UserProfile.FirstName = conversation.WhatsappUser.phone_number;
                    }

                    conversation.WhatsappUser.CompanyId = conversation.CompanyId;

                    break;
                case "email":
                    if (conversation.UserProfile.FirstName == "Anonymous")
                    {
                        if (conversation.EmailAddress != null)
                        {
                            conversation.UserProfile.FirstName = conversation.EmailAddress.Name;
                        }
                    }

                    conversation.EmailAddress.CompanyId = conversation.CompanyId;

                    break;
                case ChannelTypes.Wechat:
                    if (conversation.UserProfile.FirstName == "Anonymous")
                    {
                        if (conversation.WeChatUser != null)
                        {
                            conversation.UserProfile.FirstName = conversation.WeChatUser.nickname;
                        }
                    }

                    conversation.WeChatUser.CompanyId = conversation.CompanyId;

                    break;
                case ChannelTypes.LiveChat:
                    if (conversation.UserProfile.FirstName == "Anonymous")
                    {
                        if (conversation.WebClient != null)
                        {
                            conversation.UserProfile.FirstName = conversation.WebClient.Name;
                        }
                    }

                    conversation.WebClient.CompanyId = conversation.CompanyId;

                    break;
                case ChannelTypes.Line:
                    if (conversation.UserProfile.FirstName == "Anonymous")
                    {
                        if (conversation.LineUser != null)
                        {
                            conversation.UserProfile.FirstName = conversation.LineUser.displayName;
                        }
                    }

                    conversation.LineUser.CompanyId = conversation.CompanyId;

                    break;
                case ChannelTypes.Viber:
                    if (conversation.UserProfile.FirstName == "Anonymous")
                    {
                        if (conversation.ViberUser != null)
                        {
                            conversation.UserProfile.FirstName = conversation.ViberUser.DisplayName;
                        }
                    }

                    conversation.ViberUser.CompanyId = conversation.CompanyId;

                    break;
                case ChannelTypes.Whatsapp360Dialog:
                    if (string.IsNullOrEmpty(conversation.UserProfile.FirstName)
                        || conversation.UserProfile.FirstName == "Anonymous"
                        || conversation.UserProfile.FirstName == conversation.WhatsApp360DialogUser.WhatsAppId)
                    {
                        if (conversation.WhatsApp360DialogUser != null)
                        {
                            if (!string.IsNullOrEmpty(conversation.WhatsApp360DialogUser.Name))
                            {
                                conversation.UserProfile.FirstName = conversation.WhatsApp360DialogUser.Name;
                            }
                            else if (!string.IsNullOrEmpty(conversationMessage.MessageUniqueID) &&
                                     !string.IsNullOrEmpty(conversation.WhatsApp360DialogUser.PhoneNumber))
                            {
                                conversation.UserProfile.FirstName = conversation.WhatsApp360DialogUser.WhatsAppId;
                            }
                        }
                    }

                    conversation.WhatsApp360DialogUser.CompanyId = conversation.CompanyId;

                    break;
                case ChannelTypes.WhatsappCloudApi:
                    if (conversation.UserProfile.FirstName == "Anonymous"
                        || string.IsNullOrEmpty(conversation.UserProfile.FirstName)
                        || conversation.UserProfile.FirstName == conversation.WhatsappCloudApiUser.WhatsappId)
                    {
                        if (conversation.WhatsappCloudApiUser != null)
                        {
                            if (!string.IsNullOrEmpty(conversation.WhatsappCloudApiUser.WhatsappUserDisplayName))
                            {
                                conversation.UserProfile.FirstName =
                                    conversation.WhatsappCloudApiUser.WhatsappUserDisplayName;
                            }
                            else if (!string.IsNullOrEmpty(conversationMessage.MessageUniqueID) &&
                                     !string.IsNullOrEmpty(conversation.WhatsappCloudApiUser.WhatsappId))
                            {
                                conversation.UserProfile.FirstName = conversation.WhatsappCloudApiUser.WhatsappId;
                            }
                        }
                    }

                    conversation.WhatsappCloudApiUser.CompanyId = conversation.CompanyId;

                    break;
                case ChannelTypes.Telegram:
                    if (conversation.UserProfile.FirstName == "Anonymous")
                    {
                        if (conversation.TelegramUser != null)
                        {
                            conversation.UserProfile.FirstName = conversation.TelegramUser.FirstName;
                            conversation.UserProfile.LastName = conversation.TelegramUser.LastName;
                        }
                    }

                    conversation.TelegramUser.CompanyId = conversation.CompanyId;

                    break;
                case ChannelTypes.Sms:
                    if (conversation.UserProfile.FirstName == "Anonymous")
                    {
                        if (conversation.SMSUser != null)
                        {
                            conversation.UserProfile.FirstName = conversation.SMSUser.phone_number;
                        }
                    }

                    conversation.SMSUser.CompanyId = conversation.CompanyId;

                    break;
            }

            // Append ChannelIdentityId if not existed
            if (string.IsNullOrEmpty(conversationMessage.ChannelIdentityId))
            {
                switch (conversationMessage.Channel)
                {
                    case ChannelTypes.WhatsappTwilio:
                    case ChannelTypes.Whatsapp360Dialog:
                    case ChannelTypes.WhatsappCloudApi:
                    case ChannelTypes.Facebook:
                    case ChannelTypes.Instagram:
                    case ChannelTypes.Sms:
                    case ChannelTypes.Wechat:
                    case ChannelTypes.Line:
                    case ChannelTypes.Viber:
                    case ChannelTypes.Telegram:
                        conversationMessage.ChannelIdentityId = conversationMessage.GetIMessagingChannelUser()?.ChannelIdentityId;
                        break;
                    default:
                        break;
                }
            }

            DateTimeOffset dateTimeOffset = conversationMessage.CreatedAt;
            conversationMessage.Timestamp = dateTimeOffset.ToUnixTimeSeconds();
            conversationMessage.FrondendTimestamp = dateTimeOffset.ToUnixTimeMilliseconds();

            return conversationMessage;
        }

        private async Task ConversationLastActivity(
            Conversation conversation,
            ConversationMessage conversationMessage,
            bool trigerAssignment = true)
        {
            try
            {
                if (!trigerAssignment
                    && conversationMessage.Channel == ChannelTypes.LiveChat)
                {
                    return;
                }

                if (conversation.LastMessageChannel != conversationMessage.Channel)
                {
                    conversation.LastMessageChannel = conversationMessage.Channel;

                    BackgroundJob.Enqueue<IUserProfileService>(
                        x => x.SetFieldValueByFieldNameSafe(
                            conversation.UserProfile.Id,
                            "LastChannel",
                            conversationMessage.Channel,
                            true));
                }

                conversation.LastChannelIdentityId = conversationMessage.ChannelIdentityId;

                // BackgroundJob.Enqueue<IUserProfileService>(x => x.SetFieldValueByFieldNameSafe(conversation.UserProfile.Id, "LastChannel", conversationMessage.Channel, true));
                var isCSReply = await IsCSReply(conversation, conversationMessage);

                if (isCSReply
                    || conversationMessage.IsSentFromSleekflow)
                {
                    if (!conversation.UserProfile.LastContact.HasValue
                        || conversation.UserProfile.LastContact < conversationMessage.CreatedAt)
                    {
                        conversation.UserProfile.LastContact = conversationMessage.CreatedAt;
                        await _userProfileService.SetFieldValueByFieldNameSafe(
                            conversation.UserProfile,
                            "LastContact",
                            conversationMessage.CreatedAt.ToString("o"),
                            trigerAssignment);
                    }
                }

                // BackgroundJob.Enqueue<IUserProfileService>(x => x.SetFieldValueByFieldNameSafe(conversation.UserProfile.Id, "LastContact", conversation.UpdatedTime.ToString("o"), true));

                // if system message for web, don't log the last contact
                if (conversationMessage.Channel == ChannelTypes.LiveChat
                    && conversationMessage.Sender == null
                    && conversationMessage.WebClientSender == null)
                {
                    return;
                }

                if (isCSReply
                    && conversationMessage.DeliveryType == DeliveryType.Normal)
                {
                    // Add collaborator when reply
                    if (!string.IsNullOrEmpty(conversationMessage.SenderId)
                        && conversation.Assignee != null
                        && conversation.Assignee.IdentityId != conversationMessage.SenderId)
                    {
                        var sender = await _appDbContext.UserRoleStaffs
                            .FirstOrDefaultAsync(
                                x =>
                                    x.CompanyId == conversation.CompanyId
                                    && x.IdentityId == conversationMessage.SenderId);

                        var rolePermission = await _appDbContext.CompanyRolePermissions
                            .FirstOrDefaultAsync(
                                x =>
                                    x.CompanyId == conversation.CompanyId
                                    && x.StaffUserRole == sender.RoleType);

                        if (rolePermission != null
                            && rolePermission.Permission.AddAsCollaboratorWhenReply)
                        {
                            conversation.AdditionalAssignees = await _appDbContext.ConversationAdditionalAssignees
                                .Where(x => x.ConversationId == conversation.Id)
                                .ToListAsync();

                            if (conversation.AdditionalAssignees.All(x => x.AssigneeId != sender.Id))
                            {
                                conversation = await _conversationAssigneeService.AddAdditionalAssignees(
                                    conversation,
                                    new List<long>
                                    {
                                        sender.Id
                                    });
                            }
                        }
                    }

                    if (!conversation.AssigneeId.HasValue && trigerAssignment)
                    {
                        _logger.LogInformation(
                            "ConversationId: {ConversationId} didn't have assignee",
                            conversation?.Id);
                        var userProfile = await _appDbContext.UserProfiles
                            .FirstOrDefaultAsync(x => x.Id == conversation.UserProfileId);

                        var contactOwner = await _userProfileService.GetCustomFieldByFieldName(
                            userProfile,
                            "ContactOwner");

                        if (contactOwner != null
                            && !string.IsNullOrEmpty(contactOwner.Value))
                        {
                            conversation.Assignee = await _appDbContext.UserRoleStaffs
                                .FirstOrDefaultAsync(
                                    x =>
                                        x.CompanyId == userProfile.CompanyId
                                        && x.IdentityId == contactOwner.Value);

                            await ChangeConversationAssignee(conversation, conversation.Assignee, trigerAssignment);
                        }
                        else if (conversationMessage.Sender != null)
                        {
                            var staffRepliedLock = await _lockService.AcquireLockAsync(
                                $"staff-replied-as-owner:{conversation.Id}",
                                TimeSpan.FromSeconds(10));

                            if (staffRepliedLock != null)
                            {
                                var assignee = await _appDbContext.UserRoleStaffs
                                    .FirstOrDefaultAsync(x => x.IdentityId == conversationMessage.SenderId);

                                if (assignee.Id != 1)
                                {
                                    conversation.Assignee = assignee;

                                    if (conversation.Assignee != null)
                                    {
                                        await ChangeConversationAssignee(
                                            conversation,
                                            conversation.Assignee,
                                            trigerAssignment);
                                    }

                                    if (!conversation.AssignedTeamId.HasValue)
                                    {
                                        var assignedTeam = await _appDbContext.CompanyStaffTeams
                                            .FirstOrDefaultAsync(
                                                x => x.Members.Any(
                                                    y => y.Staff.IdentityId == conversationMessage.SenderId));

                                        if (assignedTeam != null)
                                        {
                                            await ChangeConversationAssignedTeam(
                                                conversation,
                                                assignedTeam,
                                                trigerAssignment);
                                        }
                                    }
                                }
                            }
                        }
                    }
                }

                if (!isCSReply)
                {
                    try
                    {
                        if (!conversationMessage.IsSentFromSleekflow)
                        {
                            if (!conversation.UserProfile.LastContactFromCustomers.HasValue
                                || conversation.UserProfile.LastContactFromCustomers < conversationMessage.CreatedAt)
                            {
                                conversation.UserProfile.LastContactFromCustomers = conversationMessage.CreatedAt;

                                await _userProfileService.SetFieldValueByFieldNameSafe(
                                    conversation.UserProfile,
                                    "LastContactFromCustomers",
                                    conversationMessage.CreatedAt.ToString("o"),
                                    true);
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(
                            ex,
                            "[{MethodName}] Update LastContactFromCustomers error for user profile {UserProfileId}: {ExceptionMessage}",
                            nameof(ConversationLastActivity),
                            conversation.UserProfileId,
                            ex.Message);


                        if (!conversation.UserProfile.LastContactFromCustomers.HasValue
                            || conversation.UserProfile.LastContactFromCustomers < conversationMessage.CreatedAt)
                        {
                            conversation.UserProfile.LastContactFromCustomers = conversationMessage.CreatedAt;

                            await _userProfileService.SetFieldValueByFieldNameSafe(
                                conversation.UserProfile,
                                "LastContactFromCustomers",
                                conversationMessage.CreatedAt.ToString("o"),
                                true);
                        }
                    }
                }

                if (conversation.ActiveStatus == ActiveStatus.Inactive)
                {
                    conversation.ActiveStatus = ActiveStatus.Active;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[{MethodName}] error for conversation {ConversationId}: {ExceptionString}",
                    nameof(ConversationLastActivity),
                    conversation?.Id,
                    ex.ToString());
            }
        }

        private ConversationMessage PrepareReplyMessage(
            Conversation conversation,
            ConversationMessage conversationMessage,
            string replyMessage)
        {
            var webcomeMessage = new ConversationMessage
            {
                ConversationId = conversation.Id,
                Channel = conversationMessage.Channel,
                MessageType = "text",
                MessageContent = replyMessage,
                facebookReceiver = conversation.facebookUser,
                whatsappReceiver = conversation.WhatsappUser,
                WebClientReceiver = conversation.WebClient,
                WeChatReceiver = conversation.WeChatUser,
                LineReceiver = conversation.LineUser,
                SMSReceiver = conversation.SMSUser,
                ViberReceiver = conversation.ViberUser,
                TelegramReceiver = conversation.TelegramUser,
                InstagramReceiver = conversation.InstagramUser,
                Whatsapp360DialogReceiver = conversation.WhatsApp360DialogUser,
                WhatsappCloudApiReceiver = conversation.WhatsappCloudApiUser,
                DeliveryType = DeliveryType.AutomatedMessage,
                IsSentFromSleekflow = true
            };

            return webcomeMessage;
        }

        public class Params
        {
            public string Name { get; set; }
        }

        public async Task<List<string>> FormatParamsWithPaymentUrl(
            UserProfile userProfile,
            List<string> template_params,
            string paymentUrl = null)
        {
            List<Params> formatedParams = new List<Params>();

            foreach (var myparams in template_params)
            {
                formatedParams.Add(
                    new Params
                    {
                        Name = myparams
                    });
            }

            // Broadcast payment link
            if (formatedParams
                    .Any(x => x.Name == "payment_url")
                && paymentUrl != null)
            {
                formatedParams
                    .Where(x => x.Name == "payment_url")
                    .ToList()
                    .ForEach(x => x.Name = paymentUrl);

                template_params = formatedParams
                    .Select(x => x.Name)
                    .ToList();
            }

            return await FormatParams(userProfile, template_params);
        }

        public async Task<List<string>> FormatParams(UserProfile userProfile, List<string> templateParams)
        {
            List<Params> formatedParams = new List<Params>();

            foreach (var myparams in templateParams)
            {
                formatedParams.Add(
                    new Params
                    {
                        Name = myparams
                    });
            }

            var dbContext = _dbContextService.GetDbContext();

            formatedParams
                .Where(x => x.Name == "@firstname")
                .ToList()
                .ForEach(x => x.Name = userProfile.FirstName);

            formatedParams
                .Where(x => x.Name == "@lastname")
                .ToList()
                .ForEach(x => x.Name = userProfile.LastName);

            formatedParams
                .Where(x => string.IsNullOrEmpty(x.Name))
                .ToList()
                .ForEach(x => x.Name = string.Empty);

            if (formatedParams.Any(x => x.Name == "@contactownerphonenumber"))
            {
                var staff = await dbContext.Conversations
                    .Where(x => x.UserProfileId == userProfile.Id)
                    .Select(x => x.Assignee)
                    .FirstOrDefaultAsync();

                var contactOwnerPhoneNumber = await _companyService.GetContactOwnerPhoneNumber(staff);


                formatedParams
                    .Where(x => x.Name == "@contactownerphonenumber")
                    .ToList()
                    .ForEach(x => x.Name = contactOwnerPhoneNumber);
            }

            var fields = formatedParams
                .Where(
                    x =>
                    x.Name != null
                    && x.Name.Contains("@"))
                .ToList();

            foreach (var fieldName in fields)
            {
                try
                {
                    var targetField = await (
                            from _field in dbContext.CompanyCustomUserProfileFields
                            where _field.FieldName.ToLower() == fieldName.Name.Replace("@", string.Empty).ToLower() &&
                                  _field.CompanyId == userProfile.CompanyId
                            select _field)
                        .FirstOrDefaultAsync();

                    var targetFieldValue = await (from _userProfile in dbContext.UserProfiles
                                                  join userProfileCustomField in dbContext.UserProfileCustomFields on _userProfile.Id equals
                                                      userProfileCustomField.UserProfileId
                                                  where userProfileCustomField.CompanyDefinedFieldId == targetField.Id &&
                                                        userProfileCustomField.UserProfileId == userProfile.Id &&
                                                        _userProfile.CompanyId == userProfile.CompanyId
                                                  select new
                                                  {
                                                      _userProfile,
                                                      userProfileCustomField
                                                  })
                        .FirstOrDefaultAsync();

                    if (targetFieldValue != null)
                    {
                        try
                        {
                            switch (targetField.Type)
                            {
                                case FieldDataType.TravisUser:
                                    var contactOwner = await dbContext.UserRoleStaffs
                                        .Include(x => x.Identity)
                                        .FirstOrDefaultAsync(
                                            x =>
                                                x.CompanyId == userProfile.CompanyId
                                                && x.IdentityId == targetFieldValue.userProfileCustomField.Value);

                                    var contactOwnerName = (!string.IsNullOrEmpty(contactOwner.Identity.FirstName))
                                        ? contactOwner.Identity.FirstName
                                        : contactOwner.Identity.DisplayName;

                                    formatedParams
                                        .Where(x => x.Name == fieldName.Name)
                                        .ToList()
                                        .ForEach(x => x.Name = contactOwnerName);

                                    break;
                                case FieldDataType.DateTime:
                                case FieldDataType.Date:
                                    var timeZoneId = await dbContext.CompanyCompanies
                                        .Where(x => x.Id == userProfile.CompanyId)
                                        .Select(x => x.TimeZoneInfoId)
                                        .FirstOrDefaultAsync();

                                    var timezoneInfo = TimeZoneHelper.GetTimeZoneById(timeZoneId);

                                    var datetime = Convert.ToDateTime(targetFieldValue.userProfileCustomField.Value)
                                        .ToUniversalTime();

                                    if (timezoneInfo != null)
                                    {
                                        datetime = datetime.AddHours(timezoneInfo.BaseUtcOffsetInHour);
                                    }

                                    if (targetField.Type == FieldDataType.DateTime)
                                    {
                                        formatedParams
                                            .Where(x => x.Name == fieldName.Name)
                                            .ToList()
                                            .ForEach(
                                            x => x.Name = datetime.ToString("dd MMM yyyy HH:mm"));
                                    }
                                    else
                                    {
                                        formatedParams
                                            .Where(x => x.Name == fieldName.Name)
                                            .ToList()
                                            .ForEach(x => x.Name = datetime.ToString("dd MMM yyyy"));
                                    }

                                    break;
                                case FieldDataType.ContactOwnerField:
                                    var converstion = await dbContext.Conversations
                                        .FirstOrDefaultAsync(x => x.UserProfileId == userProfile.Id);


                                    if (converstion == null
                                        || !converstion.AssigneeId.HasValue)
                                    {
                                        break;
                                    }

                                    var _contactOwner = await dbContext.UserRoleStaffs
                                        .FirstOrDefaultAsync(
                                            x =>
                                                x.CompanyId == userProfile.CompanyId &&
                                                x.Id == converstion.AssigneeId);

                                    switch (targetField.FieldName)
                                    {
                                        case "PositionOfContactOwner":
                                            formatedParams
                                                .Where(x => x.Name == fieldName.Name)
                                                .ToList()
                                                .ForEach(x => x.Name = _contactOwner.Position);

                                            break;
                                        case "MessageOfContactOwner":
                                            formatedParams
                                                .Where(x => x.Name == fieldName.Name)
                                                .ToList()
                                                .ForEach(x => x.Name = _contactOwner.Message);

                                            break;
                                    }

                                    break;

                                case FieldDataType.MultiLineText:
                                    var targetMultiLineTextValue =
                                        TrimTextValue(targetFieldValue.userProfileCustomField.Value);

                                    formatedParams
                                        .Where(x => x.Name == fieldName.Name)
                                        .ToList()
                                        .ForEach(x => x.Name = targetMultiLineTextValue);
                                    break;

                                case FieldDataType.SingleLineText:
                                    var targetSingleLineTextValue =
                                        TrimTextValue(targetFieldValue.userProfileCustomField.Value);

                                    formatedParams
                                        .Where(x => x.Name == fieldName.Name)
                                        .ToList()
                                        .ForEach(x => x.Name = targetSingleLineTextValue);
                                    break;
                                default:
                                    if (fieldName.Name.ToLower() == "@assignedteam")
                                    {
                                        var teamId = Convert.ToInt64(targetFieldValue.userProfileCustomField.Value);

                                        var teams = await dbContext.CompanyStaffTeams
                                            .FirstOrDefaultAsync(
                                                x =>
                                                    x.CompanyId == userProfile.CompanyId
                                                    && x.Id == teamId);


                                        formatedParams
                                            .Where(x => x.Name == fieldName.Name)
                                            .ToList()
                                            .ForEach(x => x.Name = teams.TeamName);
                                    }
                                    else
                                    {
                                        formatedParams
                                            .Where(x => x.Name == fieldName.Name)
                                            .ToList()
                                            .ForEach(x => x.Name = targetFieldValue.userProfileCustomField.Value?
                                                .Replace(Environment.NewLine, " ")
                                                .Replace("\n", " "));
                                    }

                                    break;
                            }
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(
                                ex,
                                "[{MethodName}] {TargetFieldName} Convert Error: {ExceptionString}",
                                nameof(FormatParams),
                                targetField.FieldName,
                                ex.ToString());


                            formatedParams
                                .Where(x => x.Name == fieldName.Name)
                                .ToList()
                                .ForEach(x => x.Name = targetFieldValue.userProfileCustomField.Value?
                                    .Replace(Environment.NewLine, " ")
                                    .Replace("\n", " "));
                        }
                    }
                    else
                    {
                        switch (targetField.Type)
                        {
                            case FieldDataType.TravisUser:
                                var _converstion = await dbContext.Conversations
                                    .Include(x => x.Assignee.Identity)
                                    .FirstOrDefaultAsync(x => x.UserProfileId == userProfile.Id);


                                if (_converstion == null ||
                                    !_converstion.AssigneeId.HasValue)
                                {
                                    formatedParams
                                        .Where(x => x.Name == fieldName.Name)
                                        .ToList()
                                        .ForEach(x => x.Name = string.Empty);

                                    break;
                                }

                                var contactOwnerName = (!string.IsNullOrEmpty(_converstion.Assignee.Identity.FirstName))
                                    ? _converstion.Assignee.Identity.FirstName
                                    : _converstion.Assignee.Identity.DisplayName;

                                formatedParams
                                    .Where(x => x.Name == fieldName.Name)
                                    .ToList()
                                    .ForEach(x => x.Name = contactOwnerName);

                                break;
                            case FieldDataType.ContactOwnerField:
                                var converstion = await dbContext.Conversations
                                    .FirstOrDefaultAsync(x => x.UserProfileId == userProfile.Id);


                                if (converstion == null ||
                                    !converstion.AssigneeId.HasValue)
                                {
                                    break;
                                }

                                var _contactOwner = await dbContext.UserRoleStaffs
                                    .FirstOrDefaultAsync(
                                        x =>
                                            x.CompanyId == userProfile.CompanyId &&
                                            x.Id == converstion.AssigneeId);

                                switch (targetField.FieldName)
                                {
                                    case "PositionOfContactOwner":
                                        formatedParams
                                            .Where(x => x.Name == fieldName.Name)
                                            .ToList()
                                            .ForEach(
                                                x =>
                                                    x.Name = string.IsNullOrEmpty(_contactOwner.Position) ?
                                                        string.Empty :
                                                        _contactOwner.Position);
                                        break;
                                    case "MessageOfContactOwner":
                                    case "UniqueLink":
                                        formatedParams
                                            .Where(x => x.Name == fieldName.Name)
                                            .ToList()
                                            .ForEach(
                                                x =>
                                                    x.Name = string.IsNullOrEmpty(_contactOwner.Message) ?
                                                        string.Empty :
                                                        _contactOwner.Message);
                                        break;
                                }

                                break;
                            default:
                                formatedParams
                                    .Where(x => x.Name == fieldName.Name)
                                    .ToList()
                                    .ForEach(x => x.Name = string.Empty);

                                break;
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(
                        ex,
                        "Company {CompanyId} could not format params for user profile {UserProfileId}. Params: {@Params}",
                        userProfile.CompanyId,
                        userProfile.Id,
                        templateParams);
                }
            }

            foreach (var name in formatedParams)
            {
                if (name.Name == null)
                {
                    name.Name = string.Empty;
                }
            }

            return formatedParams.Select(x => x.Name).ToList();
        }

        /// <summary>
        /// Replace new lines, tabs and spaces to at most 3 consecutive spaces
        ///
        /// Resource: https://developers.facebook.com/docs/whatsapp/message-templates/creation/
        /// Whatsapp template param cannot contain newline or tab characters, and cannot have more than 4 consecutive spaces
        /// </summary>
        /// <param name="value">Value.</param>
        /// <returns>Trimmed value.</returns>
        private static string TrimTextValue(string value)
        {
            return value == null
                ? null
                : Regex.Replace(
                    Regex.Replace(
                        value,
                        @"\r|\n|\t",
                        " "),
                    @"\ {4,}",
                    "   ");
        }

        public async Task RemoveScheduledMessage(string companyId, DeleteMessageInput input)
        {
            foreach (var messageId in input.MessageIds)
            {
                var scheduledMessage = await _appDbContext.ConversationMessages
                    .Include(x => x.Conversation)
                    .Include(x => x.UploadedFiles)
                    .FirstOrDefaultAsync(
                        x =>
                            x.CompanyId == companyId
                            && x.Id == messageId
                            && x.Status == MessageStatus.Scheduled);

                if (scheduledMessage == null)
                {
                    continue;
                }

                scheduledMessage.Status = MessageStatus.Deleted;
                await _appDbContext.SaveChangesAsync();

                await _signalRService.SignalROnMessageStatusChanged(scheduledMessage);

                BackgroundJob.Delete(scheduledMessage.JobId);
            }
        }

        public async Task DeleteMessage(string companyId, DeleteMessageInput input)
        {
            var conversationMessages = await _appDbContext.ConversationMessages
                .Where(x => input.MessageIds.Contains(x.Id) && x.CompanyId == companyId)
                .ToListAsync();

            var toBeUpdatedConversationIds = conversationMessages
                .Select(x => x.ConversationId)
                .DistinctBy(x => x)
                .ToList();

            await _appDbContext.ConversationMessages
                .Where(
                    x => input.MessageIds.Contains(x.Id)
                         && x.CompanyId == companyId)
                .ExecuteDeleteAsync();

            await _appDbContext.SaveChangesAsync();

            foreach (var conversationId in toBeUpdatedConversationIds)
            {
                var lastConversationMessageId =
                    (await _appDbContext.ConversationMessages
                        .Where(x => x.ConversationId == conversationId && x.CompanyId == companyId)
                        .OrderByDescending(x => x.CreatedAt)
                        .FirstOrDefaultAsync())
                    .Id;
                var conversation = await _appDbContext.Conversations.FirstOrDefaultAsync(x => x.Id == conversationId);
                conversation.LastMessageId = lastConversationMessageId;
                await _appDbContext.SaveChangesAsync();
            }
        }

        public async Task<IQueryable<Conversation>> GetConversations(
            string companyId,
            Staff staff,
            string status = "all",
            string assignedTo = "all",
            string channels = null,
            DateTime? afterUpdatedAt = null,
            DateTime? afterModifiedAt = null,
            string channelIds = null,
            string tags = null,
            long? teamId = null,
            bool? isTeamUnassigned = null,
            bool? isUnread = null,
            string orderBy = null,
            string version = "1",
            bool? isAssigned = null,
            bool? isCollaborator = null)
        {
            var channelList = new List<string>();

            if (!string.IsNullOrEmpty(channels))
            {
                channelList = channels.Split(",").ToList();
            }

            var channelIdList = new List<string>();

            if (!string.IsNullOrEmpty(channelIds))
            {
                channelIdList = channelIds.Split(",").ToList();
            }

            var tagsList = new List<string>();

            if (!string.IsNullOrEmpty(tags))
            {
                tagsList = tags.Split(",").ToList();
            }

            var to = assignedTo;

            var teamMemberIds = new List<long>();

            if (staff.RoleType == StaffUserRole.TeamAdmin)
            {
                var teamAdminTeamIds = await _appDbContext.CompanyStaffTeams
                    .Where(x => x.Members.Any(member => member.StaffId == staff.Id) && x.CompanyId == staff.CompanyId)
                    .Select(x => x.Id)
                    .ToListAsync();

                teamMemberIds = await _appDbContext.CompanyTeamMembers
                    .Where(x => teamAdminTeamIds.Contains(x.CompanyTeamId) && x.StaffId != staff.Id)
                    .Select(x => x.StaffId)
                    .Distinct()
                    .ToListAsync();
            }


            var dbContext = _dbContextService.GetDbContext();

            var conversations = dbContext.Conversations
                .Where(
                    x =>
                        x.CompanyId == companyId
                        && x.ActiveStatus == ActiveStatus.Active)
                .WhereIf(
                    status != "all" && status != "scheduled",
                    conversation => conversation.Status == status.ToLower())
                .WhereIf(
                    status == "scheduled",
                    conversation => conversation.ChatHistory.Any(x => x.Status == MessageStatus.Scheduled))
                .WhereIf(
                    channelList.Count > 0 && !channelList.Contains("all"),
                    conversation => conversation.ChatHistory.Any(x => channelList.Contains(x.Channel)))
                .WhereIf(afterUpdatedAt.HasValue, conversation => conversation.UpdatedTime > afterUpdatedAt)
                .WhereIf(afterModifiedAt.HasValue, conversation => conversation.ModifiedAt > afterModifiedAt)
                .WhereIf(isAssigned.HasValue && isAssigned.Value, conversation => conversation.AssigneeId.HasValue)
                .WhereIf(isAssigned.HasValue && !isAssigned.Value, conversation => !conversation.AssigneeId.HasValue)
                .WhereIf(
                    isCollaborator.HasValue && isCollaborator.Value,
                    conversation =>
                        conversation.AdditionalAssignees.Any(
                            x => x.CompanyId == companyId && x.Assignee.IdentityId == to));

            if (tagsList.Count > 0)
            {
                var hashtagsList = await dbContext.CompanyDefinedHashtags
                    .Where(
                        x =>
                            x.CompanyId == companyId
                            && (tagsList.Contains(x.Hashtag) || tagsList.Contains(x.Id)))
                    .Select(x => x.Id)
                    .ToListAsync();

                conversations = conversations.Where(
                    conversation => conversation.conversationHashtags
                        .Any(
                            x => x.CompanyId == companyId
                                 && hashtagsList.Contains(x.HashtagId)));
            }

            if (isUnread.HasValue)
            {
                var personalUnreadConversationIds = dbContext.ConversationUnreadRecords
                    .Where(
                        x =>
                            x.CompanyId == companyId
                            && x.StaffId == staff.Id
                            && x.NotificationDeliveryStatus != NotificationDeliveryStatus.Read)
                    .Select(x => x.ConversationId);

                if (isUnread.Value)
                {
                    conversations = from conversation in conversations
                                    where conversation.UnreadMessageCount > 0
                                          || personalUnreadConversationIds.Contains(conversation.Id)
                                    select conversation;
                }
                else
                {
                    conversations = from conversation in conversations
                                    where conversation.UnreadMessageCount <= 0
                                          || personalUnreadConversationIds.Contains(conversation.Id)
                                    select conversation;
                }
            }

            switch (assignedTo)
            {
                case "all":
                    switch (staff.RoleType)
                    {
                        case StaffUserRole.Admin:
                        case StaffUserRole.DemoAdmin:
                            break;
                        case StaffUserRole.TeamAdmin:
                        case StaffUserRole.Staff:
                            var managedTeam = await dbContext.CompanyStaffTeams
                                .Where(x => x.Members.Any(m => m.StaffId == staff.Id))
                                .Select(x => x.Id)
                                .ToListAsync();

                            if (staff.RoleType == StaffUserRole.TeamAdmin)
                            {
                                var staffAccessControlAggregate = await _accessControlAggregationService.GetStaffAccessControlAggregateAsync(staff);
                                var teamAdminSpec = new TeamAdminConversationSpecification(staffAccessControlAggregate);
                                conversations = conversations.Where(teamAdminSpec.ToExpression());
                            }
                            else
                            {
                                var isShowDefaultChannelMessagesOnly = false;

                                var staffPermission = await _appDbContext.CompanyRolePermissions
                                    .FirstOrDefaultAsync(
                                        x =>
                                            x.CompanyId == companyId
                                            && x.StaffUserRole == staff.RoleType);
                                if (staffPermission != null)
                                {
                                    isShowDefaultChannelMessagesOnly =
                                        staffPermission.Permission.IsShowDefaultChannelMessagesOnly;
                                }

                                if (isShowDefaultChannelMessagesOnly)
                                {
                                    conversations = conversations.Where(
                                        conversation =>
                                            conversation.AssigneeId == staff.Id //Assigned to the staff
                                            || (conversation.AssignedTeamId.HasValue
                                                && ((managedTeam.Contains(conversation.AssignedTeamId.Value)
                                                     && !conversation.AssigneeId.HasValue)
                                                    || conversation.AdditionalAssignees
                                                        .Any(
                                                            x => x.CompanyId == companyId
                                                                 && x.AssigneeId == staff.Id)))
                                    );
                                }
                                else
                                {
                                    conversations = conversations.Where(
                                        conversation =>
                                            conversation.AssigneeId == staff.Id //Assigned to the staff
                                            || (!conversation.AssignedTeamId.HasValue && !conversation.AssigneeId.HasValue) //Assigned to neither team nor staff
                                            || (conversation.AssignedTeamId.HasValue
                                                && managedTeam.Contains(conversation.AssignedTeamId.Value)
                                                && !conversation.AssigneeId.HasValue) //Assigned to the team, not to others
                                            || conversation.AdditionalAssignees
                                                .Any(
                                                    x => x.CompanyId == companyId
                                                         && x.AssigneeId == staff.Id) //Assigned to collaborator
                                    );
                                }
                            }

                            break;
                    }

                    break;
                case "unassigned":
                    conversations = from conversation in conversations
                                    where conversation.AssigneeId == null
                                    select conversation;

                    switch (staff.RoleType)
                    {
                        case StaffUserRole.TeamAdmin:
                        case StaffUserRole.Staff:
                            var managedTeam = await dbContext.CompanyStaffTeams
                                .Where(x => x.Members.Any(m => m.StaffId == staff.Id))
                                .Select(x => x.Id)
                                .ToListAsync();

                            if (managedTeam.Count == 0)
                            {
                                break;
                            }

                            var isShowDefaultChannelMessagesOnly = false;

                            var staffPermission = await dbContext.CompanyRolePermissions
                                .FirstOrDefaultAsync(
                                    x =>
                                        x.CompanyId == companyId
                                        && x.StaffUserRole == staff.RoleType);
                            if (staffPermission != null)
                            {
                                isShowDefaultChannelMessagesOnly =
                                    staffPermission.Permission.IsShowDefaultChannelMessagesOnly;
                            }

                            if (isShowDefaultChannelMessagesOnly)
                            {
                                conversations = conversations.Where(
                                    conversation =>
                                        conversation.AssignedTeamId.HasValue
                                        && (managedTeam.Contains(conversation.AssignedTeamId.Value)
                                            || conversation.AdditionalAssignees
                                                .Any(
                                                    x => x.CompanyId == companyId
                                                         && x.AssigneeId == staff.Id))
                                );
                            }
                            else
                            {
                                conversations = conversations.Where(
                                    conversation =>
                                        (!conversation.AssignedTeamId.HasValue && !conversation.AssigneeId.HasValue)
                                        || ((conversation.AssignedTeamId.HasValue
                                             && managedTeam.Contains(conversation.AssignedTeamId.Value))
                                            || conversation.AdditionalAssignees
                                                .Any(
                                                    x => x.CompanyId == companyId
                                                         && x.AssigneeId == staff.Id)));
                            }

                            break;
                    }

                    break;
                case "mentioned":
                    conversations = conversations.Where(
                        conversation => conversation.ChatHistory.Any(
                            y => y.Channel == ChannelTypes.Note
                                 && y.MessageAssignee.IdentityId == staff.IdentityId
                                 && y.UpdatedAt > DateTime.UtcNow.AddDays(-2))
                    );

                    break;
                case "team":
                    switch (staff.RoleType)
                    {
                        case StaffUserRole.Admin:
                        case StaffUserRole.DemoAdmin:
                        case StaffUserRole.TeamAdmin:
                            conversations = conversations.Where(conversation => conversation.AssignedTeamId == teamId);

                            break;
                        case StaffUserRole.Staff:
                            conversations = conversations.Where(
                                conversation => conversation.AssignedTeamId == teamId &&
                                                (!conversation.AssigneeId.HasValue ||
                                                 conversation.AssigneeId == staff.Id));

                            break;
                    }

                    if (isTeamUnassigned.HasValue)
                    {
                        conversations = conversations.Where(conversation => !conversation.AssigneeId.HasValue);
                    }

                    break;
                case "collaborator":
                    conversations = conversations
                        .Where(
                            conversation =>
                                conversation.AdditionalAssignees.Any(
                                    x => x.CompanyId == companyId && x.AssigneeId == staff.Id));
                    break;
                case "bookmarked":
                    conversations = conversations
                        .Where(conversation => conversation.ConversationBookmarks.Any(x => x.StaffId == staff.Id));
                    break;
                default:
                    if (isCollaborator.HasValue && isCollaborator.Value)
                    {
                        break;
                    }

                    if (assignedTo == "you")
                    {
                        assignedTo = staff.IdentityId;
                    }

                    switch (staff.RoleType)
                    {
                        case StaffUserRole.Admin:
                        case StaffUserRole.DemoAdmin:
                            break;
                        case StaffUserRole.TeamAdmin:
                            var isManagedTeam = await dbContext.CompanyStaffTeams
                                .AnyAsync(
                                    x =>
                                        x.Members.Any(m => m.StaffId == staff.Id)
                                        && x.Members.Any(m => m.Staff.IdentityId == assignedTo));
                            if (!isManagedTeam)
                            {
                                if (assignedTo != staff.IdentityId)
                                {
                                    throw new Exception($"{assignedTo} is not your teammates");
                                }
                            }

                            break;
                        case StaffUserRole.Staff:
                            if (assignedTo != staff.IdentityId)
                            {
                                throw new Exception($"You cannot access conversations assigned to {assignedTo}");
                            }

                            var managedTeam = await dbContext.CompanyStaffTeams
                                .Where(x => x.Members.Any(m => m.StaffId == staff.Id))
                                .Select(x => x.Id)
                                .ToListAsync();

                            if (managedTeam.Count == 0)
                            {
                                break;
                            }

                            var isShowDefaultChannelMessagesOnly = false;

                            var staffPermission = await dbContext.CompanyRolePermissions
                                .FirstOrDefaultAsync(
                                    x =>
                                        x.CompanyId == companyId
                                        && x.StaffUserRole == staff.RoleType);

                            if (staffPermission != null)
                            {
                                isShowDefaultChannelMessagesOnly =
                                    staffPermission.Permission.IsShowDefaultChannelMessagesOnly;
                            }

                            if (isShowDefaultChannelMessagesOnly)
                            {
                                conversations = conversations.Where(
                                    conversation =>
                                        conversation.AssigneeId == staff.Id //Assigned to the staff
                                        || (conversation.AssignedTeamId.HasValue
                                        && ((managedTeam.Contains(conversation.AssignedTeamId.Value)
                                             && !conversation.AssigneeId.HasValue)
                                            || conversation.AdditionalAssignees
                                                .Any(
                                                    x => x.CompanyId == companyId
                                                         && x.AssigneeId == staff.Id))));
                            }
                            else
                            {
                                conversations = conversations.Where(
                                    conversation =>
                                        conversation.AssigneeId == staff.Id //Assigned to the staff
                                        || (!conversation.AssignedTeamId.HasValue &&
                                            !conversation.AssigneeId.HasValue) //Assigned to neither team nor staff
                                        || (conversation.AssignedTeamId.HasValue
                                            && managedTeam.Contains(conversation.AssignedTeamId.Value)
                                            && !conversation.AssigneeId.HasValue) //Assigned to the team, not to others
                                        || conversation.AdditionalAssignees
                                            .Any(
                                                x => x.CompanyId == companyId
                                                     && x.AssigneeId == staff.Id)); //Assigned to collaborator
                            }

                            break;
                    }

                    var assignedToId = await dbContext.UserRoleStaffs
                        .Where(
                            x =>
                                x.CompanyId == companyId
                                && x.IdentityId == assignedTo)
                        .Select(x => x.Id)
                        .FirstOrDefaultAsync();

                    conversations = conversations
                        .WhereIf(
                            version == "1",
                            conversation =>
                                conversation.AssigneeId == assignedToId
                                || conversation.AdditionalAssignees.Any(
                                    x => x.CompanyId == companyId && x.AssigneeId == assignedToId))
                        .WhereIf(
                            version == "2",
                            conversation => conversation.AssigneeId == assignedToId);

                    break;
            }

            // Filter channel at last
            if (!string.IsNullOrEmpty(channelIds))
            {
                try
                {
                    if (channels.Contains(ChannelTypes.WhatsappTwilio)
                        && !channels.Contains(ChannelTypes.Whatsapp360Dialog)
                        && !channels.Contains(ChannelTypes.WhatsappCloudApi))
                    {
                        var newChannelIds = new List<string>();
                        var newInstanceSender = new List<string>();

                        foreach (var myChannelId in channelIdList)
                        {
                            var twilioInstance = myChannelId.Split(";", StringSplitOptions.RemoveEmptyEntries);
                            newChannelIds.Add(twilioInstance[0]);

                            if (twilioInstance.Count() > 1)
                            {
                                newInstanceSender.Add(twilioInstance[1].Replace(": ", ":+"));
                            }
                        }

                        if (newInstanceSender.Count > 0)
                        {
                            conversations = from conversation in conversations
                                            where conversation.WhatsAppSenderHistories.Any(
                                                x =>
                                                    newInstanceSender.Contains(x.InstanceSender) &&
                                                    newChannelIds.Contains(x.InstanceId))
                                            select conversation;
                        }
                        else
                        {
                            conversations = from conversation in conversations
                                            where conversation.WhatsAppSenderHistories.Any(
                                                x => newChannelIds.Contains(x.InstanceId))
                                            select conversation;
                        }
                    }

                    if (channels.Contains(ChannelTypes.Facebook))
                    {
                        conversations = from conversation in conversations
                                        where channelIdList.Contains(conversation.facebookUser.pageId)
                                        select conversation;
                    }

                    if (channels.Contains(ChannelTypes.Instagram))
                    {
                        conversations = from conversation in conversations
                                        where channelIdList.Contains(conversation.InstagramUser.InstagramPageId)
                                        select conversation;
                    }

                    if (channels.Contains(ChannelTypes.Whatsapp360Dialog))
                    {
                        var whatsapp360DialogIds = new List<long?>();

                        foreach (var channelId in channelIdList)
                        {
                            var good = long.TryParse(channelId, out var id);

                            if (good)
                            {
                                whatsapp360DialogIds.Add(id);
                            }
                        }

                        conversations = from conversation in conversations
                                        where whatsapp360DialogIds.Contains(conversation.WhatsApp360DialogUser.ChannelId)
                                        select conversation;
                    }

                    if (channels.Contains(ChannelTypes.WhatsappCloudApi))
                    {
                        conversations = from conversation in conversations
                                        where channelIdList.Contains(conversation.WhatsappCloudApiUser.WhatsappChannelPhoneNumber)
                                        select conversation;
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(
                        ex,
                        "[{MethodName}] Company {CompanyId} unable to parse TargetedChannelModel {ExceptionMessage}",
                        nameof(GetConversations),
                        companyId,
                        ex.Message);
                }
            }

            return orderBy switch
            {
                "asc" => conversations
                    .OrderByDescending(
                        conversation => conversation.ConversationBookmarks.Any(x => x.StaffId == staff.Id))
                    .ThenBy(conversation => conversation.UpdatedTime),
                "desc" => conversations
                    .OrderByDescending(
                        conversation => conversation.ConversationBookmarks.Any(x => x.StaffId == staff.Id))
                    .ThenByDescending(conversation => conversation.UpdatedTime),
                "ascUnreplied" => conversations.OrderByDescending(x => x.IsUnreplied).ThenBy(x => x.UpdatedTime),
                "descUnreplied" => conversations.OrderByDescending(x => x.IsUnreplied)
                    .ThenByDescending(x => x.UpdatedTime),
                _ => conversations.OrderByDescending(conversation => conversation.UpdatedTime)
            };
        }

        public IQueryable<ConversationMessage> GetConversationMessage(
            string conversationId,
            string companyId,
            long? beforeMessageId = null,
            long? afterMessageId = null,
            long? beforeTimestamp = null,
            long? afterTimestamp = null,
            string channel = null,
            string channelIds = null,
            bool? IsFromUser = null)
        {
            var result = _appDbContext.ConversationMessages
                .Where(conversationMessages => conversationMessages.CompanyId == companyId);

            result = result.WhereIf(
                !string.IsNullOrEmpty(conversationId) && conversationId != "all",
                conversationMessages => conversationMessages.ConversationId == conversationId);

            // Deprecated message id fetch
            if (!beforeMessageId.HasValue && !afterMessageId.HasValue && afterTimestamp == null && beforeTimestamp == null)
            {
                beforeMessageId = result
                    .OrderByDescending(x => x.Id)
                    .Take(1)
                    .Select(x => x.Id)
                    .FirstOrDefault();
                if (beforeMessageId.HasValue)
                {
                    beforeMessageId += 1;
                }
            }

            if (beforeMessageId.HasValue
                && afterMessageId.HasValue)
            {
                result = result.Where(m => m.Id < beforeMessageId && m.Id > afterMessageId);
            }
            else if (beforeMessageId.HasValue)
            {
                result = result.Where(m => m.Id < beforeMessageId);
            }
            else if (afterMessageId.HasValue)
            {
                result = result.Where(m => m.Id > afterMessageId);
            }

            result = result.WhereIf(afterTimestamp.HasValue, m => afterTimestamp <= m.Timestamp);
            result = result.WhereIf(beforeTimestamp.HasValue, m => m.Timestamp <= beforeTimestamp);

            var channelIdList = new List<string>();

            if (!string.IsNullOrEmpty(channelIds))
            {
                channelIdList = channelIds
                    .Split(",")
                    .Select(x => x.Trim()).ToList();
            }

            if (!string.IsNullOrEmpty(channel))
            {
                try
                {
                    result = result
                        .Where(x => x.Channel == channel || x.Channel == "note");
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex,
                        "[{MethodName}] Company {CompanyId} error filtering query by channel. {ExceptionMessage}",
                        nameof(GetConversationMessage),
                        companyId,
                        ex.Message);
                }
            }

            if (channelIdList.Count > 0)
            {
                try
                {
                    if (channel == ChannelTypes.WhatsappTwilio)
                    {
                        var newChannelIds = new List<string>();
                        var newInstanceSender = new List<string>();

                        foreach (var myChannelId in channelIdList)
                        {
                            var twilioInstance = myChannelId.Split(";", StringSplitOptions.RemoveEmptyEntries);
                            newChannelIds.Add(twilioInstance[0]);

                            if (twilioInstance.Length > 1)
                            {
                                newInstanceSender.Add(twilioInstance[1].Replace(": ", ":+"));
                            }
                        }

                        result = result.Where(
                            conversationMessage =>
                                newChannelIds.Contains(conversationMessage.whatsappReceiver.InstanceId)
                                || newChannelIds.Contains(conversationMessage.whatsappSender.InstanceId));

                        if (newInstanceSender.Count > 0)
                        {
                            result = result.Where(
                                conversationMessage =>
                                    newInstanceSender.Contains(conversationMessage.whatsappReceiver.InstaneSender)
                                    || newInstanceSender.Contains(conversationMessage.whatsappSender.InstaneSender));
                        }
                    }
                    else if (channel == ChannelTypes.Whatsapp360Dialog)
                    {
                        result = result.Where(
                            x => channelIdList.Contains(x.Whatsapp360DialogSender.ChannelWhatsAppPhoneNumber)
                                 || channelIdList.Contains(x.Whatsapp360DialogReceiver.ChannelWhatsAppPhoneNumber));
                    }
                    else if (channel == ChannelTypes.WhatsappCloudApi)
                    {
                        result = result.Where(x => channelIdList.Contains(x.ChannelIdentityId));
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex,
                        "[{MethodName}] Company {CompanyId} error filtering query by channelIdList: {@ChannelIdList}. {ExceptionMessage}",
                        nameof(GetConversationMessage),
                        companyId,
                        channelIdList,
                        ex.Message);
                }
            }

            if (IsFromUser.HasValue)
            {
                result = result.Where(
                    conversationMessage => conversationMessage.IsSentFromSleekflow == !IsFromUser.Value);
            }

            return result;
        }

        public IOrderedQueryable<ConversationMessage> GetConversationMessagesHistory(
            string companyId,
            string staffId = null,
            string status = null,
            string channels = null,
            DateTime? messageFrom = null,
            DateTime? messageTo = null,
            string channelIds = null,
            string tags = null,
            long? teamId = null,
            bool? isTeamUnassigned = null)
        {
            var channelList = new List<string>();

            if (!string.IsNullOrEmpty(channels))
            {
                channelList = channels.Split(",").ToList();
            }

            var channelIdList = new List<string>();

            if (!string.IsNullOrEmpty(channelIds))
            {
                channelIdList = channelIds.Split(",").ToList();
            }

            var tagsList = new List<string>();

            if (!string.IsNullOrEmpty(tags))
            {
                tagsList = tags.Split(",").ToList();
            }

            var conversationMessages =
                from conversationMessage in _appDbContext.ConversationMessages
                where conversationMessage.CompanyId == companyId
                      && _appDbContext.Conversations
                          .Where(
                              x =>
                                  x.CompanyId == companyId
                                  && x.UserProfile.ActiveStatus == ActiveStatus.Active)
                          .Select(x => x.Id)
                          .Contains(conversationMessage.ConversationId)
                orderby conversationMessage.Timestamp
                select conversationMessage;

            if (!string.IsNullOrEmpty(status))
            {
                conversationMessages =
                    from conversationMessage in _appDbContext.ConversationMessages
                    where _appDbContext.Conversations
                        .Where(
                            x =>
                                x.CompanyId == companyId
                                && x.Status == status)
                        .Select(x => x.Id)
                        .Contains(conversationMessage.ConversationId)
                    orderby conversationMessage.Timestamp
                    select conversationMessage;
            }

            if (!string.IsNullOrEmpty(staffId))
            {
                if (staffId == "unassigned")
                {
                    conversationMessages =
                        from conversationMessage in conversationMessages
                        where _appDbContext.Conversations
                            .Where(
                                x =>
                                    x.CompanyId == companyId
                                    && !x.AssigneeId.HasValue)
                            .Select(x => x.Id)
                            .Contains(conversationMessage.ConversationId)
                        orderby conversationMessage.Timestamp
                        select conversationMessage;
                }
                else
                {
                    conversationMessages =
                        from conversationMessage in conversationMessages
                        where _appDbContext.Conversations
                                  .Where(
                                      x =>
                                          x.CompanyId == companyId
                                          && x.Assignee.IdentityId == staffId)
                                  .Select(x => x.Id)
                                  .Contains(conversationMessage.ConversationId)
                              || _appDbContext.Conversations
                                  .Where(
                                      x =>
                                          x.CompanyId == companyId
                                          && x.AdditionalAssignees.Any(x => x.Assignee.IdentityId == staffId && x.CompanyId == companyId))
                                  .Select(x => x.Id)
                                  .Contains(conversationMessage.ConversationId)
                        orderby conversationMessage.Timestamp
                        select conversationMessage;
                }
            }
            else if (teamId.HasValue)
            {
                var results = _appDbContext.CompanyTeamMembers
                    .Where(y => y.CompanyTeamId == teamId.Value)
                    .Select(y => y.StaffId);

                conversationMessages =
                    from conversationMessage in conversationMessages
                    where _appDbContext.Conversations
                              .Where(
                                  x =>
                                      x.CompanyId == companyId
                                      && x.AssignedTeam.Id == teamId.Value)
                              .Select(x => x.Id)
                              .Contains(conversationMessage.ConversationId)
                          || _appDbContext.Conversations
                              .Where(
                                  x =>
                                      x.CompanyId == companyId
                                      && results.Contains(x.AssigneeId.Value))
                              .Select(x => x.Id)
                              .Contains(conversationMessage.ConversationId)
                    orderby conversationMessage.Timestamp
                    select conversationMessage;

                if (isTeamUnassigned.HasValue
                    && isTeamUnassigned.Value)
                {
                    conversationMessages =
                        from conversationMessage in conversationMessages
                        where _appDbContext.Conversations
                            .Where(
                                x =>
                                    x.CompanyId == companyId
                                    && !x.AssigneeId.HasValue)
                            .Select(x => x.Id)
                            .Contains(conversationMessage.ConversationId)
                        orderby conversationMessage.Timestamp
                        select conversationMessage;
                }
            }

            if (!string.IsNullOrEmpty(channels))
            {
                conversationMessages =
                    from conversationMessage in conversationMessages
                    where channelList.Contains(conversationMessage.Channel)
                    orderby conversationMessage.Timestamp
                    select conversationMessage;
            }

            if (!string.IsNullOrEmpty(channelIds))
            {
                try
                {
                    if (channels.Contains(ChannelTypes.WhatsappTwilio)
                        && !channels.Contains(ChannelTypes.Whatsapp360Dialog)
                        && !channels.Contains(ChannelTypes.WhatsappCloudApi))
                    {
                        var newChannelIds = new List<string>();
                        var newInstanceSender = new List<string>();

                        foreach (var myChannelId in channelIdList)
                        {
                            var twilioInstance = myChannelId.Split(";", StringSplitOptions.RemoveEmptyEntries);
                            newChannelIds.Add(twilioInstance[0]);

                            if (twilioInstance.Count() > 1)
                            {
                                newInstanceSender.Add(twilioInstance[1].Replace(": ", ":+"));
                            }
                        }

                        conversationMessages =
                            from conversationMessage in conversationMessages
                            where newChannelIds.Contains(conversationMessage.whatsappReceiver.InstanceId)
                                  || newChannelIds.Contains(conversationMessage.whatsappSender.InstanceId)
                            orderby conversationMessage.Timestamp
                            select conversationMessage;

                        if (newInstanceSender.Count > 0)
                        {
                            conversationMessages =
                                from conversationMessage in conversationMessages
                                where newChannelIds.Contains(conversationMessage.whatsappReceiver.InstanceId)
                                      || newChannelIds.Contains(conversationMessage.whatsappSender.InstanceId)
                                orderby conversationMessage.Timestamp
                                select conversationMessage;
                        }
                    }

                    if (channels.Contains(ChannelTypes.Facebook))
                    {
                        conversationMessages =
                            from conversationMessage in conversationMessages
                            where channelIdList.Contains(conversationMessage.facebookReceiver.pageId)
                                  || channelIdList.Contains(conversationMessage.facebookSender.pageId)
                            orderby conversationMessage.Timestamp
                            select conversationMessage;
                    }

                    if (channels.Contains(ChannelTypes.Whatsapp360Dialog))
                    {
                        var whatsapp360DialogIds = new List<long?>();

                        foreach (var channelId in channelIdList)
                        {
                            var good = long.TryParse(channelId, out var id);

                            if (good)
                            {
                                whatsapp360DialogIds.Add(id);
                            }
                        }

                        conversationMessages =
                            from conversationMessage in conversationMessages
                            where whatsapp360DialogIds.Contains(conversationMessage.Whatsapp360DialogReceiver.ChannelId)
                                  || whatsapp360DialogIds.Contains(conversationMessage.Whatsapp360DialogSender.ChannelId)
                            orderby conversationMessage.Timestamp
                            select conversationMessage;
                    }

                    if (channels.Contains(ChannelTypes.WhatsappCloudApi))
                    {
                        conversationMessages =
                            from conversationMessage in conversationMessages
                            where channelIds.Contains(conversationMessage.ChannelIdentityId)
                            orderby conversationMessage.Timestamp
                            select conversationMessage;
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex,
                        "[{MethodName}] Company {CompanyId} error filtering query by channelIds: {ChannelIds}. {ExceptionMessage}",
                        nameof(GetConversationMessagesHistory),
                        companyId,
                        channelIds,
                        ex.Message);
                }
            }

            if (tagsList.Count > 0)
            {
                var hashTagsResultQ =
                    from conversation in _appDbContext.Conversations
                    where conversation.CompanyId == companyId
                    select conversation;

                foreach (var tag in tagsList)
                {
                    hashTagsResultQ =
                        from _hashTagsResult in hashTagsResultQ
                        where _hashTagsResult.conversationHashtags
                            .Select(x => x.Hashtag.Hashtag.ToLower())
                            .Contains(tag.ToLower())
                        select _hashTagsResult;
                }

                var hashTagsResult = hashTagsResultQ.Select(x => x.Id);

                conversationMessages =
                    from conversationMessage in conversationMessages
                    where hashTagsResult.Contains(conversationMessage.ConversationId)
                    orderby conversationMessage.Timestamp
                    select conversationMessage;
            }

            if (messageFrom.HasValue)
            {
                conversationMessages =
                    from conversationMessage in conversationMessages
                    where messageFrom.Value <= conversationMessage.CreatedAt
                    orderby conversationMessage.Timestamp
                    select conversationMessage;
            }

            if (messageTo.HasValue)
            {
                conversationMessages =
                    from conversationMessage in conversationMessages
                    where messageTo.Value > conversationMessage.CreatedAt
                    orderby conversationMessage.Timestamp
                    select conversationMessage;
            }

            return conversationMessages;
        }

        public async Task AckConversationMessageReceived(string companyId, long staffId, List<long> messageIds)
        {
            await _appDbContext.ConversationUnreadRecords.Where(
                    x => x.CompanyId == companyId
                         && x.StaffId == staffId
                         && messageIds.Contains(x.MessageId)
                         && x.NotificationDeliveryStatus == NotificationDeliveryStatus.Sent)
                .ExecuteUpdateAsync(
                    unreadRecord =>
                        unreadRecord
                            .SetProperty(r => r.DeliveredAt, DateTime.Now)
                            .SetProperty(r => r.NotificationDeliveryStatus, NotificationDeliveryStatus.Delivered));
        }

        public async Task ReadConversationPersonally(string conversationId, Staff companyUser)
        {
            var myLock = await _lockService.AcquireLockAsync(
                $"ReadConversationPersonally:{conversationId}:{companyUser.Id}",
                TimeSpan.FromSeconds(2));

            if (myLock == null)
            {
                return;
            }

            var userProfileId = await _appDbContext.Conversations
                .Where(x => x.Id == conversationId)
                .Select(x => x.UserProfileId)
                .FirstOrDefaultAsync();

            if (!await _appDbContext.Conversations
                    .AnyAsync(
                        x =>
                            x.Id == conversationId
                            && x.UnreadMessageCount > 0)
                && !await _appDbContext.ConversationUnreadRecords
                        .AnyAsync(
                            x =>
                                x.CompanyId == companyUser.CompanyId
                                && x.StaffId == companyUser.Id
                                && x.ConversationId == conversationId
                                && x.NotificationDeliveryStatus != NotificationDeliveryStatus.Read))
            {
                // BackgroundJob.Enqueue<IDbAuditLogService>(x => x.AddActivityLogs(companyUser.CompanyId, userProfileId, companyUser.IdentityId, new List<RemarkViewModel> {new RemarkViewModel {Remarks = $"{companyUser.Identity.DisplayName} viewed this conversation"}}));
                return;
            }

            // No need to reset the unread count of conversation anymore
            await _appDbContext.Conversations
                .Where(x => x.Id == conversationId)
                .ExecuteUpdateAsync(
                    conversation =>
                        conversation.SetProperty(c => c.UnreadMessageCount, 0));


            await _appDbContext.ConversationUnreadRecords
                .Where(
                    x =>
                        x.CompanyId == companyUser.CompanyId
                        && x.StaffId == companyUser.Id
                        && x.ConversationId == conversationId
                        && x.NotificationDeliveryStatus != NotificationDeliveryStatus.Read)
                .ExecuteUpdateAsync(
                    unreadRecord =>
                        unreadRecord
                            .SetProperty(r => r.ReadAt, DateTime.Now)
                            .SetProperty(r => r.NotificationDeliveryStatus, NotificationDeliveryStatus.Read));

            if (companyUser.Id != 1)
            {
                await _userProfileHooks.OnConversationReadAsync(
                    companyUser.CompanyId,
                    userProfileId,
                    null,
                    () => Task.FromResult(
                        new OnConversationReadData(new StaffData(companyUser.Id.ToString(), companyUser.IdentityId,
                            companyUser.Identity.DisplayName))));
            }
        }

        public async Task ReadConversation(string conversationId, Staff companyUser, bool isForEveryone = false)
        {
            ILockService.Lock myLock = null;

            var dbContext = _dbContextService.GetDbContext();

            if (!isForEveryone)
            {
                myLock = await _lockService.AcquireLockAsync(
                    $"ReadConversation:{conversationId}:{companyUser.Id}",
                    TimeSpan.FromSeconds(2));

                if (myLock == null)
                {
                    return;
                }
            }

            var userProfileId = await dbContext.Conversations
                .Where(x => x.Id == conversationId)
                .Select(x => x.UserProfileId)
                .FirstOrDefaultAsync();

            if (!await dbContext.Conversations
                    .AnyAsync(
                        x =>
                            x.Id == conversationId &&
                            x.UnreadMessageCount > 0)
                && !await dbContext.ConversationUnreadRecords
                    .Where(
                        x =>
                            x.CompanyId == companyUser.CompanyId
                            && x.ConversationId == conversationId
                            && x.NotificationDeliveryStatus != NotificationDeliveryStatus.Read)
                    .WhereIf(
                        !isForEveryone,
                        x => x.StaffId == companyUser.Id)
                    .AnyAsync())
            {
                // BackgroundJob.Enqueue<IDbAuditLogService>(x => x.AddActivityLogs(companyUser.CompanyId, userProfileId, companyUser.IdentityId, new List<RemarkViewModel> {new RemarkViewModel {Remarks = $"{companyUser.Identity.DisplayName} viewed this conversation"}}));
                return;
            }

            // No need to reset the unread count of conversation anymore
            await dbContext.Conversations
                .Where(x => x.Id == conversationId)
                .ExecuteUpdateAsync(
                    conversation => conversation.SetProperty(c => c.UnreadMessageCount, 0));

            await dbContext.ConversationUnreadRecords
                .Where(
                    x =>
                        x.CompanyId == companyUser.CompanyId
                        && x.ConversationId == conversationId
                        && x.NotificationDeliveryStatus != NotificationDeliveryStatus.Read)
                .WhereIf(
                    !isForEveryone,
                    x => x.StaffId == companyUser.Id)
                .ExecuteUpdateAsync(
                    unreadRecord =>
                        unreadRecord
                            .SetProperty(r => r.ReadAt, DateTime.Now)
                            .SetProperty(r => r.NotificationDeliveryStatus, NotificationDeliveryStatus.Read));

            if (companyUser.Id != 1)
            {
                await _userProfileHooks.OnConversationReadAsync(
                    companyUser.CompanyId,
                    userProfileId,
                    null,
                    () => Task.FromResult(
                        new OnConversationReadData(new StaffData(companyUser.Id.ToString(), companyUser.IdentityId,
                                companyUser.Identity.DisplayName))));
            }

            if (myLock != null)
            {
                await _lockService.ReleaseLockAsync(myLock);
            }
        }

        public async Task<ConversationStatusResponseViewModel> MarkConversationAsUnread(
            string conversationId,
            Staff companyUser)
        {
            var myLock = await _lockService.AcquireLockAsync(
                $"MarkConversationAsUnread:{conversationId}:{companyUser.Id}",
                TimeSpan.FromSeconds(2));

            if (myLock == null)
            {
                throw new Exception("locked");
            }

            var conversation = await _appDbContext.Conversations
                .Include(x => x.UserProfile)
                .Include(x => x.Assignee.Identity)
                .Include(x => x.AdditionalAssignees)
                .FirstOrDefaultAsync(
                    x =>
                        x.Id == conversationId
                        && x.CompanyId == companyUser.CompanyId);

            conversation.UnreadMessageCount = 1;

            // Current assignment type between the Conversation and User
            NotificationDeliveryType notificationDeliveryType;
            if (conversation.AssigneeId == companyUser.Id)
            {
                notificationDeliveryType = NotificationDeliveryType.Assignee;
            }
            else if (conversation.AdditionalAssignees.Any(x => x.AssigneeId == companyUser.Id && x.CompanyId == companyUser.CompanyId))
            {
                notificationDeliveryType = NotificationDeliveryType.Collaborator;
            }
            else
            {
                notificationDeliveryType = NotificationDeliveryType.Mentioned;
            }

            var updatedUnreadRecordCount = await _appDbContext.ConversationUnreadRecords
                .Where(
                    x =>
                        x.CompanyId == companyUser.CompanyId
                        && x.StaffId == companyUser.Id
                        && x.ConversationId == conversationId
                        && x.NotificationDeliveryType == notificationDeliveryType)
                .OrderByDescending(x => x.Id)
                .Take(1)
                .ExecuteUpdateAsync(
                    unreadRecord =>
                        unreadRecord.SetProperty(r => r.NotificationDeliveryStatus, NotificationDeliveryStatus.Delivered));

            // If no unread records between the conversation and this User
            if (updatedUnreadRecordCount == 0)
            {
                var conversationMessageId = await _appDbContext.ConversationMessages
                    .Where(x => x.CompanyId == companyUser.CompanyId && x.ConversationId == conversationId)
                    .OrderByDescending(x => x.CreatedAt)
                    .Select(x => x.Id)
                    .FirstOrDefaultAsync();

                if (conversationMessageId != 0)
                {
                    await _appDbContext.ConversationUnreadRecords.AddAsync(
                        new ConversationUnreadRecord()
                        {
                            CompanyId = companyUser.CompanyId,
                            ConversationId = conversationId,
                            MessageId = conversationMessageId,
                            StaffId = companyUser.Id,
                            StaffIdentityId = companyUser.IdentityId,
                            NotificationDeliveryType = notificationDeliveryType,
                            NotificationDeliveryStatus = NotificationDeliveryStatus.Delivered,
                            SentAt = DateTime.UtcNow,
                            DeliveredAt = DateTime.UtcNow,
                            ReadAt = DateTime.UtcNow
                        });
                }
                else
                {
                    throw new Exception("Mark as unread failed because no valid message found!");
                }
            }

            await _appDbContext.SaveChangesAsync();

            var responseVm = _mapper.Map<ConversationStatusResponseViewModel>(conversation);
            await _signalRService.SignalROnConversationUnreadStatusChanged(conversation, companyUser.IdentityId);

            return responseVm;
        }

        public async Task<List<ConversationMessageResponseViewModel>> GetUndeliveredMessages(Staff companyUser)
        {
            var getUndeliveredMessageCacheKeyPattern = new GetUndeliveredMessageCacheKeyPattern(companyUser.Id);
            var data = await _cacheManagerService.GetCacheAsync(getUndeliveredMessageCacheKeyPattern);

            if (!string.IsNullOrEmpty(data))
            {
                return JsonConvert.DeserializeObject<List<ConversationMessageResponseViewModel>>(data);
            }

            var myLock = await _lockService.AcquireLockAsync(
                $"GetUndeliveredMessages{companyUser.Id}",
                TimeSpan.FromSeconds(10));

            if (myLock == null)
            {
                throw new Exception("locked");
            }

            var undeliveredMessageIds = await _appDbContext.ConversationUnreadRecords
                .Where(
                    x =>
                        x.CompanyId == companyUser.CompanyId
                        && x.StaffId == companyUser.Id
                        && x.NotificationDeliveryStatus == NotificationDeliveryStatus.Sent)
                .Select(x => x.MessageId)
                .ToListAsync();

            var response = await _appDbContext.ConversationMessages
                .Where(
                    x =>
                        x.CompanyId == companyUser.CompanyId
                        && undeliveredMessageIds.Contains(x.Id))
                .Include(x => x.UploadedFiles)
                .Include(x => x.EmailFrom)
                .Include(x => x.Sender)
                .Include(x => x.facebookSender)
                .Include(x => x.facebookReceiver)
                .Include(x => x.whatsappSender)
                .Include(x => x.whatsappReceiver)
                .Include(x => x.WebClientSender)
                .Include(x => x.WebClientReceiver)
                .Include(x => x.MessageAssignee.Identity)
                .Include(x => x.WeChatSender)
                .Include(x => x.WeChatReceiver)
                .Include(x => x.LineSender)
                .Include(x => x.LineReceiver)
                .Include(x => x.SMSSender)
                .Include(x => x.SMSReceiver)
                .Include(x => x.InstagramSender)
                .Include(x => x.InstagramReceiver)
                .Include(x => x.ViberSender)
                .Include(x => x.ViberReceiver)
                .Include(x => x.TelegramSender)
                .Include(x => x.TelegramReceiver)
                .Include(x => x.Whatsapp360DialogSender)
                .Include(x => x.Whatsapp360DialogReceiver)
                .Include(x => x.Whatsapp360DialogExtendedMessagePayload)
                .Include(x => x.ExtendedMessagePayload)
                .OrderByDescending(x => x.Timestamp)
                .ProjectTo<ConversationMessageResponseViewModel>(_mapper.ConfigurationProvider)
                .ToListAsync();

            await _sleekPayService.AddSleekPayRecord(response);

            foreach (var message in response)
            {
                await _signalRService.SignalRResendOnMessageReceived(companyUser.IdentityId, message);
            }

            await _cacheManagerService.SaveCacheAsync(
                getUndeliveredMessageCacheKeyPattern,
                response);

            return response;
        }

        public async Task<bool> SendOptInMessageTwilio(ConversationMessage message)
        {
            var hasSentOptInToday = await _appDbContext.ConversationMessages
                .AnyAsync(
                    x => x.ConversationId == message.ConversationId
                         && x.Channel.ToLower() == message.Channel.ToLower()
                         && x.whatsappSenderId == message.whatsappSenderId
                         && x.UpdatedAt.Date == DateTime.UtcNow.Date
                         && x.DeliveryType == DeliveryType.ReadMore);

            if (hasSentOptInToday)
            {
                return false;
            }

            var whatsappReceiver = await _appDbContext.SenderWhatsappSenders
                .FirstOrDefaultAsync(x => x.Id == message.whatsappReceiverId);

            // Send read more status
            var configQ = _appDbContext.ConfigWhatsAppConfigs
                .Where(x => x.TwilioAccountId == whatsappReceiver.InstanceId);

            if (!string.IsNullOrEmpty(whatsappReceiver.InstaneSender))
            {
                configQ = configQ
                    .Where(x => x.WhatsAppSender == whatsappReceiver.InstaneSender);
            }

            var config = await configQ.FirstOrDefaultAsync();

            var companySetting = await _appDbContext.CompanyCompanies
                .Where(x => x.Id == message.CompanyId)
                .Select(x => x.CompanySetting)
                .FirstOrDefaultAsync();

            if (companySetting == null)
            {
                return false;
            }

            if (!string.IsNullOrEmpty(config?.ReadMoreTemplateMessage)
                && companySetting.IsOptInOn)
            {
                await SendReadMoreMessage(
                    message.CompanyId,
                    message.ConversationId,
                    message.whatsappSenderId,
                    config.ReadMoreTemplateMessage,
                    config.ReadMoreMessageContentSid);

                return true;
            }

            return false;
        }

        public async Task AddFbAdClickToMessenger(Entry entry, FBMessaging messaging)
        {
            var facebookId = string.Empty;
            try
            {
                while (true)
                {
                    var lockId = $"{entry.Id}_{messaging.recipient?.id}";

                    if (entry.Id == messaging.recipient?.id)
                    {
                        lockId = $"{messaging.recipient?.id}_{messaging.sender.id}";
                        facebookId = messaging.sender.id;
                    }
                    else
                    {
                        facebookId = messaging.recipient?.id;
                    }

                    if (string.IsNullOrEmpty(facebookId))
                    {
                        _logger.LogWarning("no facebookId found in {WebhookPayload} {Messaging}", entry, messaging);
                        return;
                    }

                    var myLock = await _lockService.AcquireLockAsync(lockId, TimeSpan.FromSeconds(2));

                    if (myLock == null)
                    {
                        await Task.Delay(TimeSpan.FromSeconds(2));
                    }
                    else
                    {
                        break;
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Unable to lock AddFbAdClickToMessenger: {Exception} for {WebhookPayload} {Messaging}", ex.Message, entry, messaging);
            }

            var pageId = entry.Id;

            var facebookConfig =
                await _appDbContext.ConfigFacebookConfigs.FirstOrDefaultAsync(x => x.PageId == entry.Id);

            if (facebookConfig == null)
            {
                return;
            }

            var facebookSender = await _appDbContext.SenderFacebookSenders.Where(
                    x => x.FacebookId == facebookId && x.CompanyId == facebookConfig.CompanyId &&
                         x.pageId == pageId)
                .FirstOrDefaultAsync();

            if (facebookSender == null)
            {
                facebookSender = new FacebookSender
                {
                    FacebookId = facebookId,
                    pageId = facebookConfig.PageId
                };
                facebookSender.CompanyId = facebookConfig.CompanyId;
            }

            if (facebookSender.FacebookId != pageId)
            {
                facebookSender = await FetchFacebookUserProfile(
                    facebookConfig.CompanyId,
                    facebookSender,
                    pageId,
                    facebookConfig.PageAccessToken);
            }

            var facebookPageAccessToken = facebookConfig.PageAccessToken;

            ConversationMessage clickToAdMessage = null;

            await Policy
                .Handle<Exception>()
                .WaitAndRetryAsync(
                    5,
                    sleepDurationProvider: i => TimeSpan.FromSeconds(5),
                    onRetry: (exception, retryCount) =>
                    {
                        _logger.LogError(
                            "Getting ad click to open fb messenger error {Exception} with {RetryCount}th times",
                            JsonConvert.SerializeObject(exception),
                            retryCount);
                    })
                .ExecuteAndCaptureAsync(
                    async () =>
                    {
                        clickToAdMessage = await _appDbContext.ConversationMessages.Where(
                                x =>
                                    x.CompanyId == facebookConfig.CompanyId &&
                                    x.facebookSenderId.HasValue &&
                                    x.facebookSender.FacebookId == facebookSender.FacebookId &&
                                    x.facebookSender.pageId == facebookConfig.PageId &&
                                    !x.IsSentFromSleekflow && x.CreatedAt > DateTime.UtcNow.AddMinutes(-5))
                            .FirstOrDefaultAsync();

                        if (clickToAdMessage == null)
                        {
                            throw new Exception(
                                $"cannot retrieve the ad click to open thread in messenger from db for {JsonConvert.SerializeObject(messaging)} webhook");
                        }

                        if (clickToAdMessage != null)
                        {
                            try
                            {
                                using var client = new HttpClient();

                                var facebookCommentClient = new FacebookPageCommentClient(
                                    facebookPageAccessToken,
                                    client);

                                var getFacebookPagePostDetailsResponse =
                                    await facebookCommentClient.GetFacebookPagePostDetailsAsync(
                                        $"{pageId}_{messaging.Referral.AdsContextData.PostId}",
                                        null,
                                        default);

                                if (getFacebookPagePostDetailsResponse != null)
                                {
                                    var adPermalinkUrl = getFacebookPagePostDetailsResponse.PermalinkUrl;
                                    var adText = getFacebookPagePostDetailsResponse.Message;

                                    clickToAdMessage.ExtendedMessagePayload = new ExtendedMessagePayload()
                                    {
                                        Channel = ChannelTypes.Facebook,
                                        ExtendedMessageType = ExtendedMessageType.FacebookAdClickToMessenger,
                                        ExtendedMessagePayloadDetail = new ExtendedMessagePayloadDetail()
                                        {
                                            FacebookAdClickToMessengerObject =
                                                new FacebookAdsClickToMessengerObject()
                                                {
                                                    AdId = messaging.Referral.AdId,
                                                    AdTitle = messaging.Referral.AdsContextData.AdTitle,
                                                    AdText = adText,
                                                    AdPermalinkUrl = adPermalinkUrl
                                                }
                                        }
                                    };

                                    if (!string.IsNullOrEmpty(clickToAdMessage.MessageContent))
                                    {
                                        clickToAdMessage.ExtendedMessagePayload.ExtendedMessagePayloadDetail
                                            .FacebookAdClickToMessengerObject.Message = clickToAdMessage.MessageContent;
                                    }

                                    await _appDbContext.SaveChangesAsync();
                                }

                            }
                            catch (Exception ex)
                            {
                                _logger.LogError(
                                    "Error occured for fetching the ad post details for fb message {Webhook} with {Exception}",
                                    JsonConvert.SerializeObject(messaging),
                                    JsonConvert.SerializeObject(ex));
                            }
                        }
                    });
        }

        public async Task<ConversationWithCountViewModel> SearchConversationMessagesAsync(
            Staff companyUser,
            string assignedTo,
            string keywords,
            int offset,
            int limit,
            string status,
            string channels,
            string channelIds,
            long? teamId,
            string behaviourVersion = "1",
            bool isAllowCache = true)
        {
            var searchMessageCacheKeyPattern = new SearchMessageCacheKeyPattern(
                companyUser.Id,
                assignedTo,
                offset,
                limit,
                keywords,
                status,
                channels,
                channelIds,
                teamId,
                behaviourVersion);

            if (isAllowCache)
            {
                var data = await _cacheManagerService.GetCacheAsync(searchMessageCacheKeyPattern);

                if (data != null)
                {
                    return JsonConvert.DeserializeObject<ConversationWithCountViewModel>(data);
                }
            }

            while (true)
            {
                var myLock = await _lockService.AcquireLockAsync(
                    $"lock:v2_search_message{searchMessageCacheKeyPattern.GenerateKeyPattern()}",
                    TimeSpan.FromSeconds(15));

                if (myLock == null)
                {
                    if (isAllowCache)
                    {
                        var cacheData = await _cacheManagerService.GetCacheAsync(searchMessageCacheKeyPattern);

                        if (cacheData != null)
                        {
                            return JsonConvert.DeserializeObject<ConversationWithCountViewModel>(
                                cacheData);
                        }
                    }

                    await Task.Delay(5000);
                }
                else
                {
                    break;
                }
            }

            var channelList = new List<string>();

            if (!string.IsNullOrEmpty(channels))
            {
                channelList = channels.Split(",").ToList();
            }

            var dateThreshold = DateTime.UtcNow.AddDays(-365).ToString("yyyy-MM-dd");
            var keywordsParameter = new SqlParameter(
                "@keywords",
                string.IsNullOrEmpty(keywords) ? string.Empty : keywords);

            var filteredConversationMessageQueryable = _appDbContext.ConversationMessages.FromSqlRaw(
                    $"""
                     SELECT [c].*
                     FROM [ConversationMessages] AS [c]
                     LEFT JOIN [Conversations] AS [c0] ON [c].[ConversationId] = [c0].[Id]
                     WHERE [c].[CompanyId] = '{companyUser.CompanyId}'
                       AND [c].[CreatedAt] > '{dateThreshold}'
                       AND [c0].[CompanyId] = '{companyUser.CompanyId}'
                       AND ((@keywords = N'') OR CHARINDEX(@keywords, [c].[MessageContent]) > 0)
                       {status.ToLower() switch
                    {
                        "open" => "AND [c0].[Status] = 'open'",
                        "closed" => "AND [c0].[Status] = 'closed'",
                        "pending" => "AND [c0].[Status] = 'pending'",
                        _ => string.Empty
                    }}
                       AND [c0].[ActiveStatus] = 1
                     """,
                    keywordsParameter)
                .WhereIf(
                    channelList.Count > 0,
                    cm => channelList.Contains(cm.Channel));

            switch (assignedTo)
            {
                case "all":
                    switch (companyUser.RoleType)
                    {
                        case StaffUserRole.TeamAdmin:
                            var managedTeam = await _appDbContext.CompanyStaffTeams
                                .Where(x => x.Members.Any(member => member.StaffId == companyUser.Id))
                                .Select(x => x.Id)
                                .ToListAsync();

                            if (managedTeam == null)
                            {
                                throw new ArgumentNullException(nameof(managedTeam.ToString));
                            }

                            var teamAdminTeamIds = await _appDbContext.CompanyStaffTeams
                                .Where(
                                    x => x.Members.Any(member => member.StaffId == companyUser.Id) &&
                                         x.CompanyId == companyUser.CompanyId).Select(x => x.Id)
                                .ToListAsync();

                            var teamMemberIds = await _appDbContext.CompanyTeamMembers
                                .Where(x => teamAdminTeamIds.Contains(x.CompanyTeamId) && x.StaffId != companyUser.Id)
                                .Select(x => x.StaffId)
                                .Distinct()
                                .ToListAsync();

                            filteredConversationMessageQueryable = filteredConversationMessageQueryable.Where(
                                cm =>
                                    (cm.Conversation.AssigneeId == null && cm.Conversation.AssignedTeamId == null) // Unassigned conversation
                                    || cm.Conversation.AssigneeId == companyUser.Id // Team Admin is the contact owner
                                    || cm.Conversation.AdditionalAssignees.Any(
                                        x => x.AssigneeId == companyUser.Id
                                             && x.CompanyId == companyUser.CompanyId) // Team Admin is the collaborator
                                    || (cm.Conversation.AssignedTeamId.HasValue
                                        && teamAdminTeamIds.Contains(
                                            cm.Conversation.AssignedTeamId
                                                .Value)) // The conversations managed under the team admin's team.//
                                    || cm.Conversation.AdditionalAssignees
                                        .Any( // The conversation has team member as collaborator
                                            x => teamMemberIds.Contains((long)x.AssigneeId))
                                    || (cm.Channel == ChannelTypes.Note && cm.MessageAssigneeId == companyUser.Id // Team Admin has been mentioned within 2 days
                                                                        && cm.UpdatedAt > DateTime.UtcNow.AddDays(-2)));
                            break;
                        case StaffUserRole.Staff:
                            var associatedTeam = await _appDbContext.CompanyStaffTeams
                                .Where(x => x.Members.Any(member => member.StaffId == companyUser.Id))
                                .Select(x => x.Id)
                                .ToListAsync();

                            if (associatedTeam == null)
                            {
                                throw new ArgumentNullException(nameof(associatedTeam.ToString));
                            }

                            var staffAssociatedTeamIds = await _appDbContext.CompanyStaffTeams
                                .Where(
                                    x => x.Members.Any(member => member.StaffId == companyUser.Id) &&
                                         x.CompanyId == companyUser.CompanyId).Select(x => x.Id)
                                .ToListAsync();

                            var staffAssociatedTeamMemberIds = await _appDbContext.CompanyTeamMembers
                                .Where(x => staffAssociatedTeamIds.Contains(x.CompanyTeamId) && x.StaffId != companyUser.Id)
                                .Select(x => x.StaffId)
                                .Distinct()
                                .ToListAsync();

                            filteredConversationMessageQueryable = filteredConversationMessageQueryable.Where(
                                cm =>
                                    cm.Conversation.AssigneeId == companyUser.Id // Staff is the contact owner
                                    || cm.Conversation.AdditionalAssignees.Any( // Staff is the collaborator
                                        x => x.AssigneeId == companyUser.Id
                                             && x.CompanyId == companyUser.CompanyId)
                                    || (cm.Conversation.AssignedTeamId.HasValue && !cm.Conversation.AssigneeId.HasValue // The conversation is assigned to team inbox
                                        && staffAssociatedTeamMemberIds.Contains(cm.Conversation.AssignedTeamId.Value)) // The conversation is under the staff associated teams
                                    || (cm.Channel == ChannelTypes.Note && cm.MessageAssigneeId == companyUser.Id // The staff has been mentioned within 2 days
                                                                        && cm.UpdatedAt > DateTime.UtcNow.AddDays(-2)));

                            if (companyUser.RoleType == StaffUserRole.Staff)
                            {
                                var isShowDefaultChannelMessagesOnly = false;

                                var staffPermission = await _appDbContext.CompanyRolePermissions
                                    .FirstOrDefaultAsync(
                                        x =>
                                            x.CompanyId == companyUser.CompanyId
                                            && x.StaffUserRole == companyUser.RoleType);
                                if (staffPermission != null)
                                {
                                    isShowDefaultChannelMessagesOnly =
                                        staffPermission.Permission.IsShowDefaultChannelMessagesOnly;
                                }

                                if (isShowDefaultChannelMessagesOnly)
                                {
                                    filteredConversationMessageQueryable = filteredConversationMessageQueryable
                                        .Where(
                                            cm => cm.Conversation.AssignedTeamId.HasValue
                                                  && (associatedTeam.Contains(cm.Conversation.AssignedTeamId.Value)
                                                      || cm.Conversation.AdditionalAssignees
                                                          .Any(
                                                              x => x.CompanyId == companyUser.CompanyId
                                                                   && x.AssigneeId == companyUser.Id)
                                                  ));
                                }
                                else
                                {
                                    filteredConversationMessageQueryable = filteredConversationMessageQueryable
                                        .Where(
                                            cm => cm.Conversation.AssigneeId == companyUser.Id
                                                  || (!cm.Conversation.AssignedTeamId.HasValue
                                                      && !cm.Conversation.AssigneeId.HasValue)
                                                  || (cm.Conversation.AssignedTeamId.HasValue
                                                      && associatedTeam.Contains(cm.Conversation.AssignedTeamId.Value)
                                                      && !cm.Conversation.AssigneeId.HasValue)
                                                  || cm.Conversation.AdditionalAssignees
                                                      .Any(
                                                          x => x.CompanyId == companyUser.CompanyId
                                                               && x.AssigneeId == companyUser.Id));
                                }
                            }

                            break;
                    }

                    break;

                case "mentioned":
                    var unixTime = DateTime.UtcNow.AddDays(-2).ToUnixTime();
                    var mentionedConversationIds = await _appDbContext.Conversations
                        .Where(
                            conversation => conversation.ChatHistory
                                .Any(
                                    y => y.Channel == ChannelTypes.Note
                                         && y.MessageAssignee.IdentityId == companyUser.IdentityId
                                         && y.UpdatedAt > DateTime.UtcNow.AddDays(-2)))
                        .Select(x => x.Id)
                        .ToListAsync();

                    filteredConversationMessageQueryable = filteredConversationMessageQueryable
                        .Where(
                            cm =>
                                mentionedConversationIds.Contains(cm.ConversationId)
                                && cm.Timestamp > unixTime);

                    break;

                case "unassigned":
                    filteredConversationMessageQueryable = filteredConversationMessageQueryable.Where(
                        cm =>
                            !cm.Conversation.AssigneeId.HasValue);

                    break;

                case "collaborator":
                    filteredConversationMessageQueryable = filteredConversationMessageQueryable.Where(
                        cm =>
                            cm.Conversation.AdditionalAssignees.Any(
                                x =>
                                    x.AssigneeId == companyUser.Id && x.CompanyId == companyUser.CompanyId));

                    break;

                case "team":
                    switch (companyUser.RoleType)
                    {
                        case StaffUserRole.Admin:
                        case StaffUserRole.DemoAdmin:
                        case StaffUserRole.TeamAdmin:
                            filteredConversationMessageQueryable = filteredConversationMessageQueryable
                                .Where(
                                    conversationMessage => conversationMessage.Conversation.AssignedTeamId == teamId);

                            break;
                        case StaffUserRole.Staff:
                            filteredConversationMessageQueryable = filteredConversationMessageQueryable
                                .Where(
                                    conversationMessage => conversationMessage.Conversation.AssignedTeamId == teamId &&
                                                           (!conversationMessage.Conversation.AssigneeId.HasValue ||
                                                            conversationMessage.Conversation.AssigneeId ==
                                                            companyUser.Id));

                            break;
                    }

                    break;

                default:
                    if (companyUser.RoleType != StaffUserRole.Admin &&
                        companyUser.RoleType != StaffUserRole.DemoAdmin &&
                        assignedTo != companyUser.IdentityId)
                    {
                        throw new Exception($"You cannot access conversations assigned to {assignedTo}");
                    }

                    var assignedToId = await _appDbContext.UserRoleStaffs
                        .Where(x => x.CompanyId == companyUser.CompanyId && x.IdentityId == assignedTo)
                        .Select(x => x.Id)
                        .FirstOrDefaultAsync();

                    filteredConversationMessageQueryable = filteredConversationMessageQueryable
                        .WhereIf(
                            behaviourVersion == "1",
                            cm =>
                                cm.Conversation.AssigneeId == assignedToId
                                || cm.Conversation.AdditionalAssignees.Any(
                                    x => x.CompanyId == companyUser.CompanyId && x.AssigneeId == assignedToId))
                        .WhereIf(
                            behaviourVersion == "2",
                            cm => cm.Conversation.AssigneeId == assignedToId);

                    break;
            }

            filteredConversationMessageQueryable = filteredConversationMessageQueryable.Take(10000);

            var chanelIds = new List<string>();

            if (!string.IsNullOrEmpty(channelIds))
            {
                chanelIds = channelIds.Split(",").ToList();
            }

            if (chanelIds.Count > 0)
            {
                try
                {
                    if (chanelIds.Contains(ChannelTypes.WhatsappTwilio))
                    {
                        var newChannelIds = new List<string>();
                        var newInstanceSender = new List<string>();

                        foreach (var myChannelId in chanelIds)
                        {
                            var twilioInstance = myChannelId.Split(";", StringSplitOptions.RemoveEmptyEntries);
                            newChannelIds.Add(twilioInstance[0]);

                            if (twilioInstance.Count() > 1)
                            {
                                newInstanceSender.Add(twilioInstance[1].Replace(": ", ":+"));
                            }
                        }

                        filteredConversationMessageQueryable = filteredConversationMessageQueryable
                            .Where(
                                cm =>
                                    newChannelIds.Contains(cm.whatsappReceiver.InstanceId) ||
                                    newChannelIds.Contains(cm.whatsappSender.InstanceId));

                        if (newInstanceSender.Count > 0)
                        {
                            filteredConversationMessageQueryable = filteredConversationMessageQueryable
                                .Where(
                                    cm =>
                                        newInstanceSender.Contains(cm.whatsappReceiver.InstaneSender) ||
                                        newInstanceSender.Contains(cm.whatsappSender.InstaneSender));
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(
                        ex,
                        "[{MethodName}] Company {CompanyId} error filtering query by channelIds: {ChannelIds}. {ExceptionMessage}",
                        nameof(SearchConversationMessagesAsync),
                        companyUser?.CompanyId,
                        channelIds,
                        ex.Message);
                }
            }

            var conversationMessageIds = await _measureQueryTimeService.MeasureQueryTimeFromFuncIQueryable(
                () => filteredConversationMessageQueryable
                    .OrderByDescending(x => x.CreatedAt)
                    .Select(cm => cm.Id)
                    .Skip(offset)
                    .Take(limit),
                nameof(SearchConversationMessagesAsync));

            var conversationMessages = conversationMessageIds.Any()
                ? await _appDbContext.ConversationMessages
                    .Where(cm => conversationMessageIds.Contains(cm.Id))
                    .Include(x => x.UploadedFiles)
                    .Include(x => x.EmailFrom)
                    .Include(x => x.Sender)
                    .Include(x => x.facebookSender)
                    .Include(x => x.facebookReceiver)
                    .Include(x => x.whatsappSender)
                    .Include(x => x.whatsappReceiver)
                    .Include(x => x.WebClientSender)
                    .Include(x => x.WebClientReceiver)
                    .Include(x => x.MessageAssignee.Identity)
                    .Include(x => x.WeChatSender)
                    .Include(x => x.WeChatReceiver)
                    .Include(x => x.LineSender)
                    .Include(x => x.LineReceiver)
                    .Include(x => x.SMSSender)
                    .Include(x => x.SMSReceiver)
                    .Include(x => x.ViberSender)
                    .Include(x => x.ViberReceiver)
                    .Include(x => x.TelegramSender)
                    .Include(x => x.TelegramReceiver)
                    .Include(x => x.Whatsapp360DialogSender)
                    .Include(x => x.Whatsapp360DialogReceiver)
                    .Include(x => x.Whatsapp360DialogExtendedMessagePayload)
                    .Include(x => x.ExtendedMessagePayload)
                    .AsSplitQuery()
                    .AsNoTracking()
                    .ToListAsync()
                : new List<ConversationMessage>();

            // PII masking operations.
            if (await _piiMaskingService.IsConfiguredAsync(companyUser.CompanyId))
            {
                // Remove messages that match company PII masking pattern.
                var toBeRemovedMessageIds = new List<long>();
                foreach (var message in conversationMessages)
                {
                    // Only match messages from end users.
                    if (!message.IsSentFromSleekflow
                        && message.MessageContent is not null
                        && await _piiMaskingService.IsMatchAsync(companyUser.CompanyId, message.MessageContent))
                    {
                        toBeRemovedMessageIds.Add(message.Id);
                    }
                }

                conversationMessages.RemoveAll(x => toBeRemovedMessageIds.Contains(x.Id));
                filteredConversationMessageQueryable = filteredConversationMessageQueryable
                    .Where(cm => !toBeRemovedMessageIds.Contains(cm.Id));
            }

            var conversationIdToMessagesDict = conversationMessages
                .GroupBy(x => x.ConversationId)
                .Select(g => g.ToList())
                .ToDictionary(list => list[0].ConversationId, list => list);

            // conversation id list
            var conversationIds = conversationIdToMessagesDict.Keys.ToList();

            // all fetched conversations
            var conversationViewModel = await _appDbContext.Conversations
                .Where(x => conversationIds.Contains(x.Id))
                .Include(x => x.UserProfile)
                .Include(x => x.WhatsappUser)
                .Include(x => x.facebookUser)
                .Include(x => x.Assignee.Identity)
                .Include(x => x.EmailAddress)
                .Include(x => x.WebClient)
                .Include(x => x.WeChatUser)
                .Include(x => x.LineUser)
                .Include(x => x.SMSUser)
                .Include(x => x.WhatsApp360DialogUser)
                .Include(x => x.WhatsappCloudApiUser)
                .Include(x => x.TelegramUser)
                .Include(x => x.ViberUser)
                .AsSplitQuery()
                .ProjectTo<ConversationNoCompanyResponseViewModel>(_mapper.ConfigurationProvider)
                .ToDictionaryAsync(x => x.ConversationId, x => x);

            // related AdditionalAssignees group by conversationId
            var additionalAssigneeResponses = await _appDbContext.ConversationAdditionalAssignees
                .Include(x => x.Assignee.Identity)
                .Where(x => conversationIds.Contains(x.ConversationId))
                .ProjectTo<AdditionalAssigneeResponse>(_mapper.ConfigurationProvider)
                .GroupBy(x => x.ConversationId)
                .Select(g => g.ToList())
                .ToDictionaryAsync(list => list[0].ConversationId, list => list);

            // related ConversationHashtags group by conversationId
            var conversationHashtagResponses = await _appDbContext.ConversationHashtags
                .Include(x => x.Hashtag)
                .Where(x => conversationIds.Contains(x.ConversationId))
                .ProjectTo<ConversationHashtagResponse>(_mapper.ConfigurationProvider)
                .GroupBy(x => x.ConversationId)
                .Select(g => g.ToList())
                .ToDictionaryAsync(list => list[0].ConversationId, list => list);

            // get channel for each conversation
            var conversationsChannelList = await _appDbContext.ConversationMessages
                .Where(cm => conversationIds.Contains(cm.ConversationId))
                .Select(
                    cm => new
                    {
                        cm.ConversationId,
                        cm.UpdatedAt,
                        cm.Channel
                    })
                .GroupBy(cm => cm.ConversationId)
                .Select(
                    grp =>
                        grp.OrderByDescending(g => g.UpdatedAt).FirstOrDefault())
                .ToDictionaryAsync(
                    x => x.ConversationId,
                    x => new List<string>
                    {
                        x.Channel
                    });

            var results = new List<ConversationNoCompanyResponseViewModel>();

            // assemble messages to related conversation response view model
            foreach (var conversationId in conversationIds)
            {
                conversationViewModel[conversationId].AdditionalAssignees =
                    additionalAssigneeResponses.TryGetValue(conversationId, out var additionalAssignees)
                        ? additionalAssignees
                        : new List<AdditionalAssigneeResponse>();

                conversationViewModel[conversationId].ConversationHashtags =
                    conversationHashtagResponses.TryGetValue(conversationId, out var conversationHashtags)
                        ? conversationHashtags
                        : new List<ConversationHashtagResponse>();

                conversationViewModel[conversationId].ConversationChannels =
                    conversationsChannelList[conversationId];

                conversationViewModel[conversationId].Messages =
                    _mapper.Map<List<ConversationMessageResponseViewModel>>(
                        conversationIdToMessagesDict[conversationId]
                            .OrderByDescending(x => x.CreatedAt)
                            .ToList());
                await _sleekPayService.AddSleekPayRecord(conversationViewModel[conversationId].Messages);

                results.Add(conversationViewModel[conversationId]);
            }

            var response = new ConversationWithCountViewModel
            {
                Data = results.OrderByDescending(x => x.UpdatedTime).ToList(),
                Count = await filteredConversationMessageQueryable.CountAsync()
            };

            if (conversationIds.Any() && _isEnableTicketingLogic)
            {
                var userProfileIds = results.Select(x => x.UserProfile.Id).ToList();

                var ticketCount = await _ticketingHubMessageService.GetTicketingUserProfileCountAsync(
                    companyUser.CompanyId,
                    userProfileIds);

                if (ticketCount != null)
                {
                    response.Data = response.Data.GroupJoin(
                        ticketCount,
                        result => result.UserProfile.Id,
                        count => count.SleekflowUserProfileId,
                        (result, countGroup) => new ConversationNoCompanyResponseViewModel
                        {
                            ConversationId = result.ConversationId,
                            CompanyId = result.CompanyId,
                            ConversationChannels = result.ConversationChannels,
                            MessageGroupName = result.MessageGroupName,
                            UserProfile = result.UserProfile,
                            Status = result.Status,
                            Assignee = result.Assignee,
                            AdditionalAssignees = result.AdditionalAssignees,
                            ConversationHashtags = result.ConversationHashtags,
                            LastMessage = result.LastMessage,
                            Messages = result.Messages,
                            UpdatedTime = result.UpdatedTime,
                            ModifiedAt = result.ModifiedAt,
                            UnreadMessageCount = result.UnreadMessageCount,
                            SnoozeUntil = result.SnoozeUntil,
                            FirstMessageId = result.FirstMessageId,
                            LastMessageId = result.LastMessageId,
                            LastMessageChannel = result.LastMessageChannel,
                            LastChannelIdentityId = result.LastChannelIdentityId,
                            AssignedTeam = result.AssignedTeam,
                            IsSandbox = result.IsSandbox,
                            IsBookmarked = result.IsBookmarked,
                            Metadata = result.Metadata,
                            TicketCount = countGroup.Select(x => x.Count).DefaultIfEmpty(0).FirstOrDefault()
                        }).ToList();
                }
            }

            await _cacheManagerService.SaveCacheAsync(searchMessageCacheKeyPattern, response);

            return response;
        }

        public async Task<List<ConversationMessage>> GetConversationLastMessagesAsync(
            string sleekflowCompanyId,
            string userProfileId,
            int offSet,
            int limit)
        {
            var validMessageStatus = new List<MessageStatus>
            {
                MessageStatus.Sent,
                MessageStatus.Received,
                MessageStatus.Read
            };
            var conversationMessages = await _appDbContext.ConversationMessages.Where(
                    c => c.CompanyId == sleekflowCompanyId
                         && c.Conversation.UserProfileId == userProfileId
                         && validMessageStatus.Contains(c.Status))
                .Include(c => c.Conversation)
                .OrderByDescending(c => c.CreatedAt)
                .Skip(offSet)
                .Take(limit)
                .ToListAsync();
            return conversationMessages.OrderBy(c => c.CreatedAt).ToList();
        }

        // get the related message id from the quoted message's Metadata
        public async Task<ConversationMessage?> GetOptInReplyTargetUndeliveredMessage(ConversationMessage receivedMessage)
        {
            if (receivedMessage.QuotedMsgId is null)
            {
                return null;
            }

            var conversationMessageQueryable = _appDbContext
                .ConversationMessages
                .AsNoTracking()
                .Where(x => x.CompanyId == receivedMessage.CompanyId)
                .Where(x => x.ConversationId == receivedMessage.ConversationId);

            var quotedMessage = await conversationMessageQueryable
                    .Where(x=> x.MessageUniqueID == receivedMessage.QuotedMsgId)
                    .FirstOrDefaultAsync();

            if (quotedMessage is null)
            {
                return null;
            }

            var conversationMessageId= ExtractValueFromConversationMessageMetadata<long>(quotedMessage, "targetedConversationMessageId");

            if(conversationMessageId == 0)
            {
                return null;
            }

            var targetedUndeliveredMessage = await conversationMessageQueryable
                .Where(x=> x.Id == conversationMessageId)
                .Where(x=>x.Status == MessageStatus.Undelivered)
                .FirstOrDefaultAsync();

            return targetedUndeliveredMessage;
        }

        public T ExtractValueFromConversationMessageMetadata<T>(ConversationMessage conversationMessage, string key)
        {
            if (!conversationMessage.Metadata.TryGetValue(
                    key,
                    out var objectItem))
            {
                return default;
            }

            if (objectItem is T value)
            {
                return value;
            }

            try
            {
                return (T)Convert.ChangeType(objectItem, typeof(T));
            }
            catch (InvalidCastException)
            {
                return default;
            }
        }

        public async Task AddConversationMessageCache<T>(
            string conversationId,
            ConversationMessageCacheKeyPattern cacheKeyPattern,
            T data)
        {
            var masterCacheKeyPattern = new ConversationMessageMasterCacheKeyPattern(conversationId);

            var masterCacheKey = await _cacheManagerService.GetCacheAsync(masterCacheKeyPattern);
            var cacheKeyPatterns = new List<ConversationMessageCacheKeyPattern>();
            if (masterCacheKey != null)
            {
                cacheKeyPatterns =
                    JsonConvert.DeserializeObject<List<ConversationMessageCacheKeyPattern>>(masterCacheKey);
            }

            cacheKeyPatterns.Add(cacheKeyPattern);
            await _cacheManagerService.SaveCacheAsync(
                masterCacheKeyPattern,
                cacheKeyPatterns);

            await _cacheManagerService.SaveCacheAsync(
                cacheKeyPattern,
                data);
        }

        public async Task AddConversationMessageV2Cache<T>(
            string companyId,
            ConversationMessageV2CacheKeyPattern cacheKeyPattern,
            T data)
        {
            var masterCacheKeyPattern = new ConversationMessageV2MasterCacheKeyPattern(companyId);

            var masterCacheKey = await _cacheManagerService.GetCacheAsync(masterCacheKeyPattern);
            var cacheKeyPatterns = new List<ConversationMessageV2CacheKeyPattern>();
            if (masterCacheKey != null)
            {
                cacheKeyPatterns =
                    JsonConvert.DeserializeObject<List<ConversationMessageV2CacheKeyPattern>>(masterCacheKey);
            }

            cacheKeyPatterns.Add(cacheKeyPattern);
            await _cacheManagerService.SaveCacheAsync(
                masterCacheKeyPattern,
                cacheKeyPatterns);

            await _cacheManagerService.SaveCacheAsync(
                cacheKeyPattern,
                data);
        }

        public async Task AddGetConversationMessagesCache<T>(
            string conversationId,
            GetConversationMessagesCacheKeyPattern cacheKeyPattern,
            T data)
        {
            var masterCacheKeyPattern = new GetConversationMessagesMasterCacheKeyPattern(conversationId);

            var masterCacheKey = await _cacheManagerService.GetCacheAsync(masterCacheKeyPattern);
            var cacheKeyPatterns = new List<GetConversationMessagesCacheKeyPattern>();
            if (masterCacheKey != null)
            {
                cacheKeyPatterns =
                    JsonConvert.DeserializeObject<List<GetConversationMessagesCacheKeyPattern>>(masterCacheKey);
            }

            cacheKeyPatterns.Add(cacheKeyPattern);
            await _cacheManagerService.SaveCacheAsync(
                masterCacheKeyPattern,
                cacheKeyPatterns);

            await _cacheManagerService.SaveCacheAsync(
                cacheKeyPattern,
                data);
        }

        public async Task RemoveConversationMessageV2Cache(string companyId)
        {
            var v2MasterCacheKeyPattern = new ConversationMessageV2MasterCacheKeyPattern(companyId);

            var v2MasterCacheKey = await _cacheManagerService.GetCacheAsync(v2MasterCacheKeyPattern);
            if (v2MasterCacheKey != null)
            {
                await _cacheManagerService.DeleteCacheAsync(v2MasterCacheKeyPattern);
                var cacheKeyPatterns =
                    JsonConvert.DeserializeObject<List<ConversationMessageV2CacheKeyPattern>>(v2MasterCacheKey);
                foreach (var cacheKeyPattern in cacheKeyPatterns)
                {
                    await _cacheManagerService.DeleteCacheAsync(cacheKeyPattern);
                }
            }
        }

        public async Task RemoveConversationMessageCache(string conversationId)
        {
            var masterCacheKeyPattern = new ConversationMessageMasterCacheKeyPattern(conversationId);

            var masterCacheKey = await _cacheManagerService.GetCacheAsync(masterCacheKeyPattern);
            if (masterCacheKey != null)
            {
                await _cacheManagerService.DeleteCacheAsync(masterCacheKeyPattern);
                var cacheKeyPatterns =
                    JsonConvert.DeserializeObject<List<ConversationMessageCacheKeyPattern>>(masterCacheKey);
                foreach (var cacheKeyPattern in cacheKeyPatterns)
                {
                    await _cacheManagerService.DeleteCacheAsync(cacheKeyPattern);
                }
            }
        }

        public async Task RemoveCache(string companyId, string conversationId)
        {
            await RemoveConversationMessageCache(conversationId);
            await RemoveConversationMessageV2Cache(companyId);
            await RemoveGetConversationMessagesCache(conversationId);
        }

        public async Task RemoveGetConversationMessagesCache(string conversationId)
        {
            var masterCacheKeyPattern = new GetConversationMessagesMasterCacheKeyPattern(conversationId);

            var masterCacheKey = await _cacheManagerService.GetCacheAsync(masterCacheKeyPattern);
            if (masterCacheKey != null)
            {
                await _cacheManagerService.DeleteCacheAsync(masterCacheKeyPattern);
                var cacheKeyPatterns =
                    JsonConvert.DeserializeObject<List<GetConversationMessagesCacheKeyPattern>>(masterCacheKey);
                foreach (var cacheKeyPattern in cacheKeyPatterns)
                {
                    await _cacheManagerService.DeleteCacheAsync(cacheKeyPattern);
                }
            }
        }
    }
}