using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Threading.Tasks;
using isRock.LineBot;
using Microsoft.AspNetCore.Http.Extensions;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Travis_backend.ChannelDomain.ViewModels;
using Travis_backend.Constants;
using Travis_backend.ConversationDomain.Models;
using Travis_backend.ConversationServices;
using Travis_backend.Database;
using Travis_backend.MessageDomain.Models;
using Travis_backend.OpenTelemetry.Meters;
using Travis_backend.OpenTelemetry.Meters.Constants;

namespace Travis_backend.MessageDomain.ChannelMessageProvider.ChannelMessageHandlers;

public class LineChannelMessageHandler : IChannelMessageHandler
{
    private readonly ApplicationDbContext _appDbContext;
    private readonly IConfiguration _configuration;
    private readonly ILogger<LineChannelMessageHandler> _logger;
    private readonly IAzureBlobStorageService _azureBlobStorageService;
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly IConversationMeters _conversationMeters;

    public string ChannelType => ChannelTypes.Line;

    public LineChannelMessageHandler(
        ApplicationDbContext appDbContext,
        IConfiguration configuration,
        ILogger<LineChannelMessageHandler> logger,
        IAzureBlobStorageService azureBlobStorageService,
        IHttpClientFactory httpClientFactory,
        IConversationMeters conversationMeters)
    {
        _appDbContext = appDbContext;
        _configuration = configuration;
        _logger = logger;
        _azureBlobStorageService = azureBlobStorageService;
        _httpClientFactory = httpClientFactory;
        _conversationMeters = conversationMeters;
    }

    public async ValueTask<ConversationMessage> SendChannelMessageAsync(
        Conversation conversation,
        ConversationMessage conversationMessage)
    {
        if (conversationMessage.LineReceiver != null && string.IsNullOrEmpty(conversationMessage.MessageUniqueID))
        {
            string messageContent = string.Empty;

            if (conversationMessage.TranslationResults != null)
            {
                messageContent = conversationMessage.TranslationResults.FirstOrDefault()?.translations
                    .FirstOrDefault().text;
            }
            else
            {
                messageContent = conversationMessage.MessageContent;
            }

            var lineConfig = await _appDbContext.ConfigLineConfigs.Where(x => x.CompanyId == conversation.CompanyId)
                .FirstOrDefaultAsync();

            if (lineConfig == null)
            {
                throw new Exception($"No Line Config found. CompanyId: {conversation.CompanyId}");
            }

            if (string.IsNullOrEmpty(lineConfig.ChannelAccessToken) || DateTime.UtcNow > lineConfig.TokenExpireAt)
            {
                var formContent = new FormUrlEncodedContent(
                    new[]
                    {
                        new KeyValuePair<string, string>("grant_type", "client_credentials"),
                        new KeyValuePair<string, string>("client_id", lineConfig.ChannelID),
                        new KeyValuePair<string, string>("client_secret", lineConfig.ChannelSecert)
                    });
                var httpClient = _httpClientFactory.CreateClient(HttpClientHandlerName.Default);

                var getAccessTokenResponse = await httpClient.PostAsync(
                    $"https://api.line.me/v2/oauth/accessToken",
                    formContent);

                if (getAccessTokenResponse.IsSuccessStatusCode)
                {
                    var accessToken = JsonConvert.DeserializeObject<LineAccessTokenResult>(
                        await getAccessTokenResponse.Content.ReadAsStringAsync());
                    lineConfig.ChannelAccessToken = accessToken.access_token;
                    lineConfig.TokenExpireAt = DateTime.UtcNow.AddSeconds(accessToken.expires_in - 30);
                    await _appDbContext.SaveChangesAsync();
                }
            }

            try
            {
                var lineBot = new isRock.LineBot.Bot(lineConfig.ChannelAccessToken);

                switch (conversationMessage.MessageType)
                {
                    case "text":
                        lineBot.PushMessage(conversationMessage.LineReceiver.userId, messageContent);

                        break;
                    case "file":
                        if (conversationMessage.UploadedFiles != null &&
                            conversationMessage.UploadedFiles.Count > 0)
                        {
                            var domainName = _configuration.GetValue<String>("Values:DomainName");

                            foreach (var uploadedFile in conversationMessage.UploadedFiles)
                            {
                                var url = $"{domainName}/Message/File/Private/{uploadedFile.FileId}";

                                if (string.Equals(
                                        "image/jpg",
                                        uploadedFile.MIMEType,
                                        StringComparison.OrdinalIgnoreCase) ||
                                    string.Equals(
                                        "image/jpeg",
                                        uploadedFile.MIMEType,
                                        StringComparison.OrdinalIgnoreCase) ||
                                    string.Equals(
                                        "image/png",
                                        uploadedFile.MIMEType,
                                        StringComparison.OrdinalIgnoreCase))
                                {
                                    lineBot.PushMessage(conversationMessage.LineReceiver.userId, new Uri(url));
                                }
                                else
                                {
                                    url = _azureBlobStorageService.GetAzureBlobSasUriForever(
                                        uploadedFile.Filename,
                                        uploadedFile.BlobContainer);

                                    // Send message as document
                                    var extension = Path.GetExtension(uploadedFile.Filename);

                                    if (!string.IsNullOrWhiteSpace(extension) && extension.Length > 1)
                                    {
                                        extension = extension[1..];
                                    }

                                    string title = Path.GetFileNameWithoutExtension(uploadedFile.Filename) ??
                                                   Guid.NewGuid().ToString();

                                    lineBot.PushMessage(
                                        conversationMessage.LineReceiver.userId,
                                        new ButtonsTemplate
                                        {
                                            title = title.Length > 40 ? title.Substring(0, 40) : title,
                                            text = (!string.IsNullOrWhiteSpace(extension)
                                                ? extension
                                                : uploadedFile.MIMEType?.Split("/").ElementAtOrDefault(1) ??
                                                  "attachment").ToUpper(),
                                            actions = new ()
                                            {
                                                new UriAction
                                                {
                                                    label = "Download", uri = new (UriHelper.Encode(new (url)))
                                                }
                                            }
                                        });
                                }
                            }

                            // Send image caption message
                            if (!string.IsNullOrEmpty(messageContent))
                            {
                                lineBot.PushMessage(conversationMessage.LineReceiver.userId, messageContent);
                            }
                        }

                        break;
                }

                conversationMessage.Status = MessageStatus.Read;

                _conversationMeters.IncrementCounter(ChannelTypes.Line, ConversationMeterOptions.SendSuccess);
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Send LINE message error for conversation {ConversationId}: {ExceptionMessage}",
                    conversation?.Id,
                    ex.Message);

                conversationMessage.Status = MessageStatus.Failed;
                conversationMessage.ChannelStatusMessage = ex.Message;

                _conversationMeters.IncrementCounter(ChannelTypes.Line, ConversationMeterOptions.SendFailed);
            }

            await _appDbContext.SaveChangesAsync();
        }

        return conversationMessage;
    }
}