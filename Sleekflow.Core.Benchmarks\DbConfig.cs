namespace Sleekflow.Core.Benchmarks;

public class DbConfig
{
    public const string Env = DevEnv;
    public const string ConnStr =
        "Server=tcp:traviscrmdbhk.database.windows.net,1433;Initial Catalog=travis-crm-prod-db;Persist Security Info=False;User ID=travis;Password=*****************************************;MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Column Encryption Setting=Enabled;Connection Timeout=30;";

    // public const string Env = StagingEnv;
    // public const string ConnStr =
    //     "...";

    // public const string Env = ProdEnv;
    // public const string ConnStr =
    //     "...";

    public const string ProdEnv = "prod";
    public const string StagingEnv = "staging";
    public const string DevEnv = "dev";
}