using System.Collections.Generic;
using System.Net.Http;
using Newtonsoft.Json;
using Travis_backend.PartnerStackIntegrationDomain.Models;

namespace Travis_backend.PartnerStackIntegrationDomain.Clients;

public class ListPartnerStackTransactionsRequest
    : PartnerStackIntegrationClientApiRequestBase<ListPartnerStackTransactionsResponse>
{
    public ListPartnerStackTransactionsRequest(
        string customerKey = null,
        string customerExternalKey = null,
        string customerEmail = null,
        string productKey = null,
        string categoryKey = null,
        int limit = 10,
        string startingAfter = null,
        string endingBefore = null)
        : base(
            $"/transactions?customer_key={customerKey}",
            HttpMethod.Get)
    {
        QueryParams = new Dictionary<string, string>
        {
            ["customer_key"] = customerKey,
            ["customer_external_key"] = customerExternalKey,
            ["customer_email"] = customerEmail,
            ["product_key"] = productKey,
            ["category_key"] = categoryKey,
            ["limit"] = limit.ToString(),
            ["starting_after"] = startingAfter,
            ["ending_before"] = endingBefore
        };
    }
}

public class ListPartnerStackTransactionsResponse
{
    [JsonProperty("data")]
    public ListPartnerStackTransactionsData Data { get; set; }

    [JsonProperty("message")]
    public string Message { get; set; }

    [JsonProperty("status")]
    public int Status { get; set; }
}