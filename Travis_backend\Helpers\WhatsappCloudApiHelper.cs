﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using CsvHelper.Configuration.Attributes;
using GraphApi.Client.Const.WhatsappCloudApi;
using Newtonsoft.Json;
using Sleekflow.Apis.MessagingHub.Model;
using Travis_backend.Extensions;

namespace Travis_backend.Helpers;

public static class WhatsappCloudApiWebhookHelper
{
    private const string HashSalt = "fTjWnZq4t7w!z%C*F-JaNdRgUkXp2s5uZr4u7x!A%C*F-JaNdRgUkXp2s5v8y/B?";

    public static string GeneratedWebhookUrl(string domain, string companyId, string messagingHubPhoneNumberId)
    {
        return domain +
               $"/whatsapp/cloudapi/webhook/{companyId}/{messagingHubPhoneNumberId}?verify_code={GeneratedVerifyCode(companyId, messagingHubPhoneNumberId)}";
    }

    public static string GeneratedVerifyCode(string companyId, string messagingHubPhoneNumberId)
    {
        return SHA256Helper.sha256_hash($"{companyId}-{messagingHubPhoneNumberId}-{HashSalt}");
    }

    public static bool IsValidVerifyCode(string verifyCode, string companyId, string messagingHubPhoneNumberId)
    {
        return verifyCode == GeneratedVerifyCode(companyId, messagingHubPhoneNumberId);
    }
}

public static class WhatsappCloudApiConversationUsageHelper
{
    public static List<ConversationUsageCsvExportDto> MapConversationUsageCsvExportDtos(
        GetWhatsappCloudApiConversationUsageAnalyticOutput analytic)
    {
        var results = analytic.ConversationUsageAnalytic
            .GranularConversationUsageAnalytics
            .OrderBy(c => c.Start)
            .Select(
                granularConversationUsageAnalytic =>
            {
                var result = new ConversationUsageCsvExportDto
                {
                    Date = granularConversationUsageAnalytic.Start,
                    WabaId = analytic.FacebookBusinessWaba.FacebookWabaId,
                    WabaName = analytic.FacebookBusinessWaba.FacebookWabaName,
                    Timezone = analytic.FacebookBusinessWaba.FacebookWabaTimezone.DisplayName,
                    BusinessInitiatedPaidQuantity = granularConversationUsageAnalytic.BusinessInitiatedPaidQuantity,
                    BusinessInitiatedFreeTierQuantity =
                        granularConversationUsageAnalytic.BusinessInitiatedFreeTierQuantity,
                    UserInitiatedPaidQuantity = granularConversationUsageAnalytic.UserInitiatedPaidQuantity,
                    UserInitiatedFreeTierQuantity = granularConversationUsageAnalytic.UserInitiatedFreeTierQuantity,
                    UserInitiatedFreeEntryPointQuantity =
                        granularConversationUsageAnalytic.UserInitiatedFreeEntryPointQuantity,
                    Currency = granularConversationUsageAnalytic.Used.CurrencyIsoCode,
                    Charges = decimal.Round(
                        granularConversationUsageAnalytic.Used.Amount,
                        2,
                        MidpointRounding.AwayFromZero),
                };

                result.Conversations = granularConversationUsageAnalytic.BusinessInitiatedPaidQuantity +
                                       granularConversationUsageAnalytic.BusinessInitiatedFreeTierQuantity +
                                       granularConversationUsageAnalytic.UserInitiatedPaidQuantity +
                                       granularConversationUsageAnalytic.UserInitiatedFreeTierQuantity +
                                       granularConversationUsageAnalytic.UserInitiatedFreeEntryPointQuantity;

                if (granularConversationUsageAnalytic.ConversationCategoryQuantities != null)
                {
                    result.AuthenticationTypeConversations = (granularConversationUsageAnalytic.ConversationCategoryQuantities
                        .TryGetValue(WhatsappConversationAnalyticConversationCategoryConst.AUTHENTICATION, out var authenticationConversations)
                        ? authenticationConversations
                        : 0) ?? 0;
                    result.MarketingTypeConversations = (granularConversationUsageAnalytic.ConversationCategoryQuantities
                        .TryGetValue(WhatsappConversationAnalyticConversationCategoryConst.MARKETING, out var marketingConversations)
                        ? marketingConversations
                        : 0) ?? 0;
                    result.ServiceTypeConversations = (granularConversationUsageAnalytic.ConversationCategoryQuantities
                        .TryGetValue(WhatsappConversationAnalyticConversationCategoryConst.SERVICE, out var serviceConversations)
                        ? serviceConversations
                        : 0) ?? 0;
                    result.UtilityTypeConversations = (granularConversationUsageAnalytic.ConversationCategoryQuantities
                        .TryGetValue(WhatsappConversationAnalyticConversationCategoryConst.UTILITY, out var utilityConversations)
                        ? utilityConversations
                        : 0) ?? 0;
                }

                return result;
            })
            .ToList();

        return results;
    }

    public static WhatsappCloudApiConversationUsageAnalyticDto SumWhatsappCloudApiConversationUsageAnalytic(
        WhatsappCloudApiConversationUsageAnalyticDto left,
        WhatsappCloudApiConversationUsageAnalyticDto right)
    {
        var totalBusinessInitiatedPaidQuantity =
            left.TotalBusinessInitiatedPaidQuantity + right.TotalBusinessInitiatedPaidQuantity;
        var totalBusinessInitiatedFreeTierQuantity = left.TotalBusinessInitiatedFreeTierQuantity +
                                                     right.TotalBusinessInitiatedFreeTierQuantity;
        var totalUserInitiatedPaidQuantity = left.TotalUserInitiatedPaidQuantity + right.TotalUserInitiatedPaidQuantity;
        var totalUserInitiatedFreeTierQuantity =
            left.TotalUserInitiatedFreeTierQuantity + right.TotalUserInitiatedFreeTierQuantity;
        var totalUserInitiatedFreeEntryPointQuantity = left.TotalUserInitiatedFreeEntryPointQuantity +
                                                       right.TotalUserInitiatedFreeEntryPointQuantity;
        var conversationCategoryQuantities = SumConversationCategoryQuantities(
            new[]
            {
                left.ConversationCategoryQuantities,
                right.ConversationCategoryQuantities
            });
        var totalUsed = AddMoney(left.TotalUsed, right.TotalUsed);
        var totalMarkup = AddMoney(left.TotalMarkup, right.TotalMarkup);
        var totalTransactionHandlingFee = AddMoney(left.TotalTransactionHandlingFee, right.TotalTransactionHandlingFee);
        var granularity = left.Granularity;
        var start = string.CompareOrdinal(left.Start, right.Start) == -1 ? left.Start : right.Start;
        var end = string.CompareOrdinal(left.End, right.End) == 1 ? left.End : right.End;

        var result = new WhatsappCloudApiConversationUsageAnalyticDto(
            totalBusinessInitiatedPaidQuantity,
            totalBusinessInitiatedFreeTierQuantity,
            totalUserInitiatedPaidQuantity,
            totalUserInitiatedFreeTierQuantity,
            totalUserInitiatedFreeEntryPointQuantity,
            conversationCategoryQuantities,
            totalUsed,
            totalMarkup,
            totalTransactionHandlingFee,
            granularity,
            start,
            end);

        return result;
    }

    public static Money AddMoney(Money left, Money right)
    {
        if (left == null && right == null)
        {
            return null;
        }

        if (!(left != null && right != null))
        {
            return left ?? right;
        }

        if (left.CurrencyIsoCode != right.CurrencyIsoCode)
        {
            throw new NotImplementedException();
        }

        return new Money(left.CurrencyIsoCode, left.Amount + right.Amount);
    }

    public static Dictionary<string, int?> SumConversationCategoryQuantities(
        IEnumerable<Dictionary<string, int?>> conversationCategoryQuantitiesList)
    {
        if (conversationCategoryQuantitiesList == null)
        {
            return null;
        }

        conversationCategoryQuantitiesList = conversationCategoryQuantitiesList.Where(x => x != null);

        if (!conversationCategoryQuantitiesList.Any())
        {
            return null;
        }

        var sumConversationCategoryQuantities = new Dictionary<string, int?>();

        foreach (var conversationCategoryQuantities in conversationCategoryQuantitiesList)
        {
            foreach (var (key, value) in conversationCategoryQuantities)
            {
                sumConversationCategoryQuantities.TryAdd(key, 0);

                sumConversationCategoryQuantities[key] += value;
            }
        }

        return sumConversationCategoryQuantities;
    }

    public static bool IsConversationCategoryQuantitiesEqual(
        Dictionary<string, int?> left,
        Dictionary<string, int?> right)
    {
        if (left != null && right == null)
        {
            return false;
        }

        if (left == null && right != null)
        {
            return false;
        }

        if (left == null && right == null)
        {
            return true;
        }

        return left!.Count == right!.Count && !left.Except(right).Any();
    }

    public class ConversationUsageCsvExportDto
    {
        [Name("Date")]
        public string Date { get; set; }

        [Name("Waba Id")]
        public string WabaId { get; set; }

        [Name("Waba Name")]
        public string WabaName { get; set; }

        [Name("Timezone")]
        public string Timezone { get; set; }

        [Name("Conversations")]
        public int Conversations { get; set; }

        [Name("AuthenticationTypeConversations")]
        public int AuthenticationTypeConversations { get; set; }

        [Name("MarketingTypeConversations")]
        public int MarketingTypeConversations { get; set; }

        [Name("ServiceTypeConversations")]
        public int ServiceTypeConversations { get; set; }

        [Name("UtilityTypeConversations")]
        public int UtilityTypeConversations { get; set; }

        [Name("Business-initiated Paid Conversations")]
        public int BusinessInitiatedPaidQuantity { get; set; }

        [Name("Business-initiated Free Tier Conversations")]
        public int BusinessInitiatedFreeTierQuantity { get; set; }

        [Name("User-initiated Paid Conversations")]
        public int UserInitiatedPaidQuantity { get; set; }

        [Name("User-initiated Free Tier Conversations")]
        public int UserInitiatedFreeTierQuantity { get; set; }

        [Name("User-initiated Free Entry Point Conversations")]
        public int UserInitiatedFreeEntryPointQuantity { get; set; }

        [Name("Currency")]
        public string Currency { get; set; }

        [Name("Charges")]
        public decimal Charges { get; set; }
    }
}

public static class WhatsappCloudApiCacheKeyHelper
{
    public static string GetTemplateResponseCacheKey(string companyId, string wabaId)
    {
        var cacheKey = $"WhatsappCloudApi_GetTemplates_{companyId}_{wabaId}";
        return cacheKey;
    }

    public static string GetTemplateBookmarksCacheKey(string companyId, string wabaId)
    {
        var cacheKey = $"WhatsappCloudApi_GetTemplateBookmarks_{companyId}_{wabaId}";
        return cacheKey;
    }

    public static string GetAddNewContactRedLockKey(string companyId, string phoneNumber)
    {
        return $"WhatsappCloudApi_new_contact_redlock_{companyId}_{phoneNumber}";
    }

    public static string GetWabaProductCatalogPhoneNumberCommerceSettingsCacheKey(string companyId, string wabaId, string phoneNumberId)
    {
        var cacheKey = $"WhatsappCloudApi_GetWabaProductCatalogPhoneNumberCommerceSettings_{companyId}_{wabaId}_{phoneNumberId}";
        return cacheKey;
    }

    public static string GetProductCatalogProductItemsCacheKey(string companyId, string wabaId)
    {
        var cacheKey = $"WhatsappCloudApi_GetProductCatalogProductItems_{companyId}_{wabaId}";

        return cacheKey;
    }

    public static string GetFacebookWabasProductCatalogsCacheKey(string companyId)
    {
        var cacheKey = $"WhatsappCloudApi_GetFacebookWabasProductCatalogs_{companyId}";

        return cacheKey;
    }
}

public static class WhatsappCloudApiExtendedMessageHelper
{
    public static bool CheckTextContainsUrl(string text)
    {
        if (string.IsNullOrWhiteSpace(text))
        {
            return false;
        }

        return Regex.IsMatch(
            text,
            @"(^\s|)(http://|https://)([a-z0-9]+\.[A-Za-z]{2,4}(\s|))",
            RegexOptions.IgnoreCase | RegexOptions.Compiled);
    }

    // public static string FormatParamToBodyText(this string messageBody, List<TemplateMessageComponentObject> components)
    // {
    //     if (components == null)
    //         return messageBody;
    //
    //     var bodyComponentObject = components.FirstOrDefault(x => x.Type == ComponentType.body);
    //
    //     if (bodyComponentObject == null)
    //         return messageBody;
    //
    //     if (bodyComponentObject.Parameters == null)
    //         return messageBody;
    //
    //     var bodyParams = bodyComponentObject.Parameters.ToArray();
    //
    //     for (var i = 0; i < bodyParams.Length; i++)
    //     {
    //         messageBody = messageBody.Replace("{{" + (i + 1) + "}}", bodyParams[i].Text);
    //     }
    //
    //     return messageBody;
    // }
    public static List<WhatsappCloudApiTemplateMessageComponentObject> Format(
        this List<WhatsappCloudApiTemplateMessageComponentObject> components,
        List<string> parameters)
    {
        if (components == null)
        {
            return null;
        }

        var objectsWithUserProfileParams = JsonConvert.SerializeObject(
            components,
            new JsonSerializerSettings
            {
                NullValueHandling = NullValueHandling.Ignore
            });
        objectsWithUserProfileParams =
            objectsWithUserProfileParams.SafeFormat(parameters.Select(x => x.ToString()).ToArray());

        return JsonConvert.DeserializeObject<List<WhatsappCloudApiTemplateMessageComponentObject>>(
            objectsWithUserProfileParams);
    }

    public static WhatsappCloudApiInteractiveObject Format(
        this WhatsappCloudApiInteractiveObject interactiveObject,
        List<string> parameters)
    {
        if (interactiveObject == null)
        {
            return null;
        }

        var interactiveObjectWithUserProfileParams = JsonConvert.SerializeObject(
            interactiveObject,
            new JsonSerializerSettings
            {
                NullValueHandling = NullValueHandling.Ignore
            });
        interactiveObjectWithUserProfileParams =
            interactiveObjectWithUserProfileParams.SafeFormat(parameters.Select(x => x.ToString()).ToArray());

        return JsonConvert.DeserializeObject<WhatsappCloudApiInteractiveObject>(interactiveObjectWithUserProfileParams);
    }

    public static List<string> WhatsappCloudApiProductMessageValidStockAvailability()
    {
        return new List<string>()
        {
            "in stock", "preorder", "available for order"
        };
    }

    public static List<string> WhatsappCloudApiProductMessageInValidStockAvailability()
    {
        return new List<string>()
        {
            "out of stock", "discontinued", "pending"
        };
    }
}

public static class WhatsappCloudApiFileHelper
{
    private const string Salt = "q3t6w9z$C&F)J@NcRy$B&E)H@McQfThWmZq4t7w!z%C*F-JaNfUjWnZr4u7x!A%D";

    public static string GenerateHashCodeForFile(string fileId)
    {
        return SHA256Helper.sha256_hash($"{fileId}{Salt}");
    }

    public static bool CheckHashCodeIsValid(string fileId, string hashCode)
    {
        if (string.IsNullOrWhiteSpace(fileId) || string.IsNullOrWhiteSpace(hashCode))
        {
            return false;
        }

        var validCode = GenerateHashCodeForFile(fileId);

        return hashCode == validCode;
    }

    public const long AllowAudioFileByteSize = 16000000; // 16MB

    public static List<string> AllowedAudioContentTypes => new ()
    {
        "audio/aac",
        "audio/mp4",
        "audio/mpeg",
        "audio/amr",
        "audio/ogg",
    };

    public const long AllowedImageFileByteSize = 5000000; // 5MB

    public static List<string> AllowedImageContentTypes => new ()
    {
        "image/jpeg", "image/png"
    };

    public const long AllowedStickerFileByteSize = 500000; // 500KB

    public static List<string> AllowedStickerContentTypes => new ()
    {
        "image/webp"
    };

    public const long AllowedDocumentFileByteSize = 100000000; // 100MB

    public static List<string> AllowedTemplateDocumentContentTypes => new ()
    {
        "text/plain",
        "application/pdf",
        "application/vnd.ms-powerpoint",
        "application/msword",
        "application/vnd.ms-excel",
        "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
        "application/vnd.openxmlformats-officedocument.presentationml.presentation",
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    };

    public const long AllowedVideoFileByteSize = 16000000; // 16MB

    public static List<string> AllowedVideoContentTypes => new ()
    {
        "video/mp4", "video/3gpp"
    };
}

public static class WhatsappCloudApiMessageObjectFactory
{
    public static WhatsappCloudApiMessageObject CreateTextMessage(
        string whatsappReceiverId,
        string textMessage,
        Dictionary<string, string> callbackDataDict,
        bool previewUrl = false,
        string messageIdToReply = null)
    {
        var message = new WhatsappCloudApiMessageObject
        {
            RecipientType = "individual",
            MessagingProduct = "whatsapp",
            To = whatsappReceiverId,
            Type = WhatsappCloudApiMessageTypeConst.text,
            Text = new WhatsappCloudApiTextObject()
            {
                Body = textMessage, PreviewUrl = previewUrl
            },
            BizOpaqueCallbackData = callbackDataDict.FormatQueryString().ToString()
        };

        if (messageIdToReply != null)
        {
            message.Context = new WhatsappCloudApiContextObject(messageIdToReply);
        }

        return message;
    }

    public static WhatsappCloudApiMessageObject CreateReactionMessage(
        string whatsappReceiverId,
        string messageIdToReact,
        string emoji,
        Dictionary<string, string> callbackDataDict)
    {
        return new WhatsappCloudApiMessageObject()
        {
            RecipientType = "individual",
            MessagingProduct = "whatsapp",
            To = whatsappReceiverId,
            Type = WhatsappCloudApiMessageTypeConst.reaction,
            Reaction = new WhatsappCloudApiReactionObject()
            {
                MessageId = messageIdToReact, Emoji = emoji
            },
            BizOpaqueCallbackData = callbackDataDict.FormatQueryString().ToString()
        };
    }

    public static WhatsappCloudApiMessageObject CreateImageMessageByMediaId(
        string whatsappReceiverId,
        string mediaId,
        Dictionary<string, string> callbackDataDict,
        string? caption = null,
        string? fileName = null,
        string messageIdToReply = null)
    {
        var message = new WhatsappCloudApiMessageObject
        {
            RecipientType = "individual",
            MessagingProduct = "whatsapp",
            To = whatsappReceiverId,
            Type = WhatsappCloudApiMessageTypeConst.image,
            Image = new WhatsappCloudApiMediaObject()
            {
                Id = mediaId, Caption = caption, Filename = fileName
            },
            BizOpaqueCallbackData = callbackDataDict.FormatQueryString().ToString()
        };

        if (messageIdToReply != null)
        {
            message.Context = new WhatsappCloudApiContextObject(messageIdToReply);
        }

        return message;
    }

    public static WhatsappCloudApiMessageObject CreateImageMessageByMediaLink(
        string whatsappReceiverId,
        string imageLink,
        Dictionary<string, string> callbackDataDict,
        string? caption = null,
        string? fileName = null,
        WhatsappCloudApiProviderObject provider = null,
        string messageIdToReply = null)
    {
        var message = new WhatsappCloudApiMessageObject
        {
            RecipientType = "individual",
            MessagingProduct = "whatsapp",
            To = whatsappReceiverId,
            Type = WhatsappCloudApiMessageTypeConst.image,
            Image = new WhatsappCloudApiMediaObject()
            {
                Link = imageLink, Caption = caption, Filename = fileName, Provider = provider
            },
            BizOpaqueCallbackData = callbackDataDict.FormatQueryString().ToString()
        };

        if (messageIdToReply != null)
        {
            message.Context = new WhatsappCloudApiContextObject(messageIdToReply);
        }

        return message;
    }

    public static WhatsappCloudApiMessageObject CreateVideoMessageByMediaId(
        string whatsappReceiverId,
        string mediaId,
        string caption,
        Dictionary<string, string> callbackDataDict,
        string messageIdToReply = null)
    {
        var message = new WhatsappCloudApiMessageObject
        {
            RecipientType = "individual",
            MessagingProduct = "whatsapp",
            To = whatsappReceiverId,
            Type = WhatsappCloudApiMessageTypeConst.video,
            Video = new WhatsappCloudApiMediaObject()
            {
                Id = mediaId, Caption = caption
            },
            BizOpaqueCallbackData = callbackDataDict.FormatQueryString().ToString()
        };

        if (messageIdToReply != null)
        {
            message.Context = new WhatsappCloudApiContextObject(messageIdToReply);
        }

        return message;
    }

    public static WhatsappCloudApiMessageObject CreateVideoMessageByMediaLink(
        string whatsappReceiverId,
        string imageLink,
        string caption,
        Dictionary<string, string> callbackDataDict,
        WhatsappCloudApiProviderObject provider = null,
        string messageIdToReply = null)
    {
        var message = new WhatsappCloudApiMessageObject
        {
            RecipientType = "individual",
            MessagingProduct = "whatsapp",
            To = whatsappReceiverId,
            Type = WhatsappCloudApiMessageTypeConst.video,
            Video = new WhatsappCloudApiMediaObject()
            {
                Link = imageLink, Caption = caption, Provider = provider
            },
            BizOpaqueCallbackData = callbackDataDict.FormatQueryString().ToString()
        };

        if (messageIdToReply != null)
        {
            message.Context = new WhatsappCloudApiContextObject(messageIdToReply);
        }

        return message;
    }

    public static WhatsappCloudApiMessageObject CreateDocumentMessageByMediaId(
        string whatsappReceiverId,
        string fileName,
        string mediaId,
        string caption,
        Dictionary<string, string> callbackDataDict,
        string messageIdToReply = null)
    {
        var message = new WhatsappCloudApiMessageObject
        {
            RecipientType = "individual",
            MessagingProduct = "whatsapp",
            To = whatsappReceiverId,
            Type = WhatsappCloudApiMessageTypeConst.document,
            Document = new WhatsappCloudApiMediaObject()
            {
                Id = mediaId, Filename = fileName, Caption = caption,
            },
            BizOpaqueCallbackData = callbackDataDict.FormatQueryString().ToString()
        };

        if (messageIdToReply != null)
        {
            message.Context = new WhatsappCloudApiContextObject(messageIdToReply);
        }

        return message;
    }

    public static WhatsappCloudApiMessageObject CreateDocumentMessageByLink(
        string whatsappReceiverId,
        string fileName,
        string documentLink,
        string caption,
        Dictionary<string, string> callbackDataDict,
        WhatsappCloudApiProviderObject provider = null,
        string messageIdToReply = null)
    {
        var message = new WhatsappCloudApiMessageObject
        {
            RecipientType = "individual",
            MessagingProduct = "whatsapp",
            To = whatsappReceiverId,
            Type = WhatsappCloudApiMessageTypeConst.document,
            Document = new WhatsappCloudApiMediaObject()
            {
                Link = documentLink, Caption = caption, Filename = fileName, Provider = provider,
            },
            BizOpaqueCallbackData = callbackDataDict.FormatQueryString().ToString()
        };

        if (messageIdToReply != null)
        {
            message.Context = new WhatsappCloudApiContextObject(messageIdToReply);
        }

        return message;
    }

    public static WhatsappCloudApiMessageObject CreateAudioMessageByMediaId(
        string whatsappReceiverId,
        string mediaId,
        Dictionary<string, string> callbackDataDict,
        string messageIdToReply = null)
    {
        var message = new WhatsappCloudApiMessageObject
        {
            RecipientType = "individual",
            MessagingProduct = "whatsapp",
            To = whatsappReceiverId,
            Type = WhatsappCloudApiMessageTypeConst.audio,
            Audio = new WhatsappCloudApiMediaObject()
            {
                Id = mediaId,
            },
            BizOpaqueCallbackData = callbackDataDict.FormatQueryString().ToString()
        };

        if (messageIdToReply != null)
        {
            message.Context = new WhatsappCloudApiContextObject(messageIdToReply);
        }

        return message;
    }

    public static WhatsappCloudApiMessageObject CreateAudioMessageByMediaLink(
        string whatsappReceiverId,
        string audioLink,
        Dictionary<string, string> callbackDataDict,
        WhatsappCloudApiProviderObject provider = null,
        string messageIdToReply = null)
    {
        var message = new WhatsappCloudApiMessageObject
        {
            RecipientType = "individual",
            MessagingProduct = "whatsapp",
            To = whatsappReceiverId,
            Type = WhatsappCloudApiMessageTypeConst.audio,
            Audio = new WhatsappCloudApiMediaObject()
            {
                Link = audioLink, Provider = provider,
            },
            BizOpaqueCallbackData = callbackDataDict.FormatQueryString().ToString()
        };

        if (messageIdToReply != null)
        {
            message.Context = new WhatsappCloudApiContextObject(messageIdToReply);
        }

        return message;
    }

    public static WhatsappCloudApiMessageObject CreateStickerMessageByMediaId(
        string whatsappReceiverId,
        string mediaId,
        Dictionary<string, string> callbackDataDict,
        string messageIdToReply = null)
    {
        var message = new WhatsappCloudApiMessageObject
        {
            RecipientType = "individual",
            MessagingProduct = "whatsapp",
            To = whatsappReceiverId,
            Type = WhatsappCloudApiMessageTypeConst.sticker,
            Sticker = new WhatsappCloudApiMediaObject()
            {
                Id = mediaId,
            },
            BizOpaqueCallbackData = callbackDataDict.FormatQueryString().ToString()
        };

        if (messageIdToReply != null)
        {
            message.Context = new WhatsappCloudApiContextObject(messageIdToReply);
        }

        return message;
    }

    public static WhatsappCloudApiMessageObject CreateStickerMessageByLink(
        string whatsappReceiverId,
        string stickerLink,
        Dictionary<string, string> callbackDataDict,
        WhatsappCloudApiProviderObject provider = null)
    {
        return new WhatsappCloudApiMessageObject
        {
            RecipientType = "individual",
            MessagingProduct = "whatsapp",
            To = whatsappReceiverId,
            Type = WhatsappCloudApiMessageTypeConst.sticker,
            Sticker = new WhatsappCloudApiMediaObject()
            {
                Link = stickerLink, Provider = provider
            },
            BizOpaqueCallbackData = callbackDataDict.FormatQueryString().ToString()
        };
    }

    public static WhatsappCloudApiMessageObject CreateTemplateMessage(
        string whatsappReceiverId,
        string templateName,
        string language,
        List<WhatsappCloudApiTemplateMessageComponentObject> components,
        Dictionary<string, string> callbackDataDict)
    {
        var message = new WhatsappCloudApiMessageObject
        {
            MessagingProduct = "whatsapp",
            To = whatsappReceiverId,
            Type = WhatsappCloudApiMessageTypeConst.template,
            Template = new WhatsappCloudApiTemplateMessageObject()
            {
                Name = templateName,
                Language = new WhatsappCloudApiLanguageObject()
                {
                    Code = language, Policy = "deterministic"
                },
                Components = components,
            },
            BizOpaqueCallbackData = callbackDataDict.FormatQueryString().ToString()
        };

        return message;
    }

    public static WhatsappCloudApiMessageObject CreateInteractiveMessage(
        string whatsappId,
        WhatsappCloudApiInteractiveObject interactiveObject,
        Dictionary<string, string> callbackDataDict)
    {
        return new WhatsappCloudApiMessageObject
        {
            RecipientType = "individual",
            MessagingProduct = "whatsapp",
            To = whatsappId,
            Type = WhatsappCloudApiMessageTypeConst.interactive,
            Interactive = interactiveObject,
            BizOpaqueCallbackData = callbackDataDict.FormatQueryString().ToString()
        };
    }

    public static WhatsappCloudApiMessageObject CreateSingleProductMessage(
        string whatsappId,
        string bodyText,
        string footerText,
        string catalogId,
        string productRetailerId,
        Dictionary<string, string> callbackDataDict)
    {
        var productMessageInteractiveObject = new WhatsappCloudApiInteractiveObject()
        {
            Type = WhatsappCloudApiInteractiveTypeConst.product,
        };

        if (!string.IsNullOrWhiteSpace(bodyText))
        {
            productMessageInteractiveObject.Body = new WhatsappCloudApiTextBodyObject()
            {
                Text = bodyText
            };
        }

        if (!string.IsNullOrWhiteSpace(footerText))
        {
            productMessageInteractiveObject.Footer = new WhatsappCloudApiTextFooterObject()
            {
                Text = footerText
            };
        }

        productMessageInteractiveObject.Action = new WhatsappCloudApiActionObject()
        {
            CatalogId = catalogId, ProductRetailerId = productRetailerId
        };

        return new WhatsappCloudApiMessageObject
        {
            RecipientType = "individual",
            MessagingProduct = "whatsapp",
            To = whatsappId,
            Type = WhatsappCloudApiMessageTypeConst.interactive,
            Interactive = productMessageInteractiveObject,
            BizOpaqueCallbackData = callbackDataDict.FormatQueryString().ToString()
        };
    }

    public static WhatsappCloudApiMessageObject CreateMultiProductMessage(
        string whatsappId,
        string bodyText,
        string footerText,
        List<WhatsappCloudApiSectionObject> productSections,
        Dictionary<string, string> callbackDataDict)
    {
        var multiProductMessageInteractiveObject = new WhatsappCloudApiInteractiveObject()
        {
            Type = WhatsappCloudApiInteractiveTypeConst.product_list,
        };

        if (!string.IsNullOrWhiteSpace(bodyText))
        {
            multiProductMessageInteractiveObject.Body = new WhatsappCloudApiTextBodyObject()
            {
                Text = bodyText
            };
        }

        if (!string.IsNullOrWhiteSpace(footerText))
        {
            multiProductMessageInteractiveObject.Footer = new WhatsappCloudApiTextFooterObject()
            {
                Text = footerText
            };
        }

        multiProductMessageInteractiveObject.Action = new WhatsappCloudApiActionObject()
        {
            Sections = productSections
        };

        return new WhatsappCloudApiMessageObject
        {
            RecipientType = "individual",
            MessagingProduct = "whatsapp",
            To = whatsappId,
            Type = WhatsappCloudApiMessageTypeConst.interactive,
            Interactive = multiProductMessageInteractiveObject,
            BizOpaqueCallbackData = callbackDataDict.FormatQueryString().ToString()
        };
    }

    public static WhatsappCloudApiMessageObject CreateLocationMessage(
        string whatsappId,
        double latitude,
        double longitude,
        Dictionary<string, string> callbackDataDict,
        string name = null,
        string address = null,
        string messageIdToReply = null)
    {
        var message = new WhatsappCloudApiMessageObject
        {
            RecipientType = "individual",
            MessagingProduct = "whatsapp",
            To = whatsappId,
            Type = WhatsappCloudApiMessageTypeConst.location,
            Location = new WhatsappCloudApiLocationObject()
            {
                Latitude = latitude, Longitude = longitude, Name = name, Address = address,
            },
            BizOpaqueCallbackData = callbackDataDict.FormatQueryString().ToString()
        };

        if (messageIdToReply != null)
        {
            message.Context = new WhatsappCloudApiContextObject(messageIdToReply);
        }

        return message;
    }

    public static WhatsappCloudApiMessageObject CreateContactsMessage(
        string whatsappId,
        List<WhatsappCloudApiContactObject> contacts,
        Dictionary<string, string> callbackDataDict,
        string messageIdToReply = null)
    {
        var message = new WhatsappCloudApiMessageObject
        {
            RecipientType = "individual",
            MessagingProduct = "whatsapp",
            To = whatsappId,
            Type = WhatsappCloudApiMessageTypeConst.contacts,
            Contacts = contacts,
            BizOpaqueCallbackData = callbackDataDict.FormatQueryString().ToString()
        };

        if (messageIdToReply != null)
        {
            message.Context = new WhatsappCloudApiContextObject(messageIdToReply);
        }

        return message;
    }
}