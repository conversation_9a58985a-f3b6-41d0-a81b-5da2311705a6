using System.Collections;
using System.Collections.Generic;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Options;
using Travis_backend.Infrastructures.Options;

namespace Travis_backend.Controllers.UtilityControllers;

[Route("app")]
public class ApplicationController : Controller
{
    private readonly GlobalPricingOptions _globalPricingOptions;

    private readonly IConfiguration _configuration;

    public ApplicationController(IOptions<GlobalPricingOptions> globalPricingOptions, IConfiguration configuration)
    {
        _configuration = configuration;
        _globalPricingOptions = globalPricingOptions.Value;
    }

    [AllowAnonymous]
    [HttpGet("feature-info")]
    public IActionResult GetAppFeatureInfo()
    {
        var result = new Dictionary<string, bool>();

        var featureFlagsSection = _configuration.GetSection("FeatureFlags");
        var featureFlags = featureFlagsSection.GetChildren();

        foreach (var featureFlag in featureFlags)
        {
            var key = $"is{featureFlag.Key}Enabled";
            result[key] = featureFlag.GetValue<bool>("IsEnabled");
        }

        result["isGlobalPricingFeatureEnabled"] = _globalPricingOptions.IsFeatureEnabled;
        result["isPlanMigrationIncentiveCampaignPeriod"] = _globalPricingOptions.IsPlanMigrationIncentiveCampaignPeriod;

        return Ok(result);
    }
}