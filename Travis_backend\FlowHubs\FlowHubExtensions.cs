﻿using System.Net.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Scrutor;
using Sleekflow.Apis.FlowHub.Api;
using Travis_backend.Constants;
using Travis_backend.FlowHubs.HttpClientHandlers;
using Travis_backend.FlowHubs.Interfaces;
using Travis_backend.FlowHubs.Services;

namespace Travis_backend.FlowHubs;

public static class FlowHubExtensions
{
    public static IServiceCollection RegisterFlowHubServices(
        this IServiceCollection services,
        IConfiguration configuration)
    {
        var flowHubConfig = new Sleekflow.Apis.FlowHub.Client.Configuration
        {
            BasePath = configuration.GetValue<string>("FlowHub:Endpoint")
        };

        services.AddSingleton(flowHubConfig);
        services.AddScoped<IFlowHubService, FlowHubService>();
        services.AddScoped<IFlowHubConfigService, FlowHubConfigService>();
        services.AddScoped<IFlowHubExecutionService, FlowHubExecutionService>();
        services.AddScoped<IUserProfileHooks, UserProfileHooks>();
        services.AddScoped<IFlowHubScheduledWorkflowEnrollmentService, FlowHubScheduledWorkflowEnrollmentService>();
        services.AddScoped<IStaffHooks, StaffHooks>();
        services.AddScoped<IUserProfileLabelHooks, UserProfileLabelHooks>();
        services.AddScoped<IMessageTemplateHooks, MessageTemplateHooks>();
        services.AddScoped<IUserProfileListHooks, UserProfileListHooks>();
        services.AddScoped<IBroadcastHooks, BroadcastHooks>();

        services.AddTransient<FlowHubHttpClientHandler>();
        services.AddHttpClient(
            HttpClientHandlerName.FlowHub,
            (sp, client) =>
            {
                var config = sp.GetRequiredService<IConfiguration>();
                client.DefaultRequestHeaders.TryAddWithoutValidation(
                    "X-Sleekflow-Key",
                    config.GetValue<string>("FlowHub:Key"));
            });

        services.AddScoped<IBlobsApi, BlobsApi>(
            sp =>
                new (
                    sp.GetRequiredService<IHttpClientFactory>()
                        .CreateClient(HttpClientHandlerName.FlowHub),
                    sp.GetRequiredService<Sleekflow.Apis.FlowHub.Client.Configuration>(),
                    sp.GetRequiredService<FlowHubHttpClientHandler>()));

        services.AddScoped<IEventsApi, EventsApi>(
            sp =>
                new (
                    sp.GetRequiredService<IHttpClientFactory>()
                        .CreateClient(HttpClientHandlerName.FlowHub),
                    sp.GetRequiredService<Sleekflow.Apis.FlowHub.Client.Configuration>(),
                    sp.GetRequiredService<FlowHubHttpClientHandler>()));

        services.AddScoped<IExecutionsApi, ExecutionsApi>(
            sp =>
                new (
                    sp.GetRequiredService<IHttpClientFactory>()
                        .CreateClient(HttpClientHandlerName.FlowHub),
                    sp.GetRequiredService<Sleekflow.Apis.FlowHub.Client.Configuration>(),
                    sp.GetRequiredService<FlowHubHttpClientHandler>()));

        services.AddScoped<IFlowHubConfigsApi, FlowHubConfigsApi>(
            sp =>
                new (
                    sp.GetRequiredService<IHttpClientFactory>()
                        .CreateClient(HttpClientHandlerName.FlowHub),
                    sp.GetRequiredService<Sleekflow.Apis.FlowHub.Client.Configuration>(),
                    sp.GetRequiredService<FlowHubHttpClientHandler>()));

        services.AddScoped<IStatesApi, StatesApi>(
            sp =>
                new (
                    sp.GetRequiredService<IHttpClientFactory>()
                        .CreateClient(HttpClientHandlerName.FlowHub),
                    sp.GetRequiredService<Sleekflow.Apis.FlowHub.Client.Configuration>(),
                    sp.GetRequiredService<FlowHubHttpClientHandler>()));

        services.AddScoped<IWorkflowsApi, WorkflowsApi>(
            sp =>
                new (
                    sp.GetRequiredService<IHttpClientFactory>()
                        .CreateClient(HttpClientHandlerName.FlowHub),
                    sp.GetRequiredService<Sleekflow.Apis.FlowHub.Client.Configuration>(),
                    sp.GetRequiredService<FlowHubHttpClientHandler>()));

        services.AddScoped<IWorkflowGroupsApi, WorkflowGroupsApi>(
            sp =>
                new (
                    sp.GetRequiredService<IHttpClientFactory>()
                        .CreateClient(HttpClientHandlerName.FlowHub),
                    sp.GetRequiredService<Sleekflow.Apis.FlowHub.Client.Configuration>(),
                    sp.GetRequiredService<FlowHubHttpClientHandler>()));

        return services;
    }

    public static IServiceCollection RegisterUpdateContactOwnerRelationshipsAssignmentStrategyHandlers(
        this IServiceCollection services)
    {
        services.Scan(
            selector =>
                selector.FromAssemblyOf<IUpdateContactOwnerRelationshipsAssignmentStrategyHandler>()
                    .AddClasses(
                        classes =>
                            classes
                                .Where(c => !c.IsAbstract && !c.IsInterface)
                                .AssignableTo(typeof(IUpdateContactOwnerRelationshipsAssignmentStrategyHandler)))
                    .UsingRegistrationStrategy(RegistrationStrategy.Append)
                    .AsImplementedInterfaces()
                    .WithScopedLifetime());

        return services;
    }
}