using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.WebJobs;
using Microsoft.Azure.WebJobs.Extensions.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Polly;
using ShopifySharp;
using ShopifySharp.Filters;
using Sleekflow.SleekPay.Model;
using Sleekflow.SleekPay.Service;
using Sleekflow.SleekPay.SQLDatabase;
using Sleekflow.SleekPay.SQLDatabase.Entity;
using Stripe;
using Stripe.Reporting;
using Policy = Polly.Policy;

namespace Sleekflow.SleekPay.Functions;

public class StripeWebhook
{
    private readonly ApplicationDbContext _appDbContext;
    private readonly IStripePaymentService _stripePaymentService;
    private readonly IShopifyService _shopifyService;
    private readonly IStripeReportService _stripeReportService;

    public StripeWebhook(
        ApplicationDbContext appDbContext,
        IStripePaymentService stripePaymentService,
        IShopifyService shopifyService,
        IStripeReportService stripeReportService)
    {
        _appDbContext = appDbContext;
        _stripePaymentService = stripePaymentService;
        _shopifyService = shopifyService;
        _stripeReportService = stripeReportService;
    }

    [FunctionName("StripeConnectWebhook")]
    public async Task<IActionResult> RunWebhookConnect([HttpTrigger(AuthorizationLevel.Anonymous, "get", "post", Route = "webhook/connect")] HttpRequest req, ILogger log)
    {
        var json = await new StreamReader(req.Body).ReadToEndAsync();

        // If you are testing your webhook locally with the Stripe CLI you
        // can find the endpoint's secret by running `stripe listen`
        // Otherwise, find your endpoint's secret in your webhook settings
        // in the Developer Dashboard

        // Verify webhook signature and extract the event.
        // See https://stripe.com/docs/webhooks/signatures for more information.
        try
        {
            var stripeEventUnverified = JsonConvert.DeserializeObject<Stripe.Event>(json);

            if (stripeEventUnverified.Type == Events.AccountApplicationDeauthorized || stripeEventUnverified.Type == Events.AccountUpdated)
            {
                var stripePaymentConfig = await _appDbContext.ConfigStripePaymentConfigs.FirstOrDefaultAsync(x => x.AccountId == stripeEventUnverified.Account);

                if (stripePaymentConfig is null)
                {
                    return new OkObjectResult(
                        $"Events related to Stripe account {stripeEventUnverified.Account} is not to be handled in this environment");
                }

                SetStripeApiKeyByPlatformCountry(stripePaymentConfig.Country);

                var endpointSecret = GetStripeWebhookSecretByPlatformCountry(stripePaymentConfig.Country, "connect");

                var stripeEvent = EventUtility.ConstructEvent(json, req.Headers["Stripe-Signature"], endpointSecret);

                if (stripeEvent.Type == Events.AccountApplicationDeauthorized)
                {
                    var connectedAccountId = stripeEvent.Account;
                    await HandleDeauthorization(connectedAccountId);
                }

                if (stripeEvent.Type == Events.AccountUpdated)
                {
                    var account = stripeEvent.Data.Object as Account;
                    await HandleAccountUpdate(account);
                }
            }

            return new OkResult();
        }
        catch (Exception e)
        {
            return new BadRequestObjectResult(e);
        }
    }

    [FunctionName("StripePaymentWebhook")]
    public async Task<IActionResult> RunPaymentWebhookConnect([HttpTrigger(AuthorizationLevel.Anonymous, "get", "post", Route = "webhook/payment")] HttpRequest req, ILogger log)
    {
        var json = await new StreamReader(req.Body).ReadToEndAsync();

        // If you are testing your webhook locally with the Stripe CLI you
        // can find the endpoint's secret by running `stripe listen`
        // Otherwise, find your endpoint's secret in your webhook settings
        // in the Developer Dashboard

        // Verify webhook signature and extract the event.
        // See https://stripe.com/docs/webhooks/signatures for more information.
        try
        {
            var stripeEventUnverified = JsonConvert.DeserializeObject<Stripe.Event>(json);

            if (stripeEventUnverified.Type == Events.PaymentIntentSucceeded)
            {
                var paymentIntentUnverified = stripeEventUnverified.Data.Object as PaymentIntent;

                var stripePaymentRecord = await _appDbContext.StripePaymentRecords.FirstOrDefaultAsync(x => x.StripePaymentIntentId == paymentIntentUnverified.Id);

                if (stripePaymentRecord is null)
                {
                    return new OkObjectResult(
                        $"Events related to Stripe payment intent {paymentIntentUnverified?.Id} is not to be handled in this environment");
                }

                SetStripeApiKeyByPlatformCountry(stripePaymentRecord.PlatformCountry);

                var endpointSecret = GetStripeWebhookSecretByPlatformCountry(stripePaymentRecord.PlatformCountry, "payment");

                var stripeEvent = EventUtility.ConstructEvent(json, req.Headers["Stripe-Signature"], endpointSecret);

                if (stripeEvent.Type == Events.PaymentIntentSucceeded)
                {
                    var paymentIntent = stripeEvent.Data.Object as PaymentIntent;

                    await HandleSuccessfulPaymentIntent(paymentIntent);
                }

                // if (stripeEvent.Type == Events.PaymentIntentCanceled) {
                //     var paymentIntent = stripeEvent.Data.Object as PaymentIntent;
                //     await HandleCanceledPaymentIntent(paymentIntent);
                // }
            }

            if (stripeEventUnverified.Type == Events.ChargeRefundUpdated)
            {
                var refundUnverified = stripeEventUnverified.Data.Object as Stripe.Refund;

                var stripePaymentRecord = await _appDbContext.StripePaymentRecords.FirstOrDefaultAsync(x => x.StripePaymentIntentId == refundUnverified.PaymentIntentId);

                if (stripePaymentRecord is null)
                {
                    return new OkObjectResult(
                        $"Events related to Stripe payment intent {refundUnverified?.PaymentIntentId} is not to be handled in this environment");
                }

                SetStripeApiKeyByPlatformCountry(stripePaymentRecord.PlatformCountry);

                var endpointSecret = GetStripeWebhookSecretByPlatformCountry(stripePaymentRecord.PlatformCountry, "payment");

                var stripeEvent = EventUtility.ConstructEvent(json, req.Headers["Stripe-Signature"], endpointSecret);

                if (stripeEvent.Type == Events.ChargeRefundUpdated)
                {
                    var refund = stripeEvent.Data.Object as Stripe.Refund;

                    var chargeService = new Stripe.ChargeService();
                    var charge = await chargeService.GetAsync(refund?.ChargeId);

                    await HandleRefundUpdate(refund, charge);
                }
            }

            return new OkResult();
        }
        catch (Exception ex)
        {
            return new BadRequestObjectResult(ex);
        }
    }

    [FunctionName("StripeReportWebhook")]
    public async Task<IActionResult> RunReportWebhook([HttpTrigger(AuthorizationLevel.Anonymous, "get", "post", Route = "webhook/report")] HttpRequest req, ILogger log)
    {
        try
        {
            var eventRequestBody = await new StreamReader(req.Body).ReadToEndAsync();

            log.LogInformation($"webhook/report event: {eventRequestBody}");

            var stripeEventUnverified = JsonConvert.DeserializeObject<Stripe.Event>(eventRequestBody);

            var reportRunUnverified = stripeEventUnverified.Data.Object as ReportRun;

            if (reportRunUnverified == null || reportRunUnverified.ReportType != "activity.itemized.2")
            {
                log.LogError($"Invalid report run object: {reportRunUnverified?.Id}");

                return new OkResult();
            }

            var stripePaymentReportExportRecord =
                await _appDbContext.StripePaymentReportExportRecords.FirstOrDefaultAsync(r =>
                    r.StripeReportRunId == reportRunUnverified.Id);

            if (stripePaymentReportExportRecord is null)
            {
                return new OkObjectResult(
                    $"Events related to Stripe report run {reportRunUnverified.Id} is not to be handled in this environment");
            }

            SetStripeApiKeyByPlatformCountry(stripePaymentReportExportRecord.PlatformCountry);

            var endpointSecret = GetStripeWebhookSecretByPlatformCountry(stripePaymentReportExportRecord.PlatformCountry, "report");

            var stripeEvent = EventUtility.ConstructEvent(eventRequestBody, req.Headers["Stripe-Signature"], endpointSecret);

            if (stripeEvent.Type != Events.ReportingReportRunSucceeded
                && stripeEvent.Type != Events.ReportingReportRunFailed)
            {
                log.LogError($"Invalid Stripe report event type: {reportRunUnverified.Id} - {stripeEvent.Type}");

                return new OkResult();
            }

            if (stripeEvent.Type == Events.ReportingReportRunSucceeded)
            {
                var reportRun = stripeEvent.Data.Object as ReportRun;

                await HandleReportRunSucceeded(
                    stripePaymentReportExportRecord.Id,
                    stripePaymentReportExportRecord.CompanyId,
                    reportRun,
                    stripePaymentReportExportRecord.ReportReceiverEmailAddress,
                    stripePaymentReportExportRecord.ReportDataStartAt,
                    stripePaymentReportExportRecord.ReportDataEndAt,
                    stripePaymentReportExportRecord.PlatformCountry);
            }
            else
            {
                await HandleReportRunFailed(stripePaymentReportExportRecord.Id);
            }

            return new OkResult();
        }
        catch (Exception ex)
        {
            log.LogError($"webhook/report exception: {ex.Message}");

            return new BadRequestObjectResult(ex);
        }
    }

    private async Task HandleRefundUpdate(Stripe.Refund refund, Stripe.Charge charge)
    {
        var stripePaymentRecord = await _appDbContext.StripePaymentRecords.FirstOrDefaultAsync(x => x.StripePaymentIntentId == refund.PaymentIntentId);

        var chargeRefundedAmount = (decimal)(charge.AmountRefunded * 0.01);
        var refundAmount = (decimal)(refund.Amount * 0.01);

        if (refund.Status == "succeeded")
        {
            if (chargeRefundedAmount == stripePaymentRecord.PayAmount)
            {
                stripePaymentRecord.Status = StripePaymentStatus.Refunded;
            }
            else
            {
                stripePaymentRecord.Status = StripePaymentStatus.PartialRefund;
            }

            if (stripePaymentRecord.ShopifyOrderId.HasValue)
            {
                var shopifyRefund = new ShopifyRefund
                {
                    ShopifyOrderId = stripePaymentRecord.ShopifyOrderId,
                    ShopifyId = stripePaymentRecord.ShopifyId,
                    RefundAmount = refundAmount
                };
                if (Enum.TryParse(stripePaymentRecord.RefundReason, out RefundReason refundReason))
                {
                    shopifyRefund.RefundReason = refundReason;
                }
                else
                {
                    shopifyRefund.RefundReason = RefundReason.Custom;
                    shopifyRefund.CustomRefundReason = stripePaymentRecord.RefundReason;
                }

                await _stripePaymentService.ApplyRefundToShopifyOrder(shopifyRefund);
            }
        }

        if (refund.Status == "pending")
        {
            stripePaymentRecord.Status = StripePaymentStatus.RefundPending;

            if (stripePaymentRecord.ShopifyOrderId.HasValue &&
                stripePaymentRecord.RefundedAmount.HasValue &&
                chargeRefundedAmount < stripePaymentRecord.RefundedAmount)
            {
                // TBC
            }
        }

        if (refund.Status == "failed")
        {
            stripePaymentRecord.Status = StripePaymentStatus.RefundFailed;

            if (stripePaymentRecord.ShopifyOrderId.HasValue &&
                stripePaymentRecord.RefundedAmount.HasValue &&
                chargeRefundedAmount < stripePaymentRecord.RefundedAmount)
            {
                // TBC
            }
        }

        stripePaymentRecord.RefundedAmount = chargeRefundedAmount;

        await _appDbContext.SaveChangesAsync();

        await SendPaymentResultToSleekFlow(stripePaymentRecord);
    }

    private async Task HandleAccountUpdate(Account account) {
        // Collect more required information, e.g.
        var stripePaymentConfig = await _appDbContext.ConfigStripePaymentConfigs.FirstOrDefaultAsync(x => x.AccountId == account.Id);

        if (stripePaymentConfig == null)
            throw new Exception("stripe payment config error");

        if (account.ChargesEnabled)
            stripePaymentConfig.Status = StripePaymentRegistrationStatus.Registered;
        await _appDbContext.SaveChangesAsync();
    }

    private async Task HandleDeauthorization(string connectedAccountId) {
        // Clean up account state.
        var stripePaymentConfig = await _appDbContext.ConfigStripePaymentConfigs.FirstOrDefaultAsync(x => x.AccountId == connectedAccountId);

        if (stripePaymentConfig == null)
            throw new Exception("stripe payment config error");

        stripePaymentConfig.Status = StripePaymentRegistrationStatus.Deauthorized;
        await _appDbContext.SaveChangesAsync();
    }

    private async Task HandleSuccessfulPaymentIntent(PaymentIntent paymentIntent) {
        // Fulfill the purchase.
        var stripePaymentRecord = await _appDbContext.StripePaymentRecords.FirstOrDefaultAsync(x => x.StripePaymentIntentId == paymentIntent.Id);
        if (stripePaymentRecord == null)
            return;

        stripePaymentRecord.Currency = paymentIntent.Currency;
        stripePaymentRecord.Status = StripePaymentStatus.Paid;
        stripePaymentRecord.AmountDue = (decimal)paymentIntent.Amount / 100;
        stripePaymentRecord.AmountReceived = (decimal)paymentIntent.AmountReceived / 100;
        stripePaymentRecord.CustomerId = paymentIntent.CustomerId;
        stripePaymentRecord.PayAmount = (decimal)paymentIntent.Amount / 100;
        stripePaymentRecord.ReceiptUrl = paymentIntent.Charges.FirstOrDefault()?.ReceiptUrl;
        stripePaymentRecord.UpdatedAt = DateTime.UtcNow;
        stripePaymentRecord.PaymentIntentPayload = paymentIntent;
        stripePaymentRecord.PaidAt = DateTime.UtcNow;
        if (paymentIntent.ApplicationFeeAmount != null)
            stripePaymentRecord.ReceivedApplicationFeeAmount = (decimal) paymentIntent.ApplicationFeeAmount / 100;

        var shipping = paymentIntent.Shipping;
        if (shipping != null)
        {
            stripePaymentRecord.Shipping = new StripeShipping
            {
                Address = new StripeAddress
                {
                    City = shipping.Address?.City,
                    Country = shipping.Address?.Country,
                    Line1 = shipping.Address?.Line1,
                    Line2 = shipping.Address?.Line2,
                    PostalCode = shipping.Address?.PostalCode,
                    State = shipping.Address?.State,
                },
                Carrier = shipping.Carrier,
                Name = shipping.Name,
                Phone = shipping.Phone,
                TrackingNumber = shipping.TrackingNumber,
            };
        }

        var charges = paymentIntent.Charges?.ToList();
        if (charges != null && charges.Count > 0 && charges[0].BillingDetails != null)
        {
            var billing = charges[0].BillingDetails;

            stripePaymentRecord.Billing = new StripeBilling
            {
                Address = new StripeAddress
                {
                    City = billing.Address?.City,
                    Country = billing.Address?.Country,
                    Line1 = billing.Address?.Line1,
                    Line2 = billing.Address?.Line2,
                    PostalCode = billing.Address?.PostalCode,
                    State = billing.Address?.State,
                },
                Email = billing.Email,
                Name = billing.Name,
                Phone = billing.Phone,
            };
        }

        await _appDbContext.SaveChangesAsync();

        //Set draft order to paid
        await UpdateDraftOrderToPaid(stripePaymentRecord, paymentIntent);

        //Notify SleekFlow API by webhook
        await SendPaymentResultToSleekFlow(stripePaymentRecord);
    }

    private async Task HandleCanceledPaymentIntent(PaymentIntent paymentIntent) {
        // Fulfill the purchase.
        var stripePaymentRecord = await _appDbContext.StripePaymentRecords.FirstOrDefaultAsync(x => x.StripePaymentIntentId == paymentIntent.Id);
        if (stripePaymentRecord == null)
            return;

        stripePaymentRecord.Currency = paymentIntent.Currency;
        stripePaymentRecord.Status = StripePaymentStatus.Canceled;
        stripePaymentRecord.CustomerId = paymentIntent.CustomerId;
        stripePaymentRecord.UpdatedAt = DateTime.UtcNow;
        stripePaymentRecord.PaymentIntentPayload = paymentIntent;
        stripePaymentRecord.CanceledAt = paymentIntent.CanceledAt;
        stripePaymentRecord.CancellationReason = paymentIntent.CancellationReason;

        await _appDbContext.SaveChangesAsync();

        //Delete draft order
        await DeleteDraftOrder(stripePaymentRecord);

        //Notify SleekFlow API by webhook
        await SendPaymentResultToSleekFlow(stripePaymentRecord);
    }

    private async Task HandleReportRunSucceeded(
        long stripePaymentReportExportRecordId,
        string companyId,
        ReportRun reportRun,
        string reportReceiverEmailAddress,
        DateTime reportDataDateRangeStartAt,
        DateTime reportDataDateRangeEndAt,
        string platformCountry)
    {
        await _stripeReportService.ProcessStripeReportRunSucceededEvent(
            stripePaymentReportExportRecordId,
            companyId,
            reportRun.Result,
            reportReceiverEmailAddress,
            reportDataDateRangeStartAt,
            reportDataDateRangeEndAt,
            platformCountry);
    }

    private async Task HandleReportRunFailed(
        long stripePaymentReportExportRecordId)
    {
        await _stripeReportService.ProcessStripeReportRunFailedEvent(
            stripePaymentReportExportRecordId);
    }

    private async Task DeleteDraftOrder(StripePaymentRecord stripePaymentRecord)
    {
        if (!stripePaymentRecord.ShopifyDraftOrderId.HasValue)
            return;

        var shopifyConfig = await _appDbContext.ConfigShopifyConfigs.FirstOrDefaultAsync(x => x.CompanyId == stripePaymentRecord.CompanyId && x.Id == stripePaymentRecord.ShopifyId);

        if (shopifyConfig == null)
            return;

        //Delete
        //var checkoutService = new DraftOrderService(shopifyConfig.UsersMyShopifyUrl, shopifyConfig.AccessToken);
        //await checkoutService.DeleteAsync(stripePaymentRecord.ShopifyDraftOrderId.Value);

        var productVariant = new ProductVariantService(shopifyConfig.UsersMyShopifyUrl, shopifyConfig.AccessToken);

        //Release inventory
        foreach (var lineItem in stripePaymentRecord.LineItems)
        {
            var variantIdString = lineItem.Metadata?["variantId"];

            if (long.TryParse(variantIdString, out var variantId))
            {
                var variants = await productVariant.GetAsync(variantId);

                if (!variants.InventoryItemId.HasValue)
                    continue;

                var filter = new InventoryLevelListFilter()
                {
                    InventoryItemIds = new List<long>()
                    {
                        variants.InventoryItemId.Value
                    }
                };

                var inventoryService = new InventoryLevelService(shopifyConfig.UsersMyShopifyUrl, shopifyConfig.AccessToken);

                var levels = await inventoryService.ListAsync(filter);
                var level = levels.Items.First();

                level.Available += lineItem.Quantity;

                await inventoryService.SetAsync(level);
            }
        }
    }

    private async Task UpdateDraftOrderToPaid(StripePaymentRecord stripePaymentRecord, PaymentIntent paymentIntent)
    {
        if (!stripePaymentRecord.ShopifyDraftOrderId.HasValue)
            return;

        var shopifyConfig = await _appDbContext.ConfigShopifyConfigs.FirstOrDefaultAsync(x => x.CompanyId == stripePaymentRecord.CompanyId && x.Id == stripePaymentRecord.ShopifyId);

        if (shopifyConfig == null)
            return;

        try
        {
            try
            {
                var draftOrderService = new DraftOrderService(shopifyConfig.UsersMyShopifyUrl, shopifyConfig.AccessToken);
                var draftOrder = await draftOrderService.GetAsync(stripePaymentRecord.ShopifyDraftOrderId.Value);

                var billingAddress = paymentIntent?.Charges?.FirstOrDefault()?.BillingDetails?.Address;
                if (billingAddress != null)
                {
                    draftOrder.BillingAddress = new ShopifySharp.Address()
                    {
                        Zip = billingAddress.PostalCode,
                        Address1 = billingAddress.Line1,
                        Address2 = billingAddress.Line2,
                        CountryName = billingAddress.Country,
                        City = (!string.IsNullOrEmpty(billingAddress.City)) ? billingAddress.City : billingAddress.State,
                        Phone = paymentIntent.Charges.FirstOrDefault()?.BillingDetails.Phone,
                        Company = paymentIntent.Shipping?.Carrier,
                        Name = paymentIntent.Charges.FirstOrDefault()?.BillingDetails.Name,
                        Country = billingAddress.Country,
                        Province = billingAddress.State,
                    };

                    draftOrder = await draftOrderService.UpdateAsync((long)draftOrder.Id, draftOrder);
                }

                var shippingFeeAmount = stripePaymentRecord.PayAmount - stripePaymentRecord.LineItems.Sum(l => (l.Amount - l.TotalDiscount) * l.Quantity);

                var stripePaymentConfig = await _appDbContext.ConfigStripePaymentConfigs.FirstOrDefaultAsync(c =>
                    c.CompanyId == stripePaymentRecord.CompanyId &&
                    c.Country == stripePaymentRecord.PlatformCountry);

                var shopifyShippingRateTitle = "Shipping";
                if (stripePaymentConfig?.ShippingOptions?.Count > 0)
                {
                    var shippingOption =
                        stripePaymentConfig.ShippingOptions.FirstOrDefault(s => s.ShippingFee == shippingFeeAmount);

                    if (shippingOption?.DisplayName != null)
                    {
                        shopifyShippingRateTitle = shippingOption.DisplayName;
                    }
                }

                try
                {
                    var shopifyGraphQlAdminApiVersion =
                        Environment.GetEnvironmentVariable("ShopifyGraphQlAdminApiVersion");

                    await _shopifyService.DraftOrderUpdate(
                        stripePaymentRecord.CompanyId,
                        stripePaymentRecord.ShopifyDraftOrderId.Value,
                        shippingFeeAmount,
                        shopifyShippingRateTitle,
                        stripePaymentRecord.Currency,
                        shopifyConfig.Currency,
                        shopifyConfig.UsersMyShopifyUrl,
                        shopifyConfig.AccessToken,
                        shopifyGraphQlAdminApiVersion);
                }
                catch (Exception)
                {
                    draftOrder.Note += $"\nTotal Amount: ${stripePaymentRecord.PayAmount}"
                                       + $"\nShipping Fee Amount: ${shippingFeeAmount}";

                    await draftOrderService.UpdateAsync(draftOrder.Id.Value, draftOrder);
                }

                var draftOrderCompleted = await draftOrderService.CompleteAsync(stripePaymentRecord.ShopifyDraftOrderId.Value);
                stripePaymentRecord.ShopifyOrderId = draftOrderCompleted.OrderId;
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex);
            }

            if (stripePaymentRecord.ShopifyOrderId != null)
            {
                var orderService =
                    new ShopifySharp.OrderService(shopifyConfig.UsersMyShopifyUrl, shopifyConfig.AccessToken);

                var order = await orderService.GetAsync(stripePaymentRecord.ShopifyOrderId.Value);

                if (paymentIntent?.Shipping?.Address != null)
                {
                    order.ShippingAddress = new ShopifySharp.Address()
                    {
                        Zip = paymentIntent.Shipping?.Address.PostalCode,
                        Address1 = paymentIntent.Shipping?.Address?.Line1,
                        Address2 = paymentIntent.Shipping?.Address?.Line2,
                        CountryName = paymentIntent.Shipping?.Address?.Country,
                        City = (!string.IsNullOrEmpty(paymentIntent.Shipping?.Address?.City)) ? paymentIntent.Shipping?.Address?.City : paymentIntent.Shipping?.Address?.State,
                        Phone = (!string.IsNullOrEmpty(paymentIntent.Shipping?.Phone)) ? paymentIntent.Shipping?.Phone : order.Phone,
                        Company = paymentIntent.Shipping?.Carrier,
                        Name = paymentIntent.Shipping?.Name,
                        Country = paymentIntent.Shipping?.Address?.Country,
                        Province = paymentIntent.Shipping?.Address?.State,
                    };

                    try
                    {
                        await orderService.UpdateAsync(order.Id.Value, order);
                    }
                    catch (Exception)
                    {
                        //Add to exception
                        order = await orderService.GetAsync(stripePaymentRecord.ShopifyOrderId.Value);
                        stripePaymentRecord.ShopifyOrderStatusUrl = order.OrderStatusUrl;

                        if (paymentIntent?.Shipping?.Address != null)
                        {
                            order.Note += $"\nAddress: {paymentIntent?.Shipping?.Address.Line1} {paymentIntent?.Shipping?.Address.Line2} {paymentIntent?.Shipping?.Address.City} {paymentIntent?.Shipping?.Address.Country}";
                            await orderService.UpdateAsync(order.Id.Value, order);
                        }
                    }
                }

                stripePaymentRecord.ShopifyOrderStatusUrl = order.OrderStatusUrl;
            }

            await _appDbContext.SaveChangesAsync();
        }
        catch (Exception ex)
        {
            Console.WriteLine(ex);
        }
    }

    private async Task SendPaymentResultToSleekFlow(StripePaymentRecord stripePaymentRecord)
    {
        var sleekFlowApiServer = Environment.GetEnvironmentVariable("SleekFlowAPIDomain");
        var httpClient = new HttpClient();
        await Policy
            .Handle<Exception>()
            .WaitAndRetryAsync(5, sleepDurationProvider: i => TimeSpan.FromSeconds(5), onRetry: (exception, retryCount) => { Console.WriteLine($"Retry: {retryCount}, Message:{exception.Message}", exception); })
            .ExecuteAndCaptureAsync(async () =>
            {
                var apiResponse = await httpClient.PostAsJsonAsync($"{sleekFlowApiServer}/sleekpay/webhook/payment/result" , stripePaymentRecord);

                if (!apiResponse.IsSuccessStatusCode)
                    throw new Exception($"Cannot post result to SleekFlow: {await apiResponse.Content.ReadAsStringAsync()}");
            });
    }

    private static string GetStripeWebhookSecretByPlatformCountry(string platformCountry, string webhookType)
    {
        switch (platformCountry.ToLower())
        {
            case "hk":
                switch (webhookType)
                {
                    case "payment":
                        return Environment.GetEnvironmentVariable("StripeWebhookSecret_HK");
                    case "connect":
                        return Environment.GetEnvironmentVariable("StripeConnectWebhookSecret_HK");
                    case "report":
                        return Environment.GetEnvironmentVariable("StripeReportWebhookSecret_HK");
                    default:
                        return null;
                }
            case "sg":
                switch (webhookType)
                {
                    case "payment":
                        return Environment.GetEnvironmentVariable("StripeWebhookSecret_SG");
                    case "connect":
                        return Environment.GetEnvironmentVariable("StripeConnectWebhookSecret_SG");
                    case "report":
                        return Environment.GetEnvironmentVariable("StripeReportWebhookSecret_SG");
                    default:
                        return null;
                }
            case "my":
                switch (webhookType)
                {
                    case "payment":
                        return Environment.GetEnvironmentVariable("StripeWebhookSecret_MY");
                    case "connect":
                        return Environment.GetEnvironmentVariable("StripeConnectWebhookSecret_MY");
                    case "report":
                        return Environment.GetEnvironmentVariable("StripeReportWebhookSecret_MY");
                    default:
                        return null;
                }
            case "gb":
                switch (webhookType)
                {
                    case "payment":
                        return Environment.GetEnvironmentVariable("StripeWebhookSecret_GB");
                    case "connect":
                        return Environment.GetEnvironmentVariable("StripeConnectWebhookSecret_GB");
                    case "report":
                        return Environment.GetEnvironmentVariable("StripeReportWebhookSecret_GB");
                    default:
                        return null;
                }
            default:
                return null;
        }
    }

    private static void SetStripeApiKeyByPlatformCountry(string platformCountry)
    {
        switch (platformCountry.ToLower())
        {
            case "hk":
                StripeConfiguration.ApiKey = Environment.GetEnvironmentVariable("Stripe_Secret_Key_HK");
                break;
            case "sg":
                StripeConfiguration.ApiKey = Environment.GetEnvironmentVariable("Stripe_Secret_Key_SG");
                break;
            case "my":
                StripeConfiguration.ApiKey = Environment.GetEnvironmentVariable("Stripe_Secret_Key_MY");
                break;
            case "gb":
                StripeConfiguration.ApiKey = Environment.GetEnvironmentVariable("Stripe_Secret_Key_GB");
                break;
        }
    }
}