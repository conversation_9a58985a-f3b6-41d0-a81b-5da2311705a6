using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AutoMapper;
using AutoMapper.QueryableExtensions;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Travis_backend.Cache;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.Constants;
using Travis_backend.ConversationDomain.ViewModels;
using Travis_backend.Database;
using Travis_backend.Enums;
using Travis_backend.MessageDomain.Models;
using Travis_backend.MessageDomain.ViewModels;
using Travis_backend.StripeIntegrationDomain.Services;

namespace Travis_backend.ConversationDomain.Services;

public interface IConversationValidator
{
    ValueTask<(bool IsValid, string ErrorMessage)> ValidateConversationStaffHaveSendMessagePermission(
        string conversationId,
        Staff staff);
}

public class ConversationValidator : IConversationValidator
{
    private readonly ApplicationDbContext _appDbContext;
    private readonly ILogger<ConversationValidator> _logger;
    private readonly IConversationService _conversationService;

    public ConversationValidator(
        ApplicationDbContext appDbContext,
        ILogger<ConversationValidator> logger,
        IConversationService conversationService)
    {
        _appDbContext = appDbContext;
        _logger = logger;
        _conversationService = conversationService;
    }


    public async ValueTask<(bool IsValid, string ErrorMessage)> ValidateConversationStaffHaveSendMessagePermission(string conversationId, Staff staff)
    {
        string errorMessage = null;

        // OTO Bodycare (HK) Ltd. only
        if (staff.CompanyId is "60cf7ef2-b249-47af-af18-476962ae5c89"
            && staff.RoleType == StaffUserRole.Staff
            && await _appDbContext.Conversations.AnyAsync(
                x =>
                    x.Id == conversationId
                    && x.AssigneeId != null
                    && x.AssigneeId != staff.Id))
        {
            errorMessage = "This conversation is already owned by others";
        }

        // MediLase only
        if (staff.CompanyId == "07959d97-3a5a-4155-a240-e9f47dba13fa"
            && !await _conversationService.IsStaffAllowedToSendMessage(
                staff,
                conversationId))
        {
            errorMessage = string.Format(
                "The Staff with Id:{0} and role:{1} is not allowed to send messages in the conversation with Id:{2}",
                staff.Id,
                staff.RoleType.ToString(),
                conversationId);

            _logger.LogError(
                "[{MethodName}] The staff with Id:{StaffId} and role:{StaffRole} is not allowed to send messages in the conversation with Id:{ConversationId}",
                nameof(ValidateConversationStaffHaveSendMessagePermission),
                staff.Id,
                staff.RoleType.ToString(),
                conversationId);
        }

        return (errorMessage == null, errorMessage);
    }
}