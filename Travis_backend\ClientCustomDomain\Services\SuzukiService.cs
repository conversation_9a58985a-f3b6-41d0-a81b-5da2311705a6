﻿#nullable enable
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Travis_backend.ClientCustomDomain.Constant;
using Travis_backend.ClientCustomDomain.ViewModels;
using Travis_backend.ContactDomain.Models;
using Travis_backend.ContactDomain.Services;
using Travis_backend.ConversationDomain.Services;
using Travis_backend.ConversationServices;
using Travis_backend.Database;

namespace Travis_backend.ClientCustomDomain.Services;

public interface ISuzukiService
{
    Task AssignCollaboratorByCustomField(
        string userProfileId,
        UserProfile? userProfile,
        string customFieldName,
        string? fallbackCustomFieldName = null);

    Task AssignContactOwnerByCustomField(
        string userProfileId,
        UserProfile? userProfile,
        string customFieldName,
        string? fallbackCustomFieldName = null);

    Task AssignServiceAdvisorIdsToCustomFieldsInSuzuki(
        UserProfile userProfile);

    Task AssignSalesmanIdsToCustomFieldsInSuzuki(
        UserProfile userProfile);

    Task CreateLeadInSuzuki(
        string userProfileId,
        UserProfile? userProfile,
        string? salesmanIdFieldName,
        string? salesmanFallbackIdFieldName = null);
}

public class SuzukiService : ISuzukiService
{
    private readonly ApplicationDbContext _appDbContext;
    private readonly ILogger _logger;
    private readonly IUserProfileService _userProfileService;
    private readonly IConversationMessageService _conversationMessageService;
    private readonly IConversationAssigneeService _conversationAssigneeService;
    private readonly ISuzukiUtilityService _suzukiUtilityService;

    public SuzukiService(
        ApplicationDbContext appDbContext,
        ILogger<SuzukiService> logger,
        IUserProfileService userProfileService,
        IConversationMessageService conversationMessageService,
        IConversationAssigneeService conversationAssigneeService,
        ISuzukiUtilityService suzukiUtilityService)
    {
        _appDbContext = appDbContext;
        _logger = logger;
        _userProfileService = userProfileService;
        _conversationMessageService = conversationMessageService;
        _conversationAssigneeService = conversationAssigneeService;
        _suzukiUtilityService = suzukiUtilityService;
    }

    public async Task AssignCollaboratorByCustomField(
        string userProfileId,
        UserProfile? userProfile,
        string customFieldName,
        string? fallbackCustomFieldName = null)
    {
        if (userProfile == null)
        {
            userProfile = await _appDbContext.UserProfiles
                .FirstOrDefaultAsync(x => x.Id == userProfileId);
        }

        var conversation = await _userProfileService.GetConversationByUserProfileId(
            userProfile.CompanyId,
            userProfile.Id,
            "closed");
        if (conversation == null)
        {
            return;
        }

        var customFieldValue = await _suzukiUtilityService.GetCustomFieldValue(
            userProfile.CompanyId,
            userProfile.Id,
            customFieldName);

        var value = string.IsNullOrWhiteSpace(customFieldValue) && fallbackCustomFieldName != null
            ? await _suzukiUtilityService.GetCustomFieldValue(
                userProfile.CompanyId,
                userProfile.Id,
                fallbackCustomFieldName)
            : customFieldValue;
        if (string.IsNullOrWhiteSpace(value))
        {
            return;
        }

        // compare email with suzuki's staffs
        var staffs = await _appDbContext.UserRoleStaffs
            .Where(
                x =>
                    x.CompanyId == userProfile.CompanyId
                    && x.Id != 1)
            .Include(x => x.Identity)
            .OrderBy(x => x.Order)
            .ThenBy(x => x.Id)
            .Where(x => x.Identity.Email == value)
            .ToListAsync();
        if (!staffs.Any())
        {
            return;
        }

        var targetStaff = staffs.First();
        var targetStaffId = targetStaff.Id;

        await _conversationAssigneeService.AddAdditionalAssignees(
            conversation,
            new List<long>()
            {
                targetStaffId
            },
            null,
            true);
    }

    public async Task AssignContactOwnerByCustomField(
        string userProfileId,
        UserProfile? userProfile,
        string customFieldName,
        string? fallbackCustomFieldName = null)
    {
        if (userProfile == null)
        {
            userProfile = await _appDbContext.UserProfiles
                .FirstOrDefaultAsync(x => x.Id == userProfileId);
        }

        var conversation =
            await _userProfileService.GetConversationByUserProfileId(
                userProfile.CompanyId,
                userProfile.Id,
                "closed");
        if (conversation == null)
        {
            return;
        }

        var customFieldValue = await _suzukiUtilityService.GetCustomFieldValue(
            userProfile.CompanyId,
            userProfile.Id,
            customFieldName);

        var value = string.IsNullOrWhiteSpace(customFieldValue) && fallbackCustomFieldName != null
            ? await _suzukiUtilityService.GetCustomFieldValue(
                userProfile.CompanyId,
                userProfile.Id,
                fallbackCustomFieldName)
            : customFieldValue;
        if (string.IsNullOrWhiteSpace(value))
        {
            return;
        }

        // compare email with suzuki's staffs
        var staffs = await _appDbContext.UserRoleStaffs
            .Where(x => x.CompanyId == userProfile.CompanyId && x.Id != 1)
            .Include(x => x.Identity)
            .OrderBy(x => x.Order)
            .ThenBy(x => x.Id)
            .Where(x => x.Identity.Email == value)
            .ToListAsync();
        if (!staffs.Any())
        {
            return;
        }

        var targetStaff = staffs.First();
        var targetStaffId = targetStaff.Id;

        await _conversationMessageService.ChangeConversationAssignee(
            conversation,
            targetStaff);

        var assignedTeam = await _appDbContext.CompanyStaffTeams
            .Where(x => x.Members.Any(y => y.StaffId == targetStaffId))
            .FirstOrDefaultAsync();
        if (assignedTeam != null)
        {
            await _conversationMessageService.ChangeConversationAssignedTeam(conversation, assignedTeam);
        }
    }

    public async Task AssignServiceAdvisorIdsToCustomFieldsInSuzuki(
        UserProfile userProfile)
    {
        if (userProfile.PhoneNumber == null)
        {
            _logger.LogError("prod - get suzuki serviceadvisor assignee fails: phone number not provided");
            throw new ArgumentNullException(nameof(userProfile.PhoneNumber));
        }

        var brand =
            await _suzukiUtilityService.GetCustomFieldValue(userProfile.CompanyId, userProfile.Id, "Brand");
        var enquiryType =
            await _suzukiUtilityService.GetCustomFieldValue(userProfile.CompanyId, userProfile.Id, "Enquiry Type");
        var regNo =
            await _suzukiUtilityService.GetCustomFieldValue(userProfile.CompanyId, userProfile.Id, "regNo");

        var getServiceAdvisorAssignmentRequest = new SuzukiGetServiceAdvisorAssignmentRequest
        {
            Phone = userProfile.PhoneNumber,
            Email = userProfile.Email,
            Brand = brand,
            EnquiryType = enquiryType,
            Regno = regNo
        };

        var getAssigneeUrl =
            @"https://sdmfghk.my.salesforce.com/services/apexrest/SleekFlow/assignment/serviceadvisor";
        var suzukiAccessToken = await _suzukiUtilityService.GetProductionSuzukiAccessToken();

        var getServiceAdvisorAssignmentResponseStr = await _suzukiUtilityService.PostJsonAsync(
            getAssigneeUrl,
            getServiceAdvisorAssignmentRequest,
            new Dictionary<string, string>()
            {
                {
                    "Authorization", "Bearer " + suzukiAccessToken
                },
                {
                    "Accept", "application/json"
                }
            });
        var suzukiGetServiceAdvisorAssignmentResponse =
            JsonConvert.DeserializeObject<SuzukiGetServiceAdvisorAssignmentResponse>(
                getServiceAdvisorAssignmentResponseStr)!;

        if (!string.IsNullOrEmpty(suzukiGetServiceAdvisorAssignmentResponse.CsaId))
        {
            await _suzukiUtilityService.SetCustomFieldValue(
                userProfile.CompanyId,
                userProfile.Id,
                $"{SuzukiRoleTypes.Csa}Id",
                suzukiGetServiceAdvisorAssignmentResponse.CsaId);
        }

        if (!string.IsNullOrEmpty(suzukiGetServiceAdvisorAssignmentResponse.Csa))
        {
            await _suzukiUtilityService.SetCustomFieldValue(
                userProfile.CompanyId,
                userProfile.Id,
                $"{SuzukiRoleTypes.Csa}",
                suzukiGetServiceAdvisorAssignmentResponse.Csa);
        }

        if (!string.IsNullOrEmpty(suzukiGetServiceAdvisorAssignmentResponse.CsaOnDutyId))
        {
            await _suzukiUtilityService.SetCustomFieldValue(
                userProfile.CompanyId,
                userProfile.Id,
                $"{SuzukiRoleTypes.CsaOnDuty}Id",
                suzukiGetServiceAdvisorAssignmentResponse.CsaOnDutyId);
        }

        if (!string.IsNullOrEmpty(suzukiGetServiceAdvisorAssignmentResponse.CsaOnDuty))
        {
            await _suzukiUtilityService.SetCustomFieldValue(
                userProfile.CompanyId,
                userProfile.Id,
                $"{SuzukiRoleTypes.CsaOnDuty}",
                suzukiGetServiceAdvisorAssignmentResponse.CsaOnDuty);
        }

        if (!string.IsNullOrEmpty(suzukiGetServiceAdvisorAssignmentResponse.CsaEmail))
        {
            await _suzukiUtilityService.SetCustomFieldValue(
                userProfile.CompanyId,
                userProfile.Id,
                $"{SuzukiRoleTypes.Csa}Email",
                suzukiGetServiceAdvisorAssignmentResponse.CsaEmail);
        }

        if (!string.IsNullOrEmpty(suzukiGetServiceAdvisorAssignmentResponse.CsaOnDutyEmail))
        {
            await _suzukiUtilityService.SetCustomFieldValue(
                userProfile.CompanyId,
                userProfile.Id,
                $"{SuzukiRoleTypes.CsaOnDuty}Email",
                suzukiGetServiceAdvisorAssignmentResponse.CsaOnDutyEmail);
        }
    }

    public async Task AssignSalesmanIdsToCustomFieldsInSuzuki(
        UserProfile userProfile)
    {
        if (userProfile.PhoneNumber == null)
        {
            _logger.LogError("prod - get suzuki salesman assginee fails: phone number not provided");
            throw new ArgumentNullException(nameof(userProfile.PhoneNumber));
        }

        var existingOpportunityId =
            await _suzukiUtilityService.GetCustomFieldValue(
                userProfile.CompanyId,
                userProfile.Id,
                "existingOpportunityId");

        var brand = await _suzukiUtilityService.GetCustomFieldValue(userProfile.CompanyId, userProfile.Id, "Brand");

        var enquiryType =
            await _suzukiUtilityService.GetCustomFieldValue(userProfile.CompanyId, userProfile.Id, "Enquiry Type");

        var getSalesmanAssignmentRequest = new SuzukiGetSalesmanAssignmentRequest
        {
            Phone = userProfile.PhoneNumber,
            Email = userProfile.Email,
            Brand = brand,
            ExistingOpportunityId = existingOpportunityId,
            EnquiryType = enquiryType
        };

        var getAssigneeUrl = @"https://sdmfghk.my.salesforce.com/services/apexrest/SleekFlow/assignment/salesman";
        var suzukiAccessToken = await _suzukiUtilityService.GetProductionSuzukiAccessToken();

        var getSalesmanAssignmentResponseStr = await _suzukiUtilityService.PostJsonAsync(
            getAssigneeUrl,
            getSalesmanAssignmentRequest,
            new Dictionary<string, string>()
            {
                {
                    "Authorization", "Bearer " + suzukiAccessToken
                },
                {
                    "Accept", "application/json"
                }
            });
        var suzukiGetSalesmanAssignmentResponse =
            JsonConvert.DeserializeObject<SuzukiGetSalesmanAssignmentResponse>(getSalesmanAssignmentResponseStr)!;

        if (!string.IsNullOrEmpty(suzukiGetSalesmanAssignmentResponse.SalesmanId))
        {
            await _suzukiUtilityService.SetCustomFieldValue(
                userProfile.CompanyId,
                userProfile.Id,
                $"{SuzukiRoleTypes.Salesman}Id",
                suzukiGetSalesmanAssignmentResponse.SalesmanId);
        }

        if (!string.IsNullOrEmpty(suzukiGetSalesmanAssignmentResponse.Salesman))
        {
            await _suzukiUtilityService.SetCustomFieldValue(
                userProfile.CompanyId,
                userProfile.Id,
                SuzukiRoleTypes.Salesman,
                suzukiGetSalesmanAssignmentResponse.Salesman);
        }

        if (!string.IsNullOrEmpty(suzukiGetSalesmanAssignmentResponse.SalesmanEmail))
        {
            await _suzukiUtilityService.SetCustomFieldValue(
                userProfile.CompanyId,
                userProfile.Id,
                $"{SuzukiRoleTypes.Salesman}Email",
                suzukiGetSalesmanAssignmentResponse.SalesmanEmail);
        }

        if (!string.IsNullOrEmpty(suzukiGetSalesmanAssignmentResponse.SalesmanOnDutyId))
        {
            await _suzukiUtilityService.SetCustomFieldValue(
                userProfile.CompanyId,
                userProfile.Id,
                $"{SuzukiRoleTypes.SalesmanOnDuty}Id",
                suzukiGetSalesmanAssignmentResponse.SalesmanOnDutyId);
        }

        if (!string.IsNullOrEmpty(suzukiGetSalesmanAssignmentResponse.SalesmanOnDuty))
        {
            await _suzukiUtilityService.SetCustomFieldValue(
                userProfile.CompanyId,
                userProfile.Id,
                SuzukiRoleTypes.SalesmanOnDuty,
                suzukiGetSalesmanAssignmentResponse.SalesmanOnDuty);
        }

        if (!string.IsNullOrEmpty(suzukiGetSalesmanAssignmentResponse.SalesmanOnDutyEmail))
        {
            await _suzukiUtilityService.SetCustomFieldValue(
                userProfile.CompanyId,
                userProfile.Id,
                $"{SuzukiRoleTypes.SalesmanOnDuty}Email",
                suzukiGetSalesmanAssignmentResponse.SalesmanOnDutyEmail);
        }

        if (!string.IsNullOrEmpty(suzukiGetSalesmanAssignmentResponse.SupervisorId))
        {
            await _suzukiUtilityService.SetCustomFieldValue(
                userProfile.CompanyId,
                userProfile.Id,
                $"{SuzukiRoleTypes.Supervisor}Id",
                suzukiGetSalesmanAssignmentResponse.SupervisorId);
        }

        if (!string.IsNullOrEmpty(suzukiGetSalesmanAssignmentResponse.Supervisor))
        {
            await _suzukiUtilityService.SetCustomFieldValue(
                userProfile.CompanyId,
                userProfile.Id,
                SuzukiRoleTypes.Supervisor,
                suzukiGetSalesmanAssignmentResponse.Supervisor);
        }

        if (!string.IsNullOrEmpty(suzukiGetSalesmanAssignmentResponse.SupervisorEmail))
        {
            await _suzukiUtilityService.SetCustomFieldValue(
                userProfile.CompanyId,
                userProfile.Id,
                $"{SuzukiRoleTypes.Supervisor}Email",
                suzukiGetSalesmanAssignmentResponse.SupervisorEmail);
        }
    }

    // "ContactOwner" is a special value for salesmanIdFieldName
    public async Task CreateLeadInSuzuki(
        string userProfileId,
        UserProfile? userProfile,
        string? salesmanIdFieldName,
        string? salesmanFallbackIdFieldName = null)
    {
        userProfile ??= await _appDbContext.UserProfiles
            .FirstOrDefaultAsync(x => x.Id == userProfileId);

        var conversation = await _appDbContext.Conversations
            .FirstOrDefaultAsync(
                x =>
                    x.CompanyId == userProfile.CompanyId
                    && x.UserProfileId == userProfile.Id);

        var brand =
            await _suzukiUtilityService.GetCustomFieldValue(userProfile.CompanyId, userProfile.Id, "Brand");
        var enquiryType =
            await _suzukiUtilityService.GetCustomFieldValue(userProfile.CompanyId, userProfile.Id, "Enquiry Type");
        var modelInterest =
            await _suzukiUtilityService.GetCustomFieldValue(userProfile.CompanyId, userProfile.Id, "Model Interest");
        var language =
            await _suzukiUtilityService.GetCustomFieldValue(userProfile.CompanyId, userProfile.Id, "Language");
        var subscriber =
            await _suzukiUtilityService.GetCustomFieldValue(userProfile.CompanyId, userProfile.Id, "Subscriber");
        var preferredServiceDay =
            await _suzukiUtilityService.GetCustomFieldValue(
                userProfile.CompanyId,
                userProfile.Id,
                "preferredServiceDate");
        var typeOfWork =
            await _suzukiUtilityService.GetCustomFieldValue(userProfile.CompanyId, userProfile.Id, "typeOfWork");
        var opportunityPreference =
            await _suzukiUtilityService.GetCustomFieldValue(
                userProfile.CompanyId,
                userProfile.Id,
                "opportunityPreference");
        var registrationNo =
            await _suzukiUtilityService.GetCustomFieldValue(userProfile.CompanyId, userProfile.Id, "registrationNo");
        var existingLeadId =
            await _suzukiUtilityService.GetCustomFieldValue(userProfile.CompanyId, userProfile.Id, "existingLeadId");
        var existingOpportunityId =
            await _suzukiUtilityService.GetCustomFieldValue(
                userProfile.CompanyId,
                userProfile.Id,
                "existingOpportunityId");
        var existingCampaignId =
            await _suzukiUtilityService.GetCustomFieldValue(
                userProfile.CompanyId,
                userProfile.Id,
                "existingCampaignId");

        var sleekflowContactOwnerEmail =
            await _suzukiUtilityService.GetContactOwnerEmail(userProfile.CompanyId, userProfile.Id);

        string? salesmanId;
        string? salesman;
        string? salesmanEmail;
        if (salesmanIdFieldName == "ContactOwner")
        {
            salesmanId = null;
            salesman = null;
            salesmanEmail = sleekflowContactOwnerEmail;
        }
        else
        {
            salesmanId = salesmanIdFieldName == null
                ? null
                : await _suzukiUtilityService.GetCustomFieldValue(
                      userProfile.CompanyId,
                      userProfile.Id,
                      salesmanIdFieldName)
                  ?? (
                      salesmanFallbackIdFieldName == null
                          ? null
                          : await _suzukiUtilityService.GetCustomFieldValue(
                              userProfile.CompanyId,
                              userProfile.Id,
                              salesmanFallbackIdFieldName));
            salesman = salesmanIdFieldName == null
                ? null
                : await _suzukiUtilityService.GetCustomFieldValue(
                      userProfile.CompanyId,
                      userProfile.Id,
                      salesmanIdFieldName.Replace("Id", string.Empty))
                  ?? (
                      salesmanFallbackIdFieldName == null
                          ? null
                          : await _suzukiUtilityService.GetCustomFieldValue(
                              userProfile.CompanyId,
                              userProfile.Id,
                              salesmanFallbackIdFieldName.Replace("Id", string.Empty)));
            salesmanEmail = string.IsNullOrEmpty(salesmanIdFieldName)
                ? null
                : await _suzukiUtilityService.GetCustomFieldValue(
                      userProfile.CompanyId,
                      userProfile.Id,
                      salesmanIdFieldName.Replace("Id", "Email"))
                  ?? (
                      salesmanFallbackIdFieldName == null
                          ? null
                          : await _suzukiUtilityService.GetCustomFieldValue(
                              userProfile.CompanyId,
                              userProfile.Id,
                              salesmanFallbackIdFieldName!.Replace("Id", "Email")));
        }

        var createSuzukiLeadRequestUrl =
            @"https://sdmfghk.my.salesforce.com/services/apexrest/SleekFlow/lead";

        var createSuzukiLeadRequest = new SuzukiCreateLeadRequest(
            userProfile.PhoneNumber,
            userProfile.Email,
            brand,
            userProfile.FirstName,
            userProfileId,
            conversation.Id,
            existingCampaignId,
            enquiryType,
            userProfile.LastName,
            modelInterest,
            language,
            string.Equals(subscriber, "true", StringComparison.InvariantCultureIgnoreCase),
            string.IsNullOrWhiteSpace(salesmanId) ? null : salesmanId,
            string.IsNullOrWhiteSpace(salesman) ? null : salesman,
            string.IsNullOrWhiteSpace(salesmanEmail) ? null : salesmanEmail,
            sleekflowContactOwnerEmail,
            string.IsNullOrWhiteSpace(existingLeadId) ? null : existingLeadId,
            string.IsNullOrWhiteSpace(existingOpportunityId) ? null : existingOpportunityId,
            registrationNo,
            preferredServiceDay,
            typeOfWork,
            opportunityPreference);

        var suzukiAccessToken = await _suzukiUtilityService.GetProductionSuzukiAccessToken();

        var createSuzukiLeadResponseStr =
            await _suzukiUtilityService.PostJsonAsync(
                createSuzukiLeadRequestUrl,
                createSuzukiLeadRequest,
                new Dictionary<string, string>()
                {
                    {
                        "Authorization", "Bearer " + suzukiAccessToken
                    },
                    {
                        "Accept", "application/json"
                    }
                });

        var suzukiCreateLeadResponse =
            JsonConvert.DeserializeObject<SuzukiCreateLeadResponse>(createSuzukiLeadResponseStr);

        if (suzukiCreateLeadResponse.Result != "Success")
        {
            _logger.LogError("prod - create suzuki lead/opportunity fails");
        }

        // store leadId and oppId in custom fields
        if (!string.IsNullOrEmpty(suzukiCreateLeadResponse.LeadId))
        {
            await _suzukiUtilityService.SetCustomFieldValue(
                userProfile.CompanyId,
                userProfile.Id,
                "existingLeadId",
                suzukiCreateLeadResponse.LeadId);
        }

        if (!string.IsNullOrEmpty(suzukiCreateLeadResponse.OpportunityId))
        {
            await _suzukiUtilityService.SetCustomFieldValue(
                userProfile.CompanyId,
                userProfile.Id,
                "existingOpportunityId",
                suzukiCreateLeadResponse.OpportunityId);
        }
    }
}