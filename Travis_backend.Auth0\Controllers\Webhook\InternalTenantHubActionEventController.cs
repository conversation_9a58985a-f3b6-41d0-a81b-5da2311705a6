using System.ComponentModel.DataAnnotations;
using System.IdentityModel.Tokens.Jwt;
using System.Text;
using AutoMapper;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.IdentityModel.Tokens;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using Sleekflow.Apis.FlowHub.Api;
using Sleekflow.Apis.FlowHub.Model;
using Sleekflow.Apis.TenantHub.Api;
using Sleekflow.Apis.TenantHub.Model;
using Travis_backend.AccountAuthenticationDomain.Models;
using Travis_backend.Auth0.Models;
using Travis_backend.Auth0.Services;
using Travis_backend.Cache;
using Travis_backend.Cache.Models.CacheKeyPatterns;
using Travis_backend.CommonDomain.ViewModels;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.CompanyDomain.Services;
using Travis_backend.CompanyDomain.ViewModels;
using Travis_backend.Constants;
using Travis_backend.ConversationDomain.ViewModels;
using Travis_backend.ConversationServices;
using Travis_backend.Database;
using Travis_backend.Enums;
using Travis_backend.FlowHubs;
using Travis_backend.IntelligentHubDomain.Services;
using Travis_backend.InternalDomain.Services;
using Travis_backend.Models;
using Travis_backend.ShareInvitationDomain.Models;
using Travis_backend.ShareInvitationDomain.ViewModels;
using Travis_backend.SignalR;
using Auth0User = Auth0.ManagementApi.Models.User;
using Hangfire;

namespace Travis_backend.Auth0.Controllers.Webhook;

[Route("auth0/tenant-hub")]
public class InternalTenantHubActionEventController : Controller
{
    private readonly ICoreService _coreService;
    private readonly IConfiguration _configuration;
    private readonly ICompanyTeamService _companyTeamService;
    private readonly SleekflowUserManager _sleekflowUserManager;
    private readonly ILogger<InternalTenantHubActionEventController> _logger;
    private readonly ApplicationDbContext _appDbContext;
    private readonly ICacheManagerService _cacheManagerService;
    private readonly IManagementIpWhitelistsApi _ipWhitelistsApi;
    private readonly IManagementFeaturesApi _managementFeaturesApi;
    private readonly IManagementEnabledFeaturesApi _managementEnabledFeaturesApi;
    private readonly ISignalRService _signalRService;
    private readonly IMapper _mapper;
    private readonly IAuth0CompanyService _auth0CompanyService;
    private readonly ICompanyUsageService _companyUsageService;
    private readonly IFlowHubConfigsApi _flowHubConfigsApi;
    private readonly IStaffHooks _staffHooks;

    public InternalTenantHubActionEventController(
        ICoreService coreService,
        IConfiguration configuration,
        ICompanyTeamService companyTeamService,
        UserManager<ApplicationUser> userManager,
        ILogger<InternalTenantHubActionEventController> logger,
        ApplicationDbContext appDbContext,
        ICacheManagerService cacheManagerService,
        IManagementIpWhitelistsApi ipWhitelistsApi,
        IManagementFeaturesApi managementFeaturesApi,
        IManagementEnabledFeaturesApi managementEnabledFeaturesApi,
        ISignalRService signalRService,
        IMapper mapper,
        IAuth0CompanyService auth0CompanyService,
        ICompanyUsageService companyUsageService,
        IFlowHubConfigsApi flowHubConfigsApi,
        IStaffHooks staffHooks)
    {
        _logger = logger;
        _appDbContext = appDbContext;
        _cacheManagerService = cacheManagerService;
        _ipWhitelistsApi = ipWhitelistsApi;
        _managementFeaturesApi = managementFeaturesApi;
        _managementEnabledFeaturesApi = managementEnabledFeaturesApi;
        _signalRService = signalRService;
        _mapper = mapper;
        _auth0CompanyService = auth0CompanyService;
        _companyUsageService = companyUsageService;
        _coreService = coreService;
        _configuration = configuration;
        _companyTeamService = companyTeamService;
        _sleekflowUserManager = (SleekflowUserManager) userManager;
        _flowHubConfigsApi = flowHubConfigsApi;
        _staffHooks = staffHooks;
    }

    // TODO Update Validation Logic
    private async Task<bool> ValidateToken(string jwt)
    {
        var jwtHandler = new JwtSecurityTokenHandler();
        // The secretKey
        // 1. No conflict with different environment. (e.g. uat only work in uat only and not work with production)
        // 2. Audience + random string can improve security.
        // 3. Issuer - TenantHub
        // 4. Audience - Sleekflow.TenantHub.io

        var secretKey = _configuration["Auth0:TenantHub:SecretKey"] ?? string.Empty;

        var tokenValidate = new TokenValidationParameters
        {
            ValidateLifetime = false,
            ValidateAudience = false,
            ValidateIssuer = false,
            RequireExpirationTime = true,
            RequireSignedTokens = true,
            IssuerSigningKey = new SymmetricSecurityKey(Encoding.ASCII.GetBytes(secretKey))
        };
        return (await jwtHandler.ValidateTokenAsync(jwt, tokenValidate)).IsValid;
    }

    private async Task<bool> ValidateTenantHubHeader()
    {
        Request.Headers.TryGetValue("X-Tenant-Hub-Authorization", out var token);

        return !string.IsNullOrEmpty(token) && await ValidateToken(token.ToString());
    }

    public class GetUserInput
    {
        [JsonProperty("sleekflow_user_id")]
        public string SleekflowUserId { get; set; }

        [JsonConstructor]
        public GetUserInput(string sleekflowUserId)
        {
            SleekflowUserId = sleekflowUserId;
        }
    }

    public class GetUserOutput
    {
        [JsonProperty("staff_id")]
        public long StaffId { get; set; }

        [JsonProperty("company_id")]
        public string? CompanyId { get; set; }

        [JsonProperty("user_id")]
        public string UserId { get; set; }

        [JsonProperty("first_name")]
        public string FirstName { get; set; }

        [JsonProperty("last_name")]
        public string LastName { get; set; }

        [JsonProperty("display_id")]
        public string DisplayName { get; set; }

        [JsonProperty("user_name")]
        public string? UserName { get; set; }

        [JsonProperty("email")]
        public string? Email { get; set; }

        [JsonProperty("phone_number")]
        public string? PhoneNumber { get; set; }

        [JsonProperty("position")]
        public string Position { get; set; }

        [JsonProperty("created_at")]
        public DateTime CreatedAt { get; set; }

        [JsonProperty("last_login_at")]
        public DateTime LastLoginAt { get; set; }

        [JsonProperty("email_confirmed")]
        public bool EmailConfirmed { get; set; }

        [JsonProperty("status")]
        [JsonConverter(typeof(StringEnumConverter))]
        public StaffStatus Status { get; set; }

        [JsonProperty("role")]
        public string Role { get; set; }

        [JsonProperty("role_type")]
        [JsonConverter(typeof(StringEnumConverter))]
        public StaffUserRole RoleType { get; set; }

        [JsonProperty("roles")]
        public List<string> Roles { get; set; }

        [JsonProperty("time_zone_info_id")]
        public string TimeZoneInfoId { get; set; }

        [JsonProperty("team_ids")]
        public List<string> TeamIds { get; set; }

        [JsonConstructor]
        public GetUserOutput(
            long staffId,
            string companyId,
            string userId,
            string firstName,
            string lastName,
            string displayName,
            string? userName,
            string? email,
            string? phoneNumber,
            string position,
            DateTime createdAt,
            DateTime lastLoginAt,
            bool emailConfirmed,
            StaffStatus status,
            string role,
            StaffUserRole roleType,
            List<string> roles,
            string timeZoneInfoId,
            List<string> teamIds)
        {
            StaffId = staffId;
            CompanyId = companyId;
            UserId = userId;
            FirstName = firstName;
            LastName = lastName;
            DisplayName = displayName;
            UserName = userName;
            Email = email;
            PhoneNumber = phoneNumber;
            Position = position;
            CreatedAt = createdAt;
            LastLoginAt = lastLoginAt;
            EmailConfirmed = emailConfirmed;
            Status = status;
            Role = role;
            RoleType = roleType;
            Roles = roles;
            TimeZoneInfoId = timeZoneInfoId;
            TeamIds = teamIds;
        }

        public GetUserOutput(Staff staff, List<string> teamIds, List<string> roles)
            : this(
                staff.Id,
                staff.CompanyId,
                staff.Identity.Id,
                staff.Identity.FirstName,
                staff.Identity.LastName,
                staff.Identity.DisplayName,
                staff.Identity.UserName,
                staff.Identity.Email,
                staff.Identity.PhoneNumber,
                staff.Position,
                staff.Identity.CreatedAt,
                staff.Identity.LastLoginAt,
                staff.Identity.EmailConfirmed,
                staff.Status,
                staff.Role,
                staff.RoleType,
                roles,
                staff.TimeZoneInfoId,
                teamIds)
        {
        }
    }

    [HttpPost("GetUser")]
    [ProducesResponseType(typeof(GetUserOutput), StatusCodes.Status200OK)]
    public async Task<ActionResult<GetUserOutput>> GetUser(
        [FromBody]
        GetUserInput getUserInput)
    {
        Request.Headers.TryGetValue("X-Tenant-Hub-Authorization", out var token);
        if (string.IsNullOrEmpty(token))
        {
            Request.Headers.TryGetValue("X-Tenant-Hub-Authorziation", out token);
        }

        _logger.LogInformation("GetUser {GetUserInput}", JsonConvert.SerializeObject(getUserInput));
        if (string.IsNullOrEmpty(token) || !await ValidateToken(token.ToString()))
        {
            _logger.LogError("[{GetUserName}] Token is invalid {Token}",nameof(GetUser), token);
            return NotFound();
        }

        var user = await _sleekflowUserManager.FindByIdAsync(getUserInput.SleekflowUserId);
        _logger.LogInformation("Obtained asp.net user information {User}", JsonConvert.SerializeObject(user));
        if (user is null)
        {
            _logger.LogError("[{GetUserName}] User ({Id}) not found", nameof(GetUser), getUserInput.SleekflowUserId);
            return NotFound();
        }

        var staff = await _coreService.GetCompanyStaff(user);
        if (staff is null)
        {
            _logger.LogError(
                "[{GetUserName}] User staff of identity id ({Id}) not found",
                nameof(GetUser),
                getUserInput.SleekflowUserId);
            return NotFound();
        }

        var roles = await _sleekflowUserManager.GetRolesAsync(user);
        var companyTeams = await _companyTeamService.GetCompanyteamFromStaffId(staff.CompanyId, staff.IdentityId);
        return Ok(
            new GetUserOutput(
                staff,
                companyTeams.Select(c => c.Id.ToString()).ToList(),
                new List<string>
                {

                    Enum.GetName(typeof(StaffUserRole), staff.RoleType) ?? ""
                }.Concat(roles).ToList()));
    }

    public class CleanIpWhitelistCacheInput
    {
        [JsonProperty("company_id")]
        public string CompanyId { get; set; }

        public CleanIpWhitelistCacheInput(string companyId)
        {
            CompanyId = companyId;
        }
    }

    public class CleanIpWhitelistCacheOutput
    {
        [JsonProperty("success")]
        public bool Success { get; set; }

        public CleanIpWhitelistCacheOutput(bool success)
        {
            Success = success;
        }
    }

    [HttpPost("CleanIpWhitelistCache")]
    [ProducesResponseType(typeof(CleanIpWhitelistCacheOutput), StatusCodes.Status200OK)]
    public async Task<ActionResult<CleanIpWhitelistCacheOutput>> CleanIpWhitelistCache(
        [FromBody]
        CleanIpWhitelistCacheInput cleanIpWhitelistCacheInput)
    {
        try
        {
            Request.Headers.TryGetValue("X-Tenant-Hub-Authorization", out var token);
            _logger.LogInformation(
                "CleanIPWhitelistCache {CompanyId}",
                cleanIpWhitelistCacheInput.CompanyId);
            if (string.IsNullOrEmpty(token) || !await ValidateToken(token.ToString()))
            {
                return NotFound();
            }

            var ipWhitelistCacheKeyPattern = new IpWhitelistCacheKeyPattern(cleanIpWhitelistCacheInput.CompanyId);

            await _cacheManagerService.DeleteCacheAsync(ipWhitelistCacheKeyPattern);

            return Ok(new CleanIpWhitelistCacheOutput(true));
        }
        catch (Exception e)
        {
            _logger.LogError(
                "[{CleanIpWhitelistCacheName}] Error when send the clean cache request: {EMessage}",
                nameof(CleanIpWhitelistCache), e.Message);
            return Ok(new CleanIpWhitelistCacheOutput(false));
        }
    }

    public class RefreshIpWhitelistCacheInput
    {
        [JsonProperty("company_id")]
        public string CompanyId { get; set; }

        public RefreshIpWhitelistCacheInput()
        {
        }

        public RefreshIpWhitelistCacheInput(string companyId)
        {
            CompanyId = companyId;
        }
    }

    public class RefreshIpWhitelistCacheOutput
    {
        [JsonProperty("success")]
        public bool Success { get; set; } = false;

        [JsonProperty("is_enabled")]
        public bool IsEnabled { get; set; } = false;

        [JsonProperty("data")]
        public IpWhitelistSettings? IpWhitelistSettings { get; set; }

        public RefreshIpWhitelistCacheOutput(bool success, bool isEnabled, IpWhitelistSettings? ipWhitelistSettings)
        {
            Success = success;
            IsEnabled = isEnabled;
            IpWhitelistSettings = ipWhitelistSettings;
        }
    }

    [HttpPost("RefreshIPWhitelistCache")]
    [ProducesResponseType(typeof(RefreshIpWhitelistCacheOutput), StatusCodes.Status200OK)]
    public async Task<ActionResult<RefreshIpWhitelistCacheOutput>> RefreshIpWhitelistCache(
        [FromBody] RefreshIpWhitelistCacheInput refreshIpWhitelistCacheInput)
    {
        try
        {
            Request.Headers.TryGetValue("X-Tenant-Hub-Authorization", out var token);
            _logger.LogInformation(
                "Refresh IPWhitelist Cache {RefreshIPWhitelistCache}",
                JsonConvert.SerializeObject(refreshIpWhitelistCacheInput));
            if (string.IsNullOrEmpty(token) || !await ValidateToken(token.ToString()))
            {
                return NotFound();
            }

            var company =
                await _appDbContext.CompanyCompanies.FirstOrDefaultAsync(
                    c => c.Id == refreshIpWhitelistCacheInput.CompanyId);
            if (company is null)
            {
                throw new Exception(
                    $"{nameof(RefreshIpWhitelistCache)} Company id {refreshIpWhitelistCacheInput.CompanyId} not found.");
            }

            var managementGetAllFeaturesOutputOutput =
                await _managementFeaturesApi.ManagementFeaturesGetAllFeaturesPostAsync(body: new object());
            var ipWhitelistFeature =
                managementGetAllFeaturesOutputOutput.Data.Features.First(
                    f => f.Name == "IpWhitelist");
            var isIPWhitelistFeatureEnabled =
                (await _managementEnabledFeaturesApi.ManagementEnabledFeaturesIsFeatureEnabledForCompanyPostAsync(
                    managementIsFeatureEnabledForCompanyInput: new ManagementIsFeatureEnabledForCompanyInput(
                        refreshIpWhitelistCacheInput.CompanyId,
                        ipWhitelistFeature.Id))).Data?.IsFeatureEnabled ?? false;

            if (isIPWhitelistFeatureEnabled)
            {
                var ipWhitelists = _ipWhitelistsApi.ManagementIpWhitelistsGetIpWhitelistSettingsPost(
                    managementGetIpWhitelistSettingsInput: new ManagementGetIpWhitelistSettingsInput(
                        refreshIpWhitelistCacheInput.CompanyId)).Data?.IpWhitelistSettings;

                var ipWhitelistCacheKeyPatternRefresh = new IpWhitelistCacheKeyPattern(refreshIpWhitelistCacheInput.CompanyId);

                // If don't want it to expire, don't specify an expiration
                await _cacheManagerService.SaveCacheAsync(ipWhitelistCacheKeyPatternRefresh, ipWhitelists);

                return Ok(
                    new RefreshIpWhitelistCacheOutput(
                        success: true,
                        isEnabled: true,
                        ipWhitelistSettings: ipWhitelists));
            }

            var ipWhitelistCacheKeyPatternClean = new IpWhitelistCacheKeyPattern(refreshIpWhitelistCacheInput.CompanyId);
            await _cacheManagerService.DeleteCacheAsync(ipWhitelistCacheKeyPatternClean);

            return Ok(
                new RefreshIpWhitelistCacheOutput(
                    success: true,
                    isEnabled: false,
                    ipWhitelistSettings: null));
        }
        catch (Exception e)
        {
            _logger.LogError(
                "[{RefreshIpWhitelistCacheName}] Error when saving cache: {EMessage}",
                nameof(RefreshIpWhitelistCache),
                e.Message);

            return Ok(
                new RefreshIpWhitelistCacheOutput(
                    success: false,
                    isEnabled: false,
                    ipWhitelistSettings: null));
        }
    }

    public class RegisterCompanyResponse : RegisterCompanyResult
    {
        public string UserId { get; set; }

        public string Email { get; set; }

        public string UserName { get; set; }

        public string? FirstName { get; set; }

        public string? LastName { get; set; }

        public RegisterCompanyResponse(
            string staffId,
            string signalRGroupName,
            bool isShopifyAccount,
            List<string> associatedCompanyIds,
            string location,
            string userId,
            string email,
            string userName,
            string firstName,
            string lastName)
            : base(staffId, signalRGroupName, isShopifyAccount, associatedCompanyIds, location)
        {
            UserId = userId;
            Email = email;
            UserName = userName;
            FirstName = firstName;
            LastName = lastName;
        }
    }

    [HttpPost("RegisterCompany")]
    [ProducesResponseType(typeof(RegisterCompanyResponse), StatusCodes.Status200OK)]
    public async Task<ActionResult<RegisterCompanyResponse>> RegisterCompany(
        [FromBody]
        RegisterCompanyInput registerCompanyInput)
    {
        if (!await ValidateTenantHubHeader())
        {
            return NotFound();
        }

        if (!ModelState.IsValid)
        {
            return BadRequest(InvalidModelStateResponse.FromModelState(ModelState));
        }

        if (registerCompanyInput.UserId is null)
        {
            return BadRequest("UserId is required");
        }

        var dbUser = await _sleekflowUserManager.FindByIdAsync(registerCompanyInput.UserId);
        try
        {
            if (dbUser is null)
            {
                // TODO: User will add to the current location if user is not found, should not be bad request .. ?
                // Implementation for migrated users
                /*
                _logger.LogError(
                    $"[{nameof(RegisterCompany)}] User ({registerCompanyInput.UserId}) not found in {registerCompanyInput.Location} server");

                return BadRequest(
                    new ResponseViewModel()
                    {
                        message =
                            $"[{nameof(RegisterCompany)}] User not found in {registerCompanyInput.Location} server"
                    });
                    */

                // TODO: Should we create a new user if not found?
                // Implementation for creating a new user
                _logger.LogCritical(
                    $"[{nameof(RegisterCompany)}] User ({registerCompanyInput.UserId}) not found in {registerCompanyInput.Location} server, creating new user");

                var auth0Users = await _sleekflowUserManager.GetAuth0UsersById(registerCompanyInput.UserId);
                var auth0User = auth0Users?.FirstOrDefault(
                    u => JsonConvert.DeserializeObject<Auth0AppMetadata>(u.AppMetadata.ToString()).SleekflowId ==
                         registerCompanyInput.UserId);
                if (auth0User is null)
                {
                    _logger.LogError(
                        "[{RegisterCompanyName}] Auth0 user not found for user id {UserId}",
                        nameof(RegisterCompany),
                        registerCompanyInput.UserId);
                    return BadRequest("User not found");
                }

                var (_, createdUser) = await _sleekflowUserManager.CreateNewDbUser(auth0User);
                dbUser = createdUser;
            }

            var isSocialLogin = registerCompanyInput.ConnectionStrategy is not "auth0";

            dbUser.EmailConfirmed = isSocialLogin;

            // If Social Login, we will not update the name
            if (!isSocialLogin)
            {
                dbUser.FirstName = registerCompanyInput.FirstName;
                dbUser.LastName = registerCompanyInput.LastName;
                dbUser.DisplayName =
                    $"{registerCompanyInput.FirstName} {registerCompanyInput.LastName}";
            }

            dbUser.PhoneNumber = registerCompanyInput.PhoneNumber;
            dbUser.IsAgreeMarketingConsent = registerCompanyInput.IsAgreeMarketingConsent;

            var updateResult = await _sleekflowUserManager.UpdateAsync(dbUser);
            if (!updateResult.Succeeded)
            {
                foreach (var updateResultError in updateResult.Errors)
                {
                    _logger.LogError(
                        $"[{nameof(RegisterCompany)}] Unable to update the user, {{Description}} {{Code}}",
                        updateResultError.Description,
                        updateResultError.Code);
                }

                return BadRequest(ModelState);
            }

            var updatedUser = await _sleekflowUserManager.GetUserAsync(dbUser);
            var registerCompanyOutput =
                await _auth0CompanyService.RegisterCompanyAsync(
                    updatedUser ?? throw new InvalidOperationException(), registerCompanyInput);
            try
            {
                await _flowHubConfigsApi.FlowHubConfigsEnrollFlowHubPostAsync(
                    enrollFlowHubInput: new EnrollFlowHubInput(
                        registerCompanyInput.CompanyId,
                        dbUser.Id.ToString(),
                        new List<string>(),
                        _configuration.GetValue<string>("Values:DomainName")));
            }
            catch (Exception e)
            {
                _logger.LogError(
                    e,
                    "[{MethodName}] Unable to enrol flow limit for company {CompanyId}",
                    nameof(RegisterCompany),
                    registerCompanyInput.CompanyId);
            }

            BackgroundJob.Enqueue<IFlowHubService>(x => x.UpdateFlowHubConfigAsync(registerCompanyInput.CompanyId, null));
            BackgroundJob.Enqueue<IIntelligentHubService>(x => x.RefreshIntelligentHubConfigAsync(registerCompanyInput.CompanyId));

            return new RegisterCompanyResponse(
                registerCompanyOutput.StaffId,
                registerCompanyOutput.SignalRGroupName,
                registerCompanyOutput.IsShopifyAccount,
                registerCompanyOutput.AssociatedCompanyIds,
                registerCompanyInput.Location,
                updatedUser.Id,
                updatedUser.Email!,
                updatedUser.UserName!,
                updatedUser.FirstName,
                updatedUser.LastName);
        }
        catch (Exception e)
        {
            _logger.LogError(
                e,
                $"[{nameof(RegisterCompany)}] Error occur when register company: {{ExceptionMessage}}",
                e.Message);
            throw;
        }
    }

    public class GetApplicationUserInput
    {
        [JsonProperty("sleekflow_user_id")]
        public string? SleekflowUserId { get; set; }

        [JsonProperty("email")]
        public string? Email { get; set; }

        [JsonProperty("username")]
        public string? UserName { get; set; }

        [JsonConstructor]
        public GetApplicationUserInput(string? sleekflowUserId=null, string? email=null, string? userName=null)
        {
            SleekflowUserId = sleekflowUserId;
            Email = email;
            UserName = userName;
        }
    }

    [HttpPost("GetApplicationUser")]
    [ProducesResponseType(typeof(ApplicationUser), StatusCodes.Status200OK)]
    public async Task<ActionResult<ApplicationUser>> GetApplicationUser(
        [FromBody]
        GetApplicationUserInput getUserInput)
    {
        if (!await ValidateTenantHubHeader())
        {
            return NotFound();
        }

        if (getUserInput?.SleekflowUserId is not null)
        {
            var user = await _sleekflowUserManager.FindByIdAsync(getUserInput.SleekflowUserId);
            return Ok(user);
        }

        if (getUserInput.Email is not null)
        {
            var user = await _sleekflowUserManager.FindByEmailAsync(getUserInput.Email);
            return Ok(user);
        }

        if (getUserInput.UserName is not null)
        {
            var user = await _sleekflowUserManager.FindByNameAsync(getUserInput.UserName);
            return Ok(user);
        }

        return BadRequest("Invalid input");
    }

    public class AssociateDbUserWithAuth0Input
    {
        [JsonProperty("auth0_user")]
        public Auth0User Auth0User { get; set; }

        [JsonProperty("application_user")]
        public ApplicationUser ApplicationUser { get; set; }

        [JsonProperty("roles")]
        public List<string?> Roles { get; set; }

        public AssociateDbUserWithAuth0Input(Auth0User auth0User, ApplicationUser applicationUser)
        {
            Auth0User = auth0User;
            ApplicationUser = applicationUser;
        }
    }

    public class AssociateDbUserWithAuth0Output
    {
        [JsonProperty("auth0_user")]
        public Auth0User UpdatedAuth0User { get; set; }

        [JsonProperty("application_user")]
        public ApplicationUser ApplicationUser { get; set; }

        [JsonProperty("roles")]
        public List<string>? Roles { get; set; }

        public AssociateDbUserWithAuth0Output(Auth0User auth0User, ApplicationUser applicationUser, List<string>? roles)
        {
            UpdatedAuth0User = auth0User;
            ApplicationUser = applicationUser;
            Roles = roles;
        }
    }


    [HttpPost("AssociateDbUserToAuth0")]
    public async Task<ActionResult<AssociateDbUserWithAuth0Output>> AssociateDbUserWithAuth0(
        [FromBody] AssociateDbUserWithAuth0Input input)
    {
        if (!await ValidateTenantHubHeader())
        {
            return NotFound();
        }

        var dbRoles = await _sleekflowUserManager.GetRolesAsync(input.ApplicationUser);
        var removeItems = input.Roles.Except(dbRoles);
        var addItems = dbRoles.Except(input.Roles);

        var enumerable = removeItems.ToList();
        if (enumerable.Any())
        {
            foreach (var role in enumerable)
            {
                await _sleekflowUserManager.RemoveFromRoleAsync(input.ApplicationUser, role!);
            }
        }

        var items = addItems.ToList();
        if (items.Any())
        {
            foreach (var role in items)
            {
                await _sleekflowUserManager.AddToRoleAsync(input.ApplicationUser, role!);
            }
        }

        return Ok(
            new AssociateDbUserWithAuth0Output(
                input.Auth0User,
                input.ApplicationUser,
                (await _sleekflowUserManager.GetRolesAsync(input.ApplicationUser))?.ToList()));
    }

    public class CreateNewDbUserInput
    {
        [JsonProperty("sleekflow_user_id")]
        public string SleekflowUserId { get; set; }

        [JsonProperty("auth0_user")]
        public Auth0User Auth0User { get; set; }

        public CreateNewDbUserInput(Auth0User auth0User, string sleekflowUserId)
        {
            Auth0User = auth0User;
            SleekflowUserId = sleekflowUserId;
        }
    }

    public class CreateNewDbUserOutput
    {
        [JsonProperty("application_user")]
        public ApplicationUser ApplicationUser { get; set; }

        public CreateNewDbUserOutput(ApplicationUser applicationUser)
        {
            ApplicationUser = applicationUser;
        }
    }

    [HttpPost("CreateNewDbUser")]
    public async Task<ActionResult<CreateNewDbUserOutput>> CreateNewDbUser([FromBody] CreateNewDbUserInput input)
    {
        if (!await ValidateTenantHubHeader())
        {
            return NotFound();
        }

        var createdUser = await _sleekflowUserManager.CreateNewDbUserByAuth0User(input.Auth0User);

        return Ok(new CreateNewDbUserOutput(createdUser));
    }

    public class UpdateDbuserInput
    {
        [JsonProperty("auth0_user")]
        public Auth0User Auth0User { get; set; }

        public UpdateDbuserInput(Auth0User auth0User)
        {
            Auth0User = auth0User;
        }
    }

    public class UpdateDbUserOutput
    {
        [JsonProperty("message")]
        public string Message { get; set; }

        [JsonProperty("application_user")]
        public ApplicationUser ApplicationUser { get; set; }

        public UpdateDbUserOutput(string message, ApplicationUser applicationUser)
        {
            Message = message;
            ApplicationUser = applicationUser;
        }
    }

    [HttpPost("UpdateDbUser")]
    public async Task<ActionResult<UpdateDbUserOutput>> UpdateDbUser([FromBody] UpdateDbuserInput input)
    {
        if (!await ValidateTenantHubHeader())
        {
            return NotFound();
        }

        var updatedUser = await _sleekflowUserManager.UpdateDbUserByAuth0User(input.Auth0User);

        return Ok(new UpdateDbUserOutput("User updated successfully", updatedUser));
    }

    public class DeleteDbUserInput
    {
        [JsonProperty("sleekflow_user_id")]
        public string SleekflowUserId { get; set; }

        [JsonProperty("is_also_delete_auth0_user")]
        public bool AlsoDeleteAuth0User { get; set; }

        public DeleteDbUserInput(string sleekflowUserId, bool? alsoDeleteAuth0User = true)
        {
            SleekflowUserId = sleekflowUserId;
            AlsoDeleteAuth0User = alsoDeleteAuth0User ?? true;
        }
    }

    [HttpPost("DeleteDbUser")]
    public async Task<IActionResult> DeleteDbUser([FromBody] DeleteDbUserInput input)
    {
        if (!await ValidateTenantHubHeader())
        {
            return NotFound();
        }

        var deleteUser = await _sleekflowUserManager.FindByIdAsync(input.SleekflowUserId);
        if (deleteUser is not null)
        {
            await _sleekflowUserManager.DeleteAsync(deleteUser, input.AlsoDeleteAuth0User);
        }

        return Ok("User deleted successfully");
    }

    public class OnUserChangePasswordInput
    {
        [JsonProperty("sleekflow_user_id")]
        public string SleekflowUserId { get; set; }

        public OnUserChangePasswordInput(string sleekflowUserId)
        {
            SleekflowUserId = sleekflowUserId;
        }
    }

    [HttpPost("OnUserChangePassword")]
    public async Task<IActionResult> OnUserChangePassword([FromBody] OnUserChangePasswordInput input)
    {
        if (!await ValidateTenantHubHeader())
        {
            return NotFound();
        }

        var dbUser = await _sleekflowUserManager.FindByIdAsync(input.SleekflowUserId);

        if (dbUser is not null)
        {
            // Reset password auto logout all session
            var staff = await _appDbContext.UserRoleStaffs
                .Where(x => x.IdentityId == dbUser.Id)
                .Include(x => x.RegisteredSessions)
                .FirstOrDefaultAsync();
            if (staff == null)
            {
                _logger.LogInformation(
                    $"[{nameof(OnUserChangePassword)}] Skip the disconnect Signal connection because Staff is null user id: {dbUser.Id}");

                return Ok();
            }

            staff
                .RegisteredSessions
                .ForEach(x => x.SessionStatus = SessionStatus.AutoLogout);
            foreach (var device in staff.RegisteredSessions)
            {
                await _signalRService.SignalRAutoLogout(device.UUID, _mapper.Map<ActiveSessionResponse>(device));
            }

            await _appDbContext.SaveChangesAsync();
            await _sleekflowUserManager.UpdateAsync(dbUser);
        }

        return Ok();
    }

    public class InviteUserByEmailsInput
    {
        [Required]
        [JsonProperty("sleekflow_user_id")]
        public string SleekflowUserId { get; set; }

        [Required]
        [JsonProperty("invite_users")]
        public List<InviteUserInputObject> InviteUsers { get; set; }

        [Required]
        [JsonProperty("team_ids")]
        public List<long> TeamIds { get; set; }

        [JsonConstructor]
        public InviteUserByEmailsInput(string sleekflowUserId, List<InviteUserInputObject> inviteUsers, List<long> teamIds)
        {
            SleekflowUserId = sleekflowUserId;
            InviteUsers = inviteUsers;
            TeamIds = teamIds;
        }
    }

    public class InviteUserByEmailsResponse : List<InvitedUserByEmailObject>
    {
        public InviteUserByEmailsResponse(
            List<InvitedUserByEmailObject> invitedUsers)
        {
            var result = this;
            result.AddRange(invitedUsers);
        }
    }

    [HttpPost("InviteUsersByEmail")]
    [ProducesResponseType(typeof(InviteUserByEmailsResponse), StatusCodes.Status200OK)]
    public async Task<ActionResult<InviteUserByEmailsResponse>> InviteUsersByEmail(
        [FromBody] InviteUserByEmailsInput input)
    {
        if (!await ValidateTenantHubHeader())
        {
            return NotFound();
        }

        return Ok(
            new InviteUserByEmailsResponse(
                await _auth0CompanyService.InviteUsersByEmailAsync(
                    await _sleekflowUserManager.FindByIdAsync(input.SleekflowUserId),
                    input.TeamIds,
                    input.InviteUsers)));
    }

    public class ShareableInvitationInput
    {
        [JsonProperty("sleekflow_user_id")]
        public string SleekflowUserId { get; set; }

        [JsonProperty("data")]
        public ShareableInvitationViewModel ShareableInvitation { get; set; }

        [JsonConstructor]
        public ShareableInvitationInput(string sleekflowUserId, ShareableInvitationViewModel shareableInvitation)
        {
            SleekflowUserId = sleekflowUserId;
            ShareableInvitation = shareableInvitation;
        }
    }

    [HttpPost("GenerateShareableLink")]
    [ProducesResponseType(typeof(ShareableInvitationResponse), StatusCodes.Status200OK)]
    public async Task<ActionResult<ShareableInvitationResponse>> GenerateShareableLink(
        [FromBody] ShareableInvitationInput shareableInvitationInput)
    {
        if (!await ValidateTenantHubHeader())
        {
            return NotFound();
        }

        var (result, location) = await _auth0CompanyService.GenerateShareableLinkAsync(
            shareableInvitationInput.SleekflowUserId,
            shareableInvitationInput.ShareableInvitation);

        var response = _mapper.Map<ShareableInvitationResponse>(result);
        response.Location = location;

        return Ok(response);
    }

    public class AcceptInviteLinkInput
    {
        [Required]
        [JsonProperty("shareable_id")]
        public string ShareableId { get; set; }

        [Required]
        [JsonProperty("invite_shared_user_object")]
        public InviteSharedUserObject InviteSharedUserObject { get; set; }

        [JsonProperty("location")]
        public string Location { get; set; }

        public AcceptInviteLinkInput(string shareableId, InviteSharedUserObject inviteSharedUserObject, string location)
        {
            ShareableId = shareableId;
            InviteSharedUserObject = inviteSharedUserObject;
            Location = location;
        }
    }

    public class AcceptInviteLinkOutput
    {
        [JsonProperty("user_id")]
        public string UserId { get; set; }

        [JsonProperty("company_id")]
        public string CompanyId { get; set; }

        [JsonProperty("staff_id")]
        public string StaffId { get; set; }

        [JsonProperty("role_type")]
        public string RoleType { get; set; }

        [JsonProperty("team_ids")]
        public List<string> TeamIds { get; set; }

        [JsonProperty("auth0_user")]
        public Auth0User? Auth0User { get; set; }
    }

    [HttpPost("AcceptShareableInviteLink")]
    public async Task<ActionResult> AcceptInviteLink(
        [FromBody] AcceptInviteLinkInput acceptInviteLinkInput)
    {
        if (!await ValidateTenantHubHeader())
        {
            return NotFound();
        }

        if (!ModelState.IsValid)
        {
            return BadRequest(new ResponseViewModel
            {
                message = ModelState?.First().Value?.Errors.First().ErrorMessage,
                errorId = "INVALID_FORM_VALUE"
            });
        }

        _logger.LogDebug(
            "[{AcceptShareableInviteLinkName}] Accepting shareable invite link {ShareableId}: {SerializeObject}",
            nameof(AcceptInviteLink),
            acceptInviteLinkInput.ShareableId,
            JsonConvert.SerializeObject(acceptInviteLinkInput));

        var invitation = await _appDbContext.CompanyShareableInvitations
                    .Include(x => x.ShareableInvitationRecords)
                    .FirstOrDefaultAsync(x => x.InvitationId == acceptInviteLinkInput.ShareableId);

        if (invitation == null)
        {
            return BadRequest();
        }

        var usage = await _companyUsageService.GetCompanyUsage(invitation.CompanyId);

        if (usage.MaximumAgents <= usage.totalAgents)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Not enough agent quota, please purchase",
                    errorId = "SHARED_INVITE_EXCEEDED_COMPANY_QUOTA"
                });
        }

        if (invitation.Status == ShareableLinkStatus.Disabled)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Invitation disabled", errorId = "SHARED_INVITE_DISABLED"
                });
        }

        if (invitation.Quota <= invitation.ShareableInvitationRecords.Count)
        {
            invitation.Status = ShareableLinkStatus.Disabled;

            await _appDbContext.SaveChangesAsync();

            return BadRequest(
                new ResponseViewModel
                {
                    message = "Exceeded quota", errorId = "SHARED_INVITE_EXCEEDED_INVITATION_QUOTA"
                });
        }

        if (DateTime.UtcNow > invitation.ExpirationDate)
        {
            invitation.Status = ShareableLinkStatus.Disabled;

            await _appDbContext.SaveChangesAsync();

            return BadRequest(
                new ResponseViewModel
                {
                    message = "Expired", errorId = "SHARED_INVITE_EXPIRED"
                });
        }

        var staffId = await _appDbContext.UserRoleStaffs
            .Where(x => x.CompanyId == invitation.CompanyId)
            .OrderBy(x => x.Order)
            .ThenBy(x => x.Id)
            .Select(x => x.IdentityId)
            .FirstOrDefaultAsync();

        var inviteSharedUserObject = acceptInviteLinkInput.InviteSharedUserObject;
        var user = await _sleekflowUserManager.FindByEmailAsync(inviteSharedUserObject.Email);
        var auth0User = new Auth0User();

        _logger.LogInformation(
            "[{AcceptShareableInviteLink}] Company {CompanyId} creating user {UserEmail} with TenantHubUserId {TenantHubUserId}, object: {JsonObject}",
            nameof(AcceptInviteLink),
            invitation.CompanyId,
            inviteSharedUserObject.Email,
            inviteSharedUserObject.TenantHubUserId,
            JsonConvert.SerializeObject(inviteSharedUserObject));

        if (user is null)
        {
            if (inviteSharedUserObject.UserId is null)
            {
                _logger.LogError(
                    $"[{nameof(AcceptInviteLink)}] Missing user id in inviteSharedUserObject");
                throw new Exception("User id is Required");
            }
            if (inviteSharedUserObject.TenantHubUserId is null)
            {
                _logger.LogError(
                    $"[{nameof(AcceptInviteLink)}] Missing TenantHub user id in inviteSharedUserObject");
                throw new Exception("TenantHub User id is Required");
            }

            var result = await _sleekflowUserManager.CreateWithTenantHubIdAsync(
                new ApplicationUser
                {
                    Id = inviteSharedUserObject.UserId,
                    UserName = inviteSharedUserObject.UserName ?? inviteSharedUserObject.Email,
                    Email = inviteSharedUserObject.Email,
                    FirstName = inviteSharedUserObject.Firstname,
                    LastName = inviteSharedUserObject.Lastname,
                    DisplayName = $"{inviteSharedUserObject.Firstname} {inviteSharedUserObject.Lastname}",
                    PhoneNumber = inviteSharedUserObject.PhoneNumber
                },
                inviteSharedUserObject.Password,
                inviteSharedUserObject.TenantHubUserId);
            var identityResult = result.IdentityResult;
            auth0User = result.User;

            if (!identityResult.Succeeded)
            {
                _logger.LogError(
                    "[CompanyInvite] Company {CompanyId} create user {UserEmail} error: {Errors}",
                    invitation.CompanyId,
                    inviteSharedUserObject.Email,
                    JsonConvert.SerializeObject(identityResult.Errors));

                return BadRequest(
                    new ResponseViewModel
                    {
                        message = identityResult.Errors.First().Description, errorId = "CREATE_USER_FAILED"
                    });
            }

            user = await _sleekflowUserManager.FindByEmailAsync(inviteSharedUserObject.Email);
        }

        var userHaveCompanyRegistered = await _appDbContext.UserRoleStaffs
            .AnyAsync(u => u.IdentityId == user.Id);

        if (userHaveCompanyRegistered)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = $"User {user.Email} have registered a company",
                    errorId = "USER_HAVE_COMPANY_REGISTERED"
                });
        }

        var staff = new Staff
        {
            CompanyId = invitation.CompanyId,
            IdentityId = user.Id,
            Identity = user,
            Locale = "en",
            RoleType = invitation.Role,
            Position = inviteSharedUserObject.Position,
            TimeZoneInfoId = inviteSharedUserObject.TimeZoneInfoId,
            NotificationSettingId = 1
        };

        await _appDbContext.UserRoleStaffs.AddAsync(staff);

        invitation.ShareableInvitationRecords.Add(
            new ShareableInvitationRecord
            {
                InvitedStaff = staff
            });

        invitation.Redeemed = invitation.ShareableInvitationRecords.Count;

        if (invitation.Redeemed == invitation.Quota)
        {
            invitation.Status = ShareableLinkStatus.Disabled;
        }

        await _appDbContext.SaveChangesAsync();

        await _companyTeamService.AddOrRemoveTeam(
            invitation.CompanyId,
            staff.Id,
            invitation.TeamIds);

        var company = await _appDbContext.CompanyCompanies
            .FirstOrDefaultAsync(x => x.Id == invitation.CompanyId);

        await _coreService.UpdatePlanFreeToFreemium(staff);

        /* // TODO: Delete before release, this is for reference purpose
        var response = _mapper.Map<AuthenticationResponse>(user);

        response.SignalRGroupName = staff.Company.SignalRGroupName;
        response.IsShopifyAccount = staff.Company.IsShopifyAccount;
        response.AssociatedCompanyIds =
            await _appDbContext.UserRoleStaffs
                .Where(x => x.IdentityId == user.Id)
                .Select(x => x.CompanyId)
                .ToListAsync();
        _logger.LogInformation(
            $"[InviteByShareableURL] {response.Id}, {response.Email}, {response.SignalRGroupName}");
            */
        var response = new AcceptInviteLinkOutput()
        {
            UserId = user.Id,
            CompanyId = invitation.CompanyId,
            StaffId = staff.Id.ToString(),
            RoleType = staff.RoleType.ToString(),
            TeamIds = invitation.TeamIds
                .ConvertAll(x => x.ToString())
                .ToList(),
            Auth0User = auth0User
        };

        /*
        BackgroundJob.Enqueue<ICoreService>(
            x => x.AddToSleekFlowCRM(
                staff.CompanyId,
                user,
                "User",
                null,
                null,
                null,
                null));

        BackgroundJob.Enqueue<IInternalHubSpotService>(
            x => x.SyncCompanyStaffs(staff.CompanyId));
            */

        return Ok(response);
    }

    public class ResendInvitationEmailInput
    {
        [JsonProperty("admin_user_id")]
        public string AdminUserId { get; set; }

        [JsonProperty("company_id")]
        public string CompanyId { get; set; }

        [JsonProperty("sleekflow_user_id")]
        public string SleekflowUserId { get; set; }

        public ResendInvitationEmailInput(string adminUserId, string companyId, string sleekflowUserId)
        {
            AdminUserId = adminUserId;
            CompanyId = companyId;
            SleekflowUserId = sleekflowUserId;
        }
    }

    [HttpPost("ResendInvitationEmail")]
    public async Task<ActionResult<bool>> ResendInvitationEmail([FromBody] ResendInvitationEmailInput emailInput)
    {
        if (ModelState.IsValid)
        {
            var adminStaff = await _coreService.GetCompanyStaff(await _sleekflowUserManager.FindByIdAsync(emailInput.AdminUserId));

            if (adminStaff == null || adminStaff?.RoleType == StaffUserRole.Staff)
            {
                return Unauthorized();
            }

            return await _auth0CompanyService.ResendInvitationEmailAsync(emailInput.SleekflowUserId, adminStaff);
        }

        return BadRequest(ModelState);
    }

    public class IsCompanyRegisteredInput
    {
        [JsonProperty("sleekflow_user_id")]
        public string SleekflowUserId { get; set; }

        [JsonConstructor]
        public IsCompanyRegisteredInput(string sleekflowUserId)
        {
            SleekflowUserId = sleekflowUserId;
        }
    }

    public class IsCompanyRegisterOutput
    {
        [JsonProperty("is_company_registered")]
        public bool IsCompanyRegistered { get; set; }

        [JsonConstructor]
        public IsCompanyRegisterOutput(bool isCompanyRegistered)
        {
            IsCompanyRegistered = isCompanyRegistered;
        }
    }

    [HttpPost("IsCompanyRegistered")]
    public async Task<ActionResult<bool>> IsCompanyRegistered([FromBody] IsCompanyRegisteredInput input)
    {
        if (!await ValidateTenantHubHeader())
        {
            return NotFound();
        }

        var targetStaff = await _appDbContext.UserRoleStaffs.FirstOrDefaultAsync(u => u.IdentityId == input.SleekflowUserId);
        return Ok(new IsCompanyRegisterOutput(targetStaff != null));
    }

    public class DeleteCompanyStaffInput
    {
        [JsonProperty("company_id")]
        public string CompanyId { get; set; }

        [JsonProperty("staff_id")]
        public long StaffId { get; set; }

        [JsonConstructor]
        public DeleteCompanyStaffInput(long staffId, string companyId)
        {
            StaffId = staffId;
            CompanyId = companyId;
        }
    }

    public class DeleteCompanyStaffOutput
    {
        [JsonProperty("message")]
        public string Message { get; set; }

        [JsonConstructor]
        public DeleteCompanyStaffOutput(string message)
        {
            Message = message;
        }
    }

    [HttpPost("DeleteCompanyStaff")]
    public async Task<ActionResult<DeleteCompanyStaffOutput>> DeleteCompanyStaff([FromBody] DeleteCompanyStaffInput input)
    {
        if (!await ValidateTenantHubHeader())
        {
            return NotFound();
        }

        var isRemoved = await _coreService.RemoveStaffData(input.CompanyId, input.StaffId);
        if (!isRemoved)
        {
            return BadRequest(
                new DeleteCompanyStaffOutput($"Company Staff ({input.StaffId}) cannot be deleted!"));
        }

        var getCompanyUsageCacheKeyPattern = new GetCompanyUsageCacheKeyPattern(input.CompanyId);
        await _staffHooks.OnStaffDeletedAsync(input.CompanyId, input.StaffId);
        await _cacheManagerService.DeleteCacheAsync(getCompanyUsageCacheKeyPattern);

        return Ok(
            new DeleteCompanyStaffOutput($"Company Staff ({input.StaffId}) has been deleted!"));
    }
}