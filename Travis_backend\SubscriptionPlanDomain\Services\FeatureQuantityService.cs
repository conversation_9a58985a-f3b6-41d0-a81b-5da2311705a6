using System;
using System.Collections.Generic;
using System.Collections.Immutable;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.Constants;
using Travis_backend.Database;
using Travis_backend.Enums;
using Travis_backend.Extensions;
using Travis_backend.SubscriptionPlanDomain.Constants;
using Travis_backend.SubscriptionPlanDomain.Models;

namespace Travis_backend.SubscriptionPlanDomain.Services;

public interface IFeatureQuantityService
{
    /// <summary>
    /// Get company's feature quantity with breakdown total quantity to included quantity and purchased quantity.
    /// </summary>
    /// <param name="companyId">Company Id.</param>
    /// <param name="featureId">Feature Id.</param>
    /// <returns>FeatureQuantityInfo.</returns>
    Task<FeatureQuantityInfo> GetFeatureQuantityInfoAsync(string companyId, string featureId);

    /// <summary>
    /// Check is company has certain feature.
    /// </summary>
    /// <param name="companyId">Company Id.</param>
    /// <param name="featureId">Feature Id.</param>
    /// <returns>Boolean</returns>
    Task<bool> HasFeature(string companyId, string featureId);

    Task<int> GetFeatureQuantityAsync(string companyId, string featureId);

    Task<int> GetSubscriptionFeatureQuantityAsync(string companyId, string featureId);

    Task<int> GetAddOnsFeatureQuantityAsync(string companyId, string featureId);

    /// <summary>
    /// Get FeatureQuantity from selected PlanDefinition
    /// </summary>
    /// <param name="subscriptionPlanId">Subscription Plan Id.</param>
    /// <param name="featureId">Feature Id.</param>
    /// <returns>FeatureQuantity</returns>
    Task<FeatureQuantity> GetPlanFeatureQuantity(string subscriptionPlanId, string featureId);

    /// <summary>
    /// Determine whether company has reached purchase limit for the feature.
    /// </summary>
    /// <param name="companyId">Company Id.</param>
    /// <param name="baseSubscriptionPlanId">Base Subscription Plan Id.</param>
    /// <param name="featureId">Feature Id.</param>
    /// <param name="purchaseQuantity">Purchase Amount. Must be equals or greater than 0.</param>
    /// <returns>True if the feature is reached purchase limit; otherwise, false.</returns>
    Task<bool> IsFeatureReachedPurchaseLimit(string companyId, string baseSubscriptionPlanId, string featureId, int purchaseQuantity = 0);

    /// <summary>
    /// Determine whether company has exceed purchase limit for the feature
    /// </summary>
    /// <param name="companyId">Company Id.</param>
    /// <param name="baseSubscriptionPlanId">Base Subscription Plan Id.</param>
    /// <param name="featureId">Feature Id.</param>
    /// <param name="purchaseQuantity">Purchase Amount. Must be equals or greater than 0.</param>
    /// <returns>True if the feature is exceed purchase limit; otherwise, false.</returns>
    Task<bool> IsPurchaseFeatureExceedPurchaseLimit(string companyId, string baseSubscriptionPlanId, string featureId, int purchaseQuantity);
}

public class FeatureQuantityService : IFeatureQuantityService
{
    private readonly ApplicationDbContext _appDbContext;
    private readonly IPlanDefinitionService _planDefinitionService;
    private readonly ILogger<FeatureQuantityService> _logger;

    private static readonly IEnumerable<BillStatus> ExcludedBillStatus = new[] { BillStatus.Inactive, BillStatus.Terminated };

    public FeatureQuantityService(
        ApplicationDbContext appDbContext,
        IPlanDefinitionService planDefinitionService,
        ILogger<FeatureQuantityService> logger)
    {
        _appDbContext = appDbContext;
        _planDefinitionService = planDefinitionService;
        _logger = logger;
    }

    /// <inheritdoc />
    public async Task<FeatureQuantityInfo> GetFeatureQuantityInfoAsync(string companyId, string featureId)
    {
        var includedQuantity = await GetSubscriptionFeatureQuantityAsync(companyId, featureId);
        var purchasedQuantity = await GetAddOnsFeatureQuantityAsync(companyId, featureId);
        return new FeatureQuantityInfo(companyId, featureId, includedQuantity, purchasedQuantity);
    }

    /// <inheritdoc />
    public async Task<bool> HasFeature(string companyId, string featureId)
    {
        var featureQuantityInfo = await GetFeatureQuantityAsync(companyId, featureId);
        return featureQuantityInfo > 0;
    }

    public async Task<int> GetFeatureQuantityAsync(string companyId, string featureId)
    {
        var featureQuantityFromSubscription =
            await GetSubscriptionFeatureQuantityAsync(companyId, featureId);

        var featureQuantityFromAddOns =
            await GetAddOnsFeatureQuantityAsync(companyId, featureId);

        return featureQuantityFromSubscription + featureQuantityFromAddOns;
    }

    public async Task<int> GetSubscriptionFeatureQuantityAsync(string companyId, string featureId)
    {
        if (featureId == FeatureId.FlowBuilderFlowEnrolment)
        {
            //// Temporary solution & direction for Future Refactor:
            //// Binding each features with specific quantity calculation strategy,
            //// and use strategy to calculate instead of summing up all quantity from bill record.
            var addOnQuantity = await GetAddOnsFeatureQuantityAsync(companyId, featureId);

            if (addOnQuantity > 0)
            {
                return 0;
            }
        }

        var subscriptionBillRecords = await _appDbContext.CompanyBillRecords
            .Where(
                x => x.CompanyId == companyId &&
                     !ExcludedBillStatus.Contains(x.Status) &&
                     ValidSubscriptionPlan.SubscriptionPlan.Contains(x.SubscriptionPlanId) &&
                     x.PeriodStart <= DateTime.UtcNow &&
                     x.PeriodEnd > DateTime.UtcNow)
            .OrderByDescending(x => x.created)
            .ThenByDescending(x => x.PayAmount)
            .ToListAsync();

        if (subscriptionBillRecords.Count == 0)
        {
            return 0;
        }

        BillRecord subscriptionBillRecord;
        if (subscriptionBillRecords.Any(b => !ValidSubscriptionPlan.FreePlans.Contains(b.SubscriptionPlanId)))
        {
            subscriptionBillRecord = subscriptionBillRecords.FirstOrDefault(
                b => !ValidSubscriptionPlan.FreePlans.Contains(b.SubscriptionPlanId));
        }
        else
        {
            subscriptionBillRecord = subscriptionBillRecords.FirstOrDefault();
        }

        if (subscriptionBillRecord == null)
        {
            return 0;
        }

        string subscriptionPlanId;
        if (await _appDbContext.CustomSubscriptionPlanTranslationMaps.AnyAsync(
                m => m.SourceSubscriptionPlanId == subscriptionBillRecord.SubscriptionPlanId))
        {
            subscriptionPlanId = (await _appDbContext.CustomSubscriptionPlanTranslationMaps.FirstOrDefaultAsync(
                m => m.SourceSubscriptionPlanId == subscriptionBillRecord.SubscriptionPlanId)).DestinationSubscriptionPlanId;
        }
        else
        {
            subscriptionPlanId = subscriptionBillRecord.SubscriptionPlanId;
        }

        return await GetSubscriptionPlansFeatureQuantityAsync(
            new List<(string Id, int Quantity)>
        {
            new (subscriptionPlanId, 1)
        }, featureId);
    }

    public async Task<int> GetAddOnsFeatureQuantityAsync(string companyId, string featureId)
    {
        var addOnBillRecords = await _appDbContext.CompanyBillRecords
            .Where(
                x => x.CompanyId == companyId &&
                     !ExcludedBillStatus.Contains(x.Status) &&
                     !ValidSubscriptionPlan.SubscriptionPlan.Contains(x.SubscriptionPlanId) &&
                     x.PeriodStart <= DateTime.UtcNow &&
                     x.PeriodEnd > DateTime.UtcNow)
            .ToListAsync();

        if (!addOnBillRecords.Any())
        {
            return 0;
        }
        else if (featureId.EqualsIgnoreCase(FeatureId.FlowBuilderFlowEnrolment))
        {
            //// Special handle for enrolment add-on
            //// Need to refactor in future
            //// Take only 1 to handle the case of Stripe's subscription cancelled event received after update quantity to FlowHub.
            var enrollmentAddOnBillRecords = addOnBillRecords.Where(x => SubscriptionPlansId.FlowBuilderFlowEnrolmentsAddOns.Contains(x.SubscriptionPlanId)).ToList();

            if (enrollmentAddOnBillRecords.Count > 1)
            {
                var validEnrollmentAddOnBillRecord = enrollmentAddOnBillRecords
                    .OrderByDescending(x => x.created)
                    .ThenByDescending(x => x.PayAmount)
                    .First();

                var enrollmentAddOnBillRecordIds = enrollmentAddOnBillRecords.Select(x => x.Id).ToList();
                var billRecordIdToRemove = enrollmentAddOnBillRecordIds.Except(ImmutableList.Create(validEnrollmentAddOnBillRecord.Id));

                addOnBillRecords.RemoveAll(x => billRecordIdToRemove.Contains(x.Id));
            }
        }

        return await GetSubscriptionPlansFeatureQuantityAsync(
            addOnBillRecords
                .Select(br => (Id: br.SubscriptionPlanId, Quantity: (int) br.quantity))
                .ToList(), featureId);
    }

    /// <inheritdoc />
    public async Task<FeatureQuantity> GetPlanFeatureQuantity(string subscriptionPlanId, string featureId)
    {
        var planDefinition = await _planDefinitionService.GetPlanDefinitionAsync(subscriptionPlanId);
        return planDefinition.FeatureQuantities.FirstOrDefault(x => x.FeatureId == featureId, new FeatureQuantity(featureId, 0));
    }

    /// <inheritdoc />
    public async Task<bool> IsFeatureReachedPurchaseLimit(string companyId, string baseSubscriptionPlanId, string featureId, int purchaseQuantity = 0)
    {
        if (purchaseQuantity < 0)
        {
            throw new ArgumentOutOfRangeException(nameof(purchaseQuantity), "Must be equals or greater than 0.");
        }

        bool Predicate(long maximumQuantity, int currentQuantity) => currentQuantity + purchaseQuantity >= maximumQuantity;

        return await EvaluatePurchaseLimitAsync(companyId, baseSubscriptionPlanId, featureId, Predicate);
    }

    /// <inheritdoc />
    public async Task<bool> IsPurchaseFeatureExceedPurchaseLimit(string companyId, string baseSubscriptionPlanId, string featureId, int purchaseQuantity)
    {
        if (purchaseQuantity < 0)
        {
            throw new ArgumentOutOfRangeException(nameof(purchaseQuantity), "Must be equals or greater than 0.");
        }

        bool Predicate(long maximumQuantity, int currentQuantity) => currentQuantity + purchaseQuantity > maximumQuantity;

        return await EvaluatePurchaseLimitAsync(companyId, baseSubscriptionPlanId, featureId, Predicate);
    }

    private async Task<bool> EvaluatePurchaseLimitAsync(
        string companyId,
        string baseSubscriptionPlanId,
        string featureId,
        Func<long, int, bool> predicate)
    {
        var basePlanFeatureQuantity = await GetPlanFeatureQuantity(baseSubscriptionPlanId, featureId);
        var currentQuantity = await GetFeatureQuantityAsync(companyId, featureId);

        _logger.LogInformation(
            "EvaluatePurchaseLimit. CompanyId: {CompanyId}, BaseSubscriptionPlanId: {BaseSubscriptionPlanId} CurrentQuantity: {CurrentQuantity}, MaximumQuantity: {MaximumQuantity}",
            companyId,
            baseSubscriptionPlanId,
            currentQuantity,
            basePlanFeatureQuantity.MaximumQuantity
        );

        return basePlanFeatureQuantity.MaximumQuantity.HasValue && predicate(basePlanFeatureQuantity.MaximumQuantity.Value, currentQuantity);
    }

    private async Task<int> GetSubscriptionPlansFeatureQuantityAsync(
        List<(string Id, int Quantity)> subscriptionPlans,
        string featureId)
    {
        var featureQuantitiesSum = 0;

        foreach (var subscriptionPlan in subscriptionPlans)
        {
            var planDefinition = await _planDefinitionService
                .GetPlanDefinitionAsync(subscriptionPlan.Id);

            if (planDefinition is null)
            {
                continue;
            }

            featureQuantitiesSum += planDefinition.FeatureQuantities
                .Where(fq => fq.FeatureId == featureId)
                .Sum(fq => fq.Quantity * subscriptionPlan.Quantity);
        }

        return featureQuantitiesSum;
    }
}