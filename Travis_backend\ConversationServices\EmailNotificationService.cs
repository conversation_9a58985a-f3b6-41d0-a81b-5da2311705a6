﻿using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Hangfire;
using Humanizer;
using isRock.LineBot.Extensions;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using SendGrid;
using SendGrid.Helpers.Mail;
using ShopifySharp;
using Travis_backend.AccountAuthenticationDomain.Models;
using Travis_backend.ChannelDomain.Repositories;
using Travis_backend.ChannelDomain.ViewModels;
using Travis_backend.CommonDomain.Repositories;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.CompanyDomain.Services;
using Travis_backend.Constants;
using Travis_backend.ContactDomain.Models;
using Travis_backend.ContactDomain.Services;
using Travis_backend.ContactDomain.ViewModels;
using Travis_backend.ConversationDomain.Models;
using Travis_backend.CoreDomain.Models;
using Travis_backend.Database;
using Travis_backend.Enums;
using Travis_backend.Helpers;
using Travis_backend.MessageDomain.Models;

namespace Travis_backend.ConversationServices
{
    public interface IEmailNotificationService
    {
        Task NewMessageNotification(Conversation conversation, ConversationMessage conversationMessage);

        Task NewAssigneeNotification(string conversationId);

        Task SendUnAssigneeNotification(string staffId, string conversationId);

        Task NewNoteNotification(Conversation conversation, ConversationMessage conversationMesasge);

        Task ResetPassword(ApplicationUser applicationUser, string resetCode);

        Task SendInvitationEmail(ApplicationUser applicationUser, string resetCode, Staff staff, string? tenanthubUserId = null);

        Task SendEmailVerification(ApplicationUser applicationUser, string verificationCode);

        Task SendInvoiceToCustomer(
            string staffId,
            long? billRecordId,
            NotificationType notificationType = NotificationType.SendInvoiceToCustomer);

        Task WhatsappRegistrationNotifrication(WhatsappRegistrationViewModel whatsappRegistrationViewModel);

        Task WhatsappRegistrationInternalSlack(
            string companyId,
            WhatsappRegistrationViewModel whatsappRegistrationViewModel,
            string twilioAccountSid);

        Task SMSRegistrationNotifrication(SMSRegistrationViewModel whatsappRegistrationViewModel);

        // Task CannelWhatsAppSubscription(string staffId);
        Task WhatsAppTrialEnd(string companyId);

        Task SendWelcomeOnBoardNotification(
            string companyId,
            NotificationType notificationType = NotificationType.WelcomeToSleekFlow,
            string PlanId = null,
            string leadSource = null);

        Task ImportCompleted(long importContactHistoryId, List<ImportFailedRecord> failedRecords);

        Task BulkImportCompleted(
            string email,
            string displayName,
            string importName,
            int importedCount,
            int failedCount);

        Task SendAccountDeletionEmail(string companyId, NotificationType notificationType);

        Task WhatsappStepToProceed(WhatsappRegistrationViewModel whatsappRegistrationViewModel);

        Task SendDripEmailV2(
            string companyId,
            NotificationType notificationType = NotificationType.WelcomeToSleekFlow,
            string PlanId = null,
            string leadSource = null);

        Task SendNewCompanyEmailToSlackChannel(string companyId);

        Task SendShopifyDataRequest(Shop shopInfo, UserProfile customer);

        Task SendSystemAlertToSlackChannel(
            string subject,
            string message,
            string category,
            string type = "alert",
            string companyId = null,
            bool prependCompanyInfo = false);

        Task<Response> SendChatHistoryBackupEmailAsync(
            SendGridMessage msg);

        Task SendCustomFieldAnalytics(
            string companyId,
            string analyticsEmailNotificationConfigId,
            DateTime dateTime,
            Dictionary<string, byte[]> attachments);

        Task SendWorkflowInfiniteLoopEmailAsync(
            string companyId,
            string workflowId,
            string workflowName,
            DateTimeOffset createdAt);

        Task SendExecutionUsageReachedThresholdEmailAsync(string companyId, double threshold);

        Task IntegrationDisconnected(
            string companyId,
            string integrationName,
            string helpCenterUrl);
    }

    public class EmailNotificationService : IEmailNotificationService
    {
        private readonly ApplicationDbContext _appDbContext;
        private readonly IConfiguration _configuration;
        private readonly ILogger _logger;
        private readonly IUserProfileService _userProfileService;
        private readonly string _appDomainName;
        private readonly ICompanyUsageService _companyUsageService;
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly IIntegrationAlertConfigRepository _integrationAlertConfigRepository;

        public EmailNotificationService(
            ApplicationDbContext appDbContext,
            IConfiguration configuration,
            ILogger<EmailNotificationService> logger,
            IUserProfileService userProfileService,
            ICompanyUsageService companyUsageService,
            UserManager<ApplicationUser> userManager,
            IIntegrationAlertConfigRepository integrationAlertConfigRepository)
        {
            _appDbContext = appDbContext;
            _configuration = configuration;
            _logger = logger;
            _userProfileService = userProfileService;
            _companyUsageService = companyUsageService;
            _appDomainName = _configuration.GetValue<String>("Values:AppDomainName");
            _userManager = userManager;
            _integrationAlertConfigRepository = integrationAlertConfigRepository;
        }

        public async Task WhatsappRegistrationNotifrication(WhatsappRegistrationViewModel whatsappRegistrationViewModel)
        {
            try
            {
                var coreEmailConfig = await _appDbContext.CoreEmailConfigs.FirstOrDefaultAsync();
                var client = new SendGridClient(coreEmailConfig.SendGridKey);
                var from = new EmailAddress("<EMAIL>", coreEmailConfig.SenderName);

                var recipients = new List<EmailAddress>();
                recipients.Add(new EmailAddress("<EMAIL>"));

                // recipients.Add(new EmailAddress(whatsappRegistrationViewModel.Email));
                var subject = $"[SleekFlow] You've submitted an application to WhatsApp Official Business API";

                var plainTextContent = subject;

                var emailTemplate = await _appDbContext.CoreEmailNotificationTemplates
                    .OrderByDescending(x => x.Id)
                    .FirstOrDefaultAsync(x => x.NotificationType == NotificationType.WhatsappRegistration);

                var htmlContent = emailTemplate.EmailTemplate;

                try
                {
                    htmlContent = htmlContent.Replace("[Company_Name]", whatsappRegistrationViewModel.CompanyName);
                    htmlContent = htmlContent.Replace("[Customer]", whatsappRegistrationViewModel.FirstName);
                    htmlContent = htmlContent.Replace("[firstname]", whatsappRegistrationViewModel.FirstName);
                    htmlContent = htmlContent.Replace("[lastname]", whatsappRegistrationViewModel.LastName);
                    htmlContent = htmlContent.Replace(
                        "[companyname]",
                        $"{whatsappRegistrationViewModel.CompanyName}, prefer start with: {whatsappRegistrationViewModel.PreferStartWith}");
                    htmlContent = htmlContent.Replace("[companywebsite]", whatsappRegistrationViewModel.CompanyWebsite);
                    htmlContent = htmlContent.Replace("[companyemail]", whatsappRegistrationViewModel.Email);
                    htmlContent = htmlContent.Replace("[phonenumber]", whatsappRegistrationViewModel.PhoneNumber);
                    htmlContent = htmlContent.Replace(
                        "[businessid]",
                        whatsappRegistrationViewModel.FacebookBusinessManagerId);
                    htmlContent = htmlContent.Replace(
                        "[yes/no]",
                        whatsappRegistrationViewModel.CompletedFacebookVerifications);
                    htmlContent = htmlContent.Replace(
                        "[BR_url]",
                        string.Join(", ", whatsappRegistrationViewModel.fileURLs.ToArray()));
                    htmlContent = htmlContent.Replace(
                        "[ProofOfAddress]",
                        whatsappRegistrationViewModel.ProofOfAddressURLs);
                    htmlContent = htmlContent.Replace(
                        "[IdentityDocument]",
                        whatsappRegistrationViewModel.identifyDocumentURLs);
                    htmlContent = htmlContent.Replace(
                        "[LetterOfAuthorization]",
                        whatsappRegistrationViewModel.LetterOfAuthorizationURLs);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, ex.Message);
                }

                var msg = MailHelper.CreateSingleEmailToMultipleRecipients(
                    from,
                    recipients,
                    subject,
                    plainTextContent,
                    htmlContent);

                var response = await client.SendEmailAsync(msg);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, ex.Message);
            }
        }

        public async Task WhatsappRegistrationInternalSlack(
            string companyId,
            WhatsappRegistrationViewModel whatsappRegistrationViewModel,
            string twilioAccountSid)
        {
            try
            {
                var coreEmailConfig = await _appDbContext.CoreEmailConfigs.FirstOrDefaultAsync();
                var client = new SendGridClient(coreEmailConfig.SendGridKey);
                var from = new EmailAddress(coreEmailConfig.Email, coreEmailConfig.SenderName);

                var recipients = new List<EmailAddress>
                {
                    new ("<EMAIL>")
                };

                var subject = $"New WhatsApp Application from {whatsappRegistrationViewModel.CompanyName}";

                var plainTextContent = subject;

                var emailTemplate = await _appDbContext.CoreEmailNotificationTemplates
                    .OrderByDescending(x => x.Id)
                    .FirstOrDefaultAsync(x => x.NotificationType == NotificationType.NewWhatsappApplicationToSlack);

                var htmlContent = emailTemplate.EmailTemplate;

                try
                {
                    var companyInfo = companyId != null ?
                        await _appDbContext.CompanyCompanies
                            .Where(x => x.Id == companyId)
                            .Select(
                                x => new
                                {
                                    x.CompanyName,
                                    x.Id
                                })
                            .FirstOrDefaultAsync() :
                        null;

                    htmlContent = htmlContent.Replace(
                        "[Sleekflow_Company_Info]",
                        companyInfo != null ? $"{companyInfo.CompanyName} (ID: {companyInfo.Id})" : "N/A");
                    htmlContent = htmlContent.Replace("[Company_Name]", whatsappRegistrationViewModel.CompanyName);
                    htmlContent = htmlContent.Replace(
                        "[Company_Website]",
                        whatsappRegistrationViewModel.CompanyWebsite);
                    htmlContent = htmlContent.Replace(
                        "[Prefer_Start_With]",
                        whatsappRegistrationViewModel.PreferStartWith);
                    htmlContent = htmlContent.Replace(
                        "[Contact_Person]",
                        $"{whatsappRegistrationViewModel.FirstName} {whatsappRegistrationViewModel.LastName}");
                    htmlContent = htmlContent.Replace("[Email]", whatsappRegistrationViewModel.Email);
                    htmlContent = htmlContent.Replace("[Phone_Number]", whatsappRegistrationViewModel.PhoneNumber);
                    htmlContent = htmlContent.Replace(
                        "[Facebook_Business_ID]",
                        whatsappRegistrationViewModel.FacebookBusinessManagerId);
                    htmlContent = htmlContent.Replace("[Twilio_Account_SID]", twilioAccountSid);
                    htmlContent = htmlContent.Replace(
                        "[BR_url]",
                        whatsappRegistrationViewModel.fileURLs is { Count: > 0 }
                            ? string.Join(", ", whatsappRegistrationViewModel.fileURLs.ToArray())
                            : "N/A");
                    htmlContent = htmlContent.Replace(
                        "[ProofOfAddress]",
                        whatsappRegistrationViewModel.ProofOfAddressURLs ?? "N/A");
                    htmlContent = htmlContent.Replace(
                        "[IdentityDocument]",
                        whatsappRegistrationViewModel.identifyDocumentURLs ?? "N/A");
                    htmlContent = htmlContent.Replace(
                        "[LetterOfAuthorization]",
                        whatsappRegistrationViewModel.LetterOfAuthorizationURLs ?? "N/A");
                    htmlContent = htmlContent.Replace(
                        "[ApplyAt]",
                        DateTime.UtcNow.AddHours(8).ToString("yyyy/MM/dd HH:mm:ss"));
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, ex.Message);
                }

                var msg = MailHelper.CreateSingleEmailToMultipleRecipients(
                    from,
                    recipients,
                    subject,
                    plainTextContent,
                    htmlContent);

                var response = await client.SendEmailAsync(msg);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, ex.Message);
            }
        }

        public async Task WhatsappStepToProceed(WhatsappRegistrationViewModel whatsappRegistrationViewModel)
        {
            try
            {
                var coreEmailConfig = await _appDbContext.CoreEmailConfigs.FirstOrDefaultAsync();
                var client = new SendGridClient(coreEmailConfig.SendGridKey);
                var from = new EmailAddress("<EMAIL>", "SleekFlow WhatsApp");

                var recipients = new List<EmailAddress>();
                recipients.Add(new EmailAddress("<EMAIL>"));
                recipients.Add(
                    new EmailAddress(whatsappRegistrationViewModel.Email, whatsappRegistrationViewModel.FirstName));

                var wel_subject = $"Welcome to SleekFlow👋";
                var wel_Template = await _appDbContext.CoreEmailNotificationTemplates
                    .OrderByDescending(x => x.Id)
                    .FirstOrDefaultAsync(x => x.NotificationType == NotificationType.twilio_step_to_proceed_en);

                var appDomainName = _configuration.GetValue<String>("Values:AppDomainName");
                var verified_url =
                    $"{appDomainName.Replace("https://", string.Empty)}/twilio/whatsapp/verification?referenceId={whatsappRegistrationViewModel.ReferenceId}&email={whatsappRegistrationViewModel.Email}&isverified=true";

                var wel_htmlContent = wel_Template.EmailTemplate;
                try
                {
                    wel_htmlContent = wel_htmlContent
                        .Replace("[Customer]", whatsappRegistrationViewModel.FirstName)
                        .Replace("[Company Name]", whatsappRegistrationViewModel.CompanyName)
                        .Replace("[Company_Name]", whatsappRegistrationViewModel.CompanyName)
                        .Replace("[companyname]", whatsappRegistrationViewModel.CompanyName)
                        .Replace("[Verified_URL]", verified_url);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, ex.Message);
                }

                var msg = MailHelper.CreateSingleEmailToMultipleRecipients(
                    from,
                    recipients,
                    wel_Template.EmailSubject,
                    wel_Template.EmailSubject,
                    wel_htmlContent);

                var response = await client.SendEmailAsync(msg);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, ex.Message);
            }
        }

        public async Task SMSRegistrationNotifrication(SMSRegistrationViewModel whatsappRegistrationViewModel)
        {
            try
            {
                var coreEmailConfig = await _appDbContext.CoreEmailConfigs.FirstOrDefaultAsync();
                var client = new SendGridClient(coreEmailConfig.SendGridKey);
                var from = new EmailAddress("<EMAIL>", coreEmailConfig.SenderName);

                var recipients = new List<EmailAddress>();
                recipients.Add(new EmailAddress("<EMAIL>"));
                recipients.Add(new EmailAddress(whatsappRegistrationViewModel.Email));

                var subject = $"[SleekFlow] You've submitted an application to SMS";

                var plainTextContent = subject;

                var emailTemplate = await _appDbContext.CoreEmailNotificationTemplates
                    .OrderByDescending(x => x.Id)
                    .FirstOrDefaultAsync(x => x.NotificationType == NotificationType.SMSRegistration);

                var htmlContent = emailTemplate.EmailTemplate;

                try
                {
                    htmlContent = htmlContent.Replace("[Company_Name]", whatsappRegistrationViewModel.CompanyName);
                    htmlContent = htmlContent.Replace("[Customer]", whatsappRegistrationViewModel.FirstName);
                    htmlContent = htmlContent.Replace("[firstname]", whatsappRegistrationViewModel.FirstName);
                    htmlContent = htmlContent.Replace("[lastname]", whatsappRegistrationViewModel.LastName);
                    htmlContent = htmlContent.Replace("[companyname]", whatsappRegistrationViewModel.CompanyName);
                    htmlContent = htmlContent.Replace("[companyemail]", whatsappRegistrationViewModel.Email);
                    htmlContent = htmlContent.Replace("[phonenumber]", whatsappRegistrationViewModel.PhoneNumber);
                    htmlContent = htmlContent.Replace(
                        "[BR_url]",
                        string.Join(", ", whatsappRegistrationViewModel.fileURLs.ToArray()));
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, ex.Message);
                }

                var msg = MailHelper.CreateSingleEmailToMultipleRecipients(
                    from,
                    recipients,
                    subject,
                    plainTextContent,
                    htmlContent);

                var response = await client.SendEmailAsync(msg);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, ex.Message);
            }
        }

        public async Task NewAssigneeNotification(string conversationId)
        {
            try
            {
                var conversation = await _appDbContext.Conversations
                    .Include(x => x.UserProfile)
                    .Include(x => x.Assignee.Identity)
                    .Include(x => x.Assignee.NotificationSetting)
                    .FirstOrDefaultAsync(x => x.Id == conversationId);

                var coreEmailConfig = await _appDbContext.CoreEmailConfigs.FirstOrDefaultAsync();
                var client = new SendGridClient(coreEmailConfig.SendGridKey);
                var from = new EmailAddress(coreEmailConfig.Email, coreEmailConfig.SenderName);

                // Get companyInfo
                var companyInfo = await _appDbContext.CompanyCompanies
                    .Where(x => x.Id == conversation.CompanyId)
                    .Select(
                        x => new
                        {
                            x.TimeZoneInfoId,
                            x.CompanyName
                        })
                    .AsNoTracking()
                    .FirstOrDefaultAsync();

                var recipients = new List<EmailAddress>();
                if (conversation.Assignee != null)
                {
                    if (conversation.Assignee.NotificationSetting?.EmailNotificationNewMessages == false)
                    {
                        return;
                    }

                    recipients.Add(new EmailAddress(conversation.Assignee.Identity.Email));
                }

                var conversationMessage = await _appDbContext.ConversationMessages
                    .OrderByDescending(x => x.Id)
                    .FirstOrDefaultAsync(
                        x =>
                            x.ConversationId == conversation.Id &&
                            x.Channel != ChannelTypes.Note);

                var getTimeZone = TimeZoneHelper.GetTimeZoneById(companyInfo.TimeZoneInfoId);
                DateTime dateTime = conversationMessage.CreatedAt
                    .ToUniversalTime()
                    .AddHours(getTimeZone.BaseUtcOffsetInHour);

                string dateformat = dateTime.ToString("MMM dd yyyy HH:mm:ss");

                if (getTimeZone.BaseUtcOffsetInHour > 0)
                {
                    dateformat = dateformat + $" GMT +{getTimeZone.BaseUtcOffsetInHour}";
                }
                else
                {
                    dateformat = dateformat + $" GMT {getTimeZone.BaseUtcOffsetInHour}";
                }

                var priority = await _userProfileService.GetCustomFieldByFieldName(
                    conversation.UserProfile,
                    "Priority");

                var subject =
                    $"[{coreEmailConfig.SenderName}] New Assignment - Conversation from {ConversationHelper.GetChannelName(conversationMessage.Channel)} by {conversation.UserProfile.FirstName}";

                var plainTextContent = $"User: {conversation.UserProfile.FirstName}/n" +
                                       $"Status: {conversation.Status.ToUpperInvariant()}/n" +
                                       $"Assignee: {conversation.Assignee?.Identity?.DisplayName}/n";

                var emailTemplate = await _appDbContext.CoreEmailNotificationTemplates
                    .OrderByDescending(x => x.Id)
                    .FirstOrDefaultAsync(x => x.NotificationType == NotificationType.NewAssignee);

                var htmlContent = emailTemplate.EmailTemplate
                    .Replace("[Company_Name]", companyInfo.CompanyName)
                    .Replace("[Customer]", conversation.UserProfile.FirstName)
                    .Replace("[Status]", conversation.Status.ToUpperInvariant())
                    .Replace("[Assignee]", conversation.Assignee?.Identity?.DisplayName)
                    .Replace("[Channel]", ConversationHelper.GetChannelName(conversationMessage.Channel))
                    .Replace("[Priority]", (priority != null) ? priority.Value : "-")
                    .Replace("[Datetime]", dateformat)
                    .Replace("[Conversation_URL]", $"{_appDomainName}/inbox/all/{conversation.Id}");

                var msg = MailHelper.CreateSingleEmailToMultipleRecipients(
                    from,
                    recipients,
                    subject,
                    plainTextContent,
                    htmlContent);

                var response = await client.SendEmailAsync(msg);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, ex.Message);
            }
        }

        public async Task NewMessageNotification(Conversation conversation, ConversationMessage conversationMessage)
        {
            try
            {
                var coreEmailConfig = await _appDbContext.CoreEmailConfigs.FirstOrDefaultAsync();
                var client = new SendGridClient(coreEmailConfig.SendGridKey);
                var from = new EmailAddress(coreEmailConfig.Email, coreEmailConfig.SenderName);

                var recipients = new List<EmailAddress>();

                var assignee = await _appDbContext.Conversations
                    .Include(x => x.Assignee.Identity)
                    .Include(x => x.Assignee.NotificationSetting)
                    .FirstOrDefaultAsync(x => x.Id == conversation.Id);

                if (assignee.Assignee != null)
                {
                    if (assignee.Assignee.NotificationSetting?.EmailNotificationNewMessages == false)
                    {
                        return;
                    }

                    recipients.Add(new EmailAddress(assignee.Assignee.Identity.Email));
                }
                else
                {
                    return;

                    // var staffs = _appDbContext.UserRoleStaffs.Where(x => x.CompanyId == conversation.CompanyId).Include(x => x.Identity);
                    // foreach (var staff in staffs)
                    // {
                    //    recipients.Add(new EmailAddress(staff.Identity.Email));
                    // }
                }

                // Get companyInfo
                var companyInfo = await _appDbContext.CompanyCompanies
                    .Where(x => x.Id == conversation.CompanyId)
                    .Select(
                        x => new
                        {
                            x.TimeZoneInfoId,
                            x.CompanyName
                        })
                    .AsNoTracking()
                    .FirstOrDefaultAsync();

                var subject =
                    $"[{coreEmailConfig.SenderName}] New Message from {ConversationHelper.GetChannelName(conversationMessage.Channel)} by {conversation.UserProfile.FirstName}";

                var messageContent = string.Empty;

                if (conversationMessage.MessageContent != null)
                {
                    if (conversationMessage.TranslationResults != null)
                    {
                        messageContent = conversationMessage.TranslationResults.FirstOrDefault().translations
                            .FirstOrDefault().text;
                    }
                    else
                    {
                        messageContent = conversationMessage.MessageContent;
                    }
                }

                var getTimeZone = TimeZoneHelper.GetTimeZoneById(companyInfo.TimeZoneInfoId);
                DateTime dateTime = conversationMessage.CreatedAt
                    .ToUniversalTime()
                    .AddHours(getTimeZone.BaseUtcOffsetInHour);

                string dateformat = dateTime.ToString("MMM dd yyyy HH:mm:ss");

                if (getTimeZone.BaseUtcOffsetInHour > 0)
                {
                    dateformat = dateformat + $" GMT +{getTimeZone.BaseUtcOffsetInHour}";
                }
                else
                {
                    dateformat = dateformat + $" GMT {getTimeZone.BaseUtcOffsetInHour}";
                }

                var priority = await _userProfileService.GetCustomFieldByFieldName(
                    conversation.UserProfile,
                    "Priority");

                var emailTemplate = await _appDbContext.CoreEmailNotificationTemplates
                    .OrderByDescending(x => x.Id)
                    .FirstOrDefaultAsync(x => x.NotificationType == NotificationType.NewMessage);

                var plainTextContent = $"User: {conversation.UserProfile.FirstName}/n" +
                                       $"Status: {conversation.Status.ToUpperInvariant()}/n" +
                                       $"Assignee: {assignee.Assignee?.Identity?.DisplayName}/n" +
                                       $"Message: {messageContent}";

                var htmlContent = emailTemplate.EmailTemplate
                    .Replace("[Company_Name]", companyInfo.CompanyName)
                    .Replace("[Customer]", conversation.UserProfile.FirstName)
                    .Replace("[Status]", conversation.Status.ToUpperInvariant())
                    .Replace(
                        "[Assignee]",
                        assignee.Assignee != null ?
                            assignee.Assignee?.Identity?.DisplayName :
                            companyInfo.CompanyName)
                    .Replace("[Message_Content]", messageContent)
                    .Replace("[Channel]", ConversationHelper.GetChannelName(conversationMessage.Channel))
                    .Replace("[Priority]", (priority != null) ? priority.Value : "-")
                    .Replace("[Datetime]", dateformat)
                    .Replace("[Conversation_URL]", $"{_appDomainName}/inbox/all/{conversation.Id}");

                var msg = MailHelper.CreateSingleEmailToMultipleRecipients(
                    from,
                    recipients,
                    subject,
                    plainTextContent,
                    htmlContent);

                var response = await client.SendEmailAsync(msg);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, ex.Message);
            }
        }

        public async Task NewNoteNotification(Conversation conversation, ConversationMessage conversationMessage)
        {
            try
            {
                var coreEmailConfig = await _appDbContext.CoreEmailConfigs.FirstOrDefaultAsync();
                var client = new SendGridClient(coreEmailConfig.SendGridKey);
                var from = new EmailAddress(coreEmailConfig.Email, coreEmailConfig.SenderName);

                conversation = await _appDbContext.Conversations
                    .Include(x => x.Assignee.Identity)
                    .Include(x => x.Assignee.NotificationSetting)
                    .Include(x => x.UserProfile)
                    .FirstOrDefaultAsync(x => x.Id == conversation.Id);

                var recipients = new List<EmailAddress>();

                if (conversationMessage.MessageAssignee != null)
                {
                    if (conversation.Assignee.NotificationSetting?.EmailNotificationNewMessages == false)
                    {
                        return;
                    }

                    recipients.Add(new EmailAddress(conversationMessage.MessageAssignee.Identity.Email));
                }
                else
                {
                    return;

                    // var staffs = _appDbContext.UserRoleStaffs.Where(x => x.CompanyId == conversation.CompanyId).Include(x => x.Identity);
                    // foreach (var staff in staffs)
                    // {
                    //    recipients.Add(new EmailAddress(staff.Identity.Email));
                    // }
                }

                var subject =
                    $"[{coreEmailConfig.SenderName}] Your Teammate has tagged you in a Conversation with {conversation.UserProfile.FirstName}";

                var messageContent = string.Empty;
                if (conversationMessage.MessageContent != null)
                {
                    if (conversationMessage.TranslationResults != null)
                    {
                        messageContent = conversationMessage.TranslationResults.FirstOrDefault().translations
                            .FirstOrDefault().text;
                    }
                    else
                    {
                        messageContent = conversationMessage.MessageContent;
                    }
                }

                // Get companyInfo
                var companyInfo = await _appDbContext.CompanyCompanies
                    .Where(x => x.Id == conversation.CompanyId)
                    .Select(
                        x => new
                        {
                            x.TimeZoneInfoId, x.CompanyName
                        })
                    .AsNoTracking()
                    .FirstOrDefaultAsync();

                var getTimeZone = TimeZoneHelper.GetTimeZoneById(companyInfo.TimeZoneInfoId);
                DateTime dateTime = conversationMessage.CreatedAt
                    .ToUniversalTime()
                    .AddHours(getTimeZone.BaseUtcOffsetInHour);

                string dateformat = dateTime.ToString("MMM dd yyyy HH:mm:ss");

                if (getTimeZone.BaseUtcOffsetInHour > 0)
                {
                    dateformat = dateformat + $" GMT +{getTimeZone.BaseUtcOffsetInHour}";
                }
                else
                {
                    dateformat = dateformat + $" GMT {getTimeZone.BaseUtcOffsetInHour}";
                }

                var priority = await _userProfileService.GetCustomFieldByFieldName(
                    conversation.UserProfile,
                    "Priority");

                var plainTextContent = $"User: {conversation.UserProfile.FirstName}/n" +
                                       $"Status: {conversation.Status.ToUpperInvariant()}/n" +
                                       $"Assignee: {conversationMessage.MessageAssignee?.Identity?.DisplayName}/n" +
                                       $"Message: {messageContent}";

                var emailTemplate = await _appDbContext.CoreEmailNotificationTemplates
                    .OrderByDescending(x => x.Id)
                    .FirstOrDefaultAsync(x => x.NotificationType == NotificationType.NewNote);

                var htmlContent = emailTemplate.EmailTemplate
                    .Replace("[Company_Name]", companyInfo.CompanyName)
                    .Replace("[Customer]", conversation.UserProfile.FirstName)
                    .Replace("[Sender]", conversationMessage.Sender.DisplayName)
                    .Replace("[TargetedStaff]", conversationMessage.MessageAssignee?.Identity?.DisplayName)
                    .Replace("[Status]", conversation.Status.ToUpperInvariant())
                    .Replace("[Assignee]", conversation.Assignee?.Identity?.DisplayName)
                    .Replace("[Message_Content]", messageContent)
                    .Replace("[Channel]", ConversationHelper.GetChannelName(conversationMessage.Channel))
                    .Replace("[Priority]", (priority != null) ? priority.Value : "-")
                    .Replace("[Datetime]", dateformat)
                    .Replace("[Conversation_URL]", $"{_appDomainName}/inbox/all/{conversation.Id}");

                var msg = MailHelper.CreateSingleEmailToMultipleRecipients(
                    from,
                    recipients,
                    subject,
                    plainTextContent,
                    htmlContent);

                var response = await client.SendEmailAsync(msg);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, ex.Message);
            }
        }

        public async Task SendUnAssigneeNotification(string staffId, string conversationId)
        {
            try
            {
                var conversation = await _appDbContext.Conversations
                    .Include(x => x.UserProfile)
                    .Include(x => x.Assignee)
                    .FirstOrDefaultAsync(x => x.Id == conversationId);

                // Get companyInfo
                var companyInfo = await _appDbContext.CompanyCompanies
                    .Where(x => x.Id == conversation.CompanyId)
                    .Select(
                        x => new
                        {
                            x.TimeZoneInfoId,
                            x.CompanyName
                        })
                    .AsNoTracking()
                    .FirstOrDefaultAsync();

                var actionStaff = await _appDbContext.UserRoleStaffs
                    .Include(x => x.Identity)
                    .FirstOrDefaultAsync(
                        x =>
                            x.CompanyId == conversation.CompanyId &&
                            x.IdentityId == staffId);

                var coreEmailConfig = await _appDbContext.CoreEmailConfigs.FirstOrDefaultAsync();
                var client = new SendGridClient(coreEmailConfig.SendGridKey);
                var from = new EmailAddress(coreEmailConfig.Email, coreEmailConfig.SenderName);

                var recipients = new List<EmailAddress>();

                var staffs = await _appDbContext.UserRoleStaffs
                    .Where(
                        x =>
                            x.CompanyId == conversation.CompanyId &&
                            x.Id != 1)
                    .Include(x => x.Identity)
                    .Include(x => x.NotificationSetting)
                    .ToListAsync();

                foreach (var staff in staffs)
                {
                    if (staff.NotificationSetting?.EmailNotificationConversationUpdates == true)
                    {
                        recipients.Add(new EmailAddress(staff.Identity.Email));
                    }
                }

                var conversationMessage = await _appDbContext.ConversationMessages
                    .OrderByDescending(x => x.Id)
                    .FirstOrDefaultAsync(
                        x =>
                            x.ConversationId == conversation.Id &&
                            x.Channel != ChannelTypes.Note);

                var getTimeZone = TimeZoneHelper.GetTimeZoneById(companyInfo.TimeZoneInfoId);
                DateTime dateTime = conversationMessage.CreatedAt
                    .ToUniversalTime()
                    .AddHours(getTimeZone.BaseUtcOffsetInHour);

                string dateformat = dateTime.ToString("MMM dd yyyy HH:mm:ss");

                if (getTimeZone.BaseUtcOffsetInHour > 0)
                {
                    dateformat = dateformat + $" GMT +{getTimeZone.BaseUtcOffsetInHour}";
                }
                else
                {
                    dateformat = dateformat + $" GMT {getTimeZone.BaseUtcOffsetInHour}";
                }

                var priority = await _userProfileService.GetCustomFieldByFieldName(
                    conversation.UserProfile,
                    "Priority");

                var subject =
                    $"[{coreEmailConfig.SenderName}] Un-assignment - Conversation from {ConversationHelper.GetChannelName(conversationMessage.Channel)} by {conversation.UserProfile.FirstName} is assigned by {actionStaff.Identity.DisplayName}";

                var plainTextContent = $"User: {conversation.UserProfile.FirstName}/n" +
                                       $"Status: {conversation.Status.ToUpperInvariant()}/n" +
                                       $"Assignee: {conversation.Assignee?.Identity?.DisplayName}/n";

                var emailTemplate = await _appDbContext.CoreEmailNotificationTemplates
                    .OrderByDescending(x => x.Id)
                    .FirstOrDefaultAsync(x => x.NotificationType == NotificationType.UnAssigneConversation);

                var htmlContent = emailTemplate.EmailTemplate
                    .Replace("[Company_Name]", companyInfo.CompanyName)
                    .Replace("[Customer]", conversation.UserProfile.FirstName)
                    .Replace("[Status]", conversation.Status.ToUpperInvariant())
                    .Replace("[StaffName]", actionStaff.Identity.DisplayName)
                    .Replace("[Assignee]", "-")
                    .Replace("[Channel]", ConversationHelper.GetChannelName(conversationMessage.Channel))
                    .Replace("[Priority]", (priority != null) ? priority.Value : "-")
                    .Replace("[Datetime]", dateformat)
                    .Replace("[Conversation_URL]", $"{_appDomainName}/inbox/all/{conversation.Id}");

                var msg = MailHelper.CreateSingleEmailToMultipleRecipients(
                    from,
                    recipients,
                    subject,
                    plainTextContent,
                    htmlContent);

                var response = await client.SendEmailAsync(msg);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, ex.Message);
            }
        }

        public async Task ResetPassword(ApplicationUser applicationUser, string resetCode)
        {
            try
            {
                var coreEmailConfig = await _appDbContext.CoreEmailConfigs.FirstOrDefaultAsync();
                var client = new SendGridClient(coreEmailConfig.SendGridKey);
                var from = new EmailAddress(coreEmailConfig.Email, coreEmailConfig.SenderName);

                var recipients = new List<EmailAddress>()
                {
                    new EmailAddress(applicationUser.Email, applicationUser.DisplayName)
                };

                var appDomainName = _configuration.GetValue<String>("Values:AppDomainName");

                var subject = $"[{coreEmailConfig.SenderName}] Reset your password";

                var resetTemplate = await _appDbContext.CoreEmailNotificationTemplates
                    .FirstOrDefaultAsync(x => x.NotificationType == NotificationType.ResetPassword);

                var plainTextContent = $"Resetting your password.";
                var htmlContent = resetTemplate.EmailTemplate
                    .Replace("[Customer]", applicationUser.DisplayName)
                    .Replace(
                        "[Rest_Password_URL]",
                        $"{appDomainName}/reset/password?userId={applicationUser.Id}&code={System.Web.HttpUtility.UrlEncode(resetCode)}");

                var msg = MailHelper.CreateSingleEmailToMultipleRecipients(
                    from,
                    recipients,
                    subject,
                    plainTextContent,
                    htmlContent);

                var response = await client.SendEmailAsync(msg);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, ex.Message);
            }
        }

        public async Task SendInvitationEmail(ApplicationUser applicationUser, string resetCode, Staff staff, string? tenanthubUserId=null)
        {
            try
            {
                var coreEmailConfig = await _appDbContext.CoreEmailConfigs.FirstOrDefaultAsync();
                var client = new SendGridClient(coreEmailConfig.SendGridKey);
                var from = new EmailAddress(coreEmailConfig.Email, coreEmailConfig.SenderName);
                var serverLocation = _configuration["SF_ENVIRONMENT"];

                var recipients = new List<EmailAddress>()
                {
                    new EmailAddress(applicationUser.Email, applicationUser.DisplayName)
                };

                var appDomainName = _configuration.GetValue<String>("Values:AppDomainNameV1");

                var subject = $"[{coreEmailConfig.SenderName}] Invite you to join {staff.Company.CompanyName}";

                var resetTemplate = await _appDbContext.CoreEmailNotificationTemplates
                    .OrderByDescending(x => x.Id)
                    .FirstOrDefaultAsync(x => x.NotificationType == NotificationType.Invitation);

                var plainTextContent = $"Invite you to join {staff.Company.CompanyName}";
                var htmlContent = resetTemplate.EmailTemplate
                    .Replace("[Customer]", applicationUser.Email)
                    .Replace("[Invite_From]", staff.Identity.DisplayName)
                    .Replace("[Company_Name]", staff.Company.CompanyName)
                    .Replace(
                        "[Accept_Invitation_URL]",
                        $"{appDomainName}/company/Invitation/Accept?userId={applicationUser.Id}&code={System.Web.HttpUtility.UrlEncode(resetCode)}&location={serverLocation}&companyId={staff.CompanyId}&tenanthubUserId={tenanthubUserId}");

                // _logger.LogError($"Resend email: {$"{appDomainName}/company/Invitation/Accept?userId={applicationUser.Id}&code={resetCode}"}");
                var msg = MailHelper.CreateSingleEmailToMultipleRecipients(
                    from,
                    recipients,
                    subject,
                    plainTextContent,
                    htmlContent);

                var response = await client.SendEmailAsync(msg);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, ex.Message);
            }
        }

        public async Task SendEmailVerification(ApplicationUser applicationUser, string verificationCode)
        {
            try
            {
                var coreEmailConfig = await _appDbContext.CoreEmailConfigs.FirstOrDefaultAsync();
                var client = new SendGridClient(coreEmailConfig.SendGridKey);
                var from = new EmailAddress(coreEmailConfig.Email, coreEmailConfig.SenderName);

                var recipients = new List<EmailAddress>()
                {
                    new EmailAddress(applicationUser.Email, applicationUser.DisplayName)
                };

                var appDomainName = _configuration.GetValue<String>("Values:AppDomainName");

                var subject = $"[{coreEmailConfig.SenderName}] Verify your email";

                var resetTemplate = await _appDbContext.CoreEmailNotificationTemplates
                    .OrderByDescending(x => x.Id)
                    .FirstOrDefaultAsync(x => x.NotificationType == NotificationType.EmailVerification);

                var plainTextContent = $"Verify your email.";
                var htmlContent = resetTemplate.EmailTemplate
                    .Replace("[Customer]", applicationUser.DisplayName)
                    .Replace(
                        "[Verify_Account_URL]",
                        $"{appDomainName}/verifyEmail?userId={applicationUser.Id}&code={System.Web.HttpUtility.UrlEncode(verificationCode)}");

                var msg = MailHelper.CreateSingleEmailToMultipleRecipients(
                    from,
                    recipients,
                    subject,
                    plainTextContent,
                    htmlContent);

                var response = await client.SendEmailAsync(msg);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, ex.Message);
            }
        }

        public async Task SendCustomFieldAnalytics(
            string companyId,
            string analyticsEmailNotificationConfigId,
            DateTime dateTime,
            Dictionary<string, byte[]> attachments)
        {
            try
            {
                var emailTemplate = await _appDbContext.CoreEmailNotificationTemplates
                    .AsNoTracking()
                    .Where(x => x.NotificationType == NotificationType.ExportCustomFieldAnalytics)
                    .Select(x => x.EmailTemplate)
                    .FirstOrDefaultAsync();

                if (emailTemplate == null)
                {
                    _logger.LogError(
                        "[SendCustomFieldAnalyticEmail] - companyId:{CompanyId} no emailTemplate for CustomFieldAnalytic was found. Please check the database",
                        companyId);

                    return;
                }

                var company = await _appDbContext.CompanyCompanies.FirstOrDefaultAsync(x => x.Id == companyId);

                var analyticsEmailNotificationConfig = await _appDbContext.AnalyticsEmailNotificationConfigs
                        .FirstOrDefaultAsync(x => x.Id == analyticsEmailNotificationConfigId);

                var owner = await _appDbContext.UserRoleStaffs
                    .Include(x => x.Identity)
                    .OrderBy(x => x.Order)
                    .ThenBy(x => x.Id)
                    .FirstOrDefaultAsync(
                        x =>
                            x.CompanyId == companyId &&
                            x.Id != 1);

                // var subject = $"SleekFlow Conversation Report - {YYYY-MM-DD}"; //TODO: email contents ot be fixed
                var subject = $"SleekFlow Conversation Report - {dateTime:yyyy-MM-dd}";
                var plainTextContent = subject;
                var htmlContent = emailTemplate;
                var coreEmailConfig = await _appDbContext.CoreEmailConfigs.FirstOrDefaultAsync();
                var recipients = new List<EmailAddress>();

                if (company != null
                    && analyticsEmailNotificationConfig != null
                    && owner != null)
                {
                    foreach (var emailAddress in analyticsEmailNotificationConfig.EmailAddresses)
                    {
                        recipients.Add(new EmailAddress(emailAddress, company.CompanyName));
                    }
                }

                var client = new SendGridClient(apiKey: coreEmailConfig?.SendGridKey);
                var from = new EmailAddress(email: coreEmailConfig?.Email, name: coreEmailConfig?.SenderName);
                var to = new List<EmailAddress>();

                var msg = MailHelper.CreateSingleEmailToMultipleRecipients(
                    from,
                    recipients,
                    subject,
                    plainTextContent,
                    htmlContent);

                long attachmentSizeCount = 0;

                foreach (var file in attachments)
                {
                    attachmentSizeCount += file.Value.LongLength;

                    // 23000000MB is the current system email attachment Limit
                    if (attachmentSizeCount >= 23000000)
                    {
                        _logger.LogInformation(
                            "[SendCustomFieldAnalyticEmail] - companyId:{CompanyId} - current size of the email attachments: {AttachmentSizeCount}",
                            companyId,
                            attachmentSizeCount);

                        _logger.LogError(
                            "[SendCustomFieldAnalyticEmail] - companyId:{CompanyId} Fail to add the attachment - {FileKey}" +
                            " size:{FileValueLongLength}, since the attachments exceed the size limit for attachments 23000000",
                            companyId,
                            file.Key,
                            file.Value.LongLength);

                        attachmentSizeCount -= file.Value.LongLength;
                    }
                    else
                    {
                        using (var fileStream = new MemoryStream(buffer: file.Value))
                        {
                            await msg.AddAttachmentAsync(filename: file.Key, contentStream: fileStream);
                        }
                    }
                }

                var response = await client.SendEmailAsync(msg: msg);

                if (response.IsSuccessStatusCode)
                {
                    _logger.LogInformation(
                        "[SendCustomFieldAnalyticEmail] - companyId:{CompanyId} the backup email is sent successfully",
                        companyId);
                }
                else
                {
                    // chatHistoryBackupConfig.FailToSendAt = DateTime.UtcNow;
                    await _appDbContext.SaveChangesAsync();

                    // If you see a status code 413
                    // A 413 HTTP error code occurs when the size of a client's request exceeds the server's file size limit.
                    _logger.LogError(
                        "[SendCustomFieldAnalyticEmail] - companyId:{CompanyId} fail to send the email. response:{JsonResponse}",
                        companyId,
                        response.ToJson());
                }

                _logger.LogInformation(
                    "[SendCustomFieldAnalyticEmail] - companyId:{CompanyId} - current size of the email attachments: {AttachmentSizeCount}",
                    companyId,
                    attachmentSizeCount);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, ex.Message);
            }
        }

        // public async Task CannelSubscription(Staff staff)
        // {
        //    try
        //    {
        //        var coreEmailConfig = await _appDbContext.CoreEmailConfigs.FirstOrDefaultAsync();
        //        var client = new SendGridClient(coreEmailConfig.SendGridKey);
        //        var from = new EmailAddress(coreEmailConfig.Email, coreEmailConfig.SenderName);

        // var recipients = new List<EmailAddress>() {
        //            new EmailAddress(staff.Identity.Email, staff.Identity.DisplayName),
        //            new EmailAddress("<EMAIL>"),
        //            //new EmailAddress("<EMAIL>"),
        //        };

        // var appDomainName = _configuration.GetValue<String>("Values:AppDomainName");

        // var subject = $"[{coreEmailConfig.SenderName}] Request to cancel sleekflow";

        // var LastbillRecord = await _appDbContext.CompanyBillRecords.Where(x => x.CompanyId == staff.CompanyId).OrderByDescending(x => x.created).FirstOrDefaultAsync();

        // var resetTemplate = await _appDbContext.CoreEmailNotificationTemplates.Where(x => x.NotificationType == NotificationType.CannelSubscription).OrderByDescending(x => x.Id).FirstOrDefaultAsync();
        //        var plainTextContent = subject;
        //        var htmlContent = resetTemplate.EmailTemplate
        //                         .Replace("[Customer]", staff.Identity.DisplayName)
        //                         .Replace("[Company_Name]", staff.Company.CompanyName)
        //                         .Replace("[Date_of_Expiry]", LastbillRecord.PeriodEnd.ToUniversalTime().ToString("d"));

        // var msg = MailHelper.CreateSingleEmailToMultipleRecipients(from, recipients, subject, plainTextContent, htmlContent);
        //        var response = await client.SendEmailAsync(msg);
        //    }
        //    catch (Exception ex)
        //    {
        //        _logger.LogError(ex, ex.Message);
        //    }
        // }
        public async Task ImportCompleted(long importContactHistoryId, List<ImportFailedRecord> failedRecords)
        {
            try
            {
                var importContactHistory = await _appDbContext.CompanyImportContactHistories
                    .Include(x => x.ImportedFrom.Identity)
                    .FirstOrDefaultAsync(x => x.Id == importContactHistoryId);

                var coreEmailConfig = await _appDbContext.CoreEmailConfigs.FirstOrDefaultAsync();
                var client = new SendGridClient(coreEmailConfig.SendGridKey);
                var from = new EmailAddress(coreEmailConfig.Email, coreEmailConfig.SenderName);

                var recipients = new List<EmailAddress>()
                {
                    new EmailAddress(
                        importContactHistory.ImportedFrom.Identity.Email,
                        importContactHistory.ImportedFrom.Identity.DisplayName),

                    // new EmailAddress("<EMAIL>"),
                    // new EmailAddress("<EMAIL>"),
                };

                var failed = string.Empty;
                if (failedRecords.Any())
                {
                    foreach (var failedRecord in failedRecords)
                    {
                        var errorRow =
                            $"<p style=\"color:red\">Row: {failedRecord.Row + 1}, Name: {failedRecord.Name}, Phone Number: {failedRecord.PhoneNumber}, Email: {failedRecord.Email}, Reason: {failedRecord.ErrorMessage}</p>";
                        failed += errorRow;
                    }
                }

                var subject =
                    $"[{coreEmailConfig.SenderName}] Your contacts list: [{importContactHistory.ImportName}] are imported successfully";

                var resetTemplate = await _appDbContext.CoreEmailNotificationTemplates
                    .OrderByDescending(x => x.Id)
                    .FirstOrDefaultAsync(x => x.NotificationType == NotificationType.ImportCompleted);

                var plainTextContent = subject;

                var htmlContent = resetTemplate.EmailTemplate
                    .Replace("[Customer]", importContactHistory.ImportedFrom.Identity.DisplayName)
                    .Replace("[Import_Name]", importContactHistory.ImportName)
                    .Replace("[Imported_Name]", importContactHistory.ImportName)
                    .Replace("[Imported]", importContactHistory.ImportedCount.ToString())
                    .Replace("[Updated]", importContactHistory.UpdatedCount.ToString())
                    .Replace("[Failed]", importContactHistory.FailedCount.ToString() + failed);

                var msg = MailHelper.CreateSingleEmailToMultipleRecipients(
                    from,
                    recipients,
                    subject,
                    plainTextContent,
                    htmlContent);

                var response = await client.SendEmailAsync(msg);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, ex.Message);
            }
        }

        public async Task BulkImportCompleted(
            string email,
            string displayName,
            string importName,
            int importedCount,
            int failedCount)
        {
            try
            {
                var coreEmailConfig = await _appDbContext.CoreEmailConfigs.FirstOrDefaultAsync();
                var client = new SendGridClient(coreEmailConfig.SendGridKey);
                var from = new EmailAddress(coreEmailConfig.Email, coreEmailConfig.SenderName);

                var recipients = new List<EmailAddress>()
                {
                    new EmailAddress(
                        email,
                        displayName),

                    // new EmailAddress("<EMAIL>"),
                    // new EmailAddress("<EMAIL>"),
                };

                var subject =
                    $"[{coreEmailConfig.SenderName}] Your express import is completed.";

                var resetTemplate = await _appDbContext.CoreEmailNotificationTemplates
                    .OrderByDescending(x => x.Id)
                    .FirstOrDefaultAsync(x => x.NotificationType == NotificationType.ImportCompleted);

                var plainTextContent = subject;

                var htmlContent = resetTemplate.EmailTemplate
                    .Replace("[Customer]", displayName)
                    .Replace("[Import_Name]", importName ?? "Individual Contacts")
                    .Replace("[Imported_Name]", importName ?? "Individual Contacts")
                    .Replace("[Imported]", importedCount.ToString())
                    .Replace("[Updated]", "0")
                    .Replace("[Failed]", failedCount.ToString());

                var msg = MailHelper.CreateSingleEmailToMultipleRecipients(
                    from,
                    recipients,
                    subject,
                    plainTextContent,
                    htmlContent);

                var response = await client.SendEmailAsync(msg);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, ex.Message);
            }
        }

        public async Task SendInvoiceToCustomer(
            string staffId,
            long? billRecordId,
            NotificationType notificationType = NotificationType.SendInvoiceToCustomer)
        {
            try
            {
                var billRecord = await _appDbContext.CompanyBillRecords
                    .Include(x => x.SubscriptionPlan)
                    .FirstOrDefaultAsync(x => x.Id == billRecordId);

                var staff = await _appDbContext.UserRoleStaffs
                    .Include(x => x.Identity)
                    .Include(x => x.Company)
                    .FirstOrDefaultAsync(
                        x =>
                            x.CompanyId == billRecord.CompanyId &&
                            x.IdentityId == staffId);

                var coreEmailConfig = await _appDbContext.CoreEmailConfigs.FirstOrDefaultAsync();
                var client = new SendGridClient(coreEmailConfig.SendGridKey);
                var from = new EmailAddress(coreEmailConfig.Email, coreEmailConfig.SenderName);

                var recipients = new List<EmailAddress>()
                {
                    new EmailAddress(staff.Identity.Email, staff.Identity.DisplayName),

                    // new EmailAddress("<EMAIL>"),
                    // new EmailAddress("<EMAIL>"),
                };

                if (notificationType == NotificationType.ConnectedWhatsapp ||
                    notificationType == NotificationType.ChangePlan ||
                    notificationType == NotificationType.CancelSubscription ||
                    notificationType == NotificationType.CannelWhatsApp)
                {
                    recipients.Add(new EmailAddress("<EMAIL>"));
                }

                var appDomainName = _configuration.GetValue<String>("Values:AppDomainName");

                var dateTime = DateTime.UtcNow.ToString("MMM-yyyy");

                var resetTemplate = await _appDbContext.CoreEmailNotificationTemplates
                    .OrderByDescending(x => x.Id)
                    .FirstOrDefaultAsync(x => x.NotificationType == notificationType);

                var subject =
                    $"[{coreEmailConfig.SenderName}] Your {billRecord.SubscriptionPlan.SubscriptionName} invoice for {dateTime} is ready";

                if (notificationType == NotificationType.ChangePlan)
                {
                    subject =
                        $"[{coreEmailConfig.SenderName}] Your Plan has now been updated to {billRecord.SubscriptionPlan.SubscriptionName}";
                }

                if (notificationType == NotificationType.CancelSubscription)
                {
                    subject = $"[{coreEmailConfig.SenderName}] SleekFlow Subscription Cancellation";
                }

                if (notificationType == NotificationType.ConnectedWhatsapp)
                {
                    subject = $"[{coreEmailConfig.SenderName}] WhatsApp Successfully Connected";
                }

                if (notificationType == NotificationType.CannelWhatsApp)
                {
                    subject = $"[{coreEmailConfig.SenderName}] WhatsApp Subscription Cancellation";
                }

                if (notificationType == NotificationType.drip_planupdate_en)
                {
                    subject = resetTemplate.EmailSubject;
                }

                var plainTextContent = subject;
                var htmlContent = resetTemplate.EmailTemplate
                    .Replace("[Customer]", staff.Identity.DisplayName)
                    .Replace("[Company_Name]", staff.Company?.CompanyName)
                    .Replace("[Invoice_URL]", billRecord?.invoice_pdf);

                try
                {
                    htmlContent = htmlContent
                        .Replace(
                            "[Date_of_Expiry]",
                            billRecord?.PeriodEnd.ToString("dd-MM-yyyy HH:mm:ss") + " UTC")
                        .Replace("[Plan_Name]", billRecord?.SubscriptionPlan?.SubscriptionName)
                        .Replace(
                            "[Number_of_Campaign_Messages]",
                            billRecord?.SubscriptionPlan?.MaximumCampaignSent + string.Empty)
                        .Replace("[Number_of_agents]", billRecord?.SubscriptionPlan?.IncludedAgents + string.Empty);
                }
                catch (Exception ex)
                {
                    _logger.LogInformation("bill record error" + ex.ToString());
                }

                var msg = MailHelper.CreateSingleEmailToMultipleRecipients(
                    from,
                    recipients,
                    subject,
                    plainTextContent,
                    htmlContent);

                var response = await client.SendEmailAsync(msg);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, ex.Message);
            }
        }
        /*
        public async Task ConnectedWhatsapp(string staffId)
        {
            try
            {
                var staff = await _appDbContext.UserRoleStaffs.Where(x => x.IdentityId == staffId).Include(x => x.Identity).Include(x => x.Company).FirstOrDefaultAsync();

                var coreEmailConfig = await _appDbContext.CoreEmailConfigs.FirstOrDefaultAsync();
                var client = new SendGridClient(coreEmailConfig.SendGridKey);
                var from = new EmailAddress(coreEmailConfig.Email, coreEmailConfig.SenderName);

                var recipients = new List<EmailAddress>() {
                    new EmailAddress(staff.Identity.Email, staff.Identity.DisplayName),
                    new EmailAddress("<EMAIL>"),
                    //new EmailAddress("<EMAIL>"),
                };

                var appDomainName = _configuration.GetValue<String>("Values:AppDomainName");

                var subject = $"[{coreEmailConfig.SenderName}] Cannel WhatsApp subscription";

                var resetTemplate = await _appDbContext.CoreEmailNotificationTemplates.Where(x => x.NotificationType == NotificationType.CannelWhatsApp).OrderByDescending(x => x.Id).FirstOrDefaultAsync();
                var plainTextContent = subject;
                var htmlContent = resetTemplate.EmailTemplate
                                 .Replace("[Customer]", staff.Identity.DisplayName)
                                 .Replace("[Company_Name]", staff.Company.CompanyName);

                var msg = MailHelper.CreateSingleEmailToMultipleRecipients(from, recipients, subject, plainTextContent, htmlContent);
                var response = await client.SendEmailAsync(msg);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, ex.Message);
            }
        } */
        /*
        public async Task CannelWhatsAppSubscription(string staffId)
        {
            try
            {
                var staff = await _appDbContext.UserRoleStaffs.Where(x => x.IdentityId == staffId).Include(x => x.Identity).Include(x => x.Company).FirstOrDefaultAsync();
                var bill = await _appDbContext.CompanyBillRecords.Where(x => x.CompanyId == staff.CompanyId && x.SubscriptionPlanId == "sleekflow_whatsapp").OrderByDescending(x => x.Id).FirstOrDefaultAsync();

                var coreEmailConfig = await _appDbContext.CoreEmailConfigs.FirstOrDefaultAsync();
                var client = new SendGridClient(coreEmailConfig.SendGridKey);
                var from = new EmailAddress(coreEmailConfig.Email, coreEmailConfig.SenderName);

                var recipients = new List<EmailAddress>() {
                    new EmailAddress(staff.Identity.Email, staff.Identity.DisplayName),
                    new EmailAddress("<EMAIL>"),
                    //new EmailAddress("<EMAIL>"),
                };

                var appDomainName = _configuration.GetValue<String>("Values:AppDomainName");

                var subject = $"[{coreEmailConfig.SenderName}] You have successfully cancelled your WhatsApp Subscription";

                var resetTemplate = await _appDbContext.CoreEmailNotificationTemplates.Where(x => x.NotificationType == NotificationType.CannelWhatsApp).OrderByDescending(x => x.Id).FirstOrDefaultAsync();
                var plainTextContent = subject;
                var htmlContent = resetTemplate.EmailTemplate
                                 .Replace("[Customer]", staff.Identity.DisplayName)
                                 .Replace("[Company_Name]", staff.Company.CompanyName);

                try
                {
                    htmlContent = htmlContent.Replace("[Date_of_Expiry_WhatsApp]", bill?.PeriodEnd.ToString());
                }
                catch (Exception ex)
                {
                    _logger.LogInformation("bill record error" + ex.ToString());
                }

                var msg = MailHelper.CreateSingleEmailToMultipleRecipients(from, recipients, subject, plainTextContent, htmlContent);
                var response = await client.SendEmailAsync(msg);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, ex.Message);
            }
        } */

        public async Task WhatsAppTrialEnd(string companyId)
        {
            try
            {
                var coreEmailConfig = await _appDbContext.CoreEmailConfigs.FirstOrDefaultAsync();
                var client = new SendGridClient(coreEmailConfig.SendGridKey);
                var from = new EmailAddress(coreEmailConfig.Email, coreEmailConfig.SenderName);

                var recipients = new List<EmailAddress>();

                // recipients.Add(new EmailAddress("<EMAIL>"));
                var staffs = await _appDbContext.UserRoleStaffs
                    .Where(
                        x =>
                            x.CompanyId == companyId &&
                            x.Id != 1)
                    .Include(x => x.Identity)
                    .ToListAsync();

                var company = await _appDbContext.CompanyCompanies
                    .FirstOrDefaultAsync(x => x.Id == companyId);

                foreach (var staff in staffs)
                {
                    recipients.Add(new EmailAddress(staff.Identity.Email));
                }

                var appDomainName = _configuration.GetValue<string>("Values:AppDomainName");

                var subject = $"[{coreEmailConfig.SenderName}] WhatsApp Trial End";

                var resetTemplate = await _appDbContext.CoreEmailNotificationTemplates
                    .OrderByDescending(x => x.Id)
                    .FirstOrDefaultAsync(x => x.NotificationType == NotificationType.TrialEndWhatsApp);

                var plainTextContent = subject;

                var htmlContent = resetTemplate.EmailTemplate
                    .Replace(
                        "[Customer]",
                        string.Join(", ", staffs.Select(x => x.Identity.DisplayName).ToArray()))
                    .Replace("[Company_Name]", company.CompanyName);

                var msg = MailHelper.CreateSingleEmailToMultipleRecipients(
                    from,
                    recipients,
                    subject,
                    plainTextContent,
                    htmlContent);

                var response = await client.SendEmailAsync(msg);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, ex.Message);
            }
        }

        public async Task SendWelcomeOnBoardNotification(
            string companyId,
            NotificationType notificationType = NotificationType.WelcomeToSleekFlow,
            string PlanId = null,
            string leadSource = null)
        {
            try
            {
                var company = await _appDbContext.CompanyCompanies
                    .Include(x => x.BillRecords)
                        .ThenInclude(x => x.SubscriptionPlan)
                    .FirstOrDefaultAsync(x => x.Id == companyId);

                var coreEmailConfig = await _appDbContext.CoreEmailConfigs.FirstOrDefaultAsync();
                var client = new SendGridClient(coreEmailConfig.SendGridKey);
                var from = new EmailAddress(coreEmailConfig.Email, coreEmailConfig.SenderName);

                var staffs = await _appDbContext.UserRoleStaffs
                    .Where(
                        x =>
                            x.CompanyId == company.Id &&
                            x.Id != 1)
                    .Include(x => x.Identity)
                    .OrderBy(x => x.Order)
                    .ThenBy(x => x.Id)
                    .ToListAsync();

                if (notificationType == NotificationType.WelcomeToSleekFlow)
                {
                    // Henson Message
                    var recipients = new List<EmailAddress>()
                    {
                        new EmailAddress("<EMAIL>")
                    };

                    var appDomainName = _configuration.GetValue<String>("Values:AppDomainName");

                    var subject =
                        $"[{coreEmailConfig.SenderName}] New Company [{company.CompanyName}] Joined Sleekflow!";

                    try
                    {
                        var resetTemplate = await _appDbContext.CoreEmailNotificationTemplates
                            .OrderByDescending(x => x.Id)
                            .FirstOrDefaultAsync(x => x.NotificationType == NotificationType.NewCompanyCreatedToSupport);

                        var plainTextContent = subject;
                        var htmlContent = resetTemplate.EmailTemplate
                            .Replace("[Customer]", staffs.FirstOrDefault().Identity.DisplayName)
                            .Replace(
                                "[Registered_Email]",
                                string.Join(", ", staffs.Select(x => x.Identity.Email).ToArray()))
                            .Replace("[Company_Name]", company.CompanyName)
                            .Replace(
                                "[Phone_Number]",
                                string.Join(", ", staffs.Select(x => x.Identity.PhoneNumber).ToArray()))
                            .Replace("[Plan_Id]", $"{PlanId}, Lead Source: {leadSource}");

                        var supportNotification = MailHelper.CreateSingleEmailToMultipleRecipients(
                            from,
                            recipients,
                            subject,
                            plainTextContent,
                            htmlContent);

                        await client.SendEmailAsync(supportNotification);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, ex.Message);
                    }

                    // Send Welcome whatsapp
                    // wait SendWhatsapp(notificationType, staffs);
                }

                var wel_subject = $"Welcome to SleekFlow👋";
                from = new EmailAddress("<EMAIL>", "Minnie Au");

                var wel_Template = await _appDbContext.CoreEmailNotificationTemplates
                    .OrderByDescending(x => x.Id)
                    .FirstOrDefaultAsync(x => x.NotificationType == NotificationType.WelcomeToSleekFlow);

                var usage = await _companyUsageService.GetCompanyUsage(companyId);

                switch (notificationType)
                {
                    case NotificationType.GetStartedWithTeamInbox:
                        if (usage.totalAgents > 1)
                        {
                            return;
                        }

                        // from = new EmailAddress("<EMAIL>", "Ronald Yu");
                        wel_subject = $"Sell better with your team on SleekFlow!";
                        wel_Template = await _appDbContext.CoreEmailNotificationTemplates
                            .OrderByDescending(x => x.Id)
                            .FirstOrDefaultAsync(x => x.NotificationType == notificationType);

                        break;
                    case NotificationType.WebsiteMessenger:
                        wel_subject = $"Looking to convert more leads from your website?";
                        wel_Template = await _appDbContext.CoreEmailNotificationTemplates
                            .OrderByDescending(x => x.Id)
                            .FirstOrDefaultAsync(x => x.NotificationType == notificationType);

                        break;
                    case NotificationType.CampaignMessages:
                        wel_subject = $"Convert more customers with campaign messages";
                        wel_Template = await _appDbContext.CoreEmailNotificationTemplates
                            .OrderByDescending(x => x.Id)
                            .FirstOrDefaultAsync(x => x.NotificationType == notificationType);

                        break;
                    case NotificationType.ReEngage:
                        var messageCount = await _appDbContext.ConversationMessages
                            .Where(x => x.CompanyId == company.Id)
                            .CountAsync();

                        if (messageCount > 0)
                        {
                            return;
                        }

                        // await SendWhatsapp(notificationType, staffs);
                        from = new EmailAddress("<EMAIL>", "Ronald Yu");
                        wel_subject = $"Anything we can help, {staffs.FirstOrDefault().Identity.FirstName}?";
                        wel_Template = await _appDbContext.CoreEmailNotificationTemplates
                            .OrderByDescending(x => x.Id)
                            .FirstOrDefaultAsync(x => x.NotificationType == notificationType);

                        break;
                    case NotificationType.ExitBeforePayment:
                        // var lastValidPayment = await _appDbContext.CompanyBillRecords.Where(x => x.CompanyId == company.Id && !string.IsNullOrEmpty(x.customerId) && ValidSubscriptionPlan.SubscriptionPlan.Contains(x.SubscriptionPlanId)).OrderByDescending(x => x.created).FirstOrDefaultAsync();

                        // if (lastValidPayment != null && ValidSubscriptionPlan.PaidPlan.Contains(lastValidPayment.SubscriptionPlanId))
                        //    return;

                        // wel_subject = $"What happened {string.Join(", ", staffs.Select(x => x.Identity.DisplayName).ToArray())}?";
                        // wel_Template = await _appDbContext.CoreEmailNotificationTemplates.Where(x => x.NotificationType == notificationType).OrderByDescending(x => x.Id).FirstOrDefaultAsync();
                        return;
                    case NotificationType.NoChannelAdded:
                        if (usage.totalChannelAdded > 0)
                        {
                            return;
                        }

                        wel_subject = $"Need help adding channels on SleekFlow?";
                        wel_Template = await _appDbContext.CoreEmailNotificationTemplates
                            .OrderByDescending(x => x.Id)
                            .FirstOrDefaultAsync(x => x.NotificationType == notificationType);

                        break;
                    case NotificationType.GoodUsage:
                        if (usage.totalChannelAdded == 0)
                        {
                            return;
                        }

                        if (usage.LastLogin < DateTime.UtcNow.AddDays(-7))
                        {
                            return;
                        }

                        from = new EmailAddress("<EMAIL>", "Crystal Wong");
                        wel_subject =
                            $"How's it going, {string.Join(", ", staffs.Select(x => x.Identity.DisplayName).ToArray())}?";
                        wel_Template = await _appDbContext.CoreEmailNotificationTemplates
                            .OrderByDescending(x => x.Id)
                            .FirstOrDefaultAsync(x => x.NotificationType == notificationType);

                        break;
                    case NotificationType.BadUsage:
                        if (usage.totalChannelAdded > 0 &&
                            usage.LastLogin > DateTime.UtcNow.AddDays(-7))
                        {
                            return;
                        }

                        from = new EmailAddress("<EMAIL>", "Henson Tsai");
                        wel_subject = $"Anything we can help, {staffs.FirstOrDefault().Identity.DisplayName}?";
                        wel_Template = await _appDbContext.CoreEmailNotificationTemplates
                            .OrderByDescending(x => x.Id)
                            .FirstOrDefaultAsync(x => x.NotificationType == notificationType);

                        break;
                    case NotificationType.MidWayPromo:
                        if (usage.totalChannelAdded > 0 &&
                            usage.LastLogin > DateTime.UtcNow.AddDays(-7))
                        {
                            return;
                        }

                        from = new EmailAddress("<EMAIL>", "Crystal Wong");
                        wel_subject = $"[SleekFlow] 10% Off Monthly Subscription Plans!";
                        wel_Template = await _appDbContext.CoreEmailNotificationTemplates
                            .OrderByDescending(x => x.Id)
                            .FirstOrDefaultAsync(x => x.NotificationType == notificationType);

                        return;

                    // break;
                    case NotificationType.FinalPromo:
                        if (usage.totalChannelAdded > 0 && usage.LastLogin > DateTime.UtcNow.AddDays(-7))
                        {
                            return;
                        }

                        from = new EmailAddress("<EMAIL>", "Crystal Wong");
                        wel_subject = $"[SleekFlow] 20% Off Monthly Subscription Plans!";
                        wel_Template = await _appDbContext.CoreEmailNotificationTemplates
                            .OrderByDescending(x => x.Id)
                            .FirstOrDefaultAsync(x => x.NotificationType == notificationType);

                        return;

                    // break;
                    case NotificationType.NoUsageDemo:
                        wel_subject = $"Get to know SleekFlow";
                        wel_Template = await _appDbContext.CoreEmailNotificationTemplates
                            .OrderByDescending(x => x.Id)
                            .FirstOrDefaultAsync(x => x.NotificationType == notificationType);

                        break;
                }

                try
                {
                    var wel_recipients = new List<EmailAddress>();

                    foreach (Staff staff in staffs)
                    {
                        wel_recipients.Add(new EmailAddress(staff.Identity.Email, staff.Identity.DisplayName));
                    }

                    var wel_plainTextContent = wel_subject;
                    var wel_htmlContent = wel_Template.EmailTemplate
                        .Replace("[Customer]", staffs.FirstOrDefault().Identity.DisplayName)
                        .Replace(
                            "[Subcription_name]",
                            company.BillRecords.FirstOrDefault().SubscriptionPlan.SubscriptionName)
                        .Replace("[Company Name]", company.CompanyName)
                        .Replace("[Company_Name]", company.CompanyName);

                    var wel_Notification = MailHelper.CreateSingleEmailToMultipleRecipients(
                        from,
                        wel_recipients,
                        wel_subject,
                        wel_plainTextContent,
                        wel_htmlContent);

                    await client.SendEmailAsync(wel_Notification);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, ex.Message);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, ex.Message);
            }
        }

        public async Task SendDripEmailV2(
            string companyId,
            NotificationType notificationType = NotificationType.signup_welcome_en,
            string PlanId = null,
            string leadSource = null)
        {
            try
            {
                switch (notificationType)
                {
                    case NotificationType.signup_automation_en:
                    case NotificationType.signup_eshop_en:
                    case NotificationType.signup_masked_en:
                    case NotificationType.signup_api_en:
                    case NotificationType.signup_channel_en:
                    case NotificationType.signup_flowbuilder_en:
                        return;
                }

                var company = await _appDbContext.CompanyCompanies
                    .Include(x => x.BillRecords)
                        .ThenInclude(x => x.SubscriptionPlan)
                    .FirstOrDefaultAsync(x => x.Id == companyId);

                var coreEmailConfig = await _appDbContext.CoreEmailConfigs.FirstOrDefaultAsync();
                var client = new SendGridClient(coreEmailConfig.SendGridKey);
                var from = new EmailAddress(coreEmailConfig.Email, coreEmailConfig.SenderName);

                var allStaffs = await _appDbContext.UserRoleStaffs
                    .Where(
                        x =>
                            x.CompanyId == company.Id &&
                            x.Id != 1)
                    .Include(x => x.Identity)
                    .OrderBy(x => x.Order)
                    .ThenBy(x => x.Id)
                    .ToListAsync();

                var sleekFlowCompanyId = _configuration.GetValue<String>("Values:SleekFlowCompanyId");
                var staffs = new List<Staff>();
                foreach (var staff in allStaffs)
                {
                    var unsubedStaff = await _userProfileService.GetUserProfilesIdsByFields(
                        sleekFlowCompanyId,
                        new List<Condition>
                        {
                            new Condition
                            {
                                FieldName = "email",
                                ConditionOperator = SupportedOperator.Equals,
                                Values = new List<string>
                                {
                                    staff.Identity.Email
                                },
                                NextOperator = SupportedNextOperator.And
                            },
                            new Condition
                            {
                                FieldName = "Subscriber",
                                ConditionOperator = SupportedOperator.Equals,
                                Values = new List<string>
                                {
                                    "false"
                                }
                            }
                        });
                    if (unsubedStaff.UserProfileIds?.Count == 0)
                    {
                        staffs.Add(staff);
                    }
                }

                var appDomainName = _configuration.GetValue<String>("Values:AppDomainName");
                var verified_url =
                    $"{appDomainName.Replace("https://", string.Empty)}/twilio/whatsapp/verification?referenceId={companyId}&email={allStaffs.FirstOrDefault()?.Identity.Email}&isverified=true";

                if (notificationType == NotificationType.signup_welcome_en)
                {
                    // Henson Message
                    var recipients = new List<EmailAddress>()
                    {
                        new EmailAddress("<EMAIL>")
                    };

                    var subject =
                        $"[{coreEmailConfig.SenderName}] New Company [{company.CompanyName}] Joined Sleekflow!";

                    try
                    {
                        var resetTemplate = await _appDbContext.CoreEmailNotificationTemplates
                            .OrderByDescending(x => x.Id)
                            .FirstOrDefaultAsync(x => x.NotificationType == NotificationType.NewCompanyCreatedToSupport);

                        var plainTextContent = subject;
                        var htmlContent = resetTemplate.EmailTemplate
                            .Replace("[Customer]", allStaffs.FirstOrDefault().Identity.DisplayName)
                            .Replace(
                                "[Registered_Email]",
                                string.Join(", ", allStaffs.Select(x => x.Identity.Email).ToArray()))
                            .Replace("[Company_Name]", company.CompanyName)
                            .Replace(
                                "[Phone_Number]",
                                string.Join(", ", allStaffs.Select(x => x.Identity.PhoneNumber).ToArray()))
                            .Replace("[Plan_Id]", $"{PlanId}, Lead Source: {leadSource}");

                        var supportNotification = MailHelper.CreateSingleEmailToMultipleRecipients(
                            from,
                            recipients,
                            subject,
                            plainTextContent,
                            htmlContent);

                        await client.SendEmailAsync(supportNotification);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, ex.Message);
                    }

                    // Send Welcome whatsapp
                    // wait SendWhatsapp(notificationType, staffs);
                }

                var wel_subject = $"Welcome to SleekFlow👋";
                from = new EmailAddress("<EMAIL>", "Minnie Au");
                var wel_Template = await _appDbContext.CoreEmailNotificationTemplates
                    .OrderByDescending(x => x.Id)
                    .FirstOrDefaultAsync(x => x.NotificationType == notificationType);

                var wel_recipients = new List<EmailAddress>();

                var usage = await _companyUsageService.GetCompanyUsage(companyId);
                switch (notificationType)
                {
                    case NotificationType.twilio_topup_en:
                        if (!await _appDbContext.CompanyTwilioUsageRecords
                                .AnyAsync(
                                    x =>
                                        x.CompanyId == company.Id &&
                                        x.Balance < 0))
                        {
                            return;
                        }
                        else if (await _appDbContext.CompanyTwilioUsageRecords
                                     .AnyAsync(
                                         x =>
                                             x.CompanyId == company.Id &&
                                             !x.IsVerified))
                        {
                            wel_Template = await _appDbContext.CoreEmailNotificationTemplates
                                .OrderByDescending(x => x.Id)
                                .FirstOrDefaultAsync(x => x.NotificationType == NotificationType.twilio_facebook_verification_en);
                        }

                        from = new EmailAddress("<EMAIL>", "SleekFlow WhatsApp");

                        break;
                    case NotificationType.twilio_facebook_verification_en:
                        if (await _appDbContext.ConfigWhatsAppConfigs
                                .AnyAsync(x => x.CompanyId == company.Id) ||
                            await _appDbContext.CompanyTwilioUsageRecords
                                .AnyAsync(
                                    x =>
                                        x.CompanyId == company.Id &&
                                        x.IsVerified))
                        {
                            return;
                        }

                        from = new EmailAddress("<EMAIL>", "SleekFlow WhatsApp");
                        break;

                    case NotificationType.signup_offical_en:
                        if (await _appDbContext.ConfigWhatsAppConfigs
                                .AnyAsync(x => x.CompanyId == company.Id) ||
                            await _appDbContext.CompanyTwilioUsageRecords
                                .AnyAsync(
                                    x =>
                                        x.CompanyId == company.Id &&
                                        x.IsVerified))
                        {
                            return;
                        }

                        from = new EmailAddress("<EMAIL>", "SleekFlow WhatsApp");

                        break;
                    case NotificationType.twilio_step_to_proceed_en:
                        from = new EmailAddress("<EMAIL>", "SleekFlow WhatsApp");

                        break;

                    case NotificationType.twilio_completed_verification_en:
                        from = new EmailAddress("<EMAIL>", "SleekFlow WhatsApp");
                        wel_recipients.Add(new EmailAddress("<EMAIL>", "SleekFlow WhatsApp"));

                        break;
                }

                try
                {
                    foreach (Staff staff in staffs)
                    {
                        wel_recipients.Add(new EmailAddress(staff.Identity.Email, staff.Identity.DisplayName));
                    }

                    wel_subject = wel_Template.EmailSubject;
                    var wel_plainTextContent = wel_subject;
                    var wel_htmlContent = wel_Template.EmailTemplate
                        .Replace("[Customer]", allStaffs.FirstOrDefault().Identity.DisplayName)
                        .Replace(
                            "[Subcription_name]",
                            company.BillRecords.FirstOrDefault().SubscriptionPlan.SubscriptionName)
                        .Replace("[Company Name]", company.CompanyName)
                        .Replace("[Company_Name]", company.CompanyName)
                        .Replace("[companyname]", company.CompanyName)
                        .Replace("[Verified_URL]", verified_url);

                    var wel_Notification = MailHelper.CreateSingleEmailToMultipleRecipients(
                        from,
                        wel_recipients,
                        wel_subject,
                        wel_plainTextContent,
                        wel_htmlContent);

                    await client.SendEmailAsync(wel_Notification);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, ex.Message);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, ex.Message);
            }
        }

        [AutomaticRetry(
            Attempts = 5,
            DelaysInSeconds = new[]
            {
                60
            })]
        public async Task SendNewCompanyEmailToSlackChannel(string companyId)
        {
            try
            {
                var company = await _appDbContext.CompanyCompanies
                    .AsNoTracking()
                    .Include(x => x.BillRecords)
                    .Include(x => x.CmsCompanyOwner)
                    .Include(x => x.CmsCompanyAdditionalInfo)
                    .Include(x => x.Staffs)
                    .ThenInclude(x => x.Identity)
                    .Where(x => x.Id == companyId)
                    .Select(
                        x => new
                        {
                            CompanyId = x.Id,
                            CompanyName = x.CompanyName,
                            CreatedAt = x.CreatedAt,
                            CmsHubSpotCompanyMaps =
                                _appDbContext.CmsHubSpotCompanyMaps.FirstOrDefault(h => h.CompanyId == companyId),
                            CmsCompanyOwner = x.CmsCompanyOwner,
                            Staffs = x.Staffs,
                            CompanyCountry = x.CompanyCountry,
                            CompanyType = x.CompanyType,
                            CommunicationTools = x.CommunicationTools,
                            CmsCompanyAdditionalInfo = x.CmsCompanyAdditionalInfo
                        })
                    .FirstOrDefaultAsync();

                if (company == null ||
                    company.CmsHubSpotCompanyMaps == null ||
                    company.CmsCompanyOwner == null ||
                    company.CmsCompanyAdditionalInfo?.CompanyTier == null)
                {
                    throw new ArgumentNullException(nameof(companyId), "HubSpot Sync process may not complete.");
                }

                var cmsHubSpotContactOwnerMap = await _appDbContext.CmsHubSpotContactOwnerMaps
                    .FirstAsync(x => x.ContactOwnerId == company.CmsCompanyOwner.Id);

                var slackEmailChannelAddress = InternalSlackSignupEmailChannelHelper.ResolveEmailChannelAddress(
                    cmsHubSpotContactOwnerMap.HubspotTeams,
                    company.CompanyCountry);

                if (!InternalSlackSignupEmailChannelHelper.IsValidEmailChannelAddressAndCompanyTier(
                        slackEmailChannelAddress,
                        company.CmsCompanyAdditionalInfo.CompanyTier))
                {
                    _logger.LogWarning(
                        "Invalid Company Tier for id {CompanyId}",
                        company.CompanyId);
                    return;
                }

                var owner = company.Staffs
                    .OrderBy(s => s.Order)
                    .ThenBy(s => s.Id)
                    .First();

                var subject = $"{company.CompanyName} - {company.CmsCompanyOwner.DisplayName}";

                var optionalCompanyType = company.CompanyType switch
                {
                    CompanyType.Reseller => " (Reseller)",
                    CompanyType.ResellerClient => " (Reseller Client)",
                    _ => string.Empty
                };

                var customFieldResult = await _appDbContext.CompanyCompanies
                    .AsNoTracking()
                    .Where(x => x.Id == companyId)
                    .Select(
                        x => new
                        {
                            CompanyId = x.Id,
                            Industry =
                                x.CompanyCustomFields
                                    .FirstOrDefault(f => f.CompanyId == x.Id && f.FieldName == "Industry").Value,
                            OnlineShopSystem =
                                x.CompanyCustomFields.FirstOrDefault(
                                    f => f.CompanyId == x.Id && f.FieldName == "OnlineShopSystem").Value,
                            CompanyWebsite =
                                x.CompanyCustomFields.FirstOrDefault(
                                    f => f.CompanyId == x.Id && f.FieldName == "CompanyWebsite").Value,
                            CompanySize = x.CompanyCustomFields
                                .FirstOrDefault(f => f.CompanyId == x.Id && f.FieldName == "CompanySize").Value,
                        })
                    .FirstOrDefaultAsync();

                var hubspotContactOwner = await _appDbContext.CmsHubSpotUserContactMaps
                    .AsNoTracking()
                    .FirstOrDefaultAsync(x => x.Email == owner.Identity.Email);

                var emailTemplate = await _appDbContext.CoreEmailNotificationTemplates
                    .Where(x => x.NotificationType == NotificationType.NewCompanyCreatedToSlack)
                    .Select(x => x.EmailTemplate)
                    .FirstOrDefaultAsync();

                var htmlContent = emailTemplate
                        .Replace("[Company_Name]", company.CompanyName + optionalCompanyType)
                        .Replace("[Industry]", customFieldResult?.Industry ?? "N/A")
                        .Replace("[Online_Shop_System]", customFieldResult?.OnlineShopSystem ?? "N/A")
                        .Replace(
                            "[Communication_Tools]",
                            company.CommunicationTools == null
                                ? "N/A"
                                : string.Join(
                                    ",",
                                    company.CommunicationTools.Select(x => x.Humanize(LetterCasing.Title))))
                        .Replace("[Contact_Owner]", company.CmsCompanyOwner.DisplayName)
                        .Replace("[Owner_Name]", owner.Identity.DisplayName)
                        .Replace("[Owner_Email]", owner.Identity.Email)
                        .Replace("[Owner_Phone]", owner.Identity.PhoneNumber)
                        .Replace("[Country]", company.CompanyCountry)
                        .Replace("[Created_At]", company.CreatedAt.AddHours(8).ToString("yyyy/MM/dd HH:mm:ss") + " HKT")
                        .Replace("[Company_ID]", company.CompanyId)
                        .Replace("[HubSpot_Company_ID]", company.CmsHubSpotCompanyMaps.HubSpotCompanyObjectId)
                        .Replace(
                            "[HubSpot_Company_Tier]",
                            company.CmsCompanyAdditionalInfo?.CompanyTier?.Humanize(LetterCasing.Title))
                        .Replace("[Company_Website]", customFieldResult?.CompanyWebsite ?? "N/A")
                        .Replace("[Company_Size]", customFieldResult?.CompanySize ?? "N/A")
                        .Replace("[HubSpot_Contact_ID]", hubspotContactOwner.HubSpotContactObjectId)
                    ;

                var coreEmailConfig = await _appDbContext.CoreEmailConfigs.FirstOrDefaultAsync();
                var client = new SendGridClient(coreEmailConfig.SendGridKey);
                var from = new EmailAddress(coreEmailConfig.Email, coreEmailConfig.SenderName);

                var supportNotification = MailHelper.CreateSingleEmailToMultipleRecipients(
                    from,
                    new List<EmailAddress>
                    {
                        new (slackEmailChannelAddress), new (InternalSlackSignupEmailChannelAddressConst.All)
                    },
                    subject,
                    subject,
                    htmlContent);

                await client.SendEmailAsync(supportNotification);
            }
            catch (ArgumentNullException ex)
            {
                _logger.LogError(ex, ex.Message);
                throw;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, ex.Message);
            }
        }

        [AutomaticRetry(
            Attempts = 1,
            DelaysInSeconds = new[]
            {
                60
            })]
        public async Task SendSystemAlertToSlackChannel(
            string subject,
            string message,
            string category,
            string type = "alert",
            string companyId = null,
            bool prependCompanyInfo = false)
        {
            try
            {
                var prependInfo = string.Empty;
                var additionalInfo = string.Empty;
                if (!string.IsNullOrEmpty(companyId))
                {
                    var company = await _appDbContext.CompanyCompanies
                        .Where(x => x.Id == companyId)
                        .Select(
                            x => new
                            {
                                CompanyId = x.Id, CompanyName = x.CompanyName,
                            })
                        .FirstOrDefaultAsync();

                    if (company != null)
                    {
                        var companyInfo = @$"<div>
                            <span style='color:#0f6593;'><b>Company Info</b></span><br/>
                            <span style='color:#0f103d;'>Company Id: <b>{company.CompanyId}</b></span><br/>
                            <span style='color:#0f103d;'>Company Name: <b>{company.CompanyName}</b></span><br/>
                            <span style='color:#0f103d;'>Open In: <a clicktracking=off href='https://powerflow.sleekflow.io/companies/detail/{company.CompanyId}' target='_blank'>Powerflow</a>
                            </div>";

                        if (prependCompanyInfo)
                        {
                            prependInfo = companyInfo;
                        }
                        else
                        {
                            additionalInfo = companyInfo;
                        }
                    }
                }

                var emailTemplate = await _appDbContext.CoreEmailNotificationTemplates
                    .Where(x => x.NotificationType == NotificationType.SystemAlertToSlack)
                    .Select(x => x.EmailTemplate)
                    .FirstOrDefaultAsync();

                var htmlContent = emailTemplate
                    .Replace("[Alert_Name]", subject)
                    .Replace("[Alert_Message]", message.Replace("[[", "<b>").Replace("]]", "</b>").Replace("\n", "<br/>"))
                    .Replace("[Category]", category)
                    .Replace("[Prepend_Info]", prependInfo)
                    .Replace("[Additional_Info]", additionalInfo)
                    .Replace("[Created_At]", DateTime.UtcNow.AddHours(8).ToString("yyyy/MM/dd HH:mm:ss") + " HKT");

                // Title Color
                switch (type)
                {
                    case "alert":
                        break;
                    case "resolved":
                        htmlContent = htmlContent.Replace("#e55353", "#2eb85c");
                        break;
                    case "notification":
                        htmlContent = htmlContent.Replace("#e55353", "#1a6d98");
                        break;
                }

                var coreEmailConfig = await _appDbContext.CoreEmailConfigs.FirstOrDefaultAsync();
                var client = new SendGridClient(coreEmailConfig.SendGridKey);
                var from = new EmailAddress(coreEmailConfig.Email, coreEmailConfig.SenderName);

                var channelEmailAddress = category switch
                {
                    ChannelTypes.WhatsappCloudApi =>
                        "<EMAIL>",
                    "stripe" => "<EMAIL>",
                    "partner-stack" => "<EMAIL>",
                    _ => "<EMAIL>"
                };

                var supportNotification = MailHelper.CreateSingleEmailToMultipleRecipients(
                    from,
                    new List<EmailAddress>
                    {
                        new (channelEmailAddress)
                    },
                    subject,
                    subject,
                    htmlContent);

                await client.SendEmailAsync(supportNotification);
            }
            catch (ArgumentNullException ex)
            {
                _logger.LogError(ex, ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, ex.Message);
            }
        }

        public async Task SendAccountDeletionEmail(string companyId, NotificationType notificationType)
        {
            try
            {
                var coreEmailConfig = await _appDbContext.CoreEmailConfigs.FirstOrDefaultAsync();
                var client = new SendGridClient(coreEmailConfig.SendGridKey);
                var from = new EmailAddress(coreEmailConfig.Email, coreEmailConfig.SenderName);

                var recipients = new List<EmailAddress>()
                {
                    // new EmailAddress("<EMAIL>")
                };

                var owner = await _appDbContext.UserRoleStaffs
                    .Include(x => x.Identity)
                    .OrderBy(x => x.Order)
                    .ThenBy(x => x.Id)
                    .FirstOrDefaultAsync(
                        x =>
                            x.CompanyId == companyId &&
                            x.Id != 1);

                if (owner != null)
                {
                    recipients.Add(new EmailAddress(owner.Identity.Email));
                }

                var appDomainName = _configuration.GetValue<String>("Values:AppDomainName");

                var subject =
                    $"[{coreEmailConfig.SenderName}] Your free SleekFlow account will be deactivated in 3 days.";
                if (notificationType == NotificationType.AccountDeleted)
                {
                    subject = $"[{coreEmailConfig.SenderName}] Your free SleekFlow account has been deactivated.";
                }

                if (notificationType == NotificationType.TwilioAccountSuspended)
                {
                    subject = $"[{coreEmailConfig.SenderName}] Your WhatsApp/ SMS Account has been suspended";
                }

                var resetTemplate = await _appDbContext.CoreEmailNotificationTemplates
                    .OrderByDescending(x => x.Id)
                    .FirstOrDefaultAsync(x => x.NotificationType == notificationType);

                var plainTextContent = subject;
                var htmlContent = resetTemplate.EmailTemplate
                    .Replace("[Customer]", owner?.Identity.DisplayName);

                var msg = MailHelper.CreateSingleEmailToMultipleRecipients(
                    from,
                    recipients,
                    subject,
                    plainTextContent,
                    htmlContent);

                var response = await client.SendEmailAsync(msg);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, ex.Message);
            }
        }

        public async Task SendShopifyDataRequest(Shop shopInfo, UserProfile customer)
        {
            var emailTemplate = await _appDbContext.CoreEmailNotificationTemplates
                .Where(x => x.NotificationType == NotificationType.ShopifyDataRequest)
                .Select(x => x.EmailTemplate)
                .FirstOrDefaultAsync();

            string orderHistory = string.Empty;
            if (customer.ShopifyOrderRecords != null)
            {
                // Order history
                var order = customer.ShopifyOrderRecords;
                orderHistory +=
                    "<tr><td>Date</td><td>Order id</td><td>Order Name</td><td>Status</td><td>Payment</td></tr>";

                for (int i = 0; i < order.Count; i++)
                {
                    orderHistory += $"<tr>" +
                                    $"<td>{order[i].CreatedAt.ToLocalTime():yyyy/MM/dd HH:mm:ss}</td>" +
                                    $"<td>{order[i].OrderId}</td><td>{order[i].OrderName}</td>" +
                                    $"<td>{order[i].Status}</td><td>{order[i].Payment}</td>" +
                                    $"</tr>\n";
                }
            }

            var htmlContent = emailTemplate
                .Replace("{ShopDomain}", shopInfo.Domain)
                .Replace("{FirstName}", customer.FirstName)
                .Replace("{LastName}", customer.LastName)
                .Replace("{PhoneNumber}", customer.PhoneNumber)
                .Replace("{Email}", customer.Email)
                .Replace("{OrderDetails}", orderHistory)
                .Replace("{ShopifyCustomerId}", customer.ShopifyCustomerId.ToString());

            var subject = "[SleekFlow] Your customer data request from Shopify";
            var coreEmailConfig = await _appDbContext.CoreEmailConfigs.FirstOrDefaultAsync();
            var client = new SendGridClient(coreEmailConfig?.SendGridKey);
            var from = new EmailAddress(coreEmailConfig?.Email, coreEmailConfig?.SenderName);
            var plainTextContent = subject;

            var to = new List<EmailAddress>()
            {
                new EmailAddress(shopInfo.Email)
            };

            var mailContent = MailHelper.CreateSingleEmailToMultipleRecipients(
                from,
                to,
                subject,
                plainTextContent,
                htmlContent);

            var response = await client.SendEmailAsync(mailContent);
        }

        public async Task<Response> SendChatHistoryBackupEmailAsync(
            SendGridMessage msg)
        {
            var coreEmailConfig = await _appDbContext.CoreEmailConfigs.FirstOrDefaultAsync();

            if (coreEmailConfig is null)
            {
                throw new Exception("[SendChatHistoryBackupEmailAsync] - coreEmailConfig cannot be found");
            }

            var client = new SendGridClient(apiKey: coreEmailConfig.SendGridKey);
            var response = await client.SendEmailAsync(msg: msg);

            return response;
        }

        public async Task SendWorkflowInfiniteLoopEmailAsync(
            string companyId,
            string workflowId,
            string workflowName,
            DateTimeOffset createdAt)
        {
            var emailTemplate = await _appDbContext.CoreEmailNotificationTemplates
                .AsNoTracking()
                .Where(x => x.NotificationType == NotificationType.WorkflowInfiniteLoop)
                .Select(x => x.EmailTemplate)
                .FirstOrDefaultAsync();

            var coreEmailConfig = await _appDbContext.CoreEmailConfigs.FirstOrDefaultAsync();

            var htmlContent = emailTemplate
                .Replace("[flow_id]", workflowId)
                .Replace("[flow_name]", workflowName)
                .Replace(
                    "[date_time]",
                    createdAt.ToString("yyyy-MM-dd hh:mm:ss") + " " + TimeZoneInfo.Utc.DisplayName);

            var client = new SendGridClient(apiKey: coreEmailConfig?.SendGridKey);
            var from = new EmailAddress(email: coreEmailConfig?.Email, name: coreEmailConfig?.SenderName);

            var companyAdmins = await _appDbContext.UserRoleStaffs
                .Include(staff => staff.Identity)
                .Where(s => s.CompanyId == companyId && s.RoleType == StaffUserRole.Admin)
                .ToListAsync();

            var to = companyAdmins
                .Select(companyAdmin => new EmailAddress(companyAdmin.Identity.Email, companyAdmin.Identity.DisplayName))
                .ToList();

            var msg = MailHelper.CreateSingleEmailToMultipleRecipients(
                from,
                to,
                subject: "[SleekFlow] Flow Builder Alert: Unusual enrollment attempts in flow",
                plainTextContent: "[SleekFlow] Flow Builder Alert: Unusual enrollment attempts in flow",
                htmlContent: htmlContent);

            await client.SendEmailAsync(msg);
        }

        public async Task SendExecutionUsageReachedThresholdEmailAsync(string companyId, double threshold)
        {
            var adminIdentities = await _appDbContext.UserRoleStaffs
                .AsNoTracking()
                .Include(staff => staff.Identity)
                .Where(s => s.CompanyId == companyId && s.RoleType == StaffUserRole.Admin)
                .Select(x => x.Identity)
                .ToListAsync();

            var subject = $"You'{(threshold < 100.00d ? "re" : "ve")} reach{(threshold < 100.00d ? "ing" : "ed")} your plan's flow enrollment limit";
            var contentTitle = $"Flow enrollment limit{(threshold < 100.00d ? " nearly" : string.Empty)} reached";
            var reachedThresholdMessage = GetEnrolmentUsageReachedThresholdMessage(threshold);
            var actionMessage = GetEnrolmentUsageReachedThresholdActionMessage(threshold);

            var emailTemplate = await GetEmailNotificationTemplateAsync(NotificationType.ExecutionUsageReachedThreshold);

            var coreEmailConfig = await _appDbContext.CoreEmailConfigs.FirstOrDefaultAsync();
            var client = new SendGridClient(apiKey: coreEmailConfig?.SendGridKey);
            var from = new EmailAddress(email: coreEmailConfig?.Email, name: coreEmailConfig?.SenderName);

            foreach (var adminIdentity in adminIdentities)
            {
                var htmlContent = emailTemplate.EmailTemplate
                    .Replace("[content_title]", contentTitle)
                    .Replace("[first_name]", adminIdentity.FirstName)
                    .Replace("[reached_threshold_message]", reachedThresholdMessage)
                    .Replace("[action_message]", actionMessage);

                var to = new EmailAddress(adminIdentity.Email, adminIdentity.DisplayName);
                var msg = MailHelper.CreateSingleEmail(from, to, subject, subject, htmlContent);

                await client.SendEmailAsync(msg);
            }
        }

        private Task<EmailNotificationTemplate?> GetEmailNotificationTemplateAsync(NotificationType notificationType)
        {
            return _appDbContext.CoreEmailNotificationTemplates
                .AsNoTracking()
                .FirstOrDefaultAsync(x => x.NotificationType == notificationType);
        }

        public async Task IntegrationDisconnected(
            string companyId,
            string integrationName,
            string helpCenterUrl)
        {
            try
            {
                var coreEmailConfig = await _appDbContext.CoreEmailConfigs.FirstOrDefaultAsync();
                var client = new SendGridClient(coreEmailConfig.SendGridKey);
                var from = new EmailAddress(coreEmailConfig.Email, coreEmailConfig.SenderName);

                var alertConfig = await _integrationAlertConfigRepository
                    .FindConfigByCompanyIdAsync(companyId);

                if (alertConfig is null
                    || alertConfig.Emails is null
                    || alertConfig.Emails.Count == 0)
                {
                    return;
                }

                var recipients = alertConfig.Emails
                    .Select(e => new EmailAddress(e))
                    .ToList();

                var subject =
                    $"[{coreEmailConfig.SenderName}] Your {integrationName} integration is disconnected.";

                var template = await _appDbContext.CoreEmailNotificationTemplates
                    .OrderByDescending(x => x.Id)
                    .FirstOrDefaultAsync(x => x.NotificationType == NotificationType.IntegrationDisconnected);

                if (template is null)
                {
                    _logger.LogError("IntegrationDisconnected email template not found");
                    return;
                }

                var plainTextContent = subject;

                var htmlContent = template.EmailTemplate
                    .Replace("[integration_name]", integrationName)
                    .Replace("[help_center_url]", helpCenterUrl);

                var msg = MailHelper.CreateSingleEmailToMultipleRecipients(
                    from,
                    recipients,
                    subject,
                    plainTextContent,
                    htmlContent);

                var response = await client.SendEmailAsync(msg);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, ex.Message);
            }
        }

        private string GetEnrolmentUsageReachedThresholdMessage(double threshold)
        {
            return threshold switch
            {
                >= 100.00d => "You have reached your monthly flow enrollment limit. No further enrollments can be processed, which may disrupt your workflows.",
                _ => $"You have reached {threshold}% of your monthly flow enrollment limit. Once this limit is hit, further enrollments will be blocked."
            };
        }

        private string GetEnrolmentUsageReachedThresholdActionMessage(double threshold)
        {
            return threshold switch
            {
                >= 100.00d => "Act now to resume uninterrupted service—explore our add-ons or upgrade your plan to increase your flow enrollment limits.",
                _ => "Act now to prevent disruptions by increasing your flow enrollment limits through our add-ons or by upgrading your plan"
            };
        }
    }
}