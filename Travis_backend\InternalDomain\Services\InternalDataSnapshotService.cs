﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AutoMapper;
using Hangfire;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Travis_backend.BackgroundTaskServices.Attributes;
using Travis_backend.Constants;
using Travis_backend.Helpers;
using Travis_backend.InternalDomain.Models;

namespace Travis_backend.InternalDomain.Services;

public interface IInternalDataSnapshotService
{
    [HangfireJobExpirationTimeout(timeoutInMinutes: 1440)]
    Task<bool> CreateDailyCompanySnapshotDataAsync();

    [HangfireJobExpirationTimeout(timeoutInMinutes: 1440)]
    Task<bool> CreateDailySnapshotDataAsync();

    [HangfireJobExpirationTimeout(timeoutInMinutes: 1440)]
    Task UploadCompanyDetailData();

    [HangfireJobExpirationTimeout(timeoutInMinutes: 1440)]
    Task UploadBillRecordsData();

    [HangfireJobExpirationTimeout(timeoutInMinutes: 1440)]
    Task UploadCmsSalesInputPaymentData();

    [HangfireJobExpirationTimeout(timeoutInMinutes: 1440)]
    Task UploadSleekPayReportData();

    [HangfireJobExpirationTimeout(timeoutInMinutes: 1440)]
    Task UploadAllTimeWhatsappCloudApiConversationUsageAnalyticReport();

    [HangfireJobExpirationTimeout(timeoutInMinutes: 1440)]
    Task UploadFlowBuilderUsageDataAsync();

    [HangfireJobExpirationTimeout(timeoutInMinutes: 1440)]
    Task CreateByMonthDailyWhatsappCloudApiConversationUsageAnalyticsSnapshot(DateTime startOfMonth);

    [HangfireJobExpirationTimeout(timeoutInMinutes: 1440)]
    Task UploadBugBqMetrics();
}

public class InternalDataSnapshotService : IInternalDataSnapshotService
{
    private readonly ILogger<InternalDataSnapshotService> _logger;
    private readonly IConfiguration _configuration;
    private readonly IInternalCompanyDataRepository _internalCompanyDataRepository;
    private readonly IInternalAnalyticService _internalAnalyticService;
    private readonly IInternalGoogleCloudStorage _internalGoogleCloudStorage;
    private readonly IMapper _mapper;
    private readonly IInternalSquadMetricsRepository _internalSquadMetricsRepository;

    public InternalDataSnapshotService(
        ILogger<InternalDataSnapshotService> logger,
        IInternalCompanyDataRepository internalCompanyDataRepository,
        IConfiguration configuration,
        IInternalGoogleCloudStorage internalGoogleCloudStorage,
        IMapper mapper,
        IInternalAnalyticService internalAnalyticService,
        IInternalSquadMetricsRepository internalSquadMetricsRepository)
    {
        _logger = logger;
        _internalCompanyDataRepository = internalCompanyDataRepository;
        _configuration = configuration;
        _internalGoogleCloudStorage = internalGoogleCloudStorage;
        _mapper = mapper;
        _internalAnalyticService = internalAnalyticService;
        _internalSquadMetricsRepository = internalSquadMetricsRepository;
    }

    // Corn job for create snapshots
    [HangfireJobExpirationTimeout(timeoutInMinutes: 1440)]
    public async Task<bool> CreateDailyCompanySnapshotDataAsync()
    {
        if (!CheckIsDataSnapshotEnable())
        {
            return false;
        }

        await _internalAnalyticService.UpdateCompanyAllTimeRevenueAnalyticInfo();

        // Company Detail
        var uploadCompanyDetailDataJobId = BackgroundJob.Enqueue(() => UploadCompanyDetailData());
        _logger.LogInformation(
            "[CreateDailyCompanySnapshotData] Start UploadCompanyDetailData, Hangfire Job Id {HangfireJobId}",
            uploadCompanyDetailDataJobId);

        // Bill Records
        var uploadBillRecordsDataJobId = BackgroundJob.Enqueue(() => UploadBillRecordsData());
        _logger.LogInformation(
            "[CreateDailyCompanySnapshotData] Start UploadBillRecordsData, Hangfire Job Id {HangfireJobId}",
            uploadBillRecordsDataJobId);

        // Cms Sales Input Payment Csv Data
        var uploadCmsSalesInputPaymentDataJobId = BackgroundJob.Enqueue(() => UploadCmsSalesInputPaymentData());
        _logger.LogInformation(
            "[CreateDailyCompanySnapshotData] Start UploadCmsSalesInputPaymentData, Hangfire Job Id {HangfireJobId}",
            uploadCmsSalesInputPaymentDataJobId);

        return true;
    }

    [HangfireJobExpirationTimeout(timeoutInMinutes: 1440)]
    public async Task<bool> CreateDailySnapshotDataAsync()
    {
        if (!CheckIsDataSnapshotEnable())
        {
            return false;
        }

        // SleekPay Report
        var uploadSleekPayReportDataJobId = BackgroundJob.Enqueue(() => UploadSleekPayReportData());
        _logger.LogInformation(
            "[CreateDailyCompanySnapshotData] Start UploadSleekPayReportData, Hangfire Job Id {HangfireJobId}",
            uploadSleekPayReportDataJobId);

        // Whatsapp Cloud API
        var uploadAllTimeWhatsappCloudApiConversationUsageAnalyticReportJobId = BackgroundJob.Schedule(
            () => UploadAllTimeWhatsappCloudApiConversationUsageAnalyticReport(),
            TimeSpan.FromMinutes(10));
        _logger.LogInformation(
            "[CreateDailyCompanySnapshotData] Start UploadAllTimeWhatsappCloudApiConversationUsageAnalyticReport, Hangfire Job Id {HangfireJobId}",
            uploadAllTimeWhatsappCloudApiConversationUsageAnalyticReportJobId);

        // Bugbq Metrics
        var uploadBugBqMetricsJobId = BackgroundJob.Schedule(() => UploadBugBqMetrics(), TimeSpan.FromMinutes(20));
        _logger.LogInformation(
            "[CreateDailyCompanySnapshotData] Start UploadBugBqMetrics, Hangfire Job Id {HangfireJobId}",
            uploadBugBqMetricsJobId);

        // Flow Builder Usage
        var uploadFlowBuilderUsageDataJobId = BackgroundJob.Enqueue(() => UploadFlowBuilderUsageDataAsync());
        _logger.LogInformation(
            "[CreateDailyCompanySnapshotData] Start UploadFlowBuilderUsageDataAsync, Hangfire Job Id {HangfireJobId}",
            uploadFlowBuilderUsageDataJobId);

        return true;
    }

    [HangfireJobExpirationTimeout(timeoutInMinutes: 1440)]
    public async Task UploadCompanyDetailData()
    {
        // Company Detail
        _logger.LogInformation("[UploadCompanyDetailData] Upload Company Detail Data Started");

        var companySnapshotInPastDay =
            await _internalCompanyDataRepository.GetCompanySnapshotDataAsync(
                DateTime.UtcNow.Date.AddDays(-1).AddHours(8));
        var detail = await _internalCompanyDataRepository.GetCmsCompanyDetails(null, companySnapshotInPastDay);
        var snapshotData = detail.Select(CmsCompanyDetailConverter.ConvertToCompanySnapshotData).ToList();

        if (CheckIsDataSnapshotEnable())
        {
            await _internalCompanyDataRepository.SaveCompanySnapshotDataAsync(snapshotData, DateTime.UtcNow);
        }

        var snapshotTrimData = _mapper.Map<List<CompanySnapshotTrimData>>(snapshotData);
        await _internalGoogleCloudStorage.UploadCompanyDetailCsvAsync(snapshotTrimData, DateTime.UtcNow);

        // Onboarding Data
        var companyOnboardingData = await _internalCompanyDataRepository.GetCompanyOnboardingProgresses(
            null,
            detail.Select(CmsCompanyDetailConverter.ConvertToCmsCompanySubscriptionPlan).ToList());
        await _internalGoogleCloudStorage.UploadCompanyOnboardingCsvAsync(companyOnboardingData, DateTime.UtcNow);

        _logger.LogInformation("[UploadCompanyDetailData] Upload Company Detail Data Completed");
    }

    [HangfireJobExpirationTimeout(timeoutInMinutes: 1440)]
    public async Task UploadFlowBuilderUsageDataAsync()
    {
        _logger.LogInformation("[UploadFlowBuilderUsageDataAsync] Upload Flow Builder Usage Data Started");
        var flowBuilderUsageData =
            await _internalCompanyDataRepository.GetCmsCompanyFlowBuilderUsageAsync(
                DateTime.UtcNow.Date.AddDays(-1),
                DateTime.UtcNow.Date);

        await _internalGoogleCloudStorage.UploadFlowBuilderUsageCsvAsync(flowBuilderUsageData, DateTime.UtcNow);
        _logger.LogInformation("[UploadFlowBuilderUsageDataAsync] Upload Flow Builder Usage Data Completed");
    }

    [HangfireJobExpirationTimeout(timeoutInMinutes: 1440)]
    public async Task UploadBillRecordsData()
    {
        _logger.LogInformation("[UploadBillRecordsData] Upload Bill Records Data Started");
        var billRecords = await _internalCompanyDataRepository.GetCmsCompanyBillRecords();

        var billRecordSnapshotData =
            billRecords.Select(CmsCompanyDetailConverter.ConvertToCompanyBillRecordSnapshotData).ToList();
        await _internalGoogleCloudStorage.UploadCompanyBillRecordCsvAsync(billRecordSnapshotData, DateTime.UtcNow);
        _logger.LogInformation("[UploadBillRecordsData] Upload Bill Records Data Completed");
    }

    [HangfireJobExpirationTimeout(timeoutInMinutes: 1440)]
    public async Task UploadCmsSalesInputPaymentData()
    {
        _logger.LogInformation("[UploadCmsSalesInputPaymentData] Upload Cms Sales Input Payment Data Started");
        var cmsSalesInputPaymentCsv = await _internalCompanyDataRepository.GetCmsSalesInputPaymentCsv(null);

        await _internalGoogleCloudStorage.UploadCmsSalesInputPaymentCsvAsync(
            cmsSalesInputPaymentCsv,
            DateTime.UtcNow);
        _logger.LogInformation("[UploadCmsSalesInputPaymentData] Upload Cms Sales Input Payment Data Completed");
    }

    [HangfireJobExpirationTimeout(timeoutInMinutes: 1440)]
    public async Task UploadSleekPayReportData()
    {
        _logger.LogInformation("[UploadSleekPayReportData] Upload SleekPay Report Data Started");
        var sleekPayData = await _internalCompanyDataRepository.GetCmsSleekPayReportData(null);
        await _internalGoogleCloudStorage.UploadSleekPayReportData(sleekPayData, DateTime.UtcNow);
        _logger.LogInformation("[UploadSleekPayReportData] Upload SleekPay Report Data Completed");
    }

    [HangfireJobExpirationTimeout(timeoutInMinutes: 1440)]
    public async Task UploadAllTimeWhatsappCloudApiConversationUsageAnalyticReport()
    {
        if (_configuration["SF_ENVIRONMENT"] != LocationNames.EastAsia)
        {
            return;
        }

        _logger.LogInformation("[UploadAllTimeWhatsappCloudApiConversationUsageAnalyticReport] Upload All Time Whatsapp Cloud Api Conversation Usage Analytic Report Started");

        // Snapshot at every 7th of the month
        if (DateTime.UtcNow.Day == 7)
        {
            await CreateByMonthDailyWhatsappCloudApiConversationUsageAnalyticsSnapshot(
                DateTime.UtcNow.AddMonths(-1).StartOfMonth());
        }

        var result =
            await _internalCompanyDataRepository.GetDailyWhatsappCloudApiConversationUsageAnalytic(
                new DateTime(2022, 11, 1),
                DateTime.UtcNow.Date,
                true);

        await _internalGoogleCloudStorage.UploadWhatsappCloudApiConversationUsageAnalyticsCsvAsync(
            result,
            DateTime.UtcNow);

        _logger.LogInformation("[UploadAllTimeWhatsappCloudApiConversationUsageAnalyticReport] Upload All Time Whatsapp Cloud Api Conversation Usage Analytic Report Completed");
    }

    public async Task CreateByMonthDailyWhatsappCloudApiConversationUsageAnalyticsSnapshot(DateTime startOfMonth)
    {
        startOfMonth = startOfMonth.StartOfMonth();
        var endOfMonth = startOfMonth.EndOfMonth();

        var conversationUsageAnalytics =
            await _internalCompanyDataRepository.GetDailyWhatsappCloudApiConversationUsageAnalytic(
                startOfMonth,
                endOfMonth,
                false);

        await _internalCompanyDataRepository.CreateAllTimeDailyWhatsappCloudApiConversationUsageAnalyticSnapshot(
            startOfMonth,
            conversationUsageAnalytics);
    }

    [HangfireJobExpirationTimeout(timeoutInMinutes: 1440)]
    public async Task UploadBugBqMetrics()
    {
        _logger.LogInformation("[UploadBugBqMetrics] Upload BugBq Metrics Started");
        var dateStart = DateTime.UtcNow.AddDays(-1).StartOfDay();
        var dateEnd = DateTime.UtcNow.StartOfDay();

        var bugbqMessageMetricByCompanySummary = await _internalSquadMetricsRepository.GetMessageVolumeByCompanySummary(
            dateStart.ToString("yyyy-MM-dd"),
            dateStart,
            dateEnd);

        await _internalGoogleCloudStorage.UploadBugbqSquadMetricsCsvAsync(
            bugbqMessageMetricByCompanySummary,
            dateStart);
        _logger.LogInformation("[UploadBugBqMetrics] Upload BugBq Metrics Completed");
    }

    private bool CheckIsDataSnapshotEnable()
    {
        return _configuration.GetValue<bool>("DataSnapshot:IsEnable");
    }
}