﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using AutoMapper;
using GraphApi.Client.Const.WhatsappCloudApi;
using GraphApi.Client.Helpers;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json.Linq;
using NReco.VideoConverter;
using Sleekflow.Apis.MessagingHub.Api;
using Sleekflow.Apis.MessagingHub.Client;
using Sleekflow.Apis.MessagingHub.Model;
using Travis_backend.Constants;
using Travis_backend.ConversationDomain.Models;
using Travis_backend.ConversationServices;
using Travis_backend.Database;
using Travis_backend.Enums;
using Travis_backend.FileDomain.Services;
using Travis_backend.FlowHubs;
using Travis_backend.Helpers;
using Travis_backend.MessageDomain.Models;
using Travis_backend.MessageDomain.ViewModels;
using Travis_backend.OpenTelemetry.Meters;
using Travis_backend.OpenTelemetry.Meters.Constants;
using Travis_backend.SignalR;

namespace Travis_backend.MessageDomain.ChannelMessageProvider.ChannelMessageHandlers;

public class WhatsappCloudApiChannelMessageHandler : IChannelMessageHandler
{
    private readonly ApplicationDbContext _appDbContext;
    private readonly IChannelsApi _channelsApi;
    private readonly IWhatsappCloudApiService _whatsappCloudApiService;
    private readonly ILogger<WhatsappCloudApiChannelMessageHandler> _logger;
    private readonly IConfiguration _configuration;
    private readonly IMediaProcessService _mediaProcessService;
    private readonly IMapper _mapper;
    private readonly ISignalRService _signalRService;
    private readonly IEmailNotificationService _emailNotificationService;
    private readonly IConversationMeters _conversationMeters;
    private readonly IUserProfileHooks _userProfileHooks;

    public string ChannelType => ChannelTypes.WhatsappCloudApi;

    public WhatsappCloudApiChannelMessageHandler(
        ApplicationDbContext appDbContext,
        IChannelsApi channelsApi,
        IWhatsappCloudApiService whatsappCloudApiService,
        ILogger<WhatsappCloudApiChannelMessageHandler> logger,
        IConfiguration configuration,
        IMediaProcessService mediaProcessService,
        IMapper mapper,
        ISignalRService signalRService,
        IEmailNotificationService emailNotificationService,
        IConversationMeters conversationMeters,
        IUserProfileHooks userProfileHooks)
    {
        _appDbContext = appDbContext;
        _channelsApi = channelsApi;
        _whatsappCloudApiService = whatsappCloudApiService;
        _logger = logger;
        _configuration = configuration;
        _mediaProcessService = mediaProcessService;
        _mapper = mapper;
        _signalRService = signalRService;
        _emailNotificationService = emailNotificationService;
        _conversationMeters = conversationMeters;
        _userProfileHooks = userProfileHooks;
    }

    public async ValueTask<ConversationMessage> SendChannelMessageAsync(
        Conversation conversation,
        ConversationMessage conversationMessage)
    {
        if ((!conversationMessage.IsSentFromSleekflow || conversationMessage.IsFromImport) &&
            !string.IsNullOrEmpty(conversationMessage.MessageUniqueID))
        {
            return conversationMessage;
        }

        var inititalMessageStatus = conversationMessage.Status;

        conversationMessage.ChannelIdentityId = conversation.WhatsappCloudApiUser.WhatsappChannelPhoneNumber;

        var whatsappCloudApiConfig = await _appDbContext.ConfigWhatsappCloudApiConfigs.AsNoTracking()
            .FirstOrDefaultAsync(
                x => x.CompanyId == conversation.CompanyId && x.WhatsappPhoneNumber ==
                    conversation.WhatsappCloudApiUser.WhatsappChannelPhoneNumber);

        try
        {
            SendWhatsappCloudApiMessageOutputOutput messageResponse = null;

            var callbackDataDict = new Dictionary<string, string>()
            {
                {
                    "sleekflow_core_message_id", conversationMessage.Id.ToString()
                }
            };

            switch (conversationMessage.MessageType)
            {
                case "text":
                    messageResponse = await _channelsApi.ChannelsSendWhatsappCloudApiMessagePostAsync(
                        sendWhatsappCloudApiMessageInput: new SendWhatsappCloudApiMessageInput(
                            conversationMessage.CompanyId,
                            whatsappCloudApiConfig.MessagingHubWabaPhoneNumberId,
                            WhatsappCloudApiMessageObjectFactory.CreateTextMessage(
                                conversation.WhatsappCloudApiUser.WhatsappId,
                                conversationMessage.MessageContent,
                                callbackDataDict,
                                WhatsappCloudApiExtendedMessageHelper.CheckTextContainsUrl(
                                    conversationMessage.MessageContent),
                                conversationMessage.QuotedMsgId)));

                    break;
                case "template":
                    try
                    {
                        var templates = await _whatsappCloudApiService.GetTemplates(
                            whatsappCloudApiConfig.CompanyId,
                            whatsappCloudApiConfig.MessagingHubWabaId);

                        var template = templates.FirstOrDefault(
                            x => x.Name == conversationMessage.ExtendedMessagePayload
                                     .ExtendedMessagePayloadDetail.WhatsappCloudApiTemplateMessageObject
                                     .TemplateName &&
                                 x.Language == conversationMessage.ExtendedMessagePayload
                                     .ExtendedMessagePayloadDetail.WhatsappCloudApiTemplateMessageObject
                                     .Language);
                        if (template == null && !string.IsNullOrEmpty(
                                conversationMessage.ExtendedMessagePayload.ExtendedMessagePayloadDetail
                                    .WhatsappCloudApiTemplateMessageObject.TemplateId))
                        {
                            template = templates.First(
                                x => x.Id == conversationMessage.ExtendedMessagePayload.ExtendedMessagePayloadDetail
                                    .WhatsappCloudApiTemplateMessageObject.TemplateId);
                        }

                        if (template != null)
                        {
                            if (string.IsNullOrWhiteSpace(conversationMessage.MessageContent))
                            {
                                conversationMessage.MessageContent = template.Components.First(
                                        x => x.Type == WhatsappCloudApiTemplateComponentTypeConst.BODY)
                                    .Text.FormatWhatsappCloudApiTemplateParamToBodyText(
                                        conversationMessage.ExtendedMessagePayload.ExtendedMessagePayloadDetail
                                            .WhatsappCloudApiTemplateMessageObject.Components);
                            }

                            conversationMessage.ExtendedMessagePayload.ExtendedMessagePayloadDetail
                                .WhatsappCloudApiTemplateMessageObject.TemplateId = template.Id;

                            _appDbContext.Entry(conversationMessage.ExtendedMessagePayload)
                                .Property(x => x.ExtendedMessagePayloadDetail).IsModified = true;

                            conversationMessage.Metadata ??= new Dictionary<string, object>();

                            conversationMessage.Metadata["whatsappcloudapi:template:components"] = template.Components;
                            _appDbContext.Entry(conversationMessage).Property(x => x.Metadata).IsModified = true;

                            var flowTemplateButtonObject = template.Components
                                .Where(
                                    x => x.Type.Equals(
                                        WhatsappCloudApiTemplateComponentTypeConst.BUTTONS,
                                        StringComparison.OrdinalIgnoreCase))
                                .SelectMany(x => x.Buttons)
                                .FirstOrDefault(
                                    button => button.Type.Equals(
                                        WhatsappCloudApiTemplateButtonTypeConst.FLOW,
                                        StringComparison.OrdinalIgnoreCase));

                            if (flowTemplateButtonObject != null)
                            {
                                try
                                {
                                    var flowToken = FlowTokenHelper.GenerateFlowToken(
                                    whatsappCloudApiConfig.FacebookWabaId,
                                    flowTemplateButtonObject.FlowId,
                                    DateTimeOffset.UtcNow.ToUnixTimeMilliseconds().ToString());

                                    var flowButtonParameterObject = conversationMessage.ExtendedMessagePayload
                                        .ExtendedMessagePayloadDetail
                                        .WhatsappCloudApiTemplateMessageObject.Components
                                        .Where(
                                            x => x.Type.Equals(
                                                     WhatsappCloudApiComponentTypeConst.button,
                                                     StringComparison.OrdinalIgnoreCase) &&
                                                 x.SubType.Equals(
                                                     WhatsappCloudApiTemplateButtonTypeConst.FLOW,
                                                     StringComparison.OrdinalIgnoreCase))
                                        .SelectMany(x => x.Parameters)
                                        .FirstOrDefault(
                                            x => x.Type.Equals(
                                                WhatsappCloudApiParameterTypeConst.action,
                                                StringComparison.OrdinalIgnoreCase));

                                    if (flowButtonParameterObject != null)
                                    {
                                        flowButtonParameterObject.Action??= new ();
                                        flowButtonParameterObject.Action.FlowToken = flowToken;
                                    }

                                    var getFlowResponse = await _whatsappCloudApiService.GetWhatsappFlowAsync(
                                        whatsappCloudApiConfig.CompanyId,
                                        whatsappCloudApiConfig.MessagingHubWabaId,
                                        flowTemplateButtonObject.FlowId);
                                    conversationMessage.Metadata["whatsappcloudapi:flow:details"] = getFlowResponse;
                                }
                                catch (Exception ex)
                                {
                                    _logger.LogError(
                                        ex,
                                        "Error getting Waba {WabaId} Flow {FlowId} details",
                                        whatsappCloudApiConfig.MessagingHubWabaId,
                                        flowTemplateButtonObject.FlowId);
                                }
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(
                            ex,
                            "[{MethodName}] Conversation {ConversationId} error during format conversation message {MessageId} body from template & param. {ExceptionMessage}",
                            nameof(SendChannelMessageAsync),
                            conversation.Id,
                            conversationMessage.Id,
                            ex.Message);
                    }

                    messageResponse = await _channelsApi.ChannelsSendWhatsappCloudApiMessagePostAsync(
                        sendWhatsappCloudApiMessageInput: new SendWhatsappCloudApiMessageInput(
                            conversation.CompanyId,
                            whatsappCloudApiConfig.MessagingHubWabaPhoneNumberId,
                            WhatsappCloudApiMessageObjectFactory.CreateTemplateMessage(
                                conversation.WhatsappCloudApiUser.WhatsappId,
                                conversationMessage.ExtendedMessagePayload.ExtendedMessagePayloadDetail
                                    .WhatsappCloudApiTemplateMessageObject.TemplateName,
                                conversationMessage.ExtendedMessagePayload.ExtendedMessagePayloadDetail
                                    .WhatsappCloudApiTemplateMessageObject.Language,
                                conversationMessage.ExtendedMessagePayload.ExtendedMessagePayloadDetail
                                    .WhatsappCloudApiTemplateMessageObject.Components,
                                callbackDataDict)));

                    break;
                case "interactive":
                    if (string.IsNullOrWhiteSpace(conversationMessage.MessageContent))
                    {
                        conversationMessage.MessageContent = conversationMessage.ExtendedMessagePayload
                            .ExtendedMessagePayloadDetail.WhatsappCloudApiInteractiveObject.Body.Text;
                    }

                    if (conversationMessage.ExtendedMessagePayload
                        .ExtendedMessagePayloadDetail
                        .WhatsappCloudApiInteractiveObject?.Action is { Name: "flow" })
                    {
                        try
                        {
                            var flowToken = FlowTokenHelper.GenerateFlowToken(
                                whatsappCloudApiConfig.FacebookWabaId,
                                conversationMessage.ExtendedMessagePayload.ExtendedMessagePayloadDetail
                                    .WhatsappCloudApiInteractiveObject.Action.Parameters.FlowId,
                                DateTimeOffset.UtcNow.ToUnixTimeMilliseconds().ToString());

                            conversationMessage.ExtendedMessagePayload.ExtendedMessagePayloadDetail
                                .WhatsappCloudApiInteractiveObject.Action.Parameters.FlowToken = flowToken;

                            var getFlowResponse = await _whatsappCloudApiService.GetWhatsappFlowAsync(
                                whatsappCloudApiConfig.CompanyId,
                                whatsappCloudApiConfig.MessagingHubWabaId,
                                conversationMessage.ExtendedMessagePayload.ExtendedMessagePayloadDetail
                                    .WhatsappCloudApiInteractiveObject.Action.Parameters.FlowId);
                            conversationMessage.Metadata ??= new Dictionary<string, object>();
                            conversationMessage.Metadata["whatsappcloudapi:flow:details"] = getFlowResponse;
                            _appDbContext.Entry(conversationMessage).Property(x => x.Metadata).IsModified = true;
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(
                                ex,
                                "Error getting Waba {WabaId} Flow {FlowId} details",
                                whatsappCloudApiConfig.MessagingHubWabaId,
                                conversationMessage.ExtendedMessagePayload.ExtendedMessagePayloadDetail
                                    .WhatsappCloudApiInteractiveObject.Action.Parameters.FlowId);
                        }
                    }

                    messageResponse = await _channelsApi.ChannelsSendWhatsappCloudApiMessagePostAsync(
                        sendWhatsappCloudApiMessageInput: new SendWhatsappCloudApiMessageInput(
                            conversation.CompanyId,
                            whatsappCloudApiConfig.MessagingHubWabaPhoneNumberId,
                            WhatsappCloudApiMessageObjectFactory.CreateInteractiveMessage(
                                conversation.WhatsappCloudApiUser.WhatsappId,
                                conversationMessage.ExtendedMessagePayload.ExtendedMessagePayloadDetail
                                    .WhatsappCloudApiInteractiveObject,
                                callbackDataDict)));

                    break;
                case "file":
                    var domainName = _configuration.GetValue<String>("Values:DomainName");

                    foreach (var uploadedFile in conversationMessage.UploadedFiles)
                    {
                        if (uploadedFile != null)
                        {
                            // var url = $"{domainName}/Message/File/Private/{uploadedFile.FileId}/{uploadedFile.Filename}?mode=redirect";
                            var url = $"{domainName}/Message/File/Private/{uploadedFile.FileId}";

                            if (uploadedFile.MIMEType.Contains("video"))
                            {
                                if (!uploadedFile.MIMEType.Contains("mp4"))
                                {
                                    await _mediaProcessService.ProcessMedia(
                                        conversation.Id,
                                        uploadedFile,
                                        Format.mp4);
                                }

                                messageResponse = await _channelsApi.ChannelsSendWhatsappCloudApiMessagePostAsync(
                                    sendWhatsappCloudApiMessageInput: new SendWhatsappCloudApiMessageInput(
                                        conversation.CompanyId,
                                        whatsappCloudApiConfig.MessagingHubWabaPhoneNumberId,
                                        WhatsappCloudApiMessageObjectFactory.CreateVideoMessageByMediaLink(
                                            conversation.WhatsappCloudApiUser.WhatsappId,
                                            url,
                                            conversationMessage.MessageContent,
                                            callbackDataDict,
                                            messageIdToReply: conversationMessage.QuotedMsgId)));
                            }
                            else if ((uploadedFile.MIMEType.Contains("audio") &&
                                      !uploadedFile.MIMEType.Contains("mp3")) ||
                                     Path.GetExtension(uploadedFile.Filename) == ".webm" ||
                                     Path.GetExtension(uploadedFile.Filename) == ".bin")
                            {
                                await _mediaProcessService.ProcessMedia(conversation.Id, uploadedFile, "mp3");

                                conversationMessage.MessageContent = null;

                                messageResponse = await _channelsApi.ChannelsSendWhatsappCloudApiMessagePostAsync(
                                    sendWhatsappCloudApiMessageInput: new SendWhatsappCloudApiMessageInput(
                                        conversation.CompanyId,
                                        whatsappCloudApiConfig.MessagingHubWabaPhoneNumberId,
                                        WhatsappCloudApiMessageObjectFactory.CreateAudioMessageByMediaLink(
                                            conversation.WhatsappCloudApiUser.WhatsappId,
                                            url,
                                            callbackDataDict,
                                            messageIdToReply: conversationMessage.QuotedMsgId)));
                            }
                            else if (uploadedFile.MIMEType.Contains("image") &&
                                     !uploadedFile.MIMEType.Contains("gif"))
                            {
                                if (uploadedFile.MIMEType == "image/webp")
                                {
                                    conversationMessage.MessageContent = null;

                                    messageResponse =
                                        await _channelsApi.ChannelsSendWhatsappCloudApiMessagePostAsync(
                                            sendWhatsappCloudApiMessageInput: new SendWhatsappCloudApiMessageInput(
                                                conversation.CompanyId,
                                                whatsappCloudApiConfig.MessagingHubWabaPhoneNumberId,
                                                WhatsappCloudApiMessageObjectFactory.CreateStickerMessageByLink(
                                                    conversation.WhatsappCloudApiUser.WhatsappId,
                                                    url,
                                                    callbackDataDict)));
                                }
                                else
                                {
                                    messageResponse =
                                        await _channelsApi.ChannelsSendWhatsappCloudApiMessagePostAsync(
                                            sendWhatsappCloudApiMessageInput: new SendWhatsappCloudApiMessageInput(
                                                conversation.CompanyId,
                                                whatsappCloudApiConfig.MessagingHubWabaPhoneNumberId,
                                                WhatsappCloudApiMessageObjectFactory.CreateImageMessageByMediaLink(
                                                    conversation.WhatsappCloudApiUser.WhatsappId,
                                                    url,
                                                    callbackDataDict,
                                                    conversationMessage.MessageContent,
                                                    messageIdToReply: conversationMessage.QuotedMsgId)));
                                }
                            }
                            else
                            {
                                messageResponse = await _channelsApi.ChannelsSendWhatsappCloudApiMessagePostAsync(
                                    sendWhatsappCloudApiMessageInput: new SendWhatsappCloudApiMessageInput(
                                        conversation.CompanyId,
                                        whatsappCloudApiConfig.MessagingHubWabaPhoneNumberId,
                                        WhatsappCloudApiMessageObjectFactory.CreateDocumentMessageByLink(
                                            conversation.WhatsappCloudApiUser.WhatsappId,
                                            Path.GetFileName(uploadedFile.Filename),
                                            url,
                                            conversationMessage.MessageContent,
                                            callbackDataDict,
                                            messageIdToReply: conversationMessage.QuotedMsgId)));
                            }
                        }
                    }

                    break;
                case "contacts":
                    if (string.IsNullOrWhiteSpace(conversationMessage.MessageContent))
                    {
                        conversationMessage.MessageContent = conversationMessage.ExtendedMessagePayload
                            .ExtendedMessagePayloadDetail.WhatsappCloudApiContactsObject.First().Name.FormattedName;
                    }

                    messageResponse = await _channelsApi.ChannelsSendWhatsappCloudApiMessagePostAsync(
                        sendWhatsappCloudApiMessageInput: new SendWhatsappCloudApiMessageInput(
                            conversation.CompanyId,
                            whatsappCloudApiConfig.MessagingHubWabaPhoneNumberId,
                            WhatsappCloudApiMessageObjectFactory.CreateContactsMessage(
                                conversation.WhatsappCloudApiUser.WhatsappId,
                                conversationMessage.ExtendedMessagePayload.ExtendedMessagePayloadDetail
                                    .WhatsappCloudApiContactsObject,
                                callbackDataDict,
                                messageIdToReply: conversationMessage.QuotedMsgId)));

                    break;
                case "location":
                    if (string.IsNullOrWhiteSpace(conversationMessage.MessageContent))
                    {
                        conversationMessage.MessageContent =
                            $"{conversationMessage.ExtendedMessagePayload.ExtendedMessagePayloadDetail.WhatsappCloudApiLocationObject.Longitude.ToString()}, {conversationMessage.ExtendedMessagePayload.ExtendedMessagePayloadDetail.WhatsappCloudApiLocationObject.Latitude.ToString()}";
                    }

                    messageResponse = await _channelsApi.ChannelsSendWhatsappCloudApiMessagePostAsync(
                        sendWhatsappCloudApiMessageInput: new SendWhatsappCloudApiMessageInput(
                            conversation.CompanyId,
                            whatsappCloudApiConfig.MessagingHubWabaPhoneNumberId,
                            WhatsappCloudApiMessageObjectFactory.CreateLocationMessage(
                                conversation.WhatsappCloudApiUser.WhatsappId,
                                conversationMessage.ExtendedMessagePayload.ExtendedMessagePayloadDetail
                                    .WhatsappCloudApiLocationObject.Latitude,
                                conversationMessage.ExtendedMessagePayload.ExtendedMessagePayloadDetail
                                    .WhatsappCloudApiLocationObject.Longitude,
                                callbackDataDict,
                                conversationMessage.ExtendedMessagePayload.ExtendedMessagePayloadDetail
                                    .WhatsappCloudApiLocationObject.Name,
                                conversationMessage.ExtendedMessagePayload.ExtendedMessagePayloadDetail
                                    .WhatsappCloudApiLocationObject.Address,
                                messageIdToReply: conversationMessage.QuotedMsgId)));

                    break;
                case "reaction":
                    if (string.IsNullOrWhiteSpace(conversationMessage.MessageContent))
                    {
                        conversationMessage.MessageContent = conversationMessage.ExtendedMessagePayload
                            .ExtendedMessagePayloadDetail.WhatsappCloudApiReactionObject.Emoji;
                    }

                    messageResponse = await _channelsApi.ChannelsSendWhatsappCloudApiMessagePostAsync(
                        sendWhatsappCloudApiMessageInput: new SendWhatsappCloudApiMessageInput(
                            conversation.CompanyId,
                            whatsappCloudApiConfig.MessagingHubWabaPhoneNumberId,
                            WhatsappCloudApiMessageObjectFactory.CreateReactionMessage(
                                conversation.WhatsappCloudApiUser.WhatsappId,
                                conversationMessage.ExtendedMessagePayload.ExtendedMessagePayloadDetail
                                    .WhatsappCloudApiReactionObject.MessageId,
                                conversationMessage.ExtendedMessagePayload.ExtendedMessagePayloadDetail
                                    .WhatsappCloudApiReactionObject.Emoji,
                                callbackDataDict)));

                    break;
            }

            conversationMessage.MessageUniqueID =
                messageResponse.Data.MessageResponse.Messages.FirstOrDefault()?.Id;
            conversationMessage.Status = MessageStatus.Sending;

            _logger.LogInformation(
                "Outgoing MessageId From Messaging Hub - Cloud API: {ConversationMessageMessageUniqueID}",
                conversationMessage.MessageUniqueID);

            _conversationMeters.IncrementCounter(ChannelTypes.WhatsappCloudApi, ConversationMeterOptions.SendSuccess);
        }
        catch (SleekflowErrorCodeException ex)
        {
            if (ex.ErrorCode == 6002)
            {
                conversationMessage.Status = MessageStatus.OutOfCredit;
            }
            else
            {
                conversationMessage.Status = MessageStatus.Failed;
            }

            // conversationMessage.IsSentFromSleekflow = true;
            await _appDbContext.SaveChangesAsync();

            _conversationMeters.IncrementCounter(ChannelTypes.WhatsappCloudApi, ConversationMeterOptions.SendFailed);

            // Error message Format: (#code) message - additional message
            if (ex.Output != null && ex.Output.ErrorContext != null)
            {
                ConversationMessageError conversationMessageError = null;

                if (ex.Output.ErrorContext.TryGetValue("error", out object errorObj))
                {
                    var error = (JObject) errorObj;

                    try
                    {
                        conversationMessageError = new ()
                        {
                            Code = error["code"].ToString(), InnerError = error
                        };

                        if (!string.IsNullOrEmpty(error["error_user_msg"]?.ToString()))
                        {
                            conversationMessageError.Message = error["error_user_msg"].ToString();
                        }
                        else if (!string.IsNullOrEmpty(error["message"]?.ToString()))
                        {
                            conversationMessageError.Message = error["message"].ToString();
                        }
                        else if (!string.IsNullOrEmpty(error["error_data"]?["details"]?.ToString()))
                        {
                            conversationMessageError.Message = error["error_data"]?["details"]?.ToString();
                        }

                        var errorUserMsg = error["error_user_msg"]?.ToString() ?? null;

                        if (!string.IsNullOrEmpty(errorUserMsg))
                        {
                            conversationMessage.ChannelStatusMessage =
                                $"{error["code"]} {error["error_user_msg"]}";
                        }
                        else
                        {
                            conversationMessage.ChannelStatusMessage = error["message"].ToString();

                            try
                            {
                                var details = error["error_data"]?["details"]?.ToString() ?? null;

                                if (!string.IsNullOrEmpty(details))
                                {
                                    conversationMessage.ChannelStatusMessage += " - " + details;
                                }
                            }
                            catch
                            {
                                // ignored
                            }
                        }
                    }
                    catch (Exception e)
                    {
                        conversationMessage.ChannelStatusMessage = ex.Message;
                    }

                    _logger.LogError(
                        ex,
                        "Error From Messaging Hub - Cloud API, Message unique ID: {MessageUniqueId}, Channel status message: {ChannelStatusMessage}",
                        conversationMessage?.MessageUniqueID,
                        conversationMessage?.ChannelStatusMessage);

                    if (conversation.WhatsappCloudApiUser.WhatsappId.StartsWith("55")
                        &&
                        ($"(#{error["code"]})" == "(#100)" ||
                         conversationMessage.ChannelStatusMessage.Contains("131026"))
                        &&
                        conversation.WhatsappCloudApiUser.WhatsappId.Length == 13)
                    {
                        // Check current conversation has successful send history with number added 9
                        var successfulSendMessageStatus = new[]
                        {
                            MessageStatus.Read,
                            MessageStatus.Sent,
                            MessageStatus.Received,
                            MessageStatus.PaymentLinkPaid,
                            MessageStatus.Sending
                        };

                        var haveSuccessfulSendHistoryWhenAdded9 = await _appDbContext.ConversationMessages
                            .Where(
                                x =>
                                    x.ConversationId == conversation.Id &&
                                    x.Channel == ChannelTypes.WhatsappCloudApi &&
                                    x.IsSentFromSleekflow &&
                                    successfulSendMessageStatus.Contains(x.Status))
                            .AnyAsync();

                        if (!haveSuccessfulSendHistoryWhenAdded9)
                        {
                            // Add 9 to phone number & resend
                            conversationMessage.MessageChecksum = Guid.NewGuid().ToString();
                            conversationMessage.ChannelStatusMessage = null;

                            try
                            {
                                conversationMessage = await OnBrazilSendCloudAPIMessageFailed(
                                    conversation,
                                    conversationMessage);
                                conversationMessage.Status = MessageStatus.Sending;
                            }
                            catch (Exception e)
                            {
                                _logger.LogError(
                                    ex,
                                    "OnBrazilSendCloudAPIMessageFailed fails conversationId: {ConversationId}, " +
                                    "conversationMessage Checksum: {MessageChecksum}, error: {ExceptionMessage}",
                                    conversation?.Id,
                                    conversationMessage?.MessageChecksum,
                                    ex.Message);
                            }
                        }
                    }
                }

                if (conversationMessageError == null)
                {
                    conversationMessageError = new ()
                    {
                        Code = ex.ErrorCode.ToString(), Message = ex.Message, InnerError = ex.Output.ErrorContext
                    };
                }

                conversationMessage.Metadata ??= new Dictionary<string, object>();
                conversationMessage.Metadata.Add(
                    "errors",
                    new List<ConversationMessageError>
                    {
                        conversationMessageError
                    });
            }
        }
        catch (Exception ex)
        {
            conversationMessage.Status = MessageStatus.Failed;

            _logger.LogError(
                ex,
                "[WhatsappCloudApiChannelMessageHandler] Error processing send message");

            _conversationMeters.IncrementCounter(ChannelTypes.WhatsappCloudApi, ConversationMeterOptions.SendFailed);
        }

        conversationMessage.IsSentFromSleekflow = true;
        conversation.LastMessageChannelId = whatsappCloudApiConfig.Id;
        conversation.LastChannelIdentityId = conversationMessage.ChannelIdentityId;
        await _appDbContext.SaveChangesAsync();

        if (conversationMessage.Status != inititalMessageStatus)
        {
            await _userProfileHooks.OnMessageStatusUpdatedAsync(
                conversation.CompanyId,
                conversation.UserProfileId,
                conversation,
                conversationMessage);
        }

        return conversationMessage;
    }

    // Some really old Brazil whatsapp account does not have "9" added, but our app would add "9" for all Brazil number
    // causing us failed to send msg to them
    // Solution:
    // 1. add an internal note to our user indicating this issue and change the phone number
    // 2. resend the msg
    private async Task<ConversationMessage> OnBrazilSendCloudAPIMessageFailed(
        Conversation conversation,
        ConversationMessage conversationMessage)
    {
        var targetedConversation = await _appDbContext.Conversations
            .Where(x => x.Id == conversation.Id)
            .Include(x => x.UserProfile)
            .ThenInclude(x => x.WhatsappCloudApiUser)
            .FirstOrDefaultAsync();
        var targetedContact = targetedConversation.UserProfile;

        if (targetedContact == default || targetedContact.WhatsappCloudApiUser == null)
        {
            throw new NullReferenceException(nameof(targetedContact) + "or its cloudAPI sender is null");
        }

        targetedContact.PhoneNumber = targetedContact.PhoneNumber.Remove(4, 1);
        targetedContact.WhatsappCloudApiUser.WhatsappId = targetedContact.PhoneNumber;

        try
        {
            await _appDbContext.SaveChangesAsync();
        }
        catch (DbUpdateException ex)
        {
            _logger.LogError(
                ex,
                "OnBrazilSendCloudAPIMessageFailed: fails to save the update phone number: {PhoneNumber} conversationId: {ConversationId}",
                targetedContact.PhoneNumber,
                conversation?.Id);

            throw;
        }

        // Note content:
        // [Automatically generated] This client's Whatsapp account number does not match with the standard Brazil phone number format.
        // Changed phone number stored in Sleekflow to match client's Whatsapp number (removed the 9)
        var note = new ConversationNoteViewModel
        {
            MessageGroupName = conversation.CompanyId,
            MessageContent =
                $"[Gerado automaticamente] O número da conta do Whatsapp deste cliente não corresponde ao formato de número de telefone padrão do Brasil. " +
                $"Número de telefone alterado armazenado no Sleekflow para corresponder ao número do Whatsapp do cliente (removido o 9)",
            ConversationId = conversation.Id,
            MessageChecksum = Guid.NewGuid().ToString(),
            MessageType = "text"
        };

        var noteMessage = _mapper.Map<ConversationMessage>(note);

        noteMessage = await SendConversationNote(
            conversation.CompanyId,
            conversation.Id,
            conversationMessage?.SenderId,
            noteMessage,
            note);

        _logger.LogInformation(
            "Resending Brazil cloud API message using the updated phone number, new phoneNumber: {PhoneNumber}",
            targetedContact.PhoneNumber);
        var result = await SendChannelMessageAsync(conversation, conversationMessage);

        return result;
    }

    private async Task<ConversationMessage> SendConversationNote(
        string companyId,
        string conversationId,
        string staffId,
        ConversationMessage conversationMessage,
        ConversationNoteViewModel conversationNoteViewModel)
    {
        if (string.IsNullOrEmpty(conversationMessage.MessageContent) && conversationNoteViewModel?.files == null)
        {
            return null;
        }

        var conversation = await _appDbContext.Conversations
            .Where(x => x.CompanyId == companyId && x.Id == conversationId).FirstOrDefaultAsync();
        conversationMessage.Channel = ChannelTypes.Note;
        conversationMessage.ConversationId = conversationId;
        conversationMessage.CompanyId = companyId;
        conversationMessage.IsSentFromSleekflow = true;
        conversationMessage.LocalTimestamp = conversationNoteViewModel?.LocalTimestamp;

        if (conversation == null)
        {
            throw new Exception("conversation not found");
        }

        if (!string.IsNullOrEmpty(conversationNoteViewModel?.AssigneeId))
        {
            conversationMessage.MessageAssignee = await _appDbContext.UserRoleStaffs
                .Where(x => x.CompanyId == companyId && x.IdentityId == conversationNoteViewModel.AssigneeId)
                .Include(x => x.Identity).FirstOrDefaultAsync();
        }

        if (!string.IsNullOrEmpty(staffId))
        {
            conversationMessage.SenderId = staffId;

            conversationMessage.Sender = await _appDbContext.UserRoleStaffs
                .Where(x => x.CompanyId == companyId && x.IdentityId == staffId).Include(x => x.Identity)
                .Select(x => x.Identity).FirstOrDefaultAsync();
        }

        await _appDbContext.ConversationMessages.AddAsync(conversationMessage);

        conversation.UpdatedTime = DateTime.UtcNow;
        conversation.ModifiedAt = DateTime.UtcNow;

        conversationMessage.Visibility = "private";
        conversationMessage.Status = MessageStatus.Read;
        await _appDbContext.SaveChangesAsync();

        conversation.LastMessageId = conversationMessage.Id;
        conversation.ActiveStatus = ActiveStatus.Active;

        await _appDbContext.SaveChangesAsync();

        // await SignalRPublishToSubscriberConversationStatusChanged(conversation);
        await _signalRService.SignalROnConversationNoteReceived(conversation, conversationMessage);
        await _emailNotificationService.NewNoteNotification(conversation, conversationMessage);

        return conversationMessage;
    }
}