using System.Collections.Generic;
using Newtonsoft.Json;

namespace Travis_backend.ContactDomain.ViewModels;

public class NewProfileViewModel
{
    public string FirstName { get; set; }

    public string LastName { get; set; }

    [JsonProperty("PhoneNumber")]
    public string PhoneNumber { set { WhatsAppPhoneNumber = value; } }

    [JsonProperty("WhatsAppPhoneNumber")]
    public string WhatsAppPhoneNumber { get; set; }

    public string Email { get; set; }

    public List<AddCustomFieldsViewModel> UserProfileFields { get; set; } = new List<AddCustomFieldsViewModel>();

    public List<string> Labels { get; set; } = new List<string>();

    public List<string> AddLabels { get; set; } = new List<string>();

    public List<string> RemoveLabels { get; set; } = new List<string>();

    public List<long> ListIds { get; set; } = new List<long>();
}