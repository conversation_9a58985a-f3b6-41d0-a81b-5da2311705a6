namespace Travis_backend.Enums;

public enum SupportedOperator
{
    Equals = 0,
    HigherThan = 1,
    <PERSON><PERSON>han = 2,
    Contains = 3,
    IsNotNull = 4,
    IsNull = 5,
    IsNotContains = 6,
    DateBeforeDayAgo = 7,
    DateAfterDayAgo = 8,
    TimeBefore = 9,
    TimeAfter = 10,
    IsBetween = 11,
    IsNotBetween = 12,
    DayOfWeek = 13,
    IsExactlyDaysBefore = 14,
    IsExactlyDaysAfter = 15,
    IsAway = 16,
    IsActive = 17,
    IsToday = 18,
    RegexMatched = 19,
    ContainsExactly = 20,
    IsNotContainsExactly = 21,
    ContainsAll = 22,
    IsNotContainsAll = 23,
    ContainsAny = 24,
    IsNotContainsAny = 25,
    IsChanged = 26,
    Included = 500,
    NotIncluded = 501,
    Equal = 502,
    NotEqual = 503,
    IsNotNullOrEmpty = 504,
    GroupBy = 505,
    StringIsNullOrEmpty = 506,
    StringIsNotNullOrEmpty = 507,
    DateTimeBefore = 509,
    DateTimeAfter = 510,
    TimeBetween = 511,
    TimeNotBetween = 512,
    DateTimeBetween = 513,
    IncludeSubQuery = 514,
    NumberHigherThan = 515,
    NumberLessThan = 516,
    LikeOrMultipleValues = 517,
    IsNotLikeOrMultipleValues = 518
}