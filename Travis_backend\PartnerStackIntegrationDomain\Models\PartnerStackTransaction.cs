using System;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Travis_backend.Helpers;

namespace Travis_backend.PartnerStackIntegrationDomain.Models;

public class PartnerStackTransaction
{
    [JsonProperty("created_at")]
    [JsonConverter(typeof(JsonConvertHelper.EpochConverter))]
    public DateTime CreatedAt { get; set; }

    [JsonProperty("key")]
    public string Key { get; set; }

    [JsonProperty("updated_at")]
    [JsonConverter(typeof(JsonConvertHelper.EpochConverter))]
    public DateTime UpdatedAt { get; set; }

    [JsonProperty("amount")]
    public int Amount { get; set; }

    [JsonProperty("category_key")]
    public string CategoryKey { get; set; }

    [JsonProperty("currency")]
    public string Currency { get; set; }

    [JsonProperty("customer_key")]
    public string CustomerKey { get; set; }

    [JsonProperty("metadata")]
    public JObject Metadata { get; set; }

    [Json<PERSON>roperty("product_key")]
    public string ProductKey { get; set; }

    [JsonProperty("amount_usd")]
    public int AmountUsd { get; set; }

    [JsonProperty("approved")]
    public bool Approved { get; set; }

    [JsonProperty("customer")]
    public PartnerStackTransactionCustomerDto Customer { get; set; }

    [JsonProperty("extension")]
    public JObject Extension { get; set; }
}