using System.Threading.Tasks;
using Sleekflow.Apis.FlowHub.Api;
using Sleekflow.Apis.FlowHub.Model;

namespace Travis_backend.FlowHubs;

public class FlowHubConfigService : IFlowHubConfigService
{
    private readonly IFlowHubConfigsApi _flowHubConfigsApi;

    public FlowHubConfigService(IFlowHubConfigsApi flowHubConfigsApi)
    {
        _flowHubConfigsApi = flowHubConfigsApi;
    }

    /// <inheritdoc />
    public async Task<FlowHubConfig> GetFlowHubConfigAsync(string companyId)
    {
        var flowHubConfigOutput = await _flowHubConfigsApi.FlowHubConfigsGetFlowHubConfigPostAsync(
            getFlowHubConfigInput: new GetFlowHubConfigInput(companyId));

        var flowHubConfig = flowHubConfigOutput.Data.FlowHubConfig;
        flowHubConfig.UsageLimit ??= new UsageLimit(0, 0, 0, 0);
        flowHubConfig.UsageLimitOffset ??= new UsageLimitOffset(0, 0, 0);

        return flowHubConfig;
    }

    /// <inheritdoc />
    public async Task<FlowHubConfig> ToggleFlowHubUsageLimitAsync(string companyId, bool isEnable)
    {
        var input = new ToggleFlowHubUsageLimitInput(companyId, isEnable);
        var toggleFlowHubUsageLimitOutput = await _flowHubConfigsApi.FlowHubConfigsToggleFlowHubUsageLimitPostAsync(toggleFlowHubUsageLimitInput: input);

        return toggleFlowHubUsageLimitOutput.Data.FlowHubConfig;
    }
}