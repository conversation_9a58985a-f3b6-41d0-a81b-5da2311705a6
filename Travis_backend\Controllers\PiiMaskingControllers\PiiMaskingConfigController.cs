using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;
using Hangfire;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Sleekflow.Apis.AuditHub.Api;
using Sleekflow.Apis.AuditHub.Model;
using Travis_backend.AccountAuthenticationDomain.Models;
using Travis_backend.Cache;
using Travis_backend.Cache.Models.CacheKeyPatterns;
using Travis_backend.CommonDomain.ViewModels;
using Travis_backend.ConversationServices;
using Travis_backend.**********************;
using Travis_backend.Enums;
using Travis_backend.PiiMasking.Models;
using Travis_backend.PiiMaskingDomain.Repositories;
using Travis_backend.PiiMaskingDomain.Services;

namespace Travis_backend.Controllers.PiiMaskingControllers;

[Authorize]
[Route("PiiMaskingConfig")]
public class PiiMaskingConfigController : ControllerBase
{
    private readonly ICacheManagerService _cacheManagerService;
    private readonly ICoreService _coreService;
    private readonly IPiiMaskingConfigRepository _piiMaskingConfigRepository;
    private readonly IPiiMaskingService _piiMaskingService;
    private readonly UserManager<ApplicationUser> _userManager;
    private readonly IDistributedInvocationContextService _distributedInvocationContextService;

    public PiiMaskingConfigController(
        ICacheManagerService cacheManagerService,
        ICoreService coreService,
        IPiiMaskingConfigRepository piiMaskingConfigRepository,
        IPiiMaskingService piiMaskingService,
        UserManager<ApplicationUser> userManager,
        IDistributedInvocationContextService distributedInvocationContextService)
    {
        _cacheManagerService = cacheManagerService;
        _coreService = coreService;
        _piiMaskingConfigRepository = piiMaskingConfigRepository;
        _piiMaskingService = piiMaskingService;
        _userManager = userManager;
        _distributedInvocationContextService = distributedInvocationContextService;
    }

    public class CreateConfigInput
    {
        [Required]
        [JsonProperty("display_name")]
        public string DisplayName { get; set; }

        [Required]
        [JsonProperty("regex_patterns")]
        public string[] RegexPatterns { get; set; }

        [Required]
        [JsonProperty("masking_locations")]
        public string[] MaskingLocations { get; set; }

        [Required]
        [JsonProperty("masking_roles")]
        public string[] MaskingRoles { get; set; }

        [JsonProperty("masking_custom_object_schema_ids")]
        public string[] MaskingCustomObjectSchemaIds { get; set; }

        [JsonProperty("is_platform_api_masked")]
        public bool IsPlatformApiMasked { get; set; }
    }

    public class CreateConfigOutput
    {
        [JsonProperty("id")]
        public long Id { get; set; }
    }

    [HttpPost("CreateConfig")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult> CreateConfig(
        [FromBody] CreateConfigInput input)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(InvalidModelStateResponse.FromModelState(ModelState));
        }

        var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (companyUser == null || companyUser.RoleType != StaffUserRole.Admin)
        {
            return Unauthorized();
        }

        var locations = MaskingLocations.None;

        if (input.MaskingLocations.Length > 0
            && !Enum.TryParse(
                string.Join(",", input.MaskingLocations),
                out locations))
        {
            return BadRequest("Invalid masking locations.");
        }

        if (!Enum.TryParse(
                string.Join(",", input.MaskingRoles),
                out MaskingRoles roles))
        {
            return BadRequest("Invalid masking roles.");
        }

        var validatedPatterns = new List<string>();
        foreach (var pattern in input.RegexPatterns)
        {
            try
            {
                var regex = new Regex(pattern.Trim());
                validatedPatterns.Add(pattern.Trim());
            }
            catch (Exception)
            {
                return BadRequest("One or more regex patterns are invalid.");
            }
        }

        var config = new PiiMaskingConfig
        {
            DisplayName = input.DisplayName.Trim(),
            CompanyId = companyUser.CompanyId,
            RegexPatterns = validatedPatterns,
            MaskingCustomObjectSchemaIds = input.MaskingCustomObjectSchemaIds?.ToList(),
            MaskingLocations = locations,
            MaskingRoles = roles,
            IsPlatformApiMasked = input.IsPlatformApiMasked
        };

        await _piiMaskingConfigRepository.CreateConfigAsync(config);

        BackgroundJob.Enqueue<ISystemAuditLogsApi>(
            x => x.SystemAuditLogsCreateSystemAuditLogPostAsync(
                null,
                _distributedInvocationContextService.GetSerializedContextHeader(),
                new CreateSystemAuditLogInput(
                    null,
                    "pii-masking-config-created",
                    new PiiMaskingConfigCreatedSystemLogData(
                        config.Id,
                        config.DisplayName,
                        config.RegexPatterns,
                        config.MaskingCustomObjectSchemaIds,
                        (int)config.MaskingRoles,
                        (int)config.MaskingLocations,
                        config.IsPlatformApiMasked)),
                CancellationToken.None));

        await _cacheManagerService.DeleteCacheAsync(new PiiMaskingConfigCacheKeyPattern(companyUser.CompanyId));

        return Ok(new CreateConfigOutput { Id = config.Id });
    }

    public class UpdateConfigInput
    {
        [Required]
        [JsonProperty("id")]
        public long Id { get; set; }

        [Required]
        [JsonProperty("display_name")]
        public string DisplayName { get; set; }

        [Required]
        [JsonProperty("regex_patterns")]
        public string[] RegexPatterns { get; set; }

        [Required]
        [JsonProperty("masking_locations")]
        public string[] MaskingLocations { get; set; }

        [Required]
        [JsonProperty("masking_roles")]
        public string[] MaskingRoles { get; set; }

        [JsonProperty("masking_custom_object_schema_ids")]
        public string[] MaskingCustomObjectSchemaIds { get; set; }

        [JsonProperty("is_platform_api_masked")]
        public bool IsPlatformApiMasked { get; set; }
    }

    [HttpPost("UpdateConfig")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult> UpdateConfig(
        [FromBody] UpdateConfigInput input)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(InvalidModelStateResponse.FromModelState(ModelState));
        }

        var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (companyUser == null || companyUser.RoleType != StaffUserRole.Admin)
        {
            return Unauthorized();
        }

        var locations = MaskingLocations.None;

        if (input.MaskingLocations.Length > 0
            && !Enum.TryParse(
                string.Join(",", input.MaskingLocations),
                out locations))
        {
            return BadRequest("Invalid masking locations.");
        }

        if (!Enum.TryParse(
                string.Join(",", input.MaskingRoles),
                out MaskingRoles roles))
        {
            return BadRequest("Invalid masking roles.");
        }

        var config = await _piiMaskingConfigRepository.FindConfigAsync(companyUser.CompanyId, input.Id);
        if (config is null)
        {
            return NotFound("Config not found.");
        }

        var validatedPatterns = new List<string>();
        foreach (var pattern in input.RegexPatterns)
        {
            try
            {
                var regex = new Regex(pattern.Trim());
                validatedPatterns.Add(pattern.Trim());
            }
            catch (Exception)
            {
                return BadRequest("One or more regex patterns are invalid.");
            }
        }

        config.DisplayName = input.DisplayName.Trim();
        config.RegexPatterns = validatedPatterns;
        config.MaskingLocations = locations;
        config.MaskingRoles = roles;
        config.MaskingCustomObjectSchemaIds = input.MaskingCustomObjectSchemaIds?.ToList();
        config.IsPlatformApiMasked = input.IsPlatformApiMasked;
        config.UpdatedAt = DateTime.UtcNow;

        await _piiMaskingConfigRepository.UpdateConfigAsync(config);

        BackgroundJob.Enqueue<ISystemAuditLogsApi>(
            x => x.SystemAuditLogsCreateSystemAuditLogPostAsync(
                null,
                _distributedInvocationContextService.GetSerializedContextHeader(),
                new CreateSystemAuditLogInput(
                    null,
                    "pii-masking-config-updated",
                    new PiiMaskingConfigUpdatedSystemLogData(
                        config.Id,
                        config.DisplayName,
                        config.RegexPatterns,
                        config.MaskingCustomObjectSchemaIds,
                        (int)config.MaskingRoles,
                        (int)config.MaskingLocations,
                        config.IsPlatformApiMasked)),
                CancellationToken.None));

        await _cacheManagerService.DeleteCacheAsync(new PiiMaskingConfigCacheKeyPattern(companyUser.CompanyId));

        return Ok();
    }

    public class DeleteConfigInput
    {
        [Required]
        [JsonProperty("id")]
        public long Id { get; set; }
    }

    [HttpPost("DeleteConfig")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult> DeleteConfig(
        [FromBody] DeleteConfigInput input)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(InvalidModelStateResponse.FromModelState(ModelState));
        }

        var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (companyUser == null || companyUser.RoleType != StaffUserRole.Admin)
        {
            return Unauthorized();
        }

        var config = await _piiMaskingConfigRepository.FindConfigAsync(companyUser.CompanyId, input.Id);
        if (config is null)
        {
            return NotFound("Config not found.");
        }

        await _piiMaskingConfigRepository.DeleteConfigAsync(config);

        BackgroundJob.Enqueue<ISystemAuditLogsApi>(
            x => x.SystemAuditLogsCreateSystemAuditLogPostAsync(
                null,
                _distributedInvocationContextService.GetSerializedContextHeader(),
                new CreateSystemAuditLogInput(
                    null,
                    "pii-masking-config-deleted",
                    new PiiMaskingConfigDeletedSystemLogData(
                        config.Id,
                        config.DisplayName)),
                CancellationToken.None));

        await _cacheManagerService.DeleteCacheAsync(new PiiMaskingConfigCacheKeyPattern(companyUser.CompanyId));

        return Ok();
    }

    public class SearchInput
    {
        [JsonProperty("display_name")]
        public string DisplayName { get; set; }

        [JsonProperty("masking_locations")]
        public string[] MaskingLocations { get; set; }

        [JsonProperty("masking_roles")]
        public string[] MaskingRoles { get; set; }

        [JsonProperty("offset")]
        public int Offset { get; set; } = 0;

        [JsonProperty("limit")]
        public int Limit { get; set; } = 10;
    }

    public class SearchOutput
    {
        [JsonProperty("configs")]
        public List<PiiMaskingConfigDto> Configs { get; set; }

        [JsonProperty("total_result")]
        public int TotalResult { get; set; }

        public class PiiMaskingConfigDto
        {
            [JsonProperty("id")]
            public long Id { get; set; }

            [JsonProperty("display_name")]
            public string DisplayName { get; set; }

            [JsonProperty("company_id")]
            public string CompanyId { get; set; }

            [JsonProperty("regex_patterns")]
            public string[] RegexPatterns { get; set; }

            [JsonProperty("masking_locations")]
            public string[] MaskingLocations { get; set; }

            [JsonProperty("masking_roles")]
            public string[] MaskingRoles { get; set; }

            [JsonProperty("masking_custom_object_schema_ids")]
            public string[] MaskingCustomObjectSchemaIds { get; set; }

            [JsonProperty("is_platform_api_masked")]
            public bool IsPlatformApiMasked { get; set; }

            [JsonProperty("created_at")]
            public DateTime CreatedAt { get; set; }
        }
    }

    [HttpPost("Search")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult> Search(
        [FromBody] SearchInput input)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(InvalidModelStateResponse.FromModelState(ModelState));
        }

        var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (companyUser == null || companyUser.RoleType != StaffUserRole.Admin)
        {
            return Unauthorized();
        }

        MaskingLocations locations = MaskingLocations.None;

        if (input.MaskingLocations is not null)
        {
            if (input.MaskingLocations.Length == 0)
            {
                locations = MaskingLocations.None;
            }
            else if (!Enum.TryParse(string.Join(",", input.MaskingLocations), out locations))
            {
                return BadRequest("Invalid masking locations.");
            }
        }

        var roles = MaskingRoles.None;

        if (input.MaskingRoles is not null
            && !Enum.TryParse(
                string.Join(",", input.MaskingRoles),
                out roles))
        {
            return BadRequest("Invalid masking roles.");
        }

        var (configs, totalResult) = await _piiMaskingConfigRepository.GetConfigsAsync(
            companyUser.CompanyId,
            input.DisplayName,
            input.MaskingLocations is null
                ? null
                : locations,
            roles,
            input.Offset,
            input.Limit);

        var output = new SearchOutput
        {
            Configs = configs.Select(x => new SearchOutput.PiiMaskingConfigDto
            {
                Id = x.Id,
                DisplayName = x.DisplayName,
                CompanyId = x.CompanyId,
                RegexPatterns = x.RegexPatterns.ToArray(),
                MaskingLocations = x.MaskingLocations == MaskingLocations.None
                    ? []
                    : x.MaskingLocations.ToString().Split(", "),
                MaskingRoles = x.MaskingRoles.ToString().Split(", "),
                MaskingCustomObjectSchemaIds = x.MaskingCustomObjectSchemaIds?.ToArray(),
                IsPlatformApiMasked = x.IsPlatformApiMasked,
                CreatedAt = x.CreatedAt
            })
            .ToList(),

            TotalResult = totalResult
        };

        return Ok(output);
    }

    public class TestInput
    {
        [Required]
        [JsonProperty("regex_patterns")]
        public string[] RegexPatterns { get; set; }

        [Required]
        [JsonProperty("test_message")]
        public string TestMessage { get; set; }
    }

    public class TestOutput
    {
        [JsonProperty("masked_message")]
        public string MaskedMessage { get; set; }
    }

    [HttpPost("Test")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult> Test(
        [FromBody] TestInput input)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(InvalidModelStateResponse.FromModelState(ModelState));
        }

        var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (companyUser == null || companyUser.RoleType != StaffUserRole.Admin)
        {
            return Unauthorized();
        }

        var regexes = new List<Regex>();

        try
        {
            regexes = input.RegexPatterns
                .Select(x => new Regex(x))
                .ToList();
        }
        catch (Exception)
        {
            return BadRequest("One or more regex patterns are invalid.");
        }

        var output = new TestOutput
        {
            MaskedMessage = _piiMaskingService.Mask(input.TestMessage, regexes)
        };

        return Ok(output);
    }

    public class BulkUpdateConfigsInput
    {
        [Required]
        [JsonProperty("ids")]
        public long[] Ids { get; set; }

        [JsonProperty("masking_locations")]
        public string[] MaskingLocations { get; set; }

        [JsonProperty("masking_roles")]
        public string[] MaskingRoles { get; set; }

        [JsonProperty("masking_custom_object_schema_ids")]
        public string[] MaskingCustomObjectSchemaIds { get; set; }

        [JsonProperty("is_platform_api_masked")]
        public bool? IsPlatformApiMasked { get; set; }
    }

    [HttpPost("BulkUpdateConfigs")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult> BulkUpdateConfigs(
        [FromBody] BulkUpdateConfigsInput input)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(InvalidModelStateResponse.FromModelState(ModelState));
        }

        var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (companyUser == null || companyUser.RoleType != StaffUserRole.Admin)
        {
            return Unauthorized();
        }

        var locations = MaskingLocations.None;
        var roles = MaskingRoles.None;

        if (input.MaskingLocations is not null
            && input.MaskingLocations.Length > 0
            && !Enum.TryParse(
                string.Join(",", input.MaskingLocations),
                out locations))
        {
            return BadRequest("Invalid masking locations.");
        }

        if (input.MaskingRoles is not null
            && !Enum.TryParse(
                string.Join(",", input.MaskingRoles),
                out roles))
        {
            return BadRequest("Invalid masking roles.");
        }

        var configs = await _piiMaskingConfigRepository.GetConfigsAsync(
            companyUser.CompanyId,
            input.Ids);

        foreach (var config in configs)
        {
            var hasUpdate = false;

            if (input.MaskingLocations is not null)
            {
                config.MaskingLocations = locations;
                hasUpdate = true;
            }

            if (input.MaskingRoles is not null)
            {
                config.MaskingRoles = roles;
                hasUpdate = true;
            }

            if (input.MaskingCustomObjectSchemaIds is not null)
            {
                config.MaskingCustomObjectSchemaIds = input.MaskingCustomObjectSchemaIds.ToList();
                hasUpdate = true;
            }

            if (input.IsPlatformApiMasked is not null)
            {
                config.IsPlatformApiMasked = input.IsPlatformApiMasked.Value;
                hasUpdate = true;
            }

            if (hasUpdate)
            {
                config.UpdatedAt = DateTime.UtcNow;
                await _piiMaskingConfigRepository.UpdateConfigAsync(config);

                BackgroundJob.Enqueue<ISystemAuditLogsApi>(
                    x => x.SystemAuditLogsCreateSystemAuditLogPostAsync(
                        null,
                        _distributedInvocationContextService.GetSerializedContextHeader(),
                        new CreateSystemAuditLogInput(
                            null,
                            "pii-masking-config-updated",
                            new PiiMaskingConfigUpdatedSystemLogData(
                                config.Id,
                                config.DisplayName,
                                config.RegexPatterns,
                                config.MaskingCustomObjectSchemaIds,
                                (int)config.MaskingRoles,
                                (int)config.MaskingLocations,
                                config.IsPlatformApiMasked)),
                        CancellationToken.None));
            }
        }

        await _cacheManagerService.DeleteCacheAsync(new PiiMaskingConfigCacheKeyPattern(companyUser.CompanyId));

        return Ok();
    }

    public class BulkDeleteConfigsInput
    {
        [Required]
        [JsonProperty("ids")]
        public long[] Ids { get; set; }
    }

    [HttpPost("BulkDeleteConfigs")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult> BulkDeleteConfigs(
        [FromBody] BulkDeleteConfigsInput input)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(InvalidModelStateResponse.FromModelState(ModelState));
        }

        var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (companyUser == null || companyUser.RoleType != StaffUserRole.Admin)
        {
            return Unauthorized();
        }

        var configs = await _piiMaskingConfigRepository.GetConfigsAsync(
            companyUser.CompanyId,
            input.Ids);

        foreach (var config in configs)
        {
            await _piiMaskingConfigRepository.DeleteConfigAsync(config);

            BackgroundJob.Enqueue<ISystemAuditLogsApi>(
                x => x.SystemAuditLogsCreateSystemAuditLogPostAsync(
                    null,
                    _distributedInvocationContextService.GetSerializedContextHeader(),
                    new CreateSystemAuditLogInput(
                        null,
                        "pii-masking-config-deleted",
                        new PiiMaskingConfigDeletedSystemLogData(
                            config.Id,
                            config.DisplayName)),
                    CancellationToken.None));
        }

        await _cacheManagerService.DeleteCacheAsync(new PiiMaskingConfigCacheKeyPattern(companyUser.CompanyId));

        return Ok();
    }

    public class GetMaskingLocationOptionsOutput
    {
        [JsonProperty("masking_location_options")]
        public string[] MaskingLocationOptions { get; set; }
    }

    [HttpPost("GetMaskingLocationOptions")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult> GetMaskingLocationOptions()
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(InvalidModelStateResponse.FromModelState(ModelState));
        }

        var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (companyUser == null || companyUser.RoleType != StaffUserRole.Admin)
        {
            return Unauthorized();
        }

        var output = new GetMaskingLocationOptionsOutput
        {
            MaskingLocationOptions = (MaskingLocations.IncomingMessage | MaskingLocations.Contact)
                .ToString()
                .Split(", ")
        };

        return Ok(output);
    }

    public class GetMaskingRoleOptionsOutput
    {
        [JsonProperty("masking_role_options")]
        public string[] MaskingRoleOptions { get; set; }
    }

    [HttpPost("GetMaskingRoleOptions")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult> GetMaskingRoleOptions()
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(InvalidModelStateResponse.FromModelState(ModelState));
        }

        var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (companyUser == null || companyUser.RoleType != StaffUserRole.Admin)
        {
            return Unauthorized();
        }

        var output = new GetMaskingRoleOptionsOutput
        {
            MaskingRoleOptions = (MaskingRoles.Admin | MaskingRoles.TeamAdmin | MaskingRoles.Staff)
                .ToString()
                .Split(", ")
        };

        return Ok(output);
    }
}