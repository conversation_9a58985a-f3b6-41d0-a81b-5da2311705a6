﻿using Hangfire.Client;
using Hangfire.Common;
using Hangfire.Logging;
using Hangfire.Server;
using Hangfire.States;
using Hangfire.Storage;
using Serilog.Context;
using Travis_backend.Enums;
using Travis_backend.OpenTelemetry.Meters;

namespace Travis_backend.BackgroundTaskServices.Attributes;

public class BackgroundJobFilterAttribute : JobFilterAttribute, IServerFilter, IClientFilter, IApplyStateFilter
{
    private static readonly ILog Logger = LogProvider.GetCurrentClassLogger();
    private readonly IHangfireMeters _hangfireMeters;

    public BackgroundJobFilterAttribute(IHangfireMeters hangfireMeters)
    {
        _hangfireMeters = hangfireMeters;
    }

    public void OnCreating(CreatingContext context)
    {
        Logger.InfoFormat(
            "[Background Job Creating] Job name: `{0}`",
            context.Job.Method.Name);
    }

    public void OnCreated(CreatedContext context)
    {
        _hangfireMeters.IncrementCounter(HangfireStateTypes.Scheduled);
        Logger.InfoFormat(
            "[Background Job Created] Job name: `{0}`, Job Id: `{1}`",
            context.Job.Method.Name,
            context.BackgroundJob?.Id);
    }

    public void OnPerforming(PerformingContext context)
    {
        var backgroundJob = context.BackgroundJob;
        LogContext.PushProperty("SleekflowBackgroundJobId", backgroundJob.Id);
        LogContext.PushProperty("SleekflowBackgroundJobName", backgroundJob.Job.Method.Name);

        Logger.InfoFormat(
            "[Background Job Started] - Job Name: `{0}`, Job Id: `{1}` ",
            backgroundJob.Job.Method.Name,
            backgroundJob.Id);

        _hangfireMeters.IncrementCounter(HangfireStateTypes.Processing);
    }

    public void OnPerformed(PerformedContext context)
    {
        var backgroundJob = context.BackgroundJob;
        Logger.InfoFormat(
            "[Background Job Completed] - Job Name: `{0}`, Job Id: `{1}` ",
            backgroundJob.Job.Method.Name,
            backgroundJob.Id);
    }

    public void OnStateUnapplied(ApplyStateContext context, IWriteOnlyTransaction transaction)
    {
        Logger.InfoFormat(
            "Job `{0}` state `{1}` was unapplied.",
            context.BackgroundJob.Id,
            context.OldStateName);
    }

    public void OnStateApplied(ApplyStateContext context, IWriteOnlyTransaction transaction)
    {
        var backgroundJob = context.BackgroundJob;

        switch (context.NewState)
        {
            case SucceededState succeededState:
                Logger.InfoFormat(
                    "[Background Job Succeeded] - Job Name: `{0}`, Job Id: `{1}`, Latency: `{2}ms`, Performance Duration: `{3}ms`",
                    backgroundJob.Job.Method.Name,
                    backgroundJob.Id,
                    succeededState.Latency,
                    succeededState.PerformanceDuration);
                _hangfireMeters.IncrementCounter(HangfireStateTypes.Succeeded);
                break;

            case FailedState:
                Logger.InfoFormat(
                    "[Background Job Failed] - Job Name: `{0}`, Job Id: `{1}` ",
                    backgroundJob.Job.Method.Name,
                    backgroundJob.Id);
                _hangfireMeters.IncrementCounter(HangfireStateTypes.Failed);
                break;

            case DeletedState:
                Logger.InfoFormat(
                    "[Background Job Deleted] - Job Name: `{0}`, Job Id: `{1}` ",
                    backgroundJob.Job.Method.Name,
                    backgroundJob.Id);
                _hangfireMeters.IncrementCounter(HangfireStateTypes.Deleted);
                break;
        }
    }
}