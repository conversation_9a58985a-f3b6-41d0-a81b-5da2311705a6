﻿using Travis_backend.ChannelDomain.Models;
using Travis_backend.CompanyDomain.ViewModels;
using Travis_backend.ContactDomain.ViewModels;
using Travis_backend.InternalDomain.ViewModels;
using Twilio.Rest.Api.V2010;

namespace Sleekflow.Powerflow.Apis.ViewModels;

public class GetCompanyIdNamePairResponse
{
    public List<CompanyIdNamePair> CompanyIdsNamePairs { get; set; }
}

public class CompanyIdNamePair
{
    public string CompanyId { get; set; }

    public string CompanyName { get; set; }
}

public class GetAllCmsSelectionsResponse
{
    public List<CmsUserDto> CompanyOwners { get; set; } = new ();

    public List<CmsUserDto> SalesContactOwners { get; set; } = new ();

    public List<CmsUserDto> CustomerSuccessContactOwners { get; set; } = new ();

    public List<string> AllCmsLeadSources { get; set; }

    public List<string> AllSubscriptionPlans { get; set; }

    public List<string> AllAddOnPlans { get; set; }

    public List<string> AllCmsIndustries { get; set; }
}

public class CmsCompanyUsageResponse
{
    public CompanyUsage CompanyUsage { get; set; }

    public decimal MonthlyRecurringRevenue { get; set; }

    public List<CmsBillRecordDto> CmsBillingPeriodUsages { get; set; }
}

public class GetAllCompanyDetailsResponse
{
    public CmsCompanyResponse CompanyDetail { get; set; }

    public CmsCompanyUsageResponse CompanyUsage { get; set; }

    public GetCmsCompanyBillRecordsResponse CompanyBillRecord { get; set; }

    public GetCompanyStaffsResponse CompanyStaff { get; set; }
}

public class GetCmsCompanyBillRecordsResponse
{
    public List<CmsBillRecordDto> BillRecords { get; set; } = new ();

    public List<CmsSalesPaymentRecordDto> CmsSalesPaymentRecords { get; set; } = new ();

    public List<CmsDailyRevenueAnalyticDto> DailyAnalytics { get; set; }

    public string InitialPaidDate { get; set; }
}

public class InvestigateCompanyStatusResponse
{
    public string CompanyId { get; set; }

    public string CompanyName { get; set; }

    public bool IsDiving { get; set; }

    public DivingUserInfoResponse User { get; set; }
}

public class GetFacebookConnectedResponse
{
    // public List<FacebookConnectedDto> ConnectedMessenger { get; set; } = new();
    // public List<FacebookConnectedDto> ConnectedLeadAds { get; set; } = new();
    public List<FacebookConnectedDto> FacebookInstances { get; set; } = new ();
}

public class GetTwilioUsageResponse
{
    public List<CmsTwilioUsageDto> ConnectedSubAccounts { get; set; }

    public List<CmsTwilioUsageDto> NotConnectedSubAccounts { get; set; }

    public List<CmsTwilioUsageDto> NonSubAccounts { get; set; }
}

public class SetTwilioCreditResponse
{
    public TwilioUsageRecord TwilioUsageRecord { get; set; }
}

public class GetTwilioTopUpLogResponse
{
    public List<TwilioTopUpLogDto> TwilioTopUpLogs { get; set; }
}

public class UpdateTwilioSubAccountResponse
{
    public AccountResource.StatusEnum AccountStatus;

    public string AccountSid { get; set; }
}

public class IssueCompanyPublicApiKeyResponse
{
    public string ApiKey { get; set; }
}

public class GetCompanyPublicApiKeysResponse
{
    public List<CompanyPublicApiKeyDto> ApiKeys { get; set; }
}

public class GetCompanyStaffsResponse
{
    public List<CmsCompanyStaffDto> CompanyStaffs { get; set; }
}

public class GetResetPasswordResponse
{
    public string Url { get; set; }
}

public class GetLoginAsSecretResponse
{
    public CmsCompanyStaffDto LoginAsStaff { get; set; }

    public string LoginAsSecret { get; set; }

    public DateTime? ExpireAt { get; set; }
}

public class CmsCompanyStaffListResponse
{
    public List<CmsCompanyStaffData> CompanyStaffDataList { get; set; }
}

public class ImportStaffResponse
{
    public ImportSpreadsheet Details { get; set; }

    public List<Dictionary<string, string>> Summary { get; set; }
}