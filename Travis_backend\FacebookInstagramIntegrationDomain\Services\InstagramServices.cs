using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using System.Web;
using Hangfire;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Travis_backend.AutomationDomain.Models;
using Travis_backend.ChannelDomain.Helpers.Instagram;
using Travis_backend.Constants;
using Travis_backend.ContactDomain.Models;
using Travis_backend.ContactDomain.Services;
using Travis_backend.ConversationDomain.ViewModels;
using Travis_backend.ConversationServices.ViewModels;
using Travis_backend.Database;
using Travis_backend.Enums;
using Travis_backend.Extensions;
using Travis_backend.MessageDomain.Models;
using Travis_backend.FlowHubs;
using Travis_backend.FlowHubs.Models;
using PlatformType = Travis_backend.AutomationDomain.Models.PlatformType;

namespace Travis_backend.ConversationServices;

public interface IInstagramService
{
    Task<List<FbIgPostVM>> GetCompanyInstagramMedias(
        string igPageId,
        string access_token,
        DateTime? fromDateTime = null,
        DateTime? toDateTime = null,
        int offset = 0,
        int limit = 100,
        string searchKeyword = null);

    Task<FbIgPostVM> GetaInstagramMediaDetails(string igPageId, string mediaId, string access_token);
    Task<UserProfile> CreateUserProfile(string senderId, string pageId, string userName = null);

    Task AddUserReplyDmHistory(FBMessaging messaging);

    Task<string> GetProfilePictureLink(string pageId, string accessToken);

    Task HandleIgAutoAction(AutomationAction automationAction, Entry entry, Change change, bool isNewContact);

    Task HandleIgIcebreakerDm(long automationActionId, FBMessaging messaging, bool isNewContact);

    Task PreviewDmAutomation(long automationActionId, FBMessaging messaging, bool isNewContact);

    Task<PrivateReplyResponse> PrivateReplyForComment(PmBodyData bodyData, string access_token);

    Task<PrivateReplyResponse> CarouselReplyForComment(CarouselBodyData carouselBodyData, string accessToken);

    Task<PrivateReplyResponse> QuickReplyButtonForComment(
        QuickReplyButtonBodyData quickReplyButtonBodyData,
        string accessToken);

    Task<bool> HasInstagramBusinessAccountAsync(string pageId, string accessToken);
}

public class InstagramService : IInstagramService
{
    private readonly ILogger<InstagramService> _logger;
    private readonly ApplicationDbContext _appDbContext;
    private readonly IConfiguration _configuration;
    private readonly IAzureBlobStorageService _azureBlobStorageService;
    private readonly IUserProfileService _userProfileService;
    private readonly IConversationMessageService _conversationMessageService;
    private readonly IUserProfileHooks _userProfileHooks;
    private readonly IHttpClientFactory _httpClientFactory;

    public InstagramService(
        ILogger<InstagramService> logger,
        ApplicationDbContext appDbContext,
        IConfiguration configuration,
        IAzureBlobStorageService azureBlobStorageService,
        IUserProfileService userProfileService,
        IConversationMessageService conversationMessageService,
        IUserProfileHooks userProfileHooks,
        IHttpClientFactory httpClientFactory)
    {
        _logger = logger;
        _appDbContext = appDbContext;
        _configuration = configuration;
        _azureBlobStorageService = azureBlobStorageService;
        _userProfileService = userProfileService;
        _conversationMessageService = conversationMessageService;
        _userProfileHooks = userProfileHooks;
        _httpClientFactory = httpClientFactory;
    }

    public async Task<List<FbIgPostVM>> GetCompanyInstagramMedias(
        string igPageId,
        string access_token,
        DateTime? fromDateTime = null,
        DateTime? toDateTime = null,
        int offset = 0,
        int limit = 100,
        string searchKeyword = null)
    {
        var instagramMediasVms = new List<FbIgPostVM>();

        var fields = "id, caption, timestamp, media_url";

        var url = $"https://graph.facebook.com/v12.0/{igPageId}/media?fields={fields}&access_token={access_token}";
        var doNext = true;
        var iterationUrl = url;

        var httpClient = _httpClientFactory.CreateClient(HttpClientHandlerName.Default);

        var iterateLimit = 1000;
        var iteration = 0;
        while (doNext && iteration < iterateLimit)
        {
            doNext = false; // avoid infinite loop

            try
            {
                var httpResponse = await httpClient.GetAsync(iterationUrl);
                iteration++;
                if (httpResponse.IsSuccessStatusCode)
                {
                    var result = await httpResponse.Content.ReadAsStringAsync();
                    InstagramMedia ingstagraMedia = JsonConvert.DeserializeObject<InstagramMedia>(result);
                    if (ingstagraMedia != null && ingstagraMedia.MediaData.Any())
                    {
                        foreach (var data in ingstagraMedia.MediaData)
                        {
                            var isPass = string.IsNullOrEmpty(searchKeyword) || data.Caption != null &&
                                data.Caption.ToLower().Contains(searchKeyword.ToLower());
                            if (instagramMediasVms.Count < offset + limit && fromDateTime.HasValue &&
                                toDateTime.HasValue)
                            {
                                if (fromDateTime.Value <= data.CreatedAt && data.CreatedAt <= toDateTime && isPass)
                                {
                                    var companyFbPost = new FbIgPostVM()
                                    {
                                        Id = data.Id,
                                        Content = data.Caption,
                                        CreatedAt = data.CreatedAt,
                                        MediaUrl = data.PictureUrl
                                    };

                                    instagramMediasVms.Add(companyFbPost);
                                }
                            }
                            else if (instagramMediasVms.Count < offset + limit && fromDateTime.HasValue)
                            {
                                if (fromDateTime.Value <= data.CreatedAt && isPass)
                                {
                                    var companyFbPost = new FbIgPostVM()
                                    {
                                        Id = data.Id,
                                        Content = data.Caption,
                                        CreatedAt = data.CreatedAt,
                                        MediaUrl = data.PictureUrl
                                    };

                                    instagramMediasVms.Add(companyFbPost);
                                }
                            }
                            else if (instagramMediasVms.Count < offset + limit && toDateTime.HasValue)
                            {
                                if (toDateTime.Value >= data.CreatedAt && isPass)
                                {
                                    var companyFbPost = new FbIgPostVM()
                                    {
                                        Id = data.Id,
                                        Content = data.Caption,
                                        CreatedAt = data.CreatedAt,
                                        MediaUrl = data.PictureUrl
                                    };

                                    if (!string.IsNullOrEmpty(data.PictureUrl))
                                    {
                                        companyFbPost.MediaUrl = data.PictureUrl;
                                    }

                                    instagramMediasVms.Add(companyFbPost);
                                }
                            }
                            else if (isPass && instagramMediasVms.Count < offset + limit)
                            {
                                var companyFbPost = new FbIgPostVM()
                                {
                                    Id = data.Id,
                                    Content = data.Caption,
                                    CreatedAt = data.CreatedAt,
                                    MediaUrl = data.PictureUrl
                                };

                                instagramMediasVms.Add(companyFbPost);
                            }
                        }

                        if (ingstagraMedia.Paging != null && ingstagraMedia.Paging.FbPostCursors != null &&
                            ingstagraMedia.Paging.FbPostCursors.After != null &&
                            ((instagramMediasVms.Any() && instagramMediasVms.Count < offset + limit) ||
                             (instagramMediasVms.Any() && fromDateTime.HasValue &&
                              fromDateTime.Value < instagramMediasVms.Last().CreatedAt) || (!instagramMediasVms.Any() &&
                                 fromDateTime.HasValue &&
                                 fromDateTime.Value < ingstagraMedia.MediaData.Last().CreatedAt)))
                        {
                            iterationUrl = url + $"&after={ingstagraMedia.Paging.FbPostCursors.After}";
                            doNext = true;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[Instagram {MethodName}] Error for page id {InstagramPageId}. {ExceptionMessage}",
                    nameof(GetCompanyInstagramMedias),
                    igPageId,
                    ex.Message);
            }
        }

        instagramMediasVms = instagramMediasVms.Skip(offset).Take(limit).ToList();
        return instagramMediasVms;
    }

    public async Task<FbIgPostVM> GetaInstagramMediaDetails(
        string igPageId,
        string mediaId,
        string access_token)
    {
        var instagramMediaDetails = new FbIgPostVM();

        if (igPageId == null || mediaId == null || access_token == null)
        {
            return instagramMediaDetails;
        }

        var fields = "id, caption, is_comment_enabled, media_url, timestamp, permalink";

        var url = $"https://graph.facebook.com/v12.0/{mediaId}?fields={fields}&access_token={access_token}";

        try
        {
            var httpClient = _httpClientFactory.CreateClient(HttpClientHandlerName.Default);
            var httpResponse = await httpClient.GetAsync(url);

            if (httpResponse.IsSuccessStatusCode)
            {
                var result = await httpResponse.Content.ReadAsStringAsync();

                MediaData instagramMediaDetailsData = JsonConvert.DeserializeObject<MediaData>(result);

                if (instagramMediaDetailsData == null)
                {
                    return instagramMediaDetails;
                }

                instagramMediaDetails.Id = instagramMediaDetailsData.Id;
                instagramMediaDetails.Content = instagramMediaDetailsData.Caption;
                instagramMediaDetails.CreatedAt = instagramMediaDetailsData.CreatedAt;
                instagramMediaDetails.MediaUrl = instagramMediaDetailsData.PictureUrl;
                instagramMediaDetails.PostUrl = instagramMediaDetailsData.PostUrl;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "[Instagram {MethodName}] Error for media id {InstagramMediaId} in page id {InstagramPageId}. {ExceptionMessage}",
                nameof(GetaInstagramMediaDetails),
                mediaId,
                igPageId,
                ex.Message);
        }

        return instagramMediaDetails;
    }

    private async Task<IgReplyCommentResponse> ReplyCommentInPost(
        string commentId,
        string replyContent,
        string access_token)
    {
        var replyCommentResponse = new IgReplyCommentResponse();
        replyCommentResponse.isSuccess = false;
        var url =
            $"https://graph.facebook.com/{commentId}/replies?message={HttpUtility.UrlEncode(replyContent)}&access_token={access_token}";
        try
        {
            var httpClient = _httpClientFactory.CreateClient(HttpClientHandlerName.Default);
            var httpResponse = await httpClient.PostAsync(url, null);

            if (httpResponse.IsSuccessStatusCode)
            {
                string result = await httpResponse.Content.ReadAsStringAsync();
                FbReplyCommentHttpResponse fbReplyCommentHttpResponse =
                    JsonConvert.DeserializeObject<FbReplyCommentHttpResponse>(result);
                if (fbReplyCommentHttpResponse != null)
                {
                    replyCommentResponse.CommentId = fbReplyCommentHttpResponse.Id;
                    replyCommentResponse.isSuccess = true;
                }
            }
            else
            {
                replyCommentResponse.ErrorMessage = httpResponse.Content.ReadAsStringAsync().Result;
            }

            return replyCommentResponse;
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "[Instagram {MethodName}] Error for comment id {InstagramCommentId}. {ExceptionMessage}",
                nameof(ReplyCommentInPost),
                commentId,
                ex.Message);

            replyCommentResponse.ErrorMessage = ex.Message;
        }

        return replyCommentResponse;
    }

    public async Task<PrivateReplyResponse> PrivateReplyForComment(PmBodyData bodyData, string access_token)
    {
        var privateReplyResponse = new PrivateReplyResponse();
        privateReplyResponse.isSuccess = false;
        var url = $"https://graph.facebook.com/v12.0/me/messages?access_token={access_token}";

        var bodyStr = JsonConvert.SerializeObject(bodyData);
        var bodyJson = new StringContent(bodyStr, Encoding.UTF8, "application/json");

        try
        {
            var httpClient = _httpClientFactory.CreateClient(HttpClientHandlerName.Default);
            var httpResponse = await httpClient.PostAsync(url, bodyJson);

            if (httpResponse.IsSuccessStatusCode)
            {
                string result = await httpResponse.Content.ReadAsStringAsync();
                FbMessageHttpResponse fbMessageHttpResponse =
                    JsonConvert.DeserializeObject<FbMessageHttpResponse>(result);
                if (fbMessageHttpResponse != null)
                {
                    privateReplyResponse.recipientId = fbMessageHttpResponse.RecipientId;
                    privateReplyResponse.messageId = fbMessageHttpResponse.MessageId;
                    privateReplyResponse.isSuccess = true;
                }
            }
            else
            {
                privateReplyResponse.errorMessage = httpResponse.Content.ReadAsStringAsync().Result;
            }

            return privateReplyResponse;
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "[Instagram {MethodName}] Error replying to Instagram user {InstagramUserId}'s comment id {InstagramCommentId}. {ExceptionMessage}",
                nameof(PrivateReplyForComment),
                bodyData.Recipient.Id,
                bodyData.Recipient.CommentId,
                ex.Message);

            privateReplyResponse.errorMessage = ex.Message;
        }

        return privateReplyResponse;
    }

    public async Task<PrivateReplyResponse> CarouselReplyForComment(
        CarouselBodyData carouselBodyData,
        string accessToken)
    {
        var privateReplyResponse = new PrivateReplyResponse();
        privateReplyResponse.isSuccess = false;
        var url = $"https://graph.facebook.com/v12.0/me/messages?access_token={accessToken}";

        var bodyStr = JsonConvert.SerializeObject(carouselBodyData);
        var bodyJson = new StringContent(bodyStr, Encoding.UTF8, "application/json");

        try
        {
            var httpClient = _httpClientFactory.CreateClient(HttpClientHandlerName.Default);
            var httpResponse = await httpClient.PostAsync(url, bodyJson);

            if (httpResponse.IsSuccessStatusCode)
            {
                string result = await httpResponse.Content.ReadAsStringAsync();
                FbMessageHttpResponse fbMessageHttpResponse =
                    JsonConvert.DeserializeObject<FbMessageHttpResponse>(result);
                if (fbMessageHttpResponse != null)
                {
                    privateReplyResponse.recipientId = fbMessageHttpResponse.RecipientId;
                    privateReplyResponse.messageId = fbMessageHttpResponse.MessageId;
                    privateReplyResponse.isSuccess = true;
                }
            }
            else
            {
                privateReplyResponse.errorMessage = httpResponse.Content.ReadAsStringAsync().Result;
            }

            return privateReplyResponse;
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "[Instagram {MethodName}] Error replying to Instagram user {InstagramUserId}'s comment id {InstagramCommentId}. {ExceptionMessage}",
                nameof(CarouselReplyForComment),
                carouselBodyData.Recipient.Id,
                carouselBodyData.Recipient.CommentId,
                ex.Message);

            privateReplyResponse.errorMessage = ex.Message;
        }

        return privateReplyResponse;
    }

    public async Task<PrivateReplyResponse> QuickReplyButtonForComment(
        QuickReplyButtonBodyData quickReplyButtonBodyData,
        string accessToken)
    {
        var privateReplyResponse = new PrivateReplyResponse();
        privateReplyResponse.isSuccess = false;
        var url = $"https://graph.facebook.com/v13.0/me/messages?access_token={accessToken}";
        var bodyStr = JsonConvert.SerializeObject(quickReplyButtonBodyData);
        var bodyJson = new StringContent(bodyStr, Encoding.UTF8, "application/json");
        try
        {
            var httpClient = _httpClientFactory.CreateClient(HttpClientHandlerName.Default);
            var httpResponse = await httpClient.PostAsync(url, bodyJson);

            if (httpResponse.IsSuccessStatusCode)
            {
                string result = await httpResponse.Content.ReadAsStringAsync();
                FbMessageHttpResponse fbMessageHttpResponse =
                    JsonConvert.DeserializeObject<FbMessageHttpResponse>(result);
                if (fbMessageHttpResponse != null)
                {
                    privateReplyResponse.recipientId = fbMessageHttpResponse.RecipientId;
                    privateReplyResponse.messageId = fbMessageHttpResponse.MessageId;
                    privateReplyResponse.isSuccess = true;
                }
            }
            else
            {
                privateReplyResponse.errorMessage = httpResponse.Content.ReadAsStringAsync().Result;
            }

            return privateReplyResponse;
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "[Instagram {MethodName}] Error replying to Instagram user {InstagramUserId}'s comment id {InstagramCommentId}. {ExceptionMessage}",
                nameof(QuickReplyButtonForComment),
                quickReplyButtonBodyData.Recipient.Id,
                quickReplyButtonBodyData.Recipient.CommentId,
                ex.Message);

            privateReplyResponse.errorMessage = ex.Message;
        }

        return privateReplyResponse;
    }

    public async Task<bool> HasInstagramBusinessAccountAsync(string pageId, string pageAccessToken)
    {
        var httpClient = _httpClientFactory.CreateClient(HttpClientHandlerName.Default);

        var instagramBusinessAccountUri =
            InstagramBusinessAccountUriBuilder.GetInstagramBusinessAccountUri(pageId, pageAccessToken);

        var response = await httpClient.GetAsync(instagramBusinessAccountUri);
        if (!response.IsSuccessStatusCode) return false;

        var content = await response.Content.ReadAsStringAsync();
        var instagramBusinessAccountIdResult = JsonConvert.DeserializeObject<InstagramBusinessAccountResult>(content);

        return instagramBusinessAccountIdResult.InstagramBusinessAccount is not null;
    }

    public async Task AddUserReplyDmHistory(FBMessaging messaging)
    {
        var pageId = messaging.recipient.id;
        var senderId = messaging.sender.id;
        var now = DateTime.UtcNow;
        var timeDiffConfig = 12; // limit to count the reply within 12 hrs

        try
        {
            var history = await _appDbContext.FbIgAutoReplyHistoryRecords.OrderByDescending(x => x.CreatedAt)
                .FirstOrDefaultAsync(
                    x => x.AccountId == senderId && x.PageId == pageId &&
                         x.AutoAction == AutoAction.InstagramInitiateDm &&
                         EF.Functions.DateDiffHour(x.CreatedAt, now) <= timeDiffConfig && x.ErrorMessage == null);
            if (history == null)
            {
                return;
            }

            if (!history.ContactReplyMessages.Any())
            {
                history.ContactReplyMessages = new List<ContactReplyMessage>();
            }

            var newList = new List<ContactReplyMessage>();

            foreach (var contactReplyMessage in history.ContactReplyMessages)
            {
                newList.Add(contactReplyMessage.DeepCopy());
            }

            if (messaging.message != null)
            {
                if (messaging.message.text != null)
                {
                    newList.Add(
                        new ContactReplyMessage()
                        {
                            MessageContent = messaging.message.text, MessageId = messaging.message.mid, ReplyTime = now
                        });
                }
                else if (messaging.message.attachments.Any())
                {
                    newList.Add(
                        new ContactReplyMessage()
                        {
                            AttachmentPayload = messaging.message.attachments,
                            MessageId = messaging.message.mid,
                            ReplyTime = now
                        });
                }
            }
            else if (messaging.postback != null)
            {
                newList.Add(
                    new ContactReplyMessage()
                    {
                        MessageId = messaging.postback.Mid, MessageContent = messaging.postback.Title, ReplyTime = now
                    });
            }

            history.ContactReplyMessages = newList;
            history.UpdatedAt = now;
            await _appDbContext.SaveChangesAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "[Instagram {MethodName}] Error adding DM history from {SenderId} to Instagram page {InstagramPageId}. {ExceptionMessage}",
                nameof(AddUserReplyDmHistory),
                senderId,
                pageId,
                ex.Message);
        }
    }

    public async Task<UserProfile> CreateUserProfile(string senderId, string pageId, string userName = null)
    {
        var config = await _appDbContext.ConfigInstagramConfigs.Where(x => x.InstagramPageId == pageId)
            .FirstOrDefaultAsync();
        if (config == null)
        {
            return null;
        }

        var existingIgUser = await _appDbContext.SenderInstagramSenders
            .Where(x => x.CompanyId == config.CompanyId && x.InstagramId == senderId && x.InstagramPageId == pageId)
            .OrderByDescending(x => x.Id).LastOrDefaultAsync();

        var existingUserProfile = new UserProfile();

        if (existingIgUser != null)
        {
            existingUserProfile = await _appDbContext.UserProfiles.Include(x => x.InstagramUser)
                .Where(x => x.CompanyId == config.CompanyId && x.InstagramUserId == existingIgUser.Id).FirstOrDefaultAsync();
        }
        else
        {
            existingUserProfile = null;
        }

        if (existingUserProfile != null)
        {
            return existingUserProfile;
        }

        var sender = new InstagramSender()
        {
            CompanyId = config.CompanyId,
            InstagramId = senderId,
            InstagramPageId = pageId,
            PageId = config.PageId,
            Username = userName,
            Name = userName
        };

        try
        {
            var httpClient = _httpClientFactory.CreateClient(HttpClientHandlerName.Default);

            var profileDataResponse = await httpClient.GetStringAsync(
                $"https://graph.facebook.com/{sender.InstagramId}?access_token={config.PageAccessToken}&fields=name,profile_pic,username");

            var profileData = JsonConvert.DeserializeObject<IGProfile>(profileDataResponse);

            if (!string.IsNullOrEmpty(profileData?.ProfilePic))
            {
                var newUploadedFile = await _userProfileService.UploadSocialProfilePicture(
                    config.CompanyId,
                    ChannelTypes.Instagram,
                    sender.InstagramId,
                    profileData.ProfilePic);

                if (newUploadedFile == null)
                {
                    _logger.LogWarning(
                        "[Instagram {MethodName}] Profile pic upload failed for Instagram user {InstagramUserId}, page id {InstagramPageId}",
                        nameof(CreateUserProfile),
                        sender.InstagramId,
                        pageId);
                }
                else
                {
                    _appDbContext.UserProfilePictureFiles.Add(newUploadedFile);

                    var domainName = _configuration.GetValue<string>("Values:DomainName");
                    sender.ProfilePic = $"{domainName}/userprofile/picture/{newUploadedFile.ProfilePictureId}";
                }
            }

            sender.Name = string.IsNullOrEmpty(profileData?.Name) ? "No Name" : profileData.Name;
            sender.Username = profileData?.Username;
            await _appDbContext.SaveChangesAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "[Instagram {MethodName}] Error getting user data for Instagram user {SenderId}, page id {InstagramPageId}. {ExceptionMessage}",
                nameof(CreateUserProfile),
                sender.InstagramId,
                pageId,
                ex.Message);
        }

        var userProfile = new UserProfile
        {
            CompanyId = config.CompanyId,
            InstagramUser = sender,
            FirstName = sender.Name
        };

        _appDbContext.UserProfiles.Add(userProfile);
        await _appDbContext.SaveChangesAsync();
        BackgroundJob.Schedule<IAutomationService>(x => x.NewContactTrigger(userProfile.Id), TimeSpan.FromSeconds(30));

        await _userProfileHooks.OnUserProfileCreatedAsync(
            config.CompanyId,
            userProfile.Id,
            null,
            () => Task.FromResult(new OnUserProfileCreatedData(userProfile)));

        Dictionary<string, string> userProfileFieldsDict = new ()
        {
            ["Subscriber"] = "true",
            ["firstname"] = userProfile.FirstName ?? string.Empty
        };

        var importUserProfile = userProfileFieldsDict.ToImportUserProfileObject();
        userProfile = await _userProfileService.BulkSetFields(
            userProfile,
            null,
            importUserProfile,
            false);

        return userProfile;
    }

    public async Task<string> GetProfilePictureLink(string pageId, string accessToken)
    {
        try
        {
            var httpClient = _httpClientFactory.CreateClient(HttpClientHandlerName.Default);

            var pageProfileResponse = await httpClient.GetStringAsync(
                $"https://graph.facebook.com/{pageId}?fields=name, profile_picture_url&access_token={accessToken}");

            var pageProfileData = JsonConvert.DeserializeObject<JObject>(pageProfileResponse);

            if (pageProfileData != null && pageProfileData["profile_picture_url"] != null)
            {
                return (string) pageProfileData["profile_picture_url"];
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "[Instagram {MethodName}] Error getting Instagram page {InstagramPageId} profile picture. {ExceptionMessage}",
                nameof(GetProfilePictureLink),
                pageId,
                ex.Message);
        }

        return string.Empty;
    }

    private async Task AddConversationMessageForInitiateDmFromComment(
        string pageId,
        string postId,
        string messageId,
        string senderId,
        string accessToken)
    {
        try
        {
            await _conversationMessageService.IGFetchByMessageId(pageId, messageId, false);

            var message = await _appDbContext.ConversationMessages.AsNoTracking()
                .FirstOrDefaultAsync(x => x.MessageUniqueID == messageId);

            if (message != null &&
                !await _appDbContext.ConversationMessages.AnyAsync(
                    x => x.MessageUniqueID == $"initiateDmForComment_{messageId}"))
            {
                var sender = await _appDbContext.SenderInstagramSenders.FirstOrDefaultAsync(
                    x => x.InstagramId == senderId && x.InstagramPageId == pageId);
                var receiver = await _appDbContext.SenderInstagramSenders.FirstOrDefaultAsync(
                    x => x.InstagramId == pageId && x.InstagramPageId == pageId);

                if (receiver == null)
                {
                    var config = await _appDbContext.ConfigInstagramConfigs
                        .Where(x => x.InstagramPageId == pageId && x.PageAccessToken == accessToken)
                        .FirstOrDefaultAsync();

                    receiver = new InstagramSender()
                    {
                        InstagramId = pageId,
                        InstagramPageId = pageId,
                        CompanyId = config.CompanyId,
                        Name = config.PageName
                    };

                    await _appDbContext.SenderInstagramSenders.AddAsync(receiver);
                    await _appDbContext.SaveChangesAsync();
                }

                var mediaDetails = await GetaInstagramMediaDetails(pageId, postId, accessToken);

                if (mediaDetails != null && mediaDetails.PostUrl != null)
                {
                    var conversationMessage = new ConversationMessage()
                    {
                        ConversationId = message.ConversationId,
                        CompanyId = message.CompanyId,
                        MessageContent =
                            $"You are responding to a user comment to a post on your Page. View comment.({mediaDetails.PostUrl})",
                        Channel = ChannelTypes.Instagram,
                        MessageType = "text",
                        MessageUniqueID = "initiateDmForComment" + "_" + messageId,
                        Timestamp = message.Timestamp - 10,
                        LocalTimestamp = message.LocalTimestamp - 10,
                        FrondendTimestamp = message.FrondendTimestamp - 10,
                        InstagramSender = receiver,
                        InstagramReceiver = sender,
                        IsSentFromSleekflow = true,
                        Status = MessageStatus.Sent,
                        CreatedAt = message.CreatedAt.AddSeconds(-10),
                        UpdatedAt = message.UpdatedAt.AddSeconds(-10)
                    };

                    await _appDbContext.ConversationMessages.AddAsync(conversationMessage);
                    await _appDbContext.SaveChangesAsync();

                    await _conversationMessageService.RemoveCache(conversationMessage.CompanyId,conversationMessage.ConversationId);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(
                "[Instagram [{MethodName}]] Error for direct message id {InstagramMessageId} from Instagram user {InstagramUserId} " +
                "in post id {InstagramPostId} of page id {InstagramPageId}. {ExceptionMessage}",
                nameof(AddConversationMessageForInitiateDmFromComment),
                messageId,
                senderId,
                postId,
                pageId,
                ex.Message);
        }
    }

    public async Task HandleIgAutoAction(
        AutomationAction automationAction,
        Entry entry,
        Change change,
        bool isNewContact)
    {
        var fbIgAutoReply = automationAction.FbIgAutoReply;

        if (automationAction.AutomatedTriggerType == AutomatedTriggerType.InstagramInitiateDm)
        {
            var config =
                await _appDbContext.ConfigInstagramConfigs.FirstOrDefaultAsync(x => x.InstagramPageId == entry.Id);
            if (config == null)
            {
                return;
            }

            if (fbIgAutoReply.MessageFormat == MessageFormat.Text)
            {
                var bodyData = new PmBodyData()
                {
                    Recipient = new FacebookMessageRecipient()
                    {
                        CommentId = change.Value.Id
                    },
                    Message = new Message()
                    {
                        Text = fbIgAutoReply.MessageContent
                    }
                };

                var historyRecord = new FbIgAutoReplyHistoryRecord()
                {
                    FbIgAutoReplyId = fbIgAutoReply.Id,
                    PlatformType = PlatformType.Instagram,
                    PostId = change.Value.Media.Id,
                    PageId = entry.Id,
                    AutoAction = AutoAction.InstagramInitiateDm,
                    FbIgAutoReply = fbIgAutoReply,
                    ParentCommentId = change.Value.Id,
                    ParentCommentMessage = change.Value.Text,
                    AccountId = change.Value.From.Id,
                    IsNewContact = isNewContact,
                    ContactReplyMessages = new List<ContactReplyMessage>()
                };

                var privateReplyResponse =
                    await PrivateReplyForComment(bodyData, config.PageAccessToken);

                if (privateReplyResponse.isSuccess)
                {
                    historyRecord.MessageId = privateReplyResponse.messageId;
                    await AddConversationMessageForInitiateDmFromComment(
                        entry.Id,
                        change.Value.Media.Id,
                        privateReplyResponse.messageId,
                        change.Value.From.Id,
                        config.PageAccessToken);
                }
                else
                {
                    historyRecord.ErrorMessage = privateReplyResponse.errorMessage;
                }

                await _appDbContext.FbIgAutoReplyHistoryRecords.AddAsync(historyRecord);

                await _appDbContext.SaveChangesAsync();
            }
            else if (fbIgAutoReply.MessageFormat == MessageFormat.Attachment)
            {
                var fbIgAutoReplyAttachment = fbIgAutoReply.FbIgAutoReplyFiles[0];
                var companyId = await _appDbContext.ConfigInstagramConfigs
                    .Where(x => x.InstagramPageId == entry.Id).Select(x => x.CompanyId).FirstOrDefaultAsync();
                var storageConfig = await _appDbContext.CompanyCompanies
                    .Where(x => x.Id == companyId).Select(x => x.StorageConfig)
                    .FirstOrDefaultAsync();

                if (storageConfig == null)
                {
                    _logger.LogWarning(
                        "[{MethodName}] Instagram page {InstagramPageId}, auto reply format: {MessageFormat}. Storage config not found for companyId {CompanyId}",
                        nameof(HandleIgAutoAction),
                        entry.Id,
                        MessageFormat.Attachment.ToString(),
                        companyId);
                }

                var accessUrl = _azureBlobStorageService.GetAzureBlobSasUriForever(
                    fbIgAutoReply.FbIgAutoReplyFiles[0].Filename,
                    storageConfig.ContainerName,
                    true);

                var fileType = fbIgAutoReplyAttachment.MIMEType;

                if (fileType.Contains("image")) // ig only support file url for image type only
                {
                    fileType = "image";
                }

                var bodyData = new PmBodyData()
                {
                    Recipient = new FacebookMessageRecipient()
                    {
                        CommentId = change.Value.Id
                    },
                    Message = new Message()
                    {
                        MessageAttachment = new MessageAttachment()
                        {
                            Type = fileType,
                            MessagePayload = new MessagePayload()
                            {
                                Url = accessUrl
                            }
                        }
                    }
                };

                var historyRecord = new FbIgAutoReplyHistoryRecord()
                {
                    FbIgAutoReplyId = fbIgAutoReply.Id,
                    PlatformType = PlatformType.Instagram,
                    PostId = change.Value.Media.Id,
                    PageId = entry.Id,
                    AutoAction = AutoAction.InstagramInitiateDm,
                    FbIgAutoReply = fbIgAutoReply,
                    ParentCommentId = change.Value.Id,
                    ParentCommentMessage = change.Value.Text,
                    AccountId = change.Value.From.Id,
                    IsNewContact = isNewContact,
                    ContactReplyMessages = new List<ContactReplyMessage>()
                };

                var privateReplyResponse =
                    await PrivateReplyForComment(bodyData, config.PageAccessToken);

                if (privateReplyResponse.isSuccess)
                {
                    historyRecord.MessageId = privateReplyResponse.messageId;
                }
                else
                {
                    historyRecord.ErrorMessage = privateReplyResponse.errorMessage;
                }

                await _appDbContext.FbIgAutoReplyHistoryRecords.AddAsync(historyRecord);

                await _appDbContext.SaveChangesAsync();
            }
            else if (fbIgAutoReply.MessageFormat == MessageFormat.Carousel)
            {
                MessageAttachment messageAttachment = fbIgAutoReply.MessageAttachment;

                if (messageAttachment == null)
                {
                    return;
                }

                try
                {
                    var carouselPayload = new CarouselPayload()
                    {
                        TemplateType = messageAttachment.MessagePayload.TemplateType,
                        Elements = messageAttachment.MessagePayload.Elements
                    };

                    var carouselAttachment = new CarouselAttachment()
                    {
                        Type = messageAttachment.Type, CarouselPayload = carouselPayload
                    };

                    var carouselBodyData = new CarouselBodyData()
                    {
                        MessagingType = "RESPONSE",
                        Recipient = new FacebookMessageRecipient()
                        {
                            CommentId = change.Value.From.Id
                        },
                        CarouselMessage = new CarouselMessage()
                        {
                            CarouselAttachment = carouselAttachment
                        }
                    };

                    var privateReplyResponse =
                        await CarouselReplyForComment(carouselBodyData, config.PageAccessToken);

                    var historyRecord = new FbIgAutoReplyHistoryRecord()
                    {
                        FbIgAutoReplyId = fbIgAutoReply.Id,
                        PlatformType = PlatformType.Instagram,
                        PostId = change.Value.Media.Id,
                        PageId = entry.Id,
                        AutoAction = AutoAction.InstagramInitiateDm,
                        FbIgAutoReply = fbIgAutoReply,
                        ParentCommentId = change.Value.Id,
                        ParentCommentMessage = change.Value.Text,
                        AccountId = change.Value.From.Id,
                        IsNewContact = isNewContact,
                        ContactReplyMessages = new List<ContactReplyMessage>()
                    };

                    if (privateReplyResponse.isSuccess)
                    {
                        historyRecord.MessageId = privateReplyResponse.messageId;
                        await AddConversationMessageForInitiateDmFromComment(
                            entry.Id,
                            change.Value.Media.Id,
                            privateReplyResponse.messageId,
                            change.Value.From.Id,
                            config.PageAccessToken);
                    }
                    else
                    {
                        historyRecord.ErrorMessage = privateReplyResponse.errorMessage;
                    }

                    await _appDbContext.FbIgAutoReplyHistoryRecords.AddAsync(historyRecord);

                    await _appDbContext.SaveChangesAsync();
                }
                catch (Exception ex)
                {
                    _logger.LogError(
                        ex,
                        "[Instagram {MethodName}] Error processing auto reply format: {MessageFormat} for Instagram page {InstagramPageId}. {ExceptionMessage}",
                        nameof(HandleIgAutoAction),
                        MessageFormat.Carousel.ToString(),
                        entry.Id,
                        ex.Message);
                }
            }
            else if (fbIgAutoReply.MessageFormat == MessageFormat.QuickReplyButton)
            {
                List<QuickReplyButton> quickReplyButtons = fbIgAutoReply.QuickReplyButtons;
                if (quickReplyButtons == null)
                {
                    return;
                }

                try
                {
                    var quickReplyButtonBodyData = new QuickReplyButtonBodyData()
                    {
                        Recipient = new FacebookMessageRecipient()
                        {
                            CommentId = change.Value.CommentId
                        },
                        QuickReplyButtonMessage = new QuickReplyButtonMessage()
                        {
                            Text = fbIgAutoReply.MessageContent, QuickReplyButtons = fbIgAutoReply.QuickReplyButtons
                        }
                    };

                    var privateReplyResponse = await QuickReplyButtonForComment(
                        quickReplyButtonBodyData,
                        config.PageAccessToken);
                    var historyRecord = new FbIgAutoReplyHistoryRecord()
                    {
                        FbIgAutoReplyId = fbIgAutoReply.Id,
                        PlatformType = PlatformType.Instagram,
                        PostId = change.Value.Media.Id,
                        PageId = entry.Id,
                        AutoAction = AutoAction.InstagramInitiateDm,
                        FbIgAutoReply = fbIgAutoReply,
                        ParentCommentId = change.Value.Id,
                        ParentCommentMessage = change.Value.Text,
                        AccountId = change.Value.From.Id,
                        IsNewContact = isNewContact,
                        ContactReplyMessages = new List<ContactReplyMessage>()
                    };

                    if (privateReplyResponse.isSuccess)
                    {
                        historyRecord.MessageId = privateReplyResponse.messageId;
                        await AddConversationMessageForInitiateDmFromComment(
                            entry.Id,
                            change.Value.Media.Id,
                            privateReplyResponse.messageId,
                            change.Value.From.Id,
                            config.PageAccessToken);
                    }
                    else
                    {
                        historyRecord.ErrorMessage = privateReplyResponse.errorMessage;
                    }

                    await _appDbContext.FbIgAutoReplyHistoryRecords.AddAsync(historyRecord);

                    await _appDbContext.SaveChangesAsync();
                }
                catch (Exception ex)
                {
                    _logger.LogError(
                        ex,
                        "[Instagram {MethodName}] Error processing auto reply format: {MessageFormat} for Instagram page {InstagramPageId}. {ExceptionMessage}",
                        nameof(HandleIgAutoAction),
                        MessageFormat.QuickReplyButton.ToString(),
                        entry.Id,
                        ex.Message);
                }
            }
        }
        else if (automationAction.AutomatedTriggerType == AutomatedTriggerType.InstagramReplyComment)
        {
            var config =
                await _appDbContext.ConfigInstagramConfigs.FirstOrDefaultAsync(x => x.InstagramPageId == entry.Id);
            if (config == null)
            {
                return;
            }

            var replyContent = fbIgAutoReply.MessageContent;

            var replyCommentResponse =
                await ReplyCommentInPost(
                    change.Value.Id,
                    replyContent,
                    config.PageAccessToken);

            var historyRecord = new FbIgAutoReplyHistoryRecord()
            {
                FbIgAutoReplyId = fbIgAutoReply.Id,
                PlatformType = PlatformType.Instagram,
                PostId = change.Value.Media.Id,
                PageId = entry.Id,
                AutoAction = AutoAction.InstagramReplyComment,
                FbIgAutoReply = fbIgAutoReply,
                ParentCommentId = change.Value.Id,
                ParentCommentMessage = change.Value.Text,
                IsNewContact = isNewContact,
                AccountId = change.Value.From.Id,
                ContactReplyMessages = new List<ContactReplyMessage>()
            };
            if (replyCommentResponse.isSuccess)
            {
                historyRecord.CommentId = replyCommentResponse.CommentId;
                replyCommentResponse.isSuccess = true;
            }
            else
            {
                historyRecord.ErrorMessage = replyCommentResponse.ErrorMessage;
            }

            await _appDbContext.FbIgAutoReplyHistoryRecords.AddAsync(historyRecord);
            await _appDbContext.SaveChangesAsync();
        }
    }

    public async Task HandleIgIcebreakerDm(long automationActionId, FBMessaging messaging, bool isNewContact)
    {
        var igUserId = messaging.sender.id;
        var pageId = messaging.recipient.id;

        var automationAction = await _appDbContext.CompanyAutomationActions
            .Include(x => x.FbIgAutoReply).ThenInclude(x => x.FbIgAutoReplyFiles)
            .FirstOrDefaultAsync(x => x.Id == automationActionId);

        if (automationAction == null)
        {
            return;
        }

        if (automationAction.AutomatedTriggerType == AutomatedTriggerType.InstagramInitiateDm)
        {
            var config =
                await _appDbContext.ConfigInstagramConfigs.FirstOrDefaultAsync(x => x.InstagramPageId == pageId);
            if (config == null)
            {
                return;
            }

            var fbIgAutoReply = automationAction.FbIgAutoReply;

            if (fbIgAutoReply.MessageFormat == MessageFormat.Text)
            {
                var bodyData = new PmBodyData()
                {
                    Recipient = new FacebookMessageRecipient()
                    {
                        Id = igUserId
                    },
                    Message = new Message()
                    {
                        Text = fbIgAutoReply.MessageContent
                    }
                };

                var historyRecord = new FbIgAutoReplyHistoryRecord()
                {
                    FbIgAutoReplyId = fbIgAutoReply.Id,
                    PlatformType = PlatformType.Instagram,
                    PageId = pageId,
                    AutoAction = AutoAction.InstagramInitiateDm,
                    FbIgAutoReply = fbIgAutoReply,
                    AccountId = igUserId,
                    IsNewContact = isNewContact,
                    ContactReplyMessages = new List<ContactReplyMessage>()
                };

                var privateReplyResponse =
                    await PrivateReplyForComment(bodyData, config.PageAccessToken);

                if (privateReplyResponse.isSuccess)
                {
                    historyRecord.MessageId = privateReplyResponse.messageId;
                }
                else
                {
                    historyRecord.ErrorMessage = privateReplyResponse.errorMessage;
                }

                await _appDbContext.FbIgAutoReplyHistoryRecords.AddAsync(historyRecord);

                await _appDbContext.SaveChangesAsync();
            }
            else if (fbIgAutoReply.MessageFormat == MessageFormat.Attachment)
            {
                var fbIgAutoReplyAttachment = fbIgAutoReply.FbIgAutoReplyFiles[0];
                var companyId = await _appDbContext.ConfigInstagramConfigs
                    .Where(x => x.InstagramPageId == pageId).Select(x => x.CompanyId).FirstOrDefaultAsync();
                var storageConfig = await _appDbContext.CompanyCompanies
                    .Where(x => x.Id == companyId).Select(x => x.StorageConfig)
                    .FirstOrDefaultAsync();

                if (storageConfig == null)
                {
                    _logger.LogWarning(
                        "[Instagram {MethodName}] Instagram page {InstagramPageId}, auto reply format: {MessageFormat}. Storage config not found for companyId {CompanyId}",
                        nameof(HandleIgIcebreakerDm),
                        pageId,
                        MessageFormat.Attachment.ToString(),
                        companyId);
                }

                var accessUrl = _azureBlobStorageService.GetAzureBlobSasUriForever(
                    fbIgAutoReply.FbIgAutoReplyFiles[0].Filename,
                    storageConfig.ContainerName,
                    true);

                var fileType = fbIgAutoReplyAttachment.MIMEType;

                if (fileType.Contains("image")) // ig only support file url for image type only
                {
                    fileType = "image";
                }

                var bodyData = new PmBodyData()
                {
                    Recipient = new FacebookMessageRecipient()
                    {
                        Id = igUserId
                    },
                    Message = new Message()
                    {
                        MessageAttachment = new MessageAttachment()
                        {
                            Type = fileType,
                            MessagePayload = new MessagePayload()
                            {
                                Url = accessUrl
                            }
                        }
                    }
                };

                var historyRecord = new FbIgAutoReplyHistoryRecord()
                {
                    FbIgAutoReplyId = fbIgAutoReply.Id,
                    PlatformType = PlatformType.Instagram,
                    PageId = pageId,
                    AutoAction = AutoAction.InstagramInitiateDm,
                    FbIgAutoReply = fbIgAutoReply,
                    AccountId = igUserId,
                    IsNewContact = isNewContact,
                    ContactReplyMessages = new List<ContactReplyMessage>()
                };

                var privateReplyResponse =
                    await PrivateReplyForComment(bodyData, config.PageAccessToken);

                if (privateReplyResponse.isSuccess)
                {
                    historyRecord.MessageId = privateReplyResponse.messageId;
                }
                else
                {
                    historyRecord.ErrorMessage = privateReplyResponse.errorMessage;
                }

                await _appDbContext.FbIgAutoReplyHistoryRecords.AddAsync(historyRecord);

                await _appDbContext.SaveChangesAsync();
            }
            else if (fbIgAutoReply.MessageFormat == MessageFormat.Carousel)
            {
                MessageAttachment messageAttachment = fbIgAutoReply.MessageAttachment;

                if (messageAttachment == null)
                {
                    return;
                }

                try
                {
                    var carouselPayload = new CarouselPayload()
                    {
                        TemplateType = messageAttachment.MessagePayload.TemplateType,
                        Elements = messageAttachment.MessagePayload.Elements
                    };

                    var carouselAttachment = new CarouselAttachment()
                    {
                        Type = messageAttachment.Type, CarouselPayload = carouselPayload
                    };

                    var carouselBodyData = new CarouselBodyData()
                    {
                        Recipient = new FacebookMessageRecipient()
                        {
                            Id = igUserId
                        },
                        CarouselMessage = new CarouselMessage()
                        {
                            CarouselAttachment = carouselAttachment
                        }
                    };

                    var privateReplyResponse =
                        await CarouselReplyForComment(carouselBodyData, config.PageAccessToken);

                    var historyRecord = new FbIgAutoReplyHistoryRecord()
                    {
                        FbIgAutoReplyId = fbIgAutoReply.Id,
                        PlatformType = PlatformType.Instagram,
                        PageId = pageId,
                        AutoAction = AutoAction.InstagramInitiateDm,
                        FbIgAutoReply = fbIgAutoReply,
                        AccountId = igUserId,
                        IsNewContact = isNewContact,
                        ContactReplyMessages = new List<ContactReplyMessage>()
                    };

                    if (privateReplyResponse.isSuccess)
                    {
                        historyRecord.MessageId = privateReplyResponse.messageId;
                    }
                    else
                    {
                        historyRecord.ErrorMessage = privateReplyResponse.errorMessage;
                    }

                    await _appDbContext.FbIgAutoReplyHistoryRecords.AddAsync(historyRecord);

                    await _appDbContext.SaveChangesAsync();
                }
                catch (Exception ex)
                {
                    _logger.LogError(
                        ex,
                        "[Instagram  {MethodName}] Error processing auto reply format {MessageFormat} for sender {InstagramUserId} " +
                        "Instagram page {InstagramPageId}. {ExceptionMessage}",
                        nameof(HandleIgIcebreakerDm),
                        MessageFormat.Carousel.ToString(),
                        igUserId,
                        pageId,
                        ex.Message);
                }
            }
            else if (fbIgAutoReply.MessageFormat == MessageFormat.QuickReplyButton)
            {
                List<QuickReplyButton> quickReplyButtons = fbIgAutoReply.QuickReplyButtons;
                if (quickReplyButtons == null)
                {
                    return;
                }

                try
                {
                    var quickReplyButtonBodyData = new QuickReplyButtonBodyData()
                    {
                        Recipient = new FacebookMessageRecipient()
                        {
                            Id = igUserId
                        },
                        QuickReplyButtonMessage = new QuickReplyButtonMessage()
                        {
                            Text = fbIgAutoReply.MessageContent, QuickReplyButtons = fbIgAutoReply.QuickReplyButtons
                        }
                    };

                    var privateReplyResponse = await QuickReplyButtonForComment(
                        quickReplyButtonBodyData,
                        config.PageAccessToken);
                    var historyRecord = new FbIgAutoReplyHistoryRecord()
                    {
                        FbIgAutoReplyId = fbIgAutoReply.Id,
                        PlatformType = PlatformType.Instagram,
                        PageId = pageId,
                        AutoAction = AutoAction.InstagramInitiateDm,
                        FbIgAutoReply = fbIgAutoReply,
                        AccountId = igUserId,
                        IsNewContact = isNewContact,
                        ContactReplyMessages = new List<ContactReplyMessage>()
                    };

                    if (privateReplyResponse.isSuccess)
                    {
                        historyRecord.MessageId = privateReplyResponse.messageId;
                    }
                    else
                    {
                        historyRecord.ErrorMessage = privateReplyResponse.errorMessage;
                    }

                    await _appDbContext.FbIgAutoReplyHistoryRecords.AddAsync(historyRecord);

                    await _appDbContext.SaveChangesAsync();
                }
                catch (Exception ex)
                {
                    _logger.LogError(
                        ex,
                        "[Instagram {MethodName}] Error processing auto reply format {MessageFormat} for sender {InstagramUserId} " +
                        "Instagram page {InstagramPageId}. {ExceptionMessage}",
                        nameof(HandleIgIcebreakerDm),
                        MessageFormat.QuickReplyButton.ToString(),
                        igUserId,
                        pageId,
                        ex.Message);
                }
            }
        }
    }

    public async Task PreviewDmAutomation(long automationActionId, FBMessaging messaging, bool isNewContact)
    {
        var igUserId = messaging.sender.id;
        var pageId = messaging.recipient.id;

        var automationAction = await _appDbContext.CompanyAutomationActions
            .Include(x => x.FbIgAutoReply).ThenInclude(x => x.FbIgAutoReplyFiles)
            .FirstOrDefaultAsync(x => x.Id == automationActionId);

        if (automationAction == null)
        {
            return;
        }

        if (automationAction.AutomatedTriggerType == AutomatedTriggerType.InstagramInitiateDm)
        {
            var config =
                await _appDbContext.ConfigInstagramConfigs.FirstOrDefaultAsync(x => x.InstagramPageId == pageId);
            if (config == null)
            {
                return;
            }

            var fbIgAutoReply = automationAction.FbIgAutoReply;

            if (fbIgAutoReply.MessageFormat == MessageFormat.Text)
            {
                var bodyData = new PmBodyData()
                {
                    Recipient = new FacebookMessageRecipient()
                    {
                        Id = igUserId
                    },
                    Message = new Message()
                    {
                        Text = fbIgAutoReply.MessageContent
                    }
                };

                var historyRecord = new FbIgAutoReplyHistoryRecord()
                {
                    FbIgAutoReplyId = fbIgAutoReply.Id,
                    PlatformType = PlatformType.Instagram,
                    PageId = pageId,
                    AutoAction = AutoAction.InstagramInitiateDm,
                    FbIgAutoReply = fbIgAutoReply,
                    AccountId = igUserId,
                    IsNewContact = isNewContact,
                    PreviewMessageId = messaging.message.mid,
                    PreviewCode = messaging.message.text,
                    ContactReplyMessages = new List<ContactReplyMessage>()
                };

                var privateReplyResponse =
                    await PrivateReplyForComment(bodyData, config.PageAccessToken);

                if (privateReplyResponse.isSuccess)
                {
                    historyRecord.MessageId = privateReplyResponse.messageId;
                }
                else
                {
                    historyRecord.ErrorMessage = privateReplyResponse.errorMessage;
                }

                await _appDbContext.FbIgAutoReplyHistoryRecords.AddAsync(historyRecord);

                await _appDbContext.SaveChangesAsync();
            }
            else if (fbIgAutoReply.MessageFormat == MessageFormat.Attachment)
            {
                var fbIgAutoReplyAttachment = fbIgAutoReply.FbIgAutoReplyFiles[0];
                var companyId = await _appDbContext.ConfigInstagramConfigs
                    .Where(x => x.InstagramPageId == pageId).Select(x => x.CompanyId).FirstOrDefaultAsync();
                var storageConfig = await _appDbContext.CompanyCompanies
                    .Where(x => x.Id == companyId).Select(x => x.StorageConfig)
                    .FirstOrDefaultAsync();

                if (storageConfig == null)
                {
                    _logger.LogWarning(
                        "[Instagram {MethodName}] Instagram page {InstagramPageId}, auto reply format: {MessageFormat}. Storage config not found for companyId {CompanyId}",
                        nameof(PreviewDmAutomation),
                        pageId,
                        MessageFormat.Attachment.ToString(),
                        companyId);
                }

                var accessUrl = _azureBlobStorageService.GetAzureBlobSasUriForever(
                    fbIgAutoReply.FbIgAutoReplyFiles[0].Filename,
                    storageConfig.ContainerName,
                    true);

                var fileType = fbIgAutoReplyAttachment.MIMEType;

                if (fileType.Contains("image")) // ig only support file url for image type only
                {
                    fileType = "image";
                }

                var bodyData = new PmBodyData()
                {
                    Recipient = new FacebookMessageRecipient()
                    {
                        Id = igUserId
                    },
                    Message = new Message()
                    {
                        MessageAttachment = new MessageAttachment()
                        {
                            Type = fileType,
                            MessagePayload = new MessagePayload()
                            {
                                Url = accessUrl
                            }
                        }
                    }
                };

                var historyRecord = new FbIgAutoReplyHistoryRecord()
                {
                    FbIgAutoReplyId = fbIgAutoReply.Id,
                    PlatformType = PlatformType.Instagram,
                    PageId = pageId,
                    AutoAction = AutoAction.InstagramInitiateDm,
                    FbIgAutoReply = fbIgAutoReply,
                    AccountId = igUserId,
                    IsNewContact = isNewContact,
                    PreviewMessageId = messaging.message.mid,
                    PreviewCode = messaging.message.text,
                    ContactReplyMessages = new List<ContactReplyMessage>()
                };

                var privateReplyResponse =
                    await PrivateReplyForComment(bodyData, config.PageAccessToken);

                if (privateReplyResponse.isSuccess)
                {
                    historyRecord.MessageId = privateReplyResponse.messageId;
                }
                else
                {
                    historyRecord.ErrorMessage = privateReplyResponse.errorMessage;
                }

                await _appDbContext.FbIgAutoReplyHistoryRecords.AddAsync(historyRecord);

                await _appDbContext.SaveChangesAsync();
            }
            else if (fbIgAutoReply.MessageFormat == MessageFormat.Carousel)
            {
                MessageAttachment messageAttachment = fbIgAutoReply.MessageAttachment;

                if (messageAttachment == null)
                {
                    return;
                }

                try
                {
                    var carouselPayload = new CarouselPayload()
                    {
                        TemplateType = messageAttachment.MessagePayload.TemplateType,
                        Elements = messageAttachment.MessagePayload.Elements
                    };

                    var carouselAttachment = new CarouselAttachment()
                    {
                        Type = messageAttachment.Type, CarouselPayload = carouselPayload
                    };

                    var carouselBodyData = new CarouselBodyData()
                    {
                        Recipient = new FacebookMessageRecipient()
                        {
                            Id = igUserId
                        },
                        CarouselMessage = new CarouselMessage()
                        {
                            CarouselAttachment = carouselAttachment
                        }
                    };

                    var privateReplyResponse =
                        await CarouselReplyForComment(carouselBodyData, config.PageAccessToken);

                    var historyRecord = new FbIgAutoReplyHistoryRecord()
                    {
                        FbIgAutoReplyId = fbIgAutoReply.Id,
                        PlatformType = PlatformType.Instagram,
                        PageId = pageId,
                        AutoAction = AutoAction.InstagramInitiateDm,
                        FbIgAutoReply = fbIgAutoReply,
                        AccountId = igUserId,
                        IsNewContact = isNewContact,
                        PreviewMessageId = messaging.message.mid,
                        PreviewCode = messaging.message.text,
                        ContactReplyMessages = new List<ContactReplyMessage>()
                    };

                    if (privateReplyResponse.isSuccess)
                    {
                        historyRecord.MessageId = privateReplyResponse.messageId;
                    }
                    else
                    {
                        historyRecord.ErrorMessage = privateReplyResponse.errorMessage;
                    }

                    await _appDbContext.FbIgAutoReplyHistoryRecords.AddAsync(historyRecord);

                    await _appDbContext.SaveChangesAsync();
                }
                catch (Exception ex)
                {
                    _logger.LogError(
                        ex,
                        "[Instagram {MethodName}] Error processing auto reply format {MessageFormat} for sender " +
                        "{InstagramUserId} Instagram page {InstagramPageId}. {ExceptionMessage}",
                        nameof(PreviewDmAutomation),
                        MessageFormat.Carousel.ToString(),
                        igUserId,
                        pageId,
                        ex.Message);
                }
            }
            else if (fbIgAutoReply.MessageFormat == MessageFormat.QuickReplyButton)
            {
                List<QuickReplyButton> quickReplyButtons = fbIgAutoReply.QuickReplyButtons;
                if (quickReplyButtons == null)
                {
                    return;
                }

                try
                {
                    var quickReplyButtonBodyData = new QuickReplyButtonBodyData()
                    {
                        Recipient = new FacebookMessageRecipient()
                        {
                            Id = igUserId
                        },
                        QuickReplyButtonMessage = new QuickReplyButtonMessage()
                        {
                            Text = fbIgAutoReply.MessageContent, QuickReplyButtons = fbIgAutoReply.QuickReplyButtons
                        }
                    };

                    var privateReplyResponse = await QuickReplyButtonForComment(
                        quickReplyButtonBodyData,
                        config.PageAccessToken);
                    var historyRecord = new FbIgAutoReplyHistoryRecord()
                    {
                        FbIgAutoReplyId = fbIgAutoReply.Id,
                        PlatformType = PlatformType.Instagram,
                        PageId = pageId,
                        AutoAction = AutoAction.InstagramInitiateDm,
                        FbIgAutoReply = fbIgAutoReply,
                        AccountId = igUserId,
                        IsNewContact = isNewContact,
                        PreviewMessageId = messaging.message.mid,
                        PreviewCode = messaging.message.text,
                        ContactReplyMessages = new List<ContactReplyMessage>()
                    };

                    if (privateReplyResponse.isSuccess)
                    {
                        historyRecord.MessageId = privateReplyResponse.messageId;
                    }
                    else
                    {
                        historyRecord.ErrorMessage = privateReplyResponse.errorMessage;
                    }

                    await _appDbContext.FbIgAutoReplyHistoryRecords.AddAsync(historyRecord);

                    await _appDbContext.SaveChangesAsync();
                }
                catch (Exception ex)
                {
                    _logger.LogError(
                        ex,
                        "[Instagram {MethodName}] Error processing auto reply format {MessageFormat} for sender " +
                        "{InstagramUserId} Instagram page {InstagramPageId}. {ExceptionMessage}",
                        nameof(PreviewDmAutomation),
                        MessageFormat.QuickReplyButton.ToString(),
                        igUserId,
                        pageId,
                        ex.Message);
                }
            }
        }
    }
}