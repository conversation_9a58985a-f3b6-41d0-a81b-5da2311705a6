using System.Threading.Tasks;
using Travis_backend.ConversationDomain.Models;
using Travis_backend.MessageDomain.Models;

namespace Travis_backend.MessageDomain.ChannelMessageProvider;

public interface IChannelMessageHandler
{
    string ChannelType { get; }

    ValueTask<ConversationMessage> SendChannelMessageAsync(
        Conversation conversation,
        ConversationMessage conversationMessage);
}