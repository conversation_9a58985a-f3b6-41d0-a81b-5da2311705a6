using System;
using Travis_backend.ConversationDomain.ViewModels;

namespace Travis_backend.ResellerDomain.ViewModels;

public class TransactionLogDto
{
    public string ResellerCompanyProfileId { get; set; }

    public decimal Amount { get; set; }

    public string Currency { get; set; }

    public long? BillRecordId { get; set; }

    public BillRecordDto BillRecordDto { get; set; }

    public string ClientCompanyId { get; set; }

    public CompanyResponse ClientCompany { get; set; }

    public string UserIdentityId { get; set; }

    public UserInfoResponse UserIdentity { get; set; }

    public string TransactionMode { get; set; }

    public string TopUpStatus { get; set; }

    public string TopUpMethod { get; set; }

    public string TransactionCategory { get; set; }

    public string TransactionAction { get; set; }

    public string Detail { get; set; }

    public string InvoiceId { get; set; }

    public DateTime CreatedAt { get; set; }

    public decimal Balance { get; set; }

    public string TransactionType { get; set; }
}