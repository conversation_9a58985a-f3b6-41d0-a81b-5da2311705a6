﻿using System;
using Travis_backend.CommonDomain.Models;

namespace Travis_backend.Models.BackgroundTask
{
    public class BackgroundTask : DateAuditedEntity<long>
    {
        public string CompanyId { get; set; }

        public long StaffId { get; set; }

        public string UserId { get; set; }

        public int Total { get; set; }

        public int Progress { get; set; }

        public bool IsCompleted { get; set; } = false;

        public bool IsDismissed { get; set; } = false;

        public DateTime? StartedAt { get; set; }

        public DateTime? CompletedAt { get; set; }

        public BackgroundTaskType TaskType { get; set; }

        // Json string, parse according to TaskType
        public string TargetPayload { get; set; }

        // public string TaskPayload { get; set; }
        public string ResultPayload { get; set; }

        public string ErrorMessage { get; set; }
    }

    public enum BackgroundTaskType
    {
        ImportContacts = 10,
        AddContactsToList = 11,
        BulkUpdateContactsCustomFields = 12,
        BulkImportContacts = 13,
        ImportContactsV2 = 14,

        ExportContactsListToCsv = 21,
        ExportBroadcastStatusListToCsv = 22,
        ExportAnalyticToCsv = 23,

        ImportWhatsAppHistory = 31,
        ImportGenericHistory = 32,

        ConvertCampaignLeadsToContactList = 40,

        LoopThroughAndEnrollContactsToFlowHub = 50,

        ExportFlowHubWorkflowExecutionUsagesToCsv = 60,
    }
}