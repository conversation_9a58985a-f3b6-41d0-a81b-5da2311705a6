using System.Collections.Generic;
using Travis_backend.MessageDomain.ViewModels;

namespace Travis_backend.ContactDomain.ViewModels;

public class BulkUpdateCustomFieldsViewModel
{
    public List<string> UserProfileIds { get; set; }

    public List<AddCustomFieldsViewModel> UpdateCustomFields { get; set; }

    public List<ConversationHashtagViewModel> SetConversationLabels { get; set; }

    public List<ConversationHashtagViewModel> AddConversationLabels { get; set; }

    public List<ConversationHashtagViewModel> RemoveConversationLabels { get; set; }

    public bool IsTriggerAutomation { get; set; } = true;

    public string Status { get; set; }

    public List<string> AssigneeIdList { get; set; }
}