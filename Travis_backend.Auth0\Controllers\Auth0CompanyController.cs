using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Travis_backend.Auth0.Services;
using Travis_backend.Auth0.ViewModels;
using Travis_backend.CommonDomain.ViewModels;
using Travis_backend.ConversationServices;
using Travis_backend.Enums;

namespace Travis_backend.Auth0.Controllers;

[Authorize]
public class Auth0CompanyController : Controller
{
    private readonly SleekflowUserManager _userManager;
    private readonly ICoreService _coreService;
    private readonly IAuth0CompanyService _auth0CompanyService;

    public Auth0CompanyController(
        SleekflowUserManager userManager,
        ICoreService coreService,
        IAuth0CompanyService auth0CompanyService)
    {
        _userManager = userManager;
        _coreService = coreService;
        _auth0CompanyService = auth0CompanyService;
    }

    /// <summary>
    /// Get Reset password link of staff.
    /// </summary>
    /// <param name="staffIdentityId"></param>
    /// <returns>GetResetPasswordLinkResponse</returns>
    [HttpPost]
    [Route("Company/Staff/Auth0/ResetPasswordLink")]
    [ProducesResponseType(typeof(GetResetPasswordLinkResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<ActionResult<GetResetPasswordLinkResponse>> GenerateStaffResetPasswordLink(
        [FromBody]
        GetResetPasswordLinkResquest getResetPasswordLinkResquest)
    {
        try
        {
            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
            if (companyUser.RoleType != StaffUserRole.Admin)
            {
                throw new Exception("Access Denied");
            }

            var url = await _auth0CompanyService.GenerateResetPasswordLinkForStaff(
                companyUser.CompanyId,
                getResetPasswordLinkResquest.StaffIdentityId);

            return Ok(
                new GetResetPasswordLinkResponse
                {
                    Url = url
                });
        }
        catch (Exception err)
        {
            return BadRequest(
                new ResponseViewModel()
                {
                    message = err.Message
                });
        }
    }
}