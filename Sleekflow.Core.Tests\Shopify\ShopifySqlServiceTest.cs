using System.Reflection;
using System.Text.RegularExpressions;
using Humanizer;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging.Abstractions;
using Travis_backend.Controllers.ShopifyIntegrationControllers;
using Travis_backend.Database;
using Travis_backend.Database.Services;
using Travis_backend.IntegrationServices.Models;

namespace Sleekflow.Core.Tests.Shopify;

public partial class ShopifySqlServiceTest
{
    private ShopifySqlService? _shopifySqlService;

    // Regular expression to remove DECLARE and semicolon from the query string and replace all whitespaces with a single space.
    [GeneratedRegex(@"DECLARE\s.*?;\s*|\s+", RegexOptions.IgnoreCase | RegexOptions.Singleline, "en-HK")]
    private static partial Regex MyRegex();

    [SetUp]
    public void Setup()
    {
        const string connectionString =
            "Server=tcp:sleekflow-core-sql-server-eas-dev928ea268.database.windows.net,1433;Initial Catalog=travis-crm-prod-db;Persist Security Info=False;User ID=s81604a6f;Password=*********************************************************************;MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;Pooling=true;Max Pool Size=500;Min Pool Size=100;";

        var dbContextService = new DbContextService(
            new PersistenceContext(),
            new ApplicationDbContext(GetDbContextOptions<ApplicationDbContext>(connectionString)),
            new ApplicationReadDbContext(GetDbContextOptions<ApplicationReadDbContext>(connectionString)));

        _shopifySqlService = new ShopifySqlService(NullLogger<ShopifySqlService>.Instance, dbContextService);
    }

    [TestCase(
        new object?[]
        {
            "b6d7e442-38ae-4b9a-b100-2951729768bc",
            "2022-01-01",
            "2022-01-02"
        },
        "SELECT CAST(@UPSO_Type AS VARCHAR) AS 'Type', CAST(@UPSO_MessageFrom AS DATETIME) AS 'From', CAST(@UPSO_MessageTo AS DATETIME) AS 'To', CAST(UPSO.Payment AS VARCHAR) AS 'Status', CAST(SUM(UPSO.TotalPrice) AS DECIMAL(18,2)) AS 'TotalPrice', CAST(COUNT(*) AS BIGINT) AS 'TotalCount', CAST(@UPSO_TeamId AS VARCHAR) AS 'TeamId' FROM UserProfileShopifyOrders UPSO WHERE UPSO.CompanyId = @UPSO_CompanyId AND UPSO.ConversionStatus NOT IN (@UPSO_ConversionStatus_0,@UPSO_ConversionStatus_1) AND UPSO.CreatedAt > CAST(@UPSO_MessageFrom AS DATETIME) AND UPSO.CreatedAt < CAST(@UPSO_MessageTo AS DATETIME) GROUP BY UPSO.Payment")]
    [TestCase(
        new object?[]
        {
            "b6d7e442-38ae-4b9a-b100-2951729768bc",
            null,
            null
        },
        "SELECT CAST(@UPSO_Type AS VARCHAR) AS 'Type', CAST(@UPSO_MessageFrom AS DATETIME) AS 'From', CAST(@UPSO_MessageTo AS DATETIME) AS 'To', CAST(UPSO.Payment AS VARCHAR) AS 'Status', CAST(SUM(UPSO.TotalPrice) AS DECIMAL(18,2)) AS 'TotalPrice', CAST(COUNT(*) AS BIGINT) AS 'TotalCount', CAST(@UPSO_TeamId AS VARCHAR) AS 'TeamId' FROM UserProfileShopifyOrders UPSO WHERE UPSO.CompanyId = @UPSO_CompanyId AND UPSO.ConversionStatus NOT IN (@UPSO_ConversionStatus_0,@UPSO_ConversionStatus_1) GROUP BY UPSO.Payment")]
    [TestCase(
        new object?[]
        {
            "b6d7e442-38ae-4b9a-b100-2951729768bc",
            "2022-01-01",
            null
        },
        "SELECT CAST(@UPSO_Type AS VARCHAR) AS 'Type', CAST(@UPSO_MessageFrom AS DATETIME) AS 'From', CAST(@UPSO_MessageTo AS DATETIME) AS 'To', CAST(UPSO.Payment AS VARCHAR) AS 'Status', CAST(SUM(UPSO.TotalPrice) AS DECIMAL(18,2)) AS 'TotalPrice', CAST(COUNT(*) AS BIGINT) AS 'TotalCount', CAST(@UPSO_TeamId AS VARCHAR) AS 'TeamId' FROM UserProfileShopifyOrders UPSO WHERE UPSO.CompanyId = @UPSO_CompanyId AND UPSO.ConversionStatus NOT IN (@UPSO_ConversionStatus_0,@UPSO_ConversionStatus_1) AND UPSO.CreatedAt > CAST(@UPSO_MessageFrom AS DATETIME) GROUP BY UPSO.Payment")]
    [TestCase(
        new object?[]
        {
            "b6d7e442-38ae-4b9a-b100-2951729768bc",
            null,
            "2022-01-02"
        },
        "SELECT CAST(@UPSO_Type AS VARCHAR) AS 'Type', CAST(@UPSO_MessageFrom AS DATETIME) AS 'From', CAST(@UPSO_MessageTo AS DATETIME) AS 'To', CAST(UPSO.Payment AS VARCHAR) AS 'Status', CAST(SUM(UPSO.TotalPrice) AS DECIMAL(18,2)) AS 'TotalPrice', CAST(COUNT(*) AS BIGINT) AS 'TotalCount', CAST(@UPSO_TeamId AS VARCHAR) AS 'TeamId' FROM UserProfileShopifyOrders UPSO WHERE UPSO.CompanyId = @UPSO_CompanyId AND UPSO.ConversionStatus NOT IN (@UPSO_ConversionStatus_0,@UPSO_ConversionStatus_1) AND UPSO.CreatedAt < CAST(@UPSO_MessageTo AS DATETIME) GROUP BY UPSO.Payment")]
    public void GetExternalSourceShopifyOrderRecordsTest(object?[] parameters, string expectedQuery)
    {
        var result = InvokeMethod(
            _shopifySqlService!,
            "GetExternalSourceShopifyOrderRecords",
            parameters);

        Assert.That(GetQueryString((IQueryable<ShopifyAttribution>) result), Is.EqualTo(expectedQuery));
    }

    [TestCase(
        new object?[]
        {
            "b6d7e442-38ae-4b9a-b100-2951729768bc",
            "2022-01-01",
            "2022-01-02",
            false
        },
        "SELECT CAST(@UPSO_Type AS VARCHAR) AS 'Type', CAST(@UPSO_MessageFrom AS DATETIME) AS 'From', CAST(@UPSO_MessageTo AS DATETIME) AS 'To', CAST(@UPSO_Status AS VARCHAR) AS 'Status', CAST(SUM(UPSO.TotalPrice) AS DECIMAL(18,2)) AS 'TotalPrice', CAST(COUNT(*) AS BIGINT) AS 'TotalCount', CAST(@UPSO_TeamId AS VARCHAR) AS 'TeamId' FROM UserProfileShopifyOrders UPSO WHERE UPSO.CompanyId = @UPSO_CompanyId AND UPSO.ConversionStatus = @UPSO_ConversionStatus AND UPSO.Payment = @UPSO_Payment AND UPSO.CreatedAt > CAST(@UPSO_MessageFrom AS DATETIME) AND UPSO.CreatedAt < CAST(@UPSO_MessageTo AS DATETIME) GROUP BY UPSO.Payment")]
    [TestCase(
        new object?[]
        {
            "b6d7e442-38ae-4b9a-b100-2951729768bc",
            null,
            null,
            false
        },
        "SELECT CAST(@UPSO_Type AS VARCHAR) AS 'Type', CAST(@UPSO_MessageFrom AS DATETIME) AS 'From', CAST(@UPSO_MessageTo AS DATETIME) AS 'To', CAST(@UPSO_Status AS VARCHAR) AS 'Status', CAST(SUM(UPSO.TotalPrice) AS DECIMAL(18,2)) AS 'TotalPrice', CAST(COUNT(*) AS BIGINT) AS 'TotalCount', CAST(@UPSO_TeamId AS VARCHAR) AS 'TeamId' FROM UserProfileShopifyOrders UPSO WHERE UPSO.CompanyId = @UPSO_CompanyId AND UPSO.ConversionStatus = @UPSO_ConversionStatus AND UPSO.Payment = @UPSO_Payment GROUP BY UPSO.Payment")]
    [TestCase(
        new object?[]
        {
            "b6d7e442-38ae-4b9a-b100-2951729768bc",
            "2022-01-01",
            null,
            false
        },
        "SELECT CAST(@UPSO_Type AS VARCHAR) AS 'Type', CAST(@UPSO_MessageFrom AS DATETIME) AS 'From', CAST(@UPSO_MessageTo AS DATETIME) AS 'To', CAST(@UPSO_Status AS VARCHAR) AS 'Status', CAST(SUM(UPSO.TotalPrice) AS DECIMAL(18,2)) AS 'TotalPrice', CAST(COUNT(*) AS BIGINT) AS 'TotalCount', CAST(@UPSO_TeamId AS VARCHAR) AS 'TeamId' FROM UserProfileShopifyOrders UPSO WHERE UPSO.CompanyId = @UPSO_CompanyId AND UPSO.ConversionStatus = @UPSO_ConversionStatus AND UPSO.Payment = @UPSO_Payment AND UPSO.CreatedAt > CAST(@UPSO_MessageFrom AS DATETIME) GROUP BY UPSO.Payment")]
    [TestCase(
        new object?[]
        {
            "b6d7e442-38ae-4b9a-b100-2951729768bc",
            null,
            "2022-01-02",
            false
        },
        "SELECT CAST(@UPSO_Type AS VARCHAR) AS 'Type', CAST(@UPSO_MessageFrom AS DATETIME) AS 'From', CAST(@UPSO_MessageTo AS DATETIME) AS 'To', CAST(@UPSO_Status AS VARCHAR) AS 'Status', CAST(SUM(UPSO.TotalPrice) AS DECIMAL(18,2)) AS 'TotalPrice', CAST(COUNT(*) AS BIGINT) AS 'TotalCount', CAST(@UPSO_TeamId AS VARCHAR) AS 'TeamId' FROM UserProfileShopifyOrders UPSO WHERE UPSO.CompanyId = @UPSO_CompanyId AND UPSO.ConversionStatus = @UPSO_ConversionStatus AND UPSO.Payment = @UPSO_Payment AND UPSO.CreatedAt < CAST(@UPSO_MessageTo AS DATETIME) GROUP BY UPSO.Payment")]
    [TestCase(
        new object?[]
        {
            "b6d7e442-38ae-4b9a-b100-2951729768bc",
            "2022-01-01",
            "2022-01-02",
            true
        },
        "SELECT CAST(@UPSO_Type AS VARCHAR) AS 'Type', CAST(@UPSO_MessageFrom AS DATETIME) AS 'From', CAST(@UPSO_MessageTo AS DATETIME) AS 'To', CAST(@UPSO_Status AS VARCHAR) AS 'Status', CAST(SUM(UPSO.TotalPrice) AS DECIMAL(18,2)) AS 'TotalPrice', CAST(COUNT(*) AS BIGINT) AS 'TotalCount', CAST(UPSO.TeamId AS VARCHAR) AS 'TeamId' FROM UserProfileShopifyOrders UPSO WHERE UPSO.CompanyId = @UPSO_CompanyId AND UPSO.ConversionStatus = @UPSO_ConversionStatus AND UPSO.Payment = @UPSO_Payment AND UPSO.TeamId IS NOT NULL AND UPSO.CreatedAt > CAST(@UPSO_MessageFrom AS DATETIME) AND UPSO.CreatedAt < CAST(@UPSO_MessageTo AS DATETIME) GROUP BY UPSO.TeamId")]
    [TestCase(
        new object?[]
        {
            "b6d7e442-38ae-4b9a-b100-2951729768bc",
            null,
            null,
            true
        },
        "SELECT CAST(@UPSO_Type AS VARCHAR) AS 'Type', CAST(@UPSO_MessageFrom AS DATETIME) AS 'From', CAST(@UPSO_MessageTo AS DATETIME) AS 'To', CAST(@UPSO_Status AS VARCHAR) AS 'Status', CAST(SUM(UPSO.TotalPrice) AS DECIMAL(18,2)) AS 'TotalPrice', CAST(COUNT(*) AS BIGINT) AS 'TotalCount', CAST(UPSO.TeamId AS VARCHAR) AS 'TeamId' FROM UserProfileShopifyOrders UPSO WHERE UPSO.CompanyId = @UPSO_CompanyId AND UPSO.ConversionStatus = @UPSO_ConversionStatus AND UPSO.Payment = @UPSO_Payment AND UPSO.TeamId IS NOT NULL GROUP BY UPSO.TeamId")]
    [TestCase(
        new object?[]
        {
            "b6d7e442-38ae-4b9a-b100-2951729768bc",
            "2022-01-01",
            null,
            true
        },
        "SELECT CAST(@UPSO_Type AS VARCHAR) AS 'Type', CAST(@UPSO_MessageFrom AS DATETIME) AS 'From', CAST(@UPSO_MessageTo AS DATETIME) AS 'To', CAST(@UPSO_Status AS VARCHAR) AS 'Status', CAST(SUM(UPSO.TotalPrice) AS DECIMAL(18,2)) AS 'TotalPrice', CAST(COUNT(*) AS BIGINT) AS 'TotalCount', CAST(UPSO.TeamId AS VARCHAR) AS 'TeamId' FROM UserProfileShopifyOrders UPSO WHERE UPSO.CompanyId = @UPSO_CompanyId AND UPSO.ConversionStatus = @UPSO_ConversionStatus AND UPSO.Payment = @UPSO_Payment AND UPSO.TeamId IS NOT NULL AND UPSO.CreatedAt > CAST(@UPSO_MessageFrom AS DATETIME) GROUP BY UPSO.TeamId")]
    [TestCase(
        new object?[]
        {
            "b6d7e442-38ae-4b9a-b100-2951729768bc",
            null,
            "2022-01-02",
            true
        },
        "SELECT CAST(@UPSO_Type AS VARCHAR) AS 'Type', CAST(@UPSO_MessageFrom AS DATETIME) AS 'From', CAST(@UPSO_MessageTo AS DATETIME) AS 'To', CAST(@UPSO_Status AS VARCHAR) AS 'Status', CAST(SUM(UPSO.TotalPrice) AS DECIMAL(18,2)) AS 'TotalPrice', CAST(COUNT(*) AS BIGINT) AS 'TotalCount', CAST(UPSO.TeamId AS VARCHAR) AS 'TeamId' FROM UserProfileShopifyOrders UPSO WHERE UPSO.CompanyId = @UPSO_CompanyId AND UPSO.ConversionStatus = @UPSO_ConversionStatus AND UPSO.Payment = @UPSO_Payment AND UPSO.TeamId IS NOT NULL AND UPSO.CreatedAt < CAST(@UPSO_MessageTo AS DATETIME) GROUP BY UPSO.TeamId")]
    public void GetCheckoutLinkPaidShopifyOrderRecordsTest(object?[] parameters, string expectedQuery)
    {
        var result = InvokeMethod(
            _shopifySqlService!,
            "GetCheckoutLinkPaidShopifyOrderRecords",
            parameters);

        Assert.That(GetQueryString((IQueryable<ShopifyAttribution>) result), Is.EqualTo(expectedQuery));
    }

    [TestCase(
        new object?[]
        {
            "b6d7e442-38ae-4b9a-b100-2951729768bc",
            "2022-01-01",
            "2022-01-02",
            false
        },
        "SELECT CAST(@SPR_Type AS VARCHAR) AS 'Type', CAST(@SPR_MessageFrom AS DATETIME) AS 'From', CAST(@SPR_MessageTo AS DATETIME) AS 'To', CAST(@SPR_Status AS VARCHAR) AS 'Status', CAST(SUM(SPR.AmountReceived) AS DECIMAL(18,2)) AS 'TotalPrice', CAST(COUNT(*) AS BIGINT) AS 'TotalCount', CAST(@SPR_TeamId AS VARCHAR) AS 'TeamId' FROM StripePaymentRecords SPR WHERE SPR.CompanyId = @SPR_CompanyId AND SPR.Status = @SPR_PaymentStatus AND SPR.CreatedAt > CAST(@SPR_MessageFrom AS DATETIME) AND SPR.CreatedAt < CAST(@SPR_MessageTo AS DATETIME) GROUP BY SPR.Status")]
    [TestCase(
        new object?[]
        {
            "b6d7e442-38ae-4b9a-b100-2951729768bc",
            null,
            null,
            false
        },
        "SELECT CAST(@SPR_Type AS VARCHAR) AS 'Type', CAST(@SPR_MessageFrom AS DATETIME) AS 'From', CAST(@SPR_MessageTo AS DATETIME) AS 'To', CAST(@SPR_Status AS VARCHAR) AS 'Status', CAST(SUM(SPR.AmountReceived) AS DECIMAL(18,2)) AS 'TotalPrice', CAST(COUNT(*) AS BIGINT) AS 'TotalCount', CAST(@SPR_TeamId AS VARCHAR) AS 'TeamId' FROM StripePaymentRecords SPR WHERE SPR.CompanyId = @SPR_CompanyId AND SPR.Status = @SPR_PaymentStatus GROUP BY SPR.Status")]
    [TestCase(
        new object?[]
        {
            "b6d7e442-38ae-4b9a-b100-2951729768bc",
            "2022-01-01",
            null,
            false
        },
        "SELECT CAST(@SPR_Type AS VARCHAR) AS 'Type', CAST(@SPR_MessageFrom AS DATETIME) AS 'From', CAST(@SPR_MessageTo AS DATETIME) AS 'To', CAST(@SPR_Status AS VARCHAR) AS 'Status', CAST(SUM(SPR.AmountReceived) AS DECIMAL(18,2)) AS 'TotalPrice', CAST(COUNT(*) AS BIGINT) AS 'TotalCount', CAST(@SPR_TeamId AS VARCHAR) AS 'TeamId' FROM StripePaymentRecords SPR WHERE SPR.CompanyId = @SPR_CompanyId AND SPR.Status = @SPR_PaymentStatus AND SPR.CreatedAt > CAST(@SPR_MessageFrom AS DATETIME) GROUP BY SPR.Status")]
    [TestCase(
        new object?[]
        {
            "b6d7e442-38ae-4b9a-b100-2951729768bc",
            null,
            "2022-01-02",
            false
        },
        "SELECT CAST(@SPR_Type AS VARCHAR) AS 'Type', CAST(@SPR_MessageFrom AS DATETIME) AS 'From', CAST(@SPR_MessageTo AS DATETIME) AS 'To', CAST(@SPR_Status AS VARCHAR) AS 'Status', CAST(SUM(SPR.AmountReceived) AS DECIMAL(18,2)) AS 'TotalPrice', CAST(COUNT(*) AS BIGINT) AS 'TotalCount', CAST(@SPR_TeamId AS VARCHAR) AS 'TeamId' FROM StripePaymentRecords SPR WHERE SPR.CompanyId = @SPR_CompanyId AND SPR.Status = @SPR_PaymentStatus AND SPR.CreatedAt < CAST(@SPR_MessageTo AS DATETIME) GROUP BY SPR.Status")]
    [TestCase(
        new object?[]
        {
            "b6d7e442-38ae-4b9a-b100-2951729768bc",
            "2022-01-01",
            "2022-01-02",
            true
        },
        "SELECT CAST(@SPR_Type AS VARCHAR) AS 'Type', CAST(@SPR_MessageFrom AS DATETIME) AS 'From', CAST(@SPR_MessageTo AS DATETIME) AS 'To', CAST(@SPR_Status AS VARCHAR) AS 'Status', CAST(SUM(SPR.AmountReceived) AS DECIMAL(18,2)) AS 'TotalPrice', CAST(COUNT(*) AS BIGINT) AS 'TotalCount', CAST(SPR.TeamId AS VARCHAR) AS 'TeamId' FROM StripePaymentRecords SPR WHERE SPR.CompanyId = @SPR_CompanyId AND SPR.Status = @SPR_PaymentStatus AND SPR.TeamId IS NOT NULL AND SPR.CreatedAt > CAST(@SPR_MessageFrom AS DATETIME) AND SPR.CreatedAt < CAST(@SPR_MessageTo AS DATETIME) GROUP BY SPR.TeamId")]
    [TestCase(
        new object?[]
        {
            "b6d7e442-38ae-4b9a-b100-2951729768bc",
            null,
            null,
            true
        },
        "SELECT CAST(@SPR_Type AS VARCHAR) AS 'Type', CAST(@SPR_MessageFrom AS DATETIME) AS 'From', CAST(@SPR_MessageTo AS DATETIME) AS 'To', CAST(@SPR_Status AS VARCHAR) AS 'Status', CAST(SUM(SPR.AmountReceived) AS DECIMAL(18,2)) AS 'TotalPrice', CAST(COUNT(*) AS BIGINT) AS 'TotalCount', CAST(SPR.TeamId AS VARCHAR) AS 'TeamId' FROM StripePaymentRecords SPR WHERE SPR.CompanyId = @SPR_CompanyId AND SPR.Status = @SPR_PaymentStatus AND SPR.TeamId IS NOT NULL GROUP BY SPR.TeamId")]
    [TestCase(
        new object?[]
        {
            "b6d7e442-38ae-4b9a-b100-2951729768bc",
            "2022-01-01",
            null,
            true
        },
        "SELECT CAST(@SPR_Type AS VARCHAR) AS 'Type', CAST(@SPR_MessageFrom AS DATETIME) AS 'From', CAST(@SPR_MessageTo AS DATETIME) AS 'To', CAST(@SPR_Status AS VARCHAR) AS 'Status', CAST(SUM(SPR.AmountReceived) AS DECIMAL(18,2)) AS 'TotalPrice', CAST(COUNT(*) AS BIGINT) AS 'TotalCount', CAST(SPR.TeamId AS VARCHAR) AS 'TeamId' FROM StripePaymentRecords SPR WHERE SPR.CompanyId = @SPR_CompanyId AND SPR.Status = @SPR_PaymentStatus AND SPR.TeamId IS NOT NULL AND SPR.CreatedAt > CAST(@SPR_MessageFrom AS DATETIME) GROUP BY SPR.TeamId")]
    [TestCase(
        new object?[]
        {
            "b6d7e442-38ae-4b9a-b100-2951729768bc",
            null,
            "2022-01-02",
            true
        },
        "SELECT CAST(@SPR_Type AS VARCHAR) AS 'Type', CAST(@SPR_MessageFrom AS DATETIME) AS 'From', CAST(@SPR_MessageTo AS DATETIME) AS 'To', CAST(@SPR_Status AS VARCHAR) AS 'Status', CAST(SUM(SPR.AmountReceived) AS DECIMAL(18,2)) AS 'TotalPrice', CAST(COUNT(*) AS BIGINT) AS 'TotalCount', CAST(SPR.TeamId AS VARCHAR) AS 'TeamId' FROM StripePaymentRecords SPR WHERE SPR.CompanyId = @SPR_CompanyId AND SPR.Status = @SPR_PaymentStatus AND SPR.TeamId IS NOT NULL AND SPR.CreatedAt < CAST(@SPR_MessageTo AS DATETIME) GROUP BY SPR.TeamId")]
    public void GetStripePaidPaymentRecordsTest(object?[] parameters, string expectedQuery)
    {
        var result = InvokeMethod(
            _shopifySqlService!,
            "GetStripePaidPaymentRecords",
            parameters);

        Assert.That(GetQueryString((IQueryable<ShopifyAttribution>) result), Is.EqualTo(expectedQuery));
    }

    [TestCase(
        new object?[]
        {
            "b6d7e442-38ae-4b9a-b100-2951729768bc",
            "2022-01-01",
            "2022-01-02"
        },
        "SELECT CAST(@UPSO_Type AS VARCHAR) AS 'Type', CAST(@UPSO_MessageFrom AS DATETIME) AS 'From', CAST(@UPSO_MessageTo AS DATETIME) AS 'To', CAST(@UPSO_Status AS VARCHAR) AS 'Status', CAST(SUM(UPSO.TotalPrice) AS DECIMAL(18,2)) AS 'TotalPrice', CAST(COUNT(*) AS BIGINT) AS 'TotalCount', CAST(@UPSO_TeamId AS VARCHAR) AS 'TeamId' FROM UserProfileShopifyOrders UPSO WHERE UPSO.CompanyId = @UPSO_CompanyId AND UPSO.ConversionStatus = @UPSO_ConversionStatus AND UPSO.Payment = @UPSO_Payment AND UPSO.CreatedAt > CAST(@UPSO_MessageFrom AS DATETIME) AND UPSO.CreatedAt < CAST(@UPSO_MessageTo AS DATETIME) GROUP BY UPSO.ConversionStatus")]
    [TestCase(
        new object?[]
        {
            "b6d7e442-38ae-4b9a-b100-2951729768bc",
            null,
            null
        },
        "SELECT CAST(@UPSO_Type AS VARCHAR) AS 'Type', CAST(@UPSO_MessageFrom AS DATETIME) AS 'From', CAST(@UPSO_MessageTo AS DATETIME) AS 'To', CAST(@UPSO_Status AS VARCHAR) AS 'Status', CAST(SUM(UPSO.TotalPrice) AS DECIMAL(18,2)) AS 'TotalPrice', CAST(COUNT(*) AS BIGINT) AS 'TotalCount', CAST(@UPSO_TeamId AS VARCHAR) AS 'TeamId' FROM UserProfileShopifyOrders UPSO WHERE UPSO.CompanyId = @UPSO_CompanyId AND UPSO.ConversionStatus = @UPSO_ConversionStatus AND UPSO.Payment = @UPSO_Payment GROUP BY UPSO.ConversionStatus")]
    [TestCase(
        new object?[]
        {
            "b6d7e442-38ae-4b9a-b100-2951729768bc",
            "2022-01-01",
            null
        },
        "SELECT CAST(@UPSO_Type AS VARCHAR) AS 'Type', CAST(@UPSO_MessageFrom AS DATETIME) AS 'From', CAST(@UPSO_MessageTo AS DATETIME) AS 'To', CAST(@UPSO_Status AS VARCHAR) AS 'Status', CAST(SUM(UPSO.TotalPrice) AS DECIMAL(18,2)) AS 'TotalPrice', CAST(COUNT(*) AS BIGINT) AS 'TotalCount', CAST(@UPSO_TeamId AS VARCHAR) AS 'TeamId' FROM UserProfileShopifyOrders UPSO WHERE UPSO.CompanyId = @UPSO_CompanyId AND UPSO.ConversionStatus = @UPSO_ConversionStatus AND UPSO.Payment = @UPSO_Payment AND UPSO.CreatedAt > CAST(@UPSO_MessageFrom AS DATETIME) GROUP BY UPSO.ConversionStatus")]
    [TestCase(
        new object?[]
        {
            "b6d7e442-38ae-4b9a-b100-2951729768bc",
            null,
            "2022-01-02"
        },
        "SELECT CAST(@UPSO_Type AS VARCHAR) AS 'Type', CAST(@UPSO_MessageFrom AS DATETIME) AS 'From', CAST(@UPSO_MessageTo AS DATETIME) AS 'To', CAST(@UPSO_Status AS VARCHAR) AS 'Status', CAST(SUM(UPSO.TotalPrice) AS DECIMAL(18,2)) AS 'TotalPrice', CAST(COUNT(*) AS BIGINT) AS 'TotalCount', CAST(@UPSO_TeamId AS VARCHAR) AS 'TeamId' FROM UserProfileShopifyOrders UPSO WHERE UPSO.CompanyId = @UPSO_CompanyId AND UPSO.ConversionStatus = @UPSO_ConversionStatus AND UPSO.Payment = @UPSO_Payment AND UPSO.CreatedAt < CAST(@UPSO_MessageTo AS DATETIME) GROUP BY UPSO.ConversionStatus")]
    public void GetCompanyInfluencedSalesPerformanceTest(object?[] parameters, string expectedQuery)
    {
        var result = InvokeMethod(
            _shopifySqlService!,
            "GetCompanyInfluencedSalesPerformance",
            parameters);

        Assert.That(GetQueryString((IQueryable<ShopifyAttribution>) result), Is.EqualTo(expectedQuery));
    }

    [TestCase(
        new object?[]
        {
            "URS",
            "b6d7e442-38ae-4b9a-b100-2951729768bc",
            "2022-01-01",
            "2022-01-02",
            1L
        },
        "SELECT CAST(URS.Id AS BIGINT) AS StaffId, CAST(COALESCE(CSLGRSQ.LinkSharedCount, 0) AS INTEGER) AS LinkSharedCount, CAST(COALESCE(CSLTRSQ.LinkSharedClicks, 0) AS INTEGER) AS LinkSharedClicks, CAST(COALESCE(CSLGRSQ.PaymentLinkSharedCount, 0) AS INTEGER) AS PaymentLinkSharedCount,CAST(COALESCE(CSLTRSQ.PaymentLinkSharedClicks,0) AS INTEGER) AS PaymentLinkSharedClicks,CAST((COALESCE(UPSOSQ.PaymentLinkSharedPaid, 0) + COALESCE(SPRSQ.PaymentLinkSharedPaid, 0)) AS INTEGER) AS PaymentLinkSharedPaid,CAST(COALESCE(UPSOSQ.PaymentLinkConvertedAmount, 0.00) + COALESCE(SPRSQ.PaymentLinkConvertedAmount, 0.00) AS DECIMAL(18, 2)) AS PaymentLinkConvertedAmount FROM UserRoleStaffs URS JOIN CompanyTeamMembers CTM ON URS.Id = CTM.StaffId AND CTM.CompanyTeamId = @CTM_TeamId LEFT JOIN (SELECT CSLGR.StaffId AS StaffId, COUNT(CSLGR.Id) AS LinkSharedCount,COUNT(IIF(CSLGR.ShareLinkType IN (@CSLGR_PaymentLinkSharedCount_ShareLinkType_0,@CSLGR_PaymentLinkSharedCount_ShareLinkType_1) , CSLGR.Id, NULL)) AS PaymentLinkSharedCount FROM CoreShareLinkGenerationRecords CSLGR WHERE CSLGR.ShareLinkType != @CSLGR_ShareLinkType AND CSLGR.CreatedAt > CAST(@CSLGR_MessageFrom AS DATETIME) AND CSLGR.CreatedAt < CAST(@CSLGR_MessageTo AS DATETIME) GROUP BY CSLGR.StaffId ) CSLGRSQ ON URS.Id = CSLGRSQ.StaffId LEFT JOIN (SELECT CSLTR.StaffId AS StaffId, COUNT(CSLTR.TrackingId) AS LinkSharedClicks,COUNT(IIF(CSLTR.ShareLinkType IN (@CSLTR_PaymentLinkSharedClicks_ShareLinkType_0,@CSLTR_PaymentLinkSharedClicks_ShareLinkType_1) , CSLTR.Id, NULL)) AS PaymentLinkSharedClicks FROM CoreShareLinkTrackingRecords CSLTR WHERE CSLTR.ShareLinkType != @CSLTR_ShareLinkType AND CSLTR.TrackedAt > CAST(@CSLTR_MessageFrom AS DATETIME) AND CSLTR.TrackedAt < CAST(@CSLTR_MessageTo AS DATETIME) GROUP BY CSLTR.StaffId ) CSLTRSQ ON URS.Id = CSLTRSQ.StaffId LEFT JOIN (SELECT UPSO.StaffId AS StaffId, COUNT(UPSO.Id) AS PaymentLinkSharedPaid, COALESCE(SUM(UPSO.TotalPrice),0) AS PaymentLinkConvertedAmount FROM UserProfileShopifyOrders UPSO WHERE UPSO.Payment = @UPSO_Payment AND UPSO.ConversionStatus = @UPSO_ConversionStatus AND UPSO.CreatedAt > CAST(@UPSO_MessageFrom AS DATETIME) AND UPSO.CreatedAt < CAST(@UPSO_MessageTo AS DATETIME) GROUP BY UPSO.StaffId ) UPSOSQ ON URS.Id = UPSOSQ.StaffId LEFT JOIN (SELECT SPR.StaffId AS StaffId, COUNT(SPR.Id) AS PaymentLinkSharedPaid, COALESCE(SUM(SPR.AmountReceived), 0) - COALESCE(SUM(SPR.RefundedAmount), 0) AS PaymentLinkConvertedAmount FROM StripePaymentRecords SPR WHERE SPR.Status = @SPR_Status AND SPR.CreatedAt > CAST(@SPR_MessageFrom AS DATETIME) AND SPR.CreatedAt < CAST(@SPR_MessageTo AS DATETIME) GROUP BY SPR.StaffId ) SPRSQ ON URS.Id = SPRSQ.StaffId WHERE URS.CompanyId = @URS_CompanyId AND URS.Id != @URS_Id GROUP BY URS.Id, URS.[Order], CSLGRSQ.LinkSharedCount, CSLTRSQ.LinkSharedClicks, CSLGRSQ.PaymentLinkSharedCount, CSLTRSQ.PaymentLinkSharedClicks,UPSOSQ.PaymentLinkSharedPaid, UPSOSQ.PaymentLinkConvertedAmount, SPRSQ.PaymentLinkSharedPaid, SPRSQ.PaymentLinkConvertedAmount")]
    [TestCase(
        new object?[]
        {
            "URS",
            "b6d7e442-38ae-4b9a-b100-2951729768bc",
            "2022-01-01",
            "2022-01-02",
            null
        },
          "SELECT CAST(URS.Id AS BIGINT) AS StaffId, CAST(COALESCE(CSLGRSQ.LinkSharedCount, 0) AS INTEGER) AS LinkSharedCount, CAST(COALESCE(CSLTRSQ.LinkSharedClicks, 0) AS INTEGER) AS LinkSharedClicks, CAST(COALESCE(CSLGRSQ.PaymentLinkSharedCount, 0) AS INTEGER) AS PaymentLinkSharedCount,CAST(COALESCE(CSLTRSQ.PaymentLinkSharedClicks,0) AS INTEGER) AS PaymentLinkSharedClicks,CAST((COALESCE(UPSOSQ.PaymentLinkSharedPaid, 0) + COALESCE(SPRSQ.PaymentLinkSharedPaid, 0)) AS INTEGER) AS PaymentLinkSharedPaid,CAST(COALESCE(UPSOSQ.PaymentLinkConvertedAmount, 0.00) + COALESCE(SPRSQ.PaymentLinkConvertedAmount, 0.00) AS DECIMAL(18, 2)) AS PaymentLinkConvertedAmount FROM UserRoleStaffs URS LEFT JOIN (SELECT CSLGR.StaffId AS StaffId, COUNT(CSLGR.Id) AS LinkSharedCount,COUNT(IIF(CSLGR.ShareLinkType IN (@CSLGR_PaymentLinkSharedCount_ShareLinkType_0,@CSLGR_PaymentLinkSharedCount_ShareLinkType_1) , CSLGR.Id, NULL)) AS PaymentLinkSharedCount FROM CoreShareLinkGenerationRecords CSLGR WHERE CSLGR.ShareLinkType != @CSLGR_ShareLinkType AND CSLGR.CreatedAt > CAST(@CSLGR_MessageFrom AS DATETIME) AND CSLGR.CreatedAt < CAST(@CSLGR_MessageTo AS DATETIME) GROUP BY CSLGR.StaffId ) CSLGRSQ ON URS.Id = CSLGRSQ.StaffId LEFT JOIN (SELECT CSLTR.StaffId AS StaffId, COUNT(CSLTR.TrackingId) AS LinkSharedClicks,COUNT(IIF(CSLTR.ShareLinkType IN (@CSLTR_PaymentLinkSharedClicks_ShareLinkType_0,@CSLTR_PaymentLinkSharedClicks_ShareLinkType_1) , CSLTR.Id, NULL)) AS PaymentLinkSharedClicks FROM CoreShareLinkTrackingRecords CSLTR WHERE CSLTR.ShareLinkType != @CSLTR_ShareLinkType AND CSLTR.TrackedAt > CAST(@CSLTR_MessageFrom AS DATETIME) AND CSLTR.TrackedAt < CAST(@CSLTR_MessageTo AS DATETIME) GROUP BY CSLTR.StaffId ) CSLTRSQ ON URS.Id = CSLTRSQ.StaffId LEFT JOIN (SELECT UPSO.StaffId AS StaffId, COUNT(UPSO.Id) AS PaymentLinkSharedPaid, COALESCE(SUM(UPSO.TotalPrice),0) AS PaymentLinkConvertedAmount FROM UserProfileShopifyOrders UPSO WHERE UPSO.Payment = @UPSO_Payment AND UPSO.ConversionStatus = @UPSO_ConversionStatus AND UPSO.CreatedAt > CAST(@UPSO_MessageFrom AS DATETIME) AND UPSO.CreatedAt < CAST(@UPSO_MessageTo AS DATETIME) GROUP BY UPSO.StaffId ) UPSOSQ ON URS.Id = UPSOSQ.StaffId LEFT JOIN (SELECT SPR.StaffId AS StaffId, COUNT(SPR.Id) AS PaymentLinkSharedPaid, COALESCE(SUM(SPR.AmountReceived), 0) - COALESCE(SUM(SPR.RefundedAmount), 0) AS PaymentLinkConvertedAmount FROM StripePaymentRecords SPR WHERE SPR.Status = @SPR_Status AND SPR.CreatedAt > CAST(@SPR_MessageFrom AS DATETIME) AND SPR.CreatedAt < CAST(@SPR_MessageTo AS DATETIME) GROUP BY SPR.StaffId ) SPRSQ ON URS.Id = SPRSQ.StaffId WHERE URS.CompanyId = @URS_CompanyId AND URS.Id != @URS_Id GROUP BY URS.Id, URS.[Order], CSLGRSQ.LinkSharedCount, CSLTRSQ.LinkSharedClicks, CSLGRSQ.PaymentLinkSharedCount, CSLTRSQ.PaymentLinkSharedClicks,UPSOSQ.PaymentLinkSharedPaid, UPSOSQ.PaymentLinkConvertedAmount, SPRSQ.PaymentLinkSharedPaid, SPRSQ.PaymentLinkConvertedAmount")]
    [TestCase(
        new object?[]
        {
            "URS",
            "b6d7e442-38ae-4b9a-b100-2951729768bc",
            null,
            null,
            null
        },
          "SELECT CAST(URS.Id AS BIGINT) AS StaffId, CAST(COALESCE(CSLGRSQ.LinkSharedCount, 0) AS INTEGER) AS LinkSharedCount, CAST(COALESCE(CSLTRSQ.LinkSharedClicks, 0) AS INTEGER) AS LinkSharedClicks, CAST(COALESCE(CSLGRSQ.PaymentLinkSharedCount, 0) AS INTEGER) AS PaymentLinkSharedCount,CAST(COALESCE(CSLTRSQ.PaymentLinkSharedClicks,0) AS INTEGER) AS PaymentLinkSharedClicks,CAST((COALESCE(UPSOSQ.PaymentLinkSharedPaid, 0) + COALESCE(SPRSQ.PaymentLinkSharedPaid, 0)) AS INTEGER) AS PaymentLinkSharedPaid,CAST(COALESCE(UPSOSQ.PaymentLinkConvertedAmount, 0.00) + COALESCE(SPRSQ.PaymentLinkConvertedAmount, 0.00) AS DECIMAL(18, 2)) AS PaymentLinkConvertedAmount FROM UserRoleStaffs URS LEFT JOIN (SELECT CSLGR.StaffId AS StaffId, COUNT(CSLGR.Id) AS LinkSharedCount,COUNT(IIF(CSLGR.ShareLinkType IN (@CSLGR_PaymentLinkSharedCount_ShareLinkType_0,@CSLGR_PaymentLinkSharedCount_ShareLinkType_1) , CSLGR.Id, NULL)) AS PaymentLinkSharedCount FROM CoreShareLinkGenerationRecords CSLGR WHERE CSLGR.ShareLinkType != @CSLGR_ShareLinkType GROUP BY CSLGR.StaffId ) CSLGRSQ ON URS.Id = CSLGRSQ.StaffId LEFT JOIN (SELECT CSLTR.StaffId AS StaffId, COUNT(CSLTR.TrackingId) AS LinkSharedClicks,COUNT(IIF(CSLTR.ShareLinkType IN (@CSLTR_PaymentLinkSharedClicks_ShareLinkType_0,@CSLTR_PaymentLinkSharedClicks_ShareLinkType_1) , CSLTR.Id, NULL)) AS PaymentLinkSharedClicks FROM CoreShareLinkTrackingRecords CSLTR WHERE CSLTR.ShareLinkType != @CSLTR_ShareLinkType GROUP BY CSLTR.StaffId ) CSLTRSQ ON URS.Id = CSLTRSQ.StaffId LEFT JOIN (SELECT UPSO.StaffId AS StaffId, COUNT(UPSO.Id) AS PaymentLinkSharedPaid, COALESCE(SUM(UPSO.TotalPrice),0) AS PaymentLinkConvertedAmount FROM UserProfileShopifyOrders UPSO WHERE UPSO.Payment = @UPSO_Payment AND UPSO.ConversionStatus = @UPSO_ConversionStatus GROUP BY UPSO.StaffId ) UPSOSQ ON URS.Id = UPSOSQ.StaffId LEFT JOIN (SELECT SPR.StaffId AS StaffId, COUNT(SPR.Id) AS PaymentLinkSharedPaid, COALESCE(SUM(SPR.AmountReceived), 0) - COALESCE(SUM(SPR.RefundedAmount), 0) AS PaymentLinkConvertedAmount FROM StripePaymentRecords SPR WHERE SPR.Status = @SPR_Status GROUP BY SPR.StaffId ) SPRSQ ON URS.Id = SPRSQ.StaffId WHERE URS.CompanyId = @URS_CompanyId AND URS.Id != @URS_Id GROUP BY URS.Id, URS.[Order], CSLGRSQ.LinkSharedCount, CSLTRSQ.LinkSharedClicks, CSLGRSQ.PaymentLinkSharedCount, CSLTRSQ.PaymentLinkSharedClicks,UPSOSQ.PaymentLinkSharedPaid, UPSOSQ.PaymentLinkConvertedAmount, SPRSQ.PaymentLinkSharedPaid, SPRSQ.PaymentLinkConvertedAmount")]
    [TestCase(
        new object?[]
        {
            "URS",
            "b6d7e442-38ae-4b9a-b100-2951729768bc",
            "2022-01-01",
            null,
            null
        },
          "SELECT CAST(URS.Id AS BIGINT) AS StaffId, CAST(COALESCE(CSLGRSQ.LinkSharedCount, 0) AS INTEGER) AS LinkSharedCount, CAST(COALESCE(CSLTRSQ.LinkSharedClicks, 0) AS INTEGER) AS LinkSharedClicks, CAST(COALESCE(CSLGRSQ.PaymentLinkSharedCount, 0) AS INTEGER) AS PaymentLinkSharedCount,CAST(COALESCE(CSLTRSQ.PaymentLinkSharedClicks,0) AS INTEGER) AS PaymentLinkSharedClicks,CAST((COALESCE(UPSOSQ.PaymentLinkSharedPaid, 0) + COALESCE(SPRSQ.PaymentLinkSharedPaid, 0)) AS INTEGER) AS PaymentLinkSharedPaid,CAST(COALESCE(UPSOSQ.PaymentLinkConvertedAmount, 0.00) + COALESCE(SPRSQ.PaymentLinkConvertedAmount, 0.00) AS DECIMAL(18, 2)) AS PaymentLinkConvertedAmount FROM UserRoleStaffs URS LEFT JOIN (SELECT CSLGR.StaffId AS StaffId, COUNT(CSLGR.Id) AS LinkSharedCount,COUNT(IIF(CSLGR.ShareLinkType IN (@CSLGR_PaymentLinkSharedCount_ShareLinkType_0,@CSLGR_PaymentLinkSharedCount_ShareLinkType_1) , CSLGR.Id, NULL)) AS PaymentLinkSharedCount FROM CoreShareLinkGenerationRecords CSLGR WHERE CSLGR.ShareLinkType != @CSLGR_ShareLinkType AND CSLGR.CreatedAt > CAST(@CSLGR_MessageFrom AS DATETIME) GROUP BY CSLGR.StaffId ) CSLGRSQ ON URS.Id = CSLGRSQ.StaffId LEFT JOIN (SELECT CSLTR.StaffId AS StaffId, COUNT(CSLTR.TrackingId) AS LinkSharedClicks,COUNT(IIF(CSLTR.ShareLinkType IN (@CSLTR_PaymentLinkSharedClicks_ShareLinkType_0,@CSLTR_PaymentLinkSharedClicks_ShareLinkType_1) , CSLTR.Id, NULL)) AS PaymentLinkSharedClicks FROM CoreShareLinkTrackingRecords CSLTR WHERE CSLTR.ShareLinkType != @CSLTR_ShareLinkType AND CSLTR.TrackedAt > CAST(@CSLTR_MessageFrom AS DATETIME) GROUP BY CSLTR.StaffId ) CSLTRSQ ON URS.Id = CSLTRSQ.StaffId LEFT JOIN (SELECT UPSO.StaffId AS StaffId, COUNT(UPSO.Id) AS PaymentLinkSharedPaid, COALESCE(SUM(UPSO.TotalPrice),0) AS PaymentLinkConvertedAmount FROM UserProfileShopifyOrders UPSO WHERE UPSO.Payment = @UPSO_Payment AND UPSO.ConversionStatus = @UPSO_ConversionStatus AND UPSO.CreatedAt > CAST(@UPSO_MessageFrom AS DATETIME) GROUP BY UPSO.StaffId ) UPSOSQ ON URS.Id = UPSOSQ.StaffId LEFT JOIN (SELECT SPR.StaffId AS StaffId, COUNT(SPR.Id) AS PaymentLinkSharedPaid, COALESCE(SUM(SPR.AmountReceived), 0) - COALESCE(SUM(SPR.RefundedAmount), 0) AS PaymentLinkConvertedAmount FROM StripePaymentRecords SPR WHERE SPR.Status = @SPR_Status AND SPR.CreatedAt > CAST(@SPR_MessageFrom AS DATETIME) GROUP BY SPR.StaffId ) SPRSQ ON URS.Id = SPRSQ.StaffId WHERE URS.CompanyId = @URS_CompanyId AND URS.Id != @URS_Id GROUP BY URS.Id, URS.[Order], CSLGRSQ.LinkSharedCount, CSLTRSQ.LinkSharedClicks, CSLGRSQ.PaymentLinkSharedCount, CSLTRSQ.PaymentLinkSharedClicks,UPSOSQ.PaymentLinkSharedPaid, UPSOSQ.PaymentLinkConvertedAmount, SPRSQ.PaymentLinkSharedPaid, SPRSQ.PaymentLinkConvertedAmount")]
    [TestCase(
        new object?[]
        {
            "URS",
            "b6d7e442-38ae-4b9a-b100-2951729768bc",
            null,
            "2022-01-02",
            null
        },
          "SELECT CAST(URS.Id AS BIGINT) AS StaffId, CAST(COALESCE(CSLGRSQ.LinkSharedCount, 0) AS INTEGER) AS LinkSharedCount, CAST(COALESCE(CSLTRSQ.LinkSharedClicks, 0) AS INTEGER) AS LinkSharedClicks, CAST(COALESCE(CSLGRSQ.PaymentLinkSharedCount, 0) AS INTEGER) AS PaymentLinkSharedCount,CAST(COALESCE(CSLTRSQ.PaymentLinkSharedClicks,0) AS INTEGER) AS PaymentLinkSharedClicks,CAST((COALESCE(UPSOSQ.PaymentLinkSharedPaid, 0) + COALESCE(SPRSQ.PaymentLinkSharedPaid, 0)) AS INTEGER) AS PaymentLinkSharedPaid,CAST(COALESCE(UPSOSQ.PaymentLinkConvertedAmount, 0.00) + COALESCE(SPRSQ.PaymentLinkConvertedAmount, 0.00) AS DECIMAL(18, 2)) AS PaymentLinkConvertedAmount FROM UserRoleStaffs URS LEFT JOIN (SELECT CSLGR.StaffId AS StaffId, COUNT(CSLGR.Id) AS LinkSharedCount,COUNT(IIF(CSLGR.ShareLinkType IN (@CSLGR_PaymentLinkSharedCount_ShareLinkType_0,@CSLGR_PaymentLinkSharedCount_ShareLinkType_1) , CSLGR.Id, NULL)) AS PaymentLinkSharedCount FROM CoreShareLinkGenerationRecords CSLGR WHERE CSLGR.ShareLinkType != @CSLGR_ShareLinkType AND CSLGR.CreatedAt < CAST(@CSLGR_MessageTo AS DATETIME) GROUP BY CSLGR.StaffId ) CSLGRSQ ON URS.Id = CSLGRSQ.StaffId LEFT JOIN (SELECT CSLTR.StaffId AS StaffId, COUNT(CSLTR.TrackingId) AS LinkSharedClicks,COUNT(IIF(CSLTR.ShareLinkType IN (@CSLTR_PaymentLinkSharedClicks_ShareLinkType_0,@CSLTR_PaymentLinkSharedClicks_ShareLinkType_1) , CSLTR.Id, NULL)) AS PaymentLinkSharedClicks FROM CoreShareLinkTrackingRecords CSLTR WHERE CSLTR.ShareLinkType != @CSLTR_ShareLinkType AND CSLTR.TrackedAt < CAST(@CSLTR_MessageTo AS DATETIME) GROUP BY CSLTR.StaffId ) CSLTRSQ ON URS.Id = CSLTRSQ.StaffId LEFT JOIN (SELECT UPSO.StaffId AS StaffId, COUNT(UPSO.Id) AS PaymentLinkSharedPaid, COALESCE(SUM(UPSO.TotalPrice),0) AS PaymentLinkConvertedAmount FROM UserProfileShopifyOrders UPSO WHERE UPSO.Payment = @UPSO_Payment AND UPSO.ConversionStatus = @UPSO_ConversionStatus AND UPSO.CreatedAt < CAST(@UPSO_MessageTo AS DATETIME) GROUP BY UPSO.StaffId ) UPSOSQ ON URS.Id = UPSOSQ.StaffId LEFT JOIN (SELECT SPR.StaffId AS StaffId, COUNT(SPR.Id) AS PaymentLinkSharedPaid, COALESCE(SUM(SPR.AmountReceived), 0) - COALESCE(SUM(SPR.RefundedAmount), 0) AS PaymentLinkConvertedAmount FROM StripePaymentRecords SPR WHERE SPR.Status = @SPR_Status AND SPR.CreatedAt < CAST(@SPR_MessageTo AS DATETIME) GROUP BY SPR.StaffId ) SPRSQ ON URS.Id = SPRSQ.StaffId WHERE URS.CompanyId = @URS_CompanyId AND URS.Id != @URS_Id GROUP BY URS.Id, URS.[Order], CSLGRSQ.LinkSharedCount, CSLTRSQ.LinkSharedClicks, CSLGRSQ.PaymentLinkSharedCount, CSLTRSQ.PaymentLinkSharedClicks,UPSOSQ.PaymentLinkSharedPaid, UPSOSQ.PaymentLinkConvertedAmount, SPRSQ.PaymentLinkSharedPaid, SPRSQ.PaymentLinkConvertedAmount")]
    [TestCase(
        new object?[]
        {
            "URS",
            "b6d7e442-38ae-4b9a-b100-2951729768bc",
            null,
            null,
            1L
        },
          "SELECT CAST(URS.Id AS BIGINT) AS StaffId, CAST(COALESCE(CSLGRSQ.LinkSharedCount, 0) AS INTEGER) AS LinkSharedCount, CAST(COALESCE(CSLTRSQ.LinkSharedClicks, 0) AS INTEGER) AS LinkSharedClicks, CAST(COALESCE(CSLGRSQ.PaymentLinkSharedCount, 0) AS INTEGER) AS PaymentLinkSharedCount,CAST(COALESCE(CSLTRSQ.PaymentLinkSharedClicks,0) AS INTEGER) AS PaymentLinkSharedClicks,CAST((COALESCE(UPSOSQ.PaymentLinkSharedPaid, 0) + COALESCE(SPRSQ.PaymentLinkSharedPaid, 0)) AS INTEGER) AS PaymentLinkSharedPaid,CAST(COALESCE(UPSOSQ.PaymentLinkConvertedAmount, 0.00) + COALESCE(SPRSQ.PaymentLinkConvertedAmount, 0.00) AS DECIMAL(18, 2)) AS PaymentLinkConvertedAmount FROM UserRoleStaffs URS JOIN CompanyTeamMembers CTM ON URS.Id = CTM.StaffId AND CTM.CompanyTeamId = @CTM_TeamId LEFT JOIN (SELECT CSLGR.StaffId AS StaffId, COUNT(CSLGR.Id) AS LinkSharedCount,COUNT(IIF(CSLGR.ShareLinkType IN (@CSLGR_PaymentLinkSharedCount_ShareLinkType_0,@CSLGR_PaymentLinkSharedCount_ShareLinkType_1) , CSLGR.Id, NULL)) AS PaymentLinkSharedCount FROM CoreShareLinkGenerationRecords CSLGR WHERE CSLGR.ShareLinkType != @CSLGR_ShareLinkType GROUP BY CSLGR.StaffId ) CSLGRSQ ON URS.Id = CSLGRSQ.StaffId LEFT JOIN (SELECT CSLTR.StaffId AS StaffId, COUNT(CSLTR.TrackingId) AS LinkSharedClicks,COUNT(IIF(CSLTR.ShareLinkType IN (@CSLTR_PaymentLinkSharedClicks_ShareLinkType_0,@CSLTR_PaymentLinkSharedClicks_ShareLinkType_1) , CSLTR.Id, NULL)) AS PaymentLinkSharedClicks FROM CoreShareLinkTrackingRecords CSLTR WHERE CSLTR.ShareLinkType != @CSLTR_ShareLinkType GROUP BY CSLTR.StaffId ) CSLTRSQ ON URS.Id = CSLTRSQ.StaffId LEFT JOIN (SELECT UPSO.StaffId AS StaffId, COUNT(UPSO.Id) AS PaymentLinkSharedPaid, COALESCE(SUM(UPSO.TotalPrice),0) AS PaymentLinkConvertedAmount FROM UserProfileShopifyOrders UPSO WHERE UPSO.Payment = @UPSO_Payment AND UPSO.ConversionStatus = @UPSO_ConversionStatus GROUP BY UPSO.StaffId ) UPSOSQ ON URS.Id = UPSOSQ.StaffId LEFT JOIN (SELECT SPR.StaffId AS StaffId, COUNT(SPR.Id) AS PaymentLinkSharedPaid, COALESCE(SUM(SPR.AmountReceived), 0) - COALESCE(SUM(SPR.RefundedAmount), 0) AS PaymentLinkConvertedAmount FROM StripePaymentRecords SPR WHERE SPR.Status = @SPR_Status GROUP BY SPR.StaffId ) SPRSQ ON URS.Id = SPRSQ.StaffId WHERE URS.CompanyId = @URS_CompanyId AND URS.Id != @URS_Id GROUP BY URS.Id, URS.[Order], CSLGRSQ.LinkSharedCount, CSLTRSQ.LinkSharedClicks, CSLGRSQ.PaymentLinkSharedCount, CSLTRSQ.PaymentLinkSharedClicks,UPSOSQ.PaymentLinkSharedPaid, UPSOSQ.PaymentLinkConvertedAmount, SPRSQ.PaymentLinkSharedPaid, SPRSQ.PaymentLinkConvertedAmount")]
    [TestCase(
        new object?[]
        {
            "URS",
            "b6d7e442-38ae-4b9a-b100-2951729768bc",
            "2022-01-01",
            null,
            1L
        },
          "SELECT CAST(URS.Id AS BIGINT) AS StaffId, CAST(COALESCE(CSLGRSQ.LinkSharedCount, 0) AS INTEGER) AS LinkSharedCount, CAST(COALESCE(CSLTRSQ.LinkSharedClicks, 0) AS INTEGER) AS LinkSharedClicks, CAST(COALESCE(CSLGRSQ.PaymentLinkSharedCount, 0) AS INTEGER) AS PaymentLinkSharedCount,CAST(COALESCE(CSLTRSQ.PaymentLinkSharedClicks,0) AS INTEGER) AS PaymentLinkSharedClicks,CAST((COALESCE(UPSOSQ.PaymentLinkSharedPaid, 0) + COALESCE(SPRSQ.PaymentLinkSharedPaid, 0)) AS INTEGER) AS PaymentLinkSharedPaid,CAST(COALESCE(UPSOSQ.PaymentLinkConvertedAmount, 0.00) + COALESCE(SPRSQ.PaymentLinkConvertedAmount, 0.00) AS DECIMAL(18, 2)) AS PaymentLinkConvertedAmount FROM UserRoleStaffs URS JOIN CompanyTeamMembers CTM ON URS.Id = CTM.StaffId AND CTM.CompanyTeamId = @CTM_TeamId LEFT JOIN (SELECT CSLGR.StaffId AS StaffId, COUNT(CSLGR.Id) AS LinkSharedCount,COUNT(IIF(CSLGR.ShareLinkType IN (@CSLGR_PaymentLinkSharedCount_ShareLinkType_0,@CSLGR_PaymentLinkSharedCount_ShareLinkType_1) , CSLGR.Id, NULL)) AS PaymentLinkSharedCount FROM CoreShareLinkGenerationRecords CSLGR WHERE CSLGR.ShareLinkType != @CSLGR_ShareLinkType AND CSLGR.CreatedAt > CAST(@CSLGR_MessageFrom AS DATETIME) GROUP BY CSLGR.StaffId ) CSLGRSQ ON URS.Id = CSLGRSQ.StaffId LEFT JOIN (SELECT CSLTR.StaffId AS StaffId, COUNT(CSLTR.TrackingId) AS LinkSharedClicks,COUNT(IIF(CSLTR.ShareLinkType IN (@CSLTR_PaymentLinkSharedClicks_ShareLinkType_0,@CSLTR_PaymentLinkSharedClicks_ShareLinkType_1) , CSLTR.Id, NULL)) AS PaymentLinkSharedClicks FROM CoreShareLinkTrackingRecords CSLTR WHERE CSLTR.ShareLinkType != @CSLTR_ShareLinkType AND CSLTR.TrackedAt > CAST(@CSLTR_MessageFrom AS DATETIME) GROUP BY CSLTR.StaffId ) CSLTRSQ ON URS.Id = CSLTRSQ.StaffId LEFT JOIN (SELECT UPSO.StaffId AS StaffId, COUNT(UPSO.Id) AS PaymentLinkSharedPaid, COALESCE(SUM(UPSO.TotalPrice),0) AS PaymentLinkConvertedAmount FROM UserProfileShopifyOrders UPSO WHERE UPSO.Payment = @UPSO_Payment AND UPSO.ConversionStatus = @UPSO_ConversionStatus AND UPSO.CreatedAt > CAST(@UPSO_MessageFrom AS DATETIME) GROUP BY UPSO.StaffId ) UPSOSQ ON URS.Id = UPSOSQ.StaffId LEFT JOIN (SELECT SPR.StaffId AS StaffId, COUNT(SPR.Id) AS PaymentLinkSharedPaid, COALESCE(SUM(SPR.AmountReceived), 0) - COALESCE(SUM(SPR.RefundedAmount), 0) AS PaymentLinkConvertedAmount FROM StripePaymentRecords SPR WHERE SPR.Status = @SPR_Status AND SPR.CreatedAt > CAST(@SPR_MessageFrom AS DATETIME) GROUP BY SPR.StaffId ) SPRSQ ON URS.Id = SPRSQ.StaffId WHERE URS.CompanyId = @URS_CompanyId AND URS.Id != @URS_Id GROUP BY URS.Id, URS.[Order], CSLGRSQ.LinkSharedCount, CSLTRSQ.LinkSharedClicks, CSLGRSQ.PaymentLinkSharedCount, CSLTRSQ.PaymentLinkSharedClicks,UPSOSQ.PaymentLinkSharedPaid, UPSOSQ.PaymentLinkConvertedAmount, SPRSQ.PaymentLinkSharedPaid, SPRSQ.PaymentLinkConvertedAmount"
)]
    [TestCase(
        new object?[]
        {
            "URS",
            "b6d7e442-38ae-4b9a-b100-2951729768bc",
            null,
            "2022-01-02",
            1L
        },
          "SELECT CAST(URS.Id AS BIGINT) AS StaffId, CAST(COALESCE(CSLGRSQ.LinkSharedCount, 0) AS INTEGER) AS LinkSharedCount, CAST(COALESCE(CSLTRSQ.LinkSharedClicks, 0) AS INTEGER) AS LinkSharedClicks, CAST(COALESCE(CSLGRSQ.PaymentLinkSharedCount, 0) AS INTEGER) AS PaymentLinkSharedCount,CAST(COALESCE(CSLTRSQ.PaymentLinkSharedClicks,0) AS INTEGER) AS PaymentLinkSharedClicks,CAST((COALESCE(UPSOSQ.PaymentLinkSharedPaid, 0) + COALESCE(SPRSQ.PaymentLinkSharedPaid, 0)) AS INTEGER) AS PaymentLinkSharedPaid,CAST(COALESCE(UPSOSQ.PaymentLinkConvertedAmount, 0.00) + COALESCE(SPRSQ.PaymentLinkConvertedAmount, 0.00) AS DECIMAL(18, 2)) AS PaymentLinkConvertedAmount FROM UserRoleStaffs URS JOIN CompanyTeamMembers CTM ON URS.Id = CTM.StaffId AND CTM.CompanyTeamId = @CTM_TeamId LEFT JOIN (SELECT CSLGR.StaffId AS StaffId, COUNT(CSLGR.Id) AS LinkSharedCount,COUNT(IIF(CSLGR.ShareLinkType IN (@CSLGR_PaymentLinkSharedCount_ShareLinkType_0,@CSLGR_PaymentLinkSharedCount_ShareLinkType_1) , CSLGR.Id, NULL)) AS PaymentLinkSharedCount FROM CoreShareLinkGenerationRecords CSLGR WHERE CSLGR.ShareLinkType != @CSLGR_ShareLinkType AND CSLGR.CreatedAt < CAST(@CSLGR_MessageTo AS DATETIME) GROUP BY CSLGR.StaffId ) CSLGRSQ ON URS.Id = CSLGRSQ.StaffId LEFT JOIN (SELECT CSLTR.StaffId AS StaffId, COUNT(CSLTR.TrackingId) AS LinkSharedClicks,COUNT(IIF(CSLTR.ShareLinkType IN (@CSLTR_PaymentLinkSharedClicks_ShareLinkType_0,@CSLTR_PaymentLinkSharedClicks_ShareLinkType_1) , CSLTR.Id, NULL)) AS PaymentLinkSharedClicks FROM CoreShareLinkTrackingRecords CSLTR WHERE CSLTR.ShareLinkType != @CSLTR_ShareLinkType AND CSLTR.TrackedAt < CAST(@CSLTR_MessageTo AS DATETIME) GROUP BY CSLTR.StaffId ) CSLTRSQ ON URS.Id = CSLTRSQ.StaffId LEFT JOIN (SELECT UPSO.StaffId AS StaffId, COUNT(UPSO.Id) AS PaymentLinkSharedPaid, COALESCE(SUM(UPSO.TotalPrice),0) AS PaymentLinkConvertedAmount FROM UserProfileShopifyOrders UPSO WHERE UPSO.Payment = @UPSO_Payment AND UPSO.ConversionStatus = @UPSO_ConversionStatus AND UPSO.CreatedAt < CAST(@UPSO_MessageTo AS DATETIME) GROUP BY UPSO.StaffId ) UPSOSQ ON URS.Id = UPSOSQ.StaffId LEFT JOIN (SELECT SPR.StaffId AS StaffId, COUNT(SPR.Id) AS PaymentLinkSharedPaid, COALESCE(SUM(SPR.AmountReceived), 0) - COALESCE(SUM(SPR.RefundedAmount), 0) AS PaymentLinkConvertedAmount FROM StripePaymentRecords SPR WHERE SPR.Status = @SPR_Status AND SPR.CreatedAt < CAST(@SPR_MessageTo AS DATETIME) GROUP BY SPR.StaffId ) SPRSQ ON URS.Id = SPRSQ.StaffId WHERE URS.CompanyId = @URS_CompanyId AND URS.Id != @URS_Id GROUP BY URS.Id, URS.[Order], CSLGRSQ.LinkSharedCount, CSLTRSQ.LinkSharedClicks, CSLGRSQ.PaymentLinkSharedCount, CSLTRSQ.PaymentLinkSharedClicks,UPSOSQ.PaymentLinkSharedPaid, UPSOSQ.PaymentLinkConvertedAmount, SPRSQ.PaymentLinkSharedPaid, SPRSQ.PaymentLinkConvertedAmount")]
    public void ConstructShopifySalesPerformancesQueryTest(object?[] parameters, string expectedQuery)
    {
        var result = InvokeMethod(
            _shopifySqlService!,
            "ConstructShopifySalesPerformancesQuery",
            parameters);

        var (_, _, query) = ((string, List<SqlParameter>, IQueryable<ShopifySalesPerformance>)) result;
        Assert.That(GetQueryString(query), Is.EqualTo(expectedQuery));
    }

    private DbContextOptions<T> GetDbContextOptions<T>(string connectionString)
        where T : DbContext
    {
        return new DbContextOptionsBuilder<T>()
            .UseSqlServer(connectionString)
            .Options;
    }

    private static string GetQueryString(IQueryable query)
    {
        return MyRegex().Replace(query.ToQueryString(), " ").Trim();
    }

    private static object InvokeMethod<T>(T instance, string methodName, object?[] parameters)
    {
        var method = typeof(T).GetMethod(
            methodName,
            BindingFlags.NonPublic | BindingFlags.Instance);

        if (method is null)
        {
            throw new NoMatchFoundException("Method not found.");
        }

        return method.Invoke(instance, ExtractDateTimeParameters(parameters))
               ?? throw new Exception("Method invocation failed.");
    }

    private static object?[] ExtractDateTimeParameters(params object?[] parameters)
    {
        return parameters.Select(
            p =>
            {
                if (p is string str && DateTime.TryParse(str, out var dateTime))
                {
                    return dateTime;
                }

                return p;
            }).ToArray();
    }
}