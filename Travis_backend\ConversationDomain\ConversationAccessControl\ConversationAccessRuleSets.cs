using System;
using System.Collections.Generic;
using Travis_backend.ConversationDomain.Extensions;
using Travis_backend.ConversationDomain.Models;
using Travis_backend.ConversationDomain.ViewModels;
using Travis_backend.Enums;

namespace Travis_backend.ConversationDomain.ConversationAccessControl;

// A class for managing the conversation access rules and determine which one to use
public interface IConversationAccessRuleSet
{
    public bool Evaluate(StaffAccessControlAggregate staff, Conversation conversation);
}

public abstract class BaseConversationAccessRuleSet : IConversationAccessRuleSet
{
    protected List<(Func<Conversation, bool> Condition, IConversationAccessRule Rule)> Rules { get; init; } = new ();

    public bool Evaluate(StaffAccessControlAggregate staff, Conversation conversation)
    {
        return staff.RoleType switch
        {
            StaffUserRole.Admin => new AdminConversationSpecification(staff).IsSatisfiedBy(conversation),
            StaffUserRole.TeamAdmin => new TeamAdminConversationSpecification(staff).IsSatisfiedBy(conversation),
            StaffUserRole.Staff => new StaffConversationSpecification(staff).IsSatisfiedBy(conversation),
            _ => false
        };
    }
}

/// <remarks>
/// This class provides a standard set of rules for determining access to conversations.
/// For custom access control requirements, create a new class that inherits from
/// BaseConversationAccessRuleSet and implement company-specific rules.
/// </remarks>
public class ConversationAccessRuleSet : BaseConversationAccessRuleSet
{
    public ConversationAccessRuleSet()
    {
        Rules = new List<(Func<Conversation, bool>, IConversationAccessRule)>
        {
            (c => c.IsAssignedToTeamOnly(), new TeamOnlyRule()),
            (c => c.IsAssignedToContactOwnerAndTeam(), new ContactOwnerAndTeamRule()),
            (c => c.IsAssignedToContactOwnerOnly(), new ContactOwnerOnlyRule()),
            (c => c.IsUnassigned(), new UnassignedRule()),
            (c => c.HasCollaborators(), new CollaboratorRule()),
            (c => c.HasMentionedStaffs(), new MentionedRule())
        };
    }
}