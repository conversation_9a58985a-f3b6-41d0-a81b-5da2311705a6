using System;
using System.Diagnostics.CodeAnalysis;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Sleekflow.Apis.FlowHub.Model;
using Travis_backend.CompanyDomain.Services;
using Travis_backend.ConversationServices;
using Travis_backend.FlowHubs.Attributes;

namespace Travis_backend.FlowHubs.Controllers;

[Route("FlowHub/Internals")]
[FlowHubAuthorization]
[SuppressMessage("ReSharper", "ConditionIsAlwaysTrueOrFalse")]
public class FlowHubInternalsServicesCallController : ControllerBase
{
    private readonly ICompanySubscriptionService _companySubscriptionService;
    private readonly ILogger<FlowHubInternalsServicesCallController> _logger;
    private readonly IEmailNotificationService _emailNotificationService;

    public FlowHubInternalsServicesCallController(
        ILogger<FlowHubInternalsServicesCallController> logger,
        IEmailNotificationService emailNotificationService,
        ICompanySubscriptionService companySubscriptionService)
    {
        _logger = logger;
        _emailNotificationService = emailNotificationService;
        _companySubscriptionService = companySubscriptionService;
    }


    [HttpPost("ServicesCall/EmailNotificationService/SendWorkflowInfiniteLoopEmail")]
    public async Task<IActionResult> SendWorkflowInfiniteLoopEmail(
        [FromBody]
        SendWorkflowInfiniteLoopEmailInput sendWorkflowInfiniteLoopEmailInput)
    {
        var companyId = sendWorkflowInfiniteLoopEmailInput.SleekflowCompanyId;
        var workflowId = sendWorkflowInfiniteLoopEmailInput.WorkflowId;
        var workflowName = sendWorkflowInfiniteLoopEmailInput.WorkflowName;
        var createdAt = sendWorkflowInfiniteLoopEmailInput.CreatedAt;

        try
        {
            await _emailNotificationService.SendWorkflowInfiniteLoopEmailAsync(
                companyId,
                workflowId,
                workflowName,
                createdAt);
        }
        catch (Exception exception)
        {
            _logger.LogError(
                exception,
                "[FlowHub Service Call {ServiceName}] [Method {MethodName}] for Workflow {WorkflowId} " +
                "error sending workflow infinite loop email to company {CompanyId} admins. {ExceptionMessage}",
                nameof(EmailNotificationService),
                nameof(SendWorkflowInfiniteLoopEmail),
                workflowId,
                companyId,
                exception);
        }

        return Ok();
    }

    [HttpPost("ServicesCall/EmailNotificationService/SendExecutionUsageReachedThresholdEmail")]
    public async Task<IActionResult> SendExecutionUsageReachedThresholdEmail(
        [FromBody] SendExecutionUsageReachedThresholdEmailInput request)
    {
        try
        {
            await _emailNotificationService.SendExecutionUsageReachedThresholdEmailAsync(request.SleekflowCompanyId, request.Threshold);
        }
        catch (Exception exception)
        {
            _logger.LogError(
                exception,
                "[FlowHub Service Call {ServiceName}] [Method {MethodName}] " +
                "error sending execution usage reached {Threshold} threshold email to company {CompanyId} admins. {ExceptionMessage}",
                nameof(EmailNotificationService),
                nameof(SendExecutionUsageReachedThresholdEmail),
                request.Threshold,
                request.SleekflowCompanyId,
                exception.Message);
        }

        return Ok();
    }
    
    [HttpPost("ServicesCall/GetCompanyUsageCycle")]
    public async Task<IActionResult> GetCompanyUsageCycle([FromBody] GetCompanyUsageCycleInput request)
    {
        var usageCycle = await _companySubscriptionService.GetMonthlyUsageCycleAsync(request.SleekflowCompanyId);

        var output = new GetCompanyUsageCycleOutput(
            new DateTimeOffset(usageCycle.From, TimeSpan.Zero),
            new DateTimeOffset(usageCycle.To, TimeSpan.Zero));

        return Ok(output);
    }
}