using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using AutoMapper;
using AutoMapper.QueryableExtensions;
using Hangfire;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Stripe.Checkout;
using Travis_backend.AccountAuthenticationDomain.Models;
using Travis_backend.AutomationDomain.Models;
using Travis_backend.CommonDomain.Services;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.CompanyDomain.Services;
using Travis_backend.Constants;
using Travis_backend.ConversationDomain.ViewModels;
using Travis_backend.ConversationServices;
using Travis_backend.CoreDomain.Models;
using Travis_backend.Database;
using Travis_backend.Enums;
using Travis_backend.Extensions;
using Travis_backend.FileDomain.Services;
using Travis_backend.Helpers;
using Travis_backend.InternalDomain.Services;
using Travis_backend.ResellerDomain.Constants;
using Travis_backend.ResellerDomain.Models;
using Travis_backend.ResellerDomain.ViewModels;
using Travis_backend.SubscriptionPlanDomain.Models;

namespace Travis_backend.ResellerDomain.Services;

public interface IResellerPortalRepository
{
    Task<ResponseWrapper> GetUserIdentityReseller(ApplicationUser userIdentity);

    Task<ResponseWrapper> GetResellerOwnProfile(string resellerProfileId);

    Task<ResponseWrapper> GetResellerStaffOwnProfile(ApplicationUser userIdentity);

    Task<ResponseWrapper> CreateClientCompany(
        ApplicationUser userIdentity,
        RegisterClientCompanyViewModel registerCompanyViewModel);

    Task<ResponseWrapper> CreateClientAccount(
        ApplicationUser resellerUser,
        RegisterClientAccountViewModel registerClientAccountViewModel);

    Task<ResponseWrapper> GetResellerClientCompanies(string resellerProfileId, GetCompaniesViewModel request);

    Task<ResponseWrapper> GetResellerCompanyById(string resellerProfileId, string clientCompanyId);

    Task<ResponseWrapper> GetAllTransactionLogs(string resellerProfileId, GetTransactionLogsRequest request);

    Task<ResponseWrapper> GetAllActivityLogs(string resellerProfileId, GetActivityLogsRequest request);

    Task<ResponseWrapper> UpdateResellerProfileLogo(ResellerCompanyProfile resellerCompanyProfile, IFormFile logoFile);

    Task<ResponseWrapper> UpdateResellerClientProfileLogo(
        ResellerClientCompanyProfile resellerClientCompanyProfile,
        IFormFile logoFile);

    Task TopUp(
        string resellerCompanyProfileId,
        string resellerTopUpLogId,
        decimal topupAmount,
        ResellerTopUpMethod topupMethod = ResellerTopUpMethod.Stripe,
        Session stripeCheckoutSession = null,
        string internalUserId = null);

    Task<ResponseWrapper> SetResellerStaffLoginClient(string clientCompanyId, ApplicationUser userIdentity);

    Task SetSubscriptionPlanMaximumUsage(Company company);

    Task<ResponseWrapper> CheckAlreadyUpDowngraded(
        Company company,
        string toSubscriptionPlanId,
        ChangePlanType changePlanType);

    Task<TransactionLogsResponse> GetAllCmsTransactionLogs(string resellerProfileId);

    Task<ResponseWrapper> GetResellerClientStaffs(string resellerProfileId, string clientCompanyId);

    Task<ResponseWrapper> RemoveResellerClientStaff(
        string resellerProfileId,
        string email,
        string clientCompanyId,
        string modifiedById);

    string GetResellerSubscriptionPlanId(
        SubscriptionTier subscriptionTier,
        ResellerCompanyProfile resellerProfile,
        string changeAddOnType = null);

    string SetTransactionAction(SubscriptionPlan subscriptionPlan);

    Task AddResellerActivityLog(ResellerActivityLog resellerActivityLog);

    Task<ResellerCompanyProfile> CheckAndUpdateResellerSubscriptionPlanConfig(
        ResellerCompanyProfile resellerCompanyProfile);
}

public class ResellerPortalRepository : IResellerPortalRepository
{
    private readonly ApplicationDbContext _appDbContext;
    private readonly IMapper _mapper;
    private readonly ILogger<ResellerPortalRepository> _logger;
    private readonly IConfiguration _configuration;
    private readonly UserManager<ApplicationUser> _userManager;
    private readonly IUploadService _uploadService;
    private readonly IAzureBlobStorageService _azureBlobStorageService;
    private readonly ICompanyUsageService _companyUsageService;
    private readonly ICountryService _countryService;
    private readonly IResellerBaseService _resellerBaseService;

    public ResellerPortalRepository(
        ApplicationDbContext appDbContext,
        IMapper mapper,
        IConfiguration configuration,
        ILogger<ResellerPortalRepository> logger,
        UserManager<ApplicationUser> userManager,
        IUploadService uploadService,
        IAzureBlobStorageService azureBlobStorageService,
        ICompanyUsageService companyUsageService,
        ICountryService countryService,
        IResellerBaseService resellerBaseService)
    {
        _appDbContext = appDbContext;
        _mapper = mapper;
        _configuration = configuration;
        _logger = logger;
        _userManager = userManager;
        _uploadService = uploadService;
        _azureBlobStorageService = azureBlobStorageService;
        _companyUsageService = companyUsageService;
        _countryService = countryService;
        _resellerBaseService = resellerBaseService;
    }

    public async Task<ResponseWrapper> GetUserIdentityReseller(ApplicationUser userIdentity)
    {
        var response = new ResponseWrapper()
        {
            IsSuccess = false
        };
        var resellerStaff =
            await _appDbContext.ResellerStaffs.FirstOrDefaultAsync(x => x.IdentityId == userIdentity.Id);

        if (resellerStaff == null)
        {
            response.ErrorMsg = "Identity does not belong to any reseller company staff";

            return response;
        }

        response.IsSuccess = true;
        response.Data = resellerStaff.ResellerCompanyProfileId;

        return response;
    }

    public async Task<ResponseWrapper> GetResellerOwnProfile(string resellerProfileId)
    {
        var response = new ResponseWrapper()
        {
            IsSuccess = false
        };
        var resellerProfile = await _appDbContext.ResellerCompanyProfiles.AsNoTracking()
            .Include(x => x.ResellerProfileLogo).Include(x => x.Company).Include(x => x.ClientCompanyProfiles)
            .FirstOrDefaultAsync(x => x.Id == resellerProfileId);

        if (resellerProfile == null)
        {
            response.ErrorMsg = "Reseller Profile Not Found";

            return response;
        }

        var resellerProfileInfo = new ResellerProfileDto()
        {
            ResellerProfileId = resellerProfileId,
            ResellerCompanyId = resellerProfile.CompanyId,
            ResellerCompanyName = resellerProfile.Company.CompanyName,
            PhoneNumber = resellerProfile.PhoneNumber,
            ResellerDiscount = resellerProfile.ResellerDiscount,
            Currency = resellerProfile.Currency,
            Balance = resellerProfile.Balance,
            TopUp = resellerProfile.TopUp,
            Debited = resellerProfile.Debited,
            ClientCompanyNumber = resellerProfile.ClientCompanyProfiles?.Count ?? 0,
            NewlyAddedClientsThisMonth = resellerProfile.ClientCompanyProfiles?.Count(
                x => DateTime.UtcNow.Month == x.CreatedAt.Month && DateTime.UtcNow.Year == x.CreatedAt.Year) ?? 0
        };

        response.Data = resellerProfileInfo;
        response.IsSuccess = true;

        return response;
    }

    public async Task<ResponseWrapper> GetResellerStaffOwnProfile(ApplicationUser userIdentity)
    {
        var response = new ResponseWrapper()
        {
            IsSuccess = false
        };
        var resellerStaff = await _appDbContext.ResellerStaffs.AsNoTracking().Include(x => x.ResellerCompanyProfile)
            .ThenInclude(x => x.Company).ThenInclude(x => x.StorageConfig).Include(x => x.ResellerCompanyProfile)
            .ThenInclude(x => x.ResellerProfileLogo).Include(x => x.ProfilePicture)
            .FirstOrDefaultAsync(x => x.IdentityId == userIdentity.Id);

        if (resellerStaff == null)
        {
            response.ErrorMsg = "Reseller Staff Not Found";

            return response;
        }

        var resellerStaffInfo = new ResellerStaffInformation
        {
            Id = resellerStaff.Id,
            IdentityId = userIdentity.Id,
            UserInfo = _mapper.Map<UserInfoResponse>(userIdentity),
            ResellerProfileId = resellerStaff.ResellerCompanyProfileId,
            ResellerProfileInformation = _mapper.Map<ResellerProfileInformation>(resellerStaff.ResellerCompanyProfile),
            ResellerCompanyId = resellerStaff.ResellerCompanyProfile.CompanyId,
            CompanyResponse = _mapper.Map<CompanyResponse>(resellerStaff.ResellerCompanyProfile.Company),
            TimeZoneInfoId = resellerStaff.TimeZoneInfoId,
            TimeZoneInfo = TimeZoneHelper.GetTimeZoneById(resellerStaff.TimeZoneInfoId),
            Locale = resellerStaff.Locale,
            Position = resellerStaff.Position,
            ProfilePictureURL = resellerStaff.ProfilePicture != null
                ? _azureBlobStorageService.GetAzureBlobSasUri(
                    resellerStaff.ProfilePicture.Filename,
                    resellerStaff.ProfilePicture.BlobContainer)
                : null,
            TeamNames = resellerStaff.TeamNames
        };

        if (resellerStaff.ResellerCompanyProfile.Company.TimeZoneInfoId != null)
        {
            resellerStaffInfo.CompanyResponse.TimeZoneInfo =
                TimeZoneHelper.GetTimeZoneById(resellerStaff.ResellerCompanyProfile.Company.TimeZoneInfoId);
        }

        if (resellerStaff.ResellerCompanyProfile.ResellerProfileLogo != null)
        {
            var logoAccessUrl = _azureBlobStorageService.GetAzureBlobSasUri(
                resellerStaff.ResellerCompanyProfile.ResellerProfileLogo.Filename,
                resellerStaff.ResellerCompanyProfile.Company.StorageConfig.ContainerName);
            resellerStaffInfo.ResellerProfileInformation.ResellerCompanyLogoLink = logoAccessUrl;
        }

        response.Data = resellerStaffInfo;
        response.IsSuccess = true;
        return response;
    }

    public async Task<ResponseWrapper> CreateClientCompany(
        ApplicationUser userIdentity,
        RegisterClientCompanyViewModel registerCompanyViewModel)
    {
        var response = new ResponseWrapper()
        {
            IsSuccess = false
        };

        var resellerStaff = await _appDbContext.ResellerStaffs
            .Include(x => x.ResellerCompanyProfile)
            .ThenInclude(x => x.Company)
            .FirstOrDefaultAsync(
                x => x.IdentityId == userIdentity.Id &&
                     x.ResellerCompanyProfile.Company.CompanyType == CompanyType.Reseller);

        if (resellerStaff == null)
        {
            response.ErrorMsg = "Identity does not belong to any Reseller Company";

            return response;
        }

        // Check if the reseller has a country tier and subscription plan config, if not then update it.
        resellerStaff.ResellerCompanyProfile =
            await CheckAndUpdateResellerSubscriptionPlanConfig(
                resellerStaff.ResellerCompanyProfile);

        // Check if the subscription plan is valid
        if (registerCompanyViewModel.SubscriptionPlanId != resellerStaff.ResellerCompanyProfile
                .ResellerSubscriptionPlanConfig[ResellerSubscriptionPlanIds.SubscriptionFreePlanId] &&
            registerCompanyViewModel.SubscriptionPlanId != resellerStaff.ResellerCompanyProfile
                .ResellerSubscriptionPlanConfig[ResellerSubscriptionPlanIds.SubscriptionProPlanId] &&
            registerCompanyViewModel.SubscriptionPlanId != resellerStaff.ResellerCompanyProfile
                .ResellerSubscriptionPlanConfig[ResellerSubscriptionPlanIds.SubscriptionPremiumPlanId])
        {
            response.ErrorMsg = "Invalid subscription Id";

            return response;
        }

        // Check if the subscription plan is exist
        var subscriptionPlan = await _appDbContext.CoreSubscriptionPlans
            .Where(x => x.Id == registerCompanyViewModel.SubscriptionPlanId).FirstOrDefaultAsync();

        if (subscriptionPlan == null)
        {
            response.ErrorMsg = "Subscription Plan Not Found";

            return response;
        }

        var fee = Convert.ToDecimal(subscriptionPlan.Amount);
        fee = Math.Round(fee * (1 - resellerStaff.ResellerCompanyProfile.ResellerDiscount), 0) *
              registerCompanyViewModel.Duration;

        if (resellerStaff.ResellerCompanyProfile.Balance - fee <
            resellerStaff.ResellerCompanyProfile.BalanceMinimumLimit)
        {
            response.ErrorMsg =
                $"Insufficient Balance to pay subscription plan id {registerCompanyViewModel.SubscriptionPlanId}, You has reached the Minimum Balance Limit. " +
                $"Balance: {resellerStaff.ResellerCompanyProfile.Balance:0.00}, " +
                $"Minimum Balance Limit: {resellerStaff.ResellerCompanyProfile.BalanceMinimumLimit:0.00}.";

            return response;
        }

        var periodEnd = DateTime.UtcNow.AddMonths(registerCompanyViewModel.Duration);

        var billRecord = new BillRecord
        {
            SubscriptionPlanId = subscriptionPlan.Id,
            PayAmount = Convert.ToDouble(fee),
            Status = BillStatus.Active,
            PaymentStatus = PaymentStatus.Paid,
            PeriodStart = DateTime.UtcNow,
            PeriodEnd = periodEnd,
            PaidByReseller = true,
            quantity = 1
        };

        var company = new Company
        {
            CompanyName = registerCompanyViewModel.CompanyName,
            BillRecords = new List<BillRecord>
            {
                billRecord
            },
            IsFreeTrial = false,
            TimeZoneInfoId = "GMT Standard Time",
            CompanyType = registerCompanyViewModel.CompanyType
        };
        company.StorageConfig = new StorageConfig()
        {
            ContainerName = company.Id
        };

        var customFields = new List<CompanyCustomField>()
        {
            new CompanyCustomField
            {
                Category = "CompanyInfo",
                FieldName = "CompanySize",
                Type = FieldDataType.SingleLineText,
                CompanyCustomFieldFieldLinguals = new List<CompanyCustomFieldFieldLingual>
                {
                    new CompanyCustomFieldFieldLingual
                    {
                        DisplayName = "公司人數", Language = "zh-HK"
                    },
                    new CompanyCustomFieldFieldLingual
                    {
                        DisplayName = "Company Size", Language = "en"
                    }
                },
                Value = registerCompanyViewModel.CompanySize
            },
            new CompanyCustomField
            {
                Category = "CompanyInfo",
                FieldName = "PhoneNumber",
                Type = FieldDataType.SingleLineText,
                CompanyCustomFieldFieldLinguals = new List<CompanyCustomFieldFieldLingual>
                {
                    new CompanyCustomFieldFieldLingual
                    {
                        DisplayName = "公司電話", Language = "zh-HK"
                    },
                    new CompanyCustomFieldFieldLingual
                    {
                        DisplayName = "Company Phone", Language = "en"
                    }
                },
                Value = registerCompanyViewModel.PhoneNumber
            },
            new CompanyCustomField
            {
                Category = "CompanyInfo",
                FieldName = "CompanyWebsite",
                Type = FieldDataType.SingleLineText,
                CompanyCustomFieldFieldLinguals = new List<CompanyCustomFieldFieldLingual>
                {
                    new CompanyCustomFieldFieldLingual
                    {
                        DisplayName = "公司網頁", Language = "zh-HK"
                    },
                    new CompanyCustomFieldFieldLingual
                    {
                        DisplayName = "Company Website", Language = "en"
                    }
                },
                Value = registerCompanyViewModel.CompanyWebsite
            }
        };

        company.CompanyCustomFields = customFields;

        company.SignalRGroupName = company.Id;

        company = AddDefaultUserFiels(company);
        company = AddDefaultQuickReply(company);
        company = AddDefaultLabel(company);
        company = AddDefaultCompanyList(company);

        try
        {
            var phoneNumberUtil = PhoneNumbers.PhoneNumberUtil.GetInstance();
            var phonenumber = phoneNumberUtil.Parse($"+{userIdentity.PhoneNumber}", null);
            var countCode = PhoneNumberHelper.GetCountryCode(phonenumber);
            string countryName = _countryService.GetCountryEnglishNameByCountryCode(countCode);

            company.CompanyCountry = countryName;
        }
        catch (Exception ex)
        {
            _logger.LogInformation("country ignore" + ex.Message);
        }

        _appDbContext.CompanyCompanies.Add(company);
        await _appDbContext.SaveChangesAsync();

        await SetSubscriptionPlanMaximumUsage(company);

        var resellerClientCompanyProfile = new ResellerClientCompanyProfile()
        {
            ResellerCompanyProfile = resellerStaff.ResellerCompanyProfile,
            ClientCompanyId = company.Id,
            ContactOwnerIdentityId = userIdentity.Id
        };
        await _appDbContext.ResellerClientCompanyProfiles.AddAsync(resellerClientCompanyProfile);
        await _appDbContext.SaveChangesAsync();

        var responseWrapper = await _resellerBaseService.AddResellerStaffsToClientCompanies(
            resellerStaff.ResellerCompanyProfile,
            clientCompany: company);

        if (responseWrapper.IsSuccess)
        {
            var resellerClientStaves = (List<Staff>) responseWrapper.Data;
            var resellerClientStaff = resellerClientStaves.First(x => x.IdentityId == userIdentity.Id);

            var assignmentRule = new AssignmentRule
            {
                AssignmentRuleName = "Default",
                AssignmentType = AssignmentType.SpecificPerson,
                AssignedStaff = resellerClientStaff,
                CompanyId = company.Id
            };
            await _appDbContext.CompanyAssignmentRules.AddAsync(assignmentRule);
        }

        await _appDbContext.SaveChangesAsync();

        resellerStaff.ResellerCompanyProfile.Debited += fee;

        var transactionLog = new ResellerTransactionLog()
        {
            ResellerCompanyProfileId = resellerStaff.ResellerCompanyProfileId,
            Amount = fee,
            Currency = resellerStaff.ResellerCompanyProfile.Currency,
            BillRecordId = billRecord.Id,
            ClientCompanyId = company.Id,
            UserIdentityId = userIdentity.Id,
            TransactionMode = TransactionMode.Debit,
            TransactionCategory = ResellerTransactionCategory.Subscription,
            TransactionAction = "Switch " + SetTransactionAction(subscriptionPlan)
        };

        await _appDbContext.ResellerTransactionLogs.AddAsync(transactionLog);
        await _appDbContext.SaveChangesAsync();

        var clientUserId = string.Empty;

        if (registerCompanyViewModel.RegisterClientCompanyAccountViewModel != null)
        {
            var registerClientAccountViewModel = new RegisterClientAccountViewModel
            {
                ClientCompanyId = company.Id,
                DisplayName = registerCompanyViewModel.RegisterClientCompanyAccountViewModel.DisplayName,
                Email = registerCompanyViewModel.RegisterClientCompanyAccountViewModel.Email,
                FirstName = registerCompanyViewModel.RegisterClientCompanyAccountViewModel.FirstName,
                LastName = registerCompanyViewModel.RegisterClientCompanyAccountViewModel.LastName,
                PhoneNumber = registerCompanyViewModel.RegisterClientCompanyAccountViewModel.PhoneNumber,
                RoleType = StaffUserRole.Admin,
                Username = registerCompanyViewModel.RegisterClientCompanyAccountViewModel.Username,
                Password = registerCompanyViewModel.RegisterClientCompanyAccountViewModel.Password
            };

            var resellerStaffResponse =
                await CreateClientAccount(
                    userIdentity,
                    registerClientAccountViewModel);

            if (!resellerStaffResponse.IsSuccess)
            {
                _logger.LogWarning(
                    "Reseller Client Company {ResellerClientCompanyId} Staff {ResellerClientUsername} creation error: {ErrorMessage}",
                    company.Id,
                    registerCompanyViewModel.RegisterClientCompanyAccountViewModel.Username,
                    resellerStaffResponse.ErrorMsg);
            }

            clientUserId = resellerStaffResponse.Data.ToString();
        }

        // HubSpot
        BackgroundJob.Schedule<IInternalHubSpotService>(
            x => x.CreateNewResellerSignupCompanyAndStaff(
                company.Id),
            TimeSpan.FromMinutes(2));

        // Sync PartnerStack Customer Key From HubSpot Contact
        BackgroundJob.Schedule<IInternalPartnerStackService>(
            x => x.SyncPartnerStackCustomerKeyFromHubSpotContact(
                company.Id,
                registerCompanyViewModel.RegisterClientCompanyAccountViewModel.Email),
            TimeSpan.FromMinutes(4));

        response.IsSuccess = true;

        response.Data = new ResellerClientInformation()
        {
            ResellerClientId = resellerClientCompanyProfile.Id,
            ResellerProfileId = resellerStaff.ResellerCompanyProfileId,
            ClientCompanyId = company.Id,
            ClientCompanyName = company.CompanyName,
            ClientUserId = clientUserId,
            SubscriptionPlans = _mapper.Map<List<SubscriptionPlanDto>>(
                new List<SubscriptionPlan>()
                {
                    await _appDbContext.CoreSubscriptionPlans.FirstOrDefaultAsync(
                        x => x.Id == billRecord.SubscriptionPlanId)
                })
        };

        return response;
    }

    public async Task<ResponseWrapper> CreateClientAccount(
        ApplicationUser resellerUser,
        RegisterClientAccountViewModel registerClientAccountViewModel)
    {
        var response = new ResponseWrapper()
        {
            IsSuccess = false
        };

        var resellerCompany = await _appDbContext.ResellerStaffs.Include(x => x.ResellerCompanyProfile)
            .ThenInclude(x => x.Company).Where(x => x.IdentityId == resellerUser.Id)
            .Select(x => x.ResellerCompanyProfile.Company).FirstOrDefaultAsync();

        var clientCompanyProfile = await _appDbContext.ResellerClientCompanyProfiles
            .Include(x => x.ClientCompany)
            .FirstOrDefaultAsync(
                x => x.ClientCompanyId == registerClientAccountViewModel.ClientCompanyId
                     && resellerCompany.Id == x.ResellerCompanyProfile.CompanyId);

        if (clientCompanyProfile == null)
        {
            response.ErrorMsg = "Client Company Profile not found";

            return response;
        }

        var existingUser = await _userManager.FindByEmailAsync(registerClientAccountViewModel.Email);
        if (existingUser != null)
        {
            response.ErrorMsg = "user email already taken";

            return response;
        }

        existingUser = await _userManager.FindByNameAsync(registerClientAccountViewModel.Username);
        if (existingUser != null)
        {
            response.ErrorMsg = "username already taken";

            return response;
        }

        if (!_resellerBaseService.ValidatePassword(registerClientAccountViewModel.Password))
        {
            response.ErrorMsg =
                "Password must be at least 8 characters long, contain at least one uppercase letter, one lowercase letter, one number and one special character";

            return response;
        }

        // Check the maximum agents limit is reached or not
        var resellerCompanyProfileId = await _appDbContext.ResellerStaffs
            .AsNoTracking()
            .Where(x => x.IdentityId == resellerUser.Id)
            .Select(x => x.ResellerCompanyProfileId)
            .FirstOrDefaultAsync();

        var totalAgents =
            await _appDbContext.UserRoleStaffs.CountAsync(
                staff => staff.CompanyId == clientCompanyProfile.ClientCompanyId && !_appDbContext.ResellerStaffs
                    .Where(
                        rs => rs.ResellerCompanyProfileId ==
                              resellerCompanyProfileId)
                    .Select(rs => rs.IdentityId).ToList().Contains(staff.IdentityId));

        var companyUsage = await _companyUsageService.GetCompanyUsage(registerClientAccountViewModel.ClientCompanyId);

        if (totalAgents >= companyUsage.MaximumAgents)
        {
            response.ErrorMsg = "Maximum agents reached";
            return response;
        }

        var userIdentity = _mapper.Map<ApplicationUser>(registerClientAccountViewModel);

        if (string.IsNullOrEmpty(userIdentity.UserName))
        {
            userIdentity.UserName = registerClientAccountViewModel.Email;
        }

        userIdentity.UserRole = "staff";
        userIdentity.EmailConfirmed = true;

        var identityResult = await _userManager.CreateAsync(userIdentity, registerClientAccountViewModel.Password);

        if (identityResult.Succeeded)
        {
            await _appDbContext.UserRoleStaffs.AddAsync(
                new Staff
                {
                    IdentityId = userIdentity.Id,
                    Company = clientCompanyProfile.ClientCompany,
                    Locale = "zh-hk",
                    NotificationSettingId = 1,
                    RoleType = registerClientAccountViewModel.RoleType
                });

            await _appDbContext.SaveChangesAsync();

            await AddResellerActivityLog(
                new ResellerActivityLog
                {
                    ResellerCompanyProfileId = resellerCompanyProfileId,
                    CompanyId = registerClientAccountViewModel.ClientCompanyId,
                    CreatedByUserId = resellerUser.Id,
                    Category = ResellerActivityLogCategory.User,
                    Action = $"Create New User - {registerClientAccountViewModel.Username}"
                });

            // HubSpot
            BackgroundJob.Enqueue<IInternalHubSpotService>(
                x => x.SyncCompanyStaffs(registerClientAccountViewModel.ClientCompanyId));

            response.IsSuccess = true;
            response.Data = userIdentity.Id;

            return response;
        }

        _logger.LogError(
            "Client Account username {Username}, email {Email} cannot be created with error : {CreateUserErrors}",
            registerClientAccountViewModel.Username,
            registerClientAccountViewModel.Email,
            JsonConvert.SerializeObject(identityResult.Errors));

        if (identityResult.Errors.Any(
                x => x.Code == _userManager.ErrorDescriber.DuplicateUserName(userIdentity.UserName).Code))
        {
            response.ErrorMsg = "username already taken";

            return response;
        }
        else if (identityResult.Errors.Any(
                     x => x.Code == _userManager.ErrorDescriber.DuplicateEmail(userIdentity.Email).Code))
        {
            response.ErrorMsg = "user email already taken";

            return response;
        }

        response.ErrorMsg = $"Client Account cannot be created with error : {identityResult.Errors}";

        return response;
    }

    public async Task<ResponseWrapper> GetAllTransactionLogs(
        string resellerProfileId,
        GetTransactionLogsRequest request)
    {
        var response = new ResponseWrapper()
        {
            IsSuccess = false
        };

        var resellerProfile = await _appDbContext.ResellerCompanyProfiles
            .Include(x => x.ClientCompanyProfiles)
            .Include(x => x.ResellerTransactionLogs)
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.Id == resellerProfileId);

        if (resellerProfile == null)
        {
            response.ErrorMsg = "Reseller Profile Not Found";

            return response;
        }

        var clientCompanyIds = resellerProfile.ClientCompanyProfiles
            .Select(x => x.ClientCompanyId)
            .ToList();

        var clientCompanies = await _appDbContext.CompanyCompanies
            .AsNoTracking()
            .Where(x => clientCompanyIds.Contains(x.Id))
            .Select(
                x => new CompanyResponse()
                {
                    Id = x.Id, CompanyName = x.CompanyName,
                })
            .ToListAsync();

        var transactionLogDtos = new List<TransactionLogDto>();

        if (resellerProfile.ResellerTransactionLogs == null || !resellerProfile.ResellerTransactionLogs.Any())
        {
            response.Data = new TransactionLogsResponse()
            {
                TransactionLogsNumber = 0, TransactionLogs = transactionLogDtos
            };
            response.IsSuccess = true;
            return response;
        }

        var transactionLogs = resellerProfile.ResellerTransactionLogs
            .Where(x => x.Amount > 0)
            .ToList();

        var transactionLogResponse = new TransactionLogsResponse()
        {
            TransactionLogsNumber = transactionLogs.Count
        };

        transactionLogDtos = _mapper.Map<List<TransactionLogDto>>(transactionLogs);

        var masterBalance = 0.00M;

        foreach (var transactionLog in transactionLogDtos)
        {
            transactionLog.Amount = transactionLog.TransactionMode == TransactionMode.Debit.ToString()
                ? -transactionLog.Amount
                : transactionLog.Amount;
            transactionLog.ClientCompany = transactionLog.ClientCompanyId == null
                ? new CompanyResponse()
                {
                    CompanyName = "Reseller Account"
                }
                : clientCompanies.FirstOrDefault(x => x.Id == transactionLog.ClientCompanyId);
            transactionLog.Balance = masterBalance += transactionLog.Amount;
            transactionLog.TransactionType =
                Regex.Replace(transactionLog.TransactionCategory, @"([a-z])([A-Z])(?![\s\S]*([a-z])([A-Z]))", "$1 $2") +
                " - " + transactionLog.TransactionAction;
        }

        transactionLogDtos = request.Limit == -1
            ? transactionLogDtos
                .OrderByDescending(x => x.CreatedAt)
                .ToList()
            : transactionLogDtos
                .OrderByDescending(x => x.CreatedAt)
                .Skip(request.Offset)
                .Take(request.Limit)
                .ToList();

        transactionLogResponse.TransactionLogs = transactionLogDtos;
        response.IsSuccess = true;
        response.Data = transactionLogResponse;

        return response;
    }

    public async Task<TransactionLogsResponse> GetAllCmsTransactionLogs(string resellerProfileId)
    {
        var resellerProfile = await _appDbContext.ResellerCompanyProfiles
            .AsNoTracking()
            .AsSplitQuery()
            .Include(x => x.ClientCompanyProfiles)
            .FirstOrDefaultAsync(x => x.Id == resellerProfileId);

        if (resellerProfile == null)
        {
            return null;
        }

        var resellerTransactionLogs = await _appDbContext.ResellerTransactionLogs
            .AsNoTracking()
            .OrderByDescending(x => x.CreatedAt)
            .Where(x => x.ResellerCompanyProfileId == resellerProfileId)
            .ToListAsync();

        var billRecordIds = resellerTransactionLogs.Where(x => x.BillRecordId != null).Select(x => x.BillRecordId)
            .ToList();
        var billRecords = await _appDbContext.CompanyBillRecords
            .Where(x => billRecordIds.Contains(x.Id))
            .ProjectTo<BillRecordDto>(_mapper.ConfigurationProvider)
            .ToListAsync();

        var clientCompanyIds = resellerProfile.ClientCompanyProfiles.Select(x => x.ClientCompanyId).ToList();
        var clientCompanies = await _appDbContext.CompanyCompanies
            .Where(x => clientCompanyIds.Contains(x.Id))
            .Select(
                x => new CompanyResponse()
                {
                    Id = x.Id, CompanyName = x.CompanyName, CompanyType = x.CompanyType, CreatedAt = x.CreatedAt
                })
            .ToListAsync();

        var userIdentityIds = resellerTransactionLogs.Where(x => x.UserIdentityId != null).Select(x => x.UserIdentityId)
            .ToList();
        var userInfoResponses = await _appDbContext.Users.Where(x => userIdentityIds.Contains(x.Id))
            .ProjectTo<UserInfoResponse>(_mapper.ConfigurationProvider).ToListAsync();

        var transactionLogDtos = new List<TransactionLogDto>();
        foreach (var transactionLog in resellerTransactionLogs)
        {
            var transactionLogDto = _mapper.Map<TransactionLogDto>(transactionLog);

            if (transactionLog.BillRecordId != null)
            {
                transactionLogDto.BillRecordDto = billRecords.FirstOrDefault(x => x.Id == transactionLog.BillRecordId);
            }

            if (!string.IsNullOrEmpty(transactionLog.ClientCompanyId))
            {
                transactionLogDto.ClientCompany =
                    clientCompanies.FirstOrDefault(x => x.Id == transactionLog.ClientCompanyId);
            }

            if (!string.IsNullOrEmpty(transactionLog.UserIdentityId))
            {
                transactionLogDto.UserIdentity =
                    userInfoResponses.FirstOrDefault(x => x.Id == transactionLog.UserIdentityId);
            }

            transactionLogDtos.Add(transactionLogDto);
        }

        var transactionLogResponse = new TransactionLogsResponse()
        {
            TransactionLogs = transactionLogDtos, TransactionLogsNumber = resellerTransactionLogs.Count
        };

        return transactionLogResponse;
    }

    public async Task<ResponseWrapper> GetAllActivityLogs(
        string resellerProfileId,
        GetActivityLogsRequest request)
    {
        var response = new ResponseWrapper
        {
            IsSuccess = false
        };

        var resellerActivityLogs = await _appDbContext.ResellerActivityLogs
            .AsNoTracking()
            .OrderByDescending(x => x.CreatedAt)
            .Where(x => x.ResellerCompanyProfileId == resellerProfileId)
            .Include(resellerActivityLog => resellerActivityLog.Company)
            .Include(resellerActivityLog => resellerActivityLog.CreatedByUser)
            .ToListAsync();

        var resellerActivityLogDtoList = new List<ResellerActivityLogDto>();

        if (!resellerActivityLogs.Any())
        {
            response.Data = new ActivityLogsResponse
            {
                ActivityLogsNumber = 0, ActivityLogs = resellerActivityLogDtoList
            };
            response.IsSuccess = true;
            return response;
        }

        var resellerActivityLogsResponse = new ActivityLogsResponse
        {
            ActivityLogsNumber = resellerActivityLogs.Count
        };

        if (request.Limit != -1)
        {
            resellerActivityLogs = resellerActivityLogs
                .Skip(request.Offset)
                .Take(request.Limit)
                .ToList();
        }

        resellerActivityLogDtoList.AddRange(
            resellerActivityLogs.Select(
                activityLog => new ResellerActivityLogDto
                {
                    Category = activityLog.Category,
                    Action = activityLog.Action,
                    Account = activityLog.CompanyId == activityLog.CreatedByUser.CompanyId
                        ? "Master Account"
                        : activityLog.Company.CompanyName,
                    CreatedBy = activityLog.CreatedByUser.UserName,
                    CreatedAt = activityLog.CreatedAt
                }));

        resellerActivityLogsResponse.ActivityLogs = resellerActivityLogDtoList;
        response.IsSuccess = true;
        response.Data = resellerActivityLogsResponse;

        return response;
    }

    public async Task<ResponseWrapper> GetResellerClientCompanies(
        string resellerProfileId,
        GetCompaniesViewModel request)
    {
        var response = new ResponseWrapper()
        {
            IsSuccess = false
        };

        var getCompanyDto = new GetResellerClientCompanyDto()
        {
            ClientCompanies = new List<ResellerClientCompanyUsageDetails>()
        };
        getCompanyDto.ClientCompanyNumber = 0;

        var resellerProfile = await _appDbContext.ResellerCompanyProfiles
            .Include(x => x.ClientCompanyProfiles)
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.Id == resellerProfileId);

        if (resellerProfile == null)
        {
            response.ErrorMsg = "Reseller Profile not found";
            response.Data = getCompanyDto;
            return response;
        }

        if (resellerProfile.ClientCompanyProfiles.Count == 0)
        {
            response.Data = getCompanyDto;
            response.IsSuccess = true;
            return response;
        }

        var clientCompanyIds = resellerProfile.ClientCompanyProfiles != null
            ? resellerProfile.ClientCompanyProfiles.Select(x => x.ClientCompanyId).ToList()
            : new List<string>();

        var resellerStaffs = _appDbContext.ResellerStaffs
            .Where(x => x.ResellerCompanyProfileId == resellerProfileId)
            .Include(x => x.ProfilePicture)
            .AsNoTracking();

        var usageList = await _appDbContext.CompanyCompanies
            .AsSplitQuery()
            .Include(x => x.BillRecords)
            .Include(x => x.Staffs)
            .ThenInclude(x => x.Identity)
            .Include(x => x.UserProfiles)
            .WhereIf(
                clientCompanyIds.Count != 0,
                company => clientCompanyIds.Contains(company.Id))
            .OrderBy(x => x.CreatedAt)
            .AsNoTracking()
            .Select(
                x => new ResellerClientCompanyUsageDetails
                {
                    ClientCompanyId = x.Id,
                    ClientCompanyName = x.CompanyName,
                    CreatedAt = x.CreatedAt,
                    CurrentSubscriptionPlan = x.BillRecords
                        .OrderByDescending(billRecord => billRecord.created)
                        .ThenByDescending(billRecord => billRecord.PayAmount)
                        .Where(
                            billRecord => ValidSubscriptionPlan.SubscriptionPlan.Contains(
                                              billRecord.SubscriptionPlanId) &&
                                          billRecord.Status != BillStatus.Inactive &&
                                          billRecord.Status != BillStatus.Terminated &&
                                          billRecord.PeriodStart <= DateTime.UtcNow &&
                                          billRecord.PeriodEnd > DateTime.UtcNow)
                        .Select(billRecord => _mapper.Map<SubscriptionPlanDto>(billRecord.SubscriptionPlan))
                        .FirstOrDefault(),
                    TotalAgents =
                        x.Staffs.Count(
                            staff => !resellerStaffs.Select(rs => rs.IdentityId).Contains(staff.IdentityId)),
                    TotalContacts =
                        x.UserProfiles.Count(
                            userProfile => userProfile.ActiveStatus == ActiveStatus.Active),
                    TotalWhatsappInstance = x.WhatsappCloudApiConfigs.Count,
                    ContactOwner = x.Staffs
                        .Where(
                            staff => staff.RoleType == StaffUserRole.Admin &&
                                     !resellerStaffs.Select(rs => rs.IdentityId).Contains(staff.IdentityId))
                        .Select(
                            staff => new ResellerClientStaffInformation()
                            {
                                Username = staff.Identity.UserName,
                                Email = staff.Identity.Email,
                                PhoneNumber = staff.Identity.PhoneNumber,
                                FirstName = staff.Identity.FirstName,
                                LastName = staff.Identity.LastName,
                                DisplayName = staff.Identity.DisplayName,
                                RoleType = staff.RoleType,
                                CreatedAt = staff.Identity.CreatedAt
                            })
                        .FirstOrDefault(),
                    Assignee = resellerStaffs
                        .Where(
                            rs =>
                                rs.IdentityId == x.ResellerClientCompanyProfile.AssigneeIdentityId)
                        .Select(
                            rs => new ResellerStaffInformation()
                            {
                                Id = rs.Id,
                                IdentityId = rs.IdentityId,
                                UserInfo =
                                    _mapper.Map<UserInfoResponse>(
                                        _appDbContext.Users.FirstOrDefault(
                                            user => user.Id == rs.IdentityId)),
                                ProfilePictureURL = rs.ProfilePicture != null
                                    ? _azureBlobStorageService.GetAzureBlobSasUri(
                                        rs.ProfilePicture.Filename,
                                        rs.ProfilePicture.BlobContainer,
                                        1)
                                    : null
                            })
                        .FirstOrDefault(),
                    CompanyStatus = x.IsDeleted ? "Deleted" : "Active",
                    MonthlyRecurringRevenue = BillRecordRevenueCalculator.SumMonthlyRecurringRevenue(
                        x.BillRecords.Where(b => b.PeriodStart <= DateTime.UtcNow && b.Status != BillStatus.Inactive)
                            .OrderByDescending(br => br.created)
                            .ToList(),
                        default)
                })
            .ToListAsync();

        // Renew the subscription plan if it is expired and the company is not deleted
        foreach (var companyUsageDetail in usageList)
        {
            if (companyUsageDetail.CurrentSubscriptionPlan != null ||
                companyUsageDetail.CompanyStatus == "Deleted")
            {
                continue;
            }

            await _companyUsageService.GetBillingPeriodUsages(companyUsageDetail.ClientCompanyId);

            var billRecords = _appDbContext.CompanyBillRecords
                .OrderByDescending(billRecord => billRecord.created)
                .ThenByDescending(billRecord => billRecord.PayAmount)
                .Where(
                    billRecord => billRecord.CompanyId == companyUsageDetail.ClientCompanyId)
                .AsNoTracking();

            companyUsageDetail.CurrentSubscriptionPlan =
                await billRecords
                    .Where(
                        billRecord => ValidSubscriptionPlan.SubscriptionPlan.Contains(
                                          billRecord.SubscriptionPlanId) &&
                                      billRecord.Status != BillStatus.Inactive &&
                                      billRecord.Status != BillStatus.Terminated &&
                                      billRecord.PeriodStart <= DateTime.UtcNow &&
                                      billRecord.PeriodEnd > DateTime.UtcNow)
                    .Select(billRecord => _mapper.Map<SubscriptionPlanDto>(billRecord.SubscriptionPlan))
                    .FirstOrDefaultAsync();

            companyUsageDetail.MonthlyRecurringRevenue = BillRecordRevenueCalculator.SumMonthlyRecurringRevenue(
                await billRecords
                    .Where(
                        br => br.PeriodStart <= DateTime.UtcNow &&
                             br.Status != BillStatus.Inactive)
                    .OrderByDescending(br => br.created)
                    .ToListAsync());
        }

        getCompanyDto.ClientCompanyNumber = usageList.Count;

        usageList = request.Limit == -1
            ? usageList
            : usageList.Skip(request.Offset).Take(request.Limit).ToList();

        getCompanyDto.ClientCompanies = usageList;

        response.Data = getCompanyDto;
        response.IsSuccess = true;

        return response;
    }

    public async Task<ResponseWrapper> GetResellerCompanyById(string resellerProfileId, string clientCompanyId)
    {
        var response = new ResponseWrapper()
        {
            IsSuccess = false
        };

        var clientCompanyProfile = await _appDbContext.ResellerClientCompanyProfiles
            .Include(resellerClientCompanyProfile => resellerClientCompanyProfile.ResellerCompanyProfile)
            .AsNoTracking()
            .FirstOrDefaultAsync(
                x => x.ClientCompanyId == clientCompanyId && x.ResellerCompanyProfileId == resellerProfileId);

        if (clientCompanyProfile == null)
        {
            response.ErrorMsg = "Client Company Not Found";
            return response;
        }

        var companyUsage = await _companyUsageService.GetCompanyUsage(clientCompanyId);

        var resellerStaffs = _appDbContext.ResellerStaffs
            .Where(x => x.ResellerCompanyProfileId == resellerProfileId)
            .Include(x => x.ProfilePicture)
            .AsNoTracking();

        var usage = await _appDbContext.CompanyCompanies
            .Where(x => x.Id == clientCompanyId && x.CompanyType == CompanyType.ResellerClient)
            .AsSplitQuery()
            .Include(x => x.Staffs)
            .ThenInclude(x => x.Identity)
            .Include(x => x.UserProfiles)
            .Include(x => x.BillRecords)
            .ThenInclude(x => x.SubscriptionPlan)
            .Include(x => x.ResellerClientCompanyProfile)
            .Include(x => x.CompanyIconFile)
            .AsNoTracking()
            .Select(
                x => new ResellerClientCompanyUsageDetails()
                {
                    ClientCompanyId = x.Id,
                    ClientCompanyName = x.CompanyName,
                    ClientProfileId = x.ResellerClientCompanyProfile.Id,
                    ClientCompanyLogoLink =
                        x.CompanyIconFile != null
                            ? _azureBlobStorageService.GetAzureBlobSasUri(
                                x.CompanyIconFile.Filename,
                                x.CompanyIconFile.BlobContainer,
                                24)
                            : null,
                    CreatedAt = x.CreatedAt,
                    CurrentSubscriptionPlan =
                        x.BillRecords
                            .OrderByDescending(billRecord => billRecord.created)
                            .ThenByDescending(billRecord => billRecord.PayAmount)
                            .Where(
                                billRecord => ValidSubscriptionPlan.SubscriptionPlan.Contains(
                                                  billRecord.SubscriptionPlanId) &&
                                              billRecord.Status != BillStatus.Inactive &&
                                              billRecord.Status != BillStatus.Terminated &&
                                              billRecord.PeriodStart <= DateTime.UtcNow &&
                                              billRecord.PeriodEnd > DateTime.UtcNow)
                            .Select(billRecord => _mapper.Map<SubscriptionPlanDto>(billRecord.SubscriptionPlan))
                            .FirstOrDefault(),
                    BillRecords = _mapper.Map<List<BillRecordDto>>(x.BillRecords),
                    TotalAgents =
                        x.Staffs.Count(
                            staff => !resellerStaffs.Select(rs => rs.IdentityId).Contains(staff.IdentityId)),
                    TotalContacts =
                        x.UserProfiles.Count(
                            userProfile => userProfile.ActiveStatus == ActiveStatus.Active),
                    TotalWhatsappInstance = x.WhatsappCloudApiConfigs.Count,
                    WhatsAppConfigCount = x.WhatsAppConfigs.Count,
                    WhatsApp360DialogConfigCount = x.WhatsApp360DialogConfigs.Count,
                    InstagramConfigCount = x.InstagramConfigs.Count,
                    FacebookConfigCount = x.FacebookConfigs.Count,
                    WebClientSenderCount = x.WebClientSenders.Any() ? 1 : 0,
                    LineConfigCount = x.LineConfigs.Count,
                    SMSConfigCount = x.SMSConfigs.Count,
                    TelegramConfigCount = x.TelegramConfigs.Count,
                    ViberConfigCount = x.ViberConfigs.Count,
                    WeChatConfigCount = x.WeChatConfig != null ? 1 : 0,
                    EmailConfigCount = x.EmailConfig != null ? 1 : 0,
                    ShoplineConfigCount = x.ShoplineConfigs.Count,
                    ShopifyConfigCount = x.ShopifyConfigs.Count,
                    MaximumContacts = companyUsage.MaximumContacts,
                    MaximumAgents = companyUsage.MaximumAgents,
                    MaximumWhatsappInstance = companyUsage.MaximumNumberOfWhatsappCloudApiChannels,
                    ContactOwner = x.Staffs
                        .Where(
                            staff => staff.RoleType == StaffUserRole.Admin &&
                                     !resellerStaffs.Select(rs => rs.IdentityId).Contains(staff.IdentityId))
                        .Select(
                            staff => new ResellerClientStaffInformation()
                            {
                                Username = staff.Identity.UserName,
                                Email = staff.Identity.Email,
                                PhoneNumber = staff.Identity.PhoneNumber,
                                FirstName = staff.Identity.FirstName,
                                LastName = staff.Identity.LastName,
                                DisplayName = staff.Identity.DisplayName,
                                RoleType = staff.RoleType,
                                CreatedAt = staff.Identity.CreatedAt
                            })
                        .FirstOrDefault(),
                    Assignee = resellerStaffs
                        .Where(
                            resellerStaff =>
                                resellerStaff.IdentityId == x.ResellerClientCompanyProfile.AssigneeIdentityId).Select(
                            resellerStaff => new ResellerStaffInformation()
                            {
                                Id = resellerStaff.Id,
                                IdentityId = resellerStaff.IdentityId,
                                UserInfo =
                                    _mapper.Map<UserInfoResponse>(
                                        _appDbContext.Users.FirstOrDefault(
                                            user => user.Id == resellerStaff.IdentityId)),
                                ProfilePictureURL = resellerStaff.ProfilePicture != null
                                    ? _azureBlobStorageService.GetAzureBlobSasUri(
                                        resellerStaff.ProfilePicture.Filename,
                                        resellerStaff.ProfilePicture.BlobContainer,
                                        1)
                                    : null
                            })
                        .FirstOrDefault(),
                    CompanyStatus = x.IsDeleted ? "Deleted" : "Active",
                    MonthlyRecurringRevenue = BillRecordRevenueCalculator.SumMonthlyRecurringRevenue(
                        x.BillRecords.Where(b => b.PeriodStart <= DateTime.UtcNow && b.Status != BillStatus.Inactive)
                            .OrderByDescending(br => br.created)
                            .ToList(),
                        default)
                })
            .FirstOrDefaultAsync();

        if (usage == null)
        {
            response.ErrorMsg = "Client Company Usage Detail Not Found";

            return response;
        }

        response.Data = usage;
        response.IsSuccess = true;
        return response;
    }

    public async Task<ResponseWrapper> UpdateResellerProfileLogo(
        ResellerCompanyProfile resellerCompanyProfile,
        IFormFile logoFile)
    {
        var response = new ResponseWrapper()
        {
            IsSuccess = false
        };
        var storageConfig =
            await _appDbContext.ConfigStorageConfigs.FirstOrDefaultAsync(
                x => x.CompanyId == resellerCompanyProfile.CompanyId);

        if (storageConfig == null)
        {
            response.ErrorMsg = "Reseller Company Storage Config Not Found";
            return response;
        }

        var filePath =
            $"ResellerProfileLogo/{resellerCompanyProfile.Id}/{DateTime.UtcNow.ToString("o")}/{logoFile.FileName}";
        var uploadFileResult = await _uploadService.UploadImage(storageConfig.ContainerName, filePath, logoFile);

        if (uploadFileResult?.Url == null)
        {
            response.ErrorMsg = "Upload Reseller Logo File Error";
            return response;
        }

        if (resellerCompanyProfile.ResellerProfileLogo != null)
        {
            resellerCompanyProfile.ResellerProfileLogo.Filename = filePath;
            resellerCompanyProfile.ResellerProfileLogo.BlobContainer = storageConfig.ContainerName;
            resellerCompanyProfile.ResellerProfileLogo.Url = uploadFileResult.Url;
            resellerCompanyProfile.ResellerProfileLogo.MIMEType = logoFile.ContentType;
            resellerCompanyProfile.ResellerProfileLogo.UpdatedAt = DateTime.UtcNow;
            await _appDbContext.SaveChangesAsync();
        }
        else
        {
            var resellerProfileLogo = new ResellerProfileLogo()
            {
                ResellerCompanyProfileId = resellerCompanyProfile.Id,
                Filename = filePath,
                BlobContainer = storageConfig.ContainerName,
                Url = uploadFileResult.Url,
                MIMEType = logoFile.ContentType
            };

            resellerCompanyProfile.ResellerProfileLogo = resellerProfileLogo;

            await _appDbContext.ResellerProfileLogos.AddAsync(resellerProfileLogo);
            await _appDbContext.SaveChangesAsync();
        }

        var accessUrl = _azureBlobStorageService.GetAzureBlobSasUri(filePath, storageConfig.ContainerName, 30 * 24);
        resellerCompanyProfile.UpdatedAt = DateTime.UtcNow;
        await _appDbContext.SaveChangesAsync();

        response.IsSuccess = true;
        response.Data = accessUrl;

        return response;
    }

    public async Task<ResponseWrapper> UpdateResellerClientProfileLogo(
        ResellerClientCompanyProfile resellerClientCompanyProfile,
        IFormFile logoFile)
    {
        var response = new ResponseWrapper()
        {
            IsSuccess = false
        };
        var storageConfig =
            await _appDbContext.ConfigStorageConfigs.FirstOrDefaultAsync(
                x => x.CompanyId == resellerClientCompanyProfile.ClientCompanyId);

        if (storageConfig == null)
        {
            response.ErrorMsg = "Reseller Company Storage Config Not Found";
            return response;
        }

        var filePath =
            $"ResellerClientProfileLogo/{resellerClientCompanyProfile.ClientCompanyId}/{DateTime.UtcNow.ToString("o")}/{logoFile.FileName}";
        var uploadFileResult = await _uploadService.UploadImage(storageConfig.ContainerName, filePath, logoFile);

        if (uploadFileResult?.Url == null)
        {
            response.ErrorMsg = "Upload Reseller Logo File Error";
            return response;
        }

        if (resellerClientCompanyProfile.ClientCompany.CompanyIconFile != null)
        {
            resellerClientCompanyProfile.ClientCompany.CompanyIconFile.Filename = filePath;
            resellerClientCompanyProfile.ClientCompany.CompanyIconFile.BlobContainer = storageConfig.ContainerName;
            resellerClientCompanyProfile.ClientCompany.CompanyIconFile.Url = uploadFileResult.Url;
            resellerClientCompanyProfile.ClientCompany.CompanyIconFile.MIMEType = logoFile.ContentType;
            await _appDbContext.SaveChangesAsync();
        }
        else
        {
            var resellerClientCompanyLogo = new CompanyIconFile()
            {
                CompanyId = resellerClientCompanyProfile.ClientCompanyId,
                Filename = filePath,
                BlobContainer = storageConfig.ContainerName,
                Url = uploadFileResult.Url,
                MIMEType = logoFile.ContentType
            };

            await _appDbContext.CompanyIconFiles.AddAsync(resellerClientCompanyLogo);
            await _appDbContext.SaveChangesAsync();
        }

        var accessUrl = _azureBlobStorageService.GetAzureBlobSasUri(filePath, storageConfig.ContainerName, 30 * 24);
        resellerClientCompanyProfile.UpdatedAt = DateTime.UtcNow;
        await _appDbContext.SaveChangesAsync();

        response.IsSuccess = true;
        response.Data = accessUrl;

        return response;
    }

    public async Task TopUp(
        string resellerCompanyProfileId,
        string resellerTopUpLogId,
        decimal topUpAmount,
        ResellerTopUpMethod topupMethod = ResellerTopUpMethod.Stripe,
        Session stripeCheckoutSession = null,
        string internalUserId = null)
    {
        long logId;

        var isValid = long.TryParse(resellerTopUpLogId, out logId);

        if (!isValid)
        {
            return;
        }

        var transactionLog = await _appDbContext.ResellerTransactionLogs.Include(x => x.ResellerCompanyProfile)
            .FirstOrDefaultAsync(
                x => x.Id == logId && x.ResellerCompanyProfileId == resellerCompanyProfileId &&
                     x.TopUpMethod == ResellerTopUpMethod.Stripe);
        if (transactionLog == null)
        {
            return;
        }

        if (transactionLog.TopupStatus == TopupStatus.Redeemed ||
            transactionLog.TopupStatus == TopupStatus.Cancelled)
        {
            return;
        }

        transactionLog.Amount = topUpAmount;
        transactionLog.InvoiceId = stripeCheckoutSession?.Id;
        transactionLog.CustomerEmail = stripeCheckoutSession?.CustomerEmail;
        transactionLog.CustomerId = stripeCheckoutSession?.CustomerId;
        transactionLog.TopupStatus = TopupStatus.Redeemed;
        transactionLog.UpdatedAt = DateTime.UtcNow;
        transactionLog.ResellerCompanyProfile.TopUp += topUpAmount;
        transactionLog.ResellerCompanyProfile.UpdatedAt = DateTime.UtcNow;
        await _appDbContext.SaveChangesAsync();

        await AddResellerActivityLog(
            new ResellerActivityLog
            {
                ResellerCompanyProfileId = transactionLog.ResellerCompanyProfileId,
                CompanyId = stripeCheckoutSession?.Metadata["resellerCompanyId"],
                CreatedByUserId = stripeCheckoutSession?.Metadata["resellerUserId"],
                Category = ResellerActivityLogCategory.Balance,
                Action = $"Top Up Adjustment: {topUpAmount}"
            });
    }

    public async Task<ResponseWrapper> SetResellerStaffLoginClient(string clientCompanyId, ApplicationUser userIdentity)
    {
        var response = new ResponseWrapper()
        {
            IsSuccess = false
        };
        try
        {
            var anyStaffInClient = await _appDbContext.UserRoleStaffs.AnyAsync(
                x => x.CompanyId == clientCompanyId && x.IdentityId == userIdentity.Id);

            if (!anyStaffInClient)
            {
                var resellerStaff = await _appDbContext.ResellerStaffs.Include(x => x.ProfilePicture)
                    .FirstOrDefaultAsync(x => x.IdentityId == userIdentity.Id);

                var staffToAdd = new Staff()
                {
                    IdentityId = userIdentity.Id,
                    CompanyId = clientCompanyId,
                    Role = "Reseller Staff",
                    RoleType = StaffUserRole.Admin,
                    Locale = resellerStaff?.Locale,
                    TimeZoneInfoId = resellerStaff?.TimeZoneInfoId,
                    Position = resellerStaff?.Position,
                    ProfilePicture = resellerStaff?.ProfilePicture,
                    Status = StaffStatus.Active
                };

                await _appDbContext.UserRoleStaffs.AddAsync(staffToAdd);
                await _appDbContext.SaveChangesAsync();
            }

            userIdentity.CompanyId = clientCompanyId;
            await _appDbContext.SaveChangesAsync();

            response.IsSuccess = true;
            response.Data = _configuration.GetValue<String>("Values:AppDomainName");
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "[{MethodName}] Error setting user id {UserIdentityId} login client to client company {ClientCompanyId}. {ExceptionMessage}",
                nameof(SetResellerStaffLoginClient),
                userIdentity.Id,
                clientCompanyId,
                ex.Message);

            response.Data = ex.Message;
        }

        return response;
    }

    public async Task<ResponseWrapper> GetResellerClientStaffs(
        string resellerProfileId,
        string clientCompanyId)
    {
        var response = new ResponseWrapper()
        {
            IsSuccess = false
        };

        try
        {
            var resellerClientCompanyProfile = await _appDbContext.ResellerClientCompanyProfiles
                .Include(x => x.ClientCompany)
                .Include(x => x.ResellerCompanyProfile)
                .FirstOrDefaultAsync(
                    x => x.ClientCompanyId == clientCompanyId && x.ResellerCompanyProfileId == resellerProfileId);

            if (resellerClientCompanyProfile == null)
            {
                response.ErrorMsg = "Reseller Client Company Profile Not Found";
                return response;
            }

            var resellerStaffIds = await _appDbContext.ResellerStaffs
                .Where(x => x.ResellerCompanyProfileId == resellerProfileId)
                .Select(x => x.IdentityId)
                .ToListAsync();

            var clientCompanyStaffs = await _appDbContext.UserRoleStaffs
                .Where(x => x.CompanyId == clientCompanyId && !resellerStaffIds.Contains(x.IdentityId))
                .Include(x => x.Identity)
                .ToListAsync();

            var resellerClientStaffs = new List<ResellerClientStaffInformation>();

            foreach (var clientCompanyStaff in clientCompanyStaffs)
            {
                var user = await _userManager.FindByIdAsync(clientCompanyStaff.IdentityId);

                if (user == null)
                {
                    continue;
                }

                var resellerClientStaff = new ResellerClientStaffInformation()
                {
                    Username = user.UserName,
                    Email = user.Email,
                    PhoneNumber = user.PhoneNumber,
                    FirstName = user.FirstName,
                    LastName = user.LastName,
                    DisplayName = user.DisplayName,
                    RoleType = clientCompanyStaff.RoleType,
                    CreatedAt = user.CreatedAt
                };

                resellerClientStaffs.Add(resellerClientStaff);
            }

            response.IsSuccess = true;
            response.Data = resellerClientStaffs;
            return response;
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "[{MethodName}] Error getting reseller client staffs for reseller profile {ResellerProfileId} and client company {ClientCompanyId}. {ExceptionMessage}",
                nameof(GetResellerClientStaffs),
                resellerProfileId,
                clientCompanyId,
                ex.Message);

            response.ErrorMsg = ex.Message;
        }

        return response;
    }

    public async Task<ResponseWrapper> RemoveResellerClientStaff(
        string resellerProfileId,
        string email,
        string clientCompanyId,
        string modifiedById)
    {
        var response = new ResponseWrapper()
        {
            IsSuccess = false
        };

        try
        {
            var clientCompanyProfile = await _appDbContext.ResellerClientCompanyProfiles
                .Include(x => x.ClientCompany)
                .FirstOrDefaultAsync(
                    x => x.ClientCompanyId == clientCompanyId
                         && x.ResellerCompanyProfileId == resellerProfileId);

            if (clientCompanyProfile == null)
            {
                response.ErrorMsg = "Client Company Profile Not Found";
                return response;
            }

            var userToRemove = await _userManager.FindByEmailAsync(email);

            if (userToRemove == null)
            {
                response.ErrorMsg = "User Not Found";
                return response;
            }

            var userRoleStaffsToRemove = await _appDbContext.UserRoleStaffs
                .Include(x => x.ProfilePicture)
                .Where(
                    x => x.IdentityId == userToRemove.Id
                         && x.CompanyId == clientCompanyId)
                .ToListAsync();

            if (!userRoleStaffsToRemove.Any())
            {
                response.ErrorMsg = "User is not a staff in current Client Company";
                return response;
            }

            var profilePictures = userRoleStaffsToRemove
                .Where(x => x.ProfilePicture != null)
                .Select(x => x.ProfilePicture)
                .ToList();

            if (profilePictures.Any())
            {
                _appDbContext.UserStaffProfilePictures.RemoveRange(profilePictures);
            }

            _appDbContext.UserRoleStaffs.RemoveRange(userRoleStaffsToRemove);

            await _appDbContext.SaveChangesAsync();
            await _userManager.DeleteAsync(userToRemove);

            await AddResellerActivityLog(
                new ResellerActivityLog
                {
                    ResellerCompanyProfileId = resellerProfileId,
                    CompanyId = clientCompanyId,
                    CreatedByUserId = modifiedById,
                    Category = ResellerActivityLogCategory.User,
                    Action = $"Remove User - {userToRemove.UserName}"
                });

            response.IsSuccess = true;
            response.Data = "User removed successfully";
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "[{MethodName}] Error removing reseller client staff {Email} from client company {ClientCompanyId}. {ExceptionMessage}",
                nameof(RemoveResellerClientStaff),
                email,
                clientCompanyId,
                ex.Message);

            response.ErrorMsg = ex.Message;
        }

        return response;
    }

    public async Task SetSubscriptionPlanMaximumUsage(
        Company company)
    {
        await _resellerBaseService.SetSubscriptionPlanMaximumUsage(company);
    }

    public async Task<ResponseWrapper> CheckAlreadyUpDowngraded(
        Company company,
        string toSubscriptionPlanId,
        ChangePlanType changePlanType)
    {
        var responseWrapper = new ResponseWrapper()
        {
            IsSuccess = false
        };

        try
        {
            if (changePlanType == ChangePlanType.Upgrade)
            {
                var latestUpgradedPlan = company.BillRecords.OrderByDescending(x => x.PeriodEnd)
                    .ThenByDescending(x => x.Id)
                    .Where(
                        x => ValidSubscriptionPlan.SubscriptionPlan.Contains(x.SubscriptionPlanId) &&
                             x.UpgradeFromBillRecordId != null && x.PeriodEnd > DateTime.UtcNow &&
                             x.Status == BillStatus.Active).Select(x => x.SubscriptionPlanId).FirstOrDefault();
                var isUpgraded = latestUpgradedPlan == toSubscriptionPlanId;

                responseWrapper.IsSuccess = true;
                responseWrapper.Data = isUpgraded;
            }
            else if (changePlanType == ChangePlanType.Downgrade)
            {
                var latestDowngradedPlan = company.BillRecords.OrderByDescending(x => x.PeriodEnd)
                    .ThenByDescending(x => x.Id).Where(
                        x => ValidSubscriptionPlan.SubscriptionPlan.Contains(x.SubscriptionPlanId) &&
                             x.DowngradeFromBillRecordId != null && x.PeriodEnd > DateTime.UtcNow &&
                             x.Status == BillStatus.Active).Select(x => x.SubscriptionPlanId).FirstOrDefault();
                var isDowngraded = latestDowngradedPlan == toSubscriptionPlanId;

                responseWrapper.IsSuccess = true;
                responseWrapper.Data = isDowngraded;
            }
        }
        catch (Exception ex)
        {
            responseWrapper.ErrorMsg = ex.Message;
        }

        return responseWrapper;
    }

    private async Task<AnyDowngradePlan> CheckAnyDowngradePlan(
        List<BillRecordDto> billRecords,
        SubscriptionPlan currentPlan)
    {
        var response = new AnyDowngradePlan()
        {
            IsDowngraded = false
        };
        var currentBillRecord = billRecords.OrderByDescending(x => x.PeriodEnd)
            .FirstOrDefault(x => x.SubscriptionPlanId == currentPlan.Id);
        if (currentBillRecord == null)
        {
            return response;
        }

        var downgradePlanBillRecord = billRecords.FirstOrDefault(
            x => x.PeriodEnd > currentBillRecord.PeriodEnd && IsDowngradePlan(currentPlan.Id, x.SubscriptionPlanId) &&
                 x.Status == BillStatus.Active);

        if (downgradePlanBillRecord == null)
        {
            return response;
        }

        response.DowngradedSubscriptionPlan = _mapper.Map<SubscriptionPlanDto>(
            await _appDbContext.CoreSubscriptionPlans.FirstOrDefaultAsync(
                x => x.Id == downgradePlanBillRecord.SubscriptionPlanId));
        response.EffectiveDate = downgradePlanBillRecord.PeriodStart;
        response.IsDowngraded = true;
        return response;
    }

    private bool IsDowngradePlan(string currentPlanId, string nextPlanId)
    {
        var isDowngraded =
            ValidSubscriptionPlan.PremiumTier.Contains(currentPlanId) &&
            ValidSubscriptionPlan.FreePlans.Contains(nextPlanId) ||
            ValidSubscriptionPlan.ProTier.Contains(currentPlanId) &&
            ValidSubscriptionPlan.FreePlans.Contains(nextPlanId) ||
            ValidSubscriptionPlan.PremiumTier.Contains(currentPlanId) &&
            ValidSubscriptionPlan.ProTier.Contains(nextPlanId);

        return isDowngraded;
    }

    private async Task<List<CustomUserProfileFieldOption>> GetCountryList()
    {
        var result = await _appDbContext.CoreCountries.ToListAsync();

        if (result.Count > 0)
        {
            var response = _mapper.Map<List<CustomUserProfileFieldOption>>(result);

            return response;
        }
        else
        {
            List<Country> cultureList = new List<Country>();

            CultureInfo[] getCultureInfo = CultureInfo.GetCultures(CultureTypes.SpecificCultures);

            foreach (CultureInfo getCulture in getCultureInfo)
            {
                try
                {
                    RegionInfo getRegionInfo = new RegionInfo(getCulture.LCID);

                    if (!cultureList.Select(x => x.EnglishName).Contains(getRegionInfo.EnglishName))
                    {
                        cultureList.Add(
                            new Country
                            {
                                Id = getRegionInfo.TwoLetterISORegionName,
                                EnglishName = getRegionInfo.EnglishName,
                                DisplayName = getRegionInfo.DisplayName
                            });
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogInformation(ex.Message);
                }
            }

            await _appDbContext.CoreCountries.AddRangeAsync(cultureList);
            await _appDbContext.SaveChangesAsync();

            var response = _mapper.Map<List<CustomUserProfileFieldOption>>(cultureList);

            return response;
        }
    }

    private Company AddDefaultLabel(Company company)
    {
        company.CompanyHashtags = new List<CompanyHashtag>();

        company.CompanyHashtags.Add(
            new CompanyHashtag
            {
                Hashtag = "New customer", HashTagColor = HashTagColor.Cyan
            });
        company.CompanyHashtags.Add(
            new CompanyHashtag
            {
                Hashtag = "New order", HashTagColor = HashTagColor.Yellow
            });
        company.CompanyHashtags.Add(
            new CompanyHashtag
            {
                Hashtag = "Pending payment", HashTagColor = HashTagColor.Pink
            });
        company.CompanyHashtags.Add(
            new CompanyHashtag
            {
                Hashtag = "Paid", HashTagColor = HashTagColor.Purple
            });
        company.CompanyHashtags.Add(
            new CompanyHashtag
            {
                Hashtag = "Order complete", HashTagColor = HashTagColor.Green
            });
        company.CompanyHashtags.Add(
            new CompanyHashtag
            {
                Hashtag = "VIP", HashTagColor = HashTagColor.Blue
            });
        company.CompanyHashtags.Add(
            new CompanyHashtag
            {
                Hashtag = "Issue", HashTagColor = HashTagColor.Red
            });

        return company;
    }

    private Company AddDefaultUserFiels(Company company)
    {
        List<CompanyCustomUserProfileField> companyCustomUserProfiles = new List<CompanyCustomUserProfileField>()
        {
            new CompanyCustomUserProfileField
            {
                FieldName = "Labels",
                Type = FieldDataType.Labels,
                Order = 0,
                CustomUserProfileFieldLinguals = new List<CustomUserProfileFieldLingual>()
                {
                    new CustomUserProfileFieldLingual
                    {
                        DisplayName = "Labels", Language = "en"
                    },
                    new CustomUserProfileFieldLingual
                    {
                        DisplayName = "標籤", Language = "zh-hk"
                    }
                },
                IsDeletable = false,
                IsEditable = false,
                IsDefault = true,
                FieldsCategory = FieldsCategory.Segmentation
            },
            new CompanyCustomUserProfileField
            {
                FieldName = "Lists",
                Type = FieldDataType.Lists,
                Order = 1,
                CustomUserProfileFieldLinguals = new List<CustomUserProfileFieldLingual>()
                {
                    new CustomUserProfileFieldLingual
                    {
                        DisplayName = "Lists", Language = "en"
                    },
                    new CustomUserProfileFieldLingual
                    {
                        DisplayName = "名單", Language = "zh-hk"
                    }
                },
                IsDeletable = false,
                IsEditable = false,
                IsDefault = true,
                FieldsCategory = FieldsCategory.Segmentation
            },
            new CompanyCustomUserProfileField
            {
                FieldName = "Email",
                Type = FieldDataType.Email,
                Order = 2,
                CustomUserProfileFieldLinguals = new List<CustomUserProfileFieldLingual>()
                {
                    new CustomUserProfileFieldLingual
                    {
                        DisplayName = "Email", Language = "en"
                    },
                    new CustomUserProfileFieldLingual
                    {
                        DisplayName = "電郵地址", Language = "zh-hk"
                    }
                },
                IsDeletable = false
            },
            new CompanyCustomUserProfileField
            {
                FieldName = "PhoneNumber",
                Type = FieldDataType.PhoneNumber,
                Order = 3,
                CustomUserProfileFieldLinguals = new List<CustomUserProfileFieldLingual>()
                {
                    new CustomUserProfileFieldLingual
                    {
                        DisplayName = "Phone Number", Language = "en"
                    },
                    new CustomUserProfileFieldLingual
                    {
                        DisplayName = "電話號碼", Language = "zh-hk"
                    }
                },
                IsDeletable = false
            },
            new CompanyCustomUserProfileField
            {
                FieldName = "CompanyName",
                Type = FieldDataType.SingleLineText,
                Order = 4,
                CustomUserProfileFieldLinguals = new List<CustomUserProfileFieldLingual>()
                {
                    new CustomUserProfileFieldLingual
                    {
                        DisplayName = "Company Name", Language = "en"
                    },
                    new CustomUserProfileFieldLingual
                    {
                        DisplayName = "公司名稱", Language = "zh-hk"
                    }
                }
            },
            new CompanyCustomUserProfileField
            {
                FieldName = "JobTitle",
                Type = FieldDataType.SingleLineText,
                Order = 5,
                CustomUserProfileFieldLinguals = new List<CustomUserProfileFieldLingual>()
                {
                    new CustomUserProfileFieldLingual
                    {
                        DisplayName = "Job Title", Language = "en"
                    },
                    new CustomUserProfileFieldLingual
                    {
                        DisplayName = "職稱", Language = "zh-hk"
                    }
                }
            },
            new CompanyCustomUserProfileField
            {
                FieldName = "ContactOwner",
                Type = FieldDataType.TravisUser,
                Order = 6,
                CustomUserProfileFieldLinguals = new List<CustomUserProfileFieldLingual>()
                {
                    new CustomUserProfileFieldLingual
                    {
                        DisplayName = "Contact Owner", Language = "en"
                    },
                    new CustomUserProfileFieldLingual
                    {
                        DisplayName = "聯絡負責人", Language = "zh-hk"
                    }
                },
                IsDeletable = false,
                IsEditable = true,
                IsDefault = true,
                FieldsCategory = FieldsCategory.SleekFlowUser
            },
            new CompanyCustomUserProfileField
            {
                FieldName = "LeadStage",
                Type = FieldDataType.Options,
                Order = 7,
                CustomUserProfileFieldLinguals = new List<CustomUserProfileFieldLingual>()
                {
                    new CustomUserProfileFieldLingual
                    {
                        DisplayName = "Lead Stage", Language = "en"
                    },
                    new CustomUserProfileFieldLingual
                    {
                        DisplayName = "階段", Language = "zh-hk"
                    }
                },
                CustomUserProfileFieldOptions = new List<CustomUserProfileFieldOption>()
                {
                    new CustomUserProfileFieldOption
                    {
                        Value = "Prospect",
                        CustomUserProfileFieldOptionLinguals = new List<CustomUserProfileFieldOptionLingual>()
                        {
                            new CustomUserProfileFieldOptionLingual
                            {
                                DisplayName = "Prospect", Language = "en"
                            },
                            new CustomUserProfileFieldOptionLingual
                            {
                                DisplayName = "Prospect", Language = "zh-HK"
                            }
                        },
                        Order = 0
                    },
                    new CustomUserProfileFieldOption
                    {
                        Value = "Contacted",
                        CustomUserProfileFieldOptionLinguals = new List<CustomUserProfileFieldOptionLingual>()
                        {
                            new CustomUserProfileFieldOptionLingual
                            {
                                DisplayName = "Contacted", Language = "en"
                            },
                            new CustomUserProfileFieldOptionLingual
                            {
                                DisplayName = "Contacted", Language = "zh-HK"
                            }
                        },
                        Order = 1
                    },
                    new CustomUserProfileFieldOption
                    {
                        Value = "Lead",
                        CustomUserProfileFieldOptionLinguals = new List<CustomUserProfileFieldOptionLingual>()
                        {
                            new CustomUserProfileFieldOptionLingual
                            {
                                DisplayName = "Lead", Language = "en"
                            },
                            new CustomUserProfileFieldOptionLingual
                            {
                                DisplayName = "Lead", Language = "zh-HK"
                            }
                        },
                        Order = 2
                    },
                    new CustomUserProfileFieldOption
                    {
                        Value = "Opportunity",
                        CustomUserProfileFieldOptionLinguals = new List<CustomUserProfileFieldOptionLingual>()
                        {
                            new CustomUserProfileFieldOptionLingual
                            {
                                DisplayName = "Opportunity", Language = "en"
                            },
                            new CustomUserProfileFieldOptionLingual
                            {
                                DisplayName = "Opportunity", Language = "zh-HK"
                            }
                        },
                        Order = 3
                    },
                    new CustomUserProfileFieldOption
                    {
                        Value = "Customer",
                        CustomUserProfileFieldOptionLinguals = new List<CustomUserProfileFieldOptionLingual>()
                        {
                            new CustomUserProfileFieldOptionLingual
                            {
                                DisplayName = "Customer", Language = "en"
                            },
                            new CustomUserProfileFieldOptionLingual
                            {
                                DisplayName = "Customer", Language = "zh-HK"
                            }
                        },
                        Order = 4
                    },
                    new CustomUserProfileFieldOption
                    {
                        Value = "Lost",
                        CustomUserProfileFieldOptionLinguals = new List<CustomUserProfileFieldOptionLingual>()
                        {
                            new CustomUserProfileFieldOptionLingual
                            {
                                DisplayName = "Lost", Language = "en"
                            },
                            new CustomUserProfileFieldOptionLingual
                            {
                                DisplayName = "Lost", Language = "zh-HK"
                            }
                        },
                        Order = 5
                    },
                    new CustomUserProfileFieldOption
                    {
                        Value = "Inactive",
                        CustomUserProfileFieldOptionLinguals = new List<CustomUserProfileFieldOptionLingual>()
                        {
                            new CustomUserProfileFieldOptionLingual
                            {
                                DisplayName = "Inactive / to be reviewed", Language = "en"
                            },
                            new CustomUserProfileFieldOptionLingual
                            {
                                DisplayName = "Inactive / to be reviewed", Language = "zh-HK"
                            }
                        },
                        Order = 6
                    }
                },
                IsDeletable = false,
                IsEditable = true,
                IsDefault = true
            },
            new CompanyCustomUserProfileField
            {
                FieldName = "LeadSource",
                Type = FieldDataType.Options,
                Order = 8,
                CustomUserProfileFieldLinguals = new List<CustomUserProfileFieldLingual>()
                {
                    new CustomUserProfileFieldLingual
                    {
                        DisplayName = "Lead Source", Language = "en"
                    },
                    new CustomUserProfileFieldLingual
                    {
                        DisplayName = "資料來源", Language = "zh-hk"
                    }
                },
                CustomUserProfileFieldOptions = new List<CustomUserProfileFieldOption>()
                {
                    new CustomUserProfileFieldOption
                    {
                        Value = "Organic search",
                        CustomUserProfileFieldOptionLinguals = new List<CustomUserProfileFieldOptionLingual>()
                        {
                            new CustomUserProfileFieldOptionLingual
                            {
                                DisplayName = "Organic search", Language = "en"
                            },
                            new CustomUserProfileFieldOptionLingual
                            {
                                DisplayName = "網站", Language = "zh-HK"
                            }
                        },
                        Order = 0
                    },
                    new CustomUserProfileFieldOption
                    {
                        Value = "Referrals",
                        CustomUserProfileFieldOptionLinguals = new List<CustomUserProfileFieldOptionLingual>()
                        {
                            new CustomUserProfileFieldOptionLingual
                            {
                                DisplayName = "Referrals", Language = "en"
                            },
                            new CustomUserProfileFieldOptionLingual
                            {
                                DisplayName = "推薦", Language = "zh-HK"
                            }
                        },
                        Order = 1
                    },
                    new CustomUserProfileFieldOption
                    {
                        Value = "Social media",
                        CustomUserProfileFieldOptionLinguals = new List<CustomUserProfileFieldOptionLingual>()
                        {
                            new CustomUserProfileFieldOptionLingual
                            {
                                DisplayName = "Social media", Language = "en"
                            },
                            new CustomUserProfileFieldOptionLingual
                            {
                                DisplayName = "社交媒體", Language = "zh-HK"
                            }
                        },
                        Order = 2
                    },
                    new CustomUserProfileFieldOption
                    {
                        Value = "Email marketing",
                        CustomUserProfileFieldOptionLinguals = new List<CustomUserProfileFieldOptionLingual>()
                        {
                            new CustomUserProfileFieldOptionLingual
                            {
                                DisplayName = "Email marketing", Language = "en"
                            },
                            new CustomUserProfileFieldOptionLingual
                            {
                                DisplayName = "社交媒體", Language = "zh-HK"
                            }
                        },
                        Order = 3
                    },
                    new CustomUserProfileFieldOption
                    {
                        Value = "Paid search",
                        CustomUserProfileFieldOptionLinguals = new List<CustomUserProfileFieldOptionLingual>()
                        {
                            new CustomUserProfileFieldOptionLingual
                            {
                                DisplayName = "Paid search", Language = "en"
                            },
                            new CustomUserProfileFieldOptionLingual
                            {
                                DisplayName = "付費搜索", Language = "zh-HK"
                            }
                        },
                        Order = 4
                    },
                    new CustomUserProfileFieldOption
                    {
                        Value = "Paid social",
                        CustomUserProfileFieldOptionLinguals = new List<CustomUserProfileFieldOptionLingual>()
                        {
                            new CustomUserProfileFieldOptionLingual
                            {
                                DisplayName = "Paid social", Language = "en"
                            },
                            new CustomUserProfileFieldOptionLingual
                            {
                                DisplayName = "付費社交媒體", Language = "zh-HK"
                            }
                        },
                        Order = 5
                    },
                    new CustomUserProfileFieldOption
                    {
                        Value = "Direct traffic",
                        CustomUserProfileFieldOptionLinguals = new List<CustomUserProfileFieldOptionLingual>()
                        {
                            new CustomUserProfileFieldOptionLingual
                            {
                                DisplayName = "Direct traffic", Language = "en"
                            },
                            new CustomUserProfileFieldOptionLingual
                            {
                                DisplayName = "直接流量", Language = "zh-HK"
                            }
                        },
                        Order = 6
                    },
                    new CustomUserProfileFieldOption
                    {
                        Value = "Events and seminar",
                        CustomUserProfileFieldOptionLinguals = new List<CustomUserProfileFieldOptionLingual>()
                        {
                            new CustomUserProfileFieldOptionLingual
                            {
                                DisplayName = "Events and seminar", Language = "en"
                            },
                            new CustomUserProfileFieldOptionLingual
                            {
                                DisplayName = "研討會", Language = "zh-HK"
                            }
                        },
                        Order = 7
                    },
                    new CustomUserProfileFieldOption
                    {
                        Value = "Other offline sources",
                        CustomUserProfileFieldOptionLinguals = new List<CustomUserProfileFieldOptionLingual>()
                        {
                            new CustomUserProfileFieldOptionLingual
                            {
                                DisplayName = "Other offline sources", Language = "en"
                            },
                            new CustomUserProfileFieldOptionLingual
                            {
                                DisplayName = "其他活動", Language = "zh-HK"
                            }
                        },
                        Order = 8
                    },
                    new CustomUserProfileFieldOption
                    {
                        Value = "Others",
                        CustomUserProfileFieldOptionLinguals = new List<CustomUserProfileFieldOptionLingual>()
                        {
                            new CustomUserProfileFieldOptionLingual
                            {
                                DisplayName = "Others", Language = "en"
                            },
                            new CustomUserProfileFieldOptionLingual
                            {
                                DisplayName = "其他", Language = "zh-HK"
                            }
                        },
                        Order = 9
                    }
                },
                IsDeletable = false,
                IsEditable = true,
                IsDefault = true
            },
            new CompanyCustomUserProfileField
            {
                FieldName = "Priority",
                Type = FieldDataType.Options,
                Order = 9,
                CustomUserProfileFieldLinguals = new List<CustomUserProfileFieldLingual>()
                {
                    new CustomUserProfileFieldLingual
                    {
                        DisplayName = "Priority", Language = "en"
                    },
                    new CustomUserProfileFieldLingual
                    {
                        DisplayName = "優先度", Language = "zh-hk"
                    }
                },
                CustomUserProfileFieldOptions = new List<CustomUserProfileFieldOption>()
                {
                    new CustomUserProfileFieldOption
                    {
                        Value = "High",
                        CustomUserProfileFieldOptionLinguals = new List<CustomUserProfileFieldOptionLingual>()
                        {
                            new CustomUserProfileFieldOptionLingual
                            {
                                DisplayName = "High", Language = "en"
                            },
                            new CustomUserProfileFieldOptionLingual
                            {
                                DisplayName = "高", Language = "zh-HK"
                            }
                        },
                        Order = 0
                    },
                    new CustomUserProfileFieldOption
                    {
                        Value = "Medium",
                        CustomUserProfileFieldOptionLinguals = new List<CustomUserProfileFieldOptionLingual>()
                        {
                            new CustomUserProfileFieldOptionLingual
                            {
                                DisplayName = "Medium", Language = "en"
                            },
                            new CustomUserProfileFieldOptionLingual
                            {
                                DisplayName = "中", Language = "zh-HK"
                            }
                        },
                        Order = 1
                    },
                    new CustomUserProfileFieldOption
                    {
                        Value = "Low",
                        CustomUserProfileFieldOptionLinguals = new List<CustomUserProfileFieldOptionLingual>()
                        {
                            new CustomUserProfileFieldOptionLingual
                            {
                                DisplayName = "Low", Language = "en"
                            },
                            new CustomUserProfileFieldOptionLingual
                            {
                                DisplayName = "低", Language = "zh-HK"
                            }
                        },
                        Order = 2
                    }
                }
            },
            new CompanyCustomUserProfileField
            {
                FieldName = "Country",
                Type = FieldDataType.Options,
                Order = 10,
                CustomUserProfileFieldLinguals = new List<CustomUserProfileFieldLingual>()
                {
                    new CustomUserProfileFieldLingual
                    {
                        DisplayName = "Country", Language = "en"
                    },
                    new CustomUserProfileFieldLingual
                    {
                        DisplayName = "國家", Language = "zh-hk"
                    },
                },
                CustomUserProfileFieldOptions = GetCountryList().Result,
                IsDefault = true,
                IsDeletable = false,
                IsEditable = false
            },
            new CompanyCustomUserProfileField
            {
                FieldName = "Subscriber",
                Type = FieldDataType.Boolean,
                Order = 11,
                CustomUserProfileFieldLinguals = new List<CustomUserProfileFieldLingual>()
                {
                    new CustomUserProfileFieldLingual
                    {
                        DisplayName = "Subscriber", Language = "en"
                    },
                    new CustomUserProfileFieldLingual
                    {
                        DisplayName = "訂閱者", Language = "zh-hk"
                    }
                },
                IsDeletable = false,
                IsEditable = true,
                IsDefault = true
            },
            new CompanyCustomUserProfileField
            {
                FieldName = "LastChannel",
                Type = FieldDataType.Channel,
                Order = 12,
                CustomUserProfileFieldLinguals = new List<CustomUserProfileFieldLingual>()
                {
                    new CustomUserProfileFieldLingual
                    {
                        DisplayName = "Last Channel", Language = "en"
                    },
                    new CustomUserProfileFieldLingual
                    {
                        DisplayName = "最後聯繫頻道", Language = "zh-hk"
                    }
                },
                IsEditable = false,
                IsDeletable = false,
                FieldsCategory = FieldsCategory.Message
            },
            new CompanyCustomUserProfileField
            {
                FieldName = "LastContact",
                Type = FieldDataType.DateTime,
                Order = 13,
                CustomUserProfileFieldLinguals = new List<CustomUserProfileFieldLingual>()
                {
                    new CustomUserProfileFieldLingual
                    {
                        DisplayName = "Last Contact From You", Language = "en"
                    },
                    new CustomUserProfileFieldLingual
                    {
                        DisplayName = "最後聯絡時間", Language = "zh-hk"
                    }
                },
                IsEditable = false,
                IsDeletable = false,
                FieldsCategory = FieldsCategory.Message
            },
            new CompanyCustomUserProfileField
            {
                FieldName = "LastContactFromCustomers",
                Type = FieldDataType.DateTime,
                Order = 14,
                CustomUserProfileFieldLinguals = new List<CustomUserProfileFieldLingual>()
                {
                    new CustomUserProfileFieldLingual
                    {
                        DisplayName = "Last Contact From Customers", Language = "en"
                    },
                    new CustomUserProfileFieldLingual
                    {
                        DisplayName = "客戶的最後聯絡時間", Language = "zh-hk"
                    }
                },
                IsEditable = false,
                IsDeletable = false,
                FieldsCategory = FieldsCategory.Message
            }
        };

        company.CustomUserProfileFields = new List<CompanyCustomUserProfileField>();

        foreach (var customFeild in companyCustomUserProfiles)
        {
            company.CustomUserProfileFields.Add(customFeild);
        }

        return company;
    }

    private Company AddDefaultQuickReply(Company company)
    {
        company.QuickReplies = new List<CompanyQuickReply>()
        {
            new CompanyQuickReply
            {
                Value = "Welcome",
                CompanyQuickReplyLinguals = new List<CompanyQuickReplyLingual>
                {
                    new CompanyQuickReplyLingual
                    {
                        Language = "en", Value = "Hello. Thank you for contacting. How may we help you today?"
                    }
                }
            },
            new CompanyQuickReply
            {
                Value = "Thanks",
                CompanyQuickReplyLinguals = new List<CompanyQuickReplyLingual>
                {
                    new CompanyQuickReplyLingual
                    {
                        Language = "en",
                        Value = "Thank you for your business! We look forward to working with you again."
                    }
                }
            },
            new CompanyQuickReply
            {
                Value = "Away",
                CompanyQuickReplyLinguals = new List<CompanyQuickReplyLingual>
                {
                    new CompanyQuickReplyLingual
                    {
                        Language = "en",
                        Value =
                            "Hello. Thank you for your message. We’re not here right now, but will respond as soon as we return."
                    }
                }
            },
            new CompanyQuickReply
            {
                Value = "Close",
                CompanyQuickReplyLinguals = new List<CompanyQuickReplyLingual>
                {
                    new CompanyQuickReplyLingual
                    {
                        Language = "en",
                        Value = "Let me know if you have any further questions. Our team is always here for you."
                    }
                }
            }
        };

        return company;
    }

    private Company AddDefaultCompanyList(Company company)
    {
        company.ImportContactHistories = new List<ImportContactHistory>();

        company.ImportContactHistories.Add(
            new ImportContactHistory
            {
                ImportName = "[Template] Leads"
            });
        company.ImportContactHistories.Add(
            new ImportContactHistory
            {
                ImportName = "[Template] Customers"
            });
        company.ImportContactHistories.Add(
            new ImportContactHistory
            {
                ImportName = "[Template] Pending"
            });
        company.ImportContactHistories.Add(
            new ImportContactHistory
            {
                ImportName = "[Template] VIP"
            });

        return company;
    }

    public string GetResellerSubscriptionPlanId(
        SubscriptionTier subscriptionTier,
        ResellerCompanyProfile resellerProfile,
        string changeAddOnType = null)
    {
        if (changeAddOnType == null)
        {
            return subscriptionTier switch
            {
                SubscriptionTier.Pro => resellerProfile.ResellerSubscriptionPlanConfig[ResellerSubscriptionPlanIds.SubscriptionProPlanId],
                SubscriptionTier.Premium => resellerProfile.ResellerSubscriptionPlanConfig[ResellerSubscriptionPlanIds.SubscriptionPremiumPlanId],
                SubscriptionTier.Free => resellerProfile.ResellerSubscriptionPlanConfig[ResellerSubscriptionPlanIds.SubscriptionFreePlanId],
                _ => resellerProfile.ResellerSubscriptionPlanConfig[ResellerSubscriptionPlanIds.SubscriptionFreePlanId]
            };
        }

        switch (subscriptionTier)
        {
            case SubscriptionTier.Free:
                return changeAddOnType switch
                {
                    ChangeAddOnType.Agent => resellerProfile.ResellerSubscriptionPlanConfig[ResellerSubscriptionPlanIds.AddOnAgentProPlanId],
                    ChangeAddOnType.AdditionalContact => resellerProfile.ResellerSubscriptionPlanConfig[ResellerSubscriptionPlanIds.AddOnCustomerContactProPlanId],
                    ChangeAddOnType.WhatsAppPhoneNumber => resellerProfile.ResellerSubscriptionPlanConfig[ResellerSubscriptionPlanIds.AddOnWhatsAppPhoneNumberProPlanId],
                    _ => resellerProfile.ResellerSubscriptionPlanConfig[ResellerSubscriptionPlanIds.SubscriptionFreePlanId]
                };
            case SubscriptionTier.Pro:
                return changeAddOnType switch
                {
                    ChangeAddOnType.Agent => resellerProfile.ResellerSubscriptionPlanConfig[ResellerSubscriptionPlanIds.AddOnAgentProPlanId],
                    ChangeAddOnType.AdditionalContact => resellerProfile.ResellerSubscriptionPlanConfig[ResellerSubscriptionPlanIds.AddOnCustomerContactProPlanId],
                    ChangeAddOnType.WhatsAppPhoneNumber => resellerProfile.ResellerSubscriptionPlanConfig[ResellerSubscriptionPlanIds.AddOnWhatsAppPhoneNumberProPlanId],
                    _ => resellerProfile.ResellerSubscriptionPlanConfig[ResellerSubscriptionPlanIds.SubscriptionFreePlanId]
                };
            case SubscriptionTier.Premium:
                return changeAddOnType switch
                {
                    ChangeAddOnType.Agent => resellerProfile.ResellerSubscriptionPlanConfig[ResellerSubscriptionPlanIds.AddOnAgentPremiumPlanId],
                    ChangeAddOnType.AdditionalContact => resellerProfile.ResellerSubscriptionPlanConfig[ResellerSubscriptionPlanIds.AddOnCustomerContactPremiumPlanId],
                    ChangeAddOnType.WhatsAppPhoneNumber => resellerProfile.ResellerSubscriptionPlanConfig[ResellerSubscriptionPlanIds.AddOnWhatsAppPhoneNumberPremiumPlanId],
                    _ => resellerProfile.ResellerSubscriptionPlanConfig[ResellerSubscriptionPlanIds.SubscriptionFreePlanId]
                };
            case SubscriptionTier.Enterprise:
            case SubscriptionTier.AddOn:
            case SubscriptionTier.Agent:
            case SubscriptionTier.MarkUpLog:
            default:
                return resellerProfile.ResellerSubscriptionPlanConfig[ResellerSubscriptionPlanIds.SubscriptionFreePlanId];
        }
    }

    public string SetTransactionAction(SubscriptionPlan subscriptionPlan)
    {
        return _resellerBaseService.SetTransactionAction(subscriptionPlan);
    }

    public async Task AddResellerActivityLog(ResellerActivityLog resellerActivityLog)
    {
        await _resellerBaseService.AddResellerActivityLog(resellerActivityLog);
    }

    public Task<ResellerCompanyProfile> CheckAndUpdateResellerSubscriptionPlanConfig(
        ResellerCompanyProfile resellerCompanyProfile)
    {
        return _resellerBaseService.CheckAndUpdateResellerSubscriptionPlanConfig(resellerCompanyProfile);
    }
}