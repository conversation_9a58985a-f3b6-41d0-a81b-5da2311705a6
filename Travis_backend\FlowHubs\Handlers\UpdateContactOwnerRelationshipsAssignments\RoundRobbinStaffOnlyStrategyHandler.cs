﻿using System.Threading.Tasks;
using Sleekflow.Apis.FlowHub.Model;
using Travis_backend.CompanyDomain.Services;
using Travis_backend.ContactDomain.Services;
using Travis_backend.ConversationServices;
using Travis_backend.FlowHubs.Constants;
using Travis_backend.FlowHubs.Exceptions;
using Travis_backend.FlowHubs.Interfaces;

namespace Travis_backend.FlowHubs.Handlers.UpdateContactOwnerRelationshipsAssignments;

public sealed class RoundRobbinStaffOnlyStrategyHandler : IUpdateContactOwnerRelationshipsAssignmentStrategyHandler
{
    private readonly ICompanyService _companyService;
    private readonly IUserProfileService _userProfileService;
    private readonly IConversationMessageService _conversationMessageService;

    public string StrategyName => UpdateContactOwnerRelationshipsAssignmentStrategy.RoundRobbin_StaffOnly;

    public RoundRobbinStaffOnlyStrategyHandler(
        ICompanyService companyService,
        IUserProfileService userProfileService,
        IConversationMessageService conversationMessageService)
    {
        _companyService = companyService;
        _userProfileService = userProfileService;
        _conversationMessageService = conversationMessageService;
    }

    public async Task HandleAsync(UpdateContactOwnerRelationshipsInput input)
    {
        var workflowVersionedId = input.StateIdentity.WorkflowVersionedId;
        var companyId = input.StateIdentity.SleekflowCompanyId;
        var contactId = input.ContactId;
        var staffId = input.StaffId;

        var conversation = await _userProfileService.GetConversationByUserProfileId(
            companyId,
            userProfileId: contactId,
            status: "closed");

        var staff = await _companyService.GetStaffAsync(companyId, staffId);

        if (staff is null)
        {
            throw new CompanyStaffNotFoundException(
                workflowVersionedId,
                companyId,
                staffId);
        }

        await _conversationMessageService.ChangeConversationAssignee(
            conversation,
            assignee: staff);

        await _conversationMessageService.ChangeConversationAssignedTeam(
            conversation,
            companyTeam: null);
    }
}