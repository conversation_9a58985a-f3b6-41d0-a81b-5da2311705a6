using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.OpenApi.Extensions;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using Newtonsoft.Json.Linq;
using Sleekflow.Apis.FlowHub.Api;
using Sleekflow.Apis.FlowHub.Model;
using Travis_backend.Cache;
using Travis_backend.Cache.Models.CacheKeyPatterns;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.CompanyDomain.Services;
using Travis_backend.Constants;
using Travis_backend.ContactDomain.Models;
using Travis_backend.Database;
using Travis_backend.Enums;
using Travis_backend.FlowHubs.Models;
using Travis_backend.Infrastructures.Options;
using Travis_backend.InternalDomain.ViewModels;
using Travis_backend.SubscriptionPlanDomain.Constants;
using Travis_backend.SubscriptionPlanDomain.Services;

namespace Travis_backend.FlowHubs;

public interface IFlowHubService
{
    Task<bool> IsFlowHubEnabledAsync(string companyId);

    Task<Dictionary<string, JToken>> GetUserProfileDictAsync(string companyId, string userProfileId);

    Task<Dictionary<string, JToken>> GetUserProfileDictAsync(UserProfile userProfile);

    Task<Dictionary<string, JToken>> GetStaffDictAsync(string inputStaffId, string companyId);

    Task<Dictionary<string, JToken>> GetStaffDictAsync(Staff staff);

    Task UpdateFlowHubConfigAsync(string companyId, Staff? staff);

    Task<UsageLimit> GetUsageLimitForUpdateAsync(string companyId);

    Task<List<CmsCompanyFlowHubData>> GetCompaniesFlowHubData();
}

public class FlowHubService
    : IFlowHubService
{
    private readonly ICompanySubscriptionService _companySubscriptionService;
    private readonly ISubscriptionPlanService _subscriptionPlanService;
    private readonly ILogger<FlowHubService> _logger;
    private readonly IFeatureQuantityService _featureQuantityService;
    private readonly ICacheManagerService _cacheManagerService;
    private readonly IFlowHubConfigsApi _flowHubConfigsApi;
    private readonly IExecutionsApi _executionsApi;
    private readonly IWorkflowsApi _workflowsApi;
    private readonly ApplicationDbContext _appDbContext;
    private readonly IMapper _mapper;
    private readonly FeatureFlagsOptions _featureFlagsOptions;

    public FlowHubService(
        ICompanySubscriptionService companySubscriptionService,
        ISubscriptionPlanService subscriptionPlanService,
        ILogger<FlowHubService> logger,
        IFeatureQuantityService featureQuantityService,
        ICacheManagerService cacheManagerService,
        IFlowHubConfigsApi flowHubConfigsApi,
        IExecutionsApi executionsApi,
        IWorkflowsApi workflowsApi,
        ApplicationDbContext appDbContext,
        IMapper mapper,
        IOptions<FeatureFlagsOptions> featureFlagsOptions)
    {
        _companySubscriptionService = companySubscriptionService;
        _subscriptionPlanService = subscriptionPlanService;
        _logger = logger;
        _featureQuantityService = featureQuantityService;
        _cacheManagerService = cacheManagerService;
        _flowHubConfigsApi = flowHubConfigsApi;
        _executionsApi = executionsApi;
        _workflowsApi = workflowsApi;
        _appDbContext = appDbContext;
        _mapper = mapper;
        _featureFlagsOptions = featureFlagsOptions.Value;
    }


    private static readonly JsonSerializerSettings DefaultLoggingJsonSerializerSettings = new JsonSerializerSettings()
    {
        Converters = new List<JsonConverter>()
        {
            new IsoDateTimeConverter
            {
                DateTimeFormat = "yyyy'-'MM'-'dd'T'HH':'mm':'ss.fff'Z'"
            }
        },
        ReferenceLoopHandling = ReferenceLoopHandling.Ignore,
        DateParseHandling = DateParseHandling.DateTimeOffset,
        DateTimeZoneHandling = DateTimeZoneHandling.Utc,
        MaxDepth = 32,
    };

    public async Task UpdateFlowHubConfigAsync(string companyId, Staff? staff)
    {
        var getFlowHubConfigOutputOutput =
            await _flowHubConfigsApi.FlowHubConfigsGetFlowHubConfigPostAsync(
                getFlowHubConfigInput: new GetFlowHubConfigInput(companyId));

        _logger.LogInformation(
            "[{MethodName}] Company FlowHubConfig, CompanyId: {CompanyId}, IsSuccess: {IsSuccess}, IsEnrolled: {IsEnrolled}",
            nameof(UpdateFlowHubConfigAsync),
            companyId,
            getFlowHubConfigOutputOutput.Success,
            getFlowHubConfigOutputOutput.Data.FlowHubConfig.IsEnrolled);

        if (getFlowHubConfigOutputOutput.Success &&
            getFlowHubConfigOutputOutput.Data.FlowHubConfig.IsEnrolled)
        {
            _logger.LogInformation("Updating FlowHub UsageLimit for Company: {CompanyId}", companyId);

            var usageLimit = await GetUsageLimitForUpdateAsync(companyId);

            var updateFlowHubConfigOutputOutput =
                await _flowHubConfigsApi.FlowHubConfigsUpdateFlowHubConfigPostAsync(
                    updateFlowHubConfigInput: new UpdateFlowHubConfigInput(
                        sleekflowCompanyId: companyId,
                        sleekflowStaffId: staff?.Id.ToString()!,
                        usageLimit: usageLimit));

            if (!updateFlowHubConfigOutputOutput.Success)
            {
                _logger.LogError(
                    "Update Flow Builder config error: company id {CompanyId}",
                    staff.CompanyId);
            }
        }
    }

    public async Task<bool> IsFlowHubEnabledAsync(string companyId)
    {
        try
        {
            var flowHubConfigCacheKeyPattern = new FlowHubConfigCacheKeyPattern(companyId);
            var cachedFlowHubConfigStr = await _cacheManagerService.GetCacheAsync(flowHubConfigCacheKeyPattern);

            if (cachedFlowHubConfigStr != null)
            {
                return JsonConvert.DeserializeObject<FlowHubConfig>(cachedFlowHubConfigStr).IsEnrolled;
            }

            var flowHubConfigOutputOutput = await _flowHubConfigsApi.FlowHubConfigsGetFlowHubConfigPostAsync(
                getFlowHubConfigInput: new GetFlowHubConfigInput(companyId));

            await _cacheManagerService.SaveCacheAsync(
                flowHubConfigCacheKeyPattern,
                flowHubConfigOutputOutput.Data.FlowHubConfig);

            return flowHubConfigOutputOutput.Data.FlowHubConfig.IsEnrolled;
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "Failed to get FlowHubConfig for company {CompanyId}",
                companyId);

            return false;
        }
    }

    public async Task<Dictionary<string, JToken>> GetUserProfileDictAsync(string companyId, string userProfileId)
    {
        var userProfile = await _appDbContext.UserProfiles
            .Where(x => x.Id == userProfileId && x.CompanyId == companyId)
            .Include(x => x.CustomFields)
            .ThenInclude(x => x.CompanyDefinedField)
            .FirstOrDefaultAsync();
        if (userProfile == null)
        {
            throw new Exception("Unable to find the user profile");
        }

        return await GetUserProfileDictAsync(userProfile);
    }

    public async Task<Dictionary<string, JToken>> GetUserProfileDictAsync(UserProfile userProfile)
    {
        try
        {
            var fieldIdToFieldNames = await _appDbContext.CompanyCustomUserProfileFields
                .Where(x => x.CompanyId == userProfile.CompanyId)
                .Select(
                    x => new
                    {
                        x.Id, x.FieldName
                    })
                .ToListAsync();
            var fieldIdToFieldNameDict = fieldIdToFieldNames
                .GroupBy(x => x.Id)
                .ToDictionary(
                    x => x.Key,
                    x => x.First().FieldName);

            var userProfileDto = _mapper.Map<UserProfileDto>(userProfile)!;

            var conversationIds = await _appDbContext.Conversations
                .Where(c => c.UserProfileId == userProfile.Id)
                .Select(c => c.Id)
                .ToListAsync();
            var conversationId = conversationIds.FirstOrDefault();
            if (conversationId != null)
            {
                var hashtags = await _appDbContext.ConversationHashtags
                    .Where(ch => ch.ConversationId == conversationId)
                    .Include(ch => ch.Hashtag)
                    .Select(ch => ch.Hashtag)
                    .ToListAsync();

                userProfileDto.Labels = hashtags
                    .Select(
                        h => new LabelData(
                            h.Hashtag,
                            h.HashTagColor.GetDisplayName(),
                            h.HashTagType.GetDisplayName()))
                    .ToList();
            }

            var userProfileDict =
                JsonConvert.DeserializeObject<Dictionary<string, JToken>>(
                    JsonConvert.SerializeObject(
                        userProfileDto));

            var userProfileCustomFields = userProfile.CustomFields == null
                ? await _appDbContext.UserProfileCustomFields
                    .Where(cf => cf.UserProfileId == userProfile.Id)
                    .ToListAsync()
                : userProfile.CustomFields.ToList();

            foreach (var userProfileCustomField in userProfileCustomFields)
            {
                userProfileDict[
                        fieldIdToFieldNameDict.GetValueOrDefault(
                            userProfileCustomField.CompanyDefinedFieldId,
                            userProfileCustomField.CompanyDefinedFieldId)]
                    = userProfileCustomField.Value == null
                        ? null
                        : JToken.FromObject(userProfileCustomField.Value);
                userProfileDict[userProfileCustomField.CompanyDefinedFieldId]
                    = userProfileCustomField.Value == null
                        ? null
                        : JToken.FromObject(userProfileCustomField.Value);
            }

            if (conversationId != null)
            {
                userProfileDict["conversation_id"] = JToken.FromObject(conversationId);
            }

            // Contact owner related custom fields
            if (userProfileDict.TryGetValue("ContactOwner", out var contactOwnerToken)
                && !string.IsNullOrWhiteSpace(contactOwnerToken.Value<string>()))
            {
                var contactOwnerId = contactOwnerToken.Value<string>();

                var contactOwner = await _appDbContext.UserRoleStaffs
                    .Where(x => x.IdentityId == contactOwnerId)
                    .Select(x => new
                    {
                        x.Position,
                        x.Message
                    })
                    .FirstOrDefaultAsync();

                if (contactOwner is not null)
                {
                    if (!string.IsNullOrWhiteSpace(contactOwner.Position))
                    {
                        userProfileDict["PositionOfContactOwner"] = JToken.FromObject(contactOwner.Position);
                    }

                    if (!string.IsNullOrWhiteSpace(contactOwner.Message))
                    {
                        userProfileDict["UniqueLink"] = JToken.FromObject(contactOwner.Message);
                        userProfileDict["MessageOfContactOwner"] = JToken.FromObject(contactOwner.Message);
                    }
                }
            }

            return userProfileDict;
        }
        catch (Exception e)
        {
            _logger.LogError(
                e,
                "Failed to get user profile dict. userProfileId=[{UserProfileId}], companyId=[{CompanyId}]",
                userProfile.Id,
                userProfile.CompanyId);

            throw;
        }
    }

    public async Task<Dictionary<string, JToken>> GetStaffDictAsync(
        string inputStaffId,
        string companyId)
    {
        Staff staff;
        if (int.TryParse(inputStaffId, out var staffId))
        {
            staff = await _appDbContext.UserRoleStaffs
                .Where(x => x.CompanyId == companyId && x.Id != 1)
                .Include(x => x.Identity)
                .OrderBy(x => x.Order)
                .ThenBy(x => x.Id)
                .Where(x => x.Id == staffId)
                .FirstOrDefaultAsync();
        }
        else
        {
            // StaffId is actually IdentityId
            staff = await _appDbContext.UserRoleStaffs
                .Where(x => x.CompanyId == companyId && x.Id != 1)
                .Include(x => x.Identity)
                .OrderBy(x => x.Order)
                .ThenBy(x => x.Id)
                .Where(x => x.IdentityId == inputStaffId)
                .FirstOrDefaultAsync();
        }

        if (staff == null)
        {
            return null;
        }

        return await GetStaffDictAsync(staff);
    }

    public async Task<Dictionary<string, JToken>> GetStaffDictAsync(Staff staff)
    {
        try
        {
            var staffDto = _mapper.Map<StaffDto>(staff);
            var staffDict = JsonConvert.DeserializeObject<Dictionary<string, JToken>>(
                JsonConvert.SerializeObject(staffDto));

            return await Task.FromResult(staffDict);
        }
        catch (Exception e)
        {
            _logger.LogError(
                e,
                "Failed to get staff dict. staffIdentityId=[{staffIdentityId}], companyId=[{CompanyId}]",
                staff.IdentityId,
                staff.CompanyId);

            throw;
        }
    }

    public async Task<List<CmsCompanyFlowHubData>> GetCompaniesFlowHubData()
    {
        string continuationToken = null;
        var flowHubConfigs = new List<FlowHubConfig>();

        do
        {
            var getEnrolledFlowHubConfigsOutputOutput =
                await _flowHubConfigsApi.FlowHubConfigsGetEnrolledFlowHubConfigsPostAsync(
                    getEnrolledFlowHubConfigsInput: new GetEnrolledFlowHubConfigsInput(
                        continuationToken,
                        100));

            flowHubConfigs.AddRange(getEnrolledFlowHubConfigsOutputOutput.Data.FlowHubConfigs);
            continuationToken = getEnrolledFlowHubConfigsOutputOutput.Data.ContinuationToken;
        }
        while (!string.IsNullOrEmpty(continuationToken));

        var cmsCompanyFlowHubDataList = await _appDbContext.CompanyCompanies
            .AsNoTracking()
            .Select(
                x => new CmsCompanyFlowHubData
                {
                    CompanyId = x.Id
                })
            .ToListAsync();

        ConcurrentBag<CmsCompanyFlowHubData> enrolledFlowHubCompanies = new ();

        // Get Enrolled Flow Builder Companies
        await Parallel.ForEachAsync(
            cmsCompanyFlowHubDataList,
            new ParallelOptions
            {
                MaxDegreeOfParallelism = 10
            },
            (company, cancellationToken) =>
            {
                var flowHubConfig = flowHubConfigs.Find(x => x.SleekflowCompanyId == company.CompanyId);

                if (flowHubConfig == null)
                {
                    return ValueTask.CompletedTask;
                }

                company.BaseMaximumNumOfWorkflows = flowHubConfig.UsageLimit?.MaximumNumOfWorkflows.GetValueOrDefault(0) ?? 0;
                company.BaseMaximumNumOfActiveWorkflows = flowHubConfig.UsageLimit?.MaximumNumOfActiveWorkflows.GetValueOrDefault(0) ?? 0;
                company.BaseMaximumNumOfNodesPerWorkflow = flowHubConfig.UsageLimit?.MaximumNumOfNodesPerWorkflow.GetValueOrDefault(0) ?? 0;
                company.BaseMaximumNumOfMonthlyWorkflowExecutions = flowHubConfig.UsageLimit?.MaximumNumOfMonthlyWorkflowExecutions.GetValueOrDefault(0) ?? 0;

                company.MaximumNumOfActiveWorkflowsOffset = flowHubConfig.UsageLimitOffset?.MaximumNumOfActiveWorkflowsOffset.GetValueOrDefault(0) ?? 0;
                company.MaximumNumOfNodesPerWorkflowOffset = flowHubConfig.UsageLimitOffset?.MaximumNumOfNodesPerWorkflowOffset.GetValueOrDefault(0) ?? 0;
                company.MaximumNumOfMonthlyWorkflowExecutionsOffset = flowHubConfig.UsageLimitOffset?.MaximumNumOfMonthlyWorkflowExecutionsOffset.GetValueOrDefault(0) ?? 0;

                enrolledFlowHubCompanies.Add(company);
                return ValueTask.CompletedTask;
            });

        await Parallel.ForEachAsync(
            enrolledFlowHubCompanies,
            new ParallelOptions
            {
                MaxDegreeOfParallelism = 10
            },
            async (company, cancellationToken) =>
            {
                var countWorkflowsOutputOutput = await _workflowsApi.WorkflowsCountWorkflowsPostAsync(
                    countWorkflowsInput: new CountWorkflowsInput(company.CompanyId, workflowType: "normal"),
                    cancellationToken: cancellationToken);

                if (countWorkflowsOutputOutput.Success)
                {
                    company.NumOfWorkflows = countWorkflowsOutputOutput.Data.NumOfWorkflows;
                    company.NumOfActiveWorkflows = countWorkflowsOutputOutput.Data.NumOfActiveWorkflows;
                }

                var utcNow = DateTimeOffset.UtcNow;
                var startDateOfCurrentMonth = new DateTimeOffset(
                    utcNow.Year,
                    utcNow.Month,
                    1,
                    0,
                    0,
                    0,
                    TimeSpan.Zero);
                var endDateOfCurrentMonth = startDateOfCurrentMonth.AddMonths(1);

                var getWorkflowExecutionStatisticsOutputOutput =
                    await _executionsApi.ExecutionsGetWorkflowExecutionStatisticsPostAsync(
                        getWorkflowExecutionStatisticsInput: new GetWorkflowExecutionStatisticsInput(
                            company.CompanyId,
                            new GetWorkflowExecutionStatisticsInputFilters(
                                fromDateTime: startDateOfCurrentMonth,
                                toDateTime: endDateOfCurrentMonth,
                                workflowType: "normal")),
                        cancellationToken: cancellationToken);

                if (getWorkflowExecutionStatisticsOutputOutput.Success)
                {
                    company.NumOfEnrolments = Convert.ToInt32(
                        getWorkflowExecutionStatisticsOutputOutput.Data.NumOfStartedWorkflows -
                        getWorkflowExecutionStatisticsOutputOutput.Data.NumOfFailedWorkflows);

                    company.EnrolmentPercentage = company.MaximumNumOfMonthlyWorkflowExecutions < 1
                        ? 0.00M
                        : company.NumOfEnrolments / Convert.ToDecimal(company.MaximumNumOfMonthlyWorkflowExecutions);
                }
            });

        return enrolledFlowHubCompanies.ToList();
    }

    public async Task<UsageLimit> GetUsageLimitForUpdateAsync(string companyId)
    {
        var usageLimit = new UsageLimit
        {
            MaximumNumOfWorkflows = 500,
            MaximumNumOfActiveWorkflows = await _featureQuantityService.GetFeatureQuantityAsync(companyId, FeatureId.FlowBuilderMaxActiveWorkflowCount),
            MaximumNumOfNodesPerWorkflow = await _featureQuantityService.GetFeatureQuantityAsync(companyId, FeatureId.FlowBuilderMaxNodesPerWorkflowCount),
            MaximumNumOfMonthlyWorkflowExecutions = await _featureQuantityService.GetFeatureQuantityAsync(companyId, FeatureId.FlowBuilderFlowEnrolment)
        };

        return usageLimit;
    }
}