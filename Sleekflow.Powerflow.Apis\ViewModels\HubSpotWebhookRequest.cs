﻿using Newtonsoft.Json;

namespace Sleekflow.Powerflow.Apis.ViewModels
{
    public class HubSpotWebhookRequest<T> where T : HubSpotObjectChangePropertiesBase
    {
        [JsonProperty("portalId")]
        public int PortalId { get; set; }

        [JsonProperty("objectType")]
        public string ObjectType { get; set; }

        [JsonProperty("objectTypeId")]
        public string ObjectTypeId { get; set; }

        [JsonProperty("objectId")]
        public long ObjectId { get; set; }

        [JsonProperty("properties")]
        public T Properties { get; set; }

        [JsonProperty("version")]
        public int Version { get; set; }

        [JsonProperty("secondaryIdentifier")]
        public object SecondaryIdentifier { get; set; }

        [JsonProperty("isDeleted")]
        public bool IsDeleted { get; set; }
    }

    public class Version
    {
        [JsonProperty("name")]
        public string Name { get; set; }

        [JsonProperty("value")]
        public string Value { get; set; }

        [JsonProperty("timestamp")]
        public long Timestamp { get; set; }

        [JsonProperty("sourceId")]
        public string SourceId { get; set; }

        [JsonProperty("source")]
        public string Source { get; set; }

        // [JsonProperty("sourceVid")]
        // public List<object> SourceVid { get; set; }
        [JsonProperty("requestId")]
        public string RequestId { get; set; }

        [JsonProperty("updatedByUserId")]
        public int UpdatedByUserId { get; set; }
    }

    public class PropertyChangePayload
    {
        [JsonProperty("versions")]
        public List<Version> Versions { get; set; }

        [JsonProperty("value")]
        public string Value { get; set; }

        [JsonProperty("timestamp")]
        public long Timestamp { get; set; }

        [JsonProperty("source")]
        public string Source { get; set; }

        [JsonProperty("sourceId")]
        public string SourceId { get; set; }

        [JsonProperty("updatedByUserId")]
        public long? UpdatedByUserId { get; set; }
    }

    public class ContactProperties : HubSpotObjectChangePropertiesBase
    {
        [JsonProperty("firstname")]
        public PropertyChangePayload FirstNamePayload { get; set; }

        [JsonProperty("lastname")]
        public PropertyChangePayload LastNamePayload { get; set; }

        [JsonProperty("email")]
        public PropertyChangePayload EmailPayload { get; set; }

        [JsonProperty("phone")]
        public PropertyChangePayload PhonePayload { get; set; }

        [JsonProperty("hubspot_owner_id")]
        public PropertyChangePayload HubspotOwnerIdPayload { get; set; }
    }

    public class WhatsApplicationTicketProperties : HubSpotObjectChangePropertiesBase
    {
        [JsonProperty("hs_pipeline_stage")]
        public PropertyChangePayload PipelineStage { get; set; }
    }

    public class CompanyProperties : HubSpotObjectChangePropertiesBase
    {
        [JsonProperty("sleekflow_company_id")]
        public PropertyChangePayload SleekflowCompanyIdPayload { get; set; }

        [JsonProperty("hubspot_owner_id")]
        public PropertyChangePayload HubspotCompanyOwnerIdPayload { get; set; }

        [JsonProperty("activation_owner")]
        public PropertyChangePayload HubspotActivationOwnerIdPayload { get; set; }

        [JsonProperty("initial_idea_customer_profile_tier")]
        public PropertyChangePayload InitialIdeaCustomerProfileTier { get; set; }
    }

    public class CompanyTierHubSpotWebhook
    {
        [JsonProperty("sleekflow_company_id")]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("initial_idea_customer_profile_tier")]
        public string InitialIdeaCustomerProfileTier { get; set; }
    }

    public class CompanyChurnReasonProperties : HubSpotObjectChangePropertiesBase
    {
        [JsonProperty("sleekflow_company_id")]
        public PropertyChangePayload SleekflowCompanyId { get; set; }

        [JsonProperty("churn_reasons")]
        public PropertyChangePayload ChurnReasons { get; set; }
    }

    public class CompanyIndustryHubSpotWebhook
    {
        [JsonProperty("sleekflow_company_id")]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("industry")]
        public string Industry { get; set; }
    }

    public abstract class HubSpotObjectChangePropertiesBase
    {
    }

    public class PartnerStackCustomerKeyHubSpotWebhook
    {
        [JsonProperty("email")]
        public string Email { get; set; }

        [JsonProperty("customer_key")]
        public string CustomerKey { get; set; }
    }

    public class PartnerStackPartnerKeyHubSpotWebhook
    {
        [JsonProperty("email")]
        public string Email { get; set; }

        [JsonProperty("partner_key")]
        public string PartnerKey { get; set; }
    }
}