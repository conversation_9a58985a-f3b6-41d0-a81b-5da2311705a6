using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Travis_backend.CommonDomain.Repositories;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.Constants;
using Travis_backend.ConversationDomain.Models;
using Travis_backend.ConversationDomain.ViewModels;
using Travis_backend.Database;

namespace Travis_backend.ConversationDomain.ConversationAccessControl;

public interface IAccessControlAggregationService
{
    public Task<StaffAccessControlAggregate> GetStaffAccessControlAggregateAsync(long staffId, string companyId);

    public Task<StaffAccessControlAggregate> GetStaffAccessControlAggregateAsync(Staff staff);

    public Task<Conversation> GetAggregatedConversationAsync(string conversationId);
}

public class AccessControlAggregationService : IAccessControlAggregationService
{
    private readonly ApplicationDbContext _appDbContext;

    public AccessControlAggregationService(
            ApplicationDbContext appDbContext)
        {
            _appDbContext = appDbContext;
        }

    public async Task<StaffAccessControlAggregate> GetStaffAccessControlAggregateAsync(long staffId, string companyId)
    {
        var staff = await _appDbContext.UserRoleStaffs
            .AsNoTracking()
            .FirstOrDefaultAsync(staff => staff.Id == staffId && staff.CompanyId == companyId);

        if (staff == null)
        {
            return null;
        }

        var rolePermission = await GetRolePermissionAsync(staff);
        var associatedTeams = await GetAssociatedTeamsAsync(staffId);

        return new StaffAccessControlAggregate
        {
            StaffId = staff.Id,
            Status = staff.Status,
            RoleType = staff.RoleType,
            CompanyId = staff.CompanyId,
            RolePermission = rolePermission,
            AssociatedTeams = associatedTeams
        };
    }

    public async Task<StaffAccessControlAggregate> GetStaffAccessControlAggregateAsync(Staff staff)
    {
        var rolePermission = await GetRolePermissionAsync(staff);
        var associatedTeams = await GetAssociatedTeamsAsync(staff.Id);

        return new StaffAccessControlAggregate
        {
            StaffId = staff.Id,
            Status = staff.Status,
            RoleType = staff.RoleType,
            CompanyId = staff.CompanyId,
            RolePermission = rolePermission,
            AssociatedTeams = associatedTeams
        };
    }

    public async Task<Conversation> GetAggregatedConversationAsync(string conversationId)
    {
        var conversation = await _appDbContext.Conversations
            .AsNoTracking()
            .Where(x => x.Id == conversationId)
            .FirstOrDefaultAsync();

        // Get the conversation messages that has mentioned staff only to improve the performance
        conversation.ChatHistory = await _appDbContext.ConversationMessages
            .AsNoTracking()
            .Where(x => x.ConversationId == conversationId
                        && x.MessageAssigneeId != null
                        && x.UpdatedAt > DateTime.UtcNow.AddDays(-2)
                        && x.Channel == ChannelTypes.Note)
            .ToListAsync();

        conversation.AdditionalAssignees = await _appDbContext.ConversationAdditionalAssignees
            .AsNoTracking()
            .Where(x => x.ConversationId == conversationId)
            .ToListAsync();

        return conversation;
    }

    private async Task<RolePermission> GetRolePermissionAsync(Staff staff)
    {
        return await _appDbContext.CompanyRolePermissions
            .AsNoTracking()
            .FirstOrDefaultAsync(rolePermission =>
                rolePermission.StaffUserRole == staff.RoleType &&
                rolePermission.CompanyId == staff.CompanyId);
    }

    private async Task<List<TeamAccessControlAggregate>> GetAssociatedTeamsAsync(long staffId)
    {
        var teams = await _appDbContext.CompanyTeamMembers
            .AsNoTracking()
            .Where(team => team.StaffId == staffId)
            .ToListAsync();

        var teamIds = teams.Select(tm => tm.CompanyTeamId).Distinct().ToList();

        var allTeamMembers = await _appDbContext.CompanyTeamMembers
            .AsNoTracking()
            .Where(tm => teamIds.Contains(tm.CompanyTeamId))
            .ToListAsync();

        return teamIds.Select(teamId => new TeamAccessControlAggregate
        {
            Id = teamId,
            TeamMemberStaffIds = allTeamMembers
                .Where(tm => tm.CompanyTeamId == teamId)
                .Select(tm => tm.StaffId)
                .ToList()
        }).ToList();
    }
}