using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Sleekflow.Apis.TicketingHub.Api;
using Sleekflow.Apis.TicketingHub.Model;
using Travis_backend.AccountAuthenticationDomain.Models;
using Travis_backend.Cache;
using Travis_backend.Cache.Models.CacheKeyPatterns;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.Constants;
using Travis_backend.ContactDomain.Services;
using Travis_backend.ConversationDomain.Models;
using Travis_backend.ConversationDomain.Services;
using Travis_backend.ConversationServices;
using Travis_backend.Database;
using Travis_backend.Enums;
using Travis_backend.Extensions;
using Travis_backend.MessageDomain.Models;
using Travis_backend.TicketingHubDomain.Models.**********************;
using Travis_backend.TicketingHubDomain.ViewModels;

namespace Travis_backend.TicketingHubDomain.Services;

public interface ITicketingHubService
{
    #region Tickets

    public Task<CreateTicketOutputOutput> CreateTicketAsync(
        string companyId,
        string userProfileId,
        string title,
        Channel channel,
        string priorityId,
        long assigneeId,
        Dictionary<string, object> metadata,
        string description,
        List<MediaDto> medias,
        string typeId,
        string url,
        DateTimeOffset? dueDate,
        long staffId,
        List<long> associatedMessageIds,
        TicketUser ticketUser);

    Task<GetTicketOutputOutput> GetTicketAsync(
        string id,
        string companyId,
        TicketUser ticketUser);

    Task<GetSchemafulTicketsOutputOutput> GetTicketsAsync(
        string companyId,
        List<FilterGroup> filterGroups,
        Sort sort,
        int limit,
        string continuationToken,
        TicketUser ticketUser);

    Task<GetTicketIdsOutputOutput> GetTicketIdsAsync(
        string companyId,
        List<FilterGroup> filterGroups,
        Sort sort,
        TicketUser ticketUser);

    Task<GetTicketCountOutputOutput> GetTicketCountAsync(
        string companyId,
        List<FilterGroup> filterGroups,
        List<GroupBy> groupBys,
        TicketUser ticketUser);

    Task<UpdateTicketOutputOutput> UpdateTicketAsync(
        string id,
        string companyId,
        Dictionary<string, object> updatedProperties,
        TicketUser ticketUser);

    Task<DeleteTicketOutputOutput> DeleteTicketAsync(
        string id,
        string companyId,
        TicketUser ticketUser);

    Task<DeleteTicketsOutputOutput> DeleteTicketsAsync(
        List<string> ids,
        string companyId,
        TicketUser ticketUser);

    #endregion

    #region Ticket Statuses

    public Task<GetTicketStatusesOutputOutput> GetTicketStatusesAsync(
        string companyId,
        TicketUser ticketUser,
        int limit = 1000);

    #endregion)

    #region Ticket Priorities

    public Task<GetTicketPrioritiesOutputOutput> GetTicketPrioritiesAsync(
        string companyId,
        TicketUser ticketUser = null,
        int limit = 1000);

    #endregion

    #region Ticket Types

    public Task<GetTicketTypesOutputOutput> GetTicketTypesAsync(
        string companyId,
        List<FilterGroup> filterGroups,
        Sort sort,
        int limit,
        string continuationToken,
        TicketUser ticketUser);

    Task<GetTicketTypeOutputOutput> GetTicketTypeAsync(
        string id,
        string companyId,
        TicketUser ticketUser);

    Task<CreateTicketTypeOutputOutput> CreateTicketTypeAsync(
        string companyId,
        string label,
        TicketUser ticketUser);

    Task<UpdateTicketTypeOutputOutput> UpdateTicketTypeAsync(
        string id,
        string companyId,
        string label,
        TicketUser ticketUser);

    Task<UpdateTicketTypeOrderOutputOutput> UpdateTicketTypeOrderAsync(
        string companyId,
        List<TicketTypeArrangement> ticketTypes,
        TicketUser ticketUser);

    Task<DeleteTicketTypeOutputOutput> DeleteTicketTypeAsync(
        string id,
        string companyId,
        TicketUser ticketUser);

    #endregion

    #region Ticket Activity

    public Task<GetSchemafulTicketActivitiesOutputOutput> GetTicketActivitiesAsync(
        string companyId,
        List<FilterGroup> filterGroups,
        Sort sort,
        int limit,
        string continuationToken);

    #endregion

    #region Ticket Comment

    Task<GetTicketCommentsOutputOutput> GetTicketCommentsAsync(
        string companyId,
        List<FilterGroup> filterGroups,
        Sort sort,
        int limit,
        string continuationToken);

    Task<CreateTicketCommentOutputOutput> CreateTicketCommentAsync(
        string sleekflowCompanyId,
        string ticketId,
        string content,
        List<TicketCommentMediaDto> medias,
        long staffId);

    Task<DeleteTicketCommentOutputOutput> DeleteTicketCommentAsync(
        string id,
        string sleekflowCompanyId,
        string ticketId,
        long staffId);

    #endregion

    #region Ticket Conversation Indicator

    #endregion

    #region Ticket User

    Task<TicketUser> GetTicketUserAsync(Staff staff);

    #endregion

    #region Managements

    Task<GetTicketMetricsOutputOutput> GetTicketMetricsAsync(
        DateTimeOffset startDate,
        DateTimeOffset endDate);

    Task<GetTicketCompanyConfigsOutputOutput> GetTicketCompanyConfigsAsync(
        List<FilterGroup> filterGroups,
        Sort sort,
        int limit,
        string continuationToken);

    Task<GetTicketCompanyConfigCountOutputOutput> GetTicketCompanyConfigsAsync(
        List<FilterGroup> filterGroups,
        List<GroupBy> groupBys);

    #endregion

    Task CreateTicketConversationIndicatorMessage(
        TicketDto ticket,
        string operation,
        TicketUser ticketUser); // TODO: Also adding the intended status indicator and other kinds of stuff
}

public class TicketingHubService : ITicketingHubService
{
    private readonly ILogger<TicketingHubService> _logger;
    private readonly ApplicationDbContext _appDbContext;
    private readonly UserManager<ApplicationUser> _userManager;
    private readonly ITicketsApi _ticketsApi;
    private readonly ITicketPrioritiesApi _ticketPrioritiesApi;
    private readonly ITicketStatusesApi _ticketStatusesApi;
    private readonly ITicketTypesApi _ticketTypesApi;
    private readonly ITicketActivitiesApi _ticketActivitiesApi;
    private readonly ICacheManagerService _cacheManagerService;
    private readonly IUserProfileService _userProfileService;
    private readonly IConversationService _conversationService;
    private readonly IConversationMessageService _conversationMessageService;
    private readonly IConversationAssigneeService _conversationAssigneeService;
    private readonly ICoreService _coreService;
    private readonly ILockService _lockService;
    private readonly ITicketCommentsApi _ticketCommentsApi;
    private readonly IManagementsApi _managementsApi;

    public TicketingHubService(
        ILogger<TicketingHubService> logger,
        ApplicationDbContext appDbContext,
        UserManager<ApplicationUser> userManager,
        ITicketsApi ticketsApi,
        ITicketPrioritiesApi ticketPrioritiesApi,
        ITicketStatusesApi ticketStatusesApi,
        ITicketTypesApi ticketTypesApi,
        ITicketActivitiesApi ticketActivitiesApi,
        ICacheManagerService cacheManagerService,
        IUserProfileService userProfileService,
        IConversationService conversationService,
        IConversationMessageService conversationMessageService,
        IConversationAssigneeService conversationAssigneeService,
        ICoreService coreService,
        ILockService lockService,
        ITicketCommentsApi ticketCommentsApi,
        IManagementsApi managementsApi)
    {
        _logger = logger;
        _appDbContext = appDbContext;
        _userManager = userManager;
        _ticketsApi = ticketsApi;
        _ticketPrioritiesApi = ticketPrioritiesApi;
        _ticketStatusesApi = ticketStatusesApi;
        _ticketTypesApi = ticketTypesApi;
        _ticketActivitiesApi = ticketActivitiesApi;
        _cacheManagerService = cacheManagerService;
        _userProfileService = userProfileService;
        _conversationService = conversationService;
        _conversationMessageService = conversationMessageService;
        _conversationAssigneeService = conversationAssigneeService;
        _coreService = coreService;
        _lockService = lockService;
        _ticketCommentsApi = ticketCommentsApi;
        _managementsApi = managementsApi;
    }

    #region Tickets

    public async Task<CreateTicketOutputOutput> CreateTicketAsync(
        string companyId,
        string userProfileId,
        string title,
        Channel channel,
        string priorityId,
        long assigneeId,
        Dictionary<string, object> metadata,
        string description,
        List<MediaDto> medias,
        string typeId,
        string url,
        DateTimeOffset? dueDate,
        long staffId,
        List<long> associatedMessageIds,
        TicketUser ticketUser)
    {
        var staffTeamIds = await _appDbContext.CompanyTeamMembers
            .AsNoTracking()
            .Where(y => y.StaffId == staffId)
            .Select(y => y.CompanyTeamId.ToString())
            .ToListAsync();

        var assigneeTeamIds = await _appDbContext.CompanyTeamMembers
            .AsNoTracking()
            .Where(y => y.StaffId == assigneeId)
            .Select(y => y.CompanyTeamId.ToString())
            .ToListAsync();

        var createdTicket = await _ticketsApi.TicketsCreateTicketPostAsync(
            createTicketInput: new CreateTicketInput(
                companyId,
                userProfileId,
                title,
                channel,
                priorityId,
                assigneeId.ToString(),
                assigneeTeamIds,
                metadata ?? new Dictionary<string, object>(),
                description,
                medias,
                typeId,
                url,
                dueDate,
                staffId.ToString(),
                staffTeamIds,
                associatedMessageIds,
                ticketUser));

        if (createdTicket.Success)
        {
            // If the ticket assignee is not already assigned to the ticket and is not listed as a collaborator, then add the ticket assignee as a collaborator.
            if (long.TryParse(createdTicket.Data.Ticket.AssigneeId, out assigneeId))
            {
                userProfileId = createdTicket.Data.Ticket.SleekflowUserProfileId;
                var conversation = await _userProfileService.GetConversationByUserProfileId(companyId, userProfileId);

                if (conversation.AssigneeId != assigneeId &&
                    conversation.AdditionalAssignees.All(x => x.AssigneeId != assigneeId))
                {
                    await _conversationAssigneeService.AddAdditionalAssignees(
                        conversation,
                        new List<long>()
                        {
                            assigneeId
                        });
                }

                _logger.LogInformation(
                    "Add collaborator ticket id: {Id}, collaborator: {Collaborator} by staff: {StaffId}",
                    createdTicket.Data.Ticket.Id,
                    assigneeId,
                    staffId);
            }
        }

        return createdTicket;
    }

    public async Task<GetTicketOutputOutput> GetTicketAsync(
        string id,
        string companyId,
        TicketUser ticketUser)
    {
        return await _ticketsApi.TicketsGetTicketPostAsync(
            getTicketInput:
            new GetTicketInput(
                id,
                companyId,
                ticketUser));
    }

    public async Task<GetSchemafulTicketsOutputOutput> GetTicketsAsync(
        string companyId,
        List<FilterGroup> filterGroups,
        Sort sort,
        int limit,
        string continuationToken,
        TicketUser ticketUser)
    {
        var result = await _ticketsApi.TicketsGetTicketsPostAsync(
            getTicketsInput: new GetTicketsInput(
                companyId,
                filterGroups,
                sort,
                limit,
                continuationToken,
                ticketUser));

        if (!result.Success)
        {
            return new GetSchemafulTicketsOutputOutput(
                result.Success,
                null,
                result.Message,
                result.DateTime,
                result.HttpStatusCode,
                result.ErrorCode,
                result.ErrorContext,
                result.RequestId);
        }

        if (!result.Data.Tickets.Any())
        {
            return new GetSchemafulTicketsOutputOutput(
                result.Success,
                new GetSchemafulTicketsOutput(new List<GetSchemafulTicketDto>(), 0, null),
                result.Message,
                result.DateTime,
                result.HttpStatusCode,
                result.ErrorCode,
                result.ErrorContext,
                result.RequestId);
        }

        var userProfileIds = result.Data.Tickets.Select(x => x.SleekflowUserProfileId).ToList();

        var userProfilesWithConversations = await _appDbContext.UserProfiles
            .Where(
                x => x.CompanyId == companyId &&
                     userProfileIds.Contains(x.Id) &&
                     x.ActiveStatus == ActiveStatus.Active)
            .Include(x => x.Conversation)
            .Select(
                x => new
                {
                    UserProfileId = x.Id,
                    ConversationId = x.Conversation.Id,
                    FirstName = x.FirstName,
                    LastName = x.LastName
                })
            .ToListAsync();

        // Create a dictionary for quick lookup
        var userProfileMap = userProfilesWithConversations
            .ToDictionary(
                x =>
                    x.UserProfileId,
                x => x);

        var tickets = result.Data.Tickets
            .GroupJoin(
                userProfileMap,
                ticket => ticket.SleekflowUserProfileId,
                user => user.Value.UserProfileId,
                (ticket, tickUsers) => new
                {
                    Ticket = ticket, User = tickUsers.FirstOrDefault()
                })
            .Select(
                x => new GetSchemafulTicketDto(
                    x.Ticket.Id,
                    x.Ticket.SleekflowCompanyId,
                    x.Ticket.ExternalId,
                    x.Ticket.Title,
                    x.Ticket.Description,
                    x.Ticket.StatusId,
                    x.Ticket.PriorityId,
                    x.Ticket.TypeId,
                    x.Ticket.Channel,
                    x.Ticket.Medias,
                    x.Ticket.DueDate,
                    x.Ticket.SleekflowUserProfileId,
                    x.Ticket.AssigneeId,
                    x.Ticket.AssignedTeamIds,
                    x.Ticket.AssociatedMessageIds,
                    x.Ticket.Url,
                    x.Ticket.ResolutionTime,
                    x.Ticket.ResolvedAt,
                    x.Ticket.CreatedAt,
                    x.User.Value?.ConversationId,
                    new TicketUserProfileViewModel(
                        x.User.Value?.UserProfileId,
                        x.User.Value?.FirstName,
                        x.User.Value?.LastName))).ToList();

        return new GetSchemafulTicketsOutputOutput(
            result.Success,
            new GetSchemafulTicketsOutput(
                tickets,
                result.Data.Count,
                result.Data.ContinuationToken),
            result.Message,
            result.DateTime,
            result.HttpStatusCode,
            result.ErrorCode,
            result.ErrorContext,
            result.RequestId);
    }

    public async Task<GetTicketIdsOutputOutput> GetTicketIdsAsync(
        string companyId,
        List<FilterGroup> filterGroups,
        Sort sort,
        TicketUser ticketUser)
    {
        return await _ticketsApi.TicketsGetTicketIdsPostAsync(
            getTicketIdsInput: new GetTicketIdsInput(companyId, filterGroups, sort, ticketUser));
    }

    public async Task<GetTicketCountOutputOutput> GetTicketCountAsync(
        string companyId,
        List<FilterGroup> filterGroups,
        List<GroupBy> groupBys,
        TicketUser ticketUser)
    {
        return await _ticketsApi.TicketsGetTicketCountPostAsync(
            getTicketCountInput: new GetTicketCountInput(
                companyId,
                filterGroups,
                groupBys,
                ticketUser));
    }

    public async Task<UpdateTicketOutputOutput> UpdateTicketAsync(
        string id,
        string companyId,
        Dictionary<string, object> updatedProperties,
        TicketUser ticketUser)
    {
        var updatedTicket = await _ticketsApi.TicketsUpdateTicketPostAsync(
            updateTicketInput: new UpdateTicketInput(
                id,
                companyId,
                ticketUser.SleekflowUserId,
                ticketUser.SleekflowTeamIds,
                updatedProperties,
                ticketUser));

        if (!updatedTicket.Success)
        {
            return updatedTicket;
        }

        if (updatedProperties.TryGetValue("assignee_id", out var property))
        {
            if (long.TryParse(property as string ?? string.Empty, out var assigneeId))
            {
                var userProfileId = updatedTicket.Data.Ticket.SleekflowUserProfileId;
                var conversation = await _userProfileService.GetConversationByUserProfileId(companyId, userProfileId);

                // If the ticket assignee is not already assigned to the ticket and is not listed as a collaborator, then add the ticket assignee as a collaborator.
                if (conversation.AssigneeId != assigneeId &&
                    conversation.AdditionalAssignees.All(x => x.AssigneeId != assigneeId))
                {
                    await _conversationAssigneeService.AddAdditionalAssignees(
                        conversation,
                        new List<long>()
                        {
                            assigneeId
                        });
                }

                _logger.LogInformation(
                    "Add collaborator ticket id: {Id}, collaborator: {Collaborator} by staff: {StaffId}",
                    updatedTicket.Data.Ticket.Id,
                    assigneeId,
                    ticketUser.SleekflowUserId);
            }
        }

        return updatedTicket;
    }

    public async Task<DeleteTicketOutputOutput> DeleteTicketAsync(
        string id,
        string companyId,
        TicketUser ticketUser)
    {
        return await _ticketsApi.TicketsDeleteTicketPostAsync(
            deleteTicketInput: new DeleteTicketInput(
                id,
                companyId,
                ticketUser.SleekflowUserId,
                ticketUser.SleekflowTeamIds,
                ticketUser));
    }

    public async Task<DeleteTicketsOutputOutput> DeleteTicketsAsync(
        List<string> ids,
        string companyId,
        TicketUser ticketUser)
    {
        return await _ticketsApi.TicketsDeleteTicketsPostAsync(
            deleteTicketsInput: new DeleteTicketsInput(
                ids,
                companyId,
                ticketUser.SleekflowUserId,
                ticketUser.SleekflowTeamIds,
                ticketUser));
    }

    #endregion

    #region Ticket Statuses

    public async Task<GetTicketStatusesOutputOutput> GetTicketStatusesAsync(
        string companyId,
        TicketUser ticketUser,
        int limit = 1000)
    {
        return await _ticketStatusesApi.TicketStatusesGetTicketStatusesPostAsync(
            getTicketStatusesInput: new GetTicketStatusesInput(
                companyId,
                new List<FilterGroup>(),
                limit: limit));
    }

    #endregion

    #region Ticket Priorities

    public async Task<GetTicketPrioritiesOutputOutput> GetTicketPrioritiesAsync(
        string companyId,
        TicketUser ticketUser,
        int limit = 1000)
    {
        return await _ticketPrioritiesApi.TicketPrioritiesGetTicketPrioritiesPostAsync(
            getTicketPrioritiesInput: new GetTicketPrioritiesInput(companyId, limit: limit));
    }

    #endregion

    #region Ticket Types

    public async Task<GetTicketTypesOutputOutput> GetTicketTypesAsync(
        string companyId,
        List<FilterGroup> filterGroups,
        Sort sort,
        int limit,
        string continuationToken,
        TicketUser ticketUser)
    {
        return await _ticketTypesApi.TicketTypesGetTicketTypesPostAsync(
            getTicketTypesInput: new GetTicketTypesInput(
                companyId,
                filterGroups,
                sort,
                limit: limit,
                continuationToken,
                ticketUser));
    }

    public async Task<GetTicketTypeOutputOutput> GetTicketTypeAsync(
        string id,
        string companyId,
        TicketUser ticketUser)
    {
        return await _ticketTypesApi.TicketTypesGetTicketTypePostAsync(
            getTicketTypeInput: new GetTicketTypeInput(
                id,
                companyId,
                ticketUser));
    }

    public async Task<CreateTicketTypeOutputOutput> CreateTicketTypeAsync(
        string companyId,
        string label,
        TicketUser ticketUser)
    {
        return await _ticketTypesApi.TicketTypesCreateTicketTypePostAsync(
            createTicketTypeInput: new CreateTicketTypeInput(
                companyId,
                label,
                ticketUser.SleekflowUserIdentityId,
                ticketUser.SleekflowTeamIds,
                ticketUser));
    }

    public async Task<UpdateTicketTypeOutputOutput> UpdateTicketTypeAsync(
        string id,
        string companyId,
        string label,
        TicketUser ticketUser)
    {
        return await _ticketTypesApi.TicketTypesUpdateTicketTypePostAsync(
            updateTicketTypeInput: new UpdateTicketTypeInput(
                id,
                companyId,
                label,
                ticketUser.SleekflowUserIdentityId,
                ticketUser.SleekflowTeamIds,
                ticketUser));
    }

    public async Task<UpdateTicketTypeOrderOutputOutput> UpdateTicketTypeOrderAsync(
        string companyId,
        List<TicketTypeArrangement> ticketTypes,
        TicketUser ticketUser)
    {
        return await _ticketTypesApi.TicketTypesUpdateTicketTypeOrderPostAsync(
            updateTicketTypeOrderInput: new UpdateTicketTypeOrderInput(
                companyId,
                ticketTypes,
                ticketUser.SleekflowUserIdentityId,
                ticketUser.SleekflowTeamIds,
                ticketUser));
    }

    public async Task<DeleteTicketTypeOutputOutput> DeleteTicketTypeAsync(
        string id,
        string companyId,
        TicketUser ticketUser)
    {
        return await _ticketTypesApi.TicketTypesDeleteTicketTypePostAsync(
            deleteTicketTypeInput: new DeleteTicketTypeInput(
                id,
                companyId,
                ticketUser.SleekflowUserIdentityId,
                ticketUser.SleekflowTeamIds,
                ticketUser));
    }

    #endregion

    #region Ticket Activities

    public async Task<GetSchemafulTicketActivitiesOutputOutput> GetTicketActivitiesAsync(
        string companyId,
        List<FilterGroup> filterGroups,
        Sort sort,
        int limit,
        string continuationToken)
    {
        var ticketActivitiesOutputOutput = await _ticketActivitiesApi.TicketActivitiesGetTicketActivitiesPostAsync(
            getTicketActivitiesInput: new GetTicketActivitiesInput(
                companyId,
                filterGroups,
                sort,
                limit,
                continuationToken));

        if (ticketActivitiesOutputOutput.Success && ticketActivitiesOutputOutput.Data.TicketActivities.Any())
        {
            var companyStaffIds = ticketActivitiesOutputOutput.Data.TicketActivities
                .Select(x => Convert.ToInt64(x.CreatedBy.SleekflowStaffId))
                .ToList();

            var companyAssigneeStaffIds = ticketActivitiesOutputOutput.Data.TicketActivities
                .SelectMany(ticket => ticket.Items)
                .Where(item => item.Field == "assignee_id")
                .SelectMany(
                    item => new[]
                    {
                        ((JObject) item.Before)["id"],
                        ((JObject) item.After)["id"]
                    })
                .Where(id => id != null)
                .Select(Convert.ToInt64)
                .ToList();

            companyStaffIds.AddRange(companyAssigneeStaffIds);

            var companyUserRoleStaffs = await _appDbContext.UserRoleStaffs
                .Where(x => companyStaffIds.Contains(x.Id) && x.CompanyId == companyId)
                .Include(x => x.Identity)
                .Select(
                    x => new TicketActivityStaffViewModel(
                        x.Id,
                        x.Identity.FirstName,
                        x.Identity.LastName,
                        x.Identity.DisplayName,
                        x.Identity.Email)).ToListAsync();

            var companyUserRoleStaffDictionary = companyUserRoleStaffs.ToDictionary(user => user.Id, user => user);

            var ticketActivities = ticketActivitiesOutputOutput.Data.TicketActivities
                .Select(
                    ticketActivities => new GetSchemafulTicketActivitiesDto(
                        ticketActivities.Id,
                        ticketActivities.SleekflowCompanyId,
                        ticketActivities.TicketId,
                        ticketActivities.Items
                            .Select(
                                item => new TicketActivityItem(
                                    item.Action,
                                    item.Field,
                                    item.Field == "assignee_id"
                                        ? UpdateAssigneeDetails(item.Before, companyUserRoleStaffDictionary)
                                        : item.Before,
                                    item.Field == "assignee_id"
                                        ? UpdateAssigneeDetails(item.After, companyUserRoleStaffDictionary)
                                        : item.After))
                            .ToList(),
                        ticketActivities.Metadata,
                        ticketActivities.CreatedBy,
                        ticketActivities.CreatedAt,
                        companyUserRoleStaffDictionary.Where(
                                x => x.Key == Convert.ToInt64(ticketActivities.CreatedBy.SleekflowStaffId))
                            .Select(
                                y => new TicketActivityStaffViewModel(
                                    y.Value.Id,
                                    y.Value.FirstName,
                                    y.Value.LastName,
                                    y.Value.DisplayName,
                                    y.Value.Email)).FirstOrDefault()))
                .ToList();

            return new GetSchemafulTicketActivitiesOutputOutput(
                ticketActivitiesOutputOutput.Success,
                new GetSchemafulTicketActivitiesOutput(
                    ticketActivities,
                    ticketActivitiesOutputOutput.Data.Count,
                    ticketActivitiesOutputOutput.Data.ContinuationToken),
                ticketActivitiesOutputOutput.Message,
                ticketActivitiesOutputOutput.DateTime,
                ticketActivitiesOutputOutput.HttpStatusCode,
                ticketActivitiesOutputOutput.ErrorCode,
                ticketActivitiesOutputOutput.ErrorContext,
                ticketActivitiesOutputOutput.RequestId);
        }

        return new GetSchemafulTicketActivitiesOutputOutput(
            ticketActivitiesOutputOutput.Success,
            null,
            ticketActivitiesOutputOutput.Message,
            ticketActivitiesOutputOutput.DateTime,
            ticketActivitiesOutputOutput.HttpStatusCode,
            ticketActivitiesOutputOutput.ErrorCode,
            ticketActivitiesOutputOutput.ErrorContext,
            ticketActivitiesOutputOutput.RequestId);
    }

    public async Task<TicketUser> GetTicketUserAsync(Staff staff)
    {
        var cachePattern = new TicketingHubTicketUserPattern(staff.IdentityId);

        var data = await _cacheManagerService.GetCacheAsync(cachePattern);

        if (!string.IsNullOrEmpty(data))
        {
            return JsonConvert.DeserializeObject<TicketUser>(data);
        }

        var staffTeamIds = await _appDbContext.CompanyTeamMembers
            .AsNoTracking()
            .Where(y => y.StaffId == staff.Id)
            .Select(y => y.CompanyTeamId.ToString())
            .ToListAsync();

        var role = staff.RoleType switch
        {
            StaffUserRole.Staff => "Staff",
            StaffUserRole.TeamAdmin => "TeamAdmin",
            StaffUserRole.Admin => "Admin",
            _ => "Staff"
        };

        var result = new TicketUser(staff.Id.ToString(), staff.IdentityId, role, staffTeamIds);

        await _cacheManagerService.SaveCacheAsync(cachePattern, JsonConvert.SerializeObject(result));

        return result;
    }

    private object UpdateAssigneeDetails(
        object beforeOrAfter,
        Dictionary<long, TicketActivityStaffViewModel> userDictionary)
    {
        try
        {
            if (beforeOrAfter is JObject jObject && jObject["id"] != null)
            {
                var id = jObject["id"].ToString();

                if (userDictionary.TryGetValue(Convert.ToInt64(id), out var user))
                {
                    return new
                    {
                        id = user.Id,
                        first_name = user.FirstName,
                        last_name = user.LastName,
                        display_name = user.DisplayName,
                        email = user.Email
                    };
                }
            }

            return beforeOrAfter;
        }
        catch (Exception e)
        {
            _logger.LogError(
                "Error during mapping assignee for ticket activity, ex: {e}",
                e);

            return beforeOrAfter;
        }
    }

    #endregion

    #region Ticket Comment

    public async Task<GetTicketCommentsOutputOutput> GetTicketCommentsAsync(
        string companyId,
        List<FilterGroup> filterGroups,
        Sort sort,
        int limit,
        string continuationToken)
    {
        return await _ticketCommentsApi.TicketCommentsGetTicketCommentsPostAsync(
            getTicketCommentsInput: new GetTicketCommentsInput(
                companyId,
                filterGroups,
                sort,
                limit,
                continuationToken));
    }

    public async Task<CreateTicketCommentOutputOutput> CreateTicketCommentAsync(
        string sleekflowCompanyId,
        string ticketId,
        string content,
        List<TicketCommentMediaDto> medias,
        long staffId)
    {
        var staffTeamIds = await _appDbContext.CompanyTeamMembers
            .AsNoTracking()
            .Where(y => y.StaffId == staffId)
            .Select(y => y.CompanyTeamId.ToString())
            .ToListAsync();

        return await _ticketCommentsApi.TicketCommentsCreateTicketCommentPostAsync(
            createTicketCommentInput: new CreateTicketCommentInput(
                sleekflowCompanyId,
                ticketId,
                content,
                medias,
                staffId.ToString(),
                staffTeamIds));
    }

    public async Task<DeleteTicketCommentOutputOutput> DeleteTicketCommentAsync(
        string id,
        string sleekflowCompanyId,
        string ticketId,
        long staffId)
    {
        var staffTeamIds = await _appDbContext.CompanyTeamMembers
            .AsNoTracking()
            .Where(y => y.StaffId == staffId)
            .Select(y => y.CompanyTeamId.ToString())
            .ToListAsync();

        return await _ticketCommentsApi.TicketCommentsDeleteTicketCommentPostAsync(
            deleteTicketCommentInput: new DeleteTicketCommentInput(
                id,
                sleekflowCompanyId,
                ticketId,
                staffId.ToString(),
                staffTeamIds));
    }

    #endregion

    #region Ticket Conversation Indicator

    public async Task CreateTicketConversationIndicatorMessage(
        TicketDto ticket,
        string operation,
        TicketUser ticketUser)
    {
        var key = new TicketConversationIndicatorMessageCacheKeyPattern(ticket.Id).GenerateKeyPattern();
        ILockService.Lock myLock;

        while (true)
        {
            myLock = await _lockService.AcquireLockAsync(key, TimeSpan.FromSeconds(10));


            if (myLock == null)
            {
                await Task.Delay(TimeSpan.FromSeconds(10));
            }
            else
            {
                break;
            }
        }

        var conversation = await GetConversation(ticket.SleekflowUserProfileId);
        var conversationIndicator = await CreateTicketConversationIndicator(ticket, operation, ticketUser);

        var conversationMessage = new ConversationMessage
        {
            Channel = ticket.Channel.ChannelType,
            ChannelIdentityId = ticket.Channel.ChannelIdentityId,
            ConversationId = conversation.Id,
            CompanyId = conversation.CompanyId,
            SenderId = ticketUser.SleekflowUserIdentityId,
            MessageType = MessageTypes.System,
            DeliveryType = DeliveryType.Normal,
            IsSentFromSleekflow = true,
            Metadata = new Dictionary<string, object>()
            {
                {
                    "conversation_indicator", conversationIndicator
                }
            }
        };

        conversationMessage =
            await _conversationMessageService.SendConversationIndicatorMessage(conversation, conversationMessage);


        ticket = (await _ticketsApi.TicketsGetTicketPostAsync(
            getTicketInput: new GetTicketInput(ticket.Id, ticket.SleekflowCompanyId))).Data.Ticket;

        if (ticket.AssociatedMessageIds.IsNullOrEmpty())
        {
            ticket.AssociatedMessageIds = new List<long>()
            {
                conversationMessage.Id
            };
        }
        else
        {
            ticket.AssociatedMessageIds.Add(conversationMessage.Id);
        }

        await _ticketsApi.TicketsUpdateTicketPostAsync(
            updateTicketInput: new UpdateTicketInput(
                ticket.Id,
                ticket.SleekflowCompanyId,
                ticket.AssigneeId,
                ticket.AssignedTeamIds,
                new Dictionary<string, object>()
                {
                    {
                        "associated_message_ids", ticket.AssociatedMessageIds
                    }
                },
                ticketUser));

        await _lockService.ReleaseLockAsync(myLock);
    }

    public async Task<ConversationIndicator> CreateTicketConversationIndicator(
        TicketDto ticket,
        string operation,
        TicketUser ticketUser)
    {
        var i18NKey = string.Empty;
        var defaultValue = string.Empty;
        var components = new List<ConversationIndicatorComponent>();
        var staff = await _coreService.GetCompanyStaffByStaffId(long.Parse(ticketUser.SleekflowUserId));
        var user = await _userManager.FindByIdAsync(staff.IdentityId) ?? throw new Exception();

        switch (operation)
        {
            case TicketConversationIndicatorOperation.CreateTicket:
                i18NKey = "inbox.system-message.created-ticket";
                defaultValue = "<subject>{{subject}}</subject> created a new ticket <ticket>#{{ticket}}</ticket>";
                components = new List<ConversationIndicatorComponent>
                {
                    new StaffIndicatorComponent(
                        staff.Id.ToString(),
                        "subject",
                        $"{user.FirstName} {user.LastName}",
                        "#0D122C"),
                    new TicketIndicatorComponent(
                        "ticket",
                        ticket.ExternalId.ToString(),
                        "#0066FF",
                        new ConversationIndicatorWebAction(
                            "openDialog",
                            new TicketIndicatorWebActionParam(ticket.Id, "view-ticket")))
                };
                break;
            case TicketConversationIndicatorOperation.SystemCreateTicket:
                i18NKey = "inbox.system-message.created-ticket";
                defaultValue = "<subject>{{subject}}</subject> created a new <ticket>#{{ticket}}</ticket>";
                components = new List<ConversationIndicatorComponent>
                {
                    new StaffIndicatorComponent(
                        staff.Id.ToString(),
                        "subject",
                        $"System",
                        "#0D122C"),
                    new TicketIndicatorComponent(
                        "ticket",
                        ticket.ExternalId.ToString(),
                        "#0066FF",
                        new ConversationIndicatorWebAction(
                            "openDialog",
                            new TicketIndicatorWebActionParam(ticket.Id, "view-ticket")))
                };
                break;
            case TicketConversationIndicatorOperation.ResolveTicket:
                i18NKey = "inbox.system-message.resolved-ticket";
                defaultValue = "<subject>{{subject}}</subject> resolved the ticket <ticket>#{{ticket}}</ticket>";
                components = new List<ConversationIndicatorComponent>
                {
                    new StaffIndicatorComponent(
                        staff.Id.ToString(),
                        "subject",
                        $"{user.FirstName} {user.LastName}",
                        "#0D122C"),
                    new TicketIndicatorComponent(
                        "ticket",
                        ticket.ExternalId.ToString(),
                        "#0066FF",
                        new ConversationIndicatorWebAction(
                            "openDialog",
                            new TicketIndicatorWebActionParam(ticket.Id, "view-ticket")))
                };
                break;
            case TicketConversationIndicatorOperation.UpdateTicket:

                var status = await _ticketStatusesApi.TicketStatusesGetTicketStatusPostAsync(
                    getTicketStatusInput: new GetTicketStatusInput(ticket.StatusId, ticket.SleekflowCompanyId));
                i18NKey = "inbox.system-message.updated-ticket";
                defaultValue =
                    "Ticket <ticket>#{{ticket}}</ticket> status updated to {{status}} by <subject>{{subject}}</subject>";
                components = new List<ConversationIndicatorComponent>
                {
                    new StaffIndicatorComponent(
                        staff.Id.ToString(),
                        "subject",
                        $"{user.FirstName} {user.LastName}",
                        "#0D122C"),
                    new TicketIndicatorComponent(
                        "ticket",
                        ticket.ExternalId.ToString(),
                        "#0066FF",
                        new ConversationIndicatorWebAction(
                            "openDialog",
                            new TicketIndicatorWebActionParam(ticket.Id, "view-ticket"))),
                    new StatusIndicatorComponent(
                        "status",
                        "status",
                        status.Data.TicketStatus.I18nKey)
                };
                break;
            default:
                throw new NotImplementedException();
        }

        return new ConversationIndicator("ticket", i18NKey, defaultValue, components);
    }

    private async Task<Conversation> GetConversation(string userProfileId)
    {
        return await _appDbContext.Conversations.FirstOrDefaultAsync(x => x.UserProfileId == userProfileId);
    }

    public static class TicketConversationIndicatorOperation
    {
        public const string CreateTicket = "create";
        public const string SystemCreateTicket = "systemCreate";
        public const string ResolveTicket = "resolve";
        public const string UpdateTicket = "update";
    }

    #endregion

    #region Managements

    public async Task<GetTicketMetricsOutputOutput> GetTicketMetricsAsync(
        DateTimeOffset startDate,
        DateTimeOffset endDate)
    {
        return await _managementsApi.ManagementsGetTicketMetricsPostAsync(
            getTicketMetricsInput:
            new GetTicketMetricsInput(
                startDate,
                endDate));
    }

    public async Task<GetTicketCompanyConfigsOutputOutput> GetTicketCompanyConfigsAsync(
        List<FilterGroup> filterGroups,
        Sort sort,
        int limit,
        string continuationToken)
    {
        return await _managementsApi.ManagementsGetTicketCompanyConfigsPostAsync(
            getTicketCompanyConfigsInput: new GetTicketCompanyConfigsInput(
                filterGroups,
                sort,
                limit,
                continuationToken));
    }

    public async Task<GetTicketCompanyConfigCountOutputOutput> GetTicketCompanyConfigsAsync(
        List<FilterGroup> filterGroups,
        List<GroupBy> groupBys)
    {
        return await _managementsApi.ManagementsGetTicketCompanyConfigCountPostAsync(
            getTicketCompanyConfigCountInput: new GetTicketCompanyConfigCountInput(
                filterGroups,
                groupBys));
    }

    #endregion
}