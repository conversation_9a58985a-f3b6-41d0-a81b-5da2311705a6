﻿using System.Collections.Generic;
using System.Runtime.Serialization;
using Indicia.HubSpot.Api.Companies;

namespace Travis_backend.InternalDomain.Models
{
    public class InternalHubSpotCompany : HubSpotCompanyObject
    {
        public static class Property
        {
            #region HubSpot Property

            public const string Name = "name";
            public const string Domain = "domain";
            public const string Website = "website";
            public const string Description = "description";
            public const string Country = "country";
            public const string HubSpotOwnerId = "hubspot_owner_id";
            public const string HubSpotActivationOwnerId = "activation_owner";
            public const string PartnerStackLeadKey = "partnerstack_lead_key";
            public const string PartnerStackPartnerKey = "partnerstack_partner_key";
            public const string PartnerStackDealKey = "partnerstack_deal_key";
            public const string CustomerKey = "customer_key";

            #endregion

            #region CMS Property

            public const string AddOnPlans = "add_on_plans";
            public const string CampaignMessageLimit = "campaign_message_limit";
            public const string CampaignMessageSent = "campaign_message_sent";
            public const string CompanyOwnerName = "company_owner_name";
            public const string CompanyOwnerPhoneEmail = "company_owner_phone_email";
            public const string CompanyOwnerPhoneNumber = "company_owner_phone_number";
            public const string ContactCount = "contact_count";
            public const string ContactCountLimit = "contact_count_limit";
            public const string CreateAt = "create_at";
            public const string EmailChannelCount = "email_channel_count";
            public const string FacebookMessengerCount = "facebook_messenger_count";
            public const string InstagramMessengerCount = "instagram_messenger_count";
            public const string IsBankTransfer = "is_bank_transfer";
            public const string PaymentTerms = "payment_terms";
            public const string LastStaffLoginAt = "last_staff_login_at";
            public const string LineCount = "line_count";
            public const string LiveChat = "live_chat";
            public const string MonthlyRecurringRevenue = "monthly_recurring_revenue";
            public const string PowerflowLink = "powerflow_link";
            public const string SleekflowCompanyId = "sleekflow_company_id";
            public const string SleekflowCompanyName = "sleekflow_company_name";
            public const string SmsCount = "sms_count";
            public const string StaffCount = "staff_count";
            public const string StaffCountLimit = "staff_count_limit";
            public const string AutomationRuleCount = "automation_rule_count";
            public const string AutomationRuleCountLimit = "automation_rule_count_limit";
            public const string SubscriptionPlan = "subscription_plan";
            public const string SubscriptionPlanStartDate = "subscription_plan_start_date";
            public const string SubscriptionPlanEndDate = "subscription_plan_end_date";
            public const string InitialPaidSubscriptionPlan = "initial_paid_subscription_plan";
            public const string InitialPaidSubscriptionPlanStartDate = "initial_paid_subscription_plan_start_date";
            public const string LastPaidSubscriptionPlan = "last_paid_subscription_plan";
            public const string LastPaidSubscriptionPlanEndDate = "last_paid_subscription_plan_end_date";
            public const string TelegramCount = "telegram_count";
            public const string ViberCount = "viber_count";
            public const string WeChatCount = "wechat_count";
            public const string WhatsAppChatApiCount = "whatsapp_chat_api_count";
            public const string WhatsAppOfficialChannelCount = "whatsapp_official_channel_count";
            public const string WhatsappCloudApiChannelCount = "whatsapp_cloud_api_channel_count";
            public const string MessagingLimit = "messaging_limit";
            public const string TwilioAccountSid = "twilio_account_sid";
            public const string N360dialogBalance = "n360dialog_balance";
            public const string TwilioBalance = "twilio_balance";
            public const string ConversationCount = "conversation_count";
            public const string BroadcastCount = "broadcast_count";
            public const string LiveAutomationRuleCount = "live_automation_rule_count";
            public const string NewEnquiriesInPastDay = "new_enquiries_in_past_day";
            public const string NewContactsInPastDay = "new_contacts_in_past_day";
            public const string ActiveConversationsInPastDay = "active_conversations_in_past_day";
            public const string ShopifyCount = "shopify_count";
            public const string ShoplineCount = "shopline_count";
            public const string ApiKeyCount = "api_key_count";
            public const string ZapierIntegrationCount = "zapier_integration_count";
            public const string SupportTicketCount = "support_ticket_count";
            public const string SupportTicketCountInPastTwoWeek = "support_ticket_count_in_past_two_week";
            public const string SupportTicketCountInPastMonth = "support_ticket_count_in_past_month";

            public const string NumberOfConnectedChannels = "number_of_connected_channels";
            public const string TypesOfChannels = "types_of_channels";
            public const string TypesOfSoftwareIntegrations = "types_of_software_integrations";
            public const string ActiveConversationsDifference = "active_conversations_difference";
            public const string NewContactsDifference = "new_contacts_difference";
            public const string NewEnquiriesDifference = "new_enquiries_difference";
            public const string BroadcastCountInPastWeek = "broadcast_count_in_past_week";

            public const string ActiveAgentsInPastDay = "active_agents_in_past_day";
            public const string LastAgentMessageSentAt = "last_agent_message_sent_at";
            public const string PaymentFailedCount = "payment_failed_count";
            public const string PaymentFailedCountInPastThreeMonth = "payment_failed_count_in_past_three_month";

            public const string IsDeletedFromSleekflow = "is_deleted_from_sleekflow";
            public const string InitialPaidDate = "initial_paid_date";
            public const string LeadTimeForSignupToPaid = "lead_time_for_signup_to_paid";
            public const string InitialMonthlyRecurringRevenue = "initial_monthly_recurring_revenue";
            public const string InboxMessagesInPast7Days = "inbox_messages_in_past_7_days";
            public const string CompanyType = "sleekflow_company_type";
            public const string CompanySize = "company_size";
            public const string WorkflowCount = "flows_count";
            public const string ActiveWorkflowCount = "active_flows_count";
            public const string DailyWorkflowExecutionCount = "daily_workflow_execution_count";

            public const string ChurnReasons = "churn_reasons";
            public const string CustomerChurnNote = "customer_churn_note";

            #endregion

            public static class PaymentTermsValue
            {
                public const string Free = "free";
                public const string Monthly = "monthly";
                public const string Annually = "annually";
            }

            public static List<string> IdProperties => new ()
            {
                Name,
                Domain,
                HubSpotOwnerId,
                HubSpotActivationOwnerId,
                SleekflowCompanyId,
                TwilioAccountSid,
            };

            public static List<string> AllProperties => new ()
            {
                Name,
                Domain,
                Website,
                Description,
                Country,
                HubSpotOwnerId,
                HubSpotActivationOwnerId,
                AddOnPlans,
                CampaignMessageLimit,
                CampaignMessageSent,
                CompanyOwnerName,
                CompanyOwnerPhoneEmail,
                CompanyOwnerPhoneNumber,
                ContactCount,
                ContactCountLimit,
                CreateAt,
                EmailChannelCount,
                FacebookMessengerCount,
                InstagramMessengerCount,
                IsBankTransfer,
                LastStaffLoginAt,
                LineCount,
                LiveChat,
                MonthlyRecurringRevenue,
                PowerflowLink,
                SleekflowCompanyId,
                SleekflowCompanyName,
                AutomationRuleCount,
                AutomationRuleCountLimit,
                SmsCount,
                StaffCount,
                StaffCountLimit,
                SubscriptionPlan,
                SubscriptionPlanStartDate,
                SubscriptionPlanEndDate,
                InitialPaidSubscriptionPlan,
                InitialPaidSubscriptionPlanStartDate,
                LastPaidSubscriptionPlan,
                LastPaidSubscriptionPlanEndDate,
                TelegramCount,
                ViberCount,
                WeChatCount,
                WhatsAppChatApiCount,
                WhatsAppOfficialChannelCount,
                WhatsappCloudApiChannelCount,
                TwilioAccountSid,
                N360dialogBalance,
                TwilioBalance,
                ConversationCount,
                BroadcastCount,
                LiveAutomationRuleCount,
                NewEnquiriesInPastDay,
                NewContactsInPastDay,
                ActiveConversationsInPastDay,
                ShopifyCount,
                ShoplineCount,
                ApiKeyCount,
                ZapierIntegrationCount,
                SupportTicketCount,
                SupportTicketCountInPastMonth,
                InitialPaidDate,
                WorkflowCount,
                ActiveWorkflowCount,
                DailyWorkflowExecutionCount,
                PartnerStackLeadKey,
                ChurnReasons,
                CustomerChurnNote,
                PartnerStackPartnerKey,
                PartnerStackDealKey,
                CustomerKey
            };
        }

        [DataMember(Name = Property.HubSpotOwnerId)]
        public string HubSpotOwnerId { get; set; }

        [DataMember(Name = Property.HubSpotActivationOwnerId)]
        public string HubSpotActivationOwnerId { get; set; }

        [DataMember(Name = Property.AddOnPlans)]
        public string AddOnPlans { get; set; }

        [DataMember(Name = Property.CampaignMessageSent)]
        public int? CampaignMessageSent { get; set; }

        [DataMember(Name = Property.CampaignMessageLimit)]
        public int? CampaignMessageLimit { get; set; }

        [DataMember(Name = Property.CompanyOwnerName)]
        public string CompanyOwnerName { get; set; }

        [DataMember(Name = Property.CompanyOwnerPhoneEmail)]
        public string CompanyOwnerPhoneEmail { get; set; }

        [DataMember(Name = Property.CompanyOwnerPhoneNumber)]
        public string CompanyOwnerPhoneNumber { get; set; }

        [DataMember(Name = Property.ContactCount)]
        public int? ContactCount { get; set; }

        [DataMember(Name = Property.ContactCountLimit)]
        public int? ContactCountLimit { get; set; }

        [DataMember(Name = Property.CreateAt)]
        public string CreateAt { get; set; }

        [DataMember(Name = Property.IsBankTransfer)]
        public bool? IsBankTransfer { get; set; }

        [DataMember(Name = Property.PaymentTerms)]
        public string PaymentTerms { get; set; }

        [DataMember(Name = Property.LastStaffLoginAt)]
        public string LastStaffLoginAt { get; set; }

        [DataMember(Name = Property.MonthlyRecurringRevenue)]
        public decimal? MonthlyRecurringRevenue { get; set; }

        [DataMember(Name = Property.PowerflowLink)]
        public string PowerflowLink { get; set; }

        [DataMember(Name = Property.SleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [DataMember(Name = Property.SleekflowCompanyName)]
        public string SleekflowCompanyName { get; set; }

        [DataMember(Name = Property.SmsCount)]
        public int? SmsCount { get; set; }

        [DataMember(Name = Property.StaffCount)]
        public int? StaffCount { get; set; }

        [DataMember(Name = Property.StaffCountLimit)]
        public int? StaffCountLimit { get; set; }

        [DataMember(Name = Property.AutomationRuleCount)]
        public int? AutomationRuleCount { get; set; }

        [DataMember(Name = Property.AutomationRuleCountLimit)]
        public int? AutomationRuleCountLimit { get; set; }

        [DataMember(Name = Property.SubscriptionPlan)]
        public string SubscriptionPlan { get; set; }

        [DataMember(Name = Property.SubscriptionPlanEndDate)]
        public string SubscriptionPlanEndDate { get; set; }

        [DataMember(Name = Property.SubscriptionPlanStartDate)]
        public string SubscriptionPlanStartDate { get; set; }

        [DataMember(Name = Property.InitialPaidSubscriptionPlan)]
        public string InitialPaidSubscriptionPlan { get; set; }

        [DataMember(Name = Property.InitialPaidSubscriptionPlanStartDate)]
        public string InitialPaidSubscriptionPlanStartDate { get; set; }

        [DataMember(Name = Property.LastPaidSubscriptionPlan)]
        public string LastPaidSubscriptionPlan { get; set; }

        [DataMember(Name = Property.LastPaidSubscriptionPlanEndDate)]
        public string LastPaidSubscriptionPlanEndDate { get; set; }

        [DataMember(Name = Property.WhatsAppChatApiCount)]
        public int? WhatsAppChatApiCount { get; set; }

        [DataMember(Name = Property.WhatsAppOfficialChannelCount)]
        public int? WhatsAppOfficialChannelCount { get; set; }

        [DataMember(Name = Property.WhatsappCloudApiChannelCount)]
        public int? WhatsappCloudApiChannelCount { get; set; }

        [DataMember(Name = Property.MessagingLimit)]
        public string MessagingLimit { get; set; }

        [DataMember(Name = Property.LineCount)]
        public int? LineCount { get; set; }

        [DataMember(Name = Property.LiveChat)]
        public int? LiveChat { get; set; }

        [DataMember(Name = Property.FacebookMessengerCount)]
        public int? FacebookMessengerCount { get; set; }

        [DataMember(Name = Property.InstagramMessengerCount)]
        public int? InstagramMessengerCount { get; set; }

        [DataMember(Name = Property.EmailChannelCount)]
        public int? EmailChannelCount { get; set; }

        [DataMember(Name = Property.TelegramCount)]
        public int? TelegramCount { get; set; }

        [DataMember(Name = Property.ViberCount)]
        public int? ViberCount { get; set; }

        [DataMember(Name = Property.WeChatCount)]
        public int? WeChatCount { get; set; }

        [DataMember(Name = Property.TwilioAccountSid)]
        public string TwilioAccountSid { get; set; }

        [DataMember(Name = Property.TwilioBalance)]
        public decimal? TwilioBalance { get; set; }

        [DataMember(Name = Property.N360dialogBalance)]
        public decimal? N360dialogBalance { get; set; }

        [DataMember(Name = Property.ConversationCount)]
        public int? ConversationCount { get; set; }

        [DataMember(Name = Property.BroadcastCount)]
        public int? BroadcastCount { get; set; }

        [DataMember(Name = Property.NewEnquiriesInPastDay)]
        public int? NewEnquiriesInPastDay { get; set; }

        [DataMember(Name = Property.NewContactsInPastDay)]
        public int? NewContactsInPastDay { get; set; }

        [DataMember(Name = Property.ActiveConversationsInPastDay)]
        public int? ActiveConversationsInPastDay { get; set; }

        [DataMember(Name = Property.LiveAutomationRuleCount)]
        public int? LiveAutomationRuleCount { get; set; }

        [DataMember(Name = Property.ShopifyCount)]
        public int? ShopifyCount { get; set; }

        [DataMember(Name = Property.ShoplineCount)]
        public int? ShoplineCount { get; set; }

        [DataMember(Name = Property.ApiKeyCount)]
        public int? ApiKeyCount { get; set; }

        [DataMember(Name = Property.ZapierIntegrationCount)]
        public int? ZapierIntegrationCount { get; set; }

        [DataMember(Name = Property.SupportTicketCount)]
        public int? SupportTicketCount { get; set; }

        [DataMember(Name = Property.SupportTicketCountInPastMonth)]
        public int? SupportTicketCountInPastMonth { get; set; }

        [DataMember(Name = Property.NumberOfConnectedChannels)]
        public int? NumberOfConnectedChannels { get; set; }

        [DataMember(Name = Property.TypesOfChannels)]
        public int? TypesOfChannels { get; set; }

        [DataMember(Name = Property.TypesOfSoftwareIntegrations)]
        public int? TypesOfSoftwareIntegrations { get; set; }

        [DataMember(Name = Property.ActiveConversationsDifference)]
        public float? ActiveConversationsDifferenceInPercentage { get; set; }

        [DataMember(Name = Property.NewContactsDifference)]
        public float? NewContactsDifferenceInPercentage { get; set; }

        [DataMember(Name = Property.NewEnquiriesDifference)]
        public float? NewEnquiriesDifferenceInPercentage { get; set; }

        [DataMember(Name = Property.BroadcastCountInPastWeek)]
        public int? BroadcastCountInPastWeek { get; set; }

        [DataMember(Name = Property.ActiveAgentsInPastDay)]
        public int? ActiveAgentsInPastDay { get; set; }

        [DataMember(Name = Property.LastAgentMessageSentAt)]
        public string? LastAgentMessageSentAt { get; set; }

        [DataMember(Name = Property.SupportTicketCountInPastTwoWeek)]
        public int? SupportTicketCountInPastTwoWeek { get; set; }

        [DataMember(Name = Property.PaymentFailedCount)]
        public int? PaymentFailedCount { get; set; }

        [DataMember(Name = Property.PaymentFailedCountInPastThreeMonth)]
        public int? PaymentFailedCountInPastThreeMonth { get; set; }

        [DataMember(Name = Property.IsDeletedFromSleekflow)]
        public bool? IsDeletedFromSleekflow { get; set; }

        [DataMember(Name = Property.InitialPaidDate)]
        public string InitialPaidDate { get; set; }

        [DataMember(Name = Property.LeadTimeForSignupToPaid)]
        public int? LeadTimeForSignupToPaid { get; set; }

        [DataMember(Name = Property.InitialMonthlyRecurringRevenue)]
        public decimal? InitialMonthlyRecurringRevenue { get; set; }

        [DataMember(Name = Property.InboxMessagesInPast7Days)]
        public int? InboxMessagesInPast7Days { get; set; }

        [DataMember(Name = Property.CompanyType)]
        public string CompanyType { get; set; }

        [DataMember(Name = Property.CompanySize)]
        public string CompanySize { get; set; }

        [DataMember(Name = Property.WorkflowCount)]
        public int? WorkflowCount { get; set; }

        [DataMember(Name = Property.ActiveWorkflowCount)]
        public int? ActiveWorkflowCount { get; set; }

        [DataMember(Name = Property.DailyWorkflowExecutionCount)]
        public long? DailyWorkflowExecutionCount { get; set; }

        [DataMember(Name = Property.PartnerStackLeadKey)]
        public string PartnerStackLeadKey { get; set; }

        [DataMember(Name = Property.ChurnReasons)]
        public string ChurnReasons { get; set; }

        [DataMember(Name = Property.CustomerChurnNote)]
        public string CustomerChurnNote { get; set; }
    }
}