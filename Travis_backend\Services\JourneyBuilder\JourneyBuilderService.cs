using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.StaticFiles;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;
using Sleekflow.Apis.CrmHub.Client;
using Sleekflow.Apis.MessagingHub.Model;
using Travis_backend.Cache;
using Travis_backend.Constants;
using Travis_backend.ConversationDomain.Models;
using Travis_backend.ConversationServices;
using Travis_backend.Data.WhatsappCloudApi;
using Travis_backend.Database;
using Travis_backend.Helpers;
using Travis_backend.MessageDomain.Models;
using Travis_backend.MessageDomain.ViewModels;
using WABA360Dialog.ApiClient.Payloads.Enums;

namespace Travis_backend.Services.JourneyBuilder
{
    public interface IJourneyBuilderService
    {
        Task<List<string>> GetFileUrl(string apiKey, UploadExtendedMessageFileViewModel file);

        Task SendCloudApiMessage(JourneyBuilderMessageViewModel sendMessageForm);
    }

    public class JourneyBuilderService : IJourneyBuilderService
    {
        private readonly ILogger _logger;
        private readonly ApplicationDbContext _appDbContext;
        private readonly IExtendedMessageFileService _extendedMessageFileService;
        private readonly ICacheManagerService _cacheManagerService;
        private readonly IConversationMessageService _conversationMessageService;
        private readonly IWhatsappCloudApiService _whatsappCloudApiService;

        public JourneyBuilderService(
            ILogger<JourneyBuilderService> logger,
            ApplicationDbContext appDbContext,
            IExtendedMessageFileService extendedMessageFileService,
            ICacheManagerService cacheManagerService,
            IConversationMessageService messagingService,
            IWhatsappCloudApiService whatsappCloudApiService)
        {
            _logger = logger;
            _appDbContext = appDbContext;
            _extendedMessageFileService = extendedMessageFileService;
            _cacheManagerService = cacheManagerService;
            _conversationMessageService = messagingService;
            _whatsappCloudApiService = whatsappCloudApiService;
        }

        public async Task<List<string>> GetFileUrl(
            string apiKey,
            UploadExtendedMessageFileViewModel uploadExtendedMessageFileViewModel)
        {
            // Return an empty list if upload failed
            var urls = new List<string>();

            try
            {
                var apiKeyObj = await _appDbContext.CompanyAPIKeys
                    .Where(x =>
                        x.APIKey == apiKey &&
                        x.KeyType == ApiKeyTypes.JourneyBuilder)
                    .FirstOrDefaultAsync();
                var companyId = apiKeyObj.CompanyId;
                var containerName =
                    await _extendedMessageFileService.GetCompanyStorageConfigContainerNameAsync(companyId);

                if (uploadExtendedMessageFileViewModel.Channel != ChannelTypes.WhatsappCloudApi)
                {
                    throw new Exception($"Channel {uploadExtendedMessageFileViewModel.Channel} not supported.");
                }

                switch (EnumStringConverter.GetMessageType(uploadExtendedMessageFileViewModel.MediaType))
                {
                    case MessageType.image:
                        if (!WhatsappCloudApiFileHelper.AllowedImageContentTypes
                                .Contains(uploadExtendedMessageFileViewModel.File.ContentType))
                        {
                            throw new Exception(
                                $"Uploaded file MIME type {uploadExtendedMessageFileViewModel.File.ContentType} is not allowed.");
                        }

                        if (uploadExtendedMessageFileViewModel.File.Length >=
                            WhatsappCloudApiFileHelper.AllowedImageFileByteSize)
                        {
                            throw new Exception("Uploaded file is too large.");
                        }

                        break;
                    case MessageType.audio:
                        if (!WhatsappCloudApiFileHelper.AllowedAudioContentTypes.Contains(
                                uploadExtendedMessageFileViewModel.File.ContentType))
                        {
                            throw new Exception(
                                $"Uploaded file MIME type {uploadExtendedMessageFileViewModel.File.ContentType} is not allowed.");
                        }

                        if (uploadExtendedMessageFileViewModel.File.Length >=
                            WhatsappCloudApiFileHelper.AllowAudioFileByteSize)
                        {
                            throw new Exception("Uploaded file is too large.");
                        }

                        break;
                    case MessageType.video:
                        if (!WhatsappCloudApiFileHelper.AllowedVideoContentTypes.Contains(
                                uploadExtendedMessageFileViewModel.File.ContentType))
                        {
                            throw new Exception(
                                $"Uploaded file MIME type {uploadExtendedMessageFileViewModel.File.ContentType} is not allowed.");
                        }

                        if (uploadExtendedMessageFileViewModel.File.Length >=
                            WhatsappCloudApiFileHelper.AllowedVideoFileByteSize)
                        {
                            throw new Exception("Uploaded file is too large.");
                        }

                        break;
                    case MessageType.document:
                        if (!uploadExtendedMessageFileViewModel.IsTemplateFile.HasValue)
                        {
                            throw new Exception("Need to specify template file or not.");
                        }

                        if (!WhatsappCloudApiFileHelper.AllowedTemplateDocumentContentTypes.Contains(
                                uploadExtendedMessageFileViewModel.File.ContentType) &&
                            uploadExtendedMessageFileViewModel.IsTemplateFile.Value)
                        {
                            throw new Exception(
                                $"Uploaded file MIME type {uploadExtendedMessageFileViewModel.File.ContentType} is not allowed.");
                        }

                        if (uploadExtendedMessageFileViewModel.File.Length >=
                            WhatsappCloudApiFileHelper.AllowedDocumentFileByteSize)
                        {
                            throw new Exception("Uploaded file is too large.");
                        }

                        break;
                }

                var response = await _extendedMessageFileService.UploadExtendedMessageFileAsync(
                    companyId,
                    uploadExtendedMessageFileViewModel.Channel,
                    uploadExtendedMessageFileViewModel.ExtendedMessageType,
                    containerName,
                    BlobUploadPathNameBuilder.GetExtendedMessagePayloadFilePath(uploadExtendedMessageFileViewModel.File.FileName),
                    uploadExtendedMessageFileViewModel.File,
                    uploadExtendedMessageFileViewModel.MediaType,
                    uploadExtendedMessageFileViewModel.DisplayName);

                urls.Add(response.Url);
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[JourneyBuilder {MethodName} endpoint] SFMC upload file error: {ApiKey}. {ExceptionMessage}",
                    nameof(GetFileUrl),
                    apiKey ?? "-1",
                    ex.Message);
            }

            return urls;
        }

        public async Task SendCloudApiMessage(
            JourneyBuilderMessageViewModel sendMessageForm)
        {
            try
            {
                var apiKeyObj = await _appDbContext.CompanyAPIKeys
                    .Where(x =>
                        x.APIKey == sendMessageForm.ApiKey &&
                        x.KeyType == ApiKeyTypes.JourneyBuilder)
                    .FirstOrDefaultAsync();

                var companyId = apiKeyObj.CompanyId;
                var company = await _appDbContext.CompanyCompanies.FirstOrDefaultAsync(x => x.Id == companyId);

                var conversation = new Conversation
                {
                    MessageGroupName = company.SignalRGroupName, CompanyId = company.Id
                };

                var conversationMessage = new ConversationMessage
                {
                    Channel = sendMessageForm.Channel,
                    MessageType = sendMessageForm.MessageType,
                    MessageContent = sendMessageForm.MessageContent,
                    DeliveryType = DeliveryType.AutomatedMessage,
                    CompanyId = company.Id,
                    AnalyticTags = sendMessageForm.AnalyticTags,
                };

                if (conversationMessage.Channel != ChannelTypes.WhatsappCloudApi)
                {
                    throw new Exception($"Channel {conversationMessage.Channel} not supported.");
                }

                var sender = await _appDbContext.WhatsappCloudApiSenders
                    .AsNoTracking()
                    .Where(
                        x => x.CompanyId == companyId &&
                             x.WhatsappId == PhoneNumberHelper.NormalizePhoneNumber(sendMessageForm.To))
                    .FirstOrDefaultAsync();

                if (!string.IsNullOrEmpty(sendMessageForm.To))
                {
                    var cache = await _cacheManagerService.GetCacheWithConstantKeyAsync(
                        $"publicapi_add_contact_{company.Id}_{PhoneNumberHelper.NormalizePhoneNumber(sendMessageForm.To)}");
                    var acquiredLock = !string.IsNullOrEmpty(cache);
                    if (acquiredLock)
                    {
                        await Task.Delay(5000);
                    }
                }

                var cloudApiConfig = await _appDbContext
                    .ConfigWhatsappCloudApiConfigs
                    .AsNoTracking()
                    .FirstOrDefaultAsync(
                        x =>
                            x.CompanyId == companyId && x.WhatsappPhoneNumber == sendMessageForm.From);

                if (cloudApiConfig == null)
                {
                    throw new Exception(
                        $"WhatsApp Cloud Api Channel with Number {sendMessageForm.From} not found.");
                }

                if (sender == null)
                {
                    sender = new WhatsappCloudApiSender()
                    {
                        CompanyId = companyId,
                        WhatsappId = PhoneNumberHelper.NormalizePhoneNumber(sendMessageForm.To),
                        WhatsappUserDisplayName = "Anonymous",
                        WhatsappChannelPhoneNumber = sendMessageForm.From,
                    };
                }

                sender.WhatsappChannelPhoneNumber = sendMessageForm.From;
                conversation.WhatsappCloudApiUser = sender;
                conversationMessage.WhatsappCloudApiSender = conversation.WhatsappCloudApiUser;
                conversationMessage.ChannelIdentityId = sendMessageForm.From;

                if (sendMessageForm.fileURLs?.Count > 0)
                {
                    var provider = new FileExtensionContentTypeProvider();
                    string contentType;

                    var fileURLMessages = new List<FileURLMessage>();

                    foreach (var fileUrl in sendMessageForm.fileURLs)
                    {
                        if (!provider.TryGetContentType(fileUrl, out contentType))
                        {
                            contentType = "application/octet-stream";
                        }

                        fileURLMessages.Add(
                            new FileURLMessage
                            {
                                FileName = Path.GetFileName(fileUrl), FileURL = fileUrl, MIMEType = contentType
                            });
                    }

                    await _conversationMessageService.SendFileMessageByFBURL(
                        conversation,
                        conversationMessage,
                        fileURLMessages);
                }
                else
                {
                    if (sendMessageForm.MessageType == "file")
                    {
                        await _conversationMessageService.SendFileMessage(
                            conversation,
                            conversationMessage,
                            new ConversationMessageViewModel
                            {
                                files = sendMessageForm.files
                            });
                    }
                    else if (sendMessageForm.MessageType == "text")
                    {
                        await _conversationMessageService.SendMessage(conversation, conversationMessage);
                    }
                    else if (sendMessageForm.MessageType is "template" or "interactive" or "contacts"
                             or "location" or "reaction")
                    {
                        conversationMessage.ExtendedMessagePayload = new ExtendedMessagePayload()
                        {
                            Channel = ChannelTypes.WhatsappCloudApi
                        };

                        conversationMessage.ExtendedMessagePayload.SetExtendedMessagePayloadDetailWithType(
                            sendMessageForm.TemplatePayload);

                        switch (sendMessageForm.MessageType)
                        {
                            case "template":
                                if (conversationMessage.ExtendedMessagePayload.ExtendedMessagePayloadDetail ==
                                    null ||
                                    conversationMessage.ExtendedMessagePayload.ExtendedMessagePayloadDetail
                                        .WhatsappCloudApiTemplateMessageObject == null ||
                                    string.IsNullOrWhiteSpace(
                                        conversationMessage.ExtendedMessagePayload
                                            .ExtendedMessagePayloadDetail.WhatsappCloudApiTemplateMessageObject
                                            .TemplateName) ||
                                    string.IsNullOrWhiteSpace(
                                        conversationMessage.ExtendedMessagePayload
                                            .ExtendedMessagePayloadDetail.WhatsappCloudApiTemplateMessageObject
                                            .Language) ||
                                    conversationMessage.ExtendedMessagePayload.ExtendedMessagePayloadDetail
                                        .WhatsappCloudApiTemplateMessageObject == null)
                                {
                                    throw new Exception("Template message format is invalid.");
                                }

                                var messagingHubWabaId = await _appDbContext.ConfigWhatsappCloudApiConfigs
                                    .Where(
                                        x => x.CompanyId == company.Id && x.WhatsappPhoneNumber ==
                                            conversation.WhatsappCloudApiUser.WhatsappChannelPhoneNumber)
                                    .Select(x => x.MessagingHubWabaId).FirstOrDefaultAsync();

                                try
                                {
                                    var cacheKey =
                                        WhatsappCloudApiCacheKeyHelper.GetTemplateResponseCacheKey(
                                            company.Id,
                                            messagingHubWabaId);

                                    var cache = await _cacheManagerService.GetCacheWithConstantKeyAsync(cacheKey);

                                    List<WhatsappCloudApiTemplateResponse> result;
                                    WhatsappCloudApiTemplate template = null;

                                    if (!string.IsNullOrEmpty(cache))
                                    {
                                        result =
                                            JsonConvert.DeserializeObject<List<WhatsappCloudApiTemplateResponse>>(
                                                cache);

                                        template = result.FirstOrDefault(
                                            x =>
                                                x.Name == conversationMessage.ExtendedMessagePayload
                                                    .ExtendedMessagePayloadDetail.WhatsappCloudApiTemplateMessageObject
                                                    .TemplateName &&
                                                x.Language == conversationMessage.ExtendedMessagePayload
                                                    .ExtendedMessagePayloadDetail.WhatsappCloudApiTemplateMessageObject
                                                    .Language);
                                    }

                                    if (template == null)
                                    {
                                        result = await _whatsappCloudApiService.GetTemplates(
                                            company.Id,
                                            messagingHubWabaId);

                                        await _cacheManagerService.SaveCacheWithConstantKeyAsync(
                                            cacheKey,
                                            result,
                                            TimeSpan.FromMinutes(30),
                                            new JsonSerializerSettings
                                            {
                                                DateTimeZoneHandling = DateTimeZoneHandling.Utc,
                                                NullValueHandling = NullValueHandling.Ignore,
                                                ContractResolver = new CamelCasePropertyNamesContractResolver()
                                            });

                                        template = result.FirstOrDefault(
                                            x =>
                                                x.Name == conversationMessage.ExtendedMessagePayload
                                                    .ExtendedMessagePayloadDetail.WhatsappCloudApiTemplateMessageObject
                                                    .TemplateName &&
                                                x.Language == conversationMessage.ExtendedMessagePayload
                                                    .ExtendedMessagePayloadDetail.WhatsappCloudApiTemplateMessageObject
                                                    .Language);
                                    }

                                    if (template == null)
                                    {
                                        throw new Exception(
                                            $"Template '{conversationMessage.ExtendedMessagePayload.ExtendedMessagePayloadDetail.WhatsappCloudApiTemplateMessageObject.TemplateName}' not found.");
                                    }

                                    conversationMessage.MessageContent =
                                        template.Components.First(x => x.Type == "BODY").Text;
                                    conversationMessage.MessageContent =
                                        conversationMessage.MessageContent
                                            .FormatWhatsappCloudApiTemplateParamToBodyText(
                                                conversationMessage
                                                    .ExtendedMessagePayload.ExtendedMessagePayloadDetail
                                                    .WhatsappCloudApiTemplateMessageObject.Components);
                                }
                                catch (SleekflowErrorCodeException ex)
                                {
                                    _logger.LogError(
                                        ex,
                                        "Journey Builder Sending Message Error, CompanyId {CompanyId} with error: {ExceptionString}",
                                        companyId,
                                        ex.ToString());

                                    throw;
                                }

                                break;
                            case "interactive":
                                if (conversationMessage.ExtendedMessagePayload.ExtendedMessagePayloadDetail
                                        .WhatsappCloudApiInteractiveObject == null)
                                {
                                    throw new Exception("Interactive message format is invalid.");
                                }

                                break;
                            case "contacts":
                                if (conversationMessage.ExtendedMessagePayload.ExtendedMessagePayloadDetail
                                        .WhatsappCloudApiContactsObject == null)
                                {
                                    throw new Exception("Contacts message format is invalid.");
                                }

                                break;
                            case "location":
                                if (conversationMessage.ExtendedMessagePayload.ExtendedMessagePayloadDetail
                                        .WhatsappCloudApiLocationObject == null)
                                {
                                    throw new Exception("Contacts message format is invalid.");
                                }

                                break;
                            case "reaction":
                                if (conversationMessage.ExtendedMessagePayload.ExtendedMessagePayloadDetail
                                        .WhatsappCloudApiReactionObject == null)
                                {
                                    throw new Exception("Reaction message format is invalid.");
                                }

                                break;
                            default:
                                throw new ArgumentException("Supported message type.");
                        }

                        await _conversationMessageService.SendMessage(conversation, conversationMessage);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "SFMC send message error: {ApiKey}. {ExceptionMessage}",
                    sendMessageForm.ApiKey ?? "-1",
                    ex.Message);

                throw;
            }
        }
    }
}