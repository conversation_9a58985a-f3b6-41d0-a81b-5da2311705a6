using Newtonsoft.Json;

namespace Travis_backend.PartnerStackIntegrationDomain.Models;

public class PartnerStackAddress
{
    [JsonProperty("city")]
    public string City { get; set; }

    [JsonProperty("country")]
    public string Country { get; set; }

    [JsonProperty("key")]
    public string Key { get; set; }

    [Json<PERSON>roperty("postal")]
    public string Postal { get; set; }

    [Json<PERSON>roperty("primary")]
    public bool Primary { get; set; }

    [JsonProperty("region")]
    public string Region { get; set; }

    [JsonProperty("state")]
    public string State { get; set; }

    [JsonProperty("street")]
    public string Street { get; set; }

    [JsonProperty("unit")]
    public string Unit { get; set; }
}