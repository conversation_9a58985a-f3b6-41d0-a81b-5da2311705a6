using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Sleekflow.Powerflow.Apis.Services.Conversation.Services;
using Sleekflow.Powerflow.Apis.Services.Conversation.Services.Export;
using Travis_backend.AccountAuthenticationDomain.Models;
using Travis_backend.CommonDomain.ViewModels;
using Travis_backend.Configuration;
using ExportConversationSnapshotRequest = Sleekflow.Powerflow.Apis.ViewModels.ExportConversationSnapshotRequest;

namespace Sleekflow.Powerflow.Apis.Controllers;

/// <summary>
/// Internal Cms APIs for conversation.
/// </summary>
[Authorize(Roles = ApplicationUserRole.InternalCmsUser)] // Basic Role Requirement
[Route("/internal/conversation/[action]")]
public class InternalCmsConversationSnapshotController : InternalControllerBase
{
    private readonly IInternalConversationSnapshotService _internalConversationSnapshotService;
    private readonly IInternalConversationSnapshotExportService _internalConversationSnapshotExportService;

    public InternalCmsConversationSnapshotController(
        UserManager<ApplicationUser> userManager,
        IInternalConversationSnapshotService internalConversationSnapshotService,
        IInternalConversationSnapshotExportService internalConversationSnapshotExportService)
        : base(userManager)
    {
        _internalConversationSnapshotService = internalConversationSnapshotService;
        _internalConversationSnapshotExportService = internalConversationSnapshotExportService;
    }

    /// <summary>
    /// Export Company Conversation snapshot in targeted format.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    public async Task<IActionResult> ExportConversationSnapshot(
        [FromBody]
        ExportConversationSnapshotRequest request)
    {
        if (await GetCurrentValidInternalUser(
                new List<string>()
                {
                    ApplicationUserRole.InternalCmsUser
                }) == null)
        {
            return Unauthorized();
        }

        if (!ModelState.IsValid)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Invalid Form"
                });
        }

        var fileName = request.Start is not null && request.End is not null
            ? $"conversation/snapshot-{request.Start.Value.Date:yyyy-MM-dd} - {request.End.Value.Date:yyyy-MM-dd}"
            : "conversation/snapshot";

        var conversationSnapshot = await _internalConversationSnapshotService.FindConversationSnapshotsAsync(
            companyId: request.CompanyId,
            startDate: request.Start,
            endDate: request.End,
            limit: request.Limit);

        var file = await _internalConversationSnapshotExportService.ExportConversationSnapshotAsync(
            conversationSnapshot,
            request.FileFormat,
            fileName);

        return File(file.FileContents, file.ContentType, file.Filename);
    }
}