using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using AutoMapper;
using AutoMapper.QueryableExtensions;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Travis_backend.Cache;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.Constants;
using Travis_backend.ConversationDomain.Repositories;
using Travis_backend.ConversationDomain.ViewModels;
using Travis_backend.Database;
using Travis_backend.Database.Services;
using Travis_backend.Enums;
using Travis_backend.MessageDomain.Models;
using Travis_backend.MessageDomain.ViewModels;
using Travis_backend.StripeIntegrationDomain.Services;

namespace Travis_backend.ConversationDomain.Services;

public interface IConversationService
{
    Task<ConversationNoCompanyResponseViewModel> GetConversationDetails(
        string companyId,
        long companyUserId,
        StaffUserRole companyUserRole,
        string conversationId,
        CancellationToken cancellationToken = default);

    Task<HashSet<long>> FilterConversationMessage(
        IQueryable<ConversationMessage> allMessageCandidates,
        List<string> whatsappIds,
        List<long> whatsapp360dialogDefaultChannelIds,
        List<string> whatsappCloudApiDefaultChannelIds,
        List<string> twilioSenderIds);

    Task<bool> GetConversationLastMessage(
        ConversationNoCompanyResponseViewModel conversation,
        bool filterLastMessage,
        List<string> whatsappIds,
        List<long> whatsapp360dialogDefaultChannelIds,
        List<string> channelIdList,
        List<string> channelList,
        List<string> twilioSenderIds,
        bool isUpdatedLastMessageId,
        List<string> whatsappCloudApiDefaultChannelIds,
        CancellationToken cancellationToken = default);

    Task UpdateConversationMetadataAsync(
        string companyId,
        string conversationId,
        string key,
        object valueObject);

    ValueTask<bool> IsStaffAllowedToSendMessage(Staff staff, string conversationId);

    /// <summary>
    /// Get broadcast message count created between two dates.
    /// </summary>
    /// <param name="companyId">Company Id.</param>
    /// <param name="from">Broadcast message created from.</param>
    /// <param name="to">Broadcast message created to.</param>
    /// <returns>Number of broadcast message.</returns>
    Task<int> GetBroadcastMessageCountCreatedBetweenDateAsync(string companyId, DateTime from, DateTime to);

}

public class ConversationService : IConversationService
{
    private readonly ApplicationDbContext _appDbContext;
    private readonly IMapper _mapper;
    private readonly ILogger _logger;
    private readonly IConfiguration _configuration;
    private readonly ILockService _lockService;
    private readonly ISleekPayService _sleekPayService;
    private readonly IDbContextService _dbContextService;
    private readonly IConversationMessageRepository _conversationMessageRepository;

    private static IReadOnlyList<string> NonAssignableAsTeamDefaultChannels
        = new List<string>()
        {
            ChannelTypes.Note,
            ChannelTypes.LiveChat,
            ChannelTypes.Wechat,
            ChannelTypes.Telegram,
            ChannelTypes.Viber,
            ChannelTypes.Line,
            ChannelTypes.Sms
        };

    public ConversationService(
        ApplicationDbContext appDbContext,
        IMapper mapper,
        IConfiguration configuration,
        ILogger<ConversationService> logger,
        ILockService lockService,
        ISleekPayService sleekPayService,
        IDbContextService dbContextService,
        IConversationMessageRepository conversationMessageRepository)
    {
        _appDbContext = appDbContext;
        _mapper = mapper;
        _configuration = configuration;
        _logger = logger;
        _lockService = lockService;
        _sleekPayService = sleekPayService;
        _dbContextService = dbContextService;
        _conversationMessageRepository = conversationMessageRepository;
    }

    public async Task<ConversationNoCompanyResponseViewModel> GetConversationDetails(
        string companyId,
        long companyUserId,
        StaffUserRole companyUserRole,
        string conversationId,
        CancellationToken cancellationToken = default)
    {
        var dbContext = _dbContextService.GetDbContext();
        var responseViewModel = await dbContext.Conversations
            .Where(x =>
                x.CompanyId == companyId
                && x.Id == conversationId)
            .ProjectTo<ConversationNoCompanyResponseViewModel>(_mapper.ConfigurationProvider)
            .AsSplitQuery()
            .AsNoTracking()
            .FirstOrDefaultAsync(cancellationToken: cancellationToken);

        if (responseViewModel == null)
        {
            throw new EntryPointNotFoundException("no conversation found");
        }

        // Personal bookmark
        responseViewModel.IsBookmarked = await dbContext.ConversationBookmarks
            .AnyAsync(
                x =>
                    x.ConversationId == responseViewModel.ConversationId
                    && x.StaffId == companyUserId,
                cancellationToken: cancellationToken);

        // Personal unread
        if (responseViewModel.UnreadMessageCount == 0)
        {
            responseViewModel.UnreadMessageCount = await dbContext.ConversationUnreadRecords
                .CountAsync(
                    y =>
                        y.CompanyId == companyId
                        && y.StaffId == companyUserId
                        && y.ConversationId == responseViewModel.ConversationId
                        && y.NotificationDeliveryStatus != NotificationDeliveryStatus.Read,
                    cancellationToken: cancellationToken);
        }

        try
        {
            try
            {
                responseViewModel.ConversationChannels = await GetConversationChannels(responseViewModel.ConversationId);
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[{MethodName}] Error retrieving channels of past messages for conversationId {ConversationId}: {ExceptionMessage}",
                    nameof(GetConversationDetails),
                    conversationId,
                    ex.Message);
            }

            // mappedResult.Company.CompanyHashtags.ForEach(x => x.Count = _appDbContext.ConversationHashtags.Count(y => y.Hashtag.Id == x.Id));
            var rolePermission = await dbContext.CompanyRolePermissions
                .AsNoTracking()
                .FirstOrDefaultAsync(
                    x =>
                        x.CompanyId == companyId
                        && x.StaffUserRole == companyUserRole,
                    cancellationToken: cancellationToken);

            var filterLastMessage =
                rolePermission != null
                && rolePermission.Permission.IsShowDefaultChannelMessagesOnly;

            var associatedTeams = await dbContext.CompanyStaffTeams
                .Where(x => x.Members.Any(y => y.StaffId == companyUserId))
                .AsNoTracking()
                .ToListAsync(cancellationToken: cancellationToken);

            var channelList = new List<string>();
            var channelIdList = new List<string>();
            var whatsappIds = new List<string>();
            var twilioSenderIds = new List<string>();
            var whatsapp360dialogDefaultChannelIds = new List<long>();
            var whatsappCloudDefaultChannelIds = new List<string>();

            foreach (var associatedTeam in associatedTeams)
            {
                if (associatedTeam.DefaultChannels?.Count > 0)
                {
                    foreach (var defaultChannel in associatedTeam.DefaultChannels
                                 .Where(
                                     x =>
                                         x.channel != ChannelTypes.Whatsapp360Dialog
                                         && x.channel != ChannelTypes.WhatsappCloudApi))
                    {
                        channelList.Add(defaultChannel.channel);

                        if (defaultChannel.ids != null)
                        {
                            foreach (var id in defaultChannel.ids)
                            {
                                var twilioInstance = id.Split(";", StringSplitOptions.RemoveEmptyEntries);
                                whatsappIds.Add(twilioInstance[0]);

                                if (twilioInstance.Count() > 1)
                                {
                                    twilioSenderIds.Add(twilioInstance[1].Replace(": ", ":+"));
                                }
                            }
                        }
                    }

                    foreach (var defaultChannel in associatedTeam.DefaultChannels
                                 .Where(x => x.channel == ChannelTypes.Whatsapp360Dialog))
                    {
                        foreach (var channelId in defaultChannel.ids)
                        {
                            var validLong = long.TryParse(channelId, out var whatsapp360dialogChannelId);

                            if (validLong)
                            {
                                whatsapp360dialogDefaultChannelIds.Add(whatsapp360dialogChannelId);
                            }
                        }
                    }

                    foreach (var defaultChannel in associatedTeam.DefaultChannels
                                 .Where(x => x.channel == ChannelTypes.WhatsappCloudApi))
                    {
                        foreach (var channelId in defaultChannel.ids)
                        {
                            whatsappCloudDefaultChannelIds.Add(channelId);
                        }
                    }
                }
            }

            await GetConversationLastMessage(
                responseViewModel,
                filterLastMessage,
                whatsappIds,
                whatsapp360dialogDefaultChannelIds,
                channelIdList,
                channelList,
                twilioSenderIds,
                false,
                whatsappCloudDefaultChannelIds,
                cancellationToken);

            responseViewModel.FirstMessageId = await dbContext.ConversationMessages
                .Where(y => y.ConversationId == responseViewModel.ConversationId)
                .OrderBy(x => x.CreatedAt)
                .Select(x => x.Id)
                .FirstOrDefaultAsync(cancellationToken: cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "[{MethodName}] Exception for conversation {ConversationId}: {ExceptionMessage}",
                nameof(GetConversationDetails),
                conversationId,
                ex.Message);
        }

        await _sleekPayService.AddSleekPayRecord(responseViewModel.LastMessage);
        responseViewModel.UserProfile.ConversationId = responseViewModel.ConversationId;

        // Attach Contact Listing in the signalR response
        var lists = await _appDbContext.CompanyImportContactHistories
            .Where(x => x.ImportedUserProfiles.Any(y => y.UserProfileId == responseViewModel.UserProfile.Id))
            .OrderByDescending(x => x.IsBookmarked)
            .ThenBy(x => x.Order)
            .ThenByDescending(x => x.CreatedAt)
            .Select(
                x => new
                {
                    x.Id, x.ImportName
                })
            .AsNoTracking()
            .ToListAsync(cancellationToken: cancellationToken);

        foreach (var list in lists)
        {
            responseViewModel.UserProfile.ContactLists.Add(
                new ContactJoinedList
                {
                    Id = list.Id, ListName = list.ImportName
                });
        }

        return responseViewModel;
    }

    public async Task<bool> GetConversationLastMessage(
        ConversationNoCompanyResponseViewModel conversation,
        bool filterLastMessage,
        List<string> whatsappIds,
        List<long> whatsapp360dialogDefaultChannelIds,
        List<string> channelIdList,
        List<string> channelList,
        List<string> twilioSenderIds,
        bool isUpdatedLastMessageId,
        List<string> whatsappCloudApiDefaultChannelIds,
        CancellationToken cancellationToken = default)
    {
        var dbContext = _dbContextService.GetDbContext();
        if (conversation.LastMessageId.HasValue
            && (!filterLastMessage
                || (whatsappIds.Count == 0
                    && whatsapp360dialogDefaultChannelIds.Count == 0
                    && whatsappCloudApiDefaultChannelIds.Count == 0)))
        {
            var lastMessage = await dbContext.ConversationMessages
                .Where(x => x.Id == conversation.LastMessageId)
                .Include(x => x.UploadedFiles)
                .Include(x => x.EmailFrom)
                .Include(x => x.Sender)
                .Include(x => x.Receiver)
                .Include(x => x.SenderDevice)
                .Include(x => x.ReceiverDevice)
                .Include(x => x.facebookSender)
                .Include(x => x.facebookReceiver)
                .Include(x => x.whatsappSender)
                .Include(x => x.whatsappReceiver)
                .Include(x => x.WebClientSender)
                .Include(x => x.WebClientReceiver)
                .Include(x => x.MessageAssignee.Identity)
                .Include(x => x.WeChatSender)
                .Include(x => x.WeChatReceiver)
                .Include(x => x.LineSender)
                .Include(x => x.LineReceiver)
                .Include(x => x.SMSSender)
                .Include(x => x.SMSReceiver)
                .Include(x => x.InstagramReceiver)
                .Include(x => x.InstagramSender)
                .Include(x => x.ViberSender)
                .Include(x => x.ViberReceiver)
                .Include(x => x.TelegramSender)
                .Include(x => x.TelegramReceiver)
                .Include(x => x.Whatsapp360DialogSender)
                .Include(x => x.Whatsapp360DialogReceiver)
                .Include(x => x.Whatsapp360DialogExtendedMessagePayload)
                .Include(x => x.ExtendedMessagePayload)
                .ProjectTo<ConversationMessageResponseViewModel>(_mapper.ConfigurationProvider)
                .ToListAsync(cancellationToken: cancellationToken);

            conversation.LastMessage = lastMessage;
            await _sleekPayService.AddSleekPayRecord(conversation.LastMessage);

            conversation.ConversationChannels.Add(conversation.LastMessageChannel);
        }
        else
        {
            var conversationMessagesQuery = dbContext.ConversationMessages
                .Where(message => message.ConversationId == conversation.ConversationId);

            HashSet<long> filteredMessagedIds;

            if (whatsappIds.Count == 0
                && whatsappCloudApiDefaultChannelIds.Count == 0
                && whatsapp360dialogDefaultChannelIds.Count == 0
                && twilioSenderIds.Count == 0)
            {
                filteredMessagedIds = (await conversationMessagesQuery
                        .Select(x => x.Id)
                        .ToListAsync(cancellationToken: cancellationToken))
                    .ToHashSet();
            }
            else
            {
                filteredMessagedIds = await FilterConversationMessage(
                    conversationMessagesQuery,
                    whatsappIds,
                    whatsapp360dialogDefaultChannelIds,
                    whatsappCloudApiDefaultChannelIds,
                    twilioSenderIds);
            }

            if (channelIdList.Count > 0)
            {
                try
                {
                    var newWhatsappId = new List<string>();
                    var newInstanceSender = new List<string>();
                    var newWhatsapp360dialogDefaultChannelIds = new List<long>();
                    var newWhatsappCloudApiChannelIds = new List<string>();

                    if (channelList.Contains(ChannelTypes.WhatsappTwilio))
                    {
                        foreach (var myChannelId in channelIdList)
                        {
                            var twilioInstance = myChannelId.Split(";", StringSplitOptions.RemoveEmptyEntries);
                            newWhatsappId.Add(twilioInstance[0]);

                            if (twilioInstance.Count() > 1)
                            {
                                newInstanceSender.Add(twilioInstance[1].Replace(": ", ":+"));
                            }
                        }
                    }

                    if (channelList.Contains(ChannelTypes.Whatsapp360Dialog))
                    {
                        var whatsapp360DialogChannelId = channelIdList
                            .Select(x => long.TryParse(x, out var id) ? id : 0)
                            .Where(x => x != 0)
                            .Distinct()
                            .ToList();

                        newWhatsapp360dialogDefaultChannelIds.AddRange(whatsapp360DialogChannelId);
                    }

                    if (channelList.Contains(ChannelTypes.WhatsappCloudApi))
                    {
                        var whatsappCloudApiIds = channelIdList
                            .Distinct()
                            .ToList();

                        newWhatsappCloudApiChannelIds.AddRange(whatsappCloudApiIds);
                    }

                    if (channelList.Contains(ChannelTypes.Instagram))
                    {
                        var instagramIds = channelIdList
                            .Distinct()
                            .ToList();

                        newWhatsappId.AddRange(instagramIds);
                    }

                    if (channelList.Contains(ChannelTypes.Facebook))
                    {
                        var facebookIds = channelIdList
                            .Distinct()
                            .ToList();

                        newWhatsappId.AddRange(facebookIds);
                    }

                    var newFilteredMessagedIds = await FilterConversationMessage(
                        conversationMessagesQuery,
                        newWhatsappId,
                        newWhatsapp360dialogDefaultChannelIds,
                        newWhatsappCloudApiChannelIds,
                        newInstanceSender);

                    if (newFilteredMessagedIds.Count > 0)
                    {
                        filteredMessagedIds.IntersectWith(newFilteredMessagedIds);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(
                        ex,
                        "[{MethodName}] error: {ExceptionMessage}",
                        nameof(GetConversationLastMessage),
                        ex.Message);
                }
            }

            conversation.LastMessage = await dbContext.ConversationMessages
                .Where(x => filteredMessagedIds.Contains(x.Id))
                .OrderByDescending(x => x.Timestamp)
                .AsNoTracking()
                .ProjectTo<ConversationMessageResponseViewModel>(_mapper.ConfigurationProvider)
                .Take(1)
                .ToListAsync(cancellationToken: cancellationToken);

            await _sleekPayService.AddSleekPayRecord(conversation.LastMessage);

            if (conversation.ConversationChannels.Count == 0)
            {
                conversation.ConversationChannels = await GetConversationChannels(conversation.ConversationId, cancellationToken);
            }
        }

        return isUpdatedLastMessageId;
    }

    private async Task<List<string>> GetConversationChannels(
        string conversationId,
        CancellationToken cancellationToken = default)
    {
        var conversationChannels = await _dbContextService.GetDbContext().ConversationMessages
            .Where(x => x.ConversationId == conversationId)
            .Select(x => x.Channel)
            .Distinct()
            .ToListAsync(cancellationToken: cancellationToken);

        if (conversationChannels.Contains(ChannelTypes.Note))
        {
            conversationChannels.Remove(ChannelTypes.Note);
        }

        return conversationChannels;
    }

    public async ValueTask<bool> IsStaffAllowedToSendMessage(Staff staff, string conversationId)
    {
        var isStaffRole = staff.RoleType == StaffUserRole.Staff;

        // Bug Fix: DEVS-3349 [Travel Expert] Unassigned tab is not working normally
        // This bug is caused by another Bug Fix: DEVS-2429
        // All members can send messages to an unassigned conversation
        var isUnassignedConversation = await _appDbContext.Conversations.AnyAsync(
            x => x.Id == conversationId
                 && x.AssigneeId == null);

        if (isUnassignedConversation)
        {
            return true;
        }

        // non-Staff role members can send messages to an assigned conversation
        if (!isStaffRole)
        {
            return true;
        }

        // In response to Alex Yu's request for better DB performance, we use EF Core's search here.
        // This decreases round-trip fetches for the conversation object by performing checks within the search.
        var isContactOwner = await _appDbContext.Conversations
            .AnyAsync(x =>
                x.Id == conversationId
                && x.AssigneeId == staff.Id);

        var isCollaborator = await _appDbContext.ConversationAdditionalAssignees
            .AnyAsync(x =>
                x.ConversationId == conversationId
                && x.AssigneeId == staff.Id);

        // Staff role members are only permitted to send messages in an assigned conversation if they are the contact owner or collaborator.
        return isCollaborator || isContactOwner;

        // Previous Fix done by Peter - commit number: 52a57907
        // switch (staffInfo.RoleType)
        // {
        //    case StaffUserRole.Staff:
        //        if (_appDbContext.Conversations.Where(y => y.Id == conversationMessageViewModel.ConversationId && (y.AssigneeId == staffInfo.Id || !y.AssigneeId.HasValue)).Count() > 0)
        //            break;
        //        return Unauthorized();
        //    case StaffUserRole.TeamAdmin:
        //        var teams = _appDbContext.CompanyStaffTeams.Where(x => x.Members.Where(y => y.StaffId == staffInfo.Id).Count() > 0);
        //        if (_appDbContext.Conversations.Where(y => y.Id == conversationMessageViewModel.ConversationId && (y.AssigneeId == staffInfo.Id || !y.AssigneeId.HasValue || teams.Where(z => z.Members.Where(a => a.StaffId == y.AssigneeId).Count() > 0).Count() > 0)).Count() > 0 ||
        //            _appDbContext.Conversations.Where(y => y.Id == conversationMessageViewModel.ConversationId && y.AssignedTeam.Members.Where(x => x.StaffId == staffInfo.Id).Count() > 0).Count() > 0)
        //            break;
        //        return Unauthorized();
        // }
    }

    public async Task<HashSet<long>> FilterConversationMessage(
        IQueryable<ConversationMessage> allMessageCandidates,
        List<string> whatsappIds,
        List<long> whatsapp360dialogDefaultChannelIds,
        List<string> whatsappCloudApiDefaultChannelIds,
        List<string> twilioSenderIds)
    {
        var resultQueryables = new List<IQueryable<ConversationMessage>>();

        if (whatsapp360dialogDefaultChannelIds is { Count: > 0 })
        {
            var whatsapp360dialogDefaultMessagesQueryable =
                from conversationMessage in allMessageCandidates
                where whatsapp360dialogDefaultChannelIds.Contains(
                          conversationMessage.Whatsapp360DialogReceiver.ChannelId.Value)
                      || whatsapp360dialogDefaultChannelIds.Contains(
                          conversationMessage.Whatsapp360DialogSender.ChannelId.Value)
                      || NonAssignableAsTeamDefaultChannels.Contains(conversationMessage.Channel)
                select conversationMessage;

            resultQueryables.Add(whatsapp360dialogDefaultMessagesQueryable);
        }

        if (twilioSenderIds is { Count: > 0 })
        {
            var twilioMessagesQueryable = from conversationMessage in allMessageCandidates
                where (conversationMessage.Channel == ChannelTypes.WhatsappTwilio
                       && conversationMessage.whatsappReceiver.InstaneSender != null
                       && whatsappIds.Contains(conversationMessage.whatsappReceiver.InstanceId)
                       && twilioSenderIds.Contains(conversationMessage.whatsappReceiver.InstaneSender))
                      || NonAssignableAsTeamDefaultChannels.Contains(conversationMessage.Channel)
                select conversationMessage;

            resultQueryables.Add(twilioMessagesQueryable);
        }

        if (whatsappIds is { Count: > 0 })
        {
            var whatsappMessagesQueryable =
                from conversationMessage in allMessageCandidates
                where whatsappIds.Contains(conversationMessage.facebookReceiver.pageId)
                      || whatsappIds.Contains(conversationMessage.InstagramReceiver.InstagramPageId)
                      || NonAssignableAsTeamDefaultChannels.Contains(conversationMessage.Channel)
                select conversationMessage;

            resultQueryables.Add(whatsappMessagesQueryable);
        }

        if (whatsappCloudApiDefaultChannelIds is { Count: > 0 })
        {
            var whatsappCloudApiMessagesQueryable = allMessageCandidates
                .Where(
                    x =>
                        whatsappCloudApiDefaultChannelIds.Contains(x.ChannelIdentityId)
                        || NonAssignableAsTeamDefaultChannels.Contains(x.Channel));

            resultQueryables.Add(whatsappCloudApiMessagesQueryable);
        }

        if (resultQueryables.Count == 0)
        {
            return new HashSet<long>();
        }

        var queryable = resultQueryables.FirstOrDefault();

        for (var i = 1; i < resultQueryables.Count; i++)
        {
            queryable = queryable!.Union(resultQueryables[i]);
        }

        var filteredMessagedIds = (await queryable!
                .Select(x => x.Id)
                .ToListAsync())
            .ToHashSet();

        return filteredMessagedIds;
    }

    public async Task UpdateConversationMetadataAsync(string companyId, string conversationId, string key, object valueObject)
    {
        ILockService.Lock myLock = null;

        try
        {
            while (true)
            {
                var lockId = $"{companyId}_{conversationId}";

                myLock = await _lockService.AcquireLockAsync(lockId, TimeSpan.FromSeconds(2));

                if (myLock == null)
                {
                    await Task.Delay(TimeSpan.FromSeconds(2));
                }
                else
                {
                    break;
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "[{MethodName}] Unable to lock UpdateConversationMetadataAsync for {CompanyId} {ConversationId}: {ExceptionMessage}",
                nameof(UpdateConversationMetadataAsync),
                companyId,
                conversationId,
                ex.Message);
        }

        var conversation = await _appDbContext.Conversations.Where(x => x.CompanyId == companyId && x.Id == conversationId)
            .FirstOrDefaultAsync();

        if (conversation.Metadata == null)
        {
            conversation.Metadata = new Dictionary<string, object>()
            {
                {
                    key, valueObject
                }
            };
        }
        else
        {
            conversation.Metadata[key] = valueObject;
        }

        await _appDbContext.Conversations.Where(x => x.Id == conversationId)
            .ExecuteUpdateAsync(s => s.SetProperty(c => c.Metadata, conversation.Metadata));

        await _lockService.ReleaseLockAsync(myLock);
    }

    /// <inheritdoc />
    public async Task<int> GetBroadcastMessageCountCreatedBetweenDateAsync(string companyId, DateTime from, DateTime to)
    {
        return await _conversationMessageRepository.CountByCreatedBetweenDateTimeAsync(
            companyId,
            DeliveryType.Broadcast,
            from,
            to);
    }
}