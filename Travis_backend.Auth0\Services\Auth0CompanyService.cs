using System.Security;
using AutoMapper;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using Travis_backend.AccountAuthenticationDomain.Models;
using Travis_backend.AccountAuthenticationDomain.Services;
using Travis_backend.AccountAuthenticationDomain.ViewModels;
using Travis_backend.Auth0.Controllers;
using Travis_backend.Auth0.Controllers.Webhook;
using Travis_backend.Auth0.Exceptions;
using Travis_backend.Auth0.Models;
using Travis_backend.Cache;
using Travis_backend.CommonDomain.ViewModels;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.CompanyDomain.Services;
using Travis_backend.CompanyDomain.ViewModels;
using Travis_backend.ConversationDomain.ViewModels;
using Travis_backend.ConversationServices;
using Travis_backend.Database;
using Travis_backend.Enums;
using Travis_backend.ShareInvitationDomain.Models;
using Travis_backend.ShareInvitationDomain.ViewModels;
using Travis_backend.SubscriptionPlanDomain.Services;
using Auth0User = Auth0.ManagementApi.Models.User;

namespace Travis_backend.Auth0.Services;

public interface IAuth0CompanyService
{
    Task<string> GenerateResetPasswordLinkForStaff(string companyId, string staffIdentityId);

    Task<RegisterCompanyResult> RegisterCompanyAsync(
        ApplicationUser adminUser,
        RegisterCompanyInput registerCompanyInput);

    Task<List<InvitedUserByEmailObject>> InviteUsersByEmailAsync(
        ApplicationUser? adminUser,
        List<long> teamIds,
        List<InviteUserInputObject> inviteUsers);

    Task<(ShareableInvitation, string)> GenerateShareableLinkAsync(
        string adminUserId,
        ShareableInvitationViewModel shareableInvitationViewModel);

    Task<bool> ResendInvitationEmailAsync(string staffUserId, Staff adminUser);
}

public class Auth0CompanyService
    : IAuth0CompanyService
{
    private readonly SleekflowUserManager _userManager;
    private readonly ICoreService _coreService;
    private readonly ILogger<Auth0CompanyService> _logger;
    private readonly ApplicationDbContext _appDbContext;
    private readonly ICompanyTeamService _companyTeamService;
    private readonly ICompanyInfoCacheService _companyInfoCacheService;
    private readonly IMapper _mapper;
    private readonly IEmailNotificationService _emailNotificationService;
    private readonly IConfiguration _configuration;
    private readonly IDefaultSubscriptionPlanIdGetter _defaultSubscriptionPlanIdGetter;

    public Auth0CompanyService(
        SleekflowUserManager userManager,
        ILogger<Auth0CompanyService> logger,
        ICoreService coreService,
        ApplicationDbContext appDbContext,
        ICompanyInfoCacheService companyInfoCacheService,
        IMapper mapper,
        IEmailNotificationService emailNotificationService,
        ICompanyTeamService companyTeamService,
        IConfiguration configuration,
        IDefaultSubscriptionPlanIdGetter defaultSubscriptionPlanIdGetter)
    {
        _userManager = userManager;
        _logger = logger;
        _coreService = coreService;
        _appDbContext = appDbContext;
        _companyInfoCacheService = companyInfoCacheService;
        _mapper = mapper;
        _emailNotificationService = emailNotificationService;
        _companyTeamService = companyTeamService;
        _configuration = configuration;
        _defaultSubscriptionPlanIdGetter = defaultSubscriptionPlanIdGetter;
    }

    public async Task<string> GenerateResetPasswordLinkForStaff(string companyId, string staffIdentityId)
    {
        var userIdentity = await _userManager.FindByIdAsync(staffIdentityId);
        if (userIdentity is null)
        {
            throw new Exception($"The user staff of identity id {staffIdentityId} not found");
        }

        var userStaff = await _coreService.GetCompanyStaff(userIdentity);
        if (userStaff is null || userStaff.CompanyId != companyId)
        {
            throw new Exception($"User is not in your company");
        }

        var url = await _userManager.GeneratePasswordResetUrlAsync(userIdentity);
        return url;
    }

    public async Task<RegisterCompanyResult> RegisterCompanyAsync(
        ApplicationUser adminUser,
        RegisterCompanyInput registerCompanyInput)
    {
        try
        {
            await _coreService.CreateCompany(
                adminUser,
                new RegisterCompanyViewModel
                {
                    Id = registerCompanyInput.CompanyId,
                    CompanyName = registerCompanyInput.CompanyName,
                    CompanySize = registerCompanyInput.CompanySize,
                    PromotionCode = registerCompanyInput.PromotionCode,
                    PhoneNumber = registerCompanyInput.PhoneNumber,
                    SubscriptionPlanId = registerCompanyInput.SubscriptionPlanId,
                    HeardFrom = registerCompanyInput.HeardFrom,
                    lmref = registerCompanyInput.Lmref,
                    TimeZoneInfoId = registerCompanyInput.TimeZoneInfoId,
                    WebClientUUID = registerCompanyInput.WebClientUUID,
                    Referral = registerCompanyInput.Referral,
                    CompanyType = registerCompanyInput.CompanyType,
                    Industry = registerCompanyInput.Industry,
                    OnlineShopSystem = registerCompanyInput.OnlineShopSystem,
                    CommunicationTools = registerCompanyInput.CommunicationTools,
                    CompanyWebsite = registerCompanyInput.CompanyWebsite,
                });
        }
        catch (Exception e)
        {
            _logger.LogError(
                e,
                $"[{nameof(RegisterCompanyAsync)}]Create company error: {{ExceptionMessage}}",
                e.Message);
            throw;
        }

        var staff = await _coreService.GetCompanyStaff(adminUser);
        var freeBills = _appDbContext.CompanyBillRecords.Where(
            x =>
                x.CompanyId == staff.CompanyId && x.SubscriptionPlanId == "sleekflow_free").ToList();
        if (freeBills.Count > 0)
        {
            // change all free to freemium
            foreach (var free in freeBills)
            {
                // Default plan update: use "sleekflow_v10_startup" instead of "sleekflow_freemium" after Global Pricing release
                free.SubscriptionPlanId = _defaultSubscriptionPlanIdGetter.GetDefaultStartupPlanId();
                free.Status = BillStatus.Active;
            }

            await _appDbContext.SaveChangesAsync();
        }

        var associatedCompanyIds = await _appDbContext.UserRoleStaffs
            .Where(x => x.IdentityId == adminUser.Id)
            .Select(x => x.CompanyId)
            .ToListAsync();

        _logger.LogInformation(
                "Company created: {0}, \nStaff created: {1}, {2}, {3}",
                registerCompanyInput.CompanyName,
                staff.Identity.Id,
                staff.Identity.Email,
                staff.Company.SignalRGroupName);

        return new RegisterCompanyResult(
            staffId: staff.Id.ToString(),
            signalRGroupName: staff.Company.SignalRGroupName,
            isShopifyAccount: staff.Company.IsShopifyAccount,
            associatedCompanyIds: associatedCompanyIds,
            location: registerCompanyInput.Location);
    }



    public async Task<List<InvitedUserByEmailObject>> InviteUsersByEmailAsync(
        ApplicationUser? adminUser,
        List<long> teamIds,
        List<InviteUserInputObject> inviteUsers)
    {
        try
        {
            if (adminUser is null)
            {
                throw new Exception("[InviteUsersByEmailAsync] User not found");
            }

            var companyUser = await _coreService.GetCompanyStaff(adminUser);

            if (companyUser == null)
            {
                throw new Exception($"[InviteUsersByEmailAsync] User not found in company.");
            }

            var addedUsers = new List<InvitedUserByEmailObject>();
            foreach (var inviteUser in inviteUsers)
            {
                Auth0User? auth0User = null;
                var user = await _userManager.FindByEmailAsync(inviteUser.Email);
                if (user is null)
                {
                    if (inviteUser.TenantHubUserId is null)
                    {
                        throw new Exception("[InviteUsersByEmailAsync] TenantHubUserId is required");
                    }

                    // var identityResult = await _userManager.CreateAsync(
                    var result = await _userManager.CreateWithTenantHubIdAsync(
                        new ApplicationUser
                        {
                            Id = inviteUser.SleekflowUserId,
                            UserName = inviteUser.UserName ?? inviteUser.Email,
                            Email = inviteUser.Email,
                            FirstName = inviteUser.Firstname,
                            LastName = inviteUser.Lastname,
                            DisplayName = $"{inviteUser.Firstname} {inviteUser.Lastname}",
                            EmailConfirmed = true,
                        },
                        inviteUser.TenantHubUserId);
                    var identityResult = result.IdentityResult;

                    if (!identityResult.Succeeded)
                    {
                        _logger.LogError(
                            "[InviteUsersByEmailAsync] Company {CompanyId} create user {UserEmail} error: {Errors}",
                            companyUser.CompanyId,
                            inviteUser.Email,
                            JsonConvert.SerializeObject(identityResult.Errors));

                        throw new IdentityResultException(identityResult);
                    }

                    auth0User = result.Auth0User;
                    user = await _userManager.FindByEmailAsync(inviteUser.Email);
                }

                var userIsRegisteredCompany =
                    await _appDbContext.UserRoleStaffs.AnyAsync(u => u.IdentityId == user!.Id);
                if (userIsRegisteredCompany)
                {
                    _logger.LogError("[InviteUsersByEmailAsync] User ${Email} have registered a company", user!.Email);
                    throw new Exception($"User ${user!.Email} have registered a company");
                }

                // If user is no company associated, set it to invite. user for later checking
                if (!user.UserName.StartsWith("invite."))
                {
                    var changeUserNameResult = await _userManager.SetUserNameAsync(
                        user,
                        $"invite.{SleekflowUserManager.GenerateRandomString(22, false)}");

                    if (!changeUserNameResult.Succeeded)
                    {
                        _logger.LogError(
                            "[InviteUsersByEmailAsync] Company {CompanyId} set username failure for email {UserEmail} error: {Errors}",
                            companyUser.CompanyId,
                            user.Email,
                            JsonConvert.SerializeObject(changeUserNameResult.Errors));

                        throw new IdentityResultException(changeUserNameResult);
                    }
                }

                var staff = new Staff
                {
                    CompanyId = companyUser.CompanyId,
                    IdentityId = user.Id,
                    Identity = user,
                    Locale = "en",
                    RoleType = inviteUser.UserRole,
                    Position = inviteUser.Position,
                    TimeZoneInfoId = string.IsNullOrEmpty(inviteUser.TimeZoneInfoId)
                        ? companyUser.Company.TimeZoneInfoId
                        : inviteUser.TimeZoneInfoId,
                    NotificationSettingId = 1
                };
                await _appDbContext.UserRoleStaffs.AddAsync(staff);
                await _appDbContext.SaveChangesAsync();

                await _companyTeamService.AddOrRemoveTeam(
                    companyUser.CompanyId,
                    staff.Id,
                    teamIds);

                addedUsers.Add(
                    new InvitedUserByEmailObject(
                            user.Email!,
                            user.UserName,
                            user.FirstName,
                            user.LastName,
                            staff.RoleType.ToString(),
                            staff.Position,
                            staff.TimeZoneInfoId,
                            user.Id,
                            inviteUser.TenantHubUserId!,
                            staff.Id,
                            auth0User
                        ));

                var code = await _userManager.GenerateUserTokenAsync(
                    user,
                    SleekflowTokenProviderOptions.InviteTokenProviderName,
                    "InviteUsersByEmail");
                await _emailNotificationService.SendInvitationEmail(user, code, companyUser, inviteUser.TenantHubUserId);
                var staffId = await _appDbContext.UserRoleStaffs
                    .Where(x => x.CompanyId == companyUser.CompanyId)
                    .OrderBy(x => x.Order)
                    .ThenBy(x => x.Id)
                    .Select(x => x.IdentityId)
                    .FirstOrDefaultAsync();
            }

            await _companyInfoCacheService.RemoveCompanyInfoCache(companyUser.CompanyId);
            return addedUsers;
        }
        catch (Exception e)
        {
            // Remove all added users when invite failed.
            foreach (var inviteUser in inviteUsers)
            {
                var user = await _userManager.FindByIdAsync(inviteUser.SleekflowUserId);
                if (user is not null)
                {
                    var staff = await _coreService.GetCompanyStaff(user);
                    if (staff is not null)
                    {
                        await _coreService.RemoveStaffData(staff.CompanyId, staff.Id);
                    }
                    else
                    {
                        await _userManager.DeleteAsync(user);
                    }
                }
            }

            if (e is IdentityResultException)
            {
                _logger.LogError(
                    "[{InviteUsersByEmailAsyncName}] e.Message, InnerException: {InnerExceptionMessage}",
                    nameof(InviteUsersByEmailAsync),
                    e.InnerException?.Message);
                throw;
            }

            _logger.LogError(
                e,
                "[{InviteUsersByEmailAsyncName}] error: {EMessage}",
                nameof(InviteUsersByEmailAsync),
                e.Message);
            throw new Exception($"Invite user failed: {e.Message}", e);
        }
    }

    public async Task<(ShareableInvitation, string)> GenerateShareableLinkAsync(
        string adminUserId, ShareableInvitationViewModel shareableInvitationViewModel)
    {
       var companyUser = await _coreService.GetCompanyStaff(
                await _userManager.FindByIdAsync(adminUserId));

       if (companyUser == null)
       {
           _logger.LogError($"{nameof(GenerateShareableLinkAsync)} UnauthorizedAccessException: Access denied");
           throw new UnauthorizedAccessException("User Unauthorized.");
       }

       if (companyUser.RoleType == StaffUserRole.Staff)
       {
           _logger.LogError($"{nameof(GenerateShareableLinkAsync)} Access denied");
           throw new SecurityException("Access denied for staff role");
       }

       var invitation = await _appDbContext.CompanyShareableInvitations
           .Include(x => x.ShareableInvitationRecords)
           .ThenInclude(x => x.InvitedStaff.Identity)
           .FirstOrDefaultAsync(
               x =>
                   x.TeamIds == shareableInvitationViewModel.TeamIds
                   && x.Role == shareableInvitationViewModel.Role
                   && x.GeneratedById == companyUser.Id
                   && x.Status == ShareableLinkStatus.Enabled);

       if (invitation == null)
       {
           invitation = new ShareableInvitation
           {
               CompanyId = companyUser.CompanyId,
               Role = shareableInvitationViewModel.Role,
               TeamIds = shareableInvitationViewModel.TeamIds,
               Quota = shareableInvitationViewModel.Quota,
               ExpirationDate = shareableInvitationViewModel.ExpirationDate.ToUniversalTime(),
               GeneratedById = companyUser.Id,
               Status = ShareableLinkStatus.Enabled,
           };

           _appDbContext.CompanyShareableInvitations.Add(invitation);
       }

       invitation.Role = shareableInvitationViewModel.Role;
       invitation.TeamIds = shareableInvitationViewModel.TeamIds;
       invitation.Quota = shareableInvitationViewModel.Quota;
       invitation.ExpirationDate = shareableInvitationViewModel.ExpirationDate;
       invitation.UpdatedAt = DateTime.UtcNow;

       await _appDbContext.SaveChangesAsync();

       var location = _configuration["SF_ENVIRONMENT"] ?? string.Empty;

       return (invitation, location);
    }

    public async Task<bool> ResendInvitationEmailAsync(string staffUserId, Staff adminStaff)
    {
        var user = await _userManager.FindByIdAsync(staffUserId);

        var code = await _userManager.GenerateUserTokenAsync(
            user,
            SleekflowTokenProviderOptions.InviteTokenProviderName,
            "ResendInvitationEmail");
        await _emailNotificationService.SendInvitationEmail(user, code, adminStaff);

        return true;
    }
}

