using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using NReco.VideoConverter;
using Travis_backend.Constants;
using Travis_backend.ConversationDomain.Models;
using Travis_backend.ConversationServices;
using Travis_backend.Database;
using Travis_backend.FileDomain.Services;
using Travis_backend.MessageDomain.Models;
using Travis_backend.OpenTelemetry.Meters;
using Travis_backend.OpenTelemetry.Meters.Constants;
using Twilio;
using Twilio.Rest.Api.V2010.Account;
using Twilio.Types;

namespace Travis_backend.MessageDomain.ChannelMessageProvider.ChannelMessageHandlers;

public class WhatsappTwilioChannelMessageHandler : IChannelMessageHandler
{
    private readonly ApplicationDbContext _appDbContext;
    private readonly ITwilioService _twilioService;
    private readonly IConfiguration _configuration;
    private readonly ILogger<WhatsappTwilioChannelMessageHandler> _logger;
    private readonly IMediaProcessService _mediaProcessService;
    private readonly IConversationMeters _conversationMeters;

    public string ChannelType => ChannelTypes.WhatsappTwilio;

    public WhatsappTwilioChannelMessageHandler(
        ApplicationDbContext appDbContext,
        ITwilioService twilioService,
        IConfiguration configuration,
        ILogger<WhatsappTwilioChannelMessageHandler> logger,
        IMediaProcessService mediaProcessService,
        IConversationMeters conversationMeters)
    {
        _appDbContext = appDbContext;
        _twilioService = twilioService;
        _configuration = configuration;
        _logger = logger;
        _mediaProcessService = mediaProcessService;
        _conversationMeters = conversationMeters;
    }

    public async ValueTask<ConversationMessage> SendChannelMessageAsync(
        Conversation conversation,
        ConversationMessage conversationMessage)
    {
        if (conversationMessage.whatsappReceiver != null
            && string.IsNullOrEmpty(conversationMessage.MessageUniqueID))
        {
            conversationMessage.ChannelIdentityId = conversationMessage.whatsappReceiver.ChannelIdentityId;
            string messageContent = string.Empty;

            if (conversationMessage.TranslationResults != null)
            {
                messageContent = conversationMessage.TranslationResults
                    .FirstOrDefault().translations
                    .FirstOrDefault().text;
            }
            else
            {
                messageContent = conversationMessage.MessageContent;
            }

            if (conversationMessage.whatsappReceiver.whatsAppId.ToLower().Contains("whatsapp"))
            {
                var accountSID = string.Empty;
                var accountSecret = string.Empty;
                var isSubAccount = false;
                string messagingServiceSid = null;

                List<Uri> mediaURI = new List<Uri>();

                var domainName = _configuration.GetValue<String>("Values:DomainName");

                var messageSender = string.Empty;

                if (conversation.IsSandbox)
                {
                    conversationMessage.IsSandbox = true;

                    var twilioSandboxConfig = await _appDbContext.CoreSandboxTwilioConfigs
                        .FirstOrDefaultAsync();

                    // TwilioClient.Init(twilioSandboxConfig.TwilioAccountSid, twilioSandboxConfig.TwilioSecret);
                    accountSID = twilioSandboxConfig.TwilioAccountSid;
                    accountSecret = twilioSandboxConfig.TwilioSecret;
                    messageSender = twilioSandboxConfig.PhoneNumber;

                    if (!await _appDbContext.SenderSandboxSenders
                            .AnyAsync(
                                x =>
                                    x.CompanyId == conversation.CompanyId
                                    && x.phone_number == conversationMessage.whatsappReceiver.whatsAppId
                                        .Replace("whatsapp:+", string.Empty)))
                    {
                        conversationMessage.Status = MessageStatus.Failed;
                        conversationMessage.ChannelStatusMessage = "Not in sandbox";

                        return conversationMessage;
                    }
                }
                else
                {
                    try
                    {
                        var twilioConfig = await _appDbContext.ConfigWhatsAppConfigs
                            .FirstOrDefaultAsync(
                                x =>
                                    x.CompanyId == conversation.CompanyId
                                    && x.WhatsAppSender == conversationMessage.whatsappReceiver.InstaneSender
                                    && x.TwilioAccountId == conversationMessage.whatsappReceiver.InstanceId);

                        if (twilioConfig == null)
                        {
                            twilioConfig = await _appDbContext.ConfigWhatsAppConfigs
                                .FirstOrDefaultAsync(
                                    x =>
                                        x.CompanyId == conversation.CompanyId
                                        && x.TwilioAccountId == conversationMessage.whatsappReceiver.InstanceId);
                        }

                        if (twilioConfig == null)
                        {
                            twilioConfig = await _appDbContext.ConfigWhatsAppConfigs
                                .FirstOrDefaultAsync(x => x.CompanyId == conversation.CompanyId);
                        }

                        accountSID = twilioConfig.TwilioAccountId;
                        accountSecret = twilioConfig.TwilioSecret;
                        isSubAccount = twilioConfig.IsSubaccount;

                        messagingServiceSid = string.IsNullOrEmpty(twilioConfig.MessagingServiceSid)
                            ? null
                            : twilioConfig.MessagingServiceSid;

                        // TwilioClient.Init(twilioConfig?.TwilioAccountId, twilioConfig?.TwilioSecret);
                        messageSender = twilioConfig.WhatsAppSender;

                        conversationMessage.whatsappSender = await _appDbContext.SenderWhatsappSenders
                            .FirstOrDefaultAsync(x => x.whatsAppId == messageSender);
                    }
                    catch (Exception ex)
                    {
                        conversationMessage.Status = MessageStatus.Failed;
                        conversationMessage.ChannelStatusMessage = "Cannot send " + ex.Message;

                        await _appDbContext.SaveChangesAsync();

                        return conversationMessage;
                    }
                }

                try
                {
                    if (isSubAccount)
                    {
                        var record = await _appDbContext.CompanyTwilioUsageRecords
                            .FirstOrDefaultAsync(x => x.TwilioAccountId == accountSID);

                        if (record?.Balance < 0)
                        {
                            conversationMessage.Status = MessageStatus.OutOfCredit;
                            conversationMessage.ChannelStatusMessage = "Out of credit, please charge";

                            await _appDbContext.SaveChangesAsync();

                            return conversationMessage;
                        }
                    }

                    // Add TwilioContentObject for frontend display
                    try
                    {
                        if (conversationMessage.ExtendedMessagePayload?.ExtendedMessageType ==
                            ExtendedMessageType.TwilioContentApi)
                        {
                            conversationMessage.ExtendedMessagePayload.ExtendedMessagePayloadDetail
                                    .WhatsappTwilioContentApiObject.TwilioContentObject =
                                await _twilioService.GetTemplateByContentApiAsync(
                                    accountSID,
                                    accountSecret,
                                    conversationMessage.ExtendedMessagePayload.ExtendedMessagePayloadDetail
                                        .WhatsappTwilioContentApiObject.ContentSid);

                            // Update ExtendedMessagePayload
                            await _appDbContext.ExtendedMessagePayloads
                                .Where(x => x.Id == conversationMessage.ExtendedMessagePayload.Id)
                                .ExecuteUpdateAsync(
                                    payload =>
                                        payload.SetProperty(
                                            p => p.ExtendedMessagePayloadDetail,
                                            conversationMessage.ExtendedMessagePayload.ExtendedMessagePayloadDetail));
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(
                            ex,
                            "[{MethodName}] Update Twilio message ExtendedMessagePayload in DB exception for conversation {ConversationId}: {ExceptionMessage}",
                            nameof(SendChannelMessageAsync),
                            conversation?.Id,
                            ex.Message);
                    }

                    MessageResource whatsappMessage = null;
                    var isSentImageWithCaption = false;

                    foreach (var uploadedFile in conversationMessage.UploadedFiles)
                    {
                        switch (conversationMessage.MessageType)
                        {
                            case "file":
                                // convert to mp4
                                if (uploadedFile.MIMEType.Contains("video")
                                    && !uploadedFile.MIMEType.Contains("mp4"))
                                {
                                    await _mediaProcessService.ProcessMedia(
                                        conversation.Id,
                                        uploadedFile,
                                        Format.mp4);
                                }

                                if ((uploadedFile.MIMEType.Contains("audio")
                                     && !uploadedFile.MIMEType.Contains("mp3"))
                                    || Path.GetExtension(uploadedFile.Filename) == ".webm"
                                    || Path.GetExtension(uploadedFile.Filename) == ".bin")
                                {
                                    await _mediaProcessService.ProcessMedia(conversation.Id, uploadedFile, "mp3");
                                }

                                break;
                            case "voice":
                                if (uploadedFile.MIMEType.Contains("audio")
                                    && !uploadedFile.MIMEType.Contains("mp3"))
                                {
                                    await _mediaProcessService.ProcessMedia(conversation.Id, uploadedFile, "mp3");
                                }

                                break;
                        }

                        // var filename = Path.GetFileName(uploadedFile.Filename);
                        // mediaURI.Add(new Uri(_azureBlobStorageService.GetAzureBlobSasUri(uploadedFile.Filename, uploadedFile.BlobContainer, 24)));
                        mediaURI.Add(new Uri($"{domainName}/Message/File/Private/{uploadedFile.FileId}"));

                        if (conversationMessage.UploadedFiles.Count == 1
                            && uploadedFile.MIMEType.Contains("image"))
                        {
                            TwilioClient.Init(accountSID, accountSecret);

                            whatsappMessage = MessageResource.Create(
                                from: new PhoneNumber(messageSender),
                                to: new PhoneNumber(conversationMessage.whatsappReceiver.whatsAppId),
                                mediaUrl: mediaURI,
                                body: messageContent,
                                messagingServiceSid: messagingServiceSid,
                                contentSid: conversationMessage.ExtendedMessagePayload?.ExtendedMessagePayloadDetail
                                    ?.WhatsappTwilioContentApiObject?.ContentSid,
                                contentVariables: conversationMessage.ExtendedMessagePayload
                                    ?.ExtendedMessagePayloadDetail?.WhatsappTwilioContentApiObject
                                    ?.ContentVariablesJson,
                                statusCallback: new Uri($"{domainName}/twilio/webhook/status"));
                            isSentImageWithCaption = true;

                            break;
                        }

                        TwilioClient.Init(accountSID, accountSecret);

                        whatsappMessage = await MessageResource.CreateAsync(
                            from: new PhoneNumber(messageSender),
                            to: new PhoneNumber(conversationMessage.whatsappReceiver.whatsAppId),
                            mediaUrl: mediaURI,
                            messagingServiceSid: messagingServiceSid,
                            contentSid: conversationMessage.ExtendedMessagePayload?.ExtendedMessagePayloadDetail
                                ?.WhatsappTwilioContentApiObject?.ContentSid,
                            contentVariables: conversationMessage.ExtendedMessagePayload
                                ?.ExtendedMessagePayloadDetail?.WhatsappTwilioContentApiObject
                                ?.ContentVariablesJson,
                            statusCallback: new Uri($"{domainName}/twilio/webhook/status"));

                        mediaURI = new List<Uri>();
                    }

                    var isContentApiTemplate = conversationMessage.ExtendedMessagePayload
                        ?.ExtendedMessagePayloadDetail
                        ?.WhatsappTwilioContentApiObject != null;
                    var isTextMessage = !string.IsNullOrEmpty(messageContent);

                    // Send Text message
                    if (isTextMessage && !isSentImageWithCaption && !isContentApiTemplate)
                    {
                        TwilioClient.Init(accountSID, accountSecret);

                        whatsappMessage = await MessageResource.CreateAsync(
                            from: new PhoneNumber(messageSender),
                            to: new PhoneNumber(conversationMessage.whatsappReceiver.whatsAppId),
                            body: messageContent,
                            messagingServiceSid: messagingServiceSid,
                            statusCallback: new Uri($"{domainName}/twilio/webhook/status"));
                    }

                    try
                    {
                        // Send Content Api template message
                        if (isContentApiTemplate)
                        {
                            TwilioClient.Init(accountSID, accountSecret);

                            whatsappMessage = await MessageResource.CreateAsync(
                                from: new PhoneNumber(messageSender),
                                to: new PhoneNumber(conversationMessage.whatsappReceiver.whatsAppId),
                                messagingServiceSid: messagingServiceSid,
                                contentSid: conversationMessage.ExtendedMessagePayload?.ExtendedMessagePayloadDetail
                                    .WhatsappTwilioContentApiObject.ContentSid,
                                contentVariables: conversationMessage.ExtendedMessagePayload
                                    .ExtendedMessagePayloadDetail.WhatsappTwilioContentApiObject
                                    ?.ContentVariablesJson,
                                statusCallback: new Uri($"{domainName}/twilio/webhook/status"));

                            if (string.IsNullOrEmpty(conversationMessage.MessageContent))
                            {
                                conversationMessage.MessageContent =
                                    (await MessageResource.FetchAsync(whatsappMessage.Sid)).Body;
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(
                            ex,
                            "[{MethodName}] Failed to send Content Api template message for conversation {ConversationId}. {ExceptionToString}",
                            nameof(SendChannelMessageAsync),
                            conversation?.Id,
                            ex.ToString());
                    }

                    try
                    {
                        if (!conversation.IsSandbox)
                        {
                            if (conversationMessage.whatsappReceiver.InstanceId != accountSID ||
                                !await _appDbContext.SenderWhatsappSenders
                                    .AnyAsync(
                                        x =>
                                            x.whatsAppId == conversationMessage.whatsappReceiver.whatsAppId
                                            && x.CompanyId == conversationMessage.CompanyId
                                            && x.InstanceId == accountSID))
                            {
                                conversationMessage.whatsappReceiver.InstanceId = accountSID;
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(
                            ex,
                            "[{MethodName}] Unable to update instanceId for conversation {ConversationId}: {ExceptionMessage}",
                            nameof(SendChannelMessageAsync),
                            conversation?.Id,
                            ex.Message);
                    }

                    conversationMessage.MessageUniqueID = whatsappMessage.Sid;

                    conversationMessage.Status =
                        (whatsappMessage.Status == MessageResource.StatusEnum.Queued ||
                         whatsappMessage.Status == MessageResource.StatusEnum.Accepted) ? MessageStatus.Sent :
                        (whatsappMessage.Status == MessageResource.StatusEnum.Delivered) ? MessageStatus.Received :
                        (whatsappMessage.Status == MessageResource.StatusEnum.Read) ? MessageStatus.Read :
                        MessageStatus.Failed;

                    // Remove the hotfix
                    // if (conversationMessage.Status == MessageStatus.Sent && conversationMessage.DeliveryType != DeliveryType.ReadMore)
                    // {
                    //     //if the message status is Sent, let schedule a checker to check the Status will change to deliver or not
                    //     BackgroundJob.Schedule(() => CheckTwilioStatusIsRemainSend(conversationMessage.Id), TimeSpan.FromSeconds(20));
                    // }
                    _logger.LogInformation(
                        "Outgoing MessageId from Twilio for conversation {ConversationId}: {WhatsappMessageSid}",
                        conversation?.Id,
                        whatsappMessage.Sid);

                    if (whatsappMessage.ErrorCode.HasValue)
                    {
                        try
                        {
                            conversationMessage.ChannelStatusMessage =
                                $"{whatsappMessage.ErrorCode} {whatsappMessage.ErrorMessage}";
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(
                                ex,
                                "[{MethodName}] Conversation {ConversationId} error updating channel status message for message {MessageId}. {ExceptionMessage}",
                                nameof(SendChannelMessageAsync),
                                conversation?.Id,
                                conversationMessage?.Id,
                                ex.Message);
                        }
                    }

                    // double price = 0.0;
                    // double.TryParse(whatsappMessage.Price, out price);
                    // if (price == 0.0) price = -0.005;
                    // conversationMessage.Price = price;
                }
                catch (Exception ex)
                {
                    if (ex is Twilio.Exceptions.ApiException twilioException)
                    {
                        conversationMessage.Metadata ??= new Dictionary<string, object>();
                        conversationMessage.Metadata.Add(
                            "errors",
                            new List<ConversationMessageError>
                            {
                                new ()
                                {
                                    Code = twilioException.Code.ToString(),
                                    Message = twilioException.Message,
                                    InnerError = new
                                    {
                                        Code = twilioException.Code,
                                        Status = twilioException.Status,
                                        MoreInfo = twilioException.MoreInfo,
                                        Details = twilioException.Details,
                                        Data = twilioException.Data,
                                        Message = twilioException.Message,
                                    }
                                }
                            });
                    }

                    _logger.LogError(
                        ex,
                        "[{MethodName}] Unable to send twilio message for conversation {ConversationId}. {ExceptionToString}",
                        nameof(SendChannelMessageAsync),
                        conversation?.Id,
                        ex.ToString());

                    conversationMessage.Status = MessageStatus.Failed;
                }
            }

            await _appDbContext.SaveChangesAsync();
        }
        else
        {
            if (conversationMessage.whatsappReceiver != null)
            {
                conversationMessage.ChannelIdentityId = conversationMessage.whatsappReceiver.ChannelIdentityId;
            }
            else if (conversationMessage.whatsappSender != null)
            {
                conversationMessage.ChannelIdentityId = conversationMessage.whatsappSender.ChannelIdentityId;
            }

            conversationMessage.Status = MessageStatus.Received;
        }

        _conversationMeters.IncrementCounter(
            ChannelTypes.LiveChat,
            conversationMessage.Status != MessageStatus.Failed
                ? ConversationMeterOptions.SendSuccess
                : ConversationMeterOptions.SendFailed);

        return conversationMessage;
    }
}