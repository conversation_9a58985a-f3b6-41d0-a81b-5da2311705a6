using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Stripe;
using Travis_backend.Extensions;

namespace Travis_backend.StripeIntegrationDomain.Services;

public class StripeSubscriptionService : IStripeSubscriptionService
{
    /// <summary>
    /// IStripeServicesFactory.
    /// </summary>
    private readonly IStripeServicesFactory _stripeServicesFactory;

    /// <summary>
    /// ILogger.
    /// </summary>
    private readonly ILogger<StripeSubscriptionService>_logger;

    /// <summary>
    /// Initializes a new instance of the <see cref="StripeSubscriptionService"/> class.
    /// </summary>
    /// <param name="stripeServicesFactory">IStripeServicesFactory.</param>
    /// <param name="logger">ILogger.</param>
    public StripeSubscriptionService(IStripeServicesFactory stripeServicesFactory, ILogger<StripeSubscriptionService> logger)
    {
        _stripeServicesFactory = stripeServicesFactory;
        _logger = logger;
    }

    /// <inheritdoc />
    public Task<Subscription> GetSubscriptionAsync(string stripeSubscriptionId)
    {
        var subscriptionService = _stripeServicesFactory.Create<SubscriptionService>();
        return subscriptionService.GetAsync(stripeSubscriptionId);
    }

    /// <inheritdoc />
    public Task<Subscription> CancelSubscriptionAsync(string stripeSubscriptionId, bool prorate = true, bool invoiceNow = false)
    {
        var subscriptionService = _stripeServicesFactory.Create<SubscriptionService>();

        SubscriptionCancelOptions subscriptionCancelOptions = new SubscriptionCancelOptions();
        subscriptionCancelOptions.Prorate = prorate;
        subscriptionCancelOptions.InvoiceNow = invoiceNow;

        return subscriptionService.CancelAsync(stripeSubscriptionId, subscriptionCancelOptions);
    }

    /// <inheritdoc />
    public async Task<bool> IsSubscriptionCancelledWithProrate(string customerId, string subscriptionId, IEnumerable<string> subscriptionPlanIds)
    {
        _logger.LogInformation("Check whether Subscription is Cancelled with Prorate. SubscriptionId: {SubscriptionId}, Items: {Item}", subscriptionId, subscriptionPlanIds);

        #region Check in Subscription's Invoice

        //// Handle Subscription Cancellation via API with Prorate = True, InvoiceNow = True
        var subscriptionService = _stripeServicesFactory.Create<SubscriptionService>();

        var subscriptionGetOptions = new SubscriptionGetOptions();
        subscriptionGetOptions.AddExpand("latest_invoice");

        var subscription = await subscriptionService.GetAsync(subscriptionId, subscriptionGetOptions);

        if (subscription.Status != SubscriptionStatuses.Canceled)
        {
            return false;
        }

        var isLatestInvoiceHasProrateItem = subscription.LatestInvoice.Lines.Any(x => x.Proration && x.Amount < 0 && subscriptionPlanIds.ContainsIgnoreCase(x.Plan.Id));

        _logger.LogInformation(
            "Is Prorate Items in Latest Invoice. InvoiceId: {InvoiceId}, Result: {Result}",
            subscription.LatestInvoiceId, isLatestInvoiceHasProrateItem);

        if (isLatestInvoiceHasProrateItem)
        {
            return true;
        }

        #endregion

        #region Check in Pending Invoice Items

        //// Handle Subscription Cancellation via API with Prorate = True, InvoiceNow = False
        var invoiceItemService = _stripeServicesFactory.Create<InvoiceItemService>();

        var invoiceItemListOptions = new InvoiceItemListOptions();
        invoiceItemListOptions.Customer = subscription.CustomerId;
        invoiceItemListOptions.Pending = true;

        var pendingInvoiceItems = await invoiceItemService.ListAsync(invoiceItemListOptions);
        var proratedPendingInvoiceItems = pendingInvoiceItems.Where(x => x.Proration && x.Amount < 0 && subscriptionPlanIds.ContainsIgnoreCase(x.Plan.Id));

        _logger.LogInformation(
            "Is Prorate Items in Pending Invoice Item. ProratedInvoiceItem: {ProratedInvoiceItem}",
            string.Join(",", proratedPendingInvoiceItems.Select(x => x.Plan.Id)));

        if (proratedPendingInvoiceItems.Any())
        {
            return true;
        }

        #endregion

        #region Check in Credit Notes

        //// Handle Subscription Cancellation via Stripe Dashboard with Option 'Immediately' & 'Refund'
        //// For more details, observe the API calls in Stripe's Dashboard when Cancel
        var creditNoteService = _stripeServicesFactory.Create<CreditNoteService>();

        CreditNoteListOptions creditNoteListOptions = new CreditNoteListOptions();
        creditNoteListOptions.Invoice = subscription.LatestInvoiceId;

        var hasCreditNoteRefund = false;

        //// Retry for query CreditNote to prevent CreditNote creation delayed issue on Stripe
        foreach (var i in Enumerable.Range(1, 10))
        {
            var creditNotes = await creditNoteService.ListAsync(creditNoteListOptions);
            hasCreditNoteRefund = creditNotes.Any(x => x.Amount > 0);

            if (hasCreditNoteRefund == true)
            {
                break;
            }

            _logger.LogInformation(
                "CreditNote Not Found for Invoice: \"{InvoiceIde}\", Tried: {I}",
                subscription.LatestInvoiceId,
                i);

            Thread.Sleep(TimeSpan.FromSeconds(2));
        }

        _logger.LogInformation(
            "Is Prorate Items in Credit Note. InvoiceId: {InvoiceId}, HasRefund: {HasRefund}",
            subscription.LatestInvoiceId, hasCreditNoteRefund);

        return hasCreditNoteRefund;

        #endregion
    }

    /// <inheritdoc />
    public async Task DeleteSubscriptionItemAsync(string stripeSubscriptionId, string subscriptionItemId)
    {
        var subscription = await GetSubscriptionAsync(stripeSubscriptionId);

        if (subscription.Items.Data.All(x => x.Id != subscriptionItemId))
        {
            throw new ApplicationException($"SubscriptionItemId: {subscriptionItemId} does not exist in Subscription: {stripeSubscriptionId}");
        }

        var subscriptionItemService = _stripeServicesFactory.Create<SubscriptionItemService>();

        var subscriptionItemDeleteOptions = new SubscriptionItemDeleteOptions();
        subscriptionItemDeleteOptions.ProrationBehavior = "always_invoice";

        await subscriptionItemService.DeleteAsync(subscriptionItemId, subscriptionItemDeleteOptions);
    }
}