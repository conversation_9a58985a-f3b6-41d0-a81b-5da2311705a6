using System.IdentityModel.Tokens.Jwt;
using System.Text;
using Auth0.ManagementApi.Models;
using Auth0.AuthenticationApi.Models;
using AutoMapper;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.IdentityModel.Tokens;
using Newtonsoft.Json;
using Sleekflow.Apis.TenantHub.Api;
using Sleekflow.Apis.TenantHub.Model;
using Travis_backend.AccountAuthenticationDomain.Models;
using Travis_backend.Auth0.Configuration;
using Travis_backend.Auth0.Models;
using Travis_backend.Auth0.Services;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.Configuration;
using Travis_backend.ConversationServices;
using Travis_backend.Database;
using Travis_backend.Enums;
using Travis_backend.SignalR;
using User = Auth0.ManagementApi.Models.User;

namespace Travis_backend.Auth0.Controllers.Webhook;

[Route("auth0")]
public class Auth0ActionEventController : Controller
{
    private readonly IMapper _mapper;
    private readonly ILogger<Auth0ActionEventController> _logger;
    private readonly SleekflowUserManager _userManager;
    private readonly IConfiguration _configuration;
    private readonly Auth0Config _auth0Config;
    private readonly ApplicationDbContext _appDbContext;
    private readonly ISignalRService _signalRService;
    private readonly ICoreService _coreService;
    private readonly IManagementEnabledFeaturesApi _managementEnabledFeaturesApi;
    private readonly IManagementFeaturesApi _managementFeaturesApi;
    private readonly IManagementIpWhitelistsApi _managementIpWhitelistsApi;

    private const string SecretKeyEndStr = "+wsadz4gI_3DUXI8P";

    public Auth0ActionEventController(
        ApplicationDbContext appDbContext,
        ISignalRService signalRService,
        IMapper mapper,
        ILogger<Auth0ActionEventController> logger,
        UserManager<ApplicationUser> userManager,
        IConfiguration configuration,
        Auth0Config auth0Config,
        ICoreService coreService,
        IManagementEnabledFeaturesApi managementEnabledFeaturesApi,
        IManagementFeaturesApi managementFeaturesApi,
        IManagementIpWhitelistsApi managementIpWhitelistsApi)
    {
        _appDbContext = appDbContext;
        _signalRService = signalRService;
        _mapper = mapper;
        _logger = logger;
        _userManager = (SleekflowUserManager) userManager;
        _configuration = configuration;
        _auth0Config = auth0Config;
        _coreService = coreService;
        _managementEnabledFeaturesApi = managementEnabledFeaturesApi;
        _managementFeaturesApi = managementFeaturesApi;
        _managementIpWhitelistsApi = managementIpWhitelistsApi;
    }

    internal async Task<bool> ValidateToken(string jwt)
    {
        if (string.IsNullOrEmpty(jwt)) return false;

        var jwtHandler = new JwtSecurityTokenHandler();

        // The secretKey
        // 1. No conflict with different environment. (e.g. uat only work in uat only and not work with production)
        // 2. Audience + random string can improve security.
        var secretKey = _configuration["Auth0:ActionIssuer"] + _configuration["Auth0:ActionAudience"] + SecretKeyEndStr;

        var tokenValidate = new TokenValidationParameters
        {
            ValidateLifetime = false,
            ValidateAudience = true,
            ValidateIssuer = true,
            ValidIssuer = _configuration["Auth0:ActionIssuer"],
            ValidAudience = _configuration["Auth0:ActionAudience"],
            RequireExpirationTime = true,
            RequireSignedTokens = true,
            IssuerSigningKey =
                new SymmetricSecurityKey(
                    Encoding.ASCII.GetBytes(
                        secretKey))
        };
        var principal = await jwtHandler.ValidateTokenAsync(jwt, tokenValidate);

        return principal.IsValid;
    }

    public class PostUserLoginResponse
    {
        [JsonProperty("auth0_user_id")]
        public string Auth0UserId { get; set; }

        [JsonProperty("app_metadata")]
        public Auth0AppMetadata? AppMetadata { get; set; }

        [JsonProperty("status")]
        public string Status { get; set; }

        public PostUserLoginResponse(string auth0UserId, Auth0AppMetadata? appMetadata, string status)
        {
            Auth0UserId = auth0UserId;
            AppMetadata = appMetadata;
            Status = status;
        }
    }

    public static class PostUserLoginResponseStatus
    {
        public const string Unchanged = "unchanged";
        public const string MergedByAuth0UserId = "merged_by_auth0_user_id";
        public const string MergedByEmail = "merged_by_email";
        public const string ErrorConflictUsername = "error_conflict_username";
        public const string ErrorConflictEmailAndNotVerified = "error_conflict_email_and_not_verified";
        public const string ErrorEmailAndNotVerified = "error_email_and_not_verified";
        public const string ErrorUserMustCompleteInvitation = "error_user_must_complete_invitation";
        public const string NewUser = "new_user";
        public const string ErrorNotValidEnterpriseUser = "error_not_valid_enterprise_user";
        public const string MergedByEnterpriseImportedUser = "merged_by_enterprise_imported_user";

        public static readonly string[] AllStatuses =
        {
            Unchanged,
            MergedByAuth0UserId,
            MergedByEmail,
            ErrorConflictUsername,
            ErrorConflictEmailAndNotVerified,
            ErrorEmailAndNotVerified,
            ErrorUserMustCompleteInvitation,
            NewUser,
            ErrorNotValidEnterpriseUser,
            MergedByEnterpriseImportedUser
        };
    }

    [HttpPost("PostUserLogin")]
    [ProducesResponseType(typeof(PostUserLoginResponse), StatusCodes.Status200OK)]
    public async Task<ActionResult<PostUserLoginResponse>> PostUserLogin(
        [FromQuery(Name = "connection_strategy")]
        string? connectionStrategy,
        [FromQuery]
        string token,
        [FromBody]
        User auth0EventUser)
    {
        _logger.LogInformation(
            "PostUserLogin {Auth0EventUser},\nConnection Provider: {Connection}",
            JsonConvert.SerializeObject(auth0EventUser, Formatting.None),
            connectionStrategy);

        var isValid = await ValidateToken(token);
        if (!isValid)
        {
            return NotFound();
        }

        try
        {
            // Map Imported Enterprise User by Connection and Email
            if (connectionStrategy == "adfs" ||
                connectionStrategy == "oidc" )
            {
                var possibleImportedEnterpriseUser = await _userManager.FindByEmailAsync(auth0EventUser.Email);
                if (possibleImportedEnterpriseUser is not null)
                {
                    var (updatedAuth0User, _) =
                        await _userManager.AssociateDbUserWithAuth0UserOnAuth0(
                            auth0EventUser,
                            possibleImportedEnterpriseUser);

                    possibleImportedEnterpriseUser.LastLoginAt = DateTime.UtcNow;
                    possibleImportedEnterpriseUser.EmailConfirmed = auth0EventUser.EmailVerified.GetValueOrDefault();

                    var auth0AppMetadata = SleekflowUserManager.GetAuth0AppMetadata(updatedAuth0User);

                    return Ok(
                        new PostUserLoginResponse(
                            auth0EventUser.UserId,
                            auth0AppMetadata,
                            PostUserLoginResponseStatus.MergedByEnterpriseImportedUser));
                }

                // The handling of not valid enterprise user
                _logger.LogInformation(
                    "PostUserLogin {Auth0EventUser}. Not valid enterprise user",
                    JsonConvert.SerializeObject(auth0EventUser, Formatting.None));

                throw new Exception(PostUserLoginResponseStatus.ErrorNotValidEnterpriseUser);
            }
            var tuple = await _userManager.FindByAuth0User(auth0EventUser);
            if (tuple != null)
            {
                var applicationUser = tuple.Value.Item1;
                var auth0AppMetadata = tuple.Value.Item2;

                // Make sure all invited user has completed the registration
                if (applicationUser.UserName!.StartsWith("invite."))
                {
                    _logger.LogInformation(
                        "PostUserLogin {Auth0EventUser}. User has not complete the invitation",
                        JsonConvert.SerializeObject(auth0EventUser, Formatting.None));

                    throw new Exception(PostUserLoginResponseStatus.ErrorUserMustCompleteInvitation);
                }

                // Need to make sure the email is verified to let the user login
                if (auth0AppMetadata.LoginRequiresEmailVerification is true
                    && auth0EventUser.EmailVerified is not true)
                {
                    _logger.LogInformation(
                        "PostUserLogin {Auth0EventUser}. You need to first verify your email",
                        JsonConvert.SerializeObject(auth0EventUser, Formatting.None));

                    await _userManager.SendVerifyEmailAsync(auth0EventUser);

                    throw new Exception(PostUserLoginResponseStatus.ErrorEmailAndNotVerified);
                }

                // Make sure the last login time has stored in user db.
                applicationUser.LastLoginAt = DateTime.UtcNow;
                applicationUser.EmailConfirmed = auth0EventUser.EmailVerified.GetValueOrDefault();
                await _userManager.UpdateDbUserAsync(applicationUser);

                return Ok(
                    new PostUserLoginResponse(
                        auth0EventUser.UserId,
                        tuple.Value.Item2,
                        PostUserLoginResponseStatus.Unchanged));
            }

            // Map Auth0User to DbUser if matched the id (the user is by import)
            // auth|0j2093jg90j90jsd90oii -> 0j2093jg90j90jsd90oii
            var possibleDbUserId = auth0EventUser.UserId.Split("|").ElementAtOrDefault(1);
            if (possibleDbUserId is not null)
            {
                var possibleDbUser = await _userManager.FindByIdAsync(possibleDbUserId);
                if (possibleDbUser is not null)
                {
                    var (updatedAuth0User, _) =
                        await _userManager.AssociateDbUserWithAuth0UserOnAuth0(auth0EventUser, possibleDbUser);

                    // Make sure the last login time has stored in user db.
                    possibleDbUser.LastLoginAt = DateTime.UtcNow;
                    possibleDbUser.EmailConfirmed = auth0EventUser.EmailVerified.GetValueOrDefault();
                    await _userManager.UpdateDbUserAsync(possibleDbUser);

                    // No need to verify email again
                    var auth0AppMetadata = SleekflowUserManager.GetAuth0AppMetadata(updatedAuth0User);
                    auth0AppMetadata!.LoginRequiresEmailVerification = false;

                    return Ok(
                        new PostUserLoginResponse(
                            auth0EventUser.UserId,
                            auth0AppMetadata,
                            PostUserLoginResponseStatus.MergedByAuth0UserId));
                }
            }

            if (auth0EventUser.UserName is not null)
            {
                // Can't map because the Username is conflicted
                // This is unlikely to happen
                var existingDbUserWithUserName = await _userManager.FindByNameAsync(auth0EventUser.UserName);
                if (existingDbUserWithUserName is not null)
                {
                    _logger.LogInformation(
                        "PostUserLogin {Auth0EventUser}. The username is already taken. Please choose another user name",
                        JsonConvert.SerializeObject(auth0EventUser, Formatting.None));

                    throw new Exception(PostUserLoginResponseStatus.ErrorConflictUsername);
                }
            }

            // Map Auth0User to SleekflowUser if matched the email (social login or user is by signup)
            var existingDbUser = await _userManager.FindByEmailAsync(auth0EventUser.Email!);
            if (existingDbUser is not null)
            {
                // Considering the sleekflow_id is null, so we can safely assume the user is by signup if the email is not verified.
                // We need to make sure the email is verified to merge the user
                if (auth0EventUser.EmailVerified is not true)
                {
                    _logger.LogInformation(
                        "PostUserLogin {Auth0EventUser}. You need to first verify your email",
                        JsonConvert.SerializeObject(auth0EventUser, Formatting.None));

                    await _userManager.SendVerifyEmailAsync(auth0EventUser);

                    throw new Exception(PostUserLoginResponseStatus.ErrorConflictEmailAndNotVerified);
                }

                var (updatedAuth0User, _) =
                    await _userManager.AssociateDbUserWithAuth0UserOnAuth0(auth0EventUser, existingDbUser);

                // Make sure the last login time has stored in user db.
                existingDbUser.LastLoginAt = DateTime.UtcNow;
                existingDbUser.EmailConfirmed = auth0EventUser.EmailVerified.GetValueOrDefault();
                await _userManager.UpdateDbUserAsync(existingDbUser);

                // Need to make sure the email is verified to let the user login
                var auth0AppMetadata = SleekflowUserManager.GetAuth0AppMetadata(updatedAuth0User);
                auth0AppMetadata!.LoginRequiresEmailVerification = true;

                return Ok(
                    new PostUserLoginResponse(
                        auth0EventUser.UserId,
                        auth0AppMetadata,
                        PostUserLoginResponseStatus.MergedByEmail));
            }

            var (newlyAssociatedAuth0User, _) =
                await _userManager.CreateNewDbUserAndAssociateWithAuth0UserOnAuth0(auth0EventUser);

            return Ok(
                new PostUserLoginResponse(
                    auth0EventUser.UserId,
                    SleekflowUserManager.GetAuth0AppMetadata(newlyAssociatedAuth0User),
                    PostUserLoginResponseStatus.NewUser));
        }
        catch (Exception e)
        {
            if (PostUserLoginResponseStatus.AllStatuses.Contains(e.Message))
            {
                return Ok(
                    new PostUserLoginResponse(
                        auth0EventUser.UserId,
                        null,
                        e.Message));
            }

            _logger.LogError(
                e,
                "PostUserLogin {Auth0EventUser}",
                JsonConvert.SerializeObject(auth0EventUser, Formatting.None));

            return new ObjectResult(
                new PostUserLoginResponse(
                    auth0EventUser.UserId,
                    null,
                    e.Message))
            {
                StatusCode = StatusCodes.Status500InternalServerError
            };
        }
    }

    [HttpPost("PostUserChangePassword")]
    public async Task<ActionResult> PostUserChangePassword(
        [FromQuery]
        string token,
        [FromBody]
        User auth0EventUser)
    {
        var isValid = await ValidateToken(token);
        if (!isValid)
        {
            return NotFound();
        }

        var tuple = await _userManager.FindByAuth0User(auth0EventUser);
        if (tuple == null)
        {
            return Ok();
        }

        var (dbUser, _) = tuple.Value;

        // Reset password auto logout all session
        var staff = await _appDbContext.UserRoleStaffs
            .Where(x => x.IdentityId == dbUser.Id)
            .Include(x => x.RegisteredSessions)
            .FirstOrDefaultAsync();
        if (staff == null)
        {
            _logger.LogInformation("Staff is null");

            return Ok();
        }

        staff
            .RegisteredSessions
            .ForEach(x => x.SessionStatus = SessionStatus.AutoLogout);
        foreach (var device in staff.RegisteredSessions)
        {
            await _signalRService.SignalRAutoLogout(device.UUID, _mapper.Map<ActiveSessionResponse>(device));
        }

        await _appDbContext.SaveChangesAsync();
        await _userManager.UpdateAsync(dbUser);

        return Ok();
    }

    public class PreUserRegistrationRequest
    {
        [JsonProperty("auth0_event_user")]
        public User Auth0EventUser { get; set; }

        [JsonProperty("auth0_client_id")]
        public string Auth0ClientId { get; set; }

        [JsonConstructor]
        public PreUserRegistrationRequest(
            User auth0EventUser,
            string auth0ClientId)
        {
            Auth0EventUser = auth0EventUser;
            Auth0ClientId = auth0ClientId;
        }
    }

    public class PreUserRegistrationResponse
    {
        [JsonProperty("app_metadata")]
        public Auth0AppMetadata? AppMetadata { get; set; }

        [JsonProperty("status")]
        public string Status { get; set; }

        [JsonConstructor]
        public PreUserRegistrationResponse(Auth0AppMetadata? appMetadata, string status)
        {
            AppMetadata = appMetadata;
            Status = status;
        }
    }

    public static class PreUserRegistrationResponseStatus
    {
        public const string ErrorConflictUsername = "error_conflict_username";
        public const string ErrorConflictEmail = "error_conflict_email";
        public const string Good = "good";

        public static readonly string[] AllStatuses =
        {
            ErrorConflictUsername,
            ErrorConflictEmail,
            Good,
        };
    }

    [HttpPost("PreUserRegistration")]
    public async Task<ActionResult<string>> PreUserRegistration(
        [FromQuery]
        string token,
        [FromBody]
        PreUserRegistrationRequest preUserRegistrationRequest)
    {
        _logger.LogInformation(
            "PreUserRegistration {PreUserRegistrationRequest}",
            JsonConvert.SerializeObject(preUserRegistrationRequest, Formatting.None));

        var isValid = await ValidateToken(token);
        if (!isValid)
        {
            return NotFound();
        }

        var auth0EventUser = preUserRegistrationRequest.Auth0EventUser;

        try
        {
            // If the user is from our api client, we don't need to check the conflict
            if (preUserRegistrationRequest.Auth0ClientId == _auth0Config.ClientId)
            {
                return Ok(
                    new PreUserRegistrationResponse(
                        SleekflowUserManager.GetAuth0AppMetadata(preUserRegistrationRequest.Auth0EventUser),
                        PreUserRegistrationResponseStatus.Good));
            }

            if (auth0EventUser.UserName != null)
            {
                // Username shouldn't not be conflicted
                var applicationUser = await _userManager.FindByNameAsync(auth0EventUser.UserName);
                if (applicationUser != null)
                {
                    throw new Exception(PreUserRegistrationResponseStatus.ErrorConflictUsername);
                }
            }

            if (auth0EventUser.Email != null)
            {
                var users =
                    auth0EventUser.Email == null
                        ? new List<User>()
                        : await _userManager.GetAllAuth0UsersByEmailAsync(auth0EventUser.Email);
                var allUserIdentities = users.SelectMany(u => u.Identities).ToList();

                // Email shouldn't be conflicted, when it has username-password user
                var hasUsernamePasswordUser = allUserIdentities.Any(
                    i =>
                        i.Connection is "Sleekflow-Username-Password-Authentication");
                if (hasUsernamePasswordUser)
                {
                    throw new Exception(PreUserRegistrationResponseStatus.ErrorConflictEmail);
                }
            }

            ApplicationUser? dbUser;
            dbUser = auth0EventUser.UserName != null
                ? await _userManager.FindByNameAsync(auth0EventUser.UserName)
                : null;
            dbUser = dbUser == null && auth0EventUser.Email != null
                ? await _userManager.FindByEmailAsync(auth0EventUser.Email)
                : null;
            if (dbUser != null)
            {
                return Ok(
                    new PreUserRegistrationResponse(
                        new Auth0AppMetadata(
                            await _userManager.GetRolesAsync(dbUser),
                            dbUser.PhoneNumber,
                            dbUser.Id,
                            true,
                            null),
                        PreUserRegistrationResponseStatus.Good));
            }

            var (auth0AppMetadata, _) =
                await _userManager.CreateNewDbUser(auth0EventUser);

            // If it is a brand new user, it doesn't require email verification
            auth0AppMetadata.LoginRequiresEmailVerification = false;

            return Ok(
                new PreUserRegistrationResponse(
                    auth0AppMetadata,
                    PreUserRegistrationResponseStatus.Good));
        }
        catch (Exception e)
        {
            if (PreUserRegistrationResponseStatus.AllStatuses.Contains(e.Message))
            {
                return Ok(
                    new PreUserRegistrationResponse(
                        null,
                        e.Message));
            }

            _logger.LogError(
                e,
                "PreUserRegistration {Auth0EventUser}",
                JsonConvert.SerializeObject(auth0EventUser, Formatting.None));

            return new ObjectResult(
                new PreUserRegistrationResponse(
                    null,
                    e.Message))
            {
                StatusCode = StatusCodes.Status500InternalServerError
            };
        }
    }


    public class RequiresMfaRequest
    {
        [JsonProperty("auth0_event_user")]
        public User Auth0EventUser { get; set; }

        [JsonProperty("auth0_client_name")]
        public string Auth0ClientName { get; set; }

        [JsonProperty("connection_strategy")]
        public string ConnectionStrategy { get; set; }

        [JsonProperty("connection_name")]
        public string ConnectionName { get; set; }

        [JsonConstructor]
        public RequiresMfaRequest(
            User auth0EventUser,
            string auth0ClientName,
            string connectionStrategy,
            string connectionName)
        {
            Auth0EventUser = auth0EventUser;
            Auth0ClientName = auth0ClientName;
            ConnectionStrategy = connectionStrategy;
            ConnectionName = connectionName;
        }
    }

    public class RequiresMfaResponse
    {
        [JsonProperty("is_mfa_required")]
        public bool IsMfaRequired { get; set; }

        [JsonProperty("allow_remember_browser")]
        public bool AllowRememberBrowser { get; set; } = true;
    }

    [Obsolete("This endpoint is moved to TenantHub")]
    [HttpPost("RequiresMFA")]
    public async Task<ActionResult<RequiresMfaResponse>> RequiresMfa(
        [FromQuery]
        string token,
        [FromBody]
        RequiresMfaRequest requiresMfaRequest)
    {
        var isValid = await ValidateToken(token);
        if (!isValid)
        {
            return BadRequest("Not a valid event token");
        }

        // We will skip the checking if user is login from enterprise connection now
        if (requiresMfaRequest.ConnectionStrategy is "adfs" or "oidc")
        {
            return Ok(
                new RequiresMfaResponse()
                {
                    IsMfaRequired = false
                });
        }

        var auth0EventUser = requiresMfaRequest.Auth0EventUser;
        var appMetadata = SleekflowUserManager.GetAuth0AppMetadata(auth0EventUser);

        // Check if user is using dive feature.
        if (appMetadata.LoginAsUser is not null
            && appMetadata.LoginAsUser.UserId is not null
            && appMetadata.LoginAsUser.CompanyId is not null
            && appMetadata.Roles is not null
            && appMetadata.Roles.Contains(ApplicationUserRole.InternalCmsSuperUser))
        {
            if (appMetadata.LoginAsUser.ExpireAt > DateTime.UtcNow)
            {
                return Ok(
                    new RequiresMfaResponse()
                    {
                        IsMfaRequired = false
                    });
            }
        }

        var user = (await _userManager.FindByIdAsync(appMetadata.SleekflowId!))!;

        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(user));
        if (staff == null)
        {
            return Ok(
                new RequiresMfaResponse()
                {
                    IsMfaRequired = false
                });
        }

        var staffRole = staff.RoleType;
        var staffRoleStr = staffRole switch
        {
            StaffUserRole.Admin => "Admin",
            StaffUserRole.Staff => "Staff",
            StaffUserRole.TeamAdmin => "TeamAdmin",
            _ => null
        };
        if (staffRoleStr == null)
        {
            return Ok(
                new RequiresMfaResponse()
                {
                    IsMfaRequired = false
                });
        }

        var getAllFeaturesOutputOutput =
            await _managementFeaturesApi.ManagementFeaturesGetAllFeaturesPostAsync(body: new object());
        var feature =
            getAllFeaturesOutputOutput.Data.Features.First(f => f.Name == "2FA");
        var mfaDisableRememberBrowser =
            getAllFeaturesOutputOutput.Data.Features.First(f => f.Name == "2FA_DisableRememberBrowser");

        var managementGetFeatureEnablementsOutputOutput =
            await _managementEnabledFeaturesApi.ManagementEnabledFeaturesGetFeatureEnablementsPostAsync(
                managementGetFeatureEnablementsInput: new ManagementGetFeatureEnablementsInput(
                    feature.Id,
                    staff.CompanyId));
        var isDisableRememberBrowser =
            await _managementEnabledFeaturesApi.ManagementEnabledFeaturesIsFeatureEnabledForCompanyPostAsync(
                managementIsFeatureEnabledForCompanyInput: new ManagementIsFeatureEnabledForCompanyInput(
                    staff.CompanyId,
                    mfaDisableRememberBrowser.Id));

        return Ok(
            new RequiresMfaResponse()
            {
                IsMfaRequired =
                    managementGetFeatureEnablementsOutputOutput.Data.IsFeatureEnabledForCompany
                    && managementGetFeatureEnablementsOutputOutput.Data.IsFeatureEnabledForRolesDict.ContainsKey(staffRoleStr)
                    && managementGetFeatureEnablementsOutputOutput.Data.IsFeatureEnabledForRolesDict[staffRoleStr],
                AllowRememberBrowser = !isDisableRememberBrowser.Data.IsFeatureEnabled
            });
    }

    public class IsAllowedIpRequest
    {
        [JsonProperty("auth0_event_user")]
        public User Auth0EventUser { get; set; }

        [JsonProperty("auth0_client_name")]
        public string Auth0ClientName { get; set; }

        [JsonProperty("protocol")]
        public string Protocol { get; set; }

        [JsonProperty("ip")]
        public string IP { get; set; }

        [JsonConstructor]
        public IsAllowedIpRequest(
            User auth0EventUser,
            string auth0ClientName,
            string ip)
        {
            Auth0EventUser = auth0EventUser;
            Auth0ClientName = auth0ClientName;
            IP = ip;
        }
    }

    public class IsAllowedIpResponse
    {
        [JsonProperty("is_allowed_ip")]
        public bool IsAllowedIp { get; set; }

        [JsonProperty("message")]
        public string Message { get; set; }

        [JsonProperty("ip")]
        public string IP { get; set; }

        public IsAllowedIpResponse(bool isAllowedIp, string message, string ip)
        {
            IsAllowedIp = isAllowedIp;
            Message = message;
            IP = ip;
        }
    }

    [HttpPost("IsAllowedIP")]
    public async Task<ActionResult<IsAllowedIpResponse>> IsAllowedIp(
        [FromQuery]
        string token,
        [FromBody]
        IsAllowedIpRequest isAllowedIpRequest)
    {
        var isValid = await ValidateToken(token);
        if (!isValid)
        {
            return BadRequest("Not a valid event token");
        }

        var auth0EventUser = isAllowedIpRequest.Auth0EventUser;
        var appMetadata = SleekflowUserManager.GetAuth0AppMetadata(auth0EventUser);
        var user = (await _userManager.FindByIdAsync(appMetadata.SleekflowId!))!;

        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(user));
        if (staff == null)
        {
            return Ok(new IsAllowedIpResponse(true, "allowed", isAllowedIpRequest.IP));
        }

        var getAllFeaturesOutputOutput = await _managementFeaturesApi.ManagementFeaturesGetAllFeaturesPostAsync(body: new object());
        var feature =
            getAllFeaturesOutputOutput.Data.Features.First(f => f.Name == "IpWhitelist");

        var whitelistFeature =
            await _managementEnabledFeaturesApi.ManagementEnabledFeaturesIsFeatureEnabledForCompanyPostAsync(
                managementIsFeatureEnabledForCompanyInput: new (
                    staff.CompanyId,
                    feature.Id));

        if (whitelistFeature.Data.IsFeatureEnabled)
        {
            if (string.IsNullOrEmpty(isAllowedIpRequest.IP))
            {
                return Ok(
                    new IsAllowedIpResponse(
                        false, "Hidden IP is not allowed when IP whitelist feature is enabled", string.Empty));
            }

            var result = await _managementIpWhitelistsApi.ManagementIpWhitelistsIsAllowedIpPostAsync(
                managementIsAllowedIpInput: new (
                    staff.CompanyId,
                    isAllowedIpRequest.IP));

            return Ok(
                new IsAllowedIpResponse(
                    result.Data.IsAllowedIp,
                    result.Message,
                    isAllowedIpRequest.IP));
        }

        return Ok(
            new IsAllowedIpResponse(
                true,
                "allowed",
                isAllowedIpRequest.IP));
    }
}