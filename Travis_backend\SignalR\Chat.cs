﻿using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.SignalR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System;
using System.Linq;
using System.Threading.Tasks;
using Travis_backend.AccountAuthenticationDomain.Models;
using Travis_backend.ConversationDomain.ViewModels;
using Travis_backend.Database;
using Travis_backend.MessageDomain.Models;
using Travis_backend.MessageDomain.ViewModels;

namespace Travis_backend.SignalR
{
    public class Chat : Hub
    {
        private readonly ApplicationDbContext _appDbContext;
        private readonly ILogger _logger;
        private readonly IMapper _mapper;

        public Chat(
            ApplicationDbContext dbContext,
            ILogger<Chat> logger,
            IMapper mapper)
        {
            _appDbContext = dbContext;
            _logger = logger;
            _mapper = mapper;
        }

        public override async Task OnConnectedAsync()
        {
            try
            {
                if (await _appDbContext.SenderWebClientSenders.AnyAsync(
                        x => x.SignalRConnectionId == Context.ConnectionId))
                {
                    var webclient = await _appDbContext.SenderWebClientSenders
                        .Where(x => x.SignalRConnectionId == Context.ConnectionId)
                        .AsNoTracking()
                        .FirstOrDefaultAsync();

                    if (webclient != null)
                    {
                        try
                        {
                            webclient.OnlineStatus = OnlineStatus.Online;
                            var webClientResponse = _mapper.Map<WebClientResponse>(webclient);
                            await Clients.Group(webclient.WebClientUUID).SendAsync(
                                "OnLiveChatOnlineStatusChanged",
                                webClientResponse);

                            // set Online Status
                            await _appDbContext.SenderWebClientSenders
                                .Where(x => x.SignalRConnectionId == Context.ConnectionId)
                                .ExecuteUpdateAsync(
                                    calls => calls.SetProperty(
                                        p => p.OnlineStatus, OnlineStatus.Online));
                        }
                        catch (Exception exx)
                        {
                            _logger.LogError(
                                exx,
                                "[OnConnectedAsync] ConnectionId: {ContextConnectionId} WebClientUUID: {WebclientWebClientUuid} Update live chat status error {ExceptionMessage}",
                                Context.ConnectionId,
                                webclient.WebClientUUID,
                                exx.Message);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[OnConnectedAsync] ConnectionId: {ContextConnectionId} Update live chat status error {ExceptionMessage}",
                    Context.ConnectionId,
                    ex.Message);
            }

            try
            {
                await base.OnConnectedAsync().ConfigureAwait(false);
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[OnConnectedAsync] ConnectionId: {ContextConnectionId} Error",
                    Context.ConnectionId);
            }
        }

        public override async Task OnDisconnectedAsync(Exception ex)
        {
            // try
            // {
            //     if (await _appDbContext.SenderWebClientSenders.AnyAsync(
            //             x => x.SignalRConnectionId == Context.ConnectionId))
            //     {
            //         var webclient = await _appDbContext.SenderWebClientSenders
            //             .Where(x => x.SignalRConnectionId == Context.ConnectionId)
            //             .AsNoTracking()
            //             .FirstOrDefaultAsync();
            //
            //         if (webclient != null)
            //         {
            //             try
            //             {
            //                 webclient.OnlineStatus = OnlineStatus.Offline;
            //                 var webClientResponse = _mapper.Map<WebClientResponse>(webclient);
            //                 await Clients.Group(webclient.WebClientUUID).SendAsync(
            //                     "OnLiveChatOnlineStatusChanged",
            //                     webClientResponse);
            //
            //                 // Send offline event
            //                 await _appDbContext.SenderWebClientSenders
            //                     .Where(x => x.SignalRConnectionId == Context.ConnectionId)
            //                     .ExecuteUpdateAsync(
            //                         calls => calls.SetProperty(
            //                             p => p.OnlineStatus,
            //                             OnlineStatus.Offline));
            //
            //                 // Notify webclient status changes
            //             }
            //             catch (Exception exx)
            //             {
            //                 _logger.LogError(
            //                     exx,
            //                     "[OnDisconnectedAsync] ConnectionId: {ContextConnectionId} WebClientUUID: {WebclientWebClientUuid} Update live chat status error {ExceptionMessage}",
            //                     Context.ConnectionId,
            //                     webclient.WebClientUUID,
            //                     exx.Message);
            //             }
            //         }
            //     }
            //     else
            //     {
            //         _logger.LogWarning(
            //             "[{MethodName}] {ContextConnectionId} webclient not found, live chat status: offline",
            //             nameof(OnDisconnectedAsync),
            //             Context.ConnectionId);
            //     }
            // }
            // catch (Exception exxx)
            // {
            //     _logger.LogError(
            //         exxx,
            //         "[OnDisconnectedAsync] ConnectionId: {ContextConnectionId} Update live chat status error {ExceptionMessage}",
            //         Context.ConnectionId,
            //         exxx.Message);
            // }

            try
            {
                await base.OnDisconnectedAsync(ex).ConfigureAwait(false);
            }
            catch (Exception disconnectEx)
            {
                _logger.LogError(
                    disconnectEx,
                    "[OnDisconnectedAsync] ConnectionId: {ContextConnectionId} Error {ExceptionMessage}",
                    Context.ConnectionId,
                    disconnectEx.Message);
            }
        }

        public string GetConnectionId()
        {
            return Context.ConnectionId;
        }

        [Authorize]
        public async Task AddToGroup(string groupName)
        {
            // _logger.LogWarning($"[SiganlR] AddToGroup is {groupName}");
            if (Context.User.Identity.IsAuthenticated)
            {
                var name = Context.User.Identity.Name;
            }

            await Groups.AddToGroupAsync(Context.ConnectionId, groupName);

            // _logger.LogWarning($"[SiganlR] AddToGroup Done: {groupName}");

            // await Clients.Group(groupName).SendAsync("Send", $"{Context.ConnectionId} has joined the group {groupName}.");
        }

        [Authorize]
        public async Task DeviceAddToGroup(string deviceUUID)
        {
            await Groups.AddToGroupAsync(Context.ConnectionId, deviceUUID);
        }

        public async Task RemoveFromGroup(string groupName)
        {
            await Groups.RemoveFromGroupAsync(Context.ConnectionId, groupName);

            // await Clients.Group(groupName).SendAsync("Send", $"{Context.ConnectionId} has left the group {groupName}.");
        }

        public async Task WebClientAddToGroup(string groupName)
        {
            await Groups.AddToGroupAsync(Context.ConnectionId, groupName);

            // await Clients.Group(groupName).SendAsync("Send", $"{Context.ConnectionId} has left the group {groupName}.");
        }

        public async Task ConversationTyping(string groupName, string conversationId, string staffId)
        {
            await Clients.Group(groupName).SendAsync(
                "OnConversationTyping",
                new ConversationTypingObject
                {
                    ConversationId = conversationId, StaffId = staffId
                });
            /*
            try
            {
                var conversation = await _appDbContext.Conversations.Where(x => x.Id == conversationId).FirstOrDefaultAsync();
                if (conversation.WhatsappUserId.HasValue)
                {
                    var whatsappSender = await _appDbContext.SenderWhatsappSenders.Where(x => x.Id == conversation.WhatsappUserId).FirstOrDefaultAsync();
                    if (!whatsappSender.whatsAppId.Contains("whatsapp:+"))
                    {
                        var config = await _appDbContext.ConfigWSChatAPIConfigs.Where(x => x.WSChatAPIInstance == whatsappSender.InstanceId && x.CompanyId == groupName && !x.IsDeleted).FirstOrDefaultAsync();
                        if (config != null)
                        {
                            var client = new HttpClient();
                            var responseMessage = await client.GetAsync($"{config.WSChatAPIURL}/dialogs?token={config.WSChatAPIKey}");

                            var request = new WhatsappChatAPITypingRequest
                            {
                                chatId = whatsappSender.whatsAppId.Replace(" ", "").Replace("(", "").Replace(")", "").Replace("+", ""),
                                on = true,
                                duration = 1
                            };

                            await client.PostAsJsonAsync($"{config.WSChatAPIURL}/typing?token={config.WSChatAPIKey}", request);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("Pin chatapi error: " + ex.Message);
            } */
        }

        public void BroadcastMessage(string name, string message)
        {
            Clients.All.SendAsync("broadcastMessage", name, message);
        }

        public void Echo(string name, string message)
        {
            Clients.Client(Context.ConnectionId).SendAsync("echo", name, message + " (echo from server)");
        }

        public void Send(string message)
        {
            Clients.User(Context.UserIdentifier).SendAsync("Send", $"Hello {Context.UserIdentifier}");
        }
    }
}