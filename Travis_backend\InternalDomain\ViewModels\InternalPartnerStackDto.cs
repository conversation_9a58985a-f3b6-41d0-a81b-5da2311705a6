using System;
using System.Collections.Generic;
using Travis_backend.InternalDomain.Models;

namespace Travis_backend.InternalDomain.ViewModels;

public class CmsPartnerStackCustomerMapDto
{
    public long Id { get; set; }

    public string CompanyId { get; set; }

    public string PartnerStackCustomerKey { get; set; }

    public IndividualCommissionConfig IndividualCommissionConfig { get; set; }

    public PartnerStackPartnerInformation PartnerStackPartnerInformation { get; set; }

    public DateTime UpdatedAt { get; set; }

    public DateTime CreatedAt { get; set; }
}

public class SyncMrrToPartnerStackDto
{
    public string SleekFlowCompanyId { get; set; }

    public string SleekFlowCompanyName { get; set; }

    public string Message { get; set; }

    public List<SyncMrrTransactionToPartnerStackResult> Results { get; set; }
}

public class SyncMrrTransactionToPartnerStackResult
{
    public bool IsSuccess { get; set; }

    public string PartnerStackTransactionCategoryKey { get; set; }

    public string PartnerStackTransactionProductKey { get; set; }
}