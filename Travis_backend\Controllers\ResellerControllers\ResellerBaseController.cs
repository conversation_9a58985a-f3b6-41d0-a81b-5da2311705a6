﻿using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;
using Travis_backend.AccountAuthenticationDomain.Models;
using Travis_backend.Database;
using Travis_backend.Enums;
using Travis_backend.ResellerDomain.ViewModels;

namespace Travis_backend.Controllers.ResellerControllers
{
    public abstract class ResellerBaseController : Controller
    {
        protected readonly UserManager<ApplicationUser> _userManager;
        private readonly ApplicationDbContext _appDbContext;

        protected readonly JsonSerializerSettings _jsonSerializerSettings = new ()
        {
            DateTimeZoneHandling = DateTimeZoneHandling.Utc,
            NullValueHandling = NullValueHandling.Ignore,
            ContractResolver = new CamelCasePropertyNamesContractResolver()
        };

        protected ResellerBaseController(UserManager<ApplicationUser> userManager, ApplicationDbContext appDbContext)
        {
            _userManager = userManager;
            _appDbContext = appDbContext;
        }

        protected async Task<ApplicationUser> GetCurrentValidResellerUser(List<string> resellerPortalRoles)
        {
            var user = await _userManager.GetUserAsync(User);

            if (user == null)
            {
                return null;
            }

            var currentUserRoles = await _userManager.GetRolesAsync(user);

            return resellerPortalRoles.Any(x => currentUserRoles.Contains(x)) ? user : null;
        }

        protected async Task<ResellerCompanyDto> GetResellerCompanyDto(ApplicationUser user)
        {
            var resellerCompany = await _appDbContext.CompanyCompanies.FirstOrDefaultAsync(
                x => x.Id == user.CompanyId && x.CompanyType == CompanyType.Reseller);

            if (resellerCompany == null)
            {
                return null;
            }

            return new ResellerCompanyDto()
            {
                ResellerCompanyId = resellerCompany.Id, ResellerCompanyName = resellerCompany.CompanyName
            };
        }
    }
}