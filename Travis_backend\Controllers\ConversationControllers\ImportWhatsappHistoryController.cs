using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using AutoMapper;
using CsvHelper;
using ICSharpCode.SharpZipLib.Core;
using ICSharpCode.SharpZipLib.Zip;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Travis_backend.AccountAuthenticationDomain.Models;
using Travis_backend.AutomationDomain.Models;
using Travis_backend.BackgroundTaskServices;
using Travis_backend.BackgroundTaskServices.Helpers;
using Travis_backend.CommonDomain.ViewModels;
using Travis_backend.ConversationDomain.ViewModels;
using Travis_backend.ConversationServices;
using Travis_backend.Data.BackgroundTask;
using Travis_backend.Database;
using Travis_backend.Extensions;
using Travis_backend.Helpers;
using Travis_backend.InternalDomain.Models;
using ZipFile = ICSharpCode.SharpZipLib.Zip.ZipFile;

namespace Travis_backend.Controllers.ConversationControllers;

[Authorize]
public class ImportWhatsappHistoryController : Controller
{
    private readonly ApplicationDbContext _appDbContext;
    private readonly UserManager<ApplicationUser> _userManager;
    private readonly SignInManager<ApplicationUser> _signInManager;
    private readonly IMapper _mapper;
    private readonly ILogger _logger;
    private readonly IConfiguration _configuration;
    private readonly IAzureBlobStorageService _uploadService;
    private readonly IConversationMessageService _conversationMessageService;
    private readonly ICoreService _coreService;
    private readonly IBackgroundTaskService _backgroundTaskService;

    private readonly string[] _imbeeCsvHeaders = new string[]
    {
        "Chat Id",
        "Message Id",
        "Date Time",
        "Sender Type",
        "Sender Name",
        "Sender Email",
        "Message Type",
        "Text",
        "Filename",
        "Status",
        "Revoked",
        "Channel Name",
        "Channel Number",
        "Sender Teams"
    };

    public ImportWhatsappHistoryController(
        ApplicationDbContext appDbContext,
        UserManager<ApplicationUser> userManager,
        SignInManager<ApplicationUser> signInManager,
        IMapper mapper,
        IConfiguration configuration,
        ILogger<ImportWhatsappHistoryController> logger,
        IAzureBlobStorageService uploadService,
        IConversationMessageService conversationMessageService,
        ICoreService coreService,
        IBackgroundTaskService backgroundTaskService)
    {
        _userManager = userManager;
        _signInManager = signInManager;
        _appDbContext = appDbContext;
        _mapper = mapper;
        _configuration = configuration;
        _logger = logger;
        _uploadService = uploadService;
        _conversationMessageService = conversationMessageService;
        _coreService = coreService;
        _backgroundTaskService = backgroundTaskService;
    }

    [HttpPost("import/whatsapp/history")]
    public async Task<ActionResult<BackgroundTaskViewModel>> UploadWhatsAppHistoryZip(
        [FromForm]
        WhatsappHistory historyFile)
    {
        var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (historyFile.file == null)
        {
            return BadRequest(
                new ResponseViewModel()
                {
                    message = "no file"
                });
        }

        //Phone number cleaning, replace all non-numeric characters
        historyFile.PhoneNumber = PhoneNumberHelper.NormalizePhoneNumber(historyFile.PhoneNumber);

        try
        {
            var zipFileTemp = TempFileHelper.GetTempFileFullPath("import_chat_history");

            using (Stream fileStream = new FileStream(zipFileTemp, FileMode.Create))
            {
                await historyFile.file.CopyToAsync(fileStream);
            }

            var path = Path.GetTempPath();
            var folder = Guid.NewGuid().ToString();
            var extractionPath = Directory.CreateDirectory(path + folder).FullName;

            using (var zip = new ZipFile(zipFileTemp))
            {
                foreach (ZipEntry entry in zip)
                {
                    if (entry.IsFile)
                    {
                        using (var stream = zip.GetInputStream(entry))
                        {
                            var uncleanFullPath = Path.Join(extractionPath, entry.Name);
                            var cleanDirectoryPath = CleanDirectoryPath(Path.GetDirectoryName(uncleanFullPath));
                            var fileName = Path.GetFileName(uncleanFullPath);

                            if (!Directory.Exists(cleanDirectoryPath) && !string.IsNullOrWhiteSpace(cleanDirectoryPath))
                            {
                                Directory.CreateDirectory(cleanDirectoryPath);
                            }

                            using (var fileStream = System.IO.File.Create(Path.Join(cleanDirectoryPath, fileName)))
                            {
                                var buffer = new byte[4096];

                                StreamUtils.Copy(stream, fileStream, buffer);
                            }
                        }
                    }
                    else
                    {
                        Directory.CreateDirectory(CleanDirectoryPath(Path.Join(extractionPath, entry.Name[0..^1])));
                    }
                }
            }

            var directories = Directory.GetDirectories(extractionPath);

            var chatHistories = new List<ChatHistory>();

            if (directories.Length > 0)
            {
                foreach (var directory in directories)
                {
                    var chatHistory = new ChatHistory();

                    var historyFileName = Path.GetFileName(directory);
                    var nameComponent = historyFileName.Split("___");


                    chatHistory.ChatRoomId = nameComponent[0];
                    chatHistory.ChatRoomName = nameComponent[1];

                    var jsonFiles = Directory.GetFiles(directory);

                    if (jsonFiles.Length == 0)
                    {
                        continue;
                    }

                    var readJsonFile = System.IO.File.ReadAllText(jsonFiles.FirstOrDefault());
                    chatHistory.MessageHistories = JsonConvert.DeserializeObject<List<MessageHistory>>(readJsonFile);

                    chatHistory.MessageHistories = chatHistory.MessageHistories
                        .Where(
                            x => x.FromUser == historyFile.PhoneNumber || x.ToUser == historyFile.PhoneNumber)
                        .ToList();

                    var readFileString = await System.IO.File.ReadAllTextAsync(jsonFiles.FirstOrDefault());

                    //Json handling
                    List<MessageHistory> deserializedResult =
                        JsonConvert.DeserializeObject<List<MessageHistory>>(readFileString);
                    chatHistory.MessageHistories = deserializedResult;
                    chatHistories.Add(chatHistory);

                    continue;


                }
            }
            else
            {
                //ImBee import logic
                var csvFiles = Directory.GetFiles(extractionPath);

                foreach (var csvFile in csvFiles)
                {
                    var historyFileName = Path.GetFileName(csvFile);

                    var nameComponents = historyFileName.Replace(".csv", string.Empty).Split("-");

                    if (nameComponents.Length < 4)
                    {
                        continue;
                    }

                    var conversationInboundPhoneNumber = nameComponents[3];

                    var chatHistory = new ChatHistory
                    {
                        ChatRoomId = conversationInboundPhoneNumber, ChatRoomName = conversationInboundPhoneNumber
                    };

                    using (var reader = new StreamReader(csvFile))
                    using (var csv = new CsvReader(reader, CultureInfo.InvariantCulture))
                    {
                        var records = new List<MessageHistory>();

                        //Check whether all headers exist
                        await csv.ReadAsync();
                        csv.ReadHeader();
                        var header = csv.HeaderRecord;
                        var hasHeaderRecord = header.Intersect(_imbeeCsvHeaders).Count() == header.Length;

                        if (!hasHeaderRecord)
                        {
                            continue;
                        }

                        while (await csv.ReadAsync())
                        {
                            //Checking for conditions that would exclude the record from being imported
                            if (csv.GetField<string>("Message Type") != "Text")
                            {
                                continue;
                            }

                            var channelName = csv.GetField<string>("Channel Name");

                            if (channelName.ToLower().Contains("fb") || channelName.ToLower().Contains("ig"))
                            {
                                break;
                            }

                            var isFromSleekflow = true;


                            if (csv.GetField<string>("Sender Type") == "Customer")
                            {
                                isFromSleekflow = false;
                            }

                            var replyAgent = string.Empty;

                            var channelPhoneNumber = csv.GetField<string>("Channel Number").Replace("+", string.Empty);

                            var record = new MessageHistory
                            {
                                Text = csv.GetField<string>("Text"),
                                FromUser = isFromSleekflow ? channelPhoneNumber : conversationInboundPhoneNumber,
                                ToUser = isFromSleekflow ? conversationInboundPhoneNumber : channelPhoneNumber,
                                Time = DateTime.Parse(csv.GetField<string>("Date Time")).ToUnixTimeMilliSeconds()
                            };
                            records.Add(record);
                        }

                        if (records.Count > 0)
                        {
                            chatHistory.MessageHistories = records;
                            chatHistories.Add(chatHistory);
                        }
                    }
                }
            }

            // clean up
            System.IO.DirectoryInfo di = new DirectoryInfo(extractionPath);

            foreach (FileInfo file in di.GetFiles())
            {
                file.Delete();
            }

            foreach (DirectoryInfo dir in di.GetDirectories())
            {
                dir.Delete(true);
            }

            Directory.Delete(extractionPath);
            TempFileHelper.DeleteFileWithRetry(zipFileTemp);

            historyFile.PhoneNumber = historyFile.PhoneNumber?
                .Replace(" ", string.Empty);

            var importedToChannelId = new TargetedChannelModel
            {
                channel = historyFile.ChannelName,
                ids = new List<string>
                {
                    historyFile.ChannelId
                }
            };

            var importWhatsAppHistoryViewModel = new ImportWhatsAppHistoryViewModel
            {
                ImportedToChannel = importedToChannelId,
                PhoneNumber = historyFile.PhoneNumber,
                ChatHistories = chatHistories
            };

            var createdTask = await _backgroundTaskService.EnqueueImportWhatsAppHistoryTask(
                companyUser.IdentityId,
                companyUser.CompanyId,
                companyUser.Id,
                importWhatsAppHistoryViewModel);

            await _appDbContext.ImportWhatsappHistoryRecords.AddAsync(
                new ImportWhatsappHistoryRecord()
                {
                    CompanyId = companyUser.CompanyId,
                    PhoneNumber = historyFile.PhoneNumber,
                    ChannelName = historyFile.ChannelName,
                    ChannelId = historyFile.ChannelId,
                    Filename = historyFile.file.FileName
                });

            await _appDbContext.SaveChangesAsync();

            return Ok(createdTask.MapToResultViewModel());
        }
        catch (Exception ex)
        {
            return BadRequest(ex);
        }
    }

    [HttpPost]
    [RequestSizeLimit(long.MaxValue)]
    [Route("import/chat/history")]
    public async Task<ActionResult<BackgroundTaskViewModel>> UploadGenericHistory(
        [FromBody]
        GenericImportHistory genericImportHistory)
    {
        var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        var importedToChannelId = new TargetedChannelModel
        {
            channel = genericImportHistory.ChannelName,
            ids = new List<string>
            {
                genericImportHistory.ChannelId
            }
        };

        var createdTask = await _backgroundTaskService.EnqueueImportGenericHistoryTask(
            companyUser.IdentityId,
            companyUser.CompanyId,
            companyUser.Id,
            new ImportGenericHistoryViewModel
            {
                CompanyId = companyUser.CompanyId,
                StaffId = companyUser.Id,
                UserId = companyUser.IdentityId,
                PhoneNumber = genericImportHistory.PhoneNumber,
                ChatHistories = genericImportHistory.ChatHistories,
                ImportedToChannel = importedToChannelId
            });

        return Ok(createdTask.MapToResultViewModel());
    }

    [HttpPost]
    [RequestSizeLimit(long.MaxValue)]
    [Route("import/chat/history/file")]
    public async Task<ActionResult> UploadGenericHistoryWithFile(
        [FromForm]
        WhatsappHistory whatsappHistory)
    {
        try
        {
            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
            var importedToChannelId = new TargetedChannelModel
            {
                channel = whatsappHistory.ChannelName,
                ids = new List<string>
                {
                    whatsappHistory.ChannelId
                }
            };


            var result = new StringBuilder();
            using (var reader = new StreamReader(whatsappHistory.file.OpenReadStream()))
            {
                result.Append(await reader.ReadToEndAsync());
            }

            var chatHistories = JsonConvert.DeserializeObject<List<ChatHistory>>(result.ToString());

            var jsonString = JsonConvert.SerializeObject(chatHistories.Skip(7000).Take(7000));

            var createdTask = await _backgroundTaskService.EnqueueImportGenericHistoryTask(
                companyUser.IdentityId,
                companyUser.CompanyId,
                companyUser.Id,
                new ImportGenericHistoryViewModel
                {
                    CompanyId = companyUser.CompanyId,
                    StaffId = companyUser.Id,
                    UserId = companyUser.IdentityId,
                    PhoneNumber = whatsappHistory.PhoneNumber,
                    ChatHistories = chatHistories,
                    ImportedToChannel = importedToChannelId
                });
            return Ok(createdTask.MapToResultViewModel());

            // return Ok(jsonString);
        }
        catch (Exception e)
        {
            return BadRequest(
                new ResponseViewModel()
                {
                    message = "no file"
                });
        }
    }

    private static string CleanDirectoryPath(string path)
    {
        return path.Trim().Replace("\n", string.Empty).Replace("\r", string.Empty);
    }

    public class WhatsappHistory
    {
        public IFormFile file { get; set; }

        public string PhoneNumber { get; set; }

        public string ChannelName { get; set; }

        public string ChannelId { get; set; }
    }

    public class GenericImportHistory
    {
        public string PhoneNumber { get; set; }

        public string ChannelName { get; set; }

        public string ChannelId { get; set; }

        public List<ChatHistory> ChatHistories { get; set; } = new List<ChatHistory>();
    }
}
