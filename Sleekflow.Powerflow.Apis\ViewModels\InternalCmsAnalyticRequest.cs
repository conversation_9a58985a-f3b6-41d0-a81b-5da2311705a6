﻿using System.ComponentModel.DataAnnotations;
using Travis_backend.Enums;

namespace Sleekflow.Powerflow.Apis.ViewModels;

public class InternalCmsAnalyticRequest
{
    [Required]
    public DateTime Start { get; set; }

    [Required]
    public DateTime End { get; set; }

    public int TimezoneHourOffset { get; set; } = 8;

    public bool AllowCache { get; set; } = true;

    public bool IsExcludeMarkupPlan { get; set; } = false;

    public CompanyType? CompanyType { get; set; } = null;
}

public class InternalCmsAnalyticByOwnerIdRequest : InternalCmsAnalyticRequest
{
    public string OwnerId { get; set; }
}

public class InternalCmsAnalyticByOwnerIdsRequest : InternalCmsAnalyticRequest
{
    public List<string> OwnerIds { get; set; }
}

public class GetCmsCohortAnalysisDataRequest
{
    [Required]
    public int Year { get; set; }

    public bool AllowCache { get; set; } = true;
}

public class GetCompanyRevenueAnalyticRequest
{
    [Required]
    public string CompanyId { get; set; }

    public int TimezoneHourOffset { get; set; } = 8;
}

public class InternalCmsAnalyticByPartnerStackGroupNamesRequest : InternalCmsAnalyticRequest
{
    public List<string> GroupNames { get; set; }
}

public class GetTest
{
    public string a { get; set; }
}