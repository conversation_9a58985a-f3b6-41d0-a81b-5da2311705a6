﻿namespace Sleekflow.Powerflow.Apis.ViewModels;

public class ManageCmsRoleRequest
{
    public string CmsPassword { get; set; }
}

public class ManageUserCmsRoleRequest : ManageCmsRoleRequest
{
    public bool IsCmsUser { get; set; }

    public bool IsCmsSuperUser { get; set; }

    public bool IsCmsSalesUser { get; set; }

    public bool IsCmsCustomerSuccessUser { get; set; }

    public bool IsCmsTeamLead { get; set; }

    public bool IsCmsAdmin { get; set; }
}

public class GrantCmsUserPermissionRequest : ManageUserCmsRoleRequest
{
    public string Email { get; set; }
}