﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using AutoMapper;
using Force.DeepCloner;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.StaticFiles;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Travis_backend.AccountAuthenticationDomain.Models;
using Travis_backend.CommonDomain.ViewModels;
using Travis_backend.Constants;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.ContactDomain.Services;
using Travis_backend.ConversationDomain.ConversationResolver;
using Travis_backend.ConversationDomain.Models;
using Travis_backend.ConversationDomain.Services;
using Travis_backend.ConversationServices;
using Travis_backend.Database;
using Travis_backend.Enums;
using Travis_backend.Extensions;
using Travis_backend.FileDomain.Models;
using Travis_backend.FileDomain.Services;
using Travis_backend.Helpers;
using Travis_backend.MessageDomain.Models;
using Travis_backend.MessageDomain.Services;
using Travis_backend.MessageDomain.ViewModels;
using Travis_backend.StripeIntegrationDomain.Services;

namespace Travis_backend.Controllers.MessageControllers
{
    [Authorize]
    public class MessageController : Controller
    {
        private readonly ApplicationDbContext _appDbContext;
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly IMapper _mapper;
        private readonly ILogger _logger;
        private readonly IConfiguration _configuration;
        private readonly IConversationMessageService _conversationMessageService;
        private readonly IConversationService _conversationService;
        private readonly IAzureBlobStorageService _azureBlobStorageService;
        private readonly IUserProfileService _userProfileService;
        private readonly ICoreService _coreService;
        private readonly ISleekPayService _sleekPayService;

        public MessageController(
            ApplicationDbContext appDbContext,
            UserManager<ApplicationUser> userManager,
            IMapper mapper,
            IConfiguration configuration,
            ILogger<MessageController> logger,
            IConversationMessageService messagingService,
            IUserProfileService userProfileService,
            IAzureBlobStorageService azureBlobStorageService,
            ICoreService coreService,
            ISleekPayService sleekPayService,
            IConversationService conversationService)
        {
            _userManager = userManager;
            _appDbContext = appDbContext;
            _mapper = mapper;
            _configuration = configuration;
            _logger = logger;
            _conversationMessageService = messagingService;
            _azureBlobStorageService = azureBlobStorageService;
            _userProfileService = userProfileService;
            _coreService = coreService;
            _sleekPayService = sleekPayService;
            _conversationService = conversationService;
        }

        [HttpPost]
        [Route("Message/Send")]
        public async Task<IActionResult> Send(
            [FromBody]
            ExtendedConversationMessageViewModel conversationMessageViewModel)
        {
            try
            {
                var conversationMessage = _mapper.Map<ConversationMessage>(conversationMessageViewModel);
                var conversation = _mapper.Map<Conversation>(conversationMessageViewModel);

                var company = await _appDbContext.CompanyCompanies
                    .FirstOrDefaultAsync(x => x.SignalRGroupName == conversation.MessageGroupName);

                if (User.Identity.IsAuthenticated)
                {
                    var user = await _userManager.GetUserAsync(User);
                    conversationMessage.SenderId = user.Id;
                    conversationMessage.Sender = user;
                    conversationMessage.IsSentFromSleekflow = true;

                    var staffInfo = await _appDbContext.UserRoleStaffs.AsSplitQuery()
                        .WhereIf(
                            await _appDbContext.ResellerStaffs
                                .AnyAsync(x => x.IdentityId == user.Id),
                            x => x.CompanyId == user.CompanyId)
                        .Where(x => x.IdentityId == conversationMessage.SenderId)
                        .Include(x => x.Company)
                        .FirstOrDefaultAsync();

                    if (company == null)
                    {
                        company = await _appDbContext.CompanyCompanies
                            .FirstOrDefaultAsync(x => x.Id == staffInfo.CompanyId);
                    }

                    if (conversation.MessageGroupName != staffInfo.Company.SignalRGroupName)
                    {
                        return Unauthorized();
                    }

                    if (
                        // OTO's company ID uat test company ID (will remove later)
                        company.Id is "60cf7ef2-b249-47af-af18-476962ae5c89"
                            or "b6d7e442-38ae-4b9a-b100-2951729768bc" &&
                        staffInfo.RoleType == StaffUserRole.Staff &&
                        await _appDbContext.Conversations.AnyAsync(
                            (x) =>
                                x.Id == conversationMessageViewModel.ConversationId
                                && x.AssigneeId != null
                                && x.AssigneeId != staffInfo.Id)
                    )
                    {
                        return BadRequest(
                            new ResponseViewModel
                            {
                                message = "This conversation is already owned by others"
                            });
                    }

                    // Bug Fix: DEVS-2429 - [MediLase] collaborator is removed from the convo but still able to reply
                    // MediLase only
                    if (staffInfo.CompanyId == "07959d97-3a5a-4155-a240-e9f47dba13fa"
                        && !await _conversationService.IsStaffAllowedToSendMessage(
                            staffInfo,
                            conversationMessageViewModel.ConversationId))
                    {
                        var errorMessage = string.Format(
                            "The Staff with Id:{0} and role:{1} is not allowed to send messages in the conversation with Id:{2}",
                            staffInfo.Id,
                            staffInfo.RoleType.ToString(),
                            conversation.Id);

                        _logger.LogError(
                            "[{MethodName}] The staff with Id:{StaffId} and role:{StaffRole} is not allowed to send messages in the conversation with Id:{ConversationId}",
                            nameof(Send),
                            staffInfo.Id,
                            staffInfo.RoleType.ToString(),
                            conversation.Id);

                        return BadRequest(
                            new ResponseViewModel
                            {
                                message = errorMessage
                            });
                    }
                }

                if (!string.IsNullOrEmpty(conversationMessageViewModel.MessageChecksum)
                    && await _appDbContext.ConversationMessages
                        .AnyAsync(
                            x =>
                                x.CompanyId == company.Id
                                && x.MessageChecksum == conversationMessageViewModel.MessageChecksum))
                {
                    return BadRequest(
                        new ResponseViewModel
                        {
                            message =
                                $"message with checksum: {conversationMessageViewModel.MessageChecksum} sent before."
                        });
                }

                // Link SleekPay Record
                if (!string.IsNullOrEmpty(conversationMessageViewModel.PaymentIntentId))
                {
                    conversationMessage.SleekPayRecordId = await _appDbContext.StripePaymentRecords
                        .Where(
                            x =>
                                x.CompanyId == company.Id
                                && x.StripePaymentIntentId == conversationMessageViewModel.PaymentIntentId)
                        .Select(x => x.Id)
                        .FirstOrDefaultAsync();

                    conversationMessage.DeliveryType = DeliveryType.PaymentLink;
                }

                switch (conversationMessage.Channel)
                {
                    // case "naive":
                    //    if (!string.IsNullOrEmpty(conversationMessage.ReceiverId))
                    //    {
                    //        conversation.NaiveUser = _appDbContext.UserRoleGuests.Where(x => x.IdentityId == conversationMessage.ReceiverId && x.CompanyId == company.Id).FirstOrDefault();
                    //    }

                    // if (conversation.NaiveUser == null)
                    //    {
                    //        conversation.NaiveUser = _appDbContext.UserRoleGuests.Where(x => x.IdentityId == conversationMessage.SenderId && x.CompanyId == company.Id).FirstOrDefault();
                    //    }
                    //    break;
                    case ChannelTypes.Facebook:
                        if (conversationMessageViewModel.MessageType == "interactive")
                        {
                            if (conversationMessageViewModel.ExtendedMessagePayload == null)
                            {
                                return BadRequest(
                                    new ResponseViewModel()
                                    {
                                        message = "Please Add Extend Message Payload for Interactive Message"
                                    });
                            }
                        }

                        if (string.IsNullOrEmpty(conversationMessageViewModel.ConversationId))
                        {
                            conversation = await _appDbContext.Conversations
                                .Include(x => x.facebookUser)
                                .FirstOrDefaultAsync(
                                    x =>
                                        x.facebookUser.FacebookId == conversationMessageViewModel.FacebookReceiverId
                                        && x.CompanyId == company.Id);
                        }
                        else
                        {
                            conversation = await _appDbContext.Conversations
                                .Include(x => x.facebookUser)
                                .FirstOrDefaultAsync(
                                    x =>
                                        x.Id == conversationMessageViewModel.ConversationId
                                        && x.CompanyId == company.Id);
                        }

                        if (conversation.facebookUser == null)
                        {
                            conversation.facebookUser = await _appDbContext.SenderFacebookSenders
                                .FirstOrDefaultAsync(
                                    x =>
                                        x.FacebookId == conversationMessageViewModel.FacebookReceiverId
                                        && x.CompanyId == company.Id);

                            if (conversation.facebookUser == null)
                            {
                                conversation.facebookUser = new FacebookSender
                                {
                                    FacebookId = conversationMessageViewModel.FacebookReceiverId,
                                    CompanyId = company.Id
                                };
                            }
                        }

                        conversationMessage.facebookReceiver = conversation.facebookUser;

                        break;
                    case "naive":
                    case ChannelTypes.Email:
                        conversationMessage.Channel = ChannelTypes.Email;

                        if (string.IsNullOrEmpty(conversationMessageViewModel.ConversationId))
                        {
                            conversation = await _appDbContext.Conversations
                                .Include(x => x.EmailAddress)
                                .FirstOrDefaultAsync(
                                    x =>
                                        x.EmailAddress.Email == conversationMessageViewModel.EmailFrom
                                        && x.CompanyId == company.Id);
                        }
                        else
                        {
                            conversation = await _appDbContext.Conversations
                                .Include(x => x.EmailAddress)
                                .FirstOrDefaultAsync(
                                    x =>
                                        x.Id == conversationMessageViewModel.ConversationId
                                        && x.CompanyId == company.Id);
                        }

                        if (conversation.EmailAddress == null)
                        {
                            if (string.IsNullOrEmpty(conversationMessageViewModel.EmailFrom))
                            {
                                return BadRequest();
                            }

                            conversation.EmailAddress = new EmailSender
                            {
                                Email = conversationMessageViewModel.EmailFrom,
                                CompanyId = company.Id
                            };
                        }

                        conversationMessage.EmailFrom = conversation.EmailAddress;

                        break;
                    case ChannelTypes.WhatsappTwilio:
                        if (string.IsNullOrEmpty(conversationMessageViewModel.ConversationId))
                        {
                            conversation = await _appDbContext.Conversations
                                .Include(x => x.WhatsappUser)
                                .OrderByDescending(x => x.UpdatedTime)
                                .FirstOrDefaultAsync(
                                    x =>
                                        x.WhatsappUser.whatsAppId == conversationMessageViewModel.WhatsappReceiverId
                                        && x.CompanyId == company.Id);
                        }
                        else
                        {
                            conversation = await _appDbContext.Conversations
                                .Include(x => x.WhatsappUser)
                                .FirstOrDefaultAsync(
                                    x =>
                                        x.Id == conversationMessageViewModel.ConversationId
                                        && x.CompanyId == company.Id);
                        }

                        if (conversation == null)
                        {
                            conversation = _mapper.Map<Conversation>(conversationMessageViewModel);

                            // conversation.Status = "open";
                            if (conversation.WhatsappUser == null)
                            {
                                var twilioSender = await _appDbContext.ConfigWhatsAppConfigs
                                    .FirstOrDefaultAsync(x => x.CompanyId == company.Id);

                                if (twilioSender == null)
                                {
                                    return BadRequest(
                                        new ResponseViewModel
                                        {
                                            message = "no sender found"
                                        });
                                }

                                conversation.WhatsappUser = await _appDbContext.SenderWhatsappSenders
                                    .FirstOrDefaultAsync(
                                        x =>
                                            x.whatsAppId == $"whatsapp:+{conversationMessageViewModel.WhatsappReceiverId
                                                .Replace("@c.us", string.Empty)
                                                .Replace("whatsapp:+", string.Empty)}"
                                            && x.CompanyId == company.Id
                                            && x.InstanceId == twilioSender.TwilioAccountId);

                                if (conversation.WhatsappUser == null)
                                {
                                    conversation.WhatsappUser = new WhatsAppSender
                                    {
                                        whatsAppId = $"whatsapp:+{conversationMessageViewModel.WhatsappReceiverId
                                            .Replace("@c.us", string.Empty)
                                            .Replace("whatsapp:+", string.Empty)}",
                                        InstanceId = twilioSender.TwilioAccountId,
                                        CompanyId = company.Id
                                    };
                                }
                            }
                        }

                        // if (conversation.WhatsappUser == null)
                        //    conversation.WhatsappUser = new WhatsAppSender { whatsAppId = conversationMessageViewModel.WhatsappReceiverId, CompanyId = company.Id };
                        if (conversation.WhatsappUser.whatsAppId.Contains("@")
                            && string.IsNullOrEmpty(conversation.WhatsappUser.phone_number))
                        {
                            conversation.WhatsappUser.phone_number =
                                PhoneNumberHelper.GetPhoneNumber(conversation.WhatsappUser.whatsAppId);

                            conversation.WhatsappUser.name = conversation.WhatsappUser.phone_number;
                        }

                        conversationMessage.whatsappReceiver = conversation.WhatsappUser;

                        // Fix for mobile send request
                        if (!string.IsNullOrEmpty(conversationMessage.ChannelIdentityId))
                        {
                            conversationMessageViewModel.ChannelIdentityId = null;
                            conversationMessage.ChannelIdentityId = null;
                        }

                        break;
                    case ChannelTypes.Whatsapp360Dialog:
                        if (string.IsNullOrEmpty(conversationMessageViewModel.ConversationId))
                        {
                            conversation = await _appDbContext.Conversations
                                .Include(x => x.WhatsApp360DialogUser)
                                .OrderByDescending(x => x.UpdatedTime)
                                .FirstOrDefaultAsync(x => x.Id == conversationMessageViewModel.ConversationId);
                        }
                        else
                        {
                            conversation = await _appDbContext.Conversations
                                .Include(x => x.WhatsApp360DialogUser)
                                .FirstOrDefaultAsync(
                                    x =>
                                        x.Id == conversationMessageViewModel.ConversationId
                                        && x.CompanyId == company.Id);
                        }

                        if (conversation.WhatsApp360DialogUser == null ||
                            (conversationMessageViewModel.ChannelId.HasValue &&
                             conversation.WhatsApp360DialogUser.ChannelId != conversationMessageViewModel.ChannelId))
                        {
                            conversationMessage.Whatsapp360DialogReceiver = new WhatsApp360DialogSender()
                            {
                                WhatsAppId = conversationMessageViewModel.Whatsapp360dialogReceiverId,
                                PhoneNumber = "+" + conversationMessageViewModel.Whatsapp360dialogReceiverId,
                                CompanyId = company.Id,
                                ChannelId = conversationMessageViewModel.ChannelId,
                                ChannelWhatsAppPhoneNumber = await _appDbContext.ConfigWhatsApp360DialogConfigs
                                    .Where(
                                        x =>
                                            x.CompanyId == company.Id
                                            && x.Id == conversationMessageViewModel.ChannelId)
                                    .Select(x => x.WhatsAppPhoneNumber)
                                    .FirstAsync(),
                            };
                        }
                        else
                        {
                            conversationMessage.Whatsapp360DialogReceiver = conversation.WhatsApp360DialogUser;
                        }

                        // Fix for mobile send request
                        if (!string.IsNullOrEmpty(conversationMessage.ChannelIdentityId))
                        {
                            conversationMessageViewModel.ChannelIdentityId = null;
                            conversationMessage.ChannelIdentityId = null;
                        }

                        break;

                    case ChannelTypes.WhatsappCloudApi:
                        if (string.IsNullOrEmpty(conversationMessageViewModel.ConversationId))
                        {
                            conversation = await _appDbContext.Conversations
                                .Include(x => x.WhatsappCloudApiUser)
                                .OrderByDescending(x => x.UpdatedTime)
                                .FirstOrDefaultAsync(x => x.Id == conversationMessageViewModel.ConversationId);
                        }
                        else
                        {
                            conversation = await _appDbContext.Conversations
                                .Include(x => x.WhatsappCloudApiUser)
                                .FirstOrDefaultAsync(
                                    x =>
                                        x.Id == conversationMessageViewModel.ConversationId
                                        && x.CompanyId == company.Id);
                        }

                        conversationMessage.WhatsappCloudApiReceiver = conversation.WhatsappCloudApiUser;

                        break;
                    case ChannelTypes.LiveChat:
                        if (!string.IsNullOrEmpty(conversationMessageViewModel.WebClientSenderId))
                        {
                            conversationMessage.WebClientSender = await _appDbContext.SenderWebClientSenders
                                .Where(
                                    x =>
                                        x.WebClientUUID == conversationMessageViewModel.WebClientSenderId
                                        && x.CompanyId == company.Id)
                                .FirstOrDefaultAsync();

                            conversation.WebClient = conversationMessage.WebClientSender;
                        }
                        else if (!string.IsNullOrEmpty(conversationMessageViewModel.WebClientReceiverId))
                        {
                            conversationMessage.WebClientReceiver = await _appDbContext.SenderWebClientSenders
                                .Where(
                                    x =>
                                        x.WebClientUUID == conversationMessageViewModel.WebClientReceiverId
                                        && x.CompanyId == company.Id)
                                .FirstOrDefaultAsync();

                            conversation.WebClient = conversationMessage.WebClientReceiver;
                        }

                        if (conversation.WebClient == null)
                        {
                            return BadRequest();
                        }

                        break;
                    case ChannelTypes.Wechat:
                        if (string.IsNullOrEmpty(conversationMessageViewModel.WeChatReceiverOpenId))
                        {
                            return BadRequest();
                        }

                        conversation = await _appDbContext.Conversations
                            .Include(x => x.WeChatUser)
                            .FirstOrDefaultAsync(
                                x =>
                                    x.WeChatUser.openid == conversationMessageViewModel.WeChatReceiverOpenId
                                    && x.CompanyId == company.Id);

                        if (conversation.WeChatUser == null)
                        {
                            conversation.WeChatUser = new WeChatSender
                            {
                                openid = conversationMessageViewModel.WeChatReceiverOpenId,
                                CompanyId = company.Id
                            };
                        }

                        conversationMessage.WeChatReceiver = conversation.WeChatUser;

                        break;
                    case ChannelTypes.Line:
                        conversation = await _appDbContext.Conversations
                            .Include(x => x.LineUser)
                            .FirstOrDefaultAsync(
                                x =>
                                    x.LineUser.userId == conversationMessageViewModel.LineReceiverId
                                    && x.CompanyId == company.Id);

                        if (conversation.LineUser == null)
                        {
                            conversation.LineUser = new LineSender
                            {
                                userId = conversationMessageViewModel.LineReceiverId,
                                CompanyId = company.Id
                            };
                        }

                        conversationMessage.LineReceiver = conversation.LineUser;

                        break;
                    case ChannelTypes.Viber:
                        conversation = await _appDbContext.Conversations
                            .Include(x => x.ViberUser)
                            .FirstOrDefaultAsync(
                                x =>
                                    x.CompanyId == company.Id
                                    && x.Id == conversationMessageViewModel.ConversationId);

                        if (!conversation.ViberUser.IsSubscribed)
                        {
                            return BadRequest(
                                new ResponseViewModel()
                                {
                                    message = "This Viber user is currently unsubscribed."
                                });
                        }

                        conversationMessage.ViberReceiver = conversation.ViberUser;

                        break;
                    case ChannelTypes.Telegram:
                        conversation = await _appDbContext.Conversations
                            .Include(x => x.TelegramUser)
                            .FirstOrDefaultAsync(
                                x =>
                                    x.CompanyId == company.Id
                                    && x.Id == conversationMessageViewModel.ConversationId);

                        conversationMessage.TelegramReceiver = conversation.TelegramUser;

                        break;

                    case ChannelTypes.Sms:
                        if (!await _appDbContext.ConfigSMSConfigs
                                .AnyAsync(x => x.CompanyId == company.Id))
                        {
                            return BadRequest(
                                new ResponseViewModel
                                {
                                    message = $"no sms config"
                                });
                        }

                        conversation = await _appDbContext.Conversations
                            .Include(x => x.SMSUser)
                            .FirstOrDefaultAsync(
                                x =>
                                    x.SMSUser.SMSId == conversationMessageViewModel.SMSReceiverId
                                    && x.CompanyId == company.Id);

                        if (conversation == null)
                        {
                            var userProfile = await _appDbContext.UserProfiles
                                .Include(x => x.SMSUser)
                                .FirstOrDefaultAsync(
                                    x =>
                                        x.SMSUser.SMSId == conversationMessageViewModel.SMSReceiverId
                                        && x.CompanyId == company.Id);

                            if (userProfile != null)
                            {
                                conversation = await _appDbContext.Conversations
                                    .FirstOrDefaultAsync(
                                        x =>
                                            x.UserProfileId == userProfile.Id
                                            && x.CompanyId == company.Id);

                                conversation.SMSUserId = userProfile.SMSUserId;
                                await _appDbContext.SaveChangesAsync();
                            }
                            else if (!string.IsNullOrEmpty(conversationMessageViewModel.ConversationId))
                            {
                                conversation = await _appDbContext.Conversations
                                    .Include(x => x.SMSUser)
                                    .FirstOrDefaultAsync(
                                        x =>
                                            x.Id == conversationMessageViewModel.ConversationId
                                            && x.CompanyId == company.Id);

                                if (conversation == null)
                                {
                                    return BadRequest(
                                        new ResponseViewModel
                                        {
                                            message =
                                                $"Not able to send message to {conversationMessageViewModel.SMSReceiverId}"
                                        });
                                }

                                userProfile = await _appDbContext.UserProfiles
                                    .Include(x => x.SMSUser)
                                    .FirstOrDefaultAsync(
                                        x =>
                                            x.Id == conversation.UserProfileId &&
                                            x.CompanyId == company.Id);

                                if (userProfile == null)
                                {
                                    return BadRequest(
                                        new ResponseViewModel
                                        {
                                            message =
                                                $"Not able to send message to {conversationMessageViewModel.SMSReceiverId}"
                                        });
                                }

                                userProfile.SMSUser = new SMSSender
                                {
                                    SMSId = conversationMessageViewModel.SMSReceiverId,
                                    phone_number = conversationMessageViewModel.SMSReceiverId
                                        .Replace("+", string.Empty),
                                    name = conversationMessageViewModel.SMSReceiverId
                                        .Replace("+", string.Empty),
                                    CompanyId = company.Id
                                };

                                conversation.SMSUser = userProfile.SMSUser;
                                await _appDbContext.SaveChangesAsync();
                            }
                        }

                        if (conversation.SMSUser == null)
                        {
                            conversation.SMSUser = new SMSSender
                            {
                                SMSId = conversationMessageViewModel.SMSReceiverId,
                                phone_number = conversationMessageViewModel.SMSReceiverId
                                    .Replace("+", string.Empty),
                                name = conversationMessageViewModel.SMSReceiverId
                                    .Replace("+", string.Empty),
                                CompanyId = company.Id
                            };
                        }

                        conversationMessage.SMSReceiver = conversation.SMSUser;

                        break;

                    case ChannelTypes.Instagram:
                        if (conversationMessageViewModel.MessageType == "interactive")
                        {
                            if (conversationMessageViewModel.ExtendedMessagePayload == null)
                            {
                                return BadRequest(
                                    new ResponseViewModel()
                                    {
                                        message = "Please Add Extend Message Payload for Interactive Message"
                                    });
                            }
                        }

                        conversation = await _appDbContext.Conversations
                            .Include(x => x.InstagramUser)
                            .FirstOrDefaultAsync(
                                x =>
                                    x.Id == conversationMessageViewModel.ConversationId
                                    && x.CompanyId == company.Id);

                        conversationMessage.InstagramReceiver = conversation.InstagramUser;

                        break;
                }

                try
                {
                    List<ConversationMessage> results = null;

                    var textConversationMessage = _mapper.Map<ConversationMessage>(conversationMessageViewModel);
                    var textConversation = _mapper.Map<Conversation>(conversationMessageViewModel);

                    if (conversationMessageViewModel.QuickReplyId.HasValue)
                    {
                        var quickReply = await _appDbContext.CompanyQuickReplies
                            .Include(x => x.QuickReplyFile)
                            .FirstOrDefaultAsync(
                                x =>
                                    x.Id == conversationMessageViewModel.QuickReplyId.Value
                                    && x.CompanyId == company.Id);

                        if (quickReply.QuickReplyFile != null)
                        {
                            conversationMessage.DeliveryType = DeliveryType.QuickReply;
                            conversationMessage.MessageType = "file";

                            var provider = new FileExtensionContentTypeProvider();
                            string contentType;

                            var domainName = _configuration.GetValue<string>("Values:DomainName");
                            var url =
                                $"{domainName}/company/quickReply/attachment/private/{quickReply.QuickReplyFile.QuickReplyFileId}";

                            var fileURLMessages = new List<FileURLMessage>();

                            if (!provider.TryGetContentType(url, out contentType))
                            {
                                contentType = "application/octet-stream";
                            }

                            switch (conversationMessage.Channel)
                            {
                                case ChannelTypes.Facebook:
                                case ChannelTypes.Instagram:
                                case ChannelTypes.Wechat:
                                    conversationMessage.MessageContent = null;
                                    break;
                            }

                            fileURLMessages.Add(
                                new FileURLMessage
                                {
                                    FileName = Path.GetFileName(quickReply.QuickReplyFile.Filename),
                                    FileURL = url,
                                    MIMEType = contentType
                                });

                            results = (await _conversationMessageService.SendFileMessageByFBURL(
                                conversation,
                                conversationMessage,
                                fileURLMessages))
                                .ToList();

                            IEnumerable<ConversationMessage> textMessageResult;

                            switch (conversationMessage.Channel)
                            {
                                case ChannelTypes.Facebook:
                                    textConversationMessage.MessageUniqueID = null;
                                    textConversationMessage.Id = 0;
                                    textConversationMessage.MessageChecksum = Guid.NewGuid().ToString();
                                    textConversationMessage.MessageType = "text";

                                    if (User.Identity.IsAuthenticated)
                                    {
                                        textConversationMessage.SenderId = _userManager.GetUserId(User);
                                        textConversationMessage.Sender = await _userManager.GetUserAsync(User);
                                        textConversationMessage.IsSentFromSleekflow = true;

                                        var staffInfo = await _appDbContext.UserRoleStaffs
                                            .Include(x => x.Company)
                                            .FirstOrDefaultAsync(x => x.IdentityId == textConversationMessage.SenderId);

                                        if (textConversation.MessageGroupName != staffInfo.Company.SignalRGroupName)
                                        {
                                            return Unauthorized();
                                        }
                                    }

                                    if (string.IsNullOrEmpty(conversationMessageViewModel.ConversationId))
                                    {
                                        textConversation = await _appDbContext.Conversations
                                            .Include(x => x.facebookUser)
                                            .FirstOrDefaultAsync(
                                                x =>
                                                    x.facebookUser.FacebookId == conversationMessageViewModel.FacebookReceiverId
                                                    && x.CompanyId == company.Id);
                                    }
                                    else
                                    {
                                        textConversation = await _appDbContext.Conversations
                                            .Include(x => x.facebookUser)
                                            .FirstOrDefaultAsync(
                                                x =>
                                                    x.Id == conversationMessageViewModel.ConversationId
                                                    && x.CompanyId == company.Id);
                                    }

                                    if (textConversation.facebookUser == null)
                                    {
                                        textConversation.facebookUser = await _appDbContext.SenderFacebookSenders
                                            .FirstOrDefaultAsync(
                                                x =>
                                                    x.FacebookId == conversationMessageViewModel.FacebookReceiverId
                                                    && x.CompanyId == company.Id);

                                        if (textConversation.facebookUser == null)
                                        {
                                            textConversation.facebookUser = new FacebookSender
                                            {
                                                FacebookId = conversationMessageViewModel.FacebookReceiverId,
                                                CompanyId = company.Id
                                            };
                                        }
                                    }

                                    textConversationMessage.facebookReceiver = textConversation.facebookUser;
                                    textConversationMessage.ConversationId = textConversation.Id;
                                    textConversationMessage.Conversation = textConversation;

                                    textMessageResult = (await _conversationMessageService.SendMessage(
                                        textConversation,
                                        textConversationMessage))
                                        .ToList();

                                    results.AddRange(textMessageResult);

                                    break;
                                case ChannelTypes.Instagram:
                                    textConversationMessage.MessageUniqueID = null;
                                    textConversationMessage.Id = 0;
                                    textConversationMessage.MessageChecksum = Guid.NewGuid().ToString();
                                    textConversationMessage.UploadedFiles = new List<UploadedFile>();
                                    textConversationMessage.MessageType = "text";

                                    textConversation = await _appDbContext.Conversations
                                        .Include(x => x.InstagramUser)
                                        .FirstOrDefaultAsync(
                                            x =>
                                                x.Id == conversationMessageViewModel.ConversationId
                                                && x.CompanyId == company.Id);

                                    textConversationMessage.InstagramReceiver = textConversation.InstagramUser;
                                    textConversationMessage.ConversationId = textConversation.Id;
                                    textConversationMessage.Conversation = textConversation;

                                    textMessageResult = (await _conversationMessageService.SendMessage(
                                        textConversation,
                                        textConversationMessage))
                                        .AsEnumerable();

                                    results.AddRange(textMessageResult);

                                    break;
                                case ChannelTypes.Wechat:
                                    var companyQuickReplyLingualsList =
                                        await _appDbContext.CompanyQuickReplies
                                            .AsNoTracking()
                                            .Where(x => x.Id == quickReply.Id)
                                            .Include(x => x.CompanyQuickReplyLinguals)
                                            .SelectMany(x => x.CompanyQuickReplyLinguals)
                                            .ToListAsync();

                                    var textMsg = companyQuickReplyLingualsList.FirstOrDefault().Value;

                                    textConversationMessage.MessageUniqueID = null;
                                    textConversationMessage.Id = 0;
                                    textConversationMessage.MessageChecksum = Guid.NewGuid().ToString();
                                    textConversationMessage.UploadedFiles = new List<UploadedFile>();
                                    textConversationMessage.MessageType = "text";
                                    textConversationMessage.MessageContent = textMsg;

                                    if (User.Identity.IsAuthenticated)
                                    {
                                        textConversationMessage.SenderId = _userManager.GetUserId(User);
                                        textConversationMessage.Sender = await _userManager.GetUserAsync(User);
                                        textConversationMessage.IsSentFromSleekflow = true;

                                        var staffInfo = await _appDbContext.UserRoleStaffs
                                            .Include(x => x.Company)
                                            .FirstOrDefaultAsync(x => x.IdentityId == textConversationMessage.SenderId);

                                        if (textConversation.MessageGroupName != staffInfo.Company.SignalRGroupName)
                                        {
                                            return Unauthorized();
                                        }
                                    }

                                    textConversation =
                                        string.IsNullOrEmpty(conversationMessageViewModel.ConversationId) ?
                                            await _appDbContext.Conversations
                                                .Include(x => x.WeChatUser)
                                                .FirstOrDefaultAsync(
                                                    x =>
                                                        x.WeChatUser.openid == conversationMessageViewModel
                                                            .WeChatReceiverOpenId
                                                        && x.CompanyId == company.Id) :
                                            await _appDbContext.Conversations
                                                .Include(x => x.WeChatUser)
                                                .FirstOrDefaultAsync(
                                                    x =>
                                                        x.Id == conversationMessageViewModel.ConversationId
                                                        && x.CompanyId == company.Id);

                                    if (textConversation.WeChatUser == null)
                                    {
                                        textConversation.WeChatUser = await _appDbContext.SenderWeChatSenders
                                            .FirstOrDefaultAsync(
                                                x =>
                                                    x.openid == conversationMessageViewModel.WeChatReceiverOpenId
                                                    && x.CompanyId == company.Id);

                                        if (textConversation.WeChatUser == null)
                                        {
                                            textConversation.WeChatUser = new WeChatSender
                                            {
                                                openid = conversationMessageViewModel.WeChatReceiverOpenId,
                                                CompanyId = company.Id
                                            };
                                        }
                                    }

                                    textConversationMessage.WeChatReceiver = textConversation.WeChatUser;
                                    textConversationMessage.ConversationId = textConversation.Id;
                                    textConversationMessage.Conversation = textConversation;

                                    textMessageResult = (await _conversationMessageService.SendMessage(
                                        textConversation,
                                        textConversationMessage))
                                        .ToList();

                                    results.AddRange(textMessageResult);
                                    break;
                            }
                        }
                    }
                    else if (conversationMessageViewModel.fileURLs?.Count > 0)
                    {
                        conversationMessage.DeliveryType = DeliveryType.QuickReply;
                        conversationMessage.MessageType = "file";

                        var provider = new FileExtensionContentTypeProvider();
                        string contentType;

                        var fileURLMessages = new List<FileURLMessage>();

                        for (var i = 0; i < conversationMessageViewModel.fileURLs.Count; i++)
                        {
                            var url = conversationMessageViewModel.fileURLs[i];
                            var fileName = conversationMessageViewModel.fileNames[i];

                            if (!provider.TryGetContentType(url, out contentType))
                            {
                                contentType = "application/octet-stream";
                            }

                            fileURLMessages.Add(
                                new FileURLMessage
                                {
                                    FileName = fileName,
                                    FileURL = url,
                                    MIMEType = contentType
                                });
                        }

                        switch (conversationMessage.Channel)
                        {
                            case ChannelTypes.Facebook:
                            case ChannelTypes.Instagram:
                                conversationMessage.MessageContent = null;
                                break;
                        }

                        results = (await _conversationMessageService.SendFileMessageByFBURL(
                            conversation,
                            conversationMessage,
                            fileURLMessages))
                            .ToList();

                        switch (conversationMessage.Channel)
                        {
                            case ChannelTypes.Facebook:
                                textConversationMessage.MessageUniqueID = null;
                                textConversationMessage.Id = 0;
                                textConversationMessage.MessageChecksum =
                                    Guid.NewGuid().ToString(); // Empty the files?
                                textConversationMessage.MessageType = "text";

                                if (User.Identity.IsAuthenticated)
                                {
                                    textConversationMessage.SenderId = _userManager.GetUserId(User);
                                    textConversationMessage.Sender = await _userManager.GetUserAsync(User);
                                    textConversationMessage.IsSentFromSleekflow = true;

                                    var staffInfo = await _appDbContext.UserRoleStaffs
                                        .Include(x => x.Company)
                                        .FirstOrDefaultAsync(x => x.IdentityId == textConversationMessage.SenderId);

                                    if (textConversation.MessageGroupName != staffInfo.Company.SignalRGroupName)
                                    {
                                        return Unauthorized();
                                    }
                                }

                                if (string.IsNullOrEmpty(conversationMessageViewModel.ConversationId))
                                {
                                    textConversation = await _appDbContext.Conversations
                                        .Include(x => x.facebookUser)
                                        .FirstOrDefaultAsync(
                                            x =>
                                                x.facebookUser.FacebookId == conversationMessageViewModel.FacebookReceiverId
                                                && x.CompanyId == company.Id);
                                }
                                else
                                {
                                    textConversation = await _appDbContext.Conversations
                                        .Include(x => x.facebookUser)
                                        .FirstOrDefaultAsync(
                                            x =>
                                                x.Id == conversationMessageViewModel.ConversationId
                                                && x.CompanyId == company.Id);
                                }

                                if (textConversation.facebookUser == null)
                                {
                                    textConversation.facebookUser = await _appDbContext.SenderFacebookSenders
                                        .FirstOrDefaultAsync(
                                            x =>
                                                x.FacebookId == conversationMessageViewModel.FacebookReceiverId
                                                && x.CompanyId == company.Id);

                                    if (textConversation.facebookUser == null)
                                    {
                                        textConversation.facebookUser = new FacebookSender
                                        {
                                            FacebookId = conversationMessageViewModel.FacebookReceiverId,
                                            CompanyId = company.Id
                                        };
                                    }
                                }

                                textConversationMessage.facebookReceiver = textConversation.facebookUser;
                                textConversationMessage.ConversationId = textConversation.Id;
                                textConversationMessage.Conversation = textConversation;

                                var results2 = (await _conversationMessageService.SendMessage(
                                    textConversation,
                                    textConversationMessage))
                                    .ToList();

                                results.AddRange(results2);
                                break;
                            case ChannelTypes.Instagram:
                                break;
                        }
                    }
                    else
                    {
                        results = (await _conversationMessageService.SendMessage(
                                conversation,
                                conversationMessage))
                            .ToList();
                    }

                    var conversationMessagesVM = _mapper.Map<List<ConversationMessageResponseViewModel>>(results);
                    await _sleekPayService.AddSleekPayRecord(conversationMessagesVM);

                    return Ok(conversationMessagesVM);
                }
                catch (Exception ex)
                {
                    _logger.LogError(
                        ex,
                        "Message/Send (inner) exception: {ExceptionMessage}, Request payload: {RequestPayload}",
                        ex.Message,
                        JsonConvert.SerializeObject(conversationMessageViewModel));

                    return BadRequest(ex.Message);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Message/Send (outer) exception: {ExceptionMessage}, Request payload: {RequestPayload}",
                    ex.Message,
                    JsonConvert.SerializeObject(conversationMessageViewModel));

                return BadRequest(
                    new ResponseViewModel
                    {
                        message = $"{ex.ToString()}"
                    });
            }
        }

        [HttpPost]
        [Authorize]
        [Route("Message/Forward")]
        public async Task<IActionResult> Forward([FromBody] ForwardMessageViewModel forwardMessageViewModel)
        {
            try
            {
                var companyUser = await _coreService.GetCompanyStaff(
                    await _userManager.GetUserAsync(User));

                if (companyUser == null)
                {
                    return Unauthorized();
                }

                var forwardedCount = 0;

                foreach (var conversationId in forwardMessageViewModel.ConversationIds)
                {
                    var conversation = await _appDbContext.Conversations
                        .Include(x => x.facebookUser)
                        .Include(x => x.WhatsappUser)
                        .Include(x => x.EmailAddress)
                        .Include(x => x.WeChatUser)
                        .Include(x => x.WebClient)
                        .Include(x => x.LineUser)
                        .Include(x => x.SMSUser)
                        .Include(x => x.InstagramUser)
                        .Include(x => x.ViberUser)
                        .Include(x => x.TelegramUser)
                        .Include(x => x.WhatsApp360DialogUser)
                        .Include(x => x.WhatsappCloudApiUser)
                        .FirstOrDefaultAsync(
                            x =>
                                x.CompanyId == companyUser.CompanyId
                                && (x.Id == conversationId
                                    || x.UserProfileId == conversationId));

                    if (conversation == null)
                    {
                        try
                        {
                            conversation = await _userProfileService.GetConversationByUserProfileId(
                                companyUser.CompanyId,
                                conversationId,
                                "closed",
                                false);

                            // switch (staffInfo.RoleType)
                            // {
                            //    case StaffUserRole.Staff:
                            //        if (_appDbContext.Conversations.Where(y => y.Id == conversation.Id && (y.AssigneeId == staffInfo.Id || !y.AssigneeId.HasValue)).Count() > 0)
                            //            break;
                            //        return Unauthorized();
                            //    case StaffUserRole.TeamAdmin:
                            //        var teams = _appDbContext.CompanyStaffTeams.Where(x => x.Members.Where(y => y.StaffId == staffInfo.Id).Count() > 0);
                            //        if (_appDbContext.Conversations.Where(y => y.Id == conversation.Id && (y.AssigneeId == staffInfo.Id || !y.AssigneeId.HasValue || teams.Where(z => z.Members.Where(a => a.StaffId == y.AssigneeId).Count() > 0).Count() > 0)).Count() > 0 ||
                            //            _appDbContext.Conversations.Where(y => y.Id == conversation.Id && y.AssignedTeam.Members.Where(x => x.StaffId == staffInfo.Id).Count() > 0).Count() > 0)
                            //            break;
                            //        return Unauthorized();
                            // }
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(
                                ex,
                                "[{MethodName} endpoint] Error getting conversation {ConversationOrUserProfileId}. {ExceptionMessage}",
                                nameof(Forward),
                                conversationId,
                                ex.Message);

                            continue;
                        }
                    }

                    // WSChatAPIConfig config = null;
                    // if (conversation.WhatsappUserId.HasValue)
                    //    config = await _appDbContext.ConfigWSChatAPIConfigs.Where(x => x.CompanyId == conversation.CompanyId && x.WSChatAPIInstance == conversation.WhatsappUser.InstanceId && !x.IsDeleted).FirstOrDefaultAsync();
                    foreach (var forwardedMessageId in forwardMessageViewModel.MessageIds)
                    {
                        var messageTobeForwarded = await _appDbContext.ConversationMessages
                            .Include(x => x.UploadedFiles)
                            .FirstOrDefaultAsync(
                                x =>
                                    x.CompanyId == companyUser.CompanyId
                                    && x.Id == forwardedMessageId);

                        if (messageTobeForwarded == null)
                        {
                            continue;
                        }

                        // switch (staffInfo.RoleType)
                        // {
                        //    case StaffUserRole.Staff:
                        //        if (_appDbContext.Conversations.Where(y => y.Id == messageTobeForwarded.ConversationId && (y.AssigneeId == staffInfo.Id || !y.AssigneeId.HasValue)).Count() > 0)
                        //            break;
                        //        return Unauthorized();
                        //    case StaffUserRole.TeamAdmin:
                        //        var teams = _appDbContext.CompanyStaffTeams.Where(x => x.Members.Where(y => y.StaffId == staffInfo.Id).Count() > 0);
                        //        if (_appDbContext.Conversations.Where(y => y.Id == messageTobeForwarded.ConversationId && (y.AssigneeId == staffInfo.Id || !y.AssigneeId.HasValue || teams.Where(z => z.Members.Where(a => a.StaffId == y.AssigneeId).Count() > 0).Count() > 0)).Count() > 0 ||
                        //            _appDbContext.Conversations.Where(y => y.Id == messageTobeForwarded.ConversationId && y.AssignedTeam.Members.Where(x => x.StaffId == staffInfo.Id).Count() > 0).Count() > 0)
                        //            break;
                        //        return Unauthorized();
                        // }
                        var conversationMessageViewModel = new ExtendedConversationMessageViewModel
                        {
                            Channel = conversation.LastMessageChannel,
                            MessageContent = messageTobeForwarded.MessageContent,
                            MessageType = messageTobeForwarded.MessageType,
                            ConversationId = conversation.Id,
                            MessageGroupName = companyUser.CompanyId,
                        };

                        if (messageTobeForwarded.UploadedFiles?.Count > 0)
                        {
                            conversationMessageViewModel.fileURLs = new List<string>();
                            conversationMessageViewModel.fileNames = new List<string>();

                            var domainName = _configuration.GetValue<String>("Values:DomainName");

                            foreach (var file in messageTobeForwarded.UploadedFiles)
                            {
                                conversationMessageViewModel.fileURLs.Add(
                                    $"{domainName}/Message/File/Private/{file.FileId}");
                                var fileName = Path.GetFileName(file.Filename);
                                conversationMessageViewModel.fileNames.Add(fileName);
                            }
                        }

                        switch (conversation.LastMessageChannel)
                        {
                            case ChannelTypes.Facebook:
                                conversationMessageViewModel.FacebookReceiverId = conversation.facebookUser?.FacebookId;

                                break;
                            case ChannelTypes.WhatsappTwilio:
                                conversationMessageViewModel.WhatsappReceiverId = conversation.WhatsappUser?.whatsAppId;

                                // if (config != null && conversationMessageViewModel.Channel == messageTobeForwarded.Channel)
                                // {
                                //    messageTobeForwarded.whatsappReceiver = await _appDbContext.SenderWhatsappSenders.Where(X => X.Id == messageTobeForwarded.whatsappReceiverId).FirstOrDefaultAsync();
                                //    if (messageTobeForwarded?.whatsappReceiver?.InstanceId == conversation.WhatsappUser?.InstanceId)
                                //    {
                                //        //do forward
                                //        var request = new ForwardRequest
                                //        {
                                //            chatId = conversation.WhatsappUser?.whatsAppId,
                                //            messageId = new List<string> { messageTobeForwarded.MessageUniqueID }
                                //        };

                                // var client = new HttpClient();
                                //        var sendMessageResponse = await client.PostAsJsonAsync($"{config.WSChatAPIURL}/forwardMessage?token={config.WSChatAPIKey}", request);
                                //        var sendMessageString = await sendMessageResponse.Content.ReadAsStringAsync();
                                //        var sendMessage = JsonConvert.DeserializeObject<SendChatAPIResponseRequest>(sendMessageString);

                                // if (sendMessage.sent)
                                //        {
                                //            forwardedCount += 1;
                                //            continue;
                                //        }
                                //    }
                                // }
                                break;
                            case ChannelTypes.Whatsapp360Dialog:
                                conversationMessageViewModel.Whatsapp360dialogReceiverId =
                                    conversation.WhatsApp360DialogUser.WhatsAppId;

                                break;
                            case ChannelTypes.WhatsappCloudApi:
                                conversationMessageViewModel.WhatsappCloudApiReceiverId =
                                    conversation.WhatsappCloudApiUser.WhatsappId;

                                break;
                            case ChannelTypes.Line:
                                conversationMessageViewModel.LineReceiverId = conversation.LineUser?.userId;

                                break;
                            case ChannelTypes.Viber:
                                conversationMessageViewModel.ViberReceiverId = conversation.ViberUser?.ViberUserId;

                                break;
                            case ChannelTypes.Telegram:
                                conversationMessageViewModel.TelegramReceiverId =
                                    conversation.TelegramUser.TelegramChatId;

                                break;
                            case ChannelTypes.Wechat:
                                conversationMessageViewModel.WeChatReceiverOpenId = conversation.WeChatUser?.openid;

                                break;
                            case ChannelTypes.Sms:
                                conversationMessageViewModel.SMSReceiverId = conversation.SMSUser?.SMSId;

                                break;
                            case ChannelTypes.Email:
                                break;
                            case ChannelTypes.LiveChat:
                                conversationMessageViewModel.WebClientReceiverId =
                                    conversation.WebClient?.WebClientUUID;

                                break;
                            case null:
                                if (conversation.WhatsappUserId.HasValue)
                                {
                                    conversationMessageViewModel.Channel = ChannelTypes.WhatsappTwilio;
                                    conversationMessageViewModel.WhatsappReceiverId =
                                        conversation.WhatsappUser?.whatsAppId;

                                    break;
                                }

                                continue;
                        }

                        await Send(conversationMessageViewModel);

                        forwardedCount += 1;
                    }
                }

                return Ok(
                    new ResponseViewModel
                    {
                        message = $"Forwarded to {forwardedCount} people"
                    });
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Message/Forward exception: {ExceptionMessage}, RequestPayload: {RequestPayload}",
                    ex.Message,
                    JsonConvert.SerializeObject(forwardMessageViewModel));
            }

            return BadRequest();
        }

        [HttpPost]
        [Authorize]
        [Route("Message/Schedule/Delete")]
        public async Task<ActionResult<ResponseViewModel>> RemoveScheduledMessage(
            [FromBody]
            DeleteMessageInput input)
        {
            var companyUser = await _coreService.GetCompanyStaff(
                await _userManager.GetUserAsync(User));

            if (companyUser == null)
            {
                return Unauthorized();
            }

            await _conversationMessageService.RemoveScheduledMessage(
                companyUser.CompanyId,
                input);

            return Ok(
                new ResponseViewModel
                {
                    message = "success"
                });
        }

        [HttpPost]
        [Route("/Message/File/Send")]
        public async Task<IActionResult> SendFileMessage(
            [FromForm]
            ConversationMessageViewModel conversationMessageViewModel)
        {
            var conversationMessage = _mapper.Map<ConversationMessage>(conversationMessageViewModel);
            var conversation = _mapper.Map<Conversation>(conversationMessageViewModel);

            var company = await _appDbContext.CompanyCompanies
                .FirstOrDefaultAsync(x => x.SignalRGroupName == conversation.MessageGroupName);

            if (User.Identity.IsAuthenticated)
            {
                conversationMessage.SenderId = _userManager.GetUserId(User);
                conversationMessage.Sender = await _userManager.GetUserAsync(User);
                conversationMessage.IsSentFromSleekflow = true;

                var staffInfo = await _appDbContext.UserRoleStaffs
                    .Include(x => x.Company)
                    .FirstOrDefaultAsync(x => x.IdentityId == conversationMessage.SenderId);

                if (conversation.MessageGroupName != staffInfo.Company.SignalRGroupName)
                {
                    return Unauthorized();
                }

                // Bug Fix: DEVS-2429 - [MediLase] collaborator is removed from the convo but still able to reply
                // MediLase only
                if (staffInfo.CompanyId == "07959d97-3a5a-4155-a240-e9f47dba13fa"
                    && !await _conversationService.IsStaffAllowedToSendMessage(
                        staffInfo,
                        conversationMessageViewModel.ConversationId))
                {
                    var errorMessage = string.Format(
                        "The staff with Id:{0} and role:{1} is not allowed to send messages in the conversation with Id:{2}",
                        staffInfo.Id,
                        staffInfo.RoleType.ToString(),
                        conversation.Id);

                    _logger.LogError(
                        "[{MethodName}] The staff with Id:{StaffId} and role:{StaffRole} is not allowed to send messages in the conversation with Id:{ConversationId}",
                        nameof(Send),
                        staffInfo.Id,
                        staffInfo.RoleType.ToString(),
                        conversation.Id);

                    return Unauthorized(
                        new ResponseViewModel
                        {
                            message = errorMessage
                        });
                }
            }

            if (!string.IsNullOrEmpty(conversationMessageViewModel.MessageChecksum)
                && await _appDbContext.ConversationMessages
                    .AnyAsync(
                        x =>
                            x.CompanyId == company.Id
                            && x.MessageChecksum == conversationMessageViewModel.MessageChecksum))
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        message = $"message with checksum: {conversationMessageViewModel.MessageChecksum} sent before."
                    });
            }

            switch (conversationMessage.Channel)
            {
                // case "naive":
                //    if (!string.IsNullOrEmpty(conversationMessage.ReceiverId))
                //    {
                //        conversation.NaiveUser = _appDbContext.UserRoleGuests.Where(x => x.IdentityId == conversationMessage.ReceiverId && x.CompanyId == company.Id).FirstOrDefault();
                //    }

                // if (conversation.NaiveUser == null)
                //    {
                //        conversation.NaiveUser = _appDbContext.UserRoleGuests.Where(x => x.IdentityId == conversationMessage.SenderId && x.CompanyId == company.Id).FirstOrDefault();
                //    }
                //    break;
                case ChannelTypes.Facebook:
                    if (string.IsNullOrEmpty(conversationMessageViewModel.ConversationId))
                    {
                        conversation = await _appDbContext.Conversations
                            .Include(x => x.facebookUser)
                            .FirstOrDefaultAsync(
                                x =>
                                    x.facebookUser.FacebookId == conversationMessageViewModel.FacebookReceiverId
                                    && x.CompanyId == company.Id);
                    }
                    else
                    {
                        conversation = await _appDbContext.Conversations
                            .Include(x => x.facebookUser)
                            .FirstOrDefaultAsync(
                                x =>
                                    x.Id == conversationMessageViewModel.ConversationId
                                    && x.CompanyId == company.Id);
                    }

                    if (conversation.facebookUser == null)
                    {
                        conversation.facebookUser = await _appDbContext.SenderFacebookSenders
                            .FirstOrDefaultAsync(
                                x =>
                                    x.FacebookId == conversationMessageViewModel.FacebookReceiverId
                                    && x.CompanyId == company.Id);

                        if (conversation.facebookUser == null)
                        {
                            conversation.facebookUser = new FacebookSender
                            {
                                FacebookId = conversationMessageViewModel.FacebookReceiverId,
                                CompanyId = company.Id
                            };
                        }
                    }

                    conversationMessage.facebookReceiver = conversation.facebookUser;

                    break;
                case ChannelTypes.WhatsappTwilio:
                    if (string.IsNullOrEmpty(conversationMessageViewModel.ConversationId))
                    {
                        conversation = await _appDbContext.Conversations
                            .Include(x => x.WhatsappUser)
                            .OrderByDescending(x => x.UpdatedTime)
                            .FirstOrDefaultAsync(
                                x =>
                                    x.WhatsappUser.whatsAppId == conversationMessageViewModel.WhatsappReceiverId
                                    && x.CompanyId == company.Id);
                    }
                    else
                    {
                        conversation = await  _appDbContext.Conversations
                            .Include(x => x.WhatsappUser)
                            .FirstOrDefaultAsync(
                                x =>
                                    x.Id == conversationMessageViewModel.ConversationId
                                    && x.WhatsappUser.whatsAppId == conversationMessageViewModel.WhatsappReceiverId
                                    && x.CompanyId == company.Id);
                    }

                    if (conversation == null)
                    {
                        conversation = _mapper.Map<Conversation>(conversationMessageViewModel);

                        // conversation.Status = "open";
                        if (conversation.WhatsappUser == null)
                        {
                            var twilioSender = await _appDbContext.ConfigWhatsAppConfigs
                                .FirstOrDefaultAsync(x => x.CompanyId == company.Id);

                            if (twilioSender == null)
                            {
                                return BadRequest(
                                    new ResponseViewModel
                                    {
                                        message = "no sender found"
                                    });
                            }

                            conversation.WhatsappUser = await _appDbContext.SenderWhatsappSenders
                                .FirstOrDefaultAsync(
                                    x =>
                                        x.whatsAppId == $"whatsapp:+{conversationMessageViewModel.WhatsappReceiverId
                                            .Replace("@c.us", string.Empty)
                                            .Replace("whatsapp:+", string.Empty)}"
                                        && x.CompanyId == company.Id
                                        && x.InstanceId == twilioSender.TwilioAccountId);

                            if (conversation.WhatsappUser == null)
                            {
                                conversation.WhatsappUser = new WhatsAppSender
                                {
                                    whatsAppId = $"whatsapp:+{conversationMessageViewModel.WhatsappReceiverId
                                        .Replace("@c.us", string.Empty)
                                        .Replace("whatsapp:+", string.Empty)}",
                                    InstanceId = twilioSender.TwilioAccountId,
                                    CompanyId = company.Id
                                };
                            }
                        }
                    }

                    // if (conversation.WhatsappUser == null)
                    //    conversation.WhatsappUser = new WhatsAppSender { whatsAppId = conversationMessageViewModel.WhatsappReceiverId, CompanyId = company.Id };
                    if (conversation.WhatsappUser.whatsAppId.Contains("@")
                        && string.IsNullOrEmpty(conversation.WhatsappUser.phone_number))
                    {
                        conversation.WhatsappUser.phone_number =
                            PhoneNumberHelper.GetPhoneNumber(conversation.WhatsappUser.whatsAppId);

                        conversation.WhatsappUser.name = conversation.WhatsappUser.phone_number;
                    }

                    conversationMessage.whatsappReceiver = conversation.WhatsappUser;

                    // Fix for mobile send request
                    if (!string.IsNullOrEmpty(conversationMessage.ChannelIdentityId))
                    {
                        conversationMessageViewModel.ChannelIdentityId = null;
                        conversationMessage.ChannelIdentityId = null;
                    }

                    break;
                case ChannelTypes.WhatsappCloudApi: // Use multiple message method
                    if (string.IsNullOrEmpty(conversationMessageViewModel.ConversationId))
                    {
                        conversation = await _appDbContext.Conversations
                            .Include(x => x.WhatsappCloudApiUser)
                            .OrderByDescending(x => x.UpdatedTime)
                            .FirstOrDefaultAsync(x => x.Id == conversationMessageViewModel.ConversationId);
                    }
                    else
                    {
                        conversation = await _appDbContext.Conversations
                            .Include(x => x.WhatsappCloudApiUser)
                            .FirstOrDefaultAsync(
                                x =>
                                    x.Id == conversationMessageViewModel.ConversationId
                                    && x.CompanyId == company.Id);
                    }

                    conversationMessage.WhatsappCloudApiReceiver = conversation.WhatsappCloudApiUser;

                    break;
                case ChannelTypes.LiveChat:
                    if (!string.IsNullOrEmpty(conversationMessageViewModel.WebClientSenderId))
                    {
                        conversationMessage.WebClientSender = await _appDbContext.SenderWebClientSenders
                            .FirstOrDefaultAsync(
                                x =>
                                    x.WebClientUUID == conversationMessageViewModel.WebClientSenderId
                                    && x.CompanyId == company.Id);

                        conversation.WebClient = conversationMessage.WebClientSender;
                    }
                    else if (!string.IsNullOrEmpty(conversationMessageViewModel.WebClientReceiverId))
                    {
                        if (conversationMessage.Sender == null)
                        {
                            return BadRequest(
                                new ResponseViewModel
                                {
                                    message = "Please login as staff to reply"
                                });
                        }

                        conversationMessage.WebClientReceiver = await _appDbContext.SenderWebClientSenders
                            .FirstOrDefaultAsync(
                                x =>
                                    x.WebClientUUID == conversationMessageViewModel.WebClientReceiverId
                                    && x.CompanyId == company.Id);

                        conversation.WebClient = conversationMessage.WebClientReceiver;
                    }

                    if (conversation.WebClient == null)
                    {
                        return BadRequest();
                    }

                    break;
                case ChannelTypes.Wechat:
                    if (string.IsNullOrEmpty(conversationMessageViewModel.WeChatReceiverOpenId))
                    {
                        return BadRequest();
                    }

                    conversation = await _appDbContext.Conversations
                        .Include(x => x.WeChatUser)
                        .FirstOrDefaultAsync(
                            x =>
                                x.WeChatUser.openid == conversationMessageViewModel.WeChatReceiverOpenId
                                && x.CompanyId == company.Id);

                    if (conversation.WeChatUser == null)
                    {
                        conversation.WeChatUser = new WeChatSender
                        {
                            openid = conversationMessageViewModel.WeChatReceiverOpenId,
                            CompanyId = company.Id
                        };
                    }

                    conversationMessage.WeChatReceiver = conversation.WeChatUser;

                    break;
                case ChannelTypes.Line:
                    conversation = await _appDbContext.Conversations
                        .Include(x => x.LineUser)
                        .FirstOrDefaultAsync(
                            x =>
                                x.LineUser.userId == conversationMessageViewModel.LineReceiverId
                                && x.CompanyId == company.Id);


                    if (conversation.LineUser == null)
                    {
                        conversation.LineUser = new LineSender
                        {
                            userId = conversationMessageViewModel.LineReceiverId,
                            CompanyId = company.Id
                        };
                    }

                    conversationMessage.LineReceiver = conversation.LineUser;

                    break;
                case ChannelTypes.Viber:
                    conversation = await _appDbContext.Conversations
                        .Include(x => x.ViberUser)
                        .FirstOrDefaultAsync(
                            x =>
                                x.CompanyId == company.Id
                                && x.Id == conversationMessageViewModel.ConversationId);

                    if (!conversation.ViberUser.IsSubscribed)
                    {
                        return BadRequest(
                            new ResponseViewModel()
                            {
                                message = "This Viber user is currently subscribed."
                            });
                    }

                    conversationMessage.ViberReceiver = conversation.ViberUser;

                    break;
                case ChannelTypes.Telegram:
                    conversation = await _appDbContext.Conversations
                        .Include(x => x.TelegramUser)
                        .FirstOrDefaultAsync(
                            x =>
                                x.CompanyId == company.Id
                                && x.Id == conversationMessageViewModel.ConversationId);

                    conversationMessage.TelegramReceiver = conversation.TelegramUser;

                    break;
                case ChannelTypes.Sms:
                    if (!await _appDbContext.ConfigSMSConfigs
                            .AnyAsync(x => x.CompanyId == company.Id))
                    {
                        return BadRequest(
                            new ResponseViewModel
                            {
                                message = $"no sms config"
                            });
                    }

                    conversation = await _appDbContext.Conversations
                        .Include(x => x.SMSUser)
                        .FirstOrDefaultAsync(
                            x =>
                                x.SMSUser.SMSId == conversationMessageViewModel.SMSReceiverId
                                && x.CompanyId == company.Id);

                    if (conversation.SMSUser == null)
                    {
                        conversation.SMSUser = new SMSSender
                        {
                            SMSId = conversationMessageViewModel.SMSReceiverId,
                            phone_number = conversationMessageViewModel.SMSReceiverId
                                .Replace("+", string.Empty),
                            name = conversationMessageViewModel.SMSReceiverId
                                .Replace("+", string.Empty),
                            CompanyId = company.Id
                        };
                    }

                    conversationMessage.SMSReceiver = conversation.SMSUser;

                    break;
                case null:
                case "naive":
                case ChannelTypes.Email:
                    conversationMessage.Channel = ChannelTypes.Email;

                    if (string.IsNullOrEmpty(conversationMessageViewModel.ConversationId))
                    {
                        conversation = await _appDbContext.Conversations
                            .Include(x => x.EmailAddress)
                            .FirstOrDefaultAsync(
                                x =>
                                    x.EmailAddress.Email == conversationMessageViewModel.EmailFrom
                                    && x.CompanyId == company.Id);
                    }
                    else
                    {
                        conversation = await _appDbContext.Conversations
                            .Include(x => x.EmailAddress)
                            .FirstOrDefaultAsync(
                                x =>
                                    x.Id == conversationMessageViewModel.ConversationId
                                    && x.CompanyId == company.Id);
                    }

                    if (conversation.EmailAddress == null)
                    {
                        if (string.IsNullOrEmpty(conversationMessageViewModel.EmailFrom))
                        {
                            return BadRequest();
                        }

                        conversation.EmailAddress = new EmailSender
                        {
                            Email = conversationMessageViewModel.EmailFrom,
                            CompanyId = company.Id
                        };
                    }

                    conversationMessage.EmailFrom = conversation.EmailAddress;

                    break;
                case ChannelTypes.Instagram:
                    conversation = _appDbContext.Conversations
                        .Include(x => x.InstagramUser)
                        .FirstOrDefault(
                            x =>
                                x.Id == conversationMessageViewModel.ConversationId
                                && x.CompanyId == company.Id);

                    conversationMessage.InstagramReceiver = conversation.InstagramUser;

                    break;
            }

            try
            {
                List<ConversationMessage> results = new List<ConversationMessage>();
                List<ConversationMessage> textMessageResult = new List<ConversationMessage>();

                if (conversationMessage.Channel == ChannelTypes.Whatsapp360Dialog)
                {
                    var files = conversationMessageViewModel.files;

                    // Fix for mobile send request
                    if (!string.IsNullOrEmpty(conversationMessage.ChannelIdentityId))
                    {
                        conversationMessageViewModel.ChannelIdentityId = null;
                        conversationMessage.ChannelIdentityId = null;
                    }

                    conversationMessageViewModel.files = null;

                    for (var index = 0; index < files.Count; index++)
                    {
                        var file = files[index];

                        // Retrieve the conversation again, and plug that again
                        var whatsapp360ConversationMessageViewModel = conversationMessageViewModel.DeepClone();

                        var whatsapp360ConversationMessage =
                            _mapper.Map<ConversationMessage>(whatsapp360ConversationMessageViewModel);

                        var whatsapp360Conversation =
                            _mapper.Map<Conversation>(whatsapp360ConversationMessageViewModel);

                        whatsapp360ConversationMessage.MessageChecksum = index == 0
                            ? conversationMessageViewModel.MessageChecksum
                            : Guid.NewGuid().ToString();

                        whatsapp360ConversationMessage.MessageContent =
                            index == 0 ? conversationMessageViewModel.MessageContent : null;

                        whatsapp360ConversationMessage.SenderId = conversationMessage.SenderId;

                        if (string.IsNullOrEmpty(whatsapp360ConversationMessageViewModel.ConversationId))
                        {
                            whatsapp360Conversation = await _appDbContext.Conversations
                                .Include(x => x.WhatsApp360DialogUser)
                                .OrderByDescending(x => x.UpdatedTime)
                                .FirstOrDefaultAsync(x => x.Id == whatsapp360ConversationMessageViewModel.ConversationId);
                        }
                        else
                        {
                            whatsapp360Conversation = await _appDbContext.Conversations
                                .Include(x => x.WhatsApp360DialogUser)
                                .FirstOrDefaultAsync(
                                    x =>
                                        x.Id == whatsapp360ConversationMessageViewModel.ConversationId
                                        && x.CompanyId == company.Id);
                        }

                        if (whatsapp360Conversation.WhatsApp360DialogUser == null ||
                            whatsapp360Conversation.WhatsApp360DialogUser.ChannelId !=
                            whatsapp360ConversationMessageViewModel.ChannelId)
                        {
                            whatsapp360ConversationMessage.Whatsapp360DialogReceiver = new WhatsApp360DialogSender()
                            {
                                WhatsAppId = whatsapp360ConversationMessageViewModel.Whatsapp360dialogReceiverId,
                                PhoneNumber = "+" + whatsapp360ConversationMessageViewModel.Whatsapp360dialogReceiverId,
                                CompanyId = company.Id,
                                ChannelId = whatsapp360ConversationMessageViewModel.ChannelId,
                                ChannelWhatsAppPhoneNumber = await _appDbContext.ConfigWhatsApp360DialogConfigs
                                    .Where(
                                        x =>
                                            x.CompanyId == company.Id
                                            && x.Id == whatsapp360ConversationMessageViewModel.ChannelId)
                                    .Select(x => x.WhatsAppPhoneNumber)
                                    .FirstAsync(),
                            };
                        }
                        else
                        {
                            whatsapp360ConversationMessage.Whatsapp360DialogReceiver =
                                whatsapp360Conversation.WhatsApp360DialogUser;
                        }

                        whatsapp360ConversationMessageViewModel.files = new List<IFormFile>
                        {
                            file
                        };
                        var whatsapp360Results = await _conversationMessageService.SendFileMessage(
                            whatsapp360Conversation,
                            whatsapp360ConversationMessage,
                            whatsapp360ConversationMessageViewModel);

                        foreach (var whatsapp360Result in whatsapp360Results)
                        {
                            results.Add(whatsapp360Result);
                        }
                    }
                }
                else if (conversationMessage.Channel == ChannelTypes.WhatsappCloudApi)
                {
                    var files = conversationMessageViewModel.files;
                    conversationMessageViewModel.files = null;

                    for (var index = 0; index < files.Count; index++)
                    {
                        var file = files[index];
                        var whatsappCloudApiConversationMessageViewModel = conversationMessageViewModel.DeepClone();

                        var whatsappCloudApiConversationMessage =
                            _mapper.Map<ConversationMessage>(whatsappCloudApiConversationMessageViewModel);

                        var whatsappCloudApiConversation =
                            _mapper.Map<Conversation>(whatsappCloudApiConversationMessageViewModel);

                        whatsappCloudApiConversationMessage.MessageChecksum = index == 0
                            ? conversationMessageViewModel.MessageChecksum
                            : Guid.NewGuid().ToString();

                        whatsappCloudApiConversationMessage.MessageContent =
                            index == 0 ? conversationMessageViewModel.MessageContent : null;

                        whatsappCloudApiConversationMessage.SenderId = conversationMessage.SenderId;

                        if (string.IsNullOrEmpty(whatsappCloudApiConversationMessageViewModel.ConversationId))
                        {
                            whatsappCloudApiConversation = await _appDbContext.Conversations
                                .Include(x => x.WhatsappCloudApiUser)
                                .OrderByDescending(x => x.UpdatedTime)
                                .FirstOrDefaultAsync(x => x.Id == whatsappCloudApiConversationMessageViewModel.ConversationId);

                        }
                        else
                        {
                            whatsappCloudApiConversation = await _appDbContext.Conversations
                                .Include(x => x.WhatsappCloudApiUser)
                                .FirstOrDefaultAsync(
                                    x =>
                                        x.Id == whatsappCloudApiConversationMessageViewModel.ConversationId
                                        && x.CompanyId == company.Id);
                        }

                        if (whatsappCloudApiConversation.WhatsappCloudApiUser == null)
                        {
                            whatsappCloudApiConversationMessage.WhatsappCloudApiReceiver = new WhatsappCloudApiSender()
                            {
                                WhatsappId = whatsappCloudApiConversationMessageViewModel.WhatsappCloudApiReceiverId,
                                ConversationId = whatsappCloudApiConversationMessageViewModel.ConversationId,
                                UserProfileId = conversationMessage.WhatsappCloudApiReceiver.UserProfileId,

                                // = "+" + whatsappCloudApiConversationMessageViewModel.Whatsapp360dialogReceiverId,
                                // = company.Id,
                                WhatsappChannelPhoneNumber = await _appDbContext.ConfigWhatsappCloudApiConfigs
                                    .Where(x => x.CompanyId == company.Id)
                                    .Select(x => x.WhatsappPhoneNumber)
                                    .FirstAsync(),
                            };
                        }
                        else
                        {
                            whatsappCloudApiConversationMessage.WhatsappCloudApiReceiver =
                                whatsappCloudApiConversation.WhatsappCloudApiUser;
                        }

                        whatsappCloudApiConversationMessageViewModel.files = new List<IFormFile>
                        {
                            file
                        };

                        var whatsappCloudApiResults = await _conversationMessageService.SendFileMessage(
                            whatsappCloudApiConversation,
                            whatsappCloudApiConversationMessage,
                            whatsappCloudApiConversationMessageViewModel);

                        foreach (var whatsappCloudApiResult in whatsappCloudApiResults)
                        {
                            results.Add(whatsappCloudApiResult);
                        }
                    }
                }
                else if (conversationMessage.Channel == ChannelTypes.Facebook)
                {
                    conversationMessage.MessageContent = null;

                    results = (await _conversationMessageService.SendFileMessage(
                        conversation,
                        conversationMessage,
                        conversationMessageViewModel)).ToList();

                    var textConversationMessage = _mapper.Map<ConversationMessage>(conversationMessageViewModel);
                    var textConversation = _mapper.Map<Conversation>(conversationMessageViewModel);

                    try
                    {
                        textConversationMessage.MessageUniqueID = null;
                        textConversationMessage.Id = 0;
                        textConversationMessage.MessageChecksum = Guid.NewGuid().ToString();
                        textConversationMessage.UploadedFiles = new List<UploadedFile>(); // Empty the files?
                        textConversationMessage.MessageType = "text";

                        if (User.Identity.IsAuthenticated)
                        {
                            textConversationMessage.SenderId = _userManager.GetUserId(User);
                            textConversationMessage.Sender = await _userManager.GetUserAsync(User);
                            textConversationMessage.IsSentFromSleekflow = true;

                            var staffInfo = await _appDbContext.UserRoleStaffs
                                .Include(x => x.Company)
                                .FirstOrDefaultAsync(x => x.IdentityId == textConversationMessage.SenderId);

                            if (textConversation.MessageGroupName != staffInfo.Company.SignalRGroupName)
                            {
                                return Unauthorized();
                            }
                        }

                        if (string.IsNullOrEmpty(conversationMessageViewModel.ConversationId))
                        {
                            textConversation = await _appDbContext.Conversations
                                .Include(x => x.facebookUser)
                                .FirstOrDefaultAsync(
                                    x =>
                                        x.facebookUser.FacebookId == conversationMessageViewModel.FacebookReceiverId
                                        && x.CompanyId == company.Id);
                        }
                        else
                        {
                            textConversation = await _appDbContext.Conversations
                                .Include(x => x.facebookUser)
                                .FirstOrDefaultAsync(
                                    x =>
                                        x.Id == conversationMessageViewModel.ConversationId
                                        && x.CompanyId == company.Id);
                        }

                        if (textConversation.facebookUser == null)
                        {
                            textConversation.facebookUser = await _appDbContext.SenderFacebookSenders
                                .FirstOrDefaultAsync(
                                    x =>
                                        x.FacebookId == conversationMessageViewModel.FacebookReceiverId
                                        && x.CompanyId == company.Id);

                            if (textConversation.facebookUser == null)
                            {
                                textConversation.facebookUser = new FacebookSender
                                {
                                    FacebookId = conversationMessageViewModel.FacebookReceiverId,
                                    CompanyId = company.Id
                                };
                            }
                        }

                        textConversationMessage.facebookReceiver = textConversation.facebookUser;
                        textConversationMessage.ConversationId = textConversation.Id;
                        textConversationMessage.Conversation = textConversation;

                        textMessageResult = (await _conversationMessageService.SendMessage(
                            textConversation,
                            textConversationMessage))
                            .ToList();

                        results.AddRange(textMessageResult);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(
                            ex,
                            "[{MethodName} endpoint] Send message to Facebook error: {ExceptionMessage}",
                            nameof(SendFileMessage),
                            ex.Message);
                    }
                }
                else if (conversationMessage.Channel == ChannelTypes.Instagram)
                {
                    conversationMessage.MessageContent = null;

                    results = (await _conversationMessageService.SendFileMessage(
                        conversation,
                        conversationMessage,
                        conversationMessageViewModel)).ToList();

                    var textConversationMessage = _mapper.Map<ConversationMessage>(conversationMessageViewModel);
                    var textConversation = new Conversation();

                    try
                    {
                        textConversationMessage.MessageUniqueID = null;
                        textConversationMessage.Id = 0;
                        textConversationMessage.MessageChecksum = Guid.NewGuid().ToString();
                        textConversationMessage.UploadedFiles = new List<UploadedFile>();
                        textConversationMessage.MessageType = "text";

                        textConversation = await _appDbContext.Conversations
                            .Include(x => x.InstagramUser)
                            .FirstOrDefaultAsync(
                                x =>
                                    x.Id == conversationMessageViewModel.ConversationId
                                    && x.CompanyId == company.Id);

                        textConversationMessage.InstagramReceiver = textConversation.InstagramUser;
                        textConversationMessage.ConversationId = textConversation.Id;
                        textConversationMessage.Conversation = textConversation;

                        var testMessageResult = (await _conversationMessageService.SendMessage(
                            textConversation,
                            textConversationMessage))
                            .AsEnumerable();

                        results.AddRange(testMessageResult);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(
                            ex,
                            "[{MethodName} endpoint] Send message to Instagram error: {ExceptionMessage}",
                            nameof(SendFileMessage),
                            ex.Message);
                    }
                }
                else if (conversationMessage.Channel == ChannelTypes.Wechat)
                {
                    // send file first
                    conversationMessage.MessageContent = null;

                    results = (await _conversationMessageService.SendFileMessage(
                        conversation,
                        conversationMessage,
                        conversationMessageViewModel)).ToList();

                    // send text if any
                    var textConversationMessage = _mapper.Map<ConversationMessage>(conversationMessageViewModel);
                    var textConversation = _mapper.Map<Conversation>(conversationMessageViewModel);

                    if (textConversationMessage.MessageContent != null)
                    {
                        try
                        {
                            textConversationMessage.MessageUniqueID = null;
                            textConversationMessage.Id = 0;
                            textConversationMessage.MessageChecksum = Guid.NewGuid().ToString();
                            textConversationMessage.UploadedFiles = new List<UploadedFile>();
                            textConversationMessage.MessageType = "text";

                            if (User.Identity.IsAuthenticated)
                            {
                                textConversationMessage.SenderId = _userManager.GetUserId(User);
                                textConversationMessage.Sender = await _userManager.GetUserAsync(User);
                                textConversationMessage.IsSentFromSleekflow = true;

                                var staffInfo = await _appDbContext.UserRoleStaffs
                                    .Include(x => x.Company)
                                    .FirstOrDefaultAsync(x => x.IdentityId == textConversationMessage.SenderId);

                                if (textConversation.MessageGroupName != staffInfo.Company.SignalRGroupName)
                                {
                                    return Unauthorized();
                                }
                            }

                            textConversation = string.IsNullOrEmpty(conversationMessageViewModel.ConversationId) ?
                                await _appDbContext.Conversations
                                    .Include(x => x.WeChatUser)
                                    .FirstOrDefaultAsync(
                                        x =>
                                            x.WeChatUser.openid == conversationMessageViewModel.WeChatReceiverOpenId
                                            && x.CompanyId == company.Id) :
                                await _appDbContext.Conversations
                                    .Include(x => x.WeChatUser)
                                    .FirstOrDefaultAsync(
                                        x =>
                                            x.Id == conversationMessageViewModel.ConversationId
                                            && x.CompanyId == company.Id);

                            if (textConversation.WeChatUser == null)
                            {
                                textConversation.WeChatUser = await _appDbContext.SenderWeChatSenders
                                    .FirstOrDefaultAsync(
                                        x =>
                                            x.openid == conversationMessageViewModel.WeChatReceiverOpenId
                                            && x.CompanyId == company.Id);

                                if (textConversation.WeChatUser == null)
                                {
                                    textConversation.WeChatUser = new WeChatSender
                                    {
                                        openid = conversationMessageViewModel.WeChatReceiverOpenId,
                                        CompanyId = company.Id
                                    };
                                }
                            }

                            textConversationMessage.WeChatReceiver = textConversation.WeChatUser;
                            textConversationMessage.ConversationId = textConversation.Id;
                            textConversationMessage.Conversation = textConversation;

                            textMessageResult = (await _conversationMessageService.SendMessage(
                                textConversation,
                                textConversationMessage))
                                .ToList();

                            results.AddRange(textMessageResult);
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(
                                ex,
                                "[{MethodName} endpoint] Send message to WeChat error: {ExceptionMessage}",
                                nameof(SendFileMessage),
                                ex.Message);
                        }
                    }
                }
                else
                {
                    results = (await _conversationMessageService.SendFileMessage(
                        conversation,
                        conversationMessage,
                        conversationMessageViewModel))
                        .ToList();
                }

                var conversationMessagesVM = _mapper.Map<List<ConversationMessageResponseViewModel>>(results);

                return Ok(conversationMessagesVM);
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[{MethodName} endpoint] error: {ExceptionMessage}",
                    nameof(SendFileMessage),
                    ex.Message);

                return BadRequest(ex.Message);
            }
        }

        [HttpGet]
        [AllowAnonymous]
        [Route("Message/File/private/{filenameId}")]
        [Route("Message/File/private/{filenameId}/{downloadFilename}")]
        public async Task<IActionResult> GetPrivateAzureBlob(
            string filenameId,
            string downloadFilename,
            [FromQuery]
            string mode = null)
        {
            try
            {
                var file = await _appDbContext.ConversationMessageUploadedFiles
                    .FirstOrDefaultAsync(x => x.FileId == filenameId);

                if (file == null)
                {
                    return NotFound();
                }

                if (mode is "redirect")
                {
                    var url = new Uri(_azureBlobStorageService.GetAzureBlobSasUri(
                        file.Filename,
                        file.BlobContainer));

                    return Redirect(url.AbsoluteUri);
                }

                var stream = await _azureBlobStorageService.DownloadFromAzureBlob(
                    file.Filename,
                    file.BlobContainer);

                var extension = Path.GetExtension(file.Filename);

                if (string.IsNullOrEmpty(extension))
                {
                    switch (file.MIMEType)
                    {
                        case "video/mp4":
                            extension = "mp4";

                            break;
                        case "image/jpeg":
                            extension = "jpg";

                            break;
                        case "image/png":
                            extension = "png";

                            break;
                    }
                }

                var filename = Path.GetFileNameWithoutExtension(file.Filename);
                var fileType = string.IsNullOrEmpty(file.MIMEType) ? "application/octet-stream" : file.MIMEType;

                var fileDownloadName =
                    $"{filename}{((string.IsNullOrEmpty(extension) && file.MIMEType == "video/mp4") ? ".mp4" : $".{extension}")}";

                // var data = stream.ToArray();
                // string encodeFilename = HttpUtility.UrlEncode(fileDownloadName, Encoding.UTF8);
                // Response.Headers.Add($"Content-Disposition", $"attachment; filename={Uri.EscapeUriString(Path.GetFileName(file.Filename))}; filename*=UTF-8\"{Uri.EscapeUriString(fileDownloadName)}");
                // return new FileStreamResult(new MemoryStream(data), fileType);
                fileDownloadName = fileDownloadName
                    .Replace(" ", string.Empty)
                    .Replace("(", string.Empty)
                    .Replace(")", string.Empty)
                    .Replace("[", string.Empty)
                    .Replace("]", string.Empty);

                var res = File(
                    stream.ToArray(),
                    $"{fileType}",
                    fileDownloadName);

                res.EnableRangeProcessing = true;

                return res;
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[{MethodName} endpoint] error for fileNameId {FileNameId}: {ExceptionMessage}",
                    nameof(GetPrivateAzureBlob),
                    filenameId,
                    ex.Message);

                return BadRequest(ex.Message);
            }
        }

        // [HttpGet]
        // [AllowAnonymous]
        // [Route("Message/File/{filenameId}")]
        // public async Task<IActionResult> GetAzureBlob(string filenameId)
        // {
        //    try
        //    {
        //        var file = _appDbContext.ConversationMessageUploadedFiles.Where(x => x.FileId == filenameId).FirstOrDefault();
        //        var stream = await _azureBlobStorageService.DownloadFromAzureBlob(file.Filename, file.BlobContainer);
        //        var extension = Path.GetExtension(file.Filename);
        //        if (string.IsNullOrEmpty(extension))
        //        {
        //            switch (file.MIMEType)
        //            {
        //                case "video/mp4":
        //                    extension = "mp4";
        //                    break;
        //                case "image/jpeg":
        //                    extension = "jpg";
        //                    break;
        //                case "image/png":
        //                    extension = "png";
        //                    break;
        //            }
        //        }
        //        var filename = Path.GetFileNameWithoutExtension(file.Filename);
        //        var res = File(stream.ToArray(), (string.IsNullOrEmpty(file.MIMEType)) ? "application/octet-stream" : file.MIMEType, $"{filename}{((string.IsNullOrEmpty(extension) && file.MIMEType == "video/mp4") ? ".mp4" : $".{extension}")}");
        //        res.EnableRangeProcessing = true;
        //        return res;
        //    }
        //    catch (Exception ex)
        //    {
        //        return BadRequest(ex.Message);
        //    }
        // }
    }
}