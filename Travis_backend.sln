﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.6.33815.320
MinimumVisualStudioVersion = 10.0.40219.1
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Travis_backend", "Travis_backend\Travis_backend.csproj", "{1872E09E-F9E6-4598-8E4A-96B983A4DD3D}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Sleekflow.Core.Tests", "Sleekflow.Core.Tests\Sleekflow.Core.Tests.csproj", "{4203F9D4-4746-4312-AD0B-CD6393FA04A5}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Travis_backend.Auth0", "Travis_backend.Auth0\Travis_backend.Auth0.csproj", "{A40D906E-CE62-4E6A-9C17-6528CD579701}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Sleekflow.Powerflow.Apis", "Sleekflow.Powerflow.Apis\Sleekflow.Powerflow.Apis.csproj", "{B28BE5E6-8D1B-450A-B2E0-FE39611B05CB}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Sleekflow.Core.DataMigrator", "Sleekflow.Core.DataMigrator\Sleekflow.Core.DataMigrator.csproj", "{1634E150-1389-4922-9014-FF0921BC27A0}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Sleekflow.Core.Infra", "Sleekflow.Core.Infra\Sleekflow.Core.Infra.csproj", "{0D247922-C315-4E6E-92EB-5394DCEAEF75}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Sleekflow.SleekPay", "Sleekflow.SleekPay\Sleekflow.SleekPay.csproj", "{C8B24F71-3506-4A4E-A3DD-C53A9B5CFCB3}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Sleekflow.Core.Infra.Perf", "Sleekflow.Core.Infra.Perf\Sleekflow.Core.Infra.Perf.csproj", "{9787DF18-027C-4D77-AA49-2BED7BCBED04}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Sleekflow.Core.Benchmarks", "Sleekflow.Core.Benchmarks\Sleekflow.Core.Benchmarks.csproj", "{C991A770-317A-4A73-8DAE-C472B3FBC8E5}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Sleekflow.Core.Exporter", "Sleekflow.Core.Exporter\Sleekflow.Core.Exporter.csproj", "{BB8A7D32-3CC8-4F1B-A317-6639209C9B2F}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Sleekflow.SleekPay.Tests", "Sleekflow.SleekPay.Tests\Sleekflow.SleekPay.Tests.csproj", "{50481E49-0855-482D-B785-1AFA44498EBF}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		OBSOLETE_AUTH|Any CPU = OBSOLETE_AUTH|Any CPU
		QA|Any CPU = QA|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{1872E09E-F9E6-4598-8E4A-96B983A4DD3D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{1872E09E-F9E6-4598-8E4A-96B983A4DD3D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{1872E09E-F9E6-4598-8E4A-96B983A4DD3D}.OBSOLETE_AUTH|Any CPU.ActiveCfg = OBSOLETE_AUTH|Any CPU
		{1872E09E-F9E6-4598-8E4A-96B983A4DD3D}.OBSOLETE_AUTH|Any CPU.Build.0 = OBSOLETE_AUTH|Any CPU
		{1872E09E-F9E6-4598-8E4A-96B983A4DD3D}.QA|Any CPU.ActiveCfg = QA|Any CPU
		{1872E09E-F9E6-4598-8E4A-96B983A4DD3D}.QA|Any CPU.Build.0 = QA|Any CPU
		{1872E09E-F9E6-4598-8E4A-96B983A4DD3D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{1872E09E-F9E6-4598-8E4A-96B983A4DD3D}.Release|Any CPU.Build.0 = Release|Any CPU
		{4203F9D4-4746-4312-AD0B-CD6393FA04A5}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{4203F9D4-4746-4312-AD0B-CD6393FA04A5}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{4203F9D4-4746-4312-AD0B-CD6393FA04A5}.OBSOLETE_AUTH|Any CPU.ActiveCfg = OBSOLETE_AUTH|Any CPU
		{4203F9D4-4746-4312-AD0B-CD6393FA04A5}.OBSOLETE_AUTH|Any CPU.Build.0 = OBSOLETE_AUTH|Any CPU
		{4203F9D4-4746-4312-AD0B-CD6393FA04A5}.QA|Any CPU.ActiveCfg = QA|Any CPU
		{4203F9D4-4746-4312-AD0B-CD6393FA04A5}.QA|Any CPU.Build.0 = QA|Any CPU
		{4203F9D4-4746-4312-AD0B-CD6393FA04A5}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{4203F9D4-4746-4312-AD0B-CD6393FA04A5}.Release|Any CPU.Build.0 = Release|Any CPU
		{A40D906E-CE62-4E6A-9C17-6528CD579701}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A40D906E-CE62-4E6A-9C17-6528CD579701}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A40D906E-CE62-4E6A-9C17-6528CD579701}.OBSOLETE_AUTH|Any CPU.ActiveCfg = OBSOLETE_AUTH|Any CPU
		{A40D906E-CE62-4E6A-9C17-6528CD579701}.OBSOLETE_AUTH|Any CPU.Build.0 = OBSOLETE_AUTH|Any CPU
		{A40D906E-CE62-4E6A-9C17-6528CD579701}.QA|Any CPU.ActiveCfg = QA|Any CPU
		{A40D906E-CE62-4E6A-9C17-6528CD579701}.QA|Any CPU.Build.0 = QA|Any CPU
		{A40D906E-CE62-4E6A-9C17-6528CD579701}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A40D906E-CE62-4E6A-9C17-6528CD579701}.Release|Any CPU.Build.0 = Release|Any CPU
		{B28BE5E6-8D1B-450A-B2E0-FE39611B05CB}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B28BE5E6-8D1B-450A-B2E0-FE39611B05CB}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B28BE5E6-8D1B-450A-B2E0-FE39611B05CB}.OBSOLETE_AUTH|Any CPU.ActiveCfg = Debug|Any CPU
		{B28BE5E6-8D1B-450A-B2E0-FE39611B05CB}.OBSOLETE_AUTH|Any CPU.Build.0 = Debug|Any CPU
		{B28BE5E6-8D1B-450A-B2E0-FE39611B05CB}.QA|Any CPU.ActiveCfg = Debug|Any CPU
		{B28BE5E6-8D1B-450A-B2E0-FE39611B05CB}.QA|Any CPU.Build.0 = Debug|Any CPU
		{B28BE5E6-8D1B-450A-B2E0-FE39611B05CB}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B28BE5E6-8D1B-450A-B2E0-FE39611B05CB}.Release|Any CPU.Build.0 = Release|Any CPU
		{1634E150-1389-4922-9014-FF0921BC27A0}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{1634E150-1389-4922-9014-FF0921BC27A0}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{1634E150-1389-4922-9014-FF0921BC27A0}.OBSOLETE_AUTH|Any CPU.ActiveCfg = Debug|Any CPU
		{1634E150-1389-4922-9014-FF0921BC27A0}.OBSOLETE_AUTH|Any CPU.Build.0 = Debug|Any CPU
		{1634E150-1389-4922-9014-FF0921BC27A0}.QA|Any CPU.ActiveCfg = Debug|Any CPU
		{1634E150-1389-4922-9014-FF0921BC27A0}.QA|Any CPU.Build.0 = Debug|Any CPU
		{1634E150-1389-4922-9014-FF0921BC27A0}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{1634E150-1389-4922-9014-FF0921BC27A0}.Release|Any CPU.Build.0 = Release|Any CPU
		{0D247922-C315-4E6E-92EB-5394DCEAEF75}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{0D247922-C315-4E6E-92EB-5394DCEAEF75}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{0D247922-C315-4E6E-92EB-5394DCEAEF75}.OBSOLETE_AUTH|Any CPU.ActiveCfg = Debug|Any CPU
		{0D247922-C315-4E6E-92EB-5394DCEAEF75}.OBSOLETE_AUTH|Any CPU.Build.0 = Debug|Any CPU
		{0D247922-C315-4E6E-92EB-5394DCEAEF75}.QA|Any CPU.ActiveCfg = Debug|Any CPU
		{0D247922-C315-4E6E-92EB-5394DCEAEF75}.QA|Any CPU.Build.0 = Debug|Any CPU
		{0D247922-C315-4E6E-92EB-5394DCEAEF75}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{0D247922-C315-4E6E-92EB-5394DCEAEF75}.Release|Any CPU.Build.0 = Release|Any CPU
		{C8B24F71-3506-4A4E-A3DD-C53A9B5CFCB3}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C8B24F71-3506-4A4E-A3DD-C53A9B5CFCB3}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C8B24F71-3506-4A4E-A3DD-C53A9B5CFCB3}.OBSOLETE_AUTH|Any CPU.ActiveCfg = Debug|Any CPU
		{C8B24F71-3506-4A4E-A3DD-C53A9B5CFCB3}.OBSOLETE_AUTH|Any CPU.Build.0 = Debug|Any CPU
		{C8B24F71-3506-4A4E-A3DD-C53A9B5CFCB3}.QA|Any CPU.ActiveCfg = Debug|Any CPU
		{C8B24F71-3506-4A4E-A3DD-C53A9B5CFCB3}.QA|Any CPU.Build.0 = Debug|Any CPU
		{C8B24F71-3506-4A4E-A3DD-C53A9B5CFCB3}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C8B24F71-3506-4A4E-A3DD-C53A9B5CFCB3}.Release|Any CPU.Build.0 = Release|Any CPU
		{9787DF18-027C-4D77-AA49-2BED7BCBED04}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{9787DF18-027C-4D77-AA49-2BED7BCBED04}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{9787DF18-027C-4D77-AA49-2BED7BCBED04}.OBSOLETE_AUTH|Any CPU.ActiveCfg = Debug|Any CPU
		{9787DF18-027C-4D77-AA49-2BED7BCBED04}.OBSOLETE_AUTH|Any CPU.Build.0 = Debug|Any CPU
		{9787DF18-027C-4D77-AA49-2BED7BCBED04}.QA|Any CPU.ActiveCfg = Debug|Any CPU
		{9787DF18-027C-4D77-AA49-2BED7BCBED04}.QA|Any CPU.Build.0 = Debug|Any CPU
		{9787DF18-027C-4D77-AA49-2BED7BCBED04}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{9787DF18-027C-4D77-AA49-2BED7BCBED04}.Release|Any CPU.Build.0 = Release|Any CPU
		{C991A770-317A-4A73-8DAE-C472B3FBC8E5}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C991A770-317A-4A73-8DAE-C472B3FBC8E5}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C991A770-317A-4A73-8DAE-C472B3FBC8E5}.OBSOLETE_AUTH|Any CPU.ActiveCfg = Debug|Any CPU
		{C991A770-317A-4A73-8DAE-C472B3FBC8E5}.OBSOLETE_AUTH|Any CPU.Build.0 = Debug|Any CPU
		{C991A770-317A-4A73-8DAE-C472B3FBC8E5}.QA|Any CPU.ActiveCfg = Debug|Any CPU
		{C991A770-317A-4A73-8DAE-C472B3FBC8E5}.QA|Any CPU.Build.0 = Debug|Any CPU
		{C991A770-317A-4A73-8DAE-C472B3FBC8E5}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C991A770-317A-4A73-8DAE-C472B3FBC8E5}.Release|Any CPU.Build.0 = Release|Any CPU
		{BB8A7D32-3CC8-4F1B-A317-6639209C9B2F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{BB8A7D32-3CC8-4F1B-A317-6639209C9B2F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{BB8A7D32-3CC8-4F1B-A317-6639209C9B2F}.OBSOLETE_AUTH|Any CPU.ActiveCfg = Debug|Any CPU
		{BB8A7D32-3CC8-4F1B-A317-6639209C9B2F}.OBSOLETE_AUTH|Any CPU.Build.0 = Debug|Any CPU
		{BB8A7D32-3CC8-4F1B-A317-6639209C9B2F}.QA|Any CPU.ActiveCfg = Debug|Any CPU
		{BB8A7D32-3CC8-4F1B-A317-6639209C9B2F}.QA|Any CPU.Build.0 = Debug|Any CPU
		{BB8A7D32-3CC8-4F1B-A317-6639209C9B2F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{BB8A7D32-3CC8-4F1B-A317-6639209C9B2F}.Release|Any CPU.Build.0 = Release|Any CPU
		{50481E49-0855-482D-B785-1AFA44498EBF}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{50481E49-0855-482D-B785-1AFA44498EBF}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{50481E49-0855-482D-B785-1AFA44498EBF}.OBSOLETE_AUTH|Any CPU.ActiveCfg = Debug|Any CPU
		{50481E49-0855-482D-B785-1AFA44498EBF}.OBSOLETE_AUTH|Any CPU.Build.0 = Debug|Any CPU
		{50481E49-0855-482D-B785-1AFA44498EBF}.QA|Any CPU.ActiveCfg = Debug|Any CPU
		{50481E49-0855-482D-B785-1AFA44498EBF}.QA|Any CPU.Build.0 = Debug|Any CPU
		{50481E49-0855-482D-B785-1AFA44498EBF}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{50481E49-0855-482D-B785-1AFA44498EBF}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {A28AC922-7FF1-4566-93F6-2BA1C2C2EAF4}
	EndGlobalSection
EndGlobal
