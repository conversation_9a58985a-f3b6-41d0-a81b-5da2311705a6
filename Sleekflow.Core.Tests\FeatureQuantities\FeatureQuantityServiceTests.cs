using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Moq;
using Travis_backend.Database;
using Travis_backend.SubscriptionPlanDomain.Repositories;
using Travis_backend.SubscriptionPlanDomain.Services;

namespace Sleekflow.Core.Tests.FeatureQuantities;

[TestFixture]
public class PlanDefinitionServiceTests
{
    private IPlanDefinitionService _planDefinitionService;
    private IPlanDefinitionRepository _planDefinitionRepository;
    private ApplicationDbContext _applicationDbContext;
    private IFeatureQuantityService _featureQuantityService;

    [SetUp]
    public void Setup()
    {
        var connectionString =
            "Server=tcp:sleekflow-core-sql-server-eas-productiond2a4d949.database.windows.net,1433;Initial Catalog=sleekflow-crm-prod;Persist Security Info=False;User ID=sleekflow_read_only;Password=7*4#5Ctg3Nwrestadesw7M54$NfYtt;MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;Pooling=true;Max Pool Size=500;Min Pool Size=100;";
            // "Server=tcp:sleekflow.database.windows.net,1433;Initial Catalog=sleekflow-crm-prod;Persist Security Info=False;User ID=sleekflowadmin;Password=*****************;MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=120;";
        var options = new DbContextOptionsBuilder<ApplicationDbContext>()
            .UseSqlServer(connectionString)
            .Options;
        _applicationDbContext = new ApplicationDbContext(options);

        var logger = new Mock<ILogger<FeatureQuantityService>>();

        _planDefinitionRepository = new PlanDefinitionRepository();
        _planDefinitionService = new PlanDefinitionService(_planDefinitionRepository, _applicationDbContext);
        _featureQuantityService = new FeatureQuantityService(_applicationDbContext, _planDefinitionService, logger.Object);
    }

    // Test case is disabled due to feature plan is updated
    // Team: Team GenR
    // [Test]
    // public async Task GetFeatureQuantityAsync_Returns_CorrectFeatureQuantityFromSubscription()
    // {
    //     var companyId = "00129338-b70d-471b-a3f4-efe7e7024ab5";
    //
    //     var contactsFeatureId = "contacts";
    //     var contactsExpectedQuantity = 2000;
    //
    //     var messagesFeatureId = "messages";
    //     var messagesExpectedQuantity = 5000;
    //
    //     var agentsFeatureId = "agents";
    //     var agentsExpectedQuantity = 3;
    //
    //     var campaignsFeatureId = "campaigns";
    //     var campaignsExpectedQuantity = 5000;
    //
    //     var automationsFeatureId = "automations";
    //     var automationsExpectedQuantity = 10;
    //
    //     var channelsFeatureId = "channels";
    //     var channelsExpectedQuantity = 5;
    //
    //     var apiCallsFeatureId = "api_calls";
    //     var apiCallsExpectedQuantity = 5000;
    //
    //     var contactsResult = await _featureQuantityService.GetSubscriptionFeatureQuantityAsync(companyId, contactsFeatureId);
    //     var messagesResult = await _featureQuantityService.GetSubscriptionFeatureQuantityAsync(companyId, messagesFeatureId);
    //     var agentsResult = await _featureQuantityService.GetSubscriptionFeatureQuantityAsync(companyId, agentsFeatureId);
    //     var campaignsResult = await _featureQuantityService.GetSubscriptionFeatureQuantityAsync(companyId, campaignsFeatureId);
    //     var automationsResult = await _featureQuantityService.GetSubscriptionFeatureQuantityAsync(companyId, automationsFeatureId);
    //     var channelsResult = await _featureQuantityService.GetSubscriptionFeatureQuantityAsync(companyId, channelsFeatureId);
    //     var apiCallsResult = await _featureQuantityService.GetSubscriptionFeatureQuantityAsync(companyId, apiCallsFeatureId);
    //
    //     Assert.Multiple(() =>
    //     {
    //         Assert.That(contactsResult, Is.EqualTo(contactsExpectedQuantity));
    //         Assert.That(messagesResult, Is.EqualTo(messagesExpectedQuantity));
    //         Assert.That(agentsResult, Is.EqualTo(agentsExpectedQuantity));
    //         Assert.That(campaignsResult, Is.EqualTo(campaignsExpectedQuantity));
    //         Assert.That(automationsResult, Is.EqualTo(automationsExpectedQuantity));
    //         Assert.That(channelsResult, Is.EqualTo(channelsExpectedQuantity));
    //         Assert.That(apiCallsResult, Is.EqualTo(apiCallsExpectedQuantity));
    //     });
    // }

    [Test]
    public async Task GetFeatureQuantityAsync_Returns_ZeroWhenNoMatchingSubscription()
    {
        var companyId = "00129338-b70d-471b-a3f4-efe7e7024ab5";

        var invalidFeatureId = "invalid";
        var invalidExpectedQuantity = 0;

        var invalidFeatureResult = await _featureQuantityService.GetSubscriptionFeatureQuantityAsync(companyId, invalidFeatureId);

        Assert.That(invalidFeatureResult, Is.EqualTo(invalidExpectedQuantity));
    }

    // Test case is disabled due to feature plan is updated
    // Team: Team GenR
    // [Test]
    // public async Task GetFeatureQuantityAsync_Returns_CorrectFeatureQuantityFromAddOns()
    // {
    //     var companyId = "0c77e099-e15b-4445-bb24-7933448ac4d2";
    //
    //     var contactsFeatureId = "contacts";
    //     var contactsExpectedQuantity = 1000000;
    //
    //     var contactsFeatureResult = await _featureQuantityService.GetAddOnsFeatureQuantityAsync(companyId, contactsFeatureId);
    //
    //     Assert.That(contactsFeatureResult, Is.EqualTo(contactsExpectedQuantity));
    // }

    // [Test]
    // public async Task GetFeatureQuantityAsync_Returns_CorrectFeatureQuantity()
    // {
    //     var companyId = "1377c401-3f2a-4f93-9434-78bb4deb60d7";
    //
    //     var contactsFeatureId = "contacts";
    //     var contactsExpectedQuantity = 5000;
    //
    //     var messagesFeatureId = "messages";
    //     var messagesExpectedQuantity = 5000;
    //
    //     var agentsFeatureId = "agents";
    //     var agentsExpectedQuantity = 10;
    //
    //     var campaignsFeatureId = "campaigns";
    //     var campaignsExpectedQuantity = 5000;
    //
    //     var automationsFeatureId = "automations";
    //     var automationsExpectedQuantity = 5;
    //
    //     var channelsFeatureId = "channels";
    //     var channelsExpectedQuantity = 1000000;
    //
    //     var apiCallsFeatureId = "api_calls";
    //     var apiCallsExpectedQuantity = 5000;
    //
    //     var contactsResult = await _featureQuantityService.GetFeatureQuantityAsync(companyId, contactsFeatureId);
    //     var messagesResult = await _featureQuantityService.GetFeatureQuantityAsync(companyId, messagesFeatureId);
    //     var agentsResult = await _featureQuantityService.GetFeatureQuantityAsync(companyId, agentsFeatureId);
    //     var campaignsResult = await _featureQuantityService.GetFeatureQuantityAsync(companyId, campaignsFeatureId);
    //     var automationsResult = await _featureQuantityService.GetFeatureQuantityAsync(companyId, automationsFeatureId);
    //     var channelsResult = await _featureQuantityService.GetFeatureQuantityAsync(companyId, channelsFeatureId);
    //     var apiCallsResult = await _featureQuantityService.GetFeatureQuantityAsync(companyId, apiCallsFeatureId);
    //
    //     Assert.Multiple(() =>
    //     {
    //         Assert.That(contactsResult, Is.EqualTo(contactsExpectedQuantity));
    //         Assert.That(messagesResult, Is.EqualTo(messagesExpectedQuantity));
    //         Assert.That(agentsResult, Is.EqualTo(agentsExpectedQuantity));
    //         Assert.That(campaignsResult, Is.EqualTo(campaignsExpectedQuantity));
    //         Assert.That(automationsResult, Is.EqualTo(automationsExpectedQuantity));
    //         Assert.That(channelsResult, Is.EqualTo(channelsExpectedQuantity));
    //         Assert.That(apiCallsResult, Is.EqualTo(apiCallsExpectedQuantity));
    //     });
    // }
}