﻿using System;
using System.Linq.Expressions;
using System.Threading;
using System.Threading.Tasks;
using Hangfire;
using Hangfire.Pro.Redis;

namespace Travis_backend.Cache;

public interface IBackgroundJobQueueService
{
    string EnqueueRandomStorage<T>(Expression<Func<T, Task>> methodCall);

    string EnqueueRandomStorage<T>(Expression<Action<T>> methodCall);

    string EnqueueRandomStorage(Expression<Func<Task>> methodCall);

    string EnqueueRandomStorage<T>(string queue, Expression<Func<T, Task>> methodCall);

    string EnqueueRandomStorage(string queue, Expression<Func<Task>> methodCall);
}

public class BackgroundJobQueueService : IBackgroundJobQueueService
{
    private int _counter = 0;

    public string EnqueueRandomStorage<T>(Expression<Func<T, Task>> methodCall)
    {
        var storage = GetNextStorage();
        return new BackgroundJobClient(storage).Enqueue(methodCall);
    }

    public string EnqueueRandomStorage<T>(Expression<Action<T>> methodCall)
    {
        var storage = GetNextStorage();
        return new BackgroundJobClient(storage).Enqueue(methodCall);
    }

    public string EnqueueRandomStorage<T>(string queue, Expression<Func<T, Task>> methodCall)
    {
        var storage = GetNextStorage();
        return new BackgroundJobClient(storage).Enqueue(queue, methodCall);
    }

    public string EnqueueRandomStorage(Expression<Func<Task>> methodCall)
    {
        var storage = GetNextStorage();
        return new BackgroundJobClient(storage).Enqueue(methodCall);
    }

    public string EnqueueRandomStorage(string queue, Expression<Func<Task>> methodCall)
    {
        var storage = GetNextStorage();
        return new BackgroundJobClient(storage).Enqueue(queue, methodCall);
    }

    private RedisStorage GetNextStorage()
    {
        var storages = BackgroundJobQueueInitializer.GetHangfireStorages();

        return storages[Interlocked.Increment(ref _counter) % storages.Count];
    }
}