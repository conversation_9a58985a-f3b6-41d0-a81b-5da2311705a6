using System;
using System.Collections.Generic;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Sleekflow.Apis.CrmHub.Model;
using Sleekflow.Apis.MessagingHub.Model;
using Travis_backend.AutomationDomain.Models;
using Travis_backend.BroadcastDomain.ViewModels;
using Travis_backend.ChannelDomain.Models;
using Travis_backend.ChannelDomain.ViewModels;
using Travis_backend.ChannelDomain.ViewModels.Interfaces;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.CompanyDomain.ViewModels;
using Travis_backend.Enums;
using Travis_backend.ConversationServices.Models;
using Travis_backend.MessageDomain.Models;
using Travis_backend.MessageDomain.ViewModels;
using Travis_backend.Models.ChatChannelConfig;
using Travis_backend.IntegrationServices.Models;
using Travis_backend.ShoplineIntegrationDomain.Models;
using Travis_backend.SubscriptionPlanDomain.Models;
using Travis_backend.ContactDomain.ViewModels;

namespace Travis_backend.ConversationDomain.ViewModels
{
    public class ConversationWithCountViewModel
    {
        public List<ConversationNoCompanyResponseViewModel> Data { get; set; } = new ();

        public int Count { get; set; }
    }

    public class ConversationNoCompanyResponseViewModel
    {
        public string ConversationId { get; set; }

        public string CompanyId { get; set; }

        public List<string> ConversationChannels { get; set; } = new List<string>();

        public string MessageGroupName { get; set; }

        public UserProfileNoCompanyResponse UserProfile { get; set; }

        public string Status { get; set; }

        public StaffWithoutCompanyResponse Assignee { get; set; }

        public List<AdditionalAssigneeResponse> AdditionalAssignees { get; set; }

        public List<ConversationHashtagResponse> ConversationHashtags { get; set; }

        public List<ConversationMessageResponseViewModel> LastMessage { get; set; }

        public List<ConversationMessageResponseViewModel> Messages { get; set; }

        public DateTime UpdatedTime { get; set; }

        public DateTime ModifiedAt { get; set; }

        public int UnreadMessageCount { get; set; }

        public DateTime? SnoozeUntil { get; set; }

        public long? FirstMessageId { get; set; }

        public long? LastMessageId { get; set; }

        public string LastMessageChannel { get; set; }

        public string LastChannelIdentityId { get; set; }

        public CompanyTeamResponse AssignedTeam { get; set; }

        public bool IsSandbox { get; set; }

        public bool IsBookmarked { get; set; }

        public ConversationMetadataResponseViewModel Metadata { get; set; }

        public int TicketCount { get; set; }
    }

    public class ConversationResponseViewModel
    {
        public string ConversationId { get; set; }

        public string CompanyId { get; set; }

        public CompanyResponse Company { get; set; }

        public List<string> ConversationChannels { get; set; } = new List<string>();

        public string MessageGroupName { get; set; }

        public UserProfileNoCompanyResponse UserProfile { get; set; }

        public string Status { get; set; }

        public StaffWithoutCompanyResponse Assignee { get; set; }

        public List<AdditionalAssigneeResponse> AdditionalAssignees { get; set; }

        public List<ConversationHashtagResponse> ConversationHashtags { get; set; }

        public List<ConversationMessageResponseViewModel> LastMessage { get; set; }

        public List<ConversationMessageResponseViewModel> Messages { get; set; }

        public DateTime UpdatedTime { get; set; }

        public DateTime ModifiedAt { get; set; }

        public int UnreadMessageCount { get; set; }

        public DateTime? SnoozeUntil { get; set; }

        public long? FirstMessageId { get; set; }

        public string LastMessageChannel { get; set; }

        public string LastChannelIdentityId { get; set; }

        public CompanyTeamResponse AssignedTeam { get; set; }

        public bool IsSandbox { get; set; }

        public bool IsBookmarked { get; set; }
    }

    public class ConversationStatusResponseViewModel
    {
        public string ConversationId { get; set; }

        public string CompanyId { get; set; }

        public string Status { get; set; }

        public bool IsBookmarked { get; set; }

        public StaffWithoutCompanyResponse Assignee { get; set; }

        public CompanyTeamResponse AssignedTeam { get; set; }

        public DateTime UpdatedTime { get; set; }

        public DateTime? SnoozeUntil { get; set; }
    }

    public class ConversationAssignResponseViewModel
    {
        public string ConversationId { get; set; }

        public string CompanyId { get; set; }

        public string Status { get; set; }

        public StaffWithoutCompanyResponse Assignee { get; set; }

        public CompanyTeamResponse AssignedTeam { get; set; }

        public List<AdditionalAssigneeResponse> AdditionalAssignees { get; set; }

        public DateTime UpdatedTime { get; set; }

        public DateTime? SnoozeUntil { get; set; }
    }

    public class RemarkResponse
    {
        public string RemarkId { get; set; }

        public string UserProfileId { get; set; }

        public string Remarks { get; set; }

        public StaffWithoutCompanyResponse RemarksStaff { get; set; }

        public Dictionary<string, object> Data { get; set; }

        public DateTime CreatedAt { get; set; }

        public string Type { get; set; }
    }

    public class UpdateRemarkResponse : RemarkResponse
    {
        public StaffWithoutCompanyResponse UpdatedBy { get; set; }

        public DateTime UpdatedAt { get; set; }
    }

    public class DeleteRemarkResponse
    {
        public string RemarkId { get; set; }

        public string UserProfileId { get; set; }
    }

    public class AdditionalAssigneeChanged
    {
        public string ConversationId { get; set; }

        public StaffWithoutCompanyResponse Staff { get; set; }

        public ConversationNoCompanyResponseViewModel Conversation { get; set; }
    }

    public enum AdditionalAssigneeEvent
    {
        Added,
        Removed,
        Exceeded
    }

    public class UserProfileResponse
    {
        public string Id { get; set; }

        public CompanyResponse Company { get; set; }

        public string FirstName { get; set; }

        public string LastName { get; set; }

        public string PictureUrl { get; set; }

        public string DisplayProfilePicture
        {
            get
            {
                if (InstagramUser?.ProfilePic != null)
                {
                    return InstagramUser.ProfilePic;
                }

                if (FacebookAccount?.profile_pic != null)
                {
                    return FacebookAccount.profile_pic;
                }

                if (WeChatUser?.headimgurl != null)
                {
                    return WeChatUser?.headimgurl;
                }

                if (WhatsAppAccount?.profile_pic != null)
                {
                    return WhatsAppAccount?.profile_pic;
                }

                if (LineUser?.pictureUrl != null)
                {
                    return LineUser?.pictureUrl;
                }

                if (ViberUser?.PictureUrl != null)
                {
                    return ViberUser?.PictureUrl;
                }

                if (TelegramUser?.PicturePhotoUrl != null)
                {
                    return TelegramUser?.PicturePhotoUrl;
                }

                return PictureUrl;
            }
        }

        public FacebookInfoResponse FacebookAccount { get; set; }

        public WhatsAppSenderResponse WhatsAppAccount { get; set; }

        public UserDeviceResponse UserDevice { get; set; }

        public GuestResponse RegisteredUser { get; set; }

        public EmailSenderResponse EmailAddress { get; set; }

        public WebClientResponse WebClient { get; set; }

        public WeChatUserInfoResponse WeChatUser { get; set; }

        public LineSenderResponse LineUser { get; set; }

        public SMSSenderResponse SMSUser { get; set; }

        public ViberSenderResponse ViberUser { get; set; }

        public TelegramSenderResponse TelegramUser { get; set; }

        public InstagramSenderResponse InstagramUser { get; set; }

        public WhatsApp360DialogSenderResponse WhatsApp360DialogUser { get; set; }

        public WhatsappCloudApiSenderResponse WhatsappCloudApiUser { get; set; }

        public IList<UserProfileCustomFieldViewModel> CustomFields { get; set; }

        public IList<ConversationHashtagResponse> ConversationHashtags { get; set; }

        public string ConversationId { get; set; }

        public List<ConversationMessageResponseViewModel> LastMessage { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        public DateTime? LastContact { get; set; }

        public DateTime? LastContactFromCustomers { get; set; }

        public bool IsSandbox { get; set; }

        public string Description { get; set; }
    }

    public class UserProfileNoCompanyResponse
    {
        public string Id { get; set; }

        public string FirstName { get; set; }

        public string LastName { get; set; }

        public string PictureUrl { get; set; }

        public string DisplayProfilePicture
        {
            get
            {
                if (InstagramUser?.ProfilePic != null)
                {
                    return InstagramUser.ProfilePic;
                }

                if (FacebookAccount?.profile_pic != null)
                {
                    return FacebookAccount.profile_pic;
                }

                if (WeChatUser?.headimgurl != null)
                {
                    return WeChatUser?.headimgurl;
                }

                if (WhatsAppAccount?.profile_pic != null)
                {
                    return WhatsAppAccount?.profile_pic;
                }

                if (LineUser?.pictureUrl != null)
                {
                    return LineUser?.pictureUrl;
                }

                if (ViberUser?.PictureUrl != null)
                {
                    return ViberUser?.PictureUrl;
                }

                if (TelegramUser?.PicturePhotoUrl != null)
                {
                    return TelegramUser?.PicturePhotoUrl;
                }

                return PictureUrl;
            }
        }

        public FacebookInfoResponse FacebookAccount { get; set; }

        public WhatsAppSenderResponse WhatsAppAccount { get; set; }

        public UserDeviceResponse UserDevice { get; set; }

        public GuestResponse RegisteredUser { get; set; }

        public EmailSenderResponse EmailAddress { get; set; }

        public WebClientResponse WebClient { get; set; }

        public WeChatUserInfoResponse WeChatUser { get; set; }

        public LineSenderResponse LineUser { get; set; }

        public SMSSenderResponse SMSUser { get; set; }

        public InstagramSenderResponse InstagramUser { get; set; }

        public ViberSenderResponse ViberUser { get; set; }

        public TelegramSenderResponse TelegramUser { get; set; }

        public WhatsApp360DialogSenderResponse WhatsApp360DialogUser { get; set; }

        public WhatsappCloudApiSenderResponse WhatsappCloudApiUser { get; set; }

        public IList<UserProfileCustomFieldNoOptionsViewModel> CustomFields { get; set; }

        public IList<ConversationHashtagResponse> ConversationHashtags { get; set; }

        public string ConversationId { get; set; }

        public List<ConversationMessageResponseViewModel> LastMessage { get; set; }

        public List<ContactJoinedList> ContactLists { get; set; } = new List<ContactJoinedList>();

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        public DateTime? LastContact { get; set; }

        public DateTime? LastContactFromCustomers { get; set; }

        public bool IsSandbox { get; set; }

        public string Description { get; set; }

        public bool IsShopifyProfile { get; set; }

        public string Status { get; set; }

        public List<StaffWithoutCompanyResponse> AssigneeList { get; set; }

        public List<ContactCollaboratorViewModel> Collaborators { get; set; }
    }

    public class ContactCollaboratorViewModel
    {
        public string FirstName { get; set; }

        public string LastName { get; set; }

        public string IdentityId { get; set; }
    }

    public class UserProfileContactCollaboratorViewModel
    {
        public string ConversationId { get; set; }

        public string UserProfileId { get; set; }

        public List<ContactCollaboratorViewModel> AdditionalAssignees { get; set; }
    }

    public class ContactJoinedList
    {
        public long Id { get; set; }

        public string ListName { get; set; }
    }

    public class UserGroupBriefResult
    {
        public List<ImportContactHistoryBriefResponse> userGroups { get; set; }

        public int TotalGroups { get; set; }
    }

    public class UserGroupResult
    {
        public List<ImportContactHistoryResponse> userGroups { get; set; }

        public int TotalGroups { get; set; }
    }

    public class ImportContactHistoryBriefResponse
    {
        public long Id { get; set; }

        public string ImportName { get; set; }
    }

    public class ImportContactHistoryResponse
    {
        public long Id { get; set; }

        public string CompanyId { get; set; }

        public string ImportName { get; set; }

        public int ImportedCount { get; set; }

        public int UpdatedCount { get; set; }

        public int FailedCount { get; set; }

        public bool IsImported { get; set; }

        public DateTime CreatedAt { get; set; }

        public List<ImportedUserProfileResponse> ImportedUserProfiles { get; set; }

        public StaffWithoutCompanyResponse ImportedFrom { get; set; }

        public string Status { get; set; }

        public int TotalContactCount { get; set; }

        public int Order { get; set; }

        public bool IsBookmarked { get; set; }

        public string Alias { get; set; }

        public string ContactListType { get; set; }
    }

    public class ImportedUserProfileResponse
    {
        public string UserProfileId { get; set; }
    }

    public class ImportedUserProfileForAutomationTrigger
    {
        public long ImportContactHistoryId { get; set; }

        public string UserProfileId { get; set; }
    }

    public class UserProfileCustomFieldNoOptionsViewModel
    {
        [JsonIgnore]
        public string UserProfileId { get; set; }

        public string CompanyDefinedFieldId { get; set; }

        // public CompanyCustomUserProfileFieldNoOptionsViewModel CompanyDefinedField { get; set; }
        public string Value { get; set; }
    }

    public class UserProfileCustomFieldViewModel
    {
        public string CompanyDefinedFieldId { get; set; }

        public CompanyCustomUserProfileFieldViewModel CompanyDefinedField { get; set; }

        public string Value { get; set; }
    }

    public class UserProfileTotalResponse
    {
        public long TotalNumberOfUserProfile { get; set; }
    }

    public class UserProfileDescriptionModel
    {
        public string Description { get; set; }
    }

    public class SearchUserConversationResponse
    {
        public List<ConversationNoCompanyResponseViewModel> Conversations { get; set; }

        public int TotalResult { get; set; }
    }

    public class SearchUserContactsResponse
    {
        public List<UserProfileNoCompanyResponse> UserProfiles { get; set; }

        public int TotalResult { get; set; }
    }

    public class SafeDeletedUserProfilesResponse
    {
        public List<SafeDeletedUserProfileViewModel> SafeDeletedUserProfiles { get; set; }

        public long TotalResult { get; set; }
    }

    public class SelectAllContactsResponse
    {
        public List<string> UserProfileIds { get; set; }

        public int TotalResult { get; set; }
    }

    public class TotalCountResponse
    {
        public long Count { get; set; }
    }

    public partial class UserDeviceResponse
    {
        public string UserId { get; set; }

        public string DeviceName { get; set; }

        public string DeviceUUID { get; set; }

        public DevicePlatform Platform { get; set; }

        public string OSVersion { get; set; }

        public string AppVersion { get; set; }

        public string NotificationToken { get; set; }

        public int Status { get; set; } = 0;

        public DateTime last_login { get; set; } = DateTime.UtcNow;

        public DateTime created_at { get; set; } = DateTime.UtcNow;

        public DateTime updated_at { get; set; } = DateTime.UtcNow;

        public string IP_Address { get; set; }

        public string SignalRConnectionId { get; set; }

        public string DeviceModel { get; set; }

        public string BuildVersion { get; set; }

        public string AppID { get; set; }

        public int UnreadBadgeNumber { get; set; }
    }

    public class WhatsAppSenderResponse : IMessagingChannelUserDto
    {
        public string Id { get; set; }

        public string phone_number { get; set; }

        public string name { get; set; }

        public string locale { get; set; } = "en";

        public string profile_pic { get; set; }

        public string InstanceId { get; set; }

        public bool is_group
        {
            get
            {
                if (string.IsNullOrEmpty(Id))
                {
                    return false;
                }

                return Id.Contains("@g.us");
            }
        }

        public bool is_twilio
        {
            get
            {
                if (string.IsNullOrEmpty(Id))
                {
                    return false;
                }

                return Id.Contains("whatsapp:+");
            }
        }

        public string ChannelType { get; set; }

        public string UserIdentityId { get; set; }

        public string ChannelIdentityId { get; set; }
    }

    public class WhatsApp360DialogSenderResponse : IMessagingChannelUserDto
    {
        public long Id { get; set; }

        public string WhatsAppId { get; set; }

        public string PhoneNumber { get; set; }

        public string CompanyId { get; set; }

        public string Name { get; set; }

        public string ContactStatus { get; set; } // enum ref. contact status

        public long? ChannelId { get; set; }

        public string ChannelWhatsAppPhoneNumber { get; set; }

        public DateTime CreatedAt { get; set; }

        public DateTime UpdatedAt { get; set; }

        public string ChannelType { get; set; }

        public string UserIdentityId { get; set; }

        public string ChannelIdentityId { get; set; }
    }

    public class SMSSenderResponse : IMessagingChannelUserDto
    {
        public string Id { get; set; }

        public string phone_number { get; set; }

        public string name { get; set; }

        public string locale { get; set; } = "en";

        public string profile_pic { get; set; }

        public string ChannelType { get; set; }

        public string UserIdentityId { get; set; }

        public string ChannelIdentityId { get; set; }
    }

    public class EmailSenderResponse : IMessagingChannelUserDto
    {
        public string Email { get; set; }

        public string Name { get; set; }

        public string locale { get; set; } = "en";

        public string ChannelType { get; set; }

        public string UserIdentityId { get; set; }

        public string ChannelIdentityId { get; set; }
    }

    public class WebClientInitResponse
    {
        public WebClientResponse WebClient { get; set; }

        // public List<ChannelResponse> Channel { get; set; }
    }

    public class WebClientConnectRequest
    {
        public string PhoneNumber { get; set; }

        public string Email { get; set; }

        public string Name { get; set; }
    }

    public class WebClientPopupMessageRequest
    {
        public string PopupMessage { get; set; }
    }

    public class ChannelResponse
    {
        public string Channel { get; set; }

        public string Value { get; set; }

        public string URL { get; set; }

        public string QRcodeURL { get; set; }
    }

    public class WebClientResponse
    {
        public string WebClientUUID { get; set; }

        public string IPAddress { get; set; }

        public string Name { get; set; }

        public string Locale { get; set; } = "en";

        public DateTime? CreatedAt { get; set; }

        public DateTime? UpdatedAt { get; set; }

        public string OnlineStatus { get; set; }

        public string Device { get; set; }
    }

    public class WebClientInfoResponse
    {
        public string OnlineStatus { get; set; }

        public List<IPAddressInfo> Results { get; set; }

        public int Total { get; set; }
    }

    public class WeChatUserInfoResponse : IMessagingChannelUserDto
    {
        public string openid { get; set; }

        public string nickname { get; set; }

        public string language { get; set; }

        public string headimgurl { get; set; }

        public string ChannelType { get; set; }

        public string UserIdentityId { get; set; }

        public string ChannelIdentityId { get; set; }
    }

    public class LineSenderResponse : IMessagingChannelUserDto
    {
        public string userId { get; set; }

        public string displayName { get; set; }

        public string pictureUrl { get; set; }

        public string statusMessage { get; set; }

        public string ChannelType { get; set; }

        public string UserIdentityId { get; set; }

        public string ChannelIdentityId { get; set; }
    }

    public class ViberSenderResponse : IMessagingChannelUserDto
    {
        public long Id { get; set; }

        public string CompanyId { get; set; }

        public string ViberUserId { get; set; }

        public string DisplayName { get; set; }

        public string ViberBotId { get; set; }

        public bool IsSubscribed { get; set; }

        public string PictureUrl { get; set; }

        public string ChannelType { get; set; }

        public string UserIdentityId { get; set; }

        public string ChannelIdentityId { get; set; }
    }

    public class TelegramSenderResponse : IMessagingChannelUserDto
    {
        public long Id { get; set; }

        public string CompanyId { get; set; }

        public long TelegramChatId { get; set; }

        public string FirstName { get; set; }

        public string LastName { get; set; }

        public string Type { get; set; }

        public string PicturePhotoUrl { get; set; }

        public long TelegramBotId { get; set; }

        public string ChannelType { get; set; }

        public string UserIdentityId { get; set; }

        public string ChannelIdentityId { get; set; }
    }

    public class FacebookInfoResponse : IMessagingChannelUserDto
    {
        public string Id { get; set; }

        public string pageId { get; set; }

        public string name { get; set; }

        public string email { get; set; }

        public string locale { get; set; } = "en";

        public string profile_pic { get; set; }

        public string ChannelType { get; set; }

        public string UserIdentityId { get; set; }

        public string ChannelIdentityId { get; set; }
    }

    public class GuestResponse
    {
        public UserInfoResponse UserInfo { get; set; } // navigation property

        public string Location { get; set; }

        public string Locale { get; set; }

        public string Gender { get; set; }
    }

    public class CompanyChannelResponse
    {
        public string CompanyName { get; set; }

        public List<FacebookConfigViewModel> FacebookConfigs { get; set; }

        public List<FacebookConfigViewModel> LeadAdsFacebookConfigs { get; set; }

        public EmailConfigViewModel EmailConfig { get; set; }

        public List<WhatsAppConfigViewModel> WhatsAppConfigs { get; set; }

        public WeChatConfigViewModel WeChatConfig { get; set; }

        public List<LineConfigViewModel> LineConfigs { get; set; }

        public List<SMSViewConfigViewModel> SMSConfigs { get; set; }

        public List<ShoplineConfigResponse> ShoplineConfigs { get; set; }

        public List<ShopifyConfigResponse> ShopifyConfigs { get; set; }
    }

    public class CompanyResponse
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();

        public string CompanyName { get; set; }

        public string SignalRGroupName { get; set; }

        public MyTimeZoneInfo TimeZoneInfo { get; set; }

        public List<FacebookConfigViewModel> FacebookConfigs { get; set; }

        public List<FacebookConfigViewModel> LeadAdsFacebookConfigs { get; set; }

        public EmailConfigViewModel EmailConfig { get; set; }

        public List<WhatsAppConfigViewModel> WhatsAppConfigs { get; set; }

        public WeChatConfigViewModel WeChatConfig { get; set; }

        public List<LineConfigViewModel> LineConfigs { get; set; }

        public List<ViberConfigResponse> ViberConfigs { get; set; }

        public List<TelegramConfigDto> TelegramConfigs { get; set; }

        public List<SMSViewConfigViewModel> SMSConfigs { get; set; }

        public List<ShoplineConfigResponse> ShoplineConfigs { get; set; }

        public List<ShopifyConfigResponse> ShopifyConfigs { get; set; }

        public List<InstagramConfigResponse> InstagramConfigs { get; set; }

        public List<CompanyCustomFieldViewModel> CompanyCustomFields { get; set; }

        public List<CompanyCustomUserProfileFieldViewModel> CustomUserProfileFields { get; set; }

        public List<CompanyHashtagResponse> CompanyHashtags { get; set; }

        public List<WhatsApp360DialogConfigViewModel> WhatsApp360DialogConfigs { get; set; }

        public List<WhatsApp360DialogUsageRecordViewModel> WhatsApp360DialogUsageRecords { get; set; }

        public List<WhatsappCloudApiConfigViewModel> WhatsappCloudApiConfigs { get; set; }

        public List<BusinessBalanceDto> WhatsappCloudApiUsageRecords { get; set; }

        public List<BillRecordResponse> BillRecords { get; set; }

        public List<ProviderConfigDto> CrmHubProviderConfigs { get; set; }

        public DateTime CreatedAt { get; set; }

        public string CompanyIconFileURL
        {
            get
            {
                if (CompanyIconFile != null)
                {
                    return $"company/icon/{CompanyIconFile.ProfilePictureId}";
                }

                return null;
            }
        }

        public CompanyIconFile CompanyIconFile { get; set; }

        public int MaximumAgents { get; set; }

        public int MaximumWhatsappInstance { get; set; }

        public int? MaximumAutomations { get; set; }

        public int? MaximumNumberOfChannel { get; set; }

        public int CurrentAgents { get; set; }

        public bool IsSubscriptionActive { get; set; }

        public string CompanyCountry { get; set; }

        public bool IsFreeTrial { get; set; }

        public string ReferralCode { get; set; }

        public bool IsRemovedChannels { get; set; }

        public bool EnableSensitiveSetting { get; set; }

        public bool IsShowQRCodeMapping { get; set; }

        public bool IsQRCodeMappingEnabled { get; set; }

        public long PurchasedWhatsAppPhoneNumber { get; set; } // No longer used

        public List<TwilioUsageRecord> TwilioUsageRecords { get; set; }

        public CompanySetting CompanySetting { get; set; }

        public List<RolePermissionResponse> RolePermission { get; set; }

        public bool IsExceededTwilioDailyLimit { get; set; }

        public bool IsPaymentFailed { get; set; }

        public bool IsSandbox { get; set; }

        public string DefaultInboxOrder { get; set; }

        #region SFCC

        public bool IsEnabledSFCC { get; set; }

        #endregion

        #region Shopify

        public bool IsShopifyAccount { get; set; }

        public int? ShopifyOrderConversion { get; set; }

        public int MaximumShopifyStore { get; set; }

        #endregion

        #region ForMultipleCompany

        public List<AssociatedCompany> AssociatedCompaniesList { get; set; }

        #endregion

        #region Stripe

        public bool IsStripeIntegrationEnabled { get; set; }

        public bool IsStripePaymentEnabled { get; set; }

        #endregion

        public CompanyType CompanyType { get; set; }

        public ResellerCompanyProfileViewModel Reseller { get; set; }

        public AddonStatus AddonStatus { get; set; }

        public DateTime? BaseSubscriptionEndDateTime { get; set; }

        #region Contact

        public bool IsExpressImportEnabled { get; set; }

        #endregion

        public string SubscriptionCountryTier { get; set; }
    }

    public class AddonStatus
    {
        public bool IsAdditionalStaffEnabled { get; set; }

        public bool IsAdditionalContactsEnabled { get; set; }

        public bool IsUnlimitedContactEnabled { get; set; }

        public bool IsUnlimitedChannelEnabled { get; set; }

        public bool IsEnterpriseContactMaskingEnabled { get; set; }

        public bool IsWhatsappQrCodeEnabled { get; set; }

        public bool IsShopifyIntegrationEnabled { get; set; }

        public bool IsHubspotIntegrationEnabled { get; set; }

        public bool IsPaymentIntegrationEnabled { get; set; }

        public bool IsSalesforceCrmEnabled { get; set; }

        public bool IsSalesforceMarketingCloudEnabled { get; set; }

        public bool IsSalesforceCommerceCloudEnabled { get; set; }

        public bool IsOnboardingSupportActivated { get; set; }

        public bool IsPrioritySupportActivated { get; set; }

        public bool IsChatbotSetupSupportActivated { get; set; }

        public bool IsHubspotIntegrationFreeTrialEligible { get; set; }

        public bool IsSalesforceCrmFreeTrialEligible { get; set; }

        public bool IsMicrosoftDynamics365IntegrationEnabled { get; set; }

        public bool IsFacebookLeadAdsEnabled { get; set; }

        public bool IsPlatformApiEnabled { get; set; }

        public bool IsMakeIntegrationEnabled { get; set; }

        public bool IsZapierIntegrationEnabled { get; set; }

        public bool IsPiiMaskingEnabled { get; set; }
    }

    public class AssociatedCompany
    {
        public string CompanyId { get; set; }

        public string CompanyName { get; set; }

        public string CompanyIconURL { get; set; }

        public CompanyIconFile CompanyIconFile { get; set; }
    }

    public class OnboardingStatus
    {
        public bool IsMessagingChannelAdded { get; set; }

        public bool IsWebWidgetCustomized { get; set; }

        public bool IsWebWidgetAdded { get; set; }

        public bool IsInvitedTeammate { get; set; }

        public bool IsAssignmentRuleAdded { get; set; }

        public bool IsBroadcastMessageSent { get; set; }
    }

    public class NewOnboardingStatus
    {
        public bool IsInboxDemoCompleted { get; set; }

        public bool IsInboxInUse { get; set; }

        public bool IsMessagingChannelConnected { get; set; }

        public bool IsWhatsappConsultationBooked { get; set; }

        public bool IsInvitedTeammate { get; set; }

        public bool IsAutomationRuleAdded { get; set; }

        public bool IsQuickReplyAdded { get; set; }

        public bool IsContactListCreated { get; set; }

        public bool IsCampaignCreated { get; set; }

        public bool IsWebWidgetAdded { get; set; }
    }

    public class BillRecordResponse
    {
        public long Id { get; set; }

        public string CompanyId { get; set; }

        public SubscriptionPlan SubscriptionPlan { get; set; }

        public string SubscriptionPlanId { get; set; }

        public DateTime PeriodStart { get; set; }

        public DateTime PeriodEnd { get; set; }

        public BillStatus Status { get; set; }

        public PaymentStatus PaymentStatus { get; set; }

        public double PayAmount { get; set; }

        public StaffWithoutCompanyResponse PurchaseStaff { get; set; }

        public string invoice_Id { get; set; }

        public string stripe_subscriptionId { get; set; }

        public string customerId { get; set; }

        public string customer_email { get; set; }

        public string hosted_invoice_url { get; set; }

        public string invoice_pdf { get; set; }

        public string chargeId { get; set; }

        public long amount_due { get; set; }

        public long amount_paid { get; set; }

        public long amount_remaining { get; set; }

        public string currency { get; set; }

        public DateTime created { get; set; }

        public long? ShopifyChargeId { get; set; }

        public bool PaidByReseller { get; set; }

        public bool IsFreeTrial { get; set; }
    }

    public class CompanyCustomUserProfileFieldNoOptionsViewModel
    {
        public string Id { get; set; }

        public List<CustomUserProfileFieldLingualViewModel> CustomUserProfileFieldLinguals { get; set; }

        public string FieldName { get; set; }

        public string Type { get; set; }

        public int Order { get; set; }

        public bool? IsVisible { get; set; }

        public bool? IsEditable { get; set; }

        public bool? IsDefault { get; set; }
    }

    public class CompanyCustomUserProfileFieldViewModel
    {
        public string Id { get; set; }

        public List<CustomUserProfileFieldLingualViewModel> CustomUserProfileFieldLinguals { get; set; }

        public List<CompanyCustomUserProfileFieldOptionViewModel> CustomUserProfileFieldOptions { get; set; }

        public string FieldName { get; set; }

        public string Type { get; set; }

        public int Order { get; set; }

        public bool? IsVisible { get; set; }

        public bool? IsEditable { get; set; }

        public bool? IsDefault { get; set; }

        public bool? IsDeletable { get; set; }

        public string FieldsCategory { get; set; }
    }

    public class CompanyCustomUserProfileFieldOptionViewModel
    {
        public long Id { get; set; }

        public List<CustomUserProfileFieldLingualViewModel> CustomUserProfileFieldOptionLinguals { get; set; }

        public string Value { get; set; }

        public int Order { get; set; }
    }

    public class CustomUserProfileFieldLingualViewModel
    {
        public string Language { get; set; }

        public string DisplayName { get; set; }
    }

    public class CompanyCustomFieldViewModel
    {
        public string Category { get; set; }

        public List<CompanyCustomFieldFieldLingualViewModel> CompanyCustomFieldFieldLinguals { get; set; }

        public string FieldName { get; set; }

        public string Value { get; set; }

        public string Type { get; set; }

        public int? Order { get; set; }

        public bool? IsVisible { get; set; } = true;

        public bool? IsEditable { get; set; } = true;

        public bool? IsDefault { get; set; }
    }

    public class CompanyCustomFieldFieldLingualViewModel
    {
        public string Language { get; set; }

        public string DisplayName { get; set; }
    }

    public class StaffResponse
    {
        public CompanyResponse Company { get; set; }

        public UserInfoResponse UserInfo { get; set; }

        public string StaffId { get; set; }

        public string Role { get; set; }

        public string RoleType { get; set; }

        public string Name { get; set; }

        public string Locale { get; set; }

        public string TimeZoneInfoId { get; set; }

        public MyTimeZoneInfo TimeZoneInfo { get; set; }

        public string Position { get; set; }

        public string ProfilePictureURL
        {
            get
            {
                if (string.IsNullOrEmpty(ProfilePicture?.ProfilePictureId))
                {
                    return null;
                }

                return $"/comapny/profilepicture/{ProfilePicture?.ProfilePictureId}";
            }
        }

        public ProfilePictureFile ProfilePicture { get; set; }

        public string Status { get; set; }

        public bool? IsAcceptedInvitation
        {
            get
            {
                if (UserInfo != null)
                {
                    return UserInfo.EmailConfirmed && !UserInfo.UserName.StartsWith("invite.");
                }

                return null;
            }
        }

        public bool IsShowName { get; set; }

        public string Message { get; set; }
    }

    public class AdditionalAssigneeResponse
    {
        [JsonIgnore]
        public string ConversationId { get; set; }

        public StaffWithoutCompanyResponse Assignee { get; set; }
    }

    public class StaffWithoutCompanyResponse
    {
        public UserInfoResponse UserInfo { get; set; }

        public long StaffId { get; set; }

        public string Role { get; set; }

        public string RoleType { get; set; }

        public string Name
        {
            get
            {
                if (!string.IsNullOrEmpty(UserInfo?.DisplayName))
                {
                    return UserInfo.DisplayName;
                }
                else
                {
                    return null;
                }
            }
        }

        public string Locale { get; set; }

        public string TimeZoneInfoId { get; set; }

        public MyTimeZoneInfo TimeZoneInfo { get; set; }

        public string Position { get; set; }

        public string ProfilePictureURL
        {
            get
            {
                if (string.IsNullOrEmpty(ProfilePicture?.ProfilePictureId))
                {
                    return null;
                }

                return $"/comapny/profilepicture/{ProfilePicture?.ProfilePictureId}";
            }
        }

        public ProfilePictureFile ProfilePicture { get; set; }

        public string Status { get; set; }

        public bool? IsAcceptedInvitation
        {
            get
            {
                if (UserInfo != null)
                {
                    return UserInfo.EmailConfirmed && !UserInfo.UserName.StartsWith("invite.");
                }

                return null;
            }
        }

        public StaffNotificationSetting NotificationSetting { get; set; }

        public bool IsShowName { get; set; }

        public string Message { get; set; }

        public bool IsNewlyRegistered { get; set; }

        public List<CompanyTeamResponse> AssociatedTeams { get; set; }

        #region QRCode mapping

        public string QRCodeIdentity { get; set; }

        public TargetedChannelModel QRCodeChannel { get; set; }

        #endregion

        #region DefaultCurrency

        public string DefaultCurrency { get; set; }

        #endregion

        public bool IsCompanyOwner { get; set; }
    }

    public class UserInfoResponse
    {
        public string Id { get; set; }

        public string FirstName { get; set; }

        public string LastName { get; set; }

        public string DisplayName { get; set; }

        public string UserName { get; set; }

        public string Email { get; set; }

        public string UserRole { get; set; }

        public string PhoneNumber { get; set; }

        public bool EmailConfirmed { get; set; }

        public DateTime CreatedAt { get; set; }
    }

    public class ChannelSignal
    {
        public string ChannelName { get; set; }
    }

    public class SignalROnChatAPIAdded
    {
        public string InstanceId { get; set; }
    }

    public class SegmentResponse
    {
        public long Id { get; set; }

        public string CompanyId { get; set; }

        public string Name { get; set; }

        public List<ConditionResponse> Conditions { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        public string Status { get; set; }

        public StaffWithoutCompanyResponse SavedBy { get; set; }
    }

    public class CheckWhatsappConversationSessionStatusViewModel
    {
        public List<string> ConversationIds { get; set; }

        public List<string> WhatsappPhoneNumbers { get; set; }
    }

    public class WhatsappConversationSessionStatus
    {
        public string ConversationId { get; set; }

        public string WhatsappPhoneNumber { get; set; }

        public DateTime? LastClientMessageReceivedAt { get; set; }

        public bool IsTemplateMessageRequired
        {
            get
            {
                if (LastClientMessageReceivedAt == null)
                {
                    return true;
                }

                return LastClientMessageReceivedAt <= DateTime.UtcNow.AddDays(-1);
            }
        }
    }

    public class ResellerPlanChangeResponse
    {
        public string ClientCompanyId { get; set; }

        public string ClientCompanyName { get; set; }

        public bool IsSuccess { get; set; }

        public string ErrorMessage { get; set; }
    }

    public class FbIgPageResponseVM
    {
        public List<FbIgPostVM> FacebookPosts { get; set; }

        public List<FbIgPostVM> InstagramMedias { get; set; }

        public VMtype ViewModelType { get; set; }
    }

    public enum VMtype
    {
        Facebook = 0,
        Instagram = 1
    }

    public class FbIgPostVM
    {
        public string Id { get; set; }

        public string Content { get; set; }

        public DateTime CreatedAt { get; set; }

        public string MediaUrl { get; set; }

        public string PostUrl { get; set; }
    }

    public class CompanyPagesResponseVM
    {
        public List<CompanyPageInfo> PageInfos { get; set; }
    }

    public class CompanyPageInfo
    {
        public string PageName { get; set; }

        public string PageId { get; set; }

        public string PageProfilePictureLink { get; set; }

        public FacebookStatus Status { get; set; }
    }

    public class FacebookIcebreakerVM
    {
        public string Question { get; set; }

        public string Payload { get; set; }
    }

    public class ResellerCompanyProfileViewModel
    {
        public string CompanyName { get; set; }

        public string CompanyProfileId { get; set; }

        public string ContactEmail { get; set; }

        public string LogoLink { get; set; }
    }

    public class ConversationMessageCountResponse
    {
        public int Count { get; set; }
    }

    public class ConversationUnreadSummary
    {
        public int AssignedToMe { get; set; }

        public int Collaborator { get; set; }

        public int Mentioned { get; set; }
    }

    public class ResultLine
    {
        public string AssigneeId { get; set; }

        public string Type { get; set; }

        public string Status { get; set; }

        public long? TeamId { get; set; }

        public long Count { get; set; }
    }

    public class ConversationSummary
    {
        public List<ResultLine> ConversationSummaries { get; set; } = new List<ResultLine>();

        public List<HashtagSummary> HashtagSummaries { get; set; } = new List<HashtagSummary>();

        public long? UnreadCount { get; set; }
    }

    public class HashtagSummary
    {
        public string HashtagId { get; set; }

        public string Hashtag { get; set; }

        public int Count { get; set; }
    }

    public class ConversationMetadataResponseViewModel
    {
        [JsonProperty("whatsappcloudapi")]
        public List<ConversationMetadataChannelResponseViewModel> WhatsappCloudApi { get; set; }
    }

    public class ConversationMetadataChannelResponseViewModel
    {
        [JsonProperty("channel_identity_id")]
        public string ChannelIdentityId { get; set; }

        [JsonProperty("channel_metadata")]
        public JObject ChannelMetadata { get; set; }
    }

    public class StaffOverviewResponse
    {
        public long Id { get; set; }

        public string Username { get; set; }

        public string DisplayName { get; set; }

        public string FirstName { get; set; }

        public string LastName { get; set; }

        public string Email { get; set; }

        public string Role { get; set; }

        public ProfilePictureFile ProfilePicture { get; set; }

        public string StaffIdentityId { get; set; }

        public List<long> AssociatedTeamIds { get; set; }

        public string Position { get; set; }

        public string Status { get; set; }

        public DateTime CreatedAt { get; set; }

        public bool IsCompanyOwner { get; set; }
    }
}