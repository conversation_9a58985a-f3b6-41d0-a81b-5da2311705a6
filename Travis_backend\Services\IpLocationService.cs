﻿using System;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Json;
using System.Threading.Tasks;
using System.Web;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using IPGeolocation;
using Travis_backend.CommonDomain.ViewModels;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.Helpers;

namespace Travis_backend.Services;

public interface IIpLocationService
{
    /// <summary>
    /// Get ISO 2 letters country code.
    /// </summary>
    /// <param name="ipAddress">IP Address.</param>
    /// <returns>Country Code.</returns>
    public Task<string> GetTwoLettersCountryCodeAsync(string ipAddress);

    public Task<MyTimeZoneInfo> GetTimeZoneByIpaddressAsync(string ipaddress);

    public Task<IpLocationInfo> GetLocationInfoByIpaddressAsync(string ipaddress);
}

/// <summary>
/// This timezone service have changed to use IPGeolocation.
/// SDK Documentation:
/// https://ipgeolocation.io/documentation/ip-geolocation-api-c-sharp-dotnet-sdk.html
/// </summary>
public class IpLocationService : IIpLocationService
{
    private readonly ILogger<IpLocationService> _logger;
    private readonly IPGeolocationAPI _geoApi;

    public IpLocationService(
        ILogger<IpLocationService> logger,
        IPGeolocationAPI geoApi)
    {
        _logger = logger;
        _geoApi = geoApi;
    }

    /// <inheritdoc />
    public async Task<string> GetTwoLettersCountryCodeAsync(string ipAddress)
    {
        var locationInfo = await GetLocationInfoByIpaddressAsync(ipAddress);
        return locationInfo.CountryCode2;
    }

    /// <summary>
    /// Get TimeZoneInfo by ip address
    /// </summary>
    /// <param name="ipaddress">127.0.0.1</param>
    /// <returns>MyTimeZoneInfo</returns>
    public async Task<MyTimeZoneInfo> GetTimeZoneByIpaddressAsync(string ipaddress)
    {
        try
        {
            // var geoApi = InitGeoApi();
            var geoParams = new GeolocationParams();

            geoParams.SetLang("en");
            geoParams.SetFields("geo,time_zone");
            geoParams.SetIPAddress(ipaddress);

            var location = _geoApi.GetGeolocation(geoParams);

            location.TryGetValue("status", out var status);
            location.TryGetValue("message", out var message);

            var geoInfo =
                location.TryGetValue("response", out var value)
                    ? (Geolocation) value
                    : null;

            if (status != null && (int)status != 200)
            {
                _logger.LogError(
                    "[GetTimeZoneByIpaddressAsync] Get geolocation failed. {ReturnMessage}",
                    message?.ToString());
                throw new Exception(
                    $"Get geolocation failed. {message?.ToString()}");
            }

            return TimeZoneHelper.GetTimeZoneByIanaId(geoInfo?.GetTimezone().GetName());
        }
        catch (Exception e)
        {
            _logger.LogError(
                "[GetTimeZoneByIpaddressAsync] Ip look failed. Ip Address: {IpAddress}. Message: {Message}",
                ipaddress,
                e.Message);
            throw new Exception($"Ip Look Up failed. {e.Message}");
        }
    }

    public async Task<IpLocationInfo> GetLocationInfoByIpaddressAsync(string ipaddress)
    {
        try
        {
            // var geoApi = InitGeoApi();
            var geoParams = new GeolocationParams();

            geoParams.SetLang("en");
            geoParams.SetFields("geo, currency");
            geoParams.SetIPAddress(ipaddress);

            var location = _geoApi.GetGeolocation(geoParams);

            location.TryGetValue("status", out var status);
            location.TryGetValue("message", out var message);

            var geoInfo =
                location.TryGetValue("response", out var value)
                    ? (Geolocation) value
                    : null;

            if (status != null && (int)status != 200)
            {
                _logger.LogError(
                    "[GetLocationInfoByIpaddressAsync] Get geolocation failed. {ReturnMessage}",
                    message?.ToString());
                throw new Exception(
                    $"Get geolocation failed. {message?.ToString()}");
            }

            return new IpLocationInfo
            {
                CountryCode2 = geoInfo?.GetCountryCode2(),
                CountryCode3 = geoInfo?.GetCountryCode3(),
                CountryName = geoInfo?.GetCountryName(),
                StateProvince = geoInfo?.GetStateProvince(),
                City = geoInfo?.GetCity(),
                District = geoInfo?.GetDistrict(),
                ZipCode = geoInfo?.GetZipCode(),
                Latitude = geoInfo?.GetLatitude(),
                Longitude = geoInfo?.GetLongitude(),
                Currency = new IpLocationCurrencyInfo
                {
                    CurrencyName = geoInfo?.GetCurrency().GetName(),
                    CurrencyCode = geoInfo?.GetCurrency().GetCode(),
                    CurrencySymbol = geoInfo?.GetCurrency().GetSymbol()
                }
            };
        }
        catch (Exception e)
        {
            _logger.LogError(
                "[GetLocationInfoByIpaddressAsync] Ip look failed. Ip Address: {IpAddress}. Message: {Message}",
                ipaddress,
                e.Message);
            throw new Exception($"Ip Look Up failed. {e.Message}");
        }
    }
}