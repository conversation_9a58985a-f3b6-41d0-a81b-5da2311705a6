﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Threading.Tasks;
using AutoMapper;
using Hangfire;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Polly;
using Travis_backend.AccountAuthenticationDomain.Models;
using Travis_backend.BackgroundTaskServices.Attributes;
using Travis_backend.Cache;
using Travis_backend.ChannelDomain.ViewModels;
using Travis_backend.Constants;
using Travis_backend.ContactDomain.Services;
using Travis_backend.ConversationDomain.ConversationResolver;
using Travis_backend.ConversationServices;
using Travis_backend.Database;
using Travis_backend.MessageDomain.Models;
using Travis_backend.MessageDomain.ViewModels;

namespace Travis_backend.Controllers.WebhookControllers
{
    public class LiveWebhookController : Controller
    {
        private readonly ApplicationDbContext _appDbContext;
        private readonly ILogger<LiveWebhookController> _logger;
        private readonly IConversationMessageService _messagingService;
        private readonly IHttpClientFactory _httpClientFactory;
        private readonly IConversationResolver _conversationResolver;
        private readonly ILockService _lockService;

        public LiveWebhookController(
            ApplicationDbContext appDbContext,
            IMapper mapper,
            ILogger<LiveWebhookController> logger,
            IConversationMessageService messagingService,
            IHttpClientFactory httpClientFactory,
            IConversationResolver conversationResolver,
            ILockService lockService)
        {
            _appDbContext = appDbContext;
            _logger = logger;
            _messagingService = messagingService;
            _httpClientFactory = httpClientFactory;
            _conversationResolver = conversationResolver;
            _lockService = lockService;
        }

        [HttpPost]
        [Route("line/webhook/{companyId}")]
        public async Task<IActionResult> LineWebhook(string companyId, [FromBody] LineWebhook webhookData)
        {
            _logger.LogInformation(
                "LineWebhook: {LineWebhookPayload} from Company: {CompanyId}",
                companyId,
                JsonConvert.SerializeObject(webhookData));

            try
            {
                Policy.Handle<Exception>()
                    .WaitAndRetry(
                        3,
                        sleepDurationProvider: i => TimeSpan.FromMilliseconds(100),
                        onRetry: (exception, _, retryCount, _) =>
                        {
                            _logger.LogError(
                                exception,
                                "LineWebhook: Enqueue error {ExceptionMessage} with retry {RetryCount}",
                                exception.Message,
                                retryCount);
                        })
                    .Execute(
                        () =>
                        {
                            BackgroundJob.Enqueue(() => HandleLineWebhook(companyId, webhookData));
                        });
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "LineWebhook Error: {ExceptionMessage}",
                    ex.Message);

                return BadRequest();
            }

            return Ok();
        }

        [ApiExplorerSettings(IgnoreApi = true)]
        [HangfireJobExpirationTimeout(timeoutInMinutes: 60)]
        [AutomaticRetry(
            Attempts = 10,
            OnAttemptsExceeded = AttemptsExceededAction.Fail,
            DelaysInSeconds =
            [
                60, // 1 minute
                120, // 2 minutes
                240, // 4 minutes
                480, // 8 minutes
                960, // 16 minutes
                1920, // 32 minutes
                3600, // 1 hour
                7200, // 2 hours
                14400, // 4 hours
                28800, // 8 hours
            ])]
        public async Task HandleLineWebhook(string companyId, LineWebhook webhookData)
        {
            try
            {
                var lineConfig = await _appDbContext.ConfigLineConfigs.Where(x => x.CompanyId == companyId)
                    .FirstOrDefaultAsync();

                var formContent = new FormUrlEncodedContent(
                    new[]
                    {
                        new KeyValuePair<string, string>("grant_type", "client_credentials"),
                        new KeyValuePair<string, string>("client_id", lineConfig.ChannelID),
                        new KeyValuePair<string, string>("client_secret", lineConfig.ChannelSecert)
                    });

                if (lineConfig.TokenExpireAt < DateTime.UtcNow)
                {
                    var httpClient = _httpClientFactory.CreateClient(HttpClientHandlerName.Default);

                    var getAccessTokenResponse = await httpClient.PostAsync(
                        $"https://api.line.me/v2/oauth/accessToken",
                        formContent);

                    if (getAccessTokenResponse.IsSuccessStatusCode)
                    {
                        var accessToken = JsonConvert.DeserializeObject<LineAccessTokenResult>(
                            await getAccessTokenResponse.Content.ReadAsStringAsync());
                        lineConfig.ChannelAccessToken = accessToken.access_token;
                        lineConfig.TokenExpireAt = DateTime.UtcNow.AddSeconds(accessToken.expires_in - 30);
                        await _appDbContext.SaveChangesAsync();
                    }
                }

                var bot = new isRock.LineBot.Bot(lineConfig.ChannelAccessToken);
                // Get LINE Event
                foreach (var lineEvent in webhookData.events)
                {
                    // prepare reply message
                    if (lineEvent.type.ToLower() == "message")
                    {
                        var (conversation, myLock) = await _conversationResolver.GetConversationForReceivingMessageAsync(
                            companyId,
                            ChannelTypes.Line,
                            lineConfig.ChannelIdentityId,
                            lineEvent.source.userId);

                        var conversationMessage = new ConversationMessage()
                        {
                            MessageContent = lineEvent.message.text,
                            Channel = ChannelTypes.Line,
                            LineSender = conversation.LineUser,
                            MessageType = "text",
                            MessageUniqueID = lineEvent.message.id,
                            IsSentFromSleekflow = false,
                            Status = MessageStatus.Received,
                            DeliveryType = DeliveryType.Normal,
                            ChannelIdentityId = lineConfig.ChannelIdentityId,
                        };

                        var httpClient = _httpClientFactory.CreateClient(HttpClientHandlerName.Default);

                        var fileURL = new List<FileURLMessage>();

                        httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue(
                            "Bearer",
                            lineConfig.ChannelAccessToken);

                        switch (lineEvent.message.type.ToLower())
                        {
                            case "text":
                                await _messagingService.SendMessage(conversation, conversationMessage);
                                break;
                            case "image":
                                conversationMessage.MessageType = "file";
                                conversationMessage.MessageContent = lineEvent.message.text;

                                switch (lineEvent.message.contentProvider.type)
                                {
                                    case ChannelTypes.Line:
                                        var response = await httpClient.GetAsync(
                                            $"https://api-data.line.me/v2/bot/message/{lineEvent.message.id}/content");
                                        if (response.IsSuccessStatusCode)
                                        {
                                            var fileStream = await response.Content.ReadAsStreamAsync();
                                            var contentHeaders = response.Content.Headers.ToList();
                                            var contentTypes = contentHeaders.Where(x => x.Key == "Content-Type")
                                                .FirstOrDefault();
                                            var contentType = contentTypes.Value.FirstOrDefault();

                                            fileURL.Add(
                                                new FileURLMessage
                                                {
                                                    FileName = $"image_{DateTime.UtcNow}.jpg",
                                                    MIMEType = contentType,
                                                    FileStream = fileStream
                                                });
                                        }

                                        break;
                                    case "external":
                                        fileURL.Add(
                                            new FileURLMessage
                                            {
                                                FileName = $"image_{DateTime.UtcNow}.jpg",
                                                MIMEType = "image/jpeg",
                                                FileURL = lineEvent.message.contentProvider.originalContentUrl
                                            });
                                        break;
                                }

                                await _messagingService.SendFileMessageByFBURL(
                                    conversation,
                                    conversationMessage,
                                    fileURL);

                                break;
                            case "sticker":

                                break;
                            default:
                                conversationMessage.MessageType = "file";
                                conversationMessage.MessageContent = lineEvent.message.text;

                                switch (lineEvent.message.contentProvider.type)
                                {
                                    case ChannelTypes.Line:
                                        var response = await httpClient.GetAsync(
                                            $"https://api-data.line.me/v2/bot/message/{lineEvent.message.id}/content");
                                        if (response.IsSuccessStatusCode)
                                        {
                                            var fileStream = await response.Content.ReadAsStreamAsync();
                                            var contentHeaders = response.Content.Headers.ToList();
                                            var contentTypes = contentHeaders.Where(x => x.Key == "Content-Type")
                                                .FirstOrDefault();
                                            var contentType = contentTypes.Value.FirstOrDefault();

                                            switch (contentType)
                                            {
                                                case "audio/x-m4a":
                                                    fileURL.Add(
                                                        new FileURLMessage()
                                                        {
                                                            FileName = $"audio_{DateTime.UtcNow}.m4a",
                                                            MIMEType = contentType,
                                                            FileStream = fileStream
                                                        });
                                                    break;
                                                case "video/mp4":
                                                    fileURL.Add(
                                                        new FileURLMessage()
                                                        {
                                                            FileName = $"video_{DateTime.UtcNow}.mp4",
                                                            MIMEType = contentType,
                                                            FileStream = fileStream
                                                        });
                                                    break;
                                                case "image/jpeg":
                                                    fileURL.Add(
                                                        new FileURLMessage()
                                                        {
                                                            FileName = $"image_{DateTime.UtcNow}.jpg",
                                                            MIMEType = contentType,
                                                            FileStream = fileStream
                                                        });
                                                    break;
                                                default:
                                                    fileURL.Add(
                                                        new FileURLMessage()
                                                        {
                                                            FileName = $"file_{DateTime.UtcNow}",
                                                            MIMEType = contentType,
                                                            FileStream = fileStream
                                                        });
                                                    break;
                                            }
                                        }

                                        break;
                                    case "external":
                                        fileURL.Add(
                                            new FileURLMessage
                                            {
                                                FileName = $"image_{DateTime.UtcNow}.jpg",
                                                MIMEType = "image/jpeg",
                                                FileURL = lineEvent.message.contentProvider.originalContentUrl
                                            });
                                        break;
                                }

                                await _messagingService.SendFileMessageByFBURL(
                                    conversation,
                                    conversationMessage,
                                    fileURL);

                                break;
                        }

                        await _lockService.ReleaseLockAsync(myLock);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "LINE Webhook Error {ExceptionString}",
                    ex.ToString());
            }
        }
    }
}