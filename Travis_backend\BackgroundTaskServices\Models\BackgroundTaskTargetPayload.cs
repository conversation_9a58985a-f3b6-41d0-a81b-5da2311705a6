﻿using Sleekflow.Apis.FlowHub.Model;

namespace Travis_backend.BackgroundTaskServices.Models;

public abstract class BackgroundTaskTargetPayload
{
    public abstract BackgroundTaskTargetType TargetType { get; set; }
}

public class ContactListBackgroundTaskTargetPayload : BackgroundTaskTargetPayload
{
    public long ListId { get; set; }

    public string ImportName { get; set; }

    public override BackgroundTaskTargetType TargetType { get; set; } = BackgroundTaskTargetType.List;
}

public class ContactBackgroundTaskTargetPayload : BackgroundTaskTargetPayload
{
    public override BackgroundTaskTargetType TargetType { get; set; } = BackgroundTaskTargetType.Contact;
}

public class CampaignBackgroundTaskTargetPayload : BackgroundTaskTargetPayload
{
    public string BroadcastTemplateId { get; set; }

    public string TemplateName { get; set; }

    public override BackgroundTaskTargetType TargetType { get; set; } = BackgroundTaskTargetType.Campaign;
}

public class AnalyticBackgroundTaskTargetPayload : BackgroundTaskTargetPayload
{
    public override BackgroundTaskTargetType TargetType { get; set; } = BackgroundTaskTargetType.Analytic;
}

public class WhatsAppHistoryBackgroundTaskTargetPayload : BackgroundTaskTargetPayload
{
    public override BackgroundTaskTargetType TargetType { get; set; } = BackgroundTaskTargetType.WhatsApp;
}

public class ConvertCampaignLeadsToContactListTaskTargetPayLoad : BackgroundTaskTargetPayload
{
    public string NewContactListName { get; set; }

    public long ListId { get; set; }

    public override BackgroundTaskTargetType TargetType { get; set; } = BackgroundTaskTargetType.List;
}

public class LoopThroughAndEnrollContactsToFlowHubTaskTargetPayload : BackgroundTaskTargetPayload
{
    public override BackgroundTaskTargetType TargetType { get; set; } = BackgroundTaskTargetType.Contact;

    public string FlowHubWorkflowId { get; set; }

    public string FlowHubWorkflowVersionedId { get; set; }
}

public class ExportFlowHubWorkflowExecutionUsagesToCsvTaskTargetPayload : BackgroundTaskTargetPayload
{
    public override BackgroundTaskTargetType TargetType { get; set; } = BackgroundTaskTargetType.FlowHubUsages;

    public WorkflowExecutionUsageFilters Filters { get; set; }
}

public enum BackgroundTaskTargetType
{
    None = 0,
    Contact = 1,
    List = 2,
    Campaign = 3,
    Analytic = 4,
    WhatsApp = 5,
    IndividualContacts = 6,
    FlowHubUsages = 7
}