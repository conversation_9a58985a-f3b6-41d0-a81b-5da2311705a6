﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using AutoMapper;
using Hangfire;
using isRock.LineBot.Extensions;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using RestSharp;
using ShopifySharp;
using ShopifySharp.Filters;
using ShopifySharp.Lists;
using Travis_backend.Cache;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.CompanyDomain.Services;
using Travis_backend.ContactDomain.Constants;
using Travis_backend.ContactDomain.Models;
using Travis_backend.ContactDomain.Services;
using Travis_backend.ContactDomain.ViewModels;
using Travis_backend.ConversationServices;
using Travis_backend.Database;
using Travis_backend.Enums;
using Travis_backend.Extensions;
using Travis_backend.Helpers;
using Travis_backend.IntegrationServices.Models;
using Travis_backend.IntegrationServices.ViewModels;
using Travis_backend.StripeIntegrationDomain.Models;
using Travis_backend.SubscriptionPlanDomain.Constants;
using Travis_backend.SubscriptionPlanDomain.Services;
using CustomerService = ShopifySharp.CustomerService;
using OrderService = ShopifySharp.OrderService;
using Product = ShopifySharp.Product;
using ProductService = ShopifySharp.ProductService;

namespace Travis_backend.IntegrationServices
{
    public interface IShopifyService
    {
        Task SyncProduct(string companyId, long shopifyIntegrationId);

        Task SyncShopify(string companyId, long shopifyIntegrationId);

        Task SyncOrders(string companyId, long shopifyIntegrationId);

        Task SyncCustomers(string companyId, long shopifyIntegrationId);

        Task<Dictionary<string, List<string>>> PatchNewWebhookForAllCompanies();

        Task<Dictionary<string, List<string>>> RollbackWebhookForAllCompanies();

        Task RegisterWebhook(string companyId, long shopifyIntegrationId);

        Task RegisterNewWebhooks(string companyId, long shopifyIntegrationId);

        Task UpsertOrders(
            string companyId,
            long shopifyIntegrationId,
            ShopifySharp.Order order,
            bool isOrderCreated,
            bool updateCustomField = true);

        Task CustomerUpdated(
            string companyId,
            long shopifyIntegrationId,
            ShopifySharp.Customer customer,
            AutomationType? shopifyTrigger = AutomationType.ShopifyNewCustomerTrigger);

        Task CheckoutUpdated(string companyId, long shopifyIntegrationId, ShopifyCheckout checkout);
        Task AppUninstalled(string companyId, long shopifyIntegrationId);

        Task RemoveWebhooks(string companyId, long shopifyIntegrationId);

        Task<ListResult<Webhook>> ListWebhooks(string companyId, long shopifyIntegrationId);

        Task CheckAllAbandonedCart();

        Task CheckAbandoned(string companyId, long shopifyIntegrationId);

        Task<ShopifyConfig> GetCompanyConfigByShopDomain(string shopDomain);

        bool VerifyMandatoryWebhookRequest<T>(out T classObj, IHeaderDictionary headers, string body);

        Task<bool> DeleteCustomerProfile(string shopDomain, long? customerId, string customerEmail);

        Task SendDataRequestEmail(CustomerDataRequestWebhook dataRequset);

        Task<ShopifyConfig> UpdateShopifyConfig(
            long shopifyConfigId,
            string companyId,
            UpdateShopifyConfigRequest updateShopifyConfigRequest);

        Task<ShopifyConfig> UpdateShopifySupportedCountry(
            long shopifyConfigId,
            string companyId,
            UpdateSupportedCountryRequest updateSupportedCountryRequest);

        Task<JToken> GetMultipleCurrencyAsync(string variantId, ShopifyConfig config);

        Task<List<ShopifyConfig>> UpdateCompanyShopifyDiscountSetting(string companyId, bool isEnabledDiscounts);

        Task<bool> IsCompanyShopifyDiscountsEnabled(string companyId);

        Task CreateShopifyProductMessageTemplateAsync(
            long shopifyConfigId,
            string companyId,
            string templateMessageBody,
            List<string> templateParams);

        Task UpdateShopifyProductMessageTemplateAsync(
            long shopifyProductMessageTemplateId,
            string companyId,
            string templateMessageBody,
            List<string> templateParams);

        Task<List<ShopifyProductMessageTemplate>> GetShopifyProductMessageTemplatesAsync(
            long shopifyConfigId,
            string companyId);

        Task<List<string>> GetShopifyProductCurrenciesAsync(
            long shopifyConfigId,
            string companyId);

        Task<List<ShopifyStripeCurrencyConnectionStatus>> GetShopifyStripeConnectionStatusAsync(
            long shopifyConfigId,
            string companyId);

        Task<Customer> GetOrCreateShopifyCustomer(
            long shopifyConfigId,
            string companyId,
            string phone,
            string email);
    }

    public class ShopifyService : IShopifyService
    {
        private readonly ApplicationDbContext _appDbContext;
        private readonly IMapper _mapper;
        private readonly IConfiguration _configuration;
        private readonly ILogger _logger;
        private readonly IUserProfileService _userProfileService;
        private readonly ICompanyService _companyService;
        private readonly ILockService _lockService;
        private readonly ICompanyInfoCacheService _companyInfoCacheService;

        private readonly IEmailNotificationService _emailService;

        private readonly IDefaultSubscriptionPlanIdGetter _defaultSubscriptionPlanIdGetter;

        public ShopifyService(
            ApplicationDbContext appDbContext,
            IMapper mapper,
            IConfiguration configuration,
            ILogger<ShopifyService> logger,
            IUserProfileService userProfileService,
            ICompanyService companyService,
            IEmailNotificationService emailService,
            ILockService lockService,
            ICompanyInfoCacheService companyInfoCacheService,
            IDefaultSubscriptionPlanIdGetter defaultSubscriptionPlanIdGetter)
        {
            _appDbContext = appDbContext;
            _mapper = mapper;
            _configuration = configuration;
            _logger = logger;
            _userProfileService = userProfileService;
            _companyService = companyService;
            _emailService = emailService;
            _lockService = lockService;
            _companyInfoCacheService = companyInfoCacheService;
            _defaultSubscriptionPlanIdGetter = defaultSubscriptionPlanIdGetter;
        }

        public async Task SendDataRequestEmail(CustomerDataRequestWebhook customerDataRequest)
        {
            var compConfig = await GetCompanyConfigByShopDomain(customerDataRequest.ShopDomain);

            if (compConfig != null)
            {
                var userProfile = (await _userProfileService.GetUserProfilesByFields(
                        compConfig.CompanyId,
                        new List<Condition>()
                        {
                            new Condition()
                            {
                                FieldName = "ShopifyCustomerId",
                                ConditionOperator = SupportedOperator.Equals,
                                Values = new List<string>
                                {
                                    customerDataRequest.Customer.Id.ToString()
                                },
                                NextOperator = SupportedNextOperator.And
                            },
                            new Condition()
                            {
                                FieldName = "Email",
                                ConditionOperator = SupportedOperator.Equals,
                                Values = new List<string>
                                {
                                    customerDataRequest.Customer.Email
                                }
                            }
                        })
                    ).UserProfiles.FirstOrDefault();

                if (userProfile != null)
                {
                    var service = new ShopService(
                        compConfig.UsersMyShopifyUrl,
                        compConfig.AccessToken);

                    var shopInfo = await service.GetAsync();

                    BackgroundJob.Enqueue<EmailNotificationService>(
                        b => b.SendShopifyDataRequest(shopInfo, userProfile));
                }
            }
        }

        public async Task<bool> DeleteCustomerProfile(string shopDomain, long? customerId, string customerEmail)
        {
            try
            {
                var compConfig = await GetCompanyConfigByShopDomain(shopDomain);

                if (compConfig != null)
                {
                    var redactUserProfiles = (await _userProfileService.GetUserProfilesByFields(
                            compConfig.CompanyId,
                            new List<Condition>()
                            {
                                new Condition()
                                {
                                    FieldName = "ShopifyCustomerId",
                                    ConditionOperator = SupportedOperator.Equals,
                                    Values = new List<string>
                                    {
                                        customerId.ToString()
                                    },
                                    NextOperator = SupportedNextOperator.And
                                },
                                new Condition()
                                {
                                    FieldName = "Email",
                                    ConditionOperator = SupportedOperator.Equals,
                                    Values = new List<string>
                                    {
                                        customerEmail
                                    }
                                }
                            })
                        ).UserProfiles.FirstOrDefault();

                    if (redactUserProfiles != null)
                    {
                        _logger.LogInformation(
                            "[{MethodName}] Company {CompanyId} is deleting Shopify user profile of email {UserProfileEmail} and shopify customer id {ShopifyCustomerId}",
                            nameof(DeleteCustomerProfile),
                            compConfig.CompanyId,
                            customerEmail,
                            customerId);

                        await _userProfileService.DeleteUserProfileLinked(
                            redactUserProfiles,
                            new UserProfileDeletionTriggerContext(
                                UpdateUserProfileTriggerSource.ShopifyRedact,
                                null));
                    }
                }

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogCritical(
                    ex,
                    "Shopify {MethodName} for {CustomerEmail} error: {ExceptionMessage}",
                    nameof(DeleteCustomerProfile),
                    customerEmail,
                    ex.Message);

                return false;
            }
        }

        public bool VerifyMandatoryWebhookRequest<T>(out T model, IHeaderDictionary headers, string rawBody)
        {
            string appsApiSecret = _configuration.GetValue<string>("Shopify:ShopifySecretKey");
            model = JsonConvert.DeserializeObject<T>(rawBody);

            return AuthorizationService.IsAuthenticWebhook(
                headers,
                rawBody,
                appsApiSecret);
        }

        public async Task<ShopifyConfig?> GetCompanyConfigByShopDomain(string domain)
        {
            var config = await _appDbContext.ConfigShopifyConfigs
                .FirstOrDefaultAsync(x => x.UsersMyShopifyUrl == domain);

            return config;
        }

        public async Task<ListResult<Webhook>> ListWebhooks(string companyId, long shopifyIntegrationId)
        {
            var config = await _appDbContext.ConfigShopifyConfigs
                .FirstOrDefaultAsync(
                    x =>
                        x.Id == shopifyIntegrationId
                        && x.CompanyId == companyId);

            var service = new WebhookService(config.UsersMyShopifyUrl, config.AccessToken);
            var domainName = _configuration.GetValue<String>("Values:DomainName");

            var result = await service.ListAsync();
            return result;
        }

        public async Task RemoveWebhooks(string companyId, long shopifyIntegrationId)
        {
            try
            {
                var config = await _appDbContext.ConfigShopifyConfigs
                    .FirstOrDefaultAsync(
                        x =>
                            x.Id == shopifyIntegrationId
                            && x.CompanyId == companyId);

                var service = new WebhookService(config.UsersMyShopifyUrl, config.AccessToken);
                var domainName = _configuration.GetValue<String>("Values:DomainName");

                var result = await service.ListAsync();

                foreach (var webhook in result.Items)
                {
                    await service.DeleteAsync(webhook.Id.Value);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Shopify {MethodName} error for company {CompanyId}: {ExceptionMessage}",
                    nameof(RemoveWebhooks),
                    companyId,
                    ex.Message);
            }
        }

        public async Task<Dictionary<string, List<string>>> PatchNewWebhookForAllCompanies()
        {
            Dictionary<string, List<string>> results = new ();

            List<ShopifyConfig> shopifyConfigs = await _appDbContext.ConfigShopifyConfigs.ToListAsync();

            foreach (ShopifyConfig config in shopifyConfigs)
            {
                string key = $"{config.CompanyId}#{config.Id}";
                results[key] = new ();

                try
                {
                    await RegisterNewWebhooks(config.CompanyId, config.Id);

                    ListResult<Webhook> registeredWebhooks = await ListWebhooks(config.CompanyId, config.Id);

                    results[key].AddRange(registeredWebhooks.Items.Select(i => $"{i.Topic} - {i.Address}"));
                }
                catch (Exception ex)
                {
                    _logger.LogError(
                        "Error patching new Shopify webhooks for company id: {CompanyId}, config id: {ShopifyConfigId}",
                        config.CompanyId,
                        config.Id);

                    results[key].Add(ex.Message);
                }
            }

            return results;
        }

        public async Task<Dictionary<string, List<string>>> RollbackWebhookForAllCompanies()
        {
            Dictionary<string, List<string>> results = new ();

            List<ShopifyConfig> shopifyConfigs = await _appDbContext.ConfigShopifyConfigs.ToListAsync();

            foreach (ShopifyConfig config in shopifyConfigs)
            {
                string key = $"{config.CompanyId}#{config.Id}";
                results[key] = new ();

                try
                {
                    await RegisterWebhook(config.CompanyId, config.Id);

                    ListResult<Webhook> registeredWebhooks = await ListWebhooks(config.CompanyId, config.Id);

                    results[key].AddRange(registeredWebhooks.Items.Select(i => $"{i.Topic} - {i.Address}"));
                }
                catch (Exception ex)
                {
                    _logger.LogError(
                        "Error rolling back Shopify webhooks for company id: {CompanyId}, config id: {ShopifyConfigId}",
                        config.CompanyId,
                        config.Id);

                    results[key].Add(ex.Message);
                }
            }

            return results;
        }

        public async Task RegisterWebhook(string companyId, long shopifyIntegrationId)
        {
            var config = await _appDbContext.ConfigShopifyConfigs
                .FirstOrDefaultAsync(
                    x =>
                        x.Id == shopifyIntegrationId
                        && x.CompanyId == companyId);

            var service = new WebhookService(config.UsersMyShopifyUrl, config.AccessToken);
            var domainName = _configuration.GetValue<String>("Values:DomainName");

            var result = await service.ListAsync();

            foreach (var webhook in result.Items)
            {
                await service.DeleteAsync(webhook.Id.Value);
            }

            Webhook hook = new Webhook()
            {
                Address = $"{domainName}/company/shopify/webhook/{config.CompanyId}/shopify/{config.Id}/orders",
                CreatedAt = DateTime.Now,
                Format = "json",
                Topic = "orders/create",
            };

            try
            {
                await service.CreateAsync(hook);
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Shopify {MethodName} {HookTopic} error for company {CompanyId}: {ExceptionMessage}",
                    nameof(RegisterWebhook),
                    hook.Topic,
                    companyId,
                    ex.Message);
            }

            /*
            hook = new ShopifySharp.Webhook()
            {
                Address = $"{domainName}/company/shopify/webhook/{config.CompanyId}/shopify/{config.Id}/orders",
                CreatedAt = DateTime.Now,
                Format = "json",
                Topic = "orders/cancelled",
            };
            try
            {
                await service.CreateAsync(hook);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, ex.Message);
            }
            hook = new ShopifySharp.Webhook()
            {
                Address = $"{domainName}/company/shopify/webhook/{config.CompanyId}/shopify/{config.Id}/orders",
                CreatedAt = DateTime.Now,
                Format = "json",
                Topic = "orders/fulfilled",
            };
            try
            {
                await service.CreateAsync(hook);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, ex.Message);
            }
            hook = new ShopifySharp.Webhook()
            {
                Address = $"{domainName}/company/shopify/webhook/{config.CompanyId}/shopify/{config.Id}/orders",
                CreatedAt = DateTime.Now,
                Format = "json",
                Topic = "orders/paid",
            };
            try
            {
                await service.CreateAsync(hook);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, ex.Message);
            }
            hook = new ShopifySharp.Webhook()
            {
                Address = $"{domainName}/company/shopify/webhook/{config.CompanyId}/shopify/{config.Id}/orders",
                CreatedAt = DateTime.Now,
                Format = "json",
                Topic = "orders/partially_fulfilled",
            };
            try
            {
                await service.CreateAsync(hook);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, ex.Message);
            } */

            hook = new ShopifySharp.Webhook()
            {
                Address = $"{domainName}/company/shopify/webhook/{config.CompanyId}/shopify/{config.Id}/orders",
                CreatedAt = DateTime.Now,
                Format = "json",
                Topic = "orders/updated",
            };

            try
            {
                await service.CreateAsync(hook);
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Shopify {MethodName} {HookTopic} error for company {CompanyId}: {ExceptionMessage}",
                    nameof(RegisterWebhook),
                    hook.Topic,
                    companyId,
                    ex.Message);
            }

            hook = new ShopifySharp.Webhook()
            {
                Address =
                    $"{domainName}/company/shopify/webhook/{config.CompanyId}/shopify/{config.Id}/customers?topic=create",
                CreatedAt = DateTime.Now,
                Format = "json",
                Topic = "customers/create",
            };

            try
            {
                await service.CreateAsync(hook);
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Shopify {MethodName} {HookTopic} error for company {CompanyId}: {ExceptionMessage}",
                    nameof(RegisterWebhook),
                    hook.Topic,
                    companyId,
                    ex.Message);
            }

            hook = new ShopifySharp.Webhook()
            {
                Address =
                    $"{domainName}/company/shopify/webhook/{config.CompanyId}/shopify/{config.Id}/customers?topic=create",
                CreatedAt = DateTime.Now,
                Format = "json",
                Topic = "customers/update",
            };

            try
            {
                await service.CreateAsync(hook);
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Shopify {MethodName} {HookTopic} error for company {CompanyId}: {ExceptionMessage}",
                    nameof(RegisterWebhook),
                    hook.Topic,
                    companyId,
                    ex.Message);
            }

            hook = new ShopifySharp.Webhook()
            {
                Address = $"{domainName}/company/shopify/webhook/{config.CompanyId}/shopify/{config.Id}/uninstalled",
                CreatedAt = DateTime.Now,
                Format = "json",
                Topic = "app/uninstalled",
            };

            try
            {
                await service.CreateAsync(hook);
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Shopify {MethodName} {HookTopic} error for company {CompanyId}: {ExceptionMessage}",
                    nameof(RegisterWebhook),
                    hook.Topic,
                    companyId,
                    ex.Message);
            }

            //hook = new ShopifySharp.Webhook()
            //{
            //    Address = $"{domainName}/company/shopify/webhook/{config.CompanyId}/shopify/{config.Id}/draftorders",
            //    CreatedAt = DateTime.Now,
            //    Format = "json",
            //    Topic = "draft_orders/create",
            // };
            // try
            // {
            //    await service.CreateAsync(hook);
            // }
            // catch (Exception ex)
            // {
            //    _logger.LogError(ex, ex.Message);
            // }

            // hook = new ShopifySharp.Webhook()
            // {
            //    Address = $"{domainName}/company/shopify/webhook/{condig.CompanyId}/shopify/{condig.Id}/draftorders",
            //    CreatedAt = DateTime.Now,
            //    Format = "json",
            //    Topic = "draft_orders/update",
            // };
            // try
            // {
            //    await service.CreateAsync(hook);
            // }
            // catch (Exception ex)
            // {
            //    _logger.LogError(ex, ex.Message);
            // }
        }

        public async Task RegisterNewWebhooks(string companyId, long shopifyIntegrationId)
        {
            ShopifyConfig shopifyConfig = await _appDbContext.ConfigShopifyConfigs
                .FirstOrDefaultAsync(
                    c =>
                        c.CompanyId.Equals(companyId) &&
                        c.Id.Equals(shopifyIntegrationId));

            if (shopifyConfig is null)
            {
                return;
            }

            // Remove all existing webhooks
            WebhookService webhookService = new (
                shopifyConfig.UsersMyShopifyUrl,
                shopifyConfig.AccessToken);

            ListResult<Webhook> existingWebhooks = await webhookService.ListAsync();

            foreach (Webhook webhook in existingWebhooks.Items.Where(i => i.Id.HasValue))
            {
                await webhookService.DeleteAsync(webhook.Id!.Value);
            }

            string webhookDomain = _configuration.GetValue<string>("Values:DomainName");

            // Register new webhooks
            List<Webhook> webhooksToRegister = ShopifyWebHookHelper.GetWebhooksToRegister(
                webhookDomain,
                companyId,
                shopifyIntegrationId);

            foreach (Webhook newWebhook in webhooksToRegister)
            {
                await webhookService.CreateAsync(newWebhook);
            }
        }

        public async Task UpsertOrders(
            string companyId,
            long shopifyIntegrationId,
            ShopifySharp.Order order,
            bool isOrderCreated,
            bool updateCustomField = true)
        {
            string orderChecksum = SHA256Helper.sha256_hash(JsonConvert.SerializeObject(order));

            if (await _lockService.AcquireLockAsync(
                    $"ShopifyOrder:{shopifyIntegrationId}:{order.Id}:{isOrderCreated}:{orderChecksum}",
                    TimeSpan.FromSeconds(15)) == null)
            {
                return;
            }

            var newProfileViewModel = new NewProfileViewModel
            {
                UserProfileFields = new List<AddCustomFieldsViewModel>()
            };

            var shopifyConfig = await _appDbContext.ConfigShopifyConfigs
                .FirstOrDefaultAsync(
                    x =>
                        x.CompanyId == companyId
                        && x.Id == shopifyIntegrationId);

            if (shopifyConfig == null)
            {
                return;
            }

            if (shopifyConfig.SyncOnlyIfPhoneNumberExist
                && string.IsNullOrEmpty(order.Customer?.Phone)
                && string.IsNullOrEmpty(order.Phone)
                && string.IsNullOrEmpty(order.Customer?.DefaultAddress?.Phone))
            {
                return;
            }

            if (!string.IsNullOrEmpty(order.Email))
            {
                newProfileViewModel.Email = order.Email;
            }

            if (!string.IsNullOrEmpty(order.Phone))
            {
                newProfileViewModel.PhoneNumber = order.Phone
                    .Replace(" ", string.Empty)
                    .Replace("-", string.Empty)
                    .Replace("(", string.Empty)
                    .Replace(")", string.Empty)
                    .Replace("+", string.Empty);
            }

            if (string.IsNullOrEmpty(newProfileViewModel.Email)
                && !string.IsNullOrEmpty(order.Customer?.Email))
            {
                newProfileViewModel.Email = order.Customer?.Email;
            }

            if (string.IsNullOrEmpty(newProfileViewModel.WhatsAppPhoneNumber)
                && !string.IsNullOrEmpty(order.Customer?.Phone))
            {
                newProfileViewModel.PhoneNumber = order.Customer?.Phone;
            }

            if (string.IsNullOrEmpty(newProfileViewModel.WhatsAppPhoneNumber)
                && !string.IsNullOrEmpty(order.Customer?.DefaultAddress?.Phone))
            {
                var phoneNumberUtil = PhoneNumbers.PhoneNumberUtil.GetInstance();

                try
                {
                    var phoneNumber = phoneNumberUtil.Parse(
                        $"+{order.Customer?.DefaultAddress?.Phone
                            .Replace(" ", string.Empty)
                            .Replace("-", string.Empty)
                            .Replace("(", string.Empty)
                            .Replace(")", string.Empty)
                            .Replace("+", string.Empty)}",
                        null);

                    var countCode = PhoneNumberHelper.GetCountryCode(phoneNumber);

                    if (countCode == order.Customer?.DefaultAddress?.CountryCode)
                    {
                        newProfileViewModel.PhoneNumber = order.Customer?.DefaultAddress?.Phone
                            .Replace(" ", string.Empty)
                            .Replace("-", string.Empty)
                            .Replace("(", string.Empty)
                            .Replace(")", string.Empty)
                            .Replace("+", string.Empty);
                    }
                    else
                    {
                        var countryCode = phoneNumberUtil.GetCountryCodeForRegion(
                            order.Customer?.DefaultAddress?.CountryCode);

                        newProfileViewModel.PhoneNumber =
                            $"{countryCode}" +
                            order.Customer?.DefaultAddress?.Phone
                                .Replace(" ", string.Empty)
                                .Replace("-", string.Empty)
                                .Replace("(", string.Empty)
                                .Replace(")", string.Empty)
                                .Replace("+", string.Empty);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(
                        ex,
                        "Company {CompanyId} Shopify {MethodName} error parsing default address phone {PhoneNumber} with country code {CountryCode}: {ExceptionMessage}",
                        companyId,
                        nameof(UpsertOrders),
                        order.Customer?.DefaultAddress?.Phone,
                        order.Customer?.DefaultAddress?.CountryCode,
                        ex.Message);

                    var countryCode = phoneNumberUtil.GetCountryCodeForRegion(
                        order.Customer?.DefaultAddress?.CountryCode);

                    newProfileViewModel.PhoneNumber =
                        $"{countryCode}" +
                        order.Customer?.DefaultAddress?.Phone
                            .Replace(" ", string.Empty)
                            .Replace("-", string.Empty)
                            .Replace("(", string.Empty)
                            .Replace(")", string.Empty)
                            .Replace("+", string.Empty);
                }
            }

            var myLineItems = _mapper.Map<List<MyShopifyLineItem>>(order.LineItems);

            // Filter only if order is not cancelled, otherwise myLineItems will empty after filtered
            if (!order.CancelledAt.HasValue)
            {
                myLineItems = myLineItems
                    .Where(
                        item =>
                            item.FulfillableQuantity > 0
                            || !string.IsNullOrWhiteSpace(item.FulfillmentStatus))
                    .ToList();
            }

            var service = new ProductService(
                shopifyConfig.UsersMyShopifyUrl,
                shopifyConfig.AccessToken);

            var product = await service.ListAsync(
                new ProductListFilter
                {
                    Ids = order.LineItems
                        .Where(x => x.ProductId.HasValue)
                        .Select(x => x.ProductId.Value)
                });

            if (shopifyConfig.SyncCustomerTags
                && !string.IsNullOrEmpty(order.Customer?.Tags))
            {
                newProfileViewModel.Labels = order.Customer.Tags
                    .Split(",", StringSplitOptions.TrimEntries)
                    .ToList();
            }

            var lineItems = string.Empty;

            foreach (var lineItem in myLineItems)
            {
                var item = product.Items
                    .FirstOrDefault(x => x.Id == lineItem.ProductId);

                if (item is not null)
                {
                    lineItem.ImageURL = item.Images.FirstOrDefault()?.Src;

                    if (shopifyConfig.SyncProductTags)
                    {
                        lineItem.Tags = item.Tags;
                    }
                }

                var itemFulfilledQuantity = order.Fulfillments?
                    .Where(f => !f.Status.Equals("cancelled", StringComparison.OrdinalIgnoreCase))
                    .SelectMany(f => f.LineItems)
                    .Where(
                        i =>
                            i.ProductId == lineItem.ProductId
                            && i.Id == lineItem.Id)
                    .Sum(i => i.Quantity) ?? 0;

                /*
                 * 1. For partial fulfilled,  lineItem.FulfillableQuantity still has value,
                 *    so lineItem.FulfillableQuantity + fulfilled quantity = total quantity
                 * 2. For fulfilled, lineItem.FulfillableQuantity is 0,
                 *    so 0 + fulfilled quantity = total quantity
                 * 3. For unfulfilled, lineItem.FulfillmentStatus is null. So
                 *    lineItem.FulfillableQuantity is the total quantity.
                 *
                 * If order is cancelled, lineItem.FulfillmentStatus is null and lineItem.FulfillableQuantity is 0,
                 * Just take the quantity will do. The order is cancelled as a whole.
                 */
                var lineItemTotalQuantity = !string.IsNullOrWhiteSpace(lineItem.FulfillmentStatus) ?
                    lineItem.FulfillableQuantity + itemFulfilledQuantity :
                    order.CancelledAt.HasValue
                        ? lineItem.Quantity
                        : lineItem.FulfillableQuantity;

                var perItemPrice = lineItem.Price;

                // Handle discount if any
                if (lineItem.TotalDiscount > 0
                    && lineItemTotalQuantity > 0)
                {
                    var discountPerItem = lineItem.TotalDiscount / lineItemTotalQuantity;
                    perItemPrice = lineItem.Price - discountPerItem;
                }

                lineItems +=
                    $"{lineItem.Name}:\n{order.Currency} {perItemPrice:N} x {lineItemTotalQuantity}  " +
                    $"{order.Currency} {perItemPrice * lineItemTotalQuantity:N}\n";
            }

            if (!string.IsNullOrEmpty(order.Customer?.FirstName))
            {
                newProfileViewModel.FirstName = order.Customer?.FirstName;
            }

            if (!string.IsNullOrEmpty(order.Customer?.LastName))
            {
                newProfileViewModel.LastName = order.Customer?.LastName;
            }

            var orderStatus = "open";

            if (order.CancelledAt.HasValue)
            {
                orderStatus = "cancelled";
            }

            if (order.ClosedAt.HasValue)
            {
                orderStatus = "closed";
            }

            long? customerLastOrderId = await TryGetCustomerLastOrderId(companyId, newProfileViewModel);

            if (isOrderCreated
                || (customerLastOrderId.HasValue
                    && order.Id == customerLastOrderId.Value))
            {
                newProfileViewModel.UserProfileFields.Add(
                    new AddCustomFieldsViewModel
                    {
                        CustomFieldName = "Last Order Number",
                        CustomValue = order.OrderNumber.Value.ToString()
                    });
                newProfileViewModel.UserProfileFields.Add(
                    new AddCustomFieldsViewModel
                    {
                        CustomFieldName = "Last Order Items",
                        CustomValue = lineItems
                    });
                newProfileViewModel.UserProfileFields.Add(
                    new AddCustomFieldsViewModel
                    {
                        CustomFieldName = "Last Order Amount",
                        CustomValue = $"{order.Currency} {order.CurrentTotalPrice:N}"
                    });
                newProfileViewModel.UserProfileFields.Add(
                    new AddCustomFieldsViewModel
                    {
                        CustomFieldName = "Last Order Currency",
                        CustomValue = order.Currency
                    });
                newProfileViewModel.UserProfileFields.Add(
                    new AddCustomFieldsViewModel
                    {
                        CustomFieldName = "Last Order Total Price",
                        CustomValue = $"{order.CurrentTotalPrice:N}"
                    });

                newProfileViewModel.UserProfileFields.Add(
                    new AddCustomFieldsViewModel
                    {
                        CustomFieldName = "Last Order Date",
                        CustomValue = order.CreatedAt.Value.UtcDateTime.ToString("o")
                    });

                if (order.UpdatedAt.HasValue)
                {
                    newProfileViewModel.UserProfileFields.Add(
                        new AddCustomFieldsViewModel
                        {
                            CustomFieldName = "Last Order Updated Date",
                            CustomValue = order.UpdatedAt.Value.UtcDateTime.ToString("o")
                        });
                }

                newProfileViewModel.UserProfileFields.Add(
                    new AddCustomFieldsViewModel
                    {
                        CustomFieldName = "Last Order Status",
                        CustomValue = orderStatus
                    });
                newProfileViewModel.UserProfileFields.Add(
                    new AddCustomFieldsViewModel
                    {
                        CustomFieldName = "Last Order Status URL",
                        CustomValue = order.OrderStatusUrl
                    });
                newProfileViewModel.UserProfileFields.Add(
                    new AddCustomFieldsViewModel
                    {
                        CustomFieldName = "Last Order Financial Status",
                        CustomValue = order.FinancialStatus
                    });
                newProfileViewModel.UserProfileFields.Add(
                    new AddCustomFieldsViewModel
                    {
                        CustomFieldName = "Last Order Fulfillment Status",
                        CustomValue = order.FulfillmentStatus
                    });
                newProfileViewModel.UserProfileFields.Add(
                    new AddCustomFieldsViewModel
                    {
                        CustomFieldName = "Last Order Remarks",
                        CustomValue = order.Note
                    });

                newProfileViewModel.UserProfileFields.Add(
                    new AddCustomFieldsViewModel
                    {
                        CustomFieldName = "Last Order Shipping Method",
                        CustomValue = order?.ShippingLines?.FirstOrDefault()?.Title
                    });
                newProfileViewModel.UserProfileFields.Add(
                    new AddCustomFieldsViewModel
                    {
                        CustomFieldName = "Last Order Tracking Company",
                        CustomValue = string.Join(
                            '\n',
                            order?.Fulfillments?
                                .Where(f => !f.Status.Equals("cancelled", StringComparison.OrdinalIgnoreCase))
                                .Select(f => f.TrackingCompany)
                                .Distinct() ?? Enumerable.Empty<string>())
                    });
                newProfileViewModel.UserProfileFields.Add(
                    new AddCustomFieldsViewModel
                    {
                        CustomFieldName = "Last Order Tracking URL",
                        CustomValue = string.Join(
                            '\n',
                            order?.Fulfillments?
                                .Where(f => !f.Status.Equals("cancelled", StringComparison.OrdinalIgnoreCase))
                                .SelectMany(f => f.TrackingUrls)
                                .Distinct() ?? Enumerable.Empty<string>())
                    });
                newProfileViewModel.UserProfileFields.Add(
                    new AddCustomFieldsViewModel
                    {
                        CustomFieldName = "Last Order Tracking Number",
                        CustomValue = string.Join(
                            '\n',
                            order?.Fulfillments?
                                .Where(f => !f.Status.Equals("cancelled", StringComparison.OrdinalIgnoreCase))
                                .SelectMany(f => f.TrackingNumbers)
                                .Distinct() ?? Enumerable.Empty<string>())
                    });
            }

            newProfileViewModel.UserProfileFields.Add(
                new AddCustomFieldsViewModel
                {
                    CustomFieldName = "LeadSource",
                    CustomValue = "Shopify"
                });

            if (order.Metafields != null)
            {
                foreach (var metafield in order.Metafields)
                {
                    if (!await _companyService.IsCustomUserProfileFieldExist(companyId, metafield.Key))
                    {
                        await _companyService.AddCustomUserProfileFields(
                            companyId,
                            metafield.Key,
                            FieldDataType.SingleLineText,
                            FieldsCategory.Shopify);
                    }

                    newProfileViewModel.UserProfileFields.Add(
                        new AddCustomFieldsViewModel
                        {
                            CustomFieldName = metafield.Key,
                            CustomValue = metafield.Value.ToString()
                        });
                }
            }

            try
            {
                var userProfiles = await _userProfileService.AddOrUpdateUserProfile(
                    companyId,
                    newProfileViewModel,
                    updateCustomField,
                    updateCustomField ? AutomationType.ShopifyNewOrUpdatedOrderTrigger : null,
                    HashTagType.Shopify);

                foreach (var userProfile in userProfiles)
                {
                    var existingOrder = await _appDbContext.UserProfileShopifyOrders
                        .FirstOrDefaultAsync(
                            x =>
                                x.CompanyId == userProfile.CompanyId
                                && x.UserProfileId == userProfile.Id
                                && x.OrderName == order.Name);

                    if (existingOrder is null)
                    {
                        existingOrder = new ShopifyOrderRecord
                        {
                            CompanyId = userProfile.CompanyId,
                            UserProfileId = userProfile.Id,
                            OrderName = order.Name
                        };

                        var stripePaymentRecord =
                            await _appDbContext.StripePaymentRecords
                                .FirstOrDefaultAsync(
                                    x =>
                                        x.CompanyId == companyId
                                        && x.ShopifyOrderId == order.Id);

                        if (stripePaymentRecord is not null)
                        {
                            existingOrder.PlatformCountry = stripePaymentRecord.PlatformCountry;
                        }

                        await _appDbContext.UserProfileShopifyOrders.AddAsync(existingOrder);
                    }

                    if (!userProfile.IsShopifyProfile)
                    {
                        userProfile.IsShopifyProfile = true;
                    }

                    if (!userProfile.ShopifyCustomerId.HasValue)
                    {
                        userProfile.ShopifyCustomerId = order.Customer?.Id;
                    }

                    existingOrder.CreatedAt = order.CreatedAt.Value.UtcDateTime;
                    existingOrder.URL = order.OrderStatusUrl;
                    existingOrder.OrderId = order.Id;
                    existingOrder.Status = orderStatus;
                    existingOrder.Fulfillment = order.FulfillmentStatus;
                    existingOrder.Note = order.Note;
                    existingOrder.Payment = order.FinancialStatus;
                    existingOrder.LineItems = myLineItems;

                    try
                    {
                        existingOrder.Fulfillments = order.Fulfillments.ToList();
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(
                            ex,
                            "Company {CompanyId} error setting 'Fulfillments' of existing Shopify order {ShopifyOrderId}",
                            companyId,
                            existingOrder.OrderId);
                    }

                    existingOrder.CartToken = order.CartToken;
                    existingOrder.Currency = order.Currency;
                    existingOrder.CustomerId = order.Customer?.Id;

                    // Track order by staff
                    if (!string.IsNullOrEmpty(order.Note))
                    {
                        var match = Regex.Match(order.Note, "StaffId: (.*)");

                        if (match.Success)
                        {
                            // Add to sandbox
                            var staffIdString = match.Groups[1].Value;

                            try
                            {
                                existingOrder.StaffId = Int64.Parse(staffIdString);
                                existingOrder.ConversionStatus = ConversionStatus.InfluencedSalesBySleekFlow;
                            }
                            catch (Exception ex)
                            {
                                _logger.LogError(
                                    ex,
                                    "Error processing company {CompanyId} Shopify order note, Order ID {OrderId}, Message: {ExceptionMessage}",
                                    companyId,
                                    order.Id,
                                    ex.Message);
                            }
                        }
                    }

                    if (string.IsNullOrEmpty(shopifyConfig.Currency))
                    {
                        shopifyConfig.Currency = order.Currency;
                    }

                    if (order.CurrentTotalPrice.HasValue)
                    {
                        existingOrder.TotalPrice = order.CurrentTotalPrice.Value;
                        existingOrder.TotalLineItemsPrice = order.CurrentTotalPrice.Value;
                    }

                    if (order.CurrentTotalDiscounts.HasValue)
                    {
                        existingOrder.TotalDiscounts = order.CurrentTotalDiscounts.Value;
                    }

                    if (order.CurrentTotalTax.HasValue)
                    {
                        existingOrder.TotalTax = order.CurrentTotalTax.Value;
                    }

                    if (order.CurrentSubtotalPrice.HasValue)
                    {
                        existingOrder.SubtotalPrice = order.CurrentSubtotalPrice.Value;
                    }

                    if (shopifyConfig.SyncOrderTags)
                    {
                        existingOrder.Tags = order.Tags;
                    }

                    if (await _appDbContext.UserProfileShopifyAbandonedCarts
                            .AnyAsync(
                                x =>
                                    x.CompanyId == companyId
                                    && x.CartToken == order.CartToken))
                    {
                        // Converted from abandoned cart
                        // Remove abandoned cart
                        var abandonedCart = await _appDbContext.UserProfileShopifyAbandonedCarts
                            .FirstOrDefaultAsync(
                                x =>
                                    x.CompanyId == companyId
                                    && x.CartToken == order.CartToken);

                        abandonedCart.StaffId = existingOrder.StaffId;

                        // Check if triggered automkation after the abandoned cart created
                        var convertedFromAutomation = await _appDbContext.CompanyAutomationHistories
                            .AnyAsync(
                                x =>
                                    x.TargetUserProfileId == userProfile.Id
                                    && x.CreatedAt > abandonedCart.Date
                                    && x.CreatedAt < order.CreatedAt);

                        if (!convertedFromAutomation)
                        {
                            var shopifyOrderConversion = 7;

                            convertedFromAutomation = await _appDbContext.ConversationMessages
                                .AnyAsync(
                                    x =>
                                        x.Conversation.UserProfileId == userProfile.Id
                                        && x.CreatedAt < order.CreatedAt
                                        && order.CreatedAt.Value.AddDays(-shopifyOrderConversion) < x.CreatedAt);
                        }

                        if (convertedFromAutomation)
                        {
                            var conversation = await _appDbContext.Conversations
                                .FirstOrDefaultAsync(x => x.UserProfileId == userProfile.Id);

                            if (conversation != null)
                            {
                                existingOrder.TeamId = conversation.AssignedTeamId;
                                abandonedCart.TeamId = conversation.AssignedTeamId;

                                if (!existingOrder.StaffId.HasValue
                                    && conversation.AssigneeId.HasValue)
                                {
                                    existingOrder.StaffId = conversation.AssigneeId;
                                    abandonedCart.StaffId = conversation.AssigneeId;
                                }
                            }
                        }

                        abandonedCart.Status = convertedFromAutomation
                            ? ConversionStatus.InfluencedSalesBySleekFlow
                            : ConversionStatus.Converted;

                        abandonedCart.RecoveredDate = order.CreatedAt.Value.UtcDateTime;
                        abandonedCart.Currency = order.Currency;
                        abandonedCart.TotalPrice = existingOrder.TotalPrice;
                        abandonedCart.TotalDiscounts = existingOrder.TotalDiscounts;
                        abandonedCart.TotalLineItemsPrice = existingOrder.TotalLineItemsPrice;
                        abandonedCart.TotalTax = existingOrder.TotalTax;
                        abandonedCart.SubtotalPrice = existingOrder.SubtotalPrice;

                        if (existingOrder.ConversionStatus != ConversionStatus.InfluencedSalesBySleekFlow)
                        {
                            existingOrder.ConversionStatus = abandonedCart.Status;
                        }

                        await _userProfileService.UpdateUserProfileCustomFields(
                            companyId,
                            userProfile.Id,
                            null,
                            new List<AddCustomFieldsViewModel>
                            {
                                new AddCustomFieldsViewModel
                                {
                                    CustomFieldName = "Abandoned Cart Items",
                                    CustomValue = string.Empty
                                },
                                new AddCustomFieldsViewModel
                                {
                                    CustomFieldName = "Abandoned Cart Amount",
                                    CustomValue = string.Empty
                                },
                                new AddCustomFieldsViewModel
                                {
                                    CustomFieldName = "Abandoned Cart Date",
                                    CustomValue = string.Empty
                                },
                                new AddCustomFieldsViewModel
                                {
                                    CustomFieldName = "Abandoned Cart URL",
                                    CustomValue = string.Empty
                                },
                                new AddCustomFieldsViewModel
                                {
                                    CustomFieldName = "Abandoned Cart Total Amount",
                                    CustomValue = string.Empty
                                },
                                new AddCustomFieldsViewModel
                                {
                                    CustomFieldName = "Abandoned Cart Currency",
                                    CustomValue = string.Empty
                                }
                            },
                            false);
                    }
                    else
                    {
                        var shopifyOrderConversionChat = 7;

                        var isTalkedToSleekFlowWithInTheConversationWindows =
                            await _appDbContext.ConversationMessages
                                .AnyAsync(
                                    x =>
                                        x.Conversation.UserProfileId == userProfile.Id
                                        && x.CreatedAt < order.CreatedAt
                                        && order.CreatedAt.Value.AddDays(-shopifyOrderConversionChat) < x.CreatedAt);

                        if (isTalkedToSleekFlowWithInTheConversationWindows)
                        {
                            var conversation = await _appDbContext.Conversations
                                .FirstOrDefaultAsync(x => x.UserProfileId == userProfile.Id);

                            if (conversation != null)
                            {
                                existingOrder.TeamId = conversation.AssignedTeamId;

                                if (!existingOrder.StaffId.HasValue
                                    && conversation.AssigneeId.HasValue)
                                {
                                    existingOrder.StaffId = conversation.AssigneeId;
                                }
                            }

                            existingOrder.ConversionStatus = ConversionStatus.InfluencedSalesBySleekFlow;
                        }
                    }

                    if (existingOrder.Tags != null)
                    {
                        if (existingOrder.Tags.Contains("ShopifyCheckoutLink"))
                        {
                            existingOrder.ConversionStatus = ConversionStatus.ShopifyCheckoutLink;
                        }

                        if (existingOrder.Tags.Contains("SleekFlow"))
                        {
                            existingOrder.ConversionStatus = ConversionStatus.SleekPay;
                        }
                    }

                    if (order.UpdatedAt.HasValue)
                    {
                        existingOrder.UpdatedAt = order.UpdatedAt.Value.UtcDateTime;
                    }

                    await _appDbContext.SaveChangesAsync();
                }
            }
            catch (FormatException fex)
            {
                _logger.LogError(
                    fex,
                    "Company {CompanyId} Shopify {MethodName} FormatException: {ExceptionMessage}, payload: {Payload}",
                    companyId,
                    nameof(UpsertOrders),
                    fex.Message,
                    JsonConvert.SerializeObject(order));
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Company {CompanyId} Shopify {MethodName} cannot save order to db {ExceptionMessage}, payload: {Payload}",
                    companyId,
                    nameof(UpsertOrders),
                    ex.Message,
                    JsonConvert.SerializeObject(order));
            }
        }

        private async Task<long?> TryGetCustomerLastOrderId(string companyId, NewProfileViewModel newProfileViewModel)
        {
            long? customerLastOrderId = default;
            string userProfileId = string.Empty;

            // Try to match by phone number
            if (!string.IsNullOrWhiteSpace(newProfileViewModel?.WhatsAppPhoneNumber))
            {
                userProfileId = await _appDbContext.UserProfiles
                    .Where(
                        x =>
                            x.PhoneNumber == newProfileViewModel.WhatsAppPhoneNumber
                                 .Replace(" ", string.Empty)
                                 .Replace("-", string.Empty)
                                 .Replace("(", string.Empty)
                                 .Replace(")", string.Empty)
                                 .Replace("+", string.Empty)
                            && x.CompanyId == companyId)
                    .Select(x => x.Id)
                    .FirstOrDefaultAsync();
            }

            // When there is no match by phone number or when phone number is not available,
            // try to match by email instead
            if (string.IsNullOrWhiteSpace(userProfileId) &&
                !string.IsNullOrWhiteSpace(newProfileViewModel?.Email))
            {
                userProfileId = await _appDbContext.UserProfiles
                    .Where(
                        x => x.Email.ToLower() == newProfileViewModel.Email.ToLower() &&
                             x.CompanyId == companyId)
                    .Select(x => x.Id)
                    .FirstOrDefaultAsync();
            }

            // There is match, try retrieve customer's last order
            if (!string.IsNullOrWhiteSpace(userProfileId))
            {
                var lastOrder = await _appDbContext.UserProfileShopifyOrders
                    .Where(
                        x =>
                            x.CompanyId == companyId
                            && x.UserProfileId == userProfileId)
                    .OrderByDescending(x => x.CreatedAt)
                    .Take(1)
                    .FirstOrDefaultAsync();

                customerLastOrderId = lastOrder?.OrderId;
            }

            return customerLastOrderId;
        }

        public async Task CheckoutUpdated(string companyId, long shopifyIntegrationId, ShopifyCheckout checkout)
        {
            var newProfileViewModel = new NewProfileViewModel();
            newProfileViewModel.UserProfileFields = new List<AddCustomFieldsViewModel>();

            if (!string.IsNullOrEmpty(checkout.Email))
            {
                newProfileViewModel.Email = checkout.Email;
            }

            if (string.IsNullOrEmpty(newProfileViewModel.Email)
                && !string.IsNullOrEmpty(checkout.Customer?.Email))
            {
                newProfileViewModel.Email = checkout.Customer?.Email;
            }

            if (string.IsNullOrEmpty(newProfileViewModel.WhatsAppPhoneNumber)
                && !string.IsNullOrEmpty(checkout.Customer?.Phone))
            {
                newProfileViewModel.PhoneNumber = checkout.Customer?.Phone
                    .Replace(" ", string.Empty)
                    .Replace("-", string.Empty)
                    .Replace("(", string.Empty)
                    .Replace(")", string.Empty)
                    .Replace("+", string.Empty);
            }

            if (string.IsNullOrEmpty(newProfileViewModel.WhatsAppPhoneNumber)
                && !string.IsNullOrEmpty(checkout.Customer?.DefaultAddress?.Phone))
            {
                var phoneNumberUtil = PhoneNumbers.PhoneNumberUtil.GetInstance();
                try
                {
                    var phonenumber = phoneNumberUtil.Parse(
                        $"+{checkout.Customer?.DefaultAddress?.Phone
                            .Replace(" ", string.Empty)
                            .Replace("-", string.Empty)
                            .Replace("(", string.Empty)
                            .Replace(")", string.Empty)
                            .Replace("+", string.Empty)}",
                        null);

                    var countCode = PhoneNumberHelper.GetCountryCode(phonenumber);

                    if (countCode == checkout.Customer?.DefaultAddress?.CountryCode)
                    {
                        newProfileViewModel.PhoneNumber = checkout.Customer?.DefaultAddress?.Phone
                            .Replace(" ", string.Empty)
                            .Replace("-", string.Empty)
                            .Replace("(", string.Empty)
                            .Replace(")", string.Empty)
                            .Replace("+", string.Empty);
                    }
                    else
                    {
                        var countryCode = phoneNumberUtil.GetCountryCodeForRegion(
                            checkout.Customer?.DefaultAddress?.CountryCode);

                        newProfileViewModel.PhoneNumber =
                            $"{countryCode}" +
                            checkout.Customer?.DefaultAddress?.Phone
                                .Replace(" ", string.Empty)
                                .Replace("-", string.Empty)
                                .Replace("(", string.Empty)
                                .Replace(")", string.Empty)
                                .Replace("+", string.Empty);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(
                        ex,
                        "[{MethodName}] Company {CompanyId} error parsing customer default address phone {PhoneNumber}. {ExceptionString}",
                        nameof(CheckoutUpdated),
                        companyId,
                        checkout?.Customer?.DefaultAddress?.Phone,
                        ex.ToString());

                    var countryCode = phoneNumberUtil.GetCountryCodeForRegion(
                        checkout.Customer?.DefaultAddress?.CountryCode);

                    newProfileViewModel.PhoneNumber =
                        $"{countryCode}" +
                        checkout.Customer?.DefaultAddress?.Phone
                            .Replace(" ", string.Empty)
                            .Replace("-", string.Empty)
                            .Replace("(", string.Empty)
                            .Replace(")", string.Empty)
                            .Replace("+", string.Empty);
                }
            }

            var shopifyConfig = await _appDbContext.ConfigShopifyConfigs
                .FirstOrDefaultAsync(
                    x =>
                        x.CompanyId == companyId
                        && x.Id == shopifyIntegrationId);

            var service = new ProductService(
                shopifyConfig.UsersMyShopifyUrl,
                shopifyConfig.AccessToken);

            var product = await service.ListAsync(
                new ProductListFilter
                {
                    Ids = checkout.LineItems
                        .Select(x => x.ProductId)
                });

            try
            {
                var myLineItems = checkout.LineItems;

                var lineItems = string.Empty;

                foreach (var lineItem in myLineItems)
                {
                    var item = product.Items
                        .FirstOrDefault(x => x.Id == lineItem.ProductId);

                    lineItem.ImageURL = item.Images.FirstOrDefault()?.Src;
                    lineItem.Tags = item.Tags;

                    lineItems +=
                        $"{lineItem.Title}:\n{checkout.Currency} {lineItem.Price} x {lineItem.Quantity}  {checkout.Currency} {lineItem.Price * lineItem.Quantity}\n";
                }

                // lineItems += $"\nTotal: {order.Currency} {order.TotalPrice}";
                if (!string.IsNullOrEmpty(checkout.Customer?.FirstName))
                {
                    newProfileViewModel.FirstName = checkout.Customer?.FirstName;
                }

                if (!string.IsNullOrEmpty(checkout.Customer?.LastName))
                {
                    newProfileViewModel.LastName = checkout.Customer?.LastName;
                }

                newProfileViewModel.UserProfileFields.Add(
                    new AddCustomFieldsViewModel
                    {
                        CustomFieldName = "Abandoned Cart Items",
                        CustomValue = lineItems
                    });
                newProfileViewModel.UserProfileFields.Add(
                    new AddCustomFieldsViewModel
                    {
                        CustomFieldName = "Abandoned Cart Amount",
                        CustomValue = $"{checkout.Currency} {checkout.TotalPrice}"
                    });
                newProfileViewModel.UserProfileFields.Add(
                    new AddCustomFieldsViewModel
                    {
                        CustomFieldName = "Abandoned Cart Currency",
                        CustomValue = checkout.Currency
                    });
                newProfileViewModel.UserProfileFields.Add(
                    new AddCustomFieldsViewModel
                    {
                        CustomFieldName = "Abandoned Cart Total Amount",
                        CustomValue = checkout.TotalPrice
                    });
                newProfileViewModel.UserProfileFields.Add(
                    new AddCustomFieldsViewModel
                    {
                        CustomFieldName = "Abandoned Cart Date",
                        CustomValue = checkout.CreatedAt.Value.UtcDateTime.ToString("o")
                    });
                if (checkout.UpdatedAt.HasValue)
                {
                    newProfileViewModel.UserProfileFields.Add(
                        new AddCustomFieldsViewModel
                        {
                            CustomFieldName = "Abandoned Cart Date",
                            CustomValue = checkout.UpdatedAt.Value.UtcDateTime.ToString("o")
                        });
                }

                newProfileViewModel.UserProfileFields.Add(
                    new AddCustomFieldsViewModel
                    {
                        CustomFieldName = "Abandoned Cart URL",
                        CustomValue = checkout.AbandonedCheckoutUrl
                    });

                newProfileViewModel.UserProfileFields.Add(
                    new AddCustomFieldsViewModel
                    {
                        CustomFieldName = "Last Order Remarks",
                        CustomValue = checkout.Note
                    });
                newProfileViewModel.UserProfileFields.Add(
                    new AddCustomFieldsViewModel
                    {
                        CustomFieldName = "LeadSource",
                        CustomValue = "Shopify"
                    });

                var userProfiles = await _userProfileService.AddOrUpdateUserProfile(
                    companyId,
                    newProfileViewModel,
                    true,
                    AutomationType.ShopifyNewAbandonedCart,
                    HashTagType.Shopify);

                try
                {
                    foreach (var userProfile in userProfiles)
                    {
                        var existingCart = await _appDbContext.UserProfileShopifyAbandonedCarts
                            .FirstOrDefaultAsync(
                                x =>
                                    x.CompanyId == userProfile.CompanyId
                                    && x.CartToken == checkout.CartToken);

                        if (existingCart == null)
                        {
                            existingCart = new ShopifyAbandonedCart
                            {
                                CompanyId = userProfile.CompanyId,
                                UserProfileId = userProfile.Id,
                                CartToken = checkout.CartToken
                            };

                            await _appDbContext.UserProfileShopifyAbandonedCarts.AddAsync(existingCart);
                        }

                        existingCart.Date = checkout.UpdatedAt.Value.UtcDateTime;
                        existingCart.AbandonedURL = checkout.AbandonedCheckoutUrl;
                        existingCart.LineItems = myLineItems;
                        existingCart.Currency = checkout.Currency;
                        existingCart.TotalPrice = ConvertToDecimal(checkout.TotalPrice);
                        existingCart.TotalDiscounts = ConvertToDecimal(checkout.TotalDiscounts);
                        existingCart.TotalLineItemsPrice = ConvertToDecimal(checkout.TotalLineItemsPrice);
                        existingCart.TotalTax = ConvertToDecimal(checkout.TotalTax);
                        existingCart.SubtotalPrice = ConvertToDecimal(checkout.SubtotalPrice);
                        existingCart.CustomerId = checkout.Customer?.Id;

                        if (!userProfile.IsShopifyProfile)
                        {
                            userProfile.IsShopifyProfile = true;
                        }

                        if (!userProfile.ShopifyCustomerId.HasValue)
                        {
                            userProfile.ShopifyCustomerId = checkout.Customer?.Id;
                        }

                        await _appDbContext.SaveChangesAsync();
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(
                        ex,
                        "[{MethodName}] Company {CompanyId} cannot save order to db {ExceptionMessage}, Checkout payload: {Payload}",
                        nameof(CheckoutUpdated),
                        companyId,
                        ex.Message,
                        JsonConvert.SerializeObject(checkout));
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[{MethodName}] Company {CompanyId} error: {ExceptionMessage}, Checkout payload: {Payload}",
                    nameof(CheckoutUpdated),
                    companyId,
                    ex.Message,
                    JsonConvert.SerializeObject(checkout));
            }
        }

        public async Task CustomerUpdated(
            string companyId,
            long shopifyIntegrationId,
            ShopifySharp.Customer customer,
            AutomationType? shopifyTrigger = AutomationType.ShopifyNewCustomerTrigger)
        {
            var newProfileViewModel = new NewProfileViewModel();
            newProfileViewModel.UserProfileFields = new List<AddCustomFieldsViewModel>();

            var shopifyConfig = await _appDbContext.ConfigShopifyConfigs
                .FirstOrDefaultAsync(
                    x =>
                        x.CompanyId == companyId
                        && x.Id == shopifyIntegrationId);

            if (shopifyConfig == null)
            {
                return;
            }

            if (shopifyConfig.SyncOnlyIfPhoneNumberExist
                && string.IsNullOrEmpty(customer.Phone)
                && string.IsNullOrEmpty(customer.DefaultAddress?.Phone))
            {
                return;
            }

            if (!string.IsNullOrEmpty(customer.Email))
            {
                newProfileViewModel.Email = customer.Email;
            }

            if (!string.IsNullOrEmpty(customer.Phone))
            {
                newProfileViewModel.PhoneNumber = customer.Phone
                    .Replace(" ", string.Empty)
                    .Replace("-", string.Empty)
                    .Replace("(", string.Empty)
                    .Replace(")", string.Empty)
                    .Replace("+", string.Empty);
            }

            if (string.IsNullOrEmpty(customer.Phone)
                && !string.IsNullOrEmpty(customer.DefaultAddress?.Phone))
            {
                var phoneNumberUtil = PhoneNumbers.PhoneNumberUtil.GetInstance();

                try
                {
                    var phoneNumber = phoneNumberUtil.Parse(
                        $"+{customer.DefaultAddress.Phone
                            .Replace(" ", string.Empty)
                            .Replace("-", string.Empty)
                            .Replace("(", string.Empty)
                            .Replace(")", string.Empty)
                            .Replace("+", string.Empty)}",
                        null);

                    var countCode = PhoneNumberHelper.GetCountryCode(phoneNumber);

                    if (countCode == customer.DefaultAddress?.CountryCode)
                    {
                        newProfileViewModel.PhoneNumber = customer.DefaultAddress.Phone
                            .Replace(" ", string.Empty)
                            .Replace("-", string.Empty)
                            .Replace("(", string.Empty)
                            .Replace(")", string.Empty)
                            .Replace("+", string.Empty);
                    }
                    else
                    {
                        var countryCode = phoneNumberUtil.GetCountryCodeForRegion(
                            customer.DefaultAddress?.CountryCode);

                        newProfileViewModel.PhoneNumber =
                            $"{countryCode}" +
                            customer.DefaultAddress.Phone
                                .Replace(" ", string.Empty)
                                .Replace("-", string.Empty)
                                .Replace("(", string.Empty)
                                .Replace(")", string.Empty)
                                .Replace("+", string.Empty);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(
                        ex,
                        "[{MethodName}] error when parsing default address phone number: {ExceptionMessage}, CompanyId: {CompanyId}, Contact phone number: {PhoneNumber}, Shopify Integration Id: {ShopifyIntegrationId}",
                        nameof(CustomerUpdated),
                        ex.Message,
                        companyId,
                        customer.Phone,
                        shopifyIntegrationId);

                    var countryCode = phoneNumberUtil.GetCountryCodeForRegion(customer.DefaultAddress?.CountryCode);
                    newProfileViewModel.PhoneNumber = $"{countryCode}" + customer.DefaultAddress.Phone
                        .Replace(" ", string.Empty)
                        .Replace("-", string.Empty).Replace("(", string.Empty).Replace(")", string.Empty)
                        .Replace("+", string.Empty);
                }
            }

            newProfileViewModel.FirstName = customer.FirstName;
            newProfileViewModel.LastName = customer.LastName;

            if (shopifyConfig.SyncCustomerTags
                && !string.IsNullOrEmpty(customer.Tags))
            {
                newProfileViewModel.Labels = customer.Tags
                    .Split(",", StringSplitOptions.TrimEntries)
                    .ToList();
            }

            newProfileViewModel.UserProfileFields.Add(
                new AddCustomFieldsViewModel
                {
                    CustomFieldName = "LeadSource",
                    CustomValue = "Shopify"
                });

            try
            {
                var userProfiles = await _userProfileService.AddOrUpdateUserProfile(
                    companyId,
                    newProfileViewModel,
                    true,
                    shopifyTrigger,
                    HashTagType.Shopify);

                foreach (var userProfile in userProfiles)
                {
                    userProfile.ShopifyCustomerId = customer?.Id;
                    await _appDbContext.SaveChangesAsync();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Company {CompanyId} Shopify {MethodName} error when attempting {OperationName}: {ExceptionMessage}",
                    companyId,
                    nameof(CustomerUpdated),
                    nameof(UserProfileService.AddOrUpdateUserProfile),
                    ex.Message);
            }
        }

        public async Task AppUninstalled(string companyId, long shopifyIntegrationId)
        {
            var shopifyConfig = await _appDbContext.ConfigShopifyConfigs
                .FirstOrDefaultAsync(
                    x =>
                        x.CompanyId == companyId
                        && x.Id == shopifyIntegrationId);

            if (shopifyConfig is null)
            {
                return;
            }

            // Shopify deletes subscription update webhooks after app uninstallation
            // We have to cancel the active bill record manually for billing owner
            // Billing owner has Shopify ChargeId
            if (shopifyConfig.ChargeId.HasValue)
            {
                var activeBillRecords = await _appDbContext.CompanyBillRecords
                    .Where(
                        x =>
                            x.CompanyId == shopifyConfig.CompanyId
                            && x.Status == BillStatus.Active)
                    .ToListAsync();

                var chargedBillRecord = activeBillRecords
                    .FirstOrDefault(x => x.ShopifyChargeId == shopifyConfig.ChargeId);

                if (chargedBillRecord is not null)
                {
                    chargedBillRecord.Status = BillStatus.Canceled;
                    chargedBillRecord.PayAmount = 0;
                    chargedBillRecord.amount_due = 0;
                    chargedBillRecord.amount_paid = 0;

                    if (!activeBillRecords.Any(x => SubscriptionPlansId.FreemiumStartupPlans.ContainsIgnoreCase(x.SubscriptionPlanId)))
                    {
                        // Create a new bill record with freemium plan if there is no existing active freemium plan
                        // This indicates that the user will revert to the freemium plan once their subscription is canceled
                        var freemiumBillRecord = new BillRecord()
                        {
                            CompanyId = shopifyConfig.CompanyId,
                            SubscriptionPlanId = _defaultSubscriptionPlanIdGetter.GetDefaultStartupPlanId(),
                            Status = BillStatus.Active,
                            PaymentStatus = PaymentStatus.FreeOfCharge,
                            PayAmount = 0,
                            currency = chargedBillRecord.currency,
                            PeriodStart = DateTime.UtcNow,
                            PeriodEnd = DateTime.UtcNow.AddMonths(1)
                        };

                        await _appDbContext.CompanyBillRecords.AddAsync(freemiumBillRecord);
                    }
                }
            }

            await _companyInfoCacheService.RemoveCompanyInfoCache(companyId);
            _appDbContext.ConfigShopifyConfigs.Remove(shopifyConfig);

            await _appDbContext.SaveChangesAsync();
        }

        public async Task SyncShopify(string companyId, long shopifyIntegrationId)
        {
            var shopifyConfig = await _appDbContext.ConfigShopifyConfigs
                .FirstOrDefaultAsync(
                    x =>
                        x.CompanyId == companyId
                        && x.Id == shopifyIntegrationId);

            shopifyConfig.Status = ShopifyStatus.Syncing;

            await _appDbContext.SaveChangesAsync();

            await SyncOrders(companyId, shopifyIntegrationId);
            await SyncCustomers(companyId, shopifyIntegrationId);

            shopifyConfig.Status = ShopifyStatus.Connected;
            shopifyConfig.LastSyncOrderAt = null;
            shopifyConfig.LastSyncCustomerAt = null;

            await _appDbContext.SaveChangesAsync();
        }

        public async Task SyncOrders(string companyId, long shopifyIntegrationId)
        {
            var config = await _appDbContext.ConfigShopifyConfigs
                .FirstOrDefaultAsync(
                    x =>
                        x.Id == shopifyIntegrationId
                        && x.CompanyId == companyId);

            var service = new OrderService(
                config.UsersMyShopifyUrl,
                config.AccessToken);

            var orders = await service.ListAsync(
                new OrderListFilter
                {
                    Limit = 250,
                    Status = "open",
                    CreatedAtMax = config.LastSyncOrderAt
                });

            while (true)
            {
                if (orders.Items.Any())
                {
                    foreach (var item in orders.Items.Reverse())
                    {
                        try
                        {
                            await UpsertOrders(
                                companyId,
                                shopifyIntegrationId,
                                item,
                                false,
                                false);
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(
                                ex,
                                "Company {CompanyId} Shopify sync order error: {ItemName} {ItemPhone} {ItemEmail} {ExceptionMessage}",
                                companyId,
                                item.Name,
                                item.Phone,
                                item.Email,
                                ex.Message);
                        }
                    }

                    config.LastSyncOrderAt = orders.Items.LastOrDefault().CreatedAt.Value.UtcDateTime;
                    await _appDbContext.SaveChangesAsync();
                }

                if (!orders.HasNextPage)
                {
                    break;
                }

                if (!await _appDbContext.ConfigShopifyConfigs
                        .AnyAsync(
                            x =>
                                x.Id == shopifyIntegrationId
                                && x.CompanyId == companyId))
                {
                    break;
                }

                orders = await service.ListAsync(orders.GetNextPageFilter());
            }
        }

        public async Task SyncCustomers(string companyId, long shopifyIntegrationId)
        {
            var config = await _appDbContext.ConfigShopifyConfigs
                .FirstOrDefaultAsync(
                    x =>
                        x.Id == shopifyIntegrationId
                        && x.CompanyId == companyId);

            var service = new CustomerService(
                config.UsersMyShopifyUrl,
                config.AccessToken);

            var customers = await service.ListAsync(
                new CustomerListFilter
                {
                    Limit = 50,
                    CreatedAtMax = config.LastSyncCustomerAt
                });

            while (true)
            {
                if (customers.Items.Count() > 0)
                {
                    foreach (var item in customers.Items)
                    {
                        try
                        {
                            await CustomerUpdated(
                                companyId,
                                shopifyIntegrationId,
                                item,
                                null);

                            await Task.Delay(5000);
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(
                                ex,
                                "Company {CompanyId} Shopify sync customer error: {ItemFirstName} {ItemPhone} {ItemEmail} {ExceptionMessage}",
                                companyId,
                                item.FirstName,
                                item.Phone,
                                item.Email,
                                ex.Message);
                        }
                    }

                    config.LastSyncCustomerAt = customers.Items.LastOrDefault().CreatedAt.Value.UtcDateTime;
                    await _appDbContext.SaveChangesAsync();
                }

                if (!customers.HasNextPage)
                {
                    break;
                }

                if (!await _appDbContext.ConfigShopifyConfigs
                        .AnyAsync(
                            x =>
                                x.Id == shopifyIntegrationId
                                && x.CompanyId == companyId))
                {
                    break;
                }

                customers = await service.ListAsync(customers.GetNextPageFilter());
            }
        }

        public async Task SyncProduct(string companyId, long shopifyIntegrationId)
        {
            var config = await _appDbContext.ConfigShopifyConfigs
                .FirstOrDefaultAsync(
                    x =>
                        x.Id == shopifyIntegrationId
                        && x.CompanyId == companyId);

            var service = new ProductService(
                config.UsersMyShopifyUrl,
                config.AccessToken);

            // Current inventoried product IDs
            var invalidProductIds = await _appDbContext.ShopifyProductRecords
                .Where(
                    x =>
                        x.CompanyId == companyId
                        && x.ShopifyId == shopifyIntegrationId)
                .Select(x => x.ProductId)
                .ToListAsync();

            var productListFilter = new ProductListFilter()
            {
                Limit = 250,
                Status = "active"
            };

            // Add all active product & remove deleted/archived product
            var listResult = await service.ListAsync(productListFilter);

            await SaveProductToDatabase(
                companyId,
                shopifyIntegrationId,
                config,
                _mapper.Map<List<ShopifyProduct>>(
                    listResult.Items.ToList()));

            invalidProductIds.RemoveAll(x => listResult.Items.Any(y => y.Id == x));

            while (listResult.HasNextPage)
            {
                var nextFilter = listResult.GetNextPageFilter();

                listResult = await service.ListAsync(nextFilter);

                await SaveProductToDatabase(
                    companyId,
                    shopifyIntegrationId,
                    config,
                    _mapper.Map<List<ShopifyProduct>>(
                        listResult.Items.ToList()));

                invalidProductIds.RemoveAll(x => listResult.Items.Any(y => y.Id == x));
            }

            // Add collection
            var collectionServices = new CustomCollectionService(
                config.UsersMyShopifyUrl,
                config.AccessToken);

            var customCollectionListFilter = new CustomCollectionListFilter()
            {
                Limit = 250
            };

            var collectionResult = await collectionServices.ListAsync(
                customCollectionListFilter);

            await SaveCustomCollectionToDatabase(
                companyId,
                config,
                _mapper.Map<List<ShopifyCollection>>(
                    collectionResult.Items.ToList()));

            while (collectionResult.HasNextPage)
            {
                var nextFilter = collectionResult.GetNextPageFilter();

                collectionResult = await collectionServices.ListAsync(nextFilter);

                await SaveCustomCollectionToDatabase(
                    companyId,
                    config,
                    _mapper.Map<List<ShopifyCollection>>(
                        collectionResult.Items.ToList()));
            }

            var smartCollectionServices = new SmartCollectionService(
                config.UsersMyShopifyUrl,
                config.AccessToken);

            var smartCollectionListFilter = new SmartCollectionListFilter()
            {
                Limit = 250
            };

            var smartCollectionResult = await smartCollectionServices.ListAsync(
                smartCollectionListFilter);

            await SaveCustomCollectionToDatabase(
                companyId,
                config,
                _mapper.Map<List<ShopifyCollection>>(
                    smartCollectionResult.Items.ToList()));

            while (smartCollectionResult.HasNextPage)
            {
                var nextFilter = smartCollectionResult.GetNextPageFilter();

                smartCollectionResult = await smartCollectionServices.ListAsync(nextFilter);

                await SaveCustomCollectionToDatabase(
                    companyId,
                    config,
                    _mapper.Map<List<ShopifyCollection>>(
                        smartCollectionResult.Items.ToList()));
            }

            // Remove Inactive product
            await _appDbContext.ShopifyCollectionProductRecords
                .Where(
                    x =>
                        x.ProductRecord.CompanyId == companyId
                        && x.ProductRecord.ShopifyId == shopifyIntegrationId
                        && invalidProductIds.Contains(x.ProductRecord.ProductId))
                .ExecuteDeleteAsync();

            await _appDbContext.ShopifyProductRecords
                .Where(
                    x =>
                        x.CompanyId == companyId
                        && x.ShopifyId == shopifyIntegrationId
                        && invalidProductIds.Contains(x.ProductId))
                .ExecuteDeleteAsync();
        }

        public async Task SaveCustomCollectionToDatabase(
            string companyId,
            ShopifyConfig config,
            List<ShopifyCollection> collections)
        {
            var service = new ProductService(
                config.UsersMyShopifyUrl,
                config.AccessToken);

            foreach (var collection in collections)
            {
                var savedCollection = await _appDbContext.ShopifyCollectionRecords
                    .FirstOrDefaultAsync(
                        x =>
                            x.CompanyId == companyId
                            && x.ShopifyId == config.Id
                            && x.CollectionId == collection.Id);

                if (savedCollection == null)
                {
                    savedCollection = new ShopifyCollectionRecord()
                    {
                        CompanyId = companyId,
                        ShopifyId = config.Id,
                        CollectionId = collection.Id,
                        SortOrder = collection.SortOrder,
                        PublishedAt = collection.PublishedAt,
                        PublishedScope = collection.PublishedScope,
                        Title = collection.Title,
                        CollectionPayload = collection
                    };

                    await _appDbContext.ShopifyCollectionRecords.AddAsync(savedCollection);
                }

                savedCollection.CollectionId = collection.Id;
                savedCollection.SortOrder = collection.SortOrder;
                savedCollection.PublishedAt = collection.PublishedAt;
                savedCollection.PublishedScope = collection.PublishedScope;
                savedCollection.Title = collection.Title;
                savedCollection.CollectionPayload = collection;

                await _appDbContext.SaveChangesAsync();

                var productListFilter = new ProductListFilter()
                {
                    CollectionId = savedCollection.CollectionId,
                    Limit = 250,
                    Status = "active"
                };

                // Add all active product
                var listResult = await service.ListAsync(productListFilter);

                await LinkProductToCollection(
                    companyId,
                    config.Id,
                    savedCollection.Id,
                    listResult.Items.ToList());

                while (listResult.HasNextPage)
                {
                    var nextFilter = listResult.GetNextPageFilter();
                    listResult = await service.ListAsync(nextFilter);

                    await LinkProductToCollection(
                        companyId,
                        config.Id,
                        savedCollection.Id,
                        listResult.Items.ToList());
                }
            }

            await _appDbContext.SaveChangesAsync();
        }

        private async Task LinkProductToCollection(
            string companyId,
            long shopifyIntegrationId,
            long saveCollectionId,
            List<Product> products)
        {
            foreach (var product in products)
            {
                if (!await _appDbContext.ShopifyProductRecords
                        .AnyAsync(
                            x =>
                                x.CompanyId == companyId
                                && x.ShopifyId == shopifyIntegrationId
                                && x.ProductId == product.Id))
                {
                    continue;
                }

                var productId = await _appDbContext.ShopifyProductRecords
                    .Where(
                        x =>
                            x.CompanyId == companyId &&
                            x.ShopifyId == shopifyIntegrationId &&
                            x.ProductId == product.Id)
                    .Select(x => x.Id)
                    .FirstOrDefaultAsync();

                if (!await _appDbContext.ShopifyCollectionProductRecords
                        .AnyAsync(
                            x =>
                                x.ShopifyCollectionRecordId == saveCollectionId
                                && x.ProductRecordId == productId))
                {
                    await _appDbContext.ShopifyCollectionProductRecords.AddAsync(
                        new ShopifyCollectionProductRecord()
                        {
                            ShopifyCollectionRecordId = saveCollectionId,
                            ProductRecordId = productId
                        });
                }
            }

            await _appDbContext.SaveChangesAsync();
        }

        private async Task SaveProductToDatabase(
            string companyId,
            long shopifyId,
            ShopifyConfig config,
            List<ShopifyProduct> products)
        {
            foreach (var product in products)
            {
                var existing = await _appDbContext.ShopifyProductRecords
                    .FirstOrDefaultAsync(
                        x =>
                            x.CompanyId == companyId
                            && x.ShopifyId == shopifyId
                            && x.ProductId == product.Id);

                if (existing == null)
                {
                    existing = new ShopifyProductRecord()
                    {
                        CompanyId = companyId,
                        ShopifyId = shopifyId,
                        ProductId = product.Id,
                        Title = product.Title,
                        ProductPayload = product,
                    };

                    await _appDbContext.ShopifyProductRecords.AddAsync(existing);
                }

                existing.ProductId = product.Id;
                existing.Title = product.Title;
                existing.ProductPayload = product;
                existing.Sku = string.Join(",", product.Variants.Select(x => x.SKU));

                if (existing.Sku?.Length > 450)
                {
                    existing.Sku = existing.Sku.Substring(0, 449);
                }

                if (config.SupportedCountries == null
                    || !config.SupportedCountries.Any()
                    || !product.Variants.Any())
                {
                    continue;
                }

                // Add multiple currencies value to
                foreach (var productVariant in product.Variants)
                {
                    productVariant.MultipleCurrencies = new List<Currency>();

                    var multipleCurrencyResult = await GetMultipleCurrencyAsync(
                        product.Variants.FirstOrDefault()?.Id.ToString(),
                        config);

                    var productVariantMultipleVariant =
                        JObject.Parse(multipleCurrencyResult["productVariant"].ToJson());

                    foreach (var (key, value) in productVariantMultipleVariant)
                    {
                        var amount = value["price"]?.Value<decimal>("amount");
                        var currencyCode = value["price"]?.Value<string>("currencyCode");

                        if (string.IsNullOrEmpty(config.Currency))
                        {
                            config.Currency = currencyCode;
                            await _appDbContext.SaveChangesAsync();
                        }

                        if (amount != null)
                        {
                            productVariant.MultipleCurrencies.Add(
                                new Currency()
                                {
                                    Name = key,
                                    Amount = amount.Value,
                                    CurrencyCode = currencyCode
                                });
                        }
                    }
                }
            }

            await _appDbContext.SaveChangesAsync();
        }

        public async Task CheckAllAbandonedCart()
        {
            var shopifyIntegrations = await _appDbContext.ConfigShopifyConfigs
                .Where(x => x.Status != ShopifyStatus.Disconnected)
                .ToListAsync();

            foreach (var shopify in shopifyIntegrations)
            {
                try
                {
                    BackgroundJob.Enqueue<IShopifyService>(
                        x =>
                            x.CheckAbandoned(shopify.CompanyId, shopify.Id));
                }
                catch (Exception ex)
                {
                    _logger.LogError(
                        ex,
                        "Check abandoned cart error for {ShopifyCompanyId} {ShopifyId}",
                        shopify.CompanyId,
                        shopify.Id);
                }
            }
        }

        public async Task CheckAbandoned(string companyId, long shopifyIntegrationId)
        {
            var config = await _appDbContext.ConfigShopifyConfigs
                .FirstOrDefaultAsync(
                    x =>
                        x.Id == shopifyIntegrationId
                        && x.CompanyId == companyId);

            try
            {
                UriBuilder uriBuilder = new (config.UsersMyShopifyUrl)
                {
                    Scheme = Uri.UriSchemeHttps,
                    Port = -1,
                    Path = "admin/api/2024-01/checkouts.json"
                };

                var restClient = new RestClient(uriBuilder.Uri.AbsoluteUri);
                var request = new RestRequest(Method.GET);

                request.AddHeader("X-Shopify-Access-Token", config.AccessToken);
                request.AddParameter("limit", 250);

                if (config.LastUpdatedAt.HasValue)
                {
                    DateTime abandonedCartEarliestCreatedAt = DateTime.SpecifyKind(
                        config.LastUpdatedAt.Value,
                        DateTimeKind.Utc);

                    request.AddParameter(
                        "updated_at_min",
                        new DateTimeOffset(abandonedCartEarliestCreatedAt)
                            .ToString("yyyy-MM-ddTHH:mm:ss%K"));
                }

                IRestResponse<ShopifyCheckouts> response = await restClient.ExecuteAsync<ShopifyCheckouts>(request);
                var uploadFileResult = JsonConvert.DeserializeObject<ShopifyCheckouts>(response.Content);

                DateTime cutOff = DateTime.UtcNow;

                if (uploadFileResult.Checkouts != null
                    && uploadFileResult.Checkouts.Any())
                {
                    foreach (var item in uploadFileResult.Checkouts)
                    {
                        try
                        {
                            await CheckoutUpdated(
                                companyId,
                                shopifyIntegrationId,
                                item);
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(
                                ex,
                                "Company ID: {CompanyId} process {OperationName} error: {ExceptionString}",
                                companyId,
                                nameof(CheckoutUpdated),
                                ex.ToString());
                        }
                    }
                }

                config.LastUpdatedAt = cutOff;
                await _appDbContext.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Company ID: {CompanyId}. Check abandoned cart error: {Exception}",
                    companyId,
                    ex.ToString());
            }
        }

        public async Task<ShopifyConfig> UpdateShopifyConfig(
            long shopifyConfigId,
            string companyId,
            UpdateShopifyConfigRequest updateShopifyConfigRequest)
        {
            var shopifyConfig = await _appDbContext.ConfigShopifyConfigs
                .FirstOrDefaultAsync(
                    x =>
                        x.Id == shopifyConfigId
                        && x.CompanyId == companyId);

            if (shopifyConfig == null)
            {
                return null;
            }

            shopifyConfig.Name = updateShopifyConfigRequest.Name;
            shopifyConfig.IsShowInInbox = updateShopifyConfigRequest.IsShowInInbox;
            shopifyConfig.PaymentLinkSetting = updateShopifyConfigRequest.PaymentLinkSetting;

            await _appDbContext.SaveChangesAsync();

            return shopifyConfig;
        }

        public async Task<ShopifyConfig> UpdateShopifySupportedCountry(
            long shopifyConfigId,
            string companyId,
            UpdateSupportedCountryRequest updateSupportedCountryRequest)
        {
            var shopifyConfig = await _appDbContext.ConfigShopifyConfigs
                .FirstOrDefaultAsync(
                    x =>
                        x.Id == shopifyConfigId
                        && x.CompanyId == companyId);

            if (shopifyConfig == null)
            {
                return null;
            }

            shopifyConfig.SupportedCountries = updateSupportedCountryRequest.SupportedCountries;

            await _appDbContext.SaveChangesAsync();

            return shopifyConfig;
        }

        public async Task<JToken> GetMultipleCurrencyAsync(string variantId, ShopifyConfig config)
        {
            var query = @$"query {{
                    productVariant(id: ""gid://shopify/ProductVariant/{variantId}"") {{";

            query = config.SupportedCountries.Aggregate(
                query,
                (current, country) => current + $@"
                            {country.CountryName.Replace(" ", string.Empty)}: contextualPricing(context: {{country: {country.CountryCode}}}) {{
                                price {{
                                    amount
                                    currencyCode
                                }}
                            }}");

            query += $@"
                        }}
                    }}";

            try
            {
                // Create the subscription
                var service = new GraphService(config.UsersMyShopifyUrl, config.AccessToken);

                return await service.PostAsync(query);
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Shopify {MethodName} error: {ExceptionMessage}",
                    nameof(GetMultipleCurrencyAsync),
                    ex.Message);

                throw;
            }
        }

        public async Task<List<ShopifyConfig>> UpdateCompanyShopifyDiscountSetting(
            string companyId,
            bool isEnabledDiscounts)
        {
            var shopifyConfigs = await _appDbContext.ConfigShopifyConfigs
                .Where(x => x.CompanyId == companyId)
                .ToListAsync();

            shopifyConfigs.ForEach(x => x.IsEnabledDiscounts = isEnabledDiscounts);

            await _appDbContext.SaveChangesAsync();

            return shopifyConfigs;
        }

        public async Task<bool> IsCompanyShopifyDiscountsEnabled(string companyId)
        {
            var shopifyConfig = await _appDbContext.ConfigShopifyConfigs
                .FirstOrDefaultAsync(x => x.CompanyId == companyId);

            return shopifyConfig == null || shopifyConfig.IsEnabledDiscounts;
        }

        public async Task CreateShopifyProductMessageTemplateAsync(
            long shopifyConfigId,
            string companyId,
            string templateMessageBody,
            List<string> templateParams)
        {
            var shopifyConfig = await _appDbContext.ConfigShopifyConfigs
                .FirstOrDefaultAsync(
                    c =>
                        c.Id == shopifyConfigId
                        && c.CompanyId == companyId);

            if (shopifyConfig == null)
            {
                throw new Exception("Shopify store not found");
            }

            await _appDbContext.ShopifyProductMessageTemplates.AddAsync(
                new ShopifyProductMessageTemplate
                {
                    CompanyId = companyId,
                    ShopifyConfigId = shopifyConfigId,
                    MessageBody = templateMessageBody,
                    Params = templateParams
                });

            await _appDbContext.SaveChangesAsync();
        }

        public async Task UpdateShopifyProductMessageTemplateAsync(
            long shopifyProductMessageTemplateId,
            string companyId,
            string templateMessageBody,
            List<string> templateParams)
        {
            var shopifyProductMessageTemplate =
                await _appDbContext.ShopifyProductMessageTemplates
                    .FirstOrDefaultAsync(
                        t =>
                            t.Id == shopifyProductMessageTemplateId
                            && t.CompanyId == companyId);

            if (shopifyProductMessageTemplate == null)
            {
                throw new Exception("Template not found");
            }

            shopifyProductMessageTemplate.MessageBody = templateMessageBody;
            shopifyProductMessageTemplate.Params = templateParams;

            await _appDbContext.SaveChangesAsync();
        }

        public async Task<List<ShopifyProductMessageTemplate>> GetShopifyProductMessageTemplatesAsync(
            long shopifyConfigId,
            string companyId)
        {
            return await _appDbContext.ShopifyProductMessageTemplates
                .Where(
                    t =>
                        t.ShopifyConfigId == shopifyConfigId
                        && t.CompanyId == companyId)
                .ToListAsync();
        }

        public async Task<List<ShopifyStripeCurrencyConnectionStatus>> GetShopifyStripeConnectionStatusAsync(
            long shopifyConfigId,
            string companyId)
        {
            var shopifyProductCurrencies = await GetShopifyProductCurrenciesAsync(shopifyConfigId, companyId);

            var shopifyStripeCurrencyConnectionStatuses = new List<ShopifyStripeCurrencyConnectionStatus>();

            foreach (var shopifyProductCurrency in shopifyProductCurrencies)
            {
                shopifyStripeCurrencyConnectionStatuses.Add(
                    new ShopifyStripeCurrencyConnectionStatus
                    {
                        Currency = shopifyProductCurrency,
                        IsConnected = await _appDbContext.ConfigStripePaymentConfigs.AnyAsync(
                            c =>
                                c.CompanyId == companyId
                                && c.Status == StripePaymentRegistrationStatus.Registered
                                && c.DefaultCurrency.ToLower() == shopifyProductCurrency.ToLower())
                    });
            }

            return shopifyStripeCurrencyConnectionStatuses;
        }

        public async Task<List<string>> GetShopifyProductCurrenciesAsync(long shopifyConfigId, string companyId)
        {
            var shopifyConfig = await _appDbContext.ConfigShopifyConfigs
                .FirstOrDefaultAsync(
                    x =>
                        x.Id == shopifyConfigId
                        && x.CompanyId == companyId);

            if (shopifyConfig == null)
            {
                throw new Exception("Shopify store not found");
            }

            var shopifyProductRecords =
                await _appDbContext.ShopifyProductRecords
                    .Where(
                        r =>
                            r.CompanyId == companyId
                            && r.ShopifyId == shopifyConfigId)
                    .ToListAsync();

            var shopifyProductCurrencies = new List<string>();

            foreach (var shopifyProductRecord in shopifyProductRecords)
            {
                var additionalProductCurrencies =
                    shopifyProductRecord.ProductPayload?.Variants?
                        .Where(pv => !pv.MultipleCurrencies.IsNullOrEmpty())
                        .Select(v => v.MultipleCurrencies)
                        .SelectMany(mc => mc)
                        .Distinct()
                        .Select(c => c.CurrencyCode)
                        .Except(shopifyProductCurrencies);

                if (additionalProductCurrencies != null)
                {
                    shopifyProductCurrencies = shopifyProductCurrencies
                        .Union(additionalProductCurrencies)
                        .ToList();
                }
            }

            return shopifyProductCurrencies.Count == 0 ?
                new List<string>
                {
                    shopifyConfig.Currency
                } :
                shopifyProductCurrencies;
        }

        public async Task<Customer> GetOrCreateShopifyCustomer(
            long shopifyConfigId,
            string companyId,
            string phone,
            string email)
        {
            var shopifyConfig = await _appDbContext.ConfigShopifyConfigs
                .FirstOrDefaultAsync(
                    x =>
                        x.Id == shopifyConfigId
                        && x.CompanyId == companyId);

            if (shopifyConfig == null)
            {
                throw new Exception("Shopify store not found");
            }

            var fields = new List<(string Name, string Value)>
            {
                new ()
                {
                    Name = "phone",
                    Value = phone
                },
                new ()
                {
                    Name = "email",
                    Value = email
                }
            };

            var customerService = new CustomerService(
                shopifyConfig.UsersMyShopifyUrl,
                shopifyConfig.AccessToken);

            if (await IsShopifyCustomerInExistenceAsync(
                    customerService,
                    fields))
            {
                return (await customerService.SearchAsync(
                    new CustomerSearchListFilter
                    {
                        Query = GetShopifyFieldPhraseSearchOrQuery(fields)
                    })).Items.FirstOrDefault();
            }

            return await customerService.CreateAsync(
                new Customer
                {
                    Email = email,
                    Phone = phone
                });
        }

        #region helper

        private decimal ConvertToDecimal(string value)
        {
            decimal result = 0;
            decimal.TryParse(value, out result);
            return result;
        }

        private static async Task<bool> IsShopifyCustomerInExistenceAsync(
            CustomerService customerService,
            List<(string Name, string Value)> fields)
        {
            var customers = await customerService.SearchAsync(
                new CustomerSearchListFilter
                {
                    Query = GetShopifyFieldPhraseSearchOrQuery(fields)
                });

            return customers.Items.Any();
        }

        private static string GetShopifyFieldPhraseSearchOrQuery(
            List<(string Name, string Value)> fields)
        {
            var queryBuilder = new StringBuilder();

            foreach (var field in fields)
            {
                if (string.IsNullOrEmpty(field.Value))
                {
                    continue;
                }

                if (queryBuilder.Length > 0)
                {
                    queryBuilder.Append(" OR ");
                }

                queryBuilder.Append($"{field.Name}:\"{field.Value}\"");
            }

            return queryBuilder.ToString();
        }

        #endregion
    }
}