﻿using AutoMapper;
using Sleekflow.Apis.MessagingHub.Model;
using Travis_backend.Data.WhatsappCloudApi;
using Travis_backend.MessageDomain.Models;
using Travis_backend.Models.ChatChannelConfig;

namespace Travis_backend.Mapping;

public class WhatsappCloudApiMappingProfile : Profile
{
    public WhatsappCloudApiMappingProfile()
    {
        CreateMap<WabaDto, WhatsappCloudApiMessagingHubWabaDto>()
            .ForMember(response => response.MessagingHubWabaId, src => src.MapFrom(x => x.Id));

        CreateMap<WabaPhoneNumberDto, WhatsappCloudApiMessagingHubWabaPhoneNumberDto>()
            .ForMember(response => response.MessagingHubWabaPhoneNumberId, src => src.MapFrom(x => x.Id));

        CreateMap<WhatsappCloudApiConfig, WhatsappCloudApiConfigViewModel>()
            .ForMember(
                response => response.FacebookWabaBusinessId,
                src => src.MapFrom(x => x.Waba.FacebookWabaBusinessId))
            .ForMember(response => response.FacebookWabaId, src => src.MapFrom(x => x.Waba.FacebookWabaId))
            .ForMember(
                response => response.FacebookPhoneNumberId,
                src => src.MapFrom(x => x.WabaPhoneNumber.FacebookPhoneNumberId))
            .ForMember(
                response => response.FacebookDisplayPhoneNumber,
                src => src.MapFrom(x => x.WabaPhoneNumber.FacebookPhoneNumber))
            .ForMember(
                response => response.FacebookPhoneNumberStatus,
                src => src.MapFrom(x => x.WabaPhoneNumber.FacebookPhoneNumberStatus))
            .ForMember(
                response => response.FacebookPhoneNumberQualityRating,
                src => src.MapFrom(x => x.WabaPhoneNumber.FacebookPhoneNumberQualityRating))
            .ForMember(
                response => response.FacebookPhoneNumberNameStatus,
                src => src.MapFrom(x => x.WabaPhoneNumber.FacebookPhoneNumberNameStatus))
            .ForMember(
                response => response.FacebookPhoneNumberNewNameStatus,
                src => src.MapFrom(x => x.WabaPhoneNumber.FacebookPhoneNumberNewNameStatus))
            .ForMember(
                response => response.FacebookWabaBusinessVerificationStatus,
                src => src.MapFrom(x => x.Waba.FacebookWabaBusinessVerificationStatus))
            .ForMember(
                response => response.FacebookPhoneNumberIsPinEnabled,
                src => src.MapFrom(x => x.WabaPhoneNumber.FacebookPhoneNumberIsPinEnabled))
            .ForMember(
                response => response.FacebookPhoneNumberAccountMode,
                src => src.MapFrom(x => x.WabaPhoneNumber.FacebookPhoneNumberAccountMode))
            .ForMember(
                response => response.FacebookPhoneNumberCodeVerificationStatus,
                src => src.MapFrom(x => x.WabaPhoneNumber.FacebookPhoneNumberCodeVerificationStatus))
            .ForMember(
                response => response.FacebookPhoneNumberIsOfficialBusinessAccount,
                src => src.MapFrom(x => x.WabaPhoneNumber.FacebookPhoneNumberIsOfficialBusinessAccount))
            .ForMember(
                response => response.FacebookPhoneNumberMessagingLimitTier,
                src => src.MapFrom(x => x.WabaPhoneNumber.FacebookPhoneNumberMessagingLimitTier))
            .ForMember(
                response => response.FacebookPhoneNumberQualityScore,
                src => src.MapFrom(x => x.WabaPhoneNumber.FacebookPhoneNumberQualityScore))
            .ForMember(
                response => response.FacebookProductCatalogId,
                src => src.MapFrom(x => x.Waba.WabaProductCatalog.FacebookProductCatalogId))
            .ForMember(
                response => response.FacebookProductCatalogName,
                src => src.MapFrom(x => x.Waba.WabaProductCatalog.FacebookProductCatalogName))
            .ForMember(
                response => response.FacebookDatasetId,
                src => src.MapFrom(x => x.Waba.WabaDataset.FacebookDatasetId))
            .ForMember(
                response => response.FacebookDatasetName,
                src => src.MapFrom(x => x.Waba.WabaDataset.FacebookDatasetName));

        CreateMap<WhatsappCloudApiSender, WhatsappCloudApiSenderResponse>();

        CreateMap<GetBusinessDetailResponse, UserBusinessDto>()
            .ForMember(response => response.FacebookBusinessId, src => src.MapFrom(x => x.Id))
            .ForMember(response => response.FacebookBusinessName, src => src.MapFrom(x => x.Name));

        CreateMap<GetWhatsappBusinessAccountSubscribedAppsResponse, BusinessWabaDto>()
            .ForMember(response => response.FacebookWabaId, src => src.MapFrom(x => x.Id))
            .ForMember(response => response.FacebookWabaName, src => src.MapFrom(x => x.Name));

        CreateMap<WhatsappPhoneNumberDetail, BusinessWabaPhoneNumbersDto>()
            .ForMember(response => response.FacebookPhoneNumberId, src => src.MapFrom(x => x.Id))
            .ForMember(response => response.FacebookPhoneNumber, src => src.MapFrom(x => x.DisplayPhoneNumber));
    }
}