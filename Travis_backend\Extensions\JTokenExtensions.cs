﻿#nullable enable
using Newtonsoft.Json.Linq;

namespace Travis_backend.Extensions;

public static class JTokenExtensions
{
    public static JToken? AddField(
        this JToken? token,
        string path,
        string fieldName,
        object fieldValue)
    {
        if (token is null)
        {
            return null;
        }

        var targetToken = token[path];

        if (targetToken is null)
        {
            token[path] = JToken.Parse("{}");
            targetToken = token[path]!;
        }

        if (targetToken.Type is JTokenType.Null)
        {
            targetToken = JToken.Parse("{}");
            token[path] = targetToken;
        }

        if (targetToken.Type is JTokenType.Object)
        {
            targetToken[fieldName] = JToken.FromObject(fieldValue);
        }

        return token;
    }
}