﻿using System;
using System.Collections.Generic;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using Travis_backend.InternalDomain.Models;

namespace Travis_backend.InternalDomain.ViewModels
{
    public class CmsSalesPaymentRecordDto
    {
        public long Id { get; set; }

        public string CompanyId { get; set; }

        public string CompanyName { get; set; }

        public long BillRecordId { get; set; }

        public CmsBillRecordLiteDto BillRecord { get; set; }

        public decimal SubscriptionFee { get; set; }

        public decimal OneTimeSetupFee { get; set; }

        public decimal WhatsappCreditAmount { get; set; }

        public string Currency { get; set; }

        [JsonConverter(typeof(StringEnumConverter))]
        public PaymentMethod PaymentMethod { get; set; }

        [JsonProperty("paymentTerm")]
        public int? PaymentTermInt { get; set; }

        public string Remark { get; set; }

        public DateTime? PaidAt { get; set; }

        public string InvoiceId { get; set; }

        public string ContactOwnerId { get; set; }

        public string ContactOwnerDisplayName { get; set; }

        public DateTime CreatedAt { get; set; }

        public string CreateUserDisplayName { get; set; }

        public string CreateUserId { get; set; }

        public string LastModifiedByUserDisplayName { get; set; }

        public string LastModifiedByUserId { get; set; }

        public decimal? Discount { get; set; }

        public int? PeriodInMonths { get; set; }

        public DateTime LastModifiedDate { get; set; }

        public List<CmsSalesPaymentRecordFileDto> Files { get; set; } = new ();
    }

    public class CmsSalesPaymentRecordFileDto
    {
        public long Id { get; set; }

        public string Filename { get; set; }

        public string Url { get; set; }

        public string MIMEType { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    }
}