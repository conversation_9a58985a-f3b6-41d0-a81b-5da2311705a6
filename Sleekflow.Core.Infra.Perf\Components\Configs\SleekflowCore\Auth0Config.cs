using Newtonsoft.Json;

namespace Sleekflow.Core.Infra.Perf.Components.Configs.SleekflowCore;

public class Auth0Config
{
    [JsonProperty("action_audience")]
    public string ActionAudience { get; set; }

    [JsonProperty("action_issuer")]
    public string ActionIssuer { get; set; }

    [JsonProperty("audience")]
    public string Audience { get; set; }

    [JsonProperty("client_id")]
    public string ClientId { get; set; }

    [JsonProperty("client_secret")]
    public string ClientSecret { get; set; }

    [JsonProperty("database_connection_name")]
    public string DatabaseConnectionName { get; set; }

    [JsonProperty("domain")]
    public string Domain { get; set; }

    [JsonProperty("http_retries")]
    public string HttpRetries { get; set; }

    [JsonProperty("issuers")]
    public IEnumerable<string> Issuers { get; set; }

    [JsonProperty("namespace")]
    public string Namespace { get; set; }

    [JsonProperty("role_claim_type")]
    public string RoleClaimType { get; set; }

    [JsonProperty("user_email_claim_type")]
    public string UserEmailClaimType { get; set; }

    [JsonProperty("user_id_claim_type")]
    public string UserIdClaimType { get; set; }

    [JsonProperty("username_claim_type")]
    public string UsernameClaimType { get; set; }

    [JsonProperty("tenant_hub_secret_key")]
    public string TenantHubSecretKey { get; set; }

    public Auth0Config(
        string actionAudience,
        string actionIssuer,
        string audience,
        string clientId,
        string clientSecret,
        string databaseConnectionName,
        string domain,
        string httpRetries,
        IEnumerable<string> issuers,
        string ns,
        string roleClaimType,
        string userEmailClaimType,
        string userIdClaimType,
        string usernameClaimType,
        string tenantHubSecretKey)
    {
        ActionAudience = actionAudience;
        ActionIssuer = actionIssuer;
        Audience = audience;
        ClientId = clientId;
        ClientSecret = clientSecret;
        DatabaseConnectionName = databaseConnectionName;
        Domain = domain;
        HttpRetries = httpRetries;
        Issuers = issuers;
        Namespace = ns;
        RoleClaimType = roleClaimType;
        UserEmailClaimType = userEmailClaimType;
        UserIdClaimType = userIdClaimType;
        UsernameClaimType = usernameClaimType;
        TenantHubSecretKey = tenantHubSecretKey;
    }
}