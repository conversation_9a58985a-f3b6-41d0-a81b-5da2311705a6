using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Travis_backend.Constants;
using Travis_backend.ConversationDomain.Models;
using Travis_backend.Database;
using Travis_backend.MessageDomain.Models;
using Travis_backend.OpenTelemetry.Meters;
using Travis_backend.OpenTelemetry.Meters.Constants;
using Twilio;
using Twilio.Rest.Api.V2010.Account;
using Twilio.Types;

namespace Travis_backend.MessageDomain.ChannelMessageProvider.ChannelMessageHandlers;

public class SmsChannelMessageHandler : IChannelMessageHandler
{
    private readonly ApplicationDbContext _appDbContext;
    private readonly IConfiguration _configuration;
    private readonly ILogger<SmsChannelMessageHandler> _logger;
    private readonly IConversationMeters _conversationMeters;

    public string ChannelType => ChannelTypes.Sms;

    public SmsChannelMessageHandler(
        ApplicationDbContext appDbContext,
        IConfiguration configuration,
        ILogger<SmsChannelMessageHandler> logger,
        IConversationMeters conversationMeters)
    {
        _appDbContext = appDbContext;
        _configuration = configuration;
        _logger = logger;
        _conversationMeters = conversationMeters;
    }

    public async ValueTask<ConversationMessage> SendChannelMessageAsync(
        Conversation conversation,
        ConversationMessage conversationMessage)
    {
        var smsConfig = await _appDbContext.ConfigSMSConfigs.Where(x => x.CompanyId == conversation.CompanyId)
            .FirstOrDefaultAsync();

        if (smsConfig == null)
        {
            throw new Exception($"No SMSConfig found. CompanyId: {conversation.CompanyId}");
        }

        if (conversationMessage.SMSReceiver != null &&
            smsConfig.SMSSender != conversationMessage.SMSReceiver?.SMSId &&
            string.IsNullOrEmpty(conversationMessage.MessageUniqueID))
        {
            string messageContent = string.Empty;

            if (conversationMessage.TranslationResults != null)
            {
                messageContent = conversationMessage.TranslationResults.FirstOrDefault().translations.FirstOrDefault()
                    .text;
            }
            else
            {
                messageContent = conversationMessage.MessageContent;
            }

            TwilioClient.Init(smsConfig.TwilioAccountId, smsConfig.TwilioSecret);
            List<Uri> mediaURI = new List<Uri>();

            var domainName = _configuration.GetValue<String>("Values:DomainName");

            foreach (var uploadedFile in conversationMessage.UploadedFiles)
            {
                messageContent += $"\n{domainName}/Message/File/Private/{uploadedFile.FileId}";

                // mediaURI.Add(new Uri($"{domainName}/Message/File/{uploadedFile.FileId}"));
            }

            try
            {
                var whatsappMessage = MessageResource.Create(
                    from: new PhoneNumber(smsConfig.SMSSender),
                    to: new PhoneNumber(conversationMessage.SMSReceiver.SMSId),
                    body: messageContent,
                    mediaUrl: mediaURI,
                    statusCallback: new Uri($"{domainName}/twilio/webhook/status"));

                try
                {
                    if (conversationMessage.SMSReceiver.InstanceId != smsConfig.TwilioAccountId ||
                        !_appDbContext.SenderSMSSenders.Any(
                            x => x.SMSId == conversationMessage.SMSReceiver.SMSId &&
                                 x.CompanyId == conversationMessage.CompanyId &&
                                 x.InstanceId == smsConfig.TwilioAccountId))
                    {
                        conversationMessage.SMSReceiver.InstanceId = smsConfig.TwilioAccountId;
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError($"Unable to update instanceId {ex.Message}");
                }

                conversationMessage.MessageUniqueID = whatsappMessage.Sid;
                conversationMessage.Status = MessageStatus.Sent;

                _conversationMeters.IncrementCounter(ChannelTypes.Sms, ConversationMeterOptions.SendSuccess);

                // double price = 0.0;
                // double.TryParse(whatsappMessage.Price, out price);
                // if (price == 0.0) price = -0.005;
                // conversationMessage.Price = price;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Unable to send twilio message {ex.ToString()}");
                conversationMessage.Status = MessageStatus.Failed;
                conversationMessage.ChannelStatusMessage = ex.Message;

                _conversationMeters.IncrementCounter(ChannelTypes.Sms, ConversationMeterOptions.SendFailed);
            }

            await _appDbContext.SaveChangesAsync();
        }
        else
        {
            conversationMessage.Status = MessageStatus.Sent;
        }

        await _appDbContext.SaveChangesAsync();

        return conversationMessage;
    }
}