﻿using System;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;

namespace Travis_backend.MigrationJobs.Attributes;

[AttributeUsage(AttributeTargets.Class | AttributeTargets.Method)]
public class MigrationJobAuthorizationAttribute : Attribute, IAuthorizationFilter
{
    private const string RequiredSecretValue = "6Of0cRjb9JRHXHsvYzboQYSl4kgBAYtc";

    public void OnAuthorization(AuthorizationFilterContext context)
    {
        if (!context.HttpContext.Request.Query.TryGetValue("SecretKey", out var secretValue))
        {
            context.Result = new UnauthorizedResult();
        }

        if (!string.Equals(secretValue, RequiredSecretValue))
        {
            context.Result = new UnauthorizedResult();
        }
    }
}