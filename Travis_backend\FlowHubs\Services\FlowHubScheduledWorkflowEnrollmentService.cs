﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Hangfire;
using Hangfire.Storage;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Sleekflow.Apis.CrmHub.Api;
using Sleekflow.Apis.CrmHub.Model;
using Sleekflow.Apis.FlowHub.Model;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.Database;
using Travis_backend.Enums;

namespace Travis_backend.FlowHubs.Services;

public interface IFlowHubScheduledWorkflowEnrollmentService
{
    void EnqueueManualWorkflowEnrollment(
        WorkflowDto workflowDto,
        Staff staff,
        List<string> userProfileIds,
        List<long> contactListIds);

    void EnqueueScheduledWorkflowEnrollment(
        WorkflowDto workflowDto);

    void CancelScheduledWorkflowEnrollment(
        string companyId,
        string workflowId);

    string GetScheduledWorkflowEnrollmentStatus(
        string companyId,
        string workflowId);
}

public class FlowHubScheduledWorkflowEnrollmentService : IFlowHubScheduledWorkflowEnrollmentService
{
    private readonly ApplicationDbContext _dbContext;
    private readonly ILogger<FlowHubScheduledWorkflowEnrollmentService> _logger;
    private readonly IBackgroundJobClient _backgroundJobClient;
    private readonly IRecurringJobManager _recurringJobManager;
    private readonly IInflowActionsApi _inflowActionsApi;

    public FlowHubScheduledWorkflowEnrollmentService(
        ApplicationDbContext dbContext,
        ILogger<FlowHubScheduledWorkflowEnrollmentService> logger,
        IBackgroundJobClient backgroundJobClient,
        IRecurringJobManager recurringJobManager,
        IInflowActionsApi inflowActionsApi)
    {
        _dbContext = dbContext;
        _logger = logger;
        _backgroundJobClient = backgroundJobClient;
        _recurringJobManager = recurringJobManager;
        _inflowActionsApi = inflowActionsApi;
    }

    public void EnqueueManualWorkflowEnrollment(
        WorkflowDto workflowDto,
        Staff staff,
        List<string> userProfileIds,
        List<long> contactListIds)
    {
        var runAt = DateTimeOffset.UtcNow.AddSeconds(10);
        var jobId = $"{workflowDto.SleekflowCompanyId}:{workflowDto.WorkflowId}";

        _logger.LogInformation(
            "Manual enrollment by {StaffId} for workflow {WorkflowId} in company {CompanyId} scheduled to run at {RunAt}",
            staff.IdentityId,
            workflowDto.WorkflowId,
            workflowDto.SleekflowCompanyId,
            runAt.ToString("o"));

        _recurringJobManager.AddOrUpdate(
            jobId,
            () => TriggerManualWorkflowEnrollmentAsync(
                workflowDto.SleekflowCompanyId,
                workflowDto.WorkflowId,
                workflowDto.WorkflowVersionedId,
                userProfileIds,
                contactListIds,
                CancellationToken.None),
            $"{runAt.Second} {runAt.Minute} {runAt.Hour} {runAt.Day} {runAt.Month} {(int)runAt.DayOfWeek}");
    }

    public async Task TriggerManualWorkflowEnrollmentAsync(
        string companyId,
        string workflowId,
        string workflowVersionedId,
        List<string> userProfileIds,
        List<long> contactListIds,
        CancellationToken cancellationToken)
    {
        try
        {
            var userProfileIdsToEnroll = userProfileIds ?? new List<string>();

            if (contactListIds is { Count: > 0 })
            {
                var contactListUserProfiles = await _dbContext.CompanyImportedUserProfiles
                    .Where(c => contactListIds.Contains(c.ImportContactHistoryId))
                    .Select(c => c.UserProfileId)
                    .ToListAsync(cancellationToken);

                userProfileIdsToEnroll.AddRange(contactListUserProfiles);
            }

            var userProfileIdsSet = userProfileIdsToEnroll.ToHashSet();

            foreach (var userProfileId in userProfileIdsSet)
            {
                if (cancellationToken.IsCancellationRequested)
                {
                    break;
                }

                _backgroundJobClient.Enqueue<IUserProfileHooks>(
                    x => x.OnUserProfileManuallyEnrolledAsync(
                        companyId,
                        userProfileId,
                        workflowId,
                        workflowVersionedId));
            }

            CancelScheduledWorkflowEnrollment(companyId, workflowId);
        }
        catch (OperationCanceledException)
        {
            CancelScheduledWorkflowEnrollment(companyId, workflowId);
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "[{MethodName}] Error while manually enrolling user profiles for workflow versioned id: {WorkflowVersionedId}, company id: {CompanyId},",
                nameof(TriggerManualWorkflowEnrollmentAsync),
                workflowVersionedId,
                companyId);
        }
    }

    public void EnqueueScheduledWorkflowEnrollment(WorkflowDto workflowDto)
    {
        if (workflowDto?.WorkflowScheduleSettings != null
            && workflowDto.WorkflowScheduleSettings.ScheduleType != "none")
        {
            var runAt = DateTimeOffset.UtcNow.AddSeconds(10);
            var jobId = $"{workflowDto.SleekflowCompanyId}:{workflowDto.WorkflowId}";
            var cron = $"{runAt.Second} {runAt.Minute} {runAt.Hour} {runAt.Day} {runAt.Month} {(int) runAt.DayOfWeek}";

            _logger.LogInformation(
                "Manual enrollment for workflow {WorkflowId} in company {CompanyId} scheduled to run at {RunAt}",
                workflowDto.WorkflowId,
                workflowDto.SleekflowCompanyId,
                runAt.ToString("o"));

            if (workflowDto.WorkflowScheduleSettings.ScheduleType == "schemaful_object_property_based_date_time")
            {
                _recurringJobManager.AddOrUpdate(
                    jobId,
                    () => TriggerSchemafulObjectPropertyBasedScheduledWorkflowFirstRunAsync(
                        workflowDto.SleekflowCompanyId,
                        workflowDto.WorkflowId,
                        workflowDto.WorkflowVersionedId,
                        workflowDto.WorkflowScheduleSettings.SchemaId,
                        CancellationToken.None),
                    cron);
            }
            else
            {
                _recurringJobManager.AddOrUpdate(
                    jobId,
                    () => TriggerScheduledWorkflowFirstRunAsync(
                        workflowDto.SleekflowCompanyId,
                        workflowDto.WorkflowId,
                        workflowDto.WorkflowVersionedId,
                        CancellationToken.None),
                    cron);
            }
        }
    }

    public void CancelScheduledWorkflowEnrollment(string companyId, string workflowId)
    {
        var jobId = $"{companyId}:{workflowId}";

        using var connection = JobStorage.Current.GetReadOnlyConnection();
        var recurringJob = connection.GetRecurringJobs(new List<string>() { jobId }).FirstOrDefault();

        if (recurringJob is { Job: not null, LastJobId: not null })
        {
            _logger.LogInformation(
                "Manual enrollment for workflow {WorkflowId} in company {CompanyId} cancelled, job id: {JobId}",
                workflowId,
                companyId,
                recurringJob.LastJobId);

            _backgroundJobClient.Delete(recurringJob.LastJobId);
            _recurringJobManager.RemoveIfExists(jobId);
        }
    }

    public string GetScheduledWorkflowEnrollmentStatus(string companyId, string workflowId)
    {
        var jobId = $"{companyId}:{workflowId}";

        using var connection = JobStorage.Current.GetReadOnlyConnection();
        var recurringJob = connection.GetRecurringJobs(new List<string>() { jobId }).FirstOrDefault();

        return recurringJob?.LastJobState switch
        {
            "Awaiting" or "Scheduled" or "Enqueued" => "Pending",
            "Processing" => "Processing",
            "Succeeded" => "Completed",
            "Deleted" => "Cancelled",
            "Failed" => "Failed",
            _ => string.Empty
        };
    }

    public async Task TriggerScheduledWorkflowFirstRunAsync(
        string companyId,
        string workflowId,
        string workflowVersionedId,
        CancellationToken cancellationToken)
    {
        try
        {
            var userProfileIds = await _dbContext.UserProfiles
                .Where(c => c.CompanyId == companyId && c.ActiveStatus == ActiveStatus.Active)
                .OrderBy(c => c.CreatedAt)
                .Select(x => x.Id)
                .ToListAsync(cancellationToken);

            foreach (var userProfileId in userProfileIds)
            {
                if (cancellationToken.IsCancellationRequested)
                {
                    break;
                }

                // Manual enroll contact into workflow
                _backgroundJobClient.Enqueue<IUserProfileHooks>(
                    x => x.OnUserProfileManuallyEnrolledAsync(
                        companyId,
                        userProfileId,
                        workflowId,
                        workflowVersionedId));
            }

            CancelScheduledWorkflowEnrollment(companyId, workflowId);
        }
        catch (OperationCanceledException)
        {
            CancelScheduledWorkflowEnrollment(companyId, workflowId);
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "Error while manually enrolling user profiles for workflow versioned id: {WorkflowVersionedId}, company id: {CompanyId},",
                workflowVersionedId,
                companyId);
        }
    }

    public async Task TriggerSchemafulObjectPropertyBasedScheduledWorkflowFirstRunAsync(
        string companyId,
        string workflowId,
        string workflowVersionedId,
        string schemaId,
        CancellationToken cancellationToken)
    {
        try
        {
            await _inflowActionsApi.InflowActionsLoopThroughAndEnrollSchemafulObjectsToFlowHubPostAsync(
                loopThroughAndEnrollSchemafulObjectsToFlowHubInput: new LoopThroughAndEnrollSchemafulObjectsToFlowHubInput(
                    companyId,
                    schemaId,
                    workflowId,
                    workflowVersionedId),
                cancellationToken: cancellationToken);

            CancelScheduledWorkflowEnrollment(companyId, workflowId);
        }
        catch (OperationCanceledException)
        {
            // This method is to trigger starting an enrollment in CrmHub which hosted by Azure Durable Function.
            // So the cancellation here just aims to remove the job from Hangfire.
            // In stead we will terminate the running enrollments of this workflow before starting a new job in CrmHub.
            CancelScheduledWorkflowEnrollment(companyId, workflowId);
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "Error while manually enrolling schemaful objects for workflow versioned id: {WorkflowVersionedId}, company id: {CompanyId}, schema id: {SchemaId},",
                workflowVersionedId,
                companyId,
                schemaId);
        }
    }
}