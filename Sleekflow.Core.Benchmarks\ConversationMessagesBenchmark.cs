using BenchmarkDotNet.Attributes;
using BenchmarkDotNet.Engines;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Travis_backend.Database;

namespace Sleekflow.Core.Benchmarks;

[SimpleJob(RunStrategy.Monitoring, launchCount: 1, warmupCount: 1, iterationCount: 5, invocationCount: 1)]
public class ConversationMessagesBenchmark
{
    private bool _firstCall = true;
    private List<string>? _random100ConversationIds;

    private readonly ApplicationDbContext _applicationDbContext;
    private readonly ILogger<ConversationMessagesBenchmark> _logger;

    public ConversationMessagesBenchmark()
    {
        var loggerFactory = LoggerFactory.Create(builder => { builder.AddConsole(); });
        _logger = loggerFactory.CreateLogger<ConversationMessagesBenchmark>();

        _applicationDbContext = new ApplicationDbContext(
            new DbContextOptionsBuilder<ApplicationDbContext>()
                .UseSqlServer(new SqlConnection(DbConfig.ConnStr))
                .Options);
    }

    [GlobalSetup]
    public async Task GlobalSetup()
    {
        _random100ConversationIds = (await _applicationDbContext.Conversations.Take(100).ToListAsync())
            .Select(x => x.Id)
            .ToList();
    }

    [Benchmark(Baseline = true)]
    public async Task<List<(string ConversationId, long Id)>> GetAllFromDb()
    {
        var conversationIds = _random100ConversationIds!;

        var queryable = _applicationDbContext.ConversationMessages
            .Where(
                y => conversationIds
                    .Contains(y.ConversationId))
            .OrderBy(y => y.Id)
            .Select(
                y => new
                {
                    y.ConversationId, y.Id
                });

        if (_firstCall)
        {
            var queryString = queryable.ToQueryString();

            _logger.LogInformation(nameof(GetAllFromDb) + " " + queryString);

            _firstCall = false;
        }

        return (await queryable.ToListAsync())
            .Select(i => (i.ConversationId, i.Id))
            .ToList();
    }

    [Benchmark]
    public async Task<Dictionary<string, long>> GroupByInDb()
    {
        var conversationIds = _random100ConversationIds!;

        var queryable = _applicationDbContext.ConversationMessages
            .Where(y => conversationIds.Contains(y.ConversationId))
            .GroupBy(y => y.ConversationId)
            .Select(
                g => new
                {
                    ConversationId = g.Key, Id = g.Min(y => y.Id)
                });

        if (_firstCall)
        {
            var queryString = queryable.ToQueryString();

            _logger.LogInformation(nameof(GroupByInDb) + " " + queryString);

            _firstCall = false;
        }

        return await queryable.ToDictionaryAsync(x => x.ConversationId, x => x.Id);
    }
}