﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Net.Http;
using System.Threading.Tasks;
using AutoMapper;
using GraphApi.Client.Const.WhatsappCloudApi;
using GraphApi.Client.Helpers;
using GraphApi.Client.Payloads.Models.Flows;
using Hangfire;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc.ModelBinding.Validation;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Newtonsoft.Json.Serialization;
using Polly;
using Sleekflow.Apis.MessagingHub.Api;
using Sleekflow.Apis.MessagingHub.Model;
using Travis_backend.Cache;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.Cache.Models.CacheKeyPatterns;
using Travis_backend.CompanyDomain.Services;
using Travis_backend.Constants;
using Travis_backend.Data.WhatsappCloudApi;
using Travis_backend.Database;
using Travis_backend.Enums;
using Travis_backend.Exceptions;
using Travis_backend.Extensions;
using Travis_backend.FlowHubs;
using Travis_backend.Helpers;
using Travis_backend.InternalDomain.Services;
using Travis_backend.MessageDomain.Models;
using Travis_backend.Models.ChatChannelConfig;
using Travis_backend.ResellerDomain.Models;
using SleekflowErrorCodeException = Sleekflow.Apis.MessagingHub.Client.SleekflowErrorCodeException;
using TemplateButtonObject = Sleekflow.Apis.MessagingHub.Model.TemplateButtonObject;
using WhatsappCloudApiUpdateFlowMetadataObject =
    GraphApi.Client.Models.MessageObjects.FlowObjects.WhatsappCloudApiUpdateFlowMetadataObject;

namespace Travis_backend.ConversationServices;

public interface IWhatsappCloudApiService
{
    Task<List<WabaDto>> GetConnectWaba(string companyId, bool shouldRefresh = false);

    Task<(List<WabaDto> ConnectedWabas, List<string> ForbiddenWabaNames)> ConnectWaba(
        string companyId,
        string facebookAppUserToken);

    Task<(List<WabaDto> ConnectedWabas, List<string> ForbiddenWabaNames)> ConnectWabaWithFacebookAuthorizationCode(
        string companyId,
        string facebookAuthorizationCode);

    Task<WhatsappCloudApiConfig> ConnectWabaPhoneNumber(
        string companyId,
        string channelName,
        string messagingHubWabaId,
        string messagingHubWabaPhoneNumberId,
        string pin = null);

    Task<WhatsappCloudApiConfig> UpdateWabaPhoneNumber(
        string companyId,
        string channelName,
        string messagingHubWabaId,
        string messagingHubWabaPhoneNumberId);

    Task DisconnectWabaPhoneNumber(string companyId, string messagingHubWabaId, string messagingHubWabaPhoneNumberId);

    Task ReconnectWabaPhoneNumber(string companyId, string messagingHubWabaId, string messagingHubWabaPhoneNumberId);

    // Templates
    Task<List<WhatsappCloudApiTemplateResponse>> GetTemplates(
        string companyId,
        string messagingHubWabaId,
        bool allowCache = true);

    Task<string> CreateTemplate(
        string companyId,
        string messagingHubWabaId,
        WhatsappCloudApiCreateTemplateObject createTemplateObject);

    Task<bool> UpdateTemplate(
        string companyId,
        string messagingHubWabaId,
        string templateId,
        List<WhatsappCloudApiTemplateComponentObject> templateComponents);

    Task<bool> DeleteTemplate(
        string companyId,
        string messagingHubWabaId,
        string templateName);

    Task<Dictionary<string, string>> RequestUploadTemplateFileUrlAsync(
        string companyId,
        string messagingHubWabaId);

    Task<Dictionary<string, string>> RetrieveTemplateFileHeaderHandleAsync(
        string companyId,
        string messagingHubWabaId,
        string blobId);

    Task<bool> AddTemplateBookmarkAsync(
        string companyId,
        string messagingHubWabaId,
        string templateId,
        string templateName,
        string templateLanguage);

    Task<bool> DeleteTemplateBookmarkAsync(
        string companyId,
        string messagingHubWabaId,
        string templateId);

    Task<bool> DeleteWabaTemplateBookmarksAsync(
        string companyId,
        string messagingHubWabaId);

    // Hangfire tasks
    Task RegisterDefaultTemplates(string companyId, string messagingHubWabaId);

    Task RefreshWhatsappCloudApiConfigsDetail(List<string> companyIds = null);

    Task ReplaceWhatsappCloudApiSender(string companyId, string removedWhatsappPhoneNumber);

    Task AddBackWhatsappCloudApiSenderToUserProfile(string companyId);

    Task<GetWhatsappCloudApiBusinessBalancesOutput> GetBusinessBalances(string sleekflowCompanyId);

    #region auto top-up

    Task<GetWhatsappCloudApiBusinessBalanceAutoTopUpProfileOutput> GetBusinessBalanceAutoTopUpProfile(
        string sleekflowCompanyId,
        string facebookBusinessId);

    Task<GetWhatsappCloudApiBusinessBalanceAutoTopUpProfileSettingsOutput> GetBusinessBalanceAutoTopUpProfileSettings(
        string sleekflowCompanyId);

    Task<UpsertWhatsappCloudApiBusinessBalanceAutoTopUpProfileOutput> UpsertBusinessBalanceAutoTopUpProfile(
        BusinessBalanceAutoTopUpProfileDto businessBalanceAutoTopUpProfileDto,
        string sleekflowCompanyId,
        long staffId,
        string email,
        string creditedBy,
        string creditedByDisplayName,
        string redirectToUrl,
        string phoneNumber);

    #endregion

    # region manual top-up

    Task<GetWhatsappCloudApiBusinessBalanceStripeTopUpPlansOutput> GetBusinessBalanceStripeTopUpPlans(
        string sleekflowCompanyId);

    Task<GenerateWhatsappCloudApiBusinessBalanceStripeTopUpLinkOutput> GenerateBusinessBalanceStripeTopUpLink(
        string sleekflowCompanyId,
        string staffId,
        string? displayName,
        string email,
        string stripeTopUpPlanId,
        string facebookBusinessId,
        string? redirectToUrl,
        string? wabaId);

    Task<TopUpWhatsappCloudApiBusinessBalanceOutputOutput> TopUp(
        string companyId,
        string uniqueId,
        string facebookBusinessId,
        string topUpMethod,
        Money credit,
        string creditedBy,
        string creditedByDisplayName,
        string resellerTransactionLogDetail,
        StripeTopUpCreditDetail stripeTopUpCreditDetail = null,
        Dictionary<string, object?> metadata = null);

    Task<GetBusinessBalanceStripeTopUpInvoiceResponse> GetBusinessBalancesStripeTopUpInvoices(
        string sleekflowCompanyId,
        DateTime? start,
        DateTime? end,
        string continuationToken = null,
        int limit = 10);

    # endregion

    // Migration tasks
    Task<List<UserBusinessDto>> GetUserBusinesses(string companyId, string userAccessToken);

    Task<WabasByBspDto> GetUserBusinessWhatsappAccounts(string companyId, string businessId, string userAccessToken);

    Task<List<BusinessWabaPhoneNumbersDto>> GetUserBusinessWabaPhoneNumbers(
        string companyId,
        string facebookWabaId,
        string userAccessToken);

    Task<PhoneNumberWabaMigrationRequestDto> InitiatePhoneNumberWabaMigration(
        string companyId,
        string facebookWabaId,
        string phoneNumber);

    Task<PhoneNumberVerificationCodeRequestDto> RequestPhoneNumberVerificationCode(
        string companyId,
        string facebookWabaId,
        string destinationPhoneNumberId,
        string phoneNumber,
        string codeMethod,
        string language);

    Task<PhoneNumberWabaMigrationVerificationDto> PhoneNumberWabaMigrationVerificationCode(
        string companyId,
        string facebookWabaId,
        string destinationPhoneNumberId,
        string phoneNumber,
        string code);

    Task<PhoneNumberRegistrationDto> RegisterWhatsAppPhoneNumberAsync(
        string companyId,
        string facebookWabaId,
        string destinationPhoneNumberId,
        string phoneNumber,
        string pin = null);

    Task<PhoneNumberDeRegistrationDto> DeRegisterWhatsAppPhoneNumberAsync(
        string companyId,
        string messagingHubWabaId,
        string messagingHubPhoneNumberId);

    Task MigrateWhatsApp360DialogDataToCloudApiAsync(string companyId);

    Task<GetWhatsappCloudApiConversationUsageAnalyticOutput> GetConversationUsageByFacebookWabaIdAsync(
        string companyId,
        string facebookBusinessId,
        string facebookWabaId,
        DateTime start,
        DateTime end);

    Task SendWhatsAppCloudApiSystemAlertAsync(
        string subject,
        string message,
        string category,
        string type,
        string companyId,
        bool prependCompanyInfo);

    Task<WabaDto> GetWabaDtoFromFacebookWabaPhoneNumberId(
        string companyId,
        string facebookWabaId,
        string facebookPhoneNumberId);

    Task<GetWhatsappCloudApiMediaOutputOutput> GetWhatsappCloudApiMediaAsync(
        string companyId,
        string messagingHubPhoneNumberId,
        string mediaId);

    TemplateMessagePayload GenerateTemplateMessagePayload(
        List<WhatsappCloudApiTemplateComponentObject> components);

    #region Conversational Automation

    Task<List<GetConversationalAutomationListResponse>> GetConversationalAutomationsAsync(
        string companyId,
        string facebookWabaId,
        CursorBasedPaginationParam paginationParam = null);

    Task<UpdateConversationalAutomationOutputOutput> UpdateConversationalAutomationAsync(
        UpdateConversationalAutomationInput input);

    #endregion


    #region Waba Level Credit Management

    Task<GetWabaBalanceAutoTopUpProfileOutputOutput> GetWabaBalanceAutoTopUpProfileAsync(
        GetWabaBalanceAutoTopUpProfileInput input,
        CancellationToken cancellationToken);

    Task<GetBusinessBalanceCreditTransferTransactionLogsOutputOutput> GetBusinessWabaBalanceTransferTransactionsAsync(
        GetBusinessBalanceCreditTransferTransactionLogsInput input,
        CancellationToken cancellationToken);

    Task<UpsertWabaBalanceAutoTopUpProfileOutputOutput> UpsertWabaBalanceAutoTopUpProfileAsync(
        UpsertWabaBalanceAutoTopUpProfileRequest input,
        Staff staff,
        CancellationToken cancellationToken);

    Task<AllocateBusinessWabaLevelCreditOutputOutput> AllocateCreditBetweenBusinessAndWabaAsync(
        AllocateBusinessWabaLevelCreditInput input,
        CancellationToken cancellationToken);

    Task<SwitchFromBusinessLevelToWabaLevelCreditManagementOutputOutput> SwitchToWabaLevelCreditManagementAsync(
        SwitchFromBusinessLevelToWabaLevelCreditManagementInput input,
        CancellationToken cancellationToken);

    Task<SwitchFromWabaLevelToBusinessLevelCreditManagementOutputOutput> SwitchToBusinessLevelCreditManagementAsync(
            SwitchFromWabaLevelToBusinessLevelCreditManagementInput input,
            CancellationToken cancellationToken);

    #endregion

    Task<Money> GetWabaAllTimeUsageAsync(
        BusinessBalanceTransactionLogFilter businessBalanceTransactionLogFilter,
        int limit = 10000,
        CancellationToken cancellationToken = default);

    #region WhatsApp Flows

    Task<GetFlowsResponse> GetWhatsappFlowsByWabaIdAsync(
        string companyId,
        string messagingHubWabaId,
        CursorBasedPaginationParam paginationParam = null);

    Task<GetFlowResponse> GetWhatsappFlowAsync(string companyId, string messagingHubWabaId, string flowId);

    Task<List<GetWhatsappFlowsByWabaResponse>> GetAllWhatsappFlowsByWabaAsync(string companyId);

    Task<FlowComponent> GetWhatsappFlowJsonAsync(string companyId, string messagingHubWabaId, string flowId);

    Task<List<ScreenSubmissionDataPayload>> GetTerminalScreensPayloadsAsync(
        string companyId,
        string messagingHubWabaId,
        string flowId);

    Task<GetFlowResponse> UpdateWhatsappFlowMetadataAsync(
        string companyId,
        string messagingHubWabaId,
        string flowId,
        WhatsappCloudApiUpdateFlowMetadataObject updateFlowMetadataObject,
        string staffIdentityId);

    #endregion

    #region Conversion Api
    Task<WabaDto> SetupWabaDatasetAsync(
        string companyId,
        string messagingHubWabaId,
        string facebookDatasetName,
        string staffIdentityId);

    #endregion

}

public class WhatsappCloudApiService : IWhatsappCloudApiService
{
    private readonly ApplicationDbContext _appDbContext;
    private readonly ILogger<WhatsappCloudApiService> _logger;
    private readonly IConfiguration _configuration;
    private readonly IWebHostEnvironment _webHostEnvironment;
    private readonly IMapper _mapper;
    private readonly IWabasApi _wabasApi;
    private readonly IChannelsApi _channelsApi;
    private readonly ITemplatesApi _templatesApi;
    private readonly ITransactionLogsApi _transactionLogsApi;
    private readonly ICacheManagerService _cacheManagerService;
    private readonly ILockService _lockService;
    private readonly ICompanyInfoCacheService _companyInfoCacheService;
    private readonly IBalancesApi _balancesApi;
    private readonly IMigrationsApi _migrationsApi;
    private readonly IInternalWhatsappCloudApiService _internalWhatsappCloudApiService;
    private readonly IMediasApi _mediasApi;
    private readonly IManagementsApi _managementsApi;
    private readonly IConversationalAutomationsApi _conversationalAutomationsApi;
    private readonly IMessageTemplateHooks _messageTemplateHooks;
    private readonly IWhatsappFlowsApi _whatsappFlowsApi;
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly IMetaConversionApisApi _metaConversionApisApi;

    public WhatsappCloudApiService(
        ApplicationDbContext appDbContext,
        ILogger<WhatsappCloudApiService> logger,
        IConfiguration configuration,
        IWebHostEnvironment webHostEnvironment,
        IMapper mapper,
        IWabasApi wabasApi,
        ITemplatesApi templatesApi,
        IChannelsApi channelsApi,
        ICacheManagerService cacheManagerService,
        ILockService lockService,
        ICompanyInfoCacheService companyInfoCacheService,
        IBalancesApi balancesApi,
        IMigrationsApi migrationsApi,
        IInternalWhatsappCloudApiService internalWhatsappCloudApiService,
        IMediasApi mediasApi,
        ITransactionLogsApi transactionLogsApi,
        IManagementsApi managementsApi,
        IConversationalAutomationsApi conversationalAutomationsApi,
        IMessageTemplateHooks messageTemplateHooks,
        IWhatsappFlowsApi whatsappFlowsApi,
        IHttpClientFactory httpClientFactory,
        IMetaConversionApisApi metaConversionApisApi)
    {
        _appDbContext = appDbContext;
        _logger = logger;
        _configuration = configuration;
        _webHostEnvironment = webHostEnvironment;
        _mapper = mapper;
        _wabasApi = wabasApi;
        _templatesApi = templatesApi;
        _channelsApi = channelsApi;
        _cacheManagerService = cacheManagerService;
        _lockService = lockService;
        _companyInfoCacheService = companyInfoCacheService;
        _balancesApi = balancesApi;
        _migrationsApi = migrationsApi;
        _internalWhatsappCloudApiService = internalWhatsappCloudApiService;
        _mediasApi = mediasApi;
        _transactionLogsApi = transactionLogsApi;
        _managementsApi = managementsApi;
        _conversationalAutomationsApi = conversationalAutomationsApi;
        _messageTemplateHooks = messageTemplateHooks;
        _whatsappFlowsApi = whatsappFlowsApi;
        _httpClientFactory = httpClientFactory;
        _metaConversionApisApi = metaConversionApisApi;
    }

    public async Task<List<WabaDto>> GetConnectWaba(string companyId, bool shouldRefresh)
    {
        var output = await _wabasApi.WabasGetConnectedWhatsappCloudApiWabasPostAsync(
            getConnectedWhatsappCloudApiWabasInput:
            new GetConnectedWhatsappCloudApiWabasInput(companyId, shouldRefresh));

        return output.Data.ConnectedWabas;
    }

    public async Task<(List<WabaDto> ConnectedWabas, List<string> ForbiddenWabaNames)> ConnectWaba(
        string companyId,
        string facebookAppUserToken)
    {
        var forbiddenWabas = new List<WabaDto>();

        var getConnectedWhatsappCloudApiWabasOutputOutput =
            await _wabasApi.WabasGetConnectedWhatsappCloudApiWabasPostAsync(
                getConnectedWhatsappCloudApiWabasInput:
                new GetConnectedWhatsappCloudApiWabasInput(
                    shouldRefresh: true,
                    userAccessToken: facebookAppUserToken));

        var hasTokenBeenUsed = getConnectedWhatsappCloudApiWabasOutputOutput.Data.ConnectedWabas;

        var sleekflowCompanyIds = hasTokenBeenUsed
            .Select(w => w.SleekflowCompanyIds)
            .SelectMany(s => s)
            .Distinct()
            .ToList();

        var companyCompaniesIds = await _appDbContext.CompanyCompanies
            .Select(c => c.Id)
            .ToListAsync();

        var forbiddenList = sleekflowCompanyIds
            .Where(s => !companyCompaniesIds.Contains(s))
            .ToList();

        if (forbiddenList.Count != 0)
        {
            forbiddenWabas = hasTokenBeenUsed
                .Where(
                    w => w.SleekflowCompanyIds
                        .Intersect(forbiddenList)
                        .Any())
                .ToList();
        }

        var output = await _wabasApi.WabasConnectWhatsappCloudApiWabaPostAsync(
            connectWhatsappCloudApiWabaInput:
            new ConnectWhatsappCloudApiWabaInput(
                companyId,
                facebookAppUserToken,
                forbiddenWabaIds: forbiddenWabas.Any()
                    ? forbiddenWabas
                        .Select(w => w.FacebookWabaId)
                        .ToList()
                    : null));

        BackgroundJob.Enqueue(
            () => UpdateBusinessBalanceChangedWebhookUrlFromConnectedWabas(companyId, output.Data.ConnectedWabas));

        foreach (var waba in output.Data.ConnectedWabas)
        {
            _appDbContext.WhatsappCloudApiWabaConnections.Add(
                new WhatsappCloudApiWabaConnection(companyId, waba));

            BackgroundJob.Enqueue<IWhatsappCloudApiService>(
                x =>
                    x.RegisterDefaultTemplates(
                        companyId,
                        waba.Id));
        }

        await _appDbContext.SaveChangesAsync();

        return (
            output.Data.ConnectedWabas,
            forbiddenWabas
                .Select(w => w.FacebookWabaName)
                .ToList());
    }

    public async Task<(List<WabaDto> ConnectedWabas, List<string> ForbiddenWabaNames)>
        ConnectWabaWithFacebookAuthorizationCode(
            string companyId,
            string facebookAuthorizationCode)
    {
        var forbiddenWabas = new List<WabaDto>();

        var getConnectedWhatsappCloudApiWabasOutputOutput =
            await _wabasApi.WabasGetConnectedWhatsappCloudApiWabasPostAsync(
                getConnectedWhatsappCloudApiWabasInput:
                new GetConnectedWhatsappCloudApiWabasInput(
                    shouldRefresh: true,
                    facebookAuthorizationCode: facebookAuthorizationCode));

        if (!getConnectedWhatsappCloudApiWabasOutputOutput.Success ||
            getConnectedWhatsappCloudApiWabasOutputOutput.Data == null || string.IsNullOrEmpty(
                getConnectedWhatsappCloudApiWabasOutputOutput.Data.BusinessIntegrationSystemUserAccessToken))
        {
            throw new SleekflowUiException("Cannot get business integration system user access token");
        }

        var hasTokenBeenUsed = getConnectedWhatsappCloudApiWabasOutputOutput.Data.ConnectedWabas;

        var sleekflowCompanyIds = hasTokenBeenUsed
            .Select(w => w.SleekflowCompanyIds)
            .SelectMany(s => s)
            .Distinct()
            .ToList();

        var companyCompaniesIds = await _appDbContext.CompanyCompanies
            .Select(c => c.Id)
            .ToListAsync();

        var forbiddenList = sleekflowCompanyIds
            .Where(s => !companyCompaniesIds.Contains(s))
            .ToList();

        if (forbiddenList.Count != 0)
        {
            forbiddenWabas = hasTokenBeenUsed
                .Where(
                    w => w.SleekflowCompanyIds
                        .Intersect(forbiddenList)
                        .Any())
                .ToList();
        }

        var output =
            await _wabasApi.WabasConnectWhatsappCloudApiWabaWithBusinessIntegrationSystemUserAccessTokenPostAsync(
                connectWhatsappCloudApiWabaWithBusinessIntegrationSystemUserAccessTokenInput:
                new ConnectWhatsappCloudApiWabaWithBusinessIntegrationSystemUserAccessTokenInput(
                    companyId,
                    getConnectedWhatsappCloudApiWabasOutputOutput.Data.BusinessIntegrationSystemUserAccessToken,
                    forbiddenWabaIds: (forbiddenWabas.Any()
                        ? forbiddenWabas
                            .Select(w => w.FacebookWabaId)
                            .ToList()
                        : null)!));

        BackgroundJob.Enqueue(
            () => UpdateBusinessBalanceChangedWebhookUrlFromConnectedWabas(companyId, output.Data.ConnectedWabas));

        foreach (var waba in output.Data.ConnectedWabas)
        {
            _appDbContext.WhatsappCloudApiWabaConnections.Add(
                new WhatsappCloudApiWabaConnection(companyId, waba));

            BackgroundJob.Enqueue<IWhatsappCloudApiService>(
                x =>
                    x.RegisterDefaultTemplates(
                        companyId,
                        waba.Id));
        }

        await _appDbContext.SaveChangesAsync();

        return (
            output.Data.ConnectedWabas,
            forbiddenWabas
                .Select(w => w.FacebookWabaName)
                .ToList());
    }

    public async Task<WhatsappCloudApiConfig> ConnectWabaPhoneNumber(
        string companyId,
        string channelName,
        string messagingHubWabaId,
        string messagingHubWabaPhoneNumberId,
        string pin = null)
    {
        var getoutput = await _wabasApi.WabasGetConnectedWhatsappCloudApiWabasPostAsync(
            getConnectedWhatsappCloudApiWabasInput: new GetConnectedWhatsappCloudApiWabasInput(companyId));

        var targetWaba = getoutput.Data.ConnectedWabas
            .FirstOrDefault(x => x.Id == messagingHubWabaId);

        var targetWabaPhoneNumber =
            targetWaba?.WabaDtoPhoneNumbers
                .FirstOrDefault(x => x.Id == messagingHubWabaPhoneNumberId);

        if (targetWaba == null ||
            targetWabaPhoneNumber == null)
        {
            throw new SleekflowUiException("Waba & Phone Number not found.");
        }

        if (await _appDbContext.ConfigWhatsappCloudApiConfigs
                .AnyAsync(x => x.FacebookPhoneNumberId == targetWabaPhoneNumber.FacebookPhoneNumberId))
        {
            throw new SleekflowUiException("Phone Number already connected.");
        }

        var webhookUrl = WhatsappCloudApiWebhookHelper.GeneratedWebhookUrl(
            _configuration["Values:DomainName"],
            companyId,
            messagingHubWabaPhoneNumberId);

        var output = await _channelsApi.ChannelsConnectWhatsappCloudApiChannelPostAsync(
            connectWhatsappCloudApiChannelInput: new ConnectWhatsappCloudApiChannelInput(
                companyId,
                messagingHubWabaId,
                messagingHubWabaPhoneNumberId,
                webhookUrl,
                pin));

        var connectedWabaPhoneNumber = output.Data
            .ConnectedCloudApiChannel
            .ConnectedApiChannels
            .FirstOrDefault(x => x.Id == messagingHubWabaPhoneNumberId);

        var config = new WhatsappCloudApiConfig(
            companyId,
            channelName,
            targetWaba,
            connectedWabaPhoneNumber);

        _appDbContext.ConfigWhatsappCloudApiConfigs.Add(config);

        await _appDbContext.SaveChangesAsync();

        BackgroundJob.Enqueue<IWhatsappCloudApiService>(
            x => x.AddBackWhatsappCloudApiSenderToUserProfile(companyId));

        BackgroundJob.Enqueue<IWhatsappCloudApiService>(
            x => x.MigrateWhatsApp360DialogDataToCloudApiAsync(companyId));

        var existingChannelWithOptinConfig = await _appDbContext.ConfigWhatsappCloudApiConfigs
            .AsNoTracking()
            .FirstOrDefaultAsync(
                x =>
                    x.CompanyId == config.CompanyId &&
                    x.FacebookWabaId == config.FacebookWabaId &&
                    x.IsOptInEnable);

        if (existingChannelWithOptinConfig != null)
        {
            config.IsOptInEnable = existingChannelWithOptinConfig.IsOptInEnable;
            config.OptInConfig = existingChannelWithOptinConfig.OptInConfig;

            await _appDbContext.SaveChangesAsync();
        }

        if (await _appDbContext.CompanySandboxes
                .AnyAsync(x => x.CompanyId == companyId))
        {
            BackgroundJob.Enqueue<ICompanyService>(
                x => x.DeleteSandbox(companyId));
        }

        await _companyInfoCacheService.RemoveCompanyInfoCache(companyId);

        return config;
    }

    public async Task<WhatsappCloudApiConfig> UpdateWabaPhoneNumber(
        string companyId,
        string channelName,
        string messagingHubWabaId,
        string messagingHubWabaPhoneNumberId)
    {
        var config = await _appDbContext.ConfigWhatsappCloudApiConfigs
            .FirstOrDefaultAsync(
                x => x.CompanyId == companyId &&
                     x.MessagingHubWabaId == messagingHubWabaId &&
                     x.MessagingHubWabaPhoneNumberId == messagingHubWabaPhoneNumberId);

        if (config == null)
        {
            throw new SleekflowUiException("Channel does not exist.");
        }

        config.ChannelName = channelName;

        await _appDbContext.SaveChangesAsync();

        await _companyInfoCacheService.RemoveCompanyInfoCache(companyId);

        return config;
    }

    public async Task DisconnectWabaPhoneNumber(
        string companyId,
        string messagingHubWabaId,
        string messagingHubWabaPhoneNumberId)
    {
        var config = await _appDbContext.ConfigWhatsappCloudApiConfigs
            .FirstOrDefaultAsync(
                x =>
                    x.CompanyId == companyId &&
                    x.MessagingHubWabaId == messagingHubWabaId &&
                    x.MessagingHubWabaPhoneNumberId == messagingHubWabaPhoneNumberId);

        if (config == null)
        {
            throw new SleekflowUiException("Whatsapp Cloud Api channel not found.");
        }

        var whatsappPhoneNumberToBeRemoved = config.WhatsappPhoneNumber;

        var output =
            await _channelsApi.ChannelsDisconnectWhatsappCloudApiChannelPostAsync(
                disconnectWhatsappCloudApiChannelInput: new DisconnectWhatsappCloudApiChannelInput(
                    companyId,
                    config.MessagingHubWabaId,
                    config.MessagingHubWabaPhoneNumberId));

        _appDbContext.ConfigWhatsappCloudApiConfigs.Remove(config);
        await _appDbContext.SaveChangesAsync();

        await _companyInfoCacheService.RemoveCompanyInfoCache(companyId);

        BackgroundJob.Enqueue<IWhatsappCloudApiService>(
            x =>
                x.ReplaceWhatsappCloudApiSender(
                    companyId,
                    whatsappPhoneNumberToBeRemoved));

        if (!await _appDbContext.ConfigWhatsappCloudApiConfigs
                .AnyAsync(
                    x =>
                        x.CompanyId == companyId &&
                        x.MessagingHubWabaId == messagingHubWabaId))
        {
            BackgroundJob.Enqueue<IWhatsappCloudApiService>(
                x =>
                    x.DeleteWabaTemplateBookmarksAsync(
                        companyId,
                        messagingHubWabaId));
        }
    }

    public async Task ReconnectWabaPhoneNumber(
        string companyId,
        string messagingHubWabaId,
        string messagingHubWabaPhoneNumberId)
    {
        var config = await _appDbContext.ConfigWhatsappCloudApiConfigs
            .FirstOrDefaultAsync(
                x =>
                    x.CompanyId == companyId &&
                    x.MessagingHubWabaId == messagingHubWabaId &&
                    x.MessagingHubWabaPhoneNumberId == messagingHubWabaPhoneNumberId);

        if (config == null)
        {
            throw new SleekflowUiException("Whatsapp Cloud Api channel not found.");
        }

        var webhookUrl = WhatsappCloudApiWebhookHelper.GeneratedWebhookUrl(
            _configuration["Values:DomainName"],
            companyId,
            messagingHubWabaPhoneNumberId);

        var output =
            await _channelsApi.ChannelsReconnectWhatsappCloudApiChannelPostAsync(
                reconnectWhatsappCloudApiChannelInput: new ReconnectWhatsappCloudApiChannelInput(
                    companyId,
                    config.MessagingHubWabaId,
                    config.MessagingHubWabaPhoneNumberId,
                    webhookUrl));
    }

    public async Task<List<WhatsappCloudApiTemplateResponse>> GetTemplates(
        string companyId,
        string messagingHubWabaId,
        bool allowCache = true)
    {
        var templates = new List<WhatsappCloudApiTemplate>();
        var response = new List<WhatsappCloudApiTemplateResponse>();

        var cacheKey = WhatsappCloudApiCacheKeyHelper.GetTemplateResponseCacheKey(
            companyId,
            messagingHubWabaId);

        var bookmarkCacheKey = WhatsappCloudApiCacheKeyHelper.GetTemplateBookmarksCacheKey(
            companyId,
            messagingHubWabaId);

        if (allowCache)
        {
            var cache = await _cacheManagerService.GetCacheWithConstantKeyAsync(cacheKey);

            if (!string.IsNullOrEmpty(cache))
            {
                templates = JsonConvert.DeserializeObject<List<WhatsappCloudApiTemplate>>(cache);

                var cacheBookmarks = await _cacheManagerService.GetCacheWithConstantKeyAsync(bookmarkCacheKey);

                if (!string.IsNullOrEmpty(cacheBookmarks))
                {
                    var bookmarks =
                        JsonConvert.DeserializeObject<List<WhatsAppCloudApiTemplateBookmarkResponse>>(cacheBookmarks);

                    foreach (var template in templates)
                    {
                        if (bookmarks.Any(x => x.TemplateId == template.Id))
                        {
                            response.Add(
                                new WhatsappCloudApiTemplateResponse(
                                    template.Id,
                                    template.Name,
                                    template.Components,
                                    template.Category,
                                    template.Status,
                                    template.Language,
                                    template.RejectedReason,
                                    true));
                        }
                        else
                        {
                            response.Add(
                                new WhatsappCloudApiTemplateResponse(
                                    template.Id,
                                    template.Name,
                                    template.Components,
                                    template.Category,
                                    template.Status,
                                    template.Language,
                                    template.RejectedReason,
                                    false));
                        }
                    }

                    return response;
                }
            }
        }

        if (!templates.Any())
        {
            var output =
                await _templatesApi.TemplatesGetWhatsappCloudApiTemplatesPostAsync(
                    getWhatsappCloudApiTemplatesInput: new GetWhatsappCloudApiTemplatesInput(
                        messagingHubWabaId,
                        companyId));

            // filter sample templates
            templates = output.Data.MessageTemplates
                .Where(x => !WhatsappTemplateHelper.FacebookSampleTemplateNames.Contains(x.Name))
                .ToList();

            await _cacheManagerService.SaveCacheWithConstantKeyAsync(
                cacheKey,
                templates,
                TimeSpan.FromMinutes(30),
                new JsonSerializerSettings
                {
                    DateTimeZoneHandling = DateTimeZoneHandling.Utc,
                    NullValueHandling = NullValueHandling.Ignore,
                    ContractResolver = new CamelCasePropertyNamesContractResolver()
                });
        }

        var bookmarkedTemplates = await _appDbContext.WhatsAppCloudApiTemplateBookmarks
            .Where(
                x =>
                    x.CompanyId == companyId &&
                    x.MessagingHubWabaId == messagingHubWabaId)
            .ToListAsync();

        foreach (var template in templates)
        {
            response.Add(
                new WhatsappCloudApiTemplateResponse(
                    template.Id,
                    template.Name,
                    template.Components,
                    template.Category,
                    template.Status,
                    template.Language,
                    template.RejectedReason,
                    false));
        }

        var updatedBookmarks = new List<WhatsAppCloudApiTemplateBookmarkResponse>();

        // add IsTemplateBookmarked to response and update the data in bookmarks
        foreach (var bookmarkedTemplate in bookmarkedTemplates)
        {
            var bookmarkedResult = response
                .FirstOrDefault(
                    x =>
                        x.Id == bookmarkedTemplate.TemplateId &&
                        x.Name == bookmarkedTemplate.TemplateName &&
                        x.Language == bookmarkedTemplate.TemplateLanguage);

            if (bookmarkedResult != null)
            {
                response.First(
                    x =>
                        x.Id == bookmarkedTemplate.TemplateId &&
                        x.Name == bookmarkedTemplate.TemplateName &&
                        x.Language == bookmarkedTemplate.TemplateLanguage).IsTemplateBookmarked = true;

                bookmarkedTemplate.TemplateName = bookmarkedResult.Name;
                bookmarkedTemplate.TemplateLanguage = bookmarkedResult.Language;
                updatedBookmarks.Add(_mapper.Map<WhatsAppCloudApiTemplateBookmarkResponse>(bookmarkedTemplate));
            }
            else
            {
                _appDbContext.WhatsAppCloudApiTemplateBookmarks.Remove(bookmarkedTemplate);
            }

            await _appDbContext.SaveChangesAsync();
        }

        if (updatedBookmarks.Any())
        {
            await _cacheManagerService.SaveCacheWithConstantKeyAsync(
                bookmarkCacheKey,
                updatedBookmarks,
                TimeSpan.FromMinutes(30),
                new JsonSerializerSettings
                {
                    DateTimeZoneHandling = DateTimeZoneHandling.Utc,
                    NullValueHandling = NullValueHandling.Ignore,
                    ContractResolver = new CamelCasePropertyNamesContractResolver()
                });
        }

        return response;
    }

    public async Task<string> CreateTemplate(
        string companyId,
        string messagingHubWabaId,
        WhatsappCloudApiCreateTemplateObject createTemplateObject)
    {
        var response =
            await _templatesApi.TemplatesCreateWhatsappCloudApiTemplatePostAsync(
                createWhatsappCloudApiTemplateInput: new CreateWhatsappCloudApiTemplateInput(
                    messagingHubWabaId,
                    companyId,
                    createTemplateObject));

        var templateCacheKey = WhatsappCloudApiCacheKeyHelper.GetTemplateResponseCacheKey(
            companyId,
            messagingHubWabaId);

        await _cacheManagerService.DeleteCacheWithConstantKeyAsync(templateCacheKey);

        return response.Data.Id;
    }

    public async Task<bool> UpdateTemplate(
        string companyId,
        string messagingHubWabaId,
        string templateId,
        List<WhatsappCloudApiTemplateComponentObject> templateComponents)
    {
        var response =
            await _templatesApi.TemplatesEditWhatsappCloudApiTemplatePostAsync(
                editWhatsappCloudApiTemplateInput: new EditWhatsappCloudApiTemplateInput(
                    messagingHubWabaId,
                    companyId,
                    templateId,
                    templateComponents));

        var templateCacheKey = WhatsappCloudApiCacheKeyHelper.GetTemplateResponseCacheKey(
            companyId,
            messagingHubWabaId);

        await _cacheManagerService.DeleteCacheWithConstantKeyAsync(templateCacheKey);

        return response.Data.Success;
    }

    public async Task<bool> DeleteTemplate(string companyId, string messagingHubWabaId, string templateName)
    {
        var response =
            await _templatesApi.TemplatesDeleteWhatsappCloudApiTemplatePostAsync(
                deleteWhatsappCloudApiTemplateInput: new DeleteWhatsappCloudApiTemplateInput(
                    messagingHubWabaId,
                    companyId,
                    templateName));

        var templateCacheKey = WhatsappCloudApiCacheKeyHelper.GetTemplateResponseCacheKey(
            companyId,
            messagingHubWabaId);

        await _cacheManagerService.DeleteCacheWithConstantKeyAsync(templateCacheKey);

        var bookmarkedTemplate = await _appDbContext.WhatsAppCloudApiTemplateBookmarks
            .Where(
                x =>
                    x.CompanyId == companyId &&
                    x.MessagingHubWabaId == messagingHubWabaId &&
                    x.TemplateName == templateName)
            .FirstOrDefaultAsync();

        if (bookmarkedTemplate != null)
        {
            _appDbContext.WhatsAppCloudApiTemplateBookmarks.Remove(bookmarkedTemplate);
        }

        var configs = await _appDbContext.ConfigWhatsappCloudApiConfigs
            .Where(c => c.CompanyId == companyId)
            .Where(x=> x.MessagingHubWabaId == messagingHubWabaId)
            .ToListAsync();

        configs
            .Where(x=>x.OptInConfig!=null)
            .Where(x=>x.OptInConfig.TemplateName==templateName)
            .ToList()
            .ForEach(
            x =>
            {
                x.OptInConfig = null;
                x.IsOptInEnable = false;
            });

        await _appDbContext.SaveChangesAsync();
        return response.Data.Success;
    }

    public async Task<Dictionary<string, string>> RequestUploadTemplateFileUrlAsync(
        string companyId,
        string messagingHubWabaId)
    {
        try
        {
            if (!await _appDbContext.ConfigWhatsappCloudApiConfigs
                    .AnyAsync(
                        x =>
                            x.CompanyId == companyId &&
                            x.MessagingHubWabaId == messagingHubWabaId))
            {
                throw new SleekflowUiException("unable to find the waba connected");
            }

            var getUploadMediaLinkOutputOutput =
                await _mediasApi.MediasGetUploadMediaLinkPostAsync(body: new object());

            if (!getUploadMediaLinkOutputOutput.Success ||
                getUploadMediaLinkOutputOutput.Data == null)
            {
                throw new SleekflowUiException(
                    JsonConvert.SerializeObject(
                        getUploadMediaLinkOutputOutput.Message));
            }

            return new Dictionary<string, string>()
            {
                {
                    getUploadMediaLinkOutputOutput.Data.BlobSasUrl.BlobId,
                    getUploadMediaLinkOutputOutput.Data.BlobSasUrl.Url
                }
            };
        }
        catch (SleekflowErrorCodeException ex)
        {
            throw new SleekflowUiException(ex.ToString());
        }
    }

    public async Task<Dictionary<string, string>> RetrieveTemplateFileHeaderHandleAsync(
        string companyId,
        string messagingHubWabaId,
        string blobId)
    {
        try
        {
            var uploadTemplateHeaderFileOutputOutput =
                await _templatesApi.TemplatesUploadTemplateHeaderFilePostAsync(
                    uploadTemplateHeaderFileInput: new UploadTemplateHeaderFileInput(
                        companyId,
                        messagingHubWabaId,
                        blobId));

            if (!uploadTemplateHeaderFileOutputOutput.Success ||
                uploadTemplateHeaderFileOutputOutput.Data == null)
            {
                throw new SleekflowUiException("The object doesn't exist or ObjectId Blob content not found");
            }

            return new Dictionary<string, string>()
            {
                {
                    uploadTemplateHeaderFileOutputOutput.Data.HeaderHandle,
                    uploadTemplateHeaderFileOutputOutput.Data.FileUrl
                }
            };
        }
        catch (SleekflowErrorCodeException ex)
        {
            throw new SleekflowUiException(ex.ToString());
        }
    }

    public async Task<bool> AddTemplateBookmarkAsync(
        string companyId,
        string messagingHubWabaId,
        string templateId,
        string templateName,
        string templateLanguage)
    {
        var key = $"{companyId}_{messagingHubWabaId}_{templateId}_bookmark";

        ILockService.Lock myLock = null;

        while (true)
        {
            myLock = await _lockService.AcquireLockAsync(
                key,
                TimeSpan.FromSeconds(2));

            if (myLock == null)
            {
                await Task.Delay(TimeSpan.FromSeconds(3));
            }
            else
            {
                break;
            }
        }

        var existingBookmark = await _appDbContext.WhatsAppCloudApiTemplateBookmarks
            .FirstOrDefaultAsync(
                x =>
                    x.CompanyId == companyId &&
                    x.MessagingHubWabaId == messagingHubWabaId &&
                    x.TemplateId == templateId);

        if (existingBookmark != null)
        {
            existingBookmark.TemplateName = templateName;
            existingBookmark.TemplateLanguage = templateLanguage;
            existingBookmark.UpdatedAt = DateTime.UtcNow;

            await _appDbContext.SaveChangesAsync();

            throw new SleekflowUiException("template already bookmarked");
        }

        var templateBookmark = new WhatsAppCloudApiTemplateBookmark()
        {
            CompanyId = companyId,
            MessagingHubWabaId = messagingHubWabaId,
            TemplateId = templateId,
            TemplateName = templateName,
            TemplateLanguage = templateLanguage,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        await _appDbContext.WhatsAppCloudApiTemplateBookmarks.AddAsync(templateBookmark);
        var response = await _appDbContext.SaveChangesAsync() > 0;

        await _lockService.ReleaseLockAsync(myLock);

        var cacheKey = WhatsappCloudApiCacheKeyHelper.GetTemplateBookmarksCacheKey(
            companyId,
            messagingHubWabaId);

        await _cacheManagerService.DeleteCacheWithConstantKeyAsync(cacheKey);

        return response;
    }

    public async Task<bool> DeleteTemplateBookmarkAsync(string companyId, string messagingHubWabaId, string templateId)
    {
        var templateBookmark = await _appDbContext.WhatsAppCloudApiTemplateBookmarks
            .FirstOrDefaultAsync(
                x =>
                    x.CompanyId == companyId &&
                    x.MessagingHubWabaId == messagingHubWabaId &&
                    x.TemplateId == templateId);

        if (templateBookmark == null)
        {
            throw new SleekflowUiException("template is not bookmarked");
        }

        _appDbContext.WhatsAppCloudApiTemplateBookmarks.Remove(templateBookmark);

        var response = await _appDbContext.SaveChangesAsync() > 0;

        var cacheKey = WhatsappCloudApiCacheKeyHelper.GetTemplateBookmarksCacheKey(
            companyId,
            messagingHubWabaId);

        await _cacheManagerService.DeleteCacheWithConstantKeyAsync(cacheKey);

        var configs = await _appDbContext.ConfigWhatsappCloudApiConfigs
            .Where(c => c.CompanyId == companyId && c.MessagingHubWabaId == messagingHubWabaId)
            .ToListAsync();

        foreach (var whatsappCloudApiConfig in configs)
        {
            await _messageTemplateHooks.OnMessageTemplateUnbookmarkedAsync(
                companyId,
                templateId,
                ChannelTypes.WhatsappCloudApi,
                whatsappCloudApiConfig.ChannelIdentityId,
                templateBookmark.TemplateName);
        }

        return response;
    }

    public async Task<bool> DeleteWabaTemplateBookmarksAsync(string companyId, string messagingHubWabaId)
    {
        var templateBookmarks = await _appDbContext.WhatsAppCloudApiTemplateBookmarks
            .Where(
                x =>
                    x.CompanyId == companyId &&
                    x.MessagingHubWabaId == messagingHubWabaId)
            .ToListAsync();

        if (templateBookmarks != null &&
            templateBookmarks.Any())
        {
            var cacheKey = WhatsappCloudApiCacheKeyHelper.GetTemplateBookmarksCacheKey(
                companyId,
                messagingHubWabaId);

            await _cacheManagerService.DeleteCacheWithConstantKeyAsync(cacheKey);

            _appDbContext.RemoveRange(templateBookmarks);

            return await _appDbContext.SaveChangesAsync() > 0;
        }

        return false;
    }

    public async Task RefreshWhatsappCloudApiConfigsDetail(List<string> companyIds = null)
    {
        var configs = await _appDbContext.ConfigWhatsappCloudApiConfigs
            .WhereIf(
                companyIds != null,
                x => companyIds.Contains(x.CompanyId))
            .ToListAsync();

        var connectedCloudApiCompanyIds = configs
            .Select(x => x.CompanyId)
            .Distinct()
            .ToList();

        foreach (var companyId in configs
                     .Select(x => x.CompanyId)
                     .Distinct()
                     .ToList())
        {
            try
            {
                var output = await
                    _wabasApi.WabasGetConnectedWhatsappCloudApiWabasPostAsync(
                        getConnectedWhatsappCloudApiWabasInput:
                        new GetConnectedWhatsappCloudApiWabasInput(
                            sleekflowCompanyId: companyId,
                            shouldRefresh: true));

                foreach (var wabaDto in output.Data.ConnectedWabas)
                {
                    foreach (var whatsappCloudApiConfig in configs
                                 .Where(x => x.MessagingHubWabaId == wabaDto.Id))
                    {
                        whatsappCloudApiConfig.SetWabaDetail(wabaDto);
                        _appDbContext.Entry(whatsappCloudApiConfig).Property(x => x.Waba).IsModified = true;
                    }

                    foreach (var wabaPhoneNumberDto in wabaDto.WabaDtoPhoneNumbers)
                    {
                        foreach (var whatsappCloudApiConfig in configs.Where(
                                     x => x.MessagingHubWabaPhoneNumberId == wabaPhoneNumberDto.Id))
                        {
                            whatsappCloudApiConfig.SetWabaPhoneNumberDetail(wabaPhoneNumberDto);

                            _appDbContext.Entry(whatsappCloudApiConfig).Property(x => x.WabaPhoneNumber).IsModified =
                                true;
                        }
                    }
                }
            }
            catch (Exception e)
            {
                _logger.LogError(
                    e,
                    "WhatsApp Cloud API RefreshConfigDetail error for company {CompanyId}. {ExceptionMessage}",
                    companyId,
                    e.Message);
            }
        }

        await _appDbContext.SaveChangesAsync();

        // Remove Company Cache
        foreach (var companyId in connectedCloudApiCompanyIds)
        {
            await _companyInfoCacheService.RemoveCompanyInfoCache(companyId);
        }
    }

    public async Task RegisterDefaultTemplates(string companyId, string messagingHubWabaId)
    {
        var company = await _appDbContext.CompanyCompanies
            .FirstOrDefaultAsync(x => x.Id == companyId);

        if (company == null)
        {
            return;
        }

        var listOfTemplatesToBeRegistered = new List<WhatsappCloudApiCreateTemplateObject>()
        {
            new ()
            {
                Category = WhatsappCloudApiTemplateCategoryConst.UTILITY,
                Name = "optin_1",
                Language = WhatsappCloudApiLanguageConst.English,
                Components = new List<WhatsappCloudApiTemplateComponentObject>
                {
                    new ()
                    {
                        Type = WhatsappCloudApiTemplateComponentTypeConst.BODY,
                        Text = $"*Notification*\nHello there! You received a new message from {company.CompanyName}.",
                    },
                    new ()
                    {
                        Type = WhatsappCloudApiTemplateComponentTypeConst.BUTTONS,
                        Buttons = new List<TemplateButtonObject>
                        {
                            new ()
                            {
                                Type = WhatsappCloudApiTemplateButtonTypeConst.QUICK_REPLY, Text = "Read more"
                            }
                        }
                    },
                }
            },
            new ()
            {
                Category = WhatsappCloudApiTemplateCategoryConst.UTILITY,
                Name = "optin_1",
                Language = WhatsappCloudApiLanguageConst.Chinese_HK,
                Components = new List<WhatsappCloudApiTemplateComponentObject>
                {
                    new ()
                    {
                        Type = WhatsappCloudApiTemplateComponentTypeConst.BODY,
                        Text = $"*新訊息通知*\n你好! 你收到來自{company.CompanyName}的新訊息。",
                    },
                    new ()
                    {
                        Type = WhatsappCloudApiTemplateComponentTypeConst.BUTTONS,
                        Buttons = new List<TemplateButtonObject>
                        {
                            new ()
                            {
                                Type = WhatsappCloudApiTemplateButtonTypeConst.QUICK_REPLY, Text = "查看更多"
                            }
                        }
                    }
                }
            },
            new ()
            {
                Category = WhatsappCloudApiTemplateCategoryConst.UTILITY,
                Name = "optin_2",
                Language = WhatsappCloudApiLanguageConst.English,
                Components = new List<WhatsappCloudApiTemplateComponentObject>
                {
                    new ()
                    {
                        Type = WhatsappCloudApiTemplateComponentTypeConst.BODY,
                        Text = $"Hello there! You received an update from {company.CompanyName}.",
                    },
                    new ()
                    {
                        Type = WhatsappCloudApiTemplateComponentTypeConst.BUTTONS,
                        Buttons = new List<TemplateButtonObject>
                        {
                            new ()
                            {
                                Type = WhatsappCloudApiTemplateButtonTypeConst.QUICK_REPLY, Text = "Read more"
                            }
                        }
                    },
                }
            },
            new ()
            {
                Category = WhatsappCloudApiTemplateCategoryConst.UTILITY,
                Name = "optin_2",
                Language = WhatsappCloudApiLanguageConst.Chinese_HK,
                Components = new List<WhatsappCloudApiTemplateComponentObject>
                {
                    new ()
                    {
                        Type = WhatsappCloudApiTemplateComponentTypeConst.BODY,
                        Text = $"你好! 以下為一則來自{company.CompanyName}的新訊息。",
                    },
                    new ()
                    {
                        Type = WhatsappCloudApiTemplateComponentTypeConst.BUTTONS,
                        Buttons = new List<TemplateButtonObject>
                        {
                            new ()
                            {
                                Type = WhatsappCloudApiTemplateButtonTypeConst.QUICK_REPLY, Text = "查看更多"
                            }
                        }
                    }
                }
            },
            new ()
            {
                Category = WhatsappCloudApiTemplateCategoryConst.MARKETING,
                Name = "christmas_promotion_sample",
                Language = WhatsappCloudApiLanguageConst.English,
                Components = new List<WhatsappCloudApiTemplateComponentObject>
                {
                    new ()
                    {
                        Type = WhatsappCloudApiTemplateComponentTypeConst.BODY,
                        Text = "Hello {{1}}, Need a gift to impress someone special?\n\n" +
                               "Christmas is here, and our prices are falling. " +
                               "Get up to 50% discount on top brands and styles that look good and make you feel confident. " +
                               "Get free delivery if you order before 31st December. " +
                               "Flaunt your style before winter arrives. ",
                        Example = new ExampleObject()
                        {
                            BodyText = new List<List<string>>()
                            {
                                new ()
                                {
                                    "Customer"
                                }
                            }
                        }
                    }
                }
            },
            new ()
            {
                Category = WhatsappCloudApiTemplateCategoryConst.MARKETING,
                Name = "greetings_1",
                Language = WhatsappCloudApiLanguageConst.English,
                Components = new List<WhatsappCloudApiTemplateComponentObject>
                {
                    new ()
                    {
                        Type = WhatsappCloudApiTemplateComponentTypeConst.BODY,
                        Text = "Hello {{1}}, thank you for your message!",
                        Example = new ExampleObject()
                        {
                            BodyText = new List<List<string>>()
                            {
                                new ()
                                {
                                    "Customer"
                                }
                            }
                        }
                    },
                }
            },
            new ()
            {
                Category = WhatsappCloudApiTemplateCategoryConst.MARKETING,
                Name = "greetings_1",
                Language = WhatsappCloudApiLanguageConst.Chinese_HK,
                Components = new List<WhatsappCloudApiTemplateComponentObject>
                {
                    new ()
                    {
                        Type = WhatsappCloudApiTemplateComponentTypeConst.BODY,
                        Text = "你好{{1}}，感謝你的詢問。",
                        Example = new ExampleObject()
                        {
                            BodyText = new List<List<string>>()
                            {
                                new ()
                                {
                                    "用戶", "客人"
                                }
                            }
                        }
                    },
                }
            },
            new ()
            {
                Category = WhatsappCloudApiTemplateCategoryConst.UTILITY,
                Name = "booking_confirm_1",
                Language = WhatsappCloudApiLanguageConst.English,
                Components = new List<WhatsappCloudApiTemplateComponentObject>
                {
                    new ()
                    {
                        Type = WhatsappCloudApiTemplateComponentTypeConst.BODY,
                        Text =
                            "Hello {{1}}. Your booking has been confirmed\n\nHere are your booking details:\nDate: {{2}}\nTime: {{3}}\n\nWe look forward to seeing you, thanks!",
                        Example = new ExampleObject()
                        {
                            BodyText = new List<List<string>>()
                            {
                                new ()
                                {
                                    "John", "12 June 2024", "3pm"
                                },
                            }
                        }
                    },
                }
            },
            new ()
            {
                Category = WhatsappCloudApiTemplateCategoryConst.UTILITY,
                Name = "booking_confirm_1",
                Language = WhatsappCloudApiLanguageConst.Chinese_HK,
                Components = new List<WhatsappCloudApiTemplateComponentObject>
                {
                    new ()
                    {
                        Type = WhatsappCloudApiTemplateComponentTypeConst.BODY,
                        Text =
                            "你好{{1}}，你的預約已被確認。\n\n以下是預約詳情：\n日期：{{2}}\n時間：{{3}}\n\n期待與你見面，感謝！",
                        Example = new ExampleObject()
                        {
                            BodyText = new List<List<string>>()
                            {
                                new ()
                                {
                                    "John", "2024 年 6 月 12 日", "3pm"
                                },
                            }
                        }
                    },
                }
            },
            new ()
            {
                Category = WhatsappCloudApiTemplateCategoryConst.UTILITY,
                Name = "booking_confirm_1",
                Language = WhatsappCloudApiLanguageConst.Chinese_CN,
                Components = new List<WhatsappCloudApiTemplateComponentObject>
                {
                    new ()
                    {
                        Type = WhatsappCloudApiTemplateComponentTypeConst.BODY,
                        Text =
                            "你好{{1}}，你的预约已被确认。\n\n以下是预约详情：\n日期：{{2}}\n时间：{{3}}\n\n期待与你见面，感谢！",
                        Example = new ExampleObject()
                        {
                            BodyText = new List<List<string>>()
                            {
                                new ()
                                {
                                    "John", "2024 年 6 月 12 日", "3pm"
                                },
                            }
                        }
                    },
                }
            },
            new ()
            {
                Category = WhatsappCloudApiTemplateCategoryConst.UTILITY,
                Name = "doc_reminder_1",
                Language = WhatsappCloudApiLanguageConst.English,
                Components = new List<WhatsappCloudApiTemplateComponentObject>
                {
                    new ()
                    {
                        Type = WhatsappCloudApiTemplateComponentTypeConst.BODY,
                        Text =
                            "Hello {{1}}. Thank you for choosing our service.\n\n{{2}}, thank you!",
                        Example = new ExampleObject()
                        {
                            BodyText = new List<List<string>>()
                            {
                                new ()
                                {
                                    "John",
                                    "Please bring along your documentation for registration next time when you visit our store"
                                },
                            }
                        }
                    },
                }
            },
            new ()
            {
                Category = WhatsappCloudApiTemplateCategoryConst.UTILITY,
                Name = "doc_reminder_1",
                Language = WhatsappCloudApiLanguageConst.Chinese_HK,
                Components = new List<WhatsappCloudApiTemplateComponentObject>
                {
                    new ()
                    {
                        Type = WhatsappCloudApiTemplateComponentTypeConst.BODY,
                        Text =
                            "你好{{1}}，感謝你選擇我們的服務。\n\n{{2}}，感謝！",
                        Example = new ExampleObject()
                        {
                            BodyText = new List<List<string>>()
                            {
                                new ()
                                {
                                    "John", "下次前來到訪我們分店時，請攜帶文件完成註冊手續"
                                },
                            }
                        }
                    },
                }
            },
            new ()
            {
                Category = WhatsappCloudApiTemplateCategoryConst.UTILITY,
                Name = "doc_reminder_1",
                Language = WhatsappCloudApiLanguageConst.Chinese_CN,
                Components = new List<WhatsappCloudApiTemplateComponentObject>
                {
                    new ()
                    {
                        Type = WhatsappCloudApiTemplateComponentTypeConst.BODY,
                        Text =
                            "你好{{1}}，感谢你选择我们的服务。\n\n{{2}}，感谢！",
                        Example = new ExampleObject()
                        {
                            BodyText = new List<List<string>>()
                            {
                                new ()
                                {
                                    "John", "下次前来到访我们分店时，请携带文件完成注册手续"
                                },
                            }
                        }
                    },
                }
            },
            new ()
            {
                Category = WhatsappCloudApiTemplateCategoryConst.UTILITY,
                Name = "regular_notice_1",
                Language = WhatsappCloudApiLanguageConst.English,
                Components = new List<WhatsappCloudApiTemplateComponentObject>
                {
                    new ()
                    {
                        Type = WhatsappCloudApiTemplateComponentTypeConst.BODY,
                        Text =
                            "Hello {{1}}. Please be informed that {{2}}. Thank you!",
                        Example = new ExampleObject()
                        {
                            BodyText = new List<List<string>>()
                            {
                                new ()
                                {
                                    "John", "Our branch will be closed on the coming Sunday for regular maintenance"
                                },
                            }
                        }
                    },
                }
            },
            new ()
            {
                Category = WhatsappCloudApiTemplateCategoryConst.UTILITY,
                Name = "regular_notice_1",
                Language = WhatsappCloudApiLanguageConst.Chinese_HK,
                Components = new List<WhatsappCloudApiTemplateComponentObject>
                {
                    new ()
                    {
                        Type = WhatsappCloudApiTemplateComponentTypeConst.BODY,
                        Text =
                            "你好{{1}}，請注意{{2}}。感謝！",
                        Example = new ExampleObject()
                        {
                            BodyText = new List<List<string>>()
                            {
                                new ()
                                {
                                    "John", "本分店將於下星期日關閉以進行定期維修"
                                },
                            }
                        }
                    },
                }
            },
            new ()
            {
                Category = WhatsappCloudApiTemplateCategoryConst.UTILITY,
                Name = "regular_notice_1",
                Language = WhatsappCloudApiLanguageConst.Chinese_CN,
                Components = new List<WhatsappCloudApiTemplateComponentObject>
                {
                    new ()
                    {
                        Type = WhatsappCloudApiTemplateComponentTypeConst.BODY,
                        Text =
                            "你好{{1}}，請注意{{2}}。感谢！",
                        Example = new ExampleObject()
                        {
                            BodyText = new List<List<string>>()
                            {
                                new ()
                                {
                                    "John", "本分店将于下星期天关闭以进行定期维修"
                                },
                            }
                        }
                    },
                }
            },
            // new()
            // {
            //     Category = WhatsappCloudApiTemplateCategoryConst.MARKETING,
            //     Name = "greetings_2",
            //     Language = WhatsappCloudApiLanguageConst.English,
            //     Components = new List<WhatsappCloudApiTemplateComponentObject>
            //     {
            //         new()
            //         {
            //             Type = WhatsappCloudApiTemplateComponentTypeConst.BODY,
            //             Text = "Hello {{1}},\n\nThanks for contacting, {{2}} thank you.",
            //         },
            //     }
            // },
            // new()
            // {
            //     Category = WhatsappCloudApiTemplateCategoryConst.MARKETING,
            //     Name = "greetings_2",
            //     Language = WhatsappCloudApiLanguageConst.Chinese_HK,
            //     Components = new List<WhatsappCloudApiTemplateComponentObject>
            //     {
            //         new()
            //         {
            //             Type = WhatsappCloudApiTemplateComponentTypeConst.BODY,
            //             Text = "你好{{1}},\n\n感謝您聯繫我們，{{2}} 謝謝。",
            //         },
            //     }
            // },
            // new()
            // {
            //     Category = WhatsappCloudApiTemplateCategoryConst.UTILITY,
            //     Name = "optin_3",
            //     Language = WhatsappCloudApiLanguageConst.English,
            //     Components = new List<WhatsappCloudApiTemplateComponentObject>
            //     {
            //         new()
            //         {
            //             Type = WhatsappCloudApiTemplateComponentTypeConst.BODY,
            //             Text = $"*Notification*\nHello there! You received a new message from {company.CompanyName}.",
            //         },
            //         new()
            //         {
            //             Type = WhatsappCloudApiTemplateComponentTypeConst.BUTTONS,
            //             Buttons = new List<TemplateButtonObject>
            //             {
            //                 new()
            //                 {
            //                     Type = WhatsappCloudApiTemplateButtonTypeConst.QUICK_REPLY,
            //                     Text = "Read more"
            //                 }
            //             }
            //         },
            //     }
            // },
            // new()
            // {
            //     Category = WhatsappCloudApiTemplateCategoryConst.UTILITY,
            //     Name = "optin_3",
            //     Language = WhatsappCloudApiLanguageConst.Chinese_HK,
            //     Components = new List<WhatsappCloudApiTemplateComponentObject>
            //     {
            //         new()
            //         {
            //             Type = WhatsappCloudApiTemplateComponentTypeConst.BODY,
            //             Text = $"*新訊息通知*\n你好! 你收到來自{company.CompanyName}的新訊息。",
            //         },
            //         new()
            //         {
            //             Type = WhatsappCloudApiTemplateComponentTypeConst.BUTTONS,
            //             Buttons = new List<TemplateButtonObject>
            //             {
            //                 new()
            //                 {
            //                     Type = WhatsappCloudApiTemplateButtonTypeConst.QUICK_REPLY,
            //                     Text = "查看更多"
            //                 }
            //             }
            //         }
            //     }
            // },
            // new()
            // {
            //     Category = WhatsappCloudApiTemplateCategoryConst.UTILITY,
            //     Name = "optin_4",
            //     Language = WhatsappCloudApiLanguageConst.English,
            //     Components = new List<WhatsappCloudApiTemplateComponentObject>
            //     {
            //         new()
            //         {
            //             Type = WhatsappCloudApiTemplateComponentTypeConst.BODY,
            //             Text = $"Hello there! You received an update from {company.CompanyName}.",
            //         },
            //         new()
            //         {
            //             Type = WhatsappCloudApiTemplateComponentTypeConst.BUTTONS,
            //             Buttons = new List<TemplateButtonObject>
            //             {
            //                 new()
            //                 {
            //                     Type = WhatsappCloudApiTemplateButtonTypeConst.QUICK_REPLY,
            //                     Text = "Read more"
            //                 }
            //             }
            //         },
            //     }
            // },
            // new()
            // {
            //     Category = WhatsappCloudApiTemplateCategoryConst.UTILITY,
            //     Name = "optin_4",
            //     Language = WhatsappCloudApiLanguageConst.Chinese_HK,
            //     Components = new List<WhatsappCloudApiTemplateComponentObject>
            //     {
            //         new()
            //         {
            //             Type = WhatsappCloudApiTemplateComponentTypeConst.BODY,
            //             Text = $"你好! 以下為一則來自{company.CompanyName}的新訊息。",
            //         },
            //         new()
            //         {
            //             Type = WhatsappCloudApiTemplateComponentTypeConst.BUTTONS,
            //             Buttons = new List<TemplateButtonObject>
            //             {
            //                 new()
            //                 {
            //                     Type = WhatsappCloudApiTemplateButtonTypeConst.QUICK_REPLY,
            //                     Text = "查看更多"
            //                 }
            //             }
            //         }
            //     }
            // },
        };

        try
        {
            var uploadTemplateOutput =
                await _templatesApi.TemplatesUploadTemplateHeaderFilePostAsync(
                    uploadTemplateHeaderFileInput: new UploadTemplateHeaderFileInput(
                        companyId,
                        messagingHubWabaId,
                        _configuration.GetValue<String>("WhatsAppCloudApiTemplate:DefaultImageBlobId"),
                        "Internal"));

            if (uploadTemplateOutput.Data != null &&
                !string.IsNullOrEmpty(uploadTemplateOutput.Data.HeaderHandle))
            {
                listOfTemplatesToBeRegistered.AddRange(
                    new List<WhatsappCloudApiCreateTemplateObject>()
                    {
                        new ()
                        {
                            Category = WhatsappCloudApiTemplateCategoryConst.MARKETING,
                            Name = "greetings_1_image",
                            Language = WhatsappCloudApiLanguageConst.English,
                            Components = new List<WhatsappCloudApiTemplateComponentObject>
                            {
                                new ()
                                {
                                    Type = WhatsappCloudApiTemplateComponentTypeConst.HEADER,
                                    Format = "image",
                                    Example = new ExampleObject()
                                    {
                                        HeaderHandle = new List<string>()
                                        {
                                            uploadTemplateOutput.Data.HeaderHandle
                                        }
                                    }
                                },
                                new ()
                                {
                                    Type = WhatsappCloudApiTemplateComponentTypeConst.BODY,
                                    Text = "Hello {{1}}, thank you for your message!",
                                    Example = new ExampleObject()
                                    {
                                        BodyText = new List<List<string>>()
                                        {
                                            new List<string>()
                                            {
                                                "Sir"
                                            }
                                        }
                                    }
                                },
                            }
                        },
                        new ()
                        {
                            Category = WhatsappCloudApiTemplateCategoryConst.MARKETING,
                            Name = "greetings_1_image",
                            Language = WhatsappCloudApiLanguageConst.Chinese_HK,
                            Components = new List<WhatsappCloudApiTemplateComponentObject>
                            {
                                new ()
                                {
                                    Type = WhatsappCloudApiTemplateComponentTypeConst.HEADER,
                                    Format = "image",
                                    Example = new ExampleObject()
                                    {
                                        HeaderHandle = new List<string>()
                                        {
                                            uploadTemplateOutput.Data.HeaderHandle
                                        }
                                    }
                                },
                                new ()
                                {
                                    Type = WhatsappCloudApiTemplateComponentTypeConst.BODY,
                                    Text = "你好{{1}}，感謝你的詢問。",
                                    Example = new ExampleObject()
                                    {
                                        BodyText = new List<List<string>>()
                                        {
                                            new List<string>()
                                            {
                                                "先生"
                                            }
                                        }
                                    }
                                },
                            }
                        },
                    });
            }
        }
        catch (SleekflowErrorCodeException ex)
        {
            _logger.LogError(
                ex,
                "WhatsApp Cloud API register default template for company {CompanyId} waba {CloudApiWabaId} error: {ExceptionMessage}",
                companyId,
                messagingHubWabaId,
                ex.Message);
        }

        var output = await GetTemplates(
            companyId,
            messagingHubWabaId,
            false);

        var notRegisteredTemplate = listOfTemplatesToBeRegistered
            .Where(
                templateObject =>
                    !output.Any(
                        x =>
                            x.Name == templateObject.Name &&
                            x.Language == templateObject.Language))
            .ToList();

        foreach (var template in notRegisteredTemplate)
        {
            try
            {
                await _templatesApi.TemplatesCreateWhatsappCloudApiTemplatePostAsync(
                    createWhatsappCloudApiTemplateInput: new CreateWhatsappCloudApiTemplateInput(
                        messagingHubWabaId,
                        companyId,
                        template));
            }
            catch (SleekflowErrorCodeException ex)
            {
                _logger.LogError(
                    ex,
                    "Company {CompanyId} waba {CloudApiWabaId} register Cloud API Template {TemplateName} error: {ExceptionMessage}",
                    companyId,
                    messagingHubWabaId,
                    template.Name,
                    ex.Message);
            }
        }
    }

    public async Task ReplaceWhatsappCloudApiSender(string companyId, string removedWhatsappPhoneNumber)
    {
        var defaultCloudApiChannel = await _appDbContext.ConfigWhatsappCloudApiConfigs
            .AsNoTracking()
            .Where(x => x.CompanyId == companyId)
            .OrderBy(x => x.CreatedAt)
            .Select(
                x => new
                {
                    x.Id,
                    x.WhatsappPhoneNumber
                })
            .FirstOrDefaultAsync();

        if (defaultCloudApiChannel == null)
        {
            return;
        }

        _appDbContext.Database.SetCommandTimeout(120);

        await _appDbContext.WhatsappCloudApiSenders
            .Where(
                x =>
                    x.CompanyId == companyId &&
                    x.WhatsappChannelPhoneNumber == removedWhatsappPhoneNumber)
            .ExecuteUpdateAsync(
                calls => calls.SetProperty(
                    p => p.WhatsappChannelPhoneNumber,
                    defaultCloudApiChannel.WhatsappPhoneNumber));
    }

    public async Task AddBackWhatsappCloudApiSenderToUserProfile(string companyId)
    {
        const int chunkSize = 500;

        var defaultCloudApiChannel = await _appDbContext.ConfigWhatsappCloudApiConfigs
            .AsNoTracking()
            .Where(x => x.CompanyId == companyId)
            .OrderBy(x => x.CreatedAt)
            .Select(
                x => new
                {
                    x.Id,
                    x.WhatsappPhoneNumber
                })
            .FirstAsync();

        var userProfiles = await _appDbContext.UserProfiles
            .Where(x => x.CompanyId == companyId && x.PhoneNumber != null && x.WhatsappCloudApiUser == null)
            .Select(
                x => new
                {
                    UserProfileId = x.Id,
                    PhoneNumber = x.PhoneNumber,
                    ConversationId = x.Conversation.Id ?? null,
                })
            .ToListAsync();

        var contactsWithPhoneNumber = userProfiles
            .DistinctBy(x => x.PhoneNumber)
            .ToList();

        _appDbContext.Database.SetCommandTimeout(120);

        foreach (var userProfilesChucks in contactsWithPhoneNumber.Chunk(chunkSize))
        {
            try
            {
                var newSenders = userProfilesChucks
                    .Select(
                        x => new WhatsappCloudApiSender
                        {
                            CompanyId = companyId,
                            UserProfileId = x.UserProfileId,
                            ConversationId = x.ConversationId,
                            WhatsappId = PhoneNumberHelper.NormalizePhoneNumber(x.PhoneNumber),
                            WhatsappUserDisplayName = null,
                            WhatsappChannelPhoneNumber = defaultCloudApiChannel.WhatsappPhoneNumber
                        })
                    .ToList();

                var existedNumbers = await _appDbContext.WhatsappCloudApiSenders
                    .Where(
                        x =>
                            x.CompanyId == companyId &&
                            newSenders
                                .Select(b => b.WhatsappId)
                                .Contains(x.WhatsappId))
                    .Select(x => x.WhatsappId)
                    .ToListAsync();

                newSenders = newSenders
                    .Where(b => !existedNumbers.Contains(b.WhatsappId))
                    .ToList();

                _appDbContext.WhatsappCloudApiSenders.AddRange(newSenders);

                await _appDbContext.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Cloud Api add back sender error for user profiles {UserProfileIds}",
                    JsonConvert.SerializeObject(
                        userProfilesChucks
                            .Select(profile => profile.UserProfileId)
                            .ToList()));
            }
        }

        while (await _appDbContext.WhatsappCloudApiSenders
                   .AnyAsync(
                       x =>
                           x.CompanyId == companyId &&
                           x.WhatsappChannelPhoneNumber == null))
        {
            var updatedCount = await _appDbContext.WhatsappCloudApiSenders
                .Where(
                    x =>
                        x.CompanyId == companyId &&
                        x.WhatsappChannelPhoneNumber == null)
                .ExecuteUpdateAsync(
                    calls => calls.SetProperty(
                        p => p.WhatsappChannelPhoneNumber,
                        defaultCloudApiChannel.WhatsappPhoneNumber));

            if (updatedCount == 0)
            {
                break;
            }

            await _appDbContext.SaveChangesAsync();
        }
    }

    # region manual top-up

    public async Task<GetWhatsappCloudApiBusinessBalanceStripeTopUpPlansOutput> GetBusinessBalanceStripeTopUpPlans(
        string sleekflowCompanyId)
    {
        var output =
            await _balancesApi.BalancesGetWhatsappCloudApiBusinessBalanceStripeTopUpPlansPostAsync(
                getWhatsappCloudApiBusinessBalanceStripeTopUpPlansInput:
                new GetWhatsappCloudApiBusinessBalanceStripeTopUpPlansInput(
                    sleekflowCompanyId));

        return output.Data;
    }

    public async Task<GenerateWhatsappCloudApiBusinessBalanceStripeTopUpLinkOutput>
        GenerateBusinessBalanceStripeTopUpLink(
            string sleekflowCompanyId,
            string staffId,
            string? displayName,
            string email,
            string stripeTopUpPlanId,
            string facebookBusinessId,
            string? redirectToUrl,
            string? wabaId)
    {
        var billRecord = await _appDbContext.CompanyBillRecords
            .AsNoTracking()
            .OrderByDescending(x => x.created)
            .FirstOrDefaultAsync(
                x =>
                    x.CompanyId == sleekflowCompanyId &&
                    x.customer_email == email);

        var customerId = billRecord?.customerId;

        redirectToUrl ??= _configuration.GetValue<string>("Values:AppDomainName");

        var generateWhatsappCloudApiBusinessBalanceStripeTopUpLinkInput =
            new GenerateWhatsappCloudApiBusinessBalanceStripeTopUpLinkInput(
                sleekflowCompanyId,
                stripeTopUpPlanId,
                facebookBusinessId,
                wabaId,
                customerId,
                staffId,
                displayName,
                redirectToUrl);

        var response =
            await _balancesApi.BalancesGenerateWhatsappCloudApiBusinessBalanceStripeTopUpLinkPostAsync(
                generateWhatsappCloudApiBusinessBalanceStripeTopUpLinkInput:
                generateWhatsappCloudApiBusinessBalanceStripeTopUpLinkInput);

        return response.Data;
    }

    public async Task<TopUpWhatsappCloudApiBusinessBalanceOutputOutput> TopUp(
        string companyId,
        string uniqueId,
        string facebookBusinessId,
        string topUpMethod,
        Money credit,
        string creditedBy,
        string creditedByDisplayName,
        string resellerTransactionLogDetail,
        StripeTopUpCreditDetail stripeTopUpCreditDetail = null,
        Dictionary<string, object?> metadata = null)
    {
        // Verify the credit amount is not zero
        if (credit.Amount == 0)
        {
            return new TopUpWhatsappCloudApiBusinessBalanceOutputOutput(
                message: "Credit amount cannot be zero.");
        }

        var company = await _appDbContext.CompanyCompanies
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.Id == companyId);

        // Check if the company is a reseller client
        if (company is { CompanyType: CompanyType.ResellerClient })
        {
            var resellerProfile = await _appDbContext.ResellerClientCompanyProfiles
                .Where(x => x.ClientCompanyId == companyId)
                .Include(x => x.ResellerCompanyProfile)
                .Select(x => x.ResellerCompanyProfile)
                .FirstOrDefaultAsync();

            if (resellerProfile == null)
            {
                return new TopUpWhatsappCloudApiBusinessBalanceOutputOutput(
                    message: "Reseller profile not found");
            }

            // Top up whatsapp credit
            if (credit.Amount > 0)
            {
                if (resellerProfile.Balance - credit.Amount < 0)
                {
                    return new TopUpWhatsappCloudApiBusinessBalanceOutputOutput(
                        message:
                        $"Insufficient Balance, You have not enough balance to top-up WhatsApp Credit. " +
                        $"Balance: {resellerProfile.Balance:0.00}");
                }

                var whatsAppCreditTopUpTransactionLog = new ResellerTransactionLog
                {
                    ResellerCompanyProfileId = resellerProfile.Id,
                    Amount = credit.Amount,
                    Currency = resellerProfile.Currency,
                    ClientCompanyId = companyId,
                    TransactionMode = TransactionMode.Debit,
                    UserIdentityId = creditedBy,
                    TransactionCategory = ResellerTransactionCategory.WhatsAppCredit,
                    TransactionAction = "Add WhatsApp Credit",
                    Detail = resellerTransactionLogDetail,
                    InvoiceId = metadata?.GetValueOrDefault("invoice_id")?.ToString()
                };

                resellerProfile.Debited += credit.Amount;

                await _appDbContext.ResellerTransactionLogs.AddAsync(whatsAppCreditTopUpTransactionLog);
            }
            else
            {
                // Refund whatsapp credit to master balance
                var whatsAppCreditRefundTransactionLog = new ResellerTransactionLog
                {
                    ResellerCompanyProfileId = resellerProfile.Id,
                    Amount = credit.Amount,
                    Currency = resellerProfile.Currency,
                    ClientCompanyId = companyId,
                    TransactionMode = TransactionMode.Refund,
                    UserIdentityId = creditedBy,
                    TransactionCategory = ResellerTransactionCategory.WhatsAppCredit,
                    TransactionAction = "Refund WhatsApp Credit",
                    Detail = resellerTransactionLogDetail,
                    InvoiceId = metadata?.GetValueOrDefault("invoice_id")?.ToString()
                };

                resellerProfile.TopUp += credit.Amount;

                await _appDbContext.ResellerTransactionLogs.AddAsync(whatsAppCreditRefundTransactionLog);
            }
        }

        metadata ??= new Dictionary<string, object>();

        var result =
            await _balancesApi.BalancesTopUpWhatsappCloudApiBusinessBalancePostAsync(
                topUpWhatsappCloudApiBusinessBalanceInput: new TopUpWhatsappCloudApiBusinessBalanceInput(
                    uniqueId,
                    facebookBusinessId,
                    topUpMethod,
                    credit,
                    creditedBy,
                    creditedByDisplayName,
                    stripeTopUpCreditDetail!,
                    metadata));

        if (!result.Success)
        {
            return result;
        }

        await _appDbContext.SaveChangesAsync();

        return result;
    }

    # endregion

    # region auto top-up

    public async Task<UpsertWhatsappCloudApiBusinessBalanceAutoTopUpProfileOutput>
        UpsertBusinessBalanceAutoTopUpProfile(
            BusinessBalanceAutoTopUpProfileDto businessBalanceAutoTopUpProfileDto,
            string sleekflowCompanyId,
            long staffId,
            string email,
            string creditedBy,
            string creditedByDisplayName,
            string redirectToUrl,
            string phoneNumber)
    {
        var customerId = await _appDbContext.CompanyBillRecords
            .AsNoTracking()
            .Where(
                x => x.CompanyId == sleekflowCompanyId &&
                     x.stripe_subscriptionId != null)
            .OrderByDescending(x => x.created)
            .Select(x => x.customerId)
            .FirstOrDefaultAsync();

        var staffTeamIds = await _appDbContext.CompanyTeamMembers
            .AsNoTracking()
            .Where(y => y.StaffId == staffId)
            .Select(y => y.CompanyTeamId.ToString())
            .ToListAsync();

        var result =
            await _balancesApi.BalancesUpsertWhatsappCloudApiBusinessBalanceAutoTopUpProfilePostAsync(
                upsertWhatsappCloudApiBusinessBalanceAutoTopUpProfileInput: new
                    UpsertWhatsappCloudApiBusinessBalanceAutoTopUpProfileInput(
                        businessBalanceAutoTopUpProfileDto,
                        customerId,
                        sleekflowCompanyId,
                        staffId.ToString(),
                        creditedBy,
                        creditedByDisplayName,
                        redirectToUrl,
                        phoneNumber,
                        staffTeamIds));

        return result.Data;
    }

    public async Task<GetWhatsappCloudApiBusinessBalanceAutoTopUpProfileOutput> GetBusinessBalanceAutoTopUpProfile(
        string sleekflowCompanyId,
        string facebookBusinessId)
    {
        var result =
            await _balancesApi.BalancesGetWhatsappCloudApiBusinessBalanceAutoTopUpProfilePostAsync(
                getWhatsappCloudApiBusinessBalanceAutoTopUpProfileInput: new
                    GetWhatsappCloudApiBusinessBalanceAutoTopUpProfileInput(
                        sleekflowCompanyId,
                        facebookBusinessId));

        return result.Data;
    }

    public async Task<GetBusinessBalanceStripeTopUpInvoiceResponse> GetBusinessBalancesStripeTopUpInvoices(
        string sleekflowCompanyId,
        DateTime? start,
        DateTime? end,
        string continuationToken = null,
        int limit = 10)
    {
        var timeZoneId = await _appDbContext.CompanyCompanies
            .AsNoTracking()
            .Where(x => x.Id == sleekflowCompanyId)
            .Select(x => x.TimeZoneInfoId)
            .FirstOrDefaultAsync();

        var timeZoneInfo = TimeZoneInfo.FindSystemTimeZoneById(timeZoneId);

        DateTimeOffset? startDateTimeOffset = null;
        DateTimeOffset? endDateTimeOffset = null;

        if (start is not null && end is not null)
        {
            startDateTimeOffset = new DateTimeOffset(
                (DateTime)start,
                timeZoneInfo.GetUtcOffset((DateTime)start));

            endDateTimeOffset = new DateTimeOffset(
                ((DateTime)end).EndOfDay(),
                timeZoneInfo.GetUtcOffset(((DateTime)end).EndOfDay()));
        }

        var result =
            await _balancesApi.BalancesGetWhatsappCloudApiBusinessBalanceStripeTopUpInvoicesPostAsync(
                getWhatsappCloudApiBusinessBalanceStripeTopUpInvoicesInput:
                new GetWhatsappCloudApiBusinessBalanceStripeTopUpInvoicesInput(
                    sleekflowCompanyId,
                    startDateTimeOffset,
                    endDateTimeOffset,
                    limit,
                    continuationToken!));

        var output = result.Data;

        // Convert BusinessBalanceInvoice to BusinessBalanceInvoiceDto
        var businessBalanceInvoiceDto = output.Invoices
            .Select(
                x =>
                    new BusinessBalanceInvoiceDto
                    {
                        FacebookBusinessId = x.FacebookBusinessId,
                        FacebookBusinessName = x.FacebookBusinessName,
                        InvoicePdf = x.InvoicePdf,
                        InvoiceStatus = x.Status,
                        BillDate = x.CreatedAt,
                        PayAmount = x.PayAmount,
                        Description = x.Description
                    })
            .ToList();

        return new GetBusinessBalanceStripeTopUpInvoiceResponse(
            businessBalanceInvoiceDto,
            output.NextContinuationToken,
            output.Count);
    }

    public async Task<GetWhatsappCloudApiBusinessBalanceAutoTopUpProfileSettingsOutput>
        GetBusinessBalanceAutoTopUpProfileSettings(string sleekflowCompanyId)
    {
        var result =
            await _balancesApi.BalancesGetWhatsappCloudApiBusinessBalanceAutoTopUpProfileSettingsPostAsync(
                getWhatsappCloudApiBusinessBalanceAutoTopUpProfileSettingsInput:
                new GetWhatsappCloudApiBusinessBalanceAutoTopUpProfileSettingsInput(
                    sleekflowCompanyId));

        return result.Data;
    }

    # endregion

    public async Task<GetWhatsappCloudApiBusinessBalancesOutput> GetBusinessBalances(string sleekflowCompanyId)
    {
        var result =
            await _balancesApi.BalancesGetWhatsappCloudApiBusinessBalancesPostAsync(
                getWhatsappCloudApiBusinessBalancesInput:
                new GetWhatsappCloudApiBusinessBalancesInput(
                    sleekflowCompanyId));

        return result.Data;
    }

    public async Task<List<UserBusinessDto>> GetUserBusinesses(string companyId, string userAccessToken)
    {
        var userBusinessesOutput =
            await _migrationsApi.MigrationsGetWhatsappCloudApiUserBusinessesPostAsync(
                getWhatsappCloudApiUserBusinessesInput:
                new GetWhatsappCloudApiUserBusinessesInput(
                    companyId,
                    userAccessToken));

        return _mapper.Map<List<UserBusinessDto>>(userBusinessesOutput.Data.UserBusinesses);
    }

    public async Task<WabasByBspDto> GetUserBusinessWhatsappAccounts(
        string companyId,
        string businessId,
        string userAccessToken)
    {
        var wabasByBspDto = new WabasByBspDto()
        {
            SleekflowOnlyWabas = new List<BusinessWabaDto>(),
            OtherBspWabas = new List<BusinessWabaDto>()
        };
        var sleekflowFacebookClientId = _configuration.GetValue<String>("Facebook:ClientId");

        var wabasOutput =
            await _migrationsApi.MigrationsGetWhatsappCloudApiUserBusinessWabasPostAsync(
                getWhatsappCloudApiUserBusinessWabasInput:
                new GetWhatsappCloudApiUserBusinessWabasInput(
                    companyId,
                    userAccessToken,
                    businessId));

        if (wabasOutput.Success)
        {
            foreach (var userBusinessWaba in wabasOutput.Data.UserBusinessWabas)
            {
                if (userBusinessWaba.OnBehalfOfBusinessInfo.Id == businessId)
                {
                    if (userBusinessWaba.SubscribedApps != null &&
                        userBusinessWaba.SubscribedApps.Data.Count == 1 &&
                        userBusinessWaba.SubscribedApps.Data
                            .Any(x => x._WhatsappBusinessApiData.Id == sleekflowFacebookClientId))
                    {
                        var businessWabaDto = _mapper.Map<BusinessWabaDto>(userBusinessWaba);
                        businessWabaDto.BspName = "Sleekflow";
                        wabasByBspDto.SleekflowOnlyWabas.Add(businessWabaDto);
                    }
                    else
                    {
                        var businessWabaDto = _mapper.Map<BusinessWabaDto>(userBusinessWaba);

                        if (userBusinessWaba.SubscribedApps != null)
                        {
                            businessWabaDto.BspName = userBusinessWaba.SubscribedApps.Data
                                .Select(x => x._WhatsappBusinessApiData.Name)
                                .First();
                        }

                        wabasByBspDto.OtherBspWabas.Add(businessWabaDto);
                    }
                }
            }
        }

        return wabasByBspDto;
    }

    public async Task<List<BusinessWabaPhoneNumbersDto>> GetUserBusinessWabaPhoneNumbers(
        string companyId,
        string facebookWabaId,
        string userAccessToken)
    {
        var phoneNumbersOutput =
            await _migrationsApi.MigrationsGetWhatsappCloudApiUserBusinessPhoneNumbersByWabaIdPostAsync(
                getWhatsappCloudApiUserBusinessPhoneNumbersByWabaIdInput:
                new GetWhatsappCloudApiUserBusinessPhoneNumbersByWabaIdInput(
                    companyId,
                    userAccessToken,
                    facebookWabaId));

        return _mapper.Map<List<BusinessWabaPhoneNumbersDto>>(phoneNumbersOutput.Data.PhoneNumbers);
    }

    public async Task<PhoneNumberWabaMigrationRequestDto> InitiatePhoneNumberWabaMigration(
        string companyId,
        string facebookWabaId,
        string phoneNumber)
    {
        var phoneNumberMigrationDto = new PhoneNumberWabaMigrationRequestDto()
        {
            FacebookPhoneNumber = phoneNumber
        };

        var phoneNumberUtil = PhoneNumbers.PhoneNumberUtil.GetInstance();
        var phoneNumberInstance = phoneNumberUtil.Parse($"+{phoneNumber}", null);

        if (!PhoneNumberHelper.IsValidPhoneNumberContainsCountryCode(phoneNumber))
        {
            throw new SleekflowUiException($"Invalid Phone Number {phoneNumber}: no country code detected");
        }

        string countryCode;
        var countryCodeMappings = PhoneNumberHelper.BuildMappings();
        var country = PhoneNumberHelper.GetCountryCode(phoneNumberInstance);

        if (country == "US" ||
            country == "CA")
        {
            countryCode = "1";
        }
        else
        {
            countryCode = countryCodeMappings[country];
        }

        phoneNumber = phoneNumberInstance.NationalNumber.ToString();

        string destinationPhoneNumberId = null;

        try
        {
            var initiateMigrationOutput =
                await _migrationsApi.MigrationsInitiateWhatsappCloudApiPhoneNumberWabaMigrationPostAsync(
                    initiateWhatsappCloudApiPhoneNumberWabaMigrationInput:
                    new InitiateWhatsappCloudApiPhoneNumberWabaMigrationInput(
                        companyId,
                        facebookWabaId,
                        countryCode,
                        phoneNumber));

            if (initiateMigrationOutput.Success &&
                !string.IsNullOrEmpty(initiateMigrationOutput.Data.Id))
            {
                phoneNumberMigrationDto.MigrationInitiated = true;
                destinationPhoneNumberId = initiateMigrationOutput.Data.Id;
                phoneNumberMigrationDto.DestinationPhoneNumberId = destinationPhoneNumberId;
            }
            else if (initiateMigrationOutput.ErrorContext != null)
            {
                await SendWhatsAppCloudApiSystemAlertAsync(
                    $"WhatsApp Cloud Api Migration Error Occured - ({phoneNumber})",
                    $"WhatsApp Cloud Api Migration [[Phone Number:{phoneNumber} Facebook Waba Id:{facebookWabaId}]] received error [[{JsonConvert.SerializeObject(initiateMigrationOutput.ErrorContext["error"])}]] during their recent migration initiation API call",
                    ChannelTypes.WhatsappCloudApi,
                    "alert",
                    companyId,
                    true);

                _logger.LogError(
                    "Migrating phone number +{CountryCode} {PhoneNumber} into waba id {FacebookWabaId} and destination phone number id {DestinationPhoneNumberId} error: {InitiateMigrationOutputErrors}",
                    countryCode,
                    phoneNumber,
                    facebookWabaId,
                    string.IsNullOrWhiteSpace(destinationPhoneNumberId) ? string.Empty : destinationPhoneNumberId,
                    JsonConvert.SerializeObject(initiateMigrationOutput.ErrorContext["error"]));

                JObject errorObject = (JObject)initiateMigrationOutput.ErrorContext["error"];

                if (errorObject != null &&
                    errorObject["error_user_title"] != null &&
                    errorObject["error_user_msg"] != null)
                {
                    throw new SleekflowUiException(
                        $"{errorObject["error_user_title"]}. {errorObject["error_user_msg"]}");
                }

                throw new SleekflowUiException("migration initiation error");
            }
        }
        catch (SleekflowErrorCodeException ex)
        {
            await SendWhatsAppCloudApiSystemAlertAsync(
                $"WhatsApp Cloud Api Migration Error Occured - ({phoneNumber})",
                $"WhatsApp Cloud Api Migration [[Phone Number:{phoneNumber} Facebook Waba Id:{facebookWabaId}]] received error output [[{JsonConvert.SerializeObject(ex.Output)}]] during their recent migration initiation API call",
                ChannelTypes.WhatsappCloudApi,
                "alert",
                companyId,
                true);

            _logger.LogError(
                ex,
                "Exception migrating phone number +{CountryCode} {PhoneNumber} into waba id {FacebookWabaId} and destination phone number id {DestinationPhoneNumberId} error: {InitiateMigrationOutputErrors}",
                countryCode,
                phoneNumber,
                facebookWabaId,
                string.IsNullOrWhiteSpace(destinationPhoneNumberId) ? string.Empty : destinationPhoneNumberId,
                JsonConvert.SerializeObject(ex.Output));

            if (ex.Output != null &&
                ex.Output.ErrorContext != null)
            {
                JObject errorObject = (JObject)ex.Output.ErrorContext["error"];

                if (errorObject != null &&
                    errorObject["error_user_title"] != null &&
                    errorObject["error_user_msg"] != null)
                {
                    throw new SleekflowUiException(
                        $"{errorObject["error_user_title"]}. {errorObject["error_user_msg"]}");
                }
            }

            throw new SleekflowUiException("migration initiation error");
        }

        return phoneNumberMigrationDto;
    }

    public async Task<PhoneNumberVerificationCodeRequestDto> RequestPhoneNumberVerificationCode(
        string companyId,
        string facebookWabaId,
        string destinationPhoneNumberId,
        string phoneNumber,
        string codeMethod,
        string language)
    {
        var phoneNumberVerificationCodeRequestDto = new PhoneNumberVerificationCodeRequestDto()
        {
            FacebookPhoneNumber = phoneNumber,
            DestinationPhoneNumberId = destinationPhoneNumberId,
            CodeMethod = codeMethod,
            Language = language
        };

        try
        {
            var verificationCodeRequestOutput =
                await _migrationsApi.MigrationsRequestWhatsappCloudApiPhoneNumberOwnershipVerificationCodePostAsync(
                    requestWhatsappCloudApiPhoneNumberOwnershipVerificationCodeInput:
                    new RequestWhatsappCloudApiPhoneNumberOwnershipVerificationCodeInput(
                        companyId,
                        facebookWabaId,
                        destinationPhoneNumberId,
                        codeMethod,
                        language));

            if (verificationCodeRequestOutput.Success)
            {
                phoneNumberVerificationCodeRequestDto.CodeSent = verificationCodeRequestOutput.Data.Success;
            }
            else if (verificationCodeRequestOutput.ErrorContext != null)
            {
                await SendWhatsAppCloudApiSystemAlertAsync(
                    $"WhatsApp Cloud Api Migration Error Occured - ({phoneNumber})",
                    $"WhatsApp Cloud Api Migration [[Phone Number:{phoneNumber} Facebook Waba Id:{facebookWabaId} Phone Number Id:{destinationPhoneNumberId}]] received error [[{JsonConvert.SerializeObject(verificationCodeRequestOutput.ErrorContext["error"])}]] during their recent request verification code API call",
                    ChannelTypes.WhatsappCloudApi,
                    "alert",
                    companyId,
                    true);

                _logger.LogError(
                    "Migrating phone number {PhoneNumber} into waba id {FacebookWabaId} destination phone number id {DestinationPhoneNumberId} with {Language} verification code method {CodeMethod} error: {VerificationCodeRequestOutputErrors}",
                    phoneNumber,
                    facebookWabaId,
                    destinationPhoneNumberId,
                    language,
                    codeMethod,
                    JsonConvert.SerializeObject(verificationCodeRequestOutput.ErrorContext["error"]));

                JObject errorObject = (JObject)verificationCodeRequestOutput.ErrorContext["error"];

                if (errorObject != null && errorObject["error_user_title"] != null &&
                    errorObject["error_user_msg"] != null)
                {
                    throw new SleekflowUiException(
                        $"{errorObject["error_user_title"]}. {errorObject["error_user_msg"]}");
                }

                throw new SleekflowUiException("request verification code error");
            }
        }
        catch (SleekflowErrorCodeException ex)
        {
            await SendWhatsAppCloudApiSystemAlertAsync(
                $"WhatsApp Cloud Api Migration Error Occured - ({phoneNumber})",
                $"WhatsApp Cloud Api Migration [[Phone Number:{phoneNumber} Facebook Waba Id:{facebookWabaId} Phone Number Id:{destinationPhoneNumberId}]] received error [[{JsonConvert.SerializeObject(ex.Output)}]] during their recent request verification code API call",
                ChannelTypes.WhatsappCloudApi,
                "alert",
                companyId,
                true);

            _logger.LogError(
                "Exception migrating phone number {PhoneNumber} into waba id {FacebookWabaId} destination phone number id {DestinationPhoneNumberId} with {Language} verification code method {CodeMethod} error: {VerificationCodeRequestOutputErrors}",
                phoneNumber,
                facebookWabaId,
                destinationPhoneNumberId,
                language,
                codeMethod,
                JsonConvert.SerializeObject(ex.Output));

            if (ex.Output != null &&
                ex.Output.ErrorContext != null)
            {
                JObject errorObject = (JObject)ex.Output.ErrorContext["error"];

                if (errorObject != null && errorObject["error_user_title"] != null &&
                    errorObject["error_user_msg"] != null)
                {
                    throw new SleekflowUiException(
                        $"{errorObject["error_user_title"]}. {errorObject["error_user_msg"]}");
                }
            }

            throw new SleekflowUiException("request verification code error");
        }

        return phoneNumberVerificationCodeRequestDto;
    }

    public async Task<PhoneNumberWabaMigrationVerificationDto> PhoneNumberWabaMigrationVerificationCode(
        string companyId,
        string facebookWabaId,
        string destinationPhoneNumberId,
        string phoneNumber,
        string code)
    {
        var phoneNumberWabaMigrationVerificationDto =
            new PhoneNumberWabaMigrationVerificationDto()
            {
                FacebookPhoneNumber = phoneNumber,
                DestinationPhoneNumberId = destinationPhoneNumberId
            };

        try
        {
            var phoneNumberWabaMigrationVerificationCodeOutput =
                await _migrationsApi.MigrationsVerifyWhatsappCloudApiPhoneNumberOwnershipPostAsync(
                    verifyWhatsappCloudApiPhoneNumberOwnershipInput:
                    new VerifyWhatsappCloudApiPhoneNumberOwnershipInput(
                        companyId,
                        facebookWabaId,
                        destinationPhoneNumberId,
                        code));

            if (phoneNumberWabaMigrationVerificationCodeOutput.Success)
            {
                phoneNumberWabaMigrationVerificationDto.Success =
                    phoneNumberWabaMigrationVerificationCodeOutput.Data.Success;
            }
            else if (phoneNumberWabaMigrationVerificationCodeOutput.ErrorContext != null)
            {
                await SendWhatsAppCloudApiSystemAlertAsync(
                    $"WhatsApp Cloud Api Migration Error Occured - ({phoneNumber})",
                    $"WhatsApp Cloud Api Migration [[Phone Number:{phoneNumber} Facebook Waba Id:{facebookWabaId} Phone Number Id:{destinationPhoneNumberId}]] received error [[{JsonConvert.SerializeObject(phoneNumberWabaMigrationVerificationCodeOutput.ErrorContext["error"])}]] during their recent code verification API call",
                    ChannelTypes.WhatsappCloudApi,
                    "alert",
                    companyId,
                    true);

                _logger.LogError(
                    "Verifying phone number {PhoneNumber} into destination phone number id {DestinationPhoneNumberId} with code {Code} error: {PhoneNumberWabaMigrationVerificationCodeOutputErrors}",
                    phoneNumber,
                    destinationPhoneNumberId,
                    code,
                    JsonConvert.SerializeObject(phoneNumberWabaMigrationVerificationCodeOutput.ErrorContext["error"]));

                JObject errorObject = (JObject)phoneNumberWabaMigrationVerificationCodeOutput.ErrorContext["error"];

                if (errorObject != null &&
                    errorObject["error_user_title"] != null &&
                    errorObject["error_user_msg"] != null)
                {
                    throw new SleekflowUiException(
                        $"{errorObject["error_user_title"]}. {errorObject["error_user_msg"]}");
                }

                throw new SleekflowUiException("code verification error");
            }
        }
        catch (SleekflowErrorCodeException ex)
        {
            await SendWhatsAppCloudApiSystemAlertAsync(
                $"WhatsApp Cloud Api Migration Error Occured - ({phoneNumber})",
                $"WhatsApp Cloud Api Migration [[Phone Number:{phoneNumber} Facebook Waba Id:{facebookWabaId} Phone Number Id:{destinationPhoneNumberId}]] received error [[{JsonConvert.SerializeObject(ex.Output)}]] during their recent code verification API call",
                ChannelTypes.WhatsappCloudApi,
                "alert",
                companyId,
                true);

            _logger.LogError(
                ex,
                "Exception verifying phone number {PhoneNumber} into destination phone number id {DestinationPhoneNumberId} with code {Code} error: {ExceptionOutput}",
                phoneNumber,
                destinationPhoneNumberId,
                code,
                JsonConvert.SerializeObject(ex.Output));

            if (ex.Output != null &&
                ex.Output.ErrorContext != null)
            {
                JObject errorObject = (JObject)ex.Output.ErrorContext["error"];

                if (errorObject != null &&
                    errorObject["error_user_title"] != null &&
                    errorObject["error_user_msg"] != null)
                {
                    throw new SleekflowUiException(
                        $"{errorObject["error_user_title"]}. {errorObject["error_user_msg"]}");
                }
            }

            throw new SleekflowUiException("code verification error");
        }

        return phoneNumberWabaMigrationVerificationDto;
    }

    public async Task<PhoneNumberRegistrationDto> RegisterWhatsAppPhoneNumberAsync(
        string companyId,
        string facebookWabaId,
        string destinationPhoneNumberId,
        string phoneNumber,
        string pin = null)
    {
        PhoneNumberRegistrationDto phoneNumberRegistrationDto = new PhoneNumberRegistrationDto()
        {
            FacebookPhoneNumber = phoneNumber,
            DestinationPhoneNumberId = destinationPhoneNumberId,
        };

        try
        {
            var phoneNumberRegistrationOutput =
                await _channelsApi.ChannelsRegisterWhatsAppPhoneNumberPostAsync(
                    registerWhatsAppPhoneNumberInput: new RegisterWhatsAppPhoneNumberInput(
                        companyId,
                        facebookWabaId,
                        destinationPhoneNumberId,
                        pin ?? "000000"));

            if (phoneNumberRegistrationOutput.Success &&
                phoneNumberRegistrationOutput.Data.RegisterPhoneNumberResponse.Success)
            {
                phoneNumberRegistrationDto.Success = true;
                var wabaDtos = await GetConnectWaba(companyId, true);

                if (wabaDtos == null || !wabaDtos.Any())
                {
                    await SendWhatsAppCloudApiSystemAlertAsync(
                        $"WhatsApp Cloud Api Migration Error Occured - ({phoneNumber})",
                        $"WhatsApp Cloud Api Migration [[Phone Number:{phoneNumber} Facebook Waba Id:{facebookWabaId} Phone Number Id:{destinationPhoneNumberId}]] unable to find the waba and phone number in messaging hub after successful registration during their recent code verification registration API call",
                        ChannelTypes.WhatsappCloudApi,
                        "alert",
                        companyId,
                        true);

                    _logger.LogError(
                        "Unable to find phone number {PhoneNumber} and waba {FacebookWabaId} in messaging hub",
                        phoneNumber,
                        facebookWabaId);

                    throw new SleekflowUiException("Waba & Phone Number not found in MessagingHub");
                }

                var messagingHubDict = wabaDtos
                    .Where(
                        x => x.FacebookWabaId == facebookWabaId &&
                             x.WabaDtoPhoneNumbers
                                 .Any(y => y.FacebookPhoneNumberId == destinationPhoneNumberId))
                    .Select(
                        x => new Dictionary<string, string>()
                        {
                            {
                                x.Id, x.WabaDtoPhoneNumbers
                                    .Where(y => y.FacebookPhoneNumberId == destinationPhoneNumberId)
                                    .Select(y => y.Id)
                                    .FirstOrDefault()
                            }
                        })
                    .FirstOrDefault();

                if (messagingHubDict != null &&
                    messagingHubDict.Keys.Any())
                {
                    phoneNumberRegistrationDto.MessagingHubWabaId = messagingHubDict.Keys.First();

                    phoneNumberRegistrationDto.MessagingHubPhoneNumberId =
                        messagingHubDict[phoneNumberRegistrationDto.MessagingHubWabaId];
                }
                else
                {
                    await SendWhatsAppCloudApiSystemAlertAsync(
                        $"WhatsApp Cloud Api Migration Error Occured - ({phoneNumber})",
                        $"WhatsApp Cloud Api Migration [[Phone Number:{phoneNumber} Facebook Waba Id:{facebookWabaId} Phone Number Id:{destinationPhoneNumberId}]] unable to find the waba and phone number in messaging hub after successful registration during their recent code verification registration API call",
                        ChannelTypes.WhatsappCloudApi,
                        "alert",
                        companyId,
                        true);

                    _logger.LogError(
                        "Unable to find phone number {PhoneNumber} and waba {FacebookWabaId} in messaging hub",
                        phoneNumber,
                        facebookWabaId);

                    throw new SleekflowUiException("Waba & Phone Number not found in MessagingHub");
                }
            }
        }
        catch (SleekflowErrorCodeException ex)
        {
            await SendWhatsAppCloudApiSystemAlertAsync(
                $"WhatsApp Cloud Api Registration Error Occured - ({phoneNumber})",
                $"WhatsApp Cloud Api Registration [[Phone Number:{phoneNumber} Facebook Waba Id:{facebookWabaId} Phone Number Id:{destinationPhoneNumberId}]] received error [{JsonConvert.SerializeObject(ex.Output)}] during their recent code verification API call",
                ChannelTypes.WhatsappCloudApi,
                "alert",
                companyId,
                true);

            _logger.LogError(
                ex,
                "Exception registering phone number {PhoneNumber} into destination phone number id {DestinationPhoneNumberId} error: {ExceptionOutput}",
                phoneNumber,
                destinationPhoneNumberId,
                JsonConvert.SerializeObject(ex.Output));

            if (ex.Output != null &&
                ex.Output.ErrorContext != null)
            {
                JObject errorObject = (JObject)ex.Output.ErrorContext["error"];

                if (errorObject != null &&
                    errorObject["error_user_title"] != null &&
                    errorObject["error_user_msg"] != null)
                {
                    throw new SleekflowUiException(
                        $"{errorObject["error_user_title"]}. {errorObject["error_user_msg"]}");
                }
            }

            throw new SleekflowUiException("phone number registration error");
        }

        return phoneNumberRegistrationDto;
    }

    public async Task<PhoneNumberDeRegistrationDto> DeRegisterWhatsAppPhoneNumberAsync(
        string companyId,
        string messagingHubWabaId,
        string messagingHubPhoneNumberId)
    {
        try
        {
            var deregisterPhoneNumberOutputOutput =
                await _managementsApi.ManagementsDeregisterPhoneNumberPostAsync(
                    deregisterPhoneNumberInput:
                    new DeregisterPhoneNumberInput(
                        companyId,
                        messagingHubWabaId,
                        messagingHubPhoneNumberId));

            return new PhoneNumberDeRegistrationDto()
            {
                Success = deregisterPhoneNumberOutputOutput.Success &&
                          deregisterPhoneNumberOutputOutput.Data.Success
            };
        }
        catch (SleekflowErrorCodeException ex)
        {
            _logger.LogError(
                ex,
                "De-Registering phone number id {MessagingHubPhoneNumberId} error: {ExceptionOutput}",
                messagingHubPhoneNumberId,
                JsonConvert.SerializeObject(ex.Output));

            if (ex.Output != null &&
                ex.Output.ErrorContext != null)
            {
                JObject errorObject = (JObject)ex.Output.ErrorContext["error"];

                if (errorObject != null &&
                    errorObject["error_user_title"] != null &&
                    errorObject["error_user_msg"] != null)
                {
                    throw new SleekflowUiException(
                        $"{errorObject["error_user_title"]}. {errorObject["error_user_msg"]}");
                }
            }

            throw new SleekflowUiException("phone number registration error");
        }
    }

    public async Task MigrateWhatsApp360DialogDataToCloudApiAsync(string companyId)
    {
        await _internalWhatsappCloudApiService.Migrate360DialogAssignmentRulesAsync(companyId);

        await _internalWhatsappCloudApiService.MigrateWhatsapp360DialogDefaultChannelAsync(companyId);

        await _internalWhatsappCloudApiService.MigrateWhatsapp360DialogLastChannelAsync(companyId);

        await _internalWhatsappCloudApiService.MigrateWhatsapp360DialogConversationMessagesAsync(companyId);
    }

    public async Task<GetWhatsappCloudApiConversationUsageAnalyticOutput> GetConversationUsageByFacebookWabaIdAsync(
        string companyId,
        string facebookBusinessId,
        string facebookWabaId,
        DateTime start,
        DateTime end)
    {
        return (await _transactionLogsApi.TransactionLogsGetWhatsappCloudApiConversationUsageAnalyticPostAsync(
            getWhatsappCloudApiConversationUsageAnalyticInput: new GetWhatsappCloudApiConversationUsageAnalyticInput(
                companyId,
                facebookBusinessId,
                facebookWabaId,
                start,
                end,
                WhatsappConversationAnalyticGranularityConst.DAILY))).Data;
    }

    public async Task SendWhatsAppCloudApiSystemAlertAsync(
        string subject,
        string message,
        string category,
        string type,
        string companyId,
        bool prependCompanyInfo)
    {
        BackgroundJob.Enqueue<IEmailNotificationService>(
            x => x.SendSystemAlertToSlackChannel(
                subject,
                message,
                category,
                type,
                companyId,
                prependCompanyInfo));
    }

    public async Task<WabaDto> GetWabaDtoFromFacebookWabaPhoneNumberId(
        string companyId,
        string facebookWabaId,
        string facebookPhoneNumberId)
    {
        var wabaDto = await _appDbContext.ConfigWhatsappCloudApiConfigs
            .Where(
                x =>
                    x.CompanyId == companyId &&
                    x.FacebookWabaId == facebookWabaId &&
                    x.FacebookPhoneNumberId == facebookPhoneNumberId)
            .Select(x => x.Waba)
            .FirstOrDefaultAsync();

        return wabaDto;
    }

    public async Task<GetWhatsappCloudApiMediaOutputOutput> GetWhatsappCloudApiMediaAsync(
        string companyId,
        string messagingHubPhoneNumberId,
        string mediaId)
    {
        GetWhatsappCloudApiMediaOutputOutput getWhatsappCloudApiMediaOutputOutput = null;

        await Policy
            .Handle<Exception>()
            .WaitAndRetryAsync(
                5,
                sleepDurationProvider: i => TimeSpan.FromSeconds(5),
                onRetry: (exception, _, retryCount, _) =>
                {
                    _logger.LogError(
                        "{RetryCount}th time: Getting WhatsApp Cloud Api Media {MediaId} error: {ExceptionMessage}",
                        retryCount,
                        mediaId,
                        JsonConvert.SerializeObject(exception));
                })
            .ExecuteAndCaptureAsync(
                async () =>
                    getWhatsappCloudApiMediaOutputOutput = await _channelsApi.ChannelsGetWhatsappCloudApiMediaPostAsync(
                        getWhatsappCloudApiMediaInput: new GetWhatsappCloudApiMediaInput(
                            companyId,
                            messagingHubPhoneNumberId,
                            mediaId)));

        return getWhatsappCloudApiMediaOutputOutput;
    }

    public TemplateMessagePayload GenerateTemplateMessagePayload(
        List<WhatsappCloudApiTemplateComponentObject> components)
    {
        return WhatsappTemplateHelper.TemplateMessagePayloadGenerator(components);
    }

    public async Task<List<GetConversationalAutomationListResponse>> GetConversationalAutomationsAsync(
        string companyId,
        string facebookWabaId,
        CursorBasedPaginationParam paginationParam = null)
    {
        var result = await _conversationalAutomationsApi
            .ConversationalAutomationsGetWabaPhoneNumbersConversationalAutomationsPostAsync(
                getWabaPhoneNumbersConversationalAutomationsInput: new
                    GetWabaPhoneNumbersConversationalAutomationsInput(
                        facebookWabaId,
                        paginationParam));

        if (!result.Success)
        {
            throw new Exception(JsonConvert.SerializeObject(result));
        }

        var conversationalAutomationList = result.Data.ConversationalAutomationsResponse.Data;

        var channelPhoneNumberIdNameList = await _appDbContext.ConfigWhatsappCloudApiConfigs
            .AsNoTracking()
            .Where(x => x.CompanyId == companyId)
            .Where(
                x => conversationalAutomationList
                    .Select(y => y.Id)
                    .Contains(x.FacebookPhoneNumberId))
            .Select(
                x => new
                {
                    x.FacebookPhoneNumberId,
                    x.ChannelName,
                    x.WabaPhoneNumber.FacebookPhoneNumberStatus
                })
            .OrderByDescending(x => x.ChannelName)
            .ToListAsync();

        return channelPhoneNumberIdNameList
            .Select(
                x =>
                    new GetConversationalAutomationListResponse(
                        x.ChannelName,
                        x.FacebookPhoneNumberStatus,
                        conversationalAutomationList
                            .Find(y => y.Id == x.FacebookPhoneNumberId)))
            .ToList();
    }

    public async Task<UpdateConversationalAutomationOutputOutput> UpdateConversationalAutomationAsync(
        UpdateConversationalAutomationInput input)
    {
        return await _conversationalAutomationsApi
            .ConversationalAutomationsUpdateConversationalAutomationPostAsync(
                updateConversationalAutomationInput: new UpdateConversationalAutomationInput(
                    input.SleekflowCompanyId,
                    input.FacebookWabaId,
                    input.SleekflowStaffId,
                    input.FacebookPhoneNumberId,
                    input.ConversationAutomation));
    }

    #region Waba Level Credit Management

    public async Task<GetWabaBalanceAutoTopUpProfileOutputOutput> GetWabaBalanceAutoTopUpProfileAsync(
        GetWabaBalanceAutoTopUpProfileInput input,
        CancellationToken cancellationToken)
    {
        return await _balancesApi
            .BalancesGetWabaBalanceAutoTopUpProfilePostAsync(
                getWabaBalanceAutoTopUpProfileInput: input,
                cancellationToken: cancellationToken);
    }

    public async Task<GetBusinessBalanceCreditTransferTransactionLogsOutputOutput> GetBusinessWabaBalanceTransferTransactionsAsync(
        GetBusinessBalanceCreditTransferTransactionLogsInput input,
        CancellationToken cancellationToken)
    {
        return await _balancesApi
             .BalancesGetBusinessBalanceCreditTransferTransactionLogsPostAsync(
                 getBusinessBalanceCreditTransferTransactionLogsInput: input,
                 cancellationToken: cancellationToken);
    }

    public async Task<AllocateBusinessWabaLevelCreditOutputOutput> AllocateCreditBetweenBusinessAndWabaAsync(
        AllocateBusinessWabaLevelCreditInput input,
        CancellationToken cancellationToken)
    {
        return await _balancesApi
            .BalancesAllocateBusinessWabaLevelCreditPostAsync(
                allocateBusinessWabaLevelCreditInput: input,
                cancellationToken: cancellationToken);
    }

    public async Task<UpsertWabaBalanceAutoTopUpProfileOutputOutput> UpsertWabaBalanceAutoTopUpProfileAsync(
        UpsertWabaBalanceAutoTopUpProfileRequest input,
        Staff staff,
        CancellationToken cancellationToken)
    {
        var customerId = await _appDbContext.CompanyBillRecords
            .AsNoTracking()
            .Where(
                x => x.CompanyId == staff.CompanyId &&
                     x.stripe_subscriptionId != null)
            .OrderByDescending(x => x.created)
            .Select(x => x.customerId)
            .FirstOrDefaultAsync(cancellationToken);

        return await _balancesApi
            .BalancesUpsertWabaBalanceAutoTopUpProfilePostAsync(
                upsertWabaBalanceAutoTopUpProfileInput: new UpsertWabaBalanceAutoTopUpProfileInput(
                    input.AutoTopUpProfile,
                    customerId,
                    staff.IdentityId,
                    staff.Identity.DisplayName,
                    input.RedirectToUrl,
                    input.PhoneNumber,
                    staff.CompanyId,
                    staff.IdentityId),
                cancellationToken: cancellationToken);
    }

    public async Task<SwitchFromBusinessLevelToWabaLevelCreditManagementOutputOutput> SwitchToWabaLevelCreditManagementAsync(
        SwitchFromBusinessLevelToWabaLevelCreditManagementInput input,
        CancellationToken cancellationToken)
    {
        return await _balancesApi
            .BalancesSwitchFromBusinessLevelToWabaLevelCreditManagementPostAsync(
                switchFromBusinessLevelToWabaLevelCreditManagementInput: input,
                cancellationToken: cancellationToken);
    }

    public async Task<SwitchFromWabaLevelToBusinessLevelCreditManagementOutputOutput> SwitchToBusinessLevelCreditManagementAsync(
        SwitchFromWabaLevelToBusinessLevelCreditManagementInput input,
        CancellationToken cancellationToken)
    {
        return await _balancesApi
            .BalancesSwitchFromWabaLevelToBusinessLevelCreditManagementPostAsync(
                switchFromWabaLevelToBusinessLevelCreditManagementInput: input,
                cancellationToken: cancellationToken);
    }

    #endregion

    private async Task<GetFilteredManagementWhatsappCloudApiBusinessBalanceTransactionLogsOutput>
        GetFilteredManagementWhatsappCloudApiBusinessBalanceTransactionLogs(
            BusinessBalanceTransactionLogFilter businessBalanceTransactionLogFilter,
            int limit = 10000,
            string continuationToken = null,
            CancellationToken cancellationToken = default)
    {
        return (await _managementsApi
            .ManagementsGetFilteredManagementWhatsappCloudApiBusinessBalanceTransactionLogsPostAsync(
                getFilteredManagementWhatsappCloudApiBusinessBalanceTransactionLogsInput: new
                    GetFilteredManagementWhatsappCloudApiBusinessBalanceTransactionLogsInput(
                        businessBalanceTransactionLogFilter,
                        limit,
                        continuationToken),
                cancellationToken: cancellationToken)).Data;
    }

    public async Task<Money> GetWabaAllTimeUsageAsync(
        BusinessBalanceTransactionLogFilter businessBalanceTransactionLogFilter,
        int limit = 10000,
        CancellationToken cancellationToken = default)
    {
        string continueToken = null;
        List<BusinessBalanceTransactionLog> list = [];
        do
        {
            var result = await GetFilteredManagementWhatsappCloudApiBusinessBalanceTransactionLogs(
                businessBalanceTransactionLogFilter,
                limit,
                continueToken,
                cancellationToken);

            continueToken = result.NextContinuationToken;
            list.AddRange(result.BusinessBalanceTransactionLogs);
        }
        while (!string.IsNullOrEmpty(continueToken));

        var currency = list.FirstOrDefault()?.Used.CurrencyIsoCode ?? "USD";
        var sum = list.Sum(x => x.Used.Amount + x.TransactionHandlingFee.Amount + x.Markup.Amount);

        return new Money(currency, sum);
    }

    public async Task<GetFlowsResponse> GetWhatsappFlowsByWabaIdAsync(
        string companyId,
        string messagingHubWabaId,
        CursorBasedPaginationParam paginationParam = null)
    {
        var getWhatsappCloudApiFlowsCacheKeyPattern =
            new GetWhatsappCloudApiFlowsCacheKeyPattern(companyId, messagingHubWabaId);

        if (paginationParam == null)
        {
            var cacheData = await _cacheManagerService.GetCacheAsync(getWhatsappCloudApiFlowsCacheKeyPattern);

            if (!string.IsNullOrEmpty(cacheData))
            {
                var deserializedFlows =
                    JsonConvert.DeserializeObject<GetFlowsResponse>(cacheData);

                return deserializedFlows;
            }
        }

        var getWhatsappFlowsByWabaIdOutputOutput =
            await _whatsappFlowsApi.WhatsappFlowsGetWhatsappFlowsByWabaIdPostAsync(
                getWhatsappFlowsByWabaIdInput: new GetWhatsappFlowsByWabaIdInput(
                    companyId,
                    messagingHubWabaId,
                    paginationParam));

        if (paginationParam == null)
        {
            await _cacheManagerService.SaveCacheAsync(
                getWhatsappCloudApiFlowsCacheKeyPattern,
                getWhatsappFlowsByWabaIdOutputOutput.Data.Flows);
        }

        return getWhatsappFlowsByWabaIdOutputOutput.Data.Flows;
    }

    public async Task<GetFlowResponse> GetWhatsappFlowAsync(string companyId, string messagingHubWabaId, string flowId)
    {
        var getWhatsappCloudApiFlowsCacheKeyPattern =
            new GetWhatsappCloudApiFlowsCacheKeyPattern(companyId, messagingHubWabaId);

        var cacheData = await _cacheManagerService.GetCacheAsync(getWhatsappCloudApiFlowsCacheKeyPattern);

        if (!string.IsNullOrEmpty(cacheData))
        {
            var deserializedFlows =
                JsonConvert.DeserializeObject<GetFlowsResponse>(cacheData);

            var targetedFlow = deserializedFlows.Data.FirstOrDefault(x => x.Id == flowId);

            if (targetedFlow != null)
            {
                return targetedFlow;
            }
        }

        var getWhatsappFlowOutputOutput = await _whatsappFlowsApi.WhatsappFlowsGetWhatsappFlowPostAsync(
            getWhatsappFlowInput: new GetWhatsappFlowInput(companyId, messagingHubWabaId, flowId));

        BackgroundJob.Enqueue(() => GetWhatsappFlowsByWabaIdAsync(companyId, messagingHubWabaId, null));

        return getWhatsappFlowOutputOutput.Data.FlowDetails;
    }

    public async Task<List<GetWhatsappFlowsByWabaResponse>> GetAllWhatsappFlowsByWabaAsync(string companyId)
    {
        var companyWabas = await _appDbContext.ConfigWhatsappCloudApiConfigs
            .AsNoTracking()
            .Where(x => x.CompanyId == companyId)
            .GroupBy(x => x.MessagingHubWabaId)
            .Select(
                g => new
                {
                    MessagingHubWabaId = g.Key,
                    FacebookWabaName = g.Select(x => x.FacebookWabaName).FirstOrDefault()
                })
            .ToListAsync();

        var getWhatsappFlowsByWabaList = new List<GetWhatsappFlowsByWabaResponse>();

        foreach (var companyWaba in companyWabas)
        {
            try
            {
                var whatsappFlows = await GetWhatsappFlowsByWabaIdAsync(companyId, companyWaba.MessagingHubWabaId);

                if (whatsappFlows.Data != null && whatsappFlows.Data.Any())
                {
                    getWhatsappFlowsByWabaList.Add(
                        new GetWhatsappFlowsByWabaResponse(
                            companyWaba.MessagingHubWabaId,
                            companyWaba.FacebookWabaName,
                            whatsappFlows));
                }
            }
            catch (Exception e)
            {
                _logger.LogError(
                    e,
                    "Error getting WhatsApp Flows by Waba Id {MessagingHubWabaId} in Company Id {CompanyId}",
                    companyWaba.MessagingHubWabaId,
                    companyId);
            }
        }

        return getWhatsappFlowsByWabaList;
    }

    public async Task<FlowComponent> GetWhatsappFlowJsonAsync(
        string companyId,
        string messagingHubWabaId,
        string flowId)
    {
        var flow = await GetWhatsappFlowAsync(companyId, messagingHubWabaId, flowId);
        var asset = flow?.Assets.Data.FirstOrDefault();

        if (asset == null)
        {
            throw new MissingFieldException(nameof(GetFlowResponse), nameof(asset.DownloadUrl));
        }

        var httpClient = _httpClientFactory.CreateClient(HttpClientHandlerName.Default);
        var response = await httpClient.GetAsync(asset.DownloadUrl);
        response.EnsureSuccessStatusCode();

        var stringResponse = await response.Content.ReadAsStringAsync();

        return JsonConvert.DeserializeObject<FlowComponent>(stringResponse);
    }

    public async Task<List<ScreenSubmissionDataPayload>> GetTerminalScreensPayloadsAsync(
        string companyId,
        string messagingHubWabaId,
        string flowId)
    {
        var flowComponent = await GetWhatsappFlowJsonAsync(companyId, messagingHubWabaId, flowId);

        var terminalScreensPayloads = WhatsappFlowTerminalScreensSubmissionDataPayloadsHelper
            .GetFlowScreenSubmissionDataPayloadsResponse(flowComponent);

        return terminalScreensPayloads;
    }

    public async Task<GetFlowResponse> UpdateWhatsappFlowMetadataAsync(
        string companyId,
        string messagingHubWabaId,
        string flowId,
        WhatsappCloudApiUpdateFlowMetadataObject updateFlowMetadataObject,
        string staffIdentityId)
    {
        _logger.LogInformation(
            "Company {CompanyId} Staff {StaffIdentityId} Updating Waba {WabaId} WhatsApp Flow {FlowId} Metadata with {UpdateFlowMetadataObject}",
            companyId,
            staffIdentityId,
            messagingHubWabaId,
            flowId,
            JsonConvert.SerializeObject(updateFlowMetadataObject));

        var updateWhatsappFlowMetadataOutputOutput =
            await _whatsappFlowsApi.WhatsappFlowsUpdateWhatsappFlowMetadataPostAsync(
                updateWhatsappFlowMetadataInput: new UpdateWhatsappFlowMetadataInput(
                    companyId,
                    messagingHubWabaId,
                    flowId,
                    new Sleekflow.Apis.MessagingHub.Model.WhatsappCloudApiUpdateFlowMetadataObject()
                    {
                        Name = updateFlowMetadataObject.Name,
                        Categories = updateFlowMetadataObject.Categories,
                        EndpointUri = updateFlowMetadataObject.EndpointUri,
                        ApplicationId = updateFlowMetadataObject.ApplicationId
                    },
                    staffIdentityId));

        BackgroundJob.Enqueue(() => GetWhatsappFlowsByWabaIdAsync(companyId, messagingHubWabaId, null));

        return updateWhatsappFlowMetadataOutputOutput.Data.UpdatedFlowDetails;
    }

    public async Task UpdateBusinessBalanceChangedWebhookUrlFromConnectedWabas(string sleekflowCompanyId, List<WabaDto> connectedWabas)
    {
        var facebookBusinessId = new HashSet<string>();

        if (connectedWabas.Any())
        {
            connectedWabas.Select(x => x.FacebookBusinessId).Distinct().ToList()
                .ForEach(x => facebookBusinessId.Add(x));
        }

        var domainUrl = _configuration.GetValue<string>("Values:DomainName");

        if (string.IsNullOrEmpty(domainUrl))
        {
            return;
        }

        var webhookUrl = $"{domainUrl}/whatsapp/cloudapi/businessbalance/webhook";

        var upsertWhatsappCloudApiBusinessBalanceChangedEventWebhooksOutputOutput =
            await _balancesApi.BalancesUpsertWhatsappCloudApiBusinessBalanceChangedEventWebhooksPostAsync(
                upsertWhatsappCloudApiBusinessBalanceChangedEventWebhooksInput:
                new UpsertWhatsappCloudApiBusinessBalanceChangedEventWebhooksInput(
                    sleekflowCompanyId,
                    facebookBusinessId.ToList(),
                    webhookUrl));

        if (!upsertWhatsappCloudApiBusinessBalanceChangedEventWebhooksOutputOutput.Success)
        {
            _logger.LogError(
                "Error {ErrorOutput} updating business balance changed webhook url from connected wabas {Wabas} from Company {SleekflowCompanyId},",
                JsonConvert.SerializeObject(upsertWhatsappCloudApiBusinessBalanceChangedEventWebhooksOutputOutput),
                JsonConvert.SerializeObject(connectedWabas),
                sleekflowCompanyId);
        }
    }

    public async Task<WabaDto> SetupWabaDatasetAsync(
        string companyId,
        string messagingHubWabaId,
        string facebookDatasetName,
        string staffIdentityId)
    {
        var facebookWabaId = await _appDbContext.ConfigWhatsappCloudApiConfigs
            .Where(x => x.CompanyId == companyId && x.MessagingHubWabaId == messagingHubWabaId)
            .Select(x => x.FacebookWabaId).FirstOrDefaultAsync();

        if (string.IsNullOrEmpty(facebookWabaId))
        {
            throw new SleekflowUiException("Messaging Hub Waba Id not found", 404);
        }

        var createWabaDatasetOutputOutput = await _metaConversionApisApi.MetaConversionApisCreateWabaDatasetPostAsync(
            createWabaDatasetInput: new CreateWabaDatasetInput(
                facebookWabaId,
                facebookDatasetName,
                companyId,
                staffIdentityId));

        var wabaDto = createWabaDatasetOutputOutput.Data.Waba;

        var wabaChannels = await _appDbContext.ConfigWhatsappCloudApiConfigs
            .Where(x => x.MessagingHubWabaId == messagingHubWabaId).ToListAsync();

        foreach (var wabaChannel in wabaChannels)
        {
            wabaChannel.Waba = wabaDto;
            _appDbContext.Entry(wabaChannel).Property(x => x.Waba).IsModified = true;
        }

        await _appDbContext.SaveChangesAsync();

        return wabaDto;
    }
}