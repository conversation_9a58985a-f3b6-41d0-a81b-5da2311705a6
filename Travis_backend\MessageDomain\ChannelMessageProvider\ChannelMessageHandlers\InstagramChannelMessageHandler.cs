using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Travis_backend.Constants;
using Travis_backend.ContactDomain.Services;
using Travis_backend.ConversationDomain.Models;
using Travis_backend.ConversationServices.ViewModels;
using Travis_backend.Database;
using Travis_backend.FileDomain.Services;
using Travis_backend.MessageDomain.Models;
using Travis_backend.OpenTelemetry.Meters;
using Travis_backend.OpenTelemetry.Meters.Constants;

namespace Travis_backend.MessageDomain.ChannelMessageProvider.ChannelMessageHandlers;

public class InstagramChannelMessageHandler : IChannelMessageHandler
{
    private readonly IUserProfileService _userProfileService;
    private readonly ApplicationDbContext _appDbContext;
    private readonly IConfiguration _configuration;
    private readonly ILogger<InstagramChannelMessageHandler> _logger;
    private readonly IMediaProcessService _mediaProcessService;
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly IConversationMeters _conversationMeters;

    public string ChannelType => ChannelTypes.Instagram;

    public InstagramChannelMessageHandler(
        IUserProfileService userProfileService,
        ApplicationDbContext appDbContext,
        IConfiguration configuration,
        ILogger<InstagramChannelMessageHandler> logger,
        IMediaProcessService mediaProcessService,
        IHttpClientFactory httpClientFactory,
        IConversationMeters conversationMeters)
    {
        _userProfileService = userProfileService;
        _appDbContext = appDbContext;
        _configuration = configuration;
        _logger = logger;
        _mediaProcessService = mediaProcessService;
        _httpClientFactory = httpClientFactory;
        _conversationMeters = conversationMeters;
    }

    public async ValueTask<ConversationMessage> SendChannelMessageAsync(
        Conversation conversation,
        ConversationMessage conversationMessage)
    {
        try
        {
            if (conversationMessage.InstagramReceiver != null
                && conversationMessage.InstagramReceiver?.InstagramPageId !=
                conversationMessage.InstagramReceiver?.InstagramId
                && string.IsNullOrEmpty(conversationMessage.MessageUniqueID))
            {
                var instagramConfig = await _appDbContext.ConfigInstagramConfigs
                    .FirstOrDefaultAsync(
                        x => x.InstagramPageId == conversationMessage.InstagramReceiver.InstagramPageId);

                var messengerAccessToken = instagramConfig.PageAccessToken;

                var sendFBMessage = new SendFacebookMessage()
                {
                    messaging_type = "MESSAGE_TAG",
                    tag = "HUMAN_AGENT",
                    recipient = new Recipient()
                    {
                        id = conversationMessage.InstagramReceiver.InstagramId
                    }
                };

                try
                {
                    var httpClient = _httpClientFactory.CreateClient(HttpClientHandlerName.Default);

                    switch (conversationMessage.MessageType)
                    {
                        case "text":
                            string messageContent = string.Empty;

                            if (conversationMessage.TranslationResults != null)
                            {
                                messageContent = conversationMessage.TranslationResults
                                    .FirstOrDefault().translations
                                    .FirstOrDefault().text;
                            }
                            else
                            {
                                messageContent = conversationMessage.MessageContent;
                            }

                            sendFBMessage.message = new MessageData()
                            {
                                text = messageContent
                            };

                            var sendMessageResponse = await httpClient.PostAsJsonAsync(
                                $"https://graph.facebook.com/v20.0/me/messages?access_token={messengerAccessToken}",
                                sendFBMessage);
                            var sendMessageString = await sendMessageResponse.Content.ReadAsStringAsync();
                            var sendMessage = JsonConvert.DeserializeObject<SendMessageResponse>(sendMessageString);
                            conversationMessage.MessageUniqueID = sendMessage?.message_id;
                            if (sendMessage?.error != null)
                            {
                                conversationMessage.ChannelStatusMessage =
                                    $"{sendMessage.error.error_subcode}-{sendMessage.error.message}";

                                conversationMessage.Metadata ??= new Dictionary<string, object>();
                                conversationMessage.Metadata.Add("error", new List<ConversationMessageError>()
                                {
                                    new ()
                                        {
                                            Code = sendMessage.error.error_subcode.ToString(),
                                            Message = sendMessage.error.message,
                                            InnerError = sendMessage.error
                                        }
                                });

                                conversationMessage.Status = MessageStatus.Failed;

                                _logger.LogError(
                                    "Instagram Page Id {InstagramPageId} Sending text message to InstagramUserId {InstagramUserId} Error: {ResponseBody}",
                                    conversationMessage.InstagramReceiver.InstagramPageId,
                                    conversationMessage.InstagramReceiver.InstagramId,
                                    sendMessageString);
                            }

                            await _appDbContext.SaveChangesAsync();

                            break;
                        case "file":
                            foreach (var uploadedFile in conversationMessage.UploadedFiles)
                            {
                                if ((uploadedFile.MIMEType.Contains("audio") &&
                                     !uploadedFile.MIMEType.Contains("mp3")) ||
                                    Path.GetExtension(uploadedFile.Filename) == ".bin")
                                {
                                    await _mediaProcessService.ProcessMedia(conversation.Id, uploadedFile, "mp3");
                                }

                                string messageType = "file";

                                switch (uploadedFile.MIMEType)
                                {
                                    case "image/png":
                                    case "image/jpeg":
                                    case "image/gif":
                                        messageType = "image";

                                        break;
                                    case "audio/wav":
                                    case "audio/mpeg":
                                        messageType = "audio";

                                        break;

                                    case "video/mp4":
                                    case "video/ogg":
                                    case "video/x-msvideo":
                                    case "video/quicktime":
                                    case "video/webm":
                                        messageType = "video";
                                        break;

                                    default:
                                        messageType = "file";

                                        break;
                                }

                                var filename = Path.GetFileName(uploadedFile.Filename);

                                filename = filename
                                    .Replace(" ", string.Empty)
                                    .Replace("(", string.Empty)
                                    .Replace(")", string.Empty)
                                    .Replace("[", string.Empty)
                                    .Replace("]", string.Empty);

                                var domainName = _configuration.GetValue<String>("Values:DomainName");

                                sendFBMessage.message = new MessageData()
                                {
                                    attachment = new FBAttachment
                                    {
                                        payload = new AttachmentPayload
                                        {
                                            url =
                                                $"{domainName}/Message/File/Private/{uploadedFile.FileId}/{filename}",
                                            is_reusable = true
                                        },
                                        type = messageType
                                    }
                                };

                                try
                                {
                                    var sendAttResponse = await httpClient.PostAsJsonAsync(
                                        $"https://graph.facebook.com/v20.0/me/messages?access_token={messengerAccessToken}",
                                        sendFBMessage);
                                    var sendAttString = await sendAttResponse.Content.ReadAsStringAsync();
                                    var sendAtt = JsonConvert.DeserializeObject<SendMessageResponse>(sendAttString);

                                    if (conversationMessage.MessageContent == null)
                                    {
                                        conversationMessage.MessageUniqueID = sendAtt.message_id;
                                        if (sendAtt?.error != null)
                                        {
                                            conversationMessage.ChannelStatusMessage =
                                                $"{sendAtt.error.error_subcode}-{sendAtt.error.message}";

                                            conversationMessage.Metadata ??= new Dictionary<string, object>();
                                            conversationMessage.Metadata.Add("error", new List<ConversationMessageError>()
                                            {
                                                new ()
                                                {
                                                    Code = sendAtt.error.error_subcode.ToString(),
                                                    Message = sendAtt.error.message,
                                                    InnerError = sendAtt.error
                                                }
                                            });

                                            conversationMessage.Status = MessageStatus.Failed;

                                            _logger.LogError(
                                                "Instagram Page Id {InstagramPageId} Sending file message to InstagramUserId {InstagramUserId} Error: {ResponseBody}",
                                                conversationMessage.InstagramReceiver.InstagramPageId,
                                                conversationMessage.InstagramReceiver.InstagramId,
                                                sendAtt);
                                        }

                                        await _appDbContext.SaveChangesAsync();
                                    }
                                }
                                catch (Exception ex)
                                {
                                    _logger.LogError(
                                        ex,
                                        "Send Instagram file message error for conversation {ConversationId}: {ExceptionString}",
                                        conversation?.Id,
                                        ex.ToString());

                                    conversationMessage.Status = MessageStatus.Failed;
                                    conversationMessage.ChannelStatusMessage += $";Error: {ex.ToString()}";
                                }
                            }

                            break;
                        case "interactive":
                            if (conversationMessage.ExtendedMessagePayload == null ||
                                conversationMessage.ExtendedMessagePayload.ExtendedMessageType !=
                                ExtendedMessageType.InstagramInteractiveMessage ||
                                conversationMessage.ExtendedMessagePayload.ExtendedMessagePayloadDetail
                                    .FacebookMessagePayload == null)
                            {
                                break;
                            }

                            sendFBMessage.message = conversationMessage.ExtendedMessagePayload
                                .ExtendedMessagePayloadDetail.FacebookMessagePayload;

                            var sendFbInteractiveMessage = await httpClient.PostAsJsonAsync(
                                $"https://graph.facebook.com/me/messages?access_token={messengerAccessToken}",
                                sendFBMessage);

                            var sendInteractiveMsgString =
                                await sendFbInteractiveMessage.Content.ReadAsStringAsync();

                            var sendInteractiveMsg =
                                JsonConvert.DeserializeObject<SendMessageResponse>(sendInteractiveMsgString);

                            conversationMessage.MessageUniqueID = sendInteractiveMsg.message_id;

                            if (sendInteractiveMsg.error != null)
                            {
                                conversationMessage.ChannelStatusMessage =
                                    $"{sendInteractiveMsg.error.error_subcode}-{sendInteractiveMsg.error.message}";

                                conversationMessage.Metadata ??= new Dictionary<string, object>();
                                conversationMessage.Metadata.Add("error", new List<ConversationMessageError>()
                                {
                                    new ()
                                    {
                                        Code = sendInteractiveMsg.error.error_subcode.ToString(),
                                        Message = sendInteractiveMsg.error.message,
                                        InnerError = sendInteractiveMsg.error
                                    }
                                });

                                conversationMessage.Status = MessageStatus.Failed;

                                _logger.LogError(
                                    "Instagram Page Id {InstagramPageId} Sending message to InstagramUserId {InstagramUserId} Error: {ResponseBody}",
                                    conversationMessage.InstagramReceiver.InstagramPageId,
                                    conversationMessage.InstagramReceiver.InstagramId,
                                    sendInteractiveMsg);
                            }

                            await _appDbContext.SaveChangesAsync();

                            break;
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(
                        ex,
                        "Send Instagram message error for conversation {ConversationId}: {ExceptionString}",
                        conversation?.Id,
                        ex.ToString());

                    conversationMessage.Status = MessageStatus.Failed;
                    conversationMessage.ChannelStatusMessage += $";Error: {ex.ToString()}";
                }

                await IGFetchFacebookUserProfile(
                    conversation.CompanyId,
                    conversationMessage.InstagramReceiver,
                    instagramConfig.PageAccessToken);

                await _appDbContext.SaveChangesAsync();
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "Send Instagram file message general exception for conversation {ConversationId}: {ExceptionString}",
                conversation?.Id,
                ex.ToString());

            conversationMessage.Status = MessageStatus.Failed;
            conversationMessage.ChannelStatusMessage += $";Error: {ex.ToString()}";
        }

        if (!string.IsNullOrEmpty(conversationMessage.MessageUniqueID))
        {
            conversationMessage.Status = MessageStatus.Sent;

            _conversationMeters.IncrementCounter(ChannelTypes.Instagram, ConversationMeterOptions.SendSuccess);
        }
        else
        {
            conversationMessage.Status = MessageStatus.Failed;

            _conversationMeters.IncrementCounter(ChannelTypes.Instagram, ConversationMeterOptions.SendFailed);
        }

        await _appDbContext.SaveChangesAsync();

        return conversationMessage;
    }

    /// <summary>
    /// User profile will only be updated when profile picture, name or username is null or empty, or name is 'No Name'.
    /// </summary>
    /// <remarks>
    /// Reduce roundtrips/frequent fetching with the conditions because fetching occurs for every messages.
    /// </remarks>
    /// <param name="companyId">Company ID.</param>
    /// <param name="igSender">Instagram sender.</param>
    /// <param name="token">Page access token.</param>
    private async Task IGFetchFacebookUserProfile(
        string companyId,
        InstagramSender igSender,
        string token)
    {
        try
        {
            if (string.IsNullOrEmpty(igSender.ProfilePic)
                || string.IsNullOrEmpty(igSender.Username)
                || string.IsNullOrEmpty(igSender.Name)
                || igSender.Name == "No Name"
                || igSender.ProfilePic.Contains("https://scontent-hkg4-1.xx.fbcdn.net"))
            {
                var httpClient = _httpClientFactory.CreateClient(HttpClientHandlerName.Default);

                var profileDataResponse = await httpClient.GetStringAsync(
                    $"https://graph.facebook.com/{igSender.InstagramId}?access_token={token}&fields=name,profile_pic,username");
                var profileData = JsonConvert.DeserializeObject<IGProfile>(profileDataResponse);

                if (!string.IsNullOrEmpty(profileData?.ProfilePic))
                {
                    var newUploadedFile = await _userProfileService.UploadSocialProfilePicture(
                        companyId,
                        ChannelTypes.Instagram,
                        igSender.InstagramId,
                        profileData.ProfilePic);

                    if (newUploadedFile == null)
                    {
                        _logger.LogWarning(
                            "Instagram profile pic upload fail for {SenderInstagramId}",
                            igSender.InstagramId);
                    }
                    else
                    {
                        _appDbContext.UserProfilePictureFiles.Add(newUploadedFile);

                        var domainName = _configuration.GetValue<string>("Values:DomainName");
                        igSender.ProfilePic = $"{domainName}/userprofile/picture/{newUploadedFile.ProfilePictureId}";
                    }
                }

                igSender.Name = string.IsNullOrEmpty(profileData?.Name) ? "No Name" : profileData.Name;
                igSender.Username = profileData?.Username;
                await _appDbContext.SaveChangesAsync();
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "{CompanyId} Fetch Instagram Profile Picture error for {SenderFacebookId}: {ExceptionMessage}",
                companyId,
                igSender.InstagramId,
                ex.Message);
        }
    }
}