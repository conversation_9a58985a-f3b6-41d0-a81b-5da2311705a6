<Project Sdk="Microsoft.NET.Sdk">
    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <AzureFunctionsVersion>V4</AzureFunctionsVersion>
        <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
    </PropertyGroup>
    <ItemGroup>
        <PackageReference Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore" Version="8.0.7" />
        <PackageReference Include="LinqKit.Core" Version="1.2.5" />
        <PackageReference Include="Microsoft.Azure.Functions.Extensions" Version="1.1.0" />
        <PackageReference Include="Microsoft.Data.SqlClient" Version="5.2.1" />
        <PackageReference Include="Microsoft.EntityFrameworkCore" Version="8.0.7" />
        <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="8.0.7" />
        <PackageReference Include="Microsoft.IdentityModel.Protocols.OpenIdConnect" Version="8.0.1" />
        <PackageReference Include="Microsoft.NET.Sdk.Functions" Version="4.4.1" />
        <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
        <PackageReference Include="Polly" Version="8.4.1" />
        <PackageReference Include="SendGrid" Version="9.28.1" />
        <PackageReference Include="ShopifySharp" Version="5.16.0" />
        <PackageReference Include="StackExchange.Redis" Version="2.8.0" />
        <PackageReference Include="Stripe.net" Version="39.126.0" />
        <PackageReference Include="Z.EntityFramework.Plus.EFCore" Version="8.103.2" />
    </ItemGroup>

    <!-- https://github.com/dotnet/runtime/issues/62329 -->
    <ItemGroup Condition="'$(OS)' == 'Windows_NT'">
        <RuntimeHostConfigurationOption Include="System.Globalization.AppLocalIcu" Value="********" />
        <PackageReference Include="Microsoft.ICU.ICU4C.Runtime" Version="********" />
    </ItemGroup>

    <ItemGroup>
        <None Update="host.json">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="local.settings.json">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
            <CopyToPublishDirectory>Never</CopyToPublishDirectory>
        </None>
    </ItemGroup>
</Project>
