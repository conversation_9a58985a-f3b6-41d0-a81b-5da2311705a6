﻿using System;
using System.Collections.Generic;
using System.Linq;
using TimeZoneConverter;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.Enums;
using DateTime = System.DateTime;
using TimeZoneConverter;
using Travis_backend.Extensions;

namespace Travis_backend.Helpers
{
    public static class TimeZoneHelper
    {
        public static List<MyTimeZoneInfo> CachedTimeZones { get; set; }

        public static MyTimeZoneInfo GmtStandardTimeZoneInfo => new()
        {
            Id = "GMT Standard Time",
            DisplayName = "(UTC+00:00) Dublin, Edinburgh, Lisbon, London",
            StandardName = "GMT Standard Time",
            BaseUtcOffset = TimeSpan.Zero
        };

        public static MyTimeZoneInfo ChinaStandardTimeZoneInfo => new()
        {
            Id = "China Standard Time",
            DisplayName = "(UTC+08:00) Hong Kong",
            StandardName = "China Standard Time",
            BaseUtcOffset = TimeSpan.FromHours(8)
        };

        public static List<MyTimeZoneInfo> GetTimeZones()
        {
            if (CachedTimeZones != null)
            {
                return CachedTimeZones;
            }

            var timeZoneInfos = TZConvert.KnownIanaTimeZoneNames
                .Select(
                    timeZoneId =>
                    {
                        try
                        {
                            var timeZone = TZConvert.GetTimeZoneInfo(timeZoneId);

                            return new MyTimeZoneInfo
                            {
                                Id = timeZoneId,
                                DisplayName = GetTimeZoneDisplayName(timeZone),
                                StandardName =
                                    TZConvert.TryIanaToWindows(timeZoneId, out var windowsName)
                                        ? windowsName
                                        : timeZone.StandardName,
                                BaseUtcOffset = GetTimeZoneUtcOffset(timeZone)
                            };
                        }
                        catch (Exception)
                        {
                            return null;
                        }
                    })
                .Where(i => i != null)
                .OrderBy(x => x.BaseUtcOffset)
                .ThenBy(x => x.Id)
                .ToList();

            CachedTimeZones ??= timeZoneInfos;

            return timeZoneInfos;
        }

        public static string GetTimeZoneDisplayName(TimeZoneInfo timeZone, DateTime? utcDateTimeInstance = null)
            => GetTimeZoneUtcOffset(timeZone, utcDateTimeInstance) != timeZone.BaseUtcOffset
                ? $"(UTC{GetTimeZoneSign(timeZone, utcDateTimeInstance)}" +
                  $"{GetTimeZoneUtcOffset(timeZone, utcDateTimeInstance):hh\\:mm}) {timeZone.DaylightName}"
                : timeZone.DisplayName;

        public static TimeSpan GetTimeZoneUtcOffset(TimeZoneInfo timeZone, DateTime? utcDateTimeInstance = null)
        {
            var adjustmentRule = timeZone.GetAdjustmentRules()
                .FirstOrDefault(
                    x =>
                        x.DateStart <= DateTime.UtcNow.Date &&
                        x.DateEnd >= DateTime.UtcNow.Date &&
                        timeZone.IsDaylightSavingTime(utcDateTimeInstance.GetValueOrDefault(DateTime.UtcNow)));

            return timeZone.BaseUtcOffset + (adjustmentRule?.DaylightDelta ?? TimeSpan.Zero);
        }

        private static string GetTimeZoneSign(TimeZoneInfo timeZone, DateTime? utcDateTimeInstance = null)
            => GetTimeZoneUtcOffset(timeZone, utcDateTimeInstance) >= TimeSpan.Zero ? "+" : "-";

        public static MyTimeZoneInfo GetTimeZoneByIdOrDefault(string id, MyTimeZoneInfo defaultTimeZone = null)
        {
            if (string.IsNullOrWhiteSpace(id))
            {
                return defaultTimeZone;
            }
            else if (id.EqualsIgnoreCase("China Standard Time"))
            {
                return ChinaStandardTimeZoneInfo;
            }

            ICollection<MyTimeZoneInfo> items = new List<MyTimeZoneInfo>();

            try
            {
                var timeZones = GetTimeZones();

                foreach (var timeZone in timeZones.Where(x => x.Id == id))
                {
                    items.Add(
                        new MyTimeZoneInfo
                        {
                            Id = timeZone.Id,
                            DisplayName = timeZone.DisplayName,
                            StandardName = timeZone.StandardName,
                            BaseUtcOffset = timeZone.BaseUtcOffset
                        });
                }

                foreach (var timeZone in timeZones.Where(x => x.StandardName == id))
                {
                    items.Add(
                        new MyTimeZoneInfo
                        {
                            Id = timeZone.Id,
                            DisplayName = timeZone.DisplayName,
                            StandardName = timeZone.StandardName,
                            BaseUtcOffset = timeZone.BaseUtcOffset
                        });
                }

                return items.Count == 0 ? defaultTimeZone : items.FirstOrDefault();
            }
            catch
            {
                return items.Count == 0 ? defaultTimeZone : items.FirstOrDefault();
            }
        }

        public static MyTimeZoneInfo GetTimeZoneById(string id)
        {
            List<MyTimeZoneInfo> items = new List<MyTimeZoneInfo>();
            if (id == "China Standard Time")
            {
                items.Add(
                    new MyTimeZoneInfo
                    {
                        Id = "China Standard Time",
                        DisplayName = "(UTC+08:00) Hong Kong",
                        StandardName = "China Standard Time",
                        BaseUtcOffset = TimeSpan.FromHours(8)
                    });
                return items.FirstOrDefault();
            }

            var timeZones = GetTimeZones();
            if (string.IsNullOrEmpty(id))
            {
                items.Add(
                    new MyTimeZoneInfo
                    {
                        Id = "GMT Standard Time",
                        DisplayName = "(UTC+00:00) Dublin, Edinburgh, Lisbon, London",
                        StandardName = "GMT Standard Time",
                        BaseUtcOffset = new TimeSpan()
                    });
                return items.FirstOrDefault();
            }

            try
            {
                foreach (var timeZone in timeZones.Where(x => x.Id == id))
                {
                    items.Add(
                        new MyTimeZoneInfo
                        {
                            Id = timeZone.Id,
                            DisplayName = timeZone.DisplayName,
                            StandardName = timeZone.StandardName,
                            BaseUtcOffset = timeZone.BaseUtcOffset
                        });
                }

                foreach (var timeZone in timeZones.Where(x => x.StandardName == id))
                {
                    items.Add(
                        new MyTimeZoneInfo
                        {
                            Id = timeZone.Id,
                            DisplayName = timeZone.DisplayName,
                            StandardName = timeZone.StandardName,
                            BaseUtcOffset = timeZone.BaseUtcOffset
                        });
                }

                if (items.Count == 0)
                {
                    items.Add(
                        new MyTimeZoneInfo
                        {
                            Id = "GMT Standard Time",
                            DisplayName = "(UTC+00:00) Dublin, Edinburgh, Lisbon, London",
                            StandardName = "GMT Standard Time",
                            BaseUtcOffset = new TimeSpan()
                        });
                }

                return items.FirstOrDefault();
            }
            catch
            {
                if (items.Count == 0)
                {
                    items.Add(
                        new MyTimeZoneInfo
                        {
                            Id = "GMT Standard Time",
                            DisplayName = "(UTC+00:00) Dublin, Edinburgh, Lisbon, London",
                            StandardName = "GMT Standard Time",
                            BaseUtcOffset = new TimeSpan()
                        });
                }

                return items.FirstOrDefault();
            }
        }

        public static MyTimeZoneInfo GetTimeZoneByCountryCode(string code)
        {
            try
            {
                var timeZones = TimeZoneInfo.FindSystemTimeZoneById(code);

                return new MyTimeZoneInfo
                {
                    Id = timeZones.Id,
                    DisplayName = timeZones.DisplayName,
                    StandardName = timeZones.StandardName,
                    BaseUtcOffset = timeZones.BaseUtcOffset
                };
            }
            catch (Exception)
            {
                return new MyTimeZoneInfo
                {
                    Id = "GMT Standard Time",
                    DisplayName = "(UTC+00:00) Dublin, Edinburgh, Lisbon, London",
                    StandardName = "GMT Standard Time",
                    BaseUtcOffset = new TimeSpan()
                };
            }
        }

        public static MyTimeZoneInfo GetTimeZoneByCountryDisplayName(string displayName)
        {
            displayName = displayName.Replace(" SAR", string.Empty);
            var timeZones = GetTimeZones();
            List<MyTimeZoneInfo> items = new List<MyTimeZoneInfo>();
            foreach (var timeZone in timeZones.Where(
                         x => x.DisplayName.ToLower().Contains(displayName.ToLower()) ||
                              x.StandardName.ToLower().Contains(displayName.ToLower())))
            {
                items.Add(
                    new MyTimeZoneInfo
                    {
                        Id = timeZone.Id,
                        DisplayName = timeZone.DisplayName,
                        StandardName = timeZone.StandardName,
                        BaseUtcOffset = timeZone.BaseUtcOffset
                    });
            }

            if (items.Count == 0)
            {
                items.Add(
                    new MyTimeZoneInfo
                    {
                        Id = "GMT Standard Time",
                        DisplayName = "(UTC+00:00) Dublin, Edinburgh, Lisbon, London",
                        StandardName = "GMT Standard Time",
                        BaseUtcOffset = new TimeSpan()
                    });
            }

            return items.FirstOrDefault();
        }

        /// <summary>
        /// Get TimeZoneInfo by IANA
        /// </summary>
        /// <param name="timeZoneName">America/Los_Angeles</param>
        /// <returns>MyTimeZoneInfo</returns>
        public static MyTimeZoneInfo GetTimeZoneByIanaId(string timeZoneName)
        {
            var timeZoneInfo = TZConvert.GetTimeZoneInfo(timeZoneName);
            return new MyTimeZoneInfo
            {
                Id = timeZoneInfo.Id,
                DisplayName = GetTimeZoneDisplayName(timeZoneInfo),
                StandardName = timeZoneInfo.StandardName,
                BaseUtcOffset = GetTimeZoneUtcOffset(timeZoneInfo)
            };
        }

        public static string ConvertTimeZoneIdToWindowsTimeZoneId(string ianaTimeZoneId)
        {
            return TZConvert.TryIanaToWindows(ianaTimeZoneId, out var windowsTimeZoneId) ? windowsTimeZoneId : ianaTimeZoneId;
        }
    }

    public static class MyDateTimeUtil
    {
        public static DateTime CreateDateFromTime(int year, int month, int day, DateTime time)
        {
            return new DateTime(year, month, day, time.Hour, time.Minute, 0);
        }

        public static DateTime DateTimeConditionConvertor(TimeValueType? timeValueType, int value)
        {
            switch (timeValueType)
            {
                case TimeValueType.Seconds:
                    return DateTime.UtcNow.AddSeconds(value);
                case TimeValueType.Minutes:
                    return DateTime.UtcNow.AddMinutes(value);
                case TimeValueType.Hours:
                    return DateTime.UtcNow.AddHours(value);
                default:
                    return DateTime.UtcNow.AddDays(value);
            }
        }

        public static DateTime StartOfDay(this DateTime theDate)
        {
            return theDate.Date;
        }

        public static DateTime EndOfDay(this DateTime theDate)
        {
            return theDate.Date.AddDays(1).AddTicks(-1);
        }

        public static DateTime StartOfMonth(this DateTime theDate)
        {
            return new DateTime(theDate.Year, theDate.Month, 1);
        }

        public static DateTime EndOfMonth(this DateTime theDate)
        {
            return theDate.StartOfMonth().AddMonths(1).AddDays(-1);
        }

        public static DateTime EndOfDay(this DateTime theDate, string timeZoneId)
        {
            var timeZoneInfo = TimeZoneHelper.GetTimeZoneById(timeZoneId);
            return EndOfDay(theDate).AddHours(timeZoneInfo.BaseUtcOffsetInHour);
        }

        public static DateTime StartOfDay(this DateTime theDate, string timeZoneId)
        {
            var timeZoneInfo = TimeZoneHelper.GetTimeZoneById(timeZoneId);
            return StartOfDay(theDate).AddHours(timeZoneInfo.BaseUtcOffsetInHour);
        }

        public static DateTime TimeZoned(this DateTime theDate, string timeZoneId)
        {
            var timeZoneInfo = TimeZoneHelper.GetTimeZoneById(timeZoneId);
            return theDate.AddHours(timeZoneInfo.BaseUtcOffsetInHour);
        }

        public static DateTime Tolocaltime(this DateTime theDate, string timeZoneId)
        {
            if (timeZoneId == null)
            {
                return theDate;
            }

            var timeZoneInfo = TimeZoneHelper.GetTimeZoneById(timeZoneId);
            return theDate.AddHours(-timeZoneInfo.BaseUtcOffsetInHour);
        }

        public static DateTime ToDisplayTime(this DateTime theDate, string timeZoneId)
        {
            if (timeZoneId == null)
            {
                return theDate;
            }

            var timeZoneInfo = TimeZoneHelper.GetTimeZoneById(timeZoneId);
            return theDate.AddHours(+timeZoneInfo.BaseUtcOffsetInHour);
        }

        public static DateTime UnixTimeStampToDateTime(long unixTimeStamp)
        {
            DateTime dateTime = new DateTime(1970, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc);
            dateTime = dateTime.AddMilliseconds(unixTimeStamp).ToLocalTime();
            return dateTime;
        }

        public static DateTime UnixTimeStampToDateTimeUTC(long unixTimeStamp)
        {
            var dateTime = new DateTime(1970, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc);
            dateTime = dateTime.AddMilliseconds(unixTimeStamp);
            return dateTime;
        }

        /// <summary>
        /// Converts a UTC DateTime object to a DateTime object in a specific time zone.
        /// As Alex suggested we should not check the kind -> since it will make it very hard to use as an api
        /// Developers should ensure that the input DateTime value is a UTC DateTime.
        /// If the input DateTime is not UTC, the conversion result may be inaccurate.
        /// </summary>
        public static DateTime ConvertUtcDateTimeToSpecificTimeZoneDateTime(this DateTime dateTime, string timeZoneId)
        {
            if (dateTime.Kind is not DateTimeKind.Utc)
            {
                dateTime = DateTime.SpecifyKind(dateTime, DateTimeKind.Utc);
            }

            // Change the offset (e.g. +8:00) base on the time zone
            var dateTimeInSpecificTimeZone = TimeZoneInfo.ConvertTime(
                dateTime,
                TimeZoneInfo.FindSystemTimeZoneById(timeZoneId));

            return DateTime.SpecifyKind(dateTimeInSpecificTimeZone, DateTimeKind.Utc);
        }

        /// <summary>
        /// Converts a DateTime object in a specific time zone to a UTC DateTime object.
        /// As Alex suggested we should not check the kind -> since it will make it very hard to use as an api
        /// Developers should ensure that the input DateTime value is in a specific time zone.
        /// If the input DateTime is not in a specific time zone, the conversion result may be inaccurate.
        /// </summary>
        public static DateTime ConvertSpecificTimeZoneDateTimeToUtcDateTime(this DateTime dateTime, string timeZoneId)
        {
            if (dateTime.Kind is not DateTimeKind.Unspecified)
            {
                dateTime = DateTime.SpecifyKind(dateTime, DateTimeKind.Unspecified);
            }

            var dateTimeInSpecificTimeZone = TimeZoneInfo.ConvertTime(
                dateTime,
                TimeZoneInfo.FindSystemTimeZoneById(timeZoneId),
                TimeZoneInfo.Utc);

            return DateTime.SpecifyKind(dateTimeInSpecificTimeZone, DateTimeKind.Utc);
        }

        public static DateTime ConvertUtcDateTimeStringToUtcDateTime(this string dateTimeString)
        {
            return DateTime.SpecifyKind(DateTimeOffset.Parse(dateTimeString).DateTime, DateTimeKind.Utc);
        }

        // <summary>
        // Sets the UTC offset of a DateTimeOffset object according to the specified time zone ID, taking daylight saving time (DST) into account.
        // </summary>
        public static DateTimeOffset SetTimeZoneUtcOffset(this DateTimeOffset dateTimeOffset, string timeZoneId)
        {
            ValidateTimeZoneId(timeZoneId);

            var timeZoneInfo = GetTimeZoneById(timeZoneId);

            // GetUtcOffset() accounts for DST
            var timeZoneUtcOffset = timeZoneInfo.GetUtcOffset(dateTimeOffset);

            return dateTimeOffset.Offset != timeZoneUtcOffset
                ? dateTimeOffset.ToOffset(timeZoneInfo.BaseUtcOffset)
                : dateTimeOffset;
        }

        public static DateTimeOffset ConvertUtcDateTimeToDateTimeOffset(this DateTime utcDateTime, string timeZoneId)
        {
            ValidateTimeZoneId(timeZoneId);
            var specificTimeZoneDateTime = DateTime.SpecifyKind(
                ConvertUtcDateTimeToSpecificTimeZoneDateTime(utcDateTime, timeZoneId),
                DateTimeKind.Unspecified);
            return ConvertToDateTimeOffset(specificTimeZoneDateTime, timeZoneId);
        }

        public static DateTimeOffset ConvertSpecificTimeZoneDateTimeToDateTimeOffset(
            this DateTime specificTimeZoneDateTime,
            string timeZoneId)
        {
            ValidateTimeZoneId(timeZoneId);
            specificTimeZoneDateTime = DateTime.SpecifyKind(specificTimeZoneDateTime, DateTimeKind.Unspecified);
            return ConvertToDateTimeOffset(specificTimeZoneDateTime, timeZoneId);
        }

        private static void ValidateTimeZoneId(string timeZoneId)
        {
            if (string.IsNullOrWhiteSpace(timeZoneId))
            {
                throw new ArgumentException("Time zone ID must not be null or empty.", nameof(timeZoneId));
            }
        }

        private static DateTimeOffset ConvertToDateTimeOffset(DateTime specificTimeZoneDateTime, string timeZoneId)
        {
            var localTimeZone = GetTimeZoneById(timeZoneId);
            return new DateTimeOffset(specificTimeZoneDateTime, localTimeZone.GetUtcOffset(specificTimeZoneDateTime));
        }

        public static TimeZoneInfo GetTimeZoneById(string timeZoneId)
        {
            try
            {
                return TimeZoneInfo.FindSystemTimeZoneById(timeZoneId);
            }
            catch (TimeZoneNotFoundException)
            {
                throw new TimeZoneNotFoundException($"Time zone with ID '{timeZoneId}' not found.");
            }
        }

        public static bool IsBefore(this DateTime dateTimeToCheck, DateTime before)
        {
            return dateTimeToCheck < before;
        }

        public static bool IsAfter(this DateTime dateTimeToCheck, DateTime after)
        {
            return dateTimeToCheck > after;
        }

        public static bool IsWithinRange(this DateTime dateTimeToCheck, DateTime start, DateTime end)
        {
            return dateTimeToCheck >= start && dateTimeToCheck <= end;
        }

        public static bool IsNotWithinRange(this DateTime dateTimeToCheck, DateTime start, DateTime end)
        {
            return !IsWithinRange(dateTimeToCheck, start, end);
        }

        public static bool IsSameDate(this DateTime dateTimeToCheck, DateTime sameDate)
        {
            return dateTimeToCheck.Year == sameDate.Year &&
                   dateTimeToCheck.Month == sameDate.Month &&
                   dateTimeToCheck.Day == sameDate.Day;
        }

        public static bool IsToday(this DateTime dateTimeToCheckInUtc, string timeZoneId)
        {
            var now = DateTime.UtcNow;
            var today = now.ConvertUtcDateTimeToSpecificTimeZoneDateTime(timeZoneId);
            var datetimeToCheck = dateTimeToCheckInUtc.ConvertUtcDateTimeToSpecificTimeZoneDateTime(timeZoneId);
            return datetimeToCheck.IsSameDate(today);
        }

        public static List<(DateTime StartOfMonth, DateTime EndOfMonth)> GetMonthStartEndLists(
            DateTime start,
            DateTime end)
        {
            var ss = start.StartOfMonth();
            var ee = end.EndOfMonth();
            var months = new List<(DateTime StartOfMonth, DateTime EndOfMonth)>();

            for (var s = ss; s < ee; s = s.AddMonths(1))
            {
                months.Add(new (s, s.EndOfMonth()));
            }

            return months;
        }

        public static long ConvertDateTimeToTimestamp(DateTime dateTime)
        {
            return new DateTimeOffset(dateTime).ToUnixTimeSeconds();
        }
    }
}