using System.Runtime.CompilerServices;
using System.Security.Claims;
using Auth0.AuthenticationApi;
using Auth0.AuthenticationApi.Models;
using Auth0.ManagementApi;
using Auth0.ManagementApi.Models;
using Auth0.ManagementApi.Models.Users;
using Auth0.ManagementApi.Paging;
using isRock.LIFF;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.DotNet.Scaffolding.Shared.Messaging;
using Microsoft.Extensions.Logging.Abstractions;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using Telegram.Bot.Exceptions;
using Travis_backend.AccountAuthenticationDomain.Models;
using Travis_backend.Auth0.Configuration;
using Travis_backend.Auth0.Models;
using Travis_backend.Cache;
using AccessTokenResponse = Auth0.AuthenticationApi.Models.AccessTokenResponse;

[assembly: InternalsVisibleTo("Sleekflow.Core.Tests")]

namespace Travis_backend.Auth0.Services;

public class SleekflowUserManager : UserManager<ApplicationUser>
{
    private readonly ILogger<SleekflowUserManager?> _logger;
    private readonly IAuthenticationApiClient _authenticateApiClient;
    private readonly IManagementApiClient _managementApiClient;
    private readonly IOptions<IdentityOptions> _options;
    private readonly Auth0Config _auth0Config;
    private readonly ICacheManagerService _cacheManagerService;
    internal readonly IHttpContextAccessor HttpContextAccessor;

    public SleekflowUserManager(
        IUserStore<ApplicationUser> store,
        IOptions<IdentityOptions> optionsAccessor,
        IPasswordHasher<ApplicationUser> passwordHasher,
        IEnumerable<IUserValidator<ApplicationUser>> userValidators,
        IEnumerable<IPasswordValidator<ApplicationUser>> passwordValidators,
        ILookupNormalizer keyNormalizer,
        IdentityErrorDescriber errors,
        IServiceProvider services,
        ILogger<SleekflowUserManager> logger,
        Auth0Config auth0Config,
        IManagementApiClient managementApiClient,
        IAuthenticationApiClient authenticateApiClient,
        IHttpContextAccessor httpContextAccessor,
        ICacheManagerService cacheManagerService)
        : base(
            store,
            optionsAccessor,
            passwordHasher,
            userValidators,
            passwordValidators,
            keyNormalizer,
            errors,
            services,
            logger)
    {
        _options = optionsAccessor;
        _logger = logger;
        _auth0Config = auth0Config;
        _managementApiClient = managementApiClient;
        _authenticateApiClient = authenticateApiClient;
        HttpContextAccessor = httpContextAccessor;
        _cacheManagerService = cacheManagerService;
    }

    /// <summary>
    /// For auth0EventUserAsApplicationUser name checking, because Auth0 does not allow using email string as auth0EventUserAsApplicationUser name.
    /// </summary>
    public static bool IsValidEmail(string email)
    {
        try
        {
            var isEmail = new System.Net.Mail.MailAddress(email);
            return isEmail.Address == email;
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// Convert Auth0 user to Application User.
    /// </summary>
    public static ApplicationUser ToApplicationUser(User auth0User)
    {
        var auth0AppMetadata = GetAuth0AppMetadata(auth0User);
        var dbUser = new ApplicationUser()
        {
            Id = (auth0AppMetadata?.SleekflowId ?? null)!,
            UserName = auth0User.UserName ?? auth0User.Email,
            Email = auth0User.Email,
            DisplayName = auth0User.FullName,
            FirstName = auth0User.FirstName,
            LastName = auth0User.LastName,
            PictureUrl = auth0User.Picture,
            PhoneNumber = auth0AppMetadata?.PhoneNumber,
            EmailConfirmed = auth0User.EmailVerified ?? false
        };

        return dbUser;
    }

    /// <summary>
    /// Convert Auth0 user to Auth0 UserUpdateRequest.
    /// </summary>
    public static UserUpdateRequest ToUserUpdateRequest(User user)
    {
        var result = new UserUpdateRequest()
        {
            FullName = user.FullName,
            NickName = user.NickName,
            FirstName = user.FirstName,
            LastName = user.LastName,
            Picture = user.Picture,
            EmailVerified = user.EmailVerified,
            UserMetadata = user.UserMetadata,
            AppMetadata = user.AppMetadata
        };

        return result;
    }

    /// <summary>
    /// Convert ApplicationUser to Auth0 UserUpdateRequest.
    /// </summary>
    private async Task<UserUpdateRequest> ToUserUpdateRequest(
        ApplicationUser dbUser, User auth0User, string? tenantHubUserId = null)
    {
        var roles = await GetRolesAsync(dbUser.Id);

        var auth0AppMetadata = GetAuth0AppMetadata(auth0User)!;
        auth0AppMetadata.Roles = roles;
        auth0AppMetadata.PhoneNumber = dbUser.PhoneNumber;
        auth0AppMetadata.TenantHubUserId = tenantHubUserId ?? auth0AppMetadata.TenantHubUserId;

        if (dbUser.Id != auth0AppMetadata.SleekflowId)
        {
            throw new Exception("SleekflowId is not equal to user.Id.");
        }

        // "auth0" is the correct value instead of Sleekflow-Username-Password-Authentication
        UserUpdateRequest userUpdateRequest;
        if (auth0User.Identities.Any(i => i.Provider == "auth0"))
        {
            userUpdateRequest = new UserUpdateRequest()
            {
                FullName = dbUser.DisplayName,
                FirstName = dbUser.FirstName,
                LastName = dbUser.LastName,
                Picture = dbUser.PictureUrl,
                AppMetadata = auth0AppMetadata,
            };
        }
        else
        {
            // social logins don't allow to update the name, family_name, given_name, picture
            userUpdateRequest = new UserUpdateRequest()
            {
                AppMetadata = auth0AppMetadata
            };
        }

        if (dbUser.EmailConfirmed)
        {
            userUpdateRequest.EmailVerified = true;
        }

        return userUpdateRequest;
    }

    /// <summary>
    /// Convert ApplicationUser to Auth0 CreateRequest.
    /// </summary>
    public static UserCreateRequest ToCreateRequest(ApplicationUser user, string? tenantHubUserId = null)
    {
        if (user.UserName == null || user.UserName == user.Email)
        {
            user.UserName = $"invite.{GenerateRandomString(22)}";
        }

        var result = new UserCreateRequest()
        {
            UserId = user.Id,
            UserName = user.UserName,
            Email = user.Email,
            FullName = user.DisplayName,
            FirstName = user.FirstName,
            LastName = user.LastName,
            Picture = user.PictureUrl,
            AppMetadata = new Auth0AppMetadata(
                new List<string>(),
                user.PhoneNumber,
                user.Id,
                true,
                null,
                tenantHubUserId),
            EmailVerified = user.EmailConfirmed
        };

        return result;
    }

    public string? FindClaimsValue(ClaimsPrincipal principal, string name, bool searchAuth0Namespace = false)
    {
        return searchAuth0Namespace
            ? principal.Claims.FirstOrDefault(d => d.Type == $"{_auth0Config.Namespace}{name}")?.Value
            : principal.Claims.FirstOrDefault(d => d.Type == name)?.Value;
    }

    public static Auth0AppMetadata? GetAuth0AppMetadata(User auth0User)
    {
        Auth0AppMetadata? auth0AppMetadata =
            auth0User.AppMetadata is null
                ? null
                : JsonConvert.DeserializeObject<Auth0AppMetadata?>(JsonConvert.SerializeObject(auth0User.AppMetadata));

        return auth0AppMetadata;
    }

    public async Task<(ApplicationUser, Auth0AppMetadata)?> FindByAuth0User(User auth0User)
    {
        var auth0UserAppMetadata = GetAuth0AppMetadata(auth0User);
        if (auth0UserAppMetadata == null)
        {
            return null;
        }

        var sleekflowId = auth0UserAppMetadata.SleekflowId;
        if (sleekflowId == null)
        {
            return null;
        }

        var user = await base.FindByIdAsync(sleekflowId);
        if (user == null)
        {
            throw new Exception($"Unable to find the user in the database with Id {sleekflowId}.");
        }

        return (user, auth0UserAppMetadata);
    }

    public async Task<string> GeneratePasswordResetUrlAsync(ApplicationUser dbUser)
    {
        var auth0Users = await GetAuth0Users(dbUser);
        if (auth0Users.Count == 0)
        {
            throw new Exception("Unable to find the user in Auth0.");
        }

        var auth0User = auth0Users.FirstOrDefault(
            u => u.Identities.Any(i => i.Connection == "Sleekflow-Username-Password-Authentication"));
        if (auth0User == null)
        {
            throw new Exception("Unable to find the user in Auth0.");
        }

        var ticket = await _managementApiClient.Tickets.CreatePasswordChangeTicketAsync(
            new PasswordChangeTicketRequest()
            {
                ResultUrl = "https://app.sleekflow.io", UserId = auth0User.UserId, MarkEmailAsVerified = false
            });
        return ticket.Value;
    }

    public async Task<User> CreateNewAuth0User(UserCreateRequest user)
    {
        user.Connection = _auth0Config.DatabaseConnectionName;
        user.Password = GenerateRandomString(32);

        try
        {
            var createResponse = await _managementApiClient.Users.CreateAsync(user);
            if (createResponse.GetType() != typeof(User))
            {
                _logger.LogError(
                    "[CreateNewAuth0User] User {UserEmail} cannot be created in Auth0",
                    user.Email);

                throw new Exception("Create user fail");
            }

            return createResponse;
        }
        catch (Exception err)
        {
            throw new Exception($"[CreateNewAuth0User] {err.Message}");
        }
    }

    public async Task<ApplicationUser> CreateNewDbUserByAuth0User(
        User auth0User)
    {
        var auth0UserAsApplicationUser = ToApplicationUser(auth0User);

        if (string.IsNullOrEmpty(auth0UserAsApplicationUser.Id))
        {
            throw new Exception($"[{nameof(CreateNewDbUserByAuth0User)}] User {auth0UserAsApplicationUser.Email} has no Id");
        }

        var createResult = await base.Store.CreateAsync(auth0UserAsApplicationUser, base.CancellationToken);
        if (createResult != IdentityResult.Success)
        {
            throw new Exception(
                "Unable to create the user. " + string.Join(
                    ", ",
                    createResult.Errors.Select(e => e.Code + ": " + e.Description)));
        }

        var passwordResetToken = await GeneratePasswordResetTokenAsync(auth0UserAsApplicationUser);
        var passwordResetResult = await base.ResetPasswordAsync(
            auth0UserAsApplicationUser,
            passwordResetToken,
            GenerateRandomString(60));
        if (passwordResetResult != IdentityResult.Success)
        {
            throw new Exception("Unable to reset the password.");
        }

        return auth0UserAsApplicationUser;
    }

    public async Task<ApplicationUser> UpdateDbUserByAuth0User(
        User auth0User)
    {
        var auth0UserAsApplicationUser = ToApplicationUser(auth0User);
        if (string.IsNullOrEmpty(auth0UserAsApplicationUser.Id))
        {
            throw new Exception($"[{nameof(UpdateDbUserByAuth0User)}] User {auth0UserAsApplicationUser.Email} has no Id");
        }

        var dbUser = await this.FindByIdAsync(auth0UserAsApplicationUser.Id);
        if (dbUser == null)
        {
            throw new Exception($"[{nameof(UpdateDbUserByAuth0User)}] User {auth0UserAsApplicationUser.Email} not found");
        }

        dbUser.FirstName = auth0UserAsApplicationUser?.FirstName;
        dbUser.UserName = auth0UserAsApplicationUser?.UserName;
        dbUser.LastName = auth0UserAsApplicationUser?.LastName;
        dbUser.DisplayName = auth0UserAsApplicationUser?.DisplayName;
        dbUser.EmailConfirmed = auth0UserAsApplicationUser?.EmailConfirmed ?? false;
        dbUser.IsAgreeMarketingConsent = auth0UserAsApplicationUser?.IsAgreeMarketingConsent ?? dbUser.IsAgreeMarketingConsent;

        var createResult = await base.Store.UpdateAsync(dbUser, base.CancellationToken);
        if (createResult != IdentityResult.Success)
        {
            throw new Exception(
                "Unable to update the user. " + string.Join(
                    ", ",
                    createResult.Errors.Select(e => e.Code + ": " + e.Description)));
        }

        return auth0UserAsApplicationUser;
    }

    public async Task<(Auth0AppMetadata Auth0AppMetadata, ApplicationUser Auth0UserAsApplicationUser)> CreateNewDbUser(
        User auth0User)
    {
        var auth0UserAsApplicationUser = ToApplicationUser(auth0User);

        auth0UserAsApplicationUser.Id = auth0UserAsApplicationUser.Id
                                        ?? Guid.NewGuid().ToString();

        var createResult = await this.Store.CreateAsync(auth0UserAsApplicationUser, this.CancellationToken);
        if (createResult != IdentityResult.Success)
        {
            throw new Exception(
                "Unable to create the user. " + string.Join(
                    ", ",
                    createResult.Errors.Select(e => e.Code + ": " + e.Description)));
        }

        var passwordResetToken = await GeneratePasswordResetTokenAsync(auth0UserAsApplicationUser);
        var passwordResetResult = await base.ResetPasswordAsync(
            auth0UserAsApplicationUser,
            passwordResetToken,
            GenerateRandomString(64));
        if (passwordResetResult != IdentityResult.Success)
        {
            throw new Exception("Unable to reset the password.");
        }

        var userRoles = await GetRolesAsync(auth0UserAsApplicationUser.Id);

        var auth0AppMetadata = new Auth0AppMetadata(
            userRoles,
            auth0UserAsApplicationUser.PhoneNumber,
            auth0UserAsApplicationUser.Id,
            true,
            null);
        return (auth0AppMetadata, auth0UserAsApplicationUser);
    }

    public async Task<(User UpdatedAuth0User, ApplicationUser Auth0UserAsApplicationUser)>
        CreateNewDbUserAndAssociateWithAuth0UserOnAuth0(User auth0User)
    {
        var auth0UserAsApplicationUser = ToApplicationUser(auth0User);

        // Check if the user already exists in the database
        var duplicateUser = await base.FindByEmailAsync(auth0UserAsApplicationUser.Email!)
                            ?? await base.FindByNameAsync(auth0UserAsApplicationUser.UserName!);
        if (duplicateUser is not null)
        {
            throw new Exception("Unable to create the user due to the email or username is duplicate.");
        }

        auth0UserAsApplicationUser.Id = Guid.NewGuid().ToString();

        var createResult = await this.Store.CreateAsync(auth0UserAsApplicationUser, this.CancellationToken);
        if (createResult != IdentityResult.Success)
        {
            throw new Exception(
                "Unable to create the user. " + string.Join(
                    ", ",
                    createResult.Errors.Select(e => e.Code + ": " + e.Description)));
        }

        var passwordResetToken = await GeneratePasswordResetTokenAsync(auth0UserAsApplicationUser);
        var passwordResetResult = await base.ResetPasswordAsync(
            auth0UserAsApplicationUser,
            passwordResetToken,
            GenerateRandomString(32));
        if (passwordResetResult != IdentityResult.Success)
        {
            throw new Exception("Unable to reset the password.");
        }

        var userRoles = await GetRolesAsync(auth0UserAsApplicationUser.Id);

        var auth0AppMetadata = new Auth0AppMetadata(
            userRoles,
            auth0UserAsApplicationUser.PhoneNumber,
            auth0UserAsApplicationUser.Id,
            true,
            null);
        var updatedAuth0User = await _managementApiClient.Users.UpdateAsync(
            auth0User.UserId,
            new UserUpdateRequest()
            {
                AppMetadata = auth0AppMetadata,
            });

        if (updatedAuth0User != null)
        {
            return (updatedAuth0User, auth0UserAsApplicationUser);
        }

        throw new Exception("Unable to associate with the Auth0User.");
    }

    public async Task<(User UpdatedAuth0User, ApplicationUser DbUser)> AssociateDbUserWithAuth0UserOnAuth0(
        User auth0User,
        ApplicationUser dbUser)
    {
        _logger.LogInformation(
            "AssociateDbUserWithAuth0UserOnAuth0 {Auth0EventUser}, Mapping {Auth0UserId} to {SleekflowUserId}",
            JsonConvert.SerializeObject(auth0User, Formatting.None),
            auth0User.UserId,
            dbUser.Id);

        var roles = await GetRolesAsync(dbUser.Id);
        var appMetadata = new Auth0AppMetadata(
            roles,
            dbUser.PhoneNumber,
            dbUser.Id,
            true,
            null);
        var updatedAuth0User = await _managementApiClient.Users.UpdateAsync(
            auth0User.UserId,
            new UserUpdateRequest
            {
                AppMetadata = appMetadata
            });

        return (updatedAuth0User, dbUser);
    }

    public async Task<string> RequestPasswordReset(string email)
    {
        try
        {
            var dbUser = await FindByEmailAsync(email);
            var auth0Users = (dbUser == null)
                ? await GetAllAuth0UsersByEmailAsync(email)
                : await GetAuth0Users(dbUser);
            var auth0PasswordUser =
                auth0Users?.FirstOrDefault(u => u.Identities.Any(i => i.Provider == "auth0")) ?? null;

            if (auth0Users is null)
            {
                _logger.LogError(
                    "[ResetPasswordRequest] User {Email} not found in Auth0",
                    email);

                throw new Exception("User not found");
            }

            if (auth0PasswordUser is null)
            {
                var firstDbUser = auth0Users.MinBy(u => u.CreatedAt)!;
                var rndPassword = GenerateRandomString(30);
                var createRequest = ToCreateRequest(ToApplicationUser(firstDbUser));

                createRequest.Connection = _auth0Config.DatabaseConnectionName;
                createRequest.Password = rndPassword;

                var createResponse = await _managementApiClient.Users.CreateAsync(createRequest);
                if (createResponse.GetType() != typeof(User))
                {
                    _logger.LogError(
                        "[ResetPasswordRequest] User {Email} cannot be created in Auth0",
                        email);

                    throw new Exception("Create user fail");
                }
            }

            var responseMessage = await _authenticateApiClient.ChangePasswordAsync(
                new ChangePasswordRequest()
                {
                    ClientId = _auth0Config.ClientId, Connection = _auth0Config.DatabaseConnectionName, Email = email
                });
            return responseMessage;
        }
        catch (Exception err)
        {
            throw new Exception(err.Message);
        }
    }

    public async Task<bool> CancelLoginAsUser(ApplicationUser user)
    {
        _logger.LogInformation($"Dive - User {user.Id} cancelling dive");

        var auth0Users = await GetAuth0Users(user);

        foreach (var auth0User in auth0Users)
        {
            try
            {
                var appMetadata = GetAuth0AppMetadata(auth0User)!;
                appMetadata.LoginAsUser = new LoginAsUser();

                var cancelDiveRequest = new UserUpdateRequest()
                {
                    AppMetadata = appMetadata
                };
                var updatedUser = await _managementApiClient.Users.UpdateAsync(auth0User.UserId, cancelDiveRequest);
            }
            catch (Exception err)
            {
                _logger.LogError(
                    err,
                    "Dive - Auth0User {UserId} failed to cancel dive",
                    auth0User.UserId);
            }

            await Task.Delay(1000);
        }

        _logger.LogInformation($"Dive - User {user.Email} cancelled dive");

        return true;
    }

    public async Task<LoginAsUser?> SetLoginAsUser(
        ApplicationUser dbUser,
        string targetCompanyId,
        string targetStaffIdentityId,
        long targetStaffId,
        int durationInMinutes)
    {
        _logger.LogInformation(
            "Dive - User {AdminUser} starting dive to user: {TargetStaffIdentityId}, Company: {TargetCompanyId}",
            dbUser.Id,
            targetStaffIdentityId,
            targetCompanyId);

        var getUsersRequest = new GetUsersRequest()
        {
            Query = $"app_metadata.sleekflow_id:\"{dbUser.Id}\""
        };
        var auth0Users = await _managementApiClient.Users.GetAllAsync(getUsersRequest);
        if (auth0Users.Count == 0)
        {
            throw new Exception("User not found");
        }

        var targetAuth0Users = await _managementApiClient.Users.GetAllAsync(
            new GetUsersRequest()
            {
                Query = $"app_metadata.sleekflow_id:\"{targetStaffIdentityId}\""
            });
        if (targetAuth0Users.Count == 0)
        {
            throw new Exception("Target user not found");
        }

        var targetAuth0User = targetAuth0Users.FirstOrDefault(
            u => u.Identities.Any(i => i.Provider.Contains("auth0")));
        targetAuth0User ??= targetAuth0Users[0];
        var targetTenantHubUserId = GetAuth0AppMetadata(targetAuth0User)?.TenantHubUserId;

        var expireAt = DateTime.UtcNow.AddMinutes(durationInMinutes);
        var targetUserRoles = await GetRolesAsync(targetStaffIdentityId);

        var loginAsUser = new LoginAsUser()
        {
            UserId = targetStaffIdentityId,
            TenantHubUserId = targetTenantHubUserId,
            StaffId = targetStaffId,
            CompanyId = targetCompanyId,
            Roles = targetUserRoles,
            ExpireAt = expireAt
        };

        foreach (var auth0User in auth0Users)
        {
            try
            {
                var auth0AppMetadata = GetAuth0AppMetadata(auth0User)!;
                auth0AppMetadata.LoginAsUser = loginAsUser;

                var userUpdateRequestForLoginAsUser = new UserUpdateRequest()
                {
                    AppMetadata = auth0AppMetadata
                };
                var updatedUser = await _managementApiClient.Users.UpdateAsync(
                    auth0User.UserId,
                    userUpdateRequestForLoginAsUser);
            }
            catch (Exception err)
            {
                _logger.LogError(
                    err,
                    "Dive - Auth0User {UserId} failed to dive",
                    auth0User.UserId);
            }
        }

        _logger.LogInformation(
            "Dive - User {AdminUser} dived to user: {TargetStaffIdentityId}, Company: {TargetCompanyId}",
            dbUser.Id,
            targetStaffIdentityId,
            targetCompanyId);

        return loginAsUser;
    }

    public static string GenerateRandomString(int length = 10, bool includeSymbols = true)
    {
        var rnd = new Random(Guid.NewGuid().GetHashCode());
        var chars = includeSymbols
            ? "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789-_"
            : "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";

        return new string(
            Enumerable.Repeat(chars, length)
                .Select(s => s[rnd.Next(s.Length)]).ToArray());
    }

    /// <summary>
    /// Return Auth0 id from ClaimsPrincipal.
    /// </summary>
    /// <remarks>
    /// For Auth0 update purpose, so we should return the auth0 id.
    /// Return Auth0 id from ClaimsPrincipal.
    /// note: This method cannot applied to Test case.
    /// </remarks>
    public override string GetUserId(ClaimsPrincipal principal)
    {
        if (principal == null)
        {
            throw new ArgumentNullException(nameof(principal));
        }

        var id = FindClaimsValue(principal, _options.Value.ClaimsIdentity.UserIdClaimType);

        return id;
    }

    public IList<string>? GetRolesFromClaims(ClaimsPrincipal principal)
    {
        var stringValue = FindClaimsValue(principal, _options.Value.ClaimsIdentity.RoleClaimType);
        if (stringValue is null)
        {
            return null;
        }

        var roles = JsonConvert.DeserializeObject<IList<string>>(stringValue);
        return roles;
    }

    public override async Task<ApplicationUser> GetUserAsync(ClaimsPrincipal principal)
    {
        var user = await base.GetUserAsync(principal);
        if (user == null)
        {
            var email = FindClaimsValue(principal, _options.Value.ClaimsIdentity.EmailClaimType);
            if (email != null)
            {
                user = await base.FindByEmailAsync(email);
            }
        }

        return user!;
    }

    public string? GetAuth0ConnectionFromHttpContext(ClaimsPrincipal? principal = null)
    {
        if (principal == null && HttpContextAccessor != null)
        {
            principal = HttpContextAccessor.HttpContext?.User;
        }

        if (principal is null)
        {
            return null;
        }

        return FindClaimsValue(principal, "connection", true);
    }

    public string? GetClaimAuth0ConnectionStrategy(ClaimsPrincipal? principal = null)
    {
        if (principal == null && HttpContextAccessor != null)
        {
            principal = HttpContextAccessor.HttpContext?.User;
        }

        if (principal is null)
        {
            return null;
        }

        return FindClaimsValue(principal, "connection_strategy", true);
    }

    public async Task<AccessTokenResponse> GetAuth0UserTokenAsync(string username, string password)
    {
        var accessTokenResponse = await _authenticateApiClient.GetTokenAsync(
            new ResourceOwnerTokenRequest
            {
                Audience = _auth0Config.Audience,
                ClientId = _auth0Config.ClientId,
                ClientSecret = _auth0Config.ClientSecret,
                Realm = _auth0Config.DatabaseConnectionName,
                Password = password,
                Scope = "openid",
                Username = username,
            });

        return accessTokenResponse;
    }

    /// <summary>
    ///
    /// </summary>
    /// <param name="user"></param>
    /// <returns></returns>
    public async Task<string?> GetAuth0UserIdAsync(ApplicationUser user)
    {
        string query;
        var connection = GetAuth0ConnectionFromHttpContext();
        if (!string.IsNullOrEmpty(user.Email))
        {
            query = $"email:{user.Email}";
        }
        else
        {
            query = $"username:{user.UserName}";
        }

        query += string.IsNullOrEmpty(connection) ? string.Empty : $" AND identities.connection:\"{connection}\"";

        var getUsersRequest = new GetUsersRequest()
        {
            Query = query
        };
        var items = await _managementApiClient.Users.GetAllAsync(getUsersRequest);
        var result = items.First();

        return result?.UserId;
    }

    internal async Task<ApplicationUser?> GetUserAsync(ApplicationUser user)
    {
        return await base.FindByIdAsync(user.Id);
    }

    internal async Task<List<User>> GetAuth0Users(ApplicationUser dbUser)
    {
        var getUsersRequest = new GetUsersRequest()
        {
            Query = $"app_metadata.sleekflow_id:\"{dbUser.Id}\""
        };

        var auth0Users = await _managementApiClient.Users.GetAllAsync(getUsersRequest);
        if (auth0Users.Count != 0)
        {
            return auth0Users.ToList();
        }

        // Search by email can have immediate consistency result.
        // ref: https://auth0.com/docs/manage-users/user-search?_gl=1*16d9rpm*_gcl_au*NTk0OTc2NjE2LjE3MDQ4NjgxNzE.*_ga*MTkxODc3NTIzMy4xNjg3NDg5MTU3*_ga_QKMSDV5369*MTcwOTI3Mzg2MS44NS4xLjE3MDkyNzM4NzUuNDYuMC4w
        var usersByEmail = await _managementApiClient.Users.GetUsersByEmailAsync(dbUser.Email);

        if (usersByEmail.Count == 0)
        {
            throw new Exception("No user is found");
        }

        return usersByEmail.ToList();
    }

    public async Task<List<User>> GetAuth0UsersById(string sleekflowId)
    {
        var getUsersRequest = new GetUsersRequest()
        {
            Query = $"app_metadata.sleekflow_id:\"{sleekflowId}\""
        };
        var auth0Users = await _managementApiClient.Users.GetAllAsync(getUsersRequest);
        if (auth0Users.Count == 0)
        {
            throw new Exception("No user is found");
        }

        return auth0Users.ToList();
    }

    public async Task<IPagedList<User>> GetAuth0UsersByQuery(string query, int page=0, int itemPerPage=50)
    {
        var getUsersRequest = new GetUsersRequest()
        {
            Query = $"{query}"
        };
        var users = await _managementApiClient.Users.GetAllAsync(
            getUsersRequest,
            new PaginationInfo(page, itemPerPage, true));

        return users;
    }

    public async Task<List<User>> GetAllAuth0UsersByEmailAsync(string email)
    {
        var getUsersRequest = new GetUsersRequest()
        {
            Query = $"email:{email}"
        };
        var users = await _managementApiClient.Users.GetAllAsync(getUsersRequest);

        return users.ToList();
    }

    /// <summary>
    ///
    /// </summary>
    /// <param name="userId"></param>
    /// <returns></returns>
    private async Task<IList<string>?> GetRolesAsync(string userId)
    {
        var user = await FindByIdAsync(userId);
        return await GetRolesAsync(user!);
    }

    /// <summary>
    /// Search users by role.
    /// </summary>
    /// <param name="roleName"></param>
    /// <returns></returns>
    public override async Task<IList<ApplicationUser>> GetUsersInRoleAsync(string roleName)
    {
        return await base.GetUsersInRoleAsync(roleName);
    }

    /// <summary>
    /// Assign role to auth0EventUserAsApplicationUser.
    /// </summary>
    /// <param name="user"></param>
    /// <param name="role"></param>
    /// <returns></returns>
    public override async Task<IdentityResult> AddToRoleAsync(ApplicationUser user, string role)
    {
        ThrowIfDisposed();

        var dbUser = (await base.FindByIdAsync(user.Id))!;
        var dbUpdateResult = await base.AddToRoleAsync(dbUser, role);

        return dbUpdateResult;
    }

    public override Task<IdentityResult> RemoveFromRolesAsync(ApplicationUser user, IEnumerable<string> roles)
    {
        throw new NotImplementedException();
    }

    /// <summary>
    /// Remove auth0EventUserAsApplicationUser role.
    /// </summary>
    /// <param name="user"></param>
    /// <param name="role"></param>
    /// <returns></returns>
    public override async Task<IdentityResult> RemoveFromRoleAsync(ApplicationUser user, string role)
    {
        ThrowIfDisposed();

        var dbUser = (await base.FindByIdAsync(user.Id))!;
        var dbUpdateResult = await base.RemoveFromRoleAsync(dbUser, role);

        return dbUpdateResult;
    }

    public override async Task<IdentityResult> CreateAsync(ApplicationUser user, string password)
    {
        var result = await CreateWithTenantHubIdAsync(user, password, null);
        return result.IdentityResult;
    }

    /// <summary>
    /// Create "Application auth0EventUserAsApplicationUser" with password.
    /// </summary>
    /// <param name="user"></param>
    /// <param name="password"></param>
    /// <param name="tenantHubUserId"></param>
    /// <returns></returns>
    /// <exception cref="Exception"></exception>
    public async Task<(IdentityResult IdentityResult, User? User)> CreateWithTenantHubIdAsync(
        ApplicationUser user,
        string password,
        string? tenantHubUserId)
    {
        ThrowIfDisposed();

        try
        {
            if (!IsValidEmail(user.Email))
            {
                return (IdentityResult.Failed(ErrorDescriber.InvalidEmail(user.Email)), null);
            }

            var duplicate = base.Users.Any(u => u.Email == user.Email);
            if (duplicate)
            {
                return (IdentityResult.Failed(ErrorDescriber.DuplicateEmail(user.Email)), null);
            }

            var result = await ValidateUserAsync(user);
            if (!result.Succeeded)
            {
                return (result, null);
            }

            await UpdateNormalizedUserNameAsync(user);
            await UpdateNormalizedEmailAsync(user);

            var createResult = await base.Store.CreateAsync(user, base.CancellationToken);
            if (createResult != IdentityResult.Success)
            {
                return (createResult, null);
            }

            var resetToken = await GeneratePasswordResetTokenAsync(user);
            var pwdResult = await base.ResetPasswordAsync(user, resetToken, password);
            if (pwdResult != IdentityResult.Success)
            {
                await base.DeleteAsync(user);
                return (IdentityResult.Failed(pwdResult.Errors.ToArray()), null);
            }

            try
            {
                var request = ToCreateRequest(user, tenantHubUserId);
                request.Password = password;
                request.Connection = _auth0Config.DatabaseConnectionName;

                var auth0Response = await _managementApiClient.Users.CreateAsync(request);
                if (auth0Response.GetType() != typeof(User))
                {
                    return (IdentityResult.Failed(), null);
                }

                return (
                    auth0Response.GetType() == typeof(User) ? IdentityResult.Success : IdentityResult.Failed(),
                    auth0Response);
            }
            catch (Exception auth0Exception)
            {
                await base.DeleteAsync(user);
                return (IdentityResult.Failed(
                    new IdentityError()
                    {
                        Code = "OtherError", Description = auth0Exception.Message
                    }), null);
            }
        }
        catch (Exception exception)
        {
            return (IdentityResult.Failed(
                new IdentityError()
                {
                    Code = exception.Source ?? "OtherError", Description = exception.Message
                }), null);
        }
    }

    public override async Task<IdentityResult> AddPasswordAsync(ApplicationUser user, string password)
    {
        var auth0Users = await GetAuth0Users(user);
        if (auth0Users.Count == 0)
        {
            throw new Exception("Unable to find the user in Auth0.");
        }

        var auth0User = auth0Users.FirstOrDefault(
            u => u.Identities.Any(i => i.Connection == "Sleekflow-Username-Password-Authentication"));
        if (auth0User == null)
        {
            throw new Exception("Unable to find the user in Auth0.");
        }

        User auth0Response;
        try
        {
            auth0Response = await _managementApiClient.Users.UpdateAsync(
                auth0User.UserId,
                new UserUpdateRequest()
                {
                    Password = password
                });
        }
        catch (Exception err)
        {
            string[] auth0Error = err.Message.Split(':');
            if (auth0Error.Length > 1)
            {
                return IdentityResult.Failed(
                    new IdentityError()
                    {
                        Code = auth0Error[0], Description = auth0Error[1]
                    });
            }

            return IdentityResult.Failed(
                new IdentityError()
                {
                    Description = err.Message
                });
        }

        return auth0Response != null && auth0Response.GetType() == typeof(User)
            ? await base.AddPasswordAsync(user, password)
            : IdentityResult.Failed(
                new IdentityError()
                {
                    Description = "Cannot create Auth0 user."
                });
    }


    public override async Task<IdentityResult> CreateAsync(ApplicationUser user)
    {
        var result = await CreateWithTenantHubIdAsync(user);
        return result.IdentityResult;
    }

    /// <summary>
    /// Create new auth0EventUserAsApplicationUser with random password because auth0 does not allow empty password.
    /// Caution: We cannot call base.CreateAsync or this.CreateAsync because it's will cause "Slack overflow" error on
    ///     base UserManager.
    /// </summary>
    /// <param name="user"></param>
    /// <returns>IdentityResult.</returns>
    public async Task<(IdentityResult IdentityResult, User? Auth0User)> CreateWithTenantHubIdAsync(ApplicationUser user, string tenantHubUserId = null)
    {
        /*
         * Auth0: Cannot insert auth0EventUserAsApplicationUser without password.
         * This function is for create auth0EventUserAsApplicationUser in db only.
         * Calling this will cause "Slack overflow" error.
         *   var rndPassword = SleekflowUserManager.GenerateRandomString(32);
         *   return await this.CreateAsync(auth0EventUserAsApplicationUser, rndPassword);
         *
         * update: create random password to compatible to old logic.
         * error return: auth0 result to identities.
         */
        try
        {
            if (!IsValidEmail(user.Email))
            {
                return (IdentityResult.Failed(ErrorDescriber.InvalidEmail(user.Email)), null);
            }

            var isDuplicateUser = base.Users.Any(u => u.Email == user.Email);
            if (isDuplicateUser)
            {
                return (IdentityResult.Failed(ErrorDescriber.DuplicateEmail(user.Email)), null);
            }

            var rndPassword = SleekflowUserManager.GenerateRandomString(32);
            var request = ToCreateRequest(user, tenantHubUserId);
            request.Password = rndPassword;
            request.Connection = _auth0Config.DatabaseConnectionName;

            var createResult = await base.CreateAsync(user);

            if (!createResult.Succeeded)
            {
                return (createResult, null);
            }

            try
            {
                var auth0Response = await _managementApiClient.Users.CreateAsync(request);
                if (auth0Response.GetType() != typeof(User))
                {
                    await base.DeleteAsync(user);
                    return (IdentityResult.Failed(
                        new IdentityError()
                        {
                            Description = "Error occur when trying to create auth0 user."
                        }), null);
                }

                return (IdentityResult.Success, auth0Response);
            }
            catch (Exception err)
            {
                await base.DeleteAsync(user);
                return (IdentityResult.Failed(
                    new IdentityError()
                    {
                        Code = "OtherError", Description = err.Message
                    }), null);
            }
        }
        catch (Exception exception)
        {
            return (IdentityResult.Failed(
                new IdentityError()
                {
                    Code = "OtherError", Description = exception.Message
                }), null);
        }
    }

    /// <summary>
    /// Please aware this api is not suggested by Auth0, just added it just in case.
    /// Because it will not show any identity errors if the password string doesn't meet the password policy from Auth0.
    /// </summary>
    /// <param name="user"></param>
    /// <param name="newPassword"></param>
    /// <exception cref="UserNotFoundException"></exception>
    /// <returns></returns>
    public async Task<IdentityResult> ResetPasswordAsync(ApplicationUser user, string newPassword)
    {
        var dbUser = await base.FindByIdAsync(user.Id);
        if (dbUser is null)
        {
            throw new Exception("User not found");
        }

        var token = await base.GeneratePasswordResetTokenAsync(user);
        var identityResult = await this.ResetPasswordAsync(dbUser, token, newPassword);

        return identityResult;
    }

    /// <summary>
    ///
    /// </summary>
    /// <param name="user"></param>
    /// <param name="token"></param>
    /// <param name="newPassword"></param>
    /// <returns></returns>
    public override async Task<IdentityResult> ResetPasswordAsync(
        ApplicationUser user,
        string token,
        string newPassword)
    {
        var dbUser = (await base.FindByIdAsync(user.Id))!;
        var dbUpdateResult = await base.ResetPasswordAsync(user, token, newPassword);
        if (dbUpdateResult != IdentityResult.Success)
        {
            return dbUpdateResult;
        }

        var auth0Users = await GetAuth0Users(dbUser);
        foreach (var auth0User in auth0Users)
        {
            if (auth0User.Identities.All(i => i.Connection != _auth0Config.DatabaseConnectionName))
            {
                continue;
            }

            try
            {
                var updatedUser = await _managementApiClient.Users.UpdateAsync(
                    auth0User.UserId,
                    new UserUpdateRequest()
                    {
                        Password = newPassword
                    });

                await Task.Delay(1000);

                if (updatedUser.GetType() != typeof(User))
                {
                    throw new Exception("The response type is incorrect");
                }
            }
            catch (Exception e)
            {
                throw new Exception(e.Message);
            }

            await Task.Delay(1000);
        }

        return IdentityResult.Success;
    }

    public override async Task<IdentityResult> UpdateAsync(ApplicationUser user)
    {
        var result = await UpdateAndGetAsync(user);
        return result.IdentityResult;
    }

    /// <summary>
    /// Update auth0EventUserAsApplicationUser information by ApplicationUser
    /// The following auth0EventUserAsApplicationUser attributes cannot be updated: family_name, given_name, name, picture.
    /// The connection (sleekflow-connection-google-oauth2) must either be a database connection (using the Auth0 store),
    /// a passwordless connection (email or sms) or has disabled 'Sync auth0EventUserAsApplicationUser profile attributes at each login'.
    /// </summary>
    /// <param name="user"></param>
    /// <returns></returns>
    public async Task<(IdentityResult IdentityResult, List<User> Auth0Users)> UpdateAndGetAsync(ApplicationUser user)
    {
        try
        {
            var dbUser = (await base.FindByIdAsync(user.Id))!;
            var dbUserUpdateResult = await UpdateDbUserAsync(dbUser);
            var updatedAuth0Users = new List<User>();

            if (dbUserUpdateResult.Succeeded)
            {
                var auth0Users = await GetAuth0Users(dbUser);
                foreach (var auth0User in auth0Users)
                {
                    var userUpdateRequest = await ToUserUpdateRequest(user, auth0User);

                    var updatedAuth0User = await _managementApiClient.Users.UpdateAsync(
                        auth0User.UserId,
                        userUpdateRequest);

                    updatedAuth0Users.Add(updatedAuth0User);

                    await Task.Delay(1000);
                }
            }

            return (dbUserUpdateResult, updatedAuth0Users);
        }
        catch (Exception exception)
        {
            throw new Exception(exception.Message, exception.InnerException);
        }
    }

    /// <summary>
    /// Update auth0EventUserAsApplicationUser information.
    /// </summary>
    /// <param name="user"></param>
    /// <returns></returns>
    /// <exception cref="Exception"></exception>
    internal async Task<IdentityResult> UpdateDbUserAsync(ApplicationUser user)
    {
        var userId = await base.GetUserIdAsync(user);
        var updateUser = await base.FindByIdAsync(userId);

        if (updateUser == null)
        {
            throw new Exception("User not found. ");
        }

        updateUser.FirstName = user?.FirstName ?? updateUser.FirstName;
        updateUser.LastName = user?.LastName ?? updateUser.LastName;
        updateUser.DisplayName = user?.DisplayName ?? updateUser.DisplayName;
        updateUser.EmailConfirmed = user?.EmailConfirmed ?? updateUser.EmailConfirmed;
        updateUser.PhoneNumber = user?.PhoneNumber ?? updateUser.PhoneNumber;
        updateUser.PictureUrl = user?.PictureUrl ?? updateUser.PictureUrl;
        updateUser.CompanyId = user?.CompanyId ?? updateUser.CompanyId;
        updateUser.IsAgreeMarketingConsent = user?.IsAgreeMarketingConsent ?? updateUser.IsAgreeMarketingConsent;
        updateUser.LastLoginAt = user?.LastLoginAt ?? updateUser.LastLoginAt;
        updateUser.AccessFailedCount = user?.AccessFailedCount ?? updateUser.AccessFailedCount;
        updateUser.InviteToken = user?.InviteToken ?? updateUser.InviteToken;
        updateUser.InviteTokenExpireAt = user?.InviteTokenExpireAt ?? updateUser.InviteTokenExpireAt;

        return await base.UpdateUserAsync(updateUser);
    }

    /// <summary>
    /// Delete auth0EventUserAsApplicationUser.
    /// </summary>
    /// <param name="user"></param>
    /// <returns></returns>
    /// <exception cref="UserNotFoundException"></exception>
    /// <exception cref="Exception"></exception>
    public override async Task<IdentityResult> DeleteAsync(ApplicationUser user)
    {
        return await DeleteAsync(user, false);
    }

    public async Task<IdentityResult> DeleteAsync(ApplicationUser user, bool alsoDeleteInAuth0)
    {
        try
        {
            var dbUser = await base.FindByIdAsync(user.Id);
            var deleteResult = IdentityResult.Success;
            if (dbUser is not null)
            {
                try
                {
                    await base.DeleteAsync(dbUser!);
                }
                catch (Exception e)
                {
                    _logger.LogError("[{DeleteAsyncName}] {EMessage}", nameof(DeleteAsync), e.Message);
                    if (!alsoDeleteInAuth0)
                    {
                        return IdentityResult.Failed(new IdentityError() { Description = e.Message });
                    }
                }
            }

            if (alsoDeleteInAuth0)
            {
                var auth0Users = await GetAuth0Users(dbUser);
                if (auth0Users.Count == 0)
                {
                    _logger.LogCritical($"[{nameof(DeleteAsync)}] Unable to find the Auth0 user to delete");
                    return IdentityResult.Failed(
                        new IdentityError()
                        {
                            Description = "Unable to find the Auth0 user to delete"
                        });
                }

                foreach (var auth0User in auth0Users)
                {
                    // Auth0 management API do not have any response return.
                    await _managementApiClient.Users.DeleteAsync(auth0User.UserId);

                    await Task.Delay(500);
                }
            }

            return deleteResult;
        }
        catch (Exception exception)
        {
            throw new Exception
                ($"[{nameof(DeleteAsync)}] {exception.Message}", exception.InnerException);
        }
    }

    public async Task<IList<Role>> GetAllRolesAsync()
    {
        try
        {
            var roles = await _managementApiClient.Roles.GetAllAsync(new GetRolesRequest());

            return roles.ToList();
        }
        catch (Exception e)
        {
            throw new Exception(e.Message, e.InnerException);
        }
    }

    public async Task SendVerifyEmailAsync(ApplicationUser user)
    {
        var auth0Users = await GetAuth0Users(user);
        if (auth0Users.Count == 0)
        {
            throw new Exception("Unable to find the user in Auth0");
        }

        var auth0User = auth0Users.FirstOrDefault(
            u => u.Identities.Any(i => i.Connection == _auth0Config.DatabaseConnectionName));
        if (auth0User == null)
        {
            throw new Exception($"[{nameof(SendVerifyEmailAsync)}]Cannot verify with social login");
        }

        await SendVerifyEmailAsync(auth0User);
    }

    public async Task<Job?> SendVerifyEmailAsync(User auth0User)
    {
        var identity = auth0User.Identities.FirstOrDefault(u => u.Provider == "auth0")
                       ?? auth0User.Identities[0];

        var cacheKey = $"VerifyEmailSent:{identity.UserId}";
        var verifyEmailJobData = await _cacheManagerService.GetCacheWithConstantKeyAsync(cacheKey);
        Job? job = string.IsNullOrWhiteSpace(verifyEmailJobData)
            ? null
            : JsonConvert.DeserializeObject<Job>(verifyEmailJobData);

        if (job is null)
        {
            var verifyEmailJobRequest = new VerifyEmailJobRequest()
            {
                UserId = auth0User.UserId,
                Identity = new EmailVerificationIdentity()
                {
                    UserId = identity.UserId, Provider = identity.Provider
                }
            };
            job = await _managementApiClient.Jobs.SendVerificationEmailAsync(verifyEmailJobRequest);
            await _cacheManagerService.SaveCacheWithConstantKeyAsync(cacheKey, job, TimeSpan.FromMinutes(3));
        }

        return job;
    }

    /// <summary>
    /// Override auth0EventUserAsApplicationUser manager method.
    /// </summary>
    /// <param name="user"></param>
    /// <returns></returns>
    public override async Task<string> GenerateEmailConfirmationTokenAsync(ApplicationUser user)
    {
        var auth0Users = await GetAllAuth0UsersByEmailAsync(user.Email!);
        foreach (var auth0User in auth0Users)
        {
            if (auth0User.EmailVerified.HasValue && auth0User.EmailVerified.Value)
            {
                // ignore
            }
            else
            {
                var verifyEmailJob = await SendVerifyEmailAsync(auth0User);
            }
        }

        return string.Empty;
    }

    public async Task<IdentityResult> SetEmailConfirmedAsync(ApplicationUser user, bool isEmailVerified)
    {
        var auth0Users = await GetAuth0Users(user);

        foreach (var auth0User in auth0Users)
        {
            if (auth0User.Identities.All(u => u.Connection != _auth0Config.DatabaseConnectionName))
            {
                continue;
            }

            try
            {
                var userUpdateRequest = new UserUpdateRequest()
                {
                    EmailVerified = isEmailVerified
                };

                var dbUser = await _managementApiClient.Users.UpdateAsync(auth0User.UserId, userUpdateRequest);
                if (dbUser.GetType() != typeof(User))
                {
                    return IdentityResult.Failed(ErrorDescriber.DefaultError());
                }

                if (dbUser.GetType() != typeof(User))
                {
                    return IdentityResult.Failed(
                        new IdentityError()
                        {
                            Description =
                                $"[{nameof(SetEmailAsync)}]Errors occur when updating Auth0 email of user {user.Email}."
                        });
                }
            }
            catch (Exception err)
            {
                _logger.LogError("{ErrMessage}\\n{ErrStackTrace}", err.Message, err.StackTrace);
                return IdentityResult.Failed(
                    new IdentityError()
                    {
                        Description = err.Message
                    });
            }
        }

        user.EmailConfirmed = isEmailVerified;
        var identityResult = await base.Store.UpdateAsync(user, base.CancellationToken);
        if (!identityResult.Succeeded)
        {
            throw new Exception(
                $"[{nameof(SetEmailConfirmedAsync)}]Errors occur when updating db auth0EventUserAsApplicationUser ({user.Email}):\n" +
                $"{JsonConvert.SerializeObject(identityResult.Errors)}");
        }

        return IdentityResult.Success;
    }

    /// <summary>
    /// Override the base "SetUserName" in usermanager
    /// Will update the both db and auth0 user as well.
    /// If db (provider = 'auth0') user is not exist in auth0 database and user is in "invite" status,
    /// will create database user automatically.
    /// </summary>
    /// <param name="user"></param>
    /// <param name="newUserName"></param>
    /// <returns></returns>
    /// <exception cref="Exception"></exception>
    public override async Task<IdentityResult> SetUserNameAsync(ApplicationUser user, string? newUserName)
    {
        if (string.IsNullOrEmpty(newUserName))
        {
            throw new Exception("New user name not provided");
        }

        var userId = await base.GetUserIdAsync(user);
        var updateUser = await base.FindByIdAsync(userId);

        if (updateUser == null)
        {
            return IdentityResult.Failed(
                new IdentityError()
                {
                    Code = "OtherError", Description = "[SetUserNameAsync] User not found"
                });
        }

        var auth0Users = await GetAuth0Users(user);

        // Handle the case for no password user
        var allAuth0UsersWithoutPassword = auth0Users?
            .All(u => u.Identities.All(i => i.Provider != "auth0")) ?? true;
        if (allAuth0UsersWithoutPassword && newUserName.StartsWith("invite."))
        {
            try
            {
                var createdAuth0User = await CreateNewAuth0User(ToCreateRequest(user));
                return createdAuth0User.GetType() == typeof(User)
                    ? IdentityResult.Success
                    : IdentityResult.Failed(
                        new IdentityError()
                        {
                            Code = "OtherError", Description = "[SetUserNameAsync] Create user failed"
                        });
            }
            catch (Exception err)
            {
                return IdentityResult.Failed(
                    new IdentityError()
                    {
                        Code = "OtherError", Description = err.Message
                    });
            }
        }

        auth0Users = await GetAuth0Users(user);

        foreach (var auth0User in auth0Users)
        {
            if (auth0User.Identities.All(u => u.Connection != _auth0Config.DatabaseConnectionName))
            {
                continue;
            }

            try
            {
                var userUpdateRequest = new UserUpdateRequest()
                {
                    UserName = newUserName
                };

                var dbUser = await _managementApiClient.Users.UpdateAsync(auth0User.UserId, userUpdateRequest);
                if (dbUser.GetType() != typeof(User))
                {
                    return IdentityResult.Failed(ErrorDescriber.DefaultError());
                }

                var identityResult = await base.SetUserNameAsync(updateUser, newUserName);

                if (identityResult.Succeeded)
                {
                    return dbUser.GetType() == typeof(User)
                        ? IdentityResult.Success
                        : IdentityResult.Failed();
                }

                throw new Exception(
                    $"Errors occur when updating db auth0EventUserAsApplicationUser ({user.UserName}):\n" +
                    $"{JsonConvert.SerializeObject(identityResult.Errors)}");
            }
            catch (Exception err)
            {
                _logger.LogError(
                    err,
                    "{ErrMessage}\\n{ErrStackTrace}",
                    err.Message,
                    err.StackTrace);

                return IdentityResult.Failed(
                    new IdentityError()
                    {
                        Description = err.Message
                    });
            }
        }

        return IdentityResult.Failed(
            new IdentityError()
            {
                Description = $"User {user.UserName} not found in Auth0"
            });
    }

    public override async Task<IdentityResult> SetEmailAsync(ApplicationUser user, string? email)
    {
        if (string.IsNullOrEmpty(email))
        {
            throw new ArgumentNullException($"[SetEmailAsync]Email cannot be null or empty of user {user.UserName}.");
        }

        var auth0Users = await GetAuth0Users(user);

        foreach (var auth0User in auth0Users)
        {
            if (auth0User.Identities.All(u => u.Connection != _auth0Config.DatabaseConnectionName))
            {
                continue;
            }

            try
            {
                var userUpdateRequest = new UserUpdateRequest()
                {
                    Email = email
                };

                var dbUser = await _managementApiClient.Users.UpdateAsync(auth0User.UserId, userUpdateRequest);
                if (dbUser.GetType() != typeof(User))
                {
                    return IdentityResult.Failed(ErrorDescriber.DefaultError());
                }

                if (dbUser.GetType() != typeof(User))
                {
                    return IdentityResult.Failed(
                        new IdentityError()
                        {
                            Description = $"[{nameof(SetEmailAsync)}]Errors occur when updating Auth0 email of user {user.Email}."
                        });
                }
            }
            catch (Exception err)
            {
                _logger.LogError("{ErrMessage}\\n{ErrStackTrace}", err.Message, err.StackTrace);
                return IdentityResult.Failed(
                    new IdentityError()
                    {
                        Description = err.Message
                    });
            }
        }

        var identityResult = await base.SetEmailAsync(user, email);
        if (!identityResult.Succeeded)
        {
            throw new Exception(
                $"[{nameof(SetEmailAsync)}]Errors occur when updating db auth0EventUserAsApplicationUser ({user.Email}):\n" +
                $"{JsonConvert.SerializeObject(identityResult.Errors)}");
        }

        return IdentityResult.Success;
    }

    public async Task<IPagedList<LogEntry>> GetLogsAsync(string auth0UserId, int page = 0, int itemsPerPage=50)
    {
        var pageInfo = new PaginationInfo(page, itemsPerPage, true);
        var logsResponse = await _managementApiClient.Users.GetLogsAsync(
            new GetUserLogsRequest()
            {
                UserId = auth0UserId
            }, pageInfo);

        return logsResponse;
    }

    public async Task<IPagedList<AuthenticationMethod>> GetMfaAsync(string auth0UserId)
    {
        var mfaResponse =
            await _managementApiClient.Users.GetAuthenticationMethodsAsync(auth0UserId);

        return mfaResponse;
    }

    public async Task<bool> ResetMfaAsync(string auth0UserId, string? mfaId)
    {
        try
        {
            await _managementApiClient.Users.DeleteAuthenticationMethodAsync(
                auth0UserId,
                mfaId);

            return true;
        }
        catch (Exception err)
        {
            _logger.LogError(
                "[ResetMfaAsync]Reset Mfa failed with user id: {userId}, mfa id: {mfaId},\n{message}",
                auth0UserId, mfaId, err.Message);

            return false;
        }
    }

    public async Task<bool> SetBlockedStatus(ApplicationUser user, bool isBlocked)
    {
        try
        {
            var auth0Users = await GetAuth0Users(user);

            foreach (var auth0User in auth0Users)
            {
                var request = new UserUpdateRequest()
                {
                    Blocked = isBlocked
                };

                var updateUser = await _managementApiClient.Users.UpdateAsync(auth0User.UserId, request);

                if (updateUser.GetType() != typeof(User))
                {
                    return false;
                }
            }

            return true;
        }
        catch (Exception err)
        {
            _logger.LogError(
                "[{MethodName}]Set blocked status failed of user {UserEmail}({UserId}) to status {Status},\n{Message}",
                nameof(SetBlockedStatus), user.Email, user.Id, isBlocked, err.Message);
            return false;
        }
    }
}