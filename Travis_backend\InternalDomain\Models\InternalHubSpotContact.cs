﻿using System.Collections.Generic;
using System.Runtime.Serialization;
using Indicia.HubSpot.Api.Contacts;
using Travis_backend.Enums;

namespace Travis_backend.InternalDomain.Models
{
    public class InternalHubSpotContact : HubSpotContactObject
    {
        public static class Property
        {
            #region HubSpot Property

            public const string Id = "id";
            public const string Email = "email";
            public const string FirstName = "firstname";
            public const string LastName = "lastname";
            public const string Website = "website";
            public const string Company = "company";
            public const string Phone = "phone";
            public const string Country = "country";
            public const string EmailConfirmed = "hs_content_membership_email_confirmed";

            public const string LeadStatus = "hs_lead_status";

            public const string JobTitle = "jobtitle";
            public const string ContactOwner = "hubspot_owner_id";

            #endregion

            #region Parntership

            public const string PartnerCompanyName = "partner_company_name";
            public const string PartnerName = "partner_name";
            public const string PartnerLocation = "partner_location";
            public const string PartnerProgram = "partner_program";
            public const string PartnershipStatus = "partnership_status";
            public const string PartnerStackLeadKey = "partnerstack_lead_key";
            public const string PartnerStackPartnerKey = "partnerstack_partner_key";
            public const string PartnerStackDealKey = "partnerstack_deal_key";

            #endregion

            #region Sleekflow

            public const string IsSleekflowUser = "is_sleekflow_user";
            public const string IsSleekflowContact = "is_sleekflow_contact";
            public const string IsNewSignupUser = "is_new_signup";
            public const string SleekflowUserRole = "sleekflow_user_role";
            public const string IsSleekflowEmailConfirmed = "is_sleekflow_email_confirmed";

            public const string CompanyPlanUpgradeToPremiumStatus = "company_plan_upgrade_to_premium_status";
            public const string CompanyPlanUpgradeToProStatus = "company_plan_upgrade_to_pro_status";

            #endregion

            public const string LeadType = "lead_type";
            public const string LeadSource = "lead_source";
            public const string LeadFormCompanyName = "lead_form_company_name";
            public const string IsWebsiteENewsSubscriber = "is_website_e_news_subscriber";
            public const string WebsiteLink = "website_link";
            public const string NumberOfEmployees = "numemployees";
            public const string WebsiteENewsSubscriptionSource = "website_e_news_subscription_source";
            public const string EComPlatform = "e_com_platform__demo_";
            public const string MarketingConsent = "marketing_consent";
            public const string LatestLeadEntryPoint = "latest_lead_entry_point";
            public const string PartnershipsOffer = "partnerships_offer";
            public const string Module = "module";
            public const string CustomerKey = "customer_key";

            public static class PartnershipStatusValue
            {
                public const string PendingApproval = "Pending Approval";
                public const string Approved = "Approved";
                public const string Rejected = "Rejected";
            }

            public static class LeadStatusValue
            {
                public const string New = "NEW";
                public const string Open = "OPEN";
                public const string InProgress = "IN_PROGRESS";
                public const string OpenDeal = "OPEN_DEAL";
                public const string Unqualified = "UNQUALIFIED";
                public const string AttemptedToContact = "ATTEMPTED_TO_CONTACT";
                public const string Connected = "CONNECTED";
                public const string BadTiming = "BAD_TIMING";
            }

            public static class LeadSourceValue
            {
                public const string Import = "Import";
                public const string Inbound = "Inbound";
                public const string CustomerReferrals = "Customer Referrals";
                public const string PartnerReferrals = "Partner Referrals";
                public const string Events = "Events";
                public const string OutboundOutreach = "Email Outreach";
            }

            public static class SleekflowUserRoleValue
            {
                public const string NotSet = "";
                public const string Staff = "Staff";
                public const string Admin = "Admin";
                public const string TeamAdmin = "Team Admin";
                public const string Marketing = "Marketing";
                public const string DemoAdmin = "Demo Admin";

                public static string Parse(StaffUserRole role)
                {
                    return role switch
                    {
                        StaffUserRole.Staff => Staff,
                        StaffUserRole.Admin => Admin,
                        StaffUserRole.TeamAdmin => TeamAdmin,
                        StaffUserRole.Marketing => Marketing,
                        StaffUserRole.DemoAdmin => DemoAdmin,
                        _ => NotSet
                    };
                }
            }

            public static class LeadTypeValue
            {
                public const string NotSet = "";
                public const string Owner = "Owner";
                public const string InvitedUser = "Invited User";
            }

            public static class CompanyPlanUpgradeStatus
            {
                public const string No = "No";
                public const string Yes = "Yes";
                public const string CongratsEmailSent = "Congrats Email Sent";
            }

            public static List<string> IdProperties => new ()
            {
                Id, Email,
            };

            public static List<string> AllProperties => new ()
            {
                Id,
                Email,
                FirstName,
                LastName,
                Website,
                Company,
                Phone,
                LeadStatus,
                LeadSource,
                Country,
                JobTitle,
                IsSleekflowUser,
                IsNewSignupUser,
                SleekflowUserRole,
                ContactOwner,
                EmailConfirmed,
                IsSleekflowEmailConfirmed,
                PartnerCompanyName,
                PartnerName,
                PartnerLocation,
                PartnerProgram,
                PartnershipStatus,
                PartnerStackLeadKey,
                PartnerStackPartnerKey,
                PartnerStackDealKey,
                WebsiteLink,
                CustomerKey
            };
        }

        [DataMember(Name = Property.ContactOwner)]
        public string ContactOwner { get; set; }

        [DataMember(Name = Property.LeadSource)]
        public string LeadSource { get; set; }

        [DataMember(Name = Property.LeadStatus)]
        public string LeadStatus { get; set; }

        [DataMember(Name = Property.JobTitle)]
        public string JobTitle { get; set; }

        [DataMember(Name = Property.Country)]
        public string Country { get; set; }

        [DataMember(Name = Property.LeadFormCompanyName)]
        public string LeadFormCompanyName { get; set; }

        [DataMember(Name = Property.IsWebsiteENewsSubscriber)]
        public bool? IsWebsiteENewsSubscriber { get; set; }

        [DataMember(Name = Property.IsSleekflowUser)]
        public bool? IsSleekflowUser { get; set; }

        [DataMember(Name = Property.IsSleekflowContact)]
        public bool? IsSleekflowContact { get; set; }

        [DataMember(Name = Property.IsNewSignupUser)]
        public bool? IsNewSignupUser { get; set; }

        [DataMember(Name = Property.LeadType)]
        public string LeadType { get; set; }

        [DataMember(Name = Property.SleekflowUserRole)]
        public string SleekflowUserRole { get; set; }

        [DataMember(Name = Property.CompanyPlanUpgradeToPremiumStatus)]
        public string IsCompanyPlanUpgradeToPremium { get; set; }

        [DataMember(Name = Property.CompanyPlanUpgradeToProStatus)]
        public string IsCompanyPlanUpgradeToPro { get; set; }

        [DataMember(Name = Property.IsSleekflowEmailConfirmed)]
        public bool? IsSleekflowEmailConfirmed { get; set; }

        [DataMember(Name = Property.PartnerCompanyName)]
        public string PartnerCompanyName { get; set; }

        [DataMember(Name = Property.PartnerName)]
        public string PartnerName { get; set; }

        [DataMember(Name = Property.PartnerLocation)]
        public string PartnerLocation { get; set; }

        [DataMember(Name = Property.PartnerProgram)]
        public string PartnerProgram { get; set; }

        [DataMember(Name = Property.PartnershipStatus)]
        public string PartnershipStatus { get; set; }

        [DataMember(Name = Property.WebsiteLink)]
        public string WebsiteLink { get; set; }

        [DataMember(Name = Property.NumberOfEmployees)]
        public string NumberOfEmployees { get; set; }

        [DataMember(Name = Property.WebsiteENewsSubscriptionSource)]
        public string WebsiteENewsSubscriptionSource { get; set; }

        [DataMember(Name = Property.EComPlatform)]
        public string EComPlatform { get; set; }

        [DataMember(Name = Property.MarketingConsent)]
        public bool? IsAgreeMarketingConsent { get; set; }

        [DataMember(Name = Property.LatestLeadEntryPoint)]
        public string LatestLeadEntryPoint { get; set; }

        [DataMember(Name = Property.Module)]
        public string Module { get; set; }

        [DataMember(Name = Property.PartnershipsOffer)]
        public string PartnershipsOffer { get; set; }

        [DataMember(Name = Property.PartnerStackLeadKey)]
        public string PartnerStackLeadKey { get; set; }

        [DataMember(Name = Property.PartnerStackPartnerKey)]
        public string PartnerStackPartnerKey { get; set; }

        [DataMember(Name = Property.PartnerStackDealKey)]
        public string PartnerStackDealKey { get; set; }

        [DataMember(Name = Property.CustomerKey)]
        public string CustomerKey { get; set; }
    }
}