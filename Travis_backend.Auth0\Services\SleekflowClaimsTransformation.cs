using System.Security.Claims;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Identity;
using Microsoft.Extensions.Options;
using Microsoft.IdentityModel.JsonWebTokens;
using Newtonsoft.Json;
using Travis_backend.AccountAuthenticationDomain.Models;
using Travis_backend.Auth0.Models;
using Travis_backend.Auth0.Configuration;
using Travis_backend.Cache;
using Travis_backend.Configuration;
using Travis_backend.Exceptions;
using Travis_backend.Extensions;

namespace Travis_backend.Auth0.Services;

/// <summary>
///
/// </summary>
public class SleekflowClaimsTransformation : IClaimsTransformation
{
    private readonly IdentityOptions _options;
    private readonly SleekflowUserManager _userManager;
    private readonly ILogger<SleekflowClaimsTransformation> _logger;
    private readonly ICacheManagerService _cacheManagerService;
    private readonly ILockService _lockService;
    private readonly Auth0Config _auth0Config;

    /// <summary>
    ///
    /// </summary>
    /// <param name="options"></param>
    /// <param name="auth0Config"></param>
    /// <param name="userManager"></param>
    /// <param name="logger"></param>
    /// <param name="cacheManagerService"></param>
    /// <param name="lockService"></param>
    public SleekflowClaimsTransformation(
        IOptions<IdentityOptions> options,
        Auth0Config auth0Config,
        UserManager<ApplicationUser> userManager,
        ILogger<SleekflowClaimsTransformation> logger,
        ICacheManagerService cacheManagerService,
        ILockService lockService)
    {
        _options = options?.Value ?? new IdentityOptions();
        _userManager = (SleekflowUserManager) userManager;
        _auth0Config = auth0Config;
        _logger = logger;
        _cacheManagerService = cacheManagerService;
        _lockService = lockService;
    }

    /// <summary>
    ///
    /// </summary>
    /// <param name="principal"></param>
    /// <returns></returns>
    public async Task<ClaimsPrincipal> TransformAsync(ClaimsPrincipal principal)
    {
        try
        {
            ApplicationUser currentUser = await _userManager.GetUserAsync(principal);
            ApplicationUser transformedUser;
            var isLoginAsUser = false;

            // Dive into the user
            var data = _userManager.FindClaimsValue(principal, "login_as_user", true);
            if (data != null && !data.IsNullOrEmpty())
            {
                var loginAsUser = JsonConvert.DeserializeObject<LoginAsUser>(data);
                if (loginAsUser is not null && loginAsUser.UserId != null && loginAsUser.CompanyId != null)
                {
                    if (loginAsUser.ExpireAt > DateTime.UtcNow)
                    {
                        // Make sure the dive user is super-user.
                        var checkRoles = await _userManager.GetRolesAsync(currentUser);
                        if (checkRoles != null && checkRoles.Contains(ApplicationUserRole.InternalCmsSuperUser))
                        {
                            isLoginAsUser = true;
                            ClearAuth0PrincipalClaims(ref principal);

                            transformedUser = await _userManager.FindByIdAsync(loginAsUser.UserId!)
                                              ?? throw new Exception("Unable to find the dived user.");
                        }
                        else
                        {
                            _logger.LogError(
                                "[Dive] - Dive user to {UserId} failed: Permission denied",
                                loginAsUser.UserId!);

                            // Get Real User Instead
                            transformedUser = currentUser;
                        }
                    }
                    else
                    {
                        _logger.LogInformation(
                            "[Dive] - The dive user from {CurrentUserId} to {LoginAsUserId} is expired. Start Clear",
                            currentUser.Id,
                            loginAsUser.UserId!);

                        var id = string.Join(
                            "_",
                            "SleekflowClaimsTransformation",
                            "Dive",
                            currentUser.Id,
                            loginAsUser.UserId!,
                            loginAsUser.ExpireAt!.Value.Ticks.ToString());

                        // If already cancel, just skip to throw error
                        var isLogInAsUserReset = (await _cacheManagerService.GetCacheWithConstantKeyAsync(id)) == "true";
                        if (isLogInAsUserReset)
                        {
                            throw new SleekflowDiveAuthenticationException(
                                $"[Dive] - The dive user from {currentUser.Id} to {loginAsUser.UserId} is expired.");
                        }

                        var lockId = string.Join(
                            "_",
                            "SleekflowClaimsTransformation",
                            "Dive",
                            currentUser.Id,
                            loginAsUser.UserId!,
                            loginAsUser.ExpireAt!.Value.Ticks.ToString(),
                            "Lock");

                        // If not yet cancel, cancel login as user
                        var @lock = await _lockService.WaitUntilLockAcquiredAsync(
                            lockId,
                            TimeSpan.FromSeconds(10));
                        try
                        {
                            isLogInAsUserReset = (await _cacheManagerService.GetCacheWithConstantKeyAsync(id)) == "true";
                            if (!isLogInAsUserReset)
                            {
                                _logger.LogInformation(
                                    "[Dive] - The dive user from {CurrentUserId} to {LoginAsUserId} is expired. Clearing",
                                    currentUser.Id,
                                    loginAsUser.UserId!);

                                await _userManager.CancelLoginAsUser(currentUser);
                                await _cacheManagerService.SaveCacheWithConstantKeyAsync(id, "true", TimeSpan.FromMinutes(1));
                            }
                        }
                        finally
                        {
                            await _lockService.ReleaseLockAsync(@lock);
                        }

                        _logger.LogInformation(
                            "[Dive] - The dive user from {CurrentUserId} to {LoginAsUserId} is expired. Completed Clear",
                            currentUser.Id,
                            loginAsUser.UserId!);

                        throw new SleekflowDiveAuthenticationException(
                            $"[Dive] - The dive user from {currentUser.Id} to {loginAsUser.UserId} is expired.");
                    }
                }
                else
                {
                    transformedUser = currentUser;
                }
            }
            else
            {
                transformedUser = currentUser;
            }

            var userConnection = _userManager.GetAuth0ConnectionFromHttpContext(principal);
            var userConnectionStrategy = _userManager.GetClaimAuth0ConnectionStrategy(principal);

            var claimsIdentity = new ClaimsIdentity();
            claimsIdentity.AddClaim(new Claim($"{_auth0Config.Namespace}name", transformedUser.DisplayName ?? string.Empty));
            claimsIdentity.AddClaim(new Claim(_options.ClaimsIdentity.UserIdClaimType, transformedUser.Id));
            claimsIdentity.AddClaim(new Claim(_options.ClaimsIdentity.UserNameClaimType, transformedUser.UserName!));
            claimsIdentity.AddClaim(new Claim(_options.ClaimsIdentity.EmailClaimType, transformedUser.Email!));

            if (userConnection != null)
            {
                claimsIdentity.AddClaim(new Claim($"{_auth0Config.Namespace}connection", userConnection));
            }

            if (userConnectionStrategy != null)
            {
                claimsIdentity.AddClaim(
                    new Claim($"{_auth0Config.Namespace}connection_strategy", userConnectionStrategy));
            }

            var nameIdentifier = _userManager.FindClaimsValue(principal, ClaimTypes.NameIdentifier);
            if (nameIdentifier != null)
            {
                claimsIdentity.AddClaim(new Claim(JwtRegisteredClaimNames.Sub, nameIdentifier));
                claimsIdentity.AddClaim(new Claim(ClaimTypes.NameIdentifier, transformedUser.Id));
            }

            var iss = _userManager.FindClaimsValue(principal, JwtRegisteredClaimNames.Iss);
            if (iss != null)
            {
                claimsIdentity.AddClaim(new Claim(JwtRegisteredClaimNames.Iss, iss));
            }

            var roles = await _userManager.GetRolesAsync(transformedUser);
            if (roles is not null)
            {
                foreach (var role in roles)
                {
                    claimsIdentity.AddClaim(new Claim(ClaimTypes.Role, role));
                }
            }

            if (isLoginAsUser)
            {
                claimsIdentity.AddClaim(new Claim("IsLoginAsUser", "true"));
            }

            // SignalR
            if (principal.Identity?.AuthenticationType == "AuthenticationTypes.Federation")
            {
                if (nameIdentifier == null)
                {
                    claimsIdentity.AddClaim(new Claim(JwtRegisteredClaimNames.Sub, transformedUser.Id));
                    claimsIdentity.AddClaim(new Claim(ClaimTypes.NameIdentifier, transformedUser.Id));
                }

                if (roles == null)
                {
                    var dbRoles = await _userManager.GetRolesAsync(transformedUser);
                    foreach (var role in dbRoles)
                    {
                        claimsIdentity.AddClaim(new Claim(ClaimTypes.Role, role));
                    }
                }
            }

            if (claimsIdentity.Claims.Any())
            {
                ClearAuth0PrincipalClaims(ref principal);
                principal.AddIdentity(claimsIdentity);
            }

            return principal;
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "Error in transforming claims for user {UserId}",
                principal.Identity?.Name);

            throw;
        }
    }

    private void ClearAuth0PrincipalClaims(ref ClaimsPrincipal principal)
    {
        var removeItems = principal.Claims.Where(c => c.Type.StartsWith(_auth0Config.Namespace)).ToList();
        var nameIdentifier = principal.Claims.Where(c => c.Type == ClaimTypes.NameIdentifier).ToList();
        var principalIdentity = principal.Identity as ClaimsIdentity;
        foreach (var item in removeItems)
        {
            principalIdentity?.RemoveClaim(item);
        }

        foreach (var idItem in nameIdentifier)
        {
            principalIdentity?.RemoveClaim(idItem);
        }
    }
}