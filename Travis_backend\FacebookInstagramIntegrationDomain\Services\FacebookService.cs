using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using System.Web;
using AutoMapper;
using Hangfire;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Travis_backend.AutomationDomain.Models;
using Travis_backend.Cache;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.CompanyDomain.ViewModels;
using Travis_backend.Constants;
using Travis_backend.ContactDomain.Models;
using Travis_backend.ContactDomain.Services;
using Travis_backend.ConversationDomain.ViewModels;
using Travis_backend.ConversationServices.Models;
using Travis_backend.ConversationServices.ViewModels;
using Travis_backend.Database;
using Travis_backend.Enums;
using Travis_backend.Extensions;
using Travis_backend.MessageDomain.Models;
using Travis_backend.MessageDomain.ViewModels;
using Travis_backend.FlowHubs;
using Travis_backend.FlowHubs.Models;
using Travis_backend.OpenTelemetry.Meters;
using Travis_backend.OpenTelemetry.Meters.Constants;
using AutomationAction = Travis_backend.AutomationDomain.Models.AutomationAction;
using PlatformType = Travis_backend.AutomationDomain.Models.PlatformType;

namespace Travis_backend.ConversationServices;

public interface IFacebookService
{
    Task<ResponseWrapper> AddOrUpdateFbIgCommentRule(
        AssignmentRuleViewModel assignmentRuleViewModel,
        Staff companyUser);

    Task<List<FbIgPostVM>> GetCompanyFacebookPosts(
        string pageId,
        string access_token,
        DateTime? fromDateTime = null,
        DateTime? toDateTime = null,
        int offset = 0,
        int limit = 100,
        string searchKeyword = null);

    Task<FbIgPostVM> GetaFacebookPostDetails(string postId, string access_token);
    Task<UserProfile> CreateUserProfile(string senderId, string pageId, string name = null);

    Task AddUserReplyDmHistory(FBMessaging messaging);

    Task<string> GetProfilePictureLink(string pageId, string accessToken);

    Task CheckAccessTokenValidity(FacebookConfig fbConfig = null, InstagramConfig igConfig = null);

    Task HandleFbAutoAction(AutomationAction automationAction, Entry entry, Change change, bool isNewContact);

    Task HandleFbIcebreakerDm(long automationActionId, FBMessaging messaging, bool isNewContact);

    Task PreviewDmAutomation(long automationActionId, FBMessaging messaging, bool isNewContact);

    Task<PrivateReplyResponse> PrivateReplyForComment(PmBodyData bodyData, string access_token);

    Task<PrivateReplyResponse> CarouselReplyForComment(CarouselBodyData carouselBodyData, string accessToken);

    Task<PrivateReplyResponse> QuickReplyButtonForComment(
        QuickReplyButtonBodyData quickReplyButtonBodyData,
        string accessToken);

    Task<PrivateReplyResponse> ButtonTemplateForComment(
        ButtonTemplateBodyData buttonTemplateBodyData,
        string accessToken);

    Task AddFbUserOneTimeToken(FBMessaging fbMessaging);

    Task<List<FacebookOTNTopicTokenResponse>> GetFacebookOTNTopicTokens(string pageId, string facebookId);

    Task<FacebookAppAccessToken> GetFacebookAccessTokenByAuthorizationCodeAsync(string code);

    Task<AccountInfo> GetFacebookPagesUserHasRoleOnAsync(string accessToken);

    Task<string> GetBusinessIdAssociatedWithPageAsync(string pageId, string businessIntegrationSystemUserAccessToken);
}

public class FacebookService : IFacebookService
{
    private readonly ILogger<FacebookService> _logger;
    private readonly ApplicationDbContext _appDbContext;
    private readonly IConfiguration _configuration;
    private readonly IAzureBlobStorageService _azureBlobStorageService;
    private readonly IUserProfileService _userProfileService;
    private readonly IMapper _mapper;
    private readonly ICompanyInfoCacheService _companyInfoCacheService;
    private readonly IConversationMessageService _conversationMessageService;
    private readonly IUserProfileHooks _userProfileHooks;
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly IMetaChannelConnectionMeters _metaChannelConnectionMeters;

    public FacebookService(
        ILogger<FacebookService> logger,
        ApplicationDbContext appDbContext,
        IConfiguration configuration,
        IAzureBlobStorageService azureBlobStorageService,
        IUserProfileService userProfileService,
        IMapper mapper,
        ICompanyInfoCacheService companyInfoCacheService,
        IConversationMessageService conversationMessageService,
        IUserProfileHooks userProfileHooks,
        IHttpClientFactory httpClientFactory,
        IMetaChannelConnectionMeters metaChannelConnectionMeters)
    {
        _logger = logger;
        _appDbContext = appDbContext;
        _configuration = configuration;
        _azureBlobStorageService = azureBlobStorageService;
        _userProfileService = userProfileService;
        _mapper = mapper;
        _companyInfoCacheService = companyInfoCacheService;
        _conversationMessageService = conversationMessageService;
        _userProfileHooks = userProfileHooks;
        _httpClientFactory = httpClientFactory;
        _metaChannelConnectionMeters = metaChannelConnectionMeters;
    }

    public async Task<ResponseWrapper> AddOrUpdateFbIgCommentRule(
        AssignmentRuleViewModel assignmentRuleViewModel,
        Staff companyUser)
    {
        var responseWrapper = new ResponseWrapper()
        {
            IsSuccess = false
        };

        if (!(assignmentRuleViewModel.Conditions.Any(
                x => x.FieldName.ToLower() == "pageid" && x.Values != null && x.Values.Any() &&
                     x.ConditionOperator == SupportedOperator.Equals) || (!assignmentRuleViewModel.Conditions.Any(
                x => x.FieldName.ToLower() == "postid" && x.Values != null && x.Values.Any() &&
                     x.ConditionOperator == SupportedOperator.Equals) && !assignmentRuleViewModel.IsPreview)))
        {
            responseWrapper.ErrorMsg =
                "Page Id and Post Id cannot be empty in conditions Or Preview Mode should be on if omitting postid";

            return responseWrapper;
        }

        var pageId = assignmentRuleViewModel.Conditions.Where(x => x.FieldName.ToLower() == "pageid")
            .Select(x => x.Values.FirstOrDefault()).FirstOrDefault();
        string postId = null;

        if (assignmentRuleViewModel.Conditions.Any(x => x.FieldName.ToLower() == "postid"))
        {
            postId = assignmentRuleViewModel.Conditions.Where(x => x.FieldName.ToLower() == "postid")
                .Select(x => x.Values.FirstOrDefault()).FirstOrDefault();
        }

        try
        {
            if (assignmentRuleViewModel.AssignmentId == null)
            {
                var assignmentRule = _mapper.Map<AssignmentRule>(assignmentRuleViewModel);

                if (assignmentRuleViewModel.IsPreview)
                {
                    var previewCodeAssignmentRuleList = await _appDbContext.CompanyAssignmentRules
                        .Include(x => x.AutomationActions).ThenInclude(x => x.FbIgAutoReply).Where(
                            x => (x.AutomationType == AutomationType.FacebookPostComment ||
                                  x.AutomationType == AutomationType.InstagramMediaComment) && x.IsPreview)
                        .ToListAsync();
                    var previewCodes = previewCodeAssignmentRuleList.Where(x => x.PreviewCode != null)
                        .Select(x => x.PreviewCode).ToList();
                    var retryLimit = 500;
                    var retry = 0;

                    while (retry < retryLimit)
                    {
                        Random rnd = new Random();
                        var previewCode = rnd.Next(1, 9).ToString() + rnd.Next(0, 9).ToString() +
                                          rnd.Next(0, 9).ToString();
                        assignmentRule.PreviewCode = previewCode;

                        if (!previewCodes.Contains(previewCode))
                        {
                            break;
                        }

                        retry++;
                    }
                }
                else
                {
                    assignmentRule.PreviewCode = null;
                }

                if (assignmentRuleViewModel.AutomationActions != null &&
                    assignmentRuleViewModel.AutomationActions.Any())
                {
                    assignmentRule.AutomationActions = new List<AutomationAction>();

                    foreach (var automationVM in assignmentRuleViewModel.AutomationActions)
                    {
                        var automation = new AutomationAction()
                        {
                            CompanyId = companyUser.CompanyId,
                        };
                        if (automationVM.AutomatedTriggerType == AutomatedTriggerType.FacebookInitiateDm ||
                            automationVM.AutomatedTriggerType == AutomatedTriggerType.FacebookReplyComment ||
                            automationVM.AutomatedTriggerType == AutomatedTriggerType.InstagramInitiateDm ||
                            automationVM.AutomatedTriggerType == AutomatedTriggerType.InstagramReplyComment)
                        {
                            automation.AutomatedTriggerType = automationVM.AutomatedTriggerType;
                            automation.Order = automationVM.Order;
                            automation.ActionWait = automationVM.ActionWait;
                            automation.ActionWaitDays = automationVM.ActionWaitDays;

                            if (automation.FbIgAutoReply == null)
                            {
                                automation.FbIgAutoReply = new FbIgAutoReply()
                                {
                                    MessageContent = automationVM.FbIgAutoReply.MessageContent,
                                    AutoAction =
                                        (AutoAction) Enum.Parse(
                                            typeof(AutoAction),
                                            automationVM.AutomatedTriggerType.ToString(),
                                            true),
                                    MessageAttachment = automationVM.FbIgAutoReply.MessageAttachment,
                                    MessageFormat = automationVM.FbIgAutoReply.MessageFormat,
                                    QuickReplyButtons = automationVM.FbIgAutoReply.QuickReplyButtons,
                                    PageId = pageId,
                                    PostId = postId,
                                    PlatformType =
                                        (automation.AutomatedTriggerType == AutomatedTriggerType.FacebookInitiateDm ||
                                         automation.AutomatedTriggerType == AutomatedTriggerType.FacebookReplyComment)
                                            ? PlatformType.Facebook
                                            : PlatformType.Instagram
                                };

                                if (automation.FbIgAutoReply.PlatformType == PlatformType.Facebook &&
                                    automationVM.AutomatedTriggerType == AutomatedTriggerType.FacebookReplyComment)
                                {
                                    if (automationVM.FbIgAutoReply.LikeComment.HasValue &&
                                        automationVM.FbIgAutoReply.LikeComment.Value == true)
                                    {
                                        automation.FbIgAutoReply.LikeComment = true;
                                    }
                                    else
                                    {
                                        automation.FbIgAutoReply.LikeComment = false;
                                    }
                                }
                            }
                            else
                            {
                                automation.FbIgAutoReply.AutoAction = (AutoAction) Enum.Parse(
                                    typeof(AutoAction),
                                    automationVM.AutomatedTriggerType.ToString(),
                                    true);
                                automation.FbIgAutoReply.MessageContent = automationVM.FbIgAutoReply.MessageContent;
                                automation.FbIgAutoReply.MessageAttachment =
                                    automationVM.FbIgAutoReply.MessageAttachment;
                                automation.FbIgAutoReply.MessageFormat = automationVM.FbIgAutoReply.MessageFormat;
                                automation.FbIgAutoReply.PageId = pageId;
                                automation.FbIgAutoReply.QuickReplyButtons =
                                    automationVM.FbIgAutoReply.QuickReplyButtons;
                                automation.FbIgAutoReply.PostId = postId;
                                automation.FbIgAutoReply.UpdatedAt = DateTime.UtcNow;
                                automation.FbIgAutoReply.PlatformType =
                                    (automationVM.AutomatedTriggerType == AutomatedTriggerType.FacebookInitiateDm ||
                                     automationVM.AutomatedTriggerType == AutomatedTriggerType.FacebookReplyComment)
                                        ? PlatformType.Facebook
                                        : PlatformType.Instagram;

                                if (automation.FbIgAutoReply != null &&
                                    automation.FbIgAutoReply.PlatformType == PlatformType.Facebook &&
                                    automationVM.AutomatedTriggerType == AutomatedTriggerType.FacebookReplyComment)
                                {
                                    if (automationVM.FbIgAutoReply.LikeComment.HasValue &&
                                        automationVM.FbIgAutoReply.LikeComment.Value == true)
                                    {
                                        automation.FbIgAutoReply.LikeComment = true;
                                    }
                                    else
                                    {
                                        automation.FbIgAutoReply.LikeComment = false;
                                    }
                                }
                            }
                        }
                        else
                        {
                            automation.AssignmentType = automationVM.AssignmentType;
                            automation.AutomatedTriggerType = automationVM.AutomatedTriggerType;
                            automation.MessageContent = automationVM.MessageContent;
                            automation.MessageParams = automationVM.MessageParams;
                            automation.ActionAddConversationHashtags = automationVM.ActionAddConversationHashtags;
                            automation.ActionAddConversationRemarks = automationVM.ActionAddConversationRemarks;
                            automation.ActionUpdateCustomFields = automationVM.ActionUpdateCustomFields;
                            automation.ActionAddedToGroupIds = automationVM.ActionAddedToGroupIds;
                            automation.ActionRemoveFromGroupIds = automationVM.ActionRemoveFromGroupIds;
                            automation.ActionWait = automationVM.ActionWait;
                            automation.ActionWaitDays = automationVM.ActionWaitDays;
                            automation.Order = automationVM.Order;
                            automation.TeamAssignmentType = automationVM.TeamAssignmentType;
                            automation.ChangeConversationStatus = automationVM.ChangeConversationStatus;
                            automation.TargetedChannelWithIds = automationVM.TargetedChannelWithIds;
                            automation.AddAdditionalAssigneeIds = automationVM.AddAdditionalAssigneeIds;
                            automation.WebhookURL = automationVM.WebhookURL;

                            if (!string.IsNullOrEmpty(automationVM.StaffId))
                            {
                                automation.AssignedStaff = await _appDbContext.UserRoleStaffs
                                    .Where(
                                        x => x.IdentityId == automationVM.StaffId &&
                                             x.CompanyId == companyUser.CompanyId).Include(x => x.Identity)
                                    .FirstOrDefaultAsync();
                            }
                            else
                            {
                                automation.AssignedStaffId = null;
                            }

                            if (automationVM.TeamId.HasValue)
                            {
                                automation.AssignedTeam = await _appDbContext.CompanyStaffTeams
                                    .Where(
                                        x => x.Id == automationVM.TeamId.Value && x.CompanyId == companyUser.CompanyId)
                                    .FirstOrDefaultAsync();
                            }
                            else
                            {
                                automation.AssignedTeamId = null;
                            }
                        }

                        assignmentRule.AutomationActions.Add(automation);
                    }
                }

                assignmentRule.CompanyId = companyUser.CompanyId;
                assignmentRule.SavedById = companyUser.Id;
                await _appDbContext.CompanyAssignmentRules.AddAsync(assignmentRule);
                await _appDbContext.SaveChangesAsync();
                responseWrapper.IsSuccess = true;
                responseWrapper.Data = assignmentRule;
            }
            else
            {
                var assignmentRule =
                    await _appDbContext.CompanyAssignmentRules.Include(x => x.AutomationActions)
                        .ThenInclude(x => x.FbIgAutoReply).FirstOrDefaultAsync(
                            x => x.AssignmentId == assignmentRuleViewModel.AssignmentId);

                if (assignmentRule == null)
                {
                    responseWrapper.ErrorMsg = "Assignment Rule Not Found";
                    return responseWrapper;
                }

                if (assignmentRuleViewModel.IsPreview)
                {
                    var previewCodeAssignmentRuleList = await _appDbContext.CompanyAssignmentRules
                        .Where(x => x.AutomationType == AutomationType.FacebookPostComment && x.IsPreview)
                        .ToListAsync();
                    var previewCodes = previewCodeAssignmentRuleList.Where(x => x.PreviewCode != null)
                        .Select(x => x.PreviewCode).ToList();
                    var retryLimit = 500;
                    var retry = 0;
                    while (retry < retryLimit)
                    {
                        Random rnd = new Random();
                        var previewCode = rnd.Next(1, 9).ToString() + rnd.Next(0, 9).ToString() +
                                          rnd.Next(0, 9).ToString();
                        assignmentRule.PreviewCode = previewCode;
                        if (!previewCodes.Contains(previewCode))
                        {
                            break;
                        }

                        retry++;
                    }
                }
                else
                {
                    assignmentRule.IsPreview = false;
                    assignmentRule.PreviewCode = null;
                }

                assignmentRule.AssignmentRuleName = assignmentRuleViewModel.AssignmentRuleName;
                assignmentRule.CompanyId = companyUser.CompanyId;
                assignmentRule.AutomationType = assignmentRuleViewModel.AutomationType;
                assignmentRule.Status = assignmentRuleViewModel.Status;
                assignmentRule.SavedById = companyUser.Id;
                assignmentRule.Conditions = assignmentRuleViewModel.Conditions;
                assignmentRule.UpdatedAt = DateTime.UtcNow;
                await _appDbContext.SaveChangesAsync();

                if (assignmentRuleViewModel.AutomationActions != null)
                {
                    var removeList = new List<AutomationAction>();
                    foreach (var automation in assignmentRule.AutomationActions)
                    {
                        var automationVM = assignmentRuleViewModel.AutomationActions.Where(x => x.Id == automation.Id)
                            .FirstOrDefault();
                        if (automationVM == null)
                        {
                            removeList.Add(automation);
                        }
                        else
                        {
                            if (automationVM.AutomatedTriggerType == AutomatedTriggerType.FacebookInitiateDm ||
                                automationVM.AutomatedTriggerType == AutomatedTriggerType.FacebookReplyComment ||
                                automationVM.AutomatedTriggerType == AutomatedTriggerType.InstagramInitiateDm ||
                                automationVM.AutomatedTriggerType == AutomatedTriggerType.InstagramReplyComment)
                            {
                                automation.CompanyId = companyUser.CompanyId;
                                automation.AutomatedTriggerType = automationVM.AutomatedTriggerType;
                                automation.Order = automationVM.Order;
                                automation.ActionWait = automationVM.ActionWait;
                                automation.ActionWaitDays = automationVM.ActionWaitDays;

                                if (automation.FbIgAutoReply == null)
                                {
                                    automation.FbIgAutoReply = new FbIgAutoReply()
                                    {
                                        MessageContent = automationVM.FbIgAutoReply.MessageContent,
                                        AutoAction =
                                            (AutoAction) Enum.Parse(
                                                typeof(AutoAction),
                                                automationVM.AutomatedTriggerType.ToString(),
                                                true),
                                        MessageAttachment = automationVM.FbIgAutoReply.MessageAttachment,
                                        MessageFormat = automationVM.FbIgAutoReply.MessageFormat,
                                        QuickReplyButtons = automationVM.FbIgAutoReply.QuickReplyButtons,
                                        PageId = pageId,
                                        PostId = postId,
                                        PlatformType =
                                            (automationVM.AutomatedTriggerType ==
                                             AutomatedTriggerType.FacebookInitiateDm ||
                                             automationVM.AutomatedTriggerType ==
                                             AutomatedTriggerType.FacebookReplyComment)
                                                ? PlatformType.Facebook
                                                : PlatformType.Instagram
                                    };

                                    if (automation.FbIgAutoReply != null &&
                                        automation.FbIgAutoReply.PlatformType == PlatformType.Facebook &&
                                        automationVM.AutomatedTriggerType == AutomatedTriggerType.FacebookReplyComment)
                                    {
                                        if (automationVM.FbIgAutoReply.LikeComment.HasValue &&
                                            automationVM.FbIgAutoReply.LikeComment.Value == true)
                                        {
                                            automation.FbIgAutoReply.LikeComment = true;
                                        }
                                        else
                                        {
                                            automation.FbIgAutoReply.LikeComment = false;
                                        }
                                    }
                                }
                                else
                                {
                                    automation.FbIgAutoReply.AutoAction = (AutoAction) Enum.Parse(
                                        typeof(AutoAction),
                                        automationVM.AutomatedTriggerType.ToString(),
                                        true);
                                    automation.FbIgAutoReply.MessageContent = automationVM.FbIgAutoReply.MessageContent;
                                    automation.FbIgAutoReply.MessageAttachment =
                                        automationVM.FbIgAutoReply.MessageAttachment;
                                    automation.FbIgAutoReply.MessageFormat = automationVM.FbIgAutoReply.MessageFormat;
                                    automation.FbIgAutoReply.PageId = pageId;
                                    automation.FbIgAutoReply.QuickReplyButtons =
                                        automationVM.FbIgAutoReply.QuickReplyButtons;
                                    automation.FbIgAutoReply.PostId = postId;
                                    automation.FbIgAutoReply.UpdatedAt = DateTime.UtcNow;
                                    automation.FbIgAutoReply.PlatformType =
                                        (automationVM.AutomatedTriggerType == AutomatedTriggerType.FacebookInitiateDm ||
                                         automationVM.AutomatedTriggerType == AutomatedTriggerType.FacebookReplyComment)
                                            ? PlatformType.Facebook
                                            : PlatformType.Instagram;
                                }

                                if (automation.FbIgAutoReply != null &&
                                    automation.FbIgAutoReply.PlatformType == PlatformType.Facebook &&
                                    automationVM.AutomatedTriggerType == AutomatedTriggerType.FacebookReplyComment)
                                {
                                    if (automationVM.FbIgAutoReply.LikeComment.HasValue &&
                                        automationVM.FbIgAutoReply.LikeComment.Value == true)
                                    {
                                        automation.FbIgAutoReply.LikeComment = true;
                                    }
                                    else
                                    {
                                        automation.FbIgAutoReply.LikeComment = false;
                                    }
                                }
                            }
                            else
                            {
                                automation.AssignmentType = automationVM.AssignmentType;
                                automation.AutomatedTriggerType = automationVM.AutomatedTriggerType;
                                automation.MessageContent = automationVM.MessageContent;
                                automation.MessageParams = automationVM.MessageParams;
                                automation.ActionAddConversationHashtags = automationVM.ActionAddConversationHashtags;
                                automation.ActionAddConversationRemarks = automationVM.ActionAddConversationRemarks;
                                automation.ActionUpdateCustomFields = automationVM.ActionUpdateCustomFields;
                                automation.ActionAddedToGroupIds = automationVM.ActionAddedToGroupIds;
                                automation.ActionRemoveFromGroupIds = automationVM.ActionRemoveFromGroupIds;
                                automation.ActionWait = automationVM.ActionWait;
                                automation.ActionWaitDays = automationVM.ActionWaitDays;
                                automation.Order = automationVM.Order;
                                automation.TeamAssignmentType = automationVM.TeamAssignmentType;
                                automation.ChangeConversationStatus = automationVM.ChangeConversationStatus;
                                automation.TargetedChannelWithIds = automationVM.TargetedChannelWithIds;
                                automation.AddAdditionalAssigneeIds = automationVM.AddAdditionalAssigneeIds;
                                automation.WebhookURL = automationVM.WebhookURL;

                                if (!string.IsNullOrEmpty(automationVM.StaffId))
                                {
                                    automation.AssignedStaff = await _appDbContext.UserRoleStaffs
                                        .Where(
                                            x => x.IdentityId == automationVM.StaffId &&
                                                 x.CompanyId == companyUser.CompanyId).Include(x => x.Identity)
                                        .FirstOrDefaultAsync();
                                }
                                else
                                {
                                    automation.AssignedStaffId = null;
                                }

                                if (automationVM.TeamId.HasValue)
                                {
                                    automation.AssignedTeam = await _appDbContext.CompanyStaffTeams
                                        .Where(
                                            x => x.Id == automationVM.TeamId.Value &&
                                                 x.CompanyId == companyUser.CompanyId).FirstOrDefaultAsync();
                                }
                                else
                                {
                                    automation.AssignedTeamId = null;
                                }
                            }
                        }
                    }

                    foreach (var automation in removeList)
                    {
                        assignmentRule.AutomationActions.Remove(automation);
                    }

                    await _appDbContext.SaveChangesAsync();

                    var newAutomationRule = assignmentRuleViewModel.AutomationActions.Where(x => x.Id == null).ToList();
                    foreach (var automation in newAutomationRule)
                    {
                        if (automation.AutomatedTriggerType == AutomatedTriggerType.FacebookInitiateDm ||
                            automation.AutomatedTriggerType == AutomatedTriggerType.FacebookReplyComment ||
                            automation.AutomatedTriggerType == AutomatedTriggerType.InstagramInitiateDm ||
                            automation.AutomatedTriggerType == AutomatedTriggerType.InstagramReplyComment)
                        {
                            var automationAction = new AutomationAction()
                            {
                                CompanyId = companyUser.CompanyId
                            };

                            automationAction.AutomatedTriggerType = automation.AutomatedTriggerType;
                            automationAction.Order = automation.Order;
                            automationAction.ActionWait = automation.ActionWait;
                            automationAction.ActionWaitDays = automation.ActionWaitDays;

                            automationAction.FbIgAutoReply = new FbIgAutoReply();
                            automationAction.FbIgAutoReply.AutoAction = (AutoAction) Enum.Parse(
                                typeof(AutoAction),
                                automation.AutomatedTriggerType.ToString(),
                                true);
                            automationAction.FbIgAutoReply.MessageContent = automation.FbIgAutoReply.MessageContent;
                            automationAction.FbIgAutoReply.PlatformType =
                                (automation.AutomatedTriggerType == AutomatedTriggerType.FacebookInitiateDm ||
                                 automation.AutomatedTriggerType == AutomatedTriggerType.FacebookReplyComment)
                                    ? PlatformType.Facebook
                                    : PlatformType.Instagram;
                            automationAction.FbIgAutoReply.MessageAttachment =
                                automation.FbIgAutoReply.MessageAttachment;
                            automationAction.FbIgAutoReply.MessageFormat = automation.FbIgAutoReply.MessageFormat;
                            automationAction.FbIgAutoReply.PageId = assignmentRuleViewModel.Conditions
                                .FirstOrDefault(x => x.FieldName.ToLower() == "pageid")?.Values.First();
                            automationAction.FbIgAutoReply.QuickReplyButtons =
                                automation.FbIgAutoReply.QuickReplyButtons;
                            automationAction.FbIgAutoReply.PostId = assignmentRuleViewModel.Conditions
                                .FirstOrDefault(x => x.FieldName.ToLower() == "postid")?.Values.First();
                            ;

                            if (automationAction.FbIgAutoReply != null &&
                                automationAction.FbIgAutoReply.PlatformType == PlatformType.Facebook &&
                                automation.AutomatedTriggerType == AutomatedTriggerType.FacebookReplyComment)
                            {
                                if (automation.FbIgAutoReply.LikeComment.HasValue &&
                                    automation.FbIgAutoReply.LikeComment.Value == true)
                                {
                                    automationAction.FbIgAutoReply.LikeComment = true;
                                }
                                else
                                {
                                    automationAction.FbIgAutoReply.LikeComment = false;
                                }
                            }

                            assignmentRule.AutomationActions.Add(automationAction);
                            await _appDbContext.SaveChangesAsync();
                        }
                        else
                        {
                            var automationAction = new AutomationAction
                            {
                                CompanyId = companyUser.CompanyId,
                                AssignmentType = automation.AssignmentType,
                                AutomatedTriggerType = automation.AutomatedTriggerType,
                                MessageContent = automation.MessageContent,
                                MessageParams = automation.MessageParams,
                                ActionAddConversationHashtags = automation.ActionAddConversationHashtags,
                                ActionAddConversationRemarks = automation.ActionAddConversationRemarks,
                                ActionUpdateCustomFields = automation.ActionUpdateCustomFields,
                                ActionAddedToGroupIds = automation.ActionAddedToGroupIds,
                                ActionRemoveFromGroupIds = automation.ActionRemoveFromGroupIds,
                                ActionWait = automation.ActionWait,
                                ActionWaitDays = automation.ActionWaitDays,
                                Order = automation.Order,
                                TeamAssignmentType = automation.TeamAssignmentType,
                                ChangeConversationStatus = automation.ChangeConversationStatus,
                                TargetedChannelWithIds = automation.TargetedChannelWithIds,
                                AddAdditionalAssigneeIds = automation.AddAdditionalAssigneeIds,
                                WebhookURL = automation.WebhookURL
                            };
                            if (!string.IsNullOrEmpty(automation.StaffId))
                            {
                                automationAction.AssignedStaff = await _appDbContext.UserRoleStaffs
                                    .Where(
                                        x => x.IdentityId == automation.StaffId && x.CompanyId == companyUser.CompanyId)
                                    .Include(x => x.Identity).FirstOrDefaultAsync();
                            }
                            else
                            {
                                automationAction.AssignedStaffId = null;
                            }

                            if (automation.TeamId.HasValue)
                            {
                                automationAction.AssignedTeam = await _appDbContext.CompanyStaffTeams
                                    .Where(x => x.Id == automation.TeamId.Value && x.CompanyId == companyUser.CompanyId)
                                    .FirstOrDefaultAsync();
                            }
                            else
                            {
                                automationAction.AssignedTeamId = null;
                            }

                            if (automationAction.FbIgAutoReply != null &&
                                automationAction.FbIgAutoReply.PlatformType == PlatformType.Facebook &&
                                automation.AutomatedTriggerType == AutomatedTriggerType.FacebookReplyComment)
                            {
                                if (automation.FbIgAutoReply.LikeComment.HasValue &&
                                    automation.FbIgAutoReply.LikeComment.Value == true)
                                {
                                    automationAction.FbIgAutoReply.LikeComment = true;
                                }
                                else
                                {
                                    automationAction.FbIgAutoReply.LikeComment = false;
                                }
                            }

                            assignmentRule.AutomationActions.Add(automationAction);
                            await _appDbContext.SaveChangesAsync();
                        }
                    }
                }
                else
                {
                    _appDbContext.CompanyAutomationActions.RemoveRange(assignmentRule.AutomationActions);
                    assignmentRule.AutomationActions = null;
                    await _appDbContext.SaveChangesAsync();
                }

                responseWrapper.IsSuccess = true;
                responseWrapper.Data = assignmentRule;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "[{MethodName}] Company {CompanyId} error, Payload {Payload} error: {ExceptionMessage}",
                nameof(AddOrUpdateFbIgCommentRule),
                companyUser.CompanyId,
                JsonConvert.SerializeObject(assignmentRuleViewModel),
                ex.Message);

            responseWrapper.ErrorMsg = ex.Message;
        }

        return responseWrapper;
    }

    public async Task<List<FbIgPostVM>> GetCompanyFacebookPosts(
        string pageId,
        string access_token,
        DateTime? fromDateTime = null,
        DateTime? toDateTime = null,
        int offset = 0,
        int limit = 100,
        string searchKeyword = null)
    {
        var CompanyFacebookPosts = new List<FbIgPostVM>();

        if (pageId == null || access_token == null)
        {
            return CompanyFacebookPosts;
        }

        var httpClient = _httpClientFactory.CreateClient(HttpClientHandlerName.Default);

        var fields = "id, message, created_time, picture";
        var url = $"https://graph.facebook.com/v12.0/{pageId}/feed";
        url += $"?fields={fields}&access_token={access_token}";
        var doNext = true;
        var iterationUrl = url;
        var iterateLimit = 1000;
        var iteration = 0;

        while (doNext && iteration < iterateLimit)
        {
            doNext = false; // avoid infinite loop

            try
            {
                var httpResponse = await httpClient.GetAsync(iterationUrl);
                iteration++;
                if (httpResponse.IsSuccessStatusCode)
                {
                    string result = await httpResponse.Content.ReadAsStringAsync();
                    FacebookFeed fbResponse = JsonConvert.DeserializeObject<FacebookFeed>(result);

                    if (fbResponse != null && fbResponse.FeedData.Any())
                    {
                        foreach (var data in fbResponse.FeedData)
                        {
                            var isPass = string.IsNullOrEmpty(searchKeyword) || data.Message != null &&
                                data.Message.ToLower().Contains(searchKeyword.ToLower());
                            if (CompanyFacebookPosts.Count < offset + limit && fromDateTime.HasValue &&
                                toDateTime.HasValue)
                            {
                                if (fromDateTime.Value <= data.CreatedAt && data.CreatedAt <= toDateTime && isPass)
                                {
                                    var companyFbPost = new FbIgPostVM()
                                    {
                                        Id = data.Id, Content = data.Message, CreatedAt = data.CreatedAt
                                    };

                                    if (!string.IsNullOrEmpty(data.PictureUrl))
                                    {
                                        companyFbPost.MediaUrl = data.PictureUrl;
                                    }

                                    CompanyFacebookPosts.Add(companyFbPost);
                                }
                            }
                            else if (CompanyFacebookPosts.Count < offset + limit && fromDateTime.HasValue)
                            {
                                if (fromDateTime.Value <= data.CreatedAt && isPass)
                                {
                                    var companyFbPost = new FbIgPostVM()
                                    {
                                        Id = data.Id, Content = data.Message, CreatedAt = data.CreatedAt
                                    };

                                    if (!string.IsNullOrEmpty(data.PictureUrl))
                                    {
                                        companyFbPost.MediaUrl = data.PictureUrl;
                                    }

                                    CompanyFacebookPosts.Add(companyFbPost);
                                }
                            }
                            else if (CompanyFacebookPosts.Count < offset + limit && toDateTime.HasValue)
                            {
                                if (toDateTime.Value >= data.CreatedAt && isPass)
                                {
                                    var companyFbPost = new FbIgPostVM()
                                    {
                                        Id = data.Id, Content = data.Message, CreatedAt = data.CreatedAt
                                    };

                                    if (!string.IsNullOrEmpty(data.PictureUrl))
                                    {
                                        companyFbPost.MediaUrl = data.PictureUrl;
                                    }

                                    CompanyFacebookPosts.Add(companyFbPost);
                                }
                            }
                            else if (isPass && CompanyFacebookPosts.Count < offset + limit)
                            {
                                var companyFbPost = new FbIgPostVM()
                                {
                                    Id = data.Id, Content = data.Message, CreatedAt = data.CreatedAt
                                };

                                if (!string.IsNullOrEmpty(data.PictureUrl))
                                {
                                    companyFbPost.MediaUrl = data.PictureUrl;
                                }

                                CompanyFacebookPosts.Add(companyFbPost);
                            }
                        }

                        if (fbResponse.Paging != null && fbResponse.Paging.FbPostCursors != null &&
                            fbResponse.Paging.FbPostCursors.After != null &&
                            ((CompanyFacebookPosts.Any() && CompanyFacebookPosts.Count < offset + limit) ||
                             (CompanyFacebookPosts.Any() && fromDateTime.HasValue &&
                              fromDateTime.Value < CompanyFacebookPosts.Last().CreatedAt) ||
                             (!CompanyFacebookPosts.Any() && fromDateTime.HasValue &&
                              fromDateTime.Value < fbResponse.FeedData.Last().CreatedAt)))
                        {
                            iterationUrl = url + $"&after={fbResponse.Paging.FbPostCursors.After}";
                            doNext = true;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[{MethodName}] Facebook page {FacebookPageId} error when fetching from {IterationUrl}, iteration count {IterationCount}. {ExceptionMessage}",
                    nameof(GetCompanyFacebookPosts),
                    pageId,
                    iterationUrl,
                    iteration,
                    ex.Message);
            }
        }

        CompanyFacebookPosts = CompanyFacebookPosts.Skip(offset).Take(limit).ToList();

        return CompanyFacebookPosts;
    }

    public async Task<FbIgPostVM> GetaFacebookPostDetails(string postId, string access_token)
    {
        var facebookPostDetails = new FbIgPostVM();

        if (postId == null || access_token == null)
        {
            return facebookPostDetails;
        }

        var httpClient = _httpClientFactory.CreateClient(HttpClientHandlerName.Default);

        var fields = "id, message, created_time, picture";
        var url = $"https://graph.facebook.com/v12.0/{postId}";
        url += $"?fields={fields}&access_token={access_token}";

        var httpResponse = await httpClient.GetAsync(url);

        if (httpResponse.IsSuccessStatusCode)
        {
            try
            {
                string result = await httpResponse.Content.ReadAsStringAsync();
                FeedData feedData = JsonConvert.DeserializeObject<FeedData>(result);
                facebookPostDetails.Id = feedData.Id;
                facebookPostDetails.Content = feedData.Message;
                facebookPostDetails.CreatedAt = feedData.CreatedAt;

                if (!string.IsNullOrEmpty(feedData.PictureUrl))
                {
                    facebookPostDetails.MediaUrl = feedData.PictureUrl;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[{MethodName}] Error fetching Facebook post id {FacebookPostId}. {ExceptionMessage}",
                    nameof(GetaFacebookPostDetails),
                    postId,
                    ex.Message);
            }
        }

        return facebookPostDetails;
    }

    public async Task HandleFbAutoAction(
        AutomationAction automationAction,
        Entry entry,
        Change change,
        bool isNewContact)
    {
        var fbIgAutoReply = automationAction.FbIgAutoReply;

        if (automationAction.AutomatedTriggerType == AutomatedTriggerType.FacebookInitiateDm)
        {
            var config = await _appDbContext.ConfigFacebookConfigs.FirstOrDefaultAsync(x => x.PageId == entry.Id);
            if (config == null)
            {
                return;
            }

            if (fbIgAutoReply.MessageFormat == MessageFormat.Text)
            {
                var bodyData = new PmBodyData()
                {
                    Recipient = new FacebookMessageRecipient()
                    {
                        CommentId = change.Value.CommentId
                    },
                    Message = new Message()
                    {
                        Text = fbIgAutoReply.MessageContent
                    }
                };

                var historyRecord = new FbIgAutoReplyHistoryRecord()
                {
                    FbIgAutoReplyId = fbIgAutoReply.Id,
                    PlatformType = PlatformType.Facebook,
                    PostId = change.Value.PostId,
                    PageId = entry.Id,
                    AutoAction = AutoAction.FacebookInitiateDm,
                    FbIgAutoReply = fbIgAutoReply,
                    ParentCommentId = change.Value.CommentId,
                    ParentCommentMessage = change.Value.Message,
                    AccountId = change.Value.From.Id,
                    IsNewContact = isNewContact,
                    ContactReplyMessages = new List<ContactReplyMessage>()
                };

                var privateReplyResponse =
                    await PrivateReplyForComment(bodyData, config.PageAccessToken);

                if (privateReplyResponse.isSuccess)
                {
                    historyRecord.MessageId = privateReplyResponse.messageId;
                }
                else
                {
                    historyRecord.ErrorMessage = privateReplyResponse.errorMessage;
                }

                await _appDbContext.FbIgAutoReplyHistoryRecords.AddAsync(historyRecord);

                await _appDbContext.SaveChangesAsync();
            }
            else if (fbIgAutoReply.MessageFormat == MessageFormat.Attachment)
            {
                var fbIgAutoReplyAttachment = fbIgAutoReply.FbIgAutoReplyFiles[0];
                var companyId = await _appDbContext.ConfigFacebookConfigs
                    .Where(x => x.PageId == entry.Id).Select(x => x.CompanyId).FirstOrDefaultAsync();
                var storageConfig = await _appDbContext.CompanyCompanies
                    .Where(x => x.Id == companyId).Select(x => x.StorageConfig)
                    .FirstOrDefaultAsync();

                if (storageConfig == null)
                {
                    _logger.LogWarning(
                        "[{MethodName}] Facebook page {FacebookPageId}, auto reply format: {MessageFormat}. Storage config not found for companyId {CompanyId}",
                        nameof(HandleFbAutoAction),
                        entry.Id,
                        MessageFormat.Attachment.ToString(),
                        companyId);
                }

                var accessUrl = _azureBlobStorageService.GetAzureBlobSasUriForever(
                    fbIgAutoReply.FbIgAutoReplyFiles[0].Filename,
                    storageConfig.ContainerName,
                    true);

                var fileType = fbIgAutoReplyAttachment.MIMEType;

                if (fileType.Contains("image"))
                {
                    fileType = "image";
                }
                else if (fileType.Contains("video"))
                {
                    fileType = "video";
                }
                else if (fileType.Contains("audio"))
                {
                    fileType = "audio";
                }
                else
                {
                    fileType = "file";
                }

                var bodyData = new PmBodyData()
                {
                    Recipient = new FacebookMessageRecipient()
                    {
                        CommentId = change.Value.CommentId
                    },
                    Message = new Message()
                    {
                        MessageAttachment = new MessageAttachment()
                        {
                            Type = fileType,
                            MessagePayload = new MessagePayload()
                            {
                                Url = accessUrl
                            }
                        }
                    }
                };

                var historyRecord = new FbIgAutoReplyHistoryRecord()
                {
                    FbIgAutoReplyId = fbIgAutoReply.Id,
                    PlatformType = PlatformType.Facebook,
                    PostId = change.Value.PostId,
                    PageId = entry.Id,
                    AutoAction = AutoAction.FacebookInitiateDm,
                    FbIgAutoReply = fbIgAutoReply,
                    ParentCommentId = change.Value.CommentId,
                    ParentCommentMessage = change.Value.Message,
                    AccountId = change.Value.From.Id,
                    IsNewContact = isNewContact,
                    ContactReplyMessages = new List<ContactReplyMessage>()
                };

                var privateReplyResponse =
                    await PrivateReplyForComment(bodyData, config.PageAccessToken);

                if (privateReplyResponse.isSuccess)
                {
                    historyRecord.MessageId = privateReplyResponse.messageId;
                }
                else
                {
                    historyRecord.ErrorMessage = privateReplyResponse.errorMessage;
                }

                await _appDbContext.FbIgAutoReplyHistoryRecords.AddAsync(historyRecord);

                await _appDbContext.SaveChangesAsync();
            }
            else if (fbIgAutoReply.MessageFormat == MessageFormat.Carousel)
            {
                MessageAttachment messageAttachment = fbIgAutoReply.MessageAttachment;

                if (messageAttachment == null)
                {
                    return;
                }

                try
                {
                    var carouselPayload = new CarouselPayload()
                    {
                        TemplateType = messageAttachment.MessagePayload.TemplateType,
                        Elements = messageAttachment.MessagePayload.Elements
                    };

                    var carouselAttachment = new CarouselAttachment()
                    {
                        Type = messageAttachment.Type, CarouselPayload = carouselPayload
                    };

                    var carouselBodyData = new CarouselBodyData()
                    {
                        Recipient = new FacebookMessageRecipient()
                        {
                            CommentId = change.Value.CommentId
                        },
                        CarouselMessage = new CarouselMessage()
                        {
                            CarouselAttachment = carouselAttachment
                        }
                    };

                    var privateReplyResponse =
                        await CarouselReplyForComment(carouselBodyData, config.PageAccessToken);

                    var historyRecord = new FbIgAutoReplyHistoryRecord()
                    {
                        FbIgAutoReplyId = fbIgAutoReply.Id,
                        PlatformType = PlatformType.Facebook,
                        PostId = change.Value.PostId,
                        PageId = entry.Id,
                        AutoAction = AutoAction.FacebookInitiateDm,
                        FbIgAutoReply = fbIgAutoReply,
                        ParentCommentId = change.Value.CommentId,
                        ParentCommentMessage = change.Value.Message,
                        AccountId = change.Value.From.Id,
                        IsNewContact = isNewContact,
                        ContactReplyMessages = new List<ContactReplyMessage>()
                    };

                    if (privateReplyResponse.isSuccess)
                    {
                        historyRecord.MessageId = privateReplyResponse.messageId;
                    }
                    else
                    {
                        historyRecord.ErrorMessage = privateReplyResponse.errorMessage;
                    }

                    await _appDbContext.FbIgAutoReplyHistoryRecords.AddAsync(historyRecord);

                    await _appDbContext.SaveChangesAsync();
                }
                catch (Exception ex)
                {
                    _logger.LogError(
                        ex,
                        "[{MethodName}] Error processing auto reply format: {MessageFormat} for Facebook page {FacebookPageId}. {ExceptionMessage}",
                        nameof(HandleFbAutoAction),
                        MessageFormat.Carousel.ToString(),
                        entry.Id,
                        ex.Message);
                }
            }
            else if (fbIgAutoReply.MessageFormat == MessageFormat.QuickReplyButton)
            {
                List<QuickReplyButton> quickReplyButtons = fbIgAutoReply.QuickReplyButtons;
                if (quickReplyButtons == null)
                {
                    return;
                }

                try
                {
                    var quickReplyButtonBodyData = new QuickReplyButtonBodyData()
                    {
                        Recipient = new FacebookMessageRecipient()
                        {
                            CommentId = change.Value.CommentId
                        },
                        QuickReplyButtonMessage = new QuickReplyButtonMessage()
                        {
                            Text = fbIgAutoReply.MessageContent, QuickReplyButtons = fbIgAutoReply.QuickReplyButtons
                        }
                    };

                    var privateReplyResponse = await QuickReplyButtonForComment(
                        quickReplyButtonBodyData,
                        config.PageAccessToken);
                    var historyRecord = new FbIgAutoReplyHistoryRecord()
                    {
                        FbIgAutoReplyId = fbIgAutoReply.Id,
                        PlatformType = PlatformType.Facebook,
                        PostId = change.Value.PostId,
                        PageId = entry.Id,
                        AutoAction = AutoAction.FacebookInitiateDm,
                        FbIgAutoReply = fbIgAutoReply,
                        ParentCommentId = change.Value.CommentId,
                        ParentCommentMessage = change.Value.Message,
                        AccountId = change.Value.From.Id,
                        IsNewContact = isNewContact,
                        ContactReplyMessages = new List<ContactReplyMessage>()
                    };

                    if (privateReplyResponse.isSuccess)
                    {
                        historyRecord.MessageId = privateReplyResponse.messageId;
                    }
                    else
                    {
                        historyRecord.ErrorMessage = privateReplyResponse.errorMessage;
                    }

                    await _appDbContext.FbIgAutoReplyHistoryRecords.AddAsync(historyRecord);

                    await _appDbContext.SaveChangesAsync();
                }
                catch (Exception ex)
                {
                    _logger.LogError(
                        ex,
                        "[{MethodName}] Error processing auto reply format: {MessageFormat} for Facebook page {FacebookPageId}. {ExceptionMessage}",
                        nameof(HandleFbAutoAction),
                        MessageFormat.QuickReplyButton.ToString(),
                        entry.Id,
                        ex.Message);
                }
            }
            else if (fbIgAutoReply.MessageFormat == MessageFormat.Button)
            {
                MessageAttachment messageAttachment = fbIgAutoReply.MessageAttachment;
                if (messageAttachment == null)
                {
                    return;
                }

                try
                {
                    var buttonTemplateBodyData = new ButtonTemplateBodyData()
                    {
                        Recipient = new FacebookMessageRecipient()
                        {
                            CommentId = change.Value.CommentId
                        },
                        ButtonMessage = new ButtonMessage()
                        {
                            ButtonAttachment = new ButtonAttachment()
                            {
                                Type = messageAttachment.Type,
                                ButtonPayload = new ButtonPayload()
                                {
                                    TemplateType = messageAttachment.MessagePayload
                                        .TemplateType,
                                    Text = messageAttachment.MessagePayload.Text,
                                    Buttons = messageAttachment.MessagePayload.Buttons
                                }
                            }
                        }
                    };

                    var privateReplyResponse = await ButtonTemplateForComment(
                        buttonTemplateBodyData,
                        config.PageAccessToken);

                    var historyRecord = new FbIgAutoReplyHistoryRecord()
                    {
                        FbIgAutoReplyId = fbIgAutoReply.Id,
                        PlatformType = PlatformType.Facebook,
                        PostId = change.Value.PostId,
                        PageId = entry.Id,
                        AutoAction = AutoAction.FacebookInitiateDm,
                        FbIgAutoReply = fbIgAutoReply,
                        ParentCommentId = change.Value.CommentId,
                        ParentCommentMessage = change.Value.Message,
                        AccountId = change.Value.From.Id,
                        IsNewContact = isNewContact,
                        ContactReplyMessages = new List<ContactReplyMessage>()
                    };

                    if (privateReplyResponse.isSuccess)
                    {
                        historyRecord.MessageId = privateReplyResponse.messageId;
                    }
                    else
                    {
                        historyRecord.ErrorMessage = privateReplyResponse.errorMessage;
                    }

                    await _appDbContext.FbIgAutoReplyHistoryRecords.AddAsync(historyRecord);

                    await _appDbContext.SaveChangesAsync();
                }
                catch (Exception ex)
                {
                    _logger.LogError(
                        ex,
                        "[{MethodName}] Error processing auto reply format: {MessageFormat} for Facebook page {FacebookPageId}. {ExceptionMessage}",
                        nameof(HandleFbAutoAction),
                        MessageFormat.Button.ToString(),
                        entry.Id,
                        ex.Message);
                }
            }
        }
        else if (automationAction.AutomatedTriggerType == AutomatedTriggerType.FacebookReplyComment)
        {
            var config = await _appDbContext.ConfigFacebookConfigs.FirstOrDefaultAsync(x => x.PageId == entry.Id);
            if (config == null)
            {
                return;
            }

            FbReplyCommentResponse replyCommentResponse = null;

            if (!string.IsNullOrWhiteSpace(fbIgAutoReply.MessageContent))
            {
                var bodyData = new PmBodyData()
                {
                    Recipient = new FacebookMessageRecipient()
                    {
                        CommentId = change.Value.CommentId
                    },
                    Message = new Message()
                    {
                        Text = fbIgAutoReply.MessageContent
                    }
                };

                replyCommentResponse = await ReplyCommentInPost(
                    change.Value.CommentId,
                    bodyData,
                    config.PageAccessToken);
            }

            if (fbIgAutoReply.LikeComment.GetValueOrDefault(false))
            {
                await LikeComment(change.Value.CommentId, config.PageAccessToken);
            }

            var historyRecord = new FbIgAutoReplyHistoryRecord()
            {
                FbIgAutoReplyId = fbIgAutoReply.Id,
                PlatformType = PlatformType.Facebook,
                PostId = change.Value.PostId,
                PageId = entry.Id,
                AutoAction = AutoAction.FacebookReplyComment,
                FbIgAutoReply = fbIgAutoReply,
                ParentCommentId = change.Value.CommentId,
                ParentCommentMessage = change.Value.Message,
                IsNewContact = isNewContact,
                AccountId = change.Value.From.Id,
                ContactReplyMessages = new List<ContactReplyMessage>()
            };

            if (replyCommentResponse is { isSuccess: true })
            {
                historyRecord.CommentId = replyCommentResponse.CommentId;
            }
            else if (replyCommentResponse is { isSuccess: false })
            {
                historyRecord.ErrorMessage = replyCommentResponse.ErrorMessage;
            }

            await _appDbContext.FbIgAutoReplyHistoryRecords.AddAsync(historyRecord);
            await _appDbContext.SaveChangesAsync();
        }
    }

    public async Task HandleFbIcebreakerDm(long automationActionId, FBMessaging messaging, bool isNewContact)
    {
        var fbUserId = messaging.sender.id;
        var pageId = messaging.recipient.id;

        var automationAction = await _appDbContext.CompanyAutomationActions
            .Include(x => x.FbIgAutoReply).ThenInclude(x => x.FbIgAutoReplyFiles)
            .FirstOrDefaultAsync(x => x.Id == automationActionId);

        if (automationAction.AutomatedTriggerType == AutomatedTriggerType.FacebookInitiateDm)
        {
            var config = await _appDbContext.ConfigFacebookConfigs.FirstOrDefaultAsync(x => x.PageId == pageId);
            if (config == null)
            {
                return;
            }

            var fbIgAutoReply = automationAction.FbIgAutoReply;

            if (fbIgAutoReply.MessageFormat == MessageFormat.Text)
            {
                var bodyData = new PmBodyData()
                {
                    Recipient = new FacebookMessageRecipient()
                    {
                        Id = fbUserId
                    },
                    Message = new Message()
                    {
                        Text = fbIgAutoReply.MessageContent
                    }
                };

                var historyRecord = new FbIgAutoReplyHistoryRecord()
                {
                    FbIgAutoReplyId = fbIgAutoReply.Id,
                    PlatformType = PlatformType.Facebook,
                    PageId = pageId,
                    AutoAction = AutoAction.FacebookInitiateDm,
                    FbIgAutoReply = fbIgAutoReply,
                    AccountId = fbUserId,
                    IsNewContact = isNewContact,
                    ContactReplyMessages = new List<ContactReplyMessage>()
                };

                var privateReplyResponse =
                    await PrivateReplyForComment(bodyData, config.PageAccessToken);

                if (privateReplyResponse.isSuccess)
                {
                    historyRecord.MessageId = privateReplyResponse.messageId;
                }
                else
                {
                    historyRecord.ErrorMessage = privateReplyResponse.errorMessage;
                }

                await _appDbContext.FbIgAutoReplyHistoryRecords.AddAsync(historyRecord);

                await _appDbContext.SaveChangesAsync();
            }
            else if (fbIgAutoReply.MessageFormat == MessageFormat.Attachment)
            {
                var fbIgAutoReplyAttachment = fbIgAutoReply.FbIgAutoReplyFiles[0];
                var companyId = await _appDbContext.ConfigFacebookConfigs
                    .Where(x => x.PageId == pageId).Select(x => x.CompanyId).FirstOrDefaultAsync();
                var storageConfig = await _appDbContext.CompanyCompanies
                    .Where(x => x.Id == companyId).Select(x => x.StorageConfig)
                    .FirstOrDefaultAsync();

                if (storageConfig == null)
                {
                    _logger.LogWarning(
                        "[{MethodName}] Facebook page {FacebookPageId}, auto reply format: {MessageFormat}. Storage config not found for companyId {CompanyId}",
                        nameof(HandleFbIcebreakerDm),
                        pageId,
                        MessageFormat.Attachment.ToString(),
                        companyId);
                }

                var accessUrl = _azureBlobStorageService.GetAzureBlobSasUriForever(
                    fbIgAutoReply.FbIgAutoReplyFiles[0].Filename,
                    storageConfig.ContainerName,
                    true);

                var fileType = fbIgAutoReplyAttachment.MIMEType;

                if (fileType.Contains("image"))
                {
                    fileType = "image";
                }
                else if (fileType.Contains("video"))
                {
                    fileType = "video";
                }
                else if (fileType.Contains("audio"))
                {
                    fileType = "audio";
                }
                else
                {
                    fileType = "file";
                }

                var bodyData = new PmBodyData()
                {
                    Recipient = new FacebookMessageRecipient()
                    {
                        Id = fbUserId
                    },
                    Message = new Message()
                    {
                        MessageAttachment = new MessageAttachment()
                        {
                            Type = fileType,
                            MessagePayload = new MessagePayload()
                            {
                                Url = accessUrl
                            }
                        }
                    }
                };

                var historyRecord = new FbIgAutoReplyHistoryRecord()
                {
                    FbIgAutoReplyId = fbIgAutoReply.Id,
                    PlatformType = PlatformType.Facebook,
                    PageId = pageId,
                    AutoAction = AutoAction.FacebookInitiateDm,
                    FbIgAutoReply = fbIgAutoReply,
                    AccountId = fbUserId,
                    IsNewContact = isNewContact,
                    ContactReplyMessages = new List<ContactReplyMessage>()
                };

                var privateReplyResponse =
                    await PrivateReplyForComment(bodyData, config.PageAccessToken);

                if (privateReplyResponse.isSuccess)
                {
                    historyRecord.MessageId = privateReplyResponse.messageId;
                }
                else
                {
                    historyRecord.ErrorMessage = privateReplyResponse.errorMessage;
                }

                await _appDbContext.FbIgAutoReplyHistoryRecords.AddAsync(historyRecord);

                await _appDbContext.SaveChangesAsync();
            }
            else if (fbIgAutoReply.MessageFormat == MessageFormat.Carousel)
            {
                MessageAttachment messageAttachment = fbIgAutoReply.MessageAttachment;

                if (messageAttachment == null)
                {
                    return;
                }

                try
                {
                    var carouselPayload = new CarouselPayload()
                    {
                        TemplateType = messageAttachment.MessagePayload.TemplateType,
                        Elements = messageAttachment.MessagePayload.Elements
                    };

                    var carouselAttachment = new CarouselAttachment()
                    {
                        Type = messageAttachment.Type, CarouselPayload = carouselPayload
                    };

                    var carouselBodyData = new CarouselBodyData()
                    {
                        Recipient = new FacebookMessageRecipient()
                        {
                            Id = fbUserId
                        },
                        CarouselMessage = new CarouselMessage()
                        {
                            CarouselAttachment = carouselAttachment
                        }
                    };

                    var privateReplyResponse =
                        await CarouselReplyForComment(carouselBodyData, config.PageAccessToken);

                    var historyRecord = new FbIgAutoReplyHistoryRecord()
                    {
                        FbIgAutoReplyId = fbIgAutoReply.Id,
                        PlatformType = PlatformType.Facebook,
                        AutoAction = AutoAction.FacebookInitiateDm,
                        FbIgAutoReply = fbIgAutoReply,
                        AccountId = fbUserId,
                        IsNewContact = isNewContact,
                        ContactReplyMessages = new List<ContactReplyMessage>()
                    };

                    if (privateReplyResponse.isSuccess)
                    {
                        historyRecord.MessageId = privateReplyResponse.messageId;
                    }
                    else
                    {
                        historyRecord.ErrorMessage = privateReplyResponse.errorMessage;
                    }

                    await _appDbContext.FbIgAutoReplyHistoryRecords.AddAsync(historyRecord);

                    await _appDbContext.SaveChangesAsync();
                }
                catch (Exception ex)
                {
                    _logger.LogError(
                        ex,
                        "[{MethodName}] Error processing auto reply format {MessageFormat} for sender {FacebookUserId} Facebook page {FacebookPageId}. {ExceptionMessage}",
                        nameof(HandleFbIcebreakerDm),
                        MessageFormat.Carousel.ToString(),
                        fbUserId,
                        pageId,
                        ex.Message);
                }
            }
            else if (fbIgAutoReply.MessageFormat == MessageFormat.QuickReplyButton)
            {
                List<QuickReplyButton> quickReplyButtons = fbIgAutoReply.QuickReplyButtons;
                if (quickReplyButtons == null)
                {
                    return;
                }

                try
                {
                    var quickReplyButtonBodyData = new QuickReplyButtonBodyData()
                    {
                        Recipient = new FacebookMessageRecipient()
                        {
                            Id = fbUserId
                        },
                        QuickReplyButtonMessage = new QuickReplyButtonMessage()
                        {
                            Text = fbIgAutoReply.MessageContent, QuickReplyButtons = fbIgAutoReply.QuickReplyButtons
                        }
                    };

                    var privateReplyResponse = await QuickReplyButtonForComment(
                        quickReplyButtonBodyData,
                        config.PageAccessToken);
                    var historyRecord = new FbIgAutoReplyHistoryRecord()
                    {
                        FbIgAutoReplyId = fbIgAutoReply.Id,
                        PlatformType = PlatformType.Facebook,
                        AutoAction = AutoAction.FacebookInitiateDm,
                        FbIgAutoReply = fbIgAutoReply,
                        AccountId = fbUserId,
                        IsNewContact = isNewContact,
                        ContactReplyMessages = new List<ContactReplyMessage>()
                    };

                    if (privateReplyResponse.isSuccess)
                    {
                        historyRecord.MessageId = privateReplyResponse.messageId;
                    }
                    else
                    {
                        historyRecord.ErrorMessage = privateReplyResponse.errorMessage;
                    }

                    await _appDbContext.FbIgAutoReplyHistoryRecords.AddAsync(historyRecord);

                    await _appDbContext.SaveChangesAsync();
                }
                catch (Exception ex)
                {
                    _logger.LogError(
                        ex,
                        "[{MethodName}] Error processing auto reply format {MessageFormat} for sender {FacebookUserId} Facebook page {FacebookPageId}. {ExceptionMessage}",
                        nameof(HandleFbIcebreakerDm),
                        MessageFormat.QuickReplyButton.ToString(),
                        fbUserId,
                        pageId,
                        ex.Message);
                }
            }
            else if (fbIgAutoReply.MessageFormat == MessageFormat.Button)
            {
                MessageAttachment messageAttachment = fbIgAutoReply.MessageAttachment;
                if (messageAttachment == null)
                {
                    return;
                }

                try
                {
                    var buttonTemplateBodyData = new ButtonTemplateBodyData()
                    {
                        Recipient = new FacebookMessageRecipient()
                        {
                            Id = fbUserId
                        },
                        ButtonMessage = new ButtonMessage()
                        {
                            ButtonAttachment = new ButtonAttachment()
                            {
                                Type = messageAttachment.Type,
                                ButtonPayload = new ButtonPayload()
                                {
                                    TemplateType = messageAttachment.MessagePayload
                                        .TemplateType,
                                    Text = messageAttachment.MessagePayload.Text,
                                    Buttons = messageAttachment.MessagePayload.Buttons
                                }
                            }
                        }
                    };

                    var privateReplyResponse = await ButtonTemplateForComment(
                        buttonTemplateBodyData,
                        config.PageAccessToken);

                    var historyRecord = new FbIgAutoReplyHistoryRecord()
                    {
                        FbIgAutoReplyId = fbIgAutoReply.Id,
                        PlatformType = PlatformType.Facebook,
                        AutoAction = AutoAction.FacebookInitiateDm,
                        FbIgAutoReply = fbIgAutoReply,
                        AccountId = fbUserId,
                        IsNewContact = isNewContact,
                        ContactReplyMessages = new List<ContactReplyMessage>()
                    };

                    if (privateReplyResponse.isSuccess)
                    {
                        historyRecord.MessageId = privateReplyResponse.messageId;
                    }
                    else
                    {
                        historyRecord.ErrorMessage = privateReplyResponse.errorMessage;
                    }

                    await _appDbContext.FbIgAutoReplyHistoryRecords.AddAsync(historyRecord);

                    await _appDbContext.SaveChangesAsync();
                }
                catch (Exception ex)
                {
                    _logger.LogError(
                        ex,
                        "[{MethodName}] Error processing auto reply format {MessageFormat} for sender {FacebookUserId} Facebook page {FacebookPageId}. {ExceptionMessage}",
                        nameof(HandleFbIcebreakerDm),
                        MessageFormat.Button.ToString(),
                        fbUserId,
                        pageId,
                        ex.Message);
                }
            }
        }
    }

    public async Task PreviewDmAutomation(long automationActionId, FBMessaging messaging, bool isNewContact)
    {
        var pageId = messaging.recipient.id;
        var fbUserId = messaging.sender.id;

        var automationAction = await _appDbContext.CompanyAutomationActions
            .Include(x => x.FbIgAutoReply).ThenInclude(x => x.FbIgAutoReplyFiles)
            .FirstOrDefaultAsync(x => x.Id == automationActionId);

        if (automationAction.AutomatedTriggerType == AutomatedTriggerType.FacebookInitiateDm)
        {
            var config = await _appDbContext.ConfigFacebookConfigs.FirstOrDefaultAsync(x => x.PageId == pageId);
            if (config == null)
            {
                return;
            }

            var fbIgAutoReply = automationAction.FbIgAutoReply;

            if (fbIgAutoReply.MessageFormat == MessageFormat.Text)
            {
                var bodyData = new PmBodyData()
                {
                    Recipient = new FacebookMessageRecipient()
                    {
                        Id = fbUserId
                    },
                    Message = new Message()
                    {
                        Text = fbIgAutoReply.MessageContent
                    }
                };

                var historyRecord = new FbIgAutoReplyHistoryRecord()
                {
                    FbIgAutoReplyId = fbIgAutoReply.Id,
                    PlatformType = PlatformType.Facebook,
                    PageId = pageId,
                    AutoAction = AutoAction.FacebookInitiateDm,
                    FbIgAutoReply = fbIgAutoReply,
                    AccountId = fbUserId,
                    IsNewContact = isNewContact,
                    PreviewMessageId = messaging.message.mid,
                    PreviewCode = messaging.message.text,
                    ContactReplyMessages = new List<ContactReplyMessage>()
                };

                var privateReplyResponse =
                    await PrivateReplyForComment(bodyData, config.PageAccessToken);

                if (privateReplyResponse.isSuccess)
                {
                    historyRecord.MessageId = privateReplyResponse.messageId;
                }
                else
                {
                    historyRecord.ErrorMessage = privateReplyResponse.errorMessage;
                }

                await _appDbContext.FbIgAutoReplyHistoryRecords.AddAsync(historyRecord);

                await _appDbContext.SaveChangesAsync();
            }
            else if (fbIgAutoReply.MessageFormat == MessageFormat.Attachment)
            {
                var fbIgAutoReplyAttachment = fbIgAutoReply.FbIgAutoReplyFiles[0];
                var companyId = await _appDbContext.ConfigFacebookConfigs
                    .Where(x => x.PageId == pageId).Select(x => x.CompanyId).FirstOrDefaultAsync();
                var storageConfig = await _appDbContext.CompanyCompanies
                    .Where(x => x.Id == companyId).Select(x => x.StorageConfig)
                    .FirstOrDefaultAsync();
                if (storageConfig == null)
                {
                    _logger.LogWarning(
                        "[{MethodName}] Facebook page {FacebookPageId}, auto reply format: {MessageFormat}. Storage config not found for companyId {CompanyId}",
                        nameof(PreviewDmAutomation),
                        pageId,
                        MessageFormat.Attachment.ToString(),
                        companyId);
                }

                var accessUrl = _azureBlobStorageService.GetAzureBlobSasUriForever(
                    fbIgAutoReply.FbIgAutoReplyFiles[0].Filename,
                    storageConfig.ContainerName,
                    true);

                var fileType = fbIgAutoReplyAttachment.MIMEType;

                if (fileType.Contains("image"))
                {
                    fileType = "image";
                }
                else if (fileType.Contains("video"))
                {
                    fileType = "video";
                }
                else if (fileType.Contains("audio"))
                {
                    fileType = "audio";
                }
                else
                {
                    fileType = "file";
                }

                var bodyData = new PmBodyData()
                {
                    Recipient = new FacebookMessageRecipient()
                    {
                        Id = fbUserId
                    },
                    Message = new Message()
                    {
                        MessageAttachment = new MessageAttachment()
                        {
                            Type = fileType,
                            MessagePayload = new MessagePayload()
                            {
                                Url = accessUrl
                            }
                        }
                    }
                };

                var historyRecord = new FbIgAutoReplyHistoryRecord()
                {
                    FbIgAutoReplyId = fbIgAutoReply.Id,
                    PlatformType = PlatformType.Facebook,
                    PageId = pageId,
                    AutoAction = AutoAction.FacebookInitiateDm,
                    FbIgAutoReply = fbIgAutoReply,
                    AccountId = fbUserId,
                    IsNewContact = isNewContact,
                    PreviewMessageId = messaging.message.mid,
                    PreviewCode = messaging.message.text,
                    ContactReplyMessages = new List<ContactReplyMessage>()
                };

                var privateReplyResponse =
                    await PrivateReplyForComment(bodyData, config.PageAccessToken);

                if (privateReplyResponse.isSuccess)
                {
                    historyRecord.MessageId = privateReplyResponse.messageId;
                }
                else
                {
                    historyRecord.ErrorMessage = privateReplyResponse.errorMessage;
                }

                await _appDbContext.FbIgAutoReplyHistoryRecords.AddAsync(historyRecord);

                await _appDbContext.SaveChangesAsync();
            }
            else if (fbIgAutoReply.MessageFormat == MessageFormat.Carousel)
            {
                MessageAttachment messageAttachment = fbIgAutoReply.MessageAttachment;

                if (messageAttachment == null)
                {
                    return;
                }

                try
                {
                    var carouselPayload = new CarouselPayload()
                    {
                        TemplateType = messageAttachment.MessagePayload.TemplateType,
                        Elements = messageAttachment.MessagePayload.Elements
                    };

                    var carouselAttachment = new CarouselAttachment()
                    {
                        Type = messageAttachment.Type, CarouselPayload = carouselPayload
                    };

                    var carouselBodyData = new CarouselBodyData()
                    {
                        Recipient = new FacebookMessageRecipient()
                        {
                            Id = fbUserId
                        },
                        CarouselMessage = new CarouselMessage()
                        {
                            CarouselAttachment = carouselAttachment
                        }
                    };

                    var privateReplyResponse =
                        await CarouselReplyForComment(carouselBodyData, config.PageAccessToken);

                    var historyRecord = new FbIgAutoReplyHistoryRecord()
                    {
                        FbIgAutoReplyId = fbIgAutoReply.Id,
                        PlatformType = PlatformType.Facebook,
                        AutoAction = AutoAction.FacebookInitiateDm,
                        FbIgAutoReply = fbIgAutoReply,
                        AccountId = fbUserId,
                        IsNewContact = isNewContact,
                        PreviewMessageId = messaging.message.mid,
                        PreviewCode = messaging.message.text,
                        ContactReplyMessages = new List<ContactReplyMessage>()
                    };

                    if (privateReplyResponse.isSuccess)
                    {
                        historyRecord.MessageId = privateReplyResponse.messageId;
                    }
                    else
                    {
                        historyRecord.ErrorMessage = privateReplyResponse.errorMessage;
                    }

                    await _appDbContext.FbIgAutoReplyHistoryRecords.AddAsync(historyRecord);

                    await _appDbContext.SaveChangesAsync();
                }
                catch (Exception ex)
                {
                    _logger.LogError(
                        ex,
                        "[{MethodName}] Error processing auto reply format {MessageFormat} for sender {FacebookUserId} Facebook page {FacebookPageId}. {ExceptionMessage}",
                        nameof(PreviewDmAutomation),
                        MessageFormat.Carousel.ToString(),
                        fbUserId,
                        pageId,
                        ex.Message);
                }
            }
            else if (fbIgAutoReply.MessageFormat == MessageFormat.QuickReplyButton)
            {
                List<QuickReplyButton> quickReplyButtons = fbIgAutoReply.QuickReplyButtons;
                if (quickReplyButtons == null)
                {
                    return;
                }

                try
                {
                    var quickReplyButtonBodyData = new QuickReplyButtonBodyData()
                    {
                        Recipient = new FacebookMessageRecipient()
                        {
                            Id = fbUserId
                        },
                        QuickReplyButtonMessage = new QuickReplyButtonMessage()
                        {
                            Text = fbIgAutoReply.MessageContent, QuickReplyButtons = fbIgAutoReply.QuickReplyButtons
                        }
                    };

                    var privateReplyResponse = await QuickReplyButtonForComment(
                        quickReplyButtonBodyData,
                        config.PageAccessToken);
                    var historyRecord = new FbIgAutoReplyHistoryRecord()
                    {
                        FbIgAutoReplyId = fbIgAutoReply.Id,
                        PlatformType = PlatformType.Facebook,
                        AutoAction = AutoAction.FacebookInitiateDm,
                        FbIgAutoReply = fbIgAutoReply,
                        AccountId = fbUserId,
                        IsNewContact = isNewContact,
                        PreviewMessageId = messaging.message.mid,
                        PreviewCode = messaging.message.text,
                        ContactReplyMessages = new List<ContactReplyMessage>()
                    };

                    if (privateReplyResponse.isSuccess)
                    {
                        historyRecord.MessageId = privateReplyResponse.messageId;
                    }
                    else
                    {
                        historyRecord.ErrorMessage = privateReplyResponse.errorMessage;
                    }

                    await _appDbContext.FbIgAutoReplyHistoryRecords.AddAsync(historyRecord);

                    await _appDbContext.SaveChangesAsync();
                }
                catch (Exception ex)
                {
                    _logger.LogError(
                        ex,
                        "[{MethodName}] Error processing auto reply format {MessageFormat} for sender {FacebookUserId} Facebook page {FacebookPageId}. {ExceptionMessage}",
                        nameof(PreviewDmAutomation),
                        MessageFormat.QuickReplyButton.ToString(),
                        fbUserId,
                        pageId,
                        ex.Message);
                }
            }
            else if (fbIgAutoReply.MessageFormat == MessageFormat.Button)
            {
                MessageAttachment messageAttachment = fbIgAutoReply.MessageAttachment;
                if (messageAttachment == null)
                {
                    return;
                }

                try
                {
                    var buttonTemplateBodyData = new ButtonTemplateBodyData()
                    {
                        Recipient = new FacebookMessageRecipient()
                        {
                            Id = fbUserId
                        },
                        ButtonMessage = new ButtonMessage()
                        {
                            ButtonAttachment = new ButtonAttachment()
                            {
                                Type = messageAttachment.Type,
                                ButtonPayload = new ButtonPayload()
                                {
                                    TemplateType = messageAttachment.MessagePayload
                                        .TemplateType,
                                    Text = messageAttachment.MessagePayload.Text,
                                    Buttons = messageAttachment.MessagePayload.Buttons
                                }
                            }
                        }
                    };

                    var privateReplyResponse = await ButtonTemplateForComment(
                        buttonTemplateBodyData,
                        config.PageAccessToken);

                    var historyRecord = new FbIgAutoReplyHistoryRecord()
                    {
                        FbIgAutoReplyId = fbIgAutoReply.Id,
                        PlatformType = PlatformType.Facebook,
                        AutoAction = AutoAction.FacebookInitiateDm,
                        FbIgAutoReply = fbIgAutoReply,
                        AccountId = fbUserId,
                        IsNewContact = isNewContact,
                        PreviewMessageId = messaging.message.mid,
                        PreviewCode = messaging.message.text,
                        ContactReplyMessages = new List<ContactReplyMessage>()
                    };

                    if (privateReplyResponse.isSuccess)
                    {
                        historyRecord.MessageId = privateReplyResponse.messageId;
                    }
                    else
                    {
                        historyRecord.ErrorMessage = privateReplyResponse.errorMessage;
                    }

                    await _appDbContext.FbIgAutoReplyHistoryRecords.AddAsync(historyRecord);

                    await _appDbContext.SaveChangesAsync();
                }
                catch (Exception ex)
                {
                    _logger.LogError(
                        ex,
                        "[{MethodName}] Error processing auto reply format {MessageFormat} for sender {FacebookUserId} Facebook page {FacebookPageId}. {ExceptionMessage}",
                        nameof(PreviewDmAutomation),
                        MessageFormat.Button.ToString(),
                        fbUserId,
                        pageId,
                        ex.Message);
                }
            }
        }
    }

    public async Task<FbReplyCommentResponse> ReplyCommentInPost(
        string objectId,
        PmBodyData bodyData,
        string access_token)
    {
        var replyCommentResponse = new FbReplyCommentResponse();
        replyCommentResponse.isSuccess = false;

        var url = string.Empty;
        StringContent content = null;
        if (bodyData.Message.Text != null)
        {
            url =
                $"https://graph.facebook.com/{objectId}/comments?message={HttpUtility.UrlEncode(bodyData.Message.Text)}&access_token={access_token}";
        }
        else
        {
            url = $"https://graph.facebook.com/{objectId}/comments?access_token={access_token}";
            var bodyStr = JsonConvert.SerializeObject(bodyData);
            content = new StringContent(bodyStr, Encoding.UTF8, "application/json");
        }

        try
        {
            var httpClient = _httpClientFactory.CreateClient(HttpClientHandlerName.Default);

            var httpResponse = await httpClient.PostAsync(url, content);

            if (httpResponse.IsSuccessStatusCode)
            {
                string result = await httpResponse.Content.ReadAsStringAsync();
                FbReplyCommentHttpResponse httpResponseContent =
                    JsonConvert.DeserializeObject<FbReplyCommentHttpResponse>(result);
                if (httpResponseContent != null)
                {
                    replyCommentResponse.CommentId = httpResponseContent.Id;
                    replyCommentResponse.isSuccess = true;
                }
            }
            else
            {
                replyCommentResponse.ErrorMessage = httpResponse.Content.ReadAsStringAsync().Result;
            }

            return replyCommentResponse;
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "[{MethodName}] Error replying to comment in post {PostId}. {ExceptionMessage}",
                nameof(ReplyCommentInPost),
                objectId,
                ex.Message);

            replyCommentResponse.ErrorMessage = ex.Message;
        }

        return replyCommentResponse;
    }

    public async Task<PrivateReplyResponse> PrivateReplyForComment(PmBodyData bodyData, string access_token)
    {
        var privateReplyResponse = new PrivateReplyResponse();
        privateReplyResponse.isSuccess = false;
        var url = $"https://graph.facebook.com/v12.0/me/messages?access_token={access_token}";

        var bodyStr = JsonConvert.SerializeObject(bodyData);
        var bodyJson = new StringContent(bodyStr, Encoding.UTF8, "application/json");

        try
        {
            var httpClient = _httpClientFactory.CreateClient(HttpClientHandlerName.Default);

            var httpResponse = await httpClient.PostAsync(url, bodyJson);

            if (httpResponse.IsSuccessStatusCode)
            {
                string result = await httpResponse.Content.ReadAsStringAsync();
                FbMessageHttpResponse fbMessageHttpResponse =
                    JsonConvert.DeserializeObject<FbMessageHttpResponse>(result);
                if (fbMessageHttpResponse != null)
                {
                    privateReplyResponse.recipientId = fbMessageHttpResponse.RecipientId;
                    privateReplyResponse.messageId = fbMessageHttpResponse.MessageId;
                    privateReplyResponse.isSuccess = true;
                }
            }
            else
            {
                privateReplyResponse.errorMessage = httpResponse.Content.ReadAsStringAsync().Result;
            }

            return privateReplyResponse;
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "[{MethodName}] Error replying to Facebook user {FacebookUserId}'s comment id {FacebookCommentId}. {ExceptionMessage}",
                nameof(PrivateReplyForComment),
                bodyData?.Recipient?.Id,
                bodyData?.Recipient?.CommentId,
                ex.Message);

            privateReplyResponse.errorMessage = ex.Message;
        }

        return privateReplyResponse;
    }

    public async Task<PrivateReplyResponse> CarouselReplyForComment(
        CarouselBodyData carouselBodyData,
        string accessToken)
    {
        var privateReplyResponse = new PrivateReplyResponse();
        privateReplyResponse.isSuccess = false;
        var url = $"https://graph.facebook.com/v12.0/me/messages?access_token={accessToken}";

        var bodyStr = JsonConvert.SerializeObject(carouselBodyData);
        var bodyJson = new StringContent(bodyStr, Encoding.UTF8, "application/json");

        try
        {
            var httpClient = _httpClientFactory.CreateClient(HttpClientHandlerName.Default);

            var httpResponse = await httpClient.PostAsync(url, bodyJson);

            if (httpResponse.IsSuccessStatusCode)
            {
                string result = await httpResponse.Content.ReadAsStringAsync();
                FbMessageHttpResponse fbMessageHttpResponse =
                    JsonConvert.DeserializeObject<FbMessageHttpResponse>(result);
                if (fbMessageHttpResponse != null)
                {
                    privateReplyResponse.recipientId = fbMessageHttpResponse.RecipientId;
                    privateReplyResponse.messageId = fbMessageHttpResponse.MessageId;
                    privateReplyResponse.isSuccess = true;
                }
            }
            else
            {
                privateReplyResponse.errorMessage = httpResponse.Content.ReadAsStringAsync().Result;
            }

            return privateReplyResponse;
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "[{MethodName}] Error replying to Facebook user {FacebookUserId}'s comment id {FacebookCommentId}. {ExceptionMessage}",
                nameof(CarouselReplyForComment),
                carouselBodyData?.Recipient?.Id,
                carouselBodyData?.Recipient?.CommentId,
                ex.Message);

            privateReplyResponse.errorMessage = ex.Message;
        }

        return privateReplyResponse;
    }

    public async Task<PrivateReplyResponse> QuickReplyButtonForComment(
        QuickReplyButtonBodyData quickReplyButtonBodyData,
        string accessToken)
    {
        var privateReplyResponse = new PrivateReplyResponse();
        privateReplyResponse.isSuccess = false;
        var url = $"https://graph.facebook.com/v13.0/me/messages?access_token={accessToken}";
        var bodyStr = JsonConvert.SerializeObject(quickReplyButtonBodyData);
        var bodyJson = new StringContent(bodyStr, Encoding.UTF8, "application/json");
        try
        {
            var httpClient = _httpClientFactory.CreateClient(HttpClientHandlerName.Default);

            var httpResponse = await httpClient.PostAsync(url, bodyJson);

            if (httpResponse.IsSuccessStatusCode)
            {
                string result = await httpResponse.Content.ReadAsStringAsync();
                FbMessageHttpResponse fbMessageHttpResponse =
                    JsonConvert.DeserializeObject<FbMessageHttpResponse>(result);
                if (fbMessageHttpResponse != null)
                {
                    privateReplyResponse.recipientId = fbMessageHttpResponse.RecipientId;
                    privateReplyResponse.messageId = fbMessageHttpResponse.MessageId;
                    privateReplyResponse.isSuccess = true;
                }
            }
            else
            {
                privateReplyResponse.errorMessage = httpResponse.Content.ReadAsStringAsync().Result;
            }

            return privateReplyResponse;
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "[{MethodName}] Error replying to Facebook user {FacebookUserId}'s comment id {FacebookCommentId}. {ExceptionMessage}",
                nameof(QuickReplyButtonForComment),
                quickReplyButtonBodyData?.Recipient?.Id,
                quickReplyButtonBodyData?.Recipient?.CommentId,
                ex.Message);

            privateReplyResponse.errorMessage = ex.Message;
        }

        return privateReplyResponse;
    }

    public async Task<PrivateReplyResponse> ButtonTemplateForComment(
        ButtonTemplateBodyData buttonTemplateBodyData,
        string accessToken)
    {
        var privateReplyResponse = new PrivateReplyResponse();
        privateReplyResponse.isSuccess = false;
        var url = $"https://graph.facebook.com/v2.6/me/messages?access_token={accessToken}";
        var bodyStr = JsonConvert.SerializeObject(buttonTemplateBodyData);
        var bodyJson = new StringContent(bodyStr, Encoding.UTF8, "application/json");
        try
        {
            var httpClient = _httpClientFactory.CreateClient(HttpClientHandlerName.Default);

            var httpResponse = await httpClient.PostAsync(url, bodyJson);

            if (httpResponse.IsSuccessStatusCode)
            {
                string result = await httpResponse.Content.ReadAsStringAsync();
                FbMessageHttpResponse fbMessageHttpResponse =
                    JsonConvert.DeserializeObject<FbMessageHttpResponse>(result);
                if (fbMessageHttpResponse != null)
                {
                    privateReplyResponse.recipientId = fbMessageHttpResponse.RecipientId;
                    privateReplyResponse.messageId = fbMessageHttpResponse.MessageId;
                    privateReplyResponse.isSuccess = true;
                }
            }
            else
            {
                privateReplyResponse.errorMessage = httpResponse.Content.ReadAsStringAsync().Result;
            }

            return privateReplyResponse;
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "[{MethodName}] Error replying to Facebook user {FacebookUserId}'s comment id {FacebookCommentId}. {ExceptionMessage}",
                nameof(ButtonTemplateForComment),
                buttonTemplateBodyData?.Recipient?.Id,
                buttonTemplateBodyData?.Recipient?.CommentId,
                ex.Message);

            privateReplyResponse.errorMessage = ex.Message;
        }

        return privateReplyResponse;
    }

    private async Task<FbReplyCommentResponse> LikeComment(string objectId, string access_token)
    {
        var url = $"https://graph.facebook.com/v12.0/{objectId}/likes?access_token={access_token}";
        var replyCommentResponse = new FbReplyCommentResponse()
        {
            isSuccess = false
        };
        try
        {
            var httpClient = _httpClientFactory.CreateClient(HttpClientHandlerName.Default);

            var httpResponse = await httpClient.PostAsync(url, null);

            if (httpResponse.IsSuccessStatusCode)
            {
                string result = await httpResponse.Content.ReadAsStringAsync();
                FbLikeCommentHttpResponse fbLikeCommentHttpResponse =
                    JsonConvert.DeserializeObject<FbLikeCommentHttpResponse>(result);

                if (fbLikeCommentHttpResponse is { isSuccess: true })
                {
                    replyCommentResponse.isSuccess = true;
                }
                else
                {
                    replyCommentResponse.isSuccess = false;
                }
            }
            else
            {
                replyCommentResponse.ErrorMessage = httpResponse.Content.ReadAsStringAsync().Result;
                replyCommentResponse.isSuccess = false;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "[{MethodName}] Error reacting like to comment id {FacebookCommentId}. {ExceptionMessage}",
                nameof(LikeComment),
                objectId,
                ex.Message);

            replyCommentResponse.ErrorMessage = ex.Message;
        }

        return replyCommentResponse;
    }

    public async Task AddUserReplyDmHistory(FBMessaging messaging)
    {
        var pageId = messaging.recipient.id;
        var senderId = messaging.sender.id;
        var now = DateTime.UtcNow;
        var timeDiffConfig = 12; // limit to count the reply within 12 hrs

        try
        {
            var history = await _appDbContext.FbIgAutoReplyHistoryRecords.OrderByDescending(x => x.CreatedAt)
                .FirstOrDefaultAsync(
                    x => x.AccountId == senderId && x.PageId == pageId &&
                         x.AutoAction == AutoAction.FacebookInitiateDm &&
                         EF.Functions.DateDiffHour(x.CreatedAt, now) <= timeDiffConfig && x.ErrorMessage == null);

            if (history == null)
            {
                return;
            }

            if (!history.ContactReplyMessages.Any())
            {
                history.ContactReplyMessages = new List<ContactReplyMessage>();
            }

            var newList = new List<ContactReplyMessage>();

            foreach (var contactReplyMessage in history.ContactReplyMessages)
            {
                newList.Add(contactReplyMessage.DeepCopy());
            }

            if (messaging.message != null)
            {
                if (messaging.message.text != null)
                {
                    newList.Add(
                        new ContactReplyMessage()
                        {
                            MessageContent = messaging.message.text, MessageId = messaging.message.mid, ReplyTime = now
                        });
                }
                else if (messaging.message.attachments.Any())
                {
                    newList.Add(
                        new ContactReplyMessage()
                        {
                            AttachmentPayload = messaging.message.attachments,
                            MessageId = messaging.message.mid,
                            ReplyTime = now
                        });
                }
            }
            else if (messaging.postback != null)
            {
                newList.Add(
                    new ContactReplyMessage()
                    {
                        MessageId = messaging.postback.Mid, MessageContent = messaging.postback.Title, ReplyTime = now
                    });
            }

            history.ContactReplyMessages = newList;
            history.UpdatedAt = now;
            await _appDbContext.SaveChangesAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "[{MethodName}] Error adding DM history from {SenderId} to Facebook page {FacebookPageId}. {ExceptionMessage}",
                nameof(AddUserReplyDmHistory),
                senderId,
                pageId,
                ex.Message);
        }
    }

    public async Task<UserProfile> CreateUserProfile(string senderId, string pageId, string name = null)
    {
        var config = await _appDbContext.ConfigFacebookConfigs
            .FirstOrDefaultAsync(x => x.PageId == pageId);

        if (config is null)
        {
            return null;
        }

        var facebookUser = await _appDbContext.SenderFacebookSenders
            .Where(x =>
                x.CompanyId == config.CompanyId
                && x.FacebookId == senderId
                && x.pageId == pageId)
            .OrderByDescending(x => x.Id)
            .LastOrDefaultAsync();

        UserProfile existingUserProfile = null;

        if (facebookUser is not null)
        {
            existingUserProfile = await _appDbContext.UserProfiles
                .Include(x => x.FacebookAccount)
                .FirstOrDefaultAsync(x =>
                    x.CompanyId == config.CompanyId
                    && x.FacebookAccountId == facebookUser.Id);
        }

        if (existingUserProfile is not null)
        {
            return existingUserProfile;
        }

        var sender = facebookUser ?? new FacebookSender
        {
            CompanyId = config.CompanyId,
            FacebookId = senderId,
            pageId = pageId,
            first_name = name
        };

        try
        {
            var httpClient = _httpClientFactory.CreateClient(HttpClientHandlerName.Default);

            var profileDataResponse = await httpClient.GetStringAsync(
                $"https://graph.facebook.com/{senderId}?access_token={config.PageAccessToken}&fields=profile_pic,gender,locale,first_name,last_name");

            var profileData = JsonConvert.DeserializeObject<FacebookUserProfile>(profileDataResponse);

            var newUploadedFile = await _userProfileService.UploadSocialProfilePicture(
                config.CompanyId,
                ChannelTypes.Facebook,
                senderId,
                profileData.profile_pic);

            if (newUploadedFile == null)
            {
                _logger.LogWarning(
                    "[Facebook {MethodName}] Facebook profile pic upload fail for {FacebookUserId}",
                    nameof(CreateUserProfile),
                    senderId);
            }
            else
            {
                _appDbContext.UserProfilePictureFiles.Add(newUploadedFile);

                var domainName = _configuration.GetValue<String>("Values:DomainName");
                sender.profile_pic = $"{domainName}/userprofile/picture/{newUploadedFile.ProfilePictureId}";
            }

            if (string.IsNullOrEmpty(name))
            {
                name = "No Name";
            }

            sender.first_name = string.IsNullOrEmpty(profileData.first_name) ? name : profileData.first_name;
            sender.last_name = profileData.last_name;
            sender.locale = profileData.locale;
            sender.gender = profileData.gender;
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "[Facebook {MethodName}] Failed to get Facebook user {FacebookUserId} data. {ExceptionMessage}",
                nameof(CreateUserProfile),
                senderId,
                ex.Message);
        }

        var userProfile = new UserProfile
        {
            CompanyId = config.CompanyId,
            FacebookAccount = sender,
            FirstName = sender.first_name,
            LastName = sender.last_name,
        };

        _appDbContext.UserProfiles.Add(userProfile);
        await _appDbContext.SaveChangesAsync();

        BackgroundJob.Schedule<IAutomationService>(x => x.NewContactTrigger(userProfile.Id), TimeSpan.FromSeconds(30));

        await _userProfileHooks.OnUserProfileCreatedAsync(
            config.CompanyId,
            userProfile.Id,
            null,
            () => Task.FromResult(new OnUserProfileCreatedData(userProfile)));

        Dictionary<string, string> userProfileFieldsDict = new()
        {
            ["Subscriber"] = "true",
            ["firstname"] = userProfile.FirstName ?? string.Empty,
            ["lastname"] = userProfile.LastName ?? string.Empty
        };

        var importUserProfile = userProfileFieldsDict.ToImportUserProfileObject();

        userProfile = await _userProfileService.BulkSetFields(
            userProfile,
            null,
            importUserProfile,
            false);

        return userProfile;
    }

    public async Task<string> GetProfilePictureLink(string pageId, string accessToken)
    {
        try
        {
            var httpClient = _httpClientFactory.CreateClient(HttpClientHandlerName.Default);

            var pageProfileResponse = await httpClient.GetStringAsync(
                $"https://graph.facebook.com/v13.0/{pageId}/picture?redirect=0&access_token={accessToken}");

            var pageProfileData = JsonConvert.DeserializeObject<JObject>(pageProfileResponse);

            if (pageProfileData != null && pageProfileData["data"] != null && pageProfileData["data"]["url"] != null)
            {
                return (string) pageProfileData["data"]["url"];
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "[Facebook {MethodName}] Failed to get profile picture for Facebook page {PageId}. {ExceptionMessage}",
                nameof(GetProfilePictureLink),
                pageId,
                ex.Message);
        }

        return string.Empty;
    }

    public async Task CheckAccessTokenValidity(FacebookConfig fbConfig = null, InstagramConfig igConfig = null)
    {
        if (fbConfig == null && igConfig == null)
        {
            return;
        }

        if (fbConfig != null)
        {
            var httpClient = _httpClientFactory.CreateClient(HttpClientHandlerName.Default);

            try
            {
                var response = await httpClient.GetAsync(
                    $"https://graph.facebook.com/me?access_token={fbConfig.PageAccessToken}");

                if (response.IsSuccessStatusCode)
                {
                    return;
                }
                else if (response.StatusCode == HttpStatusCode.BadRequest)
                {
                    fbConfig.Status = FacebookStatus.Invalid;
                    await _appDbContext.SaveChangesAsync();
                    await _companyInfoCacheService.RemoveCompanyInfoCache(fbConfig.CompanyId);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[Facebook {MethodName}] Error checking Facebook page {FacebookPageId} token validity. {ExceptionMessage}",
                    nameof(CheckAccessTokenValidity),
                    fbConfig.PageId,
                    ex.Message);
            }
        }
        else if (igConfig != null)
        {
            var httpClient = _httpClientFactory.CreateClient(HttpClientHandlerName.Default);

            try
            {
                var response = await httpClient.GetAsync(
                    $"https://graph.facebook.com/me?access_token={igConfig.PageAccessToken}");

                if (response.IsSuccessStatusCode)
                {
                    return;
                }
                else if (response.StatusCode == HttpStatusCode.BadRequest)
                {
                    igConfig.Status = FacebookStatus.Invalid;
                    await _appDbContext.SaveChangesAsync();
                    await _companyInfoCacheService.RemoveCompanyInfoCache(igConfig.CompanyId);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[Instagram {MethodName}] Error checking Instagram page {FacebookPageId} token validity. {ExceptionMessage}",
                    nameof(CheckAccessTokenValidity),
                    igConfig.PageId,
                    ex.Message);
            }
        }
    }

    public async Task AddFbUserOneTimeToken(FBMessaging fbMessaging)
    {
        var fbUserOtnToken = await _appDbContext.FacebookUserOneTimeTokens.Include(x => x.FacebookUser).AsSplitQuery()
            .FirstOrDefaultAsync(
                x => x.FacebookUser.FacebookId == fbMessaging.sender.id && !x.IsTokenRedeemed && x.FacebookOTNTopicId ==
                    _appDbContext.FacebookOtnTopics
                        .Where(y => y.PageId + "-" + y.Topic + "-" + y.Id == fbMessaging.optin.payload)
                        .Select(y => y.Id).FirstOrDefault());

        if (fbUserOtnToken != null)
        {
            fbUserOtnToken.OneTimeToken = fbMessaging.optin.one_time_notif_token;
            fbUserOtnToken.IsTokenRedeemed = true;
            fbUserOtnToken.ExpiryDate = DateTime.UtcNow.AddYears(1);
            fbUserOtnToken.UpdatedAt = DateTime.UtcNow;
            await _appDbContext.SaveChangesAsync();
            await _conversationMessageService.FbSaveOtnNotifyMeMessage(fbMessaging.recipient.id, fbMessaging);
        }
    }

    public async Task<List<FacebookOTNTopicTokenResponse>> GetFacebookOTNTopicTokens(string pageId, string facebookId)
    {
        var topicTokens = await _appDbContext.FacebookOtnTopics.AsSplitQuery().Where(x => x.PageId == pageId).Include(
                x => x.FacebookUserOneTimeTokens.Where(
                    y => y.IsTokenRedeemed && y.FacebookUserId == _appDbContext.SenderFacebookSenders
                        .Where(z => z.pageId == pageId && z.FacebookId == facebookId).Select(z => z.Id)
                        .FirstOrDefault()))
            .ToListAsync();

        var expiredTokens = topicTokens.SelectMany(x => x.FacebookUserOneTimeTokens)
            .Where(x => x.ExpiryDate < DateTime.UtcNow).ToList();
        if (expiredTokens != null && expiredTokens.Any())
        {
            topicTokens.ForEach(
                x =>
                {
                    if (x.FacebookUserOneTimeTokens.Any())
                    {
                        x.FacebookUserOneTimeTokens.RemoveAll(y => expiredTokens.Any(z => z.Id == y.Id));
                    }
                });

            _appDbContext.FacebookUserOneTimeTokens.RemoveRange(expiredTokens);
            await _appDbContext.SaveChangesAsync();
        }

        var response = _mapper.Map<List<FacebookOTNTopicTokenResponse>>(topicTokens);

        return response;
    }

    public async Task<FacebookAppAccessToken> GetFacebookAccessTokenByAuthorizationCodeAsync(string code)
    {
        var clientId = _configuration.GetValue<string>("Facebook:ClientId");
        var clientSecret = _configuration.GetValue<string>("Facebook:ClientSecret");

        var httpClient = _httpClientFactory.CreateClient(HttpClientHandlerName.Default);

        // Facebook Login For Business - System User Access Token
        var httpResponseMessage = await httpClient.SendAsync(
            new HttpRequestMessage(
                HttpMethod.Get,
                new Uri(
                    $"https://graph.facebook.com/oauth/access_token?client_id={clientId}" +
                    $"&client_secret={clientSecret}" +
                    $"&code={code}")));

        if (!httpResponseMessage.IsSuccessStatusCode)
        {
            _metaChannelConnectionMeters.IncrementCounter(
                MetaChannelConnectionApis.OauthAccessToken,
                MetaChannelConnectionApiCallResults.Failure);

            var error = await httpResponseMessage.Content.ReadAsStringAsync();
            throw new Exception(error);
        }

        var systemUserAccessToken = JsonConvert.DeserializeObject<FacebookAppAccessToken>(
            await httpResponseMessage.Content.ReadAsStringAsync());

        _metaChannelConnectionMeters.IncrementCounter(
            MetaChannelConnectionApis.OauthAccessToken,
            MetaChannelConnectionApiCallResults.Success);

        return systemUserAccessToken;
    }

    public async Task<AccountInfo> GetFacebookPagesUserHasRoleOnAsync(string accessToken)
    {
        var results = new AccountInfo();

        var httpClient = _httpClientFactory.CreateClient(HttpClientHandlerName.Default);

        var uriBuilder = new UriBuilder("https://graph.facebook.com/me/accounts");

        var query = HttpUtility.ParseQueryString(uriBuilder.Query);

        query["access_token"] = accessToken;

        // Facebook Login For Business
        results.business_integration_system_user_access_token = accessToken;

        uriBuilder.Query = query.ToString();

        var url = uriBuilder.ToString();

        var httpResponseMessage = await httpClient.SendAsync(new HttpRequestMessage(HttpMethod.Get, new Uri(url)));

        if (!httpResponseMessage.IsSuccessStatusCode)
        {
            _metaChannelConnectionMeters.IncrementCounter(
                MetaChannelConnectionApis.MeAccounts,
                MetaChannelConnectionApiCallResults.Failure);
        }

        var pages = await httpResponseMessage.Content.ReadAsStringAsync();

        var accountInfo = JsonConvert.DeserializeObject<AccountInfo>(pages);
        results.data = accountInfo.data;
        results.paging = accountInfo.paging;

        while (!string.IsNullOrEmpty(accountInfo.paging.next))
        {
            pages = await httpClient.GetStringAsync(accountInfo.paging.next);
            accountInfo = JsonConvert.DeserializeObject<AccountInfo>(pages);
            results.data.AddRange(accountInfo.data);
        }

        if (results.data.Any())
        {
            _metaChannelConnectionMeters.IncrementCounter(
                MetaChannelConnectionApis.MeAccounts,
                MetaChannelConnectionApiCallResults.Success);
        }
        else
        {
            _metaChannelConnectionMeters.IncrementCounter(
                MetaChannelConnectionApis.MeAccounts,
                MetaChannelConnectionApiCallResults.Failure);
        }

        return results;
    }

    public async Task<string> GetBusinessIdAssociatedWithPageAsync(string pageId, string businessIntegrationSystemUserAccessToken)
    {
        var results = new AccountInfo();

        var httpClient = _httpClientFactory.CreateClient(HttpClientHandlerName.Default);

        var uriBuilder = new UriBuilder("https://graph.facebook.com/me/accounts");

        var query = HttpUtility.ParseQueryString(uriBuilder.Query);

        query["access_token"] = businessIntegrationSystemUserAccessToken;
        query["fields"] = "business";

        uriBuilder.Query = query.ToString();

        var url = uriBuilder.ToString();

        var pages = await httpClient.GetStringAsync(url);

        var accountInfo = JsonConvert.DeserializeObject<AccountInfo>(pages);
        results.data = accountInfo.data;
        results.paging = accountInfo.paging;

        while (!string.IsNullOrEmpty(accountInfo.paging.next))
        {
            pages = await httpClient.GetStringAsync(accountInfo.paging.next);
            accountInfo = JsonConvert.DeserializeObject<AccountInfo>(pages);
            results.data.AddRange(accountInfo.data);
        }

        return results.data.Where(x => x.id == pageId).Select(x => x.business.id).FirstOrDefault().ToString();
    }
}