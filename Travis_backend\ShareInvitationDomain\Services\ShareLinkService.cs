using System;
using System.Threading.Tasks;
using Travis_backend.ShareInvitationDomain.Models;
using Sleekflow.Apis.ShareHub.Api;
using Sleekflow.Apis.ShareHub.Model;

namespace Travis_backend.ShareInvitationDomain.Services;

public interface IShareLinkService
{
    Task<CreateShareLinkTracking> GenerateShareLink(CreateShareLinkTracking createShareLinkTracking);
}

public class ShareLinkService : IShareLinkService
{
    private readonly ILinksApi _linksApi;

    public ShareLinkService(ILinksApi linksApi)
    {
        _linksApi = linksApi;
    }

    public async Task<CreateShareLinkTracking> GenerateShareLink(CreateShareLinkTracking createShareLinkTracking)
    {
        var linksShortenOutputOutput = await _linksApi.LinksShortenPostAsync(
            shortenInput: new ShortenInput(
                createShareLinkTracking.CompanyId,
                string.Empty,
                createShareLinkTracking.Url,
                createShareLinkTracking.Title));


        if (!linksShortenOutputOutput.Success)
        {
            throw new Exception("Unable to generate share link");
        }

        var shareLink = linksShortenOutputOutput.Data.Link;

        return new CreateShareLinkTracking()
        {
            CompanyId = shareLink.SleekflowCompanyId, Title = shareLink.Title, TrackingUrl = shareLink.ShortUrl,
        };
    }
}