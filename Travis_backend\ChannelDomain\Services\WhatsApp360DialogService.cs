﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AutoMapper;
using Hangfire;
using Humanizer;
using Microsoft.AspNetCore.Hosting;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Travis_backend.ChannelDomain.Models;
using Travis_backend.ChannelDomain.ViewModels;
using Travis_backend.CompanyDomain.Services;
using Travis_backend.Constants;
using Travis_backend.CoreDomain.ViewModels;
using Travis_backend.Database;
using Travis_backend.Enums;
using Travis_backend.Helpers;
using Travis_backend.MessageDomain.Models;
using WABA360Dialog;
using WABA360Dialog.ApiClient.Exceptions;
using WABA360Dialog.ApiClient.Interfaces;
using WABA360Dialog.ApiClient.Payloads;
using WABA360Dialog.ApiClient.Payloads.Enums;
using WABA360Dialog.ApiClient.Payloads.Models;
using WABA360Dialog.ApiClient.Payloads.Models.Common;
using WABA360Dialog.ApiClient.Payloads.Models.MessageObjects.TemplateObjects;
using WABA360Dialog.Common.Converters;
using WABA360Dialog.Common.Enums;
using WABA360Dialog.PartnerClient.Converters;
using WABA360Dialog.PartnerClient.Models;
using WABA360Dialog.PartnerClient.Payloads;
using WABA360Dialog.PartnerClient.Payloads.Const;
using WABA360Dialog.PartnerClient.Payloads.Models;

namespace Travis_backend.ConversationServices;

public interface IWhatsApp360DialogService
{
    Task<WhatsApp360DialogConfig> ConnectChannel(string companyId, string channelName, string apiKey);

    Task<WhatsApp360DialogConfig> ReconnectChannel(string companyId, long whatsapp360DialogConfigId);

    Task<bool> RemoveChannel(string companyId, long whatsapp360DialogConfigId);

    Task<WhatsApp360DialogConfig> UpdateChannel(
        string companyId,
        long whatsapp360DialogConfigId,
        string channelName = null,
        string apiKey = null);

    Task<GetWhatsApp360DialogTemplateResponse> GetWhatsApp360DialogTemplate(
        string companyId,
        long whatsapp360DialogConfigId,
        int limit = 1000,
        int offset = 0);

    Task<GetWhatsApp360DialogTemplateResponse> GetAllWhatsApp360DialogTemplates(
        string companyId,
        long whatsapp360DialogConfigId);

    Task UpdateChannelErrorStatus(string companyId, long whatsapp360DialogConfigId, ApiClientException ex);

    Task ClearChannelErrorStatus(string companyId, long whatsapp360DialogConfigId);

    Task UpdateChannelErrorStatus(
        string companyId,
        long whatsapp360DialogConfigId,
        IEnumerable<ErrorObject> errors,
        ClientApiMeta meta,
        string requestPath,
        int httpStatusCode,
        string requestBody,
        string responseBody);

    Task AddBackWhatsApp360DialogUserInUserProfilesAndConversations(string companyId);

    Task ReplaceDisconnectedWhatsApp360DialogUsersConfigId(string companyId, long newChannelConfigId);

    Task RegisterDefaultTemplates(long whatsapp360DialogConfigId);

    Task ActivateChannel(string companyId, long whatsApp360DialogConfigId);

    Task SuspendChannel(string companyId, long whatsApp360DialogConfigId);

    Task SuspendClientChannels(string companyId, string clientId);

    Task ActivateClientChannels(string companyId, string clientId);

    Task UpdateAllChannelStatus();

    Task<bool> CheckAndEnablePartnerConfig(string companyId, string partnerId, string clientId, TopUpMode topUpMode);

    Task<WhatsApp360DialogConfig> UpdateCompanyChannelStatus(string channelId, PartnerChannel partnerChannel = null);

    Task<WhatsApp360DialogConfig> UpdateCompanyChannelStatus(
        string companyId,
        long configId,
        PartnerChannel partnerChannel = null);

    Task<List<PartnerChannel>> GetAllPartnerChannels(string partnerId, bool updateStatus = false);

    Task<List<WhatsAppBusinessApiClient>> GetAllPartnerClients(string partnerId);

    Task<Whatsapp360DialogOnboardingChannelInfo> CreateOnboardingChannelInfo(
        string partnerId,
        string clientId,
        string channelId);
}

public class WhatsApp360DialogService : IWhatsApp360DialogService
{
    private readonly ApplicationDbContext _appDbContext;
    private readonly ICoreWhatsApp360DialogPartnerAuthService _coreWhatsApp360DialogPartnerAuthService;
    private readonly ICoreWhatsApp360DialogUsageService _coreWhatsApp360DialogUsageService;
    private readonly ILogger _logger;
    private readonly IConfiguration _configuration;
    private readonly IWebHostEnvironment _webHostEnvironment;
    private readonly IMapper _mapper;

    public WhatsApp360DialogService(
        ApplicationDbContext appDbContext,
        ILogger<WhatsApp360DialogService> logger,
        ICoreWhatsApp360DialogPartnerAuthService coreWhatsApp360DialogPartnerAuthService,
        IConfiguration configuration,
        IWebHostEnvironment webHostEnvironment,
        IMapper mapper,
        ICoreWhatsApp360DialogUsageService coreWhatsApp360DialogUsageService)
    {
        _appDbContext = appDbContext;
        _logger = logger;
        _coreWhatsApp360DialogPartnerAuthService = coreWhatsApp360DialogPartnerAuthService;
        _configuration = configuration;
        _webHostEnvironment = webHostEnvironment;
        _mapper = mapper;
        _coreWhatsApp360DialogUsageService = coreWhatsApp360DialogUsageService;
    }

    public async Task<WhatsApp360DialogConfig> ConnectChannel(string companyId, string channelName, string apiKey)
    {
        var newWhatsApp360DialogConfig = new WhatsApp360DialogConfig
        {
            CompanyId = companyId,
            ChannelName = channelName,
            ApiKey = apiKey,
        };

        var client = new WABA360DialogApiClient(apiKey);
        var checkPhoneNumberResponse = await client.CheckPhoneNumberAsync();

        if (await _appDbContext.ConfigWhatsApp360DialogConfigs
                .AnyAsync(
                    x => x.WhatsAppPhoneNumber == checkPhoneNumberResponse.PhoneNumber))
        {
            throw new Exception("This number is currently connected!");
        }

        newWhatsApp360DialogConfig.WhatsAppPhoneNumber = checkPhoneNumberResponse.PhoneNumber;
        newWhatsApp360DialogConfig.ChannelIdentityId = checkPhoneNumberResponse.PhoneNumber;

        var partnerChannelDetail =
            await GetPartnerChannelDetailByWhatsappPhoneNumber(checkPhoneNumberResponse.PhoneNumber);

        if (partnerChannelDetail != null)
        {
            newWhatsApp360DialogConfig.WhatsAppPhoneNumber = partnerChannelDetail.PartnerChannel.SetupInfo.PhoneNumber;
            newWhatsApp360DialogConfig.WhatsAppChannelSetupName =
                partnerChannelDetail.PartnerChannel.SetupInfo.PhoneName;
            newWhatsApp360DialogConfig.ClientId = partnerChannelDetail.PartnerChannel.ClientId;
            newWhatsApp360DialogConfig.ChannelId = partnerChannelDetail.PartnerChannel.Id;
            newWhatsApp360DialogConfig.ChannelStatus = partnerChannelDetail.PartnerChannel.Status.GetString();
            newWhatsApp360DialogConfig.AccountMode = partnerChannelDetail.PartnerChannel.AccountMode.GetString();
            newWhatsApp360DialogConfig.WabaAccountId =
                partnerChannelDetail.PartnerChannel.WhatsAppBusinessApiAccount.Id;
            newWhatsApp360DialogConfig.WabaStatus = partnerChannelDetail.PartnerChannel.WhatsAppBusinessApiAccount
                .FacebookAccountStatus.GetString();
            newWhatsApp360DialogConfig.WabaBusinessId = partnerChannelDetail.PartnerChannel.WhatsAppBusinessApiAccount
                ?.OnBehalfOfBusinessInfo?.Id;
            newWhatsApp360DialogConfig.WabaAccountName = partnerChannelDetail.PartnerChannel.WhatsAppBusinessApiAccount
                ?.OnBehalfOfBusinessInfo?.Name;
            newWhatsApp360DialogConfig.WabaBusinessStatus = partnerChannelDetail.PartnerChannel
                .WhatsAppBusinessApiAccount?.OnBehalfOfBusinessInfo?.Status.GetString();
            newWhatsApp360DialogConfig.WabaAccountType = partnerChannelDetail.PartnerChannel.WhatsAppBusinessApiAccount
                ?.OnBehalfOfBusinessInfo?.Type;
            newWhatsApp360DialogConfig.CurrentQualityRating = partnerChannelDetail.PartnerChannel.CurrentQualityRating;
            newWhatsApp360DialogConfig.CurrentLimit = partnerChannelDetail.PartnerChannel.CurrentLimit;
            newWhatsApp360DialogConfig.PartnerId = partnerChannelDetail.PartnerId;

            newWhatsApp360DialogConfig.IsClient = true;
        }
        else
        {
            newWhatsApp360DialogConfig.IsClient = false;
        }

        try
        {
            _appDbContext.ConfigWhatsApp360DialogConfigs.Add(newWhatsApp360DialogConfig);

            await _appDbContext.SaveChangesAsync();

            await Setup360DialogChannelWebhook(
                client,
                newWhatsApp360DialogConfig.Id,
                newWhatsApp360DialogConfig.WhatsAppPhoneNumber,
                companyId);

            await _appDbContext.SaveChangesAsync();
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Error while connecting 360Dialog channel");

            _appDbContext.ConfigWhatsApp360DialogConfigs.Remove(newWhatsApp360DialogConfig);
            await _appDbContext.SaveChangesAsync();

            throw;
        }

        if (newWhatsApp360DialogConfig.WabaAccountId != null)
        {
            var existingChannelWithOptinConfig = await _appDbContext.ConfigWhatsApp360DialogConfigs
                .AsNoTracking()
                .FirstOrDefaultAsync(
                    x =>
                        x.CompanyId == newWhatsApp360DialogConfig.CompanyId
                        && x.WabaAccountId == newWhatsApp360DialogConfig.WabaAccountId
                        && x.IsOptInEnable);

            if (existingChannelWithOptinConfig != null)
            {
                newWhatsApp360DialogConfig.IsOptInEnable = existingChannelWithOptinConfig.IsOptInEnable;
                newWhatsApp360DialogConfig.OptInConfig = existingChannelWithOptinConfig.OptInConfig;

                await _appDbContext.SaveChangesAsync();
            }
        }

        BackgroundJob.Enqueue<IWhatsApp360DialogService>(
            x =>
                x.RegisterDefaultTemplates(newWhatsApp360DialogConfig.Id));

        if (partnerChannelDetail != null)
        {
            BackgroundJob.Enqueue<IWhatsApp360DialogService>(
                x =>
                    x.CheckAndEnablePartnerConfig(
                        companyId,
                        partnerChannelDetail.PartnerId,
                        partnerChannelDetail.PartnerChannel.ClientId,
                        partnerChannelDetail.TopUpMode));
        }

        if (_webHostEnvironment.IsDevelopment())
        {
            await AddBackWhatsApp360DialogUserInUserProfilesAndConversations(companyId);
        }
        else
        {
            BackgroundJob.Enqueue<IWhatsApp360DialogService>(
                x =>
                    x.AddBackWhatsApp360DialogUserInUserProfilesAndConversations(companyId));
        }

        if (await _appDbContext.CompanySandboxes
                .AnyAsync(x => x.CompanyId == companyId))
        {
            BackgroundJob.Enqueue<ICompanyService>(
                x =>
                    x.DeleteSandbox(companyId));
        }

        BackgroundJob.Enqueue<IWhatsApp360DialogService>(
            x =>
                x.ReplaceDisconnectedWhatsApp360DialogUsersConfigId(
                    companyId,
                    newWhatsApp360DialogConfig.Id));

        return newWhatsApp360DialogConfig;
    }

    private async Task<WhatsApp360DialogPartnerChannelViewModel> GetPartnerChannelDetailByWhatsappPhoneNumber(
        string whatsappPhoneNumber)
    {
        var allPartners =
            await _coreWhatsApp360DialogPartnerAuthService.GetAllWhatsApp360DialogPartnerAuthCredentialsAsync();

        foreach (var partner in allPartners)
        {
            var partnerAuthToken = await _coreWhatsApp360DialogPartnerAuthService.GetAuthTokenAsync(partner.PartnerId);

            var partnerClient = new WABA360DialogPartnerClient(
                new PartnerInfo(partnerAuthToken.PartnerId),
                partnerAuthToken.AccessToken);

            var partnerChannelsResponse = await partnerClient.GetPartnerChannelsAsync(
                1000,
                0,
                null,
                new GetPartnerChannelsFilter()
                {
                    SetupInfoPhoneNumber = whatsappPhoneNumber
                });

            var partnerChannelDetail = partnerChannelsResponse.PartnerChannels
                .FirstOrDefault(x => x.SetupInfo.PhoneNumber == whatsappPhoneNumber);

            if (partnerChannelDetail != null)
            {
                return new WhatsApp360DialogPartnerChannelViewModel
                {
                    PartnerId = partner.PartnerId,
                    TopUpMode = partner.TopUpMode,
                    PartnerChannel = partnerChannelDetail
                };
            }
        }

        return null; // Not Found
    }

    public async Task<bool> RemoveChannel(string companyId, long whatsapp360DialogConfigId)
    {
        var whatsApp360DialogConfig = await _appDbContext.ConfigWhatsApp360DialogConfigs
            .FirstOrDefaultAsync(
                x =>
                    x.CompanyId == companyId
                    && x.Id == whatsapp360DialogConfigId);

        _appDbContext.ConfigWhatsApp360DialogConfigs.Remove(whatsApp360DialogConfig);

        await _appDbContext.WhatsApp360DialogMediaFiles
            .Where(x => x.WhatsApp360DialogConfigId == whatsapp360DialogConfigId)
            .ExecuteUpdateAsync(
                file =>
                    file.SetProperty(f => f.WhatsApp360DialogConfigId, (long?)null));

        await _appDbContext.WhatsApp360DialogTemplateBookmarks
            .Where(x => x.WhatsApp360DialogConfigId == whatsapp360DialogConfigId)
            .ExecuteDeleteAsync();

        await _appDbContext.SaveChangesAsync();

        // Remove 360Dialog channel id from team default channels
        var companyTeams = await _appDbContext.CompanyStaffTeams
            .Where(x => x.CompanyId == companyId)
            .ToListAsync();

        foreach (var team in companyTeams)
        {
            var whatsapp360DialogChannel = team.DefaultChannels?
                .FirstOrDefault(x => x.channel == ChannelTypes.Whatsapp360Dialog);

            if (whatsapp360DialogChannel != null && whatsapp360DialogChannel.ids != null)
            {
                whatsapp360DialogChannel.ids.Remove(whatsapp360DialogConfigId.ToString());
                _appDbContext.Entry(team).Property(t => t.DefaultChannels).IsModified = true;
            }
        }

        await _appDbContext.SaveChangesAsync();

        return true;
    }

    public async Task<WhatsApp360DialogConfig> UpdateChannel(
        string companyId,
        long whatsapp360DialogConfigId,
        string channelName = null,
        string apiKey = null)
    {
        var whatsApp360DialogConfig = await _appDbContext.ConfigWhatsApp360DialogConfigs
            .FirstAsync(
                x =>
                    x.CompanyId == companyId
                    && x.Id == whatsapp360DialogConfigId);

        if (!string.IsNullOrWhiteSpace(channelName))
        {
            whatsApp360DialogConfig.ChannelName = channelName;
        }

        if (!string.IsNullOrWhiteSpace(apiKey) && whatsApp360DialogConfig.ApiKey != apiKey)
        {
            // Update Api Key and Reset webhook
            whatsApp360DialogConfig.ApiKey = apiKey;

            var client = new WABA360DialogApiClient(whatsApp360DialogConfig.ApiKey);

            var checkPhoneNumberResponse = await client.CheckPhoneNumberAsync();
            if (checkPhoneNumberResponse.PhoneNumber != whatsApp360DialogConfig.WhatsAppPhoneNumber)
            {
                throw new ArgumentException("The Api Key contains different WhatsApp Number, cannot be updated.");
            }

            if (whatsApp360DialogConfig.ChannelErrorStatus is WhatsApp360DialogChannelErrorStatus.InvalidApiKey)
            {
                whatsApp360DialogConfig.ChannelErrorStatus = null;
                whatsApp360DialogConfig.ChannelErrorStatusStartAt = null;
            }

            await Setup360DialogChannelWebhook(
                client,
                whatsApp360DialogConfig.Id,
                whatsApp360DialogConfig.WhatsAppPhoneNumber,
                companyId);
        }

        whatsApp360DialogConfig.UpdatedAt = DateTime.UtcNow;

        await _appDbContext.SaveChangesAsync();

        return whatsApp360DialogConfig;
    }

    public async Task<GetWhatsApp360DialogTemplateResponse> GetWhatsApp360DialogTemplate(
        string companyId,
        long whatsapp360DialogConfigId,
        int limit = 1000,
        int offset = 0)
    {
        var config = await _appDbContext.ConfigWhatsApp360DialogConfigs
            .AsSplitQuery()
            .Include(x => x.TemplateBookmarks)
            .ThenInclude(x => x.CreatedByStaff)
            .ThenInclude(x => x.Identity)
            .FirstOrDefaultAsync(
                x =>
                    x.CompanyId == companyId
                    && x.Id == whatsapp360DialogConfigId);

        var client = new WABA360DialogApiClient(config.ApiKey);

        var templateResponse = await client.GetTemplateAsync(limit, offset);

        // Filter out default sample templates
        templateResponse.WhatsAppBusinessApiTemplates = templateResponse.WhatsAppBusinessApiTemplates
            .Where(x => !WhatsappTemplateHelper.FacebookSampleTemplateNames.Contains(x.Name))
            .ToList();

        var result = new GetWhatsApp360DialogTemplateResponse
        {
            Total = templateResponse.Total,
            Offset = templateResponse.Offset,
            Count = templateResponse.Count,
            WhatsAppTemplates = _mapper.Map<List<WhatsAppBusinessApiTemplateViewModel>>(
                templateResponse.WhatsAppBusinessApiTemplates)
        };

        foreach (var templateBookmark in config.TemplateBookmarks)
        {
            var vm = result.WhatsAppTemplates.FirstOrDefault(
                x =>
                    x.Namespace == templateBookmark.Namespace
                    && x.Name == templateBookmark.TemplateName
                    && x.Language.GetString() == templateBookmark.TemplateLanguage);

            if (vm != null)
            {
                vm.BookmarkId = templateBookmark.Id;
                vm.IsTemplateBookmarked = true;
                vm.BookmarkVisibleToTeamIds = string.IsNullOrWhiteSpace(templateBookmark.VisibleToTeamIds) ?
                    null :
                    templateBookmark.VisibleToTeamIds
                        .Split(',')
                        .Select(long.Parse)
                        .ToList();
            }
        }

        if (string.IsNullOrEmpty(config.TemplateNamespace))
        {
            config.TemplateNamespace = templateResponse.WhatsAppBusinessApiTemplates.FirstOrDefault()?.Namespace;
            await _appDbContext.SaveChangesAsync();
        }

        // Change Authentication Type template to sync with Cloud API one
        foreach (var template in result.WhatsAppTemplates)
        {
            if (template.Category == "AUTHENTICATION")
            {
                var body = template.Components.First(x => x.Type == TemplateComponentType.BODY);
                var buttons = template.Components.First(x => x.Type == TemplateComponentType.BUTTONS);

                if (string.IsNullOrEmpty(body.Text) && buttons.Buttons.Any(x => x.Type == TemplateButtonType.OTP))
                {
                    body.Text = body.AddSecurityRecommendation == true
                        ? "*{{1}}* is your verification code. For your security, do not share this code."
                        : "*{{1}}* is your verification code.";

                    var buttonText = buttons.Buttons.First(x => x.Type == TemplateButtonType.OTP).Text;

                    buttons.Buttons = new List<TemplateButtonObject>()
                    {
                        new ()
                        {
                            Type = TemplateButtonType.URL,
                            Url = "https://www.whatsapp.com/otp/code/?otp_type=COPY_CODE&code=otp{{1}}",
                            Text = buttonText ?? "Copy code",
                            Example = new List<string>()
                            {
                                "https://www.whatsapp.com/otp/code/?otp_type=COPY_CODE&code=otp123456"
                            },
                        }
                    };
                }
            }
        }

        if (config.ChannelErrorStatus is WhatsApp360DialogChannelErrorStatus.InvalidApiKey)
        {
            await ClearChannelErrorStatus(companyId, whatsapp360DialogConfigId);
        }

        result.WhatsAppTemplates = result.WhatsAppTemplates
            .OrderByDescending(x => x.IsTemplateBookmarked)
            .ToList();

        return result;
    }

    public async Task<GetWhatsApp360DialogTemplateResponse> GetAllWhatsApp360DialogTemplates(
        string companyId,
        long whatsapp360DialogConfigId)
    {
        var limit = 1000;
        var offset = 0;
        var hasMore = true;
        var allTemplates = new GetWhatsApp360DialogTemplateResponse
        {
            Total = 0,
            Offset = 0,
            Count = 0,
            WhatsAppTemplates = new List<WhatsAppBusinessApiTemplateViewModel>()
        };

        while (hasMore)
        {
            var result = await GetWhatsApp360DialogTemplate(
                companyId,
                whatsapp360DialogConfigId,
                limit,
                offset);

            allTemplates.WhatsAppTemplates.AddRange(result.WhatsAppTemplates);

            hasMore = result.Total > offset + limit;
            offset += limit;
            if (!hasMore)
            {
                allTemplates.Total = result.Total;
            }
        }

        allTemplates.Count = allTemplates.WhatsAppTemplates.Count;

        return allTemplates;
    }

    public async Task UpdateChannelErrorStatus(string companyId, long whatsapp360DialogConfigId, ApiClientException ex)
    {
        var config = await _appDbContext.ConfigWhatsApp360DialogConfigs
            .FirstOrDefaultAsync(
                x =>
                    x.CompanyId == companyId
                    && x.Id == whatsapp360DialogConfigId);

        if (config == null)
        {
            return;
        }

        var isErrorStatusSet = false;

        if (ex.Error != null)
        {
            if (config.ChannelErrorStatus == null
                && ex.Error.Any(x => x.Code == 1014)
                && config.ChannelErrorStatus != WhatsApp360DialogChannelErrorStatus.ConnectionRefused)
            {
                config.ChannelErrorStatus = WhatsApp360DialogChannelErrorStatus.ConnectionRefused;
                config.ChannelErrorStatusStartAt = DateTime.UtcNow;
                isErrorStatusSet = true;
            }
        }
        else if (ex.Meta != null)
        {
            switch (ex.Meta)
            {
                case { HttpCode: 400 }:
                    if (config.ChannelErrorStatus is not WhatsApp360DialogChannelErrorStatus.GeneralError)
                    {
                        config.ChannelErrorStatus = WhatsApp360DialogChannelErrorStatus.GeneralError;
                        config.ChannelErrorStatusStartAt = DateTime.UtcNow;
                        isErrorStatusSet = true;
                    }

                    break;
                case { HttpCode: 401, DeveloperMessage: "Invalid api key" }:
                    if (config.ChannelErrorStatus is not WhatsApp360DialogChannelErrorStatus.InvalidApiKey)
                    {
                        config.ChannelErrorStatus = WhatsApp360DialogChannelErrorStatus.InvalidApiKey;
                        config.ChannelErrorStatusStartAt = DateTime.UtcNow;
                        isErrorStatusSet = true;
                    }

                    break;
                case { HttpCode: > 500 }:
                    if (config.ChannelErrorStatus == null)
                    {
                        config.ChannelErrorStatus = WhatsApp360DialogChannelErrorStatus.InternalServerError;
                        config.ChannelErrorStatusStartAt = DateTime.UtcNow;
                        isErrorStatusSet = true;
                    }

                    break;
            }
        }

        await _appDbContext.SaveChangesAsync();

        if (isErrorStatusSet)
        {
            BackgroundJob.Enqueue<IEmailNotificationService>(
                x => x.SendSystemAlertToSlackChannel(
                    $"360 Dialog Channel Error Occured - {config.ChannelName} ({config.WhatsAppPhoneNumber})",
                    $"360 Dialog Channel [[{config.ChannelName} ({config.WhatsAppPhoneNumber})]] received error [[{config.ChannelErrorStatus.ToString().Humanize()}]] during their recent API call.\n[[Last API Call Info:]]\nRequest Path:\n{ex.RequestPath}\nResponse Body:\n{ex.ResponseBody}",
                    "360Dialog",
                    "alert",
                    config.CompanyId,
                    true));
        }
    }

    public async Task ClearChannelErrorStatus(string companyId, long whatsapp360DialogConfigId)
    {
        var config = await _appDbContext.ConfigWhatsApp360DialogConfigs
            .FirstOrDefaultAsync(
                x =>
                    x.CompanyId == companyId
                    && x.Id == whatsapp360DialogConfigId);

        if (config == null)
        {
            return;
        }

        var resolvedChannelErrorStatus = config.ChannelErrorStatus;

        config.ChannelErrorStatus = null;
        config.ChannelErrorStatusStartAt = null;

        await _appDbContext.SaveChangesAsync();

        BackgroundJob.Enqueue<IEmailNotificationService>(
            x => x.SendSystemAlertToSlackChannel(
                $"360 Dialog Channel Error Resolved - {config.ChannelName} ({config.WhatsAppPhoneNumber})",
                $"360 Dialog Channel [[{config.ChannelName} ({config.WhatsAppPhoneNumber})]] error [[{resolvedChannelErrorStatus.ToString().Humanize()}]] has been resolved.",
                "360Dialog",
                "resolved",
                config.CompanyId,
                true));
    }

    // Hangfire friendly
    public async Task UpdateChannelErrorStatus(
        string companyId,
        long whatsapp360DialogConfigId,
        IEnumerable<ErrorObject> errors,
        ClientApiMeta meta,
        string requestPath,
        int httpStatusCode,
        string requestBody,
        string responseBody)
    {
        await UpdateChannelErrorStatus(
            companyId,
            whatsapp360DialogConfigId,
            new ApiClientException(
                errors,
                meta,
                requestPath,
                httpStatusCode,
                requestBody,
                responseBody));
    }

    public async Task<WhatsApp360DialogConfig> ReconnectChannel(string companyId, long whatsapp360DialogConfigId)
    {
        var whatsApp360DialogConfig =
            await _appDbContext.ConfigWhatsApp360DialogConfigs
                .FirstOrDefaultAsync(
                    x =>
                        x.CompanyId == companyId
                        && x.Id == whatsapp360DialogConfigId);

        var client = new WABA360DialogApiClient(whatsApp360DialogConfig.ApiKey);

        var checkPhoneNumberResponse = await client.CheckPhoneNumberAsync();

        if (checkPhoneNumberResponse.PhoneNumber != whatsApp360DialogConfig.WhatsAppPhoneNumber)
        {
            throw new ArgumentException("The Api Key contains different WhatsApp Number, cannot be reconnected.");
        }

        if (whatsApp360DialogConfig.ChannelErrorStatus is WhatsApp360DialogChannelErrorStatus.InvalidApiKey)
        {
            await ClearChannelErrorStatus(companyId, whatsapp360DialogConfigId);
        }

        await Setup360DialogChannelWebhook(
            client,
            whatsApp360DialogConfig.Id,
            whatsApp360DialogConfig.WhatsAppPhoneNumber,
            companyId);

        return whatsApp360DialogConfig;
    }

    private async Task Setup360DialogChannelWebhook(
        IWABA360DialogApiClient client,
        long whatsApp360DialogConfigId,
        string whatsAppPhoneNumber,
        string companyId)
    {
        var headerConfig = new Dictionary<string, string>
        {
            {
                WhatsApp360DialogHelper.CredentialHeaderName,
                WhatsApp360DialogHelper.GenerateCredential(
                    whatsApp360DialogConfigId,
                    whatsAppPhoneNumber,
                    companyId)
            },
        };

        var domain = _configuration["Values:DomainName"];
        SetWebhookUrlResponse response;

        if (await _appDbContext.BlastMessageConfigs
                .AnyAsync(
                    x =>
                        x.CompanyId == companyId
                        && x.IsEnabled))
        {
            await client.SetWebhookUrlAsync($"{domain}/whatsapp/360dialog/webhook/blast", headerConfig);
        }
        else
        {
            await client.SetWebhookUrlAsync($"{domain}/whatsapp/360dialog/webhook", headerConfig);
        }
    }

    public async Task AddBackWhatsApp360DialogUserInUserProfilesAndConversations(string companyId)
    {
        const int chunkSize = 100;

        var allChannels = await _appDbContext.ConfigWhatsApp360DialogConfigs
            .AsNoTracking()
            .Where(x => x.CompanyId == companyId)
            .OrderBy(x => x.CreatedAt)
            .Select(
                x => new
                {
                    x.Id, x.WhatsAppPhoneNumber
                })
            .ToListAsync();

        var allChannelIds = allChannels.Select(x => x.Id).ToList();

        var defaultChannel = allChannels.First();

        while (true)
        {
            var userProfiles = await _appDbContext.UserProfiles
                .Include(x => x.WhatsAppAccount)
                .Include(x => x.WhatsApp360DialogUser)
                .Where(
                    x =>
                        x.CompanyId == companyId
                        && x.WhatsAppAccountId != null
                        && x.WhatsApp360DialogUserId == null)
                .Take(chunkSize)
                .ToListAsync();

            var conversations = await _appDbContext.Conversations
                .Where(x => userProfiles.Select(u => u.Id).Contains(x.UserProfileId))
                .Take(chunkSize)
                .ToListAsync();

            if (userProfiles.Count == 0)
            {
                break;
            }

            try
            {
                foreach (var userProfile in userProfiles)
                {
                    if (userProfile.WhatsApp360DialogUserId != null)
                    {
                        if (userProfile.WhatsApp360DialogUser.ChannelId == null ||
                            !allChannelIds.Contains(userProfile.WhatsApp360DialogUser.ChannelId.Value))
                        {
                            userProfile.WhatsApp360DialogUser.ChannelId = defaultChannel.Id;
                        }
                    }
                    else
                    {
                        var newWhatsappUser = new WhatsApp360DialogSender()
                        {
                            PhoneNumber = $"+{userProfile.PhoneNumber}",
                            WhatsAppId = userProfile.PhoneNumber,
                            CompanyId = companyId,
                            ChannelId = defaultChannel.Id,
                            ChannelWhatsAppPhoneNumber = defaultChannel.WhatsAppPhoneNumber
                        };

                        userProfile.WhatsApp360DialogUser = newWhatsappUser;
                    }

                    var conversation = conversations
                        .FirstOrDefault(x => x.UserProfileId == userProfile.Id);

                    if (conversation != null)
                    {
                        conversation.WhatsApp360DialogUser = userProfile.WhatsApp360DialogUser;
                    }
                }
            }
            catch (Exception e)
            {
                // ignored
            }

            await _appDbContext.SaveChangesAsync();
        }
    }

    public async Task ReplaceDisconnectedWhatsApp360DialogUsersConfigId(string companyId, long newChannelConfigId)
    {
        var newChannelConfig = await _appDbContext.ConfigWhatsApp360DialogConfigs
            .AsNoTracking()
            .Where(
                x =>
                    x.CompanyId == companyId
                    && x.Id == newChannelConfigId)
            .FirstOrDefaultAsync();

        if (await _appDbContext.SenderWhatsApp360DialogSenders
                .AnyAsync(
                    x =>
                        x.CompanyId == companyId
                        && x.ChannelWhatsAppPhoneNumber == newChannelConfig.WhatsAppPhoneNumber))
        {
            await _appDbContext.SenderWhatsApp360DialogSenders
                .Where(
                    x => x.CompanyId == companyId
                         && x.ChannelWhatsAppPhoneNumber == newChannelConfig.WhatsAppPhoneNumber)
                .ExecuteUpdateAsync(
                    sender =>
                        sender
                            .SetProperty(s => s.ChannelId, newChannelConfigId)
                            .SetProperty(s => s.LastContactStatusCheckDate, (DateTime?)null)
                            .SetProperty(s => s.ContactStatus, (string)null));

            await _appDbContext.SaveChangesAsync();
        }
    }

    public async Task RegisterDefaultTemplates(long whatsapp360DialogConfigId)
    {
        var config = await _appDbContext.ConfigWhatsApp360DialogConfigs
            .AsNoTracking()
            .Include(x => x.Company)
            .FirstOrDefaultAsync(x => x.Id == whatsapp360DialogConfigId);

        if (config == null)
        {
            return;
        }

        var client = new WABA360DialogApiClient(config.ApiKey);

        try
        {
            var templateResponse = await GetWhatsApp360DialogTemplate(
                config.CompanyId,
                config.Id,
                2000,
                0);

            var listOfTemplates = new List<CreateTemplateObject>()
            {
                new ()
                {
                    Category = TemplateCategoryConst.MARKETING,
                    Name = "optin_1",
                    Language = WhatsAppLanguage.English,
                    Components = new List<TemplateComponentObject>
                    {
                        new ()
                        {
                            Type = TemplateComponentType.BODY,
                            Text = $"*Notification*\nHello there! You received a new message from {config.Company.CompanyName}.",
                        },
                        new ()
                        {
                            Type = TemplateComponentType.BUTTONS,
                            Buttons = new List<TemplateButtonObject>
                            {
                                new ()
                                {
                                    Type = TemplateButtonType.QUICK_REPLY, Text = "Read more"
                                }
                            }
                        },
                    }
                },
                new ()
                {
                    Category = TemplateCategoryConst.MARKETING,
                    Name = "optin_1",
                    Language = WhatsAppLanguage.Chinese_HK,
                    Components = new List<TemplateComponentObject>
                    {
                        new ()
                        {
                            Type = TemplateComponentType.BODY,
                            Text = $"*新訊息通知*\n你好! 你收到來自{config.Company.CompanyName}的新訊息。",
                        },
                        new ()
                        {
                            Type = TemplateComponentType.BUTTONS,
                            Buttons = new List<TemplateButtonObject>
                            {
                                new ()
                                {
                                    Type = TemplateButtonType.QUICK_REPLY, Text = "查看更多"
                                }
                            }
                        }
                    }
                },
                new ()
                {
                    Category = TemplateCategoryConst.MARKETING,
                    Name = "optin_2",
                    Language = WhatsAppLanguage.English,
                    Components = new List<TemplateComponentObject>
                    {
                        new ()
                        {
                            Type = TemplateComponentType.BODY,
                            Text = $"Hello there! You received an update from {config.Company.CompanyName}.",
                        },
                        new ()
                        {
                            Type = TemplateComponentType.BUTTONS,
                            Buttons = new List<TemplateButtonObject>
                            {
                                new ()
                                {
                                    Type = TemplateButtonType.QUICK_REPLY, Text = "Read more"
                                }
                            }
                        },
                    }
                },
                new ()
                {
                    Category = TemplateCategoryConst.MARKETING,
                    Name = "optin_2",
                    Language = WhatsAppLanguage.Chinese_HK,
                    Components = new List<TemplateComponentObject>
                    {
                        new ()
                        {
                            Type = TemplateComponentType.BODY, Text = $"你好! 以下為一則來自{config.Company.CompanyName}的新訊息。",
                        },
                        new ()
                        {
                            Type = TemplateComponentType.BUTTONS,
                            Buttons = new List<TemplateButtonObject>
                            {
                                new ()
                                {
                                    Type = TemplateButtonType.QUICK_REPLY, Text = "查看更多"
                                }
                            }
                        }
                    }
                },
                new ()
                {
                    Category = TemplateCategoryConst.MARKETING,
                    Name = "optin_3",
                    Language = WhatsAppLanguage.English,
                    Components = new List<TemplateComponentObject>
                    {
                        new ()
                        {
                            Type = TemplateComponentType.BODY,
                            Text =
                                $"*Notification*\nHello there! You received a new message from {config.WhatsAppChannelSetupName ?? config.Company.CompanyName}.",
                        },
                        new ()
                        {
                            Type = TemplateComponentType.BUTTONS,
                            Buttons = new List<TemplateButtonObject>
                            {
                                new ()
                                {
                                    Type = TemplateButtonType.QUICK_REPLY, Text = "Read more"
                                }
                            }
                        },
                    }
                },
                new ()
                {
                    Category = TemplateCategoryConst.MARKETING,
                    Name = "optin_3",
                    Language = WhatsAppLanguage.Chinese_HK,
                    Components = new List<TemplateComponentObject>
                    {
                        new ()
                        {
                            Type = TemplateComponentType.BODY,
                            Text =
                                $"*新訊息通知*\n你好! 你收到來自{config.WhatsAppChannelSetupName ?? config.Company.CompanyName}的新訊息。",
                        },
                        new ()
                        {
                            Type = TemplateComponentType.BUTTONS,
                            Buttons = new List<TemplateButtonObject>
                            {
                                new ()
                                {
                                    Type = TemplateButtonType.QUICK_REPLY, Text = "查看更多"
                                }
                            }
                        }
                    }
                },
                new ()
                {
                    Category = TemplateCategoryConst.MARKETING,
                    Name = "optin_4",
                    Language = WhatsAppLanguage.English,
                    Components = new List<TemplateComponentObject>
                    {
                        new ()
                        {
                            Type = TemplateComponentType.BODY,
                            Text =
                                $"Hello there! You received an update from {config.WhatsAppChannelSetupName ?? config.Company.CompanyName}.",
                        },
                        new ()
                        {
                            Type = TemplateComponentType.BUTTONS,
                            Buttons = new List<TemplateButtonObject>
                            {
                                new ()
                                {
                                    Type = TemplateButtonType.QUICK_REPLY, Text = "Read more"
                                }
                            }
                        },
                    }
                },
                new ()
                {
                    Category = TemplateCategoryConst.MARKETING,
                    Name = "optin_4",
                    Language = WhatsAppLanguage.Chinese_HK,
                    Components = new List<TemplateComponentObject>
                    {
                        new ()
                        {
                            Type = TemplateComponentType.BODY,
                            Text = $"你好! 以下為一則來自{config.WhatsAppChannelSetupName ?? config.Company.CompanyName}的新訊息。",
                        },
                        new ()
                        {
                            Type = TemplateComponentType.BUTTONS,
                            Buttons = new List<TemplateButtonObject>
                            {
                                new ()
                                {
                                    Type = TemplateButtonType.QUICK_REPLY, Text = "查看更多"
                                }
                            }
                        }
                    }
                },
                new ()
                {
                    Category = TemplateCategoryConst.MARKETING,
                    Name = "greetings_1",
                    Language = WhatsAppLanguage.English,
                    Components = new List<TemplateComponentObject>
                    {
                        new ()
                        {
                            Type = TemplateComponentType.BODY, Text = "Hello {{1}}",
                        },
                    }
                },
                new ()
                {
                    Category = TemplateCategoryConst.MARKETING,
                    Name = "greetings_1",
                    Language = WhatsAppLanguage.Chinese_HK,
                    Components = new List<TemplateComponentObject>
                    {
                        new ()
                        {
                            Type = TemplateComponentType.BODY, Text = "你好 {{1}}",
                        },
                    }
                },
            };

            foreach (var template in listOfTemplates
                         .Where(
                             template => !templateResponse.WhatsAppTemplates
                                 .Any(
                                     x =>
                                         x.Name == template.Name
                                         && x.Language == template.Language)))
            {
                try
                {
                    await client.CreateTemplateAsync(template);
                }
                catch (ApiClientException e)
                {
                    _logger.LogError(
                        e,
                        "Error creating default template {TemplateName} ({TemplateLanguage}) for {WhatsApp360DialogConfigId}",
                        template.Name,
                        template.Language,
                        whatsapp360DialogConfigId);
                }
            }
        }
        catch (ApiClientException e)
        {
            _logger.LogError(
                e,
                "Error creating default templates for {WhatsApp360DialogConfigId}",
                whatsapp360DialogConfigId);

            throw new Exception("Error during fetch templates!");
        }
    }

    public async Task SuspendChannel(string companyId, long whatsApp360DialogConfigId)
    {
        await _appDbContext.ConfigWhatsApp360DialogConfigs
            .Where(
                x =>
                    x.CompanyId == companyId
                    && x.Id == whatsApp360DialogConfigId)
            .ExecuteUpdateAsync(
                config =>
                    config
                        .SetProperty(c => c.IsSuspended, true)
                        .SetProperty(c => c.SuspendedAt, DateTime.UtcNow)
                        .SetProperty(c => c.UpdatedAt, DateTime.UtcNow));

        await _appDbContext.SaveChangesAsync();
    }

    public async Task ActivateChannel(string companyId, long whatsApp360DialogConfigId)
    {
        await _appDbContext.ConfigWhatsApp360DialogConfigs
            .Where(
                x =>
                    x.CompanyId == companyId
                    && x.Id == whatsApp360DialogConfigId)
            .ExecuteUpdateAsync(
                config =>
                    config
                        .SetProperty(c => c.IsSuspended, false)
                        .SetProperty(c => c.SuspendedAt, (DateTime?)null)
                        .SetProperty(c => c.UpdatedAt, DateTime.UtcNow));

        await _appDbContext.SaveChangesAsync();
    }

    public async Task SuspendClientChannels(string companyId, string clientId)
    {
        await _appDbContext.ConfigWhatsApp360DialogConfigs
            .Where(
                x =>
                    x.CompanyId == companyId
                    && x.ClientId == clientId && x.IsClient)
            .ExecuteUpdateAsync(
                config =>
                    config
                        .SetProperty(c => c.IsSuspended, true)
                        .SetProperty(c => c.SuspendedAt, DateTime.UtcNow)
                        .SetProperty(c => c.UpdatedAt, DateTime.UtcNow));

        await _appDbContext.SaveChangesAsync();
    }

    public async Task ActivateClientChannels(string companyId, string clientId)
    {
        await _appDbContext.ConfigWhatsApp360DialogConfigs
            .Where(
                x =>
                    x.CompanyId == companyId
                    && x.ClientId == clientId
                    && x.IsClient)
            .ExecuteUpdateAsync(
                config =>
                    config
                        .SetProperty(c => c.IsSuspended, false)
                        .SetProperty(c => c.SuspendedAt, (DateTime?)null)
                        .SetProperty(c => c.UpdatedAt, DateTime.UtcNow));

        await _appDbContext.SaveChangesAsync();
    }

    public async Task UpdateAllChannelStatus()
    {
        var allPartners =
            await _coreWhatsApp360DialogPartnerAuthService.GetAllWhatsApp360DialogPartnerAuthCredentialsAsync();

        foreach (var partner in allPartners)
        {
            var allPartnerChannels = await GetAllPartnerChannels(partner.PartnerId);

            foreach (var partnerChannel in allPartnerChannels)
            {
                var whatsApp360DialogConfig = await _appDbContext.ConfigWhatsApp360DialogConfigs
                    .FirstOrDefaultAsync(x => x.WhatsAppPhoneNumber == partnerChannel.SetupInfo.PhoneNumber);

                if (whatsApp360DialogConfig == null)
                {
                    continue;
                }

                UpdateWhatsApp360DialogConfigDetails(whatsApp360DialogConfig, partnerChannel, partner.PartnerId);

                await _appDbContext.SaveChangesAsync();
            }
        }
    }

    public async Task<bool> CheckAndEnablePartnerConfig(
        string companyId,
        string partnerId,
        string clientId,
        TopUpMode topUpMode)
    {
        return topUpMode switch
        {
            TopUpMode.DirectPayment => await _coreWhatsApp360DialogUsageService.EnableDirectPayment(
                companyId,
                partnerId,
                clientId),

            TopUpMode.PartnerPayment => await _coreWhatsApp360DialogUsageService.EnablePartnerPayment(
                companyId,
                partnerId,
                clientId,
                0),
            _ => true
        };
    }

    public async Task<WhatsApp360DialogConfig> UpdateCompanyChannelStatus(
        string channelId,
        PartnerChannel partnerChannel = null)
    {
        var whatsApp360DialogConfig = await _appDbContext.ConfigWhatsApp360DialogConfigs
            .FirstOrDefaultAsync(x => x.ClientId == channelId);

        if (whatsApp360DialogConfig == null)
        {
            return null;
        }

        if (partnerChannel == null)
        {
            var partnerAuthToken =
                await _coreWhatsApp360DialogPartnerAuthService.GetAuthTokenAsync(whatsApp360DialogConfig.PartnerId);

            var partnerClient = new WABA360DialogPartnerClient(
                new PartnerInfo(partnerAuthToken.PartnerId),
                partnerAuthToken.AccessToken);

            var partnerChannelsResponse = await partnerClient.GetPartnerChannelsAsync(
                10,
                0,
                filters: new GetPartnerChannelsFilter()
                {
                    Id = whatsApp360DialogConfig.ChannelId
                });

            partnerChannel = partnerChannelsResponse.PartnerChannels
                .FirstOrDefault(x => x.Id == whatsApp360DialogConfig.ChannelId);
        }

        UpdateWhatsApp360DialogConfigDetails(whatsApp360DialogConfig, partnerChannel);

        await _appDbContext.SaveChangesAsync();

        return whatsApp360DialogConfig;
    }

    public async Task<WhatsApp360DialogConfig> UpdateCompanyChannelStatus(
        string companyId,
        long configId,
        PartnerChannel partnerChannel = null)
    {
        var whatsApp360DialogConfig = await _appDbContext.ConfigWhatsApp360DialogConfigs
            .FirstOrDefaultAsync(
                x =>
                    x.Id == configId
                    && x.CompanyId == companyId
                    && x.IsClient);

        if (whatsApp360DialogConfig == null)
        {
            return null;
        }

        if (partnerChannel != null)
        {
            UpdateWhatsApp360DialogConfigDetails(whatsApp360DialogConfig, partnerChannel);
        }
        else
        {
            var partnerChannelDetail =
                await GetPartnerChannelDetailByWhatsappPhoneNumber(whatsApp360DialogConfig.WhatsAppPhoneNumber);

            if (partnerChannelDetail != null)
            {
                UpdateWhatsApp360DialogConfigDetails(
                    whatsApp360DialogConfig,
                    partnerChannelDetail.PartnerChannel,
                    partnerChannelDetail.PartnerId);
            }
        }

        await _appDbContext.SaveChangesAsync();

        return whatsApp360DialogConfig;
    }

    private WhatsApp360DialogConfig UpdateWhatsApp360DialogConfigDetails(
        WhatsApp360DialogConfig whatsApp360DialogConfig,
        PartnerChannel partnerChannel,
        string partnerId = null)
    {
        if (whatsApp360DialogConfig == null ||
            partnerChannel == null)
        {
            return null;
        }

        if (!string.IsNullOrEmpty(partnerId)
            && whatsApp360DialogConfig.PartnerId != partnerId)
        {
            whatsApp360DialogConfig.PartnerId = partnerId;
            whatsApp360DialogConfig.IsClient = true;
        }

        if (!string.IsNullOrEmpty(partnerChannel.Id)
            && whatsApp360DialogConfig.ChannelId != partnerChannel.Id)
        {
            whatsApp360DialogConfig.ChannelId = partnerChannel.Id;
        }

        if (!string.IsNullOrEmpty(partnerChannel.ClientId)
            && whatsApp360DialogConfig.ClientId != partnerChannel.ClientId)
        {
            whatsApp360DialogConfig.ClientId = partnerChannel.ClientId;
        }

        if (!string.IsNullOrEmpty(partnerChannel.SetupInfo.PhoneName)
            && whatsApp360DialogConfig.WhatsAppChannelSetupName != partnerChannel.SetupInfo.PhoneName)
        {
            whatsApp360DialogConfig.WhatsAppChannelSetupName = partnerChannel.SetupInfo.PhoneName;
        }

        if (!string.IsNullOrEmpty(partnerChannel.Status.GetString())
            && whatsApp360DialogConfig.ChannelStatus != partnerChannel.Status.GetString())
        {
            whatsApp360DialogConfig.ChannelStatus = partnerChannel.Status.GetString();
        }

        if (!string.IsNullOrEmpty(partnerChannel.AccountMode.GetString())
            && whatsApp360DialogConfig.AccountMode != partnerChannel.AccountMode.GetString())
        {
            whatsApp360DialogConfig.AccountMode = partnerChannel.AccountMode.GetString();
        }

        if (!string.IsNullOrEmpty(partnerChannel.WhatsAppBusinessApiAccount?.FacebookAccountStatus.GetString())
            && whatsApp360DialogConfig.WabaStatus !=
                partnerChannel.WhatsAppBusinessApiAccount?.FacebookAccountStatus.GetString())
        {
            whatsApp360DialogConfig.WabaStatus =
                partnerChannel.WhatsAppBusinessApiAccount?.FacebookAccountStatus.GetString();
        }

        if (!string.IsNullOrEmpty(partnerChannel.WhatsAppBusinessApiAccount?.OnBehalfOfBusinessInfo?.Name)
            && whatsApp360DialogConfig.WabaAccountName != partnerChannel.WhatsAppBusinessApiAccount?.OnBehalfOfBusinessInfo?.Name)
        {
            whatsApp360DialogConfig.WabaAccountName =
                partnerChannel.WhatsAppBusinessApiAccount?.OnBehalfOfBusinessInfo?.Name;
        }

        if (!string.IsNullOrEmpty(partnerChannel.WhatsAppBusinessApiAccount?.OnBehalfOfBusinessInfo?.Status.GetString())
            && whatsApp360DialogConfig.WabaBusinessStatus != partnerChannel.WhatsAppBusinessApiAccount?.OnBehalfOfBusinessInfo?.Status.GetString())
        {
            whatsApp360DialogConfig.WabaBusinessStatus =
                partnerChannel.WhatsAppBusinessApiAccount?.OnBehalfOfBusinessInfo?.Status.GetString();
        }

        if (!string.IsNullOrEmpty(partnerChannel.WhatsAppBusinessApiAccount?.OnBehalfOfBusinessInfo?.Type)
            && whatsApp360DialogConfig.WabaAccountType != partnerChannel.WhatsAppBusinessApiAccount?.OnBehalfOfBusinessInfo?.Type)
        {
            whatsApp360DialogConfig.WabaAccountType =
                partnerChannel.WhatsAppBusinessApiAccount?.OnBehalfOfBusinessInfo?.Type;
        }

        if (!string.IsNullOrEmpty(partnerChannel.CurrentQualityRating)
            && whatsApp360DialogConfig.CurrentQualityRating != partnerChannel.CurrentQualityRating)
        {
            whatsApp360DialogConfig.CurrentQualityRating = partnerChannel.CurrentQualityRating;
        }

        if (!string.IsNullOrEmpty(partnerChannel.CurrentLimit)
            && whatsApp360DialogConfig.CurrentLimit != partnerChannel.CurrentLimit)
        {
            whatsApp360DialogConfig.CurrentLimit = partnerChannel.CurrentLimit;
        }

        if (!string.IsNullOrEmpty(partnerChannel.WhatsAppBusinessApiAccount?.Id)
            && whatsApp360DialogConfig.WabaAccountId != partnerChannel.WhatsAppBusinessApiAccount?.Id)
        {
            whatsApp360DialogConfig.WabaAccountId = partnerChannel.WhatsAppBusinessApiAccount.Id;
        }

        if (!string.IsNullOrEmpty(partnerChannel.WhatsAppBusinessApiAccount?.OnBehalfOfBusinessInfo?.Id)
            && whatsApp360DialogConfig.WabaBusinessId != partnerChannel.WhatsAppBusinessApiAccount?.OnBehalfOfBusinessInfo?.Id)
        {
            whatsApp360DialogConfig.WabaBusinessId = partnerChannel.WhatsAppBusinessApiAccount.OnBehalfOfBusinessInfo.Id;
        }

        whatsApp360DialogConfig.UpdatedAt = DateTime.UtcNow;

        return whatsApp360DialogConfig;
    }

    public async Task<List<PartnerChannel>> GetAllPartnerChannels(string partnerId, bool updateStatus = false)
    {
        var partnerAuthToken =
            await _coreWhatsApp360DialogPartnerAuthService.GetAuthTokenAsync(partnerId);

        var partnerClient = new WABA360DialogPartnerClient(
            new PartnerInfo(partnerAuthToken.PartnerId),
            partnerAuthToken.AccessToken);

        // Get all channels
        var allPartnerChannels = new List<PartnerChannel>();
        var fetched = 0;
        var done = false;
        const int limit = 1000;

        do
        {
            var partnerChannelsResponse =
                await partnerClient.GetPartnerChannelsAsync(
                    limit,
                    fetched);

            if (!partnerChannelsResponse.PartnerChannels.Any())
            {
                break;
            }

            fetched += limit;

            allPartnerChannels.AddRange(partnerChannelsResponse.PartnerChannels);

            done = fetched >= partnerChannelsResponse.Total;
        }
        while (!done);

        if (updateStatus)
        {
            foreach (var partnerChannel in allPartnerChannels)
            {
                var whatsApp360DialogConfig = await _appDbContext.ConfigWhatsApp360DialogConfigs
                    .FirstOrDefaultAsync(x => x.ChannelId == partnerChannel.Id);

                if (whatsApp360DialogConfig == null)
                {
                    continue;
                }

                UpdateWhatsApp360DialogConfigDetails(whatsApp360DialogConfig, partnerChannel);

                await _appDbContext.SaveChangesAsync();
            }
        }

        return allPartnerChannels;
    }

    public async Task<List<WhatsAppBusinessApiClient>> GetAllPartnerClients(string partnerId)
    {
        var partnerAuthToken =
            await _coreWhatsApp360DialogPartnerAuthService.GetAuthTokenAsync(partnerId);

        var partnerClient = new WABA360DialogPartnerClient(
            new PartnerInfo(partnerAuthToken.PartnerId),
            partnerAuthToken.AccessToken);

        // Get all channels
        var allClients = new List<WhatsAppBusinessApiClient>();
        var fetched = 0;
        var done = false;
        const int limit = 1000;

        do
        {
            var partnerClientsResponse = await partnerClient.GetPartnerClientsAsync(limit, fetched);

            if (!partnerClientsResponse.Clients.Any())
            {
                break;
            }

            fetched += limit;

            allClients.AddRange(partnerClientsResponse.Clients);

            done = fetched >= partnerClientsResponse.Total;
        }
        while (!done);

        return allClients;
    }

    public async Task<Whatsapp360DialogOnboardingChannelInfo> CreateOnboardingChannelInfo(
        string partnerId,
        string clientId,
        string channelId)
    {
        var partnerAuthToken =
            await _coreWhatsApp360DialogPartnerAuthService.GetAuthTokenAsync(partnerId);

        var partnerClient = new WABA360DialogPartnerClient(
            new PartnerInfo(partnerAuthToken.PartnerId),
            partnerAuthToken.AccessToken);

        var channelsResponse = await partnerClient.GetPartnerChannelsAsync(
            filters: new GetPartnerChannelsFilter
            {
                ClientId = clientId,
                Id = channelId
            });

        var channel = channelsResponse.PartnerChannels
            .FirstOrDefault(x => x.Id == channelId);

        if (channel == null)
        {
            return null;
        }

        var createApiKeyByChannelResponse =
            await partnerClient.CreateApiKeyByChannelAsync(channelId);

        var result = new Whatsapp360DialogOnboardingChannelInfo
        {
            ApiKey = createApiKeyByChannelResponse.ApiKey,
            PhoneNumber = channel.SetupInfo.PhoneNumber,
            PhoneName = channel.SetupInfo.PhoneName,
        };

        return result;
    }
}