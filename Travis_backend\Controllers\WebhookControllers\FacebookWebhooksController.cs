﻿#nullable enable
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using Hangfire;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Polly;
using Sleekflow.Apis.WebhookHub.Api;
using Sleekflow.Apis.WebhookHub.Model;
using Travis_backend.AccountAuthenticationDomain.Models;
using Travis_backend.BackgroundTaskServices.Attributes;
using Travis_backend.Cache;
using Travis_backend.Cache.Models.CacheKeyPatterns;
using Travis_backend.CommonDomain.ViewModels;
using Travis_backend.Constants;
using Travis_backend.ConversationServices;
using Travis_backend.ConversationServices.ViewModels;
using Travis_backend.Database;
using Travis_backend.MessageDomain.Models;
using Travis_backend.SignalR;

namespace Travis_backend.Controllers.WebhookControllers
{
    public class FacebookWebhooksController : Controller
    {
        private readonly ApplicationDbContext _appDbContext;
        private readonly ILogger<FacebookWebhooksController> _logger;
        private readonly IConfiguration _configuration;
        private readonly IConversationMessageService _messagingService;
        private readonly ILeadAdsServiceService _leadAdsServiceService;
        private readonly ISignalRService _signalRService;
        private readonly IFacebookService _facebookService;
        private readonly IWebHostEnvironment _env;
        private readonly IHttpClientFactory _httpClientFactory;
        private readonly ICacheManagerService _cacheManagerService;
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly ICoreService _coreService;
        private readonly IFacebookWebhooksApi _facebookWebhooks;


        public FacebookWebhooksController(
            ApplicationDbContext appDbContext,
            IConfiguration configuration,
            ILogger<FacebookWebhooksController> logger,
            IConversationMessageService messagingService,
            ILeadAdsServiceService leadAdsServiceService,
            ISignalRService signalRService,
            IFacebookService facebookService,
            IWebHostEnvironment env,
            IHttpClientFactory httpClientFactory,
            ICacheManagerService cacheManagerService,
            UserManager<ApplicationUser> userManager,
            ICoreService coreService,
            IFacebookWebhooksApi facebookWebhooks)
        {
            _appDbContext = appDbContext;
            _configuration = configuration;
            _logger = logger;
            _messagingService = messagingService;
            _signalRService = signalRService;
            _leadAdsServiceService = leadAdsServiceService;
            _facebookService = facebookService;
            _env = env;
            _httpClientFactory = httpClientFactory;
            _cacheManagerService = cacheManagerService;
            _userManager = userManager;
            _coreService = coreService;
            _facebookWebhooks = facebookWebhooks;
        }

        [HttpGet]
        [Route("facebook/Webhook")]
        public string Get(
            [FromQuery(Name = "hub.mode")]
            string hub_mode,
            [FromQuery(Name = "hub.challenge")]
            string hub_challenge,
            [FromQuery(Name = "hub.verify_token")]
            string hub_verify_token)
        {
            if ("peterleung" == hub_verify_token)
            {
                _logger.LogInformation("Get received. Token OK : {0}", hub_verify_token);
                return hub_challenge;
            }
            else
            {
                _logger.LogError(
                    "Error. Token did not match. Got : {0}, Expected : {1}",
                    hub_verify_token,
                    "peterleung");
                return "error. no match";
            }
        }

        // Old original facebook webhook url and handler
        [HttpPost]
        [Route("facebook/Webhook")]
        public async Task<IActionResult> Post([FromBody] JObject webhookdata)
        {
            _logger.LogInformation("Facebook webhook: {WebhookPayload}", JsonConvert.SerializeObject(webhookdata));

            var data = webhookdata.ToObject<WebhookJsonData>();

            var entryIds = data.Entry.Select(x => x.Id).Where(x => !string.IsNullOrEmpty(x)).ToArray();

            var channelExisted = await ChannelExistedAsync(entryIds);

            if (channelExisted)
            {
                try
                {
                    Policy.Handle<Exception>()
                        .WaitAndRetry(
                            3,
                            sleepDurationProvider: i => TimeSpan.FromMilliseconds(100),
                            onRetry: (exception, _, retryCount, _) =>
                            {
                                _logger.LogError(
                                    exception,
                                    "Facebook webhook: Enqueue error {ExceptionMessage} with retry {RetryCount}",
                                    exception.Message,
                                    retryCount);
                            })
                        .Execute(
                            () =>
                            {
                                BackgroundJob.Enqueue(() => HandleFacebookWebhook(data));
                            });
                }
                catch (Exception ex)
                {
                    _logger.LogError(
                        ex,
                        "Facebook webhook error: {ExceptionMessage}",
                        ex.Message);

                    return BadRequest();
                }
            }

            // Forward webhook
            if (_env.IsProduction())
            {
                // HK UAT
                var isUatChannels = entryIds.Any(
                    x => x is "112397298007886" or "17841451164302461" or "101889675999584" or "100818021267026"
                        or "128251680169133" or "107794218888420" or "101023949624204" or "125473493982977"
                        or "17841448950110035" or "17841448325029731" or "17841411370782491" or "17841459163926186"
                        or "17841458551833204" or "17841445233241108" or "17841462065104285");

                if (isUatChannels)
                {
                    try
                    {
                        var httpClient = _httpClientFactory.CreateClient(HttpClientHandlerName.Default);
                        httpClient.Timeout = TimeSpan.FromSeconds(30);

                        var httpRequestMessage = new HttpRequestMessage(
                            HttpMethod.Post,
                            "https://sleekflow-core-dev-e6d7dyf5drg4eag5.z01.azurefd.net/facebook/webhook");

                        var httpContent = new StringContent(
                            JsonConvert.SerializeObject(webhookdata),
                            Encoding.UTF8,
                            "application/json");

                        httpRequestMessage.Content = httpContent;

                        await httpClient.SendAsync(httpRequestMessage);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(
                            ex,
                            "Replay Facebook webhook error to UAT: {WebhookPayload}",
                            JsonConvert.SerializeObject(webhookdata));
                    }
                }

                var isStagingChannels = entryIds.Any(
                    x => x is "205864619274755" or "17841463875544737" or "175520422315600" or "17841463525469726"
                        or "207914995747196" or "125473493982977" or "17841462065104285" or "17841464575216973");

                if (isStagingChannels)
                {
                    try
                    {
                        var httpClient = _httpClientFactory.CreateClient(HttpClientHandlerName.Default);
                        httpClient.Timeout = TimeSpan.FromSeconds(30);

                        var httpRequestMessage = new HttpRequestMessage(
                            HttpMethod.Post,
                            "https://sleekflow-core-staging-dycncqcebbf4ggag.z01.azurefd.net/facebook/webhook");

                        var httpContent = new StringContent(
                            JsonConvert.SerializeObject(webhookdata),
                            Encoding.UTF8,
                            "application/json");

                        httpRequestMessage.Content = httpContent;

                        await httpClient.SendAsync(httpRequestMessage);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(
                            ex,
                            "Replay Facebook webhook error to : {WebhookPayload}",
                            JsonConvert.SerializeObject(webhookdata));
                    }
                }


                // Global deployment servers
                if (!channelExisted && _configuration["SF_ENVIRONMENT"] == "eastasia")
                {
                    var globalDeploymentEnvironments = new[]
                    {
                        "eastus",
                        "southeastasia",
                        "uaenorth",
                        "westeurope"
                    };

                    var httpClient = _httpClientFactory.CreateClient(HttpClientHandlerName.Default);
                    httpClient.Timeout = TimeSpan.FromSeconds(30);
                    foreach (var env in globalDeploymentEnvironments)
                    {
                        try
                        {
                            var httpRequestMessage = new HttpRequestMessage(
                                HttpMethod.Post,
                                _configuration["Values:SleekflowCoreAzureFrontDoorDomain"] + "/facebook/Webhook");

                            var httpContent = new StringContent(
                                JsonConvert.SerializeObject(webhookdata),
                                Encoding.UTF8,
                                "application/json");

                            httpRequestMessage.Content = httpContent;

                            httpRequestMessage.Headers.Add("X-Sleekflow-Location", env);

                            var httpResponse = await httpClient.SendAsync(httpRequestMessage);
                            if (!httpResponse.IsSuccessStatusCode)
                            {
                                _logger.LogError(
                                    "[FacebookWebhook] Replay Facebook webhook error to Global Deployment Environment {Environment}, Webhook: {WebhookPayload}, Response Code: {Response}, Response Body: {ResponseBody}",
                                    env,
                                    JsonConvert.SerializeObject(webhookdata),
                                    httpResponse.StatusCode,
                                    await httpResponse.Content.ReadAsStringAsync());
                            }
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(
                                ex,
                                "[FacebookWebhook] Replay Facebook webhook error to Global Deployment Env {Environment}, Webhook: {WebhookPayload}",
                                env,
                                JsonConvert.SerializeObject(webhookdata));
                        }
                    }
                }
            }

            return Ok();
        }

        public class RegisterAllRequest
        {
            [JsonProperty("is_ig")]
            public bool IsIg { get; set; }
        }

        [HttpPost]
        [Route("facebook/webhook/register-all")]
        public async Task<IActionResult> RegisterAllFacebookWebhooks(RegisterAllRequest registerAllRequest)
        {
            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            if (companyUser == null)
            {
                return Unauthorized();
            }

            string webhookUrl;

            var pageIdAndUrlMap = new Dictionary<string, string>();

            if (registerAllRequest.IsIg)
            {
                var igConfigs = await _appDbContext.ConfigInstagramConfigs.ToListAsync();
                webhookUrl = _configuration["Values:DomainName"] + DefaultWebhookUrls.InstagramWebhookHandlingUrl;

                foreach (var config in igConfigs)
                {
                    try
                    {
                        var registerWebhookOutputOutput =
                            await _facebookWebhooks.FacebookWebhooksRegisterWebhookPostAsync(
                                registerWebhookInput: new RegisterWebhookInput(
                                    config.CompanyId,
                                    webhookUrl,
                                    config.InstagramPageId));

                        if (registerWebhookOutputOutput.Success)
                        {
                            pageIdAndUrlMap[registerWebhookOutputOutput.Data.PageId] =
                                registerWebhookOutputOutput.Data.WebhookUrl;
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(
                            ex,
                            "[InstagramWebhookRegistration]  [companyId]: {companyId} failed to register, error: {ExceptionString}",
                            config.CompanyId,
                            ex.ToString());
                    }
                }
            }
            else
            {
                var facebookConfigs = await _appDbContext.ConfigFacebookConfigs.ToListAsync();

                webhookUrl = _configuration["Values:DomainName"] + DefaultWebhookUrls.FacebookWebhookHandlingUrl;
                foreach (var config in facebookConfigs)
                {
                    try
                    {
                        var registerWebhookOutputOutput =
                            await _facebookWebhooks.FacebookWebhooksRegisterWebhookPostAsync(
                                registerWebhookInput: new RegisterWebhookInput(
                                    config.CompanyId,
                                    webhookUrl,
                                    config.PageId));

                        if (registerWebhookOutputOutput.Success)
                        {
                            pageIdAndUrlMap[registerWebhookOutputOutput.Data.PageId] =
                                registerWebhookOutputOutput.Data.WebhookUrl;
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(
                            ex,
                            "[FacebookWebhookRegistration]  [companyId]: {companyId} failed to register, error: {ExceptionString}",
                            config.CompanyId,
                            ex.ToString());
                    }
                }
            }

            return Ok(pageIdAndUrlMap);
        }

        public class RegisterWebhookRequest
        {
            [JsonProperty("sleekflow_company_id")]
            public string SleekflowCompanyId { get; set; }

            [JsonProperty("page_id")]
            public string PageId { get; set; }

            [JsonProperty("is_ig")]
            public bool IsIg { get; set; }
        }

        public class RegisterWebhookResponse
        {
            [JsonProperty("webhook_url")]
            public string WebhookUrl { get; set; }

            [JsonProperty("page_id")]
            public string PageId { get; set; }

            [JsonConstructor]
            public RegisterWebhookResponse(string pageId, string webhookUrl)
            {
                WebhookUrl = webhookUrl;
                PageId = pageId;
            }
        }

        [HttpPost]
        [Route("facebook/webhook/register")]
        public async Task<ActionResult<RegisterWebhookResponse>> RegisterWebhook(
            [FromBody]
            RegisterWebhookRequest registerWebhookRequest)
        {
            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            if (companyUser == null)
            {
                return Unauthorized();
            }

            string webhookUrl;

            if (registerWebhookRequest.IsIg)
            {
                webhookUrl = _configuration["Values:DomainName"] + "/facebook/webhook/HandleInstagramWebhookPayload";
            }
            else
            {
                webhookUrl = _configuration["Values:DomainName"] + "/facebook/webhook/HandleFacebookWebhookPayload";
            }

            var registerWebhookOutputOutput = await _facebookWebhooks.FacebookWebhooksRegisterWebhookPostAsync(
                registerWebhookInput: new RegisterWebhookInput(
                    registerWebhookRequest.SleekflowCompanyId,
                    webhookUrl,
                    registerWebhookRequest.PageId));

            if (registerWebhookOutputOutput.Success)
            {
                return Ok(
                    new RegisterWebhookResponse(
                        registerWebhookOutputOutput.Data.PageId,
                        registerWebhookOutputOutput.Data.WebhookUrl));
            }

            return StatusCode(
                registerWebhookOutputOutput.HttpStatusCode,
                registerWebhookOutputOutput.Message);
        }

        public class DeregisterWebhookRequest
        {
            [JsonProperty("sleekflow_company_id")]
            public string SleekflowCompanyId { get; set; }

            [JsonProperty("webhook_url")]
            public string WebhookUrl { get; set; }

            [JsonProperty("page_id")]
            public string PageId { get; set; }
        }

        [HttpPost]
        [Route("facebook/webhook/Deregister")]
        public async Task<IActionResult> DeregisterWebhook([FromBody] DeregisterWebhookRequest deregisterWebhookRequest)
        {
            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            if (companyUser == null)
            {
                return Unauthorized();
            }

            await _facebookWebhooks.FacebookWebhooksDeregisterWebhookPostAsync(
                deregisterWebhookInput: new DeregisterWebhookInput(
                    deregisterWebhookRequest.SleekflowCompanyId,
                    deregisterWebhookRequest.WebhookUrl,
                    deregisterWebhookRequest.PageId));

            return Ok();
        }

        [HttpPost]
        [Route("facebook/webhook/ForwardWebhook")]
        public async Task<OkResult> ForwardWebhook([FromBody] JObject webhookData)
        {
            _logger.LogInformation(
                "Received Facebook webhook data: {webhook_payload}",
                JsonConvert.SerializeObject(webhookData));

            var data = webhookData.ToObject<WebhookJsonData>();

            List<StringStringValueTuple> entries = new ();

            // V2 Project does not have models in Travis_backend.ConversationServices.ViewModels
            // so serialize objects into string to process
            foreach (var entry in data.Entry)
            {
                var entryId = entry.Id;
                if (string.IsNullOrEmpty(entryId))
                {
                    continue;
                }

                var entryStr = JsonConvert.SerializeObject(entry);
                entries.Add(new StringStringValueTuple(entryId, entryStr));
            }

            await _facebookWebhooks.FacebookWebhooksForwardWebhooksPostAsync(
                forwardWebhooksInput: new ForwardWebhooksInput(entries));

            return Ok();
        }

        public class ReplayCompanyFailedWebhookEventsRequest
        {
            [JsonProperty("sleekflow_company_id")]
            public string SleekflowCompanyId { get; set; }

            [JsonProperty("limit")]
            public int Limit { get; set; }

            [JsonProperty("continuation_token")]
            public string? ContinuationToken { get; set; }
        }

        public class ReplayCompanyFailedWebhookEventsResponse
        {
            [JsonProperty("continuation_token")]
            public string? ContinuationToken { get; set; }

            [JsonConstructor]
            public ReplayCompanyFailedWebhookEventsResponse(string continuationToken)
            {
                ContinuationToken = continuationToken;
            }
        }

        [HttpPost]
        [Route("facebook/webhook/ReplayCompanyFailedWebhookEvents")]
        public async Task<ActionResult<ReplayCompanyFailedWebhookEventsResponse>> ReplayCompanyFailedWebhookEvents([FromBody]
            ReplayCompanyFailedWebhookEventsRequest replayCompanyFailedWebhookEventsRequest)
        {
            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            if (companyUser == null)
            {
                return Unauthorized();
            }

            var replayCompanyFailedWebhookEventsOutputOutput =
                await _facebookWebhooks.FacebookWebhooksReplayCompanyFailedWebhookEventsPostAsync(
                    replayCompanyFailedWebhookEventsInput: new ReplayCompanyFailedWebhookEventsInput(
                        replayCompanyFailedWebhookEventsRequest.SleekflowCompanyId,
                        replayCompanyFailedWebhookEventsRequest.Limit,
                        replayCompanyFailedWebhookEventsRequest.ContinuationToken));

            if (replayCompanyFailedWebhookEventsOutputOutput.Success)
            {
                return Ok(
                    new ReplayCompanyFailedWebhookEventsResponse(
                        replayCompanyFailedWebhookEventsOutputOutput.Data.ContinuationToken));
            }

            return Ok();
        }

        public class GetAllFailedFacebookWebhookEventIdsRequest
        {
            [JsonProperty("continuation_token")]
            public string? ContinuationToken { get; set; }

            [JsonProperty("limit")]
            public int Limit { get; set; }
        }

        public class GetAllFailedFacebookWebhookEventIdsResponse
        {
            [JsonProperty("failed_facebook_webhook_event_ids")]
            public List<string> FailedFacebookWebhookEventIds { get; set; }

            [JsonProperty("continuation_token")]
            public string? ContinuationToken { get; set; }

            [JsonConstructor]
            public GetAllFailedFacebookWebhookEventIdsResponse(
                List<string> failedFacebookWebhookEventIds,
                string? continuationToken)
            {
                FailedFacebookWebhookEventIds = failedFacebookWebhookEventIds;
                ContinuationToken = continuationToken;
            }
        }

        [HttpPost]
        [Route("facebook/webhook/GetAllFailedFacebookWebhookEventIds")]
        public async Task<ActionResult<GetAllFailedFacebookWebhookEventIdsResponse>>
            GetAllFailedFacebookWebhookEventIds(
                [FromBody] GetAllFailedFacebookWebhookEventIdsRequest getAllFailedFacebookWebhookEventIdsRequest)
        {
            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            if (companyUser == null)
            {
                return Unauthorized();
            }

            var getAllFailedFacebookWebhookEventIdsOutputOutput =
                await _facebookWebhooks.FacebookWebhooksGetAllFailedFacebookWebhookEventIdsPostAsync(
                    getAllFailedFacebookWebhookEventIdsInput: new GetAllFailedFacebookWebhookEventIdsInput(
                        getAllFailedFacebookWebhookEventIdsRequest.ContinuationToken,
                        getAllFailedFacebookWebhookEventIdsRequest.Limit));


            if (getAllFailedFacebookWebhookEventIdsOutputOutput.Success)
            {
                return Ok(
                    new GetAllFailedFacebookWebhookEventIdsResponse(
                        getAllFailedFacebookWebhookEventIdsOutputOutput.Data.FailedFacebookWebhookEventIds,
                        getAllFailedFacebookWebhookEventIdsOutputOutput.Data.ContinuationToken));
            }

            return Ok();
        }

        public class ReplayWebhookEventsRequest
        {
            [JsonProperty("webhook_event_ids")]
            public List<string> WebhookEventIds { get; set; }
        }

        [HttpPost]
        [Route("facebook/webhook/ReplayWebhookEvents")]
        public async Task<IActionResult> ReplayWebhookEvents(
            [FromBody]
            ReplayWebhookEventsRequest replayWebhookEventsRequest)
        {
            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            if (companyUser == null)
            {
                return Unauthorized();
            }

            await _facebookWebhooks.FacebookWebhooksReplayWebhookEventsPostAsync(
                replayWebhookEventsInput: new ReplayWebhookEventsInput(replayWebhookEventsRequest.WebhookEventIds));

            return Ok();
        }


        public class GetCompanyActiveWebhooksRequest
        {
            [JsonProperty("sleekflow_company_id")]
            public string SleekflowCompanyId { get; set; }

            [JsonProperty("page_id")]
            public string PageId { get; set; }
        }

        public class GetCompanyActiveWebhooksResponse
        {
            [JsonProperty("webhooks")]
            public List<WebhookProperty> Webhooks { get; set; }

            public GetCompanyActiveWebhooksResponse(List<WebhookProperty> webhooks)
            {
                Webhooks = webhooks;
            }
        }

        [HttpPost]
        [Route("facebook/webhook/GetCompanyActiveWebhooks")]
        public async Task<ActionResult<GetCompanyActiveWebhooksResponse>> GetCompanyActiveWebhooks(
            [FromBody]
            GetCompanyActiveWebhooksRequest getCompanyActiveWebhooksRequest)
        {
            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            if (companyUser == null)
            {
                return Unauthorized();
            }

            var getCompanyActiveWebhooksOutputOutput =
                await _facebookWebhooks.FacebookWebhooksGetCompanyActiveWebhooksPostAsync(
                    getCompanyActiveWebhooksInput: new GetCompanyActiveWebhooksInput(
                        getCompanyActiveWebhooksRequest.SleekflowCompanyId,
                        getCompanyActiveWebhooksRequest.PageId));

            if (getCompanyActiveWebhooksOutputOutput.Success)
            {
                return Ok(
                    new GetCompanyActiveWebhooksResponse(
                        getCompanyActiveWebhooksOutputOutput.Data.Webhooks));
            }

            return Ok();
        }

        public class GetCompanyInactiveWebhooksRequest
        {
            [JsonProperty("sleekflow_company_id")]
            public string SleekflowCompanyId { get; set; }

            [JsonProperty("page_id")]
            public string PageId { get; set; }
        }

        public class GetCompanyInactiveWebhooksResponse
        {
            [JsonProperty("webhooks")]
            public List<WebhookProperty> Webhooks { get; set; }

            public GetCompanyInactiveWebhooksResponse(List<WebhookProperty> webhooks)
            {
                Webhooks = webhooks;
            }
        }

        [HttpPost]
        [Route("facebook/webhook/GetCompanyInactiveWebhooks")]
        public async Task<ActionResult<GetCompanyActiveWebhooksResponse>> GetCompanyInactiveWebhooks(
            [FromBody]
            GetCompanyInactiveWebhooksRequest getCompanyInactiveWebhooksRequest)
        {
            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            if (companyUser == null)
            {
                return Unauthorized();
            }

            var getCompanyInactiveWebhooksOutputOutput =
                await _facebookWebhooks.FacebookWebhooksGetCompanyInactiveWebhooksPostAsync(
                    getCompanyInactiveWebhooksInput: new GetCompanyInactiveWebhooksInput(
                        getCompanyInactiveWebhooksRequest.SleekflowCompanyId,
                        getCompanyInactiveWebhooksRequest.PageId));

            if (getCompanyInactiveWebhooksOutputOutput.Success)
            {
                return Ok(
                    new GetCompanyInactiveWebhooksResponse(
                        getCompanyInactiveWebhooksOutputOutput.Data.Webhooks));
            }

            return Ok();
        }

        [HttpPost]
        [AllowAnonymous]
        [Route("facebook/webhook/HandleFacebookWebhookPayload")]
        public async Task<OkResult> HandleFacebookWebhookPayload([FromBody] JObject payload)
        {
            _logger.LogInformation("Start Handling Facebook Webhook Payload: {webhook_payload}", JsonConvert.SerializeObject(payload));
            var fbEntry = JsonConvert.DeserializeObject<Entry>(payload.ToString());
            var channelExisted = await ChannelExistedAsync(
                new[]
                {
                    fbEntry.Id
                });

            if (channelExisted)
            {
                BackgroundJob.Enqueue(() => HandleMetaWebhookFromEntry(fbEntry, "page"));
            }

            return Ok();
        }

        [HttpPost]
        [AllowAnonymous]
        [Route("facebook/webhook/HandleInstagramWebhookPayload")]
        public async Task<OkResult> HandleInstagramWebhookPayload([FromBody] JObject payload)
        {
            _logger.LogInformation("Start Handling Instagram Webhook Payload: {webhook_payload}", JsonConvert.SerializeObject(payload));
            var entry = JsonConvert.DeserializeObject<Entry>(payload.ToString());
            var channelExisted = await ChannelExistedAsync(
                new[]
                {
                    entry.Id
                });

            if (channelExisted)
            {
                BackgroundJob.Enqueue(() => HandleMetaWebhookFromEntry(entry, "instagram"));
            }

            return Ok();
        }

        private async ValueTask<bool> ChannelExistedAsync(string[] entryIds)
        {
            if (entryIds == null || entryIds.Length == 0)
            {
                return true; // Just Process in HK server
            }

            try
            {
                // Get connected fb entry from local or cache
                List<string> connectedFacebookWebhookEntryIds;

                var connectedFacebookWebhookCacheKeyPattern = new ConnectedFacebookWebhookCacheKeyPattern();
                var connectedFacebookWebhookEntryIdsCache =
                    await _cacheManagerService.GetCacheAsync(connectedFacebookWebhookCacheKeyPattern);
                if (string.IsNullOrEmpty(connectedFacebookWebhookEntryIdsCache))
                {
                    connectedFacebookWebhookEntryIds =
                        await _appDbContext.ConfigFacebookConfigs.Select(x => x.PageId).ToListAsync();
                    var instagramEntryIds = await _appDbContext.ConfigInstagramConfigs.Select(
                        x => new
                        {
                            x.PageId, x.InstagramPageId
                        }).ToListAsync();

                    instagramEntryIds.ForEach(
                        x =>
                        {
                            connectedFacebookWebhookEntryIds.Add(x.InstagramPageId);
                            connectedFacebookWebhookEntryIds.Add(x.PageId);
                        });

                    connectedFacebookWebhookEntryIds = connectedFacebookWebhookEntryIds.Distinct().ToList();

                    await _cacheManagerService.SaveCacheAsync(
                        connectedFacebookWebhookCacheKeyPattern,
                        connectedFacebookWebhookEntryIds);
                }
                else
                {
                    connectedFacebookWebhookEntryIds =
                        JsonConvert.DeserializeObject<List<string>>(connectedFacebookWebhookEntryIdsCache);
                }

                return connectedFacebookWebhookEntryIds.Any(entryIds.Contains);
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[{MethodName}] Facebook Webhook Check Channel Existed Error, entryIds {EntryIds}",
                    nameof(ChannelExistedAsync),
                    JsonConvert.SerializeObject(entryIds));

                return true;
            }
        }

        [ApiExplorerSettings(IgnoreApi = true)]
        [HangfireJobExpirationTimeout(timeoutInMinutes: 60)]
        [AutomaticRetry(
            Attempts = 10,
            OnAttemptsExceeded = AttemptsExceededAction.Fail,
            DelaysInSeconds =
            [
                60, // 1 minute
                120, // 2 minutes
                240, // 4 minutes
                480, // 8 minutes
                960, // 16 minutes
                1920, // 32 minutes
                3600, // 1 hour
                7200, // 2 hours
                14400, // 4 hours
                28800, // 8 hours
            ])]
        public async Task HandleFacebookWebhook(WebhookJsonData data)
        {
            foreach (Entry entry in data.Entry)
            {
                if (entry.Messaging != null)
                {
                    foreach (FBMessaging messaging in entry.Messaging)
                    {
                        if (await ProcessFacebookMessagesWebhook(data.Object, messaging, entry))
                        {
                            break;
                        }
                    }
                }

                if (entry.Changes != null)
                {
                    foreach (Change change in entry.Changes)
                    {
                        switch (change.Field)
                        {
                            case "conversations":
                                await _messagingService.FetchByThreadId(change);
                                break;
                            case "leadgen":
                                await _leadAdsServiceService.NewLeadAds(change);
                                break;
                            case "feed":
                                if (change.Value.Item == "comment" && change.Value.Message != null &&
                                    change.Value.From.Id != entry.Id)
                                {
                                    BackgroundJob.Enqueue<IAutomationService>(x => x.FbCommentTrigger(entry, change));
                                }

                                break;
                            case "comments":
                                if (change.Value.Text != null && change.Value.Media != null &&
                                    change.Value.From.Id != entry.Id)
                                {
                                    BackgroundJob.Enqueue<IAutomationService>(x => x.IgCommentTrigger(entry, change));
                                }

                                break;
                        }
                    }
                }

                if (entry.Standby != null)
                {
                    foreach (FBMessaging messaging in entry.Standby)
                    {
                        if (await ProcessFacebookMessagesWebhook(data.Object, messaging, entry))
                        {
                            break;
                        }
                    }
                }
            }
        }

        [ApiExplorerSettings(IgnoreApi = true)]
        [HangfireJobExpirationTimeout(timeoutInMinutes: 60)]
        [AutomaticRetry(
            Attempts = 10,
            OnAttemptsExceeded = AttemptsExceededAction.Fail,
            DelaysInSeconds =
            [
                60, // 1 minute
                120, // 2 minutes
                240, // 4 minutes
                480, // 8 minutes
                960, // 16 minutes
                1920, // 32 minutes
                3600, // 1 hour
                7200, // 2 hours
                14400, // 4 hours
                28800, // 8 hours
            ])]
        public async Task HandleMetaWebhookFromEntry(Entry entry, string type)
        {
            if (entry.Messaging != null)
            {
                foreach (FBMessaging messaging in entry.Messaging)
                {
                    if (await ProcessFacebookMessagesWebhook(type, messaging, entry))
                    {
                        break;
                    }
                }
            }

            if (entry.Changes != null)
            {
                foreach (Change change in entry.Changes)
                {
                    switch (change.Field)
                    {
                        case "conversations":
                            await _messagingService.FetchByThreadId(change);
                            break;
                        case "leadgen":
                            await _leadAdsServiceService.NewLeadAds(change);
                            break;
                        case "feed":
                            if (change.Value.Item == "comment" && change.Value.Message != null &&
                                change.Value.From.Id != entry.Id)
                            {
                                BackgroundJob.Enqueue<IAutomationService>(x => x.FbCommentTrigger(entry, change));
                            }

                            break;
                        case "comments":
                            if (change.Value.Text != null && change.Value.Media != null &&
                                change.Value.From.Id != entry.Id)
                            {
                                BackgroundJob.Enqueue<IAutomationService>(x => x.IgCommentTrigger(entry, change));
                            }

                            break;
                    }
                }
            }

            if (entry.Standby != null)
            {
                foreach (FBMessaging messaging in entry.Standby)
                {
                    if (await ProcessFacebookMessagesWebhook("page", messaging, entry))
                    {
                        break;
                    }
                }
            }
        }

        private async Task<bool> ProcessFacebookMessagesWebhook(
            string webhookType,
            FBMessaging messaging,
            Entry entry)
        {
            if (messaging.message != null)
            {
                switch (webhookType)
                {
                    case "page":
                        var result = await _messagingService.FetchFacebookMessage(entry, messaging);
                        BackgroundJob.Enqueue<IAutomationService>(x => x.FbIgPreviewCodeTrigger(messaging, entry));
                        BackgroundJob.Enqueue<IFacebookService>(x => x.AddUserReplyDmHistory(messaging));
                        break;
                    case "instagram":
                        var instagramResult = await _messagingService.FetchInstagramMessage(entry, messaging);
                        BackgroundJob.Enqueue<IAutomationService>(x => x.FbIgPreviewCodeTrigger(messaging, entry));
                        BackgroundJob.Enqueue<IInstagramService>(x => x.AddUserReplyDmHistory(messaging));
                        break;
                }
            }
            else if (messaging.postback != null)
            {
                var result = await _messagingService.FetchFacebookPostback(entry, messaging);
                if (webhookType == "page")
                {
                    BackgroundJob.Enqueue<IAutomationService>(x => x.FbIcebreakerTrigger(messaging));
                    BackgroundJob.Enqueue<IFacebookService>(x => x.AddUserReplyDmHistory(messaging));
                }
                else if (webhookType == "instagram")
                {
                    BackgroundJob.Enqueue<IAutomationService>(x => x.IgIcebreakerTrigger(messaging));
                    BackgroundJob.Enqueue<IInstagramService>(x => x.AddUserReplyDmHistory(messaging));
                }
            }
            else if (messaging.delivery != null)
            {
                if (messaging.delivery.mids != null)
                {
                    foreach (var mid in messaging.delivery.mids)
                    {
                        await _messagingService.FetchByMessageId(entry.Id, mid);
                    }
                }

                var config = await _appDbContext.ConfigFacebookConfigs.FirstOrDefaultAsync(x => x.PageId == entry.Id);

                if (config == null)
                {
                    return true;
                }

                if (messaging.delivery.mids == null)
                {
                    return true;
                }

                var deliveredMessage = await _appDbContext.ConversationMessages
                    .Where(x => messaging.delivery.mids.Contains(x.MessageUniqueID)).Include(x => x.UploadedFiles)
                    .ToListAsync(); // .Include(x => x.facebookReceiver).Include(x => x.facebookSender).Include(x => x.Sender);

                if (deliveredMessage.Any())
                {
                    deliveredMessage.ForEach(x => x.Status = MessageStatus.Received);
                    await _appDbContext.SaveChangesAsync();

                    foreach (var message in deliveredMessage)
                    {
                        await _signalRService.SignalROnMessageStatusChanged(message);
                    }
                }
            }
            else if (messaging.read != null)
            {
                Expression<Func<ConversationMessage, bool>> filter;

                switch (webhookType)
                {
                    case "page":
                        DateTime timespan = new DateTime(1970, 1, 1, 0, 0, 0, 0, System.DateTimeKind.Utc);
                        timespan = timespan.AddMilliseconds(messaging.read.watermark).ToUniversalTime();
                        var facebookConfig =
                            await _appDbContext.ConfigFacebookConfigs.FirstOrDefaultAsync(x => x.PageId == entry.Id);
                        if (facebookConfig == null)
                        {
                            return true;
                        }

                        var fbReadMessage = await _appDbContext.ConversationMessages
                            .Where(
                                x =>
                                    x.CompanyId == facebookConfig.CompanyId &&
                                    x.Channel == ChannelTypes.Facebook
                                    && x.facebookReceiver.FacebookId == messaging.sender.id
                                    && x.Status != MessageStatus.Read &&
                                    x.Status != MessageStatus.Failed &&
                                    x.CreatedAt <= timespan)
                            .OrderByDescending(x => x.CreatedAt)
                            .Take(1)
                            .FirstOrDefaultAsync();

                        if (fbReadMessage != null)
                        {
                            filter = x => x.ConversationId == fbReadMessage.ConversationId &&
                                          x.facebookReceiver != null &&
                                          x.facebookReceiver.FacebookId == messaging.sender.id &&
                                          x.IsSentFromSleekflow == true &&
                                          x.Timestamp <= fbReadMessage.Timestamp &&
                                          x.Status != MessageStatus.Read &&
                                          x.Status != MessageStatus.Scheduled &&
                                          x.Status != MessageStatus.Failed;

                            var deliveredMessages = await _appDbContext.ConversationMessages
                                .Where(filter)
                                .ToListAsync();

                            foreach (var msg in deliveredMessages)
                            {
                                msg.Status = MessageStatus.Read;
                            }

                            await _appDbContext.SaveChangesAsync();

                            foreach (var msg in deliveredMessages)
                            {
                                await _signalRService.SignalROnMessageStatusChanged(msg);
                            }
                        }

                        break;
                    case ChannelTypes.Instagram:
                        var instagramConfig =
                            await _appDbContext.ConfigInstagramConfigs.FirstOrDefaultAsync(
                                x => x.InstagramPageId == entry.Id);
                        if (instagramConfig == null)
                        {
                            return true;
                        }

                        var igReadMessage = await _appDbContext.ConversationMessages.FirstOrDefaultAsync(
                            x =>
                                x.CompanyId == instagramConfig.CompanyId &&
                                x.MessageUniqueID == messaging.read.Mid &&
                                x.Status != MessageStatus.Read &&
                                x.Status != MessageStatus.Failed);

                        if (igReadMessage != null)
                        {
                            filter = x => x.ConversationId == igReadMessage.ConversationId &&
                                          x.InstagramReceiver != null &&
                                          x.InstagramReceiver.InstagramId == messaging.sender.id &&
                                          x.IsSentFromSleekflow == true &&
                                          x.Timestamp <= igReadMessage.Timestamp &&
                                          x.Status != MessageStatus.Read &&
                                          x.Status != MessageStatus.Scheduled &&
                                          x.Status != MessageStatus.Failed;

                            var deliveredMessages = await _appDbContext.ConversationMessages
                                .Where(filter)
                                .ToListAsync();

                            foreach (var msg in deliveredMessages)
                            {
                                msg.Status = MessageStatus.Read;
                            }

                            await _appDbContext.SaveChangesAsync();

                            foreach (var msg in deliveredMessages)
                            {
                                await _signalRService.SignalROnMessageStatusChanged(msg);
                            }
                        }

                        break;
                    default:
                        throw new Exception("This is not a facebook webhook");
                }
            }
            else if (messaging.optin != null)
            {
                if (messaging.optin.type == "one_time_notif_req")
                {
                    await _facebookService.AddFbUserOneTimeToken(messaging);
                }
            }
            else if (messaging.Referral != null)
            {
                if (!string.IsNullOrEmpty(messaging.Referral.Source) &&
                    messaging.Referral.Source.ToLower() == "ads" && !string.IsNullOrEmpty(messaging.Referral.Type) &&
                    messaging.Referral.Type.ToLower() == "open_thread")
                {
                    BackgroundJob.Enqueue<IConversationMessageService>(
                        x => x.AddFbAdClickToMessenger(entry, messaging));
                }
            }

            return false;
        }

        [HttpGet]
        [Authorize]
        [Route("facebook/connect")]
        public async Task<IActionResult> Redirect(
            [FromQuery(Name = "code")]
            string code,
            [FromQuery(Name = "appDomainName")]
            string appDomainName)
        {
            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            if (companyUser == null)
            {
                return Unauthorized();
            }

            try
            {
                _logger.LogInformation(
                    "[FacebookConnect] [companyId]: {companyId} [code]: {code}",
                    companyUser.CompanyId,
                    code);

                appDomainName = string.IsNullOrEmpty(appDomainName)
                    ? _configuration.GetValue<string>("Values:AppDomainName")
                    : appDomainName;

                if (!AuthorizedAppDomainNames.AllAuthorizedAppDomainNames.Contains(appDomainName))
                {
                    return BadRequest(
                        new ResponseViewModel()
                        {
                            message = "app domain name is not in the list"
                        });
                }

                var facebookAccessToken = await _facebookService.GetFacebookAccessTokenByAuthorizationCodeAsync(code);

                _logger.LogInformation(
                    "[FacebookConnect] [companyId]: {companyId} [appAccessTokenResponse]: {FacebookAppAccessTokenResponse}",
                    companyUser.CompanyId,
                    JsonConvert.SerializeObject(facebookAccessToken));

                var results =
                    await _facebookService.GetFacebookPagesUserHasRoleOnAsync(facebookAccessToken.AccessToken);

                _logger.LogInformation(
                    "[FacebookConnect]  [companyId]: {companyId} pages: {Pages}",
                    companyUser.CompanyId,
                    JsonConvert.SerializeObject(results));

                return Ok(results);
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[FacebookConnect]  [companyId]: {companyId} redirect code: {Code}, Retrieve account info error: {ExceptionString}",
                    companyUser.CompanyId,
                    code,
                    ex.ToString());

                return BadRequest(ex.Message);
            }
        }

        [HttpGet]
        [Authorize]
        [Route("instagram/connect")]
        public async Task<IActionResult> InstagramRedirect(
            [FromQuery(Name = "code")]
            string code,
            [FromQuery(Name = "appDomainName")]
            string appDomainName)
        {
            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            if (companyUser == null)
            {
                return Unauthorized();
            }

            try
            {
                _logger.LogInformation(
                    "[InstagramConnect] [companyId]: {companyId} [code]: {code}",
                    companyUser.CompanyId,
                    code);

                appDomainName = string.IsNullOrEmpty(appDomainName)
                    ? _configuration.GetValue<string>("Values:AppDomainName")
                    : appDomainName;

                if (!AuthorizedAppDomainNames.AllAuthorizedAppDomainNames.Contains(appDomainName))
                {
                    return BadRequest(
                        new ResponseViewModel()
                        {
                            message = "app domain name is not in the list"
                        });
                }

                var facebookAccessToken = await _facebookService.GetFacebookAccessTokenByAuthorizationCodeAsync(code);

                _logger.LogInformation(
                    "[InstagramConnect] [companyId]: {companyId} [appAccessTokenResponse]: {FacebookAppAccessTokenResponse}",
                    companyUser.CompanyId,
                    JsonConvert.SerializeObject(facebookAccessToken));

                var results =
                    await _facebookService.GetFacebookPagesUserHasRoleOnAsync(facebookAccessToken.AccessToken);

                _logger.LogInformation(
                    "[InstagramConnect] [companyId]: {companyId} pages: {Pages}",
                    companyUser.CompanyId,
                    JsonConvert.SerializeObject(results));

                return Ok(results);
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[InstagramConnect]  [companyId]: {companyId} redirect code: {Code}, Retrieve account info error {ExceptionString}",
                    companyUser.CompanyId,
                    code,
                    ex.ToString());

                return BadRequest(ex.Message);
            }
        }

        [HttpGet]
        [Route("facebook/connect/access_token")]
        public async Task<IActionResult> TokenRedirect([FromQuery(Name = "access_token")] string access_token)
        {
            try
            {
                var httpClient = _httpClientFactory.CreateClient(HttpClientHandlerName.Default);

                var pages = await httpClient.GetStringAsync(
                    $"https://graph.facebook.com/me/accounts?access_token={access_token}");
                var accountInfo = JsonConvert.DeserializeObject<AccountInfo>(pages);

                _logger.LogInformation(
                    "[FacebookConnect] pages: {Pages}",
                    pages);

                return Ok(accountInfo);
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[FacebookConnect] redirect token: {AccessToken}, Retrieve account info error {ExceptionString}",
                    access_token,
                    ex.ToString());
                return BadRequest(ex.Message);
            }
        }
    }
}